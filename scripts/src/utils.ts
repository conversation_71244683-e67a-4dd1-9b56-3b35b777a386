import ExcelJS from "exceljs";
import * as fs from 'fs';
import * as path from 'path';

export function excelSerialToDate(serial: number | undefined | null): Date | null {
  if (serial == null || serial === 0) {
    return null;
  }

  const unixTime = (serial - 25569) * 86400 * 1000;
  return new Date(unixTime);
}

export async function writeExcel(data: any[], fileName: string): Promise<void> {
  const assetsDir = path.resolve(__dirname, '../assets');

  // Verificar si la carpeta 'assets' existe, y si no, crearla
  if (!fs.existsSync(assetsDir)) {
    fs.mkdirSync(assetsDir, { recursive: true });
    console.log(`Carpeta creada: ${assetsDir}`);
  }

  console.log(`Escribiendo archivo Excel: ${fileName} ...`);

  if (data.length === 0) {
    console.log("No hay datos para escribir.");
    return;
  }

  const workbook = new ExcelJS.stream.xlsx.WorkbookWriter({
    filename: path.join(assetsDir, `${fileName}.xlsx`),
    useStyles: true,
    useSharedStrings: true,
  });

  // Tamaño del bloque para manejar grandes volúmenes de datos
  const blockSize = 1000000; // Tamaño máximo recomendado para cada hoja
  const totalBlocks = Math.ceil(data.length / blockSize);
  let sheetIndex = 0;
  console.log(`Progreso: 0%`);

  for (let i = 0; i < totalBlocks; i++) {
    const start = i * blockSize;
    const end = Math.min(start + blockSize, data.length);
    const block = data.slice(start, end);

    const worksheet = workbook.addWorksheet(`Hoja${++sheetIndex}`);
    worksheet.columns = Object.keys(block[0]).map(key => ({ header: key, key: key }));

    for (const row of block) {
      worksheet.addRow(row).commit();
    }

    // Calcular y mostrar el progreso en consola, sobrescribiendo la línea anterior
    const progress = ((i + 1) / totalBlocks) * 100;
    console.log(`Progreso: ${progress.toFixed(2)}%`);
  }

  await workbook.commit();
  console.log(`Archivo Excel creado en assets/${fileName}.xlsx`);
}
