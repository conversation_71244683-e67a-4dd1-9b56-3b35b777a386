import { PrismaClient as PrismaTo } from "../prisma/generated/qa";
import { PrismaClient as PrismaFrom } from "../prisma/generated/dev";

const to = new PrismaTo();
const from = new PrismaFrom();

main()
  .then(() => process.exit(0))
  .catch((e) => {
    console.error(e);
    process.exit(1);
  });

async function main() {
  const service_documentsQA = await from.service_documents.findMany();
  let countServiceDocuments = 0;
  const batchSize = 20;

  for (let i = 0; i < service_documentsQA.length; i += batchSize) {
    const batch = service_documentsQA.slice(i, i + batchSize);

    const serviceDocumentsUpserts = batch.map(async (service_document) => {
      countServiceDocuments++;
      console.log(
        "Migrando documento de servicio: " +
        countServiceDocuments +
        " de " +
        service_documentsQA.length +
        " - ID: " +
        service_document.id,
      );
      await to.service_documents.upsert({
        create: service_document,
        update: service_document,
        where: {
          id: service_document.id,
        },
      });
    });

    // Esperar a que el batch de 20 se complete antes de continuar
    await Promise.all(serviceDocumentsUpserts);
  }
}