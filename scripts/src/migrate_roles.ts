import { PrismaClient as PrismaTo } from "../prisma/generated/qa";
import { PrismaClient as PrismaFrom } from "../prisma/generated/dev";

const to = new PrismaTo();
const from = new PrismaFrom();

main()
  .then(() => process.exit(0))
  .catch((e) => {
    console.error(e);
    process.exit(1);
  });

async function main() {
  const areasQA = await from.areas.findMany();
  let countArea = 0;
  const batchSize = 20;

  for (let i = 0; i < areasQA.length; i += batchSize) {
    const batch = areasQA.slice(i, i + batchSize);

    const areaUpserts = batch.map(async (area) => {
      countArea++;
      console.log(
        "Migrando area: " +
        countArea +
        " de " +
        areasQA.length +
        " - ID: " +
        area.id +
        " Name: " +
        area.name,
      );
      await to.areas.upsert({
        create: area,
        update: area,
        where: {
          id: area.id,
        },
      });
    });

    // Esperar a que el batch de 20 se complete antes de continuar
    await Promise.all(areaUpserts);
  }
}