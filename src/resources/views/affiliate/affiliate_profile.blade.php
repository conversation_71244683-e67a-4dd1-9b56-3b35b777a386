<style>
    .accordion .grid .row {
        padding: 0 !important;
        padding-left: 1.5rem !important;
    }

    .accordion .grid .row .column {
        padding: 0.25rem !important;
        font-size: 0.85em;
    }

    .circle {
        width: 20px;
        height: 20px;
        border-radius: 10px;
        color: white;
        display: grid;
        place-items: center;
        margin: 10px;
    }
</style>
<div class="ui attached segment">
    <div class="ui styled fluid accordion">
        @if(optional($affiliate)->doc_type != 'CJ')
            <div class="title active">
                <i class="dropdown icon">
                </i>
                Asegurado
            </div>
            <div class="content active">
                <div class="ui four columns grid">
                    <div class="row">
                        <div class="column">
                            <b>
                                Tipo de documento:
                            </b>
                            {{optional($affiliate)->doc_type }}
                        </div>
                        <div class="column">
                            <b>
                                # de documento:
                            </b>
                            {{ optional($affiliate)->doc_number }}
                        </div>
                        <div class="column">
                            <b>
                                Nombres y apellidos:
                            </b>
                            {{ mb_convert_case(mb_strtolower( optional($affiliate)->full_name), MB_CASE_TITLE, "UTF-8")  }}
                        </div>
                        <div class="column">
                            <b>
                                Fecha de nacimiento:
                            </b>
                            {{ optional($affiliate)->birthday ? optional($affiliate)->birthday->formatLocalized('%B %d, %Y') : ''}}
                        </div>
                    </div>
                    <div class="row">
                        <div class="column">
                            <b>
                                Edad:
                            </b>
                            {{ optional($affiliate)->age() }}
                        </div>
                        <div class="column">
                            <b>
                                Sexo:
                            </b>
                            {{ $GENDERS[optional($affiliate)->gender] }}
                        </div>
                        <div class="column">
                            <b>
                                Estado civil:
                            </b>
                            {{--                        {{ $CIVIL_STATUS[$affiliate->civil_status] }}--}}
                        </div>
                        <div class="column">
                            <b>
                                Nivel escolar:
                            </b>
                            {{--                        {{ $SCHOOL_LEVELS[$affiliate->school_level] }}--}}
                        </div>
                    </div>
                    <div class="row">
                        <div class="column">
                            <b>
                                Dirección:
                            </b>
                            {{ optional($affiliate)->address }}
                            <br/>
                            <span class="municipality" data-department="{{ optional($affiliate)->department }}"
                                  data-municipality="{{ optional($affiliate)->municipality }}">
                    </span>
                        </div>
                        <div class="column">
                            <b>
                                Correo electrónico
                            </b>
                            {{ optional($affiliate)->email }}
                        </div>
                        <div class="column">
                            <b>
                                Teléfono:
                            </b>
                            {{ optional($affiliate)->phone }}
                        </div>
                        <div class="column">
                            <b>
                                Celular:
                            </b>
                            {{ optional($affiliate)->cellphone }}
                        </div>
                    </div>
                    <div class="row">
                        <div class="column">
                            <div class="ui buttons" style="{{optional($affiliate)->rais ? '' : 'padding-top: 1.5rem'}}">
                                @unless(Auth::user()->isConsultant())

                                    {{-- <a class="ui primary button"
                                       href="{{secure_url('/afiliado/' . $affiliate->id . '/editar')}}">
                                        <i class="edit icon">
                                        </i>
                                        Editar información
                                    </a> --}}
                                @endunless
                                {{-- <button class="ui secondary button" data-content="Historial de novedades"
                                        id="historicalBTN">
                                    <i class="history icon">
                                    </i>
                                </button> --}}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        @else
            <div class="title active">
                <i class="dropdown icon">
                </i>
                Tomador
            </div>
            <div class="content active">
                <div class="ui four columns grid">
                    <div class="row">
                        <div class="column">
                            <b>
                                Tipo de documento:
                            </b>
                            {{ optional($affiliate)->doc_type }}
                        </div>
                        <div class="column">
                            <b>
                                # de documento:
                            </b>
                            {{ optional($affiliate)->doc_number }}
                        </div>
                        <div class="column">
                            <b>
                                Nombres y apellidos:
                            </b>
                            {{ mb_convert_case(mb_strtolower(optional($affiliate)->full_name, 'UTF-8'), MB_CASE_TITLE, 'UTF-8') }}
                        </div>
                        <div class="column">
                            <b>
                                Fecha de fundación:
                            </b>
                            {{ optional($affiliate)->birthday ? optional($affiliate)->birthday->formatLocalized('%B %d, %Y') : ''}}
                        </div>
                    </div>
                    <div class="row">
                        <div class="column">
                            <b>
                                Dirección:
                            </b>
                            {{ optional($affiliate)->address }}
                            <br/>
                            <span class="municipality" data-department="{{ optional($affiliate)->department }}"
                                  data-municipality="{{ optional($affiliate)->municipality }}">
                    </span>
                        </div>
                        <div class="column">
                            <b>
                                Correo electrónico
                            </b>
                            {{ optional($affiliate)->email }}
                        </div>
                        <div class="column">
                            <b>
                                Teléfono:
                            </b>
                            {{ optional($affiliate)->phone }}
                        </div>
                        <div class="column">
                            <b>
                                Celular:
                            </b>
                            {{ optional($affiliate)->cellphone }}
                        </div>
                    </div>
                    <div class="row">
                        <div class="column">
                            <div class="ui buttons" style="{{optional($affiliate)->rais ? '' : 'padding-top: 1.5rem'}}">
                                @unless(Auth::user()->isConsultant())

                                    <a class="ui basic small blue button"
                                       href="{{secure_url('/afiliado/' . optional($affiliate)->id . '/editar')}}">
                                        <i class="edit icon">
                                        </i>
                                        Editar información
                                    </a>
                                @endunless
                                <button class="ui basic icon small yellow button" data-content="Historial de novedades"
                                        id="historicalBTN">
                                    <i class="history icon">
                                    </i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        @endif


        {{--        <div class="title">--}}
        {{--            <i class="dropdown icon"></i>--}}
        {{--            Empleador--}}
        {{--        </div>--}}
        {{--        <div class="content">--}}
        {{--            <div class="fluid styled accordion">--}}
        {{--                @foreach ($affiliate->employments as $employment)--}}
        {{--                    <div class="title">--}}
        {{--                        <i class="dropdown icon">--}}
        {{--                        </i>--}}
        {{--                        {{ $employment->employer ? $employment->employer->name : 'Independiente' }} ---}}
        {{--                        ({{ $employment->start_date->formatLocalized('%B %d, %Y') }}--}}
        {{--                        - {{ $employment->actual || $employment->end_date == null ? 'actualidad' : $employment->end_date->formatLocalized('%B %d, %Y') }}--}}
        {{--                        )--}}
        {{--                    </div>--}}
        {{--                    <div class="content">--}}
        {{--                        <div class="ui grid">--}}
        {{--                            @if ($employment->employer)--}}
        {{--                                <div class="four columns row">--}}
        {{--                                    <div class="column">--}}
        {{--                                        <b>--}}
        {{--                                            NIT:--}}
        {{--                                        </b>--}}
        {{--                                        {{ $employment->employer->nit }}-{{ $employment->employer->nit_digit }}--}}
        {{--                                    </div>--}}
        {{--                                    <div class="column">--}}
        {{--                                        <b>--}}
        {{--                                            Sucursal:--}}
        {{--                                        </b>--}}
        {{--                                        {{ $employment->branch->name }}--}}
        {{--                                    </div>--}}
        {{--                                    <div class="column">--}}
        {{--                                        <b>--}}
        {{--                                            Direccion:--}}
        {{--                                        </b>--}}
        {{--                                        {{ $employment->branch->address }}--}}
        {{--                                        <br/>--}}
        {{--                                        <span class="municipality"--}}
        {{--                                              data-department="{{ $employment->branch->department }}"--}}
        {{--                                              data-municipality="{{ $employment->branch->municipality }}">--}}
        {{--                                </span>--}}
        {{--                                    </div>--}}
        {{--                                    <div class="column">--}}
        {{--                                        <b>--}}
        {{--                                            Persona de contacto:--}}
        {{--                                        </b>--}}
        {{--                                        {{ $employment->branch->contact }}--}}
        {{--                                    </div>--}}
        {{--                                    <div class="column">--}}
        {{--                                        <b>--}}
        {{--                                            Telefono:--}}
        {{--                                        </b>--}}
        {{--                                        {{ $employment->branch->phone }}--}}
        {{--                                    </div>--}}
        {{--                                    <div class="column">--}}
        {{--                                        <b>--}}
        {{--                                            Celular:--}}
        {{--                                        </b>--}}
        {{--                                        {{ $employment->branch->cellphone }}--}}
        {{--                                    </div>--}}
        {{--                                    <div class="column">--}}
        {{--                                        <b>--}}
        {{--                                            E-mail:--}}
        {{--                                        </b>--}}
        {{--                                        {{ $employment->branch->email }}--}}
        {{--                                    </div>--}}
        {{--                                    <div class="column">--}}
        {{--                                        <b>--}}
        {{--                                            ARL:--}}
        {{--                                        </b>--}}
        {{--                                        --}}{{--                                        {{ $ARL_LIST[$employment->employer->arl] }}--}}
        {{--                                    </div>--}}
        {{--                                </div>--}}
        {{--                            @endif--}}
        {{--                            <div class="row">--}}
        {{--                                <div class="four wide column">--}}
        {{--                                    <b>--}}
        {{--                                        Cargo:--}}
        {{--                                    </b>--}}
        {{--                                    {{ $employment->position }}--}}
        {{--                                </div>--}}
        {{--                                <div class="twelve wide column">--}}
        {{--                                    <b>--}}
        {{--                                        Ocupación:--}}
        {{--                                    </b>--}}
        {{--                                    <span class="ocupation" data-code="{{ $employment->Ocupación--}}
        {{-- }}">--}}
        {{--                                </span>--}}
        {{--                                </div>--}}
        {{--                                <div class="sixteen wide column">--}}
        {{--                                    <b>--}}
        {{--                                        Funciones:--}}
        {{--                                    </b>--}}
        {{--                                    {!! nl2br(e( $employment->position_functions)) !!}--}}
        {{--                                </div>--}}
        {{--                                <div class="sixteen wide column">--}}
        {{--                                    <b>--}}
        {{--                                        Descripcion:--}}
        {{--                                    </b>--}}
        {{--                                    {!! nl2br(e( $employment->description)) !!}--}}
        {{--                                </div>--}}
        {{--                            </div>--}}
        {{--                        </div>--}}
        {{--                    </div>--}}
        {{--                @endforeach--}}
        {{--            </div>--}}
        {{--        </div>--}}
        @if (false)
            <div class="title">
                <i class="dropdown icon">
                </i>
                Historia clinica
            </div>
            <div class="content">
                <div class="ui grid">
                    <div class="sixteen wide column">
                        <b>
                            Resumen:
                        </b>
                        <p>
                            {{ optional($affiliate)->clinical_history_summary or 'No se ha cargado una historia clinica' }}
                        </p>
                    </div>
                    <table class="ui compact very basic table">
                        <thead>
                        <tr>
                            <th>
                                Fecha
                            </th>
                            <th>
                                Observacion
                            </th>
                            <th>
                                Archivos
                            </th>
                        </tr>
                        </thead>
                        <tbody>
                        @foreach(optional($affiliate)->clinicalHistoryFiles() as $file)
                            <tr>
                                <td>
                                    {{$file->created_at->formatLocalized('%B %d, %Y')}}
                                </td>
                                <td>
                                    {{$file->comment}}
                                </td>
                                <td>
                                    @foreach($file->files as $f)
                                        <a class="ui basic small blue icon button"
                                           href="{{ secure_url('file/'.$f->path) }}" target="_blank">
                                            <i class="download icon">
                                            </i>
                                        </a>
                                    @endforeach
                                </td>
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        @endif
    </div>
    <div class="ui info message" style="text-align: center">
        <div class="header">Trámites</div>
    </div>
    @if(optional($affiliate)->survival_state == 'FALLECIDO')
        <div class="ui negative message" style="text-align: center">
            <div class="header">EL ASEGURADO SE ENCUENTRA FALLECIDO</div>
            Fallecido el día {{date('d/m/Y', strtotime(optional($affiliate)->death_date))}}
        </div>
    @endif
    <div class="ui styled fluid accordion">
        {{-- Cotización SORT  --}}
        @if(Auth::user()->is_view_service(74) && $quotations->isNotEmpty())
            <div class="title" style="color: {{$quotations->isNotEmpty() ? "#15566c" : ""}}">
                <i class="dropdown icon">
                </i>
                Cotización SORT
            </div>
            <div class="content">
                <div class="ui vertically divided small grid">
                    @foreach($quotations as $activity)
                        @unless($activity->finished_at && !Request::get('archived'))
                            <div class="row">

                                <div class="two wide column">
                                    <b>
                                        # de cotización:
                                    </b>
                                    <a class="header">
                                        {{ $activity->quotation ? $activity->quotation->formatNumber() : 'Id no disponible' }}
                                    </a>
                                </div>
                                <div class="actividad" data-fecha="{{ $activity->created_at }}" style="width:250px">
                                    <div class="two wide column">
                                        <b>Fecha de cotización:</b>
                                        <span class="fecha-creacion"></span>
                                    </div>
                                </div>
                                <div class="three wide column">
                                    <b>
                                        Monto de la prima:
                                    </b>
                                    {{ $activity->quotation ? $activity->quotation->temporality == 'short' ? 
                                    ($activity->quotation->type_currency === 'USD' ? '$' : '₡') . ' ' .  number_format($activity->quotation->single_payment_value, 2, ',', '.') : 
                                    ($activity->quotation->type_currency === 'USD' ? '$' : '₡') . ' ' . number_format($activity->quotation->annual_calculation_amount, 2, ',', '.') : ''  }}
                                   
                                </div>
                                <div class="three wide column">
                                    <b>
                                        Estado de la cotización:
                                    </b>
                                    {{ mb_strtoupper(mb_substr($activity->state->name, 0, 1), 'UTF-8') . mb_strtolower(mb_substr($activity->state->name, 1), 'UTF-8') }}

                                </div>
                                <div class="three wide column">
                                    <b>
                                        Código asesor:
                                    </b>
                                    <a>
                                        {{ $activity->quotation->code ?? ''}}
                                    </a>
                                </div>
                                <div class="one wide column">
                                    <a class="ui basic mini blue button"
                                       href="{{secure_url('servicio/' . $activity->id)}}">
                                        <i class="unhide icon"></i> Ver
                                    </a>
                                </div>
                            </div>
                        @endunless
                    @endforeach
                </div>

            </div>
        @endif
        {{-- Poliza SORT  --}}
        @if(Auth::user()->is_view_service(75) && $policy_sort->isNotEmpty())
            <div class="title" style="color: {{$policy_sort->isNotEmpty() ? "#15566c" : ""}}">
                <i class="dropdown icon">
                </i>
                Póliza SORT
            </div>
            <div class="content">
                <div class="ui vertically divided small grid">
                    @foreach($policy_sort as $activity)
                        @unless($activity->finished_at && !Request::get('archived'))
                            <div class="row">

                                <div class="two wide column">
                                    <b># de emisión:</b>
                                    <a class="header">
                                        {{ $activity->policy_sort ? $activity->policy_sort->id : 'Id no disponible' }}
                                    </a>
                                </div>

                                <div class="two wide column">
                                    <b>Póliza SORT:</b>
                                    <a class="header">
                                        {{ $activity->policy_sort ? $activity->policy_sort->formatNumberConsecutive() : 'Id no disponible' }}
                                    </a>
                                </div>

                                <div class="two wide column">
                                    <b>Fecha inicio vigencia:</b>
                                    {{ $activity->policy_sort && $activity->policy_sort->validity_from
                                        ? ucfirst(strftime('%A %e de %B del %Y', strtotime($activity->policy_sort->validity_from)))
                                        : 'Fecha no disponible'
                                    }}
                                </div>

                                <div class="two wide column">
                                    <b>Fecha fin vigencia:</b>
                                    {{ $activity->policy_sort && $activity->policy_sort->validity_to
                                        ? ucfirst(strftime('%A %e de %B del %Y', strtotime($activity->policy_sort->validity_to)))
                                        : 'Fecha no disponible'
                                    }}
                                </div>

                                <div class="two wide column">
                                    <b>Valor de la prima:</b>
                                    @php
                                        //monto de la prima
                                        switch ($activity->policy_sort->periodicity) {
                                            case 1: // Anual
                                                $calculatedAmount = $activity->policy_sort->annual_calculation_amount * 1;
                                                break;
                                            case 2: // Semestral
                                                $calculatedAmount = $activity->policy_sort->semiannual_calculation_amount * 2;
                                                break;
                                            case 3: // Trimestral
                                                $calculatedAmount = $activity->policy_sort->quarterly_calculation_amount * 4;
                                                break;
                                            case 4: // Mensual
                                                $calculatedAmount = $activity->policy_sort->monthly_calculation_amount * 12;
                                                break;
                                            default:
                                                $calculatedAmount = $activity->policy_sort->annual_calculation_amount;
                                                break;
                                        }
                                    @endphp
                                    @if ($activity->policy_sort)
                                        @if ($activity->policy_sort->temporality === 'permanent')
                                            {{ ($activity->policy_sort->type_currency === 'USD' ? '$' : '₡') . ' ' . number_format($calculatedAmount, 2, ',', '.') }}
                                        @else
                                            {{ ($activity->policy_sort->type_currency === 'USD' ? '$' : '₡') . ' ' . number_format($activity->policy_sort->single_payment_value, 2, ',', '.') }}
                                        @endif
                                    @else
                                        N/a
                                    @endif
                                </div>

                                <div class="two wide column">
                                    <b>Forma de pago:</b>
                                    @if(isset($activity->policy_sort) && $activity->policy_sort->temporality == 'permanent')
                                        {{ucfirst(mb_strtolower($PERIODICITYT[$activity->policy_sort->periodicity] ?? '')) }}
                                    @else
                                        Pago único
                                    @endif
                                </div>

                                <div class="three wide column">
                                    <b>Estado de la póliza:</b>
                                    {{ $activity->state && $activity->state->name
                                        ? ucfirst(mb_strtolower($activity->state->name))
                                        : 'Estado no disponible'
                                    }}
                                </div>


                                <div class="one wide column">
                                    <a class="ui basic mini blue button"
                                       href="{{ secure_url('servicio/' . $activity->id) }}">
                                        <i class="unhide icon"></i> Ver
                                    </a>
                                </div>

                            </div>

                        @endunless
                    @endforeach
                </div>
            </div>
        @endif
        {{--Cobros Poliza SORT  --}}
        @if(Auth::user()->is_view_service(76) && $policy_sort_collection->isNotEmpty())
            <div class="title" style="color: {{$policy_sort_collection->isNotEmpty() ? "#15566c" : ""}}">
                <i class="dropdown icon">
                </i>
                Cobro póliza SORT
            </div>
            <div class="content">
                <div class="ui vertically divided small grid">
                    @foreach($policy_sort_collection as $activity)
                        @unless($activity->finished_at && !Request::get('archived'))
                            <div class="row">
                                <div class="one wide column">
                                    <b>
                                        # de servicio:
                                    </b>
                                    <a class="header" href="{{secure_url('servicio/' . $activity->id)}}">
                                        {{$activity->id}}
                                    </a>
                                </div>
                                <div class="two wide column">
                                    <b>
                                        Tipo de trámite:
                                    </b>
                                    <a class="header" href="{{secure_url('servicio/' . $activity->id)}}">
                                        {{$activity->service->name}}
                                        @if ($activity->oid)
                                            <div style="font-size: 0.75em; color: gray;">
                                                OID: {{$activity->oid}}</div>
                                        @endif
                                    </a>
                                </div>
                                <div class="two wide column">
                                    <b>Fecha de solicitud:</b>
                                    {{ $activity && !empty($activity->policy_sort_collection->created_at)
                                       ? ucfirst(strftime('%A %e de %B del %Y', strtotime($activity->policy_sort_collection->created_at )))
                                       : 'Fecha no disponible'
                                    }}
                                </div>
                                <div class="two wide column">
                                    <b>
                                        Estado del servicio:
                                    </b>
                                    {{ mb_strtoupper(mb_substr($activity->state->name, 0, 1), 'UTF-8') . mb_strtolower(mb_substr($activity->state->name, 1), 'UTF-8') }}
                                </div>
                                <div class="two wide column">
                                    <b>
                                        Funcionario actual:
                                    </b>
                                    {{ucwords(strtolower($activity->user->full_name))}}
                                </div>
                                <div class="one wide column">
                                    <b>
                                        Póliza SORT:
                                    </b>
                                    @if(!empty($activity->parent_activity->policy_sort->id))
                                        <a class="header" href="{{secure_url('servicio/' . $activity->parent_activity->id)}}">

                                            {{ $activity->parent_activity->policy_sort->formatNumberConsecutive() }}

                                        </a>
                                    @endif
                                </div>
                                <div class="two wide column">
                                    <b> Fecha límite de pago del recibo:</b>
                                    {{ $activity && !empty($activity->policy_sort_collection->due_date)
                                       ? ucfirst(strftime('%A %e de %B del %Y', strtotime($activity->policy_sort_collection->due_date)))
                                       : 'Fecha no disponible'
                                    }}
                                </div>
                                <div class="one wide column">
                                    <b>
                                        Tipo de recibo:
                                    </b>
                                    {{$TYPE_RECEIPT[$activity->policy_sort_collection->type_receipt ?? ''] ?? ''}}
                                </div>
                                <div class="two wide column">
                                    <b>
                                        Valor a pagar:
                                    </b>
                                    @if(!empty($activity->policy_sort_collection->total_amount ?? ''))
                                        {{$MONEY_TYPE[$activity->parent_activity->policy_sort->type_currency ?? '']['symbol'] ?? ''}}
                                        {{number_format($activity->policy_sort_collection->total_amount, 2, ',', '.')}}
                                    @endif
                                </div>
                                <div class="one wide column">
                                    <a class="ui basic mini blue button"
                                       href="{{secure_url('servicio/' . $activity->id)}}">
                                        <i class="unhide icon"></i> Ver
                                    </a>
                                </div>
                            </div>
                        @endunless
                    @endforeach
                </div>

            </div>
        @endif
        {{--Variaciones SORT  --}}
        @if(Auth::user()->is_view_service(77) && $variations_sort->isNotEmpty())
            <div class="title" style="color: {{$variations_sort->isNotEmpty() ? "#15566c" : ""}}">
                <i class="dropdown icon">
                </i>
                Variaciones SORT
            </div>
            <div class="content">
                <div class="ui vertically divided small grid">
                    @foreach($variations_sort as $activity)
                        @unless($activity->finished_at && !Request::get('archived'))
                            <div class="row">
                                <div class="two wide column">
                                    <b>
                                       # de servicio:
                                    </b>
                                    <a class="header" href="{{secure_url('servicio/' . $activity->id)}}">
                                        {{$activity->id}}
                                    </a>
                                </div>
                                <div class="two wide column">
                                    <b>
                                        Tipo de trámite:
                                    </b>
                                    <a class="header" href="{{secure_url('servicio/' . $activity->id)}}">
                                        {{$activity->service->name}}
                                        @if ($activity->oid)
                                            <div style="font-size: 0.75em; color: gray;">
                                                OID: {{$activity->oid}}</div>
                                        @endif
                                    </a>
                                </div>
                                <div class="two wide column">
                                    <b>
                                        Fecha de solicitud:
                                    </b>
                                    {{ucfirst(strtolower($activity->created_at->formatLocalized('%A %d de %B de %Y')))}}
                                </div>
                                <div class="three wide column">
                                    <b>
                                        Estado del servicio:
                                    </b>
                                    {{ucfirst(mb_strtolower($activity->state->name))}}
                                </div>
                                <div class="two wide column">
                                    <b>
                                       # de radicado solicitud de variación:
                                    </b>
                                    {{isset($activity->variations_sort)? ucfirst(mb_strtolower($activity->variations_sort->id)):''}}
                                </div>
                                <div class="three wide column">
                                    <b>
                                        Tipo de variación solicitada:
                                    </b>
                                    {{isset($activity->variations_sort->variation_type) && isset($VARIATIONS_TYPE[$activity->variations_sort->variation_type])
            ? ucfirst(mb_strtolower($VARIATIONS_TYPE[$activity->variations_sort->variation_type]))
            : 'No disponible'}}
                                </div>
                                <div class="one wide column">
                                    <a class="ui basic mini blue button"
                                       href="{{secure_url('servicio/' . $activity->id)}}">
                                        <i class="unhide icon"></i> Ver
                                    </a>
                                </div>
                            </div>
                        @endunless
                    @endforeach
                </div>

            </div>
        @endif
        {{--CONSTANCIAS - POLIZA SORT  --}}
        @if(Auth::user()->is_view_service(78) && $constancy_sort->isNotEmpty())
            <div class="title" style="color: {{$constancy_sort->isNotEmpty() ? "#15566c" : ""}}">
                <i class="dropdown icon">
                </i>
                Constancias - poliza SORT
            </div>
            <div class="content">
                <div class="ui vertically divided small grid">
                    @foreach($constancy_sort as $activity)
                        @unless($activity->finished_at && !Request::get('archived'))
                            <div class="row">
                                <div class="two wide column">
                                    <b>
                                        # de Trámite:
                                    </b>
                                    <a class="header" href="{{secure_url('servicio/' . $activity->id)}}">
                                        {{$activity->id}}
                                    </a>
                                </div>
                                <div class="two wide column">
                                    <b>
                                        Tipo de trámite:
                                    </b>
                                    <a class="header" href="{{secure_url('servicio/' . $activity->id)}}">
                                        {{$activity->service->name}}
                                        @if ($activity->oid)
                                            <div style="font-size: 0.75em; color: gray;">
                                                OID: {{$activity->oid}}</div>
                                        @endif
                                    </a>
                                </div>
                                <div class="two wide column">
                                    <b>
                                        Tipo de constancia:
                                    </b>
                                    {{$activity->constancy_sort ? ucfirst(mb_strtolower($TYPE_CONSTANCY[$activity->constancy_sort->type ?? ''] ?? '')) :"No disponible"}}
                                </div>
                                <div class="two wide column">
                                    <b>
                                        Fecha del trámite:
                                    </b>
                                    {{ucfirst(strtolower($activity->created_at->formatLocalized('%A %d de %B de %Y')))}}
                                </div>
                                <div class="three wide column">
                                    <b>
                                        Estado:
                                    </b>
                                    {{ucfirst(mb_strtolower($activity->state->name))}}
                                </div>

                                <div class="three wide column">
                                    <b>
                                        Funcionario actual:
                                    </b>
                                    <a>
                                        {{ucwords(strtolower($activity->user->full_name))}}
                                    </a>
                                </div>
                                <div class="one wide column">
                                    <a class="ui basic mini blue button"
                                       href="{{secure_url('servicio/' . $activity->id)}}">
                                        <i class="unhide icon"></i> Ver
                                    </a>
                                </div>
                            </div>
                        @endunless
                    @endforeach
                </div>

            </div>
        @endif
        {{--REPORTE PLANILLA TOMADOR  --}}
        @if(Auth::user()->is_view_service(79) && $affiliate_taker_report->isNotEmpty())
            <div class="title" style="color: {{$affiliate_taker_report->isNotEmpty() ? "#15566c" : ""}}">
                <i class="dropdown icon">
                </i>
                Reporte planilla tomador
            </div>
            <div class="content">
                <div class="ui vertically divided small grid">
                    @foreach($affiliate_taker_report as $activity)
                        @unless($activity->finished_at && !Request::get('archived'))
                            <div class="row">
                                <div class="one wide column">
                                    <b>
                                        # de trámite:
                                    </b>
                                    <a class="header" href="{{secure_url('servicio/' . $activity->id)}}">
                                        {{$activity->id}}
                                    </a>
                                </div>
                                <div class="two wide column">
                                    <b>
                                        Tipo de trámite:
                                    </b>
                                    <a class="header" href="{{secure_url('servicio/' . $activity->id)}}">
                                        {{ucfirst(mb_strtolower($activity->service->name))}}
                                        @if ($activity->oid)
                                            <div style="font-size: 0.75em; color: gray;">
                                                OID: {{$activity->oid}}</div>
                                        @endif
                                    </a>
                                </div>
                                <div class="two wide column">
                                    <b>
                                        Fecha del trámite:
                                    </b>
                                    {{ucfirst(strtolower($activity->created_at->formatLocalized('%B %d, %Y')))}}
                                </div>
                                <div class="two wide column">
                                    <b>
                                        Estado:
                                    </b>
                                    {{ucfirst(mb_strtolower($activity->state->name))}}
                                </div>
                                <div class="one wide column">
                                    <b>
                                        # póliza:
                                    </b>
                                    <a class="header" href="{{secure_url('servicio/' . $activity->parent->id)}}">
                                        {{isset($activity->parent->policy_sort) ? $activity->parent->policy_sort->formatNumberConsecutive() : null}}
                                    </a>
                                </div>
                                <div class="two wide column">
                                    <b>
                                        Periodo de la planilla:
                                    </b>
                                    {{$activity->period}}
                                </div>
                                <div class="two wide column">
                                    <b>
                                        Funcionario actual:
                                    </b>
                                    {{ mb_convert_case($activity->user->full_name, MB_CASE_TITLE, "UTF-8")}}
                                </div>
                                <div class="one wide column">
                                    <a class="ui basic mini blue button"
                                       href="{{secure_url('servicio/' . $activity->id)}}">
                                        <i class="unhide icon"></i> Ver
                                    </a>
                                </div>
                            </div>
                        @endunless
                    @endforeach
                </div>

            </div>
        @endif
        {{--REPORTE PLANILLA AFILIADO  --}}
        @if(Auth::user()->is_view_service(80) && $affiliate_workforce_report->isNotEmpty())
            <div class="title" style="color: {{$affiliate_workforce_report->isNotEmpty() ? "#15566c" : ""}}">
                <i class="dropdown icon">
                </i>
                Reporte planilla asegurado
            </div>
            <div class="content">
                <div class="ui vertically divided small grid">
                    @foreach($affiliate_workforce_report as $activity)
                        @unless($activity->finished_at && !Request::get('archived'))
                            <div class="row">
                                <div class="two wide column">
                                    <b>
                                        # de servicio:
                                    </b>
                                    <a class="header" href="{{secure_url('servicio/' . $activity->id)}}">
                                        {{$activity->id}}
                                    </a>
                                </div>
                                <div class="two wide column">
                                    <b>
                                        Tipo de trámite:
                                    </b>
                                    <a class="header" href="{{secure_url('servicio/' . $activity->id)}}">
                                        {{$activity->service->name}}
                                        @if ($activity->oid)
                                            <div style="font-size: 0.75em; color: gray;">
                                                OID: {{$activity->oid}}</div>
                                        @endif
                                    </a>
                                </div>
                                <div class="two wide column">
                                    <b>
                                        Fecha de solicitud:
                                    </b>
                                    {{ ucfirst(strftime('%A %e de %B del %Y', strtotime($activity->created_at)))}}
                                </div>
                                <div class="three wide column">
                                    <b>
                                        Estado del servicio:
                                    </b>
                                    {{ ucfirst(mb_strtolower($activity->state->name, 'UTF-8')) }}
                                </div>
                                <div class="two wide column">
                                    <b>
                                        TI del afiliado:
                                    </b>
                                        {{ optional(optional($activity)->affiliate)->doc_type }} {{ optional(optional($activity)->affiliate)->doc_number }}
                                </div>
                                <div class="one wide column">
                                    <b>
                                        Periodo:
                                    </b>
                                    {{ $activity->determinePeriodAffiliateWorkforce() ?? '' }}
                                </div>
                                <div class="three wide column">
                                    <b>
                                        Nombre del afiliado:
                                    </b>
                                        {{ mb_convert_case(optional(optional($activity)->affiliate)->full_name, MB_CASE_TITLE, "UTF-8")}}
                                </div>
                                <div class="one wide column">
                                    <a class="ui basic mini blue button"
                                       href="{{secure_url('servicio/' . $activity->id)}}">
                                        <i class="unhide icon"></i> Ver
                                    </a>
                                </div>
                            </div>
                        @endunless
                    @endforeach
                </div>

            </div>
        @endif
        {{--PRESTACIONES MÉDICAS - SORT --}}
        @if(Auth::user()->is_view_service(83) && $medical_services_sort->isNotEmpty())
            <div class="title" style="color: {{$medical_services_sort->isNotEmpty() ? "#15566c" : ""}}">
                <i class="dropdown icon">
                </i>
                Prestaciones médicas - SORT
            </div>
            <div class="content">
                <div class="ui vertically divided small grid">
                    @foreach($medical_services_sort as $activity)
                        @unless($activity->finished_at && !Request::get('archived'))
                            @php
                                $dataPolicy = $activity->policy_sort_profile_affiliate_medical_service() ?? '';
                                $dataGis = $activity->gis_profile_affiliate_medical_service() ?? '';
                            @endphp
                            <div class="row">
                                <div class="one wide column">
                                    <b>
                                        # de servicio:
                                    </b>
                                    <a class="header" href="{{secure_url('servicio/' . $activity->id)}}">
                                        {{$activity->id}}
                                    </a>
                                </div>
                                <div class="two wide column">
                                    <b>
                                        Tipo de trámite:
                                    </b>
                                    <a class="header" href="{{secure_url('servicio/' . $activity->id)}}">
                                        {{$activity->service->name}}
                                        @if ($activity->oid)
                                            <div style="font-size: 0.75em; color: gray;">
                                                OID: {{$activity->oid}}</div>
                                        @endif
                                    </a>
                                </div>
                                <div class="two wide column">
                                    <b>Fecha de solicitud:</b>
                                    {{ $activity && !empty($activity->created_at)
                                      ? ucfirst(strftime('%A %e de %B del %Y', strtotime($activity->created_at )))
                                      : 'Fecha no disponible'
                                    }}
                                </div>
                                <div class="two wide column">
                                    <b>
                                        Estado del servicio:
                                    </b>
                                    {{ mb_strtoupper(mb_substr($activity->state->name, 0, 1), 'UTF-8') . mb_strtolower(mb_substr($activity->state->name, 1), 'UTF-8') }}
                                </div>
                                <div class="one wide column">
                                    <b>
                                        Tipo de servicio:
                                    </b>
                                    @php
                                        if ($activity->medical_services_sort) {
                                            $typeService = $activity->medical_services_sort->action_id;
                                            $serviceType = \App\Providers\AppServiceProvider::$TYPE_MEDICAL_SERVICE[$typeService] ?? 'Prestación médica';
                                        } else {
                                            $serviceType = 'Prestación médica';
                                        }
                                    @endphp

                                    {{ $serviceType }}
                                </div>
                                <div class="two wide column">
                                    <b>
                                        Nombre del paciente:
                                    </b>
                                    @if(!empty($activity->affiliate->full_name))
                                        {{ mb_convert_case(mb_strtolower($activity->affiliate->full_name, 'UTF-8'), MB_CASE_TITLE, 'UTF-8') }}
                                    @endif
                                </div>
                                <div class="one wide column">
                                    <b>
                                        Póliza SORT:
                                    </b>
                                    @if($dataPolicy)
                                        <a class="header" href="{{secure_url('servicio/' . $dataPolicy->activity_id)}}">

                                            {{ $dataPolicy->formatNumberConsecutive() }}

                                        </a>
                                    @endif
                                </div>
                                <div class="two wide column">
                                    <b>
                                        Fecha del caso:
                                    </b>
                                    @if($dataGis)
                                        {{ $activity && !empty($dataGis->created_at)
                                            ? ucfirst(strftime('%A %e de %B del %Y', strtotime($dataGis->created_at )))
                                            : 'Fecha no disponible'
                                        }}
                                    @endif
                                </div>
                                <div class="one wide column">
                                    <b>
                                        # del aviso:
                                    </b>
                                        @if($dataGis)
                                            {{ $dataGis->consecutive_gis }}
                                        @endif
                                </div>
                                <div class="one wide column">
                                    <b>
                                        # del caso:
                                    </b>
                                    @if($dataGis)
                                        {{ $dataGis->formatCaseNumberIfReported() }}
                                    @endif
                                </div>
                                <div class="one wide column">
                                    <a class="ui basic mini blue button"
                                       href="{{secure_url('servicio/' . $activity->id)}}">
                                        <i class="unhide icon"></i> Ver
                                    </a>
                                </div>
                            </div>
                        @endunless
                    @endforeach
                </div>
            </div>
        @endif
        {{--PRESTACIONES MÉDICAS - Atención secundaria - SORT --}}
        @if(Auth::user()->is_view_service(\App\Service::SERVICE_MEDICAL_SERVICES_SECONDARY_CARE_SORT_MNK) &&  $medical_services_secondary_care_sort->isNotEmpty())
            <div class="title" style="color: {{$medical_services_secondary_care_sort->isNotEmpty() ? "#15566c" : ""}}">
                <i class="dropdown icon">
                </i>
                Prestación médica SORT - atención secundaria
            </div>
            <div class="content">
                <div class="ui vertically divided small grid">
                    @foreach($medical_services_secondary_care_sort as $activity)
                        @unless($activity->finished_at && !Request::get('archived'))
                            @php
                                $dataPolicy = $activity->policy_sort_profile_affiliate_medical_service() ?? '';
                                $dataGis = $activity->gis_profile_affiliate_medical_service() ?? '';
                            @endphp
                            <div class="row">
                                <div class="one wide column">
                                    <b>
                                        # de servicio:
                                    </b>
                                    <a class="header" href="{{secure_url('servicio/' . $activity->id)}}">
                                        {{$activity->id}}
                                    </a>
                                </div>
                                <div class="two wide column">
                                    <b>
                                        Tipo de trámite:
                                    </b>
                                    <a class="header" href="{{secure_url('servicio/' . $activity->id)}}">
                                        {{$activity->service->name}}
                                        @if ($activity->oid)
                                            <div style="font-size: 0.75em; color: gray;">
                                                OID: {{$activity->oid}}</div>
                                        @endif
                                    </a>
                                </div>
                                <div class="two wide column">
                                    <b>Fecha de solicitud:</b>
                                    {{ $activity && !empty($activity->created_at)
                                      ? ucfirst(strftime('%A %e de %B del %Y', strtotime($activity->created_at )))
                                      : 'Fecha no disponible'
                                    }}
                                </div>
                                <div class="two wide column">
                                    <b>
                                        Estado del servicio:
                                    </b>
                                    {{ mb_strtoupper(mb_substr($activity->state->name, 0, 1), 'UTF-8') . mb_strtolower(mb_substr($activity->state->name, 1), 'UTF-8') }}
                                </div>
                                <div class="one wide column">
                                    <b>
                                        Tipo de servicio:
                                    </b>
                                    @php
                                        if ($activity->medical_services_sort) {
                                            $typeService = $activity->medical_services_sort->action_id;
                                            $serviceType = \App\Providers\AppServiceProvider::$TYPE_MEDICAL_SERVICE[$typeService] ?? 'Prestación médica';
                                        } else {
                                            $serviceType = 'Prestación médica';
                                        }
                                    @endphp

                                    {{ $serviceType }}
                                </div>
                                <div class="two wide column">
                                    <b>
                                        Nombre del paciente:
                                    </b>
                                    @if(!empty($activity->affiliate->full_name))
                                        {{ mb_convert_case(mb_strtolower($activity->affiliate->full_name, 'UTF-8'), MB_CASE_TITLE, 'UTF-8') }}
                                    @endif
                                </div>
                                <div class="one wide column">
                                    <b>
                                        Póliza SORT:
                                    </b>
                                    @if($dataPolicy)
                                        <a class="header" href="{{secure_url('servicio/' . $dataPolicy->activity_id)}}">

                                            {{ $dataPolicy->formatSortNumber() }}

                                        </a>
                                    @endif
                                </div>
                                <div class="two wide column">
                                    <b>
                                        Fecha del caso:
                                    </b>
                                    @if($dataGis)
                                        {{ $activity && !empty($dataGis->created_at)
                                            ? ucfirst(strftime('%A %e de %B del %Y', strtotime($dataGis->created_at )))
                                            : 'Fecha no disponible'
                                        }}
                                    @endif
                                </div>

                                <div class="one wide column">
                                    <b>
                                        # del aviso:
                                    </b>
                                    @if($dataGis)
                                        {{ $dataGis->consecutive_gis }}
                                    @endif
                                </div>
                                <div class="one wide column">
                                    <b>
                                        # del caso:
                                    </b>
                                    @if($dataGis)
                                        {{ $dataGis->formatCaseNumberIfReported() }}
                                    @endif
                                </div>

                                <div class="one wide column">
                                    <a class="ui basic mini blue button"
                                       href="{{secure_url('servicio/' . $activity->id)}}">
                                        <i class="unhide icon"></i> Ver
                                    </a>
                                </div>
                            </div>
                        @endunless
                    @endforeach
                </div>
            </div>
        @endif
        {{--PE IT - SORT --}}
        @if(Auth::user()->is_view_service(\App\Service::SERVICE_PE_IT_SORT_MNK) && $pe_it_sort->isNotEmpty())
            <div class="title" style="color: {{$pe_it_sort->isNotEmpty() ? "#15566c" : ""}}">
                <i class="dropdown icon">
                </i>
                PE - Incapacidad temporal SORT
            </div>
            <div class="content">
                <div class="ui vertically divided small grid">
                    @foreach($pe_it_sort as $activity)
                        @unless($activity->finished_at && !Request::get('archived'))
                            <div class="row">
                                <div class="two wide column">
                                    <b>
                                        # de servicio:
                                    </b>
                                    <a class="header" href="{{secure_url('servicio/' . $activity->id)}}">
                                        {{$activity->id}}
                                    </a>
                                </div>
                                <div class="two wide column">
                                    <b>
                                        Tipo de trámite:
                                    </b>
                                    <a class="header" href="{{secure_url('servicio/' . $activity->id)}}">
                                        {{$activity->service->name}}
                                        @if ($activity->oid)
                                            <div style="font-size: 0.75em; color: gray;">
                                                OID: {{$activity->oid}}</div>
                                        @endif
                                    </a>
                                </div>
                                <div class="two wide column">
                                    <b>
                                        Fecha del trámite:
                                    </b>
                                    {{ ucfirst(strftime('%A %e de %B del %Y', strtotime($activity->created_at)))}}
                                </div>
                                <div class="two wide column">
                                    <b>
                                        Estado del servicio:
                                    </b>
                                    {{ mb_strtoupper(mb_substr($activity->state->name, 0, 1), 'UTF-8') . mb_strtolower(mb_substr($activity->state->name, 1), 'UTF-8') }}
                                </div>
                                <div class="two wide column">
                                    <b>
                                        Funcionario actual:
                                    </b>
                                        {{ mb_convert_case($activity->user->full_name, MB_CASE_TITLE, "UTF-8") }}
                                </div>
                                <div class="two wide column">
                                    <b>
                                        Días IT:
                                    </b>
                                    {{ $activity->peItSort->inabilitySort->days_it ?? "" }}
                                </div>
                                <div class="two wide column">
                                    <b>
                                        Valor a pagar IT:
                                    </b>
                                    {{ $activity->peItSort->inabilitySort->amount_pay ?? "Pendiente" }}
                                </div>
                                <div class="one wide column">
                                    <a class="ui basic mini blue button"
                                       href="{{secure_url('servicio/' . $activity->id)}}">
                                        <i class="unhide icon"></i> Ver
                                    </a>
                                </div>
                            </div>
                        @endunless
                    @endforeach
                </div>

            </div>
        @endif
        {{--PE IP - SORT --}}
        @if(Auth::user()->is_view_service(86) && $pe_ip_sort->isNotEmpty())
            <div class="title" style="color: {{$pe_ip_sort->isNotEmpty() ? "#15566c" : ""}}">
                <i class="dropdown icon">
                </i>
                PE - Incapacidad permanente SORT
            </div>
            <div class="content">
                <div class="ui vertically divided small grid">
                    @foreach($pe_ip_sort as $activity)
                        @unless($activity->finished_at && !Request::get('archived'))
                            <div class="row">
                                <div class="two wide column">
                                    <b>
                                        # de trámite:
                                    </b>
                                    <a class="header" href="{{secure_url('servicio/' . $activity->id)}}">
                                        {{$activity->id}}
                                    </a>
                                </div>
                                <div class="two wide column">
                                    <b>
                                        Tipo de trámite:
                                    </b>
                                    <a class="header" href="{{secure_url('servicio/' . $activity->id)}}">
                                        {{$activity->service->name}}
                                        @if ($activity->oid)
                                            <div style="font-size: 0.75em; color: gray;">
                                                OID: {{$activity->oid}}</div>
                                        @endif
                                    </a>
                                </div>
                                <div class="two wide column">
                                    <b>
                                        Fecha del trámite:
                                    </b>
                                    {{ ucfirst(strftime('%A %e de %B del %Y', strtotime($activity->created_at)))}}
                                </div>
                                <div class="two wide column">
                                    <b>
                                        Estado del servicio:
                                    </b>
                                    {{ mb_strtoupper(mb_substr($activity->state->name, 0, 1), 'UTF-8') . mb_strtolower(mb_substr($activity->state->name, 1), 'UTF-8') }}
                                </div>
                                <div class="two wide column">
                                    <b>
                                        Funcionario actual:
                                    </b>
                                    <a>
                                        {{ mb_convert_case($activity->user->full_name, MB_CASE_TITLE, "UTF-8") }}
                                    </a>
                                </div>
                                <div class="two wide column">
                                    <b>
                                        Tipo de incapacidad permanente:
                                    </b>
                                    {{ $activity->pe_ip_sort->casedata_type_disability ?? "" }}
                                </div>
                                <div class="two wide column">
                                    <b>
                                        Renta por incapacidad permanente:
                                    </b>
                                    {{ preg_replace('/[^a-zA-Z€\$\£\¥\₣₤₯₰₲]/', '', $activity->pe_ip_sort->calc_annual_income ?? '') . ' ' . number_format((float) str_replace([',', ' '], '', preg_replace('/[^0-9,\.]/', '', $activity->pe_ip_sort->calc_annual_income ?? '')), 2, ',', '.') }}

                                </div>
                                <div class="two wide column">
                                    <a class="ui basic mini blue button"
                                       href="{{secure_url('servicio/' . $activity->id)}}">
                                        <i class="unhide icon"></i> Ver
                                    </a>
                                </div>
                            </div>
                        @endunless
                    @endforeach
                </div>

            </div>
        @endif
        {{--MEDICAMENTOS --}}
        @if(Auth::user()->is_view_service(87) && $medication->isNotEmpty())
            <div class="title" style="color: {{$medication->isNotEmpty() ? "#15566c" : ""}}">
                <i class="dropdown icon">
                </i>
                Medicamentos
            </div>
            <div class="content">
                <div class="ui vertically divided small grid">
                    @foreach($medication as $activity)
                        @unless($activity->finished_at && !Request::get('archived'))
                            @php
                                $dataPolicyMedication = $activity->policy_sort_profile_affiliate() ?? '';
                                $dataGisMedication = $activity->gis_profile_affiliate() ?? '';
                            @endphp
                            <div class="row">
                                <div class="one wide column">
                                    <b>
                                        # de servicio:
                                    </b>
                                    <a class="header" href="{{secure_url('servicio/' . $activity->id)}}">
                                        {{$activity->id}}
                                    </a>
                                </div>
                                <div class="two wide column">
                                    <b>
                                        Tipo de trámite:
                                    </b>
                                    <a class="header" href="{{secure_url('servicio/' . $activity->id)}}">
                                        {{$activity->service->name}}
                                        @if ($activity->oid)
                                            <div style="font-size: 0.75em; color: gray;">
                                                OID: {{$activity->oid}}</div>
                                        @endif
                                    </a>
                                </div>
                                <div class="two wide column">
                                    <b>Fecha de solicitud:</b>
                                    {{ $activity && !empty($activity->created_at)
                                      ? ucfirst(strftime('%A %e de %B del %Y', strtotime($activity->created_at )))
                                      : 'Fecha no disponible'
                                    }}
                                </div>
                                <div class="two wide column">
                                    <b>
                                        Estado del servicio:
                                    </b>
                                    {{ mb_strtoupper(mb_substr($activity->state->name, 0, 1), 'UTF-8') . mb_strtolower(mb_substr($activity->state->name, 1), 'UTF-8') }}
                                </div>
                                <div class="two wide column">
                                    <b>
                                        Tipo de servicio:
                                    </b>
                                    @php
                                        $typeService = $activity->type_service_medication;
                                        switch ($typeService) {
                                            case "controlled-medication-formula":
                                                $serviceLabel = "Fórmula de medicamentos controlados";
                                                break;
                                            case "prescription":
                                                $serviceLabel = "Fórmula médica";
                                                break;
                                            default:
                                                $serviceLabel = "Tipo de servicio desconocido";
                                        }
                                    @endphp

                                    {{ $serviceLabel }}
                                </div>
                                <div class="two wide column">
                                    <b>
                                        Nombre del paciente:
                                    </b>
                                    @if(!empty($activity->parent_activity->affiliate->full_name))
                                        {{ mb_convert_case(mb_strtolower(optional($activity->parent_activity->affiliate)->full_name, 'UTF-8'), MB_CASE_TITLE, 'UTF-8') }}
                                    @endif
                                </div>
                                <div class="one wide column">
                                    <b>
                                        Póliza SORT:
                                    </b>
                                    @if($dataPolicyMedication)
                                        <a class="header" href="{{secure_url('servicio/' . $dataPolicyMedication->activity_id)}}">

                                            {{ $dataPolicyMedication->formatSortNumber()}}

                                        </a>
                                    @endif
                                </div>
                                <div class="two wide column">
                                    <b>
                                        Fecha del caso:
                                    </b>
                                    @if($dataGisMedication)
                                        {{ $activity && !empty($dataGisMedication->created_at)
                                         ? ucfirst(strftime('%A %e de %B del %Y', strtotime($dataGisMedication->created_at )))
                                         : 'Fecha no disponible'
                                       }}
                                    @endif
                                </div>

                                <div class="one wide column">
                                    <b>
                                        # del aviso:
                                    </b>
                                    @if($dataGisMedication)
                                        {{ $dataGisMedication->consecutive_gis }}
                                    @endif
                                </div>
                                <div class="one wide column">
                                    <b>
                                        # del caso:
                                    </b>
                                    @if($dataGisMedication)
                                        {{ $dataGisMedication->formatCaseNumberIfReported() }}
                                    @endif
                                </div>

                                <div class="one wide column">
                                    <a class="ui basic mini blue button"
                                       href="{{secure_url('servicio/' . $activity->id)}}">
                                        <i class="unhide icon"></i> Ver
                                    </a>
                                </div>
                            </div>
                        @endunless
                    @endforeach
                </div>
            </div>
        @endif
        {{--GESTIÓN INTEGRAL DEL SINIESTRO - SORT --}}
        @if(Auth::user()->is_view_service(88) && $gist_sort->isNotEmpty())
            <div class="title" style="color: {{$gist_sort->isNotEmpty() ? "#15566c" : ""}}">
                <i class="dropdown icon">
                </i>
                Gestión integral del siniestro - SORT
            </div>
            <div class="content">
                <div class="ui vertically divided small grid">
                    @foreach($gist_sort as $activity)
                        @unless($activity->finished_at && !Request::get('archived'))
                            <div class="row">
                                <div class="two wide column">
                                    <b>
                                        # de identificación:
                                    </b>
                                    <a>
                                        {{$activity->affiliate? optional(optional($activity)->affiliate)->doc_type .' '. optional(optional($activity)->affiliate)->doc_number :''}}
                                    </a>
                                </div>

                                <div class="two wide column">
                                    <b>
                                        Nombre:
                                    </b>
                                    <a>
                                        {{ $activity->affiliate
                                            ? mb_convert_case(mb_strtolower(optional(optional($activity)->affiliate)->full_name, 'UTF-8'), MB_CASE_TITLE, 'UTF-8')
                                            : '' }}
                                    </a>
                                </div>

                                <div class="three wide column">
                                    <b>
                                        Fecha del caso:
                                    </b>
                                    {{$activity->created_at->formatLocalized('%A %d de %B de %Y')}}
                                </div>

                                <div class="one wide column">
                                    <b>
                                        # del aviso:
                                    </b>
                                        {{ isset($activity->gis_sort) ? $activity->gis_sort->consecutive_gis : '' }}
                                </div>
                                <div class="one wide column">
                                    <b>
                                        # del caso:
                                    </b>
                                        {{ isset($activity->gis_sort) ? $activity->gis_sort->formatCaseNumberIfReported() : '' }}
                                </div>

                                <div class="three wide column">
                                    <b>
                                        Estado:
                                    </b>
                                    {{ucfirst(mb_strtolower($activity->state->name))}}
                                </div>
                                <div class="one wide column">
                                    <a class="ui basic mini blue button"
                                       href="{{secure_url('servicio/' . $activity->id)}}">
                                        <i class="unhide icon"></i> Ver
                                    </a>
                                </div>
                            </div>
                        @endunless
                    @endforeach
                </div>

            </div>
        @endif
        {{--PE MPT - SORT --}}
        @if(Auth::user()->is_view_service(\App\Service::SERVICE_PE_MPT_SORT_MNK) && $pe_mpt_sort->isNotEmpty())
            <div class="title" style="color: {{$pe_mpt_sort->isNotEmpty() ? "#15566c" : ""}}">
                <i class="dropdown icon">
                </i>
                PE - Muerte persona trabajadora SORT
            </div>
            <div class="content">
                <div class="ui vertically divided small grid">
                    @foreach($pe_mpt_sort as $activity)
                        @unless($activity->finished_at && !Request::get('archived'))
                            <div class="row">
                                <div class="two wide column">
                                    <b>
                                        # de trámite:
                                    </b>
                                    <a class="header" href="{{secure_url('servicio/' . $activity->id)}}">
                                        {{$activity->id}}
                                    </a>
                                </div>
                                <div class="two wide column">
                                    <b>
                                        Tipo de trámite:
                                    </b>
                                    <a class="header" href="{{secure_url('servicio/' . $activity->id)}}">
                                        {{$activity->service->name}}
                                        @if ($activity->oid)
                                            <div style="font-size: 0.75em; color: gray;">
                                                OID: {{$activity->oid}}</div>
                                        @endif
                                    </a>
                                </div>
                                <div class="two wide column">
                                    <b>
                                        Fecha del trámite:
                                    </b>
                                    {{ ucfirst(strftime('%A %e de %B del %Y', strtotime($activity->created_at)))}}
                                </div>
                                <div class="two wide column">
                                    <b>
                                        Estado del servicio:
                                    </b>
                                    {{ mb_strtoupper(mb_substr($activity->state->name, 0, 1), 'UTF-8') . mb_strtolower(mb_substr($activity->state->name, 1), 'UTF-8') }}
                                </div>
                                <div class="two wide column">
                                    <b>
                                        Funcionario actual:
                                    </b>
                                    <a>
                                        {{ mb_convert_case($activity->user->full_name, MB_CASE_TITLE, "UTF-8") }}
                                    </a>
                                </div>
                                <div class="two wide column">
                                    <b>
                                        Renta Anual:
                                    </b>
                                    {{ preg_replace('/[^a-zA-Z€\$\£\¥\₣₤₯₰₲]/', '', $activity->pe_mpt_sort->annual_income ?? '') . ' ' . number_format((float) str_replace([',', ' '], '', preg_replace('/[^0-9,\.]/', '', $activity->pe_mpt_sort->annual_income ?? '')), 2, ',', '.') }}
                                </div>
                                <div class="two wide column">
                                    <b>
                                        Total de años a pagar:
                                    </b>
                                    {{ $activity->pe_mpt_sort->total_years_to_pay ?? "" }}
                                </div>
                                <div class="one wide column">
                                    <a class="ui basic mini blue button"
                                       href="{{secure_url('servicio/' . $activity->id)}}">
                                        <i class="unhide icon"></i> Ver
                                    </a>
                                </div>
                            </div>
                        @endunless
                    @endforeach
                </div>

            </div>
        @endif
        {{--CUENTAS MEDICAS --}}
        @if(Auth::user()->is_view_service(90) && $medical_bills->isNotEmpty())
            <div class="title" style="color: {{$medical_bills->isNotEmpty() ? "#15566c" : ""}}">
                <i class="dropdown icon">
                </i>
                Cuentas médicas
            </div>
            <div class="content">
                <div class="ui vertically divided small grid">
                    @foreach($medical_bills as $activity)
                        @unless($activity->finished_at && !Request::get('archived'))
                            <div class="row">
                                <div class="two wide column">
                                    <b>
                                        # de servicio:
                                    </b>
                                    <a class="header" href="{{secure_url('servicio/' . $activity->id)}}">
                                        {{$activity->id}}
                                    </a>
                                </div>
                                <div class="two wide column">
                                    <b>
                                        Tipo de trámite:
                                    </b>
                                    <a class="header" href="{{secure_url('servicio/' . $activity->id)}}">
                                        {{$activity->service->name}}
                                        @if ($activity->oid)
                                            <div style="font-size: 0.75em; color: gray;">
                                                OID: {{$activity->oid}}</div>
                                        @endif
                                    </a>
                                </div>
                                <div class="two wide column">
                                    <b>Fecha de solicitud:</b>
                                    {{ $activity && !empty($activity->created_at)
                                      ? ucfirst(strftime('%A %e de %B del %Y', strtotime($activity->created_at )))
                                      : 'Fecha no disponible'
                                    }}
                                </div>
                                <div class="two wide column">
                                    <b>
                                        Estado del servicio:
                                    </b>
                                    {{ mb_strtoupper(mb_substr($activity->state->name, 0, 1), 'UTF-8') . mb_strtolower(mb_substr($activity->state->name, 1), 'UTF-8') }}
                                </div>
                                <div class="two wide column">
                                    <b>
                                        Nombre del proveedor:
                                    </b>
                                    {{ $activity && !empty(optional(optional($activity->medical_bill)->provider())->name)
                                         ? mb_convert_case(mb_strtolower(optional(optional($activity->medical_bill)->provider())->name, 'UTF-8'), MB_CASE_TITLE, 'UTF-8')
                                         : 'Proveedor no especificado'
                                     }}
                                </div>
                                <div class="two wide column">
                                    <b>Fecha radicación factura:</b>
                                    {{ $activity && !empty($activity->medical_bill->date_created_electronic_invoice)
                                        ? ucfirst(strftime('%A %e de %B del %Y', strtotime($activity->medical_bill->date_created_electronic_invoice )))
                                        : 'Factura electrónica sin radicar'
                                    }}
                                </div>
                                <div class="two wide column">
                                    <b>
                                        Valor total factura:
                                    </b>
                                    @if(!empty($activity->medical_bill->total_value_invoice))
                                        {{$TYPE_CURRENCY[$activity->medical_bill->type_currency] ?? ''}}
                                        {{number_format($activity->medical_bill->total_value_invoice, 2, ',', '.')}}
                                    @else
                                        Factura electrónica sin enviar
                                    @endif
                                </div>
                                <div class="one wide column">
                                    <a class="ui basic mini blue button"
                                       href="{{secure_url('servicio/' . $activity->id)}}">
                                        <i class="unhide icon"></i> Ver
                                    </a>
                                </div>
                            </div>
                        @endunless
                    @endforeach
                </div>
            </div>
        @endif
        {{--PE RECONOCIMIENTO DE GASTOS --}}
        @if(Auth::user()->is_view_service(\App\Service::SERVICE_PE_RECOGNITION_EXPENSES_MNK) && $pe_recognition_expenses->isNotEmpty() )
            <div class="title" style="color: {{$pe_recognition_expenses->isNotEmpty() ? "#15566c" : ""}}">
                <i class="dropdown icon">
                </i>
                Pe - Reconocimiento de gastos SORT
            </div>
            <div class="content">
                <div class="ui vertically divided small grid">
                    @foreach($pe_recognition_expenses as $activity)
                        @unless($activity->finished_at && !Request::get('archived'))
                            <div class="row">
                                <div class="one wide column">
                                    <b>
                                        # de servicio:
                                    </b>
                                    <a class="header" href="{{secure_url('servicio/' . $activity->id)}}">
                                        {{$activity->id}}
                                    </a>
                                </div>
                                <div class="two wide column">
                                    <b>
                                        Tipo de trámite:
                                    </b>
                                    <a class="header" href="{{secure_url('servicio/' . $activity->id)}}">
                                        {{$activity->service->name}}
                                        @if ($activity->oid)
                                            <div style="font-size: 0.75em; color: gray;">
                                                OID: {{$activity->oid}}</div>
                                        @endif
                                    </a>
                                </div>
                                <div class="two wide column">
                                    <b>
                                        Fecha del trámite:
                                    </b>
                                    {{ ucfirst(strftime('%A %e de %B del %Y', strtotime($activity->created_at)))}}
                                </div>
                                <div class="three wide column">
                                    <b>
                                        Estado del servicio:
                                    </b>
                                    {{ mb_strtoupper(mb_substr($activity->state->name, 0, 1), 'UTF-8') . mb_strtolower(mb_substr($activity->state->name, 1), 'UTF-8') }}
                                </div>
                                <div class="two wide column">
                                    <b>
                                        Funcionario actual:
                                    </b>
                                    <a>
                                        {{ mb_convert_case($activity->user->full_name, MB_CASE_TITLE, "UTF-8") }}
                                    </a>
                                </div>
                                <div class="two wide column">
                                    <b>
                                        Tipo de gasto:
                                    </b>
                                        {{ $activity->pe_recognition_expenses && $activity->pe_recognition_expenses->invoice_concepts
                                            ? (\App\Providers\AppServiceProvider::$TYPE_EXPENSE[$activity->pe_recognition_expenses->invoice_concepts] ?? '')
                                            : ''
                                        }}
                                </div>
                                <div class="three wide column">
                                    <b>
                                        Valor aceptado para reconocimiento de gastos:
                                    </b>
                                        {{$MONEY_TYPE[$activity->parent_activity->gis_sort->activity->parent_activity->policy_sort->type_currency ?? '']['symbol'] ?? ''}}
                                        {{number_format($activity->pe_recognition_expenses->accepted_value_of_recognition ?? 0, 2, ',', '.') }}
                                </div>
                                <div class="one wide column">
                                    <a class="ui basic mini blue button"
                                       href="{{secure_url('servicio/' . $activity->id)}}">
                                        <i class="unhide icon"></i> Ver
                                    </a>
                                </div>
                            </div>
                        @endunless
                    @endforeach
                </div>
            </div>
        @endif
        {{--INSUMOS O MOT --}}
        @if(Auth::user()->is_view_service(\App\Service::SERVICE_SUPPLIER_MOT_MNK) && $supplier_mot->isNotEmpty())
            <div class="title" style="color: {{$supplier_mot->isNotEmpty() ? "#15566c" : ""}}">
                <i class="dropdown icon">
                </i>
                Insumos o MOT
            </div>
            <div class="content">
                <div class="ui vertically divided small grid">
                    @foreach($supplier_mot as $activity)
                        @unless($activity->finished_at && !Request::get('archived'))
                            @php
                                $dataPolicySuppliesMot = $activity->policy_sort_profile_affiliate() ?? '';
                                $dataGisSuppliesMot = $activity->gis_profile_affiliate() ?? '';
                            @endphp

                            <div class="row">
                                <div class="two wide column">
                                    <b>
                                        # de servicio:
                                    </b>
                                    <a class="header" href="{{secure_url('servicio/' . $activity->id)}}">
                                        {{$activity->id}}
                                    </a>
                                </div>
                                <div class="two wide column">
                                    <b>
                                        Tipo de trámite:
                                    </b>
                                    <a class="header" href="{{secure_url('servicio/' . $activity->id)}}">
                                        {{$activity->service->name}}
                                        @if ($activity->oid)
                                            <div style="font-size: 0.75em; color: gray;">
                                                OID: {{$activity->oid}}</div>
                                        @endif
                                    </a>
                                </div>
                                <div class="two wide column">
                                    <b>Fecha de solicitud:</b>
                                    {{ $activity && !empty($activity->created_at)
                                      ? ucfirst(strftime('%A %e de %B del %Y', strtotime($activity->created_at )))
                                      : 'Fecha no disponible'
                                    }}
                                </div>
                                <div class="two wide column">
                                    <b>
                                        Estado del servicio:
                                    </b>
                                    {{ mb_strtoupper(mb_substr($activity->state->name, 0, 1), 'UTF-8') . mb_strtolower(mb_substr($activity->state->name, 1), 'UTF-8') }}
                                </div>
                                <div class="two wide column">
                                    <b>
                                        Nombre del paciente:
                                    </b>
                                    @if(!empty($activity->parent_activity->affiliate->full_name))
                                        {{ mb_convert_case(mb_strtolower(optional(optional($activity)->parent_activity)->affiliate->full_name, 'UTF-8'), MB_CASE_TITLE, 'UTF-8') }}
                                    @endif
                                </div>
                                <div class="one wide column">
                                    <b>
                                        Póliza SORT:
                                    </b>
                                    @if($dataPolicySuppliesMot)
                                        <a class="header" href="{{secure_url('servicio/' . $dataPolicySuppliesMot->activity_id)}}">

                                            {{ $dataPolicySuppliesMot->formatSortNumber() }}

                                        </a>
                                    @endif
                                </div>
                                <div class="two wide column">
                                    <b>Fecha del caso:</b>
                                    @if($dataGisSuppliesMot)
                                        {{ $activity && !empty($dataGisSuppliesMot->created_at)
                                           ? ucfirst(strftime('%A %e de %B del %Y', strtotime($dataGisSuppliesMot->created_at )))
                                           : 'Fecha no disponible'
                                        }}
                                    @endif
                                </div>

                                <div class="one wide column">
                                    <b>
                                        # del aviso:
                                    </b>
                                    @if($dataGisSuppliesMot)
                                        {{ $dataGisSuppliesMot->consecutive_gis }}
                                    @endif
                                </div>
                                <div class="one wide column">
                                    <b>
                                        # del caso:
                                    </b>
                                    @if($dataGisSuppliesMot)
                                        {{ $dataGisSuppliesMot->formatCaseNumberIfReported()}}
                                    @endif
                                </div>

                                <div class="one wide column">
                                    <a class="ui basic mini blue button"
                                       href="{{secure_url('servicio/' . $activity->id)}}">
                                        <i class="unhide icon"></i> Ver
                                    </a>
                                </div>
                            </div>
                        @endunless
                    @endforeach
                </div>
            </div>
        @endif
        {{--PAGOS DE AFILIADOS--}}
        @if(Auth::user()->is_view_service(\App\Service::SERVICE_AFFILIATE_PAYMENT_MNK) && $affiliate_payment->isNotEmpty())
            <div class="title" style="color: {{$affiliate_payment->isNotEmpty() ? "#15566c" : ""}}">
                <i class="dropdown icon">
                </i>
                Pagos asegurados SORT
            </div>
            <div class="content">
                <div class="ui vertically divided small grid">
                    @foreach($affiliate_payment as $activity)
                        @unless($activity->finished_at && !Request::get('archived'))
                            <div class="row">
                                <div class="two wide column">
                                    <b>
                                        # de Trámite:
                                    </b>
                                    <a class="header" href="{{secure_url('servicio/' . $activity->id)}}">
                                        {{$activity->id}}
                                    </a>
                                </div>
                                <div class="two wide column">
                                    <b>
                                        Tipo de trámite:
                                    </b>
                                    <a class="header" href="{{secure_url('servicio/' . $activity->id)}}">
                                        {{$activity->service->name}}
                                        @if ($activity->oid)
                                            <div style="font-size: 0.75em; color: gray;">
                                                OID: {{$activity->oid}}</div>
                                        @endif
                                    </a>
                                </div>
                                <div class="two wide column">
                                    <b>
                                        Fecha del trámite:
                                    </b>
                                    {{ ucfirst(strftime('%A %e de %B del %Y', strtotime($activity->created_at)))}}
                                </div>
                                <div class="three wide column">
                                    <b>
                                        Estado:
                                    </b>
                                    {{ mb_strtoupper(mb_substr($activity->state->name, 0, 1), 'UTF-8') . mb_strtolower(mb_substr($activity->state->name, 1), 'UTF-8') }}
                                </div>
                                <div class="three wide column">
                                    <b>
                                        Funcionario actual:
                                    </b>
                                    <a>
                                        {{ mb_convert_case($activity->user->full_name, MB_CASE_TITLE, "UTF-8") }}
                                    </a>
                                </div>
                                <div class="one wide column">
                                    <a class="ui basic mini blue button"
                                       href="{{secure_url('servicio/' . $activity->id)}}">
                                        <i class="unhide icon"></i> Ver
                                    </a>
                                </div>
                            </div>
                        @endunless
                    @endforeach
                </div>
            </div>
        @endif
        {{--LIQUIDACIÓN PÓLIZA SORT--}}
        @if(Auth::user()->is_view_service(\App\Service::SERVICE_LIQUIDATION_SORT_MNK) && $liquidation->isNotEmpty())
            <div class="title" style="color: {{$liquidation->isNotEmpty() ? "#15566c" : ""}}">
                <i class="dropdown icon">
                </i>
                Liquidación póliza SORT
            </div>
            <div class="content">
                <div class="ui vertically divided small grid">
                    @foreach($liquidation as $activity)
                        @unless($activity->finished_at && !Request::get('archived'))
                            <div class="row">
                                <div class="two wide column">
                                    <b>
                                        # servicio:
                                    </b>
                                    <a class="header" href="{{secure_url('servicio/' . $activity->id)}}">
                                        {{$activity->id}}
                                    </a>
                                </div>
                                <div class="two wide column">
                                    <b>
                                        Póliza SORT:
                                    </b>
                                    {{$activity->parent_activity->policy_sort->formatSortNumber() ?? ''}}
                                </div>

                                <div class="two wide column">
                                    <b>
                                        Tipo de trámite:
                                    </b>
                                    <a class="header" href="{{secure_url('servicio/' . $activity->id)}}">
                                        {{$activity->service->name}}
                                        @if ($activity->oid)
                                            <div style="font-size: 0.75em; color: gray;">
                                                OID: {{$activity->oid}}</div>
                                        @endif
                                    </a>
                                </div>
                                <div class="two wide column">
                                    <b>
                                        Fecha de creación del servicio:
                                    </b>
                                    {{ucfirst(strftime('%A %e de %B del %Y', strtotime($activity->created_at)))}}
                                </div>
                                <div class="two wide column">
                                    <b>
                                        Estado del servicio:
                                    </b>
                                    {{isset($activity->state->name) ? ucfirst(mb_strtolower($activity->state->name)) : ''  }}
                                </div>
                                <div class="two wide column">
                                    <b>
                                        Funcionario actual:
                                    </b>
                                        {{ucwords(mb_strtolower($activity->user->full_name))}}
                                </div>
                                <div class="two wide column">
                                    <b>
                                        Valor Liquidación:
                                    </b>
                                    {{$MONEY_TYPE[$activity->parent_activity->policy_sort->type_currency ?? '']['symbol'] ?? ''}}
                                    {{ number_format( $activity->liquidation_sort->settlement_result ?? 0, 2, ',', '.') }}
                                </div>
                                <div class="two wide column">
                                    <b>
                                        Estado de pago:
                                    </b>
                                    {{ $activity->liquidation_sort ? $activity->liquidation_sort->get_payment_status($activity->liquidation_sort->activity_id) : '' }}
                                </div>
                                <div class="two wide column">
                                    <b>
                                        Fecha de pago:
                                    </b>
                                    @if($activity->liquidation_sort && $activity->liquidation_sort->get_payment_date($activity->liquidation_sort->activity_id))
                                        {{ ucfirst(strftime('%A %e de %B del %Y', strtotime($activity->liquidation_sort->get_payment_date($activity->liquidation_sort->activity_id)))) }}
                                    @endif
                                </div>

                                <div class="one wide column">
                                    <a class="ui basic mini blue button"
                                       href="{{secure_url('servicio/' . $activity->id)}}">
                                        <i class="unhide icon"></i> Ver
                                    </a>
                                </div>
                            </div>
                        @endunless
                    @endforeach
                </div>
            </div>
        @endif
        {{--RENOVACION--}}
        @if(Auth::user()->is_view_service(\App\Service::SERVICE_RENEWAL_SORT_MNK) && $renewal->isNotEmpty())
            <div class="title" style="color: {{$renewal->isNotEmpty() ? "#15566c" : ""}}">
                <i class="dropdown icon">
                </i>
                Renovación
            </div>
            <div class="content">
                <div class="ui vertically divided small grid">
                    @foreach($renewal as $activity)
                        @unless($activity->finished_at && !Request::get('archived'))
                            <div class="row">
                                <div class="two wide column">
                                    <b>
                                        ID de Trámite:
                                    </b>
                                    <a class="header" href="{{secure_url('servicio/' . $activity->id)}}">
                                        {{$activity->id}}
                                    </a>
                                </div>
                                <div class="two wide column">
                                    <b>
                                        Tipo de trámite:
                                    </b>
                                    <a class="header" href="{{secure_url('servicio/' . $activity->id)}}">
                                        {{$activity->service->name}}
                                        @if ($activity->oid)
                                            <div style="font-size: 0.75em; color: gray;">
                                                OID: {{$activity->oid}}</div>
                                        @endif
                                    </a>
                                </div>
                                <div class="two wide column">
                                    <b>
                                        Fecha del trámite:
                                    </b>
                                    {{$activity->created_at->formatLocalized('%B %d, %Y')}}
                                </div>
                                <div class="three wide column">
                                    <b>
                                        Estado:
                                    </b>
                                    {{$activity->state->name}}
                                </div>
                                <div class="three wide column">
                                    <b>
                                        Funcionario actual:
                                    </b>
                                    <a>
                                        {{$activity->user->full_name}}
                                    </a>
                                </div>
                                <div class="one wide column">
                                    <a class="ui basic mini blue button"
                                       href="{{secure_url('servicio/' . $activity->id)}}">
                                        <i class="unhide icon"></i> Ver
                                    </a>
                                </div>
                            </div>
                        @endunless
                    @endforeach
                </div>
            </div>
        @endif
        {{--REINTEGRO--}}
        @if(Auth::user()->is_view_service(\App\Service::SERVICE_REINTEGRATE_MNK) && $reintegrate->isNotEmpty())
            <div class="title" style="color: {{$reintegrate->isNotEmpty() ? "#15566c" : ""}}">
                <i class="dropdown icon">
                </i>
                Reintegro
            </div>
            <div class="content">
                <div class="ui vertically divided small grid">
                    @foreach($reintegrate as $activity)
                        @unless($activity->finished_at && !Request::get('archived'))
                            <div class="row">
                                <div class="two wide column">
                                    <b>
                                        # del servicio:
                                    </b>
                                    <a class="header" href="{{secure_url('servicio/' . $activity->id)}}">
                                        {{$activity->id}}
                                    </a>
                                </div>
                                <div class="two wide column">
                                    <b>
                                        Tipo de trámite:
                                    </b>
                                    <a class="header" href="{{secure_url('servicio/' . $activity->id)}}">
                                        {{$activity->service->name}}
                                        @if ($activity->oid)
                                            <div style="font-size: 0.75em; color: gray;">
                                                OID: {{$activity->oid}}</div>
                                        @endif
                                    </a>
                                </div>
                                <div class="one wide column">
                                    <b>
                                        # Trámite:
                                    </b>
                                    {{$activity->reintegrate->id ?? ''}}
                                </div>
                                <div class="one wide column">
                                    <b>
                                        # del aviso:
                                    </b>
                                    {{isset($activity->parent->gis_sort) ? $activity->parent->gis_sort->consecutive_gis : ''}}
                                </div>
                                <div class="one wide column">
                                    <b>
                                        # del caso:
                                    </b>
                                    {{isset($activity->parent->gis_sort) ? $activity->parent->gis_sort->formatCaseNumberIfReported() : ''}}
                                </div>
                                <div class="two wide column">
                                    <b>Fecha de solicitud:</b>
                                    {{ $activity && !empty($activity->created_at)
                                      ? ucfirst(strftime('%A %e de %B del %Y', strtotime($activity->created_at )))
                                      : 'Fecha no disponible'
                                    }}
                                </div>
                                <div class="two wide column">
                                    <b>
                                        Estado del servicio:
                                    </b>
                                    {{ mb_strtoupper(mb_substr($activity->state->name, 0, 1), 'UTF-8') . mb_strtolower(mb_substr($activity->state->name, 1), 'UTF-8') }}
                                </div>
                                <div class="two wide column">
                                    <b>
                                        Funcionario actual:
                                    </b>

                                    {{  ucwords(strtolower($activity->user->full_name)) }}
                                </div>
                                <div class="one wide column">
                                    <a class="ui basic mini blue button"
                                       href="{{secure_url('servicio/' . $activity->id)}}">
                                        <i class="unhide icon"></i> Ver
                                    </a>
                                </div>
                            </div>
                        @endunless
                    @endforeach
                </div>
            </div>
        @endif
        {{--PAGOS ADMINISTRATIVOS--}}
        @if(Auth::user()->is_view_service(\App\Service::SERVICE_ADMINISTRATIVE_PAYMENTS_MNK) && $administrative_payment->isNotEmpty())
            <div class="title" style="color: {{$administrative_payment->isNotEmpty() ? "#15566c" : ""}}">
                <i class="dropdown icon">
                </i>
                Pagos administrativos
            </div>
            <div class="content">
                <div class="ui vertically divided small grid">
                    @foreach($administrative_payment as $activity)
                        @unless($activity->finished_at && !Request::get('archived'))
                            <div class="row">
                                <div class="two wide column">
                                    <b>
                                        # del servicio:
                                    </b>
                                    <a class="header" href="{{secure_url('servicio/' . $activity->id)}}">
                                        {{$activity->id}}
                                    </a>
                                </div>
                                <div class="two wide column">
                                    <b>
                                        Tipo de trámite:
                                    </b>
                                    <a class="header" href="{{secure_url('servicio/' . $activity->id)}}">
                                        {{$activity->service->name}}
                                        @if ($activity->oid)
                                            <div style="font-size: 0.75em; color: gray;">
                                                OID: {{$activity->oid}}</div>
                                        @endif
                                    </a>
                                </div>
                                <div class="two wide column">
                                    <b>
                                        # Trámite:
                                    </b>
                                    {{$activity->administrative_payment->id ?? ''}}
                                </div>

                                <div class="three wide column">
                                    <b>Fecha de solicitud:</b>
                                    {{ $activity && !empty($activity->created_at)
                                      ? ucfirst(strftime('%A %e de %B del %Y', strtotime($activity->created_at )))
                                      : 'Fecha no disponible'
                                    }}
                                </div>
                                <div class="two wide column">
                                    <b>
                                        Estado del servicio:
                                    </b>
                                    {{ mb_strtoupper(mb_substr($activity->state->name, 0, 1), 'UTF-8') . mb_strtolower(mb_substr($activity->state->name, 1), 'UTF-8') }}
                                </div>
                                <div class="two wide column">
                                    <b>
                                        Funcionario actual:
                                    </b>

                                    {{  ucwords(strtolower($activity->user->full_name)) }}
                                </div>
                                <div class="one wide column">
                                    <a class="ui basic mini blue button"
                                       href="{{secure_url('servicio/' . $activity->id)}}">
                                        <i class="unhide icon"></i> Ver
                                    </a>
                                </div>
                            </div>
                        @endunless
                    @endforeach
                </div>
            </div>
        @endif

</div>
    {{-- Crear nuevo tramite --}}
    @unless (count(Auth::user()->colpensiones_services()) == 0)
        <div class="ui divider"></div>
        <form action="{{secure_url('/service/create')}}" method="post"
              class="ui secondary segment form" id="new-procedure-form">

            <div class="ui header">Nuevo Trámite</div>
            <input type="hidden" name="affiliate_id" value="{{optional($affiliate)->id}}">
            {{csrf_field()}}
            <div class="inline four fields">
                <div class="field">
                    <div class="ui fluid selection dropdown">
                        <input name="service_group" id="service_group" type="hidden">
                        <i class="dropdown icon"></i>
                        <div class="default text">Seleccione uno</div>
                        <div class="upward menu">
                            @foreach(Auth::user()->colpensiones_services() as $k => $v)
                                <div class="item"
                                     data-value="{{$k}}">{{mb_strtoupper(mb_substr($v['NAME'], 0, 1, 'UTF-8'), 'UTF-8') . mb_strtolower(mb_substr($v['NAME'], 1, null, 'UTF-8'), 'UTF-8')}}</div>
                            @endforeach
                        </div>
                    </div>
                </div>
                <div class="field">
                    <div class="ui fluid selection dropdown upward">
                        <input name="service_id" id="service_id" type="hidden">
                        <i class="dropdown icon"></i>
                        <div class="default text">Seleccione un servicio general primero</div>
                        <div class="upward menu">
                        </div>
                    </div>
                </div>

                <div class="field service-group-container">
                    @if(isset($medical_services_sort_select) && $medical_services_sort_select->isEmpty())
                        <div class="ui message warning visible block" id="medical-service-dropdown">
                            <p>No cuenta con prestaciones médicas en estado Valoración realizada.</p>
                        </div>
                        <input name="medical_service" id="medical_service" type="hidden">
                    @else
                        <div class="ui fluid selection dropdown medical-service-dropdown disabled" id="medical-service-dropdown">
                            <input name="medical_service" id="medical_service" type="hidden">
                            <i class="dropdown icon"></i>
                            <div class="default text">Seleccione uno</div>
                            <div class="upward menu">
                                @foreach($medical_services_sort_select as $activity)
                                    <div class="item" data-value="{{$activity->id}}">
                                        Prestación médica {{$activity->id}}
                                        <a class="ui basic"
                                           href="{{ secure_url('servicio/'.$activity->id.'/medical_services') }}"
                                           target="_blank">
                                            <i class="unhide icon"></i>
                                        </a>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @endif
                </div>
                <div class="field">
                    <button class="ui secondary button"><i class="add icon"></i>Crear</button>
                </div>
            </div>
        </form>
    @endunless
</div>
@include('affiliate.affiliate_historical', array('affiliate' => $affiliate))

<script src="https://cdn.jsdelivr.net/npm/moment@2.29.1/min/moment.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/moment@2.29.1/locale/es.js"></script>

<script type="text/javascript">

    const MNK_SERVICES = {!! json_encode($MNK_SERVICES) !!};
    const areaId = @json(auth()->user()->area_id);

    $(document).ready(function () {

        const container = $('.service-group-container');
        container.hide();

        $('#new-procedure-form').on('submit', function(e) {

            if ($('#service_group').val() == 7 && ($('#service_id').val() == 84 || $('#service_id').val() == 86)) {

                const medical_service = $('#medical_service').val();
                $('#medical-service-dropdown').next('.error-message').remove();

                if (!medical_service) {

                    $('#medical-service-dropdown').after(
                        `<div class="ui pointing red basic label error-message">Este campo es obligatorio</div>`
                    );
                    e.preventDefault();
                }
            }

        });

        $('.ui.dropdown').dropdown();
        $('.ui.accordion b').css('display', 'block');
        $('.ui.modal').modal();
        $('.ui.modal .content').popup();
        $('.basic.icon.button').popup();
        $('#historicalBTN').on('click', function (e) {
            e.preventDefault();
            if ($("#content_historical").html() === "") {
                $("#content_historical").html("<div>Cargando...</div>");
                $('#historical').modal('show');

                $.ajax({
                    type: 'GET',
                    url: '/logs/get_historial_affiliate/' + {!! optional($affiliate)->id !!},
                    dataType: 'json',
                    contentType: 'application/json',
                    success: function (response) {
                        $('#historical').modal('hide');
                        $('#content_historical').html(response.html);
                        $('#historical').modal('show');
                    },
                    error: function (e) {
                        console.error(e);
                    }
                });
            } else {
                $('#historical').modal('show');
            }
        });
        $('.ui.accordion').accordion({
            exclusive: false
        });
        $('.ui.form').form({
            fields: {service_id: 'empty'}
        });

        $.getJSON('/js/colombia.json', function (colombia) {
            $.each($(".municipality"), function (k, m) {
                for (let i = 0; i < colombia.length; i++) {
                    if (colombia[i].code == $(m).data('department')) {
                        let department = colombia[i].name;

                        for (let j = 0; j < colombia[i].municipalities.length; j++) {
                            if (colombia[i].municipalities[j].code == $(m).data('municipality')) {
                                let municipality = colombia[i].municipalities[j].name;
                                let text = municipality + ' - ' + department;
                                $(m).text(text);
                            }
                        }
                    }
                }
            });
        });
        $('[name=service_group]').change(function () {
            const dtype = $(this).val();

            if (dtype == 7) {
                container.show();
            } else {
                container.hide();
            }

            $("[name=service_id]").parent().dropdown('clear');

            $("[name=service_id]").parent().find('.menu').empty();

            for (let i in MNK_SERVICES[dtype]['OPTIONS']) {
                const details = MNK_SERVICES[dtype]['OPTIONS'][i];
                const formattedDetails = details.charAt(0).toUpperCase() + details.slice(1).toLowerCase(); // Convierte a tipo oración

                if (areaId == 48 && dtype == 7){ //Auditor
                    if (i == 84 || i == 86) {
                        $("[name=service_id]").parent().find('.menu').append(`<div class="item" data-value="${i}">${formattedDetails}</div>`);
                    }
                } else if (areaId == 49 && dtype == 7) { //Analista de indemnizaciones
                    if (i == 89 || i == 91 || i == 93) {
                        $("[name=service_id]").parent().find('.menu').append(`<div class="item" data-value="${i}">${formattedDetails}</div>`);
                    }
                } else {
                    $("[name=service_id]").parent().find('.menu').append(`<div class="item" data-value="${i}">${formattedDetails}</div>`);
                }

            }

            $("[name=service_id]").parent().dropdown();
        });

        $('[name=service_id]').change(function () {
            const value = $(this).val();
            const medicalServiceDropdown = $('.medical-service-dropdown');
            $('[name=medical_service]').val("");
            $('.medical-service-dropdown .text').text("Seleccione uno");

            if (value == 84 || value == 86) {
                medicalServiceDropdown.removeClass('disabled');
            } else {
                medicalServiceDropdown.addClass('disabled');
            }
        });

        $.getJSON('/js/ciuo.json', function (ciuo) {

            $.each($(".ocupation"), function (k, m) {
                for (let i = 0; i < ciuo.length; i++) {
                    if (ciuo[i].COD == $(m).data('code')) {
                        let ocupation = ciuo[i].DESCRIPTION + ' - ' + ciuo[i].COD;
                        $(m).text(ocupation);
                    }
                }
            });
        });

        $.getJSON('/js/new_ciuo.json?v=1', function (ciuo) {

            $.each($(".ocupation"), function (k, m) {
                for (let i = 0; i < ciuo.length; i++) {

                    if (ciuo[i].COD == $(m).data('code')) {
                        let ocupation = '(N) ' + ciuo[i].DESCRIPTION + ' - ' + ciuo[i].COD;
                        $(m).text(ocupation);
                    }
                }
            });
        });

        $.getJSON('/js/ciiu.json', function (ciiu) {

            $.each($(".activity"), function (k, m) {
                for (let i = 0; i < ciiu.length; i++) {
                    if (ciiu[i].COD == $(m).data('code')) {
                        let activity = ciiu[i].DESCRIPTION + ' - ' + ciiu[i].COD;
                        $(m).text(activity);
                    }
                }
            });
        });
    });
</script>

<script>
    document.addEventListener("DOMContentLoaded", function () {
        // Establecer el idioma a español
        moment.locale('es');

        // Seleccionar todos los elementos con clase "actividad"
        const actividades = document.querySelectorAll('.actividad');

        actividades.forEach(function (actividad) {
            // Obtener la fecha desde el atributo data-fecha
            let createdAt = actividad.getAttribute('data-fecha');

            // Formatear la fecha con Moment.js
            let fechaFormateada = moment(createdAt).format('dddd D [de] MMMM [de] YYYY');

            // Capitalizar la primera letra de la fecha formateada
            fechaFormateada = fechaFormateada.charAt(0).toUpperCase() + fechaFormateada.slice(1);

            // Usar querySelector para seleccionar el primer elemento con clase "fecha-creacion"
            actividad.querySelector('.fecha-creacion').innerText = `${fechaFormateada}`;
        });
    });
</script>
<script>
    document.addEventListener("DOMContentLoaded", function () {
        // Establecer el idioma a español
        moment.locale('es');

        // Seleccionar todos los elementos con clase "actividad"
        const items = document.querySelectorAll('.itemFecha');

        items.forEach(function (item) {
            // Obtener la fecha desde el atributo data-fecha
            let createdAt = item.getAttribute('data-fecha');

            // Formatear la fecha con Moment.js
            let fechaFormateada = moment(createdAt).format('dddd D [de] MMMM [de] YYYY');

            // Capitalizar la primera letra de la fecha formateada
            fechaFormateada = fechaFormateada.charAt(0).toUpperCase() + fechaFormateada.slice(1);

            // Usar querySelector para seleccionar el primer elemento con clase "fecha-formatear"
            item.querySelector('.fecha-formatear').innerText = `${fechaFormateada}`;
        });
    });
</script>