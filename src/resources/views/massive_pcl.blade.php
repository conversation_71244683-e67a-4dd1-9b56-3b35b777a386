@extends('layouts.main')

@section('title', 'CARGA MASIVA PCL')

@section('menu')
    @parent
@endsection

@section('content')
    <div class="ui basic segment">
        <h1 class="ui header" style="padding-left: 1rem">Carga Masiva PCL</h1>
        <div class="ui info message" style="width: 97%;height: auto;margin-left: 2rem">
            <div class="header">Excel</div>
            <div id="dashboard">
                ■ Los nombres de las columnas no deben tener espaciados al inicio, fin o intermedio.    ■ Debe usarse una plantilla nueva en cada cargue para evitar registros "Fantasma" en el cargue
            </div>
        </div>
        <div class="ui warning message" style="width: 97%;height: auto;margin-left: 2rem; margin-bottom: 2rem">
            <div class="header">Advertencia</div>
            <div id="dashboard">
                ■ En caso de que exista información cargada, esta se sobrescribirá con la información a cargar.
            </div>
        </div>
        <div class="ui">
            <div class="ui two columns grid">
                <div class="column">
                    <form autocomplete="off" action="{{secure_url('/cargues_pcl/pcl_cases')}}" method="post" enctype="multipart/form-data" class="ui yellow segment small form">
                        <div class="ui dividing header">Cargar ESTRUCTURA CREACIÓN AFILIADO Y SERVICIO (Paso 1)</div>
                        <div class="required field">
                            <label>Archivo Excel [Columnas: TIPO_DE_DOCUMENTO - NUMERO_DE_DOCUMENTO - NOMBRES - APELLIDOS - FECHA_DE_NACIMIENTO - SEXO - TIPO_DE_AFILIADO - ESTADO_CIVIL - NIVEL_ESCOLAR - CODIGO_DEPARTAMENTO - CODIGO_MUNICIPIO - DEPARTAMENTO - MUNICIPIO - DIRECCION - TELEFONO - CELULAR - ES_PENSIONADO - ESTADO_DE_SUPERVIVENCIA - TRASLADO - EPS - REGIONAL - AFP - ARL - FECHA_INICIO_TRAMITE - FUENTE_DE_INFORMACION - RADICADO_BIZAGI - ESTADO_FINAL - ESTADO_FINAL_HOMOLOGADO]</label>
                            <input type="file" name="pcl_file" accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel">
                        </div>
                        <div class="field">
                            <button class="ui blue button"><i class="upload icon"></i> Cargar</button>
                            <a href="https://renapp-colpensiones.s3.us-east-2.amazonaws.com/Templates/PLANTILLA_ESTRUCTURA_CREACION_AF_SER.xlsx">PLANTILLA_ESTRUCTURA_CREACION_AF_SER.xlsx</a>
                        </div>
                        {{csrf_field()}}
                    </form>
                </div>
                <div class="column">
                    <form autocomplete="off" action="{{secure_url('/cargues_pcl/pcl_cases_dictum')}}" method="post" enctype="multipart/form-data" class="ui yellow segment small form">
                        <div class="ui dividing header">Cargar INFORMACIÓN DE BENEFICIARIO (Paso 1.1)</div>
                        <div class="required field">
                            <label>Archivo Excel [Columnas: CORRESPONDE_A_UNA_CALIFICACION_DE_BENEFICIARIO - TIPO_IDENTIFICACION_BENEFICIARIO - NUMERO_DE IDENTIFICACION_BENEFICIARIO - NOMBRES_Y_APELLIDOS_BENEFICIARIO - FECHA_DE_NACIMIENTO_BENEFICIARIO - SEXO_BENEFICIARIO - DIRECCION_BENEFICIARIO - TELEFONO_BENEFICIARIO - CELULAR_BENEFICIARIO - CORREO_ELECTRONICO_BENEFICIARIO - DEPARTAMENTO_BENEFICIARIO - MUNICIPIO_BENEFICIARIO - RADICADO_BIZAGI]</label>
                            <input type="file" name="pcl_file" accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel">
                        </div>
                        <div class="field">
                            <button class="ui blue button"><i class="upload icon"></i> Cargar</button>
                            <a href="https://renapp-colpensiones.s3.us-east-2.amazonaws.com/Templates/PLANTILLA_ESTRUCTURA_BENEFICIARIO.xlsx">PLANTILLA ESTRUCTURA BENEFICIARIO.xlsx</a>
                        </div>
                        {{csrf_field()}}
                    </form>
                </div>
            </div>
            <div class="ui two columns grid">
                <div class="column">
                    <form autocomplete="off" action="{{secure_url('/cargues_pcl/pcl_cases_docs')}}" method="post" enctype="multipart/form-data" class="ui yellow segment small form">
                        <div class="ui dividing header">Cargar CARGUE DE DOCUMENTOS (Paso 2)</div>
                        <div class="required field">
                            <label>Archivo Excel [Columnas: RADICADO_BIZAGI - CODIGO_DOCUMENTAL - URL_DOCUMENTO - FECHA_CARGUE - ID_COD_AB]</label>
                            <input type="file" name="pcl_file" accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel">
                        </div>
                        <div class="field">
                            <button class="ui blue button"><i class="upload icon"></i> Cargar</button>
                            <a href="https://renapp-colpensiones.s3.us-east-2.amazonaws.com/Templates/PLANTILLA_ESTRUCTURA_DOCUMENTOS.xlsx">PLANTILLA_ESTRUCTURA_DOCUMENTOS.xlsx</a></div>
                        {{csrf_field()}}
                    </form>
                </div>
                <div class="column">
                    <form autocomplete="off" action="{{secure_url('/cargues_pcl/pcl_cases_action_history')}}" method="post" enctype="multipart/form-data" class="ui yellow segment small form">
                        <div class="ui dividing header">Cargar HISTORIAL ACCIONES (Paso 3)</div>
                        <div class="required field">
                            <label>Archivo Excel [Columnas: RADICADO_BIZAGI - FECHA_ACCION_AUDIBOL - DESCRIPCION]</label>
                            <input type="file" name="pcl_file" accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel">
                        </div>
                        <div class="field">
                            <button class="ui blue button"><i class="upload icon"></i> Cargar</button>
                            <a href="https://renapp-colpensiones.s3.us-east-2.amazonaws.com/Templates/PLANTILLA_ESTRUCTURA_ACCIONES.xlsx">PLANTILLA_ESTRUCTURA_ACCIONES.xlsx</a></div>
                        {{csrf_field()}}
                    </form>
                </div>
            </div>
            <div class="ui two columns grid">
                <div class="column">
                    <form autocomplete="off" action="{{secure_url('/cargues_pcl/pcl_cases_dictum_info')}}" method="post" enctype="multipart/form-data" class="ui yellow segment small form">
                        <div class="ui dividing header">Cargar ESTRUCTURA DE DICTAMEN GENERAL (Paso 4)</div>
                        <div class="required field">
                            <label>Archivo Excel [Columnas: RADICADO_BIZAGI - CORRESPONDE_A_UNA_CALIFICACION_DE_BENEFICIARIO - TIPO_PCL - SOLICITANTE - NIT_COLPENSIONES - DIRECCION_COLPENSIONES - CIUDAD - DESCRIPCION_ENFERMEDAD_ACTUAL - DOMINANCIA - REQUIERE_TERCERAS_PERSONAS - REQUIERE_TERCERAS_PERSONAS_DECIDIR - REQUIERE_DISPOSITIVO_APOYO - CATASTROFICA_ALTO_COSTO_RUINOSA - ENFERMEDAD_DEGENERATIVA_PROGRESIVA_CRONICA - ENFERMEDAD_CONGENITA_CERCANA_NACIMIENTO - PATOLOGICOS - QUIRURGICOS - FARMACOLOGICOS - HOSPITALARIOS - TOXICOS_ALERGICOS - TRASFUSIONES - FAMILIARES - HISTORIA_CLINICA_COMPLETA - EPICRISIS - PARACLINICOS - TUTELAS - CONCEPTO_DE_REHABILITACION - OTROS - SUMA_COMBINADA - TOTAL_DEFICIENCIA - LABORALMENTE_ACTIVO - RESTRICCIONES_DEL_ROL - AUTOSUFICIENCIA_ECONOMICA - RESTRICCIONES_EDAD_CRONOLOGICA - SUSTENTACION_ROL_LABORAL - SUSTENTACION_ROL_OCUPACIONAL_OTRAS_AREAS - DEFICIENCIA - ROL_LABORAL - PCL - FECHA_DE_ESTRUCTURACION - ORIGEN - TIPO_DE_EVENTO - FECHA_DE_EVENTO - PCL_PCO - SUSTENTACION_FECHA_ESTRUCTURACION]</label>
                            <input type="file" name="pcl_file" accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel">
                        </div>
                        <div class="field">
                            <button class="ui blue button"><i class="upload icon"></i> Cargar</button>
                            <a href="https://renapp-colpensiones.s3.us-east-2.amazonaws.com/Templates/PLANTILLA_ESTRUCTURA_DICTAMEN_GENERAL.xlsx">PLANTILLA_ESTRUCTURA_DICTAMEN_GENERAL.xlsx</a></div>
                        {{csrf_field()}}
                    </form>
                </div>
                <div class="column">
                    <form autocomplete="off" action="{{secure_url('/cargues_pcl/pcl_cases_dictum_def')}}" method="post" enctype="multipart/form-data" class="ui yellow segment small form">
                        <div class="ui dividing header">Cargar DEFICIENCIAS (Paso 5)</div>
                        <div class="required field">
                            <label>Archivo Excel [Columnas: RADICADO_BIZAGI - NOMBRE_1 - NOMBRE_2 - NOMBRE_3 - NOMBRE_4 - NOMBRE_5 - NOMBRE_6 - NOMBRE_7 - NOMBRE_8 - NOMBRE_9 - NOMBRE_10 - NOMBRE_11 - NOMBRE_12 - NOMBRE_13 - TABLA_1 - TABLA_2 - TABLA_3 - TABLA_4 - TABLA_5 - TABLA_6 - TABLA_7 - TABLA_8 - TABLA_9 - TABLA_10 - TABLA_11 - TABLA_12 - TABLA_13 - DEF_1 - DEF_2 - DEF_3 - DEF_4 - DEF_5 - DEF_6 - DEF_7 - DEF_8 - DEF_9 - DEF_10 - DEF_11 - DEF_12 - DEF_13 - CLASE_DEF_1 - CFPFU_DEF_1 - CFM1_DEF_1 - CFM2_DEF_1 - CFM3_DEF_1 - CLASE_FINAL_DEF_1 - CAT_DEF_1 - CLASE_DEF_2 - CFPFU_DEF_2 - CFM1_DEF_2 - CFM2_DEF_2 - CFM3_DEF_2 - CLASE_FINAL_DEF_2 - CAT_DEF_2 - CLASE_DEF_3 - CFPFU_DEF_3 - CFM1_DEF_3 - CFM2_DEF_3 - CFM3_DEF_3 - CLASE_FINAL_DEF_3 - CAT_DEF_3 - CLASE_DEF_4 - CFPFU_DEF_4 - CFM1_DEF_4 - CFM2_DEF_4 - CFM3_DEF_4 - CLASE_FINAL_DEF_4 - CAT_DEF_4 - CLASE_DEF_5 - CFPFU_DEF_5 - CFM1_DEF_5 - CFM2_DEF_5 - CFM3_DEF_5 - CLASE_FINAL_DEF_5 - CAT_DEF_5 - CLASE_DEF_6 - CFPFU_DEF_6 - CFM1_DEF_6 - CFM2_DEF_6 - CFM3_DEF_6 - CLASE_FINAL_DEF_6 - CAT_DEF_6 - CLASE_DEF_7 - CFPFU_DEF_7 - CFM1_DEF_7 - CFM2_DEF_7 - CFM3_DEF_7 - CLASE_FINAL_DEF_7 - CAT_DEF_7 - CLASE_DEF_8 - CFPFU_DEF_8 - CFM1_DEF_8 - CFM2_DEF_8 - CFM3_DEF_8 - CLASE_FINAL_DEF_8 - CAT_DEF_8 - CLASE_DEF_9 - CFPFU_DEF_9 - CFM1_DEF_9 - CFM2_DEF_9 - CFM3_DEF_9 - CLASE_FINAL_DEF_9 - CAT_DEF_9 - CLASE_DEF_10 - CFPFU_DEF_10 - CFM1_DEF_10 - CFM2_DEF_10 - CFM3_DEF_10 - CLASE_FINAL_DEF_10 - CAT_DEF_10 - CLASE_DEF_11 - CFPFU_DEF_11 - CFM1_DEF_11 - CFM2_DEF_11 - CFM3_DEF_11 - CLASE_FINAL_DEF_11 - CAT_DEF_11 - CLASE_DEF_12 - CFPFU_DEF_12 - CFM1_DEF_12 - CFM2_DEF_12 - CFM3_DEF_12 - CLASE_FINAL_DEF_12 - CAT_DEF_12 - CLASE_DEF_13 - CFPFU_DEF_13 - CFM1_DEF_13 - CFM2_DEF_13 - CFM3_DEF_13 - CLASE_FINAL_DEF_13 - CAT_DEF_13 - CLASE_DEF_14 - CFPFU_DEF_14 - CFM1_DEF_14 - CFM2_DEF_14 - CFM3_DEF_14 - CLASE_FINAL_DEF_14 - CAT_DEF_14 - CLASE_DEF_15 - CFPFU_DEF_15 - CFM1_DEF_15 - CFM2_DEF_15 - CFM3_DEF_15 - CLASE_FINAL_DEF_15 - CAT_DEF_15]</label>
                            <input type="file" name="pcl_file" accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel">
                        </div>
                        <div class="field">
                            <button class="ui blue button"><i class="upload icon"></i> Cargar</button>
                            <a href="https://renapp-colpensiones.s3.us-east-2.amazonaws.com/Templates/PLANTILLA_ESTRUCTURA_DEFICIENCIAS.xlsx">PLANTILLA_ESTRUCTURA_DEFICIENCIAS.xlsx</a></div>
                        {{csrf_field()}}
                    </form>
                </div>
            </div>
            <div class="ui two columns grid">
                <div class="column">
                    <form autocomplete="off" action="{{secure_url('/cargues_pcl/pcl_cases_dictum_dx')}}" method="post" enctype="multipart/form-data" class="ui yellow segment small form">
                        <div class="ui dividing header">Cargar DIAGNÓSTICOS (Paso 6)</div>
                        <div class="required field">
                            <label>Archivo Excel [Columnas: RADICADO_BIZAGI - DIAGNOSTICO_1 - DIAGNOSTICO_2 - DIAGNOSTICO_3 - DIAGNOSTICO_4 - DIAGNOSTICO_5 - DIAGNOSTICO_6 - DIAGNOSTICO_7 - DIAGNOSTICO_8 - DIAGNOSTICO_9 - DIAGNOSTICO_10 - DIAGNOSTICO_11 - DIAGNOSTICO_12 - NOMBRE_1 - NOMBRE_2 - NOMBRE_3 - NOMBRE_4 - NOMBRE_5 - NOMBRE_6 - NOMBRE_7 - NOMBRE_8 - NOMBRE_9 - NOMBRE_10 - NOMBRE_11 - NOMBRE_12 - ORIGEN_1 - ORIGEN_2 - ORIGEN_3 - ORIGEN_4 - ORIGEN_5 - ORIGEN_6 - ORIGEN_7 - ORIGEN_8 - ORIGEN_9 - ORIGEN_10 - ORIGEN_11 - ORIGEN_12 - DEFICIENCIA_1 - DEFICIENCIA_2 - DEFICIENCIA_3 - DEFICIENCIA_4 - DEFICIENCIA_5 - DEFICIENCIA_6 - DEFICIENCIA_7 - DEFICIENCIA_8 - DEFICIENCIA_9 - DEFICIENCIA_10 - DEFICIENCIA_11 - DEFICIENCIA_12]</label>
                            <input type="file" name="pcl_file" accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel">
                        </div>
                        <div class="field">
                            <button class="ui blue button"><i class="upload icon"></i> Cargar</button>
                            <a href="https://renapp-colpensiones.s3.us-east-2.amazonaws.com/Templates/PLANTILLA_ESTRUCTURA_DIAGNOSTICOS.xlsx">PLANTILLA_ESTRUCTURA_DIAGNOSTICOS.xlsx</a></div>
                        {{csrf_field()}}
                    </form>
                </div>
                <div class="column">
                    <form autocomplete="off" action="{{secure_url('/cargues_pcl/pcl_cases_dictum_interconsult')}}" method="post" enctype="multipart/form-data" class="ui yellow segment small form">
                        <div class="ui dividing header">Cargar INTERCONSULTA (Paso 7)</div>
                        <div class="required field">
                            <label>Archivo Excel [Columnas: RADICADO_BIZAGI - PROFESIONAL - FECHA - ESPECIALIDAD - RESULTADO - OBSERVACIONES]</label>
                            <input type="file" name="pcl_file" accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel">
                        </div>
                        <div class="field">
                            <button class="ui blue button"><i class="upload icon"></i> Cargar</button>
                            <a href="https://renapp-colpensiones.s3.us-east-2.amazonaws.com/Templates/PLANTILLA_ESTRUCTURA_INTERCONSULTAS.xlsx">PLANTILLA_ESTRUCTURA_INTERCONSULTAS.xlsx</a></div>
                        {{csrf_field()}}
                    </form>
                </div>
            </div>
            <div class="ui two columns grid">
                <div class="column">
                    <form autocomplete="off" action="{{secure_url('/cargues_pcl/pcl_cases_table_datas')}}" method="post" enctype="multipart/form-data" class="ui yellow segment small form">
                        <div class="ui dividing header">Cargar TABLA DE DATOS ROL LABORAL / OCUPACIONAL (Paso 8) 1507</div>
                        <div class="required field">
                            <label>Archivo Excel [Columnas: RADICADO_BIZAGI - ID_SUBTIPO_AREA_OCU - VALOR_AREA_OCU]</label>
                            <input type="file" name="pcl_file" accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel">
                        </div>
                        <div class="field">
                            <button class="ui blue button"><i class="upload icon"></i> Cargar</button>
                            <a href="https://renapp-colpensiones.s3.us-east-2.amazonaws.com/Templates/PLANTILLA_ESTRUCTURA_DICTAMEN_AREA_OCUPACIONAL.xlsx">PLANTILLA_ESTRUCTURA_DICTAMEN_AREA_OCUPACIONAL.xlsx</a></div>
                        {{csrf_field()}}
                    </form>
                </div>
                <div class="column">
                    <form autocomplete="off" action="{{secure_url('/cargues_pcl/pcl_cases_update_state')}}" method="post" enctype="multipart/form-data" class="ui yellow segment small form">
                        <div class="ui dividing header">ACTUALIZAR ESTADOS PCL (Opcional)</div>
                        <div class="required field">
                            <label>Archivo Excel [Columnas: RADICADO_BIZAGI - STATE_ID]</label>
                            <div style="padding-top: 1rem;padding-bottom: 1rem;">
                                EXCEL/* <span style="color: gray;">[<b>En la columna STATE_ID se debe poner el id del estado en lugar del nombre para homologarlo.</b>]</span>
                            </div>
                            <input type="file" name="pcl_file" accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel">
                        </div>
                        <div class="field">
                            <button class="ui blue button"><i class="upload icon"></i> Cargar</button>
                            <a href="https://renapp-colpensiones.s3.us-east-2.amazonaws.com/Templates/PLANTILLA_ESTRUCTURA_ACTUALIZACION_ESTADO.xlsx">PLANTILLA_ESTRUCTURA_ACTUALIZACION_ESTADO.xlsx</a></div>
                        {{csrf_field()}}
                    </form>
                </div>
            </div>
            <div class="ui two columns grid">
                <div class="column">
                    <form autocomplete="off" action="{{secure_url('/cargues_pcl/pcl_cases_backup')}}" method="post" enctype="multipart/form-data" class="ui yellow segment small form">
                        <div class="ui dividing header">Cargar BackUp (Opcional)</div>
                        <div class="required field">
                            <label>Archivo Excel [Columnas: RADICADO_BIZAGI - TIPO_VALORACION - FECHA_VALORACION - HORA_VALORACION - CANAL_DE_CONSULTA - TIPO_DOCUMENTO_MEDICO_EVALUADOR - NUMERO_DOCUMENTO_MEDICO_EVALUADOR - RETHUS_MEDICO_EVALUADOR - NOMBRE_COMPLETO_MEDICO_EVALUADOR - NUMERO_REGISTRO_MEDICO_MEDICO_EVALUADOR - ESPECIALIDAD_MEDICO_EVALUADOR - LICENCIA_MEDICO_EVALUADOR - ANAMNESIS_DESCRIPCION]</label>
                            <input type="file" name="pcl_file" accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel">
                        </div>
                        <div class="field">
                            <button class="ui blue button"><i class="upload icon"></i> Cargar</button>
                        </div>
                        {{csrf_field()}}
                    </form>
                </div>
                <div class="column">
                    <form autocomplete="off" action="{{secure_url('/cargues_pcl/pcl_cases_historical_closed')}}" method="post" enctype="multipart/form-data" class="ui yellow segment small form">
                        <div class="ui dividing header">Cargar historicos cerrados (Opcional)</div>
                        <div class="required field">
                            <label>Archivo Excel [Columnas: RADICADO_BIZAGI - NRO_DICTAMEN - FECHA_DICTAMEN - CALIFICACION_PCL - FECHA_ESTRUCTURACION - ORIGEN_PCL]</label>
                            <input type="file" name="pcl_file" accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel">
                        </div>
                        <div class="field">
                            <button class="ui blue button"><i class="upload icon"></i> Cargar</button>
                            <a href="https://renapp-colpensiones.s3.us-east-2.amazonaws.com/Templates/PLANTILLA_ESTRUCTURA_HISTORICO_CERRADOS.xlsx">PLANTILLA_ESTRUCTURA_HISTORICO_CERRADOS.xlsx</a></div>
                        {{csrf_field()}}
                    </form>
                </div>
            </div>
            <div class="ui two columns grid">
                <div class="column">
                    <form autocomplete="off" action="{{secure_url('/cargues_pcl/pcl_cases_table_disability')}}" method="post" enctype="multipart/form-data" class="ui yellow segment small form">
                        <div class="ui dividing header">Cargar Discapacidades (Paso 9) 917</div>
                        <div class="required field">
                            <label>Archivo Excel [Columnas: RADICADO_BIZAGI - ID_SUBTIPO_DISCAPACIDAD - VALOR_DISCAPACIDAD]</label>
                            <input type="file" name="pcl_file" accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel">
                        </div>
                        <div class="field">
                            <button class="ui blue button"><i class="upload icon"></i> Cargar</button>
                            <a href="https://renapp-colpensiones.s3.us-east-2.amazonaws.com/Templates/PLANTILLA_ESTRUCTURA_DICTAMEN_DISCAPACIDADES.xlsx">PLANTILLA_ESTRUCTURA_DICTAMEN_DISCAPACIDADES.xlsx</a>
                        </div>
                        {{csrf_field()}}
                    </form>
                </div>
                <div class="column">
                    <form autocomplete="off" action="{{secure_url('/cargues_pcl/pcl_cases_table_minus')}}" method="post" enctype="multipart/form-data" class="ui yellow segment small form">
                        <div class="ui dividing header">Cargar Minusvalias (Paso 10) 917</div>
                        <div class="required field">
                            <label>Archivo Excel [Columnas: RADICADO_BIZAGI - TABLA - VALOR_MINUS - DETALLE_MINUSVALIA]</label>

                            <div style="padding-top: 1rem;padding-bottom: 1rem;">
                                EXCEL/*  <span style="color: gray;">[<b>
                                        '1' = [10 = 0, 11 = 0.5, 12 = 1, 13 = 1.5, 14 = 2, 15 = 2.5] <br>
            '2' = [20 = 0, 21 = 0.5, 22 = 1, 23 = 1.5, 24 = 2, 25 = 2.5] <br>
            '3' = [30 = 0, 31 = 0.5, 32 = 1, 33 = 1.5, 34 = 2, 35 = 2.5] <br>
            '4' = [40 = 0, 41 = 2.5, 42 = 5, 43 = 7.5, 44 = 10, 45 = 12.5, 46 = 15] <br>
            '5' = [50 = 0, 51 = 0.5, 52 = 1, 53 = 1.5, 54 = 2, 55 = 2.5] <br>
            '6' = [60 = 0, 61 = 0.5, 62 = 1, 63 = 1.5, 64 = 2, 65 = 2.5] <br>
            '7' = [71 = 2.5, 72 = 1.25, 73 = 1.7, 74 = 2, 75 = 2.25, 76 = 2.5]
                                    </b>]</span>
                            </div>

                            <input type="file" name="pcl_file" accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel">
                        </div>
                        <div class="field">
                            <button class="ui blue button"><i class="upload icon"></i> Cargar</button>
                            <a href="https://renapp-colpensiones.s3.us-east-2.amazonaws.com/Templates/PLANTILLA_ESTRUCTURA_DICTAMEN_MINUSVALIAS.xlsx">PLANTILLA_ESTRUCTURA_DICTAMEN_MINUSVALIAS.xlsx</a>
                        {{csrf_field()}}
                    </form>
                </div>
            </div>
        </div>

    </div>
    <script type="text/javascript">
        var makeUIDF = function(length) {
            var text = "";
            var possible = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";

            for (var i = 0; i < length; i++)
                text += possible.charAt(Math.floor(Math.random() * possible.length));

            return text + new Date().getTime();
        };
        var onUploadF = function(err, data) {
            var current = new Date().getTime();
            $(this).parent().find('button').removeClass('blue');
            if (err) {
                $(this).parent().find('button').addClass('red');
                $(this).parent().find('button').html('<i class="warning icon"></i> ERROR');
                return alert('There was an error uploading your photo: ', err.message);
            }

            $(this).parent().find('button').addClass('green');
            $(this).parent().find('button').html('<i class="checkmark icon"></i> OK');
            $(this).parent().find('p').html('Completado en ' + Math.round((current - initF) / 1000) + ' seg.');
            $(this).parent().find('input[type=hidden]').val(data.Key);
        };
        var onProgressF = function(progress) {
            var current = new Date().getTime();
            var speed = Math.floor((progress.loaded) / (current - initF));
            var remaing = Math.round((progress.total - progress.loaded) / (speed * 1024));
            var perc = Math.floor(progress.loaded / progress.total * 1000) / 10;
            $(this).parent().find('button').html('<i class="wait icon"></i> ' + perc + ' %');
            if (remaing <= 120){
                $(this).parent().find('p').html('Faltan: &sim;' + remaing + ' seg.<br /> (vel. &sim;'+ speed +' KB/s)');
            } else {
                $(this).parent().find('p').html('Faltan: &sim;' + Math.round(remaing / 60) + ' min.<br /> (vel. &sim;'+ speed +' KB/s)');
            }
        };
        $(document).ready(function() {

            $('#notificationsfilebtn').click(function () {
                $('#notificationsfile').click();
            });

            $('#notificationsfilebtn2').click(function () {
                $('#notificationsfile2').click();
            });
            $('#notificationsfilebtn4').click(function () {
                $('#notificationsfile4').click();
            });

            $('#notificationsfilebtnx').click(function () {
                $('#notificationsfilex').click();
            });

            $('#notificationsfilebtnxresponses').click(function () {
                $('#notificationsfilexresponses').click();
            });

            $('#notificationsfilebtnz').click(function () {
                $('#notificationsfilez').click();
            });

            $('#notificationsfile, #notificationsfile2, #notificationsfile4, #notificationsfilex, #notificationsfilexresponses, #notificationsfilez').change(function() {
                $(this).parent().find('button').removeClass('green');
                $(this).parent().find('button').removeClass('red');
                $(this).parent().find('button').addClass('blue');
                var files = $(this).prop("files");
                if (!files.length) {
                    return alert('Please choose a file to upload first.');
                }
                var file = files[0];
                var fileName = file.name;
                var fileNameExt = file.name.split('.').pop();
                var fileNameHash = makeUIDF(27);
                var albumPhotosKey = encodeURIComponent('notifications_zip') + '/';

                var photoKey = albumPhotosKey + fileNameHash + '.' + fileNameExt;
                var onUploadB = onUploadF.bind(this);
                var onProgressB = onProgressF.bind(this);

                initF = new Date().getTime();
                s3.upload({
                    Key: photoKey,
                    Body: file,
                }, {partSize: 20 * 1024 * 1024, queueSize: 1}, onUploadB).on('httpUploadProgress', onProgressB);
            });

        });
    </script>
@endsection
