@extends('layouts.main')

@section('title', 'TUTORIALES')

@section('menu')
    @parent
@endsection

@section('content')
    <div class="ui basic segment container">
        <div class="ui styled fluid accordion">
            <div class="title">
                <i class="dropdown icon"></i>
                Preguntas frecuentes
            </div>
            <div class="content">
            </div>
            <div class="title">
                <i class="dropdown icon"></i>
                Tutoriales
            </div>
            <div class="content ">
                <div class="ui cards">
                    @foreach($videos as $video)
                        <div class="ui card">
                            <div class="content">
                                <div class="header" style="padding-top: 1rem">{{$video->description}}</div>
                                <div class="ui divider"></div>
                                <div class="description">
                                    <iframe width="100%" height="315"
                                            src="{{$video->url}}"
                                            title="YouTube video player" frameborder="0"
                                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                                            referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>
    <script>
        $(document).ready(function () {
            $('.accordion').accordion({
                exclusive: false,
                collapsible: true
            });
            $('.accordion .content iframe').on('click', function (event) {
                event.stopPropagation();
            });
        });
    </script>
    <style>
        .ui.cards {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            justify-content: flex-start;
        }

        .ui.card {
            width: 330px !important;
        }
    </style>
@endsection