@extends('layouts.main')

@section('title', 'ACTIVIDAD')

@section('menu')
    @parent
@endsection

@section('content')
    <div class="ui basic segment">
        <div class="ui styled fluid accordion">
            <!-- AFILIADO -->
            <div class="active title">
                <i class="dropdown icon">
                </i>
                Información del afiliado
            </div>
            <div class="active content">
                <div class="ui five columns grid">
                    <div class="column">
                        <b>
                            Documento:
                        </b>
                        {{ $activity->affiliate->doc_type }} {{ $activity->affiliate->doc_number }}
                    </div>
                    <div class="column">
                        <b>
                            Nombres y apellidos:
                        </b>
                        <a href="{{secure_url('/afiliado', ['id' => $activity->affiliate->id])}}">
                            {{ mb_convert_case(mb_strtolower( $activity->affiliate->full_name), MB_CASE_TITLE, "UTF-8")  }}
                        </a>
                    </div>
                    @if($activity->affiliate->eps)
                        <div class="column">
                            <b>
                                EPS:
                            </b>
                            {{ $EPS_LIST[$activity->affiliate->eps] }}
                        </div>
                    @endif
                    @if($activity->affiliate->afp)
                        <div class="column">
                            <b>
                                AFP:
                            </b>
                            {{ $AFP_LIST[$activity->affiliate->afp] }}
                        </div>
                    @endif
                    @if($activity->affiliate->arl)
                        <div class="column">
                            <b>
                                ARL:
                            </b>
                            {{ $ARL_LIST[$activity->affiliate->arl] }}
                        </div>
                    @endif
                </div>
                <div class="ui one columns grid">
                    <div class="column">
                        @if($activity->employment)
                            @if($activity->affiliate->arl && $activity->employment->employer)
                                @if($activity->employment->employer->arl)
                                    @if($activity->affiliate->arl != $activity->employment->employer->arl)
                                        <div class="ui floating message yellow">
                                            <p>Las ARLs del afiliado ({{ $ARL_LIST[$activity->affiliate->arl] }}) y la
                                                empresa en vinculacion
                                                laboral {{$activity->employment->getEmployerName()}}
                                                entre {{$activity->employment->period}}
                                                ({{ $ARL_LIST[$activity->employment->employer->arl] }}) son
                                                diferentes</p>
                                        </div>
                                    @endif
                                @endif
                            @endif
                        @endif
                    </div>
                </div>
            </div>
            @if(in_array($activity->service_id,[\App\Service::SERVICE_INVALIDITY_STATE_P4_COLPENSIONES,\App\Service::SERVICE_PCL_COLPENSIONES]))
                <!-- BENEFICIARIO -->
                <div class="active title">
                    <i class="dropdown icon">
                    </i>
                    Información del Beneficiario
                </div>
                <div class="active content">

                    <div class="ui five columns grid">
                        @php
                            $service_model = null;
                            if ($activity->service_id == \App\Service::SERVICE_PCL_COLPENSIONES) {
                                $service_model = \App\Pcl::query()->where('activity_id',$activity->id)->first();
                            } else if ($activity->service_id == \App\Service::SERVICE_INVALIDITY_STATE_P4_COLPENSIONES) {
                                $service_model = \App\InvalidityStatePfour::query()->where('activity_id',$activity->id)->first();
                            }
                        @endphp
                        @if($service_model && $service_model->beneficiaries_qualification == 'SI')
                            <div class="column">
                                <b>
                                    Documento:
                                </b>
                                {{ $service_model->beneficiaries_doc_type }} {{ $service_model->beneficiaries_doc_number }}
                            </div>
                            <div class="column">
                                <b>
                                    Nombres y Apellidos:
                                </b>
                                {{ strtoupper($service_model->beneficiaries_full_name) }}
                            </div>
                        @else
                            <div class="columns" style="margin-left: -10px;  width: 100%; text-align: center">
                                <div class="ui warning message middle wide column sixteen">
                                    <div class="header"> EL CALIFICADO ES EL MISMO AFILIADO</div>
                                    <div id="dashboard">
                                        No existe dato para el campo. Para este caso la persona a calificarse es el
                                        mismo causante.
                                    </div>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            @endif
            <!-- ACTIVIDAD -->
            <div class="active title">
                <i class="dropdown icon">
                </i>
                Información de la actividad
            </div>
            <div class="active content">
                <div class="ui grid">
                    @if(App\Service::checkService($activity->service_id, App\Service::SERVICE_MEDICAMENTOS_MNK)
                    || App\Service::checkService($activity->service_id, App\Service::SERVICE_SUPPLIER_MOT_MNK)
                    || App\Service::checkService($activity->service_id, App\Service::SERVICE_POLICY_SORT_COLLECTION))
                        <div class="eight columns row">
                            @elseif(App\Service::checkService($activity->service_id, App\Service::SERVICE_MEDICAL_SERVICES_SORT_MNK)
                            || App\Service::checkService($activity->service_id, App\Service::SERVICE_MEDICAL_BILLS ) || App\Service::checkService($activity->service_id, App\Service::SERVICE_MEDICAL_SERVICES_SECONDARY_CARE_SORT_MNK))
                                <div class="seven columns row">
                                    @else
                                        <div class="six columns row">
                                            @endif
                                            <div class="column">
                                                <b>
                                                    Actividad:
                                                </b>
                                                <div class="ui left labeled small fluid button" tabindex="0">
                                                    <div style="cursor: default;"
                                                         class="ui basic right pointing fluid label">
                                                        {{$activity->service->name}}
                                                    </div>
                                                    @if(Auth::user()->is_view_general_permission_service($activity->service_id, \App\Permission::INFORMACION_DE_LA_ACTIVIDAD))
                                                        @if (App\Service::checkService($activity->service_id, App\Service::SERVICE_TEMPLATE))
                                                            <a href="{{secure_url("servicio/{$activity->id}/template")}}"
                                                               class="ui secondary icon button">
                                                                <i class="browser icon"></i>
                                                            </a>
                                                        @endif
                                                        @if (App\Service::checkService($activity->service_id, App\Service::SERVICE_QUOTATIONS))
                                                            <a href="{{secure_url("servicio/{$activity->id}/quotation")}}"
                                                               class="ui secondary icon button">
                                                                <i class="browser icon"></i>
                                                            </a>
                                                        @endif
                                                        @if (App\Service::checkService($activity->service_id, App\Service::SERVICE_IT_HISTORICAL_COLPENSIONES))
                                                            <a href="{{secure_url("servicio/{$activity->id}/it_historical")}}"
                                                               class="ui secondary icon button">
                                                                <i class="browser icon"></i>
                                                            </a>
                                                        @endif
                                                        @if (App\Service::checkService($activity->service_id, App\Service::SERVICE_IT_LIQUIDATED_COLPENSIONES))
                                                            <a href="{{secure_url("servicio/{$activity->id}/payment_bases")}}"
                                                               class="ui secondary icon button">
                                                                <i class="browser icon"></i>
                                                            </a>
                                                        @endif
                                                        @if (App\Service::checkService($activity->service_id, App\Service::SERVICE_DETERMINATION_IT))
                                                            <a href="{{secure_url("servicio/{$activity->id}/determination_it")}}"
                                                               class="ui secondary icon button">
                                                                <i class="browser icon"></i>
                                                            </a>
                                                        @endif
                                                        @if (App\Service::checkService($activity->service_id, App\Service::SERVICE_INVALIDITY_STATE_P1))
                                                            <a href="{{secure_url("servicio/{$activity->id}/invalidity_state_pone")}}"
                                                               class="ui secondary icon button">
                                                                <i class="browser icon"></i>
                                                            </a>
                                                        @endif
                                                        @if (App\Service::checkService($activity->service_id, App\Service::SERVICE_INVALIDITY_STATE_P4))
                                                            <a href="{{secure_url("servicio/{$activity->id}/invalidity_state_pfour")}}"
                                                               class="ui secondary icon button">
                                                                <i class="browser icon"></i>
                                                            </a>
                                                        @endif
                                                        @if (App\Service::checkService($activity->service_id, App\Service::SERVICE_MEETING_COLPENSIONES))
                                                            <a href="{{secure_url("servicio/{$activity->id}/tramites")}}"
                                                               class="ui secondary icon button">
                                                                <i class="browser icon"></i>
                                                            </a>
                                                        @endif
                                                        @if (App\Service::checkService($activity->service_id, App\Service::SERVICE_DICTUM_RECEPTION_COLPENSIONES))
                                                            <a href="{{secure_url("servicio/{$activity->id}/dictum_reception")}}"
                                                               class="ui secondary icon button">
                                                                <i class="browser icon"></i>
                                                            </a>
                                                        @endif
                                                        @if (App\Service::checkService($activity->service_id, App\Service::SERVICE_PCL))
                                                            <a href="{{secure_url("servicio/{$activity->id}/pcl")}}"
                                                               class="ui secondary icon button">
                                                                <i class="browser icon"></i>
                                                            </a>
                                                        @endif
                                                        @if (App\Service::checkService($activity->service_id, App\Service::SERVICE_RESPONSE_GESTION_TUTELA))
                                                            <a href="{{secure_url("servicio/{$activity->id}/tutelage")}}"
                                                               class="ui secondary icon button">
                                                                <i class="browser icon"></i>
                                                            </a>
                                                        @endif
                                                        @if (App\Service::checkService($activity->service_id, App\Service::SERVICE_RESPONSE_PQR))
                                                            <a href="{{secure_url("servicio/{$activity->id}/pqr")}}"
                                                               class="ui secondary icon button">
                                                                <i class="browser icon"></i>
                                                            </a>
                                                        @endif
                                                        @if (App\Service::checkService($activity->service_id, App\Service::SERVICE_EXPRESSION_DISAGREEMENT))
                                                            <a href="{{secure_url("servicio/{$activity->id}/expression_disagreement")}}"
                                                               class="ui secondary icon button">
                                                                <i class="browser icon"></i>
                                                            </a>
                                                        @endif
                                                        @if (App\Service::checkService($activity->service_id, App\Service::SERVICE_NOTIFICATION_DICTUM_COLPENSIONES))
                                                            <a href="{{secure_url("servicio/{$activity->id}/dictum_pcl_notification")}}"
                                                               class="ui secondary icon button">
                                                                <i class="browser icon"></i>
                                                            </a>
                                                        @endif
                                                        @if (App\Service::checkService($activity->service_id, App\Service::SERVICE_POLICY_SORT))
                                                            <a href="{{secure_url("servicio/{$activity->id}/policy_sort")}}"
                                                               class="ui secondary icon button">
                                                                <i class="browser icon"></i>
                                                            </a>
                                                        @endif
                                                        @if (App\Service::checkService($activity->service_id, App\Service::SERVICE_POLICY_SORT_COLLECTION))
                                                            <a href="{{secure_url("servicio/{$activity->id}/policy_sort_collection")}}"
                                                               class="ui secondary icon button">
                                                                <i class="browser icon"></i>
                                                            </a>
                                                        @endif
                                                        @if (App\Service::checkService($activity->service_id, App\Service::SERVICE_VARIATIONS_SORT))
                                                            <a href="{{secure_url("servicio/{$activity->id}/variations_sort")}}"
                                                               class="ui secondary icon button">
                                                                <i class="browser icon"></i>
                                                            </a>
                                                        @endif
                                                        @if (App\Service::checkService($activity->service_id, App\Service::SERVICE_REPORT_TAKEN_FORM) && $activity->state_id !== App\States\StateReportePlanillaTomador::PLANILLA_TEMPORAL)
                                                            <a href="{{secure_url("servicio/{$activity->id}/report_taken_form")}}"
                                                               class="ui secondary icon button">
                                                                <i class="browser icon"></i>
                                                            </a>
                                                        @endif
                                                        @if (App\Service::checkService($activity->service_id, App\Service::SERVICE_CONSTANCY_SORT))
                                                            <a href="{{secure_url("servicio/{$activity->id}/constancy_sort")}}"
                                                               class="ui secondary icon button">
                                                                <i class="browser icon"></i>
                                                            </a>
                                                        @endif
                                                        @if (App\Service::checkService($activity->service_id, App\Service::SERVICE_AFFILIATE_WORKFORCE_REPORT))
                                                            <a href="{{secure_url("servicio/{$activity->id}/affiliate_workforce_report")}}"
                                                               class="ui secondary icon button">
                                                                <i class="browser icon"></i>
                                                            </a>
                                                        @endif
                                                        @if (App\Service::checkService($activity->service_id, App\Service::SERVICE_MEDICAL_SERVICES_SORT))
                                                            <a href="{{secure_url("servicio/{$activity->id}/medical_services")}}"
                                                               class="ui secondary icon button">
                                                                <i class="browser icon"></i>
                                                            </a>
                                                        @endif
                                                        @if (App\Service::checkService($activity->service_id, App\Service::SERVICE_MEDICAL_SERVICES_SECONDARY_CARE_SORT))
                                                            <a href="{{secure_url("servicio/{$activity->id}/medical_services_secondary_care")}}"
                                                               class="ui secondary icon button">
                                                                <i class="browser icon"></i>
                                                            </a>
                                                        @endif
                                                        @if (App\Service::checkService($activity->service_id, App\Service::SERVICE_GIS_SORT_MNK_SORT))
                                                            <a href="{{secure_url("servicio/{$activity->id}/gis_sort")}}"
                                                               class="ui secondary icon button">
                                                                <i class="browser icon"></i>
                                                            </a>
                                                        @endif
                                                        @if (App\Service::checkService($activity->service_id, App\Service::SERVICE_PE_IT_SORT))
                                                            <a href="{{secure_url("servicio/{$activity->id}/pe_it_sort")}}"
                                                               class="ui secondary icon button">
                                                                <i class="browser icon"></i>
                                                            </a>
                                                        @endif
                                                        @if (App\Service::checkService($activity->service_id, App\Service::SERVICE_PE_IP_SORT))
                                                            <a href="{{secure_url("servicio/{$activity->id}/pe_ip_sort")}}"
                                                               class="ui secondary icon button">
                                                                <i class="browser icon"></i>
                                                            </a>
                                                        @endif
                                                        @if (App\Service::checkService($activity->service_id, App\Service::SERVICE_PE_MPT_SORT))
                                                            <a href="{{secure_url("servicio/{$activity->id}/pe_mpt_sort")}}"
                                                               class="ui secondary icon button">
                                                                <i class="browser icon"></i>
                                                            </a>
                                                        @endif
                                                        @if (App\Service::checkService($activity->service_id, App\Service::SERVICE_MEDICAMENTOS))
                                                            <a href="{{secure_url("servicio/{$activity->id}/medication_services")}}"
                                                               class="ui secondary icon button">
                                                                <i class="browser icon"></i>
                                                            </a>
                                                        @endif
                                                        @if (App\Service::checkService($activity->service_id, App\Service::SERVICE_MEDICAL_BILLS))
                                                            <a href="{{secure_url("servicio/{$activity->id}/medical_bills")}}"
                                                               class="ui secondary icon button">
                                                                <i class="browser icon"></i>
                                                            </a>
                                                        @endif
                                                        @if (App\Service::checkService($activity->service_id, App\Service::SERVICE_PE_RECOGNITION_EXPENSES))
                                                            <a href="{{secure_url("servicio/{$activity->id}/pe_recognition_expenses")}}"
                                                               class="ui secondary icon button">
                                                                <i class="browser icon"></i>
                                                            </a>
                                                        @endif
                                                        @if (App\Service::checkService($activity->service_id, App\Service::SERVICE_SUPPLIER_MOT))
                                                            <a href="{{secure_url("servicio/{$activity->id}/supplies_mot")}}"
                                                               class="ui secondary icon button">
                                                                <i class="browser icon"></i>
                                                            </a>
                                                        @endif
                                                        @if (App\Service::checkService($activity->service_id, App\Service::SERVICE_AFFILIATE_PAYMENT))
                                                            <a href="{{secure_url("servicio/{$activity->id}/affiliate_payment")}}"
                                                               class="ui secondary icon button">
                                                                <i class="browser icon"></i>
                                                            </a>
                                                        @endif
                                                        @if (App\Service::checkService($activity->service_id, App\Service::SERVICE_LIQUIDATION_SORT))
                                                            <a href="{{secure_url("servicio/{$activity->id}/liquidation")}}"
                                                               class="ui secondary icon button">
                                                                <i class="browser icon"></i>
                                                            </a>
                                                        @endif
                                                        @if (App\Service::checkService($activity->service_id, App\Service::SERVICE_RENEWAL_SORT))
                                                            <a href="{{secure_url("servicio/{$activity->id}/renewal")}}"
                                                               class="ui secondary icon button">
                                                                <i class="browser icon"></i>
                                                            </a>
                                                        @endif
                                                        @if (App\Service::checkService($activity->service_id, App\Service::SERVICE_REINTEGRATE))
                                                            <a href="{{secure_url("servicio/{$activity->id}/reintegrate")}}"
                                                               class="ui secondary icon button">
                                                                <i class="browser icon"></i>
                                                            </a>
                                                        @endif
                                                        @if (App\Service::checkService($activity->service_id, App\Service::SERVICE_ADMINISTRATIVE_PAYMENTS))
                                                            <a href="{{secure_url("servicio/{$activity->id}/administrative_payment")}}"
                                                               class="ui secondary icon button">
                                                                <i class="browser icon"></i>
                                                            </a>
                                                        @endif
                                                        @if (App\Service::checkService($activity->service_id, App\Service::SERVICE_SOCIAL_SECURITY_PROVIDERS))
                                                            <a href="{{secure_url("servicio/{$activity->id}/social_security_providers")}}"
                                                            class="ui secondary icon button">
                                                                <i class="browser icon"></i>
                                                            </a>
                                                        @endif
                                                        @if (App\Service::checkService($activity->service_id, App\Service::SERVICE_SUBROGACION))
                                                            <a href="{{secure_url("servicio/{$activity->id}/subrogacion")}}"
                                                            class="ui secondary icon button">
                                                                <i class="browser icon"></i>
                                                            </a>
                                                        @endif
                                                    @endif
                                                </div>
                                            </div>

                                            @if($activity->service_id == \App\Service::SERVICE_PE_RECOGNITION_EXPENSES_MNK)

                                                <div class="column">
                                                    <b>
                                                        # servicio:
                                                    </b>
                                                    {{$activity->id}}
                                                </div>
                                                <div class="column">
                                                    <b>
                                                        fecha del trámite:
                                                    </b>
                                                    {{ $activity && $activity->created_at
                                                        ? ucfirst(strftime('%A %e de %B del %Y', strtotime($activity->created_at)))
                                                        : 'Fecha no disponible'
                                                    }}
                                                </div>

                                                <div class="column">
                                                    <b>
                                                        Estado del servicio:
                                                    </b>
                                                    {{ mb_strtoupper(mb_substr($activity->state->name, 0, 1), 'UTF-8') . mb_strtolower(mb_substr($activity->state->name, 1), 'UTF-8') }}
                                                </div>

                                                <div class="column">
                                                    <b>
                                                        Funcionario actual:
                                                    </b>
                                                    {{ mb_convert_case($activity->user->full_name, MB_CASE_TITLE, "UTF-8")}}
                                                </div>

                                                <div class="column">
                                                    <b>
                                                        Valor aceptado para reconocimiento de gastos:
                                                    </b>
                                                    {{$MONEY_TYPE[$activity->parent_activity->gis_sort->activity->parent_activity->policy_sort->type_currency ?? '']['symbol'] ?? ''}}
                                                    {{number_format($activity->pe_recognition_expenses->accepted_value_of_recognition ?? 0, 2, ',', '.') }}
                                                </div>
                                            @endif

                                            @if (App\Service::checkService($activity->service_id, App\Service::SERVICE_REINTEGRATE))
                                                <div class="column">
                                                    <b>
                                                        Nombre:
                                                    </b>
                                                    {{$activity->parent_activity?ucwords(strtolower($activity->parent_activity->affiliate->full_name)):''}}
                                                </div>
                                                <div class="column">
                                                    <b>
                                                        Póliza SORT:
                                                    </b>
                                                    {{isset($activity->parent->parent->policy_sort) ? $activity->parent->parent->policy_sort->formatNumberConsecutive() : ''}}
                                                </div>
                                                <div class="column">
                                                    <b>
                                                        # Trámite:
                                                    </b>

                                                    {{$activity->reintegrate->id ?? '' }}
                                                </div>
                                                <div class="column">
                                                    <b>
                                                        # del aviso:
                                                    </b>
                                                    {{isset($activity->parent->gis_sort) ? $activity->parent->gis_sort->consecutive_gis : ''}}
                                                </div>
                                                <div class="column">
                                                    <b>
                                                        # del caso:
                                                    </b>
                                                    {{isset($activity->parent->gis_sort) ? $activity->parent->gis_sort->formatCaseNumberIfReported() : ''}}
                                                </div>

                                                <div class="column">
                                                    <b>
                                                        Estado:
                                                    </b>
                                                    {{ucfirst(mb_strtolower($activity->state->name)) }}
                                                </div>

                                            @endif

                                            @if (App\Service::checkService($activity->service_id, App\Service::SERVICE_ADMINISTRATIVE_PAYMENTS))
                                                <div class="column">
                                                    <b>
                                                        # Trámite:
                                                    </b>

                                                    {{$activity->administrative_payment->id ?? '' }}
                                                </div>

                                                <div class="column">
                                                    <b>
                                                        Estado:
                                                    </b>
                                                    {{ucfirst(mb_strtolower($activity->state->name)) }}
                                                </div>

                                            @endif

                                            @if (App\Service::checkService($activity->service_id, App\Service::SERVICE_POLICY_SORT))
                                                <div class="column">
                                                    <b>
                                                        # de emisión:
                                                    </b>
                                                    {{ $activity->policy_sort ? $activity->policy_sort->id : 'Id no disponible' }}

                                                </div>

                                                <div class="column">
                                                    <b>
                                                        Código único:
                                                    </b>
                                                    {{ $activity->policy_sort ? $activity->policy_sort->unique_code : 'Sin código único' }}

                                                </div>


                                                <div class="column">
                                                    <b>
                                                        Fecha inicio de vigencia
                                                    </b>
                                                    {{ $activity->policy_sort && $activity->policy_sort->validity_from
                                                        ? ucfirst(strftime('%A %e de %B del %Y', strtotime($activity->policy_sort->validity_from)))
                                                        : 'Fecha no disponible'
                                                    }}
                                                </div>

                                                <div class="column">
                                                    <b>
                                                        Fecha fin de vigencia
                                                    </b>
                                                    {{ $activity->policy_sort && $activity->policy_sort->validity_to
                                                        ? ucfirst(strftime('%A %e de %B del %Y', strtotime($activity->policy_sort->validity_to)))
                                                        : 'Fecha no disponible'
                                                    }}
                                                </div>

                                                <div class="column">
                                                    <b>Valor de la prima</b>
                                                    @if ($activity->policy_sort)
                                                        @if ($activity->policy_sort->temporality === 'permanent')
                                                            {{ ($activity->policy_sort->type_currency === 'USD' ? '$' : '₡') . ' ' . number_format($calculatedAmount, 2, ',', '.') }}
                                                        @else
                                                            {{ ($activity->policy_sort->type_currency === 'USD' ? '$' : '₡') . ' ' . number_format($activity->policy_sort->single_payment_value, 2, ',', '.') }}
                                                        @endif
                                                    @else
                                                        N/a
                                                    @endif
                                                </div>

                                                <div class="column">
                                                    <b>
                                                        Estado de la póliza:
                                                    </b>
                                                    @if(($activity->policy_sort->benefit_colective == 'Si'))
                                                        {{ucfirst(mb_strtolower($activity->state->name)) }} (colectividad)
                                                    @else
                                                        {{ucfirst(mb_strtolower($activity->state->name)) }}
                                                    @endif
                                                    
                                                </div>

                                                <div class="column">
                                                    <b>Forma de pago:</b>
                                                    @if(isset($activity->policy_sort) && $activity->policy_sort->temporality == 'permanent')
                                                        {{ucfirst(mb_strtolower($PERIODICITYT[$activity->policy_sort->periodicity] ?? '')) }}
                                                    @else
                                                        Pago único
                                                    @endif
                                                </div>

                                                <div class="column">
                                                    <b>
                                                        Valor prima pagado acumulado:
                                                    </b>
                                                    {{ $totalAmount ? ($activity->policy_sort->type_currency === 'USD' ? '$' : '₡') . ' ' . number_format($totalAmount, 2, ',', '.') : '' }}

                                                </div>

                                                <div class="column">
                                                    <b>
                                                        Póliza SORT:
                                                    </b>
                                                    {{ $activity->policy_sort ? $activity->policy_sort->formatNumberConsecutive()  : '' }}

                                                </div>
                                                <div class="column">
                                                    <b>Condiciones especiales:</b>
                                                    @php
                                                        // Primero obtenemos el ID del activity parent, si existe
                                                        $parentId = optional($activity->parent_activity)->id;
                                                
                                                        // Solo hacemos la consulta si tenemos un parentId válido
                                                        $cond = null;
                                                        if ($parentId) {
                                                            $cond = \App\ActivityAction::where('action_id', \App\Actions\ActionCotizacionSort::REPORTAR_CONDICIONES_ESPECIALES)
                                                                ->where('activity_id', $parentId)
                                                                ->first();
                                                        }
                                                    @endphp
                                                
                                                    @if($cond)
                                                      SI
                                                    @else
                                                      NO
                                                    @endif
                                                </div>
                                                
                                            @endif

                                            @if (App\Service::checkService($activity->service_id, App\Service::SERVICE_AFFILIATE_WORKFORCE_REPORT_MNK))
                                                <div class="column">
                                                    <b>
                                                        Funcionario actual:
                                                    </b>
                                                    {{ mb_convert_case($activity->user->full_name, MB_CASE_TITLE, "UTF-8")}}
                                                </div>
                                                <div class="column">
                                                    <b>
                                                        Estado:
                                                    </b>
                                                    {{ucfirst(mb_strtolower($activity->state->name)) }}
                                                </div>
                                                <div class="column">
                                                    <b> Fecha de reporte de planilla:</b>
                                                    <span id="fecha-inicio"></span>
                                                </div>
                                                <div class="column">
                                                    <b>Pediodo reportado:</b>
                                                     {{$activity->determinePeriodAffiliateWorkforce() ?? ''}}
                                                </div>
                                            @endif

                                            @if (App\Service::checkService($activity->service_id, App\Service::SERVICE_CONSTANCY_SORT))
                                                <div class="column">
                                                    <b>
                                                        Funcionario actual:
                                                    </b>
                                                    {{ mb_convert_case($activity->user->full_name, MB_CASE_TITLE, "UTF-8")}}
                                                </div>
                                                <div class="column">
                                                    <b>
                                                        Estado:
                                                    </b>
                                                    {{ucfirst(mb_strtolower($activity->state->name)) }}
                                                </div>
                                                <div class="column">
                                                    <b>
                                                        Inicio del trámite:
                                                    </b>
                                                    {{ $activity && !empty($activity->created_at)
                                                        ? ucfirst(strftime('%A %e de %B del %Y', strtotime($activity->created_at)))
                                                        : 'Fecha no disponible'
                                                    }}
                                                </div>
                                                <div class="column">
                                                    <b>Tipo constancia:</b>
                                                    @if($activity->constancy_sort->type == 'policy_certification_up_to_date')
                                                        <span>Certificado de póliza al día</span>

                                                    @endif
                                                    @if($activity->constancy_sort->type == 'employee_account_statement')
                                                        <span>Estado de cuenta del trabajador</span>

                                                    @endif
                                                    @if($activity->constancy_sort->type == 'outstanding_sums_to_be_paid')
                                                        <span>Constancia de sumas pendientes por pagar</span>

                                                    @endif
                                                    @if($activity->constancy_sort->type == 'premiums_paid_certificate')
                                                        <span>Constancia de primas pagadas</span>
                                                    @endif
                                                </div>

                                            @endif

                                            @if (App\Service::checkService($activity->service_id, App\Service::SERVICE_VARIATIONS_SORT))
                                                <div class="column">
                                                    <b>
                                                        Funcionario actual:
                                                    </b>
                                                    {{ mb_convert_case($activity->user->full_name, MB_CASE_TITLE, "UTF-8")}}
                                                </div>
                                                <div class="column">
                                                    <b>
                                                        Inicio del trámite:
                                                    </b>
                                                    {{ $activity && $activity->created_at
                                                        ? ucfirst(strftime('%A %e de %B del %Y', strtotime($activity->created_at)))
                                                        : 'Fecha no disponible'
                                                    }}
                                                </div>
                                                <div class="column">
                                                    <b>
                                                        Fecha de solicitud:
                                                    </b>
                                                    {{ $activity && $activity->created_at
                                                        ? ucfirst(strftime('%A %e de %B del %Y', strtotime($activity->created_at)))
                                                        : 'Fecha no disponible'
                                                    }}
                                                </div>
                                                <div class="column">
                                                    <b>
                                                        Tipo de variación solicitada:
                                                    </b>
                                                    {{isset($activity->variations_sort->variation_type) && isset($VARIATIONS_TYPE[$activity->variations_sort->variation_type])
                                                        ? ucfirst(mb_strtolower($VARIATIONS_TYPE[$activity->variations_sort->variation_type]))
                                                        : 'No disponible'}}
                                                </div>
                                            @endif
                                            @if (App\Service::checkService($activity->service_id, App\Service::SERVICE_GIS_SORT_MNK_SORT))
                                                <div class="column">
                                                    <b>
                                                        Nombre:
                                                    </b>
                                                    {{$activity->parent_activity?ucwords(strtolower($activity->parent_activity->affiliate->first_name)):''}}
                                                </div>
                                                <div class="column">
                                                    <b>
                                                        Póliza SORT:
                                                    </b>
                                                    {{$activity->parent_activity?$activity->parent_activity->policy_sort->formatNumberConsecutive():'' }}
                                                </div>
                                                <div class="column">
                                                    <b>
                                                        Fecha del caso:
                                                    </b>

                                                    {{$activity->created_at ?ucfirst(strftime('%A %e de %B del %Y', strtotime($activity->created_at))):'' }}
                                                </div>
                                                <div class="column">
                                                    <b>
                                                        # del aviso:
                                                    </b>
                                                    {{isset($activity->gis_sort) ? $activity->gis_sort->consecutive_gis : ''}}
                                                </div>
                                                <div class="column">
                                                    <b>
                                                        # del caso:
                                                    </b>
                                                    {{isset($activity->gis_sort) ? $activity->gis_sort->formatCaseNumberIfReported() : ''}}
                                                </div>
                                                <div class="column">
                                                    <b>
                                                        Origen:
                                                    </b>
                                                    {{ isset($activity->gis_sort) ? $activity->gis_sort->origin : '' }}
                                                </div>
                                                <div class="column">
                                                    <b>
                                                        Fecha de seguimiento:
                                                    </b>

                                                    {{isset($activity->gis_sort) && isset($activity->gis_sort->follow_up_date) ? ucfirst(strftime('%A %e de %B del %Y', strtotime($activity->gis_sort->follow_up_date))) : ''  }}
                                                </div>
                                                <div class="column">
                                                    <b>
                                                        % total PCG:
                                                    </b>
                                                    {{isset($activity->gis_sort) ? $activity->gis_sort->total_pcg : ''  }}
                                                </div>
                                                <div class="column">
                                                    <b>
                                                        Resultado de auditoría:
                                                    </b>
                                                    {{isset($activity->gis_sort) ? $activity->gis_sort->audit_result_pcg : '' }}
                                                </div>
                                                <div class="column">
                                                    <b>
                                                        Estado:
                                                    </b>
                                                    {{ucfirst(mb_strtolower($activity->state->name)) }}
                                                </div>

                                            @endif
                                            @if (App\Service::checkService($activity->service_id, App\Service::SERVICE_QUOTATIONS))
                                                <div class="column">
                                                    <b>
                                                        # de cotización:
                                                    </b>
                                                    {{isset($activity->quotation) ? $activity->quotation->formatNumber(): ''}}
                                                </div>
                                                <div class="column">
                                                    <b>
                                                        Fecha de cotización:
                                                    </b>
                                                    {{$activity->created_at->formatLocalized('%A %d de %B de %Y') }}
                                                </div>
                                                <div class="column">
                                                    <b>
                                                        Monto de la prima:
                                                    </b>
                                                    {{ $activity->quotation ? $activity->quotation->temporality == 'short'?
                                                    ($activity->quotation->type_currency === 'USD' ? '$' : '₡') . ' ' .  number_format($activity->quotation->single_payment_value, 2, ',', '.') :
                                                    ($activity->quotation->type_currency === 'USD' ? '$' : '₡') . ' ' . number_format($activity->quotation->annual_calculation_amount, 2, ',', '.') : ''  }}
                                                </div>

                                                <div class="column">
                                                    <b>
                                                        Estado:
                                                    </b>
                                                    {{ucfirst(mb_strtolower($activity->state->name)) }}
                                                </div>

                                            @endif

                                            @if (App\Service::checkService($activity->service_id, App\Service::SERVICE_MEDICAL_SERVICES_SORT_MNK))

                                                {{--Este siguiente php se utiliza para prestaciones médicas --}}
                                                @php
                                                    $dataPolicy = $activity->policy_sort_profile_affiliate_medical_service() ?? '';
                                                   $dataGis = $activity->gis_profile_affiliate_medical_service() ?? '';
                                                @endphp
                                                <div class="column">
                                                    <b>
                                                        Funcionario actual:
                                                    </b>
                                                    {{ mb_convert_case($activity->user->full_name, MB_CASE_TITLE, "UTF-8")}}
                                                </div>
                                                <div class="column">
                                                    <b>
                                                        Estado del servicio:
                                                    </b>
                                                    {{ mb_strtoupper(mb_substr($activity->state->name, 0, 1), 'UTF-8') . mb_strtolower(mb_substr($activity->state->name, 1), 'UTF-8') }}
                                                </div>
                                                <div class="column">
                                                    <b>
                                                        Inicio del trámite:
                                                    </b>
                                                    {{ $activity && !empty($activity->created_at)
                                                        ? ucfirst(strftime('%A %e de %B del %Y', strtotime($activity->created_at )))
                                                        : 'Fecha no disponible'
                                                    }}
                                                </div>
                                                <div class="column">
                                                    <b>
                                                        # identificación:
                                                    </b>
                                                    @if(!empty($activity->affiliate->doc_number))
                                                        {{  $activity->affiliate->doc_number }}
                                                    @endif
                                                </div>
                                                <div class="column">
                                                    <b>
                                                        Nombre del paciente:
                                                    </b>
                                                    @if(!empty($activity->affiliate->first_name))
                                                        {{  ucwords(strtolower($activity->affiliate->first_name)) }}
                                                    @endif
                                                </div>
                                                <div class="column">
                                                    <b>
                                                        Póliza SORT:
                                                    </b>
                                                    @if($dataPolicy)
                                                        <a class="header"
                                                           href="{{secure_url('servicio/' . $dataPolicy->activity_id)}}">

                                                            {{ $dataPolicy->id }}

                                                        </a>
                                                    @endif
                                                </div>
                                            @endif
                                            @if (App\Service::checkService($activity->service_id, App\Service::SERVICE_MEDICAL_SERVICES_SECONDARY_CARE_SORT_MNK))

                                                {{--Este siguiente php se utiliza para prestaciones médicas atención secundaria --}}
                                                @php
                                                    $dataPolicy = $activity->policy_sort_profile_affiliate_medical_service() ?? '';
                                                   $dataGis = $activity->gis_profile_affiliate_medical_service() ?? '';
                                                @endphp
                                                <div class="column">
                                                    <b>
                                                        Funcionario actual:
                                                    </b>
                                                    {{ mb_convert_case($activity->user->full_name, MB_CASE_TITLE, "UTF-8")}}
                                                </div>
                                                <div class="column">
                                                    <b>
                                                        Estado del servicio:
                                                    </b>
                                                    {{ mb_strtoupper(mb_substr($activity->state->name, 0, 1), 'UTF-8') . mb_strtolower(mb_substr($activity->state->name, 1), 'UTF-8') }}
                                                </div>
                                                <div class="column">
                                                    <b>
                                                        Inicio del trámite:
                                                    </b>
                                                    {{ $activity && !empty($activity->created_at)
                                                        ? ucfirst(strftime('%A %e de %B del %Y', strtotime($activity->created_at )))
                                                        : 'Fecha no disponible'
                                                    }}
                                                </div>
                                                <div class="column">
                                                    <b>
                                                        # identificación:
                                                    </b>
                                                    @if(!empty($activity->affiliate->doc_number))
                                                        {{  $activity->affiliate->doc_number }}
                                                    @endif
                                                </div>
                                                <div class="column">
                                                    <b>
                                                        Nombre del paciente:
                                                    </b>
                                                    @if(!empty($activity->affiliate->first_name))
                                                        {{  ucwords(strtolower($activity->affiliate->first_name)) }}
                                                    @endif
                                                </div>
                                                <div class="column">
                                                    <b>
                                                        Póliza SORT:
                                                    </b>
                                                    @if($dataPolicy)
                                                        <a class="header"
                                                           href="{{secure_url('servicio/' . $dataPolicy->activity_id)}}">

                                                            {{ $dataPolicy->id }}

                                                        </a>
                                                    @endif
                                                </div>
                                            @endif

                                            
                                            @if (App\Service::checkService($activity->service_id, App\Service::SERVICE_SUBROGACION))
                                                <div class="column">
                                                    <b>
                                                        Funcionario actual:
                                                    </b>
                                                    {{ mb_convert_case($activity->user->full_name, MB_CASE_TITLE, "UTF-8")}}
                                                </div>
                                              
                                                <div class="column">
                                                    <b>
                                                        Fecha de solicitud:
                                                    </b>
                                                    {{ $activity && $activity->created_at
                                                        ? ucfirst(strftime('%A %e de %B del %Y', strtotime($activity->created_at)))
                                                        : 'Fecha no disponible'
                                                    }}
                                                </div>
                                            
                                            @endif


                                            @if (App\Service::checkService($activity->service_id, App\Service::SERVICE_MEDICAMENTOS_MNK)
                                              || App\Service::checkService($activity->service_id, App\Service::SERVICE_SUPPLIER_MOT_MNK))

                                                {{--Este siguiente php se utiliza para medicamentos / insumos --}}
                                                @php
                                                    $dataPolicyOtherService = $activity->policy_sort_profile_affiliate() ?? '';
                                                    $dataGisOtherService = $activity->gis_profile_affiliate() ?? '';
                                                @endphp

                                                <div class="column">
                                                    <b>
                                                        Funcionario actual:
                                                    </b>
                                                    {{ mb_convert_case($activity->user->full_name, MB_CASE_TITLE, "UTF-8")}}
                                                </div>
                                                <div class="column">
                                                    <b>
                                                        Estado del servicio:
                                                    </b>
                                                    {{ mb_strtoupper(mb_substr($activity->state->name, 0, 1), 'UTF-8') . mb_strtolower(mb_substr($activity->state->name, 1), 'UTF-8') }}
                                                </div>
                                                <div class="column">
                                                    <b>
                                                        Inicio del trámite:
                                                    </b>
                                                    {{ $activity && !empty($activity->created_at)
                                                        ? ucfirst(strftime('%A %e de %B del %Y', strtotime($activity->created_at )))
                                                        : 'Fecha no disponible'
                                                    }}
                                                </div>

                                                <div class="column">
                                                    <b>
                                                        Nombre del paciente:
                                                    </b>
                                                    @if(!empty($activity->parent_activity->affiliate->first_name))
                                                        {{  ucwords(strtolower($activity->parent_activity->affiliate->first_name)) }}
                                                    @endif
                                                </div>

                                                <div class="column">
                                                    <b>
                                                        Póliza SORT:
                                                    </b>
                                                    @if($dataPolicyOtherService)
                                                        <a class="header"
                                                           href="{{secure_url('servicio/' . $dataPolicyOtherService->activity_id)}}">

                                                            {{ $dataPolicyOtherService->id }}

                                                        </a>
                                                    @endif
                                                </div>

                                                <div class="column">
                                                    @if($dataGisOtherService)
                                                        <b>Fecha del caso:</b>
                                                        {{ $activity && !empty($dataGisOtherService->created_at)
                                                        ? ucfirst(strftime('%A %e de %B del %Y', strtotime($dataGisOtherService->created_at )))
                                                        : 'Fecha no disponible'
                                                        }}
                                                    @endif
                                                </div>

                                                <div class="column">
                                                    <b>
                                                        # del aviso:
                                                    </b>
                                                    @if($dataGisOtherService)
                                                        {{ $dataGisOtherService->consecutive_gis ?? '' }}
                                                    @endif
                                                </div>

                                                <div class="column">
                                                    <b>
                                                        # del caso:
                                                    </b>
                                                    @if($dataGisOtherService)
                                                        {{ $dataGisOtherService->formatCaseNumberIfReported() }}
                                                    @endif
                                                </div>
                                            @endif

                                            @if (App\Service::checkService($activity->service_id, App\Service::SERVICE_MEDICAL_BILLS_MNK))
                                                <div class="column">
                                                    <b>
                                                        Funcionario actual:
                                                    </b>
                                                    {{ mb_convert_case($activity->user->full_name, MB_CASE_TITLE, "UTF-8")}}
                                                </div>
                                                <div class="column">
                                                    <b>
                                                        Estado del servicio:
                                                    </b>
                                                    {{ mb_strtoupper(mb_substr($activity->state->name, 0, 1), 'UTF-8') . mb_strtolower(mb_substr($activity->state->name, 1), 'UTF-8') }}
                                                </div>
                                                <div class="column">
                                                    <b>
                                                        Inicio del trámite:
                                                    </b>
                                                    {{ $activity && !empty($activity->created_at)
                                                        ? ucfirst(strftime('%A %e de %B del %Y', strtotime($activity->created_at)))
                                                        : 'Fecha no disponible'
                                                    }}
                                                </div>

                                                <div class="column">
                                                    <b>
                                                        Nombre del proveedor:
                                                    </b>
                                                    {{ $activity && !empty(optional(optional($activity->medical_bill)->provider())->name)
                                                         ? ucwords(strtolower(optional(optional($activity->medical_bill)->provider())->name ?? ''))
                                                         : 'Proveedor no especificado'
                                                    }}
                                                </div>
                                                <div class="column">
                                                    <b>Fecha radicación factura:</b>
                                                    {{ $activity && !empty($activity->medical_bill->date_created_electronic_invoice)
                                                        ? ucfirst(strftime('%A %e de %B del %Y', strtotime($activity->medical_bill->date_created_electronic_invoice )))
                                                        : 'Factura electrónica sin radicar'
                                                    }}
                                                </div>
                                                <div class="column">
                                                    <b>
                                                        Valor total factura:
                                                    </b>
                                                    @if(!empty($activity->medical_bill->total_value_invoice))
                                                        {{$TYPE_CURRENCY[$activity->medical_bill->type_currency] ?? ''}}
                                                        {{number_format($activity->medical_bill->total_value_invoice, 2, ',', '.')}}
                                                    @else
                                                        Factura electrónica sin radicar
                                                    @endif
                                                </div>
                                            @endif

                                            @if (App\Service::checkService($activity->service_id, App\Service::SERVICE_POLICY_SORT_COLLECTION))
                                                <div class="column">
                                                    <b>
                                                        Funcionario actual:
                                                    </b>
                                                    {{ mb_convert_case($activity->user->full_name, MB_CASE_TITLE, "UTF-8")}}
                                                </div>
                                                <div class="column">
                                                    <b>
                                                        Estado del servicio:
                                                    </b>
                                                    {{ mb_strtoupper(mb_substr($activity->state->name, 0, 1), 'UTF-8') . mb_strtolower(mb_substr($activity->state->name, 1), 'UTF-8') }}
                                                </div>
                                                <div class="column">
                                                    <b>
                                                        Inicio del trámite:
                                                    </b>
                                                    {{ $activity && !empty($activity->policy_sort_collection->created_at)
                                                        ? ucfirst(strftime('%A %e de %B del %Y', strtotime($activity->policy_sort_collection->created_at )))
                                                        : 'Fecha no disponible'
                                                    }}
                                                </div>
                                                <div class="column">
                                                    <b>
                                                        Póliza SORT:
                                                    </b>
                                                    @if(!empty($activity->parent_activity->policy_sort->id))
                                                        <a class="header"
                                                           href="{{secure_url('servicio/' . $activity->parent_activity->id)}}">

                                                            {{ $activity->parent_activity->policy_sort->formatNumberConsecutive() }}

                                                        </a>
                                                    @endif
                                                </div>
                                                <div class="column">
                                                    <b> Fecha límite de pago del recibo:</b>
                                                    {{ $activity && !empty($activity->policy_sort_collection->due_date)
                                                       ? ucfirst(strftime('%A %e de %B del %Y', strtotime($activity->policy_sort_collection->due_date)))
                                                       : 'Fecha no disponible'
                                                    }}
                                                </div>
                                                <div class="column">
                                                    <b>
                                                        Tipo de recibo:
                                                    </b>
                                                    {{$TYPE_RECEIPT[$activity->policy_sort_collection->type_receipt ?? ''] ?? ''}}
                                                </div>
                                                <div class="column">
                                                    <b>
                                                        Valor a pagar:
                                                    </b>
                                                    @if(!empty($activity->policy_sort_collection->total_amount ?? ''))
                                                        {{$MONEY_TYPE[$activity->parent_activity->policy_sort->type_currency]['symbol'] ?? ''}}
                                                        {{number_format($activity->policy_sort_collection->total_amount, 2, ',', '.')}}
                                                    @endif
                                                </div>
                                            @endif

                                            @if($activity->service_id == \App\Service::SERVICE_LIQUIDATION_SORT_MNK)

                                                <div class="column">
                                                    <b>
                                                        Funcionario actual:
                                                    </b>
                                                    {{ mb_convert_case($activity->user->full_name, MB_CASE_TITLE, "UTF-8")}}
                                                </div>
                                                <div class="column">
                                                    <b>
                                                        Estado del servicio:
                                                    </b>
                                                    {{ mb_strtoupper(mb_substr($activity->state->name, 0, 1), 'UTF-8') . mb_strtolower(mb_substr($activity->state->name, 1), 'UTF-8') }}
                                                </div>
                                                <div class="column">
                                                    <b>
                                                        Inicio del trámite:
                                                    </b>
                                                    {{ $activity && $activity->created_at
                                                        ? ucfirst(strftime('%A %e de %B del %Y', strtotime($activity->created_at)))
                                                        : 'Fecha no disponible'
                                                    }}
                                                </div>
                                                <div class="column">
                                                    <b>Valor liquidacion:</b>
                                                    {{$MONEY_TYPE[$activity->parent_activity->policy_sort->type_currency ?? '']['symbol'] ?? ''}}
                                                    {{ number_format( $activity->liquidation_sort->settlement_result ?? 0, 2, ',', '.') }}
                                                </div>
                                                <div class="column">
                                                    <b>Estado de pago:</b>
                                                    {{ $activity->liquidation_sort ? $activity->liquidation_sort->get_payment_status($activity->liquidation_sort->activity_id) : '' }}
                                                </div>
                                                <div class="column">
                                                    <b>
                                                        Fecha de pago:
                                                    </b>
                                                    @if($activity->liquidation_sort && $activity->liquidation_sort->get_payment_date($activity->liquidation_sort->activity_id))
                                                        {{ ucfirst(strftime('%A %e de %B del %Y', strtotime($activity->liquidation_sort->get_payment_date($activity->liquidation_sort->activity_id)))) }}
                                                    @endif
                                                </div>
                                            @endif


                                            @if($activity->affiliate->survival_state == 'FALLECIDO')
                                                <div class="columns"
                                                     style="margin-left: -10px;  width: 100%; text-align: center">
                                                    <div class="ui negative message middle wide column sixteen">
                                                        <div class="header"> EL AFILIADO SE ENCUENTRA FALLECIDO</div>
                                                        <div id="dashboard">
                                                            Fallecido el
                                                            día {{date('d/m/Y', strtotime($activity->affiliate->death_date))}}
                                                        </div>
                                                    </div>
                                                </div>
                                            @endif
                                            @if (App\Service::checkService($activity->service_id, App\Service::SERVICE_PE_IT_SORT_MNK))
                                                <div class="column">
                                                    <b>
                                                        Funcionario actual:
                                                    </b>
                                                    {{ mb_convert_case($activity->user->full_name, MB_CASE_TITLE, "UTF-8")}}
                                                </div>
                                                <div class="column">
                                                    <b>
                                                        Estado del servicio:
                                                    </b>
                                                    {{ mb_strtoupper(mb_substr($activity->state->name, 0, 1), 'UTF-8') . mb_strtolower(mb_substr($activity->state->name, 1), 'UTF-8') }}
                                                </div>
                                                <div class="column">
                                                    <b>
                                                        Inicio del trámite:
                                                    </b>
                                                    {{ $activity && $activity->created_at
                                                        ? ucfirst(strftime('%A %e de %B del %Y', strtotime($activity->created_at)))
                                                        : 'Fecha no disponible'
                                                    }}
                                                </div>
                                                <div class="column">
                                                    <b>
                                                        Días IT:
                                                    </b>
                                                    {{ $activity->peItSort->inabilitySort->days_it ?? "" }}
                                                </div>
                                                <div class="column">
                                                    <b>
                                                        Valor a pagar IT:
                                                    </b>
                                                    {{ $activity->peItSort->inabilitySort->amount_pay ?? "Pendiente"  }}

                                                </div>
                                            @endif

                                            @if (App\Service::checkService($activity->service_id, App\Service::SERVICE_PE_IP_SORT_MNK))
                                                <div class="column">
                                                    <b>
                                                        Funcionario actual:
                                                    </b>
                                                    {{ mb_convert_case($activity->user->full_name, MB_CASE_TITLE, "UTF-8")}}
                                                </div>
                                                <div class="column">
                                                    <b>
                                                        Estado del servicio:
                                                    </b>
                                                    {{ mb_strtoupper(mb_substr($activity->state->name, 0, 1), 'UTF-8') . mb_strtolower(mb_substr($activity->state->name, 1), 'UTF-8') }}
                                                </div>
                                                <div class="column">
                                                    <b>
                                                        Inicio del trámite:
                                                    </b>
                                                    {{ $activity && $activity->created_at
                                                        ? ucfirst(strftime('%A %e de %B del %Y', strtotime($activity->created_at)))
                                                        : 'Fecha no disponible'
                                                    }}
                                                </div>

                                                <div class="column">
                                                    <b>
                                                        Tipo de incapacidad permanente:
                                                    </b>
                                                    {{ $activity->pe_ip_sort->casedata_type_disability ?? "" }}
                                                </div>
                                                <div class="column">
                                                    <b>
                                                        Renta por incapacidad permanente:
                                                    </b>

                                                    {{ preg_replace('/[^a-zA-Z€\$\£\¥\₣₤₯₰₲]/', '', $activity->pe_ip_sort->calc_annual_income ?? '') . ' ' . number_format((float) str_replace([',', ' '], '', preg_replace('/[^0-9,\.]/', '', $activity->pe_ip_sort->calc_annual_income ?? '')), 2, ',', '.') }}


                                                </div>
                                            @endif
                                            @if (App\Service::checkService($activity->service_id, App\Service::SERVICE_PE_MPT_SORT_MNK))
                                                <div class="column">
                                                    <b>
                                                        Funcionario actual:
                                                    </b>
                                                    {{ mb_convert_case($activity->user->full_name, MB_CASE_TITLE, "UTF-8")}}
                                                </div>
                                                <div class="column">
                                                    <b>
                                                        Estado del servicio:
                                                    </b>
                                                    {{ mb_strtoupper(mb_substr($activity->state->name, 0, 1), 'UTF-8') . mb_strtolower(mb_substr($activity->state->name, 1), 'UTF-8') }}
                                                </div>
                                                <div class="column">
                                                    <b>
                                                        Inicio del trámite:
                                                    </b>
                                                    {{ $activity && $activity->created_at
                                                        ? ucfirst(strftime('%A %e de %B del %Y', strtotime($activity->created_at)))
                                                        : 'Fecha no disponible'
                                                    }}
                                                </div>

                                                <div class="column">
                                                    <b>
                                                        Renta Anual:
                                                    </b>
                                                    {{ preg_replace('/[^a-zA-Z€\$\£\¥\₣₤₯₰₲]/', '', $activity->pe_mpt_sort->annual_income ?? '') . ' ' . number_format((float) str_replace([',', ' '], '', preg_replace('/[^0-9,\.]/', '', $activity->pe_mpt_sort->annual_income ?? '')), 2, ',', '.') }}
                                                </div>
                                                <div class="column">
                                                    <b>
                                                        Total de años a pagar:
                                                    </b>
                                                    {{ $activity->pe_mpt_sort->total_years_to_pay ?? "" }}
                                                </div>
                                            @endif
                                            {{--Para planilla tomador--}}
                                            @if (App\Service::checkService($activity->service_id, App\Service::SERVICE_REPORT_TAKEN_FORM_MNK))
                                                <div class="column">
                                                    <b>
                                                        Funcionario actual:
                                                    </b>
                                                    {{ mb_convert_case($activity->user->full_name, MB_CASE_TITLE, "UTF-8")}}
                                                </div>
                                                <div class="column">
                                                    <b>
                                                        Estado:
                                                    </b>
                                                    {{ucfirst(mb_strtolower($activity->state->name)) }}
                                                </div>
                                                <div class="column">
                                                    <b> Fecha de reporte de planilla:</b>
                                                    <span id="fecha-inicio"></span>
                                                </div>
                                            @endif
                                            @if($moreThanTenDaysBTC)
                                                <div class="columns"
                                                     style="margin-left: -10px;  width: 100%; text-align: center">
                                                    <div class="ui negative message middle wide column sixteen">
                                                        <div class="header"> Manifestación superior a 10 dias</div>
                                                        <div id="dashboard">
                                                            Han transcurrido mas de 10 día entre la fecha de
                                                            notificación y la fecha de
                                                            recepción de la manifestación <br>
                                                            <b>Se ejecutó la acción NO APRUEBA OFICIO POR "MAL CONTEO DE
                                                                TERMINOS"</b>
                                                        </div>
                                                    </div>
                                                </div>
                                            @elseif(!$moreThanTenDaysBTC && $activity->service_id == 60 && $moreThanTenDaysActionValBTC)
                                                <div class="columns"
                                                     style="margin-left: -10px;  width: 100%; text-align: center">
                                                    <div class="ui info message middle wide column sixteen">
                                                        <div class="header"> Manifestación inferior a 10 dias</div>
                                                        <div id="dashboard">
                                                            Han transcurrido menos de 10 días entre la fecha de
                                                            notificación y la fecha de
                                                            recepción de la manifestación <br>
                                                            <b>lSe ejecutó la acción NO APRUEBA OFICIO POR "MAL CONTEO
                                                                DE TERMINOS"</b>
                                                        </div>
                                                    </div>
                                                </div>
                                            @elseif($moreThanTenDays && !$moreThanTenDaysBTC)
                                                <div class="columns"
                                                     style="margin-left: -10px;  width: 100%; text-align: center">
                                                    <div class="ui negative message middle wide column sixteen">
                                                        <div class="header"> Manifestación superior a 10 dias</div>
                                                        <div id="dashboard">
                                                            Han transcurrido mas de 10 días entre la fecha de
                                                            notificación y la fecha de
                                                            recepción de la manifestación
                                                        </div>
                                                    </div>
                                                </div>
                                            @elseif(!$moreThanTenDays && $activity->service_id == 60 && $moreThanTenDaysActionVal && !$moreThanTenDaysBTC)
                                                <div class="columns"
                                                     style="margin-left: -10px;  width: 100%; text-align: center">
                                                    <div class="ui info message middle wide column sixteen">
                                                        <div class="header"> Manifestación inferior a 10 dias</div>
                                                        <div id="dashboard">
                                                            Han transcurrido menos de 10 días entre la fecha de
                                                            notificación y la fecha de
                                                            recepción de la manifestación
                                                        </div>
                                                    </div>
                                                </div>
                                            @endif
                                            @if (App\Service::checkService($activity->service_id, App\Service::SERVICE_AUDIT))
                                                <div class="column">
                                                    <b>
                                                        Nro Radicado:
                                                    </b>
                                                    @if($activity->audit && $activity->audit->num_radicate)
                                                        {{$activity->audit->num_radicate}}
                                                    @endif
                                                </div>
                                            @endif
                                            @if (App\Service::checkService($activity->service_id, App\Service::SERVICE_CONTROVERSY))
                                                <div class="column">
                                                    <a href="{{secure_url("servicio/{$activity->id}/planilla")}}"
                                                       target="_blank"
                                                       class="ui basic small button">Planilla Junta</a>
                                                </div>
                                            @endif
                                            <div style="display: none;" class="column">
                                                <b>
                                                    Cierre del tramite:
                                                </b>
                                            </div>
                                            <div style="display: none;" class="column">
                                                <b>
                                                    Numero de autorización:
                                                </b>
                                                @if (Auth::user()->isViewer() || !in_array(Auth::user()->area_id, $activity->state->areas_ids()))
                                                    {{$activity->auth_number}}
                                                @else
                                                    <div class="ui fluid left icon input">
                                                        <input autocomplete="off" name="auth_number" type="text"
                                                               value="{{$activity->auth_number}}"/>
                                                        <i class="hashtag icon">
                                                        </i>
                                                    </div>
                                                @endif
                                            </div>
                                        </div>
                                        <div class="columns row">
                                            @if(in_array(!$activity->service_id, [53, 60]))
                                                <div class="eight wide column">
                                                    <b>
                                                        Vinculación laboral:
                                                    </b>
                                                    @if (Auth::user()->isViewer() || !in_array(Auth::user()->area_id, $activity->state->areas_ids()))
                                                        @if($activity->employment)
                                                            {{$activity->employment->getEmployerName()}}
                                                            - {{$activity->employment->period}}
                                                        @endif
                                                    @else
                                                        <div class="ui selection fluid dropdown">
                                                            <input autocomplete="off" name="employment_id" type="hidden"
                                                                   value="{{$activity->employment_id or ''}}"/>
                                                            <div class="default text">
                                                                Seleccione uno
                                                            </div>
                                                            <i class="dropdown icon">
                                                            </i>
                                                            <div class="menu">
                                                                @foreach($activity->affiliate->employments as $emp)
                                                                    <div class="item" data-value="{{$emp->id}}">
                                                                        {{$emp->getEmployerName()}} - {{$emp->period}}
                                                                    </div>
                                                                @endforeach
                                                            </div>
                                                        </div>
                                                    @endif
                                                </div>
                                            @endif
                                        </div>
                                        <div class="four columns row">
                                            @foreach($activity->service->fields as $field)
                                                <div class="column">
                                                    <b>{{$field->name}}{!! $field->required ? '<span style="color:red;">*</span>' : '' !!}</b>
                                                    @if (Auth::user()->isViewer() || !in_array(Auth::user()->area_id, $activity->state->areas_ids()))
                                                        {{$activity->fieldValue($field->id)}}
                                                    @else
                                                        @if ($field->type == 'select')
                                                            <div class="ui selection fluid dropdown">
                                                                <input onchange="saveActivity()" autocomplete="off"
                                                                       name="fields[{{$field->id}}]" type="hidden"
                                                                       value="{{$activity->fieldValue($field->id)}}"/>
                                                                <div class="default text">
                                                                    Seleccione uno
                                                                </div>
                                                                <i class="dropdown icon">
                                                                </i>
                                                                <div class="menu">
                                                                    @foreach(explode(',', $field->values) as $value)
                                                                        <div class="item" data-value="{{$value}}">
                                                                            {{$value}}
                                                                        </div>
                                                                    @endforeach
                                                                </div>
                                                            </div>
                                                        @endif
                                                        @if ($field->type == 'text')
                                                            <div class="ui form">
                                                                <div class="field">
                                                                    <input style="display: block;"
                                                                           onchange="saveActivity()"
                                                                           autocomplete="off"
                                                                           name="fields[{{$field->id}}]"
                                                                           value="{{$activity->fieldValue($field->id)}}"
                                                                            {{ $activity->creation_source == 'INTEGRACIÓN' && in_array($field->id, [10, 13 ,15])  ? 'disabled' : '' }} />
                                                                </div>
                                                            </div>
                                                        @endif
                                                    @endif
                                                </div>
                                            @endforeach
                                            <div style="position: fixed; right: 2rem; display: flex; flex-flow: row wrap; justify-content: flex-start; width: 550px; font-size: 7pt">
                                                @php
                                                    $mark_source = null;

                                                    if($activity->service_id == 57) {
                                                        $mark_source = $activity->pcl;
                                                    } elseif($activity->service_id == 64) {
                                                        $mark_source = $activity->invalidity_state_pfour;
                                                    }
                                                @endphp
                                                @if ($mark_source && count($mark_source->admin_marks) > 0)
                                                    @foreach ($mark_source->admin_marks->unique('admin_mark') as $adm_mark)
                                                        @if($adm_mark->admin_mark === 'TUTELA')
                                                            <div class="rectangle" style="background: red;">
                                                                <span>TUTELA</span></div>
                                                        @endif
                                                        @if($adm_mark->admin_mark === 'ENTES DE CONTROL')
                                                            <div class="rectangle" style="background: green;"><span>ENTES</span>
                                                            </div>
                                                        @endif
                                                        @if($adm_mark->admin_mark === 'CASO GAP')
                                                            <div class="rectangle" style="background: blue;">
                                                                <span>GAP</span></div>
                                                        @endif
                                                        @if($adm_mark->admin_mark === 'PQR')
                                                            <div class="rectangle" style="background: darkkhaki;"><span>PQR</span>
                                                            </div>
                                                        @endif
                                                        @if($adm_mark->admin_mark === 'GERENCIA PREVENCIION DEL FRAUDE')
                                                            <div class="rectangle" style="background: orange;"><span>FRAUDE</span>
                                                            </div>
                                                        @endif
                                                        @if($adm_mark->admin_mark === 'SUSPENDIDO EN NÓMINA')
                                                            <div class="rectangle" style="background: deepskyblue;">
                                                                <span>SUSPENDIDO NOM</span></div>
                                                        @endif
                                                        @if($adm_mark->admin_mark === 'REACTIVADO EN NÓMINA')
                                                            <div class="rectangle" style="background: deepskyblue;">
                                                                <span>REACTIVADO NOM</span></div>
                                                        @endif
                                                        @if($adm_mark->admin_mark === 'ACLARATORIA')
                                                            <div class="rectangle" style="background: dimgrey;"><span>ACLARATORIA</span>
                                                            </div>
                                                        @endif
                                                    @endforeach
                                                @endif
                                            </div>
                                            @foreach($activity->activity_actions as $k => $aa)
                                                @foreach($aa->fields as $field)
                                                    @if($field->action_field->type === 'button')
                                                        <div class="column">
                                                            <b>&nbsp;</b>
                                                            <div class="ui form">
                                                                <div class="field">
                                                                    <a href="{{$field->value}}" target="_blank">
                                                                        <button class="ui primary button">
                                                                            Iniciar Telemedicina
                                                                        </button>
                                                                    </a>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    @endif
                                                @endforeach
                                            @endforeach
                                        </div>
                                </div>
                        </div>
                        <!-- DOCUMENTOS DEL SERVICIO -->
                        @if(Auth::user()->is_view_general_permission_service($activity->service_id, \App\Permission::CARGAR_DOCUMENTOS))
                            @include('activity.documents', array('activity' => $activity, 'tab' => $tab))
                        @endif
                        <!-- ACTION -->
                        <!-- 1. Se muestra si el rol tiene el permiso genera y el usuario esta asignado al servicio -->
                        <!-- 2. Se muestra si el usuario es Admin -->
                        @if(Auth::user()->is_view_general_permission_service($activity->service_id, \App\Permission::NUEVA_ACCION) || Auth::user()->isAdmin())
                            <div class="title">
                                <i class="dropdown icon">
                                </i>
                                Nueva acción
                            </div>
                            @if ((!$activity->service->required_employment || $activity->employment) && (!$activity->service->required_infosource || $activity->information_source))
                                <div class="content">
                                    <form autocomplete="off" class="ui small form" id="action_form" method="post">
                                        {{csrf_field()}}
                                        <div class="fields">
                                            <div class="five wide required field">
                                                <label>
                                                    Acción
                                                </label>
                                                <div class="ui selection dropdown">
                                                    <input autocomplete="off" name="action_id" id="action"
                                                           type="hidden"/>
                                                    <div class="default text">
                                                        Seleccione uno
                                                    </div>
                                                    <i class="dropdown icon">
                                                    </i>
                                                    <div class="menu">
                                                        @foreach($activity->state_actions as $a)
                                                            <div class="{{$a->isDisabled($activity) ? 'disabled' : ''}} item"
                                                                 data-value="{{$a->id}}">
                                                                {{$a->name}}
                                                                @if ($a->isDisabled($activity))
                                                                    <span style="font-size: .75em; font-style: italic; color: red;">
                                                                    {!! $a->disabledReason($activity) !!}
                                                                    </span>
                                                                @endif
                                                            </div>
                                                        @endforeach
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="seven wide required field">
                                                <label>
                                                    Asignar a
                                                </label>
                                                <div id="users" class="ui search selection dropdown">
                                                    <input autocomplete="off" name="user_id" type="hidden"/>
                                                    <div style="width: 100%;" class="default text">
                                                        Seleccione uno
                                                    </div>
                                                    <i class="dropdown icon">
                                                    </i>
                                                    <div class="menu">
                                                    </div>
                                                </div>
                                            </div>
                                            @if(in_array(!$activity->service_id, [\App\Service::SERVICE_PCL_COLPENSIONES, \App\Service::SERVICE_DETERMINATION_IT_COLPENSIONES,\App\Service::SERVICE_INVALIDITY_STATE_P4_COLPENSIONES]))
                                                <div class="four wide field">
                                                    <label>
                                                        Fecha de alerta
                                                    </label>
                                                    <input class="datepicker" name="alert_date" type="text"/>
                                                </div>
                                            @endif
                                        </div>
                                        <div class="required field">
                                            <label>
                                                Descripción
                                            </label>
                                            <textarea name="description" rows="2"></textarea>
                                        </div>
                                        <div class="three fields" id="fields">
                                        </div>
                                        <div class="field" id="action_documents">
                                        </div>
                                        <div class="field">
                                            <button id="nueva_accion" class="ui secondary button">
                                                <i class="save icon">
                                                </i>
                                                Guardar
                                            </button>
                                        </div>
                                    </form>
                                </div>

                            @else
                                <div class="active content">
                                    <div class="ui negative message">
                                        <div class="header">No se puede ejecutar ninguna acción</div>
                                        <ul>
                                            @if ($activity->service->required_employment && !$activity->employment)
                                                <li>No se ha especificado la vinculación laboral</li>
                                            @endif
                                            @if ($activity->service->required_infosource && !$activity->information_source)
                                                <li>No se ha especificado la fuente de información</li>
                                            @endif
                                        </ul>
                                    </div>
                                </div>
                            @endif
                        @endif
                        <!-- HISTORIAL -->
                        @include('activity.history', array('activity' => $activity))
                        <!-- CORRESPONDENCE -->
                        @if (count($activity->correspondencesGroups()) > 0)
                            <div class="title">
                                <i class="dropdown icon"></i> Correspondencia
                            </div>
                            <div class="content">
                                <table class="ui very compact very basic table">
                                    <thead>
                                    <tr>
                                        <th>Orden</th>
                                        <th>Acción</th>
                                        <th>Documentos</th>
                                        <th>Fecha de envio</th>
                                        <th>Destinatarios</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    @foreach($activity->correspondencesGroups() as $consecutive => $items)
                                        @foreach($items as $k => $c)
                                            <tr>
                                                <td>
                                                    <a href="{{secure_url('correspondencia/' . $consecutive)}}">{{$consecutive}}</a>
                                                </td>
                                                <td>{{$c[0]->activity_action ? $c[0]->activity_action->action->name : ''}}</td>
                                                <td>
                                                    @if ($c[0]->activity_action)
                                                        @foreach($c[0]->activity_action->documents as $d)
                                                            {{$DOCUMENTS[$d->name]}} <br/>
                                                        @endforeach
                                                    @endif
                                                </td>
                                                <td>{{$c[0]->correspondence->send_at ? $c[0]->correspondence->send_at->format('d-m-Y') : 'NO ENVIADO'}}</td>
                                                <td>
                                                    @if(Auth::user()->user_type=='ADM')
                                                        @foreach($activity->getSendTargets($c) as $a)
                                                            @if($activity->getSendTarget($c, $a) == null && $a)
                                                                <a style="color:#a7b7c3;"
                                                                   title="Agregar elemento {{App\CorrespondenceItem::$ENTITY_TYPES[$a]}}"
                                                                   href="{{secure_url('/correspondencia/correspondenceItem'.
            '/'.$c[0]->activity_action_id.
            '/'.base64_encode($c[0]->path).
            '/'.$a.
            '/'.$c[0]->correspondence_id)}}">
                                                                    +{{App\CorrespondenceItem::$ENTITY_TYPES[$a]}}
                                                                </a>
                                                                <br/>
                                                            @endif
                                                        @endforeach
                                                    @endif
                                                    @foreach($c as $ci)
                                                        <a href="{{secure_url('/correspondencia/item/' . $ci->id)}}">{{App\CorrespondenceItem::$ENTITY_TYPES[$ci->entity_type]}}</a>
                                                        <br/>
                                                    @endforeach
                                                </td>
                                            </tr>
                                        @endforeach
                                    @endforeach
                                    </tbody>
                                </table>
                            </div>
                        @endif
                        @if (session('success'))
                            <div class="ui positive message">
                                <i class="close icon"></i>
                                <div class="header">
                                    {{ session('success') }}
                                </div>
                            </div>
                        @endif
                        @if (session('error'))
                            <div class="ui negative message">
                                <i class="close icon"></i>
                                <div class="header">
                                    {{ session('error') }}
                                </div>
                            </div>
                        @endif

                </div>
            </div>
            <!-- FIELDS -->
            <div class="field" id="text" style="display: none;">
                <label>
                    %NAME%
                </label>
                <input name="%name%" type="text"/>
            </div>
            <div class="field wide four column" id="number" style="display: none;">
                <label>
                    %NAME%
                </label>
                <input name="%name%" type="number" step="0.01"/>
            </div>
            <div class="field" id="date" style="display: none;">
                <label>
                    %NAME%
                </label>
                <input name="%name%" type="text"/>
            </div>
            <div class="field" id="time" style="display: none;">
                <label>
                    %NAME%
                </label>
                <input name="%name%" type="text"/>
            </div>
            <div class="field" id="file" style="display: none;">
                <label>
                    %NAME%
                </label>
                <button type="button" class="ui basic mini secondary button"><i class="upload icon"></i> Cargar documento
                </button>
                <input style="display: none;" id="%id%" name="%id%" type="file">
                <input id="%id%_text" name="%name%" type="hidden"/>
                <p></p>
            </div>
            <div class="field" id="call" style="display: none;">
                <label>
                    %NAME%
                </label>
                <input name="%name%" type="text"/>
            </div>
            <div class="field" id="select" style="display: none;">
                <label>
                    %NAME%
                </label>
                <select name="%name%"></select>
            </div>
            <div class="field" id="multiple" style="display: none;">
                <label>
                    %NAME%
                </label>
                <select name="%name%" multiple></select>
            </div>
            <div class="field" id="email" style="display: none;">
                <label>
                    %NAME%
                </label>
                <input name="%name%" placeholder="Separados por coma (,)" type="email" multiple/>
            </div>
            {{--    @include('affiliate.affiliate_historical_items', array('affiliate' => $activity->affiliate, 'created_at' => $activity->created_at))--}}
            <!-- END FIELDS -->
            <style>
                .rectangle {
                    min-width: 110px;
                    height: 20px;
                    border-radius: 10px;
                    color: white;
                    display: grid;
                    place-items: center;
                    margin: 5px;
                }
            </style>
            <script>
                var DOCUMENTS = @json($DOCUMENTS);
            </script>
            <script type="text/javascript">
                validateSubmitButton("nueva_accion");
                $(".ui.dropdown").dropdown();
                var saveActivity = function (cb) {
                    var fields = {};
                    @if ($activity->service->fields)
                    @foreach($activity->service->fields as $f)
                    var value = $('input[name="fields[{{$f->id}}]"]').val();
                    fields['{{$f->id}}'] = value;
                    @if($f->id == 10 || $f->id == 13 || $f->id == 15)

                    var information_source = $("input[name=information_source]").val();
                    var auth_number = $("input[name=auth_number]").val();
                    var employment_id = $("input[name=employment_id]").val();

                    $.post("/api/activity/" + {{$activity->id}}, {
                        information_source: information_source,
                        auth_number: auth_number,
                        employment_id: employment_id,
                        fields: fields
                    }, cb);
                    @endif
                    @endforeach
                    @endif
                };
                var initF = 0;

                var makeUIDF = function (length) {
                    var text = "";
                    var possible = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";

                    for (var i = 0; i < length; i++)
                        text += possible.charAt(Math.floor(Math.random() * possible.length));

                    return text + new Date().getTime();
                };
                var onUploadF = function (err, data) {
                    var current = new Date().getTime();
                    $(this).parent().find("button").removeClass("blue");
                    if (err) {
                        $(this).parent().find("button").addClass("red");
                        $(this).parent().find("button").html("<i class=\"warning icon\"></i> ERROR");
                        return alert("There was an error uploading your photo: ", err.message);
                    }

                    $(this).parent().find("button").addClass("green");
                    $(this).parent().find("button").html("<i class=\"checkmark icon\"></i> OK");
                    $(this).parent().find("p").html("Completado en " + Math.round((current - initF) / 1000) + " seg.");
                    $(this).parent().find("input[type=hidden]").val(data.Key);
                };
                var onProgressF = function (progress) {
                    var current = new Date().getTime();
                    var speed = Math.floor((progress.loaded) / (current - initF));
                    var remaing = Math.round((progress.total - progress.loaded) / (speed * 1024));
                    var perc = Math.floor(progress.loaded / progress.total * 1000) / 10;
                    $(this).parent().find("button").html("<i class=\"wait icon\"></i> " + perc + " %");
                    if (remaing <= 120) {
                        $(this).parent().find("p").html("Faltan: &sim;" + remaing + " seg.<br /> (vel. &sim;" + speed + " KB/s)");
                    } else {
                        $(this).parent().find("p").html("Faltan: &sim;" + Math.round(remaing / 60) + " min.<br /> (vel. &sim;" + speed + " KB/s)");
                    }
                };

                $(document).ready(function () {
                    //cerrar mensaje de session
                    $('.message .close').on('click', function() {
                        $(this).closest('.message').transition('fade');
                    });

                    $(".ui.modal").modal();
                    $(".ui.modal .content").popup();
                    $(".basic.icon.button").popup();
                    // $('#historicalARL').modal('attach events', '#historicalBTNARL', 'show');
                    // $('#historicalAFP').modal('attach events', '#historicalBTNAFP', 'show');

                    $(".ui.checkbox").checkbox();
                    $("b").css("display", "block");
                    $(".ui.accordion .ui.grid .row").css("padding-top", 0);
                    $(".ui.accordion .ui.grid .column").css("padding-top", 0);
                    $(".ui.accordion").accordion({
                        exclusive: false
                    });
                    @foreach($activity->service->fields as $f)
                    @if(($activity->creation_source == 'INTEGRACIÓN' || $activity->creation_source == 'MIGRACIÓN') && in_array($f->id, [10, 13, 15]))
                    $('input[name="fields[{{$f->id}}]"]').prop("disabled", true);
                    @endif
                    @endforeach
                    @if($activity->service_id == 53 || $activity->service_id == 57 || $activity->service_id == 64)
                    $(".info-source-dropdown").addClass("disabled");
                    @endif
                    $(".datepicker").pickadate({
                        selectYears: true,
                        selectMonths: true,
                        min: new Date(),
                        formatSubmit: "yyyy-mm-dd"
                    });
                    $(".ui.dropdown").dropdown();

                    $("#action_form").form({
                        inline: true,
                        fields: {
                            action_id: "empty",
                            user_id: "empty",
                            description: "empty"
                        }
                    });

                    $("input[name=information_source]").change(function () {
                        $(this).parent(".ui.dropdown").addClass("loading");
                        saveActivity(function () {
                            $("input[name=information_source]").parent(".ui.dropdown").removeClass("loading");
                            location.reload();
                        });
                    });
                    $("input[name=auth_number]").change(function () {
                        $(this).parent(".ui.input").addClass("loading");
                        saveActivity(function () {
                            $("input[name=auth_number]").parent(".ui.input").removeClass("loading");
                        });
                    });
                    $("input[name=employment_id]").change(function () {
                        $(this).parent(".ui.dropdown").addClass("loading");
                        saveActivity(function () {
                            $("input[name=employment_id]").parent(".ui.dropdown").removeClass("loading");
                            location.reload();
                        });
                    });
                    $("input[name*=\"fields[\"]").change(function () {
                        $(this).parent().addClass("loading");
                        saveActivity(function () {
                            $("input[name*=\"fields[\"]").parent().removeClass("loading");
                            location.reload();
                        });
                    });
                    let creation_source = '{{$activity->creation_source}}';
                    $("#action").change(function () {
                        let tasks = 3;
                        $(this).parent(".ui.dropdown").addClass("loading");
                        const action_id = Number($(this).val());
                        $.getJSON('/api/service/{{$activity->id}}/action/' + $(this).val() + "/fields", function (fields) {
                            var form_fields = {
                                action_id: "empty",
                                user_id: "empty",
                                description: "empty"
                            };

                            $("#fields").empty();
                            for (var i = 0; i < fields.length; i++) {
                                var name = fields[i].name.toLowerCase().replace(new RegExp(" ", "g"), "_");
                                var $field = $("#" + fields[i].type).clone(true);
                                $field.removeAttr("id");
                                $("#fields").append($field);
                                if (creation_source !== 'MIGRACIÓN' && action_id == 546) {
                                    fields[i].required = false;
                                    $field.find("input").css("pointer-events", "none");
                                    $field.find("select").dropdown({className: {dropdown: 'ui dropdown disabled'}});
                                    $field.find(".datepicker").css("pointer-events", "none");
                                }
                                if (fields[i].required) {
                                    $field.addClass("required");
                                    form_fields["fields[" + name + "]"] = "empty";
                                }
                                $field.find("label").text(fields[i].name.toUpperCase());
                                $field.show();

                                if (fields[i].type == "file") {

                                    $field.find("input[type=file]").attr("name", "fields_" + name);
                                    $field.find("input[type=file]").attr("id", "fields_" + name);
                                    $field.find("input[type=hidden]").attr("id", "fields_" + name + "_text");
                                    $field.find("input[type=hidden]").attr("name", "fields[" + name + "]");

                                    $field.find("button").click(function () {
                                        $(this).parent().find("input[type=file]").click();
                                    });

                                    $field.find("input[type=file]").change(function () {
                                        $(this).parent().find("button").removeClass("green");
                                        $(this).parent().find("button").removeClass("red");
                                        $(this).parent().find("button").addClass("blue");
                                        var files = $(this).prop("files");
                                        if (!files.length) {
                                            return alert("Please choose a file to upload first.");
                                        }
                                        var file = files[0];
                                        var fileName = file.name;
                                        var fileNameExt = file.name.split(".").pop();
                                        var fileNameHash = makeUIDF(27);
                                        var albumPhotosKey = encodeURIComponent("action_files") + "/";

                                        var photoKey = albumPhotosKey + fileNameHash + "." + fileNameExt;
                                        var onUploadB = onUploadF.bind(this);
                                        var onProgressB = onProgressF.bind(this);

                                        initF = new Date().getTime();
                                        s3.upload({
                                            Key: photoKey,
                                            Body: file,
                                        }, {
                                            partSize: 20 * 1024 * 1024,
                                            queueSize: 1
                                        }, onUploadB).on('httpUploadProgress', onProgressB);
                                    });

                                } else if (fields[i].type == "select") {
                                    $field.find("select").attr("name", "fields[" + name + "]");

                                    var values = fields[i].values.split(",");
                                    for (var j in values) {
                                        $field.find("select").append("<option value=\"" + values[j].toUpperCase() + "\">" + values[j].toUpperCase() + "</option>");
                                    }

                                    $field.find("select").dropdown();
                                    $field.find("select").dropdown("clear");

                                } else if (fields[i].type == "multiple") {
                                    $field.find("select").attr("name", "fields[" + name + "][]");

                                    var values = fields[i].values.split(",");
                                    for (var j in values) {
                                        $field.find("select").append("<option value=\"" + values[j].toUpperCase() + "\">" + values[j].toUpperCase() + "</option>");
                                    }

                                    $field.find("select").dropdown();
                                    $field.find("select").dropdown("clear");

                                } else if (fields[i].type == "date") {
                                    var inputField = $field.find("input");

                                    if (fields[i].id == 272) {
                                        console.log("entra");
                                        inputField.prop("disabled", true);
                                        inputField.val("El cálculo se hará en automático");
                                    }
                                    if (fields[i].default_value) {
                                        $field.find("input").data("value", fields[i].default_value);
                                    }

                                    $field.find("input").attr("name", "fields[" + name + "]");

                                    $field.find("input").pickadate({
                                        selectYears: true,
                                        selectMonths: true,
                                        formatSubmit: "yyyy-mm-dd"
                                    });

                                } else if (fields[i].type == "time") {
                                    if (fields[i].default_value) {
                                        $field.find("input").data("value", fields[i].default_value);
                                    }

                                    $field.find("input").attr("name", "fields[" + name + "]");


                                    $field.find("input").pickatime({
                                        formatSubmit: "H:i:00",
                                        interval: 10,
                                        min: [7, 0],
                                        max: [19, 50]
                                    });

                                } else {
                                    if (fields[i].default_value) {
                                        $field.find("input").val(fields[i].default_value);
                                    }
                                    $field.find("input").attr("name", "fields[" + name + "]");

                                }

                            }

                            $("#action_form").form({
                                fields: form_fields
                            });

                            tasks--;
                            if (tasks == 0)
                                $("input[name=action_id]").parent(".ui.dropdown").removeClass("loading");
                        });

                        const actionSelected = $(this).val();
                        $.getJSON("/api/action/" + actionSelected + "/documents", function (documents) {
                            $("#action_documents").empty();
                            if ($.isEmptyObject(documents) && '{{ $activity->service_id }}' === "53" && [366, 368, 372, 379, 380, 478].includes(parseInt(actionSelected))) {
                                $.getJSON("/api/determination_it/" + '{{ $activity->id }}', function (determination_it_data) {
                                    var communication_to_emit = determination_it_data.communication_to_emit;
                                    console.log(communication_to_emit);
                                    var documentLabel = DOCUMENTS[communication_to_emit];
                                    if (documentLabel != undefined) {
                                        $("#action_documents").append('<a class="ui basic button grey" target="_blank" href="{{secure_url("servicio/{$activity->id}/docs")}}/' + communication_to_emit + "\"><i class=\"download icon\"></i> " + documentLabel + "<a>");
                                    } else {
                                        $("#action_documents").append("<p>No se encontró el documento para emitir.</p>");
                                    }
                                });
                            } else if (!$.isEmptyObject(documents)) {
                                for (var i in documents) {
                                    var documentLabel = DOCUMENTS[i] != undefined ? DOCUMENTS[i] : "";
                                    $("#action_documents").append('<a class="ui basic button grey" target="_blank" href="{{secure_url("servicio/{$activity->id}/docs")}}/' + i + "\"><i class=\"download icon\"></i> " + documentLabel + "<a>");
                                }
                            }

                            tasks--;
                            if (tasks == 0)
                                $("input[name=action_id]").parent(".ui.dropdown").removeClass("loading");
                        });

                        $.getJSON('/api/activity/{{$activity->id}}/action/' + $(this).val() + "/users", function (users) {
                            $("#users .menu").empty();
                            $("#users input").val("");

                            let service_id = {{ json_encode($activity->service_id == 57 || $activity->service_id == 64) }};
                            let user_id = {{ json_encode($activity->user_id) }};
                            for (var i = 0; i < users.length; i++) {
                                $("#users .menu").append("<div class=\"item\" data-value=\"" + users[i].id + "\"><div class=\"description\">(" + users[i].area.name + ")</div><div class=\"text\">" + users[i].full_name + "</div></div>");
                                if (service_id) {
                                    if (users[i].id == user_id) {
                                        $("#users input").val(1);
                                    }
                                } else {
                                    if (users[i].id == user_id) {
                                        $("#users input").val(users[i].id);
                                    }
                                }
                            }

                            $("#users").dropdown({
                                sortSelect: true,
                                fullTextSearch: "exact",
                                forceSelection: false
                            });

                            tasks--;
                            if (tasks == 0)
                                $("input[name=action_id]").parent(".ui.dropdown").removeClass("loading");
                        });

                        // $.getJSON("/api/action/" + $(this).val(), function(action) {
                        //
                        //   if (action.auto_alert) {
                        //     $("input[name='alert_date']").pickadate("picker").set("select", action.auto_alert_value, { format: "yyyy-mm-dd" });
                        //   }
                        //
                        //   if (action.id == 239) { //REPORT_REQUEST_VAL_FOLLOWING
                        //     setTimeout(function() {
                        //       $("input[name=\"fields[dias_para_nueva_valoracion]\"]").change(function() {
                        //         var days = parseInt($(this).val());
                        //         var date_string = $("input[name=\"fields[fecha_ultima_valoracion]_submit\"]").val();
                        //         var parts = date_string.split("-");
                        //         var mydate = new Date(parts[0], parts[1] - 1, parts[2]);
                        //         mydate.setDate(mydate.getDate() + days);
                        //         $("input[name='alert_date']").pickadate("picker").set("select", mydate, { format: "yyyy-mm-dd" });
                        //         $("input[name=\"fields[fecha_solicitud_nueva_valoracion]\"]").pickadate("picker").set("select", mydate, { format: "yyyy-mm-dd" });
                        //       });
                        //     });
                        //   }
                        //
                        //
                        //   tasks--;
                        //   if (tasks == 0)
                        //     $("input[name=action_id]").parent(".ui.dropdown").removeClass("loading");
                        // });
                    });
                });
            </script>
            <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/locale/es.min.js"></script>

            <script>

                // Obtener la fecha desde Laravel
                var createdAt = '{{ $activity->created_at }}';

                // Formatear la fecha en el formato deseado
                var fechaFormateada = moment(createdAt).format('dddd D [de] MMMM [de] YYYY');

                // Convertir la primera letra en mayúscula
                var fechaConMayuscula = fechaFormateada.charAt(0).toUpperCase() + fechaFormateada.slice(1);

                // Insertar la fecha formateada en el elemento con id "fecha-inicio"
                document.getElementById('fecha-inicio').innerText = fechaConMayuscula;
            </script>

            <script>
                document.addEventListener("DOMContentLoaded", function () {
                    // Establecer el idioma a español
                    moment.locale('es');

                    // Seleccionar todos los elementos con clase "actividad"
                    const actividades = document.querySelectorAll('.actividad');

                    actividades.forEach(function (actividad) {
                        // Obtener la fecha desde el atributo data-fecha
                        let createdAt = actividad.getAttribute('data-fecha');

                        // Formatear la fecha con Moment.js
                        let fechaFormateada = moment(createdAt).format('dddd D [de] MMMM [de] YYYY');

                        // Capitalizar la primera letra de la fecha formateada
                        fechaFormateada = fechaFormateada.charAt(0).toUpperCase() + fechaFormateada.slice(1);

                        // Usar querySelector para seleccionar el primer elemento con clase "fecha-creacion"
                        actividad.querySelector('.fecha-creacion').innerText = `${fechaFormateada}`;
                    });
                });
            </script>

            <script>
                document.addEventListener("DOMContentLoaded", function () {
                    // Establecer el idioma a español
                    moment.locale('es');

                    // Seleccionar todos los elementos con clase "actividad"
                    const items = document.querySelectorAll('.itemFecha');

                    items.forEach(function (item) {
                        // Obtener la fecha desde el atributo data-fecha
                        let createdAt = item.getAttribute('data-fecha');

                        // Formatear la fecha con Moment.js
                        let fechaFormateada = moment(createdAt).format('dddd D [de] MMMM [de] YYYY');

                        // Capitalizar la primera letra de la fecha formateada
                        fechaFormateada = fechaFormateada.charAt(0).toUpperCase() + fechaFormateada.slice(1);

                        // Usar querySelector para seleccionar el primer elemento con clase "fecha-formatear"
                        item.querySelector('.fecha-formatear').innerText = `${fechaFormateada}`;
                    });
                });
            </script>
@endsection
