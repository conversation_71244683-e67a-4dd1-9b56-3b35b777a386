@extends('layouts.main')

@section('menu')
    @parent
@endsection

@section('content')
    <div class="ui basic segment">
        <h1>Cargue masivo HISTORIAL DE ACCION PCL</h1>
        <p>Cargue terminado</p>
        @if($fails)
            <p>Cantidad de lineas cargadas correctamente: <b>{{$completed}}</b>. Los siguientes archivos no se pudieron
                asociar:</p>
            <div class="ui list">
                <table>
                    <tr>
                        <th>Linea en el archivo EXCEL</th>
                        <th>Error</th>
                    </tr>
                    @foreach($fails as $fail)
                        <tr>
                            <th>{{$fail['row']}}</th>
                            <th>{{$fail['numero_de_documento']}}</th>
                        </tr>
                    @endforeach
                </table>
            </div>
        @else
            <p>Cantidad de lineas cargadas correctamente: <b>{{$completed}}</b>.</p>
        @endif
        @isset($list)
            @if(count($list) > 0)
                <p>La lista de los servicios Ajustados es la siguiente:</p>
                <div class="ui list">
                    <table>
                        <tr>
                            <th>Linea</th>
                            <th>Id Servicio</th>
                            <th>Radicado Bizagi</th>
                        </tr>
                        @foreach($list as $item)
                            <tr>
                                <th>{{$item['row']}}</th>
                                <th>{{$item['service_id']}}</th>
                                <th>{{$item['radicado_bizagi']}}</th>
                            </tr>
                        @endforeach
                    </table>
                </div>
            @endif
        @endisset
        <p>Para continuar realizando cargues masivos, haga clic en el link del menu llamado <b>CARGUE</b></p>
    </div>
    <style>
        table,
        th,
        td {
            border: 0.25px solid black;
        }

        th,
        td {
            padding: 2.5px;
            text-align: justify;
        }

        td[colspan=

        16
        ]
        {
            text-align: center
        ;
        }
        table {
            border-collapse: collapse;
            width: 50%;
        }

        th {
            text-align: center;
        }
    </style>
@endsection