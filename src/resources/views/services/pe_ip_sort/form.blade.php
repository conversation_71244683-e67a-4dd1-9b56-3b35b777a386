@extends('layouts.main')

@section('title', 'PE - Incapacidad permanente SORT')

@section('menu')
    @parent
@endsection

@section('content')
    <div class="ui basic segment">
        <h1 class="ui header">
            PE - Incapacidad permanente SORT
            <div class="sub header">Campos con <span style="color: red;" class="required">*</span> obligatorios.</div>
        </h1>
        <div class="ui secondary segment">
            <div class="ui three columns grid">
                <div class="column">
                    <b>Identificación:</b> {{ $activity->affiliate->doc_type }} {{ $activity->affiliate->doc_number }}
                </div>
                <div class="column">
                    <b>Nombre:</b>
                    <a href="{{ secure_url('afiliado/' . $activity->affiliate_id) }}">
                        {{ mb_convert_case($activity->affiliate->full_name, MB_CASE_TITLE, "UTF-8") }}
                    </a>
                </div>
                <div class="column"><b>Actividad:</b> <a
                        href="{{ secure_url('servicio/' . $activity->id) }}">{{ $activity->service->name }}</a>
                </div>
            </div>
        </div>

        <form autocomplete="off" action="{{ secure_url("/servicio/{$id}/pe_ip_sort/save") }}" id="peip_form" method="post" class="ui small form">
            <div class="ui styled fluid accordion">
                {{csrf_field()}}
                @include('services.pe_ip_sort.steps.general_information')
                @include('services.pe_ip_sort.steps.details_permanent_disability')
                @include('services.pe_ip_sort.steps.information_payment')
                @include('services.pe_ip_sort.steps.medical_audit')
            </div>
            <div class="ui basic segment">
                <div class="fields">
                    <div class="six wide field">
                        <button class="ui primary button"  id="update_save" style="width: 40%">
                            <i class="save icon"></i> Guardar
                        </button>
                        <a href="{{ secure_url('/servicio/' . $activity->id) }}" class="ui secondary button"><i
                                class="arrow left icon"></i> Volver a la actividad</a>

                    </div>
                </div>
            </div>

        </form>

        <div class="alert warning" id="warningAlert" style="display:none;"></div>
    </div>
    <style>
        .alert {
            color: #721c24;
            padding: 15px;
            margin: 50px;
            border: 1px solid #f5c6cb;
            border-radius: 5px;
            max-height: 200px;
            overflow-y: auto;
            display: none;
        }
        .none-transform {
            text-transform: none !important;
        }
        .error {
            border-color: rgba(252, 57, 57, 0.97) !important;
        }

        .item-label {
            font-weight: bold;
            margin-right: 10px;
        }

        .item-value {
            font-size: 1em;
            color: #333;
        }

        .ui.grid {
            margin-top: -1rem;
            margin-bottom: -1rem;
            margin-right: -1rem;
        }

        /* .ui.grid .column {
            padding: 0.5rem 1rem !important;
        } */

        .ui.accordion .title {
            text-transform: uppercase;
        }

        .ui.styled.accordion .accordion .content,
        .ui.styled.accordion .content {
            margin: 0;
            padding: .5em 1em .3em;
        }

        .disability .item,
        .general-info .item,
        .medical_audit .item,
        .financial_audit .item,
        .technical_audit .item {
            display: grid !important;
            grid-gap: 12px !important;
        }

        .disability .column,
        .general-info .column,
        .medical_audit .column,
        .financial_audit .column,
        .technical_audit .column {
            padding-top: 0px !important;
            padding-left: 1rem !important;
        }
    </style>
    <script>
        $(document).ready(function() {

            $('.ui.dropdown').dropdown();
            $('.timepicker').pickatime();

            $('.ui.accordion').accordion({
                exclusive: false
            });
            $('.datepicker').pickadate({
                formatSubmit: 'yyyy-mm-dd',
                format: 'yyyy-mm-dd',
                selectMonths: true,
                selectYears: 100,
            });

           /* $('#peip_form').on('submit', function(e) {
                e.preventDefault(); // Evitar el envío del formulario de inmediato

                let receiptType = $('#receipt_type').val();

                if (!receiptType) {
                    $('#error-message').html('Por favor, selecciona una calificación.').show();
                    return false;
                }

                let percentInput = document.getElementById('casedata_percent');

                // Eliminar el símbolo de porcentaje
                percentInput.value = percentInput.value.replace('%', '').trim();

                // Verificar si el valor es un número válido
                let numValue = parseFloat(percentInput.value);
                if (isNaN(numValue) || numValue < 0 || numValue > 100) {
                    alert("Por favor, ingrese un porcentaje válido entre 0 y 100.");
                    return; // Salir si no es válido
                }

                // Si todas las validaciones son correctas, enviar el formulario
                this.submit(); // Enviar el formulario

            });*/
            $('#peip_form').on('submit', function(event) {
                loadingMain(true);
                event.preventDefault(); // Prevenir el envío del formulario

                // Recolectar datos del formulario
                var formData = $(this).serialize(); // Recolectar los datos del formulario en formato URL-encoded

                $.ajax({
                    url: "{{ secure_url("/servicio/{$id}/pe_ip_sort/save") }}", // Ruta a la que se enviarán los datos
                    type: 'POST', // Método HTTP
                    data: formData, // Datos del formulario
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content') // Incluir el token CSRF si es necesario
                    },
                    success: function(response) {
                        // Manejar la respuesta exitosa
                        if (response.success) {
                            Swal.fire({
                                icon: 'success',
                                title: 'Éxito',
                                text: response.message,
                                confirmButtonText: 'Aceptar'
                            }).then(() => {
                                // Recargar la página después de mostrar el mensaje de éxito
                                location.reload();
                            });
                            $('#warningAlert').hide(); // Limpiar el mensaje de advertencia si existe
                        } else {
                            loadingMain(false);
                            // Mostrar mensajes de advertencia en el div
                            var errorMessage = '';
                            $.each(response.errors, function(field, message) {
                                errorMessage += message.join('<br>') + '<br>'; // Acumular mensajes de error (para arrays)
                            });
                            $('#warningAlert').html(errorMessage).show(); // Mostrar todos los errores
                        }
                    },
                    error: function(xhr) {
                        loadingMain(false);
                        // Manejar errores del servidor
                        let errorMessage = 'Error en la solicitud: ' + xhr.responseText;
                        if (xhr.responseJSON && xhr.responseJSON.errors) {
                            // Mostrar errores específicos del validador
                            errorMessage = '';
                            $.each(xhr.responseJSON.errors, function(field, messages) {
                                errorMessage += messages.join(', ') + '<br>'; // Acumular mensajes de error
                            });
                        }
                        // Mostrar el mensaje de error en el div
                        $('#warningAlert').html(errorMessage).show();
                    }
                });
            });
        });




    </script>

@endsection
