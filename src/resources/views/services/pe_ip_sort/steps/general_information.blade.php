
    <div class="title none-transform"><i class="dropdown icon"></i> Información general <span style="color: red;" class="required">*</span></div>
    <div class="content title_name-class" style="margin: 0 !important; padding: .5em 1em 1.5em !important;">
        <div class="styled fluid accordion transition visible" style="display: block !important;">
            <div class="title none-transform"><i class="dropdown icon"></i> Datos del afiliado <span style="color: red;" class="required">*</span></div>
            <div class="content" style="padding-bottom: 1.5em;">
                <div class="ui form small clearing transition hidden">
                    <div class="ui three column grid">
                        <div class="column">
                            <div class="ui list">
                                <div class="required field disabled-fields">
                                    <label class="item-label">Tipo identificación</label>
                                    <select name="affiliate_doc_type_display" id="affiliate_doc_type_display" class="ui dropdown disabled-select" disabled>
                                        <option value="{{ $activity->affiliate->doc_type ?? 'Seleccionar'}}"></option>
                                        @foreach($DOC_TYPES as $k => $v)
                                            <option class="item" value="{{$k}}">{{$v}}</option>
                                        @endforeach
                                    </select>
                                    <!-- Campo oculto para enviar el valor seleccionado -->
                                    <input type="hidden" name="affiliate_doc_type" id="affiliate_doc_type" value="{{ $activity->affiliate->doc_type ?? '' }}">
                                </div>
                            </div>
                        </div>
                        <div class="column">
                            <div class="ui list">
                                <div class="required field disabled-fields">
                                    <label for="affiliate_doc_number" class="item-label">Número de identificación</label>
                                    <input type="text" id="affiliate_doc_number" name="affiliate_doc_number" value="{{ $activity->affiliate->doc_number }}" readonly>
                                </div>
                            </div>
                        </div>
                        <div class="column">
                            <div class="ui list">
                                <div class="required field disabled-fields">
                                    <label class="item-label">Nombre del afiliado</label>
                                    <input type="text" id="affiliate_name" name="affiliate_name" value="{{ mb_convert_case($activity->affiliate->full_name, MB_CASE_TITLE, 'UTF-8') }}" readonly>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="ui three column grid ">
                        <div class="column">
                            <div class="required field">
                                <label for="affiliate_email">Correo electrónico</label>
                                <div>
                                    <input type="email"
                                           id="affiliate_email"
                                           name="affiliate_email"
                                           onchange="validateEmail()"
                                           placeholder="Correo electrónico de afiliado"
                                           class="minus_font"
                                           value="{{ $activity->affiliate->email }}">
                                    <span id="email_error" style="color: red; display: none;">Por favor, ingrese un correo electrónico válido.</span>
                                </div>
                            </div>
                        </div>
                        <div class="column">
                            <div class="ui list">
                                <div class="field">
                                    <label for="affiliate_social_box" class="item-label">Caja Costarricense de Seguro Social</label>
                                    <input type="text" id="affiliate_social_box" name="affiliate_social_box" value="{{ $peIpSort->affiliate_social_box ?? '' }}">
                                </div>
                            </div>
                        </div>
                        <div class="column">
                            <div class="ui list">
                                <div class="required field disabled-fields ">
                                    <label for="affiliate_cellphone" class="item-label ">Celular</label>
                                    <input type="text" id="affiliate_cellphone" name="affiliate_cellphone" value="{{ $activity->affiliate->phone }}">
                                </div>
                            </div>

                        </div>
                    </div>
                    <div class="ui three column grid">
                        <div class="column">
                            <div class="ui list">
                                <div class="field">
                                    <label for="affiliate_communic_email" class="item-label">¿Autoriza comunicación por correo electrónico?</label>
                                    <select id="affiliate_communic_email" name="affiliate_communic_email" class="ui dropdown">
                                        <option value="{{ $peIpSort->affiliate_communic_email  ?? 'Seleccionar'}}"></option>
                                        <option value="1">Si</option>
                                        <option value="0">No</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="title none-transform">
                <i class="dropdown icon"></i> Datos del caso <span style="color: red;" class="required">*</span>
            </div>
            <div class="content">
                <div class="ui form small clearing transition hidden"> 
                    <div class="ui five column grid">
                        <div class="column">

{{--                            <div class="required field">--}}
{{--                                <label for="casedata_num" class="item-label "># Caso</label>--}}
{{--                                <input type="text" id="casedata_num" name="casedata_num" class="only-numbers" value="{{ $peIpSort->casedata_num ?? '' }}" >--}}
{{--                            </div>--}}

                            <div class="required field">
                                <label class="item-label" for="case"># caso:{{$peIpSort->casedata_num }}</label>
                                <select id="casedata_num" name="casedata_num" class="ui dropdown {{isset($peIpSort->casedata_num) ? 'disabled-select' : '' }} "  {{isset($peIpSort->casedata_num) ? 'disabled' : '' }} >
                                    <option value="" disabled {{ !isset($peIpSort->casedata_num) ? 'selected' : '' }}>Seleccione</option>

                                    @foreach($gisSorts as $gis)
                                        <option value="{{ $gis->id }}" {{ isset($peIpSort->casedata_num) && $gis->id == $peIpSort->casedata_num ? 'selected' : '' }}>
                                            {{ $gis->formatCaseNumber() }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>

                            @if(isset($peIpSort->casedata_num))
                                <input type="hidden" name="casedata_num" value="{{ $peIpSort->casedata_num ?? '' }}">
                            @endif



                            <div class="required field">
                                <label for="casedata_date_classif">Fecha de calificación MNK</label>
                                <input id="format_date" type="text"
                                       name="casedata_date_classif" class="datepicker"
                                       placeholder="dd-mm-yyyy"
                                       value="{{ isset($peIpSort) && isset($peIpSort->casedata_date_classif) ? ucfirst(strftime('%A %e de %B del %Y', strtotime($peIpSort->casedata_date_classif ))): ''}}"
                                >
                            </div>
                        </div>
                        <div class="column">
                            <div class="required field">
                                <label for="casedata_date_accident">Fecha del accidente </label>
                                <input id="format_date" type="text"
                                       name="casedata_date_accident" class="datepicker"
                                       placeholder="dd-mm-yyyy"
                                       value="{{ isset($peIpSort) && isset($peIpSort->casedata_date_accident) ? ucfirst(strftime('%A %e de %B del %Y', strtotime($peIpSort->casedata_date_accident))) : '' }}"
                                >
                            </div>
                            <div class="required field">
                                <label for="casedata_date_medical_dis" class="item-label">Fecha de mejoría médica máxima (alta médica)</label>
                                <input id="format_date" type="text"
                                       name="casedata_date_medical_dis" class="datepicker"
                                       placeholder="dd-mm-yyyy"
                                       value="{{isset($peIpSort) && isset($peIpSort->casedata_date_medical_dis) ? ucfirst(strftime('%A %e de %B del %Y', strtotime($peIpSort->casedata_date_medical_dis))): ''}}"
                                >
                            </div>
                        </div>
                        <div class="column">
                            <div class="required field">
                                <label for="casedata_time_accident" style="width: 175px;">Hora accidente</label>
                                <div class="ui left icon input">
                                    <input type="time" placeholder="" id="casedata_time_accident" name="casedata_time_accident" value="{{ $peIpSort->casedata_time_accident ?? '' }}">
                                </div>
                            </div>
                            <div class="required field">
                                <label for="casedata_percent" class="item-label">Porcentaje de pérdida de capacidad general</label>
                                <input type="text" id="casedata_percent" name="casedata_percent" value="{{ $peIpSort->casedata_percent ?? '' }}" placeholder="0 - 100%">
                                <span id="percent_error" style="color: red; display: none;">El valor debe estar entre 0 y 100.</span>
                            </div>
                        </div>
                        <div class="column">
                            <div class="required field">
                                <label for="entity_classification" class="item-label">Entidad que emitió la calificación</label>
                                <input type="text" id="entity_classification" name="entity_classification" value="{{ $peIpSort->entity_classification  ?? ''}}" >
                            </div>
                            <div class="required field disabled-fields">
                                <label for="casedata_type_disability" class="item-label">Tipo de incapacidad permanente</label>
                                <input type="text" id="casedata_type_disability" name="casedata_type_disability" value="{{ $peIpSort->casedata_type_disability ?? '' }}" readonly>
                            </div>
                        </div>
                        <div class="column">
                            <div class="required field">
                                <label for="casedata_dictamen" class="item-label "># de dictamen o calificación</label>
                                <input type="text" id="casedata_dictamen" name="casedata_dictamen" class="only-numbers" value="{{ $peIpSort->casedata_dictamen ?? '' }}">
                            </div>
                            <div class="required field">
                                <label for="casedata_requires_assistance" class="item-label">Requiere asistencia de otra persona para realizar los actos esenciales de la vida</label>
                                <select id="casedata_requires_assistance" name="casedata_requires_assistance" class="ui dropdown">
                                    <option value="" disabled {{ !isset($peIpSort->casedata_requires_assistance) ? 'selected' : '' }}>Seleccione</option>
                                    <option value="1" {{ (isset($peIpSort->casedata_requires_assistance) && $peIpSort->casedata_requires_assistance == '1') ? 'selected' : '' }}>Si</option>
                                    <option value="0" {{ (isset($peIpSort->casedata_requires_assistance) && $peIpSort->casedata_requires_assistance == '0') ? 'selected' : '' }}>No</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <br>
                </div>
                <div class="container" id="formContainer">
                    @php $first = true; @endphp <!-- Variable para verificar el primer registro -->
                    @if($caseDx->isEmpty())
                        <div class="ui five column grid form-block" id="formGrid">
                            <div class="column">
                                <div class="required field">
                                    <label for="casedata_classif">Calificación</label>
                                    <select name="casedata_classif[]" class="ui dropdown">
                                        <option value="" disabled selected>Seleccione</option>
                                        <option value="1">Principal</option>
                                        <option value="2">Secundario</option>
                                    </select>
                                </div>
                            </div>

                            <div class="column">
                                <div class="required field">
                                    <label for="casedata_code_cie">Código CIE 10</label>
                                    <div class="ui search code">
                                        <div class="ui icon input">
                                            <input class="prompt" name="diagnostics[cod][]" type="text" value="" autocomplete="off">
                                            <i class="search icon"></i>
                                        </div>
                                        <div class="results"></div>
                                    </div>
                                </div>
                            </div>

                            <div class="column">
                                <div class="required field disabled-fields">
                                    <label for="casedata_diagnosis">Nombre del diagnóstico</label>
                                    <input type="text" name="casedata_diagnosis[]" class="" value="" readonly>
                                </div>
                            </div>

                            <div class="column">
                                <div class="required field">
                                    <label for="casedata_laterality">Lateralidad</label>
                                    <select name="casedata_laterality[]" class="ui dropdown">
                                        <option value="" disabled selected>Seleccione</option>
                                        <option value="Izquierda">Izquierda</option>
                                        <option value="Derecha">Derecha</option>
                                        <option value="Bilateral">Bilateral</option>
                                    </select>
                                </div>
                            </div>

                            <div class="column">
                                <div style="margin-top: 1rem;">
                                    <button type="button" id="addButton" class="ui basic small icon blue button">
                                        <i class="add icon"></i> Agregar Dx
                                    </button>
                                </div>
                            </div>
                        </div>
                    @else
                        @foreach($caseDx as $diagnostic)
                            <hr class="ui divider"> <!-- Línea horizontal antes del bloque -->
                            <div class="ui five column grid form-block" id="formGrid">
                                <div class="column"  style="display: none;">
                                    <div class="field">
                                        <label for="id_dx">Id Dx</label>
                                        <input class="prompt" name="id_dx[]" type="text" value="{{$diagnostic->id}}" autocomplete="off">
                                    </div>
                                </div>
                                <div class="column">
                                    <div class="required field">
                                        <label for="casedata_classif">Calificación</label>
                                        <select name="casedata_classif[]" class="ui dropdown">
                                            <option value="" disabled {{ is_null($diagnostic) || $diagnostic->casedata_classif == null ? 'selected' : '' }}>Seleccione</option>
                                            <option value="1" {{ !is_null($diagnostic) && $diagnostic->casedata_classif == 1 ? 'selected' : '' }}>Principal</option>
                                            <option value="2" {{ !is_null($diagnostic) && $diagnostic->casedata_classif == 2 ? 'selected' : '' }}>Secundario</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="column">
                                    <div class="required field">
                                        <label for="casedata_code_cie">Código CIE 10</label>
                                        <div class="ui search code">
                                            <div class="ui icon input">
                                                <input class="prompt" name="diagnostics[cod][]" type="text" value="{{ is_null($diagnostic) ? '' : $diagnostic->casedata_code_cie }}" autocomplete="off">
                                                <i class="search icon"></i>
                                            </div>
                                            <div class="results"></div>
                                        </div>
                                    </div>
                                </div>

                                <div class="column">
                                    <div class="required field disabled-fields">
                                        <label for="casedata_diagnosis">Nombre del diagnóstico</label>
                                        <input type="text" name="casedata_diagnosis[]" class="" value="{{ is_null($diagnostic) ? '' : $diagnostic->casedata_diagnosis }}" readonly>
                                    </div>
                                </div>

                                <div class="column">
                                    <div class="required field">
                                        <label for="casedata_laterality">Lateralidad</label>
                                        <select name="casedata_laterality[]" class="ui dropdown">
                                            <option value="" disabled {{ is_null($diagnostic) || $diagnostic->casedata_laterality == null ? 'selected' : '' }}>Seleccione</option>
                                            <option value="Izquierda" {{ !is_null($diagnostic) && $diagnostic->casedata_laterality == 'Izquierda' ? 'selected' : '' }}>Izquierda</option>
                                            <option value="Derecha" {{ !is_null($diagnostic) && $diagnostic->casedata_laterality == 'Derecha' ? 'selected' : '' }}>Derecha</option>
                                            <option value="Bilateral" {{ !is_null($diagnostic) && $diagnostic->casedata_laterality == 'Bilateral' ? 'selected' : '' }}>Bilateral</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="column">
                                    <div style="margin-top: 1rem;">
                                        @if($first) <!-- Mostrar botón Agregar solo en el primer registro -->
                                        <button type="button" id="addButton" class="ui basic small icon blue button">
                                            <i class="add icon"></i> Agregar Dx
                                        </button>
                                        @php $first = false; @endphp
                                        @else <!-- Mostrar botón Eliminar en los registros que vienen de la base de datos -->
                                        <div class="ui button-container" style="display: inline-block; position: relative;">
                                            <button type="button" class="ui basic small icon red button deleteButton" data-id="{{ $diagnostic->id }}">
                                                <i class="trash icon"></i> Eliminar
                                            </button>
                                            <div style="display: none" class="ui active centered inline loader" id="loading-{{ $diagnostic->id }}"></div>
                                        </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    @endif
                </div>
            </div>
        </div>

    </div>

<style>
    input[type="text"] {
        text-transform: none  !important;
    }
    .disabled-fields input[readonly]{
        background-color: #f3f4f5 !important; /* Color gris */
        color: #333 !important;
        border-color: #ddd !important;
    }
    .disabled-fields select[disabled] {
        background-color: #f3f4f5 !important; /* Color gris claro */
        color: #333 !important; /* Color del texto */
        border-color: #ddd !important; /* Color del borde */
        opacity: 1 !important;
    }
    .disabled-select {
        background-color: #f3f4f5 !important;
        color: #333 !important;
        border: 1px solid #ddd !important;
        opacity: 1 !important;
    }

    #casedata_diagnosis {
        text-transform: capitalize;
    }
    #formContainer{
        padding-block-end: 20px;
    }
    .ui.search .results {
        max-height: 200px;
        overflow-y: auto;
        overflow-x: hidden;
    }

</style>
<script type="text/javascript">

    //Función para validar Email, en el momento no está siendo utilizada, ya que lo trae del afiliado
    function validateEmail() {
        var emailField = document.getElementById('affiliate_email');
        var emailError = document.getElementById('email_error');
        var emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (emailField.value === '' || !emailPattern.test(emailField.value)) {
            emailError.style.display = 'inline';
            emailField.classList.add('error');
        } else {
            emailError.style.display = 'none';
            emailField.classList.remove('error');
        }
    }


    // Función para cambiar el formato de fecha selecciona a tipo oración
    // Ejemplo: Miércoles 09 de octubre del 2024
    $('.datepicker').pickadate({
        selectYears: 100,
        format: 'dd/mm/yyyy', // Formato de la fecha (DD/MM/YYYY)
        formatSubmit: 'yyyy-mm-dd', // Formato al enviar la fecha
        changeMonth: true,       // Habilitar selección de mes
        changeYear: true,        // Habilitar selección de año
        selectMonths: true,      // Muestra un desplegable de meses

        onClose: function() {
            // Obtener la fecha seleccionada
            var selectedDate = this.get('select', 'dd/mm/yyyy');
            if (selectedDate) {
                // Dividir la fecha seleccionada en partes
                var dateParts = selectedDate.split('/');
                var day = parseInt(dateParts[0], 10);
                var month = parseInt(dateParts[1], 10) - 1;
                var year = parseInt(dateParts[2], 10);

                var date = new Date(year, month, day);

                // Array con los nombres de los días de la semana
                var days = ['Domingo', 'Lunes', 'Martes', 'Miércoles', 'Jueves', 'Viernes', 'Sábado'];
                // Array con los nombres de los meses
                var months = ['Enero', 'Febrero', 'Marzo', 'Abril', 'Mayo', 'Junio', 'Julio', 'Agosto', 'Septiembre', 'Octubre', 'Noviembre', 'Diciembre'];

                var formattedDate = days[date.getDay()] + ' ' + date.getDate() + ' de ' + months[date.getMonth()] + ' de ' + date.getFullYear();

                $(this.$node).val(formattedDate);
            }
        }
    });

    $(document).ready(function () {
        // Inicializar dropdowns y la funcionalidad de búsqueda
        $('.ui.dropdown').dropdown();
        initSearch($('.ui.search.code'));

        // Método para clonar/duplicar/agregar nuevo caso
        $('#addButton').on('click', function () {
            var $formBlock = $('#formGrid').clone();

            // Limpiar campos y eliminar el botón "Agregar Dx" del nuevo bloque
            $formBlock.find('input').val('');
            $formBlock.find('select').dropdown('clear');
            $formBlock.find('#addButton').remove();

            // Cambiar los nombres de los campos para que sean únicos
            var index = $('.form-block').length; // Contar cuántos bloques existen
            $formBlock.find('input[name="diagnostics[cod][]"]').attr('name', `diagnostics[cod][${index}]`);
            $formBlock.find('input[name="casedata_diagnosis[]"]')
                .attr('name', `casedata_diagnosis[${index}]`)
                .attr('readonly', true); // Hacer el campo de diagnóstico solo lectura
            $formBlock.find('select[name="casedata_classif[]"]').attr('name', `casedata_classif[${index}]`);
            $formBlock.find('select[name="casedata_laterality[]"]').attr('name', `casedata_laterality[${index}]`);

            // Agregar botón de eliminar en el nuevo bloque
            $formBlock.find('.column:last-child').append(`
            <button type="button" class="ui basic small icon red button removeButton" style="margin-top: 1rem;">
                <i class="trash icon"></i> Eliminar
            </button>
        `);

            // Agregar una clase única para cada bloque
            $formBlock.addClass('form-block');

            // Insertar la línea horizontal antes del nuevo bloque
            $('#formContainer').append('<hr class="ui divider">'); // Línea horizontal usando Semantic UI

            // Insertar el nuevo bloque en el contenedor
            $('#formContainer').append($formBlock);

            // Inicializar dropdown y búsqueda en el nuevo bloque
            $formBlock.find('.ui.dropdown').dropdown();
            initSearch($formBlock.find('.ui.search.code'));
        });

        // Función para inicializar el campo de búsqueda para el CIE 10
        function initSearch($searchField) {
            $.getJSON('/js/cie10.json?v=1.0', function (json) {
                $searchField.search({
                    source: json,
                    fields: {
                        title: 'COD',
                        description: 'DESCRIPTION'
                    },
                    searchFields: ['COD', 'DESCRIPTION'],
                    regExp: {
                        escape: /[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,
                        beginsWith: ''
                    },
                    showNoResults: true,
                    maxResults: 250,
                    searchFullText: false,
                    error: {
                        noResults: 'No se encontraron resultados para tu búsqueda.'
                    },
                    onSelect: function (result, response) {
                        // Actualiza el campo de texto del diagnóstico en el bloque correcto
                        var $closestBlock = $(this).closest('.form-block'); // Solo buscar en el bloque más cercano

                        // Verificar que el campo de diagnóstico existe en este bloque
                        if ($closestBlock.length) {
                            // Actualizar solo el campo de diagnóstico correspondiente
                            $closestBlock.find('input[name^="casedata_diagnosis"]').val(result.DESCRIPTION.charAt(0).toUpperCase() + result.DESCRIPTION.slice(1).toLowerCase());
                        }
                    }
                });
            });
        }

        // Eliminar fila de formulario
        $(document).on('click', '.removeButton', function () {
            var $closestBlock = $(this).closest('.form-block');
            $closestBlock.prev('hr').remove(); // Eliminar la línea horizontal anterior
            $closestBlock.remove();
        });
    });




    const percentInput = document.getElementById('casedata_percent');
    const errorElement = document.getElementById('percent_error');


    percentInput.addEventListener('input', function () {
        // Eliminar el símbolo de porcentaje y espacios
        let value = this.value.replace('%', '').trim();
        // Reemplazar la coma por un punto para el parseo
        let numValue = parseFloat(value.replace(',', '.'));

        // Validar si el valor es un número entre 0 y 100
        if (!isNaN(numValue) && numValue >= 0 && numValue <= 100) {
            errorElement.style.display = 'none';
        } else {
            errorElement.style.display = 'inline';
        }

        // Actualizar el valor mostrado sin el símbolo de porcentaje
        this.value = value;
    });

    percentInput.addEventListener('blur', function () {
        // Eliminar el símbolo de porcentaje y espacios
        let value = this.value.replace('%', '').trim();
        // Reemplazar la coma por un punto para el parseo
        let numValue = parseFloat(value.replace(',', '.'));

        // Si el valor es válido, agregar el símbolo de porcentaje
        if (!isNaN(numValue) && numValue >= 0 && numValue <= 100 && value !== '') {
            this.value = numValue.toString().replace('.', ',') + '%'; // Cambiar el punto por una coma
        }
    });

    $(document).ready(function() {
        $('#casedata_percent').on('input', function() {
            updateIncapacityType();
        });
    });

    //Función para determinar el tipo de incapacidad que pertenece
    function updateIncapacityType() {
        const percentInput = document.getElementById('casedata_percent');
        const incapacityTypeInput = document.getElementById('casedata_type_disability');
        const percentValue = parseFloat(percentInput.value);
        const percentError = document.getElementById('percent_error');

        // Validar el porcentaje ingresado
        if (isNaN(percentValue) || percentValue < 0 || percentValue > 100) {
            percentError.style.display = 'block';
            incapacityTypeInput.value = '';
            return;
        } else {
            percentError.style.display = 'none';
        }

        // Determinar el tipo de incapacidad permanente basado en el porcentaje dado por el usuario
        if (percentValue < 50) {
            incapacityTypeInput.value = 'IMP - Incapacidad menor permanente'; // Incapacidad menor permanente
        } else if (percentValue >= 50 && percentValue < 67) {
            incapacityTypeInput.value = 'IPP - Incapacidad parcial permanente'; // Incapacidad parcial permanente
        } else if (percentValue >= 67 && percentValue < 100) {
            incapacityTypeInput.value = 'ITP - Incapacidad total permanente'; // Incapacidad total permanente
        } else if (percentValue === 100) {
            incapacityTypeInput.value = 'Gran invalidez'; // Gran invalidez
        }
    }
    // Eliminar fila de formulario con confirmación
    $(document).on('click', '.deleteButton', function () {
        var diagnosticId = $(this).data('id'); // Obtener el id correcto desde el atributo data-id
        var $loading = $('#loading-' + diagnosticId); // Seleccionar el loader correspondiente al botón

        Swal.fire({
            title: '¿Estás seguro?',
            text: "No podrás revertir esta acción",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Sí, eliminarlo',
            cancelButtonText: 'Cancelar'
        }).then((result) => {
            if (result.isConfirmed) {
                var url = `/servicio/${diagnosticId}/pe_ip_sort/delete`;

                // Mostrar el loader en el botón
                $loading.show();
                $(this).prop('disabled', true); // Deshabilitar el botón mientras se procesa

                $.ajax({
                    url: url,
                    type: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function (result) {
                        $loading.hide(); // Ocultar el loader
                        $(this).prop('disabled', false); // Rehabilitar el botón

                        if (result.success) {
                            Swal.fire(
                                'Eliminado',
                                result.message,
                                'success'
                            ).then(() => {
                                location.reload();
                            });
                        } else {
                            Swal.fire(
                                'Error',
                                result.message,
                                'error'
                            );
                        }
                    }.bind(this), // Asegurarse de que "this" se refiera al botón de eliminar
                    error: function (err) {
                        $loading.hide(); // Ocultar el loader
                        $(this).prop('disabled', false); // Rehabilitar el botón
                        console.log('Error eliminando el diagnóstico:', err);
                        Swal.fire(
                            'Error',
                            'Ocurrió un error al eliminar el diagnóstico.',
                            'error'
                        );
                    }.bind(this) // Asegurarse de que "this" se refiera al botón de eliminar
                });
            }
        });
    });
</script>
