

    <div class="title none-transform"  ><i class="dropdown icon "></i>Detalle de muerte del trabajador<span style="color: red;" class="required">*</span></div>
    <div class="content title_name-class" style="margin: 0 !important; padding: .5em 1em 1.5em; !important;">
        <div class="styled fluid accordion transition visible" style="display: block !important;">
        <div class="title none-transform"><i class="dropdown icon"></i>Calculo renta por muerte trabajador<span style="color: red;" class="required">*</span></div>
        <div class="content">
                <div class="ui form small clearing transition hidden">
                    <div class="ui four column grid">
                        <div class="column">
                            <div class="ui list">
                            <div class="required field">
                                <label class="item-label" for="Start_date_of_rent">Fecha inicio pago Renta</label>
                                <input id="Start_date_of_rent" type="text" name="start_date_of_rent" class="datepicker"
                                       placeholder="dd/mm/yyyy" value="{{ $pmtSort->start_date_of_rent_submit ?? '' }}">
                            </div>
                            </div>
                        </div>
                        <div class="column">
                            <div class="ui list">
                                <div class="required field disabled-fields">
                                    <label class="item-label">Promedio salario mensual reportado</label>
                                    <input type="text" name="prom_monthly_salary" class="minus_font only-numbers" value="{{ $averageSalaryFormatted ?? '' }}" readonly  placeholder="0.00" >
                                </div>
                            </div>
                        </div>
                        <div class="column">
                            <div class="ui list">
                                <div class="required field disabled-fields">
                                    <label class="item-label">Salario anual de la persona trabajadora</label>
                                    <input type="text" id="annual_salary_person" name="annual_salary_person" class="minus_font only-numbers" value="{{ $annualSalaryFormatted ?? '' }}" readonly placeholder="0.00">
                                </div>
                            </div>
                        </div>
                        <div class="column">
                            <div class="ui list">
                                <div class="required field">
                                    <label class="item-label">Salario anual máximo al momento del pago</label>
                                    <input type="text" id="maximum_annual_salary" name="maximum_annual_salary" class="minus_font only-numbers" value="{{ $pmtSort->maximum_annual_salary ?? '' }}" placeholder="0.00">
                                </div>
                            </div>
                        </div>
                    </div>

                <div class="ui four column grid">
                    <div class="column">
                        <div class="ui list">
                        <div class="required field disabled-fields">
                            <label class="item-label">Renta anual</label>
                            <input type="text" name="annual_income" id="annual_income" class="minus_font only-numbers" value="{{ $pmtSort->annual_income ?? '' }}" readonly placeholder="0.00" >
                        </div>
                        </div>
                    </div>
                    <div class="column">
                        <div class="ui list">
                        <div class="required field disabled-fields">
                            <label class="item-label">Valor Mensualidad</label>
                            <input type="text" name="monthly_value" id="monthly_value" class="minus_font only-numbers" value="{{ $pmtSort->monthly_value ?? '' }}" readonly placeholder="0.00">
                        </div>
                        </div>
                    </div>
                    <div class="column">
                        <div class="ui list">
                        <div class="required field">
                            <label class="item-label">Suma mensual adicional</label>
                            <input type="text" id="additional_monthly_sum" name="additional_monthly_sum"  class="minus_font only-numbers" value="{{ $pmtSort->additional_monthly_sum ?? '' }}" placeholder="0.00" >
                        </div>
                        </div>
                    </div>
                    <div class="column">
                        <div class="ui list">
                        <div class="required field disabled-fields">
                            <label class="item-label">Total mes a pagar</label>
                            <input type="text" name="total_month_to_pay"  class="minus_font only-numbers" value="{{ $pmtSort->total_month_to_pay ?? '' }}" readonly>
                        </div>
                        </div>
                    </div>
                </div>
                <div class="ui four column grid">
                    <div class="column">
                        <div class="ui list">
                        <div class="required field disabled-fields">
                            <label class="item-label">Total años a pagar</label>
                            <input type="text" name="total_years_to_pay"  class="minus_font only-numbers" value="{{ $pmtSort->total_years_to_pay ?? '' }}" readonly>
                        </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        </div>
    </div>

        <style>
            .none-transform {
                text-transform: none !important;
            }
            </style>
<script type="text/javascript">
    $(document).ready(function () {

        $('input.only-numbers').on('input', function() {
            this.value = this.value
                .replace(/[^0-9.]/g, '')


        });

    });

    // Inicializa los checkboxes
    $('.ui.radio.checkbox').checkbox();
    // Inicializa los checkboxes
    $('.ui.dropdown').dropdown();

    /* Función para establecer formato de fecha
    * Ejemplo jueves 10 de octubre del 2024
    * */
    $('.datepicker').pickadate({
        selectYears: 100,
        format: 'dd/mm/yyyy',
        formatSubmit: 'dd/mm/yyyy',
        changeMonth: true,
        changeYear: true,
        selectMonths: true,

        onClose: function() {
            // Obtener la fecha seleccionada
            var selectedDate = this.get('select', 'dd/mm/yyyy'); // Cambiar a este formato
            if (selectedDate) {
                // Dividir la fecha seleccionada en partes
                var dateParts = selectedDate.split('/');
                var day = parseInt(dateParts[0], 10);
                var month = parseInt(dateParts[1], 10) - 1; // Los meses son de 0 a 11
                var year = parseInt(dateParts[2], 10);

                // Crear el objeto Date utilizando partes
                var date = new Date(year, month, day);

                // Array con los nombres de los días de la semana
                var days = ['Domingo', 'Lunes', 'Martes', 'Miércoles', 'Jueves', 'Viernes', 'Sábado'];
                // Array con los nombres de los meses
                var months = ['Enero', 'Febrero', 'Marzo', 'Abril', 'Mayo', 'Junio', 'Julio', 'Agosto', 'Septiembre', 'Octubre', 'Noviembre', 'Diciembre'];

                // Formatear la fecha
                var formattedDate = days[date.getDay()] + ' ' + date.getDate() + ' de ' + months[date.getMonth()] + ' de ' + date.getFullYear();

                // Asignar la fecha formateada al campo de entrada
                $(this.$node).val(formattedDate); // Cambia el valor del campo de entrada actual
            }
        }
    });

    document.addEventListener('DOMContentLoaded', () => {
        // Traer el tipo de moneda del controlador
        const currencySymbol = "{!! $currencySymbol !!}";

        /**
         * Función que permite formatear con miles y decimales
         * */
        function formatCurrency(value) {
            value = value.replace(/[^0-9]/g, '');
            if (!value) return '';
            let numericValue = parseInt(value, 10);
            if (isNaN(numericValue)) return '';
            let formattedIntegerPart = numericValue.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');

            return formattedIntegerPart;
        }

        /**
         * Función para dar formato de moneda a campos de Salario anual máximo al momento del pago y Suma mensual adicional:
         * */
        function handleInputFormatting(inputField) {
            inputField.addEventListener('input', function () {
                const rawValue = this.value.replace(/[^0-9]/g, '');
                const formattedValue = formatCurrency(rawValue);
                this.value = `${currencySymbol} ${formattedValue}`;
            });

            inputField.addEventListener('blur', function () {
                const currentValue = this.value;

                // Si el valor ya contiene ".00" o decimales, no hacer nada
                if (currentValue.includes('.')) return;

                const rawValue = this.value.replace(/[^0-9]/g, '');
                const formattedValue = formatCurrency(rawValue);
                this.value = `${currencySymbol} ${formattedValue}.00`; // Agregar ".00" solo al final si no lo tiene
            });
        }

        // Aplicar la función a ambos campos
        const inputField1 = document.getElementById('maximum_annual_salary');
        const inputField2 = document.getElementById('additional_monthly_sum');

        handleInputFormatting(inputField1);
        handleInputFormatting(inputField2);
    });

    function removeFormat(input) {
        // Al hacer clic, elimina el formato de puntos y comas para tener un valor numérico limpio
        let value = input.value.replace(/\./g, '').replace(',', '.'); // Remover puntos y convertir comas en puntos (decimales)
        if (!isNaN(value) && value !== '') {
            input.value = parseFloat(value).toString(); // Convertir a string sin formato
        }
    }


    // Aplicar el formato automáticamente al cargar la página
    /*document.addEventListener("DOMContentLoaded", function() {
        let currencyInputs = document.querySelectorAll('input.only-numbers');

        // Aplica el formato a cada input al cargar la página
        currencyInputs.forEach(function(input) {
            formatCurrency(input);
        });
    });*/
    $(document).ready(function() {
        $('#type_of_beneficiary').on('change', function() {
            const selectedBeneficiary = $(this).val();
            let totalYears = '';

            // Ajusta los años dependiendo del tipo de beneficiario
            switch (selectedBeneficiary) {
                case 'CN': // Cónyuge
                    totalYears = '10';
                    break;
                case 'HJ': // Hijo
                    totalYears = '10';
                    break;
                case 'MD': // Madre
                    totalYears = '10';
                    break;
                case 'PD': // Padre
                    totalYears = '10';
                    break;
                case 'OD': // Otros dependientes
                    totalYears = '0';
                    break;
                default:
                    totalYears = '0';
            }

            // Asignar el valor al campo de texto
            $('input[name="total_years_to_pay"]').val(totalYears);
            let totalMonths = totalYears * 12;
            $('input[name="total_month_to_pay"]').val(totalMonths);
        });

    });
    document.addEventListener('DOMContentLoaded', () => {
        const currencySymbol = "{!! $currencySymbol !!}";
        console.log('currencySymbol: ', currencySymbol)
        /**
         * Función para limpiar los números de los cálculos
         * */
        function cleanValue(value) {
            if (!value) return 0;

            // Eliminar todos los símbolos no numéricos excepto el punto y el guión
            let cleanedValue = value.replace(/[^\d.-]/g, '');
            const parts = cleanedValue.split('.');

            if (parts.length > 2) {
                // Sí hay más de un punto decimal, eliminar todos excepto el último
                const lastPart = parts.pop();
                cleanedValue = parts.join('') + '.' + lastPart;
            }
            const numericValue = parseFloat(cleanedValue) || 0;
            return numericValue.toFixed(2);
        }

        /**
         * Función para calcular Renta anual y valor mensualidad
         * */
        function calculateAnnualIncome() {

            const maxSalaryInput = document.getElementById('maximum_annual_salary');
            const annualSalaryInput = document.getElementById('annual_salary_person');
            const annualIncomeInput = document.getElementById('annual_income');
            const monthlyValueInput = document.getElementById('monthly_value');

            // Si los campos están vacíos, limpiar los resultados
            if (!maxSalaryInput.value || !annualSalaryInput.value) {
                annualIncomeInput.value = '';
                monthlyValueInput.value = '';
                return;
            }
            // Limpiar valores para cálculos
            const sam = parseFloat(cleanValue(maxSalaryInput.value));
            const sat = parseFloat(cleanValue(annualSalaryInput.value));

            // Calcular Renta Anual Formula: RA = SAM + ((SAT - SAM) * 0.67)
            const annualIncome = sam + ((sat - sam) * 0.67);

            const roundedAnnualIncome = Math.round(annualIncome * 100) / 100;
            const formattedAnnualIncome = roundedAnnualIncome.toLocaleString('en-US', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            });
            annualIncomeInput.value =`${currencySymbol} ${formattedAnnualIncome}`;

            // Calcular Valor Mensual: dividir Renta Anual en 12
            const monthlyValue = annualIncome / 12;
            const formattedMonthlyValue = monthlyValue.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
            monthlyValueInput.value = `${currencySymbol} ${formattedMonthlyValue}`;
        }

        // Escuchar cuando el campo de Salario Anual Máximo pierde el enfoque (blur)
        $('input[name="maximum_annual_salary"]').on('blur', function () {
            calculateAnnualIncome();
        });
    });
</script>