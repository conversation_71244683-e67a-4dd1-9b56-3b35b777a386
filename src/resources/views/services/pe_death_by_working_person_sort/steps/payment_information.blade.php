<div class="title none-transform">
    <i class="dropdown icon"></i>Información de pago <span style="color: red;" class="required">*</span>
</div>
<div class="content title_name-class" style="margin: 0 !important; padding: .5em 1em 1.5em; !important;">
    <div id="beneficiary-section" class="styled fluid accordion transition visible" style="display: block !important;">
        <!-- Sección de Datos de los beneficiarios -->
        @foreach($peMptBeneficiaries as $beneficiary)
        <div class="title none-transform">
            <i class="dropdown icon"></i> Datos de los beneficiarios <span style="color: red;" class="required">*</span>
        </div>
        <div class="content" id="beneficiary-content">
            <div class="ui form small clearing transition hidden beneficiary-form">
                <div class="ui four column grid">

                        <div class="column">
                            <div class="ui list">
                                <div class="required field">
                                    <label for="type_of_beneficiary">Tipo de beneficiario</label>
                                    <select name="type_of_beneficiary[]" class="ui dropdown" id="type_of_beneficiary">
                                        <option value="" {{ is_null($beneficiary->type_of_beneficiary) ? 'selected' : '' }} disabled>Seleccionar</option>
                                        @foreach($TYPE_BENEFICIARY as $key => $value)
                                            <option value="{{$key}}" {{ $key == $beneficiary->type_of_beneficiary ? 'selected' : '' }}>{{$value}}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="column">
                            <div class="ui list">
                                <div class="required field">
                                    <label class="item-label">Número de identificación</label>
                                    <input type="text" name="identification_number_beneficiary[]" value="{{ $beneficiary->identification_number_beneficiary }}">
                                </div>
                            </div>
                        </div>
                        <div class="column">
                            <div class="ui list">
                                <div class="required field">
                                    <label class="item-label">Nombre y Apellido</label>
                                    <input type="text" name="name_and_surname[]" value="{{ $beneficiary->name_and_surname }}">
                                </div>
                            </div>
                        </div>
                        <div class="column">
                            <div class="ui list">
                                <div class="required field">
                                    <label for="birthdate">Fecha de nacimiento</label>
                                    <input id="birthdate" type="text" name="birthdate[]" class="datepicker" placeholder="dd-mm-yyyy" value="{{ $beneficiary->birthdate }}">
                                </div>
                            </div>
                        </div>
                        <div class="column">
                            <div class="ui list">
                                <div class="required field">
                                    <label class="item-label">Parentesco</label>
                                    <input type="text" name="relationship[]" value="{{ $beneficiary->relationship }}">
                                </div>
                            </div>
                        </div>
                        <div class="column">
                            <div class="ui list">
                                <div class="required field disabled-fields" >
                                    <label class="item-label">% de la mensualidad que corresponde</label>
                                    <input type="text" name="monthly_payment[]" id="monthly_payment" value="{{ $beneficiary->monthly_payment }}" readonly>
                                </div>
                            </div>
                        </div>
                </div>
            </div>

            <!-- Línea divisoria con Semantic UI -->
            <div class="ui divider"></div> <!-- Usando el divider de Semantic UI -->

            <div class="beneficiary-container">
                <div class="title none-transform active">
                    <i class="dropdown icon"></i> Datos Pago Beneficiario <span style="color: red;" class="required">*</span>
                </div>
                <div class="content active" id="payment-content">
                    <div class="ui form small clearing transition visible payment-beneficiary-form">
                        <div class="ui four column grid">

                                <div class="column">
                                    <div class="ui list">
                                        <div class="required field">
                                            <label class="item-label">Número de cuenta IBAN</label>
                                            <input type="text" name="account_number[]" id="iban_account_number_beneficiary" maxlength="22"  value="{{ $beneficiary->account_number }}">
                                            <small class="error-message" style="color: red; display: none;">Debe tener exactamente 22 caracteres</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="column">
                                    <div class="ui list">
                                        <div class="required field">
                                            <label class="item-label">Tipo de cuenta</label>
                                            <select name="account_type[]" class="ui dropdown">
                                                <option value=" {{isset($beneficiary)&& $beneficiary->account_type ?? 'Seleccionar' }}">Seleccionar</option>
                                                <option value="savings" {{ $beneficiary->account_type == 'savings' ? 'selected' : '' }}>Ahorros</option>
                                                <option value="current" {{ $beneficiary->account_type == 'current' ? 'selected' : '' }}>Corriente</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="column">
                                    <div class="ui list">
                                        <div class="required field">
                                            <label class="item-label">Nombre de la entidad bancaria</label>
                                            <input type="text" name="name_of_the_banking_entity[]" value="{{ $beneficiary->name_of_the_banking_entity }}">
                                        </div>
                                    </div>
                                </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
        @endforeach

    </div>

    <div id="button-container">
        <button id="add-beneficiary-payment" type="button" class="ui basic small icon blue button">
            <i class="add icon"></i> Agregar beneficiario
        </button>
    </div>
</div>



{{csrf_field()}}


<style type="text/css">

    .none-transform {
        text-transform: none !important;
    }

    .item-label {
        font-weight: bold;
        margin-right: 10px; /* Espacio entre la etiqueta y el valor */
    }

    .item-value {
        font-size: 1em; /* Ajusta el tamaño de la fuente según sea necesario */
        color: #333;
    }
    .ui.grid {
        margin-top: -1rem;
        margin-bottom: -1rem;
        margin-right: -1rem;
    }

    .ui.grid .column {
        padding: 0.5rem 1rem !important;
    }

    .ui.accordion .title {
        text-transform: uppercase;
    }
    input[type="text"] {
        text-transform: none !important ;
    }

    .minus_font {
        text-transform: none !important;
    }
    #button-container {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        margin-top: 20px;
    }

    #add-beneficiary-payment,
    #remove-beneficiary-payment {
        margin-bottom: 10px;
    }

    #remove-beneficiary-payment {
        display: none;
    }
    #remove-beneficiary-payment:hover {
        background-color: #c0392b;
    }
    .disabled-fields input[readonly],
    .disabled-fields select[disabled] {
        background-color: #f3f4f5 !important;
        color: #333 !important;
        border-color: #ddd !important;

    }

    .deleteButton {
        margin-top: 10px;
        margin-bottom: 10px;
    }

    .removeButtonContainer {
        text-align: right;
        margin-top: 10px;  /* Agrega un poco de espacio arriba del botón */
    }


</style>
<script type="text/javascript">

    $(document).ready(function () {
        $('.ui.accordion .ui.grid .row').css('padding-top', 0);
        $('.ui.accordion .ui.grid .column').css('padding-top', 0);

        $('.ui.accordion').accordion({
            exclusive: false
        });
        // Función para cambiar el formato de fecha selecciona a tipo oración
        // Ejemplo: Miércoles 09 de octubre del 2024
        $('.datepicker').pickadate({
            selectYears: 100,
            format: 'dd/mm/yyyy', // Formato de la fecha (DD/MM/YYYY)
            formatSubmit: 'dd/mm/yyyy', // Formato al enviar la fecha
            changeMonth: true,       // Habilitar selección de mes
            changeYear: true,        // Habilitar selección de año
            selectMonths: true,      // Muestra un desplegable de meses

            onClose: function() {
                // Obtener la fecha seleccionada
                var selectedDate = this.get('select', 'dd/mm/yyyy');
                if (selectedDate) {
                    // Dividir la fecha seleccionada en partes
                    var dateParts = selectedDate.split('/');
                    var day = parseInt(dateParts[0], 10);
                    var month = parseInt(dateParts[1], 10) - 1;
                    var year = parseInt(dateParts[2], 10);

                    var date = new Date(year, month, day);

                    // Array con los nombres de los días de la semana
                    var days = ['Domingo', 'Lunes', 'Martes', 'Miércoles', 'Jueves', 'Viernes', 'Sábado'];
                    // Array con los nombres de los meses
                    var months = ['Enero', 'Febrero', 'Marzo', 'Abril', 'Mayo', 'Junio', 'Julio', 'Agosto', 'Septiembre', 'Octubre', 'Noviembre', 'Diciembre'];

                    var formattedDate = days[date.getDay()] + ' ' + date.getDate() + ' de ' + months[date.getMonth()] + ' de ' + date.getFullYear();

                    $(this.$node).val(formattedDate);
                }
            }
        });
        // Asignar porcentaje a mensualidad que corresponde
        $('#type_of_beneficiary').change(function() {
            const beneficiaryType = $(this).val();
            let percentage = '';

            switch (beneficiaryType) {
                case 'CN': // Cónyuge
                    percentage = '40%';
                    break;
                case 'HJ': // Hijo
                    percentage = 'No detalla'; // No aplica
                    break;
                case 'MD': // Madre
                    percentage = '30%';
                    break;
                case 'PD': // Padre
                    percentage = '10%';
                    break;
                case 'OD': // Otros dependientes
                    percentage = 'No detalla'; // No aplica
                    break;
                default:
                    percentage = ''; // Por defecto
                    break;
            }

            // Establecer el valor del input del porcentaje
            $('#monthly_payment').val(percentage);
        });
    });

    /**
     * Función para agregar nuevo beneficiario
     * */
    document.getElementById('add-beneficiary-payment').addEventListener('click', function() {
        let beneficiarySection = document.getElementById('beneficiary-section');

        // duplicar la plantilla de datos del beneficiario y la plantilla de datos de pago
        let beneficiaryTemplate = document.querySelector('.beneficiary-form');
        let paymentTemplate = document.querySelector('.beneficiary-container');

        // duplicar ambas secciones
        let newBeneficiary = beneficiaryTemplate.cloneNode(true);
        let newPayment = paymentTemplate.cloneNode(true);

        // Agregar estilos requeridos
        newBeneficiary.style.display = 'block';
        newPayment.style.display = 'block';
        newBeneficiary.style.marginBottom = '20px';
        newPayment.style.marginBottom = '20px';
        newBeneficiary.style.marginLeft = '20px';
        newPayment.style.marginLeft = '20px';

        // Agregar la línea horizontal al inicio de los datos del beneficiario
        let divider = document.createElement('div');
        divider.classList.add('ui', 'divider');

        // Crear el botón de eliminar para la sección de beneficiarios
        let removeButton = document.createElement('button');
        removeButton.classList.add('ui', 'basic', 'small', 'icon', 'red', 'button', 'deleteButton');
        removeButton.innerHTML = '<i class="trash icon"></i> Eliminar Beneficiario';
        removeButton.style.margin = '10px';

        // Agregar evento de eliminación al botón
        removeButton.addEventListener('click', function() {
            // Eliminar la sección de beneficiarios y datos de pago
            newBeneficiary.remove();
            newPayment.remove();
            divider.remove();
        });

        // Duplicar la línea horizontal para dividir los nuevos campos
        newBeneficiary.insertBefore(divider, newBeneficiary.firstChild);

        // Agregar el botón de eliminar al final de la sección de datos de pago
        let removeButtonContainer = document.createElement('div');
        removeButtonContainer.style.textAlign = 'right';
        removeButtonContainer.style.margin = '10px';
        removeButtonContainer.appendChild(removeButton);
        newPayment.appendChild(removeButtonContainer);

        // Limpiar valores
        $(newBeneficiary).find('input').val('');
        $(newPayment).find('input').val('');
        $(newBeneficiary).find('select').val('');

        // Limpiar dropdowns de Semantic UI
        $(newBeneficiary).find('.ui.dropdown').dropdown('clear');
        $(newPayment).find('.ui.dropdown').dropdown('clear');

        // Cambiar los ID de los campos de los inputs
        let inputsBeneficiary = newBeneficiary.querySelectorAll('input');
        let inputsPayment = newPayment.querySelectorAll('input');

        // Actualizar los valores de los atributos 'name' para evitar colisiones en el formulario
        inputsBeneficiary.forEach(function(input, index) {
            input.name = input.name.replace(/\[\]$/, '') + '[]';
        });

        inputsPayment.forEach(function(input, index) {
            input.name = input.name.replace(/\[\]$/, '') + '[]';
        });

        // Añadir el formulario de beneficiarios clonado a la sección principal de beneficiarios
        beneficiarySection.appendChild(newBeneficiary);

        // Añadir la nueva sección de pago del beneficiario clonado
        beneficiarySection.appendChild(newPayment);

        // Rehacer la inicialización necesaria de los controles
        $(newBeneficiary).find('.ui.dropdown').dropdown();
        $(newBeneficiary).find('.datepicker').pickadate();

        // Reasignar el evento change a los nuevos beneficiarios clonados
        $(newBeneficiary).find('#type_of_beneficiary').change(function() {
            const beneficiaryType = $(this).val();
            let percentage = '';

            switch (beneficiaryType) {
                case 'CN': // Cónyuge
                    percentage = '40%';
                    break;
                case 'HJ': // Hijo
                    percentage = 'No detalla';
                    break;
                case 'MD': // Madre
                    percentage = '30%';
                    break;
                case 'PD': // Padre
                    percentage = '10%';
                    break;
                case 'OD': // Otros dependientes
                    percentage = 'No detalla';
                    break;
                default:
                    percentage = '';
                    break;
            }

            // Establecer el valor del input del porcentaje
            $(newBeneficiary).find('#monthly_payment').val(percentage);
        });

    });



    function removePercent(input) {
        // quitar el '%'
        input.value = input.value.replace('%', '');
    }

    function addPercent(input) {
        //validación para asignar %
        if (input.value && !input.value.endsWith('%')) {
            input.value = input.value + '%';
        }
    }

    //asignar de forma automatica %
    document.addEventListener("DOMContentLoaded", function() {
        const input = document.getElementById("monthly_payment");
        if (input.value && !input.value.endsWith('%')) {
            input.value = input.value + '%';
        }
    });

    /**
     * Función para validar 22 caracteres de Cuenta IBAN
     * */

    $(document).ready(function() {

        const ibanInput = $('#iban_account_number_beneficiary').attr('maxlength', '22');
        const errorMessage = $('.error-message');
        ibanInput.on('blur', function(event) {
            if (this.value.length !== 22) {
                event.preventDefault();
                errorMessage.show();
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'El número de cuenta IBAN debe tener exactamente 22 caracteres.',
                    confirmButtonText: 'Aceptar'
                }).then(() => {
                    this.focus();
                });

            } else {
                errorMessage.hide();
            }
        });
    });

</script>
