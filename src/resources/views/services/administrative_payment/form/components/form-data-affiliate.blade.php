<div class="title"><i class="dropdown icon"></i>
    Datos del afiliado {{-- <span style="color: red;" class="required">*</span>--}}
</div>
<div class="content">

    <div class="ui three fields">

        <div class="field">
            <div class="ui list">
                <div class="field">
                    <label class="item-label" for="identification_titular">Tipo identificación:</label>
                    <input type="text" id="identification_titular" name="identification_titular" class="readonly" value="{{ $DOC_TYPES[$activity->affiliate->doc_type] }}" readonly>
                </div>
            </div>
        </div>

        <div class="field">
            <div class="ui list">
                <div class="field">
                    <label class="item-label" for="identification_number"># Identificación:</label>
                    <input type="text" id="identification_number" name="identification_number" class="readonly" value="{{ $activity->affiliate->doc_number ?? '' }}" readonly>
                </div>
            </div>
        </div>

        <div class="field">
            <div class="ui list">
                <div class="field">
                    <label class="item-label" for="worker_name">Nombre:</label>
                    <input type="text" id="worker_name" name="worker_name" class="readonly" value="{{ mb_convert_case(mb_strtolower(($activity->affiliate->full_name ?? '')), MB_CASE_TITLE, "UTF-8") }}" readonly>
                </div>
            </div>
        </div>

    </div>

    <div class="ui three fields">

        <div class="field">
            <div class="ui list">
                <div class="field">
                    <label for="worker_email" class="required">Correo electrónico persona trabajadora:</label>
                    <input type="text" id="worker_email" name="worker_email"  class="readonly" readonly value="{{$activity->affiliate->email}}" >
                </div>
            </div>
        </div>

        <div class="field">
            <div class="ui list">
                <div class="field">
                    <label class="item-label" for="worker_phone">Teléfono persona trabajadora:</label>
                    <input type="text" id="worker_phone" name="worker_phone" class="readonly" readonly value="{{$activity->affiliate->cellphone ?? ''}}" >
                </div>
            </div>
        </div>

        <div class="field">
            <div class="ui list">
                <div class="field">
                    <label class="item-label" for="worker_address">Dirección del trabajador (otras señas):</label>
                    <input type="text" id="worker_address" name="worker_address" class="readonly" readonly value="{{ $activity->parent->affiliate->employer_address ?? '' }}" >
                </div>
            </div>
        </div>

    </div>

    <div class="ui three fields">

        <div class="field">
            <div class="ui list">
                <div class="field">
                    <label class="item-label" for="province">Provincia:</label>
                    <input type="text" id="province" name="province" class="readonly" readonly value="{{ucwords(mb_strtolower($ubicacion['province'] ?? '' ))}}" >
                </div>
            </div>
        </div>

        <div class="field">
            <div class="ui list">
                <div class="field">
                    <label class="item-label" for="canton">Cantón:</label>
                    <input type="text" id="canton" name="canton" class="readonly" readonly value="{{ucwords(mb_strtolower($ubicacion['canton'] ?? '' ))}}"  >
                </div>
            </div>
        </div>

        <div class="field">
            <div class="ui list">
                <div class="field">
                    <label class="item-label" for="district">Distrito:</label>
                    <input type="text" id="district" name="district" class="readonly" readonly value="{{ucwords(mb_strtolower($ubicacion['district'] ?? '' ))}}"  >
                </div>
            </div>
        </div>

    </div>

    <div class="ui three fields">

        <div class="field">
            <div class="ui list">
                <div class="field">
                    <label class="item-label" for="recognition_type">Tipo de reconocimiento:</label>
                    <input type="text" id="recognition_type" name="recognition_type" class="readonly" readonly value="reconocimiento de factura por riesgos de trabajo">
                </div>
            </div>
        </div>

        <div class="field">
            <div class="ui list">
                <div class="field">
                    <label class="item-label" for="authorization">Cuenta con autorización médica:</label>
                    <input type="text" id="authorization" name="authorization" class="readonly" readonly value="{{ $activity->reintegrate->authorization == '1' ? 'Si' : 'No' }}">
                </div>
            </div>
        </div>

        <div class="field">
            <div class="ui list">
                <div class="field">
                    <label class="item-label" for="authorization"># caso:</label>
                    <input type="text" id="authorization" name="authorization" class="readonly" readonly value="{{ $activity->parent->gis_sort->formatCaseNumber() }}">
                </div>
            </div>
        </div>

    </div>

    <div class="ui three fields">

        <div class="field">
            <div class="ui list">
                <div class="field">
                    <label class="item-label" for="request_description">Descripción de solicitud:</label>
                    <textarea id="request_description" name="request_description" class="readonly" readonly  > {{ $activity->reintegrate->request_description ?? ''}} </textarea>
                </div>
            </div>
        </div>

        <div class="field">
            <div class="ui list">
                <div class="field">
                    <label class="item-label" for="date_accident">Fecha de accidente:</label>
                    <input type="text" id="date_accident" name="date_accident" class="readonly" readonly value="{{ $activity->parent->created_at->formatLocalized('%A %d de %B de %Y') }}"  >
                </div>
            </div>
        </div>

        <div class="field">
            <div class="ui list">
                <div class="field">
                    <label class="item-label" for="iban">Cuenta IBAN:</label>
                    <input type="text" id="iban" name="iban" class="readonly" readonly value="{{ $activity->reintegrate->iban_account ?? '' }}"  >
                </div>
            </div>
        </div>

    </div>

</div>
