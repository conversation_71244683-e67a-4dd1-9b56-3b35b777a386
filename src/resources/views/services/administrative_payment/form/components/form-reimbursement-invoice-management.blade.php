<div class="title"><i class="dropdown icon"></i>
    Gestión factura
</div>
<div class="content">

    <div class="ui four fields">
        <div class="field">
            <div class="ui list">
                <div class="field">
                    <label class="item-label" for="iban">Cuenta IBAN:</label>
                    <input type="text" id="iban" name="iban" class="readonly" readonly value="{{ $activity->administrative_payment->iban_account ?? '' }}"  >
                </div>
            </div>
        </div>
        <div class="field">
            <div class="ui list">
                <div class="field">
                    <label class="item-label" for="iban">Tipo moneda:</label>
                    <input type="text" id="iban" name="iban" class="readonly" readonly value="{{ $issuerAdministrativePayment->currency ?? '' }}"  >
                </div>
            </div>
        </div>

        <div class="required field" >
            <label>Estado de la factura:</label>
            <select name="invoice_status" id="invoice_status" class="ui dropdown @if($disabled) readonly @endif " @if($disabled) disabled @endif >
                <option value="">Seleccione una opción</option>
                <option value="approved" @if($activity->administrative_payment->invoice_status == 'approved') selected @endif >Aprobada</option>
                <option value="rejected" @if($activity->administrative_payment->invoice_status == 'rejected') selected @endif >Rechazada</option>
            </select>
        </div>

        <div class="field">
            <div class="ui list">
                <div class="field">
                    <label class="item-label" for="retencion">Retención en la fuente:</label>
                    <input type="text" id="retencion" name="retencion" class="readonly" readonly value="{{ number_format(($issuerAdministrativePayment->net_sales_total*2)/100 ?? 0, 2, ',', '.') }}"  >
                </div>
            </div>
        </div>

    </div>

    <table class="ui celled sortable striped compact very table selectable" style="font-size: 0.8em; line-height: 1em;" id="results">
        <thead>
            <tr>
                <th class="center aligned" title="# factura"># factura</th>
                <th class="center aligned" title="Detalle">Fecha factura</th>
                <th class="center aligned" title="Detalle">Descripción</th>
                <th class="center aligned" title="Monto grabado">Monto gravado</th>
                <th class="center aligned" title="Iva">Iva</th>
                <th class="center aligned" title="Total">Total</th>
                <th class="center aligned" title="Total">Total a pagar</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td class="left aligned">{{ $issuerAdministrativePayment->consecutive_number ?? '' }}</td>
                <td class="right aligned">{{ $issuerAdministrativePayment->issue_date ?? '' }}</td>
                <td class="left aligned">{{ $issuerAdministrativePayment->serviceDetails()->first()->description ?? '' }}</td>
                <td class="right aligned">{{ number_format($issuerAdministrativePayment->net_sales_total ?? 0, 2, ',', '.') }}</td>
                <td class="right aligned">{{ number_format($issuerAdministrativePayment->total_tax ?? 0, 2, ',', '.') }}</td>
                <td class="right aligned">{{ number_format($issuerAdministrativePayment->total_invoice_amount ?? 0, 2, ',', '.') }}</td>
                <td class="right aligned">{{ number_format($issuerAdministrativePayment->total_amountDue ?? 0, 2, ',', '.') }}</td>
            </tr>
        </tbody>

{{--        <tfoot>--}}
{{--        <tr>--}}
{{--            <th colspan="4"></th>--}}
{{--            <th class="right aligned" style="font-weight: bold;" >Total</th>--}}
{{--            <th class="right aligned" id="total-aprobado" style="font-weight: bold;" >{{number_format($total ?? 0, 2, ',', '.')}}</th>--}}
{{--        </tr>--}}
{{--        </tfoot>--}}


{{--        <thead>--}}
{{--            <tr>--}}
{{--                <th class="center aligned" title="">#</th>--}}
{{--                <th class="center aligned" title="# factura"># factura</th>--}}
{{--                <th class="center aligned" title="Codigo">Código</th>--}}
{{--                <th class="center aligned" title="Detalle">Detalle</th>--}}
{{--                <th class="center aligned" title="Cantidad">Cantidad</th>--}}
{{--                <th class="center aligned" title="Valor">Valor</th>--}}
{{--            </tr>--}}
{{--        </thead>--}}
{{--        <tbody>--}}

{{--        @if($issuerAdministrativePayment->isNotEmpty())--}}
{{--            @php--}}
{{--                $contador = 0;--}}
{{--                $total = 0;--}}
{{--            @endphp--}}

{{--            @foreach($issuerAdministrativePayment as $index =>  $row )--}}
{{--                @foreach($row->serviceDetails as $rowDetails)--}}
{{--                    <tr>--}}
{{--                        <input type="hidden" name="facturas[{{ $contador }}][id]" value="{{ $rowDetails->id }}">--}}
{{--                        <input type="hidden" name="facturas[{{ $contador }}][valor]" value="{{ $rowDetails->total_line_amount }}">--}}

{{--                        <td class="left center">{{ $contador+1 }}</td>--}}
{{--                        <td class="left aligned">{{ $row->consecutive_number ?? '' }}</td>--}}
{{--                        <td class="left aligned">{{ $rowDetails->code ?? '' }}</td>--}}
{{--                        <td class="left aligned">{{ $rowDetails->description ?? '' }}</td>--}}
{{--                        <td class="right aligned">{{ number_format($rowDetails->quantity ?? 0, 2, ',', '.') }}</td>--}}
{{--                        <td id="monto_{{$contador}}" class="right aligned valor" data-valor="{{ $rowDetails->total_line_amount ?? 0 }}">--}}
{{--                            {{ number_format($rowDetails->total_line_amount ?? 0, 2, ',', '.') }}--}}
{{--                        </td>--}}

{{--                    </tr>--}}

{{--                    @php--}}
{{--                        $contador++;--}}
{{--                        $total += $rowDetails->total_line_amount;--}}
{{--                    @endphp--}}

{{--                @endforeach--}}
{{--            @endforeach--}}
{{--        @endif--}}

{{--        </tbody>--}}
{{--        <tfoot>--}}
{{--        <tr>--}}
{{--            <th colspan="4"></th>--}}
{{--            <th class="right aligned" style="font-weight: bold;" >Total</th>--}}
{{--            <th class="right aligned" id="total-aprobado" style="font-weight: bold;" >{{number_format($total ?? 0, 2, ',', '.')}}</th>--}}
{{--        </tr>--}}
{{--        </tfoot>--}}
    </table>

    <div class="two fields">

        <div class="required field" id="rejection_reason_field" style="display: none;" >
            <label for="description">Motivo del rechazo: </label>
            <textarea name="description" id="description" rows="3" placeholder=""
                      @if($disabled) class="readonly" readonly @endif > {{ $activity->administrative_payment->reason ?? '' }} </textarea>
        </div>

    </div>
</div>

<script>

    let invoice_status = '{{ $activity->administrative_payment->invoice_status ?? '' }}';

    $(document).ready(function () {
        ocultaCampo(invoice_status);

        // Asignar evento solo si el elemento existe
        const invoiceSelect = document.getElementById('invoice_status');
        if (invoiceSelect) {
            invoiceSelect.addEventListener('change', function () {
                ocultaCampo(this.value);
            });
        }
    });

    function ocultaCampo(value) {
        const rejectionField = document.getElementById('rejection_reason_field');
        if (rejectionField) {
            rejectionField.style.display = value === 'rejected' ? 'block' : 'none';
        }
    }
</script>