<div class="title"><i class="dropdown icon"></i>
    Documentos adicionales cargados
</div>
<div class="content">

    <div class="ui segment">
        <h5 class="ui dividing header">Documentos </h5>
        @foreach($activiyDocumentsAdicionales as $row)
            <div id="fields" class="fields">
                <div class="six wide field">
                    <label>Documento</label>
                    <p>{{ str_replace('documents_reintegrates/', '', $row->path ?? '') }}</p>
                </div>
                <div class="three wide field">
                    <label>Archivo</label>
                    <a href="{{secure_url('file/' . $row->path)}}" download class="ui secondary icon button"><i class="download icon"></i></a>
                </div>
                <div class="four wide field">
                    <label>Fecha de cargue</label>
                    <p>{{$row->uploaded_at ?? ''}}</p>
                </div>
            </div>
        @endforeach
    </div>

    <div class="two fields">
        <div class="required field">
            <label for="response_load_additional">Respuesta</label>
            <textarea name="response_load_additional" id="response_load_additional " rows="3" class="readonly" readonly
                      placeholder="" > {{ $activity->reintegrate->response_load_additional ?? '' }} </textarea>
        </div>
    </div>

</div>

<script>


    const uploadedFiles = {
        documentosAdicionales: []
    };

    function addFiles(event, listId, format) {
        const input = event.target;
        const category = input.id; // documentosAdicionales
        const fileList = document.getElementById(listId);
        const files = Array.from(input.files);

        if (uploadedFiles[category].length + files.length > 5) {
            Swal.fire({
                title: 'Error',
                text: 'No puedes subir más de 5 archivos en este apartado.',
                icon: 'error',
                confirmButtonText: 'Aceptar',
                confirmButtonColor: '#91c845'
            });
            return;
        }


        // Agregar nuevos archivos a la lista existente
        files.forEach(file => {
            if (!uploadedFiles[category].some(f => f.name === file.name)) {
                uploadedFiles[category].push(file);

                // Crear elemento en la lista de archivos
                const listItem = document.createElement("div");
                listItem.className = "item";
                listItem.innerHTML = `
                <div class="content">
                    <i class="${format === 'pdf' ? 'file pdf outline' : 'file code outline'} icon"></i> ${file.name}
                    <button class="ui red mini button right floated" onclick="removeFile('${category}', '${file.name}', this)">
                        <i class="trash icon"></i> Eliminar
                    </button>
                </div>
            `;
                fileList.appendChild(listItem);
            }
        });

        // Limpiar el input para permitir nuevas selecciones
        input.value = "";
    }

    // Eliminar archivo de la lista visual y de la memoria
    function removeFile(category, fileName, button) {
        uploadedFiles[category] = uploadedFiles[category].filter(file => file.name !== fileName);
        button.closest(".item").remove();
    }



</script>

