@php
    $bankDetails = auth()->user()->isAnalistaIndemnizaciones() || auth()->user()->isAdmin();
@endphp

        <div class="title none-transform"><i class="dropdown icon"></i>Información de pago <span style="color: red;" class="required">*</span> </div>
        <div class="content title_name-class" style="margin: 0 !important; padding: .5em 1em 1.5em; !important;">
            <div class="styled fluid accordion transition visible" style="display: block !important;">
            <div class="title none-transform"><i class="dropdown icon"></i>Datos titular de cuenta <span style="color: red;" class="required">*</span></div>
            <div class="content">
                <div class="ui form small clearing transition hidden">
                    <div class="ui four column grid">
                        <div class="column">
                            <div class="ui list">
                                <div class="required field">
                                    <label class="item-label" for="rehabilitation_concept_titular">Tipo de titular:</label>
                                    <select name="rehabilitation_concept_titular" id="rehabilitation_concept_titular" class="ui dropdown" >
                                        <option value=""{{ !isset($peItSort->rehabilitation_concept_titular) ? 'selected' : '' }}>Seleccione uno</option>
                                        <option value="1"{{ isset($peItSort->rehabilitation_concept_titular) && $peItSort->rehabilitation_concept_titular == 1 ? 'selected' : '' }}>Tercero</option>
                                        <option value="0"{{ isset($peItSort->rehabilitation_concept_titular) && $peItSort->rehabilitation_concept_titular == 0 ? 'selected' : '' }}>Natural</option>

                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="column">
                            <div class="required field">
                                <label class="item-label" for="identification_titular">Tipo identificación:</label>
                                <select name="identification_titular" id="identification_titular" class="ui dropdown" >
                                    <option value="{{$peItSort->identification_titular ??""}}">Seleccione uno</option>
                                    @foreach($DOC_TYPES as $k => $v)
                                        <option class="item" value="{{$k}}">{{$v}}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="column">
                            <div class="ui list">
                                <div class="required field">
                                    <label class="item-label" for="identification_number_titular">Número de identificación:</label>
                                    <input type="text" name="identification_number_titular" id="identification_number_titular" value="{{$peItSort->identification_number_titular ??""}}">
                                </div>
                            </div>
                        </div>
                        <div class="column">
                            <div class="ui list">
                                <div class="required field">
                                    <label class="item-label" for="name_titular">Nombres:</label>
                                    <input type="text" name="name_titular" id="name_titular" value="{{$peItSort->name_titular ??""}}">
                                </div>
                            </div>
                        </div>
                        <div class="column">
                            <div class="ui list">
                                <div class="required field">
                                    <label class="item-label">Tipo de persona:</label>
                                    <select name="type_of_person_titular" class="ui dropdown" >
                                        <option value="" {{ !isset($peItSort->type_of_person_titular) ? 'selected' : '' }}>Seleccione uno</option>
                                        <option value="1"{{ isset($peItSort->type_of_person_titular) && $peItSort->type_of_person_titular == 1 ? 'selected' : '' }}>Natural</option>
                                        <option value="0"{{ isset($peItSort->type_of_person_titular) && $peItSort->type_of_person_titular == 0 ? 'selected' : '' }}>Jurídico</option>

                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>


            @if($bankDetails)
                    <div class="title none-transform">
                        <i class="dropdown icon"></i>  Datos bancarios <span style="color: red;" class="required">*</span>
                    </div>
                    <div class="content">
                        <div class="ui form small clearing transition hidden">
                            <div class="ui three column grid">
                                <div class="column">
                                    <div class="ui list">
                                        <div class="required field">
                                            <label class="item-label">Número de cuenta IBAN:</label>
                                            <input type="text" name="iban_account_number" id="iban_account_number" maxlength="22" value="{{ $peItSort->iban_account_number ?? ($activityLatestPeitSort->PeItSort->iban_account_number ?? '') }}">
                                            <small class="error-message" style="color: red; display: none;">Debe tener exactamente 22 caracteres</small>
                                        </div>
                                    </div>
                                </div>

                                <div class="column">
                                    <div class="ui list">
                                        <div class="required field">
                                            <label class="item-label">Tipo de cuenta:</label>
                                            <select name="type_account" class="ui dropdown">
                                                <option value=""
                                                        {{ !isset($peItSort->type_account) && !isset($activityLatestPeitSort->PeItSort->type_account) ? 'selected' : '' }}>
                                                    Seleccione uno
                                                </option>
                                                <option value="savings"
                                                        {{ (isset($peItSort->type_account) && $peItSort->type_account == 'savings') ||
                                                           (!isset($peItSort->type_account) && isset($activityLatestPeitSort->PeItSort->type_account) && $activityLatestPeitSort->PeItSort->type_account == 'savings') ? 'selected' : '' }}>
                                                    Ahorros
                                                </option>
                                                <option value="current"
                                                        {{ (isset($peItSort->type_account) && $peItSort->type_account == 'current') ||
                                                           (!isset($peItSort->type_account) && isset($activityLatestPeitSort->PeItSort->type_account) && $activityLatestPeitSort->PeItSort->type_account == 'current') ? 'selected' : '' }}>
                                                    Corriente
                                                </option>
                                            </select>
                                        </div>

                                    </div>
                                </div>


                                <div class="column">
                                    <div class="ui list">
                                        <div class="required field">
                                            <label class="item-label">Nombre de la entidad bancaria:</label>
                                            <input type="text" name="name_of_the_bank" class="readonly" readonly value="{{$peItSort->name_of_the_bank ?? ($activityLatestPeitSort->PeItSort->name_of_the_bank ?? '') }}" >
                                            <small class="error-bank-message" style="color: red; display: none;">Banco no identificado</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

            @endif



        </div>
    </div>

    {{csrf_field()}}



<style type="text/css">
    .item-label {
        font-weight: bold;
        margin-right: 10px; /* Espacio entre la etiqueta y el valor */
    }

    .item-value {
        font-size: 1em; /* Ajusta el tamaño de la fuente según sea necesario */
        color: #333;
    }
    .ui.grid {
        margin-top: -1rem;
        margin-bottom: -1rem;
        margin-right: -1rem;
    }

    .ui.grid .column {
        padding: 0.5rem 1rem !important;
    }

    .ui.accordion .title {
        text-transform: uppercase;
    }
    .none-transform {
        text-transform: none !important;
    }
    .disabled-fields input[readonly]{
        background-color: #f3f4f5 !important; /* Color gris */
        color: #333 !important;
        border-color: #ddd !important;
    }
    .disabled-fields select[disabled] {
        background-color: #f3f4f5 !important; /* Color gris claro */
        color: #333 !important; /* Color del texto */
        border-color: #ddd !important; /* Color del borde */
        opacity: 1 !important;
    }
    .disabled-select {
        background-color: #f3f4f5 !important;
        color: #333 !important;
        border: 1px solid #ddd !important;
        opacity: 1 !important;
    }

</style>
<script type="text/javascript">

    $(document).ready(function () {
        $('.ui.accordion .ui.grid .row').css('padding-top', 0);
        $('.ui.accordion .ui.grid .column').css('padding-top', 0);

        $('.ui.accordion').accordion({
            exclusive: false
        });

    })

    /**
     * Función para asignar valores dependiendo si es natural
     **/
    document.addEventListener('DOMContentLoaded', function () {
        const tipoTitular = document.getElementById('rehabilitation_concept_titular');
        const tipoDoc = document.getElementById('identification_titular');
        const numIdentificacion = document.getElementById('identification_number_titular');
        const nombre = document.getElementById('name_titular');

        const affiliateTipoDoc = document.getElementById('type_document');
        const affiliateNumIdentificacion = document.getElementById('number_ide_pe');
        const affiliateNombre = document.getElementById('name_affiliated_pe');

        tipoTitular.addEventListener('change', function () {
            if (this.value === "0") { // Valor para "Natural"
                llenarCamposDesdeAfiliado();
            } else {
                limpiarCampos();
            }
        });

        function llenarCamposDesdeAfiliado() {
            const valorDocAfiliado = affiliateTipoDoc.value;
            console.log("Valor de tipo de documento del afiliado: ", valorDocAfiliado);

            // Verificar si el valor existe en el dropdown antes de asignarlo
            const optionExists = $('#identification_titular option[value="' + valorDocAfiliado + '"]').length > 0;
            console.log("¿Existe la opción en el dropdown? ", optionExists);

            if (optionExists) {
                // Usar Semantic UI para seleccionar el valor adecuado basado en el valor del afiliado
                $('#identification_titular').dropdown('set selected', valorDocAfiliado);
            } else {
                console.error("El valor del afiliado no coincide con ninguna opción del dropdown");
            }

            // Asignar los demás valores
            numIdentificacion.value = affiliateNumIdentificacion.value;
            nombre.value = affiliateNombre.value;

            // Disparar evento 'change' para asegurarse que el UI se actualice correctamente
            $(numIdentificacion).trigger('change');
            $(nombre).trigger('change');
        }

        function limpiarCampos() {
            // Limpiar los campos
            $('#identification_titular').dropdown('clear');
            numIdentificacion.value = '';
            nombre.value = '';
        }
    });
    /**
     * Función para validar 22 caracteres de Cuenta IBAN
     * */

    $(document).ready(function() {

        const ibanInput = $('#iban_account_number').attr('maxlength', '22');
        const errorMessage = $('.error-message');
        ibanInput.on('blur', function(event) {
            if (this.value.length !== 22) {
                event.preventDefault();
                errorMessage.show();
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'El número de cuenta IBAN debe tener exactamente 22 caracteres.',
                    confirmButtonText: 'Aceptar'
                }).then(() => {
                    this.focus();
                });

            } else {
                errorMessage.hide();
            }
        });
    });

    const bankList = @json($bankAccountIban);

    document.addEventListener('DOMContentLoaded', function () {
        const ibanInput = document.getElementById('iban_account_number');
        const bankNameInput = document.querySelector('input[name="name_of_the_bank"]');
        const errorMessageDos = $('.error-bank-message');
        const errorMessage = $('.error-message');

        ibanInput.addEventListener('input', function () {
            const iban = ibanInput.value.trim();

            // Validar longitud
            if (iban.length !== 22) {
                errorMessageDos.show();
                errorMessage.show();
                bankNameInput.value = '';
                return;
            } else {
                errorMessageDos.hide();
                errorMessage.hide();
            }

            // Extraer código bancario (caracteres 6, 7 y 8)
            const bankCode = iban.substring(5, 8);

            // Buscar en el listado
            const matchedBank = bankList.find(bank => bank.bank_code === bankCode);

            if (matchedBank) {
                bankNameInput.value = matchedBank.bank_name;
            } else {
                errorMessageDos.show();
                bankNameInput.value = '';
            }
        });
    });


</script>
