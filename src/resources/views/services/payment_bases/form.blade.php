@extends('layouts.main')

@section('title', 'Determinación de incapacidades LIQUIDADAS')

@section('menu')
    @parent
@endsection

@section('content')
    <div class="ui basic segment">
        <h1 class="ui header">
            Determinación de incapacidades LIQUIDADAS
        </h1>
        <form autocomplete="off" action="{{secure_url("servicio/{$activity->id}/payment_bases/save")}}" id="dictamen"
              method="post" class="ui small form">
            <div class="ui secondary segment">
                <div class="ui three columns grid">
                    <div class="column">
                        <b>Identificación:</b> {{$activity->affiliate->doc_type}} {{$activity->affiliate->doc_number}}
                    </div>
                    <div class="column"><b>Nombre:</b> <a
                                href="{{secure_url('afiliado/' . $activity->affiliate_id)}}">{{$activity->affiliate->full_name}}</a>
                    </div>
                    <div class="column"><b>Actividad:</b> <a
                                href="{{secure_url('servicio/' . $activity->id)}}">{{$activity->service->name}}</a>
                    </div>
                    <div class="column"><b>Nro. Dictamen:</b> {{$activity->id}}</div>
                    <div class="column"><b>Fecha solicitud:</b> {{$activity->created_at->formatLocalized('%B %d, %Y')}}
                    </div>
                    <div class="column"><b>Servicio Padre:</b>
                        <a href="{{secure_url('servicio/' . $parent->id)}}">{{$parent->service->name}}
                            - {{$parent->created_at->formatLocalized("%d/%m/%Y")}}
                        </a>
                    </div>
                </div>
            </div>
            <div class="ui styled fluid accordion">
{{--                <div class="title"><i class="dropdown icon"></i> Información Incapacidad Liquidada--}}
{{--                </div>--}}
{{--                <div class="content">--}}
{{--                    <div class="four fields">--}}
{{--                        <div class="field">--}}
{{--                            <label >radicado</label>--}}
{{--                            <input type="text" name="radicado" value="{{$activity->payment_bases ? $activity->payment_bases->radicado : ''}} " readonly>--}}
{{--                        </div>--}}
{{--                        <div class="field">--}}
{{--                            <label >documento</label>--}}
{{--                            <input type="text" name="documento" value="{{$activity->payment_bases ? $activity->payment_bases->documento : ''}} " readonly>--}}
{{--                        </div>--}}
{{--                    </div>--}}
{{--                </div>--}}
                <div class="title"><i class="dropdown icon"></i>
                    Resumen de incapacidades LIQUIDADAS
                </div>
                <div class="content">
                    @if($activity->payment_bases && count($activity->payment_bases->inabilities) > 0)
                        @foreach($activity->payment_bases->inabilities as $inability)
                            @include('services.payment_bases.inability', ['inability' => $inability])
                        @endforeach
                    @endif
                </div>

            </div>
            <div class="ui basic segment">
                <div class="ui error message"></div>
                <div class="fields">
                    <div class="four wide field">
                        <button class="ui green fluid button"><i class="save icon"></i> Guardar</button>
                    </div>
                    <div class="six wide field">
                        <a href="{{secure_url('/servicio/' . $activity->id)}}" class="ui basic small button"><i
                                    class="arrow left icon"></i> Volver a la actividad</a>
                    </div>
                </div>
            </div>
            {{csrf_field()}}
        </form>
    </div>

    <style type="text/css">
        .ui.grid .column {
            padding: 0.5rem 1rem !important;
        }

        .sender .field {
            display: none;
        }

        .ear.field {
            display: none;
        }

        .msp.field {
            display: none;
        }

        .ui.accordion .title {
            text-transform: uppercase;
        }

        .field > h3 {
            text-align: center;
            margin-top: 1.25rem;
        }

        .ui.search > .results {
            width: 30rem;
        }

        .ui.search > .results .result .title {
            padding: 0 !important;
            border: none !important;
            text-transform: none;
        }

        .ui.search > .results .result .content {
            padding: 0 !important;
        }

        th {
            text-align: center !important;
        }
    </style>
    <script type="text/javascript">
        var generatePDF = function () {
            $('form#dictamen').attr('target', '_blank');
            $('form#dictamen').attr('action', '{{secure_url('/servicio/' . $activity->id . '/payment_bases/pdf')}}');
            $('form#dictamen').submit();
            $('form#dictamen').removeAttr('target');
            $('form#dictamen').attr('action', '{{secure_url('/servicio/' . $activity->id . '/payment_bases/save')}}');
            return false;
        };

        var toggleSender = function () {
            console.log('sender');
            if ($('[name=sender]').val() == 5 || $('[name=sender]').val() == 8) {
                $('.sender .field').show();
            } else {
                $('.sender .field').hide();
            }
        };

        var cie10 = [];


        $(document).ready(function () {
            $('.ui.accordion .ui.grid .row').css('padding-top', 0);
            $('.ui.accordion .ui.grid .column').css('padding-top', 0);

            $('.ui.accordion').accordion({
                exclusive: false
            });

            $('form#dictamen a.red').click(function () {
                $(this).parent().parent().remove();
                calculateDeficienceSum();
            });
            $('form .ui.dropdown').dropdown({
                forceSelection: false
            });
            $('form .datepicker').pickadate({
                selectYears: 100,
                selectMonths: true,
                max: new Date(),
                formatSubmit: 'yyyy-mm-dd',
                format: 'dd/mm/yyyy'
            });

            $.getJSON('/js/cie10.json', function (json) {
                cie10 = json;
                $('form .ui.search.code').search({
                    source: cie10,
                    fields: {
                        title: 'COD',
                        description: 'DESCRIPTION'
                    },
                    searchFields: ['COD', 'DESCRIPTION'],
                    regExp: {
                        escape: /[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,
                        beginsWith: ''
                    },
                    showNoResults: true,
                    maxResults: 250,
                    searchFullText: false,
                    error: {
                        noResults: 'No se encontraron resultados para tu búsqueda.'
                    },
                    onSelect: function (result, response) {
                        $(this).parents('.fields').find('input.description').val(result.DESCRIPTION);
                    }
                });
            });

            $('form input').keydown(function (event) {
                if (event.keyCode == 13) {
                    event.preventDefault();
                    return false;
                }
            });

            @if ($activity->plantilla && $activity->plantilla->firm_date)
            // $('form a.basic.blue.button').hide();
            // $('form a.basic.red.button').hide();
            // $('form input, form textarea').prop('disabled', true);
            // $('form .controversy input, form .controversy textarea').prop('disabled', false);
            // $('form .pcl input, form .pcl textarea').prop('disabled', false);
            // $('form .ui.dropdown').addClass('disabled');
            // $('form .controversy .ui.dropdown').removeClass('disabled');
            // $('form .pcl .ui.dropdown').removeClass('disabled');
            @endif

            @if (Auth::user()->isViewer())
            $('.datepicker').pickadate('picker').stop();
            $('#dictamen .ui.search input').attr('disabled', 'disabled');
            $('#dictamen input, #dictamen textarea').attr('readonly', 'readonly');
            $('#dictamen .ui.dropdown').addClass('disabled');
            $('#dictamen .button').remove();
            @endif
        });
    </script>
@endsection