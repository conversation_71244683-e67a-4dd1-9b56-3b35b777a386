@extends('layouts.main')

@section('title', 'Proveedores de la seguridad social')

@section('menu')
    @parent
@endsection

@section('content')
    <div class="ui basic segment">
        <h1 class="ui header">
            Proveedores de la seguridad social
            <div class="sub header">Campos con <span style="color: red;" class="required">*</span> obligatorios.</div>
        </h1>

        <div class="ui secondary segment">
            <div class="ui grid">
                <div class="three column row">
                    <div class="column left aligned">
                        <b>Identificación:</b> {{ $activity->affiliate->doc_type }} {{ $activity->affiliate->doc_number }}
                    </div>
                    <div class="column center aligned">
                        <b>Nombre del proveedor:</b> <a
                        href="{{secure_url('afiliado/' . $activity->affiliate_id)}}">{{$activity->affiliate->full_name}}</a>
                    </div>
                    <div class="column right aligned">
                        <b>Actividad:</b> <a href="{{ secure_url('servicio/' . $activity->id) }}">{{ $activity->service->name }}</a>
                    </div>
                    <div class="column"><b>Fecha solicitud:</b> {{$activity->created_at->formatLocalized('%B %d, %Y')}}
                    </div>
                </div>
            </div>
        </div>
        
        <form autocomplete="off" action="{{secure_url("servicio/{$activity->id}/social_security_providers/save")}}" id="form-social-security-providers"
              method="post" class="ui attached form">
            {{csrf_field()}}

            <div class="ui styled fluid accordion">
                <!-- GESTION FACTURACIÓN ORDINARIA -->
                @if( $activity->state_id !== \App\States\StateSocialSecurityProvider::REGISTRADO)
                    {{--Solo el auditor tiene el permiso de parobar--}}
                    @if(
                        //Auth::user()->area_id === \App\Area::ADMINISTRATIVE || 
                        //Auth::user()->area_id === \App\Area::AUDITOR || 
                        //Auth::user()->area_id === \App\Area::ANALISTA_INDEMNIZACION ||
                        //Auth::user()->area_id === 47 && // TODO: Quitar esta validación
                        $activity->state_id === \App\States\StateSocialSecurityProvider::FACTURA_EN_REVISION_PSS
                    )
                        @include('services.social_security_providers.components.form-ordinary-invoices-management')
                    {{--La vista ahora se muestra sin problema luego de haberse aprobado / rechazado--}}
                    {{-- TODO: Implementar --}}
                    @elseif($activity->state_id !== \App\States\StateSocialSecurityProvider::FACTURA_EN_REVISION_PSS)
                        @include('services.social_security_providers.components.form-ordinary-invoices-management')
                    @endif
                @endif
            </div>

            <!-- Mostrar errores -->
            @if ($errors->any())
                <div class="ui negative message">
                    <div class="header">Errores encontrados</div>
                    <ul class="list">
                        @foreach ($errors->all() as $error)
                            <li>{!! $error !!}</li>
                            <!-- Permite que el HTML se interprete correctamente -->
                        @endforeach
                    </ul>
                </div>
            @endif

            <div style="margin-top: 25px;">
                <div class="fields one">
                    
                    {{-- 
                        @if($activity->state_id != \App\States\StateSocialSecurityProvider::FACTURA_PAGADA_PSS)
                        <div class="four wide field">
                            <button type="button" class="ui button blue" id="downloadPdfReimbursementBtn">Descargar vista previa PDF REINTEGRO</button>
                        </div>
                        @endif
                    --}}
                    <div class="field">
                        <a href="{{ secure_url('/proveedor/tablero_cuentas_medicas') }}" class="ui button secondary" id="btn-back">
                            <i class="arrow left icon"></i>
                            Volver
                        </a>
                    </div>
                    <div class="field">
                        <a href="{{ secure_url('/servicio/' . $activity->id) }}" class="ui button secondary" id="btn-back">
                            <i class="arrow left icon"></i>
                            ir a la actividad
                        </a>
                    </div>

                    @if(
                        $activity->state_id == \App\States\StateSocialSecurityProvider::FACTURA_EN_REVISION_PSS &&
                        (Auth::user()->area_id === \App\Area::ADMINISTRATIVE ||
                        Auth::user()->area_id === \App\Area::ANALISTA_INDEMNIZACION)
                    )
                        <div class="field">
                            <button class="ui primary fluid button" type="submit" id="btn-save">
                                <i class="save icon"></i> Enviar
                            </button>
                        </div>
                    @endif
                </div>
            </div>
        </form>
    </div>

    <!-- Modal para Reportar auditoría médica mnk -->
    <div class="ui modal" id="modalGenerateDoc">
        <div class="header">Datos del oficio de respuesta</div>
        <div class="content">
            <form class="ui form" method="POST" id="formGenerateDoc">
                {{ csrf_field() }}

                <div class="two fields">
                    <div class="field">
                        <label>Fecha del Oficio</label>
                        <input type="date" required name="letter_date" value="{{ old('letter_date', optional($reportData)->letter_date ?? '') }}" id="letter_date">
                    </div>
    
                    <div class="field">
                        <label>Número de consecutivo</label>
                        <input type="text" required name="consecutive_number" value="{{ old('consecutive_number', optional($reportData)->consecutive_number ?? '') }}">
                    </div>
                </div>

                <div class="two fields">
                    <div class="field">
                        <label>Asunto</label>
                        <input type="text" required name="subject" value="{{ optional($reportData)->subject ?? "" }}">
                    </div>
    
                    <div class="field">
                        <label>Nombre funcionario CCSS</label>
                        <input type="text" required name="ccss_employee_name" value="{{ old('ccss_employee_name', optional($reportData)->ccss_employee_name ?? '') }}">
                    </div>
                </div>

                <div class="two fields">
                    <div class="field">
                        <label>Mes del trámite</label>
                        <select class="ui fluid search dropdown" name="process_month" id="process_month">
                            <option value="">Seleccione un mes</option>
                            <option value="1" {{ old('process_month', optional($reportData)->process_month ?? '') == 1 ? 'selected' : '' }}>Enero</option>
                            <option value="2" {{ old('process_month', optional($reportData)->process_month ?? '') == 2 ? 'selected' : '' }}>Febrero</option>
                            <option value="3" {{ old('process_month', optional($reportData)->process_month ?? '') == 3 ? 'selected' : '' }}>Marzo</option>
                            <option value="4" {{ old('process_month', optional($reportData)->process_month ?? '') == 4 ? 'selected' : '' }}>Abril</option>
                            <option value="5" {{ old('process_month', optional($reportData)->process_month ?? '') == 5 ? 'selected' : '' }}>Mayo</option>
                            <option value="6" {{ old('process_month', optional($reportData)->process_month ?? '') == 6 ? 'selected' : '' }}>Junio</option>
                            <option value="7" {{ old('process_month', optional($reportData)->process_month ?? '') == 7 ? 'selected' : '' }}>Julio</option>
                            <option value="8" {{ old('process_month', optional($reportData)->process_month ?? '') == 8 ? 'selected' : '' }}>Agosto</option>
                            <option value="9" {{ old('process_month', optional($reportData)->process_month ?? '') == 9 ? 'selected' : '' }}>Septiembre</option>
                            <option value="10" {{ old('process_month', optional($reportData)->process_month ?? '') == 10 ? 'selected' : '' }}>Octubre</option>
                            <option value="11" {{ old('process_month', optional($reportData)->process_month ?? '') == 11 ? 'selected' : '' }}>Noviembre</option>
                            <option value="12" {{ old('process_month', optional($reportData)->process_month ?? '') == 12 ? 'selected' : '' }}>Diciembre</option>
                        </select>
                    </div>

                    <div class="field">
                        <label>Mes de facturación</label>
                        <select class="ui fluid search dropdown" name="billing_month" id="billing_month">
                            <option value="">Seleccione un mes</option>
                            <option value="1" {{ old('billing_month', optional($reportData)->billing_month ?? '') == 1 ? 'selected' : '' }}>Enero</option>
                            <option value="2" {{ old('billing_month', optional($reportData)->billing_month ?? '') == 2 ? 'selected' : '' }}>Febrero</option>
                            <option value="3" {{ old('billing_month', optional($reportData)->billing_month ?? '') == 3 ? 'selected' : '' }}>Marzo</option>
                            <option value="4" {{ old('billing_month', optional($reportData)->billing_month ?? '') == 4 ? 'selected' : '' }}>Abril</option>
                            <option value="5" {{ old('billing_month', optional($reportData)->billing_month ?? '') == 5 ? 'selected' : '' }}>Mayo</option>
                            <option value="6" {{ old('billing_month', optional($reportData)->billing_month ?? '') == 6 ? 'selected' : '' }}>Junio</option>
                            <option value="7" {{ old('billing_month', optional($reportData)->billing_month ?? '') == 7 ? 'selected' : '' }}>Julio</option>
                            <option value="8" {{ old('billing_month', optional($reportData)->billing_month ?? '') == 8 ? 'selected' : '' }}>Agosto</option>
                            <option value="9" {{ old('billing_month', optional($reportData)->billing_month ?? '') == 9 ? 'selected' : '' }}>Septiembre</option>
                            <option value="10" {{ old('billing_month', optional($reportData)->billing_month ?? '') == 10 ? 'selected' : '' }}>Octubre</option>
                            <option value="11" {{ old('billing_month', optional($reportData)->billing_month ?? '') == 11 ? 'selected' : '' }}>Noviembre</option>
                            <option value="12" {{ old('billing_month', optional($reportData)->billing_month ?? '') == 12 ? 'selected' : '' }}>Diciembre</option>
                        </select>
                    </div>
                </div>


                <div class="field">
                    <label>Observaciones</label>
                    <textarea required name="observations" rows="3">{{ old('observations', optional($reportData)->observations ?? '') }}</textarea>
                </div>

                <div class="three fields">
                    <div class="field">
                        <label>Nombre de la unidad</label>
                        <input type="text" required name="unit_name" value="{{ old('unit_name', optional($reportData)->unit_name ?? '') }}">
                    </div>
    
                    <div class="field">
                        <label>Calidades del firmante</label>
                        <input type="text" required name="signer_qualifications" value="{{ old('signer_qualifications', optional($reportData)->signer_qualifications ?? '') }}">
                    </div>
    
                    <div class="field">
                        <label>Puesto</label>
                        <input type="text" required name="position" value="{{ old('position', optional($reportData)->position ?? '') }}">
                    </div>
                </div>


                <div class="actions" style="margin-top: 15px;">
                    <div class="ui cancel button" id="btn-report-cancel">Cancelar</div>
                    <button type="submit" class="ui button primary" id="btn-report-submit">Confirmar</button>
                    {{-- <button type="button" class="ui button blue" id="downloadPdfBtn">Descargar vista previa PDF</button> --}}
                </div>
            </form>
        </div>
    </div>

    <style>
        .ui.grid .column {
            padding: 0.5rem 1rem !important;
        }

        .readonly {
            background: rgba(0, 0, 0, .05) !important;
        }


        .file-name {
            white-space: nowrap; /* Evita que el texto se divida en varias líneas */
            overflow: hidden; /* Oculta el contenido que se desborda */
            text-overflow: ellipsis; /* Agrega "..." al final cuando el texto se trunca */
            max-width: 100%; /* Asegúrate de que el texto se ajuste al ancho del contenedor */
            display: block;
        }
    </style>

    <script src="{{secure_url('https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js')}}"></script>
    <script src="{{secure_url('https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/locale/es.min.js')}}"></script>

    <script type="text/javascript">
        @if (!optional($reportData)->letter_date)
        // En fecha de oficio pone la fecha actual
        document.getElementById('letter_date').value = new Date().toISOString().split('T')[0];
        @endif

        @if (!optional($reportData)->process_month)
        // Establecer la fecha actual en el campo de fecha del oficio
        const mesActual = new Date().getMonth() + 1;
        document.getElementById('process_month').value = mesActual;
        @endif

        @if (!optional($reportData)->billing_month)
        // Establecer mes en el campo de fecha del facturación
        const fechaReferencia = new Date('{{ $socialSecurityProvider->created_at }}');
        const mesSeleccionado = fechaReferencia.getMonth() + 1;
        document.getElementById('billing_month').value = mesSeleccionado;
        @endif

        function openGenerateReport(e) {
            e.preventDefault();
            console.log("form gen doc");
            $('.ui.modal').modal('show');
        }

        $(document).ready(function () {
            $('.ui.dropdown').dropdown();
            $('.ui.accordion').accordion();
        });

        $('#formGenerateDoc').on('submit', function(e) {
            e.preventDefault(); // Prevent the default form submission

            $("#btn-report-cancel").addClass("disabled");
            $("#btn-report-submit").addClass("loading disabled");
            $("#downloadPdfBtn").addClass("disabled");

            // Serialize form data
            var formData = $(this).serialize();

            // Send AJAX request
            $.ajax({
                url: '{{secure_url("/servicio/{$activity->id}/social_security_providers/save-report-data")}}',
                method: 'POST',
                data: formData,
                success: function(response) {
                    // Show success message with SweetAlert2
                    Swal.fire({
                        icon: 'success',
                        title: 'Success!',
                        text: response.message || 'Los datos se han guardado correctamente.',
                    });

                    $("#btn-report-cancel").removeClass("disabled");
                    $("#btn-report-submit").removeClass("loading disabled");
                },
                error: function(xhr) {
                    // Show error message with SweetAlert2
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: xhr.responseJSON.message || 'Se produjo un error al procesar su solicitud.',
                    });

                    $("#downloadPdfBtn").removeClass("disabled");
                    $("#btn-report-cancel").removeClass("disabled");
                    $("#btn-report-submit").removeClass("loading disabled");
                }
            });
        });

        $('#downloadPdfBtn').on('click', function () {
            const button = $(this);
            const originalText = button.html();

            // Deshabilitar botón y mostrar spinner
            button.prop('disabled', true);
            button.html('<i class="spinner loading icon"></i> Generando PDF...');

            const form = $('#officeForm');
            const url = '{{ secure_url("/servicio/{$activity->id}/social_security_providers/preview-pdf") }}';

            // Crear y enviar formulario dinámico
            const downloadForm = document.createElement('form');
            downloadForm.method = 'POST';
            downloadForm.action = url;
            downloadForm.style.display = 'none';

            // CSRF token
            const csrfInput = document.createElement('input');
            csrfInput.type = 'hidden';
            csrfInput.name = '_token';
            csrfInput.value = '{{ csrf_token() }}';
            downloadForm.appendChild(csrfInput);

            // Agregar campos del formulario original
            form.serializeArray().forEach(function (field) {
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = field.name;
                input.value = field.value;
                downloadForm.appendChild(input);
            });

            document.body.appendChild(downloadForm);
            downloadForm.submit();
            document.body.removeChild(downloadForm);

            // Rehabilitar botón después de un pequeño delay
            setTimeout(function () {
                button.prop('disabled', false);
                button.html(originalText);
            }, 2000); // Opcional: ajustar tiempo según necesidad
        });

        $('#downloadPdfReimbursementBtn').on('click', function () {
            const button = $(this);
            const originalText = button.html();

            // Deshabilitar botón y mostrar spinner
            button.prop('disabled', true);
            button.html('<i class="spinner loading icon"></i> Generando PDF...');

            const form = $('#officeForm');
            const url = '{{ secure_url("/servicio/{$activity->id}/social_security_providers/preview-reimbursement-pdf") }}';

            // Crear y enviar formulario dinámico
            const downloadForm = document.createElement('form');
            downloadForm.method = 'POST';
            downloadForm.action = url;
            downloadForm.style.display = 'none';

            // CSRF token
            const csrfInput = document.createElement('input');
            csrfInput.type = 'hidden';
            csrfInput.name = '_token';
            csrfInput.value = '{{ csrf_token() }}';
            downloadForm.appendChild(csrfInput);

            // Agregar campos del formulario original
            form.serializeArray().forEach(function (field) {
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = field.name;
                input.value = field.value;
                downloadForm.appendChild(input);
            });

            document.body.appendChild(downloadForm);
            downloadForm.submit();
            document.body.removeChild(downloadForm);

            // Rehabilitar botón después de un pequeño delay
            setTimeout(function () {
                button.prop('disabled', false);
                button.html(originalText);
            }, 2000); // Opcional: ajustar tiempo según necesidad
        });
    </script>

    <script>
        $(document).ready(function() {
            $('#form-social-security-providers').on('submit', function(e) {
                e.preventDefault(); // Detiene el envío por defecto
                loadingMain(true);

                const resultList = [];
                $('#preInvoiceTable tbody tr').each(function (index) {
                    let result = $(this).find('select[name^="results["]').val();
                    resultList.push(result);
                });
                const resultCount = resultList.filter(item => item === "rechazado").length;

                let resultMessage = resultCount ? "Tiene (" + resultCount + ") facturas rechazadas" : '';

                Swal.fire({
                    title: '¿Estás seguro?',
                    text: resultMessage + " ¿Deseas enviar este formulario?",
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonText: 'Sí, enviar',
                    cancelButtonText: 'Cancelar'
                }).then((result) => {
                    if (result.isConfirmed) {
                        this.submit();
                    } else {
                        loadingMain(false);
                    }
                });
            });
        });
    </script>

    <!-- Validaciones del formulario-->
    <script>
        /*document.getElementById('form-social-security-providers').addEventListener('submit', function (event) {
            // Prevent form submission temporarily
            loadingMain(true);
            event.preventDefault();
            // Si el formulario no es válido, no enviarlo
            if (!validateRequiredFields($(this).attr('id'))) {
                loadingMain(false);
                return false;
            } else {
                this.submit();
            }
        });*/

        // Validaciones del formulario campos requeridos
        function validateRequiredFields(formId) {
            loadingMain(true);
            const invalidFieldsBySection = {};
            const form = document.getElementById(formId);
            if (!form) {
                console.error('Form not found');
                loadingMain(false);
                return false; // Return false if the form is not found
            }

            const titles = form.querySelectorAll('.title'); // Selección de elementos por clase .title

            titles.forEach(title => {
                const sectionTitle = title.textContent.trim(); // Título de la sección
                const content = title.nextElementSibling; // Obtener el siguiente div con el contenido

                // Omitir la validación si es la sección "DATOS DEL CASO"
                if (sectionTitle === "Datos del caso") return;

                invalidFieldsBySection[sectionTitle] = [];

                // Validar campos requeridos generales
                const requiredFields = content.querySelectorAll('.field.required textarea, .field.required input[type="text"], .field.required input[type="hidden"]');

                requiredFields.forEach(field => {
                    // Ignorar el campo de lateralidad
                    if (field.name === "diagnostics[laterality][]") return;

                    // Validar inputs de tipo datepicker o timepicker, solo el input oculto
                    if (field.classList.contains('datepicker') || field.classList.contains('timepicker')) {
                        const hiddenInput = content.querySelector(`input[type="hidden"][name="${field.name}_submit"]`);
                        if (hiddenInput && !hiddenInput.value) {
                            const label = field.closest('.field').querySelector('label');
                            if (label && !invalidFieldsBySection[sectionTitle].includes(label.textContent.trim())) {
                                invalidFieldsBySection[sectionTitle].push(label.textContent.trim());
                            }
                        }
                        return; // Salir de la iteración para evitar validar el input visible
                    }

                    // Validar textarea y input de texto, pero no el oculto
                    if ((field.tagName === 'TEXTAREA' && field.value.trim() === '') ||
                        (field.tagName === 'INPUT' && field.type !== 'hidden' && field.value.trim() === '')) {
                        const label = field.closest('.field').querySelector('label');
                        if (label && !invalidFieldsBySection[sectionTitle].includes(label.textContent.trim())) {
                            invalidFieldsBySection[sectionTitle].push(label.textContent.trim());
                        }
                    }

                    // Validar input oculto (como en el canal de consulta)
                    if (field.type === 'hidden' && !field.value) {
                        const label = field.closest('.field').querySelector('label');
                        if (label && !invalidFieldsBySection[sectionTitle].includes(label.textContent.trim())) {
                            invalidFieldsBySection[sectionTitle].push(label.textContent.trim());
                        }
                    }
                });
            });

            // Crear el mensaje de alerta utilizando el formato requerido
            let message = Object.entries(invalidFieldsBySection)
                .filter(([, fields]) => fields.length > 0)
                .map(([section, fields]) => `<strong>${section}:</strong><br>- ${fields.join('<br>- ')}`)
                .join('<br><br>');

            // Mostrar SweetAlert si hay campos inválidos
            if (message) {
                loadingMain(false);
                Swal.fire({
                    icon: 'error',
                    title: 'Campos requeridos',
                    html: `<div style="max-height: 300px; overflow-y: auto;">Por favor, completa correctamente los siguientes campos:<br><br>${message}</div>`,
                    confirmButtonText: 'Aceptar',
                     confirmButtonColor: '#91c845'
                });
                return false; // Si hay campos inválidos, no se debe enviar el formulario
            }

            // Si no hay campos inválidos, proceder a verificar resultados
            let resultPreInvoice = $('#resultPreInvoice').val();


            // Si se procede a rechazar prefactura
            if (resultPreInvoice === 'rechazado') {
                let reasonEmpty = false; // Flag para verificar si hay un motivo vacío

                $('select[name^="results"]').each(function () {
                    if ($(this).val() == 'rechazado') {
                        let rejectionInputPreInvoice = $(this).closest('tr').find('.rejection_reason_preinvoice');

                        // Verifica si el campo de razón de rechazo está vacío
                        if (rejectionInputPreInvoice.val().trim() === '') {
                            reasonEmpty = true; // Hay un motivo vacío
                        }
                    }
                });

                if (reasonEmpty) {
                    loadingMain(false);
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: 'Todos los resultados rechazados deben tener un motivo de rechazo.',
                    });
                    return false; // No se debe enviar el formulario
                }
            }

            //proceder a verificar resultados
            let resultElectronicInvoice = $('#result_electronic_invoice').val();

            // Si se procede a rechazar factura electrónica
            if (resultElectronicInvoice === 'return-electronic-invoice') {
                let reasonEmpty = false; // Flag para verificar si hay un motivo vacío

                $('select[name^="electronic_results"]').each(function () {
                    if ($(this).val() == 'rechazado') {
                        let rejectionInput = $(this).closest('tr').find('.rejection_reason');

                        // Verifica si el campo de razón de rechazo está vacío
                        if (rejectionInput.val().trim() === '') {
                            reasonEmpty = true; // Hay un motivo vacío
                        }
                    }
                });

                if (reasonEmpty) {
                    loadingMain(false);
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: 'Todos los resultados rechazados deben tener un motivo de rechazo.',
                    });
                    return false; // No se debe enviar el formulario
                }
            }
        
            return true; // Si todos los campos son válidos
        }
    </script>
@endsection