@extends('table.provider.menu.provider_data')

@section('title_section', $invoiceTypeTitle)

@section('content_provider_data')

    <form id="uploadForm" action="{{ secure_url('/proveedor/pss/ordinary_invoices') }}" method="post" enctype="multipart/form-data" style="margin-top: 25px;">
        {{ csrf_field() }}
        <input type="hidden" name="invoice_type" value="{{ $invoiceType }}">
        <input type="hidden" name="invoice_subtype" value="{{ $invoice_sub_type }}">
        <div class="ui basic segment">
            <div class="ui styled fluid accordion">
                <div class="active title">
                    <i class="dropdown icon"></i>
                    {{ $invoice_type_subtitle }}
                </div>
                <div class="active content">

                    <!-- Mostrar errores -->
                    <div class="ui negative message hidden" id="error-messages">
                        <div class="header">Errores encontrados</div>
                        <ul class="list">
                            <li>Error</li>
                        </ul>
                    </div>

                    <div class="ui fluid segments form">
                        {{-- Sección para elegir la cuenta IBAN --}}
                        <div class="ui segment">
                            <div class="required field" id="iban_account_field">
                                <label class="item-label">Cuenta IBAN</label>
                                <select name="iban_account" id="iban_account" class="ui search dropdown">
                                    <option value="">Seleccione uno</option>
                                    @foreach($ibanAccounts as $ibanAccount)
                                        <option value="{{ $ibanAccount->currency.'-'.$ibanAccount->iban }}">
                                            {{ $ibanAccount->currency }} - {{ $ibanAccount->iban }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        
                        {{-- Sección para cargar los soportes de facturación en (ZIP) --}}
                        <div class="ui segement">
                            <div class="active title">
                                <i class="dropdown icon"></i>
                                Cargar soportes de facturación (ZIP)<span style="color: red;" class="required">*</span>
                            </div>
                            <div class="active content">
                                <div class="ui segment center aligned" id="invoce_zip_file_field">
                                    <i class="huge file archive outline icon"></i>
                                    <p id="message-zip-file">No has subido un archivo</p>

                                    <!-- Input de archivo Excel oculto -->
                                    <label for="invoce_zip_file" class="ui icon secondary button">
                                        <i class="upload icon"></i>
                                        <input type="file" id="invoce_zip_file" name="invoce_zip_file"
                                               accept=".zip"
                                               style="display: none;" onchange="uploadZipFile();">
                                    </label>
                                </div>
                            </div>
                        </div>

                        {{-- Sección para cargar los soportes de facturación en (ZIP) --}}
                        <div class="ui segement">
                            <div class="active title">
                                <i class="dropdown icon"></i>
                                Cargar {{ $invoice_type_subtitle }} (EXCEL)<span style="color: red;" class="required">*</span>
                            </div>
                            <div class="active content">
                                <div class="ui segment center aligned" id="invoce_excel_file_field">
                                    <i class="huge file excel outline icon"></i>
                                    <p id="message-excel-file">No has subido un archivo</p>

                                    <!-- Input de archivo Excel oculto -->
                                    <label for="invoce_excel_file" class="ui icon secondary button">
                                        <i class="upload icon"></i>
                                        <input type="file" id="invoce_excel_file" name="invoce_excel_file"
                                               accept=".xlsx, .xls"
                                               style="display: none;" onchange="uploadExcelFile();">
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Botones de acción -->
                        <div class="ui segment">
                            <div class="fields">
                                <div class="eight wide field">
                                    <button type="submit" class="ui primary fluid button icon" id="btn-upload">
                                        <i class="save icon"></i> Cargar documentos
                                    </button>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </form>
    <style>
        .ui.segment {
            padding: 1.5rem !important;
        }

        .ui.segment.center.aligned {
            padding: 2rem !important;
            border: 1px solid #ddd;
        }

        .ui.segment .huge.file.icon {
            margin-bottom: 1rem;
        }

        .file-input-wrapper {
            margin-bottom: 1rem;
        }

        .grid > .column:first-child {
            padding-left: 0.3rem !important;
        }
    </style>

    {{--Validaciones y envío del formulario--}}
    <script>
        $('#uploadForm').submit(function (e) {
            // verificar si el campo IBAN está seleccionado
            const ibanAccount = document.getElementById('iban_account');
            if (ibanAccount.value === '') {
                e.preventDefault();
                Swal.fire({
                    title: 'Campo necesario',
                    text: 'Debe seleccionar una cuenta IBAN',
                    icon: 'warning',
                    confirmButtonText: 'Aceptar',
                });
                return;
            }

            // Verificar si el archivo Zip está presente
            const zipInput = document.getElementById('invoce_zip_file');
            if (!zipInput.files.length) {
                e.preventDefault();
                Swal.fire({
                    title: 'Campo necesario',
                    text: 'Debe adjuntar el archivo ZIP de los soportes de facturación',
                    icon: 'warning',
                    confirmButtonText: 'Aceptar',
                });
                return;
            }

            // Verificar si el archivo Excel está presente
            const excelInput = document.getElementById('invoce_excel_file');
            if (!excelInput.files.length) {
                e.preventDefault();
                Swal.fire({
                    title: 'Campo necesario',
                    text: 'Debe adjuntar el archivo EXCEL de la {{ $invoice_type_subtitle }}',
                    icon: 'warning',
                    confirmButtonText: 'Aceptar',
                });
                return;
            }

            e.preventDefault(); // Previene el envío normal del formulario
            // Desactiva los campos y el botón de carga
            $('#btn-upload').addClass('loading disabled');
            $("#iban_account_field").addClass("disabled");
            $("#invoce_zip_file_field").addClass("disabled");
            $("#invoce_excel_file_field").addClass("disabled");

            var formData = new FormData(this); // Crea el FormData con todos los campos y archivos

            $.ajax({
                url: $(this).attr('action'), // Usa la acción del formulario
                type: $(this).attr('method'), // Usa el método del formulario
                data: formData,
                processData: false, // Evita que jQuery transforme los datos
                contentType: false, // Deja que el navegador ponga el Content-Type adecuado
                success: function(response) {
                    if (response.success) {
                        $('#error-messages .list').empty();
                        $('#error-messages').addClass('hidden');

                        // Limpiar los campos del formulario
                        $('#iban_account').val('').change();

                        document.getElementById('message-zip-file').value = '';
                        document.getElementById('message-zip-file').innerText = 'No has subido un archivo';
                        document.getElementById('message-zip-file').style.color = '';

                        document.getElementById('message-excel-file').value = '';
                        document.getElementById('message-excel-file').innerText = 'No has subido un archivo';
                        document.getElementById('message-excel-file').style.color = '';
                        
                        Swal.fire({
                            icon: 'success',
                            title: 'Éxito',
                            html: "{{ $invoiceTypeSuccessMessage }}",
                            confirmButtonText: 'Aceptar',
                            allowOutsideClick: false,  // No permitir cerrar haciendo clic fuera
                            allowEscapeKey: false      // No permitir cerrar con ESC
                        }).then((result) => {
                            if (result.isConfirmed) {
                                $btnSubmit = $('#btn-upload').addClass('loading disabled');
                                window.location.href = "{{ secure_url('/proveedor/tablero_cuentas_medicas') }}";
                            }
                        });

                    } else {
                        $('#error-messages .list').empty();
                        const listaErrores = response.errors || [];
                        // Si hay errores nuevos, los agrega
                        if (listaErrores.length > 0) {
                            listaErrores.forEach(function(error) {
                                $('#error-messages .list').append('<li>' + error + '</li>');
                            });

                            // Muestra el div
                            $('#error-messages').removeClass('hidden');
                        } else {
                            // Si no hay errores, oculta el div
                            $('#error-messages').addClass('hidden');
                        }
                    }

                    // Reactiva los campos y el botón de carga
                    $('#btn-upload').removeClass('loading disabled');
                    $("#iban_account_field").removeClass("disabled");
                    $("#invoce_zip_file_field").removeClass("disabled");
                    $("#invoce_excel_file_field").removeClass("disabled");
                },
                error: function(xhr, status, error) {
                    $('#error-messages').removeClass('hidden');
                    if (xhr.status === 422) { // Código de estado para errores de validación
                        var errors = xhr.responseJSON.errors;
                        var errorList = '<ul>';
                        $.each(errors, function(field, messages) {
                            $.each(messages, function(index, message) {
                                errorList += '<li>' + message + '</li>';
                                $('#error-messages .list').append('<li>' + message + '</li>');
                            });
                        });
                    } else {
                        // Manejar otros tipos de errores
                        $('#error-messages .list').append('<li>Ocurrió un error inesperado.</li>');
                    }

                    // Reactiva los campos y el botón de carga
                    $('#btn-upload').removeClass('loading disabled');
                    $("#iban_account_field").removeClass("disabled");
                    $("#invoce_zip_file_field").removeClass("disabled");
                    $("#invoce_excel_file_field").removeClass("disabled");
                }
            });
        });
    </script>

    {{-- Helpers --}}
    <script>
        function uploadZipFile() {
            const input = document.getElementById('invoce_zip_file');
            const file = input.files[0];

            const allowedExtensions = ['application/zip', 'application/x-zip-compressed', 'application/x-compressed'];

            if (!allowedExtensions.includes(file.type)) {
                Swal.fire({
                    title: 'Error',
                    text: 'Por favor, sube un archivo de tipo ZIP (.zip).',
                    icon: 'error',
                    confirmButtonText: 'Aceptar'
                });
                input.value = ''; // Limpiar el campo si el archivo no es válido
                document.getElementById('message-zip-file').innerText = 'No has subido un archivo';
                document.getElementById('message-zip-file').style.color = '';
                return; // Detener el proceso si el archivo no es válido
            }

            if (file) {
                document.getElementById('message-zip-file').innerText = file.name;
                document.getElementById('message-zip-file').style.color = 'green';
            } else {
                Swal.fire({
                    title: 'Advertencia',
                    text: 'Por favor, sube un archivo ZIP.',
                    icon: 'warning',
                    confirmButtonText: 'Aceptar',
                });
                document.getElementById('message-zip-file').innerText = 'No has subido un archivo';
                document.getElementById('message-zip-file').style.color = '';
            }
        }

        function uploadExcelFile() {
            const input = document.getElementById('invoce_excel_file');
            const file = input.files[0];

            const allowedExtensions = ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/vnd.ms-excel'];

            if (!allowedExtensions.includes(file.type)) {
                Swal.fire({
                    title: 'Error',
                    text: 'Por favor, sube un archivo de tipo ZIP (.zip).',
                    icon: 'error',
                    confirmButtonText: 'Aceptar'
                });
                input.value = ''; // Limpiar el campo si el archivo no es válido
                document.getElementById('message-excel-file').innerText = 'No has subido un archivo';
                document.getElementById('message-excel-file').style.color = '';
                return; // Detener el proceso si el archivo no es válido
            }

            if (file) {
                document.getElementById('message-excel-file').innerText = file.name;
                document.getElementById('message-excel-file').style.color = 'green';
            } else {
                Swal.fire({
                    title: 'Advertencia',
                    text: 'Por favor, sube un archivo Excel.',
                    icon: 'warning',
                    confirmButtonText: 'Aceptar',
                });
                document.getElementById('message-excel-file').innerText = 'No has subido un archivo';
                document.getElementById('message-excel-file').style.color = '';
            }
        }
    </script>
@endsection