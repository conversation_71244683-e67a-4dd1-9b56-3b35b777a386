@extends('layouts.main')

@section('title', 'PQR')

@section('menu')
    @parent
@endsection

@section('content')
    <div class="ui basic segment">
        <h1 class="ui header">
            Respuesta PQR
            <div class="sub header">Campos con <span style="color: red;" class="required">*</span> obligatorios.</div>
        </h1>
        <form autocomplete="off" action="{{secure_url("servicio/{$activity->id}/pqr/save")}}" id="dictamen"
              method="post" class="ui small form">
            <div class="ui secondary segment">
                <div class="ui three columns grid">
                    <div class="column">
                        <b>Identificación:</b> {{$activity->affiliate->doc_type}} {{$activity->affiliate->doc_number}}
                    </div>
                    <div class="column"><b>Nombre:</b> <a
                                href="{{secure_url('afiliado/' . $activity->affiliate_id)}}">{{$activity->affiliate->full_name}}</a>
                    </div>
                    <div class="column"><b>Actividad:</b> <a
                                href="{{secure_url('servicio/' . $activity->id)}}">{{$activity->service->name}}</a>
                    </div>
                </div>
            </div>

            <div style="margin: 10px;">
                <div class="ui toggle checkbox">
                    <input name="cloned" type="checkbox"
                           value="1" {{$activity->pqr && $activity->pqr->cloned ? 'checked' : ''}}>
                    <label>Clonado</label>
                </div>
            </div>
            <div class="ui styled fluid accordion">

                <!-- DIAGNOSTICS -->
                <div id="95" class="title">
                    <i class="dropdown icon"></i>Diagnósticos
                </div>
                <div class="content small form">
                    <div class="ui basic right aligned segment">
                        <a href="#" onclick="$('.jr.field').toggle();" class="ui blue label"><i class="unhide icon"></i>
                            Mostrar/Ocultar JR</a>
                        <a href="#" onclick="$('.jn.field').toggle();" class="ui blue label"><i class="unhide icon"></i>
                            Mostrar/Ocultar JN</a>
                    </div>
                    <div style="margin-bottom: 5px;" class="fields">
                        <!--
                        <div class="two wide field">
                            <label>Cód.</label>
                        </div>
                        <div class="eight wide field">
                            <label>Descripción</label>
                        </div>
                        <div class="three wide field">
                            <label>Fecha del diagnostico</label>
                        </div>
                        <div class="two wide field">
                            <label>Origen</label>
                        </div>
                        <div class="two wide field">
                            <label>Primer calificador</label>
                        </div>
                        <div class="jr two wide field">
                            <label>Origen según JR</label>
                        </div>
                        <div class="jn two wide field">
                            <label>Origen según JN</label>
                        </div>
                        <div class="two wide field">
                            <label>En Firme</label>
                        </div>
                        -->
                        <div class="ten wide field"></div>
                        <div class="nine wide field"></div>
                        <div class="one wide field">
                            <a style="margin-top: -15px;" onclick="addDiagnostic()"
                               class="ui basic small icon blue button"><i class="add icon"></i></a>
                        </div>
                    </div>
                    <div id="diagnostics">
                        @if ($activity->pqr && count($activity->pqr->diagnostics) > 0)
                            @foreach ($activity->pqr->diagnostics as $diagnostic)
                                <div class="group-two-fields">
                                    <div class="fields">
                                        <input type="hidden" name="diagnostics[id][]" value="{{$diagnostic->id}}">
                                        <div class="two wide field">
                                            <label>Cód.</label>
                                            <div class="ui search code">
                                                <div class="ui icon input">
                                                    <input class="prompt" name="diagnostics[cod][]" type="text"
                                                           value="{{$diagnostic->code}}">
                                                    <i class="search icon"></i>
                                                </div>
                                                <div class="results"></div>
                                            </div>
                                        </div>
                                        <div class="eight wide field">
                                            <label>Nombre</label>
                                            <input class="prompt" name="diagnostics[description][]" type="text"
                                                   maxlength="500" value="{{$diagnostic->description}}" readonly>
                                        </div>
                                        <div class="three wide field">
                                            <label>Fecha del diagnostico</label>
                                            <input name="diagnostics[d_date][]" type="text" class="datepicker"
                                                   data-value="{{$diagnostic->d_date}}">
                                        </div>
                                        <div class="two wide field">
                                            <label>Origen</label>
                                            <div class="ui fluid selection dropdown">
                                                <input name="diagnostics[origin][]" type="hidden"
                                                       value="{{$diagnostic->origin}}">
                                                <i class="dropdown icon"></i>
                                                <div class="default text">Origen</div>
                                                <div class="menu">
                                                    @foreach($ORIGINS as $k => $v)
                                                        <div class="item" data-value="{{$k}}">{{$v}}</div>
                                                    @endforeach
                                                </div>
                                            </div>
                                        </div>
                                        <div class="two wide field">
                                            <label>Primer calificador</label>
                                            <div class="ui fluid selection dropdown">
                                                <input name="diagnostics[qualifier][]" type="hidden"
                                                       value="{{$diagnostic->qualifier}}">
                                                <i class="dropdown icon"></i>
                                                <div class="default text">Calificador</div>
                                                <div class="menu">
                                                    @foreach($QUALIFIERS as $k => $v)
                                                        <div class="item" data-value="{{$k}}">{{$v}}</div>
                                                    @endforeach
                                                </div>
                                            </div>
                                        </div>
                                        <div class="jr two wide field">
                                            <label>Origen según JR</label>
                                            <div class="ui fluid selection dropdown">
                                                <input name="diagnostics[origin_jr][]" type="hidden"
                                                       value="{{$diagnostic->origin_jr}}">
                                                <i class="dropdown icon"></i>
                                                <div class="default text">Origen</div>
                                                <div class="menu">
                                                    <div class="item" data-value="">&nbsp;</div>
                                                    @foreach($ORIGINS as $k => $v)
                                                        <div class="item" data-value="{{$k}}">{{$v}}</div>
                                                    @endforeach
                                                </div>
                                            </div>
                                        </div>
                                        <div class="jn two wide field">
                                            <label>Origen según JN</label>
                                            <div class="ui fluid selection dropdown">
                                                <input name="diagnostics[origin_jn][]" type="hidden"
                                                       value="{{$diagnostic->origin_jn}}">
                                                <i class="dropdown icon"></i>
                                                <div class="default text">Origen</div>
                                                <div class="menu">
                                                    <div class="item" data-value="">&nbsp;</div>
                                                    @foreach($ORIGINS as $k => $v)
                                                        <div class="item" data-value="{{$k}}">{{$v}}</div>
                                                    @endforeach
                                                </div>
                                            </div>
                                        </div>
                                        <div class="two wide field">
                                            <label>En Firme</label>
                                            @if ($diagnostic->origin_firm)
                                                <i class="checkmark green icon"></i>
                                                {{$diagnostic->firm_date->formatLocalized('%B %d, %Y')}}
                                            @endif
                                        </div>
                                        <div class="one wide field">
                                            <a class="ui red small icon basic button"><i class="remove icon"></i></a>
                                        </div>
                                    </div>
                                    <div class="fields">
                                        <div class="eight wide field">
                                            <label>Descripción</label>
                                            <input class="prompt" name="diagnostics[description_editable][]" type="text"
                                                   value="{{$diagnostic->description_editable}}">
                                        </div>
                                        <div class="two wide field">
                                            <label>Lateralidad</label>
                                            <div class="ui fluid selection dropdown">
                                                <input name="diagnostics[laterality][]" type="hidden"
                                                       value="{{$diagnostic->laterality}}">
                                                <i class="dropdown icon"></i>
                                                <div class="default text">Lateralidad</div>
                                                <div class="menu">
                                                    @foreach($LATERALITY as $k => $v)
                                                        <div class="item" data-value="{{$k}}">{{$v}}</div>
                                                    @endforeach
                                                </div>
                                            </div>
                                        </div>
                                        <div class="three wide field">
                                            <label>Tipo de Evento</label>
                                            <div class="ui fluid selection dropdown">
                                                <input name="diagnostics[type_event][]" type="hidden"
                                                       value="{{$diagnostic->type_event}}">
                                                <i class="dropdown icon"></i>
                                                <div class="default text">Tipo de Evento</div>
                                                <div class="menu">
                                                    <div class="item" data-value="AT">AT</div>
                                                    <div class="item" data-value="EL">EL</div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="three wide field">
                                            <label>Nro Siniestro</label>
                                            <input name="diagnostics[sinister_num][]" type="text"
                                                   value="{{$diagnostic->sinister_num}}">
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        @else
                            <div class="group-two-fields">
                                <div class="fields">
                                    <input type="hidden" name="diagnostics[id][]">
                                    <div class="two wide field">
                                        <label>Cód.</label>
                                        <div class="ui search code">
                                            <div class="ui icon input">
                                                <input class="code prompt" name="diagnostics[cod][]" type="text">
                                                <i class="search icon"></i>
                                            </div>
                                            <div class="results"></div>
                                        </div>
                                    </div>
                                    <div class="eight wide field">
                                        <label>Nombre</label>
                                        <input class="description prompt" name="diagnostics[description][]" type="text"
                                               maxlength="500" readonly>
                                    </div>
                                    <div class="three wide field">
                                        <label>Fecha del diagnostico</label>
                                        <input name="diagnostics[d_date][]" type="text" class="datepicker">
                                    </div>
                                    <div class="two wide field">
                                        <label>Origen</label>
                                        <div class="ui fluid selection dropdown">
                                            <input name="diagnostics[origin][]" type="hidden">
                                            <i class="dropdown icon"></i>
                                            <div class="default text">Origen</div>
                                            <div class="menu">
                                                @foreach($ORIGINS as $k => $v)
                                                    <div class="item" data-value="{{$k}}">{{$v}}</div>
                                                @endforeach
                                            </div>
                                        </div>
                                    </div>
                                    <div class="two wide field">
                                        <label>Primer calificador</label>
                                        <div class="ui fluid selection dropdown">
                                            <input name="diagnostics[qualifier][]" type="hidden">
                                            <i class="dropdown icon"></i>
                                            <div class="default text">Calificador</div>
                                            <div class="menu">
                                                @foreach($QUALIFIERS as $k => $v)
                                                    <div class="item" data-value="{{$k}}">{{$v}}</div>
                                                @endforeach
                                            </div>
                                        </div>
                                    </div>
                                    <div class="jr two wide field">
                                        <label>Origen según JR</label>
                                        <div class="ui fluid selection dropdown">
                                            <input name="diagnostics[origin_jr][]" type="hidden">
                                            <i class="dropdown icon"></i>
                                            <div class="default text">Origen</div>
                                            <div class="menu">
                                                <div class="item" data-value="">&nbsp;</div>
                                                @foreach($ORIGINS as $k => $v)
                                                    <div class="item" data-value="{{$k}}">{{$v}}</div>
                                                @endforeach
                                            </div>
                                        </div>
                                    </div>
                                    <div class="jn two wide field">
                                        <label>Origen según JN</label>
                                        <div class="ui fluid selection dropdown">
                                            <input name="diagnostics[origin_jn][]" type="hidden">
                                            <i class="dropdown icon"></i>
                                            <div class="default text">Origen</div>
                                            <div class="menu">
                                                <div class="item" data-value="">&nbsp;</div>
                                                @foreach($ORIGINS as $k => $v)
                                                    <div class="item" data-value="{{$k}}">{{$v}}</div>
                                                @endforeach
                                            </div>
                                        </div>
                                    </div>
                                    <div class="two wide field">
                                        <label>En Firme</label>
                                    </div>
                                    <div class="one wide field"></div>
                                </div>
                                <div class="fields">
                                    <div class="eight wide field">
                                        <label>Descripción</label>
                                        <input class="description prompt" name="diagnostics[description_editable][]"
                                               type="text">
                                    </div>
                                    <div class="two wide field">
                                        <label>Lateralidad</label>
                                        <div class="ui fluid selection dropdown">
                                            <input name="diagnostics[laterality][]" type="hidden">
                                            <i class="dropdown icon"></i>
                                            <div class="default text">Lateralidad</div>
                                            <div class="menu">
                                                @foreach($LATERALITY as $k => $v)
                                                    <div class="item" data-value="{{$k}}">{{$v}}</div>
                                                @endforeach
                                            </div>
                                        </div>
                                    </div>
                                    <div class="three wide field">
                                        <label>Tipo de Evento</label>
                                        <div class="ui fluid selection dropdown">
                                            <input name="diagnostics[type_event][]" type="hidden">
                                            <i class="dropdown icon"></i>
                                            <div class="default text">Tipo de Evento</div>
                                            <div class="menu">
                                                <div class="item" data-value="AT">AT</div>
                                                <div class="item" data-value="EL">EL</div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="three wide field">
                                        <label>Nro Siniestro</label>
                                        <input name="diagnostics[sinister_num][]" type="text">
                                    </div>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
                <!-- END: DIAGNOSTICS -->

                <!-- PLAINTIFFS -->
                <div id="96" class="title">
                    <i class="dropdown icon"></i>DATOS DEL ASEGURADO Y SOLICITUD <span style="color: red;"
                                                                                       class="required">*</span>
                </div>
                <div class="content form">
                    <div class="fields">
                        <div class="four wide field required">
                            <label>Solicitante</label>
                            <div class="ui fluid selection dropdown">
                                <input name="plaintiff_applicant" id="plaintiff_applicant" type="hidden"
                                       value="{{$activity->pqr ? $activity->pqr->plaintiff_applicant : ''}}">
                                <i class="dropdown icon"></i>
                                <div class="default text">Solicitante</div>
                                <div class="menu">
                                    <div class="item" data-value="Afiliado">Afiliado/Entidad</div>
                                    <div class="item" data-value="Otro">Otro</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="fields">
                        <div class="four wide field">
                            <label>Tipo de Identificación</label>
                            <div class="ui fluid selection dropdown" id="dropdown_plaintiff_type_doc">
                                <input name="plaintiff_type_doc" type="hidden"
                                       value="{{$activity->pqr ? $activity->pqr->plaintiff_type_doc : ''}}">
                                <i class="dropdown icon"></i>
                                <div class="default text">Tipo de Identificación</div>
                                <div class="menu">
                                    @foreach($DOC_TYPES as $k => $v)
                                        <div class="item" data-value="{{$k}}">{{$v}}</div>
                                    @endforeach
                                    <div class="item" data-value="NIT">NIT</div>
                                </div>
                            </div>
                        </div>
                        <div class="four wide field">
                            <label>Número de Identificación</label>
                            <input name="plaintiff_num_doc" type="text"
                                   value="{{$activity->pqr ? $activity->pqr->plaintiff_num_doc : ''}}">
                        </div>
                        <div class="eight wide field">
                            <label>Nombre</label>
                            <input name="plaintiff_name" type="text"
                                   value="{{$activity->pqr ? $activity->pqr->plaintiff_name : ''}}">
                        </div>
                        <div class="four wide field">
                            <label>No. De Radicado</label>
                            <input name="plaintiffs_radicate" type="text"
                                   value="{{$activity->pqr ? $activity->pqr->plaintiffs_radicate : ''}}">
                        </div>
                        <div class="four wide field">
                            <label>Canal de entrada</label>
                            <div class="ui fluid selection dropdown">
                                <input name="plaintiffs_channel" type="hidden"
                                       value="{{$activity->pqr ? $activity->pqr->plaintiffs_channel : ''}}">
                                <i class="dropdown icon"></i>
                                <div class="default text">Canal de entrada</div>
                                <div class="menu">
                                    <div class="item" data-value="Correo electronico">Correo electronico</div>
                                    <div class="item" data-value="Punto de atención">Punto de atención</div>
                                    <div class="item" data-value="Superfinanciera">Superfinanciera</div>
                                    <div class="item" data-value="Super Salud">Super Salud</div>
                                    <div class="item" data-value="Defensor del consumidor">Defensor del consumidor</div>
                                    <div class="item" data-value="Otro">Otro</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="fields">
                        <div class="four wide field required">
                            <label>Número Siniestro</label>
                            <input name="plaintiff_sinister_num" type="text"
                                   value="{{$activity->pqr ? $activity->pqr->plaintiff_sinister_num : ''}}">
                        </div>
                        <div class="four wide field required">
                            <label>Fecha Siniestro</label>
                            <input name="plaintiff_sinister_date" type="text" class="datepicker"
                                   data-value="{{$activity->pqr ? $activity->pqr->plaintiff_sinister_date : ''}}">
                        </div>
                    </div>

                    <div class="fields">
                        <div class="four wide field">
                            <label>Tipo PQRD</label>
                            <div class="ui fluid selection dropdown">
                                <input name="plaintiff_type_pqrd" type="hidden"
                                       value="{{$activity->pqr ? $activity->pqr->plaintiff_type_pqrd : ''}}">
                                <i class="dropdown icon"></i>
                                <div class="default text">Tipo PQRD</div>
                                <div class="menu">
                                    <div class="item" data-value="Solicitud">Solicitud</div>
                                    <div class="item" data-value="Petición">Petición</div>
                                    <div class="item" data-value="Queja">Queja</div>
                                    <div class="item" data-value="Reclamo">Reclamo</div>
                                    <div class="item" data-value="Ente de Control">Ente de Control</div>
                                </div>
                            </div>
                        </div>
                        <div class="four wide field">
                            <label>Categoría</label>
                            <div class="ui fluid selection dropdown">
                                <input name="plaintiff_category" type="hidden"
                                       value="{{$activity->pqr ? $activity->pqr->plaintiff_category : ''}}">
                                <i class="dropdown icon"></i>
                                <div class="default text">Categoría</div>
                                <div class="menu">
                                    <div class="item" data-value="PCL">PCL</div>
                                    <div class="item" data-value="Revisión Pensión">Revisión Pensión</div>
                                    <div class="item" data-value="Origen">Origen</div>
                                    <div class="item" data-value="Autorización">Autorización</div>
                                    <div class="item" data-value="Red Asistencial">Red Asistencial</div>
                                    <div class="item" data-value="Medicamentos">Medicamentos</div>
                                </div>
                            </div>
                        </div>
                        <div class="four wide field">
                            <label>Oficina</label>
                            <div class="ui fluid selection dropdown">
                                <input name="plaintiff_office" type="hidden"
                                       value="{{$activity->pqr ? $activity->pqr->plaintiff_office : ''}}">
                                <i class="dropdown icon"></i>
                                <div class="default text">Categoría</div>
                                <div class="menu">

                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="four fields">
                        <div class="four wide field">
                            <label>Fecha de asignación a la oficina</label>
                            <input name="office_assignement_date" type="text" class="datepicker"
                                   data-value="{{$activity->pqr ? $activity->pqr->office_assignement_date : ''}}">
                        </div>
                        <div class="four wide field">
                            <label>Fecha máxima de respuesta</label>
                            <input name="max_answer_date" type="text" class="datepicker"
                                   data-value="{{$activity->pqr ? $activity->pqr->max_answer_date : ''}}">
                        </div>
                        <div class="four wide field">
                            <label>Canal de radicación</label>
                            <input name="radication_channel" type="text"
                                   value="{{$activity->pqr ? $activity->pqr->radication_channel : ''}}">
                        </div>
                        <div class="four wide field">
                            <label>Fecha máxima de escalamiento</label>
                            <input name="max_scale_date" readonly type="text"
                                   value="{{$activity->pqr ? $activity->pqr->max_scale_date : ''}}">
                        </div>
                    </div>

                    <div class="fields">
                        <div class="four wide field required plaintiff_applicant">
                            <label>Tipo de documento</label>
                            <div class="ui fluid selection dropdown">
                                <input type="hidden" name="plaintiff_applicant_doc_type"
                                       value="{{$activity->pqr ? $activity->pqr->plaintiff_applicant_doc_type : 'CC'}}">
                                <i class="dropdown icon"></i>
                                <div class="default text">Escoja una opción</div>
                                <div class="menu">
                                    @foreach($DOC_TYPES as $k => $v)
                                        <div class="item" data-value="{{$k}}">{{$v}}</div>
                                    @endforeach
                                    <div class="item" data-value="NIT">NIT</div>
                                </div>
                            </div>
                        </div>
                        <div class="four wide field required plaintiff_applicant">
                            <label>Número de Identificación</label>
                            <input name="plaintiff_applicant_doc_number" type="text"
                                   value="{{$activity->pqr ? $activity->pqr->plaintiff_applicant_doc_number : ''}}">
                        </div>
                    </div>
                    <div class="fields">
                        <div class="four wide required field plaintiff_applicant">
                            <label>Nombre Solicitante</label>
                            <input name="plaintiff_applicant_name" type="text"
                                   value="{{$activity->pqr ? $activity->pqr->plaintiff_applicant_name : ''}}">
                        </div>
                        <div class="four wide required field plaintiff_applicant">
                            <label>Direccion Solicitante</label>
                            <input name="plaintiff_applicant_address" type="text"
                                   value="{{$activity->pqr ? $activity->pqr->plaintiff_applicant_address : ''}}">
                        </div>
                        <div class="four wide required field plaintiff_applicant">
                            <label>Telefono Solicitante</label>
                            <input name="plaintiff_applicant_phone" type="text"
                                   value="{{$activity->pqr ? $activity->pqr->plaintiff_applicant_phone : ''}}">
                        </div>
                        <div class="four wide required field plaintiff_applicant">
                            <label>Ciudad Solicitante</label>
                            <input name="plaintiff_applicant_city" type="text"
                                   value="{{$activity->pqr ? $activity->pqr->plaintiff_applicant_city : ''}}">
                        </div>
                        <div class="four wide required field plaintiff_applicant">
                            <label>Email Solicitante</label>
                            <input name="plaintiff_applicant_email" type="text"
                                   value="{{$activity->pqr ? $activity->pqr->plaintiff_applicant_email : ''}}">
                        </div>
                    </div>
                </div>
                <!-- END: PLAINTIFFS -->

                <!-- ATTORNEY DATA -->
                <div id="97" class="title">
                    <i class="dropdown icon"></i> DATOS DEL APODERADO
                </div>
                <div class="content">
                    <div class="fields">
                        <div class="four wide field">
                            <label>Nombres</label>
                            <input name="attorney_name" type="text"
                                   value="{{$activity->pqr ? $activity->pqr->attorney_name : ''}}">
                        </div>
                        <div class="four wide field">
                            <label>Dirección</label>
                            <input name="attorney_address" type="text"
                                   value="{{$activity->pqr ? $activity->pqr->attorney_address : ''}}">
                        </div>
                        <div class="four wide field">
                            <label>Departamento</label>
                            <div id="departments" class="ui selection dropdown">
                                <input type="hidden" name="attorney_department"
                                       value="{{$activity->pqr ? $activity->pqr->attorney_department : ''}}">
                                <i class="dropdown icon"></i>
                                <div class="default text">Seleccione uno</div>
                                <div class="menu"></div>
                            </div>
                        </div>
                        <div class="four wide field">
                            <label>Municipio</label>
                            <div id="municipalities" class="ui selection dropdown">
                                <input type="hidden" name="attorney_municipality"
                                       value="{{$activity->pqr ? $activity->pqr->attorney_municipality : ''}}">
                                <i class="dropdown icon"></i>
                                <div class="default text">Seleccione uno</div>
                                <div class="menu"></div>
                            </div>
                        </div>
                    </div>
                    <div class="fields">
                        <div class="four wide field">
                            <label>Tipo de documento</label>
                            <div class="ui fluid selection dropdown">
                                <input type="hidden" name="attorney_doc_type"
                                       value="{{$activity->pqr ? $activity->pqr->attorney_doc_type : 'CC'}}">
                                <i class="dropdown icon"></i>
                                <div class="default text">Escoja una opción</div>
                                <div class="menu">
                                    @foreach($DOC_TYPES as $k => $v)
                                        <div class="item" data-value="{{$k}}">{{$v}}</div>
                                    @endforeach
                                    <div class="item" data-value="NIT">NIT</div>
                                </div>
                            </div>
                        </div>
                        <div class="four wide field">
                            <label>Número de Identificación</label>
                            <input name="attorney_doc_number" type="text"
                                   value="{{$activity->pqr ? $activity->pqr->attorney_doc_number : ''}}">
                        </div>
                        <div class="four wide field">
                            <label>No. Registro o Tarjeta Profesional</label>
                            <input name="attorney_record" type="text"
                                   value="{{$activity->pqr ? $activity->pqr->attorney_record : ''}}">
                        </div>
                    </div>
                    <div class="fields">
                        <div class="four wide field">
                            <label>Teléfono</label>
                            <input name="attorney_phone" type="text"
                                   value="{{$activity->pqr ? $activity->pqr->attorney_phone : ''}}">
                        </div>
                        <div class="four wide field">
                            <label>Celular</label>
                            <input name="attorney_cellphone" type="text"
                                   value="{{$activity->pqr ? $activity->pqr->attorney_cellphone : ''}}">
                        </div>
                        <div class="four wide field">
                            <label>Correo Electrónico</label>
                            <input name="attorney_email" type="text"
                                   value="{{$activity->pqr ? $activity->pqr->attorney_email : ''}}">
                        </div>
                    </div>
                </div>
                <!-- END: ATTORNEY DATA -->

                <!-- COURT DATA -->
                <!--
			<div class="title">
				<i class="dropdown icon"></i> Datos del Juzgado
			</div>
			<div class="content">
				<div class="fields">
					<div class="four wide field">
						<label>Nombre del Juzgado</label>
						<input name="court_name" type="text" value="{{$activity->pqr ? $activity->pqr->court_name : ''}}">
					</div>
					<div class="four wide field">
						<label>Departamento</label>
						<div id="departments2" class="ui selection dropdown">
							<input type="hidden" name="court_department" value="{{$activity->pqr ? $activity->pqr->court_department : ''}}">
							<i class="dropdown icon"></i>
							<div class="default text">Seleccione uno</div>
							<div class="menu"></div>
						</div>
					</div>
					<div class="four wide field">
						<label>Municipio</label>
						<div id="municipalities2" class="ui selection dropdown">
							<input type="hidden" name="court_municipality" value="{{$activity->pqr ? $activity->pqr->court_municipality : ''}}">
							<i class="dropdown icon"></i>
							<div class="default text">Seleccione uno</div>
							<div class="menu"></div>
						</div>
					</div>
				</div>
				<div class="fields">
					<div class="four wide field">
						<label>No. Expediente</label>
						<input name="court_file_number" type="text" value="{{$activity->pqr ? $activity->pqr->court_file_number : ''}}">
					</div>
					<div class="four wide field">
						<label>Tipo de Demanda</label>
						<input name="court_type_of_demand" type="text" value="{{$activity->pqr ? $activity->pqr->court_type_of_demand : ''}}">
					</div>
				</div>
			</div>
			-->
                <!-- END: COURT DATA -->

                <!-- REQUESTS -->
                <div id="98" class="title"><i class="dropdown icon"></i>SOLICITUDES</div>
                <div class="content small form">
                    <div style="margin-bottom: 5px;" class="fields">
                        <div class="four wide field"><label>Tipo de Solicitud</label></div>
                        <div class="six wide field"><label>Descripción</label></div>
                        <div class="three wide field"><label>Requiere apoyo de otro proveedor</label></div>
                        <div class="six wide field"><label>Escalado a</label></div>
                        <div class="one wide field">
                            <a style="margin-top: -15px;" onclick="return addRequest();"
                               class="ui basic small icon blue button"><i class="add icon"></i></a>
                        </div>
                    </div>
                    <div id="requests">
                        @if ($activity->pqr && count($activity->pqr->requests) > 0)
                            @foreach($activity->pqr->requests as $wh)
                                <div class="fields">
                                    <input type="hidden" name="request[id][]" value="{{$wh->id}}">
                                    <div class="four wide field">
                                        <div class="ui fluid selection dropdown">
                                            <input name="request[type][]" type="hidden" value="{{$wh->type}}">
                                            <i class="dropdown icon"></i>
                                            <div class="default text">Tipo</div>
                                            <div class="menu">
                                                <div class="item" data-value="Autorizaciones">Autorizaciones</div>
                                                <div class="item" data-value="Entrega de medicamentos">Entrega de
                                                    medicamentos
                                                </div>
                                                <div class="item" data-value="Calificación de enfermedad">Calificación
                                                    de enfermedad
                                                </div>
                                                <div class="item" data-value="Calificación de accidente">Calificación de
                                                    accidente
                                                </div>
                                                <div class="item"
                                                     data-value="Calificación de consecuencias patologicas">Calificación
                                                    de consecuencias patologicas
                                                </div>
                                                <div class="item" data-value="Calificación de PCL">Calificación de PCL
                                                </div>
                                                <div class="item" data-value="Revisión PCL">Revisión PCL</div>
                                                <div class="item" data-value="Controversias">Controversias</div>
                                                <div class="item" data-value="Otros">Otros</div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="six wide field">
                                        <textarea name="request[description][]"
                                                  rows="12">{{$wh->description}}</textarea>
                                    </div>
                                    <div class="three wide field">
                                        <div class="ui fluid selection dropdown">
                                            <input name="request[require_provider][]" type="hidden"
                                                   value="{{$wh->require_provider}}">
                                            <i class="dropdown icon"></i>
                                            <div class="default text"></div>
                                            <div class="menu">
                                                <div class="item" data-value="Si">Si</div>
                                                <div class="item" data-value="No">No</div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="six wide field">
                                        <textarea name="request[scaled][]" rows="12">{{$wh->scaled}}</textarea>
                                    </div>
                                    <div class="one wide field">
                                        <a class="ui red small icon basic button"><i class="remove icon"></i></a>
                                    </div>
                                </div>
                            @endforeach
                        @else
                            <div class="fields">
                                <input type="hidden" name="request[id][]">
                                <div class="four wide field">
                                    <div class="ui fluid selection dropdown">
                                        <input name="request[type][]" type="hidden">
                                        <i class="dropdown icon"></i>
                                        <div class="default text">Tipo</div>
                                        <div class="menu">
                                            <div class="item" data-value="Autorizaciones">Autorizaciones</div>
                                            <div class="item" data-value="Entrega de medicamentos">Entrega de
                                                medicamentos
                                            </div>
                                            <div class="item" data-value="Calificación de enfermedad">Calificación de
                                                enfermedad
                                            </div>
                                            <div class="item" data-value="Calificación de accidente">Calificación de
                                                accidente
                                            </div>
                                            <div class="item" data-value="Calificación de consecuencias patologicas">
                                                Calificación de consecuencias patologicas
                                            </div>
                                            <div class="item" data-value="Calificación de PCL">Calificación de PCL</div>
                                            <div class="item" data-value="Revisión PCL">Revisión PCL</div>
                                            <div class="item" data-value="Controversias">Controversias</div>
                                            <div class="item" data-value="Otros">Otros</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="six wide field">
                                    <textarea name="request[description][]" rows="12"></textarea>
                                </div>
                                <div class="three wide field">
                                    <div class="ui fluid selection dropdown">
                                        <input name="request[require_provider][]" type="hidden">
                                        <i class="dropdown icon"></i>
                                        <div class="default text"></div>
                                        <div class="menu">
                                            <div class="item" data-value="Si">Si</div>
                                            <div class="item" data-value="No">No</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="six wide field">
                                    <textarea name="request[scaled][]" rows="12"></textarea>
                                </div>
                                <div class="one wide field"></div>
                            </div>
                        @endif
                    </div>
                </div>
                <!-- END: REQUESTS -->

                <!-- ANALISIS -->
                <div id="99" class="title">
                    <i class="dropdown icon"></i> ANÁLISIS DEL CASO
                </div>
                <div class="content">
                    <div class="field">
                        <label>Análisis del caso</label>
                        <textarea name="analisis"
                                  rows="12">{{$activity->pqr ? $activity->pqr->analisis : ''}}</textarea>
                    </div>
                </div>
                <!-- END: ANALISIS -->

                <!-- CONTEXT -->
                <!--
			<div class="title">
				<i class="dropdown icon"></i> CONTEXTO DEL EVENTO
			</div>
			<div class="content">
				<div class="field">
					<label>Contexto</label>
					<textarea name="context" rows="12">{{$activity->pqr ? $activity->pqr->context : ''}}</textarea>
				</div>
			</div>
			-->
                <!-- END: CONTEXT -->

                <!-- SUMMARY -->
                <!--
			<div class="title">
				<i class="dropdown icon"></i> RESUMEN DE HISTORIA CLÍNICA
			</div>
			<div class="content">
				<div class="field">
					<label>Resumen de historia clinica</label>
					<textarea name="summary" rows="12">{{$activity->pqr ? $activity->pqr->summary : ''}}</textarea>
				</div>
			</div>
			-->
                <!-- END: SUMMARY -->

                <!-- RESPONSE -->
                <div id="100" class="title">
                    <i class="dropdown icon"></i> RESPUESTA <span style="color: red;" class="required">*</span>
                </div>
                <div class="content">
                    <div class="required field">
                        <label>Respuesta</label>
                        <textarea name="response"
                                  rows="12">{{$activity->pqr ? $activity->pqr->response : ''}}</textarea>
                    </div>
                    <div class="fields">
                        <div class="required four wide field">
                            <label>Procedente</label>
                            <div class="ui fluid selection dropdown">
                                <input name="is_coming" type="hidden"
                                       value="{{$activity->pqr ? $activity->pqr->is_coming : ''}}">
                                <i class="dropdown icon"></i>
                                <div class="default text">Procedente</div>
                                <div class="menu">
                                    <div class="item" data-value="Si">Si</div>
                                    <div class="item" data-value="No">No</div>
                                </div>
                            </div>
                        </div>
                        <div class="required four wide field">
                            <label>Requiere Seguimiento Periodico</label>
                            <div class="ui fluid selection dropdown">
                                <input name="requires_periodic_tracking" type="hidden"
                                       value="{{$activity->pqr ? $activity->pqr->requires_periodic_tracking : ''}}">
                                <i class="dropdown icon"></i>
                                <div class="default text">Requiere Seguimiento Periodico</div>
                                <div class="menu">
                                    <div class="item" data-value="Si">Si</div>
                                    <div class="item" data-value="No">No</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- END: RESPONSE -->

                <!-- HALLAZGOS -->
                <div id="101" class="title">
                    <i class="dropdown icon"></i> HALLAZGOS Y RECOMENDACIONES
                </div>
                <div class="content">
                    <div style="margin-bottom: 5px;" class="fields">
                        <div class="four wide field"><label>Area</label></div>
                        <div class="ten wide field"><label>Descripción</label></div>
                        <div class="one wide field">
                            <a style="margin-top: -15px;" onclick="return addRecommendation();"
                               class="ui basic small icon blue button"><i class="add icon"></i></a>
                        </div>
                    </div>
                    <div id="recommendations">
                        @if ($activity->pqr && count($activity->pqr->pqr_recommendations) > 0)
                            @foreach($activity->pqr->pqr_recommendations as $recommendation)
                                <div class="fields">
                                    <input type="hidden" name="recommendation[id][]" value="{{$recommendation->id}}">
                                    <div class="four wide field">
                                        <input name="recommendation[area][]" type="text"
                                               value="{{$recommendation->area}}">
                                    </div>
                                    <div class="ten wide field">
                                        <textarea name="recommendation[description][]"
                                                  rows="12">{{$recommendation->description}}</textarea>
                                    </div>
                                    <div class="one wide field">
                                        <a class="ui red small icon basic button"><i class="remove icon"></i></a>
                                    </div>
                                </div>
                            @endforeach
                        @else
                            <div class="fields">
                                <input type="hidden" name="recommendation[id][]">
                                <div class="four wide field">
                                    <input name="recommendation[area][]" type="text">
                                </div>
                                <div class="ten wide field">
                                    <textarea name="recommendation[description][]" rows="12"></textarea>
                                </div>
                                <div class="one wide field"></div>
                            </div>
                        @endif
                    </div>
                    <!--
				<div class="field">
					<label>Hallazgos</label>
					<textarea name="findings" rows="12">{{$activity->pqr ? $activity->pqr->findings : ''}}</textarea>
				</div>
				<div class="field">
					<label>Recomendaciones</label>
					<textarea name="recommendations" rows="12">{{$activity->pqr ? $activity->pqr->recommendations : ''}}</textarea>
				</div>
				-->
                </div>
                <!-- END: HALLAZGOS -->

                @if($activity->service_id == \App\Service::SERVICE_RESPONSE_PQR_GENERAL)
                    <!-- CATEGORICAL -->
                    <div id="102" class="title">
                        <i class="dropdown icon"></i> DATOS DE CATEGORIA <span style="color: red;"
                                                                               class="required">*</span>
                    </div>
                    <div class="content">
                        <div class="fields">
                            <div class="required five wide field">
                                <label>Categoria Real</label>
                                <div class="ui fluid selection dropdown">
                                    <input name="real_category" type="hidden"
                                           value="{{$activity->pqr ? $activity->pqr->real_category : ''}}">
                                    <i class="dropdown icon"></i>
                                    <div class="default text"></div>
                                    <div class="menu">
                                        @foreach($CATEGORICAL_INFO_PQR as $k => $v)
                                            <div class="item" data-value="{{$k}}">{{$v['NAME']}}</div>
                                        @endforeach
                                    </div>
                                </div>
                            </div>
                            <div class="required four wide field">
                                <label>Causas de Procedencia</label>
                                <div class="ui fluid selection dropdown">
                                    <input name="procedende_causes" type="hidden"
                                           value="{{$activity->pqr ? $activity->pqr->procedende_causes : ''}}">
                                    <i class="dropdown icon"></i>
                                    <div class="default text"></div>
                                    <div class="menu">
                                        @if ($activity->pqr && $activity->pqr->real_category)
                                            @foreach($CATEGORICAL_INFO_PQR[$activity->pqr->real_category]['OPTIONS'] as $k => $v)
                                                <div class="item" data-value="{{$v}}">{{$v}}</div>
                                            @endforeach
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- END: CATEGORICAL -->
                    <!--SCALES_METHOD -->
                    <div id="103" class="title"><i class="dropdown icon"></i>Escalamientos
                    </div>
                    <div class="content">
                        <div style="margin-bottom: 5px;" class="fields">
                            <div class="four wide field"><label>Gestionador</label></div>
                            <div class="four wide field"><label>Fecha asignación a gestionador</label></div>
                            <div class="four wide field"><label>Proveedor de insumo</label></div>
                            <div class="four wide field"><label>Fecha de escalamiento</label></div>
                            <div class="four wide field"><label>Estado</label></div>
                            <div class="one wide field">
                                <a style="margin-top: -15px;" onclick="return addScales();"
                                   class="ui basic small icon blue button"><i class="add icon"></i></a>
                            </div>
                        </div>
                        <div id="scales">
                            @if($activity->pqr && count($activity->pqr->scales) > 0)
                                @foreach($activity->pqr->scales as $scale)
                                    <div class="fields">
                                        <input type="hidden" name="scales[id][]" value="{{$scale->id}}">
                                        <div class="four wide field">
                                            <input name="scales[manager][]" type="text" value="{{$scale->manager}}">
                                        </div>
                                        <div class="four wide field">
                                            <input name="scales[manager_assign_date][]" type="text" class="datepicker"
                                                   data-value="{{$scale->manager_assign_date}}">
                                        </div>
                                        <div class="four wide field">
                                            <input name="scales[input_supplier][]" type="text"
                                                   value="{{$scale->input_supplier}}">
                                        </div>
                                        <div class="four wide field">
                                            <input name="scales[escalation_date][]" type="text" class="datepicker"
                                                   data-value="{{$scale->escalation_date}}">
                                        </div>
                                        <div class="four wide field">
                                            <div class="ui fluid selection dropdown">
                                                <input name="scales[state][]" type="hidden"
                                                       value="{{$scale->state}}">
                                                <i class="dropdown icon"></i>
                                                <div class="default text">Estado</div>
                                                <div class="menu">
                                                    <div class="item" data-value="Archivado">ARCHIVADO</div>
                                                    <div class="item" data-value="Asignados para gestion">ASIGNADOS PARA
                                                        GESTIÓN
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="one wide field">
                                            <a class="ui red small icon basic button"><i class="remove icon"></i></a>
                                        </div>
                                    </div>
                                @endforeach
                            @else
                                <div class="fields">
                                    <input type="hidden" name="scales[id][]">
                                    <div class="four wide field">
                                        <input name="scales[manager][]" type="text">
                                    </div>
                                    <div class="four wide field">
                                        <input name="scales[manager_assign_date][]" type="text" class="datepicker">
                                    </div>
                                    <div class="four wide field">
                                        <input name="scales[input_supplier][]" type="text">
                                    </div>
                                    <div class="four wide field">
                                        <input name="scales[escalation_date][]" type="text" class="datepicker">
                                    </div>
                                    <div class="four wide field">
                                        <div class="ui fluid selection dropdown">
                                            <input name="scales[state][]" type="hidden">
                                            <i class="dropdown icon"></i>
                                            <div class="default text">Estado</div>
                                            <div class="menu">
                                                <div class="item" data-value="Archivado">ARCHIVADO</div>
                                                <div class="item" data-value="Asignados para gestion">ASIGNADOS PARA
                                                    GESTIÓN
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="one wide field"></div>
                                </div>
                            @endif
                        </div>
                    </div>
                    <!--END: SCALES_METHOD -->
                @endif
            </div>

            <div class="ui basic segment">
                <div class="fields">
                    <div class="four wide field">
                        <button id="button_save" class="ui green fluid button"><i class="save icon"></i> Guardar</button>
                    </div>
                    <div class="six wide field">
                        <!--
                    <a href="{{secure_url('/servicio/' . $activity->id . '/pqr/pdf')}}" class="ui basic small button" target="_blank" onclick="event.preventDefault(); generatePDF();">
						<i class="file pdf outline red icon"></i> Vista previa
					</a>
                    -->
                        <a href="{{secure_url('/servicio/' . $activity->id . '/pqr/word')}}"
                           class="ui basic small button" target="_blank">
                            <i class="file word outline blue icon"></i> Vista previa
                        </a>
                        <a href="{{secure_url('/servicio/' . $activity->id)}}" class="ui basic small button"><i
                                    class="arrow left icon"></i> Volver a la actividad</a>
                    </div>
                </div>
            </div>
            {{csrf_field()}}
        </form>
    </div>


    <!-- MODELS -->
    <!-- DIAGNOSTIC MODEL -->
    <div id="diagnostic_model" style="display: none;">
        <div class="group-two-fields">
            <div class="fields">
                <input type="hidden" name="diagnostics[id][]">
                <div class="two wide field">
                    <label>Cód.</label>
                    <div class="ui search">
                        <div class="ui icon input">
                            <input class="prompt" name="diagnostics[cod][]" type="text">
                            <i class="search icon"></i>
                        </div>
                        <div class="results"></div>
                    </div>
                </div>
                <div class="eight wide field">
                    <label>Nombre</label>
                    <input class="description" name="diagnostics[description][]" type="text" maxlength="500" readonly>
                </div>
                <div class="three wide field">
                    <label>Fecha del diagnostico</label>
                    <input name="diagnostics[d_date][]" type="text" class="datepicke">
                </div>
                <div class="two wide field">
                    <label>Origen</label>
                    <div class="ui fluid selection dropdown">
                        <input name="diagnostics[origin][]" type="hidden">
                        <i class="dropdown icon"></i>
                        <div class="default text">Origen</div>
                        <div class="menu">
                            @foreach($ORIGINS as $k => $v)
                                <div class="item" data-value="{{$k}}">{{$v}}</div>
                            @endforeach
                        </div>
                    </div>
                </div>
                <div class="two wide field">
                    <label>Primer calificador</label>
                    <div class="ui fluid selection dropdown">
                        <input name="diagnostics[qualifier][]" type="hidden">
                        <i class="dropdown icon"></i>
                        <div class="default text">Calificador</div>
                        <div class="menu">
                            @foreach($QUALIFIERS as $k => $v)
                                <div class="item" data-value="{{$k}}">{{$v}}</div>
                            @endforeach
                        </div>
                    </div>
                </div>
                <div class="jr two wide field">
                    <label>Origen según JR</label>
                    <div class="ui fluid selection dropdown">
                        <input name="diagnostics[origin_jr][]" type="hidden">
                        <i class="dropdown icon"></i>
                        <div class="default text">Origen</div>
                        <div class="menu">
                            <div class="item" data-value="">&nbsp;</div>
                            @foreach($ORIGINS as $k => $v)
                                <div class="item" data-value="{{$k}}">{{$v}}</div>
                            @endforeach
                        </div>
                    </div>
                </div>
                <div class="jn two wide field">
                    <label>Origen según JN</label>
                    <div class="ui fluid selection dropdown">
                        <input name="diagnostics[origin_jn][]" type="hidden">
                        <i class="dropdown icon"></i>
                        <div class="default text">Origen</div>
                        <div class="menu">
                            <div class="item" data-value="">&nbsp;</div>
                            @foreach($ORIGINS as $k => $v)
                                <div class="item" data-value="{{$k}}">{{$v}}</div>
                            @endforeach
                        </div>
                    </div>
                </div>
                <div class="two wide field">
                    <label>En Firme</label>
                </div>
                <div class="one wide field">
                    <a class="ui red small icon basic button"><i class="remove icon"></i></a>
                </div>
            </div>
            <div class="fields">
                <div class="eight wide field">
                    <label>Descripción</label>
                    <input class="description prompt" name="diagnostics[description_editable][]" type="text">
                </div>
                <div class="two wide field">
                    <label>Lateralidad</label>
                    <div class="ui fluid selection dropdown">
                        <input name="diagnostics[laterality][]" type="hidden">
                        <i class="dropdown icon"></i>
                        <div class="default text">Lateralidad</div>
                        <div class="menu">
                            @foreach($LATERALITY as $k => $v)
                                <div class="item" data-value="{{$k}}">{{$v}}</div>
                            @endforeach
                        </div>
                    </div>
                </div>
                <div class="three wide field">
                    <label>Tipo de Evento</label>
                    <div class="ui fluid selection dropdown">
                        <input name="diagnostics[type_event][]" type="hidden">
                        <i class="dropdown icon"></i>
                        <div class="default text">Tipo de Evento</div>
                        <div class="menu">
                            <div class="item" data-value="AT">AT</div>
                            <div class="item" data-value="EL">EL</div>
                        </div>
                    </div>
                </div>
                <div class="three wide field required">
                    <label>Nro Siniestro</label>
                    <input name="diagnostics[sinister_num][]" type="text">
                </div>
            </div>
        </div>
    </div>

    <!-- WORK HISTORY MODEL -->
    <div id="request_model" style="display: none;" class="fields">
        <input type="hidden" name="request[id][]">
        <div class="four wide field">
            <div class="ui fluid selection dropdown">
                <input name="request[type][]" type="hidden">
                <i class="dropdown icon"></i>
                <div class="default text">Tipo</div>
                <div class="menu">
                    <div class="item" data-value="Autorizaciones">Autorizaciones</div>
                    <div class="item" data-value="Entrega de medicamentos">Entrega de medicamentos</div>
                    <div class="item" data-value="Calificación de enfermedad">Calificación de enfermedad</div>
                    <div class="item" data-value="Calificación de accidente">Calificación de accidente</div>
                    <div class="item" data-value="Calificación de consecuencias patologicas">Calificación de
                        consecuencias patologicas
                    </div>
                    <div class="item" data-value="Calificación de PCL">Calificación de PCL</div>
                    <div class="item" data-value="Revisión PCL">Revisión PCL</div>
                    <div class="item" data-value="Controversias">Controversias</div>
                    <div class="item" data-value="Otros">Otros</div>
                </div>
            </div>
        </div>
        <div class="six wide field">
            <textarea name="request[description][]" rows="12"></textarea>
        </div>
        <div class="three wide field">
            <div class="ui fluid selection dropdown">
                <input name="request[require_provider][]" type="hidden">
                <i class="dropdown icon"></i>
                <div class="default text">Lateralidad</div>
                <div class="menu">
                    <div class="item" data-value="Si">Si</div>
                    <div class="item" data-value="No">No</div>
                </div>
            </div>
        </div>
        <div class="six wide field">
            <textarea name="request[scaled][]" rows="12"></textarea>
        </div>
        <div class="one wide field">
            <a class="ui red small icon basic button"><i class="remove icon"></i></a>
        </div>
    </div>
    <!-- SCALE  MODEL -->
    <div id="scales_model" style="display: none;" class="fields">
        <input type="hidden" name="scales[id][]">
        <div class="four wide field">
            <input name="scales[manager][]" type="text">
        </div>
        <div class="four wide field">
            <input name="scales[manager_assign_date][]" type="text" class="datepicke">
        </div>
        <div class="four wide field">
            <input name="scales[input_supplier][]" type="text">
        </div>
        <div class="four wide field">
            <input name="scales[escalation_date][]" type="text" class="datepicke">
        </div>
        <div class="four wide field">
            <div class="ui fluid selection dropdown">
                <input name="scales[state][]" type="hidden">
                <i class="dropdown icon"></i>
                <div class="default text">Estado</div>
                <div class="menu">
                    <div class="item" data-value="Archivado">ARCHIVADO</div>
                    <div class="item" data-value="Asignados para gestion">ASIGNADOS PARA GESTIÓN</div>
                </div>
            </div>
        </div>
        <div class="one wide field">
            <a class="ui red small icon basic button"><i class="remove icon"></i></a>
        </div>
    </div>

    <!-- RECOMMENDATION MODEL -->
    <div id="recommendation_model" style="display: none;" class="fields">
        <input type="hidden" name="recommendation[id][]">
        <div class="four wide field">
            <input name="recommendation[area][]" type="text">
        </div>
        <div class="ten wide field">
            <textarea name="recommendation[description][]" rows="12"></textarea>
        </div>
        <div class="one wide field">
            <a class="ui red small icon basic button"><i class="remove icon"></i></a>
        </div>
    </div>

    <style type="text/css">
        .ui.grid .column {
            padding: 0.5rem 1rem !important;
        }

        .jr.field {
            display: none;
        }

        .jn.field {
            display: none;
        }

        .ui.accordion .title {
            text-transform: uppercase;
        }

        .field > h3 {
            text-align: center;
            margin-top: 1.25rem;
        }

        .ui.search > .results {
            width: 30rem;
        }

        .ui.search > .results .result .title {
            padding: 0 !important;
            border: none !important;
            text-transform: none;
        }

        .ui.search > .results .result .content {
            padding: 0 !important;
        }

        .plaintiff_applicant {
            display: none;
        }
    </style>
    <script type="text/javascript">
        var loadTerms = function () {
            var type = $('[name=format]').val();

            $('#terms .item.first').hide();
            $('#terms .item.second').hide();

            if (type == 6) {
                $('#terms .item.first').show();
            } else if (type == 7) {
                $('#terms .item.second').show();
            }
        };
        var generatePDF = function () {
            $('form#dictamen').attr('target', '_blank');
            $('form#dictamen').attr('action', '{{secure_url('/servicio/' . $activity->id . '/pqr/pdf')}}');
            $('form#dictamen').submit();
            $('form#dictamen').removeAttr('target');
            $('form#dictamen').attr('action', '{{secure_url('/servicio/' . $activity->id . '/pqr/save')}}');
            return false;
        };
        var addDiagnostic = function (e) {
            var $fields = $('#diagnostic_model').clone(true);
            $fields.removeAttr('id');
            $fields.find('a').click(function () {
                $(this).parent().parent().parent().remove();
            });
            $fields.find('.ui.dropdown').dropdown({
                forceSelection: false
            });
            $fields.find('.datepicke').pickadate({
                selectYears: 100,
                selectMonths: true,
                max: new Date(),
                formatSubmit: 'yyyy-mm-dd',
                format: 'mmm dd, yyyy',
            });
            $fields.find('.ui.search input').change(function () {
                var valid = false;

                $(this).parents('.fields').find('input.description').val('');

                for (var i = 0; i < cie10.length; i++) {
                    if (cie10[i].COD == $(this).val().toUpperCase()) {
                        $(this).parents('.fields').find('input.description').val(cie10[i].DESCRIPTION);
                        checkDiagnosticCode($(this), cie10[i].COD);
                        valid = true;
                    }
                }

                if (!valid && $(this).val() != '') {
                    $(this).val('');
                }
            });
            $fields.find('.ui.search').search({
                source: cie10,
                fields: {
                    title: 'COD',
                    description: 'DESCRIPTION'
                },
                searchFields: ['COD', 'DESCRIPTION'],
                regExp: {
                    escape: /[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,
                    beginsWith: ''
                },
                showNoResults: true,
                maxResults: 250,
                searchFullText: false,
                error: {
                    noResults: 'No se encontraron resultados para tu búsqueda.'
                },
                onSelect: function (result, response) {
                    $(this).parents('.group-two-fields').find('input.description').val(result.DESCRIPTION);
                }
            });
            $('#diagnostics').append($fields);
            $fields.show();
            return false;
        };
        var addScales = function () {
            var $fields = $('#scales_model').clone(true);
            $fields.removeAttr('id');
            $fields.find('a').click(function () {
                $(this).parent().parent().remove();
            });
            $fields.find('.ui.dropdown').dropdown({
                forceSelection: false
            });
            $fields.find('.datepicke').pickadate({
                selectYears: 100,
                selectMonths: true,
                max: new Date(),
                formatSubmit: 'yyyy-mm-dd',
                format: 'yyyy-mm-dd'
            });

            $('#scales').append($fields);
            $fields.show();
            return false;
        };
        var addRequest = function () {
            var $fields = $('#request_model').clone(true);
            $fields.find('.ui.dropdown').dropdown({
                forceSelection: false
            });
            $fields.removeAttr('id');
            $fields.find('a').click(function () {
                $(this).parent().parent().remove();
            });
            $('#requests').append($fields);
            $fields.show();
            return false;
        };
        var addRecommendation = function () {
            var $fields = $('#recommendation_model').clone(true);
            $fields.find('.ui.dropdown').dropdown({
                forceSelection: false
            });
            $fields.removeAttr('id');
            $fields.find('a').click(function () {
                $(this).parent().parent().remove();
            });
            $('#recommendations').append($fields);
            $fields.show();
            return false;
        };
        var cie10 = [];
        var CATEGORICAL_INFO_PQR = {!! json_encode($CATEGORICAL_INFO_PQR) !!};
        $(document).ready(function () {
            $('.ui.accordion .ui.grid .row').css('padding-top', 0);
            $('.ui.accordion .ui.grid .column').css('padding-top', 0);
            $('.ui.accordion').accordion({
                exclusive: false
            });
            $('.datepicker').pickadate({
                selectYears: 100,
                selectMonths: true,
                formatSubmit: 'yyyy-mm-dd',
                format: 'mmmm dd, yyyy',
            });
            $('form#dictamen').form();
            $('form .ui.dropdown').dropdown({
                forceSelection: false
            });

            $('#diagnostics a.red').click(function () {
                $(this).parent().parent().parent().remove();
            });
            $('#requests a.red').click(function () {
                $(this).parent().parent().remove();
            });
            $('#recommendations a.red').click(function () {
                $(this).parent().parent().remove();
            });
            $.getJSON('/js/colombia.json', function (json) {
                json.sort(function (a, b) {
                    if (a['name'] === b['name']) {
                        return 0;
                    }
                    if (a['name'] > b['name']) {
                        return 1;
                    } else {
                        return -1;
                    }
                });
                colombia = json;
                for (var i = 0; i < colombia.length; i++) {
                    $("#departments .menu").append('<div class="item" data-value="' + colombia[i].code + '">' + colombia[i].name + '</div>');
                    $("#departments2 .menu").append('<div class="item" data-value="' + colombia[i].code + '">' + colombia[i].name + '</div>');
                }

                var department = $("#departments").dropdown('get value');

                for (var i = 0; i < colombia.length; i++) {
                    if (department == colombia[i].code) {
                        for (var j = 0; j < colombia[i].municipalities.length; j++) {
                            var mun = colombia[i].municipalities[j];
                            $("#municipalities .menu").append('<div class="item" data-value="' + mun.code + '">' + mun.name + '</div>');
                        }
                    }
                }

                var department2 = $("#departments2").dropdown('get value');

                for (var i = 0; i < colombia.length; i++) {
                    if (department2 == colombia[i].code) {
                        for (var j = 0; j < colombia[i].municipalities.length; j++) {
                            var mun = colombia[i].municipalities[j];
                            $("#municipalities2 .menu").append('<div class="item" data-value="' + mun.code + '">' + mun.name + '</div>');
                        }
                    }
                }

                $("#municipalities, #departments, #municipalities2, #departments2").dropdown({
                    sortSelect: false,
                    forceSelection: false
                });

                $("#departments").change(function () {
                    var department = $(this).dropdown('get value');

                    $("#municipalities").dropdown('clear');
                    $("#municipalities .menu").empty();

                    for (var i = 0; i < colombia.length; i++) {
                        if (department == colombia[i].code) {
                            for (var j = 0; j < colombia[i].municipalities.length; j++) {
                                var mun = colombia[i].municipalities[j];
                                $("#municipalities .menu").append('<div class="item" data-value="' + mun.code + '">' + mun.name + '</div>');
                            }

                            $("#municipalities input").val(colombia[i].municipalities[0].code);
                        }
                    }

                    $("#municipalities").dropdown({
                        sortSelect: false,
                        forceSelection: false
                    });
                });
                $("#departments2").change(function () {
                    var department = $(this).dropdown('get value');

                    $("#municipalities2").dropdown('clear');
                    $("#municipalities2 .menu").empty();

                    for (var i = 0; i < colombia.length; i++) {
                        if (department == colombia[i].code) {
                            for (var j = 0; j < colombia[i].municipalities.length; j++) {
                                var mun = colombia[i].municipalities[j];
                                $("#municipalities2 .menu").append('<div class="item" data-value="' + mun.code + '">' + mun.name + '</div>');
                            }

                            $("#municipalities2 input").val(colombia[i].municipalities[0].code);
                        }
                    }

                    $("#municipalities2").dropdown({
                        sortSelect: false,
                        forceSelection: false
                    });
                });
            });
            $('form .ui.search input').change(function () {
                var valid = false;

                $(this).parents('.fields').find('input.description').val('');

                for (var i = 0; i < cie10.length; i++) {
                    if (cie10[i].COD == $(this).val().toUpperCase()) {
                        $(this).parents('.fields').find('input.description').val(cie10[i].DESCRIPTION);
                        checkDiagnosticCode($(this), cie10[i].COD);
                        valid = true;
                    }
                }

                if (!valid && $(this).val() != '') {
                    $(this).val('');
                }
            });

            $('[name="real_category"]').change(function () {
                var dtype = $(this).val();
                var div_procedence_causes_options = $(this).parent().parent().parent().find("[name='procedende_causes']");

                div_procedence_causes_options.parent().dropdown('clear');
                div_procedence_causes_options.parent().find('.menu').empty();
                div_procedence_causes_options.parent().find('select').empty();

                for (var i in CATEGORICAL_INFO_PQR[dtype]['OPTIONS']) {
                    var option = CATEGORICAL_INFO_PQR[dtype]['OPTIONS'][i];
                    div_procedence_causes_options.parent().find('.menu').append('<div class="item" data-value="' + option + '">' + option + '</div>');
                    div_procedence_causes_options.parent().find('select').append('<option value="' + option + '">' + option + '</option>');
                }
                div_procedence_causes_options.delegate().trigger("chosen:updated");
            });

            $.getJSON('/js/cie10.json?v=1.0', function (json) {
                cie10 = json;
                $('form .ui.search.code').search({
                    source: cie10,
                    fields: {
                        title: 'COD',
                        description: 'DESCRIPTION'
                    },
                    searchFields: ['COD', 'DESCRIPTION'],
                    regExp: {
                        escape: /[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,
                        beginsWith: ''
                    },
                    showNoResults: true,
                    maxResults: 250,
                    searchFullText: false,
                    error: {
                        noResults: 'No se encontraron resultados para tu búsqueda.'
                    },
                    onSelect: function (result, response) {
                        $(this).parents('.group-two-fields').find('input.description').val(result.DESCRIPTION);
                    }
                });
            });

            $('form input').keydown(function (event) {
                if (event.keyCode == 13) {
                    event.preventDefault();
                    return false;
                }
            });

            loadTerms();

            @if($activity->pqr && $activity->pqr->plaintiff_applicant == 'Otro')
            $('.plaintiff_applicant').show();
            @else
            $('.plaintiff_applicant').hide();
            @endif

            $('input[name="plaintiff_applicant"]').change(function () {
                var type = $(this).val();
                if (type == 'Otro') {
                    $('.plaintiff_applicant').show();
                } else {
                    $('.plaintiff_applicant').hide();
                }

                if (type == 'Afiliado') {
                    if ('{{$activity->affiliate->doc_type}}' != 'NIT') {
                        $('#dropdown_plaintiff_type_doc').dropdown('set selected', '{{$activity->affiliate->doc_type}}');
                        //////$('input[name="plaintiff_type_doc"]').val('{{$activity->affiliate->doc_type}}');
                        $('input[name="plaintiff_num_doc"]').val('{{$activity->affiliate->doc_number}}');
                        $('input[name="plaintiff_name"]').val('{{$activity->affiliate->full_name}}');
                    }
                }
            });

            @if (Auth::user()->isViewer())
            $('#dictamen .ui.search input').attr('disabled', 'disabled');
            $('#dictamen input, #dictamen textarea').attr('readonly', 'readonly');
            $('#dictamen .ui.dropdown').addClass('disabled');
            $('#dictamen .button').remove();
            @endif
        });
    </script>
    {{--  VALIDACIÓN DE PERMISOS (Siempre al final de todo)  --}}
    <script>
        $(document).ready(function () {
            const isAssignedUser = {!! json_encode($activity->user->id == Auth::user()->id  || Auth::user()->isAdmin() || Auth::user()->isAdmin2() ) !!};
            const permissions = {!! json_encode(Auth::user()->service_permissions_list($activity->service_id)) !!};
            const activity = {!! json_encode($activity) !!};
            if (permissions.every(value => value.area_permissions[0]?.edit !== 1 || !isAssignedUser)) {
                $("#button_save").addClass("disabled");
            }
            for (const permission of permissions) {
                if (permission.area_permissions[0]?.view !== 1) {
                    $(`#${permission.id}`).css("pointer-events", "none");
                }
                if (permission.area_permissions[0]?.edit !== 1 || !isAssignedUser) {
                    const content = $(`#${permission.id}`).next(".content");
                    content.find('input, .ui.dropdown, .datepicker').css('pointer-events', 'none');
                    content.find('textarea').attr('readonly', 'readonly');
                    content.find('.ui.button').addClass('disabled');
                }
            }
        });
    </script>
@endsection
