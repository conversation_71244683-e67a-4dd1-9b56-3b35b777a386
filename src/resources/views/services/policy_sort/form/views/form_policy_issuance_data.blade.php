<div class="title"><i class="dropdown icon"></i>Datos emisión póliza<span style="color: red;" class="required">*</span>
</div>
<div class="content">
    <div class="accordion transition">
        <div class="title centered">
            <i class="dropdown icon"></i>
            Identificación del tomador<span style="color: red;" class="required">*</span>
        </div>

        <div class="content">
            <div class="two fields">
                <div class="required field">
                    <label for="typeIdentify">Tipo de identificación</label>
                    <div class="ui selection dropdown grayed-input" id="heirDocTypeDropdown">
                        <input readonly type="hidden" name="doc_type" id="docType"
                            class="minus capitalizes grayed-input" value="{{ $affiliate->doc_type ?? '' }}">
                        <i class="dropdown icon"></i>
                        <div class="default text minus heirDocTypeDropdown">Seleccionar tipo documento</div>
                        <div class="menu">
                            @foreach ($DOC_TYPES as $k => $v)
                                <div class="item minus capitalizes heirDocTypeDropdown" data-value="{{ $k }}" @if ($k == $affiliate->doc_type) class="active selected" @endif>
                                    {{ $v }}
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>

                <div class="required field">
                    <label for="numberIdentify">Número de Identificación</label>
                    <div class="ui input">
                        <input readonly name="numberIdentify" id="numberIdentify" autocomplete="off" type="text"
                            value="{{ $affiliate->doc_number ?? '' }}">
                    </div>
                </div>
            </div>
        </div>

        <div class="title centered">
            <i class="dropdown icon"></i>
            Datos del tomador<span style="color: red;" class="required">*</span>
        </div>

        <div class="content">
            <div class="three fields">
                <div class="required field">
                    <label for="policyHolderName">Nombre</label>
                    <div class="ui input">

                        <input name="policyHolderName" id="policyHolderName" autocomplete="off" type="text"
                            value="{{ mb_convert_case(mb_strtolower($affiliate->first_name ?? ''), MB_CASE_TITLE, 'UTF-8') }}"
                            readonly>
                    </div>
                </div>

                <div class="required field">
                    <label for="policyHolderPhone">Teléfonos</label>
                    <div class="ui input">

                        <input readonly name="policyHolderPhone" id="policyHolderPhone" autocomplete="off" type="text"
                            value="{{ $affiliate->phone ?? '' }}">
                    </div>
                </div>

                <div class="required field">
                    <label for="policyHolderEmail">Correo electrónico de notificaciones</label>
                    <div class="ui input">

                        <input readonly name="policyHolderEmail" id="policyHolderEmail" autocomplete="off" type="text"
                            value="{{ strtolower($policy_sort->notification_email) ?? '' }}">
                    </div>
                </div>
            </div>
            <div class="three fields">
                <div class="field">
                    <label for="policyHolderEmailAdditional">Correo electrónico de notificaciones adicional</label>
                    <div class="ui input">

                        <input readonly name="policyHolderEmailAdditional" id="policyHolderEmailAdditional" autocomplete="off" type="text"
                            value="{{ strtolower($policy_sort->notification_email_additional) ?? '' }}">
                    </div>
                </div>

                <div class="required field">
                    <label for="emailElectronicBilling">Correo electrónico para facturación electrónica</label>
                    <div class="ui input">

                        <input readonly name="emailElectronicBilling" id="emailElectronicBilling" autocomplete="off"
                            type="text" value="{{ strtolower($affiliate->electronic_billing_email) ?? '' }}">
                    </div>
                </div>

                <div class="required field">
                    <label for="ibanAcccount">Cuenta IBAN</label>
                    <div class="ui input">

                        <input readonly name="ibanAcccount" id="ibanAcccount" autocomplete="off" type="text"
                            value="{{ $affiliate->iban_account ?? '' }}">
                    </div>
                </div>
            </div>

            <div class="three fields">
                <div class="required field">
                    <label for="employerAddress">Dirección del trabajador (otras señas)</label>
                    <div class="ui input">

                        <input readonly name="employerAddress" id="employerAddress" autocomplete="off" type="text"
                            value="{{ ucfirst(strtolower($affiliate->employer_address)) ?? '' }}">
                    </div>
                </div>
            </div>

            <div class="three fields">
                {{-- Ocupación por grupo es obligatorio --}}
                <div class="required field">
                    <label>Ocupación por grupo</label>
                    <div class="ui dropdown button grayed-input"
                        style="display: flex !important; flex-direction: row; justify-content: space-between;">
                        <span class="text" style="font-weight: normal;">Selecciona una opción</span>
                        <i class="dropdown icon"></i>
                        <input type="hidden" name="occupancy_group" id="occupancy_group"
                            value="{{ $affiliate->occupancy_group ?? '' }}">
                        <input type="hidden" name="ooccupancy_group_texto" id="occupancy_group_texto">
                        <div class="menu">
                            @foreach ($OCCUPATION_GROUPS as $key => $value)
                                <div class="item">
                                    <i class="dropdown icon"></i>
                                    <span class="text">{{ $key }}</span>
                                    <div class="menu custom-submenu">
                                        @foreach ($value as $key_sub => $value_sub)
                                            <div class="item" data-value="{{ $key_sub }}">
                                                {{ $value_sub }}
                                            </div>
                                        @endforeach

                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
                <div class="required field">
                    <label for="ocupacion_tomador">Ocupación</label>
                    <div class="ui input ">
                        <input name="ocupacion_tomador" id="ocupacion_tomador" type="text" readonly class="grayed-input"
                            value="{{ $affiliate->occupation ?? '' }}">
                    </div>
                </div>

                <div></div>
            </div>
            <div class="three fields">
                <div class="required field">
                    <label for="province">Provincia</label>
                    <div id="province" class="ui search selection dropdown grayed-input">
                        <input readonly type="hidden" class="minus grayed-input" name="province"
                            value="{{ $affiliate ? $affiliate->province : '' }}">
                        <i class="dropdown icon"></i>
                        <div class="default text">Selecciona uno</div>
                        <div class="menu"></div>
                    </div>
                </div>

                <div class="required field">
                    <label for="canton">Cantón</label>
                    <div id="canton" class="ui search selection dropdown grayed-input">
                        <input readonly type="hidden" name="canton" class="minus grayed-input"
                            value="{{ $affiliate->canton ?? '' }}">
                        <i class="dropdown icon"></i>
                        <div class="default text">Selecciona uno</div>
                        <div class="menu"></div>
                    </div>
                </div>

                <div class="required field">
                    <label for="district">Distrito</label>
                    <div id="district" class="ui search selection dropdown grayed-input">
                        <input readonly type="hidden" name="district" class="minus grayed-input"
                            value="{{ $affiliate->district ?? '' }}">
                        <i class="dropdown icon"></i>
                        <div class="default text minus"></div>
                        <div class="menu"></div>
                    </div>
                </div>
            </div>
            <div class="three fields">
                <div class="field">
                    <div class="grouped fields">
                        <div class="required field">
                            <label>Sector</label>
                        </div>
                        <div class="field">
                            <div class="ui radio checkbox checkbox-spacing">
                                <input readonly type="radio" name="sector" id="public" class="minus" value="public"
                                    readonly {{ $policy_sort->economic_activity === 'public' ? 'checked' : '' }}>
                                <label for="public">Público</label>
                            </div>
                            <div class="ui radio checkbox checkbox-spacing" style="margin-left: 20px">
                                <input readonly type="radio" name="sector" id="private" class="minus" value="private"
                                    readonly {{ $policy_sort->economic_activity === 'private' ? 'checked' : '' }}>
                                <label for="private">Privado</label>
                            </div>
                        </div>
                    </div>
                </div>

{{--                <div class="required field">--}}
{{--                    <label for="economicActivityCode">Código de la actividad económica</label>--}}
{{--                    <div class="ui search code">--}}
{{--                        <div class="ui icon input">--}}
{{--                            <input readonly id="economic_activity_code" class="prompt" name="diagnostics[cod][]"--}}
{{--                                type="text" value="{{ $policy_sort->activity_economic_id ?? '' }}" autocomplete="off">--}}
{{--                            <i class="search icon"></i>--}}
{{--                        </div>--}}
{{--                        <div class="results"></div>--}}
{{--                    </div>--}}
{{--                </div>--}}

{{--                <div class="required field">--}}
{{--                    <label for="economicActivityName">Nombre Actividad Económica</label>--}}
{{--                    <input id="economic_activity_name" class="prompt" name="diagnostics[description][]" type="text"--}}
{{--                        readonly maxlength="500" value="{{ $policy_sort->economic_activity_name ?? '' }}">--}}
{{--                </div>--}}

                @if($economicActivity->economic_branch && $economicActivity->economic_branch->branch_name)  {{--  cuando es publica no tiene rama--}}
                <div class="required field">
                    <label>Rama general de actividad económica</label>
                    <input id="branch_activity" class="prompt" name="branch_activity" type="text" readonly value="{{ $economicActivity->economic_branch->branch_name ?? '' }}">
                </div>
                @endif

                <div class="required field">
                    <label>Actividad económica especifica</label>
                    <textarea readonly rows="2">{{ $economicActivity->code . ' - ' . $economicActivity->activity_name }}</textarea>
                    <input id="economic_activity_code" name="economic_activity_code" type="hidden" value="{{$economicActivity->code ?? ''}}">
                    <input id="economic_activity_name" name="economic_activity_name" type="hidden" value="{{$economicActivity->activity_name ?? ''}}">
                </div>
            </div>

            <div class="ui divider"></div>
            
            <div class=" accordion transition">
                <div class="title">
                    <i class="dropdown icon"></i>
                    Correo electrónico de notificaciones adicionales
                </div>
                <div class="content">
                    <div id="additional-emails-container"></div>
                </div>

                <div class="title">
                    <i class="dropdown icon"></i>
                    Direcciones adicionales
                </div>
                <div class="content">
                    <div id="locations-container"></div>
                </div>
            
                <div class="title">
                    <i class="dropdown icon"></i>
                    Teléfonos adicionales
                </div>
                <div class="content">
                    <div id="phones-container"></div>
                </div>
            </div>
        </div>

        <div class="title centered">
            <i class="dropdown icon"></i>
            Datos de personas autorizadas para reportes de accidentes y enfermedades<span style="color: red;"
                class="required">*</span>
        </div>

        <div class="content">
            <div id="fieldContainer" class="dynamic-field">
                <!-- Para nuevos registros, el id será vacío -->
                <input type="hidden" name="id_responsible[]" value="">

                <div class="four fields">
                    <div class="required field">
                        <label for="nameResponsible">Nombre</label>
                        <div class="ui input">
                            <input name="name_responsible[]" id="nameResponsible" autocomplete="off" type="text"
                                class="minus"
                                value="{{ mb_convert_case(mb_strtolower($affiliate->name_responsible ?? ''), MB_CASE_TITLE, 'UTF-8') }}"
                                readonly>
                        </div>
                    </div>

                    <div class="required field">
                        <label>Tipo de identificación</label>
                        <div class="ui selection dropdown ContactDocTypeResponsibleDropdown grayed-input"
                            style="pointer-events: none;" id="heirDocTypeResponsibleDropdown">
                            <input type="hidden" class="minus capitalizes grayed-input" name="doc_type_responsible[]"
                                readonly id="docTypeResponsible" value="{{ $affiliate->doc_type_responsible }}">
                            <i class="dropdown icon"></i>
                            <div class="default text minus capitalizes">Seleccionar tipo documento</div>
                            <div class="menu">
                                @foreach ($DOC_TYPES as $k => $v)
                                    <div class="item minus capitalizes heirDocTypeResponsibleDropdown" data-value="{{ $k }}"
                                        @if ($k == $affiliate->doc_type_responsible) class="active selected" @endif>
                                        {{ $v }}
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>

                    <div class="required field">
                        <label for="numberIdentifyResponsible">Número de identificación</label>
                        <div class="ui input">
                            <input name="number_identify_responsible[]" class="minus" id="numberIdentifyResponsible"
                                autocomplete="off" type="text" value="{{ $affiliate->doc_number_responsible ?: '' }}"
                                readonly>
                        </div>
                    </div>

                    <div class="required field">
                        <label for="occupationResponsible">Ocupación</label>
                        <div class="ui input">
                            <input name="ocupation_responsible[]" class="minus" id="occupationResponsible"
                                autocomplete="off" type="text"
                                value="{{ ucfirst(strtolower($affiliate->occupation_responsible)) ?? '' }}" readonly>
                        </div>
                    </div>
                </div>

                <div class="four fields">
                    <div class="required field">
                        <label for="phoneResponsible">Teléfono</label>
                        <div class="ui input">
                            <input name="phone_responsible[]" id="phoneResponsible" autocomplete="off" type="text"
                                class="minus" value="{{ $affiliate->phone_responsible ?: '' }}" readonly>
                        </div>
                    </div>

                    <div class="required field">
                        <label for="cellphoneResponsible">Celular</label>
                        <div class="ui input">
                            <input name="cellphone_responsible[]" id="cellphoneResponsible" autocomplete="off"
                                type="text" class="minus" value="{{ $affiliate->cellphone_responsible ?: '' }}"
                                readonly>
                        </div>
                    </div>

                    <div class="required field">
                        <label for="emailResponsible">Correo electrónico</label>
                        <div class="ui input">
                            <input name="email_responsible[]" onchange="validateDataEmail(event, 'emailResponsible')"
                                id="emailResponsible" autocomplete="off" type="text" class="minus" readonly
                                value="{{ ucfirst(strtolower($affiliate->email_responsible)) }}">
                        </div>
                    </div>

                    @if (!$view_emision)
                        @if(!in_array(Auth::user()->area_id, [\App\Area::INTERMEDIARY, \App\Area::INTERMEDIARY_EXECUTIVE]))
                        <div class="required field">
                            <label for="unicodeResponsible">Código único</label>
                            <div class="ui input">
                                <input name="unique_code[]" id="unicodeResponsible" autocomplete="off" type="text" class="minus" readonly>
                            </div>
                        </div>
                        @endif
                    @endif

                </div>
                <div class="four fields">
                    <div class="field" style="margin-top: 25px;">
                        <button type="button" class="ui icon button add-field-btn blue" id="addFieldButton"
                            style="display: none;">
                            <i class="plus circle icon"></i>
                        </button>
                    </div>
                </div>

            </div>
        </div>
    </div>

    <div class="ui grey message" id="info-message">
        <div class="header">
            Por favor, valide los datos ingresados para continuar con la emisión de la póliza.
        </div>
    </div>

</div>

<style>
    .input_button {
        width: 100%;
        background: white !important;
        border: 1px solid rgba(34, 36, 38, .15) !important;
    }

    .custom-submenu {
        max-width: 300px;
        /* Cambia el valor según tus necesidades */
        overflow: hidden;
        /* Oculta contenido que desborda */
        white-space: nowrap;
        /* Evita que el texto haga saltos de línea */
        text-overflow: ellipsis;
        /* Muestra puntos suspensivos (...) para textos largos */
    }

    /* Estilo para los elementos de texto dentro del submenú */
    .custom-submenu .item {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        padding: 8px;
        /* Ajusta el espaciado interno según tus necesidades */
    }
</style>

<script>
    $('#addFieldButton').on('click', function () {
        // Clonar los campos dinámicos
        var newFields = $('.dynamic-field').last()
            .clone(); // Clonar solo la última sección agregada

        // Limpiar los valores de los inputs clonados
        newFields.find('input').val('');

        // Limpiar el dropdown de tipo de identificación
        newFields.find('.ContactDocTypeResponsibleDropdown').dropdown('clear');

        // Reemplazar el botón de agregar por el de eliminar en los nuevos campos
        newFields.find('#addFieldButton').hide(); // Ocultar el botón de agregar
        // newFields.find('.remove-field-btn').show(); // Mostrar el botón de eliminar

        // Asegurarse de que el nuevo conjunto de campos no tenga ID duplicados
        newFields.find('input, select').each(function () {
            var newId = $(this).attr('id') + '_' + Math.random().toString(36).substring(
                7); // Generar un nuevo ID único
            $(this).attr('id', newId); // Asignar el nuevo ID
        });

        $('#fieldContainer').append(newFields); // Agregar los nuevos campos al contenedor

        // Inicializar el nuevo dropdown
        newFields.find('.ContactDocTypeResponsibleDropdown').dropdown({
            onChange: function (value, text, $choice) {
                updateDropdownOptions(); // Actualizar las opciones al cambiar un valor

                // Actualizar el campo "Valor actual" con el valor correspondiente
                var $currentValueInput = $(this).closest('.dynamic-field').find(
                    'input[name="current-value[]"]');
                if (currentValues[value]) {
                    $currentValueInput.val(currentValues[value]);
                } else {
                    $currentValueInput.val('');
                }
            }
        });

        updateDropdownOptions();
    });


    function updateDropdownOptions() {
        var selectedValues = [];
        $('.ContactDocTypeResponsibleDropdown').each(function () {
            var value = $(this).dropdown('get value');
            if (value) {
                selectedValues.push(value);
            }
        });
    }
</script>

<script>
    let responsables = @json($policy_contacts);

    // Lógica para llenar los campos dinámicos como en el ejemplo anterior
    responsables.forEach((responsable, index) => {
        // Agregar campos dinámicos si es necesario
        if (index > 0) {
            document.getElementById('addFieldButton').click();
        }

        // Seleccionar el último conjunto de campos dinámicos
        let dynamicFields = document.querySelectorAll('.dynamic-field');
        let currentField = dynamicFields[dynamicFields.length - 1]; // El más reciente

        // Rellenar los campos con los valores del array
        currentField.querySelector('input[name="name_responsible[]"]').value = capitalizeFirstLetterName(
            responsable
                .name_responsible);

        currentField.querySelector('input[name="number_identify_responsible[]"]').value = responsable
            .number_identify_responsible;

        currentField.querySelector('input[name="ocupation_responsible[]"]').value = capitalizeFirstLetter(
            responsable
                .ocupation_responsible);

        currentField.querySelector('input[name="phone_responsible[]"]').value = responsable.phone_responsible;

        currentField.querySelector('input[name="cellphone_responsible[]"]').value = responsable
            .cellphone_responsible;

        currentField.querySelector('input[name="email_responsible[]"]').value = capitalizeFirstLetter(
            responsable.email_responsible);


        currentField.querySelector('input[name="id_responsible[]"]').value = responsable.id;

        // Si no se está en modo emisión y $responsable existe, se asigna su unique_code; de lo contrario, se asigna una cadena vacía.
        {{-- json_encode((!$view_emision && isset($responsable)) ? $responsable->unique_code : '') --}};

        {{-- Se hizo una corrección, este codigo es visible siempre y cuando el rol del usuario no sea Intermediario, Ejecutivo intermediario --}}
        {{-- La condición se valida con php en el input html --}}
        var uniqueCode = responsable?.unique_code || ''; 

        if (uniqueCode) {
            currentField.querySelector('input[name="unique_code[]"]').value = uniqueCode;
        }
        // Manejar el dropdown de tipo de identificación
        let type_identification = responsable.type_identification.split(',')[0];

        // Asegurarse de seleccionar el dropdown como objeto jQuery
        let dropdown = $(currentField).find('.ContactDocTypeResponsibleDropdown');

        // Inicializar el dropdown si es necesario
        dropdown.dropdown();

        // Establecer los valores seleccionados dinámicamente
        dropdown.dropdown('set selected', type_identification);
    });

    function capitalizeFirstLetter(string) {
        return string.charAt(0).toUpperCase() + string.slice(1).toLowerCase();
    }
</script>

{{-- CORREOS DE NOTIFICACIONES ADICIONALES --}}
<script>
    const existingAdditionalNotificationEmails = @json($policy_additional_notification_emails);

    $(document).ready(function () {
        let additionaEmailsIndex = 0;

        // Cargar direcciones existentes si hay
        if (Array.isArray(existingPhones)) {
            existingAdditionalNotificationEmails.forEach(addr => {
                addAdditionalNotificationEmailGroup(addr);
            });
        }

        function addAdditionalNotificationEmailGroup(exisAdditionalNotificationEmail = {}) {
            if ($(".noti-email-group").length >= 2) return;

            const index = additionaEmailsIndex++;

            const group = $(`
                <div class="ui segment noti-email-group" data-index="${index}" style="margin-bottom: 8px;">
                    <div class="ui form">
                        <div class="two fields">
                            <div class="field required">
                                <label>Correo electrónico de notificaciones adicional</label>
                                <input type="email" class="ane_noti_email grayed-input">
                            </div>
                        </div>
                    </div>
                </div>
            `);

            $("#additional-emails-container").append(group);

            // Rellena campos si hay datos
            if (exisAdditionalNotificationEmail.email) group.find(".ane_noti_email").val(exisAdditionalNotificationEmail.email);

            // Si viene con ID, lo guardamos como data-attribute
            if (exisAdditionalNotificationEmail.id) {
                group.attr("data-aneemailid", exisAdditionalNotificationEmail.id);
            }
        }
    });

</script>

{{-- DIRECCIONES ADICIONALES --}}
<script>
    $('.ui.modal').modal();
    $('.ui.dropdown').dropdown();

    const existingAddresses = @json($policy_addresses);

    $(document).ready(function () {
        let costarica = [];
        let locationIndex = 0;

        $.getJSON("/js/costarica.json", function (json) {
            costarica = json["province"];

            // Cargar direcciones existentes si hay
            if (Array.isArray(existingAddresses)) {
                existingAddresses.forEach(addr => {
                    addLocationGroup(addr);
                });
            }
        });

        function addLocationGroup(exisAddress = {}) {
            const index = locationIndex++;

            const group = $(`
                <div class="ui segment location-group" data-index="${index}" style="margin-bottom: 8px;">
                    <div class="ui form">
                        <div class="two fields">
                            <div class="field required">
                                <label>Tipo de Dirección</label>
                                <input list="address_types" type="text" class="addr_type grayed-input">
                            </div>
                            <div class="field">
                                <label>Dirección Completa</label>
                                <input type="text" class="addr_address grayed-input">
                            </div>
                        </div>
                        <div class="three fields">
                            <div class="field required">
                                <label>Provincia</label>
                                <div class="ui search selection dropdown addr_province grayed-input">
                                    <input type="hidden" class="minus grayed-input" name="address_province" value="">
                                    <i class="dropdown icon"></i>
                                    <div class="default text">Selecciona uno</div>
                                    <div class="menu"></div>
                                </div>
                            </div>

                            <div class="field required">
                                <label>Cantón</label>
                                <div class="ui search selection dropdown addr_canton grayed-input">
                                    <input type="hidden" class="minus grayed-input" name="address_canton" value="">
                                    <i class="dropdown icon"></i>
                                    <div class="default text">Selecciona uno</div>
                                    <div class="menu"></div>
                                </div>
                            </div>

                            <div class="field required">
                                <label>Distrito</label>
                                <div class="ui search selection dropdown addr_district grayed-input">
                                    <input type="hidden" class="minus grayed-input" name="address_district" value="">
                                    <i class="dropdown icon"></i>
                                    <div class="default text">Selecciona uno</div>
                                    <div class="menu"></div>
                                </div>
                            </div>
                        </div>
                        <div class="field">
                            <label>Señas del lugar</label>
                            <textarea class="addr_notes grayed-input" rows="1"></textarea>
                        </div>
                    </div>
                </div>
            `);

            $("#locations-container").append(group);

            const $provinceAddr = group.find(".addr_province");
            const $cantonAddr = group.find(".addr_canton");
            const $districtAddr = group.find(".addr_district");

            populateDropdownAddr($provinceAddr, costarica);

            $provinceAddr.on('change', function () {
                const value = $(this).dropdown("get value");
                populateCantonsAddr($cantonAddr, $districtAddr, value);
            });

            $cantonAddr.on('change', function () {
                const value = $(this).dropdown("get value");
                const province = $provinceAddr.dropdown("get value");
                populateDistrictsAddr($districtAddr, province, value);
            });

            $provinceAddr.dropdown();
            $cantonAddr.dropdown();
            $districtAddr.dropdown();

            // Rellena campos si hay datos
            if (exisAddress.type) group.find(".addr_type").val(exisAddress.type);
            if (exisAddress.full_address) group.find(".addr_address").val(exisAddress.full_address);
            if (exisAddress.province_id) {
                $provinceAddr.dropdown("set selected", exisAddress.province_id);
                populateCantonsAddr($cantonAddr, $districtAddr, exisAddress.province_id);

                if (exisAddress.canton_id) {
                    $cantonAddr.dropdown("set selected", exisAddress.canton_id);
                    populateDistrictsAddr($districtAddr, exisAddress.province_id, exisAddress.canton_id);

                    if (exisAddress.district_id) {
                        $districtAddr.dropdown("set selected", exisAddress.district_id);
                    }
                }
            }
            if (exisAddress.reference) group.find(".addr_notes").val(exisAddress.reference);

            // Si viene con ID, lo guardamos como data-attribute
            if (exisAddress.id) {
                group.attr("data-addressid", exisAddress.id);
            }
        }

        function populateDropdownAddr(dropdown, items) {
            dropdown.dropdown("clear").find(".menu").empty();
            items.forEach(item => {
                dropdown.find(".menu").append(`<div class="item" data-value="${item.code}">${capitalizeAddr(item.name)}</div>`);
            });
            dropdown.dropdown("refresh");
        }

        function populateCantonsAddr($cantonAddr, $districtAddr, provinceCode) {
            const province = costarica.find(p => p.code === provinceCode);
            if (province) {
                populateDropdownAddr($cantonAddr, province.cantons);
                const cantonCode = province.cantons[0]?.code;
                if (cantonCode) {
                    $cantonAddr.dropdown("set selected", cantonCode);
                    populateDistrictsAddr($districtAddr, provinceCode, cantonCode);
                }
            }
        }

        function populateDistrictsAddr($districtAddr, provinceCode, cantonCode) {
            const province = costarica.find(p => p.code === provinceCode);
            const canton = province?.cantons.find(c => c.code === cantonCode);
            if (canton) {
                populateDropdownAddr($districtAddr, canton.districts);
                const districtCode = canton.districts[0]?.code;
                if (districtCode) {
                    $districtAddr.dropdown("set selected", districtCode);
                }
            }
        }

        function capitalizeAddr(str) {
            const exceptions = ['de', 'y', 'la', 'el', 'los', 'las', 'un', 'una', 'por', 'para', 'en', 'con'];
            return str.split(' ').map((word, i) =>
                i === 0 || !exceptions.includes(word.toLowerCase())
                    ? word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
                    : word.toLowerCase()
            ).join(' ');
        }
    });

</script>

{{-- TELEFONOS ADICIONALES --}}
<script>
    const existingPhones = @json($policy_phones);

    $(document).ready(function () {
        let countries = [];
        let phoneIndex = 0;

        $.getJSON("/js/paises.json", function (json) {
            countries = json;

            // Cargar direcciones existentes si hay
            if (Array.isArray(existingPhones)) {
                existingPhones.forEach(addr => {
                    addPhoneGroup(addr);
                });
            }
        });

        function addPhoneGroup(exisPhone = {}) {
            const index = phoneIndex++;

            const group = $(`
                <div class="ui segment phone-group" data-index="${index}" style="margin-bottom: 8px;">
                    <div class="ui form">
                        <div class="four fields">
                            <div class="field required">
                                <label>Tipo de teléfono</label>
                                <input list="phone_types" type="text" class="phon_type grayed-input">
                            </div>
                            <div class="field required">
                                <label>Código de país</label>
                                <div class="ui search selection dropdown phon_country grayed-input">
                                    <input type="hidden" class="minus" name="phone_country grayed-input" value="">
                                    <i class="dropdown icon"></i>
                                    <div class="default text">Selecciona uno</div>
                                    <div class="menu"></div>
                                </div>
                            </div>
                            <div class="field required">
                                <label>Número completo</label>
                                <input type="text" class="phon_number grayed-input" oninput="this.value = this.value.replace(/[^0-9]/g, '')">
                            </div>
                            <div class="field">
                                <label>Extensión (opcional)</label>
                                <input type="text" class="phon_extention grayed-input" oninput="this.value = this.value.replace(/[^0-9]/g, '')">
                            </div>
                        </div>
                    </div>
                </div>
            `);

            $("#phones-container").append(group);

            const $countryPhon = group.find(".phon_country");

            populateDropdownPhon($countryPhon, countries);

            $countryPhon.dropdown();

            // Rellena campos si hay datos
            if (exisPhone.type) group.find(".phon_type").val(exisPhone.type);
            if (exisPhone.full_number) group.find(".phon_number").val(exisPhone.full_number);
            if (exisPhone.extention) group.find(".phon_extention").val(exisPhone.extention);
            if (exisPhone.country) $countryPhon.dropdown("set selected", exisPhone.country);

            // Si viene con ID, lo guardamos como data-attribute
            if (exisPhone.id) {
                group.attr("data-phoneid", exisPhone.id);
            }
        }

        function populateDropdownPhon(dropdown, paises) {
            dropdown.dropdown("clear").find(".menu").empty();
            paises.forEach(pais => {
                dropdown.find(".menu").append(`
                    <div class="item" data-value="${pais.country_short_name}">
                        <i class="${pais.country_short_name.toLowerCase()} flag"></i> ${pais.country_name} (${pais.country_short_name})
                    </div>
                `);
            });
            dropdown.dropdown("refresh");
        }
    });

</script>