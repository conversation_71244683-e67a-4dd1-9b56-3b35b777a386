@php use Carbon\Carbon; @endphp
<div class="title">
    <i class="dropdown icon"></i> Reporte de planilla
</div>

<div class="content">
    <table class="ui celled table  ">
        <thead>
            <tr>
                <th>Tipo de ingreso</th>
                <th>Total trabajadores</th>
                <th>Total salarios</th>
                <th>Fecha de carga</th>
                <th>Vigencia desde</th>
                <th>Vigencia hasta</th>
                @if (!$view)
                    <th>Ver planilla</th>
                    <th>Ver certificado</th>
                @endif

            </tr>
        </thead>
        <tbody>
            @foreach ($workSheets as $line)
                @php
                    $createdAt = Carbon::parse($line->created_at);
                    $previousMonth = $createdAt->subMonth();
                    $firstDayOfPreviousMonth = $previousMonth->copy()->firstOfMonth();
                    $lastDayOfPreviousMonth = $previousMonth->copy()->lastOfMonth();
                @endphp
                <tr>
                    <td>
                        {{$line->entry_type ?? ''}}
                    </td>
                    <td>
                        {{ $line->total_affiliates }}
                    </td>
                    <td>
                        {{ number_format($line->total_salaries, 2, ',', '.') }}
                    </td>
                    <td>
                        {{ ucfirst(strftime('%A %e de %B de %Y', strtotime($line->created_at))) }}
                    </td>
                    <td>
                        {{ 
                            $first_workSheet && $first_workSheet->id == $line->id ? 
                                'Emisión' :
                                ucfirst(strftime('%A %e de %B de %Y', strtotime($firstDayOfPreviousMonth))) 
                        }}
                    </td>
                    <td>
                        {{ 
                            $first_workSheet && $first_workSheet->id == $line->id ? 
                                'Emisión' :
                                ucfirst(strftime('%A %e de %B de %Y', strtotime($lastDayOfPreviousMonth))) 
                        }}
                    </td>

                    @if (!$view)

                            @if (!empty($line->activity))
                                @php
                                    // Inicializar la variable del documento
                                    $documentPath = null;

                                    // Buscar en activity_actions solo los que tengan action_id = 85
                                    foreach ($line->activity->activity_actions as $action) {
                                        if ($action->action_id == 85 && !empty($action->documents)) {
                                            foreach ($action->documents as $document) {
                                                if (!empty($document->path)) {
                                                    $documentPath = $document->path;
                                                    break 2; // Salimos de ambos foreach al encontrar el primer documento válido
                                                }
                                            }
                                        }
                                    }
                                @endphp

                                @if ($documentPath)
                                    <td class="center aligned">
                                        <div class="icon-buttons-responsive">
                                            <button type="button"
                                                    onclick="window.open('{{ secure_url('file/' . $documentPath) }}', '_blank')"
                                                    class="ui icon tiny secondary button"
                                                    title="Descargar planilla">
                                                <i class="download icon"></i>
                                            </button>

                                            <button type="button"
                                                    onclick="window.open('{{ secure_url('/spreadsheet/'.$line->id.'/generar-excel') }}', '_blank')"
                                                    class="ui icon tiny secondary button"
                                                    title="Descargar Excel">
                                                <i class="file excel icon"></i>
                                            </button>
                                        </div>
                                    </td>

                                    <td class="center aligned">
                                        @if($line->path_report)
                                            <button type="button"
                                                    onclick="window.open('{{ secure_url('file/' . $line->path_report) }}', '_blank')"
                                                    class="ui icon tiny secondary button" title="Reporte resumen de la planilla">
                                                <i class="download icon"></i>
                                            </button>
                                        @endif
                                    </td>
                                @else
                                    <td class="center aligned">
                                        <button type="button"
                                            onclick="Swal.fire({
                                            title: 'Certificado de Planilla en Proceso',
                                            text: `El certificado de planilla está siendo generado, este estará disponible para su descarga el día de mañana. Gracias por su paciencia.`,
                                            imageUrl: '/file/client_logo/logo_mnk.png',
                                            imageHeight: 50,
                                            imageWidth: 150,
                                            confirmButtonText: 'Cerrar'
                                        });"
                                            class="ui icon tiny secondary button"
                                            title="Certificado en proceso">
                                            <i class="info circle icon"></i>
                                        </button>
                                    </td>
                                    <td class="center aligned">
                                        <a class="ui icon tiny secondary button"
                                           title="Certificado resumen de planilla en proceso"
                                           onclick="Swal.fire({
                                                title: 'Certificado resumen de planilla en proceso',
                                                text: `El certificado resumen de planilla está siendo generado, este estará disponible para su descarga el día de mañana. Gracias por su paciencia.`,
                                                imageUrl: '/file/client_logo/logo_mnk.png',
                                                imageHeight: 50,
                                                imageWidth: 150,
                                                confirmButtonText: 'Cerrar'
                                            });">

                                            <i class="info circle icon"></i>
                                        </a>
                                    </td>
                                @endif
                            @else
                                <td class="center aligned">
                                    <button type="button"
                                        onclick="Swal.fire({
                                        title: 'Certificado de Planilla en Proceso',
                                        text: `El certificado de planilla está siendo generado, este estará disponible para su descarga el día de mañana. Gracias por su paciencia.`,
                                        imageUrl: '/file/client_logo/logo_mnk.png',
                                        imageHeight: 50,
                                        imageWidth: 150,
                                        confirmButtonText: 'Cerrar'
                                    });"
                                        class="ui icon tiny secondary button"
                                        title="Certificado en proceso">
                                        <i class="info circle icon"></i>
                                    </button>
                                </td>
                                <td class="center aligned">
                                    <a class="ui icon tiny secondary button"
                                       title="Certificado resumen de planilla en proceso"
                                       onclick="Swal.fire({
                                                title: 'Certificado resumen de planilla en proceso',
                                                text: `El certificado resumen de planilla está siendo generado, este estará disponible para su descarga el día de mañana. Gracias por su paciencia.`,
                                                imageUrl: '/file/client_logo/logo_mnk.png',
                                                imageHeight: 50,
                                                imageWidth: 150,
                                                confirmButtonText: 'Cerrar'
                                            });">
                                        <i class="info circle icon"></i>
                                    </a>
                                </td>
                            @endif

                    @endif


                </tr>
            @endforeach
        </tbody>
    </table>
</div>
<style>
    .icon-buttons-responsive {
        display: flex;
        gap: 6px;
        justify-content: center;
        align-items: center;
        flex-wrap: wrap;
        width: 100%;
    }

    @media (max-width: 600px) {
        .icon-buttons-responsive {
            flex-direction: column;
        }
    }
</style>

<script>

    function downloadReporte(id) {
        Swal.fire({
            title: 'El documento se está descargando',
            text: 'Por favor, espere mientras termina el proceso.',
            allowOutsideClick: false,
            showConfirmButton: false,
            willOpen: () => {
                Swal.showLoading();
            }
        });

        let filename = 'Planilla.pdf';
        let responseObj = null;

        fetch('/intermediario/formulario/generateReportePlanilla', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '{{ csrf_token() }}',
            },
            body: JSON.stringify({ id: id }),
        })
            .then(response => {
                responseObj = response;

                const contentType = response.headers.get('Content-Type') || '';
                const isJson = contentType.includes('application/json');

                if (!response.ok) {
                    if (isJson) {
                        return response.json().then(errorJson => {
                            throw new Error(errorJson.message || 'Error desconocido');
                        });
                    } else {
                        throw new Error('Error al generar el reporte de planilla');
                    }
                }

                const contentDisposition = response.headers.get('Content-Disposition');
                if (contentDisposition && contentDisposition.includes('filename=')) {
                    filename = contentDisposition.split('filename=')[1].replace(/"/g, '');
                }

                return response.blob();
            })
            .then(blob => {
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = filename;
                document.body.appendChild(a);
                a.click();
                a.remove();
                Swal.close();
            })
            .catch(error => {
                Swal.close();
                loadingMain(false);
                console.error(error);
                Swal.fire({
                    icon: 'error',
                    text: error.message || "Hubo un error al generar el reporte de planilla",
                    confirmButtonText: 'OK'
                });
            });
    }


    function downloadReporte_vfvv(id) {
        //e.preventDefault();

        Swal.fire({
            title: 'El documento se está descargando',
            text: 'Por favor, espere mientras termina el proceso.',
            allowOutsideClick: false,
            showConfirmButton: false,
            willOpen: () => {
                Swal.showLoading(); // Iniciar loader
            }
        });

        fetch('/intermediario/formulario/generateReportePlanilla', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '{{ csrf_token() }}',
            },
            body: JSON.stringify({
                id: id
            }),
        })
            .then(response => {
                if (response.ok) {
                    loadingMain(false);
                    return response.blob();
                } else {
                    loadingMain(false);
                    throw new Error('Error al generar el reporte de planilla');
                }

                // Ocultar el loader de Swal cuando la solicitud sea exitosa
                Swal.close();
            })
            .then(blob => {
                loadingMain(false);
                // Crear un enlace temporal para descargar el archivo generado
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'Planilla.pdf';
                document.body.appendChild(a);
                a.click();
                a.remove();

                // Ocultar el loader de Swal cuando la solicitud sea exitosa
                Swal.close();
            })
            .catch(error => {
                // Ocultar el loader de Swal cuando la solicitud sea exitosa
                Swal.close();
                loadingMain(false);
                console.error(error);
                Swal.fire({
                    icon: 'error',
                    // title: 'Solicitud exitosa',
                    text: "Hubo un error al generar el reporte de planilla",
                    confirmButtonText: 'OK'
                });
            });
    }

    function downloadPlanilla(e, id) {

        e.preventDefault();

        Swal.fire({
            title: 'El documento se está descargando',
            text: 'Por favor, espere mientras termina el proceso.',
            allowOutsideClick: false,
            showConfirmButton: false,
            willOpen: () => {
                Swal.showLoading(); // Iniciar loader
            }
        });

        fetch('/intermediario/formulario/generateExcelTemplateManual', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '{{ csrf_token() }}',
                },
                body: JSON.stringify({
                    id: id
                }),
            })
            .then(response => {
                if (response.ok) {
                    loadingMain(false);
                    return response.blob();
                } else {
                    loadingMain(false);
                    throw new Error('Error al generar la plantilla');
                }

                // Ocultar el loader de Swal cuando la solicitud sea exitosa
                Swal.close();
            })
            .then(blob => {
                loadingMain(false);
                // Crear un enlace temporal para descargar el archivo generado
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'Planilla.pdf';
                document.body.appendChild(a);
                a.click();
                a.remove();

                // Ocultar el loader de Swal cuando la solicitud sea exitosa
                Swal.close();
            })
            .catch(error => {
                // Ocultar el loader de Swal cuando la solicitud sea exitosa
                Swal.close();
                loadingMain(false);
                console.error(error);
                Swal.fire({
                    icon: 'error',
                    // title: 'Solicitud exitosa',
                    text: "Hubo un error al generar la planilla",
                    confirmButtonText: 'OK'
                });
            });
    }
</script>
