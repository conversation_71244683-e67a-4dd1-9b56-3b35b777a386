<div class="title">
    <i class="dropdown icon"></i>
    Datos del representante legal<span style="color: red;" class="required">*</span>
</div>
<div class="content">
    <div class="two fields">
        <div class="required field">
            <label for="legalRepresentativeName">Nombre del representante legal</label>
            <div class="ui input">

                <input readonly name="legalRepresentativeName" id="legalRepresentativeName" autocomplete="off"
                    type="text" value="{{ mb_convert_case(mb_strtolower( $policy_sort->legal_representative_name ?? ''), MB_CASE_TITLE, "UTF-8") }}">
            </div>
        </div>

        <div class="required field">
            <label for="legalRepresentativeId">identificación del representante legal</label>
            <div class="ui input">

                <input readonly name="legalRepresentativeId" id="legalRepresentativeId" autocomplete="off"
                    type="text" value="{{ $policy_sort->legal_representative_id }}">
            </div>
        </div>
    </div>
    <div class="two fields">

        <!-- Grupo de ocupación del afiliado -->
        <div class="required field">
            <label>Ocupación por grupo del representante legal</label>
            <div class="ui dropdown button input_button grayed-input"
                style="display: flex !important; flex-direction: row; justify-content: space-between;" id="occupationsGroupDropdownRL">
                <span class="text" style="font-weight: normal;">Selecciona una opción</span>
                <i class="dropdown icon"></i>
                <input type="hidden" name="legal_representative_occupancy_group_id" id="legal_representative_occupancy_group_id"
                    value="{{ $policy_sort->legal_representative_occupancy_group_id ?? '' }}">
                <input type="hidden" name="legal_representative_occupancy_group_texto" id="legal_representative_occupancy_group_texto">
                <div class="menu">
                    @foreach ($OCCUPATION_GROUPS as $key => $value)
                        <div class="item">
                            <i class="dropdown icon"></i>
                            <span class="text">{{ $key }}</span>
                            <div class="menu custom-submenu-rl">
                                @foreach ($value as $key_sub => $value_sub)
                                    <div class="item" data-value="{{ $key_sub }}">
                                        {{ $value_sub }}</div>
                                @endforeach

                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>

        <div class="required field">
            <label for="legalRepresentativeProfession">Ocupación del representante legal</label>
            <div class="ui input">
                <input readonly name="legalRepresentativeProfession" id="legalRepresentativeProfession"
                    autocomplete="off" type="text"
                    value="{{ ucfirst(strtolower($policy_sort->legal_representative_profession)) }}">
            </div>
        </div>
    </div>
</div>
