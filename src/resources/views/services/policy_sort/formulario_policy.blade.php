@extends('layouts.main')

@section('title', 'Vista del tramitador - Cotizaciones')

@section('menu')
    @parent
@endsection

@section('content')
    <div class="intermediary ui segment">
        <form class="ui attached form" action="{{ secure_url('/get_payment_control_it') }}" method="post">
            {{ csrf_field() }}
            <div class="ui basic segment" id="policyholder">
                <div class="ui styled fluid accordion">
                    <div class="active title">
                        <i class="dropdown icon"></i>
                        Datos Intermediario  </div>
                    <div class="active content">
                        <div class="field">
                            <div class="two fields doble_input">
                                <div class=" required field">
                                    <label for="">Nombre de la Correduría </label>
                                    <div>
                                        <input type="text" id="name_c" name="name_c"
                                               placeholder="Nombre de la Correduría ">
                                    </div>
                                </div>
                                <div class="required field">
                                    <label for="">Nombre del Corredor y/o Asesor </label>
                                    <div>
                                        <input type="text" id="name_c_a"  name="name_c_a"
                                               placeholder="Nombre del Corredor y/o Asesor ">
                                    </div>
                                </div>
                            </div>
                            <div class="two fields doble_input">
                                <div class=" required field">
                                    <label for="">Código </label>
                                    <div>
                                        <input type="text" id="code" name="code"  placeholder="Código  ">
                                    </div>
                                </div>
                                <div class="required field">
                                    <label for="">Email nuevos negocios </label>
                                    <div>
                                        <input type="email" id="new_emails" name="new_emails"
                                               placeholder="Email nuevos negocios ">
                                    </div>
                                </div>
                            </div>
                            <div class=" fields title">
                                <div class=" required field">
                                    <label for="">Comisión Renovación (%) </label>
                                    <div>
                                        <input  class="title" type="text" id="comision-renovacion" name="comision-renovacion"
                                                value="10%" readonly>
                                    </div>
                                </div>
                                <div class="required field">
                                    <label for="">Comisión Emisión (%) </label>
                                    <div>
                                        <input  class="title" type="text" id="comision-emision" name="comision-emision"
                                                value="10%" readonly>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="ui basic segment">
                <div class="ui styled fluid accordion">
                    <div class="active title">
                        <i class="dropdown icon"></i>
                        Datos De La Póliza
                    </div>
                    <div class="active content">
                        <div class="four fields">
                            <div class="required field">
                                <label for="policyNumber">No Póliza</label>
                                <div class="ui input">

                                    <input name="policyNumber" id="policyNumber" autocomplete="off" type="text" value="">
                                </div>
                            </div>

                            <div class="required field">
                                <label for="startValidityDate">Fecha de Inicio Vigencia</label>
                                <input id="startValidityDate" type="text" name="startValidityDate" class="datepicker" placeholder="dd/mm/yyyy">
                            </div>

                            <div class="required field">
                                <label for="renewalDate">Fecha de Renovación*</label>
                                <input id="renewalDate" type="text" name="renewalDate" class="datepicker" placeholder="dd/mm/yyyy">
                            </div>
                            <div></div>
                        </div>
                        <div class="fields">
                            <!-- Primera columna (Estado de plantilla) -->
                            <div class="six wide field">
                                <div class="accordion">
                                    <div class="active title">
                                        <i class="dropdown icon"></i>
                                        Estado de plantilla
                                    </div>
                                    <div class="active content">
                                        <div class="field">
                                            <div class="grouped fields">
                                                <div class="field">
                                                    <div class="ui radio checkbox">
                                                        <input type="radio" name="templateStatus" id="reported" value="Reportada">
                                                        <label for="reported">Reportada</label>
                                                    </div>
                                                </div>
                                                <div class="field">
                                                    <div class="ui radio checkbox">
                                                        <input type="radio" name="templateStatus" id="uploadPending" value="Pendiente de Cargue">
                                                        <label for="uploadPending">Pendiente de Cargue</label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- Segunda columna (Valor de la Prima y otros campos) -->
                            <div class="ten wide field">
                                <div class="fields">
                                    <div class="six wide required field">
                                        <label for="premiumValue">Valor de la Prima</label>
                                        <div class="ui input">
                                            <input name="premiumValue" id="premiumValue" autocomplete="off" type="text" value="">
                                        </div>
                                    </div>
                                    <div class="six wide required field">
                                        <label for="paymentMethod">Forma de Pago</label>
                                        <div class="ui input">
                                            <input name="paymentMethod" id="paymentMethod" autocomplete="off" type="text" value="">
                                        </div>
                                    </div>
                                    <div class="six wide required field">
                                        <label for="accumulatedPaidPremium">Valor prima pagado acumulado</label>
                                        <div class="ui input">
                                            <input name="accumulatedPaidPremium" id="accumulatedPaidPremium" autocomplete="off" type="text" value="">
                                        </div>
                                    </div>
                                </div>

                                <!-- Segunda fila dentro de la segunda columna -->
                                <div class="fields">
                                    <div class="six wide required field">
                                        <label for="initialPaymentStatus">Estado de Pago Inicial</label>
                                        <div class="ui input">
                                            <input name="initialPaymentStatus" id="initialPaymentStatus" autocomplete="off" type="text" value="">
                                        </div>
                                    </div>
                                    <div class="six wide required field">
                                        <label for="initialPaymentDate">Fecha de Pago Inicial</label>
                                        <div class="ui input">
                                            <input name="initialPaymentDate" id="initialPaymentDate" type="text" class="datepicker" placeholder="dd/mm/yyyy">
                                        </div>
                                    </div>
                                    <div class="six wide required field">
                                        <label for="lastPaymentDate">Fecha Último pago</label>
                                        <div class="ui input">
                                            <input name="lastPaymentDate" id="lastPaymentDate" type="text" class="datepicker" placeholder="dd/mm/yyyy">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>


            <div class="ui basic segment">
                <div class="ui styled fluid accordion">
                    <div class="active title">
                        <i class="dropdown icon"></i>
                        Datos De Emisión Póliza
                    </div>
                    <div class="active content">
                        <div class="accordion">
                            <div class="active title">
                                <i class="dropdown icon"></i>
                                Identificación Del Tomador
                            </div>
                            <div class="active content">
                                <div class="two fields">
                                    <div class="required field">
                                        <label for="typeIdentify">Tipo de identificación</label>
                                        <div class="ui input">
                                            <input name="typeIdentify" id="typeIdentify" autocomplete="off" type="text" value="">
                                        </div>
                                    </div>

                                    <div class="required field">
                                        <label for="numberIdentify">Número de Identificación</label>
                                        <div class="ui input">
                                            <input name="numberIdentify" id="numberIdentify" autocomplete="off" type="text" value="">
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>

                        <div class="accordion">
                            <div class="active title">
                                <i class="dropdown icon"></i>
                                Datos Del Tomador
                            </div>
                            <div class="active content">
                                <div class="three fields">
                                    <div class="required field">
                                        <label for="policyHolderName">Nombre</label>
                                        <div class="ui input">

                                            <input name="policyHolderName" id="policyHolderName" autocomplete="off" type="text" value="">
                                        </div>
                                    </div>

                                    <div class="required field">
                                        <label for="policyHolderPhone">Teléfonos</label>
                                        <div class="ui input">

                                            <input name="policyHolderPhone" id="policyHolderPhone" autocomplete="off" type="text" value="">
                                        </div>
                                    </div>

                                    <div class="required field">
                                        <label for="policyHolderEmail">Email de notificaciones</label>
                                        <div class="ui input">

                                            <input name="policyHolderEmail" id="policyHolderEmail" autocomplete="off" type="text" value="">
                                        </div>
                                    </div>
                                </div>
                                <div class="three fields">
                                    <div class="required field">
                                        <label for="emailElectronicBilling">Email para facturación electrónica</label>
                                        <div class="ui input">

                                            <input name="emailElectronicBilling" id="emailElectronicBilling" autocomplete="off" type="text" value="">
                                        </div>
                                    </div>

                                    <div class="required field">
                                        <label for="ibanAcccount">Cuenta IBAN</label>
                                        <div class="ui input">

                                            <input name="ibanAcccount" id="ibanAcccount" autocomplete="off" type="text" value="">
                                        </div>
                                    </div>

                                    <div></div>
                                </div>
                                <div class="three fields">
                                    <div class="required field">
                                        <label for="employerAddress">Dirección del Patrono</label>
                                        <div class="ui input">

                                            <input name="employerAddress" id="employerAddress" autocomplete="off" type="text" value="">
                                        </div>
                                    </div>

                                    <div></div>
                                    <div></div>
                                </div>
                                <div class="three fields">
                                    <div class="required field">
                                        <label for="province">Provincia</label>
                                        <div class="ui input">

                                            <input name="province" id="province" autocomplete="off" type="text" value="">
                                        </div>
                                    </div>

                                    <div class="required field">
                                        <label for="canton">Cantón</label>
                                        <div class="ui input">

                                            <input name="canton" id="canton" autocomplete="off" type="text" value="">
                                        </div>
                                    </div>

                                    <div class="required field">
                                        <label for="district">Distrito</label>
                                        <div class="ui input">

                                            <input name="district" id="district" autocomplete="off" type="text" value="">
                                        </div>
                                    </div>
                                </div>
                                <div class="three fields">
                                    <div class="field">
                                        <div class="grouped fields">
                                            <div class="required field">
                                                <label>Sector</label>
                                            </div>
                                            <div class="field">
                                                <div class="ui radio checkbox">
                                                    <input type="radio" name="sector" id="public" value="Público">
                                                    <label for="public">Público</label>
                                                </div>
                                            </div>
                                            <div class="field">
                                                <div class="ui radio checkbox">
                                                    <input type="radio" name="sector" id="private" value="Privado">
                                                    <label for="private">Privado</label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="required field">
                                        <label for="economicActivityCode">Código de la Actividad Económica</label>
                                        <div class="ui input">
                                            <input name="economicActivityCode" id="economicActivityCode" autocomplete="off" type="text" value="">
                                        </div>
                                    </div>

                                    <div class="required field">
                                        <label for="economicActivityName">Nombre Actividad Económica</label>
                                        <div class="ui input">
                                            <input name="economicActivityName" id="policyNumber" autocomplete="off" type="text" value="">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="accordion">
                            <div class="active title">
                                <i class="dropdown icon"></i>
                                Datos del Representante Legal
                            </div>
                            <div class="active content">
                                <div class="three fields">
                                    <div class="required field">
                                        <label for="legalRepresentativeName">Nombre del representante legal</label>
                                        <div class="ui input">

                                            <input name="legalRepresentativeName" id="legalRepresentativeName" autocomplete="off" type="text" value="">
                                        </div>
                                    </div>

                                    <div class="required field">
                                        <label for="legalRepresentativeId">identificación del representante legal</label>
                                        <div class="ui input">

                                            <input name="legalRepresentativeId" id="legalRepresentativeId" autocomplete="off" type="text" value="">
                                        </div>
                                    </div>

                                    <div class="required field">
                                        <label for="legalRepresentativeProfession">Profesión del representante Legal</label>
                                        <div class="ui input">

                                            <input name="legalRepresentativeProfession" id="legalRepresentativeProfession" autocomplete="off" type="text" value="">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="accordion">
                            <div class="active title">
                                <i class="dropdown icon"></i>
                                Datos Firma
                            </div>
                            <div class="active content">
                                <div class="accordion">
                                    <div class="active title">
                                        <i class="dropdown icon"></i>
                                        Firma física
                                    </div>
                                    <div class="active content">
                                        <div class="two fields">
                                            <div class="required field">
                                                <label for="policyNumber">Descargar Documento Firma física</label>
                                                <div class="ui input">
                                                    <input name="policyNumber" id="policyNumber" autocomplete="off" type="text" value="">
                                                </div>
                                            </div>

                                            <div class="required field">
                                                <label for="policyNumber">Cargar documento firmado</label>
                                                <div class="ui input">

                                                    <input name="policyNumber" id="policyNumber" autocomplete="off" type="text" value="">
                                                </div>
                                            </div>

                                        </div>
                                        <div class="two fields">
                                            <div class="required field">
                                                <label for="policyNumber">Fecha de firma solicitud emisión</label>
                                                <div class="ui input">

                                                    <input name="policyNumber" id="policyNumber" autocomplete="off" type="text" value="20/08/2025 10:30 a.m." readonly>
                                                </div>
                                            </div>

                                            <div></div>
                                        </div>
                                    </div>
                                </div>

                                <div class="accordion">
                                    <div class="active title">
                                        <i class="dropdown icon"></i>
                                        Firma digital
                                    </div>
                                    <div class="active content">
                                        <div class="two fields">
                                            <div class="required field">
                                                <label for="policyNumber">Firma del representante o apoderado</label>
                                                <div class="ui input">

                                                    <input name="policyNumber" id="policyNumber" autocomplete="off" type="text" value="">
                                                </div>
                                            </div>

                                            <div></div>
                                        </div>
                                        <div class="two fields">
                                            <div class="required field">
                                                <label for="policyNumber">Firma intermediario</label>
                                                <div class="ui input">

                                                    <input name="policyNumber" id="policyNumber" autocomplete="off" type="text" value="">
                                                </div>
                                            </div>

                                            <div></div>
                                        </div>
                                        <div class="two fields">
                                            <div class="required field">
                                                <label for="policyNumber">Fecha de firma solicitud emisión</label>
                                                <div class="ui input">

                                                    <input name="policyNumber" id="policyNumber" autocomplete="off" type="text" value="20/08/2025 10:30 a.m." readonly>
                                                </div>
                                            </div>

                                            <div></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="accordion">
                            <div class="active title">
                                <i class="dropdown icon"></i>
                                Documentación Externo
                            </div>
                            <div class="active content">
                                <table class="ui celled table">
                                    <thead>
                                    <tr>
                                        <th>Documento</th>
                                        <th>Fecha de cargue</th>
                                        <th>Acciones</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <tr>
                                        <td>Copia identificación del representante Legal</td>
                                        <td>17/05/2018</td>
                                        <td>
                                            <div class="inline fields">
                                                <div class="field">
                                                    <button class="ui icon green button">
                                                        <i class="download icon"></i>
                                                    </button>
                                                </div>
                                                <div class="field">
                                                    <button class="ui icon blue button">
                                                        <i class="upload icon"></i>
                                                    </button>
                                                </div>
                                            </div>

                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Persona jurídica</td>
                                        <td>17/05/2018</td>
                                        <td>
                                            <div class="inline fields">
                                                <div class="field">
                                                    <button class="ui icon green button">
                                                        <i class="download icon"></i>
                                                    </button>
                                                </div>
                                                <div class="field">
                                                    <button class="ui icon blue button">
                                                        <i class="upload icon"></i>
                                                    </button>
                                                </div>
                                            </div>

                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Autorizaciones</td>
                                        <td></td>
                                        <td>
                                            <div class="inline fields">
                                                <div class="field">
                                                    <button class="ui icon green button">
                                                        <i class="download icon"></i>
                                                    </button>
                                                </div>
                                                <div class="field">
                                                    <button class="ui icon blue button">
                                                        <i class="upload icon"></i>
                                                    </button>
                                                </div>
                                            </div>

                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Emisión de solicitud</td>
                                        <td></td>
                                        <td>
                                            <div class="inline fields">
                                                <div class="field">
                                                    <button class="ui icon green button">
                                                        <i class="download icon"></i>
                                                    </button>
                                                </div>
                                                <div class="field">
                                                    <button class="ui icon blue button">
                                                        <i class="upload icon"></i>
                                                    </button>
                                                </div>
                                            </div>

                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Conozca a su cliente</td>
                                        <td>17/05/2018</td>
                                        <td>
                                            <div class="inline fields">
                                                <div class="field">
                                                    <button class="ui icon green button">
                                                        <i class="download icon"></i>
                                                    </button>
                                                </div>
                                                <div class="field">
                                                    <button class="ui icon blue button">
                                                        <i class="upload icon"></i>
                                                    </button>
                                                </div>
                                            </div>

                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="ui basic segment" id="policyholder">
                <div class="ui styled fluid accordion">
                    <div class="active title">
                        <i class="dropdown icon"></i>
                        Reporte Planillas </div>
                    <div class="active content  ">

                        <div class="ui basic segment " id="policyholder">
                            <div class="ui styled fluid celled table ">
                                <div class="active title ">
                                    <i class="dropdown icon"></i>
                                    Planilla Inicial</div>
                                <table class="ui celled table  ">
                                    <thead>
                                    <tr>
                                        <th>Tipo de ingreso
                                            <i class="dropdown icon"></i>
                                        </th>
                                        <th>Total trabajadores</th>
                                        <th>Total salarios</th>
                                        <th>Fecha de cargue</th>
                                        <th>vigencia desde</th>
                                        <th>Vigencia hasta</th>
                                        <th>Ver Planilla</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <tr>
                                        <td>          <div class="ui selection dropdown" id="typeCurrencyDropdown">
                                                <i class="dropdown icon"></i>
                                                <div class="default text"></div>
                                                <div class="menu">
                                                    @foreach($typeincomes as $k => $value)
                                                        <div class="item {{ $value === '' ? 'selected' : '' }}" data-value="{{ $k }}">
                                                            {{ $value }}
                                                        </div>
                                            @endforeach                      </td>
                                        <td>                                </td>
                                        <td>                                </td>
                                        <td>                                </td>
                                        <td>                                </td>
                                        <td>                              </td>
                                        <td>
                                            <div class="required field">
                                                <input type="file" name="affiliate_file" accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel">
                                            </div>
                                            <div class="field">
                                                <button class="ui blue button"><i class="upload icon"></i> Cargar</button>
                                                <a href="https://renapp-colpensiones.s3.us-east-2.amazonaws.com/Templates/PLANTILLA_CARGUE_MDI.xlsx">PLANTILLA_CARGUE_MDI.xlsx</a>
                                            </div></td>
                                    </tr>

                                    </tbody>
                                </table>
                            </div>
                            <!-- boton para crear o solicitar nueva cotización -->
                            <div class="row pull-right" style="margin-right: 10px;">
                                <button class="ui button green"
                                        style="color: white; background-color: #00cc00; border-color: #00cc00;"
                                        onclick="createQuotitation(event)">+ Crear nueva cotización</button>

                            </div>
                        </div>


                        <!-- Spin de carga -->
                        <div style="display: none" id="loading" class="ui active centered inline loader"></div>

                        <!-- Seccion de mensajes -->
                        <div style="display: none; padding-left: 50px; padding-top: 20px; padding-right: 50px;" id="message">
                            <div class="ui teal message" id="dynamicMessage"></div>
                        </div>


                        <div class="ui basic segment " id="policyholder ">
                            <div class="ui styled fluid  celled  table ">
                                <div class="active title Colr ">
                                    <i class="dropdown icon  "></i>
                                    Informe Mensual Planilla </div>

                                <table class="ui celled table  ">
                                    <thead>
                                    <tr>
                                        <th>Tipo de ingreso</th>
                                        <th>Total trabajadores</th>
                                        <th>Total salarios</th>
                                        <th>Fecha de cargue</th>
                                        <th>vigencia desde</th>
                                        <th>Vigencia hasta</th>
                                        <th>Ver Planilla</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <tr>
                                        <td>         <div class="ui selection dropdown" id="typeCurrencyDropdown">
                                                <i class="dropdown icon"></i>
                                                <div class="default text"></div>
                                                <div class="menu">
                                                    @foreach($typeincomes as $k => $value)
                                                        <div class="item {{ $value === '' ? 'selected' : '' }}" data-value="{{ $k }}">
                                                            {{ $value }}
                                                        </div>
                                                    @endforeach
                                                </div>
                                            </div>                </td>
                                        <td>                         </td>
                                        <td>                          </td>
                                        <td>                         </td>
                                        <td>                         </td>
                                        <td>                        </td>
                                        <td><div class="required field">
                                                <input type="file" name="affiliate_file" accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel">
                                            </div>
                                            <div class="field">
                                                <button class="ui blue button"><i class="upload icon"></i> Cargar</button>
                                                <a href="https://renapp-colpensiones.s3.us-east-2.amazonaws.com/Templates/PLANTILLA_CARGUE_MDI.xlsx">PLANTILLA_CARGUE_MDI.xlsx</a>
                                            </div>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                                <!-- boton para crear o solicitar nueva cotización -->
                                <div class="row pull-right" style="margin-right: 10px;">
                                    <button class="ui button green"
                                            style="color: white; background-color: #00cc00; border-color: #00cc00;"
                                            onclick="createQuotitation(event)">+ Crear nueva cotización</button>
                                </div>
                            </div>

                            <!-- Spin de carga -->
                            <div style="display: none" id="loading" class="ui active centered inline loader"></div>

                            <!-- Seccion de mensajes -->
                            <div style="display: none; padding-left: 50px; padding-top: 20px; padding-right: 50px;" id="message">
                                <div class="ui teal message" id="dynamicMessage"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="ui basic segment " id="policyholder ">
                <div class="ui styled fluid accordion">
                    <div class="active title">
                        <i class="dropdown icon"></i>
                        Periodo Calendario de la Compañia</div>
                    <div class=" content   fields  ">
                        <div class="  fields">
                            <div class="required field">
                                <label for="Period_type">Periodo de calendario</label>
                                <div class="ui selection dropdown" id="typeCurrencyDropdown">
                                    <input type="hidden" name="type_currency" id="type_currency">
                                    <i class="dropdown icon"></i>
                                    <div class="default text"></div>
                                    <div class="menu">
                                        @foreach($calendarPeriods as $k => $value)
                                            <div class="item {{ $value === 'Ordinario' ? 'selected' : '' }}" data-value="{{ $k }}">
                                                {{ $value }}
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="ui basic segment " id="policyholder ">
                            Periodo

                            <div class=" content   fields  ">
                                <div class="required field">
                                    <label for="start_tDate">Fecha de Inicio </label>
                                    <input id="start_Date" type="text" name="start_Date" class="datepicker" placeholder="dd/mm/yyyy">
                                </div>

                                <div class="required field">
                                    <label for="end_Date">Fecha fin</label>
                                    <input id="end_Date" type="text" name="end_Date" class="datepicker" placeholder="dd/mm/yyyy">
                                </div>


                                <div class="" style="margin-right: 10px; margin-top: 24px">
                                    <button class="ui button "
                                            style="color: white; background-color: #00cc00; border-color: #00cc00;"
                                            onclick="createQuotitation(event)">+ </button>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
            </div>  {{-- <div class="ui button" tabindex="0">Guardar</div> --}}
            <div class="ui basic segment">
                <div class="ui styled fluid accordion">
                    <div class="active title">
                        <i class="dropdown icon"></i>
                        Prima Emisión
                    </div>
                    <div class="active content">
                        <div class="accordion">
                            <div class="active title">
                                <i class="dropdown icon"></i>
                                Datos Emisión Póliza
                            </div>
                            <div class="active content">
                                <div class="three fields">
                                    <div class="required field heir_payment-class" id="heir_doc_type">

                                        <div class="ui form">
                                            <div class="required field">
                                                <label>Temporalidad</label>
                                            </div>
                                            <div class="inline fields">

                                                <div class="field">
                                                    <div class="ui radio checkbox">
                                                        <input type="radio" name="temporality" value="permanent" tabindex="0">
                                                        <label>Permanente</label>
                                                    </div>
                                                </div>
                                                <div class="field">
                                                    <div class="ui radio checkbox">
                                                        <input type="radio" name="temporality" value="short" tabindex="1">
                                                        <label>Periodo Corto</label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                    </div>

                                    <div class="required field">
                                        <label for="validity_from">Vigencia de la póliza desde</label>
                                        <input id="validity_from" type="text" name="validity_from" class="datepicker" placeholder="dd/mm/yyyy">
                                    </div>

                                    <div class="required field">
                                        <label for="validity_to">Vigencia de la póliza hasta</label>
                                        <input id="validity_to" type="text" name="validity_to" class="datepicker" placeholder="dd/mm/yyyy">
                                    </div>

                                </div>

                                <div class="three fields">
                                    <div class="field">
                                        <div class="accordion">
                                            <div class="active title">
                                                <i class="dropdown icon"></i>
                                                Modalidad aseguramiento
                                            </div>
                                            <div class="active content">

                                            </div>
                                        </div>
                                    </div>
                                    <div class="required field">
                                        <label for="moneytype">Tipo de moneda</label>
                                        <div class="ui selection dropdown" id="typeCurrencyDropdown">
                                            <input type="hidden" name="type_currency" id="type_currency">
                                            <i class="dropdown icon"></i>
                                            <div class="default text">Seleccione una opción</div>
                                            <div class="menu">
                                                @foreach($MONEY_TYPE as $k => $currency)
                                                    <div class="item" data-value="{{ $k }}">
                                                        <input type="hidden" class="type_currency_" data-nomenclature="{{ $k }}" data-symbol="{{ $currency['symbol'] }}" data-name="{{ $currency['name'] }}">
                                                        {{ $currency['symbol'] }} {{ $currency['name'] }}
                                                    </div>
                                                @endforeach
                                            </div>
                                        </div>
                                    </div>

                                    <div class="required field">
                                        <label for="salary_projection">Planilla mensual</label>
                                        <input class="prompt" id="salary_projection" type="text" name="salary_projection" value="{{old('salary_projection')}}">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="accordion">
                            <div class="active title">
                                <i class="dropdown icon"></i>
                                Valor Prima a Pagar
                            </div>
                            <div class="active content">
                                <div class="accordion">
                                    <div class="active title">
                                        <i class="dropdown icon"></i>
                                        Cálculo De La Prima Póliza Permanente
                                    </div>
                                    <div class="active content">
                                        <div class="four fields">
                                            <div class="required field">
                                                <label for="typeIdentify">Anual *</label>
                                                <div class="ui input">
                                                    <input name="typeIdentify" id="typeIdentify" autocomplete="off" type="text" value="">
                                                </div>
                                            </div>

                                            <div class="required field">
                                                <label for="numberIdentify">Semestral *</label>
                                                <div class="ui input">
                                                    <input name="numberIdentify" id="numberIdentify" autocomplete="off" type="text" value="">
                                                </div>
                                            </div>

                                            <div class="required field">
                                                <label for="numberIdentify">Trimestral *</label>
                                                <div class="ui input">
                                                    <input name="numberIdentify" id="numberIdentify" autocomplete="off" type="text" value="">
                                                </div>
                                            </div>

                                            <div class="required field">
                                                <label for="numberIdentify">Mensual *</label>
                                                <div class="ui input">
                                                    <input name="numberIdentify" id="numberIdentify" autocomplete="off" type="text" value="">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="accordion">
                                    <div class=" title active">
                                        <i class="dropdown icon"></i>
                                        Cálculo De La Prima Póliza Periodo Corto
                                    </div>
                                    <div class="content active">
                                        <div class="four fields">
                                            <div class="required field">
                                                <label for="typeIdentify">Pago único *</label>
                                                <div class="ui input">
                                                    <input name="typeIdentify" id="typeIdentify" autocomplete="off" type="text" value="">
                                                </div>
                                            </div>

                                            <div></div>
                                            <div></div>
                                            <div></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="ui basic segment">
                <div class="ui styled fluid accordion">
                    <div class="active title">
                        <i class="dropdown icon"></i>
                        Datos del pago
                    </div>
                    <div class="active content">
                        <div class="four fields">
                            <div class="required field">
                                <label for="cardholder">Titular de la tarjeta</label>
                                <div class="ui input">
                                    <input name="cardholder" id="cardholder" autocomplete="off" type="text" value="">
                                </div>
                            </div>

                            <div class="required field">
                                <label for="cvv">cvv</label>
                                <div class="ui input">
                                    <input name="cvv" id="cvv" autocomplete="off" type="text" value="">
                                </div>
                            </div>

                            <div class="required field">
                                <label for="cardNumber">Número de tarjeta</label>
                                <div class="ui input">
                                    <input name="cardNumber" id="cardNumber" autocomplete="off" type="text" value="">
                                </div>
                            </div>
                        </div>
                        <div class="four fields">
                            <div class="required field">
                                <label for="expirationDate">Fecha de vencimiento</label>
                                <div class="ui input">
                                    <input name="expirationDate" id="expirationDate" autocomplete="off" type="text" value="">
                                </div>
                            </div>

                            <div class="required field">
                                <label for="paymentStatus">Estado del pago</label>
                                <div class="ui input">
                                    <input name="paymentStatus" id="paymentStatus" autocomplete="off" type="text" value="">
                                </div>
                            </div>

                            <div class="required field">
                                <label for="paymentDate">Fecha del pago</label>
                                <div class="ui input">
                                    <input name="paymentDate" id="paymentDate" autocomplete="off" type="text" value="">
                                </div>
                            </div>

                            <div class="required field">
                                <label for="transactionId">ID de la transacción</label>
                                <div class="ui input">
                                    <input name="transactionId" id="transactionId" autocomplete="off" type="text" value="">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="ui basic segment">
                <div class="ui styled fluid accordion">
                    <div class="active title">
                        <i class="dropdown icon"></i>
                        Control de pagos abonados
                    </div>
                    <div class="active content">
                        <div class="active content">
                            <div class="accordion">
                                <div class="active title">
                                    <i class="dropdown icon"></i>
                                    Datos de la Póliza
                                </div>
                                <div class="active content">
                                    <div class="four fields">
                                        <div class="required field">
                                            <label for="numPolicy">Póliza SORT</label>
                                            <div class="ui input">
                                                <input name="numPolicy" id="numPolicy" autocomplete="off" type="text" value="">
                                            </div>
                                        </div>

                                        <div class="required field">
                                            <label for="effectiveStartDate">Fecha de inicio vigencia</label>
                                            <div class="ui input">
                                                <input name="effectiveStartDate" id="effectiveStartDate" autocomplete="off" type="text" value="">
                                            </div>
                                        </div>

                                        <div class="required field">
                                            <label for="projectedInitialPremiumValue">Valor prima inicial proyectada</label>
                                            <div class="ui input">
                                                <input name="projectedInitialPremiumValue" id="projectedInitialPremiumValue " autocomplete="off" type="text" value="">
                                            </div>
                                        </div>

                                        <div class="required field">
                                            <label for="methodOfPayment">Forma de pago</label>
                                            <div class="ui input">
                                                <input name="methodOfPayment" id="methodOfPayment" autocomplete="off" type="text" value="">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="accordion">
                                <div class="active title">
                                    <i class="dropdown icon"></i>
                                    Reporte Pagos Abonos
                                </div>
                                <div class="active content">
                                    <div class="three fields">
                                        <div class="required field">
                                            <label for="numAccumulatedCredits">No. Abonos acumulados</label>
                                            <div class="ui input">
                                                <input name="numAccumulatedCredits" id="numAccumulatedCredits" autocomplete="off" type="text" value="">
                                            </div>
                                        </div>

                                        <div class="required field">
                                            <label for="accumulatedPaidPremiumValue">Valor prima pagado acumulado</label>
                                            <div class="ui input">
                                                <input name="accumulatedPaidPremiumValue" id="accumulatedPaidPremiumValue" autocomplete="off" type="text" value="">
                                            </div>
                                        </div>
                                        <div></div>
                                    </div>
                                    <div class="three fields">
                                        <div class="required field">
                                            <label for="valuepaid1">Valor pagado - Abono 1</label>
                                            <div class="ui input">
                                                <input name="valuepaid1" id="valuepaid1" autocomplete="off" type="text" value="">
                                            </div>
                                        </div>

                                        <div class="required field">
                                            <label for="datepaid1">Fecha del pago - Abono 1</label>
                                            <div class="ui input">
                                                <input name="datepaid1" id="datepaid1" autocomplete="off" type="text" value="">
                                            </div>
                                        </div>

                                        <button class="ui icon green button">
                                            <i class="plus square outline icon"></i>
                                        </button>
                                    </div>
                                    <div class="three fields">
                                        <div class="field">
                                            <label for="valuepaid2">Valor pagado - Abono 2</label>
                                            <div class="ui input">
                                                <input name="valuepaid2" id="valuepaid2" autocomplete="off" type="text" value="">
                                            </div>
                                        </div>

                                        <div class="field">
                                            <label for="datepaid2">Fecha del pago - Abono 2</label>
                                            <div class="ui input">
                                                <input name="datepaid2" id="datepaid2" autocomplete="off" type="text" value="">
                                            </div>
                                        </div>
                                        <div></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>

        <style>
            /*******************************
                    intermediary
            *******************************/

            .intermediary .tabla-interna {
                margin-left: 20px;
                margin-right: 20px;
                margin-bottom: 20px;
            }
            .intermediary .Colr {
                background-color: #6b9dbb;
            }

        .intermediary .title {
            margin-top: 0em !important;
            text-align: center;

        }

        .intermediary .ui.dividing.header {
            padding-bottom: 1.214286rem;
            border-bottom: 1px solid rgba(34, 36, 38, .15);
        }
        input {
                text-transform: capitalize !important;
            }
        /*******************************
                intermediary
        *******************************/
    </style>

</div>
<div style="display: none" id="loading" class="ui active centered inline loader"></div>

    <script type="text/javascript">
        $(document).ready(function() {
            var url = window.location.href;
            var id = url.substring(url.lastIndexOf('/') + 1);
            $.ajax({
                url: '/webservice-acsel/1',
                type: 'GET',
                success: function(response) {
                    $("#name_c").val(response.BrokerageryName);
                    $("#name_c_a").val(response.IntermediaryName);
                    $("#code").val(response.IntermediaryCode);
                },
                error: function(response) {
                    console.log('Error al cargar los datos');
                }
            });

            $("#new_emails").blur(function() {
                const valor = $(this).val();
                const campo = $(this).attr('id');
                const activity_id = $('#activity_id').val();
                const campo2 = $('#name_c').val();
                const campo3 = $('#name_c_a').val();
                const campo4 = $('#code').val();

                const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

                if (!emailPattern.test(valor)) {
                    Swal.fire({
                        icon: 'warning',
                        // title: 'Solicitud exitosa',
                        text: "Por favor, ingrese un correo electrónico válido.",
                        confirmButtonText: 'OK'
                    });
                    return;
                }
            });


            // Inicializa el accordion
            $('.ui.accordion').accordion();

            // Inicializa los checkboxes de
            $('.ui.radio.checkbox').checkbox();

            let startValidityDate = $('#startValidityDate').pickadate({
                selectYears: 100,
                selectMonths: true,
                min: new Date(),
                formatSubmit: 'yyyy-mm-dd',
                format: 'dd/mm/yyyy'
            }).pickadate('picker');

            let renewalDate = $('#renewalDate').pickadate({
                selectYears: 100,
                selectMonths: true,
                min: new Date(),
                formatSubmit: 'yyyy-mm-dd',
                format: 'dd/mm/yyyy'
            }).pickadate('picker');


            let lastPaymentDate = $('#lastPaymentDate').pickadate({
                selectYears: 100,
                selectMonths: true,
                min: new Date(),
                formatSubmit: 'yyyy-mm-dd',
                format: 'dd/mm/yyyy'
            }).pickadate('picker');

            let initialPaymentDate = $('#initialPaymentDate').pickadate({
                selectYears: 100,
                selectMonths: true,
                min: new Date(),
                formatSubmit: 'yyyy-mm-dd',
                format: 'dd/mm/yyyy'
            }).pickadate('picker');


            let fromPicker = $('#validity_from').pickadate({
                selectYears: 100,
                selectMonths: true,
                min: new Date(),
                formatSubmit: 'yyyy-mm-dd',
                format: 'dd/mm/yyyy'
            }).pickadate('picker');

            let toPicker = $('#validity_to').pickadate({
                selectYears: 100,
                selectMonths: true,
                min: new Date(),
                formatSubmit: 'yyyy-mm-dd',
                format: 'dd/mm/yyyy'
            }).pickadate('picker');

            let start_Date = $('#start_Date').pickadate({
                selectYears: 100,
                selectMonths: true,
                min: new Date(),
                formatSubmit: 'yyyy-mm-dd',
                format: 'dd/mm/yyyy'
            }).pickadate('picker');

            let end_Date = $('#end_Date').pickadate({
                selectYears: 100,
                selectMonths: true,
                min: new Date(),
                formatSubmit: 'yyyy-mm-dd',
                format: 'dd/mm/yyyy'
            }).pickadate('picker');
        });

        // Inicializar el dropdown
        $('.ui.dropdown').dropdown();


    </script>
@endsection
