
<div class="ui basic segment" id="payroll">
    <div class="ui styled fluid accordion_validity">
        <div class="title">
            <i class="dropdown icon"></i>
            Modificar datos de trabajadores actuales
        </div>
        <div class="content">
            <div class="two fields">
                <div class="required field">
                    <label for="docType" style="margin-bottom: 0.5rem; margin-right: -3%;">Tipo de documento</label>
                    <div class="ui fluid selection dropdown" style="margin-right: 1%;">
                        <input type="hidden" name="doc_type" value="">
                        <i class="dropdown icon"></i>
                        <div class="default text">Tipo de documento</div>
                        <div class="menu">
                            <div class="item" data-value="RB">RADICADO BIZAGI</div>
                        </div>
                    </div>

                    <label for="docNumber" style="margin-top: 0rem;">Número de documento</label>
                    <div id="searchInput" class="ui left icon action fluid input" style="width: 100%;">
                        <i class="user icon"></i>
                        <input list="docList" name="doc_number" id="docNumber" autocomplete="off" type="text" value="">
                        <button class="ui green right labeled icon button">
                            <i class="search icon"></i>
                            Buscar
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript -->


<style type="text/css">
    #payroll {
        width: 164% !important
    }
</style>

<script>
    $('.ui.dropdown').dropdown();
</script>



