
<div class="ui basic segment" id="payroll">
    <div class="ui styled fluid accordion_validity">
        <div class="title">
            <i class="dropdown icon"></i>
            Reportar planilla sin modificaciones
        </div>
        <div class="content">
            <div class="ui form">
                <div class="field">
                    <a href="#" id="download-template" class="ui teal label">
                        Descargar plantilla <i class="download icon"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript -->
<script>
    document.getElementById('download-template').addEventListener('click', function (e) {
        e.preventDefault();

        let policySortId = window.selectedPolicyData.id;
        fetch('/intermediario/formulario/generar-planilla', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '{{ csrf_token() }}',
            },
            body: JSON.stringify({
                policy_sort_id: policySortId
            }),
        })
            .then(response => {
                if (response.ok) {
                    return response.blob();
                } else {
                    throw new Error('Error al generar la plantilla');
                }
            })
            .then(blob => {
                // Crear un enlace temporal para descargar el archivo generado
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'Planilla.xlsx';
                document.body.appendChild(a);
                a.click();
                a.remove();
            })
            .catch(error => {
                console.error(error);
                Swal.fire({
                    icon: 'error',
                    // title: 'Solicitud exitosa',
                    text: 'Por favor, seleccione un tipo de documento y cargue el archivo.',
                    confirmButtonText: 'OK'
                });
            });
    });
</script>

<style type="text/css">
    #payroll {
        width: 164% !important
    }
</style>
