<div class="ui attached planilla_manual" style="padding-bottom: 40px;">
    {{ csrf_field() }}

    <div class="ui basic segment">
        <div class="ui styled fluid accordion">

            <div class="title active" style="pointer-events: none;">
                <i class="dropdown icon"></i>
                Reporte de planillas <span style="color: red;" class="required">*</span>
            </div>

            <div class="content active">
                <div class="ui form ocultar_todo">
                    <div class="five fields">
                        <div class="required field">
                            <label for="typeChargeDropdown">Tipo de ingreso</label>
                            <div class="ui selection dropdown {{ (isset($isReport) && $isReport) || in_array($policy_sort->work_modality_id, [6, 7]) ? 'disabled' : '' }}"
                                id="typeChargeDropdown">
                                @php
                                    if ($policy_spreadsheet && isset($policy_spreadsheet->file) && !isset($policy_spreadsheet->file_txt)) {
                                        $type_charge = 'Carga masiva';
                                    }
                                    if (($policy_spreadsheet && !isset($policy_spreadsheet->file)) || in_array($policy_sort->work_modality_id, [6, 7])) {
                                        $type_charge = 'Manual';
                                    }
                                    if($policy_spreadsheet && isset($policy_spreadsheet->file_txt)){
                                        $type_charge = 'Cargar archivo TXT';
                                    }
                                @endphp
                                <input type="hidden" name="type_charge" id="type_charge" value="{{ $type_charge ?? '' }}" {{ isset($isReport) && $isReport ? 'disabled' : '' }}>
                                <i class="dropdown icon"></i>
                                <div class="default text">Seleccione una opción</div>
                                <div class="menu">
                                    @foreach ($TYPE_INCOME as $k => $value)
                                        {{-- Si work_modality es 4 o 3, no mostrar la opción "Carga masiva" --}}
                                        @if (!in_array($policy_sort->work_modality_id, [2, 3, 4]) || $k != 'C')
                                            <div class="item" data-value="{{ $k }}">{{ $value }}</div>
                                        @endif
                                    @endforeach
                                </div>
                            </div>
                        </div>

                        <div class="required field">
                            <label for="total-workers">Total de trabajadores</label>
                            <div class="ui input">
                                <input name="total-workers" id="total-workers" autocomplete="off" type="text"
                                    readonly value=""
                                    class="{{ isset($isReport) && $isReport ? 'disabled' : '' }}">
                            </div>
                        </div>

                        <div class="required field">
                            <label for="moneytype">Tipo de moneda</label>
                            <div class="ui selection dropdown disabled" id="typeCurrencyDropdown">
                                <input type="hidden" name="type_currency" id="type_currency"
                                    value="{{ $policy_sort->type_currency }}">
                                <i class="dropdown icon"></i>
                                <div class="default text type_currency">Seleccione una opción</div>
                                <div class="menu">
                                    @foreach ($MONEY_TYPE as $k => $currency)
                                        <div class="item type_currency" data-value="{{ $k }}">
                                            <input type="hidden" class="type_currency_"
                                                data-nomenclature="{{ $k }}"
                                                data-symbol="{{ $currency['symbol'] }}"
                                                data-name="{{ $currency['name'] }}">
                                            {{ $currency['symbol'] }}
                                            {{ mb_convert_case($currency['name'], MB_CASE_TITLE, 'UTF-8') }}
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        </div>

                        <div class="required field">
                            <label for="total-salaries">Total salario</label>
                            <div class="ui input">
                                <input name="total-salaries" id="total-salaries" autocomplete="off" type="text"
                                    readonly value="">
                            </div>
                        </div>
                    </div>

                    @if (isset($isReport) && $isReport)
                        <div class="ui message">
                            <div class="header">Importante</div>
                            <p>La planilla para el mes actual ya está cerrada.</p>
                        </div>
                    @endif
                </div>
                @if (!isset($isReport) || !$isReport)
                    @if (!in_array($policy_sort->work_modality_id, [6, 7]))
                        <div class="ui compact message" style="display: none; width: 100%;" id="message_actividad">
                            <p>Actividad económica o modalidad de aseguramiento, no requiere planilla inicial.</p>
                        </div>
                    @endif

                    <div id="policyholderManually" class="ocultar">
                        <div class="accordion transition">
                            <div class=" title" style="{{ isset($isReport) && $isReport ? 'pointer-events: none;' : '' }}">
                                <i class="dropdown icon"></i>
                                Cargar trabajadores manualmente
                            </div>

                            <div id="manual-section" class="content field"
                                style="overflow-x: auto; overflow-y: auto; height: 200px;">
                                @if ($policy_sort->work_modality_id !== 2 && $policy_sort->work_modality_id !== 3)
                                    <table class="ui form celled sortable striped very compact very small collapsing table"
                                        id="dynamicTable" style="width: 100%; min-width: 1200px;">
                                        <thead>
                                            <tr>
                                                <th>Tipo de identificación</th>
                                                <th>Nacionalidad</th>
                                                <th># identificación</th>
                                                <th>Nombres</th>
                                                <th>Apellidos</th>
                                                <th>Fecha nacimiento</th>
                                                <th>Sexo</th>
                                                <th>Correo electrónico</th>
                                                <th>Tipo jornada</th>
                                                <th>Salario mensual</th>
                                                <th>Días</th>
                                                <th>Horas</th>
                                                <th>Ocupación</th>
                                                <th>Observación</th>
                                                <th></th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>
                                                    <select name="ti" style="width: auto">
                                                        @foreach ($DOC_TYPES as $key => $value)
                                                            @if ($key != 'CJ')
                                                                <option value="{{ $key }}">{{ $value }}
                                                                </option>
                                                            @endif
                                                        @endforeach
                                                    </select>
                                                </td>
                                                <td>
                                                    <div class="ui fluid search selection dropdown"
                                                        style="width: 150px; position: relative;">
                                                        <input type="hidden" name="nacionalidad">
                                                        <i class="dropdown icon"></i>
                                                        <div class="default text">Nacionalidad</div>
                                                        <div class="menu paises">

                                                        </div>
                                                    </div>
                                                </td>

                                                <td><input type="text" max="15" name="no_identificacion"
                                                        placeholder="# Identificación" pattern="[A-Z0-9]{1,15}"></td>
                                                <td><input type="text" max="20" name="nombres"
                                                        placeholder="nombres" class="input-width-1"></td>
                                                <td><input type="text" max="20" name="apellidos"
                                                        placeholder="Primer Apellido" class="input-width-2"></td>
                                                <td><input type="date" name="fecha_nacimiento"></td>
                                                <td>
                                                    <select name="sexo" class="input-width-1" style="width: auto">
                                                        <option value="M">Masculino</option>
                                                        <option value="F">Femenino</option>
                                                    </select>
                                                </td>
                                                <td><input type="email" id='correo_electronico'
                                                        name="correo_electronico" placeholder="Correo electrónico"
                                                        required>


                                                <td>
                                                    <select name="tipo_jornada" style="width: auto">
                                                        @if(in_array($policy_sort->work_modality_id, [1, 5, 6, 7]))
                                                            <option value="TC">Tiempo completo</option>
                                                            <option value="TM">Tiempo medio</option>
                                                        @elseif($policy_sort->work_modality_id == 2)
                                                            <option value="TC">Tiempo completo</option>
                                                            <option value="TM">Tiempo medio</option>
                                                            <option value="OD">Ocasional contratado por días</option>
                                                            <option value="OH">Ocasional contratado por horas</option>
                                                        @elseif(in_array($policy_sort->work_modality_id, [3, 4]))
                                                            <option value="OD">Ocasional contratado por días</option>
                                                            <option value="OH">Ocasional contratado por horas</option>
                                                        @endif
                                                    </select>

                                                </td>
                                                <td><input type="text" name="salario_mensual"
                                                        placeholder="Salario Mensual" style="width: 10rem !important;">
                                                </td>
                                                <td><input type="text" name="dias" placeholder="Días" class="input-width-3"></td>
                                                <td><input type="text" max="400" name="horas"
                                                        placeholder="Horas" class="input-width-3"></td>
                                                <td><input type="text" max="40" name="ocupacion"
                                                        placeholder="Ocupación" class="input-width-3"></td>
                                                <td>
                                                    <textarea name="observation_affiliate" placeholder="Observaciones" rows="3" cols="100" style="width: 300px;"></textarea>
                                                </td>
                                                <td>
                                                @if (!in_array($policy_sort->work_modality_id, [6, 7]))
                                                    <button class="ui icon button secondary addRow pop"
                                                        data-tooltip="Añadir">
                                                        <i class="plus icon"></i>
                                                    </button>
                                                @endif
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                @else
                                    <table class="ui form celled sortable striped very compact very small collapsing table"
                                        id="dynamicTable" style="width: 100%; min-width: 1200px;">
                                        <thead>
                                            <tr>
                                                <th>Tipo de identificación</th>
                                                <th># identificación</th>
                                                <th>Nombres</th>
                                                <th>Apellidos</th>
                                                <th>Observación</th>
                                                <th></th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>
                                                    <select name="ti">
                                                        @foreach ($DOC_TYPES as $key => $value)
                                                            @if ($key != 'CJ')
                                                                <option value="{{ $key }}">{{ $value }}
                                                                </option>
                                                            @endif
                                                        @endforeach
                                                    </select>
                                                </td>

                                                <td><input type="text" max="15" name="no_identificacion"
                                                        placeholder="# Identificación" pattern="[A-Z0-9]{1,15}"></td>

                                                <td><input type="text" max="20" name="nombres"
                                                        style="width: 100% !important" placeholder="nombres"
                                                        class="input-width-1">
                                                </td>

                                                <td><input type="text" max="20" name="apellidos"
                                                        style="width: 100% !important" placeholder="Primer Apellido"
                                                        class="input-width-2"></td>
                                                <td>
                                                    <textarea name="observation_affiliate" placeholder="Observaciones" rows="3" cols="100" style="width: 300px;"></textarea>
                                                </td>
                                                <td>
                                                    <button class="ui icon button secondary addRow_condition pop"
                                                        data-tooltip="Añadir">
                                                        <i class="plus icon"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                @endif


                                <div>
                                    <button style="margin-top: 10px;" id="uploadManuallyRecords"
                                        class="ui primary button">Guardar planilla
                                    </button>

                                    <a onclick="download_explanation()" class="ui secondary button">
                                        <i class="ui icon download"></i> Descargar explicación planilla
                                    </a>
                                </div>

                            </div>


                        </div>
                    </div>

                    <div class="form ui basic segment ocultar" id="policyholderMassive">
                        <div class=" accordion transition ">
                            <div class=" title"
                                style="{{ isset($isReport) && $isReport ? 'pointer-events: none;' : '' }}">
                                <i class="dropdown icon"></i>
                                Cargar trabajadores con planilla masivamente
                            </div>
                            <div class="content">
                                <div class="fields three">
                                    <div class=" field">
                                        <input type="file" name="affiliate_file" accept=".xlsx, .xls">
                                    </div>
                                    <div class=" field">
                                        <button id="uploadMassiveRecords" class="ui button primary" type="button">
                                            Guardar planilla
                                        </button>
                                    </div>
                                    <div class="field">
                                        <div class="fields three">
                                            <div class="field">
                                                <a onclick="download_explanation()" class="ui secondary button">
                                                    <i class="ui icon download"></i> Descargar explicación conceptual de los diferentes campos de la planilla
                                                </a>
                                            </div>
                                            <div class="field">
                                                <a onclick="download_planilla()" class="ui secondary button">
                                                    <i class="ui icon download"></i> Descargar formato de Excel para cargue de planilla
                                                </a>
                                            </div>
                                            <div class="field" id="policySpreadsheetButtonContainer">
                                                @if (!empty($policy_spreadsheet) && !empty($policy_spreadsheet->file))
                                                    <button
                                                            type="button"
                                                            onclick="window.open('{{ secure_url('file/' . $policy_spreadsheet->file) }}', '_blank')"
                                                            class="ui button secondary">
                                                        <i class="download icon"></i>Descargar Excel de planilla recién cargada
                                                    </button>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form ui basic segment ocultar" id="txtFile">
                        <div class=" accordion transition ">
                            <div class=" title"
                                 style="{{ isset($isReport) && $isReport ? 'pointer-events: none;' : '' }}">
                                <i class="dropdown icon"></i>
                                Cargar trabajadores con archivo TXT
                            </div>
                            <div class="content">
                                <div class="fields three">
                                    <div class=" field">
                                        <input type="file" name="txt_file" accept=".txt">
                                    </div>
                                    <div class="field">
                                        <button id="uploadTxtFile" class="ui button primary" type="button">
                                            Cargar archivo TXT
                                        </button>
                                    </div>
                                    <div class="field two wide" id="generateExcel"
                                         style="{{ !empty($policy_spreadsheet) && !empty($policy_spreadsheet->file_txt) ? '' : 'display: none;' }}">
                                        <button id="downloadExcelButton" onclick="startDownloadPlanilla()" class="ui secondary button">
                                            <i class="ui icon download"></i> Descargar Excel de planilla recién cargada
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                @endif
                @if (in_array($policy_sort->work_modality_id, [6, 7]))
                <div class="ui orange message" id="info-message">
                    <div class="header">
                        Esta modalidad de póliza <b>({{ $WORK_MODALITY[$policy_sort->work_modality_id] }})</b> solo permite 1 persona asegurada. Si se pretende incluir más trabajadores, se debe cambiar la modalidad a <b>"Riesgos del Trabajo General"</b>
                    </div>
                </div>
                @endif
            </div>
        </div>
        <br>
        <div style="display: flex; justify-content: space-between; width: 100%;">
            <button class="ui button primary" id="step" onclick="backTab('documentos_externa')">Atrás

            </button>
            <button class="ui button primary" id="next" onclick="nextTab('issuance_premium')">Siguiente
            </button>
        </div>
        <!-- Modal de carga -->
        <div id="progressContainer" style="display:none; width: 100%; margin-top: 20px;">
            <div class="ui progress" id="progressBar" data-percent="0">
                <div class="bar"></div>
                <div class="label">Cargando empleados...</div>
            </div>
        </div>

        <div class="alert warning" id="warningAlert" style="display:none;"></div>

    </div>
{{--    <div class="alert warning" id="warningAlert" style="display:none;"></div>--}}
</div>

<style>
    input[readonly],
    textarea[readonly] {
        background-color: #f3f4f5 !important;
    }

    .alert {
        color: #721c24;
        padding: 15px;
        margin: 50px;
        border: 1px solid #f5c6cb;
        border-radius: 5px;
        max-height: 200px;
        overflow-y: auto;
        display: none;
    }

    .input-width-1 {
        width: 118px !important;
    }

    .input-width-2 {
        width: 125px !important;
    }

    .input-width-3 {
        width: 6rem !important;
    }

    #policyholderManually,
    #policyholderMassive,#txtFile {
        display: none;
    }
</style>

<script type="text/javascript">
    $('#download-template').on('click', function(e) {

        e.preventDefault();

        Swal.fire({
            title: 'El documento se está descargando',
            text: 'Por favor, espere mientras termina el proceso.',
            allowOutsideClick: false,
            showConfirmButton: false,
            willOpen: () => {
                Swal.showLoading(); // Iniciar loader
            }
        });

        let policySortId = @json(isset($policy_sort) ? $policy_sort->id : null);
        loadingMain(true);
        if (policySortId) {
            fetch('/intermediario/formulario/generar-planilla', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}',
                    },
                    body: JSON.stringify({
                        policy_sort_id: policySortId
                    }),
                })
                .then(response => {
                    loadingMain(false);
                    if (response.ok) {
                        loadingMain(false);
                        return response.blob();
                    } else {
                        loadingMain(false);
                        throw new Error('Error al generar la plantilla');
                    }

                    // Ocultar el loader de Swal cuando la solicitud sea exitosa
                    Swal.close();
                })
                .then(blob => {
                    loadingMain(false);
                    // Crear un enlace temporal para descargar el archivo generado
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = 'Planilla.pdf';
                    document.body.appendChild(a);
                    a.click();
                    a.remove();

                    // Ocultar el loader de Swal cuando la solicitud sea exitosa
                    Swal.close();
                })
                .catch(error => {
                    // Ocultar el loader de Swal cuando la solicitud sea exitosa
                    Swal.close();
                    loadingMain(false);
                    console.error(error);
                    Swal.fire({
                        icon: 'error',
                        // title: 'Solicitud exitosa',
                        text: "Hubo un error al generar la planilla",
                        confirmButtonText: 'OK'
                    });
                });
        }

    });

    $(document).ready(function() {
        var action = @json($existActionWithNotReport);

        const currentUrl = window.location.href;

        const tienePlanillaManual = currentUrl.includes('ingresar_planilla_manualmente');

        //modalidad de aseguramiento
        let work_modality = @json($policy_sort->work_modality_id);
        const policySort = @json($policy_sort ?? null);

        const tienePlanillaManualFromPoliza = currentUrl.includes('informe_planilla');
        if (tienePlanillaManualFromPoliza && work_modality == 6 && policySort?.doc_type && policySort?.doc_number) { 
            $('select[name="ti"]').val(policySort?.doc_type).prop('disabled', true);
            $('input[name="no_identificacion"]').val(policySort?.doc_number).prop('disabled', true);
            $('input[name="correo_electronico"]').val(policySort?.email || "");
            $('input[name="ocupacion"]').val(policySort?.legal_representative_profession || "").prop('disabled', true);
        }

        // Ocultar la sección de carga manual cuando la modalidad de aseguramiento es RT 6 = Riesgos del Trabajo Independiente
        if (tienePlanillaManual && work_modality == 6) {
            $('.planilla_manual').hide();
        } else {
            $('.planilla_manual').show();
        }

        
        if (action && !tienePlanillaManual || work_modality == 4) {
            $('.ocultar_todo').hide();
            $('#message_actividad').show();

        } else {
            $('.ocultar_todo').show();
        }

        // Comprueba si el mensaje de reportes está presente
        @if (isset($isReport) && $isReport && isset($type_charge))
            $('#typeChargeDropdown').dropdown({
                onChange: function (value) {
                    $('#type_charge').val(value);
                }
            }).dropdown('set selected', '{{ $type_charge }}');
            $(".accordion").each(function () {
                $(this).find("input").attr("readonly", true);
                $(this).find("input").addClass("prompt");
                $(this).find(".dropdown").addClass("disabled");

                $(this).find(".content").each(function () {
                    if ($(this).find(".accordion").length === 0) {
                        // Si este .content no tiene hijos con la clase .accordion, desactiva los clics y cambia el cursor a "not-allowed"
                        $(this).css({"pointer-events": "none"});
                    } else {
                        // Si tiene hijos con la clase .accordion, desactiva los clics solo en los hijos
                        $(this).find(".content").each(function () {
                            $(this).css({"pointer-events": "none"});
                        });
                    }
                });
            });
        @endif

    });

    var policy_spreadsheet = {!! json_encode($policy_spreadsheet) !!};

    document.addEventListener("DOMContentLoaded", function() {
        if (policy_spreadsheet) {
            // Asignar el valor de total_affiliates al campo de trabajadores
            document.getElementById('total-workers').value = policy_spreadsheet.total_affiliates ?? 0;

            // Asignar el valor de total_salaries al campo de salarios
            document.getElementById('total-salaries').value = @json($total_salaries);
        }
    });


    // Validación para correos electrónicos
    $(document).on('blur', 'input[name="correo_electronico"]', function() {
        const valor = $(this).val();
        const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

        if (!emailPattern.test(valor)) {
            Swal.fire({
                icon: 'warning', // Corregido el icono (de 'warinig' a 'warning')
                text: "Por favor, ingrese un correo electrónico válido.",
                confirmButtonText: 'OK',
                 confirmButtonColor: '#91c845'
            });
            return;
        }
    });


    function nextTab(event) {
        const workers = $('#total-workers').val();
        var action = @json($existActionWithNotReport);

        if (workers <= 0 && !action) {

            Swal.fire({
                icon: 'error',
                title: 'Solicitud denegada',
                text: "Por favor, ingrese un trabajador.",
                confirmButtonText: 'Aceptar',
                confirmButtonColor: '#000000',
                customClass: {
                    popup: 'custom-swal', // Clase personalizada
                },

            });

            return;
        }




        //Captura el tipo de moneda seleccionado
        const selectedCurrency = $('#type_currency').val();
        const policySortId = '{{ $policy_sort->id }}';
        // Validar tipo de moneda
        if (!selectedCurrency) {
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: 'Debe seleccionar un tipo de moneda.',
                confirmButtonText: 'OK'
            });
            return;
        }
        loadingMain(true);
        $.ajax({
            url: '/update-currency-type',
            method: 'POST',
            data: {
                _token: '{{ csrf_token() }}',
                policy_sort_id: policySortId,
                type_currency: selectedCurrency,
            },
            success: function(response) {
                if (response.success) {
                    window.location.href =
                        '/intermediario/poliza/{{ $id }}/prima_emision_policy';
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: response.message,
                        confirmButtonText: 'OK'
                    });
                }
            },
            error: function(xhr) {
                loadingMain(false);
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'Ocurrió un problema al guardar el tipo de moneda. Inténtalo nuevamente.',
                    confirmButtonText: 'OK'
                });
            }
        });
    }

    function backTab(event) {
        window.location.href = '/intermediario/poliza/{{ $id }}/documentos_externa';
    }

    $('.ui.dropdown').dropdown();
    $('.ui.accordion').accordion();

    /**
     * Función para agregar dinámicamente una fila con las restricciones de modalidad de aseguramiento
     */
    $(document).on('click', '.addRow', function(e) {
        e.preventDefault();

        let tableBody = $('#dynamicTable tbody');
        const docTypes = @json($DOC_TYPES);
        let docTypeOptions = '';

        for (let key in docTypes) {
            if (docTypes.hasOwnProperty(key)) {
                docTypeOptions += `<option value="${key}">${docTypes[key]}</option>`;
            }
        }

        const assurance = {{ $policy_sort->assurance ?? 'null' }};

        // Contar filas de trabajadores de tipo Tiempo Completo o Tiempo Medio
        const permanentWorkersCount = tableBody.find('tr').filter(function() {
            const jornadaValue = $(this).find('select[name="tipo_jornada"]').val();
            return jornadaValue === 'TC' || jornadaValue === 'TM';
        }).length;

        // Crear una nueva fila con opciones de tipo de jornada según las validaciones pertinentes
        const workModality = {{ $policy_sort->work_modality_id ?? 'null' }};
        let workShiftOptions = '';

        if ([1, 5].includes(workModality)) {
            workShiftOptions = `
        <option value="TC">Tiempo completo</option>
        <option value="TM">Tiempo medio</option>
    `;
        } else if (workModality === 2) {
            workShiftOptions = `
        <option value="TC">Tiempo completo</option>
        <option value="TM">Tiempo medio</option>
        <option value="OD">Ocasional contratado por días</option>
        <option value="OH">Ocasional contratado por horas</option>
    `;
        } else if ([3, 4].includes(workModality)) {
            workShiftOptions = `
        <option value="OD">Ocasional contratado por días</option>
        <option value="OH">Ocasional contratado por horas</option>
    `;
        }

        let newRow = `
        <tr>
            <td>
                <select name="ti" style="width: auto">
                    ${docTypeOptions}
                </select>
            </td>
            <td>
                <div class="ui fluid search selection dropdown" style="width: 150px; position: relative;">
                    <input type="hidden" name="nacionalidad">
                    <i class="dropdown icon"></i>
                    <div class="default text">Nacionalidad</div>
                    <div class="menu paises"></div>
                </div>
            </td>
            <td><input type="text" max="15" name="no_identificacion" placeholder="# Identificación" pattern="[A-Z0-9]{1,15}"></td>
            <td><input type="text" max="20" name="nombres" placeholder="nombres" class="input-width-1"></td>
            <td><input type="text" max="20" name="apellidos" placeholder="Primer Apellido" class="input-width-2"></td>
            <td><input type="date" name="fecha_nacimiento"></td>
            <td>
                <select name="sexo" style="width: auto">
                    <option value="M">Masculino</option>
                    <option value="F">Femenino</option>
                </select>
            </td>
            <td><input type="text" max="20" name="correo_electronico" placeholder="Correo electrónico" class="input-width-2"></td>
            <td>
                <select name="tipo_jornada" style="width: auto">
                    ${workShiftOptions}
                </select>
            </td>
            <td><input type="text" name="salario_mensual" placeholder="Salario Mensual" style="width: 10rem !important;"></td>
            <td><input type="text" name="dias" placeholder="Días" class="input-width-3"></td>
            <td><input type="text" max="400" name="horas" placeholder="Horas" class="input-width-3"></td>
            <td><input type="text" max="40" name="ocupacion" placeholder="Ocupación" class="input-width-3"></td>
            <td><textarea name="observation_affiliate" placeholder="Observaciones" rows="3" cols="100" style="width: 300px;"></textarea></td>
            <td>
                <button class="ui icon button secondary addRow">
                    <i class="plus icon"></i>
                </button>
                <button class="ui icon button primary removeRow">
                    <i class="minus icon"></i>
                </button>
            </td>
        </tr>
    `;

        // Agregar la nueva fila al cuerpo de la tabla
        $(newRow).appendTo(tableBody);

        // Lógica adicional para restringir la entrada de días a un máximo de 5 para las opciones ocasionales
        tableBody.on('change', 'select[name="tipo_jornada"]', function() {
            aplicarValidacionDias($(this));
        });
        const newDropdown = $('#dynamicTable tbody tr:last .ui.dropdown');
        newDropdown.dropdown({
            sortSelect: false,
            forceSelection: false
        });
        // Cargar los países en el nuevo menú desplegable
        cargarPaises(newDropdown.find('.paises'));
    });

    $(document).on('click', '.addRow_condition', function(e) {
        e.preventDefault();


        let tableBody = $('#dynamicTable tbody');
        const docTypes = @json($DOC_TYPES);
        let docTypeOptions = '';

        for (let key in docTypes) {
            if (docTypes.hasOwnProperty(key)) {
                docTypeOptions += `<option value="${key}">${docTypes[key]}</option>`;
            }
        }

        // Crear una nueva fila con opciones de tipo de jornada según las validaciones pertinentes
        let newRow = `
        <tr>
            <td>
                <select name="ti">
                    ${docTypeOptions}
                </select>
            </td>
            <td><input type="text" max="15" name="no_identificacion" placeholder="# Identificación" pattern="[A-Z0-9]{1,15}"></td>
            <td><input type="text" max="20" name="nombres" placeholder="nombres" class="input-width-1"  style="width: 100% !important"></td>
            <td><input type="text" max="20" name="apellidos" placeholder="Primer Apellido" class="input-width-2"  style="width: 100% !important"></td>
            <td><textarea name="observation_affiliate" placeholder="Observaciones" rows="3" cols="100" style="width: 300px;"></textarea></td>
            <td>
                <button class="ui icon button secondary addRow_condition">
                    <i class="plus icon"></i>
                </button>
                <button class="ui icon button primary removeRow">
                    <i class="minus icon"></i>
                </button>
            </td>
        </tr>
    `;

        // Agregar la nueva fila al cuerpo de la tabla
        $(newRow).appendTo(tableBody);

        // Lógica adicional para restringir la entrada de días a un máximo de 5 para las opciones ocasionales

        const newDropdown = $('#dynamicTable tbody tr:last .ui.dropdown');
        newDropdown.dropdown({
            sortSelect: false,
            forceSelection: false
        });
    });

    // Función para eliminar una fila al hacer clic en el botón "-"
    $(document).on('click', '.removeRow', function(e) {
        e.preventDefault();
        $(this).closest('tr').remove();
    });

    function table_row_Valid(context, work_modality_id) {


        let row = work_modality_id !== 2 && work_modality_id !== 3 ? {
            ti: context.find('select[name="ti"]').val(),
            nacionalidad: context.find('input[name="nacionalidad"]').val(),
            no_identificacion: context.find('input[name="no_identificacion"]').val(),
            nombres: context.find('input[name="nombres"]').val(),
            apellidos: context.find('input[name="apellidos"]').val(),
            fecha_nacimiento: context.find('input[name="fecha_nacimiento"]').val(),
            sexo: context.find('select[name="sexo"]').val(),
            correo_electronico: context.find('input[name="correo_electronico"]').val(),
            tipo_jornada: context.find('select[name="tipo_jornada"]').val(),
            salario_mensual: context.find('input[name="salario_mensual"]').val(),
            dias: context.find('input[name="dias"]').val(),
            horas: context.find('input[name="horas"]').val(),
            ocupacion: context.find('input[name="ocupacion"]').val(),
            observation_affiliate: context.find('textarea[name="observation_affiliate"]').val()
        } : {
            ti: context.find('select[name="ti"]').val(),
            no_identificacion: context.find('input[name="no_identificacion"]').val(),
            nombres: context.find('input[name="nombres"]').val(),
            apellidos: context.find('input[name="apellidos"]').val(),
            observation_affiliate: context.find('textarea[name="observation_affiliate"]').val()
        };

        return row;
    }

    // Metodo para guardar los datos en BD
    $('#uploadManuallyRecords').on('click', function(e) {
        e.preventDefault();

        let tableData = [];
        let message = '';
        let hasEmptyFields = false;
        var itemNumber = 1;

        const {
            work_modality_id
        } = @json(isset($policy_sort) ? $policy_sort : null);


        $('#dynamicTable tbody tr').each(function() {
            let row = table_row_Valid($(this), work_modality_id);

            tableData.push(row);

            // Quitar el borde rojo
            $(this).find('input, select').css('border', '');

            // Verificar los inputs y asignar el borde rojo
            for (let key in row) {
                if ((row[key] === '' || row[key] === null || row[key] === undefined) && key !== 'observation_affiliate') {
                    $(this).find(`[name="${key}"]`).css('border',
                        '1px solid #ef7272'); // Resaltar en rojo
                    hasEmptyFields = true;
                }
            }


            if (work_modality_id !== 2 && work_modality_id !== 3) {
                // Validar correo electrónico
                let email = row.correo_electronico;
                let emailPattern = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/;
                if (!emailPattern.test(email)) {
                    message += `Fila: ${itemNumber} - El correo electrónico ${email} no es válido.<br>`;
                    hasEmptyFields = true;
                }

                // Validar edad


                let birthDate = new Date(row.fecha_nacimiento);
                let currentDate = new Date();
                let age = currentDate.getFullYear() - birthDate.getFullYear();
                let monthDiff = currentDate.getMonth() - birthDate.getMonth();

                if (monthDiff < 0 || (monthDiff === 0 && currentDate.getDate() < birthDate.getDate())) {
                    age--;
                }
                if (age < 15) {
                    message +=
                        `Fila: ${itemNumber} - El usuario ${row.nombres} ${row.apellidos} es menor de 15 años.<br>`;
                    hasEmptyFields = true;
                }
            }


            itemNumber++;
        });

        // Validaciones para RT 6 Riesgos del Trabajo Independiente
        const currentUrlRT = window.location.href;
        const tienePlanillaManualFromPolizaRT = currentUrlRT.includes('informe_planilla');
        const policySortRT = @json($policy_sort ?? null);
        
        if (
            tienePlanillaManualFromPolizaRT && 
            work_modality_id == 6 && 
            policySortRT?.doc_type && 
            policySortRT?.doc_number && 
            tableData[0] &&
            (tableData[0]?.ti != policySortRT?.doc_type ||
            tableData[0]?.no_identificacion != policySortRT?.doc_number)
        ) {
            Swal.fire({
                icon: 'error',
                title: 'Error',
                html: "El tipo de documento o el número de identificación no coinciden con los datos de la póliza.<br> La modalidad Riesgos del Trabajo Independiente solo permite un trabajador asegurado y este debe ser el mismo patrono.",
                confirmButtonText: 'Aceptar',
                confirmButtonColor: '#000000',
                customClass: {
                    popup: 'custom-swal', // Clase personalizada
                },
            });

            return;
        }

        if (hasEmptyFields) {
            message += 'Los campos en color rojo requieren atención.';
            Swal.fire({
                icon: 'error',
                title: 'Error',
                html: message,
                confirmButtonText: 'Aceptar',
                confirmButtonColor: '#000000',
                customClass: {
                    popup: 'custom-swal', // Clase personalizada
                },
            });
        } else {
            $('.ui.dimmer').dimmer('show');

            var npoliza = '{{ $npoliza ?? '' }}';

            $.ajax({
                url: '/intermediario/formulario/store-affiliates-manually',
                method: 'POST',
                data: {
                    _token: $('meta[name="csrf-token"]').attr('content'),
                    activity_policy_id: @json($activity->id),
                    tableData: tableData,
                    taker: npoliza,
                },
                success: function(data) {
                    $('.ui.dimmer').dimmer('hide');
                    const message = responseSuccessAffiliates(data, 'MANUAL');

                    // ocultamos el mensaje de error en SweetAlert
                    const warningAlert = document.getElementById('warningAlert');
                    warningAlert.style.display = 'none';

                    // Muestra el mensaje de éxito


                    Swal.fire({
                        icon: 'success',
                        confirmButtonColor: '#000000',
                        title: 'Planilla cargada correctamente',
                        html: message,
                    }).then((result) => {
                        Swal.fire({
                            title: 'Certificado de Planilla en Proceso',
                            text: `El certificado de planilla está siendo generado, este estará disponible para su descarga el día de mañana. Gracias por su paciencia.`,
                            imageUrl: '/file/client_logo/logo_mnk.png',
                            imageHeight: 50,
                            imageWidth: 150,
                            confirmButtonText: 'Cerrar',
                            confirmButtonColor: '#000000',
                        });
                    });

                },
                error: function(xhr) {
                    $('.ui.dimmer').dimmer('hide');

                    const {
                        responseJSON
                    } = xhr;

                    if (responseJSON) {
                        // Extrae los mensajes
                        const generalMessage = responseJSON.message ||
                            'Ocurrió un error desconocido.';
                        const specificErrors = responseJSON.e || '';

                        // Reemplaza las comas por saltos de línea
                        const formattedErrors = specificErrors.replace(/, /g, '\n');

                        // Combina los mensajes
                        const fullMessage = `${generalMessage}\n${formattedErrors}`;

                        $('.ui.dimmer').dimmer('hide');
                        // Muestra el mensaje de error en SweetAlert
                        const warningAlert = document.getElementById('warningAlert');

                        setTimeout(function() {
                            window.scrollTo({
                                top: document.body.scrollHeight,
                                behavior: 'smooth'
                            });
                        }, 100);

                        warningAlert.innerText = fullMessage;
                        warningAlert.style.display = 'none';
                        warningAlert.style.display = 'block';

                        Swal.fire({
                            icon: 'warning',
                            title: 'Importante',
                            text: 'Errores encontrados',
                            confirmButtonColor: '#000000',
                        });

                    } else {
                        $('.ui.dimmer').dimmer('hide');
                        // Si no hay respuesta JSON, muestra un mensaje de error genérico
                        Swal.fire({
                            icon: 'error',
                            title: 'Error',
                            text: 'Ocurrió un error al procesar la solicitud.',
                            confirmButtonColor: '#000000',
                        });
                    }
                }
            });
        }
    });


    $('#type_charge').change(function() {
        let value = $(this).val();

        if (value === "C") {
            $("#policyholderManually").hide();
            $("#txtFile").hide();
            $("#policyholderMassive").show();

            $('.ui.accordion').accordion('open', 2);
            $('.ui.accordion').accordion('close', 1);
            $('.ui.accordion').accordion('close', 3);
        } else if(value === "M") {
            $("#policyholderMassive").hide();
            $("#txtFile").hide();
            $("#policyholderManually").show();

            $('.ui.accordion').accordion('open', 1);
            $('.ui.accordion').accordion('close', 2);
            $('.ui.accordion').accordion('close', 3);
        }else{
            $("#policyholderMassive").hide();
            $("#policyholderManually").hide();
            $("#txtFile").show();

            $('.ui.accordion').accordion('open', 3);
            $('.ui.accordion').accordion('close', 1);
            $('.ui.accordion').accordion('close', 2);
        }

    });

    function getPayrollMessage(data, typeExec) {
        if(typeExec === 'AUTO' || data.countPolicySpreadSheet == null) {
            return '';
        }
        const isTaker = '{{$npoliza ?? ''}}';
        console.log('isTaker***', isTaker !== '');
        const now = new Date();
        const countPolicySpreadSheet = data.countPolicySpreadSheet;
        const lastMonthDate = new Date(now.getFullYear(), now.getMonth() - 1, 1);
        const monthName = lastMonthDate.toLocaleString('default', { month: 'long' });
        const year = lastMonthDate.getFullYear();

        // Capitalizar primera letra del mes
        const capitalizedMonth = monthName.charAt(0).toUpperCase() + monthName.slice(1);

        // Determinar el período
        let period = `${capitalizedMonth} del ${year}`;
        if (isTaker === '' || countPolicySpreadSheet === 0) {
            period = 'de emisión';
        }

        // Mensaje final
        return `La planilla mensual de su póliza, correspondiente al período ${period}, ha sido cargada con éxito!`;
    }


    /**
     Evitar que se refresque la pagina, limpio todo
     O los limpio con JS o refresco toda la pagina ....
     **/
    function cleanTable() {

        // Tomo el tbody
        let tableBody = $('#dynamicTable tbody');

        // Eliminar todas las filas excepto la primera (si es que es una plantilla base)
        tableBody.find('tr').not(':first').remove();

        // Limpiar los valores de los campos en la primera fila
        tableBody.find('tr:first').find('input, select').each(function() {
            $(this).val(''); // Limpiar el valor de cada campo
        });

        // Resetear los dropdowns
        tableBody.find('.ui.dropdown').dropdown('clear');
    }

    function loadedEmployes(id) {
        // Mostrar la barra de progreso
        $('#progressContainer').show();

        // Inicializar la barra de progreso
        $('#progressBar').progress({
            percent: 0,
            text: {
                active: 'Cargando empleados...',
                success: 'Completado'
            }
        });

        // Variable para verificar si el proceso está completo
        let isProcessComplete = false;

        // Función para obtener el progreso
        function getProgress() {
            if (isProcessComplete) return; // Si el proceso ya está completo, no continuar

            $.ajax({
                url: '/massively/report/automatic/' + id,
                method: 'GET',
                success: function(data) {
                    // Si el porcentaje es menor al 100%, actualizar la barra de progreso
                    if (data.percentaje < 100) {
                        $('#progressBar').progress('set percent', data.percentaje);
                        $('#typeChargeDropdown').addClass('disabled');
                        $('#uploadManuallyRecords').addClass('disabled');
                        $('#uploadMassiveRecords').addClass('disabled');
                    }

                    // Si el porcentaje llega al 100%, ocultamos la barra y mostramos el mensaje de éxito
                    if (data.percentaje === 100) {
                        isProcessComplete = true; // Marcamos que el proceso está completo
                        $('#progressContainer').hide();
                        $('#typeChargeDropdown').removeClass('disabled');
                        $('#uploadManuallyRecords').removeClass('disabled');
                        $('#uploadMassiveRecords').removeClass('disabled');
                        clearInterval(progressInterval); // Detener el intervalo


                        Swal.fire({
                            title: 'Éxito',
                            text: 'Los empleados han sido cargados correctamente.',
                            icon: 'success',
                            confirmButtonColor: '#000000',
                            confirmButtonText: 'Aceptar'
                        }).then((result) => {
                            Swal.fire({
                                title: 'Certificado de Planilla en Proceso',
                                text: `El certificado de planilla está siendo generado, este estará disponible para su descarga el día de mañana. Gracias por su paciencia.`,
                                imageUrl: '/file/client_logo/logo_mnk.png',
                                imageHeight: 50,
                                imageWidth: 150,
                                confirmButtonColor: '#000000',
                                confirmButtonText: 'Cerrar'
                            });
                        });


                    }
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    // Manejo de errores de la solicitud
                    clearInterval(progressInterval); // Detener el intervalo en caso de error
                    $('#progressContainer').hide();
                    $('#typeChargeDropdown').removeClass('disabled');
                    $('#uploadManuallyRecords').removeClass('disabled');
                    $('#uploadMassiveRecords').removeClass('disabled');
                    Swal.fire({
                        title: 'Error',
                        text: 'No se pudo completar la solicitud. Por favor, inténtelo de nuevo.',
                        icon: 'error',
                        confirmButtonText: 'Cerrar'
                    });
                }
            });
        }

        // Ejecutar la primera vez para obtener el progreso
        getProgress();

        // Configurar el intervalo para ejecutar cada 2 segundos
        const progressInterval = setInterval(getProgress, 6000);
    }

    // Cargar Masivamente
    $(document).on('click', '#uploadMassiveRecords', function(e) {
        e.preventDefault();

        var npoliza = '{{ $npoliza ?? '' }}';

        // Capturar el policy_sort_id desde la URL
        const activity_policy_id = @json($activity->id);

        const fileInput = $('input[name="affiliate_file"]')[0];
        const file = fileInput.files[0];
        const observacion = $('#observaciones_sheet').val();


        if (!file) {
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: 'Por favor, selecciona un archivo para cargar.',
            });
            return;
        }

        const formData = new FormData();
        formData.append('affiliate_file', file);
        formData.append('activity_policy_id', activity_policy_id);
        formData.append('taker', npoliza);
        formData.append('observacion', observacion);

        $('.ui.dimmer').dimmer('show');

        $.ajax({
            url: '/intermediario/formulario/poliza/' + activity_policy_id +
                '/store-affiliates-massively',
            method: 'POST',
            data: formData,
            contentType: false,
            processData: false,
            success: function(data) {


                $('.ui.dimmer').dimmer('hide');

                if (data.status == false) {
                    Swal.fire({
                        icon: 'warning',
                        title: 'Importante',
                        text: data.message,
                        confirmButtonColor: '#000000'
                    })

                    return;
                }

                const message = responseSuccessAffiliates(data, 'MANUAL');

                // ocultamos el mensaje de error en SweetAlert
                const warningAlert = document.getElementById('warningAlert');
                warningAlert.style.display = 'none';

                Swal.fire({
                    icon: 'success',
                    confirmButtonColor: '#000000',
                    title: 'Planilla cargada correctamente',
                    html: message
                })


                const id = data.id;
                // Verifica si hay un archivo en la respuesta
                if (data.policy_spreadsheet && data.policy_spreadsheet.file) {
                    const fileUrl = `/file/${data.policy_spreadsheet.file}`;
                    // Generar el botón dinámicamente
                    const buttonHtml = `
                        <button type="button" onclick="window.open('${fileUrl}', '_blank')" class="ui button secondary">
                            <i class="download icon"></i> Descargar Excel de planilla recién cargada
                        </button>
                    `;
                    // Insertar el botón en el contenedor
                    document.getElementById('policySpreadsheetButtonContainer').innerHTML = buttonHtml;
                }



                loadedEmployes(id)


            },
            error: function(xhr) {
                $('.ui.dimmer').dimmer('hide');

                const {
                    responseJSON
                } = xhr;

                if (responseJSON) {
                    // Extrae los mensajes
                    const generalMessage = responseJSON.message || 'Ocurrió un error desconocido.';
                    const specificErrors = responseJSON.e || '';

                    // Reemplaza las comas por saltos de línea
                    const formattedErrors = specificErrors.replace(/, /g, '\n');

                    // Combina los mensajes
                    const fullMessage = `${generalMessage}\n${formattedErrors}`;

                    $('.ui.dimmer').dimmer('hide');
                    // Muestra el mensaje de error en SweetAlert
                    const warningAlert = document.getElementById('warningAlert');

                    setTimeout(function() {
                        window.scrollTo({
                            top: document.body.scrollHeight,
                            behavior: 'smooth'
                        });
                    }, 100);

                    warningAlert.innerText = fullMessage;
                    warningAlert.style.display = 'block';

                    Swal.fire({
                        icon: 'warning',
                        title: 'Importante',
                        text: 'Errores encontrados',
                        confirmButtonColor: '#000000'
                    });

                } else {
                    $('.ui.dimmer').dimmer('hide');
                    // Si no hay respuesta JSON, muestra un mensaje de error genérico
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: 'Ocurrió un error al procesar la solicitud.',
                    });
                }
            }
        });
    });
    var policy_spreadsheet_id = @json($policy_spreadsheet ? $policy_spreadsheet->id : null);
    //Cargar masivamente los empleados de la planilla por txt
    $(document).on('click', '#uploadTxtFile', function(e) {
        e.preventDefault();
        //caputramos los parametros para enviar al backend
        var taker = '{{ $npoliza ?? '' }}';
        const activity_policy_id = @json($activity->id);
        const fileInput = $('input[name="txt_file"]')[0];
        const file = fileInput.files[0];
        if (!file) {
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: 'Por favor, selecciona un archivo para cargar.',
            });
            return;
        }

        const formData = new FormData();
        formData.append('txt_file', file);
        formData.append('taker', taker);
        $('.ui.dimmer').dimmer('show');

        const currency = @json($policy_sort->type_currency ?? 'CRC');
        const currencyText = currency === 'USD' ? 'dólares' : 'colones';
        // Mostrar mensaje de aviso
        Swal.fire({
            title: '¡Atención!',
            text: `El contrato de esta planilla está en [Moneda:${currencyText}]. Por favor, verifique la información antes de continuar.`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Aceptar',
            cancelButtonText: 'Cancelar',
            confirmButtonColor: '#000000',
            cancelButtonColor: '#000000',
        }).then((result) => {
            if (result.isConfirmed) {
                //Solicitud ajax al backend
                $.ajax({
                    url: '/spreadsheet/'+ activity_policy_id +'/storage-file-txt',
                    method: 'POST',
                    data: formData,
                    contentType: false,
                    processData: false,
                    success: function(data) {
                        $('.ui.dimmer').dimmer('hide');
                        // ocultamos el mensaje de error en SweetAlert
                        const warningAlert = document.getElementById('warningAlert');
                        warningAlert.style.display = 'none';
                        if (data.status == false) {
                            Swal.fire({
                                icon: 'warning',
                                title: 'Importante',
                                text: data.message,
                                confirmButtonColor: '#000000'
                            })
                            return;
                        }
                        // Actualizamos la variable policy_spreadsheet
                        policy_spreadsheet_id = data.policy_spreadsheet.id;
                        $('#generateExcel').show();
                        const message = responseSuccessAffiliates(data, 'AUTO');
                        Swal.fire({
                            title: 'Éxito',
                            text: 'Los empleados han sido cargados correctamente.',
                            icon: 'success',
                            confirmButtonColor: '#000000',
                            confirmButtonText: 'Aceptar'
                        }).then((result) => {
                            Swal.fire({
                                title: 'Certificado de Planilla en Proceso',
                                text: `El certificado de planilla está siendo generado, este estará disponible para su descarga el día de mañana. Gracias por su paciencia.`,
                                imageUrl: '/file/client_logo/logo_mnk.png',
                                imageHeight: 50,
                                imageWidth: 150,
                                confirmButtonColor: '#000000',
                                confirmButtonText: 'Cerrar'
                            });
                        });
                    },
                    error: function(xhr) {
                        $('.ui.dimmer').dimmer('hide');

                        const {
                            responseJSON
                        } = xhr;

                        if (responseJSON) {
                            // Extrae los mensajes
                            const generalMessage = responseJSON.message || 'Ocurrió un error desconocido.';
                            const specificErrors = responseJSON.e || '';

                            // Reemplaza las comas por saltos de línea
                            const formattedErrors = specificErrors.replace(/, /g, '\n');

                            // Combina los mensajes
                            const fullMessage = `${generalMessage}\n${formattedErrors}`;

                            $('.ui.dimmer').dimmer('hide');
                            // Muestra el mensaje de error en SweetAlert
                            const warningAlert = document.getElementById('warningAlert');

                            setTimeout(function() {
                                window.scrollTo({
                                    top: document.body.scrollHeight,
                                    behavior: 'smooth'
                                });
                            }, 100);

                            warningAlert.innerText = fullMessage;
                            warningAlert.style.display = 'block';

                            Swal.fire({
                                icon: 'warning',
                                title: 'Importante',
                                text: 'Errores encontrados',
                                confirmButtonColor: '#000000'
                            });

                        } else {
                            $('.ui.dimmer').dimmer('hide');
                            // Si no hay respuesta JSON, muestra un mensaje de error genérico
                            Swal.fire({
                                icon: 'error',
                                title: 'Error',
                                text: 'Ocurrió un error al procesar la solicitud.',
                            });
                        }
                    }
                });
            }else {
                // Si el usuario cancela, detener el loader
                loadingMain(false);
                $('.ui.dimmer').dimmer('hide');
            }
        });
    });
    //funcion para crear el excel con el archivo txt
     function download_excel_txt() {
        Swal.fire({
            title: 'El documento se está descargando',
            text: 'Por favor, espere mientras termina el proceso.',
            allowOutsideClick: false,
            showConfirmButton: false,
            willOpen: () => {
                Swal.showLoading();
            }
        });

        $.ajax({
            url: '/spreadsheet/' + policy_spreadsheet_id + '/generar-excel',
            type: 'GET',
            xhrFields: {
                responseType: 'blob'
            },
            success: function (response, status, xhr) {
                Swal.close();

                // Extraer filename
                let filename = 'planilla.xlsx';
                const cd = xhr.getResponseHeader('Content-Disposition');
                if (cd && cd.includes('attachment')) {
                    const m = cd.match(/filename="?(.+?)"?(;|$)/);
                    if (m) filename = m[1];
                }

                // Aquí usamos `response` en lugar de `blob`
                const url = URL.createObjectURL(response);
                const a = document.createElement('a');
                a.href = url;
                a.download = filename;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);

                Swal.fire({
                    icon: 'success',
                    title: '¡Descarga exitosa!',
                    text: 'El documento se ha descargado correctamente.',
                    confirmButtonText: 'Aceptar',
                    confirmButtonColor: '#000000'
                });

                loadingMain(false);
            },
            error: function (xhr) {
                loadingMain(false);
                if (xhr.status === 400) {
                    Swal.fire({
                        icon: 'warning',
                        title: 'Espera un momento',
                        text: 'El proceso de carga de afiliados aún no ha terminado. Por favor, inténtelo más tarde.',
                        confirmButtonText: 'Cerrar',
                        confirmButtonColor: '#000000'
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: 'Error inesperado al generar el archivo.',
                        confirmButtonText: 'Cerrar'
                    });
                }
            }
        });


    }

function startDownloadPlanilla() {
  const btn = $('#downloadExcelButton');
  const id  = policy_spreadsheet_id;

  // 1) Deshabilita botón y pon spinner
  btn.prop('disabled', true).addClass('loading');

  // Flags para un solo aviso / una sola descarga
  let shownWaitingMessage = false;
  let downloadTriggered    = false;

  // 2) Función de polling
  function checkStatus() {
    $.getJSON(`/spreadsheet/${id}/status`)
      .done(res => {
        if (res.status === 'COMPLETED') {
          // 3) Sólo la primera vez
          if (!downloadTriggered) {
            downloadTriggered = true;
            btn.removeClass('loading').prop('disabled', false);
            download_excel_txt();
          }
        } else {
          // Aviso de “espera” sólo una vez
          if (!shownWaitingMessage) {
            shownWaitingMessage = true;
            Swal.fire({
              icon: 'warning',
              title: 'Espera un momento',
              text: 'El proceso de carga de afiliados aún no ha finalizado. Espere unos momentos; el documento se descargará automáticamente en breve.',
              confirmButtonText: 'Cerrar',
              confirmButtonColor: '#000000'
            });
          }
          // 4) Programar siguiente check en 5s
          setTimeout(checkStatus, 5000);
        }
      })
      .fail(() => {
        btn.removeClass('loading').prop('disabled', false);
        Swal.fire('Error', 'No se pudo comprobar el estado.', 'error');
      });
  }

  // 5) Lanza la primera comprobación
  checkStatus();
}



    /**
     * Función para procesar la respuesta success de afiliados
     * @param data
     * @returns {string}
     */
    function responseSuccessAffiliates(data, requestType ) {

        let message = getPayrollMessage(data, requestType);

        // Esconde el dimmer y limpia el campo de archivo
        $('.ui.dimmer').dimmer('hide');
        $('input[name="affiliate_file"]').val('');

        // Verificar si response es un objeto válido
        if (data && typeof data === 'object') {

            if (data.total.registros_guardados > 0) {


                // Verificar si se guardo totales
                $('#total-workers').val(data.total.registros_guardados);
                $('#total-salaries').val(data.total.salario_total);


                // Verificar si hay alertas
                if (data.alerts && Array.isArray(data.alerts) && data.alerts.length > 0) {
                    message += '<strong>Sin procesar:</strong><ul>';
                    data.alerts.forEach(function(alert) {
                        message += `<li>${alert}</li>`;
                    });
                    message += '</ul>';
                }

                // Verificar si hay errores
                if (data.errors && Array.isArray(data.errors) && data.errors.length > 0) {
                    message += '<strong>Errores:</strong><ul>';
                    data.errors.forEach(function(error) {
                        message += `<li>${error}</li>`;
                    });
                    message += '</ul>';
                }
            } else {

                // Verificar si hay errores
                if (data.errors && Array.isArray(data.errors) && data.errors.length > 0) {

                    message += '<strong>Sin procesar:</strong><ul>';
                    data.alerts.forEach(function(alert) {
                        message += `<li>${alert}</li>`;
                    });
                    message += '</ul>';
                }
            }
        } else {
            message = 'No se pudo procesar la respuesta del servidor.';
        }

        return message;
    }

    function responseErrorAffiliates(data, type) {

        $('input[name="affiliate_file"]').val('');
        $('.ui.dimmer').dimmer('hide');

        let message = '';
        if (data && data.alerts && data.alerts.length > 0) {
            if (data.alerts.length > 0) {
                message += '<strong>Alertas:</strong><ul>';
                data.alerts.forEach(function(alert) {
                    message += `<li>${alert}</li>`;
                });
                message += '</ul>';
            }
        }

        if (data && data.errors && data.errors.length > 0) {
            if (data.errors.length > 0) {
                data.errors.forEach(function(error) {
                    message += `<li>${error}</li>`;
                });
                message += '</ul>';
            }
        }

        return message;
    }

    //Funcion para decimales en forma manual
    function formatDecimalInput(input) {
        let value = input.value;
        // Permitir solo números y un punto decimal
        value = value.replace(/[^0-9.]/g, '');
        // No permitir más de un punto decimal
        if ((value.match(/\./g) || []).length > 1) {
            value = value.slice(0, -1);
        }
        // Limitar a dos decimales
        if (value.includes('.')) {
            let [integer, decimal] = value.split('.');
            decimal = decimal.substring(0, 2);
            value = integer + '.' + decimal;
        }

        input.value = value;
    }

    function aplicarValidacionDias(selectElement) {
        const selectedValue = selectElement.val();
        const diasInput = selectElement.closest('tr').find('input[name="dias"]');
        const horasInput = selectElement.closest('tr').find('input[name="horas"]');

        if (selectedValue === 'OD' || selectedValue === 'OH') {
            if (diasInput.val() > 5) {
                diasInput.val('5');
            }
            diasInput.on('input', function() {
                formatDecimalInput(this);
                if (parseFloat(this.value) > 5) {
                    this.value = '5';
                }
            });
            if (horasInput.val() > 120) {
                horasInput.val('120');
            }
            horasInput.on('input', function() {
                formatDecimalInput(this);
                if (parseFloat(this.value) > 120) {
                    this.value = '120';
                }
            });
        } else {
            diasInput.off('input');
            horasInput.off('input');
        }
    }

    // Validaciones, cuando cargue la pagina
    $(document).ready(function() {

        const npoliza = @json($npoliza ?? '');

        if (npoliza) {
            $('#next').hide();
            $('#step').hide();
        }


        // Validación para Número de Identificación (solo mayúsculas, sin caracteres especiales, y limitado a 15 caracteres)
        $(document).on('input', 'input[name="no_identificacion"]', function() {
            let uppercaseValue = this.value.toUpperCase();
            let alphanumericValue = uppercaseValue.replace(/[^A-Z0-9]/g, '');
            let truncatedValue = alphanumericValue.slice(0, 15);
            this.value = truncatedValue;
        });

        // Validación para Salario Mensual (solo números, no negativos)
        $(document).on('input', 'input[name="salario_mensual"]', function() {
            formatDecimalInput(this)
            if (this.value < 0) {
                this.value = ''; // No permitir valores negativos
            }
        });

        // Validación para Días (solo números, máximo 31)
        $(document).on('input', 'input[name="dias"]', function(e) {
            formatDecimalInput(this)
            if (parseFloat(this.value) > 31) {
                this.value = '31';
            }
        });


        // Validación para Horas (solo números, máximo 400, no negativos)
        $(document).on('input', 'input[name="horas"]', function() {
            formatDecimalInput(this)
            if (this.value > 400) {
                this.value = '400';
            }
        });

        // Validación para Ocupación
        $(document).on('input', 'input[name="ocupacion"]', function() {
            this.value = this.value.replace(/[^a-zA-ZÀ-ÿÑñ0-9\s,().:;]/g, '');
        });


        /**
         * Verifico si existen datos temporalmente guardados
         */
        let tableData = sessionStorage.getItem('tmpTableData');
        if (tableData) {

            $('#typeChargeDropdown').dropdown('set selected', 'M');
            $('#type_charge').val('M');
            $("#policyholderMassive").hide();
            $("#policyholderManually").show();

            $('.ui.accordion').accordion('open', 0);
            $('.ui.accordion').accordion('close', 2);
            // loadTableDateTemporary(tableData);
        }



    });

    /**
     * Función para guardar los datos de la tabla en sessionStorage para evitar que se pierdan los datos antes de
     * guardar y al hacer clic en antras o siguiente y poder continuar cargando los datos
     */
    function saveTableDataTemporary() {
        let tableData = [];

        $('#dynamicTable tbody tr').each(function() {
            let row = {
                ti: $(this).find('select[name="ti"]').val(),
                nacionalidad: $(this).find('input[name="nacionalidad"]').val(),
                no_identificacion: $(this).find('input[name="no_identificacion"]').val(),
                nombres: $(this).find('input[name="nombres"]').val(),
                apellidos: $(this).find('input[name="apellidos"]').val(),
                fecha_nacimiento: $(this).find('input[name="fecha_nacimiento"]').val(),
                sexo: $(this).find('select[name="sexo"]').val(),
                correo_electronico: $(this).find('input[name="correo_electronico"]').val(),
                tipo_jornada: $(this).find('select[name="tipo_jornada"]').val(),
                salario_mensual: $(this).find('input[name="salario_mensual"]').val(),
                dias: $(this).find('input[name="dias"]').val(),
                horas: $(this).find('input[name="horas"]').val(),
                ocupacion: $(this).find('input[name="ocupacion"]').val()
            };
            tableData.push(row);
        });

        // Guardar los datos en sessionStorage como JSON
        sessionStorage.setItem('tmpTableData', JSON.stringify(tableData));

    }

    /**
     * Cargo los datos guardados temporalmente en caso existan
    
    function loadTableDateTemporary(tableData) {

        // Si hay datos guardados en sessionStorage
        if (tableData) {
            tableData = JSON.parse(tableData); // Convertir los datos a un array de objetos


            let tableBody = $('#dynamicTable tbody');
            tableBody.empty(); // Limpiar las filas existentes

            // Recorrer los datos y agregar cada fila a la tabla
            tableData.forEach(function(row, index) {
                let isLastRow = index === tableData.length - 1;
                let newRow = `
                <tr>
                    <td>
                        <select name="ti" style="width: auto">
                            <option value="CD" ${row.ti === 'CD' ? 'selected' : ''}>Carnet Diplomatico</option>
                            <option value="DI" ${row.ti === 'DI' ? 'selected' : ''}>DIMEX</option>
                            <option value="CJ" ${row.ti === 'CJ' ? 'selected' : ''}>Cédula Jurídica</option>
                            <option value="CF" ${row.ti === 'CF' ? 'selected' : ''}>Cédula Física</option>
                            <option value="PS" ${row.ti === 'PS' ? 'selected' : ''}>Pasaporte</option>
                            <option value="CR" ${row.ti === 'CR' ? 'selected' : ''}>Cédula de Residencia</option>
                        </select>
                    </td>

                     <td>
                        <div class="ui fluid search selection dropdown" style="width: 150px; position: relative;">
                                <input type="hidden" name="nacionalidad" value="${row.nacionalidad}">
                                <i class="dropdown icon"></i>
                                <div class="text">
            ${row.nacionalidad ? `<i class="${row.nacionalidad.toLowerCase()} flag"></i> ${row.nacionalidad}` : 'Nacionalidad'}
        </div>

                                <div class="menu paises">
                                </div>
                        </div>
                    </td>
                    <td><input type="text" name="no_identificacion" value="${row.no_identificacion}"></td>
                    <td><input type="text" name="nombres" value="${row.nombres}"></td>
                    <td><input type="text" name="apellidos" value="${row.apellidos}"></td>
                    <td><input type="date" name="fecha_nacimiento" value="${row.fecha_nacimiento}"></td>
                    <td>
                        <select name="sexo" style="width: auto">
                            <option value="M" ${row.sexo === 'M' ? 'selected' : ''}>Masculino</option>
                            <option value="F" ${row.sexo === 'F' ? 'selected' : ''}>Femenino</option>
                        </select>
                    </td>
                    <td><input type="text" name="correo_electronico" value="${row.correo_electronico}"></td>
                    <td>
                        <select name="tipo_jornada" style="width: auto">
                            <option value="TC" ${row.tipo_jornada === 'TC' ? 'selected' : ''}>Tiempo completo</option>
                            <option value="TM" ${row.tipo_jornada === 'TM' ? 'selected' : ''}>Tiempo medio</option>
                            <option value="OD" ${row.tipo_jornada === 'OD' ? 'selected' : ''}>Ocasional contratado por días</option>
                            <option value="OH" ${row.tipo_jornada === 'OH' ? 'selected' : ''}>Ocasional contratado por horas</option>
                        </select>
                    </td>
                    <td><input type="number" name="salario_mensual" value="${row.salario_mensual}"></td>
                    <td><input type="number" name="dias" value="${row.dias}"></td>
                    <td><input type="number" max="400" name="horas" value="${row.horas}"></td>
                    <td><input type="text" name="ocupacion" value="${row.ocupacion}"></td>
                    <td>
                        <button class="ui icon button ${isLastRow ? 'green addRow' : 'red removeRow'}">
                            <i class="${isLastRow ? 'plus' : 'minus'} icon"></i>
                        </button>
                    </td>
                </tr>
            `;
                tableBody.append(newRow);
            });

            // Elimino datos temporales
            sessionStorage.removeItem('tmpTableData');

            const newDropdown = $('#dynamicTable tbody tr:last .ui.dropdown');
            newDropdown.dropdown({
                sortSelect: false,
                forceSelection: false
            });


            // Cargar los países en el nuevo menú desplegable
            cargarPaises(newDropdown.find('.paises'));
            // Inicializar los dropdowns de Semantic UI para las filas recién agregadas
            $('#dynamicTable .ui.dropdown').dropdown();
            $('.ui.dropdown').dropdown();
        }
    }
 */

    function download_planilla() {
        Swal.fire({
            title: 'El documento se está descargando',
            text: 'Por favor, espere mientras termina el proceso.',
            allowOutsideClick: false,
            showConfirmButton: false,
            willOpen: () => {
                Swal.showLoading(); // Iniciar loader
            }
        });


        $.ajax({
            url: '/servicio/policy_sort/descargar_planilla_SORT',
            type: 'GET',
            headers: {
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            },
            contentType: false, // Importante para enviar como FormData
            processData: false, // Importante para no procesar los datos como cadena de consulta
            xhrFields: {
                responseType: 'blob' // Manejar el PDF como un blob
            },
            success: function(response) {
                // Ocultar el loader de Swal cuando la solicitud sea exitosa
                Swal.close();

                const blob = new Blob([response], {
                    type: 'application/xlsx'
                });
                const link = document.createElement('a');
                link.href = window.URL.createObjectURL(blob);
                link.download = 'PLANTILLA_SORT.xlsx'; // nombres del archivo que se descargará
                link.click();

                Swal.fire({
                    icon: 'success',
                    title: '¡Descarga exitosa!',
                    text: 'El documento se ha descargado correctamente.',
                    confirmButtonText: 'Aceptar',
                    confirmButtonColor: '#000000'
                });

                loadingMain(false);
            },
            error: function(error) {
                // Ocultar el loader y mostrar el error
                Swal.close();

                Swal.fire({
                    icon: 'error',
                    title: 'Error en la solicitud',
                    text: 'Ocurrió un problema al procesar la solicitud. Inténtalo nuevamente.',
                    confirmButtonText: 'Cerrar'
                });
                loadingMain(false);

                console.error(error);
            }
        });

    }

    function download_explanation() {
        Swal.fire({
            title: 'El documento se está descargando',
            text: 'Por favor, espere mientras termina el proceso.',
            allowOutsideClick: false,
            showConfirmButton: false,
            willOpen: () => {
                Swal.showLoading(); // Iniciar loader
            }
        });


        $.ajax({
            url: '/servicio/policy_sort/descargar_Explicacion_planilla',
            type: 'GET',
            headers: {
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            },
            contentType: false, // Importante para enviar como FormData
            processData: false, // Importante para no procesar los datos como cadena de consulta
            xhrFields: {
                responseType: 'blob' // Manejar el PDF como un blob
            },
            success: function(response) {
                // Ocultar el loader de Swal cuando la solicitud sea exitosa
                Swal.close();

                const blob = new Blob([response], {
                    type: 'application/xlsx'
                });
                const link = document.createElement('a');
                link.href = window.URL.createObjectURL(blob);
                link.download = 'Explicación_Planilla_MNK.xlsx'; // nombres del archivo que se descargará
                link.click();

                Swal.fire({
                    icon: 'success',
                    title: '¡Descarga exitosa!',
                    text: 'El documento se ha descargado correctamente.',
                    confirmButtonText: 'Aceptar',
                    confirmButtonColor: '#000000'
                });

                loadingMain(false);
            },
            error: function(error) {
                // Ocultar el loader y mostrar el error
                Swal.close();

                Swal.fire({
                    icon: 'error',
                    title: 'Error en la solicitud',
                    text: 'Ocurrió un problema al procesar la solicitud. Inténtalo nuevamente.',
                    confirmButtonText: 'Cerrar'
                });

                loadingMain(false);

                console.error(error);
            }
        });

    }

    $(document).ready(function() {
        $('.ui.radio.checkbox').checkbox();
    });
</script>

<script>
    let paisesData = [];

    // Cargar los países al iniciar
    $.getJSON("/js/paises.json", function(paises) {
        paisesData = paises;
        cargarPaises($('.paises'));
    });

    // Función para cargar los países en el menú del dropdown
    function cargarPaises(menu) {
        menu.empty(); // Limpiar el contenido anterior
        $.each(paisesData, function(index, pais) {
            menu.append(
                `<div class="item" data-value="${pais.country_short_name}"><i class="${pais.country_short_name.toLowerCase()} flag"></i> ${pais.country_name} </div>`
            );
        });
    }

    // Validacion de fecha de nacimiento menor o igual y minimo 16 años
    $(document).on('change', 'input[name="fecha_nacimiento"]', function() {

        let fechaNacimiento = new Date($(this).val());
        let today = new Date();
        let miniumAge = 15;

        // Calcular la edad
        let age = today.getFullYear() - fechaNacimiento.getFullYear();
        let mes = today.getMonth() - fechaNacimiento.getMonth();

        if (mes < 0 || (mes === 0 && today.getDate() < fechaNacimiento.getDate())) {
            age--;
        }

        // Si es una fecha futura o si es menor de 16 años
        if ((fechaNacimiento > today) || (age < miniumAge)) {

            Swal.fire({
                icon: 'error',
                title: 'Fecha de nacimiento',
                text: 'El trabajador debe tener al menos 15 años.',
                confirmButtonColor: '#000000',
                confirmButtonText: 'Cerrar'
            });

            $(this).val('');
        }
    });

    //validacion al recargar la pagina para mostrar
    $(document).ready(function() {
        let value = $('#type_charge').val();
        if (value === "C") {
            $("#policyholderManually").hide();
            $("#policyholderMassive").show();

            $('.ui.accordion').accordion('open', 2);
            $('.ui.accordion').accordion('close', 1);
        } else if (value === "M") {
            $("#policyholderMassive").hide();
            $("#policyholderManually").show();

            $('.ui.accordion').accordion('open', 1);
            $('.ui.accordion').accordion('close', 2);
        }else if (value === 'TXT'){
            $("#policyholderMassive").hide();
            $("#policyholderManually").hide();
            $("#txtFile").show();

            $('.ui.accordion').accordion('open', 3);
            $('.ui.accordion').accordion('close', 1);
            $('.ui.accordion').accordion('close', 2);
        }
    });

    //validacion para el input original
    $(document).ready(function() {
        // Aplica la validación a la fila original al cargar la página
        $('select[name="tipo_jornada"]').each(function() {
            aplicarValidacionDias($(this));
        });

        // Detecta cambios en los selects dentro de la tabla
        $(document).on('change', 'select[name="tipo_jornada"]', function() {
            aplicarValidacionDias($(this));
        });

    });

</script>

