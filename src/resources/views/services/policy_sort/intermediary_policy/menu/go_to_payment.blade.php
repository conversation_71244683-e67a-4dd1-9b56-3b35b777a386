@extends('layouts.main')

@section('title', 'Vista del Intermediario - Poliza')

@section('menu')
    @parent
@endsection
@section('content')
    @include('services/policy_sort/intermediary_policy/menu/menu', ['active' => 'go_to_payment'])
    <form class="ui attached form container" id="form_prima_emision_policy"
        action="{{ secure_url('/intermediario/poliza/' . $id . '/calculo_prima') }}" method="post"
        style="padding-bottom: 40px;">
        {{ csrf_field() }}

        <div class="ui basic container segment" id="policyholder">
            <div class="ui styled fluid accordion">
                @if ($data->temporality === 'permanent')
                    <div id="permanent-section">
                        <div class="title active">
                            <i class="dropdown icon"></i>
                            Cálculo de la prima póliza permanente
                        </div>
                        <div class="content active">
                            <div class="four fields">
                                <div class="required field">
                                    <label for="typeIdentify">Anual </label>
                                    <div class="ui input">
                                        <input name="annual_calculation_amount" id="annual_calculation_amount"
                                            autocomplete="off" type="text" class="readonly"
                                            value="{{ ($data->type_currency === 'USD' ? '$' : '₡') . ' ' . $anual }}"
                                            readonly>
                                    </div>
                                </div>

                                @if ($data->semiannual_calculation_amount != 0)
                                    <div class="required field">
                                        <label for="numberIdentify">Semestral </label>
                                        <div class="ui input">
                                            <input name="semiannual_calculation_amount" id="semiannual_calculation_amount"
                                                autocomplete="off" type="text" class="readonly"
                                                value="{{ ($data->type_currency === 'USD' ? '$' : '₡') . ' ' . $semestral }}"
                                                readonly>
                                        </div>
                                    </div>
                                @endif
                                @if ($data->quarterly_calculation_amount != 0)
                                    <div class="required field">
                                        <label for="numberIdentify">Trimestral </label>
                                        <div class="ui input">
                                            <input name="quarterly_calculation_amount" id="quarterly_calculation_amount"
                                                autocomplete="off" type="text" class="readonly"
                                                value="{{ ($data->type_currency === 'USD' ? '$' : '₡') . ' ' . $trimestral }}"
                                                readonly>
                                        </div>
                                    </div>
                                @endif
                                @if ($data->monthly_calculation_amount != 0)
                                    <div class="required field" style="display: none;" id="monthly_field">
                                        <label for="numberIdentify">Mensual </label>
                                        <div class="ui input">
                                            <input name="monthly_calculation_amount" id="monthly_calculation_amount"
                                                autocomplete="off" type="text" class="readonly"
                                                value="{{ ($data->type_currency === 'USD' ? '$' : '₡') . ' ' . $mensual }}"
                                                readonly>
                                        </div>
                                    </div>
                                @endif
                            </div>
                            <div class="required field" style="width: 12rem">
                                <label for="periodicidad">Periodicidad</label>
                                <div class="ui selection dropdown">
                                    <input type="hidden" name="periodicidad" id="periodicidad"
                                        value="{{ $data->periodicity }}">
                                    <i class="dropdown icon"></i>
                                    <div class="default text">Seleccione una opción</div>
                                    <div class="menu">
                                        @if ($data->annual_calculation_amount > 0)
                                            <div class="item" data-value="1">Anual</div>
                                        @endif
                                        @if ($data->semiannual_calculation_amount > 0)
                                            <div class="item" data-value="2">Semestral</div>
                                        @endif
                                        @if ($data->quarterly_calculation_amount > 0)
                                            <div class="item" data-value="3">Trimestral</div>
                                        @endif
                                        @if ($data->monthly_calculation_amount > 0)
                                            <div class="item" data-value="4">Mensual</div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                @endif
                @if ($data->temporality === 'short')
                    <div id="short-section">
                        <div class=" title active">
                            <i class="dropdown icon"></i>
                            Cálculo de la prima póliza periodo corto
                        </div>
                        <div id="short-content" class=" content active">
                            <div class="four fields">
                                <div class="required field">
                                    <label for="typeIdentify">Pago único </label>
                                    <div class="ui input">
                                        <input name="singlePayment" id="singlePayment" autocomplete="off" type="text"
                                            value="{{ ($data->type_currency === 'USD' ? '$' : '₡') . ' ' . $unico }}"
                                            class="readonly" readonly>
                                    </div>
                                </div>

                                <div></div>
                                <div></div>
                                <div></div>
                            </div>
                        </div>
                    </div>
                @endif
            </div>
            <br>
            <div style="display:flex;justify-content: space-between">
                <div>
                    <a class="ui button primary" href="{{ url('/intermediario/poliza/' . $id . '/datos_firma') }}"
                        style="margin-right: 2rem;">
                        <i class="left chevron icon"></i> Atrás
                    </a>
                </div>
                <button type="submit" class="ui button primary" id="submit_policy" name="action" value="pay">
                    Ir a pago
                </button>
            </div>
            @if (isset($info))
                <div class="ui grey message" id="info-message">
                    <div class="header">
                        {{ $info }}
                    </div>
                </div>
            @endif

            <!-- Modal de carga -->
            <div id="progressContainer" style="display:none; width: 100%; margin-top: 20px;">
                <div class="ui progress" id="progressBar" data-percent="0">
                    <div class="bar"></div>
                    <div class="label">Cargando empleados...</div>
                </div>
            </div>
        </div>
    </form>
    <style>
        .readonly {
            background: rgba(0, 0, 0, .05) !important;
        }
    </style>
    <script>
        $(document).ready(function() {
            var lastReportId = @json($lastReportId);
            var type_preport = @json($type_preport);

            function loadedEmployes(id) {

                // Variable para verificar si el proceso está completo
                let isProcessComplete = false;

                // Función para obtener el progreso
                function getProgress() {
                    if (isProcessComplete) return; // Si el proceso ya está completo, no continuar

                    $.ajax({
                        url: '/massively/report/automatic/' + id,
                        method: 'GET',
                        success: function(data) {
                            // Si el porcentaje es menor al 100%, actualizar la barra de progreso
                            if (data.percentaje !== null && data.percentaje < 100) {
                                // Mostrar la barra de progreso
                                $('#progressContainer').show();

                                // Inicializar la barra de progreso
                                $('#progressBar').progress({
                                    percent: 0,
                                    text: {
                                        active: 'Cargando empleados...',
                                        success: 'Completado'
                                    }
                                });

                                $('#progressBar').progress('set percent', data.percentaje);
                                $('#submit_policy').addClass('disabled');
                            }

                            // Si el porcentaje llega al 100%, ocultamos la barra y mostramos el mensaje de éxito
                            if (data.percentaje === 100) {
                                isProcessComplete = true;
                                $('#progressContainer').hide();
                                $('#submit_policy').removeClass('disabled');
                                clearInterval(progressInterval);

                            }
                        },
                        error: function(jqXHR, textStatus, errorThrown) {
                            // Manejo de errores de la solicitud
                            clearInterval(progressInterval);
                            $('#progressContainer').hide();
                            $('#submit_policy').removeClass('disabled');

                            Swal.fire({
                                title: 'Error',
                                text: 'No se pudo completar la solicitud. Por favor, inténtelo de nuevo.',
                                icon: 'error',
                                confirmButtonText: 'Cerrar'
                            });
                        }
                    });
                }

                // Ejecutar la primera vez para obtener el progreso
                getProgress();

                // Configurar el intervalo para ejecutar cada 2 segundos
                const progressInterval = setInterval(getProgress, 6000);
            }
            console.log('last_report', lastReportId);

            if (lastReportId && type_preport == 'masivo') {
                loadedEmployes(lastReportId);
            }

            $('.ui.accordion').accordion();
            $('.ui.dropdown').dropdown();
            $('#periodicidad').change(function() {
                const periodicityValue = $(this).val();

                if (periodicityValue === '4') {
                    $('#monthly_field').show();
                } else {
                    $('#monthly_field').hide();

                }


            });

            const periodiValue = $('#periodicidad').val();

            if (periodiValue === '4') {
                $('#monthly_field').show();
            }

            $('#submit_policy').click(function(e) {
                e.preventDefault();
                loadingMain(true);
                let valid = true;
                let errorMsg = '';

                // Validar periodicidad
                const periodicityValue = $('#periodicidad').val();
                const temporality = '{{ $data->temporality }}';

                if (periodicityValue === '' && temporality === 'permanent') {
                    valid = false;
                    errorMsg += 'Por favor selecciona una periodicidad.<br>';
                }

                // Mostrar mensaje de error si hay problemas
                if (!valid) {
                    loadingMain(false);
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        html: errorMsg,
                        confirmButtonText: 'Aceptar'
                    });
                    return;
                }

                $('<input>').attr({
                    type: 'hidden',
                    name: 'action',
                    value: 'pay'
                }).appendTo($(this).closest('form'));
                $(this).closest('form').submit();
            });
        });
    </script>
@endsection
