@extends('layouts.main')

@section('title', 'Vista del Intermediario - Poliza')

@section('menu')
    @parent
@endsection

@section('content')
    @include('services/policy_sort/intermediary_policy/menu/menu', ['active' => 'datos_emision'])

    <form class="ui attached form container">
        {{ csrf_field() }}

        <div class=" ui styled fluid accordion" style="margin-top: 20px;">

            <div class="title active">
                <i class="dropdown icon"></i>
                Datos de emisión póliza <span style="color: red;" class="required">*</span>
            </div>


            <div class="content active">
                <div class=" accordion transition">
                    <div class="title">
                        <i class="dropdown icon"></i>
                        Identificación del tomador
                    </div>
                    <div class="content">
                        <div class="two fields">

                            <div class="required field">
                                <label>Tipo de identificación</label>
                                <div class="ui selection dropdown" id="heirDocTypeDropdown">
                                    <input type="hidden" name="doc_type" id="docType" class="minus capitalizes"
                                        value="{{ $affiliate->doc_type }}">
                                    <i class="dropdown icon"></i>
                                    <div class="default text minus heirDocTypeDropdown">Seleccionar tipo documento</div>
                                    <div class="menu">
                                        @foreach ($DOC_TYPES as $k => $v)
                                            <div class="item minus capitalizes heirDocTypeDropdown"
                                                data-value="{{ $k }}"
                                                @if ($k == $affiliate->doc_type) class="active selected" @endif>
                                                {{ $v }}
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            </div>

                            <div class="required field">
                                <label for="numberIdentify">Número de identificación</label>
                                <div class="ui input">
                                    <input name="numberIdentify" id="numberIdentify" autocomplete="off" type="text"
                                        value="{{ $affiliate->doc_number ?: '' }}">
                                </div>
                            </div>

                            <div class="field" style="margin-top: 25px;width: 54px;">
                                <button type="button" class="ui icon button add-field-btn secondary"
                                    onclick="searchIntermediary()">
                                    <i class="search icon"></i>
                                </button>
                            </div>

                        </div>
                    </div>

                    <div class="title ">
                        <i class="dropdown icon"></i>
                        Datos de la póliza
                    </div>
                    <div class=" content">
                        <div class="three fields">
                            <div class="field">
                                <label>Sector</label>
                                <div class="field">
                                    <div
                                        class="ui radio checkbox checkbox-spacing  disabled ">
                                        <input type="radio" name="sector" id="public" class="minus" value="public"
                                            {{ $policy_sort->economic_activity === 'public' ? 'checked' : '' }}>
                                        <label for="public">Público</label>
                                    </div>
                                    <div
                                        class="ui radio checkbox checkbox-spacing disabled ">
                                        <input type="radio" name="sector" id="private" class="minus" value="private"
                                            {{ $policy_sort->economic_activity === 'private' ? 'checked' : '' }}>
                                        <label for="private">Privado</label>
                                    </div>
                                </div>
                            </div>

                            @if($economicActivity->economic_branch && $economicActivity->economic_branch->branch_name)  {{--  cuando es publica no tiene rama--}}
                                <div class="required field">
                                    <label>Rama general de actividad económica</label>
                                    <input id="branch_activity" class="prompt" name="branch_activity" type="text" readonly value="{{ $economicActivity->economic_branch->branch_name ?? '' }}">
                                </div>
                            @endif

                            <div class="required field">
                                <label>Actividad económica especifica</label>
                                <textarea readonly rows="2">{{ $economicActivity->code.' - '.$economicActivity->activity_name }}</textarea>
                                <input id="economic_activity_code" name="economic_activity_code" type="hidden" value="{{$economicActivity->code ?? ''}}">
                                <input id="economic_activity_name" name="economic_activity_name" type="hidden" value="{{$economicActivity->activity_name ?? ''}}">
                            </div>

                        </div>
                    </div>

                    <div class="title vista_intermediario_tab_tomador ">
                        <i class="dropdown icon"></i>
                        Datos del tomador
                    </div>
                    <div class=" content">

                        <div class="three fields">
                            <div class="field">
                                <label for="policyUniqueCode">Código único</label>
                                <div class="ui input">

                                    <input name="policyUniqueCode" id="policyUniqueCode" autocomplete="off" type="text"
                                        value="{{ str_pad($affiliate->unique_code ?: '', 6, '0', STR_PAD_LEFT) }}" readonly>
                                </div>
                            </div>
                            <div class="required field">
                                <label for="policyHolderName">Nombre</label>
                                <div class="ui input">

                                    <input name="policyHolderName" id="policyHolderName" autocomplete="off" type="text"
                                        class="minus" value="{{ $affiliate->first_name ?: '' }}">
                                </div>
                            </div>

                            <div class="required field">
                                <label for="policyHolderPhone">Teléfonos</label>
                                <div class="ui input">

                                    <input name="policyHolderPhone" class="minus" id="policyHolderPhone"
                                        autocomplete="off" type="text" value="{{ $affiliate->phone ?: '' }}">
                                </div>
                            </div>
                        </div>

                        <div class="three fields">
                            <div class="required field">
                                <label for="policyHolderEmail">Correo electrónico de notificaciones</label>
                                <div class="ui input">

                                    <input name="policyHolderEmail" id="policyHolderEmail"
                                        onchange="validateDataEmail(event, 'policyHolderEmail')" autocomplete="off"
                                        type="text" value="{{ $affiliate->email }}">
                                </div>

                            </div>

                            <div class="field">
                                <label for="policyHolderEmailAdditional">Correo electrónico de notificaciones adicional</label>
                                <div class="ui input">

                                    <input name="policyHolderEmailAdditional" id="policyHolderEmailAdditional"
                                        onchange="validateDataEmail(event, 'policyHolderEmailAdditional')" autocomplete="off"
                                        type="text" value="{{ $policy_sort->notification_email_additional }}">
                                </div>

                            </div>

                            <div class="required field">
                                <label for="emailElectronicBilling">Correo electrónico para facturación
                                    electrónica</label>
                                <div class="ui input">

                                    <input name="emailElectronicBilling" id="emailElectronicBilling"
                                        onchange="validateDataEmail(event, 'emailElectronicBilling')" autocomplete="off"
                                        type="text" value="{{ $affiliate->electronic_billing_email }}">
                                </div>
                            </div>
                        </div>

                        <div class="three fields">
                            <div class="field">
                                <label for="ibanAccount">Cuenta IBAN</label>
                                <div class="ui input">
                                    <input name="ibanAccount" class="minus" id="ibanAccount" autocomplete="off"
                                        type="text" value="{{ $affiliate->iban_account }}">
                                </div>
                            </div>
                        </div>

                        <div class="three fields">
                            <div class="required field">
                                <label>Provincia</label>
                                <div id="province" class="ui search selection dropdown">
                                    <input type="hidden" class="minus" name="province"
                                        value="{{ $affiliate ? $affiliate->province : '' }}">
                                    <i class="dropdown icon"></i>
                                    <div class="default text">Selecciona uno</div>
                                    <div class="menu"></div>
                                </div>
                            </div>
                            <div class="required field">
                                <label>Cantón</label>
                                <div id="canton" class="ui search selection dropdown">
                                    <input type="hidden" name="canton" class="minus"
                                        value="{{ $affiliate->canton }}">
                                    <i class="dropdown icon"></i>
                                    <div class="default text">Selecciona uno</div>
                                    <div class="menu"></div>
                                </div>
                            </div>

                            <div class="required field">
                                <label>Distrito</label>
                                <div id="district" class="ui search selection dropdown">
                                    <input type="hidden" name="district" class="minus"
                                        value="{{ $affiliate->district }}">
                                    <i class="dropdown icon"></i>
                                    <div class="default text minus"></div>
                                    <div class="menu"></div>
                                </div>
                            </div>
                        </div>
                        <div class="required field">
                            <label for="employerAddress">Otras señas</label>
                            <div class="ui input">

                                <input name="employerAddress" id="employerAddress" autocomplete="off" type="text"
                                    class="minus" value="{{ $affiliate->employer_address ?? '' }}">
                            </div>
                        </div>

                        <div class="three fields">
                            <!-- Grupo de ocupación del afiliado -->
                            <div class="required field">
                                <label>Ocupación por grupo</label>
                                <div class="ui dropdown button input_button"
                                    style="display: flex !important; flex-direction: row; justify-content: space-between;">
                                    <span class="text" style="font-weight: normal;">Selecciona una opción</span>
                                    <i class="dropdown icon"></i>
                                    <input type="hidden" name="occupancy_group" id="occupancy_group"
                                        value="{{ $affiliate->occupancy_group ?? '' }}">
                                    <input type="hidden" name="occupancy_group_texto" id="occupancy_group_texto">
                                    <div class="menu">
                                        @foreach ($OCCUPATION_GROUPS as $key => $value)
                                            <div class="item">
                                                <i class="dropdown icon"></i>
                                                <span class="text">{{ $key }}</span>
                                                <div class="menu custom-submenu">
                                                    @foreach ($value as $key_sub => $value_sub)
                                                        <div class="item" data-value="{{ $key_sub }}">
                                                            {{ $value_sub }}</div>
                                                    @endforeach

                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            </div>

                            <!-- Ocupación del afiliado -->
                            <div class="required field">
                                <label>Ocupación</label>
                                <div class="ui search selection dropdown" id="occupationsDropdown">
                                    <input type="hidden" name="occupations_category_id" id="occupations_category_id" value="{{ $affiliate->occupations_category_id ?? ''  }}">
                                    <i class="dropdown icon"></i>
                                    <div class="default text">Ocupación</div>
                                    <div class="menu ">
                                        <div class="item" data-value=""> </div>
                                    </div>
                                </div>
                                <input name="ocupacion_tomador" id="ocupacion_tomador" type="hidden" value="{{ $affiliate->occupation ?? '' }}">
                            </div>

                            <div class="required field">
                                <label for="workRiskDropdown">Modalidad aseguramiento</label>
                                <div class="ui selection dropdown" id="workRiskDropdown">
                                    <input type="hidden" name="workRisk"
                                        value="{{ $activityQuotation->work_modality_id }}" id="workRisk">
                                    <i class="dropdown icon"></i>
                                    <div class="default text">Seleccionar riesgo de trabajo</div>
                                    <div class="menu">
                                        @foreach ($WORK_MODALITY as $k => $modality)
                                            <div class="item" data-value="{{ $k }}">
                                                {{ ucfirst(strtolower($modality)) }}
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="field" id="assurance-options">
                            <label for="options" id="title-option">Opciones de aseguramiento</label>
                            <div class="ui segment" id="content-option">
                                <div class="ui form">
                                    <div class="grouped fields">
                                        <div class="field">
                                            <div class="ui radio checkbox">
                                                <input type="radio" name="assurance" value="1" tabindex="0"
                                                    class="hidden"@if($activityQuotation->option_asegurement == 1) checked @endif>
                                                <label>Una persona permanente contratada para actividades domésticas,
                                                    además de uno o más trabajadores ocasionales.</label>
                                            </div>
                                        </div>
                                        <div class="field">
                                            <div class="ui radio checkbox">
                                                <input type="radio" name="assurance" value="2" tabindex="0"
                                                    class="hidden" @if($activityQuotation->option_asegurement == 2) checked @endif>

                                                <label>Dos personas permanentes contratadas para actividades domésticas,
                                                    además de uno o más trabajadores ocasionales.</label>
                                            </div>
                                        </div>
                                        <div class="field">
                                            <div class="ui radio checkbox">
                                                <input type="radio" name="assurance" value="3" tabindex="0"
                                                    class="hidden"  @if($activityQuotation->option_asegurement == 3) checked @endif>

                                                <label>Tres o más personas permanentes contratadas para actividades
                                                    domésticas, además de uno o más trabajadores ocasionales.</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="ui divider"></div>

                        <div class=" accordion transition">
                            <div class="title">
                                <i class="dropdown icon"></i>
                                Correo electrónico de notificaciones adicionales
                            </div>
                            <div class="content">
                                <div id="additional-emails-container"></div>

                                <button type="button" id="add-additional-noti-email" class="ui primary button">
                                    <i class="plus icon"></i> Agregar correo electrónico de notificaciones adicional
                                </button>
                            </div>

                            <div class="title">
                                <i class="dropdown icon"></i>
                                Direcciones adicionales
                            </div>
                            <div class="content">
                                <div id="locations-container"></div>

                                <button type="button" id="add-location" class="ui primary button">
                                    <i class="plus icon"></i> Agregar dirección
                                </button>
                                <datalist id="address_types">
                                    <option value="Oficina">
                                    <option value="Planta">
                                    <option value="Sucursal">
                                </datalist>
                            </div>

                            <div class="title">
                                <i class="dropdown icon"></i>
                                Teléfonos adicionales
                            </div>
                            <div class="content">
                                <div id="phones-container"></div>

                                <button type="button" id="add-phone" class="ui primary button">
                                    <i class="plus icon"></i> Agregar teléfono
                                </button>
                                <datalist id="phone_types">
                                    <option value="Móvil">
                                    <option value="Fijo">
                                    <option value="Trabajo">
                                </datalist>
                            </div>
                        </div>
                    </div>

                    <div class="title vista_intermediario_tab_responsable">
                        <i class="dropdown icon"></i>
                        Datos de personas autorizadas para reportes de accidentes y enfermedades
                    </div>
                    <div class="content">
                        <div id="fieldContainer" class="dynamic-field">
                            <!-- Para nuevos registros, el id será vacío -->
                            <input type="hidden" name="id_responsible[]" value="">

                            <div class="four fields">
                                <div class="required field">
                                    <label for="nameResponsible">Nombre</label>
                                    <div class="ui input">
                                        <input name="name_responsible[]" id="nameResponsible" autocomplete="off"
                                            type="text" class="minus"
                                            value="{{ $affiliate->name_responsible ?: '' }}">
                                    </div>
                                </div>

                                <div class="required field">
                                    <label>Tipo de identificación</label>
                                    <div class="ui selection dropdown ContactDocTypeResponsibleDropdown"
                                        id="heirDocTypeResponsibleDropdown">
                                        <input type="hidden" class="minus capitalizes" name="doc_type_responsible[]"
                                            id="docTypeResponsible" value="{{ $affiliate->doc_type_responsible }}">
                                        <i class="dropdown icon"></i>
                                        <div class="default text minus capitalizes">Seleccionar tipo documento</div>
                                        <div class="menu">
                                            @foreach ($DOC_TYPES as $k => $v)
                                                <div class="item minus capitalizes heirDocTypeResponsibleDropdown"
                                                    data-value="{{ $k }}"
                                                    @if ($k == $affiliate->doc_type_responsible) class="active selected" @endif>
                                                    {{ $v }}
                                                </div>
                                            @endforeach
                                        </div>
                                    </div>
                                </div>

                                <div class="required field">
                                    <label for="numberIdentifyResponsible">Número de identificación</label>
                                    <div class="ui input">
                                        <input name="number_identify_responsible[]" class="minus"
                                            id="numberIdentifyResponsible" autocomplete="off" type="text"
                                            value="{{ $affiliate->doc_number_responsible ?: '' }}">
                                    </div>
                                </div>

                                <div class="required field">
                                    <label for="occupationResponsible">Ocupación</label>
                                    <div class="ui fluid search selection dropdown occupationResponsibleDropdown">
                                        <input name="ocupation_responsible[]" id="occupationResponsible" type="hidden" value="{{ $affiliate->occupation_responsible ?? '' }}">
                                        <i class="dropdown icon"></i>
                                        <div class="default text">Seleccione una ocupación</div>
                                        <div class="menu">
                                            @foreach ($OCCUPATION_CATEGORIES as $occupationCategory)
                                                <div class="item" data-value="{{ $occupationCategory['id'] }}">{{ $occupationCategory['name'] }}</div>
                                            @endforeach
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="four fields">
                                <div class="required field">
                                    <label for="phoneResponsible">Teléfono</label>
                                    <div class="ui input">
                                        <input name="phone_responsible[]" id="phoneResponsible" autocomplete="off"
                                            type="text" class="minus"
                                            value="{{ $affiliate->phone_responsible ?? '' }}">
                                    </div>
                                </div>

                                <div class="required field">
                                    <label for="cellphoneResponsible">Celular</label>
                                    <div class="ui input">
                                        <input name="cellphone_responsible[]" id="cellphoneResponsible"
                                            autocomplete="off" type="text" class="minus"
                                            value="{{ $affiliate->cellphone_responsible ?: '' }}">
                                    </div>
                                </div>

                                <div class="required field">
                                    <label for="emailResponsible">Correo electrónico</label>
                                    <div class="ui input">
                                        <input name="email_responsible[]"
                                            onchange="validateDataEmail(event, 'emailResponsible')" id="emailResponsible"
                                            autocomplete="off" type="text" class="minus"
                                            value="{{ $affiliate->email_responsible }}">
                                    </div>
                                </div>

                                <!-- Botón Agregar/Eliminar en la misma fila que el último campo -->
                                <div class="field" style="margin-top: 25px;">
                                    <button type="button" class="ui icon button add-field-btn secondary"
                                        id="addFieldButton">
                                        <i class="plus circle icon"></i>
                                    </button>
                                    <button type="button" class="ui icon button remove-field-btn red"
                                        style="display:none;">
                                        <i class="minus circle icon"></i>
                                    </button>
                                </div>
                            </div>

                            <div class="ui divider"></div>
                        </div>
                    </div>


                    <div class="title">
                        <i class="dropdown icon"></i>
                        Datos del representante legal
                    </div>
                    <div class=" content">
                        <div class="two fields">
                            <div class="required field">
                                <label for="legalRepresentativeName">Nombre del representante legal</label>
                                <div class="ui input">

                                    <input name="legalRepresentativeName" id="legalRepresentativeName" autocomplete="off"
                                        type="text" class="minus"
                                        value="{{ $policy_sort->legal_representative_name }}">
                                </div>
                            </div>

                            <div class="required field">
                                <label for="legalRepresentativeId">Identificación del representante legal</label>
                                <div class="ui input">

                                    <input name="legalRepresentativeId" id="legalRepresentativeId" autocomplete="off"
                                        class="minus" type="text"
                                        value="{{ $policy_sort->legal_representative_id }}" />
                                </div>
                            </div>

                        </div>


                        <div class="two fields">
                            <!-- Grupo de ocupación del afiliado -->
                            <div class="required field">
                                <label>Ocupación por grupo del representante legal</label>
                                <div class="ui dropdown button input_button"
                                    style="display: flex !important; flex-direction: row; justify-content: space-between;" id="occupationsGroupDropdownRL">
                                    <span class="text" style="font-weight: normal;">Selecciona una opción</span>
                                    <i class="dropdown icon"></i>
                                    <input type="hidden" name="legal_representative_occupancy_group_id" id="legal_representative_occupancy_group_id"
                                        value="{{ $policy_sort->legal_representative_occupancy_group_id ?? '' }}">
                                    <input type="hidden" name="legal_representative_occupancy_group_texto" id="legal_representative_occupancy_group_texto">
                                    <div class="menu">
                                        @foreach ($OCCUPATION_GROUPS as $key => $value)
                                            <div class="item">
                                                <i class="dropdown icon"></i>
                                                <span class="text">{{ $key }}</span>
                                                <div class="menu custom-submenu-rl">
                                                    @foreach ($value as $key_sub => $value_sub)
                                                        <div class="item" data-value="{{ $key_sub }}">
                                                            {{ $value_sub }}</div>
                                                    @endforeach

                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            </div>

                            <!-- Ocupación del afiliado -->
                            <div class="required field">
                                <label>Ocupación del representante legal</label>
                                <div class="ui search selection dropdown" id="occupationsDropdownRL">
                                    <input type="hidden" name="legal_representative_occupations_category_id" id="legal_representative_occupations_category_id" value="{{ $policy_sort->legal_representative_occupations_category_id ?? ''  }}">
                                    <i class="dropdown icon"></i>
                                    <div class="default text">Ocupación</div>
                                    <div class="menu ">
                                        <div class="item" data-value=""> </div>
                                    </div>
                                </div>
                                <input name="legalRepresentativeProfession" id="legalRepresentativeProfession" type="hidden" value="{{ $policy_sort->legal_representative_profession ?? '' }}">
                            </div>
                        </div>

                    </div>
                </div>
                <br>
                <div class="ui grey message" id="info-message">
                    <div class="header">
                        Por favor, valide los datos ingresados para continuar con la emisión de la póliza.
                    </div>
                </div>
            </div>

           
        </div>
        <!-- boton para crear o solicitar nueva cotización -->
        <br>
        <div style=" width: 100%;">

            <div style="display: flex !important; justify-content: space-between; width: 100%;">
                <a class="ui primary button" href="/intermediario/poliza/{{ $id }}/datos_intermediario">Atrás
                </a>
                <!-- Botón de Siguiente a la derecha -->
                <button id="btn-submit-emission-data" class="ui button primary"
                    onclick="validateDataEmission(event)">Siguiente
                </button>
            </div>
            <!-- Botón de Atrás a la izquierda -->

        </div>
    </form>
    <style>
        .ui.search.code .results {
            width: 100%;
            /* Asegura que el contenedor de resultados tenga el mismo ancho que el campo de entrada */
        }

        .checkbox-spacing {
            margin-right: 20px;
        }

        .minus_font {
            text-transform: lowercase !important;
        }

        .capitalize {
            text-transform: none !important;
        }

        input {
            text-transform: none !important;
        }

        .input_button {
            width: 100%;
            background: white !important;
            border: 1px solid rgba(34, 36, 38, .15) !important;
        }

        .custom-submenu {
            max-width: 300px;
            /* Cambia el valor según tus necesidades */
            overflow: hidden;
            /* Oculta contenido que desborda */
            white-space: nowrap;
            /* Evita que el texto haga saltos de línea */
            text-overflow: ellipsis;
            /* Muestra puntos suspensivos (...) para textos largos */
        }

        /* Estilo para los elementos de texto dentro del submenú */
        .custom-submenu .item {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            padding: 8px;
            /* Ajusta el espaciado interno según tus necesidades */
        }
    </style>


    <script type="text/javascript" src="{{ secure_url('js/policy_issuance_data.js?v=1.4.15') }}"></script>
    <script>
        const occupationCategories = @json($OCCUPATION_CATEGORIES);

        $(document).ready(function() {
            // Inicializa el accordion
            $('.ui.accordion').accordion();
            // Inicializa los checkboxes
            $('.ui.radio.checkbox').checkbox();
            // Inicializa los checkboxes
            $('.ui.dropdown').dropdown();


            document.getElementById('ibanAccount').addEventListener('input', function(e) {
                let value = e.target.value;
                e.target.value = value.replace(/[^A-Za-z0-9]/g, '').slice(0, 22);
            });


            const fieldsNamesValidate = [
                'policyHolderName',
                'legalRepresentativeName',
                'legalRepresentativeProfession',
            ];


            // Función de validación para permitir solo letras y espacios
            function validateLettersAndSpaces(fieldId) {
                document.getElementById(fieldId).addEventListener('input', function(e) {
                    var value = e.target.value;
                    e.target.value = value.replace(/[^a-zA-Z\s]/g, '');
                });
            }
            // Validación específica para policyHolderPhone (permitir solo números)
            document.getElementById('policyHolderPhone').addEventListener('input', function(e) {
                var value = e.target.value;
                e.target.value = value.replace(/[^0-9]/g, '');
            });
            /**
             *  fin *
             **/


            validateEmailField("policyHolderEmail", "invalidEmail");
            validateEmailField("policyHolderEmailAdditional", "invalidEmail");
            validateEmailField("emailElectronicBilling", "invalidEmail2");
            validateEmailField("emailResponsible", "invalidEmail3");
            validateLettersOnly(document.getElementById('legalRepresentativeName'));
            validateLettersOnly(document.getElementById('policyHolderName'));
            validateLettersOnly(document.getElementById('ocupacion_tomador'));
            validateNumbersOnly(document.getElementById('policyHolderPhone'));
            validateLettersAndNumbersOnly(document.getElementById('economic_activity_code'));
            validateNumbersOnly(document.getElementById('cellphoneResponsible'));
            validateNumbersOnly(document.getElementById('numberIdentifyResponsible'));
            validateLettersAndNumbersOnly(document.getElementById('legalRepresentativeProfession'));
            validateLettersAndNumbersOnly(document.getElementById('occupationResponsible'));


            /**
             * Inicio de bloque de código que permite traer lista de actividad economica *
             **/
            initializeSearchOnPageLoad();
            const jsonSource = '';

            // ** Validaciones para el cambio de sector **
            $('input[name="sector"]').change(function() {
                $('input[name="diagnostics[cod][]"]').val('');
                $('input[name="diagnostics[description][]"]').val('');

                /* Condición que permite validar el tipo de sector */
                initializeSearchOnPageLoad();
                initializeSearch(jsonSource);
            });

            // ** Función que permite limbiar información de los campos cuando se borra el contenido del campo "Nombre" **
            $('input[name="diagnostics[cod][]"]').on('input', function() {
                if ($(this).val() === '') {
                    $('input[name="diagnostics[description][]"]').val('');
                }
            });
            /**
             * Fin de bloque de código que permite traer lista de actividad economica *
             **/
            $('#economic_activity_name, #employerAddress').each(function() {
                let value = $(this).val().toLowerCase();
                $(this).val(value.charAt(0).toUpperCase() + value.slice(1));
            });

            let afilliate = @json($affiliate);
            if (afilliate.doc_type !== 'CJ') {

                $('#legalRepresentativeName').val(afilliate.first_name ?? '');
                $('#legalRepresentativeId').val(afilliate.doc_number ?? '');

                let psLegalRepresentativeOccupancyGroupId = @json($policy_sort->legal_representative_occupancy_group_id ?? null);
                let psLegalRepresentativeOccupationsCategoryId = @json($policy_sort->legal_representative_occupations_category_id ?? null);

                if (!psLegalRepresentativeOccupancyGroupId && !psLegalRepresentativeOccupationsCategoryId) {

                    let aOccupancyGroup = @json($affiliate->occupancy_group ?? null);
                    let aOccupationsCategoryId = @json($affiliate->occupations_category_id ?? null);

                    // sincronizar el grupo ocupacional
                    $('#occupationsGroupDropdownRL').dropdown('set selected', aOccupancyGroup);
                    // sincronizar la ocupación
                    cargarOcupacionesRl(aOccupancyGroup);
                    $('#occupationsDropdownRL').dropdown('set selected', aOccupationsCategoryId);
                    let occupationRL = occupationCategories?.find(function(occupationCategory) {
                        return occupationCategory?.id == aOccupationsCategoryId;
                    });
                    $('#legalRepresentativeProfession').val(occupationRL?.name ?? null);

                }
            }

            {{-- 
            $('#ocupacion_tomador').on('input', function() {
                // Sincroniza el valor del primer campo con el segundo
                if ($('#docType').val() !== 'CJ') {
                    $('#legalRepresentativeProfession').val($(this).val());
                }

            });
             --}}

        });

        
        /**
         * Explicacion
         * 
         * Este bloque gestiona la visibilidad y el comportamiento de selección de un menú desplegable
         * para las opciones de riesgo de trabajo según el tipo de documento seleccionado. Específicamente:
         * 
         * - Si el tipo de documento es "CJ", la opción "Riesgo Independiente" (valor "6") se oculta.
         *   Además, si "Riesgo Independiente" está actualmente seleccionado, se limpia la selección
         *   y se restablece el menú desplegable a su estado predeterminado.
         * - Para otros tipos de documento, la opción "Riesgo Independiente" se muestra.
         * 
         * El bloque inicializa el estado del menú desplegable al cargar la página y lo actualiza dinámicamente
         * cuando se cambia el tipo de documento a través del menú desplegable.
         */
        $(document).ready(function () {
            function toggleIndependentRiskOption(docType) {
                const $workRiskDropdown = $('#workRiskDropdown');
                const $menu = $workRiskDropdown.find('.menu');
                const $independentItem = $menu.find('.item[data-value="6"]');

                if (docType === 'CJ') {
                    // Ocultar la opción
                    $independentItem.hide();

                    // Limpiar selección si es el valor seleccionado
                    if ($('#workRisk').val() === '6') {
                        $('#workRisk').val('');
                        $workRiskDropdown.find('.text').text('Seleccionar riesgo de trabajo');
                        $menu.find('.item').removeClass('active selected');
                    }
                } else {
                    // Mostrar la opción
                    $independentItem.show();
                }
            }

            // Inicialización al cargar la página
            toggleIndependentRiskOption($('#docType').val());

            // Manejar cambio de tipo de identificación
            $('#heirDocTypeDropdown .item').on('click', function () {
                const selectedValue = $(this).data('value');
                const selectedText = $(this).text().trim();

                // Actualizar valor y texto visibles
                $('#docType').val(selectedValue);
                $('#heirDocTypeDropdown .text').text(selectedText);
                $('#heirDocTypeDropdown .item').removeClass('active selected');
                $(this).addClass('active selected');

                toggleIndependentRiskOption(selectedValue);
            });
        });
        
        function goBack() {
            // Redirige a una URL específica
            window.location.href = '/intermediario/poliza/{{ $id }}/datos_intermediario';
        }

        /**
         * Inicio de bloque de código que permite obtener el json si tenía sector en la cotizacion *
         **/
        function initializeSearchOnPageLoad() {
            // Obtener el valor del sector seleccionado en la carga de la página
            const selectedSectorValue = $('input[name="sector"]:checked').val();
            if (selectedSectorValue) {
                // Si hay un sector seleccionado, cargar el JSON
                if (selectedSectorValue === 'public') {
                    jsonSource = '/js/economic_activity/public.json?v=1.0';
                } else if (selectedSectorValue === 'private') {
                    jsonSource = '/js/economic_activity/private.json?v=1.0';
                } else {
                    jsonSource = '';
                }
                initializeSearch(jsonSource)
            }
        }

        /**
         * Fin de bloque de código que permite obtener el json si tenia sector en la cotizacion *
         **/



        function captureDataEmission() {
            return {
                docType: $('#heirDocTypeDropdown').dropdown('get value'),
                numberIdentify: $('#numberIdentify').val(),
                policyHolderName: $('#policyHolderName').val(),
                occupation: $('#ocupacion_tomador').val(),
                occupations_category_id: $('#occupations_category_id').val(),
                workRisk: $('#workRisk').val(),
                policyHolderPhone: $('#policyHolderPhone').val(),
                policyHolderEmail: $('#policyHolderEmail').val(),
                emailElectronicBilling: $('#emailElectronicBilling').val(),
                employerAddress: $('#employerAddress').val(),
                province: $('input[name="province"]').val(),
                canton: $('input[name="canton"]').val(),
                district: $('input[name="district"]').val(),
                legalRepresentativeName: $('#legalRepresentativeName').val(),
                legalRepresentativeId: $('#legalRepresentativeId').val(),
                legalRepresentativeProfession: $('#legalRepresentativeProfession').val(),
                legalRepresentativeOccupancyGroupId: $('#legal_representative_occupancy_group_id').val(),
                legalRepresentativeOccupationsCategoryId: $('#legal_representative_occupations_category_id').val(),

                sector: $('input[name="sector"]:checked').val(),
                economic_activity_code: $('#economic_activity_code').val(),
                economic_activity_name: $('#economic_activity_name').val(),
                occupancy_group: $('#occupancy_group').val(),

                //** Nuevos campos responsable de reportes de accidentes **//
                nameResponsible: $('#nameResponsible').val(),
                docTypeResponsible: $('#docTypeResponsible').val(),
                numberIdentifyResponsible: $('#numberIdentifyResponsible').val(),
                occupationResponsible: $('#occupationResponsible').val(),
                phoneResponsible: $('#phoneResponsible').val(),
                cellphoneResponsible: $('#cellphoneResponsible').val(),
                emailResponsible: $('#emailResponsible').val(),
            };
        }

        function isValidEmail(email) {
            var emailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
            return emailPattern.test(email);
        }

        function validateDataEmission(event) {
            event.preventDefault();
            const intermediaryData = captureDataEmission();

            // Verificar campos del intermediario
            const missingIntermediaryFields = Object.entries(intermediaryData)
                .filter(([key, value]) => !value)
                .map(([key]) => {
                    switch (key) {
                        case 'docType':
                            return 'Tipo de identificación';
                        case 'numberIdentify':
                            return 'Numero de identificación';
                        case 'policyHolderName':
                            return 'Nombre del tomador';
                        case 'occupation':
                            return 'Ocupación del tomador';
                        case 'occupations_category_id':
                            return 'Ocupación / Cargo';
                        case 'workRisk':
                            return 'Modalidad aseguramiento';
                        case 'policyHolderPhone':
                            return 'Teléfonos';
                        case 'policyHolderEmail':
                            return 'Correo electrónico de notificaciones';
                        case 'emailElectronicBilling':
                            return 'Correo electrónico para facturación electrónica';
                        case 'employerAddress':
                            return 'Otras señas';
                        case 'occupancy_group':
                            return 'Ocupación por grupo';
                        case 'province':
                            return 'Provincia';
                        case 'canton':
                            return 'Cantón';
                        case 'district':
                            return 'Distrito';
                        case 'legalRepresentativeName':
                            return 'Nombre del representante legal';
                        case 'legalRepresentativeId':
                            return 'Identificación del representante legal';
                        case 'legalRepresentativeProfession':
                            return 'Profesión del representante legal';
                        case 'legalRepresentativeOccupancyGroupId':
                            return 'Ocupación por grupo del representante legal';
                        case 'legalRepresentativeOccupationsCategoryId':
                            return 'Ocupación del representante legal';

                        case 'economic_activity_code':
                            return 'Código de la actividad económica';
                        case 'economic_activity_name':
                            return 'Nombre de la actividad económica';
                        case 'nameResponsible':
                            return 'Nombre del responsable de reportes de accidente';
                        case 'docTypeResponsible':
                            return 'Tipo de identificación del responsable de reportes de accidente';
                        case 'numberIdentifyResponsible':
                            return 'Número de identificación del responsable de reportes de accidente';
                        case 'occupationResponsible':
                            return 'Ocupación del responsable de reportes de accidente';
                        case 'phoneResponsible':
                            return 'Teléfono domicilio del responsable de reportes de accidente';
                        case 'cellphoneResponsible':
                            return 'Celular del responsable de reportes de accidente';
                        case 'emailResponsible':
                            return 'Correo electrónico del responsable de reportes de accidente';
                        default:
                            return key;
                    }
                });

            // Construir mensaje de error usando Semantic UI en un solo contenedor
            let errorMessage = '<div class="ui segment">';
            if (!isValidEmail(intermediaryData.policyHolderEmail)) {
                missingIntermediaryFields.push('Correo electrónico de notificaciones inválido');
            }
            if (!isValidEmail(intermediaryData.emailElectronicBilling)) {
                missingIntermediaryFields.push('Correo electrónico para facturación electrónica inválido');
            }
            if (missingIntermediaryFields.length > 0) {
                errorMessage += '<h4>Por favor, complete los siguientes campos:</h4>';
                errorMessage += '<ul class="ui list">';
                missingIntermediaryFields.forEach(field => {
                    errorMessage += `<li>${field}</li>`;
                });
                errorMessage += '</ul>';
            }

            errorMessage += '</div>';

            if (errorMessage !== '<div class="ui segment"></div>') {
                return Swal.fire({
                    title: 'Información incompleta',
                    html: errorMessage,
                    icon: 'warning',
                    showConfirmButton: false,
                    showCancelButton: true,
                    cancelButtonText: 'Aceptar',
                    cancelButtonColor: '#000000'
                });


            } else {

                let isValid = true;

                $('input[name="name_responsible[]"]').each(function() {
                    if ($(this).val() === "") {
                        Swal.fire({
                            title: 'Campos incompletos',
                            html: "El nombre del responsable es obligatorio",
                            icon: 'warning',
                            showConfirmButton: false,
                            showCancelButton: true,
                            cancelButtonText: 'Cerrar',
                            cancelButtonColor: '#d33'
                        });
                        isValid = false;
                        return false;
                    }
                });

                $('input[name="doc_type_responsible[]"]').each(function() {
                    if ($(this).val() === "") {
                        Swal.fire({
                            title: 'Campos incompletos',
                            html: "El tupo de identificación del responsable es obligatorio",
                            icon: 'warning',
                            showConfirmButton: false,
                            showCancelButton: true,
                            cancelButtonText: 'Cerrar',
                            cancelButtonColor: '#d33'
                        });
                        isValid = false;
                        return false;
                    }
                });

                $('input[name="number_identify_responsible[]"]').each(function() {
                    if ($(this).val() === "") {
                        Swal.fire({
                            title: 'Campos incompletos',
                            html: "El número de identificación del responsable es obligatorio",
                            icon: 'warning',
                            showConfirmButton: false,
                            showCancelButton: true,
                            cancelButtonText: 'Cerrar',
                            cancelButtonColor: '#d33'
                        });
                        isValid = false;
                        return false;
                    }
                });

                $('input[name="phone_responsible[]"]').each(function() {
                    if ($(this).val() === "") {
                        Swal.fire({
                            title: 'Campos incompletos',
                            html: "El telefono del responsable es obligatorio",
                            icon: 'warning',
                            showConfirmButton: false,
                            showCancelButton: true,
                            cancelButtonText: 'Cerrar',
                            cancelButtonColor: '#d33'
                        });
                        isValid = false;
                        return false;
                    }
                });


                $('input[name="cellphone_responsible[]"]').each(function() {
                    if ($(this).val() === "") {
                        Swal.fire({
                            title: 'Campos incompletos',
                            html: "El celular del responsable es obligatorio",
                            icon: 'warning',
                            showConfirmButton: false,
                            showCancelButton: true,
                            cancelButtonText: 'Cerrar',
                            cancelButtonColor: '#d33'
                        });
                        isValid = false;
                        return false;
                    }
                });

                $('input[name="email_responsible[]"]').each(function(index) {
                    const email = $(this).val();
                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

                    if (email === "" || !emailRegex.test(email)) {
                        Swal.fire({
                            title: 'Campos incompletos',
                            html: `El correo electrónico del responsable ${index + 1} es obligatorio`,
                            icon: 'warning',
                            showConfirmButton: false,
                            showCancelButton: true,
                            cancelButtonText: 'Cerrar',
                            cancelButtonColor: '#d33'
                        });
                        isValid = false;
                        return false;
                    }
                });

                const ibanAccount = $('#ibanAccount').val();

                if (ibanAccount) {

                    let errorMessaged = '<div class="ui segment">';

                    errorMessaged += '<h4>Datos del tomador:</h4>';
                    errorMessaged += '<ul class="ui list">';
                    errorMessaged += `<li>La cuenta IBAN debe tener exactamente 22 caracteres</li>`;
                    errorMessaged += '</ul>';
                    errorMessaged += '</div>';

                    if (ibanAccount.length !== 22) {
                        Swal.fire({
                            title: 'Un momento',
                            html: errorMessaged,
                            icon: 'warning',
                            showConfirmButton: false,
                            showCancelButton: true,
                            cancelButtonText: 'Cerrar',
                            cancelButtonColor: '#d33'
                        });
                        isValid = false;
                        return false;
                    }
                }



                if (isValid) {
                    loadingButton("btn-submit-emission-data", true);
                    let data = captureDataEmission();
                    // Crear un nuevo objeto FormData
                    let formData = new FormData();

                    const docType = $('#heirDocTypeDropdown').dropdown('get value');
                    const numberIdentify = $('#numberIdentify').val();
                    const policyHolderName = $('#policyHolderName').val();
                    const ocupacion_tomador = $('#ocupacion_tomador').val();
                    const occupations_category_id = $('#occupations_category_id').val();
                    const occupancy_group = $('#occupancy_group').val();
                    const policyHolderPhone = $('#policyHolderPhone').val();
                    const policyHolderEmail = $('#policyHolderEmail').val();
                    const policyHolderEmailAdditional = $('#policyHolderEmailAdditional').val();
                    const emailElectronicBilling = $('#emailElectronicBilling').val();
                    const ibanAccount = $('#ibanAccount').val();
                    const employerAddress = $('#employerAddress').val();
                    const province = $('input[name="province"]').val();
                    const canton = $('input[name="canton"]').val();
                    const district = $('input[name="district"]').val();
                    const legalRepresentativeName = $('#legalRepresentativeName').val();
                    const legalRepresentativeId = $('#legalRepresentativeId').val();
                    const legalRepresentativeProfession = $('#legalRepresentativeProfession').val();
                    const legalRepresentativeOccupancyGroupId = $('#legal_representative_occupancy_group_id').val();
                    const legalRepresentativeOccupationsCategoryId = $('#legal_representative_occupations_category_id').val();

                    const sector = $('input[name="sector"]:checked').val();
                    const economic_activity_code = $('#economic_activity_code').val();
                    const economic_activity_name = $('#economic_activity_name').val();
                    const workRisk = $('#workRisk').val();
                    const assurance = $('input[name="assurance"]:checked').val();


                    // Agregar los valores de las variables al FormData
                    formData.append('docType', docType);
                    formData.append('numberIdentify', numberIdentify);
                    formData.append('policyHolderName', policyHolderName);
                    formData.append('occupation', ocupacion_tomador);
                    formData.append('occupations_category_id', occupations_category_id);
                    formData.append('occupancy_group', occupancy_group);
                    formData.append('policyHolderPhone', policyHolderPhone);
                    formData.append('policyHolderEmail', policyHolderEmail);
                    formData.append('policyHolderEmailAdditional', policyHolderEmailAdditional);
                    formData.append('emailElectronicBilling', emailElectronicBilling);
                    formData.append('ibanAccount', ibanAccount);
                    formData.append('employerAddress', employerAddress);
                    formData.append('province', province);
                    formData.append('canton', canton);
                    formData.append('district', district);
                    formData.append('legalRepresentativeName', legalRepresentativeName);
                    formData.append('legalRepresentativeId', legalRepresentativeId);
                    formData.append('legalRepresentativeProfession', legalRepresentativeProfession);
                    formData.append('legalRepresentativeOccupancyGroupId', legalRepresentativeOccupancyGroupId);
                    formData.append('legalRepresentativeOccupationsCategoryId', legalRepresentativeOccupationsCategoryId);

                    formData.append('sector', sector);
                    formData.append('economic_activity_code', economic_activity_code);
                    formData.append('economic_activity_name', economic_activity_name);
                    formData.append('workRisk', workRisk);
                    formData.append('assurance', assurance);

                    //Recoger todos los campos dinámicos y sus valores
                    $('.dynamic-field').each(function(index) {
                        var $dropdown = $(this).find('.ContactDocTypeResponsibleDropdown');
                        var type_identification = $dropdown.dropdown('get value');
                        var phone = $(this).find('input[name="phone_responsible[]"]').val();
                        var name = $(this).find('input[name="name_responsible[]"]').val();
                        var number_identification = $(this).find('input[name="number_identify_responsible[]"]').val();
                        var ocupation = $(this).find('input[name="ocupation_responsible[]"]').val();
                        var cellphone = $(this).find('input[name="cellphone_responsible[]"]').val();
                        var email = $(this).find('input[name="email_responsible[]"]').val();
                        var id = $(this).find('input[name="id_responsible[]"]').val();

                        let contactOccupation = occupationCategories?.find(function(occupationCategory) {
                            return occupationCategory?.id == ocupation;
                        });

                        // Agregar los datos al FormData
                        formData.append(`fields[${index}][name_responsible]`, name);
                        formData.append(`fields[${index}][type_identification]`, type_identification);
                        formData.append(`fields[${index}][number_identify_responsible]`, number_identification);
                        formData.append(`fields[${index}][ocupation_responsible]`, contactOccupation?.name);
                        formData.append(`fields[${index}][occupations_category_id]`, contactOccupation?.id);

                        formData.append(`fields[${index}][phone_responsible]`, phone);
                        formData.append(`fields[${index}][cellphone_responsible]`, cellphone);
                        formData.append(`fields[${index}][email_responsible]`, email);
                        formData.append(`fields[${index}][id]`, id);
                    });

                    $.ajax({
                        url: '/intermediario/poliza/{{ $id }}/datos_emision/save',
                        type: 'POST',
                        data: formData,
                        processData: false, // Evitar que jQuery procese el FormData
                        contentType: false,
                        headers: {
                            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                        },
                        success: function(response) {
                            // Acción cuando la solicitud sea exitosa
                            window.location.href = '/intermediario/poliza/{{ $id }}/documentos_externa';
                        },
                        error: function(xhr, status, error) {
                            // Acción cuando la solicitud falle
                            console.error('Error al enviar los datos:', error);
                            loadingButton("btn-submit-emission-data", false);
                        }
                    });
                }
            }
        }

        /**
         * Inicio Función para inicializar la búsqueda de actividades económicas.
         *La ruta del archivo JSON que contiene los datos de las actividades económicas. *
         **/
        function initializeSearch(source) {
            if (!source) return; // * No cargar si la ruta del json está vacía *
            // * Consulta de JSON correspondiente a su ruta *
            $.getJSON(source, function(json) {
                // Normalizamos los datos, asegurando que el campo CODE sea siempre una cadena
                json = json.map(function(item) {
                    item.CODE = item.CODE.toString();
                    return item;
                });
                const search = $('.ui.search.code');
                search.search('destroy');
                search.search({
                    source: json,
                    fields: {
                        title: 'CODE',
                        description: 'ACTIVITY_NAME'
                    },
                    searchFields: ['CODE', 'ACTIVITY_NAME'],
                    regExp: {
                        escape: /[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,
                        beginsWith: ''
                    },
                    showNoResults: true,
                    maxResults: 3,
                    cache: false,
                    searchFullText: false,
                    error: {
                        noResults: 'No se encontraron resultados para tu búsqueda.'
                    },
                    onSelect: function(result) {
                        $('#economic_activity_code').val(result.CODE);
                        $('input[name="diagnostics[description][]"]').val(result.ACTIVITY_NAME);
                    }
                });
            });
        }

        function validateDataEmail(event, inputId) {
            event?.preventDefault();

            const email = $(`#${inputId}`).val();
            var emailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

            if (!emailPattern.test(email)) {
                Swal.fire({
                    icon: 'warning',
                    title: 'Un momento',
                    html: 'Correo electrónico no es válido',
                });
                return;
            }
        }
        /**
         * Fin de bloque de código que permite inicializar la busqueda de la actividad economica *
         **/

        $('#addFieldButton').on('click', function() {
            // Clonar el último conjunto de campos dinámicos
            var newFields = $('.dynamic-field').last().clone();

            // Limpiar los valores de los inputs clonados
            newFields.find('input').val('');

            // Limpiar el dropdown
            newFields.find('.ContactDocTypeResponsibleDropdown').dropdown('clear');
            newFields.find('.occupationResponsibleDropdown').dropdown('clear');

            // Ocultar el botón de agregar en los nuevos campos y mostrar el de eliminar
            newFields.find('.add-field-btn').hide();
            newFields.find('.remove-field-btn').show();

            // Generar nuevos IDs únicos para inputs y selects clonados
            newFields.find('input, select').each(function() {
                var oldId = $(this).attr('id');
                if (oldId) {
                    var newId = oldId + '_' + Math.random().toString(36).substring(7);
                    $(this).attr('id', newId);
                    $(this).siblings('label[for="' + oldId + '"]').attr('for', newId);
                }
            });

            // Asignar el evento onchange a los inputs de email en los nuevos campos clonados
            newFields.find('input[name="email_responsible[]"]').on('change', function(event) {
                validateDataEmail(event, $(this).attr('id'));
            });

            // Añadir el nuevo conjunto de campos al contenedor
            $('#fieldContainer').append(newFields);

            // Inicializar el nuevo dropdown
            newFields.find('.ContactDocTypeResponsibleDropdown').dropdown({
                onChange: function(value, text, $choice) {
                    updateDropdownOptions();
                }
            });
            newFields.find('.occupationResponsibleDropdown').dropdown({
                onChange: function(value, text, $choice) {
                    updateOccupationResponsibleDropdownOptions();
                }
            });

            // Evento de eliminación para los campos clonados
            newFields.find('.remove-field-btn').on('click', function() {
                $(this).closest('.dynamic-field').remove();
            });

            updateDropdownOptions();
            updateOccupationResponsibleDropdownOptions();
        });

        // Eliminar campos dinámicos
        $('#fieldContainer').on('click', '.remove-field-btn', function() {
            $(this).closest('.dynamic-field').remove(); // Remover los campos
        });


        // Función para actualizar las opciones de los dropdowns
        function updateDropdownOptions() {
            var selectedValues = [];
            $('.ContactDocTypeResponsibleDropdown').each(function() {
                var value = $(this).dropdown('get value');
                if (value) {
                    selectedValues.push(value);
                }
            });
        }

        function updateOccupationResponsibleDropdownOptions() {
            let selectedValues = [];
            $('.occupationResponsibleDropdown').each(function() {
                let value = $(this).dropdown('get value');
                if (value) {
                    selectedValues.push(value);
                }
            });
        }

        $(document).ready(function() {
            function capitalizeWords(value) {
                if (value) {
                    return value.toLowerCase().replace(/(?:^|\s)\S/g, function(char) {
                        return char.toUpperCase();
                    });
                }
                return '';
            }

            $('#docTypeResponsible, .heirDocTypeResponsibleDropdown, #nameResponsible, #policyHolderName, #legalRepresentativeName, .heirDocTypeDropdown')
                .each(function() {
                    let value = $(this).val() || $(this).text();

                    if (value) {
                        let capitalizedValue = capitalizeWords(value);

                        if ($(this).is('input')) {
                            $(this).val(capitalizedValue);
                        } else {
                            $(this).text(capitalizedValue);
                        }
                    }
                });
        });
    </script>


    <!-- Aquí convertimos el array de PHP a JSON y lo almacenamos en una variable JavaScript -->
    <script>
        let responsables = @json($policy_contacts);

        // Lógica para llenar los campos dinámicos como en el ejemplo anterior
        responsables.forEach((responsable, index) => {
            // Agregar campos dinámicos si es necesario
            if (index > 0) {
                document.getElementById('addFieldButton').click();
            }

            // Seleccionar el último conjunto de campos dinámicos
            let dynamicFields = document.querySelectorAll('.dynamic-field');
            let currentField = dynamicFields[dynamicFields.length - 1]; // El más reciente

            // Rellenar los campos con los valores del array
            currentField.querySelector('input[name="name_responsible[]"]').value = responsable.name_responsible;
            currentField.querySelector('input[name="number_identify_responsible[]"]').value = responsable
                .number_identify_responsible;
            currentField.querySelector('input[name="ocupation_responsible[]"]').value = responsable
                .ocupation_responsible;
            currentField.querySelector('input[name="phone_responsible[]"]').value = responsable.phone_responsible;
            currentField.querySelector('input[name="cellphone_responsible[]"]').value = responsable
                .cellphone_responsible;
            currentField.querySelector('input[name="email_responsible[]"]').value = responsable.email_responsible;
            currentField.querySelector('input[name="id_responsible[]"]').value = responsable.id;

            // Manejar el dropdown de tipo de identificación
            let type_identification = responsable.type_identification.split(',')[0];

            // Asegurarse de seleccionar el dropdown como objeto jQuery
            let dropdown = $(currentField).find('.ContactDocTypeResponsibleDropdown');

            // Inicializar el dropdown si es necesario
            dropdown.dropdown();

            // Establecer los valores seleccionados dinámicamente
            dropdown.dropdown('set selected', type_identification);
        });
    </script>

    <script>
        function searchIntermediary() {

            var numero_id = $('#numberIdentify').val();
            var doc_type = $('#docType').val();

            $.ajax({
                url: '/webservice-credid',
                type: 'GET',
                data: {
                    numero_id: numero_id,
                    doc_type: doc_type
                },
                success: function(response) {

                    $("#numberIdentify").val(response.numberIdentify);
                    $("#policyHolderName").val(response.policyHolderName);
                    $("#policyHolderPhone").val(response.policyHolderPhone);
                    $("#policyHolderEmail").val(response.policyHolderEmail);
                    $("#legalRepresentativeName").val(response.legalRepresentativeName);
                    $("#legalRepresentativeId").val(response.legalRepresentativeId);
                    $("#legalRepresentativeProfession").val(response.legalRepresentativeProfession);

                    $("#province").val(response.province);
                    $("#canton").val(response.canton);
                    $("#district").val(response.district);
                    $("#ibanAccount").val(response.ibanAccount);
                    $("#emailElectronicBilling").val(response.emailElectronicBilling);
                    $("#employerAddress").val(response.employerAddress);

                },
                error: function(response) {
                    console.log('Error al cargar los datos');
                }
            });
        }
        const policy = @json($policy_sort);

        const workRiskDropdown = $('#workRiskDropdown');
        const economicActivity = policy.economic_activity; // Asegúrate de que este campo existe

        // Inicializa el dropdown de Semantic UI
        workRiskDropdown.dropdown();

        if (economicActivity === 'public') {
            // Selecciona la opción 5 y deshabilita el dropdown
            workRiskDropdown.dropdown('set selected', 5);
            workRiskDropdown.addClass('disabled');
        } else {
            // Habilita el dropdown y selecciona el valor por defecto si es necesario
            workRiskDropdown.removeClass('disabled');

            // Mostrar solo las primeras 4 opciones si no es 'public'
            workRiskDropdown.find('.menu .item').slice(4).hide();

        }

        $(document).ready(function() {
            const initialWorkRiskValue = $('#workRisk').val();
            if (initialWorkRiskValue === "3") {
                $('#title-option').show();
                $('#content-option').show();
            }
            // Función para mostrar/ocultar el bloque si el valor cambia
            $('#workRiskDropdown').dropdown({
                onChange: function(value) {
                    if (value === "3") {
                        $('#title-option').show();
                        $('#content-option').show();
                    } else {
                        $('#title-option').hide();
                        $('#content-option').hide();
                    }
                }
            });
        });
    </script>
    <style>
        input[readonly],
        textarea[readonly] {
            background-color: #f3f4f5 !important;
        }

        #content-option {
            display: none;
        }

        #title-option {
            display: none;
        }
    </style>

    {{-- CORREOS DE NOTIFICACIONES ADICIONALES --}}
    <script>
        const existingAdditionalNotificationEmails = @json($policy_additional_notification_emails);

        $(document).ready(function () {
            let additionaEmailsIndex = 0;

            // Cargar direcciones existentes si hay
            if (Array.isArray(existingPhones)) {
                existingAdditionalNotificationEmails.forEach(addr => {
                    addAdditionalNotificationEmailGroup(addr);
                });
            }

            $("#add-additional-noti-email").click(function () {
                addAdditionalNotificationEmailGroup();
            });

            function addAdditionalNotificationEmailGroup(exisAdditionalNotificationEmail = {}) {
                if ($(".noti-email-group").length >= 2) return;

                const index = additionaEmailsIndex++;

                const group = $(`
                    <div class="ui segment noti-email-group" data-index="${index}" style="margin-bottom: 8px;">
                        <div class="ui form">
                            <div class="two fields">
                                <div class="field required">
                                    <label>Correo electrónico de notificaciones adicional</label>
                                    <input type="email" class="ane_noti_email">
                                </div>
                                <div class="field" style="margin-top: 25px;">
                                    <button type="button" class="ui secondary button save-ade-email">Guardar</button>
                                    <button type="button" class="ui primary button delete-ade-email">Eliminar</button>
                                </div>
                            </div>
                        </div>
                    </div>
                `);

                $("#additional-emails-container").append(group);

                // Rellena campos si hay datos
                if (exisAdditionalNotificationEmail.email) group.find(".ane_noti_email").val(exisAdditionalNotificationEmail.email);

                // Si viene con ID, lo guardamos como data-attribute
                if (exisAdditionalNotificationEmail.id) {
                    group.attr("data-aneemailid", exisAdditionalNotificationEmail.id);
                }

                group.on("click", ".save-ade-email", function () {
                    let aneEmailID = group.attr('data-aneemailid');

                    // Verificar correo electrónico requerido
                    const aneNotiEmail = group.find(".ane_noti_email").val().trim();
                    if (aneNotiEmail == '') {
                        group.find(".ane_noti_email").closest(".field").addClass("error");
                        Swal.fire({
                            icon: 'warning',
                            title: 'Un momento',
                            text: 'El campo "Correo electrónico de notificaciones adicional" es obligatorio.',
                            confirmButtonText: 'Aceptar',
                            confirmButtonColor: '#db2828'
                        });
                        return;
                    } else {
                        group.find(".ane_noti_email").closest(".field").removeClass("error");
                    }

                    // Validación del correo electrónico
                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    if (aneNotiEmail !== '' && !emailRegex.test(aneNotiEmail)) {
                        group.find(".ane_noti_email").closest(".field").addClass("error");
                        Swal.fire({
                            icon: 'warning',
                            title: 'Un momento',
                            text: 'El campo "Correo electrónico de notificaciones adicional" es incorrecto.',
                            confirmButtonText: 'Aceptar',
                            confirmButtonColor: '#db2828'
                        });
                        return;
                    } else {
                        group.find(".ane_noti_email").closest(".field").removeClass("error");
                    }

                    const data = {
                        ane_noti_email: aneNotiEmail,
                        ane_email_id: aneEmailID
                    };

                    loadingMain(true);
                    $('.save-ade-email').addClass('disabled');
                    $('.delete-ade-email').addClass('disabled');
                    $('#add-additional-noti-email').addClass('disabled');

                    $.ajax({
                        url: '/intermediario/poliza/{{ $id }}/datos_emision/upsert_additional_emails/save',
                        method: 'POST',
                        data: data,
                        headers: { 'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content') },
                        success: function (response) {
                            loadingMain(false);
                            $('.save-ade-email').removeClass('disabled');
                            $('.delete-ade-email').removeClass('disabled');
                            $('#add-additional-noti-email').removeClass('disabled');

                            if (!aneEmailID && response?.email?.id) {
                                group.attr('data-aneemailid', response?.email?.id);
                            }

                            Swal.fire({
                                icon: 'success',
                                title: '¡Guardado!',
                                text: 'El correo electrónico de notificaciones adicional sido guardada exitosamente.',
                                confirmButtonText: 'Aceptar',
                                confirmButtonColor: '#91C845'
                            });

                        },
                        error: function (xhr) {
                            loadingMain(false);
                            $('.save-ade-email').removeClass('disabled');
                            $('.delete-ade-email').removeClass('disabled');
                            $('#add-additional-noti-email').removeClass('disabled');

                            let errorMsg = 'Ocurrió un error al guardar el correo electrónico de notificaciones adicional.';

                            if (xhr.responseJSON && xhr.responseJSON.errors) {
                                // Extrae todos los mensajes de error y únelos en una lista
                                const errors = xhr.responseJSON.errors;
                                errorMsg = Object.values(errors).map(errArr => errArr.join('<br>')).join('<br>');
                            }

                            Swal.fire({
                                icon: 'error',
                                title: 'Error',
                                html: errorMsg,
                                confirmButtonText: 'Cerrar',
                                confirmButtonColor: '#db2828'
                            });
                        }
                    });
                });

                group.on("click", ".delete-ade-email", function () {
                    const groupContainer = $(this).closest(".noti-email-group");
                    const emailID = groupContainer.attr('data-aneemailid');

                    Swal.fire({
                        title: '¿Eliminar correo electrónico de notificaciones adicional?',
                        text: "Esta acción no se puede deshacer.",
                        icon: 'warning',
                        showCancelButton: true,
                        confirmButtonColor: '#d33',
                        cancelButtonColor: '#767676',
                        confirmButtonText: 'Sí, eliminar',
                        cancelButtonText: 'Cancelar'
                    }).then((result) => {
                        if (result.isConfirmed) {
                            if (emailID) {
                                loadingMain(true);
                                $('.save-ade-email').addClass('disabled');
                                $('.delete-ade-email').addClass('disabled');
                                $('#add-additional-noti-email').addClass('disabled');

                                // Si la dirección ya existe en base de datos
                                $.ajax({
                                    url: '/intermediario/poliza/{{ $id }}/datos_emision/additional_emails/delete/' + emailID,
                                    method: 'DELETE',
                                    headers: {
                                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                                    },
                                    success: function (response) {
                                        group.remove();

                                        if ($(".noti-email-group").length < 2) {
                                            $("#add-additional-noti-email").removeClass('disabled');
                                        }

                                        Swal.fire({
                                            icon: 'success',
                                            title: '¡Eliminado!',
                                            text: 'El teléfono ha sido eliminado.',
                                            confirmButtonColor: '#91C845'
                                        });

                                        loadingMain(false);
                                        $('.save-ade-email').removeClass('disabled');
                                        $('.delete-ade-email').removeClass('disabled');
                                        $('#add-additional-noti-email').removeClass('disabled');
                                    },
                                    error: function () {
                                        Swal.fire({
                                            icon: 'error',
                                            title: 'Error',
                                            text: 'No se pudo eliminar el correo electrónico de notificaciones adicional.',
                                            confirmButtonColor: '#db2828'
                                        });

                                        loadingMain(false);
                                        $('.save-ade-email').removeClass('disabled');
                                        $('.delete-ade-email').removeClass('disabled');
                                        $('#add-additional-noti-email').removeClass('disabled');
                                    }
                                });
                            } else {
                                // Si el teléfono no ha sido guardada aún
                                group.remove();
                                if ($(".noti-email-group").length < 2) {
                                    $("#add-additional-noti-email").removeClass('disabled');
                                }
                                Swal.fire({
                                    icon: 'success',
                                    title: 'Eliminado',
                                    text: 'El correo electrónico de notificaciones adicional fue eliminada del formulario.',
                                    confirmButtonColor: '#91C845'
                                });
                            }
                        }
                    });
                });

                if ($(".noti-email-group").length >= 2) {
                    $("#add-additional-noti-email").addClass('disabled');
                }
            }
        });

    </script>

    {{-- DIRECCIONES ADICIONALES --}}
    <script>
        $('.ui.modal').modal();
        $('.ui.dropdown').dropdown();

        const existingAddresses = @json($policy_addresses);

        $(document).ready(function () {
            let costarica = [];
            let locationIndex = 0;

            $.getJSON("/js/costarica.json", function (json) {
                costarica = json["province"];

                // Cargar direcciones existentes si hay
                if (Array.isArray(existingAddresses)) {
                    existingAddresses.forEach(addr => {
                        addLocationGroup(addr);
                    });
                }
            });

            $("#add-location").click(function () {
                addLocationGroup();
            });

            function addLocationGroup(exisAddress = {}) {
                const index = locationIndex++;

                const group = $(`
                    <div class="ui segment location-group" data-index="${index}" style="margin-bottom: 8px;">
                        <div class="ui form">
                            <div class="two fields">
                                <div class="field required">
                                    <label>Tipo de Dirección</label>
                                    <input list="address_types" type="text" class="addr_type">
                                </div>
                                <div class="field">
                                    <label>Dirección Completa</label>
                                    <input type="text" class="addr_address">
                                </div>
                            </div>
                            <div class="three fields">
                                <div class="field required">
                                    <label>Provincia</label>
                                    <div class="ui search selection dropdown addr_province">
                                        <input type="hidden" class="minus" name="address_province" value="">
                                        <i class="dropdown icon"></i>
                                        <div class="default text">Selecciona uno</div>
                                        <div class="menu"></div>
                                    </div>
                                </div>

                                <div class="field required">
                                    <label>Cantón</label>
                                    <div class="ui search selection dropdown addr_canton">
                                        <input type="hidden" class="minus" name="address_canton" value="">
                                        <i class="dropdown icon"></i>
                                        <div class="default text">Selecciona uno</div>
                                        <div class="menu"></div>
                                    </div>
                                </div>

                                <div class="field required">
                                    <label>Distrito</label>
                                    <div class="ui search selection dropdown addr_district">
                                        <input type="hidden" class="minus" name="address_district" value="">
                                        <i class="dropdown icon"></i>
                                        <div class="default text">Selecciona uno</div>
                                        <div class="menu"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="field">
                                <label>Señas del lugar</label>
                                <textarea class="addr_notes" rows="1"></textarea>
                            </div>
                            <button type="button" class="ui secondary button save-location">Guardar</button>
                            <button type="button" class="ui primary button delete-location">Eliminar</button>
                        </div>
                    </div>
                `);

                $("#locations-container").append(group);

                const $provinceAddr = group.find(".addr_province");
                const $cantonAddr = group.find(".addr_canton");
                const $districtAddr = group.find(".addr_district");

                populateDropdownAddr($provinceAddr, costarica);

                $provinceAddr.on('change', function () {
                    const value = $(this).dropdown("get value");
                    populateCantonsAddr($cantonAddr, $districtAddr, value);
                });

                $cantonAddr.on('change', function () {
                    const value = $(this).dropdown("get value");
                    const province = $provinceAddr.dropdown("get value");
                    populateDistrictsAddr($districtAddr, province, value);
                });

                $provinceAddr.dropdown();
                $cantonAddr.dropdown();
                $districtAddr.dropdown();

                // Rellena campos si hay datos
                if (exisAddress.type) group.find(".addr_type").val(exisAddress.type);
                if (exisAddress.full_address) group.find(".addr_address").val(exisAddress.full_address);
                if (exisAddress.province_id) {
                    $provinceAddr.dropdown("set selected", exisAddress.province_id);
                    populateCantonsAddr($cantonAddr, $districtAddr, exisAddress.province_id);

                    if (exisAddress.canton_id) {
                        $cantonAddr.dropdown("set selected", exisAddress.canton_id);
                        populateDistrictsAddr($districtAddr, exisAddress.province_id, exisAddress.canton_id);

                        if (exisAddress.district_id) {
                            $districtAddr.dropdown("set selected", exisAddress.district_id);
                        }
                    }
                }
                if (exisAddress.reference) group.find(".addr_notes").val(exisAddress.reference);

                // Si viene con ID, lo guardamos como data-attribute
                if (exisAddress.id) {
                    group.attr("data-addressid", exisAddress.id);
                }

                group.on("click", ".save-location", function () {
                    let addressID = group.attr('data-addressid');

                    if (group.find(".addr_type").val() == '') {
                        group.find(".addr_type").closest(".field").addClass("error");
                        Swal.fire({
                            icon: 'warning',
                            title: 'Un momento',
                            text: 'El campo "Tipo de Dirección" es obligatorio.',
                            confirmButtonText: 'Aceptar',
                            confirmButtonColor: '#db2828'
                        });
                        return;
                    } else {
                        group.find(".addr_type").closest(".field").removeClass("error");
                    }

                    if (group.find(".addr_address").val() == '') {
                        group.find(".addr_address").closest(".field").addClass("error");
                        Swal.fire({
                            icon: 'warning',
                            title: 'Un momento',
                            text: 'El campo "Dirección Completa" es obligatorio.',
                            confirmButtonText: 'Aceptar',
                            confirmButtonColor: '#db2828'
                        });
                        return;
                    } else {
                        group.find(".addr_address").closest(".field").removeClass("error");
                    }

                    if ($provinceAddr.dropdown("get value") == '') {
                        $provinceAddr.closest(".field").addClass("error");
                        Swal.fire({
                            icon: 'warning',
                            title: 'Un momento',
                            text: 'El campo "Provincia" es obligatorio.',
                            confirmButtonText: 'Aceptar',
                            confirmButtonColor: '#db2828'
                        });
                        return;
                    } else {
                        $provinceAddr.closest(".field").removeClass("error");
                    }

                    if ($cantonAddr.dropdown("get value") == '') {
                        $cantonAddr.closest(".field").addClass("error");
                        Swal.fire({
                            icon: 'warning',
                            title: 'Un momento',
                            text: 'El campo "Cantón" es obligatorio.',
                            confirmButtonText: 'Aceptar',
                            confirmButtonColor: '#db2828'
                        });
                        return;
                    } else {
                        $cantonAddr.closest(".field").removeClass("error");
                    }

                    if ($districtAddr.dropdown("get value") == '') {
                        $districtAddr.closest(".field").addClass("error");
                        Swal.fire({
                            icon: 'warning',
                            title: 'Un momento',
                            text: 'El campo "Distrito" es obligatorio.',
                            confirmButtonText: 'Aceptar',
                            confirmButtonColor: '#db2828'
                        });
                        return;
                    } else {
                        $districtAddr.closest(".field").removeClass("error");
                    }

                    const data = {
                        addr_tipo: group.find(".addr_type").val(),
                        addr_direccion: group.find(".addr_address").val(),
                        addr_provincia: $provinceAddr.dropdown("get value"),
                        addr_canton: $cantonAddr.dropdown("get value"),
                        addr_distrito: $districtAddr.dropdown("get value"),
                        addr_senas: group.find(".addr_notes").val(),
                        addr_id: addressID
                    };

                    loadingMain(true);
                    $('.save-location').addClass('disabled');
                    $('.delete-location').addClass('disabled');
                    $('#add-location').addClass('disabled');

                    $.ajax({
                        url: '/intermediario/poliza/{{ $id }}/datos_emision/upsert_additional_addresses/save',
                        method: 'POST',
                        data: data,
                        headers: { 'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content') },
                        success: function (response) {
                            loadingMain(false);
                            $('.save-location').removeClass('disabled');
                            $('.delete-location').removeClass('disabled');
                            $('#add-location').removeClass('disabled');

                            if (!addressID && response?.address?.id) {
                                group.attr('data-addressid', response?.address?.id);
                            }

                            Swal.fire({
                                icon: 'success',
                                title: '¡Guardado!',
                                text: 'La dirección ha sido guardada exitosamente.',
                                confirmButtonText: 'Aceptar',
                                confirmButtonColor: '#91C845'
                            });

                        },
                        error: function (xhr) {
                            loadingMain(false);
                            $('.save-location').removeClass('disabled');
                            $('.delete-location').removeClass('disabled');
                            $('#add-location').removeClass('disabled');

                            let errorMsg = 'Ocurrió un error al guardar la dirección.';

                            if (xhr.responseJSON && xhr.responseJSON.errors) {
                                // Extrae todos los mensajes de error y únelos en una lista
                                const errors = xhr.responseJSON.errors;
                                errorMsg = Object.values(errors).map(errArr => errArr.join('<br>')).join('<br>');
                            }

                            Swal.fire({
                                icon: 'error',
                                title: 'Error',
                                html: errorMsg,
                                confirmButtonText: 'Cerrar',
                                confirmButtonColor: '#db2828'
                            });
                        }
                    });
                });

                group.on("click", ".delete-location", function () {
                    const groupContainer = $(this).closest(".location-group");
                    const addressID = groupContainer.attr('data-addressid');

                    Swal.fire({
                        title: '¿Eliminar dirección?',
                        text: "Esta acción no se puede deshacer.",
                        icon: 'warning',
                        showCancelButton: true,
                        confirmButtonColor: '#d33',
                        cancelButtonColor: '#767676',
                        confirmButtonText: 'Sí, eliminar',
                        cancelButtonText: 'Cancelar'
                    }).then((result) => {
                        if (result.isConfirmed) {
                            if (addressID) {
                                loadingMain(true);
                                $('.save-location').addClass('disabled');
                                $('.delete-location').addClass('disabled');
                                $('#add-location').addClass('disabled');

                                // Si la dirección ya existe en base de datos
                                $.ajax({
                                    url: '/intermediario/poliza/{{ $id }}/datos_emision/upsert_additional_addresses/delete/' + addressID,
                                    method: 'DELETE',
                                    headers: {
                                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                                    },
                                    success: function (response) {
                                        group.remove();

                                        Swal.fire({
                                            icon: 'success',
                                            title: '¡Eliminado!',
                                            text: 'La dirección ha sido eliminada.',
                                            confirmButtonColor: '#91C845'
                                        });

                                        loadingMain(false);
                                        $('.save-location').removeClass('disabled');
                                        $('.delete-location').removeClass('disabled');
                                        $('#add-location').removeClass('disabled');
                                    },
                                    error: function () {
                                        Swal.fire({
                                            icon: 'error',
                                            title: 'Error',
                                            text: 'No se pudo eliminar la dirección.',
                                            confirmButtonColor: '#db2828'
                                        });

                                        loadingMain(false);
                                        $('.save-location').removeClass('disabled');
                                        $('.delete-location').removeClass('disabled');
                                        $('#add-location').removeClass('disabled');
                                    }
                                });
                            } else {
                                // Si la dirección no ha sido guardada aún
                                group.remove();
                                Swal.fire({
                                    icon: 'success',
                                    title: 'Eliminado',
                                    text: 'La dirección fue eliminada del formulario.',
                                    confirmButtonColor: '#91C845'
                                });
                            }
                        }
                    });
                });

            }

            function populateDropdownAddr(dropdown, items) {
                dropdown.dropdown("clear").find(".menu").empty();
                items.forEach(item => {
                    dropdown.find(".menu").append(`<div class="item" data-value="${item.code}">${capitalizeAddr(item.name)}</div>`);
                });
                dropdown.dropdown("refresh");
            }

            function populateCantonsAddr($cantonAddr, $districtAddr, provinceCode) {
                const province = costarica.find(p => p.code === provinceCode);
                if (province) {
                    populateDropdownAddr($cantonAddr, province.cantons);
                    const cantonCode = province.cantons[0]?.code;
                    if (cantonCode) {
                        $cantonAddr.dropdown("set selected", cantonCode);
                        populateDistrictsAddr($districtAddr, provinceCode, cantonCode);
                    }
                }
            }

            function populateDistrictsAddr($districtAddr, provinceCode, cantonCode) {
                const province = costarica.find(p => p.code === provinceCode);
                const canton = province?.cantons.find(c => c.code === cantonCode);
                if (canton) {
                    populateDropdownAddr($districtAddr, canton.districts);
                    const districtCode = canton.districts[0]?.code;
                    if (districtCode) {
                        $districtAddr.dropdown("set selected", districtCode);
                    }
                }
            }

            function capitalizeAddr(str) {
                const exceptions = ['de', 'y', 'la', 'el', 'los', 'las', 'un', 'una', 'por', 'para', 'en', 'con'];
                return str.split(' ').map((word, i) =>
                    i === 0 || !exceptions.includes(word.toLowerCase())
                        ? word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
                        : word.toLowerCase()
                ).join(' ');
            }
        });

    </script>
    
    {{-- TELEFONOS ADICIONALES --}}
    <script>
        const existingPhones = @json($policy_phones);

        $(document).ready(function () {
            let countries = [];
            let phoneIndex = 0;

            $.getJSON("/js/paises.json", function (json) {
                countries = json;

                // Cargar direcciones existentes si hay
                if (Array.isArray(existingPhones)) {
                    existingPhones.forEach(addr => {
                        addPhoneGroup(addr);
                    });
                }
            });

            $("#add-phone").click(function () {
                addPhoneGroup();
            });

            function addPhoneGroup(exisPhone = {}) {
                const index = phoneIndex++;

                const group = $(`
                    <div class="ui segment phone-group" data-index="${index}" style="margin-bottom: 8px;">
                        <div class="ui form">
                            <div class="four fields">
                                <div class="field required">
                                    <label>Tipo de teléfono</label>
                                    <input list="phone_types" type="text" class="phon_type">
                                </div>
                                <div class="field required">
                                    <label>Código de país</label>
                                    <div class="ui search selection dropdown phon_country">
                                        <input type="hidden" class="minus" name="phone_country" value="">
                                        <i class="dropdown icon"></i>
                                        <div class="default text">Selecciona uno</div>
                                        <div class="menu"></div>
                                    </div>
                                </div>
                                <div class="field required">
                                    <label>Número completo</label>
                                    <input type="text" class="phon_number" oninput="this.value = this.value.replace(/[^0-9]/g, '')">
                                </div>
                                <div class="field">
                                    <label>Extensión (opcional)</label>
                                    <input type="text" class="phon_extention" oninput="this.value = this.value.replace(/[^0-9]/g, '')">
                                </div>
                            </div>
                            <button type="button" class="ui secondary button save-phone">Guardar</button>
                            <button type="button" class="ui primary button delete-phone">Eliminar</button>
                        </div>
                    </div>
                `);

                $("#phones-container").append(group);

                const $countryPhon = group.find(".phon_country");

                populateDropdownPhon($countryPhon, countries);

                $countryPhon.dropdown();

                // Rellena campos si hay datos
                if (exisPhone.type) group.find(".phon_type").val(exisPhone.type);
                if (exisPhone.full_number) group.find(".phon_number").val(exisPhone.full_number);
                if (exisPhone.extention) group.find(".phon_extention").val(exisPhone.extention);
                if (exisPhone.country) $countryPhon.dropdown("set selected", exisPhone.country);

                // Si viene con ID, lo guardamos como data-attribute
                if (exisPhone.id) {
                    group.attr("data-phoneid", exisPhone.id);
                }

                group.on("click", ".save-phone", function () {
                    let phoneID = group.attr('data-phoneid');

                    if (group.find(".phone_type").val() == '') {
                        group.find(".phone_type").closest(".field").addClass("error");
                        Swal.fire({
                            icon: 'warning',
                            title: 'Un momento',
                            text: 'El campo "Tipo de teléfono" es obligatorio.',
                            confirmButtonText: 'Aceptar',
                            confirmButtonColor: '#db2828'
                        });
                        return;
                    } else {
                        group.find(".phone_type").closest(".field").removeClass("error");
                    }

                    if (group.find(".phon_number").val() == '') {
                        group.find(".phon_number").closest(".field").addClass("error");
                        Swal.fire({
                            icon: 'warning',
                            title: 'Un momento',
                            text: 'El campo "Número completo" es obligatorio.',
                            confirmButtonText: 'Aceptar',
                            confirmButtonColor: '#db2828'
                        });
                        return;
                    } else {
                        group.find(".phon_number").closest(".field").removeClass("error");
                    }

                    if ($countryPhon.dropdown("get value") == '') {
                        $countryPhon.closest(".field").addClass("error");
                        Swal.fire({
                            icon: 'warning',
                            title: 'Un momento',
                            text: 'El campo "Código de país" es obligatorio.',
                            confirmButtonText: 'Aceptar',
                            confirmButtonColor: '#db2828'
                        });
                        return;
                    } else {
                        $countryPhon.closest(".field").removeClass("error");
                    }

                    const data = {
                        phon_type: group.find(".phon_type").val(),
                        phon_number: group.find(".phon_number").val(),
                        phon_country: $countryPhon.dropdown("get value"),
                        phon_extention: group.find(".phon_extention").val(),
                        phon_id: phoneID
                    };

                    loadingMain(true);
                    $('.save-phone').addClass('disabled');
                    $('.delete-phone').addClass('disabled');
                    $('#add-phone').addClass('disabled');

                    $.ajax({
                        url: '/intermediario/poliza/{{ $id }}/datos_emision/upsert_additional_phones/save',
                        method: 'POST',
                        data: data,
                        headers: { 'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content') },
                        success: function (response) {
                            loadingMain(false);
                            $('.save-phone').removeClass('disabled');
                            $('.delete-phone').removeClass('disabled');
                            $('#add-phone').removeClass('disabled');

                            if (!phoneID && response?.phone?.id) {
                                group.attr('data-phoneid', response?.phone?.id);
                            }

                            Swal.fire({
                                icon: 'success',
                                title: '¡Guardado!',
                                text: 'El teléfono ha sido guardada exitosamente.',
                                confirmButtonText: 'Aceptar',
                                confirmButtonColor: '#91C845'
                            });

                        },
                        error: function (xhr) {
                            loadingMain(false);
                            $('.save-phone').removeClass('disabled');
                            $('.delete-phone').removeClass('disabled');
                            $('#add-phone').removeClass('disabled');

                            let errorMsg = 'Ocurrió un error al guardar el teléfono.';

                            if (xhr.responseJSON && xhr.responseJSON.errors) {
                                // Extrae todos los mensajes de error y únelos en una lista
                                const errors = xhr.responseJSON.errors;
                                errorMsg = Object.values(errors).map(errArr => errArr.join('<br>')).join('<br>');
                            }

                            Swal.fire({
                                icon: 'error',
                                title: 'Error',
                                html: errorMsg,
                                confirmButtonText: 'Cerrar',
                                confirmButtonColor: '#db2828'
                            });
                        }
                    });
                });

                group.on("click", ".delete-phone", function () {
                    const groupContainer = $(this).closest(".phone-group");
                    const phoneID = groupContainer.attr('data-phoneid');

                    Swal.fire({
                        title: '¿Eliminar teléfono?',
                        text: "Esta acción no se puede deshacer.",
                        icon: 'warning',
                        showCancelButton: true,
                        confirmButtonColor: '#d33',
                        cancelButtonColor: '#767676',
                        confirmButtonText: 'Sí, eliminar',
                        cancelButtonText: 'Cancelar'
                    }).then((result) => {
                        if (result.isConfirmed) {
                            if (phoneID) {
                                loadingMain(true);
                                $('.save-phone').addClass('disabled');
                                $('.delete-phone').addClass('disabled');
                                $('#add-phone').addClass('disabled');

                                // Si la dirección ya existe en base de datos
                                $.ajax({
                                    url: '/intermediario/poliza/{{ $id }}/datos_emision/additional_phones/delete/' + phoneID,
                                    method: 'DELETE',
                                    headers: {
                                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                                    },
                                    success: function (response) {
                                        group.remove();

                                        Swal.fire({
                                            icon: 'success',
                                            title: '¡Eliminado!',
                                            text: 'El teléfono ha sido eliminado.',
                                            confirmButtonColor: '#91C845'
                                        });

                                        loadingMain(false);
                                        $('.save-phone').removeClass('disabled');
                                        $('.delete-phone').removeClass('disabled');
                                        $('#add-phone').removeClass('disabled');
                                    },
                                    error: function () {
                                        Swal.fire({
                                            icon: 'error',
                                            title: 'Error',
                                            text: 'No se pudo eliminar el teléfono.',
                                            confirmButtonColor: '#db2828'
                                        });

                                        loadingMain(false);
                                        $('.save-phone').removeClass('disabled');
                                        $('.delete-phone').removeClass('disabled');
                                        $('#add-phone').removeClass('disabled');
                                    }
                                });
                            } else {
                                // Si el teléfono no ha sido guardada aún
                                group.remove();
                                Swal.fire({
                                    icon: 'success',
                                    title: 'Eliminado',
                                    text: 'El teléfono fue eliminada del formulario.',
                                    confirmButtonColor: '#91C845'
                                });
                            }
                        }
                    });
                });

            }

            function populateDropdownPhon(dropdown, paises) {
                dropdown.dropdown("clear").find(".menu").empty();
                paises.forEach(pais => {
                    dropdown.find(".menu").append(`
                        <div class="item" data-value="${pais.country_short_name}">
                            <i class="${pais.country_short_name.toLowerCase()} flag"></i> ${pais.country_name} (${pais.country_short_name})
                        </div>
                    `);
                });
                dropdown.dropdown("refresh");
            }
        });

    </script>

    <script>
        let occupationsData = [];
        $(document).ready(function () {

            $('.ui.dropdown').dropdown();

            $('.custom-submenu .item').on('click', function () {
                const selectedValue = $(this).data('value');
                const selectedText = $(this).text().trim();

                // Llenamos el input hidden con el valor seleccionado
                $('#occupancy_group').val(selectedValue);
                $('#occupancy_group_texto').val(selectedText);

                const $ocupaciones = $('#occupations_category_id');
                $ocupaciones.dropdown('clear');
                $ocupaciones.empty();
                $ocupaciones.append('<option value="">Selecciona una ocupación</option>');
                $ocupaciones.dropdown('refresh');

                cargarOcupaciones(selectedValue);

            });

            $('#occupations_category_id').dropdown();


            function cargarOcupaciones(grupoId, inicial = false) {
                loadingMain(true);

                const occupationCategoriesFiltered = occupationCategories.filter((occupationCategory) => occupationCategory?.occupation_id == grupoId)

                const normalized = occupationCategoriesFiltered.map(function(item) {
                    item.id = item.id.toString();
                    return item;
                });

                initializeOccupationSearch(normalized, inicial);

                loadingMain(false);
            }

            const grupoPreseleccionado = @json($affiliate->occupancy_group ?? null);

            if (grupoPreseleccionado) {
                cargarOcupaciones(grupoPreseleccionado, true);
            }

        });

        function initializeOccupationSearch(json, inicial = false) {

            if (!json) return;

            json = json.map(function(item) {
                item.id = item.id.toString();
                return item;
            });

            occupationsData = json;

            const menu = $('#occupationsDropdown .menu');
            menu.empty();

            json.forEach(function(item) {
                const codigo = item.id.toString();
                const nombre = (item.name).trim();

                const option = $('<div>', {
                    class: 'item',
                    'data-value': codigo,
                    'data-text': `${nombre}`,
                    text: `${nombre}`
                });

                menu.append(option);
            });


            $('#occupationsDropdown').dropdown('refresh');

            $('#occupationsDropdown').dropdown({
                fullTextSearch: true,
                onChange: function(value, text, $selectedItem) {
                    if (value) {

                        const selectedOccupation = occupationsData.find(function(item) {
                            return item.id === value;
                        });

                        if (selectedOccupation && selectedOccupation?.name) {
                            // Si el tipo de identificación es diferente a CEDULA JUDIRICA
                            // El representante legal toma la misma ocupación del tomador
                            if ($('#docType').val() !== 'CJ') {
                                // sincronizar el grupo ocupacional
                                $('#occupationsGroupDropdownRL').dropdown('set selected', selectedOccupation?.occupation_id);
                                // sincronizar la ocupación
                                cargarOcupacionesRl(selectedOccupation?.occupation_id);
                                $('#occupationsDropdownRL').dropdown('set selected', selectedOccupation?.id);
                            }

                            // Establecer el nombre de la
                            $('#ocupacion_tomador').val(selectedOccupation?.name);

                        } else {
                            $('#ocupacion_tomador').val('');
                        }
                    } else {
                        $('#ocupacion_tomador').val('');
                    }
                }
            });

            if (inicial) {
                const ocupacionId = @json($affiliate->occupations_category_id ?? null);
                $('#occupations_category_id').val(ocupacionId);
                $('#occupationsDropdown').dropdown('set selected', ocupacionId);
            }

        }

    </script>

    {{-- REPRESENTANTE LEGAL --}}
    <script>
        let occupationsDataRL = [];
        $(document).ready(function () {

            $('.ui.dropdown').dropdown();

            $('.custom-submenu-rl .item').on('click', function () {
                const selectedValue = $(this).data('value');
                const selectedText = $(this).text().trim();

                // Llenamos el input hidden con el valor seleccionado
                $('#legal_representative_occupancy_group_id').val(selectedValue);
                $('#legal_representative_occupancy_group_texto').val(selectedText);

                const $ocupacionesRl = $('#legal_representative_occupations_category_id');
                $ocupacionesRl.dropdown('clear');
                $ocupacionesRl.empty();
                $ocupacionesRl.append('<option value="">Selecciona una ocupación</option>');
                $ocupacionesRl.dropdown('refresh');

                cargarOcupacionesRl(selectedValue);

            });

            $('#legal_representative_occupations_category_id').dropdown();

            const grupoPreseleccionadoRL = @json($policy_sort->legal_representative_occupancy_group_id ?? null);

            if (grupoPreseleccionadoRL) {
                cargarOcupacionesRl(grupoPreseleccionadoRL, true);
            }

        });

        function cargarOcupacionesRl(grupoId, inicial = false) {
            const occupationCategoriesFilteredRL = occupationCategories.filter((occupationCategory) => occupationCategory?.occupation_id == grupoId);

            const normalized = occupationCategoriesFilteredRL.map(function(item) {
                item.id = item.id.toString();
                return item;
            });

            initializeOccupationSearchRl(normalized, inicial);
        }

        function initializeOccupationSearchRl(json, inicial = false) {

            if (!json) return;

            json = json.map(function(item) {
                item.id = item.id.toString();
                return item;
            });

            occupationsDataRL = json;

            const menu = $('#occupationsDropdownRL .menu');
            menu.empty();

            json.forEach(function(item) {
                const codigo = item.id.toString();
                const nombre = (item.name).trim();

                const option = $('<div>', {
                    class: 'item',
                    'data-value': codigo,
                    'data-text': `${nombre}`,
                    text: `${nombre}`
                });

                menu.append(option);
            });


            $('#occupationsDropdownRL').dropdown('refresh');

            $('#occupationsDropdownRL').dropdown({
                fullTextSearch: true,
                onChange: function(value, text, $selectedItem) {
                    if (value) {

                        const selectedOccupationsDataRL = occupationsDataRL.find(function(item) {
                            return item.id === value;
                        });

                        if (selectedOccupationsDataRL && selectedOccupationsDataRL.name) {
                            $('#legalRepresentativeProfession').val(selectedOccupationsDataRL.name);
                        } else {
                            $('#legalRepresentativeProfession').val('');
                        }
                    } else {
                        $('#legalRepresentativeProfession').val('');
                    }
                }
            });

            if (inicial) {
                const ocupacionIdRL = @json($policy_sort->legal_representative_occupations_category_id ?? null);
                $('#legal_representative_occupations_category_id').val(ocupacionIdRL);
                $('#occupationsDropdownRL').dropdown('set selected', ocupacionIdRL);
            }

        }
    </script>

@endsection
