@extends('layouts.main')

@section('title', 'Vista del Intermediario - Poliza')

@section('menu')
    @parent
@endsection

@section('content')
    @include('services/policy_sort/intermediary_policy/menu/menu', ['active' => 'prima_emision_policy'])

    <form class="ui attached form container" id="form_prima_emision_policy"
        action="{{ secure_url('/intermediario/poliza/' . $id . '/prima_emision_policy') }}" method="post"
        style="padding-bottom: 40px;">
        {{ csrf_field() }}

        <input type="hidden" name="validity_from_new">
        <input type="hidden" name="validity_to_new">

        <div class="ui basic container segment" id="policyholder">
            <div class="ui styled fluid accordion">
                <div class="title">
                    <i class="dropdown icon"></i>
                    Periodo calendario
                </div>
                <div class=" content">
                    <div class="grouped fields">
                        <div class="field">
                            <div class="ui radio checkbox">
                                <input type="radio" name="period" id="ordinary" value="1">
                                <label for="ordinary">Ordinario</label>
                            </div>
                        </div>
                        <div class="field">
                            <div class="ui radio checkbox"
                                data-content="Aplica cuando los períodos de cierre de planillas difieren del calendario mensual habitual, por lo que debe incluirse las fechas de inicio y fin de cada período de planilla.">
                                <input type="radio" name="period" id="extraordinary" value="2">
                                <label for="extraordinary">Calendario especial</label>
                            </div>
                        </div>
                    </div>
                    <div class="container">
                        <div id="accordionExtraordinario" style="display: none">
                            <div id="fieldContainer">
                                @if ($policy_calendar->isNotEmpty())

                                    @foreach ($policy_calendar as $index => $item)
                                        <div class="four fields dynamic-field">
                                            <div class="required field">
                                                <label for="start_Date">Fecha de inicio</label>
                                                <input id="start_date_{{ $index }}" type="text"
                                                    name="start_Date[]" class="datepicker"
                                                    value="{{ $item->start_date ? \Carbon\Carbon::parse($item->start_date)->format('d/m/Y') : '' }}"
                                                    placeholder="dd/mm/yyyy">
                                            </div>

                                            <div class="required field">
                                                <label for="end_Date">Fecha de finalización</label>
                                                <input id="end_date_{{ $index }}" type="text" name="end_Date[]"
                                                    class="datepicker"
                                                    value="{{ $item->end_date ? \Carbon\Carbon::parse($item->end_date)->format('d/m/Y') : '' }}"
                                                    placeholder="dd/mm/yyyy">
                                            </div>

                                            <div class="required field">
                                                <label>Número de períodos</label>
                                                <div class="two fields">
                                                    <div class="field">
                                                        <input id="number_of_periods_{{ $index }}"
                                                            name="number_of_periods[]" class="fourteen wide readonly-bg"
                                                            type="text" value="{{ $item->number_period }}">
                                                    </div>
                                                    <div class="field">
                                                        @if ($index === 0)
                                                            <!-- Solo mostrar el botón "+" en la primera posición -->
                                                            <button type="button" id="increase_periods"
                                                                class="ui button secondary">+</button>
                                                            <button type="button" class="remove-field-btn ui button primary"
                                                                style="display:none">-</button>
                                                        @endif
                                                        @if ($index !== 0)
                                                            <button type="button"
                                                                class="remove-field-btn ui button primary">-</button>
                                                        @endif
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                @else
                                    <!-- Mostrar un campo vacío por defecto si no hay registros -->
                                    <div class="four fields dynamic-field">
                                        <div class="required field">
                                            <label for="start_Date">Fecha de inicio</label>
                                            <input id="start_date_0" type="text" name="start_Date[]" class="datepicker"
                                                placeholder="dd/mm/yyyy">
                                        </div>

                                        <div class="required field">
                                            <label for="end_Date">Fecha de finalización</label>
                                            <input id="end_date_0" type="text" name="end_Date[]" class="datepicker"
                                                placeholder="dd/mm/yyyy">
                                        </div>

                                        <div class="required field">
                                            <label>Número de períodos</label>
                                            <div class="two fields">
                                                <div class="field">
                                                    <input id="number_of_periods_0" name="number_of_periods[]"
                                                        class="fourteen wide readonly-bg" type="text" value="1"
                                                        readonly>
                                                </div>
                                                <div class="field">
                                                    <button type="button" id="increase_periods"
                                                        class="ui button secondary">+</button>
                                                    <button type="button" class="remove-field-btn ui button primary"
                                                        style="display: none;">-</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                @endif

                            </div>
                        </div>
                    </div>
                    <div class="field"></div>
                </div>

                <div class=" title">
                    <i class="dropdown icon"></i>
                    Datos emisión póliza
                </div>
                <div class=" content">
                    <div class="three fields">
                        <div class="required field heir_payment-class" id="heir_doc_type">

                            <div class="ui form">
                                <div class="required field">
                                    <label>Temporalidad</label>
                                </div>
                                <div class="inline fields" id="div_temporality">
                                    <div class="field">
                                        <div class="ui radio checkbox">
                                            <input type="radio" name="temporality" value="permanent" tabindex="0">
                                            <label>Permanente</label>
                                        </div>
                                    </div>
                                    <div class="field">
                                        <div class="ui radio checkbox">
                                            <input type="radio" name="temporality" value="short" tabindex="1">
                                            <label>Periodo corto</label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>

                        <div class="required field">
                            <label for="validity_from">Vigencia de la póliza desde</label>
                            <input id="validity_from" type="text" name="validity_from" class="datepicker"
                                value="{{ $data->validity_from }}" placeholder="dd/mm/yyyy">
                        </div>

                        <div class="required field">
                            <label for="validity_to">Vigencia de la póliza hasta</label>
                            <input id="validity_to" type="text" name="validity_to" class="datepicker"
                                placeholder="dd/mm/yyyy" value="{{ $data->validity_to }}">
                        </div>

                    </div>

                    <div class="three fields">
                        <div class="field">
                            <label for="moneytype">Tipo de moneda</label>
                            <input class="prompt readonly" name="money_type" type="text" autocomplete="off"
                                value="{{ isset($MONEY_TYPE[$data->type_currency]) ? $MONEY_TYPE[$data->type_currency]['symbol'] . ' ' . mb_convert_case($MONEY_TYPE[$data->type_currency]['name'], MB_CASE_TITLE, 'UTF-8') : '' }}"
                                style="background: rgba(0, 0, 0, .05);" readonly>
                        </div>
                        @if ($data->work_modality_id != 3 && $data->work_modality_id != 4)
                            <div class="required field">
                                <label for="salary_projection" id="label_salary_projection">
                                    Planilla mensual
                                </label>
                                <input class="prompt " id="salary_projection" type="text" name="salary_projection"
                                    value="{{ $data->work_modality_id == 1 ? number_format($data->sumTotalAffiliatesWithReport($data->id) != 0 ? $data->sumTotalAffiliatesWithReport($data->id) : $data->salary_projection, 2, ',', '.') : number_format($data->sumTotalAffiliatesWithReport($data->id), 2, ',', '.') }}">
                            </div>
                            @php
    $salary_estimate =
        $data->work_modality_id == 1
        ? ($data->sumTotalAffiliatesWithReport($data->id) != 0
            ? $data->sumTotalAffiliatesWithReport($data->id) * 12
            : $data->salary_projection * 12)
        : $data->sumTotalAffiliatesWithReport($data->id);
                            @endphp
                            <div class="required field" id="salary_estimate_field" style="display: none;">
                                <label for="salary_estimate">Estimación anual de salarios</label>
                                <input class="prompt" id="salary_estimate" type="text" name="salary_estimate"
                                    autocomplete="off" readonly style="background: rgba(0, 0, 0, .05);">
                            </div>
                        @endif

                    </div>

                    <div class="three fields">
                        @if ($number_workers_spreadsheet != 'No hay trabajadores')
                            <div class="field">
                                <label>Número de trabajadores<i class="info circle icon pop"
                                        data-title="Número de trabajadores"
                                        data-content="Es el número de trabajadores cargados en planilla"></i></label>
                                <input class="prompt" type="text" name="number_workers"
                                    value="{{ $number_workers_spreadsheet }}" readonly
                                    style="background: rgba(0, 0, 0, .05);">
                            </div>
                        @else


                            @php
                                $workers = !empty($number_workers) ? $number_workers : '';
                                $workers = in_array($data->work_modality_id, [6, 7]) ? '1' : $workers;
                            @endphp
                            <div class="required field">
                                <label>Número de trabajadores <i class="info circle icon pop" data-title="Número de trabajadores"
                                        data-content="Es un “campo numérico no mayor a 7 dígitos”."></i></label>
                                <input class="prompt" id="number_workers" type="text" name="number_workers" autocomplete="off"  {{ in_array($data->work_modality_id, [6, 7]) ? 'readonly' : '' }}
                                    value="{{ $workers }}">
                            </div>
                        @endif

                    </div>

                    <div class="three fields">
                        @if ($data->special_condition)

                            @if ($data->special_condition == 2 || $data->special_condition == 3)
                                <div class="required field" id="">
                                    <label for="salary_estimate">Descuento (%)</label>
                                    <input readonly class="prompt" id="" type="text" name=""
                                        style="background: rgba(0, 0, 0, .05);" autocomplete="off"
                                        value="{{ number_format($data->preventive_actions, 2, ',', '.') }}">
                                </div>
                            @endif

                            @if ($data->special_condition_payment == 'Si' && $data->temporality != 'short')
                                <div class="required field" id="">
                                    <label for="salary_estimate">Exclusión de refraccionamiento</label>
                                    <input readonly class="prompt" id="" type="text" name=""
                                        style="background: rgba(0, 0, 0, .05);" autocomplete="off" value="Si">
                                </div>
                            @endif
                        @endif
                    </div>
                </div>

            </div>
            <br>
            <div style="display:flex;justify-content: space-between">
                <div>
                    <a class="ui button primary" href="{{ url('/intermediario/poliza/' . $id . '/informe_planilla') }}"
                        style="margin-right: 2rem;">
                        <i class="left chevron icon"></i> Atrás
                    </a>
                </div>
                <button type="submit" class="ui button primary" id="submit_policy" name="action" value="recalculate">
                    Siguiente
                </button>
            </div>
        </div>
    </form>
    <style>
        .readonly-bg {
            background: rgba(0, 0, 0, .05) !important;
        }
    </style>
    <!-- Popups -->
    <script>
        $('.pop').popup({
            boundary: 'body', // Esto permite que el popup se muestre fuera de los límites de la tabla
        });
    </script>
    <script>
        $(document).ready(function() {

            const temporalityPolicy = @json($data->temporality);

            const temporalityQuotation = @json($quotation->temporality);

            const work_modality_id = @json($data->work_modality_id)

            if (work_modality_id == 3 || work_modality_id == 4) {
                $('input[name="temporality"][value="permanent"]').prop('checked', true);
                $('#validity_to').prop('disabled', true);
                $('#div_temporality').addClass('disabled')

            }



            // Evento para mostrar/ocultar el campo basado en la selección
            $('input[name="temporality"]').on('change', function() {
                if ($(this).val() === 'permanent') {
                    $('#salary_estimate_field').show();
                    $("#label_salary_projection").text("Planilla mensual");
                } else {
                    $('#salary_estimate_field').hide();
                    $("#label_salary_projection").text("Estimación total de salario o contrato");
                }
            });

            configureInputMask('#salary_estimate', {
                symbol: policy.type_currency ? currencyTypes[policy.type_currency]['symbol'] : ''
            });


            $('#salary_projection').on('input', function() {
                const current = $(this).val();

                // Remueve símbolos de colones y dólares, así como puntos de miles, y convierte la coma decimal a punto
                let numericValue = current.replace(/[$₡]/g, '').replace(/\./g, '').replace(',', '.');



                const input = document.getElementById('validity_from');
                const input2 = document.getElementById('validity_to');
                let dateValue = input.value; // Obtener el valor del input de la fecha "validity_from"
                let dateValue2 = input2.value; // Obtener el valor del input de la fecha "validity_to"

                let dataFormoment = moment(dateValue, 'dddd D [de] MMMM [de] YYYY');

                let diffDaysss = moment(dateValue2, 'dddd D [de] MMMM [de] YYYY').diff(dataFormoment,
                    'days');


                if (diffDaysss < 364) {
                    calcularProporcional(numericValue * 12);
                } else {

                    // Multiplica el valor por 12
                    let result = parseFloat(numericValue) * 12;

                    // Configura el símbolo de la moneda según el tipo (colones o dólares) y el valor de result con el formato correcto
                    $('#salary_estimate').val(result.toLocaleString('es-CR', {
                        style: 'currency',
                        currency: policy.type_currency === 'CRC' ? 'CRC' : 'USD'
                    }));

                    // Configura la máscara de entrada
                    configureInputMask('#salary_estimate', {
                        symbol: policy.type_currency ? currencyTypes[policy.type_currency][
                            'symbol'
                        ] : ''
                    });
                }


            });


            if (temporalityPolicy == null) {
                if (temporalityQuotation == 'permanent') {
                    $('input[name="temporality"][value="permanent"]').prop('checked', true);
                } else {
                    $('input[name="temporality"][value="short"]').prop('checked', true);
                }
            } else {
                if (temporalityPolicy == 'permanent') {
                    $('input[name="temporality"][value="permanent"]').prop('checked', true);
                    $('#salary_estimate_field').show();

                } else {
                    $('input[name="temporality"][value="short"]').prop('checked', true);
                }
            }
        });
    </script>
    <script>
        $('#form_prima_emision_policy').submit(function(e) {
            // Tomar los valores de los campos
            var validityFrom = $('#validity_from').val().trim();
            var validityTo = $('#validity_to').val().trim();

            // Arrays con los nombres de los meses en español
            var months = ['enero', 'febrero', 'marzo', 'abril', 'mayo', 'junio', 'julio', 'agosto', 'septiembre',
                'octubre', 'noviembre', 'diciembre'
            ];

            // Extraer las partes de la fecha usando split (asumiendo que siempre está en este formato)
            var dateParts = validityFrom.split(' ');
            var dateParts2 = validityTo.split(' ');

            // Obtener el día, mes (convertido a número) y año
            var day = parseInt(dateParts[1], 10);
            var month = months.indexOf(dateParts[3]) + 1;
            var year = parseInt(dateParts[5], 10);

            var day2 = parseInt(dateParts2[1], 10);
            var month2 = months.indexOf(dateParts2[3]) + 1;
            var year2 = parseInt(dateParts2[5], 10);

            // Crear un objeto Moment con las partes extraídas
            var formattedDateFrom = moment(`${year}-${month}-${day}`, 'YYYY-M-D').format('DD/MM/YYYY');
            var formattedDate = moment(`${year2}-${month2}-${day2}`, 'YYYY-M-D').format('DD/MM/YYYY');

            $('input[name="validity_from_new"]').val(formattedDateFrom.includes('-') ? formattedDateFrom : moment(
                formattedDateFrom, 'DD/MM/YYYY').format('YYYY-MM-DD'));
            $('input[name="validity_to_new"]').val(formattedDate.includes('-') ? formattedDate : moment(
                formattedDate, 'DD/MM/YYYY').format('YYYY-MM-DD'));
        });

        $(document).ready(function() {
            $('#form_prima_emision_policy').form({
                fields: {
                    resultado: {
                        identifier: 'workRisk',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor selecciona un resultado'
                        }]
                    }
                }
            });
        });
    </script>


    <script>
        const policy = @json($data);

        const currencyTypes = @json($MONEY_TYPE);
        const symbol = policy.type_currency ? currencyTypes[policy.type_currency]['symbol'] : '';
        configureInputMask('#salary_projection', {
            symbol
        });
        configureInputMask('#monthly_calculation_amount', {
            symbol
        });
        configureInputMask('#semiannual_calculation_amount', {
            symbol
        });
        configureInputMask('#quarterly_calculation_amount', {
            symbol
        });
        configureInputMask('#annual_calculation_amount', {
            symbol
        });
        configureInputMask('#singlePayment', {
            symbol
        });

        $('#type_currency').change(function() {
            const current = $(this).val();
            configureInputMask('#salary_projection', {
                symbol: currencyTypes[current]['symbol']
            });
        });




        $(document).ready(function() {

            $('.ui.dropdown').dropdown();
            $('.ui.accordion').accordion({
                exclusive: false
            });
            $('.ui.accordion .title').each(function() {
                $(this).addClass('active');
            });
            $('.ui.accordion .content').each(function() {
                $(this).addClass('active');
            });

            $('.ui.radio.checkbox').checkbox();
            $('.ui.radio.checkbox').popup();

            var periodicity = "{{ $data->calendar_period }}";

            if (periodicity === '1') {
                $('#ordinary').prop('checked', true);
                $('#accordionExtraordinario').hide();
                $('#accordionOrdinario').show();
            } else if (periodicity === '2') {
                $('#extraordinary').prop('checked', true);
                $('#accordionOrdinario').hide();
                $('#accordionExtraordinario').show();
            }

            $('.ui.radio.checkbox').checkbox();

            //marca modalidad de aseguramiento
            var selectedModality = "{{ $data->insurance_modality }}";
            if (selectedModality) {
                $('input[name="workRisk"][value="' + selectedModality + '"]').prop('checked', true);
                $('input[name="workRisk"][value="' + selectedModality + '"]').parent().checkbox(
                    'check'); // Si estás usando Semantic UI
            }


            var permanentFields = $('#permanent-section input');
            var shortFields = $('#short-section input');


            shortFields.prop('disabled', true); // Deshabilitar campos de periodo corto
            permanentFields.prop('disabled', true); // Habilitar campos de periodo corto

        });


        var validaPrima = @json($valida_prima);

        if (validaPrima) {
            Swal.fire({
                icon: "error",
                title: "Error",
                text: "La prima a pagar no puede ser inferior a 100 dolares o equivalente a colones ",
            });
        }
    </script>

    <script>
        //documents:
        $(document).ready(function() {

            $('input[name="period"]').on('change', function() {
                if ($(this).val() === '1') { // Valor para "Ordinario"
                    $('#accordionExtraordinario').hide();
                }
                if ($(this).val() === '2') { // Valor para "Extraordinario"
                    $('#accordionExtraordinario').show();
                }
            });

            // Opcional: ocultar el campo al cargar la página si "Ordinario" está seleccionado
            if ($('#ordinary').is(':checked')) {
                $('#accordionExtraordinario').hide();
            }

        });
    </script>

    <script>
        //datapicker
        $(document).ready(function() {

            //Fecha vigencia desde
            const validityfrom = $('#validity_from');

            //Fecha de vigencia hasta
            const validityto = $('#validity_to');

            //Instancia fecha de vigencia desde
            validityfrom.pickadate({
                selectYears: 100,
                selectMonths: true,
                min: new Date(),
                max: new Date(new Date().setFullYear(new Date().getFullYear() + 1) - 2 * 24 * 60 * 60 *
                    1000), // Restar 2 días
                formatSubmit: 'yyyy-mm-dd',
                format: 'dd/mm/yyyy',
                onSet: function(context) {
                    if (context.select) {
                        // Cierra el picker
                        this.close();

                        // Obtener la fecha seleccionada
                        let selectedDate = new Date(context.select);

                        // Dividir la fecha seleccionada en partes
                        var day = selectedDate.getDate();
                        var month = selectedDate.getMonth();
                        var year = selectedDate.getFullYear();

                        // Array con los nombres de los días de la semana
                        var days = ['Domingo', 'Lunes', 'Martes', 'Miércoles', 'Jueves', 'Viernes',
                            'Sábado'
                        ];
                        // Array con los nombres de los meses
                        var months = ['enero', 'febrero', 'marzo', 'abril', 'mayo', 'junio', 'julio',
                            'agosto', 'septiembre', 'octubre', 'noviembre', 'diciembre'
                        ];


                        // Formatear la fecha
                        var formattedDate = days[selectedDate.getDay()] + ' ' + day + ' de ' + months[
                            month] + ' de ' + year;

                        formattedDate = formattedDate.charAt(0).toUpperCase() + formattedDate.slice(1);

                        // Asignar la fecha formateada al campo de entrada
                        $(this.$node).val(formattedDate); // Cambia el valor del campo de entrada actual

                    }
                }
            }).pickadate('picker');

            var hora = $('#validity_from').pickadate('picker');
            var selectedDate = hora.get('select'); // Obtiene un objeto con la fecha seleccionada

            var dateObject = new Date(selectedDate.year, selectedDate.month, selectedDate.date);

            //Instancia fecha vigencia hasta
            // Al iniciar, guardamos el valor actual (si lo tiene) en un atributo de datos
            validityto.data('prevDate', validityto.val());

            validityto.pickadate({
                selectYears: 100,
                selectMonths: true,
                min: dateObject,
                max: new Date(dateObject.getFullYear() + 1, dateObject.getMonth(), dateObject.getDate() -
                    1),
                formatSubmit: 'yyyy-mm-dd',
                format: 'dd/mm/yyyy',
                onSet: function(context) {
                    const picker = this;
                    if (context.select) {
                        // Al entrar aquí, se ha seleccionado una nueva fecha
                        // Obtenemos el valor anterior guardado
                        var prevDate = $(picker.$node).data('prevDate') || '';

                        // Si ya había una fecha establecida, mostramos la alerta de confirmación
                        if (prevDate !== '') {
                            Swal.fire({
                                title: '¿Está seguro?',
                                text: "Ya hay una fecha seleccionada. ¿Desea cambiarla?",
                                icon: 'warning',
                                showCancelButton: true,
                                confirmButtonColor: '#3085d6',
                                cancelButtonColor: '#d33',
                                confirmButtonText: 'Sí, cambiarla',
                                cancelButtonText: 'Cancelar'
                            }).then((result) => {
                                if (result.isConfirmed) {
                                    // Se confirma el cambio: procesamos la nueva fecha y actualizamos el valor previo
                                    processNewDate(context, picker);
                                    $(picker.$node).data('prevDate', $(picker.$node).val());


                                    let fechaFin = moment($(picker.$node).val(),
                                        'dddd D [de] MMMM [de] YYYY');

                                    let diffDaysss = fechaFin.diff(dateObject,
                                        'days');

                                    let valueSalarie = $('#salary_projection').val();
                                    let numericValue = valueSalarie.replace(/[$₡]/g, '')
                                        .replace(/\./g, '').replace(',', '.');


                                    const valorTotal = numericValue * 12;

                                    if (diffDaysss < 364) {
                                        calcularProporcional(valorTotal);
                                    } else {

                                        // Formatear el resultado a 2 decimales, con coma como decimal y punto como separador de miles
                                        const formattedResultado = valorTotal.toLocaleString(
                                            'es-ES', {
                                                minimumFractionDigits: 2,
                                                maximumFractionDigits: 2
                                            });

                                        // Asignar el resultado formateado al campo
                                        $('#salary_estimate').val(formattedResultado);
                                    }



                                } else {
                                    // Se cancela: revertimos el valor del input al valor previo
                                    $(picker.$node).val(prevDate);

                                }
                            });
                        } else {
                            // Si no había valor previo, procesamos la selección directamente
                            processNewDate(context, picker);
                            $(picker.$node).data('prevDate', $(picker.$node).val());
                        }
                    }
                }
            }).pickadate('picker');



            function processNewDate(context, picker) {
                // Cierra el picker
                picker.close();
                // Obtener la fecha seleccionada a partir del timestamp
                let selectedDate = new Date(context.select);
                let day = selectedDate.getDate();
                let month = selectedDate.getMonth();
                let year = selectedDate.getFullYear();

                // Arrays con los nombres de los días y meses
                let days = ['Domingo', 'Lunes', 'Martes', 'Miércoles', 'Jueves', 'Viernes', 'Sábado'];
                let months = ['enero', 'febrero', 'marzo', 'abril', 'mayo', 'junio', 'julio', 'agosto',
                    'septiembre', 'octubre', 'noviembre', 'diciembre'
                ];

                // Formatear la fecha
                let formattedDate = days[selectedDate.getDay()] + ' ' + day + ' de ' + months[month] + ' de ' +
                    year;
                formattedDate = formattedDate.charAt(0).toUpperCase() + formattedDate.slice(1);

                // Asignar la fecha formateada al campo de entrada
                $(picker.$node).val(formattedDate);
            }


            //Cuando se cambie la fecha de vigencia hasta
            validityto.change(function() {
                const validityFrom = $('#validity_from').val();
                const validityTo = parseDate($('#validity_to').val());
                let temporary = $('input[name="temporality"]:checked').val();
            });

            //Cambio de fecha de vigencia desde
            validityfrom.change(function() {
                const validityFrom = $(this).val();
                let temporary = $('input[name="temporality"]:checked').val();

                if (temporary === 'permanent') {
                    // Detectar si la fecha está en formato 'DD/MM/YYYY' o ya en formato largo
                    let parsedDate;

                    if (moment(validityFrom, 'DD/MM/YYYY', true).isValid()) {
                        // Si la fecha viene en formato '11/10/2024'
                        parsedDate = moment(validityFrom, 'DD/MM/YYYY');
                    } else {
                        // Si la fecha viene en formato largo 'Jueves 10 de Octubre de 2024'
                        parsedDate = moment(validityFrom, 'dddd D [de] MMMM [de] YYYY',
                            'es'); // Asegurar que esté en español
                    }

                    // Sumar un año a la fecha
                    let newDate = parsedDate.add(1, 'year').subtract(1, 'day');

                    // Formatear la nueva fecha en español
                    let formattedDate = newDate.format('dddd D [de] MMMM [de] YYYY');

                    // Capitalizar el primer carácter
                    formattedDate = formattedDate.charAt(0).toUpperCase() + formattedDate.slice(1);

                    // Asignar la fecha formateada a toPicker
                    validityto.val(formattedDate);


                    // Reconfigurar toPicker sin inicializarlo de nuevo
                    const dateObject = parsedDate.clone().subtract(1, 'year').add(2, 'day').toDate();
                    const pickerTo = $('#validity_to').pickadate('picker');
                    pickerTo.set('min', dateObject);
                    pickerTo.set('max', new Date(dateObject.getFullYear() + 1, dateObject.getMonth(),
                        dateObject
                        .getDate() - 2));

                }
            });


            $('input[name="temporality"]').change(function() {
                const temporary = $('input[name="temporality"]:checked').val();
                validityto.val('');
                $('#validity_from').trigger('change');

                if (temporary === 'permanent') {
                    $("#permanent").css('display', 'block');
                    $("#short").css('display', 'none');
                } else {
                    $("#short").css('display', 'block');
                    $("#permanent").css('display', 'none');
                    $('#validity_to').prop('disabled', false);
                }
            });

        })
    </script>


    <script>
        //funtions

        function parseDate(dateStr) {
            if (typeof dateStr === 'string') {
                const [day, month, year] = dateStr.split('/');
                return new Date(`${year}-${month}-${day}`);
            }

            if (dateStr instanceof Date) {
                let year = dateStr.getFullYear();
                let month = ("0" + (dateStr.getMonth() + 1)).slice(-2);
                let day = ("0" + dateStr.getDate()).slice(-2);
                return `${year}-${month}-${day}`;
            }
        }
    </script>
    @if ($number_workers_spreadsheet == 'No hay trabajadores')
        <!-- limite digitos polizas   -->
        <script>
            document.getElementById('number_workers').addEventListener('input', function(e) {
                let value = e.target.value;
                e.target.value = value.replace(/[^0-9]/g, '').slice(0, 7);
            });
        </script>
    @endif
    <script>
        $(document).ready(function() {
            $('#submit_policy').click(function(e) {

                e.preventDefault();

                if ($('#workRisk').val() === '') {
                    e.preventDefault(); // Evita que se envíe el formulario
                    Swal.fire({
                        icon: 'error',
                        title: 'Campo vacío',
                        text: 'Por favor selecciona una modalidad de aseguramiento.'
                    });
                }


                const periodRadios = document.querySelectorAll('input[name="period"]');
                const startDateInputs = document.querySelectorAll('input[name="start_Date[]"]');
                const endDateInputs = document.querySelectorAll('input[name="end_Date[]"]');
                const numberOfPeriods = document.querySelectorAll('input[name="number_of_periods[]"]');
                const temporalityRadios = document.querySelectorAll('input[name="temporality"]');
                const validityFrom = document.getElementById('validity_from');
                const validityTo = document.getElementById('validity_to');
                const typeCurrency = document.getElementById('type_currency');
                const salaryProjection = document.getElementById('salary_projection');
                //const workRisk = document.getElementById('workRisk');

                const temporality = $('input[name="temporality"]:checked').val();

                const annualCalculation = document.getElementById('annual_calculation_amount');
                const semiannualCalculation = document.getElementById('semiannual_calculation_amount');
                const quarterlyCalculation = document.getElementById('quarterly_calculation_amount');
                const monthlyCalculation = document.getElementById('monthly_calculation_amount');

                const singlePayment = document.getElementById('singlePayment');
                var periodicity = '';

                periodRadios.forEach(radio => {
                    if (radio.checked) {
                        periodicity = radio.value;
                    }
                });


                let valid = true;
                let errorMsg = '';

                // Validar campo de periodo
                if (![...periodRadios].some(radio => radio.checked)) {
                    valid = false;
                    errorMsg += "Debe seleccionar un periodo.<br>";
                }

                //Validar fechas
                if (periodicity === '2') {
                    startDateInputs.forEach((input, index) => {
                        if (!input.value) {
                            valid = false;
                            errorMsg +=
                                `Debe ingresar la fecha de inicio para la fila ${index + 1}.<br>`;
                        }
                    });

                    endDateInputs.forEach((input, index) => {
                        if (!input.value) {
                            valid = false;
                            errorMsg +=
                                `Debe ingresar la fecha de finalización para la fila ${index + 1}.<br>`;
                        }
                    });

                    numberOfPeriods.forEach((input, index) => {
                        if (!input.value) {
                            valid = false;
                            errorMsg +=
                                `Debe ingresar el numero de periodo para la fila ${index + 1}.<br>`;
                        }
                    });
                    //periodos deben ser minimo 12
                    if (numberOfPeriods.length < 12) {
                        valid = false;
                        errorMsg += 'Debe ingresar al menos 12 periodos.<br>';
                    }

                }

                // Validar temporalidad
                if (![...temporalityRadios].some(radio => radio.checked)) {
                    valid = false;
                    errorMsg += 'Debe seleccionar una temporalidad..<br>';
                }

                // Validar vigencia de póliza
                if (!validityFrom.value) {
                    valid = false;
                    errorMsg += 'Debe ingresar la vigencia de la póliza desde..<br>';
                }
                if (!validityTo.value) {
                    valid = false;
                    errorMsg += 'Debe ingresar la vigencia de la póliza hasta.<br>';
                }

                const work_modality_id = @json($data->work_modality_id)

                if (work_modality_id != 3 && work_modality_id != 4) {
                    //Validar proyección de salarios
                    if (!salaryProjection.value) {
                        valid = false;
                        errorMsg += 'Debe ingresar la proyección de salarios. <br>';
                    }
                }

                // Validar modalidad de aseguramiento
                /*if (!workRisk.value) {
                    valid = false;
                    errorMsg += 'Debe seleccionar una modalidad de aseguramiento.<br>';
                }*/

                @if ($number_workers_spreadsheet == 'No hay trabajadores')
                    // Validar el campo número de trabajadores
                    if ($('#number_workers').val() === '' || $('#number_workers').val() === null) {
                        Swal.fire({
                            icon: 'warning',
                            title: 'Campo vacío',
                            text: 'Por favor ingresa el número de trabajadores.'
                        });
                        return;
                    } else if ($('#number_workers').val() <= 0) {
                        Swal.fire({
                            icon: 'warning',
                            title: 'Campo vacío',
                            text: 'Por favor el número de trabajadores debe ser mayor a 0.'
                        });
                        return;
                    }
                @endif

                // Validar cálculos de primas


                if ($('#periodicidad').val() === '' && temporality === 'permanent') {
                    e.preventDefault();
                    Swal.fire({
                        icon: 'warning',
                        title: 'Campo vacío',
                        text: 'Por favor selecciona una periodicidad.'
                    });
                    return;
                }

                if (!valid) {
                    Swal.fire({
                        icon: 'warning',
                        title: 'Importante',
                        html: errorMsg,
                        confirmButtonText: 'Aceptar'
                    });
                    return;
                }

                loadingMain(true);

                $('<input>').attr({
                    type: 'hidden',
                    name: 'action',
                    value: 'recalculate'
                }).appendTo($(this).closest('form'));

                // Envía el formulario manualmente
                $(this).closest('form').submit();

            });
        });
    </script>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/locale/es.min.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const input = document.getElementById('validity_from');
            const input2 = document.getElementById('validity_to');
            let dateValue = input.value; // Obtener el valor del input de la fecha "validity_from"
            let dateValue2 = input2.value; // Obtener el valor del input de la fecha "validity_to"

            const valorPlanilla = @json($salary_estimate ?? 0);
            // Si no hay valor en "validity_from", usar la fecha actual
            if (!dateValue) {
                dateValue = moment().format('YYYY-MM-DD'); // Asignar la fecha actual en formato ISO
                input.value = dateValue; // Asignar la fecha actual al input
            }

            let dataFormoment = moment(dateValue);
            // Formatear la fecha de "validity_from"
            if (dateValue) {
                const formattedDate = moment(dateValue).format('dddd D [de] MMMM [de] YYYY');
                input.value = formattedDate.charAt(0).toUpperCase() + formattedDate.slice(
                    1); // Capitaliza solo el primer carácter
            }

            // Formatear la fecha de "validity_to" si existe
            if (dateValue2) {
                const formattedDate2 = moment(dateValue2).format('dddd D [de] MMMM [de] YYYY');
                input2.value = formattedDate2.charAt(0).toUpperCase() + formattedDate2.slice(
                    1); // Capitaliza solo el primer carácter

                let diffDays = moment(dateValue2).diff(dataFormoment, 'days');


                if (diffDays < 364) {

                    calcularProporcional(parseFloat('{{ $salary_estimate ?? 0 }}'));

                } else {

                    const salaryEstimate = convertToFormattedValue(valorPlanilla);
                    $('#salary_estimate').val(salaryEstimate);
                }

            }
        });

        function calcularProporcional(salaryEstimate) {
            const validityFromVal = $('#validity_from').val();
            const validityToVal = $('#validity_to').val();
            // Convertir la variable inyectada a número

            if (isNaN(salaryEstimate)) {
                console.error('El valor de salary_estimate no es un número válido.');
                salaryEstimate = 0;
            }


            if (validityFromVal && validityToVal && !isNaN(salaryEstimate)) {

                // Convertir las fechas a objetos moment
                const startDate = moment(validityFromVal, 'dddd D [de] MMMM [de] YYYY',
                    'es');

                const endDate = moment(validityToVal, 'dddd D [de] MMMM [de] YYYY',
                    'es');
                // Calcular la diferencia en días
                const diffDays = endDate.diff(startDate, 'days');
                // Calcular: (estimacion / 365) * dias

                const resultado = (salaryEstimate / 365) * diffDays;

                // Formatear el resultado a 2 decimales, con coma como decimal y punto como separador de miles
                const formattedResultado = resultado.toLocaleString('es-ES', {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2
                });

                // Asignar el resultado formateado al campo
                $('#salary_estimate').val(formattedResultado);
            }
        }
    </script>

    <script>
        $(document).ready(function() {
            function renumerarPeriodos() {
                $('.dynamic-field').each(function(index) {
                    $(this).find('input[name="number_of_periods[]"]')
                        .val(index + 1)
                        .attr('readonly', true)
                        .addClass('readonly-bg');
                });
            }
            // Inicializar datepickers en los campos existentes
            initializeDatepickers();

            // Aplicar rango de fecha de fin basado en la fecha de inicio
            aplicarRangoFechaFin();

            // Clonar campos dinámicos al hacer clic en el botón "incrementar períodos"
            $('#increase_periods').on('click', function() {
                // Validar que las fechas del último período estén llenas
                const lastField = $('.dynamic-field').last();
                const startDate = lastField.find('input[name="start_Date[]"]').val();
                const endDate = lastField.find('input[name="end_Date[]"]').val();

                if (!startDate || !endDate) {
                    Swal.fire({
                        icon: 'error',
                        title: 'Fechas requeridas',
                        text: 'Debes seleccionar una fecha de inicio y fin antes de agregar un nuevo período.',
                        confirmButtonText: 'Aceptar',
                        confirmButtonColor: '#000000'
                    });
                    return;
                }
                // Clonar solo la última sección de campos dinámicos
                var newFields = $('.dynamic-field').last().clone();

                // Obtener el número total de períodos
                const totalPeriodos = $('.dynamic-field').length;
                if (totalPeriodos >= 26) {
                    Swal.fire({
                        icon: 'warning',
                        title: 'Límite alcanzado',
                        text: 'No puedes agregar más de 26 períodos.',
                        confirmButtonText: 'Aceptar',
                        confirmButtonColor: '#000000'

                    });
                    return;
                }

                //comparar  la fecha fin del ultimo periodo con la fecha de vigencia hasta
                const validityToStr = $('#validity_to').val();
                const validityToformattedDate = moment(validityToStr, 'dddd D [de] MMMM [de] YYYY', 'es').format('DD/MM/YYYY');
                // Convierte ambas fechas a objetos moment
                const endDateMoment = moment(endDate, 'DD/MM/YYYY');
                const validityToformattedDateMoment = moment(validityToformattedDate, 'DD/MM/YYYY');

                console.log('fecha fin',validityToformattedDateMoment)
                console.log('fecha fin ultimo periodo', endDateMoment)

                if(endDateMoment > validityToformattedDateMoment){
                    Swal.fire({
                        icon: 'warning',
                        title: 'Límite alcanzado',
                        text: 'No puedes agregar más períodos, la fecha de fin del último período ya es superior a la fecha de vigencia hasta de la póliza.',
                        confirmButtonText: 'Aceptar',
                        confirmButtonColor: '#000000'

                    });
                    return;
                }

                // Limpiar los valores excepto el de períodos (que se reenumerará después)
                newFields.find('input').not('[name="number_of_periods[]"]').val('');

                // Cambiar el botón de agregar a un botón de eliminar en los nuevos campos
                newFields.find('.remove-field-btn').show();

                // Asegurarse de que los nuevos campos tengan IDs únicos
                newFields.find('input, select').each(function() {
                    var newId = $(this).attr('id') + '_' + Math.random().toString(36).substring(
                        7); // Generar un nuevo ID
                    $(this).attr('id', newId);
                });

                // Ocultar el botón "Agregar" en las filas nuevas
                newFields.find('#increase_periods').remove();

                $('#fieldContainer').append(newFields); // Agregar los nuevos campos al contenedor
                // Inicializar pickadate en los nuevos campos
                initializeDatepickers();

                // Establecer el min del nuevo start_Date según la fecha fin del período anterior
                const lastEndStr = endDate; // del último periodo antes de clonar
                const [d, m, y] = lastEndStr.split('/');
                const lastEndDateObj = new Date(`${y}-${m}-${d}`);

                // Sugerir como nueva fecha de inicio el día siguiente
                const nextStartDate = new Date(lastEndDateObj);
                nextStartDate.setDate(nextStartDate.getDate() + 2);

                const newStartInput = newFields.find('input[name="start_Date[]"]');
                const picker = newStartInput.pickadate('picker');

                picker.set('min', nextStartDate);

                // Prellenar el nuevo campo de fecha con el día siguiente
                const formattedSuggestion = `${String(nextStartDate.getDate()).padStart(2, '0')}/${String(nextStartDate.getMonth() + 1).padStart(2, '0')}/${nextStartDate.getFullYear()}`;
                picker.set('select', nextStartDate);

                // Renumerar todos los períodos
                renumerarPeriodos();

                //rango de fechas
                aplicarRangoFechaFin();
            });

            // Eliminar campos dinámicos al hacer clic en el botón de eliminar
            $('#fieldContainer').on('click', '.remove-field-btn', function() {
                $(this).closest('.dynamic-field').remove(); // Remover los campos

                // Mostrar el botón de agregar en la primera fila si solo queda una fila
                if ($('.dynamic-field').length === 1) {
                    $('.dynamic-field').first().find('#increase_periods')
                        .show(); // Mostrar el botón en la primera fila
                }

                // Renumerar después de eliminar
                renumerarPeriodos();
            });
        });

        // Función para inicializar los datepickers
        function initializeDatepickers() {
            $('.datepicker').each(function() {
                $(this).pickadate({
                    selectYears: 100,
                    selectMonths: true,
                    min: new Date(new Date().setMonth(new Date().getMonth() -1)),
                    formatSubmit: 'yyyy-mm-dd',
                    format: 'dd/mm/yyyy',
                    onSet: function(context) {
                        if (context.select) {
                            this.close();
                            $(this.$node).blur();
                        }
                    },
                    onClose: function() {
                        $(this.$node).trigger('change');
                    }
                });
            });
        }

        // Función para aplicar el rango de fecha de fin basado en la fecha de inicio
        function aplicarRangoFechaFin() {
            $(document).on('change', 'input[name="start_Date[]"]', function() {
                const startInput = $(this);
                const fieldGroup = startInput.closest('.dynamic-field');
                const endInput = fieldGroup.find('input[name="end_Date[]"]');

                const startStr = startInput.val();
                if (!startStr) return;

                const parts = startStr.split('/');
                const startDate = new Date(parts[2], parts[1] - 1, parts[0]);

                // Calcular la fecha mínima (15 días después de la fecha de inicio)
                const minDate = new Date(startDate);
                minDate.setDate(minDate.getDate() + 15);

                // Calcular la fecha máxima (30 días después de la fecha de inicio)
                const maxDate = new Date(startDate);
                maxDate.setDate(maxDate.getDate() + 30);

                //fecha limite de la poliza
                const validityToStr = $('#validity_to').val();
                const validityToDate = moment(validityToStr, 'dddd D [de] MMMM [de] YYYY', 'es');
                const formattedDate = validityToDate.utc().format('YYYY-MM-DD');
                const formattedDateObj = new Date(formattedDate);
                formattedDateObj.setDate(formattedDateObj.getDate() + 31);


                // Obtener la fecha de fin máxima que será la menor entre la fecha máxima de 30 días y la fecha límite
                const finalMaxDate = (maxDate < formattedDateObj) ? maxDate : formattedDateObj;

                const endPicker = endInput.pickadate('picker');
                endPicker.set('min', minDate);
                endPicker.set('max', finalMaxDate);

                // Si ya había una fecha fuera de rango seleccionada, la borra
                const selectedDate = endPicker.get('select');
                if (selectedDate) {
                    const selected = new Date(selectedDate.pick);
                    if (selected < startDate || selected > maxDate) {
                        endPicker.clear();
                    }
                }
            });
        }
    </script>
    @if ($errors->any())
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                let errorList = {!! json_encode($errors->all()) !!};

                let htmlErrors = "<ul style='text-align: left;'>";
                errorList.forEach(function(err) {
                    htmlErrors += "<li>" + err + "</li>";
                });
                htmlErrors += "</ul>";

                Swal.fire({
                    icon: 'error',
                    title: 'Errores encontrados',
                    html: htmlErrors,
                    confirmButtonText: 'Aceptar',
                    confirmButtonColor: '#000000'
                });
            });
        </script>
    @endif

@endsection
