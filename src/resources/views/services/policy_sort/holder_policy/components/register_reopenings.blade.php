@extends('layouts.menu_tomador')
@section('module_content')

    <div class="ui basic segment container">
        <div class="ui styled fluid accordion">
            <div class="active title">
                <i class="dropdown icon"></i>
                Reapertura <span style="color: red;" class="required">*</span>
            </div>

            <div class="content active">
                <form class="ui attached form" id="soportes-form" enctype="multipart/form-data"
                    action="{{ secure_url('/tomador/poliza/solicitud_reapertura/' . $activity_gis . '/save') }}"
                    method="POST">

                    {{ csrf_field() }}

                    <input type="hidden" name="npoliza" id="npoliza" value="{{ $npoliza ?? '' }}" />
                    <input type="hidden" name="id" id="id" value="{{ $id ?? '' }}" />

                    <div class="step" data-step="1">
                        <div class="content_fields_reapertura">

                            <div class="four fields">
                                <div class="field required">
                                    <label for="asegurado_nombre">Nombre del asegurado</label>
                                    <div class="ui input">
                                        <input type="text" name="asegurado_nombre" id="asegurado_nombre"
                                            autocomplete="off" value="{{ $data->affiliate->full_name }}"
                                            placeholder="Ingrese nombre del asegurado" />
                                    </div>
                                </div>

                                <div class="field required">
                                    <label for="asegurado_tipo_identificacion">Identificación del asegurado</label>
                                    <div class="ui input">
                                        <input type="text" name="asegurado_tipo_identificacion" autocomplete="off"
                                            value="{{ $data->affiliate->doc_number }}"
                                            placeholder="Ingrese identificación del asegurado" />
                                    </div>
                                </div>


                                <div class="field required">
                                    <label for="tomador_nombre">Nombre del tomador</label>
                                    <div class="ui input">
                                        <input type="text" name="tomador_nombre" id="tomador_nombre" autocomplete="off"
                                            value="{{ $data->parent_activity->affiliate->full_name }}"
                                            placeholder="Ingrese nombre del tomador" />
                                    </div>
                                </div>

                                <div class="field required">
                                    <label for="numero_poliza"># de póliza</label>
                                    <div class="ui input">
                                        <input type="text" name="numero_poliza" id="numero_poliza" autocomplete="off"
                                            value="{{ $data_poliza->formatSortNumber() }}"
                                            placeholder="Ingrese número de póliza" />
                                    </div>
                                </div>
                            </div>

                            <div class="four fields">
                                <div class="field required">
                                    <label for="fecha_accidente">Fecha de accidente</label>
                                    <div class="ui input">
                                        <input type="date" name="fecha_accidente" id="fecha_accidente" autocomplete="off"
                                            value="{{ $data->gis_sort->date_accident }}"
                                            placeholder="Seleccione fecha de accidente" />
                                    </div>
                                </div>

                                <div class="field required">
                                    <label for="numero_caso"># del caso</label>
                                    <div class="ui input">
                                        <input type="text" name="numero_caso" id="numero_caso" autocomplete="off"
                                            value="{{ $data->gis_sort->consecutive }}"
                                            placeholder="Ingrese número de caso" />
                                    </div>
                                </div>

                                <div class="field">
                                    <label for="alta_medica">Alta médica</label>
                                    <div class="ui input">
                                        <input type="date" name="alta_medica" id="alta_medica" autocomplete="off"
                                            value="{{ isset($alta_medica) && $alta_medica->created_at ? $alta_medica->created_at->format('Y-m-d') : '' }}"
                                            placeholder="Seleccione fecha de alta médica" />
                                    </div>
                                </div>
                            </div>

                        </div>

                        <div class="accordion transition">
                            <div class="active title">
                                <i class="dropdown icon"></i>
                                Documentos <span style="color: red;" class="required">*</span>
                            </div>
                            <div class="content active">

                                <div class="two fields">

                                    <div class="field">
                                        <div class="ui segment">
                                            <h5 class="ui dividing header">Orden de atención médica en paciente dado de alta
                                                <span style="color: red;" class="required">*</span>
                                            </h5>
                                            <div class="ui segment center aligned" id="fileMessageXls">
                                                <i class="huge file pdf outline icon"></i>
                                                <p id="noFileMessageOne">No has subido un archivo</p>

                                                <label for="file_medical_care_order"
                                                    class="ui secondary icon custom-back-button button">
                                                    <i class="upload icon"></i>
                                                    <input type="file" id="file_medical_care_order"
                                                        name="file_medical_care_order" accept=".pdf"
                                                        style="display: none;"
                                                        onchange="updateFileName('file_medical_care_order','noFileMessageOne');">
                                                </label>
                                            </div>
                                        </div>
                                    </div>


                                    <div class="field">
                                        <div class="ui segment">
                                            <h5 class="ui dividing header">Motivo de Reapertura<span style="color: red;"
                                                    class="required">*</span>
                                            </h5>

                                            <textarea rows="7" name="motivo_reapertura" placeholder="Ingrese motivo de reapertura." id="motivo_reapertura"></textarea>

                                        </div>
                                    </div>


                                </div>

                                <br>
                                <div style="display: flex; justify-content: space-between; width: 100%;">
                                    <a></a>
                                    <button type="button" class="ui primary button" id="next-step">Siguiente</button>
                                </div>
                            </div>
                        </div>

                    </div>
                    <div class="step" data-step="2" style="display: none">
                        @include('services.policy_sort.holder_policy.components.register_reopenings_firma')

                        <div
                            style="display: flex;margin-top: 1rem;flex-direction: row;justify-content: space-between;align-items: center;">
                            <button type="button" class="ui secondary button" id="prev-step">Atrás</button>
                            <button type="submit" class="ui primary button" id="submitReapertura">Solicitar
                                reapertura</button>
                        </div>
                    </div>



                    @if (\Session::has('success'))
                        <div class="ui green message" id="success-message">
                            <i class="close icon" id="close-success-message"></i>
                            <div class="header">
                                {!! \Session::get('success') !!}
                            </div>
                        </div>
                    @endif
                    @if (\Session::has('info'))
                        <div class="ui yellow message" id="info-message">
                            <i class="close icon" id="close-info-message"></i>
                            <div class="header">
                                {!! \Session::get('info') !!}
                            </div>
                        </div>
                    @endif
                    @if (\Session::has('error'))
                        @if (\Session::has('error'))
                            <div class="ui negative message">
                                <i class="close icon"></i>
                                <div class="header">
                                    {!! \Session::get('error') !!}
                                </div>
                            </div>
                        @endif
                    @endif


                </form>
            </div>

        </div>


    </div>



    <script>
        function updateFileName(nombreuno, nombredos) {
            const input = document.getElementById(`${nombreuno}`);
            const file = input.files[0];


            if (file) {
                document.getElementById(`${nombredos}`).innerText = file.name;
                document.getElementById(`${nombredos}`).style.color = 'green';
            } else {
                Swal.fire({
                    title: 'Advertencia',
                    text: 'Por favor, sube un archivo PDF.',
                    icon: 'warning',
                    confirmButtonText: 'Aceptar',
                });
                document.getElementById(`${nombredos}`).innerText = 'No has subido un archivo';
                document.getElementById(`${nombredos}`).style.color = '';
            }
        }

        $(document).ready(function() {

            $('.ui.accordion').accordion('open', 0);
            $('.ui.accordion').accordion('open', 1);
            $('#aseguradoTipoDropdown').dropdown();

            $('#soportes-form').form({
                on: 'blur',
                fields: {
                    asegurado_nombre: {
                        identifier: 'asegurado_nombre',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor ingrese el nombre del asegurado.'
                        }]
                    },
                    asegurado_tipo_identificacion: {
                        identifier: 'asegurado_tipo_identificacion',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor seleccione el tipo de identificación.'
                        }]
                    },
                    tomador_nombre: {
                        identifier: 'tomador_nombre',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor ingrese el nombre del tomador.'
                        }]
                    },
                    numero_poliza: {
                        identifier: 'numero_poliza',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor ingrese el número de póliza.'
                        }]
                    },
                    fecha_accidente: {
                        identifier: 'fecha_accidente',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor seleccione la fecha de accidente.'
                        }]
                    },
                    numero_caso: {
                        identifier: 'numero_caso',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor ingrese el número de caso.'
                        }]
                    },
                    file_medical_care_order: {
                        identifier: 'file_medical_care_order',
                        rules: [{
                                type: 'empty',
                                prompt: 'Debes subir la orden de atención médica en PDF.'
                            },
                            {
                                type: 'regExp[/\.pdf$/]',
                                prompt: 'El archivo debe tener extensión .pdf.'
                            }
                        ]
                    },
                    motivo_reapertura: {
                        identifier: 'motivo_reapertura',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor ingrese el motivo de reapertura.'
                        }]
                    },
                    alta_medica: {
                        identifier: 'alta_medica',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor ingrese el alta medica.'
                        }]
                    }
                },
                onFailure: function(formErrors, fields) {
                    // formErrors es un array de strings con cada prompt
                    Swal.fire({
                        icon: 'error',
                        title: 'Por favor corrija los siguientes errores:',
                        html: formErrors.map(e => `&bull; ${e}`).join('<br>'),
                        confirmButtonText: 'Cerrar'
                    });
                    return false; // cancela el submit
                }
            });


            $('#next-step').on('click', function(e) {
                e.preventDefault(); // evitamos el submit o comportamiento por defecto

                // Disparamos la validación de todo el formulario
                const isValid = $('#soportes-form').form('validate form');

                if (isValid) {
                    loadingMain(false);
                    // Si pasó la validación, ocultamos el paso 1 y mostramos el paso 2
                    $('.step[data-step="1"]').hide();
                    $('.step[data-step="2"]').show();
                }
                // Si no es válido, Semantic UI ya habrá disparado onFailure y el swal
            });


            // Regresar al paso 1
            $('#prev-step').click(function() {
                $('.step[data-step="2"]').hide();
                $('.step[data-step="1"]').show();
            });

        });
    </script>
@endsection
