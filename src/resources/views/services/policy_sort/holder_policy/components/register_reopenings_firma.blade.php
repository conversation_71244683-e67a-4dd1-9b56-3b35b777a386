<div class="accordion transition">
    <div class="title active" style="pointer-events: none;">
        <i class="dropdown icon"></i>
        Tipo de firma <span style="color: red;" class="required">*</span>
    </div>
    <div class="content active">
        <div class="inline fields">
            <div class="field">
                <div class="ui radio checkbox">
                    <input type="radio" name="signatureType" id="signatureDigital" value="digital" checked>
                    <label>En línea <i class="info circle icon " data-title="En Línea"
                            data-content="Esto es el equivalente a la firma física manuscrita, la cual es diferente a la firma electrónica expedida por el banco central."></i></label>
                </div>
            </div>
        </div>

        <div class="field" style="margin-top: 1em;">
            <a type="button" class="ui button primary small no-loading-indicator" id="sign_again"
                style="display: none; z-index: 9999;" onclick="signAgain()">
                Firmar nuevamente
            </a>
        </div>

        <div class="ui grey message" id="info-message">
            <div class="header">
                Por favor, configure su firma en este apartado para que quede registrada en el sistema y no tenga
                que volver a firmar manualmente.
            </div>
        </div>

        <div class="accordion transition">

            <div class="title active centered">
                <i class="dropdown icon"></i>
                En línea
            </div>

            <div class="content active">
                <form class="ui form small clearing">



                    <div class="two fields">
                        <div class="field">
                            <div class="form">
                                <div class="required field">
                                    <label>Firma del representante o apoderado</label>
                                </div>
                                <div class="two fields">
                                    <div class="field" id="draw_representante">
                                        <canvas class="ui secondary" id="draw-canvas-representante" width="208"
                                            height="70" style="background-color: ghostwhite">
                                            No tienes un buen navegador.
                                        </canvas>
                                    </div>
                                    <div class="field">
                                        <img id="draw-image-representante" alt="Firma"
                                            style="background-color: lightgray; width: 208px; height: 70px; object-fit: inherit;" />
                                    </div>
                                </div>

                                <div class="two fields" id="button_action_representante">
                                    <div class="field">
                                        <button type="button" class="ui button secondary small" id="draw-submitBtn"
                                            name="sign">
                                            Cargar firma
                                        </button>
                                        <button type="button" id="upload-signature-representante-btn"
                                            data-content="Cargar imagen - firma" class="ui button secondary small pop">
                                            <i class="image icon"></i>
                                        </button>
                                        <input type="file" id="signature-upload-representante" accept="image/*"
                                            style="display: none;">
                                    </div>

                                    <div class="field">
                                        <button type="button" class="ui button primary small"
                                            id="draw-clearBtn-representante">
                                            Borrar Firma
                                        </button>
                                    </div>
                                </div>


                                <input type="hidden" id="draw-dataUrl-representante" name="sign_img" />

                            </div>
                        </div>
                        <div class="field" style="display: flex; flex-direction: column; justify-content: flex-end;">
                            <div style="text-align: right; margin-top: auto;">
                                <a type="button" style="display: none;" class="ui button primary small no-loading-indicator" id="downloadReponeingForm" target="_blank">
                                    Descargar formulario reapertura
                                </a>
                            </div>
                        </div>
                    </div>


                    <div class="two fields">
                        <div class="field">
                            <label id="labelDigitalSignature">Fecha de firma representante o apoderado:
                                {{ ucfirst(strftime('%A %d de %B del %Y %H:%M:%S', time())) }}
                            </label>
                        </div>

                    </div>
                </form>
            </div>
        </div>
        <br>
        <div id="mensaje-resultado" style="display:none;" class="ui yellow message">Se requiere firmar el
            documento.</div>
    </div>
</div>



<style>
    .flex-end {
        justify-content: flex-end;
    }

    .content-firma {
        padding: 3% 10%;
    }

    .button-firma-step {
        display: flex;
        justify-content: space-between;
        width: 100%;
    }
</style>
<script>
    $('.pop').popup({
        boundary: 'body', // Esto permite que el popup se muestre fuera de los límites de la tabla
    });

    /**
     * Función que permite cargar firma digital a S3*/
    function uploadDigitalSignatures() {
        let canvas_representante = document.getElementById("draw-canvas-representante");
        let image_representante = document.getElementById("draw-image-representante");

        if (isCanvasEmpty(canvas_representante)) {
            Swal.fire({
                icon: 'error',
                title: 'Error en la solicitud',
                text: 'Por favor, ingrese su firma.',
                confirmButtonText: 'Cerrar',
                confirmButtonColor: '#91c845'
            });
            return;
        }

        if (!image_representante.src || image_representante.src === window.location.href) {
            Swal.fire({
                icon: 'error',
                title: 'Error en la solicitud',
                text: 'Por favor, cargue la imagen de la firma del representante.',
                confirmButtonText: 'Cerrar',
                confirmButtonColor: '#91c845'
            });
            return;
        }

        // Capturamos el contenido de los textareas por su id
        let canvas = document.getElementById("draw-canvas-representante");
        canvas_representante.toDataURL("image/png");

        let sign1 = isCanvasEmpty(canvas_representante) ? false : canvas_representante.toDataURL("image/png");
        let sign_representante_s3 = isCanvasEmpty(canvas_representante)

        // Configuramos el objeto de datos para enviar
        let formDataupload = new FormData();
        formDataupload.append('sign1', sign1);


        // Mostrar el mensaje de carga
        Swal.fire({
            title: 'El documento se está subiendo',
            text: 'Por favor, espere mientras termina el proceso.',
            allowOutsideClick: false,
            showConfirmButton: false,
            willOpen: () => {
                Swal.showLoading(); // Mostrar el loader
            }
        });

        $.ajax({
            url: '/gis/reapertura/firma/{{ $activity_gis }}/requestIssuanceDigitalSignature',
            type: 'POST',
            data: formDataupload,
            contentType: false,
            processData: false,
            success: function(response) {
                // Mostrar el mensaje de éxito
                Swal.fire({
                    icon: 'success',
                    title: 'Solicitud exitosa',
                    text: 'La acción se ha realizado correctamente.',
                    confirmButtonText: 'OK',
                    confirmButtonColor: '#91c845'
                }).then(() => {
                    // Actualizamos el href del botón con la URL del archivo
                    $('#download-button-digital a').attr('href', response.fileUrl);
                    // Mostramos el botón de descarga
                    $('#download-button-digital').show();
                    $('button[onclick="uploadDigitalSignatures()"]').prop('disabled', true);

                    console.log('Acción completada: ', response);
                });

                $('#signatureDigital').prop('disabled', true);

                $('#button_action_representante').hide();
                $('#draw_representante').hide();

                $('#digiltalButton').prop('disabled', true);
            },
            error: function(error) {
                Swal.fire({
                    icon: 'error',
                    title: 'Error en la solicitud',
                    text: 'Ocurrió un problema al procesar la solicitud. Inténtalo nuevamente.',
                    confirmButtonText: 'Cerrar',
                    confirmButtonColor: '#91c845'
                });

                console.warn(error);
            }
        });
    }
</script>
<script>
    $(document).ready(function() {

        /**
         *Funiones para generar CANVAS
         * ** */
        $('#draw-image-representante').hide();


        // SING UPLOAD "Representative"
        (function() { // Comenzamos una funcion auto-ejecutable
            // Obtenenemos un intervalo regular(Tiempo) en la pamtalla
            window.requestAnimFrame = (function(callback) {
                return window.requestAnimationFrame ||
                    window.webkitRequestAnimationFrame ||
                    window.mozRequestAnimationFrame ||
                    window.oRequestAnimationFrame ||
                    window.msRequestAnimaitonFrame ||
                    function(callback) {
                        window.setTimeout(callback, 1000 / 60);
                        // Retrasa la ejecucion de la funcion para mejorar la experiencia
                    };
            })();

            // Traemos el canvas mediante el id del elemento html
            let canvas = document.getElementById("draw-canvas-representante");
            let ctx = canvas.getContext("2d");


            // Mandamos llamar a los Elemetos interactivos de la Interfaz HTML
            let drawText = document.getElementById("draw-dataUrl-representante");
            let drawImage = document.getElementById("draw-image-representante");
            let clearBtn = document.getElementById("draw-clearBtn-representante");
            let submitBtn = document.getElementById("draw-submitBtn");
            // Definimos que pasa cuando el boton draw-submitBtn es pulsado
            submitBtn.addEventListener("click", function(e) {

                if (isCanvasEmpty(canvas)) {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error en la solicitud',
                        text: 'Por favor, ingrese su firma.',
                        confirmButtonText: 'Cerrar',
                        confirmButtonColor: '#91c845'
                    });
                    return;
                }

                let dataUrl = canvas.toDataURL();
                drawText.value = dataUrl;
                drawImage.setAttribute("src", dataUrl);
                drawImage.style.display = 'block';

                const tmpImg = canvas.toDataURL("image/png");

                if (tmpImg) {
                    loadingMain(true);
                    $.ajax({
                        url: '/gis/reapertura/firma/' + `{{ $activity_gis }}` +
                            '/save-signature',
                        type: 'POST',
                        data: {
                            image_gis_representative: tmpImg,
                            alta_medica: $('#alta_medica').val(),
                            motivo_reapertura: $('#motivo_reapertura').val(),
                            tomador_nombre: $('#tomador_nombre').val()
                        },
                        success: function(response) {

                            if (response.success) { // Si la solicitud fue exitosa
                                //mostrar el botón de descargar formulario de reapertura
                                $('#downloadReponeingForm').attr('href', response.reopening_url);
                                $('#downloadReponeingForm').show();

                                loadingMain(false);
                            }
                            else {
                                loadingMain(false);
                                //mostrar el error
                                Swal.fire({
                                    icon: 'error',
                                    title: 'Error en la solicitud',
                                    text: 'Ocurrio un problema al procesar la solicitud. Inténtalo nuevamente.',
                                    confirmButtonText: 'Cerrar',
                                    confirmButtonColor: '#91c845'
                                });
                            }
                        },
                        error: function(xhr) {
                            //mostrar el error
                            Swal.fire({
                                icon: 'error',
                                title: 'Error en la solicitud',
                                text: 'Ocurrio un problema al procesar la solicitud. Inténtalo nuevamente.',
                                confirmButtonText: 'Cerrar',
                                confirmButtonColor: '#91c845'
                            });
                            loadingMain(false);
                        }
                    })
                }

            }, false);

            // Activamos MouseEvent para nuestra pagina
            let drawing = false;
            let mousePos = {
                x: 0,
                y: 0
            };
            let lastPos = mousePos;
            canvas.addEventListener("mousedown", function(e) {
                /*
                  Mas alla de solo llamar a una funcion, usamos function (e){...}
                  para mas versatilidad cuando ocurre un evento
                */
                // let tint = document.getElementById("color");
                // let punta = document.getElementById("puntero");

                let tint = '#000000';
                let punta = 4;

                drawing = true;
                lastPos = getMousePos(canvas, e);
            }, false);
            canvas.addEventListener("mouseup", function(e) {
                drawing = false;
            }, false);
            canvas.addEventListener("mousemove", function(e) {
                mousePos = getMousePos(canvas, e);
            }, false);

            // Activamos touchEvent para nuestra pagina
            canvas.addEventListener("touchstart", function(e) {
                mousePos = getTouchPos(canvas, e);

                e.preventDefault(); // Prevent scrolling when touching the canvas
                let touch = e.touches[0];
                let mouseEvent = new MouseEvent("mousedown", {
                    clientX: touch.clientX,
                    clientY: touch.clientY
                });
                canvas.dispatchEvent(mouseEvent);
            }, false);
            canvas.addEventListener("touchend", function(e) {
                e.preventDefault(); // Prevent scrolling when touching the canvas
                let mouseEvent = new MouseEvent("mouseup", {});
                canvas.dispatchEvent(mouseEvent);
            }, false);
            canvas.addEventListener("touchleave", function(e) {
                // Realiza el mismo proceso que touchend en caso de que el dedo se deslice fuera del canvas
                e.preventDefault(); // Prevent scrolling when touching the canvas
                let mouseEvent = new MouseEvent("mouseup", {});
                canvas.dispatchEvent(mouseEvent);
            }, false);
            canvas.addEventListener("touchmove", function(e) {
                e.preventDefault(); // Prevent scrolling when touching the canvas
                let touch = e.touches[0];
                let mouseEvent = new MouseEvent("mousemove", {
                    clientX: touch.clientX,
                    clientY: touch.clientY
                });
                canvas.dispatchEvent(mouseEvent);
            }, false);

            // Get the position of the mouse relative to the canvas
            function getMousePos(canvasDom, mouseEvent) {
                let rect = canvasDom.getBoundingClientRect();
                /*
                  Devuelve el tamaño de un elemento y su posición relativa respecto
                  a la ventana de visualización (viewport).
                */
                return {
                    x: mouseEvent.clientX - rect.left,
                    y: mouseEvent.clientY - rect.top
                };
            }

            // Get the position of a touch relative to the canvas
            function getTouchPos(canvasDom, touchEvent) {
                let rect = canvasDom.getBoundingClientRect();

                /*
                  Devuelve el tamaño de un elemento y su posición relativa respecto
                  a la ventana de visualización (viewport).
                */
                return {
                    x: touchEvent.touches[0].clientX - rect.left, // Popiedad de todo evento Touch
                    y: touchEvent.touches[0].clientY - rect.top
                };
            }

            // Draw to the canvas
            function renderCanvas() {
                if (drawing) {
                    let tint = '#000000';
                    let punta = 4;
                    ctx.strokeStyle = tint.value;
                    ctx.beginPath();
                    ctx.moveTo(lastPos.x, lastPos.y);
                    ctx.lineTo(mousePos.x, mousePos.y);

                    ctx.lineWidth = punta;
                    ctx.stroke();
                    ctx.closePath();
                    lastPos = mousePos;
                }
            }

            function clearCanvas() {
                canvas.width = canvas.width;
            }

            // Allow for animation
            (function drawLoop() {
                requestAnimFrame(drawLoop);
                renderCanvas();
            })();

        })();
        // END: SING



        // 2) Manejamos el submit manualmente en el botón
        $('#submitReapertura').on('click', function(e) {
            e.preventDefault();


            let canvas_representante = document.getElementById("draw-canvas-representante");
            let image_representante = document.getElementById("draw-image-representante");

            if (isCanvasEmpty(canvas_representante)) {
                Swal.fire({
                    icon: 'error',
                    title: 'Error en la solicitud',
                    text: 'Por favor, ingrese su firma.',
                    confirmButtonText: 'Cerrar',
                    confirmButtonColor: '#91c845'
                });
                return;
            }

            if (!image_representante.src || image_representante.src === window.location.href) {
                Swal.fire({
                    icon: 'error',
                    title: 'Error en la solicitud',
                    text: 'Por favor, cargue la imagen de la firma del representante.',
                    confirmButtonText: 'Cerrar',
                    confirmButtonColor: '#91c845'
                });
                return;
            }
            // Si todo es válido, mostramos loader y enviamos con submit nativo
            loadingMain(true);

            // 🔥 Aquí usamos el submit nativo para no re-disparar el handler jQuery
            document.getElementById('soportes-form').submit();
        });

    })

    function isCanvasEmpty(cnv) {
        const blankCanvas = document.createElement('canvas');
        blankCanvas.width = cnv.width;
        blankCanvas.height = cnv.height;

        return cnv.toDataURL() === blankCanvas.toDataURL();
    }
</script>
<script>
    $(document).ready(function() {
        // Al hacer clic en el botón, abrir el diálogo de selección de archivos
        $('#upload-signature-btn').on('click', function() {
            $('#signature-upload').click();
        });

        // Al hacer clic en el botón, abrir el diálogo de selección de archivos para el representante
        $('#upload-signature-representante-btn').on('click', function() {
            $('#signature-upload-representante').click();
        });

        // Manejar el cambio del input de tipo file para la firma del representante
        $('#signature-upload-representante').on('change', function() {
            const file = this.files[0];

            if (file) {
                const reader = new FileReader();

                reader.onload = function(event) {
                    const imageUrl = event.target.result;

                    // Mostrar la imagen en el elemento <img>
                    $('#draw-image-representante').show();
                    $('#draw-image-representante').attr('src', imageUrl);
                    $('#draw-image-representante').css("background-color", "ghostwhite");

                    $('#draw-dataUrl-representante').val(imageUrl);

                    // Dibujar la imagen en el canvas
                    const canvas = document.getElementById('draw-canvas-representante');
                    const ctx = canvas.getContext('2d');
                    const image = new Image();
                    image.onload = function() {
                        ctx.clearRect(0, 0, canvas.width, canvas.height);
                        canvas.width = 208;
                        canvas.height = 70;

                        const ratio = Math.min(canvas.width / image.width, canvas.height / image
                            .height);
                        const newWidth = image.width * ratio;
                        const newHeight = image.height * ratio;

                        const xOffset = (canvas.width - newWidth) / 2;
                        const yOffset = (canvas.height - newHeight) / 2;

                        ctx.drawImage(image, xOffset, yOffset, newWidth, newHeight);

                        // Enviar la firma al servidor
                        const imageData = canvas.toDataURL('image/png');
                        $.ajax({
                            url: '/gis/reapertura/firma/' + `{{ $activity_gis }}` +
                                '/save-signature',
                            type: 'POST',
                            data: {
                                image_gis_representative: imageData,
                            },
                            success: function() {
                                console.log("Firma temporalmente guardada...");
                            },
                            error: function(xhr) {
                                console.error(xhr);
                            }
                        });
                    };
                    image.src = imageUrl;
                };

                reader.readAsDataURL(file);
            }
        });

        // Manejar el clic del botón "Borrar Firma"
        $('#draw-clearBtn-representante').on('click', function() {
            const canvas = document.getElementById('draw-canvas-representante');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            $('#draw-image-representante').attr('src', '').hide();
            $('#signature-upload-representante').val('');
            $('#draw-dataUrl-representante').val('');

            //ocultar el botón de descargar formulario reapertura
            $('#downloadReponeingForm').hide();
        });


    });
</script>

<!--Function del botón firmar nuevamente--->
<script>
    function signAgain() {

        // Mostrar el mensaje de carga
        Swal.fire({
            title: 'Solicitud en proceso...',
            text: 'Por favor, espere un momento.',
            allowOutsideClick: false,
            showConfirmButton: false,
            willOpen: () => {
                Swal.showLoading(); // Iniciar loader
            }
        });

        $.ajax({
            url: '/gis/reapertura/firma/{{ $activity_gis }}/firmarNuevamente',
            type: 'POST', // Cambiado a POST
            headers: {
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            },
            success: function(response) {
                // Ocultar el loader de Swal cuando la solicitud sea exitosa
                Swal.close();

                Swal.fire({
                    icon: 'success',
                    title: 'Solicitud procesada',
                    text: 'Su solicitud ha sido procesada con exito.'
                });

                //recargar la pagina después de 1 segundo
                setTimeout(function() {
                    window.location.reload();
                }, 1500);
            },
            error: function(error) {
                // Ocultar el loader y mostrar el error
                Swal.close();

                Swal.fire({
                    icon: 'error',
                    title: 'Error en la solicitud',
                    text: 'Ocurrió un problema al procesar la solicitud. Inténtalo nuevamente.',
                    confirmButtonText: 'Cerrar',
                    confirmButtonColor: '#91c845'
                });

                console.error(error);
            }
        });
    }
</script>
