<div class="ui basic segment" id="policyholder" style="margin-bottom: 0;padding-bottom: 0;">
    <h2 class="ui header">Información del tomador</h2>
    <form class="ui form">
        <div class="ui secondary segment">
            <div class="three fields">
                <div class="field ">
                    <label>Nombre:</label>
                    <input
                        value="{{ isset($affiliate_tomador) ? ucwords(strtolower($affiliate_tomador->full_name)) : '' }}"
                        type="text" class="grayed-input" id="affiliate_name" readonly>
                </div>
                <div class="field">
                    <label>Correo electrónico:</label>
                    <input value="{{ isset($affiliate_tomador) ? $affiliate_tomador->email : '' }}" type="text"
                        class="grayed-input" id="affiliate_email" readonly>
                </div>

                <a href="/tomador/videoTutorial" target="_blank" class="ui button primary no-loading-indicator "
                    style="margin-bottom: 20px;margin-top: 24px;margin-left: 8px;">
                    <i class="video icon"></i> Ver tutoriales
                </a>

            </div>
            <div class="three fields">
                <div class="field">
                    <label>Pólizas</label>
                    <div class="ui selection search dropdown dropdown_menu" id="select_policy">
                        <input type="hidden" name="policy_id" id="policy_id">
                        <i class="dropdown icon"></i>
                        <div class="default text">Selecciona una opción</div>
                        <div class="menu">
                            @if (isset($policySortData))
                                @foreach ($policySortData as $policy)
                                    <div class="item" data-value="{{ $policy['id'] }}"
                                        data-activity="{{ $policy['activity_id'] }}"
                                        data-search="{{ $policy['consecutive'] }}">
                                        Póliza SORT {{ $policy['consecutive'] }}
                                    </div>
                                @endforeach
                            @endif
                        </div>
                    </div>
                </div>

                <div class="field">
                    <label>Estado de la póliza:</label>
                    <input type="text" id="state_police" value="Sin estado" class="grayed-input" readonly>
                </div>
                <!-- Beneficio de colectividad -->
                <div class="field" id="benefits_colective" style="display: none">
                    <label>Beneficio de colectividad</label>
                    <div class="benefit-options">
                        <div class="ui radio checkbox">
                            <label for="benefit_yes">Sí</label>
                            <input type="radio" name="benefit_collectivity" id="benefit_yes" value="yes">
                        </div>
                        <div class="ui radio checkbox">
                            <label for="benefit_no">No</label>
                            <input type="radio" name="benefit_collectivity" id="benefit_no" value="no">
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </form>


    @if (Auth::user()->view_tomador_autorizado())
        <a href="/admin/autorizados/list" class="button_autorizado" data-tooltip="Cambiar tomador"
            data-position="left center">
            <i class="users icon"></i>
        </a>
    @endif




    @include('services.policy_sort.components.modal_colectividad')

</div>

<div class="ui basic segment " style="position: relative;margin-top: 0;">

    <div id="policy_menu" class="@if (!$npoliza)
    hidden
    @endif">
        @if (Auth::user()->view_tomador_autorizado())

            @php
                // Aseguramos que $tomadorAutorizado->userPermission sea una colección,
                // en caso contrario usamos una colección vacía.
                // 1) Obtienes la colección de policies (o vacía)
                $userPolicies = optional($tomadorAutorizado)->userPermission ?? collect();



                if ($npoliza) {

                    // 2) Filtramos primero la policy que corresponde al activity_id que nos interesa
                    $PolicyFind = App\PolicySort::where('id', $npoliza)->first();

                    $selectedPolicy = $userPolicies->firstWhere('activity_id', $PolicyFind->activity_id);
                    // 3) Si no encontramos esa policy, usamos colección vacía; si la encontramos, tomamos sus permisos
                    $allPerms = $selectedPolicy
                        ? collect($selectedPolicy->permissions)
                        : collect();
                } else {

                    // 2) Aplana todos los permisos de cada policy en una sola colección
                    $allPerms = $userPolicies->flatMap(function ($policy) {
                        return $policy->permissions;
                    });

                }



                // 3) El mapping alias => user_view_id
                $permissionsMapping = [
                    'accidentes' => 1,
                    'planilla' => 2,
                    'constancias' => 3,
                    'consultas' => 4,
                    'variaciones' => 5,
                    'estadisticas' => 6,
                    'descargar' => 7,
                ];

                // 4) Recorre y arma tu array final
                $permData = [];
                foreach ($permissionsMapping as $alias => $viewId) {
                    $perm = $allPerms->firstWhere('user_view_id', $viewId);
                    $permData[$alias] = [
                        'view' => $perm->view ?? 0,
                        'edit' => $perm->edit ?? 0,
                    ];
                }

                // Ahora asignamos cada permiso a una variable individual, si lo necesitas por separado:
                $accidentesView = $permData['accidentes']['view'];
                $accidentesEdit = $permData['accidentes']['edit'];

                $planillaView = $permData['planilla']['view'];
                $planillaEdit = $permData['planilla']['edit'];

                $constanciasView = $permData['constancias']['view'];
                $constanciasEdit = $permData['constancias']['edit'];

                $consultasView = $permData['consultas']['view'];
                $consultasEdit = $permData['consultas']['edit'];

                $variacionesView = $permData['variaciones']['view'];
                $variacionesEdit = $permData['variaciones']['edit'];

                $estadisticasView = $permData['estadisticas']['view'];
                $estadisticasEdit = $permData['estadisticas']['edit'];

                $descargarView = $permData['descargar']['view'];
                $descargarEdit = $permData['descargar']['edit'];

                // Calcula el total de pestañas activas
                $activeTabs = 0;
                $activeTabs += $accidentesView || $accidentesEdit ? 1 : 0;
                $activeTabs += $planillaEdit ? 1 : 0;
                $activeTabs += $constanciasEdit ? 1 : 0;
                $activeTabs += $consultasView ? 1 : 0;
                $activeTabs += $variacionesView || $variacionesEdit ? 1 : 0;
                $activeTabs += $estadisticasView || $estadisticasEdit ? 1 : 0;
                $activeTabs += $descargarView || $descargarEdit ? 1 : 0;
                // Array para mapear números a palabras en inglés
                $numWords = [
                    0 => 'zero',
                    1 => 'one',
                    2 => 'two',
                    3 => 'three',
                    4 => 'four',
                    5 => 'five',
                    6 => 'six',
                    7 => 'seven',
                ];

                // Si por alguna razón activeTabs está fuera del rango, lo dejamos en 'seven' (máximo)
                $menuCountWord = isset($numWords[$activeTabs]) ? $numWords[$activeTabs] : 'seven';
            @endphp

            <div class="ui {{ $menuCountWord }} item menu menu_autorizado">
                <meta name="csrf-token" content="{{ csrf_token() }}">

                @if ($accidentesEdit || $accidentesView)
                    <div class="ui dropdown item scrolling">
                        Reporte de accidente o enfermedad
                        <i class="dropdown icon" style="margin-right: 10px;"></i>
                        <div class="menu">

                            @if ($accidentesEdit)
                                <div class="header secondary_color">
                                    <i class="tags icon"></i>
                                    Aviso de accidente
                                </div>

                                <a class="item" href="/servicio/gis_sort/tomador/npolicy"
                                    data-href="/servicio/gis_sort/tomador/npolicy">En Línea</a>
                                <a class="item" data-href="/servicio/gis_sort/tomador/npolicy">WhatsApp</a>
                                <a class="item" data-href="/servicio/gis_sort/tomador/npolicy">Llamada</a>

                                <div class="divider"></div>

                                <a href="/tomador/poliza/{{ $id }}/furat/anexar/npolicy"
                                    data-href="/tomador/poliza/{{ $id }}/furat/reporte/npolicy"
                                    class="item {{ $active === 'pendiente_reporte' ? 'active' : '' }}">
                                    <i class="table icon"></i>
                                    Avisos pendiente de formalizar reporte accidente</a>

                                <div class="divider"></div>
                            @endif


                            @if ($accidentesEdit || $accidentesView)
                                <div class="header secondary_color">
                                    <i class="tags icon"></i>
                                    Formato único reporte de accidente o enfermedad
                                </div>

                                @if ($accidentesEdit)
                                    <a class="item {{ $active === 'pendiente_reporte' ? 'active' : '' }}"
                                        href="/tomador/poliza/{{ $id }}/furat/reporte/npolicy"
                                        data-href="/tomador/poliza/{{ $id }}/furat/reporte/npolicy">Reportar
                                        formalmente el caso</a>

                                    <a class="item {{ $active === 'anexar_soportes' ? 'active' : '' }}"
                                        href="/tomador/poliza/{{ $id }}/furat/anexar/npolicy"
                                        data-href="/tomador/poliza/{{ $id }}/furat/anexar/npolicy">Anexar
                                        soportes</a>
                                @endif

                                @if ($accidentesView)
                                    <a class="item {{ $active === 'estado_casos_reportados' ? 'active' : '' }}"
                                        href="/tomador/poliza/{{ $id }}/furat/estado/npolicy"
                                        data-href="/tomador/poliza/{{ $id }}/furat/estado/npolicy">Estado
                                        de casos denunciados</a>
                                @endif
                            @endif



                            @if ($accidentesEdit)
                                <div class="divider"></div>
                                <div class="header secondary_color">
                                    <i class="tags icon"></i>
                                    Solicitud de casos
                                </div>

                                <a class="item {{ $active === 'reapertura' ? 'active' : '' }}"
                                    href="/tomador/poliza/{{ $id }}/gis/reapertura/npolicy"
                                    data-href="/tomador/poliza/{{ $id }}/gis/reapertura/npolicy">
                                    Reapertura</a>

                                <a class="item {{ $active === 'tramite_judicial' ? 'active' : '' }}"
                                    href="/tomador/poliza/{{ $id }}/gis/tramite_judicial/npolicy"
                                    data-href="/tomador/poliza/{{ $id }}/gis/tramite_judicial/npolicy">
                                    Trámite judicial</a>
                            @endif

                        </div>
                    </div>
                @endif

                @if ($planillaEdit)
                    <div class="ui dropdown item dropdown_menu_autorizado reporte_planilla_menu">
                        Reporte de planilla
                        <i class="dropdown icon reporte_planilla"></i>
                        <!-- Sub menu de planillas -->
                        <div class="menu">
                            <div class="header secondary_color">
                                <i class="tags icon"></i>
                                Reportar nueva planilla
                            </div>

                            <a class="item {{ $active === 'ingresar_manualmente' ? 'active' : '' }}"
                                href="/tomador/poliza/{{ $id }}/ingresar_manualmente/npolicy"
                                data-href="/tomador/poliza/{{ $id }}/ingresar_planilla_manualmente/npolicy"
                                id="spreadsheet">Cargar planilla</a>

                            <a class="item {{ $active === 'suspend_policy' ? 'active' : '' }}"
                                href="/tomador/poliza/{{ $id }}/suspend_policy/npolicy"
                                data-href="/tomador/poliza/{{ $id }}/suspend_policy/npolicy"
                                id="suspend_policy"> Suspensión temporal de la póliza</a>

                            <a class="item {{ $active === 'inclusion_empleado' ? 'active' : '' }}"
                                href="/tomador/poliza/{{ $id }}/inclusion_empleado/npolicy"
                                data-href="/tomador/poliza/{{ $id }}/inclusion_empleado/npolicy"
                                id="inclusion_empleado"> Inclusión de persona trabajadora</a>

                            <div class="divider"></div>

                            <div class="header secondary_color">
                                <i class="tags icon"></i>
                                Reportar planilla con los datos del periodo anterior
                            </div>

                            <a class="item {{ $active === 'reportar_sin_modificaciones' ? 'active' : '' }}"
                                href="/tomador/poliza/{{ $id }}/reportar_sin_modificaciones/npolicy"
                                data-href="/tomador/poliza/{{ $id }}/reportar_sin_modificaciones/npolicy"
                                id="report_spreadsheet">
                                Reportar planilla sin modificaciones
                            </a>

                            <a class="item {{ $active === 'modificar_datos_trabajadores' ? 'active' : '' }}"
                                href="/tomador/poliza/{{ $id }}/modificar_datos_trabajadores/npolicy"
                                data-href="/tomador/poliza/{{ $id }}/modificar_datos_trabajadores/npolicy"
                                id="report_spreadsheet_modified">
                                Reportar planilla con modificacón
                            </a>

                        </div>
                    </div>
                @endif

                @if ($constanciasEdit)
                    <div class="ui dropdown item dropdown_menu_autorizado">
                        Constancias de póliza
                        <i class="dropdown icon"></i>
                        <div class="menu">
                            <a class="item {{ $active === 'certificado_poliza_dia' ? 'active' : '' }}"
                                onclick="handleEmailRequest('policy_certification_up_to_date')"
                                id="policy_day_certificate">Certificado de póliza al día</a>
                            <a class="item {{ $active === 'estado_cuenta_trabajador' ? 'active' : '' }}"
                                onclick="openDocNumberModalOrHandleRequest('employee_account_statement')"
                                id="worker_account_status">
                                Estado de cuenta del trabajador</a>
                            <a class="item {{ $active === 'constancia_sumas_pendientes' ? 'active' : '' }}"
                                onclick="handleEmailRequest('outstanding_sums_to_be_paid')"
                                id="constancy_outstanding_amounts'">Constancia de primas pendientes por pagar</a>
                            <a class="item {{ $active === 'constancia_prima_pagada' ? 'active' : '' }}"
                                onclick="handleEmailRequest('premiums_paid_certificate')"
                                id="constancy_premium_paid'">Constancia de primas pagadas</a>
                        </div>
                    </div>

                    <!-- Modal (cuadro emergente) para capturar el número de cédula -->
                    <div class="ui modal" id="docNumberModal">
                        <div class="header">Número de cédula del trabajador</div>
                        <div class="content">
                            <form class="ui form" method="POST" id="anulacionForm">
                                {{ csrf_field() }}
                                <div class="field">
                                    <label>Número de Cédula del Trabajador</label>
                                    <input type="text" name="doc_number" id="doc_number"
                                        placeholder="Ingrese el número de cédula" required>
                                </div>
                                <div class="actions">
                                    <div class="ui cancel button" onclick="cancelDocNumber()">Cancelar</div>
                                    <button type="button" class="ui button primary"
                                        onclick="submitDocNumber()">Confirmar</button>
                                </div>
                            </form>
                        </div>
                    </div>
                @endif

                @if ($consultasView)
                    <div class="ui dropdown item dropdown_menu_autorizado">
                        Consultas de pólizas
                        <i class="dropdown icon"></i>
                        <div class="menu">
                            <a class="item {{ $active === 'recibos_pendientes' ? 'active' : '' }}"
                                data-href="/tomador/poliza/{{ $id }}/recibos_pendientes/npolicy"
                                href="/tomador/poliza/{{ $id }}/recibos_pendientes/npolicy"
                                id="renewal_sorpending_receipts">Renovaciones o recibos pendientes</a>
                            <a class="item {{ $active === 'consultar_planilla' ? 'active' : '' }}"
                                href="/tomador/poliza/{{ $id }}/consultar_planilla/npolicy"
                                data-href="/tomador/poliza/{{ $id }}/consultar_planilla/npolicy"
                                id="consult_spreadsheet">Consultar planillas</a>
                            <a class="item {{ $active === 'consultar_siniestralidad' ? 'active' : '' }}"
                                href="/tomador/poliza/{{ $id }}/consultar_siniestralidad/npolicy"
                                data-href="/tomador/poliza/{{ $id }}/consultar_siniestralidad/npolicy"
                                id="consult_accident_rate'">Consultar siniestralidad</a>
                            <a class="item {{ $active === 'consultar_inclusion' ? 'active' : '' }}"
                                href="/tomador/poliza/{{ $id }}/consultar_inclusion/npolicy"
                                data-href="/tomador/poliza/{{ $id }}/consultar_inclusion/npolicy"
                                id="consultar_inclusion'">Consultar inclusión de persona trabajadora</a>
                        </div>
                    </div>
                @endif

                @if ($variacionesEdit || $variacionesView)
                    <div class="ui dropdown item dropdown_menu_autorizado">
                        Solicitud de variaciones
                        <i class="dropdown icon"></i>
                        <div class="menu">
                            @if ($variacionesEdit)
                                <a class="item {{ $active === 'cambio_vigencia_poliza' ? 'active' : '' }}"
                                    href="/tomador/poliza/{{ $id }}/cambio_vigencia_poliza/npolicy"
                                    data-href="/tomador/poliza/{{ $id }}/cambio_vigencia_poliza/npolicy"
                                    id="validity_change">Cambio vigencia de póliza</a>

                                <a class="item {{ $active === 'cambio_forma_pago' ? 'active' : '' }}"
                                    href="/tomador/poliza/{{ $id }}/cambio_forma_pago/npolicy"
                                    data-href="/tomador/poliza/{{ $id }}/cambio_forma_pago/npolicy"
                                    id="payment_change">Cambio forma de pago</a>

                                <a class="item {{ $active === 'cancelación' ? 'active' : '' }}"
                                    href="/tomador/poliza/{{ $id }}/cancelación/npolicy"
                                    data-href="/tomador/poliza/{{ $id }}/cancelación/npolicy"
                                    id="cancel">Cancelación</a>

                                <a class="item {{ $active === 'rehabilitación' ? 'active' : '' }}"
                                    href="/tomador/poliza/{{ $id }}/rehabilitación/npolicy"
                                    data-href="/tomador/poliza/{{ $id }}/rehabilitación/npolicy"
                                    id="rehabilitation">Rehabilitación</a>

                                <div class="divider"></div>

                                <div class="header secondary_color">
                                    <i class="tags icon"></i>
                                    Actualización de datos de la póliza
                                </div>

                                <a class="item {{ $active === 'datos_basicos_contacto' ? 'active' : '' }}"
                                    href="/tomador/poliza/{{ $id }}/datos_basicos_contacto/npolicy"
                                    data-href="/tomador/poliza/{{ $id }}/datos_basicos_contacto/npolicy"
                                    id="basic_data">Datos básicos de contacto</a>
                                <a class="item {{ $active === 'otros_datos' ? 'active' : '' }}"
                                    href="/tomador/poliza/{{ $id }}/otros_datos/npolicy"
                                    data-href="/tomador/poliza/{{ $id }}/otros_datos/npolicy"
                                    id="other_data">Otros datos</a>

                                <div class="divider"></div>
                            @endif

                            @if ($variacionesView)
                                <a class="item {{ $active === 'svariaciones' ? 'active' : '' }}"
                                    href="/tomador/poliza/{{ $id }}/variaciones/npolicy"
                                    data-href="/tomador/poliza/{{ $id }}/variaciones/npolicy"
                                    id="rehabilitation">
                                    <i class="table icon"></i>
                                    Seguimiento variaciones solicitadas</a>
                            @endif

                        </div>
                    </div>
                @endif

                @if ($estadisticasEdit || $estadisticasView)
                    <a class="item {{ $active === 'estadisticas_reporte' ? 'active' : '' }}"
                        href="/tomador/poliza/{{ $id }}/estadisticas_reporte/npolicy"
                        data-href="/tomador/poliza/{{ $id }}/estadisticas_reporte/npolicy"
                        id="statistics">Estadísticas y reportes</a>
                @endif

                @if ($descargarEdit || $descargarView)
                    <a class="item falta {{ $active === 'descargar_formulario' ? 'active' : '' }}">Descargar
                        formularios</a>
                @endif

            </div>
        @else
            <div class="ui seven item menu">
                <meta name="csrf-token" content="{{ csrf_token() }}">

                <div class="ui dropdown item dropdown_menu" id="menu_reporte_accidente">
                    Reporte de accidente o enfermedad
                    <i class="dropdown icon" style="margin-right: 10px;"></i>
                    <div class="menu">


                        <div class="item">
                            Aviso de accidente
                            <i class="dropdown icon"></i>
                            <div class="menu">
                                <div class="item">
                                    Avisar accidente
                                    <i class="dropdown icon"></i>
                                    <div class="menu">
                                        <a class="item" href="/servicio/gis_sort/tomador/npolicy"
                                            data-href="/servicio/gis_sort/tomador/npolicy">En Línea</a>

                                        <a class="item" data-href="/servicio/gis_sort/tomador/npolicy">Llamada</a>
                                    </div>
                                </div>
                                <a href="/tomador/poliza/{{ $id }}/furat/anexar/npolicy"
                                    data-href="/tomador/poliza/{{ $id }}/furat/reporte/npolicy"
                                    class="item {{ $active === 'pendiente_reporte' ? 'active' : '' }}">Avisos
                                    pendiente
                                    de
                                    formalizar reporte accidente</a>
                            </div>
                        </div>


                        <div class="item">
                            Formato único reporte de accidente o enfermedad
                            <i class="dropdown icon"></i>
                            <div class="menu">

                                <a class="item {{ $active === 'pendiente_reporte' ? 'active' : '' }}"
                                    href="/tomador/poliza/{{ $id }}/furat/reporte/npolicy"
                                    data-href="/tomador/poliza/{{ $id }}/furat/reporte/npolicy">Reportar
                                    formalmente el caso</a>

                                <a class="item {{ $active === 'anexar_soportes' ? 'active' : '' }}"
                                    href="/tomador/poliza/{{ $id }}/furat/anexar/npolicy"
                                    data-href="/tomador/poliza/{{ $id }}/furat/anexar/npolicy">Anexar
                                    soportes</a>



                                <a class="item {{ $active === 'estado_casos_reportados' ? 'active' : '' }}"
                                    href="/tomador/poliza/{{ $id }}/furat/estado/npolicy"
                                    data-href="/tomador/poliza/{{ $id }}/furat/estado/npolicy">Estado de
                                    casos
                                    denunciados</a>
                            </div>

                        </div>

                        <div class="item">
                            Solicitud de casos
                            <i class="dropdown icon"></i>
                            <div class="menu">
                                <a class="item {{ $active === 'reapertura' ? 'active' : '' }}"
                                    href="/tomador/poliza/{{ $id }}/gis/reapertura/npolicy"
                                    data-href="/tomador/poliza/{{ $id }}/gis/reapertura/npolicy">
                                    Reapertura</a>
                                <a class="item {{ $active === 'tramite_judicial' ? 'active' : '' }}"
                                    href="/tomador/poliza/{{ $id }}/gis/tramite_judicial/npolicy"
                                    data-href="/tomador/poliza/{{ $id }}/gis/tramite_judicial/npolicy">
                                    Trámite judicial</a>
                            </div>

                        </div>

                        <a class="item {{ $active === 'reporte_accidente_enfermedad' ? 'active' : '' }}"
                            href="/tomador/poliza/{{ $id }}/furat/reportes/table_furat_reportes_view/npolicy"
                            data-href="/tomador/poliza/{{ $id }}/furat/reportes/table_furat_reportes_view/npolicy">Generar
                            PDF de reporte de accidente y enfermedad</a>

                        <a class="item {{ $active === 'solicitud_reconocimiento_factura' ? 'active' : '' }}"
                            href="/tomador/poliza/{{ $id }}/gis/solicitud_reconocimiento_factura/npolicy"
                            data-href="/tomador/poliza/{{ $id }}/gis/solicitud_reconocimiento_factura/npolicy">
                            Solicitud reconocimiento de factura por riesgos de trabajo
                        </a>

                        <a class="item {{ $active === 'seguimiento_solicitud_reintegro' ? 'active' : '' }}"
                            href="/tomador/poliza/{{ $id }}/gis/seguimiento_solicitud_reintegro/npolicy"
                            data-href="/tomador/poliza/{{ $id }}/gis/seguimiento_solicitud_reintegro/npolicy">
                            Seguimiento solicitudes reintegros
                        </a>

                    </div>
                </div>

                <div class="ui dropdown item dropdown_menu reporte_planilla_menu" id="menu_reporte_planilla">
                    Reporte de planilla
                    <i class="dropdown icon reporte_planilla"></i>
                    <!-- Sub menu de planillas -->
                    <div class="menu">
                        <div class="item">
                            Reportar nueva planilla
                            <i class="dropdown icon"></i>
                            <div class="menu">
                                <a class="item {{ $active === 'ingresar_manualmente' ? 'active' : '' }}"
                                    href="/tomador/poliza/{{ $id }}/ingresar_manualmente/npolicy"
                                    data-href="/tomador/poliza/{{ $id }}/ingresar_planilla_manualmente/npolicy"
                                    id="spreadsheet">Cargar planilla</a>
                                <div class="item">
                                    Reportar planilla con los datos del periodo anterior
                                    <i class="dropdown icon"></i>
                                    <div class="menu">
                                        <a class="item {{ $active === 'reportar_sin_modificaciones' ? 'active' : '' }}"
                                            href="/tomador/poliza/{{ $id }}/reportar_sin_modificaciones/npolicy"
                                            data-href="/tomador/poliza/{{ $id }}/reportar_sin_modificaciones/npolicy"
                                            id="report_spreadsheet">
                                            Reportar planilla sin modificaciones
                                        </a>
                                        <a class="item {{ $active === 'modificar_datos_trabajadores' ? 'active' : '' }}"
                                            href="/tomador/poliza/{{ $id }}/modificar_datos_trabajadores/npolicy"
                                            data-href="/tomador/poliza/{{ $id }}/modificar_datos_trabajadores/npolicy"
                                            id="report_spreadsheet_modified">
                                            Reportar planilla con modificacón
                                        </a>
                                    </div>
                                </div>
                                <a class="item {{ $active === 'suspend_policy' ? 'active' : '' }}"
                                    href="/tomador/poliza/{{ $id }}/suspend_policy/npolicy"
                                    data-href="/tomador/poliza/{{ $id }}/suspend_policy/npolicy"
                                    id="suspend_policy"> Suspensión temporal de la póliza
                                </a>
                                <a class="item {{ $active === 'inclusion_empleado' ? 'active' : '' }}"
                                    href="/tomador/poliza/{{ $id }}/inclusion_empleado/npolicy"
                                    data-href="/tomador/poliza/{{ $id }}/inclusion_empleado/npolicy"
                                    id="inclusion_empleado"> Inclusión de persona trabajadora
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="ui dropdown item dropdown_menu" id="menu_constancias_polizas">
                    Constancias de póliza
                    <i class="dropdown icon"></i>
                    <div class="menu">
                        <a class="item {{ $active === 'certificado_poliza_dia' ? 'active' : '' }}"
                            onclick="handleEmailRequest('policy_certification_up_to_date')"
                            id="policy_day_certificate">Certificado de póliza al día</a>
                        <a class="item {{ $active === 'estado_cuenta_trabajador' ? 'active' : '' }}"
                            onclick="openDocNumberModalOrHandleRequest('employee_account_statement')"
                            id="worker_account_status">
                            Estado de cuenta del trabajador
                        </a>
                        <a class="item {{ $active === 'constancia_sumas_pendientes' ? 'active' : '' }}"
                            onclick="handleEmailRequest('outstanding_sums_to_be_paid')"
                            id="constancy_outstanding_amounts'">Constancia de primas pendientes por pagar</a>
                        <a class="item {{ $active === 'constancia_prima_pagada' ? 'active' : '' }}"
                            onclick="handleEmailRequest('premiums_paid_certificate')"
                            id="constancy_premium_paid'">Constancia
                            de primas pagadas</a>

                    </div>
                </div>
                <!-- Modal (cuadro emergente) para capturar el número de cédula -->
                <div class="ui modal" id="docNumberModal">
                    <div class="header">Número de cédula del trabajador</div>
                    <div class="content">
                        <form class="ui form" method="POST" id="anulacionForm">
                            {{ csrf_field() }}

                            <div class="field">
                                <label>Número de Cédula del Trabajador</label>
                                <input type="text" name="doc_number" id="doc_number"
                                    placeholder="Ingrese el número de cédula" required>
                            </div>

                            <div class="actions">
                                <div class="ui cancel button" onclick="cancelDocNumber()">Cancelar</div>
                                <button type="button" class="ui button primary"
                                    onclick="submitDocNumber()">Confirmar
                                </button>
                            </div>
                        </form>
                    </div>
                </div>


                <div class="ui dropdown item dropdown_menu" id="menu_consultas_polizas">
                    Consultas de pólizas
                    <i class="dropdown icon"></i>
                    <div class="menu">
                        <a class="item {{ $active === 'recibos_pendientes' ? 'active' : '' }}"
                            data-href="/tomador/poliza/{{ $id }}/recibos_pendientes/npolicy"
                            href="/tomador/poliza/{{ $id }}/recibos_pendientes/npolicy"
                            id="renewal_sorpending_receipts">Renovaciones o recibos pendientes</a>
                        <a class="item {{ $active === 'consultar_planilla' ? 'active' : '' }}"
                            href="/tomador/poliza/{{ $id }}/consultar_planilla/npolicy"
                            data-href="/tomador/poliza/{{ $id }}/consultar_planilla/npolicy"
                            id="consult_spreadsheet">Consultar planillas</a>
                        <a class="item {{ $active === 'consultar_siniestralidad' ? 'active' : '' }}"
                            href="/tomador/poliza/{{ $id }}/consultar_siniestralidad/npolicy"
                            data-href="/tomador/poliza/{{ $id }}/consultar_siniestralidad/npolicy"
                            id="consult_accident_rate'">Consultar siniestralidad</a>
                        <a class="item {{ $active === 'consultar_inclusion' ? 'active' : '' }}"
                            href="/tomador/poliza/{{ $id }}/consultar_inclusion/npolicy"
                            data-href="/tomador/poliza/{{ $id }}/consultar_inclusion/npolicy"
                            id="consultar_inclusion'">Consultar inclusión de persona trabajadora</a>
                        <a class="item {{ $active === 'certificado_aseguramiento' ? 'active' : '' }}"
                            data-href="/tomador/poliza/{{ $id }}/certificado_aseguramiento/npolicy"
                            href="/tomador/poliza/{{ $id }}/certificado_aseguramiento/npolicy"
                            id="renewal_sorpending_receipts">Certificado de aseguramiento</a>

                    </div>
                </div>

                <div class="ui dropdown item dropdown_menu">
                    Solicitud de variaciones
                    <i class="dropdown icon"></i>
                    <div class="menu">
                        <a class="item {{ $active === 'cambio_vigencia_poliza' ? 'active' : '' }}"
                            href="/tomador/poliza/{{ $id }}/cambio_vigencia_poliza/npolicy"
                            data-href="/tomador/poliza/{{ $id }}/cambio_vigencia_poliza/npolicy"
                            id="validity_change">Cambio vigencia de póliza</a>
                        <div class="item" id ="menu_actualizacion_datos_poliza">
                            Actualización de datos de la póliza
                            <i class="dropdown icon"></i>
                            <div class="menu">
                                <a class="item {{ $active === 'datos_basicos_contacto' ? 'active' : '' }}"
                                    href="/tomador/poliza/{{ $id }}/datos_basicos_contacto/npolicy"
                                    data-href="/tomador/poliza/{{ $id }}/datos_basicos_contacto/npolicy"
                                    id="basic_data">Datos básicos de contacto</a>
                                <a class="item {{ $active === 'otros_datos' ? 'active' : '' }}"
                                    href="/tomador/poliza/{{ $id }}/otros_datos/npolicy"
                                    data-href="/tomador/poliza/{{ $id }}/otros_datos/npolicy"
                                    id="other_data">Otros
                                    datos</a>
                            </div>
                        </div>
                        <a class="item {{ $active === 'cambio_forma_pago' ? 'active' : '' }}"
                            href="/tomador/poliza/{{ $id }}/cambio_forma_pago/npolicy"
                            data-href="/tomador/poliza/{{ $id }}/cambio_forma_pago/npolicy"
                            id="payment_change">Cambio forma de pago</a>
                        <a class="item {{ $active === 'cancelación' ? 'active' : '' }}"
                            href="/tomador/poliza/{{ $id }}/cancelación/npolicy"
                            data-href="/tomador/poliza/{{ $id }}/cancelación/npolicy"
                            id="cancel">Cancelación</a>
                        <a class="item {{ $active === 'rehabilitación' ? 'active' : '' }}"
                            href="/tomador/poliza/{{ $id }}/rehabilitación/npolicy"
                            data-href="/tomador/poliza/{{ $id }}/rehabilitación/npolicy"
                            id="rehabilitation">Rehabilitación</a>
                        <a class="item {{ $active === 'svariaciones' ? 'active' : '' }}"
                            href="/tomador/poliza/{{ $id }}/variaciones/npolicy"
                            data-href="/tomador/poliza/{{ $id }}/variaciones/npolicy"
                            id="rehabilitation">Seguimiento variaciones solicitadas</a>
                    </div>
                </div>

                <a class="item {{ $active === 'estadisticas_reporte' ? 'active' : '' }}"
                    href="/tomador/poliza/{{ $id }}/estadisticas_reporte/npolicy"
                    data-href="/tomador/poliza/{{ $id }}/estadisticas_reporte/npolicy"
                    id="statistics">Estadísticas y
                    reportes</a>
                <a class="item falta {{ $active === 'descargar_formulario' ? 'active' : '' }}"
                   id="formularios_menu" >Descargar
                    formularios</a>
            </div>
        @endif
    </div>
    <div style="position: absolute; width: 100%">
        <div class="conten_logo">
            <p>
                Las soluciones de seguro que necesita, a un clic de distancia.</p>
            <img style="height: 61vh; margin: auto" src="{{ asset('images/mnk.png') }}" alt="">
        </div>
    </div>
</div>


<style>
    .button_autorizado {
        position: absolute;
        top: 62px;
        right: 26px;
    }

    .button_autorizado .icon {
        color: rgba(0, 0, 0, 0.7);
        transition: color 0.2s ease;
    }

    .button_autorizado:hover {
        /* background-color: rgba(0, 0, 0, 0.15); */
        transform: translateY(-2px);
    }

    .button_autorizado:hover .icon {
        color: #000000;
        /* color primario de Semantic UI */
    }


    .benefit-options {
        display: flex;
        flex-direction: column;
        gap: 8px;
        margin-left: 15px;
        /* Ajusta la separación del texto */
    }

    .benefit-options .ui.radio.checkbox {
        display: flex;
        align-items: center;
        gap: 5px;
        /* Espaciado entre el texto y el input */
    }

    label {
        font-weight: bold;
    }

    #policyholder {
        .grayed-input {
            pointer-events: none;
            background-color: #f0f0f0 !important;
            color: #888 !important;
            border: 1px solid #ddd !important;
            text-transform: none !important;
        }


    }

    .conten_logo {
        display: flex;
        justify-content: center;
        flex-direction: column;
        align-items: center;
        margin-bottom: 50px;

    }

    .hidden {
        display: none;
    }
</style>


<!-- CSS personalizado -->
<style>
    .benefit-options {
        display: flex;
        flex-direction: column;
        gap: 8px;
        margin-left: 15px;
        /* Ajusta la separación del texto */
    }

    .benefit-options .ui.radio.checkbox {
        display: flex;
        align-items: center;
        gap: 5px;
        /* Espaciado entre el texto y el input */
    }

    label {
        font-weight: bold;
    }

    .secondary_color {
        color: #91C845 !important;
    }
</style>

<script type="text/javascript">
    $(document).ready(function() {
        $('.ui.radio.checkbox').checkbox();

        $('input[name="benefit_collectivity"]').change(function(e) {
            if ($(this).val() === "yes") {
                let today = new Date().toISOString().split('T')[0]; // Obtiene la fecha de hoy

                Swal.fire({
                    title: "¿Agregar beneficio de colectividad?",
                    html: `
                    <label for="start_date">Fecha inicio colectividad:</label>
                    <input type="date" id="start_date" class="swal2-input" value="${today}" required>
                `,
                    icon: "question",
                    showCancelButton: true,
                    confirmButtonText: "<span style='color: white;'>Sí, agregar</span>",
                    cancelButtonText: "No, cancelar",
                    confirmButtonColor: "#000000", // Negro
                    cancelButtonColor: "#d33" // Rojo
                }).then((result) => {
                    if (result.isConfirmed) {
                        let startDate = document.getElementById("start_date").value;
                        if (!startDate) {
                            Swal.fire("Error", "Debe seleccionar una fecha de inicio.",
                                "error");
                            $('#benefit_no').prop('checked', true).trigger('change');
                        } else {
                            //Enviamos lapetición AJAX para ejecutar las acciones
                            loadingMain(true);

                            $.ajax({
                                url: '/tomador/benefit_colective/validate',
                                type: 'POST',
                                data: {
                                    id: selectedPolicyData.id,
                                    date_benefit: startDate,
                                },
                                success: function(response) {
                                    loadingMain(false);

                                    $(window).on('beforeunload', function() {
                                        loadingMain(false);
                                    })

                                    Swal.fire({
                                        icon: "success",
                                        title: "Importante",
                                        text: "Se agregó el beneficio de colectivad a esta póliza",
                                    }).then(() => {
                                        loadingMain(false);
                                    });
                                    return;

                                },
                                error: function(xhr) {
                                    loadingMain(false);

                                    console.error("Error:", xhr.responseJSON);
                                    Swal.fire({
                                        title: "Error",
                                        text: xhr.responseJSON.error,
                                        icon: "error"
                                    });
                                }
                            });
                        }
                    } else {
                        $('#benefit_no').prop('checked', true)

                    }
                });
            } else {
                if (!e.originalEvent) {
                    console.log("Evento programático ignorado.");
                    return;
                }
                openEmailModal();
            }
        });
    });

    $(document).ready(function() {
        $('.dropdown_menu').dropdown();


        $('.menu_autorizado .ui.dropdown').dropdown();

        $('a:not(.falta):not(.floating-button-question a):not(.floating-button a):not(.button_from_main):not(.no-loading-indicator)')
            .on('click', function(event) {
                // Mostrar el indicador de carga
                loadingMain(true);
            });

        $('#docNumberModal').modal({
            onHidden: function() {
                loadingMain(false);
            }
        });

        $('#verTutoriales').on('click', function() {
            $('#tutorialesModal').modal('show');
        });

        // Inicializar las pestañas
        $('.menu .item').tab();


    });
</script>

<script>
    $(document).ready(function() {
        // Asocia el evento click a los enlaces
        $('a.item.falta').on('click', function(e) {
            e.preventDefault(); // Evita la acción predeterminada del enlace

            // Muestra el mensaje de SweetAlert
            Swal.fire({
                icon: 'info',
                title: 'Funcionalidad en construcción',
                text: 'Esta funcionalidad aún está en desarrollo.',
                confirmButtonText: 'Aceptar'
            });
        });
    });
</script>


<script type="text/javascript">
    var poliza_id_select = '';
    var affiliate_id_select = '';
    var poliza_activity_select = '';
    var policySortData = @json(isset($policySortData) ? $policySortData : null);

    $(document).ready(function() {
        get_policy();

        @if (Auth::user()->view_tomador_autorizado())
            get_policy_autorizado();
        @endif

        // Si es RT Independiente (6) no se permite cargar planilla
        if (window?.selectedPolicyData?.work_modality_id == 6) {
            $(".reporte_planilla_menu").addClass("disabled");
            $(".reporte_planilla").hide();
        } else {
            $(".reporte_planilla_menu").removeClass("disabled");
            $(".reporte_planilla").show();
        }

        $('#select_policy').dropdown({
            fullTextSearch: true,
            searchFields: ['data-search'],
            onChange: function(value, text, $selectedItem) {

                $("#policy_menu").show();
                $("#benefits_colective").show();
                poliza_id_select = value
                poliza_activity_select = $selectedItem.data('activity');

                const policySortSelected = policySortData.find(item => item.id == value);

                // Si es RT Independiente (6) no se permite cargar planilla
                if (policySortSelected?.work_modality_id == 6) {
                    $(".reporte_planilla_menu").addClass("disabled");
                    $(".reporte_planilla").hide();
                } else {
                    $(".reporte_planilla_menu").removeClass("disabled");
                    $(".reporte_planilla").show();
                }

                updateLinks();
                change_policy(value);


            }
        });

    })

    function change_policy_two(params) {
        var url = window.location.pathname;
        var urlParts = url.split('/');
        var policyId = urlParts[urlParts.length - 1];

        if (!isNaN(policyId) && Number.isInteger(Number(policyId))) {

            urlParts[urlParts.length - 1] = params;
            // Crear la nueva URL con el valor actualizado
            var newUrl = urlParts.join('/');

            // Usar history.replaceState para cambiar la URL
            window.history.replaceState(null, null, newUrl);
            //recargar la página
            //location.reload();
        }
    }

    function change_policy(newStateId) {

        if (!affiliate_id_select) {
            console.error('No se encontró affiliate_id_select.');
            return;
        }  
        var url = window.location.pathname;

        // Expresión regular para detectar "/poliza/{número}/.../{número}"
        var regex = /\/poliza\/(\d+)\/.*\/(\d+)$/;
        var match = url.match(regex);

        // Expresión regular para detectar "/poliza/{número}/datos"
        var tomadorRegex = /\/poliza\/(\d+)\/datos$/;
        var matchTomador = url.match(tomadorRegex);

        if (match) {

            var policyNumber = match[1];
            var lastNumber = match[2];

            // Reemplazar en la URL
            var newUrl = url.replace(`/poliza/${policyNumber}/`, `/poliza/${affiliate_id_select}/`).replace(
                `/${lastNumber}`, `/${newStateId}`);

            // Usar history.replaceState para cambiar la URL sin recargar
            window.history.replaceState(null, null, newUrl);

            Swal.fire({
                title: "Procesando...",
                text: "Estamos actualizando su póliza. Por favor, espere un momento.",
                allowOutsideClick: false,
                allowEscapeKey: false,
                showConfirmButton: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            loadingMain(true);
            // Recargar la página
            location.reload();
        } else if (matchTomador) {

            var policyNumberTwo = matchTomador[1];

            var newUrlTwo = url.replace(`/poliza/${policyNumberTwo}/`, `/poliza/${affiliate_id_select}/`);

            window.history.replaceState(null, null, newUrlTwo);

            let selectedValue = $("#select_policy").dropdown("get value");
            $("#select_policy").addClass("disabled");

            // Actualizar solo los enlaces dentro de #policy_menu
            $("#policy_menu a").each(function() {
                var oldHref = $(this).attr("href");
                var oldDataHref = $(this).attr("data-href");

                if (oldHref) {
                    var newHref = oldHref.replace(`/poliza/${policyNumberTwo}/`,
                        `/poliza/${affiliate_id_select}/`);
                    $(this).attr("href", newHref);
                }

                if (oldDataHref) {
                    var newDataHref = oldDataHref.replace(`/poliza/${policyNumberTwo}/`,
                        `/poliza/${affiliate_id_select}/`);
                    $(this).attr("data-href", newDataHref);
                }
            });


             @if (Auth::user()->view_tomador_autorizado())
   //                           // 1) Tomamos la URL actual
  const urlparams = new URL(window.location);

  // 2) Seteamos (o reemplazamos) el parámetro npoliza
  urlparams.searchParams.set('idpoliza', newStateId);

window.history.replaceState({}, '', urlparams.toString());

     location.reload();
    @endif



            Swal.fire({
                title: "Procesando...",
                text: "Estamos actualizando su póliza. Por favor, espere un momento.",
                allowOutsideClick: false,
                allowEscapeKey: false,
                showConfirmButton: false,
                didOpen: () => {
                    Swal.showLoading();
                    setTimeout(() => {
                        Swal.close();

                        setTimeout(() => {
                            $("#select_policy").removeClass("disabled").dropdown(
                                "set selected", selectedValue);
                        }, 300);

                    }, 2000);
                }
            });


        }


    }

    function get_policy() {
        var url = window.location.pathname;
        var urlParts = url.split('/');
        var policyId = urlParts[urlParts.length - 1];

        // Validar si policyId es un número
        if (!isNaN(policyId) && Number.isInteger(Number(policyId))) {
            $('#select_policy').dropdown('set selected', policyId);
            $("#benefits_colective").show();
            poliza_id_select = policyId;
            isMenu(policyId);
            updateLinks()
        }
    }

      function get_policy_autorizado() {
    // 1) Inyectamos el valor desde Blade
    const npolicyid = "{{ $npoliza ?? '' }}"; 

    // 2) Intentamos convertirlo a número entero
    const policyId = parseInt(npolicyid, 10);

    // 3) Verificamos que sea un número entero
    if (!isNaN(policyId) && Number.isInteger(policyId)) {
      // 4) Le decimos al dropdown que seleccione esa opción
      $('#select_policy').dropdown('set selected', policyId);

      // 5) Mostramos el bloque de “benefits_colective” y actualizamos las variables de estado
      $("#benefits_colective").show();
      poliza_id_select = policyId;

      // 6) Llamamos a tus funciones de lógica adicionales
      isMenu(policyId);
      updateLinks();
    }
  }


    function isMenu(value) {
        if (value) {
            // Si se selecciona una póliza, mostrar el menú
            $("#policy_menu").show();
        } else {
            // Si no hay póliza seleccionada, ocultar el menú
            $("#policy_menu").hide();
        }
    }

    function capitalizeFullName(name) {
        return name.split(' ')
            .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
            .join(' ');
    }


    function constanciaName(params) {
        const type_cost = {
            'policy_certification_up_to_date': 'Certificado de póliza al día',
            'outstanding_sums_to_be_paid': 'Constancia de sumas pendientes por pagar',
            'employee_account_statement': 'Estado de cuenta del trabajador',
            'premiums_paid_certificate': 'Constancia de primas pagadas'
        };

        return type_cost[params]
    }

    function openDocNumberModalOrHandleRequest(endpoint) {
        if (endpoint === 'employee_account_statement') {
            $('#docNumberModal').modal('show'); // Mostrar la modal
        } else {
            handleEmailRequest(endpoint); // Llamar a handleEmailRequest directamente
        }
    }

    function submitDocNumber() {
        const doc_number = document.getElementById('doc_number').value;
        // Llama a handleEmailRequest con la cédula solo si es el endpoint específico
        handleEmailRequest('employee_account_statement', doc_number);

        $('#docNumberModal').modal('hide'); // Ocultar la modal después de confirmar
        //limpair el input
        document.getElementById('doc_number').value = '';
    }

    function cancelDocNumber() {
        $('#docNumberModal').modal('hide');
        loadingMain(false);
        // Limpiar el campo de entrada después de cancelar
        document.getElementById('doc_number').value = '';
    }

    function handleEmailRequest(endpoint, doc_number = null) {
        var id = poliza_id_select;

        if (endpoint === 'employee_account_statement' && doc_number) {
            // Aquí puedes agregar el código para manejar la cédula
            console.log("Número de cédula del trabajador:", doc_number);
        }

        if (window.selectedPolicyData.activityPolicy.state_id !== 20) {
            Swal.fire({
                icon: "warning",
                title: "Importante",
                text: "No se puede generar la constancia, el estado de la póliza es suspendida",
            }).then(() => {
                loadingMain(false);
            });
            return;
        } else {
            if (id) {
                Swal.fire({
                    title: '¡Su certificación está lista!',
                    text: `Por favor, elija el paso a seguir:`,
                    imageUrl: '/file/client_logo/logo_mnk.png',
                    imageHeight: 50,
                    imageWidth: 150,
                    showCancelButton: true,
                    confirmButtonColor: '#91C845',
                    cancelButtonColor: '#91C845',
                    confirmButtonText: 'Descargar documento',
                    cancelButtonText: 'Cancelar',
                    showDenyButton: true,
                    denyButtonText: 'Enviar correo',
                    denyButtonColor: '#91C845',
                    allowOutsideClick: true,
                    allowEscapeKey: true
                }).then((result) => {
                    if (result.isConfirmed) {
                        downloadDocument(id, endpoint, doc_number); // Llama a downloadDocument con cédula
                    } else if (result.isDenied) {
                        sendEmail(id, endpoint, doc_number); // Llama a sendEmail con cédula
                    } else if (result.dismiss) {
                        loadingMain(false);
                    }
                });

                // Agregar estilos para cambiar el color al pasar el mouse
                setTimeout(() => {
                    const style = document.createElement('style');
                    style.innerHTML = `
                        .swal2-confirm:hover,
                        .swal2-cancel:hover,
                        .swal2-deny:hover {
                            background-color: black !important;
                            border-color: black !important;
                        }
                    `;
                    document.head.appendChild(style);
                }, 100);
            }
        }

        loadingMain(false);
    }


    function convertirFecha(fechaStr) {
        const partesFecha = fechaStr.split('-');
        const fecha = new Date(partesFecha[0], partesFecha[1] - 1, partesFecha[2]);

        const opciones = {
            weekday: 'long',
            day: 'numeric',
            month: 'long',
            year: 'numeric'
        };

        // Formatear la fecha
        let fechaFormateada = new Intl.DateTimeFormat('es-ES', opciones).format(fecha);

        // Quitar la coma después del día de la semana, si existe
        fechaFormateada = fechaFormateada.replace(',', '');

        // Capitalizar la primera letra del día
        fechaFormateada = fechaFormateada.charAt(0).toUpperCase() + fechaFormateada.slice(1);

        return fechaFormateada;
    }

    function sendEmail(id, endpoint, doc_number) {
        if (id) {
            $.ajax({
                url: `/servicio/${id}/constancy_sort/${endpoint}`,
                method: 'POST',
                data: JSON.stringify({
                    doc_number: doc_number // Enviando la cédula en el cuerpo de la solicitud
                }),
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                success: function(data) {
                    if (data.status === 'success') {
                        loadingMain(false);
                        Swal.fire({
                            imageUrl: '/file/client_logo/logo_mnk.png',
                            imageHeight: 50,
                            imageWidth: 150,
                            title: "Correo enviado exitosamente",
                            text: `Hemos enviado un correo a la dirección que proporcionaste ({{ isset($affiliate_tomador) ? $affiliate_tomador->email : '' }}).\n\n   Si no lo ves en tu bandeja de entrada en los próximos minutos, por favor revisa tu carpeta de spam o correo no deseado, ya que es posible que llegue ahí.\n\n  Gracias por tu atención.`,
                            confirmButtonText: "Aceptar",
                            confirmButtonColor: "#91C845"
                        });
                    } else {
                        loadingMain(false);
                        Swal.fire({
                            icon: "error",
                            title: "Error",
                            text: "ERROR: " + data.message,
                        });
                    }
                },
                error: function(xhr, status, error) {
                    loadingMain(false);
                    if (xhr.status === 404) {
                        Swal.fire({
                            icon: "error",
                            title: "Afiliado no encontrado",
                            text: 'No se encontró un afiliado con la cédula proporcionada.',
                        });
                    } else {
                        Swal.fire({
                            icon: "error",
                            title: "Error",
                            text: 'Ocurrió un error al intentar descargar el documento.' + xhr
                                .responseText,
                        });
                    }
                },
                complete: function() {
                    loadingMain(false);
                }
            });


        } else {
            loadingMain(false);
            Swal.fire({
                icon: "info",
                title: "Sin resultados",
                text: "Por favor selecciona una póliza.",
            });
        }
    }

    function downloadDocument(id, endpoint, doc_number) {
        if (id) {
            $.ajax({
                url: `/servicio/${id}/constancy_sort/download_document/${endpoint}`,
                method: 'POST', // Cambiado a POST
                data: {
                    doc_number: doc_number // Enviando la cédula en el cuerpo de la solicitud
                },
                xhrFields: {
                    responseType: 'blob'
                },

                success: function(data, status, xhr) {
                    loadingMain(false);
                    if (xhr.status === 200) {
                        const blob = new Blob([data], {
                            type: 'application/pdf'
                        });
                        const link = document.createElement('a');
                        link.href = window.URL.createObjectURL(blob);
                        link.download =
                            `${constanciaName(endpoint)}.pdf`; // Nombre del archivo que se descargará
                        link.click();

                        Swal.fire({
                            imageUrl: '/file/client_logo/logo_mnk.png',
                            imageHeight: 50,
                            imageWidth: 200,
                            title: "¡Descarga exitosa!",
                            text: "El documento se ha descargado correctamente.",
                            confirmButtonText: "Aceptar",
                            confirmButtonColor: '#000000'
                        });
                    } else {
                        Swal.fire({
                            icon: "error",
                            title: "Error",
                            text: "Ocurrió un error al descargar el documento.",
                        });
                    }
                },
                error: function(data, status, xhr) {
                    loadingMain(false);
                    if (data.status === 404) {
                        Swal.fire({
                            icon: "error",
                            title: "Afiliado no encontrado",
                            text: 'No se encontró un afiliado con la cédula proporcionada.',
                        });
                    } else {
                        Swal.fire({
                            icon: "error",
                            title: "Error",
                            text: 'Ocurrió un error al intentar descargar el documento.',
                        });
                    }
                },
                complete: function() {
                    loadingMain(false);
                }
            });
        } else {
            loadingMain(false);
            Swal.fire({
                icon: "info",
                title: "Sin resultados",
                text: "Por favor selecciona una póliza.",
            });
        }
    }

    function updateLinks() {

        const selectedPolicyId = poliza_id_select;
        const selectedPolicy = policySortData.find(policy => policy.id == selectedPolicyId);
        // Verificar si se encontró la póliza seleccionada
        if (!selectedPolicy) {
            console.error('No se encontró la póliza seleccionada.');
            return;
        }

        // Almacenar los datos en la variable global
        window.selectedPolicyData = {
            id: selectedPolicy.id,
            consecutive: selectedPolicy.consecutive,
            activityPolicy: selectedPolicy.activityPolicy,
            activity_id: selectedPolicy.activity_id,
            affiliate_id: selectedPolicy.affiliate_id,
            validity_from: selectedPolicy.validity_from,
            validity_to: selectedPolicy.validity_to,
            benefit_colective: selectedPolicy.benefit_colective,
            email: selectedPolicy.email,
            work_modality_id: selectedPolicy.work_modality_id,
        };



        // Verificar si el campo tiene un valor o si es null
        let benefitValue = selectedPolicy.benefit_colective ? selectedPolicy.benefit_colective.trim().toLowerCase() :
            "no";

        // Marcar el radio button correspondiente
        if (benefitValue === "si") {
            $('#benefit_yes').prop('checked', true);
        } else {
            $('#benefit_no').prop('checked', true); // Si es "no" o null, marcar 'No'
        }

        // Bloquear checkboxes si el usuario no es admin
        const areaId = @json(auth()->user()->area_id);

        if (areaId != 1) {
            $('#benefit_yes, #benefit_no').prop('disabled', true);
        }

        // Refrescar los checkboxes de Semantic UI
        $('.ui.radio.checkbox').checkbox();

        if (selectedPolicy.activityPolicy.state_id === 20) {
            $('#state_police').val('Poliza emitida activa')
        }
        if (selectedPolicy.activityPolicy.state_id === 21) {
            $('#state_police').val('Póliza suspendida')

            const menus = [
                'menu_reporte_accidente',
                'menu_reporte_planilla',
                'menu_constancias_polizas',
                'menu_consultas_polizas',
                'menu_actualizacion_datos_poliza'
            ];

            menus.forEach(id => {
                const menu = document.getElementById(id);
                if (menu) {
                    menu.style.pointerEvents = 'none';
                    menu.style.setProperty('opacity', '0.5', 'important');
                    menu.style.setProperty('filter', 'grayscale(100%)', 'important');
                    menu.style.cursor = 'not-allowed';
                    menu.classList.add('disabled'); // si usas Semantic UI
                }
            });

            const bloqueables = [
                'statistics',
                'formularios_menu',
                'payment_change',
                'validity_change',
                'cancel'
            ];

            bloqueables.forEach(id => {
                const el = document.getElementById(id);
                if (el) {
                    el.style.pointerEvents = 'none';
                    el.style.setProperty('opacity', '0.5', 'important');
                    el.style.setProperty('filter', 'grayscale(100%)', 'important');
                    el.style.cursor = 'not-allowed';
                }
            });

        } else {
            $('#state_police').val('Póliza activa');

            const menus = [
                'menu_reporte_accidente',
                'menu_reporte_planilla',
                'menu_constancias_polizas',
                'menu_consultas_polizas',
                'menu_actualizacion_datos_poliza'
            ];

            menus.forEach(id => {
                const menu = document.getElementById(id);
                if (menu) {
                    menu.style.pointerEvents = 'auto';
                    menu.style.removeProperty('opacity');
                    menu.style.removeProperty('filter');
                    menu.style.cursor = 'pointer';
                    menu.classList.remove('disabled');
                }
            });

            const desbloqueables = [
                'statistics',
                'formularios_menu',
                'payment_change',
                'validity_change',
                'cancel'
            ];

            desbloqueables.forEach(id => {
                const el = document.getElementById(id);
                if (el) {
                    el.style.pointerEvents = 'auto';
                    el.style.removeProperty('opacity');
                    el.style.removeProperty('filter');
                    el.style.cursor = 'pointer';
                }
            });
        }


        if (selectedPolicy.activityPolicy.state_id === 185) {
            $('#state_police').val('Póliza suspendida temporalmente');
        }

        affiliate_id_select = selectedPolicy.activityPolicy.affiliate_id;

        $('#affiliate_name').val(selectedPolicy.full_name)
        $('#affiliate_email').val(selectedPolicy.email)

        // Actualizar los href de los enlaces con el selectedPolicyId
        $('a.item').each(function() {
            let originalHref = $(this).data('href');
            // Verificar si el originalHref está definido
            if (originalHref) {
                let updatedHref = originalHref.replace('npolicy', poliza_id_select);
                $(this).attr('href', updatedHref);
            }
        });
    }

    function openEmailModal() {
        if (!poliza_activity_select) {
            console.error("Error: policieId es undefined.");
            return;
        }

        // Actualizar el valor del input oculto
        $('#policy_id_modal').val(poliza_activity_select);

        $('#colectividadModal').modal('refresh').modal({
            inverted: false,
            autofocus: false,
            closable: false,
            onHidden: function() {
                $('#benefit_yes').prop('checked', true)
            }
        }).modal('show');
    }
</script>

<script type="text/javascript">
    $('#report_spreadsheet').on('click', function(e) {
        e.preventDefault();
        const originalHref = $(this).data('href');
        const updatedHref = originalHref.replace('npolicy', poliza_id_select);

        // Mostrar alerta de confirmación 
        Swal.fire({
            title: '¿Está seguro?',
            text: "¿Quieres reportar la planilla sin modificaciones, esto creara una copia de la ultima planilla registrada?",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#000000',
            cancelButtonColor: '#000000',
            confirmButtonText: 'Aceptar',
            cancelButtonText: 'Cancelar'
        }).then((result) => {
            if (result.isConfirmed) {
                // Redirigir a la URL especificada en data-href
                window.location.href = updatedHref;
            } else {
                loadingMain(false);
            }
        });
    });

    $('#report_spreadsheet_modified').on('click', function(e) {

        e.preventDefault();
        const originalHref = $(this).data('href');
        const updatedHref = originalHref.replace('npolicy', poliza_id_select);
        // Mostrar alerta de confirmación
        Swal.fire({
            title: '¿Está seguro?',
            text: "¿Quieres reportar la planilla con modificaciones, esto creara una copia de la ultima planilla registrada?",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#000000',
            cancelButtonColor: '#000000',
            confirmButtonText: 'Aceptar',
            cancelButtonText: 'Cancelar'
        }).then((result) => {
            if (result.isConfirmed) {
                // Redirigir a la URL especificada en data-href
                window.location.href = updatedHref;
            } else {
                loadingMain(false);
            }
        });
    });

    $('#suspend_policy').on('click', function(e) {

        e.preventDefault();
        const originalHref = $(this).data('href');
        const updatedHref = originalHref.replace('npolicy', poliza_id_select);
        // Mostrar alerta de confirmación
        Swal.fire({
            title: '¿Está seguro?',
            text: "¿Quieres suspender la póliza de forma temporal?",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Sí, suspender',
            cancelButtonText: 'Cancelar'
        }).then((result) => {
            if (result.isConfirmed) {
                // Redirigir a la URL especificada en data-href
                window.location.href = updatedHref;
            } else {
                loadingMain(false);
            }
        });
    });

    $('#inclusion_empleado').on('click', function(e) {
        e.preventDefault();
        loadingMain(true);
        const $this = $(this);
        $.ajax({
            url: '/tomador/benefit_colective/validate/include_worker',
            type: 'POST',
            data: {
                id: selectedPolicyData.id,
            },
            success: function(response) {

                loadingMain(false);


                if (response.success === true) {
                    Swal.fire({
                        icon: "warning",
                        title: "Importante",
                        text: 'Póliza con beneficio de Colectividad, no reporta inclusión de personas trabajadoras.',
                        confirmButtonText: 'Aceptar', // Nuevo texto del botón
                        confirmButtonColor: '#000000' // Color negro para el botón
                    });

                    return;

                } else {
                    e.preventDefault();
                    const originalHref = $this.data('href');
                    const updatedHref = originalHref.replace('npolicy', poliza_id_select);
                    // Mostrar alerta de confirmación
                    Swal.fire({
                        title: '¿Está seguro?',
                        text: "¿Quieres incluir un empleado en la póliza?",
                        icon: 'warning',
                        showCancelButton: true,
                        confirmButtonColor: '#000000',
                        cancelButtonColor: '#000000',
                        confirmButtonText: 'Sí',
                        cancelButtonText: 'Cancelar'
                    }).then((result) => {
                        if (result.isConfirmed) {
                            // Redirigir a la URL especificada en data-href
                            window.location.href = updatedHref;
                        } else {
                            loadingMain(false);
                        }
                    });
                }
            },
            error: function(xhr) {
                loadingMain(false);

                console.error("Error:", xhr.responseJSON);
                Swal.fire({
                    title: "Error",
                    text: xhr.responseJSON.error,
                    icon: "error"
                });
            }
        });
    });

    $('#spreadsheet').on('click', function(e) {

        e.preventDefault();
        const originalHref = $(this).data('href');
        const updatedHref = originalHref.replace('npolicy', poliza_id_select);

        const selectedPolicyId = poliza_id_select;
        const {
            type_currency
        } = policySortData.find(policy => policy.id == selectedPolicyId);

        // Mostrar alerta de confirmación
        Swal.fire({
            text: `Al momento de reportar la planilla, recuerde que su póliza fue emitida en ${type_currency === 'USD' ? '"Dólares"' : '"Colones"'}, por tal motivo los salarios que reporte deben estar en la misma moneda`,
            icon: 'warning',
            confirmButtonColor: '#000000',
        }).then((result) => {
            // Redirigir a la URL especificada en data-href
            window.location.href = updatedHref;

        });
    });
</script>
