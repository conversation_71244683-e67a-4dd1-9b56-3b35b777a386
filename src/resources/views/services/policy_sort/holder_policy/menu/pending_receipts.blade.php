@php
    use Carbon\Carbon;
@endphp

@extends('layouts.menu_tomador')

@section('module_content')
    <br>
    <div class="ui basic segment">
        <div class="active content">
            <table class="ui celled sortable striped very compact very small table">
                <thead>
                    <tr>
                        <!-- <th># de Póliza</th>-->
                        <th>Póliza SORT</th>
                        <th>Nombre del tomador</th>
                        <th># de recibo</th>
                        <th>Tipo de recibo</th>
                        <th>Monto a pagar</th>
                        <th>Fecha de emisión del recibo</th>
                        <th>Fecha límite de pago recibo</th>
                        <th>Días para vencimiento</th>
                        <th>Estado del pago</th>
                        <th>Acción</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach ($listado_recibos as $recibo)
                        <tr>
                            <td>SORT - {{ sprintf('%04d', $recibo->consecutive) }}</td>
                            <td>{{ $recibo->nombre_tomador }}</td>
                            <td>{{ mb_strtoupper(mb_substr($recibo->no_recibo, 0, 1), 'UTF-8') . mb_strtolower(mb_substr($recibo->no_recibo, 1), 'UTF-8') }}
                            </td>
                            <td>{{ $TYPE_RECEIPT[$recibo->tipo_recibo] ?? '' }}</td>
                            <td>{{$MONEY_TYPE[$recibo->tipo_moneda ?? '']['symbol'] ?? ''}}{{ number_format($recibo->monto_a_pagar, 2, ',', '.') }}</td>
                            <td>
                                {{ $recibo->fecha_emision_recibo ? ucfirst(strftime('%A %e de %B del %Y', strtotime($recibo->fecha_emision_recibo))) : '' }}
                            </td>

                            <td>
                                {{ $recibo->fecha_limite_pago ? ucfirst(strftime('%A %e de %B del %Y', strtotime($recibo->fecha_limite_pago))) : '' }}
                            </td>

                            <td>
                                @php
                                    if ($recibo->fecha_limite_pago) {
                                        $fechaLimitePago = Carbon::parse($recibo->fecha_limite_pago);
                                        $fechaHoy = Carbon::now();

                                        if ($fechaLimitePago->isPast()) {
                                            $diasParaVencimiento = 0; // Si la fecha de vencimiento ha pasado, muestra 0
                                        } else {
                                            // Calcular la diferencia en días hacia la fecha límite y sumar 2
                                            $diasParaVencimiento = $fechaHoy->diffInDays($fechaLimitePago) + 1; // Sumar 2 para incluir el día de hoy y un extra
                                        }
                                    } else {
                                        $diasParaVencimiento = '';
                                    }
                                @endphp

                                {{ $diasParaVencimiento }}
                            </td>
                            <td>{{ $RECEIPT_STATE[$recibo->estado_pago] ?? '' }}</td>
                            <td>
                                @if ($recibo->estado_pago === \App\PolicySortCollection::PAYMENT_STATUS_PENDING)
                                    <a class="ui clickable-link pagar"
                                        href="{{ secure_url('/servicio/' . $recibo->id_actividad_cobros . '/policy_sort_collection/pago_poliza') }}">
                                        <i class="arrow alternate circle right icon"></i>
                                    </a>
                                @endif
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
        <br>
        <!-- Paginación -->
        @if ($listado_recibos->hasPages())
            <div class="ui grid">
                <div class="eight wide column left aligned">
                    <p>Total de registros: {{ $listado_recibos->total() }}</p>
                </div>
                <div class="eight wide column right aligned">
                    <div class="ui pagination menu">
                        {{ $listado_recibos->links() }}
                    </div>
                </div>
            </div>
        @endif
    </div>
    <br>

    <script>
        $('.pagar').popup({
            boundary: 'body',
            content: 'Pagar',
            position: 'top center',
            lastResort: 'bottom center' // Si no cabe en la posición inicial, se ajusta a esta
        });
    </script>

    @if (\Session::has('success'))
        <div class="ui green message" id="success-message">
            <i class="close icon" id="close-success-message"></i>
            <div class="header">
                {!! \Session::get('success') !!}
            </div>
        </div>
    @endif

    <div class="ui modal" id="success-modal">
        <div class="header">¡Póliza Creada Exitosamente!</div>
        <div class="content">
            <p>Póliza creada exitosamente al emitir en línea.</p>
            <p>¡Su póliza ha sido emitida con éxito! El número de contrato es SORT-{{ $npoliza ?? '' }}.</p>
            <p>¡Agradecemos sinceramente su preferencia y confianza en nosotros!</p>
        </div>
        <div class="actions">
            <button class="ui secondary button" id="close-modal">Aceptar</button>
        </div>
    </div>

    <div class="ui modal" id="success-modal-spreadsheets">
        <div class="header">¡Póliza Creada Exitosamente!</div>
        <div class="content">
            <p>¡Su póliza ha sido emitida exitosamente! El número de contrato es el SORT-{{ $npoliza ?? '' }}.</p>
            <p>Sin embargo, hemos notado que está pendiente la carga del reporte de planilla. Le agradecemos mucho que, por
                favor, nos envíe este documento antes del inicio de operaciones.</p>
            <p>De lo contrario, en caso de que ocurra un riesgo laboral a uno de sus colaboradores, tendremos que tramitar
                el evento como “no asegurado” y proceder al cobro del costo total de su atención.
                Si tiene alguna consulta adicional o necesita asistencia, por favor, contáctenos al 4102-7600. ¡Será un
                gusto
                servirle!</p>
            <p>¡Agradecemos sinceramente su preferencia y confianza en nosotros!</p>
        </div>
        <div class="actions">
            <button class="ui secondary button" id="close-modal-spreadsheets">Aceptar</button>
        </div>
    </div>

    <script>
        $(document).ready(function() {

            @if (\Session::has('success'))
                @if (isset($messageSpreadsheets) && $messageSpreadsheets == true)
                    $('#success-modal-spreadsheets').modal('show');
                @else
                    $('#success-modal').modal('show');
                @endif
            @endif

            $('#close-modal').on('click', function() {
                $('#success-modal').modal('hide');
            });

            $('#close-modal-spreadsheets').on('click', function() {
                $('#success-modal-spreadsheets').modal('hide');
            });
        });
    </script>
@endsection
