@extends('layouts.menu_tomador')

@section('module_content')
    <div class="ui basic segment">

        <div class="ui message">
            <div class="header">Estadísticas y Reportes</div>
            <p>En este apartado se encuentran las estadísticas y reportes del tomador.</p>
        </div>

        <div class="ui secondary segment">
            <form class="ui form">

                <div class="three fields">
                    {{-- Cantidad de asegurados --}}
                    <div class="field">
                        <table class="ui celled table">
                            <thead>
                                <tr>
                                    <th>Cantidad de asegurados</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>{{ $count_affiliates }}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    {{-- Cantidad de asegurados por sexo --}}
                    <div class="field">
                        <canvas id="chart1"></canvas>
                    </div>
                    {{-- prima total anual --}}
                    <div class="field">
                        <table class="ui celled table">
                            <thead>
                                <tr>
                                    <th>Prima total anual (en colones)</th>
                                    <th>Prima total anual (en dólares)</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>{{ $amount_policy_colon }}</td>
                                    <td>{{ $amount_policy_dolars }}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="three fields">
                    {{-- Cantidad de abonos --}}
                    <div class="field">
                        <div class="field">
                            <table class="ui celled table">
                                <thead>
                                    <tr>
                                        <th>Cantidad de abonos (en colones)</th>
                                        <th>Cantidad de abonos (en dólares)</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>{{ $number_payments_colon }}</td>
                                        <td>{{ $number_payments_dolars }}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    {{-- Cantidad de accidentes laborales --}}
                    <div class="field">
                        <table class="ui celled table">
                            <thead>
                                <tr>
                                    <th>Cantidad de accidentes laborales</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>{{ $count_gis }}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    {{-- Monto Total Pagado en accidentes laborales --}}
                    <div class="field">
                        <table class="ui celled table">
                            <thead>
                                <tr>
                                    <th>Monto total pagado en accidentes laborales (en colones)</th>
                                    <th>Monto total pagado en accidentes laborales (en dólares)</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>{{ $paid_workplace_accidents_colon }}</td>
                                    <td>{{ $paid_workplace_accidents_dolars }}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="two fields">
                    {{-- Cantidad de accidentes laborales con incapacidad --}}
                    <div class="field">
                        <table class="ui celled table">
                            <thead>
                                <tr>
                                    <th>Cantidad de accidentes laborales con incapacidad</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>{{ $count_temporary_incapacities }}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    {{-- Siniestralidad total (Accidentes reprotados/cantidad de asegurados) --}}
                    <div class="field">
                        <canvas id="chart2"></canvas>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <!-- Scripts para gráficos -->
    <script>
        $(document).ready(function() {
            // {{-- Iteramos por cada gráfico en la lista de datos $chartData --}}
            @foreach ($chartData as $index => $chart)

                // {{-- Guardamos los datos del gráfico en una variable única --}}
                let chartData_{{ $index }} = @json($chart['data']);

                // {{-- Seleccionamos el canvas (lienzo) donde se dibujará el gráfico --}}
                let ctx_{{ $index }} = $('#{{ $chart['canvasId'] }}')[0].getContext('2d');

                // {{-- Creamos la configuración para el gráfico (tipo, datos y opciones) --}}
                let config_{{ $index }} = {
                    type: '{{ $chart['type'] }}',
                    // {{-- Tipo de gráfico (ej. 'pie', 'bar') --}}
                    data: chartData_{{ $index }},
                    // {{-- Datos del gráfico --}}
                    options: @json($chart['options'])
                    //  {{-- Opciones del gráfico (como leyenda, ejes, etc.) --}}
                };

                // {{-- Creamos e inicializamos el gráfico usando la configuración --}}
                new Chart(ctx_{{ $index }}, config_{{ $index }});
            @endforeach
        });
    </script>
@endsection
