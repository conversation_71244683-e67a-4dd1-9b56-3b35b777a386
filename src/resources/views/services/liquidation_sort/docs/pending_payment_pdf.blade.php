<!DOCTYPE html>
<html lang="es">

<head>

    <meta content="text/html; charset=utf-8" http-equiv="Content-Type" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type="text/css">
        * {
            font-family: 'Arial', sans-serif;
            font-size: 8pt;
            box-sizing: border-box;
        }

        body {
            margin: 0;
            padding: 0;
            text-align: justify;
        }

        .header {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            padding: 10px 15px;
            text-align: center;
            background-color: #ffffff;
            z-index: 100;
            height: 10px;
        }

        .footer {
            position: fixed;
            bottom: 0;
            left: 0;
            width: 100%;
            text-align: center;
            background-color: #ffffff;
            z-index: 100;
            height: 80px;
        }

        .content_body {
            margin-top: 20px;

            position: relative;
            margin-bottom: 50px;
        }

        .header img {
            width: auto;
            margin-top: -20px
        }


        table,
        th,
        td {
            border: 0.5px solid black;
        }

        table.no-border,
        table.no-border th,
        table.no-border td,
        table.no-border td b {
            font-size: 7pt;
            border: none;
            text-align: center;
        }

        th,
        td {
            padding: 1.5px;
            padding-bottom: 3px;
        }

        table {
            border-collapse: collapse;
            width: 100%;
        }

        th {
            text-align: center;
            background: #BDf;
        }

        .title_custom {
            text-align: center;
            background-color: #92C846;
            color: black;
        }

        .title-border {
            width: 400px;
            position: absolute;
            left: -23px;
            top: -10px;
        }

        .logo-image {
            max-width: 100%;
            height: 130px !important;
        }
        .td-padding-izq {
            padding-left: 10px;
        }

    </style>
</head>

<body>
<header class="header">
    <script type="text/php">
        if (isset($pdf)) {
            $x = $pdf->get_width() - 90;
            $y = 40;
            $text = "Pág {PAGE_NUM} de {PAGE_COUNT}";
            $font = 'Arial';
            $size = 8;
            $color = array(0,0,0);
            $word_space = 0.0;  //  default
            $char_space = 0.0;  //  default
            $angle = 0.0;   //  default
            $pdf->page_text($x, $y, $text, $font, $size, $color, $word_space, $char_space, $angle);
        }
    </script>

</header>

<footer class="footer">
    <img src="{{ public_path('images/mnk_footer.png') }}" alt="pie de documento" style="width: 100%;">
</footer>

<section class="content_body">
    <!--Creación del encabezado-->
    <table
            style="width: 100%; border: none; table-layout: fixed; font-size: 13px !important;margin-top: 50px !important;">
        <tbody>
        <tr>
            <td style="width: 30%; padding: 10px; border: none; text-align: left; vertical-align: top;">
                <img src="{{ public_path('images/borde_sort.png') }}" alt="" class="title-border"
                     style="height: 130px;">

                <div style="width: 70% !important;padding: 18px 28px;">
                    <div style="margin-bottom: 2px">
                        LIQUIDACIÓN DE PÓLIZA
                    </div>
                    <div style="font-weight: bold;font-size: 15px !important; ">
                        SEGURO OBLIGATORIO DE RIESGOS DEL TRABAJO
                    </div>
                </div>
            </td>

            <!-- Logo celda con ancho del 25% -->
            <td style="width: 25%; padding: 10px; border: none; text-align: right; height: auto">
                <img src="{{ public_path('images/mnk.png') }}" alt="Logo" class="logo-image"
                     style="height: 90px; width: 110px">
            </td>
        </tr>
        </tbody>
    </table>

    <!--Creación del contenido-->
    <table>
        <tr>
            <td colspan="4" style="text-align: center" class="title_custom">
                <p><strong  style="font-size: 15px;">SEGURO OBLIGATORIO DE RIESGOS DEL TRABAJO</strong></p>
                <p><strong style="font-size: 15px;">Liquidación de la póliza N° {{$policySort->formatSortNumber() ?? '0000'}}</strong></p>
            </td>
        </tr>
        <tr>
            <td colspan="2" class="td-padding-izq">
                <p><strong>Período:</strong></p>
                <p>{{isset($policySort) ? ( ucfirst(strftime('%A %e de %B del %Y',strtotime($policySort->validity_from ?? ''))) .' al '. ucfirst(strftime('%A %e de %B del %Y',strtotime($policySort->validity_to ?? '' )))) : ''}}</p>
            </td>
            <td colspan="2" class="td-padding-izq">
                <p><strong>Tomador:</strong></p>
                <p>{{ isset($policySort) ? mb_convert_case(mb_strtolower($policySort->activity->affiliate->full_name ?? ''), MB_CASE_TITLE, "UTF-8") : '' }}</p>
            </td>
        </tr>
        <tr>
            <td colspan="3" class="td-padding-izq">
                <p><strong>Correo electrónico:</strong></p>
            </td>
            <td colspan="1" style="text-align: center;">
                <p>{{ $policySort->activity->affiliate->email ?? '' }}</p>
            </td>
        </tr>
        <tr>
            <td colspan="4" style="padding-top: 20px"></td>
        </tr>
        <tr>
            <td colspan="3" class="td-padding-izq">Proyección de salarios del periodo:</td>
            <td colspan="1" class="td-padding-izq">
                @include('colones', ['currency' => $policySort->type_currency])
                {{
                 number_format(($policySort->salary_projection ?? 0)*12 , 2, ',', '.')
                }}
            </td>
        </tr>
        <tr>
            <td colspan="3" class="td-padding-izq">Salarios reportados del período:</td>
            <td colspan="1" class="td-padding-izq">
                @include('colones', ['currency' => $policySort->type_currency])
                {{
                number_format($activity->liquidation_sort->reported_salaries ?? 0, 2, ',', '.')
                }}
            </td>
        </tr>
        @php
            $mes = 1; // Inicia desde 1 para el primer registro después del ignorado
            $primero = true; // Variable para saltar el primer registro
        @endphp

        @foreach($policySpreadsheet as $index => $row)
            @if($primero)
                @php $primero = false; continue; @endphp
            @endif

            <tr>
                <td colspan="2" class="td-padding-izq">Mes {{ $mes }}</td> <!-- El mes ahora es secuencial -->
                <td colspan="1" class="td-padding-izq">
                    @include('colones', ['currency' => $policySort->type_currency])
                    {{
                        number_format($row->total_salaries ?? 0, 2, ',', '.')
                    }}
                </td>
                <td colspan="1"></td>
            </tr>

            @php $mes++; @endphp
        @endforeach
        <tr>
            <td colspan="2" class="td-padding-izq">Diferencia:</td>
            <td colspan="1"></td>
            <td colspan="1" class="td-padding-izq">
                @include('colones', ['currency' => $policySort->type_currency])
                {{ number_format( (($policySort->salary_projection ?? 0)*12)-($activity->liquidation_sort->reported_salaries ?? 0) , 2, ',', '.') }}
            </td>
        </tr>
        <tr>
            <td colspan="2" class="td-padding-izq"><b>Tarifa del período</b></td>
            <td colspan="1"></td>
            <td colspan="1" class="td-padding-izq">{{ number_format( ($activity->liquidation_sort->tem) ?? 0, 2, ',', '.') }}%</td>
        </tr>
        <tr>
            <td colspan="2" class="td-padding-izq">Sobrante (Faltante) de prima del período:</td>
            <td colspan="1"></td>
            <td colspan="1" class="td-padding-izq">
                @include('colones', ['currency' => $policySort->type_currency])
                {{ number_format( ($activity->liquidation_sort->premium_surcharge ?? 0) , 2, ',', '.') }}
            </td>
        </tr>
        <tr>
            <td colspan="2" class="td-padding-izq" >Otras sumas adeudadas</td>
            <td colspan="1"></td>
            <td colspan="1" class="td-padding-izq">
                @include('colones', ['currency' => $policySort->type_currency])
                {{ number_format( ($activity->liquidation_sort->other_amounts_owed) ?? 0, 2, ',', '.') }}
            </td>
        </tr>
        <tr>
            <td colspan="2" class="td-padding-izq">Liquidaciónes períodos anteriores</td>
            <td colspan="1"></td>
            <td colspan="1" class="td-padding-izq">
                @include('colones', ['currency' => $policySort->type_currency])
                {{ number_format( 0, 2, ',', '.') }}
            </td>
        </tr>
        <tr>
            <td colspan="2" class="td-padding-izq">Aumentos de seguro pendientes</td>
            <td colspan="1"></td>
            <td colspan="1" class="td-padding-izq">
                @include('colones', ['currency' => $policySort->type_currency])
                {{number_format( ($activity->liquidation_sort->pending_insurance_increases) ?? 0, 2, ',', '.') }}
            </td>
        </tr>
        <tr>
            <td colspan="2" class="td-padding-izq">Casos no asegurados </td>
            <td colspan="1"></td>
            <td colspan="1" class="td-padding-izq">
                @include('colones', ['currency' => $policySort->type_currency])
                {{ number_format( 0, 2, ',', '.') }}
            </td>
        </tr>
        <tr>
            <td colspan="2" class="td-padding-izq"><strong>Total Prima a cobrar (o a devolver) </strong></td>
            <td colspan="1"></td>
            <td colspan="1" class="td-padding-izq">
                @include('colones', ['currency' => $policySort->type_currency])
                {{number_format( ($activity->liquidation_sort->settlement_result) ?? 0, 2, ',', '.') }}
            </td>
        </tr>
        <tr>
            <td colspan="4" class="td-padding-izq">
                <p>En caso de cualquier consulta adicional o asistencia, por favor contáctenos al <b>4102-7600</b>. ¡Será un gusto servirle!</p>
                @if(isset($cobrar) && $cobrar)
                    <p>
                        Favor realizar el pago de la prima adeudada en un plazo no mayor a 10 días hábiles, contados a partir de la fecha de la presente notificación. De no efectuarse dicho pago, MNK Seguros iniciará las gestiones de cobro administrativo o judicial correspondientes.
                    </p>
                @else
                    <p>
                        Este sobrante de prima será depositado en la cuenta bancaria consignada en la póliza para este propósito, en un plazo no mayor de 10 días hábiles posteriores a la fecha de la presente notificación.
                    </p>
                @endif
            </td>
        </tr>
    </table>

</section>

</body>

</html>
