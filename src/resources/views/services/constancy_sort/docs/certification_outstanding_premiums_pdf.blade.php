<!DOCTYPE html>
<html lang="es">

<head>
    <meta charset="UTF-8">
    <title>Certificado de Seguro</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            margin: 0;
            padding: 0;
        }

        .content {
            margin-top: 20px;

            position: relative;
            margin-bottom: 50px;
        }

        .highlight {
            font-weight: bold;
            color: red;
        }

        .info-list {
            margin-top: 20px;
        }

        .info-list li {
            margin-bottom: 8px;
        }

        .coverage-section {
            margin-top: 30px;
            padding: 15px;
            border: 1px solid #000;
            border-radius: 10px;
            background-color: #fff;
            /* Fondo blanco para el contenido */
            color: #000;
            /* Texto negro para el contenido */
        }

        .coverage-header {
            background-color: #92C846;
            border: 1px solid #000;
            color: #000;
            padding: 10px;
            font-weight: bold;
            text-align: center;
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
        }

        .coverage-content {
            padding: 10px;
            text-align: justify;
        }

        .coverage-content table {
            width: 100%;
            border-collapse: collapse;
            background-color: #fff;
            /* Fondo blanco para la tabla */
        }

        .coverage-content th,
        .coverage-content td {
            border: 1px solid #000;
            padding: 4px; /* Reducido de 8px. Prueba con 4px o 5px. */
            text-align: center;
            /* ... */
        }

        .coverage-note {
            font-weight: bold;
        }

        .header {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            padding: 10px 15px;
            text-align: center;
            background-color: #ffffff;
            z-index: 100;
            height: 10px;
        }

        .footer {
            position: fixed;
            bottom: 0;
            left: 0;
            width: 100%;
            text-align: center;
            background-color: #ffffff;
            z-index: 100;
            height: 80px;
        }

        .align-right {
            text-align: right;
        }

        .title-border {
            width: 400px;
            position: absolute;
            left: -23px;
            top: -10px;
        }

        .logo-image {
            max-width: 100%;
            height: 130px !important;
        }
    </style>
</head>

<body>

    <header class="header">
        <script type="text/php">
        if (isset($pdf)) {
            $x = $pdf->get_width() - 90;
            $y = 40;
            $text = "Pág {PAGE_NUM} de {PAGE_COUNT}";
            $font = 'Arial';
            $size = 8;
            $color = array(0,0,0);
            $word_space = 0.0;  //  default
            $char_space = 0.0;  //  default
            $angle = 0.0;   //  default
            $pdf->page_text($x, $y, $text, $font, $size, $color, $word_space, $char_space, $angle);
        }
    </script>

    </header>

    <div class="content">
        <table
            style="width: 100%; border: none; table-layout: fixed; font-size: 13px !important;margin-top: 50px !important;">
            <tbody>
                <tr>
                    <td style="width: 30%; padding: 10px; border: none; text-align: left; vertical-align: top;">
                        <img src="{{ public_path('images/borde_sort.png') }}" alt="" class="title-border"
                            style="height: 130px;">

                        <div style="width: 70% !important;padding: 18px 28px;">
                            <div style="margin-bottom: 2px">
                                CONSTANCIA DE
                            </div>
                            <div style="font-weight: bold;font-size: 15px !important; ">
                                PRIMAS PENDIENTES
                            </div>
                        </div>
                    </td>

                    <!-- Logo celda con ancho del 25% -->
                    <td style="width: 25%; padding: 10px; border: none; text-align: right; height: auto">
                        <img src="{{ public_path('images/mnk.png') }}" alt="Logo" class="logo-image"
                            style="height: 90px; width: 110px">
                    </td>
                </tr>
            </tbody>
        </table>
        <p>
            Mediante la presente se hace constar que, según nuestros registros,
            {{ ucwords(mb_strtolower($policy_sort->activity->affiliate->full_name ?? '')) }} con número de
            identificación {{ $policy_sort->activity->affiliate->doc_number ?? '' }}, póliza {{$policy_sort->formatNumberConsecutive()}}, adeuda a 
            MNK Seguros las primas que se detalla a continuación, por concepto del Seguro Obligatorio de Riesgos del Trabajo.
        </p>

        {{-- <ul class="info-list">
            <li>Número de póliza: {{ $policy_sort->formatSortNumber() }}</li>
            <li>Moneda: {{ $policy_sort->type_currency ?? '' }}</li>
            <li>Vigencia de la póliza:
                {{ $policy_sort ? ucfirst(strftime('%A %e de %B del %Y', strtotime('+1 days', strtotime($policy_sort->validity_to)))) : '' }}
            </li>
            @if ($policy_sort->temporality == 'permanent')
                <li>Forma de pago: {{ ucfirst(mb_strtolower($PERIODICITYT[$policy_sort->periodicity] ?? '')) }}</li>
            @endif
            @if ($policy_sort->temporality == 'short')
                <li>Forma de pago: Pago único</li>
            @endif
            @php
                // Obtener el último pago
                $lastPayment = $policy_sort_collection->sortByDesc('created_at')->first();
            @endphp
            <li>Último pago:
                @if ($lastPayment)
                    {{ ucfirst(strftime('%A %e de %B del %Y', strtotime($lastPayment->created_at))) }}
                @else
                    No hay pagos registrados.
                @endif 
            </li>
            <li>Actividad económica: {{ $policy_sort->economic_activity_name ?? '' }}</li>
            <li>Lugar donde se realiza la actividad económica: {{ $policy_ubication }}</li>

        </ul> --}}

        <div class="coverage-section">
            <div class="coverage-header">DETALLE DE PRIMAS PENDIENTES:</div>
            <div class="coverage-content">
                <table>
                    <thead>
                    <tr>
                        <th>Período</th>
                        <th>Forma de pago</th>
                        <th>Prima</th>
                        <th>Concepto</th>
                    </tr>
                    </thead>
                    <tbody>
                    {{-- Verifica si hay elementos en $pay_pending antes de iterar --}}
                    @if (empty($pay_pending) || $policy_sort->periodicity == 1 || $policy_sort->temporality == 'short')
                        <tr>
                            <td>Sin prima pendiente</td>
                            <td>Sin prima pendiente</td>
                            <td>Sin prima pendiente</td>
                            <td>Sin prima pendiente</td>
                        </tr>
                    @else
                        {{-- Itera directamente sobre cada pago pendiente para mostrarlo --}}
                        @foreach ($pay_pending as $collection)
                            <tr>
                                <td>Desde
                                    {{ $collection->fechaIni ? date('d/m/Y', strtotime($collection->fechaIni))  : '' }}
                                    hasta
                                    {{ $collection->fechaFin ? date('d/m/Y', strtotime($collection->fechaFin)) : '' }}
                                </td>
                                <td>{{ ucfirst(mb_strtolower($PERIODICITYT[$policy_sort->periodicity] ?? '')) }}</td>
                                <td>@include('colones', ['currency' => $policy_sort->type_currency]){{number_format($collection->valor, 2, ',', '.')}}</td>
                                <td>
                                    @if( $policy_sort->periodicity == 1)
                                        Otro
                                    @else
                                        Abono
                                    @endif
                                </td>
                            </tr>
                        @endforeach
                    @endif
                    </tbody>
                </table>
            </div>
        </div>

        <p>
            &nbsp;
        </p>
{{-- 
        @if ($policy_sort->periodicity == 1 || $policy_sort->temporality == 'short')
            <p>
                Total, del valor de prima pendiente
                {{ $policy_sort ? ($policy_sort->type_currency === 'USD' ? '$' : '₡') : '' }} 0.00
            </p>
        @else
            @php
                $multiplierMap = [
                    2 => 2, // SEMESTRAL
                    3 => 4, // TRIMESTRAL
                    4 => 12, // MENSUAL
                ];
                $multiplier = $multiplierMap[$policy_sort->periodicity] ?? 1;
                $pending_payment = $policy_sort->amount_policy * $multiplier - $total_payment;
            @endphp
            <p>
                Total, del valor de prima pendiente
                @if ($policy_sort)
                    @include('colones', ['currency' => $policy_sort->type_currency])
                @endif
                {{ isset($pending_payment) ? number_format($pending_payment, 2) : '0.00' }}
            </p>
        @endif --}}

        <p>
            Se emite el día {{ date('d/m/Y') }} a las {{ date('H:i:s') ?? '' }}.
        </p>
        <p>
            La documentación contractual que integra este producto está registrada ante la Superintendencia
            General de Seguros de conformidad con lo dispuesto por el artículo 29, inciso d) de la Ley
            Reguladora del Mercado de Seguros, Ley 8653, según resolución número SGS-R-2593-2024 del 06
            de noviembre de 2024.
        </p>
        <b>Área de Aseguramiento</b><br>
        <b>Seguro Obligatorio de Riesgos del Trabajo</b><br>
        <b>MNK Seguros</b><br>
        <b>Correo electrónico:</b> <EMAIL><br>
        <b>Teléfono:</b> (506) 4102-7681
    </div>

    <footer class="footer">
        <img src="{{ public_path('images/mnk_footer.png') }}" alt="pie de documento"
            style="width: 100%;">
    </footer>

</body>

</html>
