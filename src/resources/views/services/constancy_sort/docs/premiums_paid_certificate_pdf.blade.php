<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <title>Certificado de Seguro</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            margin: 0;
            padding: 0;
        }
        .content {
            margin-top: 20px;

            position: relative;
            margin-bottom: 50px;
        }
        .highlight {
            font-weight: bold;
            color: red;
        }
        .info-list {
            margin-top: 20px;
        }
        .info-list li {
            margin-bottom: 8px;
        }
        .coverage-section {
            margin-top: 30px;
            padding: 15px;
            border: 1px solid #000;
            border-radius: 10px;
            background-color: #fff;
            /* Fondo blanco para el contenido */
            color: #000;
            /* Texto negro para el contenido */
        }
        .coverage-header {
            background-color: #92C846;
            border: 1px solid #000;
            color: #000;
            padding: 10px;
            font-weight: bold;
            text-align: center;
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
        }
        .coverage-content {
            padding: 10px;
            text-align: justify;
        }
        .coverage-content table {
            width: 100%;
            border-collapse: collapse;
            background-color: #fff;
            /* Fondo blanco para la tabla */
        }
        .coverage-content th {
            background-color: #92C846;
            color: #000;
            border: 1px solid #000;
            padding: 8px;
            text-align: center;
            font-weight: bold;
        }
        .coverage-content td {
            border: 1px solid #000;
            padding: 8px;
            text-align: center;
            color: #000;
            /* Texto negro para las celdas */
        }
        .coverage-note {
            font-weight: bold;
        }
        .header {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            padding: 10px 15px;
            text-align: center;
            background-color: #ffffff;
            z-index: 100;
            height: 10px;
        }
        .footer {
            position: fixed;
            bottom: 0;
            left: 0;
            width: 100%;
            text-align: center;
            background-color: #ffffff;
            z-index: 100;
            height: 80px;
        }
        .align-right {
            text-align: right;
        }
        .title-border {
            width: 400px;
            position: absolute;
            left: -23px;
            top: -10px;
        }
        .logo-image {
            max-width: 100%;
            height: 130px !important;
        }
    </style>
</head>
<body>
    <header class="header">
        <script type="text/php">
        if (isset($pdf)) {
            $x = $pdf->get_width() - 90;
            $y = 40;
            $text = "Pág {PAGE_NUM} de {PAGE_COUNT}";
            $font = 'Arial';
            $size = 8;
            $color = array(0,0,0);
            $word_space = 0.0;  //  default
            $char_space = 0.0;  //  default
            $angle = 0.0;   //  default
            $pdf->page_text($x, $y, $text, $font, $size, $color, $word_space, $char_space, $angle);
        }
    </script>
    </header>
    <div class="content">
        <table
            style="width: 100%; border: none; table-layout: fixed; font-size: 13px !important;margin-top: 25px !important;">
            <tbody>
                <tr>
                    <td style="width: 30%; padding: 10px; border: none; text-align: left; vertical-align: top;">
                        <img src="{{ public_path('images/borde_sort.png') }}" alt="" class="title-border"
                            style="height: 125px;">

                        <div style="width: 70% !important;padding: 18px 28px;">
                            <div style="margin-bottom: 2px">
                                CONSTANCIA DE
                            </div>
                            <div style="font-weight: bold;font-size: 15px !important; ">
                                PRIMAS PAGADAS
                            </div>
                        </div>
                    </td>

                    <!-- Logo celda con ancho del 25% -->
                    <td style="width: 25%; padding: 10px; border: none; text-align: right; height: auto">
                        <img src="{{ public_path('images/mnk.png') }}" alt="Logo" class="logo-image"
                            style="height: 90px; width: 110px">
                    </td>
                </tr>
            </tbody>
        </table>
        <p>
            Mediante el presente se hace constar que MNK Seguros otorga protección a
            {{ ucwords(mb_strtolower($policy_sort->activity->affiliate->full_name ?? '')) }} con número de
            identificación {{ $policy_sort->activity->affiliate->doc_number ?? '' }},
            mediante el Seguro Obligatorio de Riesgos del Trabajo en los términos y condiciones que se indican a
            continuación:
        </p>
        <ul class="info-list">
            <li>Número de póliza: {{ $policy_sort->formatSortNumber() }}</li>
            <li>Moneda: {{ $policy_sort->type_currency ?? '' }}</li>
            <li>Vigencia de la póliza: {{ $policy_sort ? ( 'Desde ' . date('d/m/Y', strtotime($policy_sort->validity_from)) . ' hasta ' . date('d/m/Y', strtotime($policy_sort->validity_to)) ) : '' }}</li>
            @if($policy_sort->temporality == 'permanent')
                <li>Forma de pago:  {{ ucfirst(mb_strtolower($PERIODICITYT[$policy_sort->periodicity] ?? '')) }}</li>
            @endif
            @if($policy_sort->temporality == 'short')
                <li>Forma de pago:  Pago único</li>
            @endif
            @php
                // Obtener el último pago
                $lastPayment = $policy_sort_collection->sortByDesc('created_at')->first();
            @endphp
            <li>Último pago:
                @if ($lastPayment)
                    {{  date('d/m/Y', strtotime($lastPayment->created_at))}}
                @else
                    No hay pagos registrados.
                @endif
            </li>
            <li>Actividad económica:  {{ $policy_sort->economic_activity_name  ?? '' }}</li>
            <li>Lugar donde se realiza la actividad económica: {{$policy_ubication}} </li>
        </ul>

        <div class="coverage-section">
            <div class="coverage-header">DETALLE DE PRIMAS PAGADAS:</div>
            <div class="coverage-content">
                <table>
                    <thead>
                    <tr>
                        <th>Período</th>
                        <th>Forma de pago</th>
                        <th>Prima</th>
                    </tr>
                    </thead>
                    <tbody>
                    @php
                        use Carbon\Carbon;

                        $start_date = Carbon::parse($policy_sort->validity_from);
                        $end_date = Carbon::parse($policy_sort->validity_to);

                        // Determinamos la duración de cada período
                        if ($policy_sort->periodicity == 2) {
                            // Semestral
                            $months = 6;
                            $amount = $policy_sort->semiannual_calculation_amount;
                        } elseif ($policy_sort->periodicity == 3) {
                            // Trimestral
                            $months = 3;
                            $amount = $policy_sort->quarterly_calculation_amount;
                        } elseif ($policy_sort->periodicity == 4) {
                            // Mensual
                            $months = 1;
                            $amount = $policy_sort->monthly_calculation_amount;
                        } else {
                            $months = null;
                        }
                    @endphp
                        @if ($policy_sort->periodicity == 1 || $policy_sort->temporality == 'short')
                            <tr>
                                <td>Desde {{ $policy_sort ?   ucfirst(strftime('%A %e de %B del %Y', strtotime($policy_sort->validity_from))) : '' }} hasta {{ $policy_sort ?  ucfirst(strftime('%A %e de %B del %Y', strtotime($policy_sort->validity_to)))  : '' }}
                                </td>
                                <td>{{ $policy_sort->periodicity == 1 ? 'Anual' : 'Pago único' }}</td>
                                <td> @include('colones', ['currency' => $policy_sort->type_currency]){{ $policy_sort->periodicity == 1 ? number_format($policy_sort->annual_calculation_amount, 2, ',', '.') :  number_format($policy_sort->single_payment_value, 2, ',', '.')  }}</td>

                            </tr>
                        @else
                            @if($months)
                                @foreach($policy_sort_collection as $index => $collection)
                                    @php
                                        // Calculamos las fechas del período
                                        $period_start = $start_date->copy();
                                        $period_end = $start_date->copy()->addMonths($months)->subDay();

                                        // Avanzamos la fecha inicial para el próximo período
                                        $start_date->addMonths($months);
                                    @endphp
                                    <tr>
                                        <td>Desde {{$period_start ? ucfirst(strftime('%A %e de %B del %Y', strtotime($period_start->toDateString())))  : '' }} hasta {{$period_start ? ucfirst(strftime('%A %e de %B del %Y', strtotime($period_end->toDateString()))) : '' }}</td>
                                        <td>{{ ucfirst(mb_strtolower($PERIODICITYT[$policy_sort->periodicity] ?? '')) }}</td>
                                        {{-- Se muestra el valor pagado --}}
                                        <td>@include('colones', ['currency' => $policy_sort->type_currency]){{number_format(optional($collection)->total_amount ?? 0, 2, ',', '.')}}</td>
                                    </tr>
                                @endforeach
                            @endif
                        @endif
                    </tbody>
                </table>
            </div>
        </div>
        <p>
            Se emite el día {{ date('d/m/Y') }} a las {{ date('H:i:s') ?? '' }}.
        </p>
        <p>
            La documentación contractual que integra este producto está registrada ante la Superintendencia
            General de Seguros de conformidad con lo dispuesto por el artículo 29, inciso d) de la Ley
            Reguladora del Mercado de Seguros, Ley 8653, según resolución número SGS-R-2593-2024 del 06
            de noviembre de 2024.
        </p>
        <b>Área de Aseguramiento</b><br>
        <b>Seguro Obligatorio de Riesgos del Trabajo</b><br>
        <b>MNK Seguros</b><br>
        <b>Correo electrónico:</b> <EMAIL><br>
        <b>Teléfono:</b> (506) 4102-7681
    </div>

    <footer class="footer">
        <img src="{{ public_path('images/mnk_footer.png') }}" alt="pie de documento"
            style="width: 100%;">
    </footer>

</body>

</html>
