@extends('layouts.main')

@section('title', 'DATOS DE LA SOLICITUD DE CONSTANCIA')

@section('menu')
    @parent
@endsection

@section('content')
    <div class="ui basic segment">
        <h1 class="ui header">
            Datos de la solicitud de constancia
            {{-- <div class="sub header">Campos con <span style="color: red;" class="required">*</span> obligatorios.</div> --}}
        </h1>
        <form autocomplete="off" action="{{ secure_url("servicio/{$activity->id}/constancy_sort/save") }}" id="dictamen"
            method="post" class="ui small form">
            <div class="ui secondary segment">
                <div class="ui three columns grid">

                    <div class="column">
                        <b>Póliza </b> <span id="no-policy"></span>
                    </div>

                    <div class="column">
                        <b># de solicitud de constancia:</b> <a id="no-solicitud"></a>
                    </div>

                    <div class="column">
                        <b>Fecha de solicitud constancia:</b> <a id="fecha-solicitud"></a>
                    </div>

                    <div class="column">
                        <b>Fecha de emisión constancia:</b> <a id="fecha-emision"></a>
                    </div>

                    <div class="column">
                        <b>Tipo de constancia:</b> <a id="doc_type"></a>
                    </div>

                </div>

            </div>
            <div class="ui styled fluid accordion">
                <div class="title"><i class="dropdown icon"></i> Recibo emisión póliza
                </div>
                <div class="content">
                    <div class="accordion transition">

                        <div class="title">
                            <i class="dropdown icon"></i>
                            Datos del tomador
                        </div>

                        <div class="content">
                            <form class="ui form small clearing">

                                <div class="three fields">

                                    <div class="field">
                                        <label for="identification_type">Tipo de identificación</label>
                                        <div class="ui  icon input">
                                            <input readonly name="identification_type" id="identification_type" autocomplete="off"
                                                type="text"
                                                value="{{ $activity->affiliate ? ucfirst(strtolower($DOC_TYPES[$activity->affiliate->doc_type])) : '' }}">
                                        </div>
                                    </div>

                                    <div class="field">
                                        <label for="identification_number">Número de identificación</label>
                                        <div class="ui  icon input">
                                            <input readonly name="doc_id" id="identification_number" autocomplete="off"
                                                type="text"
                                                value="{{ $activity->affiliate ? $activity->affiliate->doc_number : '' }}">
                                        </div>
                                    </div>

                                    <div class="field">
                                        <label for="name">Nombre</label>
                                        <div class="ui  icon input">
                                            <input readonly name="name" id="name" autocomplete="off" type="text"
                                                value="{{ $activity->affiliate ? ucwords(strtolower($activity->affiliate->first_name)) : '' }}">
                                        </div>
                                    </div>
                                </div>

                                <div class="three fields">
                                    <div class="field">
                                        <label for="phones">Teléfonos</label>
                                        <div class="ui  icon input">
                                            <input readonly name="phones" id="phones" autocomplete="off" type="text"
                                                value="{{ $activity->affiliate ? $activity->affiliate->phone : '' }}">
                                        </div>
                                    </div>

                                    <div class="field">
                                        <label for="notification_email">Correo electrónico de notificaciones</label>
                                        <div class="ui  icon input">
                                            <input readonly name="notification_email" id="notification_email" autocomplete="off"
                                                type="text"
                                                value="{{ $activity->affiliate ? ucfirst(strtolower($activity->affiliate->email)) : '' }}">
                                        </div>
                                    </div>

                                    <div class="field">
                                        <label>Sector</label>
                                        <div class="field">
                                            <div class="ui radio checkbox checkbox-spacing">
                                                <input readonly type="radio" name="sector" id="public" class="minus"
                                                    value="public"
                                                    {{ $policySort->economic_activity === 'public' ? 'checked' : '' }}>
                                                <label for="public">Público</label>
                                            </div>
                                            <div class="ui radio checkbox checkbox-spacing">
                                                <input readonly type="radio" name="sector" id="private" class="minus"
                                                    value="private"
                                                    {{ $policySort->economic_activity === 'private' ? 'checked' : '' }}>
                                                <label for="private">Privado</label>
                                            </div>
                                        </div>
                                    </div>

                                </div>

                                <div class="three fields">

                                    <div class="field">
                                        <label for="sector">Cod. actividad económica</label>
                                        <div class="ui search code">
                                            <div class="ui icon input">
                                                <input readonly id="economic_activity_code" class="prompt" name="diagnostics[cod][]"
                                                    type="text" value="{{ $policySort->activity_economic_id }}"
                                                    autocomplete="off">
                                                <i class="search icon"></i>
                                            </div>
                                            <div class="results"></div>
                                        </div>
                                    </div>

                                    <div class="field">
                                        <label for="economic_activity_name">Nombre actividad económica</label>
                                        <input readonly id="economic_activity_name" class="prompt" type="text" readonly
                                            maxlength="500" value="{{ $policySort->economic_activity_name }}">
                                    </div>
                                </div>

                            </form>
                        </div>

                        <div class="title">
                            <i class="dropdown icon"></i>
                            Datos póliza
                        </div>

                        <div class="content">
                            <form class="ui form small clearing">

                                <div class="three fields">

                                    <div class="field">
                                        <!-- <label for="policy_number"># Póliza</label> -->
                                        <label for="policy_number">Póliza SORT</label>
                                        <div class="ui  icon input">
                                            <input readonly name="policy_number" id="policy_number" autocomplete="off"
                                                type="text" value="{{ $policySort ? $policySort->formatNumberConsecutive() : '' }}">
                                        </div>
                                    </div>

                                    <div class="field">
                                        <label for="effective_start_date">Fecha de inicio vigencia</label>
                                        <input readonly type="text" id="effective_start_date" name="effective_start_date"
                                            value="{{ $policySort ? ucfirst(strftime('%A %e de %B del %Y', strtotime($policySort->validity_from))) : '' }}">
                                    </div>

                                    <div class="field">
                                        <label for="renewal_date">Fecha de renovación</label>
                                        <input readonly type="text" id="renewal_date" name="renewal_date"
                                        value="{{ $policySort ? ucfirst(strftime('%A %e de %B del %Y', strtotime('+1 days', strtotime($policySort->validity_to)))) : '' }}">
                                    </div>

                                </div>

                                <div class="three fields">

                                    <div class="field">
                                        <label>Estado de la planilla</label>
                                        <div class="ui  icon input">
                                            <input readonly name="premium_value" id="premium_value" autocomplete="off"
                                                type="text" value="Reportada">
                                        </div>
                                    </div>

                                    <div class="field">
                                        <label for="premium_value">Valor de la prima</label>
                                        <div class="ui  icon input">
                                            <input readonly name="premium_value" id="premium_value" autocomplete="off"
                                                type="text"
                                                value="{{ $policySort ? ($policySort->type_currency === 'USD' ? '$' : '₡') . ' ' . number_format($policySort->amount_policy, 2, ',', '.') : '' }}">

                                        </div>
                                    </div>

                                    
                                    <div class="required field">
                                        <label for="methodOfPayment">Forma de pago</label>
                                        <div class="ui input">
                                            @if($policySort->temporality == 'permanent')
                                            <input id="payment_method" type="text"  class="prompt readonly" autocomplete="off"
                                                value="{{ ucfirst(mb_strtolower($PERIODICITYT[$policySort->periodicity] ?? '')) }}" readonly>

                                            @endif
                                            @if($policySort->temporality == 'short')
                                                <input id="payment_method" type="text"  class="prompt readonly" autocomplete="off"
                                                    value="Pago único" readonly>
                                            @endif
                                        </div>
                                    </div>

                                </div>

                                <div class="three fields">

                                    <div class="field">
                                        <label for="accumulated_paid_premium_value">Valor prima pagado acumulado</label>
                                        <div class="ui  icon input">
                                            <input readonly name="accumulated_paid_premium_value"
                                                id="accumulated_paid_premium_value" autocomplete="off" type="text"
                                                value="{{ $totalAmount ? ($policySort->type_currency === 'USD' ? '$' : '₡') . ' ' . number_format($totalAmount, 2, ',', '.') : '0' }}">

                                        </div>
                                    </div>

                                    <div class="field">
                                        <label for="initial_payment_date">Fecha de pago inicial</label>
                                        <div class="ui  icon input">
                                            <input readonly name="initial_payment_date" id="initial_payment_date"
                                                autocomplete="off" type="text"
                                                value="{{ $oldestCreatedAt ? ucfirst(strftime('%A %e de %B del %Y', strtotime($oldestCreatedAt))) : '' }}">

                                        </div>
                                    </div>

                                    <div class="field">
                                        <label for="last_payment_date">Fecha de último pago</label>
                                        <div class="ui  icon input">
                                            <input readonly name="last_payment_date" id="last_payment_date" autocomplete="off"
                                                type="text"
                                                value="{{ $newestCreatedAt ? ucfirst(strftime('%A %e de %B del %Y', strtotime($newestCreatedAt))) : '' }}">

                                        </div>
                                    </div>

                                </div>

                            </form>
                        </div>
                    </div>






                </div>
            </div>

            <div class="ui basic segment">

                <div class="fields">
                    {{--                    <div class="four wide field"> --}}
                    {{--                        <button class="ui green fluid button"><i class="save icon"></i> Guardar</button> --}}
                    {{--                    </div> --}}
                    <div class="six wide field">
                        {{--                        <a href="{{secure_url('/servicio/' . $activity->id . '/constancy_sort/pdf')}}" --}}
                        {{--                           class="ui basic small button" target="_blank" --}}
                        {{--                           onclick="event.preventDefault(); generatePDF();"> --}}
                        {{--                            <i class="file pdf outline red icon"></i> Vista previa --}}
                        {{--                        </a> --}}
                        <a href="{{ secure_url('/servicio/' . $activity->id) }}" class="ui secondary small button"><i
                                class="arrow left icon"></i>
                            Volver a la actividad</a>
                    </div>
                </div>
            </div>
            {{ csrf_field() }}
        </form>
    </div>

    <style type="text/css">
        .ui.grid .column {
            padding: 0.5rem 1rem !important;
        }

        .sender .field {
            display: none;
        }

        .ear.field {
            display: none;
        }

        .msp.field {
            display: none;
        }



        .field>h3 {
            text-align: center;
            margin-top: 1.25rem;
        }

        .ui.search>.results {
            width: 30rem;
        }

        .ui.search>.results .result .title {
            padding: 0 !important;
            border: none !important;
            text-transform: none;
        }

        .ui.search>.results .result .content {
            padding: 0 !important;
        }

        th {
            text-align: center !important;
        }

        input[readonly],
        textarea[readonly] {
            background-color: #f3f4f5 !important;
        }

        .ui.dropdown.selection.disabled {
            background-color: #f3f4f5 !important;
        }
    </style>
    <script type="text/javascript">
        $(document).ready(function() {
            $('input, select').prop('disabled', true);

            var constacySort = @json($constacySort);

            $('#no-policy').text('{{ $policySort->formatNumberConsecutive() }}');
            $('#no-solicitud').text(constacySort.id || '');
            $('#fecha-solicitud').text(
                '{{ ucfirst(strtolower($constacySort->created_at->formatLocalized('%A %d de %B de %Y'))) }}' ||
                '');
            $('#fecha-emision').text(
                '{{ ucfirst(strtolower($constacySort->created_at->formatLocalized('%A %d de %B de %Y'))) }}' ||
                '');
            $('#doc_type').text('{{ $TYPE_CONSTANCY[$constacySort->type] }}' || '');

            // Valor por defecto
            var defaultValue = "{{ $constacySort->type }}"; // El valor predeterminado del servidor

            // Establecer el valor predeterminado en el dropdown
            $('#typeConstancySort').dropdown('set selected', defaultValue);

            // Deshabilitar el dropdown para evitar modificaciones
            $('#typeConstancySort').addClass('disabled');
        });



        var generatePDF = function() {
            $('form#dictamen').attr('target', '_blank');
            $('form#dictamen').attr('action',
                '{{ secure_url('/servicio/' . $activity->id . '/constancy_sort/pdf') }}');
            $('form#dictamen').submit();
            $('form#dictamen').removeAttr('target');
            $('form#dictamen').attr('action',
                '{{ secure_url('/servicio/' . $activity->id . '/constancy_sort/save') }}');
            return false;
        };

        var toggleSender = function() {
            console.log('sender');
            if ($('[name=sender]').val() == 5 || $('[name=sender]').val() == 8) {
                $('.sender .field').show();
            } else {
                $('.sender .field').hide();
            }
        };

        var cie10 = [];


        $(document).ready(function() {
            $('.ui.accordion .ui.grid .row').css('padding-top', 0);
            $('.ui.accordion .ui.grid .column').css('padding-top', 0);

            $('.ui.accordion').accordion({
                exclusive: false
            });

            $('form#dictamen a.red').click(function() {
                $(this).parent().parent().remove();
                calculateDeficienceSum();
            });
            $('form .ui.dropdown').dropdown({
                forceSelection: false
            });
            $('form .datepicker').pickadate({
                selectYears: 100,
                selectMonths: true,
                max: new Date(),
                formatSubmit: 'yyyy-mm-dd',
                format: 'mmm dd, yyyy'
            });

            $.getJSON('/js/cie10.json', function(json) {
                cie10 = json;
                $('form .ui.search.code').search({
                    source: cie10,
                    fields: {
                        title: 'COD',
                        description: 'DESCRIPTION'
                    },
                    searchFields: ['COD', 'DESCRIPTION'],
                    regExp: {
                        escape: /[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,
                        beginsWith: ''
                    },
                    showNoResults: true,
                    maxResults: 250,
                    searchFullText: false,
                    error: {
                        noResults: 'No se encontraron resultados para tu búsqueda.'
                    },
                    onSelect: function(result, response) {
                        $(this).parents('.fields').find('input.description').val(result
                            .DESCRIPTION);
                    }
                });
            });

            $('form input').keydown(function(event) {
                if (event.keyCode == 13) {
                    event.preventDefault();
                    return false;
                }
            });

            @if ($activity->plantilla && $activity->plantilla->firm_date)
                // $('form a.basic.blue.button').hide();
                // $('form a.basic.red.button').hide();
                // $('form input, form textarea').prop('disabled', true);
                // $('form .controversy input, form .controversy textarea').prop('disabled', false);
                // $('form .pcl input, form .pcl textarea').prop('disabled', false);
                // $('form .ui.dropdown').addClass('disabled');
                // $('form .controversy .ui.dropdown').removeClass('disabled');
                // $('form .pcl .ui.dropdown').removeClass('disabled');
            @endif

            @if (Auth::user()->isViewer())
                $('.datepicker').pickadate('picker').stop();
                $('#dictamen .ui.search input').attr('disabled', 'disabled');
                $('#dictamen input, #dictamen textarea').attr('readonly', 'readonly');
                $('#dictamen .ui.dropdown').addClass('disabled');
                $('#dictamen .button').remove();
            @endif
        });
    </script>
@endsection
