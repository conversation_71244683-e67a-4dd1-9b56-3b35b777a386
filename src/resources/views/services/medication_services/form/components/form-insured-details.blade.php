<div class="title">
    <i class="dropdown icon"></i>
    Datos del paciente
</div>
<div class="content">
    <div class="four fields">
        <!-- Campo Tipo de documento-->
        <div class="field">
            <label>Tipo de documento</label>
            <div class="ui selection dropdown readonly">
                <input class="readonly" type="hidden" name="document_type"
                       value="{{ $affiliate->doc_type ?? '' }}" readonly>
                <i class="dropdown icon"></i>
                <div class="default text"
                     style="color: black;">{{ $DOC_TYPES[$affiliate->doc_type ?? ''] ?? 'N/A' }}</div>
                <div class="menu"></div>
            </div>
        </div>
        <!-- Field numero de identificacion -->
        <div class="field">
            <label># identificación</label>
            <div class="ui search code">
                <div class="ui icon input">
                    <input class="prompt readonly" name="identification" type="text" autocomplete="off"
                           value="{{ $affiliate->doc_number ?? '' }}"  readonly>
                </div>
                <div class="results"></div>
            </div>
        </div>
        <!-- Field nombre del paciente -->
        <div class="field">
            <label>Nombre del paciente</label>
            <div class="ui search code">
                <div class="ui icon input ">
                    <input class="prompt readonly" name="name_patient" type="text" autocomplete="off"
                           value="{{ ucwords(strtolower($affiliate->first_name ?? '')) }}" readonly>
                </div>
                <div class="results"></div>
            </div>
        </div>
        <!-- Field actividad -->
        <div class="field">
            <label>Actividad</label>
            <div class="ui search code">
                <div class="ui icon input ">
                    <input class="prompt medication-input-to-lower-case readonly" name="activity" type="text"
                           autocomplete="off"
                           value="{{$activity_economic_name}}" readonly>
                </div>
                <div class="results"></div>
            </div>
        </div>  
    </div>
    <div class="four fields">
        <!-- Field fecha de dictamen -->
        <div class="field">
            <label>Fecha de dictamen</label>
            <div class="ui search code">
                <div class="ui icon input ">
                    <input class="prompt readonly" name="date_dictamen" type="text" autocomplete="off"
                           value="{{ $gis->date_ruling ?? '' }}" readonly>
                </div>
                <div class="results"></div>
            </div>
        </div>

        <!-- Field Nombre Patrono -->
        <div class="field">
            <label>Nombre patrono</label>
            <div class="ui search code">
                <div class="ui icon input ">
                    <input class="prompt readonly" name="name_patron" type="text" autocomplete="off"
                           value="{{ $policy_sort->first_name ?? '' }}" readonly>
                </div>
                <div class="results"></div>
            </div>
        </div>

        <!-- Field ID Patrono -->
        <div class="field">
            <label>Identificación patrono</label>
            <div class="ui search code">
                <div class="ui icon input">
                    <input class="prompt readonly" name="id_patron" type="text" autocomplete="off"
                           value="{{ $policy_sort->doc_number ?? '' }}" readonly>
                </div>
                <div class="results"></div>
            </div>
        </div>

        <!-- Field No. Póliza SORT -->
        <div class="field">
            <!-- <label># póliza SORT</label> -->
            <label>Póliza SORT</label>
            <div class="ui search code">
                <div class="ui icon input">
                    <input class="prompt readonly" name="num_policy_sort" type="text" autocomplete="off"
                           value="{{$policy_sort ? $policy_sort->formatSortNumber() : '' }}" readonly>
                </div>
                <div class="results"></div>
            </div>
        </div>

    </div>

    <div class="four fields">
        <!-- Field correo electronico-->
        <div class="field">
            <label>Correo electrónico del paciente</label>
            <div class="ui search code">
                <div class="ui icon input">
                    <input class="prompt readonly" type="text" autocomplete="off"
                           value="{{ $affiliate->email ?? '' }}" readonly>
                </div>
                <div class="results"></div>
            </div>
        </div>
    </div>
</div>