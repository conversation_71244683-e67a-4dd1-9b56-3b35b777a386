@extends('layouts.main')

@section('title', 'Gestión integral del siniestro - SORT')

@section('menu')
    @parent
@endsection

<@php
    
@endphp

@section('content')
    <div class="ui basic segment">
        <h1 class="ui header">
            Gestión integral del siniestro - SORT
            <div class="sub header">Campos con <span style="color: red;" class="required">*</span> obligatorios.</div>
        </h1>

        <div class="ui secondary segment">
            <div class="ui three columns grid">
                <div class="column"><b># de identificación:</b>
                    {{ $activity->affiliate->doc_type }} {{ $activity->affiliate->doc_number }}
                </div>
                <div class="column"><b>Nombre:</b> <a
                        href="{{ secure_url('afiliado/' . $activity->affiliate_id) }}">{{ Illuminate\Support\Str::title(mb_strtolower($activity->affiliate->full_name, 'UTF-8')) }}</a>
                </div>
                <div class="column"><b>Póliza SORT:</b>
                    <a>{{ $activity->parent_activity ? $activity->parent_activity->policy_sort->formatSortNumber() : '' }}</a>
                </div>

                <div class="column"><b>Fecha del caso:</b>
                    {{ ucfirst(strtolower($activity->created_at->formatLocalized('%A %d de %B de %Y'))) }}
                </div>
                <div class="column"><b># de aviso</b>
                    {{ $activity->gis_sort->consecutive_gis ?? '' }} </div>
                <div class="column"><b># de caso:</b>
                    {{ $activity->gis_sort->formatCaseNumberIfReported() ?? '' }}
                </div>
                <div class="column"><b>Proveedor:</b>
                    {{ mb_strtoupper(optional(optional($activity->gis_sort)->provider())->name, 'UTF-8') }}
                </div>
            </div>
        </div>

        <form id="gisForm" type="submit" class="ui attached form" enctype="multipart/form-data"
            action="{{ secure_url('services/gis/' . $activity->id . '/save') }}" method="POST" autocomplete="off">
            {{ csrf_field() }}


            <div class="ui styled fluid accordion">
                @include('services.gis.form.components.form_reporte_accidente', ['planillas'])
                @if(!Auth::user()->isTPA())
                    @include('services.gis.form.components.form_fase_uno', ['gis', 'documents', 'activity_diagnostic', 'activity'])
                    @include('services.gis.form.components.form_fase_dos', ['gis'])
                    @include('services.gis.form.components.form_fase_tres', ['gis'])
                    @include('services.gis.form.components.fase_3.form_datos_actuariales', ['gis'])
                    @include('services.gis.form.components.fase_3.form_indice_siniestro', ['gis'])
                    @include(
                        'services.gis.form.components.tabla_prestacion_medica.tabla_prestacion_medica',
                        ['gis', 'prestaciones_medicas']
                    )
                    @include('services.gis.form.components.form_subrogacion', ['subrogacion', 'gis_total'])
                @endif
            </div>


            <div class="ui basic segment">
                <div class="ui error message"></div>
                <div class="fields">
                    @if (Auth::user()->is_edit_general_permission_service(
                            $activity->service_id,
                            \App\Permission::INFORMACION_DE_LA_ACTIVIDAD))
                        <div class="four wide field">
                            <button type="submit" id="submitButton" class="ui primary fluid button"><i
                                    class="save icon"></i>
                                Guardar</button>
                        </div>
                    @endif
                    @php
                        $url = Auth::user()->isTPA()
                            ? '/proveedor/' . Auth::user()->id . '/asignar_servicio'
                            : "/servicio/{$activity->id}";
                    @endphp
{{--                    <a href="{{secure_url('/servicio/' . $activity->id . '/gis_sort/pdf')}}"--}}
{{--                       id="proceedWithDocumentRequestPdf"--}}
{{--                       class="ui basic small button" target="_blank"--}}
{{--                       onclick="event.preventDefault(); generatePDF();">--}}
{{--                        <i class="file pdf outline red icon"></i> Vista previa--}}
{{--                    </a>--}}
                    <div class="six wide field">
                        <a href="{{ $url }}" class="ui secondary small button">
                            <i class="arrow left icon"></i> Volver a la actividad
                        </a>
                    </div>
                </div>
            </div>

        </form>
    </div>

    <style>
        .ui.grid .column {
            padding: 0.5rem 1rem !important;
        }

        .hidden {
            display: none;
        }

        .grayed-input {
            pointer-events: none;

            background-color: #f0f0f0 !important;
            /* Fondo gris claro */
            color: #888 !important;
            /* Texto gris */
            border: 1px solid #ddd !important;

            text-transform: none !important;
            /* Borde gris claro */
        }

        .dinamic_grayed_input {
            pointer-events: none;

            background-color: #f0f0f0 !important;
            /* Fondo gris claro */
            color: #888 !important;
            /* Texto gris */
            border: 1px solid #ddd !important;

            text-transform: none !important;
            /* Borde gris claro */
        }

        .pointer-events {
            pointer-events: none;
            /* Desactiva todos los eventos de puntero */
            opacity: 0.6;
            /* Opcional: Cambia la opacidad para indicar que están deshabilitados */
        }

        .grayed-area {
            background-color: #f0f0f0 !important;
            /* Fondo gris claro */
            color: #888 !important;
            /* Texto gris */
            border: 1px solid #ddd !important;
            /* Borde gris claro */
        }
    </style>


    <script type="text/javascript" src="{{ secure_url('js/policy_issuance_data.js?v=1.4.15') }}"></script>

    <script type="text/javascript">
        {{--let generatePDF = function () {--}}
        {{--    $('form').attr('target', '_blank');--}}
        {{--    $('form').attr('action', '{{secure_url('/servicio/' . $activity->id . '/gis_sort/pdf')}}');--}}
        {{--    $('form').submit();--}}
        {{--    $('form').removeAttr('target');--}}
        {{--    $('form').attr('action', '{{secure_url('/servicio/' . $activity->id . '/gis_sort/save')}}');--}}
        {{--    return false;--}}
        {{--};--}}
        $(document).ready(function() {
            // $('.ui.dropdown').dropdown();
            $('.ui.accordion').accordion();
        });
    </script>

    <script>
        // activar validacion
        isEnable = `{{ $validForm }}`;

        enableDinamic(isEnable);



        function formInicial() {

            $('#gisForm').form({
                fields: rules,
                on: 'blur',

                onSuccess: function(event) {

                    Swal.fire({
                        title: 'Enviando...',
                        text: 'Por favor, espera mientras se procesa la solicitud.',
                        allowOutsideClick: false,
                        showConfirmButton: false,
                        willOpen: () => {
                            Swal.showLoading(); // Mostrar el loader
                        }
                    });

                    handleSubmit();
                },
                onFailure: function() {
                    loadingMain(false);
                }
            });
        }

        function handleSubmit() {
            // Convertir el estado en un objeto manejable
            var state = @json($state);

            // Crear el objeto FormData
            var formData = new FormData($('#gisForm')[0]);

            // Agregar el estado manualmente
            formData.append('state', state);

            $.ajax({
                url: $('#gisForm').attr('action'),
                method: 'POST',
                data: formData,
                processData: false, // Necesario para enviar FormData correctamente
                contentType: false, // No establecer un tipo de contenido para los archivos
                success: function(response) {
                    loadingMain(false);

                    Swal.fire({
                        icon: 'success',
                        title: 'Solicitud enviada correctamente',
                        confirmButtonText: 'OK',
                        confirmButtonColor: '#91c845'
                    })

                    // Habilitar nuevamente los campos
                    $('.dinamic_grayed_input').prop('disabled', false);
                    $('.dinamic_grayed_input.ui.dropdown').removeClass('disabled').find('input').prop(
                        'disabled', false);

                    location.reload();
                },
                error: function(xhr) {
                    loadingMain(false);
                    $('.dinamic_grayed_input').prop('disabled', false);
                    $('.dinamic_grayed_input.ui.dropdown').removeClass('disabled').find('input').prop(
                        'disabled', false);

                    // Extraer mensaje de error de la API o usar mensaje por defecto
                    let errorMessage = 'Hubo un error al procesar la solicitud';
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMessage += `: ${xhr.responseJSON.message}`;
                    } else if (xhr.responseText) {
                        errorMessage += `: ${xhr.responseText}`;
                    }

                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: errorMessage,
                        confirmButtonText: 'Cerrar',
                        confirmButtonColor: '#91c845'
                    });

                    console.error(xhr);
                }
            });

            
        }



        function enableDinamic(params) {
            $(`#${params} input, #${params} textarea, #${params} button `).removeClass('dinamic_grayed_input');
            $(`#${params} .ui.dropdown`).removeClass('dinamic_grayed_input');
        }

        //reglas campos por cada face
        const validations = {
            determinacion_origen: function(omitDiagnostics = false) {
                const baseValidations = {
                    number_dictament: {
                        identifier: 'number_dictament',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor ingresa el número de dictamen.'
                        }]
                    },
                    sustentation: {
                        identifier: 'sustentation',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor ingresa sustentatión.'
                        }]
                    },
                    event_type: {
                        identifier: 'event_type',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor selecciona el tipo de evento.'
                        }]
                    },
                    siniestro_category: {
                        identifier: 'siniestro_category',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor selecciona la categorización del siniestro.'
                        }]
                    },
                    doc_date: {
                        identifier: 'doc_date',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor ingresa la fecha del documento.'
                        }]
                    },
                    severity: {
                        identifier: 'severity',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor selecciona la severidad.'
                        }]
                    },
                    days_it: {
                        identifier: 'days_it',
                        rules: [
                            {
                                type: 'empty',
                                prompt: 'Por favor ingresa los días IT.'
                            },
                            {
                                type: 'integer',
                                prompt: 'Por favor ingresa un número válido de días IT.'
                            }
                        ]
                    },
                    qualification_date: {
                        identifier: 'qualification_date',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor ingresa la fecha de calificación.'
                        }]
                    },
                    alto_costo: {
                        identifier: 'alto_costo',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor selecciona si es de alto costo.'
                        }]
                    },
                    mortal: {
                        identifier: 'mortal',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor selecciona si es mortal.'
                        }]
                    },
                    require_extra_information: {
                        identifier: 'require_extra_information',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor seleccione si requiere información extra.'
                        }]
                    }
                };

                // Agregar validaciones de diagnostics[0] si no se deben omitir
                if (!omitDiagnostics) {
                    Object.assign(baseValidations, {
                        'diagnostics[0][calification]': {
                            identifier: 'diagnostics[0][calification]',
                            rules: [{
                                type: 'empty',
                                prompt: 'Por favor selecciona una calificación.'
                            }]
                        },
                        'diagnostics[0][code]': {
                            identifier: 'diagnostics[0][code]',
                            rules: [{
                                type: 'empty',
                                prompt: 'Por favor ingresa el código CIE 10.'
                            }]
                        },
                        'diagnostics[0][diagnosis]': {
                            identifier: 'diagnostics[0][diagnosis]',
                            rules: [{
                                type: 'empty',
                                prompt: 'Por favor ingresa el nombre del diagnóstico.'
                            }]
                        },
                        'diagnostics[0][laterality]': {
                            identifier: 'diagnostics[0][laterality]',
                            rules: [{
                                type: 'empty',
                                prompt: 'Por favor selecciona la lateralidad.'
                            }]
                        },
                        'diagnostics[0][origin]': {
                            identifier: 'diagnostics[0][origin]',
                            rules: [{
                                type: 'empty',
                                prompt: 'Por favor selecciona el origen.'
                            }]
                        }
                    });
                }

                return baseValidations;
            },
            caso_cerrado: function() {
                return {
                    interested_party: {
                        identifier: 'interested_party',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor, selecciona la parte interesada que controvierte.'
                        }]
                    },
                    controversy_reason: {
                        identifier: 'controversy_reason',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor, selecciona el motivo de controversia.'
                        }]
                    },
                    controversy_date: {
                        identifier: 'controversy_date',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor, selecciona la fecha de controversia.'
                        }]
                    },
                    controversy_support_document: {
                        identifier: 'controversy_support_document',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor, adjunta el documento soporte de controversia.'
                        }]
                    }
                }
            },
            tramite_controversia: function() {
                return {
                    name_comite: {
                        identifier: 'name_comite',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor, ingresa los nombres del Comité MNK que realiza la revisión.'
                        }]
                    },
                    controversy_result: {
                        identifier: 'controversy_result',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor, selecciona el resultado de la revisión.'
                        }]
                    },
                    mnk_support_document: {
                        identifier: 'mnk_support_document',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor, adjunta el soporte del comité MNK.'
                        }]
                    },
                    mnk_observations: {
                        identifier: 'mnk_observations',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor, ingresa las observaciones.'
                        }]
                    },
                    controversy_support_document: {
                        identifier: 'controversy_support_document',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor, adjunta el documento soporte de controversia.'
                        }]
                    }
                };

            },
            faseDos: function() {
                return {
                    follow_up_date: {
                        identifier: 'follow_up_date',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor selecciona la fecha de seguimiento.'
                        }]
                    },
                    cause_for_closure: {
                        identifier: 'cause_for_closure',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor selecciona una causa de cierre.'
                        }]
                    },
                    rehabilitation_detail: {
                        identifier: 'rehabilitation_detail',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor selecciona un detalle de rehabilitación.'
                        }]
                    },
                    functional_prognosis: {
                        identifier: 'functional_prognosis',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor selecciona un pronóstico funcional.'
                        }]
                    },
                    rehabilitation_detail: {
                        identifier: 'rehabilitation_detail',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor selecciona un detalle de rehabilitación.'
                        }]
                    },
                    event_type_follow: {
                        identifier: 'event_type_follow',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor selecciona un tipo de evento.'
                        }]
                    },
                    medical_center: {
                        identifier: 'medical_center',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor selecciona un centro médico.'
                        }]
                    },
                    bmi_details: {
                        identifier: 'bmi_details',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor selecciona una opción para BMI.'
                        }]
                    },

                    treatment_end_date: {
                        identifier: 'treatment_end_date',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor selecciona una fecha de fin del tratamiento.'
                        }]
                    },
                    expected_disability_days: {
                        identifier: 'expected_disability_days',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor introduce el número de días esperados de incapacidad.'
                        }]
                    },
                    actuarial_severity_evaluacion: {
                        identifier: 'actuarial_severity_evaluacion',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor selecciona una severidad actuarial.'
                        }]
                    },
                    diagnosis_description: {
                        identifier: 'diagnosis_description',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor proporciona una descripción del diagnóstico.'
                        }]
                    },
                    clinical_summary: {
                        identifier: 'clinical_summary',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor escribe un resumen de la historia clínica y laboral.'
                        }]
                    },
                    current_status: {
                        identifier: 'current_status',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor describe el estado actual del paciente.'
                        }]
                    },
                    recovery_possibility: {
                        identifier: 'recovery_possibility',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor describe la posibilidad de recuperación.'
                        }]
                    },
                    patient_prognosis: {
                        identifier: 'patient_prognosis',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor selecciona un pronóstico del paciente.'
                        }]
                    },
                    treatment_purpose: {
                        identifier: 'treatment_purpose',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor selecciona una finalidad del tratamiento.'
                        }]
                    },
                };
            },
            solicitud_autorizaciones: function() {
                return {
                    // solicitud de autorizaciones
                    'solicitud_autorizaciones[0][authorization_request_autorizacion]': {
                        identifier: 'authorization_request_autorizacion',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor, selecciona la fecha de solicitud de autorización.'
                        }]
                    },
                    'solicitud_autorizaciones[0][request_description_autorizacion]': {
                        identifier: 'request_description_autorizacion',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor, ingresa la descripción de la solicitud.'
                        }]
                    },
                    'solicitud_autorizaciones[0][request_reason_autorizacion]': {
                        identifier: 'request_reason_autorizacion',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor, selecciona el motivo de la solicitud.'
                        }]
                    },
                    'solicitud_autorizaciones[0][effective_notification_date_autorizacion]': {
                        identifier: 'effective_notification_date_autorizacion',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor, selecciona la fecha de notificación efectiva.'
                        }]
                    },
                }
            },
            seguimiento_plan_tratamiento: function() {
                return {
                    // seguimiento plan de tratamiento
                    'plan_tratamiento[0][authorization_date_plan]': {
                        identifier: 'plan_tratamiento[0][authorization_date_plan]',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor selecciona una fecha para la próxima valoración.'
                        }]
                    },
                    'plan_tratamiento[0][provider_name]': {
                        identifier: 'plan_tratamiento[0][provider_name]',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor ingresa el nombre del proveedor.'
                        }]
                    },
                    'plan_tratamiento[0][doctor_name]': {
                        identifier: 'plan_tratamiento[0][doctor_name]',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor ingresa el nombre del médico.'
                        }]
                    },
                    'plan_tratamiento[0][specialty]': {
                        identifier: 'plan_tratamiento[0][specialty]',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor selecciona una especialidad.'
                        }]
                    },
                    'plan_tratamiento[0][result_description]': {
                        identifier: 'plan_tratamiento[0][result_description]',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor proporciona una descripción del resultado.'
                        }]
                    },
                    'plan_tratamiento[0][observations]': {
                        identifier: 'plan_tratamiento[0][observations]',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor agrega observaciones.'
                        }]
                    },
                    'plan_tratamiento[0][case_closure_abandonment]': {
                        identifier: 'plan_tratamiento[0][case_closure_abandonment]',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor selecciona caso por abandono.'
                        }]
                    },

                }
            },
            // seguimiento auditoria del sinistro
            auditoria_sinistro: function() {
                return {
                    authorization_date: {
                        identifier: 'authorization_date',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor selecciona una fecha de seguimiento o auditoría.'
                        }]
                    },
                    follow_up_type: {
                        identifier: 'follow_up_type',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor ingresa el tipo de seguimiento.'
                        }]
                    },
                    result_description_auditoria: {
                        identifier: 'result_description_auditoria',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor proporciona una descripción del resultado.'
                        }]
                    },
                }
            },
            rehabilitacion: function() {
                return {
                    //cierre siniestro - rehabilitacion
                    has_closure_rehabilitacion: {
                        identifier: 'has_closure_rehabilitacion',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor, selecciona si hay cierre siniestro.'
                        }]
                    },
                    closure_date_rehabilitacion: {
                        identifier: 'closure_date_rehabilitacion',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor, selecciona la fecha de cierre.'
                        }]
                    },
                    requires_recommendations_rehabilitacion: {
                        identifier: 'requires_recommendations_rehabilitacion',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor, indica si requiere recomendaciones.'
                        }]
                    },
                    suspend_disability_rehabilitacion: {
                        identifier: 'suspend_disability_rehabilitacion',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor, indica si se suspende la incapacidad.'
                        }]
                    },
                    medical_discharge_rehabilitacion: {
                        identifier: 'medical_discharge_rehabilitacion',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor, indica si alcanzó la alta médica.'
                        }]
                    },
                    reason_medical_discharge_rehabilitacion: {
                        identifier: 'reason_medical_discharge_rehabilitacion',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor, especifica el motivo de la alta médica.'
                        }]
                    }
                }
            },
            reincorporacion: function() {
                return {
                    //cierre siniestro - reincorporacion
                    reintegration_date: {
                        identifier: 'reintegration_date',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor, ingresa la fecha de reintegro.'
                        }]
                    },
                    requires_support: {
                        identifier: 'requires_support',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor, selecciona si requiere acompañamiento.'
                        }]
                    },
                    requires_visit: {
                        identifier: 'requires_visit',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor, selecciona si requiere visita.'
                        }]
                    },
                    medical_discharge_reintegration: {
                        identifier: 'medical_discharge_reintegration',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor, selecciona si alcanzó la alta médica.'
                        }]
                    }
                }
            },
            faseTres: function() {
                return {
                    //calificacion:
                    calificacion_date: {
                        identifier: 'calificacion_date',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor, selecciona la fecha de calificación.'
                        }]
                    },
                    event_date: {
                        identifier: 'event_date',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor, selecciona la fecha del evento.'
                        }]
                    },
                    case_number: {
                        identifier: 'case_number',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor, ingresa el número de caso.'
                        }]
                    },
                    discharge_date: {
                        identifier: 'discharge_date',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor, selecciona la fecha de alta.'
                        }]
                    },
                    work_recommendations: {
                        identifier: 'work_recommendations',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor, ingresa las recomendaciones laborales.'
                        }]
                    },
                    variation_request_description_1: {
                        identifier: 'variation_request_description_1',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor, describe las lesiones.'
                        }]
                    },
                    variation_request_description_2: {
                        identifier: 'variation_request_description_2',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor, describe el impedimento.'
                        }]
                    },
                    accident_description_calificacion: {
                        identifier: 'accident_description_calificacion',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor, ingresa una descripción en observaciones.'
                        }]
                    },
                    brain_dominance: {
                        identifier: 'brain_dominance',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor, selecciona la dominancia cerebral.'
                        }]
                    },
                    medical_preconditions: {
                        identifier: 'medical_preconditions',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor, describe las preexistencias médicas.'
                        }]
                    },
                    cofactors: {
                        identifier: 'cofactors',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor, selecciona una opción en concausas.'
                        }]
                    },
                    'calificacion_pcg[0][affected_member]': {
                        identifier: 'calificacion_pcg[0][affected_member]',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor, especifica el miembro afectado.'
                        }]
                    },
                    'calificacion_pcg[0][loss_percentage]': {
                        identifier: 'calificacion_pcg[0][loss_percentage]',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor, ingresa el % de pérdida.'
                        }]
                    },
                    total_pcg: {
                        identifier: 'total_pcg',
                        rules: [{
                                type: 'empty',
                                prompt: 'Por favor, ingresa el % total PCG.'
                            },
                            {
                                type: 'integer[0..100]',
                                prompt: 'El % total PCG debe estar entre 0 y 100.'
                            }
                        ]
                    }
                }
            },
            auditoria_calificacion_pcg: function() {
                return { //auditoria calificacion pcg
                    agreement_members: {
                        identifier: 'agreement_members',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor, selecciona si estás de acuerdo con los miembros afectados.'
                        }]
                    },
                    agreement_percentages: {
                        identifier: 'agreement_percentages',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor, selecciona si estás de acuerdo con los porcentajes asignados.'
                        }]
                    },
                    audit_result_pcg: {
                        identifier: 'audit_result_pcg',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor, selecciona el resultado de auditoría.'
                        }]
                    },
                }
            },
            subrogacion: function() {
                return {
                    incapacidades_temporales: {
                        identifier: 'incapacidades_temporales',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor ingrese relación de costo de incapacidades.'
                        }]
                    },
                    incapacidades_permanentes: {
                        identifier: 'incapacidades_permanentes',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor ingrese incapacidades permanentes pagadas.'
                        }]
                    },
                    reconocimiento_gastos: {
                        identifier: 'reconocimiento_gastos',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor ingrese reconocimiento de gastos.'
                        }]
                    },
                    prestaciones_medicas: {
                        identifier: 'prestaciones_medicas',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor ingrese prestaciones médicas.'
                        }]
                    },
                    total_costos: {
                        identifier: 'total_costos',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor ingrese total de costos.'
                        }]
                    },
                    demandado_tipo_identificacion: {
                        identifier: 'demandado_tipo_identificacion',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor tipo de identificación.'
                        }]
                    },
                    demandado_num_identificacion: {
                        identifier: 'demandado_num_identificacion',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor ingrese número de identificación.'
                        }]
                    },
                    demandado_telefono: {
                        identifier: 'demandado_telefono',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor ingrese número de teléfono.'
                        }]
                    },
                    demandado_email: {
                        identifier: 'demandado_email',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor ingrese correo electrónico.'
                        }, {
                            type: 'email',
                            prompt: 'Por favor ingrese un correo electrónico válido.'
                        }]
                    },
                    demandado_direccion: {
                        identifier: 'demandado_direccion',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor ingrese dirección de residencia.'
                        }]
                    },
                    rep_legal_nombre: {
                        identifier: 'rep_legal_nombre',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor ingrese nombre del representante legal.'
                        }]
                    },
                    rep_legal_cedula: {
                        identifier: 'rep_legal_cedula',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor ingrese cédula del representante legal.'
                        }]
                    },
                    demandado_nombre: {
                        identifier: 'demandado_nombre',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor ingrese nombre del demandado.'
                        }]
                    },
                    expediente_judicial: {
                        identifier: 'expediente_judicial',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor ingrese número de expediente judicial.'
                        }]
                    },
                    juzgado_caso: {
                        identifier: 'juzgado_caso',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor ingrese juzgado donde se tramita el caso.'
                        }]
                    },
                    numero_proceso: {
                        identifier: 'numero_proceso',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor ingrese número de proceso judicial.'
                        }]
                    },
                    fecha_inicio_juicio: {
                        identifier: 'fecha_inicio_juicio',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor ingrese fecha de inicio del juicio.'
                        }]
                    },
                    estado_proceso: {
                        identifier: 'estado_proceso',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor ingrese estado del proceso judicial.'
                        }]
                    },
                }
            },
        };

        // Obtiene las reglas de validación para la fase seleccionada
        //let rules = validations[isEnable] ? validations[isEnable]() : {};

        let omitDiagnostics = true;
        // Obtener reglas de validación con el parámetro
        let rules = validations[isEnable] ? validations[isEnable](omitDiagnostics) : {};

        $(document).ready(function() {

            // Evento para enviar el formulario con el botón de Guardar
            $('#submitButton').on('click', function(event) {
                loadingMain(true);
                event.preventDefault();

                // Deshabilita los campos con clase .dinamic_grayed_input antes del envío
                $('.dinamic_grayed_input').prop('disabled', true);
                $('.dinamic_grayed_input.ui.dropdown').addClass('disabled').find('input').prop('disabled', true);

                let validaDiagnosticos = false;
                const totalCalifications = $('[name^="diagnostics["][name*="[calification]"]').length;

                if (totalCalifications === 1) {
                    const diagnostics = $('[name^="diagnostics["][name*="[calification]"]');

                    diagnostics.each(function() {
                        const nameAttr = $(this).attr('name');
                        const indexMatch = nameAttr.match(/diagnostics\[(\d+)\]/);

                        if (!indexMatch) return;

                        const index = indexMatch[1];

                        const calification = $(`[name="diagnostics[${index}][calification]"]`).val();
                        const code        = $(`[name="diagnostics[${index}][code]"]`).val();
                        const diagnosis   = $(`[name="diagnostics[${index}][diagnosis]"]`).val();
                        const laterality  = $(`[name="diagnostics[${index}][laterality]"]`).val();
                        const origin      = $(`[name="diagnostics[${index}][origin]"]`).val();

                        if (!calification && !code && !diagnosis && !laterality && !origin) {
                            validaDiagnosticos = true;
                        }
                    });
                }

                let omitDiagnostics = ($('#cant_diagnostics').val() > 0 && totalCalifications === 1 && validaDiagnosticos);

                rules = validations[isEnable] ? validations[isEnable](omitDiagnostics) : {};

                formInicial();
                $('#gisForm').form('validate form'); // Valida el formulario completo
            });


        });
    </script>

    <script>
        $(document).ready(function() {
            $.getJSON('/js/cie10.json', function(json) {
                cie10 = json;
                $('form .ui.search.diagnostic.code').search({
                    source: cie10,
                    fields: {
                        title: 'COD',
                        description: 'DESCRIPTION'
                    },
                    searchFields: ['COD', 'DESCRIPTION'],
                    regExp: {
                        escape: /[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,
                        beginsWith: ''
                    },
                    showNoResults: true,
                    maxResults: 250,
                    searchFullText: false,
                    error: {
                        noResults: 'No se encontraron resultados para tu búsqueda.'
                    },
                    onSelect: function(result, response) {
                        $(this).parents('.fields').find('input.description').val(result
                            .DESCRIPTION);
                    }
                });
            });
        });
    </script>

    <script>
        let addDiagnostic = function(e) {
            let $fields = $('#diagnostic_model').clone(true);
            $fields.removeAttr('id');
            $fields.find('a').click(function() {
                $(this).parent().parent().remove();
            });
            $fields.find('.ui.dropdown').dropdown({
                forceSelection: false
            });
            $fields.find('.ui.search.diagnostic input').change(function() {
                let valid = false;

                $(this).parents('.fields').find('input.description').val('');

                for (let i = 0; i < cie10.length; i++) {
                    if (cie10[i].COD == $(this).val().toUpperCase()) {
                        $(this).parents('.fields').find('input.description').val(cie10[i].DESCRIPTION);
                        valid = true;
                    }
                }

                if (!valid && $(this).val() != '') {
                    $(this).val('');
                }
            });
            $fields.find('.ui.search.diagnostic').search({
                source: cie10,
                fields: {
                    title: 'COD',
                    description: 'DESCRIPTION'
                },
                searchFields: ['COD', 'DESCRIPTION'],
                regExp: {
                    escape: /[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,
                    beginsWith: ''
                },
                showNoResults: true,
                maxResults: 250,
                searchFullText: false,
                error: {
                    noResults: 'No se encontraron resultados para tu búsqueda.'
                },
                onSelect: function(result, response) {
                    $(this).parents('.fields').find('input.description').val(result.DESCRIPTION);
                }
            });
            $('#diagnostics').append($fields);
            $fields.show();
            return false;
        };
    </script>
@endsection
