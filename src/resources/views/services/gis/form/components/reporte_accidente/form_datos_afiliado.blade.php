<div class="title">
    <i class="dropdown icon"></i>
    Datos afiliado <span style="color: red;" class="required">*</span>
</div>

<div class="content">
    <div class="four fields">
        <div class="required field">
            <label>Tipo de identificación</label>
            <div class="ui selection dropdown grayed-input" id="docTypeDropdown">
                <input type="hidden">
                <i class="dropdown icon"></i>
                <div class="default text">Selecciona una opción</div>
                <div class="menu">
                    @foreach ($DOC_TYPES as $k => $v)
                        <div class="item" data-value="{{ $k }}">
                            {{ ucfirst(mb_strtolower($v)) }}
                        </div>
                    @endforeach
                </div>
            </div>
        </div>

        <div class="required field">
            <label for="docNumber">Número de identificación</label>
            <div class="ui icon input">
                <input id="docNumber" type="text" autocomplete="off" placeholder="12345678" class="grayed-input">
            </div>
        </div>

        <div class="required field">
            <label for="name">Nombre</label>
            <div class="ui icon input ">
                <input id="name" type="text" autocomplete="off" placeholder="Nombre completo"
                    class="grayed-input">
            </div>
        </div>

        <div class="field">
            <label for="nacionalidad">Nacionalidad</label>
            <div class="ui fluid search selection  dropdown grayed-input" id="nacionality">
                <input type="hidden" id="nacionalidadInput">
                <i class="dropdown icon"></i>
                <div class="default text">Seleccionar nacionalidad</div>
                <div class="menu paises">
                    <!-- Las opciones de países se cargarán aquí -->
                </div>
            </div>
        </div>
    </div>

    <div class="four fields">
        <div class="required field">
            <label for="phone">Celular</label>
            <div class="ui input">
                <input id="phone" type="tel" autocomplete="off" placeholder="8888-8888" class="grayed-input">
            </div>
        </div>

        <div class="required field">
            <label for="email">Correo electrónico</label>
            <div class="ui icon input">
                <input id="email" type="email" autocomplete="off" placeholder="<EMAIL>"
                    class="grayed-input">
            </div>
        </div>

        <div class="required field">
            <label for="civilStatus">Estado civil</label>
            <div id="civil_status" class="ui selection dropdown grayed-input">
                <input type="hidden" class="minus">
                <i class="dropdown icon"></i>
                <div class="default text">Selecciona una opción</div>
                <div class="menu">
                    @foreach ($CIVIL_STATUS as $k => $v)
                        <div class="item" data-value="{{ $k }}">
                            {{ ucfirst(mb_strtolower($v)) }}
                        </div>
                    @endforeach
                </div>
            </div>
        </div>

        <div class="required field">
            <label>Escolaridad</label>
            <div id="escolaridad" class="ui search selection dropdown grayed-input">
                <input type="hidden" class="minus">
                <i class="dropdown icon"></i>
                <div class="default text">Selecciona una opción</div>
                <div class="menu">
                    @foreach ($SCHOOL_LEVELS as $k => $v)
                        <div class="item" data-value="{{ $k }}">
                            {{ ucfirst(mb_strtolower($v)) }}
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>

    <div class="four fields">
        <div class="required field">
            <label>Provincia</label>
            <div id="province" class="ui search selection dropdown grayed-input">
                <input type="hidden" class="minus"
                    value="{{ $activity->affiliate ? $activity->affiliate->province : '' }}">
                <i class="dropdown icon"></i>
                <div class="default text">Selecciona uno</div>
                <div class="menu"></div>
            </div>
        </div>
        <div class="required field">
            <label>Cantón</label>
            <div id="canton" class="ui search selection dropdown grayed-input">
                <input type="hidden" class="minus"
                    value="{{ $activity->affiliate ? $activity->affiliate->canton : '' }}">
                <i class="dropdown icon"></i>
                <div class="default text">Selecciona uno</div>
                <div class="menu"></div>
            </div>
        </div>

        <div class="required field">
            <label>Distrito</label>
            <div id="district" class="ui search selection dropdown grayed-input">
                <input type="hidden" class="minus"
                    value="{{ $activity->affiliate ? $activity->affiliate->district : '' }}">
                <i class="dropdown icon"></i>
                <div class="default text">Selecciona uno</div>
                <div class="menu"></div>
            </div>
        </div>
        <div class="required field">
            <label>Otras señas</label>
            <input type="text" placeholder="Otras señas" id="otras_señas" class="grayed-input">
        </div>
    </div>

    <div class="four fields">
        <div class=" field">
            <label for="iban">Cuenta IBAN</label>
            <div class="ui icon input">
                <input id="iban" type="text" autocomplete="off" placeholder="IBAN" pattern="[A-Z0-9]{1,21}"
                    maxlength="21" class="grayed-input">
            </div>
        </div>

        <div class=" field">
            <label for="iban">Grupo de edad</label>
            <div class="ui icon input">
                <input id="grupo_edad" name="grupo_edad" type="text" class="grayed-input">
            </div>
        </div>
    </div>
</div>



@push('scripts')
    <script>
        $(document).ready(function() {

            const {
                affiliate: {
                    doc_type,
                    doc_number,
                    full_name,
                    phone,
                    cellphone,
                    email,
                    country,
                    province,
                    district,
                    canton,
                    employer_address,
                    iban_account,
                    civil_status,
                    school_level,
                    policy_spreadsheet_affiliate
                }
            } = @json($activity);

            $('#docTypeDropdown').dropdown({
                onChange: function(value, text, $selectedItem) {

                    if (value === 'CF') {
                        $('#nacionality').dropdown('set selected', 'CR');
                    }
                }
            });

            $('#docTypeDropdown').dropdown('set selected', doc_type || policy_spreadsheet_affiliate[0]?.id_type);
            $('#docNumber').val(doc_number || policy_spreadsheet_affiliate[0]?.identification_number);

            $('#name').val(capitalizeFirstLetterOfEachWord(full_name));
            $('#phone').val(cellphone || policy_spreadsheet_affiliate[0]?.phone);
            $('#email').val(email || policy_spreadsheet_affiliate[0]?.email);

            if (province) {
                $('#province').dropdown('set selected', province);
            }
            if (canton) {
                $('#canton').dropdown('set selected', canton);
            }
            if (district) {
                $('#district').dropdown('set selected', district);
            }
            if (civil_status) {
                $('#civil_status').dropdown('set selected', civil_status);
            }
            if (school_level) {
                $('#escolaridad').dropdown('set selected', school_level);
            }
            if (employer_address) {
                $('#otras_señas').val(employer_address);
            }
            if (iban_account) {
                $('#iban').val(iban_account);
            }
            if (phone) {
                $('#phone').val(phone);
            }

            jsonData();

            async function cargarPaises(menu, paisesData) {
                // Limpiar el menú antes de agregar nuevos elementos
                menu.empty();

                // Agregar los países al menú
                paisesData.forEach(pais => {
                    menu.append(`
                <div class="item" data-value="${pais.country_short_name}">
                    <i class="${pais.country_short_name.toLowerCase()} flag"></i> ${pais.country_name}
                </div>
            `);
                });
            }

            async function jsonData() {
                try {
                    // Fetch de los datos de los países
                    const response = await fetch("/js/paises.json");
                    if (!response.ok) throw new Error(`Error al cargar el archivo: ${response.statusText}`);

                    const paisesData = await response.json();

                    // Seleccionar el menú donde se cargarán los países
                    const menu = $('.paises');

                    // Cargar los países en el menú
                    await cargarPaises(menu, paisesData);

                    // Establecer el valor seleccionado basado en el dato inicial
                    const nationality = country || policy_spreadsheet_affiliate[0]?.nationality;

                    console.log('nationality::'+nationality);

                    if (nationality) {
                        const selectedItem = menu.find(`.item[data-value="${nationality}"]`);

                        if (selectedItem.length > 0) {
                            selectedItem.addClass('active selected');
                            $('#nacionality').dropdown('set selected', nationality);
                        }
                    }

                    // Inicializar el dropdown después de cargar los datos
                    $('#nacionality').dropdown();
                } catch (error) {
                    console.error("Error al cargar los datos:", error);
                }
            }

            const fechaNacimiento = policy_spreadsheet_affiliate[0]?.date_of_birth;
            const rangoEdad = getAgeRange(fechaNacimiento);
            $('#grupo_edad').val(rangoEdad);

        });

        function getAgeRange(dateOfBirthStr) {
            if (!dateOfBirthStr) return 'Ignorado';

            const today = new Date();
            const birthDate = new Date(dateOfBirthStr);
            if (isNaN(birthDate)) return 'Ignorado';

            let age = today.getFullYear() - birthDate.getFullYear();
            const monthDiff = today.getMonth() - birthDate.getMonth();
            const dayDiff = today.getDate() - birthDate.getDate();

            if (monthDiff < 0 || (monthDiff === 0 && dayDiff < 0)) {
                age--;
            }

            if (age < 15) return 'Menores de 15 años';
            if (age < 18) return 'De 15 a menos de 18';
            if (age < 20) return 'De 18 a menos de 20';
            if (age < 25) return 'De 20 a menos de 25';
            if (age < 30) return 'De 25 a menos de 30';
            if (age < 35) return 'De 30 a menos de 35';
            if (age < 40) return 'De 35 a menos de 40';
            if (age < 45) return 'De 40 a menos de 45';
            if (age < 50) return 'De 45 a menos de 50';
            if (age < 55) return 'De 50 a menos de 55';
            if (age < 60) return 'De 55 a menos de 60';
            if (age < 65) return 'De 60 a menos de 65';
            if (age >= 65) return 'De 65 años o más';

            return 'Ignorado';
        }

    </script>

    <script>
        const ibanInput = document.getElementById('iban');
        const ibanError = document.getElementById('ibanError');

        ibanInput.addEventListener('input', function() {
            // Convertir a mayúsculas automáticamente
            ibanInput.value = ibanInput.value.toUpperCase();

            const ibanValue = ibanInput.value;

            // Validar longitud
            if (ibanValue.length < 1 || ibanValue.length > 21) {
                ibanError.textContent = "El IBAN debe tener entre 1 y 21 caracteres alfanuméricos.";
                ibanError.style.display = 'block';
                return;
            } else {
                ibanError.style.display = 'none';
            }


            const pattern = /^[A-Za-z0-9]*$/;
            if (!pattern.test(ibanValue)) {
                ibanError.textContent = "El IBAN debe contener solo caracteres alfanuméricos.";
                ibanError.style.display = 'block';
            } else {
                ibanError.style.display = 'none';
            }
        });
    </script>
    <script>
        const capitalizeFirstLetterOfEachWord = (string) => {

            if (string) {
                return string
                    .split(' ') // Divide el string en palabras
                    .map(word => {
                        // Si el primer carácter es una letra, lo capitaliza
                        if (word.length > 0 && /[A-Za-z]/.test(word.charAt(0))) {
                            return word.charAt(0).toUpperCase() + word.slice(1)
                                .toLowerCase(); // Capitaliza la primera letra y convierte el resto a minúscula
                        }
                        return word; // De lo contrario, devuelve la palabra tal cual (por ejemplo, si es un número)
                    })
                    .join(' '); // Une las palabras nuevamente en un string
            }
            return ''
        };
    </script>
{{--    <script>--}}
{{--        let paisesData = [];--}}

{{--        // Cargar los países al iniciar--}}
{{--        $.getJSON("/js/paises.json", function(paises) {--}}
{{--            paisesData = paises;--}}
{{--            cargarPaises($('.paises'));--}}

{{--        });--}}

{{--        function cargarPaises(menu) {--}}
{{--            // Limpiar el menú antes de agregar nuevos elementos--}}
{{--            menu.empty();--}}

{{--            $.each(paisesData, function(index, pais) {--}}
{{--                menu.append(`<div class="item" data-value="${pais.country_short_name}">--}}
{{--                                <i class="${pais.country_short_name.toLowerCase()} flag"></i> ${pais.country_name} --}}
{{--                            </div>`);--}}
{{--            });--}}
{{--        }--}}
{{--    </script>--}}
@endpush
