<div class="accordion transition">
    <div class="title"><i class="dropdown icon"></i>Enfermedad</div>
    <div class="content">
        <div class="three fields">

            <div class="required field">
                <label for="symptoms_start_date">Fecha que inició con los síntomas</label>
                <div class="ui input">
                    <input name="symptoms_start_date" id="symptoms_start_date" type="date" autocomplete="off"
                        value="{{ $gis->date_accident }}" max="{{ date('Y-m-d') }}">
                </div>
            </div>

            <div class="required field">
                <label for="agent_involved">Agente Involucrado</label>
                <div class="ui selection dropdown dropdown dropdown_dinamic" id="agentInvolvedDropdown">
                    <input type="hidden" name="agent_involved" value="{{ $gis->agent_involved }}">
                    <i class="dropdown icon"></i>
                    <div class="default text">Selecciona una opción</div>
                    <div class="menu">
                        <div class="item" data-value="agente_fisico">Agente físico</div>
                        <div class="item" data-value="agente_biologico">Agente biológico</div>
                        <div class="item" data-value="factores_psicosociales">Factores psicosociales</div>
                        <div class="item" data-value="enfermedades_osteomuscular">Enfermedades del sistema
                            osteomuscular</div>
                        <div class="item" data-value="otros">Otros</div>
                    </div>
                </div>
            </div>

            <div class="required field">
                <label for="symptoms_description">Descripción de síntomas</label>
                <div class="ui input">
                    <textarea rows="2" 
                        name="symptoms_description" id="accident_description" type="text" autocomplete="off"
                        placeholder="Describe los síntomas"> {{ $gis->accident_description ?? '' }} </textarea>
                </div>
            </div>
        </div>

        <div class="three fields">
            <div class="required field">
                <label for="ocurrencia">Enfermedades profesionales </label>
                <div class="ui selection dropdown dropdown_dinamic grayed-input" id="dropdownDisease">
                    <input type="hidden" name="disease_report_type_id" id="disease_report_type_id" value="{{ $gis->disease_report_type_id ?? '' }}" />
                    <i class="dropdown icon"></i>
                    <div class="default text">Selecciona una opción</div>
                    <div class="menu">
                        @foreach ($occupationalDisease as $row)
                            <div class="item" data-value="{{ $row->id }}">{{ $row->name }}</div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>

        {{-- formulario muñeco --}}
        @include('services.gis.form.components.reporte_accidente.components.form_dinamico_muneco')

        <div class="three fields">
            <div class="required field">
                <label for="mortal">Requiere ayuda de tercero</label>
                <div class="ui selection dropdown dropdown_dinamic" id="dropayuda">
                    <input type="hidden" name="ayuda" id="ayuda" value="{{ $gis->ayuda }}">
                    <i class="dropdown icon"></i>
                    <div class="default text">Selecciona una opción</div>
                    <div class="menu">
                        <div class="item" data-value="1">SI</div>
                        <div class="item" data-value="0">NO</div>
                    </div>
                </div>
            </div>
        </div>

    </div>
</div>
<style>
    .hidden {
        display: none;
    }
</style>
