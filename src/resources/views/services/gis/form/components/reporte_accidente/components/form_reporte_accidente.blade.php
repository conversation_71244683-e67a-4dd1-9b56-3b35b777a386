<div class="accordion transition">
    <div class="title">
        <i class="dropdown icon"></i>Accidente
    </div>
    <div class="content">
        <!-- <PERSON><PERSON>, Hora y Descripción del Accidente -->
        <div class="three fields">
            <div class="required field">
                <label for="accident_date">Fe<PERSON> del accidente</label>
                <div class="ui input">
                    <input name="accident_date" id="accident_date" type="date" autocomplete="off" />
                </div>
            </div>

            <div class="required field">
                <label for="accident_time">Hora del accidente</label>
                <div class="ui input">
                    <input name="accident_time" id="accident_time" type="time" autocomplete="off" />
                </div>
            </div>

            <div class="required field">
                <label for="accident_description">Descripción del accidente</label>
                <div class="ui input">
                    <textarea rows="2" name="accident_description" id="accident_description" type="text" autocomplete="off"
                        placeholder="Describe el accidente"> </textarea>
                </div>
            </div>
        </div>

        {{-- Formulario muñeco  --}}
        @include('services.gis.form.components.reporte_accidente.components.form_dinamico_muneco')

        <!-- Campos de selección relacionados con el accidente -->
        <div class="four fields">
            <div class="required field">
                <label for="ocurrencia">Forma de Ocurrencia</label>
                <div class="ui selection dropdown dropdown_dinamic">
                    <input type="hidden" name="ocurrencia" id="ocurrencia" />
                    <i class="dropdown icon"></i>
                    <div class="default text">Selecciona una opción</div>
                    <div class="menu">
                        @foreach ($ACCIDENT_TYPES as $k => $r)
                            <div class="item" data-value="{{ $k }}">{{ $r }}</div>
                        @endforeach
                    </div>
                </div>
            </div>

            <div class="required field">
                <label for="material_agente">Agente Material</label>
                <div class="ui selection dropdown dropdown_dinamic" id="material_agente">
                    <input type="hidden" name="material_agente" />
                    <i class="dropdown icon"></i>
                    <div class="default text">Selecciona una opción</div>
                    <div class="menu">
                        @foreach ($AGENTS as $k => $r)
                            <div class="item" data-value="{{ $k }}">{{ $r }}</div>
                        @endforeach
                    </div>
                </div>
            </div>

            <div class="required field">
                <label for="mecanismo_trauma">Mecanismo de Trauma</label>
                <div class="ui selection dropdown dropdown_dinamic">
                    <input type="hidden" name="mecanismo_trauma" id="mecanismo_trauma" />
                    <i class="dropdown icon"></i>
                    <div class="default text">Selecciona una opción</div>
                    <div class="menu">
                        @foreach ($INJURIES as $k => $r)
                            <div class="item" data-value="{{ $k }}">{{ $r }}</div>
                        @endforeach
                    </div>
                </div>
            </div>

            <div class="required field">
                <label for="ayuda_tercero">Requiere ayuda de tercero</label>
                <div class="ui selection dropdown dropdown_dinamic">
                    <input type="hidden" name="ayuda_tercero" id="ayuda_tercero" />
                    <i class="dropdown icon"></i>
                    <div class="default text">Selecciona una opción</div>
                    <div class="menu">
                        <div class="item" data-value="1">SI</div>
                        <div class="item" data-value="0">NO</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Modalidad de trabajo, fallecimiento y fecha de desaparición -->
        <div class="four fields">
            <div class="required field">
                <label for="modalidad_trabajo">Modalidad de trabajo</label>
                <div class="ui selection dropdown dropdown_dinamic">
                    <input type="hidden" name="modalidad_trabajo" id="modalidad_trabajo" />
                    <i class="dropdown icon"></i>
                    <div class="default text">Selecciona una opción</div>
                    <div class="menu">
                        @foreach ($WORK_MODES as $k => $r)
                            <div class="item" data-value="{{ $k }}">{{ $r }}</div>
                        @endforeach
                    </div>
                </div>
            </div>
            <!-- Condiciones de fallecimiento/desaparición -->
            @if ($condition_gis_value === 'desaparecido' || $condition_gis_value === 'fallecido')
                <div class="required field">
                    <label for="fallecimiento_lugar">Lugar de fallecimiento/Desaparición</label>
                    <div class="ui selection dropdown dropdown_dinamic">
                        <input type="hidden" name="fallecimiento_lugar" id="fallecimiento_lugar" />
                        <i class="dropdown icon"></i>
                        <div class="default text">Selecciona una opción</div>
                        <div class="menu">
                            <div class="item" data-value="Lugar de trabajo">Lugar de trabajo</div>
                            <div class="item" data-value="Otro">Otro</div>
                        </div>
                    </div>
                </div>
            @endif
            @if ($condition_gis_value === 'desaparecido')
                <div class="required field">
                    <label for="desaparicion_fecha">Fecha de desaparición</label>
                    <div class="ui input">
                        <input name="desaparicion_fecha" id="desaparicion_fecha" type="date"
                            autocomplete="off" />
                    </div>
                </div>
            @endif

            <div class="required field hidden" id="material_agente_otro">
                <label for="Otro">Otro agente</label>
                <div class="ui input">
                    <input name="material_agente_otro" id="material_agente_otro" type="text" autocomplete="off"
                        placeholder="Describe el agente" />
                </div>
            </div>
        </div>
    </div>
</div>


<div class="accordion transition">
    <div class="title"><i class="dropdown icon"></i>Datos de un testigo</div>
    <div class="content">
        <!-- Campos de selección relacionados con el accidente -->
        <div class="three fields">
            <div class="required field">
                <label for="lugar_accidente">Lugar del accidente</label>
                <div class="ui selection dropdown dropdown_dinamic" id="lugar_accidente">
                    <input type="hidden" name="lugar_accidente" />
                    <i class="dropdown icon"></i>
                    <div class="default text">Selecciona una opción</div>
                    <div class="menu">
                        @foreach ($WORKPLACE_LOCATIONS as $k => $r)
                            <div class="item" data-value="{{ $k }}">{{ $r }}</div>
                        @endforeach
                    </div>
                </div>
            </div>

            <div class="required field">
                <label for="causa_externa">Causa Externa</label>
                <div class="ui selection dropdown dropdown_dinamic" id="causa_externa">
                    <input type="hidden" name="causa_externa" />
                    <i class="dropdown icon"></i>
                    <div class="default text">Selecciona una opción</div>
                    <div class="menu">
                        <div class="item" data-value="1">SI</div>
                        <div class="item" data-value="0">NO</div>
                    </div>
                </div>
            </div>

            <div class="required field hidden" id="external_cause">
                <label for="external_cause">¿Cuál?</label>
                <div class="ui input">
                    <input name="external_cause" type="text" autocomplete="off"
                        placeholder="Especificar causa" />
                </div>
            </div>
        </div>

        <!-- Modalidad de trabajo, fallecimiento y primeros auxilios -->
        <div class="three fields">
            <div class="required field">
                <label for="ayuda_tercero">¿Recibió primeros auxilios o atención en un centro de salud diferente a
                    OCEANICA (MNK)?</label>
                <div class="ui selection dropdown dropdown_dinamic" id="ayuda_tercero_accidente">
                    <input type="hidden" name="ayuda_tercero" />
                    <i class="dropdown icon"></i>
                    <div class="default text">Selecciona una opción</div>
                    <div class="menu">
                        <div class="item" data-value="1">SI</div>
                        <div class="item" data-value="0">NO</div>
                    </div>
                </div>
            </div>

            <div class="required field hidden" id="auxilios_descripcion_field">
                <label for="auxilios_descripcion">¿Cuál?</label>
                <div class="ui input">
                    <input name="auxilios_descripcion" id="auxilios_descripcion" type="text" autocomplete="off"
                        placeholder="Especificar" />
                </div>
            </div>

            <div class="required field hidden" id="lugar_accidente_otro_field">
                <label for="otro_lugar">Otro lugar</label>
                <div class="ui input">
                    <input name="otro_lugar" id="otro_lugar" type="text" autocomplete="off" />
                </div>
            </div>
        </div>
    </div>
</div>
<style>
    .hidden {
        display: none;
    }
</style>
