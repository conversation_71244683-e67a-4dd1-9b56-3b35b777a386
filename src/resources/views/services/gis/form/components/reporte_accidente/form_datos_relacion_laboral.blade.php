<div class="title">
    <i class="dropdown icon"></i>
    Datos relación laboral <span style="color: red;" class="required">*</span>
</div>

<div class="content">
    <div class="four fields">
        <div class="required field">
            <label for="uniqueCode">Código único </label>
            <div class="ui input">
                <input id="uniqueCode" type="text" autocomplete="off"
                    value="{{ $activity->parent_activity ? $activity->parent_activity->policy_sort->unique_code : '' }}"
                    placeholder="12345678" class="grayed-input">
            </div>
        </div>

        <div class="required field">
            <label for="policyNumber">Póliza SORT</label>
            <div class="ui input">
                <input id="policyNumber" type="text" autocomplete="off"
                    value="{{ $activity->parent_activity ? $activity->parent_activity->policy_sort->formatSortNumber() : '' }}"
                    class="grayed-input" placeholder="1234-5678">
            </div>
        </div>

        <div class="required field">
            <label for="holderIdType">Tipo de identificación del tomador</label>

            <div class="ui selection dropdown grayed-input" id="docTypeDropdowntomador">
                <input type="hidden">
                <i class="dropdown icon"></i>
                <div class="default text">Selecciona una opción</div>
                <div class="menu">
                    @foreach ($DOC_TYPES as $k => $v)
                        <div class="item @if ($activity->parent_activity->affiliate->doc_type === $k) active selected @endif"
                            data-value="{{ $k }}">
                            {{ $v }}
                        </div>
                    @endforeach
                </div>
            </div>
        </div>

        <div class="required field">
            <label for="holderIdNumber">Número de identificación del tomador</label>
            <div class="ui input">
                <input id="holderIdNumber" type="text" autocomplete="off"
                    value="{{ $activity->parent_activity ? $activity->parent_activity->affiliate->doc_number : '' }}"
                    placeholder="1-2345-6789" class="grayed-input">
            </div>
        </div>
    </div>

    <div class="four fields">
        <div class="required field">
            <label for="holderName">Nombre del tomador</label>
            <div class="ui input">
                <input id="holderName" type="text" autocomplete="off"
                    value="{{ $activity->parent_activity ? ucwords(strtolower($activity->parent_activity->affiliate->full_name)) : '' }}"
                    class="grayed-input" placeholder="Juan Pérez">
            </div>
        </div>

        <div class="required field">
            <label for="entryDate">Fecha de ingreso a la compañía</label>
            <div class="ui input">
                <input id="entryDate" type="date" autocomplete="off" class="grayed-input" min="1900-01-01"
                    max="{{ date('Y-m-d') }}" value="{{ $activity->gis_sort->date_entry ?? '' }}">
            </div>
        </div>

        <div class="required field">
            <label for="workPlace">Lugar de trabajo</label>
            <div class="ui input">
                <input id="workPlace" type="text" autocomplete="off" class="grayed-input"
                    value="{{ $activity->gis_sort->workplace ?? '' }}" placeholder=" San José, Costa Rica">
            </div>
        </div>

        <div class="required field">
            <label>Forma de pago del salario</label>
            <div class="ui selection dropdown grayed-input" id="salary_payment">
                <input type="hidden">
                <i class="dropdown icon"></i>
                <div class="default text">Selecciona una opción</div>
                <div class="menu">
                    <div class="item" data-value="semanal">
                        Semanal
                    </div>
                    <div class="item" data-value="quincenal">
                        Quincenal
                    </div>
                    <div class="item" data-value="mensual">
                        Mensual
                    </div>
                    <div class="item" data-value="horas">
                        Horas
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="four fields">


        <div class="required field">
            <label for="workDays">Días laborados en la semana</label>
            <div class="ui input">
                <input id="workDays" type="number" min="1" max="7" class="grayed-input"
                    value="{{ $activity->gis_sort->days_worked ?? '' }}" autocomplete="off" placeholder="5">
            </div>
        </div>

        <div class="required field">
            <label for="workHours">Horario laboral desde</label>
            <div class="ui input">
                <input name="work_hours" id="workHours" type="time" autocomplete="off" class="grayed-input"
                    value="{{ $activity->gis_sort->working_hours ?? '' }}">
            </div>
        </div>

        <div class="required field">
            <label for="workHours">Horario laboral hasta</label>
            <div class="ui input">
                <input name="work_hours_hasta" id="work_hours_hasta" type="time" autocomplete="off"
                    class="grayed-input" value="{{ $activity->gis_sort->work_hours_hasta ?? '' }}">
            </div>
        </div>


        <div class="required field">
            <label>Condición</label>
            <div class="ui selection dropdown grayed-input" id="condition_gis">
                <input type="hidden">
                <i class="dropdown icon"></i>
                <div class="default text">Selecciona una opción</div>
                <div class="menu">
                    @foreach ($CONDICION as $k => $v)
                        <div class="item" data-value="{{ $k }}">
                            {{ ucfirst(mb_strtolower($v)) }}
                        </div>
                    @endforeach
                </div>
            </div>
        </div>

    </div>

    <div class="four fields">
        <div class="required field">
            <label for="occupation">Ocupación / cargo</label>
            <div class="ui input">
                <input id="occupation" type="text" autocomplete="off" class="grayed-input"
                    value="{{ $activity->gis_sort->occupation_position ?? '' }}" placeholder="Gerente de ventas">
            </div>
        </div>

        <div class="field">
            <label>Grupo ocupacional</label>
            <div class="ui dropdown button grayed-input"
                style="display: flex !important; flex-direction: row; justify-content: space-between;">
                <span class="text" style="font-weight: normal;">Selecciona una opción</span>
                <i class="dropdown icon"></i>
                <input type="hidden" name="occupancy_group" id="occupancy_group"
                    value="{{ $activity->gis_sort->occupancy_group ?? '' }}">
                <input type="hidden" name="ooccupancy_group_texto" id="occupancy_group_texto">
                <div class="menu">
                    @foreach ($OCCUPATION_GROUPS as $key => $value)
                        <div class="item">
                            <i class="dropdown icon"></i>
                            <span class="text" >{{ $key }}</span>
                            <div class="menu custom-submenu">
                                @foreach ($value as $key_sub => $value_sub)
                                    <div class="item" data-value="{{ $key_sub }}">
                                        {{ $value_sub }}</div>
                                @endforeach

                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>

        <div class="required field" style="display: none;" id="defuncion">
            <label for="Fecha de defunción">Fecha de defunción</label>
            <div class="ui input">

                <input id="defuncion_date" type="date" autocomplete="off"
                    value="{{ $activity->gis_sort->date_death }}">
            </div>
        </div>

    </div>

    <div class="four fields">
        <div class="eight  wide field" style="margin-top: 20px;">
            <table class="ui celled table">
                <thead>
                    <tr>
                        <th>Salarios devengados</th>
                        <th>Días pagados</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach ($planillas as $item)
                        <tr>

                            <td data-label="valor">
                                {{ ($activity->parent_activity->policy_sort && $activity->parent_activity->policy_sort->type_currency === 'USD'
                                    ? '$'
                                    : '₡') . number_format($item->monthly_salary, 2, ',', '.') }}

                            </td>
                            <td data-label="number"> {{ $item->days }}</td>

                        </tr>
                    @endforeach


                </tbody>
            </table>
        </div>

        <div class=" field">
        </div>
        <div class="field">
        </div>

    </div>
</div>






@push('scripts')
    <script>
        $(document).ready(function() {
            const {
                parent_activity: {
                    affiliate: {
                        doc_type,
                    }
                },
                gis_sort: {
                    conditions,
                    method_payment
                }

            } = @json($activity);



            $('#docTypeDropdowntomador').dropdown('set selected', doc_type);

            $('#salary_payment').dropdown('set selected', method_payment);


            $('#defuncion_date').on('input change', function() {
                const fechaDefuncion = $(this).val();

                if (fechaDefuncion) {
                    // Quitar la clase 'error' y el mensaje de error si hay una fecha ingresada
                    $('#defuncion').removeClass('error');
                    $('#defuncion .ui.basic.red.pointing.prompt.label').remove();
                }
            });


            $('#condition_gis input[name="condition"]').on('change', function() {
                const value = $(this).val();


                if (value === 'Fallecido') {
                    $('#defuncion').show();
                } else {
                    $('#defuncion').hide();
                    $('#defuncion input[type="date"]').val('');
                }
            });

            $('#condition_gis').dropdown('set selected', conditions);


        });
    </script>
@endpush
