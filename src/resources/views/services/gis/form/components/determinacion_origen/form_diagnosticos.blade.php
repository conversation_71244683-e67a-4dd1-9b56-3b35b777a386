<div class="title">
    <i class="dropdown icon"></i>
    Diagnósticos <span style="color: red;" class="required">*</span>
</div>
<div class="content form">
    <div class="container" id="formContainer_dignostic">

        @if ($gis->gis_diagnostics->isEmpty())
            <div class="five fields form-block dynamic-field" id="form_diagnostic_dinamic">
                <div class="required field">
                    <label for="casedata_classif">Calificación</label>
                    <select name="diagnostics[0][calification]" id="diagnostics[0][calification]"
                        class="ui dropdown dinamic_grayed_input">
                        <option value="" disabled selected>Seleccione</option>
                        <option value="1">Principal</option>
                        <option value="2">Secundario</option>
                    </select>
                </div>

                <div class="required field">
                    <label for="casedata_code_cie">Código CIE 10</label>
                    <div class="ui search code">
                        <div class="ui icon input">
                            <input class="prompt dinamic_grayed_input" name="diagnostics[0][code]"
                                id="diagnostics[0][code]" type="text" value="" autocomplete="off">
                            <i class="search icon"></i>
                        </div>
                        <div class="results" style="overflow-y: auto; height: 300px;"></div>
                    </div>
                </div>

                <div class="required field disabled-fields" id="name_diagnostico_dinamic">
                    <label for="casedata_diagnosis">Nombre del diagnóstico</label>
                    <input type="text" name="diagnostics[0][diagnosis]" id="diagnostics[0][diagnosis]" readonly
                        class="dinamic_grayed_input">
                </div>

                <div class="required field">
                    <label for="casedata_laterality">Lateralidad</label>
                    <select name="diagnostics[0][laterality]" id="diagnostics[0][laterality]"
                        class="ui dropdown dinamic_grayed_input">
                        <option value="" disabled selected>Seleccione</option>
                        <option value="Izquierda">Izquierda</option>
                        <option value="Derecha">Derecha</option>
                        <option value="Bilateral">Bilateral</option>
                    </select>
                </div>

                <div class="required field">
                    <label for="Origen">Origen</label>
                    <div class="ui selection dropdown dinamic_grayed_input">
                        <input type="hidden" name="diagnostics[0][origin]" id="diagnostics[0][origin]">
                        <i class="dropdown icon"></i>
                        <div class="default text">Origen</div>
                        <div class="menu">
                            @foreach ($ORIGIN_DIAGNOSIS as $k => $r)
                                <div class="item" data-value="{{ $k }}">{{ $r }}</div>
                            @endforeach
                        </div>
                    </div>
                </div>

                <div style="display: flex; align-items: flex-end;">
                    <button type="button" id="addButton_diagnostic"
                        class="ui secondary icon button dinamic_grayed_input">
                        <i class="add icon"></i>
                    </button>
                    <div class="ui button-container" style="display: none" id="deleteButton">
                        <button type="button" class="ui icon red button dinamic_grayed_input">
                            <i class="minus circle icon"></i>
                        </button>
                    </div>
                </div>
            </div>
        @else
       
            @foreach ($gis->gis_diagnostics as $index => $item)
           
                <div class="five fields form-block dynamic-field" id="form_diagnostic_dinamic"
                    data-index="{{ $index }}">

                    <div class="required field">
                        <label for="casedata_classif_{{ $index }}">Calificación</label>
                        <select name="diagnostics[{{ $index }}][calification]"
                            id="casedata_classif_{{ $index }}" class="ui dropdown dinamic_grayed_input">
                            <option value="" disabled {{ empty($item->casedata_classif) ? 'selected' : '' }}>
                                Seleccione</option>
                            <option value="1" {{ $item->casedata_classif == '1' ? 'selected' : '' }}>Principal
                            </option>
                            <option value="2" {{ $item->casedata_classif == '2' ? 'selected' : '' }}>Secundario
                            </option>
                        </select>
                    </div>

                    <div class="required field">
                        <label for="casedata_code_cie">Código CIE 10</label>
                        <div class="ui search code">
                            <div class="ui icon input">
                                <input class="prompt dinamic_grayed_input" name="diagnostics[{{ $index }}][code]"
                                    id="diagnostics[{{ $index }}][code]" type="text"
                                    value="{{ $item->casedata_code_cie ?? '' }}" autocomplete="off">
                                <i class="search icon"></i>
                            </div>
                            <div class="results" style="overflow-y: auto; height: 300px;"></div>
                        </div>
                    </div>

                    <div class="required field disabled-fields" id="name_diagnostico_dinamic">
                        <label for="casedata_diagnosis">Nombre del diagnóstico</label>
                        <input type="text" name="diagnostics[{{ $index }}][diagnosis]"
                            value="{{ $item->casedata_diagnosis ?? '' }}"
                            id="diagnostics[{{ $index }}][diagnosis]" readonly class="dinamic_grayed_input">
                    </div>

                    <div class="required field">
                        <label for="casedata_laterality_{{ $index }}">Lateralidad</label>
                        <select name="diagnostics[{{ $index }}][laterality]"
                            id="casedata_laterality_{{ $index }}" class="ui dropdown dinamic_grayed_input">
                            <option value="" disabled {{ empty($item->casedata_laterality) ? 'selected' : '' }}>
                                Seleccione</option>
                            <option value="Izquierda"
                                {{ $item->casedata_laterality == 'Izquierda' ? 'selected' : '' }}>
                                Izquierda</option>
                            <option value="Derecha" {{ $item->casedata_laterality == 'Derecha' ? 'selected' : '' }}>
                                Derecha
                            </option>
                            <option value="Bilateral"
                                {{ $item->casedata_laterality == 'Bilateral' ? 'selected' : '' }}>
                                Bilateral</option>
                        </select>
                    </div>

                    <div class="required field">
                        <label for="Origen">Origen</label>
                        <div class="ui selection dropdown dinamic_grayed_input">
                            <input type="hidden" name="diagnostics[{{ $index }}][origin]"
                                value="{{ $item->origin ?? '' }}" id="diagnostics[{{ $index }}][origin]">
                            <i class="dropdown icon"></i>
                            <div class="default text">Origen</div>
                            <div class="menu">
                                @foreach ($ORIGIN_DIAGNOSIS as $k => $r)
                                    <div class="item" data-value="{{ $k }}">{{ $r }}
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>

                    {{-- <div style="display: flex; align-items: flex-end;">
                        <button type="button" id="addButton_diagnostic"
                            style="{{ $index > 0 ? 'display: none;' : 'display: inline-block;' }}"
                            class="ui secondary icon button dinamic_grayed_input">
                            <i class="add icon"></i>
                        </button>
                        <div class="ui button-container"
                            style="{{ $index > 0 ? 'display: inline-block;' : 'display: none;' }}" id="deleteButton">
                            <button type="button" class="ui icon red button dinamic_grayed_input">
                                <i class="minus circle icon"></i>
                            </button>
                        </div>
                    </div> --}}
                </div>
            @endforeach

        @endif


    </div>


    @if (isset($activity_diagnostic) && $activity_diagnostic->isNotEmpty())

        @php
         $firstFollowUp = $activity_diagnostic->first()->medical_services_sort ?? null;
         $hasDiagnostics = 0;
        @endphp

        <input type="hidden" name="data_diagnostics" value="1">

        @if (isset($firstFollowUp))

            @if($firstFollowUp->followUps)

                @foreach($firstFollowUp->followUps as $followUp1)

                    @foreach ($followUp1->diagnostics   as  $followUp)

                        @php
                            $hasDiagnostics++;
                        @endphp

                        <div class="fields diagnostic-fields">
                            <input type="hidden" name="diagnostics[id][]" value="{{ $followUp->id ?? '' }}" >
                            <div class="nine wide required field disabled">
                                <label>Código</label>
                                <div class="ui search diagnostic code">
                                    <div class="ui icon input">
                                        <input class="prompt minus_font" name="diagnostics[cod][]" type="text" autocomplete="off"
                                               value="{{ $followUp->code ?? '' }}" >
                                        <i class="search icon "></i>
                                    </div>
                                    <div class="results"></div>
                                </div>
                            </div>
                            <div class="eight wide required field disabled">
                                <label>Nombre</label>
                                <input class="description prompt medical-input-to-lower-case readonly" name="diagnostics[description][]" type="text"
                                       value="{{ $followUp->description ?? '' }}" readonly  >
                            </div>
                            <div class="eight wide required field disabled">
                                <label>Descripción</label>
                                <input class="description prompt medical-input-to-lower-case readonly" name="diagnostics[description_editable][]" type="text"
                                       value="{{ $followUp->description_editable ?? '' }}" readonly  >
                            </div>
                            <div class="three wide field">
                                <label>Lateralidad</label>
                                <div class="ui fluid selection dropdown disabled">
                                    <input name="diagnostics[laterality][]" type="hidden" value="{{ $followUp->laterality ?? '' }}"  >
                                    <i class="dropdown icon"></i>
                                    <div class="default text">Lateralidad</div>
                                    <div class="menu">
                                        @foreach($LATERALITY as $k=>$r)
                                            <div class="item" data-value="{{$k}}">{{$r}}</div>
                                        @endforeach
                                    </div>
                                </div>
                            </div>
                            <div class="five wide field">
                                <label>Origen</label>

                                    <div class="ui fluid selection dropdown disabled">
                                        <input name="diagnostics[origin][]" type="hidden" value="{{ $followUp->origin ?? '' }}">
                                        <i class="dropdown icon"></i>
                                        <div class="default text">Origen</div>
                                        <div class="menu">
                                            @foreach($ORIGIN_DIAGNOSIS as $k=>$r)
                                                <div class="item" data-value="{{$k}}">{{$r}}</div>
                                            @endforeach
                                        </div>
                                    </div>

                            </div>
                            <div class="five wide required field">
                                <label>Estado</label>
                                <div  class="ui fluid selection dropdown">
                                    <input name="diagnostics[diagnostic_status][]" type="hidden" value="{{ $followUp->diagnostic_status ?? '' }}">
                                    <i class="dropdown icon"></i>
                                    <div class="default text">Estado</div>
                                    <div class="menu">
                                        @foreach($DIAGNOSTIC_STATUS as $k=>$r)
                                            <div class="item" data-value="{{$k}}">{{$r}}</div>
                                        @endforeach
                                    </div>
                                </div>
                            </div>
                            <div class="five wide required field">
                                <label>Clasificación</label>
                                <div  class="ui fluid selection dropdown disabled">
                                    <input name="diagnostics[clasificacion][]" type="hidden" value="{{ $followUp->clasificacion ?? '' }}">
                                    <i class="dropdown icon"></i>
                                    <div class="default text">Clasificación</div>
                                    <div class="menu">
                                        @foreach($CLASIFICACION as $k=>$r)
                                            <div class="item" data-value="{{$k}}">{{$r}}</div>
                                        @endforeach
                                    </div>
                                </div>
                            </div>

                        </div>
                    @endforeach
                 @endforeach
            @endif
        @endif
    @endif

    <input type="hidden" id="cant_diagnostics" name="cant_diagnostics" value="{{ $hasDiagnostics ?? 0 }}">

    <div class="four wide field" style="margin-top: 10px">
        <label for="holderIdType">Primer calificador</label>
        <div class="ui input">
            <input name="qualifier" id="calificador" type="text" autocomplete="off" value="MNK (Oceánica)"
                class="dinamic_grayed_input" readonly>
        </div>
    </div>
</div>

<script>
    $(document).ready(function() {
        // Inicializar dropdowns y la funcionalidad de búsqueda

        var $initialDiagnosisInput = $('#name_diagnostico_dinamic input[type="text"]');

        // Inicializar la búsqueda con el campo inicial
        initSearch($('.ui.search.code'), $initialDiagnosisInput);
        let diagnosticIndex = 1;

        $('#formContainer_dignostic').on('click', '#addButton_diagnostic', function() {
            var newFields = $('#form_diagnostic_dinamic').last().clone();

            // Limpiar los valores de los inputs y select de los campos clonados
            newFields.find('input').val('');
            newFields.find('.ui.dropdown').dropdown('clear');

            // Mostrar el botón de eliminar en el nuevo campo y ocultar el de añadir
            newFields.find('#addButton_diagnostic').hide();
            newFields.find('#deleteButton').show();

            // Asignar IDs y nombres únicos a los elementos clonados
            newFields.find('input, select').each(function() {
                const originalId = $(this).attr('id');

                const originalName = $(this).attr('name');
                if (originalName) {
                    // Extraer el campo base del nombre original
                    const fieldNameMatch = originalName.match(/\[(\w+)\]$/);
                    if (fieldNameMatch) {

                        const fieldName = fieldNameMatch[1];

                        const newName =
                            `diagnostics[${diagnosticIndex}][${fieldName}]`;
                        $(this).attr('name', newName);
                        $(this).attr('id', newName);


                        if (rules[originalName]) {
                            rules[newName] = {
                                identifier: newName,
                                rules: rules[originalName].rules
                            };
                        }


                    }
                }



            });

            diagnosticIndex++;

            // Añadir el nuevo conjunto de campos al contenedor
            $('#formContainer_dignostic').append(newFields);

            // Inicializar el dropdown y la búsqueda en los nuevos campos
            newFields.find('.ui.dropdown').dropdown();

            // Obtener el input específico para pasar a `initSearch`
            var $diagnosisInput = newFields.find('#name_diagnostico_dinamic input[type="text"]');
            initSearch(newFields.find('.ui.search.code'), $diagnosisInput);

            $('#gisForm').form('destroy');
        });


        // Función para inicializar el campo de búsqueda para el CIE 10
        function initSearch($searchField, $diagnosisInput) {
            $.getJSON('/js/cie10.json?v=1.0', function(json) {
                $searchField.search({
                    source: json,
                    fields: {
                        title: 'COD',
                        description: 'DESCRIPTION'
                    },
                    searchFields: ['COD', 'DESCRIPTION'],
                    regExp: {
                        escape: /[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,
                        beginsWith: ''
                    },
                    showNoResults: true,
                    maxResults: 250,
                    searchFullText: false,
                    error: {
                        noResults: 'No se encontraron resultados para tu búsqueda.'
                    },
                    onSelect: function(result, response) {
                        // Actualiza el campo de texto del diagnóstico en el input pasado
                        if ($diagnosisInput.length) {
                            $diagnosisInput.val(result.DESCRIPTION.charAt(0).toUpperCase() +
                                result.DESCRIPTION.slice(1).toLowerCase());
                        }
                    }
                });
            });
        }

        // Eliminar fila de formulario
        $('#formContainer_dignostic').on('click', '#deleteButton', function() {



            const fieldsToRemove = $(this).closest('#formContainer_dignostic').find(
                'input, select');

            // Recorre cada campo y elimina su regla en `rules`
            fieldsToRemove.each(function() {
                const fieldName = $(this).attr('name');

                console.log(fieldName);


                // Elimina la regla de validación del objeto `rules` para cada campo
                if (fieldName in rules) {
                    delete rules[fieldName];
                }
            });

            $(this).closest('#form_diagnostic_dinamic').remove();
            $('#gisForm').form('destroy');
        });


    });
</script>

@push('styles')
    <style>
        .codigo_block {
            overflow: auto;
            height: 300px;
        }
    </style>
@endpush
