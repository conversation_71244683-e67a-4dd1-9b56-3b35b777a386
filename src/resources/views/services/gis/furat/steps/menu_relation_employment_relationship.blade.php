@extends('services.gis.furat.index_gis_furat')

@section('title', 'Datos relación laboral')

@section('menu')
    @parent
@endsection

@section('form_gis_furat')

    <form class="ui attached form" action="" id="relation_employment" method="post" autocomplete="off">

        <div class="ui styled fluid accordion">
            <div class="title active">
                <i class="dropdown icon"></i>
                Datos relación laboral <span style="color: red;" class="required">*</span>
            </div>

            <div class="content active">
                <div class="four fields">
                    <div class="required field">
                        <label for="uniqueCode">Código único</label>
                        <div class="ui input">
                            <input readonly name="unique_code" id="uniqueCode" type="text" class="input_disable"
                                autocomplete="off"
                                value="{{ $activity->parent_activity ? $activity->parent_activity->policy_sort->unique_code : '' }}"
                                placeholder="12345678">
                        </div>
                    </div>

                    <div class="required field">
                        <label for="policyNumber">Póliza SORT</label>
                        <div class="ui input">
                            <input name="policy_number" id="policyNumber" type="text" autocomplete="off"
                                class="input_disable" readonly
                                value="{{ $activity->parent_activity ? $activity->parent_activity->policy_sort->formatSortNumber() : '' }}"
                                placeholder="1234-5678">
                        </div>
                    </div>

                    <div class="required field">
                        <label for="holderIdType">Tipo de identificación del tomador</label>

                        <div class="ui selection dropdown input_disable" id="docTypeDropdowntomador">
                            <input type="hidden" name="docTypeDropdowntomador">
                            <i class="dropdown icon"></i>
                            <div class="default text">Selecciona una opción</div>
                            <div class="menu">
                                @foreach ($DOC_TYPES as $k => $v)
                                    <div class="item @if ($activity->parent_activity->affiliate->doc_type === $k) active selected @endif"
                                        data-value="{{ $k }}">
                                        {{ $v }}
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>

                    <div class="required field">
                        <label for="holderIdNumber">Número de identificación del tomador</label>
                        <div class="ui input">
                            <input name="holder_id_number" id="holderIdNumber" type="text" autocomplete="off"
                                class="input_disable"
                                value="{{ $activity->parent_activity ? $activity->parent_activity->affiliate->doc_number : '' }}"
                                placeholder="1-2345-6789">
                        </div>
                    </div>
                </div>

                <div class="four fields">
                    <div class="required field">
                        <label for="holderName">Nombre del tomador</label>
                        <div class="ui input">
                            <input name="holder_name" id="holderName" type="text" autocomplete="off"
                                class="input_disable"
                                value="{{ $activity->parent_activity ? ucwords(strtolower($activity->parent_activity->affiliate->full_name)) : '' }}"
                                placeholder="Juan Pérez">
                        </div>
                    </div>

                    <div class="required field">
                        <label for="entryDate">Fecha de ingreso a la compañía</label>
                        <div class="ui input">
                            <input name="entry_date" id="entryDate" type="date" autocomplete="off" min="1900-01-01"
                                max="{{ date('Y-m-d') }}" value="{{ $activity->gis_sort->date_entry ?? '' }}">
                        </div>
                    </div>

                    <div class="required field">
                        <label for="workPlace">Lugar de trabajo</label>
                        <div class="ui input">
                            <input name="work_place" id="workPlace" type="text" autocomplete="off"
                                value="{{ $activity->gis_sort->workplace ?? '' }}" placeholder=" San José, Costa Rica">
                        </div>
                    </div>

                    <div class="required field">
                        <label>Forma de pago del salario</label>
                        <div class="ui selection dropdown" id="salary_payment">
                            <input type="hidden" name="salary_payment">
                            <i class="dropdown icon"></i>
                            <div class="default text">Selecciona una opción</div>
                            <div class="menu">
                                <div class="item" data-value="semanal">
                                    Semanal
                                </div>
                                <div class="item" data-value="quincenal">
                                    Quincenal
                                </div>
                                <div class="item" data-value="mensual">
                                    Mensual
                                </div>
                                <div class="item" data-value="horas">
                                    Horas
                                </div>
                            </div>
                        </div>
                    </div>

                </div>

                <div class="four fields">

                    <div class="required field">
                        <label for="workDays">Días laborados en la semana</label>
                        <div class="ui input">
                            <input name="work_days" id="workDays" type="number" min="1" max="7"
                                value="{{ $activity->gis_sort->days_worked ?? '' }}" autocomplete="off" placeholder="5">
                        </div>
                    </div>

                    <div class="required field">
                        <label for="workHours">Horario laboral desde</label>
                        <div class="ui input">
                            <input name="work_hours" id="workHours" type="time" autocomplete="off"
                                value="{{ $activity->gis_sort->working_hours ?? '' }}">
                        </div>
                    </div>

                    <div class="required field">
                        <label for="workHours">Horario laboral hasta</label>
                        <div class="ui input">
                            <input name="work_hours_hasta" id="work_hours_hasta" type="time" autocomplete="off"
                                value="{{ $activity->gis_sort->work_hours_hasta ?? '' }}">
                        </div>
                    </div>

                    <div class="required field">
                        <label>Condición</label>
                        <div class="ui selection dropdown" id="condition_gis">
                            <input type="hidden" name="condition">
                            <i class="dropdown icon"></i>
                            <div class="default text">Selecciona una opción</div>
                            <div class="menu">
                                @foreach ($CONDICION as $k => $v)
                                    <div class="item" data-value="{{ $k }}">
                                        {{ ucfirst(mb_strtolower($v)) }}
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>

                </div>

                <div class="four fields">
{{--                    <div class="required field">--}}
{{--                        <label for="occupation">Ocupación / cargo</label>--}}
{{--                        <div class="ui input">--}}

{{--                            <input name="occupation" id="occupation" type="text"--}}
{{--                                value="{{ $activity->gis_sort->occupation_position ?? '' }}">--}}

{{--                        </div>--}}
{{--                    </div>--}}

                    <div class="required field">
                        <label>Grupo ocupacional</label>
                        <div class="ui dropdown button input_button"
                            style="display: flex !important; flex-direction: row; justify-content: space-between;">
                            <span class="text" style="font-weight: normal;">Selecciona una opción</span>
                            <i class="dropdown icon"></i>
                            <input type="hidden" name="occupancy_group" id="occupancy_group"
                                value="{{ $activity->gis_sort->occupancy_group ?? '' }}">
                            <input type="hidden" name="ooccupancy_group_texto" id="occupancy_group_texto">
                            <div class="menu">
                                @foreach ($OCCUPATION_GROUPS as $key => $value)
                                    <div class="item">
                                        <i class="dropdown icon"></i>
                                        <span class="text">{{ $key }}</span>
                                        <div class="menu custom-submenu">
                                            @foreach ($value as $key_sub => $value_sub)
                                                <div class="item" data-value="{{ $key_sub }}">
                                                    {{ $value_sub }}</div>
                                            @endforeach

                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>

                    <div class="required field">
                        <label>Ocupación / Cargo</label>
                        <div class="ui search selection dropdown" id="occupationsDropdown">
                            <input type="hidden" name="occupations_category_id" id="occupations_category_id" value="{{ $quotation->occupations_category_id ?? ''  }}">
                            <i class="dropdown icon"></i>
                            <div class="default text">Ocupación / Cargo</div>
                            <div class="menu ">
                                <div class="item" data-value=""> </div>
                            </div>
                        </div>
                        <input name="occupation" id="occupation" type="hidden" value="{{ $activity->gis_sort->occupation_position ?? '' }}">
                    </div>

                    <div class="required field" style="display: none;" id="defuncion">
                        <label for="Fecha de defunción">Fecha de defunción</label>
                        <div class="ui input">
                            <input name="defuncion_date" id="defuncion_date" type="date" autocomplete="off" value="{{ $activity->gis_sort->date_death }}">
                        </div>
                    </div>

                </div>

                <div class="four fields">
                    <div class="eight  wide field" style="margin-top: 20px;">
                        <table class="ui celled table">
                            <thead>
                                <tr>
                                    <th>Salarios devengados</th>
                                    <th>Días pagados</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($planillas as $item)
                                    <tr>

                                        <td data-label="valor">
                                            {{ ($activity->parent_activity->policy_sort && $activity->parent_activity->policy_sort->type_currency === 'USD'
                                                ? '$'
                                                : '₡') . number_format($item->monthly_salary, 2, ',', '.') }}
                                        </td>
                                        <td data-label="number"> {{ $item->days }}</td>

                                    </tr>
                                @endforeach


                            </tbody>
                        </table>
                    </div>

                    <div class=" field">

                    </div>

                    <div class=" field">

                    </div>
                </div>

            </div>


            <div class="title active">
                <i class="dropdown icon"></i>
                Datos persona autorizada para el reporte <span style="color: red;" class="required">*</span>
            </div>

            <div class="content active">
                <div class="four fields">
                    <div class="field">
                        <label for="tipdoc">Tipo de identificación</label>
                        <div class="ui input">
                            <input name="tipdoc" id="tipdoc" type="text" autocomplete="off"
                                class="input_disable"
                                value="{{ $DOC_TYPES[$policy_contacts && count($policy_contacts) > 0 ? $policy_contacts[0]->type_identification : 'CF'] }}"
                                readonly>
                        </div>
                    </div>

                    <div class="field">
                        <label for="docNumber">Número de identificación</label>
                        <div class="ui input">
                            <input name="doc_number" id="docNumber" type="text" autocomplete="off"
                                class="input_disable"
                                value="{{ $policy_contacts && count($policy_contacts) > 0 ? $policy_contacts[0]->number_identify_responsible : '' }}"
                                placeholder="12345678" readonly>
                        </div>
                    </div>

                    <div class="field">
                        <label for="name">Nombre</label>
                        <div class="ui input">
                            <input name="name" id="name" type="text" autocomplete="off"
                                class="input_disable"
                                value="{{ $policy_contacts && count($policy_contacts) > 0 ? ucwords(strtolower($policy_contacts[0]->name_responsible)) : '' }}"
                                placeholder="Nombre completo" readonly>
                        </div>
                    </div>

                    <div class="field">
                        <label for="ocupacion">Ocupación</label>
                        <div class="ui input">
                            <input name="ocupacion" id="ocupacion" type="text" autocomplete="off"
                                class="input_disable"
                                value="{{ $policy_contacts && count($policy_contacts) > 0 ? $policy_contacts[0]->ocupation_responsible : '' }}"
                                placeholder="ocupación" readonly>
                        </div>
                    </div>
                </div>

                <div class="four fields">
                    <div class="required field">
                        <label for="homePhone">Teléfono domicilió</label>
                        <div class="ui input">
                            <input name="home_phone" id="homePhone" type="text" autocomplete="off"
                                class="input_disable"
                                value="{{ $policy_contacts && count($policy_contacts) > 0 ? $policy_contacts[0]->phone_responsible : '' }}"
                                placeholder="8888-8888" readonly>
                        </div>
                    </div>

                    <div class="field">
                        <label for="mobilePhone">Celular</label>
                        <div class="ui input">
                            <input name="mobile_phone" id="mobilePhone" type="text" autocomplete="off"
                                class="input_disable"
                                value="{{ $policy_contacts && count($policy_contacts) > 0 ? $policy_contacts[0]->cellphone_responsible : '' }}"
                                placeholder="8888-8888" readonly>
                        </div>
                    </div>

                    <div class="field">
                        <label for="email">Correo Electrónico</label>
                        <div class="ui input">
                            <input name="email" id="email" type="email" autocomplete="off"
                                class="input_disable"
                                value="{{ $policy_contacts && count($policy_contacts) > 0 ? $policy_contacts[0]->email_responsible : '' }}"
                                placeholder="<EMAIL>" readonly>
                        </div>
                    </div>
                </div>

            </div>

        </div>

        <div class="ui basic segment">
            <div class="ui error message"></div>
        </div>


        <div style="display: flex; justify-content: space-between; width: 100%;margin-top: 20px;">
            <a class="ui primary button" href="/servicio/gis_sort/furat/affilate/{{ $id }}">Atrás
            </a>

            <button id="btn-submit-data" class="ui button primary">Siguiente
            </button>
        </div>

    </form>



    <style>
        .content_authorized_report {
            margin-top: 20px !important;
        }

        .input_disable {
            pointer-events: none;
            background: #f9fafb !important
        }


        .input_button {
            width: 100%;
            background: white !important;
            border: 1px solid rgba(34, 36, 38, .15) !important;
        }

        .custom-submenu {
            max-width: 300px;
            /* Cambia el valor según tus necesidades */
            overflow: hidden;
            /* Oculta contenido que desborda */
            white-space: nowrap;
            /* Evita que el texto haga saltos de línea */
            text-overflow: ellipsis;
            /* Muestra puntos suspensivos (...) para textos largos */
        }

        /* Estilo para los elementos de texto dentro del submenú */
        .custom-submenu .item {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            padding: 8px;
            /* Ajusta el espaciado interno según tus necesidades */
        }
    </style>


    <script type="text/javascript">
        $(document).ready(function() {
            $('.ui.dropdown').dropdown();
            $('.ui.accordion').accordion();


            $('#relation_employment').form({
                fields: {
                    unique_code: {
                        identifier: 'unique_code',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor ingresa el código único.'
                        }]
                    },
                    policy_number: {
                        identifier: 'policy_number',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor ingresa el número de póliza.'
                        }]
                    },
                    docTypeDropdowntomador: {
                        identifier: 'docTypeDropdowntomador',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor selecciona un tipo de identificación.'
                        }]
                    },
                    holder_id_number: {
                        identifier: 'holder_id_number',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor ingresa el número de identificación.'
                        }]
                    },
                    holder_name: {
                        identifier: 'holder_name',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor ingresa el nombre del tomador.'
                        }]
                    },
                    entry_date: {
                        identifier: 'entry_date',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor ingresa la fecha de ingreso.'
                        }]
                    },
                    work_place: {
                        identifier: 'work_place',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor ingresa el lugar de trabajo.'
                        }]
                    },
                    occupations_category_id: {
                        identifier: 'occupations_category_id',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor ingresa la ocupación.'
                        }]
                    },
                    work_days: {
                        identifier: 'work_days',
                        rules: [{
                                type: 'empty',
                                prompt: 'Por favor ingresa los días laborados.'
                            },
                            {
                                type: 'integer[1..7]',
                                prompt: 'Los días laborados deben estar entre 1 y 7.'
                            }
                        ]
                    },
                    work_hours: {
                        identifier: 'work_hours',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor ingresa el horario laboral desde.'
                        }]
                    },
                    work_hours_hasta: {
                        identifier: 'work_hours_hasta',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor ingresa el horario laboral hasta.'
                        }]
                    },
                    condition: {
                        identifier: 'condition',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor selecciona la condición.'
                        }]
                    },
                    salary_payment: {
                        identifier: 'salary_payment',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor selecciona una Forma de pago.'
                        }]
                    },
                    tipdoc: {
                        identifier: 'tipdoc',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor ingresa el tipo de identificación.'
                        }]
                    },
                    doc_number: {
                        identifier: 'doc_number',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor ingresa el número de identificación.'
                        }]
                    },
                    name: {
                        identifier: 'name',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor ingresa tu nombre.'
                        }]
                    },
                    ocupacion: {
                        identifier: 'ocupacion',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor ingresa tu ocupación.'
                        }]
                    },
                    home_phone: {
                        identifier: 'home_phone',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor ingresa el teléfono domiciliario.'
                        }]
                    },
                    mobile_phone: {
                        identifier: 'mobile_phone',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor ingresa tu número de celular.'
                        }]
                    },
                    email: {
                        identifier: 'email',
                        rules: [{
                                type: 'empty',
                                prompt: 'Por favor ingresa tu correo electrónico.'
                            },
                            {
                                type: 'email',
                                prompt: 'Por favor ingresa un correo electrónico válido.'
                            }
                        ]
                    }
                },
                on: 'blur', // Valida cuando se pierde el foco
                inline: true, // Mensajes en línea
                // Callback para enviar el formulario si es válido
                onSuccess: function(event) {
                    event.preventDefault();

                    const validDefuntion = validFechaDefuntion();

                    if (!validDefuntion) {
                        return
                    }


                    loadingMain(true);
                    $('#btn-submit-data').prop('disabled', true);

                    const formData = $(this).serializeArray().reduce((acc, {
                        name,
                        value
                    }) => {
                        acc[name] = value; // Agrega cada campo al objeto
                        return acc;
                    }, {});

                    formData.activity = `{{ $id }}`

                    sendReport(formData);
                }

            });






        });
    </script>

    <script>
        $(document).ready(function() {


            const {
                parent_activity: {
                    affiliate: {
                        doc_type,
                    }
                },
                gis_sort: {
                    conditions,
                    method_payment
                }

            } = @json($activity);



            $('#docTypeDropdowntomador').dropdown('set selected', doc_type);

            $('#condition_gis').dropdown({
                onChange: function(value, text, $selectedItem) {

                    $('#defuncion').hide();


                    if ('Fallecido' === value) {
                        $('#defuncion').show();
                    } else {
                        $('#defuncion input[type="date"]').val('');
                    }
                }
            });

            $('#condition_gis').dropdown('set selected', conditions);
            $('#salary_payment').dropdown('set selected', method_payment);

            $('#defuncion_date').on('input change', function() {
                const fechaDefuncion = $(this).val();

                if (fechaDefuncion) {
                    // Quitar la clase 'error' y el mensaje de error si hay una fecha ingresada
                    $('#defuncion').removeClass('error');
                    $('#defuncion .ui.basic.red.pointing.prompt.label').remove();
                }
            });
        })
    </script>

    <script>
        function goNext() {
            window.location.href = '/servicio/gis_sort/furat/accident_report/{{ $id }}';
        }

        function sendReport(data) {
            $.ajax({
                url: '/furat/save_employment_relationship_data',
                type: 'POST',
                data,
                success: function(response) {
                    goNext();
                },
                error: function({
                    responseJSON: {
                        message
                    }
                }) {
                    loadingMain(false);
                    $('#btn-submit-data').prop('disabled', false);
                    Swal.fire({
                        icon: 'error',
                        title: 'Error en la solicitud',
                        text: `Ocurrió un problema al procesar la solicitud: ${message}`,
                        confirmButtonText: 'Cerrar',
                        confirmButtonColor: '#91c845'
                    });
                }
            });
        }

        function validFechaDefuntion() {

            const condition_gis = $('#condition_gis').dropdown('get value');

            if (condition_gis === 'Fallecido' && !$('#defuncion_date').val()) {
                $('#defuncion').addClass('error');

                if (!$('#defuncion .ui.basic.red.pointing.prompt.label').length) {
                    $('#defuncion').append(
                        '<div class="ui basic red pointing prompt label transition visible">Por favor ingresa la fecha de defunción.</div>'
                    );
                }

                return false;
            } else {
                $('#defuncion').removeClass('error');

                // Quitar el mensaje de error
                $('#defuncion .ui.basic.red.pointing.prompt.label').remove();
                return true;
            }

        }
    </script>

    <script>
        let occupationsData = [];

        $(document).ready(function () {

            $('.ui.dropdown').dropdown();

            $('.custom-submenu .item').on('click', function () {
                const selectedValue = $(this).data('value');
                const selectedText = $(this).text().trim();

                // Llenamos el input hidden con el valor seleccionado
                $('#occupancy_group').val(selectedValue);
                $('#occupancy_group_texto').val(selectedText);

                const $ocupaciones = $('#occupations_category_id');
                $ocupaciones.dropdown('clear');
                $ocupaciones.empty();
                $ocupaciones.append('<option value="">Selecciona una ocupación</option>');
                $ocupaciones.dropdown('refresh');

                cargarOcupaciones(selectedValue);

            });

            $('#occupations_category_id').dropdown();


            function cargarOcupaciones(grupoId, inicial = false) {

                loadingMain(true);

                //const $ocupaciones = $('#occupations_category_id');

                $.ajax({
                    url: '/get/occupations-gis',
                    method: 'GET',
                    data: {
                        occupation_id: grupoId
                    },
                    success: function(response) {

                        const normalized = response.map(function(item) {
                            item.id = item.id.toString();
                            return item;
                        });

                        initializeSearch(normalized, inicial);

                        loadingMain(false);

                    },
                    error: function() {
                        loadingMain(false);
                        console.error('Error al cargar ocupaciones desde el servidor');
                    }
                });
            }

            const grupoPreseleccionado = @json($activity->gis_sort->occupancy_group ?? null);
           // const ocupacionId = @json($activity->gis_sort->occupations_category_id ?? null);

            if (grupoPreseleccionado) {
                cargarOcupaciones(grupoPreseleccionado, true);
            }

        });



        function initializeSearch(json, inicial = false) {

            if (!json) return;

            json = json.map(function(item) {
                item.id = item.id.toString();
                return item;
            });

            occupationsData = json;

            const menu = $('#occupationsDropdown .menu');
            menu.empty();

            json.forEach(function(item) {
                const codigo = item.id.toString();
                const nombre = (item.name).trim();

                const option = $('<div>', {
                    class: 'item',
                    'data-value': codigo,
                    'data-text': `${nombre}`,
                    text: `${nombre}`
                });

                menu.append(option);
            });


            $('#occupationsDropdown').dropdown('refresh');

            $('#occupationsDropdown').dropdown({
                fullTextSearch: true,
                onChange: function(value, text, $selectedItem) {
                    if (value) {

                        const selectedActivity = occupationsData.find(function(item) {
                            return item.id === value;
                        });

                        if (selectedActivity && selectedActivity.name) {
                            $('#occupation').val(selectedActivity.name);
                        } else {
                            $('#occupation').val('');
                        }
                    } else {
                        $('#occupation').val('');
                    }
                }
            });

            if (inicial) {
                const ocupacionId = @json($activity->gis_sort->occupations_category_id ?? null);
                $('#occupations_category_id').val(ocupacionId);
                $('#occupationsDropdown').dropdown('set selected', ocupacionId);
            }

        }

    </script>

@endsection
