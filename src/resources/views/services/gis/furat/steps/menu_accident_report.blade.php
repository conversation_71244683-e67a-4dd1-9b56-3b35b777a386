@extends('services.gis.furat.index_gis_furat')

@section('title', 'Reporte Accidente')

@section('menu')
    @parent
@endsection

@section('form_gis_furat')

    <form class="ui attached form" id="form_accident" autocomplete="off">
        <input type="hidden" value="{{ $gis->id }}" name="gis_id">
        <div class="ui styled fluid accordion">
            <div class="title active">
                <i class="dropdown icon"></i>
                Reporte del caso <span style="color: red;" class="required">*</span>
            </div>

            <div class="content active">
                <div class="four fields">
                    <div class="required field">
                        <label for="report_type">Tipo de reporte</label>
                        <div class="ui selection dropdown input_disable" id="reportTypeDropdown">
                            <input type="hidden" name="report_type" value="{{ old('report_type', $gis->type_report) }}">
                            <i class="dropdown icon"></i>
                            <div class="default text">Selecciona una opción</div>
                            <div class="menu">
                                <div class="item @if ($gis->type_report == 'Accidente') selected @endif" data-value="Accidente">
                                    Accidente</div>
                                <div class="item @if ($gis->type_report == 'Enfermedad') selected @endif" data-value="Enfermedad">
                                    Enfermedad</div>
                            </div>
                        </div>
                    </div>

                    <div class="required field">
                        <label for="case_number"># del aviso</label>
                        <div class="ui input">
                            <input readonly type="text" name="case_number" class="input_disable"
                                   placeholder="Ingresa el número de caso" value="{{ $gis->consecutive_gis }}">
                        </div>
                    </div>

{{--                    <div class="required field">--}}
{{--                        <label for="case_number"># de caso</label>--}}
{{--                        <div class="ui input">--}}
{{--                            <input readonly type="text" name="case_number" class="input_disable"--}}
{{--                                placeholder="Ingresa el número de caso" value="{{ $gis->formatCaseNumberIfReported() }}">--}}
{{--                        </div>--}}
{{--                    </div>--}}

                </div>

                <!-- Accordion Enfermedad -->
                <div id="dinamic_modulo_select">
                </div>

            </div>

        </div>

        <div class="ui basic segment">
            <div class="ui error message"></div>
        </div>


        <div style="display: flex; justify-content: space-between; width: 100%; margin-top: 20px;">
            <a class="ui primary button" href="/servicio/gis_sort/furat/employment_relationship/{{ $id }}">Atrás
            </a>

            <button id="btn-submit-data" class="ui button primary">Siguiente
            </button>
        </div>
    </form>

    <style>
        .content_authorized_report {
            margin-top: 20px !important;
        }

        .content-field-cuerpo-dinamic {
            display: flex;
            gap: 10px;
            flex-direction: column;

            .field_dinamic_afectada {
                width: 100%;
            }

            .button_content_dinamic {
                align-content: end;
            }
        }

        .content_dinamic_fields {
            overflow: auto;
            height: 25vw;
            max-height: 700px;
            margin-top: 15px !important;
        }

        .hidden {
            display: none;
        }

        .input_disable {
            pointer-events: none;
            background: #f9fafb !important
        }

        /* Alinea el texto a la izquierda y el ícono a la derecha */
        .ui.dropdown .menu .item {
            display: flex;
            justify-content: space-between;
            /* Asegura que el texto y el ícono estén a los extremos */
            align-items: center;
            /* Centra verticalmente el texto y el ícono */
        }

        /* Si necesitas espacio entre el texto y el ícono */
        .ui.dropdown .menu .item .info.circle.icon {
            margin-left: 10px;
            /* Ajusta este valor según el espacio que quieras entre el texto y el ícono */
        }
    </style>


    <script type="text/javascript">
        $(document).ready(function() {
            $('.ui.dropdown').dropdown();
            $('.ui.accordion').accordion();
            $('input[type="radio"]').popup();
            // Inicializa el tooltip (popup) para los íconos con la clase 'pop' y configura la posición
            $('.pop').popup({
                position: 'right center' // Posición del tooltip
            });

        });
    </script>

    <script>
        var gis = @json($gis);

        $(document).ready(function() {

            if (gis) {
                handleDropdownChange(gis.type_report)
            }


            $('#reportTypeDropdown').dropdown({
                onChange: function(value, text, $selectedItem) {
                    $('#dinamic_modulo_select').empty();
                    handleDropdownChange(value)

                }
            });

            function handleDropdownChange(value) {
                if (value === 'Accidente') {
                    $('#dinamic_modulo_select').html(viewAccidente());
                } else if (value === 'Enfermedad') {
                    $('#dinamic_modulo_select').html(viewEnfermedad());
                }

                // Reactivar el dropdown dentro del contenido dinámico
                $('.ui.dropdown_dinamic').dropdown();

                enableFuntions();
            }


            function add_file_reporte(parteCuerpo) {
                // Clonamos el último conjunto de campos ocultos
                var newFields = $('.content-field-cuerpo-dinamic').first().clone();

                // Mostrar el nuevo conjunto de campos clonado (remover el display: none)
                newFields.css('display', 'block');

                // Asignar el valor seleccionado al input correspondiente
                newFields.find('input[name="parte_cuerpo"]').val(parteCuerpo);

                // Asignar nuevos IDs únicos para los elementos clonados
                newFields.find('input, select').each(function() {
                    var newId = $(this).attr('id') + '_' + Math.random().toString(36).substring(
                        7); // Generar un nuevo ID único
                    $(this).attr('id', newId); // Asignar el nuevo ID
                });

                // Agregar el nuevo conjunto de campos al contenedor
                $('#field_container_reporte').append(newFields);
            }


            function viewEnfermedad() {
                return `@include(
                    'services.gis.form.components.reporte_accidente.components.form_reporte_enfermedad',
                    ['gis']
                )`
            }

            function viewAccidente() {
                return `
                <div class="accordion transition">
    <div class="title">
        <i class="dropdown icon"></i>Accidente <span style="color: red;" class="required">*</span>
    </div>
    <div class="content">
        <!-- Fecha, Hora y Descripción del accidente -->
        <div class="three fields">
            <div class="required field">
                <label for="accident_date">Fecha del accidente</label>
                <div class="ui input">
                    <input value="{{ $gis->date_accident ?? '' }}" name="accident_date" id="accident_date" type="date"  max="{{ date('Y-m-d') }}"
                        autocomplete="off" />
                </div>
            </div>

            <div class="required field">
                <label for="accident_time">Hora del accidente</label>
                <div class="ui input">
                    <input value="{{ $gis->hour_accident ?? '' }}" name="accident_time" id="accident_time" type="time"
                        autocomplete="off" />
                </div>
            </div>

            <div class="required field">
                <label for="accident_description">Descripción del accidente</label>
                <div class="ui input">
                    <textarea rows="2" name="accident_description"
                        id="accident_description" type="text" autocomplete="off" placeholder="Describe el accidente" > {{ $gis->accident_description ?? '' }}</textarea>
                </div>
            </div>
        </div>


{{-- Formulario muñeco --}}
        @include('services.gis.form.components.reporte_accidente.components.form_dinamico_muneco')

        <!-- Campos de selección relacionados con el accidente -->
     
        <div class="four fields">
            <div class="required field">
                <label for="ocurrencia">Forma de accidente (incidente)</label>
                <div class="ui selection dropdown dropdown_dinamic" id="dropdownOcurrencia">
                    <input type="hidden" name="ocurrencia" value="{{ $gis->ocurrencia ?? '' }}" />
                    <i class="dropdown icon"></i>
                    <div class="default text">Selecciona una opción</div>
                    <div class="menu">
                        @foreach ($accidentType as $row)
                        <div class="item" data-value="{{ $row->id }}">{{ $row->name }}</div>
                        @endforeach
                    </div>
                </div>
            </div>

            <div class="required field">
                <label for="material_agente">Agente material involucrado</label>
                <div class="ui selection dropdown dropdown_dinamic" id="material_agente">
                    <input type="hidden" name="material_agente" value="{{ $gis->material_agente ?? '' }}" />
                    <i class="dropdown icon"></i>
                    <div class="default text">Selecciona una opción</div>
                    <div class="menu">
                        @foreach ($agetsGi as $row)
                            <div class="item" data-value="{{ $row->id }}" data-info="{{ $row->name ?? 'Información no disponible' }}">
                                {{ $row->name }}
                                <!-- Ícono de información con el contenido de data-info -->
                                <i class="info circle icon pop" 
                                data-title="{{ $row->id }}"
                                data-content="{{ $row->name ?? 'Información no disponible' }}"></i>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>

            <div id="agent-info" style="margin-top: 10px;"></div>

            <div class="required field">
                <label for="mecanismo_trauma">Mecanismo de trauma</label>
                <div class="ui selection dropdown dropdown_dinamic" id="dropdownMechanismTrauma">
                    <input type="hidden" name="mechanism_trauma" id="mechanism_trauma"
                        value="{{ $gis->mechanism_trauma ?? '' }}" />
                    <i class="dropdown icon"></i>
                    <div class="default text">Selecciona una opción</div>
                    <div class="menu">
                        @foreach ($INJURIES as $k => $r)
                        <div class="item" data-value="{{ $k }}">{{ $r }}</div>
                        @endforeach
                    </div>
                </div>
            </div>

            <div class="required field">
                <label for="ayuda_tercero">Requiere ayuda de tercero</label>
                <div class="ui selection dropdown dropdown_dinamic" id="dropdownThirdParty">
                    <input type="hidden" name="third_party" id="third_party"
                        value="{{ $gis->third_party ?? '' }}" />
                    <i class="dropdown icon"></i>
                    <div class="default text">Selecciona una opción</div>
                    <div class="menu">
                        <div class="item" data-value="1">Si</div>
                        <div class="item" data-value="0">No</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Modalidad de trabajo, fallecimiento y fecha de desaparición -->
        <div class="four fields">
            <div class="required field">
                <label for="modalidad_trabajo">Modalidad de trabajo</label>
                <div class="ui selection dropdown dropdown_dinamic" id="dropdownWorkModality">
                    <input type="hidden" name="work_modality" id="work_modality"
                        value="{{ $gis->work_modality ?? '' }}" />
                    <i class="dropdown icon"></i>
                    <div class="default text">Selecciona una opción</div>
                    <div class="menu">
                        @foreach ($WORK_MODES as $k => $r)
                        <div class="item" data-value="{{ $k }}">{{ $r }}</div>
                        @endforeach
                    </div>
                </div>
            </div>

            <!-- Condiciones de fallecimiento/desaparición -->

            @if ($gis->conditions === 'Desaparecido' || $gis->conditions === 'Fallecido')
            <div class="required field" id="death_place_field">
                <label for="fallecimiento_lugar">Lugar de fallecimiento / desaparición</label>
                <div class="ui selection dropdown dropdown_dinamic" id="death_place_drow">
                    <input type="hidden" name="death_place" value="{{ $gis->death_place ?? '' }}"/>
                    <i class="dropdown icon"></i>
                    <div class="default text">Selecciona una opción</div>
                    <div class="menu">
                        <div class="item" data-value="Lugar de trabajo">Lugar de trabajo</div>
                        <div class="item" data-value="Otro">Otro</div>
                    </div>
                </div>
            </div>
            @endif




            @if ($gis->conditions === 'Desaparecido')
            <div class="required field" id="date_disappearance_field">
                <label for="desaparicion_fecha">Fecha de desaparición</label>
                <div class="ui input">
                    <input name="date_disappearance" value = "{{ $gis->date_disappearance ?? '' }}" id="date_disappearance" type="date" autocomplete="off" />
                </div>
            </div>
            @endif

            <div class="required field hidden" id="material_agente_otro_field">
                <label for="Otro">Otro agente</label>
                <div class="ui input">
                    <input value="{{ $gis->material_agente_otro ?? '' }}" name="material_agente_otro"
                        id="material_agente_otro" type="text" autocomplete="off" placeholder="Describe el agente" />
                </div>
            </div>

               <div class="required field hidden"  id="forma_ocurrencia_otro">
                <label for="Otro">Otro forma</label>
                <div class="ui input">
                    <input name="forma_ocurrencia_otro" id="forma_ocurrencia_input" type="text" autocomplete="off" placeholder="Describe" value="{{ $gis->forma_ocurrencia_otro ?? '' }}" />
                </div>
            </div>

             <div class="required field hidden"  id="mecanismo_trauma_otro">
                <label for="Otro">Otro mecanismo</label>
                <div class="ui input">
                    <input name="mecanismo_trauma_otro"  id="mecanismo_trauma_input" type="text" autocomplete="off" placeholder="Describe" value="{{ $gis->mecanismo_trauma_otro ?? '' }}" />
                </div>
            </div>
        </div>

         <div class="three fields">
            <div class="required field">
                <label for="lugar_accidente">Lugar del accidente</label>
                <div class="ui selection dropdown dropdown_dinamic" id="lugar_accidente">
                    <input type="hidden" name="accident_place" id="accident_place"
                        value="{{ $gis->accident_place ?? '' }}" />
                    <i class="dropdown icon"></i>
                    <div class="default text">Selecciona una opción</div>
                    <div class="menu">
                        @foreach ($WORKPLACE_LOCATIONS as $k => $r)
                        <div class="item" data-value="{{ $k }}">{{ $r }}</div>
                        @endforeach
                    </div>
                </div>
            </div>

            <div class="required field">
                <label for="causa_externa">Causa externa</label>
                <div class="ui selection dropdown dropdown_dinamic" id="causa_externa">
                    <input type="hidden" name="external_cause" id="causa_externa" value="{{ $gis->external_cause ?? '' }}" />
                    <i class="dropdown icon"></i>
                    <div class="default text">Selecciona una opción</div>
                    <div class="menu">
                        <div class="item" data-value="1">Si</div>
                        <div class="item" data-value="0">No</div>
                    </div>
                </div>
            </div>

            <div class="required field hidden" id="external_cause_description">
                <label for="external_cause_descriptio">¿Cuál?</label>
                <div class="ui  input">
                    <input value="{{ $gis->external_cause_description ?? '' }}" name="external_cause_description" id="external_cause_des"
                        type="text" autocomplete="off" placeholder="Especificar causa" />
                </div>
            </div>
        </div>
          <!-- Modalidad de trabajo, fallecimiento y primeros auxilios -->
        <div class="three fields">
            <div class="required field">
                <label for="first_aid">¿Recibió primeros auxilios o atención en un centro de salud diferente a MNK?</label>
                <div class="ui selection dropdown dropdown_dinamic" id="ayuda_tercero_accidente">
                    <input type="hidden" name="first_aid" id="first_aid" value="{{ $gis->first_aid ?? '' }}" />
                    <i class="dropdown icon"></i>
                    <div class="default text">Selecciona una opción</div>
                    <div class="menu">
                        <div class="item" data-value="1">Si</div>
                        <div class="item" data-value="0">No</div>
                    </div>
                </div>
            </div>

            <div class="required field hidden" id="auxilios_descripcion_field">
                <label for="auxilios_descripcion">¿Cuál?</label>
                <div class="ui input">
                    <input value="{{ $gis->auxilios_descripcion ?? '' }}" name="auxilios_descripcion"
                        id="auxilios_descripcion" type="text" autocomplete="off" placeholder="Especificar" />
                </div>
            </div>

            <div class="required field hidden" id="lugar_accidente_otro_field">
                <label for="another_place">Otro lugar</label>
                <div class="ui input">
                    <input value="{{ $gis->another_place ?? '' }}" name="another_place" id="another_place" type="text"
                        autocomplete="off" />
                </div>
            </div>
        </div>
    </div>
</div>
<div class="accordion transition">
    <div class="title"><i class="dropdown icon"></i>Datos de un testigo</div>
    <div class="content">   
        <div class="four fields">
                    <div class="field">
                        <label>Tipo de identificación</label>
                        <div class="ui selection dropdown" id="docTypeDropdown">
                            <input type="hidden" name="doc_type_witness" value="{{ $gis->doc_type_witness ?? '' }}">
                            <i class="dropdown icon"></i>
                            <div class="default text">Selecciona una opción</div>
                            <div class="menu">
                                @foreach ($DOC_TYPES as $k => $v)
                                    <div class="item" data-value="{{ $k }}">
                                        {{ ucfirst(mb_strtolower($v)) }}
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>

                    <div class="field">
                        <label for="docNumber">Número de identificación</label>
                        <div class="ui  input">
                            <input name="doc_number_witness" id="docNumber" type="text" autocomplete="off" 
                            value="{{ $gis->doc_number_witness ?? '' }}"
                                placeholder="12345678">
                        </div>
                    </div>

                    <div class="field">
                        <label for="name">Nombre</label>
                        <div class="ui input">
                            <input name="name_witness" id="name" type="text" autocomplete="off" 
                            value="{{ $gis->name_witness ?? '' }}"
                                placeholder="Nombre completo">
                        </div>
                    </div>


                     <div class=" field">
                        <label for="Telefono">Telefono</label>
                        <div class="ui input">
                            <input name="telefonos_witness" id="Telefono" type="text" autocomplete="off"
                            value="{{ $gis->telefonos_witness ?? '' }}"
                                placeholder="Telefono">
                        </div>
                    </div>
                </div>
    </div>
</div>
                
                
                `
            }


            function enableFuntions() {

                $('.ui.dropdown').dropdown({});

                $('#dinamic_modulo_select .cuerpo_muneco').on('change', function() {

                    let parteCuerpo = $(this).val();
                    let radioId = $(this).attr('id');

                    add_file_reporte(parteCuerpo);
                });

                $('#material_agente').dropdown({
                    onChange: function(value, text, $selectedItem) {

                        $('#material_agente_otro_field').hide();

                        if (value === '9') {
                            $('#material_agente_otro_field').show();
                        }
                    }
                });

                $('#lugar_accidente').dropdown({
                    onChange: function(value, text, $selectedItem) {

                        $('#lugar_accidente_otro_field').hide();

                        if (value === '8') {
                            $('#lugar_accidente_otro_field').show();
                        }
                    }
                });

                $('#causa_externa').dropdown({
                    onChange: function(value, text, $selectedItem) {

                        $('#external_cause_description').hide();

                        if (value === '1') {
                            $('#external_cause_description').show();
                        }
                    }
                });

                // Inicializa el dropdown
                $('#dropdownOcurrencia').dropdown({
                    // Selecciona el valor desde el input oculto
                    onChange: function(value) {


                        $('#forma_ocurrencia_otro').hide();

                        if (value === '9') {
                            $('#forma_ocurrencia_otro').show();
                        }

                    }
                });

                $('#dropdownMechanismTrauma').dropdown({
                    // Actualiza el valor del input oculto cuando se selecciona algo
                    onChange: function(value) {


                        $('#mecanismo_trauma_otro').hide();

                        if (value === '54') {
                            $('#mecanismo_trauma_otro').show();
                        }
                    }
                });


                $('#ayuda_tercero_accidente').dropdown({
                    onChange: function(value, text, $selectedItem) {

                        $('#auxilios_descripcion_field').hide();

                        if (value === '1') {
                            $('#auxilios_descripcion_field').show();
                        }
                    }
                });




                $('#dinamic_modulo_select #field_container_reporte').on('click', '.remove-field-btn_reporte',
                    function() {
                        // Obtener la parte del cuerpo que corresponde al campo que se va a eliminar
                        let parteCuerpo = $(this).closest('.content-field-cuerpo-dinamic').find(
                            'input[name="parte_cuerpo"]').val();

                        // Remover los campos
                        $(this).closest('.content-field-cuerpo-dinamic').remove();

                        // Deseleccionar el radio button asociado con la parte del cuerpo eliminada
                        $('.cuerpo_muneco[value="' + parteCuerpo + '"]').prop('checked', false);
                    });
            }

        });

        function goNext() {
            // window.location.href = '/servicio/gis_sort/furat/signature_data/{{ $id }}';
            window.location.href = '/servicio/gis_sort/furat/support/{{ $id }}';
        }
    </script>

    <script>
        //INICIAR DATA:
        $(document).ready(function() {
            const mechanism_trauma = '{{ $gis->mechanism_trauma }}';
            if (mechanism_trauma) {
                $('#dropdownMechanismTrauma').dropdown('set selected', mechanism_trauma);

                // Muestra u oculta el campo adicional si el valor es '5'
                if (mechanism_trauma === '54') {
                    $('#mecanismo_trauma_otro').show();
                } else {
                    $('#mecanismo_trauma_otro').hide();
                }
            }


            const ocurrencia = '{{ $gis->ocurrencia }}';
            if (ocurrencia) {

                if (ocurrencia === '9') {
                    $('#forma_ocurrencia_otro').show();
                } else {
                    $('#forma_ocurrencia_otro').hide();
                }
            }

            const material_agente = '{{ $gis->material_agente }}';

            if (material_agente) {

                if (material_agente === '9') {
                    $('#material_agente_otro_field').show();
                } else {
                    $('#material_agente_otro_field').hide();
                }
            }


            const accident_place = '{{ $gis->accident_place }}';

            if (accident_place) {

                if (accident_place === '8') {
                    $('#lugar_accidente_otro_field').show();
                } else {
                    $('#lugar_accidente_otro_field').hide();
                }
            }

            const first_aid = '{{ $gis->first_aid }}';

            if (first_aid) {

                if (first_aid === '1') {
                    $('#auxilios_descripcion_field').show();
                } else {
                    $('#auxilios_descripcion_field').hide();
                }
            }

            const external_cause = '{{ $gis->external_cause }}';

            if (external_cause) {

                if (external_cause === '1') {
                    $('#external_cause_description').show();
                } else {
                    $('#external_cause_description').hide();
                }
            }


            $('#forma_ocurrencia_input').on('input change', function() {
                const ocurrencia = $(this).val();

                if (ocurrencia) {
                    // Quitar la clase 'error' y el mensaje de error si hay una fecha ingresada
                    $('#forma_ocurrencia_otro').removeClass('error');
                    $('#forma_ocurrencia_otro .ui.basic.red.pointing.prompt.label').remove();
                }
            });

            $('#material_agente_otro').on('input change', function() {
                const ocurrencia = $(this).val();

                if (ocurrencia) {
                    // Quitar la clase 'error' y el mensaje de error si hay una fecha ingresada
                    $('#material_agente_otro_field').removeClass('error');
                    $('#material_agente_otro_field .ui.basic.red.pointing.prompt.label').remove();
                }
            });

            $('#mecanismo_trauma_input').on('input change', function() {
                const ocurrencia = $(this).val();

                if (ocurrencia) {
                    // Quitar la clase 'error' y el mensaje de error si hay una fecha ingresada
                    $('#mecanismo_trauma_otro').removeClass('error');
                    $('#mecanismo_trauma_otro .ui.basic.red.pointing.prompt.label').remove();
                }
            });

            $('#date_disappearance').on('input change', function() {
                const ocurrencia = $(this).val();

                if (ocurrencia) {
                    // Quitar la clase 'error' y el mensaje de error si hay una fecha ingresada
                    $('#date_disappearance_field').removeClass('error');
                    $('#date_disappearance_field .ui.basic.red.pointing.prompt.label').remove();
                }
            });

            $('#death_place_drow').on('change', function() {

                
                // Quitar la clase 'error' y el mensaje de error
                $('#death_place_field').removeClass('error');
                $('#death_place_field .ui.basic.red.pointing.prompt.label').remove();

            });

            $('#another_place').on('input change', function() {
                const ocurrencia = $(this).val();

                if (ocurrencia) {
                    // Quitar la clase 'error' y el mensaje de error si hay una fecha ingresada
                    $('#lugar_accidente_otro_field').removeClass('error');
                    $('#lugar_accidente_otro_field .ui.basic.red.pointing.prompt.label').remove();
                }
            });

            $('#external_cause_des').on('input change', function() {
                const ocurrencia = $(this).val();

                if (ocurrencia) {
                    // Quitar la clase 'error' y el mensaje de error si hay una fecha ingresada
                    $('#external_cause_description').removeClass('error');
                    $('#external_cause_description .ui.basic.red.pointing.prompt.label').remove();
                }
            });

            $('#auxilios_descripcion').on('input change', function() {
                const ocurrencia = $(this).val();

                if (ocurrencia) {
                    // Quitar la clase 'error' y el mensaje de error si hay una fecha ingresada
                    $('#auxilios_descripcion_field').removeClass('error');
                    $('#auxilios_descripcion_field .ui.basic.red.pointing.prompt.label').remove();
                }
            });

        })
    </script>

    <script>
        function validOcurrencia() {


            const dropdownOcurrencia = $('#dropdownOcurrencia').dropdown('get value');


            if (dropdownOcurrencia === '9' && !$('#forma_ocurrencia_input').val()) {
                $('#forma_ocurrencia_otro').addClass('error');

                if (!$('#forma_ocurrencia_otro .ui.basic.red.pointing.prompt.label').length) {
                    $('#forma_ocurrencia_otro').append(
                        '<div class="ui basic red pointing prompt label transition visible">Por favor ingresa otro forma.</div>'
                    );
                }

                return false;
            } else {
                $('#forma_ocurrencia_otro').removeClass('error');

                // Quitar el mensaje de error
                $('#forma_ocurrencia_otro .ui.basic.red.pointing.prompt.label').remove();
                return true;
            }

        }

        function validAgente() {

            const dropdownAgente = $('#material_agente').dropdown('get value');


            if (dropdownAgente === '9' && !$('#material_agente_otro').val()) {
                $('#material_agente_otro_field').addClass('error');

                if (!$('#material_agente_otro_field .ui.basic.red.pointing.prompt.label').length) {
                    $('#material_agente_otro_field').append(
                        '<div class="ui basic red pointing prompt label transition visible">Por favor ingresa otro agente.</div>'
                    );
                }

                return false;
            } else {
                $('#material_agente_otro_field').removeClass('error');

                // Quitar el mensaje de error
                $('#material_agente_otro_field .ui.basic.red.pointing.prompt.label').remove();
                return true;
            }

        }

        function validMecanismo() {

            const dropdownMecanismo = $('#dropdownMechanismTrauma').dropdown('get value');


            if (dropdownMecanismo === '54' && !$('#mecanismo_trauma_input').val()) {
                $('#mecanismo_trauma_otro').addClass('error');

                if (!$('#mecanismo_trauma_otro .ui.basic.red.pointing.prompt.label').length) {
                    $('#mecanismo_trauma_otro').append(
                        '<div class="ui basic red pointing prompt label transition visible">Por favor ingresa otro mecanismo.</div>'
                    );
                }

                return false;
            } else {
                $('#mecanismo_trauma_otro').removeClass('error');

                // Quitar el mensaje de error
                $('#mecanismo_trauma_otro .ui.basic.red.pointing.prompt.label').remove();
                return true;
            }

        }

        function validDead() {

            const dropdownDeathPlace = $('#death_place_drow').dropdown('get value');


            if (!dropdownDeathPlace && (`{{ $gis->conditions }}` === 'Desaparecido' || `{{ $gis->conditions }}` ===
                    'Fallecido')) {
                $('#death_place_field').addClass('error');

                if (!$('#death_place_field .ui.basic.red.pointing.prompt.label').length) {
                    $('#death_place_field').append(
                        '<div class="ui basic red pointing prompt label transition visible">Por favor ingresa lugar de fallecimiento / desaparición.</div>'
                    );
                }

                return false;
            } else {
                $('#death_place_field').removeClass('error');

                // Quitar el mensaje de error
                $('#death_place_field .ui.basic.red.pointing.prompt.label').remove();
                return true;
            }

        }

        function validDateDisappearance() {

            const dateDisappearance = $('#date_disappearance').val();


            if (!dateDisappearance && `{{ $gis->conditions }}` === 'Desaparecido') {
                $('#date_disappearance_field').addClass('error');

                if (!$('#date_disappearance_field .ui.basic.red.pointing.prompt.label').length) {
                    $('#date_disappearance_field').append(
                        '<div class="ui basic red pointing prompt label transition visible">Por favor ingresa fecha de desaparición.</div>'
                    );
                }

                return false;
            } else {
                $('#date_disappearance_field').removeClass('error');

                // Quitar el mensaje de error
                $('#date_disappearance_field .ui.basic.red.pointing.prompt.label').remove();
                return true;
            }

        }

        function validaccident_place() {

            const accident_place = $('#accident_place').val();

            if (accident_place === '8' && !$('#another_place').val()) {
                $('#lugar_accidente_otro_field').addClass('error');

                if (!$('#lugar_accidente_otro_field .ui.basic.red.pointing.prompt.label').length) {
                    $('#lugar_accidente_otro_field').append(
                        '<div class="ui basic red pointing prompt label transition visible">Por favor ingresa otro lugar.</div>'
                    );
                }

                return false;
            } else {
                $('#lugar_accidente_otro_field').removeClass('error');

                // Quitar el mensaje de error
                $('#lugar_accidente_otro_field .ui.basic.red.pointing.prompt.label').remove();
                return true;
            }

        }

        function validexternal_cause() {


            const external_cause = $('#causa_externa').dropdown('get value');

            if (external_cause === '1' && !$('#external_cause_des').val()) {
                $('#external_cause_description').addClass('error');

                if (!$('#external_cause_description .ui.basic.red.pointing.prompt.label').length) {
                    $('#external_cause_description').append(
                        '<div class="ui basic red pointing prompt label transition visible">Por favor ingresa cuál.</div>'
                    );
                }

                return false;
            } else {
                $('#external_cause_description').removeClass('error');

                // Quitar el mensaje de error
                $('#external_cause_description .ui.basic.red.pointing.prompt.label').remove();
                return true;
            }

        }

        function validauxilios() {

            const first_aid = $('#first_aid').val();


            if (first_aid === '1' && !$('#auxilios_descripcion').val()) {
                $('#auxilios_descripcion_field').addClass('error');

                if (!$('#auxilios_descripcion_field .ui.basic.red.pointing.prompt.label').length) {
                    $('#auxilios_descripcion_field').append(
                        '<div class="ui basic red pointing prompt label transition visible">Por favor ingresa cuál.</div>'
                    );
                }

                return false;
            } else {
                $('#auxilios_descripcion_field').removeClass('error');

                // Quitar el mensaje de error
                $('#auxilios_descripcion_field .ui.basic.red.pointing.prompt.label').remove();
                return true;
            }

        }
    </script>

    <script>
        $(document).ready(function() {
            var fieldData = @json($fieldData); // Convierte la colección a JSON

            // Cargar valores desde fieldData en los inputs
            fieldData.forEach(function(item) {
                // Crea una nueva entrada en el HTML
                var newField = `
                <div class="content-field-cuerpo-dinamic" style="display: block;">
                    <div class="field_dinamic_afectada fields_buttons_actions">
                        <div class="required field fields_actions">
                            <label for="parte_cuerpo">Parte del cuerpo afectada</label>
                            <div class="ui input">
                                <input name="parte_cuerpo" id="${item.body_part_id}" type="text" autocomplete="off" value="${item.body_part_name}" readonly>
                            </div>
                        </div>
                        <div class="button_content_dinamic">
                            <button type="button" class="ui icon button remove-field-btn_reporte red">
                                <i class="minus circle icon"></i>
                            </button>
                        </div>
                    </div>
                </div>
            `;
                // Agrega el nuevo campo al contenedor
                $('#field_container_reporte').append(newField);

                if (item.body_part_name) {
                    $('input[type=radio][value="' + item.body_part_name + '"]').prop('checked', true);
                }
            });

        });
    </script>

    <script>
        //reglas campos por cada face
        const validations = {
            Accidente: function() {
                return {
                    report_type: {
                        identifier: 'report_type',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor selecciona el tipo de reporte.'
                        }]
                    },
                    case_number: {
                        identifier: 'case_number',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor ingresa el número de caso.'
                        }]
                    },
                    ocurrencia: {
                        identifier: 'ocurrencia',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor selecciona la forma de ocurrencia.'
                        }]
                    },
                    material_agente: {
                        identifier: 'material_agente',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor selecciona el agente material.'
                        }]
                    },
                    mechanism_trauma: {
                        identifier: 'mechanism_trauma',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor selecciona el mecanismo de trauma.'
                        }]
                    },
                    work_modality: {
                        identifier: 'work_modality',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor selecciona modalidad de trabajo.'
                        }]
                    },
                    third_party: {
                        identifier: 'third_party',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor selecciona si requiere ayuda de tercero.'
                        }]
                    },
                    accident_date: {
                        identifier: 'accident_date',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor ingresa la fecha del accidente.'
                        }]
                    },
                    accident_time: {
                        identifier: 'accident_time',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor ingresa la hora del accidente.'
                        }]
                    },
                    accident_place: {
                        identifier: 'accident_place',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor ingresa lugar del accidente.'
                        }]
                    },
                    external_cause: {
                        identifier: 'external_cause',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor ingresa causa externa.'
                        }]
                    },
                    first_aid: {
                        identifier: 'first_aid',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor ingresa recibió primeros auxilios o atención.'
                        }]
                    },
                    accident_description: {
                        identifier: 'accident_description',
                        rules: [{
                                type: 'empty',
                                prompt: 'Por favor ingresa la descripción del accidente.'
                            },
                            {
                                type: 'minLength[10]',
                                prompt: 'La descripción debe tener al menos 10 caracteres.'
                            }
                        ]
                    }
                };
            },
            Enfermedad: function() {
                return {
                    symptoms_start_date: {
                        identifier: 'symptoms_start_date',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor ingresa la fecha que inició con los síntomas.'
                        }]
                    },
                    agent_involved: {
                        identifier: 'agent_involved',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor selecciona Agente Involucrado.'
                        }]
                    },
                    ayuda: {
                        identifier: 'ayuda',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor selecciona Requiere ayuda de tercero.'
                        }]
                    },
                    symptoms_description: {
                        identifier: 'symptoms_description',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor ingresa Descripción de síntomas.'
                        }]
                    },
                    disease_report_type_id: {
                        identifier: 'disease_report_type_id',
                        rules: [{
                            type: 'empty',
                            prompt: 'Por favor ingresa el enfermedades profesionales.'
                        }]
                    },
                }
            }

        };

        let rules = validations[gis.type_report] ? validations[gis.type_report]() : {};

        function accidenteValid() {

            const validOcurrenci = validOcurrencia();
            const validAgent = validAgente();
            const validMecanism = validMecanismo();
            const validDea = validDead();
            const validdatedisappearance = validDateDisappearance();
            const validaccident = validaccident_place();
            const validauxilio = validauxilios();
            const validexternal_cau = validexternal_cause();

            if (!validOcurrenci || !validAgent || !validMecanism || !validDea || !validdatedisappearance ||
                !validaccident || !validexternal_cau || !validauxilio) {
                return false;
            }

            return true;
        }

        function enfermedadValid() {}


        function handleSubmit(form) {

            var reportType = $('#reportTypeDropdown input[name="report_type"]').val();
            var caseNumber = $('#case_number').val();

            switch (reportType) {
                case 'Accidente':
                    if (!accidenteValid()) {
                        return;
                    }
                    break;
                case 'Enfermedad':
                    enfermedadValid();
                    break;

                default:
                    return;
                    break;
            }

            loadingMain(true);
            $('#btn-submit-data').prop('disabled', true);




            var fieldData = [];

            // Selecciona todos los inputs con el nombre 'parte_cuerpo' dentro de 'field_container_reporte'
            $('#field_container_reporte input[name="parte_cuerpo"]').each(function() {

                var value = $(this).val();
                var id = $(this).attr('id');
                if (value) {
                    fieldData.push({
                        id: id,
                        value: value
                    });
                }
            });

            // Crear un objeto FormData
            var formData = new FormData(form);
            // Agregar los valores dinámicos de fieldData al FormData como un JSON string
            formData.append('fieldData', JSON.stringify(fieldData));

            $.ajax({
                type: 'POST',
                url: '/services/gis/furat/saveDataAccidenteFurat',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    goNext();
                },
                error: function(error) {
                    loadingMain(false);
                    $('#btn-submit-data').prop('disabled', false);
                    console.error('Error al enviar datos', error);
                }
            });


        }

        function formInicial() {
            $('#form_accident').form({
                fields: rules,
                on: 'blur',
                onSuccess: function(event) {
                    handleSubmit(this);
                },
                onFailure: function() {
                    console.log('Formulario no válido');
                }
            });
        }

        $(document).ready(function() {
            $('#btn-submit-data').on('click', function(event) {
                event.preventDefault();

                formInicial();
                $('#form_accident').form('validate form'); // Valida el formulario completo
            });

        });
    </script>
@endsection
