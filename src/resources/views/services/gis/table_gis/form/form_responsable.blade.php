@extends('services.gis.table_gis.gis_tomador')

@section('title', 'Responsable del Aviso')

@section('form_gis_tomador')

    <input type="hidden" name="model" value="{{ $policy_contacts[0]->model  ?? ''}}">
    <input type="hidden" name="responsible_id" value="{{ $policy_contacts[0]->responsible_id  ?? ''}}">

    <div class="ui styled fluid accordion ">
        @if (isset($policy))
            <div class="title active">
                <i class="dropdown icon button"></i>
                Datos de validación <span style="color: red;" class="required">*</span>
            </div>
            <div class="content active">
                <div class="ui form"> 
                    <div class="two fields">
                        <div class="required field">
                            <label>Código único</label>
                            <input type="text" id="unique_code" placeholder="Código Único" value="{{ $policy_contacts[0]->unique_code  ?? '' }}"
                                style="background :#e0e0e0" readonly>
                        </div>
                        <div class="required field">
                            <label>Número de identificación persona que reporta</label>
                            @if(count($policy_contacts) > 1)
                                <select name="number_identify_responsible">
                                    @foreach($policy_contacts as $contact)
                                        <option value="{{ $contact->number_identify_responsible }}" 
                                            {{ $loop->first ? 'selected' : '' }}>
                                            {{ $contact->name_responsible }} - {{ $contact->number_identify_responsible }}
                                        </option>
                                    @endforeach
                                </select>
                            @else
                                <input type="text"
                                    value="{{ count($policy_contacts) > 0 ? $policy_contacts[0]->number_identify_responsible : '' }}"
                                    style="background :#e0e0e0"
                                    placeholder="Número de identificación persona que reporta"
                                    readonly>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        @else
            <div class="title active ">
                <i class="dropdown icon button"></i>
                Datos de validación <span style="color: red;" class="required">*</span>
            </div>
            <input id="call_center" type="hidden" value="call_center" />
            <div class="content active">
                <div class="ui form">
                    <div class="three  fields">
                        <div class="required field">
                            <label>Código único</label>
                            <input type="text" placeholder="Código Único" value="" name="code_unique">
                        </div>
                        <div class="required field">
                            <label>Número de identificación persona que reporta</label>
                            <input type="text" value="" placeholder="Numero de identificación persona que reporta"
                                name="person_report">
                        </div>
                        <div class="field button_validar" onClick="validateTaker()">
                            <button class="ui button primary">
                                Validar
                            </button>
                        </div>
                    </div>
                    <div class="field" style="display: none;" id="benefit_collective_div" >
                        <label>Beneficio de colectividad</label>
                        <input style="width: 50px; background :#e0e0e0" readonly type="text" id="benefit_colective" name="benefit_colective" placeholder="Beneficio colectivo" readonly>
                    </div>
                </div>
            </div>
        @endif


        <div class="title active ">
            <i class="dropdown icon button"></i>
            Datos del tomador <span style="color: red;" class="required">*</span>
        </div>
        <div class="content active">
            <div class="ui form">
                <div class="four fields">
                    <div class="field">
                        <label>Póliza SORT</label>
                    
                        <!-- Select para múltiples pólizas (oculto por defecto) -->
                        <div id="div_number_policy_select" style="display: none !important;">
                            <select name="number_policy" id="number_policy_select" class="ui dropdown input" >
                                <option value="">Seleccione una póliza</option>
                            </select>
                        </div>
                    
                        <!-- Input para una sola póliza -->
                        <input type="text" name="number_policy_show" id="number_policy_show" placeholder="No Póliza"
                            style="background: #e0e0e0" readonly>
                    
                        <input type="hidden" name="number_policy" id="number_policy">
                    </div>
                    
                    <div class="required field">
                        <label>Tipo de identificación</label>
                        <div class="ui selection dropdown readonly" readonly id="tipo_identificacion"
                            style="background :#e0e0e0; pointer-events:none;">
                            <input type="hidden" readonly name="type_identification"
                                value="{{ isset($policy) ? $policy->activity->affiliate->doc_type : '' }}">
                            <i class="dropdown icon"></i>
                            <div class="default text">Selecciona una opción</div>
                            <div class="menu">
                                @foreach ($DOC_TYPES as $k => $v)
                                    <div class="item @if (isset($policy) && $policy->activity->affiliate->doc_type == $k) active selected @endif"
                                        data-value="{{ $k }}">
                                        {{ $v }}
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                    <div class="field">
                        <label>Número de identificación</label>
                        <input type="text" placeholder="Numero de identificación" style="background :#e0e0e0"
                            value="{{ isset($policy) ? $policy->activity->affiliate->doc_number : '' }}" readonly
                            name="number_identification">
                    </div>
                    <div class="field">
                        <label>Nombre</label>
                        <input type="text" placeholder="Nombre" style="background :#e0e0e0"
                            value="{{ isset($policy) ? ucwords(strtolower($policy->activity->affiliate->full_name)) : '' }}"
                            readonly name="full_name">
                    </div>
                </div>
            </div>
        </div>

        <div class="title active ">
            <i class="dropdown icon button"></i>
            Datos persona autorizada para el reporte <span style="color: red;" class="required">*</span>
        </div>
        <div class="content active">
            <div class="ui form">
                <div class="four fields">

                    <div class="required field">
                        <label>Tipo de identificación autorizado</label>
                        <div class="ui selection dropdown readonly" id="type_identification_responsible"
                            style="background :#e0e0e0; pointer-events:none;">
                            <input type="hidden" readonly name="type_identification_responsible"
                                value="{{ isset($policy) && count($policy_contacts) > 0 ? $policy_contacts[0]->type_identification : '' }}">
                            <i class="dropdown icon"></i>
                            <div class="default text">Selecciona una opción</div>
                            <div class="menu">
                                @foreach ($DOC_TYPES as $k => $v)
                                    <div class="item @if (isset($policy) && count($policy_contacts) > 0 && $policy_contacts[0]->type_identification == $k) active selected @endif"
                                        data-value="{{ $k }}">
                                        {{ $v }}
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>

                    <div class="field">
                        <label>Número de identificación</label>
                        <input type="text" placeholder="Numero de identificación" style="background :#e0e0e0"
                            value="{{ isset($policy) && count($policy_contacts) > 0 ? $policy_contacts[0]->number_identify_responsible : '' }}"
                            readonly name="number_identification_responsible">
                    </div>
                    <div class="field">
                        <label>Nombre</label>
                        <input type="text" placeholder="Nombre" style="background :#e0e0e0"
                            value="{{ isset($policy) && count($policy_contacts) > 0 ? ucwords(strtolower($policy_contacts[0]->name_responsible)) : '' }}"
                            readonly name="name_responsible">
                    </div>
                    <div class="field">
                        <label>Celular de contacto</label>
                        <input type="text" placeholder="Celular de contacto" style="background :#e0e0e0"
                            value="{{ isset($policy) && count($policy_contacts) > 0 ? $policy_contacts[0]->phone_responsible : '' }}"
                            readonly name="cellphone_responsible">
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="field button_siguiente">
        @if (isset($policy->id))
            <a href="{{ secure_url('/servicio/gis_sort/tomador_datos/' . $policy->id) }}" class="ui button primary">
                Siguiente
            </a>
        @else
            <a class="ui button primary" id="urlDinamyc">
                Siguiente
            </a>
        @endif
    </div>

    <style>
        .button_siguiente {
            margin-top: 1rem;
            display: flex;
            justify-content: flex-end;
            width: 100%;
        }

        .button_validar {
            display: flex;
            align-items: flex-end;
            justify-content: center;
        }
    </style>

    <script>
        $(document).ready(function () {
            $(document).ready(function() {
                $('#number_policy_select').on('change', function() {
                    $('#number_policy').val($(this).val());
                });
            });

           
            $('#benefit_collective_div').hide();

            var policy = @json($policy);

            if (policy) { 
                const formattedSortNumber = formatSortNumber(policy.consecutive);
                $('#number_policy_show').val(formattedSortNumber).show();
                $('#number_policy').val(policy.id);
                $('#div_number_policy_select').hide();
            } else {
                // Si no hay póliza, asegurarse de limpiar los campos
                $('#number_policy_show').val('').hide();
                $('#number_policy').val('');
                $('#div_number_policy_select').hide();
            }

            $('#div_number_policy_select').hide();

            // Al cambiar el valor del select
            $('select[name="number_identify_responsible"]').on('change', function () {
                var selectedValue = $(this).val();

                // Buscar el contacto correspondiente en el array de contactos
                var contact = @json($policy_contacts).find(c => c.number_identify_responsible == selectedValue);

                if (contact) {
                    // Actualizar campos con la información del contacto seleccionado
                    $('input[name="number_identification_responsible"]').val(contact.number_identify_responsible);
                    $('input[name="name_responsible"]').val(contact.name_responsible);
                    $('input[name="cellphone_responsible"]').val(contact.phone_responsible);
                    $('input[name="type_identification_responsible"]').val(contact.type_identification);
                    $('#unique_code').val(contact.unique_code);
                    $('input[name="model"]').val(contact.model);
                    $('input[name="responsible_id"]').val(contact.responsible_id);

                    // Marcar el tipo de identificación en el dropdown
                    $('#type_identification_responsible .item').removeClass('active selected');
                    $('#type_identification_responsible .item[data-value="' + contact.type_identification + '"]').addClass('active selected');
                }
            });
        });
    </script>

    <script>
        $('.ui.accordion')
            .accordion();

        $('.ui.dropdown').dropdown();
    </script>

    <script>
        document.querySelector('.button_siguiente a').addEventListener('click', function(event) {
            // Prevenir la redirección inicialmente
            event.preventDefault();

            // Obtener los campos que deben ser validados
            const tipoIdentificacionTomador = document.querySelector('input[name="type_identification"]');
            const numeroIdentificacionTomador = document.querySelector('input[name="number_identification"]');
            const nombreTomador = document.querySelector('input[name="full_name"]');
            const tipoIdentificacionAutorizado = document.querySelector(
                'input[name="type_identification_responsible"]');
            const numeroIdentificacionAutorizado = document.querySelector(
                'input[name="number_identification_responsible"]');
            const nombreAutorizado = document.querySelector('input[name="name_responsible"]');
            const celularAutorizado = document.querySelector('input[name="cellphone_responsible"]');

            const model = document.querySelector('input[name="model"]').value;
            const responsible_id = document.querySelector('input[name="responsible_id"]').value;

            const numberPolicy = document.getElementById('number_policy').value.trim();

            if (!numberPolicy) {
                Swal.fire({
                    icon: "warning",
                    title: "Campos obligatorios",
                    text: "El campo Póliza SORT es obligatorio",
                });

                return;
            }


            // Validar que todos los campos tengan valores
            if (
                !tipoIdentificacionTomador.value.trim() ||
                !numeroIdentificacionTomador.value.trim() ||
                !nombreTomador.value.trim() ||
                !tipoIdentificacionAutorizado.value.trim() ||
                !numeroIdentificacionAutorizado.value.trim() ||
                !nombreAutorizado.value.trim() ||
                !celularAutorizado.value.trim() || 
                !numberPolicy
            ) {

                // Mostrar alerta si falta algún campo
                Swal.fire({
                    icon: "warning",
                    title: "Campos obligatorios",
                    text: "Todos los campos son obligatorios",
                });
            } else {

                var call_center = $('#call_center').val();
                @isset($policy)
                    loadingMain(true);
                    const urlWithParams = `{{ secure_url('/servicio/gis_sort/tomador_datos/' . $policy->id) }}?model=${encodeURIComponent(model)}&responsible_id=${encodeURIComponent(responsible_id)}`;
                    window.location.href = urlWithParams;
                @else
                    loadingMain(true);
                    const policyId = $('#number_policy').val();
                    const dynamicUrl = `/servicio/gis_sort/tomador_datos/${policyId}?call_center=${encodeURIComponent(call_center)}&model=${encodeURIComponent(model)}&responsible_id=${encodeURIComponent(responsible_id)}`;
                    window.location.href = dynamicUrl;
                @endisset
            }
        });
    </script>
    <script>
        
        function validateTaker() {

            var code_unique = $('input[name="code_unique"]').val();
            var person_report = $('input[name="person_report"]').val();

            if (!code_unique || !person_report) {

                Swal.fire({
                    title: 'Importante!',
                    text: 'Todos los campos son obligatorios',
                    icon: 'warning',
                });

                return;
            }



            loadingMain(true);

            $.ajax({
                url: '/services/gis/validate/taker',
                type: 'POST',
                data: {
                    code_unique: code_unique,
                    person_report: person_report
                },
                success: function(response) {
                    loadingMain(false);

                    if (response.taker && response.responsible) {
                        // Se encontraron datos del afiliado en la planilla
                        Swal.fire({
                            title: 'Importante!',
                            text: 'Tomador y responsable encontrado',
                            icon: 'success',
                        });

                        addFieldTaker(response.taker, response.policy);
                        addFieldResponsible(response.responsible);

                    } else {
                        loadingMain(false);

                        // No se encontraron datos del afiliado en la planilla
                        Swal.fire({
                            title: 'Importante!',
                            text: 'No se encontró asociado este código único a alguna póliza vigente o no se encuentra este número de identificación asociado a la póliza',
                            icon: 'warning',
                        });


                    }
                },
                error: function(error) {
                    loadingMain(false);

                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: 'Ocurrió un problema al enviar el formulario. Por favor, inténtalo de nuevo.'
                    });
                }
            })

        }

        var policyData = "";

        $('#number_policy_select').on('change', function () {
            const selectedValue = $(this).val();

            // Setea el valor seleccionado en los inputs
            $('#number_policy').val(selectedValue);
            $('#number_policy_show').val(selectedValue);

            console.log('policy',policyData );
            
            // Aseguramos que 'policy' siempre sea un array
            var policy = Array.isArray(policyData) ? policyData : [policyData];

            console.log('policyData', policy);
            // Buscar la póliza por ID
            var selectedPolicy = policy.find(function (item) {
                return item.id == selectedValue;
            });

            console.log('selectedPolicy', selectedPolicy);
            console.log('selectedValue', selectedValue)

            $('#benefit_collective_div').show();

            // Si se encontró la póliza, setear el valor en benefit_collective
            if (selectedPolicy) {
                $('#benefit_colective').val(selectedPolicy.benefit_colective || 'No');
            } else {
                $('#benefit_colective').val('No');
            }

        });


        function addFieldTaker(taker, policy) {
            if (Array.isArray(policy) && policy.length > 1) {
                // Si hay múltiples pólizas, usar el select
                $('#number_policy_select').empty().append('<option value="">Seleccione una póliza</option>');

                $.each(policy, function(index, p) {
                    $('#number_policy_select').append(`<option value="${p.id}">${formatSortNumber(p.consecutive)}</option>`);
                });

                $('#div_number_policy_select').show();
                $('#number_policy_show').hide();
            } else {
                // Suponiendo que siempre habrá al menos una póliza
                let policy_sort;

                // Si es un array (aunque sea de 1 elemento), toma el primero
                if (Array.isArray(policy)) {
                    policy_sort = policy[0];
                } else {
                    // Si es un objeto, úsalo tal cual
                    policy_sort = policy;
                }

                // Si solo hay una póliza, usar el input
                const formattedSortNumber = formatSortNumber(policy_sort.consecutive);
                $('#number_policy_show').val(formattedSortNumber).show();
                $('#number_policy').val(policy_sort.id);
                $('#div_number_policy_select').hide();

                const benefit = policy_sort?.benefit_colective ?? 'No';
                $('#benefit_colective').val(benefit); 
                $('#benefit_collective_div').show();
            }

            policyData = policy;

            // Actualizar otros campos
            $('input[name="type_identification"]').val(taker.doc_type);
            $('#tipo_identificacion').dropdown('set selected', taker.doc_type);
            $('input[name="number_identification"]').val(taker.doc_number);
            $('input[name="full_name"]').val(taker.first_name);
        }


        function addFieldResponsible(responsible) {
            //Lenamos los datos del tomador
            const typeIdentification = responsible.type_identification;

            // Cambiar el valor del input oculto
            $('input[name="type_identification_responsible"]').val(typeIdentification);

            // Actualizar el dropdown visualmente
            $('#type_identification_responsible').dropdown('set selected', typeIdentification);

            $('input[name="number_identification_responsible"]').val(responsible.number_identify_responsible);
            $('input[name="cellphone_responsible"]').val(responsible.cellphone_responsible);
            $('input[name="name_responsible"]').val(responsible.name_responsible);

            $('input[name="model"]').val(responsible.model);
            $('input[name="responsible_id"]').val(responsible.responsible_id);
        }
    </script>

@endsection
