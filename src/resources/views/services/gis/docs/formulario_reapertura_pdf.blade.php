<!DOCTYPE html>

<head>
    <meta content="text/html; charset=utf-8" http-equiv="Content-Type" />
    <meta content="text/html; charset=utf-8" http-equiv="Content-Type" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style>
        * {
            font-family: 'Arial', sans-serif;
            font-size: 8pt;
            box-sizing: border-box;
        }

        /* Asegura que `html` y `body` no tengan márgenes ni scroll horizontal */
        html,
        body {
            margin: 0;
            padding: 0;
            width: 100%;
            height: 100%;
            overflow-x: hidden;
            /* Evita el desplazamiento horizontal */
        }

        body {
            margin: 0;
            padding-top: 50px;
            background-image: url('https://mnk-prod.s3.us-east-1.amazonaws.com/imagenes/fondo_comunicado.png');
            background-position: -5px -4px;
            background-repeat: no-repeat;
            background-attachment: fixed;
            background-size: 375px;
        }


        /* Footer pegado abajo */
        .footer {
            position: fixed;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 100px;
            text-align: center;
            padding: 0;
            overflow: hidden;
        }

        .footer img {
            width: 80%;
            height: 100%;
        }
        .contenedor {
            max-width: 800px;
            margin: 0 auto;
            padding: 77px;
            background: transparent;
            border-radius: 10px;
            box-shadow: none;
            display: block;
        }

        /* Estilos para los párrafos */
        p,
        p strong {
            line-height: 1.7;
            /* Interlineado vertical */
            margin-bottom: 21px;
            /* Espacio entre párrafos */
            font-size: 16px;
            /* Tamaño del texto */
        }


        table {
            border-collapse: collapse;
            width: 100%;
        }

        /* Para mejorar la visibilidad en pantallas
          pequeñas */
        @media (max-width: 768px) {

            th,
            td {
                font-size: 0.9em;
                /*
              Ajusta el tamaño de fuente en pantallas pequeñas */
            }
        }

        @page {
            margin-top: 50px;
            /* Ajusta este valor según lo necesario */
        }
    </style>
</head>

<body>
    <header
        style="width: 100%; z-index: 1000; position: fixed; top: 60px; padding-right: 70px; text-align: right; height: 70px;">
        <img src="https://mnk-prod.s3.us-east-1.amazonaws.com/imagenes/mnk_siempre_diferentes.png" alt="Logo"
            class="logo-image" style="height: 55px;" />
    </header>

    <div class="contenedor">
        <div style="margin-top: 20px;">
            <p>
                <strong>Fecha:</strong>
                {{ $fecha_solicitud ? ucfirst(strftime('%A %e de %B del %Y', strtotime($fecha_solicitud))) : '' }}
            </p>
            <p>
                <strong>Datos del asegurado:</strong>
            </p>
            <ul>
                <li><strong>Nombre completo:</strong> {{ $data['name_affiliate'] }}</li>
                <li><strong>Número de la cédula:</strong> {{ $data['num_affiliate'] }}</li>
            </ul>
            <hr>
            <br>
            <p>
                <strong>Datos del tomador:</strong>
            </p>
            <ul>
                <li><strong>Nombre o razón social:</strong> {{ $data['name_holder'] }}</li>
                <li><strong>Número de póliza:</strong> {{ $data['num_policy'] }}</li>
            </ul>
            <hr>
            <br>
            <p>
                <strong>Información del caso:</strong>
            </p>
            <ul>
                @php
                    //se obtiene la fecha de alta medica
                    $fecha_alta_medica = $data['date_medical_discharge'];
                    //validar si la fecha de alta medica no es nula
                    if ($fecha_alta_medica) {
                        $fecha_alta_medica = date('d/m/Y', strtotime($fecha_alta_medica));
                    }
                    else{
                        $fecha_alta_medica = 'Fecha no disponible';
                    }
                @endphp
                <li><strong>Número del caso:</strong> {{ $data['num_case'] }}</li>
                <li><strong>Fecha de alta médica anterior:</strong> {{ $fecha_alta_medica }}</li>
                <li><strong>Motivo de la solicitud de reapertura:</strong> {{ $data['reason'] }}</li>
            </ul>
            <hr>
            <p>
                <strong>Nombre del solicitante:</strong> {{ $data['name_applicant'] }}
            </p>
            <br>
            <p>
                <strong>Firma: </strong> <img src="{{ $data['sign1_url'] }}" alt="Firma del representante"
                    width="100" height="50">
            </p>
        </div>
        <footer class="footer">
            <img src="https://mnk-prod.s3.us-east-1.amazonaws.com/imagenes/mnk_footer.png" alt="pie de documento" />
        </footer>
    </div>
</body>

</html>
