<!DOCTYPE html>
<html lang="es">
<head>
    <meta content="text/html; charset=utf-8" http-equiv="Content-Type"/>
    <style type="text/css">
        @font-face {
            font-family: 'Century' !important;
            src: url({{ storage_path('fonts/CenturyGothic.ttf') }}) format("truetype") !important;
            font-weight: 400 !important;
            font-style: normal !important;
        }

        @font-face {
            font-family: 'Century-Bold' !important;
            src: url({{ storage_path('fonts/CenturyGothicBoldA1.ttf') }}) format("truetype") !important;
            font-weight: 700 !important;
            font-style: normal !important;
        }

        * {
            font-family: 'Century', sans-serif;
            font-size: 9pt;
        }

        b {
            font-family: 'Century-Bold', sans-serif !important;
        }

        body {
            padding-top: 75px;
        }

        .watermark {
            position: fixed;
            top: 45%;
            width: 100%;
            text-align: center;
            opacity: .1;
            transform: rotate(-45deg);
            transform-origin: 50% 50%;
            z-index: 1000;
        }

        .header {
            height: 40px;
            width: 100%;
            position: fixed;
            z-index: 0 !important;
        }

        .header img {
            height: 210px;
            width: 818px;
            position: absolute;
            left: -70px;
            top: -50px;
        }

        .footer {
            height: 40px;
            width: 100%;
            position: fixed;
            z-index: 0 !important;
            left: -82px;
            top: 78%;
        }

        .footer img {
            height: 210px;
            width: 830px;
        }

        .border-left {
            border: 0;
            border-left: 1px solid black;
        }

    </style>
</head>
@php
    $author = null;
    $date_action = null;
    foreach($activity->activity_actions as $aa){
        if ($aa->action_id == 1351) {
           $author = $aa->author_id;
           $date_action = $aa->created_at;
        }
    }
@endphp
<body>
@if($watermark)
    <div class="watermark">
        <span style="font-size: 72pt;font-weight: 500;">VISTA PREVIA</span>
    </div>
@endif
<div class="header">
{{--    <img src="{{storage_path('app/client_logo/new_header_equidad.png')}}" alt="La equidad seguros">--}}
</div>
<div class="footer">
{{--    <img src="{{storage_path('app/client_logo/new_footer_equidad.png')}}" alt="La equidad seguros">--}}
</div>
<div style="z-index: 1000 !important">
    <p style="padding-top: 2rem;">Bogotá
        D.C, {{$date_action? $date_action : Carbon\Carbon::now()->formatLocalized('%B %d, %Y')}}</p>
    <p>
        Señor(a): <br/>
        <b>{{$dictum_reception->entity_name ?  $ENTITY_NAME_PERITAJE[$dictum_reception->entity_name]['name'] : 'NO ESPECIFICA' }}</b>
        <br/>
        <b>Nit:</b> {{$dictum_reception->entity_name ?  $ENTITY_NAME_PERITAJE[$dictum_reception->entity_name]['nit'] : '' }}
        <br/>
        <b>Correo
            Electrónico:</b> {{$dictum_reception->entity_name ?  $ENTITY_NAME_PERITAJE[$dictum_reception->entity_name]['email'] : '' }}
        <br/>
        <b>Dirección:</b> {{$dictum_reception->entity_name ?  $ENTITY_NAME_PERITAJE[$dictum_reception->entity_name]['address'] : '' }}
        <br/>
        <b>Teléfono:</b> {{$dictum_reception->entity_name ?  $ENTITY_NAME_PERITAJE[$dictum_reception->entity_name]['phone'] : '' }}
        <br/>
        {{$dictum_reception->entity_name ?  $ENTITY_NAME_PERITAJE[$dictum_reception->entity_name]['city'] : 'NO ESPECIFICA' }}
        <br/>
    </p>

</div>
<div style="padding-top: 0.5rem;z-index: 1000">
    <p style="padding-left: 1.5rem;"><b>Referencia: </b>{{$dictum_reception ? $dictum_reception->subject : ''}}
    </p>
</div>
<div style="padding-top: 0.5rem;z-index: 1000">
    <p>
        Respetados señores:
    </p>
    {{--    <p>--}}
    {{--        En atención a oficio remitido por {{$dictum_reception ? $dictum_reception->entity_name : ''}} a La Equidad Seguros--}}
    {{--        O.C. - ARL le comunicamos que esta Compañía--}}
    {{--        recibió la documentación enviada en {{$folio ? $folio : ''}} {{$folio == 1 ? 'folio' : 'folios'}}, como soportes del fundamento de hecho y derecho de la--}}
    {{--        calificación de Origen por evento del asunto, según se establece en la Ley 1562 de 2012, Decreto 1072 de 2015--}}
    {{--        -Titulo 5 (Anterior Decreto 1352 de 2013) y el artículo 142 del Decreto 0019 de 2012.--}}
    {{--    </p>--}}
    {{--    <p>--}}
    {{--        Respecto a lo anterior, revisada la documentación aportada, está Aseguradora determinó pronunciarse en--}}
    {{--        {{strtoupper($dictum_reception ? $dictum_reception->pronouncement : '')}} con la calificación de origen emitida por--}}
    {{--        ustedes en primera oportunidad por la(s) patología(s)--}}
    {{--        @if($dictum_reception)--}}
    {{--            @foreach($dictum_reception->diagnostics as $diag)--}}
    {{--                {{$diag->cod}} {{$diag->description}}--}}
    {{--                ({{\App\Providers\AppServiceProvider::$LATERALITY[$diag->laterality]}});--}}
    {{--            @endforeach--}}
    {{--        @endif--}}
    {{--    </p>--}}
    {{--    <p>--}}
    {{--        Se trata de afilado de {{$activity->affiliate->age()}} años de edad actualmente, de--}}
    {{--        dominancia {{$activity->affiliate->dominance ? \App\Providers\AppServiceProvider::$DOMINANCE[$activity->affiliate->dominance] : 'SIN DEFINIR'}}--}}
    {{--        ,quien registra en nuestros aplicativos afiliación--}}
    {{--        con {{$activity->affiliate->doc_type}} {{$activity->affiliate->doc_number}}--}}
    {{--        - {{$activity->affiliate->full_name}}--}}
    {{--        desde {{$activity->affiliate->employments ? $activity->affiliate->employments[0]->start_date->formatLocalized('%d/%m/%Y') : ''}}--}}
    {{--        sin fecha de--}}
    {{--        retiro. Según--}}
    {{--        dictamen emitido por EPS registra--}}
    {{--        ocupación {{$activity->affiliate->employments ? $activity->affiliate->employments[0]->position : ''}} con fecha--}}
    {{--        de ingreso--}}
    {{--        el {{$activity->affiliate->employments ? $activity->affiliate->employments[0]->start_date->formatLocalized('%d/%m/%Y') : ''}}--}}
    {{--        sin fecha de retiro.--}}
    {{--    </p>--}}
    {{--    <p>--}}
    {{--        Con base en la documentación aportada, el equipo interdisciplinario de calificación de esta administradora--}}
    {{--        determina estar en {{strtoupper($dictum_reception ? $dictum_reception->pronouncement : '')}} con la calificación en--}}
    {{--        primera oportunidad emitida por {{$dictum_reception ? $dictum_reception->entity_name : ''}} al trabajador--}}
    {{--        {{$activity->affiliate->full_name}} {{$activity->affiliate->doc_type}} {{$activity->affiliate->doc_number}} ,--}}
    {{--        Siniestro {{$dictum_reception ? $dictum_reception->sinister_number : ''}} , radicada en esta entidad--}}
    {{--        el {{$dictum_reception ? $dictum_reception->sinister_radication_date : ''}} , bajo--}}
    {{--        el diagnóstico--}}
    {{--        @if($dictum_reception)--}}
    {{--            @foreach($dictum_reception->diagnostics as $diag)--}}
    {{--                {{$diag->cod}} {{$diag->description}}--}}
    {{--                ({{\App\Providers\AppServiceProvider::$LATERALITY[$diag->laterality]}})--}}
    {{--            @endforeach--}}
    {{--            considerando este de ORIGEN COMÚN.--}}
    {{--        @endif--}}
    {{--    </p>--}}
    <p style="text-align: justify;">
        @if($dictum_reception->argumentation)
            {!! nl2br(e($dictum_reception->argumentation)) !!}
        @endif
    </p>
    {{--    <p>--}}
    {{--        Se solicita muy especialmente, en el evento que cualquiera de las otras partes vinculadas al proceso interponga--}}
    {{--        algún recurso de ley, esta Compañía sea notificada del nuevo dictamen, para reservarnos el derecho de una--}}
    {{--        participación ante las Juntas de Calificación.--}}
    {{--    </p>--}}
    <div>
        <b>
            NOTIFICACIONES
        </b>
    </div>
    <p>
        Recibiremos notificaciones en la carrera 9A No. 99-07 Piso 12, 13, 14 y Altillo Torre la Equidad en la ciudad de
        Bogotá o al correo electrónico <a href="<EMAIL>"><EMAIL></a>.
    </p>

</div>
{{--<div style="padding-top: 2rem;">--}}
{{--    <p>Cordialmente,</p>--}}
{{--    <p>--}}
{{--        @unless($watermark)--}}
{{--            <img style="height: 80px; width: auto; margin-top: 30px; border-bottom: 1px solid black;" alt="FIRMA"--}}
{{--                 src="{{storage_path('app/firma_fcopete.png')}}"/>--}}
{{--            <br/>--}}
{{--            <br/>--}}
{{--        @endunless--}}
{{--        Departamento Medicina Laboral <br>--}}
{{--        Convenio ARL La Equidad – Ren Consultores--}}
{{--    </p>--}}
{{--</div>--}}
<div style="padding-top: 1rem;z-index: 1000">
    <p>Cordialmente,</p>
    <p>
        @unless($watermark)
            <img style="height: 80px; width: auto; margin-top: 25px; border-bottom: 1px solid black;" alt="FIRMA"
                 src="{{storage_path('app/firma_default_pronouncement.png')}}"/>
            <br/>
            <br/>
        @endunless
        Departamento Medicina Laboral <br>
        Convenio Ren Consultores – La Equidad Seguros
    </p>
</div>
<div style="padding-top: 1rem; font-size: 6.5pt;z-index: 1000">
    Anexo: 0 Folios<br>
    Copia Asegurado: {{$activity->affiliate->full_name}} <br>
    {{$activity->affiliate->address}} <br/>
    Teléfono/Celular: {{$activity->affiliate->phone}} - {{$activity->affiliate->cellphone}} <br/>
    {{$activity->affiliate->city}}
</div>
</body>
</html>