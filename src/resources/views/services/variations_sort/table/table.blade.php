@extends('layouts.main')

@section('title', 'Variaciones SORT')

@section('menu')
    @parent
@endsection

@section('content')


    <style>
        .bfilter {
            margin-top: 30px !important;
        }

        .pagination {
            display: grid;
            justify-items: end;
        }

        .truncate {
            /* white-space: nowrap; */
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 1px;
            min-width: 40px;
            /* vertical-align: top;
                                                                                                                                                                                                        text-align: center !important; */
            padding: 1em 6px !important;
        }
    </style>

    <div class="ui basic segment">
        {{-- <div class="filter"> --}}
        <h1 class="ui header">Variaciones póliza SORT</h1>
        <div class="ui styled fluid accordion">
            <div class="active title">
                <i class="dropdown icon"></i>
                Filtros
            </div>
            <div class="active content">
                <form class="ui form small clearing" method="GET">
                    {{ csrf_field() }}
                    <div class="three fields">
                        <div class="field">
                            <select class="ui fluid search dropdown" name="state_ids[]" multiple="">
                                <option value="">Estado</option>
                                @foreach ($states as $state)
                                    <option value="{{ $state->id }}"
                                        {{ in_array($state->id, request()->input('state_ids', [])) ? 'selected' : '' }}>
                                        {{ ucfirst(mb_strtolower($state->name)) }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    </div>

                    <div class="fields bfilter">
                        <div class="field">
                            <button class="ui primary  button">
                                <i class="search icon"></i>
                                Aplicar filtros
                            </button>
                        </div>

                        <div class="field">
                            <button class="ui secondary  button" id="reset" type="reset">
                                <i class="undo icon"></i>
                                Limpiar filtros
                            </button>
                        </div>

                    </div>
                </form>
            </div>
            {{-- </div> --}}

            <div class="active title">
                <i class="dropdown icon"></i>
                Resultados de la búsqueda
            </div>
            <div class=" active content">
                <table class="ui celled sortable striped compact very  table selectable "
                    style="font-size: 0.8em; line-height: 1em;" id="results">
                    <thead>
                        <tr>
                            <th class="truncate pop" title="Fecha de creación variación"
                                data-tooltip="Fecha de creación variación">Fecha de creación variación
                            </th>
                            <th class="truncate pop" data-tooltip="No. Caso Variación"># de caso por variación</th>
                            <th class="truncate pop" style="width: 80px; max-width: 80px;" data-tooltip="# de póliza">Póliza SORT</th>
                            <th class="truncate pop" data-tooltip="Tomador">Tomador</th>
                            <th class="truncate pop" data-tooltip="Correduria">Correduria</th>
                            <th class="truncate pop" data-tooltip="Intermediario">Intermediario</th>
                            <th class="truncate pop" data-tooltip="Tipo de Variación">Tipo de variación</th>
                            <th class="truncate pop" data-tooltip="Estado de Variación">Estado de variación</th>
                            <th class="truncate pop" style="width: 100px; max-width: 102px;"
                                data-tooltip="Estado de gestión">Estado de gestión</th>
                            <th class="truncate pop" data-tooltip="Motivo">Motivo</th>
                            <th class="truncate pop" style="width: 60px; max-width: 60px;">Acciones</th>
                        </tr>
                    </thead>




                    <tbody>
                        @foreach ($variations as $variation)
                            <tr>
                                <!-- Fecha de creación variación -->
                                <td class="truncate pop"
                                    title="{{ $variation->created_at->formatLocalized('%A %d de %B de %Y') ?? '' }}"
                                    data-tooltip="{{ $variation->created_at->formatLocalized('%A %d de %B de %Y') ?? '' }}">
                                    {{ ucfirst(strtolower($variation->created_at->formatLocalized('%A %d de %B de %Y'))) ?? '' }}
                                </td>

                                <!-- No. Caso Variación -->
                                <td class="truncate pop" title="{{ $variation->variations_sort->id ?? '' }}"
                                    data-tooltip="{{ $variation->variations_sort->id ?? '' }}">
                                    {{ ucfirst(strtolower($variation->variations_sort->id ?? '')) }}
                                </td>

                                <!-- No. Poliza -->

                                <td class="truncate pop"
                                    title="{{ isset($variation->parent_activity->policy_sort) ? ucfirst(strtolower($variation->parent_activity->policy_sort->formatNumberConsecutive())) : '' }}"
                                    data-tooltip="{{ isset($variation->parent_activity->policy_sort) ? ucfirst(strtolower($variation->parent_activity->policy_sort->formatNumberConsecutive())) : '' }}">
                                    {{ isset($variation->parent_activity->policy_sort) ? ucfirst(strtolower($variation->parent_activity->policy_sort->formatNumberConsecutive())) : '' }}
                                </td>


                                <!-- Tomador -->
                                <td class="truncate pop"
                                    title="{{ ucwords(strtolower($variation->affiliate->first_name ?? '')) }}"
                                    data-tooltip="{{ ucwords(strtolower($variation->affiliate->first_name ?? '')) }}">
                                    {{ ucwords(strtolower($variation->affiliate->first_name ?? '')) }}
                                </td>

                                <!-- Brokerage Name -->
                                <td class="truncate pop"
                                    title="{{ ucwords(strtolower($variation->parent_activity->policy_sort->brokerage_name ?? '')) }}"
                                    data-tooltip="{{ ucwords(strtolower($variation->parent_activity->policy_sort->brokerage_name ?? '')) }}">
                                    {{ ucwords(strtolower($variation->parent_activity->policy_sort->brokerage_name ?? '')) }}
                                </td>

                                <!-- Advisor Name -->
                                <td class="truncate pop"
                                    title="{{ ucwords(strtolower($variation->parent_activity->policy_sort->advisor_name ?? '')) }}"
                                    data-tooltip="{{ ucwords(strtolower($variation->parent_activity->policy_sort->advisor_name ?? '')) }}">
                                    {{ ucwords(strtolower($variation->parent_activity->policy_sort->advisor_name ?? '')) }}
                                </td>

                                <!-- Tipo de Acción -->
                                <td class="truncate pop" title="{{ $variation->variations_sort->action->name ?? '' }}"
                                    data-tooltip="{{ $variation->variations_sort->action->name ?? '' }}">
                                    {{ ucfirst(mb_strtolower($variation->variations_sort->action->name ?? '')) }}
                                </td>

                                <!-- Estatus de Variación -->
                                <td class="truncate pop" title="{{ $variation->state->name ?? '' }}"
                                    data-tooltip="{{ $variation->state->name ?? '' }}">
                                    {{ ucfirst(mb_strtolower($variation->state->name ?? '')) }}
                                </td>

                                <!-- Estatus de Gestión -->
                                <td class="truncate pop" title="{{ $variation->variations_sort->management_result ?? '' }}"
                                    data-tooltip="{{ $variation->variations_sort->management_result ?? '' }}">
                                    {{ ucfirst(strtolower($variation->variations_sort->management_result ?? '')) }}
                                </td>

                                <!-- Motivo -->
                                <td class="truncate pop" title="{{ $variation->variations_sort->reason ?? '' }}"
                                    data-tooltip="{{ $variation->variations_sort->reason ?? '' }}">
                                    {{ ucfirst(strtolower($variation->variations_sort->reason ?? '')) }}
                                </td>

                                <!-- Acciones -->
                                <td class="truncate pop">
                                    <a class="ui icon"
                                        href="{{ secure_url('servicio/' . $variation->id) . '/variations_sort' }}">
                                        <i class="unhide icon"></i>
                                    </a>
                                </td>


                            </tr>
                        @endforeach
                    </tbody>

                </table>


                <!-- Paginación -->
                <br>
                <div class="pagination">
                    @if ($variations->hasPages())
                        <div class="ui pagination menu">
                            {{ $variations->appends(request()->query())->links() }}
                        </div>
                    @endif
                    <p>Total de registros: {{ $variations->total() }}</p>
                </div>

            </div>

        </div>

        <script>
            //boton reset formulario
            $('.ui.icon').popup({
                boundary: 'body', // Esto permite que el popup se muestre fuera de los límites de la tabla
                content: 'Ver',
                position: 'top center'
            });

            $('.pop').popup({
                boundary: 'body', // Esto permite que el popup se muestre fuera de los límites de la tabla
            });

            $('#reset').click(function() {
                $('form .ui.dropdown').dropdown('clear');
                var url = new URL(window.location.href);
                url.search = ""; // Elimina todos los parámetros de búsqueda
                history.pushState({}, '', url);
                $('form').submit();
            });

            $('.ui.dropdown').dropdown();

            $('.ui.accordion').accordion({
                exclusive: false
            });
        </script>
    @endsection
