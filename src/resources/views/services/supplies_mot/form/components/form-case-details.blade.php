<div class="title">
    <i class="dropdown icon"></i>
    <PERSON><PERSON> del caso
</div>
<div class="content">
    <div class="three fields">
        <!-- Campo fecha del caso viene desde GIS -->
        <div class="field">
            <label>Fecha del caso</label>
            <div class="ui search code">
                <div class="ui icon input">
                    <input id="date_case_medication" class="prompt readonly" type="text" name="date_case" autocomplete="off"
                           value="{{ isset($gis) ? $gis->created_at->format('d/m/Y') : ' ' }}" readonly>
                </div>
                <div class="results"></div>
            </div>
        </div>
        <!-- Campo Número del caso debe traer el número del caso del servicio GIS -->
        <div class="field">
            <label># del aviso</label>
            <div class="ui search code">
                <div class="ui icon input">
                    <input class="prompt readonly" type="text" name="number_notice" autocomplete="off"
                           value="{{ isset($gis) ? $gis->consecutive_gis : ' ' }}"
                           readonly>
                </div>
                <div class="results"></div>
            </div>
        </div>
        <div class="field">
            <label># del caso</label>
            <div class="ui search code">
                <div class="ui icon input">
                    <input class="prompt readonly" type="text" name="number_case" autocomplete="off"
                           value="{{ isset($gis) ? $gis->formatCaseNumberIfReported() : ' ' }}"
                           readonly>
                </div>
                <div class="results"></div>
            </div>
        </div>
    </div>
</div>

<script>
    $(document).ready(function () {
        // Obtener la fecha desde Laravel
        let dateCaseMed = '{{ isset($gis) ?  $gis->created_at : ' ' }}';

        // Formatear la fecha en el formato deseado
        let dateCaseMedFormat = moment(dateCaseMed).format('dddd D [de] MMMM [de] YYYY');

        // Convertir la primera letra en mayúscula
        let dateCaseMedUpp = dateCaseMedFormat.charAt(0).toUpperCase() + dateCaseMedFormat.slice(1);

        // Insertar la fecha formateada en el elemento con id "fecha-inicio"
        $('#date_case_medication').val(dateCaseMedUpp);
    });
</script>

