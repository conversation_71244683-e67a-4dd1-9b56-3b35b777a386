<div class="title">
    <i class="dropdown icon"></i> Notas de crédito
</div>
<div class="content">

    @if($premiumSurplusDetails->isNotEmpty())

        <div>
            <table class="ui celled sortable striped compact very table selectable"
                   style="font-size: 0.8em; line-height: 1em;" id="results">
                <thead>
                <tr>
                    <th class="center aligned" title="Estado">Estado</th>
                    <th class="center aligned" title="# Nota crédito"># Nota crédito</th>
                    <th class="center aligned" title="Monto">Monto</th>
                    <th class="center aligned" title="Saldo">Saldo</th>
                    <th class="center aligned" title="Saldo aplicar">Saldo aplicado</th>
                </tr>
                </thead>
                <tbody>

                    @foreach($premiumSurplusDetails as $index =>  $row )
                        <tr>
                            <td class="center aligned">{{ $row->premium_surplus->status_surplus ?? '' }}</td>
                            <td class="right aligned">{{ $row->premium_surplus_id }}</td>
                            <td class="right aligned" data-valor="{{ $row->premium_surplus->surplus_amount ?? 0 }}"> {{ number_format($row->premium_surplus->surplus_amount ?? 0, 2, ',', '.') }} </td>
                            <td class="right aligned" data-valor="{{ $row->premium_surplus->credit_balance ?? 0 }}">  {{ number_format($row->premium_surplus->credit_balance ?? 0, 2, ',', '.') }} </td>
                            <td class="right aligned" data-valor="{{ $row->surplus_amount ?? 0 }}"> {{ number_format($row->taken_value ?? 0, 2, ',', '.') }} </td>
                        </tr>
                    @endforeach

                </tbody>
                <tfoot>
                <tr>
                    <th colspan="3"></th>
                    <th class="right aligned" style="font-weight: bold;">Total</th>
                    <th class="right aligned" id="total-aprobado" style="font-weight: bold;">{{ number_format($activity->policy_sort_collection->applied_credit_note_amount ?? 0, 2, ',', '.') }}</th>
                </tr>
                </tfoot>
            </table>

            @if($activity->policy_sort_collection->payment_status <> 'approved')
                <div class="ui right floated" style="margin-bottom: 10px;">
                    <button id="btn-eliminar-notas" class="ui primary button" type="button">
                        <i class="trash icon"></i>
                        Eliminar notas crédito
                    </button>
                </div>
            @endif

        </div>
    @else

        <div class="fields">
            <div class="four wide field">
                <label>Saldo disponible</label>
                <input type="text" name="available_amount_apply" id="available_amount_apply" placeholder="" value="" class="readonly" readonly>
            </div>
        </div>
        <div>
            <table class="ui celled sortable striped compact very table selectable"
                   style="font-size: 0.8em; line-height: 1em;" id="results">
                <thead>
                <tr>
                    <th class="center aligned" title="Estado">Estado</th>
                    <th class="center aligned" title="# Nota crédito"># Nota crédito</th>
                    <th class="center aligned" title="Nombre del tomador">Nombre del tomador</th>
                    <th class="center aligned" title="Fecha de emisión">Fecha de emisión</th>
                    <th class="center aligned" title="Moneda">Moneda</th>
                    <th class="center aligned" title="Monto">Monto</th>
                    <th class="center aligned" title="Saldo">Saldo</th>
                    <th class="center aligned" title="Saldo aplicar">Saldo aplicar</th>
                    <th class="center aligned" title="Valor a aplicar">Valor a aplicar</th>
                </tr>
                </thead>
                <tbody>

                @if($premiumSurplus->isNotEmpty())
                    @php $contador = 0; @endphp
                    @foreach($premiumSurplus as $index =>  $row )
                        <tr>
                            <input type="hidden" name="notas_credito[{{$contador}}][id]" value="{{ $row->id }}">
                            <input type="hidden" name="notas_credito[{{$contador}}][saldo]" value="{{ $row->credit_balance ?? 0 }}">

                            <td class="left center">{{ $row->status_surplus ?? '' }}</td>
                            <td class="right aligned">{{ $row->id }}</td>
                            <td class="left aligned">{{ $row->policyholder_name }}</td>
                            <td class="right aligned">{{ $row->issue_date ?? '' }}</td>
                            <td class="left aligned">{{ $row->currency }}</td>
                            <td class="right aligned" data-valor="{{ $row->surplus_amount ?? 0 }}"> {{ number_format($row->surplus_amount ?? 0, 2, ',', '.') }} </td>
                            <td class="right aligned" data-valor="{{ $row->credit_balance ?? 0 }}">  {{ number_format($row->credit_balance ?? 0, 2, ',', '.') }} </td>

                            <td class="center aligned">
                                @if($row->status_surplus != 'PAG')
                                    <div class="checkbox-container">
                                        <div class="checkbox-item">
                                            <input type="radio" name="notas_credito[{{$contador}}][tipo]" value="T" id="total_{{$contador}}" onchange="handleCheckboxChange({{$contador}}, 'T')">
                                            <label for="total_{{$contador}}">T</label>
                                        </div>
                                        <div class="checkbox-item">
                                            <input type="radio" name="notas_credito[{{$contador}}][tipo]" value="P" id="parcial_{{$contador}}" onchange="handleCheckboxChange({{$contador}}, 'P')">
                                            <label for="parcial_{{$contador}}">P</label>
                                        </div>
                                    </div>
                                @endif
                            </td>

                            <td class="left aligned">
                                <input type="text" name="notas_credito[{{ $contador }}][valor_aplicar]" id="amount_to_apply_{{$contador}}" value="{{ number_format( 0, 2, ',', '.') }}"
                                       disabled data-row="{{$contador}}" onchange="validateAndCalculateTotal({{$contador}})" >
                            </td>
                        </tr>

                        @php $contador++; @endphp
                    @endforeach
                @endif

                </tbody>
                <tfoot>
                <tr>
                    <th colspan="7"></th>
                    <th class="right aligned" style="font-weight: bold;">Total</th>
                    <th class="right aligned" id="total-aprobado" style="font-weight: bold;">0,00</th>
                </tr>
                </tfoot>
            </table>

            <div id="error-message-total" class="ui red message force-hidden">
                <i class="exclamation triangle icon"></i>
                <strong>Error:</strong> El monto total aplicado de las notas de crédito no puede exceder el saldo disponible.
                {{--<span id="max-total-value">{{ number_format($activity->policy_sort_collection->total_amount ?? 0, 2, ',', '.') }}</span>--}}
            </div>

        </div>

    @endif

</div>

<style>
    .checkbox-container {
        display: flex;
        gap: 10px;
        justify-content: center;
        align-items: center;
    }
    .checkbox-item {
        display: flex;
        align-items: center;
        gap: 3px;
    }
    .error-input {
        border: 2px solid red !important;
        background-color: #ffe6e6 !important;
    }
    .total-exceeded {
        color: red;
        font-weight: bold;
    }
    /* SOLUCIÓN: Usar clases con !important para sobrescribir cualquier estilo inline */
    .force-hidden {
        display: none !important;
    }
    .force-visible {
        display: block !important;
    }
</style>

@if(!$premiumSurplusDetails->isNotEmpty())

    <script>
        let MAX_TOTAL = 0;
        let currentTotal = 0;

        configureInputMaskNegative('#available_amount_apply', '');

        // Función para manejar el cambio de checkboxes
        function handleCheckboxChange(rowIndex, type) {
            const input = document.getElementById(`amount_to_apply_${rowIndex}`);
            const saldoCell = input.closest('tr').querySelector('td[data-valor]').nextElementSibling;
            const saldoValue = parseFloat(saldoCell.getAttribute('data-valor')) || 0;

            if (type === 'T') {
                input.disabled = false;
                $(input).val(formatNumberDisplay(saldoValue));
                input.setAttribute('readonly', 'true');
                input.style.backgroundColor = '#f0f0f0';
            } else if (type === 'P') {
                input.disabled = false;
                input.removeAttribute('readonly');
                input.style.backgroundColor = '';
                $(input).val('0,00');

                // Agregar evento de cambio para validación
                $(input).off('input.validation').on('input.validation', function() {
                    validateAndCalculateTotal(rowIndex);
                });

                input.focus();
            }

            validateAndCalculateTotal(rowIndex);
        }

        // Función para validar y calcular el total
        function validateAndCalculateTotal(rowIndex) {
            const input = document.getElementById(`amount_to_apply_${rowIndex}`);
            let value = parseNumberFromInput($(input).val());

            // Validar que el valor sea mayor o igual a 0
            if (value < 0) {
                value = 0;
                $(input).val('0,00');
            }

            // Obtener el saldo máximo para esta fila
            const saldoCell = input.closest('tr').querySelector('td[data-valor]').nextElementSibling;
            const maxSaldo = parseFloat(saldoCell.getAttribute('data-valor')) || 0;

            // Validar que no supere el saldo disponible
            if (value > maxSaldo) {
                value = maxSaldo;
                $(input).val(formatNumberDisplay(0));

                Swal.fire({
                    icon: 'error',
                    title: 'Valor no valido',
                    html: `<div style="max-height: 300px; overflow-y: auto;">
                            El monto total aplicado de las notas de crédito no puede exceder el saldo disponible.
                            </div>`,
                    confirmButtonText: 'Aceptar',
                    confirmButtonColor: '#91c845'
                });

            }

            calculatePending();
            calculateTotal();
            handleValuePaidChange(document.getElementById('value_paid'));
        }

        // Función para calcular el total general
        function calculateTotal() {
            let total = 0;
            const inputs = document.querySelectorAll('input[id^="amount_to_apply_"]');

            inputs.forEach(input => {
                if (!input.disabled) {
                    const value = parseNumberFromInput($(input).val());
                    total += value;

                }
            });

            const totalElement = document.getElementById('total-aprobado');
            const errorElement = document.getElementById('error-message-total');

            // const maxTotalSpan = document.getElementById('max-total-value');
            // const availableAmountInput = document.getElementById('available_amount_apply');

            if (total > MAX_TOTAL) {
                totalElement.classList.add('total-exceeded');
                errorElement.classList.remove('force-hidden');
                errorElement.classList.add('force-visible');

                // if (maxTotalSpan && availableAmountInput) {
                //     maxTotalSpan.textContent = availableAmountInput.value;
                // }

                // Encontrar el último input modificado y ajustarlo
                const lastModifiedInput = document.activeElement;
                if (lastModifiedInput && lastModifiedInput.id.includes('amount_to_apply_')) {
                    const currentValue = parseNumberFromInput($(lastModifiedInput).val());
                    const excess = total - MAX_TOTAL;
                    const newValue = Math.max(0, currentValue - excess);
                    $(lastModifiedInput).val(formatNumberDisplay(0));
                    total = MAX_TOTAL;
                }
            } else {
                totalElement.classList.remove('total-exceeded');
                errorElement.classList.add('force-hidden');
                errorElement.classList.remove('force-visible');


            }

            document.getElementById('applied_credit_note_amount').value = total;
            totalElement.textContent = formatNumberDisplay(total);
            currentTotal = total;
        }

        // Función para formatear números para mostrar
        function formatNumberDisplay(number) {
            return new Intl.NumberFormat('es-ES', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            }).format(number);
        }

        // Función para convertir string formateado a número
        function parseNumberFromInput(str) {
            if (!str) return 0;
            // Remover puntos de miles y reemplazar coma decimal por punto
            return parseFloat(str.replace(/\./g, '').replace(',', '.')) || 0;
        }

        // Función para resetear una fila
        function resetRow(rowIndex) {
            const totalRadio = document.getElementById(`total_${rowIndex}`);
            const parcialRadio = document.getElementById(`parcial_${rowIndex}`);
            const input = document.getElementById(`amount_to_apply_${rowIndex}`);

            totalRadio.checked = false;
            parcialRadio.checked = false;
            input.disabled = true;
            $(input).val('0,00');
            input.removeAttribute('readonly');
            input.style.backgroundColor = '';

            calculateTotal();
        }

        // Inicialización
        $(document).ready(function() {
            // Aplicar máscara inicial a todos los inputs (deshabilitados)
            $('input[id^="amount_to_apply_"]').each(function() {
                configureInputMask('#' + this.id, '');
            });

            // Calcular total inicial
            calculateTotal();
            calculatePending();
        });

        function calculatePending(){

            const totalAmount = @json($activity->policy_sort_collection->total_amount ?? 0);

            let valuePaidInput = document.getElementById('value_paid').value;

            const availableAmountApply = document.getElementById('available_amount_apply');

            let value = valuePaidInput.replace(/\./g, '').replace(',', '.'); // convertir formato a número válido
            let numericValue = parseFloat(value);

            if (!isNaN(numericValue)) {
                availableAmountApply.value = (totalAmount - numericValue);
                MAX_TOTAL = (totalAmount - numericValue);
            } else {
                availableAmountApply.value = 0;
            }

        }
    </script>

@endif

<script>
    $(document).ready(function() {
        $('#btn-eliminar-notas').on('click', function() {

            Swal.fire({
                icon: 'warning',
                title: '¿Estás seguro?',
                text: 'Eliminara las notas credito aplicadas a este cobro. ¿Desea continuar?',
                showCancelButton: true,
                confirmButtonColor: '#000000',
                cancelButtonColor: '#91C845',
                confirmButtonText: 'Sí, eliminar',
                cancelButtonText: 'Cancelar'
            }).then((result) => {
                if (result.isConfirmed) {

                    loadingMain(true);

                    $.ajax({
                        url: `/policy_sort_collection/delete_credit_notes/{{$activity->policy_sort_collection->id}}`,
                        method: 'GET',
                        data: {
                            charge_id: {{$activity->policy_sort_collection->id}}
                        },
                        success: function(response) {
                            loadingMain(false);
                            if (response.status === 200) {
                                Swal.fire({
                                    icon: 'success',
                                    title: 'Eliminado!',
                                    text: 'Registros eliminados correctamente.',
                                    confirmButtonColor: '#91C845'
                                }).then(() => {
                                    window.location.href =
                                        '{{ secure_url('/servicio/'.$activity->id.'/policy_sort_collection') }}';
                                });
                            } else {
                                Swal.fire({
                                    icon: 'error',
                                    title: 'Error',
                                    text: 'No se pudo eliminar.',
                                    confirmButtonColor: '#91C845'
                                });
                            }
                        },
                        error: function(response) {
                            loadingMain(false);
                            Swal.fire({
                                icon: "error",
                                title: "Error",
                                text: 'No es posible eliminar.',
                            });
                        },
                        complete: function() {
                            loadingMain(false);
                        }
                    });
                }
            });

        });
    });
</script>