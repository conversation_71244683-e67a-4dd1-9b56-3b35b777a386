<!DOCTYPE html>
<html lang="es">

<head>
    <meta charset="UTF-8">
    <title>Recibo de Prima</title>
    <style>
        body {
            font-family: "Courier New";
            font-size: 12px;
            color: #000;
            display: flex;
            /*justify-content: center;*/
            margin: 0;
            /*background-color: #f4f4f4;*/
            /*padding-top: 2cm; !* Agregar margen superior de 2 cm *!*/
        }

        .container {
            /*width: 80%;*/
            /*max-width: 800px;*/
            border: 1px solid #000;
            padding: 0px;
            /*background-color: #fff;*/
            padding-bottom: 100px;

        }

        /* Encabezado */
        .header {
            display: flex;
            justify-content: space-between;
            border-bottom: 1px solid #000;
            margin-bottom: 20px;
        }

        .header-section {
            width: 33.33%;
            text-align: center;
            padding: 10px;
            border: 1px solid #000;
            box-sizing: border-box;
            /* Para que el borde no desplace el contenido */
        }

        .header img {
            width: 80px;
        }

        .title {
            font-weight: bold;
            font-size: 14px;
        }

        .relation {
            font-size: 12px;
            font-weight: bold;
            line-height: 1.5;
        }

        /* Estilos generales */
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 0px;
        }

        .table th,
        .table td {
            border: 1px solid #000;
            padding: 5px;
            text-align: center;
        }

        .table-inner {
            width: 100%;
            border-collapse: collapse;
        }

        .table-inner th,
        .table-inner td {
            border: none;
            padding: 3px;
        }

        .divider {
            border-right: 1px solid #000;
        }

        .right {
            text-align: right;
        }

        .left {
            text-align: left;
        }

        .highlight {
            color: #FF0000;
            font-weight: bold;
        }

        .section-title {
            font-weight: bold;
            font-size: 13px;
            text-align: center;
            border: 1px solid #000;
            padding: 5px;
            margin-top: 15px;
            background-color: #e8ebed;
        }

        .section-title-td {
            font-weight: bold;
            font-size: 13px;
            text-align: center;
            background-color: #e3e3e3;
        }

        .info-table td {
            padding: 2px 5px;
            font-size: 12px;
        }

        .amount {
            font-weight: bold;
            color: #0000FF;
        }

        .footer {
            position: fixed;
            bottom: 0;
            left: 0;
            width: 100%;
            text-align: center;
            background-color: #ffffff;
            z-index: 100;
            height: 80px;
        }
    </style>
</head>

<body>

    <footer class="footer">
        <img src="{{ public_path('images/mnk_footer.png') }}" alt="pie de documento" style="width: 100%;">
    </footer>

    <div class="container">
        <table class="table info-table">

            <thead>
                <tr>
                    <td class="left" colspan="1">
                        <img src="{{ public_path('images/mnk.png') }}" alt="Logo" style="width: 80px;">
                    </td>
                    <td colspan="4" class="title" style="text-align: center;">
                        <div style="font-weight: bold; font-size: 14px;">RECIBO DE PRIMA</div>
                        <div>Modalidad de aseguramiento:</div>
                        <div style="font-weight: normal;">{{ $WORK_MODALITY[$work_modality_id] ?? 'Modalidad desconocida' }} </div>
                    </td>
                    <td class="relation" colspan="4" style="text-align: center;">
                        <div>ORIGINAL</div>
                        <div>Relación de Ingreso</div>
                        <div class="highlight">N°
                            {{ sprintf('%010d', $activity_policy_collection->policy_sort_collection->id ?? 0) }} </div>
                    </td>
                </tr>
            </thead>

            <tbody>
                <tr>
                    <td class="left">Recibí de:</td>
                    <td colspan="3" class="left">
                        {{ ucwords(mb_strtolower($activity_policy->affiliate->first_name ?? '')) }}</td>
                    <td>Fecha:</td>
                    <td colspan="2">{{ $fechaCostarrica }}</td>
                    <td>Hora:</td>
                    <td>{{ $horaCostarrica ?? '' }}</td>
                </tr>
                <tr>
                    <td colspan="1" class="left">Tomador:</td>
                    <td colspan="8" class="left">
                        {{ ucwords(mb_strtolower($activity_policy->affiliate->first_name ?? '')) }}</td>
                </tr>
                <tr>
                    <td colspan="1" class="left">La suma de:</td>
                    <td colspan="8" class="left">{{ $totalWords ?? '' }}</td>
                </tr>
                <tr>
                    <td colspan="1" class="left">Tipo de recibo:</td>
                    <td colspan="8" class="left">{{ $TYPE_RECEIPT[$type_receipt]?? '' }}</td>
                </tr>
                <tr>
                    <td colspan="6">Referencia</td>
                    <td colspan="1">Moneda</td>
                    <td colspan="2">Monto</td>
                </tr>
                <tr>
                    <td colspan="6">{{ $type_payment ?? 'COBRO ABONO PÓLIZA SORT' }}</td>
                    <td colspan="1">
                        {{ isset($activity_policy->policy_sort->type_currency) ? ($activity_policy->policy_sort->type_currency == 'USD' ? 'DO' : 'CRC') : '' }}
                    </td>
                    <td colspan="2">@include('colones', ['currency' => $activity_policy->policy_sort->type_currency])
                        {{ number_format($base ?? 0, 2, ',', '.') }}
                    </td>
                </tr>
                @if (optional($quotationCondition)->clap > 0)
                    <tr>
                        <td colspan="6"> CONSULTORIO LABORAL ATENCIÓN PRIMARIA (CLAP) </td>
                        <td colspan="1" class="section-title-td">
                            {{ number_format($quotationCondition->clap ?? 0, 2, ',', '.') }}%</td>
                        <td colspan="2" class="section-title-td">
                            @include('colones', [
                                'currency' => $activity_policy->policy_sort->type_currency,
                            ]) {{ number_format($descuentoClap ?? 0, 2, ',', '.') }}</td>
                    </tr>
                @endif
                @if (optional($quotationCondition)->cmc > 0)
                    <tr>
                        <td colspan="6"> RECONOCIMIENTO POR CUMPLIMIENTO Y MEJORA CONTINUA (CMC) </td>
                        <td colspan="1" class="section-title-td">
                            {{ number_format($quotationCondition->cmc ?? 0, 2, ',', '.') }}%</td>
                        <td colspan="2" class="section-title-td"> @include('colones', [
                            'currency' => $activity_policy->policy_sort->type_currency,
                        ])
                            {{ number_format($descuentoCml ?? 0, 2, ',', '.') }}
                        </td>
                    </tr>
                @endif
                <tr>
                    <td colspan="6"></td>
                    <td colspan="1" class="section-title-td">Total</td>
                    <td colspan="2" class="section-title-td">@include('colones', ['currency' => $activity_policy->policy_sort->type_currency])
                        {{ number_format($final ?? 0, 2, ',', '.') }}
                    </td>
                </tr>
                <tr>
                    <td colspan="9" class="section-title-td">Facturas canceladas</td>
                </tr>
                <tr>
                    <td colspan="1">Póliza</td>
                    <td colspan="3">Intermediario</td>
                    <td colspan="2">
                        <table class="table-inner">
                            <tr>
                                <td colspan="2">Vigencia de Factura</td>
                            </tr>
                            <tr>
                                <td class="divider">Desde</td>
                                <td>Hasta</td>
                            </tr>
                        </table>
                    </td>
                    <td colspan="1">Moneda</td>
                    <td colspan="2">Monto</td>
                </tr>

                <tr>
                    <td colspan="1">
                        {{ $activity_policy->policy_sort->consecutive ? $activity_policy->policy_sort->formatNumberConsecutive() : '' }}
                    </td>
                    <td colspan="3">{{ $activity_policy->policy_sort->brokerage_name ?? '' }}</td>
                    <td colspan="1">{{ $date_from ?? '' }}</td>
                    <td colspan="1">{{ $date_to ?? '' }}</td>
                    <td colspan="1">
                        {{ isset($activity_policy->policy_sort->type_currency) ? ($activity_policy->policy_sort->type_currency == 'USD' ? 'DO' : 'CRC') : '' }}
                    </td>
                    <td colspan="2">@include('colones', ['currency' => $activity_policy->policy_sort->type_currency])
                        {{ number_format($base ?? 0, 2, ',', '.') }}
                    </td>
                </tr>
                @if ($quotationCondition)
                    <tr>
                        <td colspan="6"></td>
                        <td colspan="1" class="section-title-td">
                            {{ $quotationCondition ? number_format($sumaDescuentos ?? 0, 2, ',', '.') . '%' : '' }}
                        </td>
                        <td colspan="2" class="section-title-td">
                            @include('colones', [
                                'currency' => $activity_policy->policy_sort->type_currency,
                            ])
                            {{ number_format($descuentos_total ?? 0, 2, ',', '.') }}
                        </td>
                    </tr>
                @endif

                <tr>
                    <td colspan="6"></td>
                    <td colspan="1" class="section-title-td">Total</td>
                    <td colspan="2" class="section-title-td">@include('colones', ['currency' => $activity_policy->policy_sort->type_currency])
                        {{ number_format($final ?? 0, 2, ',', '.') }}
                    </td>
                </tr>
            </tbody>
        </table>

        @if (isset($pagado) && $pagado)
            <table style="border-collapse: collapse; width: 100%; border: none;">
                <tr>
                    <td colspan="6" style="text-align: center; border: none;"></td>
                    <td colspan="1" style="text-align: center; border: none;">
                        <img src="{{ public_path('images/mnk.png') }}" alt="aprobado" style="width: 130px;">
                        <div style="font-weight: bold; font-size: 18px;  margin: 0; line-height: 0; ">PAGADO</div>
                    </td>
                    <td colspan="2" style="text-align: center; border: none;"></td>
                </tr>
            </table>
        @else
            <br><br><br>
        @endif

    </div>
</body>

</html>
