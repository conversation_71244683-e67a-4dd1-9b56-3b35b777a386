<!DOCTYPE html>
<html lang="es">

<head>
    <meta content="text/html; charset=utf-8" http-equiv="Content-Type" />
    <style type="text/css">
        * {
            font-family: 'Arial', sans-serif;
            font-size: 8pt;
        }

        body {
            margin: 0 1rem;
            text-align: justify;
        }

        .watermark {
            position: fixed;
            top: 45%;
            width: 100%;
            text-align: center;
            opacity: .1;
            transform: rotate(-45deg);
            transform-origin: 50% 50%;
            z-index: 1000;
        }


        .header {
            padding: 0 15px;
            text-align: center;
            position: fixed;
            top: 0px;
            left: 0;
            right: 0;
        }

        .header img {
            min-height: 50px;
            max-height: 50px;
            width: auto;
        }
        .numpage:after {
            content: counter(page);
        }

        table,
        th,
        td {
            border: 0.5px solid black;
        }

        table.no-border,
        table.no-border th,
        table.no-border td,
        table.no-border td b {
            font-size: 7pt;
            border: none;
            text-align: center;
            max-width: 0;
        }

        th,
        td {
            padding: 1.5px;
            padding-bottom: 3px;
        }

        td[colspan=16] {
            text-align: justify;
        }

        table {
            border-collapse: collapse;
            width: 100%;
        }

        th {
            text-align: center;
            background: #BDf;
        }
    </style>
</head>

<body>
@if($watermark)
    <div class="watermark">
        <span style="font-size: 72pt;font-weight: 500;">VISTA PREVIA</span>
    </div>
@endif
<div class="header">
    <script type="text/php">
            if (isset($pdf)) {
                $x = $pdf->get_width() - 90;
                $y = 40;
                $text = "Pág {PAGE_NUM} de {PAGE_COUNT}";
                $font = 'Arial';
                $size = 8;
                $color = array(0,0,0);
                $word_space = 0.0;  //  default
                $char_space = 0.0;  //  default
                $angle = 0.0;   //  default
                $pdf->page_text($x, $y, $text, $font, $size, $color, $word_space, $char_space, $angle);
            }
        </script>
</div>
<div>
    <p>
        <b>DATOS DE IDENTIFICACIÓN</b>
    <table>
        <tr>
            <td>NOMBRE:</td>
            <td>{{$activity->affiliate->full_name}}</td>
        </tr>
        <tr>
            <td>CEDULA DE CIUDADANIA:</td>
            <td>{{$activity->affiliate->doc_number}}</td>
        </tr>
        <tr>
            <td>CIUDAD:</td>
            <td>{{$activity->affiliate->city}}</td>
        </tr>
        <tr>
            <td>ENTIDAD CALIFICADORA <br />EN PRIMERA OPORTUNIDAD:</td>
            <td>{{'COMITÉ INTERDISCIPLINARIO DE CALIFICACIÓN - MAPFRE COLOMBIA VIDA SEGUROS S.A.'}}</td>
        </tr>
        <tr>
            <td>FECHA:</td>
            <td>{{Carbon\Carbon::createFromFormat('Y-m-d', App\Correspondence::nextDay())->formatLocalized('%B %d, %Y')}}</td>
        </tr>
    </table>
    <br />
    </p>
</div>
</body>
</html>
