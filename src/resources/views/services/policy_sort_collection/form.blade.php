@extends('layouts.main')

@section('title', 'Poliza SORT')

@section('menu')
    @parent
@endsection

@section('content')

        <div class="ui basic segment">
            <h1 class="ui header">
                Cobros - póliza SORT
            </h1>

            <div class="ui secondary segment">
                <div class="ui grid">
                    <div class="three column row">
                        <div class="column left aligned">
                            <b>Identificación:</b> {{ $activity->affiliate->doc_type }} {{ $activity->affiliate->doc_number }}
                        </div>
                        <div class="column center aligned">
                            <b>Nombre:</b> <a
                                href="{{ secure_url('afiliado/' . $activity->affiliate_id) }}">{{ ucwords(strtolower($activity->affiliate->full_name)) }}</a>
                        </div>
                        <div class="column right aligned">
                            <b>Actividad:</b> <a
                                href="{{ secure_url('servicio/' . $activity->id) }}">{{ $activity->service->name }}</a>
                        </div>
                    </div>
                </div>
            </div>
            @php
    $tipoRecibo = $TYPE_RECEIPT[$activity->policy_sort_collection->type_receipt] ?? 'Tipo no especificado';
            @endphp
            <form id="policy-collection-sort" enctype="multipart/form-data"
                action="{{ secure_url("servicio/{$id}/policy_sort_collection/save") }}" class="ui attached form" method="POST">

                {{csrf_field()}}

                <input type="hidden" name="payment_method" id="payment_method"
                    value="{{$activity->policy_sort_collection->payment_method ?? ''}}">

                <div class="ui styled fluid accordion">
                    <!-- Sección: Título del recibo -->
                    <div class="title"><i class="dropdown icon"></i> {{$tipoRecibo}} </div>
                    <div class="content">
                        <div class="accordion transition">
                            <!-- Sección: Datos del recibo -->
                            <div class="title"><i class="dropdown icon"></i> Datos del recibo</div>
                            <div class="content">
                                <div class="ui form small clearing transition hidden">
                                    <div class="ui form">
                                        <div class="fields">
                                            <!-- Primer input -->
                                            <div class=" four wide field">
                                                <label>Póliza SORT</label>
                                                <input type="text" name="" placeholder=""
                                                    value="{{isset($activity_policy->policy_sort->id) ? $activity_policy->policy_sort->formatNumberConsecutive() : ''}}"
                                                    class="readonly" readonly>
                                            </div>
                                            <!-- Segundo input -->
                                            <div class="four wide field">
                                                <label># de recibo </label>
                                                <input type="text" name="" placeholder=""
                                                    value="{{$activity->policy_sort_collection->id}}" class="readonly" readonly>
                                            </div>

                                            <!-- Tercer input -->
                                            <div class="four wide field">
                                                <label>Fecha de emisión del recibo</label>
                                                <input type="email" name="" placeholder="Correo"
                                                    value="{{ isset($activity->policy_sort_collection->created_at) ? ucfirst(strftime('%A %e de %B del %Y', strtotime($activity->policy_sort_collection->created_at))) : '' }}"
                                                    id="created_at" class="readonly" readonly>
                                            </div>

                                            <!-- Cuarto input -->
                                            <div class="four wide field">
                                                <label>Fecha limite de pago</label>
                                                <input type="tel" name="" placeholder=""
                                                    value="{{ isset($activity->policy_sort_collection->due_date) ? ucfirst(strftime('%A %e de %B del %Y', strtotime($activity->policy_sort_collection->due_date))) : '' }}"
                                                    id="due_date" class="readonly" readonly>
                                            </div>
                                        </div>

                                        <div class="fields">
                                            <div class="four wide field">
                                                <label>Tipo de recibo</label>
                                                <input type="tel" name="" placeholder="" value="{{$tipoRecibo}}"
                                                    class="readonly" readonly>
                                            </div>
                                            <div class="four wide field">
                                                <label>Tipo de moneda</label>
                                                <input type="tel" name="" placeholder="" value="{{ isset($activity_policy->policy_sort) && isset($MONEY_TYPE[$activity_policy->policy_sort->type_currency])
        ? $MONEY_TYPE[$activity_policy->policy_sort->type_currency]['symbol'] . ' ' . ucfirst(mb_strtolower($MONEY_TYPE[$activity_policy->policy_sort->type_currency]['name']))
        : '' }}" class="readonly" readonly>
                                            </div>

                                            <div class="four wide field">
                                                <label>Valor prima</label>
                                                @if ($activity_policy->policy_sort->temporality == 'permanent')
                                                    <input name="premiumValue" readonly type="text"
                                                        value="{{ $calculatedAmount ? ($activity_policy->policy_sort->type_currency === 'USD' ? '$' : '₡') . ' ' . number_format($calculatedAmount, 2, ',', '.') : 'N/a' }}"
                                                        class="readonly">
                                                @else
                                                    <input name="premiumValue" readonly type="text"
                                                        value="{{ $activity_policy->policy_sort->single_payment_value ? ($activity_policy->policy_sort->type_currency === 'USD' ? '$' : '₡') . ' ' . number_format($activity_policy->policy_sort->single_payment_value, 2, ',', '.') : 'N/a' }}"
                                                        class="readonly">
                                                @endif

                                            </div>
                                            <div class="four wide field">
                                                <label>Forma de pago</label>
                                                <input type="tel" name="" placeholder=""
                                                    value="{{isset($activity_policy->policy_sort->periodicity) && isset($PERIODICITYT[$activity_policy->policy_sort->periodicity]) ? ucfirst(strtolower($PERIODICITYT[$activity_policy->policy_sort->periodicity])) : 'N/A'}}"
                                                    class="readonly" readonly>
                                            </div>
                                        </div>
                                        <div class="fields">
                                            <div class="four wide field">
                                                <label># abonos acumulados</label>
                                                <input type="tel" name="" placeholder="" value="{{$totalAmountCount}}"
                                                    class="readonly" readonly>
                                            </div>
                                            <div class="four wide field">
                                                <label>Valor abono</label>
                                                <input type="tel" name="" placeholder=""
                                                    value="{{ $activity && isset($activity_policy->policy_sort) ? ($activity_policy->policy_sort->type_currency === 'USD' ? '$' : '₡') . ' ' . number_format($activity->policy_sort_collection->total_amount, 2, ',', '.') : 'N/a' }}"
                                                    class="readonly" readonly>
                                            </div>
                                            <div class="four wide field">
                                                <label>TRM</label>
                                                <input type="tel" name="" placeholder=""
                                                    value="{{ number_format($trm ?? 0, 2, ',', '.') }}" class="readonly"
                                                    readonly>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Sección: Datos del Tomador -->
                            <div class="title">
                                <i class="dropdown icon"></i> Datos tomador
                            </div>
                            <div class="content">
                                <div class="ui form small clearing transition hidden">
                                    <div class="fields">
                                        <div class="six wide field">
                                            <label>Tipo de identificación</label>
                                            <input type="tel" name="" placeholder=""
                                                value="{{$activity->affiliate->doc_type ? $DOC_TYPES[$activity->affiliate->doc_type] : ''}}"
                                                class="readonly" readonly>
                                        </div>
                                        <div class="six wide field">
                                            <label># identificación</label>
                                            <input type="tel" name="" placeholder=""
                                                value="{{$activity->affiliate->doc_number}}" class="readonly" readonly>
                                        </div>
                                        <div class="six wide field">
                                            <label>Nombre</label>
                                            <input type="tel" name="" placeholder=""
                                                value="{{ucwords(mb_strtolower($activity->affiliate->full_name))}}"
                                                class="readonly" readonly>
                                        </div>
                                    </div>
                                    <div class="fields">
                                        <div class="six wide field">
                                            <label>Teléfono</label>
                                            <input type="tel" name="" placeholder="" value="{{$activity->affiliate->phone}}"
                                                class="readonly" readonly>
                                        </div>
                                        <div class="six wide field">
                                            <label>Email para facturación electronica</label>
                                            <input type="tel" name="" placeholder=""
                                                value="{{ isset($activity_policy->affiliate) ? $activity_policy->affiliate->electronic_billing_email : ''}}"
                                                class="readonly" readonly>
                                        </div>
                                        <div class="six wide field">
                                            <label>Email de notificaciones</label>
                                            <input type="tel" name="" placeholder=""
                                                value="{{isset($activity_policy->policy_sort) ? $activity_policy->policy_sort->notification_email : ''}}"
                                                class="readonly" readonly>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- notas credito -->
                            @if(($activity->policy_sort_collection->payment_status == 'approved' && $activity->policy_sort_collection->applied_credit_note_amount != 0) ||
                                ($activity->policy_sort_collection->payment_status == 'pending' || $activity->policy_sort_collection->payment_status == 'pending-approval'))
                                @include('services.policy_sort_collection.form-credit-notes')
                            @endif

                            <!-- Sección: Datos del Pago -->
                            <div class="title">
                                <i class="dropdown icon"></i> Datos del pago
                            </div>
                            <div class="content">
                                <div class="ui form small clearing transition hidden">
                                    <div class="field" id="pt_field">
                                        <label>¿Habilitar pago por tarjeta?</label>
                                        <div style="display: flex; gap: 40px; padding:20px;">
                                            <div class="ui toggle checkbox">
                                                <input type="radio" name="has_card_payment" value="1" {{ $activity->policy_sort_collection->has_card_payment == '1' ? 'checked' : '' }} 
                                                {{ isset($activity->policy_sort_collection->payment_method) && $activity->policy_sort_collection->payment_method == 'PT' ? 'disabled' : '' }}>
                                                <label>Si</label>
                                            </div>
                                            <div class="ui toggle checkbox">
                                                <input type="radio" name="has_card_payment" value="0" {{ $activity->policy_sort_collection->has_card_payment == '0' ? 'checked' : '' }} 
                                                {{ isset($activity->policy_sort_collection->payment_method) && $activity->policy_sort_collection->payment_method == 'PT' ? 'disabled' : '' }}>
                                                <label>No</label>
                                            </div>
                                            <div class="ui small inline loader disabled" id="pt_loader"></div>
                                        </div>

                                        <div class="ui message blue" id="pt_message">
                                            <i class="close icon"></i>
                                            <div class="header">
                                                Pago tarjeta habilitado
                                            </div>
                                        </div>
                                    </div>
                                    <div class="fields">
                                        <div class="six wide field">
                                            <label>Estado del pago </label>
                                            <input type="text" name="" placeholder=""
                                                value="{{isset($activity_policy->policy_sort) ? $RECEIPT_STATE[$activity->policy_sort_collection->payment_status] ?? 'N/A' : ''}}"
                                                class="readonly" readonly>
                                        </div>
                                        <div class="six wide required field">
                                            <label>Fecha de pago</label>
                                            <input type="text" name="transaction_date" id="transaction_date"
                                                value="{{ isset($activity->policy_sort_collection->transaction_date) ? ucfirst(strftime('%A %e de %B del %Y', strtotime($activity->policy_sort_collection->transaction_date))) : '' }}"
                                                class="datepicker">
                                        </div>
                                        <div class="six wide required field">
                                            <label>Hora del pago</label>
                                            <input type="time" name="time_transaction" id="time_transaction"
                                                value="{{$activity->policy_sort_collection->time_transaction ?? ''}}">
                                        </div>
                                        <div class="six wide field">
                                            <label>Id de la transaccion</label>
                                            <input type="text" name="transaction_id" id="transaction_id"
                                                value="{{isset($activity_policy->policy_sort) ? $activity->policy_sort_collection->transaction_id : ''}}">
                                        </div>
                                    </div>
                                    <div class="fields">

                                        <div class="four wide field">
                                            <label>TRM</label>
                                            <input type="tel" name="trm" id="trm" placeholder=""
                                                value="{{ number_format($activity->policy_sort_collection->trm ?? 0, 2, ',', '.') }}"
                                                class="readonly" readonly>
                                        </div>

                                        @if(isset($activity->policy_sort_collection->payment_method) && ($activity->policy_sort_collection->payment_method == 'TB' || $activity->policy_sort_collection->payment_method == 'PT'))

                                            <div class="four wide required field">
                                                <label>Banco del depósito:</label>
                                                <div class="ui search selection search normal dropdown" id="bank_account_dropdown">
                                                    <input type="hidden" name="bank_account" id="bank_account">
                                                    <i class="dropdown icon"></i>
                                                    <div class="default text">Seleccione uno</div>
                                                    <div class="menu">
                                                        <div class="item" data-value="">Seleccione uno</div>
                                                        @foreach($banks as $bank)
                                                            <div class="item" data-value="{{ $bank->id }}">{{ $bank->name }}</div>
                                                        @endforeach
                                                    </div>
                                                </div>

                                            </div>

                                            <div class="four wide required field">
                                                <label>Número de cuenta del depósito:</label>
                                                <div class="ui search selection search normal dropdown"
                                                    id="account_details_dropdown">
                                                    <input type="hidden" name="account_details" id="account_details">
                                                    <i class="dropdown icon"></i>
                                                    <div class="default text">Seleccione uno</div>
                                                    <div class="menu" id="account_details_menu">
                                                        @foreach($banks as $bank)
                                                            @if($activity->policy_sort_collection->bank_id == $bank->id)
                                                                @foreach($bank->accounts as $account)

                                                                    <div class="item" data-value="{{ $account->id }}">
                                                                        {{ \App\Providers\AppServiceProvider::$TYPE_ACCOUNT[$account->type] ?? '' }}
                                                                        : {{ $account->account_number }}
                                                                    </div>

                                                                @endforeach
                                                            @endif
                                                        @endforeach
                                                    </div>
                                                </div>
                                            </div>


                                        @endif

                                        <div class="four wide field">
                                            @if(isset($activity->policy_sort_collection->invoice_tb))
                                                <label>Comprobante de transferencia bancaria</label>
                                                <a target="_blank"
                                                    href="{{ Storage::disk('s3')->url($activity->policy_sort_collection->invoice_tb ?? '') }}"
                                                    class="ui primary icon button comprobante">
                                                    <i class="download icon"></i>
                                                </a>
                                                @if(isset($activity->policy_sort_collection->additional_invoice_tb))
                                                    <a target="_blank"
                                                        href="{{ Storage::disk('s3')->url($activity->policy_sort_collection->additional_invoice_tb ?? '') }}"
                                                        class="ui primary icon button comprobante_adicional">
                                                        <i class="download icon"></i>
                                                    </a>
                                                @endif
                                            @endif
                                        </div>

                                    </div>
                                    <div class="four  fields">
                                        <div class="required field">
                                            <label>Moneda del pago</label>
                                            <div class="ui fluid selection dropdown">
                                                <input type="hidden" name="payment_currency" id="payment_currency"
                                                    value="{{ isset($activity->policy_sort_collection->payment_currency) ? $activity->policy_sort_collection->payment_currency : (isset($activity_policy->policy_sort->type_currency) ? $activity_policy->policy_sort->type_currency : '') }}">
                                                <i class="dropdown icon"></i>
                                                <div class="default text">Seleccionar moneda</div>
                                                <div class="menu">
                                                    @foreach($MONEY_TYPE as $key => $money)
                                                        <div class="item" data-value="{{ $key }}">
                                                            {{ $money['symbol'] . ' ' . ucfirst(mb_strtolower($money['name'])) }}
                                                        </div>
                                                    @endforeach
                                                </div>
                                            </div>
                                        </div>


                                        <div class="required field">
                                            <label>Valor pagado</label>
                                            @if($activity->policy_sort_collection->payment_method == 'TC' && $activity->policy_sort_collection->payment_status == 'approved')
                                                <input type="text" name="value_paid" id="value_paid"
                                                    value="{{ number_format($activity->policy_sort_collection->total_amount ?? 0, 2, ',', '.') }}"
                                                    class="readonly" readonly>
                                            @elseif($activity->policy_sort_collection->payment_method == 'TC')
                                                <input type="text" name="value_paid" id="value_paid"
                                                    value="{{ number_format($activity->policy_sort_collection->value_paid ?? 0, 2, ',', '.') }}"
                                                    class="readonly" readonly>
                                            @else
                                                <input type="text" name="value_paid" id="value_paid"
                                                    value="{{ number_format($activity->policy_sort_collection->value_paid ?? 0, 2, ',', '.') }}"
                                                    @if($activity->policy_sort_collection->payment_status == 'approved') class="readonly" readonly @else oninput="handleValuePaidChange(this)" @endif>
                                            @endif

                                        </div>

                                        <div class="four wide field">
                                            <label>Aplicación NC</label>
                                            <input type="text" name="applied_credit_note_amount" id="applied_credit_note_amount" placeholder="" class="readonly" readonly
                                                   value="{{ number_format($activity->policy_sort_collection->applied_credit_note_amount ?? 0, 2, ',', '.') }}" >
                                        </div>

                                        <div class="four wide field">
                                            <label>Diferencia del pago</label>
                                            <input type="text" name="payment_difference" id="payment_difference" class="readonly" placeholder="" readonly
                                                   value="{{ number_format($activity->policy_sort_collection->payment_difference ?? 0, 2, ',', '.') }}" >
                                        </div>


                                    @if(isset($activity->policy_sort_collection->payment_method) && $activity->policy_sort_collection->payment_method == 'PT')
                                            <div class="four wide required field">
                                                <label>Tarjeta del pago:</label>
                                                <div class="ui search selection search normal dropdown" id="bank_id_pt_dropdown">
                                                    <input type="hidden" name="bank_id_pt" id="bank_id_pt">
                                                    <i class="dropdown icon"></i>
                                                    <div class="default text">Seleccione uno</div>
                                                    <div class="menu">
                                                        <div class="item" data-value="">Seleccione uno</div>
                                                        @foreach($banks as $bank)
                                                            {{-- 2 (Banco Nacional) y 5 (Banco Promerica) son el id de banks en db --}} 
                                                            @if (in_array($bank->id, [2, 5]))
                                                            <div class="item" data-value="{{ $bank->id }}">{{ $bank->name }}</div>
                                                            @endif
                                                        @endforeach
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="four wide field">
                                                @if(isset($activity->policy_sort_collection->invoice_pt))
                                                    <label>Formulario de cargue único</label>
                                                    <a target="_blank" href="{{ Storage::disk('s3')->url($activity->policy_sort_collection->invoice_pt ?? '') }}"
                                                        class="ui primary icon button">
                                                        <i class="download icon"></i>
                                                    </a>
                                                @endif
                                            </div>
                                        @endif

                                    </div>
                                </div>
                            </div>



                        </div>
                    </div>
                </div>


                <div class="ui basic segment">
                    <div class="ui error message hidden"></div>
                    <div class="fields">
                        <div class="six wide field">
                            <button class="ui primary button" onclick="" style="width: 40%">
                                <i class="save icon"></i> Guardar
                            </button>
                            <a href="{{ secure_url('/servicio/' . $activity->id) }}" class="ui secondary button"><i
                                    class="arrow left icon"></i> Volver a la actividad
                            </a>
                        </div>

                    </div>
                </div>
            </form>

            @if (\Session::has('success'))
                <div class="ui green message" id="success-message">
                    <i class="close icon" id="close-success-message"></i>
                    <div class="header">
                        {!! \Session::get('success') !!}
                    </div>
                </div>
            @endif
            @if (\Session::has('info'))
                <div class="ui yellow message" id="info-message">
                    <i class="close icon" id="close-info-message"></i>
                    <div class="header">
                        {!! \Session::get('info') !!}
                    </div>
                </div>
            @endif
            @if (\Session::has('error'))
                @if (\Session::has('error'))
                    <div class="ui negative message">
                        <i class="close icon"></i>
                        <div class="header">
                            {!! \Session::get('error') !!}
                        </div>
                    </div>
                @endif
            @endif

        </div>


        <style>
            .ui.grid .column {
                padding: 0.5rem 1rem !important;
            }

            .readonly {
                background: rgba(0, 0, 0, .05) !important;
            }
        </style>
        {{-- Hace petición para habilitar o deshabilitar el pago tarjeta --}}
        <script>
            const TOTAL_AMOUNT = @json($activity->policy_sort_collection->total_amount ?? 0);
            configureInputMaskNegative('#payment_difference', '');
            configureInputMask('#applied_credit_note_amount', '');


            $('#pt_message')
                .on('click', function() {
                    $(this).closest('#pt_message').transition('fade');
                });

            $(document).ready(function () {
                $('#pt_message').addClass('hidden');
                $('input[name="has_card_payment"]').on('change', function () {
                    let selectedValue = $(this).val();

                    $('#pt_loader').removeClass('disabled').addClass('active');
                    $('#pt_field').addClass('disabled');
                    

                    $.ajax({
                        url: "{{ secure_url("servicio/{$id}/policy_sort_collection/save-has-card-payment") }}",
                        method: 'POST',
                        data: {
                            has_card_payment: selectedValue
                        },
                        success: function (response) {
                            if (selectedValue == 1) {
                                $('#pt_message .header').text('Pago tarjeta habilitado');
                                $('#pt_message').removeClass('hidden');
                                $('#pt_message').addClass('visible');
                            } else {
                                $('#pt_message .header').text('Pago tarjeta deshabilitado');
                                $('#pt_message').removeClass('hidden');
                                $('#pt_message').addClass('visible');
                            }
                            $('#pt_loader').removeClass('active').addClass('disabled');
                            $('#pt_field').removeClass('disabled');
                        },
                        error: function (xhr, status, error) {
                            $('#pt_message').addClass('hidden');
                            $('#pt_loader').removeClass('active').addClass('disabled');
                            $('#pt_field').removeClass('disabled');
                        }
                    });
                });

                const valuePaidInput = document.getElementById('value_paid');
                if (!valuePaidInput.readOnly) {
                    handleValuePaidChange(valuePaidInput);
                }


            });

            function handleValuePaidChange(element) {
                let valuePaidInput = element.value;
                const paymentDifferenceInput = document.getElementById('payment_difference');
                const availableAmountApply = document.getElementById('available_amount_apply');

                const elementApplied = document.getElementById('applied_credit_note_amount');
                let valieNc = 0;
                if (elementApplied && elementApplied.value) {
                    let appliedCreditNoteAmount = elementApplied.value;
                    valieNc = parseFloat(appliedCreditNoteAmount.replace(/\./g, '').replace(',', '.'));
                }

                let value = valuePaidInput.replace(/\./g, '').replace(',', '.'); // convertir formato a número válido

                let numericValue = parseFloat(value);

                if (!isNaN(numericValue)) {

                    let difference = (numericValue+valieNc) - TOTAL_AMOUNT;
                    //calcula el valor disponible para usar en la nota credito
                    if (availableAmountApply) {
                        availableAmountApply.value = difference;
                    }
                    //calcula direncia
                    paymentDifferenceInput.value =  difference;

                } else {
                    paymentDifferenceInput.value = '';
                }

            }

        </script>
        <script type="text/javascript">

            configureInputMask('#value_paid', '');

            const fechaPago = "{{$activity->policy_sort_collection->created_at}}";
            const fechaRecortada = fechaPago.substring(0, 10);

            $('.comprobante').popup({
                boundary: 'body',
                content: 'Comprobante',
                position: 'top center'
            });

            $('.comprobante_adicional').popup({
                boundary: 'body',
                content: 'Comprobante adicional',
                position: 'top center'
            });

            $(document).ready(function () {
                $('.ui.dropdown').dropdown({
                    forceSelection: false
                }).dropdown('set selected');

                $('.ui.accordion').accordion({
                    exclusive: false
                }).accordion('open', 0);

                $('.datepicker').pickadate({
                    selectYears: true,
                    selectMonths: true,
                    formatSubmit: 'yyyy-mm-dd'
                });


                $('#policy-collection-sort').on('submit', function (e) {
                    loadingMain(true);

                    let isValid = true;

                    const transaction_date = $('#transaction_date').val();
                    const transaction_date_submit = $('input[name="transaction_date_submit"]').val();
                    const time_transaction = $('#time_transaction').val();
                    const bank_account = $('#bank_account').val();
                    const account_details = $('#account_details').val();
                    const payment_method = $('#payment_method').val();

                    let available_amount_apply = $('#available_amount_apply').val();
                    available_amount_apply = parseFloat(available_amount_apply.replace(/\./g, '').replace(',', '.'));

                    let errorMessaged = '<div class="ui segment">';

                    errorMessaged += '<h4>Datos del pago:</h4>';


                    // const fechaServicio = new Date(fechaRecortada);
                    // const fechaPagoRealizado = new Date(transaction_date_submit);

                    const fechaServicio = new Date(fechaRecortada);
                    fechaServicio.setFullYear(fechaServicio.getFullYear() - 1);

                    const fechaPagoRealizado = new Date(transaction_date_submit);


                    if (fechaPagoRealizado < fechaServicio) {

                        errorMessaged += '<ul class="ui list">';
                        errorMessaged += `<li>La fecha de pago(${transaction_date_submit}) no puede ser menor a un año desde la fecha de creacion del servicio (${fechaRecortada})</li>`;
                        errorMessaged += '</ul>';
                        isValid = false;
                    }

                    if (!transaction_date) {

                        errorMessaged += '<ul class="ui list">';
                        errorMessaged += `<li>La fecha de pago</li>`;
                        errorMessaged += '</ul>';
                        isValid = false;
                    }

                    if (!time_transaction) {

                        errorMessaged += '<ul class="ui list">';
                        errorMessaged += `<li>Hora del pago</li>`;
                        errorMessaged += '</ul>';
                        isValid = false;

                    }

                    if (!bank_account && payment_method === 'TB') {

                        errorMessaged += '<ul class="ui list">';
                        errorMessaged += `<li>Banco del depósito</li>`;
                        errorMessaged += '</ul>';
                        isValid = false;
                    }


                    if (!account_details && payment_method === 'TB') {

                        errorMessaged += '<ul class="ui list">';
                        errorMessaged += `<li>Número de cuenta del depósito</li>`;
                        errorMessaged += '</ul>';

                        isValid = false;
                    }

                    if (available_amount_apply < 0) {
                        errorMessaged += '<ul class="ui list">';
                        errorMessaged += `<li>El monto total aplicado de las notas de crédito no puede exceder el saldo disponible</li>`;
                        errorMessaged += '</ul>';

                        isValid = false;
                    }


                    if (isValid === false) {
                        loadingMain(false);
                        errorMessaged += '</div>';

                        Swal.fire({
                            title: 'Validar los siguientes campos',
                            html: errorMessaged,
                            icon: 'warning',
                            showConfirmButton: false,
                            showCancelButton: true,
                            cancelButtonText: 'Cerrar',
                            cancelButtonColor: '#d33'
                        });

                        e.preventDefault();
                    }

                });

                const banks = @json($banks);
                const accountTypes = @json(\App\Providers\AppServiceProvider::$TYPE_ACCOUNT);

                const bankId = "{{ $activity->policy_sort_collection->bank_id }}";
                const paymentCurrency = "{{ $activity->policy_sort_collection->payment_currency }}";
                const accountId = "{{ $activity->policy_sort_collection->account_id }}";

                $('#bank_account_dropdown').dropdown({
                    onChange: function (value) {
                        const paymentCurrency = $('#payment_currency').val();
                        updateAccounts(value, paymentCurrency);
                    }
                }).dropdown('set selected', bankId);

                $('#payment_currency').change(function () {
                    const selectedCurrency = $(this).val();
                    const bank_account = $('#bank_account').val();
                    updateAccounts(bank_account, selectedCurrency);
                });

                $('#account_details_dropdown').dropdown();

                const bankIdPt = "{{ $activity->policy_sort_collection->bank_id_pt }}";
                $('#bank_id_pt_dropdown').dropdown('set selected', bankIdPt);

                function updateAccounts(bankId, paymentCurrency) {
                    const accountMenu = $('#account_details_menu');
                    accountMenu.empty();
                    accountMenu.append('<div class="item" data-value="">Seleccione uno</div>');

                    const selectedBank = banks.find(bank => bank.id == bankId);
                    if (selectedBank && selectedBank.accounts && paymentCurrency) {
                        const accountsFiltered = selectedBank.accounts?.filter(account => account.currency == paymentCurrency);

                        accountsFiltered?.forEach(account => {
                            const accountType = accountTypes[account.type] ?? '';
                            accountMenu.append(
                                `<div class="item" data-value="${account.id}">${accountType} : ${account.account_number}</div>`
                            );
                        });
                    }

                    $('#account_details_dropdown').dropdown('clear');
                    $('#account_details_dropdown').dropdown('refresh');

                    if (accountId && bankId === "{{ $activity->policy_sort_collection->bank_id }}") {
                        $('#account_details_dropdown').dropdown('set selected', accountId);
                    }
                }

                if (bankId && paymentCurrency) {
                    updateAccounts(bankId, paymentCurrency);
                }

            });
        </script>
@endsection