<div class="title">
    <i class="dropdown icon"></i> Soportes
</div>
<div class="content ">

    <table class="ui celled table">
        <thead>
            <tr>
                <th>Documento</th>
                <th><PERSON><PERSON> cargue</th>
                <th>Archivo</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td class="required">Declaración de los testigos</td>
                <td class="fecha" data-fecha="">
                    {{ isset($documents[244]) ? ucfirst(strftime('%A %e de %B del %Y', strtotime($documents[244]->uploaded_at))) : '' }}

                </td>
                <td>
                    <div class=" fields ">
                        @if (isset($documents[244]))
                            <a target="_blank" href="{{ secure_url('/file/' . $documents[244]->path) }}"
                                class="ui secondary icon button pop" style="width: 4rem" data-tooltip="Descargar">
                                <i class="download icon"></i>
                            </a>
                        @endif
                    </div>
                </td>
            </tr>
            <tr>
                <td>Investigación interna del evento</td>
                <td class="fecha" data-fecha="">
                    {{ isset($documents[245]) ? ucfirst(strftime('%A %e de %B del %Y', strtotime($documents[245]->uploaded_at))) : '' }}
                </td>
                <td>
                    <div class=" fields ">
                        @if (isset($documents[245]))
                            <a target="_blank" href="{{ secure_url('/file/' . $documents[245]->path) }}"
                                class="ui secondary icon button pop" style="width: 4rem" data-tooltip="Descargar">
                                <i class="download icon"></i>
                            </a>
                        @endif
                    </div>
                </td>
            </tr>
            <tr>
                <td>Estudio de caso</td>
                <td class="fecha" data-fecha="">
                    {{ isset($documents[246]) ? ucfirst(strftime('%A %e de %B del %Y', strtotime($documents[246]->uploaded_at))) : '' }}
                </td>
                <td>
                    <div class=" fields ">
                        @if (isset($documents[246]))
                            <a target="_blank" href="{{ secure_url('/file/' . $documents[246]->path) }}"
                                class="ui secondary icon button pop" style="width: 4rem" data-tooltip="Descargar">
                                <i class="download icon"></i>
                            </a>
                        @endif
                    </div>
                </td>
            </tr>
            <tr>
                <td>Comunicación a la aseguradora</td>
                <td class="fecha" data-fecha="">
                    {{ isset($documents[247]) ? ucfirst(strftime('%A %e de %B del %Y', strtotime($documents[247]->uploaded_at))) : '' }}
                </td>
                <td>
                    <div class=" fields ">
                        @if (isset($documents[247]))
                            <a target="_blank" href="{{ secure_url('/file/' . $documents[247]->path) }}"
                                class="ui secondary icon button pop" style="width: 4rem" data-tooltip="Descargar">
                                <i class="download icon"></i>
                            </a>
                        @endif
                    </div>
                </td>
            </tr>
            <tr>
                <td>Perfil de puesto de riesgo laboral (opcional)</td>
                <td class="fecha" data-fecha="">
                    {{ isset($documents[248]) ? ucfirst(strftime('%A %e de %B del %Y', strtotime($documents[248]->uploaded_at))) : '' }}
                </td>
                <td>
                    <div class=" fields ">
                        @if (isset($documents[248]))
                            <a target="_blank" href="{{ secure_url('/file/' . $documents[248]->path) }}"
                                class="ui secondary icon button pop" style="width: 4rem" data-tooltip="Descargar">
                                <i class="download icon"></i>
                            </a>
                        @endif
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
</div>

<script>
    $('.pop').popup({
        boundary: 'body', // Esto permite que el popup se muestre fuera de los límites de la tabla
    });
</script>
