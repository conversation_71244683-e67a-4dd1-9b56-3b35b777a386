<div class="title">
    <i class="dropdown icon"></i>
    Información de la atención <span style="color: red;">*</span>
</div>
<div class="content">
    <div class="fields">

        @php
            // Verifica si $medical_service existe
            $formattedDate = ($medical_service && !empty($medical_service->valuation_date))
                ? ucfirst(strftime('%A %e de %B del %Y', strtotime($medical_service->valuation_date)))
                : '';
        @endphp
        <!-- Primer campo: Fecha de valoración -->
        <div class=" five wide  field required">
            <label class="item-label">Fecha de valoración</label>
            <input id="valuationDate_{{ $medical_service ? $medical_service->id : 'empty' }}" type="text" name="valuation_date" placeholder="dd/mm/yyyy"
                   value="{{ isset($medical_service)  ? $formattedDate  : '' }}"  @if($disabled) class="prompt readonly" readonly @else class="datepicker" @endif>
            <input type="hidden" id="valuation_date_hidden_{{ $medical_service ? $medical_service->id : 'empty' }}" name="valuation_date_hidden"
                   value="{{ isset($medical_service)  ? $medical_service->valuation_date  : ''  }}"  @if($disabled) disabled @endif>
        </div>

        <!-- Segundo campo: Hora de valoración -->
        <div class=" five wide  required field">
            <label class="item-label">Hora de valoración:</label>
            <div class="ui icon input ">
                <input id="valuationTime" type="time" name="valuation_time" placeholder="HH:mm"
                       value="{{isset($medical_service->valuation_time)  ? $medical_service->valuation_time :'' }}" @if(!empty($disabled)) class="prompt readonly" readonly @endif   >
            </div>
        </div>

        <!-- Tercer campo: Canal de consulta -->
        <div class="five wide  required field">
            <label class="item-label">Canal de consulta</label>
            <div class="ui selection dropdown @if(!empty($disabled)) disabled @endif" id="heirDocTypeDropdown">
                <input type="hidden" name="consultation_channel1" id="consultation_channel1"
                       class="minus_font" value="{{ $medical_service->consultation_channel ?? '' }}" @if(!empty($disabled)) disabled @endif>
                <i class="dropdown icon"></i>
                <div class="default text minus_font">Seleccionar un canal de consulta</div>
                <div class="menu">
                    @foreach($CONSULTATION_CHANNELS as $k => $v)
                        <div class="item minus_font" data-value="{{$k}}">
                            {{$v}}
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>

    @if(empty($disabled))
        <div class="fields companion-fields">
            <input type="hidden" name="companions[id][]">
            <div class="five wide field">
                <label class="item-label">Nombre del acompañante</label>
                <div class="ui icon input">
                    <input class="prompt" name="companions[name][]" type="text" autocomplete="off">
                </div>
            </div>
            <div class="five wide  field">
                <label class="item-label">Teléfono de contacto</label>
                <div class="ui icon input">
                    <input class="prompt" name="companions[phone][]" type="text" autocomplete="off">
                </div>
            </div>
            <div class="five wide field">
                <label class="item-label">Parentesco</label>
                <div class="ui icon input">
                    <input class="prompt" name="companions[relationship][]" type="text" autocomplete="off">
                </div>
            </div>
            <div class="one wide field">
                <a style="margin-top: 25px;" onclick="addCompanion()" class="ui basic small icon blue fluid button">
                    <i class="add icon"></i>Agregar
                </a>
            </div>
            <div id="companion_model_secondary" style="display: none;" class="fields">
                <input type="hidden" name="companions[id][]">
                <div class="five wide field">
                    <label>Nombre del acompañante</label>
                    <div class="ui icon input">
                        <input class="prompt" name="companions[name][]" type="text" autocomplete="off">
                    </div>
                </div>
                <div class="five wide field">
                    <label>Teléfono de contacto</label>
                    <div class="ui icon input">
                        <input class="prompt" name="companions[phone][]" type="text" autocomplete="off">
                    </div>
                </div>
                <div class="five wide field">
                    <label>Parentesco</label>
                    <div class="ui icon input">
                        <input class="prompt" name="companions[relationship][]" type="text" autocomplete="off">
                    </div>
                </div>
                <div class="one wide field" style="display: flex; align-items: center; margin-top: 25px;">
                    <a class="ui red small icon basic fluid button"><i class="remove icon"></i></a>
                </div>
            </div>
        </div>
        <div id="companions_empty"></div>
    @endif
</div>

@if(!$disabled)
    <script>
        $(document).ready(function() {
            const valuation_date = $('#valuationDate_empty');
            valuation_date.pickadate({
                selectYears: true,
                selectMonths: true,
                min: new Date(),
                formatSubmit: 'yyyy-mm-dd',
                format: 'dd/mm/yyyy',
                onSet: function (context) {
                    if (context.select) {
                        const selectedDate = new Date(context.select);
                        let valuation_date_format = moment(selectedDate).format('dddd D [de] MMMM [de] YYYY');
                        let valuation_date_formatt = valuation_date_format.charAt(0).toUpperCase() + valuation_date_format.slice(1);
                        $('#valuationDate_empty').val(valuation_date_formatt);
                        $('#valuation_date_hidden_empty').val(selectedDate.toISOString().split('T')[0]);
                    }
                }
            });

            @if(isset($medical_service))
            let valuation_date_init = '{{ $medical_service->valuation_date }}';
            if(valuation_date_init != '') {
                valuation_date_init = new Date(valuation_date_init);
                let valuation_date_format_init = moment(valuation_date_init).format('dddd D [de] MMMM [de] YYYY');
                let valuation_date_formatt_init = valuation_date_format_init.charAt(0).toUpperCase() + valuation_date_format_init.slice(1);
                document.getElementById('valuationDate_empty').value = valuation_date_formatt_init;
            }
            @endif
        });
    </script>
@endif



