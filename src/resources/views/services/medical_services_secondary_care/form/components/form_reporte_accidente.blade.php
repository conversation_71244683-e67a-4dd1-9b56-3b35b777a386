<div class="title"><i class="dropdown icon"></i>Reporte accidente </div>

<div class="content">

    <div class="accordion transition">
{{--        --}}{{-- datos_afiliado --}}
{{--        @include('services.medical_services.form.components.reporte_accidente.form_datos_afiliado')--}}
{{--        --}}{{--datos_relacion_laboral--}}
{{--        @include('services.medical_services.form.components.reporte_accidente.form_datos_relacion_laboral', [--}}
{{--            'planillas',--}}
{{--        ])--}}
{{--        --}}{{--datos_persona_autorizada_para_reporte --}}
{{--        @include('services.medical_services.form.components.reporte_accidente.form_datos_persona_autorizada_para_reporte')--}}
        {{--reporte_caso --}}
        @include('services.medical_services.form.components.reporte_accidente.form_reporte_caso')
        {{--external_documentation --}}
{{--        @include('services.medical_services.form.components.reporte_accidente.form_external_documentation')--}}
    </div>


</div>

@push('scripts')
    <script>
        var condition_gis_value = '';
        $(document).ready(function() {
            $('#condition_gis').dropdown({
                onChange: function(value, text, $selectedItem) {
                    condition_gis_value = value;
                }
            });

        });
    </script>
@endpush
