@extends('layouts.main')

@section('title', 'TRÁMITES ANTE JUNTAS DE CALIFICACIÓN DE INVALIDEZ')

@section('menu')
    @parent
@endsection

@section('content')
    <div class="ui basic segment">
        <h1 class="ui header">
            Trámites ante juntas de calificación de invalidez
            <div class="sub header">Campos con <span style="color: red;" class="required">*</span> obligatorios.</div>
        </h1>
        <form autocomplete="off" action="{{secure_url("servicio/{$activity->id}/tramites/save")}}" id="dictamen"
              method="post" class="ui small form">
            <div class="ui secondary segment">
                <div class="ui three columns grid">
                    <div class="column">
                        <b>Identificación:</b> {{$activity->affiliate->doc_type}} {{$activity->affiliate->doc_number}}
                    </div>
                    <div class="column"><b>Nombre:</b> <a
                                href="{{secure_url('afiliado/' . $activity->affiliate_id)}}">{{$activity->affiliate->full_name}}</a>
                    </div>
                    <div class="column"><b>Actividad:</b> <a
                                href="{{secure_url('servicio/' . $activity->id)}}">{{$activity->service->name}}</a>
                    </div>
                    <div class="column"><b>Nro. Dictamen:</b> {{$activity->id}}</div>
                    <div class="column"><b>Fecha solicitud:</b> {{$activity->created_at->formatLocalized('%B %d, %Y')}}
                    </div>
                </div>
            </div>
            @php
                $value_sinister_num=null;
                $act = \App\Activity::query()->where('id',$activity->id)->firstOrFail();
                $service_field = \App\ServiceField::MEETING_SINISTER_NUM;
                $service_field_val = \App\ActivityServiceField::query()->where('service_field_id',$service_field)->where('activity_id',$act->id)->first();
                if (isset($service_field_val) ){
                    $value_sinister_num = $service_field_val->value;
                }
            @endphp
            <div class="ui styled fluid accordion">
                <div id="87" class="title">
                    <i class="dropdown icon"></i>Información del siniestro<span style="color: red;"
                                                                                class="required">*</span>
                </div>
                <div class="content">
                    <div class="four fields">
                        <div class="required field">
                            <label>No. de siniestro</label>
                            @if($value_sinister_num)
                                <input value="{{$value_sinister_num}}" name="sinister_number" type="number">
                            @elseif($activity->meeting)
                                <input value="{{$activity->meeting->sinister_number}}" name="sinister_number"
                                       type="number">
                            @else
                                <input value="{{''}}" name="sinister_number" type="number">
                            @endif
                        </div>
                        <div class="required field">
                            <label>Fecha de siniestro</label>
                            <input data-value="{{$activity->meeting ? $activity->meeting->sinister_date : ''}}"
                                   name="sinister_date" class="datepicker"/>
                        </div>
                        <div class="required field">
                            <label>Número de dictamen</label>
                            <input value="{{$activity->meeting ? $activity->meeting->dictum_number : ''}}"
                                   name="dictum_number" type="number">
                        </div>
                        <div class="required field">
                            <label>Fecha de calificación Equidad</label>
                            <input data-value="{{$activity->meeting ? $activity->meeting->equity_rating_date : ''}}"
                                   name="equity_rating_date" class="datepicker"/>
                        </div>
                    </div>
                    <div class="three fields">
                        <div class="required field">
                            <label>Fecha de notificación a partes interesadas</label>
                            <input data-value="{{$activity->meeting ? $activity->meeting->notification_interested_parties_date : ''}}"
                                   name="notification_interested_parties_date" class="datepicker"/>
                        </div>
                        <div class="required field">
                            <label>Calificación de:</label>
                            <div class="ui fluid selection dropdown">
                                <input value="{{$activity->meeting ? $activity->meeting->rating_of : ''}}"
                                       name="rating_of" type="hidden">
                                <i class="dropdown icon"></i>
                                <div class="default text">Seleccione uno</div>
                                <div class="menu">
                                    @foreach($MEETING_ATING_OF as $k => $v)
                                        <div class="item" data-value="{{$k}}">{{$v}}</div>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                        <div class="required field">
                            <label>Tipo de evento</label>
                            <div class="ui fluid selection dropdown">
                                <input value="{{$activity->meeting ? $activity->meeting->event_type : ''}}"
                                       name="event_type" type="hidden">
                                <i class="dropdown icon"></i>
                                <div class="default text">Seleccione uno</div>
                                <div class="menu">
                                    @foreach($MEETING_TYPE_EVENT as $k => $v)
                                        <div class="item" data-value="{{$k}}">{{$v}}</div>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div id="88" class="title">
                    <i class="dropdown icon"></i>Información remisión expediente JRCI<span style="color: red;"
                                                                                           class="required">*</span>
                </div>
                <div id="jrciFileReferralInformation" class="content">
                    <div class="fields">
                        <div class="five wide required field">
                            <label>Parte interesada que controvierte</label>
                        </div>
                        <div class="one wide field">
                            <a style="margin-top: -15px;" onclick="addIntertestedParts()"
                               class="ui basic small icon blue button">
                                <i class="add icon"></i>
                            </a>
                        </div>
                    </div>
                    <div id="interestedParts">
                        @if ($activity->meeting && count($activity->meeting->interestedParts) > 0)
                            @foreach ($activity->meeting->interestedParts as $interestedPart)
                                <div class="fields">
                                    <input type="hidden" name="interestedParts[id][]" value="{{$interestedPart->id}}">
                                    <div class="five wide required field">
                                        <div class="ui fluid selection dropdown">
                                            <input value="{{$interestedPart->interested_part}}"
                                                   name="interestedParts[interested_part][]" type="hidden">
                                            <i class="dropdown icon"></i>
                                            <div class="default text">Seleccione uno</div>
                                            <div class="menu">
                                                @foreach($MEETING_INTERESTED_PARTY_THAT_DISPUTES as $k => $v)
                                                    <div class="item" data-value="{{$k}}">{{$v}}</div>
                                                @endforeach
                                            </div>
                                        </div>
                                    </div>
                                    <div class="one wide field">
                                        <a class="ui red small icon basic button"><i class="remove icon"></i></a>
                                    </div>
                                </div>
                            @endforeach
                        @else
                            <div class="fields">
                                <input type="hidden" name="interestedParts[id][]">
                                <div class="five wide required field">
                                    <div class="ui fluid selection dropdown">
                                        <input name="interestedParts[interested_part][]" type="hidden">
                                        <i class="dropdown icon"></i>
                                        <div class="default text">Seleccione uno</div>
                                        <div class="menu">
                                            @foreach($MEETING_INTERESTED_PARTY_THAT_DISPUTES as $k => $v)
                                                <div class="item" data-value="{{$k}}">{{$v}}</div>
                                            @endforeach
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endif
                    </div>
                    <div class="three fields">
                        <div class="required field">
                            <label>Fecha de radicación de expediente</label>
                            <input data-value="{{$activity->meeting ? $activity->meeting->file_filing_date : ''}}"
                                   name="file_filing_date" class="datepicker"/>
                        </div>
                        <div class="required field">
                            <label>Fecha de controversia</label>
                            <input data-value="{{$activity->meeting ? $activity->meeting->controversy_date : ''}}"
                                   name="controversy_date" class="datepicker"/>
                        </div>
                        <div class="required field">
                            <label>Junta regional</label>
                            <div class="ui fluid selection dropdown">
                                <input value="{{$activity->meeting ? $activity->meeting->regional_board : ''}}"
                                       name="regional_board" type="hidden">
                                <i class="dropdown icon"></i>
                                <div class="default text">Seleccione uno</div>
                                <div class="menu">
                                    @foreach($BOARDS as $k => $v)
                                        <div class="item" data-value="{{$k}}">{{$v}}</div>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="one field">
                        <div class="required field">
                            <label>Motivo de controversia</label>
                            <textarea name="reason_controversy"
                                      rows="2">{{$activity->meeting ? $activity->meeting->reason_controversy : ''}}</textarea>
                        </div>
                    </div>
                    <div style="margin-bottom: 5px;" class="fields">
                        <div class="four wide required field">
                            <label>Código CIE 10</label>
                        </div>
                        <div class="four wide required field">
                            <label>Nombre del Diagnóstico</label>
                        </div>
                        <div class="four wide required field">
                            <label>Origen</label>
                        </div>
                        <div class="one wide field">
                            <a style="margin-top: -15px;" onclick="addDiagnostic()"
                               class="ui basic small icon blue button"><i class="add icon"></i></a>
                        </div>
                    </div>
                    <div id="diagnostics">
                        @if ($activity->meeting && count($activity->meeting->diagnostics) > 0)
                            @foreach ($activity->meeting->diagnostics as $diagnostic)
                                <div class="fields">
                                    <input type="hidden" name="diagnostics[id][]" value="{{$diagnostic->id}}">
                                    <div class="four wide required field">
                                        <div class="ui search code">
                                            <div class="ui icon input">
                                                <input class="prompt" name="diagnostics[cod][]" type="text"
                                                       value="{{$diagnostic->code}}">
                                                <i class="search icon"></i>
                                            </div>
                                            <div class="results"></div>
                                        </div>
                                    </div>
                                    <div class="five wide required field">
                                        <input class="prompt" name="diagnostics[description][]" type="text"
                                               value="{{$diagnostic->description}}">
                                    </div>
                                    <div class="four wide field">
                                        <div class="ui fluid selection dropdown">
                                            <input name="diagnostics[origin][]" type="hidden"
                                                   value="{{$diagnostic->origin}}">
                                            <i class="dropdown icon"></i>
                                            <div class="default text">Origen</div>
                                            <div class="menu">
                                                @foreach($ORIGINS as $k => $v)
                                                    <div class="item" data-value="{{$k}}">{{$v}}</div>
                                                @endforeach
                                            </div>
                                        </div>
                                    </div>
                                    <div class="one wide field">
                                        <a class="ui red small icon basic button"><i class="remove icon"></i></a>
                                    </div>
                                </div>
                            @endforeach
                        @else
                            <div class="fields">
                                <input type="hidden" name="diagnostics[id][]">
                                <div class="four wide required field">
                                    <div class="ui search code">
                                        <div class="ui icon input">
                                            <input class="code prompt" name="diagnostics[cod][]" type="text">
                                            <i class="search icon"></i>
                                        </div>
                                        <div class="results"></div>
                                    </div>
                                </div>
                                <div class="four wide required field">
                                    <input class="description prompt" name="diagnostics[description][]" type="text">
                                </div>
                                <div class="required four wide field">
                                    <div class="ui fluid selection dropdown">
                                        <input name="diagnostics[origin][]" type="hidden">
                                        <i class="dropdown icon"></i>
                                        <div class="default text">Origen</div>
                                        <div class="menu">
                                            @foreach($ORIGINS as $k => $v)
                                                <div class="item" data-value="{{$k}}">{{$v}}</div>
                                            @endforeach
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endif
                    </div>
                    <div class="one field">
                        <div class="field">
                            <label>% PCL</label>
                            <input value="{{$activity->meeting ? $activity->meeting->percentage_pcl : ''}}"
                                   name="percentage_pcl" type="number">
                        </div>
                    </div>
                    <div class="one field">
                        @if ($activity->meeting && count($activity->meeting->attachedDocuments) > 0)
                            @foreach($activity->meeting->attachedDocuments as $attachedDocument)
                                <input type="hidden" value="{{$attachedDocument->id}}" name="attachedDocuments[id][]">
                            @endforeach
                            <strong><label>Documentos anexos</label></strong>
                            <table class="ui celled striped very compact table">
                                <thead>
                                <tr>
                                    <th>Documento</th>
                                    <th>Opción</th>
                                </tr>
                                </thead>
                                <tbody>
                                @foreach($activity->meeting->attachedDocuments as $attachedDocument)
                                    <tr>
                                        <td>
                                            <input value="{{$attachedDocument->document}}" readonly
                                                   name="attachedDocuments[documents][]">
                                        </td>
                                        <td>
                                            <div class="ui fluid selection dropdown">
                                                <input value="{{$attachedDocument->available_option}}"
                                                       name="attachedDocuments[options][]" type="hidden">
                                                <i class="dropdown icon"></i>
                                                <div class="default text">Opciones</div>
                                                <div class="menu">
                                                    <div class="item" data-value="1">SI</div>
                                                    <div class="item" data-value="2">NO</div>
                                                    <div class="item" data-value="3">N/A</div>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                                <tr>
                                    <td id="moreAttachedDocuments">
                                        OTRO
                                    </td>
                                    <td>
                                        <center>
                                            <a onclick="addMoreAttachedDocuments()" id="moreAttachedDocuments"
                                               class="ui basic small icon blue button"><i class="add icon"></i></a>
                                        </center>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        @else
                            @foreach($MEETING_ATTACHED_DOCUMENTS as $k => $v)
                                <input type="hidden" name="attachedDocuments[id][]">
                            @endforeach
                            <strong><label>Documentos anexos</label></strong>
                            <table class="ui celled striped very compact very small collapsing table">
                                <thead>
                                <tr>
                                    <th>Documento</th>
                                    <th>Opción</th>
                                </tr>
                                </thead>
                                <tbody>
                                @foreach($MEETING_ATTACHED_DOCUMENTS as $k => $v)
                                    <tr>
                                        <td>
                                            <input value="{{$v}}" readonly name="attachedDocuments[documents][]">
                                        </td>
                                        <td>
                                            <div class="ui fluid selection dropdown">
                                                <input name="attachedDocuments[options][]" type="hidden">
                                                <i class="dropdown icon"></i>
                                                <div class="default text">Opciones</div>
                                                <div class="menu">
                                                    <div class="item" data-value="1">SI</div>
                                                    <div class="item" data-value="2">NO</div>
                                                    <div class="item" data-value="3">N/A</div>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                                <tr>
                                    <td id="moreAttachedDocuments">
                                        OTRO
                                    </td>
                                    <td>
                                        <center>
                                            <a onclick="addMoreAttachedDocuments()" id="moreAttachedDocuments"
                                               class="ui basic small icon blue button"><i class="add icon"></i></a>
                                        </center>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        @endif
                    </div>
                    <div class="five fields">
                        <div class="required field">
                            <label>Entidad a cargo del pago de honorarios</label>
                            <div class="ui fluid selection dropdown">
                                <input value="{{$activity->meeting ? $activity->meeting->fee_payment_request_entity : ''}}"
                                       name="fee_payment_request_entity"
                                       type="hidden">
                                <i class="dropdown icon"></i>
                                <div class="default text">Seleccione uno</div>
                                <div class="menu">
                                    <div class="item" data-value="LA EQUIDAD ARL">LA EQUIDAD ARL</div>
                                    <div class="item" data-value="PORVENIR S.A.">PORVENIR S.A.</div>
                                    <div class="item" data-value="COLPENSIONES">COLPENSIONES</div>
                                    <div class="item" data-value="PROTECCIÓN S.A.">PROTECCIÓN S.A.</div>
                                    <div class="item" data-value="COLFONDOS">COLFONDOS</div>
                                    <div class="item" data-value="ARL Y AFP">ARL Y AFP</div>
                                    <div class="item" data-value="SKANDIA">SKANDIA</div>
                                </div>
                            </div>
                        </div>
                        <div class="required field">
                            <label>Fecha de solicitud pago Honorarios</label>
                            <input data-value="{{$activity->meeting ? $activity->meeting->payment_request_date : ''}}"
                                   name="payment_request_date" class="datepicker"/>
                        </div>
                        <div class="required field">
                            <label>Fecha pago de honorarios</label>
                            <input data-value="{{$activity->meeting ? $activity->meeting->payment_fees_date : ''}}"
                                   name="payment_fees_date" class="datepicker"/>
                        </div>
                        <div class="required field">
                            <label> Valor honorarios</label>
                            <input value="{{$activity->meeting ? $activity->meeting->value_fees : ''}}"
                                   name="value_fees" type="number"/>
                        </div>
                        <div class="required field">
                            <label>Numero orden pago JRCI</label>
                            <textarea name="jrci_payment_order_number"
                                      rows="2">{{$activity->meeting ? $activity->meeting->jrci_payment_order_number : ''}}</textarea>
                        </div>
                        <div class="required field">
                            <label>Fecha remisión expediente</label>
                            <input data-value="{{$activity->meeting ? $activity->meeting->file_submission_date : ''}}"
                                   name="file_submission_date" class="datepicker"/>
                        </div>
                    </div>
                    <div class="five fields">
                        <div class="required field">
                            <label>Nro. radicado de salida</label>
                            <textarea name="registered_exit_number"
                                      rows="2">{{$activity->meeting ? $activity->meeting->registered_exit_number : ''}}</textarea>
                        </div>
                        <div class="required field">
                            <label>Medio de envío</label>
                            <div class="ui fluid selection dropdown">
                                <input value="{{$activity->meeting ? $activity->meeting->average_costs : ''}}"
                                       name="average_costs" type="hidden">
                                <i class="dropdown icon"></i>
                                <div class="default text">Seleccione uno</div>
                                <div class="menu">
                                    @foreach($MEETING_AVERAGE_COSTS as $k => $v)
                                        <div class="item" data-value="{{$k}}">{{$v}}</div>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                        <div class="field">
                            <label>Carta medio de evento</label>
                            @if($activity->meeting)
                                @if ($activity->meeting->medium_event_letter != null)
                                    <a class="ui green basic label"
                                       href="{{secure_url('file/'.$activity->meeting->medium_event_letter)}}"
                                       target="_blank">
                                        <i class="file icon">
                                        </i> VER
                                    </a>
                                @else
                                    <input id="notificationsfile" name="notificationsfile"
                                           type="file" onchange="uploadFile()">
                                    <input id="notification_file_text" name="medium_event_letter"
                                           type="hidden"/>
                                    <p></p>
                                @endif
                            @else
                                <input id="notificationsfile" name="notificationsfile"
                                       type="file" onchange="uploadFile()">
                                <input id="notification_file_text" name="medium_event_letter"
                                       type="hidden"/>
                                <p></p>
                            @endif
                        </div>
                        <div class="required field">
                            <label>Notificación efectiva</label>
                            <div class="ui fluid selection dropdown">
                                <input value="{{$activity->meeting ? $activity->meeting->effective_notification : ''}}"
                                       name="effective_notification" type="hidden">
                                <i class="dropdown icon"></i>
                                <div class="default text">Seleccione uno</div>
                                <div class="menu">
                                    <div class="item" data-value="1">SI</div>
                                    <div class="item" data-value="2">NO</div>
                                </div>
                            </div>
                        </div>
                        <div class="required field">
                            <label>Estado Apelación</label>
                            <div class="ui fluid selection dropdown">
                                <input value="{{$activity->meeting ? $activity->meeting->appeal_status : ''}}"
                                       name="appeal_status" type="hidden">
                                <i class="dropdown icon"></i>
                                <div class="default text">Seleccione uno</div>
                                <div class="menu">
                                    @foreach($MEETING_APPEAL_STATUS as $k => $v)
                                        <div class="item" data-value="{{$k}}">{{$v}}</div>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="five fields">
                        <div class="required field">
                            <label>Requiere seguimiento 60 días</label>
                            <div class="ui fluid selection dropdown">
                                <input value="{{$activity->meeting ? $activity->meeting->requires_follow_sixty_days : ''}}"
                                       name="requires_follow_sixty_days" type="hidden">
                                <i class="dropdown icon"></i>
                                <div class="default text">Seleccione uno</div>
                                <div class="menu">
                                    <div class="item" data-value="1">SI</div>
                                    <div class="item" data-value="2">NO</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div id="89" class="title">
                    <i class="dropdown icon"></i>Seguimiento JRCI<span style="color: red;" class="required">*</span>
                </div>
                <div id="tracingJrci" class="content">
                    <div style="position: relative" id="pcl_dictumJRCIPclBoard">
                        <div style="position: absolute; right:0;top: -2.5rem;z-index: 2">
                            <a onclick="addTracingJrci()" class="ui basic small icon blue button">
                                <i class="add icon"></i>
                            </a>
                        </div>
                    </div>
                    @if ($activity->meeting && count($activity->meeting->jrciTracings) > 0)
                        @foreach ($activity->meeting->jrciTracings as $jrciTracing)
                            <input type="hidden" name="tracingJrci[id][]" value="{{$jrciTracing->id}}">
                            <div class="four fields">
                                <div class="required field">
                                    <label>Fecha remisión carta</label>
                                    <input data-value="{{$jrciTracing->letter_remittance_date}}"
                                           name="tracingJrci[letter_remittance_date][]" class="datepicker"/>
                                </div>
                                <div class="required field">
                                    <label>Medio de envío</label>
                                    <div class="ui fluid selection dropdown">
                                        <input value="{{$jrciTracing->average_costs}}"
                                               name="tracingJrci[average_costs][]" type="hidden">
                                        <i class="dropdown icon"></i>
                                        <div class="default text">Seleccione uno</div>
                                        <div class="menu">
                                            @foreach($MEETING_JRCI_AVERAGE_COST as $k => $v)
                                                <div class="item" data-value="{{$k}}">{{$v}}</div>
                                            @endforeach
                                        </div>
                                    </div>
                                </div>
                                <div class="required field">
                                    <label>Fecha notificación pago Junta</label>
                                    <input data-value="{{$jrciTracing->board_payment_notification_date}}"
                                           name="tracingJrci[board_payment_notification_date][]" class="datepicker"/>
                                </div>
                                <div class="required field">
                                    <label>Carta medio de evento</label>
                                    @if ($jrciTracing->medium_event_letter != null)
                                        <a class="ui green basic label"
                                           href="{{secure_url('file/'.$jrciTracing->medium_event_letter)}}"
                                           target="_blank">
                                            <i class="file icon">
                                            </i> VER
                                        </a>
                                    @else
                                        <input id="notificationsfile" name="tracingJrci[notificationsfile][]"
                                               type="file" onchange="uploadFile()">
                                        <input id="notification_file_text" name="tracingJrci[medium_event_letter][]"
                                               type="hidden"/>
                                        <p></p>
                                    @endif
                                </div>
                            </div>
                            <div class="four fields">
                                <div class="required field">
                                    <label>Fecha alerta nuevo seguimiento</label>
                                    <input data-value="{{$jrciTracing->new_follow_alert_date}}"
                                           name="tracingJrci[new_follow_alert_date][]" class="datepicker2"/>
                                </div>
                                <div class="required field">
                                    <label>Notificación efectiva</label>
                                    <div class="ui fluid selection dropdown">
                                        <input value="{{$jrciTracing->effective_notification}}"
                                               name="tracingJrci[effective_notification][]" type="hidden">
                                        <i class="dropdown icon"></i>
                                        <div class="default text">Seleccione uno</div>
                                        <div class="menu">
                                            <div class="item" data-value="1">SI</div>
                                            <div class="item" data-value="2">NO</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="required field">
                                    <label>Fecha Notificación efectiva</label>
                                    <input data-value="{{$jrciTracing->effective_notification_date}}"
                                           name="tracingJrci[effective_notification_date][]" class="datepicker"/>
                                </div>
                                <div class="required field">
                                    <label>Requiere seguimiento adicional</label>
                                    <div class="ui fluid selection dropdown">
                                        <input value="{{$jrciTracing->requires_additional_follow_up}}"
                                               name="tracingJrci[requires_additional_follow_up][]" type="hidden">
                                        <i class="dropdown icon"></i>
                                        <div class="default text">Seleccione uno</div>
                                        <div class="menu">
                                            <div class="item" data-value="1">SI</div>
                                            <div onclick="enableDicFromSeg()" class="item" data-value="2">NO</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    @else
                        <input type="hidden" name="tracingJrci[id][]">
                        <div style="position: relative" id="pcl_dictumJRCIPclBoard">
                            <div style="position: absolute; right:0;top: -2.5rem;z-index: 2">
                                <a onclick="addTracingJrci()" class="ui basic small icon blue button">
                                    <i class="add icon"></i>
                                </a>
                            </div>
                        </div>
                        <div class="four fields">
                            <div class="required field">
                                <label>Fecha remisión carta</label>
                                <input name="tracingJrci[letter_remittance_date][]" class="datepicker"/>
                            </div>
                            <div class="required field">
                                <label>Medio de envío</label>
                                <div class="ui fluid selection dropdown">
                                    <input name="tracingJrci[average_costs][]" type="hidden">
                                    <i class="dropdown icon"></i>
                                    <div class="default text">Seleccione uno</div>
                                    <div class="menu">
                                        @foreach($MEETING_JRCI_AVERAGE_COST as $k => $v)
                                            <div class="item" data-value="{{$k}}">{{$v}}</div>
                                        @endforeach
                                    </div>
                                </div>
                            </div>
                            <div class="required field">
                                <label>Fecha notificación pago Junta</label>
                                <input name="tracingJrci[board_payment_notification_date][]" class="datepicker"/>
                            </div>
                            <div class="required field">
                                <label>Carta medio de evento</label>
                                <input id="notificationsfile" name="tracingJrci[notificationsfile][]"
                                       type="file" onchange="uploadFile()">
                                <input id="notificationsfile_text" name="tracingJrci[medium_event_letter][]"
                                       type="hidden"/>
                                <p></p>
                            </div>
                        </div>
                        <div class="four fields">
                            <div class="required field">
                                <label>Fecha alerta nuevo seguimiento</label>
                                <input name="tracingJrci[new_follow_alert_date][]" class="datepicker2"/>
                            </div>
                            <div class="required field">
                                <label>Notificación efectiva</label>
                                <div class="ui fluid selection dropdown">
                                    <input name="tracingJrci[effective_notification][]" type="hidden">
                                    <i class="dropdown icon"></i>
                                    <div class="default text">Seleccione uno</div>
                                    <div class="menu">
                                        <div class="item" data-value="1">SI</div>
                                        <div class="item" data-value="2">NO</div>
                                    </div>
                                </div>
                            </div>
                            <div class="required field">
                                <label>Fecha Notificación efectiva</label>
                                <input name="tracingJrci[effective_notification_date][]" class="datepicker"/>
                            </div>
                            <div class="required field">
                                <label>Requiere seguimiento adicional</label>
                                <div class="ui fluid selection dropdown">
                                    <input name="tracingJrci[requires_additional_follow_up][]" type="hidden">
                                    <i class="dropdown icon"></i>
                                    <div class="default text">Seleccione uno</div>
                                    <div class="menu">
                                        <div class="item" data-value="1">SI</div>
                                        <div onclick="enableDicFromSeg()" class="item" data-value="2">NO</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>
                <div id="90" class="title">
                    <i class="dropdown icon"></i>Dictamen JRCI<span style="color: red;" class="required">*</span>
                </div>
                <div class="content">
                    <div class="two fields">
                        <div class="required field">
                            <label>Fecha dictamen JRCI</label>
                            <input data-value="{{$activity->meeting ? $activity->meeting->jrci_dictum_date : ''}}"
                                   name="jrci_dictum_date" class="datepicker"/>
                        </div>
                        <div class="required field">
                            <label>Fecha Notificación dictamen JRCI a Equidad</label>
                            <input data-value="{{$activity->meeting ? $activity->meeting->notification_jrci_opinion_equidad_date : ''}}"
                                   name="notification_jrci_opinion_equidad_date" class="datepicker"/>
                        </div>
                    </div>
                    <div class="one field">
                        <div class="required field">
                            <label>No. dictamen JRCI</label>
                            <textarea name="dictum_jrci_number"
                                      rows="2">{{$activity->meeting ? $activity->meeting->dictum_jrci_number : ''}}</textarea>
                        </div>
                    </div>
                    <div style="margin-bottom: 5px;" class="fields">
                        <div class="four wide required field">
                            <label>Código CIE 10</label>
                        </div>
                        <div class="four wide required field">
                            <label>Nombre del Diagnóstico</label>
                        </div>
                        <div class="four wide required field">
                            <label>Origen</label>
                        </div>
                        <div class="one wide field">
                            <a style="margin-top: -15px;" onclick="addjrciDiagnostics()"
                               class="ui basic small icon blue button"><i class="add icon"></i></a>
                        </div>
                    </div>
                    <div id="jrciDiagnostics">
                        @if ($activity->meeting && count($activity->meeting->jrciDiagnostics) > 0)
                            @foreach ($activity->meeting->jrciDiagnostics as $diagnostic)
                                <div class="fields">
                                    <input type="hidden" name="jrciDiagnostics[id][]" value="{{$diagnostic->id}}">
                                    <div class="four wide required field">
                                        <div class="ui search code">
                                            <div class="ui icon input">
                                                <input class="prompt" name="jrciDiagnostics[cod][]" type="text"
                                                       value="{{$diagnostic->code}}">
                                                <i class="search icon"></i>
                                            </div>
                                            <div class="results"></div>
                                        </div>
                                    </div>
                                    <div class="five wide required field">
                                        <input class="prompt" name="jrciDiagnostics[description][]" type="text"
                                               value="{{$diagnostic->description}}">
                                    </div>
                                    <div class="four wide field">
                                        <div class="ui fluid selection dropdown">
                                            <input name="jrciDiagnostics[origin][]" type="hidden"
                                                   value="{{$diagnostic->origin}}">
                                            <i class="dropdown icon"></i>
                                            <div class="default text">Origen</div>
                                            <div class="menu">
                                                @foreach($ORIGINS as $k => $v)
                                                    <div class="item" data-value="{{$k}}">{{$v}}</div>
                                                @endforeach
                                            </div>
                                        </div>
                                    </div>
                                    <div class="one wide field">
                                        <a class="ui red small icon basic button"><i class="remove icon"></i></a>
                                    </div>
                                </div>
                            @endforeach
                        @else
                            <div class="fields">
                                <input type="hidden" name="jrciDiagnostics[id][]">
                                <div class="four wide required field">
                                    <div class="ui search code">
                                        <div class="ui icon input">
                                            <input class="code prompt" name="jrciDiagnostics[cod][]" type="text">
                                            <i class="search icon"></i>
                                        </div>
                                        <div class="results"></div>
                                    </div>
                                </div>
                                <div class="four wide required field">
                                    <input class="description prompt" name="jrciDiagnostics[description][]" type="text">
                                </div>
                                <div class="required four wide field">
                                    <div class="ui fluid selection dropdown">
                                        <input name="jrciDiagnostics[origin][]" type="hidden">
                                        <i class="dropdown icon"></i>
                                        <div class="default text">Origen</div>
                                        <div class="menu">
                                            @foreach($ORIGINS as $k => $v)
                                                <div class="item" data-value="{{$k}}">{{$v}}</div>
                                            @endforeach
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endif
                    </div>
                    <div class="three fields">
                        <div class="field">
                            <label>% PCL</label>
                            <input name="porcentage_jrci_pcl"
                                   value="{{$activity->meeting ? $activity->meeting->porcentage_jrci_pcl : ''}}"
                                   type="number">
                        </div>
                        <div class="required field">
                            <label>Pronunciamiento</label>
                            <div class="ui fluid selection dropdown">
                                <input value="{{$activity->meeting ? $activity->meeting->pronouncement : ''}}"
                                       name="pronouncement" type="hidden">
                                <i class="dropdown icon"></i>
                                <div class="default text">Seleccione uno</div>
                                <div class="menu">
                                    @foreach($MEETING_PRONOUNCEMENT as $k => $v)
                                        <div class="item" data-value="{{$k}}">{{$v}}</div>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                        <div class="required field">
                            <label>Argumentación</label>
                            <textarea name="argumentation"
                                      rows="2">{{$activity->meeting ? $activity->meeting->argumentation : ''}}</textarea>
                        </div>
                    </div>
                    <div class="four fields">
                        <div class="required field">
                            <label>Fecha remisión argumentación</label>
                            <input data-value="{{$activity->meeting ? $activity->meeting->argument_submission_date : ''}}"
                                   name="argument_submission_date" class="datepicker"/>
                        </div>
                        <div class="required field">
                            <label>Medio de envío argumentación</label>
                            <div class="ui fluid selection dropdown">
                                <input value="{{$activity->meeting ? $activity->meeting->shipping_method_argumentation : ''}}"
                                       name="shipping_method_argumentation" type="hidden">
                                <i class="dropdown icon"></i>
                                <div class="default text">Seleccione uno</div>
                                <div class="menu">
                                    @foreach($MEETING_JRCI_DICTUM_AVERAGE_COST as $k => $v)
                                        <div class="item" data-value="{{$k}}">{{$v}}</div>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                        <div class="required field">
                            <label>Fecha de radicación de argumentación</label>
                            <input data-value="{{$activity->meeting ? $activity->meeting->argument_jrci_filing_date : ''}}"
                                   name="argument_jrci_filing_date" class="datepicker"/>
                        </div>
                        <div class="required field">
                            <label>Carta medio de evento argumentación</label>
                            @if ($activity->meeting)
                                @if ($activity->meeting->average_letter_argumentation_event != null)
                                    <a class="ui green basic label"
                                       href="{{secure_url('file/'.$activity->meeting->average_letter_argumentation_event)}}"
                                       target="_blank">
                                        <i class="file icon">
                                        </i> VER
                                    </a>
                                @else
                                    <input id="notificationsfile" name="notificationsfile"
                                           type="file" onchange="uploadFile()">
                                    <input id="notification_file_text" name="average_letter_argumentation_event"
                                           type="hidden"/>
                                    <p></p>
                                @endif
                            @else
                                <input id="notificationsfile" name="notificationsfile"
                                       type="file" onchange="uploadFile()">
                                <input id="notification_file_text" name="average_letter_argumentation_event"
                                       type="hidden"/>
                                <p></p>
                            @endif
                        </div>
                    </div>
                    <div class="three fields">
                        <div class="required field">
                            <label>Tiene Ejecutoria JRCI</label>
                            <div class="ui fluid selection dropdown">
                                <input value="{{$activity->meeting ? $activity->meeting->jrci_execution : ''}}"
                                       name="jrci_execution" type="hidden">
                                <i class="dropdown icon"></i>
                                <div class="default text">Seleccione uno</div>
                                <div class="menu">
                                    <div class="item" data-value="1">SI</div>
                                    <div class="item" data-value="2">NO</div>
                                </div>
                            </div>
                        </div>
                        <div class="field">
                            <label>Fecha Ejecutoria JRCI</label>
                            <input data-value="{{$activity->meeting ? $activity->meeting->jrci_execution_date : ''}}"
                                   name="jrci_execution_date" class="datepicker"/>
                        </div>
                        <div class="required field">
                            <label>Notificación efectiva argumentación</label>
                            <div class="ui fluid selection dropdown">
                                <input value="{{$activity->meeting ? $activity->meeting->effective_notification_argumentation : ''}}"
                                       name="effective_notification_argumentation" type="hidden">
                                <i class="dropdown icon"></i>
                                <div class="default text">Seleccione uno</div>
                                <div class="menu">
                                    <div class="item" data-value="1">SI</div>
                                    <div class="item" data-value="2">NO</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div id="91" class="title">
                    <i class="dropdown icon"></i>Controversia JNCI<span style="color: red;" class="required">*</span>
                </div>
                <div id="controversyJnci" class="content">
                    <div style="position: relative" id="pcl_dictumJRCIPclBoard">
                        <div style="position: absolute; right:0;top: -2.5rem;z-index: 2">
                            <a onclick="addControversyJnci()" class="ui basic small icon blue button">
                                <i class="add icon"></i>
                            </a>
                        </div>
                    </div>
                    @if ($activity->meeting && count($activity->meeting->jnciControversies) > 0)
                        @foreach ($activity->meeting->jnciControversies as $jnciControversy)
                            <input type="hidden" name="controversyJnci[id][]" value="{{$jnciControversy->id}}">
                            <div class="three fields">
                                <div class="required field">
                                    <label>Parte interesada que controvierte</label>
                                    <div class="ui fluid selection dropdown">
                                        <input value="{{$jnciControversy->interested_party_disputes}}"
                                               name="controversyJnci[interested_party_disputes][]" type="hidden">
                                        <i class="dropdown icon"></i>
                                        <div class="default text">Seleccione uno</div>
                                        <div class="menu">
                                            @foreach($MEETING_INTERESTED_PARTY_THAT_DISPUTES as $k => $v)
                                                <div class="item" data-value="{{$k}}">{{$v}}</div>
                                            @endforeach
                                        </div>
                                    </div>
                                </div>
                                <div class="required field">
                                    <label>Fecha notificación pago de honorarios</label>
                                    <input data-value="{{$jnciControversy->file_filing_date}}"
                                           name="controversyJnci[file_filing_date][]" class="datepicker"/>
                                </div>
                                <div class="required field">
                                    <label>Valor honorarios JNCI</label>
                                    <input value="{{$jnciControversy->value_fees}}" name="controversyJnci[value_fees][]"
                                           type="number"/>
                                </div>
                                <div class="required field">
                                    <label>Fecha de controversia</label>
                                    <input data-value="{{$jnciControversy->controversy_date}}"
                                           name="controversyJnci[controversy_date][]" class="datepicker"/>
                                </div>
                            </div>
                            <div class="four fields">
                                <div class="required field">
                                    <label>Junta regional que emitió el dictamen</label>
                                    <input readonly
                                           value="{{$activity->meeting && $activity->meeting->regional_board ? $BOARDS[$activity->meeting->regional_board] : ''}}"
                                           name="controversyJnci[regional_board_issued_opinion][]">
                                </div>
                                <div class="required field">
                                    <label>Motivo de controversia</label>
                                    <textarea name="controversyJnci[reason_controversy][]"
                                              rows="2">{{$jnciControversy->reason_controversy}}</textarea>
                                </div>
                                <div class="required field">
                                    <label>Estado Apelación</label>
                                    <div class="ui fluid selection dropdown">
                                        <input value="{{$jnciControversy->appeal_status}}"
                                               name="controversyJnci[appeal_status][]" type="hidden">
                                        <i class="dropdown icon"></i>
                                        <div class="default text">Seleccione uno</div>
                                        <div class="menu">
                                            @foreach($MEETING_APPEAL_STATUS as $k => $v)
                                                <div class="item" data-value="{{$k}}">{{$v}}</div>
                                            @endforeach
                                        </div>
                                    </div>
                                </div>
                                <div class="required field">
                                    <label>Requiere seguimiento 60 días</label>
                                    <div class="ui fluid selection dropdown">
                                        <input value="{{$jnciControversy->requires_follow_sixty_days}}"
                                               name="controversyJnci[requires_follow_sixty_days][]" type="hidden">
                                        <i class="dropdown icon"></i>
                                        <div class="default text">Seleccione uno</div>
                                        <div class="menu">
                                            <div class="item" data-value="1">SI</div>
                                            <div class="item" data-value="2">NO</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    @else
                        <input type="hidden" name="controversyJnci[id][]">
                        <div class="three fields">
                            <div class="required field">
                                <label>Parte interesada que controvierte</label>
                                <div class="ui fluid selection dropdown">
                                    <input name="controversyJnci[interested_party_disputes][]" type="hidden">
                                    <i class="dropdown icon"></i>
                                    <div class="default text">Seleccione uno</div>
                                    <div class="menu">
                                        @foreach($MEETING_INTERESTED_PARTY_THAT_DISPUTES as $k => $v)
                                            <div class="item" data-value="{{$k}}">{{$v}}</div>
                                        @endforeach
                                    </div>
                                </div>
                            </div>
                            <div class="required field">
                                <label>Fecha notificación pago de honorarios</label>
                                <input name="controversyJnci[file_filing_date][]" class="datepicker"/>
                            </div>
                            <div class="required field">
                                <label>Valor honorarios JNCI</label>
                                <input name="controversyJnci[value_fees][]" type="number"/>
                            </div>
                            <div class="required field">
                                <label>Fecha de controversia</label>
                                <input name="controversyJnci[controversy_date][]" class="datepicker"/>
                            </div>
                        </div>
                        <div class="four fields">
                            <div class="required field">
                                <label>Junta regional que emitió el dictamen</label>
                                <input readonly name="controversyJnci[regional_board_issued_opinion][]" type="text">
                            </div>
                            <div class="required field">
                                <label>Motivo de controversia</label>
                                <textarea name="controversyJnci[reason_controversy][]" rows="2"></textarea>
                            </div>
                            <div class="required field">
                                <label>Estado Apelación</label>
                                <div class="ui fluid selection dropdown">
                                    <input name="controversyJnci[appeal_status][]" type="hidden">
                                    <i class="dropdown icon"></i>
                                    <div class="default text">Seleccione uno</div>
                                    <div class="menu">
                                        @foreach($MEETING_APPEAL_STATUS as $k => $v)
                                            <div class="item" data-value="{{$k}}">{{$v}}</div>
                                        @endforeach
                                    </div>
                                </div>
                            </div>
                            <div class="required field">
                                <label>Requiere seguimiento 60 días</label>
                                <div class="ui fluid selection dropdown">
                                    <input name="controversyJnci[requires_follow_sixty_days][]" type="hidden">
                                    <i class="dropdown icon"></i>
                                    <div class="default text">Seleccione uno</div>
                                    <div class="menu">
                                        <div class="item" data-value="1">SI</div>
                                        <div class="item" data-value="2">NO</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>
                <div id="92" class="title">
                    <i class="dropdown icon"></i>Seguimiento JNCI<span style="color: red;" class="required">*</span>
                </div>
                <div id="tracingJnci" class="content">
                    <div style="position: relative" id="pcl_dictumJRCIPclBoard">
                        <div style="position: absolute; right:0;top: -2.5rem;z-index: 2">
                            <a onclick="addTracingJnci()" class="ui basic small icon blue button">
                                <i class="add icon"></i>
                            </a>
                        </div>
                    </div>
                    @if ($activity->meeting && count($activity->meeting->jnciTracings) > 0)
                        @foreach ($activity->meeting->jnciTracings as $jnciTracing)
                            <input type="hidden" name="tracingJnci[id][]" value="{{$jnciTracing->id}}">
                            <div class="four fields">
                                <div class="required field">
                                    <label>Fecha remisión carta</label>
                                    <input data-value="{{$jnciTracing->letter_remittance_date}}"
                                           name="tracingJnci[letter_remittance_date][]" class="datepicker"/>
                                </div>
                                <div class="required field">
                                    <label>Medio de envío</label>
                                    <div class="ui fluid selection dropdown">
                                        <input value="{{$jnciTracing->average_costs}}"
                                               name="tracingJnci[average_costs][]" type="hidden">
                                        <i class="dropdown icon"></i>
                                        <div class="default text">Seleccione uno</div>
                                        <div class="menu">
                                            @foreach($MEETING_JRCI_AVERAGE_COST as $k => $v)
                                                <div class="item" data-value="{{$k}}">{{$v}}</div>
                                            @endforeach
                                        </div>
                                    </div>
                                </div>
                                <div class="required field">
                                    <label>Fecha notificación pago Junta</label>
                                    <input data-value="{{$jnciTracing->board_payment_notification_date}}"
                                           name="tracingJnci[board_payment_notification_date][]" class="datepicker"/>
                                </div>
                                <div class="required field">
                                    <label>Carta medio de evento</label>
                                    @if ($jnciTracing->medium_event_letter != null)
                                        <a class="ui green basic label"
                                           href="{{secure_url('file/'.$jnciTracing->medium_event_letter)}}"
                                           target="_blank">
                                            <i class="file icon">
                                            </i> VER
                                        </a>
                                    @else
                                        <input id="notificationsfile" name="tracingJnci[notificationsfile][]"
                                               type="file" onchange="uploadFile()">
                                        <input id="notificationsfile_text" name="tracingJnci[medium_event_letter][]"
                                               type="hidden"/>
                                        <p></p>
                                    @endif
                                </div>
                            </div>
                            <div class="four fields">
                                <div class="required field">
                                    <label>Fecha alerta nuevo seguimiento</label>
                                    <input data-value="{{$jnciTracing->new_follow_alert_date}}"
                                           name="tracingJnci[new_follow_alert_date][]" class="datepicker"/>
                                </div>
                                <div class="required field">
                                    <label>Notificación efectiva</label>
                                    <div class="ui fluid selection dropdown">
                                        <input value="{{$jnciTracing->effective_notification}}"
                                               name="tracingJnci[effective_notification][]" type="hidden">
                                        <i class="dropdown icon"></i>
                                        <div class="default text">Seleccione uno</div>
                                        <div class="menu">
                                            <div class="item" data-value="1">SI</div>
                                            <div class="item" data-value="2">NO</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="required field">
                                    <label>Fecha Notificación efectiva</label>
                                    <input data-value="{{$jnciTracing->effective_notification_date}}"
                                           name="tracingJnci[effective_notification_date][]" class="datepicker"/>
                                </div>
                                <div class="required field">
                                    <label>Requiere seguimiento adicional</label>
                                    <div class="ui fluid selection dropdown">
                                        <input value="{{$jnciTracing->requires_additional_follow_up}}"
                                               name="tracingJnci[requires_additional_follow_up][]" type="hidden">
                                        <i class="dropdown icon"></i>
                                        <div class="default text">Seleccione uno</div>
                                        <div class="menu">
                                            <div class="item" data-value="1">SI</div>
                                            <div onclick="enableDicFromSeg()" class="item" data-value="2">NO</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    @else
                        <input type="hidden" name="tracingJnci[id][]">
                        <div style="position: relative" id="pcl_dictumJRCIPclBoard">
                            <div style="position: absolute; right:0;top: -2.5rem;z-index: 2">
                                <a onclick="addTracingJnci()" class="ui basic small icon blue button">
                                    <i class="add icon"></i>
                                </a>
                            </div>
                        </div>
                        <div class="four fields">
                            <div class="required field">
                                <label>Fecha remisión carta</label>
                                <input name="tracingJnci[letter_remittance_date][]" class="datepicker"/>
                            </div>
                            <div class="required field">
                                <label>Medio de envío</label>
                                <div class="ui fluid selection dropdown">
                                    <input name="tracingJnci[average_costs][]" type="hidden">
                                    <i class="dropdown icon"></i>
                                    <div class="default text">Seleccione uno</div>
                                    <div class="menu">
                                        @foreach($MEETING_JRCI_AVERAGE_COST as $k => $v)
                                            <div class="item" data-value="{{$k}}">{{$v}}</div>
                                        @endforeach
                                    </div>
                                </div>
                            </div>
                            <div class="required field">
                                <label>Fecha notificación pago Junta</label>
                                <input name="tracingJnci[board_payment_notification_date][]" class="datepicker"/>
                            </div>
                            <div class="required field">
                                <label>Carta medio de evento</label>
                                <input id="notificationsfile" name="tracingJnci[medium_event_letter][]"
                                       type="file" onchange="uploadFile()">
                                <input id="notificationsfile_text" name="tracingJnci[medium_event_letter][]"
                                       type="hidden"/>
                                <p></p>
                            </div>
                        </div>
                        <div class="four fields">
                            <div class="required field">
                                <label>Fecha alerta nuevo seguimiento</label>
                                <input name="tracingJnci[new_follow_alert_date][]" class="datepicker"/>
                            </div>
                            <div class="required field">
                                <label>Notificación efectiva</label>
                                <div class="ui fluid selection dropdown">
                                    <input name="tracingJnci[effective_notification][]" type="hidden">
                                    <i class="dropdown icon"></i>
                                    <div class="default text">Seleccione uno</div>
                                    <div class="menu">
                                        <div class="item" data-value="1">SI</div>
                                        <div class="item" data-value="2">NO</div>
                                    </div>
                                </div>
                            </div>
                            <div class="required field">
                                <label>Fecha Notificación efectiva</label>
                                <input name="tracingJnci[effective_notification_date][]" class="datepicker"/>
                            </div>
                            <div class="required field">
                                <label>Requiere seguimiento adicional</label>
                                <div class="ui fluid selection dropdown">
                                    <input name="tracingJnci[requires_additional_follow_up][]" type="hidden">
                                    <i class="dropdown icon"></i>
                                    <div class="default text">Seleccione uno</div>
                                    <div class="menu">
                                        <div class="item" data-value="1">SI</div>
                                        <div onclick="enableDicFromSeg()" class="item" data-value="2">NO</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>
                <div id="93" class="title">
                    <i class="dropdown icon"></i>Dictamen JNCI<span style="color: red;" class="required">*</span>
                </div>
                <div class="content">
                    <div class="five fields">
                        <div class="required field">
                            <label>Fecha Solicitud pago de honorarios JNCI</label>
                            <input data-value="{{$activity->meeting ? $activity->meeting->request_payment_jnci_fees_date : ''}}"
                                   name="request_payment_jnci_fees_date" class="datepicker"/>
                        </div>
                        <div class="required field">
                            <label>Fecha pago de honorarios JNCI</label>
                            <input data-value="{{$activity->meeting ? $activity->meeting->jnci_fee_payment_date : ''}}"
                                   name="jnci_fee_payment_date" class="datepicker"/>
                        </div>
                        <div class="required field">
                            <label>Numero orden pago JNCI</label>
                            <textarea name="jnci_payment_order_number"
                                      rows="2">{{$activity->meeting ? $activity->meeting->jnci_payment_order_number : ''}}</textarea>
                        </div>
                        <div class="required field">
                            <label>No. Dictamen JNCI</label>
                            <textarea name="jnci_report_number"
                                      rows="2">{{$activity->meeting ? $activity->meeting->jnci_report_number : ''}}</textarea>
                        </div>
                        <div class="required field">
                            <label>Fecha Dictamen JNCI</label>
                            <input data-value="{{$activity->meeting ? $activity->meeting->jnci_dictum_date : ''}}"
                                   name="jnci_dictum_date" class="datepicker"/>
                        </div>
                    </div>
                    <div class="two fields">
                        <div class="required field">
                            <label>Fecha de notificación a Equidad</label>
                            <input data-value="{{$activity->meeting ? $activity->meeting->notification_equidad_date : ''}}"
                                   name="notification_equidad_date" class="datepicker"/>
                        </div>
                        <div class="field">
                            <label>% PCL JNCI</label>
                            <input value="{{$activity->meeting ? $activity->meeting->porcentage_jnci_pcl : ''}}"
                                   name="porcentage_jnci_pcl" type="number"/>
                        </div>
                    </div>
                    <div style="margin-bottom: 5px;" class="fields">
                        <div class="four wide required field">
                            <label>Código CIE 10</label>
                        </div>
                        <div class="four wide required field">
                            <label>Nombre del Diagnóstico</label>
                        </div>
                        <div class="four wide required field">
                            <label>Origen</label>
                        </div>
                        <div class="one wide field">
                            <a style="margin-top: -15px;" onclick="addJnciDiagnostics()"
                               class="ui basic small icon blue button"><i class="add icon"></i></a>
                        </div>
                    </div>
                    <div id="jnciDiagnostics">
                        @if ($activity->meeting && count($activity->meeting->jnciDiagnostics) > 0)
                            @foreach ($activity->meeting->jnciDiagnostics as $diagnostic)
                                <div class="fields">
                                    <input type="hidden" name="jnciDiagnostics[id][]" value="{{$diagnostic->id}}">
                                    <div class="four wide required field">
                                        <div class="ui search code">
                                            <div class="ui icon input">
                                                <input class="prompt" name="jnciDiagnostics[cod][]" type="text"
                                                       value="{{$diagnostic->code}}">
                                                <i class="search icon"></i>
                                            </div>
                                            <div class="results"></div>
                                        </div>
                                    </div>
                                    <div class="five wide required field">
                                        <input class="prompt" name="jnciDiagnostics[description][]" type="text"
                                               value="{{$diagnostic->description}}">
                                    </div>
                                    <div class="four wide field">
                                        <div class="ui fluid selection dropdown">
                                            <input name="jnciDiagnostics[origin][]" type="hidden"
                                                   value="{{$diagnostic->origin}}">
                                            <i class="dropdown icon"></i>
                                            <div class="default text">Origen</div>
                                            <div class="menu">
                                                @foreach($ORIGINS as $k => $v)
                                                    <div class="item" data-value="{{$k}}">{{$v}}</div>
                                                @endforeach
                                            </div>
                                        </div>
                                    </div>
                                    <div class="one wide field">
                                        <a class="ui red small icon basic button"><i class="remove icon"></i></a>
                                    </div>
                                </div>
                            @endforeach
                        @else
                            <div class="fields">
                                <input type="hidden" name="jnciDiagnostics[id][]">
                                <div class="four wide required field">
                                    <div class="ui search code">
                                        <div class="ui icon input">
                                            <input class="code prompt" name="jnciDiagnostics[cod][]" type="text">
                                            <i class="search icon"></i>
                                        </div>
                                        <div class="results"></div>
                                    </div>
                                </div>
                                <div class="four wide required field">
                                    <input class="description prompt" name="jnciDiagnostics[description][]" type="text">
                                </div>
                                <div class="required four wide field">
                                    <div class="ui fluid selection dropdown">
                                        <input name="jnciDiagnostics[origin][]" type="hidden">
                                        <i class="dropdown icon"></i>
                                        <div class="default text">Origen</div>
                                        <div class="menu">
                                            @foreach($ORIGINS as $k => $v)
                                                <div class="item" data-value="{{$k}}">{{$v}}</div>
                                            @endforeach
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
                <div id="94" class="title">
                    <i class="dropdown icon"></i>NO CONFORMIDAD
                </div>
                <div class="content form">
                    <div style="margin-bottom: 5px;" class="fields">
                        <div class="six wide field">
                            <label>Descripción</label>
                        </div>
                        <div class="one wide field">
                            <a style="margin-top: -15px;" onclick="addNc()"
                               class="ui basic small icon blue button"><i
                                        class="add icon"></i></a>
                        </div>
                    </div>
                    <div id="ncs">
                        @if ($activity->meeting && $activity->meeting->ncs && count($activity->meeting->ncs) > 0)
                            @foreach ($activity->meeting->ncs as $nc)
                                <div class="fields">
                                    <input type="hidden" name="ncs[id][]" value="{{$nc->id}}">
                                    <div class="six wide field">
                                        <textarea name="ncs[description][]"
                                                  rows="2">{{$nc->description}}</textarea>
                                    </div>
                                    <div class="one wide field">
                                        <a class="ui red small icon basic button"><i
                                                    class="remove icon"></i></a>
                                    </div>
                                </div>
                            @endforeach
                        @else
                            <div class="fields">
                                <input type="hidden" name="ncs[id][]">
                                <div class="six wide field">
                                    <textarea name="ncs[description][]" rows="2"></textarea>
                                </div>
                                <div class="one wide field"></div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
            <div class="ui basic segment">
                <div class="fields">
                    <div class="four wide field">
                        <button id="button_save" class="ui green fluid button"><i class="save icon"></i> Guardar
                        </button>
                    </div>
                    <div class="six wide field">
                        <a href="{{secure_url('/servicio/' . $activity->id . '/meeting/pdf')}}"
                           class="ui basic small button" target="_blank"
                           onclick="event.preventDefault(); generatePDF();">
                            <i class="file pdf outline red icon"></i> Vista previa
                        </a>
                        <a href="{{secure_url('/servicio/' . $activity->id)}}" class="ui basic small button"><i
                                    class="arrow left icon"></i> Volver a la actividad</a>
                    </div>
                </div>
            </div>
            {{csrf_field()}}
        </form>
    </div>
    <!-- MODELS -->
    <!-- NC MODEL -->
    <div id="nc_model" style="display: none;" class="fields">
        <input type="hidden" name="ncs[id][]">
        <div class="six wide field">
            <textarea name="ncs[description][]" rows="2"></textarea>
        </div>
        <div class="one wide field">
            <a class="ui red small icon basic button"><i class="remove icon"></i></a>
        </div>
    </div>
    <!-- DIAGNOSTIC MODEL -->
    <div id="diagnostic_model" style="display: none;" class="fields">
        <input type="hidden" name="diagnostics[id][]">
        <div class="four wide required field">
            <div class="ui search">
                <div class="ui icon input">
                    <input class="prompt" name="diagnostics[cod][]" type="text">
                    <i class="search icon"></i>
                </div>
                <div class="results"></div>
            </div>
        </div>
        <div class="four wide required field">
            <input class="description" name="diagnostics[description][]" type="text">
        </div>
        <div class="required four wide field">
            <div class="ui fluid selection dropdown">
                <input name="diagnostics[origin][]" type="hidden">
                <i class="dropdown icon"></i>
                <div class="default text">Origen</div>
                <div class="menu">
                    @foreach($ORIGINS as $k => $v)
                        <div class="item" data-value="{{$k}}">{{$v}}</div>
                    @endforeach
                </div>
            </div>
        </div>
        <div class="one wide field">
            <a class="ui red small icon basic button"><i class="remove icon"></i></a>
        </div>
    </div>
    <!-- JRCI DIAGNOSTIC -->
    <div id="jrci_diagnostic_model" style="display: none;" class="fields">
        <input type="hidden" name="jrciDiagnostics[id][]">
        <div class="four wide required field">
            <div class="ui search">
                <div class="ui icon input">
                    <input class="prompt" name="jrciDiagnostics[cod][]" type="text">
                    <i class="search icon"></i>
                </div>
                <div class="results"></div>
            </div>
        </div>
        <div class="four wide required field">
            <input class="description" name="jrciDiagnostics[description][]" type="text">
        </div>
        <div class="required four wide field">
            <div class="ui fluid selection dropdown">
                <input name="jrciDiagnostics[origin][]" type="hidden">
                <i class="dropdown icon"></i>
                <div class="default text">Origen</div>
                <div class="menu">
                    @foreach($ORIGINS as $k => $v)
                        <div class="item" data-value="{{$k}}">{{$v}}</div>
                    @endforeach
                </div>
            </div>
        </div>
        <div class="one wide field">
            <a class="ui red small icon basic button"><i class="remove icon"></i></a>
        </div>
    </div>
    <!-- JNCI DIAGNOSTICS -->
    <div id="jnci_diagnostic_model" style="display: none;" class="fields">
        <input type="hidden" name="jnciDiagnostics[id][]">
        <div class="four wide required field">
            <div class="ui search">
                <div class="ui icon input">
                    <input class="prompt" name="jnciDiagnostics[cod][]" type="text">
                    <i class="search icon"></i>
                </div>
                <div class="results"></div>
            </div>
        </div>
        <div class="four wide required field">
            <input class="description" name="jnciDiagnostics[description][]" type="text">
        </div>
        <div class="required four wide field">
            <div class="ui fluid selection dropdown">
                <input name="jnciDiagnostics[origin][]" type="hidden">
                <i class="dropdown icon"></i>
                <div class="default text">Origen</div>
                <div class="menu">
                    @foreach($ORIGINS as $k => $v)
                        <div class="item" data-value="{{$k}}">{{$v}}</div>
                    @endforeach
                </div>
            </div>
        </div>
        <div class="one wide field">
            <a class="ui red small icon basic button"><i class="remove icon"></i></a>
        </div>
    </div>
    <!-- MORE DOCUMENTS MODEL -->
    <div id="attachedDocumentsModel" style="display: none;">
        <input type="hidden" name="attachedDocuments[id][]">
        <div class="three fields">
            <input name="attachedDocuments[documents][]" type="text">
            <div class="ui fluid selection dropdown">
                <input name="attachedDocuments[options][]" type="hidden">
                <i class="dropdown icon"></i>
                <div class="default text">Opciones</div>
                <div class="menu">
                    <div class="item" data-value="1">SI</div>
                    <div class="item" data-value="2">NO</div>
                    <div class="item" data-value="3">N/A</div>
                </div>
            </div>
            <a class="ui red small icon basic button"><i class="remove icon"></i></a>
        </div>
    </div>
    <!-- TRACINGS JRCI MODEL -->
    <div id="tracingsJrciModel" style="display: none;">
        <div style="position: relative" id="pcl_dictumJRCIPclBoard">
            <div style="position: absolute; right:0;top: 0.4rem;z-index: 2">
                <a class="ui red small icon basic button"><i class="remove icon"></i></a>
            </div>
        </div>
        <input type="hidden" name="tracingJrci[id][]">
        <div class="ui divider"></div>
        <div class="four fields">
            <div class="required field">
                <label>Fecha remisión carta</label>
                <input name="tracingJrci[letter_remittance_date][]" class="datepicker"/>
            </div>
            <div class="required field">
                <label>Medio de envío</label>
                <div class="ui fluid selection dropdown">
                    <input name="tracingJrci[average_costs][]" type="hidden">
                    <i class="dropdown icon"></i>
                    <div class="default text">Seleccione uno</div>
                    <div class="menu">
                        @foreach($MEETING_JRCI_AVERAGE_COST as $k => $v)
                            <div class="item" data-value="{{$k}}">{{$v}}</div>
                        @endforeach
                    </div>
                </div>
            </div>
            <div class="required field">
                <label>Fecha notificación pago Junta</label>
                <input name="tracingJrci[board_payment_notification_date][]" class="datepicker"/>
            </div>
            <div class="required field">
                <label>Carta medio de evento</label>
                <input id="notificationsfile" name="tracingJrci[notificationsfile][]"
                       type="file" onchange="uploadFile()">
                <input id="notificationsfile_text" name="tracingJrci[medium_event_letter][]"
                       type="hidden"/>
                <p></p>
            </div>
        </div>
        <div class="four fields">
            <div class="required field">
                <label>Fecha alerta nuevo seguimiento</label>
                <input name="tracingJrci[new_follow_alert_date][]" class="datepicker2"/>
            </div>
            <div class="required field">
                <label>Notificación efectiva</label>
                <div class="ui fluid selection dropdown">
                    <input name="tracingJrci[effective_notification][]" type="hidden">
                    <i class="dropdown icon"></i>
                    <div class="default text">Seleccione uno</div>
                    <div class="menu">
                        <div class="item" data-value="1">SI</div>
                        <div class="item" data-value="2">NO</div>
                    </div>
                </div>
            </div>
            <div class="required field">
                <label>Fecha Notificación efectiva</label>
                <input name="tracingJrci[effective_notification_date][]" class="datepicker"/>
            </div>
            <div class="required field">
                <label>Requiere seguimiento adicional</label>
                <div class="ui fluid selection dropdown">
                    <input name="tracingJrci[requires_additional_follow_up][]" type="hidden">
                    <i class="dropdown icon"></i>
                    <div class="default text">Seleccione uno</div>
                    <div class="menu">
                        <div class="item" data-value="1">SI</div>
                        <div onclick="enableDicFromSeg()" class="item" data-value="2">NO</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- CONTROVERSY JNCI MODEL -->
    <div id="controversyJnciModel" style="display: none;">
        <div style="position: relative" id="pcl_dictumJRCIPclBoard">
            <div style="position: absolute; right:0;top: 0.4rem;z-index: 2">
                <a class="ui red small icon basic button"><i class="remove icon"></i></a>
            </div>
        </div>
        <input type="hidden" name="controversyJnci[id][]">
        <div class="ui divider"></div>
        <div class="three fields">
            <div class="required field">
                <label>Parte interesada que controvierte</label>
                <div class="ui fluid selection dropdown">
                    <input name="controversyJnci[interested_party_disputes][]" type="hidden">
                    <i class="dropdown icon"></i>
                    <div class="default text">Seleccione uno</div>
                    <div class="menu">
                        @foreach($MEETING_INTERESTED_PARTY_THAT_DISPUTES as $k => $v)
                            <div class="item" data-value="{{$k}}">{{$v}}</div>
                        @endforeach
                    </div>
                </div>
            </div>
            <div class="required field">
                <label>Fecha notificación pago de honorarios</label>
                <input name="controversyJnci[file_filing_date][]" class="datepicker"/>
            </div>
            <div class="required field">
                <label>Valor honorarios JNCI</label>
                <input name="controversyJnci[value_fees][]" type="number"/>
            </div>
            <div class="required field">
                <label>Fecha de controversia</label>
                <input name="controversyJnci[controversy_date][]" class="datepicker"/>
            </div>
        </div>
        <div class="four fields">
            <div class="required field">
                <label>Junta regional que emitió el dictamen</label>
                <input readonly name="controversyJnci[regional_board_issued_opinion][]" type="text">
            </div>
            <div class="required field">
                <label>Motivo de controversia</label>
                <textarea name="controversyJnci[reason_controversy][]" rows="2"></textarea>
            </div>
            <div class="required field">
                <label>Estado Apelación</label>
                <div class="ui fluid selection dropdown">
                    <input name="controversyJnci[appeal_status][]" type="hidden">
                    <i class="dropdown icon"></i>
                    <div class="default text">Seleccione uno</div>
                    <div class="menu">
                        @foreach($MEETING_APPEAL_STATUS as $k => $v)
                            <div class="item" data-value="{{$k}}">{{$v}}</div>
                        @endforeach
                    </div>
                </div>
            </div>
            <div class="required field">
                <label>Requiere seguimiento 60 días</label>
                <div class="ui fluid selection dropdown">
                    <input name="controversyJnci[requires_follow_sixty_days][]" type="hidden">
                    <i class="dropdown icon"></i>
                    <div class="default text">Seleccione uno</div>
                    <div class="menu">
                        <div class="item" data-value="1">SI</div>
                        <div class="item" data-value="2">NO</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- TRACINGS JNCI MODEL -->
    <div id="tracingJnciModel" style="display: none;">
        <div style="position: relative" id="pcl_dictumJRCIPclBoard">
            <div style="position: absolute; right:0;top: 0.4rem;z-index: 2">
                <a class="ui red small icon basic button"><i class="remove icon"></i></a>
            </div>
        </div>
        <input type="hidden" name="tracingJnci[id][]">
        <div class="ui divider"></div>
        <div class="four fields">
            <div class="required field">
                <label>Fecha remisión carta</label>
                <input name="tracingJnci[letter_remittance_date][]" class="datepicker"/>
            </div>
            <div class="required field">
                <label>Medio de envío</label>
                <div class="ui fluid selection dropdown">
                    <input name="tracingJnci[average_costs][]" type="hidden">
                    <i class="dropdown icon"></i>
                    <div class="default text">Seleccione uno</div>
                    <div class="menu">
                        @foreach($MEETING_JRCI_AVERAGE_COST as $k => $v)
                            <div class="item" data-value="{{$k}}">{{$v}}</div>
                        @endforeach
                    </div>
                </div>
            </div>
            <div class="required field">
                <label>Fecha notificación pago Junta</label>
                <input name="tracingJnci[board_payment_notification_date][]" class="datepicker"/>
            </div>
            <div class="required field">
                <label>Carta medio de evento</label>
                <input id="notificationsfile" name="tracingJnci[notificationsfile][]"
                       type="file" onchange="uploadFile()">
                <input id="notificationsfile_text" name="tracingJnci[medium_event_letter][]"
                       type="hidden"/>
                <p></p>
            </div>
        </div>
        <div class="four fields">
            <div class="required field">
                <label>Fecha alerta nuevo seguimiento</label>
                <input name="tracingJnci[new_follow_alert_date][]" class="datepicker"/>
            </div>
            <div class="required field">
                <label>Notificación efectiva</label>
                <div class="ui fluid selection dropdown">
                    <input name="tracingJnci[effective_notification][]" type="hidden">
                    <i class="dropdown icon"></i>
                    <div class="default text">Seleccione uno</div>
                    <div class="menu">
                        <div class="item" data-value="1">SI</div>
                        <div class="item" data-value="2">NO</div>
                    </div>
                </div>
            </div>
            <div class="required field">
                <label>Fecha Notificación efectiva</label>
                <input name="tracingJnci[effective_notification_date][]" class="datepicker"/>
            </div>
            <div class="required field">
                <label>Requiere seguimiento adicional</label>
                <div class="ui fluid selection dropdown">
                    <input name="tracingJnci[requires_additional_follow_up][]" type="hidden">
                    <i class="dropdown icon"></i>
                    <div class="default text">Seleccione uno</div>
                    <div class="menu">
                        <div class="item" data-value="1">SI</div>
                        <div onclick="enableDicFromSeg()" class="item" data-value="2">NO</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- INTERESTED PARTS MODEL -->
    <div id="interestedPartsModel" style="display: none;">
        <div class="fields">
            <input type="hidden" name="interestedParts[id][]">
            <div class="five wide required field">
                <div class="ui fluid selection dropdown">
                    <input name="interestedParts[interested_part][]" type="hidden">
                    <i class="dropdown icon"></i>
                    <div class="default text">Seleccione uno</div>
                    <div class="menu">
                        @foreach($MEETING_INTERESTED_PARTY_THAT_DISPUTES as $k => $v)
                            <div class="item" data-value="{{$k}}">{{$v}}</div>
                        @endforeach
                    </div>
                </div>
            </div>
            <div class="one wide field">
                <a class="ui red small icon basic button"><i class="remove icon"></i></a>
            </div>
        </div>
    </div>
    <style type="text/css">
        .ui.grid .column {
            padding: 0.5rem 1rem !important;
        }

        .jr.field {
            display: none;
        }

        .jn.field {
            display: none;
        }

        .ui.accordion .title {
            text-transform: uppercase;
        }

        .field > h3 {
            text-align: center;
            margin-top: 1.25rem;
        }

        .ui.search > .results {
            width: 30rem;
        }

        .ui.search > .results .result .title {
            padding: 0 !important;
            border: none !important;
            text-transform: none;
        }

        .ui.search > .results .result .content {
            padding: 0 !important;
        }
    </style>
    <script type="text/javascript">
        // add nc
        var addNc = function () {
            var $fields = $('#nc_model').clone(true);
            $fields.removeAttr('id');
            $fields.find('a').click(function () {
                $(this).parent().parent().remove();
            });
            $('#ncs').append($fields);
            $fields.show();
            return false;
        };
        let enableTracing = () => {
            alert("f")
            $("#89").click(function () {
                alert("khe")
            });
        }

        let enableDictum = () => {
            $("#dicjrci").show('slow');
            $("#89").hide('slow');
        }

        let enableDicFromSeg = () => {
            $("#dicjrci").show('slow');
        }

        let addIntertestedParts = () => {
            var $fields = $('#interestedPartsModel').clone(true);
            $fields.removeAttr('id');
            $fields.find('.ui.dropdown').dropdown({
                forceSelection: false
            });
            $fields.find('a').click(function () {
                $(this).parent().parent().remove();
            });
            $('#interestedParts').append($fields);
            $fields.show();
            return false;
        }

        let clicks = 0;
        let addMoreAttachedDocuments = e => {
            var $fields = $('#attachedDocumentsModel').clone(true);
            $fields.removeAttr('id');
            $fields.find('.ui.dropdown').dropdown({
                forceSelection: false
            });
            $fields.find('a').click(function () {
                $(this).parent().remove();
            });
            $('#moreAttachedDocuments').append($fields);
            $fields.show();
            return false;
        }

        let addDiagnosticDictum = e => {
            var $fields = $('#diagnostic_model').clone(true);
            $fields.removeAttr('id');
            $fields.find('a').click(function () {
                $(this).parent().parent().remove();
            });
            $fields.find('.ui.dropdown').dropdown({
                forceSelection: false
            });
            $fields.find('.datepicker').pickadate({
                selectYears: 100,
                selectMonths: true,
                max: new Date(),
                formatSubmit: 'yyyy-mm-dd',
                format: 'mmm dd, yyyy',
            });
            $fields.find('.ui.search input').change(function () {
                var valid = false;

                $(this).parents('.fields').find('input.description').val('');
                $(this).parents('.fields').find('input.description_editable').val('');

                for (var i = 0; i < cie10.length; i++) {
                    if (cie10[i].COD == $(this).val().toUpperCase()) {
                        $(this).parents('.fields').find('input.description').val(cie10[i].DESCRIPTION);
                        $(this).parents('.fields').find('input.description_editable').val(cie10[i].DESCRIPTION);
                        checkDiagnosticCode($(this), cie10[i].COD);
                        valid = true;
                    }
                }

                if (!valid && $(this).val() != '') {
                    $(this).val('');
                }
            });
            $fields.find('.ui.search').search({
                source: cie10,
                fields: {
                    title: 'COD',
                    description: 'DESCRIPTION'
                },
                searchFields: ['COD', 'DESCRIPTION'],
                regExp: {
                    escape: /[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,
                    beginsWith: ''
                },
                showNoResults: true,
                maxResults: 250,
                searchFullText: false,
                error: {
                    noResults: 'No se encontraron resultados para tu búsqueda.'
                },
                onSelect: function (result, response) {
                    $(this).parents('.fields').find('input.description').val(result.DESCRIPTION);
                }
            });
            $('#diagnosticsDictum').append($fields);
            $fields.show();
            return false;
        }

        let addjrciDiagnostics = () => {
            var $fields = $('#jrci_diagnostic_model').clone(true);
            $fields.removeAttr('id');
            $fields.find('a').click(function () {
                $(this).parent().parent().remove();
            });
            $fields.find('.ui.dropdown').dropdown({
                forceSelection: false
            });
            $fields.find('.datepicker').pickadate({
                selectYears: 100,
                selectMonths: true,
                max: new Date(),
                formatSubmit: 'yyyy-mm-dd',
                format: 'mmm dd, yyyy',
            });
            $fields.find('.ui.search input').change(function () {
                var valid = false;

                $(this).parents('.fields').find('input.description').val('');
                $(this).parents('.fields').find('input.description_editable').val('');

                for (var i = 0; i < cie10.length; i++) {
                    if (cie10[i].COD == $(this).val().toUpperCase()) {
                        $(this).parents('.fields').find('input.description').val(cie10[i].DESCRIPTION);
                        $(this).parents('.fields').find('input.description_editable').val(cie10[i].DESCRIPTION);
                        checkDiagnosticCode($(this), cie10[i].COD);
                        valid = true;
                    }
                }

                if (!valid && $(this).val() != '') {
                    $(this).val('');
                }
            });
            $fields.find('.ui.search').search({
                source: cie10,
                fields: {
                    title: 'COD',
                    description: 'DESCRIPTION'
                },
                searchFields: ['COD', 'DESCRIPTION'],
                regExp: {
                    escape: /[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,
                    beginsWith: ''
                },
                showNoResults: true,
                maxResults: 250,
                searchFullText: false,
                error: {
                    noResults: 'No se encontraron resultados para tu búsqueda.'
                },
                onSelect: function (result, response) {
                    $(this).parents('.fields').find('input.description').val(result.DESCRIPTION);
                }
            });
            $('#jrciDiagnostics').append($fields);
            $fields.show();
            return false;
        }

        let addJnciDiagnostics = () => {
            var $fields = $('#jnci_diagnostic_model').clone(true);
            $fields.removeAttr('id');
            $fields.find('a').click(function () {
                $(this).parent().parent().remove();
            });
            $fields.find('.ui.dropdown').dropdown({
                forceSelection: false
            });
            $fields.find('.datepicker').pickadate({
                selectYears: 100,
                selectMonths: true,
                max: new Date(),
                formatSubmit: 'yyyy-mm-dd',
                format: 'mmm dd, yyyy',
            });
            $fields.find('.ui.search input').change(function () {
                var valid = false;

                $(this).parents('.fields').find('input.description').val('');
                $(this).parents('.fields').find('input.description_editable').val('');

                for (var i = 0; i < cie10.length; i++) {
                    if (cie10[i].COD == $(this).val().toUpperCase()) {
                        $(this).parents('.fields').find('input.description').val(cie10[i].DESCRIPTION);
                        $(this).parents('.fields').find('input.description_editable').val(cie10[i].DESCRIPTION);
                        checkDiagnosticCode($(this), cie10[i].COD);
                        valid = true;
                    }
                }

                if (!valid && $(this).val() != '') {
                    $(this).val('');
                }
            });
            $fields.find('.ui.search').search({
                source: cie10,
                fields: {
                    title: 'COD',
                    description: 'DESCRIPTION'
                },
                searchFields: ['COD', 'DESCRIPTION'],
                regExp: {
                    escape: /[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,
                    beginsWith: ''
                },
                showNoResults: true,
                maxResults: 250,
                searchFullText: false,
                error: {
                    noResults: 'No se encontraron resultados para tu búsqueda.'
                },
                onSelect: function (result, response) {
                    $(this).parents('.fields').find('input.description').val(result.DESCRIPTION);
                }
            });
            $('#jnciDiagnostics').append($fields);
            $fields.show();
            return false;
        }

        let addTracingJrci = e => {
            var $fields = $('#tracingsJrciModel').clone(true);
            $fields.removeAttr('id');
            $fields.find('a').click(function () {
                $(this).parent().parent().parent().remove();
            });
            $fields.find('.datepicker').pickadate({
                selectYears: 100,
                selectMonths: true,
                max: new Date(),
                formatSubmit: 'yyyy-mm-dd',
                format: 'mmm dd, yyyy',
            });
            $fields.find('.datepicker2').pickadate({
                selectYears: 100,
                selectMonths: true,
                formatSubmit: 'yyyy-mm-dd',
                format: 'mmm dd, yyyy',
            });
            $fields.find('.ui.dropdown').dropdown({
                forceSelection: false
            });
            $('#tracingJrci').append($fields);
            $fields.show();
            return false;
        }

        let addControversyJnci = e => {
            var $fields = $('#controversyJnciModel').clone(true);
            $fields.removeAttr('id');
            $fields.find('a').click(function () {
                $(this).parent().parent().parent().remove();
            });
            $fields.find('.datepicker').pickadate({
                selectYears: 100,
                selectMonths: true,
                max: new Date(),
                formatSubmit: 'yyyy-mm-dd',
                format: 'mmm dd, yyyy',
            });
            $fields.find('.ui.dropdown').dropdown({
                forceSelection: false
            });
            $('#controversyJnci').append($fields);
            $fields.show();
            return false;
        }

        let addTracingJnci = () => {
            var $fields = $('#tracingJnciModel').clone(true);
            $fields.removeAttr('id');
            $fields.find('a').click(function () {
                $(this).parent().parent().parent().remove();
            });
            $fields.find('.datepicker').pickadate({
                selectYears: 100,
                selectMonths: true,
                max: new Date(),
                formatSubmit: 'yyyy-mm-dd',
                format: 'mmm dd, yyyy',
            });
            $fields.find('.ui.dropdown').dropdown({
                forceSelection: false
            });
            $('#tracingJnci').append($fields);
            $fields.show();
            return false;
        }
    </script>
    <script type="text/javascript">
        var makeUIDF = function (length) {
            var text = "";
            var possible = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";

            for (var i = 0; i < length; i++)
                text += possible.charAt(Math.floor(Math.random() * possible.length));

            return text + new Date().getTime();
        };
        var onUploadF = function (err, data) {
            var current = new Date().getTime();
            $(this).parent().find('button').removeClass('blue');
            if (err) {
                $(this).parent().find('button').addClass('red');
                $(this).parent().find('button').html('<i class="warning icon"></i> ERROR');
                return alert('There was an error uploading your photo: ', err.message);
            }

            $(this).parent().find('button').addClass('green');
            $(this).parent().find('button').html('<i class="checkmark icon"></i> OK');
            $(this).parent().find('p').html('Completado en ' + Math.round((current - initF) / 1000) + ' seg.');
            $(this).parent().find('input[type=hidden]').val(data.Key);
        };
        var onProgressF = function (progress) {
            var current = new Date().getTime();
            var speed = Math.floor((progress.loaded) / (current - initF));
            var remaing = Math.round((progress.total - progress.loaded) / (speed * 1024));
            var perc = Math.floor(progress.loaded / progress.total * 1000) / 10;
            $(this).parent().find('button').html('<i class="wait icon"></i> ' + perc + ' %');
            if (remaing <= 120) {
                $(this).parent().find('p').html('Faltan: &sim;' + remaing + ' seg.<br /> (vel. &sim;' + speed + ' KB/s)');
            } else {
                $(this).parent().find('p').html('Faltan: &sim;' + Math.round(remaing / 60) + ' min.<br /> (vel. &sim;' + speed + ' KB/s)');
            }
        };
        $(document).ready(function () {

            $('#notificationsfilebtn').click(function () {
                $('#notificationsfile').click();
            });

            $('#notificationsfilebtn2').click(function () {
                $('#notificationsfile2').click();
            });
            $('#notificationsfilebtn4').click(function () {
                $('#notificationsfile4').click();
            });

            $('#notificationsfile, #notificationsfile2, #notificationsfile4').change(function () {
                $(this).parent().find('button').removeClass('green');
                $(this).parent().find('button').removeClass('red');
                $(this).parent().find('button').addClass('blue');
                var files = $(this).prop("files");
                if (!files.length) {
                    return alert('Please choose a file to upload first.');
                }
                var file = files[0];
                var fileName = file.name;
                var fileNameExt = file.name.split('.').pop();
                var fileNameHash = makeUIDF(27);
                var albumPhotosKey = encodeURIComponent('meeting') + '/' + encodeURIComponent('supports') + '/';

                var photoKey = albumPhotosKey + fileNameHash + '.' + fileNameExt;
                var onUploadB = onUploadF.bind(this);
                var onProgressB = onProgressF.bind(this);

                initF = new Date().getTime();
                s3.upload({
                    Key: photoKey,
                    Body: file,
                }, {partSize: 20 * 1024 * 1024, queueSize: 1}, onUploadB).on('httpUploadProgress', onProgressB);
            });

            function uploadFile() {
                $(this).parent().find('button').removeClass('green');
                $(this).parent().find('button').removeClass('red');
                $(this).parent().find('button').addClass('blue');
                var files = $(this).prop("files");
                if (!files.length) {
                    return alert('Please choose a file to upload first.');
                }
                var file = files[0];
                var fileName = file.name;
                var fileNameExt = file.name.split('.').pop();
                var fileNameHash = makeUIDF(27);
                var albumPhotosKey = encodeURIComponent('meeting') + '/' + encodeURIComponent('supports') + '/';

                var photoKey = albumPhotosKey + fileNameHash + '.' + fileNameExt;
                var onUploadB = onUploadF.bind(this);
                var onProgressB = onProgressF.bind(this);

                initF = new Date().getTime();
                s3.upload({
                    Key: photoKey,
                    Body: file,
                }, {partSize: 20 * 1024 * 1024, queueSize: 1}, onUploadB).on('httpUploadProgress', onProgressB);
            }
        });
    </script>
    <script type="text/javascript">
        var calculateIMC = function () {
            var weight = parseFloat($('[name=weight]').val());
            var height = parseFloat($('[name=height]').val());

            console.log('calculateIMC', weight, height);

            if (height > 0) {
                var imc = Math.round((weight / Math.pow(height / 100, 2)) * 10) / 10;

                if (imc < 18.5) {
                    imc += ' (Inferior a lo normal)';
                } else if (imc >= 18.5 && imc < 25) {
                    imc += ' (Normal)';
                } else if (imc >= 25 && imc < 30) {
                    imc += ' (Superior a lo normal)';
                } else if (imc >= 30) {
                    imc += ' (Obesidad)';
                } else {
                    imc += ' ()';
                }

                $('#imc').text(imc);
            } else {
                $('#imc').text('');
            }
        }

        var checkDiagnosticCode = function ($input, value) {
            console.log(value);

            if ($('input[name="diagnostics[cod][]"][value=' + value + ']').length > 1) {
                $input.parent().addClass('error');
                $.toast({
                    text: "<b>Error:</b> Ya existe un diagnostico con este CODIGO para este afiliado",
                    showHideTransition: 'fade', // It can be plain, fade or slide
                    bgColor: '#9f3a38', // Background color for toast
                    textColor: '#fff6f6', // text color
                    allowToastClose: true, // Show the close button or not
                });
            } else {
                $input.addClass('loading');
                $.getJSON('{{secure_url('/servicio/' . $activity->id . '/checkCode')}}/' + value, function (json) {
                    if (json.id !== undefined) {
                        $input.parent().addClass('error');
                        $.toast({
                            text: "<b>Error:</b> Ya existe un diagnostico con este CODIGO para este afiliado",
                            showHideTransition: 'fade', // It can be plain, fade or slide
                            bgColor: '#9f3a38', // Background color for toast
                            textColor: '#fff6f6', // text color
                            allowToastClose: true, // Show the close button or not
                        });
                    } else {
                        $input.parent().removeClass('error');
                    }
                    $input.removeClass('loading');
                });
            }


            return false;
        }

        var generatePDF = function () {
            $('form#dictamen').attr('target', '_blank');
            $('form#dictamen').attr('action', '{{secure_url('/servicio/' . $activity->id . '/meeting/pdf')}}');
            $('form#dictamen').submit();
            $('form#dictamen').removeAttr('target');
            $('form#dictamen').attr('action', '{{secure_url('/servicio/' . $activity->id . '/dictamenEL/save')}}');
            return false;
        };

        var addDiagnostic = function (e) {
            var $fields = $('#diagnostic_model').clone(true);
            $fields.removeAttr('id');
            $fields.find('a').click(function () {
                $(this).parent().parent().remove();
            });
            $fields.find('.ui.dropdown').dropdown({
                forceSelection: false
            });
            $fields.find('.datepicker').pickadate({
                selectYears: 100,
                selectMonths: true,
                max: new Date(),
                formatSubmit: 'yyyy-mm-dd',
                format: 'mmm dd, yyyy',
            });
            $fields.find('.ui.search input').change(function () {
                var valid = false;

                $(this).parents('.fields').find('input.description').val('');
                $(this).parents('.fields').find('input.description_editable').val('');

                for (var i = 0; i < cie10.length; i++) {
                    if (cie10[i].COD == $(this).val().toUpperCase()) {
                        $(this).parents('.fields').find('input.description').val(cie10[i].DESCRIPTION);
                        $(this).parents('.fields').find('input.description_editable').val(cie10[i].DESCRIPTION);
                        checkDiagnosticCode($(this), cie10[i].COD);
                        valid = true;
                    }
                }

                if (!valid && $(this).val() != '') {
                    $(this).val('');
                }
            });
            $fields.find('.ui.search').search({
                source: cie10,
                fields: {
                    title: 'COD',
                    description: 'DESCRIPTION'
                },
                searchFields: ['COD', 'DESCRIPTION'],
                regExp: {
                    escape: /[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,
                    beginsWith: ''
                },
                showNoResults: true,
                maxResults: 250,
                searchFullText: false,
                error: {
                    noResults: 'No se encontraron resultados para tu búsqueda.'
                },
                onSelect: function (result, response) {
                    $(this).parents('.fields').find('input.description').val(result.DESCRIPTION);
                }
            });
            $('#diagnostics').append($fields);
            $fields.show();
            return false;
        };

        var cie10 = [];
        $(document).ready(function () {
            $('.ui.accordion .ui.grid .row').css('padding-top', 0);
            $('.ui.accordion .ui.grid .column').css('padding-top', 0);
            $('.ui.accordion').accordion({
                exclusive: false
            });
            $('form#dictamen').form();

            $('form#dictamen a.red').click(function () {
                $(this).parent().parent().remove();
            });
            $('form .ui.dropdown').dropdown({
                forceSelection: false
            });
            $('form .datepicker').pickadate({
                selectYears: 100,
                selectMonths: true,
                max: new Date(),
                formatSubmit: 'yyyy-mm-dd',
                format: 'mmm dd, yyyy'
            });
            $('form .datepicker2').pickadate({
                selectYears: 100,
                selectMonths: true,
                formatSubmit: 'yyyy-mm-dd',
                format: 'mmm dd, yyyy'
            });
            $('form .ui.search input').change(function () {
                var valid = false;

                $(this).parents('.fields').find('input.description').val('');

                for (var i = 0; i < cie10.length; i++) {
                    if (cie10[i].COD == $(this).val().toUpperCase()) {
                        $(this).parents('.fields').find('input.description').val(cie10[i].DESCRIPTION);
                        checkDiagnosticCode($(this), cie10[i].COD);
                        valid = true;
                    }
                }

                if (!valid && $(this).val() != '') {
                    $(this).val('');
                }
            });
            $.getJSON('/js/cie10.json', function (json) {
                cie10 = json;
                $('form .ui.search.code').search({
                    source: cie10,
                    fields: {
                        title: 'COD',
                        description: 'DESCRIPTION'
                    },
                    searchFields: ['COD', 'DESCRIPTION'],
                    regExp: {
                        escape: /[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,
                        beginsWith: ''
                    },
                    showNoResults: true,
                    maxResults: 250,
                    searchFullText: false,
                    error: {
                        noResults: 'No se encontraron resultados para tu búsqueda.'
                    },
                    onSelect: function (result, response) {
                        $(this).parents('.fields').find('input.description').val(result.DESCRIPTION);
                    }
                });
            });

            $('form input').keydown(function (event) {
                if (event.keyCode == 13) {
                    event.preventDefault();
                    return false;
                }
            });

            @if ($activity->dictum && $activity->dictum->firm_date)
            // $('form a.basic.blue.button').hide();
            // $('form a.basic.red.button').hide();
            // $('form input, form textarea').prop('disabled', true);
            // $('form .controversy input, form .controversy textarea').prop('disabled', false);
            // $('form .pcl input, form .pcl textarea').prop('disabled', false);
            // $('form .ui.dropdown').addClass('disabled');
            // $('form .controversy .ui.dropdown').removeClass('disabled');
            // $('form .pcl .ui.dropdown').removeClass('disabled');
            @endif

            @if (Auth::user()->isViewer())
            $('.datepicker').pickadate('picker').stop();
            $('#dictamen .ui.search input').attr('disabled', 'disabled');
            $('#dictamen input, #dictamen textarea').attr('readonly', 'readonly');
            $('#dictamen .ui.dropdown').addClass('disabled');
            $('#dictamen .button').remove();
            @endif
        });
    </script>
    {{--  VALIDACIÓN DE PERMISOS (Siempre al final de todo)  --}}
    <script>
        $(document).ready(function () {
            const isAssignedUser = {!! json_encode($activity->user->id == Auth::user()->id  || Auth::user()->isAdmin() || Auth::user()->isAdmin2() ) !!};
            const permissions = {!! json_encode(Auth::user()->service_permissions_list($activity->service_id)) !!};
            const activity = {!! json_encode($activity) !!};
            if (permissions.every(value => value.area_permissions[0]?.edit !== 1 || !isAssignedUser)) {
                $("#button_save").addClass("disabled");
            }
            for (const permission of permissions) {
                if (permission.area_permissions[0]?.view !== 1) {
                    $(`#${permission.id}`).css("pointer-events", "none");
                }
                if (permission.area_permissions[0]?.edit !== 1 || !isAssignedUser) {
                    const content = $(`#${permission.id}`).next(".content");
                    content.find('input, .ui.dropdown, .datepicker').css('pointer-events', 'none');
                    content.find('textarea').attr('readonly', 'readonly');
                    content.find('.ui.button').addClass('disabled');
                }
            }
        });
    </script>
@endsection
