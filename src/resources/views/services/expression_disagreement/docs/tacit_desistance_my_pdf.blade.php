<!DOCTYPE html>
<html lang="es">
<head>
    <meta content="text/html; charset=utf-8" http-equiv="Content-Type"/>
    <style type="text/css">
        * {
            font-family: 'Arial', sans-serif;
            font-size: 10pt;
        }

        body {
            margin: 0 2rem 3rem 2rem;
            padding-top: 75px;
            text-align: justify;
        }

        .watermark {
            position: fixed;
            top: 45%;
            width: 100%;
            text-align: center;
            opacity: .1;
            transform: rotate(-45deg);
            transform-origin: 50% 50%;
            z-index: 1000;
        }

        .footer {
            height: 40px;
            width: 100%;
            position: fixed;
            z-index: 0 !important;
            left: 26px;
            right: 26px;
            top: 87%;
            border-top: 0.5px solid black;
        }

        .header {
            height: 40px;
            width: 100%;
            position: fixed;
            z-index: 0 !important;
        }

        .header img {
            height: 57px;
            width: 752px;
            position: absolute;
            left: -76px;
            top: -8px;
            opacity: 0.5;
        }

        .footer img {
            min-height: 50px;
            max-height: 50px;
            width: auto;
            text-align: left;
        }

        table,
        th,
        td {
            border-collapse: collapse;
            border: 0.5px solid black;
        }

        table td th {
            text-align: center;
        }

        table,
        th,
        td {
            border: 0.5px solid black;
        }

        th,
        td {
            padding: 1.5px;
            padding-bottom: 3px;
        }

        td[colspan="16"] {
            text-align: justify;
        }

        table {
            border-collapse: collapse;
            width: 100%;
        }

        th {
            text-align: center;
            background: lightgray;
            color: black;
            font-family: 'Century-Bold', sans-serif !important;
        }
    </style>
</head>
@php
    $numberBizagi = '';
    $radicateBizagi = '';
    $date_action = null;
    foreach($activity->activity_actions as $aa){
        if ($aa->action_id == 456) {
           $date_action = $aa->created_at;
           foreach ($aa->fields as $field){
               if ($field->action_field_id == 258){
                   $radicateBizagi = $field->value;
               }
           }
        }
        if ($aa->action_id == 451) {
           foreach ($aa->fields as $field){
               if ($field->action_field_id == 259){
                   $numberBizagi = $field->value;
               }
           }
        }
    }
    setlocale(LC_TIME, 'Spanish');
    $formattedDate = $date_action ? $date_action->formatLocalized('%d de %B de %Y') : '';
@endphp
<body>
{{--<div class="watermark">--}}
{{--    <span style="font-size: 72pt;font-weight: 500;">BORRADOR</span>--}}
{{--</div>--}}
<div class="header">
    <img src="{{storage_path('app/header_mdi.jpeg')}}" alt="Logo Colpensiones">
</div>
<div class="footer">
    <table style="width: 100%">
        <tr>
            <td colspan="10" style="border: transparent;text-align: justify;opacity: 0.5;">
                <b>Colpensiones</b><br>
                Dirección: Carrera 10 No.72 – 33 Torre B Piso 11, Bogotá D.C., Colombia<br>
                Conmutador: (+57) 601 489 0909; Línea Gratuita: 01 8000 410909<br>
                www.colpensiones.gov.co
            </td>
            <script type="text/php">
                if (isset($pdf)) {
                    $x = $pdf->get_width() - 98;
                    $y= 770;
                    $text = "Página | {PAGE_NUM}";
                    $font = $fontMetrics->get_font("Helvetica", "normal");
                    $size = 9;
                    $color = array(0.430,0.430,0.430);
                    $word_space = 0.0;  //  default
                    $char_space = 0.0;  //  default
                    $angle = 0.0;   //  default
                    $pdf->page_text($x, $y, $text, $font, $size, $color, $word_space, $char_space, $angle);
                }
            </script>
        </tr>
    </table>
</div>
<div>
    <p style="text-align: right">No. de Radicado, {{$radicateBizagi}}</p>
    <p>
        Bogotá D.C, {{$formattedDate}}
    </p>
    <p>
        Señor (a) <br>
        @if($activity->expression_disagreement->manifestation_manifestation_type == 'EMPLEADOR')
            <b>{{mb_strtoupper($activity->expression_disagreement->qualification_name)}}</b><br>
        @elseif($activity->expression_disagreement->manifestation_manifestation_type == 'CALIFICADO')
            <b>{{mb_strtoupper($activity->expression_disagreement->qualification_name)}}</b><br>
        @elseif($activity->expression_disagreement->manifestation_manifestation_type == 'EPS')
            <b>{{mb_strtoupper($EPS_LIST[$activity->expression_disagreement && $activity->expression_disagreement->qualification_name ? $activity->expression_disagreement->qualification_name : '501'])}}</b>
            <br>
        @elseif($activity->expression_disagreement->manifestation_manifestation_type == 'ARL')
            <b>{{mb_strtoupper($ARL_LIST[$activity->expression_disagreement && $activity->expression_disagreement->qualification_name ? $activity->expression_disagreement->qualification_name : '0'])}}</b>
            <br>
        @elseif($activity->expression_disagreement->manifestation_manifestation_type == 'AFP')
            <b>{{mb_strtoupper($AFP_LIST[$activity->expression_disagreement && $activity->expression_disagreement->qualification_name ? $activity->expression_disagreement->qualification_name : '0'])}}</b>
            <br>
        @endif
        <span>{{ $activity->expression_disagreement->manifestation_email}}</span><br>
        {{$activity->expression_disagreement->manifestation_address}} <br>
        {{$activity->expression_disagreement->expression_disagreement_city}}
    </p>
    <p>
        <span><b>Referencia:</b></span><span
                style="margin-left: 66px">Radicado No. {{$activity->expression_disagreement->manifestation_radicated}}  del
            {{$activity->expression_disagreement->radication_date ? Carbon\Carbon::createFromFormat('Y-m-d', $activity->expression_disagreement->radication_date)->formatLocalized('%d %B %Y') : ''}}</span>
        <br>
        <span><b>Afiliado:</b></span><span style="margin-left: 85px">{{$activity->affiliate->fullname}}</span><br>
        <span><b>Identificación:</b></span><span
                style="margin-left: 48px">{{$activity->affiliate->doc_type}} {{$activity->affiliate->doc_number}}</span><br>
        <span><b>Tipo de Trámite:</b></span><span style="margin-left: 35px">Inconformidad Dictamen de Pérdida de Capacidad Laboral</span><br>
    </p>
    <p>
        Respetado(a) Señor(a):
    </p>
    <p>
        Reciba un cordial saludo de la Administradora Colombiana de Pensiones – COLPENSIONES,
    </p>
    <p>
        A través de esta comunicación la Dirección de Medicina Laboral de Colpensiones le informa que en aplicación del
        Artículo 17 de la Ley 1437 de 2011 se declaró el desistimiento tácito de la manifestación de inconformidad
        presentada bajo el radicado de referencia.
    </p>
    <ul>
        <li style="list-style: none">
            <p>
                <i>
                    (…) ARTÍCULO 17. Peticiones incompletas y desistimiento tácito. En virtud del principio de eficacia,
                    cuando la autoridad constate que una petición ya radicada está incompleta pero la actuación puede
                    continuar sin oponerse a la ley, requerirá al peticionario dentro de los diez (10) días siguientes a
                    la
                    fecha de radicación para que la complete en el término máximo de un (1) mes. A partir del día
                    siguiente
                    en que el interesado aporte los documentos o informes requeridos comenzará a correr el término para
                    resolver la petición.
                </i>
            </p>
            <p>
                <i>
                    Cuando en el curso de una actuación administrativa la autoridad advierta que el peticionario debe
                    realizar una gestión de trámite a su cargo, necesaria para adoptar una decisión de fondo, lo
                    requerirá
                    por una sola vez para que la efectúe en el término de un (1) mes, lapso durante el cual se
                    suspenderá el
                    término para decidir.
                </i>
            </p>
            <p>
                <i>
                    Se entenderá que el peticionario ha desistido de su solicitud o de la actuación cuando no satisfaga
                    el
                    requerimiento, salvo que antes de vencer el plazo concedido solicite prórroga hasta por un término
                    igual.
                </i>
                <i>
                    Vencidos los términos establecidos en este artículo, la autoridad decretará el desistimiento y el
                    archivo del expediente, mediante acto administrativo motivado, que se notificará personalmente,
                    contra
                    el cual únicamente procede recurso de reposición, sin perjuicio de que la respectiva solicitud pueda
                    ser
                    nuevamente presentada con el lleno de los requisitos legales. (…)
                </i>
            </p>
        </li>
    </ul>
    <p>
        De conformidad con la norma señalada, esta declaración de desistimiento tácito se motiva en los siguientes
        términos:
    </p>
    <p>
        Colpensiones recibió el escrito por usted presentado a través del cual manifestó su desacuerdo frente al
        Dictamen No. {{$activity->expression_disagreement->dictum_number_first_time}} y, en consecuencia, solicita la
        remisión del caso ante la Junta Regional de
        Calificación de Invalidez.
    </p>
    <br>
    <p>
        Una vez analizada la comunicación por parte de la Dirección de Medicina Laboral de Colpensiones, se observó que
        la solicitud se encontraba incompleta, haciéndole falta la siguiente documentación necesaria e indispensable
        para resolver y dar trámite a la manifestación de inconformidad y sin la cual es imposible remitir el caso ante
        la Junta Regional de Calificación de Invalidez:
    </p>
    <table>
        <tbody>
        <tr style="text-align: center">
            <td colspan="4"><b>SOLICITUD DE DOCUMENTOS </b></td>
            <td colspan="6"><b>DOCUMENTO REQUERIDO</b></td>
            <td colspan="6"><b>DESCRIPCIÓN</b></td>
        </tr>
        @foreach($activity->expression_disagreement->letter_missing_docs as $lmd)
            <tr style="text-align: center">
                <td colspan="4" style="border-bottom: white; border-top: 1px solid black">{{$lmd->requester}}</td>
                <td colspan="6"
                    style="border-bottom: white; border-top: 1px solid black">{{$lmd->doc_type_description}}</td>
                <td colspan="6" style="border-bottom: white; border-top: 1px solid black; text-align: justify">
                    @foreach(explode("\n",$lmd->doc_observation) as $text)
                        {{$text}}
                </td>
            </tr>
            <tr>
                <td colspan="4" style="border-bottom: white">
                </td>
                <td colspan="6" style="border-bottom: white">
                </td>
                <td colspan="6" style="border-bottom: white">
                    @endforeach
                </td>
            </tr>
            <tr>
                <td colspan="4"></td>
                <td colspan="6"></td>
                <td colspan="6"></td>
            </tr>
        @endforeach
        </tbody>
    </table>
    <p>
        Así las cosas, al validar su expediente administrativo se observa que la comunicación No. {{$activity->expression_disagreement ? $numberBizagi : ''}} del
        {{$activity->expression_disagreement && $activity->expression_disagreement->communication_sending_date ? trim(Carbon\Carbon::createFromFormat('Y-m-d', $activity->expression_disagreement->communication_sending_date)->formatLocalized('%d %B %Y')) : ''}}
        por medio de la cual se requirió aportar la documentación faltante, ya señalada, fue entregada de manera
        efectiva el {{$activity->expression_disagreement->effective_reception_communication_date ? trim(Carbon\Carbon::createFromFormat('Y-m-d', $activity->expression_disagreement->effective_reception_communication_date)->formatLocalized('%d %B %Y')) : ''}}
        mediante Guía No. {{$activity->expression_disagreement->letter_guide_number}} en la dirección informada y
        autorizada para recibir notificaciones.
    </p>
    <p>
        En consecuencia, el interesado tenía plazo de un (1) mes calendario contado desde el día siguiente de la entrega
        de la comunicación, esto es, hasta el {{ $activity->expression_disagreement->maximum_delivery_doc_date ? Carbon\Carbon::createFromFormat('Y-m-d', $activity->expression_disagreement->maximum_delivery_doc_date)->formatLocalized('%d %B %Y')  : ''}}
        para allegar la documentación faltante y así completar su
        petición ante la Administradora Colombiana de Pensiones Colpensiones.
    </p>
    <p>
        Sin embargo, una vez vencido el plazo legal se observa que el interesado no aportó la documentación requerida ni
        solicitó a esta Administradora de Pensiones una prórroga del plazo inicial, quien guardó absoluto silencio ante
        el requerimiento hecho por Colpensiones.
    </p>
    <p>
        Así las cosas, se establece que el interesado desistió tácitamente de su solicitud radicado
        No. {{$activity->expression_disagreement->manifestation_radicated}} del
        {{$activity->expression_disagreement->reception_date  ?Carbon\Carbon::createFromFormat('Y-m-d', $activity->expression_disagreement->reception_date)->formatLocalized('%d %B %Y') : ''}}
        ,
        por lo que la Administradora Colombiana de Pensiones Colpensiones declara su desistimiento tácito y
        el consecuente archivo del expediente.
    </p>
    <p>Agradecemos su confianza recordándole que estamos para servirle.</p>
    <p>Cordialmente,</p>
    <p>
        <img style="height: 50px; width: auto;" alt="FIRMA" src="{{storage_path('app/firma_lmlozano.jpg')}}"/>
        <br/>
        <b>LUZ MARYEN LOZANO ROSAS</b><br/>
        <b>Director de Medicina Laboral</b><br>
        <b>Administradora Colombiana De Pensiones – COLPENSIONES</b><br/>
    </p>
    @php
        $authorProyection = null;
        $authorRevission = null;
          foreach ($activity->activity_actions as $aa) {
              if ($aa->action_id == 455) {
                  $authorProyection = strtoupper($expression_disagreement->authorName($aa->author_id));
              }
              if ($aa->action_id == 594) {
                  $authorRevission = strtoupper($expression_disagreement->authorName($aa->author_id));
              }
          }
    @endphp
    <p>
        Proyectó: {{$authorProyection ? $authorProyection : ''}}<br>
        {{$authorRevission ? 'Revisó: ' . $authorRevission : ''}}
    </p>
</div>
</body>