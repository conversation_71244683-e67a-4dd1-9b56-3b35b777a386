<!DOCTYPE html>
<html lang="es">
<head>
    <meta content="text/html; charset=utf-8" http-equiv="Content-Type"/>
    <style type="text/css">
        * {
            font-family: 'Arial', sans-serif;
            font-size: 10pt;
        }

        body {
            margin: 0 2rem 3rem 2rem;
            padding-top: 75px;
            text-align: justify;
        }

        .watermark {
            position: fixed;
            top: 45%;
            width: 100%;
            text-align: center;
            opacity: .1;
            transform: rotate(-45deg);
            transform-origin: 50% 50%;
            z-index: 1000;
        }

        .footer {
            height: 40px;
            width: 100%;
            position: fixed;
            z-index: 0 !important;
            left: 26px;
            right: 26px;
            top: 87%;
            border-top: 0.5px solid black;
        }

        .header {
            height: 40px;
            width: 100%;
            position: fixed;
            z-index: 0 !important;
        }

        .header img {
            height: 57px;
            width: 752px;
            position: absolute;
            left: -76px;
            top: -8px;
            opacity: 0.5;
        }

        .footer img {
            min-height: 50px;
            max-height: 50px;
            width: auto;
            text-align: left;
        }

    </style>
</head>
@php
    $numberBizagi = '';
    $date_action = null;
    foreach($activity->activity_actions as $aa){
        if ($aa->action_id == 444) {
           $date_action = $aa->created_at;
           foreach ($aa->fields as $field){
               if ($field->action_field_id == 260){
                   $numberBizagi = $field->value;
               }
           }
        }
    }
    setlocale(LC_TIME, 'Spanish');
    $formattedDate = $date_action ? $date_action->formatLocalized('%d de %B de %Y') : '';
@endphp
<body>
<div class="watermark">
    <span style="font-size: 72pt;font-weight: 500;">BORRADOR - REVISADO</span>
</div>
<div class="header">
    <img src="{{storage_path('app/header_mdi.jpeg')}}" alt="Logo Colpensiones">
</div>
<div class="footer">
    <table style="width: 100%">
        <tr>
            <td colspan="10" style="border: transparent;text-align: justify;opacity: 0.5;">
                <b>Colpensiones</b><br>
                Dirección: Carrera 10 No.72 – 33 Torre B Piso 11, Bogotá D.C., Colombia<br>
                Conmutador: (+57) 601 489 0909; Línea Gratuita: 01 8000 410909<br>
                www.colpensiones.gov.co
            </td>
            <script type="text/php">
                if (isset($pdf)) {
                    $x = $pdf->get_width() - 98;
                    $y= 770;
                    $text = "Página | {PAGE_NUM}";
                    $font = $fontMetrics->get_font("Helvetica", "normal");
                    $size = 9;
                    $color = array(0.430,0.430,0.430);
                    $word_space = 0.0;  //  default
                    $char_space = 0.0;  //  default
                    $angle = 0.0;   //  default
                    $pdf->page_text($x, $y, $text, $font, $size, $color, $word_space, $char_space, $angle);
                }
            </script>
        </tr>
    </table>
</div>
<div>
    <p style="text-align: right">No. de Radicado, {{$numberBizagi}}</p>
    <p>
        Bogotá D.C, {{$formattedDate}}
    </p>
    <p>
        Señor (a) <br>
        @if($activity->expression_disagreement->manifestation_manifestation_type == 'EMPLEADOR')
            <b>{{mb_strtoupper($activity->expression_disagreement->qualification_name)}}</b><br>
        @elseif($activity->expression_disagreement->manifestation_manifestation_type == 'CALIFICADO')
            <b>{{mb_strtoupper($activity->expression_disagreement->qualification_name)}}</b><br>
        @elseif($activity->expression_disagreement->manifestation_manifestation_type == 'EPS')
            <b>{{mb_strtoupper($EPS_LIST[$activity->expression_disagreement && $activity->expression_disagreement->qualification_name ? $activity->expression_disagreement->qualification_name : '501'])}}</b><br>
        @elseif($activity->expression_disagreement->manifestation_manifestation_type == 'ARL')
            <b>{{mb_strtoupper($ARL_LIST[$activity->expression_disagreement && $activity->expression_disagreement->qualification_name ? $activity->expression_disagreement->qualification_name : '0'])}}</b><br>
        @elseif($activity->expression_disagreement->manifestation_manifestation_type == 'AFP')
            <b>{{mb_strtoupper($AFP_LIST[$activity->expression_disagreement && $activity->expression_disagreement->qualification_name ? $activity->expression_disagreement->qualification_name : '0'])}}</b><br>
        @endif
        <span>{{ $activity->expression_disagreement->manifestation_email}}</span><br>
        {{$activity->expression_disagreement->manifestation_address}} <br>
        {{$activity->expression_disagreement->expression_disagreement_city}}
    </p>
    <p>
        <span><b>Referencia:</b></span><span
                style="margin-left: 66px">Radicado No. {{$numberBizagi}}  del
            {{$activity->expression_disagreement->radication_date ? Carbon\Carbon::createFromFormat('Y-m-d', $activity->expression_disagreement->radication_date)->formatLocalized('%d %B %Y') : ''}}</span>
        <br>
        <span><b>Afiliado:</b></span><span style="margin-left: 85px">{{$activity->affiliate->fullname}}</span><br>
        <span><b>Identificación:</b></span><span
                style="margin-left: 48px">{{$activity->affiliate->doc_type}} {{$activity->affiliate->doc_number}}</span><br>
        <span><b>Tipo de Trámite:</b></span><span style="margin-left: 35px">Inconformidad Dictamen de Pérdida de Capacidad Laboral</span><br>
    </p>
    <p>
        Respetado(a) Señor(a):
    </p>
    <p>
        Reciban un cordial saludo de la Administradora Colombiana de Pensiones – COLPENSIONES.
    </p>
    <p>
        A través de la presente comunicación, nos permitimos informarle que esta Administradora ha recibido su
        inconformidad al Dictamen de Pérdida de Capacidad Laboral PCL emitido por Colpensiones.
    </p>
    <p>
        Al respecto, es necesario tener en cuenta lo establecido en el Decreto Ley 019 de 2012:
    </p>
    <ul>
        <li style="list-style: none">
            <i>
                Corresponde al Instituto de Seguros Sociales, Administradora Colombiana de Pensiones -COLPENSIONES-, a las
                Administradoras de Riesgos Profesionales - ARP-, a las Compañías de Seguros que asuman el riesgo de
                invalidez y muerte, y a las Entidades Promotoras de Salud EPS, determinar en una primera oportunidad la
                pérdida de capacidad laboral y calificar el grado de invalidez y el origen de estas contingencias. En caso
                de que el interesado no esté de acuerdo con la calificación deberá manifestar su inconformidad dentro de los
                diez (10) días siguientes y la entidad deberá remitirlo a las Juntas Regionales de Calificación de Invalidez
                del orden regional dentro de los cinco (5) días siguientes, cuya decisión será apelable ante la Junta
                Nacional de Calificación de Invalidez, la cual decidirá en un término de cinco (5) días. Contra dichas
                decisiones proceden las acciones legales. (…)
            </i>
        </li>
    </ul>
    <p>
        Revisados los sistemas de información se evidencia que el señor {{$activity->affiliate->fullname}} fue
        notificado del dictamen No. DML: {{$activity->expression_disagreement->dictum_number_first_time}} de
        fecha
        {{$activity->expression_disagreement->notification_date ? Carbon\Carbon::createFromFormat('Y-m-d', $activity->expression_disagreement->notification_date)->formatLocalized('%d %B %Y') : ''}}
        por medio de notificación {{$activity->expression_disagreement->manifestation_type}}: del correo electrónico
        {{$activity->expression_disagreement->manifestation_email}}
        día {{$activity->expression_disagreement->dictum_date_first_time ? Carbon\Carbon::createFromFormat('Y-m-d', $activity->expression_disagreement->dictum_date_first_time)->formatLocalized('%d %B %Y') : ''}}
        y que, de conformidad con lo señalado en la norma, el interesado tenía hasta
        el {{$activity->expression_disagreement->maximum_manifestation_date ? Carbon\Carbon::createFromFormat('Y-m-d', $activity->expression_disagreement->maximum_manifestation_date)->formatLocalized('%d %B %Y') : ''}}
        para controvertirlo.
    </p>
    <p>
        De acuerdo con lo anterior, esta Administradora le informa que la inconformidad fue radicada de forma
        extemporánea toda vez que fue interpuesta el
        día {{$activity->expression_disagreement->reception_date ? Carbon\Carbon::createFromFormat('Y-m-d', $activity->expression_disagreement->reception_date)->formatLocalized('%d %B %Y') : ''}}
        por lo que no es procedente enviar el caso a la Junta Regional de Calificación de Invalidez.
    </p>
    <p>
        En caso de requerir información adicional, por favor acérquese a nuestros Puntos de Atención Colpensiones (PAC),
        o comuníquese con la línea de servicio al ciudadano en Bogotá al 4890909, en Medellín al 2836090, o con la línea
        gratuita nacional al 018000 41 0909, en donde estaremos dispuestos a brindarle el mejor servicio
    </p>
    <p>Agradecemos su confianza recordándole que estamos para servirle.</p>
    <p>Cordialmente,</p>
    <p>
        <b>LUZ MARYEN LOZANO ROSAS</b><br/>
        <b>Director de Medicina Laboral</b><br>
        <b>Administradora Colombiana De Pensiones – COLPENSIONES</b><br/>
    </p>
    <p>
        Proyecto: <br>
        Reviso:
    </p>
</div>
</body>