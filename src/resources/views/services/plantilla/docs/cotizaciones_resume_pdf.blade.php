<!DOCTYPE html>
<html lang="es">

<head>
    <title>Cotización de Seguro de Riesgos del Trabajo</title>
    <meta content="text/html; charset=utf-8" http-equiv="Content-Type" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        .cotizacion-header {
            text-align: center;
            font-size: 1.5em;
            font-weight: bold;
            padding-top: 20px;
            font-family: 'Helvetica';
            font-weight: bold;
        }

        .section-title {
            font-size: 1.2em;
            margin-top: 20px;
            color: #ffffff;
            background-color: black;
            padding: 10px;
            text-align: center;
            font-family: 'Helvetica';
            font-weight: bold;
            border-radius: 10px;
        }

        .content-segment {
            padding: 10px;
            margin-top: 15px;
        }

        .table-pricing {
            width: 100%;
            margin-top: 20px;
        }

        .footer-note {
            margin-top: 30px;
            font-size: 0.9em;
            text-align: center;
        }

        .sub-content {
            background-color: #f0f0f0;
        }

        body {
            font-family: 'Helvetica', sans-serif;
            font-size: 0.9em;
            color: #333;
        }

        .card {
            background-color: #f0f0f0;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            margin-top: 20px;
        }

        .centered-table td,
        .centered-table th {
            text-align: center;
            vertical-align: middle;
        }


        .form-table {
            width: 100%;
            border-collapse: collapse;
            font-family: Arial, sans-serif;
            font-size: 14px;
            table-layout: fixed;
            /* Para mantener las celdas del mismo tamaño */
        }

        .form-table td {
            padding: 2px 15px;

            width: 25%;
            /* Asegura que cada celda tenga el mismo ancho */
            vertical-align: middle;
        }

        .input-field {
            display: block;
            width: 110%;
            height: 30px;
            background-color: #e9e9e9;

            border-radius: 4px;
            text-align: center;
            font-size: 11px;
        }

        .contract-table {
            width: 100%;
            border-collapse: collapse;
            font-family: Arial, sans-serif;
            font-size: 14px;
            table-layout: auto;
        }

        .contract-table td {
            padding: 2px 15px;
            vertical-align: middle;

        }

        .contract-label {
            width: 30%;
            /* Asegura que las etiquetas ocupen solo una parte del ancho */
        }

        .contract-input {
            display: block;
            width: 100%;
            height: 30px;
            background-color: #e9e9e9;
            /* Simula los campos en gris */
            border-radius: 4px;
            box-sizing: border-box;
            text-align: center;
            padding-top: 10px;
        }

        .contract-input-text {
            display: block;
            width: 100%;
            height: 120px;
            background-color: #e9e9e9;
            /* Simula los campos en gris */
            border-radius: 4px;
            box-sizing: border-box;
            text-align: left;
            padding-top: 1px;
        }


        .pricing-table {
            border-collapse: collapse;
            font-family: Arial, sans-serif;
            font-size: 14px;
            table-layout: auto;
            border-radius: 10px;
            overflow: hidden;
            margin: auto !important;
            margin-top: 40px !important;
            width: 70%;
        }

        .pricing-table th,
        .pricing-table td {
            padding: 7px;
            text-align: center;
            border: 1px solid #000;
            /* Bordes negros */
        }

        .header-cell {
            background-color: #7FC242;
            /* Color verde */
            color: #000000;
        }
        .header-cell-left {
            background-color: #7FC242; /* Color verde */
            color: #000000; /* Texto negro */
            border-top-left-radius: 10px; /* Solo la esquina superior izquierda */
            padding: 10px; /* Espaciado interno */
            border-left: 1px solid #000; /* Asegurar borde izquierdo */
            border-top: 1px solid #000; /* Asegurar borde superior */
        }
        .regular-cell {
            background-color: #ffffff;
            /* Fondo blanco */
        }
        .regular-cell-none {
            background-color: #ffffff;
            border: none !important;
        }

        .card-black {
            background-color: #000000;

            border-radius: 15px;
            width: 100%;
            color: #ffffff;
            /* Texto en blanco para mantener legibilidad */
        }

        .card-table {
            width: 100%;
            table-layout: fixed;
            /* Asegura un tamaño fijo para la tabla */
            border-spacing: 20px;
            /* Espaciado entre las imágenes */
        }

        .card-table td {
            vertical-align: middle;
            /* Alinea verticalmente el contenido de las celdas */
        }

        .card-img {
            width: 400px;
            /* Establece un ancho fijo */
            height: 188px;
        }
        .email-field {
            word-wrap: break-word;
            word-break: break-all;
            max-width: 200px;
        }
    </style>
</head>

<body>

    <table
        style="width: 100%; border: none; table-layout: fixed; font-size: 13px !important;margin-top: 30px !important;">
        <tbody>
            <tr>

                <!-- Logo celda con ancho del 25% -->
                <td style="width: 25%; border: none; text-align: left; height: auto">
                    <img src="{{ public_path('images/mnk.png') }}" alt="Logo" class="logo-image"
                        style="margin-top: -70px; width: 220px">
                </td>

                <td style="width: 50%; padding: 10px; border: none; text-align: right; vertical-align: top;">
                    <div>
                        <div style="margin-bottom: 2px ;font-weight:bold;font-size: 20px !important;color:#92C846;">
                            COTIZACIÓN DE SEGURO
                        </div>
                        <div style="font-size: 20px !important; ">
                            OBLIGATORIO DE RIESGOS DEL TRABAJO
                        </div>
                    </div>
                </td>


            </tr>
        </tbody>
    </table>

    <div class="ui container">
        <!-- Encabezado de la cotización -->
        <div class="ui container">
            <!-- Tabla para distribuir en dos columnas -->

            <table class="form-table">
                <tr>
                    <td>Tomador del contrato:</td>
                    <td>
                        <div class="input-field">
                            {{ mb_convert_case(mb_strtolower( $affiliate->first_name ?? ''), MB_CASE_TITLE, "UTF-8") }}
                        </div>
                    </td>
                    <td>Identificación:</td>
                    <td>
                        <div class="input-field">{{ $affiliate->doc_number ?? '' }}</div>
                    </td>
                </tr>
                <tr>
                    <td>Correo electrónico:</td>
                    <td>
                        <div class="input-field email-field">
                            {{ $quotation->notification_email ?? '' }}
                        </div>
                    </td>
                    <td>Teléfono:</td>
                    <td>
                        <div class="input-field">{{ $affiliate->phone ?? '' }}</div>
                    </td>
                </tr>
                <tr>
                    <td>Fecha de cotización:</td>
                    <td>
                        <div class="input-field">
                            {{ !empty($quotation_date)
                                ? ucfirst(strftime('%A %e de %B del %Y', strtotime($quotation_date)))
                                : '' }}
                        </div>
                    </td>
                    <td>Fecha de vencimiento:</td>
                    <td>
                        <div class="input-field">
                            {{ !empty($quotation_date)
                                ? ucfirst(strftime('%A %e de %B del %Y', strtotime($quotation_date . ' +15 days')))
                                : '' }}
                        </div>
                    </td>
                </tr>
            </table>


        </div>
        <br>
        <!-- Condiciones de la Cotización -->
        <div class="section-title">CONDICIONES DE LA COTIZACIÓN</div>
        <div class="ui segment content-segment">

            <table class="contract-table">


                @if(isset($quotation->work_modality_id) && $quotation->work_modality_id == 3)
                    <tr>
                        <td class="contract-label">Temporalidad</td>
                        <td>
                            <div class="contract-input">
                                {{ $quotation->temporality == 'permanent' ? 'Permanente' : 'Período corto' }}
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td class="contract-label">Modalidad de aseguramiento:</td>
                        <td>
                            <div class="contract-input">
                                {{ isset($quotation->work_modality_id) ? $WORK_MODALITY[$quotation->work_modality_id] : '' }}
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td class="contract-label">Tipo de moneda:</td>
                        <td>
                            <div class="contract-input">
                                {{ isset($quotation->type_currency) ? $MONEY_TYPE[$quotation->type_currency]['name'] : '' }}(
                                @include('colones', ['currency' => $quotation->type_currency]))</div>
                        </td>
                    </tr>
                    <tr>
                        <td class="contract-label">Opciones de cobertura:</td>
                        <td>
                            <div class="contract-input-text">
                                <ul>
                                    <li>1 persona permanente para servicios domésticos y 1 o más personas para trabajos ocasionales no mayores a 5 días al mes.</li>
                                    <li>2 personas permanentes para servicios domésticos y trabajos ocasionales no mayores a 5 días al mes.</li>
                                    <li>3 personas permanentes para servicios domésticos y trabajos ocasionales no mayores a 5 días al mes.</li>
                                </ul>
                            </div>
                        </td>
                    </tr>

                @elseif(isset($quotation->work_modality_id) && $quotation->work_modality_id == 4)
                    <tr>
                        <td class="contract-label">Modalidad de aseguramiento:</td>
                        <td>
                            <div class="contract-input">
                                {{ isset($quotation->work_modality_id) ? $WORK_MODALITY[$quotation->work_modality_id] : '' }}
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td class="contract-label">Temporalidad</td>
                        <td>
                            <div class="contract-input">
                                {{ $quotation->temporality == 'permanent' ? 'Permanente' : 'Período corto' }}
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td class="contract-label">Tipo de moneda:</td>
                        <td>
                            <div class="contract-input">
                                {{ isset($quotation->type_currency) ? $MONEY_TYPE[$quotation->type_currency]['name'] : '' }}(
                                @include('colones', ['currency' => $quotation->type_currency]))</div>
                        </td>
                    </tr>
                    <tr>
                        <td class="contract-label">Opciones de cobertura:</td>
                        <td>
                            <div class="contract-input" style="height: 60px;">
                                Trabajos ocasionales, no mayores a 5 días al mes, para realizar
                                actividades de mantenimiento o servicios en la casa de habitación,
                                lote o terreno declarados por el tomador del seguro.
                            </div>
                        </td>
                    </tr>
                @else

                    <tr>
                        <td class="contract-label">Actividad económica</td>
                        <td>
                            <div class="contract-input">{{ $activity_economic_name ?? '' }}</div>
                        </td>
                    </tr>
                    <tr>
                        <td class="contract-label">Temporalidad</td>
                        <td>
                            <div class="contract-input">
                                {{ $quotation->temporality == 'permanent' ? 'Permanente' : 'Período corto' }}
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td class="contract-label">Modalidad de aseguramiento:</td>
                        <td>
                            <div class="contract-input">
                                {{ isset($quotation->work_modality_id) ? $WORK_MODALITY[$quotation->work_modality_id] : '' }}
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td class="contract-label">Planilla mensual:</td>
                        <td>
                            <div class="contract-input">
                                @include('colones', ['currency' => $quotation->type_currency])
                                {{ number_format($quotation->salary_projection, 2, ',', '.') }}
                            </div>
                        </td>
                    </tr>

                    @if ($quotation->temporality == 'permanent')
                        <tr>
                            <td class="contract-label">Estimación anual de salarios:</td>
                            <td>
                                <div class="contract-input">
                                    @include('colones', ['currency' => $quotation->type_currency])
                                    {{ number_format($quotation->salary_projection * 12, 2, ',', '.') }}</div>
                            </td>
                        </tr>
                    @endif

                    <tr>
                        <td class="contract-label">Tipo de moneda:</td>
                        <td>
                            <div class="contract-input">
                                {{ isset($quotation->type_currency) ? $MONEY_TYPE[$quotation->type_currency]['name'] : '' }}(
                                @include('colones', ['currency' => $quotation->type_currency]))</div>
                        </td>
                    </tr>

                @endif


            </table>
        </div>

        <!-- Cobertura de riesgos del trabajo -->
        <div class="section-title">COBERTURA DE RIESGOS DEL TRABAJO</div>
        <div class="ui segment content-segment card">
            <p>La cobertura de este seguro se establece en el artículo 5 “Cobertura de riesgos del trabajo” de las
                condiciones generales del seguro, entre lo que resalta:</p>
            <p>
                Todo patrono está obligado a asegurar a sus personas trabajadoras contra los riesgos del trabajo, tanto
                accidentes como enfermedades con ocasión al trabajo, según se establece en el Título IV del Código de
                Trabajo y su reglamento.
            </p>
            <p>
                Constituyen riesgos del trabajo los accidentes y las enfermedades que ocurran a las personas
                trabajadoras,
                con ocasión o por consecuencia del trabajo que desempeñen en forma subordinada y remunerada, así como la
                agravación o reagravación que resulte como consecuencia directa, inmediata e indudable de esos
                accidentes y
                enfermedades.
            </p>
        </div>

        <!-- Valores de Prima -->
        <div class="section-title">VALORES DE PRIMA Y VIGENCIA FRACCIONADA</div>


        <table class="pricing-table">
            <tbody>

                @if(isset($quotation->work_modality_id) && $quotation->work_modality_id == 4)
                    <tr>
                        <td class="header-cell">Prima anual</td>
                        <td class="regular-cell">@include('colones', ['currency' => $quotation->type_currency])
                            {{ number_format($quotation->annual_calculation_amount, 2, ',', '.') }}</td>
                    </tr>
                @elseif(isset($quotation->work_modality_id) && $quotation->work_modality_id == 3)
                    <tr>
                        <td class="regular-cell-none"></td>
                        <td class="header-cell-left">Anual</td>
                        @if($quotation->semiannual_calculation_amount  != 0)
                            <td class="header-cell">Semestral</td>
                        @endif
                    </tr>
                    @if (isset($quotation->option_asegurement) && $quotation->option_asegurement == 1)
                        <tr>
                            <td class="header-cell">1 persona para servicios domésticos</td>
                            <td class="regular-cell">@include('colones', ['currency' => $quotation->type_currency])
                                {{ $quotation->type_currency == 'USD' ? '131,00' : '67,581' }}</td>
                        </tr>
                    @elseif(isset($quotation->option_asegurement) && $quotation->option_asegurement == 2)
                        <tr>
                            <td class="header-cell">2 personas para servicios domésticos</td>
                            <td class="regular-cell">@include('colones', ['currency' => $quotation->type_currency])
                                {{ $quotation->type_currency == 'USD' ? '246,00' : '126,771' }}</td>
                            <td class="regular-cell">@include('colones', ['currency' => $quotation->type_currency])
                                {{ $quotation->type_currency == 'USD' ? '128,00' : '65,921' }}</td>
                        </tr>
                    @else
                        <tr>
                            <td class="header-cell">3 personas para servicios domésticos
                            </td>
                            <td class="regular-cell">@include('colones', ['currency' => $quotation->type_currency])
                                {{ $quotation->type_currency == 'USD' ? '361,00' : '185,961' }}</td>
                            <td class="regular-cell">@include('colones', ['currency' => $quotation->type_currency])
                                {{ $quotation->type_currency == 'USD' ? '188,00' : '96,700' }}</td>
                        </tr>
                    @endif

                @else
                    @if ($quotation->temporality === 'short')
                        <tr>
                            <td class="header-cell">Pago único</td>
                            <td class="regular-cell">@include('colones', ['currency' => $quotation->type_currency])
                                {{ number_format($quotation->single_payment_value, 2, ',', '.') }}</td>
                            <td class="header-cell">Tarifa</td>
                            <td class="regular-cell">{{ $quotation->unico_percentage }}%</td>
                        </tr>
                    @else
                        <tr>
                            <td class="header-cell">Anual</td>
                            <td class="regular-cell">@include('colones', ['currency' => $quotation->type_currency])
                                {{ number_format($quotation->annual_calculation_amount, 2, ',', '.') }}</td>
                            <td class="header-cell">Tarifa</td>
                            <td class="regular-cell">{{ $quotation->anual_percentage }}%</td>
                        </tr>
                        <tr>
                            <td class="header-cell">Semestral</td>
                            <td class="regular-cell">@include('colones', ['currency' => $quotation->type_currency])
                                {{ number_format($quotation->semiannual_calculation_amount, 2, ',', '.') }}</td>
                            <td class="header-cell">Tarifa</td>
                            <td class="regular-cell">{{ $quotation->semestral_percentage }}%</td>
                        </tr>
                        <tr>
                            <td class="header-cell">Trimestral</td>
                            <td class="regular-cell">@include('colones', ['currency' => $quotation->type_currency])
                                {{ number_format($quotation->quarterly_calculation_amount, 2, ',', '.') }}</td>
                            <td class="header-cell">Tarifa</td>
                            <td class="regular-cell">{{ $quotation->trimestral_percentage }}%</td>
                        </tr>
                       
                    @endif
                @endif



            </tbody>

        </table>

        <br>
        <div class="card">
            <p class="sub-content">Cotización válida por 15 días naturales.</p>
            <p>Seguro Obligatorio de Riesgos del Trabajo con registro número {{ $quotation->id }} y fecha de registro
                {{ !empty($quotation_date)
                    ? ucfirst(strftime('%A %e de %B del %Y', strtotime($quotation_date)))
                    : '' }}.
            </p>
            <p><strong>Accidente de trabajo:</strong></p>
            <p>
                Accidente de trabajo es todo accidente que le suceda a la persona trabajadora como causa de la labor que
                ejecuta o como consecuencia de ésta, durante el tiempo que permanece bajo la dirección y dependencia del
                patrono o sus representantes, y que puede producirle la muerte o pérdida o reducción, temporal o
                permanente,
                de la capacidad para el trabajo.
            </p>
            <p><strong>Enfermedad de trabajo:</strong></p>
            <p>
                Se denomina enfermedad del trabajo a todo estado patológico, que resulte de la acción continuada de una
                causa, que tiene su origen o motivo en el propio trabajo o en el medio y condiciones en que la persona
                trabajadora labora, y debe establecerse que éstos han sido la causa de la enfermedad.
            </p>
        </div>
        <br>
        <div class="card-black">
            <table class="card-table">
                <tr>
                    <td style="width: 50%;">
                        <img src="{{ public_path('images/mnk_group.jpg') }}" alt="Grupo MNK" class="card-img">
                    </td>
                    <td>
                        <img src="{{ public_path('images/mnk_white_simples.png') }}" alt="Logo MNK"
                            style="margin-left: 80px">
                    </td>
                </tr>
            </table>
        </div>


    </div>

    <div class="footer" style="position: fixed; bottom: 80px;">
        <img src="{{ public_path('images/footer_mnk_quotation.png') }}" alt="pie de documento" style="width: 100%;">
    </div>

</body>

</html>