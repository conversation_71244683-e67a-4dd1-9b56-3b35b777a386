<!DOCTYPE html>
<html lang="es">

<head>
    <meta content="text/html; charset=utf-8" http-equiv="Content-Type" />

    <style type="text/css">
        * {
            font-family: 'Outfit', sans-serif;
            font-size: 10pt;
        }

        body {
            margin: 0 1rem;
            text-align: justify;
        }

        .watermark {
            position: fixed;
            top: 45%;
            width: 100%;
            text-align: center;
            opacity: .1;
            transform: rotate(-45deg);
            transform-origin: 50% 50%;
            z-index: 1000;
        }


        .header {
            padding: 0 15px;
            text-align: center;
            position: fixed;
            top: 0px;
            left: 0;
            right: 0;
        }

        .header img {
            min-height: 50px;
            max-height: 50px;
            width: auto;
        }

        .numpage:after {
            content: counter(page);
        }

        table,
        th,
        td {
            border: 0.5px solid black;
        }

        table.no-border,
        table.no-border th,
        table.no-border td,
        table.no-border td b {
            font-size: 7pt;
            border: none;
            text-align: center;
            max-width: 0;
        }

        th,
        td {
            padding: 1.5px;
            padding-bottom: 3px;
        }

        td[colspan=16] {
            text-align: justify;
        }

        table {
            border-collapse: collapse;
            width: 100%;
        }

        th {
            text-align: center;
            background: #BDf;
        }
    </style>
</head>

<body>

    <div class="header">
        <script type="text/php">
        </script>
    </div>
    <div>
        <p>
            <b>Generar certificado afiliado</b>
        <table>
            <p>Soporte radicación ejemplo, aun no se define el modelo que quedara;</p>
            <tr>
                <td>Nombre: </td>
                <td>{{ $arrayData->name }}</td>
            </tr>
            <tr>
                <td>Identificación: </td>
                <td>{{ $arrayData->identification_number }}</td>
            </tr>
 
            <tr>
                <td>Fecha:</td>
                <td>{{ Carbon\Carbon::createFromFormat('Y-m-d', App\Correspondence::nextDay())->formatLocalized('%B %d, %Y') }}
                </td>
            </tr>
        </table>
        <br />
        </p>
    </div>
</body>
</html>
 