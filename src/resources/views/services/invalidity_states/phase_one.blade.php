@extends('layouts.main')

@section('title', 'Bandeja de trabajo - Gestión fase I - Clasificación REI')

@section('menu')
    @parent
@endsection

@section('content')
    <div class="ui basic segment">
        <div class="ui container">
            <div class="ui five item stackable tabs menu">
                <a class="item" href="/invalidity_state/phase_zero">Fase 0 - Base nomina</a>
                <a class="item active">Fase I - Clasificación REI</a>
                <a class="item" href="/invalidity_state/phase_two">Fase II - Actualización de datos REI</a>
                <a class="item" href="/invalidity_state/phase_three">Fase III - Contactabilidad REI</a>
                <a class="item" href="/invalidity_state/phase_four">Fase IV - Calificación REI</a>
            </div>
        </div>
        <h1 class="ui header">
            Bandeja de trabajo - Gestión fase I - Clasificación REI
        </h1>
        <div class="ui segments">
            <div class="ui secondary segment">
                <form autocomplete="off" method="get" id="filters" class="ui form small clearing">
                    <div class="fields">
                        <div class="four wide field">
                            <label>Número de lote:</label>
                            <input name="id_lote" id="id_lote" value="{{ $id_lote }}" type="text"/>
                        </div>
                        <div class="eight wide field">
                            <label>&nbsp;</label>
                            <button class="ui basic green button">Buscar</button>
                        </div>
                    </div>

                    {{csrf_field()}}
                </form>
            </div>
            @if($id_lote)
                <table style="font-size: 0.8em; line-height: 1em;"
                       class="ui large celled table center" id="results">
                    <thead>
                    <tr>
                        <th>% de servicios necesarios muestreo</th>
                        <th>% de servicios necesarios devueltos</th>
                        <th>Acción</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr>
                        <td>
                            <table style="font-size: 0.8em; line-height: 1em;"
                                   class="ui large celled table center" id="results">
                                <thead>
                                <tr>
                                    <th>Total casos lote</th>
                                    <th>Casos que ingresaron a muestreo</th>
                                    <th>Casos actuales</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr>
                                    <td>{{$activities_by_lote}}</td>
                                    <td>Bu</td>
                                    <td>{{$percent_necessary_muestreo}}% ({{$activities_qualified_muestreo}} de {{$nro_acceptation_necessary}})</td>
                                </tr>
                                </tbody>
                            </table>
                        </td>
                        <td>{{$percent_reject_necessary}}% ({{$nro_reject}} de {{$nro_reject_necessary}}))</td>
                        <td class="">
                            <a class="ui basic green icon button"
                               href="{{secure_url('/invalidity_state/phase_one/approve_lote/'. $id_lote) }}">
                                Aprobar lote
                            </a>
                            @if($nro_reject_necessary <= $nro_reject)
                                <a class="ui basic orange icon button"
                                    href="{{secure_url('/invalidity_state/phase_one/reject_lote/'. $id_lote) }}">
                                    Rechazar lote
                                </a>
                            @endif
                            <a class="ui basic orange icon button"
                               href="{{secure_url('/invalidity_state/phase_one/return_lote/'. $id_lote) }}">
                                Devolver a proveedor
                            </a>
                        </td>
                    </tr>
                    </tbody>
                </table>
            @else
                <div class="ui secondary segment">
                    <form autocomplete="off" method="get" id="filters" class="ui form small clearing">
                        <div class="fields">
                            <div class="four wide field">
                                <label>Número de casos a asignar (Máximo {{$activities_to_phase2}}):</label>
                                <input name="num_activities" id="num_activities" value="" type="text"/>
                            </div>
                            <div class="eight wide field">
                                <label>&nbsp;</label>
                                <button class="ui basic green button" id="btn_provider" onclick="assignActivities(event, 'provider')">Asignar a proveedor</button>
{{--                                <button class="ui basic green button" id="btn_colpensiones" onclick="assignActivities(event, 'colpensiones')">Asignar a Colpensiones</button>--}}
                                <div class="ui inline loader" id="loader_assign"></div>
                            </div>
                        </div>
                        <div class="fields">
                            <div class="ui negative message" id="messageErrorFieldsEmpties" style="text-align: center; display: none">
                            </div>
                        </div>

                        {{csrf_field()}}
                    </form>
                </div>
            @endif
            <br>
            <p style="text-align: right; padding-right: 10px"><b>{{count($activities)}} casos</b></p>
            <table style="font-size: 0.8em; line-height: 1em;"
                   class="ui large celled table" id="results">
                <thead>
                <tr>
                    <th>Id</th>
                    <th>Tipo de documento - Número de documento</th>
                    <th>Nombres</th>
                    <th>Departamento - Municipio</th>
                    <th>Fecha de Nacimiento</th>
                    <th>Edad</th>
                    <th>Genero</th>
                    <th>Edad en 12 Meses</th>
                    <th>Estado Actual del servicio</th>
                    <th>No. lote</th>
                    <th>Fecha creación lote</th>
                    <th>Acciones</th>
                </tr>
                </thead>
                <tbody>
                @foreach($activities as $activity)
                    <tr class="{{$activity->ID_ACTION_MUESTREO === \App\Action::CLASSIFICATION_AUDIT_SAMPLE_REI_P1 ? 'positive' : ''}}">
                        <td>{{$activity->ID_SERVICIO}}</td>
                        <td>{{$activity->DOCUMENTO}}</td>
                        <td>{{$activity->NOMBRE}}</td>
                        <td>{{$activity->DEPARTAMENTO}}</td>
                        <td>{{$activity->FECHA_NACIMIENTO}}</td>
                        <td>{{$activity->EDAD}}</td>
                        <td>{{$activity->GENERO}}</td>
                        <td>{{$activity->EDAD_12}}</td>
                        <td>{{$activity->ESTADO_SERVICIO}}</td>
                        <td>{{$activity->ID_LOTE}}</td>
                        <td>{{$activity->FECHA_CREACION_LOTE}}</td>
                        <td style="width: 100px">
                            <a class="ui circular basic blue icon button"
                               data-tooltip="Ir al servicio" data-position="top center"
                               target="_blank"
                               href="{{secure_url('/servicio/' . $activity->ID_SERVICIO)}}">
                                <i class="eye icon"></i>
                            </a>
                        </td>
                    </tr>
                @endforeach
                </tbody>
                <tfoot>
                <tr>
                    <th></th>
                    <th></th>
                    <th></th>
                    <th></th>
                    <th></th>
                    <th></th>
                    <th></th>
                    <th></th>
                    <th></th>
                    <th></th>
                    <th></th>
                    <th><b>{{count($activities)}} casos</b></th>
                </tr>
                </tfoot>
                </tfoot>
            </table>
        </div>
    </div>

    <script type="text/javascript">
        const totalActivities = {!! $activities_to_phase2 !!};
        $(document).ready(function () {

            $('form input').keydown(function (event) {
                if (event.keyCode == 13) {
                    event.preventDefault();
                    return false;
                }
            });

            $('.datepicker').pickadate({
                selectYears: true,
                selectMonths: true,
                format: 'yyyy-mm-dd',
                formatSubmit: 'yyyy-mm-dd'
            });

            $('.ui.selection.dropdown').dropdown();
        });

        function assignActivities(e, type) {
          e.preventDefault();
          $('#messageErrorFieldsEmpties').hide();
          const numActivities = $('#num_activities').val();
          if (numActivities > totalActivities) {
            $('#messageErrorFieldsEmpties').text('El Número de casos a asignar no puede ser mayor a ' + totalActivities);
            $('#messageErrorFieldsEmpties').show();
            return;
          }
          if (numActivities !== '') {
            $('#loader_assign').addClass('active');
            $('#btn_provider').addClass('disabled');
            // $('#btn_colpensiones').addClass('disabled');

            $.ajax({
              type: 'POST',
              async: false,
              url: '/invalidity_state/phase_one/assign_activities',
              dataType: 'json',
              data: JSON.stringify({
                num_activities: numActivities,
                type: type,
              }),
              contentType: 'application/json',
              success: function (response) {
                console.log(response);
                $('#loader_assign').removeClass('active');
                if (response['error']) {
                  ajaxError = true;
                } else {
                  location.href = '{{secure_url('invalidity_state/phase_two')}}';
                }
              },
              error: function (e) {
                $('#loader_assign').removeClass('active');
                console.error(e);
              }
            });
          } else {
            $('#messageErrorFieldsEmpties').text('Asegúrate de primero de digitar le numero de casos a asignar y luego si dar clic en los botones de Asignar')
            $('#messageErrorFieldsEmpties').show();
          }
        }
    </script>
@endsection
