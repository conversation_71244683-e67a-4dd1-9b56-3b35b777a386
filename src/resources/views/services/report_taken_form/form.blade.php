@extends('layouts.main')

@section('title', 'Template')

@section('menu')
    @parent
@endsection 

@section('content')
    <div class="ui basic segment">
        <h1 class="ui header">
            Reporte planilla tomador #{{$activity->report_taken_form->id}}
            <div class="sub header">Campos con <span style="color: red;" class="required">*</span> obligatorios.</div>
        </h1>
        <form autocomplete="off" action="" id="dictamen"
              method="post" class="ui small form">
            <div class="ui secondary segment">
                <div class="ui three columns grid">
                    <div class="column">
                        <b>Identificación:</b> {{$activity->affiliate->doc_type}} {{$activity->affiliate->doc_number}}
                    </div>
                    <div class="column"><b>Nombre:</b> <a
                                href="{{secure_url('afiliado/' . $activity->affiliate_id)}}">{{ mb_convert_case(mb_strtolower( $activity->affiliate->full_name ?? ''), MB_CASE_TITLE, "UTF-8") }}</a>
                    </div>
                    <div class="column"><b>Actividad:</b> <a
                                href="{{secure_url('servicio/' . $activity->id)}}">{{$activity->service->name}}</a>
                    </div>
                    <div class="column"><b># reporte:</b> {{$activity->report_taken_form->id}}</div>
                    <div class="column"><b>Fecha solicitud:</b> {{ ucfirst(strftime('%A %e de %B del %Y', strtotime($activity->created_at)))}}
                    </div>
                </div>
            </div>
            <div class="ui styled fluid accordion">
                <div class="title"><i class="dropdown icon"></i> Información básica <span style="color: red;"
                                                                                          class="required">*</span>
                </div>
                <div class="content">
            
                    <table class="ui celled table  ">
                        <thead>
                            <tr>
                                <th>Tipo de ingreso</th>
                                <th>Total trabajadores</th>
                                <th>Total salarios</th>
                                <th>Fecha de carga</th>
                                <th>Periodo</th>
                                <th>vigencia desde</th>
                                <th>Vigencia hasta</th>
                                <th>Planilla</th>
                            </tr>
                        </thead>
                        <tbody>
                                <tr> 
                                    <td>
                                       {{$activity->report_taken_form->entry_type}}
                                    </td>
                                    <td>
                                        {{$activity->report_taken_form->total_affiliates}}
                                    </td>
                                    <td>
                                        {{ ($policy_sort && $policy_sort->type_currency === 'USD'
                                            ? '$'
                                            : '₡') 
                                        }}
                                        {{number_format($activity->report_taken_form->total_salaries, 2, ',', '.')}}
                                    </td>
                                    <td>
                                        {{ ucfirst(strftime('%A %e de %B del %Y', strtotime($activity->report_taken_form->created_at)))}}

                                    </td>
                                    <td>
                                        {{ $emision ? 'Emisión' : ucfirst(strftime('%B', strtotime($lastDayMonth))) }}
                                    </td>
                                    <td>
                                        {{isset($firstDay) ? ucfirst(strftime('%A %e de %B del %Y', strtotime($firstDay))) : 'Emisión'}}
                                    </td>
                                    <td>
                                        {{isset($lastDayMonth) ? ucfirst(strftime('%A %e de %B del %Y', strtotime($lastDayMonth))) : 'Emisión'}}
                                    </td>
                                    <td>
                                        @if (!empty($documentdSpreadsheet->path))
                                            <button type="button"
                                                    onclick="window.open('{{ secure_url('file/' . $documentdSpreadsheet->path) }}', '_blank')"
                                                    class="ui icon tiny secondary button" title="Descargar planilla">
                                                <i class="download icon"></i>
                                            </button>

                                            <button type="button"
                                                    onclick="window.open('{{ secure_url('/spreadsheet/'.$activity->report_taken_form->id.'/generar-excel') }}', '_blank')"
                                                    class="ui icon tiny secondary button" title="Descargar Excel">
                                                <i class="file excel icon"></i>
                                            </button>
                                        @elseif($emision)
                                            Planilla emisión sin certificado
                                        @else
                                            <button
                                                type="button"
                                                onclick="Swal.fire({
                                                        title: 'Certificado de Planilla en Proceso',
                                                        text: `El certificado de planilla está siendo generado, este estará disponible para su descarga el día de mañana. Gracias por su paciencia.`,
                                                        imageUrl: '/file/client_logo/logo_mnk.png',
                                                        imageHeight: 50,
                                                        imageWidth: 150,
                                                        confirmButtonText: 'Cerrar'
                                                    });"
                                                class="ui button secondary">
                                                <i class="info circle icon"></i>Certificado en proceso
                                            </button>
                                        @endif
                                    </td>
                                </tr>
                        </tbody>
                    </table>
           

                </div>
            </div>
            <div class="ui basic segment">
                <div class="ui error message"></div>
                <div class="fields">
                    <div class="six wide field">
                    
                        <a href="{{secure_url('/servicio/' . $activity->id)}}" class="ui secondary small button"><i
                                    class="arrow left icon"></i> Volver a la actividad</a>
                    </div>
                </div>
            </div>
            {{csrf_field()}}
        </form>
    </div>

    <style type="text/css">
        .ui.grid .column {
            padding: 0.5rem 1rem !important;
        }

        .sender .field {
            display: none;
        }

        .ear.field {
            display: none;
        }

        .msp.field {
            display: none;
        }

        .field > h3 {
            text-align: center;
            margin-top: 1.25rem;
        }

        .ui.search > .results {
            width: 30rem;
        }

        .ui.search > .results .result .title {
            padding: 0 !important;
            border: none !important;
            text-transform: none;
        }

        .ui.search > .results .result .content {
            padding: 0 !important;
        }

        th {
            text-align: center !important;
        }
    </style>
    <script type="text/javascript">



        var toggleSender = function () {
            console.log('sender');
            if ($('[name=sender]').val() == 5 || $('[name=sender]').val() == 8) {
                $('.sender .field').show();
            } else {
                $('.sender .field').hide();
            }
        };

        var cie10 = [];


        $(document).ready(function () {
            $('.ui.accordion .ui.grid .row').css('padding-top', 0);
            $('.ui.accordion .ui.grid .column').css('padding-top', 0);

            $('.ui.accordion').accordion({
                exclusive: false
            });
            $('.ui.accordion .title, .ui.accordion .content').addClass('active');
            $('form#dictamen a.red').click(function () {
                $(this).parent().parent().remove();
                calculateDeficienceSum();
            });
            $('form .ui.dropdown').dropdown({
                forceSelection: false
            });
            $('form .datepicker').pickadate({
                selectYears: 100,
                selectMonths: true,
                max: new Date(),
                formatSubmit: 'yyyy-mm-dd',
                format: 'mmm dd, yyyy'
            });

            $.getJSON('/js/cie10.json', function (json) {
                cie10 = json;
                $('form .ui.search.code').search({
                    source: cie10,
                    fields: {
                        title: 'COD',
                        description: 'DESCRIPTION'
                    },
                    searchFields: ['COD', 'DESCRIPTION'],
                    regExp: {
                        escape: /[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,
                        beginsWith: ''
                    },
                    showNoResults: true,
                    maxResults: 250,
                    searchFullText: false,
                    error: {
                        noResults: 'No se encontraron resultados para tu búsqueda.'
                    },
                    onSelect: function (result, response) {
                        $(this).parents('.fields').find('input.description').val(result.DESCRIPTION);
                    }
                });
            });

            $('form input').keydown(function (event) {
                if (event.keyCode == 13) {
                    event.preventDefault();
                    return false;
                }
            });


            @if (Auth::user()->isViewer())
            $('.datepicker').pickadate('picker').stop();
            $('#dictamen .ui.search input').attr('disabled', 'disabled');
            $('#dictamen input, #dictamen textarea').attr('readonly', 'readonly');
            $('#dictamen .ui.dropdown').addClass('disabled');
            $('#dictamen .button').remove();
            @endif
        });
    </script>
@endsection