@extends('layouts.main')

@section('title', 'RENOVACION')

@section('menu')
    @parent
@endsection

@section('content')
    <div class="ui basic segment">
        <h1 class="ui header">
            Renovación
            <div class="sub header">Campos con <span style="color: red;" class="required">*</span> obligatorios.</div>
        </h1>
        <div class="ui secondary segment">
            <div class="ui three columns grid">
                <div class="column">
                    <b>Identificación:</b>{{$activity->affiliate->doc_number ?? ''}}
                </div>
                <div class="column"><b>Nombre:</b> <a
                            href="{{ secure_url('afiliado/' . $activity->affiliate_id) }}">{{ ucwords(mb_strtolower($activity->affiliate->full_name)) }}</a>
                </div>
                <div class="column"><b>Actividad:</b> <a
                            href="{{ secure_url('servicio/' . $activity->id) }}">{{ $activity->service->name }}</a>
                </div>
            </div>
            <div class="ui three columns grid">
                <div class="column"><b># de servicio:</b>{{$renewalSorts->id ?? ''}}</div>
                <div class="column">
                    <b>Fecha solicitud:</b> <span id="formatte-date"> {{ isset($renewalSorts->created_at) ? ucfirst(strftime('%A %e de %B del %Y',strtotime($renewalSorts->created_at))) : '' }} </span>
                </div>
            </div>
        </div>
        <form autocomplete="off" action="{{ secure_url("/servicio/{$id}/pe_ip_sort/save") }}" id="peip_form" method="post" class="ui small form">
            <div class="ui styled fluid accordion">
                {{csrf_field()}}
                @include('services.renewal_sort.steps.renewal_data')
            </div>
            <div class="ui basic segment">
                <div class="fields">
                    <div class="six wide field">
{{--                        <button class="ui primary button" onclick="" style="width: 40%">--}}
{{--                            <i class="save icon"></i> Guardar--}}
{{--                        </button>--}}
                        <a href="{{ secure_url('/servicio/' . $activity->id) }}" class="ui secondary button"><i class="arrow left icon"></i> Volver a la actividad</a>
                    </div>
                </div>
            </div>
        </form>

        <div style="display: none" id="loading" class="ui active centered inline loader"></div>
    </div>
    <style>
        .none-transform {
            text-transform: none !important;
        }
        .error {
            border-color: rgba(252, 57, 57, 0.97) !important;
        }

        .item-label {
            font-weight: bold;
            margin-right: 10px;
        }

        .item-value {
            font-size: 1em;
            color: #333;
        }

        .ui.grid {
            margin-top: -1rem;
            margin-bottom: -1rem;
            margin-right: -1rem;
        }

        .ui.accordion .title {
            text-transform: uppercase;
        }

        .ui.styled.accordion .accordion .content,
        .ui.styled.accordion .content {
            margin: 0;
            padding: .5em 1em .3em;
        }

        .disability .item,
        .general-info .item,
        .medical_audit .item,
        .financial_audit .item,
        .technical_audit .item {
            display: grid !important;
            grid-gap: 12px !important;
        }

        .disability .column,
        .general-info .column,
        .medical_audit .column,
        .financial_audit .column,
        .technical_audit .column {
            padding-top: 0px !important;
            padding-left: 1rem !important;
        }
    </style>
    <script>

        $(document).ready(function() {

            $('.ui.dropdown').dropdown();
            $('.timepicker').pickatime();

            $('.ui.accordion').accordion({
                exclusive: false
            });

            $('.ui.accordion .title').each(function(index) {
                $('.ui.accordion').accordion('open', index);
            });

        });

    </script>

@endsection
