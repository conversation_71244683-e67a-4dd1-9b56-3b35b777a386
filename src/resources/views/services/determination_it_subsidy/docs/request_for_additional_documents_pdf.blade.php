<!DOCTYPE html>
<html lang="es">
<head>
    <meta content="text/html; charset=utf-8" http-equiv="Content-Type"/>
    <style type="text/css">
        * {
            font-family: 'Arial', sans-serif;
            font-size: 10pt;
        }

        body {
            margin: 0 2rem 3rem 2rem;
            padding-top: 75px;
            text-align: justify;

        }

        .watermark {
            position: fixed;
            top: 45%;
            width: 100%;
            text-align: center;
            opacity: .1;
            transform: rotate(-45deg);
            transform-origin: 50% 50%;
            z-index: 1000;
        }

        .footer {
            height: 40px;
            width: 100%;
            position: fixed;
            z-index: 0 !important;
            left: 26px;
            right: 26px;
            top: 87%;
            border-top: 0.5px solid black;
        }

        .header {
            height: 40px;
            width: 100%;
            position: fixed;
            z-index: 0 !important;
        }

        .header img {
            height: 57px;
            width: 752px;
            position: absolute;
            left: -76px;
            top: -8px;
            opacity: 0.5;
        }

        .footer img {
            min-height: 50px;
            max-height: 50px;
            width: auto;
            text-align: left;
        }

        .numpage:after {
            content: counter(page);
        }

        #decreto {
            padding: 0 30px 0 30px;
            font-style: italic;
        }

        #ordenJudicial {
            font-style: italic;
        }

        table,
        th,
        td {
            border-collapse: collapse;
            border: 0.5px solid black;
        }

        table td th {
            text-align: center;
        }

        td {
            font-size: 10px;
            overflow-x: auto;
        }

        .table-style {
            width: 100%;
        }

        .table-style th {
            color: black;
            padding: 10px;
        }

        .table-style td {
            padding: 8px;
            text-align: justify;
        }

        #aprobaciones {
            font-size: 8pt;
        }
    </style>
</head>

@php
    $numberBizagi = '';
    $date_action = null;
    foreach($activity->activity_actions as $aa){
        if ($aa->action_id == 647) {
           $date_action = $aa->created_at;
           foreach ($aa->fields as $field){
               if ($field->action_field_id == 348){
                   $numberBizagi = $field->value;
               }
           }
        }
    }
    setlocale(LC_TIME, 'Spanish');
    $formattedDate = $date_action ? $date_action->formatLocalized('%d de %B de %Y') : '';
    $interested_part = $activity->determination_it->interested_part_doc_req;
    if ($interested_part == 'ARL'):
        $interested_part = $ARL_LIST;
    endif;
    if($interested_part == 'EPS'):
        $interested_part = $EPS_LIST;
    endif;

@endphp

<body>
{{--<div class="watermark">
    <span style="font-size: 72pt;font-weight: 500;">BORRADOR</span>
</div>--}}
<div class="header">
    <img src="{{storage_path('app/header_mdi.jpeg')}}" alt="Logo Colpensiones">
</div>
<div class="footer">
    <table style="width: 100%">
        <tr>
            <td colspan="10" style="border: transparent;text-align: justify;opacity: 0.5;">
                <b>Colpensiones</b><br>
                Dirección: Carrera 10 No.72 – 33 Torre B Piso 11, Bogotá D.C., Colombia<br>
                Conmutador: (+57) 601 489 0909; Línea Gratuita: 01 8000 410909<br>
                www.colpensiones.gov.co
            </td>
            <script type="text/php">
                if (isset($pdf)) {
                    $x = $pdf->get_width() - 98;
                    $y= 770;
                    $text = "Página | {PAGE_NUM}";
                    $font = $fontMetrics->get_font("Helvetica", "normal");
                    $size = 9;
                    $color = array(0.430,0.430,0.430);
                    $word_space = 0.0;  //  default
                    $char_space = 0.0;  //  default
                    $angle = 0.0;   //  default
                    $pdf->page_text($x, $y, $text, $font, $size, $color, $word_space, $char_space, $angle);
                }
            </script>
        </tr>
    </table>
</div>

<div>
    <p style="text-align: right">No. de Radicado, {{$numberBizagi}}</p>
    <p>
        <b>Bogotá
            D.C.,</b>{{ $formattedDate }}
    </p>
    <br>
    <p>
        </b> <br/>

        Señor(a): <br/> <b>{{$activity->determination_it->interested_part_doc_req ? : ''}} -
            {{ $activity->determination_it->interested_part_doc_req == 'AFILIADO'
                ? $activity->determination_it->interested_part_name_doc_req : $interested_part[$activity->determination_it->interested_part_name_doc_req]}}
        </b>
        <br/>
        Dirección: {{ strtoupper($activity->determination_it->interested_part_address_doc_req) ? : ''}}
        <br/>
        Cel: {{ $activity->determination_it->interested_part_phone_doc_req ? : ''}}
        <br/>
        {{ $activity->determination_it->city_interested_part ? : ''}}
        <br/>
    </p>
    </b> <br/>
    <p><strong>Referencia:</strong><strong>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;</strong>Radicado
        No. {{ $activity->determination_it->numero_radicado ? : '' }}<br/>
        <strong>Ciudadano:&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
            &nbsp;&nbsp;</strong>{{ $activity->affiliate->full_name }}<br/>
        <strong>Identificación:</strong><strong>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
            &nbsp;</strong>{{ $activity->affiliate->doc_type }} {{ $activity->affiliate->doc_number }} <br/>
        <strong>Tipo trámite:&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;</strong>Determinación del
        Subsidio por Incapacidad Temporal</p>

    <br>
    <p>
        Respetado(a) señor(a): <br/><br/>
        Reciba un cordial saludo de la Administradora Colombiana de Pensiones – COLPENSIONES.
    </p>

    <p>
        En atención al trámite de Determinación de Subsidio por Incapacidad Temporal de la referencia, nos permitimos
        comunicarle que, una vez validados los documentos aportados por usted, se establece que deberá aportar la prueba
        documental que a continuación se relaciona, la cual, resulta necesaria para que esta Administradora de Pensiones
        decida de fondo su solicitud:
    </p>

    @php
        $hasObservation = false;
    @endphp

    @foreach($activity->determination_it->document_request as $doc_type)
        @if($doc_type->document_observation)
            @php
                $hasObservation = true;
            @endphp
        @endif
    @endforeach

    @if($hasObservation)
        <br>
        <table class="table-style">
            <tr>
                <th>TIPO DE DOCUMENTO</th>
                <th>OBSERVACIÓN</th>
            </tr>
            @foreach($activity->determination_it->document_request as $doc_type)
                <tr>
                    <td>{{$IT_DOCUMENT_TYPE_SOL_DOCS[$doc_type->document]}}</td>
                    <td>{{$doc_type->document_observation}}</td>
                </tr>
            @endforeach
        </table>
        <br>
    @else
        <ul>
            @foreach($activity->determination_it->document_request as $doc_type)
                <li>
                    {{$IT_DOCUMENT_TYPE_SOL_DOCS[$doc_type->document]}}
                </li>

            @endforeach
        </ul>
    @endif

    <p>
        Asimismo, nos permitimos informarle que dicho documento deberá aportarlo dentro del término de un (1) mes
        contado a partir del día siguiente a la fecha de recepción de la presente comunicación, esto, en aplicación del
        artículo 17 de la Ley 1437 de 2011 modificado por el artículo 1º de la Ley 1755 que señala:
    </p>

    <p id="decreto">
        “Artículo 17. Peticiones incompletas y desistimiento tácito. (…)

        <br/>

        Cuando en el curso de una actuación administrativa la autoridad advierta que el peticionario debe realizar una
        gestión de trámite a su cargo, necesaria para adoptar una decisión de fondo, lo requerirá por una sola vez para
        que la efectúe en el término de un (1) mes, lapso durante el cual se suspenderá el término para decidir.

        Se entenderá que el peticionario ha desistido de su solicitud o de la actuación cuando no satisfaga el
        requerimiento, salvo que antes de vencer el plazo concedido solicite prórroga hasta por un término igual.(…)”
    </p>

    <p style="word-wrap: break-word;">
        De otra parte, resulta necesario que el ciudadano radique el documento mencionado anteriormente en cualquier
        Punto de Atención Colpensiones – PAC por el subtrámite de Recepción de Documentos Medicina Laboral o a través de
        la sede electrónica <a
                href="https://www.colpensionestransaccional.gov.co/sede_electronica/tramitesweb/Paginas/RadicarColp/RadicarColpWeb.aspx"
                style="font-size: 0.9em; !important;">
            https://www.colpensionestransaccional.gov.co/sede_electronica/tramitesweb/Paginas/RadicarColp/RadicarColpWeb.aspx
        </a>
    <p>

    <p>
        Lo anterior, en aras de garantizar la correcta gestión de su solicitud a través de los sistemas de información
        dispuestos por la Entidad y asegurar el cumplimiento de las directrices y normativa que en materia de gestión
        documental y organización de archivos ha emitido el Archivo General de la Nación (AGN).
    </p>

    <p>
        En caso de requerir información adicional, por favor acercarse a nuestros Puntos de Atención Colpensiones (PAC);
        comunicarse con la línea de servicio al ciudadano en Bogotá al (601) 4890909, en Medellín al (604) 2836090, o
        con la línea gratuita nacional al 018000 41 0909, en donde estaremos dispuestos a brindarle el mejor servicio.
    </p>
</div>
<div style="page-break-inside:avoid;">
    <p>
        Agradecemos su confianza y le recordamos que estamos para servirle.
    </p>
    <p>Cordialmente,</p>
    <br>
    <br>
    <p>
        <img style="height: 50px; width: auto;" alt="FIRMA" src="{{storage_path('app/firma_lmlozano.jpg')}}"/>
        <br/>
        <b>LUZ MARYEN LOZANO ROSAS</b><br/>
        Directora<br/>
        <b>Dirección de Medicina Laboral</b> <br/>
    </p>
    @php
        $authorAnalyst = null;
        $authorLiquidator = null;
        $authorAuditor = null;
        $actionAuditor = false;
        $analystDate = null;
        $liquidatorDate = null;
        $auditorDate = null;
        foreach ($activity->activity_actions_it as $aa) {
            if ($aa->action_id == 642 && (!$analystDate || $aa->created_at > $analystDate)) {
                $analystDate = $aa->created_at;
                $authorAnalyst = $determination_it->authorNameEmpresa($aa->author_id);
            }
            if ($aa->action_id == 645 && (!$auditorDate || $aa->created_at > $auditorDate)) {
                $auditorDate = $aa->created_at;
                $authorAuditor = $determination_it->authorNameEmpresa($aa->author_id);
                $actionAuditor = true;
            }
        }
    @endphp
    <br>
    <p id="aprobaciones">
        Analizó:<strong>&nbsp;&nbsp;</strong>{{ $authorAnalyst ? strtoupper($authorAnalyst['author_name']) : '' }}
        {{ $authorAnalyst ? '- ' . strtoupper($authorAnalyst['company_name']) : '' }}
        <br>
        {!! $authorLiquidator ? 'Liquidó:<strong>&nbsp;&nbsp;</strong>' : '' !!}
        {{ $authorLiquidator ? strtoupper($authorLiquidator['author_name']) : '' }}
        {{ $authorLiquidator ? '- ' . strtoupper($authorLiquidator['company_name']) : '' }}
        <br>
        {!! $authorAuditor ? 'Auditó:<strong>&nbsp;&nbsp;</strong>' : '' !!}
        @if ($actionAuditor)
            {{ $authorAuditor ? strtoupper($authorAuditor['author_name']) : '' }}
            {{ $authorAuditor ? '- ' . strtoupper($authorAuditor['company_name']) : '' }}
            <br>
        @endif
    </p>
    <br>

    @if($activity->determination_it->interested_part_doc_req == 'EPS')
        <p>
            Copia: {{ $activity->affiliate->full_name }} Dirección: {{ strtoupper($activity->determination_it->address) }} Departamento y Ciudad: {{ $activity->determination_it->city }}.
        </p>
    <br>
    @endif

    <br>
</div>
</body>