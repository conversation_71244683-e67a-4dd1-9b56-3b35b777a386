<!DOCTYPE html>
<html lang="es">

<head>
    <meta content="text/html; charset=utf-8" http-equiv="Content-Type" />
    <style type="text/css">
        * {
            font-family: 'Arial', sans-serif;
            font-size: 10pt;
        }

        body {
            margin: 0 2rem 3rem 2rem;
            padding-top: 75px;
            text-align: justify;

        }

        .watermark {
            position: fixed;
            top: 45%;
            width: 100%;
            text-align: center;
            opacity: .1;
            transform: rotate(-45deg);
            transform-origin: 50% 50%;
            z-index: 1000;
        }

        .footer {
            height: 40px;
            width: 100%;
            position: fixed;
            z-index: 0 !important;
            left: 26px;
            right: 26px;
            top: 87%;
            border-top: 0.5px solid black;
        }

        .header {
            height: 40px;
            width: 100%;
            position: fixed;
            z-index: 0 !important;
        }

        .header img {
            height: 57px;
            width: 752px;
            position: absolute;
            left: -76px;
            top: -8px;
            opacity: 0.5;
        }

        .footer img {
            min-height: 50px;
            max-height: 50px;
            width: auto;
            text-align: left;
        }

        .numpage:after {
            content: counter(page);
        }

        #decreto {
            padding: 0 30px 0 30px;
            font-style: italic;
        }

        #ordenJudicial {
            font-style: italic;
        }

        table,
        th,
        td {
            border-collapse: collapse;
            border: 0.5px solid black;
        }

        table td th {
            text-align: center;
        }

        td {
            font-size: 10px;
            overflow-x: auto;
        }

        .table-style {
            width: 100%;
        }

        .table-style th {
            color: black;
            padding: 10px;
        }

        .table-style td {
            padding: 8px;
        }

        #aprobaciones {
            font-size: 8pt;
        }
    </style>
</head>

@php
    $numberBizagi = '';
    $date_action = null;
    foreach ($activity->activity_actions as $aa) {
        if ($aa->action_id == 655) {
            $date_action = $aa->created_at;
            foreach ($aa->fields as $field) {
                if ($field->action_field_id == 350) {
                    $numberBizagi = $field->value;
                }
            }
        }
    }
    setlocale(LC_TIME, 'Spanish');
    $formattedDate = $date_action ? $date_action->formatLocalized('%d de %B de %Y') : '';
    $interested_part = $activity->determination_it->interested_part_doc_req;
    if ($interested_part == 'ARL'):
        $interested_part = $ARL_LIST;
    endif;
    if ($interested_part == 'EPS'):
        $interested_part = $EPS_LIST;
    endif;

@endphp

<body>
    <div class="watermark">
        <span style="font-size: 72pt;font-weight: 500;">BORRADOR</span>
    </div>
    <div class="header">
        <img src="{{ storage_path('app/header_mdi.jpeg') }}" alt="Logo Colpensiones">
    </div>
    <div class="footer">
        <table style="width: 100%">
            <tr>
                <td colspan="10" style="border: transparent;text-align: justify;opacity: 0.5;">
                    <b>Colpensiones</b><br>
                    Dirección: Carrera 10 No.72 – 33 Torre B Piso 11, Bogotá D.C., Colombia<br>
                    Conmutador: (+57) 601 489 0909; Línea Gratuita: 01 8000 410909<br>
                    www.colpensiones.gov.co
                </td>
                <script type="text/php">
                if (isset($pdf)) {
                    $x = $pdf->get_width() - 98;
                    $y= 770;
                    $text = "Página | {PAGE_NUM}";
                    $font = $fontMetrics->get_font("Helvetica", "normal");
                    $size = 9;
                    $color = array(0.430,0.430,0.430);
                    $word_space = 0.0;  //  default
                    $char_space = 0.0;  //  default
                    $angle = 0.0;   //  default
                    $pdf->page_text($x, $y, $text, $font, $size, $color, $word_space, $char_space, $angle);
                }
            </script>
            </tr>
        </table>
    </div>

    <div>
        <p style="text-align: right">No. de Radicado, </p>
        <p>
            <b>Bogotá
                D.C., </b>{{ $formattedDate }}
        </p>
        <br>
        <p>
            </b> <br />
            Señor(a): <br />
            <b>{{ $activity->determination_it->full_name_affiliate }}</b> <br />
            {{ strtoupper($activity->determination_it->address) }}<br />
            Cel: {{ $activity->determination_it->cellphone }}<br />
            {{ $activity->determination_it->city }}
            <br />
        </p>
        </b> <br />
        <p><strong>Referencia:</strong><strong>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
                &nbsp;&nbsp;</strong>Radicado
            No. {{ $activity->id_bizagi }}<br />
            <strong>Ciudadano:&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
                &nbsp;&nbsp;</strong>{{ $activity->affiliate->full_name }}<br />
            <strong>Identificación:</strong><strong>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
                &nbsp;</strong>{{ $activity->affiliate->doc_type }} {{ $activity->affiliate->doc_number }} <br />
            <strong>Tipo trámite:&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;</strong>Determinación del
            Subsidio por Incapacidad Temporal
        </p>

        <br>
        <p>
            Respetado(a) señor(a): <br />
            Reciba un cordial saludo de la Administradora Colombiana de Pensiones – COLPENSIONES.
        </p>

        <p>
            En atención al trámite iniciado por usted, relacionado con el pago de las incapacidades presentadas a través
            del radicado de la referencia, resulta pertinente señalar que Colpensiones está a cargo del pago de las
            incapacidades por enfermedad general o accidente de origen común, hasta por 360 días calendario adicionales
            a los primeros 180 días reconocidos por su Entidad Promotora de Salud (EPS), según lo establecido en el
            artículo 142 del Decreto Ley 019 de 2012, del cual, nos permitimos transcribir el aparte referente al
            reconocimiento de las mencionadas prestaciones económicas:
        </p>

        <p id="decreto">
            <u>
                “Para los casos de accidente o enfermedad común en los cuales exista concepto favorable de
                rehabilitación de
                la Entidad Promotora de Salud,</u> la Administradora de Fondos de Pensiones postergará el trámite de
            calificación de Invalidez hasta por
            un término máximo de trescientos sesenta (360) días calendario adicionales a los primeros ciento ochenta
            (180)
            días de incapacidad temporal reconocida por la Entidad Promotora de Salud, evento en el cual, con cargo al
            seguro previsional (sic) de invalidez y sobrevivencia o de la entidad de previsión social correspondiente
            que lo hubiere expedido, la Administradora de Fondos de Pensiones otorgará un subsidio equivalente a la
            incapacidad que venía disfrutando el trabajador.

            <br />

            Las Entidades Promotoras de Salud deberían emitir dicho concepto antes de cumplirse el día (120) de
            incapacidad temporal y enviarlo antes de cumplirse el día (150), a cada una de las Administradoras de Fondos
            de Pensiones donde se encuentre afiliado el trabajador a quien se le expida el concepto respectivo, según
            corresponda. <u>Cuando la Entidad Promotora de Salud no expida el concepto favorable de rehabilitación, si a
                ello hubiere lugar, deberá pagar un subsidio equivalente a la respectiva incapacidad temporal después de
                los
                ciento ochenta (180) días iniciales con cargo a sus propios recursos, hasta cuando se emita el
                correspondiente concepto.</u>(…)” (Apartes subrayados declarados condicionalmente exequibles por la
            Corte
            Constitucional mediante Sentencia C-270/23 en el entendido de que respecto de los trabajadores con concepto
            de rehabilitación desfavorable, la AFP deberá iniciar de inmediato el proceso de calificación de invalidez y
            asumir el pago del subsidio de incapacidad sin que exceda del día 540 de incapacidad).
        </p>

        <p>
            De acuerdo con lo señalado en la norma transcrita y una vez estudiada la solicitud radicada por usted, nos
            permitimos comunicarle que no es procedente el pago de la(s) incapacidades por la(s) siguiente(s)
            causal(es):
        </p>
        <br>
        <table class="table-style">
            <tr>
                <th style="width:20%">FECHA INICIAL</th>
                <th style="width:20%">FECHA FINAL</th>
                <th style="width:60%">CAUSAL RECHAZO</th>
            </tr>
            @php
                $rejectedCount = 0;
            @endphp

            @foreach ($activity->determination_it->inabilities as $inability)
                @if ($inability->right_validation == 'RECHAZADO')
                    @php
                        $rejectedCount++;
                    @endphp
                    <tr style="text-align: center;">
                        <td>{{ \Carbon\Carbon::parse($inability->initial_date)->formatLocalized('%d de %B de %Y') }}
                        </td>
                        <td>{{ \Carbon\Carbon::parse($inability->end_date)->formatLocalized('%d de %B de %Y') }}</td>
                        <td>{{ $inability->rejection_causal }}</td>
                    </tr>
                @endif
                @if ($inability->fractional_it == 'SI')
                    @foreach ($inability->frac as $fraction)
                        @if ($fraction->validation_result == 'RECHAZADO')
                            <tr style="text-align: center;">
                                <td>{{ \Carbon\Carbon::parse($fraction->initial_date)->formatLocalized('%d de %B de %Y') }}
                                </td>
                                <td>{{ \Carbon\Carbon::parse($fraction->end_date)->formatLocalized('%d de %B de %Y') }}
                                </td>
                                <td>{{ $fraction->cause_reject }}</td>
                            </tr>
                        @endif
                    @endforeach
                @endif
            @endforeach

            @if ($rejectedCount == 0)
                <tr style="text-align: center;">
                    <td> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;</td>
                    <td> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;</td>
                    <td> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;</td>
                </tr>
            @endif
        </table>
        <br>
        <p>
            En caso de requerir información adicional, por favor acercarse a nuestros Puntos de Atención Colpensiones
            (PAC);
            comunicarse con la línea de servicio al ciudadano en Bogotá al (601) 4890909, en Medellín al (604) 2836090,
            o
            con la línea gratuita nacional al 018000 41 0909, en donde estaremos dispuestos a brindarle el mejor
            servicio.
        </p>
    </div>
    <div style="page-break-inside:avoid;">
        <p>
            Agradecemos su confianza y le recordamos que estamos para servirle.
        </p>
        <p>Cordialmente,</p>
        <br>
        <br>
        <p>

            <b>LUZ MARYEN LOZANO ROSAS</b><br />
            Directora<br />
            <b>Dirección de Medicina Laboral</b> <br />
        </p>
        @php
            $authorAnalyst = null;
            $authorLiquidator = null;
            $authorAuditor = null;
            $actionAuditor = false;
            $analystDate = null;
            $liquidatorDate = null;
            $auditorDate = null;
            foreach ($activity->activity_actions_it as $aa) {
                if ($aa->action_id == 650 && (!$analystDate || $aa->created_at > $analystDate)) {
                    $analystDate = $aa->created_at;
                    $authorAnalyst = $determination_it->authorNameEmpresa($aa->author_id);
                }
                if ($aa->action_id == 653 && (!$auditorDate || $aa->created_at > $auditorDate)) {
                    $auditorDate = $aa->created_at;
                    $authorAuditor = $determination_it->authorNameEmpresa($aa->author_id);
                    $actionAuditor = true;
                }
            }
        @endphp
        <br>
        <p id="aprobaciones">
            Analizó:<strong>&nbsp;&nbsp;</strong>{{ $authorAnalyst ? strtoupper($authorAnalyst['author_name']) : '' }}
            {{ $authorAnalyst ? '- ' . strtoupper($authorAnalyst['company_name']) : '' }}
            <br>
            {!! $authorLiquidator ? 'Liquidó:<strong>&nbsp;&nbsp;</strong>' : '' !!}
            {{ $authorLiquidator ? strtoupper($authorLiquidator['author_name']) : '' }}
            {{ $authorLiquidator ? '- ' . strtoupper($authorLiquidator['company_name']) : '' }}
            <br>
            {!! $authorAuditor ? 'Auditó:<strong>&nbsp;&nbsp;</strong>' : '' !!}
            @if ($actionAuditor)
                {{ $authorAuditor ? strtoupper($authorAuditor['author_name']) : '' }}
                {{ $authorAuditor ? '- ' . strtoupper($authorAuditor['company_name']) : '' }}
                <br>
            @endif
        </p>
        <br>
    </div>
</body>
