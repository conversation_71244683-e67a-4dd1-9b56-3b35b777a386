<!DOCTYPE html>
<html lang="es">
<head>
    <meta content="text/html; charset=utf-8" http-equiv="Content-Type"/>
    <style type="text/css">
        * {
            font-family: 'Arial', sans-serif;
            font-size: 10pt;
        }

        body {
            margin: 0 2rem 3rem 2rem;
            padding-top: 75px;
            text-align: justify;
        }

        .watermark {
            position: fixed;
            top: 45%;
            width: 100%;
            text-align: center;
            opacity: .1;
            transform: rotate(-45deg);
            transform-origin: 50% 50%;
            z-index: 1000;
        }

        .footer {
            height: 40px;
            width: 100%;
            position: fixed;
            z-index: 0 !important;
            left: 26px;
            right: 26px;
            top: 87%;
            border-top: 0.5px solid black;
        }

        .header {
            height: 40px;
            width: 100%;
            position: fixed;
            z-index: 0 !important;
        }

        .header img {
            height: 57px;
            width: 752px;
            position: absolute;
            left: -76px;
            top: -8px;
            opacity: 0.5;
        }

        .footer img {
            min-height: 50px;
            max-height: 50px;
            width: auto;
            text-align: left;
        }

        .numpage:after {
            content: counter(page);
        }

        #decreto {
            padding: 0 30px 0 30px;
            font-style: italic;
        }

        #ordenJudicial {
            font-style: italic;
        }

        table,
        th,
        td {
            border-collapse: collapse;
            border: 0.5px solid black;
        }

        table td th {
            text-align: center;
        }

        td {
            font-size: 10px;
            overflow-x: auto;
        }

        #aprobaciones {
            font-size: 8pt;
        }
    </style>
</head>

<body>
@if($watermark)
    <div class="watermark">
        <span style="font-size: 72pt;font-weight: 500;">VISTA PREVIA</span>
    </div>
@endif
<div class="header">
    <img src="{{storage_path('app/header_mdi.jpeg')}}" alt="Logo Colpensiones">
</div>
<div class="footer">
    <table style="width: 100%">
        <tr>
            <td colspan="10" style="border: transparent;text-align: justify;opacity: 0.5;">
                <b>Colpensiones</b><br>
                Dirección: Carrera 10 No.72 – 33 Torre B Piso 11, Bogotá D.C., Colombia<br>
                Conmutador: (+57) 601 489 0909; Línea Gratuita: 01 8000 410909<br>
                www.colpensiones.gov.co
            </td>
            <script type="text/php">
                if (isset($pdf)) {
                    $x = $pdf->get_width() - 98;
                    $y= 770;
                    $text = "Página | {PAGE_NUM}";
                    $font = $fontMetrics->get_font("Helvetica", "normal");
                    $size = 9;
                    $color = array(0.430,0.430,0.430);
                    $word_space = 0.0;  //  default
                    $char_space = 0.0;  //  default
                    $angle = 0.0;   //  default
                    $pdf->page_text($x, $y, $text, $font, $size, $color, $word_space, $char_space, $angle);
                }
            </script>
        </tr>
    </table>
</div>

<div>
    <br><b>OFICIO DML - I No {{ strlen((int)$activity->it_id) < 5 ? sprintf('%05d', (int)$activity->it_id) : (int)$activity->it_id }}
        de </b>{{ $activity->determination_it ? \Carbon\Carbon::parse($activity->determination_it->resolution_quality_date)->formatLocalized('%d de %B de %Y') : '' }}</p>
    <br>
    <p>
        </b> <br/>
        </b> <br/>
        Señor(a): <br/>
        <b>{{ $activity->determination_it->full_name_affiliate }}</b> <br/>
        {{ $activity->determination_it->address  }}<br/>
        Cel: {{ $activity->determination_it->cellphone  }}<br/>
        {{ $activity->determination_it->city  }}
        <br/>
    </p>
    </b> <br/>
    <p><strong>Referencia:</strong><strong>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;</strong>Radicado
        No. {{ $activity->id_bizagi }}<br/>
        <strong>Ciudadano:&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
            &nbsp;&nbsp;</strong>{{ $activity->affiliate->full_name }}<br/>
        <strong>Identificación:</strong><strong>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
            &nbsp;</strong>{{ $activity->affiliate->doc_type }} {{ $activity->affiliate->doc_number }} <br/>
        <strong>Tipo trámite:&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;</strong>Determinación del Subsidio por Incapacidad Temporal</p>

    <br>
    <p>
        Respetado(a) señor(a): <br/><br/>
        Reciba un cordial saludo de la Administradora Colombiana de Pensiones – COLPENSIONES.
    </p>

    <p>
        En atención al trámite iniciado por usted, relacionado con el pago de las incapacidades presentadas a través del
        radicado de la referencia, resulta pertinente señalar que Colpensiones está a cargo del pago de las
        incapacidades por enfermedad general o accidente de origen común, hasta por 360 días calendario, adicionales a
        los primeros 180 días reconocidos por su Entidad Promotora de Salud (EPS), según lo establecido en el artículo
        142 del Decreto Ley 019 de 2012, del cual, nos permitimos transcribir el aparte referente al reconocimiento de
        las mencionadas prestaciones económicas:
    </p>

    <p id="decreto">
        “Para los casos de accidente o enfermedad común en los cuales exista concepto favorable de rehabilitación de la
        Entidad Promotora de Salud, la Administradora de Fondos de Pensiones postergará el trámite de calificación de
        Invalidez hasta por un término máximo de trescientos sesenta (360) días calendario adicionales a los primeros
        ciento ochenta (180) días de incapacidad temporal reconocida por la Entidad Promotora de Salud, evento en el
        cual, con cargo al seguro previsional (sic) de invalidez y sobrevivencia o de la entidad de previsión social
        correspondiente que lo hubiere expedido, la Administradora de Fondos de Pensiones otorgará un subsidio
        equivalente a la incapacidad que venía disfrutando el trabajador.
    </p>
    <p id="decreto">
        Las Entidades Promotoras de Salud deberían emitir dicho concepto antes de cumplirse el día (120) de incapacidad
        temporal y enviarlo antes de cumplirse el día (150), a cada una de las Administradoras de Fondos de Pensiones
        donde se encuentre afiliado el trabajador a quien se le expida el concepto respectivo, según corresponda. Cuando
        la Entidad Promotora de Salud no expida el concepto favorable de rehabilitación, si a ello hubiere lugar, deberá
        pagar un subsidio equivalente a la respectiva incapacidad temporal después de los ciento ochenta (180) días
        iniciales con cargo a sus propios recursos, hasta cuando se emita el correspondiente concepto.(…)”
    </p>

    <p>
        De acuerdo con lo señalado en la norma transcrita, y una vez estudiada y validada la documentación aportada, se
        reconocerá y pagará el subsidio por incapacidad correspondiente a los siguientes períodos, teniendo en cuenta
        que cumple con los requisitos de legales:
    </p>
    <br>
    <table>
        <tr style="text-align: center;">
            <th><p style="transform: rotate(-90deg);">NOMBRE</p></th>
            <th><p style="transform: rotate(-90deg);">IDENTIFICACIÓN</p></th>
            <th style="padding: 30px 0 30px 0;"><p style="transform: rotate(-90deg);">TERCERO AUTORIZADO</p></th>
            <th><p style="transform: rotate(-90deg);">DÍAS A PAGAR</p></th>
            <th><p style="padding: 10px 30px 0 0; transform: rotate(-90deg);">FECHA INICIAL</p></th>
            <th><p style="padding: 10px 30px 0 0; transform: rotate(-90deg);">FECHA FINAL</p></th>
            <th><p style="transform: rotate(-90deg);">IBC</p></th>
            <th><p style="transform: rotate(-90deg);">VALOR A PAGAR</p></th>
        </tr>
        @foreach($activity->determination_it->inabilities as $inability)
            @if($inability->fractional_it == 'NO')
                @if($inability->right_validation == 'APROBADO' && $inability->payment_type == 'POR DERECHO')
                    <tr style="text-align: center; white-space: pre-wrap;">
                        <td>{{ $activity->affiliate->first_name }}
                            <br>{{ $activity->affiliate->last_name }}</td>
                        <td>{{ $activity->affiliate->doc_number }}</td>
                        <td>{{ $activity->determination_it->account_full_name && $activity->determination_it->titular_account_type == 'TERCERO' ? mb_strtoupper($activity->determination_it->account_full_name) : '' }}</td>
                        <td>{{ $inability->it_days_to_pay }}</td>
                        <td>{{ $inability->initial_date }}</td>
                        <td>{{ $inability->end_date }}</td>
                        <td>{{ $activity->determination_it ? number_format(floatval($activity->determination_it->ibls_value), 0, ',', '.') : '' }}</td>
                        <td>{{ number_format(floatval($inability->payment_it_value), 0, ',', '.') }}</td>
                    </tr>
                @endif
            @endif
            @if($inability->fractional_it == 'SI')
                @foreach($inability->frac as $fraction)
                    @if($fraction->validation_result == 'APROBADO' && $inability->payment_type == 'POR DERECHO')
                        <tr style="text-align: center; white-space: pre-wrap;">
                            <td>{{ $activity->affiliate->first_name }}
                                <br>{{ $activity->affiliate->last_name }}</td>
                            <td>{{ $activity->affiliate->doc_number }}</td>
                            <td>{{ $activity->determination_it->account_full_name && $activity->determination_it->titular_account_type == 'TERCERO' ? mb_strtoupper($activity->determination_it->account_full_name) : '' }}</td>
                            <td>{{ $fraction->it_days_pay }}</td>
                            <td>{{ $fraction->initial_date }}</td>
                            <td>{{ $fraction->end_date }}</td>
                            <td>{{ $activity->determination_it ? number_format(floatval($activity->determination_it->ibls_value), 0, ',', '.') : '' }}</td>
                            <td>{{ number_format(floatval($fraction->value_pay), 0, ',', '.') }}</td>
                        </tr>
                    @endif
                @endforeach
            @endif
        @endforeach
    </table>
    <br>
    <p>
        Ahora bien, se evidencia en su historial de Medicina Laboral orden judicial proferida por
        el {{ $activity->determination_it->judge_name }} en la cual, al resolver la Acción de Tutela instaurada según
        radicado {{ $activity->determination_it->radication_number_tutelage_it }}
        de {{ $activity->determination_it->fail_date }} obrante en el radicado
        Bizagi {{ $activity->determination_it->bizagi_radication_number_sentence }} ordenó:
    </p>

    <p id="ordenJudicial">
        (...){{ $activity->determination_it->judicial_order }}(...)
    </p>

    <p>
        En concordancia con lo anterior, se realiza la descripción de los periodos de incapacidad a reconocer en
        cumplimiento de la orden judicial, así:
    </p>
    <br>
    <table>
        <tr style="text-align: center;">
            <th><p style="transform: rotate(-90deg);">NOMBRE</p></th>
            <th><p style="transform: rotate(-90deg);">IDENTIFICACIÓN</p></th>
            <th style="padding: 30px 0 30px 0;"><p style="transform: rotate(-90deg);">TERCERO AUTORIZADO</p></th>
            <th><p style="transform: rotate(-90deg);">DÍAS A PAGAR</p></th>
            <th><p style="padding: 10px 30px 0 0; transform: rotate(-90deg);">FECHA INICIAL</p></th>
            <th><p style="padding: 10px 30px 0 0; transform: rotate(-90deg);">FECHA FINAL</p></th>
            <th><p style="transform: rotate(-90deg);">IBC</p></th>
            <th><p style="transform: rotate(-90deg);">VALOR A PAGAR</p></th>
        </tr>
        @foreach($activity->determination_it->inabilities as $inability)
            @if($inability->fractional_it == 'NO' && $inability->payment_type == 'ORDEN JUDICIAL')
                @if($inability->right_validation == 'APROBADO')
                    <tr style="text-align: center;">
                        <td style="white-space: nowrap;">{{ $activity->affiliate->first_name }}
                            <br>{{ $activity->affiliate->last_name }}</td>
                        <td>{{ $activity->affiliate->doc_number }}</td>
                        <td>{{ $activity->determination_it->account_full_name ? $activity->determination_it->account_full_name : 'N/A' }}</td>
                        <td>{{ $inability->it_days_to_pay }}</td>
                        <td>{{ $inability->initial_date }}</td>
                        <td>{{ $inability->end_date }}</td>
                        <td>{{ $activity->determination_it ? number_format(floatval($activity->determination_it->ibls_value), 0, ',', '.') : '' }}</td>
                        <td>{{ number_format(floatval($inability->payment_it_value), 0, ',', '.') }}</td>
                    </tr>

                @endif
            @endif
            @if($inability->fractional_it == 'SI')
                @foreach($inability->frac as $fraction)
                    @if($fraction->validation_result == 'APROBADO' && $inability->payment_type == 'ORDEN JUDICIAL')
                        <tr style="text-align: center;">
                            <td style="white-space: nowrap;">{{ $activity->affiliate->first_name }}
                                <br>{{ $activity->affiliate->last_name }}</td>
                            <td>{{ $activity->affiliate->doc_number }}</td>
                            <td>{{ $activity->determination_it->account_full_name && $activity->determination_it->titular_account_type == 'TERCERO' ? mb_strtoupper($activity->determination_it->account_full_name) : '' }}</td>
                            <td>{{ $fraction->it_days_pay }}</td>
                            <td>{{ $fraction->initial_date }}</td>
                            <td>{{ $fraction->end_date }}</td>
                            <td>{{ $activity->determination_it ? number_format(floatval($activity->determination_it->ibls_value), 0, ',', '.') : '' }}</td>
                            <td>{{ number_format(floatval($fraction->value_pay), 0, ',', '.') }}</td>
                        </tr>
                    @endif
                @endforeach
            @endif
        @endforeach
    </table>
    <br>
    <p>
        La liquidación del subsidio por incapacidad se realiza conforme lo establecido en el Código Sustantivo de
        Trabajo artículos 227 y 228, en concordancia con lo dispuesto en los artículos 2.2.3.3.1 y 2.2.3.4.5 del Decreto
        1427 de 2022. Para salarios fijos se realiza aplicando la fórmula “S = IBC cotizado en el mes anterior a la
        fecha de inicio de la incapacidad * 50%”, entendiendo por inicio, el reportado en el día uno (1) de la
        incapacidad inicial, no el de las prórrogas y para salarios variables, se realiza aplicando la formula “S = IBC
        (promedio del salario devengado por el trabajador en el último año de servicio anterior a la fecha de inicio de
        la incapacidad, o en todo el tiempo de servicios si no alcanza a completar un (1) año) * 50%”, sin afectar el
        salario mínimo legal mensual vigente.
    </p>
    <p>
        En este orden, el total a pagar por concepto de subsidio por incapacidad temporal, corresponde a la suma de
        <b>{{ $activity->determination_it->textNumber($activity->determination_it->payment_value) }} PESOS M/CTE
            (${{ number_format($activity->determination_it->payment_value, 0, ',', '.') }})</b>, los cuales, se pagarán
        y consignarán a través de la cuenta de {{ $activity->determination_it->account_type }}
        número {{ $activity->determination_it->account_number }} del banco {{ $bankName }}, cuyo
        titular es el señor(a) {{ $activity->determination_it->account_full_name }}. El pago ordenado se imputará con
        cargo al Certificado de
        Disponibilidad Presupuestal No. ********** del 03 de enero de 2024.
    </p>
    <p>
        El anterior pago se efectúa de conformidad con lo señalado en el Decreto 309 de 2017 que en el numeral 16 del
        artículo 10, establece como función del presidente de la Administradora Colombiana de Pensiones – COLPENSIONES
        <font style="font-style: italic;">“Dirigir la ejecución presupuestal, comprometer y ordenar el gasto, suscribir
            los actos, y celebrar los contratos y convenios que se requieran para el normal funcionamiento de
            COLPENSIONES”.</font>
    </p>
    <p>
        Asimismo, se informa que el presidente de Colpensiones a través de la Resolución 137 de 2017 en su artículo 4º
        numeral 3º delegó en el director de Medicina Laboral la función de ejecutar el presupuesto, comprometer y
        ordenar el gasto de la Administradora y de los Fondos de Reservas Pensionales que administra la empresa, por
        concepto de Pago de Incapacidades superiores a 180 días.
    </p>
    <p>
        En caso de requerir información adicional, por favor acercarse a nuestros Puntos de Atención Colpensiones (PAC);
        comunicarse con la línea de servicio al ciudadano en Bogotá al (601) 4890909, en Medellín al (604) 2836090, o
        con la línea gratuita nacional al 018000 41 0909, en donde estaremos dispuestos a brindarle el mejor servicio.
    </p>
</div>
<div style="page-break-inside:avoid;">
    <p>
        Agradecemos su confianza y le recordamos que estamos para servirle.
    </p>
    <p>Cordialmente,</p>
    <br>
    <br>
    <p>
        @unless($watermark)
            <img style="height: 50px; width: auto;" alt="FIRMA" src="{{storage_path('app/firma_lmlozano.jpg')}}"/> <br/>
        @endunless
        <b>LUZ MARYEN LOZANO ROSAS</b><br/>
        Directora de Medicina Laboral <br/>
    </p>
    @php
        $authorAnalyst = null;
        $authorLiquidator = null;
        $authorAuditor = null;
        $actionAuditor = false;
        $analystDate = null;
        $liquidatorDate = null;
        $auditorDate = null;
        foreach ($activity->activity_actions_it as $aa) {
            if ($aa->action_id == 362 && (!$analystDate || $aa->created_at > $analystDate)) {
                $analystDate = $aa->created_at;
                $authorAnalyst = $determination_it->authorNameEmpresa($aa->author_id);
            }
            if ($aa->action_id == 366 && (!$liquidatorDate || $aa->created_at > $liquidatorDate)) {
                $liquidatorDate = $aa->created_at;
                $authorLiquidator = $determination_it->authorNameEmpresa($aa->author_id);
            }
            if ($aa->action_id == 379 && (!$auditorDate || $aa->created_at > $auditorDate)) {
                $auditorDate = $aa->created_at;
                $authorAuditor = $determination_it->authorNameEmpresa($aa->author_id);
                $actionAuditor = true;
            }
        }
    @endphp
    <br>
    <p id="aprobaciones">
        Analizó:<strong>&nbsp;&nbsp;</strong>{{ $authorAnalyst ? strtoupper($authorAnalyst['author_name']) : '' }}
        {{ $authorAnalyst ? '- ' . strtoupper($authorAnalyst['company_name']) : '' }}
        <br>
        {!! $authorLiquidator ? 'Liquidó:<strong>&nbsp;&nbsp;</strong>' : '' !!}
        {{ $authorLiquidator ? strtoupper($authorLiquidator['author_name']) : '' }}
        {{ $authorLiquidator ? '- ' . strtoupper($authorLiquidator['company_name']) : '' }}
        <br>
        {!! $authorAuditor ? 'Auditó:<strong>&nbsp;&nbsp;</strong>' : '' !!}
        @if ($actionAuditor)
            {{ $authorAuditor ? strtoupper($authorAuditor['author_name']) : '' }}
            {{ $authorAuditor ? '- ' . strtoupper($authorAuditor['company_name']) : '' }}
            <br>
        @endif
    </p>
</div>
</body>
