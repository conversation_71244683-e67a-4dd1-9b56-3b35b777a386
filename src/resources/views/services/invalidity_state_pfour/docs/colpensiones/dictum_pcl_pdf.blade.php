<!DOCTYPE html>
<html lang="es">

<head>
    <meta content="text/html; charset=utf-8" http-equiv="Content-Type"/>
    <style type="text/css">
        @font-face {
            font-family: 'Century' !important;
            src: url({{ storage_path('fonts/CenturyGothic.ttf') }}) format("truetype") !important;
            font-weight: 400 !important;
            font-style: normal !important;
        }

        @font-face {
            font-family: 'Century-Bold' !important;
            src: url({{ storage_path('fonts/CenturyGothicBoldA1.ttf') }}) format("truetype") !important;
            font-weight: 700 !important;
            font-style: normal !important;
        }

        * {
            font-family: 'Century', sans-serif;
            font-size: 7pt;
        }

        b {
            font-family: 'Century-Bold', sans-serif !important;
        }

        body {
            padding-top: 10px;
        }

        .watermark {
            position: fixed;
            top: 45%;
            width: 100%;
            text-align: center;
            opacity: .1;
            transform: rotate(-45deg);
            transform-origin: 50% 50%;
            z-index: 1000;
        }

        .border-left {
            border: 0;
            border-left: 1px solid black;
        }

        table,
        th,
        td {
            border: 1px solid black;
        }

        th,
        td {
            padding: 1.5px;
            padding-bottom: 3px;
        }

        td[colspan="16"] {
            text-align: justify;
        }

        table {
            border-collapse: collapse;
            width: 100%;
        }

        th {
            text-align: center;
            background: rgb(185, 185, 185);
            color: black;
            font-family: 'Century-Bold', sans-serif !important;
        }

        td.sub {
            text-align: center;
            background: #eee;
        }

        tr.sub {
            background: #eee;
        }

        .header .line {
            background: lightgray;
            color: black;
        }

        .header_th {
            background: transparent !important;
            border: transparent;
        }

        .header_th img {
            height: 50px;
            width: auto;
            padding-bottom: 7px;
        }

        td.grey {
            background: lightgray;
        }

        .footer {
            height: 40px;
            width: 100%;
            position: fixed;
            z-index: 0 !important;
            top: 87%;
            border-top: 0.5px solid black;
            justify-content: center;
            text-align: center;
        }

        .footer > table {
            width: 100%;
            text-align: center;
        }

        .signature-table {
            max-width: 100%;
            border-collapse: collapse;
        }

        .signature-table td {
            border: 1px solid black;
            vertical-align: middle;
            text-align: center;
        }

        .signature-cell {
            padding-top : 10px;
            width: 50%; /* This ensures each td takes up 50% of the width of its container */
        }

        .signature-image {
            height: auto;
            max-height: 50px;
            width: auto;
            max-width: 275px!important;
            object-fit: contain; /* Ensures the aspect ratio is maintained */
        }
    </style>
</head>
@php
    $date_action = null;
    $withWatermark = false;
    $withSign = false;
    $exists544 = false;
    $exists513 = false;
    $exists521 = false;
    foreach($activity->activity_actions as $aa){
        if ($aa->action_id == 513 || $aa->action_id == 434 || $aa->action_id == 521) {
        // Comprobar si la acción actual es más reciente que la guardada anteriormente
            if ($date_action === null || $aa->created_at > $date_action) {
                $date_action = $aa->created_at;
            }
        }
        if ($aa->action_id == 544) {
            $exists544 = true;
        }

        if ($aa->action_id == 513) {
            $exists513 = true;
        }
        if ($aa->action_id == 521) {
            $exists521 = true;
        }
    }

    if ($preview){
        $withWatermark = true;
        $withSign = false;
    }
    if ($exists521) {
        $withWatermark = false;
        $withSign = true;
    } elseif ($exists544 && $exists513 == false) {
        $withWatermark = true;
        $withSign = true;
        $preview = false;
    } elseif (($exists544 && $exists513) || $exists521) {
        $withWatermark = false;
        $withSign = true;
    }

    $employment = null;
    if (count($activity->affiliate->employments) > 0) {
        $employment = $activity->affiliate->employments[0];
    }
@endphp
@php
    $age = null;
    $birthDay = null;
    if($activity->invalidity_state_pfour && $activity->invalidity_state_pfour->beneficiaries_qualification == 'SI') {
        $age = $activity->invalidity_state_pfour->beneficiaries_age;
        $birthDay = Carbon\Carbon::createFromFormat('Y-m-d', $activity->invalidity_state_pfour->beneficiaries_birthdate)->formatLocalized('%d/%B/%Y');
    } else {
        $age = $activity->invalidity_state_pfour->ageAffiliate();
   $birthDay = Carbon\Carbon::createFromFormat('Y-m-d', $activity->invalidity_state_pfour->birthday)->formatLocalized('%d/%B/%Y');
    }
    $deficienceCount = 1;

    // CONCAT TEXT INTERCONSULTATIONS 1507
   $textInterconsultation = '';
   $textInterconsultationGstate = '';
   $textInterconsultationVirtual = '';

   $anamDis = "ANAMNESIS - ENFERMEDAD ACTUAL" . "\n" . "Descripción:" .' '. $activity->invalidity_state_pfour->anamnesis_actual_disease . "\n";

   $patolo = "ANTECEDENTES PERSONALES" . "\n" . "Patológicos:" .' '. $activity->invalidity_state_pfour->anamnesis_patological;
   $surgic = "Quirúrgicos:" .' '. $activity->invalidity_state_pfour->anamnesis_surgical;
   $pharma = "Farmacológicos:" .' '. $activity->invalidity_state_pfour->anamnesis_pharmaceutical;
   $hospit = "Hospitalarios:" .' '. $activity->invalidity_state_pfour->anamnesis_hospitalary;
   $toxic = "Toxicos-alergicos:" .' '. $activity->invalidity_state_pfour->anamnesis_toxic;
   $transf = "Trasfusiones:" .' '. $activity->invalidity_state_pfour->anamnesis_transfusion;
   $family = "Familiares:" .' '. $activity->invalidity_state_pfour->anamnesis_family . "\n";

   $gState = "EXAMEN FISICO" . "\n" . "Estado general:" .' '. $activity->invalidity_state_pfour->general_state . "\n";
   $lat = "Lateralidad:" .' '. $activity->invalidity_state_pfour->physic_exam_laterality;
   $hei = "Talla:" .' '. $activity->invalidity_state_pfour->height;
   $wei = "Peso:" .' '. $activity->invalidity_state_pfour->weight;
   $imc = "IMC:" .' '. $activity->invalidity_state_pfour->imc;
   $ta = "Tensión Arterial:" .' '. $activity->invalidity_state_pfour->ta;
   $ta2 = "Tensión Arterial 2:" .' '. $activity->invalidity_state_pfour->ta2;
   $fc = "Frecuencia Cardíaca:" .' '. $activity->invalidity_state_pfour->fc;
   $fr = "Frecuencia Respiratoria:" .' '. $activity->invalidity_state_pfour->fr;
   $temp = "Temperatura:" .' '. $activity->invalidity_state_pfour->temperature;
   $head = "Cabeza y cuellos:" .' '. $activity->invalidity_state_pfour->head_and_neck;
   $card = "Cardio pulmonar:" .' '. $activity->invalidity_state_pfour->pulmonaryal_cardio;
   $abdo = "Abdomen:".' '. $activity->invalidity_state_pfour->abdomen;
   $exExt = "Extremidades:" .' '. $activity->invalidity_state_pfour->phisical_exam_extremities;
   $exNeu = "Neurológico:" .' '. $activity->invalidity_state_pfour->phisical_exam_neurological;
   $exMent = "Mental:" .' ' . $activity->invalidity_state_pfour->phisical_exam_mental;
   $exObs = "Observación:" .' '. $activity->invalidity_state_pfour->phisical_exam_observation;
   $reqHisCli = "Requiere historia clínica adicional y/o examenes complementarios:" .' '. $activity->invalidity_state_pfour->req_hc_add_or_complement_exams . "\n";

   $apRoleOcup = "ROL OCUPACIONAL ADULTOS MAYORES / NIÑOS Y NIÑAS MAYORES DE 3 AÑOS / ADOLESCENTES" . "\n" . "Rol ocupacional adultos mayores / niños y niñas mayores de 3 años / adolescentes:"  .' '. $activity->invalidity_state_pfour->ocucupational_role_adults_description . "\n";

   $antLab = "ANÁLISIS MÉDICO LABORAL DEL CASO" . "\n" . "Antecedente laboral:" .' '. $activity->invalidity_state_pfour->laboral_antecedent . "\n";

   $labRol = "\n" . "ROL LABORAL" . "\n" . "Actualmente esta laboralmente activo?:" .' '. $activity->invalidity_state_pfour->lab_rol_laborally_active;
   $habWork = "Trabajo habitual:" .' '. $activity->invalidity_state_pfour->lab_rol_habitual_work;
   $rolCharge = "Cargo:" .' '. $activity->invalidity_state_pfour->lab_rol_charge;
   $offAct = "Oficio (actividades que desempeña):" .' '. $activity->invalidity_state_pfour->lab_rol_office_activity;
   $labRolCT = "Tiempo en el cargo:" .' '. $activity->invalidity_state_pfour->lab_rol_charge_time;
   $rolEmpl = "Empresa:" .' '. $activity->invalidity_state_pfour->lab_rol_employer;
   $rolRes = "Restricciones que presenta actualmente para desempeñar su rol laboral:" .' '. $activity->invalidity_state_pfour->lab_rol_restrictions;
   $rolOp = "Tareas y operaciones que desempeña en este momento:" .' '. $activity->invalidity_state_pfour->lab_rol_operations;
   $helpMo = "Dispositivo de ayuda en el momento:" .' '. $activity->invalidity_state_pfour->help_device_at_the_moment;
   $helpMoD = "Descripción:" .' '. $activity->invalidity_state_pfour->help_device_at_the_moment_description;
   $arlRes = "Cuenta con restricciones o recomendaciones laborales emitidas por medicina laboral o médico tratante de EPS o ARL:" .' '. $activity->invalidity_state_pfour->lab_rol_restrictions_eps_arl;
   $tPartyH = "Requiere ayuda de terceras personales para realizar sus actividades laborales:" .' '. $activity->invalidity_state_pfour->third_party_help;
   $prevPcl = "Tiene dictamenes previos de calificacion de origen y/o PCL:" .' '. $activity->invalidity_state_pfour->previous_dictum_origin_pcl;
   $prevPclD = "Descripción del Rol Laboral:" .' '. $activity->invalidity_state_pfour->labor_role_description . "\n";

   $appKnow = "OTRAS AREAS OCUPACIONALES" . "\n" . "Aprendizaje y aplicación del conocimiento:" .' '. $activity->invalidity_state_pfour->valoration_learning_application_knowledge;
   $valComm = "Comunicación:" .' '. $activity->invalidity_state_pfour->valoration_communication;
   $valMob = "Movilidad:" .' '. $activity->invalidity_state_pfour->valoration_mobility;
   $valCare = "Cuidado Personal:" .' '. $activity->invalidity_state_pfour->valoration_personal_care;
   $valLife = "Vida doméstica:" .' '. $activity->invalidity_state_pfour->valoration_domestic_life;

   // Switchs
   $labAct = $activity->invalidity_state_pfour && $activity->invalidity_state_pfour->valoration_laborally_active == 1 ? "Laboralmente activo: SI" : "Laboralmente activo: NO";
   $useLabRol = $activity->invalidity_state_pfour && $activity->invalidity_state_pfour->valoration_use_laboral_role == 1 ? "Aplica rol ocupacional: SI" : "Aplica rol ocupacional: NO";

   // Validate Switchs
   if ($activity->invalidity_state_pfour && $activity->invalidity_state_pfour->valoration_laborally_active == 1) {
       $textInterconsultation = $anamDis . "\n" . $patolo . "\n" . $surgic . "\n" . $pharma . "\n" . $hospit . "\n" . $toxic . "\n" . $transf . "\n" . $family . "\n"  . $gState . "\n" . $lat . "\n" . $hei . "\n" . $wei . "\n" . $imc . "\n" . $ta . "\n" . $ta2 . "\n" . $fc . "\n" . $fr . "\n" . $temp . "\n" . $head . "\n" . $card . "\n" . $abdo . "\n" . $exExt . "\n" . $exNeu . "\n" . $exMent . "\n" . $exObs . "\n" . $antLab . "\n" . $labAct . "\n" . $labRol. "\n" . $habWork . "\n" . $rolCharge . "\n" . $offAct . "\n" . $labRolCT . "\n" . $rolEmpl . "\n" . $rolRes . "\n" . $rolOp . "\n" . $helpMo . "\n" . $helpMoD . "\n" . $arlRes . "\n" . $tPartyH . "\n" . $prevPcl . "\n" . $prevPclD  . "\n" .$appKnow . "\n" . $valComm . "\n" . $valMob . "\n" . $valCare . "\n" . $valLife;
       $textInterconsultationGstate = $gState . "\n" . $lat . "\n" . $hei . "\n" . $wei . "\n" . $imc . "\n" . $ta . "\n" . $ta2 . "\n" . $fc . "\n" . $fr . "\n" . $temp . "\n" . $head . "\n" . $card . "\n" . $abdo . "\n" . $exExt . "\n" . $exNeu . "\n" . $exMent . "\n" . $exObs;
       $textInterconsultationVirtual = $anamDis . "\n" . $patolo . "\n" . $surgic . "\n" . $pharma . "\n" . $hospit . "\n" . $toxic . "\n" . $transf . "\n" . $family . "\n" . "ROL LABORAL/OCUPACIONAL" . "\n" . $labAct . "\n" . $labRol . "\n" . $habWork . "\n" . $rolCharge . "\n" . $offAct . "\n" . $labRolCT . "\n" . $rolEmpl . "\n" . $rolRes . "\n" . $rolOp . "\n" . $helpMo . "\n" . $helpMoD . "\n" . $arlRes . "\n" . $tPartyH . "\n" . $prevPcl . "\n" . $prevPclD . "\n" . $appKnow . "\n" . $valComm . "\n" . $valMob . "\n" . $valCare . "\n" . $valLife;
   }

   if ($activity->invalidity_state_pfour && $activity->invalidity_state_pfour->valoration_use_laboral_role == 1) {
       $textInterconsultation = $anamDis . "\n" . $patolo . "\n" . $surgic . "\n" . $pharma . "\n" . $hospit . "\n" . $toxic . "\n" . $transf . "\n" . $family . "\n"  . $gState . "\n" . $lat . "\n" . $hei . "\n" . $wei . "\n" . $imc . "\n" . $ta . "\n" . $ta2 . "\n" . $fc . "\n" . $fr . "\n" . $temp . "\n" . $head . "\n" . $card . "\n" . $abdo . "\n" . $exExt . "\n" . $exNeu . "\n" . $exMent . "\n" . $exObs . "\n" . $antLab . "\n" . "ROL LABORAL/OCUPACIONAL" . "\n" . $useLabRol . "\n" . $apRoleOcup;
       $textInterconsultationGstate = $anamDis . "\n" . $patolo . "\n" . $surgic . "\n" . $pharma . "\n" . $hospit . "\n" . $toxic . "\n" . $transf . "\n" . $family . "\n"  . $gState . "\n" . $lat . "\n" . $hei . "\n" . $wei . "\n" . $imc . "\n" . $ta . "\n" . $ta2 . "\n" . $fc . "\n" . $fr . "\n" . $temp . "\n" . $head . "\n" . $card . "\n" . $abdo . "\n" . $exExt . "\n" . $exNeu . "\n" . $exMent . "\n" . $exObs . "\n" . $antLab . "\n" . "ROL LABORAL/OCUPACIONAL" . "\n" . $useLabRol . "\n" . $apRoleOcup;
       $textInterconsultationVirtual = $anamDis . "\n" . $patolo . "\n" . $surgic . "\n" . $pharma . "\n" . $hospit . "\n" . $toxic . "\n" . $transf . "\n" . $family . "\n" . "ROL LABORAL/OCUPACIONAL" . "\n" . $useLabRol . "\n" . $apRoleOcup;
   }
@endphp
@php
    $beneficiarieFooter = '';
    if ($activity->invalidity_state_pfour && $activity->invalidity_state_pfour->beneficiaries_qualification == 'SI'){
        $beneficiarieFooter = $activity->invalidity_state_pfour->beneficiaries_full_name. ' ' .$activity->invalidity_state_pfour->beneficiaries_doc_type. ' - ' .$activity->invalidity_state_pfour->beneficiaries_doc_number;
    } else {
        $beneficiarieFooter = $activity->invalidity_state_pfour->full_name_affiliate. ' ' .$activity->affiliate->doc_type. ' - ' .$activity->affiliate->doc_number;
    }
@endphp

@php
    $ageText = '';
    if ($age >= 0 && $age <= 3) {
        $ageText = 'Niños y niñas de o a 3 años';
    } elseif ($age > 3 && $age <= 17) {
        $ageText = 'Niños y niñas de 3 años y adolescentes';
    } elseif ($age >= 18) {
        $ageText = 'Adultos y adultos mayores';
    }
@endphp

@php
    $pcl_age_restriction = 0;
    if (number_format($activity->invalidity_state_pfour->deficiences_total) > 0) {
        $pcl_age_restriction = $activity->affiliate->pcl_age_restriction();
    }
@endphp

<body>
@php
    $split_date_time = preg_split('/\s+/', $date_action);
    $date = $split_date_time[0];
    $time = isset($split_date_time[1]) ? $split_date_time[1] : null;
@endphp

@if($withWatermark)
    <div class="watermark">
        <span style="font-size: 72pt;font-weight: 500;">BORRADOR</span>
    </div>
@endif

@php
    $beneficiarieFooter = '';
    $beneficiarieDocTypeFooter = '';
    $beneficiarieDocNumberFooter = '';

    $beneficiarieDocTypeFooter = $activity->invalidity_state_pfour->beneficiaries_qualification == 'SI' ? $activity->invalidity_state_pfour->beneficiaries_doc_type : $activity->invalidity_state_pfour->doc_type_affiliate;

    switch ($beneficiarieDocTypeFooter) {
        case "CC":
            $beneficiarieDocTypeFooter = "Cédula de Ciudadanía";
            break;
        case "NU":
            $beneficiarieDocTypeFooter = "Número Único de Identificación";
            break;
        case "NI":
            $beneficiarieDocTypeFooter = "NIT";
            break;
        case "TI":
            $beneficiarieDocTypeFooter = "Tarjeta de Identidad";
            break;
        case "CE":
            $beneficiarieDocTypeFooter = "Cédula de Extranjería";
            break;
        case "PA":
            $beneficiarieDocTypeFooter = "Pasarporte";
            break;
        case "RC":
            $beneficiarieDocTypeFooter = "Regristro Civil";
            break;
        case "CD":
            $beneficiarieDocTypeFooter = "Carné Diplomático";
            break;
        case "AS":
            $beneficiarieDocTypeFooter = "Adulto sin Identificación";
            break;
        case "MS":
            $beneficiarieDocTypeFooter = "Menor sin Identificación";
            break;
        case "F":
            $beneficiarieDocTypeFooter = "Documento Extranjero";
            break;
        case "SS":
            $beneficiarieDocTypeFooter = "Adulto sin Identificación";
            break;
        case "PE":
            $beneficiarieDocTypeFooter = "Permiso Especial de Permanencia (PEP)";
            break;
        case "PT":
            $beneficiarieDocTypeFooter = "Permiso por Protección Temporal (PPT)";
            break;
        case "SE":
            $beneficiarieDocTypeFooter = "Sociedad Extranjera sin NIT en Colombia";
            break;
        default:
            $beneficiarieDocTypeFooter = "Unknown document type";
    }

    $dictamenDateFooter = $date_action ? $date_action->formatLocalized('%d/%B/%Y') : $activity->created_at->formatLocalized('%d/%B/%Y');
    $dictamenNumberFooter = $activity->pcl_id ? : '';
    if ($activity->invalidity_state_pfour && $activity->invalidity_state_pfour->beneficiaries_qualification == 'SI'){
        $beneficiarieFooter = $activity->invalidity_state_pfour->beneficiaries_full_name;
        $beneficiarieDocTypeFooter;
        $beneficiarieDocNumberFooter = $activity->invalidity_state_pfour->beneficiaries_doc_number;
    } else {
        $beneficiarieFooter = $activity->invalidity_state_pfour->full_name_affiliate;
        $beneficiarieDocTypeFooter;
        $beneficiarieDocNumberFooter = $activity->invalidity_state_pfour->doc_number_affiliate;
    }
    if (isset($pdf)) {
        $pdf->get_dompdf()->getOptions()->setChroot([$beneficiarieFooter]);
    }
@endphp

@php
    $split_date_time = preg_split('/\s+/', $date_action);
    $date = $split_date_time[0];
    $time = isset($split_date_time[1]) ? $split_date_time[1] : null;
@endphp

<div class="footer" style="margin-top: 60px;">
    <table>
        <tr>
            <td colspan="16"
                style="font-size:8px; border: transparent;text-align: justify;opacity: 1; text-align: center">
                <b>Nombre(s) y Apellido(s): </b>{{$beneficiarieFooter}}
                <b>Tipo de documento: </b>{{$beneficiarieDocTypeFooter}}
                <b>Número de documento: </b>{{$beneficiarieDocNumberFooter}}<br>
                <b>Fecha dictamen: </b>{{$dictamenDateFooter}}
                <b>Número dictamen DML: </b>{{$dictamenNumberFooter}}
            </td>
        </tr>
        <script type="text/php">
            if (isset($pdf)) {
                $x = $pdf->get_width() - 98;
                $y = $pdf->get_height() - 30;
                $text = "";
                $font = $fontMetrics->get_font("Helvetica", "normal");
                $size = 8;
                $color = array(0.430,0.430,0.430);
                $word_space = 0.0;  //  default
                $char_space = 0.0;  //  default
                $angle = 0.0;   //  default
                $pdf->page_text($x, $y, $text, $font, $size, $color, $word_space, $char_space, $angle);
            }
        </script>
    </table>
</div>

<table id="main">
    <thead>
    <tr>
        <th colspan="4" class="header_th" style="text-align: left"><img
                    src="{{storage_path('app/client_logo/relleno_header.jpg')}}" alt="relleno"></th>
        <th colspan="10" class="header_th" style="text-align: center">
            FORMULARIO DE CALIFICACION DE LA PERDIDA DE LA CAPACIDAD LABORAL Y OCUPACIONAL <br>
            {{ $activity->invalidity_state_pfour->laborally_active ? '(persona en edad económicamente activa)' : '('.$ageText.')' }}
            <br>
            DECRETO 1507 AGOSTO 12 de 2014 RESOLUCION 3745 de 2015
        </th>
        <th colspan="2" class="header_th" style="text-align: right;"><img
                    src="{{storage_path('app/header_mdi_2.jpeg')}}" style="width: 100px; height: 27px"
                    alt="Logo Colpensiones">|
            <br>
        </th>
        <script type="text/php">
            if (isset($pdf)) {
                $x = $pdf->get_width() - 90;
                $y = 80;
                $text = "Pág {PAGE_NUM} de {PAGE_COUNT}";
                $font = 'Arial';
                $size = 8;
                $color = array(0,0,0);
                $word_space = 0.0;  //  default
                $char_space = 0.0;  //  default
                $angle = 0.0;   //  default
                $pdf->page_text($x, $y, $text, $font, $size, $color, $word_space, $char_space, $angle);
            }
        </script>
    </tr>
    </thead>
    <tbody>
    <tr>
        <th colspan="16">INFORMACIÓN GENERAL DEL DICTAMEN PERICIAL</th>
    </tr>
    <tr>
        <td colspan="4"><b>Dictamen No: </b>{{$activity->pcl_id ?: ''}}</td>
        <td colspan="4"><b>Fecha de
                solicitud: </b> <br> {{$activity->created_at->formatLocalized('%d/%B/%Y') ? : ''}}
        </td>
        <td colspan="4"><b>Fecha dictamen: </b>
            <br>{{ $date ? \Carbon\Carbon::createFromFormat('Y-m-d', $date)->formatLocalized('%d/%B/%Y') : '' }}
        </td>
        <td colspan="4"><b>Ciudad: </b>BOGOTÁ</td>
    </tr>
    <tr>
        <td colspan="16"><b>Motivo de solicitud: </b>{{$activity->service->name}}</td>
    </tr>
    <tr>
        <td colspan="8"><b>Solicitante: </b>{{$activity->invalidity_state_pfour->full_name_affiliate}}</td>
        <td colspan="8">
            <b>AFP: </b>{{$activity->invalidity_state_pfour && $activity->invalidity_state_pfour->beneficiaries_qualification == 'SI' ? '' : $activity->affiliate->afp ? $AFP_LIST[$activity->affiliate->afp] : 'COLPENSIONES'}}
        </td>
    </tr>
    <tr>
        <td colspan="6"><b>RAMA JUDICIAL: </b></td>
        <td colspan="5"><b>OTRO: </b></td>
        <td colspan="5"><b>EMPLEADOR: </b></td>
    </tr>
    <tr>
        <td colspan="6">
            <b>Afiliado: {{$activity->invalidity_state_pfour && $activity->invalidity_state_pfour->beneficiaries_qualification == 'SI' ? 'SI' : 'NO'}} </b>
        </td>
        <td colspan="5">
            <b>EPS: </b>{{$activity->invalidity_state_pfour && $activity->invalidity_state_pfour->eps  ? $EPS_LIST[$activity->affiliate->eps] : ''}}
        </td>
        <td colspan="5">
            <b>ARL: </b>{{$activity->invalidity_state_pfour && $activity->invalidity_state_pfour->beneficiaries_qualification == 'SI' ? '' : $activity->affiliate->arl ? $ARL_LIST[$activity->affiliate->arl] : $ARL_LIST[$activity->affiliate->arl]}}
        </td>
    </tr>
    <tr>
        <td colspan="8"><b>Pensionado: </b></td>
        <td colspan="8">
            <b>NIT/Documento: </b>{{($activity->affiliate->doc_type. ' ' .$activity->affiliate->doc_number) }}
        </td>
    </tr>
    <tr>
        <td colspan="16"><b>Dirección del
                Solicitante: </b>{{$activity->affiliate->address}}
        </td>
    </tr>
    <tr>
        <td colspan="4">
            <b>Teléfono: </b>{{$activity->affiliate->phone}}
        </td>
        <td colspan="2">
            <b>Cel: </b>{{$activity->affiliate->cellphone}}
        </td>
        <td colspan="5">
            <b>Email: </b>{{$activity->invalidity_state_pfour->email}}
        </td>
        <td colspan="5">
            <b>Ciudad: </b>{{$activity->invalidity_state_pfour->city}}
        </td>
    </tr>
    <tr>
        <th colspan="16">2.INFORMACION DE LA ENTIDAD CALIFICADORA</th>
    </tr>
    <tr>
        <td colspan="4">
            <b>Nombre: </b>{{$activity->invalidity_state_pfour && $activity->invalidity_state_pfour->sender ? $activity->invalidity_state_pfour->sender : 'COLPENSIONES'}}</td>
        <td colspan="4">
            <b>NIT: </b>{{$activity->invalidity_state_pfour && $activity->invalidity_state_pfour->nit_sender ? $activity->invalidity_state_pfour->nit_sender : '900336004-7'}}
        </td>
        <td colspan="8">
            <b>Dirección: </b>{{$activity->invalidity_state_pfour && $activity->invalidity_state_pfour->address_sender ? $activity->invalidity_state_pfour->address_sender : 'CARRERA 7 NO. 74 -21 EDIFICIO AURORA'}}
        </td>
    </tr>
    <tr>
        <th colspan="16">3. DATOS GENERALES DE LA PERSONA CALIFICADA</th>
    </tr>
    <tr>
        <td colspan="8">
            <b>Afiliado: </b>{{$activity->invalidity_state_pfour && $activity->invalidity_state_pfour->beneficiaries_qualification == 'NO' ? 'SI' : 'NO'}}
        </td>
        <td colspan="8">
            <b>Beneficiario: </b>{{$activity->invalidity_state_pfour && $activity->invalidity_state_pfour->beneficiaries_qualification == 'SI' ? 'SI' : 'NO'}}
        </td>
    </tr>
    <tr>
        <td colspan="16"><b>Nombres y
                Apellidos: </b>{{$activity->invalidity_state_pfour && $activity->invalidity_state_pfour->beneficiaries_qualification == 'SI' ? $activity->invalidity_state_pfour->beneficiaries_full_name : $activity->invalidity_state_pfour->full_name_affiliate}}
        </td>
    </tr>
    <tr>
        <td colspan="8"><b>Tipo de
                documento: </b>{{$activity->invalidity_state_pfour && $activity->invalidity_state_pfour->beneficiaries_qualification == 'SI' ? $activity->invalidity_state_pfour->beneficiaries_doc_type : $activity->invalidity_state_pfour->doc_type_affiliate}}
        </td>
        <td colspan="8"><b>Documento de
                identificación: </b>{{$activity->invalidity_state_pfour && $activity->invalidity_state_pfour->beneficiaries_qualification == 'SI' ? $activity->invalidity_state_pfour->beneficiaries_doc_number : $activity->invalidity_state_pfour->doc_number_affiliate}}
        </td>
    </tr>
    <tr>
        <td colspan="8"><b>Fecha
                nacimiento:</b> {{ $birthDay }}
        </td>
        <td colspan="8">
            <b>Edad: </b> {{$age}} años
        </td>
    </tr>
    <tr>
        <td colspan="16">
            <b>Género: </b>{{$activity->invalidity_state_pfour && $activity->invalidity_state_pfour->beneficiaries_qualification == 'SI' ?  $GENDERS[$activity->invalidity_state_pfour->beneficiaries_gender] : $GENDERS[$activity->affiliate->gender]}}
        </td>
    </tr>
    <tr>
        <td colspan="16"><b>ETAPAS DEL CICLO VITAL: </b>{{$activity->invalidity_state_pfour && $activity->invalidity_state_pfour->valoration_laborally_active ?
            'Rol Laboral' : 'Rol Ocupacional'}}</td>
    </tr>
    <tr>
        <td colspan="8"><b>NIVEL DE
                ESCOLARIDAD: </b> @if($activity->invalidity_state_pfour && $activity->invalidity_state_pfour->beneficiaries_qualification == 'SI')
                {{ $activity->invalidity_state_pfour->beneficiaries_school_level ? $SCHOOL_LEVELS[$activity->invalidity_state_pfour->beneficiaries_school_level] : ''}}
            @else
                {{ $activity->invalidity_state_pfour->school_level ? $SCHOOL_LEVELS[$activity->invalidity_state_pfour->school_level] : ''}}
            @endif
        </td>
        <td colspan="8"><b>Otros(Cuál): </b></td>
    </tr>
    <tr>
        <td colspan="8"><b>ESTADO
                CIVIL: </b>@if($activity->invalidity_state_pfour && $activity->invalidity_state_pfour->beneficiaries_qualification == 'SI')
                {{ $activity->invalidity_state_pfour->beneficiaries_civil_status ? $CIVIL_STATUS[$activity->invalidity_state_pfour->beneficiaries_civil_status] : ''}}
            @else
                {{ $activity->invalidity_state_pfour->civil_status ? $CIVIL_STATUS[$activity->invalidity_state_pfour->civil_status] : ''}}
            @endif
        </td>
        <td colspan="8"><b>Otros(Cuál): </b></td>
    </tr>
    <tr>
        <th colspan="16">4 ANTECEDENTES LABORALES / OCUPACIONALES DEL CALIFICADO (Beneficiario y/o Subsidiado)</th>
    </tr>
    <tr>
        <td colspan="6"><b>Tipo de vinculación laboral:</b></td>
        <td colspan="5">Independiente: {{$employment && $activity->employment != '' ? 'X' : ''}}</td>
        <td colspan="5">Dependiente: {{$employment && $activity->employment == '' ? 'X' : ''}}</td>
    </tr>
    <tr>
        <td colspan="6"><b>Nombre del trabajo/empleo: </b>{{$employment ? $employment->position : ''}}</td>
        <td colspan="5">Ocupación: {{$employment ? $employment->description : ''}}</td>
        <td colspan="5">Código CIUO:</td>
    </tr>
    <tr>
        <td colspan="8"><b>Nombre actividad económica: </b>{{$employment ? $employment->position : ''}}</td>
        <td colspan="8"><b>Clase: </b></td>
    </tr>
    <tr>
        <td colspan="8"><b>Nombre de la
                empresa: </b>{{$employment && $employment->employer ? $employment->employer->name : ''}}</td>
        <td colspan="8"><b>NIT/CC: </b>{{ $employment && $employment->employer ? $employment->employer->nit : '' }}</td>
    </tr>
    <tr>
        <th colspan="16">5. FUNDAMENTOS DE LA CALIFICACIÓN</th>
    </tr>
    <tr>
        <th colspan="16"> RELACION DE DOCUMENTOS / EXAMEN FISICO- (Descripción)</th>
    </tr>
    <tr>
        <td colspan="16" class="td_justify" style="border-bottom: transparent">
            <b>5.1 HISTORIA CLÍNICA</b>
        </td>
    </tr>
    <tr>
        <td colspan="16" style="text-align: justify">
            {{--@foreach(explode("\n",$activity->invalidity_state_pfour->description_valoration_current_illness) as $text)
                {{$text}}--}}
            {{$activity->invalidity_state_pfour->description_valoration_current_illness}}
        </td>
    </tr>
    <tr>
        <td colspan="16" class="td_justify" style="border-bottom: transparent">
            {{--@endforeach--}}
        </td>
    </tr>
    {{--<tr>
        <td colspan="16"></td>
    </tr>--}}

    <tr>
        <td colspan="16" style="border-bottom: white;"><b>5.2 ESTUDIOS CLÍNICOS/PRUEBAS OBJETIVAS</b></td>
    </tr>
    @foreach($activity->invalidity_state_pfour->interconsultations as $inter)
     
    
        @if($activity->invalidity_state_pfour->valoration_valoration_type === 'PRESENCIAL' && $inter->exam_result == 'La interconsulta de Valoración médica se verá reflejada en el PDF de dictamen.')
            <tr>
                <td colspan="4" style="border-top: 1px solid black;"><b>Fecha
                        Examen </b>{{$inter->exam_date ? $inter->exam_date->formatLocalized('%d/%B/%Y') : ''}}</td>
                <td colspan="6" style="border-top: 1px solid black;"><b>Especialidad </b>{{$inter->exam_name}}</td>
                <td colspan="6" style="border-top: 1px solid black;"><b>Profesional </b>{{$inter->exam_profesional}}
                </td>
            </tr>
            <tr>
                <td colspan="16" style="border-bottom: white; text-align: justify !important;">
                    @foreach(explode("\n",$textInterconsultation) as $text)
                        {!! htmlspecialchars($text) !!}
                </td>
            </tr>
            <tr>
                <td colspan="16" style="border-bottom: white; text-align: justify">
                    @endforeach
                </td>
            </tr>
        @elseif($activity->invalidity_state_pfour->valoration_valoration_type === 'VALORACION TELEFONICA TITULO II' && $inter->exam_result == 'La interconsulta de Valoración médica se verá reflejada en el PDF de dictamen.')
            <tr>
                <td colspan="4" style="border-top: 1px solid black;"><b>Fecha
                        Examen </b>{{$inter->exam_date ? $inter->exam_date->formatLocalized('%d/%B/%Y') : ''}}</td>
                <td colspan="6" style="border-top: 1px solid black;"><b>Especialidad </b>{{$inter->exam_name}}</td>
                <td colspan="6" style="border-top: 1px solid black;"><b>Profesional </b>{{$inter->exam_profesional}}
                </td>
            </tr>
            <tr>
                <td colspan="16" style="border-bottom: white; text-align: justify !important;">
                    @foreach(explode("\n",$textInterconsultationVirtual) as $text)
                        {!! htmlspecialchars($text) !!}
                </td>
            </tr>
            <tr>
                <td colspan="16" style="border-bottom: white; text-align: justify;">
                    @endforeach
                </td>
            </tr>
        @else
            <tr>
                <td colspan="4" style="border-top: 1px solid black;"><b>Fecha
                        Examen </b>{{$inter->exam_date ? $inter->exam_date->formatLocalized('%d/%B/%Y') : ''}}</td>
                <td colspan="6" style="border-top: 1px solid black;"><b>Especialidad </b>{{$inter->exam_name}}</td>
                <td colspan="6" style="border-top: 1px solid black;"><b>Profesional </b>{{$inter->exam_profesional}}
                </td>
            </tr>
            <tr>
                <td colspan="16" style="border-bottom: white; text-align: justify !important;">
                    @foreach(explode("\n",$inter->exam_result) as $text)
                        {!! htmlspecialchars($text) !!}
                </td>
            </tr>
            <tr>
                <td colspan="16" style="border-bottom: white; text-align: justify;">
                    @endforeach
                </td>
            </tr>
        @endif
    @endforeach
    <tr>
        <td colspan="16"></td>
    </tr>
    <tr>
        <td colspan="16"><b>5.3 EXAMEN FÍSICO</b></td>
    </tr>
    <tr>
        @php
            $date = \Carbon\Carbon::parse($activity->invalidity_state_pfour->valoration_v_date);
        @endphp
        <td colspan="6"><b>Profesional Evaluador:</b> {{$activity->invalidity_state_pfour->doc_full_name}}</td>
        <td colspan="6"><b>Fecha de valoración:</b> {{$date->formatLocalized('%d/%B/%Y')}}</td>
        <td colspan="2"><b>Hora:</b> {{$activity->invalidity_state_pfour->valoration_v_time}}</td>
        <td colspan="2"><b>Rethus:</b> {{$activity->invalidity_state_pfour->doc_rethus}}</td>
    </tr>
    @if($activity->invalidity_state_pfour->valoration_valoration_type  == 'PRESENCIAL')
        <tr>
            <td colspan="16" style="text-align: justify !important; border-top: 1px solid black">
                @foreach(explode("\n",$textInterconsultationGstate) as $text)
                    {!! htmlspecialchars($text) !!}
            </td>
        </tr>
        <tr>
            <td colspan="16" style="border-bottom: white; text-align: justify">
                @endforeach
            </td>
        </tr>
    @endif
    @if($activity->invalidity_state_pfour->valoration_valoration_type  == 'VALORACION TELEFONICA TITULO II')
        <tr>
            <td colspan="16">“No se realiza valoración presencial, se lleva a cabo seguimiento vía telefónica por el
                profesional en Fisioterapia /
                Terapia ocupacional; con el fin de realizar la descripción del Rol Laboral y/o Rol ocupacional y otras
                áreas ocupacionales”.
            </td>
        </tr>
    @endif
    <tr>
        <th colspan="16">
            6. FUNDAMENTOS PARA LA CALIFICACION DE LA PERDIDA DE LA CAPACIDAD LABORAL Y OCUPACIONAL -<br>
            TITULOS I y II <br>
            TITULO I CALIFICACIÓN / VALORACIÓN DE LAS DEFICIENCIAS
        </th>
    </tr>
    <tr>
        <th colspan="16">CLASE FUNCIONAL / VALOR PORCENTUAL</th>
    </tr>
    <tr>
        <td colspan="2"><b>CIE 10</b></td>
        <td colspan="6"><b>DIAGNÓSTICO</b></td>
        <td colspan="2"><b>ORIGEN</b></td>
        <td colspan="6"><b>DEFICIENCIA(S)/MOTIVO DE CALIFICACION/CONDICIONES DE SALUD</b></td>
    </tr>
    @foreach($invalidity_state_pfour->diagnostics as $diag)
        <tr>
            <td colspan="2">
                {{$diag->code}}
            </td>
            <td colspan="6">
                {{$diag->description}}
            </td>
            <td colspan="2">
                {{$ORIGINS_PCL[$diag->origin]}}
            </td>
            <td colspan="6">
                {{$diag->deficiences ? strtoupper($diag->deficiences) : ''}}
            </td>
        </tr>
    @endforeach
    <tr style="text-align: center">
        <td colspan="1">No.</td>
        <td colspan="3">Descripción <br></td>
        <td colspan="9" style="text-align: center"><b>Clase funcional/Valor porcentual</b></td>
        <td colspan="1"></td>
        <td colspan="1"></td>
        <td colspan="1"></td>
    </tr>
    <tr style="text-align: center">
        <td colspan="1"></td>
        <td colspan="3"></td>
        <td colspan="1">No Tabla <br></td>
        <td colspan="1">Clase</td>
        <td colspan="1">CFPFU</td>
        <td colspan="1" style="font-size: 9px">CFM 1</td>
        <td colspan="1">CFM 2</td>
        <td colspan="1">CFM 3</td>
        <td colspan="1">Ajuste Total Deficiencia</td>
        <td colspan="2">Resultado</td>
        <td colspan="1">CAT</td>
        <td colspan="1">Dominancia</td>
        <td colspan="1">% Total Deficiencia (F.Balthazar,sin ponderar)</td>
    </tr>

    <tr style="text-align: center">
        <td colspan="1"></td>
        <td colspan="3"></td>
        <td colspan="1"></td>
        <td colspan="1"></td>
        <td colspan="1"></td>
        <td colspan="1"></td>
        <td colspan="1"></td>
        <td colspan="1"></td>
        <td colspan="1"></td>
        <td colspan="1">Clase final y literal</td>
        <td colspan="1">%deficiencia</td>
        <td colspan="1"></td>
        <td colspan="1"></td>
        <td colspan="1"></td>
    </tr>

    @foreach($invalidity_state_pfour->deficiences as $deficience)
        <tr style="text-align: center">
            <td colspan="1">{{$deficienceCount++}}</td>
            <td colspan="3" style="text-align: left">{{$deficience->name}}</td>
            <td colspan="1">{{$deficience->table}}</td>
            <td colspan="1">{{$deficience->class}}</td>
            <td colspan="1">{{$deficience->cfpfu}}</td>
            <td colspan="1">{{$deficience->cfm1}}</td>
            <td colspan="1">{{$deficience->cfm2}}</td>
            <td colspan="1"></td>
            <td colspan="1"></td>
            <td colspan="1">{{$deficience->class_final}}</td>
            <td colspan="1"> {{number_format($deficience->getPercentageTotalAttribute(),2)}}</td>
            <td colspan="1">{{$deficience->cat}}</td>
            <td colspan="1">{{$deficience->dominant ? 'SI' : 'NO'}}</td>
            <td colspan="1"></td>
        </tr>
    @endforeach
    <tr>
        <td colspan="16" style="text-align: right;"><b>%Total Deficiencia (sin
                ponderar): {{number_format($activity->invalidity_state_pfour ? $activity->invalidity_state_pfour->deficiencesSum() : '0.0', 2)}}</b></td>
    </tr>
    <tr>
        <td colspan="6">CFP: Clase Factor principal</td>
        <td colspan="5">CFM: Clase Factor Modulador</td>
        <td colspan="5">CFU: Clase Factor único</td>
    </tr>
    <tr>
        <td colspan="16">Formula: Ajuste Total de Deficiencias por tabla: (CFM1-CFP) + (CFM2-CFP) + (CFM3-CFP)</td>
    </tr>
    <tr>
        <td colspan="16">Formula de Balthazar: Obtiene el valor final de las deficiencias sin ponderar</td>
    </tr>
    <tr>
        <td colspan="5">Combinación de valores:</td>
        <td colspan="1" style="border-right: transparent; text-align: center">A+</td>
        <td colspan="5" style="text-align: center">(100-A) *B</td>
        <td colspan="5">A: Deficiencia de mayor valor</td>
    </tr>
    <tr>
        <td colspan="5"></td>
        <td colspan="1" style="border-right: transparent"></td>
        <td colspan="5" style="text-align: center">100</td>
        <td colspan="5">B: Deficiencia de menor valor</td>
    </tr>
    <tr>
        <td colspan="16"><b>VALOR FINAL DE LA PRIMERA PARTE (TITULO PRIMERO)</b></td>
    </tr>
    <tr style="text-align: center">
        <td colspan="8"><b>CALCULO FINAL DE LA DEFICIENCIA- PONDERADA:</b></td>
        <td colspan="4"><b>% Total deficiencia(sin ponderar) X 0,5</b></td>
        <td colspan="4"><b>{{number_format($activity->invalidity_state_pfour ? $activity->invalidity_state_pfour->deficiences_total : '0.00', 2)}}</b></td>
    </tr>
    @if($activity->invalidity_state_pfour->use_laboral_role)
        <tr>
            <th colspan="16">TITULO II <br> VALORACION DEL ROL OCUPACIONAL - {{strtoupper($ageText)}}</th>
        </tr>
        <tr>
            <td colspan="16">
                <b>FUNDAMENTACIÓN DE LA VALORACIÓN DEL ROL OCUPACIONAL PARA {{strtoupper($ageText)}}</b>
            </td>
        </tr>
        <tr>
            @php
                $laboralDescription = '';
                if ($activity->invalidity_state_pfour->laborally_active) {
                    $laboralDescription = $activity->invalidity_state_pfour->laboral_role_sustentation;
                }
                 elseif($activity->invalidity_state_pfour->use_laboral_role) {
                    $laboralDescription = $activity->invalidity_state_pfour->use_laboral_role_description;
                 }

                $laboralText = explode("\n", $laboralDescription);
            @endphp
            <td colspan="16">
                @foreach ($laboralText as $description)
                    {{$description}} <br>
                @endforeach
            </td>
        </tr>
        <tr style="text-align: center">
            <td colspan="2"><b>CLASE</b></td>
            <td colspan="11"><b>CATEGORIA</b></td>
            <td colspan="3"><b>PORCENTAJE</b></td>
        </tr>
        @php
            $value = number_format($activity->invalidity_state_pfour ? $activity->invalidity_state_pfour->deficiences_total : '0.00', 2);
            $letter = '';

            if ($value >= 0 && $value <= 9.99) {
                $letter = 'A';
            } elseif ($value >= 10 && $value <= 24.99) {
                $letter = 'B';
            } elseif ($value >= 25 && $value <= 34.99) {
                $letter = 'C';
            } elseif ($value >= 35 && $value <= 49.99) {
                $letter = 'D';
            } elseif ($value >= 50) {
                $letter = 'E';
            }
        @endphp
        <tr style="text-align: center">
            <td colspan="2">{{$letter}}</td>
            <td colspan="11">
                {{
                    !is_null($activity->invalidity_state_pfour->role_category) && isset(App\Pcl::$ROLE_CATEGORIES[$activity->invalidity_state_pfour->role_category])
                        ? App\Pcl::$ROLE_CATEGORIES[$activity->invalidity_state_pfour->role_category]
                        : ''
                }}
            </td>
            <td colspan="3">{{number_format($activity->invalidity_state_pfour ? $activity->invalidity_state_pfour->laboral_role_total : $activity->affiliate->pcl_age_restriction(), 2)}}</td>
        </tr>
    @else
        <tr>
            <th colspan="16">TITULO II VALORACION DEL ROL LABORAL, ROL OCUPACIONAL Y OTRAS AREAS OCUPACIONALES</th>
        </tr>
        <tr>
            <td colspan="16"><b>2. OTRAS INTERCONSULTAS</b></td>
        </tr>
        <tr>
            <td colspan="16"></td>
        </tr>
        <tr>
            <td colspan="16">
                <b>2.1 FUNDAMENTACIÓN ROL LABORAL (SUSTENTACIÓN CAPACIDAD/DESEMPEÑO Y AUTOSUFICIENCIA ECONÓMICAS)</b>
            </td>
        </tr>
        <tr>
            <td colspan="16">
                {{$activity->invalidity_state_pfour ? $activity->invalidity_state_pfour->laboral_role_sustentation : ''}}
            </td>
        </tr>
        <tr>
            <td colspan="16" style="text-align: center"><b>Restricción en el rol laboral</b></td>
        </tr>
        <tr>
            <td colspan="2" style="text-align: center"><b>Tabla</b></td>
            <td colspan="7" style="text-align: center"><b>Categoría / Nombre</b></td>
            <td colspan="7" style="text-align: center"><b>Porcentaje(%)</b></td>
        </tr>
        <tr>
            <td colspan="2" style="text-align: center">1</td>
            <td colspan="7">Restricciones del rol laboral</td>
            <td colspan="7" style="text-align: center">{{$activity->invalidity_state_pfour ? $activity->invalidity_state_pfour->role_restriction : 0}}</td>
        </tr>
        <tr>
            <td colspan="16" style="text-align: center"><b>Restricción en función de la autosuficiencia económica</b>
            </td>
        </tr>
        <tr>
            <td colspan="2" style="text-align: center">2</td>
            <td colspan="7">Restricciones autosuficiencia económica</td>
            <td colspan="7" style="text-align: center">{{$activity->invalidity_state_pfour ? $activity->invalidity_state_pfour->economic_self : 0}}</td>
        </tr>
        <tr>
            <td colspan="16" style="text-align: center">
                <b>En función de edad cronológica por edad cumplida al momento de
                    calificar</b>
            </td>
        </tr>
        <tr>
            <td colspan="2" style="text-align: center">3</td>
            <td colspan="7">Restricciones en función de la edad cronológica</td>
            <td colspan="7"
                style="text-align: center">{{$activity->invalidity_state_pfour->activity_id !==2427 ? ($activity->invalidity_state_pfour ? $pcl_age_restriction : '0.0') : '0.0' }}</td>
        </tr>
        <tr>
            <td colspan="9">Sumatoria rol laboral, autosuficiencia económica y edad (30%)</td>
            <td colspan="7"
                style="text-align: center">
                @if($activity->invalidity_state_pfour->activity_id ==2427 || $pcl_age_restriction == 0.0)
                    0.0
                @elseif($activity->invalidity_state_pfour)
                    {{  $activity->invalidity_state_pfour->role_restriction + $activity->invalidity_state_pfour->economic_self + $activity->affiliate->pcl_age_restriction()}}
                @else
                    0.0
                @endif
            </td>
        </tr>
        <tr>
            <td colspan="16" style="border-bottom: transparent"></td>
        </tr>
        <tr>
            <td colspan="16"><b>CALIFICACIÓN OTRAS AREAS OCUPACIONALES</b></td>
        </tr>
        <tr>
            <td colspan="16">
                <b>2.2 FUNDAMENTACIÓN DE OTRAS AREAS OCUPACIONALES(SUSTENTACIÓN LIMITACIONES AVD y AVDI)</b>
            </td>
        </tr>
        <tr>
            <td colspan="16">{{$activity->invalidity_state_pfour ? $activity->invalidity_state_pfour->laboral_role_sustentation_and_other_occupational_activities : ''}}
            </td>
        </tr>
        <tr>
            <td colspan="2" style="text-align: center">CLASE</td>
            <td colspan="2" style="text-align: center">VALOR</td>
            <td colspan="12" style="text-align: center">Tabla 4 Escala de calificación de otras areas de ocupacionales y
                valores
            </td>
        </tr>
        <tr>
            <td colspan="2">A</td>
            <td colspan="2">0.0</td>
            <td colspan="12">No hay dificultad, no dependencia</td>
        </tr>
        <tr>
            <td colspan="2">B</td>
            <td colspan="2">0.1</td>
            <td colspan="12">Dificultad leve, no dependencia</td>
        </tr>
        <tr>
            <td colspan="2">C</td>
            <td colspan="2">0.2</td>
            <td colspan="12">Dificultad moderada, dependencia moderada</td>
        </tr>
        <tr>
            <td colspan="2">D</td>
            <td colspan="2">0.3</td>
            <td colspan="12">Dificultad severa -dependencia severa</td>
        </tr>
        <tr>
            <td colspan="2">E</td>
            <td colspan="2">0.4</td>
            <td colspan="12">Dificultad Completa- dependencia Grave completa</td>
        </tr>
        <tr>
            <td colspan="16" style="border-bottom: transparent"></td>
        </tr>


        <tr>
            <td colspan="16"></td>
        </tr>
        <tr style="text-align: center">
            <td colspan="1">COD</td>
            <td colspan="4">AREA OCUPACIONAL</td>
            <td colspan="1" class="grey">d110</td>
            <td colspan="1" class="grey">d115</td>
            <td colspan="1" class="grey">d140 145</td>
            <td colspan="1" class="grey">d150</td>
            <td colspan="1" class="grey">d160</td>
            <td colspan="1" class="grey">d165</td>
            <td colspan="1" class="grey">d170</td>
            <td colspan="1" class="grey">d172</td>
            <td colspan="1" class="grey">d175</td>
            <td colspan="1" class="grey">d1751</td>
            <td colspan="1" class="grey"></td>
        </tr>
        <tr style="text-align: center">
            <td colspan="1">d1</td>
            <td colspan="2">Tabla 6</td>
            <td colspan="2">Aprendizaje y aplicación del conocimiento</td>
            @for($i = 0; $i < 10; $i++)
                <td colspan="1">1.{{$i+1}}</td>
            @endfor
            <td colspan="1"></td>
        </tr>
        <tr style="text-align: center">
            <td colspan="1"></td>
            <td colspan="2"></td>
            <td colspan="2"></td>
            @for($i = 0; $i < 10; $i++)
                <td colspan="1">{{$activity->invalidity_state_pfour->tableValue('T6', $i)}}</td>
            @endfor
            <td colspan="1"><b>{{$activity->invalidity_state_pfour->totalTableValue('T6')}}</b></td>
        </tr>
        <tr style="text-align: center">
            <td colspan="1"></td>
            <td colspan="2"></td>
            <td colspan="2"></td>
            <td colspan="1" class="grey">d310</td>
            <td colspan="1" class="grey">d315</td>
            <td colspan="1" class="grey">d320</td>
            <td colspan="1" class="grey">d325</td>
            <td colspan="1" class="grey">d330</td>
            <td colspan="1" class="grey">d335</td>
            <td colspan="1" class="grey">d345</td>
            <td colspan="1" class="grey">d350</td>
            <td colspan="1" class="grey">d355</td>
            <td colspan="1" class="grey">d360</td>
            <td colspan="1" class="grey"></td>
        </tr>
        <tr style="text-align: center">
            <td colspan="1">d3</td>
            <td colspan="2">Tabla 7</td>
            <td colspan="2">Comunicación</td>
            @for($i = 0; $i < 10; $i++)
                <td colspan="1">1.{{$i+1}}</td>
            @endfor
            <td colspan="1"></td>
        </tr>
        <tr style="text-align: center">
            <td colspan="1"></td>
            <td colspan="2"></td>
            <td colspan="2"></td>
            @for($i = 0; $i < 10; $i++)
                <td colspan="1">{{$activity->invalidity_state_pfour->tableValue('T7', $i)}}</td>
            @endfor
            <td colspan="1"><b>{{$activity->invalidity_state_pfour->totalTableValue('T7')}}</b></td>
        </tr>
        <tr style="text-align: center">
            <td colspan="1"></td>
            <td colspan="2"></td>
            <td colspan="2"></td>
            <td colspan="1" class="grey">d410</td>
            <td colspan="1" class="grey">d415</td>
            <td colspan="1" class="grey">d430</td>
            <td colspan="1" class="grey">d440</td>
            <td colspan="1" class="grey">d445</td>
            <td colspan="1" class="grey">d455</td>
            <td colspan="1" class="grey">d460</td>
            <td colspan="1" class="grey">d465</td>
            <td colspan="1" class="grey">d470</td>
            <td colspan="1" class="grey">d475</td>
            <td colspan="1" class="grey"></td>
        </tr>
        <tr style="text-align: center">
            <td colspan="1">d4</td>
            <td colspan="2">Tabla 8</td>
            <td colspan="2">Movilidad</td>
            @for($i = 0; $i < 10; $i++)
                <td colspan="1">1.{{$i+1}}</td>
            @endfor
            <td colspan="1"></td>
        </tr>
        <tr style="text-align: center">
            <td colspan="1"></td>
            <td colspan="2"></td>
            <td colspan="2"></td>
            @for($i = 0; $i < 10; $i++)
                <td colspan="1">{{$activity->invalidity_state_pfour->tableValue('T8', $i)}}</td>
            @endfor
            <td colspan="1"><b>{{$activity->invalidity_state_pfour->totalTableValue('T8')}}</b></td>
        </tr>
        <tr style="text-align: center">
            <td colspan="1"></td>
            <td colspan="2"></td>
            <td colspan="2"></td>
            <td colspan="1" class="grey">d510</td>
            <td colspan="1" class="grey">d515</td>
            <td colspan="1" class="grey">d520</td>
            <td colspan="1" class="grey">d525</td>
            <td colspan="1" class="grey">d530</td>
            <td colspan="1" class="grey">d535</td>
            <td colspan="1" class="grey">d545</td>
            <td colspan="1" class="grey">d550</td>
            <td colspan="1" class="grey">d555</td>
            <td colspan="1" class="grey">d560</td>
            <td colspan="1" class="grey"></td>
        </tr>
        <tr style="text-align: center">
            <td colspan="1">d5</td>
            <td colspan="2">Tabla 9</td>
            <td colspan="2">Autocuidado - cuidado personal</td>
            @for($i = 0; $i < 10; $i++)
                <td colspan="1">1.{{$i+1}}</td>
            @endfor
            <td colspan="1"></td>
        </tr>
        <tr style="text-align: center">
            <td colspan="1"></td>
            <td colspan="2"></td>
            <td colspan="2"></td>
            @for($i = 0; $i < 10; $i++)
                <td colspan="1">{{$activity->invalidity_state_pfour->tableValue('T9', $i)}}</td>
            @endfor
            <td colspan="1"><b>{{$activity->invalidity_state_pfour->totalTableValue('T9')}}</b></td>
        </tr>
        <tr style="text-align: center">
            <td colspan="1"></td>
            <td colspan="2"></td>
            <td colspan="2"></td>
            <td colspan="1" class="grey">d610</td>
            <td colspan="1" class="grey">d615</td>
            <td colspan="1" class="grey">d620</td>
            <td colspan="1" class="grey">d625</td>
            <td colspan="1" class="grey">d630</td>
            <td colspan="1" class="grey">d635</td>
            <td colspan="1" class="grey">d645</td>
            <td colspan="1" class="grey">d650</td>
            <td colspan="1" class="grey">d655</td>
            <td colspan="1" class="grey">d660</td>
            <td colspan="1" class="grey"></td>
        </tr>
        <tr style="text-align: center">
            <td colspan="1">d6</td>
            <td colspan="2">Tabla 10</td>
            <td colspan="2">Vida doméstica</td>
            @for($i = 0; $i < 10; $i++)
                <td colspan="1">1.{{$i+1}}</td>
            @endfor
            <td colspan="1"></td>
        </tr>
        <tr style="text-align: center">
            <td colspan="1"></td>
            <td colspan="2"></td>
            <td colspan="2"></td>
            @for($i = 0; $i < 10; $i++)
                <td colspan="1">{{$activity->invalidity_state_pfour->tableValue('T10', $i)}}</td>
            @endfor
            <td colspan="1"><b>{{$activity->invalidity_state_pfour->totalTableValue('T10')}}</b></td>
        </tr>
        <tr style="text-align: center;">
            <td colspan="15">Sumatoria total otras áreas ocupacionales (20%)</td>
            <td colspan="1"><b>{{$activity->invalidity_state_pfour ? number_format($activity->invalidity_state_pfour->totalTableValues(), 2) : ''}}</b>
            </td>
        </tr>
        <tr>
            <td colspan="16" style="text-align: center">VALOR FINAL DE LA SEGUNDA PARTE (TITULO SEGUNDO)</td>
        </tr>
        <tr style="text-align: center">
            <td colspan="6"><span>Restricciones rol laboral+ </span><br>
                <span>Autosuficiencia económica + Edad</span></td>
            <td colspan="5">+ Otras Áreas Ocupacionales +</td>
            <td colspan="5">
                <span>= </span><span>TITULO II</span> <br>
                <span> (Valor Final)</span>
            </td>
        </tr>
        <tr style="text-align: center">
            <td colspan="6">
                @if($activity->invalidity_state_pfour->activity_id ==2427 || $pcl_age_restriction == 0.0)
                    0.0
                @elseif($activity->invalidity_state_pfour)
                    {{  $activity->invalidity_state_pfour->role_restriction + $activity->invalidity_state_pfour->economic_self + $activity->affiliate->pcl_age_restriction()}}
                @else
                    0.0
                @endif
            </td>
            <td colspan="5">{{ $total_table_values = $activity->invalidity_state_pfour ? number_format($activity->invalidity_state_pfour->totalTableValues(), 2) : '0.0' }}</td>
            <td colspan="5">{{ number_format($activity->invalidity_state_pfour ? $activity->invalidity_state_pfour->laboral_role_total : $activity->affiliate->pcl_age_restriction(), 2) }}</td>
        </tr>
    @endif
    <tr>
        <th colspan="16">7. CONCEPTO FINAL DEL DICTAMEN PERICIAL</th>
    </tr>
    <tr style="text-align: center">
        <td colspan="4" style="border-right: transparent">Perdida de capacidad laboral</td>
        <td colspan="1" style="border-right: transparent">=</td>
        <td colspan="3" style="border-right: transparent"><span>TITULO I </span><br><span>(Valor Final Ponderada)</span>
            <br><br><span>{{number_format($activity->invalidity_state_pfour ? $activity->invalidity_state_pfour->deficiences_total : '0.0', 2)}}</span>
        </td>
        <td colspan="1" style="border-right: transparent">+</td>
        <td colspan="3" style="border-right: transparent"><span>TITULO II </span><br><span>(Valor Final)</span> <br><br><span>{{number_format($activity->invalidity_state_pfour ? $activity->invalidity_state_pfour->laboral_role_total : $activity->affiliate->pcl_age_restriction(), 2)}}</span>
        </td>
        <td colspan="1" style="border-right: transparent">=</td>
        <td colspan="3"><span>Valor Final</span>
            <br><br><span>{{ $activity->invalidity_state_pfour ? number_format($activity->invalidity_state_pfour->deficiences_total + $activity->invalidity_state_pfour->laboral_role_total, 2) : number_format($activity->affiliate->pcl_age_restriction(), 2) }}</span>
        </td>
    </tr>
    <tr>
        <td colspan="16">FECHA DE ESTRUCTURACIÓN (dd-mm-aaaa):
            {{$activity->invalidity_state_pfour && $activity->invalidity_state_pfour->structuring_at ? $activity->invalidity_state_pfour->structuring_at->formatLocalized('%d/%B/%Y') : ''}} </td>
    </tr>
    <tr>
        <td colspan="16">
            <span>SUSTENTACIÓN FECHA DE ESTRUCTURACIÓN: {{$activity->invalidity_state_pfour && $activity->invalidity_state_pfour->structuring_at ? $activity->invalidity_state_pfour->structuring_at->formatLocalized('%d/%B/%Y') : ''}} - {{$activity->invalidity_state_pfour->structuring_support}}</span>
            <span></span>
        </td>
    </tr>
    <tr>
        <td colspan="16">ORIGEN: {{$ORIGINS_PCL[$activity->invalidity_state_pfour->origin] }}</td>
    </tr>
    <tr>
        <td colspan="16">FECHA DE
            ACCIDENTE: {{$activity->invalidity_state_pfour && $activity->invalidity_state_pfour->event_date ? $activity->invalidity_state_pfour->event_date->formatLocalized('%d/%B/%Y') : ''}}
        </td>
    </tr>
    <tr>
        <td colspan="16"><b>CLASIFICACIÓN CONDICIÓN DE SALUD - TIPO DE ENFERMEDAD</b></td>
    </tr>
    <tr>
        <td colspan="16">REQUIERE DE TERCERAS PERSONAS PARA DECIDIR POR SI MISMO (DISCAPACIDAD MENTAL
            ABSOLUTA): {{$activity->invalidity_state_pfour->require_3rd_dk  ? : ''}}</td>
    </tr>
    <tr>
        <td colspan="16">REQUIERE DE DISPOSITIVOS DE APOYO(Para realizar sus actividades de la vida
            diaria): {{$activity->invalidity_state_pfour->require_device ? : ''}}</td>
    </tr>
    <tr>
        <td colspan="16">REQUIERE DE TERCERAS PERSONAS PARA REALIZAR SUS ACTIVIDADES DE LA VIDA
            DIARIA: {{$activity->invalidity_state_pfour->require_3rd ? : ''}}</td>
    </tr>

    </tbody>
</table>
<div style="page-break-inside:avoid;">

    <table>
        <tbody>
        <tr>
            <td colspan="16" style="border-top: transparent;border-right: transparent;border-left: transparent;"><b>TIPO
                    DE
                    ENFERMEDAD:</b></td>
        </tr>
        <tr>
            <td colspan="16">
                <b></b><span>¿Enfermedad degenerativa, progresiva y crónica? </span>{{$activity->invalidity_state_pfour->congenital_progresion_cronical_disease ? : ''}}
            </td>
        </tr>
        <tr>
            <td colspan="16">
                <b></b><span>¿Catastrófica, alto costo, ruinosa? </span>{{$activity->invalidity_state_pfour->catastrophic ? : ''}}</td>
        </tr>
        <tr>
            <td colspan="16">
                <b></b><span>¿Enfermedad congénita o cercana al nacimiento? </span>{{$activity->invalidity_state_pfour->congenital_near_to_birth ? : ''}}
            </td>
        </tr>
        <tr>
            <td colspan="16"><b></b><span>PCL/PCO: REVISABLE: </span>{{$activity->invalidity_state_pfour->pcl_pclo}}</td>
        </tr>
        </tbody>
    </table>
    <table class="signature-table">
        <tbody>
        @php
            $author1 = null;
            $author2 = null;
            $action_1 = false;
            $action_2 = false;
            $doctor1 = [];
            $doctor2 = [];
            $author3 = null;
            $action_3 = false;
            $doctor3 = [];
             $user = new \App\User;
            foreach($activity->activity_actions as $aa){
                if ($aa->action_id == 544) {
                   $action_1 = true;

                   if ($author1 === null) {
                       $author1 = $aa->author_id;
                        $doctor1 = $user->getSignFromId($author1) ?: [];
                   }
               }
                if ($aa->action_id == 518) {
                   $action_2 = true;

                   if ($author2 === null) {
                       $author2 = $aa->author_id;
                       $doctor2 = $user->getSignFromId($author2) ?: [];
                   }
               }
                if ($aa->action_id == 513) {
                   $action_3 = true;

                   if ($author3 === null) {
                       $author3 = $aa->author_id;
                       $doctor3 = $user->getSignFromId($author3) ?: [];
                   }
               }
            }
        @endphp
        <tr style="padding-top: 1rem">
            <th colspan="16">GRUPO CALIFICADOR</th>
        </tr>
        @if($activity->id == 686488)
            <tr>
                <td style="vertical-align: middle; text-align: center;" class="signature-cell" colspan="8">
                    @if($withSign)
                        <img class="signature-image" alt="FIRMA"
                             src="{{array_key_exists('signature', $doctor1) ? secure_url('file/' . $doctor1['signature']) : '' }}"/>
                        <br/>
                    @endif
                    {{array_key_exists('full_name', $doctor1) ? $doctor1['full_name'] : ''}} <br>
                    {{array_key_exists('company', $doctor1) ? $doctor1['company'] : ''}} <br>
                    Licencia {{array_key_exists('license', $doctor1) ? $doctor1['license'] : ''}} <br>
                    Rethus {{array_key_exists('rethus', $doctor1) ? $doctor1['rethus'] : ''}}
                </td>
                <td style="vertical-align: middle; text-align: center;" class="signature-cell" colspan="8">
                    @if($withSign)
                        <img class="signature-image" alt="FIRMA"
                             src="{{array_key_exists('signature', $doctor3) ? secure_url('file/' . $doctor3['signature']) : '' }}"/>
                        <br/>
                    @endif
                    {{array_key_exists('full_name', $doctor3) ? $doctor3['full_name'] : ''}} <br>
                    {{array_key_exists('company', $doctor3) ? $doctor3['company'] : ''}} <br>
                    Licencia {{array_key_exists('license', $doctor3) ? $doctor3['license'] : ''}} <br>
                    Rethus {{array_key_exists('rethus', $doctor3) ? $doctor3['rethus'] : ''}}
                </td>
            </tr>
        @elseif($action_1 == true && $action_2 == true)
            <tr>
                <td style="vertical-align: middle; text-align: center;" class="signature-cell" colspan="8">
                    @if($withSign)
                        <img class="signature-image" alt="FIRMA"
                             src="{{array_key_exists('signature', $doctor1) ? secure_url('file/' . $doctor1['signature']) : '' }}"/>
                        <br/>
                    @endif
                    {{array_key_exists('full_name', $doctor1) ? $doctor1['full_name'] : ''}} <br>
                    {{array_key_exists('company', $doctor1) ? $doctor1['company'] : ''}} <br>
                    Licencia {{array_key_exists('license', $doctor1) ? $doctor1['license'] : ''}} <br>
                    Rethus {{array_key_exists('rethus', $doctor1) ? $doctor1['rethus'] : ''}}
                </td>
                <td style="vertical-align: middle; text-align: center;" class="signature-cell" colspan="8">
                    @if($withSign)
                        <img class="signature-image" alt="FIRMA"
                             src="{{array_key_exists('signature', $doctor2) ? secure_url('file/' . $doctor2['signature']) : '' }}"/>
                        <br/>
                    @endif
                    {{array_key_exists('full_name', $doctor2) ? $doctor2['full_name'] : ''}} <br>
                    {{array_key_exists('company', $doctor2) ? $doctor2['company'] : ''}} <br>
                    Licencia {{array_key_exists('license', $doctor2) ? $doctor2['license'] : ''}} <br>
                    Rethus {{array_key_exists('rethus', $doctor2) ? $doctor2['rethus'] : ''}}
                </td>
            </tr>
        @elseif($action_1 == true && $action_2 == false)
            <tr>
                <td style="vertical-align: middle; text-align: center;" class="signature-cell" colspan="8">
                    @if($withSign)
                        <img class="signature-image" alt="FIRMA"
                             src="{{array_key_exists('signature', $doctor1) ? secure_url('file/' . $doctor1['signature']) : '' }}"/>
                        <br/>
                    @endif
                    {{array_key_exists('full_name', $doctor1) ? $doctor1['full_name'] : ''}} <br>
                    {{array_key_exists('company', $doctor1) ? $doctor1['company'] : ''}} <br>
                    Licencia {{array_key_exists('license', $doctor1) ? $doctor1['license'] : ''}} <br>
                    Rethus {{array_key_exists('rethus', $doctor1) ? $doctor1['rethus'] : ''}}
                </td>
                <td style="vertical-align: middle; text-align: center;" class="signature-cell" colspan="8">
                    Nombre: No se ha ejecutado la acción pertinente <br>
                    Empresa: <br>
                    Licencia <br>
                    Rethus
                </td>
            </tr>
        @elseif($action_1 == false && $action_2 == true)
            <tr>
                <td style="vertical-align: middle; text-align: center;" class="signature-cell" colspan="8">
                    @if($withSign)
                        <img class="signature-image" alt="FIRMA"
                             src="{{array_key_exists('signature', $doctor2) ? secure_url('file/' . $doctor2['signature']) : '' }}"/>
                        <br/>
                    @endif
                    {{array_key_exists('full_name', $doctor2) ? $doctor2['full_name'] : ''}} <br>
                    {{array_key_exists('company', $doctor2) ? $doctor2['company'] : ''}} <br>
                    Licencia {{array_key_exists('license', $doctor2) ? $doctor2['license'] : ''}} <br>
                    Rethus {{array_key_exists('rethus', $doctor2) ? $doctor2['rethus'] : ''}}
                </td>
                <td style="vertical-align: middle; text-align: center;" class="signature-cell" colspan="8">
                    Nombre: No se ha ejecutado la acción pertinente <br>
                    Empresa: <br>
                    Licencia <br>
                    Rethus
                </td>
            </tr>
        @endif
        </tbody>
    </table>
</div>

</body>
</html>
