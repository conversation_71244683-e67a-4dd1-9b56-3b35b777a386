<div class="title">
    <i class="dropdown icon"></i>
    Gestión factura electrónica
    @if($activity->state_id == \App\States\StateMedicalBillsServiceSort::FACTURA_ELECTRONICA_EN_REVISION)
    <span style="color: red;" class="required">*</span>
    @endif
</div>
<div class="content">

    <div class="field">
        <div class="accordion">
            <div class="title">
                <i class="dropdown icon"></i>
                Ver factura electrónica
            </div>
            <div class="content">
                <div class="ui link cards" style="margin-top: 1rem; margin-bottom: 1rem;">
                    @foreach($doc_electronic_invoice as $document)
                        <div class="card">
                            <div class="content">
                                <div class="center aligned">
                                    <i class="huge file code outline icon"></i>
                                </div>
                                <div class="center aligned" style="padding-right: 4em; padding-left: 4em;" >
                                    <a href="{{ Storage::disk('s3')->url($document->path) }}" target="_blank">
                                        <button style="margin-top: 0.5em;" type="button" class="ui secondary icon button fluid">
                                            <i class="download icon"></i>
                                        </button>
                                    </a>
                                </div>
                            </div>
                            <div class="extra content center aligned">
                                <p class="file-name" title="{{ pathinfo($document->path, PATHINFO_FILENAME) }}">
                                    {{ pathinfo($document->path, PATHINFO_FILENAME) }}
                                </p>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
            <div class="title">
                <i class="dropdown icon"></i>
                Ver soportes factura electrónica
            </div>
            <div class="content">
                <div class="ui link cards" style="margin-top: 1rem; margin-bottom: 1rem;">
                    @foreach($doc_attachments_electronic_invoice_support as $document)
                        <div class="card">
                            <div class="content">
                                <div class="center aligned">
                                    <i class="huge file pdf outline icon"></i>
                                </div>
                                <div class="center aligned" style="padding-right: 4em; padding-left: 4em;" >
                                    <a href="{{ Storage::disk('s3')->url($document->path) }}" target="_blank">
                                        <button style="margin-top: 0.5em;" type="button" class="ui secondary icon button fluid">
                                            <i class="download icon"></i>
                                        </button>
                                    </a>
                                </div>
                            </div>
                            <div class="extra content center aligned">
                                <p class="file-name" title="{{ pathinfo($document->path, PATHINFO_FILENAME) }}">
                                    {{ pathinfo($document->path, PATHINFO_FILENAME) }}
                                </p>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
            <div class="title">
                <i class="dropdown icon"></i>
                Ver otros soportes
            </div>
            <div class="content">
                <div class="ui link cards" style="margin-top: 1rem; margin-bottom: 1rem;">
                    @foreach($doc_attachments_other_support as $document)
                        <div class="card">
                            <div class="content">
                                <div class="center aligned">
                                    <i class="huge file pdf outline icon"></i>
                                </div>
                                <div class="center aligned" style="padding-right: 4em; padding-left: 4em;" >
                                    <a href="{{ Storage::disk('s3')->url($document->path) }}" target="_blank">
                                        <button style="margin-top: 0.5em;" type="button" class="ui secondary icon button fluid">
                                            <i class="download icon"></i>
                                        </button>
                                    </a>
                                </div>
                            </div>
                            <div class="extra content center aligned">
                                <p class="file-name" title="{{ pathinfo($document->path, PATHINFO_FILENAME) }}">
                                    {{ pathinfo($document->path, PATHINFO_FILENAME) }}
                                </p>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>

    <table id="electronicInvoiceTable" class="ui celled table electronic_invoice_dinamic_table">
        <thead>
        <tr>
            <th># identificación trabajador</th>
            <th>Nombre paciente</th>
            <th>Póliza SORT</th>
            <th>Fecha caso</th>
            <th># del aviso</th>
            <th>Servicio</th>
            <th>Valor servicio</th>
            <th>Resultado</th>
            <th>Motivo de rechazo</th>
        </tr>
        </thead>
        <tbody>
        @foreach($dataElectronicInvoice as $index => $data)
            @php
                $currencySymbol = $TYPE_CURRENCY[$medical_bills_service->type_currency] ?? '';
                $formattedValue = $currencySymbol . number_format($data->service_value, 2, ',', '.');
            @endphp
            <tr>
                <td>{{$data->worker_id}}</td>
                <td>{{ mb_convert_case(mb_strtolower($data->patient_name, 'UTF-8'), MB_CASE_TITLE, 'UTF-8') }}</td>
                <td>
                    @if($data->policy_sort_no)
                        {{ $data->policySort ? $data->policySort->formatNumberConsecutive() ?? 'Consecutivo no disponible' : 'Póliza no disponible' }}
                    @else
                        {{'Número de póliza no disponible en la prefactura'}}
                    @endif
                </td>
                <td>
                    <div class="div_date_format" data-fecha=" {{$data->case_date}}">
                        <div class="two wide column">
                            <span class="date_format_value"></span>
                        </div>
                    </div>
                </td>
                <td>{{$data->case_number}}</td>
                <td>{{$data->service}}</td>
                <td class="service_value_electronic_invoice">
                    {{$formattedValue}}
                    <input class="value_service" type="hidden" value="{{$data->service_value}}">
                </td>
                <td>
                    <input type="hidden" name="electronic_invoice_ids[]" value="{{ $data->id }}">
                    @if($activity->state_id === \App\States\StateMedicalBillsServiceSort::FACTURA_ELECTRONICA_EN_REVISION)
                        <select name="electronic_results[{{ $index }}]" class="ui dropdown">
                            @if($activity->state_id === \App\States\StateMedicalBillsServiceSort::FACTURA_ELECTRONICA_EN_REVISION)
                                <option value="aprobado" {{ $data->result == 'aprobado' ? 'selected' : '' }}>Aprobado
                                </option>
                                <option value="rechazado" {{ $data->result == 'rechazado' ? 'selected' : '' }}>Rechazado
                                </option>
                            @endif
                        </select>
                    @else
                        <div class="ui selection dropdown readonly">
                            <input class="readonly" type="hidden"
                                   value="{{ $data->result == 'aprobado' ? 'aprobado' : 'rechazado' }}" readonly>
                            <i class="dropdown icon"></i>
                            <div class="default text"
                                 style="color: black;">{{ $data->result == 'aprobado' ? 'Aprobado' : 'Rechazado' }}</div>
                            <div class="menu"></div>
                        </div>
                    @endif

                </td>
                <td>
                    @if($activity->state_id === \App\States\StateMedicalBillsServiceSort::FACTURA_ELECTRONICA_EN_REVISION)
                        <input type="text" name="rejection_reason[{{ $index }}]" value="" class="readonly rejection_reason" readonly>
                    @else
                        <input type="text" class="readonly" value="{{$data->reason}}" readonly>
                    @endif
                </td>
            </tr>
        @endforeach
        </tbody>
        <tfoot>
        <tr>
            <th colspan="6">Total factura electrónica</th>
            <th id="totalServiceValueElectronicInovice">0</th>
            <th colspan="2"></th>
        </tr>
        </tfoot>
    </table>

    @php
        $currencyOtherInputs = $TYPE_CURRENCY[$medical_bills_service->type_currency] ?? '';
        $formatted_total_value_invoice = $currencyOtherInputs . number_format($medical_bills_service->total_value_invoice ?? 0, 2, ',', '.');
        $formatted_paid_amount = $currencyOtherInputs . number_format($medical_bills_service->paid_amount ?? 0, 2, ',', '.');
        $formatted_total_value_tax= $currencyOtherInputs . number_format($medical_bills_service->total_value_tax ?? 0, 2, ',', '.');
    @endphp
    <div class="three fields">
        <div class="field">
            <label># interno de radicación</label>
            <input type="text" name="number_electronic_invoice" class="readonly"
                   value="{{$medical_bills_service->id}}" readonly>
        </div>
        <div class="field">
            <label>Valor factura</label>
            <input type="text" class="readonly"
                   value="{{$formatted_total_value_invoice}}" readonly>
            <input type="hidden" name="total_value_invoice"
                   value="{{$medical_bills_service->total_value_invoice}}">
        </div>
        <div class="field">
            <label>Valor pagado</label>
            <input type="text" class="readonly"
                   value="{{$formatted_paid_amount}}" readonly>
            <input type="hidden" name="paid_amount"
                   value="{{$medical_bills_service->paid_amount}}">
        </div>
    </div>

    <div class="three fields">
        <div class="field">
            <label>Retención en la fuente</label>
            <input type="text" class="readonly account-number"
                   value="{{$formatted_total_value_tax}}" readonly>
            <input type="hidden" name="account-number" class="account-number-hidden"
                   value="{{$medical_bills_service->total_value_tax}}">
        </div>
        <div class="field">
            <label>Estado</label>
            <input type="text" class="medical-bills-input-to-lower-case readonly"
                   value="{{$state_medical_bills}}" readonly>
        </div>
        <div class="field"></div>
    </div>

    @if($activity->state_id == \App\States\StateMedicalBillsServiceSort::FACTURA_ELECTRONICA_EN_REVISION)
        <div class="field required" style="margin-top: 15px;">
            <label>Resultado de factura electrónica</label>
            <div class="ui selection dropdown" id="resultElectronicInvoiceDropdown">
                <input type="hidden" name="result_electronic_invoice" id="result_electronic_invoice"
                       value="">
                <i class="dropdown icon"></i>
                <div class="default text">Seleccionar resultado</div>
                <div class="menu">
                    @if($activity->state_id === \App\States\StateMedicalBillsServiceSort::FACTURA_ELECTRONICA_EN_REVISION)
                        <div class="item" data-value="approve-electronic-invoice">Aprobar pago factura electrónica</div>
                        <div class="item" data-value="return-electronic-invoice">Devolver factura electrónica</div>
                    @endif
                </div>
            </div>
        </div>
    @else
        <div class="field" style="margin-top: 15px;">
            <label>Resultado de factura electrónica</label>
            <div class="ui selection dropdown readonly" id="resultElectronicInvoiceDropdown">
                <input type="hidden" name="result_electronic_invoice" id="result_electronic_invoice"
                       value="{{ $medical_bills_service->result_electronic_invoice }}">
                <i class="dropdown icon"></i>
                <div class="default text" style="color: black;">
                    @switch($medical_bills_service->result_electronic_invoice)
                        @case('approve-electronic-invoice')
                            Aprobada
                            @break
                        @case('return-electronic-invoice')
                            Devuelta
                            @break
                        @default
                            N/A
                    @endswitch
                </div>
                <div class="menu">
                </div>
            </div>
        </div>
    @endif


    <div class="field">
        <label>Descripción de factura electrónica</label>
        <textarea class="readonly" rows="3"
                  readonly>{{ $medical_bills_service->description_electronic_invoice }}</textarea>
    </div>

    <div class="field {{ $activity->state_id == \App\States\StateMedicalBillsServiceSort::FACTURA_ELECTRONICA_EN_REVISION ? 'required' : '' }}">
        <label>Gestión auditoria</label>
        <textarea rows="3" name="audit_management" id="audit_management"
                  class="{{ $activity->state_id != \App\States\StateMedicalBillsServiceSort::FACTURA_ELECTRONICA_EN_REVISION ? 'readonly' : ''}}"
                      {{ $activity->state_id != \App\States\StateMedicalBillsServiceSort::FACTURA_ELECTRONICA_EN_REVISION ? 'readonly' : ''}}>{{ $medical_bills_service->audit_management }}</textarea>
    </div>

    <div class="field">
        <div id="return_reason_field" style="display: none;">
            <div class="field" id="return_reason_control_field">
                <label>Motivo para devolución</label>
                <textarea rows="3" name="return_reason_electronic_invoice" id="return_reason_electronic_invoice"
                          class="{{ $activity->state_id != \App\States\StateMedicalBillsServiceSort::FACTURA_ELECTRONICA_EN_REVISION ? 'readonly' : ''}}"
                      {{ $activity->state_id != \App\States\StateMedicalBillsServiceSort::FACTURA_ELECTRONICA_EN_REVISION ? 'readonly' : ''}}>{{$medical_bills_service->return_reason_electronic_invoice}}</textarea>
            </div>
        </div>
    </div>

</div>

<script>
    // Pasar type_currency desde Blade a JavaScript
    const TYPE_CURRENCY = @json($TYPE_CURRENCY[$medical_bills_service->type_currency] ?? '');

    function calculateTotalElectronicInvoice() {
        let total = 0;

        $('.electronic_invoice_dinamic_table tbody tr').each(function () {
            // Detectar si el resultado está en un <select> o en un input hidden
            let select = $(this).find('select');
            let hiddenInput = $(this).find('input.readonly');

            // Obtener el valor del resultado según el elemento disponible
            let result = select.length ? select.val() : hiddenInput.val();

            const tdServiceValue = $(this).find('.service_value_electronic_invoice');
            const serviceValue = parseFloat($(tdServiceValue).find('.value_service').val()) || 0; // Evitar NaN

            // Solo sumar si el resultado es 'aprobado'
            if (result === 'aprobado') {
                total += serviceValue;
            }
        });

        // Convertir el total a formato moneda
        const formattedTotal = total.toLocaleString('es-ES', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
            useGrouping: true
        });

        // Mostrar el total con la moneda
        $('#totalServiceValueElectronicInovice').text(TYPE_CURRENCY + formattedTotal);
    }
    function electronicInvoiceApprovedButton() {
        let redFlag = true;

        $('#electronicInvoiceTable select[name^="electronic_results"]').each(function() {
            let selectedValue = $(this).val();

            if (selectedValue !== 'aprobado') {
                redFlag = false;
                return false;
            }
        });

        return redFlag;
    }

    function showButton(showButtonApproved) {
        if (showButtonApproved) {
            // Mostrar "Aprobar pago factura electrónica" y ocultar "Devolver factura electrónica"
            $('#resultElectronicInvoiceDropdown .item[data-value="approve-electronic-invoice"]').show();
            $('#resultElectronicInvoiceDropdown .item[data-value="return-electronic-invoice"]').hide();

            // Asigna "Aprobar pago factura electrónica" como valor por defecto
            $('#resultElectronicInvoiceDropdown').dropdown('set selected', 'approve-electronic-invoice');
            $('#result_electronic_invoice').val('approve-electronic-invoice');

        } else {
            // Ocultar "Aprobar pago factura electrónica" y mostrar "Devolver factura electrónica"
            $('#resultElectronicInvoiceDropdown .item[data-value="approve-electronic-invoice"]').hide();
            $('#resultElectronicInvoiceDropdown .item[data-value="return-electronic-invoice"]').show();

            // Asigna "Devolver factura electrónica" como valor por defecto
            $('#resultElectronicInvoiceDropdown').dropdown('set selected', 'return-electronic-invoice');
            $('#result_electronic_invoice').val('return-electronic-invoice');
        }
    }

    $(document).ready(function () {
        calculateTotalElectronicInvoice();

        let showButtonApproved = electronicInvoiceApprovedButton();

        showButton(showButtonApproved);

        // Escuchar cambios en el select de resultados
        $('select[name^="electronic_results"]').on('change', function () {
            calculateTotalElectronicInvoice(); // Volver a calcular el total cuando cambie el resultado

            showButtonApproved = electronicInvoiceApprovedButton();

            showButton(showButtonApproved);

            //habilitar o deshabilitar input de motivo de rechazo (disabled)
            // Obtener el valor seleccionado
            let selectedValue = $(this).val();
            let rejectionInput = $(this).closest('tr').find('.rejection_reason'); // Selecciona el input de rechazo en la misma fila

            // Habilitar o deshabilitar el input según el valor seleccionado
            if (selectedValue === 'rechazado' || selectedValue === 'deducted_value') {
                rejectionInput.prop('readonly', false); // Habilitar
                rejectionInput.removeClass('readonly');


            } else {
                rejectionInput.prop('readonly', true); // Deshabilitar
                rejectionInput.addClass('readonly');
                rejectionInput.val(''); // Limpiar el campo cuando se deshabilita
            }
        });
    });
</script>

<script>
    $(document).ready(function () {
        // Comprobar el valor del input hidden al cargar la página
        let selectedValue = $('#result_electronic_invoice').val();

        let visibleRequired = '{{ $activity->state_id == \App\States\StateMedicalBillsServiceSort::FACTURA_ELECTRONICA_EN_REVISION ? 'required' : '' }}"'
        if (selectedValue === 'return-electronic-invoice' && visibleRequired === 'required') {
            $('#return_reason_field').css('display', 'block');
            $('#return_reason_control_field').addClass('required');
        } else {
            $('#return_reason_field').css('display', 'none'); // Asegúrate de ocultarlo si no es 'rechazado'
            $('#return_reason_control_field').removeClass('required');
        }
    });
</script>

<script>
    $('#resultElectronicInvoiceDropdown').on('change', function () {
        let selectedValue = $("#result_electronic_invoice").val();

        // Mostrar/ocultar el campo de motivo de devolución según el valor seleccionado
        if (selectedValue === 'return-electronic-invoice') {
            $('#return_reason_field').css('display', 'block');
            $("#return_reason_electronic_invoice").val('');
            $('#return_reason_control_field').addClass('required');
        } else {
            $('#return_reason_field').css('display', 'none'); // Ocultar si es 'aprobado'
            $('#return_reason_control_field').removeClass('required');
        }
    });
</script>

<script>
    document.addEventListener("DOMContentLoaded", function () {
        // Establecer el idioma a español
        moment.locale('es');

        // Seleccionar todos los elementos con clase "actividad"
        const div_date_format = document.querySelectorAll('.div_date_format');

        div_date_format.forEach(function (date_format) {
            // Obtener la fecha desde el atributo data-fecha
            let date = date_format.getAttribute('data-fecha');

            // Formatear la fecha con Moment.js
            let fechaFormateada = moment(date).format('dddd D [de] MMMM [de] YYYY');

            // Capitalizar la primera letra de la fecha formateada
            fechaFormateada = fechaFormateada.charAt(0).toUpperCase() + fechaFormateada.slice(1);

            // Usar querySelector para seleccionar el primer elemento con clase "fecha-creacion"
            date_format.querySelector('.date_format_value').innerText = `${fechaFormateada}`;
        });
    });
</script>


