<!DOCTYPE html>
<html lang="es">
<head>
    <meta content="text/html; charset=utf-8" http-equiv="Content-Type"/>
    <style type="text/css">
        * {
            font-family: 'Arial', sans-serif;
            font-size: 10pt;
        }

        body {
            margin: 0 2rem 3rem 2rem;
            padding-top: 40px;
            text-align: justify;
        }

        .watermark {
            position: fixed;
            top: 45%;
            width: 100%;
            text-align: center;
            opacity: .1;
            transform: rotate(-45deg);
            transform-origin: 50% 50%;
            z-index: 1000;
        }

        .footer {
            height: 40px;
            width: 100%;
            position: fixed;
            z-index: 0 !important;
            left: 26px;
            right: 26px;
            top: 87%;
            border-top: 0.5px solid black;
        }

        .header {
            height: 40px;
            width: 100%;
            position: fixed;
            z-index: 0 !important;
        }

        .header img {
            height: 57px;
            width: 752px;
            position: absolute;
            left: -76px;
            top: -8px;
            opacity: 0.5;
        }

        .header_radicate {
            position: fixed;
            top: 40px;
            right: 26px;
            font-size: 10pt;
        }

        .footer img {
            min-height: 50px;
            max-height: 50px;
            width: auto;
            text-align: left;
        }

        table,
        th,
        td {
            border-collapse: collapse;
            border: 0.5px solid black;
        }

        table td th {
            text-align: center;
        }

        table,
        th,
        td {
            border: 0.5px solid black;
        }

        th,
        td {
            padding: 1.5px;
            padding-bottom: 3px;
        }

        td[colspan="16"] {
            text-align: justify;
        }

        table {
            border-collapse: collapse;
            width: 100%;
        }

        th {
            text-align: center;
            background: lightgray;
            color: black;
            font-family: 'Century-Bold', sans-serif !important;
        }

        .info {
            padding-top: 40px;
        }

    </style>
</head>
@php
    $date_action_file = null;
    $bizagi_radicate = null;
    foreach($activity->activity_actions as $aa){
        if ($aa->action_id == 539) {
            foreach ($aa->fields as $field){
                if($field->action_field_id == 319){
                    $bizagi_radicate = $field->value;
                }
                if($field->action_field_id == 320){
                    $date_action_file = $field->value;
                }
            }
            break;
        }
    }
    setlocale(LC_TIME, 'Spanish');

    $formattedDate = $date_action_file ?  \Carbon\Carbon::createFromFormat('Y-m-d', $date_action_file)->formatLocalized('%d de %B de %Y') : '';
@endphp
<body>
@if($watermark)
    <div class="watermark">
        <span style="font-size: 72pt;font-weight: 500;">VISTA PREVIA</span>
    </div>
@endif
<div class="header">
    <img src="{{storage_path('app/header_mdi.jpeg')}}" alt="Logo Colpensiones">
</div>
<div class="header_radicate">
    <p style="text-align: right">Nro Radicado: {{ $bizagi_radicate }}</p>
</div>
<div class="footer">
    <table style="width: 100%">
        <tr>
            <td colspan="10" style="border: transparent;text-align: justify;opacity: 0.5;">
                <b>Colpensiones</b><br>
                Dirección: Carrera 10 No.72 – 33 Torre B Piso 11, Bogotá D.C., Colombia<br>
                Conmutador: (+57) 601 489 0909; Línea Gratuita: 01 8000 410909<br>
                www.colpensiones.gov.co
            </td>
            <script type="text/php">
                if (isset($pdf)) {
                    $x = $pdf->get_width() - 98;
                    $y= 770;
                    $text = "Página | {PAGE_NUM}";
                    $font = $fontMetrics->get_font("Helvetica", "normal");
                    $size = 9;
                    $color = array(0.430,0.430,0.430);
                    $word_space = 0.0;  //  default
                    $char_space = 0.0;  //  default
                    $angle = 0.0;   //  default
                    $pdf->page_text($x, $y, $text, $font, $size, $color, $word_space, $char_space, $angle);
                }
            </script>
        </tr>
    </table>
</div>

<div class="info">
    <p>
        Bogotá D.C, {{$formattedDate}}
    </p>
    @if($activity->pcl->beneficiaries_qualification == 'SI')
        <p>
            Señor(a): <br>
            <b>{{$activity->pcl->beneficiaries_full_name}}</b> <br>
            {{$activity->pcl->beneficiaries_address}} <br>
            Teléfono: {{$activity->pcl->beneficiaries_phone}} - {{$activity->pcl->beneficiaries_cellphone}} <br>
            Correo: {{$activity->pcl->beneficiaries_email}} <br>
            {{$activity->pcl->beneficiary_city}}
        </p>
    @else
        <p>
            Señor(a): <br>
            <b>{{$activity->pcl->full_name_affiliate}} </b><br>
            {{$activity->affiliate->address}} <br>
            Teléfono: {{$activity->affiliate->phone}} - {{$activity->affiliate->cellphone}} <br>
            Correo: {{$activity->pcl->email}} <br>
            {{$activity->pcl->city}}
        </p>
    @endif
    <p>
        <span><b>Referencia</b>:</span> <span
            style="margin-left: 58px">Radicado No. {{$activity->id_bizagi}} del {{ \Carbon\Carbon::createFromFormat('Y-m-d', $activity->pcl->radication_date)->formatLocalized('%d de %B de %Y')}}</span>
        <br>
        <span><b>Afiliado</b></span><span style="margin-left: 85px">{{$activity->affiliate->fullname}}</span><br>
        <span><b>Identificación</b></span><span
                style="margin-left: 48px">{{$activity->affiliate->doc_type}} {{$activity->affiliate->doc_number}}</span><br>
        <span><b>Tipo de Trámite</b></span><span style="margin-left: 35px">Medicina laboral Calificación de pérdida de capacidad laboral/ Ocupacional </span><br>
    </p>
    <p>
        Respetado(a) Señor(a):
    </p>
    <p>
        Reciba un cordial saludo de la Administradora Colombiana de Pensiones - COLPENSIONES.
    </p>
    <p>
        En atención al trámite de Calificación de pérdida de capacidad laboral/ Ocupacional iniciado por Usted, nos
        permitimos informarle que, una vez efectuada la revisión documental, se requiere complementar su historia
        clínica con exámenes adicionales.
    </p>
    <p>
        Dichos exámenes corresponden a los solicitados en el documento anexo.
    </p>
    <p>
        Los exámenes solicitados los debe radicar dentro los 30 días siguientes al recibo de la presente comunicación,
        en caso de que dentro de este tiempo no logre reunir la documentación en dicho término usted puede solicitar
        ante esta entidad una prórroga la cual se otorgará por el mismo plazo inicial. Es importante advertir que en el
        evento en que lo solicitado no sea allegado en el plazo previsto, Colpensiones dará cierre al trámite por
        desistimiento tácito todo lo anterior conforme al artículo 17 de la ley 1755 de 2015.
    </p>
    <p>
        Para allegar la documentación requerida usted cuenta con canales presenciales Punto de Atención Colpensiones
        (PAC) o con la página web de Colpensiones, tramites en línea tramites web recepción de documentos de medicina
        laboral en el siguiente link:
    </p>
    <p>
        <a href="https://www.colpensionestransaccional.gov.co/sede_electronica/tramitesweb/Paginas/RadicarColp/RadicarColpWebForm.aspx?tipoDoc=JnRLeB1FWBS5DEIFqSo+yw==&tipoCiu=ds3Zhg0zfGohUAr90YQZjw">www.colpensionestransaccional.gov.co</a>
    </p>
    <p>
        En caso de requerir información adicional, por favor acercarse a nuestros Puntos de Atención Colpensiones (PAC);
        comunicarse con la línea de servicio al ciudadano en Bogotá al 4890909, en Medellín al 2836090, o con la línea
        gratuita nacional al 018000 41 0909, en donde estaremos dispuestos a brindarle el mejor servicio.
    </p>
    <p>
        Agradecemos su confianza recordándole que estamos para servirle.
    </p>
    <p>
        Cordialmente,
    </p>
    @php
        $author = null;
          foreach ($activity->activity_actions as $aa) {
              if ($aa->action_id == 487) {
                  $author = strtoupper($pcl->authorName($aa->author_id));
              } elseif ($aa->action_id == 538) {
                  $author = strtoupper($pcl->authorName($aa->author_id));
              } elseif ($aa->action_id == 539) {
                  $author = strtoupper($pcl->authorName($aa->author_id));
              }
          }
    @endphp
    <p style="padding-top: 20px">
        @unless($watermark)
            <img style="height: 50px; width: auto;" alt="FIRMA" src="{{storage_path('app/firma_lmlozano.jpg')}}"/>
            <br/>
        @endunless
        <b>LUZ MARYEN LOZANO ROSAS</b><br/>
        Directora de Medicina Laboral <br/>
        Elaboró: {{$author ? $author : ''}}

    </p>
</div>
<div class="info" style="text-align: center; page-break-before: always">
    <p><b>SOLICITUD DE EXAMENES COMPLEMENTARIOS</b></p>
    <p style="text-align: justify">
        Para dar continuar su proceso de Calificación de Pérdida de Capacidad, y de acuerdo con lo establecido en el
        artículo 142 Decreto Ley 019 de 2012 y en el Decreto 1507 de 2014 es necesario complementar su Historia Clínica
        con los siguientes:
    </p>
    <table>
        <tbody>
        <tr>
            <td colspan="8"><b>SOLICITUD DE DOCUMENTOS </b></td>
            <td colspan="8"><b>DESCRIPCION DE SOLICITUD</b></td>
        </tr>
        @foreach ($activity->pcl->preliminary_opinions as $preliminary_opinions)
            <tr>
                <td colspan="8">{{$preliminary_opinions->document_preliminary_opinion}}</td>
                <td colspan="8" class="td_justify">
                    @foreach(explode("\n",$preliminary_opinions->document_preliminary_opinion_observation) as $text)
                        {{$text}}
                </td>
            </tr>
            <tr>
                <td colspan="8" class="td_justify" style="border-bottom: transparent"></td>
                <td colspan="8" class="td_justify" style="border-bottom: transparent">
                    @endforeach
                </td>
            </tr>
        @endforeach
        </tbody>
    </table>
    <p style="text-align: justify">
        Resulta pertinente informarle que cuenta con un término de treinta (30) días para allegar los documentos y
        exámenes anteriormente descritos, contados a partir del recibido de la presente.
    </p>
</div>
</body>