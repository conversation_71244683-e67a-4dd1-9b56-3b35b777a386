@extends('layouts.main')

@section('title', 'Determinación de la Pérdida de Capacidad Laboral/Ocupacional')

@section('menu')
    @parent
@endsection

@section('content')
    @php
        $data = [
            'department' => '',
            'municipality' => ''
        ];

        if ($activity->pcl) {
            $data = App::make('App\Http\Controllers\Services\PCLController')->getDepartmentAndMunicipalityByCode($activity->pcl ? $activity->pcl->radication_municipality : 0);
        }
    @endphp
    <div class="ui basic segment">
        <h1 class="ui header">
            Determinación de la Pérdida de Capacidad Laboral/Ocupacional
            <div class="sub header">Campos con <span style="color: red;" class="required">*</span> obligatorios.</div>
        </h1>
        <form autocomplete="off" action="{{secure_url("servicio/{$activity->id}/pcl/save")}}" id="dictamen"
              method="post" class="ui small form">
            <div class="ui secondary segment">
                <div class="ui four columns grid">
                    <div class="column">
                        <b>Identificación:</b> {{$activity->affiliate->doc_type}} {{$activity->affiliate->doc_number}}
                    </div>
                    <div class="column"><b>Nombre:</b> <a
                                href="{{secure_url('afiliado/' . $activity->affiliate_id)}}">{{$activity->affiliate->full_name}}</a>
                    </div>
                    <div class="column"><b>Actividad:</b> <a
                                href="{{secure_url('servicio/' . $activity->id)}}">{{$activity->service->name}}</a>
                    </div>
                    <div class="column"><b>Nro. Dictamen:</b> {{$activity->pcl_id}}</div>
                    <div class="column"><b>Fecha solicitud:</b> {{$activity->created_at->formatLocalized('%B %d, %Y')}}
                    </div>
                    <div class="column"><b>Ciudad de Radicación:</b> {{ $data['municipality_name'] }}
                    </div>
                    @if ($activity->pcl && $activity->pcl->beneficiaries_qualification)
                        @if($activity->pcl->beneficiaries_qualification == 'SI')
                            <div class="column"><b>Calificado:</b> BENEFICIARIO
                            </div>
                        @else
                            <div class="column"><b>Calificado:</b> AFILIADO
                            </div>
                        @endif
                    @else
                        <div class="column"><b>Calificado:</b> N/A
                        </div>
                    @endif
                    @if ($existsBeginServiceNotify)
                        <div class="column">
                            <a href="{{secure_url('servicio/' . $beginServiceNotify->id)}}">{{$beginServiceNotify->service->name}}
                                - {{$beginServiceNotify->id}}
                            </a>
                        </div>
                    @else
                        <div class="column">
                            <b>INICIO DE CALIFICACIÓN (NOTI): </b> N/A
                        </div>
                    @endif
                    @if ($existsDictumServiceNotify)
                        <div class="column">
                            <a href="{{secure_url('servicio/' . $dictumServiceNotify->id)}}">{{$dictumServiceNotify->service->name}}
                                - {{$dictumServiceNotify->id}}
                            </a>
                        </div>
                    @else
                        <div class="column">
                            <b>DICTAMEN DE PCL (NOTI): </b> N/A
                        </div>
                    @endif
                    <div class="column">
                        <b>Radicado Bizagi: </b>{{$activity->id_bizagi}}
                    </div>
                </div>
            </div>
            <div class="ui styled fluid accordion">
                <div id="132" class="title"><i class="dropdown icon"></i> Marcas de Administrador</div>
                <div class="content">
                    <div style="margin-bottom: 5px;" class="fields">
                        <div class="three wide field">
                            <label>Marca</label>
                        </div>
                        <div class="twelve wide field">
                            <label>Observación</label>
                        </div>
                        <div class="one wide field">
                            <a style="margin-top: -15px;" onclick="addAdminMark()"
                               class="ui basic small icon blue button"><i class="add icon"></i></a>
                        </div>
                    </div>
                    <div id="admin_mark">
                        @if ($activity->pcl && count($activity->pcl->admin_marks) > 0)
                            @foreach ($activity->pcl->admin_marks as $adm_mark)
                                <div class="fields">
                                    <input type="hidden" name="admin_mark[id][]"
                                           value="{{$adm_mark->id}}">
                                    <div class="three wide required field">
                                        <div class="ui selection dropdown">
                                            <input type="hidden" name="admin_mark[admin_mark][]" value="{{$adm_mark->admin_mark}}">
                                            <i class="dropdown icon"></i>
                                            <div class="default text">Seleccione uno</div>
                                            <div class="menu">
                                                <div class="item" data-value="TUTELA">TUTELA</div>
                                                <div class="item" data-value="ENTES DE CONTROL">ENTES DE CONTROL</div>
                                                <div class="item" data-value="CASO GAP">CASO GAP</div>
                                                <div class="item" data-value="PQR">PQR</div>
                                                <div class="item" data-value="GERENCIA PREVENCIION DEL FRAUDE">GERENCIA PREVENCIÓN DEL FRAUDE</div>
                                                <div class="item" data-value="ACLARATORIA">ACLARATORIA</div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="twelve wide required field">
                                        <textarea name="admin_mark[admin_mark_observation][]" rows="2">{{$adm_mark->admin_mark_observation}}</textarea>
                                    </div>
                                    <div class="one wide field">
                                        <a class="ui red small icon basic button"><i
                                                    class="remove icon"></i></a>
                                    </div>
                                </div>
                            @endforeach
                        @else
                            <div class="fields">
                                <input type="hidden" name="admin_mark[id][]">
                                <div class="three wide required field">
                                    <div class="ui selection dropdown">
                                        <input type="hidden" name="admin_mark[admin_mark][]">
                                        <i class="dropdown icon"></i>
                                        <div class="default text">Seleccione uno</div>
                                        <div class="menu">
                                            <div class="item" data-value="TUTELA">TUTELA</div>
                                            <div class="item" data-value="ENTES DE CONTROL">ENTES DE CONTROL</div>
                                            <div class="item" data-value="CASO GAP">CASO GAP</div>
                                            <div class="item" data-value="PQR">PQR</div>
                                            <div class="item" data-value="GERENCIA PREVENCIION DEL FRAUDE">GERENCIA PREVENCIÓN DEL FRAUDE</div>
                                            <div class="item" data-value="ACLARATORIA">ACLARATORIA</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="twelve wide required field">
                                    <textarea name="admin_mark[admin_mark_observation][]" rows="2"></textarea>
                                </div>
                                <div class="one wide field"></div>
                            </div>
                        @endif
                    </div>
                </div>
                @if($activity->pcl->historical_close)
                    <div class="title"><i class="dropdown icon"></i> Historico cerrado
                    </div>
                    <div class="content">
                        <div class="three fields">
                            <div class="required field">
                                <label>Radicado Bizagi</label>
                                <input class="text" style="color: #C3C3C3;" readonly
                                       value="{{ $activity->pcl && $activity->pcl->historical_close ? $activity->pcl->historical_close->radicado_bizagi : '' }}">
                            </div>
                            <div class="required field">
                                <label>Número de dictamen</label>
                                <input class="text" style="color: #C3C3C3;" readonly
                                       value="{{ $activity->pcl && $activity->pcl->historical_close ? $activity->pcl->historical_close->dictum_number : '' }}">
                            </div>
                            <div class="required field">
                                <label>Fecha de dictamen</label>
                                <input class="text" style="color: #C3C3C3;" readonly
                                       value="{{ $activity->pcl && $activity->pcl->historical_close ? $activity->pcl->historical_close->dictum_date : '' }}">
                            </div>
                        </div>
                        <div class="three fields">
                            <div class="required field">
                                <label>Calificación pcl</label>
                                <input class="text" style="color: #C3C3C3;" readonly
                                       value="{{ $activity->pcl && $activity->pcl->historical_close ? $activity->pcl->historical_close->pcl_percentage : '' }}">
                            </div>
                            <div class="required field">
                                <label>Fecha estructuración</label>
                                <input class="text" style="color: #C3C3C3;" readonly
                                       value="{{ $activity->pcl && $activity->pcl->historical_close ? $activity->pcl->historical_close->structure_date : '' }}">
                            </div>
                            <div class="required field">
                                <label>Origen PCL</label>
                                <input class="text" style="color: #C3C3C3;" readonly
                                       value="{{ $activity->pcl && $activity->pcl->historical_close ? $activity->pcl->historical_close->origin : '' }}">
                            </div>
                        </div>
                    </div>
                @endif
                <div id="73" class="title"><i class="dropdown icon"></i> Información general
                </div>
                <div class="content">
                    <div class="styled fluid accordion">
                        <div class="title"><i class="dropdown icon"></i>
                            Datos del Afiliado
                        </div>
                        <div class="content">
                            <div class="four fields">
                                <div class="required field">
                                    <label>Tipo identificación</label>
                                    <div class="ui selection dropdown">
                                        <input type="hidden" name="doc_type_affiliate"
                                               value="{{$activity->pcl && $activity->pcl->doc_type_affiliate ? $activity->pcl->doc_type_affiliate : $activity->affiliate->doc_type}}">
                                        <i class="dropdown icon"></i>
                                        <div class="default text">Tipo de documento</div>
                                        <div class="menu">
                                            @foreach($DOC_TYPES as $k => $v)
                                                <div class="item" data-value="{{$k}}">{{$v}}</div>
                                            @endforeach
                                        </div>
                                    </div>
                                </div>
                                <div class="required field">
                                    <label style="color: {{$activity->creation_source == 'INTEGRACIÓN' ? "green" : "black"}}">Número
                                        de identificación</label>
                                    <input name="doc_number_affiliate" class="text"
                                           value="{{ $activity->pcl && $activity->pcl->doc_number_affiliate ? $activity->pcl->doc_number_affiliate : $activity->affiliate->doc_number }}">
                                </div>
                                <div class="required field">
                                    <label style="color: {{$activity->creation_source == 'INTEGRACIÓN' ? "green" : "black"}}">Nombres
                                        y Apellidos</label>
                                    <input name="full_name_affiliate" class="text"
                                           value="{{$activity->pcl && $activity->pcl->full_name_affiliate ? $activity->pcl->full_name_affiliate : $activity->affiliate->full_name}}">
                                </div>
                                <div class="required field">
                                    <label>Fecha de nacimiento</label>
                                    <input type="text" name="birthday" class="datepicker"
                                           value="{{$activity->pcl && $activity->pcl->birthday ? $activity->pcl->birthday : $activity->affiliate->birthday }}">
                                </div>
                            </div>
                            <div class="four fields">
                                <div class="required field">
                                    <label>Edad</label>
                                    <input name="age" readonly style="color: #C3C3C3;"
                                           value="{{$activity->pcl && $activity->pcl->age ? $activity->pcl->ageAffiliate() : $activity->affiliate->age()}}">
                                </div>
                                <div class="required field">
                                    <label>Sexo</label>
                                    <div class="ui selection dropdown">
                                        <input type="hidden" name="gender"
                                               value="{{$activity->pcl && $activity->pcl->gender ? $activity->pcl->gender : $activity->affiliate->gender}}">
                                        <i class="dropdown icon"></i>
                                        <div class="default text">Seleccion uno</div>
                                        <div class="menu">
                                            @foreach($GENDERS as $k => $v)
                                                <div class="item" data-value="{{$k}}">{{$v}}</div>
                                            @endforeach
                                        </div>
                                    </div>
                                </div>
                                <div class="required field">
                                    <label>Distrito/regional</label>
                                    <div class="ui selection dropdown" >
                                        <input type="hidden" name="regional"
                                               value="{{$activity->pcl && $activity->pcl->regional ? $activity->pcl->regional : $activity->affiliate->regional}}">
                                        <i class="dropdown icon"></i>
                                        <div class="default text">Seleccion uno</div>
                                        <div class="menu">
                                            @foreach($REGIONAL as $k => $v)
                                                <div class="item" data-value="{{$k}}">{{$v}}</div>
                                            @endforeach
                                        </div>
                                    </div>
                                </div>
                                <div class="field">
                                    <label>Estado civil</label>
                                    <div class="ui selection dropdown">
                                        <input type="hidden" name="civil_status"
                                               value="{{$activity->pcl && $activity->pcl->civil_status ? $activity->pcl->civil_status : $activity->affiliate->civil_status}}">
                                        <i class="dropdown icon"></i>
                                        <div class="default text">Seleccion uno</div>
                                        <div class="menu">
                                            @foreach($CIVIL_STATUS as $k => $v)
                                                <div class="item" data-value="{{$k}}">{{$v}}</div>
                                            @endforeach
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="four fields">
                                <div class="field">
                                    <label>Nivel Escolaridad</label>
                                    <div class="ui selection dropdown">
                                        <input type="hidden" name="school_level"
                                               value="{{$activity->pcl && $activity->pcl->school_level ? $activity->pcl->school_level : $activity->affiliate->school_level}}">
                                        <i class="dropdown icon"></i>
                                        <div class="default text">Seleccion uno</div>
                                        <div class="menu">
                                            @foreach($SCHOOL_LEVELS as $k => $v)
                                                <div class="item" data-value="{{$k}}">{{$v}}</div>
                                            @endforeach
                                        </div>
                                    </div>
                                </div>
                                <div class="field">
                                    <label>Correo Electrónico</label>
                                    <input class="text" name="email"
                                           value="{{ $activity->pcl ? $activity->pcl->email : $activity->affiliate->email }}">
                                </div>
                                <div class="required field">
                                    <label>EPS</label>
                                    <div class="ui selection dropdown">
                                        <input type="hidden" name="eps"
                                               value="{{$activity->pcl && $activity->pcl->eps ? $activity->pcl->eps : $activity->affiliate->eps}}">
                                        <i class="dropdown icon"></i>
                                        <div class="default text">Seleccion uno</div>
                                        <div class="menu">
                                            @foreach($EPS_LIST as $k => $v)
                                                <div class="item" data-value="{{$k}}">{{$v}}</div>
                                            @endforeach
                                        </div>
                                    </div>
                                </div>
                                <div class="field">
                                    <label>Teléfono</label>
                                    <input class="number phone-input" name="phone"
                                           value="{{ $activity->pcl && $activity->pcl->phone ? $activity->pcl->phone : $activity->affiliate->phone }}">
                                </div>
                            </div>
                            <div class="four fields">
                                <div class="field">
                                    <label style="color: {{$activity->creation_source == 'INTEGRACIÓN' ? "green" : "black"}}">Departamento
                                        de residencia</label>
                                    <div id="departments"
                                         class="ui selection dropdown" >
                                        <input type="hidden" name="department"
                                               value="{{$activity->pcl && $activity->pcl->department  ? $activity->pcl->department : $activity->affiliate->department}}">
                                        <i class="dropdown icon"></i>
                                        <div class="default text">Seleccione uno</div>
                                        <div class="menu"></div>
                                    </div>
                                </div>
                                <div class="field">
                                    <label style="color: {{$activity->creation_source == 'INTEGRACIÓN' ? "green" : "black"}}">Ciudad
                                        de residencia</label>
                                    <div id="municipalities"
                                         class="ui selection dropdown" >
                                        <input type="hidden" name="municipality"
                                               value="{{$activity->pcl && $activity->pcl->municipality ? $activity->pcl->municipality : $activity->affiliate->municipality}}">
                                        <i class="dropdown icon"></i>
                                        <div class="default text">Seleccione uno</div>
                                        <div class="menu"></div>
                                    </div>
                                </div>
                                <div class="field">
                                    <label style="color: {{$activity->creation_source == 'INTEGRACIÓN' ? "green" : "black"}}">Dirección
                                        de residencia</label>
                                    <input class="text"
                                           name="address"
                                           value="{{$activity->pcl && $activity->pcl->address ? $activity->pcl->address : $activity->affiliate->address}}">
                                </div>
                                <div class="field">
                                    <label>Celular</label>
                                    <input class="number phone-input" name="cellphone"
                                           value="{{ $activity->pcl && $activity->pcl->cellphone ? $activity->pcl->cellphone : $activity->affiliate->cellphone }}">
                                </div>
                            </div>
                        </div>
                        <div class="title"><i class="dropdown icon"></i>
                            Datos de beneficiario
                        </div>
                        <div class="content">
                            <div class="four fields">
                                <div class="required field">
                                    <label>Corresponde a una calificación de beneficiario</label>
                                    <div class="ui selection dropdown">
                                        <input type="hidden" name="beneficiaries_qualification"
                                               id="beneficiaries_qualification"
                                               value="{{$activity->pcl ? $activity->pcl->beneficiaries_qualification : ''}}">
                                        <i class="dropdown icon"></i>
                                        <div class="default text">Selecciones uno</div>
                                        <div class="menu">
                                            <div class="item" data-value="SI">SI</div>
                                            <div class="item" data-value="NO">NO</div>
                                        </div>
                                    </div>
                                </div>
                                @php
                                    $age = null;
                                    if ($activity->pcl && $activity->pcl->beneficiaries_qualification === 'SI') {
                                        $age = $activity->pcl->age();
                                    } elseif ($activity->pcl && $activity->pcl->birthday && $activity->pcl->beneficiaries_qualification === 'NO') {
                                        $age = $activity->pcl->ageAffiliate();
                                    } else {
                                        $age = $activity->affiliate->age();
                                    }
                                @endphp
                                <div class="field beneficiary-data-field">
                                    <label>Tipo identificación</label>
                                    <div class="ui selection dropdown">
                                        <input type="hidden" name="beneficiaries_doc_type"
                                               value="{{$activity->pcl ? $activity->pcl->beneficiaries_doc_type : ''}}">
                                        <i class="dropdown icon"></i>
                                        <div class="default text">Tipo de documento</div>
                                        <div class="menu">
                                            @foreach($DOC_TYPES as $k => $v)
                                                <div class="item" data-value="{{$k}}">{{$v}}</div>
                                            @endforeach
                                        </div>
                                    </div>
                                </div>
                                <div class="field beneficiary-data-field">
                                    <label>Número de identificación</label>
                                    <input type="text" name="beneficiaries_doc_number"
                                           value="{{$activity->pcl ? $activity->pcl->beneficiaries_doc_number : ''}}">
                                </div>
                                <div class="field beneficiary-data-field">
                                    <label>Nombres y Apellidos</label>
                                    <input type="text" name="beneficiaries_full_name"
                                           value="{{$activity->pcl ? $activity->pcl->beneficiaries_full_name : ''}}">
                                </div>
                            </div>
                            <div class="four fields">
                                <div class="field beneficiary-data-field">
                                    <label>Fecha de nacimiento</label>
                                    <input type="text" name="beneficiaries_birthdate" class="datepicker"
                                           value="{{$activity->pcl ? $activity->pcl->beneficiaries_birthdate : ''}}">
                                </div>
                                <div class="field beneficiary-data-field">
                                    <label>Edad</label>
                                    <input type="text" name="beneficiaries_age" readonly
                                           value="{{$activity->pcl && $activity->pcl->beneficiaries_birthdate ? $activity->pcl->age() : ''}}">
                                </div>
                                <div class="field beneficiary-data-field">
                                    <label>Sexo</label>
                                    <div class="ui selection dropdown">
                                        <input type="hidden" name="beneficiaries_gender"
                                               value="{{$activity->pcl ? $activity->pcl->beneficiaries_gender : ''}}">
                                        <i class="dropdown icon"></i>
                                        <div class="default text">Sexo</div>
                                        <div class="menu">
                                            @foreach($GENDERS as $k => $v)
                                                <div class="item" data-value="{{$k}}">{{$v}}</div>
                                            @endforeach
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="four fields">
                                <div class="field">
                                    <label>Nivel Escolaridad</label>
                                    <div class="ui selection dropdown">
                                        <input type="hidden" name="beneficiaries_school_level"
                                               value="{{$activity->pcl && $activity->pcl->beneficiaries_school_level ? $activity->pcl->beneficiaries_school_level : ''}}">
                                        <i class="dropdown icon"></i>
                                        <div class="default text">Seleccion uno</div>
                                        <div class="menu">
                                            @foreach($SCHOOL_LEVELS as $k => $v)
                                                <div class="item" data-value="{{$k}}">{{$v}}</div>
                                            @endforeach
                                        </div>
                                    </div>
                                </div>
                                <div class="field">
                                    <label>Estado civil</label>
                                    <div class="ui selection dropdown">
                                        <input type="hidden" name="beneficiaries_civil_status"
                                               value="{{$activity->pcl && $activity->pcl->beneficiaries_civil_status ? $activity->pcl->beneficiaries_civil_status : ''}}">
                                        <i class="dropdown icon"></i>
                                        <div class="default text">Seleccion uno</div>
                                        <div class="menu">
                                            @foreach($CIVIL_STATUS as $k => $v)
                                                <div class="item" data-value="{{$k}}">{{$v}}</div>
                                            @endforeach
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>
                        <div class="active title beneficiary-data-field"><i class="dropdown icon"></i>
                            Datos contacto Beneficiario
                        </div>
                        <div class="content beneficiary-data-field">
                            <div class="four fields">
                                <div class="field beneficiary-data-field">
                                    <label>Dirección</label>
                                    <input type="text" name="beneficiaries_address"
                                           value="{{$activity->pcl ? $activity->pcl->beneficiaries_address : ''}}">
                                </div>
                                <div class="field beneficiary-data-field">
                                    <label>Teléfono </label>
                                    <input type="text" name="beneficiaries_phone"
                                           value="{{$activity->pcl ? $activity->pcl->beneficiaries_phone : ''}}">
                                </div>
                                <div class="field beneficiary-data-field">
                                    <label>Celular</label>
                                    <input type="text" name="beneficiaries_cellphone"
                                           value="{{$activity->pcl ? $activity->pcl->beneficiaries_cellphone : ''}}">
                                </div>
                                <div class="field beneficiary-data-field">
                                    <label>Correo electronico </label>
                                    <input type="text" name="beneficiaries_email"
                                           value="{{$activity->pcl ? $activity->pcl->beneficiaries_email : ''}}">
                                </div>
                            </div>
                            <div class="four fields">
                                <div class="field beneficiary-data-field">
                                    <label>Departamento</label>
                                    <div id="departments2" class="ui selection dropdown">
                                        <input type="hidden" name="beneficiaries_department"
                                               value="{{$activity->pcl ? $activity->pcl->beneficiaries_department : ''}}">
                                        <i class="dropdown icon"></i>
                                        <div class="default text">Seleccione uno</div>
                                        <div class="menu"></div>
                                    </div>
                                </div>
                                <div class="field beneficiary-data-field">
                                    <label>Municipio</label>
                                    <div id="municipalities2" class="ui selection dropdown">
                                        <input type="hidden" name="beneficiaries_municipality"
                                               value="{{$activity->pcl ? $activity->pcl->beneficiaries_municipality : ''}}">
                                        <i class="dropdown icon"></i>
                                        <div class="default text">Seleccione uno</div>
                                        <div class="menu"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="title"><i class="dropdown icon"></i>
                            Gestión del radicado bizagi
                        </div>
                        <div class="content">
                            <div class="four fields">
                                <div class="field">
                                    <label>Id case</label>
                                    <input type="text" name="id_case"
                                           value="{{$activity->pcl ? $activity->pcl->id_case : ''}}">
                                </div>
                                <div class="required field">
                                    <label>Fecha de radicación</label>
                                    <input type="text" class="datepicker" name="radication_date"
                                           value="{{$activity->pcl ? $activity->pcl->radication_date : ''}}">
                                </div>
                                <div class="field">
                                    <label>No. Radicado Bizagi</label>
                                    <input type="text" name="radication_number"
                                           value="{{$activity->pcl ? $activity->pcl->radication_number : ''}}">
                                </div>
                                <div class="field">
                                    <label>Fecha asignación proveedor</label>
                                    <input type="text" name="assignation_provider_date" class="datepicker"
                                           value="{{$activity->pcl ? $activity->pcl->assignation_provider_date : ''}}">
                                </div>
                            </div>
                            <div class="four fields">
                                <div class="field">
                                    <label>Proveedor asignado - Validación documental</label>
                                    <div class="ui fluid selection dropdown">
                                        <input name="assigned_provider_documental_validation" type="hidden" id="apdv"
                                               value="{{$activity->pcl ? $activity->pcl->assigned_provider_documental_validation : ''}}">
                                        <i class="dropdown icon"></i>
                                        <div class="default text">Seleccione uno</div>
                                        <div class="menu">
                                            <div class="item" data-value="SI">SI</div>
                                            <div class="item" data-value="NO">NO</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="field">
                                    <label>Departamento</label>
                                    <div id="departments3" class="ui selection dropdown">
                                        <input type="hidden" name="radication_department"
                                               value="{{$data['department_code']}}">
                                        <i class="dropdown icon"></i>
                                        <div class="default text">Seleccione uno</div>
                                        <div class="menu"></div>
                                    </div>
                                </div>
                                <div class="field">
                                    <label>Municipio</label>
                                    <div id="municipalities3" class="ui selection dropdown">
                                        <input type="hidden" name="radication_municipality"
                                               value="{{$data['municipality_code']}}">
                                        <i class="dropdown icon"></i>
                                        <div class="default text">Seleccione uno</div>
                                        <div class="menu"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="title"><i class="dropdown icon"></i>
                            Datos tutela o requerimiento jurídico
                        </div>
                        <div class="content">
                            <div class="four fields">
                                <div class="field">
                                    <label>Tutela</label>
                                    <div class="ui fluid selection dropdown">
                                        <input name="tutelage" type="hidden"
                                               value="{{$activity->pcl ? $activity->pcl->tutelage : ''}}">
                                        <i class="dropdown icon"></i>
                                        <div class="default text">Seleccione uno</div>
                                        <div class="menu">
                                            <div class="item" data-value="SI">SI</div>
                                            <div class="item" data-value="NO">NO</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="field">
                                    <label>No. Radicado Tutela </label>
                                    <input type="text" name="tutelage_radication_number"
                                           value="{{$activity->pcl ? $activity->pcl->tutelage_radication_number : ''}}">
                                </div>
                                <div class="field">
                                    <label>Fecha radicado tutela</label>
                                    <input type="text" name="beneficiaries_date" class="datepicker"
                                           value="{{$activity->pcl ? $activity->pcl->tutelage_radication_date : ''}}">
                                </div>
                                <div class="field">
                                    <label>Instancia de Tutela </label>
                                    <div class="ui fluid selection dropdown">
                                        <input name="tutelage_instance" type="hidden"
                                               value="{{$activity->pcl ? $activity->pcl->tutelage_instance : ''}}">
                                        <i class="dropdown icon"></i>
                                        <div class="default text">Seleccione uno</div>
                                        <div class="menu">
                                            <div class="item" data-value="Avoco">Avoco</div>
                                            <div class="item" data-value="Fallo 1ra instancia">Fallo 1ra instancia</div>
                                            <div class="item" data-value="Fallo 2da instancia">Fallo 2da instancia</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div id="67" class="title"><i class="dropdown icon"></i> Validación documental
                </div>
                <div class="content">
                    <div class="three fields">
                        <div class="field">
                            <label>Caso para rechazo ?</label>
                            <div class="ui fluid selection dropdown" id="rejectionCase">
                                <input name="rejection_case" type="hidden"
                                       value="{{$activity->pcl ? $activity->pcl->rejection_case : ''}}">
                                <i class="dropdown icon"></i>
                                <div class="default text">Seleccione uno</div>
                                <div class="menu">
                                    <div class="item" data-value="SI">SI</div>
                                    <div class="item" data-value="NO">NO</div>
                                </div>
                            </div>
                        </div>
                        <div class="field" id="rejectionCausal">
                            <label>Causal de rechazo</label>
                            <div class="ui fluid selection dropdown">
                                <input name="rejection_causal" type="hidden"
                                       value="{{$activity->pcl ? $activity->pcl->rejection_causal : ''}}">
                                <i class="dropdown icon"></i>
                                <div class="default text">Seleccione uno</div>
                                <div class="menu">
                                    @foreach($REJECTION_CAUSALS_PCL as $k => $v)
                                        <div class="item" data-value="{{$k}}">{{$v}}</div>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                        <div class="field" id="fieldObservation">
                            <label>Observaciones</label>
                            <textarea lang="es-CO" spellcheck="true" name="tutelage_observation"
                                      rows="6">{{$activity->pcl ? $activity->pcl->tutelage_observation : ''}}</textarea>
                        </div>
                    </div>
                    <div class="three fields">
                        <div class="field">
                            <label>Radicado comunicación externo</label>
                            <input type="text" name="extern_communication_radication"
                                   value="{{$activity->pcl ? $activity->pcl->extern_communication_radication : ''}}">
                        </div>
                        <div class="field">
                            <label>R.I de estandarizacion </label>
                            <input type="text" name="standardization_ri"
                                   value="{{$activity->pcl ? $activity->pcl->standardization_ri : ''}}">
                        </div>
                    </div>
                    <div class="three fields">
                        <div class="field">
                            <label>Solicitud de Documentos ?</label>
                            <div class="ui fluid selection dropdown">
                                <input name="tutelage_doc_query" type="hidden" id="requestForDocs"
                                       value="{{$activity->pcl ? $activity->pcl->tutelage_doc_query : ''}}">
                                <i class="dropdown icon"></i>
                                <div class="default text">Seleccione uno</div>
                                <div class="menu">
                                    <div class="item" data-value="SI">SI</div>
                                    <div class="item" data-value="NO">NO</div>
                                </div>
                            </div>
                        </div>
                        <div class="field">
                            <label>Convenio internacional ?</label>
                            <div class="ui fluid selection dropdown">
                                <input name="tutelage_international_convenio" type="hidden"
                                       value="{{$activity->pcl ? $activity->pcl->tutelage_international_convenio : ''}}">
                                <i class="dropdown icon"></i>
                                <div class="default text">Seleccione uno</div>
                                <div class="menu">
                                    <div class="item" data-value="SI">SI</div>
                                    <div class="item" data-value="NO">NO</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="styled fluid accordion">
                        <div class="title" id="requestDocsDrowpDown"><i class="dropdown icon"></i>
                            Solicitud de documentos
                        </div>
                        <div class="content" id="requestDocsContent">
                            <div style="margin-bottom: 5px;" class="fields">
                                <div class="seven wide field">
                                    <label>Documento</label>
                                </div>
                                <div class="eight wide field">
                                    <label>Observaciones</label>
                                </div>
                                <div class="one wide field">
                                    <a style="margin-top: -15px;" onclick="addDocumentaryValidations();"
                                       class="ui basic small icon blue button"><i class="add icon"></i></a>
                                </div>
                            </div>
                            <div id="documentary_validation">
                                @if ($activity->pcl && count($activity->pcl->documentary_validations) > 0)
                                    @foreach ($activity->pcl->documentary_validations as $documentary_validation)
                                        <div class="fields">
                                            <input type="hidden" name="documentary_validations[id][]"
                                                   value="{{$documentary_validation->id}}">
                                            <div class="seven wide field">
                                                <div class="ui fluid selection dropdown">
                                                    <input name="documentary_validations[document_validation][]"
                                                           type="hidden"
                                                           value="{{$documentary_validation->document_validation}}">
                                                    <i class="dropdown icon"></i>
                                                    <div class="default text">Seleccione uno</div>
                                                    <div class="menu">
                                                        <div class="item"
                                                             data-value="Valoraciones médicas y/o conceptos clínicos - Necesarias para la calificación según el decreto aplicado">
                                                            Valoraciones médicas y/o conceptos clínicos - Necesarias para la calificación según el decreto aplicado
                                                        </div>
                                                        <div class="item"
                                                             data-value="Pruebas objetivas y/o documentos complementarios - Necesarias para la calificación según el decreto aplicado">
                                                            Pruebas objetivas y/o documentos complementarios - Necesarias para la calificación según el decreto aplicado
                                                        </div>
                                                        <div class="item"
                                                             data-value="Pruebas de laboratorio - Necesarias para la calificación según el decreto aplicado">
                                                            Pruebas de laboratorio - Necesarias para la calificación según el decreto aplicado
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="eight wide field">
                                                    <textarea
                                                            name="documentary_validations[document_validation_observation][]"
                                                            rows="6">{{$documentary_validation->document_validation_observation}}</textarea>
                                            </div>
                                            <div class="one wide field">
                                                <a class="ui red small icon basic button"><i
                                                            class="remove icon"></i></a>
                                            </div>
                                        </div>
                                    @endforeach
                                @else
                                    <div class="fields">
                                        <input type="hidden" name="documentary_validations[id][]">
                                        <div class="seven wide field">
                                            <div class="ui fluid selection dropdown">
                                                <input name="documentary_validations[document_validation][]"
                                                       type="hidden">
                                                <i class="dropdown icon"></i>
                                                <div class="default text">Seleccione uno</div>
                                                <div class="menu">
                                                    <div class="item"
                                                         data-value="Valoraciones médicas y/o conceptos clínicos - Necesarias para la calificación según el decreto aplicado">
                                                        Valoraciones médicas y/o conceptos clínicos - Necesarias para la calificación según el decreto aplicado
                                                    </div>
                                                    <div class="item"
                                                         data-value="Pruebas objetivas y/o documentos complementarios - Necesarias para la calificación según el decreto aplicado">
                                                        Pruebas objetivas y/o documentos complementarios - Necesarias para la calificación según el decreto aplicado
                                                    </div>
                                                    <div class="item"
                                                         data-value="Pruebas de laboratorio - Necesarias para la calificación según el decreto aplicado">
                                                        Pruebas de laboratorio - Necesarias para la calificación según el decreto aplicado
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="eight wide field">
                                                <textarea
                                                        name="documentary_validations[document_validation_observation][]"
                                                        rows="6"></textarea>
                                        </div>
                                        <div class="one wide field"></div>
                                    </div>
                                @endif
                            </div>
                            <div class="four fields">
                                <div class="field">
                                    <label>Fecha de envio comunicación</label>
                                    <input type="text" name="communication_send_date" class="datepicker"
                                           value="{{$activity->pcl ? $activity->pcl->communication_send_date : ''}}">
                                </div>
                                <div class="field">
                                    <label>Estado de entrega comunicación</label>
                                    <div class="ui fluid selection dropdown">
                                        <input name="delivery_communication_state" type="hidden" id="effectiveDelivery"
                                               value="{{$activity->pcl ? $activity->pcl->delivery_communication_state : ''}}">
                                        <i class="dropdown icon"></i>
                                        <div class="default text">Seleccione uno</div>
                                        <div class="menu">
                                            <div class="item" data-value="ENTREGA EFECTIVA">Entrega Efectiva</div>
                                            <div class="item" data-value="ENTREGA NO EFECTIVA">Entrega no efectiva</div>
                                            <div class="item" data-value="ENTREGA NO EFECTIVA - INFORMACIÓN INCORRECTA">
                                                Entrega no efectiva - Información incorrecta
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="field" id="effectiveDeliveryDoc">
                                    <label>Fecha entrega efectiva Sol. Doc </label>
                                    <input type="text" name="effective_sol_document_date"
                                           id="effective_sol_document_date" class="datepicker"
                                           value="{{$activity->pcl ? $activity->pcl->effective_sol_document_date : ''}}">
                                </div>
                                {{--                                TODO :: REVISAR CALCULO 30 DIAS--}}
                                <div class="field">
                                    <label style="color: red">Fecha cumplimiento termino Sol Doc</label>
                                    <input type="text" name="compliance_sol_doc_date" id="compliance_sol_doc_date"
                                           value="{{$activity->pcl ? $activity->pcl->compliance_sol_doc_date : ''}}"

                                           readonly>
                                </div>
                            </div>
                            <div class="four fields">
                                <div class="field">
                                    <label>Radican solicitud Prorroga</label>
                                    <div class="ui fluid selection dropdown">
                                        <input name="extension_query_radication" type="hidden"
                                               value="{{$activity->pcl ? $activity->pcl->extension_query_radication : ''}}">
                                        <i class="dropdown icon"></i>
                                        <div class="default text">Seleccione uno</div>
                                        <div class="menu">
                                            <div class="item" data-value="SI">SI</div>
                                            <div class="item" data-value="NO">NO</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="title"><i class="dropdown icon"></i>
                            Solicitud de prorroga
                        </div>
                        <div class="content">
                            <div class="four fields">
                                <div class="field">
                                    <label>Fecha Rad prorroga </label>
                                    <input type="text" name="extension_query_radication_date" class="datepicker"
                                           value="{{$activity->pcl ? $activity->pcl->extension_query_radication_date : ''}}">
                                </div>
                                <div class="field">
                                    <label>No. Radicado Bizagi</label>
                                    <input type="text" name="extension_bizagi_rad_number"
                                           value="{{$activity->pcl ? $activity->pcl->extension_bizagi_rad_number : ''}}">
                                </div>
                                {{--                                TODO :: REVISAR CALCULO 60 DIAS--}}
                                <div class="field">
                                    <label style="color: red">Fecha cumplimiento termino con prorroga</label>
                                    <input type="text" name="compliance_extension_date" id="compliance_extension_date"
                                           value="{{$activity->pcl ? $activity->pcl->compliance_extension_date : ''}}">
                                </div>
                            </div>
                        </div>
                        <div class="title"><i class="dropdown icon"></i>
                            Recepción documentos
                        </div>
                        <div class="content">
                            <div class="four fields">
                                <div class="field">
                                    <label>Fecha recepcion documentos</label>
                                    <input type="text" name="doc_reception_date" class="datepicker"
                                           value="{{$activity->pcl ? $activity->pcl->doc_reception_date : ''}}">
                                </div>
                                <div class="field">
                                    <label>No. Radicado Bizagi</label>
                                    <input type="text" name="doc_bizagi_rad_number"
                                           value="{{$activity->pcl ? $activity->pcl->doc_bizagi_rad_number : ''}}">
                                </div>
                                <div class="field">
                                    <label>Aporta documentacion solicitada</label>
                                    <div class="ui fluid selection dropdown">
                                        <input name="provide_requested_documentation" type="hidden"
                                               value="{{$activity->pcl ? $activity->pcl->provide_requested_documentation : ''}}">
                                        <i class="dropdown icon"></i>
                                        <div class="default text">Seleccione uno</div>
                                        <div class="menu">
                                            <div class="item" data-value="SI">SI</div>
                                            <div class="item" data-value="NO">NO</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="title" id="68"><i class="dropdown icon"></i> Agendamiento cita
                </div>
                <div class="content" id="medicalAppoimentContent">
                    <div class="styled fluid accordion">
                        <!-- CONTACTABILITY -->
                        <div class="title"><i class="dropdown icon"></i>Registros de contactabilidad <span
                                    style="color: red;"
                                    class="required">*</span>
                        </div>
                        <div class="content" id="medicalAppoimentContent">
                            <div style="margin-bottom: 5px;" class="fields">
                                <div class="four wide required field"><label>Fecha de contacto</label></div>
                                <div class="four wide required field"><label>Teléfono o celular de contacto</label>
                                </div>
                                <div class="six wide required field"><label>Estado de comunicación</label></div>
                                <div class="one wide field" id="more_contactabilities">
                                    <a style="margin-top: -15px;" onclick="return addContactability();"
                                       class="ui basic small icon blue button"><i class="add icon"></i></a>
                                </div>
                            </div>
                            <div id="contactabilities">
                                @if ($activity->pcl && count($activity->pcl->contactabilities) > 0)
                                    @foreach($activity->pcl->contactabilities as $contact)
                                        <div class="fields">
                                            <input type="hidden" name="contactabilities[id][]"
                                                   value="{{$contact->id}}">
                                            <div class="four wide field">
                                                <input name="contactabilities[contact_date][]"
                                                       type="text" class="datepicker"
                                                       data-value="{{$contact->contact_date}}">
                                            </div>
                                            <div class="four wide field">
                                                <input name="contactabilities[contact_phone][]"
                                                       type="text" value="{{$contact->contact_phone}}">
                                            </div>
                                            <div class="six wide field">
                                                <div class="ui fluid selection dropdown">
                                                    <input name="contactabilities[communication_state][]"
                                                           type="hidden"
                                                           value="{{$contact->communication_state}}">
                                                    <i class="dropdown icon"></i>
                                                    <div class="default text">Seleccione uno</div>
                                                    <div class="menu">
                                                        <div class="item" data-value="Contesta">Contesta</div>
                                                        <div class="item" data-value="No contesta">No contesta</div>
                                                        <div class="item" data-value="Equivocado">Equivocado</div>
                                                        <div class="item" data-value="Buzon">Buzon</div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="one wide field">
                                                <a class="ui red small icon basic button"><i
                                                            class="remove icon"></i></a>
                                            </div>
                                        </div>
                                    @endforeach
                                @else
                                    <div class="fields">
                                        <input type="hidden" name="contactabilities[id][]">
                                        <div class="four wide field">
                                            <input name="contactabilities[contact_date][]"
                                                   type="text" class="datepicker">
                                        </div>
                                        <div class="four wide field">
                                            <input name="contactabilities[contact_phone][]">
                                        </div>
                                        <div class="six wide field">
                                            <div class="ui fluid selection dropdown">
                                                <input name="contactabilities[communication_state][]" type="hidden">
                                                <i class="dropdown icon"></i>
                                                <div class="default text">Seleccione uno</div>
                                                <div class="menu">
                                                    <div class="item" data-value="Contesta">Contesta</div>
                                                    <div class="item" data-value="No contesta">No contesta</div>
                                                    <div class="item" data-value="Equivocado">Equivocado</div>
                                                    <div class="item" data-value="Buzon">Buzon</div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="one wide field"></div>
                                    </div>
                                @endif
                            </div>
                        </div>
                        <!-- END: CONTACTABILITY -->
                    </div>
                </div>
                <div id="69" class="title">
                    <i class="dropdown icon"></i> valoración presencial/valoracion telefonica titulo ii
                </div>
                <div class="content" id="69_content">
                    <div class="four fields">
                        <div class="styled fluid accordion">
                            <div class="title" id="69_1">
                                <i class="dropdown icon"></i> Valoración Medica
                            </div>
                            <div class="content" id="69_1_content">
                                <div class="four fields">
                                    <div class="field">
                                        <label>Tipo Valoración</label>
                                        <div class="ui fluid selection dropdown {{  $activity->creation_source != 'MIGRACIÓN' ? ' disabled' : '' }}">
                                            <input type="hidden" name="valoration_valoration_type" id="valoration_type"
                                                   value="{{$activity->pcl ? $activity->pcl->valoration_valoration_type : ''}}">
                                            <i class="dropdown icon"></i>
                                            <div class="default text">Escoja una opción</div>
                                            <div class="menu">
                                                <div class="item" data-value="PRESENCIAL">PRESENCIAL</div>
                                                <div class="item" data-value="VALORACION TELEFONICA TITULO II">
                                                    VALORACION TELEFONICA TITULO II
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="field">
                                        <label>Fecha</label>
                                        <input name="valoration_v_date" type="text"
                                               value="{{$activity->pcl ? $activity->pcl->valoration_v_date : ''}}"
                                               class="{{ $activity->creation_source == 'MIGRACIÓN' ? 'datepicker' : '' }}"
                                               {{  $activity->creation_source != 'MIGRACIÓN' ? 'readonly' : '' }}
                                               style="{{ $activity->creation_source != 'MIGRACIÓN' ? 'color: #C3C3C3' : '' }}">
                                    </div>
                                    <div class="field">
                                        <label>Hora</label>
                                        <input name="valoration_v_time" type="text"
                                               value="{{$activity->pcl ? $activity->pcl->valoration_v_time : ''}}"
                                               class="{{ $activity->creation_source == 'MIGRACIÓN' ? 'timepicker' : '' }}"
                                               {{  $activity->creation_source != 'MIGRACIÓN' ? 'readonly' : '' }}
                                               style="{{ $activity->creation_source != 'MIGRACIÓN' ? 'color: #C3C3C3' : '' }}">
                                    </div>
                                    <div class="field">
                                        <label>Canal Consulta</label>
                                        <div class="ui fluid selection dropdown">
                                            <input type="hidden" name="valoration_consultation_channel"
                                                   value="{{$activity->pcl ? $activity->pcl->valoration_consultation_channel : ''}}">
                                            <i class="dropdown icon"></i>
                                            <div class="default text">Escoja una opción</div>
                                            <div class="menu">
                                                <div class="item" data-value="Zoom">Zoom</div>
                                                <div class="item" data-value="Whatsapp">Whatsapp</div>
                                                <div class="item" data-value="Llamada telefonica">Llamada telefónica
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="title" id="69_2">
                                <i class="dropdown icon"></i> Respondiente
                            </div>
                            <div class="content" id="69_2_content">
                                <div class="four fields">
                                    <div class="field">
                                        <label>Nombre</label>
                                        <input name="valoration_companion_name" type="text"
                                               value="{{$activity->pcl && $activity->pcl->valoration_companion_name ? $activity->pcl->valoration_companion_name : 'No reporta'}}">
                                    </div>
                                    <div class="field">
                                        <label>Teléfono contacto</label>
                                        <input name="valoration_companion_phone" type="text"
                                               value="{{$activity->pcl &&  $activity->pcl->valoration_companion_phone ? $activity->pcl->valoration_companion_phone : 'No reporta'}}">
                                    </div>
                                    <div class="field">
                                        <label>Parentesco</label>
                                        <input name="valoration_companion_kinship" type="text"
                                               value="{{$activity->pcl &&  $activity->pcl->valoration_companion_kinship ? $activity->pcl->valoration_companion_kinship : 'No reporta'}}">
                                    </div>
                                </div>
                            </div>
                            <div class="title" id="69_3">
                                <i class="dropdown icon"></i> Medico evaluador
                            </div>
                            <div class="content" id="69_3_content">
                                <div class="fields">
                                    <div class="four wide required field">
                                        <label>Tipo de documento</label>
                                        <div class="ui fluid selection dropdown" id="doc_doc_type_dropdown">
                                            <input type="hidden" name="doc_doc_type" class="doc_doc_type"
                                                   value="{{$activity->pcl ? $activity->pcl->doc_doc_type : 'CC'}}">
                                            <i class="dropdown icon"></i>
                                            <div class="default text">Escoja una opción</div>
                                            <div class="menu">
                                                @foreach($DOC_TYPES as $k => $v)
                                                    <div class="item" data-value="{{$k}}">{{$v}}</div>
                                                @endforeach
                                            </div>
                                        </div>
                                    </div>
                                    <div class="four wide required field">
                                        <label>Número de Identificación</label>
                                        <div class="ui search doctors">
                                            <div class="ui icon input">
                                                <input class="prompt" name="doc_doc_number" type="text"
                                                       value="{{$activity->pcl ? $activity->pcl->doc_doc_number : ''}}">
                                                <i class="search icon"></i>
                                            </div>
                                            <div class="results"></div>
                                        </div>
                                    </div>
                                    <div class="four wide required field">
                                        <label>Rethus</label>
                                        <input type="text" name="doc_rethus" class="doc_rethus"
                                               value="{{$activity->pcl ? $activity->pcl->doc_rethus : ''}}">
                                    </div>
                                </div>
                                <div class="fields">
                                    <div class="four wide required field">
                                        <label>Nombres y apellidos</label>
                                        <input name="doc_full_name" class="doc_full_name" type="text"
                                               value="{{$activity->pcl ? $activity->pcl->doc_full_name : ''}}">
                                    </div>
                                    <div class="four wide required field">
                                        <label>No. Registro Médico</label>
                                        <input name="doc_medical_record" class="doc_medical_record" type="text"
                                               value="{{$activity->pcl ? $activity->pcl->doc_medical_record : ''}}">
                                    </div>
                                    <div class="four wide required field">
                                        <label>Especialidad</label>
                                        <input name="doc_specialited" class="doc_specialited" type="text"
                                               value="{{$activity->pcl ? $activity->pcl->doc_specialited : ''}}">
                                    </div>
                                    <div class="four wide field">
                                        <label>No. Licencia</label>
                                        <input name="doc_license" class="doc_license" type="text"
                                               value="{{$activity->pcl ? $activity->pcl->doc_license : ''}}">
                                    </div>
                                </div>
                            </div>
                            <div class="title" id="69_4">
                                <i class="dropdown icon"></i> Anamnesis - enfermedad actual
                            </div>
                            <div class="content" id="69_4_content">
                                <div class="required field">
                                    <label>Descripción</label>
                                    <textarea name="anamnesis_actual_disease"
                                              rows="6">{{$activity->pcl && $activity->pcl->anamnesis_actual_disease ? $activity->pcl->anamnesis_actual_disease : ''}}</textarea>
                                </div>
                                <div class="required field">
                                    <label>Antecedente laboral</label>
                                    <textarea name="anamnesis_illness_history"
                                              rows="6">{{$activity->pcl && $activity->pcl->anamnesis_actual_disease ? $activity->pcl->anamnesis_illness_history : ''}}</textarea>
                                </div>
                            </div>
                            <div class="title" id="69_5">
                                <i class="dropdown icon"></i> Antecedentes Personales
                            </div>
                            <div class="content" id="69_5_content">
                                <div class="three fields">
                                    <div class="field">
                                        <label>Patológicos</label>
                                        <textarea name="anamnesis_patological"
                                                  rows="6">{{$activity->pcl && $activity->pcl->anamnesis_actual_disease ? $activity->pcl->anamnesis_patological : 'Sin hallazgos positivos'}}</textarea>
                                    </div>
                                    <div class="field">
                                        <label> Quirúrgicos </label>
                                        <textarea name="anamnesis_surgical"
                                                  rows="6">{{$activity->pcl && $activity->pcl->anamnesis_actual_disease ? $activity->pcl->anamnesis_surgical : 'Sin hallazgos positivos'}}</textarea>
                                    </div>
                                    <div class="field">
                                        <label>Farmacológicos</label>
                                        <textarea name="anamnesis_pharmaceutical"
                                                  rows="6">{{$activity->pcl && $activity->pcl->anamnesis_actual_disease ? $activity->pcl->anamnesis_pharmaceutical : 'Sin hallazgos positivos'}}</textarea>
                                    </div>
                                </div>
                                <div class="three fields">
                                    <div class="field">
                                        <label>Hospitalarios</label>
                                        <textarea name="anamnesis_hospitalary"
                                                  rows="6">{{$activity->pcl && $activity->pcl->anamnesis_actual_disease ? $activity->pcl->anamnesis_hospitalary : 'Sin hallazgos positivos'}}</textarea>
                                    </div>
                                    <div class="field">
                                        <label>Toxicos-alergicos</label>
                                        <textarea name="anamnesis_toxic"
                                                  rows="6">{{$activity->pcl && $activity->pcl->anamnesis_actual_disease ? $activity->pcl->anamnesis_toxic : 'Sin hallazgos positivos'}}</textarea>
                                    </div>
                                    <div class="field">
                                        <label>Trasfusiones </label>
                                        <textarea name="anamnesis_transfusion"
                                                  rows="6">{{$activity->pcl && $activity->pcl->anamnesis_actual_disease ? $activity->pcl->anamnesis_transfusion : 'Sin hallazgos positivos'}}</textarea>
                                    </div>
                                </div>
                                <div class="three fields">
                                    <div class="field">
                                        <label>Familiares</label>
                                        <textarea name="anamnesis_family"
                                                  rows="6">{{$activity->pcl && $activity->pcl->anamnesis_actual_disease ? $activity->pcl->anamnesis_family : 'Sin hallazgos positivos'}}</textarea>
                                    </div>
                                </div>
                            </div>
                            <div id="physical_exam_title" class="title">
                                <i class="dropdown icon"></i> Examen Fisico
                            </div>
                            <div id="physical_exam_content" class="content">
                                <div class="four fields">
                                    <div class="required field">
                                        <label>Estado general</label>
                                        <textarea name="general_state"
                                                  rows="6">{{$activity->pcl ? $activity->pcl->general_state : ''}}</textarea>
                                    </div>
                                    <div class="field">
                                        <label>Lateralidad </label>
                                        <div class="ui fluid selection dropdown">
                                            <input name="physic_exam_laterality" type="hidden"
                                                   value="{{$activity->pcl ? $activity->pcl->physic_exam_laterality : ''}}">
                                            <i class="dropdown icon"></i>
                                            <div class="default text">Seleccione uno</div>
                                            <div class="menu">
                                                <div class="item" data-value="Derecho">Derecho</div>
                                                <div class="item" data-value="Izquierdo">Izquierdo</div>
                                                <div class="item" data-value="Ambidiestro">Ambidiestro</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="ui divider" style="border-top-width: 3px"></div>
                                <div class="seven fields">
                                    <div class="required field">
                                        <label>Talla</label>
                                        <div class="ui right labeled input">
                                            <input onchange="calculateIMC()" step="any" name="height" type="number"
                                                   value="{{$activity->pcl ? $activity->pcl->height : ''}}">
                                            <div class="ui basic label">
                                                cms
                                            </div>
                                        </div>
                                    </div>
                                    <div class="required field">
                                        <label>Peso</label>
                                        <div class="ui right labeled input">
                                            <input onchange="calculateIMC()" step="any" name="weight" type="number"
                                                   value="{{$activity->pcl ? $activity->pcl->weight : ''}}">
                                            <div class="ui basic label">
                                                Kgs
                                            </div>
                                        </div>
                                    </div>
                                    <div class="required field">
                                        <label>IMC <sup><a target="_blank"
                                                           href="http://www.texasheart.org/HIC/Topics_Esp/HSmart/bmi_calculator_span.cfm">[1]</a></sup></label>
                                        <p style="line-height: 30px;"
                                           class="imc">{{$activity->pcl ? $activity->pcl->imc . " ({$activity->pcl->imc_label})" : ''}}</p>
                                        <input step="any" class="imc" name="imc_data" type="number"
                                               style="display: none"
                                               value="{{$activity->pcl ? $activity->pcl->imc_data : ''}}">
                                    </div>
                                    <div class="required field">
                                        <label>TA</label>
                                        <div class="two fields">
                                            <div class="field">
                                                <input step="any" name="ta" type="number"
                                                       value="{{$activity->pcl ? $activity->pcl->ta : ''}}">
                                            </div>
                                            <span style="font-size: 2em; line-height: 30px;">/</span>
                                            <div class="field">
                                                <input step="any" name="ta2" type="number"
                                                       value="{{$activity->pcl ? $activity->pcl->ta2 : ''}}">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="required field">
                                        <label>FC</label>
                                        <input step="any" name="fc" type="number"
                                               value="{{$activity->pcl ? $activity->pcl->fc : ''}}">
                                    </div>
                                    <div class="required field">
                                        <label>FR</label>
                                        <input step="any" name="fr" type="number"
                                               value="{{$activity->pcl ? $activity->pcl->fr : ''}}">
                                    </div>
                                    <div class="required field">
                                        <label>Temperatura</label>
                                        <input step="any" name="temperature" type="number"
                                               value="{{$activity->pcl ? $activity->pcl->temperature : ''}}">
                                    </div>
                                </div>
                                <div class="ui divider" style="border-top-width: 3px"></div>
                                <div class="three fields">
                                    <div class="field">
                                        <label>Cabeza y cuello </label>
                                        <textarea name="head_and_neck"
                                                  rows="6">{{$activity->pcl && $activity->pcl->head_and_neck ? $activity->pcl->head_and_neck : 'Sin hallazgos positivos'}}</textarea>
                                    </div>
                                    <div class="field">
                                        <label>Cardio pulmonar</label>
                                        <textarea name="pulmonaryal_cardio"
                                                  rows="6">{{$activity->pcl && $activity->pcl->pulmonaryal_cardio ? $activity->pcl->pulmonaryal_cardio : 'Sin hallazgos positivos'}}</textarea>
                                    </div>
                                    <div class="field">
                                        <label>Abdomen </label>
                                        <textarea name="abdomen"
                                                  rows="6">{{$activity->pcl && $activity->pcl->abdomen ? $activity->pcl->abdomen : 'Sin hallazgos positivos'}}</textarea>
                                    </div>
                                </div>
                                <div class="three fields">
                                    <div class="field">
                                        <label>Extremidades</label>
                                        <textarea name="phisical_exam_extremities"
                                                  rows="6">{{$activity->pcl && $activity->pcl->phisical_exam_extremities ? $activity->pcl->phisical_exam_extremities : 'Sin hallazgos positivos'}}</textarea>
                                    </div>
                                    <div class="field">
                                        <label>Neurológico </label>
                                        <textarea name="phisical_exam_neurological"
                                                  rows="6">{{$activity->pcl && $activity->pcl->phisical_exam_neurological ? $activity->pcl->phisical_exam_neurological : 'Sin hallazgos positivos'}}</textarea>
                                    </div>
                                    <div class="field">
                                        <label>Mental </label>
                                        <textarea name="phisical_exam_mental"
                                                  rows="6">{{$activity->pcl && $activity->pcl->phisical_exam_mental ? $activity->pcl->phisical_exam_mental : 'Sin hallazgos positivos'}}</textarea>
                                    </div>
                                </div>
                                <div class="three fields">
                                    <div class="field">
                                        <label>Observación</label>
                                        <textarea name="phisical_exam_observation"
                                                  rows="6">{{$activity->pcl && $activity->pcl->phisical_exam_mental ? $activity->pcl->phisical_exam_observation : ''}}</textarea>
                                    </div>
                                </div>
                                <div class="styled fluid accordion">
                                    <div class="title">
                                        <i class="dropdown icon"></i> Diagnósticos <span style="color: red;"
                                                                                         class="required">*</span>
                                    </div>
                                    <div class="content form">
                                        <div style="margin-bottom: 5px;" class="fields">
                                            <div class="three wide required field">
                                                <label>Cód.</label>
                                            </div>
                                            <div class="twelve wide required field">
                                                <label>Nombre</label>
                                            </div>
                                            <div class="twelve wide required field">
                                                <label>Descripción</label>
                                            </div>
                                            <div class="one wide field">
                                                <a style="margin-top: -15px;" onclick="addDiagnosticValoration()"
                                                   class="ui basic small icon blue button"><i class="add icon"></i></a>
                                            </div>
                                        </div>
                                        <div id="diagnostics_valoration">
                                            @if ($activity->pcl && count($activity->pcl->valoration_diagnostics) > 0)
                                                @foreach ($activity->pcl->valoration_diagnostics as $diagnostic)
                                                    <div class="fields">
                                                        <input type="hidden" name="diagnostics_valoration[id][]"
                                                               value="{{$diagnostic->id}}">
                                                        <div class="three wide required field">
                                                            <div class="ui search diagnostic code">
                                                                <div class="ui icon input">
                                                                    <input class="prompt"
                                                                           name="diagnostics_valoration[cod][]"
                                                                           type="text"
                                                                           value="{{$diagnostic->cod}}">
                                                                    <i class="search icon"></i>
                                                                </div>
                                                                <div class="results"></div>
                                                            </div>
                                                        </div>
                                                        <div class="twelve wide required field">
                                                            <input class="prompt"
                                                                   name="diagnostics_valoration[description][]"
                                                                   type="text"
                                                                   value="{{$diagnostic->description}}" readonly>
                                                        </div>
                                                        <div class="twelve wide required field">
                                                            <input class="prompt"
                                                                   name="diagnostics_valoration[description_editable][]"
                                                                   type="text"
                                                                   value="{{$diagnostic->description_editable}}">
                                                        </div>
                                                        <div class="one wide field">
                                                            <a class="ui red small icon basic button"><i
                                                                        class="remove icon"></i></a>
                                                        </div>
                                                    </div>
                                                @endforeach
                                            @else
                                                <div class="fields">
                                                    <input type="hidden" name="diagnostics_valoration[id][]">
                                                    <div class="three wide required field">
                                                        <div class="ui search diagnostic code">
                                                            <div class="ui icon input">
                                                                <input class="code prompt"
                                                                       name="diagnostics_valoration[cod][]"
                                                                       type="text">
                                                                <i class="search icon"></i>
                                                            </div>
                                                            <div class="results"></div>
                                                        </div>
                                                    </div>
                                                    <div class="twelve wide required field">
                                                        <input class="description prompt"
                                                               name="diagnostics_valoration[description][]"
                                                               type="text" readonly>
                                                    </div>
                                                    <div class="twelve wide required field">
                                                        <input class="description prompt"
                                                               name="diagnostics_valoration[description_editable][]"
                                                               type="text">
                                                    </div>
                                                    <div class="one wide field"></div>
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                                <div class="four fields" style="padding-top: 1rem">
                                    <div class="field">
                                        <label>Requiere historia clínica adicional y/o examenes complementarios</label>
                                        <div class="ui fluid selection dropdown">
                                            <input name="req_hc_add_or_complement_exams" type="hidden"
                                                   value="{{$activity->pcl ? $activity->pcl->req_hc_add_or_complement_exams : ''}}">
                                            <i class="dropdown icon"></i>
                                            <div class="default text">Seleccione uno</div>
                                            <div class="menu">
                                                <div class="item" data-value="SI">SI</div>
                                                <div class="item" data-value="NO">NO</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="title" id="laboral_antecedent_title">
                                <i class="dropdown icon"></i> Análisis médico laboral del caso
                            </div>
                            <div id="laboral_antecedent_content" class="content">
                                <div class="required field">
                                    <label>Antecedente laboral</label>
                                    <textarea name="laboral_antecedent"
                                              rows="6">{{$activity->pcl && $activity->pcl->laboral_antecedent ? $activity->pcl->laboral_antecedent : ''}}</textarea>
                                </div>
                            </div>
                            <div class="title" id="laboral_role_title"><i class="dropdown icon"></i> Rol
                                Rol laboral/Ocupacional/Discapacidades y minusvalías
                            </div>
                            <div class="content" id="laboral_role_content">
                                <div class="three fields">
                                    @if($age > 3)
                                        <div class="field">
                                            <div class="ui toggle checkbox">
                                                <input name="valoration_laborally_active" type="checkbox"
                                                       value="1" {{$activity->pcl && $activity->pcl->valoration_laborally_active ? 'checked' : ''}}>
                                                <label>Laboralmente activo</label>
                                            </div>
                                        </div>
                                        <div class="field">
                                            <div class="ui toggle checkbox">
                                                <input name="valoration_use_laboral_role" type="checkbox"
                                                       value="1" {{$activity->pcl && $activity->pcl->valoration_use_laboral_role ? 'checked' : ''}}>
                                                <label>Aplica rol ocupacional</label>
                                            </div>
                                        </div>
                                        <div class="field">
                                            <div class="ui toggle checkbox">
                                                <input name="valoration_use_disabilities_handicap" type="checkbox"
                                                       value="1" {{$activity->pcl && $activity->pcl->valoration_use_disabilities_handicap ? 'checked' : ''}}>
                                                <label>Discapacidades y Minusvalías</label>
                                            </div>
                                        </div>
                                    @else
                                        <div class="field">
                                        </div>
                                        <div class="field">
                                            <div class="ui toggle checkbox">
                                                <input name="valoration_use_laboral_role" type="checkbox"
                                                       value="1" {{$activity->pcl && $activity->pcl->valoration_use_laboral_role ? 'checked' : ''}}>
                                                <label>Aplica rol ocupacional</label>
                                            </div>
                                        </div>
                                    @endif

                                </div>
                                <div class="two fields">
                                    <div class="field" style="padding-top: 1rem">
                                    </div>
                                    {{--                                    <div class="field" id="valoration_use_rol_description">--}}
                                    {{--                                        <label>Descripción</label>--}}
                                    {{--                                        <textarea name="valoration_use_laboral_role_description"--}}
                                    {{--                                                  rows="6">{{$activity->pcl ? $activity->pcl->valoration_use_laboral_role_description : ''}}</textarea>--}}
                                    {{--                                    </div>--}}
                                </div>
                            </div>
                            <div class="title" id="tab-disabilities">
                                <i class="dropdown icon"></i> Discapacidades
                            </div>
                            <div class="content" id="tab-disabilities-content">
                                <div class="three fields">
                                    <div class="field">
                                        <label>Discapacidades de la conducta</label>
                                        <textarea name="disabilities_conduct"
                                                  rows="3">{{$activity->pcl && $activity->pcl->disabilities_conduct ? $activity->pcl->disabilities_conduct : ''}}</textarea>
                                    </div>
                                    <div class="field">
                                        <label>Discapacidades de la comunicación</label>
                                        <textarea name="disabilities_communication"
                                                  rows="3">{{$activity->pcl && $activity->pcl->disabilities_communication ? $activity->pcl->disabilities_communication : ''}}</textarea>
                                    </div>
                                    <div class="field">
                                        <label>Discapacidades del cuidado personal</label>
                                        <textarea name="disabilities_personal_care"
                                                  rows="3">{{$activity->pcl && $activity->pcl->disabilities_personal_care ? $activity->pcl->disabilities_personal_care : ''}}</textarea>
                                    </div>
                                </div>
                                <div class="three fields">
                                    <div class="field">
                                        <label>Discapacidades de la locomoción</label>
                                        <textarea name="disabilities_locomotion"
                                                  rows="3">{{$activity->pcl && $activity->pcl->disabilities_locomotion ? $activity->pcl->disabilities_locomotion : ''}}</textarea>
                                    </div>
                                    <div class="field">
                                        <label>Discapacidades de la disposición del cuerpo</label>
                                        <textarea name="disabilities_body_disposition"
                                                  rows="3">{{$activity->pcl && $activity->pcl->disabilities_body_disposition ? $activity->pcl->disabilities_body_disposition : ''}}</textarea>
                                    </div>
                                    <div class="field">
                                        <label>Discapacidades de la destreza</label>
                                        <textarea name="disabilities_skill"
                                                  rows="3">{{$activity->pcl && $activity->pcl->disabilities_skill ? $activity->pcl->disabilities_skill : ''}}</textarea>
                                    </div>
                                </div>
                                <div class="three fields">
                                    <div class="field">
                                        <label>Discapacidades de la situación</label>
                                        <textarea name="disabilities_situation"
                                                  rows="3">{{$activity->pcl && $activity->pcl->disabilities_situation ? $activity->pcl->disabilities_situation : ''}}</textarea>
                                    </div>
                                </div>
                            </div>
                            <div class="title" id="tab-handicap">
                                <i class="dropdown icon"></i> Minusvalías
                            </div>
                            <div class="content" id="tab-handicap-content">
                                <div class="three fields">
                                    <div class="field">
                                        <label>Minusvalía de orientación</label>
                                        <textarea name="handicap_orientation"
                                                  rows="3">{{$activity->pcl && $activity->pcl->handicap_orientation ? $activity->pcl->handicap_orientation : ''}}</textarea>
                                    </div>
                                    <div class="field">
                                        <label>Minusvalía de independencia física</label>
                                        <textarea name="handicap_physical_independence"
                                                  rows="3">{{$activity->pcl && $activity->pcl->handicap_physical_independence ? $activity->pcl->handicap_physical_independence : ''}}</textarea>
                                    </div>
                                    <div class="field">
                                        <label>Minusvalía de desplazamiento</label>
                                        <textarea name="handicap_displacement"
                                                  rows="3">{{$activity->pcl && $activity->pcl->handicap_displacement ? $activity->pcl->handicap_displacement : ''}}</textarea>
                                    </div>
                                </div>
                                <div class="three fields">
                                    <div class="field">
                                        <label>Minusvalía ocupacional</label>
                                        <textarea name="handicap_occupational"
                                                  rows="3">{{$activity->pcl && $activity->pcl->handicap_occupational ? $activity->pcl->handicap_occupational : ''}}</textarea>
                                    </div>
                                    <div class="field">
                                        <label>Minusvalía de integración social</label>
                                        <textarea name="handicap_social_integration"
                                                  rows="3">{{$activity->pcl && $activity->pcl->handicap_social_integration ? $activity->pcl->handicap_social_integration : ''}}</textarea>
                                    </div>
                                </div>
                            </div>
                            <div class="title" id="rol-laboral">
                                <i class="dropdown icon"></i> Rol laboral
                            </div>
                            <div class="content" id="rol-laboral-content">
                                <div class="four fields">
                                    <div class="field">
                                        <label>Actualmente esta laboralmente activo?</label>
                                        <div class="ui fluid selection dropdown">
                                            <input name="lab_rol_laborally_active" type="hidden"
                                                   value="{{$activity->pcl ? $activity->pcl->lab_rol_laborally_active : ''}}">
                                            <i class="dropdown icon"></i>
                                            <div class="default text">Seleccione uno</div>
                                            <div class="menu">
                                                <div class="item" data-value="SI">SI</div>
                                                <div class="item" data-value="NO">NO</div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="field">
                                        <label>Trabajo habitual</label>
                                        <textarea name="lab_rol_habitual_work"
                                                  rows="6">{{$activity->pcl && $activity->pcl->lab_rol_habitual_work ? $activity->pcl->lab_rol_habitual_work : ''}}</textarea>
                                    </div>
                                    <div class="field">
                                        <label>Cargo</label>
                                        <textarea name="lab_rol_charge"
                                                  rows="6">{{$activity->pcl && $activity->pcl->lab_rol_charge ? $activity->pcl->lab_rol_charge : ''}}</textarea>
                                    </div>
                                    <div class="field">
                                        <label>Oficio (actividades que desempeña)</label>
                                        <textarea name="lab_rol_office_activity"
                                                  rows="6">{{$activity->pcl && $activity->pcl->lab_rol_office_activity ? $activity->pcl->lab_rol_office_activity : ''}}</textarea>
                                    </div>
                                </div>
                                <div class="four fields">
                                    <div class="field">
                                        <label>Tiempo en el cargo</label>
                                        <textarea name="lab_rol_charge_time"
                                                  rows="6">{{$activity->pcl && $activity->pcl->lab_rol_charge_time ? $activity->pcl->lab_rol_charge_time : ''}}</textarea>
                                    </div>
                                    <div class="field">
                                        <label>Empresa</label>
                                        <textarea name="lab_rol_employer"
                                                  rows="6">{{$activity->pcl && $activity->pcl->lab_rol_employer ? $activity->pcl->lab_rol_employer : ''}}</textarea>
                                    </div>
                                </div>
                                <div class="two fields">
                                    <div class="field">
                                        <label>Restricciones que presenta actualmente para desempeñar su rol
                                            laboral:</label>
                                        <textarea name="lab_rol_restrictions"
                                                  rows="3">{{$activity->pcl ? $activity->pcl->lab_rol_restrictions : ''}}</textarea>
                                    </div>
                                    <div class="field">
                                        <label>Tareas y operaciones que desempeña en este momento:</label>
                                        <textarea name="lab_rol_operations"
                                                  rows="3">{{$activity->pcl ? $activity->pcl->lab_rol_operations : ''}}</textarea>
                                    </div>
                                </div>
                                <div class="two fields">
                                    <div class="field">
                                        <label>Dispositivo de ayuda en el momento</label>
                                        <div class="ui fluid selection dropdown">
                                            <input name="help_device_at_the_moment" type="hidden"
                                                   value="{{$activity->pcl ? $activity->pcl->help_device_at_the_moment : ''}}">
                                            <i class="dropdown icon"></i>
                                            <div class="default text">Seleccione uno</div>
                                            <div class="menu">
                                                <div class="item" data-value="SI">SI</div>
                                                <div class="item" data-value="NO">NO</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="two fields">
                                    <div class="field">
                                        <label>Descripción</label>
                                        <textarea name="help_device_at_the_moment_description"
                                                  rows="3">{{$activity->pcl ? $activity->pcl->help_device_at_the_moment_description : ''}}</textarea>
                                    </div>
                                </div>
                                <div class="two fields">
                                    <div class="field">
                                        <label>Cuenta con restricciones o recomendaciones laborales emitidas por
                                            medicina laboral o médico tratante de EPS o ARL</label>
                                        <textarea name="lab_rol_restrictions_eps_arl"
                                                  rows="3">{{$activity->pcl ? $activity->pcl->lab_rol_restrictions_eps_arl : ''}}</textarea>
                                    </div>
                                    <div class="field">
                                        <label>Requiere ayuda de terceras personales para realizar sus actividades
                                            laborales</label>
                                        <textarea name="third_party_help"
                                                  rows="3">{{$activity->pcl ? $activity->pcl->third_party_help : ''}}</textarea>
                                    </div>
                                </div>
                                <div class="two fields">
                                    <div class="field">
                                        <label>Tiene dictamenes previos de calificacion de origen y/o PCL</label>
                                        <textarea name="previous_dictum_origin_pcl"
                                                  rows="3">{{$activity->pcl ? $activity->pcl->previous_dictum_origin_pcl : ''}}</textarea>
                                    </div>
                                </div>
                                <div class="two fields">
                                    <div class="field">
                                        <label>Descripción del Rol Laboral </label>
                                        <textarea name="labor_role_description"
                                                  rows="3">{{$activity->pcl ? $activity->pcl->labor_role_description : ''}}</textarea>
                                    </div>
                                </div>
                            </div>
                            <div class="title" id="areas-ocupacionales">
                                <i class="dropdown icon"></i> Otras areas ocupacionales
                            </div>
                            <div class="content" id="areas-ocupacionales-content">
                                <div class="three fields">
                                    <div class="field">
                                        <label>Aprendizaje y aplicación del conocimiento</label>
                                        <textarea name="valoration_learning_application_knowledge"
                                                  rows="3">{{$activity->pcl ? $activity->pcl->valoration_learning_application_knowledge : ''}}</textarea>
                                    </div>
                                    <div class="field">
                                        <label>Comunicación</label>
                                        <textarea name="valoration_communication"
                                                  rows="3">{{$activity->pcl ? $activity->pcl->valoration_communication : ''}}</textarea>
                                    </div>
                                    <div class="field">
                                        <label>Movilidad</label>
                                        <textarea name="valoration_mobility"
                                                  rows="3">{{$activity->pcl ? $activity->pcl->valoration_mobility : ''}}</textarea>
                                    </div>
                                </div>
                                <div class="three fields">
                                    <div class="field">
                                        <label>Cuidado Personal</label>
                                        <textarea name="valoration_personal_care"
                                                  rows="3">{{$activity->pcl ? $activity->pcl->valoration_personal_care : ''}}</textarea>
                                    </div>
                                    <div class="field">
                                        <label>Vida doméstica</label>
                                        <textarea name="valoration_domestic_life"
                                                  rows="3">{{$activity->pcl ? $activity->pcl->valoration_domestic_life : ''}}</textarea>
                                    </div>
                                </div>
                            </div>
                            <div class="title" id="ocupational_role_adults_description">
                                <i class="dropdown icon"></i> Rol ocupacional adultos mayores / niños y niñas mayores de
                                3 años /adolescentes
                            </div>
                            <div class="content" id="ocupational_role_adults_description_content">
                                <div class="required field">
                                    <label>ROL OCUPACIONAL ADULTOS MAYORES / NIÑOS Y NIÑAS MAYORES DE 3 AÑOS
                                        /ADOLESCENTES</label>
                                    <textarea name="ocucupational_role_adults_description"
                                              rows="6">{{$activity->pcl ? $activity->pcl->ocucupational_role_adults_description : ' '}}</textarea>
                                </div>
                            </div>
                            <div class="title" id="occupational_role_kids_description">
                                <i class="dropdown icon"></i>
                                ROL OCUPACIONAL DESARROLLO EVOLUTIVO NIÑOS Y NIÑAS MENORES DE 3 AÑOS
                            </div>
                            <div class="content" id="occupational_role_kids_description_content">
                                <div class="ui container">
                                    <div class="table-container">
                                        <table class="ui very compact small definition table">
                                            <thead>
                                            <tr>
                                                <th class="collapsing">Criterios según desarrollo neuroevoluctivo
                                                    para niños y niñas de 0 a 3 años. Actividad motriz
                                                </th>
                                                <th class="collapsing">0</th>
                                                <th class="collapsing">1</th>
                                                <th class="collapsing">2</th>
                                            </tr>
                                            </thead>
                                            <tbody>
                                            @foreach(App\Pcl::$KIDS_TABLES['T1'] as $k => $ti)
                                                <tr>
                                                    <td>{{$k + 1}}. {{$ti}}</td>
                                                    <td>
                                                        <div class="ui fitted checkbox">
                                                            <input name="T1[{{$k}}]" value="0"
                                                                   type="radio" {{$activity->pcl && $activity->pcl->tableKidValue('T1', $k) == '0' ? 'checked' : ''}}>
                                                            <label></label>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div class="ui fitted checkbox">
                                                            <input name="T1[{{$k}}]" value="1"
                                                                   type="radio" {{$activity->pcl && $activity->pcl->tableKidValue('T1', $k) == '1' ? 'checked' : ''}}>
                                                            <label></label>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div class="ui fitted checkbox">
                                                            <input name="T1[{{$k}}]" value="2"
                                                                   type="radio" {{$activity->pcl && $activity->pcl->tableKidValue('T1', $k) == '2' ? 'checked' : ''}}>
                                                            <label></label>
                                                        </div>
                                                    </td>
                                                </tr>
                                            @endforeach
                                            </tbody>
                                            <thead>
                                            <tr>
                                                <th class="collapsing">Criterios según desarrollo neuroevoluctivo
                                                    para niños y niñas de 0 a 3 años. Actividad adaptativa
                                                </th>
                                                <th class="collapsing">0</th>
                                                <th class="collapsing">1</th>
                                                <th class="collapsing">2</th>
                                            </tr>
                                            </thead>
                                            <tbody>
                                            @php $start = count(App\Pcl::$KIDS_TABLES['T1']) @endphp
                                            @foreach(App\Pcl::$KIDS_TABLES['T2'] as $k => $ti)
                                                <tr>
                                                    <td>{{$start + $k + 1}}. {{$ti}}</td>
                                                    <td>
                                                        <div class="ui fitted checkbox">
                                                            <input name="T2[{{$k}}]" value="0"
                                                                   type="radio" {{$activity->pcl && $activity->pcl->tableKidValue('T2', $k) == '0' ? 'checked' : ''}}>
                                                            <label></label>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div class="ui fitted checkbox">
                                                            <input name="T2[{{$k}}]" value="1"
                                                                   type="radio" {{$activity->pcl && $activity->pcl->tableKidValue('T2', $k) == '1' ? 'checked' : ''}}>
                                                            <label></label>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div class="ui fitted checkbox">
                                                            <input name="T2[{{$k}}]" value="2"
                                                                   type="radio" {{$activity->pcl && $activity->pcl->tableKidValue('T2', $k) == '2' ? 'checked' : ''}}>
                                                            <label></label>
                                                        </div>
                                                    </td>
                                                </tr>
                                            @endforeach
                                            </tbody>
                                            <tfoot>
                                            <tr>
                                                <th>Total por Columnas</th>
                                                <th id="total0">0</th>
                                                <th id="total1">0</th>
                                                <th id="total2">0</th>
                                            </tr>
                                            <tr>
                                                <th>Total General</th>
                                                <th colspan="3" id="totalAll">0</th>
                                            </tr>
                                            </tfoot>
                                        </table>
                                        <div class="fields">
                                            <input type="hidden" id="inputTotal0" name="total0" value="0">
                                            <input type="hidden" id="inputTotal1" name="total1" value="0">
                                            <input type="hidden" id="inputTotal2" name="total2" value="0">
                                            <input type="hidden" id="inputTotalAll" name="totalAll" value="0">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div id="70" class="title"><i class="dropdown icon"></i> Dictamen preliminar
                </div>
                <div class="content">
                    <div class="styled fluid accordion">
                        @if($activity->pcl && $activity->pcl->pcl_type == 3)
                            @include('services.pcl.form_pcl_917', array('activity' => $activity))
                        @elseif($activity->pcl && $activity->pcl->pcl_type == 1)
                            @include('services.pcl.form_pcl', array('activity' => $activity))
                        @else
                            @include('services.pcl.form_pcl_selection', array('activity' => $activity))
                        @endif
                    </div>
                </div>
                <div id="71" class="title"><i class="dropdown icon"></i> Solicitud de exámenes complementarios
                </div>
                <div class="content">
                    <div class="styled fluid accordion">
                        <div class="title"><i class="dropdown icon"></i>
                            Solicitud de exámenes
                        </div>
                        <div class="content">
                            <div style="margin-bottom: 5px;" class="fields">
                                <div class="seven wide field">
                                    <label>Documento</label>
                                </div>
                                <div class="eight wide field">
                                    <label>Observaciones</label>
                                </div>
                                <div class="one wide field">
                                    <a style="margin-top: -15px;" onclick="addPreliminaryOpinions();"
                                       class="ui basic small icon blue button"><i class="add icon"></i></a>
                                </div>
                            </div>
                            <div id="preliminary_opinion">
                                @if ($activity->pcl && count($activity->pcl->preliminary_opinions) > 0)
                                    @foreach ($activity->pcl->preliminary_opinions as $preliminary_opinions)
                                        <div class="fields">
                                            <input type="hidden" name="preliminary_opinions[id][]"
                                                   value="{{$preliminary_opinions->id}}">
                                            <div class="seven wide field">
                                                <div class="ui fluid selection dropdown">
                                                    <input name="preliminary_opinions[document_preliminary_opinion][]"
                                                           type="hidden"
                                                           value="{{$preliminary_opinions->document_preliminary_opinion}}">
                                                    <i class="dropdown icon"></i>
                                                    <div class="default text">Seleccione uno</div>
                                                    <div class="menu">
                                                        <div class="item"
                                                             data-value="Valoraciones médicas y/o conceptos clínicos - Necesarias para la calificación según el decreto aplicado">
                                                            Valoraciones médicas y/o conceptos clínicos - Necesarias para la calificación según el decreto aplicado
                                                        </div>
                                                        <div class="item"
                                                             data-value="Pruebas objetivas y/o documentos complementarios - Necesarias para la calificación según el decreto aplicado">
                                                            Pruebas objetivas y/o documentos complementarios - Necesarias para la calificación según el decreto aplicado
                                                        </div>
                                                        <div class="item"
                                                             data-value="Pruebas de laboratorio - Necesarias para la calificación según el decreto aplicado">
                                                            Pruebas de laboratorio - Necesarias para la calificación según el decreto aplicado
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="eight wide field">
                                                    <textarea
                                                            name="preliminary_opinions[document_preliminary_opinion_observation][]"
                                                            rows="6">{{$preliminary_opinions->document_preliminary_opinion_observation}}</textarea>
                                            </div>
                                            <div class="one wide field">
                                                <a class="ui red small icon basic button"><i
                                                            class="remove icon"></i></a>
                                            </div>
                                        </div>
                                    @endforeach
                                @else
                                    <div class="fields">
                                        <input type="hidden" name="preliminary_opinions[id][]">
                                        <div class="seven wide field">
                                            <div class="ui fluid selection dropdown">
                                                <input name="preliminary_opinions[document_preliminary_opinion][]"
                                                       type="hidden">
                                                <i class="dropdown icon"></i>
                                                <div class="default text">Seleccione uno</div>
                                                <div class="menu">
                                                    <div class="item"
                                                         data-value="Valoraciones médicas y/o conceptos clínicos - Necesarias para la calificación según el decreto aplicado">
                                                        Valoraciones médicas y/o conceptos clínicos - Necesarias para la calificación según el decreto aplicado
                                                    </div>
                                                    <div class="item"
                                                         data-value="Pruebas objetivas y/o documentos complementarios - Necesarias para la calificación según el decreto aplicado">
                                                        Pruebas objetivas y/o documentos complementarios - Necesarias para la calificación según el decreto aplicado
                                                    </div>
                                                    <div class="item"
                                                         data-value="Pruebas de laboratorio - Necesarias para la calificación según el decreto aplicado">
                                                        Pruebas de laboratorio - Necesarias para la calificación según el decreto aplicado
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="eight wide field">
                                                <textarea
                                                        name="preliminary_opinions[document_preliminary_opinion_observation][]"
                                                        rows="6"></textarea>
                                        </div>
                                        <div class="one wide field"></div>
                                    </div>
                                @endif
                            </div>
                            <div class="four fields">
                                <div class="field">
                                    <label>Fecha de envio comunicación </label>
                                    <input type="text" name="send_communication_date" class="datepicker"
                                           value="{{$activity->pcl ? $activity->pcl->send_communication_date : ''}}">
                                </div>
                            </div>
                            <div class="four fields">
                                <div class="field">
                                    <label>Estado de entrega comunicación</label>
                                    <div class="ui fluid selection dropdown">
                                        <input name="communication_state_deliver" type="hidden"
                                               value="{{$activity->pcl ? $activity->pcl->communication_state_deliver : ''}}">
                                        <i class="dropdown icon"></i>
                                        <div class="default text">Seleccione uno</div>
                                        <div class="menu">
                                            <div class="item" data-value="ENTREGA EFECTIVA">ENTREGA EFECTIVA</div>
                                            <div class="item" data-value="ENTREGA NO EFECTIVA">ENTREGA NO EFECTIVA</div>
                                            <div class="item" data-value="ENTREGA NO EFECTIVA-INFORMACIÓN INCORRECTA">
                                                ENTREGA NO EFECTIVA-INFORMACIÓN INCORRECTA
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="field">
                                    <label>Fecha entrega efectiva Sol. Exam </label>
                                    <input type="text" name="sol_doc_deliver_date" class="datepicker"
                                           id="effective_sol_document_date_exam"
                                           value="{{$activity->pcl ? $activity->pcl->sol_doc_deliver_date : ''}}">
                                </div>
                                <div class="field">
                                    <label>Fecha cumplimiento termino Sol Exam</label>
                                    <input type="text" name="sol_doc_term_accomplished_date" class="datepicker" id="sol_doc_term_accomplished_date"
                                           value="{{$activity->pcl ? $activity->pcl->sol_doc_term_accomplished_date : ''}}">
                                </div>
                                <div class="field">
                                    <label>Radican solicitud Prorroga Exam</label>
                                    <div class="ui fluid selection dropdown">
                                        <input name="prorrogation_request_radication" type="hidden"
                                               value="{{$activity->pcl ? $activity->pcl->prorrogation_request_radication : ''}}">
                                        <i class="dropdown icon"></i>
                                        <div class="default text">Seleccione uno</div>
                                        <div class="menu">
                                            <div class="item" data-value="SI">SI</div>
                                            <div class="item" data-value="NO">NO</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="title"><i class="dropdown icon"></i>
                            Solicitud de prorroga
                        </div>
                        <div class="content">
                            <div class="four fields">
                                <div class="field">
                                    <label>Fecha Rad prorroga </label>
                                    <input type="text" name="prorrogation_rad_date" class="datepicker"
                                           id="prorrogation_rad_date_exam"
                                           value="{{$activity->pcl ? $activity->pcl->prorrogation_rad_date : ''}}">
                                </div>
                                <div class="field">
                                    <label>No. Radicado Bizagi</label>
                                    <input type="text" name="bizagi_rad_number_prorrogation"
                                           value="{{$activity->pcl ? $activity->pcl->bizagi_rad_number_prorrogation : ''}}">
                                </div>
                                <div class="field">
                                    <label style="color: red">Fecha cumplimiento termino con prorroga</label>
                                    <input type="text" name="accomplished_date_prorrogation_term" class="datepicker"
                                           id="compliance_extension_date_exam"
                                           value="{{$activity->pcl ? $activity->pcl->accomplished_date_prorrogation_term : ''}}">
                                </div>
                            </div>
                        </div>
                        <div class="title"><i class="dropdown icon"></i>
                            Recepción documentos
                        </div>
                        <div class="content">
                            <div class="four fields">
                                <div class="field">
                                    <label>Fecha recepcion documentos</label>
                                    <input type="text" name="documents_reception_date" class="datepicker"
                                           value="{{$activity->pcl ? $activity->pcl->documents_reception_date : ''}}">
                                </div>
                                <div class="field">
                                    <label>No. Radicado Bizagi</label>
                                    <input type="text" name="bizagi_rad_number_doc_reception"
                                           value="{{$activity->pcl ? $activity->pcl->bizagi_rad_number_doc_reception : ''}}">
                                </div>
                                <div class="field">
                                    <label>Aporta examenes requeridos</label>
                                    <div class="ui fluid selection dropdown">
                                        <input name="required_exams_apportation" type="hidden"
                                               value="{{$activity->pcl ? $activity->pcl->required_exams_apportation : ''}}">
                                        <i class="dropdown icon"></i>
                                        <div class="default text">Seleccione uno</div>
                                        <div class="menu">
                                            <div class="item" data-value="SI">SI</div>
                                            <div class="item" data-value="NO">NO</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div id="74" class="title"><i class="dropdown icon"></i> Control calidad
                </div>
                <div class="content">
                    <div class="styled fluid accordion">
                        <div class="title"><i class="dropdown icon"></i>
                            Datos control de calidad
                        </div>
                        <div class="content">
                            <div class="four fields">
                                <div class="field">
                                    <label>Fecha de control de calidad</label>
                                    <input type="text" name="quality_control_date" class="datepicker"
                                           value="{{$activity->pcl ? $activity->pcl->quality_control_date : ''}}">
                                </div>
                                <div class="field">
                                    <label>Quien revisa </label>
                                    <input type="text" name="quality_control_reviewer"
                                           value="{{$activity->pcl ? $activity->pcl->quality_control_reviewer : ''}}">
                                </div>
                            </div>
                        </div>
                        <div class="title"><i class="dropdown icon"></i>
                            Criterios de revisión
                        </div>
                        <div class="content">
                            <div class="four fields">
                            </div>
                        </div>
                        <div class="title"><i class="dropdown icon"></i>
                            Solicitud de examenes
                        </div>
                        <div class="content">
                            <div style="margin-bottom: 5px;" class="fields">
                                <div class="seven wide field">
                                    <label>Documento</label>
                                </div>
                                <div class="eight wide field">
                                    <label>Observaciones</label>
                                </div>
                                <div class="one wide field">
                                    <a style="margin-top: -15px;" onclick="addQualityControls();"
                                       class="ui basic small icon blue button"><i class="add icon"></i></a>
                                </div>
                            </div>
                            <div id="quality_control">
                                @if ($activity->pcl && count($activity->pcl->quality_controls) > 0)
                                    @foreach ($activity->pcl->quality_controls as $quality_control)
                                        <div class="fields">
                                            <input type="hidden" name="quality_controls[id][]"
                                                   value="{{$quality_control->id}}">
                                            <div class="seven wide field">
                                                <div class="ui fluid selection dropdown">
                                                    <input name="quality_controls[document_quality_control][]"
                                                           type="hidden"
                                                           value="{{$quality_control->document_quality_control}}">
                                                    <i class="dropdown icon"></i>
                                                    <div class="default text">Seleccione uno</div>
                                                    <div class="menu">
                                                        <div class="item"
                                                             data-value="Valoraciones médicas y/o conceptos clínicos - Necesarias para la calificación según el decreto aplicado">
                                                            Valoraciones médicas y/o conceptos clínicos - Necesarias para la calificación según el decreto aplicado
                                                        </div>
                                                        <div class="item"
                                                             data-value="Pruebas objetivas y/o documentos complementarios - Necesarias para la calificación según el decreto aplicado">
                                                            Pruebas objetivas y/o documentos complementarios - Necesarias para la calificación según el decreto aplicado
                                                        </div>
                                                        <div class="item"
                                                             data-value="Pruebas de laboratorio - Necesarias para la calificación según el decreto aplicado">
                                                            Pruebas de laboratorio - Necesarias para la calificación según el decreto aplicado
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="eight wide field">
                                                    <textarea
                                                            name="quality_controls[document_quality_control_observation][]"
                                                            rows="6">{{$quality_control->document_quality_control_observation}}</textarea>
                                            </div>
                                            <div class="one wide field">
                                                <a class="ui red small icon basic button"><i
                                                            class="remove icon"></i></a>
                                            </div>
                                        </div>
                                    @endforeach
                                @else
                                    <div class="fields">
                                        <input type="hidden" name="quality_controls[id][]">
                                        <div class="seven wide field">
                                            <div class="ui fluid selection dropdown">
                                                <input name="quality_controls[document_quality_control][]"
                                                       type="hidden">
                                                <i class="dropdown icon"></i>
                                                <div class="default text">Seleccione uno</div>
                                                <div class="menu">
                                                    <div class="item"
                                                         data-value="Valoraciones médicas y/o conceptos clínicos - Necesarias para la calificación según el decreto aplicado">
                                                        Valoraciones médicas y/o conceptos clínicos - Necesarias para la calificación según el decreto aplicado
                                                    </div>
                                                    <div class="item"
                                                         data-value="Pruebas objetivas y/o documentos complementarios - Necesarias para la calificación según el decreto aplicado">
                                                        Pruebas objetivas y/o documentos complementarios - Necesarias para la calificación según el decreto aplicado
                                                    </div>
                                                    <div class="item"
                                                         data-value="Pruebas de laboratorio - Necesarias para la calificación según el decreto aplicado">
                                                        Pruebas de laboratorio - Necesarias para la calificación según el decreto aplicado
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="eight wide field">
                                                <textarea
                                                        name="quality_controls[document_quality_control_observation][]"
                                                        rows="6"></textarea>
                                        </div>
                                        <div class="one wide field"></div>
                                    </div>
                                @endif
                            </div>
                            <div class="four fields">
                                <div class="field">
                                    <label>Fecha de envio comunicación </label>
                                    <input type="text" name="quality_send_communication_date" class="datepicker"
                                           value="{{$activity->pcl ? $activity->pcl->quality_send_communication_date : ''}}">
                                </div>
                            </div>
                            <div class="four fields">
                                <div class="field">
                                    <label>Estado de entrega comunicación</label>
                                    <div class="ui fluid selection dropdown">
                                        <input name="quality_communication_state_deliver" type="hidden"
                                               value="{{$activity->pcl ? $activity->pcl->quality_communication_state_deliver : ''}}">
                                        <i class="dropdown icon"></i>
                                        <div class="default text">Seleccione uno</div>
                                        <div class="menu">
                                            <div class="item" data-value="Entrega Efectiva">Entrega Efectiva</div>
                                            <div class="item" data-value="Entrega no efectiva">Entrega no efectiva</div>
                                            <div class="item" data-value="Entrega no efectiva-Información incorrecta">
                                                Entrega no efectiva-Información incorrecta
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="field">
                                    <label>Fecha entrega efectiva Sol. Exam </label>
                                    <input type="text" name="quality_deliver_date" class="datepicker"
                                           value="{{$activity->pcl ? $activity->pcl->quality_deliver_date : ''}}">
                                </div>
                                <div class="field">
                                    <label>Fecha cumplimiento termino Sol Exam</label>
                                    <input type="text" name="quality_accomplished_date" class="datepicker"
                                           value="{{$activity->pcl ? $activity->pcl->quality_accomplished_date : ''}}">
                                </div>
                                <div class="field">
                                    <label>Radican solicitud Prorroga</label>
                                    <div class="ui fluid selection dropdown">
                                        <input name="quality_prorrogation_request_radication" type="hidden"
                                               value="{{$activity->pcl ? $activity->pcl->quality_prorrogation_request_radication : ''}}">
                                        <i class="dropdown icon"></i>
                                        <div class="default text">Seleccione uno</div>
                                        <div class="menu">
                                            <div class="item" data-value="SI">SI</div>
                                            <div class="item" data-value="NO">NO</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="title"><i class="dropdown icon"></i>
                            Solicitud de prorroga
                        </div>
                        <div class="content">
                            <div class="four fields">
                                <div class="field">
                                    <label>Fecha Rad prorroga </label>
                                    <input type="text" name="quality_rad_date" class="datepicker"
                                           value="{{$activity->pcl ? $activity->pcl->quality_rad_date : ''}}">
                                </div>
                                <div class="field">
                                    <label>No. Radicado Bizagi</label>
                                    <input type="text" name="quality_bizagi_rad_number"
                                           value="{{$activity->pcl ? $activity->pcl->quality_bizagi_rad_number : ''}}">
                                </div>
                                <div class="field">
                                    <label>Fecha cumplimiento termino con prorroga</label>
                                    <input type="text" name="quality_accomplished_prororogation_date" class="datepicker"
                                           value="{{$activity->pcl ? $activity->pcl->quality_accomplished_prororogation_date : ''}}">
                                </div>
                            </div>
                        </div>
                        <div class="title"><i class="dropdown icon"></i>
                            Recepción documentos
                        </div>
                        <div class="content">
                            <div class="four fields">
                                <div class="field">
                                    <label>Fecha recepcion documentos</label>
                                    <input type="text" name="quality_documents_reception_date" class="datepicker"
                                           value="{{$activity->pcl ? $activity->pcl->quality_documents_reception_date : ''}}">
                                </div>
                                <div class="field">
                                    <label>No. Radicado Bizagi</label>
                                    <input type="text" name="quality_bizagi_rad_number_doc_reception"
                                           value="{{$activity->pcl ? $activity->pcl->quality_bizagi_rad_number_doc_reception : ''}}">
                                </div>
                                <div class="field">
                                    <label>Aporta examenes requeridos</label>
                                    <div class="ui fluid selection dropdown">
                                        <input name="quality_required_exams_apportation" type="hidden"
                                               value="{{$activity->pcl ? $activity->pcl->quality_required_exams_apportation : ''}}">
                                        <i class="dropdown icon"></i>
                                        <div class="default text">Seleccione uno</div>
                                        <div class="menu">
                                            <div class="item" data-value="SI">SI</div>
                                            <div class="item" data-value="NO">NO</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="title"><i class="dropdown icon"></i>
                            Resultado auditoria
                        </div>
                        <div class="content">
                            <div class="four fields">
                                <div class="field">
                                    <label>Resultado auditoria</label>
                                    <div class="ui fluid selection dropdown">
                                        <input name="quality_audit_result" type="hidden"
                                               value="{{$activity->pcl ? $activity->pcl->quality_audit_result : ''}}">
                                        <i class="dropdown icon"></i>
                                        <div class="default text">Seleccione uno</div>
                                        <div class="menu">
                                            <div class="item" data-value="Aprobado">Aprobado</div>
                                            <div class="item" data-value="Devuelto">Devuelto</div>
                                            <div class="item" data-value="Rechazar">Rechazar</div>
                                            <div class="item" data-value="Solicitar examanes">Solicitar examanes</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="field">
                                    <label>Observaciones</label>
                                    <textarea name="quality_audit_result_observation"
                                              rows="6">{{$activity->pcl ? $activity->pcl->quality_audit_result_observation : ''}}</textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div id="72" class="title"><i class="dropdown icon"></i>
                    Auditoria colpensiones
                </div>
                <div class="content">
                    <div class="styled fluid accordion">
                        <div class="title"><i class="dropdown icon"></i>
                            Datos control de calidad
                        </div>
                        <div class="content">
                            <div class="four fields">
                                <div class="field">
                                    <label>Fecha de control de calidad</label>
                                    <input type="text" name="audit_quality_control_date" class="datepicker"
                                           value="{{$activity->pcl ? $activity->pcl->audit_quality_control_date : ''}}">
                                </div>
                                <div class="field">
                                    <label>Quien revisa </label>
                                    <input type="text" name="audit_quality_control_reviewer"
                                           value="{{$activity->pcl ? $activity->pcl->audit_quality_control_reviewer : ''}}">
                                </div>
                            </div>
                        </div>
                        <div class="title"><i class="dropdown icon"></i>
                            Criterios de revisión
                        </div>
                        <div class="content">
                            <div class="four fields">
                            </div>
                        </div>
                        <div class="title"><i class="dropdown icon"></i>
                            Resultado auditoria
                        </div>
                        <div class="content">
                            <div class="four fields">
                                <div class="field">
                                    <label>Resultado auditoria</label>
                                    <div class="ui fluid selection dropdown">
                                        <input name="audit_quality_audit_result" type="hidden"
                                               value="{{$activity->pcl ? $activity->pcl->audit_quality_audit_result : ''}}">
                                        <i class="dropdown icon"></i>
                                        <div class="default text">Seleccione uno</div>
                                        <div class="menu">
                                            <div class="item" data-value="Aprobado">Aprobado</div>
                                            <div class="item" data-value="Devuelto">Devuelto</div>
                                            <div class="item" data-value="Rechazar">Rechazar</div>
                                            <div class="item" data-value="Solicitar examanes">Solicitar examanes</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="field">
                                    <label>Observaciones</label>
                                    <textarea name="audit_quality_audit_result_observation"
                                              rows="6">{{$activity->pcl ? $activity->pcl->audit_quality_audit_result_observation : ''}}</textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                @php
                    $requiredActions = collect([486,503,548,549,609,383,384,386]);
                    $activityActions = $activity->activity_actions->pluck('action_id');
                @endphp
                @if($requiredActions->contains(function($value) use ($activityActions) { return $activityActions->contains($value); }))
                    <div id="129" class="title"><i class="dropdown icon"></i>
                        Notificaciones Comunicacion inicio trámite perdidad capacidad laboral
                    </div>
                    <div class="content">
                        <div class="four fields">
                            <div class="required field">
                                <label>EPS</label>
                                <div class="ui selection dropdown">
                                    <input type="hidden" name="n_csp_eps_entity"
                                           value="{{$activity->pcl ? $activity->pcl->n_csp_eps_entity : ''}}">
                                    <i class="dropdown icon"></i>
                                    <div class="default text">Seleccion uno</div>
                                    <div class="menu">
                                        @foreach($EPS_LIST as $k => $v)
                                            <div class="item" data-value="{{$k}}">{{$v}}</div>
                                        @endforeach
                                    </div>
                                </div>
                            </div>
                            <div class="required field">
                                <label>ARL</label>
                                <div class="ui selection dropdown">
                                    <input type="hidden" name="n_csp_arl_entity"
                                           value="{{$activity->pcl ? $activity->pcl->n_csp_arl_entity : ''}}">
                                    <i class="dropdown icon"></i>
                                    <div class="default text">Seleccion uno</div>
                                    <div class="menu">
                                        @foreach($ARL_LIST as $k => $v)
                                            <div class="item" data-value="{{$k}}">{{$v}}</div>
                                        @endforeach
                                    </div>
                                </div>
                            </div>
                            <div class="required field">
                                <label>SD Solicitud de notificacion</label>
                                <input type="text" name="n_csp_sd_notification_request"
                                       value="{{$activity->pcl ? $activity->pcl->n_csp_sd_notification_request : ''}}">
                            </div>
                            <div class="required field">
                                <label>Fecha solicitud</label>
                                <input type="text" name="n_csp_application_date" class="datepicker"
                                       value="{{$activity->pcl ? $activity->pcl->n_csp_application_date : ''}}">
                            </div>
                        </div>
                        <div class="four fields">
                            <div class="required field">
                                <label>Rad Soportes Notificacion</label>
                                <input type="text" name="n_csp_rad_supports_notification"
                                       value="{{$activity->pcl ? $activity->pcl->n_csp_rad_supports_notification : ''}}">
                            </div>
                            <div class="required field">
                                <label>Fecha creacion soporte notificacion BZG</label>
                                <input type="text" name="n_csp_bzg_notification_support_creation_date"
                                       class="datepicker"
                                       value="{{$activity->pcl ? $activity->pcl->n_csp_bzg_notification_support_creation_date : ''}}">
                            </div>
                            <div class="required field">
                                <label>Medio de envio</label>
                                <div class="ui fluid selection dropdown">
                                    <input name="n_csp_average_costs" type="hidden"
                                           value="{{$activity->pcl ? $activity->pcl->n_csp_average_costs : ''}}">
                                    <i class="dropdown icon"></i>
                                    <div class="default text">Seleccione uno</div>
                                    <div class="menu">
                                        <div class="item" data-value="CORREO ELECTRONICO">CORREO ELECTRONICO
                                        </div>
                                        <div class="item" data-value="CORRESPONDENCIA">CORRESPONDENCIA</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                @endif
                @php
                    $requiredActionDictums = collect([513, 521]);
                    $activityActionDictums = $activity->activity_actions->pluck('action_id');
                @endphp
                @if($requiredActionDictums->contains(function($value) use ($activityActionDictums) { return $activityActionDictums->contains($value); }))
                    <div id="130" class="title"><i class="dropdown icon"></i>
                        Notificaciones de dictamen
                    </div>
                    <div class="content">
                        <div class="four fields">
                            <div class="required field">
                                <label>Parte interesada</label>
                                <div class="ui fluid selection dropdown">
                                    <input name="n_attorney_interested_part" type="hidden"
                                           value="{{$activity->pcl ? $activity->pcl->n_attorney_interested_part : ''}}">
                                    <i class="dropdown icon"></i>
                                    <div class="default text">Seleccione uno</div>
                                    <div class="menu">
                                        <div class="item" data-value="APODERADO">APODERADO</div>
                                        <div class="item" data-value="CIUDADANO">CIUDADANO</div>
                                        <div class="item" data-value="TERCERO AUTORIZADO">TERCERO AUTORIZADO
                                        </div>
                                        <div class="item" data-value="BENEFICIARIO">BENEFICIARIO</div>
                                    </div>
                                </div>
                            </div>
                            <div class="required field">
                                <label>nombre de tercero o apoderado</label>
                                <input type="text" name="n_attorney_name_third_party_representative"
                                       value="{{$activity->pcl ? $activity->pcl->n_attorney_name_third_party_representative : ''}}">
                            </div>
                            <div class="required field">
                                <label>Direccion</label>
                                <input type="text" name="n_attorney_address"
                                       value="{{$activity->pcl ? $activity->pcl->n_attorney_address : ''}}">
                            </div>
                            <div class="required field">
                                <label>Telefono</label>
                                <input type="text" name="n_attorney_phone"
                                       value="{{$activity->pcl ? $activity->pcl->n_attorney_phone : ''}}">
                            </div>
                        </div>
                        <div class="four fields">
                            <div class="required field">
                                <label>Ciudad</label>
                                <input type="text" name="n_attorney_city"
                                       value="{{$activity->pcl ? $activity->pcl->n_attorney_city : ''}}">
                            </div>
                            <div class="required field">
                                <label>Departamento</label>
                                <input type="text" name="n_attorney_department"
                                       value="{{$activity->pcl ? $activity->pcl->n_attorney_department : ''}}">
                            </div>
                            <div class="required field">
                                <label>Autoriza envio por Correo electronico?</label>
                                <div class="ui fluid selection dropdown">
                                    <input name="n_attorney_allow_sending_email" type="hidden"
                                           value="{{$activity->pcl ? $activity->pcl->n_attorney_allow_sending_email : ''}}">
                                    <i class="dropdown icon"></i>
                                    <div class="default text">Seleccione uno</div>
                                    <div class="menu">
                                        <div class="item" data-value="SI">SI</div>
                                        <div class="item" data-value="NO">NO</div>
                                    </div>
                                </div>
                            </div>
                            <div class="required field">
                                <label>Correo electronico</label>
                                <input type="email" name="n_attorney_email"
                                       value="{{$activity->pcl ? $activity->pcl->n_attorney_email : ''}}">
                            </div>
                        </div>
                        <div class="fields">
                            <div class="field wide sixteen">
                                <label>Parrafo automatico</label>
                                <textarea name="n_attorney_automatic_paragraph"
                                          rows="3">{{$activity->pcl && $activity->pcl->n_attorney_automatic_paragraph ? $activity->pcl->n_attorney_automatic_paragraph : 'De manera atenta me permito solicitar su gestión al fin de proceder con la notificación del ____ de ____ CALIFICADO: ____ Identificación : ____ DIRECCIÓN: ____ Afiliado: ____ IDENTIFICACIÓN: ____. Tipo notificacion: ____'}}</textarea>
                            </div>
                        </div>
                        <div class="four fields">
                            <div class="required field">
                                <label>Rad_Padre</label>
                                <input type="text" name="n_attorney_rad_father"
                                       value="{{$activity->pcl ? $activity->pcl->n_attorney_rad_father : ''}}">
                            </div>
                            <div class="required field">
                                <label>Fecha creacion RI Padre</label>
                                <input type="text" name="n_attorney_ri_father_creation_date" class="datepicker"
                                       value="{{$activity->pcl ? $activity->pcl->n_attorney_ri_father_creation_date : ''}}">
                            </div>
                            <div class="required field">
                                <label>Rad RI respuesta</label>
                                <input type="text" name="n_attorney_rad_ri_answer"
                                       value="{{$activity->pcl ? $activity->pcl->n_attorney_rad_ri_answer : ''}}">
                            </div>
                            <div class="required field">
                                <label>Lote de envio DAS</label>
                                <input type="text" name="n_attorney_das_shipping_lot" readonly
                                       value="{{$activity->pcl ? $activity->pcl->n_attorney_das_shipping_lot : ''}}">
                            </div>
                        </div>
                        <div class="four fields">
                            <div class="required field">
                                <label>Fecha envio DAS</label>
                                <input type="text" name="n_attorney_das_shipping_date" readonly
                                       value="{{$activity->pcl ? $activity->pcl->n_attorney_das_shipping_date : ''}}">
                            </div>
                            <div class="required field">
                                <label>Medio de envio</label>
                                <div class="ui fluid selection dropdown">
                                    <input name="n_attorney_average_costs" type="hidden"
                                           value="{{$activity->pcl ? $activity->pcl->n_attorney_average_costs : ''}}">
                                    <i class="dropdown icon"></i>
                                    <div class="default text">Seleccione uno</div>
                                    <div class="menu">
                                        <div class="item" data-value="CORREO ELECTRONICO">CORREO ELECTRONICO
                                        </div>
                                        <div class="item" data-value="CORRESPONDENCIA">CORRESPONDENCIA</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <h4 class="ui horizontal divider">
                            parte interesada - EPS
                        </h4>
                        <div class="four fields">
                            <div class="required field">
                                <label>EPS</label>
                                <div class="ui selection dropdown">
                                    <input type="hidden" name="n_eps_entity"
                                           value="{{$activity->pcl ? $activity->pcl->n_eps_entity : ''}}">
                                    <i class="dropdown icon"></i>
                                    <div class="default text">Seleccion uno</div>
                                    <div class="menu">
                                        @foreach($EPS_LIST as $k => $v)
                                            <div class="item" data-value="{{$k}}">{{$v}}</div>
                                        @endforeach
                                    </div>
                                </div>
                            </div>
                            <div class="required field">
                                <label>SD Solicitud de notificacion</label>
                                <input type="text" name="n_eps_sd_notification_request"
                                       value="{{$activity->pcl ? $activity->pcl->n_eps_sd_notification_request : ''}}">
                            </div>
                            <div class="required field">
                                <label>Fecha solicitud</label>
                                <input type="text" name="n_eps_application_date" class="datepicker"
                                       value="{{$activity->pcl ? $activity->pcl->n_eps_application_date : ''}}">
                            </div>
                            <div class="required field">
                                <label>Rad Soportes Notificacion</label>
                                <input type="text" name="n_eps_rad_supports_notification"
                                       value="{{$activity->pcl ? $activity->pcl->n_eps_rad_supports_notification : ''}}">
                            </div>
                        </div>
                        <div class="four fields">
                            <div class="required field">
                                <label>Fecha creacion soporte notificacion BZG</label>
                                <input type="text" name="n_eps_bzg_notification_support_creation_date"
                                       class="datepicker"
                                       value="{{$activity->pcl ? $activity->pcl->n_eps_bzg_notification_support_creation_date : ''}}">
                            </div>
                            <div class="required field">
                                <label>Medio de envio</label>
                                <div class="ui fluid selection dropdown">
                                    <input name="n_eps_average_costs" type="hidden"
                                           value="{{$activity->pcl ? $activity->pcl->n_eps_average_costs : ''}}">
                                    <i class="dropdown icon"></i>
                                    <div class="default text">Seleccione uno</div>
                                    <div class="menu">
                                        <div class="item" data-value="CORREO ELECTRONICO">CORREO ELECTRONICO
                                        </div>
                                        <div class="item" data-value="CORRESPONDENCIA">CORRESPONDENCIA</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <h4 class="ui horizontal divider">
                            parte interesada - ARL
                        </h4>
                        <div class="four fields">
                            <div class="required field">
                                <label>Tipo afiliado a ARL</label>
                                <div class="ui fluid selection dropdown">
                                    <input name="n_arl_type_affiliate" type="hidden"
                                           value="{{$activity->pcl ? $activity->pcl->n_arl_type_affiliate : ''}}">
                                    <i class="dropdown icon"></i>
                                    <div class="default text">Seleccione uno</div>
                                    <div class="menu">
                                        <div class="item" data-value="COTIZANTE">COTIZANTE</div>
                                        <div class="item" data-value="NO REGISTRA">NO REGISTRA</div>
                                    </div>
                                </div>
                            </div>
                            <div class="required field">
                                <label>ARL</label>
                                <div class="ui selection dropdown">
                                    <input type="hidden" name="n_arl_entity"
                                           value="{{$activity->pcl ? $activity->pcl->n_arl_entity : ''}}">
                                    <i class="dropdown icon"></i>
                                    <div class="default text">Seleccion uno</div>
                                    <div class="menu">
                                        @foreach($ARL_LIST as $k => $v)
                                            <div class="item" data-value="{{$k}}">{{$v}}</div>
                                        @endforeach
                                    </div>
                                </div>
                            </div>
                            <div class="required field">
                                <label>SD Solicitud de notificacion</label>
                                <input type="text" name="n_arl_sd_notification_request"
                                       value="{{$activity->pcl ? $activity->pcl->n_arl_sd_notification_request : ''}}">
                            </div>
                            <div class="required field">
                                <label>Fecha solicitud</label>
                                <input type="text" name="n_arl_application_date" class="datepicker"
                                       value="{{$activity->pcl ? $activity->pcl->n_arl_application_date : ''}}">
                            </div>
                        </div>
                        <div class="four fields">
                            <div class="required field">
                                <label>Rad Soportes Notificacion</label>
                                <input type="text" name="n_arl_rad_supports_notification"
                                       value="{{$activity->pcl ? $activity->pcl->n_arl_rad_supports_notification : ''}}">
                            </div>
                            <div class="required field">
                                <label>Fecha creacion soporte notificacion BZG</label>
                                <input type="text" name="n_arl_bzg_notification_support_creation_date"
                                       class="datepicker"
                                       value="{{$activity->pcl ? $activity->pcl->n_arl_bzg_notification_support_creation_date : ''}}">
                            </div>
                            <div class="required field">
                                <label>Medio de envio</label>
                                <div class="ui fluid selection dropdown">
                                    <input name="n_arl_average_costs" type="hidden"
                                           value="{{$activity->pcl ? $activity->pcl->n_arl_average_costs : ''}}">
                                    <i class="dropdown icon"></i>
                                    <div class="default text">Seleccione uno</div>
                                    <div class="menu">
                                        <div class="item" data-value="CORREO ELECTRONICO">CORREO ELECTRONICO
                                        </div>
                                        <div class="item" data-value="CORRESPONDENCIA">CORRESPONDENCIA</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <h4 class="ui horizontal divider">
                            parte interesada - EMPLEADOR
                        </h4>
                        <div class="four fields">
                            <div class="required field">
                                <label>Tipo afiliado</label>
                                <div class="ui fluid selection dropdown">
                                    <input name="n_emp_type_affiliate" type="hidden"
                                           value="{{$activity->pcl ? $activity->pcl->n_emp_type_affiliate : ''}}">
                                    <i class="dropdown icon"></i>
                                    <div class="default text">Seleccione uno</div>
                                    <div class="menu">
                                        <div class="item" data-value="INDEPENDIENTE">INDEPENDIENTE</div>
                                        <div class="item" data-value="DEPENDIENTE">DEPENDIENTE</div>
                                    </div>
                                </div>
                            </div>
                            <div class="required field">
                                <label>Nombre de tercero o apoderado</label>
                                <input type="text" name="n_emp_name_third_party_representative"
                                       value="{{$activity->pcl ? $activity->pcl->n_emp_name_third_party_representative : ''}}">
                            </div>
                            <div class="required field">
                                <label>Direccion</label>
                                <input type="text" name="n_emp_address"
                                       value="{{$activity->pcl ? $activity->pcl->n_emp_address : ''}}">
                            </div>
                            <div class="required field">
                                <label>Telefono</label>
                                <input type="text" name="n_emp_phone"
                                       value="{{$activity->pcl ? $activity->pcl->n_emp_phone : ''}}">
                            </div>
                        </div>
                        <div class="four fields">
                            <div class="required field">
                                <label>Ciudad</label>
                                <input type="text" name="n_emp_city"
                                       value="{{$activity->pcl ? $activity->pcl->n_emp_city : ''}}">
                            </div>
                            <div class="required field">
                                <label>Departamento</label>
                                <input type="text" name="n_emp_department"
                                       value="{{$activity->pcl ? $activity->pcl->n_emp_department : ''}}">
                            </div>
                            <div class="required field">
                                <label>Autoriza envio por Correo electronico?</label>
                                <div class="ui fluid selection dropdown">
                                    <input name="n_emp_allow_sending_email" type="hidden"
                                           value="{{$activity->pcl ? $activity->pcl->n_emp_allow_sending_email : ''}}">
                                    <i class="dropdown icon"></i>
                                    <div class="default text">Seleccione uno</div>
                                    <div class="menu">
                                        <div class="item" data-value="SI">SI</div>
                                        <div class="item" data-value="NO">NO</div>
                                    </div>
                                </div>
                            </div>
                            <div class="required field">
                                <label>Correo electronico</label>
                                <input type="email" name="n_emp_email"
                                       value="{{$activity->pcl ? $activity->pcl->n_emp_email : ''}}">
                            </div>
                        </div>
                        <div class="four fields">
                            <div class="required field">
                                <label>SD Solicitud de notificacion</label>
                                <input type="text" name="n_emp_sd_notification_request"
                                       value="{{$activity->pcl ? $activity->pcl->n_emp_sd_notification_request : ''}}">
                            </div>
                            <div class="required field">
                                <label>Fecha solicitud</label>
                                <input type="text" name="n_emp_application_date" class="datepicker"
                                       value="{{$activity->pcl ? $activity->pcl->n_emp_application_date : ''}}">
                            </div>
                            <div class="required field">
                                <label>Rad Soportes Notificacion</label>
                                <input type="text" name="n_emp_rad_supports_notification"
                                       value="{{$activity->pcl ? $activity->pcl->n_emp_rad_supports_notification : ''}}">
                            </div>
                            <div class="required field">
                                <label>Fecha creacion soporte notificacion BZG</label>
                                <input type="text" name="n_emp_bzg_notification_support_creation_date"
                                       class="datepicker"
                                       value="{{$activity->pcl ? $activity->pcl->n_emp_bzg_notification_support_creation_date : ''}}">
                            </div>
                        </div>
                        <div class="four fields">
                            <div class="required field">
                                <label>Medio de envio</label>
                                <div class="ui fluid selection dropdown">
                                    <input name="n_emp_average_costs" type="hidden"
                                           value="{{$activity->pcl ? $activity->pcl->n_emp_average_costs : ''}}">
                                    <i class="dropdown icon"></i>
                                    <div class="default text">Seleccione uno</div>
                                    <div class="menu">
                                        <div class="item" data-value="CORREO ELECTRONICO">CORREO ELECTRONICO
                                        </div>
                                        <div class="item" data-value="CORRESPONDENCIA">CORRESPONDENCIA</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                @endif

            </div>
            <div class="ui basic segment">
                <div class="ui error message"></div>
                <div class="fields">
                    <div class="four wide field">
                        <button id="button_save" class="ui green fluid button"><i class="save icon"></i> Guardar
                        </button>
                    </div>
                    <div class="field">
                        @if(!is_object($activity->pcl) || !$activity->pcl->pcl_type)
                            <div class="ui basic medium red label">
                                <i class="low vision icon"></i>Seleccione una TIPO DE PCL primero
                            </div>
                        @else
                            <a href="{{secure_url('/servicio/' . $activity->id . '/pcl/pdf')}}"
                               class="ui basic small button" target="_blank"
                               onclick="event.preventDefault(); generatePDF();">
                                <i class="file pdf outline red icon"></i> Vista previa Dictamen PCL
                            </a>
                        @endif
                    </div>
                    <div class="field">
                        @if(!is_object($activity->pcl) || !$activity->pcl->valoration_valoration_type)
                            <div class="ui basic medium red label">
                                <i class="low vision icon"></i>Seleccione una TIPO DE VALORACION primero
                            </div>
                        @else
                            <a href="{{secure_url('/servicio/' . $activity->id . '/pcl/pdf_valoration')}}"
                               class="ui basic small button" target="_blank"
                               onclick="event.preventDefault(); generatePDFValoration();">
                                <i class="file pdf outline red icon"></i> Vista previa Valoracion Médica
                            </a>
                        @endif
                    </div>
                    @if(Auth::user()->isAdmin())
                        <div class="field">
                            @if(!is_object($activity->pcl) || !$activity->pcl->valoration_valoration_type)
                                <a href="{{secure_url('/servicio/' . $activity->id . '/pcl/pdf_rejection')}}"
                                   class="ui basic small button" target="_blank"
                                   onclick="event.preventDefault(); generatePDFRejection();">
                                    <i class="file pdf outline red icon"></i> Vista previa Rechazo
                                </a>
                            @endif
                        </div>
                    @endif
                    <div class="five wide field ">
                        <a href="{{secure_url('/servicio/' . $activity->id)}}" class="ui basic small button"><i
                                    class="arrow left icon"></i> Volver a la actividad</a>
                    </div>
                </div>
            </div>
            {{csrf_field()}}
        </form>
    </div>

    {{--MODELS--}}

    <!-- CONTACTABILITY MODEL -->
    <div id="contactability_model" style="display: none;" class="fields">
        <input type="hidden" name="contactabilities[id][]">
        <div class="four wide field">
            <input name="contactabilities[contact_date][]"
                   type="text" class="datepicker3">
        </div>
        <div class="four wide field">
            <input name="contactabilities[contact_phone][]">
        </div>
        <div class="six wide field">
            <div class="ui fluid selection dropdown">
                <input name="contactabilities[communication_state][]" type="hidden">
                <i class="dropdown icon"></i>
                <div class="default text">Seleccione uno</div>
                <div class="menu">
                    <div class="item" data-value="Contesta">Contesta</div>
                    <div class="item" data-value="No contesta">No contesta</div>
                    <div class="item" data-value="Equivocado">Equivocado</div>
                    <div class="item" data-value="Buzon">Buzon</div>
                </div>
            </div>
        </div>
        <div class="one wide field">
            <a class="ui red small icon basic button"><i class="remove icon"></i></a>
        </div>
    </div>
    <!-- END: CONTACTABILITY MODEL -->

    <!-- INTERCONSULTATION MODEL -->
    <div id="interconsultation_model" style="display: none;" class="fields">
        <input type="hidden" name="interconsultations[id][]">
        <div class="eight wide field">
            <textarea name="interconsultations[exam_profesional][]" rows="2"></textarea>
        </div>
        <div class="eight wide field">
            <input name="interconsultations[exam_date][]"
                   type="text" class="datepicker2">
        </div>
        <div class="eight wide field">
            <input name="interconsultations[exam_name][]"
                   type="text">
        </div>
        <div class="ten wide field">
                                <textarea
                                        name="interconsultations[exam_result][]"
                                        rows="2"></textarea>
        </div>
        <div class="ten wide field">
                                <textarea
                                        name="interconsultations[exam_observation][]"
                                        rows="2"> </textarea>
        </div>
        <div class="one wide field">
            <a class="ui red small icon basic button"><i class="remove icon"></i></a>
        </div>
    </div>
    <!-- END: INTERCONSULTATION MODEL -->

    <!-- DEFICIENCE MODEL -->
    <div id="deficience_model" style="display: none;" class="fields">
        <input name="deficiences[id][]" type="hidden">
        <div class="eight wide field">
            <textarea name="deficiences[name][]" rows="1"></textarea>
        </div>
        <div class="three wide field">
            <input name="deficiences[table][]" type="text">
        </div>
        <div class="three wide field">
            <input name="deficiences[percentage][]" step="any" type="number">
        </div>
        <div class="twelve wide field">
            <div class="six fields">
                <div class="field">
                    <div class="ui fluid selection dropdown">
                        <input name="deficiences[class][]" type="hidden">
                        <i class="dropdown icon"></i>
                        <div class="default text"></div>
                        <div class="menu">
                            <div class="item" data-value=""></div>
                            @for($i = 0; $i < 5; $i++)
                                <div class="item" data-value="{{$i}}">{{$i}}</div>
                            @endfor
                        </div>
                    </div>
                </div>
                <div class="field">
                    <div class="ui fluid selection dropdown">
                        <input name="deficiences[cfpfu][]" type="hidden">
                        <i class="dropdown icon"></i>
                        <div class="default text"></div>
                        <div class="menu">
                            <div class="item" data-value=""></div>
                            @for($i = 0; $i < 5; $i++)
                                <div class="item" data-value="{{$i}}">{{$i}}</div>
                            @endfor
                        </div>
                    </div>
                </div>
                <div class="field">
                    <div class="ui fluid selection dropdown">
                        <input name="deficiences[cfm1][]" type="hidden">
                        <i class="dropdown icon"></i>
                        <div class="default text"></div>
                        <div class="menu">
                            <div class="item" data-value=""></div>
                            @for($i = 0; $i < 5; $i++)
                                <div class="item" data-value="{{$i}}">{{$i}}</div>
                            @endfor
                        </div>
                    </div>
                </div>
                <div class="field">
                    <div class="ui fluid selection dropdown">
                        <input name="deficiences[cfm2][]" type="hidden">
                        <i class="dropdown icon"></i>
                        <div class="default text"></div>
                        <div class="menu">
                            <div class="item" data-value=""></div>
                            @for($i = 0; $i < 5; $i++)
                                <div class="item" data-value="{{$i}}">{{$i}}</div>
                            @endfor
                        </div>
                    </div>
                </div>
                <div class="field">
                    <div class="ui fluid selection dropdown disabled">
                        <input name="deficiences[cfm3][]" type="hidden">
                        <i class="dropdown icon"></i>
                        <div class="default text"></div>
                        <div class="menu">
                            <div class="item" data-value=""></div>
                            @for($i = 0; $i < 5; $i++)
                                <div class="item" data-value="{{$i}}">{{$i}}</div>
                            @endfor
                        </div>
                    </div>
                </div>
                <div class="field">
                    <div class="ui fluid selection dropdown">
                        <input name="deficiences[class_final][]" type="hidden">
                        <i class="dropdown icon"></i>
                        <div class="default text"></div>
                        <div class="menu">
                            <div class="item" data-value=""></div>
                            @for($i = 0; $i < 5; $i++)
                                @if ($i == 0)
                                    <div class="item" data-value="{{$i}}">{{$i}}</div>
                                @else
                                    @for($j = 0; $j < 5; $j++)
                                        <div class="item"
                                             data-value="{{$i . chr(65 + $j)}}">{{$i . chr(65 + $j)}}</div>
                                    @endfor
                                @endif
                            @endfor
                        </div>
                    </div>
                </div>
                <div class="field">
                    <div class="ui fluid selection dropdown">
                        <input name="deficiences[cat][]" type="hidden">
                        <i class="dropdown icon"></i>
                        <div class="default text"></div>
                        <div class="menu">
                            <div class="item" data-value=""></div>
                            @for($i = 0; $i < 11; $i++)
                                <div class="item" data-value="{{$i}}">{{$i}}</div>
                            @endfor
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {{--        <div class="two wide ear field">--}}
        {{--            <div class="ui fitted toggle checkbox">--}}
        {{--                <input name="deficiences[ear][]" value="1" type="checkbox" onchange="toggleDeficiences('ear', this)">--}}
        {{--                <input name="deficiences[ears][]" value="-1" type="hidden">--}}
        {{--                <label></label>--}}
        {{--            </div>--}}
        {{--        </div>--}}
        <div class="two wide msp field">
            <div class="ui fitted toggle checkbox">
                <input name="deficiences[dominant][]" value="1" type="checkbox"
                       onchange="toggleDeficiences('dominant', this)">
                <input name="deficiences[dominantx][]" value="-1" type="hidden">
                <label></label>
            </div>
        </div>
        <div class="two wide dominant-percentage field">
            <input readonly type="text" name="deficiences[dominant_percentage][]">
        </div>
        <div class="two wide field">
            <input readonly type="text" name="deficiences[total][]">
        </div>
        <div class="one wide field">
            <a class="ui red small icon basic button"><i class="remove icon"></i></a>
        </div>
    </div>
    <!-- END: DEFICIENCE MODEL -->

    <!-- MODELS PCL 917 PHASE TWO-->
    <div id="deficience_model_pcl_917" style="display: none;" class="fields">
        <input name="deficiences[id][]" type="hidden">
        <div class="three wide field">
            <input name="deficiences[table][]" type="text">
        </div>
        <div class="eight wide field">
            <input name="deficiences[name][]" type="text">
        </div>
        <div class="three wide field">
            <input name="deficiences[percentage][]" step="any" type="number">
        </div>
        <div class="two wide extremity field" hidden="hidden">
            <div class="ui fitted toggle checkbox">
                <input name="deficiences[extremity][]" value="1" type="checkbox">
                <input name="deficiences[extremityx][]" value="-1" type="hidden">
                <label></label>
            </div>
        </div>
        <div class="two wide field">
            <input disabled type="text" name="deficiences[total][]">
        </div>
        <div class="one wide field">
            <a class="ui red small icon basic button"><i class="remove icon"></i></a>
        </div>
    </div>
    <!-- END:ODELS PCL 917 PHASE TWO-->

    <!-- DIAGNOSTIC MODEL -->
    <div id="diagnostic_model" style="display: none;">
        @if($activity->pcl && $activity->pcl->pcl_type == 1)
            <div class="group-two-fields">
                <div class="fields">
                    <input type="hidden" name="diagnostics[id][]">
                    <div class="two wide required field">
                        <label>Cód.</label>
                        <div class="ui search code">
                            <div class="ui icon input">
                                <input class="code prompt" name="diagnostics[cod][]"
                                       type="text">
                                <i class="search icon"></i>
                            </div>
                            <div class="results"></div>
                        </div>
                    </div>
                    <div class="eight wide required field">
                        <label>Nombre</label>
                        <input class="description prompt"
                               name="diagnostics[description][]"
                               type="text" readonly>
                    </div>
                    <div class="required two wide field">
                        <label>Origen</label>
                        <div class="ui fluid selection dropdown originDropdownDx">
                            <input name="diagnostics[origin][]" type="hidden"
                                   value="{{"C"}}">
                            <i class="dropdown icon"></i>
                            <div class="default text">Origen</div>
                            <div class="menu">
                                @foreach($ORIGINS_PCL as $k => $v)
                                    <div class="item" data-value="{{$k}}">{{$v}}</div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                    <div class="eight wide required field">
                        <label>Deficiencia(s) motivo de la calificación / Condiciones de
                            salud</label>
                        <input name="diagnostics[deficiences][]" type="text">
                    </div>
                    <div class="one wide field">
                        <a class="ui red small icon basic button"><i class="remove icon"></i></a>
                    </div>
                </div>
            </div>
        @else
            <div class="fields">
                <input type="hidden" name="diagnostics[id][]">
                <div class="two wide required field">
                    <div class="ui search code">
                        <div class="ui icon input">
                            <input class="code prompt" name="diagnostics[cod][]" type="text">
                            <i class="search icon"></i>
                        </div>
                        <div class="results"></div>
                    </div>
                </div>
                <div class="eight wide required field">
                    <input class="description prompt" name="diagnostics[description][]" type="text">
                </div>
                <div class="required two wide field">
                    <div class="ui fluid selection dropdown originDropdownDx">
                        <input name="diagnostics[origin][]" type="hidden" value="{{"C"}}">
                        <i class="dropdown icon"></i>
                        <div class="default text">Origen</div>
                        <div class="menu">
                            @foreach($ORIGINS_PCL as $k => $v)
                                <div class="item" data-value="{{$k}}">{{$v}}</div>
                            @endforeach
                        </div>
                    </div>
                </div>
                <div class="four wide field">
                    <div class="ui fluid selection dropdown">
                        <input name="diagnostics[laterality][]" type="hidden">
                        <i class="dropdown icon"></i>
                        <div class="default text">Lateralidad</div>
                        <div class="menu">
                            @foreach(\App\Providers\AppServiceProvider::$LATERALITY as $k=>$r)
                                <div class="item" data-value="{{$k}}">{{$r}}</div>
                            @endforeach
                        </div>
                    </div>
                </div>
                <div class="one wide field">
                    <a class="ui red small icon basic button"><i class="remove icon"></i></a>
                </div>
            </div>
        @endif
    </div>
    <!-- END: DIAGNOSTIC MODEL -->

    <!-- DIAGNOSTIC VALORATION MODEL -->
    <div id="diagnostic_valoration_model" style="display: none;" class="fields">
        <input type="hidden" name="diagnostics_valoration[id][]">
        <div class="three wide required field">
            <div class="ui search diagnostic">
                <div class="ui icon input">
                    <input class="prompt" name="diagnostics_valoration[cod][]" type="text">
                    <i class="search icon"></i>
                </div>
                <div class="results"></div>
            </div>
        </div>
        <div class="twelve wide required field">
            <input class="description" name="diagnostics_valoration[description][]" type="text" readonly>
        </div>
        <div class="twelve wide required field">
            <input class="description" name="diagnostics_valoration[description_editable][]" type="text">
        </div>
        <div class="one wide field">
            <a class="ui red small icon basic button"><i class="remove icon"></i></a>
        </div>
    </div>
    <!-- END: DIAGNOSTIC VALORATION MODEL -->
    <!-- ADMIN MARK MODEL -->
    <div id="admin_mark_model" style="display: none;" class="fields">
        <input type="hidden" name="admin_mark[id][]">
        <div class="three wide required field">
            <div class="ui selection dropdown">
                <input type="hidden" name="admin_mark[admin_mark][]">
                <i class="dropdown icon"></i>
                <div class="default text">Seleccione uno</div>
                <div class="menu">
                    <div class="item" data-value="TUTELA">TUTELA</div>
                    <div class="item" data-value="ENTES DE CONTROL">ENTES DE CONTROL</div>
                    <div class="item" data-value="CASO GAP">CASO GAP</div>
                    <div class="item" data-value="PQR">PQR</div>
                    <div class="item" data-value="GERENCIA PREVENCIION DEL FRAUDE">GERENCIA PREVENCIÓN DEL FRAUDE</div>
                    <div class="item" data-value="ACLARATORIA">ACLARATORIA</div>
                </div>
            </div>
        </div>
        <div class="twelve wide required field">
            <textarea name="admin_mark[admin_mark_observation][]" rows="2"></textarea>
        </div>
        <div class="one wide field">
            <a class="ui red small icon basic button"><i class="remove icon"></i></a>
        </div>
    </div>
    <!-- END: ADMIN MARK MODEL -->

    <!-- ADICIONAL VALORATIONS -->
    <div id="documentary_validation_model" style="display: none;" class="fields">
        <input type="hidden" name="documentary_validations[id][]">
        <div class="seven wide field">
            <div class="ui fluid selection dropdown">
                <input name="documentary_validations[document_validation][]"
                       type="hidden">
                <i class="dropdown icon"></i>
                <div class="default text">Seleccione uno</div>
                <div class="menu">
                    <div class="item"
                         data-value="Valoraciones médicas y/o conceptos clínicos - Necesarias para la calificación según el decreto aplicado">
                        Valoraciones médicas y/o conceptos clínicos - Necesarias para la calificación según el decreto aplicado
                    </div>
                    <div class="item"
                         data-value="Pruebas objetivas y/o documentos complementarios - Necesarias para la calificación según el decreto aplicado">
                        Pruebas objetivas y/o documentos complementarios - Necesarias para la calificación según el decreto aplicado
                    </div>
                    <div class="item"
                         data-value="Pruebas de laboratorio - Necesarias para la calificación según el decreto aplicado">
                        Pruebas de laboratorio - Necesarias para la calificación según el decreto aplicado
                    </div>
                </div>
            </div>
        </div>
        <div class="eight wide field">
            <textarea name="documentary_validations[document_validation_observation][]"
                      rows="6"></textarea>
        </div>
        <div class="one wide field">
            <a class="ui red small icon basic button"><i class="remove icon"></i></a>
        </div>
    </div>
    <!-- END: ADICIONAL VALORATIONS -->

    <!-- PRELIMINARY OPINIONS -->
    <div id="preliminary_opinion_model" style="display: none;" class="fields">
        <input type="hidden" name="preliminary_opinions[id][]">
        <div class="seven wide field">
            <div class="ui fluid selection dropdown">
                <input name="preliminary_opinions[document_preliminary_opinion][]"
                       type="hidden">
                <i class="dropdown icon"></i>
                <div class="default text">Seleccione uno</div>
                <div class="menu">
                    <div class="item"
                         data-value="Valoraciones médicas y/o conceptos clínicos - Necesarias para la calificación según el decreto aplicado">
                        Valoraciones médicas y/o conceptos clínicos - Necesarias para la calificación según el decreto aplicado
                    </div>
                    <div class="item"
                         data-value="Pruebas objetivas y/o documentos complementarios - Necesarias para la calificación según el decreto aplicado">
                        Pruebas objetivas y/o documentos complementarios - Necesarias para la calificación según el decreto aplicado
                    </div>
                    <div class="item"
                         data-value="Pruebas de laboratorio - Necesarias para la calificación según el decreto aplicado">
                        Pruebas de laboratorio - Necesarias para la calificación según el decreto aplicado
                    </div>
                </div>
            </div>
        </div>
        <div class="eight wide field">
            <textarea name="preliminary_opinions[document_preliminary_opinion_observation][]"
                      rows="6"></textarea>
        </div>
        <div class="one wide field">
            <a class="ui red small icon basic button"><i class="remove icon"></i></a>
        </div>
    </div>
    <!-- END: PRELIMINARY OPINIONS -->

    <!-- QUALITY CONTROLS -->
    <div id="quality_control_model" style="display: none;" class="fields">
        <input type="hidden" name="quality_controls[id][]">
        <div class="seven wide field">
            <div class="ui fluid selection dropdown">
                <input name="quality_controls[document_quality_control][]"
                       type="hidden">
                <i class="dropdown icon"></i>
                <div class="default text">Seleccione uno</div>
                <div class="menu">
                    <div class="item"
                         data-value="Valoraciones médicas y/o conceptos clínicos - Necesarias para la calificación según el decreto aplicado">
                        Valoraciones médicas y/o conceptos clínicos - Necesarias para la calificación según el decreto aplicado
                    </div>
                    <div class="item"
                         data-value="Pruebas objetivas y/o documentos complementarios - Necesarias para la calificación según el decreto aplicado">
                        Pruebas objetivas y/o documentos complementarios - Necesarias para la calificación según el decreto aplicado
                    </div>
                    <div class="item"
                         data-value="Pruebas de laboratorio - Necesarias para la calificación según el decreto aplicado">
                        Pruebas de laboratorio - Necesarias para la calificación según el decreto aplicado
                    </div>
                </div>
            </div>
        </div>
        <div class="eight wide field">
            <textarea name="quality_controls[document_quality_control_observation][]"
                      rows="6"></textarea>
        </div>
        <div class="one wide field">
            <a class="ui red small icon basic button"><i class="remove icon"></i></a>
        </div>
    </div>
    <!-- END: QUALITY CONTROLS -->
    <!-- WORK HISTORY MODEL -->
    <div id="work_history_model" style="display: none;" class="fields">
        <input type="hidden" name="work_history[id][]">
        <div class="six wide field"><input name="work_history[company][]" type="text"></div>
        <div class="six wide field"><input name="work_history[position][]" type="text"></div>
        <div class="five wide field"><input name="work_history[risk_factor][]" type="text"></div>
        <div class="four wide field">
            <div class="ui right labeled input">
                <input name="work_history[exposition_time][]" type="number">
                <div class="ui basic label">
                    meses
                </div>
            </div>
        </div>
        <div class="one wide field">
            <a class="ui red small icon basic button"><i class="remove icon"></i></a>
        </div>
    </div>
    {{-- END: MODELS--}}

    <style type="text/css">
        .ui.grid .column {
            padding: 0.5rem 1rem !important;
        }

        .sender .field {
            display: none;
        }

        .ear.field {
            display: none;
        }

        .msp.field {
            display: none;
        }

        .ui.accordion .title {
            text-transform: uppercase;
        }

        .field > h3 {
            text-align: center;
            margin-top: 1.25rem;
        }

        .ui.search > .results {
            width: 30rem;
        }

        .ui.search > .results .result .title {
            padding: 0 !important;
            border: none !important;
            text-transform: none;
        }

        .ui.search > .results .result .content {
            padding: 0 !important;
        }

        th {
            text-align: center !important;
        }

        th.collapsing_tittle {
            word-wrap: break-word;
        }

        .table-container {
            width: 50%;
            margin: auto;
        }

        .ui.accordion .active.title {
            color: whitesmoke !important;
            background-color: #3AAA58 !important;
        }
    </style>
    <script>
        validateSubmitButton('button_save');
        $(document).ready(function () {

            $('.phone-input').on('input', function () {
                var sanitizedValue = this.value.replace(/[^0-9]/g, ''); // Eliminar cualquier cosa que no sea un dígito
                if (sanitizedValue.length > 10) {
                    sanitizedValue = sanitizedValue.substring(0, 10); // Limitar a 10 dígitos
                }
                this.value = sanitizedValue;
            });

            $('#laborally_active_check_917').prop('checked', true).parent().hide();          // Establece los valores por defecto de 'role_restriction' y 'economic_self' a 0
            $('#role_restriction_617').val(0);
            $('#economic_self_617').val(0);

            // Oculta los campos 'role_restriction' y 'economic_self'
            $('.laborally_active_617.field').hide();

            const convenioDropdown = $("input[name=\"tutelage_international_convenio\"]");
            const accordionIds = ["#69", "#69_1", "#69_2", "#69_3", "#69_4", "#69_5", "#physical_exam_title", "#laboral_antecedent_title", "#laboral_role_title"];
            var pclAct = @json($activity->pcl);
            var interconsultationsCount = @json(count($activity->pcl->interconsultations));

            const exam_profesional = $("textarea[name=\"interconsultations[exam_profesional][]\"]");
            const exam_date = $("input[name=\"interconsultations[exam_date][]\"]");
            const exam_name = $("input[name=\"interconsultations[exam_name][]\"]");
            const exam_result = $("textarea[name=\"interconsultations[exam_result][]\"]");
            const exam_observation = $("textarea[name=\"interconsultations[exam_observation][]\"]");

            function toggleAccordionVisibility() {
                if (convenioDropdown.val() === "SI") {

                    if (!exam_profesional.val().trim()) {
                        exam_profesional.val("");
                    }
                    if (!exam_date.val()) {
                        exam_date.val("");
                    }
                    if (!exam_name.val().trim()) {
                        exam_name.val("");
                    }
                    if (!exam_result.val().trim()) {
                        exam_result.val("");
                    }
                    if (!exam_observation.val().trim()) {
                        exam_observation.val("");
                    }

                    accordionIds.forEach(function (id) {
                        $(id).hide().accordion('refresh');
                    });

                } else {
                    if (pclAct && interconsultationsCount === 0 && exam_profesional.length < 1) {
                        exam_profesional.val('{{$activity->pcl->doc_full_name.' - RM '.$activity->pcl->doc_medical_record}}');
                        exam_date.val('{{$activity->pcl ? $activity->pcl->valoration_v_date : ""}}');
                        exam_name.val('{{$activity->pcl ? $activity->pcl->valoration_valoration_type : ""}}');
                        exam_result.val('La interconsulta de Valoración médica se verá reflejada en el PDF de dictamen.');
                        exam_observation.val('');

                        accordionIds.forEach(function (id) {
                            $(id).show().accordion("refresh");
                        });
                    }
                }
            }

            toggleAccordionVisibility();
            convenioDropdown.change(toggleAccordionVisibility);
        });
    </script>
    <script type="text/javascript">
        // DOCUMENTARY VALIDATIONS
        let addDocumentaryValidations = function (e) {
            let $fields = $("#documentary_validation_model").clone(true);
            $fields.removeAttr("id");
            $fields.find("a").click(function () {
                $(this).parent().parent().remove();
            });
            $fields.find(".ui.dropdown").dropdown({
                forceSelection: false
            });
            $("#documentary_validation").append($fields);
            $fields.show();
            return false;
        };

        // PRELIMINARY OPINIONS
        let addPreliminaryOpinions = function (e) {
            let $fields = $("#preliminary_opinion_model").clone(true);
            $fields.removeAttr("id");
            $fields.find("a").click(function () {
                $(this).parent().parent().remove();
            });
            $fields.find(".ui.dropdown").dropdown({
                forceSelection: false
            });
            $("#preliminary_opinion").append($fields);
            $fields.show();
            return false;
        };

        // QUALITY CONTROLS
        let addQualityControls = function (e) {
            let $fields = $("#quality_control_model").clone(true);
            $fields.removeAttr("id");
            $fields.find("a").click(function () {
                $(this).parent().parent().remove();
            });
            $fields.find(".ui.dropdown").dropdown({
                forceSelection: false
            });
            $("#quality_control").append($fields);
            $fields.show();
            return false;
        };

        var addDiagnostic = function (e, diagnosticData) {
            // Clona el modelo de diagnóstico
            var $fields = $("#diagnostic_model").clone(true);
            $fields.removeAttr("id");

            // Encuentra y define el evento click para el botón de eliminación
            $fields.find("a").click(function () {
                $(this).parent().parent().remove();
            });

            // Inicializa todos los dropdowns
            $fields.find(".ui.dropdown").dropdown({
                forceSelection: false
            });

            // Encuentra el dropdown de 'Origen' y actualiza su valor
            var $dropdownDx = $fields.find(".originDropdownDx");
            $dropdownDx.dropdown({
                onChange: function (value, text, $selectedItem) {
                    if (value === "L") {
                        if (!confirm("¿Seguro que desea cambiar origen de común a laboral?")) {
                            $(this).dropdown("set selected", "C");
                        }
                    }
                }
            });

            if (diagnosticData && diagnosticData.origin) {
                $dropdownDx.dropdown('set selected', diagnosticData.origin);
            }

            // Configura el datepicker
            $fields.find(".datepicker").pickadate({
                selectYears: true,
                selectMonths: true,
                formatSubmit: "yyyy-mm-dd",
                format: "yyyy-mm-dd"
            });

            // Actualiza el valor del campo de búsqueda
            $fields.find(".ui.search input").change(function () {
                var valid = false;

                $(this).parents(".fields").find("input.description").val("");

                for (var i = 0; i < cie10.length; i++) {
                    if (cie10[i].COD == $(this).val().toUpperCase()) {
                        $(this).parents(".fields").find("input.description").val(cie10[i].DESCRIPTION);
                        valid = true;
                    }
                }

                if (!valid && $(this).val() != "") {
                    $(this).val("");
                }
            });

            // Inicializa el campo de búsqueda con el source de cie10
            $fields.find(".ui.search").search({
                source: cie10,
                fields: {
                    title: "COD",
                    description: "DESCRIPTION"
                },
                searchFields: ["COD", "DESCRIPTION"],
                regExp: {
                    escape: /[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,
                    beginsWith: ""
                },
                showNoResults: true,
                maxResults: 250,
                searchFullText: false,
                error: {
                    noResults: 'No se encontraron resultados para tu búsqueda.'
                },
                onSelect: function (result, response) {
                    $(this).parents(".fields").find("input.description").val(result.DESCRIPTION);
                }
            });

            // Añade el modelo clonado al DOM y después lo muestra.
            $("#diagnostics").append($fields);
            $fields.show();

            return false;
        };
        var addDiagnosticValoration = function (e) {
            var $fields = $("#diagnostic_valoration_model").clone(true);
            $fields.removeAttr("id");
            $fields.find("a").click(function () {
                $(this).parent().parent().remove();
            });
            $fields.find(".ui.dropdown").dropdown({
                forceSelection: false
            });
            $fields.find(".ui.search.diagnostic").each(function () {
                var $searchInput = $(this).find("input");
                var $descriptionInput = $searchInput.parents(".fields").find("input.description");

                $searchInput.change(function () {
                    var valid = false;
                    $descriptionInput.val("");

                    for (var i = 0; i < cie10.length; i++) {
                        if (cie10[i].COD == $searchInput.val().toUpperCase()) {
                            $descriptionInput.val(cie10[i].DESCRIPTION);
                            valid = true;
                        }
                    }

                    if (!valid && $searchInput.val() != "") {
                        $searchInput.val("");
                    }
                });

                $(this).search({
                    source: cie10,
                    fields: {
                        title: "COD",
                        description: "DESCRIPTION"
                    },
                    searchFields: ["COD", "DESCRIPTION"],
                    regExp: {
                        escape: /[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,
                        beginsWith: ""
                    },
                    showNoResults: true,
                    maxResults: 250,
                    searchFullText: false,
                    error: {
                        noResults: 'No se encontraron resultados para tu búsqueda.'
                    },
                    onSelect: function (result, response) {
                        $descriptionInput.val(result.DESCRIPTION);
                    }
                });
            });

            $("#diagnostics_valoration").append($fields);
            $fields.show();
            return false;
        };
        var addContactability = function () {
            var $fields = $("#contactability_model").clone(true);
            $fields.removeAttr("id");
            $fields.find("a").click(function () {
                $(this).parent().parent().remove();
                showPlus();
            });
            $fields.find(".ui.dropdown").dropdown({
                forceSelection: false
            });
            $("#contactabilities").append($fields);
            $fields.find(".datepicker3").pickadate({
                selectYears: true,
                selectMonths: true,
                max: new Date(),
                formatSubmit: "yyyy-mm-dd",
                format: "yyyy-mm-dd"
            });
            $fields.show();
            showPlus();
            return false;
        };
        var addAdminMark = function () {
            var $fields = $("#admin_mark_model").clone(true);
            $fields.removeAttr("id");
            $fields.find("a").click(function () {
                $(this).parent().parent().remove();
                showPlus();
            });
            $fields.find(".ui.dropdown").dropdown({
                forceSelection: false
            });
            $("#admin_mark").append($fields);
            $fields.show();
            showPlus();
            return false;
        };

        var addIntercultation = function () {
            var $fields = $("#interconsultation_model").clone(true);
            $fields.removeAttr("id");
            $fields.find("a").click(function () {
                $(this).parent().parent().remove();
            });
            $("#interconsultations").append($fields);
            $fields.find(".datepicker2").pickadate({
                selectYears: true,
                selectMonths: true,
                formatSubmit: "yyyy-mm-dd",
                format: "yyyy-mm-dd"
            });
            $fields.show();
            return false;
        };
        // DEFICIENCIES PCL 917
        var addWorkHistory = function () {
            var $fields = $("#work_history_model").clone(true);
            $fields.removeAttr("id");
            $fields.find("a").click(function () {
                $(this).parent().parent().remove();
            });
            $("#work_history").append($fields);
            $fields.show();
            return false;
        };
        var cie10 = [];

        var showPlus = function () {
            var contactability_fields = $("#contactabilities").find(".fields");
            var selectValor = contactability_fields.length;
            if (selectValor <= 5) {
                $("#more_contactabilities").show();
            } else {
                $("#more_contactabilities").hide();
            }
        };

        $(function () {
            let qualification = $("#beneficiaries_qualification").val();

            if (qualification === "SI") {
                var age = {{ $activity->pcl ? $activity->pcl->age() : ''}};
            } else {
                var age = {{ $activity->pcl ? $activity->pcl->ageAffiliate() : ''}};
            }

            // Ocultar todos los contenidos al cargar la página.
            hideAllContents();

          // Ajustar la visibilidad al inicio
          if ($('input[name="valoration_laborally_active"]').is(':checked')) {
            $('#rol-laboral').show();
            $('#areas-ocupacionales').show();
          }

          if ($('input[name="valoration_use_laboral_role"]').is(':checked') && age > 3) {
            $('#ocupational_role_adults_description').show();
            $('#valoration_use_rol_description').show();
          } else if ($('input[name="valoration_use_laboral_role"]').is(':checked') && age <= 3) {
            $('#occupational_role_kids_description').show();
            $('#valoration_use_rol_description').show();
          }

          // Ajustar la visibilidad al inicio
          if ($('input[name="valoration_use_disabilities_handicap"]').is(':checked')) {
            $('#tab-disabilities').show();
            $('#tab-handicap').show();
          }

          // Escuchar cambios en ambos checkboxes
          $('input[name="valoration_laborally_active"], input[name="valoration_use_laboral_role"], input[name="valoration_use_disabilities_handicap"]').on('change', function () {
            $('input[name="valoration_laborally_active"], input[name="valoration_use_laboral_role"], input[name="valoration_use_disabilities_handicap"]').not(this).prop('checked', false);

                // Esconder todos los contenidos cada vez que un checkbox se marque/desmarque.
                hideAllContents();

                if ($('input[name="valoration_laborally_active"]').is(':checked')) {
                    $('#rol-laboral').show();
                    $('#areas-ocupacionales').show();
                } else if ($('input[name="valoration_use_laboral_role"]').is(':checked') && age > 3) {
                    $('#ocupational_role_adults_description').show().addClass('active');
                    $('#ocupational_role_adults_description-content').show().addClass('active');
                    $('#valoration_use_rol_description').show().addClass('active');
                    $('input[name="valoration_use_disabilities_handicap"]').parent().parent().show();
                } else if ($('input[name="valoration_use_laboral_role"]').is(':checked') && age <= 3) {
                    $('#occupational_role_kids_description').show().addClass('active');
                    $('#occupational_role_kids_description_content').show().addClass('active');
                    $('#valoration_use_rol_description').show().addClass('active');
                    $('input[name="valoration_use_disabilities_handicap"]').parent().parent().show();
                } else if ($('input[name="valoration_use_disabilities_handicap"]').is(':checked')) {
                    $('#tab-disabilities').show();
                    $('#tab-handicap').show();
                }
            });

            // Función para esconder todos los contenidos.
            function hideAllContents() {
                $('#tab-disabilities').hide();
                $('#tab-disabilities-content').hide().removeClass('active');
                $('#tab-handicap').hide();
                $('#tab-handicap-content').hide().removeClass('active');
                $("#rol-laboral").hide();
                $("#rol-laboral-content").hide().removeClass("active");
                $("#areas-ocupacionales").hide();
                $("#areas-ocupacionales-content").hide().removeClass("active");
                $("#valoration_use_rol_description").hide();
                $("#ocupational_role_adults_description").hide();
                $("#ocupational_role_adults_description-content").hide();
                $("#occupational_role_kids_description").hide();
                $("#occupational_role_kids_description_content").hide();
            }
        });

        function toggleFieldsBasedOnBeneficiaryQualification() {
            let qualification = $("#beneficiaries_qualification").val();
            let titleBase917 = "CALIFICACIÓN DE PCL 917";
            let titleBase1507 = "CALIFICACIÓN DE PCL 1507";
            let spanElement = "<span style=\"color: red;\" class=\"required\">*</span>";
            if (qualification == "SI") {
                $(".beneficiary-data-field").show();
                // Cambiar los títulos según corresponda
                $("#title-917").html(`${titleBase917} - BENEFICIARIO ${spanElement}`);
                $("#title-1507").html(`${titleBase1507} - BENEFICIARIO ${spanElement}`);
            } else {
                $(".beneficiary-data-field").hide();
                // Cambiar los títulos según corresponda
                $("#title-917").html(`${titleBase917} - AFILIADO ${spanElement}`);
                $("#title-1507").html(`${titleBase1507} - AFILIADO ${spanElement}`);
            }
        }

        function handleValorationTypeChange() {
            var valorationTypeValue = $(this).val();
            var activity = <?php echo json_encode($activity); ?>;

            // Initially hide both accordions
            $("#laboral_antecedent_title").hide().removeClass("active");
            $("#laboral_antecedent_content").hide().removeClass("active");
            $("#physical_exam_title").hide().removeClass("active");
            $("#physical_exam_content").hide().removeClass("active");

            // Adjust visibility at the start
            if (valorationTypeValue == "PRESENCIAL") {
                $("#laboral_antecedent_title").show();
                $("#physical_exam_title").show();
            } else if (valorationTypeValue == "VALORACION TELEFONICA TITULO II") {
                $("#laboral_antecedent_title").hide();
                $("#physical_exam_title").hide();
            }

            if (valorationTypeValue) {
                // Show/Hide accordions
                if (valorationTypeValue == "PRESENCIAL") {
                    // Show accordions
                    $("#laboral_antecedent_title").show();
                    $("#physical_exam_title").show();
                    $("#laboral_antecedent_title, #physical_exam_title").removeClass("active");
                } else {
                    // Hide accordions
                    $("#laboral_antecedent_title, #physical_exam_title").hide();
                    $("#laboral_antecedent_title, #physical_exam_title").removeClass("active");

                    // Clear all fields within the accordions except textarea with name 'laboral_antecedent'
                    var fieldsToClean = $("#laboral_antecedent_content, #physical_exam_content").find("input[type=text], select, textarea").not("textarea[name='laboral_antecedent']");
                    fieldsToClean.val("");
                    $("#laboral_antecedent_content, #physical_exam_content").find("input[type=number]").val(null);
                    $("#laboral_antecedent_content, #physical_exam_content").find("input[type=checkbox], input[type=radio]").prop("checked", false);

                    // If not 'PRESENCIAL', set "Sin hallazgos positivos" in the textarea fields
                    if (valorationTypeValue != "PRESENCIAL") {
                        if (activity && activity.pcl) {
                            if (valorationTypeValue != "PRESENCIAL") {
                                if (activity && activity.pcl) {
                                    $("textarea[name=\"head_and_neck\"]").val(activity.pcl.head_and_neck ? activity.pcl.head_and_neck : "Sin hallazgos positivos");
                                    $("textarea[name=\"pulmonaryal_cardio\"]").val(activity.pcl.pulmonaryal_cardio ? activity.pcl.pulmonaryal_cardio : "Sin hallazgos positivos");
                                    $("textarea[name=\"abdomen\"]").val(activity.pcl.abdomen ? activity.pcl.abdomen : "Sin hallazgos positivos");
                                    $("textarea[name=\"phisical_exam_extremities\"]").val(activity.pcl.phisical_exam_extremities ? activity.pcl.phisical_exam_extremities : "Sin hallazgos positivos");
                                    $("textarea[name=\"phisical_exam_neurological\"]").val(activity.pcl.phisical_exam_neurological ? activity.pcl.phisical_exam_neurological : "Sin hallazgos positivos");
                                    $("textarea[name=\"phisical_exam_mental\"]").val(activity.pcl.phisical_exam_mental ? activity.pcl.phisical_exam_mental : "Sin hallazgos positivos");
                                }
                            }
                        }
                    }
                }
            }
        }

        $(document).ready(function () {
            $(".ui.accordion").accordion({
                exclusive: false
            });

            $("form .ui.dropdown").dropdown({
                forceSelection: false
            });
            $("form .datepicker").pickadate({
                selectYears: 80,
                max: new Date(),
                selectMonths: true,
                formatSubmit: "yyyy-mm-dd",
                format: "yyyy-mm-dd"
            });

            $("#valoration_type").change(handleValorationTypeChange).change();

            let originalValoration = {!! json_encode($activity->getOriginal('pcl') && $activity->getOriginal('pcl')->valoration_valoration_type) !!};
            let currentValoration = {!! json_encode($activity->pcl && $activity->pcl->valoration_valoration_type) !!};

            if (currentValoration === "PRESENCIAL" && originalValoration === "PRESENCIAL") {
                $("form#dictamen").form({
                    fields: {
                        height: ["empty"],
                        weight: ["empty"],
                        ta: ["empty"],
                        ta2: ["empty"],
                        fc: ["empty"],
                        fr: ["empty"],
                        temperature: ["empty"]
                    }
                });
            } else {
                $("form#dictamen").form({
                    fields: {
                        beneficiaries_qualification: ["empty"]
                    }
                });
            }
            // Cuando cambia el valor de beneficiaries_qualification.
            $("#beneficiaries_qualification").change(toggleFieldsBasedOnBeneficiaryQualification);
            // Cuando la página se carga.
            toggleFieldsBasedOnBeneficiaryQualification();


            function updateTotal() {
                var total0 = 0;
                var total1 = $("input[name^='T1'][value='1']:checked, input[name^='T2'][value='1']:checked").length * 1;
                var total2 = $("input[name^='T1'][value='2']:checked, input[name^='T2'][value='2']:checked").length * 2;
                var totalAll = total0 + total1 + total2;

                $("#total0").text(total0);
                $("#total1").text(total1);
                $("#total2").text(total2);
                $("#totalAll").text(totalAll);

                $("#inputTotal0").val(total0);
                $("#inputTotal1").val(total1);
                $("#inputTotal2").val(total2);
                $("#inputTotalAll").val(totalAll); // Agrega esta línea
            }

            // PCL 1507
            // PCL 1507
            $(function () {
                // Inicialmente ocultar ambos acordeones
                $("#use_laboral_role").hide().removeClass("active");
                $("#ocupational_role_adults_description_1507").hide();
                $("#role_category").hide();
                $("#use_laboral_role_description").hide();
                $("#ocupational_role_adults_description_content_1507").hide().removeClass("active");
                $("#childRoleDiv").hide();

                var age = {!! json_encode($age) !!};

                // Verificar el estado inicial de tu input "use_laboral_role"
                if ($("input[name=\"use_laboral_role\"]").is(":checked")) {
                    $("#use_laboral_role_description").show();
                    $("#role_category").show();
                    $("#ocupational_role_adults_description_1507").show();
                    if (age <= 3) {
                        $("#childRoleDiv").show();
                    }
                }

                // Escuchar cambios en ambos checkboxes
                $("input[name=\"use_laboral_role\"]").on("change", function () {
                    if ($(this).is(":checked")) {
                        $("#use_laboral_role_description").show();
                        $("#role_category").show();
                        $("#ocupational_role_adults_description_1507").show();
                        $("#ocupational_role_adults_description_content_1507").hide().removeClass("active");
                        if (age <= 3) {
                            $("#childRoleDiv").show();
                        }
                    } else {
                        $("#use_laboral_role_description").hide();
                        $("#role_category").hide();
                        $("#ocupational_role_adults_description_1507").hide();
                        $("#ocupational_role_adults_description_content_1507").hide().removeClass("active");
                        $("#childRoleDiv").hide();
                    }
                });
            });

            // TOTAL PCL 1507
            function updateTotal1507() {
                let total0_1507 = 0;
                let total1_1507 = $("input[name^='T1_1507'][value='1']:checked, input[name^='T2_1507'][value='1']:checked").length * 1;
                let total2_1507 = $("input[name^='T1_1507'][value='2']:checked, input[name^='T2_1507'][value='2']:checked").length * 2;
                let totalAll_1507 = total0_1507 + total1_1507 + total2_1507;

                $("#total0_1507").text(total0_1507);
                $("#total1_1507").text(total1_1507);
                $("#total2_1507").text(total2_1507);
                $("#totalAll_1507").text(totalAll_1507);

                $("#inputTotal0_1507").val(total0_1507);
                $("#inputTotal1_1507").val(total1_1507);
                $("#inputTotal2_1507").val(total2_1507);
                $("#inputTotalAll_1507").val(totalAll_1507);
            }

            updateTotal1507();
            updateTotal();

            $("input[type='radio']").change(updateTotal);

            // TOTAL PCL 1507
            $(".radios_1507").change(updateTotal1507);

            var originDropdown = $(".originDropdown");
            var originalValue = originDropdown.find("input").val();

            // Inicializamos el dropdown.
            originDropdown.dropdown();

            // Si teníamos un valor original, lo seleccionamos en el dropdown.
            if (originalValue) {
                originDropdown.dropdown("set selected", originalValue);
            }

            $(document).on("change", ".originDropdown input[type=hidden]", function () {
                var newValue = $(this).val();

                if (newValue === "L") {
                    if (!confirm("Seguro que desea cambiar origen de común a laboral?")) {
                        $(this).parent().dropdown("set selected", "C");
                        $(this).data("avoidConfirm", true);
                    }
                } else if (!newValue || newValue === "C") {
                    $(this).data("avoidConfirm", false);
                }
            });
            $(".originDropdownDx").each(function () {
                // Cada dropdown 'Origen' ahora es manejado individualmente.

                // Guarda el valor original del dropdown.
                var originalValueDx = $(this).find("input").val();

                // Inicializa el dropdown.
                $(this).dropdown();

                // Si el dropdown tenía un valor original, lo selecciona.
                if (originalValueDx) {
                    $(this).dropdown("set selected", originalValueDx);
                }

                // Añade un listener de cambios al dropdown.
                $(this).change(function () {
                    var newValue = $(this).find("input").val();

                    if (newValue === "L" && !confirm("Seguro que desea cambiar origen de común a laboral?")) {
                        $(this).dropdown("set selected", "C");
                    }
                });
            });
            showPlus();

            $("form .ui.search input").change(function () {
                var valid = false;

                $(this).parents(".fields").find("input.description").val("");

                for (var i = 0; i < cie10.length; i++) {
                    if (cie10[i].COD == $(this).val().toUpperCase()) {
                        $(this).parents(".fields").find("input.description").val(cie10[i].DESCRIPTION);
                        valid = true;
                    }
                }

                if (!valid && $(this).val() != "") {
                    $(this).val("");
                }
            });
            $.getJSON("/js/cie10.json", function (json) {
                cie10 = json;
                $("form .ui.search.code").search({
                    source: cie10,
                    fields: {
                        title: "COD",
                        description: "DESCRIPTION"
                    },
                    searchFields: ["COD", "DESCRIPTION"],
                    regExp: {
                        escape: /[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,
                        beginsWith: ""
                    },
                    showNoResults: true,
                    maxResults: 250,
                    searchFullText: false,
                    error: {
                        noResults: 'No se encontraron resultados para tu búsqueda.'
                    },
                    onSelect: function (result, response) {
                        $(this).parents(".fields").find("input.description").val(result.DESCRIPTION);
                        $(this).parents(".fields").find("textarea.description_text").val(result.DESCRIPTION);
                    }
                });
            });

            $("form input").keydown(function (event) {
                if (event.keyCode == 13) {
                    event.preventDefault();
                    return false;
                }
            });
        });
    </script>

    @if($activity->pcl && $activity->pcl->pcl_type != 3)
        <!-- PCL -->
        <script>
            $("[name=pcl_type]").change(function () {
                if ($(this).val() === "3") {
                    $("form#dictamen").submit();
                }
            });

            var toggleSender = function () {
                if ($("[name=sender]").val() == 5 || $("[name=sender]").val() == 8) {
                    $(".sender .field").show();
                } else {
                    $(".sender .field").hide();
                }
            };
            var addDeficience = function () {
                var $fields = $("#deficience_model").clone(true);
                $fields.removeAttr("id");
                $fields.find("a").click(function () {
                    $(this).parent().parent().remove();
                    calculateDeficienceSum();
                });
                $fields.find(".ui.dropdown").dropdown({
                    forceSelection: false
                });
                $fields.find(".ui.checkbox").checkbox();
                $fields.find("[name*=\"deficiences[ear]\"]").change(calculateDeficienceSum);
                $fields.find("[name*=\"deficiences[ear]\"]").prop("checked", false);
                $fields.find("[name*=\"deficiences[percentage]\"]").change(calculateDeficienceSum);
                $fields.find("[name*=\"deficiences[cat]\"]").change(calculateDeficienceSum);
                $fields.find("[name*=\"deficiences[dominant]\"]").change(calculateDeficienceSum);
                $fields.find("[name*=\"deficiences[dominant]\"]").prop("checked", false);
                $("#deficiences").append($fields);
                $fields.show();
                return false;
            };
            var toggleDeficiences = function (type, mObject) {
                var itemFields = $(mObject).parent().parent().parent();
                var ear = $(itemFields).find("input[name*=\"deficiences[ear]\"]");
                var dominant = $(itemFields).find("input[name*=\"deficiences[dominant]\"]");

                if (type === "ear") {
                    $(dominant[0]).prop("checked", false);
                } else {
                    $(ear[0]).prop("checked", false);
                }
            };
            var calculateDeficienceSum = function () {
                var totals = $("form input[name*=\"deficiences[total]\"]");
                var cats = $("form input[name*=\"deficiences[cat]\"]");
                var percentages = $("form input[name*=\"deficiences[percentage]\"]");
                var ears = $("form input[name*=\"deficiences[ear]\"]");
                var ears_data = $("form input[name*=\"deficiences[ears]\"]");
                var dominants = $("form input[name*=\"deficiences[dominant]\"]");
                var dominantxs = $("form input[name*=\"deficiences[dominantx]\"]");
                var dominantPercentages = $("form input[name*=\"deficiences[dominant_percentage]\"]");

                var deficiences = $.map(percentages, function (input, k) {
                    var cat = $(cats[k]).val() != "" ? $(cats[k]).val() : 0;
                    var total = 0;
                    var dominantPercentage = "";
                    // Validate checkbox
                    if ($(ears[k]).prop("checked")) {
                        $(ears_data[k]).val(1);
                    } else {
                        $(ears_data[k]).val(-1);
                    }

                    if ($(dominants[k]).prop("checked")) {
                        $(dominantxs[k]).val(1);
                    } else {
                        $(dominantxs[k]).val(-1);
                    }

                    if ($(dominantxs[k]).val() === "1") {
                        var A = parseFloat($(input).val());
                        var B = parseFloat($(input).val()) * 0.2;
                        total = A + ((100 - A) * B) / 100;
                        dominantPercentage = Math.round(B * 100) / 100;
                    } else {
                        total = parseFloat($(input).val());
                    }
                    total += parseFloat(cat);
                    $(dominantPercentages[k]).val(dominantPercentage);
                    if (isNaN(total)) {
                        $(totals[k]).val(0);
                    } else {
                        $(totals[k]).val(total);
                    }
                    return total;
                });

                var ear_deficiences = [];
                var normal_msd_deficiences = [];

                for (var i = 0; i < deficiences.length; i++) {
                    if (!isNaN(deficiences[i])) {
                        if ($(ears_data[i]).val() === "1") {
                            ear_deficiences.push(deficiences[i]);
                        } else {
                            normal_msd_deficiences.push(deficiences[i]);
                        }
                    }
                }

                normal_msd_deficiences.sort(function (a, b) {
                    return b - a;
                });

                var sum = null;
                var sum_pond = 0;

                for (var i = 0; i < normal_msd_deficiences.length; i++) {
                    if (!isNaN(normal_msd_deficiences[i])) {
                        if (sum == null) {
                            sum = normal_msd_deficiences[i];
                            continue;
                        }
                        sum = sum + ((100 - sum) * normal_msd_deficiences[i]) / 100;
                    }
                }

                if (sum == null) {
                    sum = 0;
                }

                sum_pond = sum / 2;

                for (var i = 0; i < ear_deficiences.length; i++) {
                    if (!isNaN(ear_deficiences[i])) {
                        sum += ear_deficiences[i];
                        sum_pond += ear_deficiences[i];
                    }
                }

                $("#deficiences_sum").text(Math.round(sum * 100) / 100);
                $("#deficiences_total").text(Math.round(sum_pond * 100) / 100);
                $("#deficiences_total_report").val($("#deficiences_total").text());
                $("#deficiences_final").text(Math.round(sum_pond * 100) / 100);

                calculateLaborallyActive();

                var total = sum_pond + parseFloat($("#laboral_role_total").text());

                $("#pcl_percentage").text(Math.round(total * 100) / 100);
                $("#pcl_percentage_report").val($("#pcl_percentage").text());

                return sum;
            };
            var calculateLaborallyActive = function (e) {
                var total = 0;
                if ($("[name=pcl_type]").val() == 1) {
                    if ($("#deficiences_total").text() > 0) {
                        total = {{$activity->affiliate->pcl_age_restriction()}};
                    }
                }
                $('#pcl_age_restriction').text(total);
                $.each($("#laboral_role input"), function (i, input) {
                    if ($(input).attr("name") != "laborally_active" &&
                        $(input).attr("name") != "use_laboral_role" &&
                        $(input).attr("name") != "role_category") {
                        if ($(input).attr("type") == "radio" && $(input).prop("checked")) {
                            total += parseFloat($(input).val());
                        } else if ($(input).attr("type") == "hidden") {
                            total += parseFloat($(input).val());
                        }
                    }
                });
                total = Math.round(total * 10) / 10;
                $("#laboral_role_total").text(total);
                $("#laboral_role_total_report").val($("#laboral_role_total").text(total));
                $("#pcl_percentage").text(total + parseFloat($("#deficiences_total").text()));
                $("#pcl_percentage_report").val($("#pcl_percentage").text());
            };
            $(document).ready(function () {
                @unless($activity->pcl && $activity->pcl->use_laboral_role)
                $(".use_laboral_role.field").hide();
                @endunless

                @unless($activity->pcl && $activity->pcl->laborally_active)
                $(".laborally_active .field, .laborally_active.field").hide();
                @endunless

                $(".ui.checkbox").checkbox({
                    onChecked: function () {
                        if ($(this).attr("name") == "use_laboral_role") {
                            $(".use_laboral_role.field").show();
                            $("input[name=laborally_active]").parent().checkbox("uncheck");
                        }
                        if ($(this).attr("name") == "laborally_active") {
                            $(".laborally_active .field, .laborally_active.field").show();
                            $("input[name=use_laboral_role]").parent().checkbox("uncheck");
                        }
                    },
                    onUnchecked: function () {
                        if ($(this).attr("name") == "use_laboral_role")
                            $(".use_laboral_role.field").hide();
                        if ($(this).attr("name") == "laborally_active")
                            $(".laborally_active .field, .laborally_active.field").hide();
                    }
                });

                $("form#dictamen a.red").click(function () {
                    $(this).parent().parent().remove();
                    calculateDeficienceSum();
                });

                $("[name=pcl_type]").change(function () {
                    if ($(this).val() == 1) {
                        $("#pcl_age_restriction").text('0');
                        if ($("#deficiences_total").text() > 0) {
                            $("#pcl_age_restriction").text('{{$activity->affiliate->pcl_age_restriction()}}');
                        }
                    } else if ($(this).val() == 2){
                        $("#pcl_age_restriction").text("0.0");
                    }

                    if ($("[name=laborally_active]").prop("checked")) {
                        calculateLaborallyActive();
                    }
                });
                $("[name=applicant]").change(toggleSender);
                $("[name*=\"deficiences[ear]\"]").change(calculateDeficienceSum);
                $("[name*=\"deficiences[percentage]\"]").change(calculateDeficienceSum);
                $("[name*=\"deficiences[cat]\"]").change(calculateDeficienceSum);
                $("[name*=\"deficiences[dominant]\"]").change(calculateDeficienceSum);

                $("#laboral_role input").change(function () {
                    if ($("[name=laborally_active]").prop("checked")) {
                        calculateLaborallyActive();
                    } else if ($("[name=use_laboral_role]").prop("checked")) {
                        var total = parseFloat($("[name=role_category]").val());

                        $("#laboral_role_total").text(total);
                        $("#laboral_role_total_report").val($("#laboral_role_total").text(total));
                        $("#pcl_percentage").text(total + parseFloat($("#deficiences_total").text()));
                        $("#pcl_percentage_report").val($("#pcl_percentage").text());
                    } else {
                        var total = 0;

                        $("#laboral_role_total").text(total);
                        $("#laboral_role_total_report").val($("#laboral_role_total").text(total));
                        $("#pcl_percentage").text(total + parseFloat($("#deficiences_total").text()));
                        $("#pcl_percentage_report").val($("#pcl_percentage").text());
                    }
                });

                $("#deficiences_sum").text(Math.round(parseFloat($("#deficiences_sum").text()) * 100) / 100);
                $("#deficiences_final").text(Math.round(parseFloat($("#deficiences_final").text()) * 100) / 100);
                $("#deficiences_total").text(Math.round(parseFloat($("#deficiences_total").text()) * 100) / 100);
                $("#deficiences_total_report").val($("#deficiences_total").text());
                $("#pcl_percentage").text(Math.round(parseFloat($("#pcl_percentage").text()) * 100) / 100);
                $("#pcl_percentage_report").val($("#pcl_percentage").text());

            });
        </script>
    @else
        <!-- 917 PCL -->
        <script type="text/javascript">
            $("[name=pcl_type]").change(function () {
                if ($(this).val() !== "3") {
                    $("form#dictamen").submit();
                }
            });

            var toggleSender = function () {
                if ($("[name=sender]").val() == 5 || $("[name=sender]").val() == 8) {
                    $(".sender .field").show();
                } else {
                    $(".sender .field").hide();
                }
            };

            var addDeficiencePcl917 = function () {
                var $fields = $("#deficience_model_pcl_917").clone(true);
                $fields.removeAttr("id");
                $fields.find("a").click(function () {
                    $(this).parent().parent().remove();
                    calculateDeficienceSum();
                });
                $fields.find(".ui.dropdown").dropdown({
                    forceSelection: false
                });
                $fields.find(".ui.checkbox").checkbox();
                $fields.find("[name*=\"deficiences[ear]\"]").change(calculateDeficienceSum);
                $fields.find("[name*=\"deficiences[percentage]\"]").change(calculateDeficienceSum);
                $fields.find("[name*=\"deficiences[cat]\"]").change(calculateDeficienceSum);
                $fields.find("[name*=\"deficiences[dominant]\"]").change(calculateDeficienceSum);
                $fields.find("[name*=\"deficiences[extremity]\"]").change(calculateDeficienceSum);
                $("#deficiences_917").append($fields);
                $fields.show();
                return false;
            };
            var calculateDeficienceSum = function () {
                var totals = $("form input[name*=\"deficiences[total]\"]");
                var cats = $("form input[name*=\"deficiences[cat]\"]");
                var percentages = $("form input[name*=\"deficiences[percentage]\"]");
                var ears = $("form input[name*=\"deficiences[ear]\"]");
                var ears_data = $("form input[name*=\"deficiences[ears]\"]");
                var dominants = $("form input[name*=\"deficiences[dominant]\"]");
                var dominantxs = $("form input[name*=\"deficiences[dominantx]\"]");

                var extremities = $("form input[name*=\"deficiences[extremity]\"]");
                var extremitiesx = $("form input[name*=\"deficiences[extremityx]\"]");

                var pcl_type = $("form input[name*=\"pcl_type\"]").val();


                var deficiences = $.map(percentages, function (input, k) {
                    var total = parseFloat($(input).val());
                    if (isNaN(total)) {
                        total = 0;
                    }
                    $(totals[k]).val(total);
                    return total;
                });

                var extremity_deficiences = [];
                var normal_deficiences = [];

                for (var i = 0; i < deficiences.length; i++) {
                    if ($(extremities[i]).prop("checked")) {
                        extremity_deficiences.push(deficiences[i]);
                        $(extremitiesx[i]).val(1);
                    } else {
                        normal_deficiences.push(deficiences[i]);
                        $(extremitiesx[i]).val(-1);
                    }

                }

                deficiences = normal_deficiences;
                deficiences.sort(function (a, b) {
                    return b - a;
                });
                var sum = null;

                for (var i = 0; i < deficiences.length; i++) {
                    if (!isNaN(deficiences[i])) {
                        if (sum == null) {
                            sum = deficiences[i];
                            continue;
                        }
                        sum = sum + ((50 - sum) * deficiences[i]) / 100;
                    }
                }

                if (sum == null) {
                    sum = 0;
                }

                for (var i = 0; i < extremity_deficiences.length; i++) {
                    if (!isNaN(extremity_deficiences[i])) {
                        sum = sum + ((100 - sum) * extremity_deficiences[i]) / 100;
                    }
                }

                sum_pond = sum;


                $("#deficiences_sum").text(Math.round(sum * 100) / 100);
                $("#deficiences_total").text(Math.round(sum_pond * 100) / 100);
                $("#deficiences_total_report").val($("#deficiences_total").text());
                $("#deficiences_final").text(Math.round(sum_pond * 100) / 100);

                var total = sum_pond + parseFloat($("#laboral_role_total").text());
                if (pcl_type != 3) {
                    var total = sum_pond + parseFloat($("#handicaps_total").text());
                }
                $("#pcl_percentage").text(Math.round(total * 100) / 100);
                $("#pcl_percentage_report").val($("#pcl_percentage").text());

                return sum;
            };
            $(document).ready(function () {

                @unless($activity->pcl && $activity->pcl->use_laboral_role)
                $(".use_laboral_role.field").hide();
                @endunless

                @unless($activity->pcl && $activity->pcl->laborally_active)
                $(".laborally_active .field, .laborally_active.field").hide();
                @endunless


                $(".ui.checkbox").checkbox({
                    onChecked: function () {
                        if ($(this).attr("name") == "use_laboral_role") {
                            $(".use_laboral_role.field").show();
                            $("input[name=laborally_active]").parent().checkbox("uncheck");
                        }
                        if ($(this).attr("name") == "laborally_active") {
                            $(".laborally_active .field, .laborally_active.field").show();
                            $("input[name=use_laboral_role]").parent().checkbox("uncheck");
                        }
                    },
                    onUnchecked: function () {
                        if ($(this).attr("name") == "use_laboral_role")
                            $(".use_laboral_role.field").hide();
                        if ($(this).attr("name") == "laborally_active")
                            $(".laborally_active .field, .laborally_active.field").hide();
                    }
                });

                $("form#dictamen a.red").click(function () {
                    $(this).parent().parent().remove();
                    calculateDeficienceSum();
                });

                $("[name=pcl_type]").change(function () {
                    if ($(this).val() == 1) {
                        $("#pcl_age_restriction").text('0');
                        if ($("#deficiences_total").text() > 0) {
                            $("#pcl_age_restriction").text('{{$activity->affiliate->pcl_age_restriction()}}');
                        }
                    } else if ($(this).val() == 2){
                        $("#pcl_age_restriction").text("0.0");
                    }
                });
                $("[name=applicant]").change(toggleSender);
                $("[name*=\"deficiences[ear]\"]").change(calculateDeficienceSum);
                $("[name*=\"deficiences[percentage]\"]").change(calculateDeficienceSum);
                $("[name*=\"deficiences[cat]\"]").change(calculateDeficienceSum);
                $("[name*=\"deficiences[dominant]\"]").change(calculateDeficienceSum);
                $("[name*=\"deficiences[extremity]\"]").change(calculateDeficienceSum);


                $("#laboral_role input").change(function () {
                    if ($("[name=laborally_active]").prop("checked")) {
                        @if($activity->pcl && $activity->pcl->pcl_type == 3)
                        var total = 0;
                        $.each($("#laboral_role input"), function (i, input) {
                            var inputValue = parseFloat($(input).val());
                            if (!isNaN(inputValue) &&
                                $(input).attr("name") != "laborally_active" &&
                                $(input).attr("name") != "use_laboral_role") {
                                if ($(input).attr("type") == "radio" && $(input).prop("checked"))
                                    total += inputValue;
                                else if ($(input).attr("type") == "hidden")
                                    total += inputValue;
                            }
                        });
                        @else
                        var total = '0';
                        if ($("#deficiences_total").text() > 0) {
                            $("#pcl_age_restriction").text('{{$activity->affiliate->pcl_age_restriction()}}');
                            total = {{$activity->affiliate->pcl_age_restriction()}};
                        }
                        $.each($("#laboral_role input"), function (i, input) {
                            if ($(input).attr("name") != "laborally_active" && $(input).attr("name") != "use_laboral_role") {
                                if ($(input).attr("type") == "radio" && $(input).prop("checked"))
                                    total += parseFloat($(input).val());
                                else if ($(input).attr("type") == "hidden")
                                    total += parseFloat($(input).val());
                            }
                        });
                        @endif
                            total = Math.round(total * 10) / 10;
                        $("#laboral_role_total").text(total);
                        $("#laboral_role_total_report").val($("#laboral_role_total").text(total));
                        $("#pcl_percentage").text(total + parseFloat($("#deficiences_total").text()) + parseFloat($("#handicaps_total").text()));
                        $("#pcl_percentage_report").val($("#pcl_percentage").text());
                    } else if ($("[name=use_laboral_role]").prop("checked")) {
                        var total = parseFloat($("[name=role_category]").val());
                        if (isNaN(total)) {
                            total = 0;
                        }

                        $("#laboral_role_total").text(total);
                        $("#laboral_role_total_report").val($("#laboral_role_total").text(total));
                        $("#pcl_percentage").text(total + parseFloat($("#deficiences_total").text()) + parseFloat($("#handicaps_total").text()));
                        $("#pcl_percentage_report").val($("#pcl_percentage").text());
                    } else {
                        var total = 0;

                        $("#laboral_role_total").text(total);
                        $("#laboral_role_total_report").val($("#laboral_role_total").text(total));
                        $("#pcl_percentage").text(total + parseFloat($("#deficiences_total").text()) + parseFloat($("#handicaps_total").text()));
                        $("#pcl_percentage_report").val($("#pcl_percentage").text());
                    }
                    $("#pcl_percentage").text(Math.round($("#pcl_percentage").text() * 100) / 100);
                    $("#pcl_percentage_report").val($("#pcl_percentage").text());
                });

                $("#handicaps input").change(function () {
                    var total = 0;
                    $.each($("#handicaps input"), function (i, input) {
                        if ($(input).attr("type") == "radio" && $(input).prop("checked"))
                            total += parseFloat($(input).val());
                        else if ($(input).attr("type") == "hidden")
                            total += parseFloat($(input).val());
                    });

                    total = Math.round(total * 10) / 10;
                    $("#handicaps_total").text(total);
                    $("#pcl_percentage").text(total + parseFloat($("#deficiences_total").text()) + parseFloat($("#laboral_role_total").text()));
                    $("#pcl_percentage").text(Math.round($("#pcl_percentage").text() * 100) / 100);
                    $("#pcl_percentage_report").val($("#pcl_percentage").text());

                });
            });
        </script>
    @endif
    <script type="text/javascript">
        var calculateIMC = function () {
            var weight = parseFloat($("[name=weight]").val());
            var height = parseFloat($("[name=height]").val());

            if (height > 0) {
                var imc = Math.round((weight / Math.pow(height / 100, 2)) * 10) / 10;

                if (imc < 18.5) {
                    imc += " (Inferior a lo normal)";
                } else if (imc >= 18.5 && imc < 25) {
                    imc += " (Normal)";
                } else if (imc >= 25 && imc < 30) {
                    imc += " (Superior a lo normal)";
                } else if (imc >= 30) {
                    imc += " (Obesidad)";
                } else {
                    imc += " ()";
                }

                $(".imc").text(imc);
            } else {
                $(".imc").text("");
            }
        };
        var generatePDF = function () {
            $("form#dictamen").attr("target", "_blank");
            $("form#dictamen").attr("action", '{{secure_url('/servicio/' . $activity->id . '/pcl/pdf')}}');
            $("form#dictamen").submit();
            $("form#dictamen").removeAttr("target");
            $("form#dictamen").attr("action", '{{secure_url('/servicio/' . $activity->id . '/pcl/save')}}');
            return false;
        };
        var generatePDFValoration = function () {
            $("form#dictamen").attr("target", "_blank");
            $("form#dictamen").attr("action", '{{secure_url('/servicio/' . $activity->id . '/pcl/pdf_valoration')}}');
            $("form#dictamen").submit();
            $("form#dictamen").removeAttr("target");
            $("form#dictamen").attr("action", '{{secure_url('/servicio/' . $activity->id . '/pcl/save')}}');
            return false;
        };
        var generatePDFRejection = function () {
            $("form#dictamen").attr("target", "_blank");
            $("form#dictamen").attr("action", '{{secure_url('/servicio/' . $activity->id . '/pcl/pdf_rejection')}}');
            $("form#dictamen").submit();
            $("form#dictamen").removeAttr("target");
            $("form#dictamen").attr("action", '{{secure_url('/servicio/' . $activity->id . '/pcl/save')}}');
            return false;
        };

        var toggleSender = function () {
            if ($("[name=sender]").val() == 5 || $("[name=sender]").val() == 8) {
                $(".sender .field").show();
            } else {
                $(".sender .field").hide();
            }
        };

        var cie10 = [];
        var doctors = [];


        $(document).ready(function () {
            calculateIMC();
            $(".ui.accordion .ui.grid .row").css("padding-top", 0);
            $(".ui.accordion .ui.grid .column").css("padding-top", 0);

            $(".ui.accordion").accordion({
                exclusive: false
            });

            $("form#dictamen a.red").click(function () {
                $(this).parent().parent().remove();
                calculateDeficienceSum();
            });
            $("form .ui.dropdown").dropdown({
                forceSelection: false
            });
            $("form .datepicker").pickadate({
                selectYears: true,
                selectMonths: true,
                max: new Date(),
                formatSubmit: "yyyy-mm-dd",
                format: "yyyy-mm-dd"

            });

            $(".timepicker").pickatime({
                formatSubmit: "H:i:00",
                interval: 10,
                min: [7, 0],
                max: [19, 50]
            });
            $.getJSON("/js/cie10.json", function (json) {
                cie10 = json;
                $("form .ui.search.code").search({
                    source: cie10,
                    fields: {
                        title: "COD",
                        description: "DESCRIPTION"
                    },
                    searchFields: ["COD", "DESCRIPTION"],
                    regExp: {
                        escape: /[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,
                        beginsWith: ""
                    },
                    showNoResults: true,
                    maxResults: 250,
                    searchFullText: false,
                    error: {
                        noResults: 'No se encontraron resultados para tu búsqueda.'
                    },
                    onSelect: function (result, response) {
                        $(this).parents(".fields").find("input.description").val(result.DESCRIPTION);
                    }
                });
            });
            $("form .ui.search.doctors input").change(function () {
                var valid = false;

                $(this).parents(".fields").find("input.name").val("");
                for (var i = 0; i < doctors.length; i++) {
                    if (doctors[i].doc_number == $(this).val().toUpperCase()) {
                        $(this).parents(".fields").find("input.doc_full_name").val(doctors[i].full_name);
                        $("#doc_doc_type_dropdown").dropdown('set selected', doctors[i].doc_type);
                        $(this).parents(".fields").find("input.doc_doc_number").val(doctors[i].doc_number);
                        $(this).parents(".fields").find("input.doc_medical_record").val(doctors[i].rm);
                        $(this).parents(".fields").find("input.doc_rethus").val(doctors[i].renu);
                        $(this).parents(".fields").find("input.doc_license").val(doctors[i].license);
                        valid = true;
                    }
                }
            });
            $.getJSON("/api/users", function (json) {
                doctors = json;
                $("form .ui.search.doctors").search({
                    source: doctors,
                    fields: {
                        title: "doc_number",
                        description: "full_name"
                    },
                    searchFields: ["doc_number", "full_name"],
                    regExp: {
                        escape: /[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,
                        beginsWith: ""
                    },
                    showNoResults: true,
                    maxResults: 250,
                    searchFullText: false,
                    error: {
                        noResults: 'No se encontraron resultados para tu búsqueda.'
                    },
                    onSelect: function (result, response) {
                        $(this).parents().parents().parents().find("input.doc_full_name").val(result.full_name);
                        $(this).parents().parents().parents().find("input.doc_doc_number").val(result.doc_number);
                        $("#doc_doc_type_dropdown").dropdown('set selected', result.doc_type);
                        $(this).parents().parents().parents().find("input.doc_medical_record").val(result.rm);
                        $(this).parents().parents().parents().find("input.doc_rethus").val(result.renu);
                        $(this).parents().parents().parents().find("input.doc_license").val(result.license);
                    }
                });
            });
            var colombia = {};
            $.getJSON('/js/colombia.json', function (json) {
                colombia = json;
                for (var i = 0; i < colombia.length; i++) {
                    $("#departments .menu").append('<div class="item" data-value="' + colombia[i].code + '">' + colombia[i].name + '</div>');
                    $("#departments2 .menu").append('<div class="item" data-value="' + colombia[i].code + '">' + colombia[i].name + '</div>');
                    $("#departments3 .menu").append('<div class="item" data-value="' + colombia[i].code + '">' + colombia[i].name + '</div>');
                    $("#departments4 .menu").append('<div class="item" data-value="' + colombia[i].code + '">' + colombia[i].name + '</div>');
                    $("#departments5 .menu").append('<div class="item" data-value="' + colombia[i].code + '">' + colombia[i].name + '</div>');
                }

                var department = $("#departments").dropdown('get value');

                for (var i = 0; i < colombia.length; i++) {
                    if (department == colombia[i].code) {
                        for (var j = 0; j < colombia[i].municipalities.length; j++) {
                            var mun = colombia[i].municipalities[j];
                            $("#municipalities .menu").append('<div class="item" data-value="' + mun.code + '">' + mun.name + '</div>');
                        }
                    }
                }

                var department2 = $("#departments2").dropdown('get value');

                for (var i = 0; i < colombia.length; i++) {
                    if (department2 == colombia[i].code) {
                        for (var j = 0; j < colombia[i].municipalities.length; j++) {
                            var mun = colombia[i].municipalities[j];
                            $("#municipalities2 .menu").append('<div class="item" data-value="' + mun.code + '">' + mun.name + '</div>');
                        }
                    }
                }
                var department3 = $("#departments3").dropdown('get value');

                for (var i = 0; i < colombia.length; i++) {
                    if (department3 == colombia[i].code) {
                        for (var j = 0; j < colombia[i].municipalities.length; j++) {
                            var mun = colombia[i].municipalities[j];
                            $("#municipalities3 .menu").append('<div class="item" data-value="' + mun.code + '">' + mun.name + '</div>');
                        }
                    }
                }
                var department4 = $("#departments4").dropdown('get value');

                for (var i = 0; i < colombia.length; i++) {
                    if (department4 == colombia[i].code) {
                        for (var j = 0; j < colombia[i].municipalities.length; j++) {
                            var mun = colombia[i].municipalities[j];
                            $("#municipalities4 .menu").append('<div class="item" data-value="' + mun.code + '">' + mun.name + '</div>');
                        }
                    }
                }
                var department5 = $("#departments5").dropdown('get value');

                for (var i = 0; i < colombia.length; i++) {
                    if (department5 == colombia[i].code) {
                        for (var j = 0; j < colombia[i].municipalities.length; j++) {
                            var mun = colombia[i].municipalities[j];
                            $("#municipalities5 .menu").append('<div class="item" data-value="' + mun.code + '">' + mun.name + '</div>');
                        }
                    }
                }

                $("#municipalities, #departments, #municipalities2, #departments2, #municipalities3, #departments3, #municipalities4, #departments4, #municipalities5, #departments5").dropdown({
                    sortSelect: false,
                    forceSelection: false
                });

                $("#departments").change(function () {
                    var department = $(this).dropdown('get value');

                    $("#municipalities").dropdown('clear');
                    $("#municipalities .menu").empty();

                    for (var i = 0; i < colombia.length; i++) {
                        if (department == colombia[i].code) {
                            for (var j = 0; j < colombia[i].municipalities.length; j++) {
                                var mun = colombia[i].municipalities[j];
                                $("#municipalities .menu").append('<div class="item" data-value="' + mun.code + '">' + mun.name + '</div>');
                            }

                            $("#municipalities input").val(colombia[i].municipalities[0].code);
                        }
                    }

                    $("#municipalities").dropdown({
                        sortSelect: false,
                        forceSelection: false
                    });
                });

                $("#departments2").change(function () {
                    var department = $(this).dropdown('get value');

                    $("#municipalities2").dropdown('clear');
                    $("#municipalities2 .menu").empty();

                    for (var i = 0; i < colombia.length; i++) {
                        if (department == colombia[i].code) {
                            for (var j = 0; j < colombia[i].municipalities.length; j++) {
                                var mun = colombia[i].municipalities[j];
                                $("#municipalities2 .menu").append('<div class="item" data-value="' + mun.code + '">' + mun.name + '</div>');
                            }

                            $("#municipalities2 input").val(colombia[i].municipalities[0].code);
                        }
                    }

                    $("#municipalities2").dropdown({
                        sortSelect: false,
                        forceSelection: false
                    });
                });

                $("#departments2").change(function () {
                    var department = $(this).dropdown('get value');

                    $("#municipalities2").dropdown('clear');
                    $("#municipalities2 .menu").empty();

                    for (var i = 0; i < colombia.length; i++) {
                        if (department == colombia[i].code) {
                            for (var j = 0; j < colombia[i].municipalities.length; j++) {
                                var mun = colombia[i].municipalities[j];
                                $("#municipalities2 .menu").append('<div class="item" data-value="' + mun.code + '">' + mun.name + '</div>');
                            }

                            $("#municipalities2 input").val(colombia[i].municipalities[0].code);
                        }
                    }

                    $("#municipalities2").dropdown({
                        sortSelect: false,
                        forceSelection: false
                    });
                });

                $("#departments3").change(function () {
                    var department = $(this).dropdown('get value');

                    $("#municipalities3").dropdown('clear');
                    $("#municipalities3 .menu").empty();

                    for (var i = 0; i < colombia.length; i++) {
                        if (department == colombia[i].code) {
                            for (var j = 0; j < colombia[i].municipalities.length; j++) {
                                var mun = colombia[i].municipalities[j];
                                $("#municipalities3 .menu").append('<div class="item" data-value="' + mun.code + '">' + mun.name + '</div>');
                            }

                            $("#municipalities3 input").val(colombia[i].municipalities[0].code);
                        }
                    }

                    $("#municipalities3").dropdown({
                        sortSelect: false,
                        forceSelection: false
                    });
                });

                $("#departments4").change(function () {
                    var department = $(this).dropdown('get value');

                    $("#municipalities4").dropdown('clear');
                    $("#municipalities4 .menu").empty();

                    for (var i = 0; i < colombia.length; i++) {
                        if (department == colombia[i].code) {
                            for (var j = 0; j < colombia[i].municipalities.length; j++) {
                                var mun = colombia[i].municipalities[j];
                                $("#municipalities4 .menu").append('<div class="item" data-value="' + mun.code + '">' + mun.name + '</div>');
                            }

                            $("#municipalities4 input").val(colombia[i].municipalities[0].code);
                        }
                    }

                    $("#municipalities4").dropdown({
                        sortSelect: false,
                        forceSelection: false
                    });
                });

                $("#departments5").change(function () {
                    var department = $(this).dropdown('get value');

                    $("#municipalities5").dropdown('clear');
                    $("#municipalities5 .menu").empty();

                    for (var i = 0; i < colombia.length; i++) {
                        if (department == colombia[i].code) {
                            for (var j = 0; j < colombia[i].municipalities.length; j++) {
                                var mun = colombia[i].municipalities[j];
                                $("#municipalities5 .menu").append('<div class="item" data-value="' + mun.code + '">' + mun.name + '</div>');
                            }

                            $("#municipalities5 input").val(colombia[i].municipalities[0].code);
                        }
                    }

                    $("#municipalities5").dropdown({
                        sortSelect: false,
                        forceSelection: false
                    });
                });
            });

            $("form input").keydown(function (event) {
                if (event.keyCode == 13) {
                    event.preventDefault();
                    return false;
                }
            });

            @if ($activity->plantilla && $activity->plantilla->firm_date)
            // $('form a.basic.blue.button').hide();
            // $('form a.basic.red.button').hide();
            // $('form input, form textarea').prop('disabled', true);
            // $('form .controversy input, form .controversy textarea').prop('disabled', false);
            // $('form .pcl input, form .pcl textarea').prop('disabled', false);
            // $('form .ui.dropdown').addClass('disabled');
            // $('form .controversy .ui.dropdown').removeClass('disabled');
            // $('form .pcl .ui.dropdown').removeClass('disabled');
            @endif

            @if (Auth::user()->isViewer())
            $(".datepicker").pickadate("picker").stop();
            $("#dictamen .ui.search input").attr("disabled", "disabled");
            $("#dictamen input, #dictamen textarea").attr("readonly", "readonly");
            $("#dictamen .ui.dropdown").addClass("disabled");
            $("#dictamen .button").remove();
            @endif
        });
    </script>

    <!-- Incluir Moment.js desde un CDN -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/moment-business-days"></script>

    <script>
        $(document).ready(function () {
            $("[name=\"rejection_case\"]").change(rejectionCaseShow);
            $("[name=\"n_attorney_allow_sending_email\"]").change(activenAttorneyAllowSendingEmail);
            $("[name=\"n_attorney_rad_father\"]").change(activenAttorneyRadFather);
            $("[name=\"n_emp_allow_sending_email\"]").change(activenEmpAllowSendingEmail);
            $("[name=\"n_emp_type_affiliate\"]").change(activenEmpTypeAffiliate);
            activenAttorneyAllowSendingEmail();
            activenAttorneyRadFather();
            activenEmpAllowSendingEmail();
            activenEmpTypeAffiliate();
        });
        const activenAttorneyAllowSendingEmail = () => {
            const nAttorneyAllowSendingEmail = $("[name=\"n_attorney_allow_sending_email\"]").val();
            if (nAttorneyAllowSendingEmail === "SI") {
                $("[name=\"n_attorney_email\"]").parent().show();
            } else {
                $("[name=\"n_attorney_email\"]").parent().hide();
                $("[name=\"n_attorney_email\"]").val("");
            }
        };

        const rejectionCaseShow = () => {
            const rejectionCase = $("[name=\"rejection_case\"]").val();
            if (rejectionCase === "SI") {
                $("[name=\"rejection_causal\"]").parent().show();
                $("[name=\"tutelage_observation\"]").parent().show();
            } else {
                $("[name=\"rejection_causal\"]").parent().hide();
                $("[name=\"tutelage_observation\"]").parent().hide();
                $("[name=\"rejection_causal\"]").val("");
                $("[name=\"tutelage_observation\"]").val("");
            }
        };

        const activenAttorneyRadFather = () => {
            const nAttorneyRadFather = $("[name=\"n_attorney_rad_father\"]").val();
            if (nAttorneyRadFather !== "" && nAttorneyRadFather !== null) {
                $("[name=\"n_attorney_rad_ri_answer\"]").parent().show();
            } else {
                $("[name=\"n_attorney_rad_ri_answer\"]").parent().hide();
                $("[name=\"n_attorney_rad_ri_answer\"]").val("");
            }
        };

        const activenEmpAllowSendingEmail = () => {
            const nEmpAllowSendingEmail = $("[name=\"n_emp_allow_sending_email\"]").val();
            if (nEmpAllowSendingEmail === "SI") {
                $("[name=\"n_emp_email\"]").parent().show();
            } else {
                $("[name=\"n_emp_email\"]").parent().hide();
                $("[name=\"n_emp_email\"]").val("");
            }
        };

        const activenEmpTypeAffiliate = () => {
            const nEmpAllowSendingEmail = $("[name=\"n_emp_type_affiliate\"]").val();
            if (nEmpAllowSendingEmail === "DEPENDIENTE") {
                $("[name=\"n_emp_name_third_party_representative\"]").parent().show();
                $("[name=\"n_emp_address\"]").parent().show();
                $("[name=\"n_emp_phone\"]").parent().show();
                $("[name=\"n_emp_city\"]").parent().show();
                $("[name=\"n_emp_department\"]").parent().show();
                $("[name=\"n_emp_allow_sending_email\"]").parent().parent().show();
                $("[name=\"n_emp_email\"]").parent().show();
                $("[name=\"n_emp_sd_notification_request\"]").parent().show();
                $("[name=\"n_emp_application_date\"]").parent().show();
                $("[name=\"n_emp_rad_supports_notification\"]").parent().show();
                $("[name=\"n_emp_bzg_notification_support_creation_date\"]").parent().show();
                $("[name=\"n_emp_average_costs\"]").parent().parent().show();
            } else {
                $("[name=\"n_emp_name_third_party_representative\"]").val("");
                $("[name=\"n_emp_address\"]").val("");
                $("[name=\"n_emp_phone\"]").val("");
                $("[name=\"n_emp_city\"]").val("");
                $("[name=\"n_emp_department\"]").val("");
                $("[name=\"n_emp_allow_sending_email\"]").parent().dropdown("clear");
                $("[name=\"n_emp_email\"]").val("");
                $("[name=\"n_emp_sd_notification_request\"]").val("");

                let picker1 = $("[name=\"n_emp_application_date\"]").pickadate("picker");
                if (picker1) {
                    picker1.set("clear");
                }

                $("[name=\"n_emp_rad_supports_notification\"]").val("");

                let picker2 = $("[name=\"n_emp_bzg_notification_support_creation_date\"]").pickadate("picker");
                if (picker2) {
                    picker2.set("clear");
                }

                $("[name=\"n_emp_average_costs\"]").parent().dropdown("clear");

                $("[name=\"n_emp_name_third_party_representative\"]").parent().hide();
                $("[name=\"n_emp_address\"]").parent().hide();
                $("[name=\"n_emp_phone\"]").parent().hide();
                $("[name=\"n_emp_city\"]").parent().hide();
                $("[name=\"n_emp_department\"]").parent().hide();
                $("[name=\"n_emp_allow_sending_email\"]").parent().parent().hide();
                $("[name=\"n_emp_email\"]").parent().hide();
                $("[name=\"n_emp_sd_notification_request\"]").parent().hide();
                $("[name=\"n_emp_application_date\"]").parent().hide();
                $("[name=\"n_emp_rad_supports_notification\"]").parent().hide();
                $("[name=\"n_emp_bzg_notification_support_creation_date\"]").parent().hide();
                $("[name=\"n_emp_average_costs\"]").parent().parent().hide();
            }
        };

        $.get("/holidays", function (data) {
            window.holidays = data;
            calculateDate();
            calculateDateExam();
            calculateProrrogationDate();
            calculateProrrogationDateExam();
            // calculateMaxManifestationDate();
        });

        function isHoliday(selectedDate) {
            const serviceId  = {!! json_encode($activity->service_id) !!};

            // Si es domingo o sábado, no es un día hábil
            if (selectedDate.getDay() === 0 || selectedDate.getDay() === 6) {
                return true;
            }

            let dd = String(selectedDate.getDate()).padStart(2, '0');
            let mm = String(selectedDate.getMonth() + 1).padStart(2, '0'); //January is 0!
            let yyyy = selectedDate.getFullYear();
            let dateStr = yyyy + '-' + mm + '-' + dd;

            // Comprobar si el día es festivo y si su service_id es null o coincide con el serviceId
            return window.holidays && window.holidays.some(holiday =>
                holiday.holiday == dateStr &&
                (holiday.service_id == null || holiday.service_id == serviceId)
            );
        }

        function calculateDate() {
            let dateValue = $("#effective_sol_document_date").val();

            if (dateValue === "") {
                return;
            }

            let selectedDate = new Date(dateValue);

            // Añade 30 días
            selectedDate.setDate(selectedDate.getDate() + 30);
            // Mientras sea sábado, domingo o un día festivo...
            while (isHoliday(selectedDate)) {
                // Añade un día
                selectedDate.setDate(selectedDate.getDate() + 1);
            }

            // Formatea la fecha en un formato de fecha string
            let dd = String(selectedDate.getDate()).padStart(2, "0");
            let mm = String(selectedDate.getMonth() + 1).padStart(2, "0"); // Enero es 0
            let yyyy = selectedDate.getFullYear();
            selectedDate = yyyy + "-" + mm + "-" + dd;
            // Asigna el valor calculado al campo máximo de entrega de documentos
            $("#compliance_sol_doc_date").val(selectedDate);
        }
        function calculateProrrogationDate() {
            let dateValue = $("#effective_sol_document_date").val();

            if (dateValue === "") {
                return;
            }

            let effectiveDate = new Date(dateValue);

            effectiveDate.setDate(effectiveDate.getDate() + 60);
            while (isHoliday(effectiveDate)) {
                effectiveDate.setDate(effectiveDate.getDate() + 1);
            }
            $("#compliance_extension_date").val(moment(effectiveDate).format("YYYY-MM-DD"));
        }

        function calculateProrrogationDateExam() {
            let dateValueExam = $("#effective_sol_document_date_exam").val();

            if (dateValueExam === "") {
                return;
            }

            let effectiveDateExam = new Date(dateValueExam);

            effectiveDateExam.setDate(effectiveDateExam.getDate() + 60);
            while (isHoliday(effectiveDateExam)) {
                effectiveDateExam.setDate(effectiveDateExam.getDate() + 1);
            }
            $("#compliance_extension_date_exam").val(moment(effectiveDateExam).format("YYYY-MM-DD"));
        }

        function calculateDateExam() {
            let dateValue = $("#effective_sol_document_date_exam").val();

            if (dateValue === "") {
                return;
            }

            let selectedDate = new Date(dateValue);
            // Añade 30 días
            selectedDate.setDate(selectedDate.getDate() + 30);

            // Mientras sea sábado, domingo o un día festivo...
            while (isHoliday(selectedDate)) {
                // Añade un día
                selectedDate.setDate(selectedDate.getDate() + 1);
            }

            // Formatea la fecha en un formato de fecha string
            let dd = String(selectedDate.getDate()).padStart(2, "0");
            let mm = String(selectedDate.getMonth() + 1).padStart(2, "0"); // Enero es 0
            let yyyy = selectedDate.getFullYear();
            selectedDate = yyyy + "-" + mm + "-" + dd;

            // Asigna el valor calculado al campo máximo de entrega de documentos
            $("#sol_doc_term_accomplished_date").val(selectedDate);
        }

        function activateEffectiveDelivery() {
            if ($("#effectiveDelivery").val() === 'ENTREGA EFECTIVA') {
                $("#effectiveDeliveryDoc").show();
            } else {
                $("#effectiveDeliveryDoc").hide();
            }
        }

        $(document).ready(function () {
            $("#effective_sol_document_date").on("change", calculateDate);

            $("#effective_sol_document_date_exam").on("change", calculateDateExam);

            function isHoliday(selectedDate) {
                // Si es domingo o sábado, no es un día hábil
                if (selectedDate.getDay() === 0 || selectedDate.getDay() === 6) {
                    return true;
                }

                let dd = String(selectedDate.getDate()).padStart(2, '0');
                let mm = String(selectedDate.getMonth() + 1).padStart(2, '0'); //January is 0!
                let yyyy = selectedDate.getFullYear();
                let dateStr = yyyy + '-' + mm + '-' + dd;

                // Asegurarse de que window.holidays se ha definido, de lo contrario considerarlo como no festivo por defecto
                return window.holidays ? window.holidays.includes(dateStr) : false;
            }

            $("#effective_sol_document_date").on("change", calculateProrrogationDate);

            // Examns
            $("#effective_sol_document_date_exam").on("change", calculateProrrogationDateExam);

            $("#effectiveDeliveryDoc").hide();

            $("#requestForDocs").on('change', function () {
                if ($("#requestForDocs").val() === 'SI') {
                    $("#requestDocsDrowpDown").addClass("active");
                    $("#requestDocsContent").addClass("active");
                    $("#68").removeClass("active");
                    $("#medicalAppoimentContent").removeClass("active");
                } else {
                    $("#requestDocsDrowpDown").removeClass("active");
                    $("#requestDocsContent").removeClass("active");
                    $("#68").addClass("active");
                    $("#medicalAppoimentContent").addClass("active");
                }
            });
            $("#effectiveDelivery").on('change', function () {
                activateEffectiveDelivery()
            });
            activateEffectiveDelivery()
        });
    </script>
    {{--  VALIDACIÓN DE PERMISOS (Siempre al final de todo)  --}}
    <script>
        $(document).ready(function () {
            const isAssignedUser = {!! json_encode($activity->user->id == Auth::user()->id  || Auth::user()->isAdmin() || Auth::user()->isAdmin2() ) !!};
            const permissions = {!! json_encode(Auth::user()->service_permissions_list($activity->service_id)) !!};
            if (permissions.every(value => value.area_permissions[0]?.edit !== 1 || !isAssignedUser)) {
                $("#button_save").addClass("disabled");
            }
            for (const permission of permissions) {
                if (permission.area_permissions[0]?.view !== 1) {
                    $(`#${permission.id}`).css("pointer-events", "none");
                }
                if (permission.area_permissions[0]?.edit !== 1 || !isAssignedUser) {
                    const content = $(`#${permission.id}`).next(".content");
                    content.find('input, .ui.dropdown, .datepicker').css('pointer-events', 'none');
                    content.find('textarea').attr('readonly', 'readonly');
                    content.find('.ui.button').addClass('disabled');
                }
            }
        });
    </script>
@endsection
