@extends('layouts.main')

@section('title', 'Reporte de planilla')

@section('menu')
    @parent
@endsection

@section('content')
    <div class="ui container">

        <h14 class="ui header">
            Formulario variación
            <div class="sub header">Campos con <span style="color: red;" class="required">*</span> obligatorios.</div>
        </h14>
        <div class="card">
        </div>
    
        <div class="intermediary ui segment">
            <form class="ui attached form">
                {{ csrf_field() }}
                <h4 class="title ui dividing header">Datos planilla reportada</h4>
                <div class="field">
                    <div class="three fields doble_input">
                        <div class="field">
                            <label for="">TI del Tomador</label>
                            <div>
                                <input type="text" id="name_c" name="name_c" value="{{ $policySort->identification_number }}" readonly
                                    placeholder="Nombre de la Correduría *">
                            </div>
                        </div>
                        <div class="field">
                            <label for="">Nombre del Tomador</label>
                            <div>
                                <input type="text" id="name_c_a" readonly value="{{ $policySort->name }}" name="name_c_a"
                                    placeholder="Nombre del Corredor y/o Asesor *">
                            </div>
                        </div>

                        <div class="field">
                            <label for="">No. Poliza asociado</label>
                            <div>
                                <input type="text" id="code" value="{{ $policySort->policy_spreadsheet_id }}" name="code" readonly placeholder="Código *">
                            </div>
                        </div>
                    </div>
                    <div class="three fields doble_input">
                        <div class="field">
                            <label for="">Fecha de reporte Planilla</label>
                            <div>
                                <input type="text" id="name_c" value="{{ $policySort->created_at }}" name="name_c" readonly
                                    placeholder="Nombre de la Correduría *">
                            </div>
                        </div>
                        <div class="field">
                            <label for="">Periodo reportado</label>
                            <div>
                                <input type="text" id="name_c_a" value="{{ $policySort->identification_number }}" readonly name="name_c_a"
                                    placeholder="Nombre del Corredor y/o Asesor *">
                            </div>
                        </div>

                        <div class="field">
                            <label for="">Salario Mensual</label>
                            <div>
                                <input type="text" id="code" value="{{ $policySort->monthly_salary }}" name="code" readonly placeholder="Código *">
                            </div>
                        </div>
                    </div>
                    <div class="three fields doble_input">
                        <div class="field">
                            <label for="">Tipo de Jornada</label>
                            <div>
                                <input type="text" id="name_c" value="{{ $policySort->work_shift_type }}" name="name_c" readonly
                                    placeholder="Nombre de la Correduría *">
                            </div>
                        </div>
                        <div class="field">
                            <label for="">Días / Horas</label>
                            <div class="two fields">
                                <div class="field">
                                  <input type="text" name="" value="{{ $policySort->days }}" placeholder="First Name">
                                </div>
                                <div class="field">
                                  <input type="text" name="" value="{{ $policySort->hours }}" placeholder="Last Name">
                                </div>
                              </div>
                        </div>

                        <div class="field">
                            <label for="">Ocupación</label>
                            <div>
                                <input type="text" id="code" value="{{ $policySort->occupation }}" name="code" readonly placeholder="Código *">
                            </div>
                        </div>
                    </div>
                </div>
                <div style="display: none" id="message">
                    <div class="ui teal message">Se ha agregado un dato</div>
                </div>
                {{-- <div class="ui button" tabindex="0">Guardar</div> --}}
            </form>

            <!-- crear funcion para representar fallo en datos del intermediario -->
            <style>
                /*******************************
                    intermediary
            *******************************/

                .intermediary .doble_input {
                    margin-bottom: 1.5em !important;
                }

                .intermediary .title {
                    margin-top: 0em !important;
                    text-align: center;
                }

                .intermediary .ui.dividing.header {
                    padding-bottom: 1.214286rem;
                    border-bottom: 1px solid rgba(34, 36, 38, .15);
                }

                /*******************************
                    intermediary
            *******************************/
            </style>

        </div>
        <div style="display: none" id="loading" class="ui active centered inline loader"></div>

        <script type="text/javascript">
     

            $('.ui.dropdown').dropdown();
        </script>

    @endsection
