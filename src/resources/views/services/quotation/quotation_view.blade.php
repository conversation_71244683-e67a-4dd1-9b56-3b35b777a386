@extends('layouts.main')

@section('title', 'Template')

@section('menu')
    @parent
@endsection

@section('content')
    <div class="ui basic segment container">
        <div class="ui header"> Cotización generada #{{$activity->quotation->formatNumber()}} </div>
        <form autocomplete="off" action="{{secure_url("servicio/{$activity->id}/quotation/save")}}" id="dictamen"
              method="post" class="ui small form">

            <div class="ui styled fluid accordion accordion-spacing">

                <div class="title"><i class="dropdown icon"></i> Datos del intermediario</div>
                <div class="content">
                    <div class="two fields">
                        <div class="field required">
                            <label for="input1">Nombre de la correduría</label>
                            <input readonly type="text" id="input1"
                                   value="{{ ucwords(strtolower($quotation->brokerage_name))}}">
                        </div>
                        <div class="field required">
                            <label for="input2">Nombre del corredor y/o asesor</label>
                            <input readonly type="text" id="input2"
                                   value="{{ mb_convert_case(mb_strtolower( $quotation->advisor_name ?? ''), MB_CASE_TITLE, "UTF-8") }}">
                        </div>
                    </div>
                    <div class="two fields">
                        <div class="field required">
                            <label for="input3">Código</label>
                            <input readonly type="text" id="input3" value="{{$quotation->code}}">
                        </div>
                        <div class="field required">
                            <label for="input4">Correo electrónico nuevos negocios</label>
                            <input readonly type="text" id="input4" value="{{$quotation->email}}">
                        </div>
                    </div>

                </div>
                {{--DATOS DEL TOMADOR--}}
                <div class="title"><i class="dropdown icon"></i> Datos de tomador</div>
                <div class="content">
                    <div class="three fields">
                        <div class="field required">
                            <label for="input1">Código único</label>
                            <input readonly type="text" id="input1"
                                   value="{{ str_pad($affiliate->id ?: '', 6, '0', STR_PAD_LEFT) }}">
                        </div>
                        <div class="field required">
                            <label for="input1">Tipo de identificación</label>
                            <input readonly type="text" id="input1" value="{{$affiliate->doc_type}}">
                        </div>
                        <div class="field required">
                            <label for="input2">Número de identificación</label>
                            <input readonly type="text" id="input2" value="{{$affiliate->doc_number}}">
                        </div>
                    </div>
                    <div class="three fields">
                        <div class="required field">
                            <label for="first_name">Nombre</label>
                            <input class="prompt" id="first_name" type="text" name="first_name"
                                   value="{{ mb_convert_case(mb_strtolower( $affiliate->first_name ?? ''), MB_CASE_TITLE, "UTF-8") }}" readonly>
                        </div>
                        <div class="required field">
                            <label for="phone">Teléfonos</label>
                            <input class="prompt" id="phone" type="text" name="phone"
                                   value="{{$affiliate->phone}}" readonly>
                        </div>
                        <div class="required field">
                            <label for="email">Correo electrónico de notificaciones</label>
                            <input class="prompt" id="email" type="text" name="email"
                                   value="{{$affiliate->email}}" readonly>
                        </div>
                    </div>
                    <div class="three fields">
                        <div class="field" style="pointer-events: none">
                            <label>Sector</label>
                            <div class="ui radio checkbox" style="margin-right: 70px;">
                                <input type="radio" name="sector" value="public"
                                        {{ old('sector', $quotation->economic_activity ?? '') == 'public' ? 'checked' : '' }}>
                                <label>Público</label>
                            </div>
                            <div class="ui radio checkbox">
                                <input type="radio" name="sector" value="private"
                                        {{ old('sector', $quotation->economic_activity ?? '') == 'private' ? 'checked' : '' }}>
                                <label>Privado</label>
                            </div>
                        </div>

{{--                        <div class="required field" style="pointer-events: none">--}}
{{--                            <label>Código de actividad económica</label>--}}
{{--                            <div class="ui search code">--}}
{{--                                <div class="ui icon input">--}}
{{--                                    <input id="economic_activity_code" class="prompt" name="diagnostics[cod][]"--}}
{{--                                           type="text" readonly--}}
{{--                                           value="{{ old('diagnostics[cod][]', $quotation->activity_economic_id ?? '') }}"--}}
{{--                                           autocomplete="off">--}}
{{--                                    <i class="search icon"></i>--}}
{{--                                </div>--}}
{{--                                <div class="results"></div>--}}
{{--                            </div>--}}
{{--                        </div>--}}
{{--                        <div class="required field" style="pointer-events: none">--}}
{{--                            <label>Nombre de la actividad económica</label>--}}
{{--                            <input id="economic_activity_name" class="prompt" name="diagnostics[description][]"--}}
{{--                                   type="text"--}}
{{--                                   maxlength="500"--}}
{{--                                   value="{{ old('diagnostics[description][]', $activity_economic_name ?? '') }}"--}}
{{--                                   readonly>--}}
{{--                        </div>--}}

                        @if($economicActivity->economic_branch && $economicActivity->economic_branch->branch_name)  {{--  cuando es publica no tiene rama--}}
                            <div class="required field">
                                <label>Rama general de actividad económica</label>
                                <input id="branch_activity" class="prompt" name="branch_activity" type="text" readonly value="{{ $economicActivity->economic_branch->branch_name ?? '' }}">
                            </div>
                        @endif

                        <div class="required field">
                            <label>Actividad económica especifica</label>
                            <textarea readonly rows="2">{{ $economicActivity->code.' - '.$economicActivity->activity_name }}</textarea>
                            <input id="economic_activity_code" name="economic_activity_code" type="hidden" value="{{$economicActivity->code ?? ''}}">
                            <input id="economic_activity_name" name="economic_activity_name" type="hidden" value="{{$economicActivity->activity_name ?? ''}}">
                        </div>

                    </div>
                </div>

                {{--DATOS DEL TOMADOR--}}
                <div class="title" id="title_data_tomador"><i class="dropdown icon"></i> Datos del contacto (reporte de
                    accidente o enfermedad)
                </div>
                <div class="content" id="content_data_tomador">
                    <div class="four fields">
                        <div class="required field">
                            <label for="nameResponsible">Nombre</label>
                            <div class="ui input">
                                <input name="nameResponsible" id="nameResponsible" autocomplete="off" type="text"
                                       value="{{$affiliate->name_responsible ?: ''}}" readonly>
                            </div>
                        </div>

                        <div class="required field">
                            <label>Tipo de identificación</label>
                            <div class="ui selection dropdown" id="heirDocTypeResponsibleDropdown">
                                <input type="hidden" name="doc_type_responsible" id="docTypeResponsible"
                                       value="{{ $affiliate->doc_type_responsible }}" readonly>
                                <i class="dropdown icon"></i>
                                <div class="default text">Seleccionar tipo documento</div>
                                <div class="menu">
                                    @foreach($DOC_TYPES as $k => $v)
                                        <div class="item" data-value="{{$k}}"
                                             @if($k == $affiliate->doc_type_responsible) class="active selected" @endif>
                                            {{$v}}
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        </div>

                        <div class="required field">
                            <label for="numberIdentifyResponsible">Número de Identificación</label>
                            <div class="ui input">
                                <input name="numberIdentifyResponsible" id="numberIdentifyResponsible"
                                       autocomplete="off" type="text"
                                       value="{{ $affiliate->doc_number_responsible ?: ''}}" readonly>
                            </div>
                        </div>
                        <div class="required field">
                            <label for="occupationResponsible">Ocupación</label>
                            <div class="ui input">
                                <input name="occupationResponsible" id="occupationResponsible" autocomplete="off"
                                       type="text"
                                       value="{{$affiliate->occupation_responsible ?: ''}}" readonly>
                            </div>
                        </div>
                    </div>

                    <div class="three fields">
                        <div class="required field">
                            <label for="phoneResponsible">Teléfono Domicilio</label>
                            <div class="ui input">
                                <input name="phoneResponsible" id="phoneResponsible" autocomplete="off" type="text"
                                       value="{{$affiliate->phone_responsible ?: ''}}" readonly>
                            </div>
                        </div>
                        <div class="required field">
                            <label for="cellphoneResponsible">Celular</label>
                            <div class="ui input">
                                <input name="cellphoneResponsible" id="cellphoneResponsible" autocomplete="off"
                                       type="text"
                                       value="{{$affiliate->cellphone_responsible ?: ''}}" readonly>
                            </div>
                        </div>

                        <div class="required field">
                            <label for="emailResponsible">Email</label>
                            <div class="ui input">
                                <input name="emailResponsible" onchange="validateDataEmail(event, 'policyHolderEmail')"
                                       id="emailResponsible" autocomplete="off" type="text"
                                       value="{{$affiliate->email_responsible}}" readonly>
                            </div>
                            {{-- <div id="invalidEmail2" class="ui red message" style="display: none;">
                                ¡El email ingresado no es valido!
                            </div> --}}
                        </div>
                    </div>
                </div>


                {{--DATOS POLIZA--}}
                <div class="title"><i class="dropdown icon"></i> Datos de la póliza</div>
                <div class="content">
                    <div class="three fields">
                        <div class="required field heir_payment-class" id="heir_doc_type">
                            <div class="ui form">
                                <div class="required field">
                                    <label>Temporalidad</label>
                                </div>
                                <div class="inline fields" style="pointer-events: none">
                                    <div class="field">
                                        <div class="ui radio checkbox">
                                            <input type="radio" name="temporality" value="permanent" tabindex="0"

                                                   @if($quotation->temporality == 'permanent') checked @endif>
                                            <label>Permanente</label>
                                        </div>
                                    </div>
                                    <div class="field">
                                        <div class="ui radio checkbox">
                                            <input type="radio" name="temporality" value="short" tabindex="1"
                                                   @if($quotation->temporality == 'short') checked @endif>
                                            <label>Periodo Corto</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="required field">
                            <label for="validity_from">Vigencia de la póliza desde</label>
                            <input id="validity_from" type="text"
                                   value=" {{ $quotation->validity_from ? \Carbon\Carbon::parse($quotation->validity_from)->format('d/m/Y') : '' }}"
                                   placeholder="dd/mm/yyyy" readonly>
                        </div>

                        <div class="required field">
                            <label for="validity_to">Vigencia de la póliza hasta</label>
                            <input id="validity_to" type="text" placeholder="dd/mm/yyyy"
                                   value="{{ $quotation->validity_to ? \Carbon\Carbon::parse($quotation->validity_to)->format('d/m/Y') : '' }}"
                                   readonly>
                        </div>
                    </div>
                    <div class="three fields">
                        <div class="required field">
                            <label for="moneytype">Tipo de moneda</label>
                            <div class="ui selection dropdown" style="pointer-events: none; background-color: #f3f4f5;"
                                 id="typeCurrencyDropdown">
                                <input type="hidden" name="type_currency" class="minus_font" id="type_currency"
                                       value="{{$quotation->type_currency}}" readonly>
                                <i class="dropdown icon"></i>
                                <div class="default text ">Seleccione una opción</div>
                                <div class="menu ">
                                    @foreach($MONEY_TYPE as $k => $currency)
                                        <div class="item " data-value="{{ $k }}">
                                            {{ $currency['symbol'] }} {{ mb_convert_case($currency['name'], MB_CASE_TITLE, 'UTF-8') }}

                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        </div>

                        @if($quotation->work_modality_id != 3 & $quotation->salary_projection > 0)
                            <div class="required field">
                                <label for="salary_projection">Planilla mensual</label>
                                <input class="prompt" id="salary_projection" type="text" name="salary_projection" style="text-align: right;"
                                       value="{{ ($quotation->type_currency === 'USD' ? '$' : '₡').number_format($quotation->salary_projection , 2, ',', '.') }}" readonly>
                            </div>
                        @endif
                    </div>
                </div>
                {{--CALCULO PRIMA--}}
                <div class="title"><i class="dropdown icon"></i> Cálculo de la prima</div>
                <div class="content">
                    @if($quotation->temporality == 'permanent')
                        <div class="ui four fields">
                            <div class="field">
                                <label for="anual">
                                    Anual
                                </label>
                                <input readonly id="anual" type="text"
                                       value="{{number_format($quotation->annual_calculation_amount, 2 , ',', '.')}}">
                            </div>
                            @if($quotation->semiannual_calculation_amount  != 0)
                                <div class="field">
                                    <label for="semestral">
                                        Semestral
                                    </label>
                                    <input readonly id="semestral" type="text"
                                           value="{{number_format($quotation->semiannual_calculation_amount, 2 , ',', '.')}}">
                                </div>
                            @endif
                            @if($quotation->quarterly_calculation_amount <> 0)
                                <div class="field">
                                    <label for="trimestral">
                                        Trimestral
                                    </label>
                                    <input readonly id="trimestral" type="text"
                                           value="{{number_format($quotation->quarterly_calculation_amount, 2 , ',', '.')}}">
                                </div>
                              
                            @endif
                        </div>
                    @endif
                    @if($quotation->temporality == 'short')
                        <div class="fields">
                            <div class="field">
                                <label for="unico">
                                    Pago único
                                </label>
                                <input readonly type="text" id="unico"
                                       value="{{number_format($quotation->single_payment_value, 2 , ',', '.')}}">
                            </div>
                        </div>
                    @endif
                </div>
            </div>
            <div class="ui basic segment">
                <div class="ui error message"></div>
                <div class="fields"> <!-- Distribución equitativa -->
                    <!-- Botón de Volver -->
                    <a href="{{ secure_url('/intermediario/cotizaciones') }}" class="ui primary small button">
                        <i class="arrow left icon"></i> Volver a la lista de cotizaciones
                    </a>

                    <!-- Botón de Descargar PDF -->
                    @if($activity->id)
                        <button class="ui secondary small button"
                           style="margin-left: 1rem;"
                        id="generatePDF">
                            <i class="file pdf outline icon"></i> Descargar PDF
                        </button>
                    @endif
                </div>
            </div>

            {{csrf_field()}}
        </form>
    </div>


    <style type="text/css">
        .ui.grid .column {
            padding: 0.5rem 1rem !important;
        }

        .sender .field {
            display: none;
        }

        .ear.field {
            display: none;
        }

        .msp.field {
            display: none;
        }

        .ui.accordion .title {
            text-transform: uppercase;
        }

        .field > h3 {
            text-align: center;
            margin-top: 1.25rem;
        }

        .ui.search > .results {
            width: 30rem;
        }

        .ui.search > .results .result .title {
            padding: 0 !important;
            border: none !important;
            text-transform: none;
        }

        .ui.search > .results .result .content {
            padding: 0 !important;
        }

        th {
            text-align: center !important;
        }

        .accordion-spacing {
            margin-bottom: 20px;
        }

        .minus_font {
            text-transform: lowercase !important;
        }

        input[readonly], textarea[readonly] {
            background-color: #f3f4f5 !important;
        }
    </style>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/locale/es.min.js"></script>

    <script type="text/javascript">
        const activityId = @json($activity->id);

        document.getElementById('generatePDF').addEventListener('click', function (e) {
            e.preventDefault();

            loadingMain(true);

            fetch(`/servicio/${activityId}/quotation/generatePdf`, {
                method: 'GET',
            })
                .then((response) => {
                    if (!response.ok) {
                        throw new Error('Error al descargar el archivo');
                    }
                    return response.blob(); // Convertir la respuesta a un Blob
                })
                .then((blob) => {
                    const url = window.URL.createObjectURL(blob);
                    const link = document.createElement('a');
                    link.href = url;
                    link.download = `Cotización_#_${activityId}.pdf`;
                    document.body.appendChild(link);
                    link.click(); // Simular el clic
                    link.remove();
                    window.URL.revokeObjectURL(url); // Liberar la URL
                })
                .catch((error) => {
                    console.error('Error durante la descarga:', error);
                    alert('Hubo un error al descargar el archivo.');
                })
                .finally(() => {
                    loadingMain(false); // Ocultar el loading
                });
        });

        //Generamos la acción solicitar condiciones especiales
        /*document.getElementById('generateAction').addEventListener('click', function (e) {
            e.preventDefault();

            loadingMain(true);

            fetch(`/servicio/${activityId}/quotation/generateActionCondition`, {
                method: 'GET',
            })
                .then((response) => {
                    Swal.fire({
                            imageUrl: '/file/client_logo/logo_mnk.png',
                            imageHeight: 50,
                            imageWidth: 150,

                            text: "¡Esta solicitud se envió a MNK!",
                            confirmButtonText: "Aceptar"
                        });
                        loadingMain(false);
                })

                .catch((error) => {
                    loadingMain(false);

                    console.error('Error durante la descarga:', error);
                    alert('Hubo un error al procesar la acción.');
                })
                .finally(() => {
                    loadingMain(false); // Ocultar el loading
                });
        });*/

        const quotation = @json($quotation);
        const currencyTypes = @json($MONEY_TYPE);
        configureInputMask('#mensual', {symbol: quotation.type_currency ? currencyTypes[quotation.type_currency]['symbol'] : ''});
        configureInputMask('#semestral', {symbol: quotation.type_currency ? currencyTypes[quotation.type_currency]['symbol'] : ''});
        configureInputMask('#trimestral', {symbol: quotation.type_currency ? currencyTypes[quotation.type_currency]['symbol'] : ''});
        configureInputMask('#anual', {symbol: quotation.type_currency ? currencyTypes[quotation.type_currency]['symbol'] : ''});
        configureInputMask('#unico', {symbol: quotation.type_currency ? currencyTypes[quotation.type_currency]['symbol'] : ''});


        var generatePDF = function () {
            $('form#dictamen').attr('target', '_blank');
            $('form#dictamen').attr('action', '{{secure_url('/servicio/' . $activity->id . '/quotation/pdf')}}');
            $('form#dictamen').submit();
            $('form#dictamen').removeAttr('target');
            $('form#dictamen').attr('action', '{{secure_url('/servicio/' . $activity->id . '/quotation/save')}}');
            return false;
        };

        var toggleSender = function () {
            console.log('sender');
            if ($('[name=sender]').val() == 5 || $('[name=sender]').val() == 8) {
                $('.sender .field').show();
            } else {
                $('.sender .field').hide();
            }
        };

        var cie10 = [];


        $(document).ready(function () {
            $('.ui.accordion .ui.grid .row').css('padding-top', 0);
            $('.ui.accordion .ui.grid .column').css('padding-top', 0);

            $('.ui.accordion').accordion({
                exclusive: false
            });
            $('.ui.accordion .content').addClass('active');
            $('.ui.accordion .title').addClass('active');
            $('form#dictamen a.red').click(function () {
                $(this).parent().parent().remove();
            });
            $('form .ui.dropdown').dropdown({
                forceSelection: false
            });
            $('form .datepicker').pickadate({
                selectYears: 100,
                selectMonths: true,
                max: new Date(),
                formatSubmit: 'yyyy-mm-dd',
                format: 'mmm dd, yyyy'
            });

            $('form input').keydown(function (event) {
                if (event.keyCode == 13) {
                    event.preventDefault();
                    return false;
                }
            });

            /**
             * Inicio de bloque de código que permite traer lista de actividad economica *
             **/
            initializeSearchOnPageLoad();
            const jsonSource = '';

            // ** Validaciones para el cambio de sector **
            $('input[name="sector"]').change(function () {
                $('input[name="diagnostics[cod][]"]').val('');
                $('input[name="diagnostics[description][]"]').val('');

                /* Condición que permite validar el tipo de sector */
                initializeSearchOnPageLoad();
                initializeSearch(jsonSource);
            });

            // ** Función que permite limbiar información de los campos cuando se borra el contenido del campo "Nombre" **
            $('input[name="diagnostics[cod][]"]').on('input', function () {
                if ($(this).val() === '') {
                    $('input[name="diagnostics[description][]"]').val('');
                }
            });

            initializeSearch(jsonSource);
            /**
             * Fin de bloque de código que permite traer lista de actividad economica *
             **/
        });

        /**
         * Fin de bloque de código que permite guardar los datos del tomador *
         **/
        /**
         * Inicio de bloque de código que permite obtener el json si tenia sector en la cotizacion *
         **/
        function initializeSearchOnPageLoad() {
            // Obtener el valor del sector seleccionado en la carga de la página
            const selectedSectorValue = $('input[name="sector"]:checked').val();
            if (selectedSectorValue) {
                // Si hay un sector seleccionado, cargar el JSON
                if (selectedSectorValue === 'public') {
                    jsonSource = '/js/economic_activity/public.json?v=1.0';
                } else if (selectedSectorValue === 'private') {
                    jsonSource = '/js/economic_activity/private.json?v=1.0';
                } else {
                    jsonSource = '';
                }
                initializeSearch(jsonSource)
            }
        }

        /**
         * Fin de bloque de código que permite obtener el json si tenia sector en la cotizacion *
         **/
        /**
         * Inicio Función para inicializar la búsqueda de actividades económicas.
         *La ruta del archivo JSON que contiene los datos de las actividades económicas. *
         **/
        function initializeSearch(source) {
            if (!source) return; // * No cargar si la ruta del json está vacía *
            // * Consulta de JSON correspondiente a su ruta *
            $.getJSON(source, function (json) {
                // Normalizamos los datos, asegurando que el campo CODE sea siempre una cadena
                json = json.map(function (item) {
                    item.CODE = item.CODE.toString();
                    return item;
                });
                const code = $('#economic_activity_code');
                const name = $('#economic_activity_name');
                name.val(json.find(x => x.CODE === code.val()).ACTIVITY_NAME);
                const search = $('.ui.search.code');
                search.search('destroy');
                search.search({
                    source: json,
                    fields: {
                        title: 'CODE',
                        description: 'ACTIVITY_NAME'
                    },
                    searchFields: ['CODE', 'ACTIVITY_NAME'],
                    regExp: {
                        escape: /[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,
                        beginsWith: ''
                    },
                    showNoResults: true,
                    maxResults: 20,
                    cache: false,
                    searchFullText: false,
                    error: {
                        noResults: 'No se encontraron resultados para tu búsqueda.'
                    },
                    onSelect: function (result) {
                        $('#economic_activity_code').val(result.CODE);
                        $('input[name="diagnostics[description][]"]').val(result.ACTIVITY_NAME);
                    }
                });
            });
        }

        function validateDataEmail(event) {
            event?.preventDefault();

            const email = $('#emailResponsible').val();
            var emailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

            if (!emailPattern.test(email)) {

                Swal.fire({
                    icon: 'warning',
                    title: 'Un momento',
                    html: 'El email no es valido',
                });
                return;
            }
        }

        var hidden_contacto = {{ $hidden_contacto ?? 'false' }};

        if (hidden_contacto) {
            document.getElementById('content_data_tomador').style.display = 'none';
            document.getElementById('title_data_tomador').style.display = 'none';
        }

        $(document).ready(function () {
            // Establecemos el locale en español
            moment.locale('es');

            // Seleccionamos el input por su id 'validity_from'
            var fechaCreada = $('#validity_from').val();
            var fechaTo = $('#validity_to').val();

            // Si la fecha es válida, formateamos la fecha
            if (fechaCreada) {
                var fechaFormateada = moment(fechaCreada, 'DD/MM/YYYY').format('dddd D [de] MMMM [de] YYYY'); // Formato esperado

                // Capitalizamos la primera letra del día
                fechaFormateada = fechaFormateada.charAt(0).toUpperCase() + fechaFormateada.slice(1);

                // Establecemos la fecha formateada en el valor del input
                $('#validity_from').val(fechaFormateada);
            }

            if (fechaTo) {
                var fechaFormateada = moment(fechaTo, 'DD/MM/YYYY').format('dddd D [de] MMMM [de] YYYY'); // Formato esperado

                // Capitalizamos la primera letra del día
                fechaFormateada = fechaFormateada.charAt(0).toUpperCase() + fechaFormateada.slice(1);

                // Establecemos la fecha formateada en el valor del input
                $('#validity_to').val(fechaFormateada);
            }
        });

    </script>

    <style>
        .title {
            text-transform: none !important;
        }

        input[type="text"] {
            text-transform: none !important;
        }
    </style>
@endsection