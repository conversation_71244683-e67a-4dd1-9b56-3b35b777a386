<div class="ui basic segment">
    <div class="ui container">
        <a href="{{ secure_url('/intermediario/cotizaciones') }}" class="ui secondary small button no-submit-link">
            <i class="arrow left icon"></i> Volver a la lista de cotizaciones
        </a>
        <div class="ui four item stackable tabs menu">
            <a class="item {{ request()->is('intermediario/cotizacion/datos/*/intermediary') ? 'active' : '' }} menu_item"
               href="{{ url('/intermediario/cotizacion/datos/' . $id . '/intermediary') }}">Datos del intermediario</a>
            <a class="item {{ request()->is('intermediario/cotizacion/datos/*/policy_holder') ? 'active' : '' }} menu_item"
               href="{{ url('/intermediario/cotizacion/datos/' . $id . '/policy_holder') }}">Datos del tomador</a>
            <a class="item {{ request()->is('intermediario/cotizacion/datos/*/quote_details') ? 'active' : '' }} menu_item"
               href="{{ url('/intermediario/cotizacion/datos/' . $id . '/quote_details') }}">Datos de la cotización</a>
            <a class="item {{ request()->is('intermediario/cotizacion/datos/*/policy_calculation') ? 'active' : '' }} menu_item"
               href="{{ url('/intermediario/cotizacion/datos/' . $id . '/policy_calculation') }}">Cálculo de la
                póliza</a>
{{--            <a class="item disabled" onclick="return false;" style="cursor: not-allowed; color: gray;">--}}
{{--                Validación alto riesgo--}}
{{--            </a>--}}
        </div>
    </div>
</div>
<script type="text/javascript">
    $(document).ready(function() {
        $('.no-submit-link').on('click', function(event) {
            event.preventDefault();
            window.location.href = $(this).attr('href');
        });
    });
</script>
