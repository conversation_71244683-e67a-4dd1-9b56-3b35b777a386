@extends('layouts.main')

@section('title', 'Datos de Cotización')

@section('menu')
    @parent
@endsection

@section('content')
    @include('services.quotation.menu.agent-data')
    <div class="ui basic segment container" id="policyholder">
        <div class="ui styled fluid accordion">
            <div class="active title">
                <i class="dropdown icon"></i>
                Identificación del tomador <span style="color: red;" class="required">*</span>
            </div>
            <div class="active content">
                <form class="ui form small clearing">
                    <div class="two fields">
                        <div class="required field">
                            <label>Tipo de identificación</label>
                            <div class="ui search selection dropdown" id="heirDocTypeDropdown">
                                <input type="hidden" name="doc_type" class="" id="docType"
                                    value="{{ old('doc_type', $affiliate->doc_type ?? '') }}">
                                <i class="dropdown icon"></i>
                                <div class="default text heirDocTypeDropdown">Tipo de identificación</div>
                                <div class="menu ">
                                    @foreach ($DOC_TYPES as $k => $v)
                                        <div class="item heirDocTypeDropdown" data-value="{{ $k }}">
                                            {{ $v }}</div>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                        <div class="required field">
                            <label for="docNumber">Número de documento</label>
                            <div class="ui left icon input">
                                <i class="user icon"></i>
                                <input name="doc_number" id="docNumber" autocomplete="off" type="text"  oninput="this.value = this.value.replace(/[^0-9]/g, '');"
                                    value="{{ old('doc_number', $affiliate->doc_number ?? '') }}">
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="active title">
                <i class="dropdown icon"></i>
                Datos del tomador <span style="color: red;" class="required">*</span>
            </div>
            <div class="active content">
                <form class="ui form small clearing">
                    <div class="four fields">
                        <div class="required field">
                            <label>Nombre</label>
                            <div class="ui left icon input">
                                <i class="user icon"></i>
                                <input type="text" name="name" class="minus" id="nameInput" placeholder="Nombre"
                                    value="{{ ucwords(strtolower(old('name', $affiliate->first_name ?? ''))) }}">
                            </div>
                        </div>
                        <div class="required field">
                            <label>Teléfonos</label>
                            <div class="ui left icon input">
                                <i class="phone icon"></i>
                                <input type="text" name="phones" id="phonesInput" placeholder="Teléfonos"
                                    value="{{ old('phones', $affiliate->phone ?? '') }}">
                            </div>
                        </div>
                        <div class="required field">
                            <label>Correo electrónico de notificaciones</label>
                            <div class="ui left icon input">
                                <i class="envelope icon"></i>
                                <input type="email" name="email" class="minus" id="validateEmail"
                                    onchange="validateDataEmail(event)" placeholder="Correo electrónico de notificaciones"
                                    value="{{ old('email', $affiliate->email ?? '') }}">
                            </div>
                        </div>
                        <div class="required field">
                            <label for="workRiskDropdown">Modalidad aseguramiento</label>
                            <div class="ui selection dropdown" id="workRiskDropdown">
                                <input type="hidden" name="workRisk" value="{{ $quotation->work_modality_id }}"
                                    id="workRisk">
                                <i class="dropdown icon"></i>
                                <div class="default text">Seleccionar riesgo de trabajo</div>
                                <div class="menu dropdown-scroll">
                                    @foreach ($WORK_MODALITY as $k => $modality)
                                        <div class="item" data-value="{{ $k }}">
                                            {{ ucfirst(strtolower($modality)) }}
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="four fields">
                        <div class="field">
                            <label>Sector</label>
                            <div class="ui radio checkbox checkbox-spacing disabled" style="margin-right: 70px;">
                                <input readonly type="radio" name="sector" class="minus disabled" value="public"
                                    {{ old('sector', $quotation->economic_activity ?? '') == 'public' ? 'checked' : '' }}>
                                <label>Público</label>
                            </div>
                            <div class="ui radio checkbox checkbox-spacing disabled" style="margin-right: 0px;">
                                <input readonly type="radio" name="sector" class="minus disabled" value="private"
                                    {{ old('sector', $quotation->economic_activity ?? 'private') == 'private' ? 'checked' : '' }}>
                                <label>Privado</label>
                            </div>
                        </div>

                        <div class="required field" id="div_economic_branch">
                            <label>Rama general de actividad económica</label>
                            <div class="ui search selection dropdown" id="economicBranchDropdown">
                                <input type="hidden" name="economic_branch" class="" id="economicBranch" value="">
                                <i class="dropdown icon"></i>
                                <div class="default text heirDocTypeDropdownDos">Rama general de actividad económica</div>
                                <div class="menu ">
                                    @foreach ($economicBranch as $row)
                                        <div class="item heirDocTypeDropdownDos" data-value="{{ $row->id }}">{{ $row->branch_name ?? '' }}</div>
                                    @endforeach
                                </div>
                            </div>
                        </div>

                        <div class="required field">
                            <label>Actividad económica especifica</label>
                            <div class="ui search selection dropdown" id="economicActivityDropdown">
                                <input type="hidden" name="economic_activity_code" class="" id="economic_activity_code" value="{{ $quotation->activity_economic_id ?? ''  }}">
                                <i class="dropdown icon"></i>
                                <div class="default text">Actividad económica especifica</div>
                                <div class="menu ">
                                    <div class="item" data-value=""> </div>
                                </div>
                            </div>
                        </div>

                        <div class="field" id="div_economic_activity_percentage">
                            <label>Tarifa</label>
                            <input id="economic_activity_percentage" class="prompt minus"
                                   value="{{ $quotation->economic_activity_percentage ?? '' }}" readonly>
                        </div>

                        <div class="required field" id="div_optionAsegurement" style="display: none">
                            <label for="optionAsegurement">Opciones de aseguramiento</label>
                            <div class="ui selection dropdown">
                                <input type="hidden" name="optionAsegurement" value="{{ $quotation->option_asegurement }}" id="optionAsegurement">
                                <i class="dropdown icon"></i>
                                <div class="default text">Selecciona una opcion de aseguramiento</div>
                                <div class="menu">
                                    @foreach ($OPTION_ASEGUREMENT as $k => $option)
                                        <div class="item" data-value="{{ $k }}">
                                            {{ ucfirst(strtolower($option)) }}
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        </div>

                    </div>
                    <div class="four fields">

                        <div class="required field" id="div_institutional_sector">
                            <label>Sector institucional</label>
                            <div class="ui search selection dropdown" id="institutionalSectorDropdown">
                                <input type="hidden" name="institutional_sector" id="institutional_sector" value="{{ $quotation->institutional_sector }}">
                                <i class="dropdown icon"></i>
                                <div class="default text">Sector institucional</div>
                                <div class="menu ">
                                    @foreach ($INSTITUTIONAL_SECTOR as $k => $option)
                                        <div class="item" data-value="{{ $k }}">
                                            {{ ucfirst(strtolower($option)) }}
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                    </div>

                </form>
                <br>
                @if(empty($quotation) || ($quotation->activity->state_id == 1 || $quotation->activity->state_id == 6))
                    <div class="row" style="padding: 0 10px;">
                        <div style="display: flex; justify-content: space-between;">

                            <a class="ui button primary"
                                href="{{ url('/intermediario/cotizacion/datos/' . $id . '/intermediary') }}">Atrás</a>

                            <button class="ui button primary" onclick="validateAndSave(event,'siguiente')">Siguiente
                            </button>
                        </div>
                    </div>
                @else
                    <div class="aler alert-danger">
                        La cotización no se puede editar porque se encuentra en el estado {{ $quotation->activity->state->name }}
                    </div>
                @endif

            </div>
        </div>
    </div>
    <style>
        input[type="text"] {
            text-transform: none !important;
        }

        .checkbox-spacing {
            margin-right: 20px;
        }

        .ui.search.code .results {
            max-height: 200px;
            /* Ajusta esta altura según tus necesidades */
            overflow-y: auto;
            border: 1px solid #ddd;
            /* Opcional: Agrega un borde para mejor visibilidad */
            padding: 5px;
            /* Opcional: Espaciado interno */
            background: #fff;
            /* Opcional: Color de fondo */
        }

        .ui.search>.results {
            width: 100% !important;
        }
        .ui.dropdown .menu.dropdown-scroll {
            max-height: 150px; /* Ajusta según la altura deseada */
            overflow-y: auto; /* Habilita el desplazamiento vertical */
            border: 1px solid #ddd; /* Opcional */
            padding: 5px; /* Opcional */
            background: #fff; /* Opcional */
        }
    </style>

    <script>

        $(document).ready(function () {
            let quotation = @json($quotation);
            
            if (quotation && quotation.activity.state_id !== 1 && quotation.activity.state_id !== 6) {
                Swal.fire({
                    icon: 'error',
                    title: 'No se puede editar la cotización',
                    text: 'La cotización se encuentra en el estado ' + quotation.activity.state.name,
                    showCancelButton: true,
                    confirmButtonText: 'Crear nueva cotización',
                    cancelButtonText: 'Salir',
                    confirmButtonColor: '#000000',
                    cancelButtonColor: '#d33'
                }).then((result) => {
                    if (result.isConfirmed) {
                        loadingMain(true)
                        $.ajax({
                            url: '/services/create/quotations',
                            type: 'POST',
                            success: function (response) {
                                $(window).on('beforeunload', function () {
                                    loadingMain(false)
                                });
                                window.location.href = '/intermediario/cotizacion/datos/' + response.id +
                                    '/intermediary';
                            },
                            error: function () {
                                loadingMain(false)
                                showMessage(
                                    'Ocurrió un error al crear la cotización, por favor intente de nuevo');
                            },
                        })
                    } else {
                        window.location.href = "{{ secure_url('/intermediario/cotizaciones') }}";
                    }
                });
            }
        });

        /**
         * Este bloque de código utiliza jQuery para manejar la lógica de mostrar u ocultar
         * una opción específica en un menú desplegable basado en el tipo de documento seleccionado.
         * 
         * - La función `toggleWorkRiskOption(docType)` controla la visibilidad de la opción
         *   "riesgo independiente" en el menú desplegable de riesgos laborales dependiendo
         *   del tipo de documento (`docType`).
         * - Si el tipo de documento es "CJ" (Cédula Jurídica), oculta la opción y deselecciona
         *   cualquier valor previamente seleccionado si corresponde a "riesgo independiente".
         * - Si el tipo de documento es diferente, muestra la opción nuevamente.
         * - Al cargar la página, se inicializa la lógica con el valor actual del tipo de documento.
         * - También se maneja el evento de clic en los elementos del menú desplegable para actualizar
         *   el tipo de documento seleccionado y aplicar la lógica correspondiente.
         */
        $(document).ready(function () {
            function toggleWorkRiskOption(docType) {
                const workRiskDropdown = $('#workRiskDropdown .menu');
                const independentRiskItem = workRiskDropdown.find('.item[data-value="6"]');

                if (docType === 'CJ') {
                    // Ocultar si es Cédula Jurídica
                    independentRiskItem.hide();
                    // Si está seleccionado, quitar la selección
                    if ($('#workRisk').val() === '6') {
                        $('#workRisk').val('');
                        $('#workRiskDropdown .text').text('Seleccionar riesgo de trabajo');
                    }
                } else {
                    // Mostrar si es otro tipo
                    independentRiskItem.show();
                }
            }

            // Inicializar al cargar la página
            toggleWorkRiskOption($('#docType').val());

            // Manejar cambios en la selección
            $('#heirDocTypeDropdown .item').on('click', function () {
                const selectedValue = $(this).data('value');
                $('#docType').val(selectedValue);
                $('#heirDocTypeDropdown .text').text($(this).text());
                toggleWorkRiskOption(selectedValue);
            });
        });

        document.getElementById("workRiskDropdown").addEventListener("change", function() {
            const selectedValue = document.getElementById("workRisk").value;

            $('#economic_activity_code').val("");
            $('#economic_activity_percentage').val("");
            $('#economicBranch').val("");
            $('#economicBranchDropdown').dropdown('clear');

            $('#institutionalSectorDropdown').dropdown('refresh');

            validaCheck(selectedValue);


            if (selectedValue == 3) { //riesgos del trabajo del hogar

                $('#div_economic_activity_percentage').hide();
                $('#div_optionAsegurement').show();
                $('#economicBranchDropdown').addClass('disabled');

            }else{

                $('#economicBranchDropdown').removeClass('disabled');
                $('#div_economic_activity_code').removeClass('disabled');
                $('#div_economic_activity_percentage').show();
                $('#div_optionAsegurement').hide();
            }

            initializeSearchOnPageLoad();

        });

        validaCheck(document.getElementById("workRisk").value);

        function validaCheck(selectedValue){
            if (selectedValue == 5) {
                $('input[name="sector"][value="public"]').prop('checked', true);
                $('#div_economic_branch').hide();

                $('#institutional_sector').val('');
                $('#institutionalSectorDropdown').removeClass('disabled');
                $('#institutionalSectorDropdown').dropdown('clear');

                // Ocultar el item 20
                $('#institutionalSectorDropdown .menu .item[data-value="20"]').hide();

                $('#institutional_sector').val('');
                $('#institutionalSectorDropdown').dropdown('set selected', '');

            } else {
                $('input[name="sector"][value="private"]').prop('checked', true);
                $('#div_economic_branch').show();

                // Mostrar el item 20
                $('#institutionalSectorDropdown .menu .item[data-value="20"]').show();


                $('#institutional_sector').val(20);
                $('#institutionalSectorDropdown').dropdown('set selected', 20);
                $('#institutionalSectorDropdown').addClass('disabled');

            }
        }

        document.getElementById("economicBranchDropdown").addEventListener("change", function() {

            $('#economicActivityDropdown').dropdown('clear');
            $('#economic_activity_code').val("");
            $('#economic_activity_percentage').val("");

            initializeSearchOnPageLoad();
        });

    </script>
    <script type="text/javascript">

        let economicActivitiesData = [];

        $(document).ready(function() {
            
            var work_modality_id = @json($quotation->work_modality_id);
            var activity_economic_id = @json($quotation->activity_economic_id);

            //Si el modalidad de aseguramiento hogar
            if(work_modality_id == 3){

                $('#div_economic_activity_code').addClass('disabled');
                $('#div_economic_activity_percentage').hide();
                $('#div_optionAsegurement').show();
            }

            $('.ui.dropdown').dropdown();

            $('.ui.accordion').accordion({
                exclusive: false
            });

            $('.ui.radio.checkbox').checkbox();

            $('#selectDropdown').dropdown({
                onChange: function(value, text, $choice) {
                    $('#resultInput').val(text);
                }
            });


            //Funcion para llamar el json si ya tenia previamente seleccionado el sector
            initializeSearchOnPageLoad(true);

            /**
             * Inicio de bloque de código que permite traer información a partir del número de documento *
             **/
            function fetchDocumentData() {
                const docType = $('#docType').val();
                const docNumber = $('#docNumber').val();

                if (docType && docNumber) {
                    loadingMain(true);
                    $.ajax({
                        url: '/web-service-credid/data',
                        method: 'GET',
                        data: {
                            numero_id: docNumber,
                            doc_type: docType
                        },
                        success: function(response) {
                            $('#nameInput').val(capitalizeFirstLetterName(response.policyHolderName));
                            $('#phonesInput').val(response.policyHolderPhone);
                            $('#validateEmail').val(response.policyHolderEmail);

                        },
                        error: function() {
                            $('#nameInput').val('');
                            $('#phonesInput').val('');
                            $('#validateEmail').val('');
                            console.log('Error al obtener los datos');
                        },
                        complete: function() {
                            // Ocultar indicador de carga
                            loadingMain(false);
                        }
                    });
                }
            }

            // Manejar el evento 'change' en el tipo de documento
            $('#docType').on('change', function() {
                fetchDocumentData();
            });

            // Manejar el evento 'blur' en el número de documento
            $('#docNumber').on('blur', function() {
                fetchDocumentData();
            });
            /**
             * Fin de bloque de código que permite traer información a partir del número de documento *
             **/

            /**
             * Inicio de bloque de código que permite traer datos a partir del tipo de documento selecionado *
             * Aplica solo para cédula jurídica o cédula física
             **/
            $('#heirDocTypeDropdown').dropdown({
                onChange: function(value, text, $choice) {
                    if (value === 'CF' || value === 'CJ') {
                        $.ajax({
                            url: '/web-service-credid/data',
                            method: 'GET',
                            data: {
                                numero_id: $('#docNumber').val(),
                                doc_type: value
                            },
                            success: function(response) {
                                $('#nameInput').val(response.policyHolderName);
                                $('#phonesInput').val(response.policyHolderPhone);

                            },
                            error: function() {
                                console.error('Error al obtener los datos');
                            }
                        });
                    }
                }
            });
            /**
             * Fin de bloque de código obtner información *
             **/

            /**

             * Para la validación del email, usar función que se encuentra en main.blade *
             **/
            validateEmailField("validateEmail", "invalidEmail")
            /**
             * Inicio de bloque de código que permite traer lista de actividad economica *
             **/
            const jsonSource = '';

            // ** Validaciones para el cambio de sector **
            $('input[name="sector"]').change(function() {
                $('input[name="diagnostics[cod][]"]').val('');
                $('input[name="diagnostics[description][]"]').val('');
                $('#economic_activity_percentage').val('');

                /* Condición que permite validar el tipo de sector */
                initializeSearchOnPageLoad();
                initializeSearch(jsonSource);
            });

            // ** Función que permite limbiar información de los campos cuando se borra el contenido del campo "Nombre" **
            $('input[name="diagnostics[cod][]"]').on('input', function() {
                if ($(this).val() === '') {
                    $('input[name="diagnostics[description][]"]').val('');
                    $('#economic_activity_percentage').val('');
                }
            });

            initializeSearch(jsonSource);
            /**
             * Fin de bloque de código que permite traer lista de actividad economica *
             **/
        });

        /**
         * Inicio de bloque de código que permite guardar los datos del tomador *
         **/

        // Función para validar el correo electrónico
        function validateEmail(email) {
            const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailPattern.test(email)) {
                Swal.fire({
                    title: 'Importante',
                    html: 'El correo electrónico no es válido',
                    icon: 'warning',
                    showConfirmButton: false,
                    showCancelButton: true,
                    cancelButtonText: 'Cerrar',
                    cancelButtonColor: '#d33'
                });
                return false;
            }
            return true;
        }

        function validateAndSave(event) {
            event?.preventDefault();

            // Obtener valores de los campos
            const docType = $('#docType').val();
            const docNumber = $('#docNumber').val();
            const name = $('#nameInput').val();
            const phones = $('#phonesInput').val();
            const email = $('#validateEmail').val();
            const sector = $('input[name="sector"]:checked').val();
            const economicActivityCode = $('#economic_activity_code').val();
            //const economicActivityName = $('#economic_activity_name').val();
            const economicActivityPercentage = $('#economic_activity_percentage').val();
            const workRisk = $('#workRisk').val();
            const optionAsegurement = $('#optionAsegurement').val();

            const institutionalSector = $('#institutional_sector').val();
            const id = @json($id);

            // Verificar campos
            const missingFields = [];
            if (!docType) missingFields.push('Tipo de identificación');
            if (!docNumber) missingFields.push('Número de documento');
            if (!name) missingFields.push('Nombre');
            if (!phones) missingFields.push('Teléfonos');
            if (!email) missingFields.push('Email de Notificaciones');
            if (!sector) missingFields.push('Sector');
            if (!economicActivityCode) missingFields.push('Código de actividad económica');
            //if (!economicActivityName) missingFields.push('Nombre de la Actividad Económica');
            if (!workRisk) missingFields.push('Modalidad aseguramiento');
            if (!institutionalSector) missingFields.push('Sector institucional');

            if(workRisk == 3){
                if (!optionAsegurement) missingFields.push('Opciones de aseguramiento');
            }

            var emailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

            if (!emailPattern.test(email)) {

                Swal.fire({
                    icon: 'error',
                    title: 'Correo electrónico inválido',
                    html: 'Por favor, ingrese un nuevo correo.',
                    confirmButtonText: 'Aceptar',
                    confirmButtonColor: '#000000', // Color negro para el botón
                });
                return;
            }

            if (missingFields.length > 0 && event) {
                // Construir mensaje de error
                let errorMessage = '<div class="ui segment"><h4>Por favor, complete los siguientes campos:</h4><ul class="ui list">';
                missingFields.forEach(field => {
                    errorMessage += `<li>${field}</li>`;
                });
                errorMessage += '</ul></div>';

                Swal.fire({
                    title: 'Información incompleta',
                    html: errorMessage,
                    icon: 'warning',
                    showConfirmButton: false,
                    showCancelButton: true,
                    cancelButtonText: 'Aceptar',
                    cancelButtonColor: '#000000',


                });
                return;
            }

            if (docType && docNumber) {
                // Si todos los campos están llenos, hacer la petición AJAX
                loadingMain(true);
                $.ajax({
                    url: '/servicio/affiliatequotation',
                    method: 'POST',
                    data: {
                        id,
                        affiliate: {
                            doc_type: docType.toUpperCase(),
                            doc_number: docNumber.toUpperCase(),
                            first_name: name.toUpperCase(),
                            last_name: '', // Si no hay campo para el apellido, puedes dejarlo vacío o quitarlo
                            phone: phones.toUpperCase(),
                            email: email.toLowerCase(),
                            sector: sector,
                            economic_activity_code: economicActivityCode.toUpperCase(),
                            //economic_activity_name: economicActivityName.toUpperCase(),
                            economic_activity_percentage: economicActivityPercentage,
                            work_modality_id: workRisk,
                            option_asegurement : optionAsegurement
                        }
                    },
                    success: function(data) {
                        
                        if(data.success == false){
                            Swal.fire({
                                title: 'Error',
                                text: data.message,
                                icon: 'error',
                                showConfirmButton: false,
                                showCancelButton: true,
                                cancelButtonText: 'Cerrar',
                                cancelButtonColor: '#d33'
                            })
                            loadingMain(false);
                            return;
                        }

                        $(window).on('beforeunload', function() {
                            loadingMain(false)
                        });
                        if (event) {
                            // Redirigir a la vista de policy_holder después de que se complete la solicitud AJAX
                            window.location.href = '/intermediario/cotizacion/datos/' + id + '/quote_details';
                        }
                    },
                    error: function(error) {
                        // Mostrar mensaje de error
                        Swal.fire({
                            title: 'Error',
                            text: error.responseJSON.message,
                            icon: 'error',
                            showConfirmButton: false,
                            showCancelButton: true,
                            cancelButtonText: 'Cerrar',
                            cancelButtonColor: '#d33'
                        })
                        loadingMain(false)
                    },
                });
            }
        }

        /**
         * Fin de bloque de código que permite guardar los datos del tomador *
         **/
        /**
         * Inicio de bloque de código que permite obtener el json si tenia sector en la cotizacion *
         **/

        function initializeSearchOnPageLoad(inicial = false) {

            $('#economic_activity_code').val("");
            $('#economicActivityDropdown').dropdown('clear');

            const selectedSectorValue = $('input[name="sector"]:checked').val();
            const selectedEconomicBranch = document.getElementById("economicBranch")?.value || '';
            let selectedWorkRisk = document.getElementById("workRisk")?.value || '';


            if (selectedSectorValue) {
                loadingMain(true);
                $.ajax({
                    url: '/get/economic-activities',
                    method: 'GET',
                    data: {
                        sector: selectedSectorValue,
                        branch: selectedEconomicBranch,
                        work_risk: selectedWorkRisk
                    },
                    success: function(data) {
                        // Normaliza CODE como string
                        // const normalized = data.map(function(item) {
                        //     item.CODE = item.CODE.toString();
                        //     return item;
                        // });

                        const normalized = data.map(item => ({
                            CODE: item.CODE.toString(),
                            PERCENTAGE: item.PERCENTAGE,
                            branch_id: item.branch_id,
                            ACTIVITY_NAME: capitalizeFirstLetter(item.ACTIVITY_NAME),
                            SEARCH_TEXT: (item.CODE + ' ' + item.ACTIVITY_NAME).toLowerCase()
                        }));

                        loadingMain(false);
                        initializeSearch(normalized, inicial);

                    },
                    error: function() {
                        loadingMain(false);
                        console.error('Error al cargar actividades económicas desde el servidor');
                    }
                });
            }
        }

        /**
         * Fin de bloque de código que permite obtener el json si tenia sector en la cotizacion *
         **/

        /**
         * Inicio Función para inicializar la búsqueda de actividades económicas.
         *La ruta del archivo JSON que contiene los datos de las actividades económicas. *
         **/

        function initializeSearch(json, inicial) {

            if (!json) return;

            // Si source es un array (datos ya cargados)
            // if (Array.isArray(source)) {
            //     setupSearch(source, inicial);
            // } else {
            //     // Si source es URL
            //     $.getJSON(source, function(json) {
            //         setupSearch(json, inicial);
            //     });
            // }

            //function setupSearch(json) {
            //     json = json.map(function(item) {
            //         item.CODE = item.CODE.toString();
            //         return item;
            //     });

                economicActivitiesData = json;

                const menu = $('#economicActivityDropdown .menu');
                menu.empty();

                // json.forEach(function(item) {
                //     const option = $('<div>', {
                //         class: 'item',
                //         'data-value': item.CODE,
                //         text: item.CODE+' - '+ titleCaseSpanish((item.ACTIVITY_NAME).trim())
                //     });
                //     menu.append(option);
                // });

                json.forEach(function(item) {
                    const codigo = item.CODE.toString();
                    const nombre = capitalizeWords((item.ACTIVITY_NAME).trim());

                    const option = $('<div>', {
                        class: 'item',
                        'data-value': codigo,
                        'data-text': `${codigo} ${nombre}`, // Aquí se incluye texto completo para búsqueda
                        text: `${codigo} - ${nombre}`
                    });

                    menu.append(option);
                });


                $('#economicActivityDropdown').dropdown('refresh');

                $('#economicActivityDropdown').dropdown({
                    minCharacters: 0,
                    fullTextSearch: false,
                    forceSelection: true,
                    apiSettings: {
                        responseAsync: function (settings, callback) {

                            const query = settings.urlData.query.toLowerCase();

                            const filtered = json
                                .filter(item => item.SEARCH_TEXT.includes(query))
                                .slice(0, 80);

                            const results = filtered.map(item => ({
                                name: item.CODE + ' - ' + item.ACTIVITY_NAME,
                                value: item.CODE
                            }));

                            callback({ success: true, results });
                        }
                    },
                    onChange: function(value, text, $selectedItem) {
                        if (value) {

                            const selectedActivity = economicActivitiesData.find(function(item) {
                                return item.CODE === value;
                            });

                            if (selectedActivity && selectedActivity.PERCENTAGE) {

                                const percentageString = selectedActivity.PERCENTAGE.replace(',', '.');
                                const percentage = Number.parseFloat(percentageString).toFixed(2);

                                $('#economic_activity_percentage').val(percentage.replace('.', ',') + '%');

                                if (selectedActivity.branch_id) {
                                    $('#economicBranch').val(selectedActivity.branch_id);
                                    $('#economicBranchDropdown').dropdown('set selected', selectedActivity.branch_id);
                                }
                            } else {
                                $('#economic_activity_percentage').val('');
                            }
                        } else {
                            $('#economic_activity_percentage').val('');
                        }
                    }
                });

                if (inicial) {
                    selectEconomicActivity(@json($quotation->activity_economic_id))
                }
           // }
        }

        function selectEconomicActivity(value) {

            if (value) {

                $('#economic_activity_code').val(value);
                $('#economicActivityDropdown').dropdown('set selected', value);

                const selectedActivity = economicActivitiesData.find(function(item) {
                    return item.CODE === value;
                });

                if (selectedActivity && selectedActivity.PERCENTAGE) {

                    const percentageString = selectedActivity.PERCENTAGE.replace(',', '.');
                    const percentage = Number.parseFloat(percentageString).toFixed(2);

                    $('#economic_activity_percentage').val(percentage.replace('.', ',') + '%');

                    if (selectedActivity.branch_id) {
                        $('#economicBranch').val(selectedActivity.branch_id);
                        $('#economicBranchDropdown').dropdown('set selected', selectedActivity.branch_id);
                    }
                } else {
                    $('#economic_activity_percentage').val('');
                }
            } else {
                $('#economic_activity_percentage').val('');
            }


            const selectedValue = document.getElementById("workRisk").value;

            if (selectedValue == 5) {
                $('input[name="sector"][value="public"]').prop('checked', true);
                $('#div_economic_branch').hide();
            } else {
                $('input[name="sector"][value="private"]').prop('checked', true);
                $('#div_economic_branch').show();
            }

            if (selectedValue == 3) {

                $('#div_economic_activity_percentage').hide();
                $('#div_optionAsegurement').show();
                $('#economicBranchDropdown').addClass('disabled');

            }else{

                $('#economicBranchDropdown').dropdown('set enabled');
                $('#div_economic_activity_code').removeClass('disabled');
                $('#div_economic_activity_percentage').show();
                $('#div_optionAsegurement').hide();
            }


        }


        /**
         * Fin de bloque de código que permite inicializar la busqueda de la actividad economica *
         **/

        $('#content .menu_item').on('click', function(event) {

            validateAndSave(event);

        });

        function validateDataEmail(event) {
            event?.preventDefault();

            const email = $('#validateEmail').val();
            var emailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

            if (!emailPattern.test(email)) {

                Swal.fire({
                    icon: 'error',
                    title: 'Correo electrónico inválido',
                    html: 'Por favor, ingrese un nuevo correo.',
                    confirmButtonText: 'Aceptar',
                    confirmButtonColor: '#000000', // Color negro para el botón
                });
                return;
            }
        }

        $(document).ready(function() {


            $('.heirDocTypeDropdown, #nameInput, #heirDocTypeDropdownDos').each(function() {
                let value = $(this).val() || $(this).text();

                if (value) {
                    let capitalizedValue = capitalizeWords(value);

                    if ($(this).is('input')) {
                        $(this).val(capitalizedValue);
                    } else {
                        $(this).text(capitalizedValue);
                    }
                }
            });

            $('.heirDocTypeDropdownDos').each(function() {
                let value = $(this).val() || $(this).text();

                if (value) {
                    let capitalizedValue = capitalizeWords(value);

                    if ($(this).is('input')) {
                        $(this).val(capitalizedValue);
                    } else {
                        $(this).text(capitalizedValue);
                    }
                }
            });

        });

        function capitalizeWords(str) {
            // if (value) {
            //     return value.toLowerCase().replace(/(?:^|\s)\S/g, function(char) {
            //         return char.toUpperCase();
            //     });
            // }
            // return '';

            const minorWords = [
                'y', 'e', 'o', 'ni', 'de', 'del', 'la', 'las', 'los',
                'el', 'en', 'con', 'a', 'por', 'para', 'al'
            ];

            return str
                .toLowerCase()
                .split(' ')
                .map((word, index) => {
                    return index === 0 || !minorWords.includes(word)
                        ? word.charAt(0).toUpperCase() + word.slice(1)
                        : word;
                })
                .join(' ');
        }
    </script>
@endsection
