@extends('layouts.main')

@section('title', 'Template')

@section('menu')
    @parent
@endsection

@section('content')
    <div class="ui basic segment">
        <h1 class="ui header">
            Cotización #{{ $activity->quotation->formatNumber() }}
        </h1>
        <form autocomplete="off" action="{{ secure_url("servicio/{$activity->id}/quotation/save") }}" id="dictamen"
            method="post" class="ui small form">
            <div class="ui secondary segment">
                <div class="ui three columns grid">
                    <div class="column">
                        <b># de identificación:</b> {{ $activity->affiliate->doc_type }}
                        {{ $activity->affiliate->doc_number }}
                    </div>
                    <div class="column capitalize"><b>Nombre:</b> <a
                            href="{{ secure_url('afiliado/' . $activity->affiliate_id) }}">{{ ucwords(mb_strtolower($activity->affiliate->full_name)) }}</a>
                    </div>

                    <div class="column"><b># de cotización:</b> {{ $activity->quotation->formatNumber() }}</div>
                    <div class="column">
                        <b>Fecha solicitud:</b> <span id="fechaSolicitud" data-fecha="{{ $activity->created_at }}"></span>
                    </div>
                </div>
            </div>
            {{-- DATOS DEL INTERMEDIARIO --}}
            <div class="ui styled fluid accordion">
                <div class="title"><i class="dropdown icon"></i> Datos del intermediario</div>
                <div class="content">

                    <div class="two fields">
                        <div class="field required">
                            <label for="input1">Nombre de la correduría</label>
                            <input readonly type="text" id="input1"
                                value="{{ ucwords(mb_strtolower($quotation->brokerage_name)) }}">
                        </div>
                        <div class="field required">
                            <label for="input2">Nombre del corredor y/o asesor</label>
                            <input readonly type="text" id="input2"
                                value="{{ mb_convert_case(mb_strtolower($quotation->advisor_name), MB_CASE_TITLE, 'UTF-8') }}">
                        </div>
                    </div>
                    <div class="two fields">
                        <div class="field required">
                            <label for="input3">Código</label>
                            <input readonly type="text" id="input3" value="{{ $quotation->code }}">
                        </div>
                        <div class="field  required">
                            <label for="input4">Correo electrónico nuevos negocios</label>
                            <input readonly type="text" id="input4" value="{{ $quotation->email }}">
                        </div>
                    </div>

                </div>
                <div class="title"><i class="dropdown icon"></i> Datos del tomador</div>
                <div class="content">
                    <div class=" fluid accordion">
                        <div class="title "><i class="dropdown icon"></i> Identificación del tomador</div>
                        <div class="content">
                            <div class="two fields">
                                <div class="field required">
                                    <label for="input1">Tipo de identificación</label>
                                    <input readonly type="text" id="input1" value="{{ $affiliate->doc_type }}">
                                </div>
                                <div class="field required">
                                    <label for="input2">Número de identificación</label>
                                    <input readonly type="text" id="input2" value="{{ $affiliate->doc_number }}">
                                </div>
                            </div>
                        </div>
                        <div class="title "><i class="dropdown icon"></i> Datos del tomador</div>
                        <div class="content">
                            <div class="three fields">
                                <div class="required field">
                                    <label for="salary_projection">Nombre</label>
                                    <input class="prompt" id="salary_projection" type="text" name="salary_projection"
                                        value="{{ mb_convert_case(mb_strtolower($affiliate->first_name), MB_CASE_TITLE, 'UTF-8') }}"
                                        readonly>
                                </div>
                                <div class="required field">
                                    <label for="salary_projection">Teléfonos</label>
                                    <input class="prompt" id="salary_projection" type="text" name="salary_projection"
                                        value="{{ $affiliate->phone }}" readonly>
                                </div>
                                <div class="required field">
                                    <label for="salary_projection">Correo electrónico de notificaciones</label>
                                    <input class="prompt" id="salary_projection" type="text" name="salary_projection"
                                        value="{{ $affiliate->email }}" readonly>
                                </div>
                            </div>
                            <div class="three fields">
                                <div class="required field heir_payment-class" id="heir_doc_type">
                                    <div class="ui form">
                                        <div class="required field">
                                            <label>Sector</label>
                                        </div>
                                        <div class="inline fields">
                                            <div class="field">
                                                <div class="ui radio checkbox">
                                                    <input type="radio" name="economic_activity" value="public"
                                                        tabindex="0" disabled
                                                        @if ($quotation->economic_activity == 'public') checked @endif>
                                                    <label>Público</label>
                                                </div>
                                            </div>
                                            <div class="field">
                                                <div class="ui radio checkbox">
                                                    <input type="radio" name="economic_activity" value="private"
                                                        tabindex="1" disabled
                                                        @if ($quotation->economic_activity == 'private') checked @endif>
                                                    <label>Privado</label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

{{--                                <div class="required field">--}}
{{--                                    <label for="validity_from">Código de actividad económica*</label>--}}
{{--                                    <input id="" type="text" value="{{ $quotation->activity_economic_id }}"--}}
{{--                                        placeholder="" readonly>--}}
{{--                                </div>--}}

{{--                                <div class="required field">--}}
{{--                                    <label for="activity_name">Nombre de la actividad económica</label>--}}
{{--                                    <input id="activity_name" type="text" value="{{ $activity_economic_name }}"--}}
{{--                                        readonly>--}}
{{--                                </div>--}}

                                @if($economicActivity->economic_branch && $economicActivity->economic_branch->branch_name)  {{--  cuando es publica no tiene rama--}}
                                <div class="required field">
                                    <label>Rama general de actividad económica</label>
                                    <input id="branch_activity" class="prompt" name="branch_activity" type="text" readonly value="{{ $economicActivity->economic_branch->branch_name ?? '' }}">
                                </div>
                                @endif

                                <div class="required field">
                                    <label>Actividad económica especifica</label>
                                    <textarea readonly rows="2">{{ $economicActivity->code.' - '.$economicActivity->activity_name }}</textarea>
                                    <input id="economic_activity_code" name="economic_activity_code" type="hidden" value="{{$economicActivity->code ?? ''}}">
                                    <input id="activity_name" name="activity_name" type="hidden" value="{{$economicActivity->activity_name ?? ''}}">
                                </div>

                            </div>
                        </div>
                    </div>

                </div>

                <div class="title"><i class="dropdown icon"></i> Datos póliza</div>
                <div class="content">

                    <div class="three fields">
                        <div class="required field heir_payment-class" id="heir_doc_type">
                            <div class="ui form">
                                <div class="required field">
                                    <label>Temporalidad</label>
                                </div>
                                <div class="inline fields">
                                    <div class="field">
                                        <div class="ui radio checkbox">
                                            <input type="radio" name="temporality" value="permanent" tabindex="0"
                                                disabled @if ($quotation->temporality == 'permanent') checked @endif>
                                            <label>Permanente</label>
                                        </div>
                                    </div>
                                    <div class="field">
                                        <div class="ui radio checkbox">
                                            <input type="radio" name="temporality" value="short" tabindex="1"
                                                disabled @if ($quotation->temporality == 'short') checked @endif>
                                            <label>Periodo Corto</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="required field">
                            <label for="validity_from">Vigencia de la póliza desde</label>
                            <input id="validity_from" type="text" value="{{ $quotation->validity_from }}"
                                placeholder="dd/mm/yyyy" readonly>
                        </div>

                        <div class="required field">
                            <label for="validity_to">Vigencia de la póliza hasta</label>
                            <input id="validity_to" type="text" placeholder="dd/mm/yyyy"
                                value="{{ $quotation->validity_to }}" readonly>
                        </div>
                    </div>
                    <div class="three fields">
                        <div class="required field">
                            <label for="moneytype">Tipo de moneda</label>
                            <div class="ui selection dropdown" style="pointer-events: none; background-color: #f3f4f5"
                                id="typeCurrencyDropdown">
                                <input type="hidden" name="type_currency" class="minus_font" id="type_currency"
                                    value="{{ $quotation->type_currency }}" readonly>
                                <i class="dropdown icon"></i>
                                <div class="default text">Seleccione una opción</div>
                                <div class="menu">
                                    @foreach ($MONEY_TYPE as $k => $currency)
                                        <div class="item " data-value="{{ $k }}">
                                            {{ $currency['symbol'] }}
                                            {{ mb_convert_case($currency['name'], MB_CASE_TITLE, 'UTF-8') }}
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                        <div class="required field">
                            @if ($quotation->salary_projection > 0)
                                @if ($quotation->temporality == 'short')
                                    <label for="salary_projection">Estimación total de salario o contrato</label>
                                @else
                                    <label for="salary_projection">Planilla mensual</label>
                                @endif

                                <input class="prompt" id="salary_projection" type="text" name="salary_projection"
                                    value="{{ $quotation->type_currency === 'USD' ? '$' : '₡' }}{{ number_format($quotation->salary_projection, 2, ',', '.') }}"
                                    readonly>
                            @endif
                        </div>
                        @if ($quotation->temporality == 'permanent' && $quotation->salary_projection > 0)
                            <div class="required field">
                                <label for="salary_projection_anual">Estimación anual de salario y/o monto
                                    asegurado</label>
                                <input class="prompt" id="salary_projection_anual" type="text"
                                    name="salary_projection_anual"
                                    value="{{ $quotation->type_currency === 'USD' ? '$' : '₡' }}{{ number_format($quotation->salary_projection * 12, 2, ',', '.') }}"
                                    readonly>
                            </div>
                        @endif
                    </div>
                </div>

                <div class="title"><i class="dropdown icon"></i> Cálculo de la prima
                </div>
                <div class="content">
                    @if ($quotation->temporality == 'permanent')
                        <div class="ui four fields">
                            <div class="field">
                                <label for="anual">
                                    Anual
                                </label>
                                <input readonly id="anual" type="text"
                                    value="{{ $quotation->type_currency === 'USD' ? '$' : '₡' }} {{ number_format($quotation->annual_calculation_amount, 2, ',', '.') }}">
                            </div>
                            @if ($quotation->semiannual_calculation_amount > 0)
                                <div class="field">
                                    <label for="semestral">
                                        Semestral
                                    </label>
                                    <input readonly id="semestral" type="text"
                                        value="{{ $quotation->type_currency === 'USD' ? '$' : '₡' }} {{ number_format($quotation->semiannual_calculation_amount, 2, ',', '.') }}">
                                </div>
                            @endif
                            @if ($quotation->quarterly_calculation_amount > 0)
                                <div class="field">
                                    <label for="trimestral">
                                        Trimestral
                                    </label>
                                    <input readonly id="trimestral" type="text"
                                        value="{{ $quotation->type_currency === 'USD' ? '$' : '₡' }} {{ number_format($quotation->quarterly_calculation_amount, 2, ',', '.') }}">
                                </div>
                            @endif
                            @if ($quotation->monthly_calculation_amount > 0)
                                <div class="field">
                                    <label for="mensual">
                                        Mensual
                                    </label>
                                    <input readonly type="text" id="mensual"
                                        value="{{ $quotation->type_currency === 'USD' ? '$' : '₡' }} {{ number_format($quotation->monthly_calculation_amount, 2, ',', '.') }}">
                                </div>
                            @endif
                        </div>
                    @endif
                    <br>
                    @if ($quotation->temporality == 'short')
                        <div class="fields">
                            <div class="field">
                                <label for="unico">
                                    Pago único
                                </label>
                                <input readonly type="text" id="unico"
                                    value="{{ $quotation->type_currency === 'USD' ? '$' : '₡' }} {{ number_format($quotation->single_payment_value, 2, ',', '.') }}">
                            </div>
                        </div>
                    @endif
                </div>
                {{-- ALTO RIESGO --}}
                {{--                <div class="title"><i class="dropdown icon"></i> Soportes</div> --}}
                {{--                <div class="content"> --}}
                {{--                    <table class="ui celled sortable striped very compact very small table"> --}}
                {{--                        <thead> --}}
                {{--                            <tr> --}}
                {{--                                <th>Documento</th> --}}
                {{--                                <th>Archivo</th> --}}
                {{--                            </tr> --}}
                {{--                        </thead> --}}
                {{--                        <tbody> --}}
                {{--                            <tr> --}}
                {{--                                <td class="required">El tomador cuenta con una comisión de salud ocupacional debidamente --}}
                {{--                                    inscrita en el Consejo de Salud Ocupacional.</td> --}}
                {{--                                <td> --}}
                {{--                                    <div class=" fields "> --}}
                {{--                                        @if (isset($documents[274])) --}}
                {{--                                            <a target="_blank" href="{{ secure_url('/file/' . $documents[274]->path) }}" --}}
                {{--                                                class="ui secondary icon button pop" style="width: 4rem" --}}
                {{--                                                data-tooltip="Descargar"> --}}
                {{--                                                <i class="download icon"></i> --}}
                {{--                                            </a> --}}
                {{--                                        @endif --}}
                {{--                                    </div> --}}
                {{--                                </td> --}}
                {{--                            </tr> --}}
                {{--                            <tr> --}}
                {{--                                <td>El tomador cuenta con una oficina de salud ocupacional debidamente inscrita en el --}}
                {{--                                    Consejo de Salud Ocupacional</td> --}}
                {{--                                <td> --}}
                {{--                                    <div class=" fields "> --}}
                {{--                                        @if (isset($documents[277])) --}}
                {{--                                            <a target="_blank" href="{{ secure_url('/file/' . $documents[277]->path) }}" --}}
                {{--                                                class="ui secondary icon button pop" style="width: 4rem" --}}
                {{--                                                data-tooltip="Descargar"> --}}
                {{--                                                <i class="download icon"></i> --}}
                {{--                                            </a> --}}
                {{--                                        @endif --}}
                {{--                                    </div> --}}
                {{--                                </td> --}}
                {{--                            </tr> --}}
                {{--                            <tr> --}}
                {{--                                <td>El tomador cuenta con un plan de salud o programa de salud ocupacional actualizado en --}}
                {{--                                    los últimos 2 años.</td> --}}
                {{--                                <td> --}}
                {{--                                    <div class=" fields "> --}}
                {{--                                        @if (isset($documents[278])) --}}
                {{--                                            <a target="_blank" href="{{ secure_url('/file/' . $documents[278]->path) }}" --}}
                {{--                                                class="ui secondary icon button pop" style="width: 4rem" --}}
                {{--                                                data-tooltip="Descargar"> --}}
                {{--                                                <i class="download icon"></i> --}}
                {{--                                            </a> --}}
                {{--                                        @endif --}}
                {{--                                    </div> --}}
                {{--                                </td> --}}
                {{--                            </tr> --}}
                {{--                            <tr> --}}
                {{--                                <td class="description"> --}}
                {{--                                    <p>Adjunte un registro estadístico de los accidentes de los últimos 2 años que incluya --}}
                {{--                                        indicadores de: Incidencia, Frecuencia, Duración Media, Gravedad. Aportar registros --}}
                {{--                                    </p> --}}
                {{--                                </td> --}}
                {{--                                <td> --}}
                {{--                                    <div class=" fields "> --}}
                {{--                                        @if (isset($documents[279])) --}}
                {{--                                            <a target="_blank" href="{{ secure_url('/file/' . $documents[279]->path) }}" --}}
                {{--                                                class="ui secondary icon button pop" style="width: 4rem" --}}
                {{--                                                data-tooltip="Descargar"> --}}
                {{--                                                <i class="download icon"></i> --}}
                {{--                                            </a> --}}
                {{--                                        @endif --}}
                {{--                                    </div> --}}
                {{--                                </td> --}}
                {{--                            </tr> --}}
                {{--                            <tr> --}}
                {{--                                <td class="description"> --}}
                {{--                                    <p>Posee la matriz de riesgos laborales actualizada en el último año. Aportar registro --}}
                {{--                                    </p> --}}
                {{--                                </td> --}}
                {{--                                <td> --}}
                {{--                                    <div class=" fields "> --}}
                {{--                                        @if (isset($documents[280])) --}}
                {{--                                            <a target="_blank" href="{{ secure_url('/file/' . $documents[280]->path) }}" --}}
                {{--                                                class="ui secondary icon button pop" style="width: 4rem" --}}
                {{--                                                data-tooltip="Descargar"> --}}
                {{--                                                <i class="download icon"></i> --}}
                {{--                                            </a> --}}
                {{--                                        @endif --}}
                {{--                                    </div> --}}
                {{--                                </td> --}}
                {{--                            </tr> --}}
                {{--                            <tr> --}}
                {{--                                <td class="description"> --}}
                {{--                                    <p>Posee servicio médico y/o brigada que atienda los accidentes laborales y enfermedades --}}
                {{--                                        del trabajo que cubra el 80 por ciento de la jornada de trabajo .</p> --}}
                {{--                                </td> --}}
                {{--                                <td> --}}
                {{--                                    <div class=" fields "> --}}
                {{--                                        @if (isset($documents[281])) --}}
                {{--                                            <a target="_blank" href="{{ secure_url('/file/' . $documents[281]->path) }}" --}}
                {{--                                                class="ui secondary icon button pop" style="width: 4rem" --}}
                {{--                                                data-tooltip="Descargar"> --}}
                {{--                                                <i class="download icon"></i> --}}
                {{--                                            </a> --}}
                {{--                                        @endif --}}
                {{--                                    </div> --}}
                {{--                                </td> --}}
                {{--                            </tr> --}}
                {{--                            <tr> --}}
                {{--                                <td class="description"> --}}
                {{--                                    <p>Aportar programa de capacitaciones en temas de seguridad y salud en el trabajo que se --}}
                {{--                                        han brindado en el último año.</p> --}}
                {{--                                </td> --}}
                {{--                                <td> --}}
                {{--                                    <div class=" fields "> --}}
                {{--                                        @if (isset($documents[282])) --}}
                {{--                                            <a target="_blank" href="{{ secure_url('/file/' . $documents[282]->path) }}" --}}
                {{--                                                class="ui secondary icon button pop" style="width: 4rem" --}}
                {{--                                                data-tooltip="Descargar"> --}}
                {{--                                                <i class="download icon"></i> --}}
                {{--                                            </a> --}}
                {{--                                        @endif --}}
                {{--                                    </div> --}}
                {{--                                </td> --}}
                {{--                            </tr> --}}
                {{--                            <tr> --}}
                {{--                                <td class="description"> --}}
                {{--                                    <p>La organización desarrolla acciones de rendición de cuentas a la alta dirección o --}}
                {{--                                        dirección corporativa del tomador en temas de seguridad y salud. Aportar evidencia --}}
                {{--                                        de la última rendición de cuentas en temas de prevención de riesgos laborales. </p> --}}
                {{--                                </td> --}}
                {{--                                <td> --}}
                {{--                                    <div class=" fields "> --}}
                {{--                                        @if (isset($documents[283])) --}}
                {{--                                            <a target="_blank" href="{{ secure_url('/file/' . $documents[283]->path) }}" --}}
                {{--                                                class="ui secondary icon button pop" style="width: 4rem" --}}
                {{--                                                data-tooltip="Descargar"> --}}
                {{--                                                <i class="download icon"></i> --}}
                {{--                                            </a> --}}
                {{--                                        @endif --}}
                {{--                                    </div> --}}
                {{--                                </td> --}}
                {{--                            </tr> --}}
                {{--                        </tbody> --}}
                {{--                    </table> --}}
                {{--                </div> --}}


                @if ($quotation->activity->activity_actions->where('action_id', 355)->isNotEmpty() && (auth()->user()->area_id == 1 || auth()->user()->area_id == 44 || auth()->user()->area_id == 58) )
                    <div class="title"><i class="dropdown icon"></i> Condiciones especiales</div>
                        <div class="content">
                            <form class="ui form">
                                <div class="fields" style="display: flex; gap: 40px; align-items: center;">
                                    <div class="field">
                                        <label>¿Posee oficina de Salud Ocupacional y Comisión debidamente inscrita?</label>
                                        <div class="inline fields">
                                            <div class="ui toggle checkbox">
                                                <input type="radio" name="salud_ocupacional" value="si" 
                                                    {{ $quotation->has_office == 'si' ? 'checked' : '' }} disabled>
                                                <label>Sí</label>
                                            </div>
                                            <div class="ui toggle checkbox" style="margin-left: 70px;">
                                                <input type="radio" name="salud_ocupacional" value="no" 
                                                    {{ $quotation->has_office == 'no' ? 'checked' : '' }} disabled>
                                                <label>No</label>
                                            </div>
                                        </div>
                                    </div>
                            
                                    <div class="field">
                                        <label>¿Actualmente cuenta con bonificaciones tarifarias?</label>
                                        <div class="inline fields">
                                            <div class="ui toggle checkbox">
                                                <input type="radio" name="bonificaciones" value="si" 
                                                    {{ $quotation->tariff_bonuses == 'si' ? 'checked' : '' }} disabled>
                                                <label>Sí</label>
                                            </div>
                                            <div class="ui toggle checkbox" style="margin-left: 70px;">
                                                <input type="radio" name="bonificaciones" value="no" 
                                                    {{ $quotation->tariff_bonuses == 'no' ? 'checked' : '' }} disabled>
                                                <label>No</label>
                                            </div>
                                        </div>
                                    </div>
                            
                                    <div class="field">
                                        <label>¿Cuenta con consultorio médico debidamente habilitado?</label>
                                        <div class="inline fields">
                                            <div class="ui toggle checkbox">
                                                <input type="radio" name="consultorio_medico" value="si" 
                                                    {{ $quotation->has_medical_office == 'si' ? 'checked' : '' }} disabled>
                                                <label>Sí</label>
                                            </div>
                                            <div class="ui toggle checkbox" style="margin-left: 70px;">
                                                <input type="radio" name="consultorio_medico" value="no" 
                                                    {{ $quotation->has_medical_office == 'no' ? 'checked' : '' }} disabled>
                                                <label>No</label>
                                            </div>
                                        </div>
                                    </div>
                                </div><br>
                                <div>

                                    @if($activity->activity_actions->where('action_id', 354)->isNotEmpty())
                                        Esta cotización ya cuenta con condiciones especiales aprobadas
                                    @else
                                        @if($quotation->activity->state_id == 7 )
                                            <button class="ui primary small button" onclick="modalCalculateConditionSpecial(event)">
                                                Condiciones especiales
                                            </button>
                                        @endif
                                    @endif
                                    
                                </div>
                            </form>
                        </div>
                    </div>
                @endif
            </div>
            <div class="ui basic segment">
                <div class="fields">
                    <div class="six wide field">
                        @if($quotation->activity->state_id == 7 )
                            <a href="{{ secure_url('/servicio/' . $activity->id . '/quotation/generatePdf') }}"
                                class="ui secondary small button" target="_blank">
                                <i class="file pdf outline white icon"></i> Vista previa
                            </a>
                        @endif
                        <a href="{{ secure_url('/servicio/' . $activity->id) }}" class="ui secondary small button"><i
                                class="arrow left icon"></i> Volver a la actividad</a>
                    </div>
                </div>
            </div>
            {{ csrf_field() }}
        </form>

         {{-- Modal calculo de descuentos condiciones especiales --}}
         <div class="ui modal" id="modalCalculateConditionSpecial">
            <i class="close icon"></i>
                <div class="header">
                    Aprobar condiciones especiales
                </div>
            <div class="content">
                <div style="display: flex; justify-content: space-between; align-items: flex-start;">
                    <table style="width: 70%">
                        <tr>
                            <td class="green-mnk">Aprobación de beneficios</td>
                        </tr>
                        <tr>
                            <td class="green-mnk">Consultorio laboral atención primaria (CLAP)</td>
                            <td>
                                <select id="select_clap"  onchange="validarCampo()">
                                    <option value="si" selected>Si</option>
                                    <option value="no">No</option>
                                </select>
                            </td>
                            <td>
                                <input  step="1" class="input-condition" value="0" name="clap" type="number" id="field_clap" min="0" max="5" oninput="validarValor(this)">
                            </td>
                        </tr>
                        <tr>
                            <td class="green-mnk" >Cumplimiento y mejora continua (CMC)</td>
                            <td>
                                <select id="select_cmc"  onchange="validarCampo()">
                                    <option value="si" selected>Si</option>
                                    <option value="no">No</option>
                                </select>
                            </td>
                            <td><input class="input-condition" name="cmc" type="number" id="field_cmc"  min="0" max="10"  oninput="validarValor(this)"></td>
                        </tr>
                        <tr>
                            <td class="green-mnk">No pago de fraccionamiento</td>
                            <td>
                                <select id="select_fraccionamiento"  onchange="validarCampo()">
                                    <option value="si">Si</option>
                                    <option value="no" selected>No</option>
                                </select>
                            </td>
                            <td></td>
                        </tr>
                        <tr>
                            <td class="green-mnk">Continuidad de bonificación</td>
                            <td>
                                <select id="select_continuidad"  onchange="validarCampo()">
                                    <option value="si" selected>Si</option>
                                    <option value="no">No</option>
                                </select>
                            </td>
                            <td><input class="input-condition" value="0" min="0" max="10" id="beneficio_continuidad" type="number" oninput="validarValor(this)"></td>
                        </tr>
                    </table><br>
                   <!-- Recuadro a la derecha -->
                    <div style="width: 28%; margin-top: 29px; border: 1px solid #ccc; padding: 15px; border-radius: 5px; background-color: #f9f9f9; margi">
                        <strong>Moneda de la cotización</strong>
                        @if($activity->quotation->type_currency === 'USD')
                            <p><strong>$</strong> Dolares </p>
                        @else
                            <p><strong>₡</strong> Colones </p>
                        @endif
                    </div>
                </div>

                <table style="width: 100%"> 
                    <tr>
                        <td class="green-mnk">Tarifa básica MNK</td>
                        <th class="green-mnk">{{$activity->quotation->economic_activity_percentage}}</th>
                    </tr>
                    <tr>
                        <td>Tipo de fraccionamiento</td>
                        <th>Anual</th>
                        <th>Semestral</th>
                        <th>Trimestral</th>
                        <th>Mensual</th>
                    </tr>
                    <tr>
                        <td>Tarifa con fraccionamiento MNK Seguros</td>
                        <td class="number-mnk" style="text-align: center">{{$activity->quotation->anual_percentage}}%</td>
                        <td class="number-mnk" style="text-align: center">{{$activity->quotation->semestral_percentage}}%</td>
                        <td class="number-mnk" style="text-align: center">{{$activity->quotation->trimestral_percentage}}%</td>
                        <td class="number-mnk" style="text-align: center">{{$activity->quotation->mensual_percentage}}%</td>
                    </tr>
                    <tr>
                        <td>Descuento por fraccionamiento <span id="opcion_fraccionamiento" style="display: inline-block; width: 70px; text-align: right;"></span></td>
                        <td class="number-mnk"><span id="anual_fraccionamiento_descuento" type="number" style="border: none; text-align: center;"></span>%</td>
                        <td class="number-mnk"><span id="semestral_fraccionamiento_descuento" type="number" style="border: none; text-align: center;"></span>%</td>
                        <td class="number-mnk"><span id="trimestral_fraccionamiento_descuento" type="number" style="border: none; text-align: center;"></span>%</td>
                        <td class="number-mnk"><span id="mensual_fraccionamiento_descuento" type="number" style="border: none; text-align: center;"></span>%</td>
                    </tr>
                    <tr>
                        <td>Beneficio de continuidad <span id="beneficio_continuidad_formateado" style="display: inline-block; width: 100px; text-align: right;"></span>%</td>
                        <td class="number-mnk"><span  id="anual_continuidad" type="number" style="border: none; text-align: center;"></span>%</td>
                        <td class="number-mnk"><span  id="semianual_continuidad" type="number" style="border: none; text-align: center;"></span>%</td>
                        <td class="number-mnk"><span  id="trimestral_continuidad" type="number" style="border: none; text-align: center;"></span>%</td>
                        <td class="number-mnk"><span  id="mensual_continuidad" type="number" style="border: none; text-align: center;"></span>%</td>
                    </tr>
                    <tr>
                        <td>Planilla anual</td>
                        <td class="number-mnk" colspan="4">{{ number_format( ($activity->quotation->salary_projection * 12), 2, '.', ',') }}</td>
                    </tr><br>
                 
                    <tr>
                        <td class="green-mnk">Prima MNK Seguros</td>
                        <td class="number-mnk"><span  id="anual_original" type="number" style="border: none; text-align: center;"></span></td>
                        <td class="number-mnk"><span  id="semestral_original" type="number" style="border: none; text-align: center;"></span></td>
                        <td class="number-mnk"><span  id="trimestral_original" type="number" style="border: none; text-align: center;"></span></td>
                        <td class="number-mnk"><span  id="mensual_original" type="number" style="border: none; text-align: center;"></span></td>
                    </tr>
                    <tr>
                        <td>Monto de la prima fraccionada MNK</td>
                        <td></td>
                        <td class="number-mnk"><span  id="semestral_original_fraccionado" type="number" style="border: none; text-align: center;"></span></td>
                        <td class="number-mnk"><span  id="trimestral_original_fraccionado" type="number" style="border: none; text-align: center;"></span></td>
                        <td class="number-mnk"><span  id="mensual_original_fraccionado" type="number" style="border: none; text-align: center;"></span></td>
                    </tr>
                    <tr>
                        <td>CMC <span id="cmc_calculado" style="display: inline-block; width: 100px; text-align: right;"></span>%</td>
                        <td class="number-mnk"><span  id="anual_cmc" type="number" style="border: none; text-align: center;"></span></td>
                        <td class="number-mnk"><span  id="semianual_cmc" type="number" style="border: none; text-align: center;"></span></td>
                        <td class="number-mnk"><span  id="trimestral_cmc" type="number" style="border: none; text-align: center;"></span></td>
                        <td class="number-mnk"><span  id="mensual_cmc" type="number" style="border: none; text-align: center;"></span></td>
                    </tr>
                    <tr>
                        <td>CLAP<span id="clap_calculado" style="display: inline-block; width: 100px; text-align: right;"></span>%</td>
                        <td class="number-mnk"><span  id="anual_clap" type="number" style="border: none; text-align: center;"></span></td>
                        <td class="number-mnk"><span  id="semianual_clap" type="number" style="border: none; text-align: center;"></span></td>
                        <td class="number-mnk"><span  id="trimestral_clap" type="number" style="border: none; text-align: center;"></span></td>
                        <td class="number-mnk"><span  id="mensual_clap" type="number" style="border: none; text-align: center;"></span></td>
                    </tr>
                    <tr>
                        <td>Monto total descuentos</td>
                        <td class="number-mnk"><span  id="anual_descuento" type="number" style="border: none; text-align: center;"></span></td>
                        <td class="number-mnk"><span  id="semianual_descuento" type="number" style="border: none; text-align: center;"></span></td>
                        <td class="number-mnk"><span  id="trimestral_descuento" type="number" style="border: none; text-align: center;"></span></td>
                        <td class="number-mnk"><span  id="mensual_descuento" type="number" style="border: none; text-align: center;"></span></td>
                    </tr>
                    <tr>
                        <td>Prima anual a pagar</td>
                        <td class="number-mnk"><span  id="anual_final" type="number" style="border: none; text-align: center;"></span></td>
                        <td class="number-mnk"><span  id="semianual_final" type="number" style="border: none; text-align: center;"></span></td>
                        <td class="number-mnk"><span  id="trimestral_final" type="number" style="border: none; text-align: center;"></span></td>
                        <td class="number-mnk"><span  id="mensual_final" type="number" style="border: none; text-align: center;"></span></td>
                    </tr>
                    <tr>
                        <td class="green-mnk">Prima fraccionada a pagar</td>
                        <td></td>
                        <td class="number-mnk"><span  id="semianual_fraccionada" type="number" style="border: none; text-align: center;"></span></td>
                        <td class="number-mnk"><span  id="trimestral_fraccionada" type="number" style="border: none; text-align: center;"></span></td>
                        <td class="number-mnk"><span  id="mensual_fraccionada" type="number" style="border: none; text-align: center;"></span></td>
                    </tr>
                </table>
            </div>
            <div class="actions" style="display: flex; justify-content: space-between; padding: 10px;">
                <button class="ui primary button" id="cancelModalButton">Cancelar</button>
                <button type="submit" class="ui primary button" id="submitModalButton">Aprobar</button>
            </div>
        </div>
    </div>


    <style type="text/css">
        .number-mnk{
            text-align: right !important;
        }
        .input-condition {
            width: 100%; 
            border: 1px solid rgba(34, 36, 38, .15); 
            border-radius: 5px;
            text-align: center;"
        }
        .green-mnk{
           background:  #91C845 !important;
        }
         table {
            border-collapse: collapse;
            font-family: Arial, sans-serif;
        }
        th, td {
            border: 1px solid rgba(34, 36, 38, .15);
            padding: 5px;
        }
        th {
            font-size: 12px;
        }
        td:first-child {
            font-weight: bold;
        }
        .title {
            text-transform: none !important;
        }

        .ui.grid .column {
            padding: 0.5rem 1rem !important;
        }

        .sender .field {
            display: none;
        }

        .ear.field {
            display: none;
        }

        .msp.field {
            display: none;
        }

        .ui.accordion .title {
            text-transform: uppercase;
        }

        .field>h3 {
            text-align: center;
            margin-top: 1.25rem;
        }

        .ui.search>.results {
            width: 30rem;
        }

        .ui.search>.results .result .title {
            padding: 0 !important;
            border: none !important;
            text-transform: none;
        }

        .ui.search>.results .result .content {
            padding: 0 !important;
        }

        th {
            text-align: center !important;
        }

        input {
            text-transform: none !important;
        }

        .capitalize {
            text-transform: capitalize !important;
        }

        .minus_font {
            text-transform: lowercase !important;
        }

        input[readonly],
        textarea[readonly] {
            background-color: #f3f4f5 !important;
        }
    </style>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/locale/es.min.js"></script>

    <script>

        $('#cancelModalButton').on('click', function(e) {
            e.preventDefault();
            $('.ui.modal').modal('hide');
        });

        let data = {}; 

        //Funcion para la validación de los selects e inputs de los beneficios
        function validarCampo() {
            //Variables de los selects de beneficios y opciones de si y no 
            let selectClap = document.getElementById("select_clap");
            let selectCmc = document.getElementById("select_cmc");
            let selectContinuidad = document.getElementById("select_continuidad");
            let selectFraccionamiento = document.getElementById("select_fraccionamiento");
            let fieldClap = document.getElementById("field_clap");
            let fieldCmc = document.getElementById("field_cmc");
            let fieldContinuidad = document.getElementById("beneficio_continuidad");
            let opcion_fraccionamiento = document.getElementById("opcion_fraccionamiento");

            //Manejo de descuento por fraccionamiento 
            if (selectFraccionamiento.value === "no") {
                opcion_fraccionamiento.innerText = 'No';
            }else{
                opcion_fraccionamiento.innerText = 'Si';
            }

            //Manejo de continuidad de bonificacion 
            if (selectContinuidad.value === "no") {
                fieldContinuidad.value = 0;
                fieldContinuidad.disabled = true;
            } else {
                fieldContinuidad.disabled = false;
            }

            // Manejo de descuento CLAP
            if (selectClap.value === "no") {
                fieldClap.value = 0;
                fieldClap.disabled = true;
                clap_calculado.innerText = 0;
            } else {
                fieldClap.disabled = false;
            }
    
            // Manejo de descuento CMC
            if (selectCmc.value === "no") {
                fieldCmc.value = 0;
                fieldCmc.disabled = true;
                cmc_calculado.innerText = 0;
            } else {
                fieldCmc.disabled = false;
            }

            validarValor();
        }

        //Función para realizar los calculos de la tabla
        function validarValor(input) {

            if(input){
                let valor = input.value;

                // Verificar si contiene algún carácter no numérico
                const invalidCharIndex = valor.search(/[^0-9]/);

                // Verifica si el valor contiene decimales, letras o cualquier cosa no entera
                if (invalidCharIndex !== -1) {
                    // Mostrar alerta de error
                    Swal.fire({
                        icon: 'error',
                        title: 'Valor no válido',
                        text: 'Solo se permiten números enteros.',
                        confirmButtonColor: "#000000"
                    });

                    // Cortar el valor hasta el primer carácter no válido
                    valor = valor.substring(0, invalidCharIndex);
                    
                    let numero = parseInt(valor);

                    // Actualizar el campo con el valor limpio
                    input.value = numero;
                }
            }

            var cmc = 0;
            var clap = 0;

            let fieldClap = document.getElementById("field_clap");
            let fieldCmc = document.getElementById("field_cmc");
            let cmc_calculado = document.getElementById("cmc_calculado");
            let clap_calculado = document.getElementById("clap_calculado");
            let continuidad = document.getElementById("beneficio_continuidad");
            let selectFraccionamiento = document.getElementById("select_fraccionamiento");
          
            // Convertir valores a números
            let valorClap = parseInt(fieldClap.value);
            let valorCmc = parseInt(fieldCmc.value);
            console.log('valorCmc', valorCmc);
            let valorContinuidad = parseInt(continuidad.value);

            if(valorContinuidad > 10){

                cmc = valorCmc ; 
                
                Swal.fire({
                    icon: "warning",
                    title: "Valor no permitido",
                    text: "El valor del beneficio de continuidad no puede ser mayor a 10",
                    confirmButtonColor: "#000000"
                });

                continuidad.value = 10;

                $('#beneficio_continuidad_formateado').text(continuidad.value);

            }else{

                $('#beneficio_continuidad_formateado').text(continuidad.value);

            }
    
            if (valorClap > 5) {
                fieldClap.value = 5;
                Swal.fire({
                    icon: "warning",
                    title: "Valor no permitido",
                    text: "El valor de CLAP no puede ser mayor a 5",
                    confirmButtonColor: "#000000"
                });

                clap = 5;
                valorClap = 5;
                clap_calculado.innerText = valorClap;
            }else{
                clap = valorClap;
                clap_calculado.innerText = valorClap;
            }
           
            if(valorCmc >= valorClap){
                cmc =  valorCmc - valorClap;
                console.log('cmc',cmc);

                cmc_calculado.innerText = cmc;
            }

            if (valorCmc > 10) {
                fieldCmc.value = 10;
                Swal.fire({
                    icon: "warning",
                    title: "Valor no permitido",
                    text: "El valor de CMC no puede ser mayor a 10",
                    confirmButtonColor: "#000000"
                });

                cmc =  10 - valorClap;

                cmc_calculado.innerText = cmc;
            }

            if (cmc >= 0 || clap >= 0 || parseInt(continuidad.value) >= 0) {
                const anualStr      = "{{ number_format($activity->quotation->annual_calculation_amount, 2, '.', ',') }}";
                const semianualStr  = "{{ number_format($activity->quotation->semiannual_calculation_amount, 2, '.', ',') }}";
                const trimestralStr = "{{ number_format($activity->quotation->quarterly_calculation_amount, 2, '.', ',') }}";
                const mensualStr    = "{{ number_format($activity->quotation->monthly_calculation_amount, 2, '.', ',') }}";

                let anual_continuidad      = "{{$activity->quotation->anual_percentage}}";
                let semestral_continuidad  = "{{$activity->quotation->semestral_percentage}}";
                let trimestral_continuidad = "{{$activity->quotation->trimestral_percentage}}";
                let mensual_continuidad    = "{{$activity->quotation->mensual_percentage}}";

                if (selectFraccionamiento.value === "no") {
                    $('#anual_fraccionamiento_descuento').text(anual_continuidad);
                    $('#semestral_fraccionamiento_descuento').text(semestral_continuidad);
                    $('#trimestral_fraccionamiento_descuento').text(trimestral_continuidad);
                    $('#mensual_fraccionamiento_descuento').text(mensual_continuidad);
                } else {
                    anual_continuidad         = "{{$activity->quotation->anual_percentage}}";
                    semestral_continuidad     = "{{$activity->quotation->anual_percentage}}";
                    trimestral_continuidad    = "{{$activity->quotation->anual_percentage}}";
                    mensual_continuidad       = "{{$activity->quotation->anual_percentage}}";

                    $('#anual_fraccionamiento_descuento').text(anual_continuidad);
                    $('#semestral_fraccionamiento_descuento').text(semestral_continuidad);
                    $('#trimestral_fraccionamiento_descuento').text(trimestral_continuidad);
                    $('#mensual_fraccionamiento_descuento').text(mensual_continuidad);
                }

                function convertir(valor, porcentaje) {
                    return parseFloat(valor.replace(/,/g, '')) * (porcentaje / 100);
                }

                function convertirFloat(valor, porcentaje) {
                    return valor * (porcentaje / 100);
                }

                function formatear(valor) {
                    return valor.toLocaleString('en-US', {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2
                    });
                }


                // Esta es la base con continuidad ya formateada desde Blade
                const salaryProjectionBaseStr = "{{ number_format(($activity->quotation->salary_projection * 12), 2, '.', ',') }}";

                // Convertimos a número
                const salaryProjectionBase = parseFloat(salaryProjectionBaseStr.replace(/,/g, ''));

                //Si tiene beneficio de continuidad
                let anualBase, semianualBase, trimestralBase, mensualBase;

                let newAnualContinuidad, newSemianualContinuidad, newTrimestralContinuidad, newMensualContinuidad;
                if (parseInt(continuidad.value) >= 0) {
                    // Mostrar beneficio de continuidad
                    $('#beneficio_continuidad_formateado').text(formatear(continuidad.value));

                    // Usar valores ajustados por continuidad
                    const anualContinuidad = parseFloat(anual_continuidad.replace(/,/g, '')) / 100;
                    newAnualContinuidad = anualContinuidad - (anualContinuidad * (continuidad.value / 100));
                    $('#anual_continuidad').text(formatear(newAnualContinuidad));
                    anualBase = parseFloat((newAnualContinuidad / 100).toFixed(4)) * salaryProjectionBase;

                    console.log('anualBase', anualBase, 'newAnualContinuidad', newAnualContinuidad , 'salaryProjectionBase', salaryProjectionBase);

                    const semianualContinuidad = parseFloat(semestral_continuidad.replace(/,/g, '')) / 100;
                    newSemianualContinuidad = semianualContinuidad - (semianualContinuidad * (continuidad.value / 100));
                    $('#semianual_continuidad').text(formatear(newSemianualContinuidad));
                    semianualBase = parseFloat((newSemianualContinuidad / 100).toFixed(4)) * salaryProjectionBase;

                    const trimestralContinuidad = parseFloat(trimestral_continuidad.replace(/,/g, '')) / 100;
                    newTrimestralContinuidad = trimestralContinuidad - (trimestralContinuidad * (continuidad.value / 100));
                    $('#trimestral_continuidad').text(formatear(newTrimestralContinuidad));
                    trimestralBase = parseFloat((newTrimestralContinuidad / 100).toFixed(4)) * salaryProjectionBase;

                    const mensualContinuidad = parseFloat(mensual_continuidad.replace(/,/g, '')) / 100;
                    newMensualContinuidad = mensualContinuidad - (mensualContinuidad * (continuidad.value / 100));
                    $('#mensual_continuidad').text(formatear(newMensualContinuidad));
                    mensualBase = parseFloat((newMensualContinuidad / 100).toFixed(4)) * salaryProjectionBase;

                    $('#anual_original').text(formatear(anualBase));
                    $('#semestral_original').text(formatear(semianualBase));
                    $('#trimestral_original').text(formatear(trimestralBase));
                    $('#mensual_original').text(formatear(mensualBase));

                    const semianualBaseFraccionado = semianualBase / 2;
                    const trimestralBasefraccionado = trimestralBase / 4; 
                    const mensualBaseFraccionado = mensualBase / 12; 

                    $('#semestral_original_fraccionado').text(formatear(semianualBaseFraccionado));
                    $('#trimestral_original_fraccionado').text(formatear(trimestralBasefraccionado));
                    $('#mensual_original_fraccionado').text(formatear(mensualBaseFraccionado));

                } else {
                    // No aplica continuidad: usar montos originales
                    anualBase = parseFloat(anualStr.replace(/,/g, ''));
                    semianualBase = parseFloat(semianualStr.replace(/,/g, ''));
                    trimestralBase = parseFloat(trimestralStr.replace(/,/g, ''));
                    mensualBase = parseFloat(mensualStr.replace(/,/g, ''));

                    const semianualBaseFraccionado = semianualBase;
                    const trimestralBasefraccionado = trimestralBase; 
                    const mensualBaseFraccionado = mensualBase ; 

                    $('#anual_original').text(formatear(anualBase));
                    $('#semestral_original').text(formatear(semianualBase * 2));
                    $('#trimestral_original').text(formatear(trimestralBase * 4));
                    $('#mensual_original').text(formatear(mensualBase * 12));

                    $('#semestral_original_fraccionado').text(formatear(semianualBaseFraccionado));
                    $('#trimestral_original_fraccionado').text(formatear(trimestralBasefraccionado));
                    $('#mensual_original_fraccionado').text(formatear(mensualBaseFraccionado));
                }
                // Inicializamos variables
                let anual_cmc = 0, anual_clap = 0;
                let semianual_cmc = 0, semianual_clap = 0;
                let trimestral_cmc = 0, trimestral_clap = 0;
                let mensual_cmc = 0, mensual_clap = 0;

                // CMC
                if (cmc >= 0) {
                    anual_cmc = convertirFloat(anualBase, cmc);
                    semianual_cmc = convertirFloat(semianualBase, cmc) / 2;
                    trimestral_cmc = convertirFloat(trimestralBase, cmc) / 4;
                    mensual_cmc = convertirFloat(mensualBase, cmc) / 12;

                    $('#anual_cmc').text(formatear(anual_cmc));
                    $('#semianual_cmc').text(formatear(semianual_cmc));
                    $('#trimestral_cmc').text(formatear(trimestral_cmc));
                    $('#mensual_cmc').text(formatear(mensual_cmc));
                }

                // CLAP
                if (clap >= 0) {
                    anual_clap = convertirFloat(anualBase, clap);
                    semianual_clap = convertirFloat(semianualBase, clap) / 2;
                    trimestral_clap = convertirFloat(trimestralBase, clap) / 4;
                    mensual_clap = convertirFloat(mensualBase, clap) / 12;

                    $('#anual_clap').text(formatear(anual_clap));
                    $('#semianual_clap').text(formatear(semianual_clap));
                    $('#trimestral_clap').text(formatear(trimestral_clap));
                    $('#mensual_clap').text(formatear(mensual_clap));
                }

                // Descuentos acumulados
                const anualDescuento = anual_cmc + anual_clap;
                const semianualDescuento = (semianual_cmc + semianual_clap) * 2;
                const trimestralDescuento = (trimestral_cmc + trimestral_clap) * 4;
                const mensualDescuento = (mensual_cmc + mensual_clap) * 12;

                $('#anual_descuento').text(formatear(anualDescuento));
                $('#semianual_descuento').text(formatear(semianualDescuento));
                $('#trimestral_descuento').text(formatear(trimestralDescuento));
                $('#mensual_descuento').text(formatear(mensualDescuento));

                // Valor total con descuento aplicado
                $('#anual_final').text(formatear(anualBase - anualDescuento));
                $('#semianual_final').text(formatear( (semianualBase)   - semianualDescuento));
                $('#trimestral_final').text(formatear( (trimestralBase) - trimestralDescuento));
                $('#mensual_final').text(formatear( (mensualBase)  - mensualDescuento));

                $('#semianual_fraccionada').text(formatear( ((semianualBase)  - semianualDescuento) / 2 ));
                $('#trimestral_fraccionada').text(formatear( ((trimestralBase) - trimestralDescuento) / 4 ));
                $('#mensual_fraccionada').text(formatear( ( (mensualBase)  - mensualDescuento) / 12));

                //Desolcutar boton aprobar 
                if( cmc > 0 || clap > 0 || continuidad.value > 0 || selectFraccionamiento.value == "si" ){
                    $('#submitModalButton').show();
                }else{
                    $('#submitModalButton').hide();
                }

                const activity_id = "{{$activity->id}}";

                data = {
                    anual_base: anualBase,
                    semianual_base: semianualBase,
                    trimestral_base: trimestralBase,
                    mensual_base: mensualBase,
                    
                    anual_continuidad: newAnualContinuidad,
                    semianual_continuidad: newSemianualContinuidad,
                    trimestral_continuidad: newTrimestralContinuidad,
                    mensual_continuidad: newMensualContinuidad,

                    anual_cmc: anual_cmc,
                    semianual_cmc: semianual_cmc,
                    trimestral_cmc: trimestral_cmc,
                    mensual_cmc: mensual_cmc,

                    anual_clap: anual_clap,
                    semianual_clap: semianual_clap,
                    trimestral_clap: trimestral_clap,
                    mensual_clap: mensual_clap,

                    anual_descuento: anualDescuento,
                    semianual_descuento: semianualDescuento,
                    trimestral_descuento: trimestralDescuento,
                    mensual_descuento: mensualDescuento,

                    anual_final: anualBase - anualDescuento,
                    semianual_final: semianualBase  - semianualDescuento,
                    trimestral_final: trimestralBase  - trimestralDescuento,
                    mensual_final: mensualBase  - mensualDescuento,

                    semianual_fraccionada: (semianualBase  - semianualDescuento) / 2,
                    trimestral_fraccionada: (trimestralBase  - trimestralDescuento) / 4,
                    mensual_fraccionada: (mensualBase  - mensualDescuento) / 12,
                   
                    continuidad_value: continuidad.value,  
                    cmc: cmc,
                    clap: clap,
                    activity_id : activity_id,
                    fraccionamiento_value : selectFraccionamiento.value

                };
            }
        }


        $('#submitModalButton').on('click', function(e) {
            e.preventDefault();  

            loadingMain(true);

            $('.ui.modal').modal('hide');

            // Enviar la variable 'data' mediante AJAX
            $.ajax({
                url: '/servicio/saveConditionSpecial',  
                type: 'POST',
                data: data,  
                success: function(response) {
                    // Manejar la respuesta del servidor
                    if (response.success) {
                        loadingMain(false);
                        Swal.fire({
                            icon: 'success',
                            title: 'Datos guardados',
                            text: 'Se enviarón los datos para la condición especial',
                            confirmButtonText: 'Aceptar',
                            confirmButtonColor: '#000000',
                        }).then((result) => {
                            if (result.isConfirmed) {
                                location.reload(); // Recarga la página
                            }
                        });

                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Datos no guardados',
                            text: 'Favor contactar al administrador',
                            confirmButtonText: 'Aceptar',
                            confirmButtonColor: '#000000',
                        });
                        loadingMain(false);
                    }
                },
                error: function(xhr, status, error) {
                    Swal.fire({
                            icon: 'error',
                            title: 'Datos no guardados',
                            text: 'Favor contactar al administrador',
                            confirmButtonText: 'Aceptar',
                            confirmButtonColor: '#000000',
                        });
                    loadingMain(false);
                }
            });
        });
    </script>

    <script type="text/javascript">

        //Modal para calcular condiciones especiales
        function modalCalculateConditionSpecial(e){
            e.preventDefault();
            $('.ui.modal').modal('show');
        }

        var generatePDF = function() {
            $('form#dictamen').attr('target', '_blank');
            $('form#dictamen').attr('action', '{{ secure_url('/servicio/' . $activity->id . '/quotation/pdf') }}');
            $('form#dictamen').submit();
            $('form#dictamen').removeAttr('target');
            $('form#dictamen').attr('action', '{{ secure_url('/servicio/' . $activity->id . '/quotation/save') }}');
            return false;
        };

        var toggleSender = function() {
            console.log('sender');
            if ($('[name=sender]').val() == 5 || $('[name=sender]').val() == 8) {
                $('.sender .field').show();
            } else {
                $('.sender .field').hide();
            }
        };

        var cie10 = [];


        $(document).ready(function() {
            $('.ui.accordion .ui.grid .row').css('padding-top', 0);
            $('.ui.accordion .ui.grid .column').css('padding-top', 0);

            $('.ui.accordion').accordion({
                exclusive: false
            });

            $('form#dictamen a.red').click(function() {
                $(this).parent().parent().remove();
                calculateDeficienceSum();
            });
            $('form .ui.dropdown').dropdown({
                forceSelection: false
            });
            $('form .datepicker').pickadate({
                selectYears: 100,
                selectMonths: true,
                max: new Date(),
                formatSubmit: 'yyyy-mm-dd',
                format: 'mmm dd, yyyy'
            });

            $.getJSON('/js/cie10.json', function(json) {
                cie10 = json;
                $('form .ui.search.code').search({
                    source: cie10,
                    fields: {
                        title: 'COD',
                        description: 'DESCRIPTION'
                    },
                    searchFields: ['COD', 'DESCRIPTION'],
                    regExp: {
                        escape: /[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,
                        beginsWith: ''
                    },
                    showNoResults: true,
                    maxResults: 250,
                    searchFullText: false,
                    error: {
                        noResults: 'No se encontraron resultados para tu búsqueda.'
                    },
                    onSelect: function(result, response) {
                        $(this).parents('.fields').find('input.description').val(result
                            .DESCRIPTION);
                    }
                });
            });

            $('form input').keydown(function(event) {
                if (event.keyCode == 13) {
                    event.preventDefault();
                    return false;
                }
            });


            @if (Auth::user()->isViewer())
                $('.datepicker').pickadate('picker').stop();
                $('#dictamen .ui.search input').attr('disabled', 'disabled');
                $('#dictamen input, #dictamen textarea').attr('readonly', 'readonly');
                $('#dictamen .ui.dropdown').addClass('disabled');
                $('#dictamen .button').remove();
            @endif
        });
    </script>

    <script>
        $(document).ready(function() {

            $('#submitModalButton').hide();
            // Establecemos Moment.js al locale español
            moment.locale('es');

            // Seleccionamos el elemento que contiene la fecha
            var fechaTexto = $('#fechaSolicitud').data('fecha');

            // Formateamos la fecha usando Moment.js
            var fechaFormateada = moment(fechaTexto).format('dddd D [de] MMMM [de] YYYY');

            // Capitalizamos la primera letra de la fecha (opcional)
            fechaFormateada = fechaFormateada.charAt(0).toUpperCase() + fechaFormateada.slice(1);

            // Colocamos la fecha formateada en el HTML
            $('#fechaSolicitud').text(fechaFormateada);



            // Obtenemos el valor del input de tipo date
            var fechaTo = $('#validity_to').val();

            // Formateamos la fecha a "Sábado 4 de octubre de 2024"
            var fechaFormateada3 = moment(fechaTo).format('dddd D [de] MMMM [de] YYYY');

            // Capitalizamos la primera letra de la fecha (opcional)
            fechaFormateada3 = fechaFormateada3.charAt(0).toUpperCase() + fechaFormateada3.slice(1);

            // Mostramos la fecha formateada en el input de texto
            $('#validity_to').val(fechaFormateada3);



            // Obtenemos el valor del input de tipo date
            var fechaFrom = $('#validity_from').val();

            // Formateamos la fecha a "Sábado 4 de octubre de 2024"
            var fechaFormateada4 = moment(fechaFrom).format('dddd D [de] MMMM [de] YYYY');

            // Capitalizamos la primera letra de la fecha (opcional)
            fechaFormateada4 = fechaFormateada4.charAt(0).toUpperCase() + fechaFormateada4.slice(1);

            // Mostramos la fecha formateada en el input de texto
            $('#validity_from').val(fechaFormateada4);

        });
    </script>

    <script>
        const activityId = @json($activity->id);
        $(document).ready(function() {
            $('#saveConditionSpecial').on('click', function(e) {
                e.preventDefault();

                // Obtener valores de los campos
                const specialCondition = $('#special_condition').val();
                let specialConditionPayment = $('#special_condition_payment').val()
                let preventiveActions = parseFloat($('#preventive_actions').val()) ||
                    0; // Convertir a número o usar 0


                specialConditionPayment = specialCondition == 2 || specialCondition == 4 ? 'No' :
                    specialConditionPayment;

                preventiveActions = specialCondition == 4 ? 0 : preventiveActions;

                // Validación según la opción seleccionada
                if (!specialCondition) {
                    Swal.fire({
                        icon: 'warning',
                        title: 'Validación fallida',
                        text: 'Por favor selecciona una opción en "Condición especial".',
                        confirmButtonText: 'Entendido',
                        confirmButtonColor: '#000000',
                    });
                    return;
                }

                if (specialCondition === "1" && !specialConditionPayment) {
                    Swal.fire({
                        icon: 'error',
                        title: 'Campo obligatorio',
                        text: 'Debes ingresar un valor en "Condición especial de pago".',
                        confirmButtonText: 'Entendido',
                        confirmButtonColor: '#000000',
                    });
                    return;
                }

                if (specialCondition === "2" && !preventiveActions) {
                    Swal.fire({
                        icon: 'error',
                        title: 'Campo obligatorio',
                        text: 'Debes ingresar un valor en "Reconocimiento por acciones preventivas".',
                        confirmButtonText: 'Entendido',
                        confirmButtonColor: '#000000',
                    });
                    return;
                }

                if (specialCondition === "3" && (!specialConditionPayment || !preventiveActions)) {
                    Swal.fire({
                        icon: 'error',
                        title: 'Campos obligatorios',
                        text: 'Debes ingresar valores en ambos campos: "Condición especial de pago" y "Reconocimiento por acciones preventivas".',
                        confirmButtonText: 'Entendido',
                        confirmButtonColor: '#000000',
                    });
                    return;
                }

                // Lógica de validación
                if (specialConditionPayment > 100) {
                    Swal.fire({
                        icon: 'error',
                        title: 'Validación fallida',
                        text: 'El campo condición especial de pago no puede ser mayor a 100%.',
                        confirmButtonText: 'Entendido',
                        confirmButtonColor: '#000000',
                    });

                    return;
                }

                if (preventiveActions > 10) {
                    Swal.fire({
                        icon: 'error',
                        title: 'Validación fallida',
                        text: 'El campo reconocimiento por acciones preventivas no puede ser mayor al 10%.',
                        confirmButtonText: 'Entendido',
                        confirmButtonColor: '#000000',
                    });

                    return;
                }

                // Crear objeto con los datos a enviar
                const data = {
                    special_condition: specialCondition,
                    special_condition_payment: specialConditionPayment,
                    preventive_actions: preventiveActions,
                };

                // Mostrar indicador de carga
                loadingMain(true);

                // Enviar datos con Ajax
                $.ajax({
                    url: `/servicio/${activityId}/quotation/saveActionConditionSpecial`,
                    method: 'POST',
                    data: data,
                    dataType: 'json', // Indica que esperas JSON en la respuesta
                    success: function(result) {
                        Swal.fire({
                            icon: 'success',
                            title: 'Éxito',
                            text: result.message ||
                                'La acción se guardó correctamente.',
                            confirmButtonText: 'Aceptar',
                            confirmButtonColor: '#000000',
                        }).then(() => {
                            location.reload();
                        });
                    },
                    error: function(xhr, status, error) {
                        console.error('Error al guardar la acción:', error);
                        Swal.fire({
                            icon: 'error',
                            title: 'Error',
                            text: 'Hubo un problema al guardar la acción. Intenta nuevamente.',
                            confirmButtonText: 'Aceptar',
                            confirmButtonColor: '#000000',
                        });
                    },
                    complete: function() {
                        loadingMain(false); // Ocultar el indicador de carga
                    },
                });
            });
        });
    </script>

@endsection
