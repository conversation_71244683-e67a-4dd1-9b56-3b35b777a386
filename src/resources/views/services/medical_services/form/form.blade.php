@extends('layouts.main')

@section('title', 'PRESTACIONES MÉDICAS - SORT')

@section('menu')
    @parent
@endsection

@section('content')
    <div class="ui basic segment">
        <h1 class="ui header">
            Prestaciones médicas SORT - atención primaria
            <div class="sub header">Campos con <span style="color: red;" class="required">*</span> obligatorios.</div>
        </h1>

        <div class="ui secondary segment">
            <div class="ui grid">
                <div class="three column row">
                    <div class="column left aligned">
                        <b>Identificación:</b> {{ $activity->affiliate->doc_type }} {{ $activity->affiliate->doc_number }}
                    </div>
                    <div class="column center aligned">
                        <b>Nombre:</b> <a
                            href="{{ secure_url('afiliado/' . $activity->affiliate_id) }}">
                            {{ mb_convert_case(mb_strtolower($activity->affiliate->full_name, 'UTF-8'), MB_CASE_TITLE, 'UTF-8') }}</a>
                    </div>
                    <div class="column right aligned">
                        <b>Actividad:</b> <a
                            href="{{ secure_url('servicio/' . $activity->id) }}">{{ $activity->service->name }}</a>
                    </div>
                </div>
            </div>
        </div>

        <form id="form-medical-services" class="ui attached form"
            action="{{ secure_url('/servicio/' . $id . '/medical_services/save') }}" method="post">
            {{ csrf_field() }}
            <div class="ui styled fluid accordion">
                <!-- Formulario para datos del paciente -->
                @include('services.medical_services.form.components.form-insured-details', [
                    'affiliate' => $activity->affiliate,
                ])

                <!-- GIS Reporte accidente -->
                @if ($gisActivity)
                    @include('services.medical_services.form.components.form_reporte_accidente', [
                        'planillas',
                    ])
                @endif

                <!-- Formulario para DATOS DEL PROVEEDOR (IPS)-->
                @include('services.medical_services.form.components.form-supplier-details', [
                    'medical_service' => $activity->medical_services_sort,
                ])
                <!-- Atención médica-->
                @if ($activity->medical_services_sort && $activity->medical_services_sort->followUps)
                    @foreach ($activity->medical_services_sort->followUps as $followUp)
                        <div class="title"><i class="dropdown icon"></i>Atención médica #{{ $followUp->follow_up_number }}
                        </div>
                        <div class="content" data-followup-id="{{ $followUp->follow_up_number }}">
                            <div class="accordion transition">
                                <!-- Formulario para valoración médica -->
                                @include(
                                    'services.medical_services.form.components.form-medical-evaluation',
                                    ['medical_service' => $followUp, 'disabled' => true]
                                )
                                <!-- Formulario para ACOMPAÑANTE -->
                                @include('services.medical_services.form.components.form-companions', [
                                    'companions' => $followUp->companions,
                                    'disabled' => true,
                                ])
                                <!-- Formulario para DIAGNÓSTICOS -->
                                @include('services.medical_services.form.components.form-diagnosis', [
                                    'diagnostics' => $followUp->previousDiagnostics(),
                                    'disabled' => true,
                                    'numeroatencion' => $followUp->follow_up_number
                                ])
                                <!-- Formulario para ORDEN DE IMÁGENES DIAGNÓSTICAS -->
                                @include(
                                    'services.medical_services.form.components.form-order-diagnostic-images',
                                    [
                                        'medical_service' => $followUp,
                                        'diagnostics_images' => $followUp->diagnosticsImages,
                                        'disabled' => true,
                                    ]
                                )
                                <!-- Formulario para enfermedad Actual -->
                                @include('services.medical_services.form.components.form-current-illness', [
                                    'medical_service' => $followUp,
                                    'disabled' => true,
                                ])
                                <!-- Formulario para antecedentes -->
                                @include('services.medical_services.form.components.form-records', [
                                    'medical_service' => $followUp,
                                    'disabled' => true,
                                ])
                                <!-- Formulario para examen fisico -->
                                @include(
                                    'services.medical_services.form.components.form-physical-examination',
                                    ['medical_service' => $followUp, 'disabled' => true]
                                )
                                <!-- Formulario para INCAPACIDAD MÉDICA O LICENCIA -->
                                @include(
                                    'services.medical_services.form.components.form-medical-incapacity-or-leave',
                                    ['medical_service' => $followUp,
                                     'disabled' => true, 'activity' => $activity,
                                     'diagnostics_mirror' => $diagnostic_mirror,
                                     'show_diagnostic_mirror' => true,
                                     'numeroatencion' => $followUp->follow_up_number]
                                )
                                <!-- Formulario para REMISIÓN A ESPECIALISTA-->
                                @include(
                                    'services.medical_services.form.components.form-referral-to-specialist',
                                    [
                                        'medical_service' => $followUp,
                                        'specialists' => $followUp->specialists,
                                        'disabled' => true,
                                    ]
                                )
                                <!-- Formulario para FÓRMULA MÉDICA -->
                                @include(
                                    'services.medical_services.form.components.form-medical-prescription',
                                    [
                                        'medical_service' => $followUp,
                                        'medical_prescriptions' => $followUp->medicalPrescriptions,
                                        'disabled' => true,
                                    ]
                                )
                                <!-- Formulario para FÓRMULA DE MEDICAMENTOS CONTROLADOS -->
                                @include(
                                    'services.medical_services.form.components.form-controlled-medication-formula',
                                    [
                                        'medical_service' => $followUp,
                                        'controlled_medications' => $followUp->controlledMedications,
                                        'disabled' => true,
                                    ]
                                )
                                <!-- Formulario para MÉDICO EVALUADOR-->
                                @include(
                                    'services.medical_services.form.components.form-evaluating-doctor',
                                    ['medical_service' => $followUp, 'disabled' => true]
                                )
                                <!-- Formulario para OBSERVACIÓN-->
                                @include('services.medical_services.form.components.form-observation', [
                                    'medical_service' => $followUp,
                                    'disabled' => true,
                                ])
                                <!-- Formulario para AUDITORÍA MÉDICA-->
                                @include('services.medical_services.form.components.form-medical-audit', [
                                    'medical_service' => $followUp,
                                    'disabled' => true,
                                ])
                                <!-- Formulario para HOSPITALIZACIÓN-->
                                @include(
                                    'services.medical_services.form.components.form-hospitalization',
                                    ['medical_service' => $followUp, 'disabled' => true]
                                )
                                <!-- Formulario para PLAN DE ACCIÓN -->
                                @include('services.medical_services.form.components.form-plan-action', [
                                    'medical_service' => $followUp,
                                    'disabled' => true,
                                ])
                            </div>
                        </div>
                    @endforeach
                @endif

                {{-- ATENCIONES MEDICAS SECUNDARIAS --}}
                @foreach ($activityMedicalSecundary as $activitySecundary)
                    <div class="title"><i class="dropdown icon"></i>Atenciónes médicas secundarias servicio
                        #{{ $activitySecundary->id }}</div>
                    <div class="content" >
                        <div class="accordion transition">
                            @if (
                                $activitySecundary->medical_services_secondary_care_sort &&
                                    $activitySecundary->medical_services_secondary_care_sort->followUps)
                                @if ($activitySecundary->medical_services_secondary_care_sort->followUps->isNotEmpty())
                                    @foreach ($activitySecundary->medical_services_secondary_care_sort->followUps as $followUp)
                                        <div class="title"><i class="dropdown icon"></i>Atención médica secundaria
                                            #{{ $followUp->follow_up_number }}</div>
                                        <div class="content">
                                            <div class="accordion transition" data-followup-id="{{ $followUp->follow_up_number }}">
                                                <!-- Formulario para valoración médica -->
                                                @include(
                                                    'services.medical_services_secondary_care.form.components.form-medical-evaluation',
                                                    ['medical_service' => $followUp, 'disabled' => true]
                                                )
                                                <!-- Formulario para DIAGNÓSTICOS -->
                                                @include(
                                                    'services.medical_services_secondary_care.form.components.form-diagnosis',
                                                    ['diagnostics' => $followUp->diagnostics, 'disabled' => true, 'numeroatencion' => $followUp->follow_up_number]
                                                )
                                                <!-- Formulario para antecedentes -->
                                                @include(
                                                    'services.medical_services_secondary_care.form.components.form-records',
                                                    ['medical_service' => $followUp, 'disabled' => true]
                                                )
                                                <!-- Formulario para examen fisico -->
                                                @include(
                                                    'services.medical_services_secondary_care.form.components.form-physical-examination',
                                                    ['medical_service' => $followUp, 'disabled' => true]
                                                )
                                                <!-- Formulario para INCAPACIDAD MÉDICA O LICENCIA -->
                                                @include(
                                                    'services.medical_services_secondary_care.form.components.form-medical-incapacity-or-leave',
                                                    ['medical_service' => $followUp, 'disabled' => true, 'diagnostics_mirror' => $diagnostic_mirror,
                                                    'show_diagnostic_mirror' => true, 'numeroatencion' =>null]
                                                )
                                                <!-- Formulario para ORDEN DE IMÁGENES DIAGNÓSTICAS -->
                                                @include(
                                                    'services.medical_services_secondary_care.form.components.form-order-diagnostic-images',
                                                    [
                                                        'medical_service' => $followUp,
                                                        'diagnostics_images' => $followUp->diagnosticsImages,
                                                        'disabled' => true,
                                                    ]
                                                )
                                                <!-- Formulario para REMISIÓN A ESPECIALISTA-->
                                                @include(
                                                    'services.medical_services_secondary_care.form.components.form-referral-to-specialist',
                                                    [
                                                        'medical_service' => $followUp,
                                                        'specialists' => $followUp->specialists,
                                                        'disabled' => true,
                                                    ]
                                                )
                                                <!-- Formulario para FÓRMULA MÉDICA -->
                                                @include(
                                                    'services.medical_services_secondary_care.form.components.form-medical-prescription',
                                                    [
                                                        'medical_service' => $followUp,
                                                        'medical_prescriptions' =>
                                                            $followUp->medicalPrescriptions,
                                                        'disabled' => true,
                                                    ]
                                                )
                                                <!-- Formulario para MÉDICO EVALUADOR-->
                                                @include(
                                                    'services.medical_services_secondary_care.form.components.form-evaluating-doctor',
                                                    ['medical_service' => $followUp, 'disabled' => true]
                                                )
                                                <!-- Formulario para OBSERVACIÓN-->
                                                @include(
                                                    'services.medical_services_secondary_care.form.components.form-observation',
                                                    ['medical_service' => $followUp, 'disabled' => true]
                                                )
                                                <!-- Formulario para AUDITORÍA MÉDICA-->
                                                @include(
                                                    'services.medical_services_secondary_care.form.components.form-medical-audit',
                                                    ['medical_service' => $followUp, 'disabled' => true]
                                                )
                                                <!-- Formulario para HOSPITALIZACIÓN-->
                                                @include(
                                                    'services.medical_services_secondary_care.form.components.form-hospitalization',
                                                    ['medical_service' => $followUp, 'disabled' => true]
                                                )
                                                <!-- Formulario para PLAN DE ACCIÓN -->
                                                @include(
                                                    'services.medical_services_secondary_care.form.components.form-plan-action',
                                                    ['medical_service' => $followUp, 'disabled' => true]
                                                )
                                            </div>
                                        </div>
                                    @endforeach
                                @else
                                    <div class="alert alert-info">
                                        <i class="info circle icon"></i>
                                        Aun no tiene atenciónes médicas registradas para este servicio.
                                    </div>
                                @endif
                            @endif

                        </div>
                    </div>
                @endforeach
                {{-- ATENCIONES MEDICAS SECUNDARIAS --}}
                @if (
                    $activity->medical_services_sort &&
                        ($activity->medical_services_sort->requires_follow_up === 1 ||
                            $activity->medical_services_sort->requires_follow_up === null))
                    <div class="title"><i class="dropdown icon"></i>Atención médica</div>
                    <div class="content" >
                        <div class="accordion transition">
                            <!-- Formulario Atención médica vacio -->
                            <!-- Formulario para valoración médica -->
                            @include('services.medical_services.form.components.form-medical-evaluation', [
                                'medical_service' => null,
                                'disabled' => false,
                            ])
                            <!-- Formulario para ACOMPAÑANTE-->
                            @include('services.medical_services.form.components.form-companions', [
                                'companions' => [],
                            ])
                            <!-- Formulario para DIAGNOSTICOS -->
                            @include('services.medical_services.form.components.form-diagnosis', [
                                'diagnostics' => $activity->medical_services_sort->followUps,
                                'disabled' => false, 'numeroatencion' => null
                            ])
                            <!-- Formulario para ORDEN DE IMÁGENES DIAGNÓSTICAS -->
                            @include(
                                'services.medical_services.form.components.form-order-diagnostic-images',
                                ['medical_service' => null, 'diagnostics_images' => [], 'disabled' => false]
                            )
                            <!-- Formulario para enfermedad Actual -->
                            @include('services.medical_services.form.components.form-current-illness')
                            <!-- Formulario para antecedentes -->
                            @include('services.medical_services.form.components.form-records')
                            <!-- Formulario para examen fisico -->
                            @include(
                                'services.medical_services.form.components.form-physical-examination',
                                ['medical_service' => null, 'disabled' => false]
                            )
                            <!-- Formulario para INCAPACIDAD MÉDICA O LICENCIA -->
                            @include(
                                'services.medical_services.form.components.form-medical-incapacity-or-leave',
                                ['medical_service' => null, 'disabled' => false, 'activity' => $activity,'diagnostics_mirror' => $activity->medical_services_sort->followUps,
                                     'show_diagnostic_mirror' => false, 'numeroatencion' =>null]
                            )
                            <!-- Formulario para REMISIÓN A ESPECIALISTA-->
                            @include(
                                'services.medical_services.form.components.form-referral-to-specialist',
                                ['medical_service' => null, 'specialists' => [], 'disabled' => false]
                            )
                            <!-- Formulario para FÓRMULA MÉDICA -->
                            @include(
                                'services.medical_services.form.components.form-medical-prescription',
                                ['medical_service' => null, 'medical_prescriptions' => [], 'disabled' => false]
                            )
                            <!-- Formulario para FÓRMULA DE MEDICAMENTOS CONTROLADOS -->
                            @include(
                                'services.medical_services.form.components.form-controlled-medication-formula',
                                ['medical_service' => null, 'controlled_medications' => [], 'disabled' => false]
                            )
                            <!-- Formulario para MÉDICO EVALUADOR-->
                            @include('services.medical_services.form.components.form-evaluating-doctor', [
                                'medical_service' => null,
                                'disabled' => false,
                            ])
                            <!-- Formulario para OBSERVACIÓN-->
                            @include('services.medical_services.form.components.form-observation', [
                                'medical_service' => null,
                                'disabled' => false,
                            ])
                            <!-- Formulario para AUDITORÍA MÉDICA-->
                            @include('services.medical_services.form.components.form-medical-audit', [
                                'medical_service' => null,
                                'disabled' => false,
                            ])
                            <!-- Formulario para HOSPITALIZACIÓN-->
                            @include('services.medical_services.form.components.form-hospitalization', [
                                'medical_service' => null,
                                'disabled' => false,
                            ])
                            <!-- Formulario para PLAN DE ACCIÓN -->
                            @include('services.medical_services.form.components.form-plan-action', [
                                'medical_service' => null,
                                'disabled' => false,
                            ])
                        </div>
                    </div>
                @endif


                @if (
                    $activity->medical_services_sort &&
                        Auth::user()->area_id === \App\Area::AUDITOR &&
                        $activity->medical_services_sort->requires_follow_up === 0)
                    <div class="atencion_oculta" style="display: none">
                        <div class="title"><i class="dropdown icon"></i>Atención médica</div>
                        <div class="content">
                            <div class="accordion transition" >
                                <!-- Formulario Atención médica vacio -->
                                <!-- Formulario para valoración médica -->
                                @include(
                                    'services.medical_services.form.components.form-medical-evaluation',
                                    [
                                        'medical_service' => null,
                                        'disabled' => false,
                                    ]
                                )
                                <!-- Formulario para ACOMPAÑANTE-->
                                @include('services.medical_services.form.components.form-companions', [
                                    'companions' => [],
                                ])
                                <!-- Formulario para DIAGNOSTICOS -->
                                @include('services.medical_services.form.components.form-diagnosis', [
                                    'diagnostics' => [],
                                    'numeroatencion' => null
                                ])
                                <!-- Formulario para ORDEN DE IMÁGENES DIAGNÓSTICAS -->
                                @include(
                                    'services.medical_services.form.components.form-order-diagnostic-images',
                                    ['medical_service' => null, 'diagnostics_images' => [], 'disabled' => false]
                                )
                                <!-- Formulario para enfermedad Actual -->
                                @include('services.medical_services.form.components.form-current-illness')
                                <!-- Formulario para antecedentes -->
                                @include('services.medical_services.form.components.form-records')
                                <!-- Formulario para examen fisico -->
                                @include(
                                    'services.medical_services.form.components.form-physical-examination',
                                    ['medical_service' => null, 'disabled' => false]
                                )
                                <!-- Formulario para INCAPACIDAD MÉDICA O LICENCIA -->
                                @include(
                                    'services.medical_services.form.components.form-medical-incapacity-or-leave',
                                    ['medical_service' => null, 'disabled' => false, 'activity' => $activity, 'diagnostics_mirror' => $diagnostic_mirror]
                                )
                                <!-- Formulario para REMISIÓN A ESPECIALISTA-->
                                @include(
                                    'services.medical_services.form.components.form-referral-to-specialist',
                                    ['medical_service' => null, 'specialists' => [], 'disabled' => false]
                                )
                                <!-- Formulario para FÓRMULA MÉDICA -->
                                @include(
                                    'services.medical_services.form.components.form-medical-prescription',
                                    [
                                        'medical_service' => null,
                                        'medical_prescriptions' => [],
                                        'disabled' => false,
                                    ]
                                )
                                <!-- Formulario para FÓRMULA DE MEDICAMENTOS CONTROLADOS -->
                                @include(
                                    'services.medical_services.form.components.form-controlled-medication-formula',
                                    [
                                        'medical_service' => null,
                                        'controlled_medications' => [],
                                        'disabled' => false,
                                    ]
                                )
                                <!-- Formulario para MÉDICO EVALUADOR-->
                                @include(
                                    'services.medical_services.form.components.form-evaluating-doctor',
                                    [
                                        'medical_service' => null,
                                        'disabled' => false,
                                    ]
                                )
                                <!-- Formulario para OBSERVACIÓN-->
                                @include('services.medical_services.form.components.form-observation', [
                                    'medical_service' => null,
                                    'disabled' => false,
                                ])
                                <!-- Formulario para AUDITORÍA MÉDICA-->
                                @include('services.medical_services.form.components.form-medical-audit', [
                                    'medical_service' => null,
                                    'disabled' => false,
                                ])
                                <!-- Formulario para HOSPITALIZACIÓN-->
                                @include(
                                    'services.medical_services.form.components.form-hospitalization',
                                    [
                                        'medical_service' => null,
                                        'disabled' => false,
                                    ]
                                )
                                <!-- Formulario para PLAN DE ACCIÓN -->
                                @include('services.medical_services.form.components.form-plan-action', [
                                    'medical_service' => null,
                                    'disabled' => false,
                                ])
                            </div>
                        </div>
                    </div>
                @endif




            </div>
            <div style="margin-top: 25px;">
                <div class="ui error message"></div>
                <div class="fields">
                    @if (
                        $activity->medical_services_sort &&
                            ($activity->medical_services_sort->requires_follow_up === 1 ||
                                $activity->medical_services_sort->requires_follow_up === null))
                        <div class="four wide field">
                            <button class="ui primary fluid button"><i class="save icon"></i> Guardar</button>
                        </div>
                    @endif

                    <div class="four wide field" id="button_oculto" style="display: none">
                        <button class="ui primary fluid button"><i class="save icon"></i> Guardar</button>
                    </div>


                    <div class="six wide field">
                        <button type="button" class="ui secondary button "
                            onclick="window.location.href='{{ secure_url('/servicio/' . $activity->id) }}'">
                            <i class="arrow left icon"></i>Volver a la actividad
                        </button>

                        @if (
                            $activity->medical_services_sort &&
                                Auth::user()->area_id === \App\Area::AUDITOR &&
                                $activity->medical_services_sort->requires_follow_up === 0)
                            <button type="button" class="ui secondary button " id="button_nota">
                                Agregar nota médica
                            </button>
                        @endif
                    </div>

                </div>
            </div>
        </form>
    </div>
    <style>
        .ui.grid .column {
            padding: 0.5rem 1rem !important;
        }

        .readonly {
            background: rgba(0, 0, 0, .05) !important;
            pointer-events: none;
            user-select: all;
        }

        .readonly_copy {
            background: rgba(0, 0, 0, .05) !important;
            user-select: all;
            pointer-events: auto;
            cursor: text;
        }

        .grayed-input {
            pointer-events: none;

            background-color: #f0f0f0 !important;
            /* Fondo gris claro */
            color: #888 !important;
            /* Texto gris */
            border: 1px solid #ddd !important;

            text-transform: none !important;
            /* Borde gris claro */
        }

        .pointer-events {
            pointer-events: none;
            /* Desactiva todos los eventos de puntero */
            opacity: 0.6;
            /* Opcional: Cambia la opacidad para indicar que están deshabilitados */
        }

        .textarea-expand {
            background: rgba(0, 0, 0, 0.05) !important;
        }
    </style>
    <script src="{{ secure_url('https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js') }}"></script>
    <script src="{{ secure_url('https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/locale/es.min.js') }}"></script>
    <script type="text/javascript" src="{{ secure_url('js/dataVademecumReady.js?v=1.4.15') }}"></script>
    <script type="text/javascript" src="{{ secure_url('js/policy_issuance_data.js?v=1.4.15') }}"></script>

    <!-- Textarea expansion, esto para ajustar el tamaño de la textarea para aquellas notas médicas guardadas -->
    <script>
        $(document).ready(function() {
            // Ajusta la altura al cargar la página
            $(".textarea-expand").each(function() {
                var altura = $(this)[0].scrollHeight;
                $(this).height(altura);
            });
        });
    </script>

    <script>
        $(document).ready(function() {
            $("#button_nota").click(function() {
                $("#button_oculto").show();
                $(".atencion_oculta").show();
            });
        });
    </script>

    <!--Preparar lista de vademecum-->
    <script>
        $(document).ready(function() {
            console.log("Total de grupos encontrados:", $(".vademecum-group").length);

            // Solicitud AJAX para obtener los datos del vademécum desde el backend
            $.ajax({
                url: "{{ secure_url('/obtenerLista/vademecum') }}", // URL
                method: "GET",
                success: function(response) {
                    if (response && response.List) {
                        vademecum = response.List; // Almacena los datos obtenidos

                        // Inicializa los dropdowns y campos para todos los grupos
                        initializeDropdownsAndFields();
                    } else {
                        console.error("No se recibieron datos válidos del servidor");
                    }
                },
                error: function(xhr, status, error) {
                    console.error("Error al cargar datos del vademécum desde la base de datos:", error);
                }
            });
        });
    </script>
    <script type="text/javascript">
        $(document).ready(function() {
            $('.ui.dropdown').dropdown();
            $('.ui.accordion').accordion();
            $('.minus_font').each(function() {
                let value = $(this).val().toLowerCase();
                $(this).val(value.charAt(0).toUpperCase() + value.slice(1));
            });
            $('.minus_font').on('input', function() {
                let value = $(this).val().toLowerCase();
                $(this).val(value.charAt(0).toUpperCase() + value.slice(1));
            });
        });
    </script>


    <!-- Script del Formulario de incapacidad o licencia -->
    <script>
        $(document).ready(function () {
            $.getJSON('/js/cie10.json', function (json) {
                cie10 = json.map(item => ({
                    COD: item.COD,
                    DESCRIPTION: capitalizeFirstLetter(item.DESCRIPTION),
                    SEARCH_TEXT: (item.COD + ' ' + item.DESCRIPTION).toLowerCase()
                }));

                //const dropdown = $('#diagnosticsCodDropdown');

                $('.diagnosticsCodDropdown').each(function () {
                    const dropdown = $(this);

                dropdown.dropdown({
                    minCharacters: 1,
                    fullTextSearch: false,
                    forceSelection: true,
                    apiSettings: {
                        responseAsync: function (settings, callback) {
                            const query = settings.urlData.query.toLowerCase();
                            const filtered = cie10
                                .filter(item => item.SEARCH_TEXT.includes(query))
                                .slice(0, 10);
                            const results = filtered.map(item => ({
                                name: item.COD + ' - ' + item.DESCRIPTION,
                                value: item.COD
                            }));
                            callback({ success: true, results });
                        }
                    },
                    onChange: function (value, text, $choice) {
                        const selectedItem = cie10.find(item => item.COD === value);
                        if (selectedItem) {
                            const container = $(this).closest('.fields');
                            container.find('input.description').val(selectedItem.DESCRIPTION);
                        }
                    }
                });

                // Preseleccionar valor
                const preselectedInput = dropdown.find('input[type="hidden"][name="diagnostics[cod][]"]');
                const preselectedValue = preselectedInput.val();

                if (preselectedValue) {
                    const selectedItem = cie10.find(item => item.COD === preselectedValue);
                    if (selectedItem) {
                        const menu = dropdown.find('.menu');

                        // Agrega el item al menú si no existe
                        if (menu.find(`.item[data-value="${selectedItem.COD}"]`).length === 0) {
                            const newItem = $('<div>', {
                                class: 'item',
                                'data-value': selectedItem.COD,
                                text: selectedItem.COD + ' - ' + selectedItem.DESCRIPTION
                            });
                            menu.append(newItem);
                        }

                        // Establece el valor manualmente en el input hidden
                        preselectedInput.val(selectedItem.COD);//

                        dropdown.dropdown('refresh');
                        // Luego selecciona ese valor
                        dropdown.dropdown('set selected', selectedItem.COD);

                        // Establece también la descripción
                        dropdown.closest('.fields').find('input.description').val(selectedItem.DESCRIPTION);
                    }
                }
                });
            });
        });
    </script>



    <!-- Script del Formulario de  IMC -->
    <script>
        let calculateIMC = function() {
            const form = $('[data-calculable="true"]');
            let weight = parseFloat($('[name=weight_input_enabled]').val());
            let height = parseFloat($('[name=height_input_enabled]').val());

            console.log('calculateIMC', weight, height);

            if (height > 0) {
                let imc = Math.round((weight / Math.pow(height / 100, 2)) * 10) / 10;

                if (imc < 18.5) {
                    imc += ' (Inferior a lo normal)';
                } else if (imc >= 18.5 && imc < 25) {
                    imc += ' (Normal)';
                } else if (imc >= 25 && imc < 30) {
                    imc += ' (Superior a lo normal)';
                } else if (imc >= 30) {
                    imc += ' (Obesidad)';
                } else {
                    imc += ' ()';
                }

                form.find('#imc_enabled').text(imc);
                form.find('#imc_input_enabled').val(imc);
            } else {
                form.find('#imc_enabled').text('');
                form.find('#imc_input_enabled').val('');
            }
        }
    </script>

    <!-- Validaciones del formulario-->
    <script>
        document.getElementById('form-medical-services').addEventListener('submit', function(event) {
            // Prevent form submission temporarily
            loadingMain(true);
            event.preventDefault();

            // Si el formulario no es válido, no enviarlo
            if (!validateRequiredFields($(this).attr('id'))) {
                loadingMain(false);
                return false;
            } else {
                this.submit();
            }
        });

        // Validaciones del formulario campos requeridos
        function validateRequiredFields(formId) {
            loadingMain(true);
            const invalidFieldsBySection = {};
            const form = document.getElementById(formId);
            if (!form) {
                console.error('Form not found');
                loadingMain(false);
                return false; // Return false if the form is not found
            }
            const titles = form.querySelectorAll('.title');
            let startValidating = false;
            titles.forEach(title => {
                const sectionTitle = title.textContent.trim();
                if (sectionTitle === "Atención médica") {
                    startValidating = true; // Activamos el flag
                }
                if (startValidating) {
                    const sectionId = title.nextElementSibling.id;

                    // Omitir la validación si es la sección "DATOS DEL CASO"
                    if (sectionTitle === "Datos del caso") return;
                    // Omitir la validación si es la sección "REMISIÓN A ESPECIALISTA"
                    if (sectionId === "referral-to-specialist") return;
                    // Omitir la validación si es la sección "INCAPACIDAD MÉDICA O LICENCIA"
                    if (sectionId === "medical-incapacity-or-leave") return;
                    // Omitir la validación si es la sección "ORDEN DE IMAGENES DIAGNÓSTICAS"
                    if (sectionId === "order-diagnostic-images") return;
                    if (sectionTitle === "Receta médica" || sectionTitle ===
                        "Receta médica psicotrópicos y estupefacientes") {
                        invalidFieldsBySection[sectionTitle] = [];
                    } else {
                        invalidFieldsBySection[sectionTitle] = [];
                        const content = title.nextElementSibling; // Obtener el siguiente div con el contenido

                        // Validar campos requeridos generales
                        const requiredFields = content.querySelectorAll(
                            '.field.required textarea, .field.required input[type="text"], .field.required input[type="number"], .field.required input[type="hidden"]'
                        );

                        requiredFields.forEach(field => {
                            if (sectionId === "action-plan") {
                                if ((field.name ===
                                        "next_follow_up_date" || field.name === "next_follow_up_submit")) {
                                    return;
                                }
                            }
                            // Ignorar el campo de lateralidad
                            if (field.name === "diagnostics[laterality][]") return;

                            // Validar inputs de tipo datepicker o timepicker, solo el input oculto
                            if (field.classList.contains('datepicker') || field.classList.contains(
                                    'timepicker')) {
                                const hiddenInput = content.querySelector(
                                    `input[type="hidden"][name="${field.name}_submit"]`);
                                if (hiddenInput && !hiddenInput.value) {
                                    const label = field.closest('.field').querySelector('label');
                                    if (label && !invalidFieldsBySection[sectionTitle].includes(label
                                            .textContent.trim())) {
                                        invalidFieldsBySection[sectionTitle].push(label.textContent.trim());
                                    }
                                }
                                return; // Salir de la iteración para evitar validar el input visible
                            }

                            // Validar textarea y input de texto, pero no el oculto
                            if ((field.tagName === 'TEXTAREA' && field.value.trim() === '') ||
                                (field.tagName === 'INPUT' && field.type !== 'hidden' && field.value
                                    .trim() === '')) {
                                const label = field.closest('.field').querySelector('label');
                                if (label && !invalidFieldsBySection[sectionTitle].includes(label
                                        .textContent.trim())) {
                                    invalidFieldsBySection[sectionTitle].push(label.textContent.trim());
                                }
                            }

                            // Validar input oculto (como en el canal de consulta)
                            if (field.type === 'hidden' && !field.value) {
                                const label = field.closest('.field').querySelector('label');
                                if (label && !invalidFieldsBySection[sectionTitle].includes(label
                                        .textContent.trim())) {
                                    invalidFieldsBySection[sectionTitle].push(label.textContent.trim());
                                }
                            }
                        });
                    }
                    const content = title.nextElementSibling;
                    let generalFields = [];
                    if (sectionId === "form-medical-prescription") {
                        generalFields = [{
                                selector: 'input[name="province_medical_prescription"]',
                                label: 'Provincia'
                            },
                            {
                                selector: 'input[name="canton_medical_prescription"]',
                                label: 'Cantón'
                            },
                            {
                                selector: 'input[name="district_medical_prescription"]',
                                label: 'Distrito'
                            },
                            {
                                selector: 'textarea[name="diagnosis_origin_prescription"]',
                                label: 'Origen del diagnóstico'
                            },
                        ];
                    } else {
                        generalFields = [{
                                selector: 'input[name="province_controlled_medication"]',
                                label: 'Provincia'
                            },
                            {
                                selector: 'input[name="canton_controlled_medication"]',
                                label: 'Cantón'
                            },
                            {
                                selector: 'input[name="district_controlled_medication"]',
                                label: 'Distrito'
                            },
                            {
                                selector: 'textarea[name="diagnosis_origin_controlled_medication"]',
                                label: 'Origen del diagnóstico'
                            },
                        ];
                    }

                    const fields = [];
                    generalFields.forEach(field => {
                        const inputElement = content.querySelector(field.selector);
                        if (inputElement) {
                            const value = inputElement.value.trim();
                            fields.push({
                                value: value,
                                label: field.label
                            });
                        }
                    });
                    // Validación específica para medicamentos
                    const medicines = content.querySelectorAll('.medicine-item');
                    medicines.forEach((medicine, index) => {
                        const medicineNumber = index + 1;

                        // Validar campos específicos dentro del bloque de medicamento
                        medicine.querySelectorAll(
                                'textarea, input[type="text"], input[type="number"], input[type="hidden"]')
                            .forEach(field => {
                                const value = field.value.trim();
                                const label = field.closest('.field').querySelector('label');
                                fields.push({
                                    value: value,
                                    label: label.textContent.trim()
                                });
                            });

                        // Comprobar si hay algún campo con valor
                        const hasValues = fields.some(field => field.value.trim() !== '');
                        // Para medicamentos 2 en adelante, todos los campos deben ser obligatorios
                        if (medicineNumber > 0 && hasValues) {
                            const invalidFields = fields.filter(field => field.value.trim() === '').map(
                                field => field.label);
                            if (invalidFields.length > 0) {
                                // Agregar campos inválidos al objeto
                                invalidFieldsBySection[sectionTitle].push(
                                    `Medicamento N° ${medicineNumber}: ${invalidFields.join(', ')}`)
                            }
                        }

                    });
                }
            });

            // Validación específica para los campos de proveedor (IPS)
            let contentIdProvider = document.getElementById('provider-details');
            if (contentIdProvider) {
                // Validación para los campos requeridos
                const isSectionValid = validateProviderDetails(contentIdProvider);
            } else {
                console.error('El elemento de detalles del proveedor no se encontró.');
            }

            let contentDiagnosis = document.getElementById('content-diagnosis');
            if (contentDiagnosis) {
                // Validación para los campos requeridos
                const isSectionValid = validateDiagnosis(contentDiagnosis);
            } else {
                console.error('El elemento Diagnóstico no se encontró.');
            }

            // Función de validación específica para la sección de detalles del proveedor (IPS)
            function validateProviderDetails(content) {
                const serviceGroup = content.querySelector('input[name="service_group"]').value.trim();
                const serviceMode = content.querySelector('input[name="service_mode"]').value.trim();
                const medicalRecord = content.querySelector('textarea[name="medical_record"]').value.trim();
                const entityPromote = content.querySelector('input[name="entity_promote"]').value.trim();
                const specialty = content.querySelector('input[name="specialty_ips"]').value.trim();

                const fields = [
                    {
                        value: serviceGroup,
                        label: 'Nivel de atención'
                    },
                    {
                        value: serviceMode,
                        label: 'Modalidad de la atención'
                    },
                    {
                        value: medicalRecord,
                        label: 'Código médico'
                    },
                    {
                        value: entityPromote,
                        label: 'Nombre del médico'
                    },
                    {
                        value: specialty,
                        label: 'Especialidad'
                    }
                ];

                const invalidFields = fields.filter(field => field.value === '').map(field => field.label);

                if (invalidFields.length > 0) {
                    // Si existen campos inválidos, los agregamos al objeto de validación
                    invalidFieldsBySection['Datos del proveedor (IPS)'] = invalidFields;
                }

                return true; // Retornamos true si todos los campos requeridos están completos
            }

            function validateDiagnosis(content) {
                const diseaseConfirmation = content.querySelector('input[name="gis_disease_confirmation"]:checked');
                const diseaseReportType = content.querySelector('input[name="disease_report_type_id"]');
                const injuryNatureId = content.querySelector('input[name="injury_nature_id"]');
                const injuryLocationId = content.querySelector('input[name="injury_location_id"]');

                const fields = [
                    {
                        value: diseaseConfirmation ? diseaseConfirmation.value.trim() : '',
                        label: 'Confirmación de enfermedad'
                    },
                    {
                        value: diseaseReportType ? diseaseReportType.value.trim() : '',
                        label: 'Enfermedades profesionales'
                    },
                    {
                        value: injuryNatureId ? injuryNatureId.value.trim() : '',
                        label: 'Naturaleza de la lesión'
                    },
                    {
                        value: injuryLocationId ? injuryLocationId.value.trim() : '',
                        label: 'Ubicación de la Lesión'
                    }

                ];


                const invalidFields = fields.filter(field => field.value === '').map(field => field.label);

                if (invalidFields.length > 0) {
                    // Si existen campos inválidos, los agregamos al objeto de validación
                    invalidFieldsBySection['Diagnósticos'] = invalidFields;
                }

                return true; // Retornamos true si todos los campos requeridos están completos
            }

            let contentToSpecialist = document.getElementById('referral-to-specialist');
            if (contentToSpecialist) {
                // Validación para los campos requeridos
                const isSectionValid = validateToSpecialist(contentToSpecialist);
            } else {
                console.error('El elemento referral-to-specialist.');
            }

            function validateToSpecialist(content) {
                const referralInput = content.querySelector('input[name="referral_specialist"]');

                if (!referralInput) {
                    return true; // Si el campo no existe, no validamos nada
                }

                const referral_specialist = referralInput.value.trim();

                // Si el valor es '0', no se valida nada y se retorna true
                if (referral_specialist === '0') {
                    return true;
                }

                let fields = [{
                    value: referral_specialist,
                    label: 'Referencia a especialista'
                }];

                if (referral_specialist === '1') {
                    const specialtyInput = content.querySelector('input[name="specialty"]');
                    const diagnosisTextarea = content.querySelector(
                        'textarea[name="origin_diagnosis_referral_specialist"]'); // Asegurarse que sea textarea

                    const specialty = specialtyInput ? specialtyInput.value.trim() : '';
                    const origin_diagnosis_referral_specialist = diagnosisTextarea ? diagnosisTextarea.value.trim() : '';

                    fields.push({
                        value: specialty,
                        label: 'Especialidad'
                    }, {
                        value: origin_diagnosis_referral_specialist,
                        label: 'Motivo de referencia'
                    });
                }

                const invalidFields = fields.filter(field => field.value === '').map(field => field.label);

                if (invalidFields.length > 0) {
                    invalidFieldsBySection['Referencia a especialista'] = invalidFields;
                    return false; // Retornar false si hay campos inválidos
                }

                return true; // Retornar true si todo está completo
            }

            // Validación específica para INCAPACIDAD MÉDICA O LICENCIA
            let contentId = document.getElementById('medical-incapacity-or-leave');

            //guardar flag de la validación de la sección de incapacidad médica
            let isSectionValidIncapacityMedicalOrLeaveSection = false;

            //flag para saber si los campos de la sección de incapacidad médicas están vacíos o llenos (true = llenos)
            let allFieldsFilledIncapacityMedicalOrLeave = true;

            if (contentId) {
                // Validación específica para INCAPACIDAD MÉDICA O LICENCIA
                isSectionValidIncapacityMedicalOrLeaveSection = validateIncapacityMedicalOrLeaveSection(contentId);
            } else {
                console.error('El elemento de incapacidad médica.');
            }
            // Función de validación específica para la sección de INCAPACIDAD MÉDICA O LICENCIA
            function validateIncapacityMedicalOrLeaveSection(content) {
                const province = content.querySelector('input[name="province_incapacity_or_leave"]').value.trim();
                const canton = content.querySelector('input[name="canton_incapacity_or_leave"]').value.trim();
                const district = content.querySelector('input[name="district_incapacity_or_leave"]').value.trim();
                const originCase = content.querySelector('input[name="origin_case"]').value.trim();
                const disabilityType = content.querySelector('input[name="disability_type"]').value.trim();
                const attentionMode = content.querySelector('input[name="attention_mode"]').value.trim();
                const startDate = content.querySelector('input[name="start_date_of_incapacity_display_submit"]').value.trim();
                const daysOfIncapacity = content.querySelector('input[name="days_of_incapacity"]').value.trim();
                const extendedIncapacityReason = content.querySelector('textarea[name="extended_incapacity_reason"]').value
                    .trim();
                //const dxPrincipal = content.querySelector('input[name="dx_principal"]').value.trim();
                //const dxPrincipalDescription = content.querySelector('input[name="dx_principal_description"]').value.trim();
                //const dxRelated = content.querySelector('input[name="dx_related"]').value.trim();
                //const dxRelatedDescription = content.querySelector('input[name="dx_related_description"]').value.trim();
                const travelExpenses = content.querySelector('input[name="travel_expenses"]').value.trim();
                const transportation = content.querySelector('input[name="transportation"]').value.trim();
                const otherSigns = content.querySelector('textarea[name="other_signs"]').value.trim();
                const observations = content.querySelector('textarea[name="observations_explanatory_notes"]').value.trim();

                // Campos a validar
                const fields = [{
                        value: province,
                        label: 'Provincia'
                    },
                    {
                        value: canton,
                        label: 'Cantón'
                    },
                    {
                        value: district,
                        label: 'Distrito'
                    },
                    {
                        value: originCase,
                        label: 'Tipo de caso'
                    },
                    {
                        value: disabilityType,
                        label: 'Tipo de incapacidad'
                    },
                    {
                        value: attentionMode,
                        label: 'Modo de atención'
                    },
                    {
                        value: startDate,
                        label: 'Fecha de inicio de incapacidad'
                    },
                    {
                        value: daysOfIncapacity,
                        label: 'Días de incapacidad'
                    },
                    {
                        value: travelExpenses,
                        label: 'Gastos de viaje'
                    },
                    {
                        value: transportation,
                        label: 'Transporte'
                    },
                    {
                        value: otherSigns,
                        label: 'Otras señas'
                    },
                    {
                        value: observations,
                        label: 'Observaciones'
                    },
                ];



                // Verificar si todos los campos están llenos
                fields.forEach(field => {
                    if (!field.value) { // Si el campo está vacío
                        allFieldsFilledIncapacityMedicalOrLeave = false;
                    }
                });

                // validar si hay algun campo llenado
                const invalidFields = [];
                let flagAnyFilledField = false;
                fields.some(field => {
                    if (field.value !== '') {
                        flagAnyFilledField = true;
                        return true; // Esto hace que se "rompa" el ciclo
                    }
                    return false; // Continúa iterando si no se cumple la condición
                });

                if (flagAnyFilledField) {
                    // Validar si hay campos vacíos, exceptuando `extendedIncapacityReason` si `daysOfIncapacity` es mayor a 3
                    fields.forEach(field => {
                        if (field.value === '' && field.label !== 'Motivo de Incapacidad mayor a 3 días') {
                            invalidFields.push(field.label);
                        }
                    });
                }

                // Si los días de incapacidad son mayores a 3, validamos el campo de `extendedIncapacityReason`
                if (flagAnyFilledField && parseInt(daysOfIncapacity) > 3 && extendedIncapacityReason === '') {
                    invalidFields.push('Motivo de Incapacidad mayor a 3 días');
                }

                // Si hay campos inválidos, los agregamos al objeto
                if (flagAnyFilledField) {
                    invalidFieldsBySection['Incapacidad médica'] = invalidFields;
                }

                return true;
            }

            // Validación específica para ORDEN DE IMÁGENES DIAGNÓSTICAS
            let contentIdOrderDiagnosticImages = document.getElementById('order-diagnostic-images');
            if (contentIdOrderDiagnosticImages) {
                // Validación específica para ORDEN DE IMÁGENES DIAGNÓSTICAS
                const isSectionValid = validateDiagnosticImagesSection(contentIdOrderDiagnosticImages);
            } else {
                console.error('El elemento de orden de imágenes diagnósticas no se encontró.');
            }
            // Función de validación específica para la sección de ORDEN DE IMÁGENES DIAGNÓSTICAS
            function validateDiagnosticImagesSection(content) {
                const provinceField = content.querySelector('input[name="province_order_diagnostic"]').value;
                const cantonField = content.querySelector('input[name="canton_order_diagnostic"]').value;
                const districtField = content.querySelector('input[name="district_order_diagnostic"]').value;
                const originField = content.querySelector('input[name="origin_diagnosis"]').value;


                const specificFieldsDiagnosticImages = [{
                        name: 'Cód.',
                        selector: 'input[name^="diagnostics_images[cod]["]'
                    },
                    {
                        name: 'Descripción',
                        selector: 'input[name^="diagnostics_images[description]["]'
                    },
                    {
                        name: 'Cantidad',
                        selector: 'input[name^="diagnostics_images[quantity]["]'
                    },
                    {
                        name: 'Notas Aclaratorias',
                        selector: 'textarea[name^="diagnostics_images[notes]["]'
                    },
                ];

                const fields = [{
                        value: provinceField,
                        label: 'Provincia'
                    },
                    {
                        value: cantonField,
                        label: 'Cantón'
                    },
                    {
                        value: districtField,
                        label: 'Distrito'
                    },
                    {
                        value: originField,
                        label: 'Origen del diagnóstico'
                    }
                ];

                // Agregar campos específicos de imágenes diagnósticas
                const diagnosticImages = content.querySelectorAll('#diagnostic_images_content .diagnostic-image-wrapper');

                diagnosticImages.forEach(imageItem => {
                    specificFieldsDiagnosticImages.forEach(field => {
                        const inputField = imageItem.querySelector(field.selector);
                        if (inputField) {
                            fields.push({
                                value: inputField.value,
                                label: field.name
                            });
                        }
                    });
                });

                // Recorrer el array specificFieldsDiagnosticImages y obtener los valores correspondientes de cada selector
                specificFieldsDiagnosticImages.forEach((field, index) => {
                    // Buscar todos los elementos que coincidan con el selector de este campo
                    const inputFields = content.querySelectorAll(field.selector);

                    inputFields.forEach((inputField, fieldIndex) => {
                        // Si el campo existe, agregarlo a la lista de fields
                        if (inputField) {
                            fields.push({
                                value: inputField.value,
                                label: `${field.name} (Orden de imagen N° ${fieldIndex + 1})`
                            });
                        }
                    });
                });

                // Comprobar si hay algún campo con valor
                const hasValues = fields.some(field => field.value.trim() !== '');

                if (hasValues) {
                    const invalidFields = fields.filter(field => field.value.trim() === '').map(field => field.label);
                    if (invalidFields.length > 0) {
                        // Agregar campos inválidos al objeto
                        invalidFieldsBySection['Orden de imágenes diagnósticas'] = invalidFields;
                    }
                }

                return true;
            }

            // Crear el mensaje de alerta utilizando el formato requerido
            let message = Object.entries(invalidFieldsBySection)
                .filter(([accordion, fields]) => {
                    // Excluimos los campos de la sección "Atención médica"
                    return accordion !== 'Atención médica' && fields.length > 0;
                })
                .map(([accordion, fields]) => `<strong>${accordion}:</strong><br>- ${fields.join('<br>- ')}`)
                .join('<br><br>');

            if (message) {
                loadingMain(false);
                Swal.fire({
                    title: 'Campos requeridos',
                    html: `<div style="max-height: 300px; overflow-y: auto;">Por favor, completa correctamente los siguientes campos:<br><br>${message}</div>`,
                    icon: 'warning',
                    confirmButtonText: 'Aceptar',
                    confirmButtonColor: '#91c845'
                });
                return false; // Si hay campos inválidos, no se debe enviar el formulario
            } else {
                // Flag para validar que se haya seleccionado al menos un diagnóstico
                let validDiagnostics = false;

                // Contenedor donde están los diagnósticos
                let divEmpty = $('#diagnostics_empty');

                // Valores válidos para los diagnósticos
                const validValues = ['LAB', 'EST', 'PARLAB', 'DE','DAT','COM','NAT','MIX','INC','NDE'];

                function checkDiagnosticsInContainer(containerId) {
                    let container = $(containerId);
                    if (container.length > 0) {
                        container.find('input[name="diagnostics[origin][]"]').each(function () {
                            let value = $(this).val();
                            console.log(`Valor encontrado en ${containerId}:`, value);
                            if (validValues.includes(value)) {
                                validDiagnostics = true;
                            }
                        });
                    }
                }

                // Revisión principal
                if ($('#diagnostics_empty').length > 0) {
                    checkDiagnosticsInContainer('#diagnostics_empty');
                } else {
                    checkDiagnosticsInContainer('#diagnostics_1');
                    checkDiagnosticsInContainer('#diagnostics_2');
                }

                // Realizar foreach de todos los inputs origen y capturar el valor
                /*
                divEmpty.find('input[name="diagnostics[origin][]"]').each(function() {
                    let value = $(this).val();
                    console.log(value);
                    // Validar si el valor está en el array de valores válidos
                    if (validValues.includes(value)) {
                        validDiagnostics = true;
                    }
                });
                */

                // solo sale este manseje si el diagnóstico no es laboral y si en la sección de la incapacidad médica todos los campos están llenos.
                //if (!validDiagnostics && allFieldsFilledIncapacityMedicalOrLeave) {
                if (!validDiagnostics) {
                    loadingMain(false);
                    Swal.fire({
                        title: 'Cambio requerido',
                        html: `
                            <div style="max-height: 300px; overflow-y: auto;">
                                No se puede generar la incapacidad porque el diagnóstico realizado por el médico encargado es de origen "No laboral".
                            </div>
                        `,
                        icon: 'warning',
                        confirmButtonText: 'Aceptar',
                        confirmButtonColor: '#91c845'
                    });
                    return false
                }
            }

            return true; // Si todos los campos son válidos
        }



        // Definir la función para limpiar los campos provincia, cantón distrito según el acordeon que clickeo
        let clearLocationFields = function(button) {

            //encontrar el div que contiene todo los inputs
            let content = $(button).closest('.content');

            // Limpiar todos los campos tipo dropdown dentro del div content
            content.find('.ui.dropdown').each(function() {
                $(this).dropdown('clear');
            });

            // Limpiar todos los campos tipo input[text] dentro del div content
            content.find('input[type="text"]').each(function() {
                $(this).val(''); // Limpiar el valor del input
            });

            // Limpiar todos los campos tipo input[number] dentro del div content
            content.find('input[type="number"]').each(function() {
                $(this).val(''); // Limpiar el valor del input
            });

            // Limpiar todos los campos tipo textarea dentro del div content
            content.find('textarea').each(function() {
                $(this).val(''); // Limpiar el contenido del textarea
            });

            // Limpiar todos los campos tipo datepicker dentro del div content
            content.find('.datepicker').each(function() {
                $(this).pickadate('picker').clear(); // Limpiar el valor del datepicker
            });

            // Limpiar todos los campos tipo input[hidden] dentro del div content
            content.find('input[type="hidden"]').each(function() {
                $(this).val(''); // Limpiar el valor del input hidden
            });

        };
    </script>

    <!-- Formateo fecha-->
    <script>
        $(document).ready(function() {
            // Establecer el idioma español para moment.js
            moment.locale('es');

            // Obtener la fecha desde Laravel
            let createdAt = '{{ $activity->created_at }}';

            // Formatear la fecha en el formato deseado
            let createdAtFormat = moment(createdAt).format('dddd D [de] MMMM [de] YYYY');

            // Convertir la primera letra en mayúscula
            let createdAtUpp = createdAtFormat.charAt(0).toUpperCase() + createdAtFormat.slice(1);

            // Insertar la fecha formateada en el elemento con id "fecha-inicio"
            let element = document.getElementById('start_date_form');
            if(element){
                element.innerText = createdAtUpp;
            }

        });
    </script>

    <script>
        $('.medical-input-to-lower-case').each(function() {
            let value = $(this).val().toLowerCase();
            $(this).val(value.charAt(0).toUpperCase() + value.slice(1));
        });
    </script>

    <script>
        // Evento para transformar el texto del input en tiempo real
        $('.medical-input-search-to-lower-case').on('input', function() {
            let value = $(this).val().toLowerCase();
            // Capitaliza la primera letra y el resto en minúsculas
            $(this).val(value.charAt(0).toUpperCase() + value.slice(1));
        });
    </script>

    <!-- coloca los textarea auto resize -->
    <script>
        $(document).ready(function() {
            $('.auto-resize-expanded').each(function() {
                // Fija el height inicial en 37px solo desde JavaScript
                $(this).css('height', '62px');

                // Ajuste automático del height al escribir
                $(this).on('input', function() {
                    this.style.height = '62px'; // Reinicia a 37px antes de ajustar
                    this.style.height = (this.scrollHeight) + 'px'; // Ajusta según el contenido
                });
            });


            $('.auto-resize').each(function() {
                // Fija el height inicial en 37px solo desde JavaScript
                $(this).css('height', '38.21px');

                // Ajuste automático del height al escribir
                $(this).on('input', function() {
                    this.style.height = '38.21px'; // Reinicia a 37px antes de ajustar
                    this.style.height = (this.scrollHeight) + 'px'; // Ajusta según el contenido
                });
            });

        });
    </script>
    <!-- Script del Formulario de ACOMPAÑANTES -->
    <script>
        $(document).ready(function() {
            function applyValidations($fields) {
                // Validar el campo de Nombre del acompañante
                $fields.find('input[name="companions[name][]"]').on('input', function() {
                    $(this).val($(this).val().replace(/[^a-zA-ZñÑ\s]/g, ''));
                });

                // Validar el campo de Teléfono de contacto
                $fields.find('input[name="companions[phone][]"]').on('input', function() {
                    $(this).val($(this).val().replace(/[^0-9]/g, ''));
                });

                // Validar el campo de Parentesco
                $fields.find('input[name="companions[relationship][]"]').on('input', function() {
                    $(this).val($(this).val().replace(/[^a-zA-ZñÑ\s]/g, ''));
                });
            } // Aplicar validaciones a los campos iniciales
            applyValidations($('.companion-fields'));

        });
        let addCompanion = function(e) {
            let $fields = $('#companion_model').clone(true);
            $fields.removeAttr('id');
            $fields.find('a').click(function() {
                $(this).parent().parent().remove();
            });
            $('#companions_empty').append($fields);
            $fields.show();
            return false;
        };
    </script>
    <!-- Script del Formulario de  DIAGNOSTICOS -->
    <script>
        let addDiagnostic_xxxxxxx = function(e) {

            let $fields = $('#diagnostic_model').clone(false);
            $fields.removeAttr('id');
            $fields.find('input[type="text"]').val('');
            $fields.find('input[type="hidden"]').val('');
            $fields.find('.description').val('');
            $fields.find('.description_editable').val('');
            $fields.find('input[name="diagnostics[origin][]"]').val('');
            $fields.find('.ui.dropdown').dropdown('clear');
            $fields.find('input[name="diagnostics[laterality][]"]').closest('.ui.dropdown').find('.default.text').text(
                'Lateralidad');
            $fields.find('input[name="diagnostics[origin][]"]').closest('.ui.dropdown').find('.default.text').text(
                'Origen');
            $fields.find('input[name="diagnostics[diagnostic_status][]"]').closest('.ui.dropdown').find('.default.text').text(
                'Estado');    
            $fields.find('input[name="diagnostics[clasificacion][]"]').closest('.ui.dropdown').find('.default.text').text(
                'Clasificación');    

            $fields.find('a').click(function() {
                $(this).parent().parent().remove();
            });
            $fields.find('.ui.dropdown').dropdown({
                forceSelection: false
            });
            $fields.find('.ui.search.diagnostic input').change(function() {
                let valid = false;

                $(this).parents('.fields').find('input.description').val('');
                $(this).parents('.fields').find('input.description_editable').val('');

                for (let i = 0; i < cie10.length; i++) {
                    if (cie10[i].COD == $(this).val().toUpperCase()) {
                        $(this).parents('.fields').find('input.description').val(cie10[i].DESCRIPTION);
                        $(this).parents('.fields').find('input.description_editable').val(cie10[i].DESCRIPTION);
                        valid = true;
                    }
                }

                if (!valid && $(this).val() != '') {
                    $(this).val('');
                }
            });
            $fields.find('.ui.search.diagnostic').search({
                source: cie10,
                fields: {
                    title: 'COD',
                    description: 'DESCRIPTION'
                },
                searchFields: ['COD', 'DESCRIPTION'],
                regExp: {
                    escape: /[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,
                    beginsWith: ''
                },
                showNoResults: true,
                maxResults: 250,
                searchFullText: false,
                error: {
                    noResults: 'No se encontraron resultados para tu búsqueda.----'
                },
                onSelect: function(result, response) {
                    $(this).parents('.fields').find('input.description').val(result.DESCRIPTION);
                    $(this).parents('.fields').find('input.description_editable').val(result.DESCRIPTION);
                }
            });

            if ($('#diagnostics_empty').length) {
                $('#diagnostics_empty').append($fields);
            } else {
                $('#diagn' +
                    'ostics_1').append($fields);
            }
            //$('#diagnostics_empty').append($fields);
            $fields.show();
            return false;
        };
    </script>
    <script>
        let addDiagnostic = function(e) {
            let $fields = $('#diagnostic_model').clone(false);
            $fields.removeAttr('id');
            $fields.find('input[type="text"]').val('');
            $fields.find('input[type="hidden"]').val('');
            $fields.find('.description').val('');
            $fields.find('.description_editable').val('');
            $fields.find('input[name="diagnostics[origin][]"]').val('');

            // Limpiar textos por defecto de los dropdowns
            $fields.find('input[name="diagnostics[laterality][]"]').closest('.ui.dropdown').find('.default.text').text('Lateralidad');
            $fields.find('input[name="diagnostics[origin][]"]').closest('.ui.dropdown').find('.default.text').text('Origen');
            $fields.find('input[name="diagnostics[diagnostic_status][]"]').closest('.ui.dropdown').find('.default.text').text('Estado');
            $fields.find('input[name="diagnostics[clasificacion][]"]').closest('.ui.dropdown').find('.default.text').text('Clasificación');

            // Botón de eliminar
            $fields.find('a').click(function() {
                $(this).parent().parent().remove();
            });

            // Inicializar todos los dropdowns
            $fields.find('.ui.dropdown').dropdown({ forceSelection: false });

            // === DROPDOWN de Códigos Diagnóstico ===
            let $dropdownCod = $fields.find('#diagnosticsCodDropdown');
            let $menuCod = $dropdownCod.find('.menu');
            $menuCod.html('<div class="item" data-value="">Seleccione un código</div>'); // Limpiar y placeholder

            // Agregar los códigos CIE10
            cie10.forEach(function(item) {
                $menuCod.append('<div class="item" data-value="' + item.COD + '">' + item.COD + ' - ' + item.DESCRIPTION + '</div>');
            });

            // Inicializar el dropdown con comportamiento
            $dropdownCod.dropdown({
                fullTextSearch: true,
                forceSelection: false,
                apiSettings: {
                    responseAsync: function (settings, callback) {
                        const query = settings.urlData.query.toLowerCase();
                        const filtered = cie10
                            .filter(item => item.SEARCH_TEXT.includes(query))
                            .slice(0, 10);
                        const results = filtered.map(item => ({
                            name: item.COD + ' - ' + item.DESCRIPTION,
                            value: item.COD
                        }));
                        callback({ success: true, results });
                    }
                },
                onChange: function(value, text, $selectedItem) {
                    let diagnostic = cie10.find(function(d) {
                        return d.COD === value;
                    });

                    if (diagnostic) {
                        $fields.find('input[name="diagnostics[description][]"]').val(diagnostic.DESCRIPTION);
                        $fields.find('input[name="diagnostics[description_editable][]"]').val(diagnostic.DESCRIPTION);
                    } else {
                        $fields.find('input[name="diagnostics[description][]"]').val('');
                        $fields.find('input[name="diagnostics[description_editable][]"]').val('');
                    }
                }
            });

            // Agregar el bloque clonado al DOM
            if ($('#diagnostics_empty').length) {
                $('#diagnostics_empty').append($fields);
            } else {
                $('#diagnostics_1').append($fields);
            }

            $fields.show();
            return false;
        };
    </script>

<script>
    
    function addDiagnostic1_xxx() {
        let $fields = $('#diagnostic_model').clone(false);
        //console.log($fields);

        $fields.removeAttr('id');
        $fields.find('input[type="text"]').val('');
        $fields.find('input[type="hidden"]').val('');
        $fields.find('.description').val('');
        $fields.find('.description_editable').val('');
        $fields.find('input[name="diagnostics[origin][]"]').val('');
        $fields.find('.ui.dropdown').dropdown('clear');
        $fields.find('input[name="diagnostics[laterality][]"]').closest('.ui.dropdown').find('.default.text').text('Lateralidad');
        $fields.find('input[name="diagnostics[origin][]"]').closest('.ui.dropdown').find('.default.text').text('Origen');
        $fields.find('input[name="diagnostics[diagnostic_status][]"]').closest('.ui.dropdown').find('.default.text').text('Estado');
        $fields.find('input[name="diagnostics[clasificacion][]"]').closest('.ui.dropdown').find('.default.text').text('Clasificación');

        $fields.find('a').click(function() {
            $(this).closest('.fields').remove();
        });

        $fields.find('.ui.dropdown').dropdown({
            forceSelection: false
        });

        $fields.find('.ui.search.diagnostic input').change(function () {
            let valid = false;
            const $parent = $(this).closest('.fields');

            $parent.find('input.description').val('');
            $parent.find('input.description_editable').val('');

            for (let i = 0; i < cie10.length; i++) {
                if (cie10[i].COD === $(this).val().toUpperCase()) {
                    $parent.find('input.description').val(cie10[i].DESCRIPTION);
                    $parent.find('input.description_editable').val(cie10[i].DESCRIPTION);
                    valid = true;
                    break;
                }
            }

            if (!valid && $(this).val() !== '') {
                $(this).val('');
            }
        });

        $fields.find('.ui.search.diagnostic').search({
            source: cie10,
            fields: {
                title: 'COD',
                description: 'DESCRIPTION'
            },
            searchFields: ['COD', 'DESCRIPTION'],
            regExp: {
                escape: /[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,
                beginsWith: ''
            },
            showNoResults: true,
            maxResults: 250,
            searchFullText: false,
            error: {
                noResults: 'No se encontraron resultados para tu búsqueda.....'
            },
            onSelect: function(result) {
                const $parent = $(this).closest('.fields');
                $parent.find('input.description').val(result.DESCRIPTION);
                $parent.find('input.description_editable').val(result.DESCRIPTION);
            }
        });

        //$fields.show(); // MOSTRAR ANTES DE INSERTAR
        //console.log($fields);
        $('#diagnostics_2').append($fields);
        $fields.show();
        return false;
    };

    function addDiagnostic1() {
        let $fields = $('#diagnostic_model').clone(false);
        $fields.removeAttr('id');

        // Limpieza de valores
        $fields.find('input[type="text"], input[type="hidden"]').val('');
        $fields.find('.description, .description_editable').val('');

        $fields.find('.ui.dropdown').dropdown('clear');

        $fields.find('input[name="diagnostics[laterality][]"]').closest('.ui.dropdown').find('.default.text').text('Lateralidad');
        $fields.find('input[name="diagnostics[origin][]"]').closest('.ui.dropdown').find('.default.text').text('Origen');
        $fields.find('input[name="diagnostics[diagnostic_status][]"]').closest('.ui.dropdown').find('.default.text').text('Estado');
        $fields.find('input[name="diagnostics[clasificacion][]"]').closest('.ui.dropdown').find('.default.text').text('Clasificación');

        const $codDropdownw = $fields.find('.ui.search.selection.dropdown').first();
        const $menuw = $codDropdownw.find('.menu');
        $menuw.empty(); // Limpia las opciones anteriores

        cie10.slice(0, 10).forEach(item => {
            $menuw.append(`<div class="item" data-value="${item.COD}">${item.COD} - ${item.DESCRIPTION}</div>`);
        });

        // Botón para eliminar bloque
        $fields.find('a').click(function() {
            $(this).closest('.fields').remove();
        });

        // Inicializa los dropdowns
        $fields.find('.ui.dropdown').dropdown({
            forceSelection: false
        });

        // Rellenar dinámicamente el dropdown del código con cie10
        const $codDropdown = $fields.find('.ui.search.selection.dropdown').first();
        const $menu = $codDropdown.find('.menu');
        $menu.empty(); // Limpia las opciones anteriores

        cie10.forEach(item => {
            $menu.append(`<div class="item" data-value="${item.COD}">${item.COD} - ${item.DESCRIPTION}</div>`);
        });

        // Re-inicializa el dropdown con búsqueda
        $codDropdown.dropdown({
            fullTextSearch: true,
            forceSelection: false,
            apiSettings: {
                responseAsync: function (settings, callback) {
                    const query = settings.urlData.query.toLowerCase();
                    const filtered = cie10
                        .filter(item => item.SEARCH_TEXT.includes(query))
                        .slice(0, 10);
                    const results = filtered.map(item => ({
                        name: item.COD + ' - ' + item.DESCRIPTION,
                        value: item.COD
                    }));
                    callback({ success: true, results });
                }
            },
            onChange: function (value, text, $selectedItem) {
                const $parent = $(this).closest('.fields');
                const selected = cie10.find(c => c.COD === value);
                if (selected) {
                    $parent.find('input.description').val(selected.DESCRIPTION);
                    $parent.find('input.description_editable').val(selected.DESCRIPTION);
                } else {
                    $parent.find('input.description').val('');
                    $parent.find('input.description_editable').val('');
                }
            }
        });

        $('#diagnostics_2').append($fields);
        $fields.show();
        return false;
    }


</script>





@push('scripts')
<script>
    $(document).ready(function() {
    function updateDiagnosticMirror() {
        let diagnostics = [];
       

    
        $('#diagnostics_2 .fields.diagnostic-fields_new').each(function() {
      console.log("ingreso");
      
      console.log(diagnostics);
            let $fields = $(this);
            //console.log($fields.find('[name="diagnostics[cod][]"]').val());
            let diagnostic = {
                cod: $fields.find('[name="diagnostics[cod][]"]').val(),
                description: $fields.find('[name="diagnostics[description][]"]').val(),
                laterality: $fields.find('[name="diagnostics[laterality][]"]').val(),
                origin: $fields.find('[name="diagnostics[origin][]"]').val(),
                diagnostic_status: $fields.find('[name="diagnostics[diagnostic_status][]"]').val(),
                clasificacion: $fields.find('[name="diagnostics[clasificacion][]"]').val()
            };
           

            if (Object.values(diagnostic).every(Boolean)) {
                if(diagnostic.diagnostic_status !== 'R'){
                    diagnostics.push(diagnostic);
                }
                
            }
           
        });
   
        $('div.valor').each(function () {

            let $fields = $(this);

            let diagnostico = {
                cod: $fields.find('[name="diagnostics[cod][]"]').val(),
                description: $fields.find('[name="diagnostics[description][]"]').val(),
                laterality: $fields.find('[name="diagnostics[laterality][]"]').val(),
                origin: $fields.find('[name="diagnostics[origin][]"]').val(),
                diagnostic_status: $fields.find('[name="diagnostics[diagnostic_status][]"]').val(),
                clasificacion: $fields.find('[name="diagnostics[clasificacion][]"]').val()
            };
         

            if (Object.values(diagnostico).every(Boolean)) {
          
                if(diagnostics.length === 0){
                    if (diagnostico.diagnostic_status !== 'R') {
                        diagnostics.push(diagnostico);
                    }

                }else{
                    diagnostics.forEach(function(diagnostic) {

                        if(diagnostic.cod !== diagnostico.cod){
                            if(diagnostico.diagnostic_status !== 'R'){
                                diagnostics.push(diagnostico);
                            }

                        }

                    });
                }

            }


           
        });
      
        const uniqueDiagnostics = diagnostics.filter((item, index, self) =>
            index === self.findIndex(d => d.cod === item.cod)
        );
        //console.log(uniqueDiagnostics);

        renderDiagnosticMirror(uniqueDiagnostics);
    }

    function renderDiagnosticMirror(diagnostics) {
        const $mirrorContainer = $('.diagnostic-mirror-container');
        const $mirrorContent = $('.diagnostic-mirror');

        if (diagnostics.length > 0 && $mirrorContent.length) {
            let html = '';
            diagnostics.forEach(diag => {
            const originDiagnosis = @json($ORIGIN_DIAGNOSIS_UPDATED);
            const diagnosisStatus = @json($DIAGNOSTIC_STATUS);
            const clasificacion = @json($CLASIFICACION);
            const latelarity = @json($LATERALITY);

            for (const [key, value] of Object.entries(originDiagnosis)) {
                if(diag.origin == key){
                    diag.origin =value;
                }
                // Aquí puedes usar key y value como necesites
            }
            
            for (const [key, value] of Object.entries(diagnosisStatus)) {
                if(diag.diagnostic_status == key){
                    diag.diagnostic_status =value;
                }
                // Aquí puedes usar key y value como necesites
            }
            
            for (const [key, value] of Object.entries(clasificacion)) {
                if(diag.clasificacion == key){
                    diag.clasificacion =value;
                }
                // Aquí puedes usar key y value como necesites
            }

            for (const [key, value] of Object.entries(latelarity)) {
                if(diag.laterality == key){
                    diag.laterality =value;
                }
                // Aquí puedes usar key y value como necesites
            }


                html += `
                    <div class="fields mirror-view">
                        <div class="eight wide field">
                            <label>Código</label>
                            <div class="ui fluid input readonly">
                                <input type="text" value="${diag.cod}" readonly>
                            </div>
                        </div>
                        <div class="eight wide field">
                            <label>Nombre</label>
                            <div class="ui fluid input readonly">
                                <input type="text" value="${diag.description}" readonly>
                            </div>
                        </div>
                         <div class="eight wide field ">
                            <label>Descripción</label>
                            <input type="text" 
                                   value="${diag.description}" readonly  >
                        </div>
                        <div class="four wide field">
                            <label>Lateralidad</label>
                            <div class="ui fluid input readonly">
                                <input type="text" value="${diag.laterality}" readonly>
                            </div>
                        </div>

                         <div class="five wide field">
                            <label>Origen</label>
                            <div class="ui fluid input readonly">
                                <input type="text" value="${diag.origin}" readonly>
                            </div>
                        </div>
                         <div class="five wide field">
                            <label>Estado</label>
                            <div class="ui fluid input readonly">
                                <input type="text" value="${diag.diagnostic_status}" readonly>
                            </div>
                        </div>
                         <div class="five wide field">
                            <label>Clasificación</label>
                            <div class="ui fluid input readonly">
                                <input type="text" value="${diag.clasificacion}" readonly>
                            </div>
                        </div>
                    </div>

                `;
            });
            $mirrorContent.empty().html(html);
            $mirrorContainer.show();
            $('.diagnostic-mirror').html(html);
            console.log($mirrorContainer.html);
        
        } else if ($mirrorContent.length) {
            
            $mirrorContent.empty();
            //$mirrorContainer.hide();
            //$mirrorContent.html('<div class="ui message">Complete los diagnósticos</div>');
        }else{
            console.log("");
        }
        //console.log($mirrorContent);
        //$('#medical-incapacity-diagnostics').val(JSON.stringify(diagnostics));
    }

    // Ejecutar en cada cambio de diagnóstico
    //$(document).on('input change', '.diagnostic-fields input, .diagnostic-fields select', updateDiagnosticMirror);

    // Cuando se expanda un acordeón (usando Semantic UI)
 
    $('.ui.accordion').accordion({
    exclusive: true,
    onOpening: function () {
        const $section = $(this);
        const $mirrorContainer = $section.find('.diagnostic-mirror-container');

        if ($mirrorContainer.length > 0) {
            const followUpId = $mirrorContainer.data('followup-id');
            // Deja que el acordeón termine de abrirse antes de actualizar el DOM
            setTimeout(() => {
                updateDiagnosticMirror();
            }, 300); // Ajusta si la animación es más lenta
        }
    }
});

    // Ejecutar al inicio si ya están visibles
    //updateDiagnosticMirror();
});
</script>
@endpush







    <!-- Asigna el valor origen de la pestaña "diagnostico" al tipo de origen de la pestaña "incapacidad medica" -->
    <script>
        $(document).ready(function() {
            // Inicializa ambos dropdowns
            $('#dropdownOrigen').dropdown();
            $('#dropdownTipoCaso').dropdown();

            // Variable de control para asegurarse de que solo se ejecute una vez
            let alreadySet = false;

            // Detectar cambios en el primer dropdown
            $('#dropdownOrigen').on('change', function() {

                //Capturar valor tipo de caso
                const valueTypeCase = $('#dropdownTipoCaso').dropdown('get value');

                // Solo ejecutar si no se ha realizado el cambio previamente
                if (!alreadySet && valueTypeCase === '') {
                    const value = $(this).dropdown('get value');

                    // Actualizar el segundo dropdown con el valor seleccionado
                    $('#dropdownTipoCaso').dropdown('set selected', value);

                    // Marcar como realizado para que no se repita
                    alreadySet = true;
                }
            });
        });
    </script>
@endsection
