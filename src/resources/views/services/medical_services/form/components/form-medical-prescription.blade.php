<div class="title">
    <i class="dropdown icon"></i>
    Receta médica
    @if(!$disabled)
        <i class="info circle icon info_receta_medica"></i>
    @endif
</div>
<div class="content" id="form-medical-prescription">
    <!-- Primera línea con 3 search -->
    <div class="location-group" data-group-id="6">
        <div class="field">
            <div class="four fields">
                <div class="field required">
                    <label>Provincia</label>
                    <div class="province ui search selection dropdown @if($disabled) disabled @endif">
                        <input type="hidden" name="province_medical_prescription"
                               class="minus_font province_value"
                               value="{{$medical_service->province_medical_prescription ?? ''}}">
                        <i class="dropdown icon"></i>
                        <div class="default text">Selecciona uno</div>
                        <div class="menu"></div>
                    </div>
                </div>
                <div class="field required">
                    <label>Cantón</label>
                    <div class="canton ui search selection dropdown @if($disabled) disabled @endif">
                        <input type="hidden" name="canton_medical_prescription" class="minus_font canton_value"
                               value="{{$medical_service->canton_medical_prescription ?? ''}}">
                        <i class="dropdown icon"></i>
                        <div class="default text">Selecciona uno</div>
                        <div class="menu"></div>
                    </div>
                </div>
                <div class="field required">
                    <label>Distrito</label>
                    <div class="district ui search selection dropdown @if($disabled) disabled @endif">
                        <input type="hidden" name="district_medical_prescription"
                               class="minus_font district_value"
                               value="{{$medical_service->district_medical_prescription ?? ''}}">
                        <i class="dropdown icon"></i>
                        <div class="default text">Selecciona uno</div>
                        <div class="menu"></div>
                    </div>
                </div>
                @if($disabled !== true)
{{--                    <div class="field">--}}
{{--                        <label>Farmacias</label>--}}
{{--                        <div class="ui search farmacias2">--}}
{{--                            <div class="ui icon input">--}}
{{--                                <input--}}
{{--                                        class="prompt minus_font"--}}
{{--                                        type="text" name="pharmacies_branch_prescription"--}}
{{--                                        value="{{ $medical_service->pharmacies_branch_prescription ?? '' }}"--}}
{{--                                        placeholder="Buscar por código o sucursal">--}}
{{--                                <i class="search icon"></i>--}}
{{--                            </div>--}}
{{--                            <div class="results"></div>--}}
{{--                        </div>--}}
{{--                    </div>--}}

                    <div class="field">
                        <label>Farmacias</label>
                        <div class="ui search selection dropdown farmaciasDosCodDropdown" id="farmaciasDosCodDropdown">
                            <input type="hidden" name="pharmacies_branch_prescription" value="{{ $medical_service->pharmacies_branch_prescription ?? ''  }}">
                            <i class="dropdown icon"></i>
                            <div class="default text">Buscar por código o sucursal</div>
                            <div class="menu ">
                                <div class="item" data-value=""> </div>
                            </div>
                        </div>
                    </div>
                @else
                    <div class="field">
                        <label>Farmacias</label>
                        <div class="ui search">
                            <div class="ui input">
                                <input class="prompt readonly" type="text" value="{{ $medical_service->pharmacies_branch_prescription ?? '' }}" readonly>
                            </div>
                        </div>
                    </div>
                @endif
                @if($disabled !== true)
                    <!-- Botón de limpiar -->
                    <div class="wide two field" style="margin-top: 25px;">
                        <button id="clear-location-button" class="ui red small icon fluid button" type="button"
                                onclick="clearLocationFields(this)">
                            <i class="undo icon"></i>
                            Limpiar
                        </button>
                    </div>
                @endif

            </div>
        </div>
    </div>

    <div class="fields">
        <div class="sixteen wide field required">
            <div class="field required">
                <label for="diagnosis-origin-2-1">Origen de los diagnósticos </label>
                <textarea class="@if(!empty($disabled)) textarea-expand @else auto-resize-expanded @endif" name="{{!empty($disabled) ? '' : 'diagnosis_origin_prescription' }}" id="diagnosis_origin-2-1"
                          placeholder=""
                          @if($disabled) readonly @else rows="1" @endif>{{$medical_service->diagnosis_origin_prescription ?? ''}}</textarea>
            </div>
        </div>
    </div>
    <div class="ui grid" id="medical-form-container-2" style="margin-top: 15px;">
        <!-- Contenedor dinámico de los medicamentos -->
        <div class="content" id="medicine-list-2_{{ $medical_prescriptions ? 1 : 'empty' }}">
            <!-- Formulario inicial-->
            @if(count($medical_prescriptions) > 0)
                @foreach($medical_prescriptions as $index => $medicine)
                    <div class="ui grid medicine-item">
                        <div class="two column row" style="margin-top: 15px;">
                            <div class="column">
                                <h4 class="medicine-title">Medicamento N° {{ $index + 1 }}</h4>
                            </div>
                            @if($index > 0 && $disabled !== true)
                                <div class="column right aligned">
                                    <a class="ui red small icon basic right floated button remove-medicine2"
                                       style="margin-top: 5px;">
                                        <i class="remove icon"></i>
                                    </a>
                                </div>
                            @endif
                        </div>
                        @if(!empty($medicine->generic_code) && !empty($medicine->generic_name) && !empty($medicine->concentration) && !empty($medicine->pharmaceutical_form))
                            <div class="four column row">
                                <div class="column">
                                    <div class="ui form">
                                        <div class="field required">
                                            <label>Código genérico</label>
                                            <div class="ui search genericCodeInput2">
                                                <div class="ui icon input">
                                                    <input id="generic-code-2[{{ $index + 1 }}]"
                                                           type="text" name="generic-code-2[{{ $index + 1 }}]"
                                                           value="{{ $medicine->generic_code }}" @if($disabled) disabled @endif>
                                                    <i class="search icon"></i>
                                                </div>
                                                <div class="results"></div>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                                <div class="column">
                                    <div class="field required">
                                        <label>Nombre genérico</label>
                                        <textarea class="auto-resize" id="generic-name-2[{{ $index + 1 }}]" name="generic-name-2[{{ $index + 1 }}]" rows="5"
                                                  readonly rows="1" @if($disabled) disabled @endif>{{ $medicine->generic_name }}</textarea>
                                    </div>
                                </div>
                                <div class="column">
                                    <div class="ui form">
                                        <div class="field required">
                                            <label for="concentration-2">Concentración</label>
                                            <textarea class="auto-resize" name="concentration-2[{{ $index + 1 }}]" id="concentration-2"
                                                      placeholder=""
                                                      readonly rows="1" @if($disabled) disabled @endif>{{ $medicine->concentration }}</textarea>
                                        </div>
                                    </div>
                                </div>
                                <div class="column">
                                    <div class="ui form">
                                        <div class="field required">
                                            <label for="forma-farmaceutica-2-1">Forma farmacéutica</label>
                                            <textarea class="auto-resize" name="pharmaceutical-form-2[{{ $index + 1 }}]"
                                                      id="pharmaceutical-form-2-1" placeholder=""
                                                      readonly rows="1" @if($disabled) disabled @endif>{{ $medicine->pharmaceutical_form }}</textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="two column row">
                                <div class="column">
                                    <div class="ui form">
                                        <div class="field required">
                                            <label for="duracion-tratamiento-2-1">Duración del tratamiento</label>
                                            <textarea class="auto-resize" name="duracion_tratamiento2[{{ $index + 1 }}]"
                                                      id="duracion-tratamiento-2-1"
                                                      placeholder="" rows="1" @if($disabled) disabled @endif>{{ $medicine->treatment_duration }}</textarea>
                                        </div>
                                    </div>
                                </div>
                                <div class="column">
                                    <div class="ui form">
                                        <div class="field required">
                                            <label for="frecuencia-2-1">Frecuencia</label>
                                            <textarea class="auto-resize" name="frecuencia2[{{ $index + 1 }}]" id="frecuencia-2-1"
                                                      placeholder="" rows="1" @if($disabled) disabled @endif>{{ $medicine->frequency }}</textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="two column row">
                                <div class="column">
                                    <div class="ui form">
                                        <div class="field required">
                                            <label for="dosis-2-1">Dosis/ vía de administración</label>
                                            <textarea class="auto-resize" name="dosis2[{{ $index + 1 }}]" id="dosis-2-1"
                                                      placeholder="" rows="1" @if($disabled) disabled @endif>{{ $medicine->dosage }}</textarea>
                                        </div>
                                    </div>
                                </div>
                                <div class="column">
                                    <div class="ui form">
                                        <div class="field required">
                                            <label for="cantidad-letras-2-1">Cantidad prescrita letras</label>
                                            <textarea class="auto-resize" name="cantidad-letras2[{{ $index + 1 }}]"
                                                      id="cantidad-letras-2-1"
                                                      placeholder="" rows="1" @if($disabled) disabled @endif>{{ $medicine->quantity_letters }}</textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="two column row">
                                <div class="column">
                                    <div class="ui form">
                                        <div class="field required">
                                            <label for="cantidad-numeros-2-1">Cantidad prescrita números</label>
                                            <textarea class="auto-resize" name="cantidad-numeros2[{{ $index + 1 }}]"
                                                      id="cantidad-numeros-2-1"
                                                      placeholder="" rows="1" @if($disabled) disabled @endif>{{ $medicine->quantity_numbers }}</textarea>
                                        </div>
                                    </div>
                                </div>
                                <div class="column">
                                    <div class="ui form">
                                        <div class="field required">
                                            <label for="notas-2-1">Notas aclaratorias</label>
                                            <textarea class="auto-resize" name="notas2[{{ $index + 1 }}]" id="notas-2-1"
                                                      placeholder="" rows="1" @if($disabled) disabled @endif>{{ $medicine->notes }}</textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @else
                            <div class="three column row">
                                <!-- Campo de moleculas de vademecum -->
                                <div class="column">
                                    <div class="ui form">
                                        <div class="field">
                                            <label>Mólecula</label>
                                            <input type="text" class="readonly"
                                                   value="{{$medicine->molecula ?? ''}}" readonly>
                                        </div>
                                    </div>
                                </div>
                                <!-- Campo para el tipo -->
                                <div class="column">
                                    <div class="ui form">
                                        <div class="field">
                                            <label>Tipo</label>
                                            <input type="text" name="number_electronic_invoice" class="readonly"
                                                   value="{{$medicine->tipo ?? ''}}" readonly>
                                        </div>
                                    </div>
                                </div>
                                <!-- Campo para la descripción (descripVademecum) con la estructura de search -->
                                <div class="column wide ">
                                    <div class="ui form">
                                        <div class="field">
                                            <label>Descripción</label>
                                            <input type="text" class="readonly"
                                                   value="{{$medicine->descrip ?? ''}}" readonly>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="three column row">
                                <!-- Campo código de vademecum-->
                                <div class="column">
                                    <div class="field">
                                        <label>Código</label>
                                        <textarea class="textarea-expand"
                                                  readonly>{{$medicine->codigo ?? ''}}</textarea>
                                    </div>
                                </div>
                                <!-- Campo casa de vademecum-->
                                <div class="column">
                                    <div class="field">
                                        <label>Casa</label>
                                        <textarea class="textarea-expand"
                                                  readonly>{{$medicine->casa ?? ''}}</textarea>
                                    </div>
                                </div>

                                <div class="column">
                                    <div class="ui form">
                                        <div class="field">
                                            <label>Duración del tratamiento</label>
                                            <textarea class="textarea-expand" readonly>{{$medicine->treatment_duration ?? ''}}</textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="three column row">
                                <div class="column">
                                    <div class="ui form">
                                        <div class="field">
                                            <label>Frecuencia</label>
                                            <textarea class="textarea-expand" readonly>{{$medicine->frequency ?? ''}}</textarea>
                                        </div>
                                    </div>
                                </div>
                                <div class="column">
                                    <div class="ui form">
                                        <div class="field">
                                            <label>Dosis/ vía de administración</label>
                                            <textarea class="textarea-expand" readonly>{{$medicine->dosage ?? ''}}</textarea>
                                        </div>
                                    </div>
                                </div>
                                <div class="column">
                                    <div class="ui form">
                                        <div class="field">
                                            <label>Cantidad prescrita letras</label>
                                            <textarea class="textarea-expand" readonly>{{$medicine->quantity_letters ?? ''}}</textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="three column row">
                                <div class="column">
                                    <div class="ui form">
                                        <div class="field">
                                            <label>Cantidad prescrita números</label>
                                            <textarea class="textarea-expand" readonly>{{$medicine->quantity_numbers ?? ''}}</textarea>
                                        </div>
                                    </div>
                                </div>
                                <div class="column">
                                    <div class="ui form">
                                        <div class="field">
                                            <label>Notas aclaratorias</label>
                                            <textarea class="textarea-expand" readonly>{{$medicine->notes ?? ''}}</textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endif
                    </div>
                @endforeach
            @elseif($disabled !== true)
                <div class="ui grid medicine-item vademecum-group" data-group-id="medical-prescription-1">
                    <!-- Primera línea: Etiqueta Medicamento N° -->
                    <div class="two column row">
                        <div class="column">
                            <h4 class="medicine-title">Medicamento N° 1</h4>
                        </div>
                    </div>
                    <div class="three column row">
                        <!-- Campo de moleculas de vademecum -->
                        <div class="column">
                            <div class="ui form">
                                <div class="required field">
                                    <label>Mólecula</label>
                                    <div class="moleculaVademecum ui search selection dropdown">
                                        <input type="hidden" class="minus_font moleculaVademecum_value" name="molecula-prescription2[1]" value=""
                                               onchange="handleMoleculaChange($(this).closest('.vademecum-group'))">
                                        <i class="dropdown icon"></i>
                                        <div class="default text">Selecciona una molécula</div>
                                        <div class="menu"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Campo para el tipo -->
                        <div class="column">
                            <div class="ui form">
                                <div class="required field">
                                    <label>Tipo</label>
                                    <div class="tipoVademecum ui search selection dropdown">
                                        <input type="hidden" class="minus_font tipoVademecum_value" value="" name="tipo-prescription2[1]"
                                               onchange="handleTipoChange($(this).closest('.vademecum-group'))">
                                        <i class="dropdown icon"></i>
                                        <div class="default text">Selecciona un tipo</div>
                                        <div class="menu"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Campo para la descripción (descripVademecum) con la estructura de search -->
{{--                        <div class="column wide ">--}}
{{--                            <div class="ui form">--}}
{{--                                <div class="required field">--}}
{{--                                    <label>Descripción +++</label>--}}
{{--                                    <div class="ui search search descripVademecum">--}}
{{--                                        <div class="ui icon input">--}}
{{--                                            <input id="descrip-prescription-[1]"--}}
{{--                                                   class="prompt minus_font"--}}
{{--                                                   type="text" name="descrip-prescription2[1]"--}}
{{--                                                   value="">--}}
{{--                                            <i class="search icon"></i>--}}
{{--                                        </div>--}}
{{--                                        <div class="results"></div>--}}
{{--                                    </div>--}}
{{--                                </div>--}}
{{--                            </div>--}}
{{--                        </div>--}}

                        <div class="column wide ">
                            <div class="ui form">
                                <div class="required field">
                                    <label>Descripción</label>
                                    <div class="ui search selection dropdown descripVademecumDropdown" id="descripVademecumDropdown">
                                        <input type="hidden" name="descrip-prescription2[1]" >
                                        <i class="dropdown icon"></i>
                                        <div class="default text"></div>
                                        <div class="menu ">
                                            <div class="item" data-value=""> </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                    <div class="three column row">
                        <!-- Campo código de vademecum-->
                        <div class="column">
                            <div class="required field">
                                <label>Código</label>
                                <textarea class="auto-resize readonly"
                                          name="codigo-vademecum2[1]" rows="1"
                                          readonly></textarea>
                            </div>
                        </div>
                        <!-- Campo casa de vademecum-->
                        <div class="column">
                            <div class="required field">
                                <label>Casa</label>
                                <textarea class="auto-resize readonly"
                                          name="casa-vademecum2[1]" rows="1"
                                          readonly></textarea>
                            </div>
                        </div>

                        <div class="column">
                            <div class="ui form">
                                <div class="required field">
                                    <label>Duración del tratamiento</label>
                                    <textarea class="auto-resize" name="duracion_tratamiento2[1]"
                                              placeholder="" rows="1"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="three column row">
                        <div class="column">
                            <div class="ui form">
                                <div class="required field">
                                    <label>Frecuencia</label>
                                    <textarea class="auto-resize" name="frecuencia2[1]"
                                              placeholder="" rows="1"></textarea>
                                </div>
                            </div>
                        </div>
                        <div class="column">
                            <div class="ui form">
                                <div class="required field">
                                    <label>Dosis/ vía de administración</label>
                                    <textarea class="auto-resize" name="dosis2[1]"
                                              placeholder="" rows="1"></textarea>
                                </div>
                            </div>
                        </div>
                        <div class="column">
                            <div class="ui form">
                                <div class="required field">
                                    <label>Cantidad prescrita letras</label>
                                    <textarea class="auto-resize" name="cantidad-letras2[1]"
                                              placeholder="" rows="1"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="three column row">
                        <div class="column">
                            <div class="ui form">
                                <div class="required field">
                                    <label>Cantidad prescrita números</label>
                                    <textarea class="auto-resize" name="cantidad-numeros2[1]"
                                              placeholder="" rows="1"></textarea>
                                </div>
                            </div>
                        </div>
                        <div class="column">
                            <div class="ui form">
                                <div class="required field">
                                    <label>Notas aclaratorias</label>
                                    <textarea class="auto-resize" name="notas2[1]"
                                              placeholder="" rows="1"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </div>
    @if($disabled !== true)
        <div class="fields">
            <div class="field two wide required" style="margin-top: 25px;">
                <a style="margin-top: 0;" onclick="addMedicineSecondBlade()"
                   class="ui basic small icon blue fluid button">
                    <i class="add icon"></i>Agregar
                </a>
            </div>
        </div>
    @endif
</div>
@if(!$disabled)
    <!-- Template oculto para duplicar -->
    <div class="ui grid medicine-item" data-group-id="" id="medicine-template-2" style="display: none;">

        <!-- Primera línea: Etiqueta Medicamento N° y botón Eliminar -->
        <div class="two column row" style="margin-top: 15px;">
            <div class="column">
                <h4 class="medicine-title">Medicamento N° <span class="medicine-number"></span></h4>
            </div>
            <div class="column right aligned">
                <a class="ui red small icon basic right floated button remove-medicine2" style="margin-top: 5px;">
                    <i class="remove icon"></i>
                </a>
            </div>
        </div>
        <div class="three column row">
            <!-- Campo de moleculas de vademecum -->
            <div class="column">
                <div class="ui form">
                    <div class="required field">
                        <label>Mólecula</label>
                        <div class="moleculaVademecum ui search selection dropdown">
                            <input type="hidden" class="minus_font moleculaVademecum_value" name="molecula-prescription2[]" value=""
                                   onchange="handleMoleculaChange($(this).closest('.vademecum-group'))">
                            <i class="dropdown icon"></i>
                            <div class="default text">Selecciona una molécula</div>
                            <div class="menu"></div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Campo para el tipo -->
            <div class="column">
                <div class="ui form">
                    <div class="required field">
                        <label>Tipo</label>
                        <div class="tipoVademecum ui search selection dropdown">
                            <input type="hidden" class="minus_font tipoVademecum_value" value="" name="tipo-prescription2[]"
                                   onchange="handleTipoChange($(this).closest('.vademecum-group'))">
                            <i class="dropdown icon"></i>
                            <div class="default text">Selecciona un tipo</div>
                            <div class="menu"></div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Campo para la descripción (descripVademecum) con la estructura de search -->
{{--            <div class="column wide ">--}}
{{--                <div class="ui form">--}}
{{--                    <div class="required field">--}}
{{--                        <label>Descripción 888</label>--}}
{{--                        <div class="ui search search descripVademecum">--}}
{{--                            <div class="ui icon input">--}}
{{--                                <input id="descrip-prescription-[]"--}}
{{--                                       class="prompt minus_font"--}}
{{--                                       type="text" name="descrip-prescription2[]"--}}
{{--                                       value="">--}}
{{--                                <i class="search icon"></i>--}}
{{--                            </div>--}}
{{--                            <div class="results"></div>--}}
{{--                        </div>--}}
{{--                    </div>--}}
{{--                </div>--}}
{{--            </div>--}}

            <div class="column wide ">
                <div class="ui form">
                    <div class="required field">
                        <label>Descripción</label>
                        <div class="ui search selection dropdown descripVademecumDropdown" id="descripVademecumDropdown">
                            <input type="hidden"  id="descrip-prescription-[]" name="descrip-prescription2[]" >
                            <i class="dropdown icon"></i>
                            <div class="default text"></div>
                            <div class="menu ">
                                <div class="item" data-value=""> </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
        <div class="three column row">
            <!-- Campo código de vademecum-->
            <div class="column">
                <div class="required field">
                    <label>Código</label>
                    <textarea class="auto-resize readonly"
                              name="codigo-vademecum2[]" rows="1"
                              readonly></textarea>
                </div>
            </div>
            <!-- Campo casa de vademecum-->
            <div class="column">
                <div class="required field">
                    <label>Casa</label>
                    <textarea class="auto-resize readonly"
                              name="casa-vademecum2[]" rows="1"
                              readonly></textarea>
                </div>
            </div>

            <div class="column">
                <div class="ui form">
                    <div class="required field">
                        <label>Duración del tratamiento</label>
                        <textarea class="auto-resize" name="duracion_tratamiento2[]"
                                  placeholder="" rows="1"></textarea>
                    </div>
                </div>
            </div>
        </div>
        <div class="three column row">
            <div class="column">
                <div class="ui form">
                    <div class="required field">
                        <label>Frecuencia</label>
                        <textarea class="auto-resize" name="frecuencia2[]"
                                  placeholder="" rows="1"></textarea>
                    </div>
                </div>
            </div>
            <div class="column">
                <div class="ui form">
                    <div class="required field">
                        <label>Dosis/ vía de administración</label>
                        <textarea class="auto-resize" name="dosis2[]"
                                  placeholder="" rows="1"></textarea>
                    </div>
                </div>
            </div>
            <div class="column">
                <div class="ui form">
                    <div class="required field">
                        <label>Cantidad prescrita letras</label>
                        <textarea class="auto-resize" name="cantidad-letras2[]"
                                  placeholder="" rows="1"></textarea>
                    </div>
                </div>
            </div>
        </div>
        <div class="three column row">
            <div class="column">
                <div class="ui form">
                    <div class="required field">
                        <label>Cantidad prescrita números</label>
                        <textarea class="auto-resize" name="cantidad-numeros2[]"
                                  placeholder="" rows="1"></textarea>
                    </div>
                </div>
            </div>
            <div class="column">
                <div class="ui form">
                    <div class="required field">
                        <label>Notas aclaratorias</label>
                        <textarea class="auto-resize" name="notas2[]"
                                  placeholder="" rows="1"></textarea>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let cumData2 = [];
        let maxDuplicate2 = 3; // Máximo de duplicados permitido
        let currentMedicineCount2 = {{ count($medical_prescriptions) > 0 ? count($medical_prescriptions) : 1 }}; // Inicial basado en datos

        // Función para agregar un nuevo medicamento en el segundo blade
        function addMedicineSecondBlade() {
            // Validar si ya se alcanzó el máximo de duplicados
            if (currentMedicineCount2 >= maxDuplicate2) {
                Swal.fire({
                    icon: 'error',
                    title: 'Limite de medicamentos alcanzado',
                    html: "Has alcanzado el número máximo de medicamentos permitidos.",
                    confirmButtonText: 'Aceptar',
                     confirmButtonColor: '#91c845'
                });
                return;
            }

            // Validar que el último medicamento esté completo antes de duplicar
            let lastMedicineItem2 = $('#medicine-list-2_empty .medicine-item').last();
            let allFilled2 = true;
            lastMedicineItem2.find('textarea').each(function () {
                if ($(this).val() === '') {
                    allFilled2 = false;
                }
            });

            if (!allFilled2) {
                Swal.fire({
                    icon: 'error',
                    title: 'Campos requeridos',
                    html: "Por favor, completa todos los campos antes de agregar un nuevo medicamento.",
                    confirmButtonText: 'Aceptar',
                     confirmButtonColor: '#91c845'
                });
                return;
            }

            // Incrementar el contador de duplicados
            currentMedicineCount2++;

            let counter2 = currentMedicineCount2; // Usar currentMedicineCount2 para el conteo
            let template2 = $('#medicine-template-2').clone();
            template2.removeAttr('id');
            template2.attr('data-group-id', 'medical-prescription-' + currentMedicineCount2); // Asigna un identificador único
            template2.addClass('vademecum-group'); // Agregar la clase "vademecum-group"
            template2.find('.medicine-number').text(counter2);

            // Cambiar los atributos name y id para evitar colisiones en el segundo blade
            template2.find('input[name="molecula-prescription2[]"]').attr('name', 'molecula-prescription2[' + counter2 + ']').attr('id', 'molecula-prescription-2-' + counter2);
            template2.find('input[name="tipo-prescription2[]"]').attr('name', 'tipo-prescription2[' + counter2 + ']').attr('id', 'tipo-prescription-2-' + counter2);
            template2.find('input[name="descrip-prescription2[]"]').attr('name', 'descrip-prescription2[' + counter2 + ']').attr('id', 'descrip-prescription-2-' + counter2);
            template2.find('textarea[name="codigo-vademecum2[]"]').attr('name', 'codigo-vademecum2[' + counter2 + ']').attr('id', 'codigo-vademecum-2-' + counter2);
            template2.find('textarea[name="casa-vademecum2[]"]').attr('name', 'casa-vademecum2[' + counter2 + ']').attr('id', 'casa-vademecum-2-' + counter2);
            template2.find('textarea[name="duracion_tratamiento2[]"]').attr('name', 'duracion_tratamiento2[' + counter2 + ']').attr('id', 'duracion-tratamiento-2-' + counter2);
            template2.find('textarea[name="frecuencia2[]"]').attr('name', 'frecuencia2[' + counter2 + ']').attr('id', 'frecuencia-2-' + counter2);
            template2.find('textarea[name="dosis2[]"]').attr('name', 'dosis2[' + counter2 + ']').attr('id', 'dosis-2-' + counter2);
            template2.find('textarea[name="cantidad-letras2[]"]').attr('name', 'cantidad-letras2[' + counter2 + ']').attr('id', 'cantidad-letras-2-' + counter2);
            template2.find('textarea[name="cantidad-numeros2[]"]').attr('name', 'cantidad-numeros2[' + counter2 + ']').attr('id', 'cantidad-numeros-2-' + counter2);
            template2.find('textarea[name="notas2[]"]').attr('name', 'notas2[' + counter2 + ']').attr('id', 'notas-2-' + counter2);

            template2.find('textarea').val(''); // Limpiar todos los textarea

            template2.appendTo('#medicine-list-2_empty');
            template2.show();

            // Llamar a la función de inicialización solo para el nuevo grupo
            initializeDropdownsAndFields(template2); // Pasar el nuevo grupo como argumento
        }

        // Función para eliminar medicamentos
        $(document).on('click', '.remove-medicine2', function () {
            $(this).closest('.medicine-item').remove();
            currentMedicineCount2--; // Decrementar el contador al eliminar
            updateIndexesSecondBlade(); // Actualiza los índices tras eliminar un medicamento
        });

        // Función para actualizar los índices de los medicamentos clonados
        let updateIndexesSecondBlade = function () {
            $('#medicine-list-2_empty .medicine-item').each(function (index) {
                let medicineNumber2 = index + 1; // El número de medicamento comienza en 1
                $(this).find('.medicine-title').text('Medicamento N° ' + medicineNumber2); // Actualizar el número en el título

                // Actualizar los atributos name y id de los campos relacionados
                $(this).find('textarea').each(function () {
                    let nameAttr = $(this).attr('name');
                    let idAttr = $(this).attr('id');

                    // Verificar si los atributos 'name' y 'id' están definidos
                    if (nameAttr && idAttr) {
                        let newName = nameAttr.replace(/\[\d+\]/, '[' + medicineNumber2 + ']'); // Reemplaza el índice en el atributo name
                        $(this).attr('name', newName);

                        let newId = idAttr.replace(/-\d+$/, '-' + medicineNumber2); // Reemplaza el índice en el atributo id
                        $(this).attr('id', newId);
                    }
                });
            });
        };
    </script>

    <script>
        let pharmacies = [];

        $(document).ready(function () {

            $.getJSON('/js/zonaFLB_NFF.json', function (json) {
                pharmacies = json.List.map(item => ({
                    codigo: item.codigo,
                    sucursal: capitalizeFirstLetter(item.sucursal),
                    supervisor: capitalizeFirstLetter(item.supervisor),
                    SEARCH_TEXT: (item.codigo + ' ' + item.sucursal).toLowerCase()
                }));

                //const dropdown = $('#diagnosticsCodDropdown');

                $('.farmaciasDosCodDropdown').each(function () {
                    const dropdown = $(this);

                    dropdown.dropdown({
                        minCharacters: 1,
                        fullTextSearch: false,
                        forceSelection: true,
                        apiSettings: {
                            responseAsync: function (settings, callback) {
                                const query = settings.urlData.query.toLowerCase();
                                const filtered = pharmacies
                                    .filter(item => item.SEARCH_TEXT.includes(query))
                                    .slice(0, 15);
                                const results = filtered.map(item => ({
                                    name: item.codigo + ' - ' + item.sucursal,
                                    value: item.sucursal
                                }));
                                callback({ success: true, results });
                            }
                        },
                        onChange: function (value, text, $choice) {
                            const selectedItem = pharmacies.find(item => item.sucursal === value);
                            if (selectedItem) {
                                const container = $(this).closest('.fields');
                                container.find('input.description').val(selectedItem.sucursal);
                            }
                        }
                    });

                    // const preselectedInput = dropdown.find('input[type="hidden"][name="pharmacies_branch_prescription"]');
                    // const preselectedValue = preselectedInput.val();
                    //
                    // if (preselectedValue) {
                    //     const selectedItem = pharmacies.find(item => item.codigo === preselectedValue);
                    //     if (selectedItem) {
                    //         const menu = dropdown.find('.menu');
                    //
                    //         // Agrega el item al menú si no existe
                    //         if (menu.find(`.item[data-value="${selectedItem.codigo}"]`).length === 0) {
                    //             const newItem = $('<div>', {
                    //                 class: 'item',
                    //                 'data-value': selectedItem.codigo,
                    //                 text: selectedItem.codigo + ' - ' + selectedItem.sucursal
                    //             });
                    //             menu.append(newItem);
                    //         }
                    //
                    //         // Establece el valor manualmente en el input hidden
                    //         preselectedInput.val(selectedItem.codigo);//
                    //
                    //         dropdown.dropdown('refresh');
                    //         // Luego selecciona ese valor
                    //         dropdown.dropdown('set selected', selectedItem.codigo);
                    //
                    //     }
                    // }
                });
            });
        });
    </script>

    <script>
        $('.info_receta_medica').popup({
            boundary: 'body',
            content: 'Si diligencia uno o más campos en la pestaña Receta médica, todos los campos de esa pestaña serán obligatorios',
            position: 'top center',
            lastResort: 'bottom center' // Si no cabe en la posición inicial, se ajusta a esta
        });
    </script>

@endif
<style>
    .ui.search.genericCodeInput2 .results {
        max-height: 200px; /* Ajusta esta altura según tus necesidades */
        overflow-y: auto;
        border: 1px solid #ddd; /* Opcional: Agrega un borde para mejor visibilidad */
        padding: 5px; /* Opcional: Espaciado interno */
        background: #fff; /* Opcional: Color de fondo */
    }

    .ui.search > .results {
        width: 100% !important;
    }
</style>


