<div class="title">
    <i class="dropdown icon"></i>
    Incapacidad médica
    @if (!$disabled)
        <i class="info circle icon info_incapacidad_medica"></i>
    @endif
</div>

@php
    // Validación segura de todas las variables
    $diagnostic_mirror = $diagnostic_mirror ?? [];
    //dd($show_diagnostic_mirror);
    $show_diagnostic_mirror = $show_diagnostic_mirror ?? false;
    //dd($diagnostic_mirror);
@endphp
<div class="content" @if (!$disabled) id="medical-incapacity-or-leave" @endif>
    <!-- Primera línea con 3 search -->
    <div class="location-group" data-group-id="21">
        <div class="field">
            <div class="four fields">
                @php

                $diagnostics = $medical_service ? $medical_service->previousDiagnostics() : collect();
                
                    //Capturar el GIS (padre de prestaciones médicas)
                    $activityParent = $activity->parent_activity ?? '';
                    //Capturar el afiliado
                    $affiliate = !empty($activityParent) ? $activityParent->affiliate ?? '' : '';

                    //Validar si existe el afiliado y asignar valores
                    if (!empty($affiliate)) {
                        $provinceGis = $affiliate->province ?? '';
                        $cantonGis = $affiliate->canton ?? '';
                        $districtGis = $affiliate->district ?? '';
                    } else {
                        $provinceGis = '';
                        $cantonGis = '';
                        $districtGis = '';
                    }
                @endphp
                <!-- campo de provincia traida de GIS -->
                <div class="field required">
                    <label>Provincia</label>
                    <div
                        class="province ui search selection dropdown  @if ($disabled) disabled @endif">
                        <input type="hidden" name="province_incapacity_or_leave" class="minus_font province_value"
                            value="{{ $provinceGis }}">
                        <i class="dropdown icon"></i>
                        <div class="default text">Selecciona uno</div>
                        <div class="menu"></div>
                    </div>
                </div>
                <div class="field required">
                    <label>Cantón</label>
                    <div class="canton ui search selection dropdown  @if ($disabled) disabled @endif">
                        <input type="hidden" name="canton_incapacity_or_leave" class="minus_font canton_value"
                            value="{{ $cantonGis }}">
                        <i class="dropdown icon"></i>
                        <div class="default text">Selecciona uno</div>
                        <div class="menu"></div>
                    </div>
                </div>
                <div class="field required">
                    <label>Distrito</label>
                    <div
                        class="district ui search selection dropdown  @if ($disabled) disabled @endif">
                        <input type="hidden" name="district_incapacity_or_leave" class="minus_font district_value"
                            value="{{ $districtGis }}">
                        <i class="dropdown icon"></i>
                        <div class="default text">Selecciona uno</div>
                        <div class="menu"></div>
                    </div>
                </div>
                {{-- otras señas  --}}
                <div class="field required">
                    <label>Otras señas</label>
                    <div class="ui search code">
                        <div class="ui icon input">
                            <textarea class="@if (!empty($disabled)) textarea-expand @else auto-resize @endif"
                                name="{{ !empty($disabled) ? '' : 'other_signs' }}" autocomplete="off"
                                @if ($disabled) readonly @else rows="1" @endif>{{ $medical_service->other_signs ?? '' }}</textarea>
                        </div>
                        <div class="results"></div>
                    </div>
                </div>
                <!-- Botón de limpiar -->
                @if ($disabled !== true)
                    <div class="wide two field" style="margin-top: 25px;">
                        <button id="clear-location-button" class="ui red small icon fluid button" type="button"
                            onclick="clearLocationFields(this)">
                            <i class="undo icon"></i>
                            Limpiar
                        </button>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Campo Tipo de caso -->
    <div class="field required">
        <label>Tipo de caso</label>
        @if ($disabled)
            <div class="ui fluid selection dropdown disabled">
                <input name="origin_case" type="hidden" value="{{ $medical_service->origin_case ?? '' }}" disabled>
                <i class="dropdown icon"></i>
                <div class="default text">Tipo de caso</div>
                <div class="menu">
                    @foreach ($ORIGIN_DIAGNOSIS as $k => $r)
                        <div class="item" data-value="{{ $k }}">{{ $r }}</div>
                    @endforeach
                </div>
            </div>
        @else
            <div id="dropdownTipoCaso" class="ui fluid selection dropdown">
                <input name="origin_case" type="hidden" value="">
                <i class="dropdown icon"></i>
                <div class="default text">Tipo de caso</div>
                <div class="menu">
                    @foreach ($ORIGIN_DIAGNOSIS_UPDATED as $k => $r)
                        <div class="item" data-value="{{ $k }}">{{ $r }}</div>
                    @endforeach
                </div>
            </div>
        @endif
    </div>

    <div class="four fields">
        <!-- Campo Tipo de incapacidad -->
        <div class="field required">
            <label>Tipo de incapacidad</label>
            <div class="ui selection dropdown @if ($disabled) disabled @endif"
                id="heirDocTypeDropdown">
                <input type="hidden" name="disability_type" id="disability_type" class="minus_font"
                    value="{{ $medical_service->disability_type ?? '' }}">
                <i class="dropdown icon"></i>
                <div class="default text minus_font">Seleccionar un canal de consulta</div>
                <div class="menu">
                    @foreach ($DISABILITY_TYPE as $k => $v)
                        <div class="item minus_font" data-value="{{ $k }}">
                            {{ $v }}
                        </div>
                    @endforeach
                </div>
            </div>
        </div>

        <!-- Campo Modalidad de atención -->
        <div class="field required">
            <label>Modalidad de atención</label>
            <div class="ui selection dropdown @if ($disabled) disabled @endif"
                id="heirDocTypeDropdown">
                <input type="hidden" name="attention_mode" id="attention_mode" class="minus_font"
                    value="{{ $medical_service->attention_mode ?? '' }}">
                <i class="dropdown icon"></i>
                <div class="default text minus_font">Seleccionar un canal de consulta</div>
                <div class="menu">
                    @foreach ($ATTENTION_MODE as $k => $v)
                        <div class="item minus_font" data-value="{{ $k }}">
                            {{ $v }}
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
        <!-- Campo Fecha de inicio incapacidad -->
        <div class="field required">
            <label>Fecha de inicio incapacidad</label>
            <input id="start_date_of_incapacity_{{ $medical_service ? $medical_service->id : 'empty' }}" type="text"
                name="start_date_of_incapacity_display" placeholder="dd/mm/yyyy"
                value="{{ $medical_service->start_date_of_incapacity ?? '' }}"
                @if ($disabled) disabled @endif>
            <input type="hidden" @if (!$disabled) id="start_date_of_incapacity_hidden" @endif
                name="start_date_of_incapacity" value="{{ $medical_service->start_date_of_incapacity ?? '' }}"
                @if ($disabled) disabled @endif>
        </div>
        <!-- Campo Días de incapacidad -->
        <div class="field required">
            <label>Días de incapacidad</label>
            <input class="@if (!$disabled) day_of_incapacity_value @endif" id="days_of_incapacity"
                type="number" name="days_of_incapacity" min="0"
                value="{{ $medical_service->days_of_incapacity ?? '' }}"
                @if ($disabled) disabled @endif>
        </div>
    </div>
    @if ($disabled)
        <!-- Campo Motivo de Incapacidad mayor a 3 días -->
        <div class="field required" style="display: @if ($disabled && $medical_service->days_of_incapacity > 3) block @else none @endif;">
            <label>Motivo de Incapacidad mayor a 3 días</label>
            <textarea class="@if (!empty($disabled)) textarea-expand @else auto-resize @endif"
                name="{{ !empty($disabled) ? '' : 'extended_incapacity_reason' }}" autocomplete="off"
                @if ($disabled) readonly @else rows="1" @endif>{{ $medical_service->extended_incapacity_reason ?? '' }}</textarea>
        </div>
    @else
        <!-- Campo Motivo de Incapacidad mayor a 3 días -->
        <div class="field">
            <div id="extended_incapacity_reason_field" style="display: none; margin-top: 15px;">
                <div id="extended_incapacity_reason_required" class="field">
                    <label>Motivo de Incapacidad mayor a 3 días</label>
                    <textarea id="extended_incapacity_reason_value" class="auto-resize" name="extended_incapacity_reason"
                        autocomplete="off" rows="1"></textarea>
                </div>
            </div>
        </div>
    @endif

@php
    //dd($medical_service);
@endphp

<!-- Este es el de tipo espejo -->

@if($show_diagnostic_mirror ?? false)
    @if(!empty($diagnostic_mirror))
    <div id="diagnostics_{{ $diagnostics->isNotEmpty() ? 1 : 'empty' }}">
        @foreach ($diagnostics as $diagnostic)
        @if (in_array($diagnostic->origin, ['LAB', 'EST', 'PARLAB']) &&
        $diagnostic->diagnostic_status === 'A')
      
         
        <div class="fields diagnostic-fields">
            <input type="hidden" name="diagnostics[id][]" value="{{ $diagnostic->id ?? '' }}"
                {{ $disabled ? 'disabled' : '' }}>
            <div class="nine wide required field">
                <label>Código</label>
                <div class="ui search diagnostic code">
                    <div class="ui icon input">
                        <input class="prompt minus_font" name="diagnostics[cod][]" type="text"
                            autocomplete="off" value="{{ $diagnostic->code ?? '' }}"
                            @if ($disabled) disabled @endif>
                        <i class="search icon "></i>
                    </div>
                    <div class="results"></div>
                </div>
            </div>
            <div class="eight wide required field">
                <label>Nombre</label>
                <input class="description prompt medical-input-to-lower-case readonly"
                    name="diagnostics[description][]" type="text"
                    value="{{ $diagnostic->description ?? '' }}" readonly {{ $disabled ? 'disabled' : '' }}>
            </div>
            <div class="eight wide required field ">
                <label>Descripción</label>
                <input class="description prompt medical-input-to-lower-case readonly"
                    name="diagnostics[description_editable][]" type="text"
                    value="{{ $diagnostic->description_editable ?? '' }}" readonly
                    {{ $disabled ? 'disabled' : '' }}>
            </div>
            <div class="three wide field">
                <label>Lateralidad</label>
                <div class="ui fluid selection dropdown disabled">
                    <input name="diagnostics[laterality][]" type="hidden"
                        value="{{ $diagnostic->laterality ?? '' }}" {{ $disabled ? 'disabled' : '' }}>
                    <i class="dropdown icon"></i>
                    <div class="default text">Lateralidad</div>
                    <div class="menu">
                        @foreach ($LATERALITY as $k => $r)
                            <div class="item" data-value="{{ $k }}">{{ $r }}</div>
                        @endforeach
                    </div>
                </div>
            </div>
            <div class="five wide field">
                <label>Origen</label>
                @if ($disabled)
                    <div class="ui fluid selection dropdown disabled">
                        <input name="diagnostics[origin][]" type="hidden"
                            value="{{ $diagnostic->origin ?? '' }}" disabled>
                        <i class="dropdown icon"></i>
                        <div class="default text">Origen</div>
                        <div class="menu">
                            @foreach ($ORIGIN_DIAGNOSIS as $k => $r)
                                <div class="item" data-value="{{ $k }}">{{ $r }}</div>
                            @endforeach
                        </div>
                    </div>
                @endif
            </div>
            <div class="five wide required field">
                <label>Estado</label>
                <div id="dropdownOrigen" class="ui fluid selection dropdown disabled">
                    <input name="diagnostics[diagnostic_status][]" type="hidden"
                        value="{{ $diagnostic->diagnostic_status ?? '' }}" disabled>
                    <i class="dropdown icon"></i>
                    <div class="default text">Estado</div>
                    <div class="menu">
                        @foreach ($DIAGNOSTIC_STATUS as $k => $r)
                            <div class="item" data-value="{{ $k }}">{{ $r }}</div>
                        @endforeach
                    </div>
                </div>
            </div>
            <div class="five wide required field">
                <label>Clasificación</label>
                <div id="dropdownOrigen" class="ui fluid selection dropdown disabled">
                    <input name="diagnostics[clasificacion][]" type="hidden"
                        value="{{ $diagnostic->clasificacion ?? '' }}" disabled>
                    <i class="dropdown icon"></i>
                    <div class="default text">Clasificación</div>
                    <div class="menu">
                        @foreach ($CLASIFICACION as $k => $r)
                            <div class="item" data-value="{{ $k }}">{{ $r }}</div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
        @endif

        @endforeach
    </div>
    @endif
    @endif

<!-- Campo oculto para almacenar los diagnósticos -->
<!-- Sección del espejo de diagnósticos -->
@if($show_diagnostic_mirror)
@php
//dd($show_diagnostic_mirror);
@endphp


<!-- Campo oculto para almacenar los diagnósticos -->
<input type="hidden" id="medical-incapacity-diagnostics" name="medical_incapacity[diagnostics]" value="">
@endif
@php
    //dd($medical_service);
@endphp


<div  class="diagnostic-mirror-container">
    <h4 class="ui header">Diagnósticos Relacionados</h4>
    @if( isset($numeroatencion) &&  $numeroatencion === null)
        <div class="diagnostic-mirror">
            <!-- Contenido generado dinámicamente por JavaScript -->
            <div class="ui message">Complete los diagnósticos en la sección correspondiente</div>
        </div>
    @endif
   
</div>




    @if($diagnostics->isNotEmpty())
    <div id="diagnostics_{{ $diagnostics->isNotEmpty() ? 1 : 'empty' }}">
        @foreach ($diagnostics as $diagnostic)
        @if (in_array($diagnostic->origin, ['LAB', 'EST', 'PARLAB']) &&
        $diagnostic->diagnostic_status === 'A')
          
        <div class="fields diagnostic-fields">
            <input type="hidden" name="diagnostics[id][]" value="{{ $diagnostic->id ?? '' }}"
                {{ $disabled ? 'disabled' : '' }}>
            <div class="nine wide required field">
                <label>Código</label>
                <div class="ui search diagnostic code">
                    <div class="ui icon input">
                        <input class="prompt minus_font" name="diagnostics[cod][]" type="text"
                            autocomplete="off" value="{{ $diagnostic->code ?? '' }}"
                            @if ($disabled) disabled @endif>
                        <i class="search icon "></i>
                    </div>
                    <div class="results"></div>
                </div>
            </div>
            <div class="eight wide required field">
                <label>Nombre</label>
                <input class="description prompt medical-input-to-lower-case readonly"
                    name="diagnostics[description][]" type="text"
                    value="{{ $diagnostic->description ?? '' }}" readonly {{ $disabled ? 'disabled' : '' }}>
            </div>
            <div class="eight wide required field ">
                <label>Descripción</label>
                <input class="description prompt medical-input-to-lower-case readonly"
                    name="diagnostics[description_editable][]" type="text"
                    value="{{ $diagnostic->description_editable ?? '' }}" readonly
                    {{ $disabled ? 'disabled' : '' }}>
            </div>
            <div class="three wide field">
                <label>Lateralidad</label>
                <div class="ui fluid selection dropdown disabled">
                    <input name="diagnostics[laterality][]" type="hidden"
                        value="{{ $diagnostic->laterality ?? '' }}" {{ $disabled ? 'disabled' : '' }}>
                    <i class="dropdown icon"></i>
                    <div class="default text">Lateralidad</div>
                    <div class="menu">
                        @foreach ($LATERALITY as $k => $r)
                            <div class="item" data-value="{{ $k }}">{{ $r }}</div>
                        @endforeach
                    </div>
                </div>
            </div>
            <div class="five wide field">
                <label>Origen</label>
                @if ($disabled)
                    <div class="ui fluid selection dropdown disabled">
                        <input name="diagnostics[origin][]" type="hidden"
                            value="{{ $diagnostic->origin ?? '' }}" disabled>
                        <i class="dropdown icon"></i>
                        <div class="default text">Origen</div>
                        <div class="menu">
                            @foreach ($ORIGIN_DIAGNOSIS as $k => $r)
                                <div class="item" data-value="{{ $k }}">{{ $r }}</div>
                            @endforeach
                        </div>
                    </div>
                @endif
            </div>
            <div class="five wide required field">
                <label>Estado</label>
                <div id="dropdownOrigen" class="ui fluid selection dropdown disabled">
                    <input name="diagnostics[diagnostic_status][]" type="hidden"
                        value="{{ $diagnostic->diagnostic_status ?? '' }}" disabled>
                    <i class="dropdown icon"></i>
                    <div class="default text">Estado</div>
                    <div class="menu">
                        @foreach ($DIAGNOSTIC_STATUS as $k => $r)
                            <div class="item" data-value="{{ $k }}">{{ $r }}</div>
                        @endforeach
                    </div>
                </div>
            </div>
            <div class="five wide required field">
                <label>Clasificación</label>
                <div id="dropdownOrigen" class="ui fluid selection dropdown disabled">
                    <input name="diagnostics[clasificacion][]" type="hidden"
                        value="{{ $diagnostic->clasificacion ?? '' }}" disabled>
                    <i class="dropdown icon"></i>
                    <div class="default text">Clasificación</div>
                    <div class="menu">
                        @foreach ($CLASIFICACION as $k => $r)
                            <div class="item" data-value="{{ $k }}">{{ $r }}</div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
        @endif

        @endforeach
    </div>
    @endif


    <div class="three fields">
        <!-- Campo Viaticos -->
        <div class="four wide field required">
            <label>Viáticos y/o pasajes</label>
            <div class="ui selection dropdown   @if ($disabled) disabled @endif"
                id="viaticosDropdown">
                <input type="hidden" name="travel_expenses" id="travel_expenses" class="minus_font"
                       value="{{ $medical_service->travel_expenses ?? '' }}">
                <i class="dropdown icon"></i>
                <div class="default text minus_font">Seleccionar una opcion</div>
                <div class="menu">
                    <div class="item minus_font" data-value="Si">Sí</div>
                    <div class="item minus_font" data-value="No">No</div>
                </div>
            </div>
        </div>
        <!-- Campo Transporte -->
        <div class="four wide field required">
            <label>Transporte</label>
            <div class="ui selection dropdown  @if($disabled) disabled @endif" id="transporteDropdown">
                <input type="hidden" name="transportation" id="transportation" class="minus_font"
                       value="{{ $medical_service->transportation ?? '' }}">
                <i class="dropdown icon"></i>
                <div class="default text minus_font">Seleccionar una opcion</div>
                <div class="menu">
                    <div class="item minus_font" data-value="Si">Sí</div>
                    <div class="item minus_font" data-value="No">No</div>
                </div>
            </div>
        </div>
        <!-- Campo Observaciones o notas aclaratorias -->
        <div class="eight wide field required">
            <label>Observaciones o notas aclaratorias</label>
            <div class="ui search code">
                <div class="ui icon input">
                            <textarea class="@if(!empty($disabled)) textarea-expand @else auto-resize @endif" name="{{!empty($disabled) ? '' : 'observations_explanatory_notes' }}"
                                      autocomplete="off" @if($disabled) readonly @else rows="1" @endif>{{ $medical_service->observations_explanatory_notes ?? '' }}</textarea>
                </div>
                <div class="results"></div>
            </div>
        </div>
    </div>
</div>
<script>
    <!-- Inicializa el picker de fecha - Formulario medical-incapcity-or-leave -->
    $(document).ready(function() {
        //formato de fecha que trae
        @if(isset($medical_service))
            let start_date = '{{ $medical_service->start_date_of_incapacity }}';
            if(start_date != ''){
                // Formatear la fecha en el formato deseado
                let start_date_format = moment(start_date).format('dddd D [de] MMMM [de] YYYY');
                // Convertir la primera letra en mayúscula
                let start_date_formatted = start_date_format.charAt(0).toUpperCase() + start_date_format.slice(1);
                // Insertar la fecha formateada en el campo de texto
                document.getElementById('start_date_of_incapacity_{{$medical_service->id}}').value = start_date_formatted;
            }
        @else
            let gis_accident = '{{ $date_accident_gis ?? '' }}';

            // Formatear la fecha en el formato deseado
            if(gis_accident){
                gis_accident = new Date(gis_accident);
            }
            const start_date_of_incapacity = $('#start_date_of_incapacity_empty');

            // Configuración básica
            let pickadateConfig = {
                selectYears: true,
                selectMonths: true,
                formatSubmit: 'yyyy-mm-dd', // Formato que se enviará al servidor
                format: 'dd/mm/yyyy', // Formato que se mostrará en el campo
                onSet: function(context) {
                    if (context.select) {
                        // Obtener la fecha seleccionada
                        const selectedDate = new Date(context.select);

                        // Usar Moment.js para formatear la fecha
                        let createdAtFormat = moment(selectedDate).format('dddd D [de] MMMM [de] YYYY');
                        let formattedDate = createdAtFormat.charAt(0).toUpperCase() + createdAtFormat.slice(1);
                        // Mostrar la fecha formateada en el campo de entrada
                        $('#start_date_of_incapacity').val(formattedDate); // Actualiza el valor del campo de texto visible

                        // Actualizar el campo oculto con la fecha en formato ISO
                        $('#start_date_of_incapacity_hidden').val(selectedDate.toISOString().split('T')[0]); // yyyy-mm-dd
                    }
                }
            };

            // Agregar 'min' directamente si existe 'gis_accident'
            if (gis_accident) {
                pickadateConfig.min = gis_accident;
            }

            // Inicializar pickadate con la configuración
            start_date_of_incapacity.pickadate(pickadateConfig);

        @endif

    });
</script>

<script>
    $(document).ready(function() {
        $('.day_of_incapacity_value').on('input', function() {
            // Permitir solo números
            let value = $(this).val();
            $(this).val(value.replace(/[^0-9]/g, '')); // Eliminar caracteres no numéricos
        });

    });
</script>
<script>
    $('.day_of_incapacity_value').on('blur', function() {
        let value = parseInt($(this).val(), 10) || 0; // Obtiene el valor del campo que activó el evento

        let extended_incapacity_reason_field = $(
        '#extended_incapacity_reason_field'); // Selecciona correctamente el elemento
        let extended_incapacity_reason_required = $(
        '#extended_incapacity_reason_required'); // Selecciona correctamente el elemento
        let extended_incapacity_reason_value = $(
        '#extended_incapacity_reason_value'); // Selecciona correctamente el elemento

        // Mostrar/ocultar el campo de motivo de devolución según el valor seleccionado
        if (value > 3) {
            extended_incapacity_reason_field.css('display', 'block'); // Muestra el campo
            extended_incapacity_reason_value.val(''); // Limpia el valor actual del campo
            extended_incapacity_reason_required.addClass('required'); // Añade la clase 'required'
        } else {
            extended_incapacity_reason_field.css('display', 'none'); // Oculta el campo
            extended_incapacity_reason_required.removeClass('required'); // Elimina la clase 'required'
        }
    });
</script>

@push('scripts')
<script>
    // Función global para actualizar desde el formulario de diagnósticos
    window.updateDiagnosticMirror = function(diagnostics) {
        console.log("window.updateDiagnosticMirror");
        const $mirrorContainer = $('#diagnostic-mirror-container');
        const $mirrorContent = $('#diagnostic-mirror');
        
        if (diagnostics.length > 0) {
            let html = '';
            diagnostics.forEach(diag => {
                html += `
                    <div class="fields mirror-view">
                        <div class="nine wide field">
                            <label>Código</label>
                            <div class="ui fluid input readonly">
                                <input type="text" value="${diag.cod}" readonly>
                            </div>
                        </div>
                        <div class="eight wide field">
                            <label>Descripción</label>
                            <div class="ui fluid input readonly">
                                <input type="text" value="${diag.description}" readonly>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            $mirrorContent.html(html);
            $mirrorContainer.show();
        } else {
            $mirrorContainer.hide();
        }
        
        // Actualizar campo oculto
        $('#medical-incacity-diagnostics').val(JSON.stringify(diagnostics));
    };
</script>
@endpush

<script>
    $('.info_incapacidad_medica').popup({
        boundary: 'body',
        content: 'Si diligencia uno o más campos en la pestaña Incapacidad médica, todos los campos de esa pestaña serán obligatorios',
        position: 'top center',
        lastResort: 'bottom center' // Si no cabe en la posición inicial, se ajusta a esta
    });
</script>
