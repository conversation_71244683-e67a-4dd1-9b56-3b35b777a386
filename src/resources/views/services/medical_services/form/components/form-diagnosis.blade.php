<div class="title">
    <i class="dropdown icon"></i>
    Diagnósticos <span style="color: red;">*</span>
</div>
@php

@endphp
<div class="content form" @if(!$disabled) id="content-diagnosis" @endif >

    <div class="five fields">

        <div class="required field">
            <label>Confirmación de enfermedad</label>
            <div class="ui form">
                <div class="inline fields">
                    <div class="field">
                        <div class="ui radio checkbox">
                            <input type="radio" @if(!$disabled) name="gis_disease_confirmation" @endif value="1"
                                   @if(isset($activity->medical_services_sort->gis_disease_confirmation) && $activity->medical_services_sort->gis_disease_confirmation == 1) checked @endif
                                   @if($disabled) disabled @endif>
                            <label>Sí</label>
                        </div>
                    </div>
                    <div class="field">
                        <div class="ui radio checkbox">
                            <input type="radio" @if(!$disabled) name="gis_disease_confirmation" @endif value="0"
                                   @if(isset($activity->medical_services_sort->gis_disease_confirmation) && $activity->medical_services_sort->gis_disease_confirmation == 0) checked @endif
                                   @if($disabled) disabled @endif>
                            <label>No</label>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="required field">
            <label for="ocurrencia">Enfermedades profesionales</label>
            <div class="ui selection dropdown @if($disabled) disabled grayed-input @endif" @if(!$disabled) id="dropdownReportDisease" @endif>
                <input type="hidden"
                       @if(!$disabled) name="disease_report_type_id" id="disease_report_type_id" @endif
                       @if($disabled) disabled @endif
                       value="{{ $activity->medical_services_sort->disease_report_type_id ? $activity->medical_services_sort->disease_report_type_id : $gis->disease_report_type_id }}">
                <i class="dropdown icon"></i>
                <div class="default text">Selecciona una opción</div>
                <div class="menu">
                    @foreach ($occupationalDisease as $row)
                        <div class="item" data-value="{{ $row->id }}">{{ $row->name }}</div>
                    @endforeach
                </div>
            </div>
        </div>

        <div class="required field">
            <label for="naturaleza">Naturaleza de la lesión</label>
            <div class="ui selection dropdown dropdown_dinamic @if($disabled) grayed-input @endif ">
                <input type="hidden" name="injury_nature_id" id="injury_nature_id"
                       value="{{ $activity->medical_services_sort->injury_nature_id ?? '' }}" />
                <i class="dropdown icon"></i>
                <div class="default text">Selecciona una opción</div>
                <div class="menu">
                    @foreach ($injuryNature as $row)
                        <div class="item" data-value="{{ $row->id }}">{{ $row->name }}</div>
                    @endforeach
                </div>
            </div>
        </div>

        <div class="required field">
            <label for="ubicacion">Ubicación de la Lesión</label>
            <div class="ui selection dropdown dropdown_dinamic @if($disabled) grayed-input @endif ">
                <input type="hidden" name="injury_location_id" id="injury_location_id"
                       value="{{ $activity->medical_services_sort->injury_location_id ?? '' }}" />
                <i class="dropdown icon"></i>
                <div class="default text">Selecciona una opción</div>
                <div class="menu">
                    @foreach ($injuryLocation as $row)
                        <div class="item" data-value="{{ $row->id }}">{{ $row->name }}</div>
                    @endforeach
                </div>
            </div>
        </div>

    </div>


    <div id="diagnostics_{{ $diagnostics ? 1 : 'empty' }}">


        @if (isset($diagnostics) && collect($diagnostics)->isNotEmpty())
            @if(isset($diagnostics->first()->diagnostics))
           
                @foreach ($diagnostics as $followUp)
                    {{-- Validamos que tenga diagnostics --}}
                    @if ($followUp->diagnostics && $followUp->diagnostics->isNotEmpty())
                        @foreach ($followUp->diagnostics as $diagnostic)
                            {{-- Aquí ya puedes trabajar con cada $diagnostic --}}

{{--                            @if($diagnostic->diagnostic_status !== 'R')--}}

                                @if( isset($numeroatencion) && $numeroatencion !== null)


                                    <div class="fields diagnostic-fields{{$numeroatencion}}">

                                @else

                                    <div class="fields valor">
                                @endif

                                    <input type="hidden" name="diagnostics[id][]" value="{{ $diagnostic->id ?? '' }}" >

                                        <div class="required field">
                                            <label>Código</label>
                                            <div class="ui search selection dropdown diagnosticsCodDropdown" id="diagnosticsCodDropdown">
                                                <input type="hidden" name="diagnostics[cod][]" value="{{ $diagnostic->code ?? ''  }}">
                                                <i class="dropdown icon"></i>
                                                <div class="default text">Código</div>
                                                <div class="menu ">
                                                    <div class="item" data-value=""> </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="eight wide required field">
                                            <label>Nombre</label>
                                            <input class="description prompt medical-input-to-lower-case readonly" name="diagnostics[description][]" type="text"
                                                   value="{{ $diagnostic->description ?? '' }}" readonly  >
                                        </div>
                                        <div class="eight wide required field ">
                                            <label>Descripción</label>
                                            <input class="description prompt medical-input-to-lower-case readonly" name="diagnostics[description_editable][]" type="text"
                                                   value="{{ $diagnostic->description_editable ?? '' }}" readonly  >
                                        </div>
                                        <div class="three wide field">
                                            <label>Lateralidad</label>
                                            <div class="ui fluid selection dropdown disabled">
                                                <input name="diagnostics[laterality][]" type="hidden" value="{{ $diagnostic->laterality ?? '' }}"  >
                                                <i class="dropdown icon"></i>
                                                <div class="default text">Lateralidad</div>
                                                <div class="menu">
                                                    @foreach($LATERALITY as $k=>$r)
                                                        <div class="item" data-value="{{$k}}">{{$r}}</div>
                                                    @endforeach
                                                </div>
                                            </div>
                                        </div>

                                        <div class="five wide field">
                                            <label>Origen</label>

                                            <div class="ui fluid selection dropdown">
                                                <input name="diagnostics[origin][]" type="hidden" value="{{ $diagnostic->origin ?? '' }}">
                                                <i class="dropdown icon"></i>
                                                <div class="default text">Origen</div>
                                                <div class="menu">
                                                    @foreach($ORIGIN_DIAGNOSIS as $k=>$r)
                                                        <div class="item" data-value="{{$k}}">{{$r}}</div>
                                                    @endforeach
                                                </div>
                                            </div>

                                        </div>
                                    <div class="five wide required field">
                                        <label>Estado</label>
                                        <div  class="ui fluid selection dropdown">
                                            <input name="diagnostics[diagnostic_status][]" type="hidden" value="{{ $diagnostic->diagnostic_status ?? '' }}">
                                            <i class="dropdown icon"></i>
                                            <div class="default text">Estado</div>
                                            <div class="menu">
                                                @foreach($DIAGNOSTIC_STATUS as $k=>$r)
                                                    <div class="item" data-value="{{$k}}">{{$r}}</div>
                                                @endforeach
                                            </div>
                                        </div>
                                    </div>
                                    <div class="five wide required field">
                                        <label>Clasificación</label>
                                        <div  class="ui fluid selection dropdown disabled">
                                            <input name="diagnostics[clasificacion][]" type="hidden" value="{{ $diagnostic->clasificacion ?? '' }}">
                                            <i class="dropdown icon"></i>
                                            <div class="default text">Clasificación</div>
                                            <div class="menu">
                                                @foreach($CLASIFICACION as $k=>$r)
                                                    <div class="item" data-value="{{$k}}">{{$r}}</div>
                                                @endforeach
                                            </div>
                                        </div>
                                    </div>
                                    <div class="one wide field">
                                        <a style="margin-top: 25px;" onclick="addDiagnostic1()"
                                           class="ui basic small icon blue button"><i class="add icon"></i></a>
                                    </div>
                                </div>
{{--                            @endif--}}

                        @endforeach
                    @endif

                @endforeach

                @if($disabled == false)
                    <div id="diagnostics_2">
                    </div>
                @endif

            @else            

                @foreach($diagnostics as $diagnostic)

                    @if(  isset($numeroatencion) &&  $numeroatencion !== null)
                        <div class="fields diagnostic-fields{{$numeroatencion}}">
                    @else
                        <div class="valor">
                    @endif

                        <input type="hidden" name="diagnostics[id][]" value="{{ $diagnostic->id ?? '' }}" {{ $disabled ? 'disabled' : '' }}>
                        <div class="nine wide required field">
                            <label>Código</label>
                            <div class="ui search diagnostic code">
                                <div class="ui icon input">
                                    <input class="prompt minus_font" name="diagnostics[cod][]" type="text" autocomplete="off"
                                           value="{{ $diagnostic->code ?? '' }}" @if($disabled) disabled @endif>
                                    <i class="search icon "></i>
                                </div>
                                <div class="results"></div>
                            </div>
                        </div>
                        <div class="eight wide required field">
                            <label>Nombre</label>
                            <input class="description prompt medical-input-to-lower-case readonly" name="diagnostics[description][]" type="text"
                                   value="{{ $diagnostic->description ?? '' }}" readonly  {{ $disabled ? 'disabled' : '' }}>
                        </div>
                        <div class="eight wide required field ">
                            <label>Descripción</label>
                            <input class="description prompt medical-input-to-lower-case readonly" name="diagnostics[description_editable][]" type="text"
                                   value="{{ $diagnostic->description_editable ?? '' }}" readonly  {{ $disabled ? 'disabled' : '' }}>
                        </div>
                        <div class="three wide field">
                            <label>Lateralidad</label>
                            <div class="ui fluid selection dropdown disabled">
                                <input name="diagnostics[laterality][]" type="hidden" value="{{ $diagnostic->laterality ?? '' }}"  {{ $disabled ? 'disabled' : '' }}>
                                <i class="dropdown icon"></i>
                                <div class="default text">Lateralidad</div>
                                <div class="menu">
                                    @foreach($LATERALITY as $k=>$r)
                                        <div class="item" data-value="{{$k}}">{{$r}}</div>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                        <div class="five wide field">
                            <label>Origen</label>
                            @if($disabled)
                                <div class="ui fluid selection dropdown disabled">
                                    <input name="diagnostics[origin][]" type="hidden" value="{{ $diagnostic->origin ?? '' }}" {{ $disabled ? 'disabled' : '' }}>
                                    <i class="dropdown icon"></i>
                                    <div class="default text">Origen</div>
                                    <div class="menu">
                                        @foreach($ORIGIN_DIAGNOSIS_UPDATED as $k=>$r)
                                            <div class="item" data-value="{{$k}}">{{$r}}</div>
                                        @endforeach
                                    </div>
                                </div>
                            @endif
                        </div>
                        <div class="five wide required field">
                            <label>Estado</label>
                            <div  class="ui fluid selection dropdown disabled">
                                <input name="diagnostics[diagnostic_status][]" type="hidden" value="{{ $diagnostic->diagnostic_status ?? '' }}" {{ $disabled ? 'disabled' : '' }}>
                                <i class="dropdown icon"></i>
                                <div class="default text">Estado</div>
                                <div class="menu">
                                    @foreach($DIAGNOSTIC_STATUS as $k=>$r)
                                        <div class="item" data-value="{{$k}}">{{$r}}</div>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                        <div class="five wide required field">
                            <label>Clasificación</label>
                            <div  class="ui fluid selection dropdown disabled">
                                <input name="diagnostics[clasificacion][]" type="hidden" value="{{ $diagnostic->clasificacion ?? '' }}" {{ $disabled ? 'disabled' : '' }}>
                                <i class="dropdown icon"></i>
                                <div class="default text">Clasificación</div>
                                <div class="menu">
                                    @foreach($CLASIFICACION as $k=>$r)
                                        <div class="item" data-value="{{$k}}">{{$r}}</div>
                                    @endforeach
                                </div>
                            </div>
                        </div>

                    </div>
                @endforeach

            @endif
        @endif
       
        @if(count($diagnostics) == 0 && empty($disabled))
            <div class="fields diagnostic-fields">
                <input type="hidden" name="diagnostics[id][]">

                <div class="required field">
                    <label>Código</label>
                    <div class="ui search selection dropdown diagnosticsCodDropdown" id="diagnosticsCodDropdown">
                        <input type="hidden" name="diagnostics[cod][]" value="{{ $diagnostic->code ?? ''  }}">
                        <i class="dropdown icon"></i>
                        <div class="default text">Código</div>
                        <div class="menu ">
                            <div class="item" data-value=""> </div>
                        </div>
                    </div>
                </div>

                <div class="eight wide required field">
                    <label>Nombre</label>
                    <input class="description prompt medical-input-to-lower-case readonly" name="diagnostics[description][]" type="text" readonly>
                </div>
                <div class="eight wide required field">
                    <label>Descripción</label>
                    <input class="description prompt medical-input-to-lower-case readonly" name="diagnostics[description_editable][]" type="text" readonly>
                </div>
                <div class="three wide field">
                    <label>Lateralidad</label>
                    <div class="ui fluid selection dropdown">
                        <input name="diagnostics[laterality][]" type="hidden">
                        <i class="dropdown icon"></i>
                        <div class="default text">Lateralidad</div>
                        <div class="menu">
                            @foreach($LATERALITY as $k=>$r)
                                <div class="item" data-value="{{$k}}">{{$r}}</div>
                            @endforeach
                        </div>
                    </div>
                </div>
                <div class="five wide required field">
                    <label>Origen</label>
                    <div id="dropdownOrigen" class="ui fluid selection dropdown">
                        <input name="diagnostics[origin][]" type="hidden" value="">
                        <i class="dropdown icon"></i>
                        <div class="default text">Origen</div>
                        <div class="menu">
                            @foreach($ORIGIN_DIAGNOSIS_UPDATED as $k=>$r)
                                <div class="item" data-value="{{$k}}">{{$r}}</div>
                            @endforeach
                        </div>
                    </div>
                </div>
                <div class="five wide required field">
                    <label>Estado</label>
                    <div  class="ui fluid selection dropdown">
                        <input name="diagnostics[diagnostic_status][]" type="hidden" value="">
                        <i class="dropdown icon"></i>
                        <div class="default text">Estado</div>
                        <div class="menu">
                            @foreach($DIAGNOSTIC_STATUS as $k=>$r)
                                <div class="item" data-value="{{$k}}">{{$r}}</div>
                            @endforeach
                        </div>
                    </div>
                </div>
                <div class="five wide required field">
                    <label>Clasificación</label>
                    <div  class="ui fluid selection dropdown">
                        <input name="diagnostics[clasificacion][]" type="hidden" value="">
                        <i class="dropdown icon"></i>
                        <div class="default text">Clasificación</div>
                        <div class="menu">
                            @foreach($CLASIFICACION as $k=>$r)
                                <div class="item" data-value="{{$k}}">{{$r}}</div>
                            @endforeach
                        </div>
                    </div>
                </div>
                <div class="one wide field">
                    <a style="margin-top: 25px;" onclick="addDiagnostic()"
                       class="ui basic small icon blue button"><i class="add icon"></i></a>
                </div>
            </div>
        @endif
    </div>

</div>
@if(empty($disabled))
    <!-- DIAGNOSTIC MODEL -->
    <div id="diagnostic_model" style="display: none;" class="fields diagnostic-fields_new">
        <input type="hidden" name="diagnostics[id][]">

        <div class="required field">
            <label>Código</label>
            <div class="ui search selection dropdown" id="diagnosticsCodDropdown">
                <input type="hidden" name="diagnostics[cod][]" >
                <i class="dropdown icon"></i>
                <div class="default text">Código</div>
                <div class="menu ">
                    <div class="item" data-value=""> </div>
                </div>
            </div>
        </div>


        <div class="eight wide required field">
            <label>Nombre</label>
            <input class="description medical-input-to-lower-case readonly" name="diagnostics[description][]" type="text" readonly>
        </div>
        <div class="eight wide required field">
            <label>Descripción</label>
            <input class="description medical-input-to-lower-case readonly" name="diagnostics[description_editable][]" type="text" readonly>
        </div>
        <div class="three wide field">
            <label>Lateralidad</label>
            <div class="ui fluid selection dropdown">
                <input name="diagnostics[laterality][]" type="hidden">
                <i class="dropdown icon"></i>
                <div class="default text">Lateralidad</div>
                <div class="menu">
                    @foreach($LATERALITY as $k=>$r)
                        <div class="item" data-value="{{$k}}">{{$r}}</div>
                    @endforeach
                </div>
            </div>
        </div>
        <div class="five wide required field">
            <label>Origen</label>
            <div class="ui fluid selection dropdown">
                <input name="diagnostics[origin][]" type="hidden">
                <i class="dropdown icon"></i>
                <div class="default text">Origen</div>
                <div class="menu">
                    @foreach($ORIGIN_DIAGNOSIS_UPDATED as $k=>$r)
                        <div class="item" data-value="{{$k}}">{{$r}}</div>
                    @endforeach
                </div>
            </div>
        </div>
        <div class="five wide required field">
            <label>Estado</label>
            <div  class="ui fluid selection dropdown">
                <input name="diagnostics[diagnostic_status][]" type="hidden" >
                <i class="dropdown icon"></i>
                <div class="default text">Estado</div>
                <div class="menu">
                    @foreach($DIAGNOSTIC_STATUS as $k=>$r)
                        <div class="item" data-value="{{$k}}">{{$r}}</div>
                    @endforeach
                </div>
            </div>
        </div>
        <div class="five wide required field">
            <label>Clasificación</label>
            <div  class="ui fluid selection dropdown">
                <input name="diagnostics[clasificacion][]" type="hidden" >
                <i class="dropdown icon"></i>
                <div class="default text">Clasificación</div>
                <div class="menu">
                    @foreach($CLASIFICACION as $k=>$r)
                        <div class="item" data-value="{{$k}}">{{$r}}</div>
                    @endforeach
                </div>
            </div>
        </div>
        <div class="one wide field">
            <a style="margin-top: 25px;" class="ui red small icon basic button"><i class="remove icon"></i></a>
        </div>
    </div>
@endif
<style>
    .ui.search.diagnostic .results {
        max-height: 200px; /* Ajusta esta altura según tus necesidades */
        overflow-y: auto;
        border: 1px solid #ddd; /* Opcional: Agrega un borde para mejor visibilidad */
        padding: 5px; /* Opcional: Espaciado interno */
        background: #fff; /* Opcional: Color de fondo */
    }
</style>

@if(!$disabled)
    <script>
        const defaultDiseaseTypeDiagnosis = @json($gis->disease_report_type_id ?? null);
        const isDisabled = @json($disabled);

        $(document).ready(function () {

            $('.ui.radio.checkbox').checkbox();
            $('#dropdownReportDisease').dropdown();

            $('input[name="gis_disease_confirmation"]').change(function () {
                if ($(this).val() === '0') {
                    toggleDropdown('disable');
                } else {
                    toggleDropdown('enable');
                }
            });
        });

        function toggleDropdown(state) {

            $('#dropdownReportDisease').dropdown('refresh');

            if (state === 'disable') {
                console.log('Desactivar dropdown', defaultDiseaseTypeDiagnosis);
                $('#disease_report_type_id').val(defaultDiseaseTypeDiagnosis);
                $('#dropdownReportDisease').dropdown('set selected', defaultDiseaseTypeDiagnosis);
                $('#dropdownReportDisease').addClass('disabled');
            } else {
                console.log('Activar dropdown');
                $('#dropdownReportDisease').removeClass('disabled');
                $('#dropdownReportDisease').dropdown('clear');
                $('#disease_report_type_id').val('');
                $('#dropdownReportDisease').dropdown('set selected', '');
            }

        }

    </script>
@endif
