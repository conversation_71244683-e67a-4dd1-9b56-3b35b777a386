<div class="title">
    <i class="dropdown icon"></i>
    Receta médica psicotrópicos y estupefacientes
    @if(!$disabled)
        <i class="info circle icon info_receta_medica_estupefacientes"></i>
    @endif
</div>
<div class="content">
    <!-- Primera línea con 3 search-->
    <div class="location-group" data-group-id="6">
        <div class="field">
            <div class="four fields">
                <div class="field required">
                    <label>Provincia</label>
                    <div class="province ui search selection dropdown @if($disabled) disabled @endif">
                        <input type="hidden" class="minus_font province_value"
                               name="province_controlled_medication"
                               value="{{$medical_service->province_controlled_medication ?? ''}}">
                        <i class="dropdown icon"></i>
                        <div class="default text">Selecciona uno</div>
                        <div class="menu"></div>
                    </div>
                </div>
                <div class="field required">
                    <label>Cantón</label>
                    <div class="canton ui search selection dropdown @if($disabled) disabled @endif">
                        <input type="hidden" name="canton_controlled_medication" class="minus_font canton_value"
                               value="{{$medical_service->canton_controlled_medication ?? ''}}">
                        <i class="dropdown icon"></i>
                        <div class="default text">Selecciona uno</div>
                        <div class="menu"></div>
                    </div>
                </div>
                <div class="field required">
                    <label>Distrito</label>
                    <div class="district ui search selection dropdown @if($disabled) disabled @endif">
                        <input type="hidden" name="district_controlled_medication"
                               class="minus_font district_value"
                               value="{{$medical_service->district_controlled_medication ?? ''}}">
                        <i class="dropdown icon"></i>
                        <div class="default text">Selecciona uno</div>
                        <div class="menu"></div>
                    </div>
                </div>
                @if($disabled !== true)
{{--                    <div class="field">--}}
{{--                        <label>Farmacias.....</label>--}}
{{--                        <div class="ui search farmacias_other">--}}
{{--                            <div class="ui icon input">--}}
{{--                                <input--}}
{{--                                        class="prompt minus_font"--}}
{{--                                        type="text" name="pharmacies_branch_controlled_medication"--}}
{{--                                        value="{{ $medical_service->pharmacies_branch_controlled_medication ?? '' }}"--}}
{{--                                        placeholder="Buscar por código o sucursal">--}}
{{--                                <i class="search icon"></i>--}}
{{--                            </div>--}}
{{--                            <div class="results"></div>--}}
{{--                        </div>--}}
{{--                    </div>--}}


                    <div class="field">
                        <label>Farmacias</label>
                        <div class="ui search selection dropdown farmaciasCodDropdown" id="farmaciasCodDropdown">
                            <input type="hidden" name="pharmacies_branch_controlled_medication" value="{{ $medical_service->pharmacies_branch_controlled_medication ?? ''  }}">
                            <i class="dropdown icon"></i>
                            <div class="default text">Buscar por código o sucursal</div>
                            <div class="menu ">
                                <div class="item" data-value=""> </div>
                            </div>
                        </div>
                    </div>

                @else
                   <div class="field">
                       <label>Farmacias</label>
                       <div class="ui search">
                           <div class="ui input">
                               <input class="prompt readonly" type="text" value="{{ $medical_service->pharmacies_branch_controlled_medication ?? '' }}" readonly>
                           </div>
                       </div>
                   </div>
                @endif
                @if($disabled !== true)
                    <!-- Botón de limpiar -->
                    <div class="wide two field" style="margin-top: 25px;">
                        <button id="clear-location-button" class="ui red small icon fluid button" type="button"
                                onclick="clearLocationFields(this)">
                            <i class="undo icon"></i>
                            Limpiar
                        </button>
                    </div>
                @endif
            </div>
        </div>
    </div>
    <!-- Segunda línea con 1 textarea -->
    <div class="fields">
        <div class="sixteen wide field">
            <div class="field">
                <div class="field required">
                    <label for="diagnosis-origin-1">Origen de los diagnósticos</label>
                    <textarea class="@if(!empty($disabled)) textarea-expand @else auto-resize-expanded @endif" name="{{!empty($disabled) ? '' : 'diagnosis_origin_controlled_medication' }}" id="diagnosis_origin-1"
                              placeholder="" @if(!empty($disabled)) readonly @else rows="1" @endif>{{$medical_service->diagnosis_origin_controlled_medication ?? ''}}</textarea>
                </div>
            </div>
        </div>
    </div>
    <div class="ui grid">
        <div class="ui grid" id="medical-form-container" style="margin-top: 15px;">
            <!-- Contenedor dinámico de los medicamentos -->
            <div class="content" id="medicine-list_{{ $controlled_medications ? 1 : 'empty' }}">
                <!-- Formulario inicial -->
                @if(count($controlled_medications) > 0)
                    @foreach($controlled_medications as $index => $medicine)
                        <div class="ui grid medicine-item">
                            <!-- Primera línea: Etiqueta Medicamento N° -->
                            <div class="two column row" style="margin-top: 15px;">
                                <div class="column">
                                    <h4 class="medicine-title">Medicamento N° {{ $index + 1 }}</h4>
                                </div>
                                @if($index > 0 && $disabled !== true)
                                    <div class="column right aligned">
                                        <a class="ui red small icon basic right floated button remove-medicine"
                                           style="margin-top: 5px;">
                                            <i class="remove icon"></i>
                                        </a>
                                    </div>
                                @endif
                            </div>
                            @if(!empty($medicine->generic_code) && !empty($medicine->generic_name) && !empty($medicine->concentration) && !empty($medicine->pharmaceutical_form))
                                <div class="four column row">
                                    <div class="column">
                                        <div class="ui form">
                                            <div class="field required">
                                                <label>Código genérico</label>
                                                <div class="ui search search genericCodeInput">
                                                    <div class="ui icon input">
                                                        <input id="generic-code-1[{{ $index + 1 }}]"
                                                               class="prompt minus_font"
                                                               type="text" name="generic-code[{{ $index + 1 }}]"
                                                               value="{{ $medicine->generic_code }}" @if($disabled) disabled @endif>
                                                        <i class="search icon"></i>
                                                    </div>
                                                    <div class="results"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="column">
                                        <div class="field required">
                                            <label>Nombre genérico</label>
                                            <textarea class="auto-resize" id="generic-name[{{ $index + 1 }}]"
                                                      name="generic-name[{{ $index + 1 }}]" rows="1"
                                                      readonly @if($disabled) disabled @endif>{{ $medicine->generic_name }}</textarea>
                                        </div>
                                    </div>
                                    <div class="column">
                                        <div class="ui form">
                                            <div class="field required">
                                                <label for="concentration-0">Concentración</label>
                                                <textarea class="auto-resize" name="concentration[{{ $index + 1 }}]" id="concentration-1"
                                                          placeholder=""
                                                          readonly rows="1" @if($disabled) disabled @endif>{{ $medicine->concentration }}</textarea>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="column">
                                        <div class="ui form">
                                            <div class="field required">
                                                <label for="forma-farmaceutica-1">Forma farmacéutica</label>
                                                <textarea class="auto-resize" name="pharmaceutical-form[{{ $index + 1 }}]"
                                                          id="pharmaceutical-form-1"
                                                          placeholder=""
                                                          readonly rows="1" @if($disabled) disabled @endif>{{ $medicine->pharmaceutical_form }}</textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="two column row">
                                    <div class="column">
                                        <div class="ui form">
                                            <div class="field required">
                                                <label for="duracion-tratamiento-1">Duración del tratamiento</label>
                                                <textarea class="auto-resize" name="duracion_tratamiento[{{ $index + 1 }}]"
                                                          id="duracion-tratamiento-1"
                                                          placeholder="" rows="1" @if($disabled) disabled @endif>{{ $medicine->treatment_duration }}</textarea>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="column">
                                        <div class="ui form">
                                            <div class="field required">
                                                <label for="frecuencia-1">Frecuencia</label>
                                                <textarea class="auto-resize" name="frecuencia[{{ $index + 1 }}]" id="frecuencia-1"
                                                          placeholder="" rows="1" @if($disabled) disabled @endif>{{ $medicine->frequency }}</textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="two column row">
                                    <div class="column">
                                        <div class="ui form">
                                            <div class="field required">
                                                <label for="dosis-1">Dosis/ vía de administración</label>
                                                <textarea class="auto-resize" name="dosis[{{ $index + 1 }}]" id="dosis-1"
                                                          placeholder="" rows="1" @if($disabled) disabled @endif>{{ $medicine->dosage }}</textarea>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="column">
                                        <div class="ui form">
                                            <div class="field required">
                                                <label for="cantidad-letras-1">Cantidad prescrita letras</label>
                                                <textarea class="auto-resize" name="cantidad-letras[{{ $index + 1 }}]"
                                                          id="cantidad-letras-1"
                                                          placeholder="" rows="1" @if($disabled) disabled @endif>{{ $medicine->quantity_letters }}</textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="two column row">
                                    <div class="column">
                                        <div class="ui form">
                                            <div class="field required">
                                                <label for="cantidad-numeros-1">Cantidad prescrita números</label>
                                                <textarea class="auto-resize" name="cantidad-numeros[{{ $index + 1 }}]"
                                                          id="cantidad-numeros-1"
                                                          placeholder="" rows="1" @if($disabled) disabled @endif>{{ $medicine->quantity_numbers }}</textarea>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="column">
                                        <div class="ui form">
                                            <div class="field required">
                                                <label for="notas-1">Notas aclaratorias</label>
                                                <textarea class="auto-resize" name="notas[{{ $index + 1 }}]" id="notas-1"
                                                          placeholder="" rows="1" @if($disabled) disabled @endif>{{ $medicine->notes }}</textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @else
                                <div class="three column row">
                                    <!-- Campo de moleculas de vademecum -->
                                    <div class="column">
                                        <div class="ui form">
                                            <div class="field">
                                                <label>Mólecula</label>
                                                <input type="text" class="readonly"
                                                       value="{{$medicine->molecula ?? ''}}" readonly>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- Campo para el tipo -->
                                    <div class="column">
                                        <div class="ui form">
                                            <div class="field">
                                                <label>Tipo</label>
                                                <input type="text" name="number_electronic_invoice" class="readonly"
                                                       value="{{$medicine->tipo ?? ''}}" readonly>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- Campo para la descripción (descripVademecum) con la estructura de search -->
                                    <div class="column wide ">
                                        <div class="ui form">
                                            <div class="field">
                                                <label>Descripción</label>
                                                <input type="text" class="readonly"
                                                       value="{{$medicine->descrip ?? ''}}" readonly>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="three column row">
                                    <!-- Campo código de vademecum-->
                                    <div class="column">
                                        <div class="field">
                                            <label>Código</label>
                                            <textarea class="textarea-expand"
                                                      readonly>{{$medicine->codigo ?? ''}}</textarea>
                                        </div>
                                    </div>
                                    <!-- Campo casa de vademecum-->
                                    <div class="column">
                                        <div class="field">
                                            <label>Casa</label>
                                            <textarea class="textarea-expand"
                                                      readonly>{{$medicine->casa ?? ''}}</textarea>
                                        </div>
                                    </div>

                                    <div class="column">
                                        <div class="ui form">
                                            <div class="field">
                                                <label>Duración del tratamiento</label>
                                                <textarea class="textarea-expand" readonly>{{$medicine->treatment_duration ?? ''}}</textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="three column row">
                                    <div class="column">
                                        <div class="ui form">
                                            <div class="field">
                                                <label>Frecuencia</label>
                                                <textarea class="textarea-expand" readonly>{{$medicine->frequency ?? ''}}</textarea>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="column">
                                        <div class="ui form">
                                            <div class="field">
                                                <label>Dosis/ vía de administración</label>
                                                <textarea class="textarea-expand" readonly>{{$medicine->dosage ?? ''}}</textarea>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="column">
                                        <div class="ui form">
                                            <div class="field">
                                                <label>Cantidad prescrita letras</label>
                                                <textarea class="textarea-expand" readonly>{{$medicine->quantity_letters ?? ''}}</textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="three column row">
                                    <div class="column">
                                        <div class="ui form">
                                            <div class="field">
                                                <label>Cantidad prescrita números</label>
                                                <textarea class="textarea-expand" readonly>{{$medicine->quantity_numbers ?? ''}}</textarea>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="column">
                                        <div class="ui form">
                                            <div class="field">
                                                <label>Notas aclaratorias</label>
                                                <textarea class="textarea-expand" readonly>{{$medicine->notes ?? ''}}</textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endif
                        </div>
                    @endforeach
                @elseif($disabled !== true)
                    <div class="ui grid medicine-item vademecum-group" data-group-id="medical-controlled-medication-1">
                        <!-- Primera línea: Etiqueta Medicamento N° -->
                        <div class="two column row">
                            <div class="column">
                                <h4 class="medicine-title">Medicamento N° 1</h4>
                            </div>
                        </div>
                        <div class="three column row">
                            <!-- Campo de moleculas de vademecum -->
                            <div class="column">
                                <div class="ui form">
                                    <div class="required field">
                                        <label>Mólecula</label>
                                        <div class="moleculaVademecum ui search selection dropdown">
                                            <input type="hidden" class="minus_font moleculaVademecum_value" name="molecula-controlled-medication[1]" value=""
                                                   onchange="handleMoleculaChange($(this).closest('.vademecum-group'))">
                                            <i class="dropdown icon"></i>
                                            <div class="default text">Selecciona una molécula</div>
                                            <div class="menu"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- Campo para el tipo -->
                            <div class="column">
                                <div class="ui form">
                                    <div class="required field">
                                        <label>Tipo</label>
                                        <div class="tipoVademecum ui search selection dropdown">
                                            <input type="hidden" class="minus_font tipoVademecum_value" value="" name="tipo-controlled-medication[1]"
                                                   onchange="handleTipoChange($(this).closest('.vademecum-group'))">
                                            <i class="dropdown icon"></i>
                                            <div class="default text">Selecciona un tipo</div>
                                            <div class="menu"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- Campo para la descripción (descripVademecum) con la estructura de search -->
{{--                            <div class="column wide ">--}}
{{--                                <div class="ui form">--}}
{{--                                    <div class="required field">--}}
{{--                                        <label>Descripción===</label>--}}
{{--                                        <div class="ui search search descripVademecum">--}}
{{--                                            <div class="ui icon input">--}}
{{--                                                <input id="descrip-prescription-[1]"--}}
{{--                                                       class="prompt minus_font"--}}
{{--                                                       type="text" name="descrip-controlled-medication[1]"--}}
{{--                                                       value="">--}}
{{--                                                <i class="search icon"></i>--}}
{{--                                            </div>--}}
{{--                                            <div class="results"></div>--}}
{{--                                        </div>--}}
{{--                                    </div>--}}
{{--                                </div>--}}
{{--                            </div>--}}

                            <div class="column wide ">
                                <div class="ui form">
                                    <div class="required field">
                                        <label>Descripción</label>
                                        <div class="ui search selection dropdown descripVademecumDropdown" id="descripVademecumDropdown">
                                            <input type="hidden" id="descrip-prescription-[1]" name="descrip-controlled-medication[1]" >
                                            <i class="dropdown icon"></i>
                                            <div class="default text"></div>
                                            <div class="menu ">
                                                <div class="item" data-value=""> </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>
                        <div class="three column row">
                            <!-- Campo código de vademecum-->
                            <div class="column">
                                <div class="required field">
                                    <label>Código</label>
                                    <textarea class="auto-resize medication-input-to-lower-case readonly"
                                              name="codigo-vademecum[1]" rows="1"
                                              readonly></textarea>
                                </div>
                            </div>
                            <!-- Campo casa de vademecum-->
                            <div class="column">
                                <div class="required field">
                                    <label>Casa</label>
                                    <textarea class="auto-resize medication-input-to-lower-case readonly"
                                              name="casa-vademecum[1]" rows="1"
                                              readonly></textarea>
                                </div>
                            </div>

                            <div class="column">
                                <div class="ui form">
                                    <div class="required field">
                                        <label>Duración del tratamiento</label>
                                        <textarea class="auto-resize" name="duracion_tratamiento[1]"
                                                  placeholder="" rows="1"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="three column row">
                            <div class="column">
                                <div class="ui form">
                                    <div class="required field">
                                        <label>Frecuencia</label>
                                        <textarea class="auto-resize" name="frecuencia[1]"
                                                  placeholder="" rows="1"></textarea>
                                    </div>
                                </div>
                            </div>
                            <div class="column">
                                <div class="ui form">
                                    <div class="required field">
                                        <label>Dosis/ vía de administración</label>
                                        <textarea class="auto-resize" name="dosis[1]"
                                                  placeholder="" rows="1"></textarea>
                                    </div>
                                </div>
                            </div>
                            <div class="column">
                                <div class="ui form">
                                    <div class="required field">
                                        <label>Cantidad prescrita letras</label>
                                        <textarea class="auto-resize" name="cantidad-letras[1]"
                                                  placeholder="" rows="1"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="three column row">
                            <div class="column">
                                <div class="ui form">
                                    <div class="required field">
                                        <label>Cantidad prescrita números</label>
                                        <textarea class="auto-resize" name="cantidad-numeros[1]"
                                                  placeholder="" rows="1"></textarea>
                                    </div>
                                </div>
                            </div>
                            <div class="column">
                                <div class="ui form">
                                    <div class="required field">
                                        <label>Notas aclaratorias</label>
                                        <textarea class="auto-resize" name="notas[1]"
                                                  placeholder="" rows="1"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>
    @if($disabled !== true)
        <div class="fields">
            <div class="field two wide" style="margin-top: 25px;">
                <a style="margin-top: 0;" onclick="addMedicine()"
                   class="ui basic small icon blue fluid button">
                    <i class="add icon"></i>Agregar
                </a>
            </div>
        </div>
    @endif
</div>
@if(!$disabled)
    <!-- Template oculto para duplicar -->
    <div class="ui grid medicine-item" data-group-id="" id="medicine-template" style="display: none;">
        <!-- Primera línea: Etiqueta Medicamento N° y botón Eliminar -->
        <div class="two column row" style="margin-top: 15px;">
            <div class="column">
                <h4 class="medicine-title">Medicamento N° <span class="medicine-number"></span></h4>
            </div>
            <div class="column right aligned">
                <a class="ui red small icon basic right floated button remove-medicine" style="margin-top: 5px;">
                    <i class="remove icon"></i>
                </a>
            </div>
        </div>
        <div class="three column row">
            <!-- Campo de moleculas de vademecum -->
            <div class="column">
                <div class="ui form">
                    <div class="required field">
                        <label>Mólecula</label>
                        <div class="moleculaVademecum ui search selection dropdown">
                            <input type="hidden" class="minus_font moleculaVademecum_value" name="molecula-controlled-medication[]" value=""
                                   onchange="handleMoleculaChange($(this).closest('.vademecum-group'))">
                            <i class="dropdown icon"></i>
                            <div class="default text">Selecciona una molécula</div>
                            <div class="menu"></div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Campo para el tipo -->
            <div class="column">
                <div class="ui form">
                    <div class="required field">
                        <label>Tipo</label>
                        <div class="tipoVademecum ui search selection dropdown">
                            <input type="hidden" class="minus_font tipoVademecum_value" value="" name="tipo-controlled-medication[]"
                                   onchange="handleTipoChange($(this).closest('.vademecum-group'))">
                            <i class="dropdown icon"></i>
                            <div class="default text">Selecciona un tipo</div>
                            <div class="menu"></div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Campo para la descripción (descripVademecum) con la estructura de search -->
{{--            <div class="column wide ">--}}
{{--                <div class="ui form">--}}
{{--                    <div class="required field">--}}
{{--                        <label>Descripción</label>--}}
{{--                        <div class="ui search search descripVademecum">--}}
{{--                            <div class="ui icon input">--}}
{{--                                <input id="descrip-prescription-[]"--}}
{{--                                       class="prompt minus_font"--}}
{{--                                       type="text" name="descrip-controlled-medication[]"--}}
{{--                                       value="">--}}
{{--                                <i class="search icon"></i>--}}
{{--                            </div>--}}
{{--                            <div class="results"></div>--}}
{{--                        </div>--}}
{{--                    </div>--}}
{{--                </div>--}}
{{--            </div>--}}

            <div class="column wide ">
                <div class="ui form">
                    <div class="required field">
                        <label>Descripción</label>
                        <div class="ui search selection dropdown descripVademecumDropdown" id="descripVademecumDropdown">
                            <input type="hidden" id="descrip-prescription-[]" name="descrip-controlled-medication[]" >
                            <i class="dropdown icon"></i>
                            <div class="default text"></div>
                            <div class="menu ">
                                <div class="item" data-value=""> </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
        <div class="three column row">
            <!-- Campo código de vademecum-->
            <div class="column">
                <div class="required field">
                    <label>Código</label>
                    <textarea class="auto-resize medication-input-to-lower-case readonly"
                              name="codigo-vademecum[]" rows="1"
                              readonly></textarea>
                </div>
            </div>
            <!-- Campo casa de vademecum-->
            <div class="column">
                <div class="required field">
                    <label>Casa</label>
                    <textarea class="auto-resize medication-input-to-lower-case readonly"
                              name="casa-vademecum[]" rows="1"
                              readonly></textarea>
                </div>
            </div>

            <div class="column">
                <div class="ui form">
                    <div class="required field">
                        <label>Duración del tratamiento</label>
                        <textarea class="auto-resize" name="duracion_tratamiento[]"
                                  placeholder="" rows="1"></textarea>
                    </div>
                </div>
            </div>
        </div>
        <div class="three column row">
            <div class="column">
                <div class="ui form">
                    <div class="required field">
                        <label>Frecuencia</label>
                        <textarea class="auto-resize" name="frecuencia[]"
                                  placeholder="" rows="1"></textarea>
                    </div>
                </div>
            </div>
            <div class="column">
                <div class="ui form">
                    <div class="required field">
                        <label>Dosis/ vía de administración</label>
                        <textarea class="auto-resize" name="dosis[]"
                                  placeholder="" rows="1"></textarea>
                    </div>
                </div>
            </div>
            <div class="column">
                <div class="ui form">
                    <div class="required field">
                        <label>Cantidad prescrita letras</label>
                        <textarea class="auto-resize" name="cantidad-letras[]"
                                  placeholder="" rows="1"></textarea>
                    </div>
                </div>
            </div>
        </div>
        <div class="three column row">
            <div class="column">
                <div class="ui form">
                    <div class="required field">
                        <label>Cantidad prescrita números</label>
                        <textarea class="auto-resize" name="cantidad-numeros[]"
                                  placeholder="" rows="1"></textarea>
                    </div>
                </div>
            </div>
            <div class="column">
                <div class="ui form">
                    <div class="required field">
                        <label>Notas aclaratorias</label>
                        <textarea class="auto-resize" name="notas[]"
                                  placeholder="" rows="1"></textarea>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let cumData = [];
        let maxDuplicate = 3; // Máximo de duplicados permitidos
        let currentMedicineCount = {{ count($controlled_medications) > 0 ? count($controlled_medications) : 1 }}; // Inicial basado en datos

        // Validar si los campos están completos
        function isFormComplete($fields) {
            let isComplete = true;
            $fields.find('textarea').each(function () {
                if ($(this).val().trim() === '') {
                    isComplete = false;
                }
            });
            return isComplete;
        }

        // Función para agregar un nuevo medicamento
        let addMedicine = function () {
            if (currentMedicineCount < maxDuplicate) {
                let $lastMedicine = $('#medicine-list_empty .medicine-item').last();
                if (isFormComplete($lastMedicine)) {
                    currentMedicineCount++;

                    let counter1 = currentMedicineCount;
                    let $fields = $('#medicine-template').clone();
                    $fields.removeAttr('id');
                    $fields.attr('data-group-id', 'medical-controlled-medication-' + currentMedicineCount); // Asigna un identificador único
                    $fields.addClass('vademecum-group'); // Agregar la clase "vademecum-group"
                    $fields.find('.medicine-number').text(counter1);

                    // Cambiar los atributos name y id para evitar colisiones
                    $fields.find('input[name="molecula-controlled-medication[]"]').attr('name', 'molecula-controlled-medication[' + counter1 + ']').attr('id', 'molecula-controlled-medication-' + counter1);
                    $fields.find('input[name="tipo-controlled-medication[]"]').attr('name', 'tipo-controlled-medication[' + counter1 + ']').attr('id', 'tipo-controlled-medication-' + counter1);
                    $fields.find('input[name="descrip-controlled-medication[]"]').attr('name', 'descrip-controlled-medication[' + counter1 + ']').attr('id', 'descrip-controlled-medication-' + counter1);
                    $fields.find('textarea[name="codigo-vademecum[]"]').attr('name', 'codigo-vademecum[' + counter1 + ']').attr('id', 'codigo-vademecum-' + counter1);
                    $fields.find('textarea[name="casa-vademecum[]"]').attr('name', 'casa-vademecum[' + counter1 + ']').attr('id', 'casa-vademecum-' + counter1);
                    $fields.find('textarea[name="duracion_tratamiento[]"]').attr('name', 'duracion_tratamiento[' + counter1 + ']').attr('id', 'duracion-tratamiento-' + counter1);
                    $fields.find('textarea[name="frecuencia[]"]').attr('name', 'frecuencia[' + counter1 + ']').attr('id', 'frecuencia-' + counter1);
                    $fields.find('textarea[name="dosis[]"]').attr('name', 'dosis[' + counter1 + ']').attr('id', 'dosis-' + counter1);
                    $fields.find('textarea[name="cantidad-letras[]"]').attr('name', 'cantidad-letras[' + counter1 + ']').attr('id', 'cantidad-letras-' + counter1);
                    $fields.find('textarea[name="cantidad-numeros[]"]').attr('name', 'cantidad-numeros[' + counter1 + ']').attr('id', 'cantidad-numeros-' + counter1);
                    $fields.find('textarea[name="notas[]"]').attr('name', 'notas[' + counter1 + ']').attr('id', 'notas-' + counter1);

                    $fields.find('textarea').val(''); // Limpiar todos los textarea

                    $fields.appendTo('#medicine-list_empty');
                    $fields.show();

                    // Llamar a la función de inicialización solo para el nuevo grupo
                    initializeDropdownsAndFields($fields); // Pasar el nuevo grupo como argumento

                    updateIndexes();
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Campos requeridos',
                        html: "Por favor, completa todos los campos antes de agregar un nuevo medicamento.",
                        confirmButtonText: 'Aceptar',
                         confirmButtonColor: '#91c845'
                    });
                }
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Limite de medicamentos alcanzado',
                    html: "Has alcanzado el número máximo de medicamentos permitidos.",
                    confirmButtonText: 'Aceptar',
                     confirmButtonColor: '#91c845'
                });
            }
        };

        // Función para eliminar medicamentos
        $(document).on('click', '.remove-medicine', function () {
            $(this).closest('.medicine-item').remove();
            currentMedicineCount--;
            updateIndexes(); // Actualiza los índices tras eliminar un medicamento
        });

        // Función para actualizar los índices de los medicamentos clonados
        let updateIndexes = function () {
            $('#medicine-list_empty .medicine-item').each(function (index) {
                let medicineNumber = index + 1; // El número de medicamento comienza en 1
                $(this).find('.medicine-title').text('Medicamento N° ' + medicineNumber); // Actualizar el número en el título

                // Actualizar los atributos name y id de los campos relacionados
                $(this).find('textarea').each(function () {
                    let nameAttr = $(this).attr('name');
                    let idAttr = $(this).attr('id');

                    // Verificar si los atributos 'name' y 'id' están definidos
                    if (nameAttr && idAttr) {
                        let newName = nameAttr.replace(/\[\d+\]/, '[' + medicineNumber + ']'); // Reemplaza el índice en el atributo name
                        $(this).attr('name', newName);

                        let newId = idAttr.replace(/-\d+$/, '-' + medicineNumber); // Reemplaza el índice en el atributo id
                        $(this).attr('id', newId);
                    }
                });
            });
        };
    </script>

    <script>
        let farmaciaData = [];

        $(document).ready(function () {

            $.getJSON('/js/zonaFLB_NFF.json', function (json) {
                farmaciaData = json.List.map(item => ({
                    codigo: item.codigo,
                    sucursal: capitalizeFirstLetter(item.sucursal),
                    supervisor: capitalizeFirstLetter(item.supervisor),
                    SEARCH_TEXT: (item.codigo + ' ' + item.sucursal).toLowerCase()
                }));

                //const dropdown = $('#diagnosticsCodDropdown');

                $('.farmaciasCodDropdown').each(function () {
                    const dropdown = $(this);

                    dropdown.dropdown({
                        minCharacters: 1,
                        fullTextSearch: false,
                        forceSelection: true,
                        apiSettings: {
                            responseAsync: function (settings, callback) {
                                const query = settings.urlData.query.toLowerCase();
                                const filtered = farmaciaData
                                    .filter(item => item.SEARCH_TEXT.includes(query))
                                    .slice(0, 10);
                                const results = filtered.map(item => ({
                                    name: item.codigo + ' - ' + item.sucursal,
                                    value: item.sucursal
                                }));
                                callback({ success: true, results });
                            }
                        },
                        onChange: function (value, text, $choice) {
                            const selectedItem = farmaciaData.find(item => item.sucursal === value);
                            if (selectedItem) {
                                const container = $(this).closest('.fields');
                                container.find('input.description').val(selectedItem.sucursal);
                            }
                        }
                    });

                    // Preseleccionar valor
                    //const preselectedInput = dropdown.find('input[type="hidden"][name="diagnostics[cod][]"]');
                    // const preselectedInput = dropdown.find('input[type="hidden"][name="pharmacies_branch_controlled_medication"]');
                    // const preselectedValue = preselectedInput.val();
                    //
                    // if (preselectedValue) {
                    //     const selectedItem = farmaciaData.find(item => item.codigo === preselectedValue);
                    //     if (selectedItem) {
                    //         const menu = dropdown.find('.menu');
                    //
                    //         // Agrega el item al menú si no existe
                    //         if (menu.find(`.item[data-value="${selectedItem.codigo}"]`).length === 0) {
                    //             const newItem = $('<div>', {
                    //                 class: 'item',
                    //                 'data-value': selectedItem.codigo,
                    //                 text: selectedItem.codigo + ' - ' + selectedItem.sucursal
                    //             });
                    //             menu.append(newItem);
                    //         }
                    //
                    //         // Establece el valor manualmente en el input hidden
                    //         preselectedInput.val(selectedItem.codigo);//
                    //
                    //         dropdown.dropdown('refresh');
                    //         // Luego selecciona ese valor
                    //         dropdown.dropdown('set selected', selectedItem.codigo);
                    //
                    //     }
                    // }
                });
            });
        });
    </script>


    <script>
        $('.info_receta_medica_estupefacientes').popup({
            boundary: 'body',
            content: 'Si diligencia uno o más campos en la pestaña Receta médica psicotrópicos y estupefacientes , todos los campos de esa pestaña serán obligatorios',
            position: 'top center',
            lastResort: 'bottom center' // Si no cabe en la posición inicial, se ajusta a esta
        });
    </script>
@endif
<style>
    .ui.search.genericCodeInput .results {
        max-height: 200px; /* Ajusta esta altura según tus necesidades */
        overflow-y: auto;
        border: 1px solid #ddd; /* Opcional: Agrega un borde para mejor visibilidad */
        padding: 5px; /* Opcional: Espaciado interno */
        background: #fff; /* Opcional: Color de fondo */
    }

    .ui.search > .results {
        width: 100% !important;
    }
</style>



