<div class="title">
    <i class="dropdown icon"></i>
    Orden de imágenes diagnósticas
    @if(!$disabled)
        <i class="info circle icon info_imagenes_diagnosticas"></i>
    @endif
</div>
<div class="content form" @if($disabled !== true) id ="order-diagnostic-images" @endif>
    <div class="field">
        <div class="location-group" data-group-id="3">
            <div class="three fields">
                <div class="field required">
                    <label for="code-ips">Provincia</label>
                    <div class="province ui search selection dropdown @if($disabled) disabled @endif">
                        <input type="hidden" class="minus_font province_value" name="province_order_diagnostic" value="{{ $medical_service->province_order_diagnostic ?? '' }}">
                        <i class="dropdown icon"></i>
                        <div class="default text">Selecciona uno</div>
                        <div class="menu"></div>
                    </div>
                </div>
                <div class="field required">
                    <label for="code-ips">Cantón</label>
                    <div class="canton ui search selection dropdown @if($disabled) disabled @endif">
                        <input type="hidden" name="canton_order_diagnostic" class="minus_font canton_value" value="{{ $medical_service->canton_order_diagnostic ?? ''}}">
                        <i class="dropdown icon"></i>
                        <div class="default text">Selecciona uno</div>
                        <div class="menu"></div>
                    </div>
                </div>
                <div class="field required">
                    <label for="code-ips">Distrito</label>
                    <div class="district ui search selection dropdown @if($disabled) disabled @endif">
                        <input type="hidden" name="district_order_diagnostic" class="minus_font district_value" value="{{ $medical_service->district_order_diagnostic ?? ''}}">
                        <i class="dropdown icon"></i>
                        <div class="default text">Selecciona uno</div>
                        <div class="menu"></div>
                    </div>
                </div>
                <!-- Botón de limpiar -->
                @if($disabled !== true)
                    <div class="wide two field" style="margin-top: 25px;">
                        <button id="clear-location-button" class="ui red small icon fluid button" type="button" onclick="clearLocationFields(this)">
                            <i class="undo icon"></i>
                            Limpiar
                        </button>
                    </div>
                @endif
            </div>
        </div>
    </div>
    <div class="fields">
        <div class="sixteen wide field required">
            <label>Origen de los diagnósticos</label>
            <div class="ui search code">
                <div class="ui icon input">
                    <input class="prompt @if($disabled) readonly @endif" name="origin_diagnosis" type="text" autocomplete="off" value="{{ $medical_service->origin_diagnosis ?? '' }}" @if($disabled) readonly @endif>
                </div>
                <div class="results"></div>
            </div>
        </div>
    </div>
    <br>
    <div id="diagnostic_images_content_{{ $diagnostics_images ? 1 : 'empty' }}">
        @if (count($diagnostics_images) > 0)
            @foreach ($diagnostics_images as $index => $diagnostic)
                <div class="diagnostic-image-wrapper"> <!-- Nuevo div contenedor -->
                    <div class="fields">
                        <div class="fifteen wide field required">
                            <h4 class="diagnostic_images_title" style="margin-top: 15px; margin-bottom: 15px;">Orden de imagen diagnóstica N° {{ $index + 1 }}</h4>
                        </div>
                        @if($index > 0 && $disabled !== true)
                            <div class="one wide field required">
                                <a class="ui red small icon basic right floated button remove-diagnostic-image"  style="margin-top: 5px;">
                                    <i class="remove icon"></i>
                                </a>
                            </div>
                        @endif
                    </div>
                    <div class="fields">
                        <input type="hidden" name="diagnostics_images[id][{{ $index + 1 }}]" value="{{ $diagnostic->id }}">
                        <div class="four wide field required">
                            <label>Códe</label>
                            <div class="ui search diagnostic code">
                                <div class="ui icon input">
                                    <input class="prompt " name="diagnostics_images[cod][{{ $index + 1 }}]" type="text" value="{{ $diagnostic->cod }}" @if($disabled) disabled @endif>
                                    <i class="search icon"></i>
                                </div>
                                <div class="results"></div>
                            </div>
                        </div>
                        <div class="four wide field required">
                            <label>Descripción</label>
                            <input class="description prompt readonly" name="diagnostics_images[description][{{ $index + 1 }}]" type="text" value="{{ $diagnostic->description }}" @if($disabled) disabled @endif>
                        </div>
                        <div class="four wide field required">
                            <label>Lateralidad</label>
                            <div class="ui fluid selection dropdown @if($disabled) disabled @endif">
                                <input name="diagnostics_images[laterality][{{ $index + 1 }}]" type="hidden" value="{{ $diagnostic->laterality }}" @if($disabled) disabled @endif>
                                <i class="dropdown icon"></i>
                                <div class="default text">Lateralidad</div>
                                <div class="menu">
                                    @foreach($LATERALITY as $k => $r)
                                        <div class="item" data-value="{{ $k }}">{{ $r }}</div>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                        <div class="four wide field required">
                            <label>Cantidad</label>
                            <input type="number" class="readonly" name="diagnostics_images[quantity][{{ $index + 1 }}]" value="{{ $diagnostic->quantity }}"  disabled>
                        </div>
                    </div>
                    <div class="fields">
                        <div class="sixteen wide field required">
                            <label>Notas aclaratorias</label>
                            <textarea @if(!$disabled) name="diagnostics_images[notes][{{ $index + 1 }}]" @endif style="height: 19px;" @if($disabled) class="textarea-expand" readonly  @endif>{{ $diagnostic->notes }}</textarea>
                        </div>
                    </div>
                </div> <!-- Fin del div contenedor -->
            @endforeach
        @elseif($disabled !== true)
            <div class="diagnostic-image-wrapper"> <!-- Nuevo div contenedor -->
                <div class="fields">
                    <div class="field">
                        <h4 class="diagnostic_images_title" style="margin-top: 15px; margin-bottom: 15px;">Orden de imagen diagnóstica N° 1</h4>
                    </div>
                </div>
                <div class="fields">
                    <input type="hidden" name="diagnostics_images[id][]">
{{--                    <div class="four wide field required">--}}
{{--                        <label>Cód.===</label>--}}
{{--                        <div class="ui search diagnostic code">--}}
{{--                            <div class="ui icon input">--}}
{{--                                <input class="prompt" name="diagnostics_images[cod][]" type="text">--}}
{{--                                <i class="search icon"></i>--}}
{{--                            </div>--}}
{{--                            <div class="results"></div>--}}
{{--                        </div>--}}
{{--                    </div>--}}


                    <div class="required field">
                        <label>Cód</label>
                        <div class="ui search selection dropdown diagnosticsCodDropdown" id="diagnosticsCodDropdown">
                            <input type="hidden" name="diagnostics_images[cod][]">
                            <i class="dropdown icon"></i>
                            <div class="default text">Código</div>
                            <div class="menu ">
                                <div class="item" data-value=""> </div>
                            </div>
                        </div>
                    </div>


                    <div class="four wide field required">
                        <label>Descripción</label>
                        <input class="description prompt readonly" name="diagnostics_images[description][]" type="text" readonly>
                    </div>
                    <div class="four wide field required">
                        <label>Lateralidad</label>
                        <div class="ui fluid selection dropdown">
                            <input name="diagnostics_images[laterality][]" type="hidden">
                            <i class="dropdown icon"></i>
                            <div class="default text">Lateralidad</div>
                            <div class="menu">
                                @foreach($LATERALITY as $k => $r)
                                    <div class="item" data-value="{{$k}}">{{$r}}</div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                    <div class="four wide field required">
                        <label>Cantidad</label>
                        <input type="number" name="diagnostics_images[quantity][]">
                    </div>
                </div>
                <div class="fields">
                    <div class="sixteen wide field required">
                        <label>Notas aclaratorias</label>
                        <textarea name="diagnostics_images[notes][]" style="height: 19px;"></textarea>
                    </div>
                </div>
            </div> <!-- Fin del div contenedor -->
        @endif
    </div>
    @if($disabled !== true)
        <div class="field two wide" style="margin-top: 25px;">
            <a style="margin-top: 0;" onclick="addDiagnosticImages()" class="ui basic small icon blue fluid button">
                <i class="add icon"></i>Agregar
            </a>
        </div>
    @endif
</div>
@if(!$disabled)
    <div id="diagnostic_images_model" style="display: none; margin-top: 25px;">
        <div class="diagnostic-image-wrapper"> <!-- Nuevo div contenedor -->
            <div class="fields">
                <div class="fifteen wide field required">
                    <h4 class="diagnostic_images_title" style="margin-top: 15px; margin-bottom: 15px;">Orden de imagen diagnóstica N° 1</h4>
                </div>
                <div class="one wide field">
                    <a class="ui red small icon basic right floated button remove-diagnostic-image"  style="margin-top: 5px;">
                        <i class="remove icon"></i>
                    </a>
                </div>
            </div>
            <div class="fields">
                <div class="four wide field required">
                    <label>Cód.8888</label>
                    <div class="ui search diagnostic code">
                        <div class="ui icon input">
                            <input class="prompt" name="diagnostics_images[cod][]" type="text">
                            <i class="search icon"></i>
                        </div>
                        <div class="results"></div>
                    </div>
                </div>
                <div class="four wide field required">
                    <label>Descripción</label>
                    <input class="description prompt readonly" name="diagnostics_images[description][]" type="text" readonly>
                </div>
                <div class="four wide field required">
                    <label>Lateralidad</label>
                    <div class="ui fluid selection dropdown">
                        <input name="diagnostics_images[laterality][]" type="hidden">
                        <i class="dropdown icon"></i>
                        <div class="default text">Lateralidad</div>
                        <div class="menu">
                            @foreach($LATERALITY as $k => $r)
                                <div class="item" data-value="{{$k}}">{{$r}}</div>
                            @endforeach
                        </div>
                    </div>
                </div>
                <div class="four wide field required">
                    <label>Cantidad</label>
                    <input type="number" name="diagnostics_images[quantity][]">
                </div>
            </div>
            <div class="fields">
                <div class="sixteen wide field required">
                    <label>Notas aclaratorias</label>
                    <textarea name="diagnostics_images[notes][]" style="height: 19px;"></textarea>
                </div>
            </div>
        </div> <!-- Fin del div contenedor -->
    </div>
    <script>
        let diagnosticImagesData = []; // Datos para el dropdown de imágenes diagnósticas
        let maxDiagnosticImages = 3; // Máximo de imágenes diagnósticas permitidas
        let diagnosticImageCount = 1; // Contador inicial de imágenes diagnósticas
        //formatear cadenas
        function formatString(str) {
            if (!str) return ''; // Maneja el caso de cadenas vacías
            return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
        }
        // Cargar datos del archivo JSON para el dropdown de diagnósticos
        $.getJSON('/js/cie10.json', function (data) {
            diagnosticImagesData = data.map(item => ({
                ...item,
                DESCRIPTION: formatString(item.DESCRIPTION) // Formatea la descripción al cargar
            }));
            initDropdownsDiagnosticImages(); // Inicializa los dropdowns tras cargar los datos
        });

        // Función para inicializar el dropdown de imágenes diagnósticas
        function initDropdownsDiagnosticImages($context) {
            let $dropdowns = $context ? $context.find('.ui.search.diagnostic, .ui.fluid.selection.dropdown') : $('.ui.search.diagnostic, .ui.fluid.selection.dropdown');

            $dropdowns.each(function () {
                let $dropdown = $(this);
                let $descriptionInput = $dropdown.closest('.fields').find('input.description');

                // Manejo del dropdown de código
                if ($dropdown.hasClass('search')) {
                    $dropdown.dropdown({
                        forceSelection: false,
                        onChange: function (value) {
                            let selectedItem = diagnosticImagesData.find(item => item.COD === value);
                            $descriptionInput.val(selectedItem ? selectedItem.DESCRIPTION : '');
                        }
                    });

                    // Manejo del evento de cambio en el campo de código
                    $dropdown.find('input').change(function () {
                        let valid = false;
                        $descriptionInput.val('');

                        const inputValue = $(this).val(); // Captura el valor ingresado
                        for (let i = 0; i < diagnosticImagesData.length; i++) {
                            if (diagnosticImagesData[i].COD == inputValue) {
                                $descriptionInput.val(diagnosticImagesData[i].DESCRIPTION || '');
                                valid = true;
                                break;
                            }
                        }

                        // Si no es válido, limpiar el input
                        if (!valid && $(this).val() !== '') {
                            $(this).val('');
                        }
                    });

                    // Configurar la búsqueda en el dropdown
                    $dropdown.search({
                        source: diagnosticImagesData,
                        fields: {
                            title: 'COD',
                            description: 'DESCRIPTION'
                        },
                        searchFields: ['COD', 'DESCRIPTION'],
                        showNoResults: true,
                        searchFullText: false,
                        error: {
                            noResults: 'No se encontraron resultados para tu búsqueda.'
                        },
                        onSelect: function (result) {
                            $descriptionInput.val(result.DESCRIPTION);
                        }
                    });
                }
                // Manejo del dropdown de lateralidad
                else if ($dropdown.hasClass('fluid selection dropdown')) {
                    $dropdown.dropdown({
                        onChange: function(value) {
                            // Aquí puedes manejar cualquier lógica adicional si es necesario
                        }
                    });
                }
            });
        }

        // Función para agregar una nueva imagen diagnóstica
        function addDiagnosticImages() {
            // Validar si ya se alcanzó el máximo de imágenes diagnósticas
            if (diagnosticImageCount >= maxDiagnosticImages) {
                Swal.fire({
                    icon: 'error',
                    title: 'Límite alcanzado',
                    text: 'Has alcanzado el número máximo de imágenes diagnósticas permitidas.',
                    confirmButtonText: 'Aceptar',
                     confirmButtonColor: '#91c845'
                });
                return; // Salir de la función si se alcanza el límite
            }

            // Clonar el modelo de imagen diagnóstica
            let template = $('#diagnostic_images_model').clone();
            template.removeAttr('id'); // Eliminar ID para evitar duplicados
            template.find('.diagnostic_images_title').text('Orden de imagen diagnóstica N° ' + (diagnosticImageCount + 1));

            // Cambiar los atributos name para evitar colisiones
            template.find('input[name^="diagnostics_images[cod]"]').attr('name', 'diagnostics_images[cod][' + diagnosticImageCount + ']');
            template.find('input[name^="diagnostics_images[description]"]').attr('name', 'diagnostics_images[description][' + diagnosticImageCount + ']');
            template.find('input[name^="diagnostics_images[laterality]"]').attr('name', 'diagnostics_images[laterality][' + diagnosticImageCount + ']');
            template.find('input[name^="diagnostics_images[quantity]"]').attr('name', 'diagnostics_images[quantity][' + diagnosticImageCount + ']');
            template.find('textarea[name^="diagnostics_images[notes]"]').attr('name', 'diagnostics_images[notes][' + diagnosticImageCount + ']');

            // Validar campos de todas las órdenes médicas existentes (excluyendo el template oculto)
            const existingWrappers = document.querySelectorAll('.diagnostic-image-wrapper'); // Seleccionar todos los contenedores
            let hasEmptyFields = false; // Bandera para campos vacíos

            existingWrappers.forEach(wrapper => {
                // Comprobar si el contenedor está oculto
                if (wrapper.closest('#diagnostic_images_model')) return; // Ignorar el template oculto

                const fieldValues = {
                    cod: wrapper.querySelector('input[name^="diagnostics_images[cod]"]').value.trim(),
                    description: wrapper.querySelector('input[name^="diagnostics_images[description]"]').value.trim(),
                    quantity: wrapper.querySelector('input[name^="diagnostics_images[quantity]"]').value.trim(),
                    notes: wrapper.querySelector('textarea[name^="diagnostics_images[notes]"]').value.trim(),
                };



                // Verificar si hay campos vacíos
                if (Object.values(fieldValues).some(value => value === '')) {
                    hasEmptyFields = true;
                    return; // Salir del forEach si se encuentra un campo vacío
                }
            });

            if (hasEmptyFields) {
                Swal.fire({
                    title: 'Información incompleta',
                    text: 'Por favor, complete los siguientes campos:',
                    icon: 'warning',
                    confirmButtonText: 'Aceptar',
                     confirmButtonColor: '#91c845'
                });
                return;
            }

            // Incrementar el contador de imágenes diagnósticas
            diagnosticImageCount++;

            // Añadir el nuevo contenedor de imágenes al contenedor principal
            template.appendTo('#diagnostic_images_content_empty');
            template.show();

            // Inicializar los dropdowns en el nuevo contenedor
            initDropdownsDiagnosticImages(template);

            // Actualizar los títulos después de agregar una nueva imagen
            updateDiagnosticLabels();
        }

        // Función para eliminar imágenes diagnósticas
        $(document).on('click', '.remove-diagnostic-image', function () {
            $(this).closest('.diagnostic-image-wrapper').remove();
            diagnosticImageCount--; // Decrementar el contador cuando se elimina una imagen
            updateDiagnosticLabels(); // Actualiza los títulos después de eliminar
        });

        // Función para actualizar los índices de las imágenes diagnósticas
        function updateDiagnosticLabels() {
            $('#diagnostic_images_content_empty .diagnostic_images_title').each(function (index) {
                $(this).text('Orden de imagen diagnóstica N° ' + (index + 1)); // Mantenemos el +1 solo para el texto
            });

            // Reenumerar atributos name de los inputs dentro de cada diagnostic-image-wrapper
            $('#diagnostic_images_content_empty .diagnostic-image-wrapper').each(function (index) {
                $(this).find('input[name^="diagnostics_images[cod]"]').attr('name', 'diagnostics_images[cod][' + index + ']');
                $(this).find('input[name^="diagnostics_images[description]"]').attr('name', 'diagnostics_images[description][' + index + ']');
                $(this).find('input[name^="diagnostics_images[laterality]"]').attr('name', 'diagnostics_images[laterality][' + index + ']');
                $(this).find('input[name^="diagnostics_images[quantity]"]').attr('name', 'diagnostics_images[quantity][' + index + ']');
                $(this).find('textarea[name^="diagnostics_images[notes]"]').attr('name', 'diagnostics_images[notes][' + index + ']');
            });
        }

        // Inicialización de eventos y dropdowns
        $(document).ready(function () {
            initDropdownsDiagnosticImages();
        });
    </script>
    <script>
        $('.info_imagenes_diagnosticas').popup({
            boundary: 'body',
            content: 'Si diligencia uno o más campos en la pestaña Orden de imágenes diagnósticas, todos los campos de esa pestaña serán obligatorios',
            position: 'top center',
            lastResort: 'bottom center' // Si no cabe en la posición inicial, se ajusta a esta
        });
    </script>
@endif

