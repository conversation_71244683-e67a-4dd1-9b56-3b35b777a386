<div class="title"><i class="dropdown icon"></i>Datos firma
</div>

<div class="content">

    <div class="accordion transition">
        @if ($gis->type_sign === 'physical')
            <div class="title centered">
                <i class="dropdown icon"></i>
                Firma física
            </div>

            <div class="content">
                <div class="inline fields">
                    <div class="field ">
                        <label for="typeIdentify">Descargar documento firma física</label>
                        <div class=" ui input">
                            <a target="_blank" href="{{ secure_url('file/') }}" class="ui secondary icon button"
                                id="download_firma">
                                <i class="download icon"></i>
                            </a>
                        </div>
                    </div>
                </div>

                <div class="inline fields">
                    <div class="field">
                        <label for="fecha_firma">Fecha de firma solicitud emisión: </label>
                        {{-- <div class="ui input">

                            <input name="fecha_firma" id="fecha_firma" autocomplete="off" type="text" value="">
                        </div> --}}
                        {{isset($activity->activity_actions[0])  ? ucfirst(strtolower($activity->activity_actions[0]->created_at->formatLocalized('%A %d de %B de %Y'))) : '' }}
                    </div>
                </div>

            </div>
        @elseif($gis->type_sign === 'digital')
            <div class="title centered">
                <i class="dropdown icon"></i>
                Firma digital
            </div>

            <div class="content">
                <div class="inline  fields" style="height: 70px;">
                    <div class="field">
                        <label for="policyHolderName">Firma del representante o apoderado</label>
                        <div class="ui input">
                            <img id="draw-image-representante" alt="Firma"
                                style="background-color: lightgray; display: none; height: 70px;" />
                        </div>
                    </div>
                </div>

                <div class="inline  fields">
                    <div class="field">
                        <label for="fecha_firma">Fecha de firma solicitud emisión: </label>
                        {{ isset($activity->activity_actions[0]) ? ucfirst(strtolower($activity->activity_actions[0]->created_at->formatLocalized('%A %d de %B de %Y'))) : '' }}
                    </div>

                    <div></div>
                    <div></div>
                </div>

            </div>
        @else
            <p>NO EXISTE FIRMA</p>
        @endif
    </div>



</div>


@push('styles')
    <style>
        .button_download {
            display: flex;
            justify-content: flex-end;
            align-items: flex-end;
        }
    </style>
@endpush


@push('scripts')
    <script>
        $(document).ready(function() {

            validateOnChange(`{{ $gis->type_sign }}`);
        });


        function validateOnChange(type) {

            if (type == 'digital') {
                $.ajax({
                    url: '/furat/' + `{{ $id }}` + '/get-signature',
                    type: 'GET',
                    success: function(response) {

                        if (response.sign_holder) {
                            const baseUrl =
                                window.location.origin;
                            $('#draw-image-representante').attr('src', `${baseUrl}/file/${response
                                        .sign_holder}`).show();

                        }

                    },
                    error: function(xhr) {

                        console.error(xhr)
                    }
                });


            } else if (type == 'physical') {

                $.ajax({
                    url: '/furat/getFormatSignature/{{ $id }}',
                    type: 'GET',
                    success: function(response) {
                        if (response.document) {

                            
                            $('#download_firma').attr('href', response.document);
                            $('#download_firma').attr('download', 'document_firmado.pdf');

                        }
                    },
                    error: function(xhr) {
                        console.error(xhr)
                    }
                });

            }
        }
    </script>
@endpush
