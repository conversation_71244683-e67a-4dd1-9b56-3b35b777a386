<div class="title">
    <i class="dropdown icon"></i>
    Reporte de siniestro
</div>

<div class="content">
    <div class="four fields">
        <div class="field">
            <label for="report_type">Tipo de reporte</label>
            <div class="ui selection dropdown readonly" id="reportTypeDropdown">
                <input type="hidden" class="readonly" readonly value="{{ old('report_type', $gis->type_report) }}">
                <i class="dropdown icon"></i>
                <div class="default text">Selecciona una opción</div>
                <div class="menu">
                    <div class="item @if ($gis->type_report == 'Accidente') selected @endif" data-value="Accidente">
                        Accidente</div>
                    <div class="item @if ($gis->type_report == 'Enfermedad') selected @endif" data-value="Enfermedad">
                        Enfermedad</div>
                </div>
            </div>
        </div>
        <div class="field">
            <label for="notice_number"># de aviso</label>
            <div class="ui input">
                <input type="text" placeholder="Ingresa el número de aviso" class="prompt readonly" readonly
                    value="{{ $gis->consecutive_gis ?? '' }}">
            </div>
        </div>
        <div class="field">
            <label for="case_number"># de caso</label>
            <div class="ui input">
                <input type="text" placeholder="Ingresa el número de caso" class="prompt readonly" readonly
                    value="{{ $gis->formatCaseNumberIfReported() }}">
            </div>
        </div>
    </div>

    <!-- Accordion Enfermedad -->
    <div id="dinamic_modulo_select">
    </div>

</div>



@push('styles')
    <style>
        .content-field-cuerpo-dinamic {
            display: flex;
            gap: 10px;
            flex-direction: column;

            .field_dinamic_afectada {
                width: 100%;
            }

            .button_content_dinamic {
                align-content: end;
            }
        }

        .content_dinamic_fields {
            overflow: auto;
            height: 38vw;
            max-height: 700px;
        }
    </style>
@endpush


@push('scripts')
    <script>
        $('input[type="radio"]').popup();
        $(document).ready(function() {


            var gis = @json($gis);
            if (gis) {
                handleDropdownChange(gis.type_report)
            }

            $('#reportTypeDropdown').dropdown({
                onChange: function(value, text, $selectedItem) {
                    $('#dinamic_modulo_select').empty();
                    handleDropdownChange(value);
                }
            });

            function handleDropdownChange(value) {
                if (value === 'Accidente') {
                    $('#dinamic_modulo_select').html(viewAccidente());
                } else if (value === 'Enfermedad') {
                    $('#dinamic_modulo_select').html(viewEnfermedad());
                }

                // Reactivar el dropdown dentro del contenido dinámico
                $('.ui.dropdown_dinamic').dropdown();
                $('.radio_button_cuerpo').find('input[type="radio"]').addClass('pointer-events');
                $('.radio_button_cuerpo_detras').find('input[type="radio"]').addClass('pointer-events');

                enableFuntions();
            }


            function add_file_reporte(parteCuerpo) {
                // Clonamos el último conjunto de campos ocultos
                var newFields = $('.content-field-cuerpo-dinamic').first().clone();

                // Mostrar el nuevo conjunto de campos clonado (remover el display: none)
                newFields.css('display', 'block');

                // Asignar el valor seleccionado al input correspondiente
                newFields.find('input[name="parte_cuerpo"]').val(parteCuerpo);

                // Asignar nuevos IDs únicos para los elementos clonados
                newFields.find('input, select').each(function() {
                    var newId = $(this).attr('id') + '_' + Math.random().toString(36).substring(
                        7); // Generar un nuevo ID único
                    $(this).attr('id', newId); // Asignar el nuevo ID
                });

                // Agregar el nuevo conjunto de campos al contenedor
                $('#field_container_reporte').append(newFields);
            }


            function viewEnfermedad() {

                return `<div class="accordion transition">
                <div class="title"><i class="dropdown icon"></i>Enfermedad </div>
                <div class="content">
                    <div class="three fields">

                        <div class="field">
                            <label for="symptoms_start_date">Fecha que inició con los síntomas</label>
                            <div class="ui  input">
                                <input  id="symptoms_start_date" type="date" class="prompt readonly" readonly value="{{ $gis->symptoms_start_date ? $gis->symptoms_start_date : $gis->date_accident ?? '' }}"
                                    autocomplete="off" >
                            </div>
                        </div>

                        <div class="field">
                            <label for="agent_involved">Agente involucrado</label>
                            <div class="ui selection dropdown dropdown dropdown_dinamic readonly" id="agentInvolvedDropdown">
                                <input type="hidden" value="{{ $gis->agent_involved ?? '' }}" >
                                <i class="dropdown icon"></i>
                                <div class="default text">Selecciona una opción</div>
                                <div class="menu">
                                    <div class="item" data-value="agente_fisico">Agente físico</div>
                                    <div class="item" data-value="agente_biologico">Agente biológico</div>
                                    <div class="item" data-value="factores_psicosociales">Factores psicosociales</div>
                                    <div class="item" data-value="enfermedades_osteomuscular">Enfermedades del sistema
                                        osteomuscular</div>
                                    <div class="item" data-value="otros">Otros</div>
                                </div>
                            </div>
                        </div>

                        <div class="field">
                            <label for="symptoms_description">Descripción de síntomas</label>
                            <div class="ui input">
                             <textarea  rows="2" readonly style="
                         background-color: #f0f0f0 !important;
            color: #888 !important;
            border: 1px solid #ddd !important;"
                        id="symptoms_description" type="text" autocomplete="off" placeholder="Describe el accidente"> {{ json_encode($gis->symptoms_description ? $gis->symptoms_description : $gis->accident_description ?? '') }} </textarea>
                                    </div>
                        </div>
                    </div>

                    <div class="three fields">
                          <div class="required field">
                             <label for="ocurrencia">Enfermedades profesionales</label>
                             <div class="ui selection dropdown dropdown_dinamic grayed-input" id="dropdownDisease">
                                <input type="hidden" name="disease_report_type_id" value="{{ $gis->disease_report_type_id ?? '' }}" />
                                <i class="dropdown icon"></i>
                                <div class="default text">Selecciona una opción</div>
                                <div class="menu">
                                    @foreach ($occupationalDisease as $row)
                                        <div class="item" data-value="{{ $row->id }}">{{ $row->name }}</div>
                                    @endforeach
                                </div>
                             </div>
                        </div>
                   </div>



{{-- formulario muñeco --}}
                    @include('services.gis.form.components.reporte_accidente.components.form_dinamico_muneco')

                    <div class="three fields">
                        <div class="field">
                            <label for="mortal">Requiere ayuda de tercero</label>
                            <div class="ui selection dropdown dropdown_dinamic grayed-input" id="dropayuda">
                                <input type="hidden"  id="ayuda"  value="{{ $gis->ayuda }}">
                                <i class="dropdown icon"></i>
                                <div class="default text">Selecciona una opción</div>
                                <div class="menu">
                                    <div class="item" data-value="1">Si</div>
                                    <div class="item" data-value="0">No</div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>`


            }

            function viewAccidente() {
                return `
        <div class="accordion transition">
    <div class="title">
        <i class="dropdown icon"></i>Accidente
    </div>
    <div class="content">
        <!-- Fecha, Hora y Descripción del Accidente -->
        <div class="three fields">
            <div class="field">
                <label for="accident_date">Fecha del accidente</label>
                <div class="ui input">
                    <input value="{{ $gis->date_accident ?? '' }}"  id="accident_date" type="date" class="prompt readonly" readonly
                        autocomplete="off" />
                </div>
            </div>

             <div class="field">
                <label for="accident_time">Hora del accidente</label>
                <div class="ui input">
                    <input value="{{ $gis->hour_accident ?? '' }}"  id="accident_time" type="time" class="prompt readonly" readonly
                        autocomplete="off" />
                </div>
            </div>

            <div class="field">
                <label for="accident_description">Descripción del accidente</label>
                <div class="ui input">
                    <textarea  rows="2" class="readonly_copy" readonly
                        id="accident_description" type="text" autocomplete="off" placeholder="Describe el accidente"> {{ json_encode($gis->accident_description ?? '') }} </textarea>
                </div>
            </div>
        </div>

        {{-- Formulario muñeco --}}
        @include('services.gis.form.components.reporte_accidente.components.form_dinamico_muneco')

        <!-- Campos de selección relacionados con el accidente -->
        <div class="four fields">
               <div class="field">
                <label for="ocurrencia">Forma de accidente (incidente)</label>
                <div class="ui selection dropdown dropdown_dinamic grayed-input" id="dropdownOcurrencia">
                    <input type="hidden" value="{{ $gis->ocurrencia ?? '' }}" class="readonly" readonly />
                    <i class="dropdown icon"></i>
                    <div class="default text">Selecciona una opción</div>
                    <div class="menu">
                        @foreach ($accidentType as $row)
                            <div class="item" data-value="{{ $row->id }}">{{ $row->name }}</div>
                        @endforeach
                    </div>
                </div>
            </div>

            <div class="field">
            <label for="material_agente">Agente material involucrado</label>
            <div class="ui selection dropdown dropdown_dinamic grayed-input" id="material_agente">
                <input type="hidden" name="material_agente" value="{{ $gis->material_agente ?? '' }}" />
                <i class="dropdown icon"></i>
                <div class="default text">Selecciona una opción</div>
                <div class="menu">
                    @foreach ($agetsGi as $row)
                        <div class="item" data-value="{{ $row->id }}" data-info="{{ $row->name ?? '' }}">{{ $row->name }}</div>
                    @endforeach
                </div>
            </div>
        </div>
        <div id="agent-info" style="margin-top: 10px;"></div>

            <div class="field">
                <label for="mecanismo_trauma">Mecanismo de trauma</label>
                <div class="ui selection dropdown dropdown_dinamic grayed-input" id="dropdownMechanismTrauma">
                    <input type="hidden" id="mechanism_trauma"
                        value="{{ $gis->mechanism_trauma ?? '' }}" />
                    <i class="dropdown icon"></i>
                    <div class="default text">Selecciona una opción</div>
                    <div class="menu">
                        @foreach ($INJURIES as $k => $r)
                        <div class="item" data-value="{{ $k }}">{{ $r }}</div>
                        @endforeach
                    </div>
                </div>
            </div>

            <div class="field">
                <label for="ayuda_tercero">Requiere ayuda de tercero</label>
                <div class="ui selection dropdown dropdown_dinamic grayed-input" id="dropdownThirdParty">
                    <input type="hidden"  id="third_party"
                        value="{{ $gis->third_party ?? '' }}" />
                    <i class="dropdown icon"></i>
                    <div class="default text">Selecciona una opción</div>
                    <div class="menu">
                        <div class="item" data-value="1">Si</div>
                        <div class="item" data-value="0">No</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Modalidad de trabajo, fallecimiento y fecha de desaparición -->
        <div class="four fields">
            <div class="field">
                <label for="modalidad_trabajo">Modalidad de trabajo</label>
                <div class="ui selection dropdown dropdown_dinamic grayed-input" id="dropdownWorkModality">
                    <input type="hidden"  id="work_modality"
                        value="{{ $gis->work_modality ?? '' }}" />
                    <i class="dropdown icon"></i>
                    <div class="default text">Selecciona una opción</div>
                    <div class="menu">
                        @foreach ($WORK_MODES as $k => $r)
                        <div class="item" data-value="{{ $k }}">{{ $r }}</div>
                        @endforeach
                    </div>
                </div>
            </div>

            <!-- Condiciones de fallecimiento/desaparición -->

            
            @if ($gis->conditions === 'desaparecido' || $gis->conditions === 'fallecido')
            <div class="field" id="death_place_field">
                <label for="fallecimiento_lugar">Lugar de fallecimiento / desaparición</label>
                <div class="ui selection dropdown dropdown_dinamic grayed-input" id="death_place_drow">
                    <input type="hidden"  value=""/>
                    <i class="dropdown icon"></i>
                    <div class="default text">Selecciona una opción</div>
                    <div class="menu">
                        <div class="item" data-value="Lugar de trabajo">Lugar de trabajo</div>
                        <div class="item" data-value="Otro">Otro</div>
                    </div>
                </div>
            </div>
            @endif

            @if ($gis->conditions === 'desaparecido')
            <div class="field" id="date_disappearance_field">
                <label for="desaparicion_fecha">Fecha de desaparición</label>
                <div class="ui input">
                    <input  value = "{{ $gis->date_disappearance ?? '' }}" id="date_disappearance" type="date" autocomplete="off" class="prompt readonly" readonly />
                </div>
            </div>
            @endif

             <div class="field hidden" id="material_agente_otro_field">
                <label for="Otro">Otro agente</label>
                <div class="ui input">
                    <input value="{{ $gis->material_agente_otro ?? '' }}"  class="prompt readonly" readonly
                        id="material_agente_otro" type="text" autocomplete="off" placeholder="Describe el agente" />
                </div>
            </div>

               <div class="field hidden"  id="forma_ocurrencia_otro">
                <label for="Otro">Otro forma</label>
                <div class="ui input">
                    <input id="forma_ocurrencia_input" class="prompt readonly" readonly type="text" autocomplete="off" placeholder="Describe" value="{{ $gis->forma_ocurrencia_otro ?? '' }}" />
                </div>
            </div>

             <div class="field hidden"  id="mecanismo_trauma_otro">
                <label for="Otro">Otro mecanismo</label>
                <div class="ui input">
                    <input  id="mecanismo_trauma_input" class="prompt readonly" readonly type="text" autocomplete="off" placeholder="Describe" value="{{ $gis->mecanismo_trauma_otro ?? '' }}" />
                </div>
            </div>
        </div>
           <!-- Campos de selección relacionados con el accidente -->
        <div class="three fields">
             <div class="field">
                <label for="lugar_accidente">Lugar del accidente</label>
                <div class="ui selection dropdown dropdown_dinamic grayed-input" id="lugar_accidente">
                    <input type="hidden" id="accident_place"
                        value="{{ $gis->accident_place ?? '' }}" />
                    <i class="dropdown icon"></i>
                    <div class="default text">Selecciona una opción</div>
                    <div class="menu">
                        @foreach ($WORKPLACE_LOCATIONS as $k => $r)
                        <div class="item" data-value="{{ $k }}">{{ $r }}</div>
                        @endforeach
                    </div>
                </div>
            </div>

           <div class="field">
                <label for="causa_externa">Causa externa</label>
                <div class="ui selection dropdown dropdown_dinamic grayed-input" id="causa_externa">
                    <input type="hidden" id="causa_externa" value="{{ $gis->external_cause ?? '' }}" />
                    <i class="dropdown icon"></i>
                    <div class="default text">Selecciona una opción</div>
                    <div class="menu">
                        <div class="item" data-value="1">Si</div>
                        <div class="item" data-value="0">No</div>
                    </div>
                </div>
            </div>

             <div class="field hidden" id="external_cause_description">
                <label for="external_cause_description">¿Cuál?</label>
                <div class="ui  input">
                    <input value="{{ $gis->external_cause_description ?? '' }}" class="prompt readonly" readonly
                        type="text" autocomplete="off" placeholder="Especificar causa" />
                </div>
            </div>
        </div>

        <!-- Modalidad de trabajo, fallecimiento y primeros auxilios -->
        <div class="three fields">
            <div class="field">
                <label for="first_aid">¿Recibió primeros auxilios o atención en un centro de salud diferente a Oceanica
                    (MNK)?</label>
                <div class="ui selection dropdown dropdown_dinamic grayed-input" id="ayuda_tercero_accidente">
                    <input type="hidden"  id="first_aid" value="{{ $gis->first_aid ?? '' }}" />
                    <i class="dropdown icon"></i>
                    <div class="default text">Selecciona una opción</div>
                    <div class="menu">
                        <div class="item" data-value="1">Si</div>
                        <div class="item" data-value="0">No</div>
                    </div>
                </div>
            </div>

              <div class="field hidden" id="auxilios_descripcion_field">
                <label for="auxilios_descripcion">¿Cuál?</label>
                <div class="ui input">
                    <input value="{{ $gis->auxilios_descripcion ?? '' }}" class="prompt readonly" readonly
                        id="auxilios_descripcion" type="text" autocomplete="off" placeholder="Especificar" />
                </div>
            </div>

             <div class="field hidden" id="lugar_accidente_otro_field">
                <label for="another_place">Otro lugar</label>
                <div class="ui input">
                    <input value="{{ $gis->another_place ?? '' }}" id="another_place" type="text" class="prompt readonly" readonly
                        autocomplete="off" />
                </div>
            </div>
        </div>
    </div>


    {{-- <div class="title"><i class="dropdown icon"></i>Datos de un testigo</div> --}}
    {{-- <div class="content"> --}}
    {{-- --}}
    {{--       <div class="four fields"> --}}
    {{--                <div class="field"> --}}
    {{--                    <label>Tipo de identificación</label> --}}
    {{--                    <div class="ui selection dropdown readonly" id="docTypeDropdown"> --}}
    {{--                        <input type="hidden" class="readonly" readonly value="{{ $gis->doc_type_witness ?? '' }}"> --}}
    {{--                        <i class="dropdown icon"></i> --}}

    {{--                        <div class="default text" style="color: black;" >{{ $DOC_TYPES[$gis->doc_type_witness ?? ''] ?? 'Selecciona una opción' }}</div> --}}
    {{--                        <div class="menu"></div> --}}


    {{--                    </div> --}}
    {{--                </div> --}}

    {{--                <div class="field"> --}}
    {{--                    <label for="docNumber">Número de identificación</label> --}}
    {{--                    <div class="ui  input"> --}}
    {{--                        <input id="docNumber" type="text" autocomplete="off"  class="prompt readonly" readonly --}}
    {{--                        value="{{ $gis->doc_number_witness ?? '' }}" --}}
    {{--                            placeholder="12345678"> --}}
    {{--                    </div> --}}
    {{--                </div> --}}

    {{--                <div class="field"> --}}
    {{--                    <label for="name">Nombre</label> --}}
    {{--                    <div class="ui input"> --}}
    {{--                        <input id="name" type="text" autocomplete="off"  class="prompt readonly" readonly --}}
    {{--                        value="{{ $gis->name_witness ?? '' }}" --}}
    {{--                            placeholder="Nombre completo"> --}}
    {{--                    </div> --}}
    {{--                </div> --}}


    {{--                 <div class=" field"> --}}
    {{--                    <label for="Telefono">Telefono</label> --}}
    {{--                    <div class="ui input"> --}}
    {{--                        <input id="Telefono" type="text" autocomplete="off" class="prompt readonly" readonly --}}
    {{--                        value="{{ $gis->telefonos_witness ?? '' }}" --}}
    {{--                            placeholder="Telefono"> --}}
    {{--                    </div> --}}
    {{--                </div> --}}
    {{--            </div> --}}
    {{-- </div> --}}
    </div>
    
`
            }


            function enableFuntions() {

                $('#dinamic_modulo_select .cuerpo_muneco').on('change', function() {

                    let parteCuerpo = $(this).val();
                    let radioId = $(this).attr('id');

                    add_file_reporte(parteCuerpo);
                });

                $('#material_agente').dropdown({
                    onChange: function(value, text, $selectedItem) {

                        $('#material_agente_otro_field').hide();

                        if (value === '9') {
                            $('#material_agente_otro_field').show();
                        }
                    }
                });

                $('#lugar_accidente').dropdown({
                    onChange: function(value, text, $selectedItem) {

                        $('#lugar_accidente_otro_field').hide();

                        if (value === '8') {
                            $('#lugar_accidente_otro_field').show();
                        }
                    }
                });

                $('#causa_externa').dropdown({
                    onChange: function(value, text, $selectedItem) {

                        $('#external_cause_description').hide();

                        if (value === '1') {
                            $('#external_cause_description').show();
                        }
                    }
                });

                // Inicializa el dropdown
                $('#dropdownOcurrencia').dropdown({
                    // Selecciona el valor desde el input oculto
                    onChange: function(value) {


                        $('#forma_ocurrencia_otro').hide();

                        if (value === '9') {
                            $('#forma_ocurrencia_otro').show();
                        }

                    }
                });

                $('#dropdownMechanismTrauma').dropdown({
                    // Actualiza el valor del input oculto cuando se selecciona algo
                    onChange: function(value) {


                        $('#mecanismo_trauma_otro').hide();

                        if (value === '5') {
                            $('#mecanismo_trauma_otro').show();
                        }
                    }
                });



                $('#ayuda_tercero_accidente').dropdown({
                    onChange: function(value, text, $selectedItem) {

                        $('#auxilios_descripcion_field').hide();

                        if (value === '1') {
                            $('#auxilios_descripcion_field').show();
                        }
                    }
                });
            }



        });
    </script>

    <script>
        //INICIAR DATA:
        $(document).ready(function() {
            const mechanism_trauma = '{{ $gis->mechanism_trauma }}';

            if (mechanism_trauma) {
                $('#dropdownMechanismTrauma').dropdown('set selected', mechanism_trauma);

                if (mechanism_trauma === '5') {
                    $('#mecanismo_trauma_otro').show();
                } else {
                    $('#mecanismo_trauma_otro').hide();
                }
            }


            const ocurrencia = '{{ $gis->ocurrencia }}';

            if (ocurrencia) {

                if (ocurrencia === '9') {
                    $('#forma_ocurrencia_otro').show();
                } else {
                    $('#forma_ocurrencia_otro').hide();
                }
            }

            const material_agente = '{{ $gis->material_agente }}';

            if (material_agente) {

                if (material_agente === '9') {
                    $('#material_agente_otro_field').show();
                } else {
                    $('#material_agente_otro_field').hide();
                }
            }


            const accident_place = '{{ $gis->accident_place }}';

            if (accident_place) {

                if (accident_place === '8') {
                    $('#lugar_accidente_otro_field').show();
                } else {
                    $('#lugar_accidente_otro_field').hide();
                }
            }

            const first_aid = '{{ $gis->first_aid }}';

            if (first_aid) {

                if (first_aid === '1') {
                    $('#auxilios_descripcion_field').show();
                } else {
                    $('#auxilios_descripcion_field').hide();
                }
            }

            const external_cause = '{{ $gis->external_cause }}';


            if (external_cause) {

                if (external_cause === '1') {
                    $('#external_cause_description').show();
                } else {
                    $('#external_cause_description').hide();
                }
            }




            $('#forma_ocurrencia_input').on('input change', function() {
                const ocurrencia = $(this).val();

                if (ocurrencia) {
                    // Quitar la clase 'error' y el mensaje de error si hay una fecha ingresada
                    $('#forma_ocurrencia_otro').removeClass('error');
                    $('#forma_ocurrencia_otro .ui.basic.red.pointing.prompt.label').remove();
                }
            });

            $('#material_agente_otro').on('input change', function() {
                const ocurrencia = $(this).val();

                if (ocurrencia) {
                    // Quitar la clase 'error' y el mensaje de error si hay una fecha ingresada
                    $('#material_agente_otro_field').removeClass('error');
                    $('#material_agente_otro_field .ui.basic.red.pointing.prompt.label').remove();
                }
            });

            $('#mecanismo_trauma_input').on('input change', function() {
                const ocurrencia = $(this).val();

                if (ocurrencia) {
                    // Quitar la clase 'error' y el mensaje de error si hay una fecha ingresada
                    $('#mecanismo_trauma_otro').removeClass('error');
                    $('#mecanismo_trauma_otro .ui.basic.red.pointing.prompt.label').remove();
                }
            });

            $('#date_disappearance').on('input change', function() {
                const ocurrencia = $(this).val();

                if (ocurrencia) {
                    // Quitar la clase 'error' y el mensaje de error si hay una fecha ingresada
                    $('#date_disappearance_field').removeClass('error');
                    $('#date_disappearance_field .ui.basic.red.pointing.prompt.label').remove();
                }
            });

        })
    </script>

    <script>
        $(document).ready(function() {
            var fieldData = @json($fieldData); // Convierte la colección a JSON

            // Cargar valores desde fieldData en los inputs
            fieldData.forEach(function(item) {
                // Crea una nueva entrada en el HTML
                var newField = `
            <div class="content-field-cuerpo-dinamic" style="display: block;">
                <div class="field_dinamic_afectada fields_buttons_actions">
                    <div class="field fields_actions">
                        <label for="parte_cuerpo">Parte del cuerpo afectada</label>
                        <div class="ui input">
                            <input  id="${item.body_part_id}" type="text" autocomplete="off" value="${item.body_part_name}" readonly class="prompt readonly" readonly>
                        </div>
                    </div>
                 
                </div>
            </div>
        `;
                // Agrega el nuevo campo al contenedor
                $('#field_container_reporte').append(newField);

                if (item.body_part_name) {
                    $('input[type=radio][value="' + item.body_part_name + '"]').prop('checked', true);
                }
            });

        });
    </script>
@endpush
