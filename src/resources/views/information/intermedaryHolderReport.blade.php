@extends('layouts.main')

@section('title', 'DESCARGAS ADMIN')

@section('menu')
    @parent
@endsection

@section('content')
    <style>
        .sixteen wide column {
            overflow-x: scroll;
        }
    </style>
    <div class="ui basic segment">
        <h1 class="ui header center"><PERSON><PERSON><PERSON> reporte</h1>
        <p style="color: #aaa;">
            Reportes enfocados en medir la productividad de los intermediarios en ventas de pólizas y realización de cotizaciones por tomador.
        </p>
        <div class="ui basic segment">
            {{--Solo accede el administrador o el area de proveedor--}}
            @if(Auth::user()->canViewReport('reporte_intermediario_tomador') || Auth::user()->isProvider())
                @include('information.components.intermedaryHolderReportComp')
            @endif

            <!-- Mostrar errores -->
            @if ($errors->any())
                <div class="ui negative message">
                    <div class="header">Errores encontrados</div>
                    <ul class="list">
                        @foreach ($errors->all() as $error)
                            <li>{!! $error !!}</li>
                            <!-- Permite que el HTML se interprete correctamente -->
                        @endforeach
                    </ul>
                </div>
            @endif
        </div>

        <script type="text/javascript">


            function pad(value) {
                return String(value).padStart(2, '0');
            }

            $(document).ready(function () {

                //showStates(); showActions();
                $('.datepicker').pickadate({
                    selectYears: true,
                    selectMonths: true,
                    formatSubmit: 'yyyy-mm-dd',
                    showOtherMonths: false,
                    selectOtherMonths: false // Evita la selección de fechas grises.
                });

                $('.ui.accordion').accordion({
                    exclusive: false
                });
                $('.ui.dropdown').dropdown({
                    fullTextSearch: true
                });
                $('.ui.dropdown.remote').dropdown({
                    apiSettings: {
                        // this url parses query server side and returns filtered results
                        url: '/api/affiliates/{query}'
                    },
                });
                $('.ui.checkbox').checkbox();
                $('#reset').click(function () {
                    $('form .ui.dropdown').dropdown('clear');
                    $.each($('form .datepicker'), function (k, el) {
                        console.log(el);
                        $(el).pickadate('picker').clear();
                    });
                });
            });
        </script>
        <!-- Scripts específicos según el tipo de reporte -->
    @stack('scriptsReport')
@endsection
