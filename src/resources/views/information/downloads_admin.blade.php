@extends('layouts.main')

@section('title', 'DESCARGAS ADMIN')

@section('menu')
    @parent
@endsection

@section('content')
    <style>
        .sixteen wide column {
            overflow-x: scroll;
        }
    </style>
    <div class="ui basic segment">
        <h1 class="ui header center">Descargas de administrador</h1>

        <div class="ui basic segment">

            <div class="ui styled fluid accordion" data-tab="definition">

                @if (!Auth::user()->isCall())
                    <div class="title">
                        <i class="dropdown icon"></i> Reporte de estados
                    </div>
                    <div class="content">
                        <form class="ui form small clearing" method="post"
                            action="{{ secure_url('/admin/reportes/download_states_delimited') }}" id="downloadStatesForm">
                            <div class="three fields">
                                <div class="field">
                                    <label>
                                        Fecha de registro <span style="color: red;" class="required">*</span>
                                    </label>
                                    <div class="two fields">
                                        <div class="field">
                                            <input class="datepicker" data-value="{{ old('state_start_date_submit') }}"
                                                name="state_start_date" placeholder="Desde" type="text" />
                                        </div>
                                        <div class="field">
                                            <input class="datepicker" data-value="{{ old('state_end_date_submit') }}"
                                                name="state_end_date" placeholder="Hasta" type="text" />
                                        </div>
                                    </div>
                                </div>
                                <div class="field">
                                    <label>
                                        Tipo de servicio
                                    </label>
                                    <div class="ui search multiple selection search dropdown">
                                        <input name="service_id" type="hidden" value="{{ old('service_id') }}" />
                                        <i class="dropdown icon"></i>
                                        <div class="default text">
                                            TODOS
                                        </div>
                                        <div class="menu">
                                            @foreach ($services as $s)
                                                @unless (App\Service::checkService($s->id, \App\Service::SERVICE_IT_LIQUIDATED_COLPENSIONES))
                                                    <div class="item" data-value="{{ $s->id }}">
                                                        {{ $s->name }}
                                                    </div>
                                                @endunless
                                            @endforeach
                                        </div>
                                    </div>
                                </div>
                                <div class="field">
                                    <label>
                                        Estado
                                    </label>
                                    <div id="states" class="ui search multiple selection search dropdown">
                                        <input name="state_id" type="hidden" value="{{ old('state_id') }}" />
                                        <i class="dropdown icon"></i>
                                        <div class="default text">
                                            TODOS
                                        </div>
                                        <div class="menu">
                                            @foreach ($states as $s)
                                                <div class="item" data-value="{{ $s->id }}">
                                                    {{ $s->name }}
                                                </div>
                                            @endforeach
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="ui error message" style="width: 25%" id="downloadStatesError"></div>
                            <div class="field">
                                <button class="ui primary button">
                                    <i class="search icon"></i>
                                    Generar reporte
                                </button>
                                <button class="ui secondary button" id="reset" type="reset">
                                    <i class="undo icon"></i>
                                    Limpiar filtros
                                </button>
                            </div>
                            {{ csrf_field() }}
                        </form>
                    </div>

                    <div class="title">
                        <i class="dropdown icon"></i> Reporte de acciones
                    </div>
                    <div class="content">
                        <form class="ui form small clearing" method="post"
                            action="{{ secure_url('/admin/reportes/download_actions_delimited') }}"
                            id="downloadActionsForm">
                            <div class="four fields">
                                <div class="field">
                                    <label>
                                        Fecha de inicio <span style="color: red;" class="required">*</span>
                                    </label>
                                    <input class="datepicker" data-value="{{ old('action_start_date_submit') }}"
                                        name="action_start_date" placeholder="Desde" type="text" />

                                </div>
                                <div class="field">
                                    <label>
                                        Fecha de finalización <span style="color: red;" class="required">*</span>
                                    </label>
                                    <input class="datepicker" data-value="{{ old('action_end_date_submit') }}"
                                        name="action_end_date" placeholder="Hasta" type="text" />
                                </div>
                                <div class="field">
                                    <label>
                                        Tipo de Servicio
                                    </label>
                                    <div class="ui search multiple selection search dropdown">
                                        <input name="service_id" type="hidden" value="{{ old('service_id') }}" />
                                        <i class="dropdown icon"></i>
                                        <div class="default text">
                                            TODOS
                                        </div>
                                        <div class="menu">
                                            @foreach ($services as $s)
                                                <div class="item" data-value="{{ $s->id }}">
                                                    {{ $s->name }}
                                                </div>
                                            @endforeach
                                        </div>
                                    </div>
                                </div>
                                <div class="field">
                                    <label>
                                        Acción
                                    </label>
                                    <div id="states" class="ui search multiple selection search dropdown">
                                        <input name="action_id" type="hidden" value="{{ old('action_id') }}" />
                                        <i class="dropdown icon"></i>
                                        <div class="default text">
                                            TODOS
                                        </div>
                                        <div class="menu">
                                            @foreach ($actions as $s)
                                                <div class="item" data-value="{{ $s->id }}">
                                                    {{ $s->name }}
                                                </div>
                                            @endforeach
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="ui error message" style="width: 25%" id="downloadActionsError"></div>
                            <div class="field">
                                <button class="ui primary button">
                                    <i class="search icon"></i>
                                    Generar reporte
                                </button>
                                <button class="ui secondary button" id="reset" type="reset">
                                    <i class="undo icon"></i>
                                    Limpiar filtros
                                </button>
                            </div>
                            {{ csrf_field() }}
                        </form>
                    </div>

                    <div class="title">
                        <i class="dropdown icon"></i> Reporte de cajas
                    </div>
                    <div class="content">
                        <form class="ui form small clearing" method="post"
                            action="{{ secure_url('/admin/reportes/download_actions_box') }}" id="downloadActionsForm">
                            <div class="two fields">
                                <div class="field">
                                    <label>
                                        Fecha de inicio <span style="color: red;" class="required">*</span>
                                    </label>
                                    <input class="datepicker" data-value="{{ old('action_start_date_submit') }}"
                                        name="action_start_date" placeholder="Desde" type="text" />

                                </div>
                                <div class="field">
                                    <label>
                                        Fecha de finalización <span style="color: red;" class="required">*</span>
                                    </label>
                                    <input class="datepicker" data-value="{{ old('action_end_date_submit') }}"
                                        name="action_end_date" placeholder="Hasta" type="text" />
                                </div>

                            </div>
                            <div class="ui error message" style="width: 25%" id="downloadActionsError"></div>
                            <div class="field">
                                <button class="ui primary button">
                                    <i class="search icon"></i>
                                    Generar reporte
                                </button>
                                <button class="ui secondary button" id="reset" type="reset">
                                    <i class="undo icon"></i>
                                    Limpiar filtros
                                </button>
                            </div>
                            {{ csrf_field() }}
                        </form>
                    </div>
                    <div class="title">
                        <i class="dropdown icon"></i> Reporte de comisión agente
                    </div>
                    <div class="content">
                        <form class="ui form small clearing" method="post"
                            action="{{ secure_url('/admin/reportes/download_agent_commission') }}"
                            id="downloadActionsForm">
                            <div class="two fields">
                                <div class="field">
                                    <label>
                                        Fecha de inicio <span style="color: red;" class="required">*</span>
                                    </label>
                                    <input class="datepicker" data-value="{{ old('action_start_date_submit') }}"
                                        name="action_start_date" placeholder="Desde" type="text" />

                                </div>
                                <div class="field">
                                    <label>
                                        Fecha de finalización <span style="color: red;" class="required">*</span>
                                    </label>
                                    <input class="datepicker" data-value="{{ old('action_end_date_submit') }}"
                                        name="action_end_date" placeholder="Hasta" type="text" />
                                </div>
                            </div>
                            <div class="ui error message" style="width: 25%" id="downloadActionsError"></div>
                            <div class="field">
                                <button class="ui primary button">
                                    <i class="search icon"></i>
                                    Generar reporte
                                </button>
                                <button class="ui secondary button" id="reset" type="reset">
                                    <i class="undo icon"></i>
                                    Limpiar filtros
                                </button>
                            </div>
                            {{ csrf_field() }}
                        </form>
                    </div>

                    <div class="title">
                        <i class="dropdown icon"></i> Reporte de pólizas
                    </div>
                    <div class="content">
                        <form class="ui form small clearing" method="post"
                            action="{{ secure_url('/admin/reportes/report_policy') }}" id="downloadActionsForm">
                            <div class="two fields">
                                <div class="field">
                                    <label>
                                        Fecha de inicio <span style="color: red;" class="required">*</span>
                                    </label>
                                    <input class="datepicker" data-value="{{ old('action_start_date_submit') }}"
                                        name="action_start_date" placeholder="Desde" type="text" />

                                </div>
                                <div class="field">
                                    <label>
                                        Fecha de finalización <span style="color: red;" class="required">*</span>
                                    </label>
                                    <input class="datepicker" data-value="{{ old('action_end_date_submit') }}"
                                        name="action_end_date" placeholder="Hasta" type="text" />
                                </div>

                            </div>
                            <div class="ui error message" style="width: 25%" id="downloadActionsError"></div>
                            <div class="field">
                                <button class="ui primary button">
                                    <i class="search icon"></i>
                                    Generar reporte
                                </button>
                                <button class="ui secondary button" id="reset" type="reset">
                                    <i class="undo icon"></i>
                                    Limpiar filtros
                                </button>
                            </div>
                            {{ csrf_field() }}
                        </form>
                    </div>

                    {{-- REPORTE DE PRIMAS --}}
                    <div class="title">
                        <i class="dropdown icon"></i> Reporte de primas
                    </div>
                    <div class="content">
                        <form class="ui form small clearing" method="post"
                            action="{{ secure_url('/admin/reportes/report_primas') }}" id="downloadPrimasForm">
                            <div class="two fields">
                                <div class="field">
                                    <label>
                                        Fecha de inicio <span style="color: red;" class="required">*</span>
                                    </label>
                                    <input class="datepicker" data-value="{{ old('primas_start_date_submit') }}"
                                        name="primas_start_date" placeholder="Desde" type="text" />
                                </div>
                                <div class="field">
                                    <label>
                                        Fecha de finalización <span style="color: red;" class="required">*</span>
                                    </label>
                                    <input class="datepicker" data-value="{{ old('primas_end_date_submit') }}"
                                        name="primas_end_date" placeholder="Hasta" type="text" />
                                </div>
                            </div>
                            <div class="ui error message" style="width: 25%" id="downloadPrimasError"></div>
                            <div class="field">
                                <button class="ui primary button">
                                    <i class="search icon"></i>
                                    Generar reporte
                                </button>
                                <button class="ui secondary button" id="resetPrimas" type="reset">
                                    <i class="undo icon"></i>
                                    Limpiar filtros
                                </button>
                            </div>
                            {{ csrf_field() }}
                        </form>
                    </div>

                    <div class="title">
                        <i class="dropdown icon"></i> Reporte de primas pendientes
                    </div>
                    <div class="content">
                        <form class="ui form small clearing" method="post"
                            action="{{ secure_url('/admin/reportes/reportPendingPremiums') }}" id="downloadPrimasForm">
                            <div class="two fields">
                                {{--                            <div class="field"> --}}
                                {{--                                <label> --}}
                                {{--                                    Fecha de inicio <span style="color: red;" class="required">*</span> --}}
                                {{--                                </label> --}}
                                {{--                                <input class="datepicker" --}}
                                {{--                                       data-value="{{old('primas_start_date_submit')}}" --}}
                                {{--                                       name="primas_start_date" placeholder="Desde" type="text" /> --}}
                                {{--                            </div> --}}

                                <div class="field">
                                    <label>
                                        Fecha cálculo <span style="color: red;" class="required">*</span>
                                    </label>
                                    <input class="datepicker"
                                        data-value="{{ old('primas_pendientes_start_date_submit') }}"
                                        name="primas_pendientes_start_date" placeholder="Fecha calculo" type="text" />
                                </div>
                                {{--                            <div class="field"> --}}
                                {{--                                <label> --}}
                                {{--                                    Fecha de finalización <span style="color: red;" class="required">*</span> --}}
                                {{--                                </label> --}}
                                {{--                                <input class="datepicker" --}}
                                {{--                                       data-value="{{old('primas_end_date_submit')}}" --}}
                                {{--                                       name="primas_end_date" placeholder="Hasta" type="text" /> --}}
                                {{--                            </div> --}}
                            </div>
                            <div class="ui error message" style="width: 25%" id="downloadPrimasError"></div>
                            <div class="field">
                                <button class="ui primary button">
                                    <i class="search icon"></i>
                                    Generar reporte
                                </button>
                                <button class="ui secondary button" id="resetPrimas" type="reset">
                                    <i class="undo icon"></i>
                                    Limpiar filtros
                                </button>
                            </div>
                            {{ csrf_field() }}
                        </form>
                    </div>

                    <div class="title">
                        <i class="dropdown icon"></i> Reporte de siniestros
                    </div>
                    <div class="content">
                        <form class="ui form small clearing" method="post"
                            action="{{ secure_url('/admin/reportes/reportIncidents') }}" id="downloadPrimasForm">
                            <div class="two fields">
                                <div class="field">
                                    <label>
                                        Fecha de inicio <span style="color: red;" class="required">*</span>
                                    </label>
                                    <input class="datepicker" data-value="{{ old('primas_start_date_submit') }}"
                                        name="primas_start_date" placeholder="Desde" type="text" />
                                </div>
                                <div class="field">
                                    <label>
                                        Fecha de finalización <span style="color: red;" class="required">*</span>
                                    </label>
                                    <input class="datepicker" data-value="{{ old('primas_end_date_submit') }}"
                                        name="primas_end_date" placeholder="Hasta" type="text" />
                                </div>
                            </div>
                            <div class="ui error message" style="width: 25%" id="downloadPrimasError"></div>
                            <div class="field">
                                <button class="ui primary button">
                                    <i class="search icon"></i>
                                    Generar reporte
                                </button>
                                <button class="ui secondary button" id="resetPrimas" type="reset">
                                    <i class="undo icon"></i>
                                    Limpiar filtros
                                </button>
                            </div>
                            {{ csrf_field() }}
                        </form>
                    </div>


                    {{-- ME-1158 - Requerimiento 41 --}}
                    <div class="title">
                        <i class="dropdown icon"></i> Reporte monto pendiente de siniestros
                    </div>
                    <div class="content">
                        <form class="ui form small clearing" method="post"
                            action="{{ secure_url('/admin/reportes/reportPendingIncidentAmount') }}"
                            id="reportPendingIncidentAmount">
                            <div class="two fields">
                                <div class="field">
                                    <label>
                                        Fecha de inicio <span style="color: red;" class="required">*</span>
                                    </label>
                                    <input class="datepicker" data-value="{{ old('rpia_start_date_submit') }}"
                                        name="rpia_start_date" placeholder="Desde" type="text" />
                                </div>
                                <div class="field">
                                    <label>
                                        Fecha de finalización <span style="color: red;" class="required">*</span>
                                    </label>
                                    <input class="datepicker" data-value="{{ old('rpia_end_date_submit') }}"
                                        name="rpia_end_date" placeholder="Hasta" type="text" />
                                </div>
                            </div>
                            <div class="ui error message" style="width: 25%"></div>
                            <div class="field">
                                <button class="ui primary button">
                                    <i class="search icon"></i>
                                    Generar reporte
                                </button>
                                <button class="ui secondary button" type="reset">
                                    <i class="undo icon"></i>
                                    Limpiar filtros
                                </button>
                            </div>
                            {{ csrf_field() }}
                        </form>
                    </div>


                    {{-- ME-3264 - Requerimiento 130 --}}
                    <div class="title">
                        <i class="dropdown icon"></i> Reporte de siniestro de primer nivel
                    </div>
                    <div class="content">
                        <form class="ui form small clearing" method="post"
                              action="{{ secure_url('/admin/reportes/reportfirstLevelGis') }}"
                              id="reportfirstLevelGis">
                            <div class="two fields">
                                <div class="field">
                                    <label>
                                        Fecha de inicio <span style="color: red;" class="required">*</span>
                                    </label>
                                    <input class="datepicker" data-value="{{ old('rflg_start_date_submit') }}"
                                           name="rflg_start_date" placeholder="Desde" type="text" />
                                </div>
                                <div class="field">
                                    <label>
                                        Fecha de finalización <span style="color: red;" class="required">*</span>
                                    </label>
                                    <input class="datepicker" data-value="{{ old('rflg_end_date_submit') }}"
                                           name="rflg_end_date" placeholder="Hasta" type="text" />
                                </div>
                            </div>
                            <div class="ui error message" style="width: 25%"></div>
                            <div class="field">
                                <button class="ui primary button">
                                    <i class="search icon"></i>
                                    Generar reporte
                                </button>
                                <button class="ui secondary button" type="reset">
                                    <i class="undo icon"></i>
                                    Limpiar filtros
                                </button>
                            </div>
                            {{ csrf_field() }}
                        </form>
                    </div>



                    <div class="title">
                        <i class="dropdown icon"></i> Reporte asientos contables
                    </div>
                    <div class="content">
                        <form class="ui form small clearing" method="post"
                            action="{{ secure_url('/admin/reportes/reportAccountingEntry') }}" id="downloadPrimasForm">
                            <div class="three fields">
                                <div class="field">
                                    <label>
                                        Fecha de inicio <span style="color: red;" class="required">*</span>
                                    </label>
                                    <input class="datepicker" data-value="{{ old('asientos_start_date_submit') }}"
                                        name="asientos_start_date" placeholder="Desde" type="text" />
                                </div>
                                <div class="field">
                                    <label>
                                        Fecha de finalización <span style="color: red;" class="required">*</span>
                                    </label>
                                    <input class="datepicker" data-value="{{ old('asientos_end_date_submit') }}"
                                        name="asientos_end_date" placeholder="Hasta" type="text" />
                                </div>
                                <div class="field">
                                    <label>
                                        # póliza
                                    </label>
                                    <input data-value="{{ old('policy_sort_id') }}" name="policy_sort_id"
                                        type="text" />
                                </div>
                            </div>
                            <div class="ui error message" style="width: 25%" id="downloadPrimasError"></div>
                            <div class="field">
                                <button class="ui primary button">
                                    <i class="search icon"></i>
                                    Generar reporte
                                </button>
                                <button class="ui secondary button" id="resetPrimas" type="reset">
                                    <i class="undo icon"></i>
                                    Limpiar filtros
                                </button>
                            </div>
                            {{ csrf_field() }}
                        </form>
                    </div>

                    <div class="title">
                        <i class="dropdown icon"></i> Reporte de reaseguro
                    </div>
                    <div class="content">
                        <form class="ui form small clearing" method="post"
                            action="{{ secure_url('/admin/reportes/reinsuranceReport') }}" id="downloadPrimasForm">
                            <div class="three fields">
                                <div class="field">
                                    <label>
                                        Fecha de inicio <span style="color: red;" class="required">*</span>
                                    </label>
                                    <input class="datepicker" data-value="{{ old('reaseguro_start_date_submit') }}"
                                        name="reaseguro_start_date" placeholder="Desde" type="text" />
                                </div>
                                <div class="field">
                                    <label>
                                        Fecha de finalización <span style="color: red;" class="required">*</span>
                                    </label>
                                    <input class="datepicker" data-value="{{ old('reaseguro_end_date_submit') }}"
                                        name="reaseguro_end_date" placeholder="Hasta" type="text" />
                                </div>
                            </div>
                            <div class="ui error message" style="width: 25%" id="downloadPrimasError"></div>
                            <div class="field">
                                <button class="ui primary button">
                                    <i class="search icon"></i>
                                    Generar reporte
                                </button>
                                <button class="ui secondary button" id="resetPrimas" type="reset">
                                    <i class="undo icon"></i>
                                    Limpiar filtros
                                </button>
                            </div>
                            {{ csrf_field() }}
                        </form>
                    </div>

                    <div class="title">
                        <i class="dropdown icon"></i> Reporte PPND
                    </div>
                    <div class="content">
                        <form class="ui form small clearing" method="post"
                            action="{{ secure_url('/admin/reportes/ppndReport') }}" id="downloadPrimasForm">
                            <div class="three fields">
                                <div class="field">
                                    <label>
                                        Fecha cálculo <span style="color: red;" class="required">*</span>
                                    </label>
                                    <input class="datepicker" data-value="{{ old('ppnd_start_date_submit') }}"
                                        name="ppnd_start_date" placeholder="Fecha calculo" type="text" />
                                </div>
                                {{--                            <div class="field"> --}}
                                {{--                                <label> --}}
                                {{--                                    Fecha de finalización <span style="color: red;" class="required">*</span> --}}
                                {{--                                </label> --}}
                                {{--                                <input class="datepicker" --}}
                                {{--                                       data-value="{{old('ppnd_end_date_submit')}}" --}}
                                {{--                                       name="ppnd_end_date" placeholder="Hasta" type="text" /> --}}
                                {{--                            </div> --}}
                            </div>
                            <div class="ui error message" style="width: 25%" id="downloadPrimasError"></div>
                            <div class="field">
                                <button class="ui primary button">
                                    <i class="search icon"></i>
                                    Generar reporte
                                </button>
                                <button class="ui secondary button" id="resetPrimas" type="reset">
                                    <i class="undo icon"></i>
                                    Limpiar filtros
                                </button>
                            </div>
                            {{ csrf_field() }}
                        </form>
                    </div>

                    <div class="title">
                        <i class="dropdown icon"></i> Reporte cartera vigente
                    </div>
                    <div class="content">
                        <form class="ui form small clearing" method="post"
                            action="{{ secure_url('/admin/reportes/generateActivePortfolioReport ') }}"
                            id="downloadPrimasForm">
                            <div class="three fields">
                                <div class="field">
                                    <label>
                                        Fecha cálculo <span style="color: red;" class="required">*</span>
                                    </label>
                                    <input class="datepicker" data-value="{{ old('cartera_start_date_submit') }}"
                                        name="cartera_start_date" placeholder="Fecha calculo" type="text" />
                                </div>
                                {{--                            <div class="field"> --}}
                                {{--                                <label> --}}
                                {{--                                    Fecha de finalización <span style="color: red;" class="required">*</span> --}}
                                {{--                                </label> --}}
                                {{--                                <input class="datepicker" --}}
                                {{--                                       data-value="{{old('ppnd_end_date_submit')}}" --}}
                                {{--                                       name="ppnd_end_date" placeholder="Hasta" type="text" /> --}}
                                {{--                            </div> --}}
                            </div>
                            <div class="ui error message" style="width: 25%" id="downloadPrimasError"></div>
                            <div class="field">
                                <button class="ui primary button">
                                    <i class="search icon"></i>
                                    Generar reporte
                                </button>
                                <button class="ui secondary button" id="resetPrimas" type="reset">
                                    <i class="undo icon"></i>
                                    Limpiar filtros
                                </button>
                            </div>
                            {{ csrf_field() }}
                        </form>
                    </div>

                    <div class="title">
                        <i class="dropdown icon"></i> Reporte pago a terceros
                    </div>
                    <div class="content">
                        <form class="ui form small clearing" method="post"
                            action="{{ secure_url('/admin/reportes/reportThirdPartyPayments') }}"
                            id="downloadPrimasForm">
                            <div class="three fields">
                                <div class="field">
                                    <label>
                                        Fecha de inicio <span style="color: red;" class="required">*</span>
                                    </label>
                                    <input class="datepicker" data-value="{{ old('terceros_start_date_submit') }}"
                                        name="terceros_start_date" placeholder="Desde" type="text" />
                                </div>
                                <div class="field">
                                    <label>
                                        Fecha de finalización <span style="color: red;" class="required">*</span>
                                    </label>
                                    <input class="datepicker" data-value="{{ old('terceros_end_date_submit') }}"
                                        name="terceros_end_date" placeholder="Hasta" type="text" />
                                </div>
                            </div>
                            <div class="ui error message" style="width: 25%" id="downloadPrimasError"></div>
                            <div class="field">
                                <button class="ui primary button">
                                    <i class="search icon"></i>
                                    Generar reporte
                                </button>
                                <button class="ui secondary button" id="resetPrimas" type="reset">
                                    <i class="undo icon"></i>
                                    Limpiar filtros
                                </button>
                            </div>
                            {{ csrf_field() }}
                        </form>
                    </div>

                    <div class="title">
                        <i class="dropdown icon"></i> Reporte OYNR
                    </div>
                    <div class="content">
                        <form class="ui form small clearing" method="post"
                            action="{{ secure_url('/admin/reportes/reportOynr') }}" id="downloadPrimasForm">
                            <div class="three fields">
                                <div class="field">
                                    <label>
                                        Fecha cálculo <span style="color: red;" class="required">*</span>
                                    </label>
                                    <input class="datepicker" data-value="{{ old('oynr_start_date_submit') }}"
                                        name="oynr_start_date" placeholder="Desde" type="text" />
                                </div>
                                {{--                            <div class="field"> --}}
                                {{--                                <label> --}}
                                {{--                                    Fecha de finalización <span style="color: red;" class="required">*</span> --}}
                                {{--                                </label> --}}
                                {{--                                <input class="datepicker" --}}
                                {{--                                       data-value="{{old('oynr_end_date_submit')}}" --}}
                                {{--                                       name="oynr_end_date" placeholder="Hasta" type="text" /> --}}
                                {{--                            </div> --}}
                            </div>
                            <div class="ui error message" style="width: 25%" id="downloadPrimasError"></div>
                            <div class="field">
                                <button class="ui primary button">
                                    <i class="search icon"></i>
                                    Generar reporte
                                </button>
                                <button class="ui secondary button" id="resetPrimas" type="reset">
                                    <i class="undo icon"></i>
                                    Limpiar filtros
                                </button>
                            </div>
                            {{ csrf_field() }}
                        </form>
                    </div>

                    <div class="title">
                        <i class="dropdown icon"></i> Reporte asientos acsel
                    </div>
                    <div class="content">
                        <form class="ui form small clearing" method="post"
                            action="{{ secure_url('/admin/reportes/acselAccountingEntriesView') }}"
                            id="downloadPrimasForm">
                            <div class="three fields">
                                <div class="field">
                                    <label>
                                        Fecha de inicio <span style="color: red;" class="required">*</span>
                                    </label>
                                    <input class="datepicker" data-value="{{ old('acsel_start_date_submit') }}"
                                        name="acsel_start_date" placeholder="Desde" type="text" />
                                </div>
                                <div class="field">
                                    <label>
                                        Fecha de finalización <span style="color: red;" class="required">*</span>
                                    </label>
                                    <input class="datepicker" data-value="{{ old('acsel_end_date_submit') }}"
                                        name="acsel_end_date" placeholder="Hasta" type="text" />
                                </div>
                            </div>
                            <div class="ui error message" style="width: 25%" id="downloadPrimasError"></div>
                            <div class="field">
                                <button class="ui primary button">
                                    <i class="search icon"></i>
                                    Generar reporte
                                </button>
                                <button class="ui secondary button" id="resetPrimas" type="reset">
                                    <i class="undo icon"></i>
                                    Limpiar filtros
                                </button>
                            </div>
                            {{ csrf_field() }}
                        </form>
                    </div>

                    <div class="title">
                        <i class="dropdown icon"></i> Reporte pólizas acsel
                    </div>
                    <div class="content">
                        <form class="ui form small clearing" method="post"
                            action="{{ secure_url('/admin/reportes/acselPolicyView') }}" id="downloadPrimasForm">
                            <div class="three fields">
                                <div class="field">
                                    <label>
                                        Fecha de inicio <span style="color: red;" class="required">*</span>
                                    </label>
                                    <input class="datepicker" data-value="{{ old('acsel_policy_start_date_submit') }}"
                                        name="acsel_policy_start_date" placeholder="Desde" type="text" />
                                </div>
                                <div class="field">
                                    <label>
                                        Fecha de finalización <span style="color: red;" class="required">*</span>
                                    </label>
                                    <input class="datepicker" data-value="{{ old('acsel_policy_end_date_submit') }}"
                                        name="acsel_policy_end_date" placeholder="Hasta" type="text" />
                                </div>
                            </div>
                            <div class="ui error message" style="width: 25%" id="downloadPrimasError"></div>
                            <div class="field">
                                <button class="ui primary button">
                                    <i class="search icon"></i>
                                    Generar reporte
                                </button>
                                <button class="ui secondary button" id="resetPrimas" type="reset">
                                    <i class="undo icon"></i>
                                    Limpiar filtros
                                </button>
                            </div>
                            {{ csrf_field() }}
                        </form>
                    </div>

                    <div class="title">
                        <i class="dropdown icon"></i> Reporte de cuentas IBAN
                    </div>
                    <div class="content">
                        <form class="ui form small clearing" method="post"
                            action="{{ secure_url('/admin/reportes/reportAccountIban') }}" id="downloadPrimasForm">
                            <div class="three fields">
                                <div class="field">
                                    <label>
                                        Fecha de inicio <span style="color: red;" class="required">*</span>
                                    </label>
                                    <input class="datepicker" data-value="{{ old('iban_start_date_submit') }}"
                                        name="iban_start_date" placeholder="Desde" type="text" />
                                </div>
                                <div class="field">
                                    <label>
                                        Fecha de finalización <span style="color: red;" class="required">*</span>
                                    </label>
                                    <input class="datepicker" data-value="{{ old('iban_end_date_submit') }}"
                                        name="iban_end_date" placeholder="Hasta" type="text" />
                                </div>
                            </div>
                            <div class="ui error message" style="width: 25%" id="downloadPrimasError"></div>
                            <div class="field">
                                <button class="ui primary button">
                                    <i class="search icon"></i>
                                    Generar reporte
                                </button>
                                <button class="ui secondary button" id="resetPrimas" type="reset">
                                    <i class="undo icon"></i>
                                    Limpiar filtros
                                </button>
                            </div>
                            {{ csrf_field() }}
                        </form>
                    </div>

                    <div class="title">
                        <i class="dropdown icon"></i> Reporte de afiliados
                    </div>
                    <div class="content">
                        <form class="ui form small clearing" method="post"
                            action="{{ secure_url('/admin/reportes/affiliateReport') }}" id="downloadAffiliateReport">
                            <div class="two fields">
                                <div class="field">
                                    <label>
                                        Período <span style="color: red;" class="required">*</span>
                                    </label>
                                    <input id="affiliate_start_date" name="affiliate_start_date" type="text"
                                        class="datepicker" value="{{ old('affiliate_start_date') }}"
                                        placeholder="Período" />
                                </div>
                            </div>
                            <div class="ui error message" style="width: 25%" id="downloadPrimasError"></div>
                            <div class="field">
                                <button class="ui primary button">
                                    <i class="search icon"></i>
                                    Generar reporte
                                </button>
                                <button class="ui secondary button" id="resetAffiliateReport" type="reset">
                                    <i class="undo icon"></i>
                                    Limpiar filtros
                                </button>
                            </div>
                            {{ csrf_field() }}
                        </form>
                    </div>

                    <div class="title">
                        <i class="dropdown icon"></i> Modelos XML SUGESE
                    </div>
                    <div class="content">
                        <div class="accordion transition">
                            <div class="title">
                                <i class="dropdown icon"></i>
                                MODELO XML 13
                            </div>
                            <div class="content">
                                <form class="ui form small clearing" method="post"
                                    action="{{ secure_url('/admin/reportes/reportXmlOne') }}" id="downloadPrimasForm">

                                    <div class="three fields">
                                        <div class="field">
                                            <label>
                                                Seleccionar Año <span style="color: red;" class="required">*</span>
                                            </label>
                                            <select name="xml_one_year" class="ui dropdown">
                                                @for ($i = date('Y'); $i >= date('Y') - 9; $i--)
                                                    <option value="{{ $i }}">{{ $i }}</option>
                                                @endfor
                                            </select>
                                        </div>

                                    </div>

                                    <div class="ui error message" style="width: 25%" id="downloadPrimasError"></div>
                                    <div class="field">
                                        <button class="ui primary button">
                                            <i class="search icon"></i>
                                            Generar reporte
                                        </button>
                                        <button class="ui secondary button" id="resetPrimas" type="reset">
                                            <i class="undo icon"></i>
                                            Limpiar filtros
                                        </button>
                                    </div>
                                    {{ csrf_field() }}
                                </form>
                            </div>

                            <div class="title">
                                <i class="dropdown icon"></i>
                                MODELO XML 14
                            </div>
                            <div class="content">
                                <form class="ui form small clearing" method="post"
                                    action="{{ secure_url('/admin/reportes/reportXmlTwo') }}" id="downloadPrimasForm">
                                    <div class="three fields">
                                        <div class="field">
                                            <label>
                                                Seleccionar Año <span style="color: red;" class="required">*</span>
                                            </label>
                                            <select name="xml_two_year" class="ui dropdown">
                                                @for ($i = date('Y'); $i >= date('Y') - 9; $i--)
                                                    <option value="{{ $i }}">{{ $i }}</option>
                                                @endfor
                                            </select>
                                        </div>

                                    </div>
                                    <div class="ui error message" style="width: 25%" id="downloadPrimasError"></div>
                                    <div class="field">
                                        <button class="ui primary button">
                                            <i class="search icon"></i>
                                            Generar reporte
                                        </button>
                                        <button class="ui secondary button" id="resetPrimas" type="reset">
                                            <i class="undo icon"></i>
                                            Limpiar filtros
                                        </button>
                                    </div>
                                    {{ csrf_field() }}
                                </form>
                            </div>

                            <div class="title">
                                <i class="dropdown icon"></i>
                                MODELO XML 15
                            </div>
                            <div class="content">
                                <form class="ui form small clearing" method="post"
                                    action="{{ secure_url('/admin/reportes/reportXmlThree') }}" id="downloadPrimasForm">
                                    <div class="three fields">
                                        <div class="field">
                                            <label>
                                                Seleccionar Año <span style="color: red;" class="required">*</span>
                                            </label>
                                            <select name="xml_three_year" class="ui dropdown">
                                                @for ($i = date('Y'); $i >= date('Y') - 9; $i--)
                                                    <option value="{{ $i }}">{{ $i }}</option>
                                                @endfor
                                            </select>
                                        </div>

                                    </div>
                                    <div class="ui error message" style="width: 25%" id="downloadPrimasError"></div>
                                    <div class="field">
                                        <button class="ui primary button">
                                            <i class="search icon"></i>
                                            Generar reporte
                                        </button>
                                        <button class="ui secondary button" id="resetPrimas" type="reset">
                                            <i class="undo icon"></i>
                                            Limpiar filtros
                                        </button>
                                    </div>
                                    {{ csrf_field() }}
                                </form>
                            </div>
                            <div class="title">
                                <i class="dropdown icon"></i>
                                MODELO XML 16
                            </div>
                            <div class="content">
                                <form class="ui form small clearing" method="post"
                                    action="{{ secure_url('/admin/reportes/reportXmlFour') }}" id="downloadPrimasForm">
                                    <div class="three fields">
                                        <div class="field">
                                            <label>
                                                Seleccionar Año <span style="color: red;" class="required">*</span>
                                            </label>
                                            <select name="xml_four_year" class="ui dropdown">
                                                @for ($i = date('Y'); $i >= date('Y') - 9; $i--)
                                                    <option value="{{ $i }}">{{ $i }}</option>
                                                @endfor
                                            </select>
                                        </div>

                                    </div>
                                    <div class="ui error message" style="width: 25%" id="downloadPrimasError"></div>
                                    <div class="field">
                                        <button class="ui primary button">
                                            <i class="search icon"></i>
                                            Generar reporte
                                        </button>
                                        <button class="ui secondary button" id="resetPrimas" type="reset">
                                            <i class="undo icon"></i>
                                            Limpiar filtros
                                        </button>
                                    </div>
                                    {{ csrf_field() }}
                                </form>
                            </div>
                            <div class="title">
                                <i class="dropdown icon"></i>
                                MODELO XML 17
                            </div>
                            <div class="content">
                                <form class="ui form small clearing" method="post"
                                    action="{{ secure_url('/admin/reportes/reportXmlFive') }}" id="downloadPrimasForm">
                                    <div class="three fields">
                                        <div class="field">
                                            <label>
                                                Seleccionar Año <span style="color: red;" class="required">*</span>
                                            </label>
                                            <select name="xml_five_year" class="ui dropdown">
                                                @for ($i = date('Y'); $i >= date('Y') - 9; $i--)
                                                    <option value="{{ $i }}">{{ $i }}</option>
                                                @endfor
                                            </select>
                                        </div>

                                    </div>
                                    <div class="ui error message" style="width: 25%" id="downloadPrimasError"></div>
                                    <div class="field">
                                        <button class="ui primary button">
                                            <i class="search icon"></i>
                                            Generar reporte
                                        </button>
                                        <button class="ui secondary button" id="resetPrimas" type="reset">
                                            <i class="undo icon"></i>
                                            Limpiar filtros
                                        </button>
                                    </div>
                                    {{ csrf_field() }}
                                </form>
                            </div>

                        </div>
                    </div>

                    {{-- ME-2584  Reporte Reaseguro --}}

                    <div class="title">
                        <i class="dropdown icon"></i> Reporte reasegurador
                    </div>
                    <div class="content">
                        <form class="ui form small clearing" method="post"
                            action="{{ secure_url('/admin/reportes/reportReinsurance') }}" id="downloadPrimasForm">
                            <div class="two fields">
                                <div class="field">
                                    <label>
                                        Fecha de inicio <span style="color: red;" class="required">*</span>
                                    </label>
                                    <input class="datepicker" data-value="{{ old('reinsurance_start_date_submit') }}"
                                        name="reinsurance_start_date" placeholder="Desde" type="text" />
                                </div>
                                <div class="field">
                                    <label>
                                        Fecha de finalización <span style="color: red;" class="required">*</span>
                                    </label>
                                    <input class="datepicker" data-value="{{ old('reinsurance_end_date_submit') }}"
                                        name="reinsurance_end_date" placeholder="Hasta" type="text" />
                                </div>
                            </div>
                            <div class="ui error message" style="width: 25%" id="downloadPrimasError"></div>
                            <div class="field">
                                <button class="ui primary button">
                                    <i class="search icon"></i>
                                    Generar reporte
                                </button>
                                <button class="ui secondary button" id="resetPrimas" type="reset">
                                    <i class="undo icon"></i>
                                    Limpiar filtros
                                </button>
                            </div>
                            {{ csrf_field() }}
                        </form>
                    </div>

                    {{-- ME-2492  Reporte de Suscripción --}}

                    <div class="title">
                        <i class="dropdown icon"></i> Reporte de Suscripción
                    </div>
                    <div class="content">
                        <form class="ui form small clearing" method="post"
                            action="{{ secure_url('/admin/reportes/reportSubscription') }}" id="downloadPrimasForm">
                            <div class="two fields">
                                <div class="field">
                                    <label>
                                        Fecha de inicio <span style="color: red;" class="required">*</span>
                                    </label>
                                    <input class="datepicker" data-value="{{ old('subscription_start_date_submit') }}"
                                        name="subscription_start_date" placeholder="Desde" type="text" />
                                </div>
                                <div class="field">
                                    <label>
                                        Fecha de finalización <span style="color: red;" class="required">*</span>
                                    </label>
                                    <input class="datepicker" data-value="{{ old('subscription_end_date_submit') }}"
                                        name="subscription_end_date" placeholder="Hasta" type="text" />
                                </div>
                            </div>
                            <div class="ui error message" style="width: 25%" id="downloadPrimasError"></div>
                            <div class="field">
                                <button class="ui primary button">
                                    <i class="search icon"></i>
                                    Generar reporte
                                </button>
                                <button class="ui secondary button" id="resetPrimas" type="reset">
                                    <i class="undo icon"></i>
                                    Limpiar filtros
                                </button>
                            </div>
                            {{ csrf_field() }}
                        </form>
                    </div>

                @endif



                @if (Auth::user()->isAdmin() ||
                        Auth::user()->isAuditor() ||
                        Auth::user()->Analista_Monitoreo_Control() ||
                        Auth::user()->isCall())
                    <div class="title">
                        <i class="dropdown icon"></i> Reporte responsable de accidentes
                    </div>
                    <div class="content">
                        <form class="ui form small clearing" method="post"
                            action="{{ secure_url('/admin/reportes/reportAdminAutorizadosSiniestro') }}"
                            id="downloadPrimasForm">

                            <div class="ui error message" style="width: 25%" id="downloadPrimasError"></div>
                            <div class="field">
                                <button class="ui primary button">
                                    <i class="search icon"></i>
                                    Generar reporte
                                </button>
                            </div>
                            {{ csrf_field() }}
                        </form>
                    </div>
                @endif

                @if (Auth::user()->isAdmin())
                    <div class="title">
                        <i class="dropdown icon"></i> Reporte condiciones especiales
                    </div>
                    <div class="content">
                        <form class="ui form small clearing" method="post"
                            action="{{ secure_url('/admin/reportes/special_conditions_report') }}">

                            <div class="field">
                                <button class="ui primary button">
                                    <i class="search icon"></i>
                                    Generar reporte
                                </button>

                            </div>
                            {{ csrf_field() }}
                        </form>
                    </div>
                @endif

                @if (Auth::user()->isAdmin())
                    <div class="title">
                        <i class="dropdown icon"></i> Reporte de acciones en mes específico
                    </div>
                    <div class="content">
                        <form class="ui form small clearing" method="post"
                            action="{{ secure_url('/admin/reportes/how_many_actions_by_month') }}"
                            id="downloadActionByMonth">
                            <div class="three fields">
                                <div class="field">
                                    <label>Año <span style="color: red;" class="required">*</span></label>
                                    <div class="ui search selection search dropdown">
                                        <input name="year" type="hidden" value="{{ old('year') }}" />
                                        <i class="dropdown icon"></i>
                                        <div class="default text">
                                            Seleccione uno
                                        </div>
                                        <div class="menu">
                                            <div class="item" data-value="2024">2024</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="field">
                                    <label>Mes <span style="color: red;" class="required">*</span></label>
                                    <div class="ui search selection search dropdown">
                                        <input name="month" type="hidden" value="{{ old('month') }}" />
                                        <i class="dropdown icon"></i>
                                        <div class="default text">
                                            Seleccione uno
                                        </div>
                                        <div class="menu">
                                            <div class="item" data-value="01">Enero</div>
                                            <div class="item" data-value="02">Febrero</div>
                                            <div class="item" data-value="03">Marzo</div>
                                            <div class="item" data-value="04">Abril</div>
                                            <div class="item" data-value="05">Mayo</div>
                                            <div class="item" data-value="06">Junio</div>
                                            <div class="item" data-value="07">Julio</div>
                                            <div class="item" data-value="08">Agosto</div>
                                            <div class="item" data-value="09">Septiembre</div>
                                            <div class="item" data-value="10">Octubre</div>
                                            <div class="item" data-value="11">Noviembre</div>
                                            <div class="item" data-value="12">Diciembre</div>
                                        </div>
                                    </div>
                                </div>
                                <br>
                            </div>
                            <div class="ui error message" style="width: 25%" id="downloadByMonthError"></div>
                            <div class="field">
                                <button class="ui primary button">
                                    <i class="search icon"></i>
                                    Generar reporte
                                </button>
                                <button class="ui secondary button" id="reset" type="reset">
                                    <i class="undo icon"></i>
                                    Limpiar filtros
                                </button>
                            </div>
                            {{ csrf_field() }}
                        </form>
                    </div>

                        <div class="title">
                            <i class="dropdown icon"></i> Reporte de gis
                        </div>
                        <div class="content">
                            <form class="ui form small clearing" method="post"
                                  action="{{ secure_url('/admin/reportes/reportGis') }}" id="downloadPrimasForm">
                                <div class="three fields">
                                    <div class="field">
                                        <label>
                                            Fecha de inicio <span style="color: red;" class="required">*</span>
                                        </label>
                                        <input class="datepicker" data-value="{{ old('gis_start_date_submit') }}"
                                               name="gis_start_date" placeholder="Desde" type="text" />
                                    </div>
                                    <div class="field">
                                        <label>
                                            Fecha de finalización <span style="color: red;" class="required">*</span>
                                        </label>
                                        <input class="datepicker" data-value="{{ old('gis_end_date_submit') }}"
                                               name="gis_end_date" placeholder="Hasta" type="text" />
                                    </div>
                                </div>
                                <div class="ui error message" style="width: 25%" id="downloadPrimasError"></div>
                                <div class="field">
                                    <button class="ui primary button">
                                        <i class="search icon"></i>
                                        Generar reporte
                                    </button>
                                    <button class="ui secondary button" id="resetPrimas" type="reset">
                                        <i class="undo icon"></i>
                                        Limpiar filtros
                                    </button>
                                </div>
                                {{ csrf_field() }}
                            </form>
                        </div>
                @endif


            </div>
            <br>
            <br>



            @if (\Session::has('success'))
                <div class="ui green message" id="success-message">
                    <i class="close icon" id="close-success-message"></i>
                    <div class="header">
                        {!! \Session::get('success') !!}
                    </div>
                </div>
            @endif
            @if (\Session::has('info'))
                <div class="ui yellow message" id="info-message">
                    <i class="close icon" id="close-info-message"></i>
                    <div class="header">
                        {!! \Session::get('info') !!}
                    </div>
                </div>
            @endif
            <!-- Mostrar errores -->
            @if ($errors->any())
                <div class="ui negative message">
                    <div class="header">Errores encontrados</div>
                    <ul class="list">
                        @foreach ($errors->all() as $error)
                            <li>{!! $error !!}</li>
                            <!-- Permite que el HTML se interprete correctamente -->
                        @endforeach
                    </ul>
                </div>
            @endif
        </div>
        <script type="text/javascript">
            $('form#downloadStatesForm').form({
                fields: {
                    state_start_date: {
                        identifier: 'state_start_date',
                        rules: [{
                            type: 'empty',
                            prompt: 'La fecha de desde no puede ir vacía.'
                        }, ]
                    },
                    state_end_date: {
                        identifier: 'state_end_date',
                        rules: [{
                            type: 'empty',
                            prompt: 'La fecha de hasta no puede ir vacía.'
                        }, ]
                    }
                }
            });
            $('form#downloadActionsForm').form({
                fields: {
                    action_start_date: {
                        identifier: 'action_start_date',
                        rules: [{
                            type: 'empty',
                            prompt: 'La fecha de desde no puede ir vacía.'
                        }, ]
                    },
                    action_end_date: {
                        identifier: 'action_end_date',
                        rules: [{
                            type: 'empty',
                            prompt: 'La fecha de hasta no puede ir vacía.'
                        }, ]
                    }
                }
            });
            $('form#determinationITForm').form({
                fields: {
                    it_start_date: {
                        identifier: 'it_start_date',
                        rules: [{
                            type: 'empty',
                            prompt: 'La fecha de desde no puede ir vacía.'
                        }, ]
                    },
                    it_end_date: {
                        identifier: 'it_end_date',
                        rules: [{
                            type: 'empty',
                            prompt: 'La fecha de hasta no puede ir vacía.'
                        }, ]
                    }
                }
            });
            $('form#paymentBasesStatesForm').form({
                fields: {
                    state_pb_start_date: {
                        identifier: 'state_pb_start_date',
                        rules: [{
                            type: 'empty',
                            prompt: 'La fecha de desde no puede ir vacía.'
                        }, ]
                    },
                    state_pb_end_date: {
                        identifier: 'state_pb_end_date',
                        rules: [{
                            type: 'empty',
                            prompt: 'La fecha de hasta no puede ir vacía.'
                        }, ]
                    }
                }
            });
            $('form#creUfForm').form({
                fields: {
                    state_pb_start_date: {
                        identifier: 'cre_start_date',
                        rules: [{
                            type: 'empty',
                            prompt: 'La fecha de desde no puede ir vacía.'
                        }, ]
                    },
                    state_pb_end_date: {
                        identifier: 'cre_end_date',
                        rules: [{
                            type: 'empty',
                            prompt: 'La fecha de hasta no puede ir vacía.'
                        }, ]
                    }
                }
            });
            $('form#budgetItForm').form({
                fields: {
                    state_pb_start_date: {
                        identifier: 'budget_start_date',
                        rules: [{
                            type: 'empty',
                            prompt: 'La fecha de desde no puede ir vacía.'
                        }, ]
                    },
                    state_pb_end_date: {
                        identifier: 'budget_end_date',
                        rules: [{
                            type: 'empty',
                            prompt: 'La fecha de hasta no puede ir vacía.'
                        }, ]
                    }
                }
            });
            $('form#affAcreedorForm').form({
                fields: {
                    state_pb_start_date: {
                        identifier: 'af_ac_start_date',
                        rules: [{
                            type: 'empty',
                            prompt: 'La fecha de desde no puede ir vacía.'
                        }, ]
                    },
                    state_pb_end_date: {
                        identifier: 'af_ac_end_date',
                        rules: [{
                            type: 'empty',
                            prompt: 'La fecha de hasta no puede ir vacía.'
                        }, ]
                    }
                }
            });
            $('form#thrAcreedorForm').form({
                fields: {
                    state_pb_start_date: {
                        identifier: 'thd_ac_start_date',
                        rules: [{
                            type: 'empty',
                            prompt: 'La fecha de desde no puede ir vacía.'
                        }, ]
                    },
                    state_pb_end_date: {
                        identifier: 'thd_ac_end_date',
                        rules: [{
                            type: 'empty',
                            prompt: 'La fecha de hasta no puede ir vacía.'
                        }, ]
                    }
                }
            });
            $('form#determinationPclForm').form({
                fields: {
                    pcl_start_date: {
                        identifier: 'pcl_start_date',
                        rules: [{
                            type: 'empty',
                            prompt: 'La fecha de desde no puede ir vacía.'
                        }, ]
                    },
                    pcl_end_date: {
                        identifier: 'pcl_end_date',
                        rules: [{
                            type: 'empty',
                            prompt: 'La fecha de hasta no puede ir vacía.'
                        }, ]
                    }
                }
            });
            $('form#ReiForm').form({
                fields: {
                    rei_start_date: {
                        identifier: 'rei_start_date',
                        rules: [{
                            type: 'empty',
                            prompt: 'La fecha de desde no puede ir vacía.'
                        }, ]
                    },
                    rei_end_date: {
                        identifier: 'rei_end_date',
                        rules: [{
                            type: 'empty',
                            prompt: 'La fecha de hasta no puede ir vacía.'
                        }, ]
                    }
                }
            });
            $('form#downloadActionByMonth').form({
                fields: {
                    year: {
                        identifier: 'year',
                        rules: [{
                            type: 'empty',
                            prompt: 'El campo año no puede ir vacío.'
                        }, ]
                    },
                    month: {
                        identifier: 'month',
                        rules: [{
                            type: 'empty',
                            prompt: 'El campo mes no puede ir vacío.'
                        }, ]
                    },
                }
            });
            $('form#downloadPrimasForm').form({
                fields: {
                    primas_start_date: {
                        identifier: 'primas_start_date',
                        rules: [{
                            type: 'empty',
                            prompt: 'La fecha de desde no puede ir vacía.'
                        }, ]
                    },
                    primas_end_date: {
                        identifier: 'primas_end_date',
                        rules: [{
                            type: 'empty',
                            prompt: 'La fecha de hasta no puede ir vacía.'
                        }, ]
                    }
                }
            });

            {{-- ME-1158 - Requerimiento 41 --}}
            $('form#reportPendingIncidentAmount').form({
                fields: {
                    rpia_start_date: {
                        identifier: 'rpia_start_date',
                        rules: [{
                            type: 'empty',
                            prompt: 'La fecha de desde no puede ir vacía.'
                        }, ]
                    },
                    rpia_end_date: {
                        identifier: 'rpia_end_date',
                        rules: [{
                            type: 'empty',
                            prompt: 'La fecha de hasta no puede ir vacía.'
                        }, ]
                    }
                }
            });

            {{-- ME-3264 - Requerimiento 130 --}}
            $('form#reportfirstLevelGis').form({
                fields: {
                    rflg_start_date: {
                        identifier: 'rflg_start_date',
                        rules: [{
                            type: 'empty',
                            prompt: 'La fecha de desde no puede ir vacía.'
                        }, ]
                    },
                    rflg_end_date: {
                        identifier: 'rflg_end_date',
                        rules: [{
                            type: 'empty',
                            prompt: 'La fecha de hasta no puede ir vacía.'
                        }, ]
                    }
                }
            });

            function pad(value) {
                return String(value).padStart(2, '0');
            }

            $(document).ready(function() {

                //showStates(); showActions();
                $('.datepicker').pickadate({
                    selectYears: true,
                    selectMonths: true,
                    formatSubmit: 'yyyy-mm-dd'
                });

                $('.ui.accordion').accordion({
                    exclusive: false
                });
                $('.ui.dropdown').dropdown({
                    fullTextSearch: true
                });
                $('.ui.dropdown.remote').dropdown({
                    apiSettings: {
                        // this url parses query server side and returns filtered results
                        url: '/api/affiliates/{query}'
                    },
                });
                $('.ui.checkbox').checkbox();
                $('#reset').click(function() {
                    $('form .ui.dropdown').dropdown('clear');
                    $.each($('form .datepicker'), function(k, el) {
                        console.log(el);
                        $(el).pickadate('picker').clear();
                    });
                });
            });
        </script>
        <script>
            $(document).ready(function() {
                // Obtener la fecha actual
                let today = new Date();
                let year = today.getFullYear();
                let month = String(today.getMonth() + 1).padStart(2, '0');

                // Establecer el atributo max en el input
                $("#affiliate_start_date").attr("max", `${year}-${month}`);
            });
        </script>

    @endsection
