<br><br>
<div class="ui styled fluid accordion" data-tab="definition">
    <div class="title">
        <i class="dropdown icon"></i> Reporte intermediario y tomador
    </div>
    <div class="content">
        <form class="ui form small clearing" method="get"
              action="{{secure_url('/admin/reportes/download_report_intermedary_holder')}}"
              id="downloadMedicationForm">
            <div class="two fields">
                <div class="field">
                    <label>
                        Fecha Inicio <span style="color: red;" class="required">*</span>
                    </label>
                    <input class="datepicker"
                           data-value="{{old('report_start_date')}}"
                           name="report_start_date" placeholder="Desde" type="text"/>
                </div>
                <div class="field">
                    <label>
                        Fecha Fin <span style="color: red;" class="required">*</span>
                    </label>
                    <input class="datepicker"
                           data-value="{{old('report_end_date')}}"
                           name="report_end_date" placeholder="Hasta" type="text"/>
                </div>
            </div>
            <div class="ui error message" style="width: 25%" id="downloadMedicationError"></div>
            <div class="field" style="margin-top: 1em;">
                <button class="ui primary button">
                    <i class="search icon"></i>
                    Generar reporte
                </button>
                <button class="ui secondary button" id="reset" type="reset">
                    <i class="undo icon"></i>
                    Limpiar filtros
                </button>
            </div>
            {{csrf_field()}}
        </form>
    </div>
</div>

@push('scriptsReport')
    <!--Validaciones adicionales-->
    <script>
        $('form#downloadMedicationForm').form({
            fields: {
                report_start_date: {
                    identifier: 'report_start_date',
                    rules: [
                        {
                            type: 'empty',
                            prompt: 'El campos fecha de inicio no puede ir vacío.'
                        },
                    ]
                },
                report_end_date: {
                    identifier: 'report_end_date',
                    rules: [
                        {
                            type: 'empty',
                            prompt: 'El campos fecha de fin no puede ir vacío.'
                        },
                    ]
                },
            }
        });
        // Detectamos cambios en los inputs de fecha y forzamos la validación.
        $("input[name='report_start_date'], input[name='report_end_date']").on('change', function () {
            const fieldName = $(this).attr('name'); // Obtenemos el nombre del campo que cambió.
            $('form#downloadMedicationForm').form('validate field', fieldName); // Validamos ese campo específico.
        });
    </script>
@endpush
