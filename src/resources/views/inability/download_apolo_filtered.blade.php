@extends('layouts.main')

@section('title', 'Descarga de reporte APOLO Filtrados')

@section('menu')
    @parent
@endsection

@section('content')
    <div class="ui basic segment">
        <div class="ui tab segment active" data-tab="definition">
            <div class="ui basic segment">
                <h1 class="ui header">Descarga de reporte APOLO Filtrados</h1>

                <div class="content">
                        <div class="ui one columns grid">
                            <div class="column">
                                <form autocomplete="off" action="{{secure_url('/radicacion-descarga-apolo')}}"
                                      method="get" class="ui green segment small form">
                                    <div>
                                        Es necesario introducir el <b>número de radicado</b> de la incapacidad o el
                                        <b>número de identificación</b> del afiliado
                                        para descargar el/los respectivo/s certificado/s.
                                    </div>
                                    <div class="ui fluid accordion">
                                        <!-- FORMATO -->
                                        <div class="content">
                                            <div class="four fields">
                                                <div class="field" id="doc_number_affiliate">
                                                    <label>Documento afiliado</label>
                                                    <textarea name="doc_number_affiliate" placeholder="CC SEPARADOS POR COMA" rows="3"></textarea>
                                                </div>
                                                <div class="field">
                                                    <div class="field">
                                                        <label>&nbsp;</label>
                                                        <button class="ui primary button" id="submit-btn">
                                                            <i class="cloud download icon"></i>
                                                            Buscar
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="fields">
                                                <div class="field d-none" id="btn-certificate">
                                                    <label>&nbsp;</label>
                                                    <a href="" target="_blank" class="ui basic mini green button" id="submit-btn-certificate">
                                                        Ver Certificado
                                                    </a>
                                                </div>
                                                <div class="field d-none" id="message-certificate">
                                                    <label class="text-warning">Affiliado no existe</label>
                                                </div>
                                            </div>
                                        </div>
                                        <!-- END: FORMATO -->
                                    </div>
                                    {{csrf_field()}}
                                </form>
                                <div class="d-none" id="cover-spin"></div>
                            </div>
                        </div>
                        <div class="ui columns grid d-none" id="show-links">
                            <div class="column">
                                <div class="ui fluid accordion">
                                    <div class="content">
                                        <div id="inject-data"
                                             style="display: block; margin-left: auto; margin-right: auto; width: 40%"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
            </div>
        </div>

    </div>
    <script type="text/javascript">
        $(document).ready(function () {
            $('.ui.dropdown').dropdown();
            $('form .datepicker').pickadate({
                selectYears: 100,
                selectMonths: true,
                max: new Date(),
                formatSubmit: 'yyyy-mm-dd',
                format: 'yyyy-mm-dd'
            });

            $('#submit-btn').click((evt) => {
                evt.preventDefault();
                $('#show-links').hide();
                const docNumber = $("textarea[name='doc_number_affiliate']").val();
                if (docNumber) {
                    $('#cover-spin').css('display', 'block');
                    $.getJSON('/api/download-certificate-apolo/' + docNumber, function (json) {
                        console.log(json.affiliates)
                        $('#cover-spin').hide();
                        if (json.affiliates !== 'None') {
                            if (json.affiliates) {
                                var idAffiliate = docNumber;
                                $('#btn-certificate').css('display', 'block');
                                $('#message-certificate').css('display', 'none');
                                $('#submit-btn-certificate').attr('href',
                                    '/load-apolo-web-services-filtered/' + idAffiliate );
                                $('#submit-btn-certificate').attr('data-value', idAffiliate);
                            } else {
                                $('#btn-certificate').css('display', 'none');
                                $('#message-certificate').css('display', 'block');
                            }
                        } else {
                            $('#btn-certificate').css('display', 'none');
                            $('#message-certificate').css('display', 'block');
                        }
                    });
                }
            });
        });
    </script>
    <style type="text/css">
        #cover-spin {
            position: fixed;
            width: 100%;
            left: 0;
            right: 0;
            top: 0;
            bottom: 0;
            background-color: rgba(255, 255, 255, 0.7);
            z-index: 9999;
            display: none;
        }

        @-webkit-keyframes spin {
            from {
                -webkit-transform: rotate(0deg);
            }
            to {
                -webkit-transform: rotate(360deg);
            }
        }

        @keyframes spin {
            from {
                transform: rotate(0deg);
            }
            to {
                transform: rotate(360deg);
            }
        }

        #cover-spin::after {
            content: '';
            display: block;
            position: absolute;
            left: 48%;
            top: 40%;
            width: 40px;
            height: 40px;
            border-style: solid;
            border-color: green;
            border-top-color: transparent;
            border-width: 4px;
            border-radius: 50%;
            -webkit-animation: spin .8s linear infinite;
            animation: spin .8s linear infinite;
        }
    </style>
@endsection
