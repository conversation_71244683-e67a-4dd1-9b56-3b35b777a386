@extends('layouts.main')
<style type="text/css">
    .column {
        max-width: 450px;
    }
</style>


@section('sidebar')
    @parent
@endsection

@section('content')
    <div class="ui basic segment">
        <div class="ui middle aligned center aligned main grid">
            <div class="column">
                <br><br><br>
                <form action="{{secure_url('/radicacion_oficina')}}" method="POST" class="ui large form">
                    {{ csrf_field() }}
                    <div class="ui left aligned basic clearing segment">
                        <div class="field">
                            <label>Usuario</label>
                            <div class="ui left icon input">
                                <i class="user icon"></i>
                                <input class="minus" type="text" name="login" placeholder="usuario">
                            </div>
                        </div>
                        <div class="field">
                            <label>Contraseña</label>
                            <div class="ui left icon input">
                                <i class="lock icon"></i>
                                <input type="password" name="password" placeholder="contraseña">
                            </div>
                        </div>
                        <div class="ui green right floated submit button">Continuar</div>
                    </div>
                    @if(count($errors))
                        <div class="ui error message" style="display: block;">
                            <ul class="list">
                                <li>Usuario y/o contraseña no coinciden.</li>
                                <li>Fallo validación del captcha.</li>
                            </ul>
                        </div>
                    @elseif(session('blocked'))
                        <div class="ui error message" style="display: block;">
                            <ul class="list">
                                <li>{{session('blocked')}}</li>
                            </ul>
                        </div>
                    @else
                        <div class="ui error message"></div>
                    @endif
                </form>
            </div>
        </div>
    </div>
    <script>
        $(document).ready(function() {
            $('.ui.checkbox').checkbox();
            $('.ui.form').form({
                fields: {
                    login: {
                        identifier  : 'login',
                        rules: [
                            {
                                type   : 'empty',
                                prompt : 'Please enter a valid username or email'
                            }
                        ]
                    },
                    password: {
                        identifier  : 'password',
                        rules: [
                            {
                                type   : 'empty',
                                prompt : 'Please enter a password'
                            }
                        ]
                    },
                    gRecaptchaResponse: {
                        identifier  : 'g-recaptcha-response',
                        rules: [
                            {
                                type   : 'empty',
                                prompt : 'Please validate the captcha'
                            }
                        ]
                    }
                },
                onSuccess: function(e, fields) {
                    if ($('#g-recaptcha-response').val() == '') {
                        $('.ui.form').form('add errors', {
                            gRecaptchaResponse: 'Please validate the captcha',
                        });
                        return false;
                    }
                    setTimeout(function () {
                        location.reload();
                    }, 20000);
                },
                onFailure: function(formErrors, fields) {
                    if ($('#g-recaptcha-response').val() == '') {
                        formErrors.push('Please validate the captcha');
                        fields['gRecaptchaResponse'] = 'Please validate the captcha';
                    }
                    $('.ui.form').form('add errors', formErrors);
                    return false;
                }
            });
        });
    </script>

@endsection