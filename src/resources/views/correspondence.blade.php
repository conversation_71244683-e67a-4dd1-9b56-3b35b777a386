@extends('layouts.main')

@section('title', 'CORRESPONDENCIA')

@section('menu')
    @parent
@endsection

@section('content')
<div class="ui basic segment">
    <h1 class="ui header">
        Bandeja de correspondencia
    </h1>
    <div class="ui segments">
        <div class="ui secondary segment">
            <form autocomplete="off" method="post" id="order_form" class="ui form small clearing">
                <div class="inline fields">
                    <div class="seven wide field">
                        <label style="width: 175px;">Número de orden global:</label>
                        <div class="ui right action input">
                            <input name="id" value="{{$order->consecutive}}" type="text">
                            <button type="button" class="ui basic icon button"><i class="search icon"></i></button>
                        </div>
                    </div>
                    <div class="seven wide field">
                        <label style="width: 175px;">Fecha de envió:</label>
                        @if($order->closed)
                        <p>{{$order->send_at->formatLocalized('%Y/%m/%d')}}</p>
                        @else
                        <input class="datepicker" type="text" name="send_at" data-value="{{$order->next_day}}">
                        @endif
                    </div>
                    <div class="two wide field">
                        @if($order->closed)
                        CERRADA
                        @else
                        <label>&nbsp;</label>
                        <button class="ui basic fluid green button"><i class="lock icon"></i> Cerrar</button>
                        @endif
                    </div>
                </div>   
                @if($order->closed)      
                <div class="field">
                <label>Estado de la Orden</label>
                <div data-value="{{$order->items()->where('state', '>', 2)->count()}}" data-total="{{$order->items()->count()}}" class="ui indicating tiny progress">
                  <div class="bar">
                    <div class="progress"></div>
                  </div>
                  <div class="label">{{$order->items()->where('state', '>', 2)->count()}} de {{$order->items()->count()}}</div>
                </div>
                </div>
                @endif
                {{csrf_field()}}
            </form>
        </div>
        <form id="data_form" method="post" class="ui segment form">
            <table style="font-size: 0.8em; line-height: 1em;" class="ui celled striped very compact very small definition table" id="results">
                <thead>
                    <tr>
                        <th></th>
                        <th>Id</th>
                        <th>Actividad</th>
                        <th>Fecha</th>
                        <th>Nro. caso</th>
                        <th>Tipo Entidad</th>
                        <th>Nombre</th>
                        <th>Dirección</th>
                        <th>Teléfono</th>
                        <th>Departamento</th>
                        <th>Municipio</th>
                        <th>Estado</th>
                        @if ($order->closed)
                        <th>Nro. Guía</th>
                        @endif
                        <th>Descripción</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($items as $item)
                    <tr class="{{$item->order ? '' : 'positive'}}">
                        <td class="collapsing">
                            @if($item->order)
                            <div class="ui fitted slider checkbox">
                              <input tabindex="-1" data-activity="{{$item->activity_id}}" data-id="{{$item->id}}" name="checked[{{$item->id}}]" autocomplete="off" type="checkbox">
                              <label></label>
                            </div>
                            @endif
                        </td>
                        <td><a tabindex="-1" href="{{secure_url("correspondencia/item/$item->id")}}">{{$item->order or 'N/A'}}</a></td>
                        <td><a tabindex="-1" href="{{secure_url("servicio/$item->activity_id")}}">{{$item->activity->service->name}}</a></td>
                        <td>{{$item->created_at->formatLocalized('%d/%m/%Y')}}</td>
                        <td>{{$item->activity->affiliate->doc_number}}</td>
                        <td>{{App\CorrespondenceItem::$ENTITY_TYPES[$item->entity_type]}}</td>
                        <td>{{$item->name}}</td>
                        <td>{{$item->address}}</td>
                        <td>{{$item->phone}}</td>
                        <td>{{$item->department_name}}</td>
                        <td>{{$item->municipality_name}}</td>
                        <td>{{$CORRESPONDENCE_STATES[$item->state]}}</td>
                        @if ($order->closed)
                            <td>{{$item->guide}}</td>
                        @endif
                        <td style="width: 175px;">
                            <div class="ui fluid input">
                                <input autocomplete="off" class="minus" name="description[{{$item->id}}]" style="display: none;" type="text">
                            </div>
                        </td>
                    </tr>
                    @endforeach
                </tbody>
                <tfoot class="full-width">
                    <tr>
                        <th colspan="8">
                            <button type="button" onclick="deleteItems()" class="ui basic red mini button"><i class="remove icon"></i> Eliminar</button>
                            <button type="button" onclick="priorizeItems()" class="ui basic orange mini button"><i class="external icon"></i> Priorizado</button>
                            @if($order->closed)
                            <a target="_blank" href="{{secure_url('correspondencia/excel/' . $order->id)}}" class="ui basic green mini button"><i class="file excel outline icon"></i> Excels</a>
                            @endif
                            <a target="_blank" href="{{secure_url('correspondencia/excelfiles/' . $order->id)}}" class="ui basic black mini button"><i class="download icon"></i> Excel de Archivos</a>
                            <a href="{{secure_url('correspondencia/recreate/' . $order->id)}}" class="ui basic blue mini button"><i class="refresh icon"></i> Recarcular consecutivos</a>
                            <a href="{{secure_url('correspondencia/sort/' . $order->id)}}" class="ui basic blue mini button"><i class="sort icon"></i> Ordenar</a>
                        </th>
                        @if ($order->closed)
                        <th colspan="6">{{$items->links()}}</th>
                        @else
                        <th colspan="5">{{$items->links()}}</th>
                        @endif
                    </tr>
                </tfoot>
            </table>
            {{csrf_field()}}
        </form>       
        <div class="ui segment">
            <div class="ui two columns grid">
                <div class="column">
                    <form autocomplete="off" action="{{secure_url('correspondencia/'.$order->id.'/guides')}}" method="post" enctype="multipart/form-data" class="ui green segment small form">
                        <div class="ui dividing header">Cargar guiás</div>
                        <div class="required field">
                            <label>Archivo Excel [Columnas: ORDEN - ID - GUIA]</label>
                            <input type="file" name="guides" accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel">
                        </div>
                        <div class="field">
                            <button class="ui green button"><i class="upload icon"></i> Cargar</button>
                        </div>
                        {{csrf_field()}}
                    </form>                    
                </div>
                <div class="column">                    
                    <form autocomplete="off" action="{{secure_url('correspondencia/'.$order->id.'/notifications')}}" method="post" class="ui purple segment small form">
                        <div class="ui dividing header">Cargar notificaciones</div>
                        <div class="required field">
                            <label>
                                Archivo ZIP:
                            </label>
                            <button id="notificationsfilebtn" type="button" class="ui basic mini blue button"><i class="upload icon"></i> Cargar documento</button>
                            <input style="display: none;" id="notificationsfile" name="notificationsfile" type="file">
                            <input id="notificationsfile_text" name="notifications" type="hidden"/>
                            <p></p>
                            <div>
                                REGISTRO.xslx <span style="color: gray;">[Columnas: CC - SERVICIO - ENTIDAD - PROCESO - FECHA]</span> <br />
                                ARCHIVOS/*  <span style="color: gray;">[Formato archivos: CC-SERVICIO-ENTIDAD-PROCESO]</span>
                            </div>
                        </div>
                        <div class="field">
                            <button class="ui purple button"><i class="upload icon"></i> Cargar</button>
                        </div>
                        {{csrf_field()}}
                    </form> 
                </div>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript">
var deleteItems = function() {
    var data = [];
    var inputs = $('input[type=checkbox]:checked');
    var submit = true;

    $.each(inputs, function(k, v) {
        if ($('input[name="description[' + $(v).data('id') + ']"]').val() == ''){
            $('input[name="description[' + $(v).data('id') + ']"]').parent().addClass('error');
            submit = false;
        }
    });

    if (submit) {
        $('#data_form').attr('action', '{{secure_url('correspondencia/'.$order->id.'/delete')}}');
        $('#data_form').submit();
    }
}
var priorizeItems = function() {
    var data = [];
    var inputs = $('input[type=checkbox]:checked');
    var submit = true;

    $.each(inputs, function(k, v) {
        if ($('input[name="description[' + $(v).data('id') + ']"]').val() == ''){
            $('input[name="description[' + $(v).data('id') + ']"]').parent().addClass('error');
            submit = false;
        }
    });

    if (submit) {
        $('#data_form').attr('action', '{{secure_url('correspondencia/'.$order->id.'/priorize')}}');
        $('#data_form').submit();
    }
}
var makeUIDF = function(length) {
    var text = "";
    var possible = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";

    for (var i = 0; i < length; i++)
        text += possible.charAt(Math.floor(Math.random() * possible.length));

    return text + new Date().getTime();
};
var onUploadF = function(err, data) {
    var current = new Date().getTime();
    $(this).parent().find('button').removeClass('blue');
    if (err) {
        $(this).parent().find('button').addClass('red');
        $(this).parent().find('button').html('<i class="warning icon"></i> ERROR');
        return alert('There was an error uploading your photo: ', err.message);
    }

    $(this).parent().find('button').addClass('green');
    $(this).parent().find('button').html('<i class="checkmark icon"></i> OK');
    $(this).parent().find('p').html('Completado en ' + Math.round((current - initF) / 1000) + ' seg.');
    $(this).parent().find('input[type=hidden]').val(data.Key);
};
var onProgressF = function(progress) {
    var current = new Date().getTime();
    var speed = Math.floor((progress.loaded) / (current - initF));
    var remaing = Math.round((progress.total - progress.loaded) / (speed * 1024));
    var perc = Math.floor(progress.loaded / progress.total * 1000) / 10;
    $(this).parent().find('button').html('<i class="wait icon"></i> ' + perc + ' %');
    if (remaing <= 120){
        $(this).parent().find('p').html('Faltan: &sim;' + remaing + ' seg.<br /> (vel. &sim;'+ speed +' KB/s)');
    } else {
        $(this).parent().find('p').html('Faltan: &sim;' + Math.round(remaing / 60) + ' min.<br /> (vel. &sim;'+ speed +' KB/s)');
    }
};
$(document).ready(function() {

    $('#notificationsfilebtn').click(function() {
        $('#notificationsfile').click();
    });

    $('#notificationsfile').change(function() {
        $(this).parent().find('button').removeClass('green');
        $(this).parent().find('button').removeClass('red');
        $(this).parent().find('button').addClass('blue');
        var files = $(this).prop("files");
        if (!files.length) {
            return alert('Please choose a file to upload first.');
        }
        var file = files[0];
        var fileName = file.name;
        var fileNameExt = file.name.split('.').pop();
        var fileNameHash = makeUIDF(27);
        var albumPhotosKey = encodeURIComponent('notifications_zip') + '/';

        var photoKey = albumPhotosKey + fileNameHash + '.' + fileNameExt;
        var onUploadB = onUploadF.bind(this);
        var onProgressB = onProgressF.bind(this);

        initF = new Date().getTime();
        s3.upload({
            Key: photoKey,
            Body: file,
        }, {partSize: 20 * 1024 * 1024, queueSize: 1}, onUploadB).on('httpUploadProgress', onProgressB);
    });

    $('input[name=id]').change(function() {
        location.href = '/correspondencia/' + $(this).val();
    });
    $('input[name*="description"]').blur(function() {
        if ($(this).val() == '') {
            $(this).parent().addClass('error');
        } else {
            $(this).parent().removeClass('error');
        }
    });

    $('form input').keydown(function(event){
        if(event.keyCode == 13) {
          event.preventDefault();
          return false;
        }
    });

    $('#order_form').form({
        fields: {id: 'empty'},
        onSuccess: function() {
            var r = confirm('¿Seguro que desea cerrar esta orden?');
            if (r) {
                return true;
            }else{
                event.preventDefault();
            }
        }
    });

    $('.datepicker').pickadate({
        selectYears: true,
        selectMonths: true,
        format: 'mmmm dd, yyyy',
        formatSubmit: 'yyyy-mm-dd'
    });

	$('.ui.accordion').accordion({
		exclusive: false
	});

    $('.ui.progress').progress();

	$('.ui.checkbox').checkbox({
        onChecked: function() {
            $('input[name="description[' + $(this).data('id') + ']"]').show();
        },
        onUnchecked: function() {
            $('input[name="description[' + $(this).data('id') + ']"]').val('');
            $('input[name="description[' + $(this).data('id') + ']"]').hide();
        }
    });

	// $('#results').DataTable({
 //        "iDisplayLength": 50,
 //        "lengthMenu": [10, 25,50,100, 250, 500],
 //        "ordering": false,
	// 	"language": {
 //        	"url": "//cdn.datatables.net/plug-ins/9dcbecd42ad/i18n/Spanish.json"
 //    	}
	// });
});
</script>
@endsection
