@extends('layouts.main')

@section('title', 'Radicación de Incapacidades')

@section('menu')
    @parent
@endsection

@section('content')
    <div class="ui basic segment">


        <div class="ui tab segment active" data-tab="definition">
            <div class="ui basic segment">

                <h1 class="ui header">Radicación Siniestro</h1>
                <div class="content">

                    <div class="ui">
                        <div class="ui one columns grid">

                            <div class="column">
                                @if($session)
                                    @if( ((strtotime(date('Y-m-d') . ' 22:00:00' ) >= strtotime(date('Y-m-d H:i:s'))) && (strtotime(date('Y-m-d H:i:s')) >= strtotime(date('Y-m-d') . ' 07:00:00' ))) )
                                        <form autocomplete="off" action="{{secure_url("radicacion_siniestro/save")}}"
                                              id="dictamen"
                                              method="post" class="ui small form">

                                            <input name="radication_date" type="hidden" value="{{date('Y-m-d')}}">
                                            <div class="ui secondary segment">
                                                <div class="ui two columns grid">
                                                    <div class="column"><b>Fecha de
                                                            radicación:</b> {{Carbon\Carbon::createFromFormat('Y-m-d', date('Y-m-d'))->formatLocalized('%B %d, %Y')}}
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="ui fluid accordion">
                                                <!-- FORMATO -->
                                                <div class="content" style="z-index: 2;position: relative;">

                                                    <div class="four fields">
                                                        <div class="required field">
                                                            <label>Tipo de solicitud</label>
                                                            <div class="ui selection dropdown">
                                                                <input type="hidden" name="type_request"
                                                                       value="" required>
                                                                <i class="dropdown icon"></i>
                                                                <div class="default text">Tipo de solicitud</div>
                                                                <div class="menu">
                                                                    <div class="item" data-value="ITP">ITP</div>
                                                                    <div class="item" data-value="VIDA">VIDA</div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div class="four fields">
                                                        <div class="required field">
                                                            <label>Tipo de documento</label>
                                                            <div class="ui selection dropdown">
                                                                <input type="hidden" name="doc_type_affiliate"
                                                                       value="{{ "CC" }}" required>
                                                                <i class="dropdown icon"></i>
                                                                <div class="default text">Tipo de documento</div>
                                                                <div class="menu">
                                                                    @foreach($DOC_TYPES as $k => $v)
                                                                        <div class="item"
                                                                             data-value="{{$k}}">{{$v}}</div>
                                                                    @endforeach
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="required field">
                                                            <label>Número de documento</label>
                                                            <input name="doc_number_affiliate" type="text">
                                                        </div>
                                                        <div class="required field">
                                                            <label>Nombres</label>
                                                            <input name="first_name_affiliate">
                                                        </div>
                                                        <div class="required field">
                                                            <label>Apellidos</label>
                                                            <input name="last_name_affiliate">
                                                        </div>
                                                    </div>
                                                    <div class="four fields">
                                                        <div class="required field">
                                                            <label>Número de crédito</label>
                                                            <div class="ui selection dropdown">
                                                                <input type="hidden" name="credit_number" required>
                                                                <i class="dropdown icon"></i>
                                                                <div class="default text">Número de crédito</div>
                                                                <div class="menu" id="credit_number">
                                                                    <div class="item" data-value="9876">9876</div>
                                                                    <div class="item" data-value="5432">5432</div>
                                                                    <div class="item" data-value="1098">1098</div>
                                                                    <div class="item" data-value="9877">9877</div>
                                                                    <div class="item" data-value="5433">5433</div>
                                                                    <div class="item" data-value="1099">1099</div>
                                                                    <div class="item" data-value="9878">9878</div>
                                                                    <div class="item" data-value="5434">5434</div>
                                                                    <div class="item" data-value="1097">1097</div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="required field">
                                                            <label>Número de poliza</label>
                                                            <div class="ui selection dropdown">
                                                                <input type="hidden" name="policy_number" required>
                                                                <i class="dropdown icon"></i>
                                                                <div class="default text">Número de Poliza</div>
                                                                <div class="menu" id="policy_number">
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="two fields">
                                                        <div class="required four wide field">
                                                            <label>
                                                                Soporte
                                                            </label>
                                                            <button id="notificationsfilebtn" type="button"
                                                                    class="ui basic mini blue button">
                                                                <i class="upload icon">

                                                                </i> Cargar documento
                                                            </button>
                                                            <input style="display: none;" id="notificationsfile"
                                                                   name="notificationsfile" type="file">
                                                            <input id="notificationsfile_text" name="notifications"
                                                                   type="hidden"/>
                                                            <p></p>
                                                        </div>
                                                        <div class="four wide field">
                                                            <label>
                                                                Soporte
                                                            </label>
                                                            <button id="notificationsfilebtn2" type="button"
                                                                    class="ui basic mini blue button">
                                                                <i class="upload icon">

                                                                </i> Cargar documento
                                                            </button>
                                                            <input style="display: none;" id="notificationsfile2"
                                                                   name="notificationsfile2" type="file">
                                                            <input id="notificationsfile_text2" name="notifications2"
                                                                   type="hidden"/>
                                                            <p></p>
                                                        </div>

                                                    </div>
                                                    <div class="ui info message" style="opacity: 0.75;max-width: 50%">
                                                        <div class="header">
                                                            <em> Estos son los documentos que deben aportar para el análisis:<br>

                                                                - Solicitud de reclamación  <br>
                                                                - Copia del documento de identidad (cedula de ciudadanía) ampliada al 150% por ambas caras. <br>
                                                                - Registro civil de defunción (VIDA)<br>
                                                                - Dictamen de pérdida de capacidad Laboral (ITP)<br>
                                                                - Soporte de pagos realizados (Crédito)<br>
                                                            </em>
                                                        </div>
                                                    </div>

                                                </div>
{{--                                                <div style="position: absolute; bottom: 7rem; text-align: right  ; width: 100%; z-index: 1;">--}}
{{--                                                    <img src="/file/client_logo/rci_palig_logo.png" height="150"--}}
{{--                                                         style="margin-right: 10rem">--}}
{{--                                                </div>--}}
                                                <!-- END: FORMATO -->
                                            </div>
                                            <div class="ui basic segment" style="z-index: 2;position: relative;">
                                                <div class="fields">
                                                    <div class="four wide field">
                                                        <button class="ui green fluid button"><i class="save icon"></i>
                                                            Guardar
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                            {{csrf_field()}}
                                        </form>
                                    @else
                                        <div class="ui info message">
                                            <div class="header">
                                                <em> Este servicio esta habilitado de 7am a 10pm.</em>
                                            </div>
                                        </div>
                                    @endif
                                @else
                                    <div class="ui success message">
                                        <i class="close icon"></i>
                                        <div class="header">
                                            <em> Radicación realizada satisfactoriamente
                                            </em>
                                        </div>
                                    </div>
                                    <a href="{{secure_url('radicacion_siniestro')}}">Regresar a radicación siniestro</a>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <style type="text/css">
        .ui.grid .column {
            padding: 0.5rem 1rem !important;
        }

        .ui.accordion .title {
            text-transform: uppercase;
        }

        .field > h3 {
            text-align: center;
            margin-top: 1.25rem;
        }

        .ui.search > .results {
            width: 30rem;
        }

        .ui.search > .results .result .title {
            padding: 0 !important;
            border: none !important;
            text-transform: none;
        }

        .ui.search > .results .result .content {
            padding: 0 !important;
        }
    </style>
    <script type="text/javascript">
        var generatePDF = function () {
            $('form#dictamen').attr('target', '_blank');
            $('form#dictamen').attr('action', '{{secure_url('/radicacion_siniestro/pdf')}}');
            $('form#dictamen').submit();
            $('form#dictamen').removeAttr('target');
            $('form#dictamen').attr('action', '{{secure_url('/radicacion_siniestro/save')}}');
            return false
        };

        $(document).ready(function () {
            $('.ui.accordion .ui.grid .row').css('padding-top', 0);
            $('.ui.accordion .ui.grid .column').css('padding-top', 0);
            $('.ui.accordion').accordion({
                exclusive: false
            });
            $('form#dictamen').form();
            $('form#dictamen a.red').click(function () {
                $(this).parent().parent().remove();
            });
            $('form .ui.dropdown').dropdown({
                forceSelection: false
            });

            $('form input').keydown(function (event) {
                if (event.keyCode == 13) {
                    event.preventDefault();
                    return false;
                }
            });

            $('form .datepicker').pickadate({
                selectYears: 100,
                selectMonths: true,
                max: new Date(),
                formatSubmit: 'yyyy-mm-dd',
                format: 'mmm dd, yyyy'
            });

            $('form#dictamen').form({
                fields: {
                    doc_type_affiliate: ['empty'],
                    doc_number_affiliate: ['empty'],
                    first_name_affiliate: ['empty'],
                    last_name_affiliate: ['empty'],
                    notifications: ['empty'],
                }
            });

            $("input[name='doc_number_affiliate']").change(function () {
                var doc_number = $(this).val();
                var doc_type = $("input[name='doc_type_affiliate']").val();
                $.getJSON('/api/affiliate/' + doc_number + "/" + doc_type, function (json) {
                    existAffiliate = json[0];
                    if (existAffiliate) {
                        $("input[name='first_name_affiliate']").val(existAffiliate.first_name);
                        $("input[name='last_name_affiliate']").val(existAffiliate.last_name);
                    } else {
                        $("input[name='first_name_affiliate']").val('');
                        $("input[name='last_name_affiliate']").val('');
                    }
                });
            })
            $("input[name='doc_number_affiliate']").change(function () {
                var doc_number = $(this).val();
                var doc_type = $("input[name='doc_type_affiliate']").val();
                $.getJSON('/api/affiliate_radication/' + doc_number + "/" + doc_type, function (json) {
                    existAffiliate = json;
                    if (existAffiliate) {
                        //LLENANDO INFO NUMEROS DE POLIZA Y CREDITO
                        var policy_number = '';
                        var credit_number = '';
                        existAffiliate.debtor.forEach (function (element) {
                            credit_number += '<option class="item" data-value="'+element.credit_number+'">'+element.credit_number+'</option>';
                        });
                        existAffiliate.debtor_policy.forEach (function (element) {
                           policy_number += '<option class="item" data-value="'+element+'">'+element+'</option>';
                        });
                        $('#policy_number').html(policy_number);
                        $('#credit_number').html(credit_number);
                    } else {
                        $('#policy_number').html('');
                        $('#credit_number').html('');
                    }
                });
            });

        });
    </script>
    <script type="text/javascript">
        var makeUIDF = function (length) {
            var text = "";
            var possible = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";

            for (var i = 0; i < length; i++)
                text += possible.charAt(Math.floor(Math.random() * possible.length));

            return text + new Date().getTime();
        };
        var onUploadF = function (err, data) {
            var current = new Date().getTime();
            $(this).parent().find('button').removeClass('blue');
            if (err) {
                $(this).parent().find('button').addClass('red');
                $(this).parent().find('button').html('<i class="warning icon"></i> ERROR');
                return alert('Hubo un error al subir el documento: ', err.message);
            }

            $(this).parent().find('button').addClass('green');
            $(this).parent().find('button').html('<i class="checkmark icon"></i> OK');
            $(this).parent().find('p').html('Completado en ' + Math.round((current - initF) / 1000) + ' seg.');
            $(this).parent().find('input[type=hidden]').val(data.Key);
        };
        var onProgressF = function (progress) {
            var current = new Date().getTime();
            var speed = Math.floor((progress.loaded) / (current - initF));
            var remaing = Math.round((progress.total - progress.loaded) / (speed * 1024));
            var perc = Math.floor(progress.loaded / progress.total * 1000) / 10;
            $(this).parent().find('button').html('<i class="wait icon"></i> ' + perc + ' %');
            if (remaing <= 120) {
                $(this).parent().find('p').html('Faltan: &sim;' + remaing + ' seg.<br /> (vel. &sim;' + speed + ' KB/s)');
            } else {
                $(this).parent().find('p').html('Faltan: &sim;' + Math.round(remaing / 60) + ' min.<br /> (vel. &sim;' + speed + ' KB/s)');
            }
        };
        $(document).ready(function () {

            $('#notificationsfilebtn').click(function () {
                $('#notificationsfile').click();
            });
            $('#notificationsfilebtnz').click(function () {
                $('#notificationsfilez').click();
            });
            $('#notificationsfilebtn2').click(function () {
                $('#notificationsfile2').click();
            });
            $('#notificationsfilebtn4').click(function () {
                $('#notificationsfile4').click();
            });

            setTimeout(function () {
                location.reload();
            }, 600000);

            $('#notificationsfile, #notificationsfile2, #notificationsfile4, #notificationsfilez').change(function () {
                $(this).parent().find('button').removeClass('green');
                $(this).parent().find('button').removeClass('red');
                $(this).parent().find('button').addClass('blue');
                var files = $(this).prop("files");
                if (!files.length) {
                    return alert('Please choose a file to upload first.');
                }
                var file = files[0];
                var fileName = file.name;
                var fileNameExt = file.name.split('.').pop();
                var fileNameHash = makeUIDF(27);
                var albumPhotosKey = encodeURIComponent('radicacion_siniestro') + '/' + encodeURIComponent('supports') + '/';

                var photoKey = albumPhotosKey + fileNameHash + '.' + fileNameExt;
                var onUploadB = onUploadF.bind(this);
                var onProgressB = onProgressF.bind(this);

                initF = new Date().getTime();
                s3.upload({
                    Key: photoKey,
                    Body: file,
                }, {partSize: 20 * 1024 * 1024, queueSize: 1}, onUploadB).on('httpUploadProgress', onProgressB);
            });

        });
    </script>
@endsection
