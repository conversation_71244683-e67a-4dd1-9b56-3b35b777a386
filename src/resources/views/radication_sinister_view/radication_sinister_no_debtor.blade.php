@extends('layouts.main')

@section('title', 'Radicación Cotización Seguro')

@section('menu')
    @parent
@endsection

@section('content')
    <div class="ui basic segment">
        <div class="ui tab segment active" data-tab="definition">
            <div class="ui basic segment">

                <h1 class="ui header">Radicación Cotización Seguro</h1>
                <div class="content">

                    <div class="ui">
                        <div class="ui one columns grid">
                            <div class="column">
                                <div class="ui error message">
                                    <i class="close icon"></i>
                                    <div class="header">
                                        <em>
                                            No existe un servicio de Seguro Deudores que cuente con los números de poliza y de crédito ingresados
                                        </em>
                                    </div>
                                </div>
                                <a href="{{secure_url('radicacion_siniestro')}}">Regresar radicación Siniestro</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <style type="text/css">
        .ui.grid .column {
            padding: 0.5rem 1rem !important;
        }

        .ui.accordion .title {
            text-transform: uppercase;
        }

        .field > h3 {
            text-align: center;
            margin-top: 1.25rem;
        }

        .ui.search > .results {
            width: 30rem;
        }

        .ui.search > .results .result .title {
            padding: 0 !important;
            border: none !important;
            text-transform: none;
        }

        .ui.search > .results .result .content {
            padding: 0 !important;
        }
    </style>
    <script type="text/javascript">
        var generatePDF = function () {
            $('form#dictamen').attr('target', '_blank');
            $('form#dictamen').attr('action', '{{secure_url('/cotizaciones/pdf')}}');
            $('form#dictamen').submit();
            $('form#dictamen').removeAttr('target');
            $('form#dictamen').attr('action', '{{secure_url('/cotizaciones/save')}}');
            return false;
        };

        $(document).ready(function () {
            $('.ui.accordion .ui.grid .row').css('padding-top', 0);
            $('.ui.accordion .ui.grid .column').css('padding-top', 0);
            $('.ui.accordion').accordion({
                exclusive: false
            });
            $('form#dictamen').form();
            $('form#dictamen a.red').click(function () {
                $(this).parent().parent().remove();
            });
            $('form .ui.dropdown').dropdown({
                forceSelection: false
            });

            $('form input').keydown(function (event) {
                if (event.keyCode == 13) {
                    event.preventDefault();
                    return false;
                }
            });

            $('form .datepicker').pickadate({
                selectYears: 100,
                selectMonths: true,
                max: new Date(),
                formatSubmit: 'yyyy-mm-dd',
                format: 'mmm dd, yyyy'
            });

            $('form#dictamen').form({
                fields: {
                    doc_type_affiliate: ['empty'],
                    doc_number_affiliate: ['empty'],
                    first_name_affiliate: ['empty'],
                    last_name_affiliate: ['empty'],
                    notifications: ['empty'],
                }
            });

            $.getJSON('/js/colombia.json', function(json) {
                json.sort(function (a, b) {
                    if (a['name'] === b['name']) {
                        return 0;
                    }
                    if (a['name'] > b['name']) {
                        return 1;
                    } else {
                        return -1;
                    }
                });
                colombia = json;
                for (var i = 0; i < colombia.length; i++) {
                    $("#departments .menu").append('<div class="item" data-value="' + colombia[i].code + '">' + colombia[i].name + '</div>');
                }

                var department = $("#departments").dropdown('get value');

                for (var i = 0; i < colombia.length; i++) {
                    if (department == colombia[i].code) {
                        for (var j = 0; j < colombia[i].municipalities.length; j++) {
                            var mun = colombia[i].municipalities[j];
                            $("#municipalities .menu").append('<div class="item" data-value="' + mun.code + '">' + mun.name + '</div>');
                        }
                    }
                }

                $("#municipalities, #departments").dropdown({
                    sortSelect: false,
                    forceSelection: false
                });

                $("#departments").change(function() {
                    var department = $(this).dropdown('get value');

                    $("#municipalities").dropdown('clear');
                    $("#municipalities .menu").empty();

                    for (var i = 0; i < colombia.length; i++) {
                        if (department == colombia[i].code) {
                            for (var j = 0; j < colombia[i].municipalities.length; j++) {
                                var mun = colombia[i].municipalities[j];
                                $("#municipalities .menu").append('<div class="item" data-value="' + mun.code + '">' + mun.name + '</div>');
                            }

                            $("#municipalities input").val(colombia[i].municipalities[0].code);
                        }
                    }

                    $("#municipalities").dropdown({
                        sortSelect: false,
                        forceSelection: false
                    });
                });

            });

            $("input[name='doc_number_affiliate']").change(function () {
                var doc_number = $(this).val();
                var doc_type = $("input[name='doc_type_affiliate']").val();
                $.getJSON('/api/affiliate/' + doc_number + "/" + doc_type, function (json) {
                    existAffiliate = json[0];
                    if (existAffiliate) {
                        $("input[name='first_name_affiliate']").val(existAffiliate.first_name);
                        $("input[name='last_name_affiliate']").val(existAffiliate.last_name);
                    } else {
                        $("input[name='first_name_affiliate']").val('');
                        $("input[name='last_name_affiliate']").val('');
                    }
                });
            });

        });
    </script>
    <script type="text/javascript">
        var makeUIDF = function (length) {
            var text = "";
            var possible = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";

            for (var i = 0; i < length; i++)
                text += possible.charAt(Math.floor(Math.random() * possible.length));

            return text + new Date().getTime();
        };
        var onUploadF = function (err, data) {
            var current = new Date().getTime();
            $(this).parent().find('button').removeClass('blue');
            if (err) {
                $(this).parent().find('button').addClass('red');
                $(this).parent().find('button').html('<i class="warning icon"></i> ERROR');
                return alert('Hubo un error al subir el documento: ', err.message);
            }

            $(this).parent().find('button').addClass('green');
            $(this).parent().find('button').html('<i class="checkmark icon"></i> OK');
            $(this).parent().find('p').html('Completado en ' + Math.round((current - initF) / 1000) + ' seg.');
            $(this).parent().find('input[type=hidden]').val(data.Key);
        };
        var onProgressF = function (progress) {
            var current = new Date().getTime();
            var speed = Math.floor((progress.loaded) / (current - initF));
            var remaing = Math.round((progress.total - progress.loaded) / (speed * 1024));
            var perc = Math.floor(progress.loaded / progress.total * 1000) / 10;
            $(this).parent().find('button').html('<i class="wait icon"></i> ' + perc + ' %');
            if (remaing <= 120) {
                $(this).parent().find('p').html('Faltan: &sim;' + remaing + ' seg.<br /> (vel. &sim;' + speed + ' KB/s)');
            } else {
                $(this).parent().find('p').html('Faltan: &sim;' + Math.round(remaing / 60) + ' min.<br /> (vel. &sim;' + speed + ' KB/s)');
            }
        };
        $(document).ready(function () {

            $('#notificationsfilebtn').click(function () {
                $('#notificationsfile').click();
            });
            $('#notificationsfilebtnz').click(function () {
                $('#notificationsfilez').click();
            });
            $('#notificationsfilebtn2').click(function () {
                $('#notificationsfile2').click();
            });
            $('#notificationsfilebtn4').click(function () {
                $('#notificationsfile4').click();
            });

            setTimeout(function () {
                location.reload();
            }, 600000);

            $('#notificationsfile, #notificationsfile2, #notificationsfile4, #notificationsfilez').change(function () {
                $(this).parent().find('button').removeClass('green');
                $(this).parent().find('button').removeClass('red');
                $(this).parent().find('button').addClass('blue');
                var files = $(this).prop("files");
                if (!files.length) {
                    return alert('Please choose a file to upload first.');
                }
                var file = files[0];
                var fileName = file.name;
                var fileNameExt = file.name.split('.').pop();
                var fileNameHash = makeUIDF(27);
                var albumPhotosKey = encodeURIComponent('auditoria') + '/' + encodeURIComponent('supports') + '/';

                var photoKey = albumPhotosKey + fileNameHash + '.' + fileNameExt;
                var onUploadB = onUploadF.bind(this);
                var onProgressB = onProgressF.bind(this);

                initF = new Date().getTime();
                s3.upload({
                    Key: photoKey,
                    Body: file,
                }, {partSize: 20 * 1024 * 1024, queueSize: 1}, onUploadB).on('httpUploadProgress', onProgressB);
            });

        });
    </script>
@endsection
