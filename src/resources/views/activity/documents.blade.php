<!-- DOCUMENTOS DEL SERVICIO -->
<div class="{{$tab == 2 ? 'active' : ''}} title">
    @if (Auth::user()->isViewer())
        <i class="dropdown icon"></i> Documentos de la actividad
    @else
        <i class="dropdown icon"></i> Documentos de la
        actividad {{$activity->documentsCompleted() ? '(completos)' : '(incompletos)'}}
    @endif
</div>
<div id="documents" class="{{$tab == 2 ? 'active' : ''}} content">

    @php
        $user = Auth::user();
        $isDisabled = ($user->area_id === \App\Area::PROVIDER && optional($user->provider)->clap == 1);
    @endphp

    <form autocomplete="off" method="post" action="{{secure_url('service/' . $activity->id . '/upload')}}"
          class="ui small form">
        @foreach($activity->service->documents as $doc)
            <input type="hidden" name="ids[]" value="{{$doc->id}}">
            @foreach($activity->getDocuments($doc->id) as $document)
                <div class="fields">
                    <input type="hidden" name="id[{{$doc->id}}][]" value="{{$document->id}}">
                    <div class="field wide three">
                        <label>Cod.</label>
                        <p>{{$doc->code}}</p>
                    </div>
                    <div class="six wide field">
                        <label>Documento</label>
                        <p>{{ucfirst(mb_strtolower($doc->name))}}</p>
                    </div>
                    <div class="field">
                        <label>Obligatorio</label>
                        <p>{{$doc->required ? "Sí" : "No"}}</p>
                    </div>
                    <div class="three wide field">
                        <label>Archivo</label>
                        @if ($document->path)
                            <a target="_blank" href="{{secure_url('file/' . $document->path)}}"
                               class="ui primary basic icon button"><i class="download icon"></i></a>
                        @endif
                        @unless (Auth::user()->isViewer())
                            <button onclick="$('input#document_{{$doc->id . $document->id}}').click()" type="button"
                                    class="ui secondary icon button"><i class="upload icon"></i></button>
                            <input style="display: none;" id="document_{{$doc->id . $document->id}}"
                                   name="document_{{$doc->id . $document->id}}" type="file">
                            <input id="document_{{$doc->id . $document->id}}_text" name="document[{{$doc->id}}][]"
                                   type="hidden" value="{{$document->path}}">
                            <p></p>
                        @endunless
                    </div>
                    <div class="four wide field">
                        <label>Fecha de carga</label>
                        @if ($document->path)
                            @if ($document->uploaded_at)
                                <div class="div_date_format" data-fecha="{{$document->uploaded_at }}">
                                    <div class="two wide column">
                                        <span class="date_format_value"></span>
                                    </div>
                                </div>
                            @else
                                <p>Desconocida</p>
                            @endif
                        @endif
                    </div>
                    <div class="five wide field">
                        <label>Resumen</label>
                        @if (Auth::user()->isViewer())
                            {{$document ? $document->summary : ''}}
                        @else
                            <textarea name="summary[{{$doc->id}}][]" cols="30" rows="2" {{ $isDisabled ? 'disabled' : '' }}>{{$document ? $document->summary : ''}}</textarea>
                        @endif
                    </div>
                </div>
            @endforeach
            @unless (Auth::user()->isViewer())
                <div id="fields_{{$doc->id}}"
                     style="display: {{count($activity->getDocuments($doc->id)) > 0 ? 'none' : 'flex'}}" class="fields">
                    <input type="hidden" name="id[{{$doc->id}}][]">
                    <div class="field wide three">
                        <label>Cod.</label>
                        <p>{{$doc->code}}</p>
                    </div>
                    <div class="six wide field">
                        <label>Documento</label>
                        <p>{{ucfirst(mb_strtolower($doc->name))}}</p>
                    </div>
                    <div class="field">
                        <label>Obligatorio</label>
                        <p>{{$doc->required ? "Si" : "No"}}</p>
                    </div>
                    <div class="three wide field">
                        <label>Archivo</label>
                        <button onclick="$('input#document_{{$doc->id}}').click()" type="button"
                                class="ui secondary icon button"><i class="upload icon"></i></button>
                        <input style="display: none;" id="document_{{$doc->id}}" name="document_{{$doc->id}}"
                               type="file">
                        <input id="document_{{$doc->id}}_text" name="document[{{$doc->id}}][]" type="hidden">
                        <p></p>
                    </div>
                    <div class="four wide field">
                        <label>Fecha de cargue</label>
                    </div>
                    <div class="five wide field">
                        <label>Resumen</label>
                        <textarea name="summary[{{$doc->id}}][]" cols="30" rows="2"></textarea>
                    </div>
                </div>
            @endunless
        @endforeach

        <div class="ui divider"></div>

        <!-- OTHERS -->
        <input type="hidden" name="ids[]" value="-1">
        @foreach($activity->getDocuments() as $other)
            <div class="fields">
                <input type="hidden" name="id[-1][]" value="{{$other->id}}">
                <div class="field wide three">
                    <label>Cod.</label>
                    <p>8</p>
                </div>
                <div class="six wide field">
                    <label>Documento</label>
                    <p>Otros</p>
                </div>
                <div class="field">
                    <label>Obligatorio</label>
                    <p>No</p>
                </div>
                <div class="three wide field">
                    <label>Archivo</label>
                    @if ($other->path)
                        <a target="_blank" href="{{secure_url('file/' . $other->path)}}" class="ui primary icon button"><i
                                    class="download icon"></i></a>
                    @endif
                    @unless (Auth::user()->isViewer())
                        <button onclick="$('input#document_-1{{$other->id}}').click()" type="button"
                                class="ui secondary icon button"><i class="upload icon"></i></button>
                        <input style="display: none;" id="document_-1{{$other->id}}" name="document_-1{{$other->id}}"
                               type="file">
                        <input id="document_-1{{$other->id}}_text" name="document[-1][]" type="hidden"
                               value="{{$other->path}}">
                        <p></p>
                    @endunless
                </div>
                <div class="four wide field">
                    <label>Fecha de cargue</label>
                    @if ($other->path)
                        @if ($other->uploaded_at)
                            <div class="div_date_format" data-fecha="{{$other->uploaded_at }}">
                                <div class="two wide column">
                                    <span class="date_format_value"></span>
                                </div>
                            </div>
                        @else
                            <p>Desconocida</p>
                        @endif
                    @endif
                </div>
                <div class="five wide field">
                    <label>Resumen</label>

                    @if ($user->isViewer() )
                        {{$other->summary}}
                    @else
                        <textarea name="summary[-1][]" cols="30" rows="2" {{ $isDisabled ? 'disabled' : '' }}>{{$other->summary}}</textarea>
                    @endif
                </div>

            </div>
        @endforeach
        @unless(Auth::user()->isViewer())
            <div id="fields_-1" style="display: {{count($activity->getDocuments()) > 0 ? 'none' : 'flex'}}"
                 class="fields">
                <input type="hidden" name="id[-1][]">
                <div class="field wide three">
                    <label>Cod.</label>
                    <p>8</p>
                </div>
                <div class="six wide field">
                    <label>Documento</label>
                    <p>Otros</p>
                </div>
                <div class="field">
                    <label>Obligatorio</label>
                    <p>No</p>
                </div>
                <div class="three wide field">
                    <label>Archivo</label>
                    <button onclick="$('input#document_-1').click()" type="button" class="ui secondary icon button"><i
                                class="upload icon"></i></button>
                    <input style="display: none;" id="document_-1" name="document_-1" type="file">
                    <input id="document_-1_text" name="document[-1][]" type="hidden">
                    <p></p>
                </div>
                <div class="four wide field">
                    <label>Fecha de cargue</label>
                </div>
                <div class="five wide field">
                    <label>Resumen</label>
                    <textarea name="summary[-1][]" cols="30" rows="2"></textarea>
                </div>
            </div>
        @endunless

        {{csrf_field()}}
        @unless (Auth::user()->isViewer())
            <div class="fields">
                <div class="six wide field">
                    <button class="ui primary button"><i class="save icon"></i> Guardar</button>
                </div>
                <div class="seven wide field">
                    <div class="ui selection dropdown">
                        <input autocomplete="off" name="copy" type="hidden"/>
                        <div class="default text">
                            Seleccione uno
                        </div>
                        <i class="dropdown icon">
                        </i>
                        <div class="menu">
                            @foreach($activity->documents as $doc)
                                <div class="item" data-value="{{$doc->document_id or -1}}">
                                    {{$doc->document ? $doc->document->code : 8}} - {{$doc->document_name}}
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
                <div class="three wide field">
                    <button onclick="showOtherDoc()" class="ui secondary fluid button" type="button"><i
                                class="add icon"></i> Agregar documento
                    </button>
                </div>
            </div>
        @endunless
    </form>
</div>
<script type="text/javascript">
    var showOtherDoc = function () {
        var id = $('input[name=copy]').val();
        $('#fields_' + id).show().animate({opacity: 0.25}, 600).animate({opacity: 1}, 400);
    }

    $(document).ready(function () {
        var init = 0;

        var makeUID = function (length) {
            var text = "";
            var possible = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";

            for (var i = 0; i < length; i++)
                text += possible.charAt(Math.floor(Math.random() * possible.length));

            return text + new Date().getTime();
        }
        var onUpload = function (err, data) {
            console.error(err);
            var current = new Date().getTime();
            $(this).parent().find('button').removeClass('blue');
            if (err) {
                $(this).parent().find('button').addClass('red');
                $(this).parent().find('button').html('<i class="warning icon"></i> ERROR');
                return alert('There was an error uploading your photo: ', err.message);
            }

            $(this).parent().find('button').addClass('green');
            $(this).parent().find('button').html('<i class="checkmark icon"></i> OK');
            $(this).parent().find('p').html('Completado en ' + Math.round((current - init) / 1000) + ' seg.');
            $(this).parent().find('input[type=hidden]').val(data.Key);
            $('#documents form').submit();
        }
        var onProgress = function (progress) {
            var current = new Date().getTime();
            var speed = Math.floor((progress.loaded) / (current - init));
            var remaing = Math.round((progress.total - progress.loaded) / (speed * 1024));
            var perc = Math.floor(progress.loaded / progress.total * 1000) / 10;
            $(this).parent().find('button').html('<i class="wait icon"></i> ' + perc + ' %');
            if (remaing <= 120) {
                $(this).parent().find('p').html('Faltan: &sim;' + remaing + ' seg.<br /> (vel. &sim;' + speed + ' KB/s)');
            } else {
                $(this).parent().find('p').html('Faltan: &sim;' + Math.round(remaing / 60) + ' min.<br /> (vel. &sim;' + speed + ' KB/s)');
            }
        }

        $('#documents input[type=file]').change(function () {
            $(this).parent().find('button').removeClass('green');
            $(this).parent().find('button').removeClass('red');
            $(this).parent().find('button').addClass('blue');
            var files = $(this).prop("files");
            if (!files.length) {
                return alert('Please choose a file to upload first.');
            }
            var file = files[0];
            var fileName = file.name;
            var fileNameExt = file.name.split('.').pop();
            var fileNameHash = makeUID(27);
            var albumPhotosKey = encodeURIComponent('documents') + '/';

            var photoKey = albumPhotosKey + fileNameHash + '.' + fileNameExt;
            var onUploadB = onUpload.bind(this);
            var onProgressB = onProgress.bind(this);

            init = new Date().getTime();
            s3.upload({
                Key: photoKey,
                Body: file,
            }, {partSize: 20 * 1024 * 1024, queueSize: 1}, onUploadB).on('httpUploadProgress', onProgressB);
        });

        @if ($tab == 2)
        $('html, body').animate({
            scrollTop: $("#documents").offset().top - 125
        }, 500);
        @endif

        $('[name*="include"],[name*="physical"],[name*="others"]').change(function () {
            if ($(this).is(':checked')) {
                $(this).val(1);
            } else {
                $(this).val(-1);
            }
        });
    });
</script>

<script>
    document.addEventListener("DOMContentLoaded", function () {
        // Establecer el idioma a español
        moment.locale('es');

        // Seleccionar todos los elementos con clase "actividad"
        const div_date_format = document.querySelectorAll('.div_date_format');

        div_date_format.forEach(function (date_format) {
            // Obtener la fecha desde el atributo data-fecha
            let date = date_format.getAttribute('data-fecha');

            // Formatear la fecha con Moment.js
            let fechaFormateada = moment(date).format('dddd D [de] MMMM [de] YYYY');

            // Capitalizar la primera letra de la fecha formateada
            fechaFormateada = fechaFormateada.charAt(0).toUpperCase() + fechaFormateada.slice(1);

            // Usar querySelector para seleccionar el primer elemento con clase "fecha-creacion"
            date_format.querySelector('.date_format_value').innerText = `${fechaFormateada}`;
        });
    });
</script>