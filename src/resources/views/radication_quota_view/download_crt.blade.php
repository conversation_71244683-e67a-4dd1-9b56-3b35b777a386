@extends('layouts.main')

@section('title', 'Radicación de Incapacidades')

@section('menu')
    @parent
@endsection

@section('content')
    <div class="ui basic segment">
        <div class="ui container">
            <div class="ui three item stackable tabs menu">
                <a class="item" href="/cotizaciones">Radicación Cotización Seguro</a>
                <a class="item" href="/cotizaciones-descarga-cotizaciones-previas">Descarga Cotizaciones previas</a>
                <a class="item active">Descarga Certificados de Seguro</a>
            </div>
        </div>
        <div class="ui tab segment active" data-tab="definition">
            <div class="ui basic segment">
                <h1 class="ui header">Descarga de Cotizaciones previas</h1>
                <div class="content">
                    <div class="ui">
                        <div class="ui one columns grid">
                            <div class="column">
                                <form autocomplete="off"
                                      action="{{secure_url('/cotizaciones-descarga-certificados-seguro')}}"
                                      method="get" class="ui green segment small form">
                                    <div>
                                        Es necesario introducir el <b>número de radicado</b> de la incapacidad o el
                                        <b>número de identificación</b> del afiliado
                                        para descargar el/los respectivo/s certificado/s.
                                    </div>
                                    <div class="ui fluid accordion">
                                        <!-- FORMATO -->
                                        <div class="content">
                                            <div class="four fields">
                                                <div class="required field">
                                                    <div class="required field">
                                                        <label>Tipo de documento</label>
                                                        <div class="ui fluid selection dropdown">
                                                            <input name="doc_type" type="hidden">
                                                            <i class="dropdown icon"></i>
                                                            <div class="default text">Tipo documento</div>
                                                            <div class="menu">
                                                                <div class="item" data-value="1">Id servicio</div>
                                                                <div class="item" data-value="2">Documento afiliado
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="field d-none" id="id_service">
                                                    <label>Id servicio</label>
                                                    <input name="id_service">
                                                </div>
                                                <div class="field d-none" id="doc_number_affiliate">
                                                    <label>Documento afiliado</label>
                                                    <input name="doc_number_affiliate">
                                                </div>
                                            </div>
                                        </div>
                                        <!-- END: FORMATO -->
                                    </div>

                                    <div class="field">
                                        <button class="ui primary button" id="submit-btn">
                                            <i class="cloud download icon"></i>
                                            Generar
                                        </button>
                                    </div>
                                    {{csrf_field()}}
                                </form>
                                <div class="d-none" id="cover-spin"></div>
                            </div>
                        </div>
                        <div class="ui columns grid d-none" id="show-links">
                            <div class="column">
                                <div class="ui fluid accordion">
                                    <div class="content">
                                        <div id="inject-data"
                                             style="display: block; margin-left: auto; margin-right: auto; width: 40%"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>
    <style type="text/css">
        .mb-1 {
            margin-bottom: 10px !important;
        }

        .d-none {
            display: none;
        }

        .ui.grid .column {
            padding: 0.5rem 1rem !important;
        }

        .ui.accordion .title {
            text-transform: uppercase;
        }

        .field > h3 {
            text-align: center;
            margin-top: 1.25rem;
        }

        .ui.search > .results {
            width: 30rem;
        }

        .ui.search > .results .result .title {
            padding: 0 !important;
            border: none !important;
            text-transform: none;
        }

        .ui.search > .results .result .content {
            padding: 0 !important;
        }

        .youtube-container {
            display: block;
            margin: 20px auto;
            width: 100%;
            max-width: 600px;
        }

        .youtube-player {
            display: block;
            width: 100%; /* assuming that the video has a 16:9 ratio */
            padding-bottom: 56.25%;
            overflow: hidden;
            position: relative;
            width: 100%;
            height: 100%;
            cursor: hand;
            cursor: pointer;
            display: block;
        }

        img.youtube-thumb {
            bottom: 0;
            display: block;
            left: 0;
            margin: auto;
            max-width: 100%;
            width: 100%;
            position: absolute;
            right: 0;
            top: 0;
            height: auto
        }

        div.play-button {
            height: 72px;
            width: 72px;
            left: 50%;
            top: 50%;
            margin-left: -36px;
            margin-top: -36px;
            position: absolute;
            background: url("http://i.imgur.com/TxzC70f.png") no-repeat;
        }

        #youtube-iframe {
            width: 100%;
            height: 100%;
            position: absolute;
            top: 0;
            left: 0;
        }

        #cover-spin {
            position: fixed;
            width: 100%;
            left: 0;
            right: 0;
            top: 0;
            bottom: 0;
            background-color: rgba(255, 255, 255, 0.7);
            z-index: 9999;
            display: none;
        }

        @-webkit-keyframes spin {
            from {
                -webkit-transform: rotate(0deg);
            }
            to {
                -webkit-transform: rotate(360deg);
            }
        }

        @keyframes spin {
            from {
                transform: rotate(0deg);
            }
            to {
                transform: rotate(360deg);
            }
        }

        #cover-spin::after {
            content: '';
            display: block;
            position: absolute;
            left: 48%;
            top: 40%;
            width: 40px;
            height: 40px;
            border-style: solid;
            border-color: green;
            border-top-color: transparent;
            border-width: 4px;
            border-radius: 50%;
            -webkit-animation: spin .8s linear infinite;
            animation: spin .8s linear infinite;
        }
    </style>
    <script type="text/javascript">
        $(document).ready(function () {
            $('.ui.dropdown').dropdown();
            $('input[name="doc_type"]').change(() => {
                const docType = $('input[name="doc_type"]').val();
                if (docType === '1') {
                    $('#doc_number_affiliate').hide();
                    $("input[name='doc_number_affiliate']").val('');
                    $('#id_service').attr('style', 'display: block !important');
                } else if (docType === '2') {
                    $('#id_service').hide();
                    $("input[name='id_service']").val('');
                    $('#doc_number_affiliate').attr('style', 'display: block !important');
                }
            });
            $('form .datepicker').pickadate({
                selectYears: 100,
                selectMonths: true,
                max: new Date(),
                formatSubmit: 'yyyy-mm-dd',
                format: 'yyyy-mm-dd'
            });
            $('#submit-btn').click((evt) => {
                evt.preventDefault();
                $('#show-links').hide();
                const docNumber = $("input[name='doc_number_affiliate']").val();
                const idService = $("input[name='id_service']").val();
                if (docNumber || idService) {
                    const isDocNumber = !!docNumber;
                    const isService = !!idService;
                    const params = isDocNumber ? {
                        number: docNumber,
                        is_DocNumber: true
                    } : isService ? {number: parseInt(idService), is_DocNumber: false} : '';
                    console.log(params);
                    $('#cover-spin').attr('style', 'display: block !important');
                    $.getJSON('api/download-certificate-action/' + params.number + '/' + params.is_DocNumber, function (json) {
                        $('#cover-spin').hide();
                        if (json !== 'None') {
                            if (Array.isArray(json.message)) {
                                let paths = '';
                                for (const item of json.message) {
                                    paths += `<div class="ui labeled button mb-1" tabindex="0"><div class="ui basic green button">` +
                                        `N. documento: ${item.doc_document} id servicio: ${item.id_activity} Fecha acción ${item.date_action}` +
                                        `</div><a class="ui basic left pointing green label" href="${item.path}" target="_blank">` +
                                        `<i class="file pdf outline icon"></i></a></div>`;
                                }
                                $('#show-links').attr('style', 'display: block !important');
                                $('#inject-data').html(paths);
                            }
                        } else {
                            Swal.fire({
                                title: 'No existe!',
                                text: 'No se ha generado certificados para estos casos',
                                icon: 'warning',
                                showConfirmButton: false,
                                showCancelButton: true,
                                cancelButtonText: 'Cerrar',
                                cancelButtonColor: '#d33'
                            });
                        }
                    });
                }
            });

            $('#link-anchor').click(() => {
                $('#link-anchor').css({display: 'none'});
            });

        });
    </script>
@endsection