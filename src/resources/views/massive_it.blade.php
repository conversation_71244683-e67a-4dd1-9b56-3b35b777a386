@extends('layouts.main')

@section('title', 'CARGA MASIVA IT')

@section('menu')
    @parent
@endsection

@section('content')
    <div class="ui basic segment">
        <h1 class="ui header" style="padding-left: 1rem">Carga Masiva IT</h1>
        <div class="ui info message" style="width: 97%;height: auto;margin-left: 2rem">
            <div class="header">Excel</div>
            <div id="dashboard">
                ■ Los nombres de las columnas no deben tener espaciados al inicio, fin o intermedio.    ■ Debe usarse una plantilla nueva en cada cargue para evitar registros "Fantasma" en el cargue
            </div>
        </div>
        <div class="ui warning message" style="width: 97%;height: auto;margin-left: 2rem; margin-bottom: 2rem">
            <div class="header">Advertencia</div>
            <div id="dashboard">
                ■ En caso de que exista información cargada, esta se sobrescribirá con la información a cargar.
            </div>
        </div>
        <div class="ui">
            <div class="ui two columns grid">
                <div class="column">
                    <form autocomplete="off" action="{{secure_url('/cargues_it/it_cases')}}" method="post" enctype="multipart/form-data" class="ui yellow segment small form">
                        <div class="ui dividing header">Cargar ESTRUCTURA CREACIÓN AFILIADO Y SERVICIO (Paso 1)</div>
                        <div class="required field">
                            <label>Archivo Excel [Columnas: TIPO_IDENTIFICACION - NUMERO_DE_DOCUMENTO - NOMBRES - APELLIDOS - FECHA_DE_NACIMIENTO - SEXO - REGIONAL - TELEFONO - CELULAR - DIRECCION - DEPARTAMENTO - MUNICIPIO - ESTADO_CIVIL - NIVEL_ESCOLAR - EPS - FECHA_DE_RADICACION - NO_RADICADO_BIZAGI - FECHA_ASIGNACION_PROVEEDOR - PROVEEDOR_ASIGNADO_VALIDACION_DOCUMENTAL - DIA_1 - DIA_180 - DIA_540 - EPS_EMISORA - TIENE_CRE - PRONOSTICO_CRE - FECHA_DE_RECEPCION_CRE_FAVORABLE - FECHA_DE_EMISION_CRE_DESFAVORABLE - SOPORTE_CRE - FECHA_EXPEDICION_SOPORTE_CREE - TUTELA - NO_RADICADO_TUTELA - TIPO_DE_DESPACHO - SECCION - NUMERO_DE_JUZGADO - JURISDICCION - DEPARTAMENTO_TUTELA - CIUDAD_TUTELA - RADICADO_BIZAGI_DE_LA_SENTENCIA - FECHA_DEL_FALLO - ORDEN_JUDICIAL - NOMBRE_JUZGADO - ESTADO_HOMOLOGADO - RADICADO_BIZAGI]</label>
                            <input type="file" name="it_file" accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel">
                        </div>
                        <div class="field">
                            <button class="ui blue button"><i class="upload icon"></i> Cargar</button>
{{--                            <a href="https://renapp-colpensiones.s3.us-east-2.amazonaws.com/Templates/PLANTILLA_ESTRUCTURA_CREACION_AF_SER.xlsx">PLANTILLA_ESTRUCTURA_CREACION_AF_SER.xlsx</a>--}}
                        </div>
                        {{csrf_field()}}
                    </form>
                </div>
                <div class="column">
                    <form autocomplete="off" action="{{secure_url('/cargues_it/it_cases_inability')}}" method="post" enctype="multipart/form-data" class="ui yellow segment small form">
                        <div class="ui dividing header">Cargar ESTRUCTURA CREACIÓN INCAPACIDADES (Paso 2)</div>
                        <div class="required field">
                            <label>Archivo Excel [Columnas: RADICADO_BIZAGI - ID_INCAPACIDAD - FECHA_INICIO - FECHA_FIN - DIAS_IT - CODIGO_DX - NOMBRE_DX - CAUSAL_RECHAZO - NOMBRE_TIPO_RECHAZO - FRACCIONADO - IBC]</label>
                            <input type="file" name="it_file" accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel">
                        </div>
                        <div class="field">
                            <button class="ui blue button"><i class="upload icon"></i> Cargar</button>
{{--                            <a href="https://renapp-colpensiones.s3.us-east-2.amazonaws.com/Templates/PLANTILLA_ESTRUCTURA_CREACION_AF_SER.xlsx">PLANTILLA_ESTRUCTURA_CREACION_AF_SER.xlsx</a>--}}
                        </div>
                        {{csrf_field()}}
                    </form>
                </div>
            </div>
            <div class="ui two columns grid">
                <div class="column">
                    <form autocomplete="off" action="{{secure_url('/cargues_it/it_cases_action_history')}}" method="post" enctype="multipart/form-data" class="ui yellow segment small form">
                        <div class="ui dividing header">Cargar ESTRUCTURA CARGUE ACCIONES (Paso 3)</div>
                        <div class="required field">
                            <label>Archivo Excel [Columnas: RADICADO_BIZAGI - FECHA_ACCION AUDIBOL - DESCRIPCION]</label>
                            <input type="file" name="it_file" accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel">
                        </div>
                        <div class="field">
                            <button class="ui blue button"><i class="upload icon"></i> Cargar</button>
{{--                            <a href="https://renapp-colpensiones.s3.us-east-2.amazonaws.com/Templates/PLANTILLA_ESTRUCTURA_CREACION_AF_SER.xlsx">PLANTILLA_ESTRUCTURA_CREACION_AF_SER.xlsx</a>--}}
                        </div>
                        {{csrf_field()}}
                    </form>
                </div>
                <div class="column">
                    <form autocomplete="off" action="{{secure_url('/cargues_it/it_cases_documents')}}" method="post" enctype="multipart/form-data" class="ui yellow segment small form">
                        <div class="ui dividing header">Cargar ESTRUCTURA DOCUMENTS (Paso 4)</div>
                        <div class="required field">
                            <label>Archivo Excel [Columnas: RADICADO_BIZAGI - CODIGO_DOCUMENTAL - URL_DOCUMENTO - FECHA_CARGUE - ID_COD_AB]</label>
                            <input type="file" name="it_file" accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel">
                        </div>
                        <div class="field">
                            <button class="ui blue button"><i class="upload icon"></i> Cargar</button>
{{--                            <a href="https://renapp-colpensiones.s3.us-east-2.amazonaws.com/Templates/PLANTILLA_ESTRUCTURA_CREACION_AF_SER.xlsx">PLANTILLA_ESTRUCTURA_CREACION_AF_SER.xlsx</a>--}}
                        </div>
                        {{csrf_field()}}
                    </form>
                </div>
            </div>
            <div class="ui two columns grid">
                <div class="column">
                    <form autocomplete="off" action="{{secure_url('/cargues_it/it_cases_update_date')}}" method="post" enctype="multipart/form-data" class="ui yellow segment small form">
                        <div class="ui dividing header">actualizar FECHA INICIO Y FECHA FIN (Opcional)</div>
                        <div class="required field">
                            <label>Archivo Excel [Columnas: RADICADO_BIZAGI - ID_INCAPACIDAD - FECHA_INICIO - FECHA_FIN]</label>
                            <input type="file" name="it_file" accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel">
                        </div>
                        <div class="field">
                            <button class="ui blue button"><i class="upload icon"></i> Cargar</button>
{{--                            <a href="https://renapp-colpensiones.s3.us-east-2.amazonaws.com/Templates/PLANTILLA_ESTRUCTURA_CREACION_AF_SER.xlsx">PLANTILLA_ESTRUCTURA_CREACION_AF_SER.xlsx</a>--}}
                        </div>
                        {{csrf_field()}}
                    </form>
                </div>
                <div class="column">
                    <form autocomplete="off" action="{{secure_url('/cargues_it/it_cases_follows')}}" method="post" enctype="multipart/form-data" class="ui yellow segment small form">
                        <div class="ui dividing header">Reportar Seguimiento "GESTIONADO" (Opcional)</div>
                        <div class="required field">
                            <label>Archivo Excel [Columnas: ID_SERVICIO - DESCRIPCION]</label>
                            <input type="file" name="it_file" accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel">
                        </div>
                        <div class="field">
                            <button class="ui blue button"><i class="upload icon"></i> Cargar</button>
{{--                            <a href="https://renapp-colpensiones.s3.us-east-2.amazonaws.com/Templates/PLANTILLA_ESTRUCTURA_CREACION_AF_SER.xlsx">PLANTILLA_ESTRUCTURA_CREACION_AF_SER.xlsx</a>--}}
                        </div>
                        {{csrf_field()}}
                    </form>
                </div>
            </div>
            <div class="ui two columns grid">
                <div class="column">
                    <form autocomplete="off" action="{{secure_url('/cargues_it/it_cases_update_state')}}" method="post" enctype="multipart/form-data" class="ui yellow segment small form">
                        <div class="ui dividing header">actualizar ESTADO CASOS IT (Opcional)</div>
                        <div class="required field">
                            <label>Archivo Excel [Columnas: ID_SERVICIO - ESTADO_FIN - OBSERVACION]</label>
                            <div style="padding-top: 1rem;padding-bottom: 1rem;">
                                EXCEL/* <span style="color: gray;">[<b>POR FAVOR COPIAR Y PEGAR SIN FORMATO LOS DATOS DEL EXCEL ANTES DE SUBIRLO PARA EVITAR INCONVENIENTES <br>
                                    RESPETAR LOS NOMBRES DE LAS COLUMNAS SIN ESPACIOS NI ACENTOS. <br>
                                    LA COLUMNA ESTADO_FIN DEBE TENER EL ID NUMERICO REFERENTE AL ESTADO QUE SE QUIERE ASIGNAR A LOS SERVICIOS</b>]</span>
                            </div>
                            <input type="file" name="it_file" accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel">
                        </div>
                        <div class="field">
                            <button class="ui blue button"><i class="upload icon"></i> Cargar</button>
{{--                            <a href="https://renapp-colpensiones.s3.us-east-2.amazonaws.com/Templates/PLANTILLA_ESTRUCTURA_CREACION_AF_SER.xlsx">PLANTILLA_ESTRUCTURA_CREACION_AF_SER.xlsx</a>--}}
                        </div>
                        {{csrf_field()}}
                    </form>
                </div>
            </div>
        </div>

    </div>
    <script type="text/javascript">
        var makeUIDF = function(length) {
            var text = "";
            var possible = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";

            for (var i = 0; i < length; i++)
                text += possible.charAt(Math.floor(Math.random() * possible.length));

            return text + new Date().getTime();
        };
        var onUploadF = function(err, data) {
            var current = new Date().getTime();
            $(this).parent().find('button').removeClass('blue');
            if (err) {
                $(this).parent().find('button').addClass('red');
                $(this).parent().find('button').html('<i class="warning icon"></i> ERROR');
                return alert('There was an error uploading your photo: ', err.message);
            }

            $(this).parent().find('button').addClass('green');
            $(this).parent().find('button').html('<i class="checkmark icon"></i> OK');
            $(this).parent().find('p').html('Completado en ' + Math.round((current - initF) / 1000) + ' seg.');
            $(this).parent().find('input[type=hidden]').val(data.Key);
        };
        var onProgressF = function(progress) {
            var current = new Date().getTime();
            var speed = Math.floor((progress.loaded) / (current - initF));
            var remaing = Math.round((progress.total - progress.loaded) / (speed * 1024));
            var perc = Math.floor(progress.loaded / progress.total * 1000) / 10;
            $(this).parent().find('button').html('<i class="wait icon"></i> ' + perc + ' %');
            if (remaing <= 120){
                $(this).parent().find('p').html('Faltan: &sim;' + remaing + ' seg.<br /> (vel. &sim;'+ speed +' KB/s)');
            } else {
                $(this).parent().find('p').html('Faltan: &sim;' + Math.round(remaing / 60) + ' min.<br /> (vel. &sim;'+ speed +' KB/s)');
            }
        };
        $(document).ready(function() {

            $('#notificationsfilebtn').click(function () {
                $('#notificationsfile').click();
            });

            $('#notificationsfilebtn2').click(function () {
                $('#notificationsfile2').click();
            });
            $('#notificationsfilebtn4').click(function () {
                $('#notificationsfile4').click();
            });

            $('#notificationsfilebtnx').click(function () {
                $('#notificationsfilex').click();
            });

            $('#notificationsfilebtnxresponses').click(function () {
                $('#notificationsfilexresponses').click();
            });

            $('#notificationsfilebtnz').click(function () {
                $('#notificationsfilez').click();
            });

            $('#notificationsfile, #notificationsfile2, #notificationsfile4, #notificationsfilex, #notificationsfilexresponses, #notificationsfilez').change(function() {
                $(this).parent().find('button').removeClass('green');
                $(this).parent().find('button').removeClass('red');
                $(this).parent().find('button').addClass('blue');
                var files = $(this).prop("files");
                if (!files.length) {
                    return alert('Please choose a file to upload first.');
                }
                var file = files[0];
                var fileName = file.name;
                var fileNameExt = file.name.split('.').pop();
                var fileNameHash = makeUIDF(27);
                var albumPhotosKey = encodeURIComponent('notifications_zip') + '/';

                var photoKey = albumPhotosKey + fileNameHash + '.' + fileNameExt;
                var onUploadB = onUploadF.bind(this);
                var onProgressB = onProgressF.bind(this);

                initF = new Date().getTime();
                s3.upload({
                    Key: photoKey,
                    Body: file,
                }, {partSize: 20 * 1024 * 1024, queueSize: 1}, onUploadB).on('httpUploadProgress', onProgressB);
            });

        });
    </script>
@endsection