@extends('layouts.main')

@section('menu')
    @parent
@endsection

@section('content')
    <style>
        .photo {
            width: 6rem;
            height: 6rem;
            border-radius: 10px;
            border: 1px solid #ccc;
            object-fit: cover;
        }
    </style>
    <div class="ui basic segment">
        <div class="ui two columns grid">
            <div class="column">
                @if (Auth::id() == $user->id)
                    <h1 class="ui header">
                        <a href="#" onclick="$('#formPic').show();">
                            <img class="photo"
                                 src="{{$user->photo ? secure_url('file/' . $user->photo) : 'images/blank.png'}}"
                                 alt=""/>
                        </a>
                        <div class="content">
                            <div>Mi perfil</div>
                            <div class="sub header">Para actualizar tu foto de perfil da clic sobre ella</div>
                        </div>
                    </h1>
                    <form action="{{secure_url('profile/chpic')}}" class="ui small segment form border-top-secondary"
                          enctype="multipart/form-data" id="formPic" method="post" style="display: none;">
                        <div class="ui dividing header">
                            Actualizar foto de perfil
                        </div>
                        <div class="required field">
                            <input accept="image/*" name="photo" type="file"/>
                        </div>
                        {{csrf_field()}}
                        <button class="ui secondary button">
                            <i class="send icon">
                            </i>
                            Enviar
                        </button>
                    </form>
                @else
                    <h1 class="ui header">
                        <img class="photo"
                             src="{{$user->photo ? secure_url('file/' . $user->photo) : 'images/blank.png'}}" alt=""/>
                        Perfil de {{$user->username}}
                    </h1>
                @endif
                <div class="ui three columns grid">
                    <div class="row">
                        <div class="column">
                            <b>
                                Nombre:
                            </b>
                            {{$user->full_name}}
                        </div>
                        <div class="column">
                            <b>
                                Correo electrónico:
                            </b>
                            @if($user->area_id == 46 || $user->area_id == 61)
                                {{$user->affiliate->email ?? 'Correo no disponible'}}
                            @else
                                {{$user->email}}
                            @endif
                        </div>
                        <div class="column">
                            <b>
                                Usuario:
                            </b>
                            {{$user->username}}
                        </div>
                    </div>
                    <div class="row">
                        <div class="column">
                            <b>
                                Rol:
                            </b>
                            {{$user->area->name}}
                        </div>
                        <div class="column">
                            <b>
                                Estado:
                            </b>
                            {{$user->active ? 'Activo' : 'Inactivo'}}
                        </div>
                    </div>
                </div>
            </div>
            <div class="column">

                @if (Auth::id() == $user->id)
                    @if(date('Y-m-d H:i:s') > Auth::user()->next_update )
                        <div class="ui info message bg-white">
                            <i class="close icon"></i>
                            <div class="header">
                                <em>Actualizá tu contraseña para poder continuar.</em>
                            </div>
                        </div>
                    @endif
                    @if(Session::has('flash_message_wrong_password'))
                        <div class="ui error message">
                            <i class="close icon"></i>
                            <div class="header">
                                <em> {!! session('flash_message_wrong_password') !!}</em>
                            </div>
                        </div>
                    @endif
                    @if(Session::has('flash_message_ok_password'))
                        <div class="ui info message bg-white">
                            <i class="close icon"></i>
                            <div class="header">
                                <em> {!! session('flash_message_ok_password') !!}</em>
                            </div>
                        </div>
                    @endif
                    <form action="{{secure_url('profile/chpw')}}" class="ui small segment form border-top-secondary" id="formPw"
                          method="post">
                        <div class="ui dividing header">
                            Cambiar contraseña
                        </div>
                        <div class="three fields">
                            <div class="required field">
                                <label>
                                    Contraseña actual
                                </label>
                                <input name="password" type="password" autocomplete="off"/>
                            </div>
                            <div class="required field">
                                <label>
                                    Nueva contraseña
                                </label>
                                <input name="new_password" type="password" autocomplete="off"/>
                            </div>
                            <div class="required field">
                                <label>
                                    Repetir contraseña
                                </label>
                                <input name="re_password" type="password" autocomplete="off"/>
                            </div>
                        </div>
                        {{csrf_field()}}
                        <button class="ui secondary button">
                            <i class="send icon">
                            </i>
                            Cambiar
                        </button>
                    </form>
                    @if ($user->email == '<EMAIL>' || $user->email == '<EMAIL>')
                        <form class="ui black segment small form" id="bill">
                            <div class="ui dividing header">Generar factura</div>
                            <div class="three fields">
                                <div class="required field">
                                    <label>Fecha inicial</label>
                                    <input name="initial_date" class="datepicker" type="text">
                                </div>
                                <div class="required field">
                                    <label>Fecha final</label>
                                    <input name="final_date" class="datepicker" type="text">
                                </div>
                                <div class="required field">
                                    <label>Numero Factura</label>
                                    <input name="invoice_num" type="number">
                                </div>
                            </div>
                            <button class="ui black button">
                                <i class="file icon">
                                </i>
                                Generar
                            </button>
                        </form>
                    @endif
                @endif
            </div>
        </div>
    </div>
    <script type="text/javascript">
        $(document).ready(function () {
            $('b').css('display', 'block');
            @if (Auth::id() == $user->id)
            $('#formPic').form({
                fields: {
                    photo: 'empty'
                }
            });
            $('#formPw').form({
                fields: {
                    password: 'empty',
                    new_password: 'empty',
                    re_password: ['empty', 'match[new_password]'],
                }
            });
            @if ($user->email == '<EMAIL>')
            $('.datepicker').pickadate({
                selectYears: true,
                selectMonths: true,
                formatSubmit: 'yyyy-mm-dd'
            });
            $('#bill').form({
                fields: {
                    initial_date: 'empty',
                    final_date: 'empty',
                    invoice_num: 'empty',
                },
                onSuccess: function (e, fields) {
                    var url = '{{secure_url('/admin/bill')}}';
                    url += '/' + fields.initial_date_submit;
                    url += '/' + fields.final_date_submit;
                    url += '/' + fields.invoice_num;

                    window.open(url);

                    return false;
                }
            });
            @endif
            @endif

            //cerrar mensajes de session
            $('.message .close').on('click', function() {
                $(this).closest('.message').transition('fade');
            });
        });
    </script>
@endsection
