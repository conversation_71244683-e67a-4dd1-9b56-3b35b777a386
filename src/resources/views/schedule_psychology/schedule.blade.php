@extends('layouts.main')

@section('title', 'HORARIO DE PSICOLOGOS')

@section('menu')
    @parent
@endsection

@section('content')
    @isset($message)
        <div class="ui message success">
            <div class="header">
                {{$message}}
            </div>
        </div>
    @endisset
    @isset($error)
        <div class="ui message">
            <div class="header">
                {{$error}}
            </div>
        </div>
    @endisset

    <div class="ui fluid accordion">
        <!-- FORMATO -->
        <div class="content" style="z-index: 2;position: relative;">
            <div class="ui basic segment">

                <div class="ui placeholder segment">
                    <div class="ui four column stackable center aligned grid">
                        <div class="middle aligned row">
                            {{csrf_field()}}
                            <div class="column">
                                <div class="ui icon header">
                                    <i class="assistive listening systems icon"></i>
                                </div>
                                <div>
                                    <form class="ui form" action="{{secure_url('/schedule/edit')}}" method="post">
                                        {{csrf_field()}}
                                        <div class="ui selection dropdown field">
                                            <input type="hidden" name="user_id" required>
                                            <i class="dropdown icon"></i>
                                            <div class="default text">Seleccionar profesional de la salud</div>
                                            <div class="menu">
                                                @foreach($orientation as $s)
                                                    <div class="item"
                                                         data-value="{{$s->id}}">{{$s->full_name}}</div>
                                                @endforeach
                                            </div>
                                        </div>
                                        <div>
                                            <button id="buttonloadOrientation" type="submit" class="ui button"
                                                    onclick="loadOrientation()">Editar Horario Orientación
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                            <div class="column">
                                <div class="ui icon header">
                                    <i class="balance scale icon"></i>
                                </div>
                                <div>
                                    <form class="ui form" action="{{secure_url('/schedule/edit')}}" method="post">
                                        {{csrf_field()}}
                                        <div class="ui selection dropdown field">
                                            <input type="hidden" name="user_id" required>
                                            <i class="dropdown icon"></i>
                                            <div class="default text">Seleccionar profesional de la salud</div>
                                            <div class="menu">
                                                @foreach($rama as $s)
                                                    <div class="item"
                                                         data-value="{{$s->id}}">{{$s->full_name}}</div>
                                                @endforeach
                                            </div>
                                        </div>
                                        <div>
                                            <button id="buttonloadRama" type="submit" class="ui button"
                                                    onclick="loadRama()">Editar Horario Judicial-Rama
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                            <div class="column">
                                <div class="ui icon header">
                                    <i class="balance scale icon"></i>
                                </div>
                                <div>
                                    <form class="ui form" action="{{secure_url('/schedule/edit')}}" method="post">
                                        {{csrf_field()}}
                                        <div class="ui selection dropdown field">
                                            <input type="hidden" name="user_id" required>
                                            <i class="dropdown icon"></i>
                                            <div class="default text">Seleccionar profesional de la salud</div>
                                            <div class="menu">
                                                @foreach($fiscalia as $s)
                                                    <div class="item"
                                                         data-value="{{$s->id}}">{{$s->full_name}}</div>
                                                @endforeach
                                            </div>
                                        </div>
                                        <div>
                                            <button id="buttonloadFiscalia" type="submit" class="ui button"
                                                    onclick="loadFiscalia()">Editar Horario Judicial-Fiscalía
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                            <div class="column">
                                <div class="ui icon header">
                                    <i class="heartbeat icon"></i>
                                </div>
                                <div>
                                    <form class="ui form" action="{{secure_url('/schedule/edit')}}" method="post">
                                        {{csrf_field()}}
                                        <div class="ui selection dropdown field">
                                            <input type="hidden" name="user_id" required>
                                            <i class="dropdown icon"></i>
                                            <div class="default text">Seleccionar profesional de la salud</div>
                                            <div class="menu">
                                                @foreach($COVID19 as $s)
                                                    <div class="item"
                                                         data-value="{{$s->id}}">{{$s->full_name}}</div>
                                                @endforeach
                                            </div>
                                        </div>
                                        <div>
                                            <button id="buttonloadCovid" type="submit" class="ui button"
                                                    onclick="loadCovid()">Editar Horario COVID-19
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script>
        $('.ui.selection.dropdown').dropdown();

        $('.ui.form')
            .form({
                fields: {
                    user_id: 'empty'
                }
            });

        function loadOrientation() {
            document.getElementById('buttonloadOrientation').classList.add("loading");
        }
        function loadRama() {
            document.getElementById('buttonloadRama').classList.add("loading");
        }
        function loadFiscalia() {
            document.getElementById('buttonloadFsicalia').classList.add("loading");
        }
        function loadCovid() {
            document.getElementById('buttonloadCovid').classList.add("loading");
        }
    </script>
@endsection
