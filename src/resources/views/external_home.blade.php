@extends('layouts.external_main')

@section('content')
@if(!isset($affiliate))
<div class="ui basic segment">
	<div class="ui grid">
		<div class="column">
			@if(!$use_form)
				<div class="ui two top attached steps">
					<div id="affiliate" class="active link step">
						<i class="user icon"></i>
						<div class="content">
							<div class="title">Ingreso Afiliado</div>
						</div>
					</div>
				</div>
				@if(Session::has('flash_message_not_exists'))
					<div class="ui error message">
						<i class="close icon"></i>
						<div class="header">
							<em> {!! session('flash_message_not_exists') !!}</em>
						</div>
					</div>
				@endif
				<form class="ui attached segment form" action="{{secure_url('/external_affiliate_index')}}" method="post">
					{{csrf_field()}}
					<div class="two fields">
						<div class="required field">
							<label for="docType">Tipo de documento</label>
							<div class="ui selection dropdown">
								<input type="hidden" name="doc_type" value="{{ $doc_type or "CC" }}">
								<i class="dropdown icon"></i>
								<div class="default text">Tipo de documento</div>
								<div class="menu">
									@foreach($DOC_TYPES as $k => $v)
									<div class="item" data-value="{{$k}}">{{$v}}</div>
									@endforeach
								</div>
							</div>
						</div>
						<div class="required field">
							<label for="docNumber">Número de documento</label>
							<div id="searchInput" class="ui left icon action fluid input">
								<i class="user icon"></i>
							  <input list="docList" name="doc_number" id="docNumber" autocomplete="off" type="text" value="">
							</div>
						</div>
					</div>
					<div class="two fields">
						<div class="required field">
							<label for="birthday">Fecha de Nacimiento</label>
							<input type="text" name="birthday" class="datepicker" data-value="">
						</div>
						<div class="field">
							<label for="docNumber">Buscar</label>
							<div id="searchInput" class="ui left icon action fluid input">
								<i class="user icon"></i>
								<button class="ui green right labeled icon button">
									<i class="search icon"></i>
									Buscar
								</button>
							</div>
						</div>
					</div>
					<input type="hidden" name="type_user" value="{{$type_user}}">
					<script type="text/javascript">
						$('.ui.dropdown').dropdown();
					</script>
				</form>
			@endif
			@include('affiliate.external_affiliate_form', array('doc_type' => 'CC', 'doc_number' => '', 'type_user'=> $type_user, 'use_form' => $use_form))
		</div>
	</div>
</div>
<datalist id="docList"></datalist>
<script type="text/javascript">
    $("input[name='doc_type']").change(function(){
        $("input[name='affiliate[doc_type]']").val($(this).val());
    });
    $('#docNumber').change(function(){
        $("input[name='affiliate[doc_number]']").val($(this).val());
    });
</script>
@else
	<div class="ui basic segment">
		@if(Session::has('flash_message_save_service'))
			<div class="ui success message">
				<i class="close icon"></i>
				<div class="header">
					<em> {!! session('flash_message_save_service') !!}</em>
				</div>
			</div>
		@endif
		<div class="ui grid">
			<div class="column">
				@include('affiliate.external_affiliate_profile', array('affiliate' => $affiliate))
			</div>
		</div>
	</div>
@endif
@endsection