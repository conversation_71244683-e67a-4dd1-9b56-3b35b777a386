@extends('layouts.main')

@section('title', 'CARGA MASIVA REI')

@section('menu')
    @parent
@endsection

@section('content')
    <div class="ui basic segment">

        {{--        @if(Auth::user()->showToAuthorizedUsersInMainCargueRei())--}}
        <h1 class="ui header">Carga Masiva REI</h1>

        <div class="ui">
            <div class="ui two columns grid">
                <div class="column">
                    <form autocomplete="off" action="{{secure_url('/cargues_rei/phase_0')}}" method="post"
                          enctype="multipart/form-data" class="ui yellow segment small form">
                        <div class="ui dividing header">Cargue - Fase 0 - Cargue base nomina</div>
                        <div class="required field">
                            <label>Archivo Excel [Columnas:TIPO_DOC - NRO_IDENTIFICACION - NOMBRES - APELLIDO - COD_DEPARTAMENTO -
                                COD_MUNICIPIO - FECHA_NAC - GENERO - TIPO_PENSIONADO
                                ]</label>
                            <div style="padding-top: 1rem;padding-bottom: 1rem;">
                                EXCEL/* <span style="color: gray;">[<b>Los nombres de las columnas no deben tener espaciados al inicio, fin o intermedio <br>
                                    En caso que un codigo de departamento o municipio esté antecedido por ceros se deben quitar para evitar error en el cargue y posterior visualización de la información. <br>
                                    Debe usarse una plantilla nueva en cada cargue para evitar registros "Fantasma" en el cargue</b>]</span>
                            </div>
                            <input type="file" name="service_file"
                                   accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel">
                        </div>
                        <div class="field">
                            <button class="ui green button"><i class="upload icon"></i> Cargar</button>
                            <a href="https://renapp-colpensiones.s3.us-east-2.amazonaws.com/Templates/FASE_0_BASE_NOMINA_REI.xlsx">Plantilla
                                FASE 0 BASE NOMINA REI.xlsx</a>
                        </div>
                        {{csrf_field()}}
                    </form>
                </div>
            </div>
        </div>
        {{--        @endif--}}
    </div>
    <script type="text/javascript">
        var showOtherDoc = function () {
            var id = $('input[name=copy]').val();
            $('#fields_' + id).show().animate({opacity: 0.25}, 600).animate({opacity: 1}, 400);
        }
        var makeUIDF = function (length) {
            var text = "";
            var possible = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";

            for (var i = 0; i < length; i++)
                text += possible.charAt(Math.floor(Math.random() * possible.length));

            return text + new Date().getTime();
        };
        var onUploadF = function (err, data) {
            var current = new Date().getTime();
            $(this).parent().find('button').removeClass('blue');
            if (err) {
                $(this).parent().find('button').addClass('red');
                $(this).parent().find('button').html('<i class="warning icon"></i> ERROR');
                return alert('There was an error uploading your photo: ', err.message);
            }

            $(this).parent().find('button').addClass('green');
            $(this).parent().find('button').html('<i class="checkmark icon"></i> OK');
            $(this).parent().find('p').html('Completado en ' + Math.round((current - initF) / 1000) + ' seg.');
            $(this).parent().find('input[type=hidden]').val(data.Key);
        };
        var onProgressF = function (progress) {
            var current = new Date().getTime();
            var speed = Math.floor((progress.loaded) / (current - initF));
            var remaing = Math.round((progress.total - progress.loaded) / (speed * 1024));
            var perc = Math.floor(progress.loaded / progress.total * 1000) / 10;
            $(this).parent().find('button').html('<i class="wait icon"></i> ' + perc + ' %');
            if (remaing <= 120) {
                $(this).parent().find('p').html('Faltan: &sim;' + remaing + ' seg.<br /> (vel. &sim;' + speed + ' KB/s)');
            } else {
                $(this).parent().find('p').html('Faltan: &sim;' + Math.round(remaing / 60) + ' min.<br /> (vel. &sim;' + speed + ' KB/s)');
            }
        };
        $(document).ready(function () {

            $('#notificationsfilebtn').click(function () {
                $('#notificationsfile').click();
            });

            $('#notificationsfilebtn2').click(function () {
                $('#notificationsfile2').click();
            });
            $('#notificationsfilebtn7').click(function () {
                $('#notificationsfile7').click();
            });

            $('#notificationsfilebtnglosas').click(function () {
                $('#notificationsfileglosas').click();
            });

            $('#notificationsfilebtn4').click(function () {
                $('#notificationsfile4').click();
            });
            $('#notificationsfilebtnx').click(function () {
                $('#notificationsfilex').click();
            });

            $('#notificationsfile, #notificationsfile2, #notificationsfile7,  #notificationsfileglosas, #notificationsfile4, #notificationsfilex').change(function () {
                $(this).parent().find('button').removeClass('green');
                $(this).parent().find('button').removeClass('red');
                $(this).parent().find('button').addClass('blue');
                var files = $(this).prop("files");
                if (!files.length) {
                    return alert('Please choose a file to upload first.');
                }
                var file = files[0];
                var fileName = file.name;
                var fileNameExt = file.name.split('.').pop();
                var fileNameHash = makeUIDF(27);
                var albumPhotosKey = encodeURIComponent('notifications_zip') + '/';

                var photoKey = albumPhotosKey + fileNameHash + '.' + fileNameExt;
                var onUploadB = onUploadF.bind(this);
                var onProgressB = onProgressF.bind(this);

                initF = new Date().getTime();
                s3.upload({
                    Key: photoKey,
                    Body: file,
                }, {partSize: 20 * 1024 * 1024, queueSize: 1}, onUploadB).on('httpUploadProgress', onProgressB);
            });

        });
        $(document).ready(function () {
            var init = 0;
            var makeUID = function (length) {
                var text = "";
                var possible = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";

                for (var i = 0; i < length; i++)
                    text += possible.charAt(Math.floor(Math.random() * possible.length));

                return text + new Date().getTime();
            };
            var onUpload = function (err, data) {
                if (err) {
                    return alert('There was an error uploading your photo: ', err.message + " " + data.Key);
                    $('#parrafo-log2').append('error en: ' + data.Key + '<br/>');
                }
            };

            var onProgress = function (progress) {
                var current = new Date().getTime();
                var speed = Math.floor((progress.loaded) / (current - init));
                var remaing = Math.round((progress.total - progress.loaded) / (speed * 1024));

                if (remaing <= 120) {
                    $('#parrafo-log').html('Faltan: &sim;' + remaing + ' seg.<br /> (vel. &sim;' + speed + ' KB/s)');
                } else {
                    $('#parrafo-log').html('Faltan: &sim;' + Math.round(remaing / 60) + ' min.<br /> (vel. &sim;' + speed + ' KB/s)');
                }
            };
            var saveDocuments = function (documents, files) {
                $.post('/carguemasivo/upload-files-glosas', {
                    documents: documents
                }, function (result) {
                    result = JSON.parse(result);
                    $('#logs-upload-files-glosas').html(result['message']);
                    console.log(result['documents_keys']);
                    saveResidualDocuments(result['documents_keys'], documents, files);
                });
            };
            var saveResidualDocuments = function (ids, documents, files) {

                //var files = $('#div-save-files-glosas input[type=file]').prop("files");

                var onUploadB = onUpload.bind(this);
                var onProgressB = onProgress.bind(this);

                for (var i = 0; i < ids.length; i++) {

                    init = new Date().getTime();
                    console.log(documents[ids[i]]);

                    s3.upload({
                        Key: documents[ids[i]]['photoKey'],
                        Body: files[ids[i]],
                    }, {partSize: 20 * 1024 * 1024, queueSize: 1}, onUploadB).on('httpUploadProgress', onProgressB);

                }
            };
            $('#div-save-files-glosas input[type=file]').change(function () {
                var files = $(this).prop("files");
                if (!files.length) {
                    return alert('Please choose a file to upload first.');
                }

                var jsonData = [];

                for (var i = 0; i < files.length; i++) {
                    var file = files[i];
                    var fileName = file.name;
                    var fileNameExt = file.name.split('.').pop();
                    var fileNameHash = makeUID(27);
                    var albumPhotosKey = encodeURIComponent('documents') + '/';

                    var photoKey = albumPhotosKey + fileNameHash + '.' + fileNameExt;
                    //var onUploadB = onUpload.bind(this);
                    //var onProgressB = onProgress.bind(this);

                    jsonData.push({'filename': fileName, 'photoKey': photoKey});
                    /*
                    s3.upload({
                        Key: photoKey,
                        Body: file,
                    }, {partSize: 20 * 1024 * 1024, queueSize: 1}, onUploadB).on('httpUploadProgress', onProgressB);
                    */
                }
                console.log(jsonData);
                saveDocuments(jsonData, files);
            });

        });
    </script>
@endsection
