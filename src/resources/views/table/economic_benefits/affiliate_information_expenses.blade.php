@extends('table.affiliate')

@section('content_affiliate')
    @include('table/economic_benefits/menu_expenses', ['active' => 'reconocimiento_gastos'])

    <form id="informacion-afiliado-gastos-form" enctype="multipart/form-data" class="ui attached form"
          action="{{ secure_url('tablero/afiliado/'.$id.'/reconocimiento_gastos/save') }}" method="POST" >

        {{ csrf_field() }}
        <div class="ui basic segment container">
            <div class="ui styled fluid accordion">
                <div class="active title">
                    <i class="dropdown icon"></i>
                    Información afiliado <span style="color: red;" class="required">*</span>
                </div>
                <div class="content active">

                    <div class="ui three fields">

                        <div class="required field">
                            <label class="item-label" for="identification_titular">Tipo identificación:</label>
                            <div id="identification_titular" class="ui search selection dropdown disabled-select-fields" style="pointer-events: none;">
                                <input type="hidden" name="identification_titular" class="minus_font" value="{{ $affiliate->doc_type }}">
                                <i class="dropdown icon"></i>
                                <div class="default text">Seleccione uno</div>
                                <div class="menu">
                                    @foreach($DOC_TYPES as $k => $v)
                                        <div class="item {{ $affiliate->doc_type == $k ? 'active selected' : '' }}" data-value="{{ $k }}">{{ $v }}</div>
                                    @endforeach
                                </div>
                            </div>
                        </div>

                        <div class="field">
                            <div class="ui list">
                                <div class="required field">
                                    <label class="item-label" for="identification_number"># Identificación:</label>
                                    <input type="text" id="identification_number" name="identification_number" class="grayed-input" value="{{$affiliate->doc_number ?? ''}}" readonly>
                                </div>
                            </div>
                        </div>

                        <div class="field">
                            <div class="ui list">
                                <div class="required field">
                                    <label class="item-label" for="worker_name">Nombre:</label>
                                    <input type="text" id="worker_name" name="worker_name" class="grayed-input" value="{{mb_convert_case(mb_strtolower($affiliate->full_name ?? '' ), MB_CASE_TITLE, "UTF-8")}}" readonly>
                                </div>
                            </div>
                        </div>

                    </div>

                    <div class="ui three fields">

                        <div class="field">
                            <div class="ui list">
                                <div class="required field">
                                    <label for="worker_email" class="required">Correo electrónico persona trabajadora:</label>
                                    <input type="text" id="worker_email" name="worker_email"  class="{{ $data_affiliate['worker_email'] == '' ? "minus_font" : "grayed-input" }} " value="{{ $data_affiliate['worker_email'] ?? '' }}" >
                                </div>
                            </div>
                        </div>

                        <div class="field">
                            <div class="ui list">
                                <div class="required field">
                                    <label class="item-label" for="worker_phone">Teléfono persona trabajadora:</label>
                                    <input type="text" id="worker_phone" name="worker_phone" class="{{ $data_affiliate['worker_phone'] == '' ? "minus_font" : "grayed-input" }} " value="{{ $data_affiliate['worker_phone'] ?? '' }}" >
                                </div>
                            </div>
                        </div>

                        <div class="field">
                            <div class="ui list">
                                <div class="required field">
                                    <label class="item-label" for="worker_address">Dirección del trabajador (otras señas):</label>
                                    <input type="text" id="worker_address" name="worker_address" class="{{ $data_affiliate['worker_address'] == '' ? "minus_font" : "grayed-input" }} " value="{{ $data_affiliate['worker_address'] ?? '' }}" >
                                </div>
                            </div>
                        </div>

                    </div>

                    <div class="ui three fields">
                        <div class="required field">
                            <label class="item-label" for="province">Provincia:</label>
                            <div id="province" class="ui selection dropdown {{$data_affiliate['province'] == '' ? "" : "disabled-select-fields" }}"  style="{{$data_affiliate['province'] == '' ? "" : "pointer-events: none;" }}" >
                                <input type="hidden" name="province" class="minus" value="">
                                <i class="dropdown icon"></i>
                                <div class="default text">Seleccione uno</div>
                                <div class="menu"></div>
                            </div>
                        </div>

                        <div class="required field">
                            <label class="item-label" for="canton">Cantón:</label>
                            <div id="canton" class="ui search selection dropdown {{$data_affiliate['province'] == '' ? "" : "disabled-select-fields" }}" style="{{$data_affiliate['canton'] == '' ? "" : "pointer-events: none;" }}" >
                                <input type="hidden" name="canton" class="minus_font" value="">
                                <i class="dropdown icon"></i>
                                <div class="default text">Seleccione uno</div>
                                <div class="menu"></div>
                            </div>
                        </div>

                        <div class="required field">
                            <label class="item-label" for="district">Distrito:</label>
                            <div id="district" class="ui search selection dropdown {{$data_affiliate['province'] == '' ? "" : "disabled-select-fields" }}" style="{{$data_affiliate['district'] == '' ? "" : "pointer-events: none;" }}">
                                <input type="hidden" name="district" class="minus_font" value="">
                                <i class="dropdown icon"></i>
                                <div class="default text minus">Seleccione uno</div>
                                <div class="menu"></div>
                            </div>
                        </div>
                    </div>

                    <div class="ui three fields">

                        <div class="field">
                            <div class="ui list">
                                <div class="required field">
                                    <label for="recognition_type" class="required">Tipo de reconocimiento:</label>
                                    <select id="recognition_type" name="recognition_type" class="ui dropdown" onchange="toggleInvoiceConceptDos()">
                                        <option value="">Seleccione el tipo de reconocimiento</option>
                                        <option value="funeral_transfer" {{($economicBenefit->recognition_type ?? '') == 'funeral_transfer' ? 'selected' : ''}}>Funeral y traslado del cadáver</option>
                                        <option value="transfer_accommodation" {{($economicBenefit->recognition_type ?? '') == 'transfer_accommodation' ? 'selected' : ''}}>Traslado, hospedaje, alimentación</option>
{{--                                        <option value="invoices" {{($economicBenefit->recognition_type ?? '') == 'invoices' ? 'selected' : ''}}>Facturas</option>--}}
                                        <option value="refund" {{($economicBenefit->recognition_type ?? '') == 'refund' ? 'selected' : ''}}>Reconocimiento de factura por riesgos de trabajo</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="field" id="invoice_concept_file" >
                            <div class="ui list">
                                <div class="field">
                                    <label class="required">Concepto de factura:</label>
                                    <select id="invoice_concept" name="invoice_concept" class="ui dropdown"  >
                                        <option value="">Seleccione el concepto de factura</option>
                                        <option value="medical_emergency" {{($economicBenefit->invoice_concept ?? '') == 'medical_emergency' ? 'selected' : ''}} >Emergencia médica</option>
                                        <option value="mnk_reference" {{($economicBenefit->invoice_concept ?? '') == 'mnk_reference' ? 'selected' : ''}} >Referencia médica de MNK</option>
                                        <option value="outpatient_services" {{($economicBenefit->invoice_concept ?? '') == 'outpatient_services' ? 'selected' : ''}} >Servicios ambulatorios</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="field" id="div_authorization" >
                            <div class="ui list">
                                <div class="required field">
                                    <label class="required">Cuenta con autorización médica:</label>
                                    <select id="authorization" name="authorization" class="ui dropdown"  >
                                        <option value="">Seleccione una opción</option>
                                        <option value="1" {{ isset($economicBenefit) && $economicBenefit->authorization == '1' ? 'selected' : '' }} >Si</option>
                                        <option value="0" {{ isset($economicBenefit) && $economicBenefit->authorization == '0' ? 'selected' : '' }}>No</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="required field">
                            <label class="item-label" for="case"># Caso:</label>
                            <div id="case_dropdown" class="ui search selection dropdown {{--{{ isset($economicBenefit->id) ? 'disabled-select-fields' : '' }} --}} "
                                    {{--style="{{ isset($economicBenefit->id) ? 'pointer-events: none;' : '' }}"  --}}>
                                <input type="hidden" name="case" id="case" class="minus_font" value="{{ isset($economicBenefit->activity_id) ? $economicBenefit->activity_id : '' }}">
                                <i class="dropdown icon"></i>
                                <div class="default text">Seleccione uno</div>
                                <div class="menu">
                                    @foreach($activity as $activitys)
                                        @if($activitys->gis_sort)
                                            <div class="item" data-value="{{ $activitys->gis_sort->activity_id }}"
                                                    {{ isset($economicBenefit->activity_id) && $activitys->gis_sort->activity_id == $economicBenefit->activity_id ? 'class=active selected' : '' }}>
                                                {{ $activitys->gis_sort->formatCaseNumber() }}
                                            </div>
                                        @endif
                                    @endforeach
                                </div>
                            </div>
                        </div>


                    </div>

                    <div class="ui three fields">

                        <div class="field">
                            <div class="ui list">
                                <div class="field">
                                    <label class="item-label" for="request_description">Descripción de solicitud:</label>
                                    <textarea id="request_description" name="request_description"> {{$economicBenefit->request_description ?? ''}} </textarea>
                                </div>
                            </div>
                        </div>

                        <div class="field" id="div_date_accident">
                            <div class="ui list">
                                <div class="required field">
                                    <label class="item-label" for="date_accident">Fecha de accidente:</label>
                                    <input type="text" id="date_accident" name="date_accident" class="grayed-input"  >
                                </div>
                            </div>
                        </div>

                        <div class="field" id="div_iban_account">
                            <div class="ui list">
                                <div class="required field">
                                    <label class="item-label" for="iban_account">Cuenta IBAN:</label>
                                    <input type="text" id="iban_account" name="iban_account" value="{{ isset($economicBenefit) && $economicBenefit->iban_account ? $economicBenefit->iban_account : '' }}"  >
                                </div>
                                <div class="ui negative message hidden" id="ibanError">El IBAN debe tener 22
                                    caracteres
                                    alfanuméricos.
                                </div>
                            </div>
                        </div>


                    </div>

                    <br>
                    <div style="display: flex; justify-content: space-between; width: 100%;">
                        <a href="#"></a>

                        <button class="ui primary button">Siguiente</button>
                    </div>
                </div>
            </div>
        </div>
    </form>

    <style>

        .disabled-select-fields {
            background-color: #f3f4f5 !important; /* Color gris */
            color: #333 !important;
            border-color: #ddd !important;
        }

    </style>


    <script>

        let activity_gis = "{{ $economicBenefit->activity_id ?? '' }}";


        function toggleInvoiceConcept() {
            var recognitionType = document.getElementById("recognition_type").value;
            var invoiceConcept = document.getElementById("invoice_concept");

            if (recognitionType === "invoices") {
                invoiceConcept.disabled = false;
            } else {
                invoiceConcept.disabled = true;
                invoiceConcept.value = "";
            }
        }

        function toggleInvoiceConceptDos(recognition_type = '') {

            if (recognition_type === ''){
                $("#authorization").val("").trigger("change");
            }

            recognition_type = recognition_type || $("#recognition_type").val(); // Si no se pasa parámetro, obtener el valor del select

            // Ocultar todos los elementos al inicio
            $("#invoice_concept_file, #div_authorization, #div_date_accident, #div_iban_account").hide();
            // Mostrar los elementos según el tipo de reconocimiento
            if (recognition_type === 'invoices') {
                $("#invoice_concept_file").show();
            } else if (recognition_type === 'refund') {
                $("#div_authorization, #div_date_accident, #div_iban_account").show();

                const selectedCase = document.getElementById('case').value;

                if (selectedCase) {
                    consultaData(selectedCase);
                }

            }
        }


        $(document).ready(function() {

            $('.datepicker').pickadate({
                selectYears: true,
                selectMonths: true,
                formatSubmit: 'yyyy-mm-dd',
            });

            // Inicialización normal del dropdown
            $('#case').dropdown();

            $('#case').on('change', function() {
                var value = $(this).val();
                if (value && $("#recognition_type").val() === 'refund') {
                    consultaData(value);
                }
            });


            if(activity_gis && $("#recognition_type").val() === 'refund') {
                consultaData(activity_gis);
            }


            var recognition_type = "{{$economicBenefit->recognition_type ?? ''}}";

            toggleInvoiceConceptDos(recognition_type);

            $('input.only-numbers').on('input', function() {
                this.value = this.value.replace(/[^0-9]/g, '');
            });


            $('#informacion-afiliado-gastos-form').on('submit', function(e) {

                loadingMain(true);

                //document.getElementById('case').disabled = false;

                $('.error-message').remove();
                let isValid = true;

                $('#informacion-afiliado-gastos-form .required').each(function() {
                    let input = $(this).find('input, select, textarea');

                    if (input.attr('id') === 'authorization' && $('#div_authorization').is(':hidden')
                    || input.attr('id') === 'iban_account' && $('#div_iban_account').is(':hidden') ) {
                        return true; // Continúa con la siguiente iteración sin validar este campo
                    }

                    if (input.val() === '') {
                        isValid = false;
                        $(this).append('<div class="ui pointing red basic label error-message">Este campo es obligatorio</div>');
                        //loadingMain(false);
                        //e.preventDefault();
                    }
                });

                const emailField = $('#worker_email');
                const emailValue = emailField.val().trim();

                function isValidEmail(email) {
                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    return emailRegex.test(email);
                }

                if (emailValue !== '' && !isValidEmail(emailValue)) {
                    isValid = false;
                    emailField.parent().append('<div class="ui pointing red basic label error-message">Correo electrónico inválido</div>');
                }

                if ($('#recognition_type').val()  === 'invoices' && $('#invoice_concept').val() === '') {
                    isValid = false;
                    $('#invoice_concept').parent().append('<div class="ui pointing red basic label error-message">Este campo es obligatorio</div>');
                }

                if ($('#recognition_type').val() === 'refund') {
                    if (isValid && $('#iban_account').val().length !== 22 ) {
                        isValid = false;
                        $('#iban_account').parent().append('<div class="ui pointing red basic label error-message">Debe tener exactamente 22 caracteres</div>');
                    }
                }

                if (!isValid) {
                    loadingMain(false);
                    //document.getElementById('case').disabled = true;
                    e.preventDefault();
                }
            });

            const selectedProvince = "{{$data_affiliate['province']}}";
            const selectedCanton = "{{$data_affiliate['canton']}}";
            const selectedDistrict = "{{$data_affiliate['district']}}";

            let costarica = {};

            $.getJSON("/js/costarica.json", function (json) {
                costarica = json["province"];
                populateDropdown($("#province"), costarica);

                // Inicializar los valores seleccionados al cargar la página
                if (selectedProvince) {
                    populateCantons(selectedProvince);
                    $("#province").dropdown("set selected", selectedProvince);
                }

                if (selectedCanton && selectedProvince) {
                    populateDistricts(selectedProvince, selectedCanton);
                    $("#canton").dropdown("set selected", selectedCanton);
                }

                if (selectedDistrict && selectedCanton && selectedProvince) {
                    $("#district").dropdown("set selected", selectedDistrict);
                }

                // Manejar cambios en la selección de provincia
                $("#province").change(function () {
                    const province = $(this).dropdown("get value");
                    populateCantons(province);
                });

                // Manejar cambios en la selección de cantón
                $("#canton").change(function () {
                    const canton = $(this).dropdown("get value");
                    const province = $("#province").dropdown("get value");
                    if (canton && province) {
                        populateDistricts(province, canton);
                    }
                });
            });

            // Función para poblar provincias, cantones o distritos
            function populateDropdown(dropdown, items) {
                dropdown.dropdown("clear");
                dropdown.find(".menu").empty();
                items.forEach(item => {
                    const itemName = item.name.charAt(0).toUpperCase() + item.name.slice(1).toLowerCase();
                    dropdown.find(".menu").append(
                        `<div class="item" data-value="${item.code}">${itemName}</div>`
                    );
                });
                // Volver a inicializar el dropdown después de actualizar los elementos
                dropdown.dropdown("refresh");
            }

            // Función para poblar cantones según la provincia seleccionada
            function populateCantons(provinceCode) {
                const province = costarica.find(p => p.code === provinceCode);
                if (province) {
                    populateDropdown($("#canton"), province.cantons);
                    const canton = selectedCanton || (province.cantons[0] ? province.cantons[0].code : null);
                    if (canton) {
                        $("#canton").dropdown("set selected", canton);
                        populateDistricts(provinceCode, canton);
                    }
                }
            }

            // Función para poblar distritos según el cantón seleccionado
            function populateDistricts(provinceCode, cantonCode) {
                const province = costarica.find(p => p.code === provinceCode);
                const canton = province ? province.cantons.find(c => c.code === cantonCode) : null;
                if (canton) {
                    populateDropdown($("#district"), canton.districts);
                    const district = selectedDistrict || (canton.districts[0] ? canton.districts[0].code : null);
                    if (district) {
                        $("#district").dropdown("set selected", district);
                    }
                }
            }

            $('#iban_account').on('input', function() {
                // Permitir solo letras y números
                $(this).val($(this).val().replace(/[^a-zA-Z0-9]/g, '').toUpperCase());

                // Limitar a 22 caracteres
                if ($(this).val().length > 22) {
                    $(this).val($(this).val().substring(0, 22));
                }
            });


        });

        function consultaData(value){
            console.log('consultaData');
            loadingMain(true);

            $.ajax({
                url: `/get-gis-affiliate/${value}`,
                type: 'GET',
                success: function(response) {

                    if (response.success) {

                        $("#date_accident").val(response.data.date_accident);

                        if (response.data.iban !== ''){
                            $("#iban_account").val(response.data.iban);
                        }

                        loadingMain(false);
                    } else {
                        loadingMain(false);

                        Swal.fire({
                            icon: 'error',
                            title: 'Error en la solicitud',
                            text: 'Error al cargar los información ',
                            confirmButtonText: 'Cerrar'
                        });
                    }

                },
                error: function (xhr) {
                    loadingMain(false);
                    console.log('Error:', xhr.responseJSON?.message || 'Error desconocido');

                    Swal.fire({
                        icon: 'error',
                        title: 'Error en la solicitud',
                        text: 'Error al cargar los información ',
                        confirmButtonText: 'Cerrar'
                    });
                }

            });
        }


    </script>

@endsection