
@extends('table.affiliate')

@section('content_affiliate')

    <div class="ui basic segment">

        <div class="ui styled fluid accordion">

            <div class="active title">
                <i class="dropdown icon"></i>
                Prestaciones económicas
            </div>
            <div class=" active content">
                <table class="ui celled sortable striped compact very  table selectable "
                       style="font-size: 0.8em; line-height: 1em;" id="results">
                    <thead>
                    <tr>
                        <th class="truncate" title="Número de identificación"># identificación</th>
                        <th class="truncate" title="Nombre del Paciente">Nombre del paciente</th>
                        <th class="truncate" title="Nombre del tomador">Nombre del tomador</th>
                        <th class="truncate" title="Fecha del caso">Fecha del caso</th>
                        <th class="truncate" title="Número del aviso"># Aviso</th>
                        <th class="truncate" title="Número del caso"># Caso</th>
                        <th class="truncate" title="Número del trámite"># Trámite</th>
                        <th class="truncate" title="Tipo servicio">Tipo servicio</th>
                        <th class="truncate" title="Estado">Estado</th>
                        <th class="truncate" title="Tipo de incapacidad">Tipo de incapacidad</th>
                        <th class="truncate" title="Días de incapacidad">Días de incapacidad</th>
                        <th class="truncate" title="Valor a reconocer">Valor a reconocer</th>
                        <th class="truncate" title="Fecha de pago">Fecha de pago</th>
                        <th class="truncate" title="servicio">servicio</th>
                        <th class="truncate" title="Observación">Observación</th>
                        <th title="Cargar documentos">Cargar documentos</th>
                        <th title="Solicitudes">Solicitudes</th>
                    </tr>
                    </thead>
                    <tbody>
                        @foreach($activity as $row)

                            <tr>
                                <td class="left aligned" >{{ $row->affiliate->doc_number ?? '' }}</td>
                                <td class="left aligned" >{{ mb_convert_case(mb_strtolower(($row->affiliate->full_name ?? '')), MB_CASE_TITLE, "UTF-8") }}</td>
                                <td class="left aligned" >{{ mb_convert_case(mb_strtolower(($row->parent->parent->affiliate->full_name ?? '')), MB_CASE_TITLE, "UTF-8") }}</td>
                                <td class="left aligned" >{{ $row->parent ? $row->parent->created_at->formatLocalized('%A %d de %B de %Y') : '' }}</td>
                                <td class="right aligned" >{{ $row->consecutive_gis ? $row->consecutive_gis  : '' }}</td>
                                <td class="right aligned" >{{ $row->aviso ? $row->aviso  : '' }}</td>
                                <td class="right aligned" >{{ $row->id_service }}</td>
                                <td class="left aligned" >{{ ucfirst(mb_strtolower($row->service->name ?? '')) }}</td>
                                <td class="left aligned" >{{ ucfirst(mb_strtolower($row->state->name ?? '')) }}</td>
                                <td class="left aligned" >{{$row->service_description}}</td>
                                <td class="right aligned" >{{$row->days}}</td>
                            {{--    total--}}
                                <td class="left aligned" >
                                    @if($row->service_id == \App\Service::SERVICE_PE_IP_SORT_MNK )
                                        {{number_format((float) ($row->pe_ip_sort->payinfo_monthy_amount_pay ?? 0), 2, ',', '.')}}
                                    @elseif($row->service_id == \App\Service::SERVICE_PE_IT_SORT_MNK)
                                        {{number_format((float) ($row->peItSort->inabilitySort->amount_pay ?? 0), 2, ',', '.')}}
                                    @elseif($row->service_id == \App\Service::SERVICE_PE_MPT_SORT_MNK)
                                        {{number_format((float) ($row->total ?? 0), 2, ',', '.')}}
                                    @elseif($row->service_id == \App\Service::SERVICE_MEDICAL_BILLS_MNK)
                                        {{number_format((float) ($row->medical_bill->total_value_invoice ?? 0), 2, ',', '.')}}
                                    @elseif($row->service_id == \App\Service::SERVICE_PE_RECOGNITION_EXPENSES_MNK)
                                        {{number_format((float) ($row->pe_recognition_expenses->accepted_value_of_recognition ?? 0), 2, ',', '.')}}
                                    @elseif($row->service_id == \App\Service::SERVICE_REINTEGRATE_MNK)

                                        @if($row->state_id == \App\States\StateReintegrate::FACTURA_REINTEGRO_APROBADA ||
                                            $row->state_id == \App\States\StateReintegrate::FACTURA_PARCIAL_REINTEGRO_APROBADA ||
                                            $row->state_id == \App\States\StateReintegrate::FACTURA_PAGADA)

                                            {{number_format((float) ($row->reintegrate->total_approved ?? 0), 2, ',', '.')}}
                                        @else
                                            0,0
                                        @endif

                                    @endif

                                </td>

                                <td class="left aligned" >{{ $row->date_payment ? ucfirst(strftime('%A %e de %B del %Y',strtotime($row->date_payment))) : '' }}</td>

                                @if($row->service_id == \App\Service::SERVICE_REINTEGRATE_MNK)
                                    <td>
                                        <a  class="ui clickable-link ver-poliza"
                                             href="/servicio/{{ $row->id }}/reintegrate">
                                            <i class="external eye icon"></i></a>
                                    </td>
                                @else
                                    <td>
                                        <a  class="ui clickable-link ver-poliza"
                                             href="/servicio/{{ $row->id }}">
                                            <i class="external eye icon"></i></a>
                                    </td>
                                @endif

                                <td class="left aligned">
                                    @if($row->service_id == \App\Service::SERVICE_PE_IT_SORT_MNK  )
                                        {{ $row->PeItSort->observation_medical ?? '' }}
                                    @endif
                                </td>
                                <td class="center aligned">
                                    @if($row->service_id == \App\Service::SERVICE_PE_IT_SORT_MNK && ($row->state_id == \App\States\StatePeItSort::EN_VALIDACION_DE_PAGOS  || $row->state_id == \App\States\StatePeItSort::VALIDACION_DE_INCAPACIDAD ) )
                                        <a href="#" class="upload-document" data-id="{{ $row->id }}">
                                            <i class="upload icon custom-icon secondary"></i>
                                        </a>
                                    @endif
                                </td>

                                <td class="center aligned">
                                    @if(!empty($row->PeItSort->disability_adjustment_document))
                                        Solicitud de ajuste enviada
                                    @else
                                        @if($row->service_id == \App\Service::SERVICE_PE_IT_SORT_MNK && ($row->state_id == \App\States\StatePeItSort::EN_VALIDACION_DE_PAGOS  || $row->state_id == \App\States\StatePeItSort::VALIDACION_DE_INCAPACIDAD || $row->state_id == \App\States\StatePeItSort::EN_PAGO_DE_FRACCIONES || $row->state_id == \App\States\StatePeItSort::PENDIENTE_SOPORTE_DE_PAGO_FRACCION) )
                                            <a class="upload-document-ajust-inhanility" data-id="{{ $row->id }}"><i class="file icon secondary"></i></a>
                                        @endif
                                    @endif
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
                <!-- Paginación -->
                <br>
                <div class="pagination">
                    @if ($activity->hasPages())
                        <div class="ui pagination menu">
                            {{ $activity->appends(request()->query())->links() }}
                        </div>
                    @endif
                    <p>Total de registros: {{ $activity->total() }}</p>
                </div>

                <div class="ui basic segment">
                    <div class="fields">
                        <div class="six wide field">
                            <a href="{{secure_url('/tablero/afiliado/'.$id.'/radica_incapacidad')}}" class="ui secondary button"> Documentar incapacidad</a>
                            <a href="{{secure_url('/tablero/afiliado/'.$id.'/reconocimiento_gastos')}}" class="ui secondary button"> Reconocimiento de gastos</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="ui modal" id="uploadModal">
            <div class="header">Cargar Documentos</div>
            <div class="content">
                <form class="ui form" id="uploadForm" enctype="multipart/form-data">
                    <input type="hidden" name="service_id" id="service_id">
                    <div class="field">
                        <label>Seleccionar archivo (PDF o Word)</label>
                        <input type="file" name="file" id="fileInput" accept=".pdf,.doc,.docx" required>
                    </div>
                </form>
            </div>
            <div class="actions">
                <button class="ui button cancel">Cancelar</button>
                <button class="ui secondary button" id="uploadBtn">Subir Archivo</button>
            </div>
        </div>

        {{-- Documentos para el ajuste del pago de la incapacidad temporal --}}
        <div class="ui modal" id="uploadModalDocumentInhability">
            <div class="header">Cargar Documentos</div>
            <div class="content">
                <form class="ui form" id="uploadFormDocument" enctype="multipart/form-data">
                    <input type="hidden" name="service_id" id="service_id_peit">
                    <div class="field">
                        <label>Seleccionar archivo (PDF o Word)</label>
                        <input type="file" name="file" id="fileInputDocument" accept=".pdf,.doc,.docx" required>
                    </div>
                </form>
            </div>
            <div class="actions">
                <button class="ui button cancel">Cancelar</button>
                <button class="ui secondary button" id="uploadBtnDocument">Subir Archivo</button>
            </div>
        </div>
    </div>

    <style>
        .pagination {
            display: grid;
            justify-items: end;
        }

        .truncate {
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 1px;
            min-width: 40px;
            padding: 1em 6px !important;
        }
        .custom-icon {
            color: blue; /* Cambia a cualquier color */
        }
    </style>

    <script>
        $('.ver-poliza').popup({
            boundary: 'body',
            content: 'Ver formulario',
            position: 'top center'
        });


        $(document).ready(function() {

            $('.ui.dropdown').dropdown();
            $('.timepicker').pickatime();

            $('.ui.accordion').accordion({
                exclusive: false
            });

            $('.abrir-modal').click(function() {
                let idServicio = $(this).data('id');
                $('#id_servicio').val(idServicio);
                $('#modalCargaDocumentos').modal('show');
            });

            $('.upload-document').on('click', function () {
                let serviceId = $(this).data('id');
                $('#service_id').val(serviceId);
                $('#uploadModal').modal('show');
            });

            //Abrir modal para cargar documentos de ajustes de incapcidad 
            $('.upload-document-ajust-inhanility').on('click', function () {
                let serviceId = $(this).data('id');
                $('#service_id_peit').val(serviceId);
                $('#uploadModalDocumentInhability').modal('show');
            });
           
            $('#uploadModal').on('hidden.bs.modal', function () {
                $('#fileInput').val(''); // Limpia el input de archivo
            });

    
            $('#uploadBtn').on('click', function () {
                let fileInput = $('#fileInput')[0].files[0];
                if (!fileInput) {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: `Por favor, selecciona un archivo..`
                    });
                    return;
                }

                let allowedExtensions = ['pdf', 'doc', 'docx'];
                let fileExtension = fileInput.name.split('.').pop().toLowerCase();
                if (!allowedExtensions.includes(fileExtension)) {
                    Swal.fire({
                        icon: 'error',
                        title: 'Formato no permitido',
                        text: `Formato no permitido. Solo se aceptan archivos PDF y Word.`
                    });
                    return;
                }

                let formData = new FormData($('#uploadForm')[0]);

                loadingButton('uploadBtn', true);

                $.ajax({
                    url: '/tablero/afiliado/upload_econimic',
                    type: 'POST',
                    data: formData,
                    contentType: false,
                    processData: false,
                    headers: {
                        'X-CSRF-TOKEN': $('input[name="_token"]').val()
                    },
                    success: function (response) {

                        $('#uploadModal').modal('hide');

                        loadingButton('uploadBtn', false);
                        Swal.fire({
                            icon: 'success',
                            title: 'Éxito',
                            text: `Archivo subido correctamente.`
                        });

                    },
                    error: function () {

                        loadingButton('uploadBtn', false);
                        Swal.fire({
                            icon: 'error',
                            title: 'Error',
                            text: `Hubo un error al subir el archivo.`
                        });
                    }
                });
            });

            // Subir documento seleccionado por AJAX
            $('#uploadBtnDocument').on('click', function () {
                let fileInput = $('#fileInputDocument')[0].files[0];
                if (!fileInput) {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: `Por favor, selecciona un archivo..`
                    });
                    return;
                }

                let allowedExtensions = ['pdf', 'doc', 'docx'];
                let fileExtension = fileInput.name.split('.').pop().toLowerCase();
                if (!allowedExtensions.includes(fileExtension)) {
                    Swal.fire({
                        icon: 'error',
                        title: 'Formato no permitido',
                        text: `Formato no permitido. Solo se aceptan archivos PDF y Word.`
                    });
                    return;
                }

                let formData = new FormData($('#uploadFormDocument')[0]);

                loadingButton('uploadBtn', true);

                $.ajax({
                    url: '/tablero/afiliado/upload_document_inhability',
                    type: 'POST',
                    data: formData,
                    contentType: false,
                    processData: false,
                    headers: {
                        'X-CSRF-TOKEN': $('input[name="_token"]').val()
                    },
                    success: function (response) {

                        $('#uploadModalDocumentInhability').modal('hide');

                        loadingButton('uploadBtn', false);
                        Swal.fire({
                            icon: 'success',
                            title: 'Éxito',
                            text: `Archivo subido correctamente.`
                        });

                    },
                    error: function () {

                        loadingButton('uploadBtn', false);
                        Swal.fire({
                            icon: 'error',
                            title: 'Error',
                            text: `Hubo un error al subir el archivo.`
                        });
                    }
                });
            });

        });

    </script>

@endsection