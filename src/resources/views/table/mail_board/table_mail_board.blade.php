@extends('layouts.main')

@section('title', 'Tablero de correos')

@section('menu')
    @parent
@endsection

@section('content')
    <div class="ui basic segment">
        <h2 style="color: black;">Tablero de correos</h2>
        <div class="ui styled fluid accordion">
            <div class="active title">
                <i class="dropdown icon"></i>
                Filtros
            </div>
            <div class="active content"> 
                <form class="ui form small clearing" method="GET" autocomplete="off" id="form-filter">
                    <input type="hidden" name="emails_array" id="emails_array" />
                    <div class="three fields">
                        {{-- label qu diga rango de fehcas del siniestor --}} 
                        <div class="field">
                            <label>Tipo servicio</label>
                            <select class="ui fluid search dropdown" name="service_ids[]" multiple="">
                                <option value="">Tipo servicio</option>
                                @foreach ($type_service as $service)
                                    <option value="{{ $service->id }}"
                                        {{ in_array($service->id, (array) old('service_ids', request()->input('service_ids', []))) ? 'selected' : '' }}>
                                        {{ ucfirst(mb_strtolower($service->name)) }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        <div class="field">
                            <label>Póliza SORT</label>
                            <input type="number" name="id_poliza" value="{{ request()->input('id_poliza') ?? '' }}"
                                class="input_data poliza_message" oninput="this.value = this.value.replace(/[^0-9]/g, '');">
                        </div>
                        <div class="field">
                            <label>Tipo persona</label>
                            <select class="ui fluid search dropdown" name="person_names[]" multiple="">
                                <option value="">Tipo persona</option>
                                @foreach ($type_person as $person)
                                    <option value="{{ $person->name }}"
                                        {{ in_array($person->name, (array) old('person_names', request()->input('person_names', []))) ? 'selected' : '' }}>
                                        {{ ucfirst(mb_strtolower($person->name)) }}
                                    </option> 
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="three fields filters">
                        <div class="field">
                            <label>Nombre</label>
                            <input type="text" name="person_name" value="{{ request()->input('person_name') ?? '' }}"
                                class="input_data">
                        </div>
                        <div class="field">
                            <label>Identificación</label>
                            <input type="text" name="person_identification" value="{{ request()->input('person_identification') ?? '' }}"
                                class="input_data">
                        </div>
                        <div class="field">
                            <label>Estado</label>
                            <select class="ui fluid search dropdown" name="state_names[]" multiple="">
                                <option value="">Estado</option>
                                @foreach ($states as $state)
                                    <option value="{{ $state->name }}"
                                        {{ in_array($state->name, (array) old('state_names', request()->input('state_names', []))) ? 'selected' : '' }}>
                                        {{ ucfirst(mb_strtolower($state->name)) }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="three fields filters">
                        <div class="field">
                            <label>Fecha de envío</label>
                            <input value="{{request()->input('start_date') ?? '' }}" placeholder="Desde"
                                type="text" name="start_date" class="datepicker">
                        </div>
                        <div class="field">
                            <label>Nombre del correo</label>
                            <input type="text" name="name_email" value="{{ request()->input('name_email') ?? '' }}"
                                class="input_data">
                        </div>
                    </div>
                    <div class="fields bfilter"> 
                        <button class="ui primary button">
                            <i class="search icon"></i>
                            Aplicar filtros
                        </button>

                        <button class="ui secondary button" id="reset" type="reset">
                            <i class="undo icon"></i>
                            Limpiar filtros
                        </button>
                    </div>
                </form>
            </div>
            <div class="active title">
                <i class="dropdown icon"></i>
                Resultados de la búsqueda
            </div>
            <div class="active content">
                <table class="ui celled sortable striped compact very table"
                    style="font-size: 0.8em; line-height: 1em;" id="results">
                    <thead>
                        <!-- Fila de encabezados -->
                        <tr>
                            <th class="truncate" title="Servicio">Servicio</th>
                            <th style="max-width: 300px; width: 300px;" class="truncate" title="Nombre del correo">Nombre del correo</th>
                            <th class="truncate" title="Póliza">Póliza</th>
                            <th class="truncate" title="Tipo de persona">Tipo de persona</th>
                            <th class="truncate" title="Nombre">Nombre</th>
                            <th class="truncate" title="Identificación">Identificación</th>
                            <th class="truncate" title="Estado">Estado</th>
                            <th class="truncate" title="Emails a los que se envío" style="max-width: 400px; width: 400px;">
                                Emails a los que se envío
                            </th>
                            <th class="truncate" title="Fecha de envío">Fecha de envío</th>
                            <th class="truncate" title="Acciones">Acciones</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($emails as $email)
                            <tr>
                                <td>{{ $email->name}}</td>
                                <td style="max-width: 300px; width: 300px;">{{ $email->name_email }}</td>
                                <td>{{ $email->number_policy}}</td>
                                <td>{{ $email->person_type }}</td>
                                <td>{{ $email->person_name }}</td>
                                <td style="word-wrap: break-word; white-space: normal;">{{ $email->person_identification }}</td>
                                <td>
                                    @if($email->state == "sent")
                                        Enviado
                                    @else
                                        {{ $email->state }}
                                    @endif
                                </td>
                                <td style="max-width: 400px; width: 400px; word-wrap: break-word; white-space: normal;">
                                    {{ $email->send_emails }}
                                </td>
                                 <td>
                                    {{ \Carbon\Carbon::parse($email->created_at)->format('d/m/Y H:i') }}
                                </td>
                                <td>
                                    <a class="ui clickable-link enviar-correo" style="cursor: pointer;"
                                        onclick="openEmailModal(this)" data-id="{{ $email->id }}"
                                        data-email="{{ $email->send_emails ?? '' }}" data-tooltip="Enviar correo">
                                        
                                        <img src="/correo2.png" alt="Enviar por correo" style="width: 20px; height: 20px;">
                                    </a>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>

                @include('table.mail_board.modal_mail_send')

                <br>

                  <!-- Paginación -->
                @if ($emails->hasPages())
                    <div class="ui grid">
                        <div class="eight wide column left aligned"></div>
                        <div class="eight wide column right aligned" style="right: 10px;">
                            <div class="ui pagination menu">
                                {{ $emails->links() }}
                            </div>
                        </div>
                    </div>
                @endif
                 <div style="text-align: right; margin-top:20px">
                    <p>Total de registros: {{ $emails->total() }}</p>
                </div>
                @if (\Session::has('success'))
                    <div class="ui success message" id="success-message">
                        <i class="close icon" id="close-success-message"></i>
                        <div class="header">
                            {!! \Session::get('success') !!}
                        </div>
                    </div>
                @endif

                <!-- Mostrar errores -->
                @if ($errors->any())
                    <div class="ui negative message">
                        <div class="header">Errores encontrados</div>
                        <ul class="list">
                            @foreach ($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.2.2/css/buttons.semanticui.min.css">

    <!-- DataTables CSS con integración para Semantic UI -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.semanticui.min.css">
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <!-- DataTables integración con Semantic UI -->
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.semanticui.min.js"></script>


    <script src="https://cdn.datatables.net/buttons/2.2.2/js/dataTables.buttons.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.semanticui.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.html5.min.js"></script>

    <!-- DataTables Select CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/select/1.3.4/css/select.semanticui.min.css">

    <!-- DataTables Select JS -->
    <script src="https://cdn.datatables.net/select/1.3.4/js/dataTables.select.min.js"></script>
    <script src="https://cdn.datatables.net/select/1.3.4/js/select.semanticui.min.js"></script>

       <!-- Agregar Moment.js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js"></script>


    <style>

        table {
            table-layout: fixed;
            width: 100%;
        }
        .bfilter {
            margin-top: 20px !important;
        }

        .truncate {
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 1px;
            min-width: 40px;
            padding: 1em 6px !important;
        }

        /* Este selector apunta al contenedor generado por DataTables para los botones */
        .dt-buttons {
            display: flex !important;
            justify-content: space-between !important;
            align-items: center;
            margin-bottom: 30px;
        }

        .butonExcel {
            margin-right: 5px !important;
        }
    </style>
    <script>

        function openEmailModal(element) {
            // Recuperar los atributos de datos del elemento que fue clickeado
            const emailId = $(element).data('id');
            const EmailEmails = $(element).data('email');

            // Debug: Imprimir valores en la consola
            console.log('Email ID:', emailId);
            console.log('Emails send:', EmailEmails);

            // Asegurarse de que los valores se están recuperando correctamente
            if (!emailId || !EmailEmails) {
                console.error("Error: Los valores de ID o Email son undefined.");
                return;
            }

            // Actualizar los valores del modal
            $('#hiddenQuotationId').val(emailId);
            $('#emails_array').val(EmailEmails);

            const emailContainer = $('#emailContainer');
            const emailsArray = $('#emails_array').val();

            console.log('correos',emailsArray);

            emailContainer.empty(); // Limpiar los inputs existentes

            if (emailsArray) {
                emailsArray.split(',').forEach(email => {
                    email = email.trim();
                    if (email) {
                        emailContainer.append(`
                            <div class="ui action input email-input" style="margin: 5px;">
                                <input type="email" name="emails[]" value="${email}" 
                                    style="border: 1px solid rgba(34, 36, 38, .15) !important;" 
                                    readonly>
                            </div>
                        `);
                    }
                });
            }

            // Mostrar el modal
            $('.longer.modal')
                .modal({
                    observeChanges: true,
                })
                .modal('show');
        }

        var table = null;

        $(document).ready(function() {

              // Función para formatear la fecha al cargar la página
              function formatDateField(selector) {
                let dateValue = $(selector).val();
                if (dateValue) {
                    let formattedDate = moment(dateValue, ["YYYY-MM-DD", "DD/MM/YYYY"]).format("DD/MM/YYYY");
                    $(selector).val(formattedDate);
                }
            }

            // Formatear ambos campos de fecha
            formatDateField('input[name="start_date"]');

            $('.ui.dropdown').dropdown({
                allowAdditions: false,  // Evita agregar valores personalizados
                fullTextSearch: true,   // Habilita la búsqueda dentro del dropdown
                clearable: true         // Permite limpiar la selección
            });

            $('.datepicker').pickadate({
                selectYears: true,  // Permite seleccionar años en un menú desplegable
                selectMonths: true, // Permite seleccionar meses en un menú desplegable
                format: 'dd/mm/yyyy',  // Formato visible en el input
                formatSubmit: 'yyyy-mm-dd', // Formato que se envía en el formulario
                hiddenName: true // Mantiene el valor original en el input sin afectar el formato visual
            });

    
            // table = $('#results').DataTable({
            //     paging: true,
            //     searching: false,
            //     ordering: true,
            //     select: {
            //         style: 'multi'
            //     },
            //     columns: [
            //         { orderable: false }, // Servicio
            //         null,                 // Nombre del correo
            //         null,                 // Póliza
            //         null,                 // Tipo de persona
            //         null,                 // Nombre
            //         null,                 // Identificación
            //         null,                 // Estado
            //         null,                 // Emails a los que se envió
            //         null,                 // Fecha de envío
            //         { orderable: false }   // Acciones
            //     ],
            //     dom: '<"ui grid butonExcel"<"row dt-buttons"<"eight wide left aligned"l><"eight wide right aligned"B>>>' +
            //         '<"row"<"sixteen wide column"tr>>' +
            //         '<"row"<"eight wide left aligned"i><"eight wide right aligned"p>>',
            //     buttons: [{
            //         extend: 'excelHtml5',
            //         title: 'Reporte de colectividad',
            //         exportOptions: {
            //             // Incluir todas las columnas excepto la última (acciones)
            //             columns: [0, 1, 2, 3, 4, 5, 6, 7, 8]
            //         },
            //         className: 'left-button'
            //     }],
            //     language: {
            //         lengthMenu: "Mostrar _MENU_ registros por página",
            //         zeroRecords: "No se encontraron registros",
            //         info: "Mostrando página _PAGE_ de _PAGES_",
            //         infoEmpty: "No hay registros disponibles",
            //         infoFiltered: "(filtrado de _MAX_ registros totales)",
            //         search: "Buscar:",
            //         paginate: {
            //             first: "Primero",
            //             last: "Último",
            //             next: "Siguiente",
            //             previous: "Anterior"
            //         },
            //         select: {
            //             rows: {
            //                 _: "%d filas seleccionadas",
            //                 0: "",
            //             }
            //         }
            //     }
            // });



        });

        $('#reset').click(function() {
            $('form .ui.dropdown').dropdown('clear');

            $.each($('form .datepicker'), function(k, el) {
                $(el).pickadate('picker').clear();
            });

            $('form input[type="text"]').val('');

            $('form input[type="number"]').val('');

            $('#form-filter').submit();
        });
    </script>

    <script>
        $('.poliza_message').popup({
            boundary: 'body',
            content: 'Este campo es númerico',
            position: 'top center',
            lastResort: 'bottom center' // Si no cabe en la posición inicial, se ajusta a esta
        });
    </script>

    <script>
        
        $(document).ready(function() {
               // Función para validar rangos de fechas
            function validateDateFilter(startDate, endDate) {
                if (!startDate || !endDate) {
                    return true; // No validar si alguno está vacío
                }

                // Convertir a objetos Date
                let start = new Date(startDate);
                let end = new Date(endDate);

                // Verificar si la fecha de inicio es mayor que la fecha de fin
                if (start > end) {
                    // Mostrar mensaje de error con SweetAlert y detener la validación
                    Swal.fire({
                        title: 'Error en la selección de fechas',
                        text: `La fecha Hasta no puede ser mayor que la fecha de desde.`,
                        icon: 'error',
                        confirmButtonText: 'Cerrar'
                    });
                    return false;
                }

                return true;
            }

            // Validar fechas cuando se envía el formulario
            $('form#form-filter').submit(function (event) {
                // Validar las fechas de emisión
                if (!validateDateFilter(
                    $('input[name="start_date"]').val(),
                    $('input[name="end_date"]').val(),
                    "Fecha de emisión"
                )) {
                    event.preventDefault();
                    return false;
                }
            });

          

            // Al hacer clic en el botón de acción, se recogen los datos de las filas seleccionadas
            $('.actionButtonLink').on('click', function(e) {
                e.preventDefault();

                loadingMain(true);

                var selectedActivities = [];
                const action_selective = $(this).data('action');

                var selectedNodes = table.rows({
                    selected: true
                }).nodes();

                $(selectedNodes).each(function() {
                    var rowData = $(this).data('policy');
                    selectedActivities.push(rowData);
                });

                caseAction(action_selective, selectedActivities)

            });

            function caseAction(params, police) {
                switch (params) {
                    case 'APLICAR':
                        Swal.fire({
                            title: '¿Está seguro?',
                            text: "¿Desea agregar el beneficio de colectividad?",
                            icon: 'warning',
                            showCancelButton: true,
                            confirmButtonColor: '#000000',
                            cancelButtonColor: '#000000',
                            confirmButtonText: 'Aceptar',
                            cancelButtonText: 'Cancelar'
                        }).then((result) => {
                            if (result.isConfirmed) {
                                addBenefitCollective(police);
                            } else {
                                loadingMain(false);
                            }
                        });
                        break;
                    case 'ELIMINAR':

                        Swal.fire({
                            title: '¿Está seguro?',
                            text: "¿Desea eliminar beneficio de colectividad?",
                            icon: 'warning',
                            showCancelButton: true,
                            confirmButtonColor: '#000000',
                            cancelButtonColor: '#000000',
                            confirmButtonText: 'Aceptar',
                            cancelButtonText: 'Cancelar'
                        }).then((result) => {
                            if (result.isConfirmed) {

                                if (police.length < 2) {
                                    openEmailColectividad(police);

                                } else {
                                    motivoConfirmacion(police);
                                }


                            } else {
                                loadingMain(false);
                            }
                        });

                        break;
                    default:
                        break;
                }

            }

            function openEmailColectividad(police) {

                if (!police[0].activity_id) {
                    console.error("Error: activity_id es undefined.");
                    return;
                }

                // // Actualizar el valor del input oculto
                $('#policy_id_modal').val(police[0].activity_id);

                $('#colectividadModal').modal('refresh').modal({
                    inverted: false,
                    autofocus: false,
                    closable: false,
                    onHidden: function() {
                        loadingMain(false);
                    }
                }).modal('show');
            }

        });
    </script>
    
@endsection
