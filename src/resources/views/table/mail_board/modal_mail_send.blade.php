<div class="ui longer modal">

    <div class="header">
        Correos electrónicos para envío masivo
    </div>

    <div class="scrolling content"> 
        <input type="hidden" id="hiddenQuotationId">

        <!-- Primer campo de correo, sin opción de eliminación -->
        <div id="emailContainer">

        </div>

        <button class="ui button secondary" id="addEmailButton" style="margin: 10px;">Agregar otro correo</button>
    </div>

    <div class="actions">
        <div class="ui button red" id="cancelButton">Cancelar</div>
        <div class="ui primary button" id="sendEmailsButton">Enviar Correos</div>
    </div>
</div>

<script>

        
    $(document).ready(function () {
        
    });
    
    $(document).ready(function() {

        // Agregar un nuevo campo de correo electrónico
        $('#addEmailButton').on('click', function() {
            // Clonar la última fila de campos de correo electrónico
            let newFields = $('#emailContainer .email-input:last').clone();

            // Limpiar los valores de los inputs clonados
            newFields.find('input').val('').removeAttr('readonly');

            // Añadir el botón de eliminación al nuevo campo
            if (!newFields.find('.remove-email').length) {
                newFields.append(`
                    <button class="ui red icon button remove-email">
                        <i class="close icon"></i>
                    </button>
                `);
            }

            // Añadir los nuevos campos clonados al contenedor
            $('#emailContainer').append(newFields);
            $('#emailModal').modal('refresh');
        });

        // Eliminar un campo de correo electrónico, excepto el primero
        $(document).on('click', '.remove-email', function() {
            $(this).closest('.email-input').remove();
        });

        // Enviar los correos electrónicos al hacer clic en el botón de enviar
        $('#sendEmailsButton').on('click', function() {
            let emails = [];
            let isValid = true;
            $('input[name="emails[]"]').each(function() {
                const email = $(this).val().trim();
                const parent = $(this).closest('.email-input');

                // Validar si el correo es válido
                if (validateEmail(email)) {
                    parent.removeClass('error');
                    emails.push(email);
                } else {
                    parent.addClass('error');

                    isValid = false;
                }
            });

            if (!isValid) {
                return;
            }

            masiveEmail(emails);
        });

        // Validación de correos electrónicos en tiempo real
        $(document).on('input', 'input[name="emails[]"]', function() {
            const email = $(this).val().trim();
            const parent = $(this).closest('.email-input');

            if (validateEmail(email) || email === "") {
                parent.removeClass('error');
            } else {
                parent.addClass('error');
            }
        });

        function validateEmail(email) {
            const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return re.test(email);
        }

        function masiveEmail(emails) {
            $('.longer.modal').modal('hide');
            const id = $('#hiddenQuotationId').val(); // Recuperar el ID almacenado

            loadingMain(true); // Mostrar el indicador de carga al iniciar

            $.ajax({
                url: '/service/table/resend_email_mail_board',
                type: 'POST',
                data: {
                    id: id,
                    emails
                },
                success: function(response) {
                    loadingMain(false); // Detener el indicador de carga tras recibir la respuesta
                    // Verificamos si se pudo enviar el correo
                    if (response) {
                        Swal.fire({
                            title: 'Correo enviado',
                            text: "El correo fue enviado exitosamente!",
                            icon: 'success',
                            confirmButtonColor: '#000000', // ← negro
                            confirmButtonText: 'Aceptar'
                        });
                    } else {
                        Swal.fire({
                            title: 'Error',
                            text: "No se pudo enviar el correo de la cotización!",
                            icon: 'error',
                            confirmButtonColor: '#000000', // ← negro
                            confirmButtonText: 'Aceptar'
                        });
                    }
                },
                error: function() {
                    showMessage('Ocurrió un error al enviar el correo');
                    loadingMain(false); // Detener el indicador de carga en caso de error
                }
            });
        }

        // Cerrar el modal al hacer clic en el botón de cancelar
        $('#cancelButton').on('click', function() {
            $('.longer.modal').modal('hide');
        });
    });
</script>
