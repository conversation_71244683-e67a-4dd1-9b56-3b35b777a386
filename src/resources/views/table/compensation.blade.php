@extends('layouts.main')

@section('title', 'Tablero indemnizaciones')

@section('menu')
    @parent
@endsection

@section('content')
    <div class="ui basic segment">
        <div class="content ui form">

            <h2 style="color: black;">Tablero Indemnizaciones</h2>

            <div class="ui three item menu">
                <div class="ui dropdown item dropdown_menu">
                    Proveedores
                    <i class="dropdown icon"></i>
                    <div class="menu">
                        <a class="item {{ $active === 'cuentas_medicas' ? 'active' : '' }}"  href="/tablero/indemnizaciones/cuentas_medicas" id="incapacidad_temporal">Cuentas Médicas</a>
                        <a class="item {{ $active === 'pagos_admnistrativos' ? 'active' : '' }}" href="/tablero/indemnizaciones/pagos_admnistrativos" id="pagos_admnistrativos">Pagos Administrativos</a>
                    </div>
                </div>
                <a href="/tablero/indemnizaciones/reconocimiento_gastos" class="item {{ $active === 'reconocimiento_gastos' ? 'active' : '' }}">Reconocimiento de Gastos</a>
                <div class="ui dropdown item dropdown_menu">
                    Prestaciones Económicas
                    <i class="dropdown icon"></i>
                    <div class="menu">
                       <a class="item {{ $active === 'incapacidad_temporal' ? 'active' : '' }}"  href="/tablero/indemnizaciones/incapacidad_temporal" id="incapacidad_temporal">Incapacidades Temporales</a>
                        <a class="item {{ $active === 'incapacidad_permanente' ? 'active' : '' }}" href="/tablero/indemnizaciones/incapacidad_permanente" id="incapacidad_permanente">Incapacidades Permanentes</a>
                        <a class="item {{ $active === 'controversias_dictamen' ? 'active' : '' }}" {{--href="/tablero/indemnizaciones/{{$id}}/controversias_dictamen"--}} id="controversias_dictamen">Controversias al Dictamen Médico</a>
                        <a class="item {{ $active === 'reaperturas' ? 'active' : '' }}" href="/tablero/indemnizaciones/reaperturas" id="reaperturas">Reaperturas</a>
                        <a class="item {{ $active === 'subrogacion' ? 'active' : '' }}" href="/tablero/indemnizaciones/subrogacion" id="subrogacion">Subrogación</a>
                    </div>
                </div>
            </div>

            <div style="position: absolute; width: 100%">
                <div style="display: flex; justify-content: center">
                    <img style="height: 70vh; margin: auto"
                         src="{{asset('images/mnk.png')}}"
                         alt="">
                </div>
            </div>
        </div>
        @if (isset($success))
            <div class="ui blue message" id="success-message">
                <i class="close icon" id="close-success-message"></i>
                <div class="header">
                    {{ $success }}
                </div>
            </div>
        @endif

        @if (\Session::has('success'))
            <div class="ui green message" id="success-message">
                <i class="close icon" id="close-success-message"></i>
                <div class="header">
                    {!! \Session::get('success') !!}
                </div>
            </div>
        @endif
        @if (\Session::has('info'))
            <div class="ui yellow message" id="info-message">
                <i class="close icon" id="close-success-message"></i>
                <div class="header">
                    {!! \Session::get('info') !!}
                </div>
            </div>
        @endif
        @if (\Session::has('error'))
            @if (\Session::has('error'))
                <div class="ui negative message">
                    <i class="close icon" id="close-success-message" ></i>
                    <div class="header">
                        {!! \Session::get('error') !!}
                    </div>
                </div>
            @endif
        @endif
    </div>

    @yield('content_compensation')

    <style>
        .grayed-input {
            pointer-events: none;
            background-color: #f0f0f0 !important;
            color: #2b2b2b !important;
            border: 1px solid #ddd !important;
            text-transform: none !important;
        }
        .swal2-confirm.btn-black-text {
            background-color: #000 !important;
            color: white !important;
        }

    </style>

    <script>    

        $(document).ready(function () {

            $('.ui.dropdown').dropdown();
            $('.timepicker').pickatime();
            $('.ui.accordion').accordion({
                exclusive: false
            });

            $('#close-success-message').on('click', function () {
                $('#success-message').fadeOut();
            });

        });

    </script>

@endsection
