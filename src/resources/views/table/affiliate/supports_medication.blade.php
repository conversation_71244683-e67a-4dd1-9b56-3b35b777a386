
@extends('table.affiliate')

@section('content_affiliate')
    @include('table/affiliate/menu_medication', ['active' => 'soportes_medicamentos'])
    <form class="ui attached form" id="soportes-medicamentos-form" enctype="multipart/form-data"
          action="{{ secure_url('tablero/afiliado/'.$id.'/soportes_medicamentos/save') }}" method="POST">

        {{ csrf_field() }}

        <input type="hidden" id="case" name="case" value="{{$case ?? ''}}">

        <div class="ui basic segment container">

            @if (session('success'))
                <div class="ui positive message">
                    <div class="header">{{ session('success') }}</div>
                </div>
            @endif

            @if ($errors->any())
                <div class="ui negative message">
                    <div class="header">Se encontraron errores:</div>
                    <ul class="list">
                        @foreach ($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif

            <div class="ui styled fluid accordion">
                <div class="active title">
                    <i class="dropdown icon"></i>
                    Soportes <span style="color: red;" class="required">*</span>
                </div>
                <div class="content active">

                    <div class="ui two column grid">

                        <div class="column">
                            <div class="ui segment">
                                <h5 class="ui dividing header">Orden medicamentos: <span style="color: red;" class="required">*</span> </h5>
                                <div class="ui segment center aligned" id="fileMessageXls">
                                    <i class="huge file pdf outline icon"></i>
                                    <p id="noFileMessage">No has subido un archivo</p>

                                    <label for="medication_order" class="ui secondary icon custom-back-button button">
                                        <i class="upload icon"></i>
                                        <input type="file" id="medication_order" name="medication_order"
                                               accept=".pdf"
                                               style="display: none;" onchange="updateFileName('medication_order','noFileMessage');">
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="column">
                            <div class="ui segment">
                                <h5 class="ui dividing header">Historia clínica: <span style="color: red;" class="required">*</span> </h5>
                                <div class="ui segment center aligned" id="fileMessageXls">
                                    <i class="huge file pdf outline icon"></i>
                                    <p id="noFileMessageDos">No has subido un archivo</p>

                                    <label for="clinical_history" class="ui secondary icon custom-back-button button">
                                        <i class="upload icon"></i>
                                        <input type="file" id="clinical_history" name="clinical_history"
                                               accept=".pdf"
                                               style="display: none;" onchange="updateFileName('clinical_history','noFileMessageDos');">
                                    </label>
                                </div>
                            </div>
                        </div>

                    </div>

                    <br>
                    <div style="display: flex; justify-content: space-between; width: 100%;">
                        <a class="ui primary button" href="{{ secure_url('tablero/afiliado/'.$id.'/solicitud_medicamentos') }}">Atrás</a>

                        <button class="ui primary button">Finalizar</button>
                    </div>
                </div>
            </div>
        </div>
    </form>

    <script>

        function updateFileName(nombreuno,nombredos) {
            const input = document.getElementById(`${nombreuno}`);
            const file = input.files[0];


            if (file) {

                const fileType = file.type;
                if (fileType !== "application/pdf") {
                    Swal.fire({
                        title: 'Advertencia',
                        text: 'Solo se permiten archivos PDF.',
                        icon: 'warning',
                        confirmButtonText: 'Aceptar',
                        confirmButtonColor: '#000',
                    });
                    input.value = ""; // Resetea el campo
                    document.getElementById(`${nombredos}`).innerText = 'No has subido un archivo';
                    document.getElementById(`${nombredos}`).style.color = '';
                } else {
                    document.getElementById(`${nombredos}`).innerText = file.name;
                    document.getElementById(`${nombredos}`).style.color = 'green';
                }

                // document.getElementById(`${nombredos}`).innerText = file.name;
                // document.getElementById(`${nombredos}`).style.color = 'green';
            } else {
                Swal.fire({
                    title: 'Advertencia',
                    text: 'Por favor, sube un archivo PDF.',
                    icon: 'warning',
                    confirmButtonText: 'Aceptar',
                });
                document.getElementById(`${nombredos}`).innerText = 'No has subido un archivo';
                document.getElementById(`${nombredos}`).style.color = '';
            }
        }

        $(document).ready(function() {
            $('#soportes-medicamentos-form').on('submit', function(e) {
                $('.error-message').remove();

                let isValid = true;

                const medicalOrder = $('#medication_order').get(0).files.length;
                const clinicalHistory = $('#clinical_history').get(0).files.length;

                if (medicalOrder === 0) {
                    isValid = false;
                    $('#noFileMessage').after('<div class="ui pointing red basic label error-message">Debes subir la orden médicamentos en formato PDF</div>');
                }

                if (clinicalHistory === 0) {
                    isValid = false;
                    $('#noFileMessageDos').after('<div class="ui pointing red basic label error-message">Debes subir la historia clínica en formato PDF</div>');
                }

                if (!isValid) {
                    e.preventDefault();
                }
            });
        });

    </script>

@endsection