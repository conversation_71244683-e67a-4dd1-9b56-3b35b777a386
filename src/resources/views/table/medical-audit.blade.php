@extends('layouts.main')

@section('title', 'Auditoria Medica')

@section('menu')
    @parent
@endsection

@section('content')
    <div class="ui basic segment">
        <div class="content">
            <h2 style="color: black;">Tablero auditoria médica</h2>
            <form id="filterForm" class="ui form small clearing" method="GET">

                <div class="three fields filters">

                    <div class="field">
                        <label>Número de identificación</label>
                        <input type="text" name="identification_number" class="input_data"
                               value="{{ request()->input('identification_number') ?? '' }}"
                               placeholder="Número de identificación"
                               oninput="this.value = this.value.replace(/[^0-9]/g, '');">
                    </div>

                    <div class="field">
                        <label>Nombre del trabajador</label>
                        <input type="text" name="full_name" class="input_data"
                               placeholder="Nombre del trabajador"
                               value="{{ request()->input('full_name') ?? '' }}" >
                    </div>

                    <div class="field">
                        <label>Número de aviso o caso</label>
                        <input type="text" name="consecutive_gis" class="input_data"
                               value="{{ request()->input('consecutive_gis') ?? '' }}"
                               placeholder="Numero de aviso o caso"
                               oninput="this.value = this.value.replace(/[^0-9AV-]/g, '');">
                    </div>
                </div>

                <div class="three fields">
                    <div class="field">
                        <label>Nombre patrono</label>
                        <input type="text" name="holderPolicy" class="input_data"
                               value="{{ request()->input('holderPolicy') ?? '' }}"
                               placeholder="Nombre patrono">
                    </div>

                    @php
                        $selectedProviders = (array) request()->input('provider', []);
                    @endphp

                    <div class="field">
                        <label for="provider">Proveedor</label>
                        <select class="ui fluid search dropdown" name="provider[]" id="provider" multiple>
                            @foreach ($providers as $provider)
                                <option value="{{ $provider->id }}" {{ in_array($provider->id, $selectedProviders) ? 'selected' : '' }}>
                                    {{ ($provider->clap == 1 ? 'CLAP-' : '') . $provider->name }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                </div>

                <div class="fields" style="margin-top: 1em;">
                    <div class="field">
                        <button class="ui primary button">
                            <i class="search icon"></i>
                            Aplicar filtros
                        </button>
                    </div>

                    <div class="field">
                        <button class="ui secondary button" id="reset" type="button">
                            <i class="undo icon"></i>
                            Limpiar filtros
                        </button>
                    </div>
                </div>
            </form>
            <table class="ui celled sortable striped compact very  table selectable"
                   style="font-size: 0.8em; line-height: 1em;" id="results">
                <thead>
                <tr>
                    <th class="truncate" title="Fecha de gestión">Fecha de gestión</th>
                    <th class="truncate" title="Tipo de identificación del trabajador">Tipo de identificación del trabajador</th>
                    <th class="truncate" title="Número de identificación del trabajador">Número de identificación del trabajador</th>
                    <th title="Nombre trabajador">Nombre trabajador</th>
                    <th title="Es asegurado">Es asegurado?</th>
                    <th title="# Aviso (Siniestro)"># Aviso (Siniestro)</th>
                    <th title="# Caso (Siniestro)"># Caso (Siniestro)</th>
                    <th class="truncate" title="Fecha del accidente o enfermedad">Fecha del accidente o enfermedad</th>
                    <th title="Póliza SORT">Póliza SORT</th>
                    <th title="Nombre del patrono">Nombre del patrono</th>
                    <th title="Tipo de servicio">Tipo de servicio</th>
                    <th title="Tipo de servicio">Estado</th>
                    <th title="Proveedor asignado">Proveedor asignado</th>
                    <th>Ver formulario</th>
                    <th>Resultado gestión</th>
                    <th title="Motivo">Motivo</th>
                    <th title="Acción">Acción</th>
                </tr>
                </thead>
                @php

                @endphp
                @foreach($activities as $activity)
                    @php
                        if ($activity->service_id == \App\Service::SERVICE_MEDICAL_SERVICES_SORT_MNK)
                        {
                            // Obtener el último seguimiento
                            $followUps = optional(optional($activity->medical_services_sort)->followUps());
                            $lastFollowUp = $followUps ? optional($followUps->latest())->first() : null;
                        }
                    @endphp
                    <tr>
                        <td>
                            {{--Fecha de gestión--}}
                            {{ $activity && !empty($activity->created_at)
                              ? ucfirst(strftime('%A %e de %B del %Y', strtotime($activity->created_at)))
                              : 'Fecha no disponible'
                            }}
                        </td>
                        <td>{{ $activity->affiliate->doc_type }}</td>
                        <td>{{ $activity->affiliate->doc_number }}</td>
                        <td>{{ mb_convert_case(mb_strtolower($activity->affiliate->full_name, 'UTF-8'), MB_CASE_TITLE, 'UTF-8') }}</td>
                        <td>Sí</td>

                        {{--numero del caso  numero del aviso --}}
                            @if($activity->service_id == \App\Service::SERVICE_REINTEGRATE_MNK)
                                <td>
                                    {{-- Padre: GIS --}}
                                    {{ $activity->getNumberAuditMedicAv(1) }}
                                </td>
                                <td>
                                    {{-- Padre: GIS --}}
                                    {{ $activity->getNumberAuditMedicCase(1) }}
                                </td>
                            @endif
                            @if($activity->service_id == \App\Service::SERVICE_MEDICAL_SERVICES_SORT_MNK)
                                <td>
                                    {{-- Padre: GIS --}}
                                    {{ $activity->getNumberAuditMedicAv(1) }}
                                </td>
                                <td>
                                    {{-- Padre: GIS --}}
                                    {{ $activity->getNumberAuditMedicCase(1) }}
                                </td>
                            @endif

                            @if($activity->service_id == \App\Service::SERVICE_MEDICAMENTOS_MNK)
                                <td>
                                    {{-- Padre: Prestaciones médicas / GIS --}}
                                    {{ $activity->getNumberAuditMedicAv(2) }}
                                </td>
                                <td>
                                    {{-- Padre: Prestaciones médicas / GIS --}}
                                    {{ $activity->getNumberAuditMedicCase(2) }}
                                </td>
                            @endif

                            @if($activity->service_id == \App\Service::SERVICE_SUPPLIER_MOT_MNK)
                                <td>
                                    {{-- Padre: Prestaciones médicas / GIS --}}
                                    {{ $activity->getNumberAuditMedicAv(2) }}
                                </td>
                                <td>
                                    {{-- Padre: Prestaciones médicas / GIS --}}
                                    {{ $activity->getNumberAuditMedicCase(2) }}
                                </td>
                            @endif

                            @if($activity->service_id == \App\Service::SERVICE_PE_IP_SORT_MNK)
                                <td>
                                    {{-- Padre: GIS --}}
                                    {{ $activity->getNumberAuditMedicAv(1) }}
                                </td>
                                <td>
                                    {{-- Padre: GIS --}}
                                    {{ $activity->getNumberAuditMedicCase(1) }}
                                </td>
                            @endif
                            @if($activity->service_id == \App\Service::SERVICE_PE_IT_SORT_MNK)
                                <td>
                                    {{-- Padre: Una de las dos (Prestaciones médicas o GIS) --}}
                                    @if($activity->parent_activity && $activity->parent_activity->service_id == \App\Service::SERVICE_MEDICAL_SERVICES_SORT_MNK)
                                        {{-- Padre: Prestaciones médicas / GIS --}}
                                        {{ $activity->getNumberAuditMedicAv(2) }}
                                    @endif
                                    @if($activity->parent_activity && $activity->parent_activity->service_id == \App\Service::SERVICE_GIS_SORT_MNK)
                                        {{-- Padre: GIS --}}
                                        {{ $activity->getNumberAuditMedicAv(1) }}
                                    @endif
                                </td>
                                <td>
                                    {{-- Padre: Una de las dos (Prestaciones médicas o GIS) --}}
                                    @if($activity->parent_activity && $activity->parent_activity->service_id == \App\Service::SERVICE_MEDICAL_SERVICES_SORT_MNK)
                                        {{-- Padre: Prestaciones médicas / GIS --}}
                                        {{ $activity->getNumberAuditMedicCase(2) }}
                                    @endif
                                    @if($activity->parent_activity && $activity->parent_activity->service_id == \App\Service::SERVICE_GIS_SORT_MNK)
                                        {{-- Padre: GIS --}}
                                        {{ $activity->getNumberAuditMedicCase(1) }}
                                    @endif
                                </td>
                            @endif

                            @if($activity->service_id == \App\Service::SERVICE_GIS_SORT_MNK)
                                <td>
                                    {{-- Soy GIS --}}
                                    {{ $activity->getNumberAuditMedicAv(0) }}
                                </td>
                                <td>
                                    {{-- Soy GIS --}}
                                    {{ $activity->getNumberAuditMedicCase(0) }}
                                </td>
                            @endif
                            @if($activity->service_id == \App\Service::SERVICE_MEDICAL_SERVICES_SECONDARY_CARE_SORT_MNK)
                                <td></td>
                                <td></td>
                            @endif
                        {{-- Número del caso --}}



                        {{-- Fecha del accidente --}}

                        @if($activity->service_id == \App\Service::SERVICE_REINTEGRATE_MNK)
                            <td>
                                {{-- Padre: GIS --}}
                                {{ $activity->getFormattedDateAccidentAuditMedic(1) }}
                            </td>
                        @endif

                        @if($activity->service_id == \App\Service::SERVICE_MEDICAL_SERVICES_SORT_MNK)
                            <td>
                                {{-- Padre: GIS --}}
                                {{ $activity->getFormattedDateAccidentAuditMedic(1) }}
                            </td>
                        @endif

                        @if($activity->service_id == \App\Service::SERVICE_MEDICAMENTOS_MNK)
                            <td>
                                {{-- Padre: Prestaciones médicas / GIS --}}
                                {{ $activity->getFormattedDateAccidentAuditMedic(2) }}
                            </td>
                        @endif

                        @if($activity->service_id == \App\Service::SERVICE_SUPPLIER_MOT_MNK)
                            <td>
                                {{-- Padre: Prestaciones médicas / GIS --}}
                                {{ $activity->getFormattedDateAccidentAuditMedic(2) }}
                            </td>
                        @endif

                        @if($activity->service_id == \App\Service::SERVICE_PE_IP_SORT_MNK)
                            <td>
                                {{-- Padre: GIS --}}
                                {{ $activity->getFormattedDateAccidentAuditMedic(1) }}
                            </td>
                        @endif

                        @if($activity->service_id == \App\Service::SERVICE_PE_IT_SORT_MNK)
                            <td>
                                {{-- Padre: Una de las dos (Prestaciones médicas o GIS) --}}
                                @if($activity->parent_activity && $activity->parent_activity->service_id == \App\Service::SERVICE_MEDICAL_SERVICES_SORT_MNK)
                                    {{-- Padre: Prestaciones médicas / GIS --}}
                                    {{ $activity->getFormattedDateAccidentAuditMedic(2) }}
                                @endif
                                @if($activity->parent_activity && $activity->parent_activity->service_id == \App\Service::SERVICE_GIS_SORT_MNK)
                                    {{-- Padre: GIS --}}
                                    {{ $activity->getFormattedDateAccidentAuditMedic(1) }}
                                @endif
                            </td>
                        @endif

                        @if($activity->service_id == \App\Service::SERVICE_GIS_SORT_MNK)
                            <td>
                                {{-- Soy GIS --}}
                                {{ $activity->getFormattedDateAccidentAuditMedic(0) }}
                            </td>
                        @endif
                        @if($activity->service_id == \App\Service::SERVICE_MEDICAL_SERVICES_SECONDARY_CARE_SORT_MNK)
                            <td>
                            </td>
                        @endif


                        {{-- # Poliza --}}
                        @if($activity->service_id == \App\Service::SERVICE_REINTEGRATE_MNK)
                            <td>
                                {{-- Padre: GIS / Póliza --}}
                                {{ $activity->getFormattedPolicySortAuditMedic(2) }}
                            </td>
                        @endif

                        @if($activity->service_id == \App\Service::SERVICE_MEDICAL_SERVICES_SORT_MNK)
                            <td>
                                {{-- Padre: GIS / Póliza --}}
                                {{ $activity->getFormattedPolicySortAuditMedic(2) }}
                            </td>
                        @endif

                        @if($activity->service_id == \App\Service::SERVICE_MEDICAMENTOS_MNK)
                            <td>
                                {{-- Padre: Prestaciones médicas / GIS / Póliza --}}
                                {{ $activity->getFormattedPolicySortAuditMedic(3) }}
                            </td>
                        @endif

                        @if($activity->service_id == \App\Service::SERVICE_SUPPLIER_MOT_MNK)
                            <td>
                                {{-- Padre: Prestaciones médicas / GIS / Póliza --}}
                                {{ $activity->getFormattedPolicySortAuditMedic(3) }}
                            </td>
                        @endif

                        @if($activity->service_id == \App\Service::SERVICE_PE_IP_SORT_MNK)
                            <td>
                                {{-- Padre: GIS / Póliza --}}
                                {{ $activity->getFormattedPolicySortAuditMedic(2) }}
                            </td>
                        @endif

                        @if($activity->service_id == \App\Service::SERVICE_PE_IT_SORT_MNK)
                            <td>
                                {{-- Padre: Una de de las dos (Prestaciones médicas o GIS) .... --}}
                                @if($activity->parent_activity && $activity->parent_activity->service_id == \App\Service::SERVICE_MEDICAL_SERVICES_SORT_MNK)
                                    {{-- Padre: Prestaciones médicas / GIS / Póliza --}}
                                    {{ $activity->getFormattedPolicySortAuditMedic(3) }}
                                @endif
                                @if($activity->parent_activity && $activity->parent_activity->service_id == \App\Service::SERVICE_GIS_SORT_MNK)
                                    {{-- Padre: GIS / Póliza --}}
                                    {{ $activity->getFormattedPolicySortAuditMedic(2) }}
                                @endif
                            </td>
                        @endif

                        @if($activity->service_id == \App\Service::SERVICE_GIS_SORT_MNK)
                            <td>
                                {{-- Padre: Póliza --}}
                                {{ $activity->getFormattedPolicySortAuditMedic(1) }}
                            </td>
                        @endif
                        @if($activity->service_id == \App\Service::SERVICE_MEDICAL_SERVICES_SECONDARY_CARE_SORT_MNK)
                            <td>
                            </td>
                        @endif

                        {{-- Nombre del patrono / tomador --}}
                        @if($activity->service_id == \App\Service::SERVICE_REINTEGRATE_MNK)
                            <td>
                                {{-- Padre: GIS / Póliza / Cotización --}}
                                {{ $activity->getAffiliateNameAuditMedic(3) }}
                            </td>
                        @endif

                        @if($activity->service_id == \App\Service::SERVICE_MEDICAL_SERVICES_SORT_MNK)
                            <td>
                                {{-- Padre: GIS / Póliza / Cotización --}}
                                {{ $activity->getAffiliateNameAuditMedic(3) }}
                            </td>
                        @endif

                        @if($activity->service_id == \App\Service::SERVICE_MEDICAMENTOS_MNK)
                            <td>
                                {{-- Padre: Prestaciones médicas / GIS / Póliza / Cotización --}}
                                {{ $activity->getAffiliateNameAuditMedic(4) }}
                            </td>
                        @endif

                        @if($activity->service_id == \App\Service::SERVICE_SUPPLIER_MOT_MNK)
                            <td>
                                {{-- Padre: Prestaciones médicas / GIS / Póliza / Cotización --}}
                                {{ $activity->getAffiliateNameAuditMedic(4) }}
                            </td>
                        @endif

                        @if($activity->service_id == \App\Service::SERVICE_PE_IP_SORT_MNK)
                            <td>
                                {{-- Padre: GIS / Póliza / Cotización --}}
                                {{ $activity->getAffiliateNameAuditMedic(3) }}
                            </td>
                        @endif

                        @if($activity->service_id == \App\Service::SERVICE_PE_IT_SORT_MNK)
                            <td>
                                {{-- Padre: Una de las dos (Prestaciones médicas o GIS) .... --}}
                                @if($activity->parent_activity && $activity->parent_activity->service_id == \App\Service::SERVICE_MEDICAL_SERVICES_SORT_MNK)
                                    {{-- Padre: Prestaciones médicas / GIS / Póliza / Cotización --}}
                                    {{ $activity->getAffiliateNameAuditMedic(4) }}
                                @endif
                                @if($activity->parent_activity && $activity->parent_activity->service_id == \App\Service::SERVICE_GIS_SORT_MNK)
                                    {{-- Padre: GIS / Póliza / Cotización --}}
                                    {{ $activity->getAffiliateNameAuditMedic(3) }}
                                @endif
                            </td>
                        @endif

                        @if($activity->service_id == \App\Service::SERVICE_GIS_SORT_MNK)
                            <td>
                                {{-- Padre: Póliza / Cotización --}}
                                {{ $activity->getAffiliateNameAuditMedic(2) }}
                            </td>
                        @endif
                        @if($activity->service_id == \App\Service::SERVICE_MEDICAL_SERVICES_SECONDARY_CARE_SORT_MNK)
                            <td>
                            </td>
                        @endif


                        {{--tipo de servicio--}}
                        @if($activity->service_id == \App\Service::SERVICE_REINTEGRATE_MNK)
                            <td>Reintegro</td>
                        @endif
                        @if($activity->service_id == \App\Service::SERVICE_MEDICAL_SERVICES_SORT_MNK)
                            <td>Prestación medica</td>
                        @endif
                        @if($activity->service_id == \App\Service::SERVICE_MEDICAMENTOS_MNK)
                            <td>Medicamentos</td>
                        @endif
                        @if($activity->service_id == \App\Service::SERVICE_SUPPLIER_MOT_MNK)
                            <td>Insumos y/ mot</td>
                        @endif
                        @if($activity->service_id == \App\Service::SERVICE_PE_IT_SORT_MNK)
                            <td>PE-Incapacidad temporal</td>
                        @endif
                        @if($activity->service_id == \App\Service::SERVICE_PE_IP_SORT_MNK)
                            <td>PE-Incapacidad permanente</td>
                        @endif
                        @if($activity->service_id == \App\Service::SERVICE_GIS_SORT_MNK)
                            <td>Gestión integral del siniestro - SORT</td>
                        @endif
                        @if($activity->service_id == \App\Service::SERVICE_MEDICAL_SERVICES_SECONDARY_CARE_SORT_MNK)
                            <td>Prestación médica SORT - atención secundaria</td>
                        @endif
                        {{--Estado--}}
                        <td>{{ucfirst(mb_strtolower($activity->state->name))}}</td>
                        {{--Proveedor asignado--}}
                        @if($activity->service_id == \App\Service::SERVICE_REINTEGRATE_MNK)
                            <td></td>
                        @endif
                        @if($activity->service_id == \App\Service::SERVICE_MEDICAL_SERVICES_SORT_MNK)
                            {{--Solo mostrarse si es diferente a los siguientes estados--}}
                            @if($activity->state_id != \App\States\StateMedicalServiceSort::REGISTRADO && $activity->state_id != \App\States\StateMedicalServiceSort::SERVICIO_ANULADO)
                                <td>
                                    {{ (optional(optional($activity->medical_services_sort)->provider())->clap == 1 ? 'CLAP-' : '') . optional(optional($activity->medical_services_sort)->provider())->name ?? '' }}
                                    <a class="ui clickable-link changeProvider"
                                       href="#" data-id="{{ $activity->id }}"
                                       data-url="/servicio/{{ $activity->id }}/cambiar_proveedor">
                                        <i class="retweet orange icon"></i></a>
                                </td>
                            @endif
                        @endif
                        @if($activity->service_id == \App\Service::SERVICE_MEDICAMENTOS_MNK)
                            <td>{{  (optional(optional($activity->medication)->provider())->clap == 1 ? 'CLAP-' : '').optional(optional($activity->medication)->provider())->name ?? ''}}</td>
                        @endif
                        @if($activity->service_id == \App\Service::SERVICE_SUPPLIER_MOT_MNK)
                            <td>{{  (optional(optional($activity->supplies_mot)->provider())->clap == 1 ? 'CLAP-' : '').optional(optional($activity->supplies_mot)->provider())->name ?? ''}}</td>
                        @endif
                        @if($activity->service_id == \App\Service::SERVICE_PE_IT_SORT_MNK)
                            <td>{{  (optional(optional($activity->peItSort)->provider())->clap == 1 ? 'CLAP-' : '').optional(optional($activity->peItSort)->provider())->name ?? ''}}</td>
                        @endif
                        @if($activity->service_id == \App\Service::SERVICE_PE_IP_SORT_MNK)
                            <td></td>
                        @endif
                        @if($activity->service_id == \App\Service::SERVICE_GIS_SORT_MNK)
                            <td>{{  (optional(optional($activity->gis_sort)->provider())->clap == 1 ? 'CLAP-' : '').optional(optional($activity->gis_sort)->provider())->name ?? ''}}</td>
                        @endif
                        @if($activity->service_id == \App\Service::SERVICE_MEDICAL_SERVICES_SECONDARY_CARE_SORT_MNK)
                            <td>{{ (optional(optional($activity->medical_services_secondary_care_sort)->provider())->clap == 1 ? 'CLAP-' : '').optional(optional($activity->medical_services_secondary_care_sort)->provider())->name ?? ''}}</td>
                        @endif
                        {{--ver formulario--}}
                        @if($activity->service_id == \App\Service::SERVICE_REINTEGRATE_MNK)
                            <td> <a  class="ui clickable-link ver-poliza"
                                     href="/servicio/{{ $activity->id }}/reintegrate">
                                    <i class="external eye icon"></i></a>
                            </td>
                        @endif
                        @if($activity->service_id == \App\Service::SERVICE_MEDICAL_SERVICES_SORT_MNK)
                            <td> <a  class="ui clickable-link ver-poliza"
                                href="/servicio/{{ $activity->id }}/medical_services">
                                <i class="external eye icon"></i></a>
                            </td>
                        @endif
                        @if($activity->service_id == \App\Service::SERVICE_MEDICAMENTOS_MNK)
                            <td> <a  class="ui clickable-link ver-poliza"
                                     href="/servicio/{{ $activity->id }}/medication_services">
                                    <i class="external eye icon"></i></a>
                            </td>
                        @endif
                        @if($activity->service_id == \App\Service::SERVICE_SUPPLIER_MOT_MNK)
                            <td> <a  class="ui clickable-link ver-poliza"
                                     href="/servicio/{{ $activity->id }}/supplies_mot">
                                    <i class="external eye icon"></i></a>
                            </td>
                        @endif
                        @if($activity->service_id == \App\Service::SERVICE_PE_IT_SORT_MNK)
                            <td> <a  class="ui clickable-link ver-poliza"
                                     href="/servicio/{{ $activity->id }}/pe_it_sort">
                                    <i class="external eye icon"></i></a>
                            </td>
                        @endif
                        @if($activity->service_id == \App\Service::SERVICE_PE_IP_SORT_MNK)
                            <td> <a  class="ui clickable-link ver-poliza"
                                     href="/servicio/{{ $activity->id }}/pe_ip_sort">
                                    <i class="external eye icon"></i></a>
                            </td>
                        @endif
                        @if($activity->service_id == \App\Service::SERVICE_GIS_SORT_MNK)
                            <td> <a  class="ui clickable-link ver-poliza"
                                     href="/servicio/{{ $activity->id }}/gis_sort">
                                    <i class="external eye icon"></i></a>
                            </td>
                        @endif
                        @if($activity->service_id == \App\Service::SERVICE_MEDICAL_SERVICES_SECONDARY_CARE_SORT_MNK)
                            <td> <a  class="ui clickable-link ver-poliza"
                                     href="/servicio/{{ $activity->id }}/medical_services_secondary_care">
                                    <i class="external eye icon"></i></a>
                            </td>
                        @endif
                        {{--resultado de la gestion--}}
                        <td>
                            {{--controversia--}}
                            @if($activity->service_id == \App\Service::SERVICE_GIS_SORT_MNK && $activity->state_id == \App\States\StateGis::CASO_EN_TRAMITE_JUDICIAL)

                                @if($activity->gis_sort->controversy_result == '1' ||  $activity->gis_sort->controversy_result == '3' )
                                    <form method="POST" action="/services/gis/approveControversies/{{ $activity->id }}" class="ui form aprobar-controversia" style="display: inline;">
                                        {{ csrf_field() }}
                                        <button type="submit" class="ui button" style="background: none; border: none; padding: 0;">
                                            <i class="check green icon"></i>
                                        </button>
                                    </form>

                                @elseif($activity->gis_sort->controversy_result == '2' ||  $activity->gis_sort->controversy_result == '4' )
                                    <form method="POST" action="/services/gis/rejectControversyResponse/{{ $activity->id }}" class="ui form rechazar-controversia" style="display: inline;">
                                        {{ csrf_field() }}
                                        <button type="submit" class="ui button" style="background: none; border: none; padding: 0;">
                                            <i class="close red icon"></i>
                                        </button>
                                    </form>
                                @else

                                    <a class="ui clickable-link link-gis"
                                       href="{{secure_url('servicio/'.$activity->id.'/gis_sort')}}" data-id="{{ $activity->id }}"
                                       data-url="{{secure_url('servicio/'.$activity->id.'/gis_sort')}}">
                                        <i class="arrow alternate circle right outline blue icon"></i></a>
                                @endif

                            @endif
                            {{--reapertura--}}
                            @if($activity->service_id == \App\Service::SERVICE_GIS_SORT_MNK && $activity->state_id == \App\States\StateGis::CASO_EN_REAPERTURA_CASO)
                                <form method="POST" action="/services/gis/approveCaseReopening/{{ $activity->id }}" class="ui form aprobar-reapertura" style="display: inline;">
                                    {{ csrf_field() }}
                                    <button type="submit" class="ui button" style="background: none; border: none; padding: 0;">
                                        <i class="check green icon"></i>
                                    </button>
                                </form>
                                <form method="POST" action="/services/gis/reopeningRejectedCase/{{$activity->id}}" class="ui form rechazar-reapertura" style="display: inline;">
                                    {{ csrf_field() }}
                                    <button type="submit" class="ui button" style="background: none; border: none; padding: 0;">
                                        <i class="close red icon"></i>
                                    </button>
                                </form>
                            @endif

                            {{--Prestaciones Medicas--}}
                            @if($activity->service_id == \App\Service::SERVICE_MEDICAL_SERVICES_SORT_MNK && $activity->state_id == \App\States\StateMedicalServiceSort::REGISTRADO)
                                <form method="POST" action="/service/{{ $activity->id }}/aprobacion_servicio" class="ui form aprobar" style="display: inline;">
                                    {{ csrf_field() }}
                                    <button type="submit" class="ui button" style="background: none; border: none; padding: 0;">
                                        <i class="check icon"></i>
                                    </button>
                                </form>
                            @endif
                            @if($activity->service_id == \App\Service::SERVICE_MEDICAL_SERVICES_SORT_MNK && $activity->state_id == \App\States\StateMedicalServiceSort::REGISTRADO)
                                <a class="ui clickable-link anular" href="#" data-id="{{ $activity->id }}" data-url="/service/{{ $activity->id }}/action_cancel_service">
                                    <i class="ban icon"></i>
                                </a>
                            @endif
                            @if($activity->state_id == \App\States\StateMedicalServiceSort::VALORACION_ASIGNADA_A_PROVEEDOR)
                                <a class="ui clickable-link reportarNegacion" href="#" data-id="{{ $activity->id }}" data-url="/service/{{$activity->id}}/negacion_servicio">
                                    <i class="close red icon"></i>
                                </a>
                            @endif
                            @if($activity->service_id == \App\Service::SERVICE_MEDICAL_SERVICES_SORT_MNK && $activity->state_id !== \App\States\StateMedicalServiceSort::REGISTRADO)
                                <a class="ui clickable-link reportAuditMnk"
                                   href="#" data-id="{{ $activity->id }}"
                                   data-url="/servicio/{{ $activity->id }}/reportar_prestaciones_mnk">
                                    <i class="bullhorn orange icon"></i></a>
                            @endif
                            @if($activity->service_id == \App\Service::SERVICE_MEDICAL_SERVICES_SORT_MNK && $lastFollowUp != null && optional($lastFollowUp)->referral_specialist == 1)
                                <a href="#" class="ui clickable-link referralSpecialist" data-id="{{ $activity->id }}" data-url = "/servicio/{{$activity->id }}/remision_especialista">
                                    <i class="user md icon"></i>
                                </a>
                            @endif
                            {{--Medicamentos--}}
                            @if($activity->service_id == \App\Service::SERVICE_MEDICAMENTOS_MNK && $activity->state_id == \App\States\StateMedicationServiceSort::SOLICITUD_MEDICAMENTOS_EN_REVISION_POR_AUDITORIA_MEDICA)
                                <form method="POST" action="/servicio/{{ $activity->id }}/medication_services/aprobar_servicio_medicamentos" class="ui form aprobar" style="display: inline;">
                                    {{ csrf_field() }}
                                    <button type="submit" class="ui button" style="background: none; border: none; padding: 0;">
                                        <i class="check icon"></i>
                                    </button>
                                </form>
                            @endif
                            @if($activity->service_id == \App\Service::SERVICE_MEDICAMENTOS_MNK && $activity->state_id == \App\States\StateMedicationServiceSort::REGISTRADO)
                                <a  class="ui clickable-link anular"
                                    href="#" data-id="{{ $activity->id }}" data-url="/servicio/{{ $activity->id }}/medication_services/anular_servicio_medicamentos">
                                    <i class="ban icon"></i></a>
                            @endif
                            @if($activity->state_id == \App\States\StateMedicationServiceSort::SOLICITUD_MEDICAMENTOS_EN_REVISION_POR_AUDITORIA_MEDICA)
                                <a class="ui clickable-link reportarNegacion" href="#" data-id="{{ $activity->id }}" data-url="/servicio/{{$activity->id}}/medication_services/reportar_negacion_medicamentos">
                                    <i class="close red icon"></i>
                                </a>
                            @endif
                            @if($activity->service_id == \App\Service::SERVICE_MEDICAMENTOS_MNK && $activity->state_id !== \App\States\StateMedicationServiceSort::REGISTRADO)
                                <a class="ui clickable-link reportAuditMnk"
                                   href="#" data-id="{{ $activity->id }}"
                                   data-url="/servicio/{{ $activity->id }}/medication_services/auditoria_mnk_medicamentos">
                                    <i class="bullhorn orange icon"></i></a>
                            @endif
                            {{--Insumos y/o MOT--}}
                            @if($activity->service_id == \App\Service::SERVICE_SUPPLIER_MOT_MNK && $activity->state_id == \App\States\StateSuppliesMot::REGISTRADO)
                                <a  class="ui clickable-link anular"
                                    href="#"  data-url="/servicio/{{ $activity->id }}/anular_insumos">
                                    <i class="ban icon"></i></a>
                            @endif
                            @if($activity->state_id == \App\States\StateSuppliesMot::SOLICITUD_INSUMOS_REVISION_AUDITORIA_MEDICA)
                                <a class="ui clickable-link reportarNegacion" href="#" data-id="{{ $activity->id }}" data-url="/servicio/{{$activity->id}}/reportar_negacion_insumos">
                                    <i class="close red icon"></i>
                                </a>
                            @endif
                            @if($activity->service_id == \App\Service::SERVICE_SUPPLIER_MOT_MNK && $activity->state_id == \App\States\StateSuppliesMot::SOLICITUD_INSUMOS_REVISION_AUDITORIA_MEDICA)
                                <form method="POST" action="/servicio/{{ $activity->id }}/aprobar_insumos" class="ui form aprobar" style="display: inline;">
                                    {{ csrf_field() }}
                                    <button type="submit" class="ui button" style="background: none; border: none; padding: 0;">
                                        <i class="check icon"></i>
                                    </button>
                                </form>
                            @endif
                            @if($activity->service_id == \App\Service::SERVICE_SUPPLIER_MOT_MNK && $activity->state_id !== \App\States\StateSuppliesMot::REGISTRADO)
                                <a class="ui clickable-link reportAuditMnk"
                                   href="#" data-id="{{ $activity->id }}"
                                   data-url="/servicio/{{ $activity->id }}/auditoria_mnk_medicamentos_insumos">
                                    <i class="bullhorn orange icon"></i></a>
                            @endif
                            {{--Pe IT--}}
                            @if($activity->service_id == \App\Service::SERVICE_PE_IT_SORT_MNK && $activity->state_id == \App\States\StatePeItSort::EN_VALIDACION_DE_PAGOS)
                                <form method="POST" action="/servicio/{{ $activity->id }}/pe_it_sort/reportar_pago_fracciones_mensual" class="ui form aprobarFractions" style="display: inline;">
                                    {{ csrf_field() }}
                                    <button type="submit" class="ui button" style="background: none; border: none; padding: 0;">
                                        <i class="check green icon"></i>
                                    </button>
                                </form>
                            @endif
                            {{--Pe IT--}}
                            @if($activity->service_id == \App\Service::SERVICE_PE_IT_SORT_MNK && $activity->state_id == \App\States\StatePeItSort::EN_VALIDACION_DE_PAGOS || $activity->state_id == \App\States\StatePeItSort::VALIDACION_DE_INCAPACIDAD )
                                <a  class="ui clickable-link anularPeit"
                                    href="#" data-id="{{ $activity->id }}" data-url="/servicio/{{ $activity->id }}/pe_it_sort/rechazar_it">
                                    <i class="close red icon"></i></a>
                            @endif
                            {{--Pe IP--}}
                            @if($activity->service_id == \App\Service::SERVICE_PE_IP_SORT_MNK && $activity->state_id == \App\States\StatePeIpSort::EN_VALIDACION_DE_PAGOS || $activity->state_id == \App\States\StatePeIpSort::VALIDACION_DE_IP )
                                <form method="POST" action="/servicio/{{ $activity->id }}/pe_ip_sort/rechazar_ip" class="ui clickable-link anularPeip" style="display: inline;">
                                    {{ csrf_field() }}
                                    <button type="submit" class="ui button" style="background: none; border: none; padding: 0;">
                                        <i class="ban icon"></i>
                                    </button>
                                </form>
                            @endif

                            {{--INICIO BOTONES APROBAR RECHAZAR GIS--}}
                            @if($activity->service_id == \App\Service::SERVICE_GIS_SORT_MNK && $activity->state_id == \App\States\StateGis::CASO_REPORTADO_VALIDACION_ORIGEN)
                                <form method="POST" action="/services/gis/requestExtraInformation/{{ $activity->id }}" class="ui form informacion-extra" style="display: inline;">
                                    {{ csrf_field() }}
                                    <button type="submit" class="ui button" style="background: none; border: none; padding: 0;">
                                        <i class="info circle icon"></i>
                                    </button>
                                </form>
                            @endif
                            @if($activity->service_id == \App\Service::SERVICE_GIS_SORT_MNK && $activity->state_id == \App\States\StateGis::ORIGEN_REPORTADO_EN_AUDITORIA_MEDICA)
                                <form method="POST" action="/services/gis/reportAdministrativeAuditApproval/{{ $activity->id }}" class="ui form aprobar-origen" style="display: inline;">
                                    {{ csrf_field() }}
                                    <button type="submit" class="ui button" style="background: none; border: none; padding: 0;">
                                        <i class="check green icon"></i>
                                    </button>
                                </form>
                                <form method="POST" action="/services/gis/reportAdministrativeAuditRejection/{{ $activity->id }}" class="ui form rechazar-origen" style="display: inline;">
                                    {{ csrf_field() }}
                                    <button type="submit" class="ui button" style="background: none; border: none; padding: 0;">
                                        <i class="close red icon"></i>
                                    </button>
                                </form>
                            @endif
                            @if($activity->service_id == \App\Service::SERVICE_GIS_SORT_MNK && $activity->state_id == \App\States\StateGis::CASO_PCG_CALIFICADO_PENDIENTE_AUDITORIA_MEDICA)
                                <form method="POST" action="/services/gis/ApproveAuditRejection/{{ $activity->id }}" class="ui form aprobar-pcg" style="display: inline;">
                                    {{ csrf_field() }}
                                    <button type="submit" class="ui button" style="background: none; border: none; padding: 0;">
                                        <i class="check green icon"></i>
                                    </button>
                                </form>
                                <form method="POST" action="/services/gis/reportAuditRejection/{{ $activity->id }}" class="ui form rechazar-pcg" style="display: inline;">
                                    {{ csrf_field() }}
                                    <button type="submit" class="ui button" style="background: none; border: none; padding: 0;">
                                        <i class="close red icon"></i>
                                    </button>
                                </form>
                            @endif
                            @if($activity->service_id == \App\Service::SERVICE_GIS_SORT_MNK && $activity->state_id == \App\States\StateGis::MUERTE_REPORTADA_EN_AUDITORIA_MEDICA)
                                <form method="POST" action="/services/gis/reportMemberDeathApproval/{{ $activity->id }}" class="ui form aprobar-muerte" style="display: inline;">
                                    {{ csrf_field() }}
                                    <button type="submit" class="ui button" style="background: none; border: none; padding: 0;">
                                        <i class="check green icon"></i>
                                    </button>
                                </form>
                                <form method="POST" action="/services/gis/reportMemberDeathRejection/{{ $activity->id }}" class="ui form rechazar-muerte" style="display: inline;">
                                    {{ csrf_field() }}
                                    <button type="submit" class="ui button" style="background: none; border: none; padding: 0;">
                                        <i class="close red icon"></i>
                                    </button>
                                </form>
                            @endif
                            @if($activity->service_id == \App\Service::SERVICE_GIS_SORT_MNK && $activity->state_id == \App\States\StateGis::EXCEPCION_CASO_REPORTADO_EN_AUDITORIA_MEDICA)
                                <form method="POST" action="/services/gis/reportAcceptedCaseExceptionApprobal/{{ $activity->id }}" class="ui form aprobar-caso-aceptado" style="display: inline;">
                                    {{ csrf_field() }}
                                    <button type="submit" class="ui button" style="background: none; border: none; padding: 0;">
                                        <i class="check green icon"></i>
                                    </button>
                                </form>
                                <form method="POST" action="/services/gis/ReportCaseExceptionRejected/{{ $activity->id }}" class="ui form rechazar-caso-aceptado" style="display: inline;">
                                    {{ csrf_field() }}
                                    <button type="submit" class="ui button" style="background: none; border: none; padding: 0;">
                                        <i class="close red icon"></i>
                                    </button>
                                </form>
                            @endif
                            @if($activity->service_id == \App\Service::SERVICE_GIS_SORT_MNK && $activity->state_id == \App\States\StateGis::CASO_EN_REAPERTURA_CASO)
                                <form method="POST" action="/services/gis/reportAdministrativeCaseReopening/{{ $activity->id }}" class="ui form reportar_reapertura_caso_administrativa" style="display: inline;">
                                    {{ csrf_field() }}
                                    <button type="submit" class="ui button" style="background: none; border: none; padding: 0;">
                                        <i class="orange inbox icon"></i>
                                    </button>
                                </form>
                            @endif
                            {{--FIN GIS--}}
                            {{--Prestaciones Medicas - atención secundaria--}}
                            @if($activity->service_id == \App\Service::SERVICE_MEDICAL_SERVICES_SECONDARY_CARE_SORT_MNK && $activity->state_id == \App\States\StateMedicalServiceSecondarySort::REGISTRADO)
                                <form method="POST" action="/service/{{ $activity->id }}/aprobacion_servicio_service_secondary" class="ui form aprobar" style="display: inline;">
                                    {{ csrf_field() }}
                                    <button type="submit" class="ui button" style="background: none; border: none; padding: 0;">
                                        <i class="check icon"></i>
                                    </button>
                                </form>
                            @endif
                            @if($activity->service_id == \App\Service::SERVICE_MEDICAL_SERVICES_SECONDARY_CARE_SORT_MNK && $activity->state_id == \App\States\StateMedicalServiceSecondarySort::REGISTRADO)
                                <a class="ui clickable-link anular" href="#" data-id="{{ $activity->id }}" data-url="/service/{{ $activity->id }}/action_cancel_service_secondary">
                                    <i class="ban icon"></i>
                                </a>
                            @endif
                            @if($activity->service_id == \App\Service::SERVICE_MEDICAL_SERVICES_SECONDARY_CARE_SORT_MNK && $activity->state_id == \App\States\StateMedicalServiceSort::VALORACION_ASIGNADA_A_PROVEEDOR)
                                <a class="ui clickable-link reportarNegacion" href="#" data-id="{{ $activity->id }}" data-url="/service/{{$activity->id}}/negacion_servicio_service_secondary">
                                    <i class="close red icon"></i>
                                </a>
                            @endif
                            @if($activity->service_id == \App\Service::SERVICE_MEDICAL_SERVICES_SECONDARY_CARE_SORT_MNK && $activity->state_id !== \App\States\StateMedicalServiceSort::REGISTRADO)
                                <a class="ui clickable-link reportAuditMnk"
                                   href="#" data-id="{{ $activity->id }}"
                                   data-url="/servicio/{{ $activity->id }}/reportar_prestaciones_mnk_service_secondary">
                                    <i class="bullhorn orange icon"></i></a>
                            @endif

                        </td>
                        <td>
                            @if(
                              $activity->state_id == \App\States\StateMedicalServiceSort::SERVICIO_ANULADO ||
                              $activity->state_id == \App\States\StateMedicationServiceSort::SERVICIO_NEGADO ||
                              $activity->state_id == \App\States\StateSuppliesMot::SERVICIO_NEGADO)
                                @switch($activity->service_id)
                                    @case(\App\Service::SERVICE_MEDICAL_SERVICES_SORT_MNK)
                                        {{ $activity->medical_services_sort->reason_cancel }}
                                        @break

                                    @case(\App\Service::SERVICE_MEDICAMENTOS_MNK)
                                        {{ $activity->medication->reason_cancel }}
                                        @break

                                    @case(\App\Service::SERVICE_SUPPLIER_MOT_MNK)
                                        {{ $activity->supplies_mot->reason_cancel }}
                                        @break
                                @endswitch
                            @endif
                        </td>
                        {{--acciones--}}
                        <td class="center aligned">
                             @if($activity->service_id == \App\Service::SERVICE_MEDICAL_SERVICES_SECONDARY_CARE_SORT_MNK)
                                        @if($activity->state_id == \App\States\StateMedicalServiceSecondarySort::VALORACION_REALIZADA)

                                            <div class="ui grid centered">
                                                @php
                                                    // Obtener el último followUp
                                                    $followUps = optional(optional($activity->medical_services_secondary_care_sort)->followUps());
                                                    $lastFollowUp = $followUps ? $followUps->latest()->first() : null;
                                                @endphp
                                                @if($lastFollowUp != null && optional($lastFollowUp)->days_of_incapacity > 0)
                                                    <div class="column" style="margin-right: 15px;">
                                                        <a href="#" class="ui clickable-link medicalp pop" onclick="downloadDisabilityAs({{$activity->id ?? ''}});" data-content="Incapacidad">
                                                            <i class="file medical icon"></i>
                                                        </a>
                                                    </div>
                                                @endif

                                                @if($lastFollowUp && optional($lastFollowUp)->required_hospitalization == 1)
                                                    <div class="column" style="margin-right: 15px;">
                                                        <form  id="hospitalizacion-form-{{$activity->id }}" action="/servicio/{{$activity->id }}/emitir_hospitalizacion" method="POST">
                                                            {{ csrf_field() }}
                                                            <a href="#" class="ui clickable-link hospitalizacionp pop" onclick="event.preventDefault(); document.getElementById('hospitalizacion-form-{{$activity->id }}').submit();" data-content="Hospitalización">
                                                                <i class="hospital icon"></i>
                                                            </a>
                                                        </form>
                                                    </div>
                                                @endif

                                                @if($lastFollowUp && optional($lastFollowUp)->origin_diagnosis)
                                                    <div class="column" style="margin-right: 15px;" >
                                                        <form id="imagen-diagnostica-form-{{$activity->id }}" action="/servicio/{{$activity->id }}/imagen_diagnostica" method="POST">
                                                            {{ csrf_field() }}
                                                            <a href="#" class="ui clickable-link diagnosticap pop" onclick="event.preventDefault(); document.getElementById('imagen-diagnostica-form-{{$activity->id }}').submit();" data-content="Orden Imágenes Dx">
                                                                <i class="image icon"></i>
                                                            </a>
                                                        </form>
                                                    </div>
                                                @endif

                                                @if($lastFollowUp && optional($lastFollowUp)->origin_diagnosis_referral_specialist)
                                                    <div class="column" style="margin-right: 15px;">
                                                        <form id="remision-especialista-form-{{$activity->id }}" action="/servicio/{{$activity->id }}/remision_especialista" method="POST">
                                                            {{ csrf_field() }}
                                                            <a href="#" class="ui clickable-link especialistap pop" onclick="event.preventDefault(); document.getElementById('remision-especialista-form-{{$activity->id }}').submit();" data-content="Remisión a esp">
                                                                <i class="user md icon"></i>
                                                            </a>
                                                        </form>
                                                    </div>
                                                @endif

                                                @if($lastFollowUp )
                                                    <div class="column" style="margin-right: 15px;">
                                                        <form id="formula-medicamentos-form-{{$activity->id }}" action="/servicio/{{$activity->id}}/formula_medicamentos" method="POST">
                                                            {{ csrf_field() }}
                                                            <a href="#" class="ui clickable-link formulap pop" onclick="event.preventDefault(); document.getElementById('formula-medicamentos-form-{{$activity->id }}').submit();" data-content="Formulación">
                                                                <i class="clipboard outline icon"></i>
                                                            </a>
                                                        </form>
                                                    </div>
                                                @endif

                                                @if($lastFollowUp && optional($lastFollowUp)->diagnosis_origin_controlled_medication != '')
                                                    <div class="column" style="margin-right: 15px;">
                                                        <form id="formula-medicamentos-controlados-form-{{$activity->id }}" action="/servicio/{{$activity->id }}/formula_medicamentos_controlados" method="POST">
                                                            {{ csrf_field() }}
                                                            <a href="#" class="ui clickable-link controladosp pop" onclick="event.preventDefault(); document.getElementById('formula-medicamentos-controlados-form-{{$activity->id }}').submit();" data-content="Form. Controlada">
                                                                <i class="medkit icon"></i>
                                                            </a>
                                                        </form>
                                                    </div>
                                                @endif

                                            </div>
                                        @endif
                             @endif
                             @if($activity->service_id == \App\Service::SERVICE_MEDICAL_SERVICES_SORT_MNK)
                                   @if($activity->state_id == \App\States\StateMedicalServiceSort::VALORACION_REALIZADA)

                                            <div class="ui grid centered">
                                                @php
                                                    // Obtener el último followUp
                                                    $followUps = optional(optional($activity->medical_services_sort)->followUps());
                                                    $lastFollowUp = $followUps ? $followUps->latest()->first() : null;
                                                @endphp
                                                @if($lastFollowUp != null && optional($lastFollowUp)->days_of_incapacity > 0)
                                                    <div class="column" style="margin-right: 15px;">
                                                        <a href="#" class="ui clickable-link medicalp pop" onclick="actionDisabilityAp({{$activity->id ?? ''}});" data-content="Incapacidad">
                                                            <i class="file medical icon"></i>
                                                        </a>
                                                    </div>
                                                @endif

                                                @if($lastFollowUp && optional($lastFollowUp)->required_hospitalization == 1)
                                                        <div class="column" style="margin-right: 15px;">
                                                            <a href="#" class="ui clickable-link hospitalizacionp pop" onclick="actionHospitalization({{$activity->id ?? ''}});" data-content="Hospitalización">
                                                                <i class="hospital icon"></i>
                                                            </a>
                                                    </div>
                                                @endif

                                                @if($lastFollowUp && optional($lastFollowUp)->origin_diagnosis)
                                                        <div class="column" style="margin-right: 15px;" >
                                                            <a href="#" class="ui clickable-link diagnosticap pop" onclick="actionDiagnosticImagingOrder({{$activity->id ?? ''}});" data-content="Orden Imágenes Dx">
                                                                <i class="image icon"></i>
                                                            </a>
                                                        </div>
                                                @endif


                                                @if($lastFollowUp && optional($lastFollowUp)->diagnosis_origin_prescription != '')
                                                                <div class="column" style="margin-right: 15px;">
                                                            <form id="formula-medicamentos-form-{{$activity->id }}" action="/servicio/{{$activity->id}}/formula_medicamentos" method="POST">
                                                                {{ csrf_field() }}
                                                                <a href="#" class="ui clickable-link formulap pop" onclick="event.preventDefault(); loadingMain(true); document.getElementById('formula-medicamentos-form-{{$activity->id }}').submit();" data-content="Formulación">
                                                                    <i class="clipboard outline icon"></i>
                                                                </a>
                                                            </form>
                                                                </div>
                                                @endif

                                                @if($lastFollowUp && optional($lastFollowUp)->diagnosis_origin_controlled_medication != '')
                                                        <div class="column" style="margin-right: 15px;">
                                                            <form id="formula-medicamentos-controlados-form-{{$activity->id }}" action="/servicio/{{$activity->id }}/formula_medicamentos_controlados" method="POST">
                                                                {{ csrf_field() }}
                                                                <a href="#" class="ui clickable-link controladosp pop" onclick="event.preventDefault(); loadingMain(true); document.getElementById('formula-medicamentos-controlados-form-{{$activity->id }}').submit();" data-content="Form. Controlada">
                                                                    <i class="medkit icon"></i>
                                                                </a>
                                                            </form>
                                                        </div>
                                                @endif

                                            </div>
                                        @endif
                             @endif
                        </td>
                    </tr>
                @endforeach
            </table>

            <br>
            <!-- Paginación -->
            @if ($activities->hasPages())
                <div class="ui grid">
                    <div class="eight wide column left aligned"></div>
                    <div class="eight wide column right aligned" style="right: 10px;">
                        <div class="ui pagination menu">
                            {{ $activities->links() }}
                        </div>
                        <p>Total de registros: {{ $activities->total() }}</p>
                    </div>
                </div>
            @endif
            @if (\Session::has('success'))
                <div class="ui green message" id="success-message">
                    <i class="close icon" id="close-success-message"></i>
                    <div class="header">
                        {!! \Session::get('success') !!}
                    </div>
                </div>
            @endif
            @if (\Session::has('error'))
                @if (\Session::has('error'))
                    <div class="ui negative message">
                        <i class="close icon"></i>
                        <div class="header">
                            {!! \Session::get('error') !!}
                        </div>
                    </div>
                @endif
            @endif
        </div>
    </div>
    <!-- Modal para capturar motivo de anulación -->
    <div class="ui modal" id="anularModal">
        <div class="header">Motivo de la Anulación</div>
        <div class="content">
            <form class="ui form" method="POST">
                {{ csrf_field() }}
                <div class="field">
                    <label>Motivo</label>
                    <select class="ui dropdown" name="motivo" id="motivo" required>
                        <option value="">Selecciona un motivo</option>
                        <option value="IPS no ofrece el servicio">IPS no ofrece el servicio</option>
                        <option value="Trabajador no está cubierto">Trabajador no está cubierto</option>
                        <option value="Patrono solicita la cancelación del servicio">Patrono solicita la cancelación del
                            servicio
                        </option>
                    </select>
                </div>

                <div class="actions">
                    <div class="ui cancel button">Cancelar</div>
                    <button type="submit" class="ui button primary">Confirmar</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Modal para Reportar auditoría médica mnk -->
    <div class="ui modal" id="reportAuditMnkModal">
        <div class="header">Reportar auditoría médica MNK</div>
        <div class="content">
            <form class="ui form" method="POST">
                {{ csrf_field() }}
                <div class="field">
                    <label>Descripción del seguimiento</label>
                    <textarea class="auto-resize" name="description"
                              placeholder="" rows="1"></textarea>
                </div>

                <div class="actions">
                    <div class="ui cancel button">Cancelar</div>
                    <button type="submit" class="ui button primary">Confirmar</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Modal para el cambio del proveedor -->
    <div class="ui modal" id="changeProviderModal">
        <div class="header">Cambiar proveedor</div>
        <div class="content">
            <form class="ui form" method="POST">
                {{ csrf_field() }}
                <div class="field">
                    <label>Proveedor</label>
                    <select class="ui dropdown" name="new_provider" id="new_provider" required>
                        @foreach($providers as $provider)
                            <option value="{{ $provider->id }}">
                                {{ ($provider->clap == 1 ? 'CLAP-' : '') . $provider->name }}
                            </option>
                        @endforeach
                    </select>
                </div>

                <div class="actions">
                    <div class="ui cancel button">Cancelar</div>
                    <button type="submit" class="ui button primary">Confirmar</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Modal para capturar motivo de anulación -->
    <div class="ui modal" id="reportarNegacionModal" style='display: none'>
        <div class="header">Reportar negación servicio</div>
        <div class="content">
            <form class="ui form" method="POST">
                {{ csrf_field() }}

                <div class="field">
                    <div class="ui selection dropdown">
                        <input type="hidden" name="reason_cancel">
                        <i class="dropdown icon"></i>
                        <div class="default text">Reportar negación servicio</div>
                        <div class="menu">
                            <div class="item" data-value="0">Evento no laboral</div>
                            <div class="item" data-value="1">Diagnósticos de origen común</div>
                            <div class="item" data-value="2">Diagnósticos no derivados del evento</div>
                            <div class="item" data-value="3">No asegurado</div>
                            <div class="item" data-value="4">No pertinencia técnica</div>
                        </div>
                    </div>
                </div>
                <div class="actions">
                    <div class="ui cancel button">Cancelar</div>
                    <button type="submit" class="ui button primary">Confirmar</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Modal para capturar motivo de anulación Pe IT -->
    <div class="ui modal" id="anularItModal">
        <div class="header">Motivo de la Anulación</div>
        <div class="content">
            <form class="ui form" method="POST">
                {{ csrf_field() }}
                <div class="field">
                    <label>Motivo</label>
                    <select class="ui dropdown" name="motivo" id="motivo" required>
                        <option value="">Selecciona un motivo</option>
                        <option value="time_limit_article_237_labor_code">Haber transcurrido el plazo de los 2 años que establece el artículo 237 del código de Trabajo</option>
                        <option value="case_prescription">Prescripción del caso</option>
                        <option value="treatment_abandonment">Abandono de tratamiento</option>
                        <option value="non_covered_case">Determinarse que se trata de un caso no amparado</option>
                        <option value="other">Otros</option>
                    </select>
                </div>

                <div class="actions">
                    <div class="ui cancel button">Cancelar</div>
                    <button type="submit" class="ui button primary">Confirmar</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Modal para referir a especialista -->
    <div class="ui modal" id="referralSpecialistModal">
        <div class="header">Resultado de la gestión</div>
        <div class="content">
            <form class="ui form" method="POST" id="referralForm">
                {{ csrf_field() }}
                <div class="field">
                    <label>Gestión</label>
                    <select class="ui dropdown" name="gestion" id="gestion" required>
                        <option value="">Gestión</option>
                        <option value="1">Si</option>
                        <option value="0">No</option>
                    </select>
                </div>
                <div class="field" id="type_of_care_field" style="display: none;">
                    <label>Tipo de Servicio</label>
                    <select class="ui dropdown" name="type_of_care" id="type_of_care" required>
                        <option value="Tipo de Servicio">Tipo de Servicio</option>
                        <option value="Consulta de primera vez">Consulta de primera vez</option>
                        <option value="Consulta de seguimiento">Consulta de seguimiento</option>
                        <option value="Cirugía">Cirugía</option>
                        <option value="Interconsulta">Interconsulta</option>
                        <option value="Valoración de urgencia">Valoración de urgencia</option>
                        <option value="Otros">Otros.</option>
                    </select>
                </div>
                <div class="actions">
                    <div class="ui cancel button">Cancelar</div>
                    <button type="submit" class="ui button primary">Confirmar</button>
                </div>
            </form>
        </div>
    </div>

    <style>
        .filter {
            margin-bottom: 30px;
        }

        .truncate {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            /* Añade "..." al texto que se desborda */
            max-width: 1px;
            min-width: 40px;
            vertical-align: top;
            text-align: center !important;
            padding: 1em 6px !important;
        }
    </style>
    <script>
        $('.ui.dropdown').dropdown();

        $('.link-gis').popup({
            boundary: 'body',
            content: 'Debe completar gestión MNK',
            position: 'top center'
        });

        $('.aprobar-reapertura').popup({
            boundary: 'body',
            content: 'Aprobar reapertura',
            position: 'top center'
        });
        $('.rechazar-reapertura').popup({
            boundary: 'body',
            content: 'Rechazar reapertura',
            position: 'top center'
        });
        $('.aprobar-controversia').popup({
            boundary: 'body',
            content: 'Aprobar trámite judicial',
            position: 'top center'
        });
        $('.rechazar-controversia').popup({
            boundary: 'body',
            content: 'Rechazar trámite judicial',
            position: 'top center'
        });
        $('.ver-poliza').popup({
            boundary: 'body',
            content: 'Ver formulario',
            position: 'top center'
        });
        $('.anular').popup({
            boundary: 'body',
            content: 'Anular servicio',
            position: 'top center'
        });
        $('.reportarNegacion').popup({
            boundary: 'body',
            content: 'Reportar negación servicio',
            position: 'top center'
        });
        $('.reportAuditMnk').popup({
            boundary: 'body',
            content: 'Reportar auditoría médica MNK',
            position: 'top center'
        });
        $('.changeProvider').popup({
            boundary: 'body',
            content: 'Cambiar proveedor',
            position: 'top center'
        });
        $('.aprobar').popup({
            boundary: 'body',
            content: 'Aprobar servicio',
            position: 'top center'
        });
        $('.aprobarFractions').popup({
            boundary: 'body',
            content: 'Aprobar pagos de fraccionamiento',
            position: 'top center'
        });
        $('.aprobar-origen').popup({
            boundary: 'body',
            content: 'Reportar aprobación auditoria administrativa',
            position: 'top center'
        });
        $('.rechazar-origen').popup({
            boundary: 'body',
            content: 'Reportar rechazo auditoria administrativa',
            position: 'top center'
        });
        $('.aprobar-pcg').popup({
            boundary: 'body',
            content: 'Aprobar PCG calificado',
            position: 'top center'
        });
        $('.rechazar-pcg').popup({
            boundary: 'body',
            content: 'Rechazar PCG calificado',
            position: 'top center'
        });
        $('.informacion-extra').popup({
            boundary: 'body',
            content: 'Solicitar información extra',
            position: 'top center'
        });
        $('.anularPeit').popup({
            boundary: 'body',
            content: 'Rechazar IT',
            position: 'top center'
        });

        //Boton aprobar mierte de afiliado
        $('.aprobar-muerte').popup({
            boundary: 'body',
            content: 'Aprobar muerte de asegurado',
            position: 'top center'
        });

        $('.rechazar-muerte').popup({
            boundary: 'body',
            content: 'Rechazar muerte de asegurado',
            position: 'top center'
        });

        $('.aprobar-caso-aceptado').popup({
            boundary: 'body',
            content: 'Aprobar caso aceptado por excepción',
            position: 'top center'
        });

        $('.rechazar-caso-aceptado').popup({
            boundary: 'body',
            content: 'Rechazar caso aceptado por excepción',
            position: 'top center'
        });

        $('.reportar_reapertura_caso_administrativa').popup({
            boundary: 'body',
            content: 'Reportar reapertura de caso administrativa',
            position: 'top center'
        });

  $('.pop').popup({
            boundary: 'body',
            position: 'top center'
        });

        $('.referralSpecialist').popup({
            boundary: 'body',
            content: 'Referir a especialista',
            position: 'top center'
        });

        // Manejar el clic en el enlace de anulación
        $('.anular').click(function (e) {
            e.preventDefault();

            let url = $(this).data('url');
            console.log('url: ', url);
            // Actualizar el atributo action del formulario en el modal con el ID del servicio
            $('#anularModal form').attr('action', url);
            // Mostrar el modal
            $('#anularModal').modal('show');
        });

        $('.anularPeit').click(function (e) {
            e.preventDefault();

            let url = $(this).data('url');
            console.log('url: ', url);
            // Actualizar el atributo action del formulario en el modal con el ID del servicio
            $('#anularItModal form').attr('action', url);
            // Mostrar el modal
            $('#anularItModal').modal('show');
        });

        // Manejar el clic en el enlace de anulación
        $('.reportAuditMnk').click(function (e)  {
            e.preventDefault();

            let url = $(this).data('url');
            // Actualizar el atributo action del formulario en el modal con el ID del servicio
            $('#reportAuditMnkModal form').attr('action', url);
            // Mostrar el modal
            $('#reportAuditMnkModal').modal('show');
        });

        //Manejar el click de remision a especialista
        $('.referralSpecialist').click(function (e) {
            e.preventDefault();
            e.stopImmediatePropagation();
            referralUrl  = $(this).data('url');
            // Actualizar el atributo action del formulario en el modal con el ID del servicio
            $('#referralSpecialistModal form').attr('action', referralUrl );
            // Mostrar el modal
            $('#referralSpecialistModal').modal('show');
        });

        // Manejar el clic en el enlace de anulación
        $('.changeProvider').click(function (e)  {
            e.preventDefault();

            let url = $(this).data('url');
            // Actualizar el atributo action del formulario en el modal con el ID del servicio
            $('#changeProviderModal form').attr('action', url);
            // Mostrar el modal
            $('#changeProviderModal').modal('show');
        });

        //cerrar mensaje de éxito
        $('#close-success-message').on('click', function () {
            $('#success-message').fadeOut();
        });

        $('.reportarNegacion').click(function (e) {
            e.preventDefault();
            e.stopImmediatePropagation();
            $('#reportarNegacionModal').modal('hide');

            let url = $(this).data('url');
            // Actualizar el atributo action del formulario en el modal con el ID del servicio
            $('#reportarNegacionModal form').attr('action', url);
            // Mostrar el modal
            $('#reportarNegacionModal').modal('show');
        });
    </script>

    <!-- coloca los textarea auto resize -->
    <script>
        $(document).ready(function () {
            $('.auto-resize').each(function () {
                // Fija el height inicial en 37px solo desde JavaScript
                $(this).css('height', '38.21px');

                // Ajuste automático del height al escribir
                $(this).on('input', function () {
                    this.style.height = '38.21px';  // Reinicia a 37px antes de ajustar
                    this.style.height = (this.scrollHeight) + 'px';  // Ajusta según el contenido
                });
            });

            {{--Para resetar los filtros del formulario--}}
            $('#reset').click(function () {
                // Restablecer el formulario por su ID
                $('form')[0].reset();

                // Limpiar campos de texto y números
                $('form input[type="text"], form input[type="number"], form input[type="hidden"]').val('');
                // Desmarcar manualmente todos los checkboxes y radio buttons
                $('form input[type="checkbox"], form input[type="radio"]').prop('checked', false);

                // Limpiar dropdowns de Semantic UI
                $('form .ui.dropdown').dropdown('clear');

                // Limpiar datepickers usando Pickadate.js
                $.each($('form .datepicker'), function (k, el) {
                    console.log(el);
                    $(el).pickadate('picker').clear();
                });

                // Enviar automáticamente el formulario después de limpiar
                $('#filterForm').submit();
            });

            //oculta tipo de servicio del modal referencia a especialista
            $('#gestion').change(function() {
                if ($(this).val() === "1") {
                    $('#type_of_care_field').show();
                    $('#type_of_care').prop('required', true); // Hacerlo obligatorio si se muestra
                } else {
                    $('#type_of_care_field').hide();
                    $('#type_of_care').prop('required', false); // No obligatorio si se oculta
                    $('#type_of_care').val(""); // Limpiar selección
                }
            });
        });
        // Ejecutar el loader al confirmar el formulario
        $('#referralForm').submit(function (e) {
            e.preventDefault();

            loadingMain(true);
            $('#referralSpecialistModal').modal('hide');
            var formData = $(this).serialize();
            $.ajax({
                url: referralUrl,
                type: 'POST',
                data: formData,
                success: function (response) {
                    loadingMain(false); // Oculta el loader
                    Swal.fire({
                        icon: 'success',
                        title: 'Éxito',
                        text: 'La gestión fue enviada correctamente.'
                    });
                },
                error: function (xhr) {
                    loadingMain(false); // Oculta el loader
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: 'Ocurrió un error al enviar la gestión.'
                    });
                }
            });
        });

    </script>

    <script>
        document.addEventListener("DOMContentLoaded", function () {
            // Establecer el idioma a español
            moment.locale('es');

            // Seleccionar todos los elementos con clase "actividad"
            const div_date_format = document.querySelectorAll('.div_date_format');

            div_date_format.forEach(function (date_format) {
                // Obtener la fecha desde el atributo data-fecha
                let date = date_format.getAttribute('data-fecha');

                // Verificar si date está vacío
                if (!date || date.trim() === '') {
                    date_format.querySelector('.date_format_value').innerText = '';
                } else {
                    // Formatear la fecha con Moment.js
                    let fechaFormateada = moment(date).format('dddd D [de] MMMM [de] YYYY');

                    // Capitalizar la primera letra de la fecha formateada
                    fechaFormateada = fechaFormateada.charAt(0).toUpperCase() + fechaFormateada.slice(1);

                    // Asignar la fecha formateada al elemento
                    date_format.querySelector('.date_format_value').innerText = `${fechaFormateada}`;
                }
            });
        });
    </script>

      <!--Esta función emite la incapacidad pero con tiempo (Atención primaria)-->
    <script>
        function actionDisabilityAp(activity_id) {

            // Mostrar una ventana de espera
            Swal.fire({
                title: 'Cargando...',
                text: 'Por favor, espera un momento.',
                allowOutsideClick: false,
                showConfirmButton: false,
                willOpen: () => {
                    Swal.showLoading();
                }
            });

            $.ajax({
                url: '/service/medical_disability_time_ap', // Ruta hacia la cual se enviará la solicitud
                type: 'POST',
                data: {
                    activity_id: activity_id,
                    _token: '{{ csrf_token() }}' // Token CSRF para seguridad
                },
                success: function (response) {
                    swal.close();
                    // Capturar el mensaje de la respuesta
                    let message = response.message;

                    Swal.fire({
                        icon: response.valid ? 'success' : 'error', // Mostrar icono según la validación
                        title: response.valid ? 'Incapacidad emitida' : 'No se pudo emitir la incapacidad',
                        text: message, // Mostrar el mensaje específico recibido del backend
                        confirmButtonText: 'Aceptar',
                        allowOutsideClick: false // Evita que se cierre al hacer clic afuera del cuadro
                    });
                },
                error: function () {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: 'Ocurrió un error al emitir la incapacidad.',
                        confirmButtonText: 'Cerrar'
                    });
                }
            });
        }
    </script>

       <!--Esta función emite hospitalización pero con tiempo (Atención primaria)-->
    <script>
        function actionHospitalization(activity_id) {

            // Mostrar una ventana de espera
            Swal.fire({
                title: 'Cargando...',
                text: 'Por favor, espera un momento.',
                allowOutsideClick: false,
                showConfirmButton: false,
                willOpen: () => {
                    Swal.showLoading();
                }
            });

            $.ajax({
                url: '/service/medical_hospitalization_ap', // Ruta hacia la cual se enviará la solicitud
                type: 'POST',
                data: {
                    activity_id: activity_id,
                    _token: '{{ csrf_token() }}' // Token CSRF para seguridad
                },
                success: function (response) {
                    swal.close();
                    // Capturar el mensaje de la respuesta
                    let message = response.message;

                    Swal.fire({
                        icon: response.valid ? 'success' : 'error', // Mostrar icono según la validación
                        title: response.valid ? 'Hospitalización emitida' : 'No se pudo emitir la hospitalización',
                        text: message, // Mostrar el mensaje específico recibido del backend
                        confirmButtonText: 'Aceptar',
                        allowOutsideClick: false // Evita que se cierre al hacer clic afuera del cuadro
                    });
                },
                error: function () {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: 'Ocurrió un error al emitir la Hospitalización.',
                        confirmButtonText: 'Cerrar'
                    });
                }
            });
        }
    </script>

     <!--Esta función emite orden imágenes diagnósticas pero con tiempo (Atención primaria)-->
    <script>
        function actionDiagnosticImagingOrder(activity_id) {

            // Mostrar una ventana de espera
            Swal.fire({
                title: 'Cargando...',
                text: 'Por favor, espera un momento.',
                allowOutsideClick: false,
                showConfirmButton: false,
                willOpen: () => {
                    Swal.showLoading();
                }
            });

            $.ajax({
                url: '/service/medical_diagnostic_imaging_order_ap', // Ruta hacia la cual se enviará la solicitud
                type: 'POST',
                data: {
                    activity_id: activity_id,
                    _token: '{{ csrf_token() }}' // Token CSRF para seguridad
                },
                success: function (response) {
                    swal.close();
                    // Capturar el mensaje de la respuesta
                    let message = response.message;

                    Swal.fire({
                        icon: response.valid ? 'success' : 'error', // Mostrar icono según la validación
                        title: response.valid ? 'Orden de imágenes diagnósticas emitida' : 'No se pudo emitir Orden de imágenes diagnósticas',
                        text: message, // Mostrar el mensaje específico recibido del backend
                        confirmButtonText: 'Aceptar',
                        allowOutsideClick: false // Evita que se cierre al hacer clic afuera del cuadro
                    });
                },
                error: function () {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: 'Ocurrió un error al emitir Orden de imágenes diagnósticas.',
                        confirmButtonText: 'Cerrar'
                    });
                }
            });
        }
    </script>

     <!--Esta consulta permite que el usuario pueda descargar el archivo (incluyendo una condición de tiempo) Atención secundaria-->
    <script>
        function downloadDisabilityAs(activity_id) {

            // Mostrar una ventana de espera
            Swal.fire({
                title: 'Cargando...',
                text: 'Por favor, espera un momento.',
                allowOutsideClick: false,
                showConfirmButton: false,
                willOpen: () => {
                    Swal.showLoading();
                }
            });

            $.ajax({
                url: '/service/medical_disability_time_as', // Ruta hacia la cual se enviará la solicitud
                type: 'POST',
                data: {
                    activity_id: activity_id,
                    _token: '{{ csrf_token() }}' // Token CSRF para seguridad
                },
                success: function (response) {
                    swal.close();
                    // Capturar el mensaje de la respuesta
                    let message = response.message;

                    Swal.fire({
                        icon: response.valid ? 'success' : 'error', // Mostrar icono según la validación
                        title: response.valid ? 'Incapacidad emitida' : 'No se pudo emitir la incapacidad',
                        text: message, // Mostrar el mensaje específico recibido del backend
                        confirmButtonText: 'Aceptar',
                        allowOutsideClick: false // Evita que se cierre al hacer clic afuera del cuadro
                    });
                },
                error: function () {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: 'Ocurrió un error al emitir la incapacidad.',
                        confirmButtonText: 'Cerrar'
                    });
                }
            });
        }
    </script>
@endsection
