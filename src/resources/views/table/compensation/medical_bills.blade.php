@extends('table.compensation')
@section('content_compensation')
    <div class="ui basic segment">
        <div class="ui styled fluid accordion">
            <div class="active title">
                <i class="dropdown icon"></i>
                Filtros
            </div>
            <div class="active content">
                <form class="ui form small clearing" method="GET" id="filterForm">
                    {{ csrf_field() }}

                    <div class="fields two">
                        <div class="field">
                            <label>Fecha de radicación</label>
                            <div class="two fields">
                                <div class="field">
                                    <input data-value="{{request()->input('action_start_date_submit')}}"
                                        placeholder="Desde" type="text" name="action_start_date" class="datepicker">
                                </div>
                                <div class="field">
                                    <input data-value="{{request()->input('action_end_date_submit')}}"
                                        placeholder="Hasta" type="text" name="action_end_date" class="datepicker">
                                </div>
                            </div>
                        </div>

                        @if (!$user->isProvider())
                            @php
                                $selectedProviders = request()->input('provider', '');
                            @endphp

                            <div class="field">
                                <label for="provider">Proveedor</label>
                                <select class="ui fluid search dropdown" name="provider" id="provider">
                                <option value="">Proveedor</option>
                                    @foreach ($providers as $provider)
                                        <option value="{{ $provider->id }}" {{ $provider->id == $selectedProviders ? 'selected' : '' }}>
                                            {{ $provider->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                        @endif
                    </div>


                    <div class="fields">
                        <div class="field">
                            <button class="ui primary button">
                                <i class="search icon"></i>
                                Aplicar filtros
                            </button>
                        </div>

                        <div class="field">
                            <button class="ui secondary button" id="reset" type="reset">
                                <i class="undo icon"></i>
                                Limpiar filtros
                            </button>
                        </div>

                    </div>
                </form>
            </div>

            <div class="active title">
                <i class="dropdown icon"></i>
                Resultados de la búsqueda
            </div>
            <div class=" active content">
                <table class="ui celled sortable striped compact very table selectable" style="font-size: 0.8em; line-height: 1em;" id="results">
                    <thead>
                        <tr>
                            <th class="truncate" title="Nombre del proveedor">Nombre del proveedor</th>
                            <th class="truncate" title="Valor factura electrónica">Valor factura electrónica</th>
                            <th class="truncate" title="Fecha radicación factura electrónica">Fecha radicación factura electrónica</th>
                            <th class="truncate" title="Tipo">Tipo</th>
                            <th class="truncate" title="Subtipo">Subtipo</th>
                            <th class="truncate" title="Ver formulario">Ver formulario</th>
                            <th class="truncate" title="Gestión">Gestión</th>
                            <th class="truncate" title="Reporte factura electrónica">Reporte factura electrónica</th>
                        </tr>
                    </thead>
                    <tbody>
                        @php
                            $invoiceTypes_front = [
                                'temporary_disability' => 'Facturas por Incapacidad Temporal (Art. 73, Ley N° 9078)',
                                'medical_services' => 'Facturas por Servicios Médicos',
                                'ins' => 'Facturas Art. 65 y 73, Ley N° 9078',
                            ];

                            $invoiceSubtypes_front = [
                                'ordinary' => 'Ordinaria',
                                'claim' => 'Reclamo',
                            ];
                        @endphp
                    @foreach($activities as $activity)
                        <tr>
                            {{--nombre del proveedor--}}
                            <td>{{ optional($activity->socialSecurityProvider)->provider->name ?? "" }}</td>
                            {{--valor factura electronica--}}
                            <td>
                                {{ $TYPE_CURRENCY[1] }}
                                {{ number_format($activity->socialSecurityProvider->total_amount, 2, ',', '.') }}
                            </td>
                            {{--fecha radicacion factura electronica--}}
                            <td>
                                {{ ucfirst(strftime('%A %e de %B del %Y', strtotime($activity->socialSecurityProvider->created_at ))) }}
                            </td>
                            <td>
                                {{ $invoiceTypes_front[trim(optional($activity->socialSecurityProvider)->invoice_type)] ?? '' }}
                            </td>
                            <td>
                                {{ $invoiceSubtypes_front[trim(optional($activity->socialSecurityProvider)->invoice_subtype)] ?? '' }}
                            </td>
                            {{--ver formulario--}}
                            <td>
                                <a class="ui clickable-link ver-formulario"
                                   href="/servicio/{{ $activity->id }}/social_security_providers">
                                    <i class="external eye icon"></i>
                                </a>
                            </td>
                            {{--gestion--}}
                            <td>
                            {{ ucfirst(mb_strtolower($activity->state->name)) }}
                            </td>
                            {{--reporte factura electronica--}}
                            <td></td>
                        </tr>
                    @endforeach
                    </tbody>
                </table>

                <br>
                <!-- Paginación -->
                @if ($activities->hasPages())
                    <div class="ui grid">
                        <div class="eight wide column left aligned"></div>
                        <div class="eight wide column right aligned" style="right: 10px;">
                            <div class="ui pagination menu">
                                {{ $activities->links() }}
                            </div>
                            <p>Total de registros: {{ $activities->total() }}</p>
                        </div>
                    </div>
                @endif
                @if (\Session::has('success'))
                    <div class="ui success message" id="success-message">
                        <i class="close icon" id="close-success-message"></i>
                        <div class="header">
                            {!! \Session::get('success') !!}
                        </div>
                    </div>
                @endif

                <!-- Mostrar errores -->
                @if ($errors->any())
                    <div class="ui negative message">
                        <div class="header">Errores encontrados</div>
                        <ul class="list">
                            @foreach ($errors->all() as $error)
                                <li>{!! $error !!}</li>
                                <!-- Permite que el HTML se interprete correctamente -->
                            @endforeach
                        </ul>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <script>

        $('.ver-formulario').popup({
            boundary: 'body',
            content: 'Ver formulario',
            position: 'top center'
        });

        $('#close-icon').on('click', function() {
            $('#success-message').hide();
        });

        $(document).ready(function() {

            $('.datepicker').pickadate({
                selectYears: true,
                selectMonths: true,
                formatSubmit: 'yyyy-mm-dd'
            });

            {{--Para resetar los filtros del formulario--}}
            $('#reset').click(function () {
                // Restablecer el formulario por su ID
                $('form')[0].reset();

                // Limpiar campos de texto y números
                $('form input[type="text"], form input[type="number"], form input[type="hidden"]').val('');
                // Desmarcar manualmente todos los checkboxes y radio buttons
                $('form input[type="checkbox"], form input[type="radio"]').prop('checked', false);

                // Limpiar dropdowns de Semantic UI
                $('form .ui.dropdown').dropdown('clear');

                // Limpiar datepickers usando Pickadate.js
                $.each($('form .datepicker'), function (k, el) {
                    console.log(el);
                    $(el).pickadate('picker').clear();
                });

                // Enviar automáticamente el formulario después de limpiar
                $('#filterForm').submit();
                
                window.history.replaceState({}, document.title, window.location.pathname);
            });
        });
    </script>

    <!--inicializar datepicker y validaciones min y max -->
    <script>
        $(document).ready(function () {
            $('.date-range').each(function () {
                let $start = $(this).find('.start-date').pickadate({
                    selectYears: true,
                    selectMonths: true,
                    format: 'dd/mm/yyyy',
                    formatSubmit: 'yyyy-mm-dd',
                    hiddenName: true,
                    clear: 'Borrame',
                }).pickadate('picker');

                let $end = $(this).find('.end-date').pickadate({
                    selectYears: true,
                    selectMonths: true,
                    format: 'dd/mm/yyyy',
                    formatSubmit: 'yyyy-mm-dd',
                    hiddenName: true,
                    clear: 'Borrame',
                }).pickadate('picker');

                // Obtener valores iniciales si existen
                let startDate = $start.get('select');
                let endDate = $end.get('select');

                // Si hay un valor inicial, ajustar las restricciones
                if (startDate) {
                    $end.set('min', startDate);
                }
                if (endDate) {
                    $start.set('max', endDate);
                }

                // Evento cuando se selecciona una fecha de inicio
                $start.on('set', function (event) {
                    if (event.select) {
                        $end.set('min', $start.get('select'));
                    } else if (event.hasOwnProperty('clear')) {
                        // Si se presiona "Borrar", eliminar la restricción mínima en el otro campo
                        $end.set('min', false);
                    }
                });

                // Evento cuando se selecciona una fecha de fin
                $end.on('set', function (event) {
                    if (event.select) {
                        $start.set('max', $end.get('select'));
                    } else if (event.hasOwnProperty('clear')) {
                        // Si se presiona "Borrar", eliminar la restricción máxima en el otro campo
                        $start.set('max', false);
                    }
                })
            });
        });
    </script>
@endsection