
@extends('table.compensation')

@section('content_compensation')

    <div class="ui basic segment">

        <div class="ui styled fluid accordion">

            <div class="active title">
                <i class="dropdown icon"></i>
                Filtros
            </div>
            <div class="active content">
                <form class="ui form small clearing" method="GET">
                    {{ csrf_field() }}
                    <div class="three fields">
                        <div class="field">
                            <label>Fecha</label>
                            <div class="two fields date-range">
                                <div class="field">
                                    <input data-value="{{request()->input('start_date_submit')}}" placeholder="Desde"
                                           type="text" name="start_date" class="datepicker start-date">
                                </div>
                                <div class="field">
                                    <input data-value="{{request()->input('end_date_submit')}}" placeholder="Hasta"
                                           type="text" name="end_date" class="datepicker end-date">
                                </div>
                            </div>
                        </div>
                        <div class="field">
                            <label># Factura</label>
                            <input type="text" name="num_fac" class="input_data"
                                   placeholder="Numero de factura"
                                   value="{{ request()->input('num_fac') ?? '' }}">
                        </div>
                    </div>

                    <div class="fields bfilter">
                        <div class="field">
                            <button class="ui primary  button">
                                <i class="search icon"></i>
                                Aplicar filtros
                            </button>
                        </div>

                        <div class="field">
                            <button class="ui secondary button" id="reset-filters" type="button">
                                <i class="undo icon"></i>
                                Limpiar filtros
                            </button>
                        </div>

                    </div>
                </form>
            </div>

            <div class="active title">
                <i class="dropdown icon"></i>
                Pagos administrativos
            </div>
            <div class=" active content">
                <table class="ui celled sortable striped compact very  table selectable "
                       style="font-size: 0.8em; line-height: 1em;" id="results">
                    <thead>
                    <tr>
                        <th title="# Trámite"># Trámite</th>
                        <th class="truncate" title="Nombre proveedor">Nombre proveedor</th>
                        <th class="truncate" title="# Factura"># Factura electrónica</th>
                        <th  title="Fecha emisión factura">Fecha emisión factura</th>
                        <th class="truncate" title="Fecha ingreso factura">Fecha ingreso factura</th>
                        <th  title="Fecha aprobación/rechazo">Fecha aprobación/rechazo </th>
                        <th  title="Contador días de ingreso">Contador días de ingreso </th>
                        <th class="truncate" title="Moneda">Moneda</th>
                        <th class="truncate" title="Monto">Monto</th>
                        <th class="truncate" title="Estado">Estado</th>
                        <th title="Ver formulario">Ver formulario</th>
                    </tr>
                    </thead>
                    <tbody>
                        @foreach($activity as $row)

                            <tr>
                                <td class="left aligned" >{{ $row->administrative_payment->id }}</td>
                                <td class="left aligned" >{{ ucwords(mb_strtolower($row->affiliate->full_name ?? '')) ?? '' }}</td>
                                <td class="left aligned" >{{ $row->last_issuer_administrative_payments()->consecutive_number ?? '' }}</td>
                                <td class="left aligned" >{{ $row->last_issuer_administrative_payments()->issue_date ? ucfirst(strftime('%A %e de %B del %Y',strtotime( $row->last_issuer_administrative_payments()->issue_date ))) : '' }}</td>
                                <td class="left aligned" >{{ ucfirst(strftime('%A %e de %B del %Y',strtotime($row->created_at)))  }}</td>
                                <td class="left aligned" >{{ $row->administrative_payment->last_date_action() ? ucfirst(strftime('%A %e de %B del %Y',strtotime($row->administrative_payment->last_date_action()))) : '' }}</td>
                                <td class="left aligned" >{{ $row->administrative_payment->day_counter() ?? 0 }}</td>
                                <td class="left aligned" >{{ $row->last_issuer_administrative_payments()->currency ?? '' }}</td>
                                <td class="left aligned" >{{ number_format($row->administrative_payment->totalIssuer() ?? 0, 2, ',', '.') }}</td>
                                <td class="left aligned" >{{ $row->state->name ?? ''  }}</td>

                                <td class="left aligned">
                                    <a class="ui clickable-link ver-formulario"
                                       href="/servicio/{{ $row->id }}/administrative_payment">
                                        <i class="external eye icon"></i>
                                    </a>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>

                <!-- Paginación -->
                <br>
                <div class="pagination">
                    @if ($activity->hasPages())
                        <div class="ui pagination menu">
                            {{ $activity->appends(request()->query())->links() }}
                        </div>
                    @endif
                    <p>Total de registros: {{ $activity->total() }}</p>
                </div>

            </div>
        </div>
    </div>

    <style>
        .pagination {
            display: grid;
            justify-items: end;
        }

        .truncate {
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 1px;
            min-width: 40px;
            padding: 1em 6px !important;
        }
    </style>

    <script>

        $('.ver-formulario').popup({
            boundary: 'body',
            content: 'Ir a incapacidad',
            position: 'top center'
        });

        $(document).ready(function() {

            $('.ui.dropdown').dropdown();
            $('.timepicker').pickatime();

            $('.ui.accordion').accordion({
                exclusive: false
            });

            $('.datepicker').pickadate({
                selectYears: true,
                selectMonths: true,
                formatSubmit: 'yyyy-mm-dd'
            });

            $('#reset-filters').on('click', function () {
                // Limpia fechas
                $('input[name="start_date"]').val('');
                $('input[name="end_date"]').val('');

                // Limpia número de factura
                $('input[name="num_fac"]').val('');

                // Opcional: también puedes reiniciar el componente pickadate visualmente
                $('.start-date').pickadate('picker').clear();
                $('.end-date').pickadate('picker').clear();

                window.location.href = "{{ secure_url('/tablero/indemnizaciones/pagos_admnistrativos') }}";
            });


        });

    </script>

@endsection