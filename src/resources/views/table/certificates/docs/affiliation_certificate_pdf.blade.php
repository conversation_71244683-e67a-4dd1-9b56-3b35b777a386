<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <title>Certificado de Seguro</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            margin: 0;
            padding: 0;
        }
        .header {
            background-color: #fff;
            color: #000;
            padding: 20px;
            text-align: center;
        }
        .header img {
            float: left;
            width: 100px;
            margin-right: 20px;
            margin-top: -10px; /* Ajusta este valor para subir el logo */
        }
        .header h1, .header h2 {
            margin: 0;
            font-weight: normal;
        }
        .content {
            padding: 20px;
        }
        .highlight {
            font-weight: bold;
            color: red;
        }
        .info-list {
            margin-top: 20px;
        }
        .info-list li {
            margin-bottom: 10px;
        }
        .info-list-dos {
            margin-top: 30px;
        }
        .info-list-dos li {
            margin-bottom: 10px;
        }
        .coverage-section {
            margin-top: 30px;
            padding: 0px;
            border: 1px solid #000;
            border-radius: 10px;
        }
        .coverage-header {
            background-color: #000;
            color: #fff;
            padding: 10px;
            font-weight: bold;
            text-align: center;
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
        }
        .coverage-header-dos {
            background-color: #fff;
            color: #000;
            padding: 10px;
            font-weight: bold;
            text-align: center;
            border-radius: 8px;
            border: 1px solid #000
        }
        .coverage-content {
            padding: 10px;
            text-align: justify;
        }
        .coverage-note {
            font-weight: bold;
        }
        .footer {
            position: fixed;
            bottom: 0;
            left: 0;
            width: 100%;
            text-align: center;
            background-color: #ffffff;
            z-index: 100;
            height: 80px;
        }
        .signature-table {
            width: 100%;
            margin-top: 30px;
            text-align: center;
        }
        .signature-table td {
            padding: 20px;
        }
        .signature-line {
            border-top: 1px solid #000;
            width: 60%;
            margin: 0 auto;
            padding-top: 10px;
        }

        .line {
            display: inline-block;
            width: 100%;
            border-bottom: 1px solid #000;
            margin-left: 5px;
        }

        .title-border {
            width: 400px;
            position: absolute;
            left: -23px;
            top: -10px;
        }
        .content_body {
            margin-top: 20px;

            position: relative;
            margin-bottom: 50px;
        }
        .logo-image {
            max-width: 100%;
            height: 130px !important;
        }

        .text_line {}

    </style>
</head>
<body>

<footer class="footer">
    <img src="{{public_path('images/mnk_footer.png')}}"
         alt="pie de documento" style="width: 100%;">
</footer>

{{--<div class="header">--}}
{{--    <img   src="{{public_path('images/mnk.png')}}" alt="Logo de Oceánica de Seguros">--}}
{{--    <h1>INCLUSION DE PERSONA ASEGURADA</h1>--}}
{{--    <h2>SEGURO OBLIGATORIO DE RIESGOS DEL TRABAJO</h2>--}}
{{--</div>--}}

<div class="content_body">
    <table
            style="width: 100%; border: none; table-layout: fixed; font-size: 13px !important;">
        <tbody>
        <tr>
            <td style="width: 30%; padding: 10px; border: none; text-align: left; vertical-align: top;">
                <img src="{{public_path('images/borde_sort.png')}}" alt=""
                     class="title-border"
                     style="height: 130px;">

                <div style="width: 70% !important;padding: 18px 28px;">
                    <div style="margin-bottom: 2px">
                        SOLICITUD DE SEGURO
                    </div>
                    <div style="font-weight: bold;font-size: 15px !important; ">
                        SEGURO OBLIGATORIO DE RIESGOS DEL TRABAJO
                    </div>
                </div>
            </td>

            <!-- Logo celda con ancho del 25% -->
            <td style="width: 30%; padding: 5px; border: none; text-align: right; height: auto">
                <img src="{{public_path('images/mnk.png')}}" alt="Logo" class="logo-image">
            </td>
        </tr>
        </tbody>
    </table>

    <div class="coverage-section">
        <div class="coverage-header">DATOS DEL TOMADOR:</div>
        <div class="coverage-content">
            <ul class="info-list">
                <li>Nombre: {{ucwords(mb_strtolower($activityTomador->affiliate->first_name ?? ''))}}</li>
                <li>Número de poliza: {{$activityTomador->parent_activity->policy_sort->formatNumberConsecutive() ?? ''}}</li>
                <li>Telefono: {{$activityTomador->affiliate->phone ?? ''}}</li>
                <li>Correo eletronico: {{$activityTomador->affiliate->email ?? ''}}</li>
                <li>Direccion: {{$activityTomador->affiliate->address ?? ''}}</li>
            </ul>
        </div>
    </div>

    <br>
    <div class="coverage-header-dos">DATOS DEL ASEGURADO</div>

    <div style="text-align: left;padding: 10px 20px;">
        <ul class="info-list">
            <li>Nombre y apellidos: {{ ucwords(mb_strtolower($activity->affiliate->full_name ?? '')) }}</li>
            <li>Tipo de identificación:
                {{ $DOC_TYPES[$activity->affiliate->doc_type ?? ''] ?? '' }}
            </li>
            <li>Número de identificación:
                {{$activity->affiliate->doc_number ?? '' }}
            </li>
            <li>Fecha de nacimiento:
                {{$policyAffiliate->date_of_birth ?? '' }}<
            </li>
            <li>Correo electrónico:
                {{$activity->affiliate->email ?? '' }}
            </li>
            <li>Teléfono:
                {{$activity->affiliate->phone ?? '' }}
            </li>
            <li>Salario:
                {{($activityTomador->parent_activity->policy_sort->type_currency === 'USD' ? '$' : '₡')}} {{number_format($policyAffiliate->monthly_salary ?? 0, 2, ',', '.')}}
            </li>
            <li>Ocupación:
                {{$policyAffiliate->occupation ?? ''}}
            </li>
            <li>Días trabajados:
                {{$policyAffiliate->days ?? ''}}
            </li>
            <li>Fecha de ingreso:
                {{ isset($policyAffiliate->created_at) ? ucfirst(strftime('%A %e de %B del %Y',strtotime($policyAffiliate->created_at))) : '' }}
            </li>
        </ul>
    </div>

    <table class="signature-table">
        <tr>
            <td>
                <div class="signature-line"></div>
                <div>Firma</div>
            </td>
            <td>
                {{ ucfirst(strftime('%A %e de %B del %Y',strtotime(date('Y-m-d')))) }}
                <div class="signature-line"></div>
                <div>Fecha</div>
            </td>
        </tr>
    </table>

</div>

</body>
</html>