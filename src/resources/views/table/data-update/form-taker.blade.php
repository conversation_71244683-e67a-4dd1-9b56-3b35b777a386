@extends('layouts.main')

@section('title', 'Poliza SORT')

@section('menu')
    @parent
@endsection

@section('content')
    <div class="ui basic segment">
        <!-- Encabezado del formulario -->
        <h1 class="ui header">
            Póliza #
            {{ $activity->policy_sort->consecutive ? $activity->policy_sort->formatNumberConsecutive() : $activity->policy_sort->id }}
            <div class="sub header">Campos con <span style="color: red;" class="required">*</span> obligatorios.</div>
        </h1>

        <div class="ui secondary segment">
            <div class="ui three columns grid">
                <div class="column">
                    <b>Tipo y número de identificación:</b> {{ $activity->affiliate->doc_type }}
                    {{ $activity->affiliate->doc_number }}
                </div>
                <div class="column"><b>Nombre del tomador:</b> <a
                        href="{{ secure_url('afiliado/' . $activity->affiliate_id) }}">{{ mb_convert_case(mb_strtolower($activity->affiliate->full_name ?? ''), MB_CASE_TITLE, 'UTF-8') }}</a>
                </div>

                <div class="column">
                    <b># de emisión:</b>
                    <span>{{ $activity->policy_sort->id }} </span>
                </div>

            </div>

            <div class="ui three columns grid">
                <div class="column">
                    <b>Fecha inicio vigencia:</b>
                    {{ $activity->policy_sort && $activity->policy_sort->validity_from
                        ? ucfirst(strftime('%A %e de %B del %Y', strtotime($activity->policy_sort->validity_from)))
                        : 'Fecha no disponible' }}
                </div>

                <div class="column">
                    <b>Fecha fin vigencia:</b>
                    {{ $activity->policy_sort && $activity->policy_sort->validity_to
                        ? ucfirst(strftime('%A %e de %B del %Y', strtotime($activity->policy_sort->validity_to)))
                        : 'Fecha no disponible' }}
                </div>

                <div class="column">
                    <b>Póliza #:</b>
                    <span>{{ $activity->policy_sort->formatNumberConsecutive() }} </span>
                </div>
            </div>
            <div class="ui three columns grid">
                <div class="column">
                    <b>Condiciones especiales:</b>
                    <span>{{ $condiciones }} </span>
                </div>
            </div>
        </div>

         <!-- Mostrar errores -->
         @if ($errors->any())
            <div class="ui negative message">
                <div class="header">Errores encontrados</div>
                <ul class="list">
                    @foreach ($errors->all() as $error)
                        <li>{!! $error !!}</li>
                    @endforeach
                </ul>
            </div>
        @endif

        @if (session('success'))
            <div class="ui success message">
                <div class="header">
                    Operación exitosa
                </div>
                <p>{{ session('success') }}</p>
            </div>
        @endif

        <form id="form-informacion-de-identificacion" class="ui attached form crud-form" action="{{ secure_url('/tablero/actualizacion_datos/tomadores/' . $activity->id . '/update') }}" method="post">
            {{ csrf_field() }}
            <div class="ui styled fluid accordion">
                <div class="title">
                    <i class="dropdown icon"></i> Información de Identificación
                </div>
                <div class="content active">
                    <!-- Campos del formulario -->
                    <div class="two fields">
                        <div class="field">
                            <label for="typeIdentify">Tipo de identificación</label>
                            <div class="ui selection dropdown grayed-input" id="heirDocTypeDropdown">
                                <input readonly type="hidden" id="docType"
                                    class="minus capitalizes grayed-input" value="{{ $affiliate->doc_type ?? '' }}">
                                <i class="dropdown icon"></i>
                                <div class="default text minus heirDocTypeDropdown">Seleccionar tipo documento</div>
                                <div class="menu">
                                    @foreach ($DOC_TYPES as $k => $v)
                                        <div class="item minus capitalizes heirDocTypeDropdown" data-value="{{ $k }}" @if ($k == $affiliate->doc_type) class="active selected" @endif>
                                            {{ $v }}
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        </div>

                        <div class="field">
                            <label for="typeIdentify">Actualización de datos</label>
                            <div class="ui selection dropdown" id="heirDocTypeDropdown2">
                                <input type="hidden" name="doc_type2" id="docType2" value="{{ old("docType2") }}" class="minus capitalizes">
                                <i class="dropdown icon"></i>
                                <div class="default text minus heirDocTypeDropdown">Seleccionar tipo documento</div>
                                <div class="menu">
                                    @foreach ($DOC_TYPES as $k => $v)
                                        <div class="item minus capitalizes heirDocTypeDropdown" data-value="{{ $k }}" @if ($k == old("docType2")) class="active selected" @endif>
                                            {{ $v }}
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="two fields">
                        <div class="field">
                            <label for="numberIdentify">Número de Identificación</label>
                            <div class="ui input">
                                <input readonly class="grayed-input" id="numberIdentify" autocomplete="off" type="text"
                                    value="{{ $affiliate->doc_number ?? '' }}">
                            </div>
                        </div>

                        <div class="field">
                            <label for="numberIdentify">Actualización de datos</label>
                            <div class="ui input">
                                <input name="numberIdentify2" id="numberIdentify2" value="{{ old("numberIdentify2") }}" autocomplete="off" type="text">
                            </div>
                        </div>
                    </div>

                    <div class="two fields">
                        <div class="field">
                            <label for="policyHolderName">Nombre</label>
                            <div class="ui input">
                                <input id="policyHolderName" class="grayed-input" autocomplete="off" type="text"
                                    value="{{ mb_convert_case(mb_strtolower($affiliate->first_name ?? ''), MB_CASE_TITLE, 'UTF-8') }}"
                                    readonly>
                            </div>
                        </div>

                        <div class="field">
                            <label for="policyHolderName2">Actualización de datos</label>
                            <div class="ui input">
                                <input name="policyHolderName2" id="policyHolderName2" value="{{ old("policyHolderName2") }}" autocomplete="off" type="text">
                            </div>
                        </div>
                    </div>

                    <div class="two fields" style="margin-top: 10px;">
                        <div class="field">
                        </div>
                        <div class="field">
                            <button class="ui secondary button" type="submit" id="submit-button">
                                <i class="save icon"></i>
                                Actualizar
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </form>

        <form id="form-informacion-de-contacto" class="ui attached form crud-form" action="{{ secure_url('/tablero/actualizacion_datos/tomadores/' . $activity->id . '/update') }}" method="post">
            {{ csrf_field() }}
            <div class="ui styled fluid accordion">
                <div class="title">
                    <i class="dropdown icon"></i> Información de Contacto
                </div>
                <div class="content active">
                    <div class="two fields">
                        <div class="field">
                            <label for="policyHolderPhone">Teléfonos</label>
                            <div class="ui input">
                                <input readonly class="grayed-input" id="policyHolderPhone" autocomplete="off" type="text" value="{{ $affiliate->phone ?? '' }}">
                            </div>
                        </div>

                        <div class="field">
                            <label for="policyHolderPhone2">Actualización de datos</label>
                            <div class="ui input">
                                <input name="policyHolderPhone2" id="policyHolderPhone2" value="{{ old("policyHolderPhone2") }}" autocomplete="off" type="text">
                            </div>
                        </div>
                    </div>

                    <div class="two fields">
                        <div class="field">
                            <label for="policyHolderEmail">Correo electrónico de notificaciones</label>
                            <div class="ui input">
                                <input readonly class="grayed-input" name="policyHolderEmail" id="policyHolderEmail" autocomplete="off" type="email"
                                    value="{{ strtolower($policy_sort->notification_email) ?? '' }}">
                            </div>
                        </div>

                        <div class="field">
                            <label for="policyHolderEmail2">Actualización de datos</label>
                            <div class="ui input">
                                <input name="policyHolderEmail2" id="policyHolderEmail2" value="{{ old("policyHolderEmail2") }}" autocomplete="off" type="email">
                            </div>
                        </div>
                    </div>

                    <div class="two fields">
                        <div class="field">
                            <label for="emailElectronicBilling">Correo electrónico para facturación electrónica</label>
                            <div class="ui input">
                                <input readonly class="grayed-input" name="emailElectronicBilling" id="emailElectronicBilling" autocomplete="off"
                                    type="email" value="{{ strtolower($affiliate->electronic_billing_email) ?? '' }}">
                            </div>
                        </div>

                        <div class="field">
                            <label for="emailElectronicBilling2">Actualización de datos</label>
                            <div class="ui input">
                                <input name="emailElectronicBilling2" id="emailElectronicBilling2" value="{{ old("emailElectronicBilling2") }}" autocomplete="off" type="email">
                            </div>
                        </div>
                    </div>

                    <div class="two fields" style="margin-top: 10px;">
                        <div class="field">
                        </div>
                        <div class="field">
                            <button class="ui secondary button" type="submit" id="submit-button">
                                <i class="save icon"></i>
                                Actualizar
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </form>

        <form id="form-informacion-personal" class="ui attached form crud-form" action="{{ secure_url('/tablero/actualizacion_datos/tomadores/' . $activity->id . '/update') }}" method="post">
            {{ csrf_field() }}
            <div class="ui styled fluid accordion">
                <div class="title">
                    <i class="dropdown icon"></i> Información Personal
                </div>
                <div class="content active">
                    <div class="two fields">
                        <div class="field">
                            <label for="nacionalidad">Nacionalidad</label>
                            <div class="ui fluid search selection  dropdown grayed-input" id="nacionality">
                                <input type="hidden" id="nacionalidadInput">
                                <i class="dropdown icon"></i>
                                <div class="default text">Seleccionar nacionalidad</div>
                                <div class="menu paises">
                                    <!-- Las opciones de países se cargarán aquí -->
                                </div>
                            </div>
                        </div>
                        <div class="field">
                            <label for="nacionalidad">Actualización de datos</label>
                            <div class="ui fluid search selection  dropdown" id="nacionality2">
                                <input type="hidden" id="nacionalidadInput2" name="nacionalidadInput2">
                                <i class="dropdown icon"></i>
                                <div class="default text">Seleccionar nacionalidad</div>
                                <div class="menu paises">
                                    <!-- Las opciones de países se cargarán aquí -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="two fields" style="margin-top: 10px;">
                        <div class="field">
                        </div>
                        <div class="field">
                            <button class="ui secondary button" type="submit" id="submit-button">
                                <i class="save icon"></i>
                                Actualizar
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </form>

        <form id="form-informacion-de-afiliacion-cuenta" class="ui attached form crud-form" action="{{ secure_url('/tablero/actualizacion_datos/tomadores/' . $activity->id . '/update') }}" method="post">
            {{ csrf_field() }}
            <div class="ui styled fluid accordion">
                <div class="title">
                    <i class="dropdown icon"></i> Información de Afiliación/Cuenta
                </div>
                <div class="content active">
                    <div class="two fields">
                        <div class="field">
                            <label for="ibanAcccount">Cuenta IBAN</label>
                            <div class="ui input">
                                <input readonly class="grayed-input" name="ibanAcccount" id="ibanAcccount" autocomplete="off" type="text"
                                    value="{{ $affiliate->iban_account ?? '' }}">
                            </div>
                        </div>

                        <div class="field">
                            <label for="ibanAcccount2">Actualización de datos</label>
                            <div class="ui input">
                                <input name="ibanAcccount2" id="ibanAcccount2" pattern="[A-Z0-9]{1,22}" maxlength="22" autocomplete="off" type="text" value="{{ old('ibanAcccount2') }}">
                            </div>
                            <div class="ui negative message hidden" id="ibanError">El IBAN debe tener 22
                                caracteres
                                alfanuméricos.
                            </div>
                        </div>
                    </div>

                    <div class="two fields" style="margin-top: 10px;">
                        <div class="field">
                        </div>
                        <div class="field">
                            <button class="ui secondary button" type="submit" id="submit-button">
                                <i class="save icon"></i>
                                Actualizar
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </form>

        <form id="form-informacion-de-direccion" class="ui attached form crud-form" action="{{ secure_url('/tablero/actualizacion_datos/tomadores/' . $activity->id . '/update') }}" method="post">
            {{ csrf_field() }}
            <div class="ui styled fluid accordion">
                <div class="title">
                    <i class="dropdown icon"></i> Información de Dirección
                </div>
                <div class="content active">

                    <div class="two fields" style="margin-top: 10px;">
                        <div class="field">
                            <label for="province">Provincia</label>
                            <div id="province" class="ui search selection dropdown grayed-input">
                                <input readonly type="hidden" class="minus grayed-input"
                                    value="{{ $affiliate ? $affiliate->province : '' }}">
                                <i class="dropdown icon"></i>
                                <div class="default text">Selecciona uno</div>
                                <div class="menu"></div>
                            </div>
                        </div>

                        <div class="field">
                            <label for="province2">Actualización de datos</label>
                            <div id="province2" class="ui search selection dropdown">
                                <input type="hidden" class="minus" name="province2" value="{{ old('province2') }}">
                                <i class="dropdown icon"></i>
                                <div class="default text">Selecciona uno</div>
                                <div class="menu"></div>
                            </div>
                        </div>
                    </div>

                    <div class="two fields" style="margin-top: 10px;">
                        <div class=" field">
                            <label for="canton">Cantón</label>
                            <div id="canton" class="ui search selection dropdown grayed-input">
                                <input readonly type="hidden" class="minus grayed-input"
                                    value="{{ $affiliate->canton ?? '' }}">
                                <i class="dropdown icon"></i>
                                <div class="default text">Selecciona uno</div>
                                <div class="menu"></div>
                            </div>
                        </div>

                        <div class="field">
                            <label for="canton">Actualización de datos</label>
                            <div id="canton2" class="ui search selection dropdown">
                                <input type="hidden" name="canton2" class="minus" value="{{ old('canton2') }}">
                                <i class="dropdown icon"></i>
                                <div class="default text">Selecciona uno</div>
                                <div class="menu"></div>
                            </div>
                        </div>
                    </div>

                    <div class="two fields" style="margin-top: 10px;">
                        <div class="field">
                            <label for="district">Distrito</label>
                            <div id="district" class="ui search selection dropdown grayed-input">
                                <input readonly type="hidden" class="minus grayed-input"
                                    value="{{ $affiliate->district ?? '' }}">
                                <i class="dropdown icon"></i>
                                <div class="default text minus"></div>
                                <div class="menu"></div>
                            </div>
                        </div>

                        <div class="field">
                            <label for="district2">Actualización de datos</label>
                            <div id="district2" class="ui search selection dropdown">
                                <input type="hidden" name="district2" class="minus" value="{{ old('district2') }}">
                                <i class="dropdown icon"></i>
                                <div class="default text minus"></div>
                                <div class="menu"></div>
                            </div>
                        </div>
                    </div>

                    <div class="two fields" style="margin-top: 10px;">
                        <div class="field">
                            <label for="employerAddress">Dirección del trabajador (otras señas)</label>
                            <div class="ui input">
                                <input readonly class="grayed-input" id="employerAddress" autocomplete="off" type="text"
                                    value="{{ ucfirst(strtolower($affiliate->employer_address)) ?? '' }}">
                            </div>
                        </div>

                        <div class="field">
                            <label for="employerAddress2">Actualización de datos</label>
                            <div class="ui input">
                                <input name="employerAddress2" id="employerAddress2" autocomplete="off" type="text" value="{{ old('employerAddress2') }}">
                            </div>
                        </div>
                    </div>
                    
                    <div class="two fields" style="margin-top: 10px;">
                        <div class="field">
                        </div>
                        <div class="field">
                            <button class="ui secondary button" type="submit" id="submit-button">
                                <i class="save icon"></i>
                                Actualizar
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </form>

        <div class="ui styled fluid accordion">
            <div class="title">
                <i class="dropdown icon"></i> Inclusiones provisionales
            </div>
            <div class="content active">

                <div class="ui basic segment">
                    <div class="ui styled fluid accordion">
                        <div class="active title">
                            <i class="dropdown icon"></i>
                            Filtros
                        </div>
                        <div class="active content">
                            <form class="ui form small clearing search-form" method="GET">
                                <div class="three fields">
                                    <div class="field">
                                        <label>Identificación del trabajador:</label>
                                        <input value="{{ request()->input('identificacion_trabajador') ?? '' }}" type="text"
                                            name="identificacion_trabajador">
                                    </div>

                                    <div class="field">
                                        <label>Nombre del trabajador:</label>
                                        <input value="{{ request()->input('nombre_trabajador') ?? '' }}" type="text"
                                            name="nombre_trabajador">
                                    </div>

                                    <div class="field">
                                        <label>Vigencia desde:</label>
                                        <div class="ui input">
                                            <input value="{{ request()->input('vigencia_date') ?? '' }}" name="vigencia_date"
                                                type="date" />
                                        </div>
                                    </div>

                                    <div class="field">
                                        <label>Vigencia hasta:</label>
                                        <div class="ui input">
                                            <input value="{{ request()->input('vigencia_hasta_date') ?? '' }}"
                                                name="vigencia_hasta_date" type="date" />
                                        </div>
                                    </div>
                                </div>

                                <div class="fields bfilter">
                                    <div class="field">
                                        <button class="ui primary button">
                                            <i class="search icon"></i>
                                            Aplicar filtros
                                        </button>
                                    </div>

                                    <div class="field">
                                        <button class="ui secondary button" id="reset_button">
                                            <i class="undo icon"></i>
                                            Limpiar filtros
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>

                        <div class="active title">
                            <i class="dropdown icon"></i>
                            Resultados de la búsqueda
                        </div>
                        <div class=" active content">
                            <table class="ui celled sortable striped compact very table selectable" style="font-size: 0.8em; line-height: 1em;" id="results">
                                <thead>
                                    <tr>
                                        <th colspan="6"></th>
                                        <th colspan="2" style="text-align: center;"><b>Información de Auditoría</b></th>
                                        <th></th>
                                    </tr>
                                    <tr>
                                        <th>Póliza SORT</th>
                                        <th>Asegurado</th>
                                        <th>Identificación del trabajador</th>
                                        <th>Vigencia desde</th>
                                        <th>Vigencia hasta</th>
                                        <th>Descargar certificado</th>
                                        <th>Ultima vez actualizado por</th>
                                        <th>ultima vez actualizado el</th>
                                        <th>Acciones</th>
                                    </tr>
                                </thead>
                                <tbody>

                                @if ($affiliateReport && $affiliateReport->total() !== 0)
                                    @foreach ($affiliateReport as $item)
                                        <tr data-trabdata='@json($item)'>
                                            <td>{{ $policy_sort->formatNumberConsecutive() ?? '' }}</td>
                                            <td>SI</td>
                                            <td>{{ $item->identification_number  }}</td>
                                            <td>{{ \Carbon\Carbon::parse($item->created_at)->format('d/m/Y') }}</td>
                                            <td>{{ \Carbon\Carbon::parse($item->created_at)->addMonth()->format('d/m/Y') }}</td>
                                            <td>
                                                <a class="downloadPdf" data-id="{{ $policy_sort->id }}" target="_blank"
                                                    data-tooltip="Descargar reporte" data-afiliate='@json($item)'>
                                                    <i class="icon download"></i>
                                                </a>
                                            </td>
                                            <td>
                                                @php
                                                    if(isset($item->taker_updates['updated_by'])) {
                                                        $userUpdater = App\User::where('id', $item->taker_updates['updated_by'])
                                                            ->select('full_name')
                                                            ->first();

                                                        if ($userUpdater) {
                                                            echo ucfirst($userUpdater->full_name);
                                                        }
                                                    }
                                                @endphp
                                            </td>
                                            <td>
                                                @if (isset($item->taker_updates['updated_at']))
                                                    <span class="fecha-update"
                                                        fecha-update="{{ $item->taker_updates['updated_at'] }}">
                                                    </span>
                                                @endif
                                            </td>
                                            <td>
                                                <button class="ui icon button editar primary basic">
                                                    <i class="edit icon"></i>
                                                </button>
                                                </a>
                                            </td>
                                        </tr>
                                    @endforeach
                                    @else
                                        <tr>
                                            <td colspan="9" class="center aligned">
                                                Sin resultados
                                            </td>
                                        </tr>
                                    @endif
                                </tbody>
                            </table>

                            <br>
                            @if ($affiliateReport)
                                <div class="pagination">
                                    @if ($affiliateReport->hasPages())
                                        <div class="ui pagination menu">
                                            {{ $affiliateReport->appends(request()->query())->links() }}
                                        </div>
                                    @endif
                                    <p>Total de registros: {{ $affiliateReport->total() }}</p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>

            </div>
        </div>

        <form id="form-gestion-de-personas-autorizadas" class="ui attached form crud-form" action="{{ secure_url('/tablero/actualizacion_datos/tomadores/' . $activity->id . '/update') }}" method="post">
        {{ csrf_field() }}
            <div class="ui styled fluid accordion">
                <div class="title">
                    <i class="dropdown icon"></i> Gestión de personas autorizadas
                </div>
                <div class="content active">
                    <div class="one fields" style="margin-bottom: 15px;">
                        <div class="ui field">
                            <label for="filterUniqueCode">Filtrar por Código Único</label>
                            <div class="ui input">
                                <input type="search" id="filterUniqueCode" placeholder="Escribe el código único...">
                            </div>
                        </div>
                        <div class="ui field">
                            <label for="filterNombree">Filtrar por Nombre</label>
                            <div class="ui input">
                                <input type="search" id="filterNombre" placeholder="Escribe el Nombre...">
                            </div>
                        </div>
                    </div>
                    <div class="ui divider"></div>
                    <div id="fieldContainer" class="dynamic-field">
                        <div class="field-container">
                            <!-- Para nuevos registros, el id será vacío -->
                            <input type="hidden" name="id_responsible[]" value="">
    
                            <div class="four fields">
                                <div class="required field">
                                    <label for="nameResponsible">Nombre</label>
                                    <div class="ui input">
                                        <input name="name_responsible[]" id="nameResponsible" autocomplete="off"
                                            type="text" class="minus"
                                            value="{{ $affiliate->name_responsible ?: '' }}">
                                    </div>
                                </div>
    
                                <div class="required field">
                                    <label>Tipo de identificación</label>
                                    <div class="ui selection dropdown ContactDocTypeResponsibleDropdown"
                                        id="heirDocTypeResponsibleDropdown">
                                        <input type="hidden" class="minus capitalizes" name="doc_type_responsible[]"
                                            id="docTypeResponsible" value="{{ $affiliate->doc_type_responsible }}">
                                        <i class="dropdown icon"></i>
                                        <div class="default text minus capitalizes">Seleccionar tipo documento</div>
                                        <div class="menu">
                                            @foreach ($DOC_TYPES as $k => $v)
                                                <div class="item minus capitalizes heirDocTypeResponsibleDropdown"
                                                    data-value="{{ $k }}"
                                                    @if ($k == $affiliate->doc_type_responsible) class="active selected" @endif>
                                                    {{ $v }}
                                                </div>
                                            @endforeach
                                        </div>
                                    </div>
                                </div>
    
                                <div class="required field">
                                    <label for="numberIdentifyResponsible">Número de identificación</label>
                                    <div class="ui input">
                                        <input name="number_identify_responsible[]" class="minus"
                                            id="numberIdentifyResponsible" autocomplete="off" type="text"
                                            value="{{ $affiliate->doc_number_responsible ?: '' }}">
                                    </div>
                                </div>
    
                                <div class="required field">
                                    <label for="occupationResponsible">Ocupación</label>
                                    <div class="ui input">
                                        <input name="ocupation_responsible[]" class="minus" id="occupationResponsible"
                                            autocomplete="off" type="text"
                                            value="{{ $affiliate->occupation_responsible ?? '' }}">
                                    </div>
                                </div>
                            </div>
    
                            <div class="four fields">
                                <div class="required field">
                                    <label for="phoneResponsible">Teléfono</label>
                                    <div class="ui input">
                                        <input name="phone_responsible[]" id="phoneResponsible" autocomplete="off"
                                            type="text" class="minus"
                                            value="{{ $affiliate->phone_responsible ?? '' }}">
                                    </div>
                                </div>
    
                                <div class="required field">
                                    <label for="cellphoneResponsible">Celular</label>
                                    <div class="ui input">
                                        <input name="cellphone_responsible[]" id="cellphoneResponsible"
                                            autocomplete="off" type="text" class="minus"
                                            value="{{ $affiliate->cellphone_responsible ?: '' }}">
                                    </div>
                                </div>
    
                                <div class="required field">
                                    <label for="emailResponsible">Correo electrónico</label>
                                    <div class="ui input">
                                        <input name="email_responsible[]"
                                            onchange="validateDataEmail(event, 'emailResponsible')" id="emailResponsible"
                                            autocomplete="off" type="text" class="minus"
                                            value="{{ $affiliate->email_responsible }}">
                                    </div>
                                </div>
    
                                <!-- Botón Agregar/Eliminar en la misma fila que el último campo -->
                                 <div class="fields two">
                                    <div class="required field">
                                        <label for="unicodeResponsible">Código único</label>
                                        <div class="ui input">
                                            <input name="unique_code[]"
                                                id="unicodeResponsible" autocomplete="off" type="text" class="minus grayed-input" readonly
                                                value="{{ $affiliate->unique_code ?? '' }}">
                                        </div>
                                    </div>
                                    <div class="field" style="margin-top: 25px;">
                                        <button type="button" class="ui icon button add-field-btn secondary"
                                            id="addFieldButton">
                                            <i class="plus circle icon"></i>
                                        </button>
                                        <button type="button" class="ui icon button remove-field-btn red"
                                            style="display:none;">
                                            <i class="minus circle icon"></i>
                                        </button>
                                    </div>
                                 </div>
                            </div>
    
                            <div class="ui divider"></div>
                        </div>
                    </div>

                    <div class="two fields" style="margin-top: 10px;">
                        <div class="field">
                        </div>
                        <div class="field">
                            <button class="ui secondary button" type="submit" id="submit-button">
                                <i class="save icon"></i>
                                Actualizar
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
        
    </div>

    <div class="ui basic segment">
        <div class="ui error message hidden"></div>
        <div class="fields">
            <div class="six wide field">
                <a href="{{ secure_url('/tablero/actualizacion_datos/tomadores') }}" class="ui secondary button"><i
                        class="arrow left icon"></i> Volver
                </a>
            </div>
        </div>
    </div>

    <div style="display: none" id="loading" class="ui active centered inline loader"></div>
    <script type="text/javascript" src="{{ secure_url('js/policy_issuance_data.js?v=1.4.15') }}"></script>

    <style>
        .ui.grid .column {
            padding: 0.5rem 1rem !important;
        }

        .hidden {
            display: none;
        }

        .grayed-input {
            pointer-events: none;

            background-color: #f0f0f0 !important;
            /* Fondo gris claro */
            color: #888 !important;
            /* Texto gris */
            border: 1px solid #ddd !important;

            text-transform: none !important;
            /* Borde gris claro */
        }

        .dinamic_grayed_input {
            pointer-events: none;

            background-color: #f0f0f0 !important;
            /* Fondo gris claro */
            color: #888 !important;
            /* Texto gris */
            border: 1px solid #ddd !important;

            text-transform: none !important;
            /* Borde gris claro */
        }

        .pointer-events {
            pointer-events: none;
            /* Desactiva todos los eventos de puntero */
            opacity: 0.6;
            /* Opcional: Cambia la opacidad para indicar que están deshabilitados */
        }

        .grayed-area {
            background-color: #f0f0f0 !important;
            /* Fondo gris claro */
            color: #888 !important;
            /* Texto gris */
            border: 1px solid #ddd !important;
            /* Borde gris claro */
        }
    </style>

    <script type="text/javascript">
        $(document).ready(function() {
            $('.ui.accordion').accordion({
                exclusive: false
            });

            // Inicializa el accordion
            $('.ui.accordion').accordion();

            // Inicializar el dropdown
            $('.ui.dropdown').dropdown();
        });
    </script>

    <script>
        const ibanInput = document.getElementById('ibanAcccount2');
        const ibanError = document.getElementById('ibanError');

        ibanInput.addEventListener('input', function() {
            // Convertir a mayúsculas automáticamente
            ibanInput.value = ibanInput.value.toUpperCase();

            const ibanValue = ibanInput.value;

            // Validar longitud
            if (!(ibanValue.length < 1) && ibanValue.length != 22) {
                ibanError.textContent = "El IBAN debe tener 22 caracteres alfanuméricos.";
                ibanError.style.display = 'block';
                return;
            } else {
                ibanError.style.display = 'none';
            }


            const pattern = /^[A-Za-z0-9]*$/;
            if (!pattern.test(ibanValue)) {
                ibanError.textContent = "El IBAN debe contener solo caracteres alfanuméricos.";
                ibanError.style.display = 'block';
            } else {
                ibanError.style.display = 'none';
            }
        });
    </script>

    <script>
    $(document).ready(function() {
        $('#ibanAcccount2').on('input', function() {
            // Permitir solo letras y números
            $(this).val($(this).val().replace(/[^a-zA-Z0-9]/g, '').toUpperCase());

            // Limitar a 22 caracteres
            if ($(this).val().length > 22) {
                $(this).val($(this).val().substring(0, 22));
            }
        });
        // maneja todos los formularios de la pagina
        $("form").on("submit", function(e) {
            e.preventDefault();
            
            // Solo gestiona los forms que tengan la clase crud-form
            if ($(this).hasClass("crud-form")) {
                let isInvalid = false;

                $('input[name="name_responsible[]"]').each(function(index, element) {
                    if ($(this).val() === "") {
                        Swal.fire({
                            title: 'Campos incompletos',
                            html: `El nombre del responsable #${index + 1} es obligatorio`,
                            icon: 'warning',
                            showConfirmButton: false,
                            showCancelButton: true,
                            cancelButtonText: 'Cerrar',
                            cancelButtonColor: '#d33'
                        });
                        isInvalid = true;
                        return;
                    }
                });

                $('input[name="doc_type_responsible[]"]').each(function(index, element) {
                    if ($(this).val() === "") {
                        Swal.fire({
                            title: 'Campos incompletos',
                            html: `El tipo de identificación del responsable #${index + 1} es obligatorio`,
                            icon: 'warning',
                            showConfirmButton: false,
                            showCancelButton: true,
                            cancelButtonText: 'Cerrar',
                            cancelButtonColor: '#d33'
                        });
                        isInvalid = true;
                        return;
                    }
                });

                $('input[name="number_identify_responsible[]"]').each(function(index, element) {
                    if ($(this).val() === "") {
                        Swal.fire({
                            title: 'Campos incompletos',
                            html: `El número de identificación del responsable #${index + 1} es obligatorio`,
                            icon: 'warning',
                            showConfirmButton: false,
                            showCancelButton: true,
                            cancelButtonText: 'Cerrar',
                            cancelButtonColor: '#d33'
                        });
                        isInvalid = true;
                        return;
                    }
                });

                $('input[name="ocupation_responsible[]"]').each(function(index, element) {
                    if ($(this).val() === "") {
                        Swal.fire({
                            title: 'Campos incompletos',
                            html: `La ocupación del responsable #${index + 1} es obligatorio`,
                            icon: 'warning',
                            showConfirmButton: false,
                            showCancelButton: true,
                            cancelButtonText: 'Cerrar',
                            cancelButtonColor: '#d33'
                        });
                        isInvalid = true;
                        return;
                    }
                });

                $('input[name="phone_responsible[]"]').each(function(index, element) {
                    if ($(this).val() === "") {
                        Swal.fire({
                            title: 'Campos incompletos',
                            html: `El teléfono del responsable #${index + 1} es obligatorio`,
                            icon: 'warning',
                            showConfirmButton: false,
                            showCancelButton: true,
                            cancelButtonText: 'Cerrar',
                            cancelButtonColor: '#d33'
                        });
                        isInvalid = true;
                        return;
                    }
                });

                $('input[name="cellphone_responsible[]"]').each(function(index, element) {
                    if ($(this).val() === "") {
                        Swal.fire({
                            title: 'Campos incompletos',
                            html: `El celular del responsable #${index + 1} es obligatorio`,
                            icon: 'warning',
                            showConfirmButton: false,
                            showCancelButton: true,
                            cancelButtonText: 'Cerrar',
                            cancelButtonColor: '#d33'
                        });
                        isInvalid = true;
                        return;
                    }
                });

                $('input[name="email_responsible[]"]').each(function(index, element) {
                    if ($(this).val() === "") {
                        Swal.fire({
                            title: 'Campos incompletos',
                            html: `El correo electrónico del responsable #${index + 1} es obligatorio`,
                            icon: 'warning',
                            showConfirmButton: false,
                            showCancelButton: true,
                            cancelButtonText: 'Cerrar',
                            cancelButtonColor: '#d33'
                        });
                        isInvalid = true;
                        return;
                    }
                });

                // Validación del IBAN
                const ibanAcccount2Value = $('#ibanAcccount2').val().trim();
                if (ibanAcccount2Value !== '' && ibanAcccount2Value.length !== 22) {
                    isInvalid = true;

                    Swal.fire({
                        title: 'Campos incompletos',
                        html: `Ingrese correctamente la cuenta IBAN`,
                        icon: 'warning',
                        showConfirmButton: false,
                        showCancelButton: true,
                        cancelButtonText: 'Cerrar',
                        cancelButtonColor: '#d33'
                    });
                }

                // Validación del correo electrónico de notificaciones
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                const valuePolicyHolderEmail2 = $('#policyHolderEmail2').val().trim();
                if (valuePolicyHolderEmail2 !== '' && !emailRegex.test(valuePolicyHolderEmail2)) {
                    isInvalid = true;

                    Swal.fire({
                        title: 'Campos incompletos',
                        html: `Ingrese correctamente el correo electrónico de notificaciones`,
                        icon: 'warning',
                        showConfirmButton: false,
                        showCancelButton: true,
                        cancelButtonText: 'Cerrar',
                        cancelButtonColor: '#d33'
                    });
                }

                // Validación del correo electrónico de notificaciones
                const valueEmailElectronicBilling2 = $('#emailElectronicBilling2').val().trim();
                if (valueEmailElectronicBilling2 !== '' && !emailRegex.test(valueEmailElectronicBilling2)) {
                    isInvalid = true;

                    Swal.fire({
                        title: 'Campos incompletos',
                        html: `Ingrese correctamente el correo electrónico para facturación electrónica`,
                        icon: 'warning',
                        showConfirmButton: false,
                        showCancelButton: true,
                        cancelButtonText: 'Cerrar',
                        cancelButtonColor: '#d33'
                    });
                }

                if (isInvalid) {
                    return;
                }

                Swal.fire({
                    title: '¿Estás seguro?',
                    text: "¿Deseas enviar este formulario?",
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonText: 'Sí, enviar',
                    cancelButtonText: 'Cancelar'
                }).then((result) => {
                    if (result.isConfirmed) {
                        loadingMain(true);
                        this.submit(); // Envía el formulario si se confirma
                    }
                });
            } else if ($(this).hasClass("search-form")) {
                this.submit();
            }
        });
    });

    $(document).ready(function () {

        jsonData();

        async function cargarPaises(menu, paisesData) {
            // Limpiar el menú antes de agregar nuevos elementos
            menu.empty();

            // Agregar los países al menú
            paisesData.forEach(pais => {
                menu.append(`
                    <div class="item" data-value="${pais.country_short_name}">
                        <i class="${pais.country_short_name.toLowerCase()} flag"></i> ${pais.country_name}
                    </div>
                `);
            });
        }

        async function jsonData() {
            try {
                let nationality_affiliate = "{{ $affiliate->country }}";
                // Fetch de los datos de los países
                const response = await fetch("/js/paises.json");
                if (!response.ok) throw new Error(`Error al cargar el archivo: ${response.statusText}`);

                const paisesData = await response.json();

                // Seleccionar el menú donde se cargarán los países
                const menu = $('.paises');

                // Cargar los países en el menú
                await cargarPaises(menu, paisesData);

                // Establecer el valor seleccionado basado en el dato inicial
                const nationality = nationality_affiliate ? nationality_affiliate : null;

                if (nationality) {
                    const selectedItem = menu.find(`.item[data-value="${nationality}"]`);

                    if (selectedItem.length > 0) {
                        selectedItem.addClass('active selected');
                        $('#nacionality').dropdown('set selected', nationality);
                    }
                }

                // Inicializar el dropdown después de cargar los datos
                $('#nacionality').dropdown();
            } catch (error) {
                console.error("Error al cargar los datos:", error);
            }
        }

        const provinceValue = $("#province2").dropdown("get value");
        const cantonValue = $("#canton2").dropdown("get value");
        const districtValue = $("#district2").dropdown("get value");



        // json costarica
        let costarica = {};

        $.getJSON("/js/costarica.json", function (json) {
            costarica = json["province"];
            populateDropdown($("#province2"), costarica);

            // Inicializa valores seleccionados si existen
            if (provinceValue) {
                populateCantons(provinceValue);
                $("#province2").dropdown("set selected", provinceValue);
            }

            if (cantonValue && provinceValue) {
                populateDistricts(provinceValue, cantonValue);
                $("#canton2").dropdown("set selected", cantonValue);
            }

            if (districtValue && cantonValue && provinceValue) {
                $("#district2").dropdown("set selected", districtValue);
            }

            // Manejar cambios en la selección de provincia
            $("#province2").change(function () {
                const province = $(this).dropdown("get value");
                populateCantons(province);
            });

            // Manejar cambios en la selección de cantón
            $("#canton2").change(function () {
                const canton = $(this).dropdown("get value");
                const province = $("#province2").dropdown("get value");
                if (canton && province) {
                    populateDistricts(province, canton);
                }
            });
        });

        // Función para poblar provincias, cantones o distritos
        function populateDropdown(dropdown, items) {
            dropdown.dropdown("clear");
            dropdown.find(".menu").empty();
            items.forEach(item => {
                const itemName = item.name.charAt(0).toUpperCase() + item.name.slice(1).toLowerCase();
                dropdown.find(".menu").append(
                    `<div class="item" data-value="${item.code}">${itemName}</div>`
                );
            });
            // Volver a inicializar el dropdown después de actualizar los elementos
            dropdown.dropdown("refresh");
        }

        // Función para poblar cantones según la provincia seleccionada
        function populateCantons(provinceCode) {
            const province = costarica.find(p => p.code === provinceCode);
            if (province) {
                populateDropdown($("#canton2"), province.cantons);
                const canton = province.cantons[0] ? province.cantons[0].code : null;
                if (canton) {
                    $("#canton2").dropdown("set selected", canton);
                    populateDistricts(provinceCode, canton);
                }
            }
        }

        // Función para poblar distritos según el cantón seleccionado
        function populateDistricts(provinceCode, cantonCode) {
            const province = costarica.find(p => p.code === provinceCode);
            const canton = province ? province.cantons.find(c => c.code === cantonCode) : null;
            if (canton) {
                populateDropdown($("#district2"), canton.districts);
                const district = canton.districts[0] ? canton.districts[0].code : null;
                if (district) {
                    $("#district2").dropdown("set selected", district);
                }
            }
        }
        });
    </script>

    <script>
        // Evento para el botón de descarga
        $('.downloadPdf').click(function(event) {
            event.preventDefault();
            const id = $(this).data('id'); // Obtener el ID del botón
            const afiliate = $(this).data('afiliate'); // Obtener el ID del botón

            const url =
                `/servicio/${id}/policy_sort/downloadSignalReportInclusion`; // Construir la URL

            $.ajax({
                url: url,
                type: 'POST',
                data: afiliate,
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr(
                        'content') // CSRF Token
                },
                xhrFields: {
                    responseType: 'blob' // Especificar que la respuesta será un blob
                },
                success: function(data) {
                    // Crear un enlace temporal para descargar el archivo
                    const blob = new Blob([data], {
                        type: 'application/pdf'
                    });
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download =
                        'reporte_inclusion_trabajador.pdf'; // Nombre del archivo descargado
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    window.URL.revokeObjectURL(url); // Limpiar el objeto URL
                    loadingMain(false);
                },
                error: function(xhr) {
                    console.error('Error al descargar el PDF:', xhr);
                    loadingMain(false);

                }
            });
        });
    </script>

    <script>
    $('#filterUniqueCode').on('input', function () {
        const filtro = $(this).val().trim().toLowerCase();

        $('.field-container').each(function () {
            const codigo = $(this).find('input[name="unique_code[]"]').val().trim().toLowerCase();

            // Mostrar si coincide o si el campo de búsqueda está vacío
            if (codigo.includes(filtro) || filtro === '') {
                $(this).show();
            } else {
                $(this).hide();
            }
        });
    });

    $('#filterNombre').on('input', function () {
        const filtro = $(this).val().trim().toLowerCase();

        $('.field-container').each(function () {
            const codigo = $(this).find('input[name="name_responsible[]"]').val().trim().toLowerCase();

            // Mostrar si coincide o si el campo de búsqueda está vacío
            if (codigo.includes(filtro) || filtro === '') {
                $(this).show();
            } else {
                $(this).hide();
            }
        });
    });
    </script>


    <!-- Aquí convertimos el array de PHP a JSON y lo almacenamos en una variable JavaScript -->
    <script>
        let responsables = @json($policy_contacts);

        $('#addFieldButton').on('click', function() {
            // Clonar el último conjunto de campos dinámicos
            var newFields = $('.field-container').last().clone();

            newFields.addClass('new-fields-fom'); // Añadir una clase para identificar los nuevos campos
            // Limpiar los valores de los inputs clonados
            newFields.find('input').val('');

            // Limpiar el dropdown
            newFields.find('.ContactDocTypeResponsibleDropdown').dropdown('clear');

            // Ocultar el botón de agregar en los nuevos campos y mostrar el de eliminar
            newFields.find('.add-field-btn').hide();
            newFields.find('.remove-field-btn').show();

            // Generar nuevos IDs únicos para inputs y selects clonados
            newFields.find('input, select').each(function() {
                var oldId = $(this).attr('id');
                if (oldId) {
                    var newId = oldId + '_' + Math.random().toString(36).substring(7);
                    $(this).attr('id', newId);
                    $(this).siblings('label[for="' + oldId + '"]').attr('for', newId);
                }
            });

            // Asignar el evento onchange a los inputs de email en los nuevos campos clonados
            newFields.find('input[name="email_responsible[]"]').on('change', function(event) {
                validateDataEmail(event, $(this).attr('id'));
            });

            // Añadir el nuevo conjunto de campos al contenedor
            $('#fieldContainer').append(newFields);

            // Inicializar el nuevo dropdown
            newFields.find('.ContactDocTypeResponsibleDropdown').dropdown({
                onChange: function(value, text, $choice) {
                    updateDropdownOptions();
                }
            });

            // Evento de eliminación para los campos clonados
            newFields.find('.remove-field-btn').on('click', function() {
                $(this).closest('.field-container').remove(); // field-container - dynamic-field
            });

            updateDropdownOptions();
        });

        // Eliminar campos dinámicos
        $('#fieldContainer').on('click', '.remove-field-btn', function() {
            $(this).closest('.field-container').remove(); // Remover los campos field-container - dynamic-field
        });


        // Función para actualizar las opciones de los dropdowns
        function updateDropdownOptions() {
            var selectedValues = [];
            $('.ContactDocTypeResponsibleDropdown').each(function() {
                var value = $(this).dropdown('get value');
                if (value) {
                    selectedValues.push(value);
                }
            });
        }

        function validateDataEmail(event, inputId) {
            event?.preventDefault();

            const email = $(`#${inputId}`).val();
            var emailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

            if (!emailPattern.test(email)) {
                Swal.fire({
                    icon: 'warning',
                    title: 'Un momento',
                    html: 'Correo electrónico no es válido',
                });
                return;
            }
        }

        // TODO: Solo renderiza un responsible (revisar)
        // Lógica para llenar los campos dinámicos como en el ejemplo anterior
        responsables.forEach((responsable, index) => {
            // Agregar campos dinámicos si es necesario
            if (index > 0) {
                document.getElementById('addFieldButton').click();
            }

            // Seleccionar el último conjunto de campos dinámicos
            let dynamicFields = document.querySelectorAll('.field-container'); // field-container - dynamic-field
            let currentField = dynamicFields[dynamicFields.length - 1]; // El más reciente

            // Rellenar los campos con los valores del array
            currentField.querySelector('input[name="name_responsible[]"]').value = responsable.name_responsible;
            currentField.querySelector('input[name="number_identify_responsible[]"]').value = responsable
                .number_identify_responsible;
            currentField.querySelector('input[name="ocupation_responsible[]"]').value = responsable
                .ocupation_responsible;
            currentField.querySelector('input[name="phone_responsible[]"]').value = responsable.phone_responsible;
            currentField.querySelector('input[name="cellphone_responsible[]"]').value = responsable
                .cellphone_responsible;
            currentField.querySelector('input[name="email_responsible[]"]').value = responsable.email_responsible;
            currentField.querySelector('input[name="id_responsible[]"]').value = responsable.id;
            currentField.querySelector('input[name="unique_code[]"]').value = responsable.unique_code;

            // Manejar el dropdown de tipo de identificación
            let type_identification = responsable.type_identification.split(',')[0];

            // Asegurarse de seleccionar el dropdown como objeto jQuery
            let dropdown = $(currentField).find('.ContactDocTypeResponsibleDropdown');

            // Inicializar el dropdown si es necesario
            dropdown.dropdown();

            // Establecer los valores seleccionados dinámicamente
            dropdown.dropdown('set selected', type_identification);
        });
    </script>

    <script>
        //boton reset formulario
        $('#reset_button').click(function(event) {
            event.preventDefault();

            var currentUrl = window.location.href;

            var indexOfQuery = currentUrl.indexOf('?');

            var urlWithoutParams = indexOfQuery !== -1 ? currentUrl.substring(0, indexOfQuery) : currentUrl;

            window.location.href = urlWithoutParams; // Redirige sin parámetros

        });
    </script>

    <script>
        // Guardar la posición del scroll antes de recargar
        window.onbeforeunload = function () {
            localStorage.setItem("scrollY", window.scrollY);
        };

        // Al cargar la página, restaurar la posición del scroll
        window.onload = function () {
            const scrollY = localStorage.getItem("scrollY");
            if (scrollY !== null) {
                window.scrollTo(0, parseInt(scrollY));
                localStorage.removeItem("scrollY"); // Limpia para evitar problemas en navegaciones normales
            }
        };
    </script>

    <script>
    let paisesDataT = [];

    let $openFormRow = null;
    let $openButton = null;

    $(document).ready(function () {
        $('button.editar').on('click', function () {
            var $btn = $(this);
            var $row = $btn.closest('tr');

            if ($openFormRow && $row.next()[0] === $openFormRow[0]) {
                $openFormRow.remove();
                $openFormRow = null;
                $openButton = null;
                return;
            }

            if ($openFormRow) {
                $openFormRow.remove();
            }

            var cells = $row.children('td');
            var id = cells.eq(2).text();
            var desde = formatToInputDate(cells.eq(3).text());
            var hasta = formatToInputDate(cells.eq(4).text());

            const trabData = $row.data('trabdata');

            // Crea el formulario de edición
            var $formRow = $(`
                <tr class="form-row">
                    <td colspan="9">
                        <form class="ui form small attached edit-form">
                            <div class="four fields">
                                <div class="required field">
                                    <label for="name">Nombres</label>
                                    <input type="text" name="first_name" value="${trabData?.first_name ?? ''}" required>
                                </div>

                                <div class="required field">
                                    <label for="name">Apellidos</label>
                                    <input type="text" name="last_name" value="${trabData?.last_name ?? ''}" required>
                                </div>

                                <div class="required field">
                                    <label for="name">Tipo de identificación</label>
                                    <div class="ui selection dropdown incluye_employe_drop" id="heirDocTypeDropdown">
                                        <input type="hidden" name="id_type" id="id_type" class="minus capitalizes" value="${trabData?.id_type ?? ''}" required>
                                        <i class="dropdown icon"></i>
                                        <div class="default text minus heirDocTypeDropdown">Seleccionar tipo documento</div>
                                        <div class="menu">
                                            @foreach ($DOC_TYPES as $k => $v)
                                                <div class="item minus capitalizes heirDocTypeDropdown"
                                                    data-value="{{ $k }}">
                                                    {{ $v }}
                                                </div>
                                            @endforeach
                                        </div>
                                    </div>
                                </div>

                                <div class="required field">
                                    <label for="nacionalidad">Nacionalidad</label>
                                    <div class="ui selection search dropdown" id="nacionalityT">
                                        <input type="hidden" name="nationality" id="nacionalidadInput" value="${trabData?.nationality ?? ''}" required>
                                        <i class="dropdown icon"></i>
                                        <div class="default text">Seleccionar nacionalidad</div>
                                        <div class="menu paises">
                                            <!-- Las opciones de países se cargarán aquí -->
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="four fields">
                                <div class="required field">
                                    <label for="identification_number">Número de identificación</label>
                                    <input type="text" name="identification_number" id="identification_number" value="${trabData?.identification_number ?? ''}" required>
                                </div>

                                <div class="required field">
                                    <label for="date_of_birth">Fecha de nacimiento</label>
                                    <input type="date" name="date_of_birth" class="datepicker" value="${trabData?.date_of_birth ?? ''}" required>
                                </div>

                                <div class="field">
                                    <label for="gender">Género</label>
                                    <select name="gender" class="ui dropdown incluye_employe_drop" id="genderDropdown">
                                        <option value="">Seleccionar</option>
                                        <option value="M" ${trabData?.gender && trabData?.gender == 'M' ? 'selected' : ''}>Masculino</option>
                                        <option value="F" ${trabData?.gender && trabData?.gender == 'F' ? 'selected' : ''}>Femenino</option>
                                    </select>
                                </div>

                                <div class="required field">
                                    <label for="work_shift_type">Tipo de jornada laboral</label>
                                    <select name="work_shift_type" id="work_shift_type" class="ui dropdown incluye_employe_drop" required>
                                        <option value="">Seleccionar</option>
                                        <option value="TC" ${trabData?.work_shift_type && trabData?.work_shift_type == 'TC' ? 'selected' : ''}>Tiempo completo</option>
                                        <option value="TM" ${trabData?.work_shift_type && trabData?.work_shift_type == 'TM' ? 'selected' : ''}>Tiempo medio</option>
                                        <option value="OD" ${trabData?.work_shift_type && trabData?.work_shift_type == 'OD' ? 'selected' : ''}>Ocasional contratado por días</option>
                                        <option value="OH" ${trabData?.work_shift_type && trabData?.work_shift_type == 'OH' ? 'selected' : ''}>Ocasional contratado por horas</option>
                                    </select>
                                </div>
                            </div>

                            <div class="four fields">
                                <div class="field required">
                                    <label>Salario mensual</label>
                                    <input type="number" min="0" name="monthly_salary" value="${trabData?.monthly_salary ?? ''}" required>
                                </div>

                                <div class="required field">
                                    <label for="days">Días laborados</label>
                                    <input type="number" min="0" name="days" value="${trabData?.days ?? ''}" required>
                                </div>

                                <div class="required field">
                                    <label for="hours">Horas laboradas</label>
                                    <input type="number" min="0" name="hours" value="${trabData?.hours ?? ''}" required>
                                </div>

                                <div class="required field">
                                    <label for="occupation">Ocupación</label>
                                    <input type="text" name="occupation" value="${trabData?.occupation ?? ''}" required>
                                </div>
                            </div>

                            <div class="three fields">
                                <div class="required field">
                                    <label for="validateEmailInput">Correo electrónico</label>
                                    <input type="email" name="email" id="validateEmailInput" value="${trabData?.email ?? ''}" autocomplete="off" required>
                                </div>

                                <div class="field">
                                    <label for="observation_affiliate">Observación</label>
                                    <textarea name="observation_affiliate" placeholder="Observaciones" rows="3">${trabData?.observation_affiliate ?? ''}</textarea>
                                </div>
                            </div>
                            <div class="three fields">
                                <div class="field"></div>
                                <div class="field"></div>
                                <div class="field"></div>
                                <div class="field">
                                    <label>&nbsp;</label>
                                    <button type="submit" class="ui primary button">Actualizar</button>
                                </div>
                            </div>
                        </form>
                    </td>
                </tr>
            `);

            $row.after($formRow);
            $openFormRow = $formRow;
            $openButton = $btn;


            // Inicializa el componente de tabs
            $('.menu .item').tab();
            $('.incluye_employe_drop').dropdown();


            // Cargar países (similar a tu lógica existente)
            $.getJSON("/js/paises.json", function(paises) {
                paisesDataT = paises;
                cargarPaisesT($('.paises'));
                $('#nacionalityT').dropdown();
            });

            $formRow.find('form').on('submit', function (e) {
                e.preventDefault();

                let isInvalid = false;

                // Validación del correo electrónico
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                const valueEmail2 = $('#validateEmailInput').val().trim();
                if (valueEmail2 !== '' && !emailRegex.test(valueEmail2)) {
                    isInvalid = true;

                    Swal.fire({
                        title: 'Campos incompletos',
                        html: `Ingrese correctamente el correo electrónico`,
                        icon: 'warning',
                        showConfirmButton: false,
                        showCancelButton: true,
                        cancelButtonText: 'Cerrar',
                        cancelButtonColor: '#d33'
                    });
                }

                if (isInvalid) {
                    return;
                }
                
                loadingMain(true);
                const formData = $(this).serialize();

                $.ajax({
                    url: "{{ secure_url('/tablero/actualizacion_datos/tomadores/' . $activity->id . '/update-inclusion/') }}" +"/"+ trabData?.id,
                    method: 'POST',
                    data: formData,
                    success: function (response) {
                        loadingMain(false);

                        if (response.success) {
                            $formRow.remove(); // Cierra el formulario

                            $row.data('trabdata', response?.data || trabData);

                            if (response?.data?.identification_number) {
                                cells.eq(2).text(response?.data?.identification_number);

                                if (response?.meta) {
                                    let fechaFormateada = moment(response?.meta?.updated_at).format('dddd D [de] MMMM [de] YYYY [a las] h:mm:ss A');
                                    fechaFormateada = fechaFormateada.charAt(0).toUpperCase() + fechaFormateada.slice(1);

                                    cells.eq(6).text(response?.meta?.updated_by);
                                    cells.eq(7).text(fechaFormateada);
                                }

                            }

                            Swal.fire({
                                icon: 'success',
                                title: 'Actualizado correctamente',
                                text: 'La información fue actualizada exitosamente.',
                                confirmButtonText: 'OK'
                            });
                        } else {
                            Swal.fire({
                                icon: 'error',
                                title: '¡Error!',
                                text: 'Ocurrió un problema al procesar la solicitud. Verifica los datos ingresados',
                                confirmButtonText: 'OK'
                            });
                        }
                    },
                    error: function () {
                        loadingMain(false);
                        Swal.fire({
                            icon: 'error',
                            title: '¡Error!',
                            text: 'Ocurrió un problema al procesar la solicitud. Verifica los datos ingresados',
                            confirmButtonText: 'OK'
                        });
                    }
                });
            });
        });

        function formatToInputDate(dateStr) {
            var parts = dateStr.split('/');
            return `${parts[2]}-${parts[1]}-${parts[0]}`;
        }

        function formatToDisplayDate(dateStr) {
            var parts = dateStr.split('-');
            return `${parts[2]}/${parts[1]}/${parts[0]}`;
        }

        function cargarPaisesT(menu) {
            // Limpiar el menú antes de agregar nuevos elementos
            menu.empty();

            $.each(paisesDataT, function(index, pais) {
                menu.append(`<div class="item" data-value="${pais.country_short_name}">
                            <i class="${pais.country_short_name.toLowerCase()} flag"></i> ${pais.country_name} 
                        </div>`);
            });
        }
    });
    </script>

    <script>
        document.addEventListener("DOMContentLoaded", function() {
            // Establecer el idioma a español
            moment.locale('es');

            // Seleccionar todos los elementos con la clase 'fecha-emitir'
            const fechaUpdate = document.querySelectorAll('.fecha-update');

            fechaUpdate.forEach(function(fechaElem) {
                // Obtener la fecha desde el atributo data-fecha
                let fecha = fechaElem.getAttribute('fecha-update');

                // Formatear la fecha con Moment.js
                let fechaFormateada = moment(fecha).format('dddd D [de] MMMM [de] YYYY [a las] h:mm:ss A');

                // Capitalizar solo la primera letra de la cadena
                fechaFormateada = fechaFormateada.charAt(0).toUpperCase() + fechaFormateada.slice(1);

                // Asignar el valor formateado al elemento
                fechaElem.innerText = fechaFormateada;
            });
        });
    </script>

@endsection