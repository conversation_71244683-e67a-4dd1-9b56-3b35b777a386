@extends('layouts.main')

@section('title', 'Gestión integral del siniestro - SORT')

@section('menu')
    @parent
@endsection

@section('content')
    <div class="ui basic segment">
        <!-- Encabezado del formulario -->
        <h1 class="ui header">
            Gestión integral del siniestro - SORT
            <div class="sub header">Campos con <span style="color: red;" class="required">*</span> obligatorios.</div>
        </h1>

        <div class="ui secondary segment">
            <div class="ui three columns grid">
                <div class="column"><b># de identificación:</b>
                    {{ $activity->affiliate->doc_type }} {{ $activity->affiliate->doc_number }}
                </div>
                <div class="column"><b>Nombre:</b> <a
                        href="{{ secure_url('afiliado/' . $activity->affiliate_id) }}">{{ Illuminate\Support\Str::title(mb_strtolower($activity->affiliate->full_name, 'UTF-8')) }}</a>
                </div>
                <div class="column"><b>Póliza SORT:</b>
                    <a>{{ $activity->parent_activity ? $activity->parent_activity->policy_sort->formatSortNumber() : '' }}</a>
                </div>

                <div class="column"><b>Fecha del caso:</b>
                    {{ ucfirst(strtolower($activity->created_at->formatLocalized('%A %d de %B de %Y'))) }}
                </div>
                <div class="column"><b># de aviso</b>
                    {{ $activity->gis_sort->consecutive_gis ?? '' }} </div>
                <div class="column"><b># de caso:</b>
                    {{ $activity->gis_sort->formatCaseNumberIfReported() ?? '' }}
                </div>
                <div class="column"><b>Proveedor:</b>
                    {{ mb_strtoupper(optional(optional($activity->gis_sort)->provider())->name, 'UTF-8') }}
                </div>
            </div>
        </div>

         <!-- Mostrar errores -->
         @if ($errors->any())
            <div class="ui negative message">
                <div class="header">Errores encontrados</div>
                <ul class="list">
                    @foreach ($errors->all() as $error)
                        <li>{!! $error !!}</li>
                    @endforeach
                </ul>
            </div>
        @endif

        @if (session('success'))
            <div class="ui success message">
                <div class="header">
                    Operación exitosa
                </div>
                <p>{{ session('success') }}</p>
            </div>
        @endif

        <form id="form-intermediario" class="ui attached form" action="{{ secure_url('/tablero/actualizacion_datos/asegurados/' . $activity->id . '/update') }}" method="post">
            {{ csrf_field() }}
            <div class="ui styled fluid accordion">
                <div class="title">
                    <i class="dropdown icon"></i> Información de Identificación <span style="color: red;" class="required">*</span>
                </div>
                <div class="content active">
                    <!-- Campos del formulario -->
                    <div class="two fields">
                        <div class="field">
                            <label>Tipo de identificación</label>
                            <div class="ui selection dropdown grayed-input" id="docTypeDropdown">
                                <input type="hidden">
                                <i class="dropdown icon"></i>
                                <div class="default text">Selecciona una opción</div>
                                <div class="menu">
                                    @foreach ($DOC_TYPES as $k => $v)
                                        <div class="item" data-value="{{ $k }}">
                                            {{ ucfirst(mb_strtolower($v)) }}
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        </div>

                        <div class="field">
                            <label>Actualización de datos</label>
                            <div class="ui selection dropdown" id="docTypeDropdown2">
                                <input type="hidden" name="doc_type2">
                                <i class="dropdown icon"></i>
                                <div class="default text">Selecciona una opción</div>
                                <div class="menu">
                                    @foreach ($DOC_TYPES as $k => $v)
                                        <div class="item" data-value="{{ $k }}">
                                            {{ ucfirst(mb_strtolower($v)) }}
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="two fields">
                        <div class="field">
                            <label for="docNumber">Número de identificación</label>
                            <div class="ui icon input">
                                <input id="docNumber" type="text" autocomplete="off" placeholder="12345678" class="grayed-input">
                            </div>
                        </div>
    
                        <div class="field">
                            <label for="docNumber">Actualización de datos</label>
                            <div class="ui icon input">
                                <input id="docNumber2" name="docNumber2" type="text" autocomplete="off" placeholder="12345678">
                            </div>
                        </div>
                    </div>

                    <div class="two fields">
                        <div class="field">
                            <label for="name">Nombre</label>
                            <div class="ui icon input ">
                                <input id="name" type="text" autocomplete="off" placeholder="Nombre completo" class="grayed-input">
                            </div>
                        </div>

                        <div class="field">
                            <label for="name2">Actualización de datos</label>
                            <div class="ui icon input ">
                                <input id="name2" name="name2" type="text" autocomplete="off" placeholder="Nombre completo">
                            </div>
                        </div>
                    </div>

                    <div class="two fields">
                        <div class="field">
                            <label for="last_name">Apellido</label>
                            <div class="ui icon input ">
                                <input id="last_name" type="text" autocomplete="off" placeholder="Apellido completo" class="grayed-input">
                            </div>
                        </div>

                        <div class="field">
                            <label for="last_name2">Actualización de datos</label>
                            <div class="ui icon input ">
                                <input id="last_name2" name="last_name2" type="text" autocomplete="off" placeholder="Apellido completo">
                            </div>
                        </div>
                    </div>

                    <div class="two fields" style="margin-top: 10px;">
                        <div class="field">
                        </div>
                        <div class="field">
                            <button class="ui secondary button" type="submit">
                                <i class="save icon"></i>
                                Actualizar
                            </button>
                        </div>
                    </div>
                </div>
            
                <div class="title">
                    <i class="dropdown icon"></i> Información de Contacto
                </div>
                <div class="content active">
                    <div class="two fields">
                        <div class="field">
                            <label for="phone">Celular</label>
                            <div class="ui input">
                                <input id="phone" type="tel" autocomplete="off" placeholder="8888-8888" class="grayed-input">
                            </div>
                        </div>

                        <div class="field">
                            <label for="phone">Actualización de datos</label>
                            <div class="ui input">
                                <input id="phone2" name="phone2" type="tel" autocomplete="off" placeholder="8888-8888">
                            </div>
                        </div>
                    </div>

                    <div class="two fields">
                        <div class="field">
                            <label for="email">Correo electrónico</label>
                            <div class="ui icon input">
                                <input id="email" type="email" autocomplete="off" placeholder="<EMAIL>" class="grayed-input">
                            </div>
                        </div>
                        <div class="field">
                            <label for="email2">Actualización de datos</label>
                            <div class="ui icon input">
                                <input id="email2" type="email" name="email2" autocomplete="off" placeholder="<EMAIL>">
                            </div>
                        </div>
                    </div>


                    <div class="two fields" style="margin-top: 10px;">
                        <div class="field">
                        </div>
                        <div class="field">
                            <button class="ui secondary button" type="submit">
                                <i class="save icon"></i>
                                Actualizar
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="title">
                    <i class="dropdown icon"></i> Información Personal
                </div>
                <div class="content active">
                    <div class="two fields">
                        <div class="field">
                            <label for="nacionalidad">Nacionalidad</label>
                            <div class="ui fluid search selection  dropdown grayed-input" id="nacionality">
                                <input type="hidden" id="nacionalidadInput">
                                <i class="dropdown icon"></i>
                                <div class="default text">Seleccionar nacionalidad</div>
                                <div class="menu paises">
                                    <!-- Las opciones de países se cargarán aquí -->
                                </div>
                            </div>
                        </div>
                        <div class="field">
                            <label for="nacionalidadInput2">Actualización de datos</label>
                            <div class="ui fluid search selection  dropdown" id="nacionality2">
                                <input type="hidden" id="nacionalidadInput2" name="nacionalidadInput2">
                                <i class="dropdown icon"></i>
                                <div class="default text">Seleccionar nacionalidad</div>
                                <div class="menu paises">
                                    <!-- Las opciones de países se cargarán aquí -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="two fields">
                        <div class="field">
                            <label for="civil_status">Estado civil</label>
                            <div id="civil_status" class="ui selection dropdown grayed-input">
                                <input type="hidden" class="minus">
                                <i class="dropdown icon"></i>
                                <div class="default text">Selecciona una opción</div>
                                <div class="menu">
                                    @foreach ($CIVIL_STATUS as $k => $v)
                                        <div class="item" data-value="{{ $k }}">
                                            {{ ucfirst(mb_strtolower($v)) }}
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                        
                        <div class="field">
                            <label for="civil_status2">Actualización de datos</label>
                            <div id="civil_status2" class="ui selection dropdown">
                                <input type="hidden" class="minus" name="civil_status2">
                                <i class="dropdown icon"></i>
                                <div class="default text">Selecciona una opción</div>
                                <div class="menu">
                                    @foreach ($CIVIL_STATUS as $k => $v)
                                        <div class="item" data-value="{{ $k }}">
                                            {{ ucfirst(mb_strtolower($v)) }}
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="two fields">
                        <div class="field">
                            <label>Escolaridad</label>
                            <div id="escolaridad" class="ui search selection dropdown grayed-input">
                                <input type="hidden" class="minus">
                                <i class="dropdown icon"></i>
                                <div class="default text">Selecciona una opción</div>
                                <div class="menu">
                                    @foreach ($SCHOOL_LEVELS as $k => $v)
                                        <div class="item" data-value="{{ $k }}">
                                            {{ ucfirst(mb_strtolower($v)) }}
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                        <div class="field">
                            <label>Actualización de datos</label>
                            <div id="escolaridad2" class="ui search selection dropdown">
                                <input type="hidden" class="minus" name="escolaridad2">
                                <i class="dropdown icon"></i>
                                <div class="default text">Selecciona una opción</div>
                                <div class="menu">
                                    @foreach ($SCHOOL_LEVELS as $k => $v)
                                        <div class="item" data-value="{{ $k }}">
                                            {{ ucfirst(mb_strtolower($v)) }}
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                    </div>


                    <div class="two fields" style="margin-top: 10px;">
                        <div class="field">
                        </div>
                        <div class="field">
                            <button class="ui secondary button" type="submit">
                                <i class="save icon"></i>
                                Actualizar
                            </button>
                        </div>
                    </div>
                </div>
            
                <div class="title">
                    <i class="dropdown icon"></i> Información de Afiliación/Cuenta <span style="color: red;" class="required">*</span>
                </div>
                <div class="content active">
                    <div class="two fields">
                        <div class="field">
                            <label>Condición</label>
                            <div class="ui selection dropdown grayed-input" id="condition_gis">
                                <input type="hidden">
                                <i class="dropdown icon"></i>
                                <div class="default text">Selecciona una opción</div>
                                <div class="menu">
                                    @foreach ($CONDICION as $k => $v)
                                        <div class="item" data-value="{{ $k }}">
                                            {{ ucfirst(mb_strtolower($v)) }}
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                        <div class="field">
                            <label>Actualización de datos</label>
                            <div class="ui selection dropdown" id="condition_gis2">
                                <input type="hidden" name="condition_gis2">
                                <i class="dropdown icon"></i>
                                <div class="default text">Selecciona una opción</div>
                                <div class="menu">
                                    @foreach ($CONDICION as $k => $v)
                                        <div class="item" data-value="{{ $k }}">
                                            {{ ucfirst(mb_strtolower($v)) }}
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="two fields">
                        <div class=" field">
                            <label for="iban">Cuenta IBAN</label>
                            <div class="ui icon input">
                                <input id="iban" type="text" autocomplete="off" placeholder="IBAN" class="grayed-input">
                            </div>
                        </div>
                        <div class=" field">
                            <label for="iban2">Actualización de datos</label>
                            <div class="ui icon input">
                                <input id="iban2" name="iban2" type="text" autocomplete="off" placeholder="IBAN" pattern="[A-Z0-9]{1,22}" maxlength="22">
                            </div>
                            <div class="ui negative message hidden" id="ibanError">El IBAN debe tener 22
                                caracteres
                                alfanuméricos.
                            </div>
                        </div>
                    </div>


                    <div class="two fields" style="margin-top: 10px;">
                        <div class="field">
                        </div>
                        <div class="field">
                            <button class="ui secondary button" type="submit">
                                <i class="save icon"></i>
                                Actualizar
                            </button>
                        </div>
                    </div>
                </div>
            
                <div class="title">
                    <i class="dropdown icon"></i> Información de Dirección
                </div>
                <div class="content active">

                    <div class="two fields" style="margin-top: 10px;">
                        <div class="field">
                            <label for="province">Provincia</label>
                            <div id="province" class="ui search selection dropdown grayed-input">
                                <input readonly type="hidden" class="minus grayed-input" 
                                value="{{ $activity->affiliate ? $activity->affiliate->province : '' }}">
                                <i class="dropdown icon"></i>
                                <div class="default text">Selecciona uno</div>
                                <div class="menu"></div>
                            </div>
                        </div>

                        <div class="field">
                            <label for="province2">Actualización de datos</label>
                            <div id="province2" class="ui search selection dropdown">
                                <input type="hidden" class="minus" name="province2" value="{{ old('province2') }}">
                                <i class="dropdown icon"></i>
                                <div class="default text">Selecciona uno</div>
                                <div class="menu"></div>
                            </div>
                        </div>
                    </div>

                    <div class="two fields" style="margin-top: 10px;">
                        <div class=" field">
                            <label for="canton">Cantón</label>
                            <div id="canton" class="ui search selection dropdown grayed-input">
                                <input readonly type="hidden" class="minus grayed-input" value="{{ $activity->affiliate ? $activity->affiliate->canton : '' }}">
                                <i class="dropdown icon"></i>
                                <div class="default text">Selecciona uno</div>
                                <div class="menu"></div>
                            </div>
                        </div>

                        <div class="field">
                            <label for="canton">Actualización de datos</label>
                            <div id="canton2" class="ui search selection dropdown">
                                <input type="hidden" name="canton2" class="minus" value="{{ old('canton2') }}">
                                <i class="dropdown icon"></i>
                                <div class="default text">Selecciona uno</div>
                                <div class="menu"></div>
                            </div>
                        </div>
                    </div>

                    <div class="two fields" style="margin-top: 10px;">
                        <div class="field">
                            <label for="district">Distrito</label>
                            <div id="district" class="ui search selection dropdown grayed-input">
                                <input readonly type="hidden" class="minus grayed-input" value="{{ $activity->affiliate ? $activity->affiliate->district : '' }}">
                                <i class="dropdown icon"></i>
                                <div class="default text minus"></div>
                                <div class="menu"></div>
                            </div>
                        </div>

                        <div class="field">
                            <label for="district2">Actualización de datos</label>
                            <div id="district2" class="ui search selection dropdown">
                                <input type="hidden" name="district2" class="minus" value="{{ old('district2') }}">
                                <i class="dropdown icon"></i>
                                <div class="default text minus"></div>
                                <div class="menu"></div>
                            </div>
                        </div>
                    </div>

                    <div class="two fields" style="margin-top: 10px;">
                        <div class="field">
                            <label for="employerAddress">Dirección del trabajador (otras señas)</label>
                            <div class="ui input">
                                <input readonly class="grayed-input" id="employerAddress" autocomplete="off" type="text">
                            </div>
                        </div>

                        <div class="field">
                            <label for="employerAddress2">Actualización de datos</label>
                            <div class="ui input">
                                <input name="employerAddress2" id="employerAddress2" autocomplete="off" type="text" value="{{ old('employerAddress2') }}">
                            </div>
                        </div>
                    </div>

                    <div class="two fields" style="margin-top: 10px;">
                        <div class="field">
                        </div>
                        <div class="field">
                            <button class="ui secondary button" type="submit">
                                <i class="save icon"></i>
                                Actualizar
                            </button>
                        </div>
                    </div>

                </div>
            
                <div class="title">
                    <i class="dropdown icon"></i> Información del Accidente
                </div>
                <div class="content active">

                    <div class="two fields" style="margin-top: 10px;">
                        <div class="field">
                            <label for="accident_date">Fecha del accidente</label>
                            <div class="ui input">
                                <input value="{{ $activity->gis_sort->date_accident ?? '' }}" id="accident_date" type="date" class="prompt readonly grayed-input" readonly
                                    autocomplete="off"/>
                            </div>
                        </div>

                        <div class="field">
                            <label for="accident_date2">Actualización de datos</label>
                            <div class="ui input">
                                <input value="{{ old('date_accident2') ?? '' }}"  id="accident_date2" name="accident_date2" type="date" class="prompt" autocomplete="off" />
                            </div>
                        </div>
                    </div>

                    <div class="two fields" style="margin-top: 10px;">
                        <div class="field">
                        </div>
                        <div class="field">
                            <button class="ui secondary button" type="submit">
                                <i class="save icon"></i>
                                Actualizar
                            </button>
                        </div>
                    </div>

                </div>
            </div>
        </form>
    </div>

    <div class="ui basic segment">
        <div class="ui error message hidden"></div>
        <div class="fields">
            <div class="six wide field">
                <a href="{{ secure_url('/tablero/actualizacion_datos/asegurados') }}" class="ui secondary button"><i
                        class="arrow left icon"></i> Volver
                </a>
            </div>
        </div>
    </div>

    <div style="display: none" id="loading" class="ui active centered inline loader"></div>

    <style>
        .ui.grid .column {
            padding: 0.5rem 1rem !important;
        }

        .hidden {
            display: none;
        }

        .grayed-input {
            pointer-events: none;

            background-color: #f0f0f0 !important;
            /* Fondo gris claro */
            color: #888 !important;
            /* Texto gris */
            border: 1px solid #ddd !important;

            text-transform: none !important;
            /* Borde gris claro */
        }

        .dinamic_grayed_input {
            pointer-events: none;

            background-color: #f0f0f0 !important;
            /* Fondo gris claro */
            color: #888 !important;
            /* Texto gris */
            border: 1px solid #ddd !important;

            text-transform: none !important;
            /* Borde gris claro */
        }

        .pointer-events {
            pointer-events: none;
            /* Desactiva todos los eventos de puntero */
            opacity: 0.6;
            /* Opcional: Cambia la opacidad para indicar que están deshabilitados */
        }

        .grayed-area {
            background-color: #f0f0f0 !important;
            /* Fondo gris claro */
            color: #888 !important;
            /* Texto gris */
            border: 1px solid #ddd !important;
            /* Borde gris claro */
        }
    </style>

    <script type="text/javascript" src="{{ secure_url('js/policy_issuance_data.js?v=1.4.15') }}"></script>

    <script type="text/javascript">
        $(document).ready(function() {
            $('.ui.accordion').accordion({
                exclusive: false
            });

            // Inicializa el accordion
            $('.ui.accordion').accordion();

            // Inicializar el dropdown
            $('.ui.dropdown').dropdown();
        });
    </script>

    <script>
        const ibanInput = document.getElementById('iban2');
        const ibanError = document.getElementById('ibanError');

        ibanInput.addEventListener('input', function() {
            // Convertir a mayúsculas automáticamente
            ibanInput.value = ibanInput.value.toUpperCase();

            const ibanValue = ibanInput.value;

            // Validar longitud
            if (!(ibanValue.length < 1) && ibanValue.length != 22) {
                ibanError.textContent = "El IBAN debe tener 22 caracteres alfanuméricos.";
                ibanError.style.display = 'block';
                return;
            } else {
                ibanError.style.display = 'none';
            }


            const pattern = /^[A-Za-z0-9]*$/;
            if (!pattern.test(ibanValue)) {
                ibanError.textContent = "El IBAN debe contener solo caracteres alfanuméricos.";
                ibanError.style.display = 'block';
            } else {
                ibanError.style.display = 'none';
            }
        });
    </script>

    <script>
    $(document).ready(function() {
        const {
            affiliate: {
                doc_type,
                doc_number,
                first_name,
                last_name,
                phone,
                email,
                school_level,
                country,
                civil_status,
                survival_state,
                iban_account,
                employer_address
            },
            gis_sort: {
                conditions
            }
        } = @json($activity);

        $('#docTypeDropdown').dropdown({
            onChange: function(value, text, $selectedItem) {
                if (value === 'CF') {
                    $('#nacionality').dropdown('set selected', 'CR');
                }
            }
        });

        // tomados desde activity > gis_sort
        if (doc_type) {
            $('#docTypeDropdown').dropdown('set selected', doc_type);
        }
        if (doc_number) {
            $('#docNumber').val(doc_number);
        }
        if (first_name) {
            $('#name').val(capitalizeFirstLetterOfEachWord(first_name));
        }
        if (last_name) {
            $('#last_name').val(capitalizeFirstLetterOfEachWord(last_name));
        }
        if (phone) {
            $('#phone').val(phone);
        }
        if (email) {
            $('#email').val(email);
        }
        if (school_level) {
            $('#escolaridad').dropdown('set selected', school_level);
        }
        if (civil_status) {
            $('#civil_status').dropdown('set selected', civil_status);
        }

        if (conditions) {
            $('#condition_gis').dropdown('set selected', conditions);
        }

        if (iban_account) {
            $('#iban').val(iban_account);
        }
        
        if (employer_address) {
            $('#employerAddress').val(employer_address);
        }

        jsonData();

        async function cargarPaises(menu, paisesData) {
            // Limpiar el menú antes de agregar nuevos elementos
            menu.empty();

            // Agregar los países al menú
            paisesData.forEach(pais => {
                menu.append(`
                    <div class="item" data-value="${pais.country_short_name}">
                        <i class="${pais.country_short_name.toLowerCase()} flag"></i> ${pais.country_name}
                    </div>
                `);
            });
        }

        async function jsonData() {
            try {
                // Fetch de los datos de los países
                const response = await fetch("/js/paises.json");
                if (!response.ok) throw new Error(`Error al cargar el archivo: ${response.statusText}`);

                const paisesData = await response.json();

                // Seleccionar el menú donde se cargarán los países
                const menu = $('.paises');

                // Cargar los países en el menú
                await cargarPaises(menu, paisesData);

                // Establecer el valor seleccionado basado en el dato inicial
                const nationality = country ? country : null;

                if (nationality) {
                    const selectedItem = menu.find(`.item[data-value="${nationality}"]`);

                    if (selectedItem.length > 0) {
                        selectedItem.addClass('active selected');
                        $('#nacionality').dropdown('set selected', nationality);
                    }
                }

                // Inicializar el dropdown después de cargar los datos
                $('#nacionality').dropdown();
            } catch (error) {
                console.error("Error al cargar los datos:", error);
            }
        }

        const provinceValue = $("#province2").dropdown("get value");
        const cantonValue = $("#canton2").dropdown("get value");
        const districtValue = $("#district2").dropdown("get value");

        // json costarica
        let costarica = {};

        $.getJSON("/js/costarica.json", function (json) {
            costarica = json["province"];
            populateDropdown($("#province2"), costarica);

            // Inicializa valores seleccionados si existen
            if (provinceValue) {
                populateCantons(provinceValue);
                $("#province2").dropdown("set selected", provinceValue);
            }

            if (cantonValue && provinceValue) {
                populateDistricts(provinceValue, cantonValue);
                $("#canton2").dropdown("set selected", cantonValue);
            }

            if (districtValue && cantonValue && provinceValue) {
                $("#district2").dropdown("set selected", districtValue);
            }

            // Manejar cambios en la selección de provincia
            $("#province2").change(function () {
                const province = $(this).dropdown("get value");
                populateCantons(province);
            });

            // Manejar cambios en la selección de cantón
            $("#canton2").change(function () {
                const canton = $(this).dropdown("get value");
                const province = $("#province2").dropdown("get value");
                if (canton && province) {
                    populateDistricts(province, canton);
                }
            });
        });

        // Función para poblar provincias, cantones o distritos
        function populateDropdown(dropdown, items) {
            dropdown.dropdown("clear");
            dropdown.find(".menu").empty();
            items.forEach(item => {
                const itemName = item.name.charAt(0).toUpperCase() + item.name.slice(1).toLowerCase();
                dropdown.find(".menu").append(
                    `<div class="item" data-value="${item.code}">${itemName}</div>`
                );
            });
            // Volver a inicializar el dropdown después de actualizar los elementos
            dropdown.dropdown("refresh");
        }

        // Función para poblar cantones según la provincia seleccionada
        function populateCantons(provinceCode) {
            const province = costarica.find(p => p.code === provinceCode);
            if (province) {
                populateDropdown($("#canton2"), province.cantons);
                const canton = province.cantons[0] ? province.cantons[0].code : null;
                if (canton) {
                    $("#canton").dropdown("set selected", canton);
                    populateDistricts(provinceCode, canton);
                }
            }
        }

        // Función para poblar distritos según el cantón seleccionado
        function populateDistricts(provinceCode, cantonCode) {
            const province = costarica.find(p => p.code === provinceCode);
            const canton = province ? province.cantons.find(c => c.code === cantonCode) : null;
            if (canton) {
                populateDropdown($("#district2"), canton.districts);
                const district = canton.districts[0] ? canton.districts[0].code : null;
                if (district) {
                    $("#district2").dropdown("set selected", district);
                }
            }
        }

        $('#form-intermediario').on('submit', function(e) {
            e.preventDefault(); // Detiene el envío por defecto
            isInvalid = false;

            // Validación del correo electrónico
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            const valueEmail2 = $('#email2').val().trim();
            if (valueEmail2 !== '' && !emailRegex.test(valueEmail2)) {
                isInvalid = true;

                Swal.fire({
                    title: 'Campos incompletos',
                    html: `Ingrese correctamente el correo electrónico`,
                    icon: 'warning',
                    showConfirmButton: false,
                    showCancelButton: true,
                    cancelButtonText: 'Cerrar',
                    cancelButtonColor: '#d33'
                });
            }

            // Validación del IBAN
            const iban2Value = $('#iban2').val().trim();
            if (iban2Value !== '' && iban2Value.length !== 22) {
                isInvalid = true;

                Swal.fire({
                    title: 'Campos incompletos',
                    html: `Ingrese correctamente la cuenta IBAN`,
                    icon: 'warning',
                    showConfirmButton: false,
                    showCancelButton: true,
                    cancelButtonText: 'Cerrar',
                    cancelButtonColor: '#d33'
                });
            }

            if (isInvalid) {
                return;
            }
            
            Swal.fire({
                title: '¿Estás seguro?',
                text: "¿Deseas enviar este formulario?",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'Sí, enviar',
                cancelButtonText: 'Cancelar'
            }).then((result) => {
                if (result.isConfirmed) {
                    loadingMain(true);
                    this.submit(); // Envía el formulario si se confirma
                }
            });
        });
    });
    </script>

    <script>
        const capitalizeFirstLetterOfEachWord = (string) => {

            if (string) {
                return string
                    .split(' ') // Divide el string en palabras
                    .map(word => {
                        // Si el primer carácter es una letra, lo capitaliza
                        if (word.length > 0 && /[A-Za-z]/.test(word.charAt(0))) {
                            return word.charAt(0).toUpperCase() + word.slice(1)
                                .toLowerCase(); // Capitaliza la primera letra y convierte el resto a minúscula
                        }
                        return word; // De lo contrario, devuelve la palabra tal cual (por ejemplo, si es un número)
                    })
                    .join(' '); // Une las palabras nuevamente en un string
            }
            return ''
        };
    </script>
@endsection