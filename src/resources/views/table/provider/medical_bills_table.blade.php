@extends('table.provider.menu.provider_data')

@section('title_section', 'Tablero de cuentas médicas')

@section('content_provider_data')
    <div class="ui basic segment">

        <div class="ui styled fluid accordion">
            <div class="active title">
                <i class="dropdown icon"></i>
                Filtros
            </div>
            <div class="active content">
                <form class="ui form small clearing" method="GET">
                    {{ csrf_field() }}

                    <div class="field">
                        <label>Fecha de radicación pre factura</label>
                        <div class="two fields">
                            <div class="field wide three">
                                <input data-value="{{request()->input('action_start_date_submit')}}"
                                       placeholder="Desde" type="text" name="action_start_date" class="datepicker">
                            </div>
                            <div class="field wide three">
                                <input data-value="{{request()->input('action_end_date_submit')}}"
                                       placeholder="Hasta" type="text" name="action_end_date" class="datepicker">
                            </div>
                        </div>
                    </div>


                    <div class="fields">
                        <div class="field">
                            <button class="ui primary button">
                                <i class="search icon"></i>
                                Aplicar filtros
                            </button>
                        </div>

                        <div class="field">
                            <button class="ui secondary button" id="reset" type="reset">
                                <i class="undo icon"></i>
                                Limpiar filtros
                            </button>
                        </div>

                    </div>
                </form>
            </div>

            <div class="active title">
                <i class="dropdown icon"></i>
                Resultados de la búsqueda
            </div>
            <div class="active content">
                <table class="ui celled sortable striped compact very  table selectable"
                       style="font-size: 0.8em; line-height: 1em;" id="results">
                    <thead>
                    <tr>
                        <th title="Nombre del proveedor">Nombre del proveedor</th>
                        {{-- <th title="# de pre factura"># pre factura</th> --}}
                        {{-- <th title="Tipo de pre factura">Tipo de pre factura</th> --}}
                        {{-- <th title="Valor pre factura">Valor total pre factura</th> --}}
                        {{-- <th title="Fecha de creación">Fecha radicación pre factura</th> --}}
                        <th title="# de factura electrónica"># factura electrónica</th>
                        <th title="Valor factura electrónica">Valor factura electrónica</th>
                        <th title="Fecha de radicación factura electrónica">Fecha radicación factura electrónica</th>
                        <th class="truncate" title="Tipo">Tipo</th>
                        <th class="truncate" title="Subtipo">Subtipo</th>
                        <th title="Ver formulario">Ver formulario</th>
                        <th title="Gestión">Gestión</th>
                        <th title="Reporte factura electrónica">Reporte factura electrónica</th>
                    </tr>
                    </thead>
                    @php
                        $invoiceTypes_front = [
                            'temporary_disability' => 'Facturas por Incapacidad Temporal (Art. 73, Ley N° 9078)',
                            'medical_services' => 'Facturas por Servicios Médicos',
                            'ins' => 'Facturas Art. 65 y 73, Ley N° 9078',
                        ];

                        $invoiceSubtypes_front = [
                            'ordinary' => 'Ordinaria',
                            'claim' => 'Reclamo',
                        ];
                    @endphp
                    @foreach($activities as $activity)
                        @if ($activity->service_id == \App\Service::SERVICE_SOCIAL_SECURITY_PROVIDERS_MNK)
                            <tr>
                                {{--nombre del proveedor--}}
                                <td>{{ (optional($activity->socialSecurityProvider)->provider->clap == 1 ? 'CLAP-' : '') .  ucwords(strtolower(optional($activity->socialSecurityProvider)->provider->name ?? "")) }}</td>
                                {{--id de la pre factura--}}
                                {{-- <td></td> --}}
                                {{--tipo de la pr efactura--}}
                                {{-- <td></td> --}}
                                {{--Valor de la pre factura--}}
                                {{-- <td></td> --}}
                                {{--fecha de radicación de la pre factura--}}
                                {{-- <td></td> --}}
                                {{--número de factura electronica--}}
                                <td>{{ $activity->socialSecurityProvider->id }}</td>
                                {{--valor total factura electronica--}}
                                <td>
                                    {{ $TYPE_CURRENCY[1] }}
                                    {{ number_format($activity->socialSecurityProvider->total_amount, 2, ',', '.') }}
                                </td>
                                {{--fecha radicación de la factura electronica--}}
                                <td>
                                    {{ ucfirst(strftime('%A %e de %B del %Y', strtotime($activity->socialSecurityProvider->created_at ))) }}
                                </td>
                                <td>
                                    {{ $invoiceTypes_front[trim(optional($activity->socialSecurityProvider)->invoice_type)] ?? '' }}
                                </td>
                                <td>
                                    {{ $invoiceSubtypes_front[trim(optional($activity->socialSecurityProvider)->invoice_subtype)] ?? '' }}
                                </td>
                                {{--Ver formulario--}}
                                <td>
                                    <a class="ui clickable-link ver-formulario"
                                    href="/servicio/{{ $activity->id }}/social_security_providers">
                                        <i class="external eye icon"></i>
                                    </a>
                                </td>
                                    {{--gestion--}}
                                <td>
                                {{ ucfirst(mb_strtolower($activity->state->name)) }}
                                </td>
                                {{--reporte factura electronica--}}
                                <td></td>
                            </tr>
                        @else
                            <tr>
                                {{--nombre del proveedor--}}
                                <td>{{ ucwords(strtolower($activity->medical_bill->provider_name))}}</td>
                                {{--id de la pre factura--}}
                                {{-- <td>{{ $activity->medical_bill->id }}</td> --}}
                                {{-- <td>
                                    @ if($activity->medical_bill->process_type == false)
                                        {{ "Una pre factura con multiples XML" }}

                                    @ else
                                        {{ "Una pre factura con único XML" }}
                                    @endif
                                </td> --}}
                                {{-- <td>
                                    @ if(!empty($activity->medical_bill->total_value_prefacture))
                                        {{$TYPE_CURRENCY[$activity->medical_bill->type_currency_prefacture] ?? ''}}
                                        {{number_format($activity->medical_bill->total_value_prefacture, 2, ',', '.')}}
                                    @ endif
                                </td> --}}
                                {{-- <td>
                                    {{ $activity && !empty($activity->medical_bill->created_at)
                                        ? ucfirst(strftime('%A %e de %B del %Y', strtotime($activity->medical_bill->created_at)))
                                        : 'Fecha no disponible'
                                    }}
                                </td> --}}
                                <td>
                                    {{--número de factura electronica--}}
                                    @if( $activity->state_id !== \App\States\StateMedicalBillsServiceSort::REGISTRADO &&
                                    $activity->state_id !== \App\States\StateMedicalBillsServiceSort::PREFACTURA_EN_REVISION &&
                                    $activity->state_id !== \App\States\StateMedicalBillsServiceSort::PREFACTURA_APROBADA &&
                                    $activity->state_id !== \App\States\StateMedicalBillsServiceSort::PREFACTURA_RECHAZADA)
                                        {{ $activity->medical_bill->number_electronic_invoice }}
                                    @endif
                                </td>
                                <td>
                                    {{--valor total factura electronica--}}
                                    @if( $activity->state_id !== \App\States\StateMedicalBillsServiceSort::REGISTRADO &&
                                    $activity->state_id !== \App\States\StateMedicalBillsServiceSort::PREFACTURA_EN_REVISION &&
                                    $activity->state_id !== \App\States\StateMedicalBillsServiceSort::PREFACTURA_APROBADA &&
                                    $activity->state_id !== \App\States\StateMedicalBillsServiceSort::PREFACTURA_RECHAZADA)
                                        @if(!empty($activity->medical_bill->total_value_invoice))
                                            {{$TYPE_CURRENCY[$activity->medical_bill->type_currency] ?? ''}}
                                            {{number_format($activity->medical_bill->total_value_invoice, 2, ',', '.')}}
                                        @endif
                                    @endif
                                </td>
                                <td>
                                    {{--fecha radicación de la factura electronica--}}
                                    @if( $activity->state_id !== \App\States\StateMedicalBillsServiceSort::REGISTRADO &&
                                    $activity->state_id !== \App\States\StateMedicalBillsServiceSort::PREFACTURA_EN_REVISION &&
                                    $activity->state_id !== \App\States\StateMedicalBillsServiceSort::PREFACTURA_APROBADA &&
                                    $activity->state_id !== \App\States\StateMedicalBillsServiceSort::PREFACTURA_RECHAZADA)
                                        {{ $activity && !empty($activity->medical_bill->date_created_electronic_invoice)
                                            ? ucfirst(strftime('%A %e de %B del %Y', strtotime($activity->medical_bill->date_created_electronic_invoice )))
                                            : ''
                                        }}
                                    @endif
                                </td>
                                <td></td>
                                <td></td>
                                <td>
                                    {{--Ver formulario--}}
                                    <a class="ui clickable-link ver-formulario"
                                    href="/servicio/{{ $activity->id }}/medical_bills">
                                        <i class="external eye icon"></i>
                                    </a>
                                </td>
                                <td>
                                    {{-- resultado de la pre factura --}}
                                    @if($activity->state_id == \App\States\StateMedicalBillsServiceSort::REGISTRADO ||
                                        $activity->state_id == \App\States\StateMedicalBillsServiceSort::PREFACTURA_APROBADA ||
                                        $activity->state_id == \App\States\StateMedicalBillsServiceSort::PREFACTURA_RECHAZADA)
                                        {{ucfirst(mb_strtolower($activity->state->name)) }}
                                    @endif

                                    @if($activity->state_id == \App\States\StateMedicalBillsServiceSort::PREFACTURA_EN_REVISION)
                                        Pre factura pendiente de aprobar
                                    @endif

                                    {{-- resultado factura electronica--}}
                                    @if( $activity->state_id !== \App\States\StateMedicalBillsServiceSort::REGISTRADO &&
                                    $activity->state_id !== \App\States\StateMedicalBillsServiceSort::PREFACTURA_EN_REVISION &&
                                    $activity->state_id !== \App\States\StateMedicalBillsServiceSort::PREFACTURA_APROBADA &&
                                    $activity->state_id !== \App\States\StateMedicalBillsServiceSort::PREFACTURA_RECHAZADA)
                                        {{ucfirst(mb_strtolower($activity->state->name)) }}
                                    @endif
                                </td>
                                <td>
                                    @if($activity->state_id === \App\States\StateMedicalBillsServiceSort::PREFACTURA_APROBADA)
                                        Pendiente generación factura electrónica
                                    @endif
                                </td>
                            </tr>
                        @endif
                    @endforeach
                </table>

                <br>
                <!-- Paginación -->
                @if ($activities->hasPages())
                    <div class="ui grid">
                        <div class="eight wide column left aligned"></div>
                        <div class="eight wide column right aligned" style="right: 10px;">
                            <div class="ui pagination menu">
                                {{ $activities->links() }}
                            </div>
                            <p>Total de registros: {{ $activities->total() }}</p>
                        </div>
                    </div>
                @endif
                @if (\Session::has('success'))
                    <div class="ui success message" id="success-message">
                        <i class="close icon" id="close-success-message"></i>
                        <div class="header">
                            {!! \Session::get('success') !!}
                        </div>
                    </div>
                @endif

                <!-- Mostrar errores -->
                @if ($errors->any())
                    <div class="ui negative message">
                        <div class="header">Errores encontrados</div>
                        <ul class="list">
                            @foreach ($errors->all() as $error)
                                <li>{!! $error !!}</li>
                                <!-- Permite que el HTML se interprete correctamente -->
                            @endforeach
                        </ul>
                    </div>
                @endif
            </div>
        </div>
    </div>
    <script>
        $('.ui.dropdown').dropdown();
        $('.ui.accordion').accordion();

        $('.ver-formulario').popup({
            boundary: 'body',
            content: 'Ver formulario',
            position: 'top center'
        });
    </script>
    <script>
        $(document).ready(function () {

            $('.datepicker').pickadate({
                selectYears: true,
                selectMonths: true,
                formatSubmit: 'yyyy-mm-dd'
            });

            $('#reset').click(function () {
                $('form .ui.dropdown').dropdown('clear');
                $.each($('form .datepicker'), function (k, el) {
                    console.log(el);
                    $(el).pickadate('picker').clear();
                });
            });
        });
    </script>

    <script>
        document.addEventListener("DOMContentLoaded", function () {
            // Establecer el idioma a español
            moment.locale('es');

            // Seleccionar todos los elementos con clase "actividad"
            const div_date_format = document.querySelectorAll('.div_date_format');

            div_date_format.forEach(function (date_format) {
                // Obtener la fecha desde el atributo data-fecha
                let date = date_format.getAttribute('data-fecha');

                // Verificar si date está vacío
                if (!date || date.trim() === '') {
                    date_format.querySelector('.date_format_value').innerText = '';
                } else {
                    // Formatear la fecha con Moment.js
                    let fechaFormateada = moment(date).format('dddd D [de] MMMM [de] YYYY');

                    // Capitalizar la primera letra de la fecha formateada
                    fechaFormateada = fechaFormateada.charAt(0).toUpperCase() + fechaFormateada.slice(1);

                    // Asignar la fecha formateada al elemento
                    date_format.querySelector('.date_format_value').innerText = `${fechaFormateada}`;
                }
            });
        });
    </script>
@endsection

