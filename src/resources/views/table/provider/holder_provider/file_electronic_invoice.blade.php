@extends('table.provider.menu.provider_data')

@section('title_section', 'Enviar factura electrónica')

@section('content_provider_data')

    <form id="uploadForm"
          enctype="multipart/form-data" style="margin-top: 25px;">
        {{ csrf_field() }}

        <div class="ui basic segment">
            <div class="ui styled fluid accordion">
                <div class="active title">
                    <i class="dropdown icon"></i>
                    Enviar factura electrónica
                </div>
                <div class="active content">
                    <div class="ui fluid form">
                        <div class="ui segments">
                            <div class="ui segment">
                                <div class="field">
                                    <label>Número de prefactura</label>
                                    <div class="two fields">
                                        <div class="wide three field">
                                            <input class="{{$idPreInvoice ? 'readonly' : ''}}" type="text"
                                                   name="idPreInvoice"
                                                   value="{{ ($idPreInvoice ?? '') }}" {{$idPreInvoice ? 'readonly' : ''}}>
                                        </div>
                                        @if(!isset($idPreInvoice))
                                            <div class="field">
                                                <button type="button" class="ui secondary button"
                                                        onclick="buttonValidatePreInvoice(event)"><i
                                                            class="search icon"></i> Buscar
                                                </button>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            </div>

                            @if ($errors->any())
                                <!-- Mostrar errores -->
                                <div class="ui segment">
                                    <div class="ui negative message">
                                        <div class="header">Errores encontrados</div>
                                        <ul class="list">
                                            @foreach ($errors->all() as $error)
                                                <li>{!! $error !!}</li>
                                                <!-- Permite que el HTML se interprete correctamente -->
                                            @endforeach
                                        </ul>
                                    </div>
                                </div>
                            @endif

                            <!-- Sección para cargar facturas electrónicas (XML) con arrastrar y soltar -->
                            <div class="active title">
                                <i class="dropdown icon"></i>
                                Cargar facturas electrónicas (XML)<span style="color: red;" class="required">*</span>
                            </div>
                            <div class="active content">
                                <div class="ui content grid doubling stackable" id="fileInputsContainerXml">

                                    {{--Descargar archivo modelo--}}
                                    <div class="sixteen wide column">
                                        <a href="{{ secure_url('files/MODELO_XML.xml') }}"
                                           class="ui secondary button icon"
                                           download="MODELO_XML.xml" target="_blank">
                                            Descargar archivo XML modelo
                                            <i class="download icon"></i>
                                        </a>
                                    </div>

                                    {{--Cargar archivos XML--}}
                                    <div class="sixteen wide column">
                                        <div id="dropZone" class="ui segment center aligned"
                                             ondrop="handleDrop(event)" ondragover="handleDragOver(event)">
                                            <i class="huge file code outline icon"></i>
                                            <p>Arrastra y suelta archivos XML</p>
                                            <label for="fileXml" class="ui icon secondary button">
                                                <i class="upload icon"></i>
                                                Selecciona archivos
                                                <input type="file" id="fileXml" name="xml_files[]" accept=".xml"
                                                       multiple style="display: none;"
                                                       onchange="handleFileSelect(event);">
                                            </label>
                                        </div>
                                    </div>

                                    {{-----Lista de archivos cargados----}}
                                    <div class="sixteen wide column">
                                        <table class="ui celled table">
                                            <thead>
                                            <tr>
                                                <th>Nombre del archivo</th>
                                                <th>Acción</th>
                                            </tr>
                                            </thead>
                                            <tbody id="fileTableBody">
                                            <!-- Los archivos se agregarán aquí -->
                                            </tbody>
                                        </table>
                                        <button type="button" id="removeAllButton" class="ui red button"
                                                onclick="removeAllFiles()" style="display: none;">Eliminar todo
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Sección para cargar soportes de factura electrónica (PDF) -->
                            <div class="active title">
                                <i class="dropdown icon"></i>
                                Cargar soportes de factura electrónica (PDF)<span style="color: red;" class="required">*</span>
                            </div>
                            <div class="active content">
                                <div class="ui content grid doubling stackable" id="invoiceSupportContainer">
                                    <div class="sixteen wide column">
                                        <div id="invoiceSupportDropZone" class="ui segment center aligned" ondrop="handleDropInvoiceSupport(event)" ondragover="handleDragOverInvoiceSupport(event)">
                                            <i class="huge file outline icon"></i>
                                            <p>Arrastra y suelta archivos PDF</p>
                                            <label for="invoiceSupportInput" class="ui icon secondary button">
                                                <i class="upload icon"></i>
                                                Selecciona archivos
                                                <input type="file" id="invoiceSupportInput" name="invoice_support_files[]" accept=".pdf" multiple style="display: none;" onchange="handleFileSelectInvoiceSupport(event);">
                                            </label>
                                        </div>
                                    </div>

                                    <div class="sixteen wide column">
                                        <table class="ui celled table">
                                            <thead>
                                            <tr>
                                                <th>Nombre del archivo</th>
                                                <th>Acción</th>
                                            </tr>
                                            </thead>
                                            <tbody id="invoiceSupportTableBody">
                                            <!-- Los archivos se agregarán aquí -->
                                            </tbody>
                                        </table>
                                        <button type="button" id="removeAllInvoiceSupportButton" class="ui red button" onclick="removeAllInvoiceSupportFiles()" style="display: none;">Eliminar todo</button>
                                    </div>
                                </div>
                            </div>


                            <!-- Sección para cargar otros soportes (PDF)-->
                            <div class="active title">
                                <i class="dropdown icon"></i>
                                Cargar otros soportes (PDF)
                            </div>
                            <div class="active content">
                                <div class="ui content grid doubling stackable" id="additionalSupportContainer">

                                    <!-- Cargar archivos PDF -->
                                    <div class="sixteen wide column">
                                        <div id="additionalSupportDropZone" class="ui segment center aligned"
                                             ondrop="handleDropAdditionalSupport(event)" ondragover="handleDragOverAdditionalSupport(event)">
                                            <i class="huge file pdf outline icon"></i>
                                            <p>Arrastra y suelta archivos PDF</p>
                                            <label for="additionalSupportInput" class="ui icon secondary button">
                                                <i class="upload icon"></i>
                                                Selecciona archivos
                                                <input type="file" id="additionalSupportInput" name="additional_support_files[]" accept=".pdf"
                                                       multiple style="display: none;" onchange="handleFileSelectAdditionalSupport(event);">
                                            </label>
                                        </div>
                                    </div>

                                    <!-- Lista de archivos cargados -->
                                    <div class="sixteen wide column">
                                        <table class="ui celled table">
                                            <thead>
                                            <tr>
                                                <th>Nombre del archivo</th>
                                                <th>Acción</th>
                                            </tr>
                                            </thead>
                                            <tbody id="additionalSupportTableBody">
                                            <!-- Los archivos se agregarán aquí -->
                                            </tbody>
                                        </table>
                                        <button type="button" id="removeAllAdditionalSupportButton" class="ui red button"
                                                onclick="removeAllAdditionalSupportFiles()" style="display: none;">Eliminar todo
                                        </button>
                                    </div>
                                </div>
                            </div>


                            <!-- Campo de descripción -->
                            <div class="ui segment">
                                <div class="field">
                                    <label>Descripción</label>
                                    <textarea name="description_electronic_invoice"
                                              placeholder="Añadir descripción..."></textarea>
                                </div>
                            </div>

                            <!-- Botones de acción -->
                            <div class="ui segment">
                                <div class="fields">
                                    <div class="eight wide field">
                                        <button type="submit" class="ui primary fluid button icon">
                                            <i class="save icon"></i> Cargar documentos
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>

    <style>
        .ui.segment {
            padding: 1.5rem !important;
        }

        .ui.segment.center.aligned {
            padding: 2rem !important;
            border: 1px solid #ddd;
        }

        .ui.segment .huge.file.icon {
            margin-bottom: 1rem;
        }

        .file-input-wrapper {
            margin-bottom: 1rem;
        }

        .grid > .column:first-child {
            padding-left: 0.3rem !important;
        }

        .readonly {
            background: rgba(0, 0, 0, .05) !important;
        }
    </style>

    {{--Este script es para maneja los XMLS Cargar facturas electrónicas (XML)*--}}
    <script>
        let fileXmlCounter = 0;
        const fileXmlMaxFiles = 100;
        const usedXmlFileIds = new Set();

        function handleDragOver(event) {
            event.preventDefault();
            event.dataTransfer.dropEffect = 'copy';
        }

        function handleDrop(event) {
            event.preventDefault();
            let files = event.dataTransfer.files;
            processFiles(files);
        }

        function handleFileSelect(event) {
            let files = event.target.files;
            processFiles(files);
        }

        function processFiles(files) {
            let fileTableBody = document.getElementById("fileTableBody");
            if (usedXmlFileIds.size + files.length > fileXmlMaxFiles) {
                Swal.fire({
                    icon: 'warning',
                    title: 'Límite alcanzado',
                    text: `Solo puedes subir hasta ${fileXmlMaxFiles} archivos XML.`
                });
                return;
            }

            for (let i = 0; i < files.length; i++) {
                if (files[i].type === "text/xml" || files[i].name.endsWith(".xml")) {
                    fileXmlCounter++;
                    usedXmlFileIds.add(fileXmlCounter);

                    let row = document.createElement("tr");
                    row.id = `fileRow_${fileXmlCounter}`;

                    row.innerHTML = `
                    <td>${files[i].name}</td>
                    <td><button class='ui red button mini' onclick='removeFile(${fileXmlCounter})'>Eliminar</button></td>
                `;
                    fileTableBody.appendChild(row);
                } else {
                    Swal.fire({
                        title: 'Error',
                        text: 'Por favor, sube un archivo XML válido.',
                        icon: 'warning',
                        confirmButtonText: 'Aceptar'
                    });
                }
            }

            toggleRemoveAllButton();
        }

        function removeFile(id) {
            document.getElementById(`fileRow_${id}`).remove();
            usedXmlFileIds.delete(id);
            // Limpiar el input file para permitir volver a subir los mismos archivos
            document.getElementById("fileXml").value = "";

            toggleRemoveAllButton();
        }

        function removeAllFiles() {
            usedXmlFileIds.forEach(id => {
                document.getElementById(`fileRow_${id}`).remove();
            });
            usedXmlFileIds.clear();
            fileXmlCounter = 0; // Reiniciar contador para evitar conflictos

            // Limpiar el input file para permitir volver a subir los mismos archivos
            document.getElementById("fileXml").value = "";

            toggleRemoveAllButton();
        }
        function toggleRemoveAllButton() {
            const removeAllButton = document.getElementById("removeAllButton");
            if (usedXmlFileIds.size > 0) {
                removeAllButton.style.display = 'inline-block';
            } else {
                removeAllButton.style.display = 'none';
            }
        }
    </script>

    {{--Este script es para maneja los pdfs Cargar soportes de factura electrónica (PDF) *--}}
    <script>
        let invoiceSupportCounter = 0;
        const invoiceSupportMaxFiles = 100;
        const usedInvoiceSupportIds = new Set();

        function handleDragOverInvoiceSupport(event) {
            event.preventDefault();
            event.dataTransfer.dropEffect = 'copy';
        }

        function handleDropInvoiceSupport(event) {
            event.preventDefault();
            let files = event.dataTransfer.files;
            processInvoiceSupportFiles(files);
        }

        function handleFileSelectInvoiceSupport(event) {
            let files = event.target.files;
            processInvoiceSupportFiles(files);
        }

        function processInvoiceSupportFiles(files) {
            let fileTableBody = document.getElementById("invoiceSupportTableBody");
            if (usedInvoiceSupportIds.size + files.length > invoiceSupportMaxFiles) {
                Swal.fire({
                    icon: 'warning',
                    title: 'Límite alcanzado',
                    text: `Solo puedes subir hasta ${invoiceSupportMaxFiles} archivos PDF.`
                });
                return;
            }

            for (let i = 0; i < files.length; i++) {
                if (files[i].type === "application/pdf" || files[i].name.endsWith(".pdf")) {
                    invoiceSupportCounter++;
                    usedInvoiceSupportIds.add(invoiceSupportCounter);

                    let row = document.createElement("tr");
                    row.id = `invoiceSupportRow_${invoiceSupportCounter}`;

                    row.innerHTML = `
                    <td>${files[i].name}</td>
                    <td><button class='ui red button mini' onclick='removeInvoiceSupportFile(${invoiceSupportCounter})'>Eliminar</button></td>
                `;
                    fileTableBody.appendChild(row);
                } else {
                    Swal.fire({
                        title: 'Error',
                        text: 'Por favor, sube un archivo PDF válido.',
                        icon: 'warning',
                        confirmButtonText: 'Aceptar'
                    });
                }
            }

            toggleRemoveAllInvoiceSupportButton();
        }

        function removeInvoiceSupportFile(id) {
            document.getElementById(`invoiceSupportRow_${id}`).remove();
            usedInvoiceSupportIds.delete(id);
            // Limpiar el input file para permitir volver a subir los mismos archivos
            document.getElementById("invoiceSupportInput").value = "";

            toggleRemoveAllInvoiceSupportButton();
        }

        function removeAllInvoiceSupportFiles() {
            usedInvoiceSupportIds.forEach(id => {
                document.getElementById(`invoiceSupportRow_${id}`).remove();
            });
            usedInvoiceSupportIds.clear();
            invoiceSupportCounter = 0; // Reiniciar el contador para evitar conflictos

            // Limpiar el input file para permitir volver a subir los mismos archivos
            document.getElementById("invoiceSupportInput").value = "";

            toggleRemoveAllInvoiceSupportButton();
        }


        function toggleRemoveAllInvoiceSupportButton() {
            const removeAllButton = document.getElementById("removeAllInvoiceSupportButton");
            removeAllButton.style.display = usedInvoiceSupportIds.size > 0 ? 'inline-block' : 'none';
        }
    </script>

    {{--- Este script es para maneja los pdfs Cargar otros soportes (PDF) ---}}
    <script>
        let additionalSupportCounter = 0;
        const additionalSupportMaxFiles = 100;
        const usedAdditionalSupportIds = new Set();

        function handleDragOverAdditionalSupport(event) {
            event.preventDefault();
            event.dataTransfer.dropEffect = 'copy';
        }

        function handleDropAdditionalSupport(event) {
            event.preventDefault();
            let files = event.dataTransfer.files;
            processAdditionalSupportFiles(files);
        }

        function handleFileSelectAdditionalSupport(event) {
            let files = event.target.files;
            processAdditionalSupportFiles(files);
        }

        function processAdditionalSupportFiles(files) {
            let fileTableBody = document.getElementById("additionalSupportTableBody");
            if (usedAdditionalSupportIds.size + files.length > additionalSupportMaxFiles) {
                Swal.fire({
                    icon: 'warning',
                    title: 'Límite alcanzado',
                    text: `Solo puedes subir hasta ${additionalSupportMaxFiles} archivos PDF.`
                });
                return;
            }

            for (let i = 0; i < files.length; i++) {
                if (files[i].type === "application/pdf" || files[i].name.endsWith(".pdf")) {
                    additionalSupportCounter++;
                    usedAdditionalSupportIds.add(additionalSupportCounter);

                    let row = document.createElement("tr");
                    row.id = `additionalSupportRow_${additionalSupportCounter}`;

                    row.innerHTML = `
                    <td>${files[i].name}</td>
                    <td><button class='ui red button mini' onclick='removeAdditionalSupportFile(${additionalSupportCounter})'>Eliminar</button></td>
                `;
                    fileTableBody.appendChild(row);
                } else {
                    Swal.fire({
                        title: 'Error',
                        text: 'Por favor, sube un archivo PDF válido.',
                        icon: 'warning',
                        confirmButtonText: 'Aceptar'
                    });
                }
            }

            toggleRemoveAllAdditionalSupportButton();
        }

        function removeAdditionalSupportFile(id) {
            document.getElementById(`additionalSupportRow_${id}`).remove();
            usedAdditionalSupportIds.delete(id);
            // Limpiar el input file para permitir volver a subir los mismos archivos
            document.getElementById("additionalSupportInput").value = "";

            toggleRemoveAllAdditionalSupportButton();
        }

        function removeAllAdditionalSupportFiles() {
            usedAdditionalSupportIds.forEach(id => {
                document.getElementById(`additionalSupportRow_${id}`).remove();
            });
            usedAdditionalSupportIds.clear();
            additionalSupportCounter = 0; // Reiniciar el contador para evitar conflictos

            // Limpiar el input file para permitir volver a subir los mismos archivos
            document.getElementById("additionalSupportInput").value = "";

            toggleRemoveAllAdditionalSupportButton();
        }

        function toggleRemoveAllAdditionalSupportButton() {
            const removeAllButton = document.getElementById("removeAllAdditionalSupportButton");
            removeAllButton.style.display = usedAdditionalSupportIds.size > 0 ? 'inline-block' : 'none';
        }
    </script>

    {{--Validaciones y envío del formulario--}}
    <script>

        function buttonValidatePreInvoice(e) {
            // Obtener el valor del input numPreInvoice
            let numPreInvoice = $('input[name="idPreInvoice"]').val();

            if (numPreInvoice.trim() === '') {
                Swal.fire({
                    icon: 'warning',
                    title: 'Advertencia',
                    text: 'Por favor, ingresa un número de prefactura.',
                    confirmButtonText: 'Aceptar'
                });
                return; // Detener si no se ha ingresado un número
            }

            //este es un flag para cuándo se envíe el formulario -> en este caso si todo es correcto no se enviará
            let sendForm = false;
            // Llamar a la función de validación y pasar el evento e
            validatePreInvoice(numPreInvoice, e, sendForm);
        }

        function validatePreInvoice(numPreInvoice, e, sendForm) {

            // Mostrar una ventana de espera
            Swal.fire({
                title: 'Buscando...',
                text: 'Por favor, espera mientras se valida el número de prefactura.',
                allowOutsideClick: false,
                showConfirmButton: false,
                willOpen: () => {
                    Swal.showLoading();
                }
            });

            $.ajax({
                url: '/proveedor/validar_prefactura', // Ruta hacia la cual se enviará la solicitud
                type: 'POST',
                data: {
                    numPreInvoice: numPreInvoice,
                    _token: '{{ csrf_token() }}' // Token CSRF para seguridad
                },
                success: function (response) {
                    swal.close();
                    // Capturar el mensaje de la respuesta
                    let message = response.message;

                    if (sendForm) {
                        Swal.fire({
                            icon: response.valid ? 'success' : 'error', // Mostrar icono según la validación
                            title: response.valid ? 'Número válido' : 'Número no válido',
                            text: message, // Mostrar el mensaje específico recibido del backend
                            confirmButtonText: 'Aceptar',
                            showConfirmButton: !response.valid, // Muestra el botón solo si response.valid es false
                            allowOutsideClick: false // Evita que se cierre al hacer clic afuera del cuadro
                        });
                    } else {
                        Swal.fire({
                            icon: response.valid ? 'success' : 'error', // Mostrar icono según la validación
                            title: response.valid ? 'Número válido' : 'Número no válido',
                            text: message, // Mostrar el mensaje específico recibido del backend
                            confirmButtonText: 'Aceptar',
                        });
                    }

                    // Si la validación no es exitosa, prevenir el envío del formulario
                    if (!response.valid) {
                        e.preventDefault(); // Detener el envío del formulario si la prefactura no es válida
                    }

                    if (sendForm && response.valid) {
                        // Cambiar la acción del formulario
                        const form = document.getElementById('uploadForm');
                        // Crear la nueva URL con el número de prefactura
                        const newUrl = `{{ secure_url('/proveedor') }}/radicar_factura_electronica/save/${numPreInvoice}`;
                        const newMethod = 'POST';

                        // Cambiar la acción y el método del formulario
                        form.setAttribute('action', newUrl); // Establece la nueva URL como acción del formulario
                        form.setAttribute('method', newMethod); // Cambia el método del formulario

                        // Envía el formulario
                        form.submit();
                    }
                },
                error: function () {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: 'Ocurrió un error al validar el número de prefactura. Inténtalo de nuevo.',
                        confirmButtonText: 'Cerrar'
                    });
                    e.preventDefault(); // Detener el envío del formulario en caso de error
                }
            });
        }

        $('#uploadForm').submit(function (e) {
            e.preventDefault(); // Prevenir el envío por defecto del formulario inicialmente

            // Obtener el valor del input numPreInvoice
            let numPreInvoice = $('input[name="idPreInvoice"]').val();

            // Verificar si los archivos XML de factura están presentes
            let fileCountXml = usedXmlFileIds.size; // Cuenta los archivos añadidos

            // Verificar si los archivos Cargar soportes de factura electrónica (PDF) * están presentes
            let fileCountinvoiceSupport = usedInvoiceSupportIds.size; // Cuenta los archivos añadidos

            // Mostrar mensajes de advertencia si falta algún archivo
            let errorMessage = '';

            // Si faltan archivos XML de factura
            if (fileCountXml === 0) {
                errorMessage += 'No has subido ningún archivo en la sección "Cargar facturas electrónicas (XML) *".\n';
            }

            // Si faltan archivos XML de factura
            if (fileCountinvoiceSupport === 0) {
                errorMessage += 'No has subido ningún archivo en la sección "Cargar soportes de factura electrónica (PDF) *".\n';
            }

            // Si hay errores, mostrar el mensaje
            if (errorMessage) {
                Swal.fire({
                    title: 'Campos necesarios',
                    html: `<div style="max-height: 300px; overflow-y: auto;">${errorMessage.replace(/\n/g, '<br><br>')}</div>`, // Agregar scroll si el mensaje es largo
                    icon: 'warning',
                    confirmButtonText: 'Aceptar',
                });
                return; // Salir de la función para evitar el envío del formulario
            }

            // Si todo está correcto, se llama a la función de validación
            validatePreInvoice(numPreInvoice, e, true);
        });

    </script>

@endsection
