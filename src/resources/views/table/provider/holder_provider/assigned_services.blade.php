
@extends('table.provider.menu.provider_data')

@section('content_provider_data')
    <div class="ui basic segment">

        <div class="ui styled fluid accordion">
            <div class="active title">
                <i class="dropdown icon"></i>
                Filtros
            </div>
            <div class="active content">
                <form id="filterForm" class="ui form small clearing">
                    {{ csrf_field() }}
                    <div class="three fields">
                        <div class="field">
                            <label>Número de identificación</label>
                            <input type="text"
                                   value="{{ request()->input('num_affiliate') }}"
                                   name="num_affiliate"
                                   id="num_affiliate"
                                   oninput="this.value = this.value.replace(/[^0-9]/g, '');"
                                   placeholder="Número de identificación"
                                   maxlength="25">
                        </div>
                        <div class="field">
                            <label>Nombre del trabajador</label>
                            <input type="text"
                                   value="{{ request()->input('name_affiliate') }}"
                                   name="name_affiliate"
                                   id="name_affiliate"
                                   placeholder="Nombre del trabajador">
                        </div>
                        <div class="field">
                            <label>Tipo servicio</label>
                            <select class="ui fluid search dropdown" name="state_ids[]" multiple>
                                <option value="">Tipo servicio</option>
                                @foreach ($type_service as $id => $name)
                                    <option value="{{ $id }}"
                                            {{ in_array($id, old('state_ids', [])) ? 'selected' : '' }}>
                                        {{ ucfirst(mb_strtolower($name)) }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    </div>

                    <div class="three fields">
                        <div class="field">
                            <label>Número de aviso o caso</label>
                            <input type="text" name="caso" id="caso" value="{{old('caso')}}"
                                   placeholder="Numero de aviso o caso"
                                   oninput="this.value = this.value.replace(/[^0-9AV-]/g, '');" >
                        </div>
                        <div class="field">
                            <label>Fecha</label>
                            <div class="two fields date-range">
                                <div class="field">
                                    <input data-value="{{old('action_start_date_submit')}}" placeholder="Desde"
                                           type="text" name="action_start_date" class="datepicker start-date">
                                </div>
                                <div class="field">
                                    <input data-value="{{old('action_end_date_submit')}}" placeholder="Hasta"
                                           type="text" name="action_end_date" class="datepicker end-date">
                                </div>
                            </div>
                        </div>
                        <div class="field">
                            <label>Nombre patrono</label>
                            <input type="text" name="holderPolicy" class="input_data"
                                   placeholder="Nombre patrono"
                                   value="{{ request()->input('holderPolicy') ?? '' }}">
                        </div>
                    </div>

                    <div class="three fields">
                        @php
                            $selectedProviders = (array) request()->input('provider', []);
                        @endphp

                        <div class="field">
                            <label for="provider">Proveedor</label>
                            <select class="ui fluid search dropdown" name="provider[]" id="provider" multiple>
                                @foreach ($providers as $provider)
                                    <option value="{{ $provider->id }}" {{ in_array($provider->id, $selectedProviders) ? 'selected' : '' }}>
                                        {{ $provider->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    </div>

                    <div class="fields bfilter" style="margin-top: 1em;">
                        <div class="field">
                            <button class="ui primary  button">
                                <i class="search icon"></i>
                                Aplicar filtros
                            </button>
                        </div>

                        <div class="field">
                            <button class="ui secondary  button" id="reset">
                                <i class="undo icon"></i>
                                Limpiar filtros
                            </button>
                        </div>

                    </div>
                </form>
            </div>
            {{-- </div> --}}

            <div class="active title">
                <i class="dropdown icon"></i>
                Resultados de la búsqueda
            </div>
            <div class=" active content">
                <table class="ui celled sortable striped compact very  table selectable "
                       style="font-size: 0.8em; line-height: 1em;" id="results">
                    <thead>
                    <tr>
                        <th class="truncate" title="Número de identificación">Tipo de identificación</th>
                        <th class="truncate" title="Número de identificación">Número de identificación</th>
                        <th class="truncate" title="Nombre del asegurado">Nombre del asegurado</th>
                        <th class="truncate" title="No. de Póliza SORT">Póliza SORT</th>
                        <th class="truncate" title="Fecha del caso">Fecha del caso</th>
                        <th class="truncate" title="Número del caso">Número del aviso</th>
                        <th class="truncate" title="Número del caso">Número del caso</th>
                        <th class="truncate" title="Nombre de patrono">Nombre de patrono</th>
                        <th class="truncate" title="Tipo servicio">Tipo servicio</th>
                        <th class="truncate" title="Número del caso">Estado</th>
                        <th class="truncate" title="Proveedor asignado">Proveedor asignado</th>
                        <th class="truncate" title="Ver">Ver</th>
                        <th class="truncate" title="Acciones">Acciones</th>
                        <th class="truncate" title="Solicitudes">Solicitudes</th>
                    </tr>
                    </thead>

                    <tbody>
                        @foreach($activities as $activity)
                            <tr>
                                @if($activity->service_id == \App\Service::SERVICE_MEDICAL_SERVICES_SORT_MNK)
                                    @php
                                        $gis = optional($activity->parent)->gis_sort;
                                        $policy_sort = optional(optional($activity->parent)->parent)->policy_sort;
                                    @endphp
                                    <td>{{ $DOC_TYPES[$activity->affiliate->doc_type] ?? 'N/A'}}</td>
                                    <td class="right aligned" >{{ $activity->affiliate->doc_number ?? 'N/A' }}</td>
                                    <td>{{ $activity->affiliate->full_name ? ucwords(mb_strtolower($activity->affiliate->full_name)) : 'N/A' }}</td>
                                    <td>{{ isset($policy_sort) ? optional($policy_sort)->formatSortNumber() : 'N/A' }}</td>
                                    <td> {{ isset($gis) ? ucfirst(strftime('%A %e de %B del %Y',strtotime($gis->created_at))) : 'N/A' }}</td>
                                    <td>{{ isset($gis) ? $gis->consecutive_gis : '' }}</td>
                                    <td>{{ isset($gis) ? $gis->formatCaseNumberIfReported() : '' }}</td>
                                    <td>
                                        {{-- Padre: GIS / Póliza / Cotización --}}
                                        {{ $activity->getAffiliateNameAuditMedic(3) }}
                                    </td>
                                    <td>Prestación médica</td>
                                    <td>{{ isset($activity->state->name) ? ucfirst(mb_strtolower($activity->state->name)) : 'N/A' }}</td>
                                    <td>{{ (optional(optional($activity->medical_services_sort)->provider())->clap == 1 ? 'CLAP-' : '') . optional(optional($activity->medical_services_sort)->provider())->name ?? ''}}</td>
                                    <td><a href="/servicio/{{$activity->id}}/medical_services" class="eyeiconp"><i class="external eye icon"></i></a></td>
                                    <td class="center aligned">
                                  
                                    </td>

                                    <td class="center aligned" >
                                        @if($activity->state_id == 63)
                                            <form id="mas-form-{{$activity->id}}" action="/servicio/{{$activity->id }}/crear_insumos_mot" method="POST">
                                                {{ csrf_field() }}
                                                <a href="#" class="ui clickable-link masp" onclick="event.preventDefault(); loadingMain(true); document.getElementById('mas-form-{{$activity->id}}').submit();">
                                                    <i class="plus icon"></i>
                                                </a>
                                            </form>
                                        @endif
                                    </td>
                                @endif
                                @if($activity->service_id == \App\Service::SERVICE_MEDICAL_SERVICES_SECONDARY_CARE_SORT_MNK)

                                        @php
                                            $gis = optional($activity->parent)->gis_sort;
                                        @endphp

                                    <td>{{ $DOC_TYPES[$activity->affiliate->doc_type] ?? 'N/A'}}</td>
                                    <td class="right aligned" >{{ $activity->affiliate->doc_number ?? 'N/A' }}</td>
                                    <td>{{ $activity->affiliate->full_name ? ucwords(mb_strtolower($activity->affiliate->full_name)) : 'N/A' }}</td>
                                    <td>{{ $activity->medical_services_secondary_care_sort->num_policy_sort ?? 'N/A' }}</td>
                                    <td> {{ isset($gis) ? ucfirst(strftime('%A %e de %B del %Y',strtotime($gis->created_at))) : 'N/A' }}</td>
                                    <td>{{ isset($gis) ? $gis->consecutive_gis : '' }}</td>
                                    <td>{{ isset($gis) ? $gis->formatCaseNumberIfReported() : '' }}</td>
                                    <td></td>
                                    <td>Prestación médica - atención secundaria</td>
                                    <td>{{ isset($activity->state->name) ? ucfirst(mb_strtolower($activity->state->name)) : 'N/A' }}</td>
                                    <td>{{optional(optional($activity->medical_services_secondary_care_sort)->provider())->name ?? ''}}</td>
                                    <td><a href="/servicio/{{$activity->id}}/medical_services_secondary_care" class="eyeiconp"><i class="external eye icon"></i></a></td>
                                    <td class="center aligned">
                                    </td>

                                    <td class="center aligned" >
                                        @if($activity->state_id == 63)
                                            <form id="mas-form-{{$activity->id}}" action="/servicio/{{$activity->id }}/crear_insumos_mot" method="POST">
                                                {{ csrf_field() }}
                                                <a href="#" class="ui clickable-link masp" onclick="event.preventDefault(); document.getElementById('mas-form-{{$activity->id}}').submit();">
                                                    <i class="plus icon"></i>
                                                </a>
                                            </form>
                                        @endif
                                    </td>
                                @endif
                            </tr>
                            @if($activity->service_id == \App\Service::SERVICE_SUPPLIER_MOT_MNK)
                                <tr>

                                    <td>{{ $DOC_TYPES[$activity->affiliate->doc_type] ?? 'N/A'}}</td>
                                    <td class="right aligned" >{{ $activity->affiliate->doc_number ?? 'N/A' }}</td>
                                    <td>{{ $activity->affiliate->full_name ? ucwords(mb_strtolower($activity->affiliate->full_name)) : 'N/A' }}</td>
                                    <td>{{ $activity->supplies_mot->num_policy_sort ?? 'N/A' }}</td>
                                    <td>{{ $activity->supplies_mot && isset($activity->supplies_mot->date_case)  ? ucfirst(strftime('%A %e de %B del %Y',strtotime($activity->supplies_mot->date_case))) : 'N/A' }}</td>
                                    <td></td>
                                    <td>{{ isset($activity->supplies_mot->number_case) ? sprintf('%04d',$activity->supplies_mot->number_case ): 'N/A' }}</td>
                                    <td>
                                        {{-- Padre: Prestaciones médicas / GIS / Póliza / Cotización --}}
                                        {{ $activity->getAffiliateNameAuditMedic(4) }}
                                    </td>
                                    <td>Insumos o MOT</td>
                                    <td>{{ isset($activity->state->name) ? ucfirst(mb_strtolower($activity->state->name)) : 'N/A' }}</td>
                                    <td>{{optional(optional($activity->supplies_mot)->provider())->name ?? ''}}</td>
                                    <td><a href="/servicio/{{$activity->id}}/supplies_mot" class="eyeiconp" ><i class="external eye icon"></i></a></td>
                                    <td class="center aligned" ></td>
                                    <td class="center aligned" ></td>
                                </tr>
                            @endif
                            @if($activity->service_id == \App\Service::SERVICE_MEDICAMENTOS_MNK)
                                <tr>

                                    <td>{{ $DOC_TYPES[$activity->affiliate->doc_type] ?? 'N/A'}}</td>
                                    <td class="right aligned" >{{ $activity->affiliate->doc_number ?? 'N/A' }}</td>
                                    <td>{{ $activity->affiliate->full_name ? ucwords(mb_strtolower($activity->affiliate->full_name)) : 'N/A' }}</td>
                                    <td>{{ $activity->medication->num_policy_sort ?? 'N/A' }}</td>
                                    <td>{{isset($activity->medication->date_case)  ? ucfirst(strftime('%A %e de %B del %Y',strtotime($activity->medication->date_case))) : 'N/A' }} </td>
                                    <td></td>
                                    <td>{{ isset($activity->medication->number_case) ? sprintf('%04d',$activity->medication->number_case ): 'N/A' }}</td>
                                    <td>
                                        {{-- Padre: Prestaciones médicas / GIS / Póliza / Cotización --}}
                                        {{ $activity->getAffiliateNameAuditMedic(4) }}
                                    </td>
                                    <td>Medicamentos</td>
                                    <td>{{ isset($activity->state->name) ? ucfirst(mb_strtolower($activity->state->name)) : 'N/A' }}</td>
                                    <td>{{optional(optional($activity->medication)->provider())->name ?? ''}}</td>
                                    <td><a href="/servicio/{{$activity->id}}/medication_services" class="eyeiconp" ><i class="external eye icon"></i></a></td>
                                    <td class="center aligned" ></td>
                                    <td class="center aligned" ></td>
                                </tr>
                            @endif
                            @if($activity->service_id == \App\Service::SERVICE_GIS_SORT_MNK)
                                <tr>
                                    <td>{{ $DOC_TYPES[$activity->affiliate->doc_type] ?? 'N/A'}}</td>
                                    <td class="right aligned" >{{ $activity->affiliate->doc_number ?? 'N/A' }}</td>
                                    <td>{{ mb_convert_case(mb_strtolower( $activity->affiliate->full_name ?? ''), MB_CASE_TITLE, "UTF-8") }}</td>
                                    <td>{{isset( $activity->parent->policy_sort) ? $activity->parent->policy_sort->formatSortNumber() : 'N/A'}}</td>
                                    <td>{{isset($activity->gis_sort) ? ucfirst(strftime('%A %e de %B del %Y',strtotime($activity->gis_sort->date_accident))) : 'N/A' }}</td>
                                    <td>{{isset($activity->gis_sort) ? $activity->gis_sort->consecutive_gis : 'N/A' }}</td>
                                    <td>{{isset($activity->gis_sort) ? $activity->gis_sort->formatCaseNumberIfReported() : 'N/A' }}</td>
                                    <td>
                                        {{-- Padre: Póliza --}}
                                        {{ $activity->getFormattedPolicySortAuditMedic(1) }}
                                    </td>
                                    <td>GIS SORT</td>
                                    <td>{{ isset($activity->state->name) ? ucfirst(mb_strtolower($activity->state->name)) : 'N/A' }}</td>
                                    <td>{{optional(optional($activity->gis_sort)->provider())->name ?? ''}}</td>
                                    <td><a href="/servicio/{{$activity->id}}/gis_sort" class="eyeiconp" ><i class="external eye icon"></i></a></td>
                                    <td class="center aligned" ></td>
                                    <td class="center aligned" ></td>
                                </tr>
                            @endif
                        @endforeach
                    </tbody>
                </table>

                <!-- Paginación -->
                <br>
                @if ($activities->hasPages())
                    <div class="ui grid">
                        <div class="eight wide column left aligned">
{{--                            <p>Total de registros: {{ $activities->total() }}</p>--}}
                        </div>
                        <div class="eight wide column right aligned" style="right: 10px;">
                            <div class="ui pagination menu">
                                {{ $activities->links() }}
                            </div>
                            <p>Total de registros: {{ $activities->total() }}</p>
                        </div>
                    </div>
                @endif


                @if (session('message'))
                    <div class="ui success message" id="success-message">
                        <i class="close icon" id="close-icon"></i>
                        <div class="header">
                            Completado
                        </div>
                        <p>{{ session('message') }}</p>
                    </div>
                @endif

                @if($error!='')
                    <div class="ui negative message">
                        <p>{{ $error }}</p>
                    </div>
                @endif

            </div>


    </div>

    </div>

    <script>

        $('.eyeiconp').popup({
            boundary: 'body',
            content: 'Ver formulario',
            position: 'top center'
        });

        $('.medicalp').popup({
            boundary: 'body',
            content: 'Incapacidad',
            position: 'top center'
        });
        $('.hospitalizacionp').popup({
            boundary: 'body',
            content: 'Hospitalización',
            position: 'top center'
        });

        $('.diagnosticap').popup({
            boundary: 'body',
            content: 'Orden Imágenes Dx',
            position: 'top center'
        });
        $('.especialistap').popup({
            boundary: 'body',
            content: 'Remisión a esp',
            position: 'top center'
        });
        $('.formulap').popup({
            boundary: 'body',
            content: 'Formulación',
            position: 'top center'
        });
        $('.controladosp').popup({
            boundary: 'body',
            content: 'Form. Controlada',
            position: 'top center'
        });

        $('.masp').popup({
            boundary: 'body',
            content: 'Solicitar insumo y/o mot',
            position: 'top center'
        });

        $('.ui.dropdown').dropdown({
            onChange: function(value) {
                $('#reason_cancel').val(value);
            }
        });


        $('#close-icon').on('click', function() {
            $('#success-message').hide();
        });

        $(document).ready(function() {

            $('.datepicker').pickadate({
                selectYears: true,
                selectMonths: true,
                formatSubmit: 'yyyy-mm-dd'
            });

            {{--Para resetar los filtros del formulario--}}
            $('#reset').click(function () {
                // Restablecer el formulario por su ID
                $('form')[0].reset();

                // Limpiar campos de texto y números
                $('form input[type="text"], form input[type="number"], form input[type="hidden"]').val('');
                // Desmarcar manualmente todos los checkboxes y radio buttons
                $('form input[type="checkbox"], form input[type="radio"]').prop('checked', false);

                // Limpiar dropdowns de Semantic UI
                $('form .ui.dropdown').dropdown('clear');

                // Limpiar datepickers usando Pickadate.js
                $.each($('form .datepicker'), function (k, el) {
                    console.log(el);
                    $(el).pickadate('picker').clear();
                });

                // Enviar automáticamente el formulario después de limpiar
                $('#filterForm').submit();
            });

            // var modal = document.getElementById('anulardModal');
            // modal.style.display = 'none';


        });
    </script>

    <!--inicializar datepicker y validaciones min y max -->
    <script>
        $(document).ready(function () {
            $('.date-range').each(function () {
                let $start = $(this).find('.start-date').pickadate({
                    selectYears: true,
                    selectMonths: true,
                    format: 'dd/mm/yyyy',
                    formatSubmit: 'yyyy-mm-dd',
                    hiddenName: true,
                    clear: 'Borrame',
                }).pickadate('picker');

                let $end = $(this).find('.end-date').pickadate({
                    selectYears: true,
                    selectMonths: true,
                    format: 'dd/mm/yyyy',
                    formatSubmit: 'yyyy-mm-dd',
                    hiddenName: true,
                    clear: 'Borrame',
                }).pickadate('picker');

                // Obtener valores iniciales si existen
                let startDate = $start.get('select');
                let endDate = $end.get('select');

                // Si hay un valor inicial, ajustar las restricciones
                if (startDate) {
                    $end.set('min', startDate);
                }
                if (endDate) {
                    $start.set('max', endDate);
                }

                // Evento cuando se selecciona una fecha de inicio
                $start.on('set', function (event) {
                    if (event.select) {
                        $end.set('min', $start.get('select'));
                    } else if (event.hasOwnProperty('clear')) {
                        // Si se presiona "Borrar", eliminar la restricción mínima en el otro campo
                        $end.set('min', false);
                    }
                });

                // Evento cuando se selecciona una fecha de fin
                $end.on('set', function (event) {
                    if (event.select) {
                        $start.set('max', $end.get('select'));
                    } else if (event.hasOwnProperty('clear')) {
                        // Si se presiona "Borrar", eliminar la restricción máxima en el otro campo
                        $start.set('max', false);
                    }
                })
            });
        });
    </script>
@endsection