@extends('table.provider.menu.provider_data')

@section('title_section', 'Enviar una pre factura con multiples XML')


@section('content_provider_data')

    <form id="uploadForm" action="{{ secure_url('/proveedor/radicar_prefactura/save/false') }}" method="post"
          enctype="multipart/form-data" style="margin-top: 25px;">
        {{ csrf_field() }}
        <div class="ui basic segment">
            <div class="ui styled fluid accordion">
                <div class="active title">
                    <i class="dropdown icon"></i>
                    Enviar una pre factura con multiples XML
                </div>
                <div class="active content">
                    <div class="ui fluid segments form">
                        <div class="ui segement">
                            <!-- Sección para cargar la prefactura (Excel) -->
                            <div class="active title">
                                <i class="dropdown icon"></i>
                                Carga plantilla pre factura (Excel)<span style="color: red;" class="required">*</span>
                            </div>
                            <div class="active content">
                                <!---- Descargar explicación plantilla pre factura -->
                                <button class="ui secondary button icon" type="button"
                                        onclick="download_explain_prefacture()">
                                    Descargar explicación plantilla pre factura
                                    <i class="download icon"></i>
                                </button>
                                <!---- Descargar plantilla pre factura para cargue-->
                                <button class="ui secondary button icon" type="button"
                                        onclick="download_plantilla()">
                                    Descargar plantilla pre factura
                                    <i class="download icon"></i>
                                </button>

                                <!-- Mostrar errores -->
                                @if ($errors->any())
                                    <div class="ui negative message">
                                        <div class="header">Errores encontrados</div>
                                        <ul class="list">
                                            @foreach ($errors->all() as $error)
                                                <li>{!! $error !!}</li> <!-- Se mantiene el HTML de negritas -->
                                            @endforeach
                                        </ul>
                                    </div>
                                @endif

                                <div class="ui segment center aligned" id="fileMessageXls">
                                    <i class="huge file excel outline icon"></i>
                                    <p id="noFileMessageXls">No has subido un archivo</p>

                                    <!-- Input de archivo Excel oculto -->
                                    <label for="affiliate_file" class="ui icon secondary button">
                                        <i class="upload icon"></i>
                                        <input type="file" id="affiliate_file" name="affiliate_file"
                                               accept=".xlsx, .xls"
                                               style="display: none;" onchange="updateFileName();">
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="ui segement">
                            <!-- Sección para cargar soportes de la prefacutra (PDF) -->
                            <div class="active title">
                                <i class="dropdown icon"></i>
                                Cargar soportes pre factura (PDF)<span style="color: red;" class="required">*</span>
                            </div>
                            <div class="active content">
                                <div class="ui content grid doubling stackable" id="preInvoiceSupportContainer">
                                    <div class="sixteen wide column">
                                        <div id="preInvoiceSupportDropZone" class="ui segment center aligned"
                                             ondrop="handleDropPreInvoiceSupport(event)"
                                             ondragover="handleDragOverPreInvoiceSupport(event)">
                                            <i class="huge file outline icon"></i>
                                            <p>Arrastra y suelta archivos PDF</p>
                                            <label for="preInvoiceSupportInput" class="ui icon secondary button">
                                                <i class="upload icon"></i>
                                                Selecciona archivos
                                                <input type="file" id="preInvoiceSupportInput" name="files[]"
                                                       accept=".pdf" multiple style="display: none;"
                                                       onchange="handleFileSelectPreInvoiceSupport(event);">
                                            </label>
                                        </div>
                                    </div>

                                    <div class="sixteen wide column">
                                        <table class="ui celled table">
                                            <thead>
                                            <tr>
                                                <th>Nombre del archivo</th>
                                                <th>Acción</th>
                                            </tr>
                                            </thead>
                                            <tbody id="preInvoiceSupportTableBody">
                                            <!-- Los archivos se agregarán aquí -->
                                            </tbody>
                                        </table>
                                        <button type="button" id="removeAllPreInvoiceSupportButton"
                                                class="ui red button" onclick="removeAllPreInvoiceSupportFiles()"
                                                style="display: none;">Eliminar todo
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Campo de descripción -->
                        <div class="ui segment">
                            <div class="field">
                                <label>Descripción</label>
                                <textarea name="description" placeholder="Añadir descripción..."></textarea>
                            </div>
                        </div>

                        <!-- Botones de acción -->
                        <div class="ui segment">
                            <div class="fields">
                                <div class="eight wide field">
                                    <button type="submit" class="ui primary fluid button icon">
                                        <i class="save icon"></i> Cargar documentos
                                    </button>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </form>
    <style>
        .ui.segment {
            padding: 1.5rem !important;
        }

        .ui.segment.center.aligned {
            padding: 2rem !important;
            border: 1px solid #ddd;
        }

        .ui.segment .huge.file.icon {
            margin-bottom: 1rem;
        }

        .file-input-wrapper {
            margin-bottom: 1rem;
        }

        .grid > .column:first-child {
            padding-left: 0.3rem !important;
        }
    </style>
    {{--Descargas de plantilals (explicativos y el ejemplo de cargue--}}
    <script>
        function download_plantilla() {
            Swal.fire({
                title: 'El documento se está descargando',
                text: 'Por favor, espere mientras termina el proceso.',
                allowOutsideClick: false,
                showConfirmButton: false,
                willOpen: () => {
                    Swal.showLoading(); // Iniciar loader
                }
            });

            $.ajax({
                url: '/proveedor/descargar_plantilla_proveedor',
                type: 'GET',
                headers: {
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                contentType: false,
                processData: false,
                xhrFields: {
                    responseType: 'blob'
                },
                success: function (response) {
                    Swal.close();
                    const blob = new Blob([response], {type: 'application/xlsx'});
                    const link = document.createElement('a');
                    link.href = window.URL.createObjectURL(blob);
                    link.download = 'PLANTILLA_PROVEEDOR.xlsx';
                    link.click();

                    Swal.fire({
                        icon: 'success',
                        title: '¡Descarga exitosa!',
                        text: 'El documento se ha descargado correctamente.',
                        confirmButtonText: 'Aceptar',
                        confirmButtonColor: '#000000'
                    });
                },
                error: function () {
                    Swal.close();
                    Swal.fire({
                        icon: 'error',
                        title: 'Error en la solicitud',
                        text: 'Ocurrió un problema al procesar la solicitud. Inténtalo nuevamente.',
                        confirmButtonText: 'Cerrar'
                    });
                }
            });
        }

        // Función para descargar el archivo de explicación de la plantilla de prefactura
        function download_explain_prefacture() {
            Swal.fire({
                title: 'El documento se está descargando',
                text: 'Por favor, espere mientras termina el proceso.',
                allowOutsideClick: false,
                showConfirmButton: false,
                willOpen: () => {
                    Swal.showLoading(); // Iniciar loader
                }
            });

            $.ajax({
                url: '/proveedor/descargar_explicacion_plantilla_prefactura',
                type: 'GET',
                headers: {
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                contentType: false,
                processData: false,
                xhrFields: {
                    responseType: 'blob'
                },
                success: function (response) {
                    Swal.close();
                    const blob = new Blob([response], {type: 'application/xlsx'});
                    const link = document.createElement('a');
                    link.href = window.URL.createObjectURL(blob);
                    link.download = 'Explicación_Plantilla_Prefactura.xlsx';
                    link.click();

                    Swal.fire({
                        icon: 'success',
                        title: '¡Descarga exitosa!',
                        text: 'El documento se ha descargado correctamente.',
                        confirmButtonText: 'Aceptar',
                        confirmButtonColor: '#000000'
                    });
                },
                error: function () {
                    Swal.close();
                    Swal.fire({
                        icon: 'error',
                        title: 'Error en la solicitud',
                        text: 'Ocurrió un problema al procesar la solicitud. Inténtalo nuevamente.',
                        confirmButtonText: 'Cerrar'
                    });
                }
            });
        }
    </script>
    {{--Este script es para maneja los pdfs Cargar soportes de factura electrónica (PDF) *--}}
    <script>
        let preInvoiceSupportCounter = 0;
        const preInvoiceSupportMaxFiles = 100;
        const usedPreInvoiceSupportIds = new Set();

        function handleDragOverPreInvoiceSupport(event) {
            event.preventDefault();
            event.dataTransfer.dropEffect = 'copy';
        }

        function handleDropPreInvoiceSupport(event) {
            event.preventDefault();
            let files = event.dataTransfer.files;
            processPreInvoiceSupportFiles(files);
        }

        function handleFileSelectPreInvoiceSupport(event) {
            let files = event.target.files;
            processPreInvoiceSupportFiles(files);
        }

        function processPreInvoiceSupportFiles(files) {
            let fileTableBody = document.getElementById("preInvoiceSupportTableBody");
            if (usedPreInvoiceSupportIds.size + files.length > preInvoiceSupportMaxFiles) {
                Swal.fire({
                    icon: 'warning',
                    title: 'Límite alcanzado',
                    text: `Solo puedes subir hasta ${preInvoiceSupportMaxFiles} archivos PDF.`
                });
                return;
            }

            for (let i = 0; i < files.length; i++) {
                if (files[i].type === "application/pdf" || files[i].name.endsWith(".pdf")) {
                    preInvoiceSupportCounter++;
                    usedPreInvoiceSupportIds.add(preInvoiceSupportCounter);

                    let row = document.createElement("tr");
                    row.id = `preInvoiceSupportRow_${preInvoiceSupportCounter}`;

                    row.innerHTML = `
                    <td>${files[i].name}</td>
                    <td><button class='ui red button mini' onclick='removePreInvoiceSupportFile(${preInvoiceSupportCounter})'>Eliminar</button></td>
                `;
                    fileTableBody.appendChild(row);
                } else {
                    Swal.fire({
                        title: 'Error',
                        text: 'Por favor, sube un archivo PDF válido.',
                        icon: 'warning',
                        confirmButtonText: 'Aceptar'
                    });
                }
            }

            toggleRemoveAllPreInvoiceSupportButton();
        }

        function removePreInvoiceSupportFile(id) {
            document.getElementById(`preInvoiceSupportRow_${id}`).remove();
            usedPreInvoiceSupportIds.delete(id);
            // Limpiar el input file para permitir volver a subir los mismos archivos
            document.getElementById("preInvoiceSupportInput").value = "";

            toggleRemoveAllPreInvoiceSupportButton();
        }

        function removeAllPreInvoiceSupportFiles() {
            usedPreInvoiceSupportIds.forEach(id => {
                document.getElementById(`preInvoiceSupportRow_${id}`).remove();
            });
            usedPreInvoiceSupportIds.clear();
            preInvoiceSupportCounter = 0; // Reiniciar el contador para evitar conflictos
            // Limpiar el input file para permitir volver a subir los mismos archivos
            document.getElementById("preInvoiceSupportInput").value = "";

            toggleRemoveAllPreInvoiceSupportButton();
        }


        function toggleRemoveAllPreInvoiceSupportButton() {
            const removeAllButton = document.getElementById("removeAllPreInvoiceSupportButton");
            removeAllButton.style.display = usedPreInvoiceSupportIds.size > 0 ? 'inline-block' : 'none';
        }
    </script>
    {{--Actualización del nombre del archivo--}}
    <script>
        function updateFileName() {
            const input = document.getElementById('affiliate_file');
            const file = input.files[0];

            const allowedExtensions = ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/vnd.ms-excel'];
            if (!allowedExtensions.includes(file.type)) {
                Swal.fire({
                    title: 'Error',
                    text: 'Por favor, sube un archivo de tipo Excel (.xlsx o .xls).',
                    icon: 'error',
                    confirmButtonText: 'Aceptar'
                });
                input.value = ''; // Limpiar el campo si el archivo no es válido
                document.getElementById('noFileMessageXls').innerText = 'No has subido un archivo';
                document.getElementById('noFileMessageXls').style.color = '';
                return; // Detener el proceso si el archivo no es válido
            }

            if (file) {
                document.getElementById('noFileMessageXls').innerText = file.name;
                document.getElementById('noFileMessageXls').style.color = 'green';
            } else {
                Swal.fire({
                    title: 'Advertencia',
                    text: 'Por favor, sube un archivo Excel.',
                    icon: 'warning',
                    confirmButtonText: 'Aceptar',
                });
                document.getElementById('noFileMessageXls').innerText = 'No has subido un archivo';
                document.getElementById('noFileMessageXls').style.color = '';
            }
        }
    </script>
    {{--Validaciones y envío del formulario--}}
    <script>
        $('#uploadForm').submit(function (e) {
            let emptyFileCount = 0;
            let excelMissing = false;

            // Verificar si el archivo Excel está presente
            const excelInput = document.getElementById('affiliate_file');
            if (!excelInput.files.length) {
                excelMissing = true;
            }

            // Verificar si los archivos PDF están presentes
            $('input[name="files[]"]').each(function () {
                if (!this.files.length) {
                    emptyFileCount++;
                }
            });

            // Si falta el archivo Excel
            if (excelMissing) {
                e.preventDefault();
                Swal.fire({
                    title: 'Campo necesario',
                    text: 'Debe adjuntar el archivo Excel de la plantilla pre factura.',
                    icon: 'warning',
                    confirmButtonText: 'Aceptar',
                });
            }

            // Si falta algún archivo PDF
            if (emptyFileCount > 0) {
                e.preventDefault();
                Swal.fire({
                    title: 'Campos necesarios',
                    text: `Debe adjuntar archivo${emptyFileCount > 1 ? 's' : ''} PDF en ${emptyFileCount} campo${emptyFileCount > 1 ? 's' : ''}.`,
                    icon: 'warning',
                    confirmButtonText: 'Aceptar',
                });
            }
        });
    </script>
@endsection
