<!DOCTYPE html>
<html lang="es">

<head>
    @php
        $currentClient = App\Client::current(Route::current());
    @endphp
    @if (App\Client::current(Route::current())->id)
        <title>RENAPP - {{ App\Client::current(Route::current())->name }} - @yield('title', 'RENAPP')</title>
        <link rel="shortcut icon" type="image/x-icon" href="/favicon.png" />
    @else
        <title></title>
        <link rel="shortcut icon" type="image/x-icon" href="/favicon.ico" />
    @endif

    <link rel="stylesheet" href="{{ secure_url('css/custom.css') }}">

    <link rel="stylesheet" type="text/css" href="{{ secure_url('/libs/semantic-ui/semantic.min.css') }}" />
    <script src="{{ secure_url('/js/jquery-3.1.1.min.js') }}"></script>
    <script src="{{ secure_url('/libs/semantic-ui/semantic.min.js') }}"></script>

    <!-- Pickadate -->
    {{--    <link rel="stylesheet" href="{{secure_url('/libs/pickadate.js-master/lib/themes/default.css')}}"/> --}}
    {{--    <link rel="stylesheet" href="{{secure_url('/libs/pickadate.js-master/lib/themes/default.date.css')}}"/> --}}
    {{--    <link rel="stylesheet" href="{{secure_url('/libs/pickadate.js-master/lib/themes/default.time.css')}}"/> --}}
    {{--    <script src="{{secure_url('/libs/pickadate.js-master/lib/picker.js')}}"></script> --}}
    {{--    <script src="{{secure_url('/libs/pickadate.js-master/lib/picker.date.js')}}"></script> --}}
    {{--    <script src="{{secure_url('/libs/pickadate.js-master/lib/picker.time.js')}}"></script> --}}
    {{--    <script src="{{secure_url('/libs/pickadate.js-master/lib/translations/es_ES.js')}}"></script> --}}
    <!-- Pickadate version 3.6.4 -->
    <link rel="stylesheet" href="{{ secure_url('/libs/pickadate.js-3.6.4/lib/themes/default.css') }}" />
    <link rel="stylesheet" href="{{ secure_url('/libs/pickadate.js-3.6.4/lib/themes/default.date.css') }}" />
    <link rel="stylesheet" href="{{ secure_url('/libs/pickadate.js-3.6.4/lib/themes/default.time.css') }}" />
    <script src="{{ secure_url('/libs/pickadate.js-3.6.4/lib/picker.js') }}"></script>
    <script src="{{ secure_url('/libs/pickadate.js-3.6.4/lib/picker.date.js') }}"></script>
    <script src="{{ secure_url('/libs/pickadate.js-3.6.4/lib/picker.time.js') }}"></script>
    <script src="{{ secure_url('/libs/pickadate.js-3.6.4/lib/translations/es_ES.js') }}"></script>

    <!-- DataTable -->
    <link rel="stylesheet" type="text/css"
        href="https://cdn.datatables.net/1.10.13/css/dataTables.semanticui.min.css" />
    <script src="https://cdn.datatables.net/1.10.13/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.10.13/js/dataTables.semanticui.min.js "></script>
    <script src="https://cdn.datatables.net/fixedcolumns/3.3.0/js/dataTables.fixedColumns.min.js "></script>
    <link
        href="https://nightly.datatables.net/fixedheader/css/fixedHeader.dataTables.css?_=f0de745b101295e88f1504c17177ff49.css"
        rel="stylesheet" type="text/css" />
    <script
        src="https://nightly.datatables.net/fixedheader/js/dataTables.fixedHeader.js?_=f0de745b101295e88f1504c17177ff49">
    </script>

    <!-- Jquery Toast -->
    <link rel="stylesheet"
        href="https://cdnjs.cloudflare.com/ajax/libs/jquery-toast-plugin/1.3.1/jquery.toast.min.css" />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-toast-plugin/1.3.1/jquery.toast.min.js"></script>

    <!-- Toast -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
    <script>
        toastr.options.positionClass = 'toast-top-right';
        toastr.options.preventDuplicates = true;
    </script>

    <!-- Chart JS -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- Sweet Alert -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@10"></script>

    <!-- AWS SDK JS -->
    <script src="https://sdk.amazonaws.com/js/aws-sdk-2.7.20.min.js"></script>

    <!-- Moment JS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.30.1/moment.min.js"></script>

    <!-- Jquery Mask -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.inputmask/5.0.9/jquery.inputmask.min.js"></script>

    <!-- Cargar el archivo de idioma español -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/locale/es.min.js"></script>

    <!-- Scripts -->
    <script>
        window.Laravel = {!! json_encode([
            'csrfToken' => csrf_token(),
        ]) !!};

        String.prototype.explode = function(separator, limit) {
            var arr = this.split(separator);
            if (limit) arr.push(arr.splice(limit - 1).join(separator));
            return arr;
        };

        $.ajaxSetup({
            headers: {
                "X-CSRF-TOKEN": '{{ csrf_token() }}'
            }
        });

        // Permite solo letras y espacios
        function validateLettersOnly(input) {
            input.addEventListener('input', function() {
                this.value = this.value.replace(/[^a-zA-ZñÑ\s]/g, '')
            });
        }

        // Validación para solo números
        function validateNumbersOnly(input) {
            input.addEventListener('input', function() {
                this.value = this.value.replace(/[^0-9]/g, '');
            });
        }

        // Validación para solo números y letras
        function validateLettersAndNumbersOnly(input) {
            // var input = document.getElementById(fieldId);
            input.addEventListener('input', function() {
                this.value = this.value.replace(/[^a-zA-Z0-9\s]/g, '');
            });
        }

        // Validación para solo letras
        function capitalizeFirstLetter(string) {
            if (!string) return ''; // Retorna vacío si la cadena está vacía
            return string.charAt(0).toUpperCase() + string.slice(1).toLowerCase();
        }

        //Validacion para nombres letras
        function capitalizeFirstLetterName(name) {
            if (!name) return ''; // Retorna vacío si la cadena está vacía
            return name.toLowerCase().replace(/(^|\s)([a-záéíóúñü])/g, (match) => match.toUpperCase());
        }

        function validateRadicateBizagi(event) {
            var currentValue = event.target.value;
            var newValue = currentValue.replace(/[^0-9\_]/g, "").replace(/^([^_]*\_)|\_+/g, "$1");
            event.target.value = newValue;
        }

        function validateSubmitButton(idButton) {
            const button = $("#" + idButton);
            if (button.length === 0) return;
            const form = button.closest("form");
            if (form.length === 0) return;
            let charging = false;
            $(window).on('beforeunload', function() {
                charging = true;
            });
            form.submit(function() {
                button.prop("disabled", true);
                button.addClass("loading");
                setTimeout(function() {
                    if (!charging) {
                        button.prop("disabled", false);
                        button.removeClass("loading");
                    }
                }, 2000);
            });
        }

        /**
         * Función para agregar loading y inhabilitar un botón
         * @param {string} idButton - El ID del botón al que se aplicará la acción.
         * @param {boolean} loading - Indica si el botón está en estado de carga (true) o no (false).
         */
        function loadingButton(idButton, loading) {
            const button = $("#" + idButton);
            if (button.length === 0) return;
            if (loading) {
                button.prop("disabled", true);
                button.addClass("loading");
            } else {
                button.prop("disabled", false);
                button.removeClass("loading");
            }
        }

        /**
         * Función para agregar loading a toda la pantalla
         * @param {boolean} loading - Indica si el botón está en estado de carga (true) o no (false).
         */
        function loadingMain(loading) {
            const div = $("#mainLoading");
            if (div.length === 0) return;

            if (loading) {
                // Asegurar que el div cubra toda la ventana
                div.css({
                    width: '100%',
                    height: '100vh', // Ocupa toda la altura de la ventana
                    position: 'fixed', // Fijo para cubrir toda la pantalla
                    top: 0,
                    left: 0,
                }).addClass("ui active dimmer");

                // Añadir el loader si no existe
                if (div.find(".loader").length === 0) {
                    div.append('<div class="ui loader"></div>');
                }
            } else {
                // Remover el loader y limpiar estilos
                div.removeClass("active dimmer").css({
                    width: '',
                    height: '',
                    position: '',
                    top: '',
                    left: '',
                    'z-index': ''
                });
            }
        }

        function validateRadicateTutelage(event) {
            var currentValue = event.target.value;
            //TODO:: revisar para que permita solo 2 -
            var newValue = currentValue.replace(/[^0-9\-]/g, "").replace(/^([^-]*\_)|\_+/g, "$1");
            event.target.value = newValue;
        }

        /*
         * Función que permite realizar validación de email, se debe enviar el id del input del email y el del input que contiene el mensaje de error
         * */
        function validateEmailField(emailFieldId, errorFieldId) {
            var emailField = $('#' + emailFieldId);
            var errorField = $('#' + errorFieldId);

            emailField.on('input', function() {
                var email = $(this).val();
                var emailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

                if (!emailPattern.test(email)) {
                    errorField.show();
                } else {
                    errorField.hide();
                }
            });
        }

        /**
         * Obtener el valor de un input en tiempo real
         */
        function getNumericMask(value) {
            let real = value.substring(1, value.length);
            real = real.replaceAll(',', '#');
            real = real.replaceAll('.', '');
            real = real.replaceAll('#', '.');
            return real;
        }

        /**
         * Función para formatear la moneda de un input en tiempo real
         *
         * @param {string} input - Selector del input a formatear
         * @param {object} options - Opciones de configuración para la máscara
         */
        function configureInputMask(input, options) {
            let inputValue = $(input).val();

            // Verifica si el valor termina en .0
            if (/\.0$/.test(inputValue)) {
                // Elimina el .0 al final
                inputValue = parseFloat(inputValue).toString();
                $(input).val(inputValue);
            }

            // Ahora aplica la máscara
            Inputmask("currency", {
                prefix: options?.symbol ?? "",
                groupSeparator: ".",
                autoGroup: true,
                digits: 2,
                radixPoint: ",",
                allowMinus: false
            }).mask(input);
        }

        function configureInputMaskNegative(input, options) {
            let inputValue = $(input).val();

            // Verifica si el valor termina en .0
            if (/\.0$/.test(inputValue)) {
                // Elimina el .0 al final
                inputValue = parseFloat(inputValue).toString();
                $(input).val(inputValue);
            }

            // Ahora aplica la máscara
            Inputmask("currency", {
                prefix: options?.symbol ?? "",
                groupSeparator: ".",
                autoGroup: true,
                digits: 2,
                radixPoint: ",",
                allowMinus: true
            }).mask(input);
        }


        /**
         * Función para formatear dd/mm/yyyy a yyyy-mm-aa
         * @param dateStr
         * @returns {string}
         */
        function convertDateFormat(dateStr) {

            // Validar si la fecha no esta vacia
            if (!dateStr) return '';

            // Divide la fecha en partes
            const parts = dateStr.split('/');
            if (parts.length !== 3) return '';

            const day = parts[0];
            const month = parts[1];
            const year = parts[2];

            // Verifica que el formato sea correcto
            if (day.length !== 2 || month.length !== 2 || year.length !== 4) return '';

            // Devuelve la fecha en formato yyyy-mm-dd
            return `${year}-${month}-${day}`;
        }

        /**
         * Función que permite sacer el id de la url
         * Solo aplica en rutas donde el id esté al final, ejemplo /algo/algo/3
         */
        function getLastIdFromUrl() {
            const url = window.location.pathname;
            return url.substring(url.lastIndexOf('/') + 1);
        }

        $(document).ready(function() {

            $("input[type=text]").attr("maxlength", 191);
            $(".mask_prices").inputmask("decimal", {
                radixPoint: ",",
                groupSeparator: ".",
                digits: 2,
                autoGroup: true,
                rightAlign: false,
                removeMaskOnSubmit: true,
                prefix: '$ '
            });
        });

        //Darle formato al id de poliza
        function formatSortNumber(id) {
            // Si el número es menor o igual a 9999, agregar ceros a la izquierda
            if (id <= 9999) {
                return 'SORT-' + id.toString().padStart(4, '0');
            }
            // Si el número es mayor a 9999, no agregar ceros
            return 'SORT-' + id;
        }

        //Funcion para convertir valor compatible con la mask
        function convertToFormattedValue(value) {
            if (!value) return "";

            // Convertir a número flotante
            const floatValue = parseFloat(value);

            // Formatear con separadores
            return floatValue.toLocaleString('de-DE', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            });
        }

        const lowercaseWords = ["de", "y", "del", "la", "las", "los", "el"];

        function capitalizeItemName(itemName) {
            return (itemName || "")
                .toLocaleLowerCase("es") // Convertir todo a minúsculas con soporte para español
                .replace(/\b\p{L}/gu, char => char.toLocaleUpperCase("es")) // Capitalizar la primera letra de cada palabra
                .split(" ") // Dividir en palabras para formatear selectivamente
                .map((word, index) => {
                    // Si es una palabra común y no es la primera, mantenerla en minúscula
                    if (index > 0 && lowercaseWords.includes(word.toLowerCase("es"))) {
                        return word.toLowerCase("es");
                    }
                    return word; // De lo contrario, devolverla tal como está
                })
                .join(" ")
                .split(" ")
                .map(word => word.charAt(0) + word.slice(1).toLocaleLowerCase("es"))
                .join(" ");
        }


        async function cargarPaises(menu, paisesData) {
            // Limpiar el menú antes de agregar nuevos elementos
            menu.empty();

            $.each(paisesData, function(index, pais) {
                menu.append(`<div class="item" data-value="${pais.country_short_name}">
                                    <i class="${pais.country_short_name.toLowerCase()} flag"></i> ${pais.country_name}
                                </div>`);
            });
        }

        async function jsonDataPaises(selectedNationality) {
            try {
                //los datos de los países
                //const response = await fetch("/public/js/paises.json");
                const response = await fetch("/js/paises.json");

                if (!response.ok) throw new Error(`Error al cargar el archivo: ${response.statusText}`);

                const paisesData = await response.json();

                // Seleccionar el menú donde se cargarán los países
                const menu = $('.paises')

                // Cargar los países en el menú
                await cargarPaises(menu, paisesData);

                // Establecer el valor seleccionado basado en el dato inicial
                const nationality = selectedNationality;

                console.log('nationality::' + nationality);

                if (nationality) {
                    const selectedItem = menu.find(`.item[data-value="${nationality}"]`);

                    if (selectedItem.length > 0) {
                        selectedItem.addClass('active selected');
                        $('#nacionality').dropdown('set selected', nationality);
                    }
                }

                // Inicializar el dropdown después de cargar los datos
                $('#nacionality').dropdown();
            } catch (error) {
                console.error("Error al cargar los datos:", error);
            }
        }
    </script>
    <script type="text/javascript">
        // AWS INIT
        const bucketName = @json(env('AWS_BUCKET'));
        const bucketRegion = @json(env('AWS_REGION'));
        const IdentityPoolId = @json(env('AWS_IDENTITY_POOL'));
        const queueUrl = @json(env('AWS_SQS_REPORTS', 'undefined'));
        const queueUrlCertificateAffiliate = @json(env('AWS_SQS_CERTIFICATE_AFFILIATE', 'undefined'));



        AWS.config.update({
            region: bucketRegion,
            credentials: new AWS.CognitoIdentityCredentials({
                IdentityPoolId: IdentityPoolId
            })
        });

        const s3 = new AWS.S3({
            region: bucketRegion,
            apiVersion: '2006-03-01',
            httpOptions: {
                timeout: 0
            },
            params: {
                Bucket: bucketName
            }
        });
    </script>
    @stack('styles')
    <style type="text/css">
        body {
            overflow-x: initial;
        }

        .text.menu {
            margin-right: 0 !important;
            margin-left: 0 !important;
        }

        .ui.toggle.checkbox .box:before,
        .ui.toggle.checkbox label:before {
            background: rgba(140, 180, 210, 0.44) !important;
        }

        .picker__select--month,
        .picker__select--year {
            width: 30% !important;
            display: inline !important;
        }

        input[type='text']:not(.minus) {
            text-transform: uppercase;
        }

        .grid>.column {
            padding: 0.25rem !important;
        }

        .grid>.column:first-child {
            padding-left: 1.75rem !important;
        }

        .accordion .grid .row {
            padding: 0rem !important;
            padding-left: 1.5rem !important;
        }

        .accordion .grid .row .column {
            padding: 0.25rem !important;
        }

        .fields {
            margin-bottom: 0.25em !important;
        }

        .width-100percent {
            width: 100%;
        }

        .menu.transition.visible {
            width: max-content;
        }

        .ui.dimmer {
            background: rgba(0, 0, 0, 0.5);
            /* Fondo oscuro */
        }

        .ui.loader {
            color: #fff;
            /* Color del texto del loader */
        }

        /*.background-color-client {*/
        /*    background-color: #002a78 !important;*/
        /*}*/
        /**
         * Estilos para el botón de búsqueda de cie10 y actividades económicas
         */
        .ui.search>.results {
            width: 40rem;
        }

        .ui.search>.results .result .title {
            padding: 0 !important;
            border: none !important;
            text-transform: none;
        }

        .ui.search>.results .result .content {
            padding: 0 !important;
        }

        .floating-button {
            position: fixed;
            bottom: 20px;
            right: 70px;
            cursor: pointer;
            z-index: 1000;
        }

        .floating-button-question {
            position: fixed;
            bottom: 20px;
            right: 10px;
            cursor: pointer;
            z-index: 1000;
        }

        .floating-button-question a {
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: black;
            color: white;
            width: 51.41px;
            height: 51.41px;
            border-radius: 50%;
            font-size: 30px;
            font-weight: 900;
            text-decoration: none;
        }

        /* Personalización del estilo del SweetAlert */
        .swal2-icon.swal2-error {
            border-color: #91C845;
            /* Borde verde */
            color: #91C845;
            /* Color del ícono */
        }

        .swal2-icon.swal2-error [class^="swal2-x-mark-line"] {
            background-color: #91C845;
            /* Color verde para las líneas de la X */
        }

        .swal2-confirm:focus {
            box-shadow: none !important;
            /* Quita el borde azul */
            outline: none !important;
            /* Quita el contorno */
        }

        .swal2-warning {
            color: #91C845 !important;
            /* Cambia el color del icono (verde) */
        }

        .swal2-icon.swal2-warning {
            border-color: #91C845 !important;
            /* Cambia el borde del círculo a verde */
        }

        .swal2-cancel:focus {
            box-shadow: none !important;
            /* Quitar el borde azul (sombra) */
            outline: none !important;
            /* Quitar el contorno */
        }

        /* Cambiar el color del spinner de carga */
        .swal2-loader {
            border-color: #91C845 !important;
            /* Cambia el color del borde */
            border-top-color: transparent !important;
            /* Hace el borde superior transparente para el efecto de rotación */
        }

        /*Dropdown menu de gestion */
        .scrollable-dropdown-menu {
            max-height: 400px;
            overflow-y: auto;
        }

    </style>
</head>

<body>
    <div id="mainLoading"></div>
    @php
        $emailParts = explode('@', Auth::user() ? Auth::user()->email : '');
        $domain = isset($emailParts[1]) ? $emailParts[1] : '';
        $date = \Carbon\Carbon::parse(Auth::user() ? Auth::user()->last_login_date : '');
        $limitDate = \Carbon\Carbon::parse('2024-05-02 00:00:00');
    @endphp

    <div class="ui dimmer">
        <div class="ui loader main"></div>
    </div>

    @section('menu')
        @if (
            $domain != 'renconsultores.com.co' &&
                $date < $limitDate &&
                App\Client::current(Route::current())->id == \App\Client::COLPENSIONES)
            <div class="ui top inverted {{ App\Client::current(Route::current())->color }} fixed menu">
                <a href="/" class="header item">
                    ===RENAPP - {{ App\Client::current(Route::current())->name }}
                </a>
            </div>
        @else
            <div class="ui top inverted {{ App\Client::current(Route::current())->color }} fixed menu">
                @if (App\Client::current(Route::current())->id)
                    <a href="/" class="header item">
                        RENAPP - {{ App\Client::current(Route::current())->name }}
                    </a>
                @endif
                @if (Auth::user())
                    @if (!Auth::user()->view_case_search())
                        <a href="{{ secure_url('/casos') }}"
                            class="{{ preg_match('/nuevo\w*/', Request::path()) ? 'active' : '' }} item">
                            <i class="file icon"></i> Aseguramiento
                        </a>
                    @else
                        @if (Auth::user()->view_aseguramiento())
                            <a href="{{ secure_url('/nuevo') }}"
                                class="{{ preg_match('/nuevo\w*/', Request::path()) ? 'active' : '' }} item">
                                <i class="file icon"></i> Aseguramiento
                            </a>
                        @endif
                    @endif
                    @if (Auth::user()->view_bandeja())
                        <a href="{{ secure_url('/bandeja') }}"
                            class="{{ preg_match('/bandeja\w*/', Request::path()) ? 'active' : '' }} item">
                            <i class="inbox icon"></i> Bandeja de trabajo
                        </a>
                    @endif
                    @if (Auth::user()->view_busqueda())
                        <a href="{{ secure_url('/busqueda') }}"
                            class="{{ preg_match('/busqueda\w*/', Request::path()) ? 'active' : '' }} item">
                            <i class="search icon"></i> Búsqueda
                        </a>
                    @endif

                    @if (Auth::user()->view_asesor())
                        <a href="{{ secure_url('/servicio/gis_sort/table_call') }}"
                            class="{{ preg_match('/table_call\w*/', Request::path()) ? 'active' : '' }} item">
                            <i class="phone icon"></i> Reporte accidente
                        </a>
                    @endif
                @endif
                <div class="right menu">
                    @if (preg_match('/radicacion\w*/', Request::path()))
                    @elseif(Auth::guest())
                        <a href="{{ secure_url('/login') }}" class="item">Iniciar sesión</a>
                    @else
                        @if (Auth::user()->view_gestion())
                            <div class="ui dropdown item">
                                <i class="laptop icon"></i> Gestión
                                <div class="menu scrollable-dropdown-menu">
                                    @if (Auth::user()->isAdmin())
                                        <a href="{{ secure_url('/asignacionmasivacasos') }}" class="item">
                                            <i class="exchange icon"></i> Asignación masiva de casos
                                        </a>
                                    @endif
                                    @if (Auth::user()->isAdmin())
                                        <a href="{{ secure_url('/cambioestado') }}"
                                            class="{{ preg_match('/cambioestado\w*/', Request::path()) ? 'active' : '' }} item">
                                            <i class="exchange icon"></i> Cambio de estado
                                        </a>
                                    @endif
                                    @if (Auth::user()->view_variations())
                                        <a href="{{ secure_url('/servicio/VariationsSort/all') }}"
                                            class="{{ preg_match('/all\w*/', Request::path()) ? 'active' : '' }} item">
                                            <i class="exchange icon"></i> Tablero de variaciones
                                        </a>
                                    @endif
                                    @if (Auth::user()->view_cobros())
                                        <a href="{{ secure_url('/analista/cobros') }}"
                                            class="{{ preg_match('/all\w*/', Request::path()) ? 'active' : '' }} item">
                                            <i class="exchange icon"></i> Tablero de cobros
                                        </a>
                                    @endif
                                    @if (Auth::user()->view_provider())
                                        <a href="{{ secure_url('/proveedor/' . (Auth::user()->id ?? '1') . '/asignar_servicio') }}"
                                            class="{{ preg_match('/all\w*/', Request::path()) ? 'active' : '' }} item">
                                            <i class="exchange icon"></i> Tablero de proveedor
                                        </a>
                                    @endif
                                    @if (Auth::user()->view_auditoria())
                                        <a href="{{ secure_url('/tablero/auditoria_medica') }}"
                                            class="{{ preg_match('/all\w*/', Request::path()) ? 'active' : '' }} item">
                                            <i class="exchange icon"></i> Tablero de auditoría médica
                                        </a>
                                    @endif

                                    {{-- Solo accede el administrador o el proveedor --}}
                                    @if (Auth::user()->view_cuentas())
                                        <a href="{{ secure_url('/proveedor/tablero_cuentas_medicas') }}"
                                            class="{{ preg_match('/all\w*/', Request::path()) ? 'active' : '' }} item">
                                            <i class="exchange icon"></i> Tablero de cuentas médicas
                                        </a>
                                    @endif
                                    {{-- Solo accede el administrador o el area de auditoria --}}
                                    @if (Auth::user()->view_auditoria_cuentas())
                                        <a href="{{ secure_url('/tablero/auditoria_cuentas_medicas') }}"
                                            class="{{ preg_match('/all\w*/', Request::path()) ? 'active' : '' }} item">
                                            <i class="exchange icon"></i> Tablero de auditoría cuentas médicas
                                        </a>
                                    @endif
                                    @if (Auth::user()->view_compensation_board())
                                        <a href="{{ secure_url('/tablero/indemnizaciones') }}"
                                            class="{{ preg_match('/all\w*/', Request::path()) ? 'active' : '' }} item">
                                            <i class="exchange icon"></i> Tablero de indemnizaciones
                                        </a>
                                    @endif
                                    @if (Auth::user()->view_monitoreo())
                                        <a href="{{ secure_url('/tablero/monitereo_y_control') }}"
                                            class="{{ preg_match('/all\w*/', Request::path()) ? 'active' : '' }} item">
                                            <i class="exchange icon"></i> Tablero de monitoreo y control
                                        </a>
                                    @endif
                                    @if (Auth::user()->view_tablero_intermediario())
                                        <a href="{{ secure_url('/intermediario') }}"
                                            class="{{ preg_match('/all\w*/', Request::path()) ? 'active' : '' }} item">
                                            <i class="exchange icon"></i> Intermediario
                                        </a>
                                    @endif
                                    @if (Auth::user()->view_Tablero_ejecutivo_comercial())
                                        <a href="{{ secure_url('/intermediario') }}"
                                            class="{{ preg_match('/all\w*/', Request::path()) ? 'active' : '' }} item">
                                            <i class="exchange icon"></i> Tablero ejecutivo comercial
                                        </a>
                                    @endif
                                    @if (Auth::user()->view_Tablero_autorizaciones())
                                        <a href="{{ secure_url('/admin/usuarios') }}" class="item">
                                            <i class="users icon"></i> Usuarios
                                        </a>

                                        <a href="{{ secure_url('admin/usuarios_permisos') }}"
                                            class="{{ preg_match('/all\w*/', Request::path()) ? 'active' : '' }} item">
                                            <i class="exchange icon"></i> Autorizaciones
                                        </a>                                    
                                    @endif
                                    @if (Auth::user()->view_tomador())
                                        @if (Auth::user()->view_tomador_autorizado())
                                            @php
                                                $tomadorAutorizado = Auth::user()->find_tomador_autorizado() ?? '1';

                                            @endphp
                                            <a href="{{ secure_url('/tomador/poliza/' . $tomadorAutorizado . '/datos') }}"
                                                class="{{ preg_match('/all\w*/', Request::path()) ? 'active' : '' }} item">
                                                <i class="exchange icon"></i> Tomador
                                            </a>
                                        @else
                                            <a href="{{ secure_url('/tomador/poliza/' . (Auth::user()->affiliate_id ?? '1') . '/datos') }}"
                                                class="{{ preg_match('/all\w*/', Request::path()) ? 'active' : '' }} item">
                                                <i class="exchange icon"></i> Tomador
                                            </a>
                                        @endif

                                    @endif
                                    @if (Auth::user()->view_afiliado())
                                        <a href="{{ secure_url('/tablero/afiliado/1964') }}"
                                            class="{{ preg_match('/all\w*/', Request::path()) ? 'active' : '' }} item">
                                            <i class="exchange icon"></i> Asegurado
                                        </a>
                                    @endif
                                    @if (Auth::user()->view_call_center())
                                        <a href="{{ secure_url('/servicio/gis_sort/tomador') }}"
                                            class="{{ preg_match('/all\w*/', Request::path()) ? 'active' : '' }} item">
                                            <i class="exchange icon"></i> Call center
                                        </a>
                                    @endif
                                    @if (Auth::user()->isAdmin() || Auth::user()->view_administrator())
                                        <a href="{{ secure_url('/tablero/colectividad') }}"
                                            class="{{ preg_match('/all\w*/', Request::path()) ? 'active' : '' }} item">
                                            <i class="exchange icon"></i> Tablero de colectividad
                                        </a>
                                    @endif
                                    @if (Auth::user()->view_correos())
                                        <a href="{{ secure_url('/tablero/correos') }}"
                                            class="{{ preg_match('/all\w*/', Request::path()) ? 'active' : '' }} item">
                                            <i class="exchange icon"></i> Tablero de correos
                                        </a>
                                    @endif
                                    {{-- TODO: Poner permisos --}}
                                    @if (Auth::user()->isAdmin() || Auth::user()->isAdministrativoMNK())
                                    <a href="{{ secure_url('/tablero/actualizacion_datos') }}"
                                        class="{{ preg_match('/actualizacion_datos/', Request::path()) ? 'active' : '' }} item">
                                        <i class="exchange icon"></i> Actualización de datos
                                    </a>
                                    @endif
                                </div>
                            </div>
                        @endif
                        @if (Auth::user()->view_reports())
                            <div class="ui dropdown item">
                                <i class="laptop icon"></i> Reportes
                                <div class="menu">
                                    @if (Auth::user()->canViewReport('reportes_internos'))
                                        <a href="{{ url('/admin/reportes/descargas_admin') }}" class="item">
                                            <i class="arrow alternate circle down icon"></i> Reportes internos
                                        </a>
                                    @endif
                                    @if (Auth::user()->canViewReport('reporte_intermediario_tomador'))
                                        <a href="{{ secure_url('/admin/reportes/reportesIntermediarioYTomador') }}"
                                            class="item">
                                            <i class="briefcase icon"></i> Reporte intermediario y tomador
                                        </a>
                                    @endif
                                    @if (Auth::user()->canViewReport('reportes_empresariales'))
                                        <a href="{{ secure_url('/admin/reportes/reportesEmpresariales') }}"
                                            class="item">
                                            <i class="briefcase icon"></i> Reportes empresariales
                                        </a>
                                    @endif
                                    @if (Auth::user()->canViewReport('reportes_operativos_consolidados'))
                                        <a href="{{ secure_url('/admin/reportes/reportesOperativos') }}" class="item">
                                            <i class="briefcase icon"></i> Reportes operativos consolidados
                                        </a>
                                    @endif
                                    @if (Auth::user()->canViewReport('reportes_operativos'))
                                        <a href="{{ secure_url('/admin/reportes/reportesGeneralDos') }}" class="item">
                                            <i class="briefcase icon"></i> Reporte de red médica
                                        </a>
                                    @endif
                                    @if (Auth::user()->canViewReport('reportes_enfermedad'))
                                        <a href="{{ secure_url('/admin/reportes/reporteEnfermedad') }}" class="item">
                                            <i class="briefcase icon"></i> Reportes enfermedad
                                        </a>
                                    @endif
                                    @if (Auth::user()->canViewReport('reportes_fallecido'))
                                        <a href="{{ secure_url('/admin/reportes/reporteFallecido') }}" class="item">
                                            <i class="briefcase icon"></i> Reportes fallecido
                                        </a>
                                    @endif
                                    @if (Auth::user()->canViewReport('reportes_por_poliza'))
                                        <a href="{{ secure_url('/admin/reportes/reportePorPolizas') }}" class="item">
                                            <i class="briefcase icon"></i> Reporte por pólizas
                                        </a>
                                    @endif
                                    @if (Auth::user()->canViewReport('reportes_operativos_consolidados'))
                                        <a href="{{ secure_url('/admin/reportes/reportesEncuestas') }}" class="item">
                                            <i class="briefcase icon"></i> Reportes encuestas
                                        </a>
                                    @endif
                                </div>
                            </div>
                        @endif
                        {{--                @if (Auth::user()->isAdmin() || Auth::user()->isCorrespondence()) --}}
                        {{--                    <div class="ui dropdown item" --}}
                        {{--                         class="{{ preg_match("/correspondencia\w*/",  Request::path()) ? 'active' : '' }} item"> --}}
                        {{--                        <i class="mail icon"></i> Correspondencia --}}
                        {{--                        <div class="menu"> --}}
                        {{--                            <a href="{{secure_url('/correspondencia')}}" class="item"> --}}
                        {{--                                <i class="mail icon"></i> Bandeja --}}
                        {{--                            </a> --}}
                        {{--                            <a href="{{secure_url('/correspondencia/buscador')}}" class="item"> --}}
                        {{--                                <i class="search icon"></i> Buscador --}}
                        {{--                            </a> --}}
                        {{--                        </div> --}}
                        {{--                    </div> --}}
                        {{--                @endif --}}
                        {{--                    @if (Auth::user()->view_loads()) --}}
                        {{--                        <div class="ui dropdown item"> --}}
                        {{--                            <i class="mail icon"></i> Cargas --}}
                        {{--                            <div class="menu"> --}}
                        {{--                                @if (!Auth::user()->isCloseAdmon()) --}}
                        {{--                                    <a href="{{secure_url('/carguemasivo')}}" --}}
                        {{--                                       class="{{ preg_match("/carguemasivo\w*/",  Request::path()) ? 'active' : '' }} item"> --}}
                        {{--                                        <i class="arrow alternate circle up icon"></i> Carga masiva --}}
                        {{--                                    </a> --}}
                        {{--                                    <a href="{{secure_url('/carguemasivopcl')}}" --}}
                        {{--                                       class="{{ preg_match("/carguemasivopcl\w*/",  Request::path()) ? 'active' : '' }} item"> --}}
                        {{--                                        <i class="arrow alternate circle up icon"></i> Carga masiva PCL --}}
                        {{--                                    </a> --}}
                        {{--                                    <a href="{{secure_url('/carguemasivoit')}}" --}}
                        {{--                                       class="{{ preg_match("/carguemasivoit\w*/",  Request::path()) ? 'active' : '' }} item"> --}}
                        {{--                                        <i class="arrow alternate circle up icon"></i> Carga masiva IT --}}
                        {{--                                    </a> --}}
                        {{--                                @endif --}}
                        {{--                                @if (Auth::user()->isCloseAdmon() || Auth::user()->isAdmin()) --}}
                        {{--                                    <a href="{{secure_url('/carguemasivomid')}}" --}}
                        {{--                                       class="{{ preg_match("/carguemasivomid\w*/",  Request::path()) ? 'active' : '' }} item"> --}}
                        {{--                                        <i class="arrow alternate circle up icon"></i> Carga masiva MDI --}}
                        {{--                                    </a> --}}
                        {{--                                @endif --}}
                        {{--                            </div> --}}
                        {{--                        </div> --}}
                        {{--                    @endif --}}
                        @if (Auth::user()->isAdmin() || Auth::user()->view_administrator())
                            <div class="ui dropdown item">
                                <i class="mail icon"></i> Administrador
                                <div class="menu">
                                    <a href="{{ secure_url('/simulacion_servicio') }}" class="item">
                                        <i class="list alternate icon"></i> Acciones por servicio
                                    </a>
                                    {{--                                <a href="{{secure_url('/list-afps')}}" class="item"> --}}
                                    {{--                                    <i class="list ul icon"></i> AFPs --}}
                                    {{--                                </a> --}}
                                    {{--                                <a href="{{secure_url('/list-arls')}}" class="item"> --}}
                                    {{--                                    <i class="list ul icon"></i> ARLs --}}
                                    {{--                                </a> --}}
                                    {{--                                <a href="{{secure_url('/list-epss')}}" class="item"> --}}
                                    {{--                                    <i class="list ul icon"></i> EPSs --}}
                                    {{--                                </a> --}}
                                    <a href="{{ secure_url('/list-docs') }}" class="item">
                                        <i class="list alternate icon"></i> Documentos
                                    </a>
                                    <a href="{{ secure_url('/admin/usuarios') }}" class="item">
                                        <i class="users icon"></i> Usuarios
                                    </a>
                                    <a href="{{ secure_url('/admin/proveedores') }}" class="item">
                                        <i class="users icon"></i> Mantenimiento de proveedores
                                    </a>
                                </div>
                            </div>
                        @endif
                        @if (Auth::user()->isAdmin())
                            <div class="ui dropdown item">
                                <i class="mail icon"></i> Configuración
                                <div class="menu">
                                    <a href="{{ secure_url('/admin/estados') }}" class="item">
                                        <i class="tag icon"></i> Estados
                                    </a>
                                    <a href="{{ secure_url('/admin/acciones') }}" class="item">
                                        <i class="write icon"></i> Acciones
                                    </a>
                                    <a href="{{ secure_url('/admin/servicios') }}" class="item">
                                        <i class="cubes icon"></i> Servicios
                                    </a>
                                    <a href="{{ secure_url('/admin/roles') }}" class="item">
                                        <i class="lock icon"></i> Roles
                                    </a>
                                    {{--                                <a href="{{secure_url('/admin/empresas')}}" class="item"> --}}
                                    {{--                                    <i class="building icon"></i> Empresas --}}
                                    {{--                                </a> --}}
                                    <a href="{{ secure_url('/admin/envio-reportes') }}" class="item">
                                        <i class="chart line icon"></i> Envío de reportes
                                    </a>
                                </div>
                            </div>
                        @endif
                        <div class="ui dropdown item">
                            <i class="user icon"></i> {{ Auth::user()->full_name }}
                            <div class="menu">
                                <a href="{{ secure_url('/perfil') }}" class="item">
                                    <i class="user icon"></i> Mi perfil
                                </a>
                                <a href="{{ secure_url('/tutoriales') }}" class="item">
                                    <i class="video icon"></i> Tutoriales
                                </a>
                                @if (Auth::user()->isAdmin())
                                    <a href="https://mnk-seguros.atlassian.net/servicedesk/customer/portal/1"
                                        target="_blank" class="item">
                                        <i class="ticket icon"></i> Soporte
                                    </a>
                                @endif
                                {{--                            @if (Auth::user()->position_id == 5 || Auth::user()->email == '<EMAIL>' || Auth::user()->email == '<EMAIL>' || Auth::user()->email == '<EMAIL>' || Auth::user()->email == '<EMAIL>') --}}
                                {{--                                <a href="{{secure_url('/facturacion')}}" class="item"> --}}
                                {{--                                    <i class="tasks icon"></i> Facturación --}}
                                {{--                                </a> --}}
                                {{--                            @endif --}}
                                {{--                            @if (Auth::user()->ascribed == 1) --}}
                                {{--                                <a href="{{secure_url('/facturacion-profesionales')}}" class="item"> --}}
                                {{--                                    <i class="tasks icon"></i> Fact. Profesional --}}
                                {{--                                </a> --}}
                                {{--                            @endif --}}
                                <a href="{{ secure_url('/logout') }}" class="item"
                                    onclick="event.preventDefault(); document.getElementById('logout-form').submit();">
                                    <i class="sign out icon"></i> Cerrar sesión
                                </a>
                            </div>
                        </div>

                        <form id="logout-form" action="{{ secure_url('/logout') }}" method="POST"
                            style="display: none;">
                            {{ csrf_field() }}
                        </form>
                        <script type="text/javascript">
                            $(".ui.dropdown").dropdown();
                            $(".ui.dropdown2").dropdown();
                            $(".ui.dropdown3").dropdown();
                            $(".ui.dropdown4").dropdown();
                        </script>
                    @endif
                    @if (session('user_huella'))
                        <a href="{{ secure_url('/cerrar_sesion_huella') }}" class="item">
                            <i class="sign out icon"></i> Cerrar sesión
                        </a>
                    @endif

                </div>
            </div>
        @endif
        <div style="margin-top: 0;" class="ui clear text menu"></div>
    @show
    <div id="content">
        @if(Session::has('flash_message_ok_password'))
            <div class="ui info message bg-white" id="flash_message_ok_password">
                <i class="close icon" id="close_flash_message_ok_password"></i>
                <div class="header">
                    <em> {!! session('flash_message_ok_password') !!}</em>
                </div>
            </div>
        @endif
        @yield('content')
    </div>
    @if (App\Client::current(Route::current())->id)
        <div class="ui text mobile hidden menu"></div>
        <div class="ui bottom four item borderless small bottom fixed menu">
            <a class="button_from_main item">&copy; {{ date('Y') }}. Todos los derechos reservados</a>
            <a class="button_from_main item" style="padding: 0">
                <img style="height: 50px; width: auto;"
                    src="{{ secure_url('file/' . App\Client::current(Route::current())->logo) }}">
            </a>
            <a target="_blank" href="http://renconsultores.com.co/" class="button_from_main item">
                <img style="height: 25px; width: auto;" src="/ren.jpg">
            </a>
        </div>
    @endif

    @unless (App::environment('local'))
        <!-- Start of renapp Zendesk Widget script -->
        <script id="ze-snippet" src="https://static.zdassets.com/ekr/snippet.js?key=d7c6976f-1306-4baf-bb4c-4dd1a8c47f93">
        </script>
        <!-- End of renapp Zendesk Widget script -->
    @endunless

    @stack('scripts')
    {{-- <script data-jsd-embedded data-key="2fed41be-e025-4b62-af0f-fdfb8bac8f57" --}}
    {{--        data-base-url="https://jsd-widget.atlassian.com" --}}
    {{--        src="https://jsd-widget.atlassian.com/assets/embed.js"></script> --}}
    <div class="floating-button-question">
        <a href="https://docs.google.com/forms/d/e/1FAIpQLSeoDjnMjhC18W4C_wbGAbl1ppjC9weVA95TcfNTnQ37WhTHyg/viewform"
            target="_blank">?</a>
    </div>
    <div class="floating-button">
        <a href="https://meet.google.com/qki-fryt-dza" target="_blank" class="ui huge icon button primary circular">
            <i class="headphone icon"></i>
        </a>
    </div>
    <script>
        $(document).ready(function () {
            //cerrar mensajes de session
            $('#close_flash_message_ok_password').on('click', function() {
                $('#flash_message_ok_password').fadeOut();
            });
        });
    </script>
</body>

</html>
