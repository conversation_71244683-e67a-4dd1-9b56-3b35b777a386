<!DOCTYPE html>
<html lang="es">

<head>
    <meta content="text/html; charset=utf-8" http-equiv="Content-Type" />
    <style type="text/css">
    * {
        font-family: 'Arial', sans-serif;
        font-size: 10pt;
    }
    
    body {
        margin: 1rem 2rem;
    	padding-top: 130px;
        text-align: justify;
    }

    .watermark {
        position: fixed;
        top: 45%;
        width: 100%;
        text-align: center;
        opacity: .1;
        transform: rotate(-45deg);
        transform-origin: 50% 50%;
        z-index: 1000;
    }

    .footer { 
        font-size: 7pt !important;
        bottom: 0;
        left: 0px;
        right: 0px;
        height: 50px;
    }
    

    .header {
    	padding: 0 15px;
        text-align: center;
        position: fixed;
        top: 20px;
        left: 0;
        right: 0;
    }

    .header img {
    	height: 70px;
    	width: auto;
    }

    .page-break {
        page-break-after: always;
    }

    .numpage:after {
        content: counter(page);
    }

    table,
    th,
    td {
        border: 0.25px solid black;
    }
    
    th,
    td {
        padding: 2.5px;
        text-align: right;
    }
    
    table {
        border-collapse: collapse;
        width: 100%;
    }

    thead tr th, tfoot tr th {
        background: #7EBE2C;
        color: white;
    }
    
    th {
        text-align: center;
    }
    </style>
</head>

<body>
    <div class="header">
        <table>
            <tr>
                <th rowspan="5"><img src="{{storage_path('app/client_logo/orienta.jpg')}}"></th>
                <td style="text-align: left;"><b>Ciudad:</b></td>
                <td style="text-align: left;">Bogotá D.C, Colombia</td>
            </tr>
            <tr>
                <td style="text-align: left;"><b>Fecha de emisión:</b></td>
                <td style="text-align: left;">{{Carbon\Carbon::createFromFormat('Y-m-d', date('Y-m-d'))->formatLocalized('%B %d, %Y')}}</td>
            </tr>
            <tr>
                <td style="text-align: left;"><b>Fecha de vencimiento:</b></td>
                <td style="text-align: left;">{{Carbon\Carbon::createFromFormat('Y-m-d', date('Y-m-d', strtotime("+3 months")))->formatLocalized('%B %d, %Y')}}</td>
            </tr>
            <tr>
                <td style="text-align: left;"><b>Nro. factura:</b></td>
                <td style="text-align: left;">{!! $invoice_num !!}</td>
            </tr>
            <tr>
                <td style="text-align: left;"><b>Pagina:</b></td>
                <td></td>
            </tr>
        </table>   
        <script type="text/php">
            if (isset($pdf)) {
                $x = $pdf->get_width() - 155;
                $y = 117;
                $text = "{PAGE_NUM} de {PAGE_COUNT}";
                $font = 'Arial';
                $size = 10;
                $color = array(0.25,0.25,0.25);
                $word_space = 0.0;  //  default
                $char_space = 0.0;  //  default
                $angle = 0.0;   //  default
                $pdf->page_text($x, $y, $text, $font, $size, $color, $word_space, $char_space, $angle);
            }
        </script>     
    </div>
    <div style="text-align: center;">
        <p><b>REN CONSULTORES</b> <br /> NIT: 900.810.402-8</p>
        <p>DEBE A:</p>
        <p><b>ORIENTA TALENTS &amp; CAREERS</b> <br /> NIT: 900.874.911-1</p>
    </div>
    <div>
        <p>
        La suma de <b>$ {{number_format($total, 0, ',', '.')}}</b> ({{mb_strtoupper($total_str, 'utf-8')}} PESOS M/CTE) por concepto de alquiler de plataforma web RENAPP para el cliente <b>{{$client->name}}</b> desde {{Carbon\Carbon::parse($initial_date)->formatLocalized('%B %d, %Y')}} hasta {{Carbon\Carbon::parse($final_date)->formatLocalized('%B %d, %Y')}}, desglosados a continuación:
        </p>
        <table>
            <thead>
                <tr>
                    <th>DETALLE</th>
                    <th>CANTIDAD</th>
                    <th>V. UNITARIO ($)</th>
                    <th>V. TOTAL ($)</th>
                </tr>
            </thead>
            <tbody>
                @foreach($concepts as $c)
                <tr>
                    <td style="text-align: justify;">{{$c['name']}}</td>
                    <td>{{$c['units']}}</td>
                    <td>{{number_format($c['unitv'], 0, ',', '.')}}</td>
                    <td>{{number_format($c['totalv'], 0, ',', '.')}}</td>
                </tr>
                @endforeach     
            </tbody> 
            <tfoot>       
                <tr>
                    <td colspan="3"><b>TOTAL:</b></td>
                    <td>{{number_format($total, 0, ',', '.')}}</td>
                </tr>                
            </tfoot>          
        </table>
    </div>
    <div>
        <p>Se adjunta un documento de EXCEL con la relación de los conceptos facturados.</p>
    </div>
    <div>
        <p>Favor consignar a la <b>cuenta de ahorros 457370181580</b> de DAVIVIENDA.</p>
        <p>Autorización facturación por computador número de resolución 18762003921708 del 06/07/2017 prefijo REN desde el numero 1 al 1000.</p>
        <p>Esta factura de venta es un título valor conforme a la ley 1231 de 2008 es exigible a su vencimiento y causa interés de mora a la tasa máxima permitida por ley.</p>
    </div>
    <div style="text-align: center;">
        <br />
        <img style="height: 75px; width: auto;" src="{{storage_path('app/firma_hsohm.jpg')}}"> <br />
        <b>HEINZ SOHM LOPEZ</b> <br />
        ORIENTA TALENTS &amp; CAREERS
    </div>
</body>
</html>
