@extends('layouts.main')

@section('menu')
    @parent
@endsection

@section('content')
    <div class="ui basic segment">
        <div class="ui segment">
            <div class="ui dividing header">
                Reporte de acciones de {{ $rule->id }} - {{ $rule->service->name }}
            </div>
            <br>
            <div class="ui grid two column" style="width: 50%">
                <div class="column">Estado inicial:</div>
                <div class="column">{{ $rule->initialState->id }} - {{ $rule->initialState->name }}</div>
            </div>
            <div class="ui grid two column" style="width: 50%">
                <div class="column">Acciónes después de la fecha:</div>
                <div class="column">{{ \Carbon\Carbon::parse($rule->from_date)->format('d/m/Y') }}</div>
            </div>
            <div class="ui grid two column" style="width: 50%">
                <div class="column">Total de acciones ejecutadas:</div>
                <div class="column">{{ $count_actions_yes + $count_actions_no }}</div>
            </div>
            <div class="ui grid two column" style="width: 50%">
                <div class="column">Regla:</div>
                <div class="column">{{ $rule->percentage }} %</div>
            </div>
            <div class="ui grid two column" style="width: 50%">
                <div class="column">Última actualización de la regla:</div>
                <div class="column">{{ \Carbon\Carbon::parse($rule->updated_at)->format('d/m/Y H:i:s') }}</div>
            </div>
            <br><br>
            <div class="ui grid two column">
                @foreach( Array('SI', 'NO') as $k => $v)
                    @php( $action = $v == 'SI' ? $rule->yesAction : $rule->noAction )
                    @php( $actions = $v == 'SI' ? $actions_yes : $actions_no )
                    @php( $count_actions = $v == 'SI' ? $count_actions_yes : $count_actions_no )
                    @php( $role = $v == 'SI' ? $rule->yesArea : $rule->noArea )
                    <div class="column">
                        <div class="ui dividing header small" style="margin-bottom: 2rem;">
                            @if($v == 'SI')
                                SÍ
                            @else
                                NO
                            @endif cumple
                        </div>
                        <div class="ui grid two column">
                            <div class="column">Acción:</div>
                            <div class="column">{{ $action->id }} - {{ $action->name }}</div>
                        </div>
                        <div class="ui grid two column">
                            <div class="column">Acciones ejecutadas:</div>
                            <div class="column">{{ $count_actions }}
                                ({{ number_format($count_actions * 100 / ($count_actions_yes + $count_actions_no), 2) }}
                                %)
                            </div>
                        </div>
                        <div class="ui grid two column">
                            <div class="column">Asignadas al rol:</div>
                            <div class="column">{{ $role->id }} - {{ $role->name }}
                            </div>
                        </div>
                        <div class="ui dividing header small" style="margin-bottom: 2rem;">
                            Últimas 100 acciones
                        </div>
                        <div class="ui grid six column">
                            <div class="column">Servicio</div>
                            <div class="column">Acción</div>
                            <div class="column">Fecha de la acción</div>
                            <div class="column">Usuario asignado</div>
                            <div class="column">Estado anterior</div>
                            <div class="column">Estado actual</div>
                        </div>
                        <br>
                        <div style="max-height: 30vh; overflow-y: scroll; overflow-x: hidden">
                            <br><br>
                            @foreach($actions as $a)
                                <div class="ui grid six column">
                                    <div class="column">{{ $a->activity_id }}</div>
                                    <div class="column">{{ $a->action->id }} - {{ $a->action->name }}</div>
                                    <div class="column">{{ \Carbon\Carbon::parse($a->created_at)->format('d/m/Y H:i:s') }}</div>
                                    <div class="column">{{ $a->new_user->name }}</div>
                                    <div class="column">{{ $a->old_state->id }} - {{ $a->old_state->name }}</div>
                                    <div class="column">{{ $a->new_state->id }} - {{ $a->new_state->name }}</div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                @endforeach
            </div>
            <br><br>
        </div>
    </div>
    <div class="ui basic segment container">
        <div class="ui segment">
            <div class="ui dividing header">
                Historial de cambios
            </div>
            <div class="ui styled fluid accordion">
                @if(count($history) == 0)
                    <div class="title">No hay registros de cambios</div>
                @endif
                @foreach($history as $h)
                    <div class="title">
                        <i class="dropdown icon"></i>
                        <div class="ui grid two column"
                             style="margin-top: -1.6rem;margin-left: 0;margin-bottom: -0.2rem;">
                            <div class="column">
                                Fecha de la
                                actualización: {{ \Carbon\Carbon::parse($h->created_at)->format('d/m/Y H:i:s') }}
                            </div>
                            <div class="column">
                                Usuario: {{ $h->author->name }}
                            </div>
                        </div>
                    </div>
                    <div class="content">
                        <div class="ui grid">
                            <div class="four wide column">Estado inicial</div>
                            <div class="twelve wide column">{{ $h->initialState->id }} - {{ $h->initialState->name }}</div>
                        </div>
                        <div class="ui grid">
                            <div class="four wide column">Acciónes después de la fecha</div>
                            <div class="twelve wide column">{{ \Carbon\Carbon::parse($h->from_date)->format('d/m/Y') }}</div>
                        </div>
                        <div class="ui grid">
                            <div class="four wide column">Regla</div>
                            <div class="twelve wide column">{{ $h->percentage }} %</div>
                        </div>
                        <div class="ui grid">
                            <div class="four wide column">Acción si cumple</div>
                            <div class="twelve wide column">{{ $h->yesAction->id }} - {{ $h->yesAction->name }}</div>
                        </div>
                        <div class="ui grid">
                            <div class="four wide column">Acción no cumple</div>
                            <div class="twelve wide column">{{ $h->noAction->id }} - {{ $h->noAction->name }}</div>
                        </div>
                        <div class="ui grid">
                            <div class="four wide column">Asignadas al rol si cumple</div>
                            <div class="twelve wide column">{{ $h->yesArea->id }} - {{ $h->yesArea->name }}</div>
                        </div>
                        <div class="ui grid">
                            <div class="four wide column">Asignadas al rol no cumple</div>
                            <div class="twelve wide column">{{ $h->noArea->id }} - {{ $h->noArea->name }}</div>
                        </div>
                        <div class="ui grid">
                            <div class="four wide column">Descripción si cumple</div>
                            <div class="twelve wide column">{{ $h->yes_description }}</div>
                        </div>
                        <div class="ui grid">
                            <div class="four wide column">Descripción no cumple</div>
                            <div class="twelve wide column">{{ $h->no_description }}</div>
                        </div>
                        <div class="ui grid">
                            <div class="four wide column">Activa</div>
                            <div class="twelve wide column">{{ $h->enable == 1 ? 'Si' : 'No' }}</div>
                        </div>
                        <div class="ui grid">
                            <div class="four wide column">Frecuencia</div>
                            <div class="twelve wide column">{{ $h->frequency_in_minutes }} minutos</div>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </div>
    <script type="text/javascript">
      $(".datepicker").pickadate({
        selectYears: true,
        selectMonths: true,
        formatSubmit: "yyyy-mm-dd",
        format: "yyyy-mm-dd"
      });
      $(".ui.accordion").accordion({
        exclusive: false
      });
      $(".ui.checkbox").checkbox();
      $(".ui.dropdown").dropdown();
    </script>
@endsection
