@extends('layouts.main')

@section('title', 'Vista del tramitador - Estadísticas y reportes')

@section('menu')
    @parent
@endsection

@section('content')
    <div class="ui basic segment">
        @include('agent.menu', ['active' => 'estadisticas_reportes_cotizaciones'])
        <div class="ui message">
            <div class="header">Estadísticas y reportes de cotizaciones</div>
            <p>En este apartado se encuentran las estadísticas y reportes del intermediario.</p>
        </div>

        <div class="ui secondary segment">
            <form class="ui form">

                <div class="two fields">
                    <div class="field">
                        <canvas id="chartBar"></canvas>
                    </div>
                    <div class="field">
                        <canvas id="chartPie"></canvas>
                    </div>
                </div>
                <br>

                <div class="three fields">
                    <div class="field">
                        <table class="ui celled sortable striped compact very  table selectable"
                               style="font-size: 0.8em; line-height: 1em;">
                            {{-- Encabezado principal --}}
                            <thead>
                            <tr>
                                <th colspan="2" style="text-align: center;">
                                    Cotizaciones (por tipo de moneda)
                                </th>
                            </tr>
                            {{-- Sub-encabezados --}}
                            <tr>
                                <th>
                                    Tipo de moneda
                                </th>
                                <th>
                                    Total prima anual
                                </th>
                            </tr>
                            </thead>

                            {{-- Cuerpo de la tabla --}}
                            <tbody>
                                @foreach($quotationsTypeCurrency as $data)
                                    <tr>
                                        <td>
                                            {{-- Moneda --}}
                                            {{ $data['type_currency'] ?? '' }}
                                        </td>
                                        <td>
                                            {{-- Fecha de cotización --}}
                                            {{ number_format($data['amount_annual_premium'] ?? 0, 2, ',', '.') }}
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                    <div class="field">
                        <table class="ui celled sortable striped compact very  table selectable"
                               style="font-size: 0.8em; line-height: 1em;">
                            {{-- Encabezado principal --}}
                            <thead>
                            <tr>
                                <th colspan="2" style="text-align: center;">
                                    Cotizaciones en colones
                                </th>
                            </tr>
                            </thead>
                            {{-- Cuerpo de la tabla --}}
                            <tbody>
                            <tr>
                                <td>Total prima anual</td>
                                <td>
                                    {{'₡'}}
                                    {{number_format($summationsQuotation->only_crc ?? 0, 2, ',', '.')}}
                                </td>
                            </tr>
                            <tr>
                                <td>Conversión a dólares</td>
                                <td>
                                    {{'$'}}
                                   @if($summationsQuotation->only_crc > 0)
                                        {{number_format(($summationsQuotation->only_crc / 510.66), 2, ',', '.')}}
                                    @else
                                        {{number_format(0, 2, ',', '.')}}
                                   @endif
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="field">
                        <table class="ui celled sortable striped compact very table selectable"
                               style="font-size: 0.8em; line-height: 1em;">
                            {{-- Encabezado principal --}}
                            <thead>
                            <tr>
                                <th colspan="2" style="text-align: center;">
                                    Cotizaciones en dolares
                                </th>
                            </tr>
                            </thead>

                            {{-- Cuerpo de la tabla --}}
                            <tbody>
                            <tr>
                                <td>Total prima anual</td>
                                <td>
                                    {{'$'}}
                                    {{number_format($summationsQuotation->only_usd ?? 0, 2, ',', '.')}}
                                </td>
                            </tr>
                            <tr>
                                <td>Conversión a colones</td>
                                <td>
                                    {{'₡'}}
                                    @if($summationsQuotation->only_usd > 0)
                                        {{number_format(($summationsQuotation->only_usd * 510.66), 2, ',', '.')}}
                                    @else
                                        {{number_format(0, 2, ',', '.')}}
                                    @endif
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <br>
                <div class="two fields">
                    <div class="field">
                        <table class="ui celled sortable striped compact very  table selectable"
                               style="font-size: 0.8em; line-height: 1em;">
                            {{-- Encabezado principal --}}
                            <thead>
                            <tr>
                                <th style="text-align: center;">
                                    Suma total prima anual en colones
                                </th>
                            </tr>
                            </thead>
                            {{-- Cuerpo de la tabla --}}
                            <tbody>
                            <tr>
                                <td style="text-align: center;">
                                    {{'₡'}}
                                    {{number_format($summationsQuotation->total_amount_annual_premium_crc ?? 0, 2, ',', '.')}}
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="field">
                        <table class="ui celled sortable striped compact very  table selectable"
                               style="font-size: 0.8em; line-height: 1em;">
                            {{-- Encabezado principal --}}
                            <thead>
                            <tr>
                                <th style="text-align: center;">
                                    Suma total prima anual en dolares
                                </th>
                            </tr>
                            </thead>
                            {{-- Cuerpo de la tabla --}}
                            <tbody>
                            <tr>
                                <td style="text-align: center;">
                                    {{'$'}}
                                    {{number_format($summationsQuotation->total_amount_annual_premium_usd ?? 0, 2, ',', '.')}}
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <br>

                <!--Tabla principal-->
                <div class="field">
                    <button class="ui button primary expotar_excel">
                        <i class="ui file excel outline icon"></i> Exportar datos a excel
                    </button>
                    <table class="ui celled sortable striped compact very  table selectable"
                           style="font-size: 0.8em; line-height: 1em;">
                        <thead>
                        <tr>
                            <th title="Fecha de cotización">Fecha de cotización</th>
                            <th title="Correduría (intermediario)">Correduría (intermediario)</th>
                            <th title="Asesor">Asesor</th>
                            <th title="Tomador">Tomador</th>
                            <th title="Moneda">Moneda</th>
                            <th title="Estimado de prima anual">Estimado de prima anual</th>
                            <th title="Modalidad de aseguramiento">Modalidad de aseguramiento</th>
                            <th title="Actividad económica">Actividad económica</th>
                            <th title="Tarifa de actividad económica">Tarifa de actividad económica</th>
                        </tr>
                        </thead>
                        @foreach($activities as $activity)
                            <tr>
                                <td>
                                    {{--Fecha de cotización--}}
                                    {{ $activity && !empty($activity->created_date)
                                               ? ucfirst(strftime('%A %e de %B del %Y', strtotime($activity->created_date)))
                                               : 'Fecha no disponible'
                                        }}
                                </td>
                                <td>
                                    {{--Correduría--}}
                                    {{ ucwords(strtolower($activity->brokerage_name)) ?? '' }}
                                </td>
                                <td>
                                    {{--Asesor--}}
                                    {{ ucwords(strtolower($activity->advisor_name)) ?? '' }}
                                </td>
                                <td>
                                    {{--Tomador--}}
                                    {{ ucwords(strtolower($activity->affiliate_name)) ?? '' }}
                                </td>
                                <td>
                                    {{--Moneda--}}
                                    {{ $activity->type_currency ?? '' }}
                                </td>
                                <td>
                                    {{--Prima anual--}}
                                    {{number_format($activity->amount_annual_premium ?? 0, 2, ',', '.')}}
                                </td>
                                <td>
                                    {{--Modalidad de aseguramiento--}}
                                    {{ $WORK_MODALITY[$activity->work_modality_id] ?? 'Modalidad desconocida' }}
                                </td>
                                <td>
                                    {{--Actividad económica--}}
                                    {{ $activity->economic_activity_name ?? '' }}
                                </td>
                                <td>
                                    {{--Tarifa económica--}}
                                    {{ isset($activity->economic_activity_percentage_number) ? number_format($activity->economic_activity_percentage_number, 2, '.', ',') . '%' : '' }}
                                </td>
                            </tr>
                        @endforeach
                    </table>

                    <br>
                    <!-- Paginación -->
                    @if ($activities->hasPages())
                        <div class="ui grid">
                            <div class="eight wide column left aligned"></div>
                            <div class="eight wide column right aligned" style="right: 10px;">
                                <div class="ui pagination menu">
                                    {{ $activities->links() }}
                                </div>
                                <p>Total de registros: {{ $activities->total() }}</p>
                            </div>
                        </div>
                    @endif
                </div>
            </form>
        </div>
    </div>
    <!-- Scripts para gráficos -->
    <script>
        {{-- Esperamos a que todo el contenido de la página se haya cargado antes de ejecutar el código --}}
        $(document).ready(function() {

            @if(!empty($chartDataPie))
                {{-- Iteramos por cada gráfico de torta en la lista de datos $chartDataPie --}}
                @foreach ($chartDataPie as $index => $chart)

                {{-- Guardamos los datos del gráfico en una variable única --}}
                let chartDataPie_{{ $index }} = @json($chart['data']);

                {{-- Seleccionamos el canvas (lienzo) donde se dibujará el gráfico --}}
                let ctxPie_{{ $index }} = $('#{{ $chart['canvasId'] }}')[0].getContext('2d');

                {{-- Creamos la configuración para el gráfico (tipo, datos y opciones) --}}
                let configPie_{{ $index }} = {
                    type: '{{ $chart['type'] }}',  {{-- Tipo de gráfico (ej. 'pie', 'bar') --}}
                    data: chartDataPie_{{ $index }},  {{-- Datos del gráfico --}}
                    options: @json($chart['options'])  {{-- Opciones del gráfico (como leyenda, ejes, etc.) --}}
                };

                {{-- Creamos e inicializamos el gráfico usando la configuración --}}
                new Chart(ctxPie_{{ $index }}, configPie_{{ $index }});

                @endforeach
            @endif

            @if(!empty($chartDataBar))
                {{-- Iteramos por cada gráfico de barras en la lista de datos $chartDataBar --}}
                @foreach ($chartDataBar as $index => $chart)

                {{-- Guardamos los datos del gráfico en una variable única --}}
                let chartDataBar_{{ $index }} = @json($chart['data']);

                {{-- Seleccionamos el canvas (lienzo) donde se dibujará el gráfico --}}
                let ctxBar_{{ $index }} = $('#{{ $chart['canvasId'] }}')[0].getContext('2d');

                {{-- Creamos la configuración para el gráfico (tipo, datos y opciones) --}}
                let configBar_{{ $index }} = {
                    type: '{{ $chart['type'] }}',  {{-- Tipo de gráfico (ej. 'pie', 'bar') --}}
                    data: chartDataBar_{{ $index }},  {{-- Datos del gráfico --}}
                    options: @json($chart['options'])  {{-- Opciones del gráfico (como leyenda, ejes, etc.) --}}
                };

                {{-- Creamos e inicializamos el gráfico usando la configuración --}}
                new Chart(ctxBar_{{ $index }}, configBar_{{ $index }});

                @endforeach
            @endif
        });
    </script>
    <script>
        $('.expotar_excel').click(function (e) {
            e.preventDefault(); // Prevenir el comportamiento por defecto del enlace
            loadingMain(true); // Mostrar el indicador de carga

            $.ajax({
                url: `{{ secure_url('/intermediario/exportar_excel_cotizaciones') }}`, // Asegúrate de que el endpoint es correcto
                type: 'GET',
                headers: {
                    'X-CSRF-TOKEN': '{{ csrf_token() }}' // Token CSRF para seguridad
                },
                xhrFields: {
                    responseType: 'blob' // Indica que la respuesta será un archivo binario
                },
                success: function (response) {
                    loadingMain(false); // Ocultar el indicador de carga

                    // Crear un Blob y gestionar la descarga
                    const blob = new Blob([response], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'}); // Cambiar el tipo si es Excel
                    const link = document.createElement('a');
                    link.href = window.URL.createObjectURL(blob);
                    link.download = 'Reporte_Cotizaciones.xlsx'; // Cambia el nombre si es necesario
                    document.body.appendChild(link); // Añadir el enlace al DOM
                    link.click(); // Simular el clic para iniciar la descarga
                    document.body.removeChild(link); // Eliminar el enlace

                    Swal.fire({
                        icon: 'success',
                        title: '¡Descarga exitosa!',
                        text: 'El documento se ha descargado correctamente.',
                        confirmButtonText: 'OK',
                        confirmButtonColor: '#91c845'
                    });
                },
                error: function () {
                    loadingMain(false); // Ocultar el indicador de carga
                    Swal.fire({
                        icon: 'error',
                        title: 'Error en la solicitud',
                        text: 'Ocurrió un problema al procesar la solicitud. Inténtalo nuevamente.',
                        confirmButtonText: 'Cerrar',
                        confirmButtonColor: '#91c845'
                    });
                }
            });
        });
    </script>

@endsection
