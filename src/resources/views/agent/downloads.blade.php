@extends('layouts.main')

@section('title', 'Vista del tramitador - Cotizaciones')

@section('menu')
    @parent
@endsection

@section('content')
    <div class="ui basic segment">
        @include('agent.menu', ['active' => 'descargas'])

        <div class="content-filter">

            <form class="ui form small clearing" method="GET" id="filter-form">

                <div class="four fields filters">

                    <div class="field">
                        <label>Nro. Póliza</label>
                        <input type="text" name="policy_consecutive" class="input_data"
                            value="{{ request()->input('policy_consecutive') ?? '' }}">
                    </div>

                    <div class="field">
                        <label>Nombre tomador</label>
                        <input type="text" name="tomador_name" value="{{ request()->input('tomador_name') ?? '' }}"
                            class="input_data">
                    </div>

                    <div class="field">
                        <label>Estado de la póliza</label>
                        <select class="ui fluid search dropdown" name="estado[]" multiple>
                            <option value="">Seleccione</option>
                            @foreach ($state_police as $state)
                                <option value="{{ $state->state_id }}" {{ in_array($state->state_id, request()->input('estado', [])) ? 'selected' : '' }}>
                                    {{ ucfirst(mb_strtolower($state->state->name)) }}
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <div class="field">
                        <button class="ui primary button">
                            <i class="search icon"></i>
                            Aplicar filtros
                        </button>
                    </div>

                    <div class="field">
                        <button class="ui secondary button" id="reset" type="reset">
                            <i class="undo icon"></i>
                            Limpiar filtros
                        </button>
                    </div>
                </div>
            </form>

        </div>

        <!-- Tabla de pólizas del intermediario -->
        <table class="ui celled sortable striped very compact very small table">
            <thead>
                <tr>
                    <th># de emisión</th>
                    <th>Póliza SORT</th>
                    <th>Fecha de emisión de la póliza</th>
                    <th>Nombre del tomador</th>
                    <th>Actividad económica</th>
                    <th>Estado de la póliza</th>
                    <th>Acciones</th>
                </tr>
            </thead>
            <tbody>
                @foreach ($policies as $policy)
                            <tr>
                                <td>{{ $policy->id }}</td>
                                <td>{{ $policy->formatNumberConsecutive() ?? '' }}</td>
                                <td>
                                    @if (
                                        $policy->activity &&
                                        $policy->activity->activity_actions->where('action_id', App\Actions\ActionPolizaSort::EMITIR_POLIZA)->isNotEmpty()
                                    )
                                                        <span class="fecha-emitir"
                                                            data-fecha="{{ $policy->activity->activity_actions->where('action_id', App\Actions\ActionPolizaSort::EMITIR_POLIZA)->first()->created_at }}"></span>
                                    @endif
                                </td>
                                <td>
                                    {{ $policy->activity && $policy->activity->affiliate
                        ? mb_convert_case($policy->activity->affiliate->full_name, MB_CASE_TITLE, 'UTF-8')
                        : 'Sin tomador' }}
                                </td>
                                <td>{{ $policy->activity_name }}</td>

                                <td>
                                    {{ $policy->activity->state ? ucfirst(mb_strtolower($policy->activity->state->name, 'UTF-8')) : 'Estado no disponible' }}
                                </td>

                                <td>
                                    <a href="/servicio/{{ $policy->activity->id }}/policy_sort/view"><i class="unhide icon"></i></a>
                                </td>
                            </tr>
                @endforeach
            </tbody>
        </table>
        <!-- Paginación -->
        <br>
        @if ($policies->hasPages())
            <div class="ui pagination menu pull-right" style="float:right">
                {{ $policies->links() }}
            </div>
        @endif

        <p style="margin-top:50px; text-align:right">Total de registros: {{ $policies->total() }}</p>

    </div>


    <style>
        .content-filter {
            width: 100%;
            display: flex;
            justify-content: flex-end;
            margin-top: 1rem;
            margin-bottom: 1rem;
        }

        .filters {
            align-items: flex-end;
        }
    </style>


    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/locale/es.min.js"></script>

    <script>
        function downloadDocument(e, id) {
            $.ajax({
                type: 'GET',
                url: '/intermediario/download/documents/' + id,
                dataType: 'json',
                contentType: 'application/json',
                success: function (response) {
                    // Iterar sobre las URLs de los documentos y forzar la descarga
                    if (response) {
                        response.documentPaths.forEach(function (path) {
                            downloadDocumentUrl(path);
                        });
                    }
                },
                error: function (e) {
                    console.error(e);
                }
            });
        }

        // Función para descargar un documento
        function downloadDocumentUrl(url) {
            var a = document.createElement('a');
            a.href = url;
            a.download = url.split('/').pop(); // Nombre del archivo
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
        }

        $('.policy-edit').popup({
            boundary: 'body',
            content: 'Ir a póliza',
            position: 'top center'
        });
        $('.policy-collections').popup({
            boundary: 'body',
            content: 'Ir a pago',
            position: 'top center'
        });
        $('.policy-resumen').popup({
            boundary: 'body',
            content: 'Descargar documentos',
            position: 'top center'
        });
        $('.policy-constancia').popup({
            boundary: 'body',
            content: 'Generar constancia',
            position: 'top center'
        });
    </script>


    <script>
        document.addEventListener("DOMContentLoaded", function () {
            // Establecer el idioma a español
            moment.locale('es');

            // Seleccionar todos los elementos con la clase 'fecha-emitir'
            const fechasEmitir = document.querySelectorAll('.fecha-emitir');

            fechasEmitir.forEach(function (fechaElem) {
                // Obtener la fecha desde el atributo data-fecha
                var fecha = fechaElem.getAttribute('data-fecha');

                // Formatear la fecha con Moment.js
                var fechaFormateada = moment(fecha).format('dddd D [de] MMMM [de] YYYY');

                // Capitalizar solo la primera letra de la cadena
                fechaFormateada = fechaFormateada.charAt(0).toUpperCase() + fechaFormateada.slice(1);

                // Asignar el valor formateado al elemento
                fechaElem.innerText = fechaFormateada;
            });
        });
    </script>

    <script>
        $(document).ready(function () {
            $('#reset').click(function () {
                // Limpiar todos los campos del formulario
                $('form .input_data').val('');
                $('.dropdown').dropdown('clear');

                // Actualizar la URL para eliminar los parámetros de búsqueda
                const url = new URL(window.location.href);
                url.search = ""; // Limpiar los parámetros GET
                history.pushState({}, '', url);

                // Enviar el formulario para aplicar los cambios (limpieza)
                $('#filter-form').submit();
            });



        });

        function openEmailModal(element) {
            // Recuperar los atributos de datos del elemento que fue clickeado
            const policieId = $(element).data('id');

            let policyData = $(element).data('policy');

            if (typeof policyData === "string") {
                policyData = JSON.parse(policyData); // Convertir JSON string a objeto
            }

            if (!policieId && !policyData) {
                console.error("Error: policieId es undefined.");
                return;
            }

            // Actualizar el valor del input oculto
            $('#policy_id').val(policieId);
            $('#constanciaModal').data('policy', policyData);


            $('#constanciaModal').modal('refresh').modal({
                inverted: false,
                autofocus: false,
                closable: false
            }).modal('show');
        }
    </script>
@endsection