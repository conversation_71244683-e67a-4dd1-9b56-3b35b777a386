<div class="ui mini modal" id="AutorizacionModal">
    <input type="hidden" id="policy_id">
    <div class="logo_mnk_modal">
        <img src="/images/mnk.png" alt="Logo MNK" class="logo">
    </div>

    <div class="ui basic segment fields_constancia">
        <form id="form_modal_autorizacion">
            <div class="ui form small clearing">
                <div class="field required" id="docNumberField">
                    <label>Número de identificación</label>
                    <input type="text" name="doc_number" id="doc_number"
                        placeholder="Ingrese el número de identificación">
                </div>

                <div class="field button_generar">
                    <div class="ui primary button" id="sendButton">Autorizar</div>
                </div>

            </div>
        </form>

    </div>

    <div class="actions">
        <div class="ui button secondary" id="cancelButton">Cancelar</div>

    </div>

</div>

<style>
    .ui.mini.modal {
        max-width: 350px !important;
        /* Ajuste del tamaño */
        transform: translate(80%, 0%) !important;
        /* Permitir ajuste automático */
    }

    /* Ajustar contenido dentro del modal */
    .fields_constancia {
        position: relative;
        top: -67px;
    }

    /* Centrar la imagen */
    .logo_mnk_modal {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 10px 0;
    }

    .logo_mnk_modal img {
        width: 150px;
        /* Ajustar tamaño */
    }

    .button_generar {
        position: relative;
        transform: translate(25%, 100%) !important;

        .ui.primary.button {
            width: 50%;
        }
    }

    .constanciaModal actions {
        text-align: left
    }
</style>



<script>
    $(document).ready(function () {
        // Inicializar el dropdown
        $('.ui.dropdown').dropdown();

        // Cerrar el modal al hacer clic en el botón de cancelar
        $('#cancelButton').on('click', function () {
            $('#AutorizacionModal').modal('hide');
        });


        $('#sendButton').on('click', function () {
            formInicial();
        });

    });
</script>
<script>
    let validations = {
        doc_number: {
            identifier: 'doc_number',
            rules: [{
                type: 'empty',
                prompt: 'Debe ingresar el número de identificación.'
            }]
        }
    }

    function handleSubmit(endpoint) {
        // 1) Recolectar valores del formulario
        
        const docNumber = $('#doc_number').val();

        $('#AutorizacionModal').modal('hide');


        // 2) Primer AJAX: comprobar existencia por documento o email
        $.ajax({
            url: '/admin/autorizados/valid',      // ruta que devuelve { exists: true/false, user: {...} }
            method: 'GET',
            data: {  doc_number: docNumber },
            dataType: 'json'
        })
            .done(function (res) {
                if (!res.exists) {
                    // 1) Usuario NO existe → preguntamos si desea crearlo
                    Swal.fire({
                        icon: 'question',
                        title: 'Usuario no encontrado',
                        text: '¿Deseas crear un nuevo usuario con estos datos?',
                        showCancelButton: true,
                        confirmButtonText: 'Crear usuario',
                        cancelButtonText: 'Cancelar'
                    }).then((result) => {
                        if (result.isConfirmed) {
                            // 2) Redirige a la ruta de creación pasando los valores en query params
                            const params = new URLSearchParams({
                                doc_number: docNumber
                            });
                            window.location.href = `/admin/usuarios?${params.toString()}`;
                        } else {
                            loadingMain(false);
                        }
                    });


                }
                else {
                    // 3b) Usuario **sí existe** → comprobamos su área
                    const user = res.user;
                    if (user.area_id !== {{ \App\Area::TOMADOR_AUTORIZADO }}) {
                        Swal.fire('Atención', 'El usuario existe pero no es un Tomador autorizado.', 'warning');
                        loadingMain(false);
                    } else {
                        // 3c) Ya es tomador autorizado → sólo autorizamos la relación
                        autorizarUsuario(user.id);
                    }


                }
            })
            .fail(function () {
                Swal.fire('Error', 'No se pudo verificar el usuario.', 'error');
            });
    }

    // Función auxiliar que envía la autorización al servidor
    function autorizarUsuario(userId) {
        $.ajax({
            url: '/admin/autorizados/autoriceTomador',               // tu URL para firstOrCreate en el back
            method: 'POST',
            data: { user_id: userId },
            dataType: 'json'
        })
            .done(function (resp) {
                Swal.fire('¡Listo!', 'Se ha autorizado correctamente.', 'success')
                    .then(() => {
                        // si quieres recargar la lista:
                        window.location.reload();
                    });
            })
            .fail(function () {
                Swal.fire('Error', 'No se pudo autorizar el usuario.', 'error');
                loadingMain(false);
            });
    }

    function formInicial() {
        $('#form_modal_autorizacion').form({
            fields: validations,
            on: 'blur',
            inline: true,
            onSuccess: function (event) {

                handleSubmit();
                loadingMain(true);

            },
            onFailure: function () {
                console.log('Formulario no válido');
            }
        }).form('validate form'); // Ejecutar la validación al instante
    }

    function loadingMain(loading) {
        const div = $("#mainLoading");
        if (div.length === 0) return;

        if (loading) {
            // Asegurar que el div cubra toda la ventana
            div.css({
                width: '100%',
                height: '100vh', // Ocupa toda la altura de la ventana
                position: 'fixed', // Fijo para cubrir toda la pantalla
                top: 0,
                left: 0,
            }).addClass("ui active dimmer");

            // Añadir el loader si no existe
            if (div.find(".loader").length === 0) {
                div.append('<div class="ui loader"></div>');
            }
        } else {
            // Remover el loader y limpiar estilos
            div.removeClass("active dimmer").css({
                width: '',
                height: '',
                position: '',
                top: '',
                left: '',
                'z-index': ''
            });
        }
    }
</script>