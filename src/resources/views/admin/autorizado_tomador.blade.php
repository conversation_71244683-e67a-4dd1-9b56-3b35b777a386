@extends('layouts.main')

@section('menu')
    @parent
@endsection

@section('content')
    <div class="ui basic segment">
        <div class="ui container">
            <h2 class="ui header">Autorizado por</h2>
            <p><strong>Correo autorizado:</strong> {{ auth()->user()->email }}</p>

            <div id="authorizedList" class="ui stackable doubling four column grid" style="gap:20px;">
                @foreach ($data_tomadores as $item)
                    <div class="column" style="padding:2px !important;">
                        <!-- 1) Muevo auth-item y data-affiliate-id al card -->
                        <div class="ui fluid card auth-item" data-affiliate-id="{{ $item->tomador->affiliate_id ?? 1 }}">
                            <div class="content block_perfil">
                                <div class="content_image">
                                    <img class="ui circular image img_perfil"
                                        src="{{ $item->tomador->photo ? secure_url('file/' . $item->tomador->photo) : asset('images/blank.png') }}">
                                </div>
                                <div class="content_data_profile">
                                    <div class="header">{{ $item->tomador->full_name }}</div>
                                    <div class="meta">{{ $item->tomador->email }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </div>

    <style>
        .block_perfil {
            display: flex;
            align-items: center;
            min-height: 120px;
        }

        .content_data_profile {
            flex: 1;
            min-width: 0;
            margin-right: 1rem;
            overflow: hidden;
        }

        .content_data_profile .header,
        .content_data_profile .meta {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .content_image {
            width: 40%;
        }

        .img_perfil {
            width: 90px !important;
            height: 90px !important;
        }

        /* Efecto hover para la tarjeta */
        .ui.fluid.card {
            transition: transform .15s, box-shadow .15s;
        }

        .ui.fluid.card:hover {
            transform: scale(1.03);
            box-shadow: 0 8px 20px rgba(0, 0, 0, .12);
            cursor: pointer;
        }

        .ui.fluid.card:hover .content {
            background-color: #f9fafb;
        }
    </style>

    <script>
        $(function() {
            // Capturamos clicks en la tarjeta (auth-item)
            $('#authorizedList').on('click', '.auth-item', function() {
                const affiliateId = $(this).data('affiliate-id') || 1;
                window.location.href = `/tomador/poliza/${affiliateId}/datos`;
            });
        });
    </script>
@endsection
