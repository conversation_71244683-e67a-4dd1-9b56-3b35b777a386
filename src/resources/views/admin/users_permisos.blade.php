@extends('layouts.main')

@section('menu')
    @parent
@endsection

@section('content')
    <div class="ui basic segment">
        <div class="ui grid">
            <div class="stretched row">
                <!-- Columna de Usuarios Autorizados -->
                <div class="six wide column">
                    <div class="ui segment">

                        <div class="content_title">
                            <h3 class="ui header">
                                Usuarios autorizados
                            </h3>
                            <button type="button" class="ui secondary icon button button_agg" onclick="openModal(this)"
                                data-tooltip="Añadir autorizados" data-position="left center">
                                <i class="plus circle icon"></i>
                            </button>
                        </div>


                        <div class="ui divider full-width-divider"></div>

                        <div class="ui fluid icon input" style="margin-bottom:0.5rem">
                            <input type="text" id="filterAuthorized" placeholder="Filtrar por nombre, email o documento…">
                            <i class="search icon"></i>
                        </div>

                        <div class="ui divider full-width-divider"></div>

                        <div id="authorizedListContainer" class="ui segment" style="max-height:400px; overflow-y:auto">
                            <div class="ui relaxed divided list" id="authorizedList">
                                @foreach ($users as $u)
                                    <div class="item auth-item" data-id="{{ $u->user->id }}"
                                        data-search="{{ strtolower($u->user->full_name . ' ' . $u->user->email . ' ' . $u->user->identification_number) }}">
                                        <div class="content">
                                            <div class="header">{{ $u->user->full_name }}</div>
                                            <div class="description">{{ $u->user->email }} ·
                                                {{ $u->user->identification_number }}
                                            </div>
                                            <div class="identification" style="display: none">
                                                {{ $u->user->identification_number }}
                                            </div>
                                        </div>
                                        {{-- <div class="right floated content">
                                            <button class="ui mini red button remove-auth"
                                                data-id="{{ $u->id }}">Quitar</button>
                                        </div> --}}
                                    </div>
                                @endforeach
                            </div>

                        </div>
                    </div>
                </div>

                <!-- Columna de Permisos -->
                <div class="ten wide column">

                    <div class="ui segment content_filter_total">

                        <form action="" id="polizas_form">
                            <div class="user_select">
                                <h3>Usuario: </h3>
                                <p id="user_select"></p>
                            </div>

                            <div class="ui divider"></div>
                            <div class="content_filter">
                                <div class="field polizas-field" id="polizas_field" style="width: 45%;">
                                    <label for="polizas_input" class="polizas-label">Pólizas</label>
                                    <div id="polizas_dropdown" class="ui fluid search selection dropdown polizas-dropdown">
                                        <input type="hidden" id="polizas_input" name="poliza_id"
                                            value="{{ request('poliza_id') }}">
                                        <i class="dropdown icon"></i>
                                        <div class="default text">Selecciona una póliza</div>
                                        <div class="menu">
                                            @foreach ($polizas_drop as $item)
                                                <div class="item polizas-item" data-value="{{ $item->consecutive }}">
                                                    SORT - {{ sprintf('%04d', $item->consecutive) }}
                                                </div>
                                            @endforeach
                                        </div>
                                    </div>
                                </div>
                                <div class="field">
                                    <button type="button" class="ui secondary icon button" id="reset_button"
                                        data-position="left center">
                                        <i class="undo icon"></i> Limpiar
                                    </button>
                                </div>
                            </div>
                        </form>


                    </div>
                    <div class="ui segment">
                        <div class="ui styled fluid accordion polizas_tomador">

                            @foreach ($polizas as $item)
                                <div class="title title_custom_ex">
                                    <span> <i class="dropdown icon"></i>
                                        Poliza {{ $item->formatNumberConsecutive() }}</span>
                                    <div class="ui toggle checkbox policy-toggle" id="{{ $item->activity_id }}"
                                        data-policy-id="{{ $item->activity_id }}">
                                        <input type="hidden" name="permissions" value="0" />
                                        <input type="checkbox" tabindex="0" class="hidden" name="permissions"
                                            onclick="event.stopPropagation();" onkeydown="event.stopPropagation();" value="1">
                                    </div>
                                </div>
                                <div class="content">

                                    <div class="title_permisos">
                                        <h3>Permisos</h3>
                                    </div>

                                    <div class="ui divider"></div>

                                    <table class="ui table" style="border: none;">
                                        <thead>
                                            <tr>
                                                <th style="border: none;">Funcionalidad</th>
                                                <th style="border: none;">Leer / Visualización</th>
                                                <th style="border: none;">Actualizar / Crear</th>
                                            </tr>
                                        </thead>
                                        <tbody id="general_permissions">
                                            @foreach ($view_permision as $index => $perm)
                                                <tr id="{{ $perm->short_name }}-{{ $item->activity_id }}">
                                                    {{-- Columna: Nombre del permiso --}}
                                                    <td style="border:none;">
                                                        {{-- Campo oculto con el id del permiso --}}
                                                        <input type="hidden" name="permissions[{{ $index }}][id]"
                                                            value="{{ $perm->id }}" />
                                                        {{ $perm->name }}
                                                    </td>
                                                    {{-- Leer --}}
                                                    <td style="border: none;" class="view">
                                                        <div class="ui toggle checkbox perm-view-toggle"
                                                            data-policy-id="{{ $item->activity_id }}"
                                                            data-perm-id="{{ $perm->id }}">
                                                            <input type="checkbox">
                                                            <label></label>
                                                        </div>
                                                    </td>

                                                    {{-- Editar --}}
                                                    <td style="border: none;" class="edit">
                                                        <div class="ui toggle checkbox perm-edit-toggle"
                                                            data-policy-id="{{ $item->activity_id }}"
                                                            data-perm-id="{{ $perm->id }}">
                                                            <input type="checkbox">
                                                            <label></label>
                                                        </div>
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            @endforeach
                        </div>
                        <!-- Paginación -->
                        <br>
                        <div class="pagination">
                            @if ($polizas->hasPages())
                                <div class="ui pagination menu">
                                    {{ $polizas->appends(request()->query())->links() }}
                                </div>
                            @endif
                            <p>Total de registros: {{ $polizas->total() }}</p>
                        </div>

                        <div class="field">
                            <button type="button" class="ui primary icon button" id="guardar_button"
                                data-position="left center">
                                <i class="save icon"></i> Guardar
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        @include('admin.components.modal_autorizacion')
    </div>


    <style>
        .full-width-divider {
            /* compensa el padding por defecto de .ui.segment (1rem a cada lado) */
            margin-left: -1rem !important;
            margin-right: -1rem !important;
        }

        .title_custom_ex {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
        }

        .polizas_tomador {
            height: 65 dvh;
            overflow-y: auto;
        }

        .title_permisos {
            background: #f9fafb;
            padding: 14px 10px;
        }

        .content_filter {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .content_filter_total {
            /* height: 96px; */
        }

        .pagination {
            display: grid;
            justify-items: end;
        }

        .auth-item {
            cursor: pointer;
            transition: background-color 0.2s;
        }

        /* Hover */
        .auth-item:hover {
            background-color: rgba(0, 0, 0, 0.03);
        }

        /* Selected */
        .auth-item.selected {
            background-color: #d0e2ff;
            /* tono suave para indicar selección */
        }

        .content_title {
            display: flex;
            align-items: center;
            justify-content: space-between
        }

        .user_select {
            display: flex;
            align-items: baseline;
        }
    </style>

    <script>
        $(document).ready(function () {

            $('.ui.checkbox').checkbox();
            $('.accordion')
                .accordion({});

            $('.ui.dropdown').dropdown({
                forceSelection: false
            });



            $('#polizas_dropdown').dropdown({
                onChange: function (value, text, $choice) {
                    // Si quieres evitar submits en selección "vacía"
                    if (!value) return;
                    // Hacer submit del form
                    $('#polizas_form').submit();
                }
            });


            $('#reset_button').on('click', function (e) {
                e.preventDefault();

                // 1) Resetea todo el formulario (inputs, selects nativos, textareas…)
                const $form = $('#polizas_form');

                // 2) Limpia todos tus Semantic UI dropdowns dentro del form
                $form.find('.ui.dropdown').dropdown('clear');

                // 3) (Opcional) Limpia el “Usuario:” si lo tienes
                $('#user_select').empty();

                // 4) Reconstruye la URL *sin* parámetros
                const url = new URL(window.location.href);
                url.search = '';
                // Usa replaceState para no llenar el historial
                history.replaceState(null, '', url.pathname);

                // 5) Envía el form para recargar sin parámetros
                $('form').submit();
            });


            //Filtro Autorizado

            const $filter = $('#filterAuthorized');
            const $items = $('#authorizedList .auth-item');

            $filter.on('input', function () {
                const term = $(this).val().toLowerCase().trim();
                $items.each(function () {
                    const hay = $(this).data('search').includes(term);
                    $(this).toggle(hay);
                });
            });


            // seleccion lista :

            // Evitar que el botón “Quitar” interrumpa el click en el contenedor
            $('#authorizedList').on('click', '.remove-auth', function (e) {
                e.stopPropagation();

            });

            // Selección única: al click en un item, deseleccionar todos y marcar solo ese
            $('#authorizedList').on('click', '.auth-item', function () {
                $('#authorizedList .auth-item').removeClass('selected');
                $(this).addClass('selected');
            });


            function cargarPermisos(response) {

                // Primero, desmarca todo para partir de cero
                $('.polizas_tomador .title .ui.toggle.checkbox')
                    .checkbox('uncheck');
                $('.polizas_tomador .content .ui.toggle.checkbox')
                    .checkbox('uncheck');

                // Ahora, por cada póliza en el JSON…
                response.polizas.forEach(policy => {
                    const actId = policy.activity_id; // ej. 325304
                    const wrapper = $(`.polizas_tomador .title #${actId}`);

                    // 1) Marca el toggle global de la póliza
                    wrapper.checkbox('check');

                    // 2) Para cada permiso interno (view/edit):
                    policy.permissions.forEach(perm => {
                        const key = perm.user_view
                            .short_name; // ej. "reportes_accidentes"
                        const trSel =
                            `#${key}-${actId}`; // coincide con tu <tr id="…">

                        // Marcar “Leer”
                        const viewCB = $(`${trSel} td.view .ui.checkbox`);
                        if (perm.view) {
                            viewCB.checkbox('check');
                        }

                        // Marcar “Editar”
                        const editCB = $(`${trSel} td.edit .ui.checkbox`);
                        if (perm.edit) {
                            editCB.checkbox('check');
                        }
                    });
                });
            }

            // Ejemplo: procesar automáticamente al seleccionar
            $('#authorizedList').on('click', '.auth-item', function () {
                // 1) Obtener ID y nombre

                loadingMain(true);
                const userId = $(this).data('id');
                const userName = $(this).find('.header').text();
                const identification = $(this).find('.identification').text();

                // 2) Mostrar en el panel de Usuario
                $('#user_select').html(`&nbsp;${userName} – ${identification}`);

                const newUrl = `${window.location.pathname}?user_id=${userId}`;
                // recarga la página de verdad (Blade recibirá user_id en GET)
                window.location.href = newUrl;

            });



            // 2) Al pulsar Guardar
            $('#guardar_button').on('click', function () {
                // Si tienes un usuario seleccionado, obtén su ID:
                loadingMain(true);

                const userId = $('#authorizedList .auth-item.selected').data('id');
                if (!userId) {
                    loadingMain(false);
                    return Swal.fire('Atención', 'Selecciona primero un usuario', 'warning');
                }

                // 3) Recolecta el payload
                const payload = [];

                // Por cada póliza
                $('.policy-toggle').each(function () {
                    const policyId = $(this).data('policy-id');
                    const active = $(this).find('input[type="checkbox"]').is(':checked') ? 1 : 0;

                    // Recolecta todos los permisos de esta póliza
                    const perms = [];
                    $(`.perm-view-toggle[data-policy-id="${policyId}"]`).each(function () {
                        const permId = $(this).data('perm-id');
                        const view = $(this).find('input[type="checkbox"]').is(':checked') ?
                            1 : 0;
                        const edit = $(
                            `.perm-edit-toggle[data-policy-id="${policyId}"][data-perm-id="${permId}"]`
                        )
                            .find('input[type="checkbox"]').is(':checked') ? 1 : 0;
                        perms.push({
                            permission_id: permId,
                            view,
                            edit
                        });
                    });

                    payload.push({
                        policy_id: policyId,
                        active: active,
                        permissions: perms
                    });
                });

                // 4) Envío por AJAX

                $.ajax({
                    url: '/admin/usuarios_permisos/save', // tu ruta en web.php
                    method: 'POST',
                    data: {
                        user_id: userId,
                        policies: payload
                    }
                })
                    .done(() => {
                        Swal.fire('¡Guardado!', 'Permisos y pólizas actualizados correctamente',
                            'success');

                        loadingMain(false);
                    })
                    .fail(err => {
                        console.error(err);
                        Swal.fire('Error', 'No se pudo guardar la información', 'error');
                        loadingMain(false);
                    });
            });


            // Función para disparar la carga si hay user_id en la URL
            function loadUserFromQuery() {
                const params = new URLSearchParams(window.location.search);
                const userId = params.get('user_id');
                if (!userId) return;

                loadingMain(true);

                // 1) Busca el ítem correspondiente y márcalo
                const $item = $(`#authorizedList .auth-item[data-id="${userId}"]`);
                if (!$item.length) return;

                $item.addClass('selected');
                const userName = $item.find('.header').text();
                const identification = $item.find('.identification').text();

                // 2) Muestra el nombre en el panel
                $('#user_select').html(`&nbsp;${userName} – ${identification}`);

                // 3) Lanza tu AJAX de carga de pólizas (puedes extraerlo a una función)
                $.ajax({
                    url: `/admin/usuarios_permisos/polices`,
                    method: 'POST',
                    data: {
                        user_id: userId
                    }
                })
                    .done(res => {
                        cargarPermisos(res);
                        loadingMain(false);
                    })
                    .fail(() => {
                        Swal.fire('Error', 'No se pudo cargar las pólizas', 'error');
                        loadingMain(false);
                    });
            }

            // Llamamos al init al cargar la página
            loadUserFromQuery();

        });

        function openModal() {

            $('#AutorizacionModal').modal('refresh').modal({
                inverted: false,
                autofocus: false,
                closable: false
            }).modal('show');
        }
    </script>
@endsection