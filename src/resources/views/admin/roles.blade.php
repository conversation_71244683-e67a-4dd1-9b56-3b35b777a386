@extends('layouts.main')

@section('menu')
    @parent
@endsection

@section('content')
    <div class="ui basic segment">
        <div class="ui grid">
            <div class="six wide column">
                <div class="ui segment">
                    <h1 class="ui header">
                        Roles
                    </h1>
                    <div class="ui celled selection relaxed small list">
                        @foreach($roles as $r)
                            <a class="item" href="{{secure_url('/admin/roles/' . $r->id)}}">
                                <div class="header">
                                    {{$r->name}}
                                </div>
                                Descripción: {{$r->description}}
                            </a>
                        @endforeach
                    </div>
                </div>
            </div>
            <div class="ten wide column">
                <form class="ui segment form" method="post">
                    <input name="form" type="hidden" value="ROLE"/>
                    {{csrf_field()}}
                    @if($role)
                        <div class="ui dividing header">
                            Actualizar rol
                        </div>
                    @else
                        <div class="ui dividing header">
                            Agregar rol
                        </div>
                    @endif
                    <input name="role[id]" type="hidden" value="{{$role ? $role->id : '' }}"/>
                    <div class="required field ">
                        <label>
                            Nombre
                        </label>
                        <input name="role[name]" type="text" value="{{$role ? $role->name : ''}}"/>
                    </div>
                    <div class="field">
                        <label>
                            Descripción
                        </label>
                        <textarea rows="2" name="role[description]"
                                  type="text">{{$role ? $role->description : ''}}</textarea>
                    </div>
                    <div class="ui dividing header">
                        Permisos generales
                    </div>
                    <div class="fields six">
                        <div class="inline field">
                            <div class="ui toggle checkbox">
                                <input type="hidden" name="role[aseguramiento]" value="0">
                                <label>Aseguramiento</label>
                                <input type="checkbox" tabindex="0" class="hidden"
                                       name="role[aseguramiento]"
                                       {{$role ? $role->aseguramiento ? 'checked' : '' : ''}} value="1">
                            </div>
                        </div>
                        <div class="inline field">
                            <div class="ui toggle checkbox">
                                <input type="hidden" name="role[bandeja]" value="0">
                                <label>Bandeja de trabajo</label>
                                <input type="checkbox" tabindex="0" class="hidden"
                                       name="role[bandeja]"
                                       {{$role ? $role->bandeja ? 'checked' : '' : ''}} value="1">
                            </div>
                        </div>
                        <div class="inline field">
                            <div class="ui toggle checkbox">
                                <input type="hidden" name="role[busqueda]" value="0">
                                <label>Busqueda</label>
                                <input type="checkbox" tabindex="0" class="hidden"
                                       name="role[busqueda]"
                                       {{$role ? $role->busqueda ? 'checked' : '' : ''}} value="1">
                            </div>
                        </div>
                        <div class="inline field">
                            <div class="ui toggle checkbox">
                                <input type="hidden" name="role[gestion]" value="0">
                                <label>Gestión</label>
                                <input type="checkbox" tabindex="0" class="hidden"
                                       name="role[gestion]"
                                       {{$role ? $role->gestion ? 'checked' : '' : ''}} value="1">
                            </div>
                        </div>
                        <div class="inline field">
                            <div class="ui toggle checkbox">
                                <input type="hidden" name="role[reports]" value="0">
                                <label>Reportes</label>
                                <input type="checkbox" tabindex="0" class="hidden"
                                       name="role[reports]"
                                       {{$role ? $role->reports ? 'checked' : '' : ''}} value="1">
                            </div>
                        </div>
                        <div class="inline field">
                            <div class="ui toggle checkbox">
                                <input type="hidden" name="role[administrator]" value="0">
                                <label>Administrador</label>
                                <input type="checkbox" tabindex="0" class="hidden"
                                       name="role[administrator]"
                                       {{$role ? $role->administrator ? 'checked' : '' : ''}} value="1">
                            </div>
                        </div>
                    </div>
                    <div class="ui dividing header">
                        Permisos tableros
                    </div>
                    <div class="fields six">
                        <div class="inline field">
                            <div class="ui toggle checkbox">
                                <input type="hidden" name="role[variations]" value="0">
                                <label>Variaciones</label>
                                <input type="checkbox" tabindex="0" class="hidden"
                                       name="role[variations]"
                                       {{$role ? $role->variations ? 'checked' : '' : ''}} value="1">
                            </div>
                        </div>
                        <div class="inline field">
                            <div class="ui toggle checkbox">
                                <input type="hidden" name="role[cobros]" value="0">
                                <label>Cobros</label>
                                <input type="checkbox" tabindex="0" class="hidden"
                                       name="role[cobros]"
                                       {{$role ? $role->cobros ? 'checked' : '' : ''}} value="1">
                            </div>
                        </div>
                        <div class="inline field">
                            <div class="ui toggle checkbox">
                                <input type="hidden" name="role[provider]" value="0">
                                <label>Proveedor</label>
                                <input type="checkbox" tabindex="0" class="hidden"
                                       name="role[provider]"
                                       {{$role ? $role->provider ? 'checked' : '' : ''}} value="1">
                            </div>
                        </div>
                        <div class="inline field">
                            <div class="ui toggle checkbox">
                                <input type="hidden" name="role[auditoria]" value="0">
                                <label>Auditoría médica</label>
                                <input type="checkbox" tabindex="0" class="hidden"
                                       name="role[auditoria]"
                                       {{$role ? $role->auditoria ? 'checked' : '' : ''}} value="1">
                            </div>
                        </div>
                        <div class="inline field">
                            <div class="ui toggle checkbox">
                                <input type="hidden" name="role[cuentas]" value="0">
                                <label>Cuentas médicas</label>
                                <input type="checkbox" tabindex="0" class="hidden"
                                       name="role[cuentas]"
                                       {{$role ? $role->cuentas ? 'checked' : '' : ''}} value="1">
                            </div>
                        </div>
                        <div class="inline field">
                            <div class="ui toggle checkbox">
                                <input type="hidden" name="role[auditoria_cuentas]" value="0">
                                <label>Auditoría cuentas médicas</label>
                                <input type="checkbox" tabindex="0" class="hidden"
                                       name="role[auditoria_cuentas]"
                                       {{$role ? $role->auditoria_cuentas ? 'checked' : '' : ''}} value="1">
                            </div>
                        </div>
                    </div>
                    <div class="fields six">
                        <div class="inline field">
                            <div class="ui toggle checkbox">
                                <input type="hidden" name="role[monitoreo]" value="0">
                                <label>Monitoreo y control</label>
                                <input type="checkbox" tabindex="0" class="hidden"
                                       name="role[monitoreo]"
                                       {{$role ? $role->monitoreo ? 'checked' : '' : ''}} value="1">
                            </div>
                        </div>
                        <div class="inline field">
                            <div class="ui toggle checkbox">
                                <input type="hidden" name="role[intermediario]" value="0">
                                <label>Intermediario</label>
                                <input type="checkbox" tabindex="0" class="hidden"
                                       name="role[intermediario]"
                                       {{$role ? $role->intermediario ? 'checked' : '' : ''}} value="1">
                            </div>
                        </div>
                        <div class="inline field">
                            <div class="ui toggle checkbox">
                                <input type="hidden" name="role[tomador]" value="0">
                                <label>Tomador</label>
                                <input type="checkbox" tabindex="0" class="hidden"
                                       name="role[tomador]"
                                       {{$role ? $role->tomador ? 'checked' : '' : ''}} value="1">
                            </div>
                        </div>
                        <div class="inline field">
                            <div class="ui toggle checkbox">
                                <input type="hidden" name="role[afiliado]" value="0">
                                <label>Afiliado</label>
                                <input type="checkbox" tabindex="0" class="hidden"
                                       name="role[afiliado]"
                                       {{$role ? $role->afiliado ? 'checked' : '' : ''}} value="1">
                            </div>
                        </div>
                        <div class="inline field">
                            <div class="ui toggle checkbox">
                                <input type="hidden" name="role[call_center]" value="0">
                                <label>Call center</label>
                                <input type="checkbox" tabindex="0" class="hidden"
                                       name="role[call_center]"
                                       {{$role ? $role->call_center ? 'checked' : '' : ''}} value="1">
                            </div>
                        </div>
                        <div class="inline field">
                            <div class="ui toggle checkbox">
                                <input type="hidden" name="role[ejecutivo_comercial]" value="0">
                                <label>Ejecutivo comercial</label>
                                <input type="checkbox" tabindex="0" class="hidden"
                                       name="role[ejecutivo_comercial]"
                                       {{$role ? $role->ejecutivo_comercial ? 'checked' : '' : ''}} value="1">
                            </div>
                        </div>
                    </div>

                    <div class="fields six">
                            <div class="inline field">
                                <div class="ui toggle checkbox">
                                    <input type="hidden" name="role[autorizaciones]" value="0">
                                    <label>Autorizaciones</label>
                                    <input type="checkbox" tabindex="0" class="hidden"
                                        name="role[autorizaciones]"
                                        {{$role ? $role->autorizaciones ? 'checked' : '' : ''}} value="1">
                                </div>
                            </div>
                            <div class="inline field">
                                <div class="ui toggle checkbox">
                                    <input type="hidden" name="role[correos]" value="0">
                                    <label>Correos</label>
                                    <input type="checkbox" tabindex="0" class="hidden"
                                        name="role[correos]"
                                        {{$role ? $role->correos ? 'checked' : '' : ''}} value="1">
                                </div>
                            </div>
                    </div>
                    {{--Permisos reportes --}}
                    <div class="ui dividing header">
                        Reportes
                    </div>
                    <div class="fields six">
                        <div class="inline field">
                            <div class="ui toggle checkbox">
                                <input type="hidden" name="role[reportes_internos]" value="0">
                                <label>Reportes internos</label>
                                <input type="checkbox" tabindex="0" class="hidden"
                                       name="role[reportes_internos]"
                                       {{$role ? $role->reportes_internos ? 'checked' : '' : ''}} value="1">
                            </div>
                        </div>
                        <div class="inline field">
                            <div class="ui toggle checkbox">
                                <input type="hidden" name="role[reporte_intermediario_tomador]" value="0">
                                <label>Reporte intermediario y tomador </label>
                                <input type="checkbox" tabindex="0" class="hidden"
                                       name="role[reporte_intermediario_tomador]"
                                       {{$role ? $role->reporte_intermediario_tomador ? 'checked' : '' : ''}} value="1">
                            </div>
                        </div>
                        <div class="inline field">
                            <div class="ui toggle checkbox">
                                <input type="hidden" name="role[reportes_empresariales]" value="0">
                                <label>Reportes empresariales</label>
                                <input type="checkbox" tabindex="0" class="hidden"
                                       name="role[reportes_empresariales]"
                                       {{$role ? $role->reportes_empresariales ? 'checked' : '' : ''}} value="1">
                            </div>
                        </div>
                        <div class="inline field">
                            <div class="ui toggle checkbox">
                                <input type="hidden" name="role[reportes_operativos_consolidados]" value="0">
                                <label>Reportes operativos consolidados</label>
                                <input type="checkbox" tabindex="0" class="hidden"
                                       name="role[reportes_operativos_consolidados]"
                                       {{$role ? $role->reportes_operativos_consolidados ? 'checked' : '' : ''}} value="1">
                            </div>
                        </div>
                        <div class="inline field">
                            <div class="ui toggle checkbox">
                                <input type="hidden" name="role[reportes_operativos]" value="0">
                                <label>Reportes operativos</label>
                                <input type="checkbox" tabindex="0" class="hidden"
                                       name="role[reportes_operativos]"
                                       {{$role ? $role->reportes_operativos ? 'checked' : '' : ''}} value="1">
                            </div>
                        </div>
                        <div class="inline field">
                            <div class="ui toggle checkbox">
                                <input type="hidden" name="role[reportes_enfermedad]" value="0">
                                <label>Reportes enfermedad</label>
                                <input type="checkbox" tabindex="0" class="hidden"
                                       name="role[reportes_enfermedad]"
                                       {{$role ? $role->reportes_enfermedad ? 'checked' : '' : ''}} value="1">
                            </div>
                        </div>
                    </div>
                    <div class="fields six">
                        <div class="inline field">
                            <div class="ui toggle checkbox">
                                <input type="hidden" name="role[reportes_fallecido]" value="0">
                                <label>Reportes fallecido</label>
                                <input type="checkbox" tabindex="0" class="hidden"
                                       name="role[reportes_fallecido]"
                                       {{$role ? $role->reportes_fallecido ? 'checked' : '' : ''}} value="1">
                            </div>
                        </div>
                        <div class="inline field">
                            <div class="ui toggle checkbox">
                                <input type="hidden" name="role[reportes_por_poliza]" value="0">
                                <label>Reportes por poliza</label>
                                <input type="checkbox" tabindex="0" class="hidden"
                                       name="role[reportes_por_poliza]"
                                       {{$role ? $role->reportes_por_poliza ? 'checked' : '' : ''}} value="1">
                            </div>
                        </div>
                    </div>
                    <div class="field" style="margin-top: 2rem">
                        <button class="ui primary  button">
                            <i class="save icon">
                            </i>
                            Guardar
                        </button>
                        @if ($role)
                            <a class="ui secondary button" href="{{secure_url('/admin/roles')}}">
                                <i class="cancel icon">
                                </i>
                                Cancelar
                            </a>
                        @endif

                    </div>
                </form>
                @if($role)
                    <div class="ui segment form">
                        <div class="ui dividing header">
                            Servicios asociados
                        </div>
                        <input name="id" type="hidden" value="{{$role->id}}"/>
                        <input name="form" type="hidden" value="SERVICES"/>
                        <div id="services" class="ui styled fluid accordion">
                            @if(count($role_services) == 0)
                                <div class="title">No hay servicios asociados</div>
                            @endif
                            <!-- SERVICES -->
                        </div>
                        <div style="margin-top: 2rem;">
                            <form method="post">
                                <input name="form" type="hidden" value="ADD_SERVICE"/>
                                {{csrf_field()}}
                                <div style="display:inline-flex;align-items: center; width: 100%;">
                                    <div class="ui field" style="width: 100%;">
                                        <label>Servicio</label>
                                        <div class="ui fluid selection dropdown">
                                            <input name="service_id" type="hidden"/>
                                            <i class="dropdown icon"></i>
                                            <div class="default text"></div>
                                            <div class="upward menu">
                                                @foreach($services as $s)
                                                    <div class="item" data-value="{{$s->id}}">
                                                        <span class="description">{!!implode(', ', array_map(function($c) { return $c['acronym']; }, $s->clients->toArray()))!!}</span>
                                                        <span class="text">{{$s->name}}</span>
                                                    </div>
                                                @endforeach
                                            </div>
                                        </div>
                                    </div>
                                    <button class="ui secondary button"
                                            style="width: 15rem;margin-top: 0.6rem; margin-left: 1rem">
                                        <i class="plus icon"> </i>
                                        Asociar servicio
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                @endif
                <!-- BASE SERVICE -->
                <div id="base_service" style="display: none;">
                    <div class="title">
                        <i class="dropdown icon"></i>
                        {name}
                    </div>
                    <div class="content">
                        <div class="transition hidden">
                            <form method="post">
                                <input name="form" type="hidden" value="UPDATE_SERVICE"/>
                                <input name="service_id" type="hidden" value="{id}"/>
                                {{csrf_field()}}
                                <div class="ui dividing header">
                                    Generales
                                </div>
                                <div class="ui grid three column">
                                    <div class="column">
                                        <b>Funcionalidad</b>
                                    </div>
                                    <div class="column">
                                        <b>Leer / Visualización</b>
                                    </div>
                                    <div class="column">
                                        <b>Actualizar / Crear</b>
                                    </div>
                                </div>
                                <div id="general_permissions" style="margin-top: 1rem;margin-left: 0.5rem">
                                    <!-- GENERAL PERMISSIONS -->
                                </div>
                                <div class="ui dividing header">
                                    Funcionalidades del servicio
                                </div>
                                <div class="ui grid four column">
                                    <div class="column">
                                        <b>Funcionalidad</b>
                                    </div>
                                    <div class="column">
                                        <b>Leer / Visualización</b>
                                    </div>
                                    <div class="column">
                                        <b>Actualizar / Crear</b>
                                    </div>
                                    <div class="column">
                                        <b>Estados</b>
                                    </div>
                                </div>
                                <div id="custom_permissions" style="margin-top: 1rem;margin-left: 0.5rem">
                                    <!-- CUSTOM PERMISSIONS -->
                                </div>
                                <button class="ui primary button" style="margin-top: 2rem;">
                                    <i class="save icon"> </i>
                                    Guardar
                                </button>
                                <a class="ui secondary button" href="{{secure_url('/admin/estados?s={id}')}}">
                                    Ver estados del servicio
                                </a>
                            </form>
                            <div style="float: right;margin-top: -2.5rem">
                                <form method="post">
                                    <input name="form" type="hidden" value="REMOVE_SERVICE"/>
                                    <input name="service_id" type="hidden" value="{id}"/>
                                    {{csrf_field()}}
                                    <button class="ui secondary button">
                                        <i class="trash icon"> </i>
                                        Remover servicio
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- BASE PERMISSION -->
                <div id="base_permission" class="ui grid" style="display: none;margin: 0">
                    <input name="permissions[{index}][id]" type="hidden" value="{id}"/>
                    <div class="column">
                        {name}
                    </div>
                    <div class="column">
                        <div class="inline field">
                            <div class="ui toggle checkbox">
                                <input type="hidden" name="permissions[{index}][view]" value="0"/>
                                <label></label>
                                <input type="checkbox" tabindex="0" class="hidden" {view}
                                       name="permissions[{index}][view]" value="1">
                            </div>
                        </div>
                    </div>
                    <div class="column">
                        <div class="inline field">
                            <div class="ui toggle checkbox">
                                <input type="hidden" name="permissions[{index}][edit]" value="0"/>
                                <label></label>
                                <input type="checkbox" tabindex="0" class="hidden" {edit}
                                       name="permissions[{index}][edit]" value="1">
                            </div>
                        </div>
                    </div>
                    <div class="column">
                        <div class="inline field">
                            <div class="ui selection multiple dropdown search">
                                <input type="hidden" name="permissions[{index}][states]" value="{states}"/>
                                <label></label>
                                <i class="dropdown icon"></i>
                                <div class="default text">Seleccione uno</div>
                                <div class="menu">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script type="text/javascript">
        const services = {!! $role_services ? json_encode($role_services) : json_encode('[]') !!};

        window.onload = function () {
            for (let service of services) {
                addService(service)
            }
            $('.ui.accordion').accordion();
            $('.ui.checkbox').checkbox();
            $('.ui.dropdown').dropdown();
        };

        let servicesIndex = 0;
        const addService = function (service) {
            const serv = $('#base_service').clone(true);
            serv.attr('id', 'service_' + servicesIndex);
            serv.html(serv.html().replaceAll('{index}', 'service_' + servicesIndex));
            serv.html(serv.html().replaceAll('{name}', service?.name));
            serv.html(serv.html().replaceAll('{id}', service?.id));
            $('#services').append(serv);
            serv.show();

            for (let permission of service?.permissions?.filter(p => p.description === 'GENERAL')) {
                addServiceGeneralPermission(servicesIndex, permission, true)
            }
            for (let permission of service.permissions.filter(p => p.description !== 'GENERAL')) {
                addServiceGeneralPermission(servicesIndex, permission, false, service?.states)
            }
            servicesIndex++;
        };

        let permissionsIndex = 0;

        const addServiceGeneralPermission = function (service_index, permission, general_permissions = true, service_states = []) {
            const serv = $('#base_permission').clone(true);
            serv.attr('id', permissionsIndex);
            const columnClass = general_permissions ? 'three column' : 'four column';
            serv.addClass(columnClass);
            serv.html(serv.html().replaceAll('{index}', permissionsIndex));
            serv.html(serv.html().replaceAll('{name}', capitalizeStringWithSpaces(permission?.name)));
            serv.html(serv.html().replaceAll('{id}', permission?.id));
            const areaPermission = permission?.area_permissions ? permission?.area_permissions[0] : null;
            serv.html(serv.html().replaceAll('{view}', areaPermission?.view ? 'checked' : ''));
            serv.html(serv.html().replaceAll('{edit}', areaPermission?.edit ? 'checked' : ''));
            serv.html(serv.html().replaceAll('{states}', areaPermission?.states ?? ''));

            if (!general_permissions) {
                const dropdownMenu = serv.find('.menu');
                dropdownMenu.empty();
                service_states.forEach(state => {
                    dropdownMenu.append(`<div class="item" data-value="${state.id}">${state.name}</div>`);
                });
            } else {
                serv.find('.ui.selection.multiple.dropdown').parent().remove();
            }

            $('#service_' + service_index).find(general_permissions ? '#general_permissions' : '#custom_permissions').append(serv);

            serv.show();
            permissionsIndex++;
        };

        function capitalizeStringWithSpaces(inputString) {
            let palabras = inputString.toLowerCase().split('_');
            palabras[0] = palabras[0].charAt(0).toUpperCase() + palabras[0].slice(1).toLowerCase();
            return palabras.join(' ');
        }
    </script>
@endsection
