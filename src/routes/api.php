<?php

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
 */

use App\Activity;
use App\Http\Controllers\UtilsController;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

Route::group(['domain' => '{cpath}.' . ENV('APP_DOMAIN', 'renapp.com'), 'middleware' => 'auth.basic'], function () {

    Route::post('/masivos/bydocs/{service_id}', 'MassiveController@bydocs');

    // REPORTES
    Route::get('/reportes/devoluciones', 'ReportController@correspondenceReturn');
    Route::get('/reportes/documentos/el', 'ReportController@documentsEL');
    Route::get('/reportes/basico', 'ReportController@basic');
    Route::get('/reportes/tramitados', 'ReportController@processed');
    Route::get('/reportes/recobros', 'ReportController@recobros2');
    Route::get('/reportes/glosas', 'ReportController@glosas');
    Route::get('/reportes/correspondencia', 'ReportController@correspondence');
    Route::get('/reportes/correspondencia2018', 'ReportController@correspondence2018');
    Route::get('/reportes/acciones', 'ReportController@actions');
    Route::get('/reportes/estados2', 'ReportController@states2');
    Route::get('/reportes/estados', 'ReportController@states');
    Route::get('/reportes/atel', 'ReportController@atel');
    Route::get('/reportes/atel_diagnostics', 'ReportController@atelDiagnostics');
    Route::get('/reportes/pcl', 'ReportController@pcl');
    Route::get('/reportes/pathological', 'ReportController@pathological');
    Route::get('/reportes/pcl_equidad', 'ReportController@pcl_equidad');
    Route::get('/reportes/revision_pcl_equidad', 'ReportController@revision_pcl_equidad');
    Route::get('/reportes/risk', 'ReportController@risk');
    Route::get('/reportes/pcl/value/{id}', 'ReportController@pclValue');
    Route::get('/reportes/disability', 'ReportController@disability');
    Route::get('/reportes/mobility', 'ReportController@mobility');
    Route::get('/reportes/rehabilitation', 'ReportController@rehabilitation');
    Route::get('/reportes/recommendation', 'ReportController@recommendation');
    Route::get('/reportes/injuries', 'ReportController@injuries');
    Route::get('/reportes/injuries2', 'ReportController@injuries2');
    Route::get('/reportes/audit', 'ReportController@audit');
    Route::get('/reportes/audit2019', 'ReportController@auditRadication');
    Route::get('/reportes/auditPri', 'ReportController@auditPri');

    Route::get('/reportes/valoration', 'ReportController@valoration');
    Route::get('/correspondence/json/{id?}', 'CorrespondenceController@downloadJson');
    Route::get('/correspondence/json_client/{id?}', 'CorrespondenceController@downloadClientJson');

    Route::get('/ws/recobros/{date?}', 'ApiController@recobros');
    Route::get('/ws/glosas/{date?}/{type?}', 'ApiController@glosas');

    // REPORTES LLAMADAS
    Route::get('/reportes/llamadascompletas', 'ReportController@completeCalls');
    Route::get('/reportes/llamadasincompletas', 'ReportController@incompleteCalls');

    // REPORTES MEDIMAS
    Route::get('/medimas/B1/firm', 'ReportMedimasController@b1_firm');

    Route::get('/medimas/B2/consolidado', 'ReportMedimasController@b2_consolidation');
    Route::get('/medimas/B2/rehabilitation', 'ReportMedimasController@b2_rehabilitation');
    Route::get('/medimas/B2/recommendation', 'ReportMedimasController@b2_recommendation');
    Route::get('/medimas/B2/controversy', 'ReportMedimasController@b2_controversy');
    Route::get('/medimas/B2/cargue', 'ReportMedimasController@b2_cargue');
    Route::get('/medimas/B2/furat', 'ReportMedimasController@b2_furat');
    Route::get('/medimas/B2/valoration', 'ReportMedimasController@b2_valoration');
    Route::get('/medimas/B2/audit', 'ReportMedimasController@b2_audit');
    Route::get('/medimas/B2/affiliates', 'ReportMedimasController@b2_affiliates');

    Route::get('/medimas/B3/consolidado_docs', 'ReportMedimasController@b3_consolidation');
    Route::get('/medimas/B3/at', 'ReportMedimasController@b3_at');
    Route::get('/medimas/B3/el', 'ReportMedimasController@b3_el');
    Route::get('/medimas/B3/pcl', 'ReportMedimasController@b3_pcl');
    Route::get('/medimas/B3/disability', 'ReportMedimasController@b3_disability');
    Route::get('/medimas/B3/rehabilitation', 'ReportMedimasController@b3_rehabilitation');
    Route::get('/medimas/B3/recommendation', 'ReportMedimasController@b3_recommendation');
    Route::get('/medimas/B3/audit', 'ReportMedimasController@b3_audit');
    Route::get('/medimas/B3/valoration', 'ReportMedimasController@b3_valoration');
    Route::get('/medimas/B3/jci', 'ReportMedimasController@b3_jci');
    Route::get('/medimas/B3/others', 'ReportMedimasController@b3_others');
    Route::get('/medimas/B3/cargue', 'ReportMedimasController@b3_cargue');
    Route::get('/medimas/B3/furat', 'ReportMedimasController@b3_furat');
});
Route::post('/updateActivityBizagi', function (Request $request) {
    $activity = Activity::find($request->input('activityId'));
    $idBizagi = Activity::query()->where('id_bizagi', $request->input('value'))->first();
    if ($idBizagi) {
        return response()->json(['success' => false, 'existingId' => $idBizagi->id]);
    }
    $activity->id_bizagi = $request->input('value');
    $activity->save();
    if ($activity->service_id == \App\Service::SERVICE_DETERMINATION_IT_COLPENSIONES) {
        $determination_it = \App\DeterminationIt::query()->where('activity_id', $activity->id)->first();
        if ($determination_it) {
            $determination_it->numero_radicado = $activity->id_bizagi;
            $determination_it->save();
        }
    } elseif ($activity->service_id == \App\Service::SERVICE_PCL_COLPENSIONES) {
        $pcl = \App\Pcl::query()->where('activity_id', $activity->id)->first();
        if ($pcl) {
            $pcl->radication_number = $activity->id_bizagi;
            $pcl->save();
        }
    }

    return response()->json(['success' => true]);
});


// API
Route::group(['domain' => '{cpath}.' . ENV('APP_DOMAIN', 'renapp.com'), 'middleware' => ['cors']], function () {
    Route::get('/timezone', function ($cpath) {
        return response()->json([
            'timezone' => date_default_timezone_get(),
            'date' => date('Y-m-d'),
            'time' => date('H:i:s'),
            'datetime' => date('Y-m-d H:i:s'),
            'timestamp' => time(),
            'carbon' => Carbon::now()->toDateTimeString(),
        ]);
    });
    //Utils
    Route::get('/utils/zipFromS3', function ($cpath) {
        try {
            return UtilsController::zipFromS3([
                'documents/G5w920COoM66df0284324cd.docx',
                'documents/zZ1WACMQvq66df02849c31e.pdf',
                'documents/GeysTuQvU266df098714772.docx',
            ]);
        } catch (Exception $e) {
            dd($e);
        }
    });
    // SERVICES
    Route::post('/sendinblue', 'ApiExposedController@sendinblue');
    Route::get('/resend_email/{id}', 'MailController@resendEmail');
    Route::get('/personal_accident_without_reports', 'Services\PersonalAccidentController@personalAccidentWithoutReport');
    Route::get('/debtor_insurance_without_reports', 'Services\DebtorController@debtorInsuranceWithoutReport');
    Route::get('/itp_calification_without_reports', 'Services\ITPController@itpCalificationWithoutReport');
    Route::get('/validate_rule_actions', 'RuleActionsController@validate');
    Route::get('/ping_colpensiones_vpn', 'PingColpensionesVpnController@index');
    Route::get('/check_services_age_rei', 'Services\InvalidityStateController@checkServicesAgeRei');
    Route::post('/upload-image', 'EditorController@uploadImage');
    Route::get('/validateCaducado', 'AgentController@validateCaducado');
    Route::get('/report_insurance_increase_unpaid', 'Services\PolicySortCollectionController@reportInsuranceIncreaseUnpaid');
    Route::get('/cron_monthly_no_payment_receipt', 'Services\PolicySortCollectionController@cronMonthlyNoPaymentReceipt');
    Route::get('/cron_quarterly_no_payment_receipt', 'Services\PolicySortCollectionController@cronQuarterlyNoPaymentReceipt');
    Route::get('/cron_biannual_no_payment_receipt', 'Services\PolicySortCollectionController@cronBiannualNoPaymentReceipt');
    Route::get('/cron_rehabilitation_no_payment_receipt', 'Services\PolicySortCollectionController@cronRehabilitationNoPaymentReceipt');
    Route::get('/report_rejection_policy_variation', 'Services\VariationsSortController@cronReportDetentionResponse');
    //Notifica el quinto dia habil de cada mes que se debe reportar la planilla definitiva
    Route::get('/cron_notify_spreadsheet_upload_5th_business_day', 'Services\PolicySortController@cronMonthlyReportSpreadsheetUpload');
    //Notifica el quinto dia habil de calendario especial que se debe reportar la planilla definitiva
    Route::get('/cron_notify_spreadsheet_upload_5th_special_business_day', 'Services\ReportTakenFormController@cronSpecialMonthlyReportSpreadsheetUpload');
    Route::get('/reportar-planilla-definitiva', 'Services\PolicySortController@reportFinalPayroll');
    Route::get('/generate_certificate_planilla', 'Services\PolicySortController@generateCertificatePlanilla');
    //Sustitucion de planilla a los 10 dias habiles de cada mes
    Route::get('/reportar-planilla-definitiva-mensual', 'Services\PolicySortController@reportFinalMonthlyPayroll');
    //Sustitucion de planilla a los 10 dias habiles de calendario especial
    Route::get('/special_calendar_replacement_sheet', 'Services\ReportTakenFormController@specialCalendarReplacementSheet');
    Route::get('/generate_liquidation_policy', 'Services\LiquidationPolicyController@generateLiquidationPolicy');
    Route::get('/reportar_pago_fracciones_mensual', 'Services\PeItSortController@calculateAndSaveFraccionamientos');
    Route::get('/servicio/{id}/pe_it_sort/calculo', 'Services\PeItSortController@calculateAndSaveFraccionamientos');
    Route::get('/generate_fraction_payment_report', 'Services\PeItSortController@generateFractionPaymentReport');
    Route::get('/generate_payment_suspend', 'Services\PeItSortController@itPaymentSuspension');

    Route::post('/servicio/{id}/pe_it_sort/report_full_disability_payment', 'Services\PeItSortController@reportFullDisabilityPayment');
    Route::get('/servicio/{id}/pe_ip_sort/Integrado', 'Services\PeIpSortController@registerIpIntegrado');
    Route::get('/servicio/{id}/pe_ip_sort/Manually', 'Services\PeIpSortController@registerIpManually');
    Route::get('/servicio/{id}/pe_ip_sort/approve', 'Services\PeIPSortController@approvalPayment');
    Route::get('/generate_renewal_sort/{id?}', 'Services\RenewalSortController@generateRenewalSort');
    Route::get('/report_unpaid_renewal_receipt', 'Services\RenewalSortController@reportUnpaidRenewalReceipt');
    Route::get('/report_unpaid_settlement_receipt', 'Services\LiquidationPolicyController@reportUnpaidSettlementReceipt');

    Route::get('/issue_expiration_notice', 'Services\RenewalSortController@issueExpirationNotice');
    Route::get('/report_missing_affiliate', 'Services\ComprehensiveAccidentManagementController@ReportMissingMemberAutomatic');
    Route::post('/payments', 'Services\AffiliatePaymentController@receivePaymentSimulator'); // Recibir el pago
    Route::patch('/payments/update','Services\AffiliatePaymentController@updatePaymentSimulatorStatus');
    Route::post('/servicio/{id}/report_payment_it', 'Services\AffiliatePaymentController@reportPaymentIt');
    Route::get('/services/gis/reportMemberDeathApproval/{id}', 'Services\ComprehensiveAccidentManagementController@reportMemberDeathApproval');
    Route::get('/formal_case_format_closure', 'Services\ComprehensiveAccidentManagementController@formalCaseFormatClosureAutomatic');
    Route::get('/closure_case_gis', 'Services\ComprehensiveAccidentManagementController@closingCaseAutomatic');

    Route::get('/enviar-certificados-emision-poliza/{id}', 'Services\PolicySortController@actionIssuePolicy');

    Route::get('/enviar-correo-cotizacion-generada/{id}', 'Services\QuotationController@sendGeneratedQuoteEmailAsync');


    Route::get('/generate-account-entry/{id}', 'Services\PolicySortController@generateAccountingEntry');
    Route::get('/report_account_case_one_003/{id}', 'Services\AccountingEntryController@reportAccountCaseTwo003');

    Route::post('/servicio/{id}/{host}/policy_sort/generateCertificate', 'Services\PolicySortController@generateCertificate');

    Route::post('/generateDocument', 'PdfController@generatePdf');
    Route::get('/report_account_case_308', 'Services\AccountingEntryController@autoAccountCase308');
    Route::get('/report_account_case_305', 'Services\AccountingEntryController@autoAccountCase305');
    Route::get('/report_account_case_150', 'Services\AccountingEntryController@autoAccountCase150');
    Route::get('/report_account_case_301', 'Services\AccountingEntryController@autoAccountCase301');
    Route::get('/report_account_case_306', 'Services\AccountingEntryController@autoAccountCase306');
    Route::get('/report_account_case_138', 'Services\AccountingEntryController@autoAccountCase138');
    Route::get('/report_account_case_307', 'Services\AccountingEntryController@autoAccountCase307');

    Route::get('/report_account_case_153/{id}/{value}', 'Services\AccountingEntryController@reportAccountCaseOneTwo153');
    Route::get('/report_account_case_154/{id}/{value}', 'Services\AccountingEntryController@reportAccountCase154');
    
    // Ruta para getDataIntermediary
    Route::get('/intermediary-data/{code_mnk}/{user_name}', 'Integrations\WebserviceAcselController@getDataIntermediary');

    //PPS 7 muerte persona asegurada
    Route::get('/pps-death-worker/{id}', 'Services\ComprehensiveAccidentManagementController@calculatePpsDeathWorker');

    //Factores de conmutacion 
    Route::get('/calculate-comutation-factor/{id}', 'Services\ComprehensiveAccidentManagementController@calculateCommutationFactor');
    
    //ejecutar asiento 050,
    Route::get('/asiento_050/{activityGis}/{ActivityGisParent}/{value}', 'Services\AccountingEntryController@reportAccountCase050');
    //Crear planilla automática a los 10 dias habiles despues de emision
    Route::get('generate_default_spreadsheet', 'Services\ReportTakenFormController@generateDefaultSpreadsheet');
    //Generar estado de cuenta abono mensual
    Route::get('generate_account_statement_monthly_payment', 'AccountStatementController@generateAccountStatementMonthlyPayment');
    //Generar estado de cuenta abono trimestral
    Route::get('generate_account_statement_quarterly', 'AccountStatementController@generateAccountStatementQuarterly');
    //Genara estado de cuenta abono semestral
    Route::get('generate_account_statement_semmiannual', 'AccountStatementController@generateAccountStatementSemmiannual');
    //Inactiva proveedores
    Route::get('/desactivate_providers', 'ProviderController@deactivateProviders');
    //envio de encuesta gis
    Route::get('/send_survey_gis', 'Services\ComprehensiveAccidentManagementController@sendSurveyGis');
    Route::get('/report_account_case_087', 'Services\AccountingEntryController@autoAccountCase087');
    Route::get('/notify_policy_liquidation', 'Services\LiquidationPolicyController@notifyPolicyLiquidation');
    Route::get('/export_today_tc', 'Services\PolicySortCollectionController@exportTodayTc');
    Route::get('/insert_premium_surplus/{id}', 'Services\PolicySortCollectionController@insertPremiumSurplus');


});
