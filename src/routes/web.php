<?php

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
 */

use App\Action;
use App\ActionField;
use App\Activity;
use App\ActivityAction;
use App\Affiliate;
use App\Client;
use App\CorrespondenceItem;
use App\DeterminationIt;
use App\EconomicActivity;
use App\Employer;
use App\Holiday;
use App\Http\Middleware\CheckClientAccess;
use App\Mail\SendDocument;
use App\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Storage;

Auth::routes();

Route::get('debug-sentry', function () {
    throw new Exception('My first Sentry error! Positiva');
});
//Route::get('version', function () {
//    phpinfo();
//});
Route::get('callFinder/{date}/{call_id}', 'CallController@finder');
Route::get('test', function () {
    User::query()->firstOrFail();
    return response()->json([
        "status" => "success",
        "name db" => DB::connection()->getDatabaseName(),
        "host db" => env('DB_HOST'),
        "php version" => phpversion(),
        "laravel version" => app()->version(),
        "app_url" => env('APP_URL'),
        "api_url" => env('API_URL'),
    ]);
});

Route::prefix('api')->group(function () {
    Route::post('cambioestado/states', "StateChangeController@getServiceStates");
    Route::get('get_document_manager', "Integrations\SoapRequest\DocumentManagerController@getDocument");
    Route::get('get_register_file', "Integrations\SoapRequest\DocumentManagerController@registerFile");
});

Route::get('callFinder/{date}/{filename}', 'CallController@finderByName');
Route::get('callFinderWsdl', 'CallController@finderWsdl');
Route::get('downloadWavs/{id}', function ($id) {
    return '<META HTTP-EQUIV="Refresh" CONTENT="1; URL=/downloadWav/' . $id . '"> <script> setTimeout(function(){  window.location.href = "/downloadWavs/' . ($id + 1) . '"; }, 10000) </script>  LLAMADA # ' . $id;
});

Route::get('testing-email-fapo/{id}', function ($id = 1) {

    config(['mail' => ['driver' => 'mail', 'host' => 'correo.renconsultores.com.co', 'port' => '465', 'from' => ['address' => '<EMAIL>', 'name' => 'PQR CONVENIO POSITIVA'], 'encryption' => 'ssl', 'username' => '<EMAIL>', 'password' => 'PqrRen.2019*']]);

    Mail::to('<EMAIL>')
        ->send(new SendDocument('<EMAIL>', 'testeo', [], 'Este es un mensaje de prueba'));
});

Route::get('downloadWav/{id}', function ($id) {
    if ($id < 0)
        $id = 0;

    $calls = App\ActivityActionField::where('value', 'like', '%.wav')
        ->select(['value'])
        ->orderBy('id')
        ->limit(1)
        ->offset($id)
        ->first()->toArray();

    if (isset($calls['value'])) {
        $items = explode("/", $calls['value']);
        $last = ($items[count($items) - 1]);
        header('Content-Description: File Transfer');
        header('Content-Type: application/octet-stream');
        header('Content-Disposition: attachment; filename=' . $last);
        header('Content-Transfer-Encoding: binary');
        header('Expires: 0');
        header('Cache-Control: must-revalidate');
        header('Pragma: public');

        ob_clean();
        flush();
        readfile($calls['value']);
    } else {
        exit();
    }
});


// Embebbed Routes

Route::group(['domain' => '{cpath}.' . ENV('APP_DOMAIN', 'renapp.com'), 'middleware' => 'iframe'], function () {

    // EMAILS
    Route::get('/list-emails', 'ServiceController@viewEmails');

    Route::get('/radicacion', 'InabilityController@radication');

    Route::get('/tutorial_radicacion', 'InabilityController@tutorial');

    Route::get('/radicacion-descarga-certificado', 'InabilityController@crtDown');
    Route::get('/radicacion-descarga-apolo', 'InabilityController@crtDownApolo');

    Route::get('/radicacion-descarga-certificado-incapacidad', 'InabilityController@crtDownInability');

    Route::get('/descarga-certificados-incapacidades-filtrado', 'InabilityController@crtDownInabilityFiltered');
    Route::get('/descarga-certificados-incapacidades-json', 'InabilityController@crtDownInabilityJson');
    Route::get('/descarga-certificados-incapacidades-json-tst', 'InabilityController@crtDownInabilityJsonTest');
    Route::get('/descarga-apolo-filtrado', 'InabilityController@ApoloDownInabilityFiltered');

    Route::get('/radicacion_oficina', 'InabilityController@radicationOffice');

    Route::get('/radicacion_verify_disabilities_pdf/{activity_id}', 'InabilityController@radicacionVerifyDisabilitiesPdf');

    Route::get('/radicacion-descarga-certificado-oficina', 'InabilityController@crtDownOffice');

    Route::post('/radicacion_oficina', 'InabilityController@radicationOffice');

    Route::get('/cerrar_sesion_huella', 'InabilityController@closeSessionHuella');

    Route::get('/radicacion-masiva', 'InabilityController@massiveRadication');
    //Route::get('/radicacion-bank', 'InabilityController@bankRadication');

    Route::get('/radicacion-masiva-oficina', 'InabilityController@massiveRadicationOffice');
    //Route::get('/radicacion-bank-oficina', 'InabilityController@bankRadicationOffice');

    Route::post('radicacion/save', 'InabilityController@save');
    //Route::post('radicacion_bank/save', 'InabilityController@saveBank');
    Route::post('radicacion/pdf', 'InabilityController@pdf');

    Route::get('/asignacion_auditoria', 'AssignmentController@form');
    Route::post('asignacion_auditoria/save', 'AssignmentController@save');

    Route::post('/radicacion/upload-massive', 'InabilityController@uploadMassive');
    Route::post('/radicacion/upload-massive-private', 'InabilityController@uploadMassivePrivate');
    Route::post('/radicacion/upload-massive-responses', 'InabilityController@uploadMassiveResponses');

    Route::get('/load-disabilities-web-services/{type_ide}/{num_id}', 'InabilityController@loadDisabilities');
    Route::get('/load-disabilities-pdf-web-services/{affiliate_id}/{state?}', 'InabilityController@loadDisabilitiesHistoryPDF');
    Route::get('/load-disabilities-pdf-web-services-filtered/{affiliate_id}/{init_date}/{last_date}', 'InabilityController@loadDisabilitiesHistoryPDFFiltered');
    Route::get('/load-disabilities-json/{affiliate_id}', 'InabilityController@queryJsonInabilities');
    Route::get('/load-disabilities-json-tst/{affiliate_id}', 'InabilityController@queryJsonInabilitiesTest');
    Route::get('/load-apolo-web-services-filtered/{affiliate_id}', 'InabilityController@loadApoloFiltered');
    Route::get('/load-authorizations-web-services/{type_ide}/{num_id}', 'InabilityController@loadAuthorizations');
    Route::get('/load-response-audit-web-services/{num_id}', 'InabilityController@loadResponseAudit');

    Route::get('/radicacion_ips', 'IpsInabilityController@ipsRadication');
    Route::get('/radicacion_ips_masiva', 'IpsInabilityController@onConstruction');
    Route::get('/radicacion_ips_license_massive', 'IpsInabilityController@onConstructionLicense');
    Route::post('radicacion_ips/save', 'IpsInabilityController@save');
    Route::get('/radicacion_ips_license', 'IpsInabilityController@ipsRadicationLicense');
    Route::post('radicacion_ips_license/save', 'IpsInabilityController@saveLicense');


    Route::get('/load-radication-web-services/{num_id}', 'InabilityController@loadRadication');

    Route::get('/pass-radications-web-services', 'InabilityController@passRadication');
    Route::get('/re_send_audit_transmission/{activities}', 'InabilityController@rePassRadication');
    Route::get('/update_auth_disa/{affiliate_id}/', 'InabilityController@updateAuthDisa');

    Route::get('TesTing/WebService', 'InabilityController@testRedPrestadores');

    Route::get('api/affiliate/{num}/{type?}', function ($cpath, $num, $type = 'CC') {
        $client = Client::where('path', $cpath)->firstOrFail();
        return Affiliate::where('client_id', '=', $client->id)->where('doc_number', $num)->where('doc_type', $type)->get();
    });
    Route::get('api/employer-a/{nit}', function ($cpath, $nit) {
        return Employer::where('nit', $nit)->with('branches')->get();
    });

    Route::get('api/networks', function ($cpath) {
        return \App\Network::select('ips_code', 'name')->get();
    });
    Route::get('api/doctors', function ($cpath) {
        return \App\Network::where('doc_type', '=', 'CC')->select('doc_number', 'name')->get();
    });

    Route::get('api/download-certificate', 'InabilityController@downloadCertificate');
    Route::get('api/download-certificate-radicate/{docNumber}', 'InabilityController@downloadCertificateRadication');
    Route::get('api/download-certificate-apolo/{docNumber}', 'InabilityController@downloadApolo');

    Route::get('/download-word/{id}', function ($cpath, $id) {

        $activity = \App\Activity::find($id);

        $name = 'No_INFO';
        $html = '<b>No hay información de la actividad o la plantilla</b>';

        $headers = array(
            "Content-type" => "text/html",
            "Content-Disposition" => "attachment;Filename=" . $name . ".doc"
        );

        return response()->make($html, 200, $headers);
    });
});

//, 'middleware' => \App\Http\Middleware\CORS::class
Route::group(['domain' => '{cpath}.' . ENV('APP_DOMAIN', 'renapp.com')], function () {

    Route::get('/external_index_with_type/{type_user}', 'ExternalController@externalIndexWithType');
    Route::get('/external_form_with_type/{type_user}', 'ExternalController@externalFormWithType');

    Route::get('/external_index/{affiliate?}', 'ExternalController@externalIndex');
    Route::get('/external_index/{affiliate?}', 'ExternalController@externalIndex');
    Route::post('/external_affiliate_index', 'ExternalController@externalAffiliateIndex');
    Route::get('/external_login/{type_user?}', 'ExternalController@externalLogin');

    //Route::get('/external-create/{affiliate?}', 'HomeController@index');
    Route::post('/affiliate/saveexternal', 'HomeController@saveAffiliateExternal');
    Route::post('/servicio/affiliatequotation', 'Services\QuotationController@saveAffiliateQuotation');
    Route::post('/servicio/save-ecomonic-activity', 'Services\QuotationController@saveEconomicActivity');
    Route::post('/servicio/saveConditionSpecial', 'Services\QuotationController@saveConditionSpecial');

    Route::get('/external_service/{id}', 'ServiceController@view');
    Route::post('/external_save_cita', 'ExternalController@saveCitaExternal');
    Route::get('/productividad', 'ServiceController@productivity');
    Route::get('/board', 'BoardController@index');
    Route::get('/report-fgn', 'ReportFgnController@reportFgnStatus');
    Route::get('/emails_frame', 'AdminController@emailsFrame');
    Route::get('/productivityActions', 'ServiceController@productivityActions');
    Route::get('/productividad-excel', 'ServiceController@productivityExcel');

    Route::get('/api/get_busy_appointments_valoration', 'ExternalController@getBusyAppointmentsValoration');

    Route::get('/report-process-pclo', 'ReportProcessPcloController@reportProcessPcloGisPcl');
    Route::get('/report-control-panel-pclo', 'ReportProcessPcloController@reportControlPanelPcloGisPcl');
});
// End Embebbed Routes

Route::group(['domain' => '{cpath}.' . ENV('APP_DOMAIN', 'renapp.com'), 'middleware' => CheckClientAccess::class], function () {

    // MI PERFIL
    Route::get('/perfil', 'UserController@profile');
    Route::post('/profile/chpw', 'UserController@changePassword');
    Route::post('/profile/chpic', 'UserController@changePicture');
});

Route::group(['domain' => '{cpath}.' . ENV('APP_DOMAIN', 'renapp.com'), 'middleware' => 'iframe'], function () {

    // radicacion_rama_judicial_testing
    Route::get('/radicacion_rama_judicial', 'RadicationJudicialController@radication');
    Route::post('radicacion_rama_judicial/save', 'RadicationJudicialController@saveRadication');

    //Satisfaction survey
    Route::get('/radicacion_encuesta_salud', 'RadicationSatisfactionSurveyController@satisfactionSurvey');
    Route::post('radicacion_encuesta_salud/save', 'RadicationSatisfactionSurveyController@saveSatisfactionSurvey');


    Route::get('/data_studio', 'BillingByServiceController@dataStudio');
    Route::get('/data_studio_how_are_we_going', 'BillingByServiceController@dataStudioHowAreWeGoing');
    Route::get('/gis_phase_two', 'BillingByServiceController@gisPhaseTwo');
    Route::get('/gis_phase_three', 'BillingByServiceController@gisPhaseThree');
    Route::get('/precio_por_servicio', 'BillingByServiceController@billing');
    Route::post('precio_por_servicio/save', 'BillingByServiceController@saveBilling');
    Route::post('precio_por_servicio/save_projections', 'BillingByServiceController@saveServiceQuantity');
    Route::get('precio_por_servicio/get_service', 'BillingByServiceController@getService');

    Route::get('api/affiliate/{num}/{type?}', function ($cpath, $num, $type = 'CC') {
        $client = Client::query()
            ->where('path', $cpath)
            ->firstOrFail();
        $affiliates = Affiliate::query()
            ->where('client_id', '=', $client->id)
            ->where('doc_number', $num)
            ->where('doc_type', $type)
            ->get();
        $activity = Activity::query()
            ->where('affiliate_id', $affiliates[0]->id)
            ->orderBy('id', 'desc')
            ->first();
        if ($activity) {
            $affiliates[0]->last_service = $activity->psychological_orientation;
        }
        return $affiliates;
    });
    Route::get('api/employer-a/{nit}', function ($cpath, $nit) {
        return Employer::where('nit', $nit)->with('branches')->get();
    });
});

Route::group([
    'domain' => '{cpath}.' . ENV('APP_DOMAIN', 'renapp.com'),
    'middleware' => [CheckClientAccess::class, 'renew.password']
], function () {

    Route::get('/EnvLog/verificar', 'Integrations\EnviosLogisticosController@verify');
    Route::get('/checkFirm/verificar', 'Integrations\CheckFirmStatusController@verify');

    Route::get('/TestingEmail', function ($cpath) {
        $client = App\Client::where('path', $cpath)->firstOrFail();
    });

    Route::get('/force/reenvios/{id}', function ($cpath, $id) {
        $last_id = $id;
        $client = App\Client::where('path', $cpath)->firstOrFail();

        $items = App\CorrespondenceItem::whereIn('id', [

            //INSERTE IDs de Correspondencia

        ])
            ->where('state', '=', '4')
            ->orderBy('id')
            ->limit(1)
            ->get();

        DB::beginTransaction();

        try {

            foreach ($items as $item) {

                $item->state = 6;
                $paths = explode(',', $item->path);

                // SEGUIMIENTO
                $action = Action::find(Action::FOLLOW);

                $activityAction = new ActivityAction;
                $activityAction->activity_id = $item->activity_id;
                $activityAction->action_id = $action->id;
                $activityAction->old_user_id = $item->activity->user_id;
                $activityAction->new_user_id = $item->activity->user_id;
                $activityAction->description = '<b>CORRESPONDENCIA:</b> [REENVIADO A ' . CorrespondenceItem::$ENTITY_TYPES[$item->entity_type] . '] ' . 'Se hace reenvio de forma masiva';
                $activityAction->alert_date = '2018-10-29';
                $activityAction->old_state_id = $item->activity->state_id;
                $activityAction->new_state_id = $item->activity->state_id;
                $activityAction->author_id = 1;
                $activityAction->created_at = '2018-10-12 15:35:01';
                $activityAction->save();

                // NUEVO ITEM
                $new_item = CorrespondenceItem::createFromActivityAction($client, $item->activity_action, $paths, $item->entity_type, 1131);

                $item->save();

                $last_id = $item->id;
            }

            DB::commit();
        } catch (Exception $e) {
            DB::rollback();
            Log::error($e);
            return 'ERROR: ' . $e->getMessage();
        }

        if ($last_id != $id) {
            return view('extras.force_resend', ['id' => $last_id]);
        }

        return 'FINISHED';
    });

    Route::get('/', function () {
        if (Auth::user()) {
            $user = App\User::find(Auth::id());
            if ($user->area_id == \App\Area::INTERMEDIARY || $user->area_id == \App\Area::INTERMEDIARY_EXECUTIVE || $user->area_id == \App\Area::COMMERCIAL_EXECUTIVE) {
                return redirect('/intermediario');
            }
            if ($user->area_id == \App\Area::TOMADOR) {
                return redirect('/tomador/poliza/' . (Auth::user()->affiliate_id ?? '1') . '/datos');
            }
            if ($user->area_id == \App\Area::TOMADOR_AUTORIZADO) {

                $listAutorizado = App\UserAuthorizedTomador::where('user_id', auth()->user()->id)->get();

                if ($listAutorizado->count() > 1) {
                    return redirect('/admin/autorizados/list');
                } else {
                    $user_tomador = App\User::find(Auth::user()->tomador_id);
                    return redirect('/tomador/poliza/' . ($user_tomador->affiliate_id ?? '1') . '/datos');
                }

            }
            if ($user->area_id == \App\Area::AUTORIZADO) {
                return redirect('/tomador/poliza/' . (Auth::user()->affiliate_id ?? '1') . '/datos');
            }
            if ($user->area_id == \App\Area::ASEGURADO) {
                return redirect('/tablero/afiliado/' . (Auth::user()->affiliate_id ?? '1'));
            }
            if ($user->area_id == \App\Area::ASESOR_CALL_CENTER) {
                return redirect('/servicio/gis_sort/tomador');
            }
            if (Auth::user()->isProvider()) {
                return redirect('/proveedor/' . (Auth::user()->affiliate_id ?? '1') . '/asignar_servicio');
            }
            if ($user) {
                $user->last_login = date('Y-m-d H:i:s');
                $user->save();
            }
            return redirect('/nuevo');
        } else {
            return redirect('/login', 302, [], true);
        }
    });

    Route::get('/home', function () {
        return redirect('/nuevo');
    });
    Route::get('/login/test', 'Auth\LoginController@test');

    Route::post('/user/password', 'HomeController@changePassword');

    Route::get('file/m/{path}/{filename}', function ($cpath, $path, $filename) {
        if (Storage::exists("m/{$path}/{$filename}")) {
            return response()->file(storage_path("app/m/{$path}/{$filename}"));
        } else if (Storage::disk('s3')->exists("m/{$path}/{$filename}")) {
            return redirect(Storage::disk('s3')->url("m/{$path}/{$filename}"));
        } else {
            return 'El archivo no ha sido encontrado, para mas información contacte al administrador.';
        }
    });

    Route::get('file/m/{path}/{subpath}/{filename}', function ($cpath, $path, $subpath, $filename) {
        if (Storage::exists("m/{$path}/{$subpath}/{$filename}")) {
            return response()->file(storage_path("app/m/{$path}/{$subpath}/{$filename}"));
        } else if (Storage::disk('s3')->exists("m/{$path}/{$subpath}/{$filename}")) {
            return redirect(Storage::disk('s3')->url("m/{$path}/{$subpath}/{$filename}"));
        } else {
            return 'El archivo no ha sido encontrado, para mas información contacte al administrador.';
        }
    });

    Route::get('file/{path}/{filename}', function ($cpath, $path, $filename) {
        if (Storage::exists("{$path}/{$filename}")) {
            return response()->file(storage_path("app/{$path}/{$filename}"));
        } else if (Storage::disk('s3')->exists("{$path}/{$filename}")) {
            return redirect(Storage::disk('s3')->url("{$path}/{$filename}"));
        } else {
            return 'El archivo no ha sido encontrado, para mas información contacte al administrador.';
        }
    });

    Route::get('file/{path}/{subpath}/{filename}', function ($cpath, $path, $subpath, $filename) {
        if (Storage::exists("{$path}/{$subpath}/{$filename}")) {
            return response()->file(storage_path("app/{$path}/{$subpath}/{$filename}"));
        } else if (Storage::disk('s3')->exists("{$path}/{$subpath}/{$filename}")) {
            return redirect(Storage::disk('s3')->url("{$path}/{$subpath}/{$filename}"));
        } else {

            if (get_headers("http://ml.occrisk.co/document/{$path}/{$subpath}/{$filename}")[0] == 'HTTP/1.1 200 OK') {
                return redirect("http://ml.occrisk.co/document/{$path}/{$subpath}/{$filename}");
            } else {
                return 'El archivo no ha sido encontrado, para mas información contacte al administrador.';
            }
        }
    });

    Route::get('file/{path}/{subpath}/{folder}/{subfolder}/{filename}', function ($cpath, $path, $subpath, $folder, $subfolder, $filename) {
        if (Storage::exists("{$path}/{$subpath}/{$folder}/{$subfolder}/{$filename}")) {
            return response()->file(storage_path("app/{$path}/{$subpath}/{$folder}/{$subfolder}/{$filename}"));
        } else if (Storage::disk('s3')->exists("{$path}/{$subpath}/{$folder}/{$subfolder}/{$filename}")) {
            return redirect(Storage::disk('s3')->url("{$path}/{$subpath}/{$folder}/{$subfolder}/{$filename}"));
        } else {

            if (get_headers("http://ml.occrisk.co/document/{$path}/{$subpath}/{$folder}/{$subfolder}/{$filename}")[0] == 'HTTP/1.1 200 OK') {
                return redirect("http://ml.occrisk.co/document/{$path}/{$subpath}/{$folder}/{$subfolder}/{$filename}");
            } else {
                return 'El archivo no ha sido encontrado, para mas información contacte al administrador.';
            }
        }
    });
    // INFORMATION LOGS
    Route::get('/logs/get_historial_affiliate/{id_affiliate}', 'ServiceController@getHistoricalAffiliate');

    // CALLS
    Route::get('callReport/{date}', 'CallController@report');
    Route::get('tutoriales', 'HomeController@tutoriales');

    // SIMULACION DE SERVICIOS
    Route::get('/simulacion_servicio', 'SimulateServiceController@view');
    Route::post('/simulacion_servicio/update', 'SimulateServiceController@view');
    Route::get('/simulacion_servicio/download', 'SimulateServiceController@download');

    // HORARIOS DE PSICOLOGOS
    Route::get('/schedule', 'ScheduleController@view');
    Route::get('/schedule/get_schedules_by_id/{id}/{date}', 'ScheduleController@getSchedulesById');
    Route::post('/schedule/get_availability_by_id_and_date', 'ScheduleController@getAvailabilityByIdAndDate');
    Route::post('/schedule/get_availability_by_id_and_end_date', 'ScheduleController@getAvailabilityByIdAndEndDate');
    Route::post('/schedule/saveSchedule_by_user', 'ScheduleController@saveScheduleByUser');
    Route::post('/schedule/deleteSchedule_by_user', 'ScheduleController@deleteScheduleByUser');
    Route::get('/schedule/get_doctors_by_filters/{department}/{valoration_type}', 'ScheduleController@getDoctorsByFilters');
    Route::get('/schedule/get_info_affiliate_by_activity_id/{activity_id}', 'ScheduleController@getInfoAffiliateByActivity');

    Route::get('/schedule/edit/{userId?}', 'ScheduleController@viewEdit');
    Route::post('/schedule/saveWeek', 'ScheduleController@saveWeek');

    Route::get('/schedule/report', 'ScheduleController@viewReport');

    Route::get('/admin/reportes/renips_busy_schedule_canceled', 'ExcelController@downloadRenipsBusyScheduleCanceled');

    // REI
    Route::get('/invalidity_state/phase_zero', 'Services\InvalidityStateController@FormPhaseZero');
    Route::post('/invalidity_state/phase_zero/assign_activities', 'Services\InvalidityStateController@phaseZeroAssignActivities');

    Route::get('/invalidity_state/phase_one', 'Services\InvalidityStateController@FormPhaseOne');
    Route::get('/invalidity_state/phase_one/reject_lote/{id_lote}', 'Services\InvalidityStateController@FormPhaseOneRejectLote');
    Route::get('/invalidity_state/phase_one/return_lote/{id_lote}', 'Services\InvalidityStateController@FormPhaseOneReturnLote');
    Route::get('/invalidity_state/phase_one/approve_lote/{id_lote}', 'Services\InvalidityStateController@FormPhaseOneApproveLote');
    Route::post('/invalidity_state/phase_one/assign_activities', 'Services\InvalidityStateController@phaseOneAssignActivities');

    Route::get('/invalidity_state/phase_two', 'Services\InvalidityStateController@FormPhaseTwo');
    Route::get('/invalidity_state/phase_two/reject_lote/{id_lote}', 'Services\InvalidityStateController@FormPhaseTwoRejectLote');
    Route::get('/invalidity_state/phase_two/return_lote/{id_lote}', 'Services\InvalidityStateController@FormPhaseTwoReturnLote');
    Route::get('/invalidity_state/phase_two/approve_lote/{id_lote}', 'Services\InvalidityStateController@FormPhaseTwoApproveLote');
    Route::post('/invalidity_state/phase_two/assign_activities', 'Services\InvalidityStateController@phaseTwoAssignActivities');

    Route::get('/invalidity_state/phase_three', 'Services\InvalidityStateController@FormPhaseThree');
    Route::get('/invalidity_state/phase_four', 'Services\InvalidityStateController@FormPhaseFour');


    // AFILIADOS
    Route::get('/repositorio_documental', 'HomeController@onConstruction');
    Route::get('/casos', 'HomeController@index');
    Route::get('/nuevo/{affiliate?}', 'HomeController@index');
    Route::post('/nuevo/{affiliate?}', 'HomeController@index');

    Route::get('/affiliate/save', function ($cpath) {
        return redirect('/nuevo');
    });
    Route::post('/affiliate/save', 'HomeController@saveAffiliate');
    Route::get('/affiliate/validate_fraud/{doc_number}', 'HomeController@validateAffiliateFraud');
    Route::post('/affiliate_employer/save', 'HomeController@saveAffiliateEmployer');
    Route::post('/affiliate/savedoc', 'HomeController@saveAffiliateDoc');

    Route::get('/afiliado/{id}', 'HomeController@viewAffiliate');
    Route::get('/afiliado/{id}/editar', 'HomeController@editAffiliate');

    // CASOS
    Route::get('/service/create', function ($cpath) {
        return redirect('/nuevo');
    });
    Route::post('/service/create', 'ServiceController@create');

    Route::get('/servicio/{id}', 'ServiceController@view');
    Route::post('/servicio/{id}', 'ActionController@action');

    Route::get('/regenerate/{id}', function ($cpath) {
        return redirect('/nuevo');
    });
    Route::post('/regenerate/{id}', 'ActionController@regenerate');
    Route::get('/masiveRegenerate/{id}', 'ActionController@masiveRegenerate');
    Route::get('/regenerar_por_actividad/{activity_id}', 'ActionController@regenerateForActivityId');
    Route::get('/regenerar_521_por_actividad/{activity_id}', 'ActionController@regenerate521ForActivityId');
    Route::post('/service/action/{id}/delete', 'ActionController@deleteAction');

    Route::get('/fill_radication_date/{id}', 'ActionController@fillingRadicationDate');

    Route::get('/service/{id}/upload', function ($cpath, $id) {
        return redirect('/servicio/' . $id);
    });
    Route::post('/service/{id}/upload', 'ServiceController@upload');

    Route::get('/service/{id}/archive', 'ServiceController@archive');
    Route::get('/service/{id}/unarchive', 'ServiceController@unarchive');
    Route::get('/service/{id}/m2o/{doc}', 'ServiceController@moveToOthers');
    Route::get('/servicio/{id}/docs/{doc}', 'ActionController@docPreview');

    // SICAT
    Route::get('/sicat', 'SicatController@listSicat');
    // VALORATION
    Route::get('/servicio/{id}/template', 'Services\TemplateController@form');

    Route::get('/servicio/{id}/template/save', function ($cpath, $id) {
        return redirect('/servicio/' . $id . '/template', 302, [], true);
    });
    Route::post('/servicio/{id}/template/save', 'Services\TemplateController@save');

    Route::get('/servicio/{id}/template/pdf', function ($cpath, $id) {
        return "Vista previa EXPIRADA, cierre esta ventana y vuelva a generarla.";
    });
    Route::post('/servicio/{id}/template/pdf', 'Services\TemplateController@pdf');
    // INVALIDITY STATE P1
    Route::get('/servicio/{id}/invalidity_state_pone', 'Services\InvalidityStateP1Controller@form');

    Route::get('/servicio/{id}/invalidity_state_pone/save', function ($cpath, $id) {
        return redirect('/servicio/' . $id . '/invalidity_state_pfour', 302, [], true);
    });
    Route::post('/servicio/{id}/invalidity_state_pone/save', 'Services\InvalidityStateP1Controller@save');

    Route::get('/servicio/{id}/invalidity_state_pone/pdf', function ($cpath, $id) {
        return "Vista previa EXPIRADA, cierre esta ventana y vuelva a generarla.";
    });
    Route::post('/servicio/{id}/invalidity_state_pone/pdf', 'Services\InvalidityStateP1Controller@pdf');
    // INVALIDITY STATE P2
    Route::get('/servicio/{id}/invalidity_state_ptwo', 'Services\InvalidityStateP2Controller@form');

    Route::get('/servicio/{id}/invalidity_state_ptwo/save', function ($cpath, $id) {
        return redirect('/servicio/' . $id . '/invalidity_state_pfour', 302, [], true);
    });
    Route::post('/servicio/{id}/invalidity_state_ptwo/save', 'Services\InvalidityStateP2Controller@save');

    Route::get('/servicio/{id}/invalidity_state_ptwo/pdf', function ($cpath, $id) {
        return "Vista previa EXPIRADA, cierre esta ventana y vuelva a generarla.";
    });
    Route::post('/servicio/{id}/invalidity_state_ptwo/pdf', 'Services\InvalidityStateP2Controller@pdf');
    // INVALIDITY STATE P3
    Route::get('/servicio/{id}/invalidity_state_pthree', 'Services\InvalidityStateP3Controller@form');

    Route::get('/servicio/{id}/invalidity_state_pthree/save', function ($cpath, $id) {
        return redirect('/servicio/' . $id . '/invalidity_state_pfour', 302, [], true);
    });
    Route::post('/servicio/{id}/invalidity_state_pthree/save', 'Services\InvalidityStateP3Controller@save');

    Route::get('/servicio/{id}/invalidity_state_pthree/pdf', function ($cpath, $id) {
        return "Vista previa EXPIRADA, cierre esta ventana y vuelva a generarla.";
    });
    Route::post('/servicio/{id}/invalidity_state_pthree/pdf', 'Services\InvalidityStateP3Controller@pdf');
    // INVALIDITY STATE P4
    Route::get('/servicio/{id}/invalidity_state_pfour', 'Services\InvalidityStateP4Controller@dictum');

    Route::get('/servicio/{id}/invalidity_state_pfour/save', function ($cpath, $id) {
        return redirect('/servicio/' . $id . '/invalidity_state_pfour', 302, [], true);
    });
    Route::post('/servicio/{id}/invalidity_state_pfour/save', 'Services\InvalidityStateP4Controller@save');

    Route::get('/servicio/{id}/invalidity_state_pfour/pdf', function ($cpath, $id) {
        return "Vista previa EXPIRADA, cierre esta ventana y vuelva a generarla.";
    });
    Route::post('/servicio/{id}/invalidity_state_pfour/pdf', 'Services\InvalidityStateP4Controller@pdf');
    Route::post('/servicio/{id}/invalidity_state_pfour/pdf_valoration', 'Services\InvalidityStateP4Controller@pdfValoration');

    // MEETING SERVICE
    Route::get('/servicio/{id}/tramites', 'Services\MeetingController@dictum');

    Route::get('/servicio/{id}/tramites/save', function ($cpath, $id) {
        return redirect('/servicio/' . $id . '/riesgo', 302, [], true);
    });
    Route::post('/servicio/{id}/tramites/save', 'Services\MeetingController@save');

    Route::get('/servicio/{id}/tramites/pdf', function ($cpath, $id) {
        return "Vista previa EXPIRADA, cierre esta ventana y vuelva a generarla.";
    });
    Route::post('/servicio/{id}/tramites/pdf', 'Services\MeetingController@pdf');
    // PCL
    Route::get('/servicio/{id}/pcl', 'Services\PCLController@dictum');

    Route::get('/servicio/{id}/pcl/save', function ($cpath, $id) {
        return redirect('/servicio/' . $id . '/pcl', 302, [], true);
    });
    Route::post('/servicio/{id}/pcl/save', 'Services\PCLController@save');

    Route::get('/servicio/{id}/pcl/pdf', function ($cpath, $id) {
        return "Vista previa EXPIRADA, cierre esta ventana y vuelva a generarla.";
    });
    Route::post('/servicio/{id}/pcl/pdf', 'Services\PCLController@pdf');

    Route::get('/servicio/{id}/pcl/pdf', function ($cpath, $id) {
        return "Vista previa EXPIRADA, cierre esta ventana y vuelva a generarla.";
    });
    Route::post('/servicio/{id}/pcl/pdf', 'Services\PCLController@pdf');
    Route::post('/servicio/{id}/pcl/pdf_valoration', 'Services\PCLController@pdfValoration');
    Route::post('/servicio/{id}/pcl/pdf_rejection', 'Services\PCLController@pdfRejection');

    // DICTUM RECEPTION
    Route::get('/servicio/{id}/dictum_reception', 'Services\DictumReceptionController@form');

    Route::get('/servicio/{id}/dictum_reception/save', function ($cpath, $id) {
        return redirect('/servicio/' . $id . '/riesgo', 302, [], true);
    });
    Route::post('/servicio/{id}/dictum_reception/save', 'Services\DictumReceptionController@save');

    Route::get('/servicio/{id}/dictum_reception/pdf', function ($cpath, $id) {
        return "Vista previa EXPIRADA, cierre esta ventana y vuelva a generarla.";
    });
    Route::post('/servicio/{id}/dictum_reception/pdf', 'Services\DictumReceptionController@pdf');
    //  PAYMENT BASES
    Route::get('/servicio/{id}/payment_bases', 'Services\PaymentBasesController@form');
    Route::get('/migrate_it_to_payment_bases/{id}', 'Services\PaymentBasesController@migrateItToPaymentBases');
    Route::get('/migrate_it_to_payment_bases_valor/{id}', 'Services\PaymentBasesController@migrateItToPaymentBasesPaidValue');
    Route::get('/migrate_it_to_payment_bases_fracs/{id}', 'Services\PaymentBasesController@migrateItToPaymentBasesFractions');
    Route::get('/servicio/{id}/payment_bases/save', function ($cpath, $id) {
        return redirect('/servicio/' . $id . '/payment_bases');
    });
    Route::post('/servicio/{id}/payment_bases/save', 'Services\PaymentBasesController@save');

    //  IT LIQUIDATED
    Route::get('/servicio/{id}/it_liquidated', 'Services\ItLiquidatedController@form');
    //  IT HISTORICAL
    Route::get('/servicio/{id}/it_historical', 'Services\ItHistoricalController@form');
    // DETERMINATION IT
    Route::get('/servicio/{id}/determination_it', 'Services\DeterminationItController@form');

    Route::get('/servicio/{id}/determination_it/save', function ($cpath, $id) {
        return redirect('/servicio/' . $id . '/determination_it');
    });
    Route::get('/servicio/{id}/determination_it/save_pri', function ($cpath, $id) {
        return redirect('/servicio/' . $id . '/determination_it');
    });
    Route::get('/servicio/{id}/determination_it/save_pri_external', function ($cpath, $id) {
        return redirect('/servicio_pri/' . $id);
    });
    Route::post('/servicio/{id}/determination_it/save', 'Services\DeterminationItController@save');
    Route::post('/servicio/{id}/determination_it/save_pri', 'Services\DeterminationItController@savePri');
    Route::post('/servicio/{id}/determination_it/save_pri_external', 'Services\DeterminationItController@savePriExternal');
    Route::get('/validate_audits_authorization/{docNumber}/{codeIps}/{dateExpedition}', 'Services\DeterminationItController@validateAuthorization');

    Route::get('/servicio/{id}/determination_it/pdf', function ($cpath, $id) {
        return "Vista previa EXPIRADA, cierre esta ventana y vuelva a generarla.";
    });
    Route::post('/servicio/{id}/determination_it/pdf', 'Services\DeterminationItController@pdf');

    Route::get('/servicio_pri/{id}', 'Services\DeterminationItController@viewPri');

    Route::get('/cert-audits/{affiliate_id}/determination_it/pdf', 'Services\DeterminationItController@loadCertificateAudits');
    // PQR SERVICE
    Route::post('/servicio/{id}/pqr/pdf', 'Services\PQRController@pdf');
    Route::get('/servicio/{id}/pqr', 'Services\PQRController@form');

    Route::get('/servicio/{id}/pqr/save', function ($cpath, $id) {
        return redirect('/servicio/' . $id . '/pqr');
    });
    Route::post('/servicio/{id}/pqr/save', 'Services\PQRController@save');

    Route::get('/servicio/{id}/pqr/pdf', function ($cpath, $id) {
        return "Vista previa EXPIRADA, cierre esta ventana y vuelva a generarla.";
    });
    Route::get('/servicio/{id}/pqr/word', 'Services\PQRController@docWord');
    // GESTIÓN TUTELAS
    Route::post('/servicio/{id}/tutelage/pdf', 'Services\GestionTutelasController@pdf');
    Route::get('/servicio/{id}/tutelage', 'Services\GestionTutelasController@form');

    Route::get('/servicio/{id}/tutelage/save', function ($cpath, $id) {
        return redirect('/servicio/' . $id . '/gestion_tutelas');
    });
    Route::post('/servicio/{id}/tutelage/save', 'Services\GestionTutelasController@save');

    Route::get('/servicio/{id}/tutelage/pdf', function ($cpath, $id) {
        return "Vista previa EXPIRADA, cierre esta ventana y vuelva a generarla.";
    });
    Route::get('/servicio/{id}/tutelage/word', 'Services\GestionTutelasController@docWord');
    // MANIFESTACIÓN DE INCONFORMIDADES
    Route::post('/servicio/{id}/expression_disagreement/pdf', 'Services\ExpressionDisagreementController@pdf');
    Route::get('/servicio/{id}/expression_disagreement', 'Services\ExpressionDisagreementController@form');

    Route::get('/servicio/{id}/expression_disagreement/save', function ($cpath, $id) {
        return redirect('/servicio/' . $id . '/gestion_tutelas');
    });
    Route::post('/servicio/{id}/expression_disagreement/save', 'Services\ExpressionDisagreementController@save');

    Route::get('/servicio/{id}/expression_disagreement/pdf', function ($cpath, $id) {
        return "Vista previa EXPIRADA, cierre esta ventana y vuelva a generarla.";
    });
    Route::get('/servicio/{id}/expression_disagreement/word', 'Services\ExpressionDisagreementController@docWord');

    // DICTUM PCL NOTIFICACION
    Route::post('/servicio/{id}/dictum_pcl_notification/pdf', 'Services\DictumPclNotificationController@pdf');
    Route::get('/servicio/{id}/dictum_pcl_notification', 'Services\DictumPclNotificationController@form');

    Route::get('/servicio/{id}/dictum_pcl_notification/save', function ($cpath, $id) {
        return redirect('/servicio/' . $id . '/gestion_tutelas');
    });
    Route::post('/servicio/{id}/dictum_pcl_notification/save', 'Services\DictumPclNotificationController@save');

    Route::get('/servicio/{id}/dictum_pcl_notification/pdf', function ($cpath, $id) {
        return "Vista previa EXPIRADA, cierre esta ventana y vuelva a generarla.";
    });
    Route::get('/servicio/{id}/dictum_pcl_notification/word', 'Services\DictumPclNotificationController@docWord');
    // QUOTATION - COTIZACION SORT
    Route::post('/servicio/{id}/quotation/pdf', 'Services\QuotationController@pdf');
    Route::get('/servicio/{id}/quotation', 'Services\QuotationController@form');
    Route::get('/servicio/{id}/quotation/generate', 'Services\QuotationController@generate');
    Route::get('/servicio/{id}/quotation/generatePdf', 'Services\QuotationController@generatePdf')->name('cotizacion.download_pdf');
    Route::get('/servicio/{id}/quotation/save', function ($cpath, $id) {
        return redirect('/servicio/' . $id . '/quotation');
    });

    //POLIZA SORT
    Route::post('/servicio/{id}/policy_sort/pdf', 'Services\PolicySortController@pdf');
    Route::get('/servicio/{id}/policy_sort/view', 'Services\PolicySortController@form');
    Route::get('/servicio/{id}/policy_sort', 'Services\PolicySortController@form');
    Route::post('/servicio/{id}/policy_sort/uploadPhysicalSignatures', 'Services\PolicySortController@uploadPhysicalSignatures');
    Route::post('/servicio/{id}/policy_sort/uploadDigitalSignatures', 'Services\PolicySortController@uploadDigitalSignatures');
    Route::get('/servicio/{id}/policy_sort/DescargarFormatoFirma', 'Services\PolicySortController@downloadSignature');
    Route::post('/servicio/{id}/policy_sort/downloadSignalReport', 'Services\PolicySortController@downloadSignalReport');
    Route::post('/servicio/{id}/policy_sort/downloadSignalReportInclusion', 'Services\PolicySortController@downloadSignalReportInclusion');
    Route::post('/tomador/benefit_colective/validate', 'Services\PolicySortController@benefitColectiveIndividual');
    Route::post('/tomador/benefit_colective/validate/include_worker', 'Services\PolicySortController@benefitColectiveIncludeWorker');
    //generar condiciones particulares manualmetne
    Route::get('/servicio/{id}/condiones_particulares', 'Services\PolicySortController@condiones_particulares');
    Route::get('/servicio/{id}/comunicado', 'Services\PolicySortController@comunicado');


    Route::get('/generate-account-entry/{id}', 'Services\PolicySortController@generateAccountingEntry');

    Route::get('/generate-account-entry/reportAccountCaseOne003/{id}', 'Services\AccountingEntryController@reportAccountCaseOne003');
    Route::get('/generate-account-entry/reportAccountCaseTwo003/{id}', 'Services\AccountingEntryController@reportAccountCaseTwo003');

    //Asiento contable 087 Disminución de pagos de incapacidad
    Route::get('/generate-account-entry/reportAccountCase087/{id}/{id2}', 'Services\AccountingEntryController@reportAccountCase087');

    //Asiento contable 108 Disminución Liberacion de reserva
    Route::get('/generate-account-entry-108/reportAccountCase108/{date?}', 'Services\AccountingEntryController@reportAccountCase108');

    //Asiento contable 151 reasuramiento
    Route::get('/generate-account-entry-151/reportAccountCase151/{date?}', 'Services\AccountingEntryController@reportAccountCase151');

    //Ruta para saber el porcentaje de avance de la planilla cargada.
    Route::get('/massively/report/automatic/{id}', 'Services\PolicySortController@loadedEmployes');

    //envio correo unico manual
    Route::get('/uniqueContactCode/{id}', 'Services\PolicySortController@uniqueContactCode');

    Route::get('/servicio/{id}/policy_sort/actionSuspendPolicyTemporality', 'Services\PolicySortController@actionSuspendPolicyTemporality');

    Route::get('/get-corredurias', 'Services\PolicySortController@getCorreduria');
    Route::get('/get-corredurias-acsel/{id}', 'Integrations\WebserviceAcselController@getDataBrokerage');
    Route::post('/get-user-corredurias', 'Services\PolicySortController@getUserCorreduria');

    Route::post('/servicio/{id}/policy_sort/save', 'Services\PolicySortController@save');
    Route::post('/validate/search/affiliate/report', 'Services\PolicySortController@searchAffiliateReport');

    Route::get('/servicio/policy_sort/descargar_planilla_SORT', 'Services\PolicySortController@downloadPlanilla');
    Route::get('/servicio/policy_sort/descargar_Explicacion_planilla', 'Services\PolicySortController@downloadExplanation');
    // COBRO POLIZA SORT
    Route::post('/servicio/{id}/policy_sort_collection/pdf', 'Services\PolicySortCollectionController@pdf');
    Route::get('/servicio/{id}/policy_sort_collection', 'Services\PolicySortCollectionController@form');
    Route::get('/servicio/{id}/policy_sort_collection/save', function ($cpath, $id) {
        return redirect('/servicio/' . $id . '/policy_sort_collection');
    });
    Route::get('/servicio/{id}/policy_sort_collection/pago_poliza', 'Services\PolicySortCollectionController@paymentPolicy');

    Route::get('/policy_sort_collection/delete_credit_notes/{id}', 'Services\PolicySortCollectionController@deleteCreditNotes');

    Route::get('/servicio/policy_sort_collection/{id}/callback', 'Services\PolicySortCollectionController@handleCallback');
    Route::post('/servicio/policy_sort_collection/payment_qa', 'Services\PolicySortCollectionController@paymenQa');

    Route::post('/servicio/{id}/policy_sort_collection/save', 'Services\PolicySortCollectionController@save');
    Route::post('/servicio/{id}/policy_sort_collection/save-has-card-payment', 'Services\PolicySortCollectionController@saveHasCardPayment');

    //Rutas para las acciones de cobro poliza sort
    Route::post('/servicio/{id}/report-electronic-invoice', 'Services\PolicySortCollectionController@reportElectronicInvoice');
    Route::post('/servicio/{id}/insurance-increase', 'Services\PolicySortCollectionController@reportInsuranceIncreasePeriod');
    Route::post('/servicio/{id}/report-monthly-payment', 'Services\PolicySortCollectionController@reportMonthlyPaymentReceipt');
    Route::post('/servicio/{id}/report-biannual-payment', 'Services\PolicySortCollectionController@reportBiannualPaymentReceipt');
    Route::post('/servicio/{id}/report-quarterly-payment', 'Services\PolicySortCollectionController@reportquarterlyPaymentReceipt');
    Route::post('/servicio/{id}/report-rehabilitation-payment', 'Services\PolicySortCollectionController@reportRehabilitationPaymentReceipt');
    Route::post('/servicio/{id}/report_liquidation', 'Services\PolicySortCollectionController@reportLiquidationPaymentReceipt');
    Route::post('/servicio/{id}/update-payment', 'Services\PolicySortCollectionController@updatePayment');
    Route::post('/servicio/{id}/register-payment-tb', 'Services\PolicySortCollectionController@registerPaymentTb');
    Route::post('/servicio/{id}/register-payment-tb-additional', 'Services\PolicySortCollectionController@registerPaymentTbAdditional');
    Route::post('/servicio/{id}/register-payment-tc', 'Services\PolicySortCollectionController@registerPaymentCc');
    Route::post('/servicio/{id}/register-payment-pt', 'Services\PolicySortCollectionController@registerPaymentPt');
    Route::post('/servicio/{id}/approve-payment', 'Services\PolicySortCollectionController@approvePaymentTb');
    Route::post('/servicio/{id}/reject-payment', 'Services\PolicySortCollectionController@rejectPaymentTb');

    //COTIZACION POLIZA SORT
    Route::post('/servicio/{id}/quotation/save', 'Services\PolicySortController@save');
    Route::get('/servicio/{id}/quotation/pdf', function ($cpath, $id) {
        return "Vista previa EXPIRADA, cierre esta ventana y vuelva a generarla.";
    });

    //VARIACIONES SORT
    Route::get('/servicio/{id}/variations_sort', 'Services\VariationsSortController@form');
    Route::get('/servicio/VariationsSort/all', 'Services\VariationsSortController@indexTabla');
    Route::get('/tomador/poliza/{id}/variaciones/{npoliza}', 'Services\VariationsSortController@tableTomador');


    //REPORTE PLANILLA AFILIADO
    Route::get('/servicio/{id}/affiliate_workforce_report', 'Services\AffiliateWorkforceReportController@form');

    Route::post('/servicio/{id}/report_affiliates', 'Services\PolicySortController@report_affiliates');

    //Rutas correos
    Route::get('/variaciones/resend_email/{id}', 'Services\VariationsSortController@resendEmail');
    //Rutas acciones
    Route::post('/variaciones/action_status', 'Services\VariationsSortController@actionStatus');
    Route::post('/variaciones/action_status_report', 'Services\VariationsSortController@actionStatusReport');
    Route::get('/servicio/VariationsSort/PdfRehabilitacion', 'Services\VariationsSortController@viewPdfRehabilitacion');
    //REPORTE PLANILLAS TOMADOR
    Route::get('/servicio/{id}/report_taken_form', 'Services\ReportTakenFormController@form');
    Route::post('/servicio/{id}/report_taken_form/save', 'Services\ReportTakenFormFormController@save');
    Route::post('/servicio/{id}/report_taken_form/pdf', 'Services\ReportTakenFormController@pdf');


    Route::get('/servicio/{id}/report_taken_form/report', 'Services\ReportTakenFormController@reportAffiliate');


    //CONSTANCIA POLIZA SORT
    //Route::get('/servicio/{id}/constancy_sort', 'Services\ConstancySortController@form');
    //Rutas correos
    //Route::get('/variaciones/resend_email/{id}', 'Services\ConstancySortController@resendEmail');

    //CONSTANCIA POLIZA SORT
    //    Route::get('/servicio/{id}/constancy_sort/email_pendiente', 'Services\ConstancySortController@email_pendiente');
    //    Route::get('/servicio/{id}/constancy_sort/email', 'Services\ConstancySortController@email');

    Route::post('/servicio/{id}/constancy_sort/{type}', 'Services\ConstancySortController@handleEmail');
    Route::post('/servicio/{id}/constancy_sort/pdf', 'Services\ConstancySortController@pdf');
    Route::get('/servicio/{id}/constancy_sort', 'Services\ConstancySortController@form');
    Route::post('/servicio/{id}/constancy_sort/download_document/{type}', 'Services\ConstancySortController@downloadDocument');
    Route::get('/planillasPendientes/{id}', 'Services\ConstancySortController@planillasPendientes');

    //Aciones Constancia SORT
    Route::get('/servicio/{id}/constancy_sort/account_statement', 'Services\ConstancySortController@generatAccountStatement');

    Route::get('/servicio/{id}/constancy_sort/save', function ($cpath, $id) {
        return redirect('/servicio/' . $id . '/constancy_sort');
    });
    Route::post('/servicio/{id}/constancy_sort/save', 'Services\ConstancySortController@save');

    Route::get('/servicio/{id}/constancy_sort/pdf', function ($cpath, $id) {
        return "Vista previa EXPIRADA, cierre esta ventana y vuelva a generarla.";
    });
    Route::get('/servicio/{id}/constancy_sort/word', 'Services\ConstancySortController@docWord');


    //LIQUIDACION
    Route::get('/servicio/{id}/liquidation', 'Services\LiquidationPolicyController@form');
    Route::get('/servicio/{id}/submitSettlementPayment', 'Services\LiquidationPolicyController@submitSettlementPayment');
    //submitSettlementPayment

    //RENOVACION
    Route::get('/servicio/{id}/renewal', 'Services\RenewalSortController@form');
    Route::get('/reportUnpaidRenewal/{id}', 'Services\RenewalSortController@reportUnpaidRenewal');

    Route::get('/reportRenewalPayment/{id}', 'Services\RenewalSortController@reportRenewalPayment');
    //

    //list
    Route::get('/list-docs', 'MassiveController@viewListDocs');
    Route::get('/list-actions', 'MassiveController@viewListActions');
    Route::get('/list-afps', 'MassiveController@viewListAFPs');
    Route::get('/list-arls', 'MassiveController@viewListARLs');
    Route::get('/list-epss', 'MassiveController@viewListEPSs');
    Route::get('/list-networks', 'MassiveController@viewNetworks');
    Route::get('/search_payment_control_it', 'ExcelController@searchPaymentControlIt');

    // VALIDATE EXCEL
    Route::get('/validate-excel', 'ValidateExcelController@validateExcelView');
    Route::get('/validate-excel/fetch_data', 'ValidateExcelController@fetch_data');
    Route::post('/validate-excel/save', 'ValidateExcelController@save');
    Route::post('/validate-excel/update_data', 'ValidateExcelController@update_data');

    // MASSIVE CHARGE PCL
    Route::get('/carguemasivopcl', 'MassiveController@massivePclView');
    Route::prefix('cargues_pcl')->group(function () {
        Route::post('pcl_cases', 'MassiveController@savePclCases');
        Route::post('pcl_cases_dictum', 'MassiveController@savePclCasesDictums');
        Route::post('pcl_cases_dictum_info', 'MassiveController@savePclCasesDictumInfos');
        Route::post('pcl_cases_dictum_interconsult', 'MassiveController@savePclCasesDictumInterInfos');
        Route::post('pcl_cases_dictum_dx', 'MassiveController@savePclCasesDictumDxInfos');
        Route::post('pcl_cases_dictum_def', 'MassiveController@savePclCasesDictumDefInfos');
        Route::post('pcl_cases_action_history', 'MassiveController@savePclCasesActionHistory');
        Route::post('pcl_cases_docs', 'MassiveController@savePclCasesDocuments');
        Route::post('pcl_cases_update_state', 'MassiveController@savePclCasesUpdateState');
        Route::post('pcl_cases_table_datas', 'MassiveController@savePclCasesDictumTableDatas');
        Route::post('pcl_cases_table_disability', 'MassiveController@savePclCasesDictumTableDisabilityDatas');
        Route::post('pcl_cases_table_minus', 'MassiveController@savePclCasesDictumMinusTableDatas');
        Route::post('pcl_cases_backup', 'MassiveController@savePclCasesBackup');
        Route::post('pcl_cases_historical_closed', 'MassiveController@savePclCasesHistoricalClosed');
    });
    // MASSIVE CHARGE IT
    Route::get('/carguemasivoit', 'MassiveController@massiveItView');
    Route::prefix('cargues_it')->group(function () {
        Route::post('it_cases', 'MassiveController@saveItCases');
        Route::post('it_cases_inability', 'MassiveController@saveItInabilitiesCases');
        Route::post('it_cases_action_history', 'MassiveController@saveItCasesActionHistory');
        Route::post('it_cases_update_state', 'MassiveController@updateItState');
        Route::post('it_cases_documents', 'MassiveController@saveItCasesDocuments');
        Route::post('it_cases_update_date', 'MassiveController@updateItInabilitiesCasesDates');
        Route::post('it_cases_follows', 'MassiveController@saveItCasesFollow');
    });

    // MASSIVE CHARGE MANIFESTACIONES DE INCONFORMIDAD
    Route::get('/carguemasivomid', 'MassiveController@massiveMdIView');
    Route::prefix('cargues_mdi')->group(function () {
        Route::post('mdi_cases', 'MassiveController@saveExpressionDisagreementCases');
    });
    // MASSIVE  CHARGE
    Route::get('/carguemasivo', 'MassiveController@massiveView');
    Route::prefix('cargue')->group(function () {
        Route::post('affiliate/client', 'MassiveController@saveMassiveClientAffiliates');
        Route::post('services/client', 'MassiveController@saveMassiveClientServices');
        Route::post('short_pcls', 'MassiveController@saveMassiveShortPCLs');
        Route::post('notifies', 'MassiveController@saveMassiveNotifies');
        Route::post('upload-files-client', 'MassiveController@uploadClientDocuments');
        Route::post('save_ciuos', 'MassiveController@saveCiuos');
        Route::post('save_eps', 'MassiveController@saveEps');
    });

    Route::get('/cargues', 'MassiveController@massiveViewClient');

    Route::prefix('carguemasivo')->group(function () {
        Route::post('affiliate', 'MassiveController@saveMassiveAffiliates');
        Route::post('usuarios', 'MassiveController@saveUsers');
        Route::post('update_user_role', 'MassiveController@updateUserRole');
        Route::post('update_user_rethus', 'MassiveController@updateUserLicRethus');
        Route::post('save_it_liquidation', 'MassiveController@saveItLiquidation');
        Route::post('affiliate_employers', 'MassiveController@saveMassiveAffiliatesEmployers');
        Route::post('edit_affiliate', 'MassiveController@editMassiveAffiliates');
        Route::post('employer', 'MassiveController@saveMassiveEmployers');
        Route::post('services', 'MassiveController@saveMassiveServices');
        Route::post('upload-files-1', 'MassiveController@uploadDocuments');
        Route::post('upload-files-2', 'MassiveController@uploadDocuments2');
        Route::post('upload-files-action', 'MassiveController@uploadDocumentActions');
        Route::post('documents_fraud', 'MassiveController@documentsFraud');
        Route::post('/carguemasivo/upload-user-photo', 'MassiveController@uploadUserPhoto');
        Route::post('save_affiliates_pyp', 'MassiveController@savePyPAffiliates');
        Route::post('/ally', 'MassiveController@radicationAlly');
    });

    Route::get('/cargues_rei', 'MassiveController@massiveViewRei');

    Route::prefix('cargues_rei')->group(function () {
        Route::post('phase_0', 'MassiveController@cargues_rei');
    });

    Route::get('/massiveReturnyCorrespondenceItems', 'MassiveController@massiveReturnyCorrespondenceItems');


    // BANDEJA
    Route::get('/bandeja2', 'ServiceController@inbox2');
    Route::get('/bandeja', 'ServiceController@inbox');
    Route::get('/espera_respuesta_proveedor', 'ServiceController@inbox_provider');
    Route::get('/busqueda2', 'ServiceController@search2');
    Route::get('/busqueda', 'ServiceController@search');
    Route::get('/download_inbox', 'ExcelController@reportInboxPositiva');
    Route::get('/download_original_inbox', 'ExcelController@reportOriginalInboxPositiva');
    Route::get('/download_psychological_inbox', 'ExcelController@reportInboxPsychological');

    Route::get('reportExpenseRecognitionPayment/{id}', 'Services\PeRecognitionExpensesController@reportExpenseRecognitionPayment');

    // FACTURACION
    Route::get('/facturacion', 'UserController@billing');

    Route::get('/facturacion-profesionales', 'UserController@billingDoc');
    Route::post('/admin/invoices', 'AdminController@uploadInvoices');
    Route::post('/admin/create-invoice', 'AdminController@createInvoice');
    Route::post('/admin/close-invoice', 'AdminController@closeInvoice');
    Route::post('/admin/docinvoices', 'AdminController@uploadDocinvoices');
    Route::post('/admin/generate-invoice', 'AdminController@bill_invoice');
    Route::post('/admin/generate-invoiceorienta', 'AdminController@bill_orienta');
    Route::post('/admin/activities-invoice', 'AdminController@generateExcelActivitiesInvoice');
    Route::post('/admin/send-email-invoice', 'AdminController@sendEmailInvoice');
    Route::post('/admin/change_state', 'AdminController@changeStateInvoice');
    Route::post('/admin/update_dovinvoice', 'AdminController@updateDocInvoice');
    Route::post('/admin/get_dovinvoice', 'AdminController@getDocInvoice');
    Route::post('/admin/docinvoice_word', 'AdminController@docinvoiceWord');
    Route::get('/admin/reportes/download_email_report', 'ExcelController@reportEmailsColpensiones');

    // ANS
    Route::get('/contractual_pre_approved_one', 'AnsController@ansDataStudioOne');
    Route::get('/contractual_pre_approved_two', 'AnsController@ansDataStudioTwo');
    Route::get('/contractual_pre_approved_three', 'AnsController@ansDataStudioThree');
    Route::get('/contractual_pre_approved_four', 'AnsController@ansDataStudioFour');

    // ADMIN
    Route::get('/admin/masivo', 'AdminController@massive2');
    Route::post('/admin/masivo', 'AdminController@massive2');
    Route::get('/admin/estados/{id?}', 'AdminController@states');
    Route::get('/admin/acciones/{id?}', 'AdminController@actions');
    Route::get('/admin/servicios/{id?}', 'AdminController@services');
    Route::get('/admin/roles/{id?}', 'AdminController@roles');
    Route::get('/admin/empresas/{id?}', 'AdminController@companies');
    Route::get('/admin/usuarios/{id?}', 'AdminController@users');
    Route::get('/admin/clientes/{id?}', 'AdminController@clients');

    //TOMADOR AUTORIZADO PERMISOS
    Route::get('/admin/usuarios_permisos/{id?}', 'AdminController@user_permision');
    Route::post('/admin/usuarios_permisos/polices', 'AdminController@getPolicyActive');
    Route::post('/admin/usuarios_permisos/save', 'AdminController@permision_save');
    Route::get('/admin/autorizados/list', 'AdminController@getAutorizadosTomador');
    Route::get('/admin/autorizados/valid', 'AdminController@checkAutorizado');
    Route::post('/admin/autorizados/autoriceTomador', 'AdminController@autoriceTomador');

    Route::post('/admin/estados/{id?}', 'AdminController@states');
    Route::post('/admin/acciones/{id?}', 'AdminController@actions');
    Route::post('/admin/servicios/{id?}', 'AdminController@services');
    Route::post('/admin/roles/{id?}', 'AdminController@roles');
    Route::post('/admin/empresas/{id?}', 'AdminController@companies');
    Route::post('/admin/usuarios/{id?}', 'AdminController@users');
    Route::post('/usuarios/permission', 'HomeController@users_permisos');
    Route::post('/admin/clientes/{id?}', 'AdminController@clients');

    //PROVEEDORES
    Route::get('/admin/proveedores/{id?}', 'ProviderController@index');
    Route::post('/admin/proveedores/{id?}', 'ProviderController@index');
    Route::get('/admin/proveedores/eliminar/{id}', 'ProviderController@delete');

    Route::get('/admin/bill/{initial_date}/{final_date}/{invoice_num}', 'AdminController@bill');
    Route::get('/admin/bill_new/{invoice_num}', 'AdminController@bill_new');
    Route::get('/admin/bill_new2/{invoice_num}', 'AdminController@bill_new2');
    Route::post('/admin/bill_update', 'AdminController@billUpdate');
    Route::post('/admin/bill_update_orienta', 'AdminController@billUpdateOrienta');

    Route::get('/admin/bill_doc_new/{invoice_num}', 'AdminController@bill_doc_new');
    Route::get('/admin/bill_doc_new2/{invoice_num}', 'AdminController@bill_doc_new2');
    Route::post('/admin/bill_doc_update', 'AdminController@bill_doc_update');

    Route::get('/admin/reportes2', 'AdminReportsController@index');

    Route::get('/admin/envio-reportes', 'SendReportController@index');
    Route::get('/admin/envio-reportes/{id}', 'SendReportController@index');
    Route::post('/admin/envio-reportes', 'SendReportController@index');
    Route::post('/admin/envio-reportes/{id}', 'SendReportController@index');

    Route::get('/admin/reportes', 'AdminController@reports');
    Route::get('/admin/reporte_no_solicitado', 'AdminController@not_generated_report');
    Route::post('/admin/reportes', 'AdminController@updateProyections');
    Route::get('/admin/reportes/comovamos', 'AdminController@howToGoing');
    Route::get('/admin/reportes/comovamosservicios', 'AdminController@howToGoingDetail');
    Route::get('/admin/reportes/recobros', 'AdminController@recobros');
    Route::get('/admin/reportes/downloads', 'AdminController@descargas');

    Route::get('/admin/reportes/productividad', 'AdminController@productivity');

    Route::get('/proformas', 'AdminController@proforms');

    Route::get('/admin/reportes/download_atel', 'ExcelController@reportATEL');
    Route::get('/admin/reportes/download-controversy', 'ExcelController@reportControversy');
    Route::get('/admin/reportes/download_states', 'ExcelController@reportStates');
    Route::get('/admin/reportes/download_pcls', 'ExcelController@reportPCL');
    Route::get('/admin/reportes/download_informativos', 'ExcelController@reportInformativos');
    Route::get('/admin/reportes/download_rehabilitations', 'ExcelController@reportRehabilitations');
    Route::get('/admin/reportes/download_rehabilitations', 'ExcelController@reportRehabilitations');

    //ADMIN DOWNLOADS
    Route::get('/admin/reportes/download_admin_states', 'ExcelController@reportAdminStates');
    Route::get('/admin/reportes/download_admin_states_pcl', 'ExcelController@reportAdminPclStates');
    Route::get('/admin/reportes/download_admin_state_115/{service}', 'ExcelController@reportAdmin115State');
    Route::get('/admin/reportes/download_admin_schedule', 'ExcelController@reportAdminSchedule');
    Route::get('/admin/reportes/download_admin_spp_covid', 'ExcelController@reportAdminSppCovid');
    Route::get('/admin/reportes/download_admin_codess', 'ExcelController@reportAdminCodess');
    Route::get('/admin/reportes/download_admin_approved_pqr', 'ExcelController@reportAdminApprovedPQR');
    Route::get('/admin/reportes/download_admin_notify', 'ExcelController@reportAdminNotifyCodess');
    Route::get('/admin/reportes/download_admin_actions', 'ExcelController@reportAdminActions');
    Route::post('/admin/reportes/download_actions_delimited', 'ExcelController@reportAdminActionsDelimited');
    Route::post('/admin/reportes/download_actions_box', 'ExcelController@reportAdminActionsBox');
    Route::post('/admin/reportes/report_policy', 'ExcelController@reportPolicy');
    Route::post('/admin/reportes/download_agent_commission', 'ExcelController@reportAgentCommission');
    Route::post('/admin/reportes/download_states_delimited', 'ExcelController@reportAdminStatesDelimited');
    Route::post('/admin/reportes/download_states_it_delimited', 'ExcelController@reportAdminStatesITDelimited');
    Route::post('/admin/reportes/download_states_mdi_delimited', 'ExcelController@reportAdminStatesMDIDelimited');
    Route::post('/admin/reportes/download_payment_bases_states_delimited', 'ExcelController@reportAdminPaymentBasesStatesDelimited');
    Route::post('/admin/reportes/budget_it_delimited', 'ExcelController@reportBudgetItDelimited');
    Route::post('/admin/reportes/affiliate_acreedor_it_delimited', 'ExcelController@reportAffiliateAcreedorItDelimited');
    Route::post('/admin/reportes/third_acreedor_it_delimited', 'ExcelController@reportThirdAcreedorItDelimited');
    Route::post('/admin/reportes/cre_unfavorable_it_delimited', 'ExcelController@reportUfCreDelimited');
    Route::post('/get_payment_control_it', 'ExcelController@getPaymentControlIt');
    Route::post('/admin/reportes/report_primas', 'ExcelController@reportPrimas');
    Route::post('/admin/reportes/reportPendingPremiums', 'ExcelController@reportPendingPremiums');
    Route::post('/admin/reportes/reportIncidents', 'ExcelController@reportIncidents');
    Route::post('/admin/reportes/reportPendingIncidentAmount', 'ExcelController@reportPendingIncidentAmount');
    Route::post('/admin/reportes/reportfirstLevelGis', 'ExcelReportGisController@reportFirstLevelGis');
    Route::post('/admin/reportes/reportAccountingEntry', 'ExcelController@reportAccountingEntry');
    Route::post('/admin/reportes/reinsuranceReport', 'ExcelController@reinsuranceReport');
    Route::post('/admin/reportes/ppndReport', 'ExcelController@ppndReport');
    Route::post('/admin/reportes/generateActivePortfolioReport', 'ExcelController@generateActivePortfolioReport');
    Route::post('/admin/reportes/reportThirdPartyPayments', 'ExcelController@reportThirdPartyPayments');
    Route::post('/admin/reportes/reportOynr', 'ExcelController@reportOynr');
    Route::post('/admin/reportes/acselAccountingEntriesView', 'ExcelController@acselAccountingEntriesView');
    Route::post('/admin/reportes/acselPolicyView', 'ExcelController@acselPolicyView');
    Route::post('/admin/reportes/reportAccountIban', 'ExcelController@reportAccountIban');
    Route::post('/admin/reportes/reportAdminAutorizadosSiniestro', 'ExcelController@reportAdminAutorizadosSiniestro');
    Route::post('/admin/reportes/reportGis', 'ExcelController@reportGis');

    Route::post('/admin/reportes/reportXmlOne', 'ExcelController@reportXmlOne');
    Route::post('/admin/reportes/reportXmlTwo', 'ExcelController@reportXmlTwo');
    Route::post('/admin/reportes/reportXmlThree', 'ExcelController@reportXmlThree');
    Route::post('/admin/reportes/reportXmlFour', 'ExcelController@reportXmlFour');
    Route::post('/admin/reportes/reportXmlFive', 'ExcelController@reportXmlFive');
    Route::post('/admin/reportes/reportReinsurance', 'ExcelController@reportReinsurance');
    Route::post('/admin/reportes/reportSubscription', 'ExcelController@reportSubscription');
    Route::post('/admin/reportes/affiliateReport', 'ExcelController@affiliateReport');
    Route::post('/admin/reportes/reportProviders', 'ExcelController@reportProviders');
    Route::post('/admin/reportes/reportUsers', 'ExcelController@reportUsers');

    Route::get('/admin/reportes/download_tutelage_gestion', 'ExcelController@reportTutelageGestion');
    Route::get('/admin/reportes/download_admin_origin_neps/{regional}', 'ExcelController@reportOriginNEPS');

    //COLPENSIONES REPORTS
    Route::post('/admin/reportes/determination_it_delimited', 'ExcelController@reportDeterminationItDelimited');
    Route::post('/admin/reportes/determination_pcl_delimited', 'ExcelController@reportDeterminationPclDelimited');
    Route::post('/admin/reportes/determination_rei_delimited', 'ExcelController@reportReiDelimited');
    Route::post('/admin/reportes/fractional_control_by_bizagi', 'ExcelController@reportFractionalControlByBizagi');
    Route::post('/admin/reportes/payment_control_it', 'ExcelController@reportPaymentControlIt');


    Route::get('/admin/reportes/actions_report_spp', 'ExcelController@reportAdminActionsSPP');
    Route::get('/admin/reportes/actions_report_pcl_innovacion', 'ExcelController@reportAdminActionsPCLInnovacion');
    Route::get('/admin/reportes/actions_report_notify', 'ExcelController@reportAdminActionsNotify');

    Route::get('/admin/reportes/active_users', 'ExcelController@reportActiveUsers');
    Route::get('/admin/reportes/how_many_actions', 'ExcelController@reportHowManyActions');
    Route::post('/admin/reportes/how_many_actions_by_month', 'ExcelController@reportActionsByMonth');
    Route::post('/admin/reportes/special_conditions_report', 'ExcelController@specialConditionsReport');
    Route::get('/admin/reportes/consecutivo_pcl', 'ExcelController@reportConsecutivePcl');
    Route::get('/admin/reportes/consecutivo_it', 'ExcelController@reportConsecutiveIt');
    Route::get('/admin/reportes/it_fractional_control', 'ExcelController@reportFractionalControl');
    Route::get('/admin/reportes/inability_701', 'ExcelController@reportInabilityIt701');
    Route::get('/admin/reportes/inability_410', 'ExcelController@reportInabilityIt410');

    Route::get('/admin/reportes/report_mdi', 'ExcelController@reportMdi');

    Route::get('/admin/reportes/control_appointments/{service}', 'ExcelController@reportControlAppointments');
    Route::get('/admin/reportes/notifications/{service}', 'ExcelController@reportNotification');
    Route::get('/admin/reportes/rejection_pcl/{service}', 'ExcelController@reportRejectionPcl');
    Route::get('/admin/reportes/exam_request/{service}', 'ExcelController@reportExamRequestPcl');
    Route::get('/admin/reportes/doc_request/{service}', 'ExcelController@reportDocRequestPcl');
    Route::get('/admin/reportes/pcl_rei_billing/{service}', 'ExcelController@reportBilling');
    Route::get('/admin/reportes/pcl_rei_all_billing/{service}', 'ExcelController@reportAllBilling');
    Route::get('/admin/reportes/ans_dates/{service}', 'ExcelController@reportAnsDatesPclRei');
    Route::get('/admin/reportes/dictum_cases/{service}', 'ExcelController@reportDictumCasesPclRei');
    Route::get('/admin/reportes/states_200/{service}', 'ExcelController@reportStates200PclRei');
    Route::get('/admin/reportes/states_100/{service}', 'ExcelController@reportBase100');

    Route::get('/admin/reportes/descargas_admin', 'AdminController@reportsAdmin');
    Route::get('/admin/reportes/lookerstudio', 'AdminController@reportsLookerStudio');
    Route::post('/api/reportes/reports-admin-get-states', 'AdminController@reportsAdminGetStates');
    Route::post('/admin/reportes/download-switch-documents', 'AdminController@reportsAdminSwitchDocuments');
    Route::get('/admin/reportes/download_admin/{service}', 'ExcelController@reportAdmin');
    Route::get('/admin/reportes/down_test/{service}', 'ExcelController@reportTest');
    Route::get('/admin/reportes/reporte_determinacion_it', 'ExcelController@reportDeterminationIt');
    Route::get('/admin/reporte/{service}', 'ExcelController@reportsServicesLMCPD');
    Route::get('/admin/reportes/services/{service}', 'ExcelController@reportServicesPositiva');
    Route::get('/admin/reportes/actions_request_provider', 'ExcelController@reportActionsProviderPositiva');
    Route::get('/admin/reportes/actions_request_provider_complete', 'ExcelController@reportActionsProviderPositivaComplete');
    Route::get('/admin/reportes/reportesEmpresariales', 'AdminController@businessReport');
    Route::get('/admin/reportes/reportesEncuestas', 'AdminController@suveyReport');

    Route::get('/admin/reportes/reportesOperativos', 'AdminController@OperationalReport');
    Route::get('/admin/reportes/reportesGeneralDos', 'AdminController@generalReportsTwo');
    Route::get('/admin/reportes/reporteEnfermedad', 'AdminController@reportDisease');
    Route::get('/admin/reportes/reporteFallecido', 'AdminController@reportDeceased');
    Route::get('/admin/reportes/reportePorPolizas', 'AdminController@reportByPolicies');
    Route::get('/admin/reportes/reportesOperativos/asegurados', 'AdminController@insuredReport');
    Route::get('/admin/reportes/reportesOperativos/accidente', 'AdminController@accidentReport');
    Route::get('/admin/reportes/reportesOperativos/trayecto', 'AdminController@commuteReport');
    Route::get('/admin/reportes/reportesOperativos/laboral', 'AdminController@workReport');
    Route::get('/admin/reportes/reportesOperativos/siniestralidad', 'AdminController@claimsReport');
    Route::get('/admin/reportes/reportesOperativos/ocurrencia', 'AdminController@sixReport');

    Route::get('/admin/reportes/reportesIntermediarioYTomador', 'AdminController@intermedaryHolderReport');
    Route::get('/admin/reportes/download_report_intermedary_holder', 'ExcelController@downloadReportIntermedaryHolder');

    Route::get('/admin/reportes/service_indemnizations', 'ExcelController@reportServiceIndemnizationPositiva');
    Route::get('/admin/reportes/actions_report_notify_dates/{num?}', 'ExcelController@reportAdminActionsNotifyDates');
    Route::get('/admin/reportes/satisfaction_surveys_report', 'ExcelController@radicationSatisfactionSurvey');

    // CORRESPONDENCIA
    Route::get('/correspondencia/buscador', 'CorrespondenceController@search');
    Route::post('/correspondencia/buscador', 'CorrespondenceController@search');
    Route::get('/correspondencia/excel/{id?}', 'CorrespondenceController@excel');
    Route::get('/correspondencia/excelfiles/{id?}', 'CorrespondenceController@excelFiles');
    Route::get('/correspondencia/recreate/{id?}', 'CorrespondenceController@recreate');
    Route::get('/correspondencia/sort/{id?}', 'CorrespondenceController@sort');
    Route::get('/correspondencia/{id?}', 'CorrespondenceController@index');
    Route::get('/correspondencia_positiva/{id?}', 'CorrespondenceController@index2');
    Route::post('/correspondencia/{id?}', 'CorrespondenceController@close');
    Route::post('/correspondencia/{id?}/delete', 'CorrespondenceController@delete');
    Route::post('/correspondencia/{id?}/priorize', 'CorrespondenceController@priorize');
    Route::get('/correspondencia/item/{id}', 'CorrespondenceController@item');
    Route::post('/correspondencia/item/{id}', 'CorrespondenceController@action');
    Route::post('/correspondencia/regenerate/{id}', 'CorrespondenceController@regenerate');
    Route::post('/correspondencia/regenerate/{id}/{doc?}', 'CorrespondenceController@regenerate');
    Route::post('/correspondencia/{id?}/guides', 'CorrespondenceController@guides');
    Route::post('/correspondencia/{id?}/notifications', 'CorrespondenceController@notifications');
    Route::post('/correspondencia/{id?}/pdfsPositiva', 'CorrespondenceController@pdfNotificationsPositiva');
    Route::get('/correspondence/downloadExcelNotificationsPDF', 'CorrespondenceController@downloadExcelNotificationsPDF');

    Route::get('/correspondencia/correspondenceItem/{idActivity}/{paths}/{only}/{consecutive}', 'CorrespondenceController@correspondenceItem');

    // CAMBIO DE ESTADO
    Route::get('/cambioestado', 'StateChangeController@stateChangeView');
    Route::post('/cambioestado/save', 'StateChangeController@save');

    //EJECUCION MANUAL DE COBRO
    Route::get('manual_execution', 'ManualExecutionController@index');
    Route::post('manual_execution/save', 'ManualExecutionController@save');
    Route::get('createAndReplaceReceipt/{id}/{monthPassed?}', 'ManualExecutionController@createAndReplaceReceipts');
    Route::get('actualizaRecibos', 'ManualExecutionController@actualizaRecibos');

    // ELIMINACIÓN DE SERVICIOS
    Route::get('/borrar_servicios', 'ServiceController@deleteServiceView');
    Route::post('/borrar_servicios/save', 'ServiceController@deleteService');

    // REGLAS DE ACCIONES
    Route::get('/reglas-acciones', 'RuleActionsController@index');
    Route::get('/reglas-acciones/{id?}', 'RuleActionsController@report');
    Route::post('/reglas-acciones', 'RuleActionsController@index');

    // ASIGNACIÓN MASIVA DE CASOS A USUARIOS
    Route::get('/asignacionmasivacasos', 'AssingMasiveActivityUserController@stateChangeView');
    Route::post('/asignacionmasivacasos/save', 'AssingMasiveActivityUserController@save');

    // SICAT
    Route::get('/mark_with_audit/{id?}', 'SicatController@mark_with_audit');

    // HELPERS
    Route::get('api/doctors', function ($cpath) {
        return \App\Doctor::$INFORMATION;
    });
    // Oceanica

    Route::post('/servicio/{id}/quotation/save', 'Services\QuotationController@save');

    // Actualizar quotation
    Route::post('/servicio/{id}/quotation/update', 'Services\QuotationController@update');

    // Rutas vista del intermediario
    Route::get('/intermediario', 'AgentController@index');
    Route::get('/intermediario/cotizaciones', 'AgentController@quotations');
    Route::get('/intermediario/constancias', 'AgentController@constancy');
    Route::get('/intermediario/polizas', 'AgentController@policy');

    Route::get('/intermediario/variaciones', 'AgentController@variacionesIntermediario');
    Route::get('/intermediario/solicitud_variaciones', 'AgentController@solicitudVariaciones');
    Route::get('/intermediario/estadisticas_reportes_cotizaciones', 'AgentController@intermedaryReportingQuotation');
    Route::get('/intermediario/estadisticas_reportes_polizas', 'AgentController@intermedaryReportingPolicy');

    //exportar reporte intermediario
    Route::get('/intermediario/exportar_excel_cotizaciones', 'AgentController@downloadReportQuotationIntermedary');
    Route::get('/intermediario/exportar_excel_polizas', 'AgentController@downloadReportPolicyIntermedary');

    //    Route::get('/intermediario/reportes', 'AgentController@reports');
    Route::get('/intermediario/descargas', 'AgentController@downloads');

    //    Route::get('/intermediario/videotutoriales', 'AgentController@videoTutorials');
    Route::get('/intermediario/genera_cotizacion', 'AgentController@generateQuotation');
    Route::get('/intermediario/emision_poliza', 'AgentController@policyIssuance');
    Route::get('/intermediario/reporte_planilla', 'AgentController@payrollReport');
    Route::get('/intermediario/preguntas_frecuentes', 'AgentController@frequentlyAskedQuestions');

    Route::get('/generar-pdf/{id}', 'AgentController@generarPdf');
    //editar cotizaciones del intermediario
    Route::get('/intermediario/editar/{id}/cotizaciones', 'AgentController@editQuotation');
    //validar los minutos despues de solicitada la cotización para solicitar la poliza
    Route::post('/intermediario/cotizaciones/validate-minutes', 'AgentController@validateMinutes');

    Route::post('/intermediario/cotizaciones/changeStateQuotation', 'AgentController@changeStateQuotation');
    //validar que la fecha de vigencia de la poliza desde se mayor o igual al dia actual
    Route::post('/intermediario/cotizaciones/validate-date', 'AgentController@validateDate');

    /*** Rutas para ejecutar acción 'Iniciar Cotización' ***/
    Route::post('/servicio/quotation/startQuotation', 'Services\QuotationController@startQuotation');
    // Generar la cotización, requiere id de la actividad
    Route::post('/servicio/quotation/generate/{id}', 'Services\QuotationController@generateQuotation');

    //Condiciones especiales de la cotización
    Route::post('/servicio/quotation/condition_special/{id}', 'Services\QuotationController@conditionSpecial');

    // Rutas para el servicio de Cotizacion de poliza SORT
    Route::get('/intermediario/cotizacion/datos', 'Services\QuotationController@index');
    //Rutas para el servicio de Poliza SORT
    Route::get('/poliza/', 'Services\PolicyController@index');
    Route::get('/intermediario/cotizacion/datos/{id}', 'Services\QuotationController@index');
    Route::post('/services/create/quotations', 'Services\QuotationController@createQuotation');

    Route::get('/get/occupations-gis', 'Services\ComprehensiveAccidentManagementController@dataOccupations');
    Route::get('/get/economic-activities', 'Services\QuotationController@dataEconomicActivities');

    Route::get('/intermediario/cotizacion/datos/{id}/intermediary', 'Services\QuotationController@showIntermediary');
    Route::get('/intermediario/cotizacion/datos/{id}/policy_holder', 'Services\QuotationController@showPolicyholder');
    Route::get('/intermediario/cotizacion/datos/{id}/quote_details', 'Services\QuotationController@showQuoteDetails');
    Route::get('/intermediario/cotizacion/datos/{id}/policy_calculation', 'Services\QuotationController@showPolicyCalculation');
    Route::get('/intermediario/cotizacion/datos/{id}/upload_documents', 'Services\QuotationController@showUploadDocuments');
    //ruta post guardar datos intermediario
    Route::post('/intermediario/{id}/cotizacion/save', 'Services\QuotationController@save_intermediary');
    //ruta get traer datos de quotations
    Route::get('/intermediario/{id}/cotizacion', 'Services\QuotationController@getQuotations');
    Route::get('/intermediario/{id}/cotizacionCal', 'Services\QuotationController@getQuotationsCal');
    //ruta post guardar datos de cálculo poliza
    Route::post('/poliza/{id}/cotizacion/save', 'Services\QuotationController@save_poliza');
    //subir documentos de cotizacion
    Route::post('/intermediario/{id}/cotizacion/upload_documents', 'Services\QuotationController@uploadDocuments');

    //Route::get('/web-service-credid/data', 'Integrations\WebServiceMnk\WebServiceCredidController@getDataPolicyHolder');
    Route::get('/web-service-credid/data', 'Integrations\WebserviceAcselController@getDataIntermediaryCredid');
    Route::get('/intermediario/cotizacion/datos', 'Services\QuotationController@index');

    //Rutas para el servicio de Poliza SORT
    //Route::post('/policy-search', 'PolicySortController@search')->name('policy.search');
    // En routes/web.php o routes/api.php
    Route::post('/suspension-payment/{id}', 'Services\PolicySortController@suspensionPayment');
    Route::post('/poliza-sort/search', 'Services\PolicySortController@search');
    Route::post('/poliza-sort/update', 'Services\PolicySortController@update');
    Route::delete('/poliza-sort/delete', 'Services\PolicySortController@delete');

    Route::get('/intermediario/download/documents/{id}', 'Services\PolicySortController@downloadDocument');

    //test route borrar luego
    Route::get('/test/actualizarEstado', 'Services\ComprehensiveAccidentManagementController@formalCaseFormatClosureAutomatic');

    // Ruta para el services medical
    Route::get('/servicio/{id}/medical_services', 'Services\MedicalServicesController@form');
    Route::get('/generar_pdf_incapacidad/{id}', 'Services\MedicalServicesController@medicalDisability_pdf');
    Route::get('/generar_pdf_carta_incapacidad/{id}', 'Services\MedicalServicesController@medicalDisabilityLetter_pdf');

    // Ruta para guardar services medical
    Route::post('/servicio/{id}/medical_services/save', 'Services\MedicalServicesController@save');

    //Aciones del servicio Prestaciones medicas
    Route::post('/service/{id}/action_cancel_service', 'Services\MedicalServicesController@cancelService');
    Route::post('/service/{id}/negacion_servicio', 'Services\MedicalServicesController@denialService');
    Route::post('/service/{id}/medical_disability', 'Services\MedicalServicesController@medical_disability');
    Route::get('/service/actividad/{id}/follow_up/{id_last_follow_up}/medical_disability', 'Services\MedicalServicesController@email_medical_disability');
    Route::post('/service/{id}/medical_disability', 'Services\MedicalServicesController@medical_disability');
    Route::post('/service/medical_disability_time_ap', 'Services\MedicalServicesController@medicalDisabilityTime');
    Route::post('/service/medical_hospitalization_ap', 'Services\MedicalServicesController@medicalHospitalizationTime');
    Route::post('/service/medical_diagnostic_imaging_order_ap', 'Services\MedicalServicesController@medicalDiagnosticImagingOrderTime');
    Route::get('/service/{id}/load_pdf_test', 'Services\MedicalServicesController@medical_disability_test');
    Route::post('/servicio/{id}/remision_especialista', 'Services\MedicalServicesController@referral_specialist');
    Route::post('/servicio/{id}/asignar_atencion_primaria', 'Services\MedicalServicesController@assignPrimaryCareService');
    Route::post('/servicio/{id}/emitir_hospitalizacion', 'Services\MedicalServicesController@issueMedicalServiceHospitalization');
    Route::post('/servicio/{id}/imagen_diagnostica', 'Services\MedicalServicesController@issueDiagnosticImagingOrder');
    Route::post('/servicio/{id}/formula_medicamentos', 'Services\MedicalServicesController@emitPrescription');
    Route::post('/servicio/{id}/formula_medicamentos_controlados', 'Services\MedicalServicesController@emitControlledPrescription');
    Route::post('/service/{id}/aprobacion_servicio', 'Services\MedicalServicesController@approveService');
    Route::post('/servicio/{id}/reportar_prestaciones_mnk', 'Services\MedicalServicesController@reportMedicalAuditMnk');
    Route::post('/servicio/{id}/cambiar_proveedor', 'Services\MedicalServicesController@changeProviderMedicalService');

    //Aciones del servicio Prestaciones medicas - atención secondaria
    Route::post('/service/{id}/action_cancel_service_secondary', 'Services\MedicalServicesSecondaryCareController@cancelService');
    Route::post('/service/{id}/negacion_servicio_service_secondary', 'Services\MedicalServicesSecondaryCareController@denialService');
    Route::post('/service/{id}/medical_disability_service_secondary', 'Services\MedicalServicesSecondaryCareController@medical_disability');
    Route::post('/service/medical_disability_time_as', 'Services\MedicalServicesSecondaryCareController@medical_disability_time');
    Route::post('/servicio/{id}/remision_especialista_service_secondary', 'Services\MedicalServicesSecondaryCareController@referral_specialist');
    Route::post('/servicio/{id}/asignar_atencion_primaria_service_secondary', 'Services\MedicalServicesSecondaryCareController@assignPrimaryCareService');
    Route::post('/servicio/{id}/emitir_hospitalizacion_service_secondary', 'Services\MedicalServicesSecondaryCareController@issueMedicalServiceHospitalization');
    Route::post('/servicio/{id}/imagen_diagnostica_service_secondary', 'Services\MedicalServicesSecondaryCareController@issueDiagnosticImagingOrder');
    Route::post('/servicio/{id}/formula_medicamentos_service_secondary', 'Services\MedicalServicesSecondaryCareController@emitPrescription');
    Route::post('/servicio/{id}/formula_medicamentos_controlados_service_secondary', 'Services\MedicalServicesSecondaryCareController@emitControlledPrescription');
    Route::post('/service/{id}/aprobacion_servicio_service_secondary', 'Services\MedicalServicesSecondaryCareController@approveService');
    Route::post('/servicio/{id}/reportar_prestaciones_mnk_service_secondary', 'Services\MedicalServicesSecondaryCareController@reportMedicalAuditMnk');
    Route::get('/servicio/{id}/medical_services_secondary_care', 'Services\MedicalServicesSecondaryCareController@form');
    Route::post('/servicio/{id}/medical_services_secondary_care/save', 'Services\MedicalServicesSecondaryCareController@save');

    //Aciones del servicio Medicamanetos
    Route::post('/servicio/{id}/medication_services/aprobar_servicio_medicamentos', 'Services\MedicationServicesController@approveService');
    Route::post('/servicio/{id}/medication_services/anular_servicio_medicamentos', 'Services\MedicationServicesController@denialService');
    Route::post('/servicio/{id}/medication_services/reportar_negacion_medicamentos', 'Services\MedicationServicesController@reportDenialService');
    Route::post('/servicio/{id}/medication_services/auditoria_mnk_medicamentos', 'Services\MedicationServicesController@reportMedicalAuditMnk');
    Route::get('/servicio/{id}/medication_services/getPharmacies', 'Services\MedicationServicesController@getPharmacies');
    //VADEMECUM
    Route::get('/obtenerLista/vademecum', 'Vademecum\VademecumController@index');
    //TABLERO MONITOREO Y CONTROL
    Route::get('/tablero/monitereo_y_control', 'Tables\MonitoreoController@indexGis');
    //TABLERO MONITOREO Y CONTROL GIS
    Route::get('/tablero/monitoreo_y_control_gis', 'Tables\MonitoreoController@indexGis');

    //TABLERO de colectividad
    Route::get('/tablero/colectividad', 'Tables\BenefitColectiveController@index');
    Route::post('/actions/delete_colectividad', 'Tables\BenefitColectiveController@deleteColective');
    Route::post('/actions/storeAksColectividad', 'Tables\BenefitColectiveController@deleteColectiveIndividual');

    //TABLERO de correos
    Route::get('/tablero/correos', 'Tables\MailBoardController@index');

    Route::post('/service/table/resend_email_mail_board', 'Tables\MailBoardController@reSendEmail');

    //TABLERO AUDITORIA MEDICA
    Route::get('/tablero/auditoria_medica', 'Tables\MedicalAuditController@index');
    Route::post('/tablero/auditoria_medica_aprobar/{id}', 'Tables\MedicalAuditController@approve');
    Route::post('/tablero/auditoria_medica_rechazar/{id}', 'Tables\MedicalAuditController@reject');

    //TABLERO INDEMNIZACIONES
    Route::get('/tablero/indemnizaciones', 'Tables\CompensationBoardController@index');
    Route::get('/tablero/indemnizaciones/reconocimiento_gastos', 'Tables\CompensationBoardController@expenseRecognition');
    Route::get('/tablero/indemnizaciones/incapacidad_temporal', 'Tables\CompensationBoardController@temporaryDisability');
    Route::get('/tablero/indemnizaciones/incapacidad_permanente', 'Tables\CompensationBoardController@permanentDisability');
    Route::get('/tablero/indemnizaciones/controversias_dictamen', 'Tables\CompensationBoardController@index');
    Route::get('/tablero/indemnizaciones/cuentas_medicas', 'Tables\CompensationBoardController@medicaBills');
    Route::get('/tablero/indemnizaciones/pagos_admnistrativos', 'Tables\CompensationBoardController@tableAdministrativePayments');
    Route::get('/tablero/indemnizaciones/reaperturas', 'Tables\CompensationBoardController@reopening');
    Route::get('/tablero/indemnizaciones/subrogacion', 'Tables\CompensationBoardController@tableSubrogacion');

    //TABLERO ACTUALIZACION DE DATOS
    Route::get('/tablero/actualizacion_datos', 'Tables\DataUpdatesController@index');
    Route::get('/tablero/actualizacion_datos/tomadores', 'Tables\DataUpdatesController@indexTaker');
    Route::get('/tablero/actualizacion_datos/tomadores/{id}', 'Tables\DataUpdatesController@editTaker');
    Route::post('/tablero/actualizacion_datos/tomadores/{id}/update', 'Tables\DataUpdatesController@updateTaker');
    Route::post('/tablero/actualizacion_datos/tomadores/{id}/update-inclusion/{inclusionId}', 'Tables\DataUpdatesController@updateTakerInclusion');

    Route::get('/tablero/actualizacion_datos/asegurados', 'Tables\DataUpdatesController@indexInsured');
    Route::get('/tablero/actualizacion_datos/asegurados/{id}', 'Tables\DataUpdatesController@editInsured');
    Route::post('/tablero/actualizacion_datos/asegurados/{id}/update', 'Tables\DataUpdatesController@updateInsured');

    Route::get('/tablero/actualizacion_datos/intermediarios', 'Tables\DataUpdatesController@indexIntermediary');
    Route::get('/tablero/actualizacion_datos/intermediarios/{id}', 'Tables\DataUpdatesController@editIntermediary');
    Route::post('/tablero/actualizacion_datos/intermediarios/{id}/update', 'Tables\DataUpdatesController@updateIntermediary');
    Route::post('/tablero/actualizacion_datos/intermediarios/{id}/search-code', 'Tables\DataUpdatesController@searchAscelCode');
    Route::post('/tablero/actualizacion_datos/intermediarios/{id}/upsert-corredor', 'Tables\DataUpdatesController@upsertIntermediary');

    //ACCIONES DE REPAERTURAS DESDE EL TABLERO DE INDEMNIZACIONES
    Route::post('/tablero/indemnizaciones/reaperturas/{activity_id}/aprobar', 'Tables\CompensationBoardController@approveReopening');
    Route::post('/tablero/indemnizaciones/reaperturas/{activity_id}/rechazar', 'Tables\CompensationBoardController@rejectReopening');
    Route::post('/tablero/indemnizaciones/reaperturas/{activity_id}/solicitar_información', 'Tables\CompensationBoardController@requestInformationReopening');
    Route::post('/tablero/indemnizaciones/reaperturas/{activity_id}/inconsistencias', 'Tables\CompensationBoardController@inconsistenciesReopening');


    //TABLERO AFILIADO
    Route::get('/tablero/afiliado/{id}', 'Tables\AfiliateTableController@index');
    Route::get('/tablero/afiliado/{id}/prestaciones_medicas', 'Tables\AfiliateTableController@medicalBenefits');
    Route::get('/tablero/afiliado/{id}/solicitud_prestacion_medica', 'Tables\AfiliateTableController@requestMedicalBenefitServices');
    Route::post('/tablero/afiliado/{id}/solicitud_prestacion_medica/save', 'Tables\AfiliateTableController@saveMedicalBenefitServices');
    Route::post('/tablero/afiliado/{id}/soportes/save', 'Tables\AfiliateTableController@saveSupports');
    Route::get('/tablero/afiliado/{id}/solicitud_medicamentos', 'Tables\AfiliateTableController@requestMedication');
    Route::post('/tablero/afiliado/{id}/solicitud_medicamentos/save', 'Tables\AfiliateTableController@saveMedication');
    Route::post('/tablero/afiliado/{id}/soportes_medicamentos/save', 'Tables\AfiliateTableController@saveSupportsMedication');
    Route::post('/tablero/afiliado/upload_econimic', 'Tables\AfiliateTableController@uploadEconimic');
    Route::post('/tablero/afiliado/upload_document_inhability', 'Tables\AfiliateTableController@uploadDocumentInhability');


    Route::get('/tablero/afiliado/{id}/prestaciones_economicas', 'Tables\AfiliateTableController@economicBenefits');
    Route::get('/tablero/afiliado/{id}/radica_incapacidad', 'Tables\AfiliateTableController@registerDisability');
    Route::post('/tablero/afiliado/{id}/radica_incapacidad/save', 'Tables\AfiliateTableController@saveRegisterDisability');
    Route::post('/tablero/afiliado/{id}/soportes_incapacidad/save', 'Tables\AfiliateTableController@saveSupportsDisability');
    Route::get('/tablero/afiliado/{id}/reconocimiento_gastos', 'Tables\AfiliateTableController@recognizeExpenses');
    Route::post('/tablero/afiliado/{id}/reconocimiento_gastos/save', 'Tables\AfiliateTableController@saveRecognizeExpenses');
    Route::post('/tablero/afiliado/{id}/soportes_gastos_refund/save', 'Tables\AfiliateTableController@saveRecognizeExpensesRefund');
    Route::post('/tablero/afiliado/{id}/soportes_gastos/save', 'Tables\AfiliateTableController@saveSupportsExpenses');

    Route::get('/tablero/afiliado/{id}/certificado_afiliacion', 'Tables\AfiliateTableController@membershipCertificate');
    Route::post('/tablero/afiliado/{id}/certificado_afiliacion/generar', 'Tables\AfiliateTableController@generateAffiliationCertificate');
    Route::get('/tablero/afiliado/{id}/certificado_afiliacion/download', 'Tables\AfiliateTableController@downloadDocumentAffiliate');
    Route::get('/tablero/afiliado/{id}/certificado_afiliacion/email', 'Tables\AfiliateTableController@emailDocumentAffiliate');

    Route::get('/tablero/afiliado/{id}/actualiza_datos', 'Tables\AfiliateTableController@updatesData');
    Route::post('/tablero/afiliado/{id}/actualiza_datos/save', 'Tables\AfiliateTableController@saveDataAfilliate');

    Route::get('/tablero/afiliado/{id}/controversias', 'Tables\AfiliateTableController@controversies');
    Route::get('/tablero/afiliado/{id}/solicitud_controversia/{activity_gis}', 'Tables\AfiliateTableController@registerControversies');
    Route::post('/tablero/afiliado/{id}/solicitud_controversia/{activity_gis}/save', 'Tables\AfiliateTableController@saveControversies');

    Route::get('/tablero/afiliado/{id}/reaperturas', 'Tables\AfiliateTableController@reopenings');
    Route::get('/tablero/afiliado/{id}/solicitud_reapertura/{activity_gis}', 'Tables\AfiliateTableController@registerReopenings');
    Route::post('/tablero/afiliado/{id}/solicitud_reapertura/{activity_gis}/save', 'Tables\AfiliateTableController@saveReopenings');

    Route::get('/tablero/afiliado/{id}/certificado_seguro', 'Tables\AfiliateTableController@secureCertificate');
    Route::get('/tablero/afiliado/{id}/certificado_seguro/download', 'Tables\AfiliateTableController@downloadDocumentSecure');
    Route::get('/tablero/afiliado/{id}/certificado_seguro/email', 'Tables\AfiliateTableController@emailDocumentSecure');

    //TABLERO AUDITORIA CUENTAS MEDICAS
    Route::get('/tablero/auditoria_cuentas_medicas', 'Tables\MedicalBillsAuditController@medicalBillsAuditTable');

    //TABLERO DEL PROVEEDOR
    Route::middleware(['supplier.board'])->group(function () {
        Route::get('/proveedor/{id}/datos', 'Tables\ProviderController@providerData');
        Route::get('/proveedor/{id}/asignar_servicio', 'Tables\ProviderController@assignedService');
    });
    Route::get('/proveedor/tablero_cuentas_medicas', 'Tables\ProviderController@medicalAccounts');
    Route::get('/proveedor/radicar_prefactura', 'Tables\ProviderController@filePreInvoice');
    Route::get('/proveedor/radicar_prefactura_multiple', 'Tables\ProviderController@filePreInvoiceMultiple');
    Route::get('/proveedor/radicar_factura_electronica/{idPreInvoice?}', 'Tables\ProviderController@fileElectronicInvoice');
    Route::post('/proveedor/radicar_prefactura/save/{processType?}', 'Services\MedicalBillsController@uploadPreInvoice');
    Route::post('/proveedor/radicar_factura_electronica/save/{numPreInvoice}', 'Services\MedicalBillsController@uploadElectronicInvoice');
    Route::get('/proveedor/descargar_plantilla_proveedor', 'Tables\ProviderController@downloadSpreadsheet');
    Route::get('/proveedor/descargar_explicacion_plantilla_prefactura', 'Tables\ProviderController@downloadExplainPreInvoice');
    Route::get('/proveedor/descargar_plantilla_proveedor_factura_electronica', 'Tables\ProviderController@downloadSpreadsheetElectronicInvoice');
    Route::post('/proveedor/validar_prefactura', 'Services\MedicalBillsController@validatePreInvoice');
    Route::post('/proveedor/validar_factura_electronica', 'Services\MedicalBillsController@validateElectronicInvoice');
    Route::post('/test/validateAccountingStateTest', 'Services\MedicalBillsController@validateAccountingStateTest');

    Route::get('/proveedor/radicar_factura_electronica_administrativo/{idPreInvoice?}', 'Tables\ProviderController@fileElectronicInvoiceAdministrative');

    //TABLERO DE ANALISTA
    Route::get('/analista/cobros', 'Tables\AnalystController@collections');
    Route::post('/analista/cobros/resend_email_collection', 'Tables\AnalystController@reSendEmail');
    Route::post('/accion_cobro/{id}', 'Tables\AnalystController@actionCollection');

    // Ruta para el services medication
    Route::get('/servicio/{id}/medication_services', 'Services\MedicationServicesController@form');
    // Ruta para guardar services medication
    Route::post('/servicio/{id}/medication_services/save', 'Services\MedicationServicesController@save');

    //ACCIONES PARA EL SERVICIO MEDICATION
    Route::post('/servicio/{id}/reportar_entrega_medicamentos', 'Services\MedicationServicesController@reportMedicationDelivery');
    Route::post('/servicio/{id}/reportar_solicitud_medicamentos', 'Services\MedicationServicesController@reportMedicationRequest');

    //Rutas para el servicio Cuentas Medicas
    Route::get('/servicio/{id}/medical_bills', 'Services\MedicalBillsController@form');
    Route::post('/servicio/{id}/medical_bills/save', 'Services\MedicalBillsController@save');

    // Rutas para el servicio Proveedores de la seguridad social (PSS) SERVICE_SOCIAL_SECURITY_PROVIDERS SSP
    Route::get('/servicio/{id}/social_security_providers', 'Services\SocialSecurityProviderController@form');
    Route::post('/servicio/{id}/social_security_providers/save', 'Services\SocialSecurityProviderController@save');
    Route::post('/servicio/{id}/social_security_providers/save-invoices', 'Services\SocialSecurityProviderController@updateOrdinaryInvoices');
    Route::post('/servicio/{id}/social_security_providers/save-report-data', 'Services\SocialSecurityProviderController@storeReportData');
    Route::post('/servicio/{id}/social_security_providers/preview-pdf', 'Services\SocialSecurityProviderController@previewPdf');
    Route::post('/servicio/{id}/social_security_providers/preview-reimbursement-pdf', 'Services\SocialSecurityProviderController@generateReimbursementReport');
    
    Route::get('/proveedor/pss/temporary_disability/ordinary', 'Services\SocialSecurityProviderController@ordinaryInvoices');
    Route::get('/proveedor/pss/temporary_disability/claim', 'Services\SocialSecurityProviderController@ordinaryInvoices');
    Route::get('/proveedor/pss/medical_services/ordinary', 'Services\SocialSecurityProviderController@ordinaryInvoices');
    Route::get('/proveedor/pss/medical_services/claim', 'Services\SocialSecurityProviderController@ordinaryInvoices');
    Route::get('/proveedor/pss/ins/ordinary', 'Services\SocialSecurityProviderController@ordinaryInvoices');

    Route::post('/proveedor/pss/ordinary_invoices', 'Services\SocialSecurityProviderController@storeOrdinaryInvoices');


    //ACCIONES PARA CUENTAS MEDICAS
    Route::get('/servicio/{id}/pre_invoice', 'Services\MedicalBillsController@preInvoice');

    //Tomador
    Route::get('/tomador/poliza/{id}/datos', 'Services\PolicySortController@policySortData');
    Route::get('/tomador/videoTutorial', 'Services\PolicySortController@videoTutoriales');

    //Rutas para el servicio INSUMOS Y MOT
    Route::post('/servicio/{id}/doby', 'Services\SuppliesMotController@reportRequestSuppliesMot');
    Route::get('/servicio/{id}/supplies_mot', 'Services\SuppliesMotController@form');
    Route::get('/servicio/{id}/reportar_solicitud_insumos', 'Services\SuppliesMotController@report_request');
    Route::post('/servicio/{id}/subir_orden_insumos', 'Services\SuppliesMotController@uploadOrderSupplies');
    Route::post('/servicio/{id}/reportar_negacion_insumos', 'Services\SuppliesMotController@reportDenialService');
    Route::post('/servicio/{id}/anular_insumos', 'Services\SuppliesMotController@denialService');
    Route::post('/servicio/{id}/aprobar_insumos', 'Services\SuppliesMotController@approve');
    Route::post('/servicio/{id}/auditoria_mnk_medicamentos_insumos', 'Services\SuppliesMotController@reportMedicalAuditMnkInsumos');

    //Rutas pare el servicio REINTEGRO
    Route::get('/servicio/{id}/reintegrate', 'Services\ReintegrateServicesController@form');
    Route::post('/servicio/{id}/reintegrate/save', 'Services\ReintegrateServicesController@save');

    //Rutas pare el servicio PAGOS ADMINISTRATIVOS
    Route::get('/servicio/{id}/administrative_payment', 'Services\AdministrativePaymentController@form');
    Route::post('/servicio/{id}/administrative_payment/save', 'Services\AdministrativePaymentController@save');

    Route::post('/administrative_payment/{id}/file_save', 'Services\AdministrativePaymentController@registerAdministrativePayment');

    // Ruta para guardar servicio INSUMOS Y MOT
    Route::post('/servicio/{id}/supplies_mot/save', 'Services\SuppliesMotController@save');
    Route::post('/servicio/{id}/crear_insumos_mot', 'Services\SuppliesMotController@supplier_mot_service');

    //gist
    Route::get('/servicio/{id}/gis_sort', 'Services\ComprehensiveAccidentManagementController@form');

    Route::get('/servicio/{id}/gis_sort/pdf', function ($cpath, $id) {
        return "Vista previa EXPIRADA, cierre esta ventana y vuelva a generarla.";
    });
    Route::post('/servicio/{id}/gis_sort/pdf', 'Services\ComprehensiveAccidentManagementController@pdf');

    //gist table tomador
    Route::get('/servicio/gis_sort/tomador/{id?}', 'Services\ComprehensiveAccidentManagementController@index');
    Route::get('/servicio/gis_sort/tomador_responsable/{id?}', 'Services\ComprehensiveAccidentManagementController@form_responsable');
    Route::get('/servicio/gis_sort/tomador_datos/{id?}', 'Services\ComprehensiveAccidentManagementController@form_datos');
    Route::get('/servicio/gis_sort/tomador_accidente/{id?}', 'Services\ComprehensiveAccidentManagementController@form_accidente');
    Route::post('/calculate_pps', 'Services\ComprehensiveAccidentManagementController@CalculatePps');
    Route::get('/validapps/{severity}', 'Services\ComprehensiveAccidentManagementController@validapps');
    //gist table call
    Route::get('/servicio/gis_sort/table_call', 'Services\ComprehensiveAccidentManagementController@table_call');

    //git furat
    Route::get('/servicio/gis_sort/furat/affilate/{id}', 'Services\ComprehensiveAccidentManagementController@form_affilate');
    Route::get('/servicio/gis_sort/furat/employment_relationship/{id}', 'Services\ComprehensiveAccidentManagementController@form_employment_relationship');
    Route::get('/servicio/gis_sort/furat/accident_report/{id?}', 'Services\ComprehensiveAccidentManagementController@form_accident_report');
    Route::get('/servicio/gis_sort/furat/accident_report_view', 'Services\ComprehensiveAccidentManagementController@form_accident_report_view');
    Route::get('/servicio/gis_sort/furat/signature_data/{id?}', 'Services\ComprehensiveAccidentManagementController@form_signature_data');
    Route::get('/servicio/gis_sort/furat/support/{id}', 'Services\ComprehensiveAccidentManagementController@form_support');
    Route::get('/tomador/poliza/{id}/furat/{table}/{npoliza}', 'Services\ComprehensiveAccidentManagementController@table_furat_view');
    Route::get('/tomador/poliza/{id}/furat/reportes/table_furat_reportes_view/{npoliza}', 'Services\ComprehensiveAccidentManagementController@table_furat_reportes_view');

    Route::get('/tomador/poliza/{id}/gis/solicitud_reconocimiento_factura/{npoliza}', 'Services\ComprehensiveAccidentManagementController@registerRefund');
    Route::post('/tomador/poliza/{id}/gis/solicitud_reconocimiento_factura_archivos/{npoliza}', 'Services\ComprehensiveAccidentManagementController@registerRefundFile');
    Route::get('/tomador/poliza/{id}/gis/solicitud_reconocimiento_factura_archivos/{npoliza}', 'Services\ComprehensiveAccidentManagementController@registerRefundFile');
    Route::get('/get-gis-affiliate/{id}', 'Services\ComprehensiveAccidentManagementController@getGisAffiliate');
    Route::post('/tomador/poliza/{id}/gis/solicitud_reconocimiento_factura_archivos/{npoliza}/save', 'Services\ComprehensiveAccidentManagementController@saveRefund');
    Route::get('/descargar-xml-ejemplo', 'Services\ReintegrateServicesController@descargarXmlEjemplo');
    Route::get('/tomador/poliza/{id}/gis/seguimiento_solicitud_reintegro/{npoliza}', 'Services\ComprehensiveAccidentManagementController@reimbursementRequestFollowup');

    Route::get('/tomador/poliza/{id}/gis/{table}/{npoliza}', 'Services\ComprehensiveAccidentManagementController@table_gis_view');
    Route::get('/tomador/poliza/{id}/solicitud_reapertura/{gis}/{npoliza}', 'Services\ComprehensiveAccidentManagementController@registerReopenings');
    Route::get('/tomador/poliza/{id}/solicitud_controversia/{gis}/{npoliza}', 'Services\ComprehensiveAccidentManagementController@registerControversies');

    Route::post('/tomador/poliza/solicitud_reapertura/{id}/save', 'Services\ComprehensiveAccidentManagementController@saveReopenings');
    Route::post('/tomador/poliza/solicitud_controversia/{id}/save', 'Services\ComprehensiveAccidentManagementController@saveControversies');

    //gis furat data
    Route::post('/furat/save_affilate_data', 'Services\ComprehensiveAccidentManagementController@form_affilate_data');
    Route::post('/furat/save_employment_relationship_data', 'Services\ComprehensiveAccidentManagementController@form_employment_relationship_data');
    Route::post('/furat/physical_signature/{id}', 'Services\ComprehensiveAccidentManagementController@requestIssuancePhysicalSignature');
    Route::post('/furat/downloadFormatSignature/{id}', 'Services\ComprehensiveAccidentManagementController@downloadFormatSignature');
    Route::get('/furat/getFormatSignature/{id}', 'Services\ComprehensiveAccidentManagementController@getDocumentSignature');
    Route::post('/furat/digital_signature/{id}', 'Services\ComprehensiveAccidentManagementController@requestIssuanceDigitalSignature');
    Route::post('/furat/load_media_furat/{id}', 'Services\ComprehensiveAccidentManagementController@loadMedia');
    Route::get('/furat/reportGis/{id}', 'Services\ComprehensiveAccidentManagementController@reportGisrequest');


    Route::post('/furat/{id}/save-signature', 'Services\ComprehensiveAccidentManagementController@saveSignature');
    Route::get('/furat/{id}/get-signature', 'Services\ComprehensiveAccidentManagementController@getSignature');

    //Gis reporte de accidentes validar afiliado en planilla reportada
    Route::post('/services/gis/validateAffiliate/{id}', 'Services\ComprehensiveAccidentManagementController@validateAffiliate');

    //Gis Guardar los datos reporte de accidente vista tomador
    Route::post('/services/gis/taker/saveDataAffiliate', 'Services\ComprehensiveAccidentManagementController@saveDataAffiliate');

    //Gis Guardar los datos reporte de accidente vista tomador
    Route::post('/services/gis/taker/saveAccident', 'Services\ComprehensiveAccidentManagementController@saveAccident');

    //GIS validar tomador y responsable del Reporte de accidente
    Route::post('/services/gis/validate/taker', 'Services\ComprehensiveAccidentManagementController@validateTaker');

    Route::post('/services/gis/{id}/save', 'Services\ComprehensiveAccidentManagementController@save');

    //Acción reportar monitoreo y control de GIS sort
    Route::POST('/services/gis/report/monitoring/control/{id}', 'Services\ComprehensiveAccidentManagementController@reportMonitoringControl');

    //Acción caso aceptado por excepción 
    Route::get('/services/gis/reportAcceptedCaseException/{id}', 'Services\ComprehensiveAccidentManagementController@reportAcceptedCaseException');

    //GIS acciones y estados 
    //REPORTAR MUERTE DE AFILIADO - RECHAZO
    Route::post('/services/gis/reportMemberDeathRejection/{id}', 'Services\ComprehensiveAccidentManagementController@reportMemberDeathRejection');
    //REPORTAR MUERTE DE AFILIADO - APROBACIÓN
    Route::post('/services/gis/reportMemberDeathApproval/{id}', 'Services\ComprehensiveAccidentManagementController@reportMemberDeathApproval');

    //Reapertura de caso de origen
    Route::get('/services/gis/reopeningOriginCase/{id}', 'Services\ComprehensiveAccidentManagementController@reopeningOriginCase');

    //Reapertura de caso rechazado
    Route::get('/services/gis/reopeningRejectedCase/{id}', 'Services\ComprehensiveAccidentManagementController@reopeningRejectedCase');
    Route::post('/services/gis/reopeningRejectedCase/{id}', 'Services\ComprehensiveAccidentManagementController@reopeningRejectedCase');
    Route::post('/services/gis/approveCaseReopening/{id}', 'Services\ComprehensiveAccidentManagementController@approveCaseReopening');

    Route::post('/services/gis/approveControversies/{id}', 'Services\ComprehensiveAccidentManagementController@approveControversies');
    Route::post('/services/gis/rejectControversyResponse/{id}', 'Services\ComprehensiveAccidentManagementController@rejectControversyResponse');


    //Reapertura de caso pcg 1
    Route::get('/services/gis/reopeningPcg1Case/{id}', 'Services\ComprehensiveAccidentManagementController@reopeningPcg1Case');

    //Reapertura de caso pcg 2
    Route::get('/services/gis/reopeningPcg2Case/{id}', 'Services\ComprehensiveAccidentManagementController@reopeningPcg2Case');

    //acción reportar alta medica 
    Route::get('/services/gis/reportMedicalDischarge/{id}', 'Services\ComprehensiveAccidentManagementController@reportMedicalDischarge');

    //accion reportar auditoria de rechazo
    Route::post('/services/gis/ApproveAuditRejection/{id}', 'Services\ComprehensiveAccidentManagementController@ApproveAuditRejection');
    Route::post('/services/gis/reportAuditRejection/{id}', 'Services\ComprehensiveAccidentManagementController@reportAuditRejection');

    //Rechazar caso aceptado por excepción
    Route::post('/services/gis/ReportCaseExceptionRejected/{id}', 'Services\ComprehensiveAccidentManagementController@ReportCaseExceptionRejected');

    //REPORTAR REAPERTURA DE CASO ADMINISTRATIVA
    Route::post('/services/gis/reportAdministrativeCaseReopening/{activity_id}', 'Services\ComprehensiveAccidentManagementController@reportAdministrativeCaseReopening');

    //Aprobar caso aceptado por excepción
    Route::post('/services/gis/reportAcceptedCaseExceptionApprobal/{id}', 'Services\ComprehensiveAccidentManagementController@reportAcceptedCaseExceptionApprobal');

    //accion reportar auditoria de rechazo sin alta medica 
    Route::get('/services/gis/reportAuditRejectionWithoutMeedicalDischarge/{id}', 'Services\ComprehensiveAccidentManagementController@reportAuditRejectionWithoutMeedicalDischarge');
    //accion REPORTAR AUDITORIA ADMINISTRATIVA - APROBACIÓN
    Route::post('/services/gis/reportAdministrativeAuditApproval/{id}', 'Services\ComprehensiveAccidentManagementController@reportAdministrativeAuditApproval');
    //accion REPORTAR AUDITORIA ADMINISTRATIVA - RECHAZO
    Route::post('/services/gis/reportAdministrativeAuditRejection/{id}', 'Services\ComprehensiveAccidentManagementController@reportAdministrativeAuditRejection');
    //accion SOLICTUD INFORMACIÓN EXTRA
    Route::post('/services/gis/requestExtraInformation/{id}', 'Services\ComprehensiveAccidentManagementController@requestExtraInformation');

    //GIS- FURAT 

    //Tablero tomador FURAT 


    //GIS validar tomador y responsable del Reporte de accidente
    Route::post('/services/gis/furat/saveDataAccidenteFurat', 'Services\ComprehensiveAccidentManagementController@saveDataAccidenteFurat');


    Route::get('/services/gis/furat/tomador', 'Services\ComprehensiveAccidentManagementController@indexFurat');


    //Reporte de Accidente o Enfermedad
    Route::post('/save/update/affiliate_report', 'Services\PolicySortController@createUpdateAffiliate');
    Route::post('/save/include/worker', 'Services\PolicySortController@includeWorkerReport');


    // Reporte planilla
    Route::get('/tomador/poliza/{id}/ingresar_planilla_manualmente/{npoliza}', 'Services\PolicySortController@enterPayrollManually');

    Route::post('/services/report/duplicate/report', 'Services\PolicySortController@duplicateReportPolicy');
    Route::get('/tomador/poliza/{id}/reportar_sin_modificaciones/{npoliza}', 'Services\PolicySortController@reportWithoutModifications');
    Route::get('/tomador/poliza/{id}/modificar_datos_trabajadores/{npoliza}', 'Services\PolicySortController@modifyWorkerData');
    Route::get('/tomador/poliza/{id}/suspend_policy/{npoliza}', 'Services\PolicySortController@suspendPolicyView');
    Route::get('/tomador/poliza/{id}/inclusion_empleado/{npoliza}', 'Services\PolicySortController@includeEmployeView');
    //Constancias de poliza
    Route::get('/tomador/poliza/{id}/certificado_poliza_dia/{npoliza}', 'Services\PolicySortController@policydaycertificate');
    Route::get('/tomador/poliza/{id}/estado_cuenta_trabajador/{npoliza}', 'Services\PolicySortController@workeraccountstatus');
    Route::get('/tomador/poliza/{id}/constancia_sumas_pendientes/{npoliza}', 'Services\PolicySortController@constancyoutstandingamounts');
    Route::get('/tomador/poliza/{id}/constancia_prima_pagada/{npoliza}', 'Services\PolicySortController@constancypremiumpaid');
    //Consulta de polizas
    Route::get('/tomador/poliza/{id}/consultar_planilla/{npoliza}', 'Services\PolicySortController@consultspreadsheet');
    Route::get('/tomador/poliza/consultar_planilla/descargar_documento/{nspreedsheet}', 'Services\PolicySortController@downloadSpreadsheetPdf');
    Route::get('/tomador/poliza/{id}/consultar_siniestralidad/{npoliza}', 'Services\PolicySortController@consultaccidentrate');
    Route::get('/tomador/poliza/{id}/consultar_inclusion/{npoliza}', 'Services\PolicySortController@consultarInclusion');
    Route::get('/tomador/poliza/descargar_tabla_siniestralidad/{npoliza}', 'Services\PolicySortController@downloadExcelConsultAccidentRate');
    //Solicitud de variaciones
    Route::get('/tomador/poliza/{id}/cambio_vigencia_poliza/{npoliza}', 'Services\PolicySortController@policyvaliditychange');
    Route::get('/tomador/poliza/{id}/datos_basicos_contacto/{npoliza}', 'Services\PolicySortController@basiccontactinformation');
    Route::get('/tomador/poliza/{id}/otros_datos/{npoliza}', 'Services\PolicySortController@otherdata');
    Route::get('/tomador/poliza/{id}/cambio_forma_pago/{npoliza}', 'Services\PolicySortController@changepaymentmethod');
    Route::get('/tomador/poliza/{id}/cancelación/{npoliza}', 'Services\PolicySortController@cancellation');
    Route::get('/tomador/poliza/{id}/rehabilitación/{npoliza}', 'Services\PolicySortController@rehabilitation');

    Route::get('/tomador/poliza/{id}/estadisticas_reporte/{npoliza}', 'Services\PolicySortController@statisticsandreporting');
    Route::get('/tomador/poliza/{id}/certificado_aseguramiento/{npoliza}', 'Services\PolicySortController@certificadoAseguramiento');
    Route::post('/certificado_aseguramiento/generar/{npoliza}', 'Services\PolicySortController@emailAsegurado');
    Route::post('/certificado_aseguramiento/generarMassive', 'Services\PolicySortController@sqsCertificateAffiliate');
    Route::post('/certificado_aseguramiento/generarMassiveAffiliate', 'Services\PolicySortController@sqsCertificateAffiliateMassive');
    Route::post('/certificado_aseguramiento/download/{npoliza}', 'Services\PolicySortController@downloadDocumentsAsegurado');

    // Poliza: listado de recibos
    Route::get('/tomador/poliza/{id}/recibos_pendientes/{npoliza}', 'Services\PolicySortCollectionController@pendingReceipts');

    //Accion EMITIR POLIZA En poliza SORT
    Route::post('/poliza/actionIssuePolicy', 'Services\PolicySortController@actionIssuePolicy');

    // Todas las vistas del intermediario (disponibles)
    Route::get('/intermediario/poliza/{id}', function ($cpath, $id) {
        return redirect("/intermediario/poliza/$id/datos_intermediario");
    });
    // Todas las vistas del intermediario poliza (disponibles)
    Route::get('/intermediario/poliza/{id}/datos_intermediario', 'Services\PolicySortController@intermediary');
    Route::get('/intermediario/poliza/{id}/datos_emision', 'Services\PolicySortController@emission');
    Route::get('/intermediario/poliza/{id}/datos_firma', 'Services\PolicySortController@signature');
    Route::post('/intermediario/poliza/{id}/save-signature', 'Services\PolicySortController@saveSignature');
    Route::get('/intermediario/poliza/{id}/get-signature', 'Services\PolicySortController@getSignature');
    Route::get('/intermediario/poliza/{id}/get-document-signature', 'Services\PolicySortController@getDocumentSignature');
    Route::get('/intermediario/poliza/{id}/documentos_externa', 'Services\PolicySortController@documentationView');
    Route::get('/intermediario/poliza/{id}/prima_emision_policy', 'Services\PolicySortController@policyEmission');
    Route::post('/intermediario/poliza/{id}/prima_emision_policy', 'Services\PolicySortController@policyEmission');
    Route::post('/prima_emision_policy/update', 'Services\PolicySortController@updatePolitySort');
    Route::get('/intermediario/poliza/{id}/calculo_prima', 'Services\PolicySortController@premiumCalculation');
    Route::post('/intermediario/poliza/{id}/calculo_prima', 'Services\PolicySortController@premiumCalculation');
    Route::post('/update-currency-type', 'Services\PolicySortController@UpdateCurrencyType');

    //Ruta para la vista de rporte de planillas vista intermediario
    Route::get('/intermediario/poliza/{id}/informe_planilla', 'Services\PolicySortController@viewSpreadsheet');
    Route::get('/intermediario/poliza/{id}/prima_emision', 'Services\PolicySortController@policyEmission');

    //guardar datos intermediario
    Route::post('/intermediario/save', 'Services\PolicySortController@guardarintermediary');

    Route::post('/calculate-premium', 'Integrations\WebserviceAcselController@calculatePremium');

    //Guardar datos de las vistas del intermediario poliza
    Route::post('/intermediario/poliza/{id}/datos_emision/save', 'Services\PolicySortController@emissionSave');
    Route::post('/intermediario/poliza/{id}/datos_emision/upsert_additional_addresses/save', 'Services\PolicySortController@upsertAdditionalAddress');
    Route::delete('/intermediario/poliza/{id}/datos_emision/upsert_additional_addresses/delete/{addressId}', 'Services\PolicySortController@deleteAdditionalAddress');
    Route::post('/intermediario/poliza/{id}/datos_emision/upsert_additional_phones/save', 'Services\PolicySortController@upsertAdditionalPhone');
    Route::delete('/intermediario/poliza/{id}/datos_emision/additional_phones/delete/{phoneId}', 'Services\PolicySortController@deleteAdditionalPhone');
    Route::post('/intermediario/poliza/{id}/datos_emision/upsert_additional_emails/save', 'Services\PolicySortController@upsertAdditionalEmail');
    Route::delete('/intermediario/poliza/{id}/datos_emision/additional_emails/delete/{emailId}', 'Services\PolicySortController@deleteAdditionalEmail');
    Route::get('/poliza/', 'Services\PolicyController@index');
    Route::get('/intermediario/poliza/{id}/pago', 'Services\PolicySortController@paymentPolicyView');
    Route::get('/intermediario/poliza/{id}/pago/resumen', 'Services\PolicySortController@policySummaryView');
    Route::post('/intermediario/cotizacion/startPolicyIssuanceFromQuotation', 'Services\PolicySortController@startPolicyIssuanceFromQuotation');
    Route::post('/servicio/quotation/resend_email_quotation', 'Services\QuotationController@reSendEmail');
    Route::get('/intermediario/formulario/policy', 'Services\PolicySortController@index');
    Route::get('/intermediario/poliza/{id}/callback', 'Services\PolicySortController@handleCallback');
    Route::post('/intermediario/poliza/payment_qa', 'Services\PolicySortController@paymenQa');


    //pdf policy-sort
    Route::post('/intermediario/poliza/{id}/DescargarFormatoFirma', 'Services\PolicySortController@downloadSignature');
    Route::post('/intermediario/poliza/{id}/uploadPhysicalSignatures', 'Services\PolicySortController@uploadPhysicalSignatures');
    Route::post('/intermediario/poliza/{id}/uploadDigitalSignatures', 'Services\PolicySortController@uploadDigitalSignatures');
    Route::post('/intermediario/poliza/{id}/requestIssuanceDigitalSignature', 'Services\PolicySortController@requestIssuanceDigitalSignature');
    Route::get('/intermediario/poliza/{id}/pago/DescargarPolizaSort', 'Services\PolicySortController@downloadPolicySort');
    Route::post('/intermediario/poliza/{id}/requestIssuancePhysicalSignature', 'Services\PolicySortController@requestIssuancePhysicalSignature');

    //Acción REGISTRAR PAGO en el servicio PolicySortController
    Route::get('/poliza/{id}/action-register-payment', 'Services\PolicySortController@actionRegisterPayment');

    // Ruta para simular envio correo
    Route::get('/mail-demo/{email}', 'MailController@sendEmailDemo');

    //Accion REPORTAR_CAMBIO_DE_VIGENCIA En poliza SORT
    Route::post('/poliza/actionChangeDatePolicity', 'Services\PolicySortController@actionChangeDatePolicity');

    // Ruta para subir los afiliados por Excel (Planilla)
    Route::post('/intermediario/formulario/poliza/{id}/store-affiliates-massively', 'Services\PolicySortController@storeAffiliatesMassively');
    //Accion de l apoliza REPORTAR SOLICITUD DE EMISIÓN FIRMADA
    Route::post('/action_report_request/policiy', 'Services\PolicySortController@actionReportRequestEmissionSigned');
    //Cargar los afiliados de planilla por txt
    Route::post('/spreadsheet/{id}/storage-file-txt', 'Services\ReportTakenFormController@storeAffiliatesMassivelyTxt');
    //Generar excel de afiliados
    Route::get('/spreadsheet/{id}/generar-excel', 'Services\ReportTakenFormController@generateExcel');
    Route::get('/spreadsheet/{id}/status', 'Services\ReportTakenFormController@statusExcel');
    Route::get('/excel/validar-descarga/{id}','Services\ReportTakenFormController@validateExcelDownload');

    // Ruta para subir los afiliados por Excel
    Route::post('/intermediario/formulario/poliza/{id}/storage-file', 'Services\PolicySortController@storeAffiliatesMassively');

    // Ruta con AJAX para subir documentos desde la vista del tomador
    Route::post('/tomador/poliza/variaciones/storage-file/{id}', 'Services\PolicySortController@uploadVariationsDocuments');
    Route::post('/tomador/poliza/guardar-planilla', 'Services\PolicySortController@insertNewSheetManually');


    // Ruta para guardar la tabla dinamica de los afiliados (Planilla)
    Route::post('/intermediario/formulario/store-affiliates-manually', 'Services\PolicySortController@storeAffiliatesManually');

    Route::post('/intermediario/formulario/generar-planilla', 'Services\PolicySortController@generateExcelTemplate');

    Route::post('/intermediario/formulario/generateExcelTemplateManual', 'Services\PolicySortController@generateExcelTemplateManual');
    Route::post('/intermediario/formulario/generateReportePlanilla', 'Services\PolicySortController@generateReportSpreadsheet');


    // vista tomador Poliza Sort (Disponibles)
    Route::get('/tomador/poliza-sort/', 'Services\PolicySortController@index');
    Route::get('/tomador/poliza/{id}/datos', 'Services\PolicySortController@policySortData');
    Route::get('/reportar-planilla-definitiva-v2/{id}', 'Services\PolicySortController@reportFinalPayroll');

    Route::get('/formatonuevo/{id}', 'Tables\AfiliateTableController@formatonuevo');


    //ruta para probar reporte planilla afiliado y generar certificado
    Route::post('/servicio/report_affiliates/{id}', 'Services\PolicySortController@report_affiliates');


    //Acciones para el Servicio Poliza Sort
    Route::post('/servicio/poliza-sort/{id}/issue-electronic-billing', 'Services\PolicySortController@issueElectronicBilling');

    // Feedback cargues
    Route::get('/proceso-cargues/{id}', 'MassiveController@feedback');
    // rutas Servicio PE IT-SORT
    Route::get('/servicio/{id}/pe_it_sort', 'Services\PeItSortController@form');
    Route::post('/servicio/{id}/pe_it_sort/save', 'Services\PeItSortController@save');
    Route::post('/servicio/{id}/pe_it_sort/register_it_manual', 'Services\PeItSortController@registerItManual');
    Route::post('/servicio/{id}/pe_it_sort/report_full_disability_payment', 'Services\PeItSortController@reportFullDisabilityPayment');
    Route::delete('/servicio/{id}/pe_it_sort/delete', 'Services\PeItSortController@deleteCaseDx');
    Route::post('/servicio/{id}/pe_it_sort/aprobar_servicio', 'Services\PeItSortController@approveService');
    Route::post('/servicio/{id}/pe_it_sort/reportar_pago_fracciones_mensual', 'Services\PeItSortController@calculateAndSaveFraccionamientos');
    Route::post('/servicio/{id}/pe_it_sort/upload_file', 'Services\PeItSortController@uploadFile');
    Route::post('/servicio/{id}/pe_it_sort/rechazar_it', 'Services\PeItSortController@rejectService');
    //Actualizar el campo de ajuste incapacidad temporal en fracciones
    Route::get('/servicio/{id}/actualizar_fraccionamiento', 'Services\PeItSortController@AdjustmentInFractionsPeit');

    //Rechazar ajuste incapacidad temporal
    Route::post('/services/pe_it_sort/rejectedAjustIncapacity/{id}', 'Services\PeItSortController@rejectedAjustIncapacity');
    //Aprobar ajuste incapacidad temporal
    Route::post('/services/pe_it_sort/approvedAjustIncapacity/{id}', 'Services\PeItSortController@approvedAjustIncapacity');


    //Rutas Servicio PE MPT-Sort
    Route::get('/servicio/{id}/pe_mpt_sort', 'Services\PeMptSortController@form');
    Route::post('/servicio/{id}/pe_mpt_sort/save', 'Services\PeMptSortController@save');
    Route::get('/servicio/{id}/pe_it_sort/emitir', 'Services\PeItSortController@EmitirIncapacidadMedica');
    Route::get('/servicio/{id}/pe_it_sort/report', 'Services\PeItSortController@reportarValidacionDocumental');

    //Rutas Servicio PE Reconocimiento gastos
    Route::get('/servicio/{id}/pe_recognition_expenses', 'Services\PeRecognitionExpensesController@form');
    Route::post('/servicio/{id}/pe_recognition_expenses/save', 'Services\PeRecognitionExpensesController@save');
    Route::get('/servicio/{id}/report_funeral_And_transport_expenses', 'Services\PeRecognitionExpensesController@reportFuneralAndTransportExpenses');
    Route::get('/get-data-case-gis/{id}', 'Services\PeRecognitionExpensesController@getDatacase');

    //Rutas Servicio Pagos de afiliados
    Route::get('/servicio/{id}/affiliate_payment', 'Services\AffiliatePaymentController@form');
    Route::post('/servicio/{id}/affiliate_payment/save', 'Services\AffiliatePaymentController@save');
    Route::post('/servicio/{id}/affiliate_payment/generatePdf', 'Services\AffiliatePaymentController@generatePdf')->name('affiliate.download_pdf');
    Route::post('/payments', 'Services\AffiliatePaymentController@receivePaymentSimulator'); // Recibir el pago
    Route::patch('/payments/{id}', 'Services\AffiliatePaymentController@updatePaymentSimulatorStatus'); // Actualizar estado
    Route::get('/payments', 'Services\AffiliatePaymentController@getPaymentsSimulator'); // Obtener todos los pagos
    Route::post('/servicio/{id}/report_payment_it', 'Services\AffiliatePaymentController@reportPaymentIt');

    Route::get('/servicio/{id}/quotation/generateActionCondition', 'Services\QuotationController@generateActionCondition');

    Route::post('/servicio/{id}/quotation/saveActionConditionSpecial', 'Services\QuotationController@saveActionConditionSpecial');

    //GIS Signature (gestión de firmas GIS)
    Route::post('/gis/reapertura/firma/{id}/save-signature', 'GisSignatureController@saveSignature');

    //subrogación
    Route::get('/servicio/{id}/subrogacion', 'Services\SubrogacionSortController@form');

    Route::get('api/users', function () {
        $users = User::where('has_schedule', 1)->get()->map(function ($user) {
            return [
                'email' => $user->email,
                'full_name' => strtoupper($user->first_name . ' ' . $user->last_name),
                'photo' => $user->photo,
                'signature' => $user->signature,
                'rm' => $user->medical_record_number,
                'license' => $user->license_number,
                'renu' => $user->rethus,
                'doc_number' => $user->identification_number,
                'doc_type' => $user->doc_type,
            ];
        });
        return $users;
    });

    // Rutas servicio PE IP - SORT
    Route::get('/servicio/{id}/pe_ip_sort', 'Services\PeIpSortController@form');
    Route::post('/servicio/{id}/pe_ip_sort/save', 'Services\PeIpSortController@save');
    Route::delete('/servicio/{id}/pe_ip_sort/delete', 'Services\PeIpSortController@deleteCaseDx');
    Route::post('/servicio/{id}/pe_ip_sort/rechazar_ip', 'Services\PeIpSortController@rejectService');


    Route::prefix('api')->middleware('auth')->group(function () {
        Route::get('/glosa/invoice/{number}', 'Services\GlosaController@invoice');
        Route::get('/invoice/{number}', 'AdminController@bill_api');
        Route::get('/docinvoice/{number}', 'AdminController@bill_doc_api');
        Route::get('/municipality_ips/{department}', 'ServiceController@getMunicipalities');
        Route::get('/name_ips/{department}/{municipality}', 'ServiceController@getNameIPS');
        Route::get('/service/{aid}/action/{id}/fields', function ($cpath, $aid, $id) {
            $activity = Activity::findOrFail($aid);
            $action = Action::findOrFail($id);
            $fields = ActionField::where('action_id', $id)->get();

            foreach ($fields as $field) {
                if ($field->type == 'email' && $action->send_targets) {
                    $emails = array();
                    foreach (explode(',', $action->send_targets) as $target) {
                        if ($target == 'affiliate') {
                            $emails[] = $activity->affiliate->email;
                        }
                        if ($target == 'employer') {
                            if ($activity->employment && $activity->employment->branch) {
                                $emails[] = $activity->employment->branch->email;
                            }
                        }
                    }
                }
            }

            return $fields;
        });
        Route::get('/determination_it/{id}/', function ($cpath, $id) {
            $determination_it = DeterminationIt::query()
                ->where('activity_id', $id)
                ->first();

            return response()->json($determination_it);
        });
        Route::get('/action/{id}/documents', function ($cpath, $id) {
            $docs = explode(',', Action::find($id)->documents);
            $result = [];
            foreach ($docs as $doc) {
                if (isset(SendDocument::$DOCUMENTS[$doc])) {
                    $result[$doc] = SendDocument::$DOCUMENTS[$doc];
                }
            }
            return $result;
        });
        //        Route::get('/action/{id}', function ($cpath, $id) {
        //            $action = Action::find($id);
        //            $action->auto_alert_value = $action->getAutoAlertValueAttribute();
        //            return $action;
        //        });
        Route::get('/activity/{activity}/action/{action}/users', function ($cpath, App\Activity $activity, App\Action $action) {
            if ($action->getStateId($activity->service_id)) {
                $areas = $action->getState($activity->service_id)->state->areas_ids();
            } else {
                $areas = $activity->state->areas_ids();
            }

            $users = User::whereIn('area_id', $areas)
                ->where('active', 1)
                ->with('area');

            //BURNED USERS

            $users = $users->get();
            return $users;
        });
        Route::post('/activity/{activity}', function (Request $req, $cpath, App\Activity $activity) {
            $activity->information_source = $req->input('information_source');
            $activity->auth_number = $req->input('auth_number');
            $activity->employment_id = $req->input('employment_id');

            if ($req->input('fields')) {
                foreach ($req->input('fields') as $id => $value) {
                    $activity->setField($id, $value);
                }
            }

            if ($activity->save()) {
                return 'OK';
            } else {
                return 'ERROR';
            }
        });
        Route::get('employer/{nit}', function ($cpath, $nit) {
            return Employer::where('nit', $nit)->with('branches')->get();
        });
        Route::get('affiliates/{query?}', function ($cpath, $query = '') {
            $client = Client::where('path', $cpath)->firstOrFail();

            $affiliates = Affiliate::where('client_id', $client->id)
                ->where(function ($q) use ($query) {
                    $q->where('first_name', 'like', $query . '%')
                        ->orWhere('last_name', 'like', $query . '%')
                        ->orWhere('doc_number', 'like', $query . '%');
                })
                ->select('id', 'client_id', 'doc_type', 'doc_number', 'first_name', 'last_name')
                ->orderBy('first_name')
                ->limit(50)
                ->get();

            $result = new stdClass;
            $result->success = true;
            $result->results = array();

            foreach ($affiliates as $a) {
                $item = new stdClass;
                $item->name = "{$a->full_name} - {$a->doc_type} {$a->doc_number}";
                $item->value = $a->id;
                $item->text = $a->full_name;

                $result->results[] = $item;
            }

            return response()->json($result);
        });

        Route::get('activities_economic/{query?}', function ($cpath, $query = '') {

            $econmic_activities = EconomicActivity::where(function ($q) use ($query) {
                $q->where('code', 'like', $query . '%')
                    ->orWhere('activity_name', 'like', $query . '%');
            })
                ->select('code', 'activity_name')
                ->limit(50)
                ->get();

            $result = new stdClass;
            $result->success = true;
            $result->results = array();

            foreach ($econmic_activities as $a) {
                $item = new stdClass;
                $item->name = "{$a->code} - {$a->activity_name}";
                $item->value = $a->code;
                $item->text = $a->activity_name;

                $result->results[] = $item;
            }

            return response()->json($result);
        });

        Route::get('employers/{query?}', function ($cpath, $query = '') {
            $client = Client::where('path', $cpath)->firstOrFail();

            $employers = Employer::where(function ($q) use ($query) {
                $q->where('nit', 'like', $query . '%')
                    ->orWhere('name', 'like', $query . '%');
            })
                ->orderBy('name')
                ->limit(50)
                ->get();

            $result = new stdClass;
            $result->success = true;
            $result->results = array();

            foreach ($employers as $e) {
                $item = new stdClass;
                $item->name = "{$e->nit} - {$e->name}";
                $item->value = $e->id;
                $item->text = $e->name;

                $result->results[] = $item;
            }

            return response()->json($result);
        });
        Route::get('information_sources/{query?}', function ($cpath, $query = '') {
            $client = Client::where('path', $cpath)->firstOrFail();
            $information_sources = \App\InformationSource::query()->where('client_id', $client->id)
                ->where(function ($q) use ($query) {
                    $q->where('name', 'like', $query . '%');
                })
                ->orderBy('name')
                ->limit(50)
                ->get();
            $result = new stdClass;
            $result->success = true;
            $result->results = array();

            foreach ($information_sources as $e) {
                $item = new stdClass;
                $item->name = "{$e->name} - {$e->service->name}";
                $item->value = $e->id;
                $item->text = $e->name;

                $result->results[] = $item;
            }

            return response()->json($result);
        });
        Route::get('radicates_positiva/{query?}', function ($cpath, $query = '') {
            $client = Client::where('path', $cpath)->firstOrFail();

            $radicates = \App\ActivityServiceField::where('value', 'like', $query . '%')
                ->select('id', 'activity_id', 'value')
                ->orderBy('id')
                ->limit(50)
                ->get();

            $result = new stdClass;
            $result->success = true;
            $result->results = array();

            foreach ($radicates as $a) {
                $item = new stdClass;
                $item->name = "{$a->value} - {$a->activity->affiliate->doc_type} {$a->activity->affiliate->doc_number}";
                $item->value = "{$a->activity_id}";
                $item->text = "{$a->value}";

                $result->results[] = $item;
            }

            return response()->json($result);
        });
    });
});

// Routa para el webservice de colpensiones
Route::match(['get', 'post'], '/IntegrationService', 'Integrations\IntegrationServiceController@handleRequest');
Route::get(
    '/ColpensionesBPM',
    'Integrations\SoapRequest\ColpensionesBPMController@RecibirEstadoRevisionDocum'
);

Route::get('/holidays', function () {
    // Devuelve todas las fechas de los días festivos y sus correspondientes service_id
    return response()->json(Holiday::all(['holiday', 'service_id']));
});
Route::post('/service/check-idbizagi', 'ServiceController@checkIdBizagi');

//MIGRATION COLP
Route::get('/process-duplicates/{id}', 'MigrateColpController@migrateAffiliates');
Route::get('/fix_affiliate_it/{id}', 'MigrateColpController@fixAffiliateIts');
Route::get('/pcl_to_rei/{id}', 'MigrateColpController@migratePclToReiActivities');

Route::get('/update_affiliate_emails/{id}', 'MigrateColpController@updateAffiliateEmails');

Route::get('/pcl_to_rei_by_bizagi/{id}', 'MigrateColpController@migratePclToReiByBizagiActivities');
Route::get('/rei_to_pcl_by_bizagi/{id}', 'MigrateColpController@migrateReiToPclByBizagiActivities');

Route::get('/replaceTextInColumnsHistorialAcciones', 'ActionController@replaceTextAreas');
//PCL
Route::get('/replaceTextInColumns', 'Services\PCLController@replaceTextAreas');
Route::get('/replaceTextInColumnsRelations', 'Services\PCLController@replaceTextAreasRelations');
//REI
Route::get('/replaceTextInColumnsRei', 'Services\InvalidityStateP4Controller@replaceTextAreas');
Route::get('/replaceTextInColumnsRelationsRei', 'Services\InvalidityStateP4Controller@replaceTextAreasRelations');
//it
Route::get('/replace_banks_code', 'Services\DeterminationItController@replaceBanks');
//MDI
Route::get('/expression_disagreement/migrate/{id_pcl}/{id_exp_disa}', 'Services\ExpressionDisagreementController@migratePclInfo');

Route::get('/reportPaymentControl/{doc_type}/{doc_number}', 'ExcelController@reportPaymentControlItExcel')->name('reportPaymentControlIt');

// Ruta para el webservice ascel simulado
Route::get('/webservice-acsel', 'Integrations\WebserviceAcselController@getDataIntermediary');
Route::get('/webservice-credid', 'Integrations\WebserviceAcselController@getDataIntermediaryCredid');

Route::get('/encuesta/{id}/mnk/{type}', 'SurveyController@index');
Route::get('/encuesta/{id}/save', 'SurveyController@save');

//prueba de jwt y api acel
Route::get('/jwt-api', 'Integrations\WebserviceAcselController@showJwt');
Route::get('/get-trm', 'Integrations\WebserviceAcselController@getTrm');
Route::get('/envio-email', 'Integrations\WebserviceAcselController@envioEmail');
Route::get('/updataDataUsers/{conca?}', 'Integrations\WebserviceAcselController@updataDataUsers');

Route::get('/envio-factura/{id}', 'Integrations\WebserviceElectronicInvoiceController@sendInvoice');

Route::get('/reportUnpaidRenewalReceipt', 'Services\RenewalSortController@reportUnpaidRenewalReceipt');
Route::get('/pruebaPDF/{id}', 'Services\LiquidationPolicyController@pruebaPDF');
Route::get('/pagofacturaprueba/{id}', 'Services\PolicySortController@pagofacturaprueba');
Route::get('/sendInvoice/{id}', 'Services\PolicySortController@pagofacturaprueba');
Route::get('/report_account_case_308', 'Services\AccountingEntryController@autoAccountCase308');
Route::get('send_email_policy/{id}', 'Services\PolicySortController@sendEmailPolicy');
Route::get('ejecutaAsiento011', 'Services\AccountingEntryController@ejecutaAsiento011');
Route::get('generaAsientos003', 'Services\AccountingEntryController@generaAsientos003');
Route::get('generaAsiento002/{id}', 'Services\AccountingEntryController@generaAsiento002');

Route::get('validaDataLiquitation/{id}', 'Services\LiquidationPolicyController@validaDataLiquitation');
Route::get('generateAccountingEntry/{id}', 'Services\PolicySortController@manualGenerateAccountingEntry');
