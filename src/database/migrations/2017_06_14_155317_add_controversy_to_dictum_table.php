<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddControversyToDictumTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('dicta', function (Blueprint $table) {
            $table->date('execution_date')->nullable();
            $table->date('jr_dictum_date')->nullable();
            $table->date('jn_dictum_date')->nullable();
            $table->text('observation')->nullable();
            $table->string('jr_dictum_number')->nullable();
            $table->string('jr_bill_number')->nullable();
        });
        Schema::table('dictum_diagnostics', function (Blueprint $table) {
            $table->string('controversy_author')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('dicta', function (Blueprint $table) {
            $table->dropColumn([
                'execution_date',
                'jr_dictum_date',
                'jn_dictum_date',
                'observation',
                'jr_dictum_number',
                'jr_bill_number',
            ]);
        });
        Schema::table('dictum_diagnostics', function (Blueprint $table) {
            $table->dropColumn(['controversy_author']);
        });
    }
}
