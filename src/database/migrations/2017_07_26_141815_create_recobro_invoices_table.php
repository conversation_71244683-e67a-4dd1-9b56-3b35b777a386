<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateRecobroInvoicesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('recobro_invoices', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('recobro_id');
            $table->string('number')->nullable();
            $table->float('value')->nullable();
            $table->string('cod')->nullable();
            $table->string('description')->nullable();
            $table->string('ips_type')->nullable();
            $table->float('quantity')->nullable();
            $table->float('unit_value')->nullable();
            $table->string('radication_num')->nullable();
            $table->date('atenttion_date')->nullable();
            $table->string('mapiiss_code')->nullable();
            $table->string('ips_nit')->nullable();
            $table->string('ips_name')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('recobro_invoices');
    }
}
