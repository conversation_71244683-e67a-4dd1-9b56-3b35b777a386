<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddControversydataToDictaTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('dicta', function (Blueprint $table) {
            $table->string('eid')->nullable();
            $table->date('controversy_eps_radication')->nullable();
        });
        Schema::table('actions', function (Blueprint $table) {
            $table->string('required_list_documents')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('dicta', function (Blueprint $table) {
            $table->dropColumn(['eid', 'controversy_eps_radication']);
        });
        Schema::table('actions', function (Blueprint $table) {
            $table->dropColumn(['required_list_documents']);
        });
    }
}
