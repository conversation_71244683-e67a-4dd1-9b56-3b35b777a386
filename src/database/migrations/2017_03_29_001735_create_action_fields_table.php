<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateActionFieldsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('action_fields', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('action_id');
            $table->string('type');
            $table->string('name');
            $table->text('values')->nullable();
            $table->boolean('required')->default(false);
            $table->boolean('a2af')->default(false);
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('action_fields');
    }
}
