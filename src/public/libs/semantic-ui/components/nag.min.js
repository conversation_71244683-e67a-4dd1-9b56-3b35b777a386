/*!
 * # Semantic UI 2.2.9 - Nag
 * http://github.com/semantic-org/semantic-ui/
 *
 *
 * Released under the MIT license
 * http://opensource.org/licenses/MIT
 *
 */
!function(e,o,t,n){"use strict";o="undefined"!=typeof o&&o.Math==Math?o:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")(),e.fn.nag=function(t){var i,s=e(this),a=s.selector||"",r=(new Date).getTime(),l=[],c=arguments[0],g="string"==typeof c,u=[].slice.call(arguments,1);return s.each(function(){var s,d=e.isPlainObject(t)?e.extend(!0,{},e.fn.nag.settings,t):e.extend({},e.fn.nag.settings),m=(d.className,d.selector),f=d.error,p=d.namespace,h="."+p,b=p+"-module",v=e(this),y=(v.find(m.close),e(d.context?d.context:"body")),k=this,S=v.data(b);o.requestAnimationFrame||o.mozRequestAnimationFrame||o.webkitRequestAnimationFrame||o.msRequestAnimationFrame||function(e){setTimeout(e,0)};s={initialize:function(){s.verbose("Initializing element"),v.on("click"+h,m.close,s.dismiss).data(b,s),d.detachable&&v.parent()[0]!==y[0]&&v.detach().prependTo(y),d.displayTime>0&&setTimeout(s.hide,d.displayTime),s.show()},destroy:function(){s.verbose("Destroying instance"),v.removeData(b).off(h)},show:function(){s.should.show()&&!v.is(":visible")&&(s.debug("Showing nag",d.animation.show),"fade"==d.animation.show?v.fadeIn(d.duration,d.easing):v.slideDown(d.duration,d.easing))},hide:function(){s.debug("Showing nag",d.animation.hide),"fade"==d.animation.show?v.fadeIn(d.duration,d.easing):v.slideUp(d.duration,d.easing)},onHide:function(){s.debug("Removing nag",d.animation.hide),v.remove(),d.onHide&&d.onHide()},dismiss:function(e){d.storageMethod&&s.storage.set(d.key,d.value),s.hide(),e.stopImmediatePropagation(),e.preventDefault()},should:{show:function(){return d.persist?(s.debug("Persistent nag is set, can show nag"),!0):s.storage.get(d.key)!=d.value.toString()?(s.debug("Stored value is not set, can show nag",s.storage.get(d.key)),!0):(s.debug("Stored value is set, cannot show nag",s.storage.get(d.key)),!1)}},get:{storageOptions:function(){var e={};return d.expires&&(e.expires=d.expires),d.domain&&(e.domain=d.domain),d.path&&(e.path=d.path),e}},clear:function(){s.storage.remove(d.key)},storage:{set:function(t,i){var a=s.get.storageOptions();if("localstorage"==d.storageMethod&&o.localStorage!==n)o.localStorage.setItem(t,i),s.debug("Value stored using local storage",t,i);else if("sessionstorage"==d.storageMethod&&o.sessionStorage!==n)o.sessionStorage.setItem(t,i),s.debug("Value stored using session storage",t,i);else{if(e.cookie===n)return void s.error(f.noCookieStorage);e.cookie(t,i,a),s.debug("Value stored using cookie",t,i,a)}},get:function(t,i){var a;return"localstorage"==d.storageMethod&&o.localStorage!==n?a=o.localStorage.getItem(t):"sessionstorage"==d.storageMethod&&o.sessionStorage!==n?a=o.sessionStorage.getItem(t):e.cookie!==n?a=e.cookie(t):s.error(f.noCookieStorage),"undefined"!=a&&"null"!=a&&a!==n&&null!==a||(a=n),a},remove:function(t){var i=s.get.storageOptions();"localstorage"==d.storageMethod&&o.localStorage!==n?o.localStorage.removeItem(t):"sessionstorage"==d.storageMethod&&o.sessionStorage!==n?o.sessionStorage.removeItem(t):e.cookie!==n?e.removeCookie(t,i):s.error(f.noStorage)}},setting:function(o,t){if(s.debug("Changing setting",o,t),e.isPlainObject(o))e.extend(!0,d,o);else{if(t===n)return d[o];e.isPlainObject(d[o])?e.extend(!0,d[o],t):d[o]=t}},internal:function(o,t){if(e.isPlainObject(o))e.extend(!0,s,o);else{if(t===n)return s[o];s[o]=t}},debug:function(){!d.silent&&d.debug&&(d.performance?s.performance.log(arguments):(s.debug=Function.prototype.bind.call(console.info,console,d.name+":"),s.debug.apply(console,arguments)))},verbose:function(){!d.silent&&d.verbose&&d.debug&&(d.performance?s.performance.log(arguments):(s.verbose=Function.prototype.bind.call(console.info,console,d.name+":"),s.verbose.apply(console,arguments)))},error:function(){d.silent||(s.error=Function.prototype.bind.call(console.error,console,d.name+":"),s.error.apply(console,arguments))},performance:{log:function(e){var o,t,n;d.performance&&(o=(new Date).getTime(),n=r||o,t=o-n,r=o,l.push({Name:e[0],Arguments:[].slice.call(e,1)||"",Element:k,"Execution Time":t})),clearTimeout(s.performance.timer),s.performance.timer=setTimeout(s.performance.display,500)},display:function(){var o=d.name+":",t=0;r=!1,clearTimeout(s.performance.timer),e.each(l,function(e,o){t+=o["Execution Time"]}),o+=" "+t+"ms",a&&(o+=" '"+a+"'"),(console.group!==n||console.table!==n)&&l.length>0&&(console.groupCollapsed(o),console.table?console.table(l):e.each(l,function(e,o){console.log(o.Name+": "+o["Execution Time"]+"ms")}),console.groupEnd()),l=[]}},invoke:function(o,t,a){var r,l,c,g=S;return t=t||u,a=k||a,"string"==typeof o&&g!==n&&(o=o.split(/[\. ]/),r=o.length-1,e.each(o,function(t,i){var a=t!=r?i+o[t+1].charAt(0).toUpperCase()+o[t+1].slice(1):o;if(e.isPlainObject(g[a])&&t!=r)g=g[a];else{if(g[a]!==n)return l=g[a],!1;if(!e.isPlainObject(g[i])||t==r)return g[i]!==n?(l=g[i],!1):(s.error(f.method,o),!1);g=g[i]}})),e.isFunction(l)?c=l.apply(a,t):l!==n&&(c=l),e.isArray(i)?i.push(c):i!==n?i=[i,c]:c!==n&&(i=c),l}},g?(S===n&&s.initialize(),s.invoke(c)):(S!==n&&S.invoke("destroy"),s.initialize())}),i!==n?i:this},e.fn.nag.settings={name:"Nag",silent:!1,debug:!1,verbose:!1,performance:!0,namespace:"Nag",persist:!1,displayTime:0,animation:{show:"slide",hide:"slide"},context:!1,detachable:!1,expires:30,domain:!1,path:"/",storageMethod:"cookie",key:"nag",value:"dismiss",error:{noCookieStorage:"$.cookie is not included. A storage solution is required.",noStorage:"Neither $.cookie or store is defined. A storage solution is required for storing state",method:"The method you called is not defined."},className:{bottom:"bottom",fixed:"fixed"},selector:{close:".close.icon"},speed:500,easing:"easeOutQuad",onHide:function(){}},e.extend(e.easing,{easeOutQuad:function(e,o,t,n,i){return-n*(o/=i)*(o-2)+t}})}(jQuery,window,document);