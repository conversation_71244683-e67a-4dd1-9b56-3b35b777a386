/*!
 * # Semantic UI 2.2.9 - Shape
 * http://github.com/semantic-org/semantic-ui/
 *
 *
 * Released under the MIT license
 * http://opensource.org/licenses/MIT
 *
 */
!function(e,t,i,n){"use strict";t="undefined"!=typeof t&&t.Math==Math?t:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")(),e.fn.shape=function(a){var o,r=e(this),s=(e("body"),(new Date).getTime()),d=[],l=arguments[0],u="string"==typeof l,c=[].slice.call(arguments,1),g=t.requestAnimationFrame||t.mozRequestAnimationFrame||t.webkitRequestAnimationFrame||t.msRequestAnimationFrame||function(e){setTimeout(e,0)};return r.each(function(){var t,f,m,p=r.selector||"",h=e.isPlainObject(a)?e.extend(!0,{},e.fn.shape.settings,a):e.extend({},e.fn.shape.settings),v=h.namespace,b=h.selector,x=h.error,y=h.className,S="."+v,w="module-"+v,C=e(this),z=C.find(b.sides),W=C.find(b.side),F=!1,H=this,T=C.data(w);m={initialize:function(){m.verbose("Initializing module for",H),m.set.defaultSide(),m.instantiate()},instantiate:function(){m.verbose("Storing instance of module",m),T=m,C.data(w,T)},destroy:function(){m.verbose("Destroying previous module for",H),C.removeData(w).off(S)},refresh:function(){m.verbose("Refreshing selector cache for",H),C=e(H),z=e(this).find(b.shape),W=e(this).find(b.side)},repaint:function(){m.verbose("Forcing repaint event");var e=z[0]||i.createElement("div");e.offsetWidth},animate:function(e,i){m.verbose("Animating box with properties",e),i=i||function(e){m.verbose("Executing animation callback"),e!==n&&e.stopPropagation(),m.reset(),m.set.active()},h.beforeChange.call(f[0]),m.get.transitionEvent()?(m.verbose("Starting CSS animation"),C.addClass(y.animating),z.css(e).one(m.get.transitionEvent(),i),m.set.duration(h.duration),g(function(){C.addClass(y.animating),t.addClass(y.hidden)})):i()},queue:function(e){m.debug("Queueing animation of",e),z.one(m.get.transitionEvent(),function(){m.debug("Executing queued animation"),setTimeout(function(){C.shape(e)},0)})},reset:function(){m.verbose("Animating states reset"),C.removeClass(y.animating).attr("style","").removeAttr("style"),z.attr("style","").removeAttr("style"),W.attr("style","").removeAttr("style").removeClass(y.hidden),f.removeClass(y.animating).attr("style","").removeAttr("style")},is:{complete:function(){return W.filter("."+y.active)[0]==f[0]},animating:function(){return C.hasClass(y.animating)}},set:{defaultSide:function(){t=C.find("."+h.className.active),f=t.next(b.side).length>0?t.next(b.side):C.find(b.side).first(),F=!1,m.verbose("Active side set to",t),m.verbose("Next side set to",f)},duration:function(e){e=e||h.duration,e="number"==typeof e?e+"ms":e,m.verbose("Setting animation duration",e),(h.duration||0===h.duration)&&z.add(W).css({"-webkit-transition-duration":e,"-moz-transition-duration":e,"-ms-transition-duration":e,"-o-transition-duration":e,"transition-duration":e})},currentStageSize:function(){var e=C.find("."+h.className.active),t=e.outerWidth(!0),i=e.outerHeight(!0);C.css({width:t,height:i})},stageSize:function(){var e=C.clone().addClass(y.loading),t=e.find("."+h.className.active),i=F?e.find(b.side).eq(F):t.next(b.side).length>0?t.next(b.side):e.find(b.side).first(),n="next"==h.width?i.outerWidth(!0):"initial"==h.width?C.width():h.width,a="next"==h.height?i.outerHeight(!0):"initial"==h.height?C.height():h.height;t.removeClass(y.active),i.addClass(y.active),e.insertAfter(C),e.remove(),"auto"!=h.width&&(C.css("width",n+h.jitter),m.verbose("Specifying width during animation",n)),"auto"!=h.height&&(C.css("height",a+h.jitter),m.verbose("Specifying height during animation",a))},nextSide:function(e){F=e,f=W.filter(e),F=W.index(f),0===f.length&&(m.set.defaultSide(),m.error(x.side)),m.verbose("Next side manually set to",f)},active:function(){m.verbose("Setting new side to active",f),W.removeClass(y.active),f.addClass(y.active),h.onChange.call(f[0]),m.set.defaultSide()}},flip:{up:function(){if(m.is.complete()&&!m.is.animating()&&!h.allowRepeats)return void m.debug("Side already visible",f);if(m.is.animating())m.queue("flip up");else{m.debug("Flipping up",f);var e=m.get.transform.up();m.set.stageSize(),m.stage.above(),m.animate(e)}},down:function(){if(m.is.complete()&&!m.is.animating()&&!h.allowRepeats)return void m.debug("Side already visible",f);if(m.is.animating())m.queue("flip down");else{m.debug("Flipping down",f);var e=m.get.transform.down();m.set.stageSize(),m.stage.below(),m.animate(e)}},left:function(){if(m.is.complete()&&!m.is.animating()&&!h.allowRepeats)return void m.debug("Side already visible",f);if(m.is.animating())m.queue("flip left");else{m.debug("Flipping left",f);var e=m.get.transform.left();m.set.stageSize(),m.stage.left(),m.animate(e)}},right:function(){if(m.is.complete()&&!m.is.animating()&&!h.allowRepeats)return void m.debug("Side already visible",f);if(m.is.animating())m.queue("flip right");else{m.debug("Flipping right",f);var e=m.get.transform.right();m.set.stageSize(),m.stage.right(),m.animate(e)}},over:function(){return!m.is.complete()||m.is.animating()||h.allowRepeats?void(m.is.animating()?m.queue("flip over"):(m.debug("Flipping over",f),m.set.stageSize(),m.stage.behind(),m.animate(m.get.transform.over()))):void m.debug("Side already visible",f)},back:function(){return!m.is.complete()||m.is.animating()||h.allowRepeats?void(m.is.animating()?m.queue("flip back"):(m.debug("Flipping back",f),m.set.stageSize(),m.stage.behind(),m.animate(m.get.transform.back()))):void m.debug("Side already visible",f)}},get:{transform:{up:function(){var e={y:-((t.outerHeight(!0)-f.outerHeight(!0))/2),z:-(t.outerHeight(!0)/2)};return{transform:"translateY("+e.y+"px) translateZ("+e.z+"px) rotateX(-90deg)"}},down:function(){var e={y:-((t.outerHeight(!0)-f.outerHeight(!0))/2),z:-(t.outerHeight(!0)/2)};return{transform:"translateY("+e.y+"px) translateZ("+e.z+"px) rotateX(90deg)"}},left:function(){var e={x:-((t.outerWidth(!0)-f.outerWidth(!0))/2),z:-(t.outerWidth(!0)/2)};return{transform:"translateX("+e.x+"px) translateZ("+e.z+"px) rotateY(90deg)"}},right:function(){var e={x:-((t.outerWidth(!0)-f.outerWidth(!0))/2),z:-(t.outerWidth(!0)/2)};return{transform:"translateX("+e.x+"px) translateZ("+e.z+"px) rotateY(-90deg)"}},over:function(){var e={x:-((t.outerWidth(!0)-f.outerWidth(!0))/2)};return{transform:"translateX("+e.x+"px) rotateY(180deg)"}},back:function(){var e={x:-((t.outerWidth(!0)-f.outerWidth(!0))/2)};return{transform:"translateX("+e.x+"px) rotateY(-180deg)"}}},transitionEvent:function(){var e,t=i.createElement("element"),a={transition:"transitionend",OTransition:"oTransitionEnd",MozTransition:"transitionend",WebkitTransition:"webkitTransitionEnd"};for(e in a)if(t.style[e]!==n)return a[e]},nextSide:function(){return t.next(b.side).length>0?t.next(b.side):C.find(b.side).first()}},stage:{above:function(){var e={origin:(t.outerHeight(!0)-f.outerHeight(!0))/2,depth:{active:f.outerHeight(!0)/2,next:t.outerHeight(!0)/2}};m.verbose("Setting the initial animation position as above",f,e),z.css({transform:"translateZ(-"+e.depth.active+"px)"}),t.css({transform:"rotateY(0deg) translateZ("+e.depth.active+"px)"}),f.addClass(y.animating).css({top:e.origin+"px",transform:"rotateX(90deg) translateZ("+e.depth.next+"px)"})},below:function(){var e={origin:(t.outerHeight(!0)-f.outerHeight(!0))/2,depth:{active:f.outerHeight(!0)/2,next:t.outerHeight(!0)/2}};m.verbose("Setting the initial animation position as below",f,e),z.css({transform:"translateZ(-"+e.depth.active+"px)"}),t.css({transform:"rotateY(0deg) translateZ("+e.depth.active+"px)"}),f.addClass(y.animating).css({top:e.origin+"px",transform:"rotateX(-90deg) translateZ("+e.depth.next+"px)"})},left:function(){var e={active:t.outerWidth(!0),next:f.outerWidth(!0)},i={origin:(e.active-e.next)/2,depth:{active:e.next/2,next:e.active/2}};m.verbose("Setting the initial animation position as left",f,i),z.css({transform:"translateZ(-"+i.depth.active+"px)"}),t.css({transform:"rotateY(0deg) translateZ("+i.depth.active+"px)"}),f.addClass(y.animating).css({left:i.origin+"px",transform:"rotateY(-90deg) translateZ("+i.depth.next+"px)"})},right:function(){var e={active:t.outerWidth(!0),next:f.outerWidth(!0)},i={origin:(e.active-e.next)/2,depth:{active:e.next/2,next:e.active/2}};m.verbose("Setting the initial animation position as left",f,i),z.css({transform:"translateZ(-"+i.depth.active+"px)"}),t.css({transform:"rotateY(0deg) translateZ("+i.depth.active+"px)"}),f.addClass(y.animating).css({left:i.origin+"px",transform:"rotateY(90deg) translateZ("+i.depth.next+"px)"})},behind:function(){var e={active:t.outerWidth(!0),next:f.outerWidth(!0)},i={origin:(e.active-e.next)/2,depth:{active:e.next/2,next:e.active/2}};m.verbose("Setting the initial animation position as behind",f,i),t.css({transform:"rotateY(0deg)"}),f.addClass(y.animating).css({left:i.origin+"px",transform:"rotateY(-180deg)"})}},setting:function(t,i){if(m.debug("Changing setting",t,i),e.isPlainObject(t))e.extend(!0,h,t);else{if(i===n)return h[t];e.isPlainObject(h[t])?e.extend(!0,h[t],i):h[t]=i}},internal:function(t,i){if(e.isPlainObject(t))e.extend(!0,m,t);else{if(i===n)return m[t];m[t]=i}},debug:function(){!h.silent&&h.debug&&(h.performance?m.performance.log(arguments):(m.debug=Function.prototype.bind.call(console.info,console,h.name+":"),m.debug.apply(console,arguments)))},verbose:function(){!h.silent&&h.verbose&&h.debug&&(h.performance?m.performance.log(arguments):(m.verbose=Function.prototype.bind.call(console.info,console,h.name+":"),m.verbose.apply(console,arguments)))},error:function(){h.silent||(m.error=Function.prototype.bind.call(console.error,console,h.name+":"),m.error.apply(console,arguments))},performance:{log:function(e){var t,i,n;h.performance&&(t=(new Date).getTime(),n=s||t,i=t-n,s=t,d.push({Name:e[0],Arguments:[].slice.call(e,1)||"",Element:H,"Execution Time":i})),clearTimeout(m.performance.timer),m.performance.timer=setTimeout(m.performance.display,500)},display:function(){var t=h.name+":",i=0;s=!1,clearTimeout(m.performance.timer),e.each(d,function(e,t){i+=t["Execution Time"]}),t+=" "+i+"ms",p&&(t+=" '"+p+"'"),r.length>1&&(t+=" ("+r.length+")"),(console.group!==n||console.table!==n)&&d.length>0&&(console.groupCollapsed(t),console.table?console.table(d):e.each(d,function(e,t){console.log(t.Name+": "+t["Execution Time"]+"ms")}),console.groupEnd()),d=[]}},invoke:function(t,i,a){var r,s,d,l=T;return i=i||c,a=H||a,"string"==typeof t&&l!==n&&(t=t.split(/[\. ]/),r=t.length-1,e.each(t,function(i,a){var o=i!=r?a+t[i+1].charAt(0).toUpperCase()+t[i+1].slice(1):t;if(e.isPlainObject(l[o])&&i!=r)l=l[o];else{if(l[o]!==n)return s=l[o],!1;if(!e.isPlainObject(l[a])||i==r)return l[a]!==n&&(s=l[a],!1);l=l[a]}})),e.isFunction(s)?d=s.apply(a,i):s!==n&&(d=s),e.isArray(o)?o.push(d):o!==n?o=[o,d]:d!==n&&(o=d),s}},u?(T===n&&m.initialize(),m.invoke(l)):(T!==n&&T.invoke("destroy"),m.initialize())}),o!==n?o:this},e.fn.shape.settings={name:"Shape",silent:!1,debug:!1,verbose:!1,jitter:0,performance:!0,namespace:"shape",width:"initial",height:"initial",beforeChange:function(){},onChange:function(){},allowRepeats:!1,duration:!1,error:{side:"You tried to switch to a side that does not exist.",method:"The method you called is not defined"},className:{animating:"animating",hidden:"hidden",loading:"loading",active:"active"},selector:{sides:".sides",side:".side"}}}(jQuery,window,document);