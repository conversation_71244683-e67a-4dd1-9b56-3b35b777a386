/*!
 * # Semantic UI 2.2.9 - Progress Bar
 * http://github.com/semantic-org/semantic-ui/
 *
 *
 * Released under the MIT license
 * http://opensource.org/licenses/MIT
 *
 */


/*******************************
            Progress
*******************************/

.ui.progress {
  position: relative;
  display: block;
  max-width: 100%;
  border: none;
  margin: 1em 0em 2.5em;
  box-shadow: none;
  background: rgba(0, 0, 0, 0.1);
  padding: 0em;
  border-radius: 0.28571429rem;
}
.ui.progress:first-child {
  margin: 0em 0em 2.5em;
}
.ui.progress:last-child {
  margin: 0em 0em 1.5em;
}


/*******************************
            Content
*******************************/


/* Activity Bar */
.ui.progress .bar {
  display: block;
  line-height: 1;
  position: relative;
  width: 0%;
  min-width: 2em;
  background: #888888;
  border-radius: 0.28571429rem;
  -webkit-transition: width 0.1s ease, background-color 0.1s ease;
  transition: width 0.1s ease, background-color 0.1s ease;
}

/* Percent Complete */
.ui.progress .bar > .progress {
  white-space: nowrap;
  position: absolute;
  width: auto;
  font-size: 0.92857143em;
  top: 50%;
  right: 0.5em;
  left: auto;
  bottom: auto;
  color: rgba(255, 255, 255, 0.7);
  text-shadow: none;
  margin-top: -0.5em;
  font-weight: bold;
  text-align: left;
}

/* Label */
.ui.progress > .label {
  position: absolute;
  width: 100%;
  font-size: 1em;
  top: 100%;
  right: auto;
  left: 0%;
  bottom: auto;
  color: rgba(0, 0, 0, 0.87);
  font-weight: bold;
  text-shadow: none;
  margin-top: 0.2em;
  text-align: center;
  -webkit-transition: color 0.4s ease;
  transition: color 0.4s ease;
}


/*******************************
            Types
*******************************/


/* Indicating */
.ui.indicating.progress[data-percent^="1"] .bar,
.ui.indicating.progress[data-percent^="2"] .bar {
  background-color: #D95C5C;
}
.ui.indicating.progress[data-percent^="3"] .bar {
  background-color: #EFBC72;
}
.ui.indicating.progress[data-percent^="4"] .bar,
.ui.indicating.progress[data-percent^="5"] .bar {
  background-color: #E6BB48;
}
.ui.indicating.progress[data-percent^="6"] .bar {
  background-color: #DDC928;
}
.ui.indicating.progress[data-percent^="7"] .bar,
.ui.indicating.progress[data-percent^="8"] .bar {
  background-color: #B4D95C;
}
.ui.indicating.progress[data-percent^="9"] .bar,
.ui.indicating.progress[data-percent^="100"] .bar {
  background-color: #66DA81;
}

/* Indicating Label */
.ui.indicating.progress[data-percent^="1"] .label,
.ui.indicating.progress[data-percent^="2"] .label {
  color: rgba(0, 0, 0, 0.87);
}
.ui.indicating.progress[data-percent^="3"] .label {
  color: rgba(0, 0, 0, 0.87);
}
.ui.indicating.progress[data-percent^="4"] .label,
.ui.indicating.progress[data-percent^="5"] .label {
  color: rgba(0, 0, 0, 0.87);
}
.ui.indicating.progress[data-percent^="6"] .label {
  color: rgba(0, 0, 0, 0.87);
}
.ui.indicating.progress[data-percent^="7"] .label,
.ui.indicating.progress[data-percent^="8"] .label {
  color: rgba(0, 0, 0, 0.87);
}
.ui.indicating.progress[data-percent^="9"] .label,
.ui.indicating.progress[data-percent^="100"] .label {
  color: rgba(0, 0, 0, 0.87);
}

/* Single Digits */
.ui.indicating.progress[data-percent="1"] .bar,
.ui.indicating.progress[data-percent="2"] .bar,
.ui.indicating.progress[data-percent="3"] .bar,
.ui.indicating.progress[data-percent="4"] .bar,
.ui.indicating.progress[data-percent="5"] .bar,
.ui.indicating.progress[data-percent="6"] .bar,
.ui.indicating.progress[data-percent="7"] .bar,
.ui.indicating.progress[data-percent="8"] .bar,
.ui.indicating.progress[data-percent="9"] .bar {
  background-color: #D95C5C;
}
.ui.indicating.progress[data-percent="1"] .label,
.ui.indicating.progress[data-percent="2"] .label,
.ui.indicating.progress[data-percent="3"] .label,
.ui.indicating.progress[data-percent="4"] .label,
.ui.indicating.progress[data-percent="5"] .label,
.ui.indicating.progress[data-percent="6"] .label,
.ui.indicating.progress[data-percent="7"] .label,
.ui.indicating.progress[data-percent="8"] .label,
.ui.indicating.progress[data-percent="9"] .label {
  color: rgba(0, 0, 0, 0.87);
}

/* Indicating Success */
.ui.indicating.progress.success .label {
  color: #1A531B;
}


/*******************************
             States
*******************************/


/*--------------
     Success
---------------*/

.ui.progress.success .bar {
  background-color: #21BA45 !important;
}
.ui.progress.success .bar,
.ui.progress.success .bar::after {
  -webkit-animation: none !important;
          animation: none !important;
}
.ui.progress.success > .label {
  color: #1A531B;
}

/*--------------
     Warning
---------------*/

.ui.progress.warning .bar {
  background-color: #F2C037 !important;
}
.ui.progress.warning .bar,
.ui.progress.warning .bar::after {
  -webkit-animation: none !important;
          animation: none !important;
}
.ui.progress.warning > .label {
  color: #794B02;
}

/*--------------
     Error
---------------*/

.ui.progress.error .bar {
  background-color: #DB2828 !important;
}
.ui.progress.error .bar,
.ui.progress.error .bar::after {
  -webkit-animation: none !important;
          animation: none !important;
}
.ui.progress.error > .label {
  color: #912D2B;
}

/*--------------
     Active
---------------*/

.ui.active.progress .bar {
  position: relative;
  min-width: 2em;
}
.ui.active.progress .bar::after {
  content: '';
  opacity: 0;
  position: absolute;
  top: 0px;
  left: 0px;
  right: 0px;
  bottom: 0px;
  background: #FFFFFF;
  border-radius: 0.28571429rem;
  -webkit-animation: progress-active 2s ease infinite;
          animation: progress-active 2s ease infinite;
}
@-webkit-keyframes progress-active {
  0% {
    opacity: 0.3;
    width: 0;
  }
  100% {
    opacity: 0;
    width: 100%;
  }
}
@keyframes progress-active {
  0% {
    opacity: 0.3;
    width: 0;
  }
  100% {
    opacity: 0;
    width: 100%;
  }
}

/*--------------
    Disabled
---------------*/

.ui.disabled.progress {
  opacity: 0.35;
}
.ui.disabled.progress .bar,
.ui.disabled.progress .bar::after {
  -webkit-animation: none !important;
          animation: none !important;
}


/*******************************
           Variations
*******************************/


/*--------------
    Inverted
---------------*/

.ui.inverted.progress {
  background: rgba(255, 255, 255, 0.08);
  border: none;
}
.ui.inverted.progress .bar {
  background: #888888;
}
.ui.inverted.progress .bar > .progress {
  color: #F9FAFB;
}
.ui.inverted.progress > .label {
  color: #FFFFFF;
}
.ui.inverted.progress.success > .label {
  color: #21BA45;
}
.ui.inverted.progress.warning > .label {
  color: #F2C037;
}
.ui.inverted.progress.error > .label {
  color: #DB2828;
}

/*--------------
    Attached
---------------*/


/* bottom attached */
.ui.progress.attached {
  background: transparent;
  position: relative;
  border: none;
  margin: 0em;
}
.ui.progress.attached,
.ui.progress.attached .bar {
  display: block;
  height: 0.2rem;
  padding: 0px;
  overflow: hidden;
  border-radius: 0em 0em 0.28571429rem 0.28571429rem;
}
.ui.progress.attached .bar {
  border-radius: 0em;
}

/* top attached */
.ui.progress.top.attached,
.ui.progress.top.attached .bar {
  top: 0px;
  border-radius: 0.28571429rem 0.28571429rem 0em 0em;
}
.ui.progress.top.attached .bar {
  border-radius: 0em;
}

/* Coupling */
.ui.segment > .ui.attached.progress,
.ui.card > .ui.attached.progress {
  position: absolute;
  top: auto;
  left: 0;
  bottom: 100%;
  width: 100%;
}
.ui.segment > .ui.bottom.attached.progress,
.ui.card > .ui.bottom.attached.progress {
  top: 100%;
  bottom: auto;
}

/*--------------
     Colors
---------------*/


/* Red */
.ui.red.progress .bar {
  background-color: #DB2828;
}
.ui.red.inverted.progress .bar {
  background-color: #FF695E;
}

/* Orange */
.ui.orange.progress .bar {
  background-color: #F2711C;
}
.ui.orange.inverted.progress .bar {
  background-color: #FF851B;
}

/* Yellow */
.ui.yellow.progress .bar {
  background-color: #FBBD08;
}
.ui.yellow.inverted.progress .bar {
  background-color: #FFE21F;
}

/* Olive */
.ui.olive.progress .bar {
  background-color: #B5CC18;
}
.ui.olive.inverted.progress .bar {
  background-color: #D9E778;
}

/* Green */
.ui.green.progress .bar {
  background-color: #21BA45;
}
.ui.green.inverted.progress .bar {
  background-color: #2ECC40;
}

/* Teal */
.ui.teal.progress .bar {
  background-color: #00B5AD;
}
.ui.teal.inverted.progress .bar {
  background-color: #6DFFFF;
}

/* Blue */
.ui.blue.progress .bar {
  background-color: #2185D0;
}
.ui.blue.inverted.progress .bar {
  background-color: #54C8FF;
}

/* Violet */
.ui.violet.progress .bar {
  background-color: #6435C9;
}
.ui.violet.inverted.progress .bar {
  background-color: #A291FB;
}

/* Purple */
.ui.purple.progress .bar {
  background-color: #A333C8;
}
.ui.purple.inverted.progress .bar {
  background-color: #DC73FF;
}

/* Pink */
.ui.pink.progress .bar {
  background-color: #E03997;
}
.ui.pink.inverted.progress .bar {
  background-color: #FF8EDF;
}

/* Brown */
.ui.brown.progress .bar {
  background-color: #A5673F;
}
.ui.brown.inverted.progress .bar {
  background-color: #D67C1C;
}

/* Grey */
.ui.grey.progress .bar {
  background-color: #767676;
}
.ui.grey.inverted.progress .bar {
  background-color: #DCDDDE;
}

/* Black */
.ui.black.progress .bar {
  background-color: #1B1C1D;
}
.ui.black.inverted.progress .bar {
  background-color: #545454;
}

/*--------------
     Sizes
---------------*/

.ui.tiny.progress {
  font-size: 0.85714286rem;
}
.ui.tiny.progress .bar {
  height: 0.5em;
}
.ui.small.progress {
  font-size: 0.92857143rem;
}
.ui.small.progress .bar {
  height: 1em;
}
.ui.progress {
  font-size: 1rem;
}
.ui.progress .bar {
  height: 1.75em;
}
.ui.large.progress {
  font-size: 1.14285714rem;
}
.ui.large.progress .bar {
  height: 2.5em;
}
.ui.big.progress {
  font-size: 1.28571429rem;
}
.ui.big.progress .bar {
  height: 3.5em;
}


/*******************************
            Progress
*******************************/



/*******************************
         Site Overrides
*******************************/

