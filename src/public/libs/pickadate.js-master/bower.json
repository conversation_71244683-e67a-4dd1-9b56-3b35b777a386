{"name": "pickadate", "description": "The mobile-friendly, responsive, and lightweight jQuery date & time input picker.", "main": ["lib/picker.js", "lib/picker.date.js", "lib/picker.time.js", "lib/themes/default.css", "lib/themes/default.date.css", "lib/themes/default.time.css"], "license": "MIT", "ignore": ["*.md", "*.htm", "_docs", "demo"], "keywords": ["date", "time", "picker", "input", "responsive"], "authors": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://amsul.ca"}, "homepage": "http://amsul.ca/pickadate.js", "repository": {"type": "git", "url": "git://github.com/ConnectedHomes/pickadate.js.git"}, "dependencies": {"jquery": ">=1.7"}, "devDependencies": {"grunt": "^0.4.5", "grunt-autoprefixer": "^1.0.1", "grunt-contrib-copy": "^0.5.0", "grunt-contrib-cssmin": "^0.9.0", "grunt-contrib-jshint": "^0.10.0", "grunt-contrib-less": "^0.11.4", "grunt-contrib-qunit": "^0.4.0", "grunt-contrib-uglify": "^0.4.1", "grunt-contrib-watch": "^0.6.1", "phantomjs": "^1.9.7-5", "zlib-browserify": "0.0.3"}}