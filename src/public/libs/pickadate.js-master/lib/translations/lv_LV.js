// Latvian

jQuery.extend( jQuery.fn.pickadate.defaults, {
    monthsFull: [ '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>' ],
    monthsShort: [ '<PERSON>', 'Feb', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON>n', '<PERSON><PERSON><PERSON>', 'Aug', 'Sep', 'Okt', 'Nov', 'Dec' ],
    weekdaysFull: [ 'Svētdiena', 'Pirmdiena', 'O<PERSON><PERSON>na', 'Tre<PERSON>diena', 'Ceturtdiena', 'Piektdiena', '<PERSON>stdiena' ],
    weekdaysShort: [ 'Sv', 'P', 'O', 'T', 'C', 'Pk', 'S' ],
    today: '<PERSON>odiena',
    clear: 'Dzēst',
    close: 'Aizvērt',
    firstDay: 1,
    format: 'yyyy.mm.dd. dddd',
    formatSubmit: 'yyyy/mm/dd',
    labelMonthNext: '<PERSON><PERSON><PERSON><PERSON><PERSON> mēnesis',
    labelMonthPrev: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> mēnesis',
    labelMonthSelect: 'Izvēlēties mēnesi',
    labelYearSelect: 'Izvēlēties gadu'
});

jQuery.extend( jQuery.fn.pickatime.defaults, {
    clear: 'Dzēst'
});
