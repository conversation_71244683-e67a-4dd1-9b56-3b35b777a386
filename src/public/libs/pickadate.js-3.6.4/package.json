{"name": "pickadate", "version": "3.6.4", "title": "pickadate.js", "description": "The mobile-friendly, responsive, and lightweight jQuery date & time input picker.", "keywords": ["date", "time", "picker", "input", "responsive"], "homepage": "http://amsul.ca/pickadate.js", "bugs": "https://github.com/amsul/pickadate.js/issues", "license": "MIT", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://amsul.ca"}, "files": ["lib", "tests", "README.md", "Gruntfile.js"], "repository": {"type": "git", "url": "https://github.com/amsul/pickadate.js.git"}, "scripts": {"prebump": "npm test", "bump": "node ./version-bump.js", "postbump": "npx grunt package && node ./version-commit.js", "push": "git push && git push origin --tags && npm publish", "test": "npx grunt test --verbose"}, "dependencies": {"jquery": ">=1.7"}, "devDependencies": {"commander": "^2.8.0", "glob": "^5.0.5", "grunt": "^0.4.5", "grunt-autoprefixer": "^3.0.0", "grunt-contrib-copy": "^0.8.0", "grunt-contrib-cssmin": "^0.12.2", "grunt-contrib-jshint": "^0.11.2", "grunt-contrib-less": "^1.0.1", "grunt-contrib-qunit": "^2.0.0", "grunt-contrib-uglify": "^0.9.1", "grunt-contrib-watch": "^0.6.1", "qunitjs": "1.x", "semver": "^4.3.3", "shelljs": "^0.4.0", "zlib-browserify": "0.0.3"}}