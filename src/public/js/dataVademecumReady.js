// JSON de Vademécum
let vademecum = {};
// JSON de descripciones para el filtro del search del campo descripVademecum
let listDescrip = {};
// Definimos las variables para las clases
const descripVademecum = ".descripVademecum"; // div search
const tipoVademecum = ".tipoVademecum";
const moleculaVademecum = ".moleculaVademecum";

// Función para inicializar los dropdowns y campos
function initializeDropdownsAndFields($group = null) {
    if ($group === null) {
        // Inicializar los dropdowns y campos para todos los grupos
        $(".vademecum-group").each(function () {
            initializeForGroup($(this));
        });
    }
    // Si hay un grupo especificado, inicializar solo ese grupo
    else {
        initializeForGroup($group);
    }
}

// Función para inicializar un solo grupo
function initializeForGroup($group) {
    const moleculaDrop = $group.find(moleculaVademecum);
    const tipoDrop = $group.find(tipoVademecum);

    // Inicializar los dropdowns
    moleculaDrop.dropdown();
    tipoDrop.dropdown();

    // Obtener las moléculas únicas
    const moleculas = [...new Set(vademecum.map(item => item.molecula.trim()))];

    const $descriptionInput = $group.find(descripVademecum + ' input'); // input search

    // Poblar el dropdown de moléculas
    populateDropdown(moleculaDrop, moleculas);

    toggleDescriptionInput('', '', $descriptionInput);
}

// Función para poblar el dropdown de moléculas o tipos
function populateDropdown(dropdown, items) {
    // Limpiar el menu y el dropdown
    dropdown.dropdown("clear");
    dropdown.find(".menu").empty();

    // Poblamos el dropdown con las opciones
    items.forEach(item => {
        const itemName = capitalizeWordsWithExceptions(item.trim());
        dropdown.find(".menu").append(
            `<div class="item" data-value="${item}">${itemName}</div>`
        );
    });
    // Volver a inicializar el dropdown
    dropdown.dropdown("refresh");
}

// Función para poblar el dropdown de tipos según la molécula seleccionada
function populateTipos(molecula, $group) {

    // Obtener el dropdown de tipos
    const tipoDrop = $group.find(tipoVademecum);
    // Obtener los tipos únicos para la molécula seleccionada
    const tipos = [...new Set(vademecum.filter(item => item.molecula.trim() === molecula).map(item => item.tipo.trim()))];
    // Poblar el dropdown de tipos
    populateDropdown(tipoDrop, tipos);
}

// Función para capitalizar las palabras con excepciones
function capitalizeWordsWithExceptions(str) {
    if (!str) return str || '';

    // Lista de excepciones
    const exceptions = ['de', 'y', 'la', 'el', 'los', 'las', 'un', 'una', 'por', 'para', 'en', 'con'];
    return str.split(' ').map((word, index) => {
        if (index === 0 || !exceptions.includes(word.toLowerCase())) {
            return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
        }
        return word.toLowerCase();
    }).join(' ');
}

// Función para inicializar el search del campo descripVademecum
function initDescripVademecumSearch_xxx($group, selectedMolecula, selectedTipo) {

    // Limpiar la lista de descripciones
    listDescrip = {};

    // Convertir las cadenas a minúsculas
    const moleculaValue = selectedMolecula.trim().toLowerCase();
    const tipoValue = selectedTipo.trim().toLowerCase();

    // Filtrar las descripciones basadas en la molécula y el tipo
    listDescrip = vademecum.filter(item => {
        return item.molecula.toLowerCase().includes(moleculaValue) &&
            item.tipo.toLowerCase().includes(tipoValue);
    });

    // Obtener el elemento de búsqueda
    const $searchElement = $group.find(descripVademecum);

    // Destruir cualquier instancia previa de búsqueda
    if ($searchElement.data('search')) {
        $searchElement.search('destroy');
    } else {
        $searchElement.off(); // Eliminar eventos previos
        $searchElement.removeData(); // Limpiar datos previos
    }

    // Variable para almacenar el estado de los resultados de búsqueda
    let searchResultsFound = false;

    // Configuración del search con Semantic UI
    $searchElement.search({
        source: listDescrip.map(item => {
            return {
                ...item,
                descrip_articulo: capitalizeWordsWithExceptions(item.descrip_articulo),
            };
        }),
        fields: {
            title: 'descrip_articulo',
            description: 'codigo'
        },
        searchFields: ['descrip_articulo', 'codigo', 'casa'],
        showNoResults: true,
        searchFullText: true,
        error: {noResults: 'No se encontraron resultados para tu búsqueda.'},
        searchMatcher: function (response, searchTerm) {
            return response.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                response.description.toLowerCase().includes(searchTerm.toLowerCase());
        },
        onResults: function (results) {
            // Actualizar el estado según si hay resultados o no
            searchResultsFound = results.length > 0;
        },
        onSelect: function (result) {
            if (result) {
                const $group = $(this).closest('.vademecum-group');
                $group.find('textarea[name^="codigo-vademecum"]').val(result.codigo);
                $group.find('textarea[name^="casa-vademecum"]').val(capitalizeWordsWithExceptions(result.casa));
            } else {
                // Limpiar los campos si no se selecciona un resultado válido
                $(this).closest('.vademecum-group').find('textarea[name^="codigo-vademecum"], textarea[name^="casa-vademecum"]').val("");
            }
        }
    });

    // Agregar evento `onblur` al campo de búsqueda
    $searchElement.find('input').on('blur', function () {
        const $group = $(this).closest('.vademecum-group');

        if (!searchResultsFound) {
            // Si no se encontraron resultados, limpiar los campos
            clearSomeFields($group);
        } else {
            // Opcional: Realizar otra acción si se encontraron resultados
            console.log('Resultados disponibles, no se limpian los campos.');
        }
    });
}

function initDescripVademecumSearch($group, selectedMolecula, selectedTipo) {

    console.log('aquiiiiiiii');
    // Preparar valores
    const moleculaValue = selectedMolecula.trim().toLowerCase();
    const tipoValue = selectedTipo.trim().toLowerCase();

    // Filtrar lista de descripciones
    const listDescrip = vademecum
        .filter(item => item.molecula.toLowerCase().includes(moleculaValue) &&
            item.tipo.toLowerCase().includes(tipoValue))
        .map(item => ({
            codigo: item.codigo,
            casa: item.casa,
            descrip_articulo: capitalizeWordsWithExceptions(item.descrip_articulo),
            search_text: (item.codigo + ' ' + item.descrip_articulo + ' ' + item.casa).toLowerCase()
        }));

    // Dropdown a inicializar
    const $dropdown = $group.find('.descripVademecumDropdown');
    $dropdown.dropdown('clear');

    // Inicializar dropdown con autocompletado
    $dropdown.dropdown({
        minCharacters: 1,
        fullTextSearch: false,
        forceSelection: true,
        apiSettings: {
            responseAsync: function (settings, callback) {
                const query = settings.urlData.query.toLowerCase();
                const filtered = listDescrip
                    .filter(item => item.search_text.includes(query))
                    .slice(0, 15);

                const results = filtered.map(item => ({
                    name: item.codigo + ' - ' + item.descrip_articulo,
                    value: item.descrip_articulo,
                    casa: item.casa
                }));

                callback({ success: true, results });
            }
        },
        onChange: function (value, text, $choice) {
            const selectedItem = listDescrip.find(item => item.descrip_articulo === value);
            if (selectedItem) {
                const container = $(this).closest('.vademecum-group');
                container.find('textarea[name^="codigo-vademecum"]').val(selectedItem.codigo);
                container.find('textarea[name^="casa-vademecum"]').val(capitalizeWordsWithExceptions(selectedItem.casa));
            }
        }
    });

}


// ** Nueva función para habilitar o deshabilitar el campo de descripción**
function toggleDescriptionInput(moleculaValue, tipoValue, $descriptionInput) {
    if (!moleculaValue || !tipoValue || moleculaValue === "" || tipoValue === "") {
        // Deshabilitar el campo de descripción si no hay "Molécula" o "Tipo"
        $descriptionInput.prop("disabled", true);
        $descriptionInput.val("");  // Limpiar el valor del input
    } else {
        // Habilitar el campo de descripción si ambos "Molécula" y "Tipo" tienen valores
        $descriptionInput.prop("disabled", false);
    }
}

// Función para manejar el cambio en el dropdown de moléculas
function handleMoleculaChange($group) {
    clearSomeFields($group);    // Limpiar los campos de Código y Casa
    // Obtener la molécula seleccionada
    const selectedMolecula = $group.find(moleculaVademecum).dropdown("get value").trim();
    populateTipos(selectedMolecula, $group); // Actualizar los tipos según la molécula seleccionada
}

// Función para manejar el cambio en el dropdown de tipos
function handleTipoChange($group) {

    console.log('aquiiiiiiii1111');
    clearSomeFields($group);    // Limpiar los campos de Código y Casa
    // Obtener la molécula y el tipo seleccionados
    const selectedMolecula = $group.find(moleculaVademecum).dropdown("get value").trim();
    const selectedTipo = $group.find(tipoVademecum).dropdown("get value").trim();
    const $descriptionInput = $group.find(descripVademecum + ' input'); // input search
    // Llamamos a toggleDescriptionInput con los valores correctos
    toggleDescriptionInput(selectedMolecula, selectedTipo, $descriptionInput);
    // Llamamos a initDescripVademecumSearch con los valores correctos
    initDescripVademecumSearch($group, selectedMolecula, selectedTipo);

}

// Función para limpiar los campos de Código y Casa
function clearSomeFields($group) {
    const $descriptionInput = $group.find(descripVademecum + ' input'); // input search

    // Limpiar los campos
    $group.find('textarea[name^="codigo-vademecum"]').val("");
    $group.find('textarea[name^="casa-vademecum"]').val("");

    // Limpiar el campo de búsqueda y actualizar la interfaz de Semantic UI
    $descriptionInput.val('').trigger('input'); // Limpiar el valor y disparar el evento de input

    // Si el componente de búsqueda de Semantic UI necesita ser actualizado visualmente:
    $group.find('.descripVademecum').dropdown('clear');
}
