<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class ServiceField extends Model
{

    const PCL_ACCOUNT = 1;
    const AUTH_NUMBER_JUNTAS = 2;
    const MEETING_SINISTER_NUM = 3;
    const ID_BIZAGI_PCL = 13;


    protected $casts = [
        'required' => 'boolean',
    ];
    //
    public function getNameAttribute($value)
    {
        return mb_strtoupper($value);
    }
    public function getValuesAttribute($value)
    {
        return mb_strtoupper($value);
    }
}
