<?php


namespace App;

use Illuminate\Database\Eloquent\Model;

class IssuerAdministrativePayment extends Model
{

    protected $fillable = [
        'name',
        'commercial_name',
        'id_type',
        'id_number',
        'province',
        'canton',
        'district',
        'neighborhood',
        'address',
        'country_code',
        'phone_number',
        'email',
        'invoice_key',
        'activity_code',
        'consecutive_number',
        'issue_date',
        'net_sales_total',
        'total_tax',
        'total_invoice_amount',
        'total_amountDue',
        'currency'
    ];

    public function activity()
    {
        return $this->belongsTo('App\Activity');
    }

    public function serviceDetails()
    {
        return $this->hasMany('App\ServiceDetailAdministrativePayment', 'issuer_id');
    }


}
