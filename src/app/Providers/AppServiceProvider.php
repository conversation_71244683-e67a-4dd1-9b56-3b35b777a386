<?php

namespace App\Providers;

use App\Affiliate;
use App\OccupationCategory;
use App\PolicySort;
use App\AFP;
use App\ARL;
use App\Branch;
use App\Employer;
use App\Employment;
use App\EPS;
use App\JR;
use App\Mail\SendDocument;
use App\Observers\LoggedModelObserver;
use App\SEGURO;
use Carbon\Carbon;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\View;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Auth;
use App\User;
use App\Area;
use App\States\StatePolicySortCollection;
use App\UserAuthorizedPolicies;


class AppServiceProvider extends ServiceProvider
{
    // Lista de dominios permitidos para correos de prueba
    public static $ALLOWED_DOMAINS = [
        'renconsultores.com.co',
        'rengroup.co',
        'mnkseguros.com',
        'gufum.com'
    ];

    public static $ACCOUNTS_PERCENTAGES = [
        1 => 100.0, // CXC COLONES
        2 => 100.0, // CXC DOLARES
        3 => 0.04,   // CXP APORTE BOMBEROS COLONES
        4 => 0.04,   // CXP APORTE BOMBEROS DOLARES
        5 => 0.005,   // CXP IMPUESTO INEC COLONES
        6 => 0.005,   // CXP IMPUESTO INEC DOLARES
        7 => 0.715,  // CUENTA POR PAGAR A REASEGURO NETO COLONES
        8 => 0.715,  // CUENTA POR PAGAR A REASEGURO NETO DOLARES
        9 => 0.15,  // CXP COMISION AGENTE COLONES
        10 => 0.15, // CXP COMISION AGENTE DOLARES
        11 => 0.15, // CXP COMISION CORREDORA COLONES
        12 => 0.15, // CXP COMISION CORREDORA DOLARES
        13 => 0.15, // COMISION AGENTE / CORREDORA COLONES
        14 => 0.15, // COMISION AGENTE / CORREDORA DOLARES
        15 => 0.90, // PRIMA CEDIDA A REASEGURO COLONES
        16 => 0.90, // PRIMA CEDIDA A REASEGURO DOLARES
        17 => 0.04,  // APORTE BOMBEROS COLONES
        18 => 0.04,  // APORTE BOMBEROS DOLARES
        19 => 0.005,  // IMPUESTO INEC COLONES
        20 => 0.005,  // IMPUESTO INEC DOLARES
        21 => 100.0, // PRIMA BRUTA COLONES
        22 => 100.0, // PRIMA BRUTA DOLARES
        23 => 0.136712, // COMISION REASEGURO COLONES
        24 => 0.136712, // COMISION REASEGURO DOLARES
        25 => 0.04,  // APORTE BOMBEROS COLONES
        26 => 0.04,  // APORTE BOMBEROS DOLARES
        27 => 0.0035,  // IMPUESTO MUNICIPAL COLONES
        28 => 0.004,  // IMPUESTO MUNICIPAL DOLARES
        29 => 0.05,  // IMPUESTO INEC COLONES
        30 => 0.05,  // IMPUESTO INEC DOLARES
        31 => 5.31, // CXC RETENCION DE IMPUESTO VALOR AGREGADO COLONES
        32 => 5.31, //CXC RETENCION DE IMPUESTO VALOR AGREGADO DOLARES
        33 => 1.76, //CXC RETENCION DE IMPUESTO DE RENTA COLONES
        34 => 1.76, //CXC RETENCION DE IMPUESTO DE RENTA DOLARES
    ];
    public static $ACCOUNTS_PERCENTAGES_003 = [
        1 => '-',       // RECIBO DE CAJA COLONES
        2 => 100.0,     // RECIBO DE CAJA DOLARES
        3 => 100.0,     // CXC COLONES
        4 => 100.0,     // CXC DOLARES
        5 => 0.02,      // CXC RETENCION DE IMPUESTO DE RENTA COLONES
        6 => 0.02,      // CXC RETENCION DE IMPUESTO DE RENTA DOLARES
        7 => 0.05,      // CXC RETENCION DE IMPUESTO VALOR AGREGADO COLONES
        8 => 0.05,      // CXC RETENCION DE IMPUESTO VALOR AGREGADO DOLARES
        9 => 0,         // DIFERENCIA CAMBIO COLONES AUMENTO
        10 => 0,        // DIFERENCIA CAMBIO COLONES DISMINUCION
        11 => 0,        // DIFERENCIA CAMBIO DOLARES AUMENTO
        12 => 0,        // DIFERENCIA CAMBIO DOLARES DISMINUCION
        13 => 0.03,     // COMISION POR GESTION DE COBRANZA BANCOS COLONES
        14 => 0.03,     // COMISION POR GESTION DE COBRANZA BANCOS DOLARES
        15 => 0,        // FALTANTE EN CAJA COLONES
        16 => 0,        // FALTANTE EN CAJA DOLARES
        17 => 0,        // SOBRANTE EN CAJA COLONES
        18 => 0,        // SOBRANTE EN CAJA DOLARES
    ];

    public static $REINSURANCE_PERCENTAGE = 0.50;

    //Porcentaje reaseguro Siniestro
    public static $PERCENTAGE_REINSURANCE_SINISTER = 0.15;

    public static $CTAUX =  [

        "banco_nacional" => [
            "CO" => "00000000010001",
            "DO" => "00000000010002"
        ],
        "davivienda" => [
            "CO" => "000000MNKDACOL",
            "DO" => "000000MNKDADOL"
        ],
        "bac_redomatic" => [
            "CO" => "00000000030001",
            "DO" => "00000000030002"
        ],
        "banco_costa_rica" => [
            "CO" => "00000000010005",
            "DO" => "00000000020002"
        ]

    ];
    //La siguiente constante de $PROVIDERS_AFFILIATES se refiere a los id's de los afiliados (de la tabla affiliates) designados para cada uno de los proveedores
    //recordar que una actividad no puede tener crearse si no tiene un afiliado asociado, por ende, el usuario que se loguea como proveedor tiene que tener un afiliado asociado
    // la anterior relación la encontramos en la columna affiliate_id de la tabla users.
    public static $PROVIDERS_AFFILIATES = [
        100 => 10, //Dokka
        101 => 7, // Emergencias médicas
        102 => 5, //Addiuva
        41450 => 8, //Hospital La Católica
        41451 => 9, //Hospital Metropolitano
        46832 => 11, //REDBRIDGE S.A
    ];

    public static $TYPE_EXPENSE = [
        'funeral_transport' => "Funeral y traslado del cadáver",
        'transport_lodging_meals' => "Traslado, Hospedaje y alimentación",
        'invoices' => "Facturas"
    ];

    public static $OCCUPATION_GROUPS = [
        'Directores y gerentes' => [
            11 => "Directores ejecutivos, personal directivo de la administración pública y miembros del poder ejecutivo y de los cuerpos legislativos",
            12 => "Directores administrativos y comerciales",
            13 => "Directores y gerentes en sectores de producción y servicios especializados",
            14 => "Gerentes de hoteles, restaurantes, comercios y otros servicios",
        ],
        'Profesionales científicos e intelectuales' => [
            21 => "Profesionales de las ciencias, matemática e ingeniería",
            22 => "Profesionales de la salud",
            23 => "Profesionales de la educación",
            24 => "Profesionales de la administración y los negocios",
            25 => "Profesionales de tecnología de la información y las comunicaciones",
            26 => "Profesionales en derecho, en ciencias sociales y culturales",
        ],
        'Técnicos y profesionales de nivel medio' => [
            31 => "Profesionales de nivel medio de las ciencias y la ingeniería",
            32 => "Profesionales de nivel medio de la salud",
            33 => "Profesionales de nivel medio en operaciones financieras, administrativas, matemáticas y estadística",
            34 => "Profesionales de nivel medio de servicios jurídicos, sociales, culturales y afines",
            35 => "Técnicos de la tecnología de la información y las comunicaciones",
            36 => "Profesionales de nivel medio de la enseñanza",
            37 => "Otras ocupaciones de nivel técnico y profesional medio",
        ],
        'Personal de apoyo administrativo' => [
            41 => "Oficinistas",
            42 => "Empleados de trato directo con el público",
            43 => "Empleados contables y encargados del registro de materiales",
            44 => "Otro personal de apoyo administrativo",
        ],
        'Trabajadores de los servicios y vendedores de comercios y mercados' => [
            51 => "Trabajadores de servicios personales",
            52 => "Vendedores",
            53 => "Trabajadores de cuidados personales",
            54 => "Personales de los servicios de protección",
        ],
        'Agricultores y trabajadores calificados agropecuarios, forestales y pesqueros' => [
            61 => "Agricultores y trabajadores calificados de explotaciones agropecuarias con destino al mercado",
            62 => "Trabajadores forestales calificados, pescadores y cazadores",
            63 => "Trabajadores agropecuarios, pescadores, cazadores y recolectores de subsistencia",
        ],
        'Operarios y artesanos de artes mecánicas y de otros oficios' => [
            71 => "Operarios de la construcción, excluyendo electricistas",
            72 => "Operarios trabajadores de la metalurgia, construcción, mecánica y afines",
            73 => "Artesanos y operarios de las artes gráficas",
            74 => "Trabajadores especializados en electricidad y electrotecnia",
            75 => "Operarios trabajadores del procesamiento de alimentos, de la confección, ebanistas, otros artesanos y afines",
        ],
        'Operadores de instalaciones y máquinas y ensambladores' => [
            81 => "Operadores de instalaciones fijas y máquinas",
            82 => "Ensambladores",
            83 => "Conductores de vehículos y operadores de equipos pesados móviles",
        ],
        'Ocupaciones elementales' => [
            91 => "Limpiadores y asistentes de limpieza",
            92 => "Peones agropecuarios, pesqueros y forestales",
            93 => "Peones de la minería, la construcción, la industria manufacturera y el transporte",
            94 => "Ayudantes de preparación de alimentos",
            95 => "Vendedores ambulantes de servicios y afines",
            96 => "Recolectores de desechos y otras ocupaciones elementales",
        ],
    ];

    public static $TYPE_ACCOUNT = [
        'client' => "Cliente",
        'bank' => "Banco",
        'iban' => "IBAN"
    ];

    public static $RECEIPT_STATE = [
        "pending"   => "Pendiente",
        "pending-approval"   => "Pendiente de aprobación",
        "approved"      => "Pagado",
        "rejected" => "Rechazado",
        "canceled" => "Rechazado",
    ];

    public static $QUOTATIONS_STATE = [
        "1" => "Registrado",
        "6" => "Cotización en trámite",
        "7" => "Cotización generada",
        "8" => "Pendiente emisión",
        "9" => "Cotización caducada"
    ];

    public static $TYPE_PERMANENT_DISABILITY = [
        "IMP" => "IMP: Incapacidad Menor Permanente",
        "IPP" => "IPP: Incapacidad Parcial Permanente",
        "ITP" => "ITP: Incapacidad Total Permanente",
        "GI" => "GI: Gran Invalidez",
    ];

    public static $TYPE_RECEIPT = [
        "emission" => "Recibo de emisión",
        "period_increase" => "Recibo aumento seguro periodo",
        "monthly_payment" => "Recibo abono mensual",
        "quarterly_payment" => "Recibo abono trimestral",
        "semiannual_payment" => "Recibo abono semestral",
        "rehabilitation" => "Recibo de rehabilitación",
        "liquidation" => "Recibo de liquidación",
        "renewal" => "Recibo de renovación",
    ];

    public static $TYPE_MEDICAL_SERVICE = [
        0 => "Prestación medica",
        1 => "Hospitalización",
        2 => "Imágenes diagnosticas",
        3 => "Remisión a especialista"
    ];
    public static $S3_DOCUMENTS_MNK = [
        'condiciones_generales' => 'https://mnk-prod.s3.us-east-1.amazonaws.com/public/Condiciones+generales+Seguro+Obligatorio+de+Riesgos+del+Trabajo.pdf',
        'norma_tecnica' => 'https://mnk-prod.s3.us-east-1.amazonaws.com/public/Norma+te%CC%81cnica+Seguro+Obligatorio+de+Riesgos+del+Trabajo.pdf',
    ];
    public static $S3_DOCUMENTS_QA = [
        'condiciones_generales' => '',
        'norma_tecnica' => 'https://mnk-prod.s3.us-east-1.amazonaws.com/public/norma_tecnica_oceanica.pdf',
    ];

    public static $TYPE_CURRENCY = [
        "1" => "₡",
        "2" => "$"
    ];
    public static $PERIODICITY = [
        "ANUAL" => 1,
        "SEMESTRAL" => 2,
        "TRIMESTRAL" => 3,
        "MENSUAL" => 4
    ];
    public static $TYPE_BENEFICIARY = [
        "CN" => "Cónyuge",
        "HJ" => "Hijo",
        "MD" => "Madre",
        "PD" => "Padre",
        "OD" => "Otros dependientes"
    ];
    public static $MNK_SERVICES = [
        1 => [
            "NAME" => "COTIZACIONES SORT",
            "OPTIONS" => [
                74 => 'COTIZACIONES SORT',
            ]
        ],
        2 => [
            "NAME" => "ASEGURAMIENTO",
            "OPTIONS" => [
                75 => 'PÓLIZA SORT',
                76 => 'COBROS PÓLIZA SORT',
                78 => 'CONSTACIAS-POLIZA SORT',
                94 => 'LIQUIDACIÓN PÓLIZA SORT',
                95 => 'RENOVACIÓN',
            ]
        ],
        3 => [
            "NAME" => "VARIACIONES SORT",
            "OPTIONS" => [
                77 => 'VARIACIONES SORT',
            ]
        ],
        4 => [
            "NAME" => "REPORTE PLANILLA TOMADOR",
            "OPTIONS" => [
                79 => 'REPORTE PLANILLA TOMADOR',
            ]
        ],
        5 => [
            "NAME" => "REPORTE PLANILLA AFILIADO",
            "OPTIONS" => [
                80 => 'REPORTE PLANILLA AFILIADO',
            ]
        ],
        6 => [
            "NAME" => "PRESTACIONES MÉDICAS - SORT",
            "OPTIONS" => [
                83 => 'PRESTACIONES MÉDICAS - SORT',
                98 => 'Prestación médica SORT - atención secundaria'
            ]
        ],
        7 => [
            "NAME" => "PRESTACIONES",
            "OPTIONS" => [
                84 => 'PE - Incapacidad temporal SORT',
                86 => 'PE - Incapacidad permanente SORT',
                89 => 'PE MUERTE PERSONA TRABAJADORA SORT',
                91 => 'PE RECONOCIMIENTO DE GASTOS',
                93 => 'PAGO AFILIADOS SORT'
            ]
        ],
        8 => [
            "NAME" => "MEDICAMENTOS",
            "OPTIONS" => [
                87 => 'MEDICAMENTOS',
            ]
        ],
        9 => [
            "NAME" => "GESTIÓN INTEGRAL DEL SINIESTRO - SORT",
            "OPTIONS" => [
                88 => 'GESTIÓN INTEGRAL DEL SINIESTRO - SORT',

            ]
        ],
        10 => [
            "NAME" => "CUENTAS MÉDICAS",
            "OPTIONS" => [
                90 => 'CUENTAS MÉDICAS',
            ]
        ],
        11 => [
            "NAME" => "INSUMOS O MOT",
            "OPTIONS" => [
                92 => 'INSUMOS O MOT',
            ]
        ],
        12 => [
            "NAME" => "REINTEGRO",
            "OPTIONS" => [
                97 => 'REINTEGRO',
            ]
        ],
        13 => [
            "NAME" => "PROVEEDORES DE LA SEGURIDAD SOCIALllllllllllllllllllllll",
            "OPTIONS" => [
                99 => 'PROVEEDORES DE LA SEGURIDAD SOCIAL',
            ]
        ],
        14 => [
            "NAME" => "SUBROGACIÓN SORT",
            "OPTIONS" => [
                101 => 'SUBROGACIÓN SORT',
            ]
        ],
    ];

    public static $MNK_PUBLIC_FILES = [
        "PLANTILLA_SORT" => "PLANTILLA_SORT.xlsx",
    ];

    public static $PERIODICITYT = [
        0 => "ÚNICO",
        1 => "ANUAL",
        2 => "SEMESTRAL",
        3 => "TRIMESTRAL",
        4 => "MENSUAL"
    ];

    public static $PERIODICITY_EXP = [
        '1' => 'anual',
        '2' => 'semianual',
        '3' => 'trimestral',
        '4' => 'mensual',
    ];

    public static $PERIODICITY_DIVISOR = [
        'semianual'  => 2,
        'trimestral' => 4,
        'mensual'    => 12,
    ];

    public static $SMMLV = [
        2016 => 689454,
        2017 => 737717,
        2018 => 781242,
        2019 => 828116,
        2020 => 877803,
        2021 => 908526,
        2022 => 1000000,
        2023 => 1160000,
        2024 => 1300000,
    ];

    public static $REJECTION_CAUSALS = [
        "2 - PERIODO DE INCAPACIDAD YA RECONOCIDO POR LA EPS",
        "3 - PERIODO DE INCAPACIDAD YA RECONOCIDO Y PAGADO POR COLPENSIONES",
        "5 - PERÍODO DE INCAPACIDAD PRESCRITO",
        "6 - PERÍODO DE INCAPACIDAD ANTERIOR AL DÍA 181",
        "7 - PERÍODO DE INCAPACIDAD POSTERIOR AL DÍA 540. ESTÁN A CARGO DE SU EPS",
        "8 - INCAPACIDAD DE ORIGEN LABORAL",
        "9 - CONCEPTO DE REHABILITACIÓN NO FAVORABLE. PROCEDE CALIFICACIÓN PCL",
        "10 - INCONSISTENCIA EN LA HISTORIA LABORAL. SOLICITAR CORRECCIÓN",
        "12 - DOCUMENTO APORTADO NO CORRESPONDE A CERTIFICADO DE INCAPACIDAD",
        "13 - PERÍODOS DE INCAPACIDAD NO ESTÁN RELACIONADOS EN EL CERTIFICADO DE RELACIÓN DE INCAPACIDADES",
        "14 - PERIODO NO COTIZADO",
        "15 - SE ENCUENTRA TRASLADADO AL RAIS",
        "17 - DESISTIMIENTO TÁCITO",
        "18 - CERTIFICADO RELACIÓN DE INCAPACIDADES NO EVIDENCIA EL PRIMER DIA",
        "19 - CONCEPTO REHABILITACIÓN CON PRONÓSTICO INDETERMINADO. DEBE SER FAVORABLE O NO FAVORABLE",
        "20 - CONCEPTO DE REHABILITACIÓN NO REMITIDO POR LA EPS. INCAPACIDADES A CARGO DE ESA ENTIDAD",
        "21 - AL DÍA 180 DE INCAPACIDAD NO SE ENCUENTRA AFILIADO A COLPENSIONES",
        "28 - INCAPACIDAD CON DIAGNOSTICO NO RELACIONADO. DEBER SER RECONOCIDA POR SU EPS",
        "29 - CERTIFICADO DE INCAPACIDAD NO CONTIENE DIAGNÓSTICO",
        "30 - CERTIFICADO RELACIÓN DE INCAPACIDADES NO CONTIENE DIAGNÓSTICO",
        "31 - INCAPACIDAD REPETIDA",
        "32 - NO PROCEDE RECONOCIMIENTO DE INCAPACIDADES. SE RECONOCIÓ Y PAGÓ INDEMNIZACIÓN SUSTITUTIVA",
        "33 - CERTIFICADO DE INCAPACIDAD NO CUMPLE REQUISITOS DEL ART. 2.2.3.3.2 DEL DECRETO 1427 DE 2022",
        "34 - NO ESTÁ AFILIADO AL SGSS EN CALIDAD DE COTIZANTE",
        "35 - NO HA COTIZADO COMO MÍNIMO 4 SEMANAS INMEDIATAMENTE ANTERIORES AL INICIO DE LA INCAPACIDAD",
        "36 - LA INCAPACIDAD TIENE ORIGEN EN TRATAMIENTOS CON FINES ESTÉTICOS",
        "37 - SE CONFIGURA ALGUNAS DE LAS CAUSALES DE ABUSO DEL DERECHO",
        "38 - CONCEPTO DE REHABILITACIÓN REMITIDO POR LA EPS CON POSTERIORIDAD AL DÍA 180",
        "39 - PERIODO NO INLCUIDO EN LA ORDEN JUDICIAL",
        "40 - TIENE STATUS DE PENSIONADO",
    ];

    public static $REJECTION_CAUSALS_PB = [
        "2" => "2 - PERIODO DE INCAPACIDAD YA RECONOCIDO POR LA EPS",
        "3" => "3 - PERIODO DE INCAPACIDAD YA RECONOCIDO Y PAGADO POR COLPENSIONES",
        "5" => "5 - PERÍODO DE INCAPACIDAD PRESCRITO",
        "6" => "6 - PERÍODO DE INCAPACIDAD ANTERIOR AL DÍA 181",
        "7" => "7 - PERÍODO DE INCAPACIDAD POSTERIOR AL DÍA 540. ESTÁN A CARGO DE SU EPS",
        "8" => "8 - INCAPACIDAD DE ORIGEN LABORAL",
        "10" => "10 - INCONSISTENCIA EN LA HISTORIA LABORAL. SOLICITAR CORRECCIÓN",
        "12" => "12 - DOCUMENTO APORTADO NO CORRESPONDE A CERTIFICADO DE INCAPACIDAD",
        "13" => "13 - PERÍODOS DE INCAPACIDAD NO ESTÁN RELACIONADOS EN EL CERTIFICADO DE RELACIÓN DE INCAPACIDADES",
        "14" => "14 - PERIODO NO COTIZADO",
        "15" => "15 - SE ENCUENTRA TRASLADADO AL RAIS",
        "17" => "17 - DESISTIMIENTO TÁCITO",
        "18" => "18 - CERTIFICADO RELACIÓN DE INCAPACIDADES NO EVIDENCIA EL PRIMER DIA",
        "19" => "19 - CONCEPTO REHABILITACIÓN CON PRONÓSTICO INDETERMINADO. DEBE SER FAVORABLE O NO FAVORABLE",
        "20" => "20 - CONCEPTO DE REHABILITACIÓN NO REMITIDO POR LA EPS. INCAPACIDADES A CARGO DE ESA ENTIDAD",
        "21" => "21 - AL DÍA 180 DE INCAPACIDAD NO SE ENCUENTRA AFILIADO A COLPENSIONES",
        "28" => "28 - INCAPACIDAD CON DIAGNOSTICO NO RELACIONADO. DEBER SER RECONOCIDA POR SU EPS",
        "29" => "29 - CERTIFICADO DE INCAPACIDAD NO CONTIENE DIAGNÓSTICO",
        "30" => "30 - CERTIFICADO RELACIÓN DE INCAPACIDADES NO CONTIENE DIAGNÓSTICO",
        "31" => "31 - INCAPACIDAD REPETIDA",
        "32" => "32 - NO PROCEDE RECONOCIMIENTO DE INCAPACIDADES. SE RECONOCIÓ Y PAGÓ INDEMNIZACIÓN SUSTITUTIVA",
        "34" => "34 - NO ESTÁ AFILIADO AL SGSS EN CALIDAD DE COTIZANTE",
        "35" => "35 - NO HA COTIZADO COMO MÍNIMO 4 SEMANAS INMEDIATAMENTE ANTERIORES AL INICIO DE LA INCAPACIDAD",
        "36" => "36 - LA INCAPACIDAD TIENE ORIGEN EN TRATAMIENTOS CON FINES ESTÉTICOS",
        "37" => "37 - SE CONFIGURA ALGUNAS DE LAS CAUSALES DE ABUSO DEL DERECHO",
        "38" => "38 - CONCEPTO DE REHABILITACIÓN REMITIDO POR LA EPS CON POSTERIORIDAD AL DÍA 180",
        "39" => "39 - PERIODO NO INLCUIDO EN LA ORDEN JUDICIAL",
        "40" => "40 - TIENE STATUS DE PENSIONADO",
    ];

    public static $WORK_MODALITY = [
        1 => "Riesgo de Trabajo General",
        6 => "Riesgos del Trabajo Independiente",
        7 => "Riesgos del Trabajo único Trabajador",
        2 => "Riesgos del Trabajo Especial Formación Técnica Dual",
        3 => "Riesgos del Trabajo Hogar",
        4 => "Riesgos del Trabajo Ocasional",
        5 => "Riesgos del Trabajo Sector Público",
    ];

    public static $OPTION_ASEGUREMENT = [
        1 => "Una persona permanente contratada para actividades domésticas, además de uno o más trabajadores ocasionales",
        2 => "Dos personas permanentes contratadas para actividades domésticas, además de uno o más trabajadores ocasionales",
        3 => "Tres o más personas permanentes contratadas para actividades domésticas, además de uno o más trabajadores ocasionales",
    ];

    public static $INSTITUTIONAL_SECTOR = [
        11 => "Gobierno central",
        12 => "Instituciones autónomas",
        20 => "Sector privado",
        90 => "Ignorado",
    ];

    public static $VARIATIONS_TYPE = [
        39 => "SOLICITAR CAMBIO VIGENCIA DE POLIZA",
        40 => "SOLICITAR ACTUALIZACION DATOS DE CONTACTO",
        41 => "SOLICITAR ACTUALIZACION OTROS DATOS",
        42 => "SOLICITAR CAMBIO FORMA DE PAGO",
        43 => "SOLICITAR CANCELACION",
        44 => "SOLICITAR REHABILITACION",
    ];

    public static $DOMINANCE = [
        "D" => "DIESTRO",
        "Z" => "ZURDO",
        "A" => "AMBIDIESTRO",
    ];

    public static $CONCAUSAS = [
        "C" => "Concurrentes",
        "S" => "Consecutivas"
    ];


    public static $MONEY_TYPE = [
        "USD" => [
            "name"   => "DÓLARES",
            "symbol" => "$"
        ],
        "CRC" => [
            "name"   => "COLONES",
            "symbol" => "₡"
        ],
    ];

    public static $REGIONAL = [
        0 => 'NO INFORMA',
        1 => 'REGIÓN 1',
        2 => 'REGIÓN 2',
        3 => 'REGIÓN 3',
        4 => 'REGIÓN 4',
    ];

    public static $CALENDAR_PERIOD = [
        "O" => 'Ordinario',
        "E" => 'Extraordinario',
    ];
    public static $CLASIFICACION = [
        1 => 'Principal',
        0 => 'Secundaria',
    ];

    public static $LATERALIDAD = [
        "D" => 'Derecha',
        "I" => 'Izquierda',
        "B" => 'Bilateral',
    ];
    public static $ESTADO_PAGO = [
        "A" => 'Aprobado',
        "R" => 'Rechazado',
        "p" => 'En Proceso (información desde Acsel)',
    ];

    public static $TYPE_CONSTANCY = [
        'policy_certification_up_to_date' => 'Certificado de póliza al día',
        'outstanding_sums_to_be_paid' => 'Constancia de sumas pendientes por pagar',
        'employee_account_statement' => 'Estado de cuenta del trabajador',
        'premiums_paid_certificate' => 'Constancia de primas pagadas',
        'insurance_certificate' => 'Certificado de seguro'
    ];

    public static $TYPE_OF_PAYMENT = [
        "D" => 'Por derecho',
        "O" => 'Por orden judicial',
    ];
    public static $VALIDATION_RESULT = [
        1 => 'Aprobado',
        0 => 'Rechazado',
    ];
    public static $BENEFICIARY = [
        "C" => 'Cónyuge',
        "H" => 'Hijo',
        "M" => 'Madre',
        "P" => 'Padre',
    ];

    public static $TYPE_IT = [
        1 => 'Nueva',
        0 => 'Prórroga',
    ];
    public static $TYPE_ATTENTION = [
        "URG" => "Urgencia",
        "AMB" => "Ambulatoria",
        "HOS" => "Hospitalaria"

    ];


    public static $TYPE_INCOME = [
        "M" => 'Manual',
        "C" => 'Carga masiva',
        "TXT" => 'Cargar archivo TXT',
    ];

    public static $QUALIFIERS = [
        "A" => "ARL",
        "E" => "EPS",
        "F" => "AFP",
        "J" => "JRCI",
        "N" => "JNCI",
        "O" => "OTRA ENTIDAD",
    ];

    public static $MEETING_ATING_OF = [
        '1' => 'Origen',
        '2' => 'Pérdida de capacidad laboral PCL'
    ];

    public static $MEETING_TYPE_EVENT = [
        "1" => 'Accidente de trabajo',
        "2" => 'Accidente común',
        "3" => 'Enfermedad laboral',
        "4" => 'Enfermedad común'
    ];

    public static $MEETING_INTERESTED_PARTY_THAT_DISPUTES = [
        "1" => 'AFP',
        "2" => 'EPS',
        "3" => 'EMPLEADOR',
        "4" => 'AFILIADO',
        "5" => 'ARL'
    ];

    public static $IT_DOCUMENTS = [
        "communication_undetermined_heirs_with_rejection" => 'Plantilla Comunicación a Herederos Indeterminados con Rechazo',
        "communication_undetermined_heirs_without_rejection" => 'Plantilla Comunicación a Herederos Indeterminados sin Rechazo',
        "heirs_payment_with_rejection" => 'Plantilla Oficio de Pago a Herederos con Rechazo',
        "heirs_payment_without_rejection" => 'Plantilla Oficio de Pago a Herederos sin Rechazo',
        "payment_day_to_day_with_rejection" => 'Plantilla Oficio de Pago Día a Día con Rechazo',
        "payment_day_to_day_without_rejection" => 'Plantilla Oficio de Pago Día a Día sin Rechazo',
        "heirs_tutelage_payment_with_rejection" => 'Plantilla Oficio de Pago a Herederos Tutela con Rechazo',
        "heirs_tutelage_payment_without_rejection" => 'Plantilla Oficio de Pago a Herederos Tutela sin Rechazo',
        "tutelage_payment_day_to_day_with_rejection" => 'Plantilla Oficio de Pago Tutela y Día a Día con Rechazo',
        "tutelage_payment_day_to_day_without_rejection" => 'Plantilla Oficio de Pago Tutela y Día a Día sin Rechazo',
        "tutelage_payment_with_rejection" => 'Plantilla Oficio de Pago Tutela con Rechazo',
        "tutelage_payment_without_rejection" => 'Plantilla Oficio de Pago Tutela sin Rechazo',
    ];

    public static $MEETING_ATTACHED_DOCUMENTS = [
        'FUREP Y/O FURAP',
        'FORMULARIO DE SOLICITUD DE DICTAMEN DILIGENCIADO',
        'COPIA DE CONSIGNACION DE PAGO HONORARIOS A LA JUNTA',
        'CALIFICACION DEL ORIGEN Y PERDIDA DE LA CAPACIDAD LABORAL JUNTO CON SU FECHA DE ESTRUCTURACION SI EL PORCENTAJE DE ESTE ULTIMO ES MAYOR A 0',
        'COPIA DE LA NOTIFICACION CALIFICACION DE PERDIDA DE CAPACIDAD LABORAL CON FIRMA Y DE FECHA DE RECIBIDO DEL TRABAJADOR',
        'COPIA DE LA CONTROVERSIA DE LA CALIFICACION DE PERDIDA DE CAPACIDAD LABORAL',
        'COPIA DE LA HISTORIA CLINICA DEL TRABAJADOR QUE REPOSA EN LAS INSTALACIONES DE LA ARL',
        'EVALUACIONES MEDICAS OCUPACIONALES DE INGRESO, PERIODICAS Y DE RETIRO',
        'CONTRATO DE TRABAJO',
        'INFORMACION OCUPACIONAL CON DESCRIPCION DE LA EXPOSICION OCUPACIONAL QUE INCLUYERA LA INFORMACION REFERENTE A LA EXPOSICION',
        'FORMULARIO DE SOLICITUD DE DICTAMEN DILIGENCIADO',
        'FOTOCOPIA DE DOCUMENTO DE IDENTIDAD',
        'CALIFICACION DE ORIGEN Y DE PERDIDA DE CAPACIDAD LABORAL (PCL)',
        'CERTIFICADO O CONSTANCIA DE REHABILITACION O DE SU ESTADO ACTUAL',
        'COPIA COMPLETA DE LA HC',
        'CONCEPTO O RECOMENDACIONES Y/O RESTRICCIONES OCUPACIONALES SI APLICA',
        'COPIA DE LA CONTROVERSIA DE LA CALIFICACION DE ORIGEN'
    ];

    public static $MEETING_AVERAGE_COSTS = [
        "1" => 'Correo fisico',
        "2" => 'Correo electrónico',
        "3" => 'Correo electrónico certificado'
    ];

    public static $MEETING_APPEAL_STATUS = [
        "1" => 'Nuevo',
        "2" => 'Suspensión',
        "3" => 'Fuera de términos',
        "4" => 'Radicado',
        "5" => 'Archivo',
        "6" => 'Desistimiento',
        "7" => 'Pendiente radicar en Junta'
    ];

    public static $MEETING_JRCI_AVERAGE_COST = [
        "1" => 'Correo electrónico',
        "2" => 'Courier',
        "3" => 'Correo electrónico y Courier'
    ];

    public static $MEETING_PRONOUNCEMENT = [
        "1" => 'Acuerdo',
        "2" => 'Desacuerdo'
    ];

    public static $MEETING_JRCI_DICTUM_AVERAGE_COST = [
        "1" => 'Correo electrónico',
        "2" => 'Courier',
        "3" => 'Correo electrónico y Courier'
    ];

    public static $PCL_REQUALIFICATION = [
        "1" => "Accidente de trabajo",
        "2" => "Enfermedad laboral",
        "3" => "Accidente común",
        "4" => "Enfermedad común",
    ];

    public static $REINCORPORATION_CLASS = [
        "1" => "Un reintegro laboral sin modificaciones",
        "2" => "Un reintegro laboral con modificaciones",
        "3" => "Una reubicación laboral temporal",
        "4" => "Una reubicación laboral definitiva",
        "5" => "Una reconversión de mano de obra",
        "6" => "Continuar desempeñando sus actividades cotidianas",
    ];

    public static $ACCIDENT_TYPES = [
        "1" => "Caídas de personas",
        "2" => "Caída de objetos",
        "3" => "Pisadas, choques o golpes por objeto",
        "4" => "Atrapado por un objeto o entre objetos",
        "5" => "Esfuerzo excesivos o falsos movimientos (peso levantado)",
        "6" => "Exposición o contacto con temperaturas extremas",
        "7" => "Exposición o contacto con corriente eléctrica",
        "8" => "Exposición o contacto con sustancias nocivas o radiaciones",
        "9" => "Otra forma de accidente (campo manual)",
        "10" => "Golpes con objetos móviles",
        "11" => "Caídas a un mismo nivel",
        "12" => "Caídas a distinto nivel",
        "13" => "Esfuerzos físicos al levantar objetos",
        "14" => "Falsos movimientos",
        "15" => "Choques con objetos móviles",
        "16" => "Otras caídas de Objetos",
        "17" => "Datos insuficientes",
    ];

    public static $AGENTS = [
        "1" => "Biológicos",
        "2" => "Físicos",
        "3" => "Químicos",
        "4" => "Psicosociales",
        "5" => "Disergonómicos",
        "6" => "Locativos y de estructura",
        "7" => "Naturales",
        "8" => "Mecánicos",
        "10" => "Eléctricos",
        "9" => "Otros agentes (campo manual)",
    ];

    public static $AGENTS_INFO = [
        "1" => "Virus, Bacterias, Hongos, Ricketsias, Parásitos, Picaduras, Mordeduras, Fluidos o excrementos.",
        "2" => "Ruido, Iluminación, Vibración (cuerpo entero, Temperaturas extremas, Radiaciones ionizantes y no ionizantes, Presión atmosférica.",
        "3" => "Polvos orgánicos inorgánicos, Fibras, Líquidos, Gases y vapores, Humos metálicos y no metálicos.",
        "4" => "Sin información.",
        "5" => "Postura, esfuerzo, movimiento repetitivo, manipulación manual de cargas.",
        "6" => "Colapso, Deslizamientos, Inestabilidad estructural (techos, paredes, pisos, pasarelas), Hacinamiento, Distribución de espacios, Espacios confinados, Seguridad humana y sus componentes, Exposición a caídas por diferencia de altura, Orden y limpieza, Sistemas de protección contra incendios.",
        "7" => "Sismo, Incendio, Vendaval, Inundación, Derrumbe, Precipitaciones.",
        "8" => " Aceleración, desaceleración, Aproximación de un elemento móvil a una parte fija, Partes cortantes, Elementos elásticos, Caída de objetos, Energía acumulada, Altura desde el suelo, Alta presión, Movilidad de la máquina, Elementos móviles, Elementos rotativos, Superficie rugosa, deslizante.",
        "10" => "Choque eléctrico (por contacto directo o indirecto), Arco eléctrico (Relámpago y ráfago), Fenómeno electromagnético, Fenómeno electrostático, Distancia insuficiente a partes activas, alta tensión, Sobrecarga, Cortocircuito.",
        "9" => "Sin información.",
    ];

    public static $INJURIES = [
        "1" => "Atrapamiento",
        "2" => "Atropellos o golpes con vehículos",
        "3" => "Caída de objetos",
        "4" => "Caídas al agua",
        "5" => "Caídas al mismo nivel",
        "6" => "Caídas de altura",
        "7" => "Choque contra objetos y/o personas",
        "8" => "Choque, colisión o volcamiento",
        "9" => "Contacto con animales y/o insectos",
        "10" => "Contacto con otras sustancias químicas",
        "11" => "Contacto con personas",
        "12" => "Contacto con sustancias cáusticas y/o corrosivas",
        "13" => "Contactos eléctricos directos alta tensión",
        "14" => "Contactos eléctricos directos baja tensión",
        "15" => "Contactos térmicos por calor",
        "16" => "Contactos térmicos por frío",
        "17" => "Cortes por objetos / herramientas cortopunzantes",
        "18" => "Exigencias psicológicas en el trabajo",
        "19" => "Explosiones",
        "20" => "Exposición a aerosoles líquidos",
        "21" => "Exposición a aerosoles sólidos",
        "22" => "Exposición a altas presiones",
        "23" => "Exposición a ambientes con deficiencia de oxígeno",
        "24" => "Exposición a bajas presiones",
        "25" => "Exposición a calor",
        "26" => "Exposición a frío",
        "27" => "Exposición a gases y vapores",
        "28" => "Exposición a radiaciones ionizantes",
        "29" => "Exposición a radiaciones no ionizantes",
        "30" => "Exposición a ruido",
        "31" => "Exposición a sustancias químicas tóxicas",
        "32" => "Exposición a vibraciones",
        "33" => "Golpes",
        "34" => "Ingesta de sustancias nocivas",
        "35" => "Proyección de fragmentos y/o partículas",
        "36" => "Acoso laboral",
        "37" => "Burnout o síndrome del quemado",
        "38" => "Estrés laboral",
        "39" => "Exigencias psicológicas en el trabajo",
        "40" => "Violencia en el trabajo",
        "41" => "Sobrecarga física debido a la manipulación de personas/pacientes",
        "42" => "Sobrecarga física debido al trabajo repetitivo de las extremidades superiores",
        "43" => "Sobrecarga física debido al trabajo repetitivo de las extremidades inferiores",
        "44" => "Sobrecarga postural debido a otras posturas forzadas",
        "45" => "Sobrecarga postural debido a trabajo arrodillado",
        "46" => "Sobrecarga postural debido a trabajo de pie",
        "47" => "Sobrecarga postural debido a trabajo en cuclillas (agachado)",
        "48" => "Sobrecarga postural debido a trabajo fuera del alcance",
        "49" => "Sobrecarga postural debido a trabajo sentado",
        "50" => "Sobrecarga postural debido a tronco inclinado, en torsión o lateralización",
        "51" => "Torcedura",
        "52" => "Transmisión aérea, hídrica y por contacto exposición a agentes biológicos",
        "53" => "Transmisión por sangre y fluidos inoculación de agentes biológicos",
        "54" => "Otros: espacio editable",
    ];


    public static $WORK_MODES = [
        "1" => "Presencial",
        "2" => "Teletrabajo",
        "3" => "Híbrido",
    ];


    public static $WORKPLACE_LOCATIONS_OLD = [
        "1" => "Puesto de trabajo",
        "2" => "Casa",
        "3" => "Trayecto",
        "4" => "En comisión",
        "5" => "Dentro del centro de trabajo",
        "6" => "Fuera del centro del trabajo",
        "7" => "Labores o trabajos no habituales fuera del centro de trabajo",
        "8" => "Otro lugar dentro del centro de trabajo",
    ];

    public static $WORKPLACE_LOCATIONS = [
        "1" => "Puesto de trabajo habitual, dentro o fuera del centro de trabajo",
        "2" => "Otro lugar dentro del centro de trabajo",
        "3" => "Labores de trabajo no habituales, fuera del centro de trabajo",
        "4" => "In Itinere "
    ];



    public static $COLPENSIONES_GPA_ORIGINS = [
        'C' => 'COMÚN',
        'L' => 'LABORAL',
    ];

    public static $ENTITY_NAME_PERITAJE = [
        00 =>
        [
            "phone" => "7484888",
            "nit" => "800149496",
            "name" => "COLFONDOS",
            "address" => "Calle 67 No 7-94, Bogotá",
            "email" => "<EMAIL> <EMAIL>",
            "city" => "BOGOTÁ D.C. - BOGOTÁ"
        ],
        0 =>
        [
            "phone" => "4890909",
            "nit" => "900336004-7",
            "name" => "COLPENSIONES",
            "address" => "Carrera 9 No. 59-43 Bogotá",
            "email" => "<EMAIL>",
            "city" => "BOGOTÁ D.C. - BOGOTÁ"
        ],
        2 =>
        [
            "phone" => "7447678",
            "nit" => "800144331-3",
            "name" => "PORVENIR ( BBVA HORIZONTE )",
            "address" => "Cra 13 N° 26A-65, Torre B, Bogotá",
            "email" => "<EMAIL>; <EMAIL>",
            "city" => "BOGOTÁ D.C. - BOGOTÁ"
        ],
        3 =>
        [
            "phone" => "2307500",
            "nit" => "800229739",
            "name" => "PROTECCION",
            "address" => "Carrera 49 # 63-100 torre proteccion, medellin",
            "email" => "<EMAIL>",
            "city" => "BOGOTÁ D.C. - BOGOTÁ"
        ],
        4 =>
        [
            "phone" => "01 8000 120 100",
            "nit" => "800148514-2",
            "name" => "SKANDIA",
            "address" => "Avenida 19 No. 109A 30, Bogotá",
            "email" => "<EMAIL>",
            "city" => "BOGOTÁ D.C. - BOGOTÁ"
        ],
        99 =>
        [
            "phone" => "01 8000 519 991",
            "nit" => "830054904-6",
            "name" => "MAPFRE COLOMBIA VIDA SEGUROS",
            "address" => "Carrera 14, 96 – 34 Piso 4",
            "email" => "<EMAIL>",
            "city" => "BOGOTÁ D.C. - BOGOTÁ"
        ],
        5 =>
        [
            "phone" => "4449448",
            "nit" => "811044203",
            "name" => "JUNTA REGIONAL DE CALIFICACIÓN DE INVALIDEZ ANTIOQUIA",
            "address" => "Calle 27 Nº 46-70, Local 225 Medellín",
            "email" => "<EMAIL>",
            "city" => "MEDELLÍN - ANTIOQUIA"
        ],
        8 =>
        [
            "phone" => "3491206",
            "nit" => "802016503",
            "name" => "JUNTA REGIONAL DE CALIFICACIÓN DE INVALIDEZ ATLANTICO",
            "address" => "Carrera 54 No. 58 – 78",
            "email" => "<EMAIL>",
            "city" => "BARRANQUILLA - ATLÁNTICO"
        ],
        11 =>
        [
            "phone" => "7953160",
            "nit" => "830106999",
            "name" => "JUNTA REGIONAL DE CALIFICACIÓN DE INVALIDEZ BOGOTÁ Y CUNDINAMARCA",
            "address" => "Calle 50 No. 25 - 37 Galerías",
            "email" => "<EMAIL>",
            "city" => "BOGOTÁ D.C. - BOGOTÁ"
        ],
        13 =>
        [
            "phone" => "6567744 -3008367707",
            "nit" => "806008908",
            "name" => "JUNTA REGIONAL DE CALIFICACIÓN DE INVALIDEZ BOLIVAR",
            "address" => "Carrera 21 No. 29 A – 72",
            "email" => "<EMAIL>",
            "city" => "CARTAGENA - BOLIVAR"
        ],
        15 =>
        [
            "phone" => "(098)7431578",
            "nit" => "900020280-6",
            "name" => "JUNTA REGIONAL DE CALIFICACIÓN DE INVALIDEZ BOYACA",
            "address" => "Calle 47 No. 1 – 44 Barrio José de las Flores sector las quintas",
            "email" => "<EMAIL>",
            "city" => "TUNJA - BOYACÁ"
        ],
        17 =>
        [
            "phone" => "(6) 8850409",
            "nit" => "900600849",
            "name" => "JUNTA REGIONAL DE CALIFICACIÓN DE INVALIDEZ CALDAS",
            "address" => "carrera 23C No. 64 A-10",
            "email" => "<EMAIL>",
            "city" => "MANIZALES - CALDAS"
        ],
        20 =>
        [
            "phone" => "5846423-3012009076-0355847689",
            "nit" => "824003600",
            "name" => "JUNTA REGIONAL DE CALIFICACIÓN DE INVALIDEZ CESAR",
            "address" => "Calle 13 A No. 14 – 93 Barrio Obrero",
            "email" => "<EMAIL>",
            "city" => "VALLEDUPAR - CESAR"
        ],
        41 =>
        [
            "phone" => "8716314",
            "nit" => "813008428",
            "name" => "JUNTA REGIONAL DE CALIFICACIÓN DE INVALIDEZ HUILA",
            "address" => "Carrera 5 No. 10 – 49 Centro Comercial Plaza Real Oficina 305",
            "email" => "<EMAIL>",
            "city" => "NEIVA - HUILA"
        ],
        47 =>
        [
            "phone" => "3008662909 - 4301053",
            "nit" => "819001283",
            "name" => "JUNTA REGIONAL DE CALIFICACIÓN DE INVALIDEZ MAGDALENA",
            "address" => "Calle 22 No. 19 B – 46 ",
            "email" => "<EMAIL>",
            "city" => "SANTA MARTA - MAGDALENA"
        ],
        50 =>
        [
            "phone" => "6662070-3138709023",
            "nit" => "822001390",
            "name" => "JUNTA REGIONAL DE CALIFICACIÓN DE INVALIDEZ META",
            "address" => "Calle 35 A No. 41 – 39 Barrio El Barzal ",
            "email" => "<EMAIL>",
            "city" => "VILLAVICENCIO - META"
        ],
        1 =>
        [
            "phone" => "7440737",
            "nit" => "830026324-5",
            "name" => "JUNTA NACIONAL DE CALIFICACIÓN DE INVALIDEZ",
            "address" => "Av Park Way - Diag. 36 Bis # 20 - 74 Barrio La Soledad",
            "email" => "<EMAIL>",
            "city" => "BOGOTÁ D.C. - CUNDINAMARCA"
        ],
        52 =>
        [
            "phone" => "7294549-7294552",
            "nit" => "900587628 -9",
            "name" => "JUNTA REGIONAL DE CALIFICACIÓN DE INVALIDEZ NARIÑO",
            "address" => "Calle 12 No. 27-112 Barrio San Felipe ",
            "email" => "<EMAIL>",
            "city" => "PASTO - NARIÑO"
        ],
        54 =>
        [
            "phone" => "5725418",
            "nit" => "807007370",
            "name" => "JUNTA REGIONAL DE CALIFICACIÓN DE INVALIDEZ NORTE DE SANTANDER",
            "address" => "Calle 11 A No. 1E – 25 Barrio Caobos Edificio Gales Urbanización Quinta Vélez apto 101 ",
            "email" => "<EMAIL>",
            "city" => "CÚCUTA - NORTE DE SANTANDER"
        ],
        63 =>
        [
            "phone" => "7443654, 3177483715",
            "nit" => "801000451-4",
            "name" => "JUNTA REGIONAL DE CALIFICACIÓN DE INVALIDEZ QUINDIO",
            "address" => "CR 13 No. 19 - 09 Lc 4 P MENOS 1 CC Altavista ",
            "email" => "<EMAIL>",
            "city" => "ARMENIA - QUINDIO"
        ],
        66 =>
        [
            "phone" => "3252589",
            "nit" => "900605547-9",
            "name" => "JUNTA REGIONAL DE CALIFICACIÓN DE INVALIDEZ RISARALDA",
            "address" => "Carrera 7 calle 18-21 Oficina 801 Edificio Antonio Correa ",
            "email" => "<EMAIL>",
            "city" => "PEREIRA - RISARALDA"
        ],
        68 =>
        [
            "phone" => "6574295-6576094",
            "nit" => "804.000.705-0",
            "name" => "JUNTA REGIONAL DE CALIFICACIÓN DE INVALIDEZ SANTANDER",
            "address" => "Carrera 37 No. 44-74 Cabecera ",
            "email" => "<EMAIL>",
            "city" => "BUCARAMANGA - SANTANDER"
        ],
        73 =>
        [
            "phone" => "2666426",
            "nit" => "809012454-7",
            "name" => "JUNTA REGIONAL DE CALIFICACIÓN DE INVALIDEZ TOLIMA",
            "address" => "Carrera 4 # 40-84",
            "email" => "<EMAIL>",
            "city" => "IBAGUÉ - TOLIMA"
        ],
        76 =>
        [
            "phone" => "5531020 – 5531015-5531092 – 5536036",
            "nit" => "805012111",
            "name" => "JUNTA REGIONAL DE CALIFICACIÓN DE INVALIDEZ VALLE DEL CAUCA",
            "address" => "Calle 5 E 42 A – 05 Barrio Tequendama",
            "email" => "<EMAIL>",
            "city" => "CALI - VALLE DEL CAUCA"
        ],
    ];

    public static $RECOMMENDATIONS_TERMS = [
        "5" => "RECOMENDACIÓN 1 VEZ",
        "6" => "RENOVACIÓN",
        "7" => "RENOVACIÓN Y NUEVO DX",
        "8" => "DX DIFERENTE AL EMITIDO INICIALMENTE",
    ];

    public static $CATEGORIZATION_INCIDENT = [
        /*"1" => "En estudio",
        "2" => "Propios del trabajo",
        "3" => "Deportivo",
        "4" => "Recreativo o cultural",
        "5" => "Violencia",
        "6" => "Tránsito",
        "7" => "Biológico",
        "8" => "SOA",
        "9" => "Común",
        "10" => "CCSS",
        TODO: se dejan comentadas opciones anteriores por si se llegan a requerir de nuevo*/
        "1" => "Laboral",
        "2" => "Común",
        "3" => "No derivado del accidente de trabajo",
        "4" => "En estudio",
        "5" => "Incidente",
        "6" => "No derivado del evento",
        "7" => "Derivado del accidente del trabajo",
        "8" => "Derivado del evento",
    ];

    public static $HIGH_COST = [
        "1" => "No aplica",
        "2" => "Amputación de miembro superior o inferior",
        "3" => "Cáncer ocupacional",
        "4" => "Compromiso severo corazón",
        "5" => "Compromiso severo hígado",
        "6" => "Compromiso severo renal",
        "7" => "Intervenciones complejas múltiples",
        "8" => "Manejo de trauma mayor",
        "9" => "Pérdida ocular",
        "10" => "Procedimiento que compromete severamente el sistema nervioso central",
        "11" => "Quemaduras de 2do y 3er grado, en más del 20% de superficie corporal total",
        "12" => "Trasplante de córnea",
        "13" => "Trasplante de corazón",
        "14" => "Trasplante de hígado",
        "15" => "Trasplante renal",
        "16" => "Trauma raquimedular",
        "17" => "UCI",
        "18" => "VIH"
    ];


    public static $SEVERITY = [
        /*"1" => "Alta inmediata",
        "2" => "Muy Leve",
        "3" => "Leve",
        "4" => "Moderado",
        "5" => "Severo",
        "6" => "Grave",
        "7" => "Crónico",
        "8" => "Mortal"
        TODO: se dejan comentadas opciones anteriores por si se llegan a requerir de nuevo*/
        "1" => "Leve",
        "2" => "Moderado",
        "3" => "Grave",
        "4" => "Fatal",
    ];
    public static $ACTUARIAL_SEVERITY = [
        "1" => "Leve",
        "2" => "Moderada",
        "3" => "Severo",
    ];
    public static $PATIENT_PROGNOSIS = [
        "1" => "Corto menor a 1 mes",
        "2" => "Mediano menor a tres meses",
        "3" => "Largo mayor a seis meses",
    ];
    public static $OPTIONS = [
        "1" => "RECIBIDO",
        "2" => "POSIBLE",
        "3" => "NO APLICA",
    ];

    public static $TREATMENT_PURPOSE = [
        "1" => "Curativa",
        "2" => "Paleativa",
    ];



    public static $RECOMMENDATIONS_FORMATS = [
        "6" => "FORMATO 1 VEZ",
        "7" => "FORMATO 2 VEZ",
    ];

    public static $INFORMATION_SOURCES_PCL = [
        "22" => "PQR o FAMIGOS",
        "25" => "SOLICITUD DIRECTA",
        "23" => "TUTELAS",
        "11" => "",
    ];


    public static $ENABLED_STATES_COBRO = [
        StatePolicySortCollection::RECIBO_AUMENTO_SEGURO_PENDIENTE_PAGO,
        StatePolicySortCollection::RECIBO_ABONO_MENSUAL_PENDIENTE_PAGO,
        StatePolicySortCollection::RECIBO_ABONO_TRIMESTRAL_PENDIENTE_PAGO,
        StatePolicySortCollection::RECIBO_ABONO_SEMESTRAL_PENDIENTE_PAGO,
        StatePolicySortCollection::RECIBO_REHABILITACION_PENDIENTE_PAGO,
        StatePolicySortCollection::PENDIENTE_PAGO_RECIBO_EMISION,
        StatePolicySortCollection::RECIBO_LIQUIDACION_PENDIENTE_PAGO,
        StatePolicySortCollection::RECIBO_RENOVACION_PENDIENTE_PAGO
    ];

    public static $RHB_FORMATS = [
        "1" => "Dx Catastróficos",
        "2" => "Formato 1 vez",
        "3" => "Formato 2 Vez - Mismo Dx",
        "4" => "Formato 2 Vez - Otro Dx",
        "5" => "Tutela",
        "8" => "Formato 3 Vez - Mismo Dx",
        "9" => "Formato 3 Vez - Otro Dx",
    ];

    public static $RHB_TREATMENTS = [
        "0" => "Vencido Formato 3 vez",
        "1" => "Remitido en terminos",
        "3" => "Vencido RHB",
        "9" => "Vencido Formato 2 vez",
    ];

    public static $DCRT_GRADES = [
        "1" => 'MODERADO: 15 - 25%',
        "2" => 'SEVERO: 25 AL 49.9%',
        "3" => 'PROFUNDO: MAYOR A 50%',
    ];

    public static $DCRT_TYPES = [
        "1" => 'MODERADO',
        "2" => 'SEVERO',
        "3" => 'PROFUNDO',
    ];

    public static $SCHOOL_LEVELS = [
        1 => "ANALFABETA",
        2 => "PREESCOLAR",
        3 => "PRIMARIA BÁSICA",
        4 => "BÁSICA",
        6 => "MEDIA",
        7 => "UNIVERSITARIA",
        8 => "POST GRADOS",
        9 => "TECNOLÓGICA",
        10 => "OTROS",
    ];
    public static $SCHOOLING = [
        0 => "SIN ESCOLARIDAD",
        1 => "PRIMARIA",
        2 => "SECUNDARIA",
        3 => "BACHILLERATO",
        4 => "LICENCIATURA",
        6 => "DOCTORADO"
    ];
    public static $DOC_TYPES = [
        "CF" => "Cédula física",
        "CJ" => "Cédula jurídica",
        "CD" => "Carnet diplomático",
        "DI" => "Dimex",
        "PA" => "Pasaporte",
        "CR" => "Cédula de residencia"
    ];
    public static $STATES_REINTEGRATE = [
        "approve" => "Aprobar",
        "reject" => "Rechazar",
        "request_info" => "Solicitar Información"
    ];
    public static $PERIODICITY_RECEIPTS = [
        "1" => "annual_payment_receipt",
        "2" => "biannual_payment_receipt",
        "3" => "quarterly_payment_receipt",
        "4" => "monthly_payment_receipt",
        "" => "one_time_payment_receipt",

        'emission' => 'issuance_receipt',
        'period_increase' => 'increase_receipt',
        'monthly_payment' => 'monthly_payment_receipt',
        'quarterly_payment' => 'quarterly_payment_receipt',
        'semiannual_payment' => 'biannual_payment_receipt',
        'rehabilitation' => 'rehabilitation_receipt',
        'liquidation' => 'settlement_receipt',
        'renewal' => 'renewal_receipt',
    ];

    public static $STATES_RESULT_REINTEGRATE = [
        "empty" => "",
        "pending_info" => "Pendiente de información",
        "all_approved" => "Aprobado",
        "all_rejected" => "Rechazado",
        "partial_approved" => "Parcialmente aprobado",
    ];

    public static $GENDERS = [
        "" => " ",
        "M" => "MASCULINO",
        "F" => "FEMENINO",
        "NB" => "NO BINARIO",
    ];
    public static $TIPO_JORNADA_LABORAL = [
        "TC" => "Tiempo completo",
        "TM" => "Tiempo medio",
        "OD" => "Ocasional contratado por días",
        "OH" => "Ocasional contratado por horas"
    ];

    public static $CIVIL_STATUS = [
        1 => "SOLTERO",
        2 => "CASADO",
        3 => "SEPARADO",
        4 => "VIUDO",
        6 => "UNIÓN LIBRE",
        7 => "DIVORCIADO",
    ];

    public static $REQUEST_REASON = [
        "calificacion_pcg" => "Calificación de PCG",
        "cierre_secuela" => "Cierre sin secuelas",
        "seguimiento_rhb" => "Seguimiento RHB",
        "adicion_dx" => "Adición Dx",
    ];

    public static $SPECIALTY = [
        "PRE" => "Preanestesica",
        "ANE" => "Anestesiología",
        "ASS" => "Administración Servicios de Salud",
        "CIRG" => "Cirugía General",
        "CVC" => "Cirugía cardiovascular",
        "TOR" => "Cirugía del Tórax o Torácica",
        "PED" => "Cirugía Infantil o Pediátrica",
        "CIRP" => "Cirugía Plástica",
        "CCS" => "Cirugía-Clinica del Seno",
        "DER" => "Dermatología",
        "HEMP" => "Hematología Pediátrica",
        "MAX" => "Cirugía Maxilofacial",
        "NUC" => "Medicina Nuclear",
        "GEN" => "Genética",
        "AVI" => "Medicina de aviación",
        "ODP" => "Odontología Pediátrica",
        "END" => "Endodoncia",
        "PER" => "Periodoncia",
        "ORT" => "Ortodoncia",
        "ROH" => "Rehabilitación Oral",
        "ODON" => "Odontología Oncológica",
        "DEP" => "Medicina Deportiva",
        "PROC" => "Proctología",
        "INA" => "Especialidad inactiva 20080717",
        "OPC" => "Ortopedia de pie y cuello de pie",
        "AMB" => "Medicamentos Ambulatorios",
        "CIRCR" => "Cirugía de Colon y de Recto",
        "COL" => "Ortopedia de columna",
        "ONCP" => "Cirugía pediátrica oncológica",
        "ONC" => "Cirugía Oncológica",
        "MANO" => "Cirugía de la mano Plástica",
        "HOM" => "Homeopatía",
        "GER" => "Geriatría",
        "ACU" => "Acupuntura",
        "DOON" => "Dermatología Oncológica",
        "TOX" => "Toxicología",
        "HEMONP" => "Hemato-oncología Pediátrica",
        "RHEP" => "Reumatología Pediátrica",
        "ORMAX" => "Ortopedia Maxilar",
        "BIO" => "Medicina Bioenergética",
        "HEON" => "Hemato - Oncología",
        "HOMEP" => "Homeopatía Pediátrica",
        "HEPA" => "Hepatología",
        "GYN" => "Ginecología y Obstetricia",
        "ORB" => "Orbitología-oftalmología",
        "OCU" => "Consulta de Oculoplastia",
        "SEX" => "Sexología",
        "PSYCH" => "Medicina Psicosomática",
        "PODO" => "Podología",
        "PREV" => "Medicina Preventiva",
        "ALT" => "Medicina Alternativa",
        "COR" => "Corneólogo",
        "SOCC" => "Salud Ocupacional",
        "IMM" => "Inmunología",
        "PEDP" => "Pediatría Prioritaria",
        "GENP" => "Medicina General Prioritaria",
        "HEART" => "Salud Administrada Corazón Sano",
        "DIAB" => "Salud Administrada Diabetes",
        "LAS" => "Consulta Estética Láser",
        "ODPR" => "Cons prioritaria por odontología gral",
        "ODSP" => "Cons prioritaria por odontología espec",
        "MEDDOM" => "Medicina General Domiciliaria",
        "SPEC" => "Medicina Especializada Domiciliaria",
        "RES" => "Terapia Respiratoria Domiciliaria",
        "FAM" => "Medicina Familiar",
        "PHY" => "Terapia Física Domiciliaria",
        "AUDIO" => "Fonoaudiología Domiciliaria",
        "OCC" => "Terapia Ocupacional Domiciliaria",
        "HAND" => "Cirugía Mano Ortopedia",
        "OCCMED" => "Medicina Laboral",
        "REHAB" => "Medicina Física-Rehabilitación",
        "ONCPED" => "Oncología Pediátrica",
        "ELEC" => "Electrofisiología",
        "GLA" => "Glaucoma",
        "HEMO" => "Hemodinamia",
        "OOP" => "Oftalmología Oncológica",
        "HIP" => "Ortopedia de Cadera",
        "SHOULDER" => "Ortopedia de Hombro",
        "ONCOPED" => "Ortopedia Oncológica",
        "KNEE" => "Ortopedia de Rodilla",
        "MEDIN" => "Medicina Interna",
        "ONCOT" => "Otorrinolaringología Oncológica",
        "RET" => "Retinología",
        "MAXO" => "Cirugía Maxilofacial (odontología)",
        "STO" => "Estomatología",
        "PALL" => "Cuidado Paliativo",
        "UROON" => "Urología Oncológica",
        "NEUROINF" => "Neuroinfectología",
        "PLASONC" => "Cirugía Plástica Oncológica",
        "RADION" => "Radioneurología",
        "CARD" => "Cardiología",
        "NEUROOP" => "Neuro-oftalmología",
        "DESTE" => "Dermatología Estética",
        "GASTROONC" => "Cirugía Gastrointestinal Oncológica",
        "PER" => "Perinatología",
        "ARTHRO" => "Artroscopista",
        "VACC" => "Vacunación",
        "URG" => "Consulta Prioritaria",
        "PEDURG" => "Consulta Prioritaria Pediatría",
        "PSYCHG" => "Psicogeriatría",
        "ENDO" => "Endocrinología",
        "NEUROOT" => "Neuro otología",
        "MIS" => "Cirugía Mínima Invasión",
        "ONCONEU" => "Neurología Oncológica",
        "ONCOOR" => "Oncología Oral",
        "NEUROINT" => "Neurología Intervencionista",
        "GASTRO" => "Cirugía Gastrointestinal",
        "ODES" => "Odontología Estética",
        "LAPGYN" => "Laparoscopista Ginecológico",
        "EYE" => "Cirugía Oftalmológica",
        "OTO" => "Otología",
        "GASTR" => "Gastroenterología",
        "LEV1" => "Nivel I",
        "LEV2" => "Nivel II",
        "LEV3" => "Nivel III",
        "LEV4" => "Nivel IV",
        "VERT" => "Vértigo y Equilibrio",
        "LARYN" => "Laringología",
        "RADIO" => "Radiocirugía",
        "OCUPRO" => "Oculista Protesista",
        "PSYONC" => "Psicología Oncológica",
        "ORAL" => "Cirugía Oral",
        "HEMA" => "Hematología",
        "LAP" => "Cirugía Laparoscopista",
        "CARDPED" => "Cirugía Cardiovascular Pediátrica",
        "EPILE" => "Epileptología",
        "HEPATPED" => "Hepatología Pediátrica",
        "HB" => "Cirugía Hepatobiliar",
        "NUT" => "Nutriología",
        "NUTONC" => "Nutrición Oncológica",
        "PATO" => "Patología y Cirugía Oral",
        "NEUROINTV" => "Neurorradiología Intervencionista",
        "NUTPED" => "Nutrición Pediátrica",
        "NEPH" => "Nefrología",
        "FERT" => "Fertilidad",
        "STRA" => "Estrabismo",
        "PAIN" => "Clínica del Dolor",
        "PALLPED" => "Cuidado Paliativo Pediátrico",
        "BARIPED" => "Cirugía Bariátrica Pediátrica",
        "BARI" => "Cirugía Bariátrica",
        "TRAN" => "Trasplantes",
        "RADINT" => "Radiología Intervencionista",
        "ELECSP" => "Electrofisiología Pediátrica",
        "NEUM" => "Neumología",
        "HEMODPED" => "Hemodinamia Pediátrica",
        "FAMCOM" => "Salud Familiar y Comunitaria",
        "CARDANES" => "Anestesiología Cardiovascular",
        "AYUR" => "Medicina Alternativa (Ayurveda)",
        "NATURO" => "Medicina Alternativa (Naturopatía)",
        "FORENS" => "Medicina Forense",
        "OSTEOP" => "Medicina Alternativa (Osteopatía)",
        "PHYTP" => "Medicina Alternativa (Fisioterapia)",
        "RHE" => "Reumatología",
        "PEDOT" => "Otorrinolaringología Pediátrica",
        "GYNE" => "Ginecología Endocrinológica",
        "NEPH" => "Nefrología Pediátrica",
        "NEURS" => "Neurocirugía",
        "NEURO" => "Neurología",
        "NEUP" => "Neurología Infantil",
        "OFT" => "Oftalmología",
        "ONC" => "Oncología Clínica",
        "ORTH" => "Ortopedia y Traumatología",
        "OTO" => "Otorrinolaringología",
        "PATH" => "Patología o Anatomía Patológica",
        "PED" => "Pediatría",
        "PSYCH" => "Psiquiatría",
        "PSYC" => "Psicología",
        "RAD" => "Radiología e Imágenes Diagnósticas",
        "SLP" => "Terapia del lenguaje",
        "PT" => "Terapia Física",
        "OT" => "Terapia Ocupacional",
        "URO" => "Urología"
    ];

    // Nivel de la atención
    public static $CARE_LEVEL = [
        'Primer nivel' => 'Primer nivel',
        'Segundo nivel' => 'Segundo nivel',
        'Tercer nivel' => 'Tercer nivel',
    ];

    // Modalidad de la atención
    public static $CARE_MODALITY = [
        'Telemedicina' => 'Telemedicina',
        'Presencial en sitio' => 'Presencial en sitio',
        'Presencial en centro médico' => 'Presencial en centro médico',
    ];


    public static $SENDERS = [
        "COLPENSIONES" => "COLPENSIONES",
    ];

    public static $ORIGINS = [
        'C' => 'COMÚN',
        'L' => 'LABORAL',
        'N' => 'ND EVENTO',
        'E' => 'EN ESTUDIO',
        'M' => 'MIXTO',
    ];

    public static $ORIGINS_PCL = [
        'C' => 'COMÚN',
        'L' => 'LABORAL',
    ];

    public static $ORIGINS_AT = [
        'C' => 'COMÚN',
        'L' => 'LABORAL',
        'N' => 'ND EVENTO',
        'E' => 'EN ESTUDIO',
        'M' => 'MIXTO',
        'D' => 'DERIVADO DEL EVENTO',
    ];

    public static $LATERALITY = [
        'D' => 'Derecha',
        'I' => 'Izquierda',
        'B' => 'Bilateral',
        'N' => 'No aplica',
    ];

    public static $DIAGNOSTIC_STATUS = [
        'A' => 'Activo',
        'R' => 'Resuelto',
    ];

    public static $ROOT_CAUSE = [
        '1' => 'AUTORIZACIÓN DE PRESTACIONES ASISTENCIALES',
        '2' => 'RECOMENDACIONES LABORALES',
        '3' => 'CALIFICACIÓN DE ORIGEN DE AT',
        '4' => 'CALIFICACIÓN DE ORIGEN DE EL',
        '5' => 'CALIFICACIÓN DE PCL',
        '6' => 'RECALIFICACIÓN DE PCL',
        '7' => 'CALIFICACIÓN INETGRAL DE PCL',
        '8' => 'RESPUESTA A DP / PQRS',
        '9' => 'REMISION A JUNTAS DE CALIFICACIÓN',
        '10' => 'NOTIFICACIÓN DE DICTAMEN',
        '11' => 'ENTREGA DE MEDICAMENTOS O INSUMOS AUTORIZADOS',
        '12' => 'PAGO INCAPACIDADES ',
        '13' => 'REEMBOLSOS',
        '14' => 'PRETENSIONES VARIAS',
        '15' => 'SOLICITUD DE INFORMACIÓN',
        '16' => 'FALTA DE ATENCIÓN MEDICA TENIENDO DERECHO',
        '17' => 'REINTEGRO LABORAL',
        '18' => 'PAGO DE IPP',
        '19' => 'RELIQUIDACION IPP',
        '20' => 'AUTORIZACION GENERADA',
        '21' => 'IT YA RECONOCIDA',
    ];

    public static $TYPE_DISABILITY = [
        'F' => 'Discapacidad Física',
        'M' => 'Discapacidad Mental',
        'C' => 'Discapacidad Cognitiva',
        'A' => 'Discapacidad Auditiva',
        'V' => 'Discapacidad Visual',
        'MU' => 'Discapacidad Múltiple',
    ];

    public static $IT_ORIGINS = [
        "1" => "MATERNIDAD",
        "2" => "ENFERMEDAD GENERAL",
        "3" => "ENFERMEDAD GENERAL - SOAT",
        "4" => "ENFERMEDAD LABORAL",
        "5" => "ACCIDENTE DE TRABAJO",
        "6" => "ACCIDENTE DE TRABAJO – SOAT",
    ];

    public static $RADICATION_TYPE = [
        '1' => 'Enfermedad  General',
        '10' => 'Menor y Mujer Maltratados',
        '2' => 'Maternidad',
        '11' => 'Paternidad',
        '12' => 'Laboral',
        '3' => 'Accidente de trabajo',
        '4' => 'Enfermedad  Profesional',
        '5' => 'Accidente de Transito',
        '6' => 'Enfermedad  Catastrófica',
        '7' => 'Promoción Y Prevención',
        '8' => 'Descanso Remunerado Por Aborto',
        '9' => 'Tratamiento Integral',
    ];

    public static $GENERAL_INFORMATION_FIELDS = [
        "DATOS DEL AFILIADO" => [
            "NAME" => "DATOS DEL AFILIADO",
            "OPTIONS" => [
                'Tipo de Identificación' => 'Tipo de Identificación',
                'Número de Identificación' => 'Número de Identificación',
                'Nombres y Apellidos' => 'Nombres y Apellidos',
                'Edad' => 'Edad',
                'Sexo' => 'Sexo',
                'Distrito/Regional' => 'Distrito/Regional',
                'EPS' => 'EPS',
                'Teléfono' => 'Teléfono',
                'Celular' => 'Celular',
                'Dirección de Residencia' => 'Dirección de Residencia',
                'Departamento de Residencia' => 'Departamento de Residencia',
                'Ciudad de Residencia' => 'Ciudad de Residencia',
                'Estado Civil' => 'Estado Civil',
                'Escolaridad' => 'Escolaridad',
            ]
        ],
        "GESTIÓN DEL RADICADO BIZAGI" => [
            "NAME" => "GESTIÓN DEL RADICADO BIZAGI",
            "OPTIONS" => [
                'Fecha de Radicación' => 'Fecha de Radicación',
                'N° Radicado Bizagi' => 'N° Radicado Bizagi',
                'Fecha Asignación Proveedor' => 'Fecha Asignación Proveedor',
                'Proveedor Asignado-Validación Documental' => 'Proveedor Asignado-Validación Documental',
            ]
        ],
        "PAGO A HEREDEROS" => [
            "NAME" => "PAGO A HEREDEROS",
            "OPTIONS" => [
                'Corresponde pago a Herederos' => 'Corresponde pago a Herederos',
                'Tipo de Identificación' => 'Tipo de Identificación',
                'Nombres y Apellidos' => 'Nombres y Apellidos',
                'Fecha Fallecimiento Afiliado' => 'Fecha Fallecimiento Afiliado',
                'Comunicación o pago a Herederos' => 'Comunicación o pago a Herederos',
                'Parentesco' => 'Parentesco',
            ]
        ],
        "DATOS CONTACTO HEREDERO" => [
            "NAME" => "DATOS CONTACTO HEREDERO",
            "OPTIONS" => [
                'Dirección' => 'Dirección',
                'Teléfono' => 'Teléfono',
                'Celular' => 'Celular',
                'Correo Electrónico' => 'Correo Electrónico',
                'Departamento' => 'Departamento',
                'Ciudad' => 'Ciudad',
            ]
        ],
    ];

    public static $MDI_MISSING_DOCUMENTS = [
        "CALIFICADO" => [
            "NAME" => "CALIFICADO",
            "OPTIONS" => [
                'Formulario Determinación de Pérdida de Capacidad Laboral/Ocupacional y Revisión del Estado de Invalidez de los pensionados' => 'Formulario Determinación de Pérdida de Capacidad Laboral/Ocupacional y Revisión del Estado de Invalidez de los pensionados',
                'Documento de Identidad del afiliado / beneficiario' => 'Documento de Identidad del afiliado / beneficiario',
                'Escrito de Manifestación de Inconformidad contra el dictamen proferido por Colpensiones' => 'Escrito de Manifestación de Inconformidad contra el dictamen proferido por Colpensiones',
            ]
        ],
        "APODERADO" => [
            "NAME" => "APODERADO",
            "OPTIONS" => [
                'Formulario Determinación de Pérdida de Capacidad Laboral/Ocupacional y Revisión del Estado de Invalidez de los pensionados' => 'Formulario Determinación de Pérdida de Capacidad Laboral/Ocupacional y Revisión del Estado de Invalidez de los pensionados',
                'Documento de Identidad del afiliado / beneficiario' => 'Documento de Identidad del afiliado / beneficiario',
                'Escrito de Manifestación de Inconformidad contra el dictamen proferido por Colpensiones' => 'Escrito de Manifestación de Inconformidad contra el dictamen proferido por Colpensiones',
                'Poder debidamente conferido por el calificado' => 'Poder debidamente conferido por el calificado',
                'Documento de Identidad del apoderado' => 'Documento de Identidad del apoderado',
                'Tarjeta Profesional del abogado ampliada al 150%' => 'Tarjeta Profesional del abogado ampliada al 150%',
            ]
        ],
        "TERCERO" => [
            "NAME" => "TERCERO",
            "OPTIONS" => [
                'Formulario Determinación de Pérdida de Capacidad Laboral/Ocupacional y Revisión del Estado de Invalidez de los pensionados' => 'Formulario Determinación de Pérdida de Capacidad Laboral/Ocupacional y Revisión del Estado de Invalidez de los pensionados',
                'Documento de Identidad del afiliado / beneficiario' => 'Documento de Identidad del afiliado / beneficiario',
                'Escrito de Manifestación de Inconformidad contra el dictamen proferido por Colpensiones' => 'Escrito de Manifestación de Inconformidad contra el dictamen proferido por Colpensiones',
                'Autorización autenticada' => 'Autorización autenticada',
                'Documento de Identidad del tercero' => 'Documento de Identidad del tercero',
                'Documento Curaduría o Persona de Apoyo' => 'Documento Curaduría o Persona de Apoyo',
            ]
        ],
        "AGENTE OFICIOSO" => [
            "NAME" => "AGENTE OFICIOSO",
            "OPTIONS" => [
                'Formulario Determinación de Pérdida de Capacidad Laboral/Ocupacional y Revisión del Estado de Invalidez de los pensionados' => 'Formulario Determinación de Pérdida de Capacidad Laboral/Ocupacional y Revisión del Estado de Invalidez de los pensionados',
                'Escrito de Manifestación de Inconformidad contra el dictamen proferido por Colpensiones' => 'Escrito de Manifestación de Inconformidad contra el dictamen proferido por Colpensiones',
                'Autorización autenticada' => 'Autorización autenticada',
                'Documento de Identidad del agente oficioso' => 'Documento de Identidad del agente oficioso',
            ]
        ],
        "EMPLEADOR" => [
            "NAME" => "EMPLEADOR",
            "OPTIONS" => [
                'Formulario Determinación de Pérdida de Capacidad Laboral/Ocupacional y Revisión del Estado de Invalidez de los pensionados' => 'Formulario Determinación de Pérdida de Capacidad Laboral/Ocupacional y Revisión del Estado de Invalidez de los pensionados',
                'Documento de Identidad del afiliado / beneficiario' => 'Documento de Identidad del afiliado / beneficiario',
                'Escrito de Manifestación de Inconformidad contra el dictamen proferido por Colpensiones' => 'Escrito de Manifestación de Inconformidad contra el dictamen proferido por Colpensiones',
                'Cámara y Comercio (si es persona Jurídica)' => 'Cámara y Comercio (si es persona Jurídica)',
            ]
        ],
        "EPS - ARL - AFP" => [
            "NAME" => "EPS - ARL - AFP",
            "OPTIONS" => [
                'Formulario Determinación de Pérdida de Capacidad Laboral/Ocupacional y Revisión del Estado de Invalidez de los pensionados' => 'Formulario Determinación de Pérdida de Capacidad Laboral/Ocupacional y Revisión del Estado de Invalidez de los pensionados',
                'Escrito de Manifestación de Inconformidad contra el dictamen proferido por Colpensiones' => 'Escrito de Manifestación de Inconformidad contra el dictamen proferido por Colpensiones',
            ]
        ],
    ];

    public static $TRAMITE_INFORMATION_FIELDS = [
        "INFORMACIÓN CRE Y CLICLO DE INCAPACIDAD" => [
            "NAME" => "INFORMACIÓN CRE Y CLICLO DE INCAPACIDAD",
            "OPTIONS" => [
                'Fecha día 1' => 'Fecha día 1',
                'Fecha día 180' => 'Fecha día 180',
                'Fecha día 540' => 'Fecha día 540',
                'Total días' => 'Total días',
                'Valor a pagar' => 'Valor a pagar',
                'IBL 2022' => 'IBL 2022',
                'IBL 2023' => 'IBL 2023',
                'IBL 2024' => 'IBL 2024',
                'EPS' => 'EPS',
                'Tiene CRE' => 'Tiene CRE',
                'Pronostico CRE' => 'Pronostico CRE',
                'Fecha de recepción CRE - Favorable' => 'Fecha de recepción CRE - Favorable',
                'Fecha de emisión CRE - Desfavorable' => 'Fecha de emisión CRE - Desfavorable',
                'Soporte CRE' => 'Soporte CRE',
                'Radicados asociados' => 'Radicados asociados',
            ]
        ],
        "DATOS TUTELA O REQUERIMIENTO JURÍDICO" => [
            "NAME" => "DATOS TUTELA O REQUERIMIENTO JURÍDICO",
            "OPTIONS" => [
                'Tutela' => 'Tutela',
                'No. Radicado Tutela' => 'No. Radicado Tutela',
                'Fecha inicial establecida por juez' => 'Fecha inicial establecida por juez',
                'Tipo de desapacho ' => 'Tipo de desapacho ',
                'Numero de Juzgado ' => 'Numero de Juzgado ',
                'Nombre de Sala' => 'Nombre de Sala',
                'Nombre de seccion ' => 'Nombre de seccion ',
                'Nombre juridisccion' => 'Nombre juridisccion',
                'Nombre especialidad' => 'Nombre especialidad',
            ]
        ],
    ];

    public static $DETAIL_PERIOS_FIELDS = [
        "INCAPACIDAD" => [
            "NAME" => "INCAPACIDAD",
            "OPTIONS" => [
                'N° de incapacidad ' => 'N° de incapacidad ',
                'Fecha inicio IT ' => 'Fecha inicio IT ',
                'Fecha fin IT' => 'Fecha fin IT',
                'Fracionado' => 'Fracionado',
                'Original' => 'Original',
                'Dias IT' => 'Dias IT',
                'Días acumulados' => 'Días acumulados',
                'Incapacidad' => 'Incapacidad',
                'Resultado validación derechos' => 'Resultado validación derechos',
                'Días IT a pagar' => 'Días IT a pagar',
                'Valor a pagar' => 'Valor a pagar',
                'Días pagados' => 'Días pagados',
            ]
        ],
        "CRITERIOS DE ORIGINALIDAD" => [
            "NAME" => "CRITERIOS DE ORIGINALIDAD",
            "OPTIONS" => [
                'Criterio Originalidad 1' => 'Criterio Originalidad 1',
                'Criterio Originalidad 2' => 'Criterio Originalidad 2',
                'Criterio Originalidad 3' => 'Criterio Originalidad 3',
                'Criterio Originalidad 4' => 'Criterio Originalidad 4',
                'Criterio Originalidad 5' => 'Criterio Originalidad 5',
                'Criterio Originalidad 6' => 'Criterio Originalidad 6',
                'Criterio Originalidad 7' => 'Criterio Originalidad 7',
                'Criterio Originalidad 8' => 'Criterio Originalidad 8',
            ]
        ],
    ];

    public static $PAYMENT_INFORMATION_FIELDS = [
        "DATOS DE LIQUIDACIÓN" => [
            "NAME" => "DATOS DE LIQUIDACIÓN",
            "OPTIONS" => [
                'Año IBC' => 'Año IBC',
                'IBC' => 'IBC',
                'Total Días' => 'Total Días',
                'Total a pagar' => 'Total a pagar',
            ]
        ],
        "DATOS TITULAR DE CUENTA" => [
            "NAME" => "DATOS TITULAR DE CUENTA",
            "OPTIONS" => [
                'Tipo de titular' => 'Tipo de titular',
                'Tipo de Identificación' => 'Tipo de Identificación',
                'Número de Identificación' => 'Número de Identificación',
                'Nombres y Apellidos' => 'Nombres y Apellidos',
                'Tipo de persona' => 'Tipo de persona',
            ]
        ],
        "DATOS BANCARIOS" => [
            "NAME" => "DATOS BANCARIOS",
            "OPTIONS" => [
                'N° de Cuenta' => 'N° de Cuenta',
                'Tipo de Cuenta' => 'Tipo de Cuenta',
                'Banco' => 'Banco',
            ]
        ],
    ];

    public static $CLASSIFICATION = [
        'F' => 'PRINCIPAL',
        'S' => 'SECUNDARIO'
    ];

    public static $ROOM_NAME_TUTELAGE_IT = [
        '' => 'NO APLICA',
        'SALA CIVIL' => 'SALA CIVIL',
        'SALA CIVIL ESPECIALIZADA EN RESTITUCIÓN DE TIERRAS' => 'SALA CIVIL ESPECIALIZADA EN RESTITUCIÓN DE TIERRAS',
        'SALA CIVIL Y DE FAMILIA' => 'SALA CIVIL Y DE FAMILIA',
        'SALA CIVIL, FAMILIA Y LABORAL ' => 'SALA CIVIL, FAMILIA Y LABORAL ',
        'SALA DE DECISIÓN ' => 'SALA DE DECISIÓN ',
        'SALA DE EXTINCIÓN DE DOMINIO' => 'SALA DE EXTINCIÓN DE DOMINIO',
        'SALA DE FAMILIA' => 'SALA DE FAMILIA',
        'SALA LABORAL' => 'SALA LABORAL',
        'SALA PENAL' => 'SALA PENAL',
        'SALA PENAL DE JUSTICIA Y  PAZ - CONOCIMIENTO' => 'SALA PENAL DE JUSTICIA Y  PAZ - CONOCIMIENTO',
        'SALA PENAL DE JUSTICIA Y  PAZ - GARANTÍAS' => 'SALA PENAL DE JUSTICIA Y  PAZ - GARANTÍAS',
        'SALA ÚNICA' => 'SALA ÚNICA'
    ];
    public static $SECTION_NAME_TUTELAGE_IT = [
        '' => 'NO APLICA',
        'SECCIÓN PRIMERA' => 'SECCIÓN PRIMERA',
        'SECCIÓN SEGUNDA' => 'SECCIÓN SEGUNDA',
        'SECCIÓN TERCERA' => 'SECCIÓN TERCERA',
        'SECCIÓN CUARTA' => 'SECCIÓN CUARTA',
    ];

    public static $OCCUPATIONS = [
        'Acomodador de parqueos (no chófer)' => 'Acomodador de parqueos (no chófer)',
        'Acompañante en buseta escolar' => 'Acompañante en buseta escolar',
        'Adiestrador de animales' => 'Adiestrador de animales',
        'Agente de aduanas' => 'Agente de aduanas',
        'Agente de seguridad' => 'Agente de seguridad',
        'Agente de ventas' => 'Agente de ventas',
        'Albañil' => 'Albañil',
        'Alistador automotriz (lijador)' => 'Alistador automotriz (lijador)',
        'Analista de crédito' => 'Analista de crédito',
        'Animador de eventos' => 'Animador de eventos',
        'Aplanchador con equipo de vapor' => 'Aplanchador con equipo de vapor',
        'Aserrador (usa sierra de motor)' => 'Aserrador (usa sierra de motor)',
        'Asistente de abogacía' => 'Asistente de abogacía',
        'Asistente domicilio' => 'Asistente domicilio',
        'Auxiliar agente de aduanas' => 'Auxiliar agente de aduanas',
        'Auxiliar de contabilidad' => 'Auxiliar de contabilidad',
        'Auxiliar dental' => 'Auxiliar dental',
        'Ayudante de cocina' => 'Ayudante de cocina',
        'Ayudante en mecánica general' => 'Ayudante en mecánica general',
        'Ayudante de operario, construcción' => 'Ayudante de operario, construcción',
        'Baqueano' => 'Baqueano',
        'Barbero' => 'Barbero',
        'Barista' => 'Barista',
        'Bodeguero (Encargado)' => 'Bodeguero (Encargado)',
        'Bodeguero (Peón)' => 'Bodeguero (Peón)',
        'Cajero' => 'Cajero',
        'Camarero' => 'Camarero',
        'Camarógrafo de prensa' => 'Camarógrafo de prensa',
        'Cantante' => 'Cantante',
        'Cantinero' => 'Cantinero',
        'Capitán de embarcación' => 'Capitán de embarcación',
        'Cargador cilindros gas y extintores' => 'Cargador cilindros gas y extintores',
        'Carnicero empleado de despacho' => 'Carnicero empleado de despacho',
        'Carnicero destazador' => 'Carnicero destazador',
        'Carpintero' => 'Carpintero',
        'Catador' => 'Catador',
        'Cerrajero' => 'Cerrajero',
        'Chapulinero' => 'Chapulinero',
        'Chef' => 'Chef',
        'Chequeador de buses' => 'Chequeador de buses',
        'Cobrador' => 'Cobrador',
        'Cocinero' => 'Cocinero',
        'Coctelero (Bartender o Barwoman)' => 'Coctelero (Bartender o Barwoman)',
        'Conductor ambulancia (socorrismo)' => 'Conductor ambulancia (socorrismo)',
        'Conductor de bus (no cobra)' => 'Conductor de bus (no cobra)',
        'Conductor de bus (cobrador)' => 'Conductor de bus (cobrador)',
        'Conductor de tráiler' => 'Conductor de tráiler',
        'Conductor de vehículo liviano' => 'Conductor de vehículo liviano',
        'Conductor de vehículo pesado' => 'Conductor de vehículo pesado',
        'Conductor microbús (menos 11 pasaj.)' => 'Conductor microbús (menos 11 pasaj.)',
        'Conserje' => 'Conserje',
        'Contador privado' => 'Contador privado',
        'Copiloto (Primer Oficial de Aviación)' => 'Copiloto (Primer Oficial de Aviación)',
        'Cortador de piezas tela (patrones)' => 'Cortador de piezas tela (patrones)',
        'Cosedor piezas (prendas a máquina)' => 'Cosedor piezas (prendas a máquina)',
        'Costurera (Modista)' => 'Costurera (Modista)',
        'Counter (Vendedor de pasajes)' => 'Counter (Vendedor de pasajes)',
        'Demostrador (display)' => 'Demostrador (display)',
        'Demostrador-vendedor' => 'Demostrador-vendedor',
        'Dependiente' => 'Dependiente',
        'Despachador de vuelo' => 'Despachador de vuelo',
        'Diagramador en artes gráficas' => 'Diagramador en artes gráficas',
        'Digitador' => 'Digitador',
        'Ebanista' => 'Ebanista',
        'Educador aspirante sin título' => 'Educador aspirante sin título',
        'Electricista' => 'Electricista',
        'Electromecánico' => 'Electromecánico',
        'Empacador, etiquetador (manual)' => 'Empacador, etiquetador (manual)',
        'Empleado de despacho' => 'Empleado de despacho',
        'Encuestador' => 'Encuestador',
        'Enderezador automotriz' => 'Enderezador automotriz',
        'Entrenador de fútbol' => 'Entrenador de fútbol',
        'Escaneador inventarios (hand held)' => 'Escaneador inventarios (hand held)',
        'Esteticista' => 'Esteticista',
        'Estilista' => 'Estilista',
        'Florista' => 'Florista',
        'Fontanero' => 'Fontanero',
        'Fotocopiador (Centro fotocopiado)' => 'Fotocopiador (Centro fotocopiado)',
        'Fotógrafo de prensa' => 'Fotógrafo de prensa',
        'Fresador (Metalmecánica)' => 'Fresador (Metalmecánica)',
        'Fumigador en casas y edificios' => 'Fumigador en casas y edificios',
        'Futbolista Primera División' => 'Futbolista Primera División',
        'Futbolista Segunda División' => 'Futbolista Segunda División',
        'Gestor de redes sociales (Comunity Manager, diseña artes finales)' => 'Gestor de redes sociales (Comunity Manager, diseña artes finales)',
        'Gestor de redes sociales (Comunity manager, monitorea y da respuesta).' => 'Gestor de redes sociales (Comunity manager, monitorea y da respuesta).',
        'Gestor redes sociales (Comunity manager, lleva datos y los interpreta).' => 'Gestor redes sociales (Comunity manager, lleva datos y los interpreta).',
        'Gondolero' => 'Gondolero',
        'Guía de turismo' => 'Guía de turismo',
        'Hojalatero' => 'Hojalatero',
        'Instalador vidrios (corta, pone marcos)' => 'Instalador vidrios (corta, pone marcos)',
        'Instructor de bailes' => 'Instructor de bailes',
        'Instructor de gimnasio (sin título)' => 'Instructor de gimnasio (sin título)',
        'Jardinero (diseña jardines)' => 'Jardinero (diseña jardines)',
        'Joyero' => 'Joyero',
        'Lavador y encerador de carros' => 'Lavador y encerador de carros',
        'Limpiador de piscinas (sin químicos)' => 'Limpiador de piscinas (sin químicos)',
        'Limpiador de tanques sépticos' => 'Limpiador de tanques sépticos',
        'Llantero' => 'Llantero',
        'Locutor de radioemisora' => 'Locutor de radioemisora',
        'Locutor de televisión' => 'Locutor de televisión',
        'Luminotécnico de televisión' => 'Luminotécnico de televisión',
        'Maestro de obras (Construcción)' => 'Maestro de obras (Construcción)',
        'Manicurista; Maquillador' => 'Manicurista; Maquillador',
        'Masajista' => 'Masajista',
        'Mantenimiento correctivo de cómputo' => 'Mantenimiento correctivo de cómputo',
        'Mantenimiento de edificios' => 'Mantenimiento de edificios',
        'Mantenimiento preventivo de cómputo' => 'Mantenimiento preventivo de cómputo',
        'Mecánico de calderas (Mantenimiento)' => 'Mecánico de calderas (Mantenimiento)',
        'Mecánico general' => 'Mecánico general',
        'Mecánico precisión' => 'Mecánico precisión',
        'Mecánico máquinas de coser industrial' => 'Mecánico máquinas de coser industrial',
        'Mensajero' => 'Mensajero',
        'Misceláneo' => 'Misceláneo',
        'Misceláneo hogares de la tercera edad' => 'Misceláneo hogares de la tercera edad',
        'Monitoreador de control de alarma' => 'Monitoreador de control de alarma',
        'Nana' => 'Nana',
        'Narrador de cuentos' => 'Narrador de cuentos',
        'Niñera' => 'Niñera',
        'Obrero de la construcción' => 'Obrero de la construcción',
        'Operador de máquina pesada' => 'Operador de máquina pesada',
        'Operador de teleférico' => 'Operador de teleférico',
        'Operador de teléfono' => 'Operador de teléfono',
        'Operador telefónico' => 'Operador telefónico',
        'Peluquero' => 'Peluquero',
        'Peón agrícola' => 'Peón agrícola',
        'Peón de la construcción' => 'Peón de la construcción',
        'Pintor de brocha gorda (pintor de obra)' => 'Pintor de brocha gorda (pintor de obra)',
        'Pintor de brocha fina (artístico)' => 'Pintor de brocha fina (artístico)',
        'Plomero' => 'Plomero',
        'Porteador' => 'Porteador',
        'Practicante' => 'Practicante',
        'Promotor de ventas' => 'Promotor de ventas',
        'Profesor de escuela (en colegio)' => 'Profesor de escuela (en colegio)',
        'Psicopedagogo' => 'Psicopedagogo',
        'Repartidor de pedidos (Delivery)' => 'Repartidor de pedidos (Delivery)',
        'Reparador de celulares' => 'Reparador de celulares',
        'Reparador de electrodomésticos' => 'Reparador de electrodomésticos',
        'Reparador de muebles' => 'Reparador de muebles',
        'Sastrero' => 'Sastrero',
        'Sello ' => 'Sello ',
        'Sello (no dentado)' => 'Sello (no dentado)',
        'Serigrafista' => 'Serigrafista',
        'Soldador' => 'Soldador',
        'Soldador (técnico)' => 'Soldador (técnico)',
        'Solicitante de empleo' => 'Solicitante de empleo',
        'Subgerente' => 'Subgerente',
        'Sustituto de docente (sin título)' => 'Sustituto de docente (sin título)',
        'Técnico (informática)' => 'Técnico (informática)',
        'Técnico de máquina pesada' => 'Técnico de máquina pesada',
        'Técnico electricista' => 'Técnico electricista',
        'Técnico en gastronomía' => 'Técnico en gastronomía',
        'Técnico en mantenimiento de aeronaves' => 'Técnico en mantenimiento de aeronaves',
        'Técnico en mantenimiento industrial' => 'Técnico en mantenimiento industrial',
        'Terapeuta ocupacional' => 'Terapeuta ocupacional',
        'Terapista de lenguaje' => 'Terapista de lenguaje',
        'Trabajador social' => 'Trabajador social',
        'Tramitador de documentos' => 'Tramitador de documentos',
        'Vendedor ambulante' => 'Vendedor ambulante',
        'Vigilante de seguridad' => 'Vigilante de seguridad',
        'Voluntario' => 'Voluntario',
        'Zootecnista' => 'Zootecnista'
    ];


    public static $CONDICION = [
        'Vivo' => 'Vivo',
        'Fallecido' => 'Fallecido',
        'Desaparecido' => 'Desaparecido',
    ];
    public static $CONDICION_SPECIAL = [
        '1' => 'Condición especial de pago',
        '2' => 'Reconocimiento por acciones preventivas',
        '3' => 'Condición especial de pago + Reconocimiento por acciones preventivas',
        '4' => 'No',
    ];
    public static $JURISDICTION_TUTELAGE_IT = [
        '' => 'NO APLICA',
        'ADMINISTRATIVO' => 'ADMINISTRATIVO',
        'CIVIL' => 'CIVIL',
        'FAMILIA' => 'FAMILIA',
        'LABORAL' => 'LABORAL',
        'PENAL' => 'PENAL',
        'PROMISCUO' => 'PROMISCUO',
    ];
    public static $SPECIALTY_TUTELAGE_IT = [
        '' => 'NO APLICA',
        'DE ADOLECENTES CONTROL GARANTÍAS ' => 'DE ADOLECENTES CONTROL GARANTÍAS ',
        'DE ADOLESCENTES CON FUNCIONES DE PEQUEÑAS CAUSAS' => 'DE ADOLESCENTES CON FUNCIONES DE PEQUEÑAS CAUSAS',
        'DE CIRCUITO' => 'DE CIRCUITO',
        'DE CIRCUITO DE CONOCIMIENTO' => 'DE CIRCUITO DE CONOCIMIENTO',
        'DE CIRCUITO DE DESCONGESTIÓN CON FUNCION DE CONOCIMIENTO' => 'DE CIRCUITO DE DESCONGESTIÓN CON FUNCION DE CONOCIMIENTO',
        'DE CIRCUITO DE EJECUCIÓN DE SENTENCIAS' => 'DE CIRCUITO DE EJECUCIÓN DE SENTENCIAS',
        'DE CIRCUITO ESPECIALIZADO' => 'DE CIRCUITO ESPECIALIZADO',
        'DE CIRCUITO ESPECIALIZADO ITINERANTE' => 'DE CIRCUITO ESPECIALIZADO ITINERANTE',
        'DE CIRCUITO LEY 600' => 'DE CIRCUITO LEY 600',
        'DE CIRCUITO MIXTO' => 'DE CIRCUITO MIXTO',
        'DE CIRCUITO PARA ADOLESCENTES DE CONOCIMIENTO' => 'DE CIRCUITO PARA ADOLESCENTES DE CONOCIMIENTO',
        'DE CIRCUITO TIERRAS' => 'DE CIRCUITO TIERRAS',
        'DE CIRCUITO TIERRAS ITINERANTE' => 'DE CIRCUITO TIERRAS ITINERANTE',
        'DE CONOCIMIENTO ADOLESCENCIA' => 'DE CONOCIMIENTO ADOLESCENCIA',
        'DE CONTROL DE GARANTÍAS ADOLESCENCIA' => 'DE CONTROL DE GARANTÍAS ADOLESCENCIA',
        'DE DESCONGESTIÓN' => 'DE DESCONGESTIÓN',
        'DE DISTRITO ' => 'DE DISTRITO ',
        'DE EJECUCIÓN DE FAMILIA' => 'DE EJECUCIÓN DE FAMILIA',
        'DE EJECUCIÓN DE PENAS' => 'DE EJECUCIÓN DE PENAS',
        'DE EJECUCION DE PENAS Y MEDIDAS DE SEGURIDAD' => 'DE EJECUCION DE PENAS Y MEDIDAS DE SEGURIDAD',
        'DE EJECUCIÓN DE SENTENCIAS PARA JUSTICIA Y PAZ' => 'DE EJECUCIÓN DE SENTENCIAS PARA JUSTICIA Y PAZ',
        'DE EXTINCIÓN DE DOMINIO' => 'DE EXTINCIÓN DE DOMINIO',
        'DE FAMILIA' => 'DE FAMILIA',
        'DE JUSTICIA Y PAZ - CONOCIMIENTO' => 'DE JUSTICIA Y PAZ - CONOCIMIENTO',
        'DE JUSTICIA Y PAZ - GARANTÍAS' => 'DE JUSTICIA Y PAZ - GARANTÍAS',
        'DE MENORES' => 'DE MENORES',
        'DE PEQUEÑAS CAUSAS CIVIL' => 'DE PEQUEÑAS CAUSAS CIVIL',
        'DE PEQUEÑAS CAUSAS LABORAL' => 'DE PEQUEÑAS CAUSAS LABORAL',
        'DE PEQUEÑAS CAUSAS Y DE COMPETENCIA MÚLTIPLE' => 'DE PEQUEÑAS CAUSAS Y DE COMPETENCIA MÚLTIPLE',
        'MUNICIPAL' => 'MUNICIPAL',
        'MUNICIPAL CONTROL GARANTÍAS' => 'MUNICIPAL CONTROL GARANTÍAS',
        'MUNICIPAL DE CONOCIMIENTO' => 'MUNICIPAL DE CONOCIMIENTO',
        'MUNICIPAL DE EJECUCIÓN DE SENTENCIAS' => 'MUNICIPAL DE EJECUCIÓN DE SENTENCIAS',
        'MUNICIPAL LEY 600' => 'MUNICIPAL LEY 600',
        'MUNICIPAL MIXTO' => 'MUNICIPAL MIXTO',
        'MUNICIPAL PARA ADOLESCENTES CONTROL GARANTÍAS' => 'MUNICIPAL PARA ADOLESCENTES CONTROL GARANTÍAS',
        'PARA ADOLESCENTES' => 'PARA ADOLESCENTES',
        'PENAL DE CIRCUITO ESPECIALIZADO ITINERANTE' => 'PENAL DE CIRCUITO ESPECIALIZADO ITINERANTE',
        'PENALES MUNICIPALES AMBULANTES BACRIM CONTROL GARANTÍAS' => 'PENALES MUNICIPALES AMBULANTES BACRIM CONTROL GARANTÍAS'
    ];
    public static $COURT_NUMBER_TUTELAGE_IT = [
        '' => 'NO APLICA',
        '1' => '1',
        '2' => '2',
        '3' => '3',
        '4' => '4',
        '5' => '5',
        '6' => '6',
        '7' => '7',
        '8' => '8',
        '9' => '9',
        '10' => '10',
        '11' => '11',
        '12' => '12',
        '13' => '13',
        '14' => '14',
        '15' => '15',
        '16' => '16',
        '17' => '17',
        '18' => '18',
        '19' => '19',
        '20' => '20',
        '21' => '21',
        '22' => '22',
        '23' => '23',
        '24' => '24',
        '25' => '25',
        '26' => '26',
        '27' => '27',
        '28' => '28',
        '29' => '29',
        '30' => '30',
        '31' => '31',
        '32' => '32',
        '33' => '33',
        '34' => '34',
        '35' => '35',
        '36' => '36',
        '37' => '37',
        '38' => '38',
        '39' => '39',
        '40' => '40',
        '41' => '41',
        '42' => '42',
        '43' => '43',
        '44' => '44',
        '45' => '45',
        '46' => '46',
        '47' => '47',
        '48' => '48',
        '49' => '49',
        '50' => '50',
        '51' => '51',
        '52' => '52',
        '53' => '53',
        '54' => '54',
        '55' => '55',
        '56' => '56',
        '57' => '57',
        '58' => '58',
        '59' => '59',
        '60' => '60',
        '61' => '61',
        '62' => '62',
        '63' => '63',
        '64' => '64',
        '65' => '65',
        '66' => '66',
        '67' => '67',
        '68' => '68',
        '69' => '69',
        '70' => '70',
        '71' => '71',
        '72' => '72',
        '73' => '73',
        '74' => '74',
        '75' => '75',
        '76' => '76',
        '77' => '77',
        '78' => '78',
        '79' => '79',
        '80' => '80',
        '81' => '81',
        '82' => '82',
        '83' => '83',
        '84' => '84',
        '85' => '85',
        '86' => '86',
        '87' => '87',
        '88' => '88',
        '89' => '89',
        '90' => '90',
        '91' => '91',
        '92' => '92',
        '93' => '93',
        '94' => '94',
        '95' => '95',
        '96' => '96',
        '97' => '97',
        '98' => '98',
        '99' => '99'
    ];

    public static $TYPE_IPS = [
        '00000' => 'POR DEFECTO',
        '1' => 'RED ADSCRITA',
        '2' => 'RED NO ADSCRITA POS CRUCE',
        '3' => 'RED NO ADSCRITA POS PURO',
        '4' => 'CENTRO MEDICO PROPIO',
    ];
    public static $SPECIALITY = [
        '00' => 'NO APLICA',
        '001' => 'PREANESTESICA',
        '01' => 'Anestesiologia',
        '02' => 'Administracion Servicios de Salud',
        '03' => 'Cirugia General',
        '04' => 'Cirugia cardiovascular',
        '05' => 'Cirugia del Torax o Toraxica',
        '06' => 'Cirugia Infantil o Pediatrica',
        '07' => 'Cirugia Plastica',
        '08' => 'Cirugia-Clinica del Seno',
        '09' => 'Dermatologia',
        '10' => 'Hematologia Pediatrica',
        '100' => 'Cirugia Maxilofacial',
        '101' => 'Medicina Nuclear',
        '102' => 'Genetica',
        '103' => 'Medicina de aviacion',
        '104' => 'Odontologia Pediatrica',
        '105' => 'Endodoncia',
        '106' => 'Periodoncia',
        '107' => 'Ortodoncia',
        '108' => 'Rehabilitacion Oral',
        '109' => 'Odontologia Oncologica',
        '11' => 'Medicina Deportiva',
        '110' => 'Proctologia',
        '111' => 'ESPECIALIDAD INACTIVA 20080717',
        '112' => 'Ortopedia de pie y cuello de pie',
        '113' => 'Medicamentos Ambulatorios',
        '114' => 'Cirugia de Colon y de Recto',
        '115' => 'Ortopedia de columna',
        '116' => 'Cirugia pediatrica oncologica',
        '117' => 'Cirugia Oncologica',
        '118' => 'Cirugia de la mano Plastica',
        '119' => 'Homeopatia',
        '12' => 'Geriatria',
        '120' => 'Acupuntura',
        '121' => 'Dermatologia Oncologica',
        '122' => 'Toxicologia',
        '123' => 'Hemato-oncologia Pediatrica',
        '124' => 'Reumatologia Pediatrica',
        '125' => 'Ortopedia Maxilar',
        '126' => 'Medicina Bioenergetica',
        '127' => 'Hemato - Oncologia',
        '128' => 'Homeopatia Pediatrica',
        '129' => 'Hepatologia',
        '13' => 'Ginecologia y Obstetricia',
        '130' => 'Orbitologia-oftalmologia',
        '131' => 'Consulta de Oculoplastia',
        '132' => 'SEXOLOGIA',
        '133' => 'Medicina Psicosomatica',
        '134' => 'Podologia',
        '135' => 'Medicina Preventiva',
        '136' => 'Medicina Alternativa',
        '138' => 'Corneologo',
        '139' => 'Salud Ocupacional',
        '14' => 'Inmunologia',
        '140' => 'Pediatría Prioritaria',
        '141' => 'Medicina General Prioritaria',
        '142' => 'Salud Administrada Corazón Sano',
        '143' => 'Salud Administrada Diabetes',
        '144' => 'Consulta Estética Laser',
        '145' => 'Cons prioritaria por odontologia gral',
        '146' => 'Cons prioritaria por odontologia espec',
        '147' => 'Medicina General Domiciliaria',
        '148' => 'Medicina Especilizada Domiciliaria',
        '149' => 'Terapia Respiratoria Domiciliaria',
        '15' => 'Medicina Familiar',
        '150' => 'Terapia Fisica Domiciliaria',
        '151' => 'Fonoaudiologia Domiciliaria',
        '152' => 'Terapia Ocupacional Domiciliaria',
        '156' => 'Cirugia Mano Ortopedia',
        '159' => 'MEDICINA LABORAL',
        '16' => 'Medicina Fisica-Rehabilitacion',
        '160' => 'Oncologia Pediatrica',
        '162' => 'Electrofisiologia',
        '163' => 'GLAUCOMA',
        '164' => 'HEMODINAMIA',
        '165' => 'OFTALMOLOGIA ONCOLOGICA',
        '166' => 'Ortopedia de Cadera',
        '167' => 'Ortopedia de Hombro',
        '168' => 'Ortopedia Oncologica',
        '169' => 'Ortopedia de Rodilla',
        '17' => 'Medicina Interna',
        '170' => 'Otorrinolaringologia Oncologica',
        '171' => 'Retinologia',
        '172' => 'Cirugia Maxilofacial (odontologia)',
        '173' => 'ESTOMATOLOGIA',
        '174' => 'Cuidado Paliativo',
        '175' => 'Urologia Oncologica',
        '176' => 'NEUROINFECTOLOGIA',
        '177' => 'Cirugia plastica oncologica',
        '178' => 'RADIONEUROLOGIA',
        '18' => 'Cardiologia',
        '180' => 'NEURO-OFTALMOLOGIA',
        '181' => 'Dermatologia Estetica',
        '182' => 'CIRUGIA GASTROINTESTINAL ONCOLOGICA',
        '183' => 'Perinatologia',
        '184' => 'ARTROSCOPISTA',
        '185' => 'VACUNACION',
        '186' => 'Consulta Prioritaria',
        '187' => 'Consulta Prioritaria Pediatria',
        '188' => 'Psicogeriatria',
        '19' => 'Endocrinologia',
        '190' => 'NEURO OTOLOGIA',
        '191' => 'CIRUGIA MINIMA INVASION ',
        '192' => 'NEUROLOGIA  ONCOLOGICA ',
        '193' => 'ONCOLOGIA ORAL',
        '194' => 'NEUROLOGIA INTERNVENCIONISTA',
        '195' => 'CIRUGIA GASTROINTESTINAL ',
        '196' => 'Odontologia Estetica',
        '197' => 'LAPAROSCOPISTA GINECOLOGICO ',
        '198' => 'CIRUGIA OFTALMOLOGICA ',
        '199' => 'OTOLOGIA',
        '20' => 'Gastroenterologia',
        '200' => 'Nivel I',
        '201' => 'Nivel II',
        '202' => 'Nivel III',
        '203' => 'Nivel IV',
        '204' => 'Vertigo y Equilibrio',
        '205' => 'Laringologia',
        '206' => 'RADIOCIRUGIA ',
        '207' => 'OCULISTA PROTESISTA',
        '208' => 'Psicologia Oncologica',
        '209' => 'CIRUGIA ORAL ',
        '21' => 'Hematologia',
        '210' => 'CIRUGIA LAPAROSCOPISTA',
        '211' => 'CIRUGIA CARDIOVASCULAR PEDIATRICA',
        '212' => 'EPILEPTOLOGIA ',
        '213' => 'HEPATOLOGIA PEDIATRICA',
        '214' => 'CIRUGIA HEPATOBILIAR',
        '215' => 'NUTRIOLOGIA',
        '216' => 'Nutricion Oncologica',
        '217' => 'Patología y Cirugía Oral',
        '218' => 'NEURORADIOLOGIA INTERVENCIONISTA',
        '219' => 'NUTRICION PEDIATRICA',
        '22' => 'Nefrologia',
        '220' => 'FERTILIDAD',
        '221' => 'ESTRABISMO ',
        '222' => 'CLINICA DEL DOLOR ',
        '224' => 'CUIDADO PALIATIVO PEDIÁTRICO',
        '225' => 'CIRUGIA BARIATRICA PEDIATRICA',
        '226' => 'CIRUGIA BARIATRICA',
        '227' => 'TRASPLANTES',
        '228' => 'RADIOLOGIA INTERVENCIONISTA',
        '229' => 'Electrofisiologia Pediatrica',
        '23' => 'Neumologia',
        '230' => 'HEMODINAMIA PEDIATRICA',
        '231' => 'Salud familiar y comunitaria',
        '232' => 'Anestesiologia cardiovascular',
        '233' => 'Medicina alternativa (Ayurveda)',
        '234' => 'Medicina alternativa (Naturopatia)',
        '235' => 'Medicina forense',
        '236' => 'Medicina Alternativa (Osteopatica)',
        '237' => 'Medicina Alternativa (Neuralterapeutica)',
        '24' => 'Reumatologia',
        '25' => 'Otorrinolaringologia Pediatrica',
        '26' => 'Ginecologia Endocrinologica',
        '27' => 'Nefrologia Pediatrica',
        '28' => 'Neurocirugia',
        '29' => 'Neurologia',
        '30' => 'Neurologia Infantil',
        '31' => 'Oftalmologia',
        '32' => 'Oncologia Clinica',
        '33' => 'Ortopedia y Traumatologia',
        '34' => 'Otorrinolaringologia',
        '35' => 'Patologia o Anatomia Patologica',
        '36' => 'Patologia y Laboratorio Clinico',
        '37' => 'Endocrinologia Pediatrica',
        '38' => 'Pediatria',
        '39' => 'Psiquiatria',
        '40' => 'Radiologia o Radiodiagnostico',
        '41' => 'Radioterapia',
        '42' => 'Alergias Pediatricas',
        '43' => 'Urologia',
        '44' => 'Fisioterapia de Mano',
        '45' => 'Anestesiologia Infantil',
        '46' => 'Neurocirugia Pediatrica',
        '47' => 'Medicina del Dolor',
        '48' => 'Dermatologia Pediatrica',
        '49' => 'Dermatopatologia Patologia de La Pi',
        '50' => 'Cardiologia Pediatrica',
        '51' => 'Neumologia Pediatrica',
        '52' => 'Neonatologia',
        '53' => 'Psiquiatria Pediatrica',
        '54' => 'Cirugia de la Mano',
        '55' => 'Medicina General',
        '56' => 'Odontologia',
        '57' => 'Infectologia Pediatrica',
        '58' => 'Gastroenterologia Pediatrica',
        '59' => 'Oftalmologia Pediatrica',
        '60' => 'Ortopedia y Traumatologia Pediatric',
        '61' => 'Medicina de Adolescentes',
        '62' => 'Urologia y Litotripcia',
        '63' => 'Psicologia Infantil',
        '64' => 'Cirugia Ginecologica Oncologica',
        '65' => 'Cirugia Vascular Periferica',
        '66' => 'Enfermedades del Seno Mastologia',
        '67' => 'Neuropsicologia',
        '68' => 'Quimioterapia',
        '69' => 'Terapia Fisica y Rehabilitacion',
        '70' => 'Nutricion y Dietetica',
        '71' => 'Optometria',
        '72' => 'Ortoptica',
        '73' => 'Psicologia',
        '74' => 'Terapia del Lenguaje',
        '75' => 'Terapia Ocupacional',
        '76' => 'Terapia Respiratoria',
        '77' => 'Audiologia',
        '78' => 'Diagnostico Prenatal',
        '79' => 'Ecografia',
        '80' => 'Ecografia Obstetrica',
        '81' => 'Ecografia Urologica',
        '82' => 'Electroencefalografia',
        '83' => 'Electromiografia y Neuroconduccion',
        '84' => 'Endoscopia Digestiva',
        '85' => 'Endoscopia Pediatrica',
        '86' => 'Escanografia',
        '87' => 'Estudio Ultrasonido Doppler',
        '88' => 'Examenes Oftalmologicos',
        '89' => 'Gamagrafia',
        '90' => 'Laboratorio Clinico',
        '91' => 'Mamografia',
        '92' => 'Prueba de Funcion Pulmonar',
        '93' => 'Resonancia Nuclear Magnetica',
        '94' => 'Infectologia',
        '95' => 'Alergologia Adultos y Pediatrica',
        '96' => 'Cirugia de Cabeza y Cuello',
        '97' => 'Urologia Pediatrica',
        '98' => 'Ginecologia',
        '99' => 'Ginecologia Oncologica',
        '240' => 'Urgenciología',
        '300' => 'Oculoplastia',
    ];

    public static $BANKS = [
        'BANCO DE LA REPÚBLICA',
        'BANCO DE BOGOTÁ',
        'BANCO POPULAR',
        'BANCO CORPBANCA COLOMBIA S.A.',
        'BANCOLOMBIA S.A.',
        'CITIBANK COLOMBIA',
        'BANCO GNB COLOMBIA S.A.',
        'BANCO GNB SUDAMERIS COLOMBIA',
        'BBVA COLOMBIA',
        'HELM BANK',
        'RED MULTIBANCA COLPATRIA S.A.',
        'BANCO DE OCCIDENTE',
        'BANCO DE COMERCIO EXTERIOR DE COLOMBIA S.A. (BANCOLDEX)',
        'BANCO CAJA SOCIAL - BCSC S.A.',
        'BANCO AGRARIO DE COLOMBIA S.A.',
        'BANCO DAVIVIENDA S.A.',
        'BANCO AV VILLAS',
        'BANCO WWB S.A.',
        'BANCO PROCREDIT',
        'BANCAMIA',
        'BANCO PICHINCHA S.A.',
        'BANCOOMEVA',
        'BANCO FALABELLA S.A.',
        'BANCO FINANDINA S.A.',
        'BANCO SANTANDER DE NEGOCIOS COLOMBIA S.A. - BANCO SANTANDER',
        'BANCO COOPERATIVO COOPCENTRAL',
    ];

    public static $IT_DISABILITY_TYPES = [
        "2" => [
            "NAME" => "PARA CONTENCIÓN DEL GASTO",
            "REASONS" => [
                "6" => "CONCEPTO DE RHB FAVORABLE",
                "9" => "IT MAYOR A 180 DÍAS",
                "5" => "IT MAYOR A 540 DÍAS",
                "8" => "IT MAYOR A 60 DÍAS",
                "10" => "MAYOR A 3 IT EN EL ULTIMO SEMESTRE",
                "7" => "SUPERA DÍAS MAXIMO DE IT POR DX",
            ],
            "RESULTS" => [
                "3" => [
                    "NAME" => "PERTINENTE POR DX",
                    "DETAILS" => [
                        "62" => "INCAPACIDAD PERTINENTE",
                        "63" => "DIAGNÓSTICO DE DIFÍCIL MANEJO",
                    ],
                ],
                "4" => [
                    "NAME" => "PARA REVISIÓN POR COMITÉ DE INC.",
                    "DETAILS" => [
                        "70" => "ERROR EN EL TRATAMIENTO MÉDICO",
                        "71" => "INCAPACIDAD EXPEDIDA POR MEDICINA GENERAL",
                        "72" => "DIAGNOSTICO NO PERTINENTE PARA DÍAS DE IT",
                        "80" => "ENFERMEDAD LABORAL ",
                        "81" => "ACCIDENTE DE TRABAJO",
                        "82" => "MAL MANEJO MÉDICO",
                        "83" => "OTRO",
                    ],
                ],
                "5" => [
                    "NAME" => "AFILIADO PENSIONADO",
                    "DETAILS" => [
                        "3" => "PACIENTE PENSIONADO POR INVALIDEZ",
                        "4" => "PACIENTE PENSIONADO POR VEJEZ",
                        "5" => "PENSIONADO PERO LABORA ACTUALMENTE",
                    ],
                ],
                "6" => [
                    "NAME" => "AUDITORÍA DE CASO",
                    "DETAILS" => [
                        "6" => "AFILIADO FALLECIDO",
                        "7" => "CASO ALERTA",
                        "8" => "CASO SOLICITADO EN MESA LABORAL",
                    ],
                ],
            ],
        ],
        "1" => [
            "NAME" => "SOLICITADA POR PRESTACIONES ECONÓMICAS",
            "REASONS" => [
                "1" => "IPS NO ADSCRITA",
                "4" => "OTRO",
                "3" => "PQR, FAMIGO O TUTELA",
                "2" => "SOSPECHA FRAUDE",
            ],
            "RESULTS" => [
                "1" => [
                    "NAME" => "APROBADA",
                    "DETAILS" => [
                        "1" => "INCAPACIDAD PERTINENTE",
                        "2" => "SUMATORIA ITP POR DX CORRELACIONADOS",
                    ],
                ],
                "2" => [
                    "NAME" => "RECHAZADA",
                    "DETAILS" => [
                        "30" => "SIN SOPORTES",
                        "31" => "SIN SOPORTES",
                        "32" => "FECHAS DE ITP NO COINCIDE CON LA OM O HC",
                        "33" => "DIAGNOSTICO DE ITP NO COINCIDE CON LA OM O HC",
                        "39" => "ITP SUPERIOR 180 DÍAS",
                        "43" => "ENFERMEDAD LABORAL",
                        "44" => "ACCIDENTE DE TRABAJO",
                        "45" => "EL PRESTADOR NO REGISTRA EN EL REGISTRO ESPECIAL DE PRESTADORES DE SERVICIOS DE SALUD DEL MINISTERIO DE SALUD",
                        "46" => "LA HISTORIA CLÍNICA NO CORRESPONDE AL PERIODO DE LA INCAPACIDAD RADICADA",
                        "47" => "LA HISTORIA CLÍNICA  SE ENCUENTRA INCOMPLETA",
                        "60" => "LA HISTORIA CLÍNICA, NO ES SUFICIENTE PARA SOPORTAR EL PERIODO DE INCAPACIDAD",
                        "61" => "DÍAS DE INCAPACIDAD EXCEDE EL NÚMERO MÁXIMO PARA EL DIAGNOSTICO",
                    ],
                ],

            ],
        ],
    ];

    public static $GLOSA_REASONS_STD = [
        1 => [
            "NAME" => "Facturación",
            "DETAILS" => [
                1 => "Estancia",
                2 => "Consultas, interconsultas y visitas médicas",
                3 => "Honorarios médicos en procedimientos",
                4 => "Honorarios otros profesionales asistenciales",
                5 => "Derechos de sala",
                6 => "Materiales",
                7 => "Medicamentos",
                8 => "Ayudas diagnósticas ",
                9 => "Conjunto integral de atención",
                11 => "Servicio o insumo incluido en estancia o derechos de sala",
                12 => "Factura excede topes autorizados",
                14 => "Error en suma de conceptos facturados",
                22 => "Prescripción dentro de los términos legales",
                23 => "Procedimiento o actividad ",
                24 => "Falta firma del prestador de servicios de salud",
                27 => "Servicio o procedimiento incluido en otro",
                28 => "Orden cancelada al prestador de servicios de salud",
            ],
        ],
        2 => [
            "NAME" => "Tarifas",
            "DETAILS" => [
                1 => "Estancia",
                2 => "Consultas, interconsultas y visitas médicas",
                3 => "Honorarios médicos en procedimientos",
                4 => "Honorarios otros profesionales asistenciales",
                5 => "Derechos de sala",
                6 => "Materiales",
                7 => "Medicamentos",
                8 => "Ayudas diagnósticas ",
                9 => "Conjunto integral de atención",
                23 => "Procedimiento o actividad",
            ],
        ],
        3 => [
            "NAME" => "Soportes",
            "DETAILS" => [
                1 => "Estancia",
                2 => "Consultas, interconsultas y visitas médicas",
                3 => "Honorarios médicos en procedimientos",
                4 => "Honorarios otros profesionales asistenciales",
                7 => "Medicamentos",
                8 => "Ayudas diagnósticas ",
                9 => "Conjunto integral de atención",
                32 => "Factura o cuenta de cobro con el Detalle de cargos",
                33 => "Copia de la epicrisis ",
                35 => "Formato accidente de tránsito y enfermedad profesional ATEP",
                36 => "Copia de factura o detalle de cargos de Seguro obligatorio de accidentes de tránsito SOAT",
                39 => "Comprobante de recibido del usuario",
                53 => "Certificación de la ocurrencia del accidente de transito",
                54 => "Acreditación de la condición de victima",
                55 => "Registro Civil de Defunción, Forma DANE IP 25-1V88 o Certificado de defunción expedido por el Notario",
                56 => "Certificación de la Fiscalía en la cual curse el proceso de muerte en accidente de tránsito de la víctima, si fuere el caso",
                57 => "Original del dictamen sobre la incapacidad permanente, expedido por las Juntas de Calificación de Invalidez, entidades creadas por la Ley 100 de 1993.",
                58 => "Acta de levantamiento de cadáver cuando la muerte se haya producido en el lugar del accidente o evento terrorista o catastrófico",
                59 => "Requisitos generales para reclamaciones por persona natural",
                60 => "Acreditación de los padres como beneficiarios",
                61 => "Acreditación de cónyuge sobreviviente como beneficiario",
                62 => "Acreditación del (la) compañero(a) permanente como beneficiario",
                63 => "Acreditación de un hijo de la victima como beneficiario",
                64 => "Acreditación de hermano de la victima como beneficiarios",
                65 => "Acreditación de persona con una relación con la victima, distinta a las señaladas como beneficiario",
                66 => "Requisitos generales para reclamaciones por prestadores de servicios de salud",
            ],
        ],
        5 => [
            "NAME" => "Coberturas",
            "DETAILS" => [
                1 => "Estancia",
                2 => "Consultas, interconsultas y visitas médicas",
                6 => "Materiales ",
                7 => "Medicamentos",
                8 => "Ayudas diagnósticas",
                23 => "Procedimiento o actividad ",
                27 => "Servicio o procedimiento incluido en otro",
                46 => "Cobertura sin agotar en la póliza Seguro obligatorio de accidentes de tránsito (SOAT)",
            ],
        ],
        6 => [
            "NAME" => "Pertinencia",
            "DETAILS" => [
                1 => "Estancia",
                2 => "Consultas, interconsultas y visitas médicas",
                3 => "Honorarios médicos en procedimientos",
                4 => "Honorarios otros profesionales asistenciales",
                5 => "Derechos de sala",
                6 => "Materiales",
                7 => "Medicamentos",
                8 => "Ayudas diagnósticas ",
                23 => "Procedimiento o actividad ",
            ],
        ],
        7 => [
            "NAME" => "ECAT",
            "DETAILS" => [
                67 => "Habilitación",
                68 => "Concordancia ",
                69 => "Beneficiario sujeto a acción de repetición",
            ],
        ],
        8 => [
            "NAME" => "Devoluciones",
            "DETAILS" => [
                49 => "Factura no cumple requisitos legales",
                50 => "Factura ya cancelada",
            ],
        ],
        9 => [
            "NAME" => "Respuestas a Glosas o Devoluciones",
            "DETAILS" => [
                96 => "Glosa o devolución injustificada",
                97 => "No subsanada (Glosa o devolución totalmente aceptada)",
                98 => "Subsanada parcial (Glosa o devolución parcialmente aceptada)",
                99 => "Subsanada (Glosa o devolución no aceptada)",
            ],
        ],
    ];

    public static $RISK_FACTOR_ORIGIN_CALIFICATION = [
        1 => [
            "NAME" => "Biológico",
            "OPTIONS" => [
                1 => 'Virus',
                2 => 'Bacterias',
                3 => 'Hongos',
                4 => 'Ricketsias',
                5 => 'Parásitos',
                6 => 'Picaduras',
                7 => 'Mordeduras',
            ]
        ],
        2 => [
            "NAME" => "Físico",
            "OPTIONS" => [
                1 => 'Ruido',
                2 => 'Iluminación',
                3 => 'Vibración',
                4 => 'Temperaturas',
                5 => 'Presión Atmosférica',
                6 => 'Radiaciones Ionizantes',
            ]
        ],
        3 => [
            "NAME" => "Químico",
            "OPTIONS" => [
                1 => 'Polvos Orgánicos',
                2 => 'Fibras',
                3 => 'Líquidos',
                4 => 'Gases y Vapores',
                5 => 'Humos Metálicos / No Metálicos',
                6 => 'Material Particulado',
            ]
        ],
        4 => [
            "NAME" => "Psicosocial",
            "OPTIONS" => [
                1 => 'Gestión Organizacional',
                2 => 'Caracteristicas de la organización del trabajo',
                3 => 'Caracteristicas del grupo social de trabajo',
                4 => 'Condiciones de la tarea',
                5 => 'Interface persona - tarea',
                6 => 'Jornada de Trabajo',
            ]
        ],
        5 => [
            "NAME" => "Biomecánicos",
            "OPTIONS" => [
                1 => 'Postura',
                2 => 'Esfuerzo',
                3 => 'Movimiento Repetitivo',
                4 => 'Manipulación Manual de Cargas',
            ]
        ],
        6 => [
            "NAME" => "Condiciones de Seguridad",
            "OPTIONS" => [
                1 => 'Mecánico',
                2 => 'Eléctrico',
                3 => 'Locativo',
                4 => 'Tecnológico',
                5 => 'Accidentes de Tránsito',
                6 => 'Públicos',
                7 => 'Trabajo en Alturas',
                8 => 'Espacios confinados',
            ]
        ],
        7 => [
            "NAME" => "Fenómenos naturales",
            "OPTIONS" => [
                1 => 'Sismo',
                2 => 'Terremoto',
                3 => 'Vendaval',
                4 => 'Inundación',
                5 => 'Derrumbe',
                6 => 'Precipitaciones',
            ]
        ],
    ];

    public static $INABILITY_ORIGIN = [
        "1" => "1. Enfermedad General",
        "2" => "2. Enfermedad Laboral",
        "3" => "3. Accidente de Trabajo",
        "4" => "4. Licencia de Maternidad",
        "5" => "5. Licencia de Paternidad",
        "6" => "6. Enfermedad General - SOAT",
    ];

    public static $PENDING_CLOSURE_CAUSE = [
        "1" => "En rehabilitación",
        "2" => "Pendiente adición diagnóstico",
        "3" => "Pendiente cierre riesgo biológico",
        "4" => "Pendiente contacto efectivo",
        "5" => "Se asigna cita médica MNK",
        "6" => "Garantía de PCG",
    ];

    public static $EVENT_TYPE = [
        "1" => "No requiere atención médica",
        "2" => "Solo urgencia",
        "3" => "Hospitalización",
        "4" => "SOA",
    ];

    public static $FUNCTIONAL_PROGNOSIS = [
        "1" => "Sin secuelas",
        "2" => "Bueno",
        "3" => "Regular",
        "4" => "Malo",
    ];


    public static $REHABILITATION_DETAILS = [
        "1" => "No aplica",
        "2" => "Cirugía de mano",
        "3" => "Cirugía maxilofacial",
        "4" => "Concepto de RHB",
        "5" => "Estudios diagnósticos",
        "6" => "Fisiatría",
        "7" => "Cirugía de pie",
        "8" => "Medicina laboral",
        "9" => "Neurocirugía",
        "10" => "Odontología",
        "11" => "Oftalmología y subespecialidades",
        "12" => "Ortopedia",
        "13" => "Otorrinolaringología",
        "14" => "Procedimiento quirúrgico",
        "15" => "Terapias",
        "16" => "SOAT",
        "17" => "Renovar Servicios",
        "18" => "Urología",
        "19" => "Medicina del dolor",
        "20" => "Cirugía del pie",
    ];

    public static $INABILITY_TYPE = [
        "1" => "Incapacidad",
        "2" => "Licencia de Maternidad",
        "3" => "Licencia de Paternidad",
    ];

    public static $IT_RESULT_INABILITY = [
        "1" => "APROBADA",
        "2" => "RECHAZADA",
        "3" => "EN ESTUDIO",
    ];
    public static $CATEGORIES_AND_CAUSAL = [
        1 => [
            "NAME" => "Atención al cliente en puntos de atención",
            "OPTIONS" => [
                1 => [
                    "NAME" => "Demora en puntos de atención",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                2 => [
                    "NAME" => "Servicio insatisfactorio, prestado por el personal de los puntos de atención",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                3 => [
                    "NAME" => "Servicio satisfactorio, prestado por el personal de los puntos de atención",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
            ],
        ],
        2 => [
            "NAME" => "Autorización de servicios de ambulancia, de traslados y hospedaje",
            "OPTIONS" => [
                4 => [
                    "NAME" => "Autorizaciones, procedimientos no pertinentes",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "PRESTACION DE SERVICIOS DE SALUD"
                    ]
                ],
                5 => [
                    "NAME" => "Certificación tope SOAT",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "PRESTACION DE SERVICIOS DE SALUD"
                    ]
                ],
                6 => [
                    "NAME" => "Demora en la gestión de autorización",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "PRESTACION DE SERVICIOS DE SALUD"
                    ]
                ],
                7 => [
                    "NAME" => "Error u omisión autorización",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "PRESTACION DE SERVICIOS DE SALUD"
                    ]
                ],
                8 => [
                    "NAME" => "Falta de seguimiento en la gestión de la autorización",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "PRESTACION DE SERVICIOS DE SALUD"
                    ]
                ],
                9 => [
                    "NAME" => "Lo solicitado no se deriva del Diagnostico laboral",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "PRESTACION DE SERVICIOS DE SALUD"
                    ]
                ],
                10 => [
                    "NAME" => "Mala atención en el proceso autorizador",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "PRESTACION DE SERVICIOS DE SALUD"
                    ]
                ],
                11 => [
                    "NAME" => "No contacto con la línea",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "PRESTACION DE SERVICIOS DE SALUD"
                    ]
                ],
                12 => [
                    "NAME" => "PCL 0%",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "PRESTACION DE SERVICIOS DE SALUD"
                    ]
                ],
                13 => [
                    "NAME" => "Requerimiento no es contra palig, proceso jurídico nos vincula",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "PRESTACION DE SERVICIOS DE SALUD"
                    ]
                ],
                14 => [
                    "NAME" => "Sin cobertura (AT)",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "PRESTACION DE SERVICIOS DE SALUD"
                    ]
                ],
                15 => [
                    "NAME" => "Traslado fallido pertinente",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "PRESTACION DE SERVICIOS DE SALUD"
                    ]
                ],
                16 => [
                    "NAME" => "Traslado gestionado",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "PRESTACION DE SERVICIOS DE SALUD"
                    ]
                ]
            ],
        ],
        3 => [
            "NAME" => "Autorizaciones Medicas",
            "OPTIONS" => [
                17 => [
                    "NAME" => "Autorización Generada",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "PRESTACION DE SERVICIOS DE SALUD"
                    ]
                ],
                18 => [
                    "NAME" => "Autorizaciones, procedimientos no pertinentes",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "PRESTACION DE SERVICIOS DE SALUD"
                    ]
                ],
                19 => [
                    "NAME" => "Deficiencia en la comunicación con la línea",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "PRESTACION DE SERVICIOS DE SALUD"
                    ]
                ],
                20 => [
                    "NAME" => "Falta de seguimiento a las autorizaciones generadas",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "PRESTACION DE SERVICIOS DE SALUD"
                    ]
                ],
                21 => [
                    "NAME" => "Inoportunidad en el cargue de la autorización",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "PRESTACION DE SERVICIOS DE SALUD"
                    ]
                ],
                22 => [
                    "NAME" => "Lo solicitado es para manejo de dx no reconocido",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "PRESTACION DE SERVICIOS DE SALUD"
                    ]
                ],
                23 => [
                    "NAME" => "Negación de servicios pertinentes",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "PRESTACION DE SERVICIOS DE SALUD"
                    ]
                ],
                24 => [
                    "NAME" => "Requerimiento no es contra palig, proceso jurídico nos vincula",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "PRESTACION DE SERVICIOS DE SALUD"
                    ]
                ],
                25 => [
                    "NAME" => "Sin Certificación tope SOAT",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "PRESTACION DE SERVICIOS DE SALUD"
                    ]
                ],
                26 => [
                    "NAME" => "Sin Orden Médica",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "PRESTACION DE SERVICIOS DE SALUD"
                    ]
                ],
                27 => [
                    "NAME" => "Siniestro con PCL 0%",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "PRESTACION DE SERVICIOS DE SALUD"
                    ]
                ],
                28 => [
                    "NAME" => "Siniestro Inactivo (EL)",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "PRESTACION DE SERVICIOS DE SALUD"
                    ]
                ],
                29 => [
                    "NAME" => "Siniestro sin cobertura (AT)",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "PRESTACION DE SERVICIOS DE SALUD"
                    ]
                ]
            ]
        ],
        4 => [
            "NAME" => "Calidad en el servicio de traslados y alojamiento",
            "OPTIONS" => [
                30 => [
                    "NAME" => "Asegurado que se reúsa ha asistir a citas programas, o no toma servicios autorizados",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "PLAN DE MODELO DE ATENCION"
                    ]
                ],
                31 => [
                    "NAME" => "Atención brindada por el proveedor autorizado de manera irrespetuosa",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "PLAN DE MODELO DE ATENCION"
                    ]
                ],
                32 => [
                    "NAME" => "Contingencias de fuerza mayor o caso fortuito",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "PLAN DE MODELO DE ATENCION"
                    ]
                ],
                33 => [
                    "NAME" => "Falta de seguimiento y coordinación telefónica por el proveedor autorizado con el asegurado",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "PLAN DE MODELO DE ATENCION"
                    ]
                ],
                34 => [
                    "NAME" => "Inoportunidad en los servicios de traslados autorizados",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "PLAN DE MODELO DE ATENCION"
                    ]
                ],
                35 => [
                    "NAME" => "No se evidencias fallas en el suministro del servicio",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "PLAN DE MODELO DE ATENCION"
                    ]
                ],
                36 => [
                    "NAME" => "Proveedor asignado no cumple con las condiciones requeridas por el asegurado para la prestación del servicio (infraestructura, alimentación)",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "PLAN DE MODELO DE ATENCION"
                    ]
                ],
                37 => [
                    "NAME" => "Requerimiento no es contra palig, proceso jurídico nos vincula",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "PLAN DE MODELO DE ATENCION"
                    ]
                ],
                38 => [
                    "NAME" => "Servicio autorizado",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "PLAN DE MODELO DE ATENCION"
                    ]
                ],
                39 => [
                    "NAME" => "Servicio autorizado de acuerdo a disponibilidad del proveedor",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "PLAN DE MODELO DE ATENCION"
                    ]
                ],
                40 => [
                    "NAME" => "Trato irrespetuoso por parte del proveedor",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "PLAN DE MODELO DE ATENCION"
                    ]
                ],
                41 => [
                    "NAME" => "Vehículo asignado que no cumple con los estándares requeridos",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "PLAN DE MODELO DE ATENCION"
                    ]
                ]
            ]
        ],
        5 => [
            "NAME" => "Calidad en la atención de la red de IPS",
            "OPTIONS" => [
                42 => [
                    "NAME" => "Deficiencias en la comunicación con la IPS",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "PLAN DE MODELO DE ATENCION"
                    ]
                ],
                43 => [
                    "NAME" => "Expedición de Incapacidades medicas retroactivas",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "PLAN DE MODELO DE ATENCION"
                    ]
                ],
                44 => [
                    "NAME" => "Falta de información por parte del medico",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "PLAN DE MODELO DE ATENCION"
                    ]
                ],
                45 => [
                    "NAME" => "Inoportunidad en la agenda",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "PLAN DE MODELO DE ATENCION"
                    ]
                ],
                46 => [
                    "NAME" => "No se cuenta servicio requerido",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "PLAN DE MODELO DE ATENCION"
                    ]
                ],
                47 => [
                    "NAME" => "Requerimiento no es contra palig, proceso jurídico nos vincula",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "PLAN DE MODELO DE ATENCION"
                    ]
                ],
                48 => [
                    "NAME" => "Trato irrespetuoso por parte del proveedor",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "PLAN DE MODELO DE ATENCION"
                    ]
                ],
                49 => [
                    "NAME" => "Usuario inconforme con el tratamiento médico asignado",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "PLAN DE MODELO DE ATENCION"
                    ]
                ]
            ]
        ],
        6 => [
            "NAME" => "Calidad en la entrega de medicamentos",
            "OPTIONS" => [
                50 => [
                    "NAME" => "Deficiencias en la comunicación con el proveedor de medicamentos",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "PLAN DE MODELO DE ATENCION"
                    ]
                ],
                51 => [
                    "NAME" => "Entrega parcial de medicamentos",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "PLAN DE MODELO DE ATENCION"
                    ]
                ],
                52 => [
                    "NAME" => "Falta de actualización datos de contacto",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "PLAN DE MODELO DE ATENCION"
                    ]
                ],
                53 => [
                    "NAME" => "Inoportunidad en la entrega de los medicamentos",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "PLAN DE MODELO DE ATENCION"
                    ]
                ],
                54 => [
                    "NAME" => "Medicamento con carta de desabastecimiento",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "PLAN DE MODELO DE ATENCION"
                    ]
                ],
                55 => [
                    "NAME" => "Medicamentos e insumos no pertinentes",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "PLAN DE MODELO DE ATENCION"
                    ]
                ],
                56 => [
                    "NAME" => "No contacto del asegurado con el punto autorizado",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "PLAN DE MODELO DE ATENCION"
                    ]
                ],
                57 => [
                    "NAME" => "Requerimiento no es contra palig, proceso jurídico nos vincula",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "PLAN DE MODELO DE ATENCION"
                    ]
                ]
            ]
        ],
        7 => [
            "NAME" => "Copias",
            "OPTIONS" => [
                58 => [
                    "NAME" => "Copias-Autorizaciones Inoportunidad en la remisión de soportes requeridos",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "COPIAS"
                    ]
                ],
                59 => [
                    "NAME" => "Copias-Autorizaciones Soporte de pago sin gestión efectiva",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "COPIAS"
                    ]
                ],
                60 => [
                    "NAME" => "Copias-Red IPS Servicio no efectivo por parte del proveedor autorizado",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "COPIAS"
                    ]
                ],
                61 => [
                    "NAME" => "Copias-Red IPS Soporte de evidencia de la prestación del servicio (Planillas o Boucher)",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "COPIAS"
                    ]
                ],
                62 => [
                    "NAME" => "Copias-Reporte y comprobación: Inoportunidad en la remisión de soportes requeridos",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "COPIAS"
                    ]
                ],
                63 => [
                    "NAME" => "Copias-Reporte y comprobación: Soporte de pago sin gestión efectiva",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "COPIAS"
                    ]
                ],
                64 => [
                    "NAME" => "Requerimiento no es contra palig, proceso jurídico nos vincula",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "COPIAS"
                    ]
                ]
            ]
        ],
        8 => [
            "NAME" => "Corrección Furat",
            "OPTIONS" => [
                65 => [
                    "NAME" => "Corrección de Furat con soportes clínicos",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "REPORTE Y COMPROBACION DE DERECHOS"
                    ]
                ],
                66 => [
                    "NAME" => "Falta historia clínica para corrección",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "REPORTE Y COMPROBACION DE DERECHOS"
                    ]
                ],
            ]
        ],
        9 => [
            "NAME" => "Cuentas Médicas",
            "OPTIONS" => [
                67 => [
                    "NAME" => "Cruce de Saldo COVID 19",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                68 => [
                    "NAME" => "Cuentas Médicas",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                69 => [
                    "NAME" => "Inoportunidad en la gestión",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                70 => [
                    "NAME" => "Sin Cruce de Saldo COVID 19",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
            ]
        ],
        10 => [
            "NAME" => "Determinación del origen",
            "OPTIONS" => [
                71 => [
                    "NAME" => "Evento Común art 12",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "REPORTE Y COMPROBACION DE DERECHOS"
                    ]
                ],
                72 => [
                    "NAME" => "Evento con origen en firme",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "REPORTE Y COMPROBACION DE DERECHOS"
                    ]
                ],
                73 => [
                    "NAME" => "Evento ya calificado",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "REPORTE Y COMPROBACION DE DERECHOS"
                    ]
                ],
                74 => [
                    "NAME" => "Falta de seguimiento a la radicación de pruebas solicitadas",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "REPORTE Y COMPROBACION DE DERECHOS"
                    ]
                ],
                75 => [
                    "NAME" => "Inoportunidad en el cargue de los siniestros",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "REPORTE Y COMPROBACION DE DERECHOS"
                    ]
                ],
                76 => [
                    "NAME" => "Inoportunidad en el pronunciamiento de calificación en primera oportunidad",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "REPORTE Y COMPROBACION DE DERECHOS"
                    ]
                ],
                77 => [
                    "NAME" => "Inoportunidad en la determinación del origen",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "REPORTE Y COMPROBACION DE DERECHOS"
                    ]
                ],
                78 => [
                    "NAME" => "Inoportunidad en la revisión de pertinencia",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "REPORTE Y COMPROBACION DE DERECHOS"
                    ]
                ],
                79 => [
                    "NAME" => "No hay reporte del Accidente",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "REPORTE Y COMPROBACION DE DERECHOS"
                    ]
                ],
                80 => [
                    "NAME" => "No hay reporte del Accidente o enfermedad Laboral",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "REPORTE Y COMPROBACION DE DERECHOS"
                    ]
                ],
                81 => [
                    "NAME" => "No se cuenta con historia clínica que soporte la patología",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "REPORTE Y COMPROBACION DE DERECHOS"
                    ]
                ],
                82 => [
                    "NAME" => "Origen en controversia",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "REPORTE Y COMPROBACION DE DERECHOS"
                    ]
                ],
                83 => [
                    "NAME" => "Pendiente aporte de pruebas solicitadas",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "REPORTE Y COMPROBACION DE DERECHOS"
                    ]
                ],
                84 => [
                    "NAME" => "Radicación de soportes médicos",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "REPORTE Y COMPROBACION DE DERECHOS"
                    ]
                ],
                85 => [
                    "NAME" => "Requerimiento no es contra palig, proceso jurídico nos vincula",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "REPORTE Y COMPROBACION DE DERECHOS"
                    ]
                ],
                86 => [
                    "NAME" => "Sin cobertura",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "REPORTE Y COMPROBACION DE DERECHOS"
                    ]
                ],
                87 => [
                    "NAME" => "Usuario inactivo",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "REPORTE Y COMPROBACION DE DERECHOS"
                    ]
                ]
            ]
        ],
        11 => [
            "NAME" => "Incapacidad Temporal",
            "OPTIONS" => [
                88 => [
                    "NAME" => "Ajuste de IBC",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                89 => [
                    "NAME" => "Certificaciones",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                90 => [
                    "NAME" => "Corrección de información en el formulario de radicación IT ",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                91 => [
                    "NAME" => "Cuenta bancaria no habilitada o de un tercero",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                92 => [
                    "NAME" => "Culminación Proceso de Rehabilitación",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                93 => [
                    "NAME" => "Demora en la calificación del origen",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                94 => [
                    "NAME" => "Diagnóstico y origen calificado como común",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                95 => [
                    "NAME" => "Fallo Judicial",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                96 => [
                    "NAME" => "Incapacidad radicada, liquidada y pagada",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                97 => [
                    "NAME" => "Incapacidades emitidas por medico fuera de red",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                98 => [
                    "NAME" => "Negación de reconocimiento de incapacidades",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                99 => [
                    "NAME" => "No procede reconocimiento de Incapacidades-COVID-19",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                100 => [
                    "NAME" => "No se evidencia Radicación",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                101 => [
                    "NAME" => "Pagos aportes salud y pensión IT enfermedad común - otros",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                102 => [
                    "NAME" => "Pagos aportes salud y pensión IT enfermedad laboral",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                103 => [
                    "NAME" => "Pendiente por soportes para reconocer, liquidar y pagar",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                104 => [
                    "NAME" => "Radiación fallida Web",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                105 => [
                    "NAME" => "Reconocimiento de Incapacidades-COVID-19",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                106 => [
                    "NAME" => "Requerimiento no es contra palig, proceso jurídico nos vincula",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                107 => [
                    "NAME" => "Retardo en el pago",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                108 => [
                    "NAME" => "Sin aporte de documentos soportes",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                109 => [
                    "NAME" => "Sin Auditoría",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                110 => [
                    "NAME" => "Sin Calificación de Origen/Pruebas (siniestro no reportado)",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                111 => [
                    "NAME" => "Sin Calificación de Origen/Pruebas (siniestro reportado)",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                112 => [
                    "NAME" => "Sin incapacidades radicadas",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                113 => [
                    "NAME" => "Sin notificación de devolución",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                114 => [
                    "NAME" => "Sin notificación de objeción",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                115 => [
                    "NAME" => "Sin relación laboral",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                116 => [
                    "NAME" => "Siniestro bloqueado",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                117 => [
                    "NAME" => "Siniestro desbloqueado",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                118 => [
                    "NAME" => "Siniestro en Controversia",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                119 => [
                    "NAME" => "Siniestro no reportado",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                120 => [
                    "NAME" => "Siniestro sin cobertura",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ]
            ]
        ],
        12 => [
            "NAME" => "Indemnización por Incapacidad Permanente Parcial",
            "OPTIONS" => [
                121 => [
                    "NAME" => "Ajuste de IBL- Reliquidación",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                122 => [
                    "NAME" => "Certificación de pago de  IPP",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                123 => [
                    "NAME" => "Corrección de información en el formulario de radicación IPP",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                124 => [
                    "NAME" => "Cuenta bancaria no habilitada o de un tercero",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                125 => [
                    "NAME" => "Diagnóstico y origen calificado como común",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                126 => [
                    "NAME" => "Estado del trámite",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                127 => [
                    "NAME" => "IBL determinado, no pertinente de ajuste",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                128 => [
                    "NAME" => "Inconsistencias atribuibles al usuario Radicación documentos soportes",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                129 => [
                    "NAME" => "Inconsistencias en aplicativos",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                130 => [
                    "NAME" => "IPP radicada, liquidada y pagada",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                131 => [
                    "NAME" => "Negación de pago justificado (prescripción)",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                132 => [
                    "NAME" => "Número de cuenta bancaria registrada, no acorde a la suministrada por el usuario",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                133 => [
                    "NAME" => "Pendiente de pago en términos",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                134 => [
                    "NAME" => "Pertinencia de pago, IPP-IT radicadas sin gestión",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                135 => [
                    "NAME" => "Proceso jurídico",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                136 => [
                    "NAME" => "Radicación fallida Web",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                137 => [
                    "NAME" => "Requerimiento no es contra palig, proceso jurídico nos vincula",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                138 => [
                    "NAME" => "Retardo en el pago - fuera de términos",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                139 => [
                    "NAME" => "Sin notificación de etapa de investigación",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                140 => [
                    "NAME" => "Sin notificación de objeción",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                141 => [
                    "NAME" => "Sin relación laboral",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                142 => [
                    "NAME" => "Siniestro en Controversia",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                143 => [
                    "NAME" => "Siniestro sin cobertura",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                144 => [
                    "NAME" => "suspensión de términos",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ]
            ]
        ],
        13 => [
            "NAME" => "Información general de procesos de indemnizaciones",
            "OPTIONS" => [
                145 => [
                    "NAME" => "Copia acto administrativo que le reconoció pensión",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                146 => [
                    "NAME" => "Copia de los actos administrativos",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                147 => [
                    "NAME" => "Copias de acto(s) administrativos con soporte de pago",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                148 => [
                    "NAME" => "Copias de documentos con soporte de pago",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ]
            ]
        ],
        14 => [
            "NAME" => "Juntas de Calificación",
            "OPTIONS" => [
                149 => [
                    "NAME" => "Caso en trámite ante las Juntas de Calificación",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "REPORTE Y COMPROBACION DE DERECHOS"
                    ]
                ],
                150 => [
                    "NAME" => "Inactivo con esta ARL",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "REPORTE Y COMPROBACION DE DERECHOS"
                    ]
                ],
                151 => [
                    "NAME" => "Inoportunidad en el pago de honorarios",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "REPORTE Y COMPROBACION DE DERECHOS"
                    ]
                ],
                152 => [
                    "NAME" => "Inoportunidad en el trámite de controversia",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "REPORTE Y COMPROBACION DE DERECHOS"
                    ]
                ],
                153 => [
                    "NAME" => "Inoportunidad en la gestión por parte de las juntas",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "REPORTE Y COMPROBACION DE DERECHOS"
                    ]
                ],
                154 => [
                    "NAME" => "Inoportunidad en le remisión de expedientes",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "REPORTE Y COMPROBACION DE DERECHOS"
                    ]
                ],
                155 => [
                    "NAME" => "Recurso fuera de términos",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "REPORTE Y COMPROBACION DE DERECHOS"
                    ]
                ],
                156 => [
                    "NAME" => "Remisión expedientes incompletos",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "REPORTE Y COMPROBACION DE DERECHOS"
                    ]
                ],
                157 => [
                    "NAME" => "Requerimiento no es contra palig, proceso jurídico nos vincula",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "REPORTE Y COMPROBACION DE DERECHOS"
                    ]
                ],
                158 => [
                    "NAME" => "Sin cobertura",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "REPORTE Y COMPROBACION DE DERECHOS"
                    ]
                ]
            ]
        ],
        15 => [
            "NAME" => "Notificaciones",
            "OPTIONS" => [
                159 => [
                    "NAME" => "Envió de comunicación a dirección errada",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "REPORTE Y COMPROBACION DE DERECHOS"
                    ]
                ],
                160 => [
                    "NAME" => "Falta de actualización de datos de contacto ",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "REPORTE Y COMPROBACION DE DERECHOS"
                    ]
                ],
                161 => [
                    "NAME" => "Inoportunidad en la notificación de dictamen emitido",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "REPORTE Y COMPROBACION DE DERECHOS"
                    ]
                ],
                162 => [
                    "NAME" => "No gestión efectiva en caso de devolución",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "REPORTE Y COMPROBACION DE DERECHOS"
                    ]
                ],
                163 => [
                    "NAME" => "Notificación efectiva,  renotificación",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "REPORTE Y COMPROBACION DE DERECHOS"
                    ]
                ],
                164 => [
                    "NAME" => "Requerimiento no es contra palig, proceso jurídico nos vincula",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "REPORTE Y COMPROBACION DE DERECHOS"
                    ]
                ],
                165 => [
                    "NAME" => "Tramite gestionado recientemente",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "REPORTE Y COMPROBACION DE DERECHOS"
                    ]
                ]
            ]
        ],
        16 => [
            "NAME" => "Pensión de Invalidez",
            "OPTIONS" => [
                166 => [
                    "NAME" => "Pensión de invalidez radicada sin trámite solicitada a través de Derecho de Petición",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                167 => [
                    "NAME" => "Sin radicación de trámite solicitada a través de Derecho de Petición",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
            ]
        ],
        17 => [
            "NAME" => "Pensión de Sobrevivientes/Sustitutiva",
            "OPTIONS" => [
                168 => [
                    "NAME" => "Pensión de sobreviviente radicada sin trámite solicitada a través de Derecho de Petición",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                169 => [
                    "NAME" => "Sin radicación de trámite solicitada a través de Derecho de Petición",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                170 => [
                    "NAME" => "Sustitución pensional radicada sin trámite solicitada a través de Derecho de Petición",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
            ]
        ],
        18 => [
            "NAME" => "Pérdida de capacidad Laboral",
            "OPTIONS" => [
                171 => [
                    "NAME" => "Calificación, Recalificación o integral no pertinente",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "REPORTE Y COMPROBACION DE DERECHOS"
                    ]
                ],
                172 => [
                    "NAME" => "Cierre por parte de RHB",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "REPORTE Y COMPROBACION DE DERECHOS"
                    ]
                ],
                173 => [
                    "NAME" => "Cuenta con el tiempo establecido por la norma para la gestión de la solicitud",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "REPORTE Y COMPROBACION DE DERECHOS"
                    ]
                ],
                174 => [
                    "NAME" => "Entrega incompleta por parte de antigua ARL",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "REPORTE Y COMPROBACION DE DERECHOS"
                    ]
                ],
                175 => [
                    "NAME" => "Evento entregado a UGPP",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "REPORTE Y COMPROBACION DE DERECHOS"
                    ]
                ],
                176 => [
                    "NAME" => "Inoportunidad en la calificación de PCL por Medicina Laboral (alta médica o con 540 días)",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "REPORTE Y COMPROBACION DE DERECHOS"
                    ]
                ],
                177 => [
                    "NAME" => "Inoportunidad en la notificación de dictamen emitido",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "REPORTE Y COMPROBACION DE DERECHOS"
                    ]
                ],
                178 => [
                    "NAME" => "No cumple con los criterios establecidos por la norma para la revisión",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "REPORTE Y COMPROBACION DE DERECHOS"
                    ]
                ],
                179 => [
                    "NAME" => "No se cuenta con evidencia de progresión de sintomatología",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "REPORTE Y COMPROBACION DE DERECHOS"
                    ]
                ],
                180 => [
                    "NAME" => "No se cuenta con historia clínica que soporte sintomatología de origen común",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "REPORTE Y COMPROBACION DE DERECHOS"
                    ]
                ],
                181 => [
                    "NAME" => "Orden medica que respalda el tramite",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "REPORTE Y COMPROBACION DE DERECHOS"
                    ]
                ],
                182 => [
                    "NAME" => "Proceso de RHB sin culminar",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "REPORTE Y COMPROBACION DE DERECHOS"
                    ]
                ],
                183 => [
                    "NAME" => "Requerimiento no es contra palig, proceso jurídico nos vincula",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "REPORTE Y COMPROBACION DE DERECHOS"
                    ]
                ],
                184 => [
                    "NAME" => "Sin aporte de Historia Clínica o exámenes diagnósticos ya autorizados para continuidad con el proceso",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "REPORTE Y COMPROBACION DE DERECHOS"
                    ]
                ],
                185 => [
                    "NAME" => "Siniestro inactivo",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "REPORTE Y COMPROBACION DE DERECHOS"
                    ]
                ],
                186 => [
                    "NAME" => "Siniestro Inactivo (EL)",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "REPORTE Y COMPROBACION DE DERECHOS"
                    ]
                ],
                187 => [
                    "NAME" => "Siniestro no reconocido como laboral",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "REPORTE Y COMPROBACION DE DERECHOS"
                    ]
                ],
                188 => [
                    "NAME" => "Siniestro sin cobertura (AT)",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "REPORTE Y COMPROBACION DE DERECHOS"
                    ]
                ],
                189 => [
                    "NAME" => "Tramite gestionado recientemente",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "REPORTE Y COMPROBACION DE DERECHOS"
                    ]
                ]
            ]
        ],
        19 => [
            "NAME" => "Proceso Reconocimiento Vida",
            "OPTIONS" => [
                190 => [
                    "NAME" => "Trámite sin gestión",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ]
            ]
        ],
        20 => [
            "NAME" => "Reembolsos",
            "OPTIONS" => [
                191 => [
                    "NAME" => "Alimentación/hospedaje no procedente",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                192 => [
                    "NAME" => "Devolución de documento a solicitud del usuario",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                193 => [
                    "NAME" => "Doble cobro",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                194 => [
                    "NAME" => "En auditoria",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                195 => [
                    "NAME" => "EPS/Particular",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                196 => [
                    "NAME" => "Error del proveedor de la RED IPS",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                197 => [
                    "NAME" => "Error en auditoría",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                198 => [
                    "NAME" => "Error operador logístico Digitex",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                199 => [
                    "NAME" => "Estado de caso",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                200 => [
                    "NAME" => "Extemporáneo por prescripción",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                201 => [
                    "NAME" => "Inoportunidad de contacto con la línea reembolsos",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                202 => [
                    "NAME" => "Mala atención línea reembolsos",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                203 => [
                    "NAME" => "No procede reconocimiento de Reembolsos-COVID-19",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                204 => [
                    "NAME" => "Pago extemporáneo",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                205 => [
                    "NAME" => "Pago ya realizado/Información de pago",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                206 => [
                    "NAME" => "Reconocimiento de Reembolsos-COVID-19",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                207 => [
                    "NAME" => "Reembolso sin Radicar",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                208 => [
                    "NAME" => "Sin autorización de traslados",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                209 => [
                    "NAME" => "Sin notificación de devolución",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                210 => [
                    "NAME" => "Sin notificación de objeción",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                211 => [
                    "NAME" => "Sin soportes",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                212 => [
                    "NAME" => "Siniestro calificado como de origen común",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                213 => [
                    "NAME" => "Tarifa no acorde a las establecidas",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                214 => [
                    "NAME" => "Trámite sin tope Soat",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ],
                215 => [
                    "NAME" => "Traslado fallido por afiliado",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GERENCIA DE INDEMNIZACIONES",
                        "PROCESO" => "PRESTACIONES ECONÓMICAS"
                    ]
                ]
            ]
        ],
        21 => [
            "NAME" => "Rehabilitación",
            "OPTIONS" => [
                216 => [
                    "NAME" => "Asegurado cuenta con finalización de actividades propuestas por el programa de rehabilitación de la ARL",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "PRESTACION DE SERVICIOS DE SALUD"
                    ]
                ],
                217 => [
                    "NAME" => "Proceso de RHB sin culminar",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "PRESTACION DE SERVICIOS DE SALUD"
                    ]
                ],
                218 => [
                    "NAME" => "Recomendaciones emitidas recientemente",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "PRESTACION DE SERVICIOS DE SALUD"
                    ]
                ],
                219 => [
                    "NAME" => "Requerimiento no es contra palig, proceso jurídico nos vincula",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "PRESTACION DE SERVICIOS DE SALUD"
                    ]
                ],
                220 => [
                    "NAME" => "Siniestro Inactivo",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "PRESTACION DE SERVICIOS DE SALUD"
                    ]
                ],
                221 => [
                    "NAME" => "Siniestro sin cobertura",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "ENTIDAD",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "PRESTACION DE SERVICIOS DE SALUD"
                    ]
                ],
                222 => [
                    "NAME" => "Solicitudes de Empresas representativas, Juntas y Órdenes Judiciales",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "PRESTACION DE SERVICIOS DE SALUD"
                    ]
                ],
                223 => [
                    "NAME" => "Vencimiento del tiempo de las recomendaciones",
                    "OPTIONS" => [
                        "PROCEDENCIA" => "USUARIO",
                        "GERENCIA" => "GRUPO PQRD GERENCIA MÉDICA",
                        "PROCESO" => "PRESTACION DE SERVICIOS DE SALUD"
                    ]
                ]
            ]
        ]
    ];
    public static $IT_REASON_INABILITY = [
        '1' => 'APROBADA',
        '2' => 'CADUCADA',
        '22' => 'TRASLAPE',
        '3' => 'CERTIFICADO INCONSISTENTE',
        '4' => 'DOCUMENTOS PENDIENTES',
        '5' => 'FONDO DE PENSIONES ',
        '6' => 'MEDICO NO REGISTRADO EN RETHUS',
        '7' => 'MORA EN PAGOS',
        '8' => 'NO PERTINENCIA MÉDICA',
        '9' => 'NO PRESENTA AFILIACIÓN AL REGIMEN CONTRIBUTIVO',
        '10' => 'NO VIGENTE AL INICIO DE LA INCAPACIDAD',
        '11' => 'PENDIENTE AUDITORÍA',
        '12' => 'PENDIENTE PRESENTAR HISTORIA CLINICA',
        '13' => 'PRESTADOR NO SE ENCUENTRA HABILITADO',
        '14' => 'PRESUNTA FALSEDAD',
        '15' => 'PROCEDIMIENTO ESTETICO Y/O EXCLUSIONES POS',
        '16' => 'PROSPECTIVIDAD',
        '17' => 'RECONOCIMIENTO NO ESTÁ A CARGO DE LA EPS',
        '18' => 'RED NO ADSCRITA',
        '19' => 'RETROACTIVIDAD',
        '20' => 'SEMANAS DE COTIZACIÓN INSUFICIENTES',
        '21' => 'SIN ASIGNAR',
    ];
    public static $CATEGORICAL_INFO_PQR = [
        1 => [
            "NAME" => "Atención_al_cliente_en_los_puntos_de_atención",
            "OPTIONS" => [
                1 => 'Demora en puntos de atención',
                2 => 'Servicio insatisfactorio, prestado por el personal de los puntos de atención',
                3 => 'Servicio satisfactorio, prestado por el personal de los puntos de atención',
            ]
        ],
        2 => [
            "NAME" => "Cuentas_Médicas",
            "OPTIONS" => [
                1 => 'Inoportunidad en la gestión',
                2 => 'Cuentas Medicas',
            ]
        ],
        3 => [
            "NAME" => "Incapacidad_Temporal_radicada_no_pagada",
            "OPTIONS" => [
                1 => 'Sin notificación de objeción ',
                2 => 'Ajuste de IBC',
                3 => 'Sin notificación de devolución',
                4 => 'Sin Auditoría ',
                5 => 'Siniestro desbloqueado',
                6 => 'Demora en la calificación del origen ',
                7 => 'Corrección de información en el formulario de radicación IT ',
                8 => 'Retardo en el pago',
                9 => 'No se evidencia Radicación',
                10 => 'Incapacidad radicada, liquidada y pagada',
                11 => 'Culminación Proceso de Rehabilitación',
                12 => 'Diagnóstico y origen calificado como común',
                13 => 'Siniestro sin cobertura',
                14 => 'Pendiente por soportes para reconocer, liquidar y pagar',
                15 => 'Sin relación laboral',
                16 => 'Cuenta bancaria no habilitada o de un tercero. ',
                17 => 'Siniestro bloqueado',
                18 => 'Siniestro no reportado',
                19 => 'Siniestro en Controversia',
                20 => 'Sin aporte de documentos soportes ',
            ]
        ],
        4 => [
            "NAME" => "Indemnización_por_Incapacidad_Permanente_Parcial",
            "OPTIONS" => [
                1 => 'Sin notificación de objeción ',
                2 => 'Sin notificación de devolución',
                3 => '"Sin notificación de etapa de investigación',
                4 => '(suspensión de términos)."',
                5 => 'Inconsistencias en aplicativos',
                6 => 'Ajuste de IBL- Reliquidación',
                7 => 'Número de cuenta bancaria registrada, no acorde a la suministrada por el usuario',
                8 => 'Corrección de información en el formulario de radicación IPP ',
                9 => 'Retardo en el pago',
                10 => 'Estado de caso (notificación efectiva)',
                11 => 'Inconsistencias atribuibles al usuario. Radicación documentos soportes',
                12 => 'IPP radicada, liquidada y pagada',
                13 => 'IBL determinado, no pertinente de ajuste',
                14 => 'Negación de pago justificado (prescripción)',
                15 => 'Diagnóstico y origen calificado como común',
                16 => 'Siniestro sin cobertura',
                17 => 'Sin relación laboral',
                18 => 'Proceso jurídico ',
                19 => 'Cuenta bancaria no habilitada o de un tercero. ',
            ]
        ],
        5 => [
            "NAME" => "Copias_Indemnizaciones",
            "OPTIONS" => [
                1 => 'Copias de documentos con soporte de pago ',
                2 => 'Copias de documentos ',
            ]
        ],
        6 => [
            "NAME" => "Solicitud_de_reconocimiento_de_pensión_mediante_Derecho_de_Petición",
            "OPTIONS" => [
                1 => 'Pensión de invalidez radicada sin trámite',
                2 => 'Pensión de sobreviviente radicada sin trámite',
                3 => 'Sustitución pensional radicada sin trámite',
                4 => 'Sin radicación de trámite',
            ]
        ],
        7 => [
            "NAME" => "Proceso_Reconocimiento_Vida",
            "OPTIONS" => [
                1 => 'Trámite sin gestión',
                2 => 'Sin radicación de trámite',
                3 => 'Información siniestros Vida',
            ]
        ],
        8 => [
            "NAME" => "Reembolsos",
            "OPTIONS" => [
                1 => 'Error en auditoría.',
                2 => 'Sin notificación de objeción ',
                3 => 'Sin notificación de devolución',
                4 => 'Mala atención línea reembolsos',
                5 => 'Inoportunidad de contacto con la línea reembolsos',
                6 => 'Error del proveedor de la RED IPS',
                7 => 'Error operador logístico. Digitex.',
                8 => 'Pago extemporáneo',
                9 => 'Eps/Particular',
                10 => 'Reembolso sin Radicar',
                11 => 'Sin soportes ',
                12 => 'Extemporáneo por prescripción',
                13 => 'Tarifa no acorde a las establecidas',
                14 => 'Estado de caso  ',
                15 => 'Trámite sin tope Soat',
                16 => 'Devolución de documento a solicitud del usuario',
                17 => 'Siniestro calificado como de origen común',
                18 => 'Doble cobro',
                19 => 'Sin autorización de traslados',
                20 => 'Traslado fallido por afiliado',
                21 => 'En auditoria',
                22 => 'Alimentación/hospedaje no procedente',
                23 => 'Trámite administrativo',
                24 => 'Pago ya realizado/Información de pago',
            ]
        ],
        14 => [
            "NAME" => "Rehabilitación",
            "OPTIONS" => [
                1 => 'Asegurado cuenta con finalización de actividades propuestas por el programa de rehabilitación de la ARL.',
                2 => 'Vencimiento del tiempo de las recomendaciones',
                3 => 'Proceso de RHB sin culminar.',
                4 => 'Recomendaciones  emitidas recientemente',
                5 => 'Solicitudes de Empresas representativas, Juntas y Ordenes Judiciales',
                6 => 'Siniestro sin cobertura',
                7 => 'Siniestro Inactivo ',
            ]
        ],
        15 => [
            "NAME" => "Autorización_de_servicios_de_ambulancia_de_traslados_y_hospedaje",
            "OPTIONS" => [
                1 => 'Traslado fallido pertinente',
                2 => 'Demora en la gestión de autorización',
                3 => 'Falta de seguimiento en la gestión de la autorización',
                4 => 'Error u omisión autorización',
                5 => 'Mala atención en el proceso autorizador',
                6 => 'No contacto con la línea',
                7 => 'Certificación tope SOAT',
                8 => 'Traslado gestionado',
                9 => 'Lo solicitado no se deriva del Diagnostico laboral',
                10 => 'Sin cobertura (AT)',
                11 => 'PCL 0%',
            ]
        ],
        16 => [
            "NAME" => "Autorizaciones_Medicas",
            "OPTIONS" => [
                1 => 'Negación de servicios pertinentes',
                2 => 'Inoportunidad en el cargue de la autorización ',
                3 => 'Falta de seguimiento a las autorizaciones generadas',
                4 => 'Deficiencia en la comunicación con la línea',
                5 => 'Lo solicitado es para manejo de dx no reconocido ',
                6 => 'Sin Orden Médica',
                7 => 'Siniestro con PCL 0% ',
                8 => 'Sin Certificación tope SOAT',
                9 => 'Autorización Generada',
                10 => 'Siniestro sin cobertura (AT)',
                11 => 'Siniestro Inactivo (EL)',
            ]
        ],
        17 => [
            "NAME" => "Copias",
            "OPTIONS" => [
                1 => 'Soporte de pago sin gestión efectiva',
                2 => 'Soporte de evidencia de la prestación del servicio (Planillas o  Boucher)',
                3 => 'Soporte de pago sin gestión efectiva',
                4 => 'Inoportunidad en la remisión de soportes requeridos',
                5 => 'Servicio no efectivo por parte del proveedor autorizado',
                6 => 'Inoportunidad en la remisión de soportes requeridos',

            ]
        ],
        18 => [
            "NAME" => "Traslados_y_Alojamiento",
            "OPTIONS" => [
                1 => ' Inoportunidad en los servicios de traslados autorizados',
                2 => 'Atención brindada por el proveedor autorizado de manera irrespetuosa',
                3 => 'Vehículo asignado que no cumple con los estándares requeridos',
                4 => 'Falta de seguimiento y coordinación telefónica por el proveedor autorizado con  el asegurado',
                5 => 'Servicio autorizado',
                6 => 'No se evidencias fallas en el suministro del servicio',
                7 => 'Contingencias de fuerza mayor o caso fortuito',
                8 => 'Proveedor asignado no cumple con las condiciones requeridas por el asegurado para la prestación del servicio (infraestructura, alimentación)',
                9 => 'Trato irrespetuoso por parte del proveedor',
                10 => 'Servicio autorizado de acuerdo a disponibilidad del proveedor ',

            ]
        ],
        19 => [
            "NAME" => "Medicamentos",
            "OPTIONS" => [
                1 => 'Inoportunidad en la entrega de los medicamentos',
                2 => 'Entrega parcial de medicamentos',
                3 => 'Deficiencias en la comunicación con el proveedor de medicamentos',
                4 => 'Medicamento con carta de desabastecimiento',
                5 => 'No contacto del asegurado  con el punto autorizado ',
                6 => 'Falta de actualización datos de contacto ',
            ]
        ],
        20 => [
            "NAME" => "Calidad_en_la_atención_de_la_red_de_IPS",
            "OPTIONS" => [
                1 => 'Trato irrespetuoso por parte del proveedor',
                2 => 'Falta de información por parte del medico ',
                3 => 'Inoportunidad en la agenda',
                4 => 'No se cuenta servicio requerido',
                5 => 'Deficiencias en la comunicación con la IPS',
                6 => 'Expedición de Incapacidades medicas retroactivas',
                7 => 'Usuario inconforme con el tratamiento medico asignado',
            ]
        ],
        21 => [
            "NAME" => "Calificación_de_perdida_de_capacidad_Laboral",
            "OPTIONS" => [
                1 => 'Cierre por parte de RHB',
                2 => 'Inoportunidad en la calificación de PCL por Medicina Laboral (alta medica o con 540 días)',
                3 => 'Orden medica que respalda el tramite',
                4 => 'Sin aporte de Historia Clínica o exámenes diagnósticos  ya autorizados para continuidad con el proceso',
                5 => 'Proceso de RHB sin culminar',
                6 => 'Tramite gestionado recientemente ',
                7 => 'Siniestro sin cobertura (AT)',
                8 => 'Siniestro Inactivo (EL)',
                9 => 'Orden medica que respalda el tramite',
                10 => 'Inoportunidad en la notificación de dictamen emitido',
                11 => 'Cuenta con el tiempo establecido por la norma para la gestión de la solicitud',
                12 => 'Tramite gestionado recientemente ',
                13 => 'Siniestro inactivo ',
                14 => 'No se cuenta con evidencia de progresion de sintomatologia',
                15 => 'Inoportunidad en la notificación de dictamen emitido',
                16 => 'No se cuenta con historia clinica que soporte sintomatologia de origen comun',
                17 => 'Siniestro inactivo',
                18 => 'Cuenta con el tiempo establecido por la norma para la gestión de la solicitud',
                19 => 'Inoportunidad en la notificación de dictamen emitido',
                20 => 'Siniestro Inactivo ',
                21 => 'Siniestro no reconocido como laboral',
                22 => 'Entrega incompleta por parte de antigua ARL',
                23 => 'No cumple con los criterios establecidos por la norma para la revisión ',
                24 => 'Evento entregado a UGPP',

            ]
        ],
        22 => [
            "NAME" => "Determinación_del_origen",
            "OPTIONS" => [
                1 => 'Inoportunidad en la determinación del origen ',
                2 => 'Falta de seguimiento a la radicación de pruebas solicitadas ',
                3 => 'Inoportunidad en el cargue de los siniestros ',
                4 => 'Inoportunidad en el pronunciamiento de calificación en primera oportunidad ',
                5 => 'Sin cobertura ',
                6 => 'Usuario inactivo',
                7 => 'Evento ya calificado',
                8 => 'No hay reporte del Accidente o enfermedad Laboral',
                9 => 'Origen en controversia ',
                10 => 'Evento Comun art 12',
                11 => 'Pendiente aporte de pruebas solicitadas ',
                12 => 'Radicación de soportes médicos ',
                13 => 'Inoportunidad en la revisión de pertinencia ',
                14 => 'Sin cobertura ',
                15 => 'Evento con origen en firme',
                16 => 'No hay reporte del Accidente',
                17 => 'No se cuenta con historia clínica que soporte la patología ',
                18 => 'Origen en controversia ',
            ]
        ],
        23 => [
            "NAME" => "Junta_Calificación",
            "OPTIONS" => [
                1 => 'Inoportunidad en el pago de honorarios',
                2 => 'Inoportunidad en el tramite de controversia',
                3 => 'Inoportunidad en le remisión de expedientes',
                4 => 'Remisión expedientes incompletos ',
                5 => 'Caso en tramite ante las Juntas de Calificación',
                6 => 'Recurso fuera de términos',
                7 => 'Inactivo con esta ARL',
                8 => 'Sin cobertura ',
                9 => 'Inoportunidad en la gestión por parte de las juntas',
            ]
        ],
        24 => [
            "NAME" => "Corrección_Furat",
            "OPTIONS" => [
                1 => 'Corrección de Furat con soportes clínicos',
                2 => 'Falta historia clínica para corrección',
            ]
        ],
        25 => [
            "NAME" => "Notificaciones",
            "OPTIONS" => [
                1 => 'Inoportunidad en la notificación de dictamen emitido',
                2 => 'Falta de actualizacion de datos de contacto ',
                3 => 'No gestión efectiva en caso de devolución ',
                4 => 'Envió de comunicación a dirección errada',
                5 => 'Tramite gestionado recientemente ',
            ]
        ],

    ];
    public static $IT_DOCUMENT_TYPE_SOL_DOCS = [
        "1" => "Carta de autorización de pago a un tercero",
        "2" => "Certificación bancaria actualizada",
        "3" => "Certificado de incapacidades objeto de recobro",
        "4" => "Certificado Relación de Incapacidades (CRI) actualizado con dia inicial y diagnósticos",
        "5" => "Cuenta de cobro",
        "6" => "Declaración expresa donde conste que es(son) el(los) único(s) heredero(s) del afiliado fallecido",
        "7" => "Dictamen y constancia de ejecutoria del mismo",
        "8" => "En caso de varios herederos, carta de autorización a uno de ellos",
        "9" => "Fotocopia documento de identidad del(los) heredero(s)",
        "10" => "Orden de pago, soporte detalle de pago y soportes legibles",
        "11" => "Registro Civil de Defunción del afiliado o pensionado fallecido",
        "12" => "Registro Civil de Nacimiento del(los) herederos(s)",
        "13" => "Relación de incapacidades pagadas por la ARL",
        "14" => "Sentencia de juicio de sucesión",
        "15" => "Soporte de la transferencia electrónica",
    ];


    public static $QUALIFICATION_REASON_AUDIT = [
        '21' => 'APROBADO',
        '1' => 'PRESUNTA FALSEDAD',
        '2' => 'TRASLAPE',
        '3' => 'CADUCADA',
        '4' => 'SEMANAS DE COTIZACIÓN INSUFICIENTES',
        '5' => 'MORA EN PAGOS',
        '6' => 'RETROACTIVIDAD',
        '7' => 'CERTIFICADO INCONSISTENTE',
        '8' => 'DOCUMENTOS PENDIENTES',
        '9' => 'PROSPECTIVIDAD',
        '10' => 'PROCEDIMIENTO ESTETICO Y/O EXCLUSIONES POS',
        '11' => 'USUARIO EN PERIODO DE PROTECCIÓN LABORAL',
        '12' => 'PENDIENTE PRESENTAR HISTORIA CLINICIA',
        '13' => 'NOOVIGENTE AL INICIO DE LA INCAPACIDAD',
        '14' => 'RED NO ADSCRITA',
        '15' => 'NO PERTINENCIA MÉDICA',
        '16' => 'NO PRESENTA AFILIACIÓN AL REGIMEN CONTRIBUTIVO',
        '17' => 'SIN PAGOS EN EL MES DE INICIO',
        '18' => 'RECONOCIMIENTO NO ESTÁ A CARGO DE LA EPS',
        '19' => 'PRESTADOR NO SE ENCUENTRA HABILITADO',
        '20' => 'MÉDICO NO REGISTRADO EN RETHUS',
    ];
    public static $REJECTION_CAUSALS_TEMP = [
        1 => 'CADUCIDAD LICENCIA DE PATERNIDAD',
        2 => 'CADUCIDAD ',
        3 => 'CERTIFICADO INCONSISTENTE',
        5 => 'DOCUMENTOS PENDIENTES',
        15 => 'MEDICO NO REGISTRADO EN RETHUS',
        16 => 'NO VIGENTE AL INICIO DE LA INCAPACIDAD',
        17 => 'PENDIENTE PRESENTAR HISTORIA CLINICA',
        18 => 'PRESTADOR NO SE ENCUENTRA HABILITADO',
        19 => 'PRORROGA',
        20 => 'PROSPECTIVIDAD',
        21 => 'RED NO ADSCRITA',
        22 => 'RETROACTIVIDAD',
        23 => 'SEMANAS DE COTIZACION INSUFICIENTES',
        24 => 'SEMANAS DE COTIZACION INSUFICIENTES - PATERNIDAD',
        25 => 'SIN PAGO EN EL MES DE INICIO',
        26 => 'USUARIO EN PERIODO DE PROTECCION LABORAL',
        27 => 'TRATAMIENTOS ESTÉTICOS Y EXCLUSIONES DEL POS',
        28 => 'MORA TRABAJADOR DEPENDIENTE',
        29 => 'MORA TRABAJADOR INDEPENDIENTE',
        30 => 'FALSEDAD',
        31 => 'APORTE FUERA DEL PLAZO ESTABLECIDO',
    ];
    public static $REJECTION_CAUSALS_PCL = [
        'a' => 'No es procedente emitir el dictamen para aportarlo como prueba en procesos judiciales o administrativos (aseguradoras). Esta solicitud la debe elevar ante otras entidades según el trámite requerido.',
        'b' => 'No es procedente emitir el dictamen por ser pensionado por Administradora de Fondo de Pensiones (AFP) / Otra entidad de Seguridad Social o de vejez por COLPENSIONES.',
        'c' => 'No es procedente emitir el dictamen por ser pensionado por la Administradora de Riesgos Laborales (ARL) o la(s) enfermedad(es) o deficiencia (s) son de ORIGEN LABORAL, esta solicitud la debe elevar ante la ARL donde se encuentra o la última donde estuvo afiliado.',
        'd' => 'No es procedente emitir el dictamen al no registrar cotizaciones o aportes en la historia laboral de COLPENSIONES.',
        'e' => 'No es procedente emitir el dictamen por encontrarse trasladado al Régimen de Ahorro Individual con Solidaridad (RAIS). Esta solicitud la debe elevar ante otras entidades según trámite requerido.',
        'f' => 'No procede la calificación de invalidez como beneficiario, al no acreditar el parentesco en calidad de Hijo o hermano, con el causante o afiliado de acuerdo a lo establecido en el artículo 13 ley 797 de 2003.
                En caso de requerir calificación en condición de hijo de crianza deberá demostrar tal calidad mediante sentencia judicial emitida por el Juez competente.',
        'g' => 'No es procedente emitir el dictamen teniendo en cuenta que la solicitud no corresponde a una prestación económica del Sistema General de Pensiones, razón por la cual y de acuerdo con la pretensión debe elevar este trámite ante la E.P.S. o A.R.L. Razón por la cual en caso de requerir los certificados de discapacidad para los trámites administrativos que demanden dicho documento, debe regirse por la Resolución 1239 de 2022.',
        'h' => 'No es procedente la Revisión del estado de invalidez ya que cuenta con dictamen menor de tres años, emitido por COLPENSIONES, Junta Regional o Nacional de Calificación de Invalidez con pérdida de capacidad laboral / ocupacional (PCL / PCLO) mayor o igual al 50%.',
        'i' => 'No es procedente adelantar nuevo proceso de calificación en primera oportunidad, toda vez que cuenta con un dictamen de pérdida de capacidad laboral/ocupacional (PCL/PCLO) mayor o igual al 50% emitido por COLPENSIONES, E.P.S., Junta Regional o Nacional de calificación de invalidez.  Lo anterior teniendo en cuenta lo dispuesto en la Circular interna 01 de 2019 y concepto emitido por la Oficina Asesora de Asuntos Legales radicado 2021_8929369. Para los dictámenes emitidos por la E.P.S,
                tendrán validez los debidamente comunicados o notificados a COLPENSIONES desde el 05 de agosto de 2021, por la E.P.S.  que lo emite y que cumplan con los requisitos descritos en el 2463 de 2001 artículo 5 y 6.',
        'j' => 'No procede emitir el dictamen de pérdida de capacidad laboral cuando la persona a calificar cuenta con un dictamen menor a un año, emitido por COLPENSIONES, E.P.S., Junta Regional o Nacional de Calificación de Invalidez con Pérdida de Capacidad Laboral / Ocupacional (PCL / PCLO) menor del 50%.  La calificación procederá a excepción que existan patologías adicionales no contempladas en la calificación anterior en firme o ejecutoriado, en cuyo caso se debe realizar una nueva calificación con la documentación actualizada que fundamente el nuevo estado de salud, iniciando el proceso como lo establece la primera oportunidad;
                la cual podrá realizarse antes de los doce (12) meses, siempre y cuando haya agotado la Mejoría Médica Máxima a partir de la fecha del dictamen que quedó ejecutoriado. En relación a los dictámenes de la E.P.S. se debe aplicar el concepto emitido por la Oficina Asesora de Asuntos Legales radicado 2021_8929369.',
        'k' => 'No es procedente emitir el dictamen, por no haber agotado la Mejoría Médica Máxima en las patologías relacionadas en la historia clínica que aporta el afiliado para el trámite de calificación de pérdida de capacidad laboral. Esta causal no aplica si la persona cuenta con un concepto de rehabilitación desfavorable emitido por la E.P.S.',
        'l' => 'No es procedente emitir el dictamen, cuando el origen y/o el grado de pérdida de la capacidad laboral / ocupacional y/o fecha de estructuración, se encuentran en controversia / recurso de reposición / apelación ante la Junta Regional / Nacional de Calificación de Invalidez.',
        'm' => 'No es procedente emitir el dictamen, por ser víctima de la violencia, solicitud que se debe elevar ante la Junta de Calificación de Invalidez correspondiente.',
        'n' => 'No es revisable por su edad y/o por presentar una condición de salud no recuperable. Mantiene su condición de invalidez.',
        'o' => 'No es procedente continuar con el trámite de calificación dado que no se puede aplicar el principio de integralidad de acuerdo al Artículo 2 del anexo técnico del decreto 1507 de 2014 no aportó la historia
                    clínica suficiente y/o actualizada y/o las pruebas clínicas o paraclínicas solicitados en los tiempos establecidos al momento de la solicitud. Una vez cuente con ellas debe iniciar nuevo trámite ante el PAC.',
        'p' => 'No procede realizar la revisión del estado de invalidez, teniendo en cuenta que no se encuentra en nómina como pensionado de invalidez por COLPENSIONES.',
        'q' => 'No procede realizar la Revisión del estado de invalidez, como es de su conocimiento actualmente lleva un trámite de investigación por parte de la Gerencia de Prevención del Fraude, de conformidad con lo establecido en el Memorando No. 081 de 2019.',
        'r' => 'No procede la calificación porque de acuerdo a la información aportada en la historia clínica, las deficiencias son consecuencia de un evento de origen laboral, por lo tanto, su ARL es la competente para atender la solicitud.',
        's' => 'No es procedente emitir el dictamen de pérdida de capacidad laboral, teniendo en cuenta que la persona a calificar tiene con concepto de rehabilitación favorable emitido por la E.P.S. y a la fecha no ha cumplido 540 días de incapacidad temporal prolongada.',
        't' => 'No es procedente emitir el dictamen de pérdida de capacidad laboral ya que el usuario a calificar se encuentra fallecido.',
        'u' => 'No es procedente emitir el dictamen de pérdida de capacidad laboral ya que el usuario no asistió a cita presencial y/o no cuenta con valoración del título II, o el contacto para valoración telefónica no fue exitoso.',
        'v' => 'No es procedente continuar con el proceso de Calificación, lo anterior teniendo en cuenta que existe un trámite en curso para la misma solicitud y este se encuentra en ejecución.',
    ];
    public static $REJECTION_CAUSALS_OLD_PCL = [
        1 => 'No es procedente emitir el dictamen para aportarlo como prueba en procesos judiciales o administrativos (aseguradoras). Esta solicitud la debe elevar ante otras entidades según trámite requerido.',
        2 => 'No es procedente emitir el dictamen por haber recibido indemnización sustitutiva por vejez o invalidez, al quedar por fuera del Sistema General del Pensiones en concordancia con el artículo  6 de decreto 1730 de 2001.',
        3 => 'No es procedente emitir el dictamen por ser pensionado por Administradora de Fondo de Pensiones (AFP) / Otra entidad de Seguridad Social o de vejez por COLPENSIONES.',
        4 => 'No es procedente emitir el dictamen por ser pensionado por la Administradora de Riesgos Laborales (ARL) o la(s) enfermedad(es) o deficiencia (s) son de ORIGEN LABORAL, esta solicitud la debe elevar ante la ARL donde se encuentra o la última donde estuvo afiliado.',
        5 => 'No es procedente emitir el dictamen al no registrar cotizaciones o aportes en la historia laboral de Colpensiones.',
        6 => 'No es procedente emitir el dictamen por encontrarse trasladado al Régimen de Ahorro Individual con Solidaridad (RAIS). Esta solicitud la debe elevar ante otras entidades según trámite requerido.',
        7 => 'No procede la calificación de invalidez, al no acreditar la calidad de hijo o hermano beneficiario del causante o afiliado como lo establece los artículos 9 y 13 ley 797 de 2003.',
        8 => 'Cuenta con dictamen emitido por Colpensiones, EPS,  Junta Regional o Nacional de Calificación de Invalidez con pérdida de capacidad laboral / ocupacional (PCL / PCO) mayor o igual al 50%.',
        9 => 'Cuenta con dictamen menor de un año, emitido por Colpensiones, EPS, Junta Regional o Nacional de Calificación de Invalidez con pérdida de capacidad laboral / ocupacional (PCL / PCO) menor del 50%.',
        10 => 'No es procedente emitir el dictamen, por no haber agotado la Mejoría Médica Máxima de las nuevas enfermedades u otras relacionadas se encuentran en estudio en primera oportunidad sobre el ORIGEN.',
        11 => 'El origen y/o el grado de pérdida de la capacidad laboral / ocupacional y/o fecha de estructuración, se encuentra (n) en controversia / recurso de reposición / apelación ante la Junta Regional / Nacional de Calificación de Invalidez.',
        1001 => 'No es procedente emitir el dictamen, por ser víctima de la violencia, solicitud que se debe elevar ante la Junta de Calificación de Invalidez correspondiente.',
        1002 => 'No es revisable por su edad y/o por presentar una condición de salud no recuperable. Mantiene su condición de invalidez.',
        1003 => 'No aporto la historia clínica suficiente, y/o actualizada y/o las pruebas clínicas o paraclínicas solicitados en los tiempos establecidos al momento de la solicitud. Una vez cuente con ellas debe iniciar nuevo trámite ante el PAC.',
        1004 => 'No es pensionado por Colpensiones.',
        1005 => 'Existe un trámite en curso para la misma solicitud o este se encuentra en ejecución.',
        1006 => 'No cuenta con concepto de rehabilitación desfavorable o no favorable, por lo tanto no es procedente emitir dictamen conforme a lo establecido en el artículo 2.2.3.3.2 del Decreto 1333 de 2018.',
        1007 => 'No es procedente emitir el dictámen para el caso de la calificación integral, las deficiencias de ORIGEN PROFESIONAL / LABORAL tienen mayor valor porcentual que las de ORIGEN COMUN. Solicitud la debe elevar ante la ARL donde se encuentra o la última donde estuvo afiliado.',
        1008 => 'La solicitud no corresponde a una prestación económica del Sistema General de Pensiones, razón por la cual y de acuerdo con la pretensión debe elevar este trámite ante la E.P.S. o A.R.L. según sea el caso.',
        1009 => 'En concordancia con la sentencia C-425 de 2005, materialmente no es invalido. Es así que no se deben incluir deficiencias de Origen Laboral.',
        1010 => 'No  es procedente emitir el dictamen de pérdida de capacidad laboral, toda vez, que usted cuenta con concepto de rehabilitación favorable emitido por su E.P.S. y a la fecha no ha cumplido 540 días de incapacidad temporal prolongada , por lo tanto, cuando este próximo a cumplir el día 540 debe acercarse para radicar nuevamente la solicitud.',
        2001 => 'No es procedente emitir el dictamen, por no haber agotado la Mejoría Médica Máxima en las patologías relacionadas en la historia clínica que aporta el afiliado para el trámite de calificación de pérdida de capacidad laboral.',
        2002 => 'No procede  revisión del estado de invalidez  por contar  con dictamen menor de tres años, emitido por Colpensiones, Junta Regional o Nacional de Calificación de Invalidez con pérdida de capacidad laboral / ocupacional (PCL / PCLO) mayor o igual al 50%.',
        2003 => 'No procede la Revisión del Estado de invalidez por tener una Investigación Administrativa Especial en curso',
    ];
    public static $SPECIALTIES = [
        1 => "Alergología",
        2 => "Anestesiología",
        3 => "Audiología",
        4 => "Cardiología",
        5 => "Cirugía de Tórax",
        6 => "Cirugía General",
        7 => "Cirugía Maxilo Facial",
        8 => "Cirugía Reconstructiva",
        9 => "Clínica del Dolor",
        10 => "Cuidados Paliativos",
        11 => "Dermatología",
        12 => "Endocrinología",
        13 => "Fisiatría y rehabilitación",
        14 => "Gastroenterología",
        15 => "Geriatría",
        16 => "Ginecología",
        17 => "Hematología",
        18 => "Hemodinamia",
        19 => "Infectología",
        20 => "Inmunología",
        21 => "Medicina de Urgencias",
        22 => "Medicina General",
        23 => "Medicina Laboral",
        24 => "Medicina Legal",
        25 => "Medicina Preventiva",
        26 => "Medicina Interna",
        27 => "Nefrología",
        28 => "Neumología",
        29 => "Neurocirugía",
        30 => "Neurología",
        31 => "Nutrición",
        32 => "Odontología",
        33 => "Oftalmología",
        34 => "Optometría",
        35 => "Ortopedia y Traumatología",
        36 => "Otorrinolaringología",
        37 => "Patología",
        38 => "Proctología",
        39 => "Psicología",
        40 => "Psiquiatría",
        41 => "Radiología",
        42 => "Reumatología",
        43 => "Salud Ocupacional",
        44 => "Terapia Física",
        45 => "Terapia Ocupacional",
        46 => "Terapia Respiratoria",
        47 => "Urología",
        48 => "Vascular periférico"
    ];

    //Actividades economicas de alto riesgo
    public static $highRiskEconomicActivities = [
        '0',
    ];
    //Actividades economicas sin planilla
    public static $ECONOMIC_ACTIVITES_WHITHOUT_SPREADSHEET = [
        "0111",
        "0112",
        "0113",
        "0114",
        "0115",
        "0116",
        "0119",
        "0121",
        "0122",
        "0123",
        "0124",
        "0125",
        "0126",
        "0127",
        "0128",
        "0129",
        "4100"
    ];
    
    //Emails notificaciones mnk de gis
    public static $EMAILS_NOTIFICATIONS_MNK = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
    ];

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        Carbon::setLocale('es_CO.utf8', 'es-CO.utf8');
        setlocale(LC_TIME, 'es_CO.utf8', 'es-CO.utf8');

        Blade::directive('money', function ($amount) {
            return "<?php echo '$' . number_format($amount, 0); ?>";
        });
        // Fix migrate String bug
        Schema::defaultStringLength(191);

        // redirects to https
        if (App::environment() == 'production') {
            \URL::forceScheme('https');
        }

        // Observers
        Affiliate::observe(LoggedModelObserver::class);
        Employment::observe(LoggedModelObserver::class);
        Employer::observe(LoggedModelObserver::class);
        Branch::observe(LoggedModelObserver::class);

        // Carga 441 ocupaciones existentes, limite maximo de 500 por seguridad
        $occupationCategories = OccupationCategory::limit(500)->get()->toArray();



        // Shared Static Data
        View::share('OCCUPATION_CATEGORIES', $occupationCategories);
        View::share('PERIODICITYT', self::$PERIODICITYT);
        View::share('REGIONAL', self::$REGIONAL);
        View::share('DOMINANCE', self::$DOMINANCE);
        View::share('CONCAUSAS', self::$CONCAUSAS);
        View::share('MONEY_TYPE', self::$MONEY_TYPE);
        View::share('WORK_MODALITY', self::$WORK_MODALITY);
        View::share('OPTION_ASEGUREMENT', self::$OPTION_ASEGUREMENT);
        View::share('INSTITUTIONAL_SECTOR', self::$INSTITUTIONAL_SECTOR);
        View::share('VARIATIONS_TYPE', self::$VARIATIONS_TYPE);
        View::share('CALENDAR_PERIOD', self::$CALENDAR_PERIOD);
        View::share('TYPE_IT', self::$TYPE_IT);
        View::share('TYPE_ATTENTION', self::$TYPE_ATTENTION);
        View::share('TYPE_INCOME', self::$TYPE_INCOME);
        View::share('GLOSA_REASONS_STD', self::$GLOSA_REASONS_STD);
        View::share('SENDERS', self::$SENDERS);
        View::share('ORIGINS', self::$ORIGINS);
        View::share('ORIGINS_AT', self::$ORIGINS_AT);
        View::share('ORIGINS_PCL', self::$ORIGINS_PCL);
        View::share('LATERALITY', self::$LATERALITY);
        View::share('DIAGNOSTIC_STATUS', self::$DIAGNOSTIC_STATUS);
        View::share('ROOT_CAUSE', self::$ROOT_CAUSE);
        View::share('TYPE_DISABILITY', self::$TYPE_DISABILITY);
        View::share('RHB_FORMATS', self::$RHB_FORMATS);
        View::share('RHB_TERMS', self::$RHB_TREATMENTS);
        View::share('REJECTION_CAUSALS_PCL', self::$REJECTION_CAUSALS_PCL);
        View::share('REJECTION_CAUSALS_OLD_PCL', self::$REJECTION_CAUSALS_OLD_PCL);
        View::share('TYPE_OF_PAYMENT', self::$TYPE_OF_PAYMENT);
        View::share('VALIDATION_RESULT', self::$VALIDATION_RESULT);
        View::share('BENEFICIARY', self::$BENEFICIARY);
        View::share('ESTADO_PAGO', self::$ESTADO_PAGO);
        View::share('TYPE_CONSTANCY', self::$TYPE_CONSTANCY);
        View::share('LATERALIDAD', self::$LATERALIDAD);
        View::share('CLASIFICACION', self::$CLASIFICACION);
        View::share('CARE_LEVEL', self::$CARE_LEVEL);
        View::share('CARE_MODALITY', self::$CARE_MODALITY);
        View::share('ENABLED_STATES_COBRO', self::$ENABLED_STATES_COBRO);
        View::share('RHB_TREATMENTS', [
            "R" => "RECIBIDO",
            "N" => "NO RECIBIDO",
            "P" => "NO APLICA",
            "PO" => "POSIBLE",
        ]);
        View::share('RHB_PURPOSES', [
            "C" => "CURATIVA",
            "P" => "PALIATIVA",
        ]);
        View::share('RHB_DURATIONS', [
            "0" => "MENOS DE 1 AÑO",
            "1" => "MAS DE 1 AÑO",
            "I" => "INDEFINIDO",
        ]);
        View::share('RHB_PERFOMANCES', [
            "1" => "FUNCIONAL",
            "2" => "SEMIFUNCIONAL",
            "3" => "DEPENDIENTE",
            "4" => "SEMIDEPENDIENTE",
            "5" => "INDEPENDIENTE",
            "6" => "SEMI-INDEPENDIENTE",
        ]);
        View::share('RHB_INSTRUMENTS', [
            "1" => "TRASLADOS Y DESPLAZAMIENTOS",
            "2" => "INSTRUMENTAL",
            "3" => "AVANZADO",
            "99" => "NO APLICA",
        ]);
        View::share('RHB_ROLE', [
            "1" => "ACTIVO",
            "2" => "INTERRUMPIDO",
            "3" => "CESANTE",
        ]);

        View::share('RECOMMENDATIONS_FORMATS', self::$RECOMMENDATIONS_FORMATS);
        View::share('RECOMMENDATIONS_TERMS', self::$RECOMMENDATIONS_TERMS);
        View::share('CATEGORIZATION_INCIDENT', self::$CATEGORIZATION_INCIDENT);
        View::share('SEVERITY', self::$SEVERITY);
        View::share('ACTUARIAL_SEVERITY', self::$ACTUARIAL_SEVERITY);
        View::share('OPTIONS', self::$OPTIONS);
        View::share('TREATMENT_PURPOSE', self::$TREATMENT_PURPOSE);
        View::share('PATIENT_PROGNOSIS', self::$PATIENT_PROGNOSIS);
        View::share('HIGH_COST', self::$HIGH_COST);
        View::share('REINCORPORATION_CLASS', self::$REINCORPORATION_CLASS);
        View::share('ACCIDENT_TYPES', self::$ACCIDENT_TYPES);
        View::share('AGENTS', self::$AGENTS);
        View::share('AGENTS_INFO', self::$AGENTS_INFO);
        View::share('INJURIES', self::$INJURIES);
        View::share('WORK_MODES', self::$WORK_MODES);
        View::share('WORKPLACE_LOCATIONS', self::$WORKPLACE_LOCATIONS);

        View::share('DCRT_GRADES', self::$DCRT_GRADES);
        View::share('DCRT_TYPES', self::$DCRT_TYPES);

        View::share('IT_ORIGINS', self::$IT_ORIGINS);

        View::share('IT_DISABILITY_TYPES', self::$IT_DISABILITY_TYPES);

        View::share('QUALIFIERS', self::$QUALIFIERS);
        View::share('CONTROVERSY_AUTHORS', [
            "A" => "ARL",
            "F" => "AFP",
            "C" => "EMPLEADOR",
            "U" => "AFILIADO",
            "E" => "EPS",
        ]);
        View::share('DOCUMENTS', SendDocument::$DOCUMENTS);
        View::share('USER_TYPES', [
            'ADM' => 'ADMINISTRADOR',
            'FNA' => 'FUNCIONARIO AVANZADO',
            'FNB' => 'FUNCIONARIO BASICO',
            'FNL' => 'FUNCIONARIO LIMITADO',
            'CON' => 'CONSULTA',
        ]);
        View::share('ADDRESS_KEYS', [
            "Avenida" => "Avenida",
            "Calle" => "Calle",
            "Carrera" => "Carrera",
            "Diagonal" => "Diagonal",
            "Transversal" => "Transversal",
        ]);
        View::share('INFORMATION_SOURCES', [
            "28" => "FUENTE DE SUSCEPTIBILIDAD",
            "29" => "REQUERIMIENTO DEL USUARIO",
        ]);
        View::share('INFORMATION_SOURCES_PCL', self::$INFORMATION_SOURCES_PCL);
        View::share('INFORMATION_SOURCES_RECOMMENDATIONS', [
            "25" => "SOLICITUD DIRECTA AFILIADO",
            "99" => "SOLICITUD EMPLEADOR",
            "22" => "PQR o FAMIGOS",
            "23" => "TUTELAS",
            "98" => "MESA LABORAL",
        ]);
        View::share('INFORMATION_SOURCES_RHB', [
            "24" => "AUDITORIA DE INCAPACIDADES",
            "21" => "MASIVO 135",
            "20" => "MASIVO 90",
            "22" => "PQR o FAMIGOS",
            "23" => "TUTELAS",
        ]);
        View::share('INFORMATION_SOURCES_MAPFRE', [
            "31" => "DIGITAL",
            "32" => "FISICA",
        ]);

        View::share('GENDERS', [
            "" => " ",
            "M" => "MASCULINO",
            "F" => "FEMENINO",
            "NB" => "NO BINARIO",
        ]);
        View::share('CIVIL_STATUS', self::$CIVIL_STATUS);
        View::share('TIPO_JORNADA_LABORAL', self::$TIPO_JORNADA_LABORAL);
        View::share('REQUEST_REASON', self::$REQUEST_REASON);
        View::share('SPECIALTY', self::$SPECIALTY);
        View::share('SCHOOL_LEVELS', self::$SCHOOL_LEVELS);
        View::share('SCHOOLING', self::$SCHOOLING);
        View::share('AFFILIATION_TYPES', [
            1 => "COTIZANTE",
            2 => "BENEFICIARIO",
        ]);
        View::share('DOC_TYPES', [
            "CF" => "Cédula física",
            "CJ" => "Cédula jurídica",
            "CD" => "Carnet diplomático",
            "DI" => "Dimex",
            "PA" => "Pasaporte",
            "CR" => "Cédula de residencia"
        ]);

        View::share('STATES_REINTEGRATE', [
            "approve" => "Aprobar",
            "reject" => "Rechazar",
            "request_info" => "Solicitar Información"
        ]);

        View::share('PERIODICITY_RECEIPTS', [
            "1" => "annual_payment_receipt",
            "2" => "biannual_payment_receipt",
            "3" => "quarterly_payment_receipt",
            "4" => "monthly_payment_receipt",
            "" => "one_time_payment_receipt",

            'emission' => 'issuance_receipt',
            'period_increase' => 'increase_receipt',
            'monthly_payment' => 'monthly_payment_receipt',
            'quarterly_payment' => 'quarterly_payment_receipt',
            'semiannual_payment' => 'biannual_payment_receipt',
            'rehabilitation' => 'rehabilitation_receipt',
            'liquidation' => 'settlement_receipt',
            'renewal' => 'renewal_receipt',
        ]);

        View::share('STATES_RESULT_REINTEGRATE', [
            "empty" => "",
            "pending_info" => "Pendiente de información",
            "all_approved" => "Aprobado",
            "all_rejected" => "Rechazado",
            "partial_approved" => "Parcialmente aprobado",
        ]);

        View::share('CONSULTATION_CHANNELS', [
            "MT" => "Meet",
            "WAP" => "Whatsapp",
            "LT" => "Llamada telefónica",
            "P" => "Presencial"
        ]);
        View::share('ORIGIN_DIAGNOSIS', [
            "LAB" => "Laboral",
            "NOLAB" => "No laboral",
            "COM" => "Común",
            "NAT" => "No derivado del accidente de trabajo",
            "MIX" => "Mixto",
            "EST" => "En estudio",
            "INC" => "Incidente",
            "NDE" => "No derivado del evento",
            "DAT" => "Derivado del accidente de trabajo",
            "DE" => "Derivado del evento",
            "PARLAB" => "Laboral parcial"
        ]);
        View::share('ORIGIN_DIAGNOSIS_UPDATED', [
            "LAB" => "Laboral",
            "NOLAB" => "No laboral",
            "EST" => "En estudio",
            "PARLAB" => "Laboral parcial"
        ]);
        View::share('TYPE_CASE', [
            "LAB" => "Laboral",
            "NOLAB" => "No laboral",
            "EST" => "En estudio",
            "INC" => "Incidente"
        ]);
        View::share('TYPE_DIAGNOSIS', [
            "PRI" => "Primario",
            "SEC" => "Secundario",
        ]);
        View::share('DISABILITY_TYPE', [
            "INI" => "Inicial",
            "PRO" => "Prórroga"
        ]);
        View::share('ATTENTION_MODE', [
            "URG" => "Urgencia",
            "AMB" => "Ambulatoria",
            "HOS" => "Hospitalaria"
        ]);
        View::share('CLIENT_TYPES', [
            1 => 'EPS',
            2 => 'AFP',
            3 => 'ARL',
            4 => 'OTRO',
        ]);
        View::share('TYPE_CURRENCY', self::$TYPE_CURRENCY);
        View::share('S3_DOCUMENTS_MNK', self::$S3_DOCUMENTS_MNK);
        View::share('BOARDS_DATA', JR::getNames());
        View::share('BOARDS', JR::getNames());
        View::share('EPS_LIST', EPS::getNames());
        View::share('AFP_LIST', AFP::getNames());
        View::share('ARL_LIST', ARL::getNames());
        View::share('EPS_DATA', EPS::getData());
        View::share('EPS_DATA_ATTRIBUTE', EPS::getDataAttribute());
        View::share('AFP_DATA_ATTRIBUTE', AFP::getDataAttribute());
        View::share('ARL_DATA_ATTRIBUTE', ARL::getDataAttribute());
        View::share('AFP_DATA', AFP::getData());
        View::share('ARL_DATA', ARL::getData());
        View::share('SEGURO_DATA', SEGURO::getData());

        View::share('RISK_FACTOR_ORIGIN_CALIFICATION', self::$RISK_FACTOR_ORIGIN_CALIFICATION);
        View::share('CATEGORICAL_INFO_PQR', self::$CATEGORICAL_INFO_PQR);

        View::share('INABILITY_ORIGIN', self::$INABILITY_ORIGIN);
        View::share('REHABILITATION_DETAILS', self::$REHABILITATION_DETAILS);
        View::share('PENDING_CLOSURE_CAUSE', self::$PENDING_CLOSURE_CAUSE);
        View::share('EVENT_TYPE', self::$EVENT_TYPE);
        View::share('FUNCTIONAL_PROGNOSIS', self::$FUNCTIONAL_PROGNOSIS);
        View::share('INABILITY_TYPE', self::$INABILITY_TYPE);

        View::share('QUALIFICATION_REASON_AUDIT', self::$QUALIFICATION_REASON_AUDIT);
        View::share('IT_RESULT_INABILITY', self::$IT_RESULT_INABILITY);
        View::share('IT_REASON_INABILITY', self::$IT_REASON_INABILITY);

        View::share('RADICATION_TYPE', self::$RADICATION_TYPE);
        View::share('MDI_MISSING_DOCUMENTS', self::$MDI_MISSING_DOCUMENTS);
        View::share('GENERAL_INFORMATION_FIELDS', self::$GENERAL_INFORMATION_FIELDS);
        View::share('TRAMITE_INFORMATION_FIELDS', self::$TRAMITE_INFORMATION_FIELDS);
        View::share('PAYMENT_INFORMATION_FIELDS', self::$PAYMENT_INFORMATION_FIELDS);
        View::share('DETAIL_PERIODS_FIELDS', self::$DETAIL_PERIOS_FIELDS);
        View::share('TYPE_IPS', self::$TYPE_IPS);
        View::share('ROOM_NAME_TUTELAGE_IT', self::$ROOM_NAME_TUTELAGE_IT);
        View::share('SECTION_NAME_TUTELAGE_IT', self::$SECTION_NAME_TUTELAGE_IT);
        View::share('OCCUPATIONS', self::$OCCUPATIONS);
        View::share('CONDICION', self::$CONDICION);
        View::share('CONDICION_SPECIAL', self::$CONDICION_SPECIAL);
        View::share('JURISDICTION_TUTELAGE_IT', self::$JURISDICTION_TUTELAGE_IT);
        View::share('SPECIALTY_TUTELAGE_IT', self::$SPECIALTY_TUTELAGE_IT);
        View::share('COURT_NUMBER_TUTELAGE_IT', self::$COURT_NUMBER_TUTELAGE_IT);
        View::share('SPECIALITY', self::$SPECIALITY);
        View::share('REJECTION_CAUSALS_TEMP', self::$REJECTION_CAUSALS_TEMP);
        View::share('MEETING_ATING_OF', self::$MEETING_ATING_OF);
        View::share('MEETING_TYPE_EVENT', self::$MEETING_TYPE_EVENT);
        View::share('MEETING_INTERESTED_PARTY_THAT_DISPUTES', self::$MEETING_INTERESTED_PARTY_THAT_DISPUTES);
        View::share('IT_DOCUMENTS', self::$IT_DOCUMENTS);
        View::share('MEETING_ATTACHED_DOCUMENTS', self::$MEETING_ATTACHED_DOCUMENTS);
        View::share('MEETING_AVERAGE_COSTS', self::$MEETING_AVERAGE_COSTS);
        View::share('MEETING_APPEAL_STATUS', self::$MEETING_APPEAL_STATUS);
        View::share('MEETING_JRCI_AVERAGE_COST', self::$MEETING_JRCI_AVERAGE_COST);
        View::share('MEETING_PRONOUNCEMENT', self::$MEETING_PRONOUNCEMENT);
        View::share('MEETING_JRCI_DICTUM_AVERAGE_COST', self::$MEETING_JRCI_DICTUM_AVERAGE_COST);
        View::share('COLPENSIONES_GPA_ORIGINS', self::$COLPENSIONES_GPA_ORIGINS);
        View::share('ENTITY_NAME_PERITAJE', self::$ENTITY_NAME_PERITAJE);
        View::share('CATEGORIES_AND_CAUSAL', self::$CATEGORIES_AND_CAUSAL);
        View::share('SMMLV', self::$SMMLV);
        View::share('REJECTION_CAUSALS', self::$REJECTION_CAUSALS);
        View::share('REJECTION_CAUSALS_PB', self::$REJECTION_CAUSALS_PB);

        View::share('BANKS', self::$BANKS);
        View::share('MNK_SERVICES', self::$MNK_SERVICES);
        View::share('IT_DOCUMENT_TYPE_SOL_DOCS', self::$IT_DOCUMENT_TYPE_SOL_DOCS);
        View::share('RECEIPT_STATE', self::$RECEIPT_STATE);
        View::share('QUOTATIONS_STATE', self::$QUOTATIONS_STATE);
        View::share('OCCUPATION_GROUPS', self::$OCCUPATION_GROUPS);
        View::share('TYPE_RECEIPT', self::$TYPE_RECEIPT);
        View::share('SPECIALTIES', self::$SPECIALTIES);
        View::share('TYPE_PERMANENT_DISABILITY', self::$TYPE_PERMANENT_DISABILITY);
        View::share('TYPE_BENEFICIARY', self::$TYPE_BENEFICIARY);
        View::share('HIGH_RISK_ECONOMIC_ACTIVITIES', self::$highRiskEconomicActivities);

        // Registrar un view composer para que se ejecute al renderizar la vista (cuando ya esté el usuario autenticado)
        View::composer('services.policy_sort.holder_policy.menu.*', function ($view) {
            $menuVariables = $this->loadMenuData();
            $view->with($menuVariables);
        });
    }

    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        //
    }

    /**
     * Carga los datos del menú solo si la ruta actual requiere mostrar el menú.
     *
     * @return array
     */
    protected function loadMenuData()
    {

        // // Verifica que exista la ruta actual.
        $currentRoute = request()->route();
        if (!$currentRoute) {
            return [
                'menuData' => [],
                'affiliate' => null,
                'policySortData' => [],
                'tomadorAutorizado' => false,
                'active' => null,
                'policyId' => null,
            ];
        }

        // Verifica si la URL actual empieza por "tomador/poliza"
        if (!request()->is('tomador/poliza*')) {
            // Si no está en esa URL, no se ejecuta la lógica del menú
            return [
                'menuData' => [],
                'affiliate' => null,
                'policySortData' => [],
                'tomadorAutorizado' => false,
                'active' => null,
                'policyId' => null,
            ];
        }

        // Capturar el número que sigue en la URL.
        // Se asume que la URL es: tomador/poliza/{affiliateId}/...
        $affiliateId = request()->segment(3); // segment(1) = tomador, segment(2) = poliza, segment(3) = número

        if (!is_numeric($affiliateId)) {
            $affiliateId = null;
        }

        $affiliate_tomador = Affiliate::where('id', $affiliateId)
            ->firstOrFail();


        $authorizedActivityIds = [];
        if (Auth::user()->view_tomador_autorizado()) {
            $authorizedActivityIds = UserAuthorizedPolicies::where('user_id', Auth::user()->id)
                ->pluck('activity_id')     // solo el campo activity_id
                ->toArray();
        }

        $policySorts = PolicySort::join('activities', 'activities.id', '=', 'policy_sorts.activity_id')
            ->join('affiliates', 'affiliates.id', '=', 'activities.affiliate_id')
            ->where('activities.service_id', 75)
            ->whereNotNull('policy_sorts.consecutive')
            ->whereIn('activities.state_id', [20, 21])

            // Si no es admin ni ejecutivo, filtro por su affiliate_id
            ->when(
                !Auth::user()->isAdmin() && !Auth::user()->isExecutiveMNK(),
                function ($query) use ($affiliateId) {
                    return $query->where('activities.affiliate_id', $affiliateId);
                }
            )

            // Si es tomador_autorizado, filtro por las activities autorizadas
            ->when(
                Auth::user()->view_tomador_autorizado(),
                function ($query) use ($authorizedActivityIds) {
                    return $query->whereIn('activities.id', $authorizedActivityIds);
                }
            )

            ->orderBy('policy_sorts.consecutive', 'asc')
            ->select([
                'policy_sorts.id',
                'policy_sorts.consecutive',
                'policy_sorts.activity_id',
                'policy_sorts.first_name',
                'policy_sorts.validity_from',
                'policy_sorts.validity_to',
                'policy_sorts.type_currency',
                'policy_sorts.benefit_colective',
                'affiliates.full_name',
                'affiliates.first_name as affiliate_first_name',
                'affiliates.email',
                'activities.affiliate_id',
                'activities.state_id',
                'policy_sorts.work_modality_id',
            ])
            ->get();

        $policySortData = $policySorts->map(function ($policySort) {
            return [
                'id' => $policySort->id,
                'consecutive' => $policySort->consecutive,
                'activity_id' => $policySort->activity_id,
                'activityPolicy' => $policySort,
                'state_id' => $policySort->state_id,
                'first_name' => $policySort->first_name,
                'validity_from' => $policySort->validity_from,
                'validity_to' => $policySort->validity_to,
                'type_currency' => $policySort->type_currency,
                'affiliate_id' => $policySort->affiliate_id,
                'email' => $policySort->email,
                'full_name' => $policySort->full_name,
                'benefit_colective' => $policySort->benefit_colective,
                'work_modality_id' => $policySort->work_modality_id,
            ];
        })->toArray();


        $tomadorAutorizado = false;

        $active = request()->path();


        $tomadorAutorizado = null;



        if (Auth::check() && Auth::user()->view_tomador_autorizado()) {

            $tomadorId = User::where('affiliate_id', $affiliate_tomador->id)->where('area_id', Area::TOMADOR)->first();

            $tomadorAutorizado = User::with([
                // Filtramos aquí las userPermission por tomador_id
                'userPermission' => function ($q) use ($tomadorId) {
                    $q->where('tomador_id', $tomadorId->id);
                },
                // Y luego cargamos los permissions de cada userPermission
                'userPermission.permissions'
            ])
                ->find(Auth::id());
        }

        return compact('policySortData', 'tomadorAutorizado', 'affiliate_tomador');
    }
}
