<?php

namespace App;


use App\Actions\ActionMedicalServiceSort;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class MedicalServicesSort extends Model
{
    protected $table = 'medical_services_sort';

    public function activity()
    {
        return $this->belongsTo('App\Activity');
    }


    protected $fillable = [
        'name_patient',
        'activity',
        'date_dictamen',
        'name_patron',
        'id_patron',
        'num_policy_sort',
        'labor_role_description',
        'supplier_code',
        'reps_code',
        'ips_identification',
        'issuing_ips',
        'provider_type',
        'province',
        'canton',
        'district',
        'service_group',
        'assigned_service',
        'service_mode',
        'medical_record',
        'specialty',
        'entity_promote',
        'valuation_date',
        'valuation_time',
        'consultation_channel',
        'consultation_reason',
        'reason_cancel',
        'records',
        'size',
        'weight',
        'ta',
        'ta2',
        'imc',
        'fc',
        'fr',
        'physical_examination_description',
        'plan_description',
        'closed_case',
        'disability_type',
        'attention_mode',
        'start_date_of_incapacity',
        'days_of_incapacity',
        'origin_case',
        'dx_principal',
        'dx_principal_description',
        'dx_related',
        'dx_related_description',
        'observations_explanatory_notes',
        'origin_diagnosis',
        'origin_diagnosis_referral_specialist',
        'document_type_evaluator',
        'identification_number_evaluator',
        'full_name_evaluator',
        'medical_registration_number_evaluator',
        'specialty_evaluator',
        'license_number_evaluator',
        'labor_role_description_observation',
        'approved_service',
        'observation_audit',
        'diagnosis_origin_prescription',
        'diagnosis_origin_controlled_medication',
        'province_controlled_medication',
        'canton_controlled_medication',
        'district_controlled_medication',
        'province_incapacity_or_leave',
        'canton_incapacity_or_leave',
        'district_incapacity_or_leave',
        'province_medical_prescription',
        'canton_medical_prescription',
        'district_medical_prescription',
        'province_order_diagnostic',
        'canton_order_diagnostic',
        'district_order_diagnostic',
        'province_referral_specialist',
        'canton_referral_specialist',
        'district_referral_specialist',
        'province_supplier_details',
        'canton_supplier_details',
        'district_supplier_details',
        'required_hospitalization',
        'date_case',
        'number_case',
        'travel_expenses',
        'transportation',
        'other_signs',
        'identification',
        'requires_follow_up',
        'action_id',
        'disease_report_type_id',
        'gis_disease_confirmation',
        'injury_location_id',
        'injury_nature_id'
    ];

    public function companions()
    {
        return $this->hasMany('App\MedicalServiceCompanion', 'medical_service_sort_id');
    }
    public function diagnostics()
    {
        return $this->hasMany('App\MedicalServiceDiagnostics', 'medical_service_sort_id');
    }
    public function diagnostics_images()
    {
        return $this->hasMany('App\MedicalServiceImageDiagnostics', 'medical_service_sort_id');
    }
    public function specialists()
    {
        return $this->hasMany('App\MedicalServiceReferralSpecialits' ,'medical_service_sort_id');
    }
    public function medical_prescriptions()
    {
        return $this->hasMany('App\MedicalServiceMedicalPrescription' ,'medical_service_sort_id');
    }
    public function controlled_medications()
    {
        return $this->hasMany('App\MedicalServiceControlledMedication' ,'medical_service_sort_id');
    }
    public function followUps()
    {
        return $this->hasMany('App\MedicalServiceFollowUp', 'medical_services_sort_id');
    }

    public function provider()
    {
        return Provider::where('id', $this->primary_care_provider)->first();
    }

    public function get_gis_format_case()
    {
        $gisTable = GisSort::where('activity_id', $this->number_case)->first();
        if (!$gisTable) return '';

        return $gisTable->formatCaseNumberLocal() ?? '';
    }

}
