<?php

namespace App;

use DateTime;
use Illuminate\Database\Eloquent\Model;

class PolicyContact extends Model
{
   
    // Nombre de la tabla
    protected $table = 'policy_contacts';

    // Campos que se pueden rellenar con asignación masiva
    protected $fillable = [
        'policy_sort_id',              
        'name_responsible',          
        'type_identification',         
        'number_identify_responsible',  
        'ocupation_responsible',        
        'phone_responsible',
        'cellphone_responsible',       
        'email_responsible',
        'unique_code',
        'occupations_category_id',
    ];

    // Relación con la tabla policy_sorts 
    public function policySort()
    {
        return $this->belongsTo(PolicySort::class, 'policy_sort_id');
    }
}
