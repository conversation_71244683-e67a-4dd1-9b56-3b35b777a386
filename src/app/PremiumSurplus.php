<?php


namespace App;

use Illuminate\Database\Eloquent\Model;

class PremiumSurplus extends Model
{

    protected $fillable = [
        'id',
        'activity_id',
        'policyholder_id',
        'policyholder_name',
        'policy_number',
        'surplus_amount',
        'receipt_number',
        'currency',
        'credit_balance',
        'issue_date',
        'status_surplus'
    ];

    public function activity()
    {
        return $this->belongsTo('App\Activity');
    }

    public function premium_surpluses_log()
    {
        return $this->hasMany('App\PremiumSurplusesLog');
    }


}
