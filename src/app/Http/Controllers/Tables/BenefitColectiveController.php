<?php

namespace App\Http\Controllers\Tables;

use App\Activity;
use App\Client;
use App\EconomicActivity;
use App\Http\Controllers\Controller;
use App\Http\Controllers\Services\PolicySortController;
use App\Service;
use App\States\StatePoliza;
use Illuminate\Http\Request;
use App\Actions\ActionPolizaSort;
use App\PolicySort;
use App\Http\Controllers\ActionController;
use Illuminate\Support\Facades\DB;
use App\Mail\SendDocumentDataBase;

class BenefitColectiveController extends Controller
{

    //Tablero de colectividad
    public function index(Request $request, $cpath)
    {
        $client = Client::where('path', $cpath)->first();


        $tomadores_name = PolicySort::join('activities', 'policy_sorts.activity_id', '=', 'activities.id')
            ->join('affiliates', 'activities.affiliate_id', '=', 'affiliates.id')
            ->where('activities.service_id', Service::SERVICE_POLICY_SORT_MNK)
            ->whereIn('activities.state_id', [
                StatePoliza::POLIZA_EMITIDA_ACTIVA,
                StatePoliza::POLIZA_EMITIDA_ACTIVA_COLECTIVIDAD
            ])
            ->pluck('affiliates.full_name');

        

        //Todas las actividades de pólizas que tengan el estado 20 emitida activa
        $activities = Activity::join('policy_sorts', 'activities.id', '=', 'policy_sorts.activity_id')
            ->join('affiliates', 'activities.affiliate_id', '=', 'affiliates.id')
            ->join('economic_activities', 'economic_activities.code', '=', 'policy_sorts.activity_economic_id')
            ->where('activities.client_id', $client->id)
            ->where('service_id', Service::SERVICE_POLICY_SORT_MNK)
            ->where('policy_sorts.work_modality_id', 1)
            ->whereIn('state_id', [
                StatePoliza::POLIZA_EMITIDA_ACTIVA,
                StatePoliza::POLIZA_EMITIDA_ACTIVA_COLECTIVIDAD
            ])
            ->select('policy_sorts.id as policy_id',  'policy_sorts.*', 'activities.*', 'affiliates.*', 'economic_activities.*');

        // Aplicar el filtro de fechas con la función
        $startDate = $request->input('start_date');
        $endDate = $request->input('end_date');

        //filtro por fechas
        if ($startDate && $endDate) {
            $activities = $activities->where(function ($query) use ($startDate, $endDate) {
                $query->where('policy_sorts.date_benefit_colective', '>=', $startDate) // La vigencia desde debe ser menor o igual a la fecha final
                    ->where('policy_sorts.validity_to', '<=', $endDate); // La vigencia hasta debe ser mayor o igual a la fecha inicial
            });
        } elseif ($startDate) {
            $activities = $activities->where('policy_sorts.date_benefit_colective', '>=', $startDate);
        } elseif ($endDate) {
            $activities = $activities->where('policy_sorts.validity_to', '<=', $endDate);
        }

        //filtro por el consecutivo de la poliza
        if ($request->id_poliza) {
            $id_poliza = $request->id_poliza;
            $activities = $activities->where('policy_sorts.consecutive', $id_poliza);
        }

        //Nombre del intermediario
        if ($request->intermediary_name) {
            $activities = $activities->where('advisor_name', 'like', '%' . $request->intermediary_name . '%');
        }

        //Nombre del tomador o patrono
        if ($request->tomador_name) {
            // Aseguramos que sea un array
            $names = is_array($request->tomador_name) ? $request->tomador_name : [$request->tomador_name];

            $activities = $activities->where(function ($query) use ($names) {
                foreach ($names as $name) {
                    $query->orWhere('affiliates.first_name', 'like', '%' . $name . '%')
                        ->orWhere('affiliates.last_name', 'like', '%' . $name . '%');
                }
            });
        }


        $economicActivitiesRaw = $request->input('activity_economic', []); // Filtra valores vacíos o null

        // Verifica si realmente es un array y tiene al menos un valor
        if (!empty($economicActivitiesRaw) && is_array($economicActivitiesRaw)) {
            // Extrae el primer valor del array (la cadena "0112,0119")
            $economicActivities = explode(',', $economicActivitiesRaw[0]); 

            // Filtra valores vacíos por si acaso
            $economicActivities = array_filter($economicActivities);
            
            // Si los valores deben ser enteros, conviértelos:
            // $economicActivities = array_map('intval', $economicActivities);

            if (!empty($economicActivities)) {
                $activities = $activities->whereIn('policy_sorts.activity_economic_id', $economicActivities);
            }
        }

        // Paginación y orden de las actividades
        $activities = $activities->orderBy('policy_sorts.consecutive', 'desc')->get();



        //Actividades economicas
        $activity_economics = EconomicActivity::get();

        return view('table.benefit_colective.table_colective', [
            'activities' => $activities,
            'activity_economics' => $activity_economics,
            'tomadores_name' => $tomadores_name
        ]);
    }

    public function deleteColective(Request $req)
    {
        // Validamos que se hayan enviado IDs
        if (empty($req->ids)) {
            return response()->json(['message' => 'No se han enviado IDs.'], 400);
        }

        if (empty($req->motivo)) {
            return response()->json(['message' => 'No se han enviado motivo.'], 400);
        }

        // Convertir a array si no lo es.
        $policyIds = is_array($req->ids) ? $req->ids : [$req->ids];

        try {
            DB::beginTransaction();

            // Llamamos al método privado que contiene la lógica común.
            $this->processDeleteColective($policyIds, false, $req->motivo);

            DB::commit();

            return response()->json(['message' => 'Acción generada'], 200);
        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'message' => 'Importante',
                'e' => $e->getMessage(),
            ], 500);
        }
    }


    private function processDeleteColective(array $policyIds, $individual = false, $motivo = null)
    {
        // Obtenemos todas las pólizas cuyo activity_id esté en el array de IDs.
        $policies = PolicySort::whereIn('activity_id', $policyIds)->get();

        // Creamos un mapa con activity_id como llave para acceder rápidamente.
        $policiesMap = $policies->keyBy('activity_id');

        // Recorremos cada ID recibido y procesamos la póliza si existe.
        foreach ($policyIds as $policyId) {
            if (!isset($policiesMap[$policyId])) {
                // Si no se encuentra la póliza, la saltamos.
                continue;
            }

            $policy = $policiesMap[$policyId];

            // Se arma la descripción para la acción.
            $description = "Eliminación del beneficio de Colectividad en la póliza {$policy->formatNumberConsecutive()}";

            // Ejecutamos la acción correspondiente.
            ActionController::create(
                $policy->activity->id,
                ActionPolizaSort::ELIMINACION_DE_COLECTIVIDAD,
                $description
            );

            // Actualizamos la póliza: se elimina la colectividad.
            $policy->update([
                "benefit_colective" => "No",
                "date_benefit_colective" => null
            ]);

            // Tomador de la póliza.
            $taker = $policy->activity->affiliate;
            $nameTaker = mb_convert_case(mb_strtolower($taker->full_name, 'UTF-8'), MB_CASE_TITLE, 'UTF-8');

            $formattedId = $policy->formatSortNumber();
            $client = $policy->activity->client;


            $informacion = [
                'nameTaker'   => $nameTaker,
                'formattedId' => $formattedId,
                'unfulfilled_conditions' => $policy->unfulfilled_conditions,
                'motivo' => $motivo
            ];

            $body = $this->bodyEmailColevidad($individual, $informacion);

            // Agrega el email principal de notificaciones y todos los adicionales
            $notiEmails = PolicySortController::getAdditionalNotificationEmails(
                $policy->id
            );
            $strEmails = implode(',', $notiEmails);

            // Envío del correo. 
            $mailSent = new SendDocumentDataBase(
                $strEmails,
                $description,
                "<EMAIL>",
                "Solicitud de colectividad",
                [
                    "text" => $body,
                    "sender" => 'MNK seguros'
                ],
                "<EMAIL>",
                [],
                "send_document",
                $client
            );
            
            // Capturar el resultado del envío
            $result = $mailSent->sendMail();

            //Registramos los datos del correo enviado para la trazabilidad
            $mailBoardController = new MailBoardController();
            $mailBoardController->createRegisterMail(
                $policy->activity_id,
                Service::SERVICE_POLICY_SORT_MNK,
                $policy->consecutive,
                'Tomador',
                $nameTaker,
                $taker->doc_number,
                $description,
                $body,
                $strEmails,
                $result,
                null
            );
        }
    }


    private function bodyEmailColevidad($individual, $content)
    {
        $currentDate = date('d/m/Y');

        // Si 'unfulfilled_conditions' existe y es una cadena, la decodificamos a array.
        if (isset($content['unfulfilled_conditions']) && is_string($content['unfulfilled_conditions'])) {
            $content['unfulfilled_conditions'] = json_decode($content['unfulfilled_conditions'], true);
        }

        if ($individual) {
            $body = "
            ¡Buen día, {$content['nameTaker']}!

            De la revisión efectuada a la póliza {$content['formattedId']}, se determinaron los siguientes incumplimientos respecto al Beneficio de Colectividad:

               • Reclasificación de casos ocurridos a personas trabajadoras de nuevo ingreso a \"caso no asegurado por no reportarse en planillas\" (recurrencia de 2 eventos en el mismo año póliza).

                    " . ($content['unfulfilled_conditions']['respon_uno'] ?? '') . "

               • Declaraciones inexactas sobre el siniestro que hagan incurrir a MNK Seguros en una aceptación indebida (recurrencia de más de 2 eventos).

                    " . ($content['unfulfilled_conditions']['respon_dos'] ?? '') . "

               • Determinación de que no se realizó una adecuada investigación de los siniestros reportados a MNK Seguros (recurrencia de más de 2 eventos).

                    " . ($content['unfulfilled_conditions']['respon_tres'] ?? '') . "

               • Deudas o recibos pendientes de pago correspondientes a periodos anteriores.

                    " . ($content['unfulfilled_conditions']['respon_cuatro'] ?? '') . "

               • Periodos de no cobertura durante los 2 últimos años de vigencia (recurrencia de más de 1 evento).

                    " . ($content['unfulfilled_conditions']['respon_cinco'] ?? '') . "

            Por lo anterior, lamentamos informarle que, a partir del " . $currentDate . " se elimina el Beneficio de Colectividad a la póliza, por lo que deberá seguir presentando el reporte de “Inclusión provisional de persona asegurada” para las personas trabajadoras de nuevo ingreso, previo al inicio de labores.

            Una vez se verifique la corrección o ajuste de los motivos que generaron el (los) incumplimiento(s) señalado(s), estaremos en la mejor disposición de valorar la reinstalación de este Beneficio.

            ¡Muchas gracias por su confianza!

            Cordialmente,

            Área de Aseguramiento  
            Seguro Obligatorio de Riesgos del Trabajo
        ";
        } else {
            $body = "
            ¡Buen día, {$content['nameTaker']}!

            Lamentamos informarle que, debido a {$content['motivo']}, a partir del " . $currentDate . " se elimina el Beneficio de Colectividad a todas las pólizas del Seguro Obligatorio de Riesgos del Trabajo, por lo que deberá seguir presentando el reporte de “Inclusión provisional de persona asegurada” para las personas trabajadoras de nuevo ingreso, previo al inicio de labores.

            En caso de consultas, por favor contáctenos al correo electrónico <EMAIL> o al teléfono (506) 4102-7681. ¡Será un gusto servirle!

            ¡Muchas gracias por su confianza!

            Cordialmente,

            Área de Aseguramiento  
            Seguro Obligatorio de Riesgos del Trabajo
        ";
        }
        return $body;
    }




    public function deleteColectiveIndividual(Request $req)
    {
        try {
            DB::beginTransaction();
            $policy = PolicySort::where('activity_id', $req->policy_id_modal)->firstOrFail();

            $respuestas = [
                'respon_uno'    => $req->input('respon_uno'),
                'respon_dos'    => $req->input('respon_dos'),
                'respon_tres'   => $req->input('respon_tres'),
                'respon_cuatro' => $req->input('respon_cuatro'),
                'respon_cinco'   => $req->input('respon_cinco'),
            ];

            $policy->unfulfilled_conditions = json_encode($respuestas);

            $policy->save();

            $policyIds = [$policy->activity_id];

            $this->processDeleteColective($policyIds, true);

            DB::commit();

            return redirect()->back()->with('success', 'Datos de colectividad guardados correctamente');
        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->back()->with('error', 'Error: ' . $e->getMessage());
        }
    }
}
