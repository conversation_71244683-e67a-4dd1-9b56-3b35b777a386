<?php

namespace App\Http\Controllers\Tables;

use App\Activity;
use App\Client;
use App\EconomicActivity;
use App\Http\Controllers\Controller;
use App\Mail\SendDocumentDataBase;
use App\MailBoard;
use App\MailBoardDocument;
use App\Service;
use App\States\StatePoliza;
use Illuminate\Http\Request;
use App\PolicySort;

class MailBoardController extends Controller
{

    //Tablero de correos
    public function index(Request $request, $cpath)
    {
        $client = Client::where('path', $cpath)->first();

        //Filtros de servicio por paremtro 
        $services_ids = $request->input('service_ids');

        //Todas las actividades de pólizas que tengan el estado 20 emitida activa
        $emails = MailBoard::join('services', 'services.id', '=', 'mail_boards.service_id')
            ->select(
                'mail_boards.id',
                'services.name',
                'mail_boards.name_email',
                'mail_boards.number_policy', 
                'mail_boards.person_type',
                'mail_boards.person_name',
                'mail_boards.person_identification',
                'mail_boards.state',
                'mail_boards.send_emails',
                'mail_boards.date_send', 
                'mail_boards.created_at'
            );

        if (!empty($services_ids)) {
            $emails->whereIn('mail_boards.service_id', $services_ids);
        }

        //filtro por el consecutivo de la poliza
        if ($request->id_poliza) {
            $id_poliza = $request->id_poliza;
            $emails->where('mail_boards.number_policy', $id_poliza);
        }

        //filtro por el consecutivo de la poliza
        if ($request->person_names) {
            $emails->whereIn('mail_boards.person_type', $request->person_names);
        }

        // Filtro por nombre de persona
        if($request->person_name){
            $emails->where('mail_boards.person_name', 'like', '%' . $request->person_name . '%');
        }

        // Filtro por identificación de persona
        if($request->person_identification){
            $emails->where('mail_boards.person_identification', 'like', '%' . $request->person_identification . '%');
        }

        //filtro por estados
        if ($request->state_names) {
            $emails->whereIn('mail_boards.state', $request->state_names);
        }

        //filtro por fecha de envio
        if ($request->start_date) {
            $emails->where('mail_boards.date_send', $request->start_date);
        }

        //filtro por nombre del correo 
        if ($request->name_email) {
            $emails->where('mail_boards.name_email', 'like', '%' . $request->name_email . '%');
        }

        $emails = $emails->orderBy('id', 'DESC')
            ->paginate(15)
            ->appends(request()->only(
                'service_ids', 'id_poliza',
                'person_names', 'person_name',
                'person_identification','state_names',
                'start_date', 'name_email'
            ));

        $type_service = Service::select('id', 'name')->get();

        $type_person = [
            (object) ['id' => 1, 'name' => 'Intermediario'],
            (object) ['id' => 2, 'name' => 'Asegurado'],
            (object) ['id' => 3, 'name' => 'Tomador'],
        ];

        $states = [
            (object) ['id' => 1, 'name' => 'Reboto'],
            (object) ['id' => 2, 'name' => 'Entregado'],
            (object) ['id' => 3, 'name' => 'otros'],
        ];

        // Aplicar el filtro de fechas con la función
        $startDate = $request->input('start_date');
        $endDate = $request->input('end_date');

      
        return view('table.mail_board.table_mail_board', [
            'emails'        => $emails,
            'type_service'  => $type_service,
            'type_person'   => $type_person,
            'states'        => $states          
        ]);
    }

    public function createRegisterMail($activityId,$serviceId, $numberPolicy, $personType, $personName, $personIdentification, $subject, $text, $email, $result, $files, $takerAuthorizedId = null, $mailer = 'default')
    {
        try {

            // Registrar en la tabla de correos para seguimientos
            $mailBoard = MailBoard::create([
                'activity_id'           => $activityId,
                'service_id'            => $serviceId,
                'number_policy'         => $numberPolicy,
                'person_type'           => $personType,
                'person_name'           => mb_convert_case(mb_strtolower($personName), MB_CASE_TITLE, "UTF-8"),
                'person_identification' => $personIdentification,
                'subject_email'         => $subject,
                'name_email'            => $subject,
                'body_email'            => is_array($text) ? ($text['text'] ?? '') : $text,
                'send_emails'           => is_array($email) ? implode(',', $email) : $email,
                'date_send'             => now(),
                'state'                 => $result['status'],
                'res_email'             => $result['message'],
                'taker_authorized_id'   => $takerAuthorizedId
            ]);

            // Registrar el documento en la tabla mail_board_documents si existe
            if (!empty($files)) {
                foreach ($files as $file) {
                    MailBoardDocument::create([
                        'type'          => $file['type'], 
                        'path'          => $file['path'],
                        'name'          => $file['name'],
                        'mail_board_id' => $mailBoard->id
                    ]);
                }
            }

        } catch (\Throwable $e) {
            dd('Error al guardar en MailBoard:', $e->getMessage());
        }
    }

    public function reSendEmail(Request $req, $cpath)
    {

        try {
            
            //Id del MailBoard
            $id = $req->input('id');
            $emailsValid = $req->emails;

            $emailBoard = MailBoard::where('id', $id)->first();

            $files = [];

            if ($emailBoard && $emailBoard->files && $emailBoard->files->isNotEmpty()) {
                foreach ($emailBoard->files as $doc) {
                    $files[] = [
                        'type' => $doc->type, 
                        'path' => $doc->path, 
                        'name' => $doc->name,
                    ];
                }
            }

            $subject = $emailBoard->subject_email;

            $text = $emailBoard->body_email;

            $emails = $emailsValid;
            $emailString = implode(',', $emails);

            // Enviar el correo el documento adjunto
            $mailSent = new SendDocumentDataBase(
                $emailString,
                $subject,
                "<EMAIL>",
                $subject,
                [
                    "text" => $text,
                    "sender" => 'mnk aseguramiento'
                ],
                "<EMAIL>",
                $files,
                "send_document_db",
                1,
                $req->host,
                '',     // ID de la actividad 
                '',     // ID de la acción de la actividad
                '',     // ID del servicio
                $emailBoard->type_provider
            );
            $mailSent->sendMail();
            // Verificamos si el correo fue enviado exitosamente
            if ($mailSent) {
                // Retornamos una respuesta positiva para que el frontend lo maneje como éxito
                return response()->json(true);
            } else {
                // Si hubo algún problema enviando el correo, retornamos false
                return response()->json(false);
            }
        } catch (\Exception $e) {
            dd($e);
        }
    }
}
