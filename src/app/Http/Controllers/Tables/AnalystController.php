<?php

namespace App\Http\Controllers\Tables;

use App\Action;
use App\Actions\ActionPolicySortCollection;
use App\Activity;
use App\ActivityActionDocument;
use App\Client;
use App\Http\Controllers\ActionController;
use App\Http\Controllers\Controller;
use App\Http\Controllers\Integrations\LogtailController;
use App\Http\Controllers\Integrations\WebserviceElectronicInvoiceController;
use App\Http\Controllers\Services\AccountingEntryController;
use App\Http\Controllers\Integrations\WebserviceAcselController;
use App\Http\Controllers\Services\LiquidationPolicyController;
use App\Http\Controllers\Services\PolicySortCollectionController;
use App\Http\Controllers\Services\PolicySortController;
use App\Http\Controllers\Services\RenewalSortController;
use App\Http\Middleware\NumberToWords;
use App\Mail\SendDocumentDataBase;
use App\PolicySort;
use App\PolicySortCollection;
use App\PolicySpreadsheet;
use App\Service;
use App\State;
use App\States\StateLiquidacion;
use App\States\StatePolicySortCollection;
use App\States\StateRenewal;
use DateTime;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use PDF;

class AnalystController extends Controller
{
    public function collections(Request $request, $cpath)
    {

        $client = Client::where('path', $cpath)->first();

        $query = Activity::where('client_id', $client->id)
            ->where('service_id', Service::SERVICE_POLICY_SORT_COLLECTION_MNK)
            ->whereNotNull('parent_id')
            ->where('state_id', '!=', State::RECIBO_ABONO_MENSUAL_ANULADO_NO_PAGO)
            ->where(function ($q) {
                $q->where('state_id', '!=', StatePolicySortCollection::RECIBO_ANULADO)
                    ->orWhere('updated_at', '>=', Carbon::now()->subDays(15));
            });

        // Filtrado por nombre
        if ($request->filled('nombre')) {
            $query->whereHas('affiliate', function ($q) use ($request) {
                $q->where('first_name', 'like', "%" . $request->input('nombre') . "%");
            });
        }
        if ($request->filled('state')) {
            $query->whereHas('policy_sort_collection', function ($q) use ($request) {
                $q->where('payment_status', $request->input('state'));
            });
        }

        if ($request->filled('tipo_recibo')) {
            $query->whereHas('policy_sort_collection', function ($q) use ($request) {
                $q->where('type_receipt', $request->input('tipo_recibo'));
            });
        }
        if ($request->filled('id_poliza')) {
            $query->whereHas('parent_activity.policy_sort', function ($q) use ($request) {
                $q->where('id', $request->input('id_poliza'));
            });
        }
        // Se le aplica el filtro Número de Póliza en caso de que se haya enviado
        if ($request->filled('num_poliza')) {
            $query->whereHas('parent_activity.policy_sort', function ($q) use ($request) {
                // Se elimina el prefijo SORT- y se filtra por el número
                $consecutive = preg_replace('/\D/', '', $request->input('num_poliza'));
                $q->where('consecutive', $consecutive);
            });
        }


        // Ejecuta la consulta y pagina los resultados
        $activities = $query->orderBy('created_at', 'desc')->paginate(10)
            ->appends(request()->only('id_poliza', 'nombre', 'tipo_recibo', 'state'));
        $policy_activity = Activity::where('client_id', $client->id)
            ->whereIn('id', $activities->pluck('parent_id'))
            ->get();

        $status = [
            'pending' => 'Pendiente',
            'cancelled' => 'Rechazado',
            'pending-approval' => 'Pendiente de aprobación',
            'approved' => 'Pagado',
            'rejected' => 'Rechazado',
            'recibo-anulado' => 'Recibo anulado',
            'devuelto_sort' => 'Devuelto área SORT',
        ];

        return view('table.analyst.collections', [
            'activities' => $activities,
            'policy_activity' => $policy_activity,
            'status' => $status
        ]);
    }

    //EJECUTAR ACCIONES DE LOS COBROS
    public function actionCollection(Request $req, $cpath, $id)
    {
        $client = Client::where('path', $cpath)->first();
        $collection_activity = Activity::where('client_id', $client->id)->where('id', $id)->first();
        $collection = $collection_activity->policy_sort_collection;

        if (!$req->filled('gestion')) {
            return response()->json([
                'error' => 'Por favor, debes seleccionar un resultado de la gestión de cobro, para el recibo #' . $collection->id . '.',
            ], 400);
        }

        $descriptions = [
            0 => "Contactado",
            1 => "Cobrado",
            2 => "Pendiente",
            3 => "Seguimiento",
            4 => "Aprobado",
            5 => "Rechazado",
            6 => "Devuelto",
            7 => "Pendiente de aprobación",
        ];

        $description = $descriptions[$req->gestion] ?? 'Gestión desconocida';
        $gestion_number = $req->gestion;

        DB::beginTransaction();
        try {
            switch ($gestion_number) {
                case 0:
                case 1:
                case 2:
                case 3:
                    // Manejo de seguimientos: Contactado, Cobrado, Pendiente, Seguimiento
                    $action_id = ActionPolicySortCollection::REPORTAR_SEGUIMIENTO_COBROS;
                    ActionController::create($collection_activity->id, $action_id, $description);
                    break;

                case 4:
                    // Manejo de acciones de cobros (Aprobar)
                    $data = $this->handleAprobacion($collection, $collection_activity, $cpath, $client, $id, $req);
                    break;

                case 5:
                    // Acciones de rechazar
                    $this->denialPayment($collection, $collection_activity, $cpath, $client);
                    break;
                case 6:
                    // Acciones de DEVUELTO
                    $action_id = ActionPolicySortCollection::REPORTAR_DEVOLUCION_AREA_SORT;
                    ActionController::create($collection_activity->id, $action_id, $description);
                    $collection->payment_status = PolicySortCollection::RETURNED_PAYMENT_STATUS;
                    $collection->save();
                    break;
                case 7:
                    // Acciones de DEVUELTO
                    $action_id = ActionPolicySortCollection::REPORTAR_DEVOLUCIÓN_ÁREA_COBROS;
                    ActionController::create($collection_activity->id, $action_id, $description);
                    $collection->payment_status = PolicySortCollection::PAYMENT_STATUS_PENDING_APPROVAL;
                    $collection->save();
                    break;

                default:
                    throw new Exception('Gestión no reconocida');
            }

            DB::commit();

            // Preparar la respuesta
            $response = [
                'message' => 'Acción ejecutada correctamente',
            ];

            if ($gestion_number == 4) {
                $spreadsheetsData = $this->spreadsheetsData($id);
                $response['messageSpreadsheets'] = $spreadsheetsData['messageSpreadsheets'] ?? null;
                $response['policy_id'] = $spreadsheetsData['policy_id'] ?? null;
            }

            return response()->json($response);
        } catch (Exception $e) {
            LogtailController::error(LogtailController::SERVERS, $e->getMessage(), LogtailController::serializeJson($e));
            DB::rollBack();
            return response()->json([
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * @throws Exception
     */
    private function handleAprobacion($collection, $collection_activity, $cpath, $client, $id, $req)
    {
        try {
            $policy = PolicySort::where('activity_id', $collection_activity->parent_id)->first();
            if ($collection) {
                $message = $collection->payment_method === 'PT' ? 'Aprobar pago pt' : 'Aprobar pago tb';
                switch ($collection->type_receipt) {
                    case PolicySortCollection::EMISSION:
                        $action = $collection->payment_method === 'PT' ?
                            ActionPolicySortCollection::APROBAR_PAGO_TARJETA :
                            ActionPolicySortCollection::APROBAR_PAGO_TB;
                        break;
                    case PolicySortCollection::PERIOD_INCREASE:
                        $action = $collection->payment_method === 'PT' ?
                            ActionPolicySortCollection::APROBAR_PAGO_TARJETA_AUMENTO_DEL_SEGURO_PERIODO :
                            ActionPolicySortCollection::APROBAR_PAGO_TB_AUMENTO_SEGURO;
                        //REPORTAR PAGO AUMENTO DE SEGURO en el servicio POLIZA SORT
                        break;
                    case PolicySortCollection::MONTHLY:
                        $action = $collection->payment_method === 'PT' ?
                            ActionPolicySortCollection::APROBAR_PAGO_TARJETA_RECIBO_ABONO_MENSUAL :
                            ActionPolicySortCollection::APROBAR_PAGO_TB_ABONO_MENSUAL;
                        //REPORTAR PAGO ABONO REALIZADO de POLIZA SORT
                        break;
                    case PolicySortCollection::QUARTERLY:
                        $action = $collection->payment_method === 'PT' ?
                            ActionPolicySortCollection::APROBAR_PAGO_TARJETA_RECIBO_ABONO_TRIMESTRAL :
                            ActionPolicySortCollection::APROBAR_PAGO_TB_RECIBO_ABONO_TRIMESTRAL;
                        //REPORTAR PAGO ABONO REALIZADO de POLIZA SORT
                        break;
                    case PolicySortCollection::SEMIANNUAL:
                        $action = $collection->payment_method === 'PT' ?
                            ActionPolicySortCollection::APROBAR_PAGO_TARJETA_RECIBO_ABONO_SEMESTRAL :
                            ActionPolicySortCollection::APROBAR_PAGO_TB_RECIBO_ABONO_SEMESTRAL;
                        //REPORTAR PAGO ABONO REALIZADO de POLIZA SORT
                        break;
                    case PolicySortCollection::REHABILITATION:
                        $action = $collection->payment_method === 'PT' ?
                            ActionPolicySortCollection::APROBAR_PAGO_TARJETA_RECIBO_REHABILITACION :
                            ActionPolicySortCollection::APROBAR_PAGO_TB_RECIBO_REHABILITACION;
                        //REPORTAR PAGO REHABILTIACION REALIZADO de POLIZA SORT
                        break;
                    case PolicySortCollection::LIQUIDATION:
                        $action = $collection->payment_method === 'PT' ?
                            ActionPolicySortCollection::APROBAR_PAGO_TARJETA_RECIBO_LIQUIDACION :
                            ActionPolicySortCollection::APROBAR_PAGO_TB_RECIBO_LiQUIDACION;
                        //REPORTAR PAGO LIQUIDACION

                        $activity_liquidation = Activity::where('parent_id', $collection_activity->parent_id)
                            ->where('service_id',Service::SERVICE_LIQUIDATION_SORT_MNK)
                            ->where('state_id', StateLiquidacion::LIQUIDACION_PENDIENTE_PAGO_POR_TOMADOR )->first();

                        $liquidation = new LiquidationPolicyController();
                        $liquidation->submitSettlementPayment($cpath, $activity_liquidation->id);
                        break;
                    case PolicySortCollection::RENEWAL:
                        $action = $collection->payment_method === 'PT' ?
                            ActionPolicySortCollection::APROBAR_PAGO_TARJETA_RECIBO_LIQUIDACION :
                            ActionPolicySortCollection::APROBAR_PAGO_TB_RECIBO_RENOVACION;
                        //REPORTAR PAGO RENOVACION
                        $activity_renewal = Activity::where('parent_id', $collection_activity->parent_id)
                            ->where('service_id', Service::SERVICE_RENEWAL_SORT_MNK)
                            ->where('state_id', StateRenewal::PENDIENTE_PAGO_RENOVACION )
                            ->first();
                        $renewal = new RenewalSortController();
                        $renewal->reportRenewalPayment($cpath, $activity_renewal->id);

                        break;

                    default:
                        throw new Exception('Tipo de cobro no reconocido: ' . $collection_activity->type_receipt);
                }
                $collection->payment_status = PolicySortCollection::PAYMENT_STATUS_APPROVED;
                $collection->save();
                //Actualizar estado de la actividad
                $activity_action = ActionController::create($collection_activity->id, $action, $message);

                // Verificar si es un cobro de emisión
                if ($collection->type_receipt == PolicySortCollection::EMISSION) {
                    $changeStatePolicy = new PolicySortCollectionController();
                    $changeStatePolicy->reportPolicyPaymentMade($collection_activity, $cpath, $req);
                }
                //envia recibo de pago
                $polisortCollection = new PolicySortCollectionController();
                $polisortCollection->sendPaymentReceipt($client, $collection_activity, $activity_action, $action, true);

                register_shutdown_function(function () use ($collection_activity) {
                    $webserviceFac = new WebserviceElectronicInvoiceController();
                    $resp = $webserviceFac->sendInvoice($collection_activity->id);
                });
            } else {
                throw new Exception('Pago no encontrado: ' . $id);
            }
            $emails = [$policy->email];
            $imageUrl = asset('images/mnk.png');
            $text = [
                "text" => "
                    <br>
                    <img src='{$imageUrl}' alt='MNK Logo' style='width: 150px; height: auto;'>
                    <br>
                    MNK SEGUROS, le entrega el documento electrónico generado por la prima de su seguro. Estos documentos adjuntos son los solicitados por hacienda para la presentación de los gastos.
                ",
                "sender" => 'MNK Seguros'
            ];
            $mailSent = new SendDocumentDataBase(
                implode(',', $emails),
                "Aprobación de pago",
                "<EMAIL>",
                "Aprobación de pago",
                $text,
                "<EMAIL>",
                [],
                "send_document_db",
                $client,
                request()->getHost(),
                $collection_activity->id,
                $activity_action->id,
                $collection_activity->service->id
            );
            
              // Capturar el resultado del envío
            $result = $mailSent->sendMail();

            //Registramos los datos del correo enviado para la trazabilidad
            $mailBoardController = new MailBoardController();
            $mailBoardController->createRegisterMail(
                $collection_activity->id,
                $collection_activity->service->id,
                $collection_activity->parent->policy_sort->consecutive,
                'Tomador',
                $collection_activity->parent->affiliate->full_name,
                $collection_activity->parent->affiliate->doc_number,
                'Aprobación de pago',
                $text,
                $emails,
                $result,
                null
            );

            $data = $this->spreadsheetsData($id);

            $policySortController = new PolicySortController();
            $policySortController->generateAccountingEntry($cpath, $policy->id);

            $this->generateAccountingEntryCollections($cpath, $policy->id, $action);

            return $data;
        } catch (Exception $e) {
            throw new Exception("Error en la aprobación del pago: " . $e->getMessage());
        }
    }



    public function spreadsheetsData($id)
    {

        $messageSpreadsheets = false;
        $policy_sorts = PolicySort::query()
            ->select('policy_sorts.*')
            ->leftJoin('activities as b', 'b.parent_id', '=', 'policy_sorts.activity_id')
            ->leftJoin('policy_sort_collections as ps', 'ps.activity_id', '=', 'b.id')
            ->where('ps.activity_id', $id)
            ->first();
        $economicActivities = [
            '0111',
            '0112',
            '0113',
            '0115',
            '0116',
            '0119',
            '0121',
            '0122',
            '0123',
            '0124',
            '0125',
            '0126',
            '0127',
            '0128',
            '0129',
            '4100'
        ];

        if ($policy_sorts && in_array($policy_sorts->activity_economic_id, $economicActivities)) {
            $countSpreadsheets = PolicySpreadsheet::leftJoin('activities as a', 'a.id', '=', 'policy_spreadsheets.activity_id')
                ->leftJoin('policy_sorts as p', 'p.activity_id', '=', 'a.parent_id')
                ->where('p.id', $policy_sorts->id)
                ->count();
            if ($countSpreadsheets == 0) {
                $messageSpreadsheets = true;
            }
        }


        $data = [
            'messageSpreadsheets' => $messageSpreadsheets,
            'policy_id' => $policy_sorts->id
        ];

        return $data;
    }

    public function reSendEmail(Request $req, $cpath)
    {
        //Id de cobros
        $id = $req->input('id');

        $client = Client::where('path', $cpath)->first();
        $collection_activity = Activity::where('client_id', $client->id)
            ->where('id', $id)
            ->first();
        $collection = $collection_activity->policy_sort_collection;

        //TODO: ENVÍO DEL CORREO
        $emails = [$collection_activity->affiliate->email];
        $text = [
            "text" => "Buen día," . "\n\n" .
                "Por medio del presente, le proporcionamos el enlace para efectuar el pago correspondiente a su recibo." . "\n\n" .
                "Por favor, debe ingresar <a href='" . secure_url('/servicio/' . $collection_activity->id . '/policy_sort_collection/pago_poliza') . "'>aquí</a> con su usuario y contraseña.\n\n",
            "sender" => 'MNK Seguros'
        ];
        $mailSent = new SendDocumentDataBase(
            implode(',', $emails),
            "Envío del enlace de pago - Recibo #{$collection->id}",
            "<EMAIL>",
            "Envío del enlace de pago - Recibo #{$collection->id}",
            $text,
            "<EMAIL>",
            [],
            "send_document_db",
            $client,
            request()->getHost(),
            $collection_activity->id,
            '',
            $collection_activity->service->id
        );
        $mailSent->sendMail();
        // Verificamos si el correo fue enviado exitosamente
        if ($mailSent) {
            // Retornamos una respuesta positiva para que el frontend lo maneje como éxito
            return response()->json(true);
        } else {
            // Si hubo algún problema enviando el correo, retornamos false
            return response()->json(false);
        }
    }

    public function emailPaymentTb($collection, $client, $activity_action, $collection_activity)
    {
        try {
            if (
                $collection->type_receipt === PolicySortCollection::MONTHLY
                || $collection->type_receipt === PolicySortCollection::QUARTERLY
                || $collection->type_receipt === PolicySortCollection::SEMIANNUAL
            ) {
                $emails = [$collection_activity->affiliate->email];

                $policy_activity = Activity::where('id', $collection_activity->parent_id)->first();
                $policy = $policy_activity->policy_sort;
                $tomador = mb_convert_case(mb_strtolower($collection_activity->affiliate->first_name ?? ''), MB_CASE_TITLE, "UTF-8");
                $url_env = config('app.url');
                $text = [
                    "text" => "¡Buen día, $tomador!

                    Nos complace informarle que hemos efectuado el abono solicitado a su póliza #{$policy->formatSortNumber()}.
                    
                    Igualmente, le recordamos que para realizar sus reportes de planillas, inclusiones o accidentes, por favor, debe ingresar <a href='{$url_env}'>aquí</a> con su usuario y contraseña.
                    
                    En caso de cualquier consulta, por favor, contáctenos al correo electrónico <EMAIL> o al teléfono 4102-7600. ¡Será un gusto servirle!
                    
                    Nos sentimos sumamente honrados y agradecidos por la confianza que ha depositado en nosotros. Nuestro propósito es transformar la protección en una experiencia ágil, confiable y humana.
                    ",
                    "sender" => 'mnk aseguramiento',

                ];

                $mailSent = new SendDocumentDataBase(
                    implode(',', $emails),
                    "Abono a la póliza #{$policy->formatSortNumber()}",
                    "<EMAIL>",
                    "Abono a la póliza #{$policy->formatSortNumber()}",
                    $text,
                    "<EMAIL>",
                    [],
                    "send_document_db",
                    $client,
                    request()->getHost(),
                    $collection_activity->id,
                    $activity_action->id,
                    $collection_activity->service->id
                );
                $mailSent->sendMail();
            }
        } catch (Exception $e) {
            throw $e;
        }
    }

    //FUNCION PARA RECHAZAR PAGOS
    private function denialPayment($collection, $collection_activity, $cpath, $client)
    {

        //Enviar correo de aprobación
        try {
            $message = 'Pago rechazado';
            switch ($collection->type_receipt) {
                case PolicySortCollection::EMISSION:
                    $action = $collection->payment_method === 'PT' ?
                        ActionPolicySortCollection::RECHAZAR_PAGO_TARJETA :
                        ActionPolicySortCollection::RECHAZAR_PAGO_TB;
                    break;
                case PolicySortCollection::PERIOD_INCREASE:
                    $action = $collection->payment_method === 'PT' ?
                        ActionPolicySortCollection::RECHAZAR_PAGO_TARJETA_AUMENTO_DEL_SEGURO_PERIODO :
                        ActionPolicySortCollection::RECHAZAR_PAGO_TB_AUMENTO_SEGURO;
                    //REPORTAR PAGO AUMENTO DE SEGURO en el servicio POLIZA SORT
                    break;
                case PolicySortCollection::MONTHLY:
                    $action = $collection->payment_method === 'PT' ?
                        ActionPolicySortCollection::RECHAZAR_PAGO_TARJETA_RECIBO_ABONO_MENSUAL :
                        ActionPolicySortCollection::RECHAZAR_PAGO_TB_ABONO_RECIBO_MENSUAL;
                    //REPORTAR PAGO ABONO REALIZADO de POLIZA SORT
                    break;
                case PolicySortCollection::QUARTERLY:
                    $action = $collection->payment_method === 'PT' ?
                        ActionPolicySortCollection::RECHAZAR_PAGO_TARJETA_RECIBO_ABONO_TRIMESTRAL :
                        ActionPolicySortCollection::RECHAZAR_PAGO_TB_RECIBO_ABONO_TRIMESTRAL;
                    //REPORTAR PAGO ABONO REALIZADO de POLIZA SORT
                    break;
                case PolicySortCollection::SEMIANNUAL:
                    $action = $collection->payment_method === 'PT' ?
                        ActionPolicySortCollection::RECHAZAR_PAGO_TARJETA_RECIBO_ABONO_SEMESTRAL :
                        ActionPolicySortCollection::RECHAZAR_PAGO_TB_RECIBO_ABONO_SEMESTRAL;
                    //REPORTAR PAGO ABONO REALIZADO de POLIZA SORT
                    break;
                case PolicySortCollection::REHABILITATION:
                    $action = $collection->payment_method === 'PT' ?
                        ActionPolicySortCollection::RECHAZAR_PAGO_TARJETA_RECIBO_REHABILITACION :
                        ActionPolicySortCollection::RECHAZAR_PAGO_TB_RECIBO_REHABILITACION;
                    //REPORTAR PAGO REHABILTIACION REALIZADO de POLIZA SORT
                    break;
                case PolicySortCollection::LIQUIDATION:
                    $action = $collection->payment_method === 'PT' ?
                        ActionPolicySortCollection::RECHAZAR_PAGO_TARJETA_RECIBO_LIQUIDACION :
                        ActionPolicySortCollection::RECHAZAR_PAGO_TB_RECIBO_LIQUIDACION;
                    break;
                case PolicySortCollection::RENEWAL:
                    $action = $collection->payment_method === 'PT' ?
                        ActionPolicySortCollection::RECHAZAR_PAGO_TARJETA_RECIBO_RENOVACION :
                        ActionPolicySortCollection::RECHAZAR_PAGO_TB_RECIBO_RENOVACION;
                    break;
                default:
                    return redirect()->back()->with('error', 'Error al ejecutar la acción');
            }
            $collection->payment_status = PolicySortCollection::PAYMENT_STATUS_REJECTED;
            if ($collection->payment_method === 'TB') {
                $collection->invoice_tb = null;
                $collection->additional_invoice_tb = null;
            } else if ($collection->payment_method === 'PT') {
                $collection->invoice_pt = null;
            }
            $collection->save();

            $activity_action = ActionController::create($collection_activity->id, $action, $message);
            $emails = [$collection_activity->affiliate->email];
            $text = [
                "text" => "Estimado cliente, su pago ha sido rechazado.",
                "sender" => 'MNK Seguros'
            ];
            $mailSent = new SendDocumentDataBase(
                implode(',', $emails),
                "Factura Electrónica",
                "<EMAIL>",
                "Factura electrónica prueba mail",
                $text,
                "<EMAIL>",
                [],
                "send_document_db",
                $client,
                request()->getHost(),
                $collection_activity->id,
                $activity_action->id,
                $collection_activity->service->id
            );
            $mailSent->sendMail();
            return $activity_action;
        } catch (Exception $e) {
            throw new Exception("Error en la aprobación del pago: " . $e->getMessage());
        }
    }

    /**
     * Función para generar asientos contables de cobres
     */
    public function generateAccountingEntryCollections($cpath, $id, $action)
    {

        $webserviceController = new WebserviceAcselController();
        $trm = $webserviceController->getTrm();

        if ($trm == 0) {
            throw new \Exception('Error: El TRM no es valido');
        }

        $policy = PolicySort::find($id);
        //$activityPayment = Activity::where('parent_id', $policy->activity->id)->where("service_id", 76)->latest()->first();

        $activityPayment = Activity::where('parent_id', $policy->activity->id)
            ->where("service_id", Service::SERVICE_POLICY_SORT_COLLECTION_MNK)
            ->whereHas('policy_sort_collection', function ($query) {
                $query->where('payment_status', PolicySortCollection::PAYMENT_STATUS_APPROVED);
            })
            ->latest()
            ->first();


        $valorPagado = $activityPayment->policy_sort_collection->value_paid ?? 0;
        $valorPagadoCobro = $activityPayment->policy_sort_collection->total_amount ?? 0;
        $valorPagadoCobroUsd = $valorPagadoCobro ?? 0;
        $valorPagadoUsd = $valorPagado ?? 0;

        if ($policy->type_currency == 'CRC') {
            $valorPagadoCobroUsd = $valorPagadoCobro / $trm;
        }

        if ($activityPayment->policy_sort_collection->payment_currency == 'CRC') {
            $valorPagadoUsd = $valorPagado / $trm;
        }

        $accountingEntryController = new AccountingEntryController();

        if (($valorPagadoUsd - $valorPagadoCobroUsd) > 10) {
            $accountingEntryController->reportAccountCase100($cpath, $policy->id);
            return response()->json([
                'success' => true
            ]);
        }

        /** Situación 1: Cuando el pago se hace a través de una transacción bancaria **/
        if (    
                //Recibos de pagos por transferencias bancarias de emision 
                $action == ActionPolicySortCollection::APROBAR_PAGO_TB 
                //Recibos de pagos por transferencia bancaria de abono mensual 
                || $action == ActionPolicySortCollection::APROBAR_PAGO_TB_ABONO_MENSUAL
                //Recibos de pagos por transferencia bancaria de abono semestral 
                || $action == ActionPolicySortCollection::APROBAR_PAGO_TB_RECIBO_ABONO_SEMESTRAL
                //Recibos de pagos por transferencia bancaria abono trimestral
                || $action == ActionPolicySortCollection::APROBAR_PAGO_TB_RECIBO_ABONO_TRIMESTRAL
                //Recibos de pagos por transferencia bancaria aumento seguro
                || $action == ActionPolicySortCollection::APROBAR_PAGO_TB_AUMENTO_SEGURO 
                //Recibos de pagos por transferencia bancaria recibo de renovacion
                || $action == ActionPolicySortCollection::APROBAR_PAGO_TB_RECIBO_RENOVACION
            ) {

            if ($activityPayment->policy_sort_collection->applied_credit_note_amount > 0) {
                $accountingEntryController->reportAccountCase055($cpath, $policy->id);
            } else {

                //if ( config('app.env') != 'prod' ) {
                if ($policy->type_currency && $policy->type_currency != $activityPayment->policy_sort_collection->payment_currency) { //moneda poliza y moneda pago diferente
                    $accountingEntryController->reportAccountCaseThree003($cpath, $policy->id, $valorPagado);
                } else if ($activityPayment->policy_sort_collection->total_amount != $activityPayment->policy_sort_collection->value_paid) { //moto pagado y prima diferente
                    $accountingEntryController->reportAccountCaseFour003($cpath, $policy->id, $valorPagado);
                } else {
                    $accountingEntryController->reportAccountCaseOne003($cpath, $policy->id);
                }
                //}

            }

            return response()->json([
                'success' => true
            ]);
        }

        /** Situación 2: Cuando el pago se hace a través de una tarjeta de crédito o pago tarjeta **/
        $allowActions = [

            //Recibos de pagos de tarjetas de emision
            ActionPolicySortCollection::APROBAR_PAGO_TARJETA,
            //Recibos de pagos de tarjetas abonos mensuales
            ActionPolicySortCollection::APROBAR_PAGO_TARJETA_RECIBO_ABONO_MENSUAL,
            //Recibos de pagos de tarjetas abonos semestrales
            ActionPolicySortCollection::APROBAR_PAGO_TARJETA_RECIBO_ABONO_TRIMESTRAL,
            //Recibos de pagos de tarjetas de abonos trimestrales 
            ActionPolicySortCollection::APROBAR_PAGO_TARJETA_RECIBO_ABONO_TRIMESTRAL,

            //Recibos de tarjetas de creditos de emision 
            ActionPolicySortCollection::REGISTRAR_PAGO_TC,
            //Recibos de tarjetas de credito de abonos mensuales
            ActionPolicySortCollection::REGISTRAR_PAGO_TC_RECIBO_ABONO_MENSUAL,
            //Recibos de tarjetas de creditos de abonos trimestrales
            ActionPolicySortCollection::REGISTRAR_PAGO_TC_RECIBO_ABONO_TRIMESTRAL,
            //Recibos de tarjetas de creditos de abonos semestrales
            ActionPolicySortCollection::REGISTRAR_PAGO_TC_RECIBO_ABONO_SEMESTRAL,
            //Recibos de tarjetas de creditos de aumento de seguro
            ActionPolicySortCollection::REGISTRAR_PAGO_TC_AUMENTO_SEGURO_PERIODO,
            //Recibos de renovación de aumentos de seguros
            ActionPolicySortCollection::REGISTRAR_PAGO_TC_RECIBO_RENOVACION
        ];
        // if ($action == ActionPolicySortCollection::APROBAR_PAGO_TARJETA || $action == ActionPolicySortCollection::REGISTRAR_PAGO_TC || $action == ActionPolicySortCollection::REGISTRAR_PAGO_TC_AUMENTO_SEGURO_PERIODO || $action == ActionPolicySortCollection::REGISTRAR_PAGO_TC_RECIBO_RENOVACION) {
        if (in_array($action, $allowActions)) {
            $accountingEntryController->reportAccountCaseTwo003($cpath, $policy->id);

            //reportAccountCaseThree003

            return response()->json([
                'success' => true
            ]);
        }
    }
}
