<?php

namespace App\Http\Controllers\Tables;

use App\Action;
use App\Affiliate;
use App\Area;
use App\DataPreInvoice;
use App\ActivityActionDocument;
use App\ConstancySort;
use App\Activity;
use App\Client;
use App\Http\Controllers\ActionController;
use App\MedicalBill;
use App\MedicalServicesSort;
use App\Medication;
use App\PolicySort;
use App\Http\Controllers\Controller;
use App\Provider;
use App\Service;
use App\State;
use App\SuppliesMot;
use App\Utilities\Utilities;
use Illuminate\Http\Request;
use App\PolicySortCollection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Response;

use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\Validator;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use PHPExcel_Shared_Date;
use App\User;


class ProviderController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    private $type_service;

    public function __construct()
    {
        $this->middleware('auth');

        $this->type_service = [
            Service::SERVICE_MEDICAL_SERVICES_SORT_MNK => 'Prestacion Medica',
            Service::SERVICE_SUPPLIER_MOT_MNK => 'Insumos o MOT',
            Service::SERVICE_MEDICAMENTOS_MNK => 'Medicamentos',
            Service::SERVICE_MEDICAL_SERVICES_SECONDARY_CARE_SORT_MNK => 'Prestación Medica - atención secundaria',
            Service::SERVICE_GIS_SORT_MNK => 'GIS SORT',
        ];
    }

    /// Obtiene los ids de las actividades descendientes
    protected function getAllDescendantActivityIds($parentIds)
    {
        $allIds = collect($parentIds); // empezamos con los padres
        $currentLevelIds = $parentIds;

        while ($currentLevelIds->isNotEmpty()) {
            $children = Activity::whereIn('parent_id', $currentLevelIds)->pluck('id');

            $allIds = $allIds->merge($children);
            $currentLevelIds = $children;
        }

        return $allIds->unique()->values(); // por si hay duplicados
    }

    public function form(Request $req, $cpath, $id)
    {

    }

    public function providerData($cpath)
    {

        // Obtener el usuario autenticado
        $user = Auth::user();

        if (is_null($user)) {
            return redirect()->back()->with('error', 'Usuario no encontrado.');
        }

        return view('table.provider.menu.provider_data', [
            'affiliate' => '',
            'policySortData' => '',
            'id' =>  $user->id, // Esto es para que el id de la ruta hacia la vista servicios asignados
            'user' => $user,
        ]);
    }

    public function medicalAccounts(Request $req, $cpath)
    {
        // Obtener el usuario autenticado
        $user = Auth::user();

        if (is_null($user)) {
            return redirect()->back()->with('error', 'Usuario no encontrado.');
        }

        // Filtros de fechas
        $action_start_date = $req->input('action_start_date_submit'); // Formato: 'Y-m-d'
        $action_end_date = $req->input('action_end_date_submit'); // Formato: 'Y-m-d'

        // Inicializar la consulta base de actividades médicas
        $activityQuery = Activity::query()->with(['socialSecurityProvider', 'medical_bill']);

        // Aplicar filtros según el rol del usuario
        switch (true) { // Usamos switch (true) para evaluar condiciones booleanas
            case $user->area_id === Area::ADMINISTRATIVE: // Administrativo
                $activityQuery
                    ->whereHas('medical_bill')
                    ->orWhereHas('socialSecurityProvider');
                break;

            case $user->isProvider(): // Proveedor
                // Filtrar por existencia de medical_bill y por affiliate_id
                $activityQuery
                    ->whereHas('medical_bill')
                    ->orWhereHas('socialSecurityProvider')
                    ->where('affiliate_id', $user->provider->affiliate_id);
                break;

            default:
                $activityQuery
                    ->whereHas('medical_bill')
                    ->orWhereHas('socialSecurityProvider');
                break;
        }

        // Procesar y aplicar los filtros de fechas
        $activityQuery = $this->applyDateFilters($activityQuery, $action_start_date, $action_end_date);

        // Ordenar por la columna 'created_at' en orden descendente
        $activityQuery->orderBy('created_at', 'desc');

        // Obtener los resultados paginados
        $activities = $activityQuery->paginate(10)
        ->appends(request()->only('action_start_date_submit', 'action_end_date_submit'));

        return view('table.provider.medical_bills_table', [
            'activities' => $activities,
            'id' =>  $user->id, // Esto es para que el id de la ruta hacia la vista servicios asignados
            'user' => $user,
        ]);
    }

    /**
     * Aplica los filtros de fecha a la consulta
     */
    private function applyDateFilters($query, $startDate, $endDate)
    {
        if (!empty($startDate)) {
            $startDate = Carbon::createFromFormat('Y-m-d', $startDate)->startOfDay();
        }

        if (!empty($endDate)) {
            $endDate = Carbon::createFromFormat('Y-m-d', $endDate)->endOfDay();
        }

        if (!empty($startDate) && !empty($endDate)) {
            // Filtrar entre las fechas de inicio y fin
            $query->whereBetween('created_at', [$startDate, $endDate]);
        } elseif (!empty($startDate)) {
            // Filtrar desde la fecha de inicio
            $query->where('created_at', '>=', $startDate);
        } elseif (!empty($endDate)) {
            // Filtrar hasta la fecha de fin
            $query->where('created_at', '<=', $endDate);
        }

        return $query;
    }

    public function filePreInvoice($cpath)
    {
        // Obtener el usuario autenticado
        $user = Auth::user();

        if (is_null($user)) {
            return redirect()->back()->with('error', 'Usuario no encontrado.');
        }

        return view('table.provider.holder_provider.file_preinvoice_one', [
            'data' => [],
            'id' =>  $user->id, // Esto es para que el id de la ruta hacia la vista servicios asignados
            'user' => $user,
        ]);
    }

    public function filePreInvoiceMultiple($cpath)
    {
        // Obtener el usuario autenticado
        $user = Auth::user();

        if (is_null($user)) {
            return redirect()->back()->with('error', 'Usuario no encontrado.');
        }

        return view('table.provider.holder_provider.file_preinvoice_two', [
            'data' => [],
            'id' =>  $user->id, // Esto es para que el id de la ruta hacia la vista servicios asignados
            'user' => $user,
        ]);
    }

    public function fileElectronicInvoice($cpath, $idPreInvoice = null)
    {
        // Obtener el usuario autenticado
        $user = Auth::user();

        if (is_null($user)) {
            return redirect()->back()->with('error', 'Usuario no encontrado.');
        }

        return view('table.provider.holder_provider.file_electronic_invoice', [
            'data' => [],
            'id' =>  $user->id, // Esto es para que el id de la ruta hacia la vista servicios asignados
            'user' => $user,
            'idPreInvoice' => $idPreInvoice // Puede ser null si no se envía
        ]);
    }

    public function fileElectronicInvoiceAdministrative($cpath, $idPreInvoice = null)
    {
        $user = Auth::user();
        $menssage = '';
        $ibanAccounts = [];

        if (is_null($user)) {
            return redirect()->back()->with('error', 'Usuario no encontrado.');
        }

        if ($user->area_id != Area::PROVIDER) {
            $menssage = 'El usuario no es de rol proveedor.';
        } elseif (!$user->provider) {
            $menssage = 'El usuario no tiene un proveedor asociado.';
        } elseif (!$user->provider->iban_accounts) {
            $menssage = 'El usuario no tiene cuentas IBAN registradas.';
        } else {
            $ibanAccounts = json_decode($user->provider->iban_accounts, true);
            if (!is_array($ibanAccounts) || count($ibanAccounts) === 0) {
                $menssage = 'El usuario no tiene cuentas IBAN válidas.';
            }
        }

        return view('table.provider.holder_provider.file_electronic_invoice_administrative', [
            'data' => [],
            'id' => $user->id,
            'user' => $user,
            'ibanAccounts' => $ibanAccounts,
            'menssage' => $menssage
        ]);
    }

    public function downloadSpreadsheet()
    {
        $filePath = public_path('files/PLANTILLA_PROVEEDOR.xlsx');

        if (!file_exists($filePath)) {
            abort(404, 'Archivo no encontrado');
        }

        return Response::download($filePath, 'PLANTILLA_PROVEEDOR.xlsx');
    }
    public function downloadExplainPreInvoice()
    {
        $filePath = public_path('files/Explicación_Plantilla_Prefactura.xlsx');

        if (!file_exists($filePath)) {
            abort(404, 'Archivo no encontrado');
        }

        return Response::download($filePath, 'Explicación_Plantilla_Prefactura.xlsx');
    }

    public function downloadSpreadsheetElectronicInvoice()
    {
        $filePath = public_path('files/PLANTILLA_PROVEEDOR_FACTURA_ELECTRONICA.xlsx');

        if (!file_exists($filePath)) {
            abort(404, 'Archivo no encontrado');
        }

        return Response::download($filePath, 'PLANTILLA_PROVEEDOR_FACTURA_ELECTRONICA.xlsx');
    }

    public function save(Request $req, $cpath)
    {
        $req->validate([
            'affiliate_file' => 'required|mimes:xlsx,xls|max:5120', // Límite de tamaño 5MB
        ]);

        $client = Client::where('path', $cpath)->firstOrFail();
        $file = $req->file('affiliate_file');
        $description = $req->input('description');

        DB::beginTransaction();
        try {

            $activityNew = Activity::create([
                'client_id' => $client->id,
                'affiliate_id' => '1',
                'service_id' => Service::SERVICE_MEDICAL_BILLS_MNK,
                'state_id' => State::REGISTRADO,
                'user_id' => Auth::id(),
            ]);

            $medicalBillNew = MedicalBill::create([
                'description' => $description,
                'activity_id' => $activityNew->id,
            ]);

            Excel::selectSheetsByIndex(0)
                ->filter('chunk')
                ->load($file->path())
                ->chunk(10, function ($reader) use (&$medicalBillNew, &$errors, &$alerts, &$total, &$processedRecords) {
                    $reader->each(function ($row) use (&$medicalBillNew, &$errors, &$alerts, &$total, &$processedRecords) {
                        $this->processAffiliateSpreadsheet($medicalBillNew->id, $row, $errors, $alerts, $total, $processedRecords);
                    });
                });

            if (!empty($errors)) {
                throw new \Exception('Errores durante el procesamiento: ' . implode(", ", $errors));
            }

            $cuentasMedicas = Action::CUENTAS_MEDICAS;
            $actionId = $cuentasMedicas::CARGUE_PRE_FACTURA;
            $activityAction = ActionController::create($activityNew->id, $actionId, 'PREFACTURA EN REVISION');

            if ($req->hasFile('supporting_pdf')) {
                $this->uploadSupportingDocuments($req->file('supporting_pdf'), $activityAction, $activityNew, $file);
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Archivo procesado correctamente.',
                'alerts' => $alerts,
                'id' => $activityNew->id
            ]);

        } catch (\Exception $e) {
            DB::rollback();

            return response()->json([
                'message' => 'Errores durante el procesamiento del cargue manual.',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    private function processAffiliateSpreadsheet($idMedicalBillNew, $row, &$errors, &$alerts, &$total, &$processedRecords)
    {
        $fields = [
            'nombre_paciente' => 'Nombre del paciente',
            'id_trabajador' => 'Identificación trabajador',
            'no_poliza_sort' => 'No. Poliza SORT',
            'fecha_caso' => 'Fecha del caso',
            'numero_caso' => 'Número del caso',
            'servicio' => 'Servicios',
            'valor_servicio' => 'Valor servicios',
            'resultado' => 'Resultado',
        ];

        $rowNumber = 1;

        if ($row instanceof \Maatwebsite\Excel\Collections\CellCollection) {
            $row = $row->toArray();
        }

        $row = array_change_key_case($row, CASE_LOWER);

        foreach ($fields as $field => $fieldName) {
            if (empty($row[$field])) {
                throw new \Exception("Faltan datos en la fila {$rowNumber}, campo: {$fieldName}.");
            }
        }

        if (is_numeric($row['fecha_caso'])) {
            $fechaCaso = PHPExcel_Shared_Date::ExcelToPHPObject($row['fecha_caso']);
        } else {
            try {
                $fechaCaso = Carbon::createFromFormat('d/m/Y', $row['fecha_caso']);
            } catch (\Exception $e) {
                $errors[] = "El formato de la fecha en la fila {$rowNumber} no es válido (d/m/Y): {$row['fecha_caso']}";
                return;
            }
        }

        $fechaCasoFormateada = $fechaCaso->format('Y-m-d');
        $policySort = PolicySort::where('id', $row['no_poliza_sort'])->first();

        if (!$policySort) {
            throw new \Exception("La póliza SORT No. {$row['no_poliza_sort']} en la fila {$rowNumber} no existe.");
        }

        $validResults = ['aprobado', 'rechazado'];
        if (!in_array(strtolower($row['resultado']), $validResults)) {
            throw new \Exception("El valor del resultado en la fila {$rowNumber} debe ser 'aprobado' o 'rechazado'.");
        }

        DataPreInvoice::create([
            'patient_name' => $row['nombre_paciente'],
            'worker_id' => $row['id_trabajador'],
            'policy_sort_no' => $row['no_poliza_sort'],
            'case_date' => $fechaCasoFormateada,
            'case_number' => $row['numero_caso'],
            'service' => $row['servicio'],
            'service_value' => $row['valor_servicio'],
            'result' => $row['resultado'],
            'id_medical_bills' => $idMedicalBillNew,
            'id_policy_sorts' => $policySort->id ?? null,
        ]);

        $processedRecords++;
    }

    /**
     * Sube los documentos PDF de soporte a S3.
     */
    private function uploadSupportingDocuments($pdfFiles, $activityAction, $activityNew, $file)
    {

        //sube archivo xlsx
        $document = 'planilla_prefactura';
        $fileName = "{$document}_{$activityNew->id}.xlsx";
        $filePath = "activity_action_document/{$fileName}";

        Storage::disk('s3')->put($filePath, fopen($file->getRealPath(), 'r+'));

        $activityActionDocument = new ActivityActionDocument();
        $activityActionDocument->activity_action_id = $activityAction->id;
        $activityActionDocument->name = $document;
        $activityActionDocument->path = $filePath;
        $activityActionDocument->save();
        //

        $camel = 1;
        foreach ($pdfFiles as $pdf) {
            $pdfFilePath = $pdf->store('uploads');
            $document = 'soporte_prefactura';
            $fileName = "{$document}_{$activityNew->id}_{$camel}.pdf";
            $filePath = "activity_action_document/{$fileName}";

            // Subir el archivo a S3
            Storage::disk('s3')->put($filePath, fopen(storage_path("app/{$pdfFilePath}"), 'r+'));

            $activityActionDocument = new ActivityActionDocument();
            $activityActionDocument->activity_action_id = $activityAction->id;
            $activityActionDocument->name = $document;
            $activityActionDocument->path = $filePath;
            $activityActionDocument->save();

            $camel++;
        }
    }

    public function assignedService($cpath, Request $request, $id)
    {

        $request->flash();
        $message = '';
        $stateIds = $request->input('state_ids');
        $caso = $request->input('caso');
        $caso = $request->input('caso');
        $numAffiliate = $request->input('num_affiliate');
        $nameAffiliate = $request->input('name_affiliate');

        $action_start_date = $request->input('action_start_date_submit');
        $action_end_date = $request->input('action_end_date_submit');

        $user = User::where('id', $id)->first();
        $client = Client::where('path', $cpath)->first();
        $serviceIds = [
            Service::SERVICE_MEDICAL_SERVICES_SORT_MNK,
            Service::SERVICE_SUPPLIER_MOT_MNK,
            Service::SERVICE_MEDICAMENTOS_MNK,
            Service::SERVICE_MEDICAL_SERVICES_SECONDARY_CARE_SORT_MNK,
        ];
        $activityQuery = Activity::where('client_id', $client->id)
            ->whereIn('service_id', $serviceIds);

        if (!empty($stateIds)) {
            $activityQuery->whereIn('service_id', $stateIds);
        }


        if (!empty($caso)) {
            $caso = ltrim($caso, '0');

            $activityQuery = $activityQuery->whereHas('parent_activity.gis_sort', function ($subQuery) use ($caso) {
                $subQuery->where('consecutive_gis', 'like', '%' . $caso . '%')
                    ->orWhere('consecutive', 'like', '%' . $caso . '%');
            });

        }

        if (!empty($numAffiliate)) {
            $activityQuery = $activityQuery->whereHas('affiliate', function ($subQuery) use ($numAffiliate) {
                $subQuery->where('doc_number', 'like', '%' . $numAffiliate . '%');
            });
        }

        if (!empty($nameAffiliate)) {
            $activityQuery = $activityQuery->whereHas('affiliate', function ($subQuery) use ($nameAffiliate) {
                $subQuery->where('full_name', 'like', '%' . $nameAffiliate . '%');
            });
        }


        if (!empty($action_start_date) && !empty($action_end_date)) {

            $activityQuery = $activityQuery->whereHas('parent_activity.gis_sort', function ($query) use ($action_start_date, $action_end_date) {
                $query->whereRaw('DATE(created_at) BETWEEN ? AND ?', [$action_start_date, $action_end_date]);
                $query->orderBy('id', 'asc');
            });


        } elseif (!empty($action_start_date)) {

            $activityQuery = $activityQuery->whereHas('parent_activity.gis_sort', function ($query) use ($action_start_date) {
                $query->whereRaw('DATE(created_at) >= ?', [$action_start_date]);
                $query->orderBy('id', 'asc');
            });

        } elseif (!empty($action_end_date)) {
            $activityQuery->whereHas('parent_activity.gis_sort', function ($query) use ($action_end_date) {
                $query->whereRaw('DATE(created_at) <= ?', [$action_end_date]);
                $query->orderBy('id', 'asc');
            });
        }

        if (Auth::user()->isProvider()) {

            $proveedor = Auth::user()->provider_id;

            $activityQuery->whereHas('medical_services_sort', function ($query) use ($proveedor) {
                $query->where('primary_care_provider', $proveedor);
            });

            $activityQuery->where(function ($query) use ($proveedor) {
                $query->whereHas('medical_services_sort', function ($q) use ($proveedor) {
                    $q->where('primary_care_provider', $proveedor);
                })->orWhereHas('medical_services_secondary_care_sort', function ($q) use ($proveedor) {
                    $q->where('primary_care_provider', $proveedor);
                });
            });


            // Si es TPA, también agregar GIS_SORT_MNK filtrado por provider_id
            if (Auth::user()->isTPA()) {
                $activityQuery->orWhere(function ($subQuery) use ($proveedor) {
                    $subQuery->where('service_id', Service::SERVICE_GIS_SORT_MNK)
                        ->whereHas('gis_sort', function ($q) use ($proveedor) {
                            $q->where('provider_id', $proveedor);
                        });
                });
            }

            if (!$proveedor) {
                $message = 'Usuario logueado sin proveedor asignado';
            }

        }

        //ME-2479 Se ajusta el filtro para nuevo rol TPA
        $typeServiceFiltered = $this->type_service;
        if (!Auth::user()->isTPA()) {
            unset($typeServiceFiltered[Service::SERVICE_GIS_SORT_MNK]);
        }


        //si viene el filtro [proveedor]
        if ($request->input('provider')) {

            $providers = is_array($request->input('provider'))
                ? $request->input('provider')
                : [$request->input('provider')];

            $activityQuery = $activityQuery->where(function ($query) use ($providers) {
                $query->whereHas('gis_sort', function ($q) use ($providers) {
                    $q->whereIn('provider_id', $providers);
                })
                    ->orWhereHas('medical_services_sort', function ($q) use ($providers) {
                        $q->whereIn('primary_care_provider', $providers);
                    })
                    ->orWhereHas('medical_services_secondary_care_sort', function ($q) use ($providers) {
                        $q->whereIn('primary_care_provider', $providers);
                    })
                    ->orWhereHas('medication', function ($q) use ($providers) {
                        $q->whereIn('provider', $providers);
                    })
                    ->orWhereHas('supplies_mot', function ($q) use ($providers) {
                        $q->whereIn('provider', $providers);
                    })
                    ->orWhereHas('peItSort', function ($q) use ($providers) {
                        $q->whereIn('code_ips_pe', $providers);
                    });
            });

        }

        if ($request->input('holderPolicy')) {
            $holderName = strtolower($request->input('holderPolicy'));

            // Buscar IDs de afiliados con coincidencia de nombre
            $affiliateIds = Affiliate::whereRaw('LOWER(full_name) LIKE ?', ["%{$holderName}%"])->pluck('id');

            // Buscar IDs de actividades relacionadas con esos afiliados (solo pólizas)
            $policyActivityIds = Activity::where('client_id', $client->id)
                ->whereHas('affiliate', function ($q) use ($affiliateIds) {
                    $q->whereIn('id', $affiliateIds);
                })
                ->whereIn('service_id', [
                    Service::SERVICE_POLICY_SORT_MNK
                ])
                ->pluck('id');

            // Obtener todos los descendientes
            $allRelatedIds = $this->getAllDescendantActivityIds($policyActivityIds);

            // Aplicar el filtro a la query original
            $activityQuery = $activityQuery->whereIn('id', $allRelatedIds);
        }

        $activityQuery = $activityQuery->orderBy('updated_at','desc');

        $activities = $activityQuery->paginate(20)
            ->appends(request()->only('num_affiliate','name_affiliate',
                'action_start_date_submit', 'action_end_date_submit',
                'caso', 'state_ids', 'holderPolicy', 'provider'));

        //enviar lista de proveedores existentes
        //$providers = Provider::all();
        $query = Provider::orderBy('name', 'asc');
        $query->where('active', 1);
        $query->whereNull('deleted_at');
        $providers = $query->get();

        return view('table.provider.holder_provider.assigned_services', [
            'activities' => $activities,
            'providers' => $providers,
            'user' => $user,
            'id' => $id,
            'type_service' => $typeServiceFiltered,
            'error' => $message
        ]);
    }

}
