<?php

namespace App\Http\Controllers\Tables;

use App\Activity;
use App\Area;
use App\Http\Controllers\Controller;
use App\State;
use App\States\StateMedicalBillsServiceSort;
use App\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Auth;

class MedicalBillsAuditController extends Controller
{

    public function medicalBillsAuditTable( Request $req, $cpath)
    {
        // Obtener el usuario autenticado
        $user = Auth::user();

        if (is_null($user)) {
            return redirect()->back()->with('error', 'Usuario no encontrado.');
        }

        // Filtros de fechas
        $action_start_date = $req->input('action_start_date_submit'); // Formato: 'Y-m-d'
        $action_end_date = $req->input('action_end_date_submit'); // Formato: 'Y-m-d'

        // Inicializar la consulta base de actividades médicas
        $activityQuery = Activity::query();

        if($user->area_id == Area::ADMINISTRATIVE){
            $activityQuery->whereHas('medical_bill');
        }
        elseif (Auth::user()->view_auditoria_cuentas())
        {
            // Filtrar por existencia de medical_bill y por el estado de la actividad
            $activityQuery->whereHas('medical_bill')
                ->where('activities.state_id', '!=', StateMedicalBillsServiceSort::REGISTRADO);
        }
        else{
            return redirect()->back()->with('error', 'Acceso no permitido');
        }

        // Procesar y aplicar los filtros de fechas
        $activityQuery = $this->applyDateFilters($activityQuery, $action_start_date, $action_end_date);

        // Ordenar por la columna 'created_at' en orden descendente
        $activityQuery->orderBy('created_at', 'desc');

        // Obtener los resultados paginados
        $activities = $activityQuery->paginate(10)
            ->appends(request()->only('action_start_date_submit', 'action_end_date_submit'));

        return view('table.medical-bills-audit', [
            'activities' => $activities,
            'id' =>  1, // Esto es para que el id de la ruta de asignaciones
            'user' => $user,
        ]);
    }

    /**
     * Aplica los filtros de fecha a la consulta
     */
    private function applyDateFilters($query, $startDate, $endDate)
    {
        if (!empty($startDate)) {
            $startDate = Carbon::createFromFormat('Y-m-d', $startDate)->startOfDay();
        }

        if (!empty($endDate)) {
            $endDate = Carbon::createFromFormat('Y-m-d', $endDate)->endOfDay();
        }

        if (!empty($startDate) && !empty($endDate)) {
            // Filtrar entre las fechas de inicio y fin
            $query->whereBetween('created_at', [$startDate, $endDate]);
        } elseif (!empty($startDate)) {
            // Filtrar desde la fecha de inicio
            $query->where('created_at', '>=', $startDate);
        } elseif (!empty($endDate)) {
            // Filtrar hasta la fecha de fin
            $query->where('created_at', '<=', $endDate);
        }

        return $query;
    }
}