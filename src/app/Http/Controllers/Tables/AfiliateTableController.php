<?php

namespace App\Http\Controllers\Tables;

use App\Action;
use App\Actions\ActionConstaciasSort;
use App\Actions\ActionMedicalServiceSecondarySort;
use App\Actions\ActionMedicalServiceSort;
use App\Actions\ActionPeItSort;
use App\Activity;
use App\ActivityAction;
use App\ActivityActionDocument;
use App\ActivityDocument;
use App\ConstancySort;
use App\EconomicBenefit;
use App\Http\Controllers\Services\ComprehensiveAccidentManagementController;
use App\Http\Controllers\Services\PeIpSortController;
use App\Http\Controllers\Services\PolicySortController;
use App\Http\Controllers\Services\ReintegrateServicesController;
use App\Mail\SendDocumentDataBase;
use App\medicalBenefitRequest;
use App\Affiliate;
use App\Client;
use App\GisSort;
use App\Http\Controllers\ActionController;
use App\Http\Controllers\Controller;
use App\Http\Controllers\Services\MedicalServicesController;
use App\Http\Controllers\Services\PeRecognitionExpensesController;
use App\MedicalServicesSecondaryCareSort;
use App\MedicalServicesSort;
use App\Actions\ActionGisSort;
use App\Medication;
use App\PeIpSort;
use App\PeitInabilitySort;
use App\PeItSort;
use App\PolicySort;
use App\PolicySpreadsheet;
use App\PolicySpreadsheetAffiliate;
use App\Provider;
use App\Service;
use App\State;
use App\States\StateGis;
use App\States\StatePeItSort;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use PDF;
use DateTime;

class AfiliateTableController extends Controller
{
    public function index(Request $request, $cpath, $id)
    {
        $client = Client::where('path', $cpath)->first();
        $activities = Activity::where('client_id', $client->id)->whereIn('service_id', [
            Service::SERVICE_MEDICAL_SERVICES_SORT_MNK,
            Service::SERVICE_SUPPLIER_MOT_MNK,
            Service::SERVICE_MEDICAMENTOS_MNK,
            Service::SERVICE_PE_IT_SORT_MNK
        ]);

        if ($request->input('tomador_name')) {
            $activities = $activities->whereHas('affiliate', function ($query) use ($request) {
                $query->where('first_name', 'like', '%' . $request->input('tomador_name') . '%')
                    ->orWhere('last_name', 'like', '%' . $request->input('tomador_name') . '%');
            });
        }

        if ($request->input('identification_number')) {
            $activities = $activities->whereHas('affiliate', function ($query) use ($request) {
                $query->where('doc_number', 'like', '%' . $request->input('identification_number') . '%');
            });
        }

        $activity = Activity::where('affiliate_id', $id)
            ->where('service_id', Service::SERVICE_GIS_SORT_MNK)
            ->first();

        $gisSort = [];
        if ($activity) {
            $gisSort = GisSort::where('activity_id', $activity->id)->first();
        }

        return view('table.affiliate', [
            'activities' => $activities->orderBy('created_at', 'asc')->paginate(20),
            'id' =>$id,
            'gisSort' => $gisSort
        ]);

    }

    public function approve($cpath,$id,Request $request)
    {
        $client = Client::where('path', $cpath)->first();
        $activity = Activity::where('client_id', $client->id)->where('id', $id)->first();

        if ($request->action == 0){
            //REMITIR IMAGENES DIAGNOSTICAS
            $medical_services = new MedicalServicesController();
            $medical_services->issueDiagnosticImagingOrder($cpath,$id);
        }elseif ($request->action == 1){
            //REMITIR INCAPACIDAD
            $medical_services = new MedicalServicesController();
            $medical_services->medical_disability($cpath,$id);
        }elseif ($request->action == 2){
            //REMITIR A ESPECIALISTA
            $medical_services = new MedicalServicesController();
            $medical_services->referral_specialist($cpath,$id);
        }elseif ($request->action == 3){//REMITIR FORMULA MEDICA
            //REMITIR FORMULA MEDICA

        }elseif ($request->action == 4){
            //REMITIR A HOSPITALIZACION
            $medical_services = new MedicalServicesController();
            $medical_services->rehabilitation($cpath,$id);
        }


        return redirect()->back();
    }

    public function reject($cpath,$id,Request $request)
    {
        $reason = $request->reason;
        $medical_services = new MedicalServicesController();
        $medical_services->cancelService($reason,$cpath,$id);

        return redirect()->back();
    }

    public function medicalBenefits ($cpath,$id)
    {

        $activity = Activity::where('affiliate_id', $id)
            ->where('service_id', Service::SERVICE_GIS_SORT_MNK)
            ->orderBy('id', 'desc')
            ->first();

        if (!$activity) {
            return redirect()->back()->withErrors('Actividad no encontrada.');
        }

        $gisSort = GisSort::where('activity_id', $activity->id)->first();

        $affiliateId = $activity->affiliate_id;


        $activity = Activity::where('affiliate_id', $affiliateId)
            ->whereIn('service_id', [Service::SERVICE_MEDICAL_SERVICES_SORT_MNK, Service::SERVICE_MEDICAL_SERVICES_SECONDARY_CARE_SORT_MNK])
            ->with(['affiliate', 'gis_sort', 'state', 'medical_services_sort', 'medical_services_secondary_care_sort',
                'state','service'])
            ->paginate(10);

        foreach ($activity as $row) {
            $row->path = $this->get_document($row->id);
        }

        return view('table.affiliate.medical_benefits', [
            'gisSort' => $gisSort,
            'activity' => $activity,
            'id' => $id
        ]);

    }
    public function get_document($id)
    {
        $path = ActivityAction::where('action_id', '341') //ActionMedicalServiceSort::EMITIR_INCAPACIDAD_MEDICA
            ->where('activity_id', $id)
            ->leftJoin('activity_action_documents as ad', 'ad.activity_action_id', '=', 'activity_actions.id')
            ->orderByDesc('ad.id')
            ->limit(1)
            ->value('ad.path');

        return $path ?: '';
    }


    public function requestMedicalBenefitServices($cpath,$id)
    {

        $activity = Activity::where('affiliate_id', $id)
            ->where('service_id', Service::SERVICE_GIS_SORT_MNK)
            ->whereRaw('(
                SELECT COUNT(*) 
                FROM activity_actions aa 
                WHERE aa.new_state_id = 160 AND aa.activity_id = activities.id
            ) > 0')
            ->orderBy('id', 'desc')
            ->with(['gis_sort','affiliate'])
            ->get();

        if ($activity->isEmpty()) {
            return redirect()->back()->with('error', 'No cuenta con reportes de accidentes o registros GIS');
        }

        $activitys = $activity->first();
        $affiliate = $activitys->affiliate;

        $gisSort = GisSort::where('activity_id', $activitys->id)->first();

        $medicalBenefitRequest = medicalBenefitRequest::where('affiliate_id', $id)
            ->where('request_type','medical_benefit_services')
            ->where('state','pending')->first();

        $data_affiliate = [
            'worker_email' => ($medicalBenefitRequest->worker_email ?? '') == '' ? ($gisSort->email_affiliate ?? '') : $medicalBenefitRequest->worker_email,
            'worker_phone' => ($medicalBenefitRequest->worker_phone ?? '') == '' ? ($gisSort->cellphone_affiliate ?? '') : $medicalBenefitRequest->worker_phone,
            'worker_address' => ($medicalBenefitRequest->worker_address ?? '') == '' ?  ($gisSort->other_signs ?? '') : $medicalBenefitRequest->worker_address,
            'province' => ($medicalBenefitRequest->province ?? '') == '' ?  ($affiliate->province ?? '') : $medicalBenefitRequest->province,
            'canton' => ($medicalBenefitRequest->canton ?? '') == '' ?  ($affiliate->canton ?? '') : $medicalBenefitRequest->canton,
            'district' => ($medicalBenefitRequest->district ?? '') == '' ?  ($affiliate->district ?? '') : $medicalBenefitRequest->district
        ];

        return view('table.affiliate.affiliate_information', [
            'activity' => $activity,
            'medicalBenefitRequest' => $medicalBenefitRequest,
            'id' => $id,
            'affiliate' =>$affiliate,
            'gisSort' => $gisSort,
            'data_affiliate' => $data_affiliate
        ]);

    }

    public function saveMedicalBenefitServices(Request $request, $cpath,$id)
    {

        $activity = Activity::where('affiliate_id', $id)
            ->where('service_id', Service::SERVICE_GIS_SORT_MNK)
            ->orderBy('id', 'desc')
            ->first();

        if (!$activity) {
            return redirect()->back()->withErrors('Actividad no encontrada.');
        }

        $gisSort = GisSort::where('activity_id', $activity->id)->first();

        if (!$gisSort) {
            return redirect()->back()->withErrors('Datos de GisSort no encontrados.');
        }

        $medicalBenefitRequest = medicalBenefitRequest::where('activity_id', $request->case)
                                            ->where('request_type','medical_benefit_services')
                                            ->orderBy('id', 'desc')->first();

        if(!$medicalBenefitRequest){
            $medicalBenefitRequest = new medicalBenefitRequest();
        }

        $medicalBenefitRequest->activity_id = $request->case;
        $medicalBenefitRequest->id_type = $request->identification_titular;
        $medicalBenefitRequest->identification = $request->identification_number;
        $medicalBenefitRequest->worker_name = $request->worker_name;
        $medicalBenefitRequest->worker_email = $request->worker_email;
        $medicalBenefitRequest->worker_phone = $request->worker_phone;
        $medicalBenefitRequest->worker_address = $request->worker_address;
        $medicalBenefitRequest->province = $request->province;
        $medicalBenefitRequest->canton = $request->canton;
        $medicalBenefitRequest->district = $request->district;
        $medicalBenefitRequest->medical_service = $request->medical_service;
        $medicalBenefitRequest->request_description = $request->request_description;
        $medicalBenefitRequest->request_type = 'medical_benefit_services';
        $medicalBenefitRequest->affiliate_id = $id;
        $medicalBenefitRequest->state = 'pending';
        $medicalBenefitRequest->attention_type = $request->attention_type;
        $medicalBenefitRequest->save();

        return view('table.affiliate.supports', [
            'gisSort' => $gisSort,
            'id' => $id,
            'case' => $request->case
        ]);

    }
    public function saveSupports(Request $request, $cpath,$id)
    {
        DB::beginTransaction();
        try {

            $activity = Activity::where('id', $request->case)
                ->where('service_id', Service::SERVICE_GIS_SORT_MNK)
                ->first();

            if (!$activity) {
                return redirect()->back()->withErrors('Actividad no encontrada.');
            }

            $gisSort = GisSort::where('activity_id', $request->case)->first();

            if (!$gisSort) {
                return redirect()->back()->withErrors('Datos de GisSort no encontrados.');
            }

            $medicalBenefitRequest = medicalBenefitRequest::where('activity_id', $request->case)
                ->where('state','pending')
                ->orderBy('id', 'desc')->first();

            if ($medicalBenefitRequest->attention_type == 'primary') {

                $activityNew = new Activity();
                $activityNew->client_id = $activity->client_id;
                $activityNew->affiliate_id = $activity->affiliate_id;
                $activityNew->service_id = Service::SERVICE_MEDICAL_SERVICES_SORT_MNK;
                $activityNew->state_id = State::REGISTRADO;
                $activityNew->parent_id = $activity->id;
                $activityNew->user_id = $activity->user_id;
                $activityNew->save();

                $serviceMedical = new MedicalServicesSort();
                $serviceMedical->activity_id = $activityNew->id;
                $serviceMedical->action_id = $medicalBenefitRequest->medical_service;
                $serviceMedical->name_patient = $medicalBenefitRequest->worker_name;
                $serviceMedical->save();

                $description = "REPORTAR SOLICITUD PRESTACIÓN MÉDICA";
                $activityActionsCreated = ActionController::create($activityNew->id,
                    ActionMedicalServiceSort::REPORTAR_SOLICITUD_PRESTACION_MEDICA,
                    $description);

//                $controller = new MedicalServicesController();
//                $id_activity = $controller->reportMedicalServiceRequest($cpath, $activityNew->id, '', '', $medicalBenefitRequest->medical_service);

                if ($request->hasFile('medical_order')) {
                    $this->storeActivityDocument($request, 'medical_order', 254, $activityNew->id);
                }
                if ($request->hasFile('clinical_history')) {
                    $this->storeActivityDocument($request, 'clinical_history', 255, $activityNew->id);
                }

            } else {
                /**
                 * Creación de prestaciones médicas - Atención secondaria
                 */
                $activityNew = new Activity();
                $activityNew->client_id = $activity->client_id;
                $activityNew->affiliate_id = $activity->affiliate_id;
                $activityNew->service_id = Service::SERVICE_MEDICAL_SERVICES_SECONDARY_CARE_SORT_MNK;
                $activityNew->state_id = State::REGISTRADO;
                $activityNew->parent_id = $activity->id;
                $activityNew->user_id = $activity->user_id;
                $activityNew->save();

                $serviceMedical = new MedicalServicesSecondaryCareSort();
                $serviceMedical->activity_id = $activityNew->id;
                $serviceMedical->action_id = $medicalBenefitRequest->medical_service;
                $serviceMedical->name_patient = $medicalBenefitRequest->worker_name;
                $serviceMedical->save();

                $description = "REPORTAR SOLICITUD PRESTACIÓN MÉDICA";
                $activityActionsCreated = ActionController::create($activityNew->id,
                    ActionMedicalServiceSecondarySort::REPORTAR_SOLICITUD_PRESTACION_MEDICA,
                    $description);

                if ($request->hasFile('medical_order')) {
                    $this->storeActivityDocument($request, 'medical_order', 254, $activityNew->id);
                }
                if ($request->hasFile('clinical_history')) {
                    $this->storeActivityDocument($request, 'clinical_history', 255, $activityNew->id);
                }

            }

            $medicalBenefitRequest->state = 'approved';
            $medicalBenefitRequest->save();

            DB::commit();

            return view('table.affiliate', [
                'id' => $id,
                'gisSort' => $gisSort,
                'success' => 'Solicitud de servicio de prestaciones médicas realizada.'
            ]);


        } catch (\Exception $e) {
             DB::rollBack();
             return redirect()->back()->withErrors('Ocurrió un error: ' . $e->getMessage());
        }


    }

    public function requestMedication($cpath,$id)
    {

        $activity = Activity::where('affiliate_id', $id)
            ->where('service_id', Service::SERVICE_GIS_SORT_MNK)
            ->whereRaw('(
                SELECT COUNT(*) 
                FROM activity_actions aa 
                WHERE aa.new_state_id = 160 AND aa.activity_id = activities.id
            ) > 0')
            ->orderBy('created_at', 'desc')
            ->with(['gis_sort','affiliate'])
            ->get();

        if ($activity->isEmpty()) {
            return redirect()->back()->with('error', 'No cuenta con reportes de accidentes o registros GIS');
        }

        $activitys = $activity->first();
        $affiliate = $activitys->affiliate;

        $gisSort = GisSort::where('activity_id', $activitys->id)->first();

        $medicalBenefitRequest = medicalBenefitRequest::where('affiliate_id', $id)
            ->where('request_type','request_medications')
            ->where('state','pending')
            ->orderBy('id', 'desc')->first();

        $data_affiliate = [
            'worker_email' => ($medicalBenefitRequest->worker_email ?? '') == '' ? ($gisSort->email_affiliate ?? '') : $medicalBenefitRequest->worker_email,
            'worker_phone' => ($medicalBenefitRequest->worker_phone ?? '') == '' ? ($gisSort->cellphone_affiliate ?? '') : $medicalBenefitRequest->worker_phone,
            'worker_address' => ($medicalBenefitRequest->worker_address ?? '') == '' ?  ($gisSort->other_signs ?? '') : $medicalBenefitRequest->worker_address,
            'province' => ($medicalBenefitRequest->province ?? '') == '' ?  ($affiliate->province ?? '') : $medicalBenefitRequest->province,
            'canton' => ($medicalBenefitRequest->canton ?? '') == '' ?  ($affiliate->canton ?? '') : $medicalBenefitRequest->canton,
            'district' => ($medicalBenefitRequest->district ?? '') == '' ?  ($affiliate->district ?? '') : $medicalBenefitRequest->district
        ];

        return view('table.affiliate.affiliate_information_medication', [
            'activity' => $activity,
            'medicalBenefitRequest' => $medicalBenefitRequest,
            'id' => $id,
            'affiliate' =>$affiliate,
            'gisSort' => $gisSort,
            'data_affiliate' => $data_affiliate
        ]);

    }
    public function saveMedication(Request $request, $cpath,$id)
    {
        $activity = Activity::where('affiliate_id', $id)
            ->where('service_id', Service::SERVICE_GIS_SORT_MNK)
            ->orderBy('id', 'desc')
            ->first();

        if (!$activity) {
            return redirect()->back()->withErrors('Actividad no encontrada.');
        }

        $gisSort = GisSort::where('activity_id', $activity->id)->first();

        if (!$gisSort) {
            return redirect()->back()->withErrors('Datos de GisSort no encontrados.');
        }

        $medicalBenefitRequest = medicalBenefitRequest::where('activity_id', $request->case)
                                            ->where('request_type','request_medications')
                                            ->orderBy('id', 'desc')->first();

        if(!$medicalBenefitRequest){
            $medicalBenefitRequest = new medicalBenefitRequest();
        }

        $medicalBenefitRequest->activity_id = $request->case;
        $medicalBenefitRequest->id_type = $request->identification_titular;
        $medicalBenefitRequest->identification = $request->identification_number;
        $medicalBenefitRequest->worker_name = $request->worker_name;
        $medicalBenefitRequest->worker_email = $request->worker_email;
        $medicalBenefitRequest->worker_phone = $request->worker_phone;
        $medicalBenefitRequest->worker_address = $request->worker_address;
        $medicalBenefitRequest->province = $request->province;
        $medicalBenefitRequest->canton = $request->canton;
        $medicalBenefitRequest->district = $request->district;
        $medicalBenefitRequest->generic_name = $request->generic_name;
        $medicalBenefitRequest->quantity = $request->dose_quantity;
        $medicalBenefitRequest->duration_days = $request->duration_days;
        $medicalBenefitRequest->request_description = $request->request_description;
        $medicalBenefitRequest->request_type = 'request_medications';
        $medicalBenefitRequest->affiliate_id = $id;
        $medicalBenefitRequest->state = 'pending';
        $medicalBenefitRequest->save();

        return view('table.affiliate.supports_medication', [
            'gisSort' => $gisSort,
            'id' => $id,
            'case' => $request->case
        ]);

    }

    public function saveSupportsMedication(Request $request, $cpath, $id)
    {

        DB::beginTransaction();
        try {

            $activity = Activity::select('activities.*')
                ->leftJoin('medical_services_sort as m', 'm.activity_id', '=', 'activities.id')
                ->leftJoin('activities as b', 'b.id', '=', 'activities.parent_id')
                ->leftJoin('gis_sort as g', 'g.activity_id', '=', 'b.id')
                ->where('g.activity_id', '=', $request->case)
                ->orderBy('m.id', 'asc')
                ->first();

            if (!$activity) {
                return redirect()->back()->withErrors('Actividad no encontrada.');
            }

            $gisSort = GisSort::where('activity_id', $request->case)->first();

            if (!$gisSort) {
                return redirect()->back()->withErrors('Datos de GisSort no encontrados.');
            }

            $medicalBenefitRequest = medicalBenefitRequest::where('activity_id', $request->case)
                ->where('state','pending')
                ->orderBy('id', 'desc')->first();


            $activityNew = new Activity();
            $activityNew->client_id = $activity->client_id;
            $activityNew->affiliate_id = $activity->affiliate_id;
            $activityNew->service_id = Service::SERVICE_MEDICAMENTOS_MNK;
            $activityNew->state_id = State::REGISTRADO;
            $activityNew->parent_id = $activity->id;
            $activityNew->user_id = $activity->user_id;
            $activityNew->save();

            $medication = new Medication();
            $medication->activity_id = $activityNew->id;
            $medication->provider = Provider::PROVIDER_DOKKA;
            $medication->name_patient = $medicalBenefitRequest->worker_name;
            $medication->province_supplier_details = $medicalBenefitRequest->province;
            $medication->canton_supplier_details = $medicalBenefitRequest->canton;
            $medication->district_supplier_details = $medicalBenefitRequest->district;
            $medication->province_controlled_medication = $medicalBenefitRequest->province;
            $medication->canton_controlled_medication = $medicalBenefitRequest->canton;
            $medication->district_controlled_medication = $medicalBenefitRequest->district;
            $medication->province_medical_prescription = $medicalBenefitRequest->district;

            $medication->save();

            if ($request->hasFile('medical_order')) {
                $this->storeActivityDocument($request, 'medical_order', 256, $activityNew->id);
            }
            if ($request->hasFile('clinical_history')) {
                $this->storeActivityDocument($request, 'clinical_history', 257, $activityNew->id);
            }

            $medicalBenefitRequest->state = 'approved';
            $medicalBenefitRequest->save();


            DB::commit();

            return view('table.affiliate', [
                'id' => $id,
                'gisSort' => $gisSort,
                'success' => 'Solicitud de medicamentos realizada.'
            ]);


        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()->withErrors('Ocurrió un error: ' . $e->getMessage());
        }

    }

    public function economicBenefits ($cpath,$id)
    {
        $activity = Activity::where('affiliate_id', $id)
            ->where('service_id', Service::SERVICE_GIS_SORT_MNK)
            ->orderBy('id', 'desc')
            ->first();

        $gisSort = GisSort::where('activity_id', $activity->id)->first();

        $affiliateId = $activity->affiliate_id;

        $activity = Activity::where('affiliate_id', $affiliateId)
            ->whereIn('service_id', [Service::SERVICE_PE_IP_SORT_MNK,
                Service::SERVICE_PE_IT_SORT_MNK,
                Service::SERVICE_PE_MPT_SORT_MNK,
                Service::SERVICE_PE_RECOGNITION_EXPENSES_MNK,
                Service::SERVICE_MEDICAL_BILLS_MNK,
                Service::SERVICE_REINTEGRATE_MNK])
            ->whereNotNull('parent_id')
            ->orderBy('created_at','desc')
            ->with(['affiliate', 'gis_sort', 'state',
                'state','service'])
            ->paginate(10);

        foreach ($activity as $row) {

            switch ($row->service_id) {
                case \App\Service::SERVICE_PE_IP_SORT_MNK:
                    $row->service_description = 'Incapacidad permanente';
                    $row->id_service = $row->pe_ip_sort->id;
                    $row->days = $row->pe_ip_sort->payinfo_total_years_pay * 365;
                    break;
                case \App\Service::SERVICE_PE_IT_SORT_MNK:
                    $row->service_description = 'Incapacidad temporal';
                    $row->id_service = $row->peItSort->id;
                    $row->days = $row->peItSort->inabilitySort->days_it;
                    break;
                case \App\Service::SERVICE_PE_MPT_SORT_MNK:
                    $row->service_description = 'Muerte de la persona trabajadora';
                    break;
                case \App\Service::SERVICE_MEDICAL_BILLS_MNK:
                    $row->service_description = 'Cuentas médicas';
                    break;
                case \App\Service::SERVICE_PE_RECOGNITION_EXPENSES_MNK:
                    $row->service_description = 'Reconocimiento de gastos';
                    $row->id_service = $row->pe_recognition_expenses->id;
                    break;
                case \App\Service::SERVICE_REINTEGRATE_MNK:
                    $row->service_description = 'Reintegro';
                    $row->id_service = $row->reintegrate->id;
                    break;
                default:
                    $row->service_description = '';
                    $row->id_service = '';
                    $row->days = '';
                    break;
            }

            $endDate = $row->peItSort->inabilitySort->lastFraction->end_date_it_fraction ?? '';
            $row->date_payment = $endDate ? Carbon::parse($endDate)->next(Carbon::WEDNESDAY)->format('d-m-Y') : '';
            if ($row->state_id == StatePeItSort::EN_PAGO_DE_FRACCIONES) {

                $peitInabilitySort = PeitInabilitySort::where('pe_it_sort_id', $row->pe_it_sort_id)->first();
                if($peitInabilitySort){

                    //trae el registro que este entre las fechas de consulta o en caso contrario trae el ultimo registro generado
                    $fraction = DB::table('peit_fraction_sorts')
                        ->whereRaw('CURRENT_DATE BETWEEN start_date_it_fraction AND end_date_it_fraction')
                        ->where('peit_inability_sort_id', $peitInabilitySort->id)
                        ->unionAll(
                            DB::table('peit_fraction_sorts')
                                ->where('peit_inability_sort_id', $peitInabilitySort->id)
                                ->orderByDesc('id')
                                ->limit(1)
                        )
                        ->first();

                    $row->start_date_it_fraction = $fraction ? $fraction->start_date_it_fraction : null;
                    $row->end_date_it_fraction = $fraction ? $fraction->end_date_it_fraction : null;
                }

            } else {
                $row->start_date_it_fraction = null;
                $row->end_date_it_fraction = null;
            }

            //traer el gis segun el servicio y asignarlo al row
            $service = $row->service_id;
            $fromParent = [
                Service::SERVICE_PE_MPT_SORT_MNK,
                Service::SERVICE_PE_RECOGNITION_EXPENSES_MNK,
                Service::SERVICE_REINTEGRATE_MNK,
            ];
            $fromGrandparent = [
                Service::SERVICE_PE_IT_SORT_MNK,
                Service::SERVICE_PE_IP_SORT_MNK,
            ];
            if (in_array($service, $fromParent)) {
                $activityGisService = $row->parent;
            } elseif (in_array($service, $fromGrandparent)) {
                $activityGisService = $row->parent->parent;
            } else {
                $activityGisService = null;
            }
            if ($activityGisService && $activityGisService->gis_sort) {
                $row->consecutive_gis = $activityGisService->gis_sort->consecutive_gis ?? '';
                $row->aviso = $activityGisService->gis_sort->formatCaseNumberIfReported();
            } else {
                $row->consecutive_gis = '';
                $row->aviso = '';
            }

        }

        return view('table.economic_benefits.economic_benefits', [
            'activity' => $activity,
            'gisSort' => $gisSort,
            'id' => $id
        ]);

    }

    public function lastPayment($id)
    {
        $transactionDate = Activity::select('policy_sort_collections.transaction_date')
            ->leftJoin('policy_sort_collections', 'policy_sort_collections.activity_id', '=', 'activities.id')
            ->where('policy_sort_collections.payment_status', 'approved')
            ->where('activities.parent_id', $id)
            ->orderBy('policy_sort_collections.transaction_date', 'desc')
            ->limit(1)
            ->first();

        return $transactionDate ? $transactionDate->transaction_date : null;
    }

    public function registerDisability($cpath,$id)
    {

        $activity = Activity::where('affiliate_id', $id)
            ->where('service_id', Service::SERVICE_GIS_SORT_MNK)
            ->whereRaw('(
                SELECT COUNT(*) 
                FROM activity_actions aa 
                WHERE aa.new_state_id = 160 AND aa.activity_id = activities.id
            ) > 0')
            ->orderBy('created_at', 'desc')
            ->with(['gis_sort','affiliate'])
            ->get();

        if ($activity->isEmpty()) {
            return redirect()->back()->with('error', 'No cuenta con reportes de accidentes o registros GIS');
        }

        $activitys = $activity->first();
        $affiliate = $activitys->affiliate;

        $gisSort = GisSort::where('activity_id', $activitys->id)->first();

        $economicBenefit = EconomicBenefit::where('affiliate_id', $id)
            ->where('request_type','file_disability')
            ->where('state','pending')
            ->orderBy('id', 'desc')->first();

        $data_affiliate = [
            'worker_email' => ($economicBenefit->worker_email ?? '') == '' ? ($gisSort->email_affiliate ?? '') : $economicBenefit->worker_email,
            'worker_phone' => ($economicBenefit->worker_phone ?? '') == '' ? ($gisSort->cellphone_affiliate ?? '') : $economicBenefit->worker_phone,
            'worker_address' => ($economicBenefit->worker_address ?? '') == '' ?  ($gisSort->other_signs ?? '') : $economicBenefit->worker_address,
            'province' => ($economicBenefit->province ?? '') == '' ?  ($affiliate->province ?? '') : $economicBenefit->province,
            'canton' => ($economicBenefit->canton ?? '') == '' ?  ($affiliate->canton ?? '') : $economicBenefit->canton,
            'district' => ($economicBenefit->district ?? '') == '' ?  ($affiliate->district ?? '') : $economicBenefit->district
        ];


        return view('table.economic_benefits.affiliate_information_disability', [
            'activity' => $activity,
            'economicBenefit' => $economicBenefit,
            'id' => $id,
            'affiliate' =>$affiliate,
            'gisSort' =>$gisSort,
            'data_affiliate' => $data_affiliate
        ]);
    }

    public function saveRegisterDisability(Request $request, $cpath,$id)
    {

        $activity = Activity::where('affiliate_id', $id)
            ->where('service_id', Service::SERVICE_GIS_SORT_MNK)
            ->orderBy('id', 'desc')
            ->first();

        if (!$activity) {
            return redirect()->back()->withErrors('Actividad no encontrada.');
        }

        $gisSort = GisSort::where('activity_id', $activity->id)->first();

        if (!$gisSort) {
            return redirect()->back()->withErrors('Datos de GisSort no encontrados.');
        }

        $economicBenefit = EconomicBenefit::where('activity_id', $request->case)
            ->where('request_type','file_disability')
            ->orderBy('id', 'desc')->first();

        if(!$economicBenefit){
            $economicBenefit = new EconomicBenefit();
        }

        $economicBenefit->activity_id = $request->case;
        $economicBenefit->identification_type = $request->identification_titular;
        $economicBenefit->identification_number = $request->identification_number;
        $economicBenefit->worker_name = $request->worker_name;
        $economicBenefit->worker_email = $request->worker_email;
        $economicBenefit->worker_phone = $request->worker_phone;
        $economicBenefit->worker_address = $request->worker_address;
        $economicBenefit->province = $request->province;
        $economicBenefit->canton = $request->canton;
        $economicBenefit->district = $request->district;
        $economicBenefit->type_of_disability = $request->incapacity_type;
        $economicBenefit->type_of_permanent_disability = $request->permanent_incapacity_type;

        if (!empty($request->start_date_incapacity_submit)) {
            $economicBenefit->start_date = $request->start_date_incapacity_submit;
        }
        if (!empty($request->end_date_incapacity_submit)) {
            $economicBenefit->end_date = $request->end_date_incapacity_submit;
        }

        $economicBenefit->disability_days = $request->incapacity_days;
        $economicBenefit->request_description = $request->request_description;
        $economicBenefit->request_type = 'file_disability'; //expense_recognition
        $economicBenefit->affiliate_id = $id;
        $economicBenefit->state = 'pending';
        $economicBenefit->save();

        return view('table.economic_benefits.supports_disability', [
            'id' => $id,
            'gisSort' => $gisSort,
            'case' => $request->case,
            'incapacity_type' => $request->incapacity_type
        ]);

    }

    public function saveSupportsDisability(Request $request, $cpath, $id)
    {
        DB::beginTransaction();
        try {

            $activity = Activity::where('id', $request->case)->first();

            if (!$activity) {
                return redirect()->back()->withErrors('Actividad no encontrada.');
            }

            $gisSort = GisSort::where('activity_id', $request->case)->first();

            if (!$gisSort) {
                return redirect()->back()->withErrors('Datos de GisSort no encontrados.');
            }

            $economicBenefit = EconomicBenefit::where('activity_id', $request->case)
                ->where('state','pending')
                ->orderBy('id', 'desc')->first();


            if ($economicBenefit->type_of_disability == 'permanent') {
                $activity_pe_ip = new Activity();
                $activity_pe_ip->parent_id = $gisSort->activity_id;
                $activity_pe_ip->client_id = $activity->client_id;
                $activity_pe_ip->service_id = Service::SERVICE_PE_IP_SORT_MNK;
                $activity_pe_ip->affiliate_id = $activity->affiliate_id;
                $activity_pe_ip->user_id = Auth::id();
                $activity_pe_ip->state_id = State::REGISTRADO;
                $activity_pe_ip->save();

                $type = '';
                if ($economicBenefit->type_of_permanent_disability == 'minor_permanent') {
                    $type = 'Incapacidad menor permanente';
                } elseif ($economicBenefit->type_of_permanent_disability == 'partial_permanent') {
                    $type = 'Incapacidad parcial permanente';
                } elseif ($economicBenefit->type_of_permanent_disability == 'total_permanent') {
                    $type = 'Incapacidad total permanente';
                } elseif ($economicBenefit->type_of_permanent_disability == 'severe_disability') {
                    $type = 'Gran invalidez';
                }


                $peItSort=PeIpSort::create([
                    'activity_id' => $activity_pe_ip->id,
                    'affiliate_doc_type' =>$economicBenefit->identification_type,
                    'affiliate_doc_number' =>$economicBenefit->identification_number,
                    'affiliate_name' =>$economicBenefit->worker_name,
                    'affiliate_email' =>$economicBenefit->worker_email,
                    'casedata_type_disability' =>$type,
                ]);

                $controller = new PeIpSortController();
                $controller->registerIpManually($request, $cpath,$activity_pe_ip->id );

                if ($request->hasFile('clinical_history')) {
                    $this->storeActivityDocument($request, 'clinical_history', 272, $activity_pe_ip->id);
                }
                if ($request->hasFile('file_pcg')) {
                    $this->storeActivityDocument($request, 'file_pcg', 273, $activity_pe_ip->id);
                }

            } else {
                //Proceso para acción REGISTRAR IT MANUAL

                $description = 'REGISTRAR IT MANUAL';
                //aperturar servicio PE IT-SORT
                $activity_pe_sit = new Activity();
                $activity_pe_sit->parent_id = $gisSort->activity_id;
                $activity_pe_sit->client_id = $activity->client_id;
                $activity_pe_sit->service_id = Service::SERVICE_PE_IT_SORT_MNK;
                $activity_pe_sit->affiliate_id = $activity->affiliate_id;
                $activity_pe_sit->user_id = Auth::id();
                $activity_pe_sit->state_id = State::REGISTRADO;
                $activity_pe_sit->save();

                //Copiar los datos al formulario

                $peItSort=PeItSort::create([
                    'activity_id' => $activity_pe_sit->id,
                    'province' =>$economicBenefit->province,
                    'canton' =>$economicBenefit->canton,
                    'district' => $economicBenefit->district,
                    'day_1' =>$economicBenefit->start_date,
                    'number_ide_pe' => $economicBenefit->identification_number,
                    'name_affiliated_pe' => $economicBenefit->worker_name,
                    'email_affiliate_pe' => $economicBenefit->worker_email
                ]);

                PeitInabilitySort::create([
                    'pe_it_sort_id' => $peItSort->id,
                    'start_date' => $economicBenefit->start_date,
                    'end_date' => $economicBenefit->end_date,
                    'days_it' => $economicBenefit->disability_days,
                ]);

                //registrar actividad
                ActionController::create(
                    $activity_pe_sit->id,
                    ActionPeItSort::REGISTRAR_IT_MANUAL,
                    $description
                );

                if ($request->hasFile('file_disability')) {
                    $this->storeActivityDocument($request, 'file_disability', 261, $activity_pe_sit->id);
                }

//                $activityDocument = ActivityDocument::where('activity_id', $activity->id)
//                    ->where('document_id',258)
//                    ->first();
//
//                $activityDocumentPeItSort = new ActivityDocument();
//                $activityDocumentPeItSort->document_id = 261;
//                $activityDocumentPeItSort->activity_id = $activity_pe_sit->id;
//                $activityDocumentPeItSort->path = $activityDocument->path;
//                $activityDocumentPeItSort->save();

            }

            $economicBenefit->state = 'approved';
            $economicBenefit->save();

            DB::commit();

            return view('table.affiliate', [
                'id' => $id,
                'gisSort' => $gisSort,
                'success' => 'Incapacidad radicada.'
            ]);


        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()->withErrors('Ocurrió un error: ' . $e->getMessage());
        }

    }

    public function recognizeExpenses($cpath,$id)
    {
        $activity = Activity::where('affiliate_id', $id)
            ->where('service_id', Service::SERVICE_GIS_SORT_MNK)
            ->whereRaw('(
                SELECT COUNT(*)
                FROM activity_actions aa
                WHERE aa.new_state_id = 160 AND aa.activity_id = activities.id
            ) > 0')
            ->orderBy('created_at', 'desc')
            ->with(['gis_sort','affiliate'])
            ->get();

        if ($activity->isEmpty()) {
            return redirect()->back()->with('error', 'No cuenta con reportes de accidentes o registros GIS');
        }

        $activitys = $activity->first();
        $affiliate = $activitys->affiliate;

        $gisSort = GisSort::where('activity_id', $activitys->id)->first();

        $economicBenefit = EconomicBenefit::where('affiliate_id', $id)
            ->where('request_type','expense_recognition')
            ->where('state','pending')
            ->orderBy('id', 'desc')
            ->first();

        $data_affiliate = [
            'worker_email' => ($economicBenefit->worker_email ?? '') == '' ? ($gisSort->email_affiliate ?? '') : $economicBenefit->worker_email,
            'worker_phone' => ($economicBenefit->worker_phone ?? '') == '' ? ($gisSort->cellphone_affiliate ?? '') : $economicBenefit->worker_phone,
            'worker_address' => ($economicBenefit->worker_address ?? '') == '' ?  ($activitys->affiliate->employer_address ?? '') : $economicBenefit->worker_address,
            'province' => ($economicBenefit->province ?? '') == '' ?  ($affiliate->province ?? '') : $economicBenefit->province,
            'canton' => ($economicBenefit->canton ?? '') == '' ?  ($affiliate->canton ?? '') : $economicBenefit->canton,
            'district' => ($economicBenefit->district ?? '') == '' ?  ($affiliate->district ?? '') : $economicBenefit->district
        ];

        return view('table.economic_benefits.affiliate_information_expenses', [
            'activity' => $activity,
            'economicBenefit' => $economicBenefit,
            'id' => $id,
            'affiliate' =>$affiliate,
            'gisSort' => $gisSort,
            'data_affiliate' => $data_affiliate
        ]);
    }

    public function saveRecognizeExpenses(Request $request, $cpath,$id)
    {

        $activity = Activity::where('affiliate_id', $id)
            ->where('service_id', Service::SERVICE_GIS_SORT_MNK)
            ->orderBy('id', 'desc')
            ->first();

        if (!$activity) {
            return redirect()->back()->withErrors('Actividad no encontrada.');
        }

        $gisSort = GisSort::where('activity_id', $activity->id)->first();

        if (!$gisSort) {
            return redirect()->back()->withErrors('Datos de GisSort no encontrados.');
        }

        $economicBenefit = EconomicBenefit::where('activity_id', $request->case)
            ->where('request_type','expense_recognition')
            ->orderBy('id', 'desc')
            ->first();

        if(!$economicBenefit){
            $economicBenefit = new EconomicBenefit();
        }

        $economicBenefit->activity_id = $request->case;
        $economicBenefit->identification_type = $request->identification_titular;
        $economicBenefit->identification_number = $request->identification_number;
        $economicBenefit->worker_name = $request->worker_name;
        $economicBenefit->worker_email = $request->worker_email;
        $economicBenefit->worker_phone = $request->worker_phone;
        $economicBenefit->worker_address = $request->worker_address;
        $economicBenefit->province = $request->province;
        $economicBenefit->canton = $request->canton;
        $economicBenefit->district = $request->district;
        $economicBenefit->recognition_type = $request->recognition_type;
        $economicBenefit->invoice_concept = $request->invoice_concept;
        $economicBenefit->request_description = $request->request_description;
        $economicBenefit->request_type = 'expense_recognition';
        $economicBenefit->affiliate_id = $id;
        $economicBenefit->state = 'pending';
        $economicBenefit->iban_account = $request->iban_account;

        if (isset($request->authorization)) {
            $economicBenefit->authorization = $request->authorization;
        }

        $economicBenefit->save();

        if ($request->recognition_type == 'refund') {
            return view('table.economic_benefits.supports_expenses_refund', [
                'id' => $id,
                'economicBenefitId' => $economicBenefit->id,
                'gisSort' => $gisSort
            ]);
        } else {

            return view('table.economic_benefits.supports_expenses', [
                'id' => $id,
                'type_doc' => $request->input('recognition_type'),
                'gisSort' => $gisSort,
                'case' => $request->case
            ]);
        }


    }


    public function saveRecognizeExpensesRefund_old(Request $request, $cpath, $id)
    {
        $reintegrateServicesController = new ReintegrateServicesController();
        $response = $reintegrateServicesController->generateServiceReintegration($request, $cpath);

        if ($response->getStatusCode() === 200) {

            $data = json_decode($response->getContent(), true);
            $message = $data['message'];
            $newActivityId = $data['id'];

            return redirect('/servicio/'.$newActivityId);

        } else if ($response->getStatusCode() === 400) {

            $data = json_decode($response->getContent(), true);
            $message = $data['message'];

            return redirect('tablero/afiliado/'.$id.'/reconocimiento_gastos')->with('error', $message);
        } else {
            return redirect('tablero/afiliado/' . $id . '/reconocimiento_gastos')->with('error', 'Ocurrió un error al generar el servicio de reintegros');
        }

    }

    public function saveRecognizeExpensesRefund(Request $request, $cpath, $id)
    {
        try {
            $reintegrateServicesController = new ReintegrateServicesController();
            $response = $reintegrateServicesController->generateServiceReintegration($request, $cpath, $id);

            $statusCode = $response->getStatusCode();
            $data = json_decode($response->getContent(), true);

            if ($statusCode === 200) {
                return response()->json([
                    'success' => true,
                    'message' => $data['message'] ?? 'Servicio de reintegro generado correctamente',
                    'id' => $data['id'] ?? null,
                    'redirect' => '/tablero/afiliado/'.$id.'/prestaciones_economicas'
                ]);
            } else if ($statusCode === 400) {
                return response()->json([
                    'success' => false,
                    'message' => $data['message'] ?? 'Error en los datos proporcionados',
                    'redirect' => '/tablero/afiliado/' . $id . '/reconocimiento_gastos'
                ], 400);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Ocurrió un error al generar el servicio de reintegros',
                    'redirect' => '/tablero/afiliado/' . $id . '/reconocimiento_gastos'
                ], 500);
            }
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error inesperado: ' . $e->getMessage(),
                'redirect' => '/tomador/poliza/' . $id . '/reconocimiento_gastos'
            ], 500);
        }
    }

    public function saveSupportsExpenses(Request $request, $cpath,$id)
    {

        DB::beginTransaction();
        try {

            $activity = Activity::where('id', $request->case)
                ->first();

            if (!$activity) {
                return redirect()->back()->withErrors('Actividad no encontrada.');
            }

            $gisSort = GisSort::where('activity_id', $request->case)->first();

            if (!$gisSort) {
                return redirect()->back()->withErrors('Datos de GisSort no encontrados.');
            }

            $economicBenefit = EconomicBenefit::where('activity_id', $request->case)
                ->where('state','pending')
                ->orderBy('id', 'desc')->first();

            if ($economicBenefit->recognition_type == 'invoices'){

                $controller = new PeRecognitionExpensesController();
                $response =  $controller->reportExpenseRecognitionInvoices($cpath, $request->case);

                if ($response->getStatusCode() === 200) {

                    $data = json_decode($response->getContent(), true);
                    $message = $data['message'];
                    $newActivityId = $data['id'];

                    if ($request->hasFile('file_reimbursement_request')) {
                        $this->storeActivityDocument($request, 'file_reimbursement_request', 252, $newActivityId);
                    }

                    if ($request->hasFile('file_medical_bills')) {
                        $this->storeActivityDocument($request, 'file_medical_bills', 253, $newActivityId);
                    }

                }

            } else if ($economicBenefit->recognition_type == 'transfer_accommodation'){

                $controller = new PeRecognitionExpensesController();
                $response = $controller->recognitionTransferAccommodationAndFood($cpath, $request->case);

                if ($response->getStatusCode() === 200) {

                    $data = json_decode($response->getContent(), true);
                    $message = $data['message'];
                    $newActivityId = $data['id'];

                    if ($request->hasFile('file_travel_invoices')) {
                        $this->storeActivityDocument($request, 'file_travel_invoices', 251, $newActivityId);
                    }
                }

            } else if ($economicBenefit->recognition_type == 'funeral_transfer'){
                $controller = new PeRecognitionExpensesController();
                $response = $controller->reportFuneralAndTransportExpenses($request, $cpath, $request->case);

                if ($response->getStatusCode() === 200) {

                    $data = json_decode($response->getContent(), true);
                    $message = $data['message'];
                    $newActivityId = $data['id'];

                    if ($request->hasFile('file_death_certificate')) {
                        $this->storeActivityDocument($request, 'file_death_certificate', 249, $newActivityId);
                    }

                    if ($request->hasFile('file_electronic_invoice')) {
                        $this->storeActivityDocument($request, 'file_electronic_invoice', 250, $newActivityId);
                    }

                }

            }

            $economicBenefit->state = 'approved';
            $economicBenefit->save();

            DB::commit();

            return view('table.affiliate', [
                'id' => $id,
                'gisSort' => $gisSort,
                'success' => 'Reconocimiento de gastos realizado.'
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()->withErrors('Ocurrió un error: ' . $e->getMessage());
        }

    }


    protected function storeActivityDocument(Request $req, $fileKey, $documentId, $id)
    {
        $file = $req->file($fileKey);

        if ($file) {

            $originalExtension = $req->file($fileKey)->getClientOriginalExtension();
            $uniqueName = Str::random(10) . uniqid() . '.' . $originalExtension;
            $filePath = "documents/{$uniqueName}";

            $req->file($fileKey)->storeAs('documents', $uniqueName, 's3');

            $activityDocument = new ActivityDocument();
            $activityDocument->document_id = $documentId;
            $activityDocument->activity_id = $id;
            $activityDocument->path = $filePath;
            $activityDocument->uploaded_at = Carbon::now();
            $activityDocument->save();

        }
    }


    public function membershipCertificate($cpath,$id)
    {
        $activity = Activity::where('affiliate_id', $id)
            ->where('service_id',Service::SERVICE_GIS_SORT_MNK)
            ->orderBy('id', 'desc')->first();

        $gisSort = GisSort::where('activity_id', $activity->id)->first();

        return view('table.certificates.membership_certificate', [
            'gisSort' => $gisSort,
            'id' =>$id
        ]);
    }

    public function generateAffiliationCertificate(Request $request, $cpath,$id)
    {
        $activity = Activity::where('affiliate_id', $id)
            ->where('service_id',Service::SERVICE_GIS_SORT_MNK)->first();

        $policySpreadsheetAffiliates = PolicySpreadsheetAffiliate::where('affiliate_id', $id)->first();

        PolicySortController::affiliateCertificate($request, $activity->id, $policySpreadsheetAffiliates, '');

        return redirect()->back()->with('success', 'Certificado de afiliación generado.');

    }

    public function secureCertificate($cpath,$id)
    {
        $activity = Activity::where('affiliate_id', $id)
            ->where('service_id',Service::SERVICE_GIS_SORT_MNK)
            ->orderBy('id', 'desc')->first();

        $gisSort = GisSort::where('activity_id', $activity->id)->first();

        return view('table.certificates.secure_certificate', [
            'gisSort' => $gisSort,
            'id' =>$id
        ]);
    }

    public function formatonuevo($cpath, $id)
    {

        $activity = Activity::where('affiliate_id', $id)
            ->where('service_id',Service::SERVICE_GIS_SORT_MNK)
            ->orderBy('id', 'desc')->first();

        $activityTomador = Activity::where('parent_id', $activity->parent_id)
            ->orderBy('id', 'desc')->first();

        $activityPlanilla = Activity::where('parent_id', $activityTomador->parent_id)
            ->where('service_id',Service::SERVICE_REPORT_TAKEN_FORM_MNK)
            ->orderBy('id', 'desc')->first();

        $policyAffiliate = DB::table('activities AS a')
            ->leftJoin('policy_spreadsheets AS p', 'p.activity_id', '=', 'a.id')
            ->leftJoin('policy_spreadsheet_affiliates AS pa', 'pa.policy_spreadsheet_id', '=', 'p.id')
            ->select('pa.*')
            ->where('a.parent_id', '=', $activityTomador->parent_id)
            ->where('a.service_id', '=', Service::SERVICE_REPORT_TAKEN_FORM_MNK)
            ->limit(1)
            ->first();

        //$policySpreadsheet = PolicySpreadsheet::where('activity_id', $activityPlanilla->id)->first();
//        $policySpreadsheetAffiliate = PolicySpreadsheetAffiliate::where('policy_spreadsheet_id', $policySpreadsheet->id)
//            ->where('affiliate_id', $id)
//            ->orderBy('id', 'desc')->first();

        $datos = [
            'activity' => $activity,
            'activityTomador' => $activityTomador,
            'policyAffiliate' => $policyAffiliate
        ];

        //AFFILIATION_CERTIFICATE

        $pdf = PDF::loadView("table.certificates.docs.affiliation_certificate_pdf", $datos);
        return $pdf->stream('certificadSeguro.pdf');

//        return view("table.certificates.docs.affiliation_certificate_pdf", $datos);
//        $pdf = PDF::loadView("table.certificates.docs.affiliation_certificate_pdf", $datos);
//        return $pdf->download('certificadSeguro.pdf');
    }

    public function downloadDocumentAffiliate(Request $req, $cpath, $id)
    {

        try{

            $client = Client::query()->where('path', $cpath)->firstOrFail();

            $activity = Activity::where('affiliate_id', $id)
                ->where('service_id',Service::SERVICE_GIS_SORT_MNK)
                ->orderBy('id', 'desc')->first();

            if (!$activity) {
                throw new Exception('No se encontró actividad para el afiliado.');
            }

            $activityTomador = Activity::where('parent_id', $activity->parent_id)
                ->orderBy('id', 'desc')->first();

            if (!$activityTomador) {
                throw new Exception('No se encontró actividad tomador.');
            }

            $activityPlanilla = Activity::where('parent_id', $activityTomador->parent_id)
                ->where('service_id',Service::SERVICE_REPORT_TAKEN_FORM_MNK)
                ->orderBy('id', 'desc')->first();

            if (!$activityPlanilla) {
                throw new Exception('No se encontró actividad de planilla.');
            }

            $policyAffiliate = DB::table('activities AS a')
                ->leftJoin('policy_spreadsheets AS p', 'p.activity_id', '=', 'a.id')
                ->leftJoin('policy_spreadsheet_affiliates AS pa', 'pa.policy_spreadsheet_id', '=', 'p.id')
                ->select('pa.*')
                ->where('a.parent_id', '=', $activityTomador->parent_id)
                ->where('a.service_id', '=', Service::SERVICE_REPORT_TAKEN_FORM_MNK)
                ->limit(1)
                ->first();

            if (!$policyAffiliate) {
                throw new Exception('No se encontró información de afiliación.');
            }

            $datos = [
                'activity' => $activity,
                'activityTomador' => $activityTomador,
                'policyAffiliate' => $policyAffiliate
            ];

            $pdf = PDF::loadView("table.certificates.docs.affiliation_certificate_pdf", $datos);

            $document = 'Certificado_afiliado';
            $fileName = "{$document}.pdf";

            $filePath = "activity_action_document/{$fileName}";
            Storage::disk('s3')->put($filePath, $pdf->output());

            $activityNew = new Activity();
            $activityNew->client_id = $client->id;
            $activityNew->affiliate_id = $id;
            $activityNew->service_id = Service::SERVICE_AFFILIATE_WORKFORCE_REPORT_MNK;
            $activityNew->state_id = State::REGISTRADO;
            $activityNew->parent_id = $activityPlanilla->id;
            $activityNew->user_id = Auth::id();
            $activityNew->save();

            $activityAction = ActionController::create($activityNew->id, Action::GENERAR_CERTIFICADO_AFILIADO,  'Certificado de afiliado');

            $activityActionDocument = new ActivityActionDocument();
            $activityActionDocument->activity_action_id = $activityAction->id;
            $activityActionDocument->name = $document;
            $activityActionDocument->path = $filePath;
            $activityActionDocument->save();

            return $pdf->stream('certificadAfiliacion.pdf');

        } catch (Exception $e) {
            DB::rollback();

            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ], 500);
        }
    }
    public function emailDocumentAffiliate(Request $req, $cpath, $id)
    {

        DB::beginTransaction();
        try {

            $client = Client::query()->where('path', $cpath)->firstOrFail();

            $activity = Activity::where('affiliate_id', $id)
                ->where('service_id',Service::SERVICE_GIS_SORT_MNK)
                ->orderBy('id', 'desc')->first();

            $activityTomador = Activity::where('parent_id', $activity->parent_id)
                ->orderBy('id', 'desc')->first();

            $activityPlanilla = Activity::where('parent_id', $activityTomador->parent_id)
                ->where('service_id',Service::SERVICE_REPORT_TAKEN_FORM_MNK)
                ->orderBy('id', 'desc')->first();

            $policyAffiliate = DB::table('activities AS a')
                ->leftJoin('policy_spreadsheets AS p', 'p.activity_id', '=', 'a.id')
                ->leftJoin('policy_spreadsheet_affiliates AS pa', 'pa.policy_spreadsheet_id', '=', 'p.id')
                ->select('pa.*')
                ->where('a.parent_id', '=', $activityTomador->parent_id)
                ->where('a.service_id', '=', Service::SERVICE_REPORT_TAKEN_FORM_MNK)
                ->limit(1)
                ->first();

            $subject = 'Certificado de afiliado';

            $datos = [
                'activity' => $activity,
                'activityTomador' => $activityTomador,
                'policyAffiliate' => $policyAffiliate
            ];

            $pdf = PDF::loadView("table.certificates.docs.affiliation_certificate_pdf", $datos);

            $time = date('Ymd_His');
            $document = 'Certificado_afiliado_'.$time;
            $fileName = "{$document}.pdf";

            $filePath = "activity_action_document/{$fileName}";
            Storage::disk('s3')->put($filePath, $pdf->output());

            $activityNew = new Activity();
            $activityNew->client_id = $client->id;
            $activityNew->affiliate_id = $id;
            $activityNew->service_id = Service::SERVICE_AFFILIATE_WORKFORCE_REPORT_MNK;
            $activityNew->state_id = State::REGISTRADO;
            $activityNew->parent_id = $activityPlanilla->id;
            $activityNew->user_id = Auth::id();
            $activityNew->save();

            $activityAction = ActionController::create($activityNew->id, Action::GENERAR_CERTIFICADO_AFILIADO,  'Certificado de afiliado');

            $activityActionDocument = new ActivityActionDocument();
            $activityActionDocument->activity_action_id = $activityAction->id;
            $activityActionDocument->name = $document;
            $activityActionDocument->path = $filePath;
            $activityActionDocument->save();

            $emailIntermediary = $activityTomador->parent_activity->policy_sort->email;
            $emailTaker = $activityTomador->affiliate->email;

            $emails = array_filter([$emailIntermediary, $emailTaker], function ($email) {
                return !empty($email);
            });

            $text = [
                "text" => "¡Buen día, <b>".ucwords(mb_strtolower($activity->affiliate->full_name ?? ''))."</b>!
                           
                           Estimado cliente, adjunto encontrará el certificado de afiliación",
                "sender" => 'MNK Seguros'
            ];
            $attachments = [
                [
                    'path' => $filePath,
                    'name' => basename($filePath),
                    'type' => 'PDF'
                ]
            ];

            $this->sendEmail($emails, $subject, $text, $attachments, $client, $activityTomador->parent_activity->policy_sort->activity_id, $activityPlanilla, $activityTomador->parent_activity->policy_sort);

            DB::commit();

            return response()->json([
                'status' => 'success',
                'message' => 'Registro creado y email enviado exitosamente',
                'emails' => $emailIntermediary
            ]);

        } catch (Exception $e) {
            DB::rollback();
            return response()->json([
                'status' => 'error',
                'message' => 'Ocurrió un error al enviar el correo.'
            ], 500);
        }

    }

    //ACCION GENERAR CERTIFICADO DE SEGURO
    public function downloadDocumentSecure(Request $req, $cpath, $id)
    {

        try {

            $activity = DB::table('activities AS a')
                ->leftJoin('policy_sorts AS p', 'p.activity_id', '=', 'a.parent_id')
                ->leftJoin('affiliates AS af', 'af.id', '=', 'a.affiliate_id')
                ->leftJoin('activities AS ab', 'ab.id', '=', 'a.parent_id')
                ->leftJoin('affiliates AS afb', 'afb.id', '=', 'ab.affiliate_id')
                ->select(
                    'afb.first_name AS nombre_tomador',
                    'afb.doc_number AS id_tomador',
                    'af.full_name AS nombre_trabajador',
                    'af.doc_number AS id_trabajador',
                    'p.validity_from',
                    'p.validity_to',
                    'p.created_at',
                    'p.id',
                    'a.parent_id'
                )
                ->where('a.affiliate_id', $id)
                ->where('a.service_id', Service::SERVICE_GIS_SORT_MNK)
                ->first();


            $policy_sort = PolicySort::where('id', $activity->id)->first();
            $client = Client::query()->where('path', $cpath)->firstOrFail();
            $affiliate = Affiliate::where('id', $id)->first();

            //Consultar planilla en la cual se encuentra el afiliado
            $spreadSheetAffiliate = PolicySpreadsheetAffiliate::where('affiliate_id', $affiliate->id)
                ->orderBy('id', 'desc')
                ->pluck('policy_spreadsheet_id')
                ->first();

            if (!$spreadSheetAffiliate) {
                throw new Exception('No se encontró planilla para el afiliado.', 404);
            }
            //consultamos la ultima planilla a la cual pertenece el afiliado
            $spreadSheet = PolicySpreadsheet::where('id', $spreadSheetAffiliate)
                ->orderBy('id', 'desc')
                ->first(['activity_id', 'created_at']);

            //consultamos todas las actividades de planilla de la poliza
            $ActivitySpreadsheet = Activity::where('service_id', Service::SERVICE_REPORT_TAKEN_FORM_MNK)
                ->where('parent_id', $policy_sort->activity_id)
                ->get();
            //Calcular el periodo al cual pertenece la planilla
            $position = $ActivitySpreadsheet->search(function ($item) use ($spreadSheet) {
                return $item->id === $spreadSheet;
            });
            switch ($position){
                case 0:
                    $month = $spreadSheet->created_at;
                    $date = Carbon::parse($month)->formatLocalized('%B');
                    $period = "Emisión";
                    break;
                default :
                    $month = $spreadSheet->created_at;
                    $date = Carbon::parse($month);
                    $previousDate = $date->copy()->subMonth();
                    $previousMonth = ucfirst($previousDate->formatLocalized('%B'));
                    $previousYear = $previousDate->year;
                    $period = $previousMonth . ' ' . $previousYear . " # -" . $position;
                    break;
            }
            $subject = 'Certificado de seguro';
            $actionId = ActionConstaciasSort::GENERAR_CERTIFICADO_SEGURO;

            $activityNew = new Activity();
            $activityNew->client_id = $client->id;
            $activityNew->affiliate_id = $id;
            $activityNew->service_id = Service::SERVICE_CONSTANCY_SORT_MNK;
            $activityNew->state_id = State::REGISTRADO;
            $activityNew->parent_id = $activity->parent_id;
            $activityNew->user_id = Auth::id();
            $activityNew->save();

            $constancy = new ConstancySort();
            $constancy->activity_id = $activityNew->id;
            $constancy->type = 'insurance_certificate';
            $constancy->save();

            $activityAction = ActionController::create($activityNew->id, $actionId,  'GENERAR CERTIFICADO DE SEGURO');

            date_default_timezone_set('America/Costa_Rica');
            $horaActual = new DateTime();
            $horaFormateada = $horaActual->format('H:i:s');
            $fechaActual =  ucfirst(strftime('%A %e de %B del %Y', strtotime($horaActual->format('Y-m-d'))));

            $datos = [
                'activity' => $activity,
                'watermark' => false,
                'horaFormateada' => $horaFormateada,
                'fechaActual' =>  $fechaActual,
                'policy_sort' => $policy_sort,
                'period' => $period
            ];

            $pdf = PDF::loadView("table.certificates.docs.secure_certificate_pdf", $datos);

            $document = 'secure_certificate';
            $fileName = "{$document}.pdf";

            $filePath = "activity_action_document/{$fileName}";
            Storage::disk('s3')->put($filePath, $pdf->output());

            $activityActionDocument = new ActivityActionDocument();
            $activityActionDocument->activity_action_id = $activityAction->id;
            $activityActionDocument->name = $document;
            $activityActionDocument->path = $filePath;
            $activityActionDocument->save();

            return $pdf->download('certificadSeguro.pdf');

        } catch (Exception $e) {

            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
            ], $e->getCode());
        }
    }

    //ACCION GENERAR CERTIFICADO DE SEGURO
    public function emailDocumentSecure(Request $req, $cpath, $id)
    {

        DB::beginTransaction();
        try {

            $activity = DB::table('activities AS a')
                ->leftJoin('policy_sorts AS p', 'p.activity_id', '=', 'a.parent_id')
                ->leftJoin('affiliates AS af', 'af.id', '=', 'a.affiliate_id')
                ->leftJoin('activities AS ab', 'ab.id', '=', 'a.parent_id')
                ->leftJoin('affiliates AS afb', 'afb.id', '=', 'ab.affiliate_id')
                ->select(
                    'afb.first_name AS nombre_tomador',
                    'afb.doc_number AS id_tomador',
                    'af.full_name AS nombre_trabajador',
                    'af.doc_number AS id_trabajador',
                    'p.validity_from',
                    'p.validity_to',
                    'p.created_at',
                    'p.id',
                    'afb.email as email_affiliate',
                    'p.email as email_policy',
                    'p.activity_id',
                    'a.parent_id'
                )
                ->where('a.affiliate_id', $id)
                ->where('a.service_id', Service::SERVICE_GIS_SORT_MNK)
                ->orderBy('id', 'desc')
                ->first();

            $policy_sort = PolicySort::where('id', $activity->id)->first();
            $client = Client::query()->where('path', $cpath)->firstOrFail();
            $affiliate = Affiliate::where('id', $policy_sort->activity->affiliate_id)->first();

            $subject = 'Constancia de aseguramiento';
            $actionId = ActionConstaciasSort::GENERAR_CERTIFICADO_SEGURO;

            $activityNew = new Activity();
            $activityNew->client_id = $client->id;
            $activityNew->affiliate_id = $id;
            $activityNew->service_id = Service::SERVICE_CONSTANCY_SORT_MNK;
            $activityNew->state_id = State::REGISTRADO;
            $activityNew->parent_id = $activity->parent_id;
            $activityNew->user_id = Auth::id();
            $activityNew->save();

            $constancy = new ConstancySort();
            $constancy->activity_id = $activityNew->id;
            $constancy->type = 'insurance_certificate';
            $constancy->save();

            $activityAction = ActionController::create($activityNew->id, $actionId,  'GENERAR CERTIFICADO DE SEGURO');


            date_default_timezone_set('America/Costa_Rica');
            $horaActual = new DateTime();
            $horaFormateada = $horaActual->format('H:i:s');
            $fechaActual =  ucfirst(strftime('%A %e de %B del %Y', strtotime($horaActual->format('Y-m-d'))));


            $datos = [
                'activity' => $activity,
                'watermark' => false,
                'horaFormateada' => $horaFormateada,
                'fechaActual' =>  $fechaActual
            ];

            $pdf = PDF::loadView("table.certificates.docs.secure_certificate_pdf", $datos);

            $document = 'constancia_de_aseguramiento';
            $fileName = "{$document}.pdf";

            $filePath = "activity_action_document/{$fileName}";
            Storage::disk('s3')->put($filePath, $pdf->output());

            $activityActionDocument = new ActivityActionDocument();
            $activityActionDocument->activity_action_id = $activityAction->id;
            $activityActionDocument->name = $document;
            $activityActionDocument->path = $filePath;
            $activityActionDocument->save();

            $emailIntermediary = $policy_sort->email;
            $emailTaker = $affiliate->email;

            $emails = array_filter([$emailIntermediary, $emailTaker], function ($email) {
                return !empty($email);
            });

            $emailBody = " ¡Buen día, ".$activity->nombre_trabajador."!
            
                           Nos complace adjuntarle su constancia de aseguramiento correspondiente a la póliza del Seguro Obligatorio de Riesgos del Trabajo ".$policy_sort->formatNumberConsecutive().", a nombre de ".$affiliate->full_name.".

                           Por favor, si tiene alguna duda o consulta al respecto, contáctenos al correo electrónico <EMAIL> o al teléfono 4102-7600. ¡Para nosotros será un gusto servirle!

                           Nuestro propósito es fortalecer la prevención en salud y seguridad laboral del país, así como proteger a sus colaboradores en el momento que más lo necesitan, generando siempre bienestar.
                        ";

            $text = [
                "text" => $emailBody,
                "sender" => 'MNK Seguros'
            ];
            $attachments = [
                [
                    'path' => $filePath,
                    'name' => basename($filePath),
                    'type' => 'PDF'
                ]
            ];

            $this->sendEmail($emails, $subject, $text, $attachments, $client, $policy_sort->activity_id, $activityNew, $policy_sort);

            DB::commit();

            return response()->json([
                'status' => 'success',
                'message' => 'Registro creado y email enviado exitosamente',
                'emails' => $emailIntermediary
            ]);

        } catch (Exception $e) {
            DB::rollback();
            return response()->json([
                'status' => 'error',
                'message' => 'Ocurrió un error al enviar el correo.'
            ], 500);
        }
    }

    public function updatesData($cpath,$id)
    {

        $activity = Activity::where('affiliate_id', $id)
            ->where('service_id',Service::SERVICE_GIS_SORT_MNK)
            ->orderBy('id', 'desc')->first();

        $gisSort = GisSort::where('activity_id', $activity->id)->first();

        return view('table.update_affiliate_data.affiliate_data', [
            'id' => $id,
            'gisSort' =>$gisSort,
            'activity' => $activity
        ]);
    }

    public function saveDataAfilliate(Request $request, $cpath, $id)
    {

        $activity = Activity::where('affiliate_id', $id)
            ->where('service_id', Service::SERVICE_GIS_SORT_MNK)
            ->orderBy('id', 'desc')
            ->first();

        if (!$activity) {
            return redirect()->back()->withErrors('Actividad no encontrada.');
        }

        $gisSort = GisSort::where('activity_id', $activity->id)->first();

        if (!$gisSort) {
            return redirect()->back()->withErrors('Datos de GisSort no encontrados.');
        }

        $affiliate = $activity->affiliate;

        if (!empty($request->marital_status)) {
            $affiliate->civil_status = $request->marital_status;
        }

        if (!empty($request->educational_level)) {
            $affiliate->school_level = $request->educational_level;
        }

        if (!empty($request->email_affiliate)) {
            $affiliate->email = $request->email_affiliate;
        }

        if (!empty($request->worker_address)) {
            //$affiliate->address = $request->worker_address;
            $affiliate->employer_address = $request->worker_address;
        }

        if (!empty($request->worker_iban_account_number)) {
            $affiliate->iban_account = $request->worker_iban_account_number;
        }

        if (!empty($request->nationality_affiliate)) {
            $affiliate->country = $request->nationality_affiliate;
        }

        if (!empty($request->province)) {
            $affiliate->province = $request->province;
        }

        if (!empty($request->canton)) {
            $affiliate->canton = $request->canton;
        }

        if (!empty($request->district)) {
            $affiliate->district = $request->district;
        }

        $affiliate->save();

        if (!empty($request->email_affiliate)) {
            $gisSort->email_affiliate = $request->email_affiliate;
            $gisSort->save();
        }


        return redirect()->back()->with('success', 'Datos actualizados correctamente.');

    }

    public function controversies ($cpath,$id)
    {

        $activity = Activity::where('service_id', Service::SERVICE_GIS_SORT_MNK)
            ->where('state_id', StateGis::CASO_CERRADO)
            ->where('affiliate_id', $id)
            ->with(['affiliate', 'gis_sort', 'state'])
            ->paginate(10);

        $activityAffiliate = Activity::where('affiliate_id', $id)
            ->where('service_id', Service::SERVICE_GIS_SORT_MNK)
            ->orderBy('id', 'desc')
            ->first();

        if (!$activityAffiliate) {
            return redirect()->back()->withErrors('Actividad no encontrada.');
        }

        $gisSort = GisSort::where('activity_id', $activityAffiliate->id)->first();

        return view('table.controversies.controversies', [
            'gisSort' => $gisSort,
            'activity' => $activity,
            'id' => $id
        ]);

    }

    public function registerControversies($cpath,$id, $activity_gis) {

        $activity = Activity::where('affiliate_id', $id)
            ->where('service_id', Service::SERVICE_GIS_SORT_MNK)
            ->orderBy('id', 'desc')
            ->first();

        if (!$activity) {
            return redirect()->back()->withErrors('Actividad no encontrada.');
        }

        $gisSort = GisSort::where('activity_id', $activity->id)->first();

        return view('table.controversies.register_controversies', [
            'gisSort' => $gisSort,
            'activity' => $activity,
            'activity_gis' => $activity_gis,
            'id' => $id
        ]);

    }

    public function saveControversies(Request $request, $cpath, $id, $activity_gis) {

        if ($request->controversy_reason == 'acuerdo_pcg') {
            $controller = new ComprehensiveAccidentManagementController();
            $response =  $controller->ReportPcgControversy($request, $activity_gis);

        } else {
            $controller = new ComprehensiveAccidentManagementController();
            $response =  $controller->ReportOriginControversy($request, $activity_gis);
        }

        $data = json_decode($response->getContent(), true);
        $message = $data['message'];

        if ($response->getStatusCode() === 200) {
            $case = sprintf('%04d',$data['case']);
            return redirect('/tablero/afiliado/'.$id.'/controversias')->with('success', 'Trámite judicial del caso '.$case.' solicitado con éxito');
        } else {
            return redirect()->back()->with('error', 'Error al solicitar trámite judicial');
        }

    }

    public function reopenings($cpath,$id){


        $activity = Activity::where('service_id', Service::SERVICE_GIS_SORT_MNK)
            ->where('state_id', StateGis::CASO_CERRADO)
            ->where('affiliate_id', $id)
            ->with(['affiliate', 'gis_sort', 'state'])
            ->paginate(10);

        $activityAffiliate = Activity::where('affiliate_id', $id)
            ->where('service_id', Service::SERVICE_GIS_SORT_MNK)
            ->orderBy('id', 'desc')
            ->first();

        if (!$activityAffiliate) {
            return redirect()->back()->withErrors('Actividad no encontrada.');
        }

        $gisSort = GisSort::where('activity_id', $activityAffiliate->id)->first();

        return view('table.reopenings.reopenings', [
            'gisSort' => $gisSort,
            'activity' => $activity,
            'id' => $id
        ]);
    }

    public function registerReopenings($cpath, $id, $activity_gis)
    {

        $activity = Activity::with(['gis_sort', 'affiliate', 'parent_activity'])->where('id', $activity_gis)->first();

        if (!$activity) {
            return redirect()->back()->withErrors('Actividad no encontrada.');
        }

        $alta_medica = $activity->activity_actions()
            ->where('action_id', ActionGisSort::REPORTAR_ALTA_MEDICA)->first();

        $activity_police = $activity->parent_activity;

        $poliza = PolicySort::where('activity_id', $activity_police->id)->first();

     
        return view('table.reopenings.register_reopenings', [
            'activity_gis' => $activity_gis,
            'data' => $activity,
            'data_poliza' => $poliza,
            'alta_medica' => $alta_medica,
            'id' => $id
        ]);

    }

    public function saveReopenings(Request $request, $cpath, $id, $activity_gis) {

        $controller = new ComprehensiveAccidentManagementController();
        $response =  $controller->ReportReopenings($request,$cpath, $activity_gis);

        $data = json_decode($response->getContent(), true);
        $message = $data['message'];

        if ($response->getStatusCode() === 200) {
            $case = sprintf('%04d',$data['case']);
            return redirect('/tablero/afiliado/'.$id.'/reaperturas')->with('success', 'Reapertura del caso '.$case.' solicitada con éxito');
        } else {
            return redirect()->back()->with('error', 'Error al solicitar reapertura del caso');
        }

    }

    private function sendEmail($emails, $subject, $text, $attachments, $client, $id, $activityAction, $policySort)
    {
        $mailSent = new SendDocumentDataBase(
            implode(',', $emails),         // Correos a enviar
            $subject,                      // Asunto del correo
            "<EMAIL>",            // Remitente
            $subject,                      // Asunto
            $text,                         // Cuerpo del email
            "<EMAIL>",  // Email de respuesta
            $attachments,                  // Archivos adjuntos
            "send_document_db",            // Tipo de envío
            $client,                       // Información del cliente
            request()->getHost(),          // Dominio
            $id,                           // ID de la actividad
            $activityAction->id,           // ID de la acción de la actividad
            $policySort->activity->service->id // ID del servicio
        );

        $mailSent->sendMail();
    }

    public function uploadEconimic(Request $request){

        DB::beginTransaction();
        try{
            $request->validate([
                'file' => 'required|mimes:pdf,doc,docx|max:5120' // Máx. 5MB
            ]);

            $serviceId = $request->input('service_id');
            $file = $request->file('file');

            if ($file) {
                $actionId = ActionPeItSort::REPORTAR_DOC_EXTERNOS;
                $activityAction = ActionController::create($serviceId, $actionId,  'Reportar documentos externos PE Subsidio por Incapacidad Temporal');

                $originalExtension = $request->file('file')->getClientOriginalExtension();
                $uniqueName = Str::random(10) . uniqid() . '.' . $originalExtension;
                $filePath = "activity_action_document/{$uniqueName}";

                $originalName = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
                $originalExtension = $file->getClientOriginalExtension();
                $cleanedName = Str::slug($originalName) . '.' . $originalExtension;

                $request->file('file')->storeAs('activity_action_document', $uniqueName, 's3');

                $activityActionDocument = new ActivityActionDocument();
                $activityActionDocument->activity_action_id = $activityAction->id;
                $activityActionDocument->name = $cleanedName;
                $activityActionDocument->path = $filePath;
                $activityActionDocument->save();

            }

            DB::commit();
            return response()->json(['message' => 'Archivo subido con éxito']);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json(['message' => 'Error al cargar archivo: '.$e]);
        }

    }

    //Guardar los documentos para el ajuste de la incapacidad temporal
    public function uploadDocumentInhability(Request $request){

        DB::beginTransaction();
        try{
            $request->validate([
                'file' => 'required|mimes:pdf,doc,docx|max:5120' // Máx. 5MB
            ]);

            $serviceId = $request->input('service_id');
            $peit_sort = PeItSort::where('activity_id', $serviceId)->first();
            $file = $request->file('file');

            if ($file) {

                $description = "REPORTAR SOLICITUD AJUSTE PAGO INCAPACIDAD TEMPORAL";
                $actionId = ActionPeItSort::REPORTAR_SOLICITUD_AJUSTE_PAGO_INCAPACIDAD_TEMPORAL;
                $activityAction = ActionController::create(
                    $serviceId, 
                    $actionId,  
                    $description
                );

                $originalExtension = $request->file('file')->getClientOriginalExtension();
                $uniqueName = Str::random(10) . uniqid() . '.' . $originalExtension;
                $filePath = "activity_action_document/{$uniqueName}";

                $originalName = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
                $originalExtension = $file->getClientOriginalExtension();
                $cleanedName = Str::slug($originalName) . '.' . $originalExtension;

                $request->file('file')->storeAs('activity_action_document', $uniqueName, 's3');

                $activityActionDocument = new ActivityActionDocument();
                $activityActionDocument->activity_action_id = $activityAction->id;
                $activityActionDocument->name = $cleanedName;
                $activityActionDocument->path = $filePath;
                $activityActionDocument->save();

                $peit_sort->update([
                    'disability_adjustment_document' => $filePath
                ]);

            }

            DB::commit();
            return response()->json(['message' => 'Archivo subido con éxito']);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json(['message' => 'Error al cargar archivo: '.$e]);
        }

    }

}