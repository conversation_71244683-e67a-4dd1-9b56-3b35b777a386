<?php

namespace App\Http\Controllers\Tables;

use App\Actions\ActionCotizacionSort;
use App\Actions\ActionGisSort;
use App\Actions\ActionPolizaSort;
use App\Activity;
use App\ActivityAction;
use App\Affiliate;
use App\Area;
use App\Client;
use App\Http\Controllers\ActionController;
use App\Http\Controllers\Controller;
use App\Http\Controllers\Integrations\IntegrationServiceController;
use App\Mail\SendDocumentDataBase;
use App\MailTemplates\Constants\Templates;
use App\MailTemplates\TemplateBuilder;
use App\PolicyContact;
use App\PolicySort;
use App\PolicySpreadsheet;
use App\PolicySpreadsheetAffiliate;
use App\Service;
use App\User;
use App\UserClient;
use Auth;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class DataUpdatesController extends Controller
{
    public function index(Request $request, $cpath)
    {
        // Obtener el usuario autenticado
        $user = Auth::user();

        if (!in_array($user->area_id, [Area::ADMINISTRATIVE, Area::ADMINISTRATIVE_MNK])) {
            return redirect()->back()->with('error', 'Usuario no autorizado.');
        }

        return view('table.data-updates', [
            'active' => ''
        ]);

    }

    public function indexTaker(Request $req, $cpath)
    {
        // Obtener el usuario autenticado
        $user = Auth::user();

        if (is_null($user)) {
            return redirect()->back()->with('error', 'Usuario no encontrado.');
        }

        if (!in_array($user->area_id, [Area::ADMINISTRATIVE, Area::ADMINISTRATIVE_MNK])) {
            return redirect()->back()->with('error', 'Usuario no autorizado.');
        }

        $policyId = "";
        if ($req->input('policy_consecutive')) {
            preg_match('/\d+/', $req->input('policy_consecutive'), $coincidencias);
            $policyId = $coincidencias[0];
        }
        $numIdentification = $req->input('num_identification');
        $tomador = $req->input('tomador_name');
        $intermediarioName = $req->input('intermediate_name');

        $policiesQuery = PolicySort::whereHas('activity', function ($query) use ($numIdentification, $tomador) {

            $query->where('service_id', Service::SERVICE_POLICY_SORT_MNK);

            // Aplicar filtro por tomador si se ha ingresado un nombre
            if (!empty($tomador)) {
                $query->whereHas('affiliate', function ($affiliateQuery) use ($tomador) {
                    $affiliateQuery->whereRaw("LOWER(full_name) LIKE ?", ["%" . strtolower($tomador) . "%"]);
                });
            }

            // Aplicar filtro por tomador si se ha ingresado una identificacion
            if (!empty($numIdentification)) {
                $query->whereHas('affiliate', function ($affiliateQuery) use ($numIdentification) {
                    $affiliateQuery->whereRaw("doc_number LIKE ?", ["%" . strtolower($numIdentification) . "%"]);
                });
            }
        })
        ->when($policyId, function ($query) use ($policyId) {
            $query->where('consecutive', $policyId);
        })
        ->when(!empty($intermediarioName), function ($query) use ($intermediarioName) {
            $query->whereRaw('LOWER(advisor_name) LIKE LOWER(?)', ['%' . strtolower($intermediarioName) . '%']);
        })
            ->with(['activity.affiliate', 'activity.state'])
            ->orderBy('created_at', 'desc');

        $policies = $policiesQuery->paginate(10)->appends(request()->query());

        return view('table.data-update.taker', [
            'active' => 'tomadores',
            'id' =>  $user->id, // Esto es para que el id de la ruta hacia la vista servicios asignados
            'user' => $user,
            'policies' => $policies,
        ]);
    }

    public function editTaker(Request $request, $cpath, $id)
    {
        // Obtener el usuario autenticado
        $user = Auth::user();

        if (!in_array($user->area_id, [Area::ADMINISTRATIVE, Area::ADMINISTRATIVE_MNK])) {
            return redirect()->back()->with('error', 'Usuario no autorizado.');
        }

        $client = Client::where('path', $cpath)->firstOrFail();

        $activity = Activity::query()
            ->where('client_id', $client->id)->where('id', $id)
            ->with(['affiliate', 'policy_sort'])
            ->firstOrFail();

        $policyContacts = PolicyContact::query()
            ->where('policy_sort_id', $activity->policy_sort->id)
            ->get();

        $condiciones = ActivityAction::query()
            ->where('action_id', '=', ActionCotizacionSort::REPORTAR_CONDICIONES_ESPECIALES)
            ->where('activity_id', '=', $activity->parent->id)
            ->first();

        // Recoger el parámetro de identificación del trabajador
        $identificacion = $request->input('identificacion_trabajador');
        $name = $request->input('nombre_trabajador');
        $desde = $request->input('vigencia_date');
        $hasta = $request->input('vigencia_hasta_date');

        // Inicializar el reporte de afiliados
        $affiliateReport = [];

        // Buscar la última actividad del reporte de planilla tomador mediante la póliza
        $activityReport = Activity::where('parent_id', $activity->id)
            ->where('service_id', Service::SERVICE_REPORT_TAKEN_FORM_MNK)
            ->latest()
            ->first();

        if ($activityReport) {
            // Buscar la última planilla tomador mediante la actividad
            $lastReport = PolicySpreadsheet::where('activity_id', $activityReport->id)->first();

            if ($lastReport) {
                // Construir la consulta inicial para los afiliados
                $affiliateReportQuery = PolicySpreadsheetAffiliate::leftJoin('affiliates', 'policy_spreadsheet_affiliates.affiliate_id', '=', 'affiliates.id')
                    ->where('policy_spreadsheet_affiliates.policy_spreadsheet_id', $lastReport->id)
                    ->where('policy_spreadsheet_affiliates.temporal', 1)
                    ->select('policy_spreadsheet_affiliates.*')
                    ->orderBy('policy_spreadsheet_affiliates.created_at', 'desc');

                // Filtrar por identificación si se proporciona
                if (!empty($identificacion)) {
                    $affiliateReportQuery->where('policy_spreadsheet_affiliates.identification_number', $identificacion);
                }

                if (!empty($name)) {
                    $affiliateReportQuery->where('policy_spreadsheet_affiliates.name', 'LIKE', '%' . $name . '%');
                }

                if (!empty($desde) && !empty($hasta)) {
                    $affiliateReportQuery->whereBetween('policy_spreadsheet_affiliates.created_at', [$desde, $hasta]);
                } elseif (!empty($desde)) {
                    $affiliateReportQuery->where('policy_spreadsheet_affiliates.created_at', '>=', $desde);
                } elseif (!empty($hasta)) {
                    $affiliateReportQuery->where('policy_spreadsheet_affiliates.created_at', '<=', $hasta);
                }


                // Paginar los resultados
                $affiliateReport = $affiliateReportQuery->paginate(10);
            }
        }

        return view('table.data-update.form-taker', [
            'activity' => $activity,
            'policy_sort' => $activity->policy_sort,
            'condiciones' => $condiciones,
            'affiliate' => $activity->affiliate,
            'policy_contacts' => $policyContacts,
            'affiliateReport' => $affiliateReport,
        ]);
    }

    public function updateTaker(Request $request, $cpath, $id)
    {
        // Obtener el usuario autenticado
        $user = Auth::user();

        if (!in_array($user->area_id, [Area::ADMINISTRATIVE, Area::ADMINISTRATIVE_MNK])) {
            return redirect()->back()->with('error', 'Usuario no autorizado.');
        }

        $client = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();
        $affiliate = $activity->affiliate; // El tomador de la poliza
        $policySort = $activity->policy_sort;

        DB::beginTransaction();
        try {

            $oldData = optional($policySort)->taker_updates ?? [];

            if ($request->input('doc_type2')) {
                $oldData['old_affiliate_doc_type'] = $affiliate->doc_type;
                $affiliate->doc_type = $request->input('doc_type2');
            }

            if ($request->input('numberIdentify2')) {
                $oldData['old_affiliate_doc_number'] = $affiliate->doc_number;
                $affiliate->doc_number = $request->input('numberIdentify2');
            }

            if ($request->input('policyHolderName2')) {
                $oldData['old_affiliate_first_name'] = $affiliate->first_name;
                $affiliate->first_name = $request->input('policyHolderName2');
            }

            if ($request->input('policyHolderPhone2')) {
                $oldData['old_affiliate_phone'] = $affiliate->phone;
                $affiliate->phone = $request->input('policyHolderPhone2');
            }

            if ($request->input('policyHolderEmail2')) {
                // TODO: Validar los demas campos
                $request->validate( [
                    'policyHolderEmail2' => 'email',
                ],[
                    'policyHolderEmail2.email' => 'El Correo electrónico de notificaciones no tiene un formato válido.',
                ]);

                $oldData['old_affiliate_email'] = $affiliate->email;
                //$affiliate->email = $request->input('policyHolderEmail2');
                $policySort->notification_email = $request->input('policyHolderEmail2');
                $policySort->save();
            }

            if ($request->input('emailElectronicBilling2')) {
                $oldData['old_affiliate_electronic_billing_email'] = $affiliate->electronic_billing_email;
                $affiliate->electronic_billing_email = $request->input('emailElectronicBilling2');
            }

            if ($request->input('nacionalidadInput2')) {
                $oldData['old_affiliate_country'] = $affiliate->country;
                $affiliate->country = $request->input('nacionalidadInput2');
            }

            if ($request->input('ibanAcccount2')) {
                $oldData['old_affiliate_iban_account'] = $affiliate->iban_account;
                $affiliate->iban_account = $request->input('ibanAcccount2');
            }

            if ($request->input('province2')) {
                $oldData['old_affiliate_province'] = $affiliate->province;
                $affiliate->province = $request->input('province2');
            }

            if ($request->input('canton2')) {
                $oldData['old_affiliate_canton'] = $affiliate->canton;
                $affiliate->canton = $request->input('canton2');
            }

            if ($request->input('district2')) {
                $oldData['old_affiliate_district'] = $affiliate->district;
                $affiliate->district = $request->input('district2');
            }

            if ($request->input('employerAddress2')) {
                $oldData['old_affiliate_employer_address'] = $affiliate->employer_address;
                $affiliate->employer_address = $request->input('employerAddress2');
            }

            // Actualización o creación de responsables
            $responsibles = $this->getResponsibles($request);
            if (!empty($responsibles)){
                // Iterar sobre los items
                foreach ($responsibles as $index => $responsible) {
                    // Sí no existe se crea
                    if (empty($responsible['id'])) {
                        $uniqueCode = substr(hash('sha256', $responsible['number_identify_responsible'] . microtime(true)), 0, 8);
                        
                        $newPolicyContact = $policySort->policy_contacts()->create([
                            'name_responsible' => $responsible['name_responsible'],
                            'type_identification' => $responsible['type_identification'],
                            'number_identify_responsible' => $responsible['number_identify_responsible'],
                            'ocupation_responsible' => $responsible['ocupation_responsible'],
                            'phone_responsible' => $responsible['phone_responsible'],
                            'cellphone_responsible' => $responsible['cellphone_responsible'],
                            'email_responsible' => $responsible['email_responsible'],
                            'unique_code' => $uniqueCode,
                        ]);

                        $responsibles[$index]['id'] = $newPolicyContact->id;

                        // Envia correo de codigo unico
                        $this->sendUniqueCodeByEmail(
                            $newPolicyContact, 
                            $uniqueCode, 
                            $activity, 
                            $client
                        );

                        $oldData['policy_contact_ids_created'][] = $newPolicyContact->id;
                    } else {
                        // Sí existe se actualiza
                        $policyContact = PolicyContact::find($responsible['id']);
                        if ($policyContact) {
                            $policyContact->update([
                                'name_responsible' => $responsible['name_responsible'],
                                'type_identification' => $responsible['type_identification'],
                                'number_identify_responsible' => $responsible['number_identify_responsible'],
                                'ocupation_responsible' => $responsible['ocupation_responsible'],
                                'phone_responsible' => $responsible['phone_responsible'],
                                'cellphone_responsible' => $responsible['cellphone_responsible'],
                                'email_responsible' => $responsible['email_responsible'],
                            ]);

                            $oldData['policy_contact_ids_updated'][] = $policyContact->id;
                        }
                    }
                }

                $responsibleToDelete = $this->getpolicyContactsToDelete($policySort, $responsibles);

                // Eliminar los responsables que no están en el request
                if (!empty($responsibleToDelete)) {
                    foreach ($responsibleToDelete as $id) {
                        $policyContact = PolicyContact::find($id);
                        if ($policyContact) {
                            $oldData['policy_contact_ids_deleted'][] = $policyContact->id;
                            $policyContact->delete();
                        }
                    }
                }
                
            }

            if (!empty($oldData)) {
                $oldData['updated_at'] = Carbon::now()->toDateTimeString();
                $oldData['updated_by'] = Auth::user()->id;

                $policySort->taker_updates = $oldData;

                $policySort->save();

                $affiliate->save();

                $activity_action = ActionController::create(
                    $policySort->activity_id,
                    ActionPolizaSort::ACTUALIZACION_DE_DATOS_TOMADOR,
                    'ACTUALIZACIÓN DE DATOS TOMADOR'
                );
            }

            DB::commit();

            return redirect()->back()->with('success', 'Datos actualizados exitosamente');
        } catch (\Throwable $th) {
            Log::error($th);
            DB::rollback();

            return back()->withErrors($th->getMessage())->withInput();
        }
    }

    private function getpolicyContactsToDelete(PolicySort $policySort, array $responsibles)
    {
        // Buscar los IDs de los responsables en el request
        $responsiblesIds = array_column($responsibles, 'id');
        // Buscar los IDs de los responsables de la póliza
        $policyContactIds = $policySort->policy_contacts()->pluck('id')->toArray();

        // Diferencias en ambos sentidos
        $diff1 = array_diff($responsiblesIds, $policyContactIds);
        $diff2 = array_diff($policyContactIds, $responsiblesIds);

        // Unir ambos resultados
        $result = array_merge($diff1, $diff2);

        return $result;
    }

    public function updateTakerInclusion(Request $request, $cpath, $id, $inclusionId)
    {
        // Obtener el usuario autenticado
        $user = Auth::user();

        if (!in_array($user->area_id, [Area::ADMINISTRATIVE, Area::ADMINISTRATIVE_MNK])) {
            return response()->json([
                'success' => false,
                'message' => 'Usuario no autorizado.',
            ], 500);
        }

        $policySpreadsheetAffiliate = PolicySpreadsheetAffiliate::where('id', $inclusionId)->firstOrFail();

        $monthly_salary = str_replace(',', '.', $request->monthly_salary);
        $monthly_salary = floatval($monthly_salary);
        $days = str_replace(',', '.', $request->days);
        $days = floatval($days);
        $hours = str_replace(',', '.', $request->hours);
        $hours = floatval($hours);

        DB::beginTransaction();
        try {
            $oldUpdatedAt = Carbon::now()->toDateTimeString();
            $oldData = [
                'name' => $policySpreadsheetAffiliate->name,
                'last_name' => $policySpreadsheetAffiliate->last_name,
                'id_type' => $policySpreadsheetAffiliate->id_type,
                'nationality' => $policySpreadsheetAffiliate->nationality,
                'identification_number' => $policySpreadsheetAffiliate->identification_number,
                'first_name' => $policySpreadsheetAffiliate->first_name,
                'date_of_birth' => $policySpreadsheetAffiliate->date_of_birth,
                'gender' => $policySpreadsheetAffiliate->gender,
                'work_shift_type' => $policySpreadsheetAffiliate->work_shift_type,
                'monthly_salary' => $policySpreadsheetAffiliate->monthly_salary,
                'days' => $policySpreadsheetAffiliate->days,
                'hours' => $policySpreadsheetAffiliate->hours,
                'occupation' => $policySpreadsheetAffiliate->occupation,
                'email' => $policySpreadsheetAffiliate->email,
                'observation_affiliate' => $policySpreadsheetAffiliate->observation_affiliate,
                'updated_at' => $oldUpdatedAt,
                'updated_by' => Auth::user()->id,
            ];

            $policySpreadsheetAffiliate->update([
                'name' => $request->first_name . ' ' . $request->last_name,
                'last_name' => $request->last_name,
                'id_type' => $request->id_type,
                'nationality' => $request->nationality,
                'identification_number' => $request->identification_number,
                'first_name' => $request->first_name,
                'date_of_birth' => $request->date_of_birth,
                'gender' => $request->gender,
                'work_shift_type' => $request->work_shift_type,
                'monthly_salary' => $monthly_salary,
                'days' => $days,
                'hours' => $hours,
                'occupation' => $request->occupation,
                'email' => $request->email,
                'observation_affiliate' => $request->observation_affiliate,
                'taker_updates' => $oldData,
            ]);
            
            $affiliate = Affiliate::where('id', $policySpreadsheetAffiliate->affiliate_id)->firstOrFail();

            $affiliate->update([
                'doc_type' => $request->id_type,
                'doc_number' => $request->identification_number,
                'first_name' => $request->first_name,
                'last_name' => $request->last_name,
                'country' => $request->nationality,
                'birthday' => $request->date_of_birth,
                'gender' => $request->gender,
                'email' => $request->email,
            ]);

            $policySpreadsheetAffiliate->refresh();
            DB::commit();

            return response()->json([
                'success' => true, 
                'data' => $policySpreadsheetAffiliate,
                'meta' => [
                    'updated_by' => Auth::user()->full_name,
                    'updated_at' => $oldUpdatedAt,
                ],
                'message' => 'Datos actualizados exitosamente'
            ]);
        } catch (\Throwable $th) {
            DB::rollback();
            Log::error($th);

            return response()->json([
                'success' => false,
                'message' => 'Ocurrió un error al procesar la solicitud.',
            ], 422);
        }
    }

    private function sendUniqueCodeByEmail(PolicyContact $policyContact, $uniqueCode, Activity $activity, Client $client)
    {
        $email = $policyContact->email_responsible ?? '';

        $emailBuild = TemplateBuilder::build(
            Templates::UNIQUE_CODE_WORK_RISK_REPORT,
            [
                'policy_sort' => $activity->policy_sort->formatNumberConsecutive(),
                'name_responsible' => mb_convert_case(mb_strtolower($policyContact->name_responsible ?? ''), MB_CASE_TITLE, "UTF-8"),
                'representante' => mb_convert_case(mb_strtolower($activity->affiliate->full_name ?? ''), MB_CASE_TITLE, "UTF-8"),
                'unique_code' => $uniqueCode,
            ]
        );
        $mailSent = new SendDocumentDataBase(
            $email,
            $emailBuild['subject'],
            "<EMAIL>",
            $emailBuild['subject'],
            [
                "text" => $emailBuild['body'],
                "sender" => $emailBuild['sender']
            ],
            "<EMAIL>",
            [],
            "send_document_db",
            $client,
            request()->getHost(),
            $activity->id
        );

        $mailSent->sendMail();
    }

    private function getResponsibles(Request $request)
    {
        $request = $request->all();

        $responsibles = [];
        $count = count(isset($request['name_responsible']) ? $request['name_responsible'] : []); // asumimos que todos los arrays tienen la misma longitud

        for ($i = 0; $i < $count; $i++) {
            // Puedes validar si hay datos suficientes para guardar ese responsable
            if (!empty($request['name_responsible'][$i]) || !empty($request['id_responsible'][$i])) {
                $responsibles[] = [
                    'id' => $request['id_responsible'][$i] ?? null,
                    'name_responsible' => $request['name_responsible'][$i] ?? null,
                    'type_identification' => $request['doc_type_responsible'][$i] ?? null,
                    'number_identify_responsible' => $request['number_identify_responsible'][$i] ?? null,
                    'ocupation_responsible' => $request['ocupation_responsible'][$i] ?? null,
                    'phone_responsible' => $request['phone_responsible'][$i] ?? null,
                    'cellphone_responsible' => $request['cellphone_responsible'][$i] ?? null,
                    'email_responsible' => $request['email_responsible'][$i] ?? null,
                ];
            }
        }

        return $responsibles;
    }

    public function indexInsured(Request $req, $cpath)
    {
        // Obtener el usuario autenticado
        $user = Auth::user();

        if (is_null($user)) {
            return redirect()->back()->with('error', 'Usuario no encontrado.');
        }

        if (!in_array($user->area_id, [Area::ADMINISTRATIVE, Area::ADMINISTRATIVE_MNK])) {
            return redirect()->back()->with('error', 'Usuario no autorizado.');
        }

        // FILTROS
        $numIdentification = $req->input('num_identification');
        $tomadorName = $req->input('tomador_name');
        $insuredName = $req->input('insured_name');
        $intermediarioName = $req->input('intermediate_name');
        $responsibleAccident = $req->input('responsible_accident');
        $policyId = "";
        if ($req->input('policy_consecutive')) {
            preg_match('/\d+/', $req->input('policy_consecutive'), $coincidencias);
            $policyId = $coincidencias[0];
        }

        $activities = Activity::query()
            ->where('service_id', Service::SERVICE_GIS_SORT_MNK)
            ->when(!empty($policyId) || !empty($intermediarioName), function ($query) use ($policyId, $intermediarioName) {
                $query->whereHas('parent_activity.policy_sort', function ($q) use ($policyId, $intermediarioName) {
                    if ($policyId) {
                        $q->where('consecutive', $policyId);
                    }

                    if ($policyId) {
                        $q->whereRaw('LOWER(advisor_name) LIKE LOWER(?)', ['%' . strtolower($intermediarioName) . '%']);
                    }
                });
            })
            ->when(!empty($responsibleAccident), function ($query) use ($responsibleAccident) {
                $query->whereHas('parent_activity.policy_sort.policy_contacts', function ($q) use ($responsibleAccident) {
                        $q->whereRaw('LOWER(name_responsible) LIKE LOWER(?)', ['%' . strtolower($responsibleAccident) . '%']);
                });
            })
            ->when(!empty($numIdentification) || !empty($tomadorName), function ($query) use ($numIdentification, $tomadorName) {
                $query->whereHas('parent_activity.affiliate', function ($q) use ($numIdentification, $tomadorName) {
                    if ($numIdentification) {
                        $q->whereRaw("doc_number LIKE ?", ["%" . strtolower($numIdentification) . "%"]);
                    }

                    if ($tomadorName) {
                        $q->whereRaw("LOWER(full_name) LIKE ?", ["%" . strtolower($tomadorName) . "%"]);
                    }
                });
            })
            ->when(!empty($insuredName), function($query) use ($insuredName) {
                $query->whereHas('affiliate', function ($q) use ($insuredName) {
                    $q->whereRaw("LOWER(full_name) LIKE ?", ["%" . strtolower($insuredName) . "%"]);
                });
            })
            ->orderBy('id', 'desc')
            ->paginate(10);

        return view('table.data-update.insured', [
            'active' => 'asegurados',
            'id' =>  $user->id, // Esto es para que el id de la ruta hacia la vista servicios asignados
            'user' => $user,
            'activities' => $activities,
        ]);
    }

    public function editInsured(Request $req, $cpath, $id)
    {
        // Obtener el usuario autenticado
        $user = Auth::user();

        if (!in_array($user->area_id, [Area::ADMINISTRATIVE, Area::ADMINISTRATIVE_MNK])) {
            return redirect()->back()->with('error', 'Usuario no autorizado.');
        }

        $client = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::query()
            ->with([
                'gis_sort',
                'affiliate',
                'parent_activity.policy_sort.policy_contacts',
                'affiliate.policy_spreadsheet_affiliate',
            ])
            ->where('client_id', $client->id)
            ->where('id', $id)
            ->firstOrFail();

        return view('table.data-update.form-insured', [
            'activity' => $activity,
        ]);
    }

    public function updateInsured(Request $request, $cpath, $id)
    {
        // Obtener el usuario autenticado
        $user = Auth::user();

        if (!in_array($user->area_id, [Area::ADMINISTRATIVE, Area::ADMINISTRATIVE_MNK])) {
            return redirect()->back()->with('error', 'Usuario no autorizado.');
        }

        $client = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();
        $gisSort = $activity->gis_sort;
        $affiliate = $activity->affiliate;

        if (!$gisSort) {
            return redirect()->back()->with('error','GIS sort no encontrado');
        }

        DB::beginTransaction();
        try {
            $oldData = optional($gisSort)->insured_updates ?? [];

            if ($request->input('doc_type2')) {
                $oldData['old_type_identification_affiliate'] = $gisSort->type_identification_affiliate;

                $affiliate->doc_type = $request->input('doc_type2');
            }

            if ($request->input('docNumber2')) {
                $oldData['old_number_identification_affiliate'] = $gisSort->number_identification_affiliate;

                $affiliate->doc_number = $request->input('docNumber2');
            }

            if ($request->input('name2')) {
                $oldData['old_name_affiliate'] = $gisSort->name_affiliate;

                $affiliate->first_name = $request->input('name2');
            }

            if ($request->input('last_name2')) {
                $oldData['old_last_name_affiliate'] = $affiliate->last_name;

                $affiliate->last_name = $request->input('last_name2');
            }

            if ($request->input('phone2')) {
                $oldData['old_cellphone_affiliate'] = $gisSort->cellphone_affiliate;

                $affiliate->phone = $request->input('phone2');
            }

            if ($request->input('email2')) {
                $oldData['old_email_affiliate'] = $gisSort->email_affiliate;

                $affiliate->email = $request->input('email2');
            }

            if ($request->input('nacionalidadInput2')) {
                $oldData['old_nationality_affiliate'] = $gisSort->nationality_affiliate;

                $affiliate->country = $request->input('nacionalidadInput2');
            }

            if ($request->input('civil_status2')) {
                $oldData['old_civil_status'] = $affiliate->civil_status;
                $affiliate->civil_status = $request->input('civil_status2');
            }

            if ($request->input('escolaridad2')) {
                $oldData['old_educational_level'] = $gisSort->educational_level;

                $affiliate->school_level = $request->input('escolaridad2');
            }

            if ($request->input('condition_gis2')) {
                $oldData['old_conditions'] = $gisSort->conditions;
                $gisSort->conditions = $request->input('condition_gis2');

                //Ejecutamos la acción REPORTAR MUERTE DE AFILIADO
                if ($request->input('condition_gis2') == 'Fallecido') {
                    ActionController::create(
                        $activity->id, 
                        ActionGisSort::REPORTAR_MUERTE_AFILIADO, 
                        "Se generó la acción reportar muerte afiliado"
                    );
                }

                //Ejecutamos la acción REPORTAR DESAPARICIÓN DE AFILIADO
                if ($request->input('condition_gis2') == 'Desaparecido') {
                    ActionController::create(
                        $activity->id, 
                        ActionGisSort::REPORTAR_DESAPARICION_AFILIADO, 
                        "Se generó la acción reportar desaparición afiliado"
                    );
                }
            }

            if ($request->input('iban2')) {
                $oldData['old_worker_iban_account_number'] = $gisSort->worker_iban_account_number;

                $affiliate->iban_account = $request->input('iban2');
            }

            if ($request->input('province2')) {
                $oldData['old_province'] = $gisSort->province;

                $affiliate->province = $request->input('province2');
            }

            if ($request->input('canton2')) {
                $oldData['old_canton'] = $gisSort->canton;

                $affiliate->canton = $request->input('canton2');
            }

            if ($request->input('district2')) {
                $oldData['old_district'] = $gisSort->district;

                $affiliate->district = $request->input('district2');
            }

            if ($request->input('employerAddress2')) {
                $oldData['old_worker_address'] = $gisSort->worker_address;

                $affiliate->employer_address = $request->input('employerAddress2');
            }

            if ($request->input('accident_date2')) {
                $oldData['old_date_accident'] = $gisSort->date_accident;
                $gisSort->date_accident = $request->input('accident_date2');
            }

            if (!empty($oldData)) {
                $oldData['updated_at'] = Carbon::now()->toDateTimeString();
                $oldData['updated_by'] = Auth::user()->id;

                $gisSort->insured_updates = $oldData;
                $gisSort->save();

                $affiliate->save();

                $activity_action = ActionController::create(
                    $activity->parent_id,
                    ActionPolizaSort::ACTUALIZACION_DE_DATOS_ASEGURADO,
                    'ACTUALIZACIÓN DE DATOS ASEGURADO'
                );

            }

            DB::commit();

            return redirect()->back()->with('success', 'Datos actualizados exitosamente');
        } catch (\Throwable $th) {
            Log::error($th->getMessage());
            DB::rollback();

            return redirect()->back()->with('error',$th->getMessage());
        }
    }

    public function indexIntermediary(Request $req, $cpath)
    {
        // Obtener el usuario autenticado
        $user = Auth::user();

        if (is_null($user)) {
            return redirect()->back()->with('error', 'Usuario no encontrado.');
        }

        if (!in_array($user->area_id, [Area::ADMINISTRATIVE, Area::ADMINISTRATIVE_MNK])) {
            return redirect()->back()->with('error', 'Usuario no autorizado.');
        }

        $policyId = "";
        if ($req->input('policy_consecutive')) {
            preg_match('/\d+/', $req->input('policy_consecutive'), $coincidencias);
            $policyId = $coincidencias[0];
        }
        $numIdentification = $req->input('num_identification');
        $tomador = $req->input('tomador_name');
        $intermediarioName = $req->input('intermediate_name');

        $policiesQuery = PolicySort::whereHas('activity', function ($query) use ($numIdentification, $tomador) {

            $query->where('service_id', Service::SERVICE_POLICY_SORT_MNK);

            // Aplicar filtro por tomador si se ha ingresado un nombre
            if (!empty($tomador)) {
                $query->whereHas('affiliate', function ($affiliateQuery) use ($tomador) {
                    $affiliateQuery->whereRaw("LOWER(full_name) LIKE ?", ["%" . strtolower($tomador) . "%"]);
                });
            }

            // Aplicar filtro por tomador si se ha ingresado una identificacion
            if (!empty($numIdentification)) {
                $query->whereHas('affiliate', function ($affiliateQuery) use ($numIdentification) {
                    $affiliateQuery->whereRaw("doc_number LIKE ?", ["%" . strtolower($numIdentification) . "%"]);
                });
            }
        })
        ->when($policyId, function ($query) use ($policyId) {
            $query->where('consecutive', $policyId);
        })
        // ->where('advisor_name', '!=', '')
        ->when(!empty($intermediarioName), function ($query) use ($intermediarioName) {
            $query->whereRaw('LOWER(advisor_name) LIKE LOWER(?)', ['%' . strtolower($intermediarioName) . '%']);
        })
            ->with(['activity.affiliate', 'activity.state'])
            ->orderBy('created_at', 'desc');

        $policies = $policiesQuery->paginate(10)->appends(request()->query());

        return view('table.data-update.intermediary', [
            'active' => 'intermediarios',
            'id' =>  $user->id, // Esto es para que el id de la ruta hacia la vista servicios asignados
            'user' => $user,
            'policies' => $policies,
        ]);
    }

    public function searchAscelCode(Request $request)
    {
        // Obtener el usuario autenticado
        $user = Auth::user();

        if (!in_array($user->area_id, [Area::ADMINISTRATIVE, Area::ADMINISTRATIVE_MNK])) {
            return response()->json([
                'success' => false,
                'message' => 'Usuario no autorizado.',
            ], 500);
        }

        // Validar que 'code2' es requerido
        $validator = Validator::make($request->all(), [
            'code2' => 'required'
        ], [
            'code2.required'=> 'El código es requerido'
        ]);

        if ($validator->fails()) {
            // Retornar error en formato JSON para AJAX
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        // Si la validación pasa, hacer lo que necesites con el código
        $code = "CO-" . $request->input('code2');

        // Consultar el codigo en acsel
        $integrationServiceController = new IntegrationServiceController();
        $url = config('app.api_url') . '/acsel/intermediario';
        $method = 'GET';
        $data = ['cod_inter' => $code];
        
        $result = $integrationServiceController->requestRenAppApi($url, $method, $data);
        
        $data = json_decode($result->getContent(), true);

        if (isset($data['success']) && $data['success'] && isset($data['data'])) {
            return response()->json([
                'success' => true,
                'code_found' => true,
                'message' => 'Código recibido correctamente',
                'code' => $code,
                'data' => $data['data'],
            ]);
        }

        return response()->json([
            'success' => true,
            'code_found' => false,
            'message' => 'Código recibido correctamente',
            'code' => $code,
            'data' => null,
        ]);
    }

    public function upsertIntermediary(Request $request, $cpath, $activity_id)
    {
        // Obtener el usuario autenticado
        $user = Auth::user();

        if (!in_array($user->area_id, [Area::ADMINISTRATIVE, Area::ADMINISTRATIVE_MNK])) {
            return response()->json([
                'success' => false,
                'message' => 'Usuario no autorizado.',
            ], 500);
        }

        $client = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::query()
            ->where('id', $activity_id)
            ->where('service_id', Service::SERVICE_POLICY_SORT_MNK)
            ->where('client_id', $client->id)
            ->firstOrFail();

        $policy_sort = $activity->policy_sort;
        $quotation = $activity->parent_activity->quotation;

        /*
            Validación de los datos
         */
        $validator = Validator::make($request->all(), [
            'codigo' => 'required',
            'nombre' => 'required',
            'email' => 'present',
            'tipo' => 'required',
            'perfil' => 'required',
            'cdesinter' => 'required',
            'tipoCorredor' => 'required',
            'detalleCorredor' => 'required',
            'descCorredor' => 'required',
            'codRol' => 'required',
            'codCorredor' => 'required',
            'claveInter' => 'required',
            'tipo_corredor' => 'required',
            "emailCorredor" => 'present'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        // upsert
        // Si el CODCORREDOR viene null o vacio quiere decir que es un agente independiente
        $isIndependentBroker = empty($request->input('codCorredor')) ||$request->input('codCorredor') === $request->input('codigo');

        //Si el CODCORREDOR trae algun valor quiere decir que es un agente vinculado a una correduria
        $hasBrokerage = !empty($request->input('codCorredor'));

        if ($isIndependentBroker && $hasBrokerage) {
            throw new \Exception('El usuario es de tipo agente independiente pero tiene una correduría asociada');
        }

        if (!$isIndependentBroker && !$hasBrokerage) {
            throw new \Exception('El usuario es de tipo corredor pero no tiene una correduría asociada');
        }

        $result = json_encode([
            "codigo" => $request->input('codigo'),
            "nombre" => $request->input('nombre'),
            "email" => $request->input('email'),
            "tipo" => $request->input('tipo'),
            "perfil" => $request->input('perfil'),
            "cdesinter" => $request->input('cdesinter'),
            "tipoCorredor" => $request->input('tipoCorredor'),
            "detalleCorredor" => $request->input('detalleCorredor'),
            "descCorredor" => $request->input('descCorredor'),
            "codRol" => $request->input('codRol'),
            "codCorredor" => $request->input('codCorredor'),
            "claveInter" => $request->input('claveInter'),
            "tipo_corredor" => $request->input('tipo_corredor'),
            "emailCorredor" => $request->input('emailCorredor'),
        ], JSON_UNESCAPED_UNICODE );

        //Buscamos el usuario por el codigo sin CO-
        $user = User::query()
            ->where('code_mnk', 'LIKE', '%'.$request->input('codCorredor') .'%')
            ->orWhere('username', 'LIKE', '%'.$request->input('codCorredor') .'%')
            ->orderBy('id','desc')
            ->first();

        //Nombre del corredor o agente
        $nameAdvisor = $request->input('detalleCorredor');
        //Email del corredor o agente
        $emailAdvisor = $request->input('emailCorredor') ?? "CO-" . $request->input('codCorredor') . "@mnk.com";
        //Codigo de corredor o agente
        $codeAdvisor  = $request->input('codCorredor');
        //Tipo de agente o asesor
        $typeAdvisor = $request->input('tipoCorredor');
        //Nombre de la correduria o intermediario
        $nameBrokerage = $request->input('nombre');
        //Codigo de la correduria o intermediario
        $codeBrokerage = $request->input('codigo');
        //Tipo de intermediario o correduria
        $typeBrokerage = $request->input('tipo');
       
        if ($user) {
            // si existe el usuario lo actualizamos
            $user->brokerage_name = $nameBrokerage ;
            $user->new_business_email = $emailAdvisor;
            $user->type_inter = $typeBrokerage;
            if($isIndependentBroker){
                $user->advisor_name = $nameBrokerage;
                $user->code_correduria =  null;
            }else{
                $user->code_correduria = $codeBrokerage;
                $user->advisor_name = $nameAdvisor;
            }
            $user->tipo_corredor = $typeAdvisor;
            $user->resp_acsel_login = 'actualizado:'.$result;
            $user->save();
            
        } else {
            // si no existe creamos el usuario

            $email = !empty($emailAdvisor) ? $emailAdvisor : $codeBrokerage . '@example.com';

            if (User::where('email', $email)->exists()) {
                $email = 'ACSEL_' . uniqid() . '_' . $email;
            }

            $password = "Temporal1*";

            $user = new User;
            $user->brokerage_name = $nameBrokerage;
            $user->new_business_email = $emailAdvisor;
            $user->type_inter = $typeBrokerage;
            $user->username = str_replace('CO-', '', $codeAdvisor);
            $user->code_mnk = $codeAdvisor;
            $user->email = $email;
            $user->area_id = 43; 
            $user->password = Hash::make($password);

            if($isIndependentBroker){
                $user->first_name =  $nameBrokerage;
                $user->advisor_name = $nameBrokerage;
                $user->code_correduria =  null;
            }else{
                $user->first_name =  $nameAdvisor;
                $user->advisor_name = $nameAdvisor;
                $user->code_correduria = $codeBrokerage;
            }

            $user->tipo_corredor = $typeAdvisor;
            $user->resp_acsel_login = 'error acsel';
            $user->save();

            // Asociar el usuario al cliente en 'UserClient' si no existe la asociación
            $userClient = UserClient::where('user_id', $user->id)
            ->where('client_id', env('CLIENT_ID'))
            ->first();

            if (!$userClient) {
                UserClient::create([
                    'user_id' => $user->id,
                    'client_id' => env('CLIENT_ID'),
                ]);
            }
        }

        $oldData = optional($policy_sort)->intermediary_updates ?? [];
        $oldData['brokerage_name'] = $nameBrokerage;
        $oldData['advisor_name'] = $nameAdvisor;
        $oldData['code'] = $codeAdvisor;
        $oldData['email'] = $emailAdvisor;
        $oldData['updated_at'] = Carbon::now()->toDateTimeString();
        $oldData['updated_by'] = Auth::user()->id;

        $policy_sort->brokerage_name = $nameBrokerage;
        $policy_sort->advisor_name = $nameAdvisor;
        $policy_sort->code = $codeAdvisor;
        $policy_sort->email = $emailAdvisor;
        $policy_sort->intermediary_updates = $oldData;
        $policy_sort->save();

        $quotation->brokerage_name = $nameBrokerage;
        $quotation->advisor_name = $nameAdvisor;
        $quotation->code = $codeAdvisor;
        $quotation->email = $emailAdvisor;
        $quotation->save();

        // actualiza updatesintermediari
        $activity_action = ActionController::create(
            $policy_sort->activity_id,
            ActionPolizaSort::ACTUALIZACION_DE_DATOS_INTERMEDIARIO,
            'ACTUALIZACIÓN DE DATOS INTERMEDIARIO'
        );

        return response()->json([
            'success' => true,
            'message' => 'Corredor actualizado correctamente',
            'data' => $request->all()
        ]);
    }

    public function editIntermediary(Request $req, $cpath, $id)
    {
        // Obtener el usuario autenticado
        $user = Auth::user();

        if (!in_array($user->area_id, [Area::ADMINISTRATIVE, Area::ADMINISTRATIVE_MNK])) {
            return redirect()->back()->with('error', 'Usuario no autorizado.');
        }

        $client = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();
        $condiciones = ActivityAction::where('action_id', '=', ActionCotizacionSort::REPORTAR_CONDICIONES_ESPECIALES)->where('activity_id', '=', $activity->parent->id)->first();

        return view('table.data-update.form-intermediary', [
            'activity' => $activity,
            'policy_sort' => $activity->policy_sort,
            'condiciones' => $condiciones,
        ]);
    }

    public function updateIntermediary(Request $request, $cpath, $id)
    {
        // Obtener el usuario autenticado
        $user = Auth::user();

        if (!in_array($user->area_id, [Area::ADMINISTRATIVE, Area::ADMINISTRATIVE_MNK])) {
            return redirect()->back()->with('error', 'Usuario no autorizado.');
        }
        
        $client = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::query()
            ->where('id', $id)
            ->where('service_id', Service::SERVICE_POLICY_SORT_MNK)
            ->where('client_id', $client->id)
            ->firstOrFail();

        $policy_sort = $activity->policy_sort;
        $quotation = $activity->parent_activity->quotation;


        DB::beginTransaction();
        try {

            $oldData = optional($policy_sort)->intermediary_updates ?? [];

            if ($request->input('new_emails2')) {
                $oldData['old_email'] = $policy_sort->email;
                $policy_sort->email = $request->input('new_emails2');
                $quotation->email = $request->input('new_emails2');
            }

            if (!empty($oldData)) {
                $oldData['updated_at'] = Carbon::now()->toDateTimeString();
                $oldData['updated_by'] = Auth::user()->id;

                $policy_sort->intermediary_updates = $oldData;
                $policy_sort->save();
                $quotation->save();

                $activity_action = ActionController::create(
                    $policy_sort->activity_id,
                    ActionPolizaSort::ACTUALIZACION_DE_DATOS_INTERMEDIARIO,
                    'ACTUALIZACIÓN DE DATOS INTERMEDIARIO'
                );
            }

            DB::commit();

            return redirect()->back()->with('success', 'Datos actualizados exitosamente');
        } catch (\Throwable $th) {
            Log::error($th);
            DB::rollback();

            return redirect()->back()->with('error',$th->getMessage());
        }
    }
}