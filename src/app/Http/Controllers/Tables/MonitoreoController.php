<?php

namespace App\Http\Controllers\Tables;

use App\Activity;
use App\Client;
use App\GisSort;
use App\Http\Controllers\Controller;
use App\Http\Controllers\Services\MedicalServicesController;
use App\MedicalServicesSort;
use App\MedicalServiceCompanion;
use App\MedicalServiceDiagnostics;
use App\MedicalServiceImageDiagnostics;
use App\MedicalServiceMedicalPrescription;
use App\MedicalServiceReferralSpecialits;
use App\Service;
use Carbon\Carbon;
use Illuminate\Http\Request;
class MonitoreoController extends Controller
{
    public function index(Request $request, $cpath)
    {
        $client = Client::where('path', $cpath)->first();
        $activities = Activity::where('client_id', $client->id)->whereIn('service_id', [
            Service::SERVICE_MEDICAL_SERVICES_SORT_MNK,
            Service::SERVICE_SUPPLIER_MOT_MNK,
            Service::SERVICE_MEDICAMENTOS_MNK,
            Service::SERVICE_PE_IT_SORT_MNK
        ]);

        if ($request->input('tomador_name')) {
            $activities = $activities->whereHas('affiliate', function ($query) use ($request) {
                $query->where('first_name', 'like', '%' . $request->input('tomador_name') . '%')
                    ->orWhere('last_name', 'like', '%' . $request->input('tomador_name') . '%');
            });
        }

        if ($request->input('identification_number')) {
            $activities = $activities->whereHas('affiliate', function ($query) use ($request) {
                $query->where('doc_number', 'like', '%' . $request->input('identification_number') . '%');
            });
        }

        return view('table.monitoreo', [
            'activities' => $activities->orderBy('created_at', 'asc')->paginate(20),
        ]);

    }

    public function approve($cpath,$id,Request $request)
    {
        $client = Client::where('path', $cpath)->first();
        $activity = Activity::where('client_id', $client->id)->where('id', $id)->first();

        if ($request->action == 0){
            //REMITIR IMAGENES DIAGNOSTICAS
            $medical_services = new MedicalServicesController();
            $medical_services->issueDiagnosticImagingOrder($cpath,$id);
        }elseif ($request->action == 1){
            //REMITIR INCAPACIDAD
            $medical_services = new MedicalServicesController();
            $medical_services->medical_disability($cpath,$id);
        }elseif ($request->action == 2){
            //REMITIR A ESPECIALISTA
            $medical_services = new MedicalServicesController();
            $medical_services->referral_specialist($cpath,$id);
        }elseif ($request->action == 3){//REMITIR FORMULA MEDICA
            //REMITIR FORMULA MEDICA

        }elseif ($request->action == 4){
            //REMITIR A HOSPITALIZACION
            $medical_services = new MedicalServicesController();
            $medical_services->rehabilitation($cpath,$id);
        }


        return redirect()->back();
    }

    public function reject($cpath,$id,Request $request)
    {
        $reason = $request->reason;
        $medical_services = new MedicalServicesController();
        $medical_services->cancelService($reason,$cpath,$id);

        return redirect()->back();
    }

    public function indexGis(Request $request, $cpath)
    {
        $client = Client::where('path', $cpath)->first();
        $activities = Activity::where('client_id', $client->id)
            ->where('service_id', Service::SERVICE_GIS_SORT_MNK)
            ->join('gis_sort', 'activities.id', '=', 'gis_sort.activity_id')
            ->select('activities.*', 'gis_sort.consecutive_gis');


        //Filtro por consecutivo del gis
        if ($request->input('consecutive_gis')) {;
            $activities = $activities->whereHas('gis_sort', function ($query) use ($request) {
                $query->where('consecutive_gis', $request->input('consecutive_gis'));
            });
        }

        //Filtro por consecutivo del gis (year-num)
        if ($request->input('year_consecutive_gis')) {;
            $activities = $activities->whereHas('gis_sort', function ($query) use ($request) {
                $query->where('consecutive', $request->input('year_consecutive_gis'));
            });
        }
        // Aplicar el filtro de fechas con la función
        $startDate = $request->input('start_date_submit');
        $endDate = $request->input('end_date_submit');

        $activities = $activities->whereHas('gis_sort', function ($query) use ($startDate, $endDate) {
            $this->filterByDateAccident($query, $startDate, $endDate);
        });

        //filtro por el num de identificacion del trabajador
        if ($request->input('id_trabajador')) {
            $activities = $activities->whereHas('affiliate', function ($query) use ($request) {
                $query->where('doc_number', 'like', '%' . $request->input('id_trabajador') . '%');
            });
        }
        //filtro por el nombre del tomador
        if ($request->input('tomador_name')) {
            $activities = $activities->whereHas('parent_activity.affiliate', function ($query) use ($request) {
                $query->where('first_name', 'like', '%' . $request->input('tomador_name') . '%')
                    ->orWhere('last_name', 'like', '%' . $request->input('tomador_name') . '%');
            });
        }
        //filtro por el id de la poliza
        if ($request->input('consecutivo_poliza')) {
            $consecutivo_poliza = $request->input('consecutivo_poliza');
            $activities = $activities->whereHas('parent_activity.policy_sort', function ($query) use ($consecutivo_poliza) {
                $query->where('consecutive', $consecutivo_poliza);
            });
        }

        // Paginación y orden de las actividades
        $activities = $activities->orderBy('gis_sort.consecutive_gis', 'desc')->paginate(10)
            ->appends(request()->only('year_consecutive_gis', 'start_date_submit', 'end_date_submit', 'id_trabajador', 'tomador_name', 'consecutivo_poliza'));

        foreach ($activities as $activity) {
            $pm_realizadas = Activity::where('parent_id', $activity->id)
                ->where('service_id', Service::SERVICE_MEDICAL_SERVICES_SORT_MNK)
                ->count();
            $activity->pm_realizadas = $pm_realizadas;

            $total_days_of_incapacity = 0;
            $related_activities = Activity::where('parent_id', $activity->id)
                ->where('service_id', Service::SERVICE_MEDICAL_SERVICES_SORT_MNK)
                ->get();
            foreach ($related_activities as $related_activity) {
                if ($related_activity->medical_services_sort) {
                    $total_days_of_incapacity += $related_activity->medical_services_sort->days_of_incapacity;
                }
            }
            $activity->days_of_incapacity = $total_days_of_incapacity;
        }

        return view('table.monitoreo_gis', [
           'activities' => $activities,
        ]);
    } 

    /**
     * Filtra actividades según las fechas proporcionadas.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query El query base.
     * @param string|null $startDate Fecha de inicio (formato 'Y-m-d').
     * @param string|null $endDate Fecha de fin (formato 'Y-m-d').
     * @param string $field Campo de la base de datos a filtrar (por defecto 'date_accident').
     * @return \Illuminate\Database\Eloquent\Builder El query filtrado.
     */
    function filterByDateAccident($query, $startDate = null, $endDate = null, $field = 'date_accident')
    {
        // Si hay una fecha de inicio, convertirla
        if (!empty($startDate)) {
            try {
                // Convertir la fecha a formato Y-m-d (por defecto, Carbon entiende el formato yyyy-mm-dd)
                $startDate = Carbon::createFromFormat('Y-m-d', $startDate)->startOfDay();
            } catch (\Exception $e) {
                // Manejo de errores si el formato no es correcto
                throw new \Exception("La fecha de inicio no es válida.");
            }
        }

        // Si hay una fecha de fin, convertirla
        if (!empty($endDate)) {
            try {
                // Convertir la fecha a formato Y-m-d (por defecto, Carbon entiende el formato yyyy-mm-dd)
                $endDate = Carbon::createFromFormat('Y-m-d', $endDate)->endOfDay();
            } catch (\Exception $e) {
                // Manejo de errores si el formato no es correcto
                throw new \Exception("La fecha de fin no es válida.");
            }
        }

        if (!empty($startDate) && !empty($endDate)) {
            // Filtrar entre las fechas de inicio y fin
            $query->whereBetween($field, [$startDate, $endDate]);
        } elseif (!empty($startDate)) {
            // Filtrar desde la fecha de inicio
            $query->where($field, '>=', $startDate);
        } elseif (!empty($endDate)) {
            // Filtrar hasta la fecha de fin
            $query->where($field, '<=', $endDate);
        }

        return $query;
    }


}