<?php

namespace App\Http\Controllers\Tables;


use App\Actions\ActionReintegrate;
use App\Actions\ActionGisSort;
use App\Activity;
use App\ActivityAction;
use App\Client;
use App\Http\Controllers\ActionController;
use App\Http\Controllers\Controller;
use App\Mail\SendDocumentDataBase;
use App\PeitInabilitySort;
use App\PeItSort;
use App\Provider;
use App\Service;
use App\States\StateGis;
use Auth;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\GisSubrogacion;

class CompensationBoardController extends Controller
{

    public function index(Request $request, $cpath)
    {
        return view('table.compensation', [
            'active' => ''
        ]);

    }

    public function expenseRecognition(Request $request, $cpath)
    {
        $activity = Activity::where('service_id', Service::SERVICE_REINTEGRATE_MNK)
            ->whereHas('activity_actions', function ($query) {
                $query->where('action_id', ActionReintegrate::APROBACION_AUD_MEDICA);
            })
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('table.compensation.expense_recognition', [
            'activity' => $activity,
            'active' => 'reconocimiento_gastos'
        ]);

    }

    public function temporaryDisability($cpath)
    {
        // Lista de acciones
        $actionIds = [
            ActionGisSort::REPORTAR_REAPERTURA_CASO_ADMINISTRATIVA,
            ActionGisSort::REPORTAR_REAPERTURA_CASO,
            ActionGisSort::REPORTAR_CASO_EXCEPCION_APRONACION,
            ActionGisSort::REPORTAR_AVISO_CASO_NO_AMPARADO,
        ];

        // Convertir a string
        $idsString = implode(',', $actionIds);

        //lo siguiente es que toca que incluir prestaciones secundarias. hacer una consulta anterior para los gises e incluirlo en un where in en el left join de ag.id

        //consultar actividades padres donde su servicio sea GIS
        $activityGisIds = Activity::where('activities.service_id', Service::SERVICE_PE_IT_SORT_MNK)
            ->whereNotNull('activities.parent_id')
            ->leftJoin('activities as apr', function ($join) {
                $join->on('apr.id', '=', 'activities.parent_id')
                    ->where('apr.service_id', Service::SERVICE_GIS_SORT_MNK);
            })
            ->whereNotNull('apr.id') // Asegura que solo tome actividades con padre GIS
            ->pluck('apr.id')
            ->unique()
            ->toArray();


        //obtener las actividades
        $activity = Activity::select('activities.*')
            ->addSelect(DB::raw("(
                SELECT action_id
                FROM activity_actions
                WHERE activity_id = ag.id
                  AND action_id IN ($idsString)
                ORDER BY created_at DESC
                LIMIT 1
            ) as latest_action_id"))
            ->whereNotNull('activities.parent_id')
            ->orderBy('activities.created_at', 'desc')
            ->leftJoin('activities as apr', function ($join) {
                $join->on('apr.id', '=', 'activities.parent_id')
                    ->whereIn('apr.service_id', [
                        Service::SERVICE_MEDICAL_SERVICES_SORT_MNK,
                        Service::SERVICE_MEDICAL_SERVICES_SECONDARY_CARE_SORT_MNK,
                    ]);
            })
            ->leftJoin('activities as ag', function ($join) use ($activityGisIds) {
                $join->on('ag.id', '=', 'apr.parent_id')
                    ->whereIn('ag.id', $activityGisIds)
                    ->where('ag.service_id', Service::SERVICE_GIS_SORT_MNK);
            })
            ->where('activities.service_id', Service::SERVICE_PE_IT_SORT_MNK)
            ->paginate(10);

        return view('table.compensation.temporary_disability', ['activity' => $activity,
            'active' => 'incapacidad_temporal']);

    }

    public function permanentDisability($cpath)
    {
        $activity = Activity::where('service_id', Service::SERVICE_PE_IP_SORT_MNK)
            ->whereNotNull('parent_id')
            ->orderBy('created_at', 'desc')
            ->with(['affiliate', 'gis_sort', 'state',
                'state', 'service'])
            ->paginate(10);

        return view('table.compensation.permanent_disability', [
            'activity' => $activity,
            'active' => 'incapacidad_permanente'
        ]);

    }

    public function tableAdministrativePayments($cpath, Request $request)
    {

        $start_date = $request->input('start_date_submit');
        $end_date = $request->input('end_date_submit');
        $num_fac = $request->input('num_fac');

        $activity = Activity::where('service_id', Service::SERVICE_ADMINISTRATIVE_PAYMENTS_MNK)
            ->orderBy('created_at', 'desc');


        if (!empty($start_date) && !empty($end_date)) {
            $activity = $activity->whereHas('issuer_administrative_payments', function ($query) use ($start_date, $end_date) {
                $query->whereRaw('DATE(issue_date) BETWEEN ? AND ?', [$start_date, $end_date]);
                $query->orderBy('id', 'asc');
            });
        }

        if (!empty($num_fac)) {
            $activity = $activity->whereHas('issuer_administrative_payments', function ($query) use ($num_fac) {
                $query->whereRaw('LOWER(consecutive_number) LIKE ?', ['%' . strtolower($num_fac) . '%']);
            });
        }

        $activity = $activity->paginate(10);

        return view('table.compensation.table_administrative_payments', [
            'activity' => $activity,
            'active' => 'pagos_admnistrativos'
        ]);

    }

//ME-2606 (Subtarea Me-2607)
    public function reopening(Request $request, $cpath)
    {
        $case_status = $request->input('case_status');

        // Map de acciones a filtrar según el estado del caso
        $caseMap = [
            "reapertura" => [ActionGisSort::REPORTAR_REAPERTURA_CASO_ADMINISTRATIVA, ActionGisSort::REPORTAR_REAPERTURA_CASO],
            "amparado" => [ActionGisSort::REPORTAR_CASO_EXCEPCION_APRONACION],
            "no_amparado" => [ActionGisSort::REPORTAR_AVISO_CASO_NO_AMPARADO],
        ];


        $flag_case_status = in_array($case_status, ["asegurado", "no_asegurado"]);
        $actionIdsToFilter = $caseMap[$case_status] ?? [];

        // Lista de acciones
        $actionIds = [
            ActionGisSort::REPORTAR_REAPERTURA_CASO_ADMINISTRATIVA,
            ActionGisSort::REPORTAR_REAPERTURA_CASO,
            ActionGisSort::REPORTAR_CASO_EXCEPCION_APRONACION,
            ActionGisSort::REPORTAR_AVISO_CASO_NO_AMPARADO,
        ];

        // Convertir a string
        $idsString = implode(',', $actionIds);

        // Obtener actividades
        $activities = Activity::select('activities.*')
            ->addSelect(DB::raw("(
                SELECT action_id
                FROM activity_actions
                WHERE activity_id = activities.id
                  AND action_id IN ($idsString)
                ORDER BY created_at DESC
                LIMIT 1
            ) as latest_action_id"))
            ->where('activities.service_id', Service::SERVICE_GIS_SORT_MNK)
            ->where('activities.state_id', StateGis::CASO_EN_REAPERTURA_CASO)
            ->orderBy('activities.created_at', 'desc');

        // Un solo bloque whereHas con todos los filtros combinados
        $activities->whereHas('gis_sort', function ($query) use ($request, $case_status, $flag_case_status) {
            $query->where('report_reopening', 1);

            if ($flag_case_status) {
                $query->where('insured_case', $case_status === "asegurado" ? 'Si' : 'No');
            }

            if ($request->input('n_aviso')) {
                $query->where('consecutive_gis', $request->input('n_aviso'));
            }

            if ($request->input('n_caso')) {
                $query->where('consecutive', $request->input('n_caso'));
            }

            if ($request->input('n_servicio')) {
                $query->where('activity_id', $request->input('n_servicio'));
            }

            // Filtro por fechas de accidente
            if ($request->input('action_start_date') && $request->input('action_end_date')) {
                $start = Carbon::createFromFormat('Y-m-d', $request->input('action_start_date'))->startOfDay();
                $end = Carbon::createFromFormat('Y-m-d', $request->input('action_end_date'))->endOfDay();
                $query->whereBetween('date_accident', [$start, $end]);

            } elseif ($request->input('action_start_date')) {

                $start = Carbon::createFromFormat('Y-m-d', $request->input('action_start_date'))->startOfDay();
                $query->where('date_accident', '>=', $start);

            } elseif ($request->input('action_end_date')) {

                $end = Carbon::createFromFormat('Y-m-d', $request->input('action_end_date'))->endOfDay();
                $query->where('date_accident', '<=', $end);
            }
        });

        //Filtro de último action_id si corresponde
        if (!empty($actionIdsToFilter) && !$flag_case_status) {
            $activities->whereRaw("
            (
                SELECT action_id FROM activity_actions
                WHERE activity_id = activities.id
                  AND action_id IN ($idsString)
                ORDER BY created_at DESC
                LIMIT 1
            ) IN (" . implode(',', $actionIdsToFilter) . ")
        ");
        }

        // Excluir si es asegurado o no asegurado
        if ($flag_case_status) {
            // Incluir si latest_action_id es NULL, excluir si tiene valor
            $activities->whereRaw("
                (
                    SELECT action_id FROM activity_actions
                    WHERE activity_id = activities.id
                      AND action_id IN ($idsString)
                    ORDER BY created_at DESC
                    LIMIT 1
                ) IS NULL
            ");
        }

        //aplicar el filtro del nombre del tomado e identificación
        $activities->whereHas('affiliate', function ($query) use ($request) {
            //Filtro por identificación del paciente
            if ($request->input('num_affiliate')) {
                $query->where('doc_number', $request->input('num_affiliate'));
            }
            //Filtro por nombre del paciente
            if ($request->input('name_affiliate')) {
                $query->where('full_name', 'like', '%' . $request->input('name_affiliate') . '%');
            }
        });

        //Paginación con parámetros agregados
        $activities = $activities->paginate(10)
            ->appends($request->only('num_affiliate', 'name_affiliate', 'action_start_date', 'action_end_date', 'n_aviso', 'n_caso', 'n_servicio', 'case_status'));

        return view('table.compensation.reopening', [
            'activities' => $activities,
            'active' => 'reapertura'
        ]);
    }

//ME-2774 (Subtarea ME-2820)
    public function tableSubrogacion(Request $request)
    {

        $subrogacion = GisSubrogacion::with('gis.activity')->paginate(10);

        return view('table.compensation.subrogacion', [
            'active' => 'subrogacion',
            'subrogacion'=> $subrogacion
        ]);
    }

//ME-2755
    public function approveReopening($cpath, $activity_id, $redirectToReopeing = true)
    {
        //Iniciamos la transacción
        DB::beginTransaction();
        try {
            //client
            $client = Client::where('path', $cpath)->firstOrFail();

            //capturar la actividad GIS
            $activity_gis = Activity::find($activity_id);

            //verificar si la actividad existe
            if (!$activity_gis) {
                //mensaje de error
                $error = 'La actividad no existe';
                // Redirigir con el mensaje de error
                return redirect()->back()->withErrors($error)->withInput();
            }

            //capturar la actividad de póliza
            $activity_policy = Activity::where('id', $activity_gis->parent_id)->first();

            //validar si la actividad de póliza existe
            if (!$activity_policy) {
                //mensaje de error
                $error = 'La actividad de póliza no existe';
                // Redirigir con el mensaje de error
                return redirect()->back()->withErrors($error)->withInput();
            }

            //estados anteriores del estado caso cerrado (permitidos)
            $ids_states = [
                StateGis::CASO_ACEPTADO_EN_SEGUIMIENTO_REHABILITACION,
                StateGis::ORIGEN_REPORTADO_EN_AUDITORIA_MEDICA,
                StateGis::CASO_PCG_CALIFICADO_PENDIENTE_AUDITORIA_MEDICA,
                StateGis::MUERTE_REPORTADA_EN_AUDITORIA_MEDICA,
                StateGis::CASO_PCG_CALIFICADO_PENDIENTE_AUDITORIA_MEDICA,
                StateGis::CASO_CERRADO_POR_NO_REPORTAR_EL_FORMATO_FORMAL_DEL_CASO,
                StateGis::REGISTRADO
            ];

            //actualizar estado de la actividad
            //1. Buscar la última acción que cambio el estado de la actividad a CASO CERRADO
            $lastAction = ActivityAction::where('activity_id', $activity_gis->id)
                ->where('new_state_id', StateGis::CASO_CERRADO)
                ->whereIn('old_state_id', $ids_states)
                ->orderBy('created_at', 'desc')
                ->first();

            //validar si la acción existe
            if (!$lastAction) {
                //mensaje de error
                $error = 'Error al aprobar la reapertura, inconsistencias encontradas en el historial de acciones del servicio GIS';
                // Redirigir con el mensaje de error
                return redirect()->back()->withErrors($error)->withInput();
            }

            //action_id para actualizar el estado de la actividad
            $action_id = null;

            //validar la última acción y asignar el action_id
            switch ($lastAction->old_state_id) {
                case StateGis::CASO_ACEPTADO_EN_SEGUIMIENTO_REHABILITACION:
                    $action_id = ActionGisSort::APROBAR_REAPERTURA_CASO_ACEPTADO_EN_SEGUIMIENTO_REHABILITACION;
                    break;
                case StateGis::ORIGEN_REPORTADO_EN_AUDITORIA_MEDICA:
                    $action_id = ActionGisSort::APROBAR_REAPERTURA_ORIGEN_REPORTADO_AUDITORIA_MEDICA;
                    break;
                case StateGis::MUERTE_REPORTADA_EN_AUDITORIA_MEDICA:
                    $action_id = ActionGisSort::APROBAR_REAPERTURA_MUERTE_REPORTADA_AUDITORIA_MEDICA;
                    break;
                case StateGis::CASO_PCG_CALIFICADO_PENDIENTE_AUDITORIA_MEDICA:
                    $action_id = ActionGisSort::APROBAR_REAPERTURA_CASO_PCG_CALIFICADO_PENDIENTE_AUDITORIA_MEDICA;
                    break;
                case StateGis::CASO_CERRADO_POR_NO_REPORTAR_EL_FORMATO_FORMAL_DEL_CASO:
                    $action_id = ActionGisSort::APROBAR_REAPERTURA_CASO_CERRADO_POR_NO_REPORTAR_FORMATO_FORMAL_CASO;
                    break;
                case StateGis::REGISTRADO:
                    $action_id = ActionGisSort::APROBAR_REAPERTURA_REGISTRADO;
                    break;
            }

            //validar si el action_id existe
            if (!$action_id) {
                //mensaje de error
                $error = 'Error al aprobar la reapertura, inconsistencias encontradas en el historial de acciones del servicio GIS';
                // Redirigir con el mensaje de error
                return redirect()->back()->withErrors($error)->withInput();
            }

            //actualizar el estado de la actividad
           ActionController::create($activity_gis->id,
                $action_id,
                'APROBAR TRÁMITE FINAL DEL CASO CON REAPERTURA');

            //actualizar red flag de GIS_SORT
            $gis_sort = $activity_gis->gis_sort;

            //validar si el gis_sort existe
            if (!$gis_sort) {
                //mensaje de error
                $error = 'El GIS SORT no existe';
                // Redirigir con el mensaje de error
                return redirect()->back()->withErrors($error)->withInput();
            }

            //actualizar red flag
            $gis_sort->report_reopening = 0;
            $gis_sort->save();

            //variables para el envio de correo
            $nombre_tomador = $activity_policy->affiliate->full_name;
            $nombre_asegurado = $activity_gis->affiliate->full_name;
            $n_caso = !empty($activity_gis->gis_sort->consecutive) ? $activity_gis->gis_sort->consecutive : $activity_gis->gis_sort->consecutive_gis;
            $fecha_accidente = ucfirst(strftime('%A %e de %B del %Y', strtotime($activity_gis->gis_sort->date_accident))) ?? 'Fecha no disponible';

            //enviar correo al asegurado y tomador
            $emails = [$activity_policy->affiliate->email, $activity_gis->affiliate->email];
            $text = [
                "text" => "<strong>Estimado(a) {$nombre_tomador}</strong> y <strong>{$nombre_asegurado}:</strong><br><br>
                Nos complace informarle que, tras la revisión de la información y la autorización de la reapertura del caso del Seguro Obligatorio de Riesgos del Trabajo N° {$n_caso}, por el evento ocurrido el {$fecha_accidente}, se ha procedido con el siguiente trámite:<br><br>
                <strong>1. Reconocimiento de Prestaciones Médicas:</strong><br>
                Se han reconocido las prestaciones médicas correspondientes al caso.<br><br>
                <strong>2. Pago de Subsidio por Incapacidad Temporal:</strong><br>
                Se procederá, cuando corresponda, con el pago del subsidio por incapacidad temporal.<br><br>
                El trámite final del caso ha sido atendido exitosamente y se encuentra registrado en el Sistema Riesgos del Trabajo en Línea.<br><br>
                Para consultas adicionales, por favor comunicarse con el Área de Indemnizaciones, al correo electrónico <strong><EMAIL></strong> o al teléfono 4102-7600 ext. 8129-8130-8140.<br><br>
                Atentamente,<br><br>",

                "sender" => 'SENDER_MNK_SEGUROS'
            ];

            $mailSent = new SendDocumentDataBase(
                implode(',', $emails),
                "Aprobación de trámite final del caso con reapertura",
                "<EMAIL>",
                "Aprobación de trámite final del caso con reapertura",
                $text,
                "<EMAIL>",
                [],
                "send_document_db",
                $client,
                request()->getHost(),
                $activity_gis->id,
                '',
                $activity_gis->service->id
            );
            $mailSent->sendMail();

            DB::commit();

            if ($redirectToReopeing) {
                //retornar al tablero de indemnizaciones en la seccion de reaperturas
                return redirect('/tablero/indemnizaciones/reaperturas')->with('success', 'La solicitud de reapertura ha sido aprobada.');
            }

        } catch (\Exception $e) {
            // Hacer rollback si hay algún error
            DB::rollBack();

            // Capturar el mensaje de error
            $error = $e->getMessage();

            // Redirigir con el mensaje de error
            return redirect()->back()->withErrors($error)->withInput();
        }
    }

//ME-2756
    public
    function rejectReopening(Request $request, $cpath, $activity_id, $redirectToReopeing = true)
    {
        //Iniciamos la transacción
        DB::beginTransaction();
        try {
            //client
            $client = Client::where('path', $cpath)->firstOrFail();

            //capturar la actividad GIS
            $activity_gis = Activity::find($activity_id);

            //capturar motivo del rechazo
            $reason = $request->reason ?? '';

            //verificar si la actividad existe
            if (!$activity_gis) {
                //mensaje de error
                $error = 'La actividad no existe';
                // Redirigir con el mensaje de error
                return redirect()->back()->withErrors($error)->withInput();
            }

            //capturar la actividad de póliza
            $activity_policy = Activity::where('id', $activity_gis->parent_id)->first();

            //validar si la actividad de póliza existe
            if (!$activity_policy) {
                //mensaje de error
                $error = 'La actividad de póliza no existe';
                // Redirigir con el mensaje de error
                return redirect()->back()->withErrors($error)->withInput();
            }

            //actualizar estado de la actividad
            ActionController::create($activity_gis->id,
                ActionGisSort::RECHAZAR_TRAMITE_FINAL_DEL_CASO_CON_REAPERTURA,
                'RECHAZO DEL TRÁMITE FINAL DEL CASO CON REAPERTURA');

            //actualizar red flag de GIS_SORT
            $gis_sort = $activity_gis->gis_sort;

            //validar si el gis_sort existe
            if (!$gis_sort) {
                //mensaje de error
                $error = 'El GIS SORT no existe';
                // Redirigir con el mensaje de error
                return redirect()->back()->withErrors($error)->withInput();
            }

            //actualizar red flag
            $gis_sort->report_reopening = 0;
            $gis_sort->save();

            //variables para el envio de correo
            $nombre_tomador = $activity_policy->affiliate->full_name;
            $nombre_asegurado = $activity_gis->affiliate->full_name;
            $n_caso = !empty($activity_gis->gis_sort->consecutive) ? $activity_gis->gis_sort->consecutive : $activity_gis->gis_sort->consecutive_gis;
            $fecha_accidente = ucfirst(strftime('%A %e de %B del %Y', strtotime($activity_gis->gis_sort->date_accident))) ?? 'Fecha no disponible';
            $motivo = ucfirst(strtolower($reason)) ?? 'Motivo no disponible';

            //enviar correo al asegurado y tomador
            $emails = [$activity_policy->affiliate->email, $activity_gis->affiliate->email];
            $text = [
                "text" => "<strong>Estimado(a) {$nombre_tomador}</strong> y <strong>{$nombre_asegurado}:</strong><br><br>
                Lamentamos informarle que, tras la revisión de la información presentada, no es posible autorizar la reapertura del caso del Seguro Obligatorio de Riesgos del Trabajo N° {$n_caso}, por el evento ocurrido el {$fecha_accidente}, debido a los siguientes motivos:<br><br>
                <strong>1. Motivo:</strong> {$motivo}.<br><br>
                Debido a esto, no procede el reconocimiento de las prestaciones médicas, ni el pago de subsidio por incapacidad temporal asociado al caso.<br><br>
                Para consultas adicionales o aportar información adicional relacionada, por favor comunicarse con el Área de Indemnizaciones al correo electrónico <strong><EMAIL></strong> o al teléfono 4102-7600 ext. 8129-8130-8140.<br><br>
                Atentamente,<br><br>",

                "sender" => 'SENDER_MNK_SEGUROS'
            ];

            $mailSent = new SendDocumentDataBase(
                implode(',', $emails),
                "Rechazo del trámite final del caso con reapertura",
                "<EMAIL>",
                "Rechazo del trámite final del caso con reapertura",
                $text,
                "<EMAIL>",
                [],
                "send_document_db",
                $client,
                request()->getHost(),
                $activity_gis->id,
                '',
                $activity_gis->service->id
            );
            $mailSent->sendMail();

            DB::commit();

            if ($redirectToReopeing) {
                //retornar al tablero de indemnizaciones en la seccion de reaperturas
                return redirect('/tablero/indemnizaciones/reaperturas')->with('success', 'La solicitud de reapertura ha sido rechazada.');
            }

        } catch (\Exception $e) {
            // Hacer rollback si hay algún error
            DB::rollBack();

            // Capturar el mensaje de error
            $error = $e->getMessage();

            // Redirigir con el mensaje de error
            return redirect()->back()->withErrors($error)->withInput();
        }
    }

//ME-2757
    public
    function requestInformationReopening($cpath, $activity_id, $redirectToReopeing = true)
    {
        //Iniciamos la transacción
        DB::beginTransaction();
        try {
            //client
            $client = Client::where('path', $cpath)->firstOrFail();

            //capturar la actividad GIS
            $activity_gis = Activity::find($activity_id);

            //verificar si la actividad existe
            if (!$activity_gis) {
                //mensaje de error
                $error = 'La actividad no existe';
                // Redirigir con el mensaje de error
                return redirect()->back()->withErrors($error)->withInput();
            }

            //capturar la actividad de póliza
            $activity_policy = Activity::where('id', $activity_gis->parent_id)->first();

            //validar si la actividad de póliza existe
            if (!$activity_policy) {
                //mensaje de error
                $error = 'La actividad de póliza no existe';
                // Redirigir con el mensaje de error
                return redirect()->back()->withErrors($error)->withInput();
            }

            //validar si el gis_sort existe
            if (!$activity_gis->gis_sort) {
                //mensaje de error
                $error = 'El GIS SORT no existe';
                // Redirigir con el mensaje de error
                return redirect()->back()->withErrors($error)->withInput();
            }

            //crear la acción para la actividad
            ActionController::create($activity_gis->id,
                ActionGisSort::SOLICITAR_REQUISITOS_PAGO_INCAPACIDAD_REAPERTURA,
                'REQUISITOS PARA EL PAGO DE INCAPACIDAD TEMPORAL POR REAPERTURA DE CASO');

            // Consultar todas las actividades de prestaciones médicas y secundarias
            $activities_pms = Activity::where('parent_id', $activity_gis->id)
                ->whereIn('service_id', [
                    Service::SERVICE_MEDICAL_SERVICES_SORT_MNK,
                    Service::SERVICE_MEDICAL_SERVICES_SECONDARY_CARE_SORT_MNK
                ])
                ->pluck('id')
                ->toArray();

            // Agrupar todos los posibles parent_id (los PMS + la GIS original)
            $parentIds = array_merge($activities_pms, [$activity_gis->id]);

            // Seleccionar el servicio de incapacidad temporal (la más reciente)
            $activity_peit = Activity::whereIn('parent_id', $parentIds)
                ->where('service_id', Service::SERVICE_PE_IT_SORT_MNK)
                ->orderBy('created_at', 'desc')
                ->first();

            // Validar antes de acceder
            $peit_sort = null;
            if ($activity_peit) {
                $peit_sort = PeItSort::where('activity_id', $activity_peit->id)->first();
            }

            //validar si el peit_sort existe
            if (!$peit_sort) {
                //mensaje de error
                $error = 'El servicio de incapacidad temporal no existe';
                // Redirigir con el mensaje de error
                return redirect()->back()->withErrors($error)->withInput();
            }

            //las fechas inicio y fin de la incapacidad está en la tabla peit_inhability_sort
            $peit_inhability_sort = PeitInabilitySort::where('pe_it_sort_id', $peit_sort->id)->first();

            //validar si el peit_inhability_sort existe
            if (!$peit_inhability_sort) {
                //mensaje de error
                $error = 'Error al leer los datos del servicio de incapacidad temporal';
                // Redirigir con el mensaje de error
                return redirect()->back()->withErrors($error)->withInput();
            }


            //variables para el envio de correo
            $nombre_asegurado = $activity_gis->affiliate->full_name;
            $n_caso = !empty($activity_gis->gis_sort->consecutive) ? $activity_gis->gis_sort->consecutive : $activity_gis->gis_sort->consecutive_gis;
            $fecha_accidente = ucfirst(strftime('%A %e de %B del %Y', strtotime($activity_gis->gis_sort->date_accident))) ?? 'Fecha no disponible';

            //las fechas inicio y fin de la incapacidad está en la tabla peit_inhability_sort
            $fecha_desde_incapacidad = $peit_inhability_sort->start_date;
            $fecha_hasta_incapacidad = $peit_inhability_sort->end_date;

            $fecha_desde_incapacidad = $fecha_desde_incapacidad ? ucfirst(strftime('%A %e de %B del %Y', strtotime($fecha_desde_incapacidad))) : 'Fecha no disponible';
            $fecha_hasta_incapacidad = $fecha_hasta_incapacidad ? ucfirst(strftime('%A %e de %B del %Y', strtotime($fecha_hasta_incapacidad))) : 'Fecha no disponible';

            //enviar correo al asegurado y tomador
            $emailAsegurado = [$activity_gis->affiliate->email];
            $emailTomador = [$activity_policy->affiliate->email];
            $text = [
                "text" => "<strong>Estimado(a)</strong> {$nombre_asegurado}:<br><br>
            Debido a que el caso del Seguro Obligatorio de Riesgos del Trabajo <strong>{$n_caso}</strong>, por el evento ocurrido a su persona el <strong>{$fecha_accidente}</strong> registra tres o más reaperturas, para continuar con el trámite de autorización del pago de incapacidad temporal del periodo <strong>{$fecha_desde_incapacidad}</strong> a <strong>{$fecha_hasta_incapacidad}</strong>, es necesario demostrar la pérdida económica sufrida, para lo cual le solicitamos presentar alguno de los siguientes requisitos:<br>
            1. Constancia de salario actual<br>
            2. Certificación vigente de la CCSS como asegurado de un patrono (SICERE)<br>
            3. Declaración jurada de ingresos variables<br>
            4. Certificación Notarial de ingresos<br><br>
            Para consultas adicionales o aportar la documentación requerida, puede comunicarse con el Área de Indemnizaciones al correo electrónico <strong><EMAIL></strong> o al teléfono 4102-7600 ext. 8129-8130-8140.<br><br>
            Atentamente,<br><br>",

                "sender" => 'SENDER_MNK_SEGUROS'
            ];

            // Enviar a asegurado
            $mailSent = new SendDocumentDataBase(
                implode(',', $emailAsegurado),
                "Requisitos para el pago de incapacidad temporal por reapertura de caso",
                "<EMAIL>",
                "Requisitos para el pago de incapacidad temporal por reapertura de caso",
                $text,
                "<EMAIL>",
                [],
                "send_document_db",
                $client,
                request()->getHost(),
                $activity_gis->id,
                '',
                $activity_gis->service->id
            );
            $mailSent->sendMail();

            // Enviar a tomador
            $mailSent = new SendDocumentDataBase(
                implode(',', $emailTomador),
                "Requisitos para el pago de incapacidad temporal por reapertura de caso",
                "<EMAIL>",
                "Requisitos para el pago de incapacidad temporal por reapertura de caso",
                $text,
                "<EMAIL>",
                [],
                "send_document_db",
                $client,
                request()->getHost(),
                $activity_gis->id,
                '',
                $activity_gis->service->id
            );
            $mailSent->sendMail();

            DB::commit();

            if ($redirectToReopeing) {
                //retornar al tablero de indemnizaciones en la seccion de reaperturas
                return redirect('/tablero/indemnizaciones/reaperturas')->with('success', 'Se ha procesado la solicitud con exito.');
            }

        } catch (\Exception $e) {
            // Hacer rollback si hay algún error
            DB::rollBack();

            // Capturar el mensaje de error
            $error = $e->getMessage();

            // Redirigir con el mensaje de error
            return redirect()->back()->withErrors($error)->withInput();
        }
    }

    public
    function requestInformationReopeningAutomatic($cpath, $activity_id)
    {
        //client
        $client = Client::where('path', $cpath)->firstOrFail();

        //capturar la actividad GIS
        $activity_gis = Activity::find($activity_id);

        //verificar si la actividad existe
        if (!$activity_gis) {
            //mensaje de error
            $error = 'La actividad no existe';
            // Redirigir con el mensaje de error
            return redirect()->back()->withErrors($error)->withInput();
        }

        //capturar la actividad de póliza
        $activity_policy = Activity::where('id', $activity_gis->parent_id)->first();

        //validar si la actividad de póliza existe
        if (!$activity_policy) {
            //mensaje de error
            $error = 'La actividad de póliza no existe';
            // Redirigir con el mensaje de error
            return redirect()->back()->withErrors($error)->withInput();
        }

        //validar si el gis_sort existe
        if (!$activity_gis->gis_sort) {
            //mensaje de error
            $error = 'El GIS SORT no existe';
            // Redirigir con el mensaje de error
            return redirect()->back()->withErrors($error)->withInput();
        }

        // Consultar todas las actividades de prestaciones médicas y secundarias
        $activities_pms = Activity::where('parent_id', $activity_gis->id)
            ->whereIn('service_id', [
                Service::SERVICE_MEDICAL_SERVICES_SORT_MNK,
                Service::SERVICE_MEDICAL_SERVICES_SECONDARY_CARE_SORT_MNK
            ])
            ->pluck('id')
            ->toArray();

        // Agrupar todos los posibles parent_id (los PMS + la GIS original)
        $parentIds = array_merge($activities_pms, [$activity_gis->id]);

        // Seleccionar el servicio de incapacidad temporal (la más reciente)
        $activity_peit = Activity::whereIn('parent_id', $parentIds)
            ->where('service_id', Service::SERVICE_PE_IT_SORT_MNK)
            ->orderBy('created_at', 'desc')
            ->first();

        //crear la acción para la actividad
        ActionController::create($activity_gis->id,
            ActionGisSort::SOLICITAR_REQUISITOS_PAGO_INCAPACIDAD_REAPERTURA,
            'REQUISITOS PARA EL PAGO DE INCAPACIDAD TEMPORAL POR REAPERTURA DE CASO');

        //capturar el peit_sort
        if ($activity_peit) {
            $peit_sort = PeItSort::where('activity_id', $activity_peit->id)->first();
        }

        //validar si el peit_sort existe
        if (!$peit_sort) {
            //mensaje de error
            $error = 'El servicio de incapacidad temporal no existe';
            // Redirigir con el mensaje de error
            return redirect()->back()->withErrors($error)->withInput();
        }

        //las fechas inicio y fin de la incapacidad está en la tabla peit_inhability_sort
        $peit_inhability_sort = PeitInabilitySort::where('pe_it_sort_id', $peit_sort->id)->first();

        //validar si el peit_inhability_sort existe
        if (!$peit_inhability_sort) {
            //mensaje de error
            $error = 'Error al leer los datos del servicio de incapacidad temporal';
            // Redirigir con el mensaje de error
            return redirect()->back()->withErrors($error)->withInput();
        }

        //variables para el envio de correo
        $nombre_asegurado = $activity_gis->affiliate->full_name;
        $n_caso = !empty($activity_gis->gis_sort->consecutive) ? $activity_gis->gis_sort->consecutive : $activity_gis->gis_sort->consecutive_gis;
        $fecha_accidente = ucfirst(strftime('%A %e de %B del %Y', strtotime($activity_gis->gis_sort->date_accident))) ?? 'Fecha no disponible';

        //las fechas inicio y fin de la incapacidad está en la tabla peit_inhability_sort
        $fecha_desde_incapacidad = $peit_inhability_sort->start_date;
        $fecha_hasta_incapacidad = $peit_inhability_sort->end_date;

        $fecha_desde_incapacidad = $fecha_desde_incapacidad ? ucfirst(strftime('%A %e de %B del %Y', strtotime($fecha_desde_incapacidad))) : 'Fecha no disponible';
        $fecha_hasta_incapacidad = $fecha_hasta_incapacidad ? ucfirst(strftime('%A %e de %B del %Y', strtotime($fecha_hasta_incapacidad))) : 'Fecha no disponible';

        //enviar correo al asegurado y tomador
        $emailAsegurado = [$activity_gis->affiliate->email];
        $emailTomador = [$activity_policy->affiliate->email];
        $text = [
            "text" => "<strong>Estimado(a)</strong> {$nombre_asegurado}:<br><br>
        Debido a que el caso del Seguro Obligatorio de Riesgos del Trabajo <strong>{$n_caso}</strong>, por el evento ocurrido a su persona el <strong>{$fecha_accidente}</strong> registra tres o más reaperturas, para continuar con el trámite de autorización del pago de incapacidad temporal del periodo <strong>{$fecha_desde_incapacidad}</strong> a <strong>{$fecha_hasta_incapacidad}</strong>, es necesario demostrar la pérdida económica sufrida, para lo cual le solicitamos presentar alguno de los siguientes requisitos:<br>
        1. Constancia de salario actual<br>
        2. Certificación vigente de la CCSS como asegurado de un patrono (SICERE)<br>
        3. Declaración jurada de ingresos variables<br>
        4. Certificación Notarial de ingresos<br><br>
        Para consultas adicionales o aportar la documentación requerida, puede comunicarse con el Área de Indemnizaciones al correo electrónico <strong><EMAIL></strong> o al teléfono 4102-7600 ext. 8129-8130-8140.<br><br>
        Atentamente,<br><br>",

            "sender" => 'SENDER_MNK_SEGUROS'
        ];

        // Enviar a asegurado
        $mailSent = new SendDocumentDataBase(
            implode(',', $emailAsegurado),
            "Requisitos para el pago de incapacidad temporal por reapertura de caso",
            "<EMAIL>",
            "Requisitos para el pago de incapacidad temporal por reapertura de caso",
            $text,
            "<EMAIL>",
            [],
            "send_document_db",
            $client,
            request()->getHost(),
            $activity_gis->id,
            '',
            $activity_gis->service->id
        );
        $mailSent->sendMail();

        // Enviar a tomador
        $mailSent = new SendDocumentDataBase(
            implode(',', $emailTomador),
            "Requisitos para el pago de incapacidad temporal por reapertura de caso",
            "<EMAIL>",
            "Requisitos para el pago de incapacidad temporal por reapertura de caso",
            $text,
            "<EMAIL>",
            [],
            "send_document_db",
            $client,
            request()->getHost(),
            $activity_gis->id,
            '',
            $activity_gis->service->id
        );
        $mailSent->sendMail();
    }


//ME-2758
    public
    function inconsistenciesReopening($cpath, $activity_id)
    {
        //Iniciamos la transacción
        DB::beginTransaction();
        try {
            //client
            $client = Client::where('path', $cpath)->firstOrFail();

            //capturar la actividad GIS
            $activity_gis = Activity::find($activity_id);

            //verificar si la actividad existe
            if (!$activity_gis) {
                //mensaje de error
                $error = 'La actividad no existe';
                // Redirigir con el mensaje de error
                return redirect()->back()->withErrors($error)->withInput();
            }

            //capturar la actividad de póliza
            $activity_policy = Activity::where('id', $activity_gis->parent_id)->first();

            //validar si la actividad de póliza existe
            if (!$activity_policy) {
                //mensaje de error
                $error = 'La actividad de póliza no existe';
                // Redirigir con el mensaje de error
                return redirect()->back()->withErrors($error)->withInput();
            }

            //validar si el gis_sort existe
            if (!$activity_gis->gis_sort) {
                //mensaje de error
                $error = 'El GIS SORT no existe';
                // Redirigir con el mensaje de error
                return redirect()->back()->withErrors($error)->withInput();
            }

            //crear la acción para la actividad
            ActionController::create($activity_gis->id,
                ActionGisSort::REPORTAR_INCONSISTENCIA_EN_REQUISITOS,
                'INCONSISTENCIA EN REQUISITOS');

            // Consultar todas las actividades de prestaciones médicas y secundarias
            $activities_pms = Activity::where('parent_id', $activity_gis->id)
                ->whereIn('service_id', [
                    Service::SERVICE_MEDICAL_SERVICES_SORT_MNK,
                    Service::SERVICE_MEDICAL_SERVICES_SECONDARY_CARE_SORT_MNK
                ])
                ->pluck('id')
                ->toArray();

            // Agrupar todos los posibles parent_id (los PMS + la GIS original)
            $parentIds = array_merge($activities_pms, [$activity_gis->id]);

            // Seleccionar el servicio de incapacidad temporal (la más reciente)
            $activity_peit = Activity::whereIn('parent_id', $parentIds)
                ->where('service_id', Service::SERVICE_PE_IT_SORT_MNK)
                ->orderBy('created_at', 'desc')
                ->first(); 

            // Validar antes de acceder
            $peit_sort = null;
            if ($activity_peit) {
                $peit_sort = PeItSort::where('activity_id', $activity_peit->id)->first();
            }

            //validar si el peit_sort existe
            if (!$peit_sort) {
                //mensaje de error
                $error = 'El servicio de incapacidad temporal no existe';
                // Redirigir con el mensaje de error
                return redirect()->back()->withErrors($error)->withInput();
            }

            //las fechas inicio y fin de la incapacidad está en la tabla peit_inhability_sort
            $peit_inhability_sort = PeitInabilitySort::where('pe_it_sort_id', $peit_sort->id)->first();

            //validar si el peit_inhability_sort existe
            if (!$peit_inhability_sort) {
                //mensaje de error
                $error = 'Error al leer los datos del servicio de incapacidad temporal';
                // Redirigir con el mensaje de error
                return redirect()->back()->withErrors($error)->withInput();
            }

            //variables para el envio de correo
            $nombre_asegurado = $activity_gis->affiliate->full_name;
            $n_caso = !empty($activity_gis->gis_sort->consecutive) ? $activity_gis->gis_sort->consecutive : $activity_gis->gis_sort->consecutive_gis;
            $fecha_accidente = ucfirst(strftime('%A %e de %B del %Y', strtotime($activity_gis->gis_sort->date_accident))) ?? 'Fecha no disponible';

            //las fechas inicio y fin de la incapacidad está en la tabla peit_inhability_sort
            $fecha_desde_incapacidad = $peit_inhability_sort->start_date;
            $fecha_hasta_incapacidad = $peit_inhability_sort->end_date;

            $fecha_desde_incapacidad = $fecha_desde_incapacidad ? ucfirst(strftime('%A %e de %B del %Y', strtotime($fecha_desde_incapacidad))) : 'Fecha no disponible';
            $fecha_hasta_incapacidad = $fecha_hasta_incapacidad ? ucfirst(strftime('%A %e de %B del %Y', strtotime($fecha_hasta_incapacidad))) : 'Fecha no disponible';

            //enviar correo al asegurado y tomador
            $emailAsegurado = [$activity_gis->affiliate->email];
            $emailTomador = [$activity_policy->affiliate->email];

            $text = [
                "text" => "<strong>Estimado(a)</strong> {$nombre_asegurado}:<br><br>
            Lamentamos informarle que, tras la revisión de la información presentada como requisito para el
            trámite de autorización del pago de incapacidad temporal del periodo {$fecha_desde_incapacidad} a {$fecha_hasta_incapacidad},
            en el caso del Seguro Obligatorio de Riesgos del Trabajo {$n_caso}, por el evento ocurrido
            a su persona el {$fecha_accidente}, se detectaron inconsistencias que impiden continuar con dicho trámite,
            por lo que se solicita comunicarse con el Área de Indemnizaciones, al correo electrónico
            <strong><EMAIL></strong> o al teléfono 4102-7600 ext. 8129-8130-8140, para aclarar
            lo que corresponda.<br><br>
            Atentamente,<br><br>",

                "sender" => 'SENDER_MNK_SEGUROS'
            ];

            //enviar correo al asegurado
            $mailSent = new SendDocumentDataBase(
                implode(',', $emailAsegurado),
                "Inconsistencia en requisitos",
                "<EMAIL>",
                "Inconsistencia en requisitos",
                $text,
                "<EMAIL>",
                [],
                "send_document_db",
                $client,
                request()->getHost(),
                $activity_gis->id,
                '',
                $activity_gis->service->id
            );
            $mailSent->sendMail();

            //enviar correo al tomador
            $mailSent = new SendDocumentDataBase(
                implode(',', $emailTomador),
                "Inconsistencia en requisitos",
                "<EMAIL>",
                "Inconsistencia en requisitos",
                $text,
                "<EMAIL>",
                [],
                "send_document_db",
                $client,
                request()->getHost(),
                $activity_gis->id,
                '',
                $activity_gis->service->id
            );
            $mailSent->sendMail();

            DB::commit();

            //retornar al tablero de indemnizaciones en la seccion de reaperturas
            return redirect('/tablero/indemnizaciones/reaperturas')->with('success', 'Se ha procesado la solicitud con exito.');
        } catch (\Exception $e) {
            // Hacer rollback si hay algún error
            DB::rollBack();

            // Capturar el mensaje de error
            $error = $e->getMessage();

            // Redirigir con el mensaje de error
            return redirect()->back()->withErrors($error)->withInput();
        }
    }

    public function medicaBills(Request $req, $cpath)
    {
        // Obtener el usuario autenticado
        $user = Auth::user();

        if (is_null($user)) {
            return redirect()->back()->with('error', 'Usuario no encontrado.');
        }

        // Filtros de fechas
        $action_start_date = $req->input('action_start_date_submit'); // Formato: 'Y-m-d'
        $action_end_date = $req->input('action_end_date_submit'); // Formato: 'Y-m-d'

        // Inicializar la consulta base de actividades médicas
        $activityQuery = Activity::query();

        // Aplicar filtros según el rol del usuario
        switch (true) { // Usamos switch (true) para evaluar condiciones booleanas
            case $user->isProvider(): // Proveedor
                // Filtrar por existencia de medical_bill y por affiliate_id
                $activityQuery->whereHas('socialSecurityProvider')
                    ->where('affiliate_id', $user->provider->affiliate_id);
                break;
            default:
                $activityQuery->whereHas('socialSecurityProvider');
                break;
        }

        $activityQuery->when($req->input('provider'), function ($query) use ($req) {
            $query->whereHas('socialSecurityProvider', function ($query) use ($req) {
                $query->where('provider_id', $req->input('provider'));
            });
        });

        // Procesar y aplicar los filtros de fechas
        $activityQuery = $this->applyDateFilters($activityQuery, $action_start_date, $action_end_date);

        // Ordenar por la columna 'created_at' en orden descendente
        $activityQuery->orderBy('created_at', 'desc');

        // Obtener los resultados paginados
        $activities = $activityQuery->paginate(10)
            ->appends(request()->only('action_start_date_submit', 'action_end_date_submit'));

        $providers = Provider::where('active', 1)
            ->whereNull('deleted_at')
            ->orderBy('name', 'asc')
            ->get();

        return view('table.compensation.medical_bills', [
            'active' => 'cuentas_medicas',
            'activities' => $activities,
            'id' =>  $user->id, // Esto es para que el id de la ruta hacia la vista servicios asignados
            'user' => $user,
            'providers' => $providers,
        ]);
    }

    private function applyDateFilters($query, $startDate, $endDate)
    {
        if (!empty($startDate)) {
            $startDate = Carbon::createFromFormat('Y-m-d', $startDate)->startOfDay();
        }

        if (!empty($endDate)) {
            $endDate = Carbon::createFromFormat('Y-m-d', $endDate)->endOfDay();
        }

        if (!empty($startDate) && !empty($endDate)) {
            // Filtrar entre las fechas de inicio y fin
            $query->whereBetween('created_at', [$startDate, $endDate]);
        } elseif (!empty($startDate)) {
            // Filtrar desde la fecha de inicio
            $query->where('created_at', '>=', $startDate);
        } elseif (!empty($endDate)) {
            // Filtrar hasta la fecha de fin
            $query->where('created_at', '<=', $endDate);
        }

        return $query;
    }

}