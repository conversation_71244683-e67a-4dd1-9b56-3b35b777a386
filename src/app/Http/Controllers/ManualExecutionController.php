<?php

namespace App\Http\Controllers;

use App\Action;
use App\Activity;
use App\ActivityAction;
use App\ActivityActionDocument;
use App\Client;
use App\Http\Controllers\Services\PolicySortCollectionController;
use App\Http\Controllers\Tables\MailBoardController;
use App\Http\Controllers\Services\PolicySortController;
use App\Http\Middleware\NumberToWords;
use App\Mail\SendDocumentDataBase;
use App\MailTemplates\Constants\Templates;
use App\MailTemplates\TemplateBuilder;
use App\PolicySortCollection;
use App\Providers\AppServiceProvider;
use App\Service;
use App\State;
use Carbon\Carbon;
use DateTime;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use PDF;
use App\QuotationConditionSpecial;
use App\Actions\ActionCotizacionSort;

class ManualExecutionController extends Controller
{
    protected $policySortController;

    public function __construct()
    {
        $this->policySortController = app()->make(PolicySortController::class);
    }
    
    public function index()
    {
//        if (Str::endsWith(Auth::user()->email, '@rengroup.co')){
            return view('manual_execution.manual_execution');
//        }
//        return redirect('/');
    }
    public function save(Request $request,$cpath)
    {
        try {
            if ($request->id_activity == '') {
                throw new \Exception('No se ingreso ninguna id de actividad');
            }
            if($request->total_amount == ''){
                throw new \Exception('No se ingreso el monto total');
            }
            if($request->validity_from == ''){
                throw new \Exception('No se ingreso la fecha de validez desde');
            }
            if ($request->validity_to == '') {
                throw new \Exception('No se ingreso la fecha de validez hasta');
            }
            if ($request->due_date == '') {
                throw new \Exception('No se ingreso la fecha de vencimiento');
            }
            $client = Client::where('path', $cpath)->firstOrFail();
            $id_activity = $request->id_activity;
            $total_amount = $request->total_amount;
            $validity_from = $request->validity_from;
            $validity_to = $request->validity_to;
            //variables para la bd
            $validity_from_mysql = Carbon::createFromFormat('d-m-Y', $validity_from)->format('Y-m-d');
            $validity_to_mysql = Carbon::createFromFormat('d-m-Y', $validity_to)->format('Y-m-d');
            $due_date = Carbon::createFromFormat('d-m-Y', $request->due_date)->toDateString();;
            $activity_policy = Activity::where('client_id', $client->id)
                ->where('id', $id_activity)
                ->first();
            if (!$activity_policy) {
                throw new \Exception('La actividad ' . $id_activity . ' no existe');
            }
            DB::beginTransaction();
            $activity_policy_collection = new Activity;
            $activity_policy_collection->parent_id = $activity_policy->id;
            $activity_policy_collection->client_id = $client->id;
            $activity_policy_collection->service_id = Service::SERVICE_POLICY_SORT_COLLECTION_MNK;
            $activity_policy_collection->affiliate_id = $activity_policy->affiliate_id;
            $activity_policy_collection->user_id = $activity_policy->user_id;
            $activity_policy_collection->state_id = State::REGISTRADO;
            $activity_policy_collection->created_at = Carbon::now()->format('Y-m-d H:i:s');
            $activity_policy_collection->save();

            switch ($activity_policy->policy_sort->periodicity)
            {
                case 2:
                    $activity_action = $this->reportPaymentReceipt($activity_policy_collection->id,$total_amount,$due_date,'semestral',$validity_from_mysql,$validity_to_mysql);
                    break;
                case 3:
                    $activity_action = $this->reportPaymentReceipt($activity_policy_collection->id,$total_amount,$due_date,'trimestral',$validity_from_mysql,$validity_to_mysql);
                    break;
                case 4:
                    $activity_action = $this->reportPaymentReceipt($activity_policy_collection->id,$total_amount,$due_date,'mensual',$validity_from_mysql,$validity_to_mysql);
                    break;
            }
            $this->sendPaymentReceipt($client,$validity_from,$validity_to,$activity_policy_collection,$activity_action);
            DB::commit();
        }catch (\Exception $e){
            DB::rollBack();
            return redirect()->back()->with('error', $e->getMessage())->withInput();
        }
        return redirect()->back()->with('success', 'cobro creado correctamente ' . $activity_policy_collection->id);
    }

    //Reportar recibos de abonos cobros
    public function reportPaymentReceipt($id, $total_amount, $due_date, $type,$validity_from = null, $validity_to = null)
    {
        $policyCollectionActivity = Activity::where('id', $id)->firstOrFail();

        try {
            // Mapeamos el tipo de abono a sus constantes correspondientes
            $types = [
                'mensual' => [
                    'action' => ACTION::REPORTAR_RECIBO_ABONO_MENSUAL,
                    'invoice_prefix' => 'Mensual-',
                    'type_receipt' => PolicySortCollection::MONTHLY
                ],
                'trimestral' => [
                    'action' => ACTION::REPORTAR_RECIBO_ABONO_TRIMESTRAL,
                    'invoice_prefix' => 'Trimestral-',
                    'type_receipt' => PolicySortCollection::QUARTERLY
                ],
                'semestral' => [
                    'action' => ACTION::REPORTAR_RECIBO_ABONO_SEMESTRAL,
                    'invoice_prefix' => 'Semestral-',
                    'type_receipt' => PolicySortCollection::SEMIANNUAL
                ],
            ];

            // Verificamos si el tipo es válido
            if (!isset($types[$type])) {
                throw new \Exception("Tipo de recibo no válido: $type");
            }

            // Crear acción
            $activityActionsCreated = ActionController::create(
                $policyCollectionActivity->id,
                $types[$type]['action'],
                'REPORTAR RECIBO ABONO ' . strtoupper($type)
            );

            // Crear el recibo
            $policy_sort_collection = new PolicySortCollection();
            $policy_sort_collection->activity_id = $id;
            $policy_sort_collection->type_receipt = $types[$type]['type_receipt'];
            $policy_sort_collection->invoice_number = $types[$type]['invoice_prefix'] . uniqid();
            $policy_sort_collection->total_amount = $total_amount;
            $policy_sort_collection->payment_status = PolicySortCollection::PAYMENT_STATUS_PENDING;
            $policy_sort_collection->due_date = $due_date;
            $policy_sort_collection->validity_from = $validity_from;
            $policy_sort_collection->validity_to = $validity_to;
            $policy_sort_collection->save();

        } catch (Exception $e) {
            throw new \Exception($e);
        }

        return $activityActionsCreated;
    }

    //enviar recibo de pago
    public function sendPaymentReceipt($client,$validity_from,$validity_to,$activity_policy_collection,$activity_action)
    {
        try {
            date_default_timezone_set('America/Costa_Rica');
            $horaActual = new DateTime();
            $horaCostarrica = $horaActual->format('h:i:s A');
            $fechaCostarrica = $horaActual->format('d/m/Y');


            $activity_policy = $activity_policy_collection->parent;
            $totalWords = NumberToWords::convertToWords($activity_policy_collection->policy_sort_collection->total_amount, $activity_policy->policy_sort->type_currency);
            $validityFrom = Carbon::parse($validity_from);
            $validityTo = Carbon::parse($validity_to);

            $quotation = $activity_policy->parent;
            $condiciones = ActivityAction::where('action_id', '=', ActionCotizacionSort::REPORTAR_CONDICIONES_ESPECIALES)->where('activity_id', '=', $quotation->id)->first();

            $work_modality_id = $activity_policy->policy_sort->work_modality_id;
            $type_receipt = $activity_policy_collection->policy_sort_collection->type_receipt;

            if ($condiciones) {
                $quotationCondition = QuotationConditionSpecial::where('activity_id', '=', $quotation->id)->first();
                [$descuentoCml, $descuentoClap, $base, $final, $descuentos_total,$sumaDescuentos] = array_values(
                    $this->policySortController->calcularDescuentos(
                        $activity_policy->policy_sort,
                        $quotationCondition
                    )
                );
            } else {
                $total = $activity_policy_collection->policy_sort_collection->total_amount;
            }

            //formatear en dia/mes y año
            $validityFromFormatDDMMYY = $validityFrom->format('d/m/Y');
            $validityToFormatDDMMYY = $validityTo->format('d/m/Y');

            $datos = [
                'activity_policy' => $activity_policy,
                'activity_policy_collection' => $activity_policy_collection,
                'totalWords' => $totalWords,
                'date_from' => $validityFromFormatDDMMYY,
                'date_to' => $validityToFormatDDMMYY,
                'watermark' => false,
                'horaCostarrica' => $horaCostarrica,
                'fechaCostarrica' => $fechaCostarrica,
                'pagado' => null,
                'descuentoCml' => $condiciones ? $descuentoCml : null,
                'descuentoClap' => $condiciones ? $descuentoClap : null,
                'base' => $condiciones ? $base : $total,
                'final' => $condiciones ? $final : $total,
                'descuentos_total' => $condiciones ? $descuentos_total : 0,
                'sumaDescuentos'=> $condiciones ? $sumaDescuentos :0,
                'quotationCondition' => $condiciones ? $quotationCondition : null,
                'work_modality_id' => $work_modality_id,
                'type_receipt'=> $type_receipt ?? ''
            ];


            $indexDoc = $activity_policy_collection->policy_sort_collection->type_receipt == 'emission'
                ? $activity_policy->policy_sort->periodicity
                : $activity_policy_collection->policy_sort_collection->type_receipt;

            $document = AppServiceProvider::$PERIODICITY_RECEIPTS[$indexDoc] ?? 'unknown_receipt_type';

            $pdf = PDF::loadView("services.policy_sort_collection.docs.monthly_payment_receipt", $datos);
            $numPoliza = $activity_policy->policy_sort->consecutive;

            //Guardamos el archivo en S3
            $filePath = "policyCollectionSort/{$document}_{$numPoliza}_$horaCostarrica.pdf";
            Storage::disk('s3')->put("$filePath", $pdf->output());

            $activityActionDocument = new ActivityActionDocument();
            $activityActionDocument->activity_action_id = $activity_action->id;
            $activityActionDocument->name = $document;
            $activityActionDocument->path = "$filePath";
            $activityActionDocument->save();

            $nameTaker = mb_convert_case(mb_strtolower($activity_policy->affiliate->full_name ?? ''), MB_CASE_TITLE, "UTF-8");
            $datePyment = ucfirst(strftime('%A %e de %B del %Y', strtotime($activity_policy_collection->policy_sort_collection->due_date)));
            $numPoliza = $activity_policy->policy_sort->consecutive ? $activity_policy->policy_sort->formatNumberConsecutive() : '';
            $validityFromFormat = $validityFrom->formatLocalized('%e de %B del %Y');
            $validityToFormat = $validityTo->formatLocalized('%e de %B del %Y');

            $emailData = TemplateBuilder::build(
                Templates::PAYMENT_COLLECTION,
                [
                    'policy_sort' => $numPoliza,
                    'name_taker' => $nameTaker,
                    'start_period' => $validityFromFormat,
                    'end_period' => $validityToFormat,
                    'payment_date' => $datePyment,
                ]
            );

            $attachments = [
                [
                    'path' => $filePath,
                    'name' => basename($filePath),
                    'type' => 'PDF'
                ]
            ];

            // Agrega el email principal de notificaciones y todos los adicionales
            $emails = PolicySortController::getAdditionalNotificationEmails(
                $activity_policy->policy_sort->id,
                [
                    $activity_policy->policy_sort->email
                ]
            );

            $mailSent = new SendDocumentDataBase(
                implode(',', $emails),
                $emailData['subject'],
                "<EMAIL>",
                $emailData['subject'],
                [
                    "text" => $emailData['body'],
                    "sender" => $emailData['sender']
                ],
                "<EMAIL>",
                $attachments,
                "send_document_db",
                $client,
                request()->getHost(),
                $activity_policy_collection->id,
                $activity_action->id,
                $activity_policy_collection->service->id
            );

            // Capturar el resultado del envío
            $result = $mailSent->sendMail();

            //Registramos los datos del correo enviado para la trazabilidad
            $mailBoardController = new MailBoardController();
            $mailBoardController->createRegisterMail(
                $activity_policy_collection->id,
                $activity_policy_collection->service->id, 
                $activity_policy->policy_sort->consecutive, 
                'Tomador', 
                $nameTaker, 
                $activity_policy->affiliate->doc_number,
                $emailData['subject'],
                $emailData['body'],
                $emails, 
                $result,
                $attachments
            );

            return true;
        } catch (\Exception $e) {
            throw new \Exception('Error en el envio del correo de aprobación de pago ' . $e->getMessage());
        }
    }

    public function actualizaRecibos($cpath){

        $activity_policy_collection = Activity::whereIn('id', [0000])->get();

        foreach ($activity_policy_collection as $activity) {
            $this->createAndReplaceReceipts($cpath, $activity->id, 1);
            echo $activity->id.', ';
        }

    }

    //generar comprobantes de pagado manual mente
    public function createAndReplaceReceipts($cpath, $activityPayment,$monthPassed = null)
    {
        $client = Client::where('path', $cpath)->firstOrFail();

        try {
            date_default_timezone_set('America/Costa_Rica');
            $horaActual = new DateTime();
            $horaCostarrica = $horaActual->format('h:i:s A');
            $fechaCostarrica = $horaActual->format('d/m/Y');

            $activity_policy_collection = Activity::where('client_id', $client->id)->where('id', $activityPayment)->firstOrFail();
            $activity_policy = $activity_policy_collection->parent;
            $totalWords = NumberToWords::convertToWords($activity_policy_collection->policy_sort_collection->total_amount ?? 0, $activity_policy->policy_sort->type_currency);

            //fecha del recibo
            $validityFrom = \Illuminate\Support\Carbon::parse($activity_policy->policy_sort->validity_from);
            $validityTo = Carbon::parse($activity_policy->policy_sort->validity_to);
            $intervals = [
                0 => $validityTo,
                1 => $validityTo,
                2 => $validityFrom->copy()->addMonths(6)->subDay(),
                3 => $validityFrom->copy()->addMonths(3)->subDay(),
                4 => $validityFrom->copy()->addMonth()->subDay(),
            ];
            $validityTo = isset($intervals[$activity_policy->policy_sort->periodicity]) ? $intervals[$activity_policy->policy_sort->periodicity] : $validityTo;
            //buscar cuantos recibos lleva para sumarle los meses
            $receiptCount = Activity::where('parent_id', $activity_policy->id)
                ->where('service_id', Service::SERVICE_POLICY_SORT_COLLECTION_MNK)
                ->count();
            $receiptIndex = max(0, $receiptCount - 1);
            $monthsToAddArray = [
                4 => 1,
                3 => 3,
                2 => 6,
            ];
            $monthsToAdd = isset($monthsToAddArray[$activity_policy->policy_sort->periodicity])
                ? $monthsToAddArray[$activity_policy->policy_sort->periodicity]
                : 0;
            $monthPassed = $receiptIndex  * $monthsToAdd;

            if ($monthPassed) {
                $validityFrom = \Illuminate\Support\Carbon::parse($validityFrom)->addMonths($monthPassed);
                $validityTo = Carbon::parse($validityTo)->addMonths($monthPassed);
            }
            //formatear en dia/mes y año
            $validityFrom = $validityFrom->format('d/m/Y');
            $validityTo = $validityTo->format('d/m/Y');

            //dd($validityFrom, $validityTo, $activity_policy->policy_sort->validity_to);

            $descuento = 0;
            $descuentoFraccionamiento = 0;
            $totalsinDescuento = 0;

            if ($activity_policy->policy_sort->special_condition) {
                if ($activity_policy->policy_sort->temporality == 'short') {
                    $descuento = $activity_policy->policy_sort->preventive_actions ?? 0;
                } elseif ($activity_policy->policy_sort->temporality == 'permanent') {

                    if ($activity_policy->policy_sort->periodicity == '1') {
                        $descuento = $activity_policy->policy_sort->preventive_actions ?? 0;
                    } elseif ($activity_policy->policy_sort->periodicity == '2') {
                        $descuentoFraccionamiento = $activity_policy->policy_sort->special_condition_payment == 'Si' ? 4 : 0;
                        $descuento = $activity_policy->policy_sort->preventive_actions ?? 0;
                    } elseif ($activity_policy->policy_sort->periodicity == '3') {
                        $descuento = $activity_policy->policy_sort->preventive_actions ?? 0;
                        $descuentoFraccionamiento = $activity_policy->policy_sort->special_condition_payment == 'Si' ? 6 : 0;
                    } elseif ($activity_policy->policy_sort->periodicity == '4') {
                        $descuento = $activity_policy->policy_sort->preventive_actions ?? 0;
                        $descuentoFraccionamiento = $activity_policy->policy_sort->special_condition_payment == 'Si' ? 8 : 0;
                    }
                }

                $totalsinDescuento = $activity_policy_collection->policy_sort_collection->total_amount * (1 - ($descuento / 100));
            }

            $fechaCostarrica = Carbon::parse($activity_policy_collection->created_at);
            $work_modality_id = $activity_policy->policy_sort->work_modality_id;
            $type_receipt = $activity_policy_collection->policy_sort_collection->type_receipt;

            $datos = [
                'activity_policy' => $activity_policy,
                'activity_policy_collection' => $activity_policy_collection,
                'totalWords' => $totalWords,
                'date_from' => $validityFrom,
                'date_to' => $validityTo,
                'watermark' => false,
                'horaCostarrica' => $activity_policy_collection->created_at->format('h:i:s A'),
                'fechaCostarrica' => $fechaCostarrica->format('d/m/Y'),
                'pagado' => true,
                'descuento' => $descuento,
                'totalsinDescuento' => $totalsinDescuento,
                'descuentoFraccionamiento' => $descuentoFraccionamiento,
                'work_modality_id' => $work_modality_id,
                'type_receipt'=> $type_receipt ?? ''
            ];

            $document = "monthly_payment_receipt";
            $pdf = PDF::loadView("services.policy_sort_collection.docs.monthly_payment_receipt", $datos);

            $timestamp = date('Ymd_His');
            $filePath = "policyCollectionSort/{$document}_{$activityPayment}_{$timestamp}.pdf";

            Storage::disk('s3')->put($filePath, $pdf->output());

            //buscar actividad y reemplazar el documento
            $activityAction = ActivityAction::where('activity_id', $activityPayment)
                ->whereIn('action_id', [182,183]) // 182,183
                ->orderBy('id', 'desc')
                ->first();

            $activityActionDocument = ActivityActionDocument::where('activity_action_id', $activityAction->id)->first();
            $activityActionDocument->path = $filePath;
            $activityActionDocument->save();

            return response()->json(['message' => 'Recibo de pago generado correctamente'], 200);
        } catch (\Exception $e) {
            dd('ACTIVITY_ID: '.$activityPayment,$e);
        }
    }


}