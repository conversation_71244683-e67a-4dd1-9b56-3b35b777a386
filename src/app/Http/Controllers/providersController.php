<?php

namespace App\Http\Controllers;
use App\Affiliate;
use App\Area;
use App\Client;
use App\Company;
use App\Provider;
use App\Schedule;
use App\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Route;

class ProvidersController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth')->except([
            'deactivateProviders'
        ]);
    }

    public function index(Request $req, $cpath, $id = null){

        $client = Client::where('path', $cpath)->firstOrFail();
        $filterProvider = Provider::get()->sortBy('name');
        $theProvider = null;

        if ($req->isMethod('post')) {
            DB::beginTransaction();
            try {

                $new = false;

                if (!$req->input('id')) {
                    $provider = new Provider();
                    $new = true;

                    $newAffiliate = Affiliate::firstOrCreate(
                                                [
                                                    'doc_number' => $req->input('doc_number'),
                                                    'doc_type' => $req->input('doc_type'),
                                                    'full_name' => $req->input('name'),
                                                    'first_name' => $req->input('name'),
                                                    'client_id' => $client->id,
                                                ]);

                    $provider->affiliate_id = $newAffiliate->id;

                } else {
                    $provider = Provider::find($req->input('id'));
                }

                $provider->email = $req->input('email');
                $provider->doc_type = $req->input('doc_type');
                $provider->doc_number = $req->input('doc_number');
                $provider->name = $req->input('name');

                if ($req->input('start_date_submit')) {
                    $provider->start_date = $req->input('start_date_submit');
                }

                if ($req->input('end_date_submit')) {
                    $provider->end_date = $req->input('end_date_submit');
                }

                $providerProtegidos = [5, 7, 8, 9, 10];
                $typeMessage = 'success';
                $message = 'Proveedor ' . $req->input('name') . ' guardado con éxito.';

                if (in_array($req->input('id'), $providerProtegidos) && $req->input('active') == 0 ) {
                    $typeMessage = 'info';

                    $name = $req->input('name');
                    $message = "La información del proveedor {$name} fue actualizada, pero no tiene permitido ser INACTIVADO !!!";

                } else {
                    $provider->active = $req->input('active') == 1;
                }

                $exists = Provider::query()
                    ->orWhere('email', '=', $provider->email)
                    ->orWhere('doc_number', '=', $provider->doc_number)
                    ->first();

                if ($exists && $new === true) {

                    return view('/information/provider_exists', [
                        'user' => $exists
                    ]);

                } else {

                    $provider->save();
                    DB::commit();
                    return redirect('/admin/proveedores', 302, [], true)->with($typeMessage, $message);
                }

            } catch (Exception $e) {
                DB::rollback();
                return back()->withInput();
            }

        } else if ($req->isMethod('get') && $id != null) {
            $theProvider = Provider::find($id);
        }

        $usuarioId = $req->input('usuario');

        $query = Provider::orderBy('active', 'desc')->orderBy('name', 'asc');

        if (!empty($usuarioId) && $usuarioId != '-1') {
            $query->where('id', $usuarioId);
        }

        //$query->whereNull('deleted_at');
        $providers = $query->get();

        return view('admin.providers', [
            'providers' => $providers,
            'filterProvider' => $filterProvider,
            'provider' => $theProvider,
        ]);
    }

    public function deactivateProviders()
    {
        try {
            DB::beginTransaction();

            $count = Provider::whereDate('end_date', '<', now())
                ->where('active', 1)
                ->whereNotIn('id', [5, 7, 8, 9, 10])
                ->update(['active' => 0]);

            DB::commit();

            return response()->json([
                'message' => 'Proveedores inactivados correctamente.',
                'count' => $count
            ], 200);

        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'message' => 'Error al inactivar proveedores.',
                'error' => $e->getMessage()
            ], 500);
        }
    }

}