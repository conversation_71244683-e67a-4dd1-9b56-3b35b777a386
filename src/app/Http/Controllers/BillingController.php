<?php

namespace App\Http\Controllers;

use App\Client;
use App\Template;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use Maatwebsite\Excel\Facades\Excel;

class BillingController extends Controller
{

    public function index(Request $req, $cpath)
    {
        Client::query()->where('path', $cpath)->firstOrFail();
        return view('billing.billing_palig');
    }

    public function submit(Request $req, $cpath)
    {
        Client::query()->where('path', $cpath)->firstOrFail();
        $fails = [];
        $count = 0;
        Excel::load($req->file('report_file')->path(), function ($reader) use ($req, &$fails, &$count) {
            foreach ($reader->toArray() as $item) {
                $error = [
                    'activity_id' => '',
                    'credit_number' => $item['idcredito'],
                    'id_number' => $item['idasegurado'],
                    'error' => '',
                ];
                try {
                    $form = Template::query()
                        ->where('credit_number', $item['idcredito'])
                        ->where('id_number', $item['idasegurado'])
                        ->firstOrFail();
                    $count = $count + 1;
                    $error['activity_id'] = $form->activity_id;
                    // Fecha Reporte PALIG - PALIG
                    $form->excel_report_date = $req->input('date_report');
                    // Mes de reporte
                    $form->report_month = $item['mes_de_reporte'];
                    // Fecha Factura PALIG
                    $form->billing_palig_date = $req->input('date_billing');
                    // No. Factura PALIG
                    $form->billing_palig_number = $req->input('number_billing');
                    // Valor Factura global
                    $form->billing_global_value = $req->input('value_billing');
                    // Valor factura Prima - Credito individual
                    $form->billing_prime_value = $item['valor_prima_neta'];

                    $form->save();

                } catch (\Exception $e) {
                    if (str_contains($e->getMessage(), 'No query results for model')) {
                        $error['error'] = 'Servicio no encontrado';
                    } else {
                        $error['error'] = $e->getMessage();
                    }
                    array_push($fails, $error);
                }
            }

            return $fails;
        });

        // SEND EMAIL
        $data = [
            'fails' => $fails,
            'date' => Carbon::now('America/Bogota'),
            'value_billing' => $req->input('value_billing'),
            'number_billing' => $req->input('number_billing'),
            'date_billing' => new Carbon($req->input('date_billing')),
            'date_start' => new Carbon($req->input('date_start')),
            'date_end' => new Carbon($req->input('date_end')),
            'count' => $count,
        ];

        $pdf = \PDF::loadView('billing.billing_palig_doc', $data);

        Mail::send('billing.billing_palig_doc', $data, function ($message) use ($data, $pdf) {
            $message
                ->from('<EMAIL>', 'RENAPP')
                ->to([
                    '<EMAIL>',
                    '<EMAIL>',
                ])
                ->subject('COMPROBANTE DE RADICACIÓN FACTURA PALIG')
                ->attachData($pdf->output(), "comprobante.pdf");
        });

        return view('billing.billing_palig_submit', [
            'fails' => $fails
        ]);
    }


}
