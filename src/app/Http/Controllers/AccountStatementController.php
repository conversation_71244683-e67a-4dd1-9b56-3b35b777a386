<?php

namespace App\Http\Controllers;

use App\AccountStatement;
use App\Actions\ActionPolizaSort;
use App\Activity;
use App\EconomicActivity;
use App\PolicySortCollection;
use App\PolicySpreadsheet;
use App\Service;
use App\State;
use App\States\StatePolicySortCollection;
use App\States\StatePoliza;
use App\Providers\AppServiceProvider;
use App\States\StateReportePlanillaTomador;
use App\Utilities\Utilities;
use Carbon\Carbon;
use DateTime;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;

class AccountStatementController extends Controller
{
    //Generar estado de cuenta mensual de pago
    public function generateAccountStatementMonthlyPayment(Request $request)
    {
        $id = uniqid();
        if (\Cache::has('cron_generate_account_statement_monthly')) {
            return response()->json([
                'status' => 200,
                'message' => 'Ya hay una validación en progreso',
            ], 200);

        }
        \Cache::put('cron_generate_account_statement_monthly', $id, now()->addMinutes(5));

        //traer el dia de referencia hace 15 dias
        if ($request->has('fecha_referencia')){
            $fechaReferencia = Carbon::createFromFormat('Y-m-d', $request->input('fecha_referencia'))->subDays(15);
        }else{
            $fechaReferencia = Carbon::now()->subDays(15);
        }

        $diaReferencia = (int) $fechaReferencia->format('d');

        // Día máximo del MES DE LA FECHA DE REFERENCIA
        $ultimoDiaMesReferencia = (int) $fechaReferencia->endOfMonth()->format('d');

        $diasComparar = [$diaReferencia];
        // Si el mes no tiene 31 días y estamos justo en el último día del mes,
        if ($ultimoDiaMesReferencia < 31 && $diaReferencia === $ultimoDiaMesReferencia) {
            // Agregamos los días inexistentes para ese mes (los días faltantes desde el último hasta 31)
            for ($d = $ultimoDiaMesReferencia + 1; $d <= 31; $d++) {
                $diasComparar[] = $d;
            }
        }

        //consulto las actividades de polizas a las cuales se les debe generar el estado de cuenta
        $policyActivities = Activity::where('service_id', Service::SERVICE_POLICY_SORT_MNK)
            ->where('state_id', StatePoliza::POLIZA_EMITIDA_ACTIVA)
            ->whereHas('policy_sort', function ($query) use ($diasComparar) {
                $query->whereRaw('DAY(validity_from) IN (' . implode(',', $diasComparar) . ')')
                    ->where('periodicity', AppServiceProvider::$PERIODICITY['MENSUAL'])
                    ->where('calendar_period', 1);
            })
            ->with(['policy_sort', 'affiliate', 'latestChildSpreadsheet'])
            ->get();
        $count = 0;
        $errors = 0;

        foreach ($policyActivities as $activity) {
            $fechaInicio = $activity->policy_sort->validity_from;
            $fechaFin = $activity->policy_sort->validity_to;

            $activityCollection = Activity::where('parent_id', $activity->id)
                ->where('service_id', Service::SERVICE_POLICY_SORT_COLLECTION_MNK)
                ->whereBetween('created_at', [$fechaInicio, $fechaFin])
                ->count();
            if ($activityCollection >= 12) {
                continue;
            }
            try {
                DB::beginTransaction();


                //buscamos el spreadsheet
                $spreadSheet = $activity->latestChildSpreadsheet->policy_spreadsheets;
                //tomamos el total de salarios de la planilla
                $spreadsheetTotalSalaries =isset($spreadSheet) ? $spreadSheet->total_salaries : 0;
                //generamos el estado de cuenta
                if ($request->has('dia_referencia')){
                    $diaReferencia = $request->input('dia_referencia');
                    $fechaDiaReferencia = Carbon::now()->month(Carbon::now()->month)->day($diaReferencia);
                    // Sumamos 15 días a la fecha de referencia
                    $diaReferencia = $fechaDiaReferencia->addDays(15);

                    $this->generateAccountStatement($spreadsheetTotalSalaries, $activity,$diaReferencia);
                }else{
                    $this->generateAccountStatement($spreadsheetTotalSalaries, $activity);
                }

                DB::commit();
                $count++;
            }catch (\Exception $e) {
                DB::rollBack();
                $errors ++;
                continue;
            }

        }
        return response()->json(['message' => 'Estados de cuenta generado correctamente',
            'count'=>$count,
            'errors'=>$errors], 200);
    }
    //Generar estado de cuenta trimestral
    public function generateAccountStatementQuarterly(Request $request)
    {
        $id = uniqid();
        if (\Cache::has('cron_generate_account_statement_quarterly')) {
            return response()->json([
                'status' => 200,
                'message' => 'Ya hay una validación en progreso',
            ], 200);

        }
        \Cache::put('cron_generate_account_statement_quarterly', $id, now()->addMinutes(5));

        //Fecha referencia para das de validacion
        if ($request->has('fecha_referencia')){
            $hoy = Carbon::createFromFormat('Y-m-d', $request->input('fecha_referencia'));
        }else{
            $hoy = now();
        }

        $diasARestar = [45, 135, 225];
        //busco los ultimos 3 trimestres para generar los estados de cuenta
        $fechasBusqueda = [];
        foreach ($diasARestar as $dias) {
            $fechasBusqueda[] = $hoy->copy()->subDays($dias)->format('Y-m-d');
        }

        //consulto las actividades de polizas a las cuales se les debe generar el estado de cuenta
        $policyActivities = Activity::where('service_id', Service::SERVICE_POLICY_SORT_MNK)
            ->where('state_id', StatePoliza::POLIZA_EMITIDA_ACTIVA)
            ->whereHas('policy_sort', function ($query) use ($fechasBusqueda) {
                $query->where('periodicity', AppServiceProvider::$PERIODICITY['TRIMESTRAL'])
                    ->whereIn('validity_from', $fechasBusqueda);
            })
            ->with(['policy_sort'])
            ->get();

        $count = 0;
        $errors = 0;
        foreach ($policyActivities as $activity) {
            $fechaInicio = $activity->policy_sort->validity_from;
            $fechaFin = $activity->policy_sort->validity_to;
            //traemos los recibos de abono trimestral
            $activitiesCollection = Activity::where('parent_id', $activity->id)
                ->where('service_id', Service::SERVICE_POLICY_SORT_COLLECTION_MNK)
                ->whereBetween('created_at', [$fechaInicio, $fechaFin])
                ->whereHas('policy_sort_collection', function ($query) {
                    $query->where('type_receipt', 'quarterly_payment');
                })
                ->with(['policy_sort_collection' => function ($query) {
                    $query->where('type_receipt', 'quarterly_payment');
                }])
                ->get();
            if ($activitiesCollection->count() >=3) {
                continue;
            }
            try {
                DB::beginTransaction();
                //traemos todas las actividades de planilla asociadas a la poliza con estado certificado tomador reportado
                $activitySpreadsheets = Activity::where('parent_id',$activity->id)
                    ->where('service_id',Service::SERVICE_REPORT_TAKEN_FORM_MNK)
                    ->whereIn('state_id',[StateReportePlanillaTomador::CERTIFICADO_DE_TOMADOR_REPORTADO])
                    ->orderBy('id')
                    ->with('policy_spreadsheets')
                    ->skip(1)
                    ->take(13)
                    ->get();

                //generamos los estados de cuenta de planillas que falten de las polizas
                $this->generateAccountStatementAll($activity,$activitySpreadsheets);
                $default = (object)['amount' => 0, 'activity_spreadsheet' => null];

                //Calculamos el monto a cobrar
                if ($activitiesCollection->count() == 0){
                    $amount = DB::table('policy_spreadsheet_account_statements')
                        ->where('activity_policy', $activity->policy_sort->activity_id)
                        ->where('number','=',1)
                        ->get();
                    $amount_1 = $amount->firstWhere('number', 1) ?? $default;
                    $amount = round($amount_1->amount,2);
                    $spreadsheetArray = [$amount_1->activity_spreadsheet];
                }elseif ($activitiesCollection->count() == 1){
                    $amount = DB::table('policy_spreadsheet_account_statements')
                        ->where('activity_policy', $activity->policy_sort->activity_id)
                        ->whereIn('number',[1,2,3,4])
                        ->get();
                    $amount_1 = $amount->firstWhere('number', 1) ?? $default;
                    $amount_2 = $amount->firstWhere('number', 2) ?? $default;
                    $amount_3 = $amount->firstWhere('number', 3) ?? $default;
                    $amount_4 = $amount->firstWhere('number', 4) ?? $amount_3;
                    $value_1 = round($amount_2->amount - $amount_1->amount,2);
                    $value_2 = round($amount_3->amount - $amount_2->amount,2);

                    //Valor del recibo
                    $amount = $value_1 + $value_2 + $amount_4->amount;
                    $spreadsheetArray = [$amount_1->activity_spreadsheet, $amount_2->activity_spreadsheet, $amount_3->activity_spreadsheet];
                }elseif ($activitiesCollection->count() == 2){
                    $amount = DB::table('policy_spreadsheet_account_statements')
                        ->where('activity_policy', $activity->policy_sort->activity_id)
                        ->whereIn('number',[3,4,5,6,7])
                        ->get();

                    $amount_3 = $amount->firstWhere('number', 3) ?? $default;
                    $amount_4 = $amount->firstWhere('number', 4) ?? $default;
                    $amount_5 = $amount->firstWhere('number', 5) ?? $default;
                    $amount_6 = $amount->firstWhere('number', 6) ?? $default;
                    $amount_7 = $amount->firstWhere('number', 7) ?? $amount_6;

                    //Valor del recibo
                    $value_1 = round($amount_4->amount - $amount_3->amount,2);
                    $value_2 = round($amount_5->amount - $amount_4->amount,2);
                    $value_3 = round($amount_6->amount - $amount_5->amount,2);


                    //Valor del recibo
                    $amount = $value_1 + $value_2 + $value_3 + $amount_7->amount;
                    $spreadsheetArray = [$amount_3->activity_spreadsheet, $amount_4->activity_spreadsheet, $amount_5->activity_spreadsheet, $amount_6->activity_spreadsheet, $amount_7->activity_spreadsheet];
                }

                //traemos el primer cobro
                $firstReceipt = Activity::where('parent_id',$activity->id)
                    ->where('service_id',Service::SERVICE_POLICY_SORT_COLLECTION_MNK)
                    ->first();
                $firstValue = floatval($firstReceipt->policy_sort_collection->total_amount);
                if ($amount < $firstValue) {
                    $amount = $firstValue;
                }

                //Generar los recibos de abono trimestral
                $this->generateAccountStatementCollection($activity,$activitiesCollection,$amount,$spreadsheetArray);

                DB::commit();
                $count++;
            }catch (\Exception $e) {
                DB::rollBack();
                $errors ++;
                continue;
            }

        }
        return response()->json(['message' => 'Estados de cuenta generado correctamente',
            'count'=>$count,
            'errors'=>$errors], 200);
    }
    //Generar recibo para mensual
    public function generateAccountStatement($spreadsheetTotalSalaries,$activity,$dia_referencia = null)
    {
        try {
            $policySort = $activity->policy_sort;

            //Creamos la actividad del cobro asociado a la poliza.
            $activity_policy_collection = new Activity;
            $activity_policy_collection->parent_id = $activity->id;
            $activity_policy_collection->client_id = $activity->client_id;
            $activity_policy_collection->service_id = Service::SERVICE_POLICY_SORT_COLLECTION_MNK;
            $activity_policy_collection->affiliate_id = $activity->affiliate_id;
            $activity_policy_collection->user_id = $activity->user_id;
            $activity_policy_collection->state_id = State::REGISTRADO;
            $activity_policy_collection->created_at = Carbon::now()->format('Y-m-d H:i:s');
            $activity_policy_collection->save();


            $manualExecutionController = new ManualExecutionController();
            if ($policySort->periodicity == AppServiceProvider::$PERIODICITY['MENSUAL']) {
                //Calculamos la fecha de vencimiento
                $validityFromPolicy = Carbon::parse($policySort->validity_from);
                $start_day = $validityFromPolicy->day;
                //Calculamos el monto a cobrar
                $total_amount = $this->calculateAmount($spreadsheetTotalSalaries,$policySort);
                //Sacamos la fecha inicio del recibo
                if ($dia_referencia != null){
                    $validity_from =  Carbon::now()->month($dia_referencia->month)->day($start_day);
                    if($validity_from->lessThan($dia_referencia)){
                        $validity_from->addMonth();
                    }
                }else{
                    $validity_from =  Carbon::now()->startOfMonth()->day($start_day);
                    if($validity_from->lessThan(Carbon::now())){
                        $validity_from->addMonth();
                    }
                }

                //sacamos la fecha vencimiento del recibo
                if ($dia_referencia != null) {
                    $due_date = Utilities::addBusinessDaysFrom(10, $validity_from);

                    if ($due_date->lessThan($dia_referencia)) {
                        $due_date->addMonth();
                        $due_date = Utilities::addBusinessDaysFrom(0, $validity_from);
                    }
                }else{
                    $due_date = Utilities::addBusinessDaysFrom(10, $validity_from);
                    if ($due_date->lessThan(Carbon::now())) {
                        $due_date->addMonth();
                        $due_date = Utilities::addBusinessDaysFrom(0, $validity_from);
                    }
                }
                $validity_to =  $validity_from->copy()->addMonth()->subDay();
                $activity_action = $manualExecutionController->reportPaymentReceipt($activity_policy_collection->id,$total_amount['amountMensual'],$due_date,'mensual',$validity_from,$validity_to);
                //Ejecutamos la accion generar calculo abono sobre poliza
                ActionController::create($activity->id,ActionPolizaSort::GENERAR_CALCULO_ABONO_MENSUAL,'Generar estado de cuenta abono mensual');
                //planillas para el estado de cuenta
                $spreadsheetAcountstatement = [$activity->latestChildSpreadsheet->id];
            }

            //Creamos el estado de cuenta en la tabla de estados de cuenta
            $account_statement = new AccountStatement;
            $account_statement->activity_policy = $activity->id;
            $account_statement->activity_collection = $activity_policy_collection->id;
            $account_statement->activity_spreadsheet = json_encode($spreadsheetAcountstatement);
            $account_statement->salary_projection = $policySort->salary_projection ?? 0;
            $account_statement->salary_spreadsheet = $spreadsheetTotalSalaries ?? 0 ;
            $account_statement->setCreatedAt(Carbon::now());
            $account_statement->save();

            //Enviamos el recibo de pago
            $manualExecutionController->sendPaymentReceipt($activity->client_id,$validity_from,$validity_to,$activity_policy_collection,$activity_action);
            return $activity_policy_collection;
        }catch (\Exception $e) {
            throw $e;
        }
    }
    //calcular el monto del estado de cuenta
    public static function calculateAmount($spreadsheetTotalSalaries,$policySort)
    {
        try {
            $countCollection = 0;
            if ($policySort->periodicity == AppServiceProvider::$PERIODICITY['MENSUAL']) {
                //traemos todas las actividades de planilla asociadas a la poliza con estado certificado tomador reportado
                $activitySpreadsheets = Activity::where('parent_id', $policySort->activity_id)
                    ->where('service_id', Service::SERVICE_REPORT_TAKEN_FORM_MNK)
                    ->whereIn('state_id', [StateReportePlanillaTomador::CERTIFICADO_DE_TOMADOR_REPORTADO, StateReportePlanillaTomador::PLANILLA_REPORTADA])
                    ->orderBy('id')
                    ->with('policy_spreadsheets')
                    ->get();
                //traemos todos los cobros
                $countCollection = Activity::where('parent_id', $policySort->activity_id)
                    ->where('service_id', Service::SERVICE_POLICY_SORT_COLLECTION_MNK)
                    ->count();

                //calculamos el multiplicador para el periodo restante
                $multiplicador = max(14 - $activitySpreadsheets->count(), 1);
                //calculamos el salario proyectado
                $numberSpreadsheet = $multiplicador - 13;
                $SpreadsheetsCalculate = $activitySpreadsheets->slice($numberSpreadsheet, -1);
                $totalSalaries = 0;
                foreach ($SpreadsheetsCalculate as $spreadsheet) {
                    $totalSalaries += $spreadsheet->policy_spreadsheets->total_salaries;
                }

                //proyeccion de salarios
                $salary_projection = $totalSalaries + ($spreadsheetTotalSalaries * $multiplicador);
                $salary_projection = floatval($salary_projection);
                if ($activitySpreadsheets->count() <= 2) {

                    $salary_projection = $spreadsheetTotalSalaries * 12;
                    $salary_projection = floatval($salary_projection);
                }
            }else{
                $salary_projection = floatval($spreadsheetTotalSalaries);
            }

            $activityEconomicId = $policySort->activity_economic_id;
            // Porcentaje de la actividad economica
            $percentage = EconomicActivity::where('code',$activityEconomicId)->first()->percentage;
            // Formato del porcentaje
            $percentage = str_replace(',', '.', $percentage);
            $percentage = round($percentage, 2);

            $temSemestral = round(($percentage * 1.04), 2);
            $temTrimestral = round(($percentage * 1.06), 2);
            $temMensual = round(($percentage * 1.08), 2);

            $amountSemestral = $salary_projection *  ($temSemestral / 100)  / 2;
            $amountTrimestral = $salary_projection * ($temTrimestral / 100)  / 4;
            $amountMensual = $salary_projection * ($temMensual / 100) / 12;

            //traemos el cobro de emision
            $emissionActivity = Activity::where('parent_id',$policySort->activity_id)
                ->where('service_id',Service::SERVICE_POLICY_SORT_COLLECTION_MNK)
                ->where('state_id',StatePolicySortCollection::POLIZA_EMITIDA_ACTIVA_PENDIENTE_FACTURACION)
                ->first();

            if ($amountMensual < $emissionActivity->policy_sort_collection->total_amount || $countCollection == 2) {
                $amountMensual = $emissionActivity->policy_sort_collection->total_amount;
            }

            return [
                'amountSemestral' => $amountSemestral,
                'amountTrimestral' => $amountTrimestral,
                'amountMensual' => $amountMensual
            ];
        }catch (\Exception $e) {
            throw $e;
        }
    }

    //generar estado de cuenta para las planillas en general
    public function generateAccountStatementAll($activity,$activitySpreadsheets){
        try {
            //traemos todas las actividades de planilla asociadas a la poliza con estado certificado tomador reportado
            $salariosAnteriores = 0;
            $policyId = $activity->id;
            $periodicity = $activity->policy_sort->periodicity;

            switch ($periodicity) {
                case '2':
                    $amount = 'amountSemestral';
                    break;
                case '3':
                    $amount ='amountTrimestral';
                    break;
                case '4':
                    $amount = 'amountMensual';
                    break;
                default:
                    $amount = 'amountMensual';
                    break;
            }

            //cargar planillas ya procesadas en la bd
            $existingStatements = DB::table('policy_spreadsheet_account_statements')
                ->where('activity_policy', $policyId)
                ->get();

            //traemos las planillas de la poliza
            foreach ($existingStatements as $existingStatement) {
                $spreadsheet = PolicySpreadsheet::where('activity_id', $existingStatement->activity_spreadsheet)->first();
                if ($spreadsheet) {
                    $salariosAnteriores += $spreadsheet->total_salaries;
                }
            }
            $index = count($existingStatements);

            foreach ($activitySpreadsheets as $activitySpreadsheet) {
                $spreadsheet = $activitySpreadsheet->policy_spreadsheets;

                // Si ya fue calculado, lo saltamos
                $alreadyExists = $existingStatements->contains('activity_spreadsheet', $spreadsheet->activity_id);
                if ($alreadyExists) {
                    continue;
                }
                $index++;
                // Cálculo del multiplicador de la formula
                $multiplicador = 13 - $index;

                $salarioActual = $spreadsheet->total_salaries;

                $monto = $salariosAnteriores + ($salarioActual * $multiplicador);

                //se calcula segun la formula
                $amount_result = $this->calculateAmount($monto,$activity->policy_sort);

                DB::table('policy_spreadsheet_account_statements')->insert([
                    'activity_policy' => $activity->id,
                    'activity_spreadsheet' => $activitySpreadsheet->id,
                    'amount' => $amount_result[$amount],
                    'number' => $index,
                    'created_at' => now(),
                ]);

                //Sumamos el salario actual a los salarios anteriores para el siguiente cálculo
                $salariosAnteriores += $salarioActual;
            }
        }catch (\Exception $e){
            throw $e;
        }
    }
    //generar recibo de cobro del estado de cuenta
    public function generateAccountStatementCollection($activityPolicysort,$activitiesCollection,$amount,$spreadsheetArray)
    {
        try {
            //traemos la actividad de la poliza
            $policySort = $activityPolicysort->policy_sort;
            $manualExecutionController = new ManualExecutionController();

            //Creamos la actividad del cobro asociado a la poliza.
            $activity_policy_collection = new Activity;
            $activity_policy_collection->parent_id = $activityPolicysort->id;
            $activity_policy_collection->client_id = $activityPolicysort->client_id;
            $activity_policy_collection->service_id = Service::SERVICE_POLICY_SORT_COLLECTION_MNK;
            $activity_policy_collection->affiliate_id = $activityPolicysort->affiliate_id;
            $activity_policy_collection->user_id = $activityPolicysort->user_id;
            $activity_policy_collection->state_id = State::REGISTRADO;
            $activity_policy_collection->created_at = Carbon::now()->format('Y-m-d H:i:s');
            $activity_policy_collection->save();

            //Revisamos la periodicidad de la poliza
            if ($policySort->periodicity == AppServiceProvider::$PERIODICITY['MENSUAL']) {
                //Mensual esta en otra funcion, despues pasarla para aca
            } elseif ($policySort->periodicity == AppServiceProvider::$PERIODICITY['TRIMESTRAL']) {
                //Calcular fechas de vencimiento
                $n = $activitiesCollection->count() + 1;
                $validity_from = Carbon::parse($policySort->validity_from)->copy()->addMonths(3 * $n);
                $validity_to = $validity_from->copy()->addMonths(3)->subDay();

                //Calculamos la fecha de vencimiento
                $due_date = Utilities::addBusinessDaysFrom(10, $validity_from);
                if ($due_date->lessThan(Carbon::now())) {
                    $due_date->addMonth();
                    $due_date = Utilities::addBusinessDaysFrom(0, $validity_from);
                }

                $activity_action = $manualExecutionController->reportPaymentReceipt($activity_policy_collection->id,$amount,$due_date,'trimestral',$validity_from,$validity_to);
                ActionController::create($activityPolicysort->id,ActionPolizaSort::GENERAR_ESTADO_CUENTA_ABONO_TRIMESTRAL,'Generar estado de cuenta abono trimestral');
            } elseif ($policySort->periodicity == AppServiceProvider::$PERIODICITY['SEMESTRAL']) {
                $validity_from = Carbon::parse($policySort->validity_from)->copy()->addMonths(6);
                $validity_to = $validity_from->copy()->addMonths(6)->subDay();
                //Calculamos la fecha de vencimiento
                $due_date = Utilities::addBusinessDaysFrom(10, $validity_from);
                if ($due_date->lessThan(Carbon::now())) {
                    $due_date->addMonth();
                    $due_date = Utilities::addBusinessDaysFrom(0, $validity_from);
                }
                $activity_action = $manualExecutionController->reportPaymentReceipt($activity_policy_collection->id,$amount,$due_date,'semestral',$validity_from,$validity_to);
            }

            //Creamos el estado de cuenta en la tabla de estados de cuenta
            $account_statement = new AccountStatement;
            $account_statement->activity_policy = $activityPolicysort->id;
            $account_statement->activity_collection = $activity_policy_collection->id;
            $account_statement->activity_spreadsheet =json_encode($spreadsheetArray);
            $account_statement->salary_projection = $policySort->salary_projection ?? 0;
            $account_statement->salary_spreadsheet = $amount;
            $account_statement->setCreatedAt(Carbon::now());
            $account_statement->save();

            //Enviamos el recibo de pago
            $manualExecutionController->sendPaymentReceipt($activityPolicysort->client_id,$validity_from,$validity_to,$activity_policy_collection,$activity_action);
            return $activity_policy_collection;

        }catch (\Exception $e){
            throw $e;
        }
    }

    //Generar estado de cuenta semestral
    public function generateAccountStatementSemmiannual(Request $request)
    {
        $id = uniqid();
        if (\Cache::has('cron_generate_account_statement_semmiannual')) {
            return response()->json([
                'status' => 200,
                'message' => 'Ya hay una validación en progreso',
            ], 200);

        }
        \Cache::put('cron_generate_account_statement_semmiannual', $id, now()->addMinutes(2));
        //Fecha referencia para das de validacion
        if ($request->has('fecha_referencia')){
            $hoy = Carbon::createFromFormat('Y-m-d', $request->input('fecha_referencia'));
        }else{
            $hoy = now();
        }

        $hoy = $hoy->toDateString();
        //restamos 45 dias a la fecha de referencia y 3 meses
        $hoy = Carbon::parse($hoy)->subDays(45)->subMonths(3)->format('Y-m-d');

        //consulto las actividades de polizas a las cuales se les debe generar el estado de cuenta
        $policyActivities = Activity::where('service_id', Service::SERVICE_POLICY_SORT_MNK)
            ->where('state_id', StatePoliza::POLIZA_EMITIDA_ACTIVA)
            ->whereHas('policy_sort', function ($query) use ($hoy) {
                $query->where('periodicity', AppServiceProvider::$PERIODICITY['SEMESTRAL'])
                    ->where('validity_from', $hoy);

            })
            ->with(['policy_sort'])
            ->get();

        $count = 0;
        $errors = 0;
        foreach ($policyActivities as $activity){
            $fechaInicio = $activity->policy_sort->validity_from;
            $fechaFin = $activity->policy_sort->validity_to;

            //traemos los recibos de abono semestral
            $activitiesCollection = Activity::where('parent_id', $activity->id)
                ->where('service_id', Service::SERVICE_POLICY_SORT_COLLECTION_MNK)
                ->whereBetween('created_at', [$fechaInicio, $fechaFin])
                ->whereHas('policy_sort_collection', function ($query) {
                    $query->where('type_receipt', 'semiannual_payment');
                })
                ->with(['policy_sort_collection' => function ($query) {
                    $query->where('type_receipt', 'semiannual_payment');
                }])
                ->get();
            if ($activitiesCollection->count() >=1) {
                continue;
            }
            try {

                DB::beginTransaction();
                //traemos todas las actividades de planilla asociadas a la poliza con estado certificado tomador reportado
                $activitySpreadsheets = Activity::where('parent_id',$activity->id)
                    ->where('service_id',Service::SERVICE_REPORT_TAKEN_FORM_MNK)
                    ->whereIn('state_id',[StateReportePlanillaTomador::CERTIFICADO_DE_TOMADOR_REPORTADO])
                    ->orderBy('id')
                    ->with('policy_spreadsheets')
                    ->skip(1)
                    ->take(13)
                    ->get();

                //generamos los estados de cuenta de planillas que falten de las polizas
                $this->generateAccountStatementAll($activity,$activitySpreadsheets);
                $default = (object)['amount' => 0, 'activity_spreadsheet' => null];
                //traemos el primer cobro
                $firstReceipt = Activity::where('parent_id',$activity->id)
                    ->where('service_id',Service::SERVICE_POLICY_SORT_COLLECTION_MNK)
                    ->first();
                $firstValue = floatval($firstReceipt->policy_sort_collection->total_amount);
                //Calulamos el monto a cobrar
                $records = DB::table('policy_spreadsheet_account_statements')
                    ->where('activity_policy', $activity->policy_sort->activity_id)
                    ->get();
                //Primera diferencia con el valor cobrado
                $amount = 0;
                if ($records->isNotEmpty()) {
                    $amount = round( $records[0]->amount - $firstValue, 2);
                }
                $spreadsheetArray = [];
                $limit = $records->count() - 1;
                if ($records->count() >= 2) {
                    for ($i = 1; $i  < $limit; $i++) {
                        $prev = $records[$i - 1]->amount;
                        $current = $records[$i]->amount;
                        $amount += round($current - $prev, 2);
                        $spreadshetArray[] = $records[$i]->activity_spreadsheet;
                    }
                }

                // Sumar el último monto como base final
                if ($records->isNotEmpty()) {
                    $amount += $records->last()->amount;
                    $spreadsheetArray[] = $records->last()->activity_spreadsheet;
                }

                if ($amount < $firstValue) {
                    $amount = $firstValue;
                }
                //Generar los recibos de abono semestral
                $this->generateAccountStatementCollection($activity,$activitiesCollection,$amount,$spreadsheetArray);

                DB::commit();
                $count++;
            }catch (\Exception $e){
                DB::rollBack();
                $errors ++;
                continue;
            }

            return response()->json(['message' => 'Estados de cuenta generado correctamente',
                'count'=>$count,
                'errors'=>$errors], 200);
        }
    }
}