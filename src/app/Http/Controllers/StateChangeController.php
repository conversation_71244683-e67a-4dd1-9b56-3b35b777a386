<?php

namespace App\Http\Controllers;

use App\Activity;
use App\ActivityAction;
use App\Client;
use App\Service;
use App\ServiceState;
use App\State;
use Auth;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Storage;

class StateChangeController extends Controller
{
    public function stateChangeView(Request $req)
    {
        return view('state_change', [
            'states' => State::get()
        ]);
    }

    public function save(Request $request, $cpath)
    {
        try {
            $client = Client::where('path', $cpath)->firstOrFail();
            $id_activities = explode("\n", $request->id_activities);
            $id_activities = array_map('trim', $id_activities);
            $state_id = $request->state_id;
            $tracing = $request->tracing;
            $state = State::find($state_id);
            if ($request->id_activities == '') {
                throw new \Exception('No se ingreso ninguna id de actividad');
            }
            if ($state_id == null) {
                throw new \Exception('No se seleccionó ningun estado');
            }
            if ($tracing == '') {
                throw new \Exception('Falta reporte de seguimiento');
            }
            DB::beginTransaction();
            foreach ($id_activities as $id_activity) {
                $activity = Activity::where('client_id', $client->id)
                                    ->where('id', $id_activity)
                                    ->first();
                if (!$activity) {
                    throw new \Exception('La actividad ' . $id_activity . ' no existe');
                }

                $service_state = ServiceState::where('service_id',$activity->service_id)
                                             ->where('state_id',$state_id)
                                             ->first();

                if (!$service_state){
                    $service = Service::find($activity->service_id);
                    throw new \Exception('La actividad ' . $id_activity . ' no puede ser modificada al estado '.$state->name
                        .' porque este no esta asociado al servicio '.$service->name);
                }
                $old_state = State::find($activity->state_id);
                $activity_actions = new ActivityAction();
                $activity_actions->activity_id = $activity->id;
                if (in_array($activity->service_id, [57, 64])) {
                    $activity_actions->action_id = '611';
                } else {
                    //$activity_actions->action_id = '6';
                    $activity_actions->action_id = '1';
                }
                $activity_actions->old_state_id = $activity->state_id;
                $activity_actions->new_state_id = $state_id;
                $activity_actions->old_user_id = $activity->user_id;
                $activity_actions->new_user_id = $activity->user_id;
                $activity_actions->description = '<b>Cambio de estado de '.$old_state->name.' a '.$state->name.'</b>'.$tracing;
                $activity_actions->author_id = Auth::id();
                $activity_actions->save();

                $activity->state_id = $state_id;
                $activity->save();
            }
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()->with('error', $e->getMessage())->withInput();
        }
        return redirect()->back()->with('success', 'Las actividades han cambiado al estado ' . $state->name);
    }
}