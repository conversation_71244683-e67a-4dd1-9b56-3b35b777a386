<?php

namespace App\Http\Controllers;

use App\Action;
use App\ActionField;
use App\ActionServiceState;
use App\Activity;
use App\Affiliate;
use App\Area;
use App\AreaPermission;
use App\BillReport;
use App\Branch;
use App\Client;
use App\ClientService;
use App\Company;
use App\Contract;
use App\Correspondence;
use App\Docinvoice;
use App\DocinvoiceActivity;
use App\Employer;
use App\Employment;
use App\Invoice;
use App\InvoiceActivity;
use App\Mail\SendDocumentDataBase;
use App\Mail\UserCreated;
use App\Permission;
use App\PolicySort;
use App\UserAuthorizedPolicies;
use App\UserAuthorizedTomador;
use App\UserPermission;
use App\Region;
use App\Rehabilitation;
use App\Report;
use App\Schedule;
use App\Service;
use App\ServiceAction;
use App\ServiceDocument;
use App\ServiceField;
use App\ServiceState;
use App\State;
use App\StateAction;
use App\StateArea;
use App\User;
use App\UserClient;
use App\States\StatePoliza;
use App\UserView;
use App\Provider;
use Auth;
use Excel;
use Illuminate\Http\File;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Storage;
use NumberFormatter;
use PDF;
use Exception;

class AdminController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function descargas(Request $req, $cpath)
    {
        return view('information.home_downloads');
    }

    public function reportsAdmin(Request $req, $cpath)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $stateServicesIt = ServiceState::query()
            ->where('service_id', 53)
            ->leftJoin('states', 'states.id', '=', 'service_states.state_id')
            ->get();
        $stateServicesMdi = ServiceState::query()
            ->where('service_id', 60)
            ->leftJoin('states', 'states.id', '=', 'service_states.state_id')
            ->get();
        return view('information.downloads_admin', [
            'client_id' => $client->id,
            'services' => $client->services,
            'states' => State::get(),
            'actions' => Action::get(),
            'states_it' => $stateServicesIt,
            'states_mdi' => $stateServicesMdi,
        ]);
    }

    //Infografía general para el rol de administrador
    public function businessReport(Request $req, $cpath)
    {
        //Se retorna la vista
        return view('information.businessReport', []);
    }

    public function suveyReport(Request $req, $cpath)
    {
        //Se retorna la vista
        return view('information.suveyReport', []);
    }

    public function OperationalReport(Request $req, $cpath)
    {
        //Se retorna la vista
        return view('information.operationalReport', [
            'active' => ''
        ]);
    }

    public function generalReportsTwo(Request $req, $cpath)
    {
        //Se retorna la vista
        return view('information.generalReportsTwo', []);
    }

    public function reportDisease(Request $req, $cpath)
    {
        //Se retorna la vista
        return view('information.reportDisease', []);
    }

    public function reportDeceased(Request $req, $cpath)
    {
        //Se retorna la vista
        return view('information.reportDeceased', []);
    }

    public function reportByPolicies(Request $req, $cpath)
    {
        //Se retorna la vista
        return view('information.reportByPolicies', []);
    }

    public function insuredReport(Request $req, $cpath)
    {
        //Se retorna la vista
        return view('information.report_operational.report_asegurados', [
            'active' => 'asegurados'
        ]);
    }
    public function accidentReport(Request $req, $cpath)
    {
        //Se retorna la vista
        return view('information.report_operational.report_accidente', [
            'active' => 'accidente'
        ]);
    }
    public function commuteReport(Request $req, $cpath)
    {
        //Se retorna la vista
        return view('information.report_operational.report_trayecto', [
            'active' => 'trayecto'
        ]);
    }

    public function workReport(Request $req, $cpath)
    {
        //Se retorna la vista
        return view('information.report_operational.report_laboral', [
            'active' => 'asegurados'
        ]);
    }

    public function claimsReport(Request $req, $cpath)
    {
        //Se retorna la vista
        return view('information.report_operational.report_siniestralidad', [
            'active' => 'siniestralidad'
        ]);
    }

    public function sixReport(Request $req, $cpath)
    {
        //Se retorna la vista
        return view('information.report_operational.report_reporte_seis', [
            'active' => 'ocurrencia'
        ]);
    }

    public function intermedaryHolderReport(Request $req, $cpath)
    {
        //Se retorna la vista
        return view('information.intermedaryHolderReport', []);
    }

    public function createInvoice(Request $req, $cpath)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $invoice = new Invoice;
        $invoice->init_date = $req->input('initial_date_submit');
        $invoice->last_date = $req->input('finish_date_submit');
        $invoice->client_id = $client->id;
        $invoice->closed = 0;
        $invoice->save();

        return redirect('facturacion');
    }

    public function closeInvoice(Request $req, $cpath)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $invoice = Invoice::where('client_id', '=', $client->id)->where('init_date', '=', $req->input('initial_date_submit'))->where('last_date', '=', $req->input('finish_date_submit'))->first();

        $doctors = DB::select(BillReport::ATELPCL_DOCTOR_CONSOLIDATE_SQL_NEW, [$client->id, $invoice->id]);
        DB::beginTransaction();

        try {
            foreach ($doctors as $doctor) {
                $docinvoice = new Docinvoice;
                $docinvoice->user_id = $doctor->FUNCIONARIO_ID;
                $docinvoice->user_name = $doctor->FUNCIONARIO;
                $docinvoice->init_date = $invoice->init_date;
                $docinvoice->last_date = $invoice->last_date;
                $docinvoice->invoice_id = $invoice->id;
                $docinvoice->client_id = $client->id;
                $docinvoice->save();
            }

            $docinvoice_activities = DB::select(BillReport::ATELPCL_DOCTOR_SQL_NEW, [$client->id, $invoice->id]);

            foreach ($docinvoice_activities as $docact) {
                $docinvoices_id = Docinvoice::where('user_id', '=', $doctor->FUNCIONARIO_ID)->pluck('id');

                $docinvoice_activity = DocinvoiceActivity::whereIn('docinvoice_id', $docinvoices_id)->where('activity_id', '=', $docact->ID_SERVICIO)->first();

                if (!$docinvoice_activity) {
                    $docinvoice_activity = new DocinvoiceActivity;
                    $docinvoice_activity->docinvoice_id = $docact->DOCINVOICE_ID;
                    $docinvoice_activity->activity_id = $docact->ID_SERVICIO;
                    $docinvoice_activity->save();
                }
            }

            $invoice->closed = 1;
            $invoice->status = 'Factura';

            $invoice->save();

            DB::commit();
        } catch (Exception $e) {
            DB::rollback();
        }

        $req->request->add(['invoice_num' => $invoice->id]);

        $this->bill_invoice($req, $cpath);

        return redirect('facturacion');
    }

    public function reports(Request $req, $cpath)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        if ($req->input('proyeccion_consolidacion') == null) {
            $items = DB::select('SELECT * FROM proyections WHERE client_id = ' . $client->id);
            foreach ($items as $item) {

                if ($item->name_service == 'CONSOLIDACION_PROYECTADOS')
                    $proyeccion_consolidacion = $item->proyections;
                elseif ($item->name_service == 'AT_PROYECTADOS')
                    $proyeccion_at = $item->proyections;
                elseif ($item->name_service == 'EL_PROYECTADOS')
                    $proyeccion_el = $item->proyections;
                elseif ($item->name_service == 'PCL_PROYECTADOS')
                    $proyeccion_pcl = $item->proyections;
                elseif ($item->name_service == 'DISCAPACIDAD_PROYECTADOS')
                    $proyeccion_discapacidad = $item->proyections;
                elseif ($item->name_service == 'REHABILITACION_PROYECTADOS')
                    $proyeccion_rehabilitacion = $item->proyections;
                elseif ($item->name_service == 'RECOMENDACION_PROYECTADOS')
                    $proyeccion_recomendacion = $item->proyections;
                elseif ($item->name_service == 'AUDITORIA_PROYECTADOS')
                    $proyeccion_auditoria = $item->proyections;
                elseif ($item->name_service == 'VALORACION_BOGOTA_PROYECTADOS')
                    $proyeccion_valoracion_bogota = $item->proyections;
                elseif ($item->name_service == 'VALORACION_NACIONAL_PROYECTADOS')
                    $proyeccion_valoracion_nacional = $item->proyections;
                elseif ($item->name_service == 'MESA_TRABAJO_PROYECTADOS')
                    $proyeccion_mesa_trabajo = $item->proyections;
                elseif ($item->name_service == 'SEGUIMIENTO_PROYECTADOS')
                    $proyeccion_seguimiento = $item->proyections;
                elseif ($item->name_service == 'ANALISIS_PROYECTADOS')
                    $proyeccion_analisis = $item->proyections;
                elseif ($item->name_service == 'CONTROVERSIAS_PROYECTADOS')
                    $proyeccion_controversias = $item->proyections;
                elseif ($item->name_service == 'CARGUE_PROYECTADOS')
                    $proyeccion_cargue = $item->proyections;
                elseif ($item->name_service == 'MOVILIDAD_PROYECTADOS')
                    $proyeccion_movilidad = $item->proyections;
            }
        }
        return View('information.home_reports')
            ->with('proyeccion_consolidacion', $proyeccion_consolidacion)
            ->with('proyeccion_at', $proyeccion_at)
            ->with('proyeccion_el', $proyeccion_el)
            ->with('proyeccion_pcl', $proyeccion_pcl)
            ->with('proyeccion_discapacidad', $proyeccion_discapacidad)
            ->with('proyeccion_rehabilitacion', $proyeccion_rehabilitacion)
            ->with('proyeccion_recomendacion', $proyeccion_recomendacion)
            ->with('proyeccion_auditoria', $proyeccion_auditoria)
            ->with('proyeccion_valoracion_bogota', $proyeccion_valoracion_bogota)
            ->with('proyeccion_valoracion_nacional', $proyeccion_valoracion_nacional)
            ->with('proyeccion_mesa_trabajo', $proyeccion_mesa_trabajo)
            ->with('proyeccion_seguimiento', $proyeccion_seguimiento)
            ->with('proyeccion_analisis', $proyeccion_analisis)
            ->with('proyeccion_controversias', $proyeccion_controversias)
            ->with('proyeccion_cargue', $proyeccion_cargue)
            ->with('proyeccion_movilidad', $proyeccion_movilidad);
    }

    public function updateProyections(Request $req, $cpath)
    {
        $client = Client::where('path', $cpath)->firstOrFail();

        DB::table('proyections')
            ->where('client_id', $client->id)
            ->where('name_service', '=', 'CONSOLIDACION_PROYECTADOS')
            ->update(['proyections' => $req->input('proyeccion_consolidacion')]);

        DB::table('proyections')
            ->where('client_id', $client->id)
            ->where('name_service', '=', 'AT_PROYECTADOS')
            ->update(['proyections' => $req->input('proyeccion_at')]);

        DB::table('proyections')
            ->where('client_id', $client->id)
            ->where('name_service', '=', 'EL_PROYECTADOS')
            ->update(['proyections' => $req->input('proyeccion_el')]);

        DB::table('proyections')
            ->where('client_id', $client->id)
            ->where('name_service', '=', 'PCL_PROYECTADOS')
            ->update(['proyections' => $req->input('proyeccion_pcl')]);

        DB::table('proyections')
            ->where('client_id', $client->id)
            ->where('name_service', '=', 'DISCAPACIDAD_PROYECTADOS')
            ->update(['proyections' => $req->input('proyeccion_discapacidad')]);

        DB::table('proyections')
            ->where('client_id', $client->id)
            ->where('name_service', '=', 'REHABILITACION_PROYECTADOS')
            ->update(['proyections' => $req->input('proyeccion_rehabilitacion')]);

        DB::table('proyections')
            ->where('client_id', $client->id)
            ->where('name_service', '=', 'RECOMENDACION_PROYECTADOS')
            ->update(['proyections' => $req->input('proyeccion_recomendacion')]);

        DB::table('proyections')
            ->where('client_id', $client->id)
            ->where('name_service', '=', 'AUDITORIA_PROYECTADOS')
            ->update(['proyections' => $req->input('proyeccion_auditoria')]);

        DB::table('proyections')
            ->where('client_id', $client->id)
            ->where('name_service', '=', 'VALORACION_BOGOTA_PROYECTADOS')
            ->update(['proyections' => $req->input('proyeccion_valoracion_bogota')]);

        DB::table('proyections')
            ->where('client_id', $client->id)
            ->where('name_service', '=', 'VALORACION_NACIONAL_PROYECTADOS')
            ->update(['proyections' => $req->input('proyeccion_valoracion_nacional')]);

        DB::table('proyections')
            ->where('client_id', $client->id)
            ->where('name_service', '=', 'MESA_TRABAJO_PROYECTADOS')
            ->update(['proyections' => $req->input('proyeccion_mesa_trabajo')]);

        DB::table('proyections')
            ->where('client_id', $client->id)
            ->where('name_service', '=', 'SEGUIMIENTO_PROYECTADOS')
            ->update(['proyections' => $req->input('proyeccion_seguimiento')]);

        DB::table('proyections')
            ->where('client_id', $client->id)
            ->where('name_service', '=', 'ANALISIS_PROYECTADOS')
            ->update(['proyections' => $req->input('proyeccion_analisis')]);

        DB::table('proyections')
            ->where('client_id', $client->id)
            ->where('name_service', '=', 'CONTROVERSIAS_PROYECTADOS')
            ->update(['proyections' => $req->input('proyeccion_controversias')]);

        DB::table('proyections')
            ->where('client_id', $client->id)
            ->where('name_service', '=', 'CARGUE_PROYECTADOS')
            ->update(['proyections' => $req->input('proyeccion_cargue')]);

        DB::table('proyections')
            ->where('client_id', $client->id)
            ->where('name_service', '=', 'MOVILIDAD_PROYECTADOS')
            ->update(['proyections' => $req->input('proyeccion_movilidad')]);

        return redirect('admin/reportes');
    }

    public function howToGoing(Request $req, $cpath)
    {
        $client = Client::where('path', $cpath)->firstOrFail();

        if (Client::FAMISANAR == $client->id) {
            $costos = [
                'CALIFICACION_EL' => 100500,
                'CALIFICACION_AT' => 35900,
                'CONSOLIDACION' => 19300,
                'CALIFICACION_PCL' => 100500,
                'DISCAPACIDAD' => 45000,
                'REHABILITACION' => 45000,
                'RECOMENDACION' => 45000,
                'AUDITORIA_IT' => 4300,
                'VALORACION_BOGOTA' => 38000,
                'VALORACION_NACIONAL' => 43000,
                'MESA_TRABAJO' => 79000,
                'SEGUIMIENTO' => 25600,
                'CONTROVERSIAS' => 45000,
                'ANALISIS' => 45000,
                'CARGUE' => 1545,
                'FURAT' => 1545,
                'RECOBROS' => 0,
            ];
        } elseif (Client::MEDIMAS == $client->id) {
            $costos = [
                'CALIFICACION_EL' => 100500,
                'CALIFICACION_AT' => 35900,
                'CONSOLIDACION' => 19300,
                'CALIFICACION_PCL' => 100500,
                'DISCAPACIDAD' => 45000,
                'REHABILITACION' => 45000,
                'RECOMENDACION' => 45000,
                'AUDITORIA_IT' => 4300,
                'VALORACION_BOGOTA' => 38000,
                'VALORACION_NACIONAL' => 43000,
                'MESA_TRABAJO' => 79000,
                'SEGUIMIENTO' => 25600,
                'CONTROVERSIAS' => 45000,
                'ANALISIS' => 45000,
                'CARGUE' => 1545,
                'FURAT' => 1545,
                'RECOBROS' => 0,
            ];
        } elseif (Client::UDEA == $client->id) {
            $costos = [
                'CALIFICACION_EL' => 100500,
                'CALIFICACION_AT' => 35900,
                'CONSOLIDACION' => 19300,
                'CALIFICACION_PCL' => 100500,
                'DISCAPACIDAD' => 45000,
                'REHABILITACION' => 45000,
                'RECOMENDACION' => 45000,
                'AUDITORIA_IT' => 4300,
                'VALORACION_BOGOTA' => 38000,
                'VALORACION_NACIONAL' => 43000,
                'MESA_TRABAJO' => 79000,
                'SEGUIMIENTO' => 25600,
                'CONTROVERSIAS' => 45000,
                'ANALISIS' => 45000,
                'CARGUE' => 1545,
                'FURAT' => 1545,
                'RECOBROS' => 0,
            ];
        }

        if ($req->input('init_date_submit') == null || $req->input('last_date_submit') == null) {
            $items = DB::select(Report::HOW_TO_GOING_NO_DATA_SQL);
        } else {
            $req->init_date = $req->input('init_date_submit');
            $req->last_date = $req->input('last_date_submit');

            $items = DB::select(str_replace(['?1', '?2', '?3', '?4'], [$client->id, $req->init_date, $req->last_date, '2017-08-01'], Report::HOW_TO_GOING_SQL));
        }


        $item_proyections = DB::select('SELECT * FROM proyections WHERE client_id = ' . $client->id);

        $proyected_values = [];
        foreach ($item_proyections as $item) {

            if ($item->name_service == 'CONSOLIDACION_PROYECTADOS')
                $proyected_values['CONSOLIDACION_PROYECTADOS'] = $item->proyections;
            elseif ($item->name_service == 'AT_PROYECTADOS')
                $proyected_values['AT_PROYECTADOS'] = $item->proyections;
            elseif ($item->name_service == 'EL_PROYECTADOS')
                $proyected_values['EL_PROYECTADOS'] = $item->proyections;
            elseif ($item->name_service == 'PCL_PROYECTADOS')
                $proyected_values['PCL_PROYECTADOS'] = $item->proyections;
            elseif ($item->name_service == 'DISCAPACIDAD_PROYECTADOS')
                $proyected_values['DISCAPACIDAD_PROYECTADOS'] = $item->proyections;
            elseif ($item->name_service == 'REHABILITACION_PROYECTADOS')
                $proyected_values['REHABILITACION_PROYECTADOS'] = $item->proyections;
            elseif ($item->name_service == 'RECOMENDACION_PROYECTADOS')
                $proyected_values['RECOMENDACION_PROYECTADOS'] = $item->proyections;
            elseif ($item->name_service == 'AUDITORIA_PROYECTADOS')
                $proyected_values['AUDITORIA_PROYECTADOS'] = $item->proyections;
            elseif ($item->name_service == 'VALORACION_BOGOTA_PROYECTADOS')
                $proyected_values['VALORACION_BOGOTA_PROYECTADOS'] = $item->proyections;
            elseif ($item->name_service == 'VALORACION_NACIONAL_PROYECTADOS')
                $proyected_values['VALORACION_NACIONAL_PROYECTADOS'] = $item->proyections;
            elseif ($item->name_service == 'MESA_TRABAJO_PROYECTADOS')
                $proyected_values['MESA_TRABAJO_PROYECTADOS'] = $item->proyections;
            elseif ($item->name_service == 'SEGUIMIENTO_PROYECTADOS')
                $proyected_values['SEGUIMIENTO_PROYECTADOS'] = $item->proyections;
            elseif ($item->name_service == 'ANALISIS_PROYECTADOS')
                $proyected_values['ANALISIS_PROYECTADOS'] = $item->proyections;
            elseif ($item->name_service == 'CONTROVERSIAS_PROYECTADOS')
                $proyected_values['CONTROVERSIAS_PROYECTADOS'] = $item->proyections;
            elseif ($item->name_service == 'CARGUE_PROYECTADOS')
                $proyected_values['CARGUE_PROYECTADOS'] = $item->proyections;
            elseif ($item->name_service == 'FURAT_PROYECTADOS')
                $proyected_values['FURAT_PROYECTADOS'] = $item->proyections;
            elseif ($item->name_service == 'MOVILIDAD_PROYECTADOS')
                $proyected_values['MOVILIDAD_PROYECTADOS'] = $item->proyections;
            elseif ($item->name_service == 'ANALISIS_PROYECTADOS')
                $proyected_values['ANALISIS_PROYECTADOS'] = $item->proyections;
        }

        $items = json_decode(json_encode($items[0]), true);

        $items['MESA_TRABAJO_COMPLETOS'] = 0;
        $items['MESA_TRABAJO_COMITE'] = 0;
        $items['MESA_TRABAJO_ASESORES'] = 0;
        $items['MESA_TRABAJO_PROFESIONALES'] = 0;

        $weekDays = 30;
        if ($req->init_date) {
            $weekDays = Correspondence::weekDays($req->init_date, $req->last_date);
        }

        $reportDay = 1;
        if ($req->report_day) {
            $reportDay = $req->report_day;
        }
        $calculated_values = [
            // AT
            'SUMATORIA_BANDEJA_AT' => $items['AT_COMPLETOS'] + $items['AT_COMITE'] + $items['AT_PROFESIONALES'],
            'SUMATORIA_SUBTOTAL_BANDEJA_AT' => ($items['AT_COMPLETOS'] + $items['AT_COMITE'] + $items['AT_PROFESIONALES']) * $costos['CALIFICACION_AT'],
            'APPROXIMATION_AT' => intval(round(($proyected_values['AT_PROYECTADOS'] / $weekDays) * ($reportDay))),
            'RESIDUAL_AT' => $items['AT_COMPLETOS'] - intval(round(($proyected_values['AT_PROYECTADOS'] / $weekDays) * ($reportDay))),
            'DIF_ACUMULADA_AT' => 0 - $proyected_values['AT_PROYECTADOS'] + $items['AT_COMPLETOS'] + $items['AT_COMITE'],
            'DIF_REAL_AT' => $items['AT_COMPLETOS'] - $proyected_values['AT_PROYECTADOS'],
            'VALOR_ACUMULADO_AT' => $costos['CALIFICACION_AT'] * $items['AT_COMPLETOS'],
            'VALOR_BANDEJA_AT' => $costos['CALIFICACION_AT'] * ($items['AT_COMPLETOS'] + $items['AT_COMITE']),
            'VALOR_PROYECTADO_AT' => $costos['CALIFICACION_AT'] * $proyected_values['AT_PROYECTADOS'],
            'FACTURA_ESTIMADA_AT' => $costos['CALIFICACION_AT'] * $proyected_values['AT_PROYECTADOS'],
            // EL
            'SUMATORIA_BANDEJA_EL' => $items['EL_COMPLETOS'] + $items['EL_COMITE'] + $items['EL_PROFESIONALES'],
            'SUMATORIA_SUBTOTAL_BANDEJA_EL' => ($items['EL_COMPLETOS'] + $items['EL_COMITE'] + $items['EL_PROFESIONALES']) * $costos['CALIFICACION_EL'],
            'APPROXIMATION_EL' => intval(round(($proyected_values['EL_PROYECTADOS'] / $weekDays) * ($reportDay))),
            'RESIDUAL_EL' => $items['EL_COMPLETOS'] - intval(round(($proyected_values['EL_PROYECTADOS'] / $weekDays) * ($reportDay))),
            'DIF_ACUMULADA_EL' => 0 - $proyected_values['EL_PROYECTADOS'] + $items['EL_COMPLETOS'] + $items['EL_COMITE'],
            'DIF_REAL_EL' => $items['EL_COMPLETOS'] - $proyected_values['EL_PROYECTADOS'],
            'VALOR_ACUMULADO_EL' => $costos['CALIFICACION_EL'] * $items['EL_COMPLETOS'],
            'VALOR_BANDEJA_EL' => $costos['CALIFICACION_EL'] * ($items['EL_COMPLETOS'] + $items['EL_COMITE']),
            'VALOR_PROYECTADO_EL' => $costos['CALIFICACION_EL'] * $proyected_values['EL_PROYECTADOS'],
            'FACTURA_ESTIMADA_EL' => $costos['CALIFICACION_EL'] * $proyected_values['EL_PROYECTADOS'],
            // PCL
            'SUMATORIA_BANDEJA_PCL' => $items['PCL_COMPLETOS'] + $items['PCL_COMITE'] + $items['PCL_PROFESIONALES'],
            'SUMATORIA_SUBTOTAL_BANDEJA_PCL' => ($items['PCL_COMPLETOS'] + $items['PCL_COMITE'] + $items['PCL_PROFESIONALES']) * $costos['CALIFICACION_PCL'],
            'APPROXIMATION_PCL' => intval(round(($proyected_values['PCL_PROYECTADOS'] / $weekDays) * ($reportDay))),
            'RESIDUAL_PCL' => $items['PCL_COMPLETOS'] - intval(round(($proyected_values['PCL_PROYECTADOS'] / $weekDays) * ($reportDay))),
            'DIF_ACUMULADA_PCL' => 0 - $proyected_values['PCL_PROYECTADOS'] + $items['PCL_COMPLETOS'] + $items['PCL_COMITE'],
            'DIF_REAL_PCL' => $items['PCL_COMPLETOS'] - $proyected_values['PCL_PROYECTADOS'],
            'VALOR_ACUMULADO_PCL' => $costos['CALIFICACION_PCL'] * $items['PCL_COMPLETOS'],
            'VALOR_BANDEJA_PCL' => $costos['CALIFICACION_PCL'] * ($items['PCL_COMPLETOS'] + $items['PCL_COMITE']),
            'VALOR_PROYECTADO_PCL' => $costos['CALIFICACION_PCL'] * $proyected_values['PCL_PROYECTADOS'],
            'FACTURA_ESTIMADA_PCL' => $costos['CALIFICACION_PCL'] * $proyected_values['PCL_PROYECTADOS'],
            // DISCAPACIDAD
            'SUMATORIA_BANDEJA_DISCAPACIDAD' => $items['DISCAPACIDAD_COMPLETOS'] + $items['DISCAPACIDAD_COMITE'] + $items['DISCAPACIDAD_PROFESIONALES'],
            'SUMATORIA_SUBTOTAL_BANDEJA_DISCAPACIDAD' => ($items['DISCAPACIDAD_COMPLETOS'] + $items['DISCAPACIDAD_COMITE'] + $items['DISCAPACIDAD_PROFESIONALES']) * $costos['DISCAPACIDAD'],
            'APPROXIMATION_DISCAPACIDAD' => intval(round(($proyected_values['DISCAPACIDAD_PROYECTADOS'] / $weekDays) * ($reportDay))),
            'RESIDUAL_DISCAPACIDAD' => $items['DISCAPACIDAD_COMPLETOS'] - intval(round(($proyected_values['DISCAPACIDAD_PROYECTADOS'] / $weekDays) * ($reportDay))),
            'DIF_ACUMULADA_DISCAPACIDAD' => 0 - $proyected_values['DISCAPACIDAD_PROYECTADOS'] + $items['DISCAPACIDAD_COMPLETOS'] + $items['DISCAPACIDAD_COMITE'],
            'DIF_REAL_DISCAPACIDAD' => $items['DISCAPACIDAD_COMPLETOS'] - $proyected_values['DISCAPACIDAD_PROYECTADOS'],
            'VALOR_ACUMULADO_DISCAPACIDAD' => $costos['DISCAPACIDAD'] * $items['DISCAPACIDAD_COMPLETOS'],
            'VALOR_BANDEJA_DISCAPACIDAD' => $costos['DISCAPACIDAD'] * ($items['DISCAPACIDAD_COMPLETOS'] + $items['DISCAPACIDAD_COMITE']),
            'VALOR_PROYECTADO_DISCAPACIDAD' => $costos['DISCAPACIDAD'] * $proyected_values['DISCAPACIDAD_PROYECTADOS'],
            'FACTURA_ESTIMADA_DISCAPACIDAD' => $costos['DISCAPACIDAD'] * $proyected_values['DISCAPACIDAD_PROYECTADOS'],
            // REHABILITACION
            'SUMATORIA_BANDEJA_REHABILITACION' => $items['REHABILITACION_COMPLETOS'] + $items['REHABILITACION_COMITE'] + $items['REHABILITACION_PROFESIONALES'],
            'SUMATORIA_SUBTOTAL_BANDEJA_REHABILITACION' => ($items['REHABILITACION_COMPLETOS'] + $items['REHABILITACION_COMITE'] + $items['REHABILITACION_PROFESIONALES']) * $costos['REHABILITACION'],
            'APPROXIMATION_REHABILITACION' => intval(round(($proyected_values['REHABILITACION_PROYECTADOS'] / $weekDays) * ($reportDay))),
            'RESIDUAL_REHABILITACION' => $items['REHABILITACION_COMPLETOS'] - intval(round(($proyected_values['REHABILITACION_PROYECTADOS'] / $weekDays) * ($reportDay))),
            'DIF_ACUMULADA_REHABILITACION' => 0 - $proyected_values['REHABILITACION_PROYECTADOS'] + $items['REHABILITACION_COMPLETOS'] + $items['REHABILITACION_COMITE'],
            'DIF_REAL_REHABILITACION' => $items['REHABILITACION_COMPLETOS'] - $proyected_values['REHABILITACION_PROYECTADOS'],
            'VALOR_ACUMULADO_REHABILITACION' => $costos['REHABILITACION'] * $items['REHABILITACION_COMPLETOS'],
            'VALOR_BANDEJA_REHABILITACION' => $costos['REHABILITACION'] * ($items['REHABILITACION_COMPLETOS'] + $items['REHABILITACION_COMITE']),
            'VALOR_PROYECTADO_REHABILITACION' => $costos['REHABILITACION'] * $proyected_values['REHABILITACION_PROYECTADOS'],
            'FACTURA_ESTIMADA_REHABILITACION' => $costos['REHABILITACION'] * $proyected_values['REHABILITACION_PROYECTADOS'],
            // RECOMENDACION
            'SUMATORIA_BANDEJA_RECOMENDACION' => $items['RECOMENDACION_COMPLETOS'] + $items['RECOMENDACION_COMITE'] + $items['RECOMENDACION_PROFESIONALES'],
            'SUMATORIA_SUBTOTAL_BANDEJA_RECOMENDACION' => ($items['RECOMENDACION_COMPLETOS'] + $items['RECOMENDACION_COMITE'] + $items['RECOMENDACION_PROFESIONALES']) * $costos['RECOMENDACION'],
            'APPROXIMATION_RECOMENDACION' => intval(round(($proyected_values['RECOMENDACION_PROYECTADOS'] / $weekDays) * ($reportDay))),
            'RESIDUAL_RECOMENDACION' => $items['RECOMENDACION_COMPLETOS'] - intval(round(($proyected_values['RECOMENDACION_PROYECTADOS'] / $weekDays) * ($reportDay))),
            'DIF_ACUMULADA_RECOMENDACION' => 0 - $proyected_values['RECOMENDACION_PROYECTADOS'] + $items['RECOMENDACION_COMPLETOS'] + $items['RECOMENDACION_COMITE'],
            'DIF_REAL_RECOMENDACION' => $items['RECOMENDACION_COMPLETOS'] - $proyected_values['RECOMENDACION_PROYECTADOS'],
            'VALOR_ACUMULADO_RECOMENDACION' => $costos['RECOMENDACION'] * $items['RECOMENDACION_COMPLETOS'],
            'VALOR_BANDEJA_RECOMENDACION' => $costos['RECOMENDACION'] * ($items['RECOMENDACION_COMPLETOS'] + $items['RECOMENDACION_COMITE']),
            'VALOR_PROYECTADO_RECOMENDACION' => $costos['RECOMENDACION'] * $proyected_values['RECOMENDACION_PROYECTADOS'],
            'FACTURA_ESTIMADA_RECOMENDACION' => $costos['RECOMENDACION'] * $proyected_values['RECOMENDACION_PROYECTADOS'],
            // AUDITORIA
            'SUMATORIA_BANDEJA_AUDITORIA' => $items['AUDITORIA_COMPLETOS'] + $items['AUDITORIA_COMITE'] + $items['AUDITORIA_PROFESIONALES'],
            'SUMATORIA_SUBTOTAL_BANDEJA_AUDITORIA' => ($items['AUDITORIA_COMPLETOS'] + $items['AUDITORIA_COMITE'] + $items['AUDITORIA_PROFESIONALES']) * $costos['AUDITORIA_IT'],
            'APPROXIMATION_AUDITORIA' => intval(round(($proyected_values['AUDITORIA_PROYECTADOS'] / $weekDays) * ($reportDay))),
            'RESIDUAL_AUDITORIA' => $items['AUDITORIA_COMPLETOS'] - intval(round(($proyected_values['AUDITORIA_PROYECTADOS'] / $weekDays) * ($reportDay))),
            'DIF_ACUMULADA_AUDITORIA' => 0 - $proyected_values['AUDITORIA_PROYECTADOS'] + $items['AUDITORIA_COMPLETOS'] + $items['AUDITORIA_COMITE'],
            'DIF_REAL_AUDITORIA' => $items['AUDITORIA_COMPLETOS'] - $proyected_values['AUDITORIA_PROYECTADOS'],
            'VALOR_ACUMULADO_AUDITORIA' => $costos['AUDITORIA_IT'] * $items['AUDITORIA_COMPLETOS'],
            'VALOR_BANDEJA_AUDITORIA' => $costos['AUDITORIA_IT'] * ($items['AUDITORIA_COMPLETOS'] + $items['AUDITORIA_COMITE']),
            'VALOR_PROYECTADO_AUDITORIA' => $costos['AUDITORIA_IT'] * $proyected_values['AUDITORIA_PROYECTADOS'],
            'FACTURA_ESTIMADA_AUDITORIA' => $costos['AUDITORIA_IT'] * $proyected_values['AUDITORIA_PROYECTADOS'],
            // VALORACION_NACIONAL
            'SUMATORIA_BANDEJA_VALORACION_NACIONAL' => $items['VALORACION_NACIONAL_COMPLETOS'] + $items['VALORACION_NACIONAL_COMITE'] + $items['VALORACION_NACIONAL_PROFESIONALES'],
            'SUMATORIA_SUBTOTAL_BANDEJA_VALORACION_NACIONAL' => ($items['VALORACION_NACIONAL_COMPLETOS'] + $items['VALORACION_NACIONAL_COMITE'] + $items['VALORACION_NACIONAL_PROFESIONALES']) * $costos['VALORACION_NACIONAL'],
            'APPROXIMATION_VALORACION_NACIONAL' => intval(round(($proyected_values['VALORACION_NACIONAL_PROYECTADOS'] / $weekDays) * ($reportDay))),
            'RESIDUAL_VALORACION_NACIONAL' => $items['VALORACION_NACIONAL_COMPLETOS'] - intval(round(($proyected_values['VALORACION_NACIONAL_PROYECTADOS'] / $weekDays) * ($reportDay))),
            'DIF_ACUMULADA_VALORACION_NACIONAL' => 0 - $proyected_values['VALORACION_NACIONAL_PROYECTADOS'] + $items['VALORACION_NACIONAL_COMPLETOS'] + $items['VALORACION_NACIONAL_COMITE'],
            'DIF_REAL_VALORACION_NACIONAL' => $items['VALORACION_NACIONAL_COMPLETOS'] - $proyected_values['VALORACION_NACIONAL_PROYECTADOS'],
            'VALOR_ACUMULADO_VALORACION_NACIONAL' => $costos['VALORACION_NACIONAL'] * $items['VALORACION_NACIONAL_COMPLETOS'],
            'VALOR_BANDEJA_VALORACION_NACIONAL' => $costos['VALORACION_NACIONAL'] * ($items['VALORACION_NACIONAL_COMPLETOS'] + $items['VALORACION_NACIONAL_COMITE']),
            'VALOR_PROYECTADO_VALORACION_NACIONAL' => $costos['VALORACION_NACIONAL'] * $proyected_values['VALORACION_NACIONAL_PROYECTADOS'],
            'FACTURA_ESTIMADA_VALORACION_NACIONAL' => $costos['VALORACION_NACIONAL'] * $proyected_values['VALORACION_NACIONAL_PROYECTADOS'],
            // MESA_TRABAJO
            'SUMATORIA_BANDEJA_MESA_TRABAJO' => 0,
            'SUMATORIA_SUBTOTAL_BANDEJA_MESA_TRABAJO' => 0,
            'APPROXIMATION_MESA_TRABAJO' => 0,
            'RESIDUAL_MESA_TRABAJO' => 0,
            'DIF_ACUMULADA_MESA_TRABAJO' => $proyected_values['MESA_TRABAJO_PROYECTADOS'] - $items['MESA_TRABAJO_COMPLETOS'] - $items['MESA_TRABAJO_COMITE'],
            'DIF_REAL_MESA_TRABAJO' => $proyected_values['MESA_TRABAJO_PROYECTADOS'] - $items['MESA_TRABAJO_COMPLETOS'],
            'VALOR_ACUMULADO_MESA_TRABAJO' => $costos['MESA_TRABAJO'] * $items['MESA_TRABAJO_COMPLETOS'],
            'VALOR_BANDEJA_MESA_TRABAJO' => $costos['MESA_TRABAJO'] * ($items['MESA_TRABAJO_COMPLETOS'] + $items['MESA_TRABAJO_COMITE']),
            'VALOR_PROYECTADO_MESA_TRABAJO' => $costos['MESA_TRABAJO'] * $proyected_values['MESA_TRABAJO_PROYECTADOS'],
            'FACTURA_ESTIMADA_MESA_TRABAJO' => $costos['MESA_TRABAJO'] * $proyected_values['MESA_TRABAJO_PROYECTADOS'],
            // ANALISIS
            'SUMATORIA_BANDEJA_ANALISIS' => $items['ANALISIS_COMPLETOS'] + $items['ANALISIS_COMITE'] + $items['ANALISIS_PROFESIONALES'],
            'SUMATORIA_SUBTOTAL_BANDEJA_ANALISIS' => ($items['ANALISIS_COMPLETOS'] + $items['ANALISIS_COMITE'] + $items['ANALISIS_PROFESIONALES']) * $costos['ANALISIS'],
            'APPROXIMATION_ANALISIS' => intval(round(($proyected_values['ANALISIS_PROYECTADOS'] / $weekDays) * ($reportDay))),
            'RESIDUAL_ANALISIS' => $items['ANALISIS_COMPLETOS'] - intval(round(($proyected_values['ANALISIS_PROYECTADOS'] / $weekDays) * ($reportDay))),
            'DIF_ACUMULADA_ANALISIS' => 0 - $proyected_values['ANALISIS_PROYECTADOS'] + $items['ANALISIS_COMPLETOS'] + $items['ANALISIS_COMITE'],
            'DIF_REAL_ANALISIS' => 0 - $proyected_values['ANALISIS_PROYECTADOS'] + $items['ANALISIS_COMPLETOS'],
            'VALOR_ACUMULADO_ANALISIS' => $costos['ANALISIS'] * $items['ANALISIS_COMPLETOS'],
            'VALOR_BANDEJA_ANALISIS' => $costos['ANALISIS'] * ($items['ANALISIS_COMPLETOS'] + $items['ANALISIS_COMITE']),
            'VALOR_PROYECTADO_ANALISIS' => $costos['ANALISIS'] * $proyected_values['ANALISIS_PROYECTADOS'],
            'FACTURA_ESTIMADA_ANALISIS' => $costos['ANALISIS'] * $proyected_values['ANALISIS_PROYECTADOS'],
        ];

        $calculated_values['TOTAL_VALOR_ACUMULADO'] = $calculated_values['VALOR_ACUMULADO_MESA_TRABAJO'] + $calculated_values['VALOR_ACUMULADO_AT'] + $calculated_values['VALOR_ACUMULADO_EL'] + $calculated_values['VALOR_ACUMULADO_PCL'] + $calculated_values['VALOR_ACUMULADO_DISCAPACIDAD'] + $calculated_values['VALOR_ACUMULADO_REHABILITACION'] + $calculated_values['VALOR_ACUMULADO_RECOMENDACION'] + $calculated_values['VALOR_ACUMULADO_AUDITORIA'] + $calculated_values['VALOR_ACUMULADO_VALORACION_NACIONAL'] + $calculated_values['VALOR_ACUMULADO_ANALISIS'];
        $calculated_values['TOTAL_VALOR_BANDEJA'] = $calculated_values['VALOR_BANDEJA_MESA_TRABAJO'] + $calculated_values['VALOR_BANDEJA_AT'] + $calculated_values['VALOR_BANDEJA_EL'] + $calculated_values['VALOR_BANDEJA_PCL'] + $calculated_values['VALOR_BANDEJA_DISCAPACIDAD'] + $calculated_values['VALOR_BANDEJA_REHABILITACION'] + $calculated_values['VALOR_BANDEJA_RECOMENDACION'] + $calculated_values['VALOR_BANDEJA_AUDITORIA'] + $calculated_values['VALOR_BANDEJA_VALORACION_NACIONAL'] + $calculated_values['VALOR_BANDEJA_ANALISIS'];
        $calculated_values['TOTAL_VALOR_PROYECTADO'] = $calculated_values['VALOR_PROYECTADO_MESA_TRABAJO'] + $calculated_values['VALOR_PROYECTADO_AT'] + $calculated_values['VALOR_PROYECTADO_EL'] + $calculated_values['VALOR_PROYECTADO_PCL'] + $calculated_values['VALOR_PROYECTADO_DISCAPACIDAD'] + $calculated_values['VALOR_PROYECTADO_REHABILITACION'] + $calculated_values['VALOR_PROYECTADO_RECOMENDACION'] + $calculated_values['VALOR_PROYECTADO_AUDITORIA'] + $calculated_values['VALOR_PROYECTADO_VALORACION_NACIONAL'] + $calculated_values['VALOR_PROYECTADO_ANALISIS'];
        $calculated_values['TOTAL_FACTURA_ESTIMADA'] = $calculated_values['FACTURA_ESTIMADA_MESA_TRABAJO'] + $calculated_values['FACTURA_ESTIMADA_AT'] + $calculated_values['FACTURA_ESTIMADA_EL'] + $calculated_values['FACTURA_ESTIMADA_PCL'] + $calculated_values['FACTURA_ESTIMADA_DISCAPACIDAD'] + $calculated_values['FACTURA_ESTIMADA_REHABILITACION'] + $calculated_values['FACTURA_ESTIMADA_RECOMENDACION'] + $calculated_values['FACTURA_ESTIMADA_AUDITORIA'] + $calculated_values['FACTURA_ESTIMADA_VALORACION_NACIONAL'] + $calculated_values['FACTURA_ESTIMADA_ANALISIS'];

        $calculated_values['TOTAL_CASOS_PROYECTADOS'] = $proyected_values['DISCAPACIDAD_PROYECTADOS'] + $proyected_values['MESA_TRABAJO_PROYECTADOS'] + $proyected_values['AT_PROYECTADOS'] + $proyected_values['EL_PROYECTADOS'] + $proyected_values['PCL_PROYECTADOS'] + $proyected_values['REHABILITACION_PROYECTADOS'] + $proyected_values['RECOMENDACION_PROYECTADOS'] + $proyected_values['AUDITORIA_PROYECTADOS'] + $proyected_values['VALORACION_NACIONAL_PROYECTADOS'] + $proyected_values['ANALISIS_PROYECTADOS'];
        $calculated_values['TOTAL_CASOS_COMPLETOS'] = $items['MESA_TRABAJO_COMPLETOS'] + $items['AT_COMPLETOS'] + $items['EL_COMPLETOS'] + $items['PCL_COMPLETOS'] + $items['DISCAPACIDAD_COMPLETOS'] + $items['REHABILITACION_COMPLETOS'] + $items['RECOMENDACION_COMPLETOS'] + $items['AUDITORIA_COMPLETOS'] + $items['VALORACION_NACIONAL_COMPLETOS'] + $items['ANALISIS_COMPLETOS'];

        $calculated_values['SUMATORIA_TOTAL_BANDEJA'] = $calculated_values['SUMATORIA_SUBTOTAL_BANDEJA_AT'] + $calculated_values['SUMATORIA_SUBTOTAL_BANDEJA_EL'] + $calculated_values['SUMATORIA_SUBTOTAL_BANDEJA_PCL'] + $calculated_values['SUMATORIA_SUBTOTAL_BANDEJA_DISCAPACIDAD'] + $calculated_values['SUMATORIA_SUBTOTAL_BANDEJA_REHABILITACION'] + $calculated_values['SUMATORIA_SUBTOTAL_BANDEJA_RECOMENDACION'] + $calculated_values['SUMATORIA_SUBTOTAL_BANDEJA_AUDITORIA'] + $calculated_values['SUMATORIA_SUBTOTAL_BANDEJA_VALORACION_NACIONAL'] + $calculated_values['SUMATORIA_SUBTOTAL_BANDEJA_ANALISIS'] + $calculated_values['SUMATORIA_SUBTOTAL_BANDEJA_MESA_TRABAJO'];

        // FORMATTED NUMBERS
        $calculated_values['TOTAL_VALOR_ACUMULADO'] = "$ " . number_format($calculated_values['TOTAL_VALOR_ACUMULADO'], 0, ',', '.');
        $calculated_values['TOTAL_VALOR_BANDEJA'] = "$ " . number_format($calculated_values['TOTAL_VALOR_BANDEJA'], 0, ',', '.');
        $calculated_values['TOTAL_VALOR_PROYECTADO'] = "$ " . number_format($calculated_values['TOTAL_VALOR_PROYECTADO'], 0, ',', '.');
        $calculated_values['TOTAL_FACTURA_ESTIMADA'] = "$ " . number_format($calculated_values['TOTAL_FACTURA_ESTIMADA'], 0, ',', '.');

        $calculated_values['VALOR_ACUMULADO_AT'] = "$ " . number_format($calculated_values['VALOR_ACUMULADO_AT'], 0, ',', '.');
        $calculated_values['VALOR_ACUMULADO_EL'] = "$ " . number_format($calculated_values['VALOR_ACUMULADO_EL'], 0, ',', '.');
        $calculated_values['VALOR_ACUMULADO_PCL'] = "$ " . number_format($calculated_values['VALOR_ACUMULADO_PCL'], 0, ',', '.');
        $calculated_values['VALOR_ACUMULADO_DISCAPACIDAD'] = "$ " . number_format($calculated_values['VALOR_ACUMULADO_DISCAPACIDAD'], 0, ',', '.');
        $calculated_values['VALOR_ACUMULADO_REHABILITACION'] = "$ " . number_format($calculated_values['VALOR_ACUMULADO_REHABILITACION'], 0, ',', '.');
        $calculated_values['VALOR_ACUMULADO_RECOMENDACION'] = "$ " . number_format($calculated_values['VALOR_ACUMULADO_RECOMENDACION'], 0, ',', '.');
        $calculated_values['VALOR_ACUMULADO_AUDITORIA'] = "$ " . number_format($calculated_values['VALOR_ACUMULADO_AUDITORIA'], 0, ',', '.');
        $calculated_values['VALOR_ACUMULADO_VALORACION_NACIONAL'] = "$ " . number_format($calculated_values['VALOR_ACUMULADO_VALORACION_NACIONAL'], 0, ',', '.');
        $calculated_values['VALOR_ACUMULADO_ANALISIS'] = "$ " . number_format($calculated_values['VALOR_ACUMULADO_ANALISIS'], 0, ',', '.');
        $calculated_values['VALOR_ACUMULADO_MESA_TRABAJO'] = "$ " . number_format($calculated_values['VALOR_ACUMULADO_MESA_TRABAJO'], 0, ',', '.');

        $calculated_values['SUMATORIA_SUBTOTAL_BANDEJA_AT'] = "$ " . number_format($calculated_values['SUMATORIA_SUBTOTAL_BANDEJA_AT'], 0, ',', '.');
        $calculated_values['SUMATORIA_SUBTOTAL_BANDEJA_EL'] = "$ " . number_format($calculated_values['SUMATORIA_SUBTOTAL_BANDEJA_EL'], 0, ',', '.');
        $calculated_values['SUMATORIA_SUBTOTAL_BANDEJA_PCL'] = "$ " . number_format($calculated_values['SUMATORIA_SUBTOTAL_BANDEJA_PCL'], 0, ',', '.');
        $calculated_values['SUMATORIA_SUBTOTAL_BANDEJA_DISCAPACIDAD'] = "$ " . number_format($calculated_values['SUMATORIA_SUBTOTAL_BANDEJA_DISCAPACIDAD'], 0, ',', '.');
        $calculated_values['SUMATORIA_SUBTOTAL_BANDEJA_REHABILITACION'] = "$ " . number_format($calculated_values['SUMATORIA_SUBTOTAL_BANDEJA_REHABILITACION'], 0, ',', '.');
        $calculated_values['SUMATORIA_SUBTOTAL_BANDEJA_RECOMENDACION'] = "$ " . number_format($calculated_values['SUMATORIA_SUBTOTAL_BANDEJA_RECOMENDACION'], 0, ',', '.');
        $calculated_values['SUMATORIA_SUBTOTAL_BANDEJA_AUDITORIA'] = "$ " . number_format($calculated_values['SUMATORIA_SUBTOTAL_BANDEJA_AUDITORIA'], 0, ',', '.');
        $calculated_values['SUMATORIA_SUBTOTAL_BANDEJA_VALORACION_NACIONAL'] = "$ " . number_format($calculated_values['SUMATORIA_SUBTOTAL_BANDEJA_VALORACION_NACIONAL'], 0, ',', '.');
        $calculated_values['SUMATORIA_SUBTOTAL_BANDEJA_ANALISIS'] = "$ " . number_format($calculated_values['SUMATORIA_SUBTOTAL_BANDEJA_ANALISIS'], 0, ',', '.');
        $calculated_values['SUMATORIA_SUBTOTAL_BANDEJA_MESA_TRABAJO'] = "$ " . number_format($calculated_values['SUMATORIA_SUBTOTAL_BANDEJA_MESA_TRABAJO'], 0, ',', '.');

        $calculated_values['SUMATORIA_TOTAL_BANDEJA'] = "$ " . number_format($calculated_values['SUMATORIA_TOTAL_BANDEJA'], 0, ',', '.');
        //END FORMATTED NUMBERS

        $values = array_merge($calculated_values, $items, $proyected_values);

        return view('information.how_to_going')
            ->with('states_info', $values)
            ->with('services', $client->services)
            ->with('costos', $costos)
            ->with('affiliates', [])
            ->with('states', State::get())
            ->with('actions', Action::get())
            ->with('users', User::get())
            ->with('init_date', $req->init_date)
            ->with('last_date', $req->last_date)
            ->with('week_days', $weekDays)
            ->with('report_day', $req->report_day)
            ->with('activity_actions_total', 1);
    }

    public function howToGoingDetail(Request $req, $cpath)
    {
        $client = Client::where('path', $cpath)->firstOrFail();

        $req->init_date = $req->input('init_date_submit');
        $req->last_date = $req->input('last_date_submit');

        $items = DB::select(str_replace(['?1', '?2', '?3', '?4'], [$client->id, $req->init_date, $req->last_date, '2017-08-01'], Report::HOW_TO_GOING_ALIANSALUD));
        $items = json_decode(json_encode($items), true);

        $states = [];
        for ($i = 0; $i < 9; $i++) {
            $states[$i] = [];
            foreach ($items as $item) {
                if ($item['diference_days'] == $i && !in_array(['name' => $item['state_name'], 'id' => $item['state_id']], $states[$i])) {
                    $states[$i][] = ['name' => $item['state_name'], 'id' => $item['state_id']];
                }
            }
        }

        $information = [];
        for ($i = 0; $i < 25; $i++)
            for ($k = 0; $k < 200; $k++)
                for ($l = 0; $l < 10; $l++)
                    $information[$i][$k][$l] = 0;


        foreach ($items as $item) {
            $information[$item['service_id']][$item['state_id']][$item['diference_days']] = $item['counter'];
        }

        return view('information.how_to_going_detail')
            ->with('information', $information)
            ->with('states', $states)
            ->with('activity_actions_total', 1);
    }


    public function massive2(Request $req, $cpath)
    {
        $client = Client::where('path', $cpath)->firstOrFail();

        if ($req->isMethod('post')) {

            $excel = Excel::load($req->file('massive')->path());
            $rows = $excel->toArray();

            foreach ($rows as $index => $row) {
                Log::info('Massive ' . $index);

                $affiliate = Affiliate::where('doc_type', $row['doc_type'])
                    ->where('doc_number', $row['doc_number'])
                    ->where('client_id', $client->id)
                    ->first();

                if (!$affiliate) {
                    continue;
                }

                $affiliate->address = $row['address'];
                $affiliate->phone = $row['phone'];
                $affiliate->cellphone = $row['cellphone'];
                $affiliate->email = $row['email'];
                $affiliate->save();

                $employer = Employer::where('nit', $row['enit'])
                    ->orWhere('nit', $row['enit'] . '-' . $row['enit_digit'])
                    ->first();

                if (!$employer) {
                    $employer = new Employer;
                    $employer->nit = $row['enit'];
                    $employer->nit_digit = $row['enit_digit'];
                    $employer->name = $row['ename'];
                    $employer->arl = $row['earl'];
                    $employer->activity = 1011;
                    $employer->save();
                }

                $branch = Branch::where('employer_id', $employer->id)
                    ->first();

                if (!$branch) {
                    $branch = new Branch;
                    $branch->employer_id = $employer->id;
                    $branch->name = 'UNICA';
                    $branch->department = $row['bdepartment'];
                    $branch->municipality = $row['bmunicipality'];
                    $branch->address = $row['baddress'];
                    $branch->contact = $row['ename'];
                    $branch->phone = $row['bphone'];
                    $branch->cellphone = $row['bcellphone'];
                    $branch->email = $row['bemail'];
                    $branch->save();
                }

                $employment = Employment::where('affiliate_id', $affiliate->id)
                    ->where('employer_id', $employer->id)
                    ->where('branch_id', $branch->id)
                    ->first();

                if (!$employment) {
                    $employment = new Employment;
                    $employment->affiliate_id = $affiliate->id;
                    $employment->employer_id = $employer->id;
                    $employment->branch_id = $branch->id;
                    $employment->position = 'NO ESPECIFICA';
                    $employment->ocupation = 1111;
                    $employment->position_functions = 'NO ESPECIFICA';
                    $employment->start_date = '2016-01-01';
                    $employment->company_time = 22;
                    $employment->position_time = 22;
                    $employment->actual = 1;
                    $employment->save();
                }
            }

            return redirect('nuevo');
        }
        return view('admin.massive');
    }

    public function massive(Request $req, $cpath)
    {
        $client = Client::where('path', $cpath)->firstOrFail();

        if ($req->isMethod('post')) {

            $excel = Excel::load($req->file('massive')->path());
            $rows = $excel->toArray();

            foreach ($rows as $index => $row) {
                Log::info('Massive ' . $index);

                $affiliate = Affiliate::where('doc_type', $row['doc_type'])
                    ->where('doc_number', $row['doc_number'])
                    ->where('client_id', $client->id)
                    ->first();

                if (!$affiliate) {
                    $affiliate = new Affiliate;
                    $affiliate->client_id = $client->id;
                    $affiliate->doc_type = $row['doc_type'];
                    $affiliate->doc_number = $row['doc_number'];
                    $affiliate->first_name = $row['first_name'];
                    $affiliate->last_name = $row['last_name1'];
                    $affiliate->birthday = $row['birthday'];
                    $affiliate->gender = $row['gender'];
                    $affiliate->civil_status = 1;
                    $affiliate->school_level = 1;
                    $affiliate->department = $row['department'];
                    $affiliate->municipality = $row['municipality'];
                    $affiliate->address = 'Calle 123';
                    $affiliate->phone = '3000000000';
                    $affiliate->cellphone = '3000000000';
                    $affiliate->affiliation_type = 1;
                    $affiliate->eps = 98;
                    $affiliate->regional = 1;
                    $affiliate->afp = 0;
                    $affiliate->arl = 0;
                    $affiliate->save();
                }

                // $employer = Employer::where('nit', $row['enit'])
                //     ->first();

                // if (!$employer) {
                //     $employer            = new Employer;
                //     $employer->nit       = $row['enit'];
                //     $employer->nit_digit = $row['enit_digit'];
                //     $employer->name      = $row['ename'];
                //     $employer->arl       = $row['earl'];
                //     $employer->activity  = $row['eactivity'];
                //     $employer->save();
                // }

                // $branch = Branch::where('employer_id', $employer->id)
                //     ->first();

                // if (!$branch) {
                //     $branch               = new Branch;
                //     $branch->employer_id  = $employer->id;
                //     $branch->name         = $row['bname'];
                //     $branch->department   = $row['bdepartment'];
                //     $branch->municipality = $row['bmunicipality'];
                //     $branch->address      = $row['baddress'];
                //     $branch->contact      = $row['bcontact'];
                //     $branch->phone        = $row['bphone'];
                //     $branch->email        = $row['bemail'];
                //     $branch->save();
                // }

                // $employment = Employment::where('affiliate_id', $affiliate->id)
                //     ->where('employer_id', $employer->id)
                //     ->where('branch_id', $branch->id)
                //     ->first();

                // if (!$employment) {
                //     $employment                     = new Employment;
                //     $employment->affiliate_id       = $affiliate->id;
                //     $employment->employer_id        = $employer->id;
                //     $employment->branch_id          = $branch->id;
                //     $employment->position           = $row['position'];
                //     $employment->ocupation          = $row['ocupation'];
                //     $employment->position_functions = $row['position_functions'];
                //     $employment->start_date         = $row['start_date'];
                //     $employment->company_time       = $row['company_time'];
                //     $employment->position_time      = $row['position_time'];
                //     $employment->actual             = $row['actual'];
                //     $employment->save();
                // }

                $activity = new Activity;
                $activity->client_id = $client->id;
                $activity->service_id = 12;
                $activity->affiliate_id = $affiliate->id;
                $activity->user_id = Auth::id();
                // $activity->employment_id = $employment->id;
                $activity->state_id = Service::find(1)->default_state_id;
                $activity->save();

                $rehabilitation = new Rehabilitation;
                $rehabilitation->activity_id = $activity->id;
                $rehabilitation->dx_primary = $row['dx_primary'];
                $rehabilitation->dx_primary_description = $row['dx_primary_description'];
                $rehabilitation->inability_days = $row['inability_days'];
                $rehabilitation->dx_origin = $row['dx_origin'];
                $rehabilitation->clinical_history = $row['clinical_history'];
                $rehabilitation->format = $row['format'];
                $rehabilitation->terms = $row['terms'];
                $rehabilitation->treatment_pharmacological = $row['treatment_pharmacological'];
                $rehabilitation->treatment_surgical = $row['treatment_surgical'];
                $rehabilitation->treatment_physical = $row['treatment_physical'];
                $rehabilitation->treatment_occupational = $row['treatment_occupational'];
                $rehabilitation->treatment_speech = $row['treatment_speech'];
                $rehabilitation->treatment_others = $row['treatment_others'];
                $rehabilitation->treatment_purpose = $row['treatment_purpose'];
                $rehabilitation->treatment_duration = $row['treatment_duration'];
                $rehabilitation->abc_perfomance = $row['abc_perfomance'];
                $rehabilitation->avd_perfomance = $row['avd_perfomance'];
                $rehabilitation->avd_type = $row['avd_type'];
                $rehabilitation->laboral_role = $row['laboral_role'];
                $rehabilitation->forecast = $row['forecast'];
                $rehabilitation->reability = $row['reability'];
                $rehabilitation->save();
            }

            return redirect('nuevo');
        }
        return view('admin.massive');
    }

    public function uploadInvoices(Request $req, $cpath)
    {
        set_time_limit(0);
        $client = Client::where('path', $cpath)->firstOrFail();
        $excel = Excel::filter('chunk')->load($req->file('service_file')->path());
        $invoice = Invoice::where('client_id', '=', $client->id)->where('closed', '=', '0')->first();
        $type = $req->input('type');
        $excel->chunk(10, function ($reader) use ($invoice, $client, $type) {
            $reader->each(function ($row) use ($invoice, $client, $type) {
                if (isset($row['servicio_id'])) {

                    try {
                        $invoice_activity = InvoiceActivity::where('invoice_id', '=', $invoice->id)->where('activity_id', '=', $row['servicio_id'])->where('service', '=', $row['nombre'])->where('type', '=', 'facturado')->first();
                        if (!$invoice_activity) {
                            $invoice_activity = new InvoiceActivity;
                            $invoice_activity->invoice_id = $invoice->id;
                            $invoice_activity->activity_id = $row['servicio_id'];
                            $invoice_activity->service = $row['nombre'];
                            if (isset($row['adicional'])) {
                                $invoice_activity->aditional = $row['adicional'];
                            }
                            $invoice_activity->type = $type;
                            $invoice_activity->save();
                        }
                    } catch (Exception $e) {
                        Log::error($e->getMessage());
                        Log::error('[Servicio: ' . $row['servicio_id'] . ' ]');
                    }
                }
            });
        });
        $invoice->quantity = InvoiceActivity::where('invoice_id', '=', $invoice->id)->where('type', '=', 'facturado')->count();
        $invoice->save();

        return redirect('facturacion');
    }

    public function uploadDocinvoices(Request $req, $cpath)
    {
        set_time_limit(0);
        $client = Client::where('path', $cpath)->firstOrFail();
        $excel = Excel::filter('chunk')->load($req->file('service_file')->path());
        $docinvoice = new Docinvoice;
        $docinvoice->client_id = $client->id;
        $docinvoice->quantity = 0;
        $docinvoice->save();

        $excel->chunk(10, function ($reader) use ($docinvoice, $client) {
            $reader->each(function ($row) use ($docinvoice, $client) {
                if (isset($row['servicio_id'])) {

                    try {
                        $activity = Activity::find($row['servicio_id']);
                        $service_instance = null;

                        if ($service_instance && !$service_instance->docinvoice_id) {
                            $service_instance->docinvoice_id = $docinvoice->id;
                            $service_instance->save();
                        }
                    } catch (Exception $e) {
                        Log::error($e->getMessage());
                        Log::error('[Servicio: ' . $row['servicio_id'] . ' ]');
                    }
                }
            });
        });
        $docinvoice->quantity = \App\Dictum::where('docinvoice_id', '=', $docinvoice->id)->count();
        $docinvoice->quantity = $docinvoice->quantity + \App\Pcl::where('docinvoice_id', '=', $docinvoice->id)->count();

        $docinvoice->save();

        return redirect('facturacion-profesionales');
    }

    public function bill(Request $req, $cpath, $initial_date, $final_date, $invoice_num)
    {
        $initial_date .= ' 00:00:00';
        $final_date .= ' 23:59:59';

        $prices = array(
            Service::SERVICE_AT_FAMISANAR => 1850,
            Service::SERVICE_EL_FAMISANAR => 4400,
            Service::SERVICE_EL_COOMEVA => 1409,
            Service::SERVICE_PCL_MAPFRE => 8750,
            Service::SERVICE_PCL_FAMISANAR => 4400,
            Service::SERVICE_CONTROVERSY_FAMISANAR => 0,
            Service::SERVICE_REHABILITATION_FAMISANAR => 1950,
            Service::SERVICE_RECOMMENDATIONS_FAMISANAR => 2060,
            Service::SERVICE_DISABILITY_FAMISANAR => 2200,
            Service::SERVICE_AUDIT_FAMISANAR => 149,
            Service::SERVICE_RECOBRO_FAMISANAR => 322,
            Service::SERVICE_GLOSA_FAMISANAR => 0,
            Service::SERVICE_INFORMATIVO_FAMISANAR => 0,
            Service::SERVICE_VALORATION_FAMISANAR => 1750,
        );
        $total = 0;
        $client = Client::where('path', $cpath)->firstOrFail();
        $activities = array();
        $concepts = array();

        $EL = DB::select(BillReport::EL_SQL, [$client->id, $initial_date, $final_date]);
        $activities[] = json_decode(json_encode($EL), true);
        if (count($EL) > 0) {
            $concepts[] = array(
                'name' => 'CALIFICACION DE ORIGEN EL',
                'units' => count($EL),
                'unitv' => $prices[Service::SERVICE_EL_FAMISANAR],
                'totalv' => $prices[Service::SERVICE_EL_FAMISANAR] * count($EL),
            );
        }

        $total += count($EL) * $prices[Service::SERVICE_EL_FAMISANAR];

        $AT = DB::select(BillReport::AT_SQL, [$client->id, $initial_date, $final_date]);
        $activities[] = json_decode(json_encode($AT), true);
        if (count($AT) > 0) {
            $concepts[] = array(
                'name' => 'CALIFICACION DE ORIGEN AT',
                'units' => count($AT),
                'unitv' => $prices[Service::SERVICE_AT_FAMISANAR],
                'totalv' => $prices[Service::SERVICE_AT_FAMISANAR] * count($AT),
            );
        }

        $total += count($AT) * $prices[Service::SERVICE_AT_FAMISANAR];

        $PCL = DB::select(BillReport::PCL_SQL, [$client->id, $initial_date, $final_date]);
        $activities[] = json_decode(json_encode($PCL), true);
        if (count($PCL) > 0) {
            $concepts[] = array(
                'name' => 'PCL',
                'units' => count($PCL),
                'unitv' => $prices[Service::SERVICE_PCL_FAMISANAR],
                'totalv' => $prices[Service::SERVICE_PCL_FAMISANAR] * count($PCL),
            );
        }

        $total += count($PCL) * $prices[Service::SERVICE_PCL_FAMISANAR];

        $REHABILITATION = DB::select(BillReport::REHABILITATION_SQL, [$client->id, $initial_date, $final_date]);
        $activities[] = json_decode(json_encode($REHABILITATION), true);
        if (count($REHABILITATION) > 0) {
            $concepts[] = array(
                'name' => 'CONCEPTO DE REHABILITACION',
                'units' => count($REHABILITATION),
                'unitv' => $prices[Service::SERVICE_REHABILITATION_FAMISANAR],
                'totalv' => $prices[Service::SERVICE_REHABILITATION_FAMISANAR] * count($REHABILITATION),
            );
        }

        $total += count($REHABILITATION) * $prices[Service::SERVICE_REHABILITATION_FAMISANAR];

        $RECOMMENDATION = DB::select(BillReport::RECOMMENDATION_SQL, [$client->id, $initial_date, $final_date]);
        $activities[] = json_decode(json_encode($RECOMMENDATION), true);
        if (count($RECOMMENDATION) > 0) {
            $concepts[] = array(
                'name' => 'RECOMENDACIONES LABORALES',
                'units' => count($RECOMMENDATION),
                'unitv' => $prices[Service::SERVICE_RECOMMENDATIONS_FAMISANAR],
                'totalv' => $prices[Service::SERVICE_RECOMMENDATIONS_FAMISANAR] * count($RECOMMENDATION),
            );
        }

        $total += count($RECOMMENDATION) * $prices[Service::SERVICE_RECOMMENDATIONS_FAMISANAR];

        $DISABILITY = DB::select(BillReport::DISABILITY_SQL, [$client->id, $initial_date, $final_date]);
        $activities[] = json_decode(json_encode($DISABILITY), true);
        if (count($DISABILITY) > 0) {
            $concepts[] = array(
                'name' => 'CERTIFICADO DE DISCAPACIDAD',
                'units' => count($DISABILITY),
                'unitv' => $prices[Service::SERVICE_DISABILITY_FAMISANAR],
                'totalv' => $prices[Service::SERVICE_DISABILITY_FAMISANAR] * count($DISABILITY),
            );
        }

        $total += count($DISABILITY) * $prices[Service::SERVICE_DISABILITY_FAMISANAR];

        $AUDIT = DB::select(BillReport::AUDIT_SQL, [$client->id, $initial_date, $final_date]);
        $activities[] = json_decode(json_encode($AUDIT), true);
        if (count($AUDIT) > 0) {
            $concepts[] = array(
                'name' => 'AUDITORIA DE INCAPACIDADES',
                'units' => count($AUDIT),
                'unitv' => $prices[Service::SERVICE_AUDIT_FAMISANAR],
                'totalv' => $prices[Service::SERVICE_AUDIT_FAMISANAR] * count($AUDIT),
            );
        }

        $total += count($AUDIT) * $prices[Service::SERVICE_AUDIT_FAMISANAR];

        $RECOBRO = BillReport::recobros($client->id, $initial_date, $final_date);
        $activities[] = $RECOBRO;
        if (count($RECOBRO) > 0) {
            $concepts[] = array(
                'name' => 'ANALISIS Y ALISTAMIENTO DE FACTURAS - RECOBRO',
                'units' => count($RECOBRO),
                'unitv' => $prices[Service::SERVICE_RECOBRO_FAMISANAR],
                'totalv' => $prices[Service::SERVICE_RECOBRO_FAMISANAR] * count($RECOBRO),
            );
        }

        $total += count($RECOBRO) * $prices[Service::SERVICE_RECOBRO_FAMISANAR];

        $activities = collect($activities)->collapse();

        $formatter = new NumberFormatter('es', NumberFormatter::SPELLOUT);

        $pdf = PDF::loadView("extras.bill", [
            'client' => $client,
            'concepts' => $concepts,
            'total' => $total,
            'total_str' => $formatter->format($total),
            'invoice_num' => $invoice_num,
            'initial_date' => $initial_date,
            'final_date' => $final_date,
        ]);

        $activities = collect($activities)->map(function ($item) {
            $item['FECHA_SERVICIO'] = \PHPExcel_Shared_Date::PHPToExcel(\Carbon\Carbon::parse($item['FECHA_SERVICIO'])->timestamp);

            return $item;
        });

        $fexcel = Excel::create("ANEXO", function ($excel) use ($activities) {

            $excel->sheet('HOJA 1', function ($sheet) use ($activities) {
                $sheet->setColumnFormat([
                    'F' => \PHPExcel_Style_NumberFormat::FORMAT_DATE_YYYYMMDD2,
                ]);
                $sheet->fromArray($activities);
                $sheet->freezeFirstRow();
                $sheet->setAutoFilter();
                $sheet->row(1, function ($row) {
                    $row->setBackground('#7EBE2C');
                    $row->setFontColor('#FFFFFF');
                    $row->setFontWeight('bold');
                });
                $sheet->setAutoSize(true);
            });
            $excel->setActiveSheetIndex(0);
        })->store('xlsx', false, true);

        $zipper = new \Chumper\Zipper\Zipper;

        $zipper->make(storage_path("INVOICE {$invoice_num}.zip"));

        $zipper->addString('INVOICE.pdf', $pdf->output());
        $zipper->add($fexcel['full']);

        $zipper->close();

        unlink($fexcel['full']);

        return response()->download(storage_path("INVOICE {$invoice_num}.zip"))->deleteFileAfterSend(true);
    }

    public function bill_orienta(Request $req, $cpath)
    {
        $invoice_num = $req->input('invoice_num');
        $prices = array(
            Service::SERVICE_AT_FAMISANAR => 1850,
            Service::SERVICE_EL_FAMISANAR => 4400,
            Service::SERVICE_EL_COOMEVA => 1409,
            Service::SERVICE_PCL_MAPFRE => 8750,
            Service::SERVICE_PCL_FAMISANAR => 4400,
            Service::SERVICE_CONTROVERSY_FAMISANAR => 0,
            Service::SERVICE_REHABILITATION_FAMISANAR => 1950,
            Service::SERVICE_RECOMMENDATIONS_FAMISANAR => 2060,
            Service::SERVICE_DISABILITY_FAMISANAR => 2200,
            Service::SERVICE_MOBILITY_FAMISANAR => 2200,
            Service::SERVICE_AUDIT_FAMISANAR => 149,
            Service::SERVICE_RECOBRO_FAMISANAR => 322,
            Service::SERVICE_GLOSA_FAMISANAR => 0,
            Service::SERVICE_INFORMATIVO_FAMISANAR => 0,
            Service::SERVICE_VALORATION_FAMISANAR => 1750,
        );
        $total = 0;
        $client = Client::where('path', $cpath)->firstOrFail();
        $activities = array();
        $concepts = array();

        $EL = DB::select(BillReport::EL_SQL_NEW, [$client->id, $invoice_num]);
        $activities[] = json_decode(json_encode($EL), true);
        if (count($EL) > 0) {
            $concepts[] = array(
                'name' => 'CALIFICACION DE ORIGEN EL',
                'units' => count($EL),
                'unitv' => $prices[Service::SERVICE_EL_FAMISANAR],
                'totalv' => $prices[Service::SERVICE_EL_FAMISANAR] * count($EL),
            );
        }

        $total += count($EL) * $prices[Service::SERVICE_EL_FAMISANAR];

        $AT = DB::select(BillReport::AT_SQL_NEW, [$client->id, $invoice_num]);
        $activities[] = json_decode(json_encode($AT), true);
        if (count($AT) > 0) {
            $concepts[] = array(
                'name' => 'CALIFICACION DE ORIGEN AT',
                'units' => count($AT),
                'unitv' => $prices[Service::SERVICE_AT_FAMISANAR],
                'totalv' => $prices[Service::SERVICE_AT_FAMISANAR] * count($AT),
            );
        }

        $total += count($AT) * $prices[Service::SERVICE_AT_FAMISANAR];

        $PCL = DB::select(BillReport::PCL_SQL_NEW, [$client->id, $invoice_num]);
        $activities[] = json_decode(json_encode($PCL), true);
        if (count($PCL) > 0) {
            $concepts[] = array(
                'name' => 'PCL',
                'units' => count($PCL),
                'unitv' => $prices[Service::SERVICE_PCL_FAMISANAR],
                'totalv' => $prices[Service::SERVICE_PCL_FAMISANAR] * count($PCL),
            );
        }

        $total += count($PCL) * $prices[Service::SERVICE_PCL_FAMISANAR];

        $REHABILITATION = DB::select(BillReport::REHABILITATION_SQL_NEW, [$client->id, $invoice_num]);
        $activities[] = json_decode(json_encode($REHABILITATION), true);
        if (count($REHABILITATION) > 0) {
            $concepts[] = array(
                'name' => 'CONCEPTO DE REHABILITACION',
                'units' => count($REHABILITATION),
                'unitv' => $prices[Service::SERVICE_REHABILITATION_FAMISANAR],
                'totalv' => $prices[Service::SERVICE_REHABILITATION_FAMISANAR] * count($REHABILITATION),
            );
        }

        $total += count($REHABILITATION) * $prices[Service::SERVICE_REHABILITATION_FAMISANAR];

        $RECOMMENDATION = DB::select(BillReport::RECOMMENDATION_SQL_NEW, [$client->id, $invoice_num]);
        $activities[] = json_decode(json_encode($RECOMMENDATION), true);
        if (count($RECOMMENDATION) > 0) {
            $concepts[] = array(
                'name' => 'RECOMENDACIONES LABORALES',
                'units' => count($RECOMMENDATION),
                'unitv' => $prices[Service::SERVICE_RECOMMENDATIONS_FAMISANAR],
                'totalv' => $prices[Service::SERVICE_RECOMMENDATIONS_FAMISANAR] * count($RECOMMENDATION),
            );
        }

        $total += count($RECOMMENDATION) * $prices[Service::SERVICE_RECOMMENDATIONS_FAMISANAR];

        $DISABILITY = DB::select(BillReport::DISABILITY_SQL_NEW, [$client->id, $invoice_num]);
        $activities[] = json_decode(json_encode($DISABILITY), true);
        if (count($DISABILITY) > 0) {
            $concepts[] = array(
                'name' => 'CERTIFICADO DE DISCAPACIDAD',
                'units' => count($DISABILITY),
                'unitv' => $prices[Service::SERVICE_DISABILITY_FAMISANAR],
                'totalv' => $prices[Service::SERVICE_DISABILITY_FAMISANAR] * count($DISABILITY),
            );
        }

        $total += count($DISABILITY) * $prices[Service::SERVICE_DISABILITY_FAMISANAR];

        $MOBILITY = DB::select(BillReport::MOBILITY_SQL_NEW, [$client->id, $invoice_num]);
        $activities[] = json_decode(json_encode($MOBILITY), true);
        if (count($MOBILITY) > 0) {
            $concepts[] = array(
                'name' => 'CERTIFICADO DE DISCAPACIDAD',
                'units' => count($MOBILITY),
                'unitv' => $prices[Service::SERVICE_MOBILITY_FAMISANAR],
                'totalv' => $prices[Service::SERVICE_MOBILITY_FAMISANAR] * count($DISABILITY),
            );
        }

        $total += count($DISABILITY) * $prices[Service::SERVICE_MOBILITY_FAMISANAR];

        $AUDIT = DB::select(BillReport::AUDIT_SQL_NEW, [$client->id, $invoice_num]);
        $activities[] = json_decode(json_encode($AUDIT), true);
        if (count($AUDIT) > 0) {
            $concepts[] = array(
                'name' => 'AUDITORIA DE INCAPACIDADES',
                'units' => count($AUDIT),
                'unitv' => $prices[Service::SERVICE_AUDIT_FAMISANAR],
                'totalv' => $prices[Service::SERVICE_AUDIT_FAMISANAR] * count($AUDIT),
            );
        }

        $total += count($AUDIT) * $prices[Service::SERVICE_AUDIT_FAMISANAR];

        $VALORACION = DB::select(BillReport::VALORATION_SQL_NEW, [$client->id, $invoice_num]);
        $activities[] = json_decode(json_encode($VALORACION), true);
        if (count($VALORACION) > 0) {
            $concepts[] = array(
                'name' => 'VALORACIÓN MÉDICA LABORAL',
                'units' => count($VALORACION),
                'unitv' => $prices[Service::SERVICE_VALORATION_FAMISANAR],
                'totalv' => $prices[Service::SERVICE_VALORATION_FAMISANAR] * count($VALORACION),
            );
        }

        $total += count($VALORACION) * $prices[Service::SERVICE_VALORATION_FAMISANAR];

        $RECOBRO = BillReport::recobros_new($client->id, $invoice_num);
        $activities[] = $RECOBRO;
        if (count($RECOBRO) > 0) {
            $concepts[] = array(
                'name' => 'ANALISIS Y ALISTAMIENTO DE FACTURAS - RECOBRO',
                'units' => count($RECOBRO),
                'unitv' => $prices[Service::SERVICE_RECOBRO_FAMISANAR],
                'totalv' => $prices[Service::SERVICE_RECOBRO_FAMISANAR] * count($RECOBRO),
            );
        }

        $total += count($RECOBRO) * $prices[Service::SERVICE_RECOBRO_FAMISANAR];

        $activities = collect($activities)->collapse();

        $formatter = new NumberFormatter('es', NumberFormatter::SPELLOUT);

        $invoice = Invoice::find($invoice_num);
        $pdf = PDF::loadView("extras.bill", [
            'client' => $client,
            'concepts' => $concepts,
            'total' => $total,
            'total_str' => $formatter->format($total),
            'invoice_num' => $invoice->invoice_number_orienta,
            'initial_date' => $invoice->init_date,
            'final_date' => $invoice->last_date,
        ]);

        $activities = collect($activities)->map(function ($item) {
            $item['FECHA_SERVICIO'] = \PHPExcel_Shared_Date::PHPToExcel(\Carbon\Carbon::parse($item['FECHA_SERVICIO'])->timestamp);

            return $item;
        });

        $fexcel = Excel::create("ANEXO", function ($excel) use ($activities) {

            $excel->sheet('HOJA 1', function ($sheet) use ($activities) {
                $sheet->setColumnFormat([
                    'F' => \PHPExcel_Style_NumberFormat::FORMAT_DATE_YYYYMMDD2,
                ]);
                $sheet->fromArray($activities);
                $sheet->freezeFirstRow();
                $sheet->setAutoFilter();
                $sheet->row(1, function ($row) {
                    $row->setBackground('#7EBE2C');
                    $row->setFontColor('#FFFFFF');
                    $row->setFontWeight('bold');
                });
                $sheet->setAutoSize(true);
            });
            $excel->setActiveSheetIndex(0);
        })->store('xlsx', false, true);

        $zipper = new \Chumper\Zipper\Zipper;

        $zipper->make(storage_path("ORIENTA INVOICE {$invoice_num}.zip"));

        $zipper->addString('ORIENTA INVOICE.pdf', $pdf->output());
        $zipper->add($fexcel['full']);

        $zipper->close();

        unlink($fexcel['full']);

        return response()->download(storage_path("ORIENTA INVOICE {$invoice_num}.zip"))->deleteFileAfterSend(true);
    }

    public function bill_invoice(Request $req, $cpath)
    {
        $invoice_num = $req->input('invoice_num');
        $client = Client::where('path', $cpath)->firstOrFail();

        $invoice_data = Invoice::find($invoice_num);
        $bill_num = $invoice_data->invoice_number;

        $all_concepts = $invoice_data->getConcepts();
        $activities = $all_concepts['activities'];
        $concepts = $all_concepts['concepts'];
        $total = $all_concepts['total'];

        $activities = collect($activities)->collapse();

        $formatter = new NumberFormatter('es', NumberFormatter::SPELLOUT);


        $pdf = PDF::loadView("extras.bill_new", [
            'client' => $client,
            'concepts' => $concepts,
            'total' => $total,
            'total_str' => $formatter->format($total),
            'invoice_num' => $invoice_data->id,
        ]);

        $activities = collect($activities)->map(function ($item) {
            $item['FECHA_SERVICIO'] = \PHPExcel_Shared_Date::PHPToExcel(\Carbon\Carbon::parse($item['FECHA_SERVICIO'])->timestamp);

            return $item;
        });

        $doctors_data = DB::select(BillReport::ATELPCL_DOCTOR_CONSOLIDATE_SQL_NEW, [$client->id, $invoice_num]);
        $doctors[] = json_decode(json_encode($doctors_data), true);
        $client_id = $client->id;

        if (count($doctors[0])) {
            $dexcel = Excel::create("PROF_ADSCRITO", function ($excel) use ($doctors, $client_id, $invoice_num) {

                foreach ($doctors[0] as $doctor) {

                    $doctors_data = DB::select(BillReport::ATELPCL_DOCTOR_SPECIFIC_SQL_NEW, [$client_id, $invoice_num, $doctor['FUNCIONARIO_ID']]);
                    $doctor_activities = json_decode(json_encode($doctors_data), true);

                    $doctor['FUNCIONARIO'] = substr($doctor['FUNCIONARIO'], 0, 29);

                    $excel->sheet($doctor['FUNCIONARIO'], function ($sheet) use ($doctor_activities) {

                        $sheet->fromArray($doctor_activities, null, 'A1', true);
                        $sheet->freezeFirstRow();
                        $sheet->setAutoFilter();

                        $sheet->row(1, function ($row) {
                            $row->setBackground('#7EBE2C');
                            $row->setFontColor('#FFFFFF');
                            $row->setFontWeight('bold');
                        });
                        $sheet->setCellValue('A1', 'NOMBRE SERVICIO');
                        $sheet->setCellValue('B1', 'ID SERVICIO');
                        $sheet->setCellValue('C1', 'FUNCIONARIO');
                        $sheet->setCellValue('D1', 'ID FUNCIONARIO');
                        $sheet->setCellValue('E1', 'ID FACTURA PROF ADSCRITO');
                        $sheet->setAutoSize(true);
                    });
                }
                $excel->setActiveSheetIndex(0);
            })->store('xlsx', false, true);
        }

        $eexcel = Excel::create("ANEXO", function ($excel) use ($activities) {

            $excel->sheet('HOJA 1', function ($sheet) use ($activities) {
                $sheet->setColumnFormat([
                    'F' => \PHPExcel_Style_NumberFormat::FORMAT_DATE_YYYYMMDD2,
                ]);
                $sheet->fromArray($activities);
                $sheet->freezeFirstRow();
                $sheet->setAutoFilter();

                $sheet->setCellValue('J1', 'PERIODO CORRESPONDIENTE');
                $sheet->setCellValue('K1', 'FECHA FACTURA REN');
                $sheet->setCellValue('L1', 'NUMERO DE FACTURA REN');
                $sheet->setCellValue('M1', 'FECHA RADICADO');
                $sheet->setCellValue('N1', 'FECHA FACTURA ORIENTA');
                $sheet->setCellValue('O1', 'NRO. FACTURA ORIENTA');
                $sheet->setCellValue('P1', 'FECHA DE RADICACIÓN');
                $sheet->setCellValue('Q1', 'GENERA COBRO PROF. ADSCRITO');
                $sheet->setCellValue('R1', 'CUENTA COBRO PROF. ADSCRITO');

                $last_row = count($activities) + 1;

                $sheet->row(1, function ($row) {
                    $row->setBackground('#7EBE2C');
                    $row->setFontColor('#FFFFFF');
                    $row->setFontWeight('bold');
                });
                //$sheet->setCellValue('J2:J'.$last_row, '');
                $sheet->setAutoSize(true);
                $sheet->setCellValue('I1', 'GLOSADO O FACTURADO');
            });
            $excel->setActiveSheetIndex(0);
        })->store('xlsx', false, true);


        $fexcel = Excel::create("FACTURACIÓN", function ($excel) use ($concepts, $total, $bill_num) {

            $excel->sheet('HOJA 1', function ($sheet) use ($concepts, $total, $bill_num) {

                $sheet->setColumnFormat([
                    'B' => '"$"#.##',
                    'D' => '"$"#.##',
                ]);
                $sheet->mergeCells('A2:A5');
                $sheet->mergeCells('B2:D3');
                $sheet->mergeCells('B4:D5');

                $sheet->setCellValue('B2', 'FACTURA NO. ' . ($bill_num ? $bill_num : '______'));
                $sheet->setCellValue('B4', 'DETALLE FACTURACIÓN POR SERVICIO');

                $objDrawing = new \PHPExcel_Worksheet_Drawing;
                $objDrawing->setPath(storage_path('app/client_logo/ren.jpg')); //your image path
                $objDrawing->setCoordinates('A2');
                $objDrawing->setWorksheet($sheet);

                $sheet->fromArray($concepts, null, 'A7', true);

                $sheet->row(7, function ($row) {
                    $row->setBackground('#7030a0');
                    $row->setFontColor('#FFFFFF');
                    $row->setFontWeight('bold');
                });

                $sheet->setCellValue('A7', 'Servicio');
                $sheet->setCellValue('B7', 'Valor Unitario');
                $sheet->setCellValue('C7', 'Nro. Casos a Facturar');
                $sheet->setCellValue('D7', 'Valor Total a Facturar');

                $last_row = count($concepts) + 8;
                $sheet->mergeCells('A' . $last_row . ':C' . $last_row);
                $sheet->row($last_row, function ($row) {
                    $row->setBackground('#b4c6e7');
                    $row->setFontColor('#0000000');
                    $row->setFontWeight('bold');
                });
                $sheet->setCellValue('A' . $last_row, 'TOTAL');
                $sheet->setCellValue('D' . $last_row, $total);

                $style = array(
                    'alignment' => array(
                        'horizontal' => \PHPExcel_Style_Alignment::HORIZONTAL_CENTER,
                        'vertical' => \PHPExcel_Style_Alignment::VERTICAL_CENTER,
                    )
                );
                $sheet->getStyle("A2:D5")->applyFromArray($style);
                $sheet->getStyle("A7:D7")->applyFromArray($style);
                $sheet->getStyle("C8:C" . $last_row)->applyFromArray($style);
                $sheet->getStyle("A" . $last_row . ":C" . $last_row)->applyFromArray($style);

                $sheet->row(2, function ($row) {
                    $row->setFontWeight('bold');
                });
                $sheet->row(4, function ($row) {
                    $row->setFontWeight('bold');
                });
                $sheet->setBorder('A2:D5', 'thin');
                $sheet->setBorder('A7:D' . $last_row, 'thin');

                $sheet->mergeCells('B' . ($last_row + 5) . ':D' . ($last_row + 5));
                $sheet->setCellValue('B' . ($last_row + 5), 'Juan Camilo Ricaurte Molina');
                $sheet->row(($last_row + 5), function ($row) {
                    $row->setFontWeight('bold');
                });

                $sheet->mergeCells('B' . ($last_row + 6) . ':D' . ($last_row + 6));
                $sheet->setCellValue('B' . ($last_row + 6), 'Dirección de Operaciones');
                $sheet->getStyle("B" . ($last_row + 5) . ":D" . ($last_row + 6))->applyFromArray($style);

                $sheet->cells("B" . ($last_row + 5) . ":D" . ($last_row + 5), function ($cells) {
                    $cells->setBorder('thin', 'none', 'none', 'none');
                });


                $sheet->setAutoSize(true);
            });
            $excel->setActiveSheetIndex(0);
        })->store('xlsx', false, true);


        $zipper = new \Chumper\Zipper\Zipper;

        $zipper->make(storage_path("INVOICE {$invoice_num}.zip"));

        //$zipper->addString('INVOICE.pdf', $pdf->output());
        $zipper->add($eexcel['full']);
        $zipper->add($fexcel['full']);

        if (count($doctors[0])) {
            $zipper->add($dexcel['full']);
        }

        $zipper->close();

        Storage::disk('s3')->putFileAs('docs', new File(storage_path("INVOICE {$invoice_num}.zip")), "INVOICE {$invoice_num}.zip");
        Storage::disk('s3')->putFileAs('docs', new File(($fexcel['full'])), "FACTURACIÓN INVOICE {$invoice_num}.xlsx");
        Storage::disk('s3')->putFileAs('docs', new File(($eexcel['full'])), "ANEXO INVOICE {$invoice_num}.xlsx");

        unlink($eexcel['full']);
        unlink($fexcel['full']);
        if (count($doctors[0])) {
            unlink($dexcel['full']);
        }

        return response()->download(storage_path("INVOICE {$invoice_num}.zip"))->deleteFileAfterSend(true);
    }

    public function generateExcelActivitiesInvoice(Request $req, $cpath)
    {
        $invoice_num = $req->input('invoice_num');
        $client = Client::where('path', $cpath)->firstOrFail();

        $invoice_data = Invoice::find($invoice_num);

        $activities = $invoice_data->getAllServices();
        $activities = collect($activities)->collapse();

        $eexcel = Excel::create("ANEXO", function ($excel) use ($activities) {

            $excel->sheet('HOJA 1', function ($sheet) use ($activities) {
                $sheet->setColumnFormat([
                    'F' => \PHPExcel_Style_NumberFormat::FORMAT_DATE_YYYYMMDD2,
                ]);
                $sheet->fromArray($activities);
                $sheet->freezeFirstRow();
                $sheet->setAutoFilter();

                $last_row = count($activities) + 1;

                $sheet->row(1, function ($row) {
                    $row->setBackground('#7EBE2C');
                    $row->setFontColor('#FFFFFF');
                    $row->setFontWeight('bold');
                });
                //$sheet->setCellValue('J2:J'.$last_row, '');
                $sheet->setAutoSize(true);
            });
            $excel->setActiveSheetIndex(0);
        })->store('xlsx', false, true);

        $zipper = new \Chumper\Zipper\Zipper;

        $zipper->make(storage_path("LISTA SERVICIOS {$invoice_num}.zip"));

        $zipper->add($eexcel['full']);

        $zipper->close();

        unlink($eexcel['full']);

        return response()->download(storage_path("LISTA SERVICIOS {$invoice_num}.zip"))->deleteFileAfterSend(true);
    }

    public function sendEmailInvoice(Request $req, $cpath)
    {
        $invoice_num = $req->input('invoice_num');
        $client = Client::where('path', $cpath)->firstOrFail();

        $invoice_data = Invoice::find($invoice_num);

        $this->bill_invoice($req, $cpath);

        $emails_billing = ['<EMAIL>'];
        $emails = ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'];

        try {

            config(['mail' => ['driver' => 'mail', 'host' => 'correo.renconsultores.com.co', 'port' => '25', 'from' => ['address' => '<EMAIL>', 'name' => 'DEPARTAMENTO MEDICINAL LABORAL - LA EQUIDAD SEGUROS'], 'encryption' => 'ssl']]);
            $subject = "Facturación {$invoice_data->init_date} - {$invoice_data->last_date}";

            $files_billing[] = [
                'type' => 'xlsx',
                'path' => "docs/FACTURACIÓN INVOICE {$invoice_num}.xlsx",
                'name' => "FACTURACIÓN INVOICE {$invoice_num}.xlsx",
            ];
            $files_billing[] = [
                'type' => 'xlsx',
                'path' => "docs/ANEXO INVOICE {$invoice_num}.xlsx",
                'name' => "ANEXO INVOICE {$invoice_num}.xlsx",
            ];
            $files[] = [
                'type' => 'pdf',
                'path' => "docs/INVOICE {$invoice_num}.zip",
                'name' => "INVOICE {$invoice_num}.zip",
            ];

            $body = "Remito Información de facturación del cliente {$client->name} en el periodo {$invoice_data->init_date} - {$invoice_data->last_date}
                                
                                Quedo atenta a cualquier inquietud.";
            $mailSent = new SendDocumentDataBase(
                implode(',', $emails),
                $subject,
                "<EMAIL>",
                "Programa de salud - Universidad de Antioquia Convenio Ren Consultores",
                [
                    "text" => $body,
                    "sender" => 'Convenio Programa de Salud Universidad de Antioquia'
                ],
                "<EMAIL>",
                $files_billing,
                "send_document_db",
                $client,
                $req->getHost(),
                null,
                null,
                null
            );
            $mailSent->sendMail();
            $mailSent = new SendDocumentDataBase(
                implode(',', $emails_billing),
                $subject,
                "<EMAIL>",
                "Programa de salud - Universidad de Antioquia Convenio Ren Consultores",
                [
                    "text" => $body,
                    "sender" => 'Convenio Programa de Salud Universidad de Antioquia'
                ],
                "<EMAIL>",
                $files,
                "send_document_db",
                $client,
                $req->getHost(),
                null,
                null,
                null
            );
            $mailSent->sendMail();

            if (!$invoice_data->send_emails) {
                $invoice_data->send_emails = 1;
            } else {
                $invoice_data->send_emails = $invoice_data->send_emails + 1;
            }
            $invoice_data->save();
        } catch (Exception $e) {
            Log::error($e->getMessage());
        }

        return redirect('facturacion');
    }

    public function bill_new(Request $req, $cpath, $invoice_num)
    {
        $prices = array(
            Service::SERVICE_AT_FAMISANAR => 1850,
            Service::SERVICE_EL_FAMISANAR => 4400,
            Service::SERVICE_EL_COOMEVA => 1409,
            Service::SERVICE_PCL_MAPFRE => 8750,
            Service::SERVICE_PCL_FAMISANAR => 4400,
            Service::SERVICE_CONTROVERSY_FAMISANAR => 0,
            Service::SERVICE_REHABILITATION_FAMISANAR => 1950,
            Service::SERVICE_RECOMMENDATIONS_FAMISANAR => 2060,
            Service::SERVICE_DISABILITY_FAMISANAR => 2200,
            Service::SERVICE_AUDIT_FAMISANAR => 149,
            Service::SERVICE_RECOBRO_FAMISANAR => 322,
            Service::SERVICE_GLOSA_FAMISANAR => 0,
            Service::SERVICE_INFORMATIVO_FAMISANAR => 0,
            Service::SERVICE_VALORATION_FAMISANAR => 1750,
        );
        $total = 0;
        $client = Client::where('path', $cpath)->firstOrFail();
        $activities = array();
        $concepts = array();

        $EL = DB::select(BillReport::EL_SQL_NEW, [$client->id, $invoice_num]);
        $activities[] = json_decode(json_encode($EL), true);
        if (count($EL) > 0) {
            $concepts[] = array(
                'name' => 'CALIFICACION DE ORIGEN EL',
                'units' => count($EL),
                'unitv' => $prices[Service::SERVICE_EL_FAMISANAR],
                'totalv' => $prices[Service::SERVICE_EL_FAMISANAR] * count($EL),
            );
        }

        $total += count($EL) * $prices[Service::SERVICE_EL_FAMISANAR];

        $AT = DB::select(BillReport::AT_SQL_NEW, [$client->id, $invoice_num]);
        $activities[] = json_decode(json_encode($AT), true);
        if (count($AT) > 0) {
            $concepts[] = array(
                'name' => 'CALIFICACION DE ORIGEN AT',
                'units' => count($AT),
                'unitv' => $prices[Service::SERVICE_AT_FAMISANAR],
                'totalv' => $prices[Service::SERVICE_AT_FAMISANAR] * count($AT),
            );
        }

        $total += count($AT) * $prices[Service::SERVICE_AT_FAMISANAR];

        $PCL = DB::select(BillReport::PCL_SQL_NEW, [$client->id, $invoice_num]);
        $activities[] = json_decode(json_encode($PCL), true);
        if (count($PCL) > 0) {
            $concepts[] = array(
                'name' => 'PCL',
                'units' => count($PCL),
                'unitv' => $prices[Service::SERVICE_PCL_FAMISANAR],
                'totalv' => $prices[Service::SERVICE_PCL_FAMISANAR] * count($PCL),
            );
        }

        $total += count($PCL) * $prices[Service::SERVICE_PCL_FAMISANAR];

        $REHABILITATION = DB::select(BillReport::REHABILITATION_SQL_NEW, [$client->id, $invoice_num]);
        $activities[] = json_decode(json_encode($REHABILITATION), true);
        if (count($REHABILITATION) > 0) {
            $concepts[] = array(
                'name' => 'CONCEPTO DE REHABILITACION',
                'units' => count($REHABILITATION),
                'unitv' => $prices[Service::SERVICE_REHABILITATION_FAMISANAR],
                'totalv' => $prices[Service::SERVICE_REHABILITATION_FAMISANAR] * count($REHABILITATION),
            );
        }

        $total += count($REHABILITATION) * $prices[Service::SERVICE_REHABILITATION_FAMISANAR];

        $RECOMMENDATION = DB::select(BillReport::RECOMMENDATION_SQL_NEW, [$client->id, $invoice_num]);
        $activities[] = json_decode(json_encode($RECOMMENDATION), true);
        if (count($RECOMMENDATION) > 0) {
            $concepts[] = array(
                'name' => 'RECOMENDACIONES LABORALES',
                'units' => count($RECOMMENDATION),
                'unitv' => $prices[Service::SERVICE_RECOMMENDATIONS_FAMISANAR],
                'totalv' => $prices[Service::SERVICE_RECOMMENDATIONS_FAMISANAR] * count($RECOMMENDATION),
            );
        }

        $total += count($RECOMMENDATION) * $prices[Service::SERVICE_RECOMMENDATIONS_FAMISANAR];

        $DISABILITY = DB::select(BillReport::DISABILITY_SQL_NEW, [$client->id, $invoice_num]);
        $activities[] = json_decode(json_encode($DISABILITY), true);
        if (count($DISABILITY) > 0) {
            $concepts[] = array(
                'name' => 'CERTIFICADO DE DISCAPACIDAD',
                'units' => count($DISABILITY),
                'unitv' => $prices[Service::SERVICE_DISABILITY_FAMISANAR],
                'totalv' => $prices[Service::SERVICE_DISABILITY_FAMISANAR] * count($DISABILITY),
            );
        }

        $total += count($DISABILITY) * $prices[Service::SERVICE_DISABILITY_FAMISANAR];

        $AUDIT = DB::select(BillReport::AUDIT_SQL_NEW, [$client->id, $invoice_num]);
        $activities[] = json_decode(json_encode($AUDIT), true);
        if (count($AUDIT) > 0) {
            $concepts[] = array(
                'name' => 'AUDITORIA DE INCAPACIDADES',
                'units' => count($AUDIT),
                'unitv' => $prices[Service::SERVICE_AUDIT_FAMISANAR],
                'totalv' => $prices[Service::SERVICE_AUDIT_FAMISANAR] * count($AUDIT),
            );
        }

        $total += count($AUDIT) * $prices[Service::SERVICE_AUDIT_FAMISANAR];

        $RECOBRO = BillReport::recobros_new($client->id, $invoice_num);
        $activities[] = $RECOBRO;
        if (count($RECOBRO) > 0) {
            $concepts[] = array(
                'name' => 'ANALISIS Y ALISTAMIENTO DE FACTURAS - RECOBRO',
                'units' => count($RECOBRO),
                'unitv' => $prices[Service::SERVICE_RECOBRO_FAMISANAR],
                'totalv' => $prices[Service::SERVICE_RECOBRO_FAMISANAR] * count($RECOBRO),
            );
        }

        $total += count($RECOBRO) * $prices[Service::SERVICE_RECOBRO_FAMISANAR];

        $activities = collect($activities)->collapse();

        $formatter = new NumberFormatter('es', NumberFormatter::SPELLOUT);

        $invoice_data = Invoice::find($invoice_num);

        $pdf = PDF::loadView("extras.bill_new", [
            'client' => $client,
            'concepts' => $concepts,
            'total' => $total,
            'total_str' => $formatter->format($total),
            'invoice_num' => $invoice_data->invoice_number,
        ]);

        $activities = collect($activities)->map(function ($item) {
            $item['FECHA_SERVICIO'] = \PHPExcel_Shared_Date::PHPToExcel(\Carbon\Carbon::parse($item['FECHA_SERVICIO'])->timestamp);

            return $item;
        });

        $fexcel = Excel::create("ANEXO", function ($excel) use ($activities) {

            $excel->sheet('HOJA 1', function ($sheet) use ($activities) {
                $sheet->setColumnFormat([
                    'F' => \PHPExcel_Style_NumberFormat::FORMAT_DATE_YYYYMMDD2,
                ]);
                $sheet->fromArray($activities);
                $sheet->freezeFirstRow();
                $sheet->setAutoFilter();
                $sheet->row(1, function ($row) {
                    $row->setBackground('#7EBE2C');
                    $row->setFontColor('#FFFFFF');
                    $row->setFontWeight('bold');
                });
                $sheet->setAutoSize(true);
            });
            $excel->setActiveSheetIndex(0);
        })->store('xlsx', false, true);

        $zipper = new \Chumper\Zipper\Zipper;

        $zipper->make(storage_path("INVOICE {$invoice_num}.zip"));

        $zipper->addString('INVOICE.pdf', $pdf->output());
        $zipper->add($fexcel['full']);

        $zipper->close();

        unlink($fexcel['full']);

        return response()->download(storage_path("INVOICE {$invoice_num}.zip"))->deleteFileAfterSend(true);
    }

    public function bill_doc_new(Request $req, $cpath, $invoice_num)
    {
        $prices = array(
            Service::SERVICE_AT_FAMISANAR => 1850,
            Service::SERVICE_EL_FAMISANAR => 4400,
            Service::SERVICE_EL_COOMEVA => 1409,
            Service::SERVICE_PCL_MAPFRE => 8750,
            Service::SERVICE_PCL_FAMISANAR => 4400,
            Service::SERVICE_CONTROVERSY_FAMISANAR => 0,
            Service::SERVICE_REHABILITATION_FAMISANAR => 1950,
            Service::SERVICE_RECOMMENDATIONS_FAMISANAR => 2060,
            Service::SERVICE_DISABILITY_FAMISANAR => 2200,
            Service::SERVICE_AUDIT_FAMISANAR => 149,
            Service::SERVICE_RECOBRO_FAMISANAR => 322,
            Service::SERVICE_GLOSA_FAMISANAR => 0,
            Service::SERVICE_INFORMATIVO_FAMISANAR => 0,
            Service::SERVICE_VALORATION_FAMISANAR => 1750,
        );
        $total = 0;
        $client = Client::where('path', $cpath)->firstOrFail();
        $activities = array();
        $concepts = array();

        $EL = DB::select(BillReport::EL_SQL_NEW, [$client->id, $invoice_num]);
        $activities[] = json_decode(json_encode($EL), true);
        if (count($EL) > 0) {
            $concepts[] = array(
                'name' => 'CALIFICACION DE ORIGEN EL',
                'units' => count($EL),
                'unitv' => $prices[Service::SERVICE_EL_FAMISANAR],
                'totalv' => $prices[Service::SERVICE_EL_FAMISANAR] * count($EL),
            );
        }

        $total += count($EL) * $prices[Service::SERVICE_EL_FAMISANAR];

        $AT = DB::select(BillReport::AT_SQL_NEW, [$client->id, $invoice_num]);
        $activities[] = json_decode(json_encode($AT), true);
        if (count($AT) > 0) {
            $concepts[] = array(
                'name' => 'CALIFICACION DE ORIGEN AT',
                'units' => count($AT),
                'unitv' => $prices[Service::SERVICE_AT_FAMISANAR],
                'totalv' => $prices[Service::SERVICE_AT_FAMISANAR] * count($AT),
            );
        }

        $total += count($AT) * $prices[Service::SERVICE_AT_FAMISANAR];

        $PCL = DB::select(BillReport::PCL_SQL_NEW, [$client->id, $invoice_num]);
        $activities[] = json_decode(json_encode($PCL), true);
        if (count($PCL) > 0) {
            $concepts[] = array(
                'name' => 'PCL',
                'units' => count($PCL),
                'unitv' => $prices[Service::SERVICE_PCL_FAMISANAR],
                'totalv' => $prices[Service::SERVICE_PCL_FAMISANAR] * count($PCL),
            );
        }

        $total += count($PCL) * $prices[Service::SERVICE_PCL_FAMISANAR];

        $activities = collect($activities)->collapse();

        $formatter = new NumberFormatter('es', NumberFormatter::SPELLOUT);

        $invoice_data = Docinvoice::find($invoice_num);

        $pdf = PDF::loadView("extras.bill_new", [
            'client' => $client,
            'concepts' => $concepts,
            'total' => $total,
            'total_str' => $formatter->format($total),
            'invoice_num' => $invoice_data->invoice_number,
        ]);

        $activities = collect($activities)->map(function ($item) {
            $item['FECHA_SERVICIO'] = \PHPExcel_Shared_Date::PHPToExcel(\Carbon\Carbon::parse($item['FECHA_SERVICIO'])->timestamp);

            return $item;
        });

        $fexcel = Excel::create("ANEXO", function ($excel) use ($activities) {

            $excel->sheet('HOJA 1', function ($sheet) use ($activities) {
                $sheet->setColumnFormat([
                    'F' => \PHPExcel_Style_NumberFormat::FORMAT_DATE_YYYYMMDD2,
                ]);
                $sheet->fromArray($activities);
                $sheet->freezeFirstRow();
                $sheet->setAutoFilter();
                $sheet->row(1, function ($row) {
                    $row->setBackground('#7EBE2C');
                    $row->setFontColor('#FFFFFF');
                    $row->setFontWeight('bold');
                });
                $sheet->setAutoSize(true);
            });
            $excel->setActiveSheetIndex(0);
        })->store('xlsx', false, true);

        $zipper = new \Chumper\Zipper\Zipper;

        $zipper->make(storage_path("INVOICE DOC {$invoice_num}.zip"));

        $zipper->addString('INVOICE_DOC.pdf', $pdf->output());
        $zipper->add($fexcel['full']);

        $zipper->close();

        unlink($fexcel['full']);

        return response()->download(storage_path("INVOICE DOC {$invoice_num}.zip"))->deleteFileAfterSend(true);
    }

    public function bill_new2(Request $req, $cpath, $invoice_num)
    {
        $prices = array(
            Service::SERVICE_AT_FAMISANAR => 1850,
            Service::SERVICE_EL_FAMISANAR => 4400,
            Service::SERVICE_EL_COOMEVA => 1409,
            Service::SERVICE_PCL_MAPFRE => 8750,
            Service::SERVICE_PCL_FAMISANAR => 4400,
            Service::SERVICE_CONTROVERSY_FAMISANAR => 0,
            Service::SERVICE_REHABILITATION_FAMISANAR => 1950,
            Service::SERVICE_RECOMMENDATIONS_FAMISANAR => 2060,
            Service::SERVICE_DISABILITY_FAMISANAR => 2200,
            Service::SERVICE_AUDIT_FAMISANAR => 149,
            Service::SERVICE_RECOBRO_FAMISANAR => 322,
            Service::SERVICE_GLOSA_FAMISANAR => 0,
            Service::SERVICE_INFORMATIVO_FAMISANAR => 0,
            Service::SERVICE_VALORATION_FAMISANAR => 1750,
        );
        $total = 0;
        $client = Client::where('path', $cpath)->firstOrFail();
        $activities = array();
        $concepts = array();

        $EL = DB::select(BillReport::EL_SQL_NEW, [$client->id, $invoice_num]);
        $activities[] = json_decode(json_encode($EL), true);
        if (count($EL) > 0) {
            $concepts[] = array(
                'name' => 'CALIFICACION DE ORIGEN EL',
                'units' => count($EL),
                'unitv' => $prices[Service::SERVICE_EL_FAMISANAR],
                'totalv' => $prices[Service::SERVICE_EL_FAMISANAR] * count($EL),
            );
        }

        $total += count($EL) * $prices[Service::SERVICE_EL_FAMISANAR];

        $AT = DB::select(BillReport::AT_SQL_NEW, [$client->id, $invoice_num]);
        $activities[] = json_decode(json_encode($AT), true);
        if (count($AT) > 0) {
            $concepts[] = array(
                'name' => 'CALIFICACION DE ORIGEN AT',
                'units' => count($AT),
                'unitv' => $prices[Service::SERVICE_AT_FAMISANAR],
                'totalv' => $prices[Service::SERVICE_AT_FAMISANAR] * count($AT),
            );
        }

        $total += count($AT) * $prices[Service::SERVICE_AT_FAMISANAR];

        $PCL = DB::select(BillReport::PCL_SQL_NEW, [$client->id, $invoice_num]);
        $activities[] = json_decode(json_encode($PCL), true);
        if (count($PCL) > 0) {
            $concepts[] = array(
                'name' => 'PCL',
                'units' => count($PCL),
                'unitv' => $prices[Service::SERVICE_PCL_FAMISANAR],
                'totalv' => $prices[Service::SERVICE_PCL_FAMISANAR] * count($PCL),
            );
        }

        $total += count($PCL) * $prices[Service::SERVICE_PCL_FAMISANAR];

        $REHABILITATION = DB::select(BillReport::REHABILITATION_SQL_NEW, [$client->id, $invoice_num]);
        $activities[] = json_decode(json_encode($REHABILITATION), true);
        if (count($REHABILITATION) > 0) {
            $concepts[] = array(
                'name' => 'CONCEPTO DE REHABILITACION',
                'units' => count($REHABILITATION),
                'unitv' => $prices[Service::SERVICE_REHABILITATION_FAMISANAR],
                'totalv' => $prices[Service::SERVICE_REHABILITATION_FAMISANAR] * count($REHABILITATION),
            );
        }

        $total += count($REHABILITATION) * $prices[Service::SERVICE_REHABILITATION_FAMISANAR];

        $RECOMMENDATION = DB::select(BillReport::RECOMMENDATION_SQL_NEW, [$client->id, $invoice_num]);
        $activities[] = json_decode(json_encode($RECOMMENDATION), true);
        if (count($RECOMMENDATION) > 0) {
            $concepts[] = array(
                'name' => 'RECOMENDACIONES LABORALES',
                'units' => count($RECOMMENDATION),
                'unitv' => $prices[Service::SERVICE_RECOMMENDATIONS_FAMISANAR],
                'totalv' => $prices[Service::SERVICE_RECOMMENDATIONS_FAMISANAR] * count($RECOMMENDATION),
            );
        }

        $total += count($RECOMMENDATION) * $prices[Service::SERVICE_RECOMMENDATIONS_FAMISANAR];

        $DISABILITY = DB::select(BillReport::DISABILITY_SQL_NEW, [$client->id, $invoice_num]);
        $activities[] = json_decode(json_encode($DISABILITY), true);
        if (count($DISABILITY) > 0) {
            $concepts[] = array(
                'name' => 'CERTIFICADO DE DISCAPACIDAD',
                'units' => count($DISABILITY),
                'unitv' => $prices[Service::SERVICE_DISABILITY_FAMISANAR],
                'totalv' => $prices[Service::SERVICE_DISABILITY_FAMISANAR] * count($DISABILITY),
            );
        }

        $total += count($DISABILITY) * $prices[Service::SERVICE_DISABILITY_FAMISANAR];

        $AUDIT = DB::select(BillReport::AUDIT_SQL_NEW, [$client->id, $invoice_num]);
        $activities[] = json_decode(json_encode($AUDIT), true);
        if (count($AUDIT) > 0) {
            $concepts[] = array(
                'name' => 'AUDITORIA DE INCAPACIDADES',
                'units' => count($AUDIT),
                'unitv' => $prices[Service::SERVICE_AUDIT_FAMISANAR],
                'totalv' => $prices[Service::SERVICE_AUDIT_FAMISANAR] * count($AUDIT),
            );
        }

        $total += count($AUDIT) * $prices[Service::SERVICE_AUDIT_FAMISANAR];

        $RECOBRO = BillReport::recobros_new($client->id, $invoice_num);
        $activities[] = $RECOBRO;
        if (count($RECOBRO) > 0) {
            $concepts[] = array(
                'name' => 'ANALISIS Y ALISTAMIENTO DE FACTURAS - RECOBRO',
                'units' => count($RECOBRO),
                'unitv' => $prices[Service::SERVICE_RECOBRO_FAMISANAR],
                'totalv' => $prices[Service::SERVICE_RECOBRO_FAMISANAR] * count($RECOBRO),
            );
        }

        $total += count($RECOBRO) * $prices[Service::SERVICE_RECOBRO_FAMISANAR];

        $activities = collect($activities)->collapse();

        $formatter = new NumberFormatter('es', NumberFormatter::SPELLOUT);

        $pdf = PDF::loadView("extras.bill2", [
            'client' => $client,
            'concepts' => $concepts,
            'total' => $total,
            'total_str' => $formatter->format($total),
            'invoice_num' => $invoice_num,
            'initial_date' => date('Y-m-d'),
            'final_date' => date('Y-m-d'),
        ]);

        $activities = collect($activities)->map(function ($item) {
            $item['FECHA_SERVICIO'] = \PHPExcel_Shared_Date::PHPToExcel(\Carbon\Carbon::parse($item['FECHA_SERVICIO'])->timestamp);

            return $item;
        });

        $fexcel = Excel::create("ANEXO", function ($excel) use ($activities) {

            $excel->sheet('HOJA 1', function ($sheet) use ($activities) {
                $sheet->setColumnFormat([
                    'F' => \PHPExcel_Style_NumberFormat::FORMAT_DATE_YYYYMMDD2,
                ]);
                $sheet->fromArray($activities);
                $sheet->freezeFirstRow();
                $sheet->setAutoFilter();
                $sheet->row(1, function ($row) {
                    $row->setBackground('#7EBE2C');
                    $row->setFontColor('#FFFFFF');
                    $row->setFontWeight('bold');
                });
                $sheet->setAutoSize(true);
            });
            $excel->setActiveSheetIndex(0);
        })->store('xlsx', false, true);

        $zipper = new \Chumper\Zipper\Zipper;

        $zipper->make(storage_path("PRE INVOICE {$invoice_num}.zip"));

        $zipper->addString('PRE-INVOICE.pdf', $pdf->output());
        $zipper->add($fexcel['full']);

        $zipper->close();

        unlink($fexcel['full']);

        return response()->download(storage_path("PRE INVOICE {$invoice_num}.zip"))->deleteFileAfterSend(true);
    }

    public function bill_doc_new2(Request $req, $cpath, $invoice_num)
    {
        $prices = array(
            Service::SERVICE_AT_FAMISANAR => 1850,
            Service::SERVICE_EL_FAMISANAR => 4400,
            Service::SERVICE_EL_COOMEVA => 1409,
            Service::SERVICE_PCL_MAPFRE => 8750,
            Service::SERVICE_PCL_FAMISANAR => 4400,
            Service::SERVICE_CONTROVERSY_FAMISANAR => 0,
            Service::SERVICE_REHABILITATION_FAMISANAR => 1950,
            Service::SERVICE_RECOMMENDATIONS_FAMISANAR => 2060,
            Service::SERVICE_DISABILITY_FAMISANAR => 2200,
            Service::SERVICE_AUDIT_FAMISANAR => 149,
            Service::SERVICE_RECOBRO_FAMISANAR => 322,
            Service::SERVICE_GLOSA_FAMISANAR => 0,
            Service::SERVICE_INFORMATIVO_FAMISANAR => 0,
            Service::SERVICE_VALORATION_FAMISANAR => 1750,
        );
        $total = 0;
        $client = Client::where('path', $cpath)->firstOrFail();
        $activities = array();
        $concepts = array();

        $EL = DB::select(BillReport::EL_SQL_NEW, [$client->id, $invoice_num]);
        $activities[] = json_decode(json_encode($EL), true);
        if (count($EL) > 0) {
            $concepts[] = array(
                'name' => 'CALIFICACION DE ORIGEN EL',
                'units' => count($EL),
                'unitv' => $prices[Service::SERVICE_EL_FAMISANAR],
                'totalv' => $prices[Service::SERVICE_EL_FAMISANAR] * count($EL),
            );
        }

        $total += count($EL) * $prices[Service::SERVICE_EL_FAMISANAR];

        $AT = DB::select(BillReport::AT_SQL_NEW, [$client->id, $invoice_num]);
        $activities[] = json_decode(json_encode($AT), true);
        if (count($AT) > 0) {
            $concepts[] = array(
                'name' => 'CALIFICACION DE ORIGEN AT',
                'units' => count($AT),
                'unitv' => $prices[Service::SERVICE_AT_FAMISANAR],
                'totalv' => $prices[Service::SERVICE_AT_FAMISANAR] * count($AT),
            );
        }

        $total += count($AT) * $prices[Service::SERVICE_AT_FAMISANAR];

        $PCL = DB::select(BillReport::PCL_SQL_NEW, [$client->id, $invoice_num]);
        $activities[] = json_decode(json_encode($PCL), true);
        if (count($PCL) > 0) {
            $concepts[] = array(
                'name' => 'PCL',
                'units' => count($PCL),
                'unitv' => $prices[Service::SERVICE_PCL_FAMISANAR],
                'totalv' => $prices[Service::SERVICE_PCL_FAMISANAR] * count($PCL),
            );
        }

        $total += count($PCL) * $prices[Service::SERVICE_PCL_FAMISANAR];

        $activities = collect($activities)->collapse();

        $formatter = new NumberFormatter('es', NumberFormatter::SPELLOUT);

        $pdf = PDF::loadView("extras.bill2", [
            'client' => $client,
            'concepts' => $concepts,
            'total' => $total,
            'total_str' => $formatter->format($total),
            'invoice_num' => $invoice_num,
            'initial_date' => date('Y-m-d'),
            'final_date' => date('Y-m-d'),
        ]);

        $activities = collect($activities)->map(function ($item) {
            $item['FECHA_SERVICIO'] = \PHPExcel_Shared_Date::PHPToExcel(\Carbon\Carbon::parse($item['FECHA_SERVICIO'])->timestamp);

            return $item;
        });

        $fexcel = Excel::create("ANEXO", function ($excel) use ($activities) {

            $excel->sheet('HOJA 1', function ($sheet) use ($activities) {
                $sheet->setColumnFormat([
                    'F' => \PHPExcel_Style_NumberFormat::FORMAT_DATE_YYYYMMDD2,
                ]);
                $sheet->fromArray($activities);
                $sheet->freezeFirstRow();
                $sheet->setAutoFilter();
                $sheet->row(1, function ($row) {
                    $row->setBackground('#7EBE2C');
                    $row->setFontColor('#FFFFFF');
                    $row->setFontWeight('bold');
                });
                $sheet->setAutoSize(true);
            });
            $excel->setActiveSheetIndex(0);
        })->store('xlsx', false, true);

        $zipper = new \Chumper\Zipper\Zipper;

        $zipper->make(storage_path("PRE INVOICE {$invoice_num}.zip"));

        $zipper->addString('PRE-INVOICE.pdf', $pdf->output());
        $zipper->add($fexcel['full']);

        $zipper->close();

        unlink($fexcel['full']);

        return response()->download(storage_path("PRE INVOICE {$invoice_num}.zip"))->deleteFileAfterSend(true);
    }


    public function bill_api(Request $req, $cpath, $number)
    {
        $prices = array(
            Service::SERVICE_AT_FAMISANAR => 1850,
            Service::SERVICE_EL_FAMISANAR => 4400,
            Service::SERVICE_EL_COOMEVA => 1409,
            Service::SERVICE_PCL_MAPFRE => 8750,
            Service::SERVICE_PCL_FAMISANAR => 4400,
            Service::SERVICE_CONTROVERSY_FAMISANAR => 0,
            Service::SERVICE_REHABILITATION_FAMISANAR => 1950,
            Service::SERVICE_RECOMMENDATIONS_FAMISANAR => 2060,
            Service::SERVICE_DISABILITY_FAMISANAR => 2200,
            Service::SERVICE_AUDIT_FAMISANAR => 149,
            Service::SERVICE_RECOBRO_FAMISANAR => 322,
            Service::SERVICE_GLOSA_FAMISANAR => 0,
            Service::SERVICE_INFORMATIVO_FAMISANAR => 0,
            Service::SERVICE_VALORATION_FAMISANAR => 1750,
        );
        $invoice = Invoice::find($number);
        $client = Client::where('id', $invoice->client_id)->firstOrFail();
        $activities = array();
        $concepts = array();

        $EL = DB::select(BillReport::EL_SQL_NEW, [$client->id, $number]);
        $activities[] = json_decode(json_encode($EL), true);
        if (count($EL) > 0) {
            $concepts[] = array(
                'name' => 'CALIFICACION DE ORIGEN EL',
                'units' => count($EL),
                'unitv' => $prices[Service::SERVICE_EL_FAMISANAR],
                'totalv' => $prices[Service::SERVICE_EL_FAMISANAR] * count($EL),
            );
        }

        $AT = DB::select(BillReport::AT_SQL_NEW, [$client->id, $number]);
        $activities[] = json_decode(json_encode($AT), true);
        if (count($AT) > 0) {
            $concepts[] = array(
                'name' => 'CALIFICACION DE ORIGEN AT',
                'units' => count($AT),
                'unitv' => $prices[Service::SERVICE_AT_FAMISANAR],
                'totalv' => $prices[Service::SERVICE_AT_FAMISANAR] * count($AT),
            );
        }

        $PCL = DB::select(BillReport::PCL_SQL_NEW, [$client->id, $number]);
        $activities[] = json_decode(json_encode($PCL), true);
        if (count($PCL) > 0) {
            $concepts[] = array(
                'name' => 'PCL',
                'units' => count($PCL),
                'unitv' => $prices[Service::SERVICE_PCL_FAMISANAR],
                'totalv' => $prices[Service::SERVICE_PCL_FAMISANAR] * count($PCL),
            );
        }

        $REHABILITATION = DB::select(BillReport::REHABILITATION_SQL_NEW, [$client->id, $number]);
        $activities[] = json_decode(json_encode($REHABILITATION), true);
        if (count($REHABILITATION) > 0) {
            $concepts[] = array(
                'name' => 'CONCEPTO DE REHABILITACION',
                'units' => count($REHABILITATION),
                'unitv' => $prices[Service::SERVICE_REHABILITATION_FAMISANAR],
                'totalv' => $prices[Service::SERVICE_REHABILITATION_FAMISANAR] * count($REHABILITATION),
            );
        }

        $RECOMMENDATION = DB::select(BillReport::RECOMMENDATION_SQL_NEW, [$client->id, $number]);
        $activities[] = json_decode(json_encode($RECOMMENDATION), true);
        if (count($RECOMMENDATION) > 0) {
            $concepts[] = array(
                'name' => 'RECOMENDACIONES LABORALES',
                'units' => count($RECOMMENDATION),
                'unitv' => $prices[Service::SERVICE_RECOMMENDATIONS_FAMISANAR],
                'totalv' => $prices[Service::SERVICE_RECOMMENDATIONS_FAMISANAR] * count($RECOMMENDATION),
            );
        }

        $DISABILITY = DB::select(BillReport::DISABILITY_SQL_NEW, [$client->id, $number]);
        $activities[] = json_decode(json_encode($DISABILITY), true);
        if (count($DISABILITY) > 0) {
            $concepts[] = array(
                'name' => 'CERTIFICADO DE DISCAPACIDAD',
                'units' => count($DISABILITY),
                'unitv' => $prices[Service::SERVICE_DISABILITY_FAMISANAR],
                'totalv' => $prices[Service::SERVICE_DISABILITY_FAMISANAR] * count($DISABILITY),
            );
        }

        $AUDIT = DB::select(BillReport::AUDIT_SQL_NEW, [$client->id, $number]);
        $activities[] = json_decode(json_encode($AUDIT), true);
        if (count($AUDIT) > 0) {
            $concepts[] = array(
                'name' => 'AUDITORIA DE INCAPACIDADES',
                'units' => count($AUDIT),
                'unitv' => $prices[Service::SERVICE_AUDIT_FAMISANAR],
                'totalv' => $prices[Service::SERVICE_AUDIT_FAMISANAR] * count($AUDIT),
            );
        }

        $RECOBRO = BillReport::recobros_new($client->id, $number);
        $activities[] = $RECOBRO;
        if (count($RECOBRO) > 0) {
            $concepts[] = array(
                'name' => 'ANALISIS Y ALISTAMIENTO DE FACTURAS - RECOBRO',
                'units' => count($RECOBRO),
                'unitv' => $prices[Service::SERVICE_RECOBRO_FAMISANAR],
                'totalv' => $prices[Service::SERVICE_RECOBRO_FAMISANAR] * count($RECOBRO),
            );
        }

        $table = '';
        foreach ($concepts as $c) {
            $table .= '<tr style="text-align: justify; font-weight: 500;"><td>' . $c['name'] . '</td><td  style="text-align: center;">' . $c['units'] . '</td></tr>';
        }

        $response = "<table>
            <thead>
                <tr>
                    <th>DETALLE</th>
                    <th>CANTIDAD</th>
                </tr>
            </thead>
            <tbody>
                $table
            </tbody>
        </table>";

        return $response;
    }


    public function bill_doc_api(Request $req, $cpath, $number)
    {
        $prices = array(
            Service::SERVICE_AT_FAMISANAR => 1850,
            Service::SERVICE_EL_FAMISANAR => 4400,
            Service::SERVICE_EL_COOMEVA => 1409,
            Service::SERVICE_PCL_MAPFRE => 8750,
            Service::SERVICE_PCL_FAMISANAR => 4400,
            Service::SERVICE_CONTROVERSY_FAMISANAR => 0,
            Service::SERVICE_REHABILITATION_FAMISANAR => 1950,
            Service::SERVICE_RECOMMENDATIONS_FAMISANAR => 2060,
            Service::SERVICE_DISABILITY_FAMISANAR => 2200,
            Service::SERVICE_AUDIT_FAMISANAR => 149,
            Service::SERVICE_RECOBRO_FAMISANAR => 322,
            Service::SERVICE_GLOSA_FAMISANAR => 0,
            Service::SERVICE_INFORMATIVO_FAMISANAR => 0,
            Service::SERVICE_VALORATION_FAMISANAR => 1750,
        );
        $invoice = Invoice::find($number);
        $client = Client::where('id', $invoice->client_id)->firstOrFail();
        $activities = array();
        $concepts = array();

        $EL = DB::select(BillReport::EL_SQL_NEW, [$client->id, $number]);
        $activities[] = json_decode(json_encode($EL), true);
        if (count($EL) > 0) {
            $concepts[] = array(
                'name' => 'CALIFICACION DE ORIGEN EL',
                'units' => count($EL),
                'unitv' => $prices[Service::SERVICE_EL_FAMISANAR],
                'totalv' => $prices[Service::SERVICE_EL_FAMISANAR] * count($EL),
            );
        }

        $AT = DB::select(BillReport::AT_SQL_NEW, [$client->id, $number]);
        $activities[] = json_decode(json_encode($AT), true);
        if (count($AT) > 0) {
            $concepts[] = array(
                'name' => 'CALIFICACION DE ORIGEN AT',
                'units' => count($AT),
                'unitv' => $prices[Service::SERVICE_AT_FAMISANAR],
                'totalv' => $prices[Service::SERVICE_AT_FAMISANAR] * count($AT),
            );
        }

        $PCL = DB::select(BillReport::PCL_SQL_NEW, [$client->id, $number]);
        $activities[] = json_decode(json_encode($PCL), true);
        if (count($PCL) > 0) {
            $concepts[] = array(
                'name' => 'PCL',
                'units' => count($PCL),
                'unitv' => $prices[Service::SERVICE_PCL_FAMISANAR],
                'totalv' => $prices[Service::SERVICE_PCL_FAMISANAR] * count($PCL),
            );
        }

        $table = '';
        foreach ($concepts as $c) {
            $table .= '<tr style="text-align: justify; font-weight: 500;"><td>' . $c['name'] . '</td><td  style="text-align: center;">' . $c['units'] . '</td></tr>';
        }

        $response = "<table>
            <thead>
                <tr>
                    <th>DETALLE</th>
                    <th>CANTIDAD</th>
                </tr>
            </thead>
            <tbody>
                $table
            </tbody>
        </table>";

        return $response;
    }

    public function changeStateInvoice(Request $req)
    {
        if ($req->isMethod('post')) {
            DB::beginTransaction();

            try {

                $invoice = Invoice::find($req->input('invoice_number'));

                if ($req->input('invoice_date_submit')) {
                    $invoice->status = "Pagado";
                    $invoice->date_pay_invoice = $req->input('invoice_date_submit');
                }

                $invoice->save();

                DB::commit();
            } catch (Exception $e) {
                DB::rollback();
            }
        }
        return redirect('facturacion');
    }

    public function billUpdate(Request $req)
    {
        if ($req->isMethod('post')) {
            DB::beginTransaction();

            try {

                $invoice = Invoice::find($req->input('invoice_num'));

                if ($req->input('invoice_number'))
                    $invoice->invoice_number = $req->input('invoice_number');
                if ($req->input('invoice_date_submit'))
                    $invoice->date_invoice = $req->input('invoice_date_submit');
                if ($req->input('invoice_receive_date_submit')) {
                    $invoice->date_receive_invoice = $req->input('invoice_receive_date_submit');
                    $invoice->status = 'Pendiente de Pago';
                }

                $invoice->save();

                DB::commit();
            } catch (Exception $e) {
                DB::rollback();
            }
        }
        return redirect('facturacion');
    }

    public function billUpdateOrienta(Request $req)
    {
        if ($req->isMethod('post')) {
            DB::beginTransaction();

            try {

                $invoice = Invoice::find($req->input('invoice_num'));

                $invoice->invoice_number_orienta = $req->input('invoice_number');
                $invoice->date_invoice_orienta = $req->input('invoice_date_submit');

                $invoice->save();

                DB::commit();
            } catch (Exception $e) {
                DB::rollback();
            }
        }
        return redirect('facturacion');
    }

    public function bill_doc_update(Request $req)
    {
        if ($req->isMethod('post')) {
            DB::beginTransaction();

            try {

                $docinvoice = Docinvoice::find($req->input('invoice_num'));

                $docinvoice->invoice_number = $req->input('invoice_number');
                $docinvoice->date_invoice = $req->input('invoice_date_submit');

                $docinvoice->save();

                DB::commit();
            } catch (Exception $e) {
                DB::rollback();
            }
        }
        return redirect('facturacion-profesionales');
    }

    public function updateDocInvoice(Request $req)
    {
        if ($req->isMethod('post')) {
            DB::beginTransaction();

            try {

                $docinvoice = Docinvoice::find($req->input('docinvoice_num'));

                $docinvoice->payed = 1;
                //$docinvoice->date_invoice  = $req->input('invoice_date_submit');

                $docinvoice->save();

                DB::commit();
            } catch (Exception $e) {
                DB::rollback();
            }
        }
        return redirect('facturacion');
    }

    public function getDocInvoice(Request $req)
    {
        $docinvoice_num = $req->input('docinvoice_num');

        $docinvoice_data = Docinvoice::find($docinvoice_num);
        $invoice_num = $docinvoice_data->invoice_id;
        $user_id = $docinvoice_data->user_id;
        $client = Client::find($docinvoice_data->client_id);

        $doctors_data = DB::select(BillReport::ATELPCL_DOCTOR_CONSOLIDATE_SQL_NEW, [$client->id, $invoice_num]);
        $doctors[] = json_decode(json_encode($doctors_data), true);
        $client_id = $client->id;

        if (count($doctors[0])) {
            $dexcel = Excel::create("PROF_ADSCRITO", function ($excel) use ($doctors, $client_id, $invoice_num, $user_id) {

                foreach ($doctors[0] as $doctor) {

                    $doctors_data = DB::select(BillReport::ATELPCL_DOCTOR_SPECIFIC_SQL_NEW, [$client_id, $invoice_num, $doctor['FUNCIONARIO_ID']]);
                    $doctor_activities = json_decode(json_encode($doctors_data), true);

                    $doctor['FUNCIONARIO'] = substr($doctor['FUNCIONARIO'], 0, 29);

                    if ($doctor['FUNCIONARIO_ID'] == $user_id) {
                        $excel->sheet($doctor['FUNCIONARIO'], function ($sheet) use ($doctor_activities) {
                            $sheet->fromArray($doctor_activities, null, 'A1', true);
                            $sheet->freezeFirstRow();
                            $sheet->setAutoFilter();

                            $sheet->row(1, function ($row) {
                                $row->setBackground('#7EBE2C');
                                $row->setFontColor('#FFFFFF');
                                $row->setFontWeight('bold');
                            });
                            $sheet->setCellValue('A1', 'NOMBRE SERVICIO');
                            $sheet->setCellValue('B1', 'ID SERVICIO');
                            $sheet->setCellValue('C1', 'FUNCIONARIO');
                            $sheet->setCellValue('D1', 'ID FUNCIONARIO');
                            $sheet->setCellValue('E1', 'ID FACTURA PROF ADSCRITO');
                            $sheet->setAutoSize(true);
                        });
                    }
                }
                $excel->setActiveSheetIndex(0);
            })->store('xlsx', false, true);
        }

        $zipper = new \Chumper\Zipper\Zipper;

        $zipper->make(storage_path("PROF_ASCRITOS_DOC{$docinvoice_num}.zip"));

        if (count($doctors[0])) {
            $zipper->add($dexcel['full']);
        }

        $zipper->close();

        Storage::disk('s3')->putFileAs('docs', new File(storage_path("PROF_ASCRITOS_DOC{$docinvoice_num}.zip")), "PROF_ASCRITOS_DOC{$invoice_num}.zip");

        if (count($doctors[0])) {
            unlink($dexcel['full']);
        }

        return response()->download(storage_path("PROF_ASCRITOS_DOC{$docinvoice_num}.zip"))->deleteFileAfterSend(true);
    }


    public function docinvoiceWord(Request $req, $cpath)
    {
        $docinvoice_num = $req->input('docinvoice_num');
        $docinvoice = Docinvoice::find($docinvoice_num);
        $client = Client::where('path', $cpath)->firstOrFail();
        $user = $docinvoice->user;
        $professional = $docinvoice->professional();
        $formatter = new NumberFormatter('es', NumberFormatter::SPELLOUT);
        $total = $docinvoice->subtotal();
        $num_casos = $docinvoice->docinvoice_activities->count();
        $total_formatted = number_format($total, 2, ',', '.');

        $name = 'cuenta_de_cobro_' . $docinvoice_num;

        $word = new \PhpOffice\PhpWord\PhpWord();
        $word->getSettings()->setThemeFontLang(new \PhpOffice\PhpWord\Style\Language(\PhpOffice\PhpWord\Style\Language::ES_ES));

        $word->setDefaultFontSize(10);
        //$word->setDefaultFontName('Century Gothic');

        $section = $word->createSection();

        $section->addText("Bogotá D.C. " . \Carbon\Carbon::createFromFormat('Y-m-d', \App\Correspondence::nextDay())->formatLocalized('%B %d, %Y'), ['size' => 11]);

        $section->addText("", ['size' => 11]);
        $section->addText("", ['size' => 11]);
        $section->addText("", ['size' => 11]);
        $section->addText("", ['size' => 11]);
        $section->addText("", ['size' => 11]);

        $section->addText("Cuenta de cobro No.", ['size' => 11, 'bold' => true], ['align' => \PhpOffice\PhpWord\SimpleType\TextAlignment::CENTER]);

        $section->addText("", ['size' => 11]);
        $section->addText("", ['size' => 11]);
        $section->addText("", ['size' => 11]);
        $section->addText("", ['size' => 11]);

        $section->addText("REN CONSULTORES", ['size' => 16, 'bold' => true], ['align' => \PhpOffice\PhpWord\SimpleType\TextAlignment::CENTER]);

        $section->addText("", ['size' => 11]);
        $section->addText("Nit. 900.810.402-8", ['size' => 11, 'bold' => true], ['align' => \PhpOffice\PhpWord\SimpleType\TextAlignment::CENTER]);

        $section->addText("", ['size' => 11]);
        $section->addText("", ['size' => 11]);
        $section->addText("", ['size' => 11]);

        $section->addText("DEBE A:", ['size' => 12, 'bold' => true], ['align' => \PhpOffice\PhpWord\SimpleType\TextAlignment::CENTER]);

        $section->addText("", ['size' => 11]);
        $section->addText(($user ? $user->full_name : "ESCRIBA SU NOMBRE AQUI"), ['size' => 12], ['align' => \PhpOffice\PhpWord\SimpleType\TextAlignment::CENTER]);

        $section->addText("", ['size' => 11]);

        $section->addText(($professional ? "C.C. {$professional->cc_nit} de Ciudad (Departamento)." : "CC 1.111.111.111 de Ciudad (Departamento)"), ['size' => 12], ['align' => \PhpOffice\PhpWord\SimpleType\TextAlignment::CENTER]);

        $section->addText("", ['size' => 11]);

        $section->addText("LA SUMA DE:", ['size' => 12, 'bold' => true], ['align' => \PhpOffice\PhpWord\SimpleType\TextAlignment::CENTER]);

        $section->addText("", ['size' => 11]);

        $section->addText("{$formatter->format($total)} ({$total_formatted})", ['size' => 12, 'bold' => true], ['align' => \PhpOffice\PhpWord\SimpleType\TextAlignment::CENTER]);

        $section->addText("", ['size' => 11]);
        $section->addText("", ['size' => 11]);
        $section->addText("", ['size' => 11]);

        $section->addText("Por concepto de:", ['size' => 12]);
        $section->addText("{$num_casos} casos del proyecto", ['size' => 12]);
        $section->addText("Durante el periodo {$docinvoice->init_date} - {$docinvoice->last_date}", ['size' => 12]);
        $section->addText("", ['size' => 11]);
        $section->addText("", ['size' => 11]);
        $section->addText("", ['size' => 11]);
        $section->addText("", ['size' => 11]);
        $section->addText("", ['size' => 11]);

        $section->addText("Cordialmente,", ['size' => 12, 'bold' => true]);

        $section->addText("", ['size' => 11]);
        $section->addText("", ['size' => 11]);
        $section->addText("", ['size' => 11]);
        $section->addText("", ['size' => 11]);

        $section->addText(($user ? $user->full_name : "Nombre de la persona que cobra"), ['size' => 12]);
        $section->addText("", ['size' => 11]);
        $section->addText(($professional ? "C.C. {$professional->cc_nit}" : "CC 1.111.111.111"), ['size' => 12]);

        $section->addText("", ['size' => 11]);

        $section->addText("Teléfono XXXXXXX Dirección Calle X # 0-00,", ['size' => 12]);
        $section->addText("*Cuenta del banco para consignar el dinero.,", ['size' => 12]);


        // Saving the document as Word file...
        $objWriter = \PhpOffice\PhpWord\IOFactory::createWriter($word, 'Word2007');
        $objWriter->save(storage_path($name . '.docx'));
        //Storage::disk('s3')->putFileAs('words', new File(storage_path($name.'.docx')), $name.'.docx');

        return response()->download(storage_path($name . '.docx'))->deleteFileAfterSend(true);
    }


    /**********************************************************/
    /***************** END BILLING METHODS  *******************/
    /**********************************************************/

    private function excelData($items)
    {
        $data = array();
        foreach ($items as $item) {
            $data[] = array(
                'SERVICIO' => $item->service->name,
                'NUMERO_DICTAMEN' => $item->id,
                'FECHA_SERVICIO' => $item->created_at->formatLocalized('%d/%m/%Y'),
                'NOMBRE_ESTADO_SERVICIO' => $item->state->name,
                'NOMBRE' => $item->affiliate->full_name,
                'TIPO_DOC' => $item->affiliate->doc_type,
                'INDENTIFICACION' => $item->affiliate->doc_number,
            );
        }
        return $data;
    }

    public function clients(Request $req, $cpath, $id = null)
    {
        $theClient = null;
        if ($req->isMethod('post')) {
            DB::beginTransaction();

            try {

                if (!$req->input('id')) {
                    $client = new Client;
                } else {
                    $client = Client::find($req->input('id'));
                }

                $client->path = $req->input('path');
                $client->name = $req->input('name');
                $client->color = $req->input('color');
                $client->nickname = $req->input('nickname');
                $client->footer = $req->input('footer');
                $client->email = $req->input('email');
                $client->address = $req->input('address');
                $client->phone = $req->input('phone');

                if ($req->hasFile('logo')) {
                    $client->logo = $req->file('logo')->store('client_logo');
                }

                $client->client_type = $req->input('client_type');
                $client->client_default = $req->input('client_default');
                $client->save();

                $services = explode(',', $req->input('services'));

                ClientService::where('client_id', $client->id)
                    ->whereNotIn('service_id', $services)
                    ->delete();

                foreach ($services as $service) {
                    ClientService::firstOrCreate(['service_id' => $service, 'client_id' => $client->id]);
                }

                DB::commit();
                return redirect('/admin/clientes');
            } catch (Exception $e) {
                DB::rollback();
                return back()->withInput();
            }
        } else if ($req->isMethod('get') && $id != null) {
            $theClient = Client::find($id);
        }
        return view('admin.clients', [
            'clients' => Client::get()->sortBy('name'),
            'services' => Service::get(),
            'client' => $theClient,
        ]);
    }

    public function users(Request $req, $cpath, $id = null)
    {
        $theUser = null;
        $clients_allowed = null;
        $clients_allowed_count = null;
        $schedule = null;
        $tomador = auth()->user()->area_id == Area::TOMADOR;

        if ($req->isMethod('post')) {
            DB::beginTransaction();
            try {
                $new = false;
                if (!$req->input('id')) {
                    $user = new User();
                    $new = true;
                } else {
                    $user = User::find($req->input('id'));
                }
                $user->area_id = $req->input('area_id');
                $user->doc_type = $req->input('doc_type');
                $user->identification_number = $req->input('identification_number');
                $user->username = $req->input('username');
                $user->last_name = $req->input('last_name');
                $user->first_name = $req->input('first_name');
                $user->department = $req->input('department');
                $user->municipality = $req->input('municipality');
                $user->email = $req->input('email');
                $user->phone = $req->input('phone');
                $user->ocupation = $req->input('ocupation');
                $user->company_id = $req->input('company_id');
                $user->medical_record_number = $req->input('medical_record_number');
                $user->rethus = $req->input('rethus');
                $user->license_number = $req->input('license_number');
                $user->tomador_id = $tomador ? auth()->user()->id : '';

                // si es tomador del seguro autorizado le creamos un codigo único
                if($req->input('area_id') == 61 ){
                    $uniqueCode = substr(hash('sha256', $req->input('identification_number') . microtime(true)), 0, 8);

                    $user->unique_code = $uniqueCode;

                    $userTaker =  auth()->user();

                    // Obtener todas las actividades con sus consecutivos
                    $activityPolicy = Activity::where('affiliate_id', $userTaker->affiliate_id)
                        ->where('service_id', 75)
                        ->with('policy_sort')
                        ->get();


                    $activityPolicyTwo = Activity::where('affiliate_id', $userTaker->affiliate_id)
                        ->where('service_id', 75)
                        ->first();

                    // Formatear los consecutivos
                    $consecutives = $activityPolicy->map(function ($activity) {
                    return $activity->policy_sort ? $activity->policy_sort->formatNumberConsecutive() : null;
                    })->filter()->implode(', ');


                    $nameResponsible = ucwords(mb_strtolower( $req->input('first_name') . ' ' . $req->input('last_name')));
                    
                    $representante = ucwords(mb_strtolower( $userTaker->full_name ?? ''));
                    $email = $req->input('email') ?? '';
    

                    $bodyEmail = "
                          <p>
                              <strong>Notificación del código único seguro de riesgo laboral</strong>
                        
                              Estimado/a $nameResponsible,
                         
                              Nos dirigimos a usted para informarle que ha sido registrado en nuestro sistema del Seguro Obligatorio de Riesgo del Trabajo, como autorizado para realizar el reporte de accidente de la(s) póliza(s)
                              <strong>$consecutives</strong> a nombre de $representante. A continuación, encontrará su código único de identificación:
                       
                              <strong>Código Único: $uniqueCode </strong>
                              Este código es personal e intransferible.
                              
                              Si tiene alguna pregunta o necesita más información, no dude en ponerse en contacto mediante el correo <a href='mailto:<EMAIL>'><EMAIL></a> o al teléfono 4102-7681.
                          </p>
                        
                      ";
    

                    $mailSent = new SendDocumentDataBase(
                        $email,
                        "Notificación del código único seguro de riesgo laboral",
                        "<EMAIL>",
                        "Notificación del código único seguro de riesgo laboral",
                        [
                            "text" => $bodyEmail,
                            "sender" => 'MNK Seguros'
                        ],
                        "<EMAIL>",
                        [],
                        "send_document_db",
                        $activityPolicyTwo->client,
                        request()->getHost(),
                        $activityPolicyTwo->id
                    );
    
                    $mailSent->sendMail();
                }

                //validar si el rol del usuario es proveedor
                if ($user->isProvider() || $user->isTPA()) {
                    //proveedor seleccionado desde el formulario
                    $provider_id = $req->input('provider');

                    //validar si se proporcionó el nombre del proveedor
                    if (!$provider_id) {
                        // Redirigir con el mensaje de error
                        return back()->withErrors("El proveedor no se proporcionó")->withInput();
                    }

                    //consultar si el proveedor si existe
                    $provider = Provider::where('id', $provider_id)->firstOrFail();

                    //validar si existe si se encontró el afaliado del proveedor
                    if (!$provider) {
                        // Redirigir con el mensaje de error
                        return back()->withErrors("El proveedor proporcionado no se encontró en el sistema")->withInput();
                    }

                    //asignar afiliado del proveedor
                    //$user->affiliate_id = $provider->affiliate_id;

                    //nombre del proveedor
                    $user->provider_id = $provider->id;
                }


                if (!$req->input('id')) {
                    $user->password = Hash::make($req->input('password'));
                }


                $user->active = $req->input('active') == 1;
                $user->has_schedule = $req->input('onsite_schedule') == 1 || $req->input('virtual_schedule') == 1;
                // Upload photo
                $photo = $req->file('photo');
                if ($photo) {
                    $user->photo = $photo->store('user_photo', 's3');
                }
                // Upload signature
                $signature = $req->file('signature');
                if ($signature) {
                    $user->signature = $signature->store('user_signature', 's3');
                }
                // Validar usuario nuevo
                $exists = User::query()
                    ->orWhere('email', '=', $user->email)
                    ->orWhere('identification_number', '=', $user->identification_number)
                    ->orWhere('username', '=', $user->username)
                    ->first();

                if ($exists && $new === true) {
                    $exists->identification_number = $req->input('identification_number');
                    $exists->save();
                    return view('/information/user_exists', [
                        'user' => $exists
                    ]);
                } else {
                    $user->save();

                    $clients = explode(',', $req->input('clients'));

                    UserClient::where('user_id', $user->id)
                        ->whereNotIn('client_id', $clients)
                        ->delete();

                    foreach ($clients as $client) {
                        UserClient::firstOrCreate(['client_id' => $client, 'user_id' => $user->id]);
                    }
                    $clients_allowed = $user->clients_ids();
                    $clients_allowed_count = count($clients_allowed);

                    if (!$req->input('id') && $req->input('send_mail')) {
                        Mail::to($user)->send(new UserCreated($user, $req->input('password')));
                    }
                    if ($user->has_schedule) {
                        $s = Schedule::query()->where('user_id', $user->id)->first();
                        if (!$s) {
                            $s = new Schedule();
                            $s->user_id = $user->id;
                            $s->type = 'ORIENTATION';
                            $s->type_schedule = 'WEEK';
                            $s->week = "{\"Lunes\":[{\"init\":\"07:00:00\",\"end\":\"17:00:00\"}],\"Martes\":[{\"init\":\"07:00:00\",\"end\":\"17:00:00\"}],\"Miercoles\":[{\"init\":\"07:00:00\",\"end\":\"17:00:00\"}],\"Jueves\":[{\"init\":\"07:00:00\",\"end\":\"17:00:00\"}],\"Viernes\":[{\"init\":\"07:00:00\",\"end\":\"17:00:00\"}],\"Sabado\":[{\"init\":\"07:00:00\",\"end\":\"12:00:00\"}],\"Domingo\":[]}";
                            $s->calendar = "[]";
                        }
                        $s->valoration_type = $req->input('onsite_schedule') == 1 ? 1 : 2;
                        $s->save();
                    }


                    if ($user->view_tomador_autorizado()) {
                        UserAuthorizedTomador::firstOrCreate([
                            'user_id' => $user->id,
                            'tomador_id' => auth()->user()->id,
                        ]);
                    }

                    DB::commit();
                    return redirect('/admin/usuarios', 302, [], true)->with('success', 'Usuario ' . $req->input('username') . ' guardado con éxito.');
                }
            } catch (Exception $e) {
                DB::rollback();
                return back()->withInput();
            }
        } else if ($req->isMethod('get') && $id != null) {
            $theUser = User::with('userPermission')->find($id);
            $clients_allowed = $theUser->clients_ids();
            $clients_allowed_count = count($clients_allowed);
            $schedule = Schedule::query()->where('user_id', $id)->first();
        }

        $client = Client::where('path', $cpath)->firstOrFail();
        $usuarioId = $req->input('usuario');


        $users = User::query()
            ->leftJoin('user_clients', 'user_clients.user_id', '=', 'users.id')
            ->where('user_clients.client_id', $client->id)
            ->select('users.*')
            ->orderBy('active', 'desc')
            ->orderBy('full_name')
            ->with('area');


        $view_permision = null;

        if ($tomador) {
            $users->where('tomador_id', auth()->user()->id);
            $view_permision  = UserView::query()->get();
        }

        $filterUser = $users->get();
        if (!empty($usuarioId) && $usuarioId != '-1') {
            $users->where('users.id', $usuarioId);
        }

        $users = $users->get();

        return view('admin.users', [
            'users' => $users,
            'filterUser' => $filterUser,
            'clients_allowed_count' => $clients_allowed_count,
            'user' => $theUser,
            'areas' => Area::get()->sortBy('name'),
            'client_default' => Client::current(Route::current())->id,
            'clients' => Client::query()->whereIn('id', [1, 2, 3])->get()->sortBy('name'),
            'companies' => Company::all(),
            'schedule' => $schedule,
            'providers' => Provider::where('active', 1)->get()->sortBy('name'),
            'tomador' => $tomador,
            'view_permision' => $view_permision
        ]);
    }


    public function autoriceTomador(Request $request)
    {
        // 1) Validamos el user_id
        $data = $request->validate([
            'user_id' => ['required', 'integer', 'exists:users,id'],
        ]);

        // 2) Creamos la relación si no existe
        $auth = UserAuthorizedTomador::firstOrCreate([
            'user_id' => $data['user_id'],
            'tomador_id' => Auth::id(),
        ]);


        return response()->json([
            'status' => 'ok',
            'authorized_id' => $auth->id,
        ]);
    }


    public function getAutorizadosTomador()  {

        $users = UserAuthorizedTomador::with(['user','tomador'])->where('user_id', auth()->user()->id)->get();
    
       
        return view('admin.autorizado_tomador', ['data_tomadores' => $users]);
    }



    public function user_permision(Request $request, $cpath, $id = null)
    {

        $poliza_id = $request->input('poliza_id');

        $users = UserAuthorizedTomador::with(['user', 'tomador'])->where('tomador_id', auth()->user()->id)->get();

        $polizas = PolicySort::query()
            ->whereNotNull('consecutive')
            ->whereHas('activity', function ($q) {
                $q->where('service_id', Service::SERVICE_POLICY_SORT_MNK)
                    ->whereIn('state_id', [
                        StatePoliza::POLIZA_EMITIDA_ACTIVA,
                        StatePoliza::POLIZA_SUSPENDIDA,
                    ]);

                if (!Auth::user()->isAdmin()) {
                    $q->where('affiliate_id', auth()->user()->affiliate_id);
                }

            })
            ->with('activity')->orderBy('consecutive', 'asc');



        $polizas_drop = $polizas->get();

        if ($poliza_id) {
            $polizas = $polizas->where('consecutive', $poliza_id);
        }

        $polizas = $polizas->paginate(10);



        $view_permision = UserView::query()->get();

        return view('admin.users_permisos', [
            'users' => $users,
            'user' => '',
            'view_permision' => $view_permision,
            'polizas' => $polizas,
            'polizas_drop' => $polizas_drop
        ]);
    }

    public function checkAutorizado(Request $req)
    {
        $user = User::where('identification_number', $req->doc_number)
            ->first();
        return response()->json([
            'exists' => (bool) $user,
            'user' => $user
        ]);
    }

    public function getPolicyActive(Request $request, $cpath)
    {

        $userId = $request->user_id;

        $polizas = UserAuthorizedPolicies::with(['permissions.userView', 'activity.policy_sort'])
            ->where('user_id', $userId)
            ->get();

        return response()->json(['polizas' => $polizas]);

    }


    public function permision_save(Request $req)
    {
        $userId = $req->input('user_id');
        $policies = $req->input('policies', []);

        DB::beginTransaction();
        try {
            foreach ($policies as $p) {
                $activityId = $p['policy_id'];
                $active = (int) $p['active'];

                if ($active) {
                    // 1) Si la póliza está activa: creamos o traemos el registro en UserAuthorizedPolicies
                    $auth = UserAuthorizedPolicies::firstOrCreate([
                        'user_id' => $userId,
                        'activity_id' => $activityId,
                        'tomador_id' => Auth::user()->id 
                    ]);
                } else {
                    // 2) Si se desactiva: borramos el registro (y con cascade los permisos, si tu FK está en cascade)
                    UserAuthorizedPolicies::where('user_id', $userId)
                        ->where('activity_id', $activityId)
                        ->where('tomador_id', Auth::user()->id )
                        ->delete();
                    // Y saltamos esta póliza
                    continue;
                }

                // 3) Ahora sincronizamos los permisos para esa autorización
                foreach ($p['permissions'] as $perm) {
                    // permission_id == user_view_id en tu tabla UserPermission
                    UserPermission::updateOrCreate(
                        [
                            'user_policies_id' => $auth->id,
                            'user_view_id' => $perm['permission_id'],
                        ],
                        [
                            'view' => (int) $perm['view'],
                            'edit' => (int) $perm['edit'],
                        ]
                    );
                }
            }



            DB::commit();

            return response()->json(['status' => 'ok']);
        } catch (Exception $e) {
            DB::rollBack();

            dd($e->getMessage());
            return response()->json([
                'message' => 'Errores durante el procesamiento del cargue automático:',
                'e' => $e->getMessage()
            ], 500);
        }




    }


    public function filter(Request $request)
    {
        $query = User::with('area');

        // Búsqueda por texto
        if ($request->filled('buscar')) {
            $search = strtolower($request->buscar);
            $query->where(function ($q) use ($search) {
                $q->whereRaw('LOWER(full_name) LIKE ?', ["%$search%"])
                    ->orWhereRaw('LOWER(email) LIKE ?', ["%$search%"])
                    ->orWhereRaw('LOWER(username) LIKE ?', ["%$search%"])
                    ->orWhereHas('area', function ($q) use ($search) {
                        $q->whereRaw('LOWER(name) LIKE ?', ["%$search%"]);
                    });
            });
        }

        // Filtro por usuario específico
        if ($request->filled('usuario') && $request->usuario != -1) {
            $query->where('id', $request->usuario);
        }

        $users = $query->paginate(15);

        // Si es petición AJAX, devolver solo la lista
        if ($request->ajax()) {
            return view('admin.users', ['users' => $users])->render();
        }

        return view('admin.users', compact('users'));
    }


    public function services(Request $req, $cpath, $id = null)
    {
        $theService = null;
        $permissions = null;
        $service_id = null;
        if ($req->isMethod('post')) {
            DB::beginTransaction();
            try {
                switch ($req->input('form')) {
                    case 'SERVICE':
                        if (!$req->input('id')) {
                            $service = new Service;
                        } else {
                            $service = Service::find($req->input('id'));
                        }

                        $service->name = $req->input('name');
                        $service->default_state_id = $req->input('default_state_id');
                        $service->required_infosource = $req->input('required_infosource');
                        $service->required_employment = $req->input('required_employment');
                        $service->save();

                        $states = explode(',', $req->input('states'));

                        ServiceState::where('service_id', $service->id)
                            ->whereNotIn('state_id', $states)
                            ->delete();

                        foreach ($states as $state) {
                            if ($state != null) {
                                ServiceState::firstOrCreate(['service_id' => $service->id, 'state_id' => $state]);
                            }
                        }

                        $actions = explode(',', $req->input('actions'));

                        ServiceAction::where('service_id', $service->id)
                            ->whereNotIn('action_id', $actions)
                            ->delete();

                        foreach ($actions as $action) {
                            if ($action != null) {
                                $sa = ServiceAction::firstOrCreate(['service_id' => $service->id, 'action_id' => $action]);
                            }
                        }

                        $clients = explode(',', $req->input('clients'));

                        ClientService::where('service_id', $service->id)
                            ->whereNotIn('client_id', $clients)
                            ->delete();

                        foreach ($clients as $client) {
                            if ($client != null) {
                                $sa = ClientService::firstOrCreate(['service_id' => $service->id, 'client_id' => $client]);
                            }
                        }

                        $documents = $req->input('documents');
                        if ($documents) {

                            ServiceDocument::query()
                                ->where('service_id', $service->id)
                                ->whereNotIn('id', $documents['id'])
                                ->delete();

                            for ($i = 0; $i < count($documents['id']); $i++) {
                                if ($documents['id'][$i] != null) {
                                    $document = ServiceDocument::query()->find($documents['id'][$i]);
                                } else {
                                    $document = new ServiceDocument;
                                }

                                $document = new ServiceDocument;
                                $document->subprocess_code = $documents['subprocess_code'][$i];
                                $document->person_to_qualify = $documents['person_to_qualify'][$i];
                                $document->companion_required = $documents['companion_required'][$i];
                                $document->third_payment_auth = $documents['third_payment_auth'][$i];
                                $document->service_id = $service->id;
                                $document->code = $documents['code'][$i];
                                $document->name = $documents['name'][$i];
                                $document->required = $documents['required'][$i];
                                $document->save();
                            }
                        }

                        $fields = $req->input('fields');

                        if ($fields) {
                            ServiceField::where('service_id', $service->id)
                                ->whereNotIn('id', $fields['id'])
                                ->delete();

                            for ($i = 0; $i < count($fields['name']); $i++) {
                                if (!$fields['id'][$i]) {
                                    $field = new ServiceField;
                                } else {
                                    $field = ServiceField::find($fields['id'][$i]);
                                }

                                $field->service_id = $service->id;
                                $field->type = $fields['type'][$i];
                                $field->name = $fields['name'][$i];
                                $field->values = $fields['values'][$i];
                                $field->required = $fields['required'][$i];
                                $field->save();
                            }
                        } else {
                            ServiceField::where('service_id', $service->id)
                                ->delete();
                        }
                        break;
                    case 'PERMISSIONS':
                        $service_id = $req->input('id');
                        $permissions_old = Permission::where('service_id', $service_id)
                            ->where(function ($query) {
                                $query->where('description', '!=', 'GENERAL')
                                    ->orWhereNull('description');
                            })
                            ->get();
                        $permissions_new = collect($req->input('permissions'))->map(function ($dato) {
                            return (object)$dato;
                        });
                        // Elimina los permisos que ya no están
                        foreach ($permissions_old as $old) {
                            $found = false;
                            foreach ($permissions_new as $new) {
                                if ($old->id == $new->id) {
                                    $found = true;
                                    break;
                                }
                            }
                            if (!$found) {
                                $old->delete();
                            }
                        }
                        foreach ($permissions_new as $permission) {
                            $p = new Permission();
                            if ($permission->id) {
                                $p = Permission::find($permission->id);
                            }
                            $p->service_id = $service_id;
                            $p->name = $permission->name;
                            $p->description = $permission->description;
                            $p->save();
                        }
                        break;
                }
                DB::commit();
                if ($service_id) {
                    return redirect('/admin/servicios/' . $service_id);
                }
                return redirect('/admin/servicios');
            } catch (Exception $e) {
                DB::rollback();
                return back()->withInput();
            }
        } else if ($req->isMethod('get') && $id != null) {
            $theService = Service::find($id);
            $permissions = Permission::where('service_id', $id)
                ->where(function ($query) {
                    $query->where('description', '!=', 'GENERAL')
                        ->orWhereNull('description');
                })
                ->get();
        }
        $services = Service::orderBy('name')
            ->with('clients')
            ->with('states')
            ->with('actions')
            ->with('documents')
            ->with('default_state')
            ->get();
        return view('admin.services', [
            'services' => $services,
            'actions' => Action::get(),
            'states' => State::get(),
            'clients' => Client::get(),
            'service' => $theService,
            'permissions' => $permissions,
        ]);
    }

    public function roles(Request $req, $cpath, $id = null)
    {
        $role = null;
        $role_services = [];
        $services = Service::orderBy('name')->with('clients')->get();
        if ($req->isMethod('post')) {
            DB::beginTransaction();
            try {
                switch ($req->input('form')) {
                    case 'ROLE':
                        $role = new Area($req->input('role'));
                        $role['name'] = strtoupper($role['name']);
                        if ($role->id == null) {
                            $role->save();
                        } else {
                            $role = Area::find($role->id);
                            $role->update($req->input('role'));
                        }
                        break;
                    case 'ADD_SERVICE':
                        $service_id = $req->input('service_id');
                        // Obtener o crear los permisos básicos o generales de un servicio
                        $permissions = [];
                        foreach (Permission::general_permissions() as $key => $value) {
                            $permission = Permission::where('name', $value)
                                ->where('service_id', $service_id)
                                ->first();
                            if (!$permission) {
                                $permission = new Permission();
                                $permission->name = $value;
                                $permission->service_id = $service_id;
                                $permission->description = 'GENERAL';
                                $permission->save();
                            }
                            $permissions[] = $permission;
                        }
                        // Asociar los permisos al rol
                        foreach ($permissions as $permission) {
                            $area_permission = new AreaPermission();
                            $area_permission->area_id = $id;
                            $area_permission->permission_id = $permission->id;
                            $area_permission->save();
                        }
                        break;
                    case 'REMOVE_SERVICE':
                        $service_id = $req->input('service_id');
                        AreaPermission::whereHas('permissions', function ($query) use ($service_id) {
                            $query->where('service_id', $service_id);
                        })
                            ->where('area_id', $id)
                            ->delete();
                        break;
                    case 'UPDATE_SERVICE':
                        $permissions = $req->input('permissions');
                        // Buscar o crear el permiso del servicio
                        foreach ($permissions as $permission) {
                            $states = array_key_exists('states', $permission) ? $permission['states'] : '';
                            AreaPermission::updateOrCreate(
                                [
                                    'permission_id' => $permission['id'],
                                    'area_id' => $id
                                ], // Valores de busqueda
                                [
                                    'view' => $permission['view'],
                                    'edit' => $permission['edit'],
                                    'states' => $states
                                ] // Valores a actualizar o insertar
                            );
                        }
                        break;
                }
                DB::commit();
                if ($id) {
                    return redirect('/admin/roles/' . $id);
                }
                return redirect('/admin/roles');
            } catch (Exception $e) {
                DB::rollback();
                return back()->withInput();
            }
        } else if ($req->isMethod('get') && $id != null) {
            $role = Area::find($id);
            $role_services = Service::whereHas('permissions.areaPermissions', function ($query) use ($id) {
                $query->where('area_id', $id);
            })->with(['permissions', 'permissions.areaPermissions' => function ($query) use ($id) {
                $query->where('area_id', $id);;
            }])->with('states')->get();
            $services = $services->diff($role_services);
        }

        return view('admin.roles', [
            'roles' => Area::orderBy('name')->get(),
            'role' => $role,
            'services' => $services,
            'role_services' => $role_services,
            'states' => State::get()
        ]);
    }

    public function companies(Request $req, $cpath, $id = null)
    {
        $company = null;
        $contracts = null;
        if ($req->isMethod('post')) {
            DB::beginTransaction();
            try {
                switch ($req->input('form')) {
                    case 'SAVE_COMPANY':
                        $id = $req->input('id');
                        if ($id || $id == 0) {
                            $company = Company::find($id);
                        } else {
                            $company = new Company();
                        }
                        $company->region_id = $req->input('region_id');
                        $company->name = strtoupper($req->input('name'));
                        $company->nit = $req->input('nit');
                        $company->dv = $req->input('dv');
                        $company->department = $req->input('department');
                        $company->municipality = $req->input('municipality');
                        $company->address = $req->input('address');
                        $company->phone = $req->input('phone');
                        $company->legal_representative = $req->input('legal_representative');
                        $company->email = strtolower($req->input('email'));
                        $company->website = strtolower($req->input('website'));
                        $company->save();
                        break;
                    case 'SAVE_CONTRACT':
                        $c = $req->input('contract');
                        if ($c['id']) {
                            $contract = Contract::find($c['id']);
                        } else {
                            $contract = new Contract();
                        }
                        $contract->company_id = $id;
                        $contract->name = $c['name'];
                        $contract->start_date = $c['start_date'];
                        $contract->end_date = $c['end_date'];
                        $contract->subject = $c['subject'];
                        $contract->save();
                        break;
                    case 'DELETE_CONTRACT':
                        $contract = $req->input('contract');
                        if ($contract['id']) {
                            Contract::find($contract['id'])->delete();
                        }
                        break;
                }
                DB::commit();
                if ($id) {
                    return redirect('/admin/empresas/' . $id);
                }
                return redirect('/admin/empresas');
            } catch (Exception $e) {
                DB::rollback();
                return back()->withInput();
            }
        } else if ($req->isMethod('get') && $id != null) {
            $company = Company::find($id);
            $contracts = Contract::orderBy('created_at')->where('company_id', $id)->get();
        }
        return view('admin.companies', [
            'companies' => Company::orderBy('name')->get(),
            'company' => $company,
            'contracts' => $contracts,
            'regions' => Region::all(),
        ]);
    }

    public function states(Request $req, $cpath, $id = null)
    {
        $theState = null;
        if ($req->isMethod('post')) {
            DB::beginTransaction();

            try {

                if (!$req->input('id')) {
                    $state = new State;
                } else {
                    $state = State::find($req->input('id'));
                }

                $state->name = $req->input('name');
                $state->autoassign_to_area_id = $req->input('autoassign_to_area_id');
                $state->save();

                $actions = explode(',', $req->input('actions'));

                StateAction::where('state_id', $state->id)
                    ->whereNotIn('action_id', $actions)
                    ->delete();

                foreach ($actions as $action) {
                    if ($action != null) {
                        StateAction::firstOrCreate(['action_id' => $action, 'state_id' => $state->id]);
                    }
                }

                $services = explode(',', $req->input('services'));

                ServiceState::where('state_id', $state->id)
                    ->whereNotIn('service_id', $services)
                    ->delete();

                foreach ($services as $service) {
                    ServiceState::firstOrCreate(['service_id' => $service, 'state_id' => $state->id]);
                }

                $areas = explode(',', $req->input('areas'));

                StateArea::where('state_id', $state->id)
                    ->whereNotIn('area_id', $areas)
                    ->delete();

                foreach ($areas as $area) {
                    StateArea::firstOrCreate(['area_id' => $area, 'state_id' => $state->id]);
                }

                DB::commit();

                if (!$req->query('s') || $req->query('s') == -1) {
                    return redirect('/admin/estados');
                } else {
                    return redirect('/admin/estados?s=' . $req->query('s'));
                }
            } catch (Exception $e) {
                DB::rollback();
                return back()->withInput();
            }
        } else if ($req->isMethod('get') && $id != null) {
            $theState = State::find($id);
        }

        if (!$req->query('s') || $req->query('s') == -1) {
            $states = State::with('actions')->orderBy('name')->get();
        } else {
            $states = State::whereHas('services', function ($query) use ($req) {
                return $query->where('services.id', $req->query('s'));
            })->with('actions')->orderBy('name')->get();
        }

        return view('admin.states', [
            'services' => Service::orderBy('name')->with('clients')->get(),
            'areas' => Area::get()->sortBy('name'),
            'states' => $states,
            'actions' => Action::get(),
            'state' => $theState,
        ]);
    }

    public function actions(Request $req, $cpath, $id = null)
    {
        $theAction = null;
        if ($req->isMethod('post')) {

            DB::beginTransaction();

            try {

                if (!$req->input('id')) {
                    $action = new Action;
                } else {
                    $action = Action::find($req->input('id'));
                }

                $action->name = $req->input('name');
                $action->required_employment = $req->input('required_employment') == 1;
                $action->required_documents = $req->input('required_documents') == 1;
                $action->required_fields = $req->input('required_fields') == 1;
                $action->required_diagnostic = $req->input('required_diagnostic') == 1;
                $action->required_fulldiagnostic = $req->input('required_fulldiagnostic') == 1;
                $action->required_dictum = $req->input('required_dictum') == 1;
                $action->restore_user = $req->input('restore_user') == 1;
                $action->documents = $req->input('documents');
                $action->send_targets = $req->input('send_targets');
                $action->dictum_dates = $req->input('dictum_dates');
                $action->new_service = $req->input('new_service');
                $action->return_last_user_action_id = $req->input('return_last_user_action_id');
                $action->required_list_documents = $req->input('required_list_documents');
                $action->auto_alert = $req->input('auto_alert');
                $action->save();

                $action_states = $req->input('action_states');
                foreach ($action_states['service_id'] as $k => $service_id) {
                    $ass = ActionServiceState::where('action_id', $action->id)
                        ->where('service_id', $service_id)
                        ->first();

                    if (!$ass) {
                        $ass = new ActionServiceState;
                        $ass->action_id = $action->id;
                        $ass->service_id = $service_id;
                    }

                    $ass->state_id = $action_states['state_id'][$k];
                    $ass->save();
                }

                $states = explode(',', $req->input('states'));

                StateAction::where('action_id', $action->id)
                    ->whereNotIn('state_id', $states)
                    ->delete();

                foreach ($states as $state) {
                    StateAction::firstOrCreate(['action_id' => $action->id, 'state_id' => $state]);
                }

                $services = explode(',', $req->input('services'));

                ServiceAction::where('action_id', $action->id)
                    ->whereNotIn('service_id', $services)
                    ->delete();

                foreach ($services as $service) {
                    ServiceAction::firstOrCreate(['service_id' => $service, 'action_id' => $action->id]);
                }

                $fields = $req->input('fields');

                if ($fields) {
                    ActionField::where('action_id', $action->id)
                        ->whereNotIn('id', $fields['id'])
                        ->delete();

                    for ($i = 0; $i < count($fields['name']); $i++) {
                        if (!$fields['id'][$i]) {
                            $field = new ActionField;
                        } else {
                            $field = ActionField::find($fields['id'][$i]);
                        }

                        $field->action_id = $action->id;
                        $field->type = $fields['type'][$i];
                        $field->name = $fields['name'][$i];
                        $field->values = $fields['values'][$i];
                        $field->required = $fields['required'][$i];
                        $field->save();
                    }
                } else {
                    ActionField::where('action_id', $action->id)
                        ->delete();
                }

                DB::commit();

                if (!$req->query('s') || $req->query('s') == -1) {
                    return redirect('/admin/acciones');
                } else {
                    return redirect('/admin/acciones?s=' . $req->query('s'));
                }
            } catch (Exception $e) {
                DB::rollback();
                return back()->withInput();
            }
        } else if ($req->isMethod('get') && $id != null) {
            $theAction = Action::find($id);
        }

        if (!$req->query('s') || $req->query('s') == -1) {
            $actions = Action::with('services')->orderBy('name')->get();
        } else {
            $actions = Action::whereHas('services', function ($query) use ($req) {
                return $query->where('services.id', $req->query('s'));
            })->with('services')->get()->sortBy('name');
        }

        return view('admin.actions', [
            'services' => Service::orderBy('name')
                ->with('documents')
                ->with('states')
                ->with('clients')->get(),
            'states' => State::get()->sortBy('name'),
            'actions' => $actions,
            'action' => $theAction,
        ]);
    }
}
