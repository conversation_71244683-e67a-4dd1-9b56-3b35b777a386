<?php

namespace App\Http\Controllers;

use App\ActivityActionField;
use Excel;
use Illuminate\Http\File;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use SoapClient;
use SoapFault;

class CallController extends Controller
{
    public function finderWsdl()
    {
        return file_get_contents('http://**************/soap/index.php?wsdl');
    }

    public function finder(Request $req, $date, $call_id)
    {
        try {
            $client = new SoapClient('http://**************/soap/index.php?wsdl');

            $response = $client->MetodoConsulta($call_id, array('trace' => true));

            Log::error("SOAP Response " . $response);

            $response = explode(' = ', $response)[1];
            $date     = str_replace('-', '/', $date);

            if ($response != '') {
                $files   = array();
                $url     = "http://**************/audios/{$date}/";
                $html    = file_get_contents($url);
                $count   = preg_match_all('/<td><a href="([^"]+)">[^<]*<\/a><\/td>/i', $html, $files);
                $matches = array_filter($files[1], function ($file) use ($response) {return stripos(strtolower($file), strtolower($response)) !== false;});

                if (count($matches) > 0) {
                    Log::error("Audio file found", $matches);

                    $fileURL = $url . implode(',', $matches);
                    Storage::put($response, file_get_contents($fileURL));
                    return response()->file(storage_path('app/' . $response))->deleteFileAfterSend(true);
                } else {
                    Log::error("Audio file not found: {$fileURL}");

                    return abort(403, 'AUDIO FILE NOT FOUND');
                }

            } else {
                Log::error("Audio ID: {$call_id}");

                return abort(404, 'UNIQUEID NOT FOUND');
            }
        } catch (SoapFault $e) {
            Log::error('SoapException: ' . $e->getMessage());
            Log::error($client->__getLastRequest());
            Log::error($client->__getLastResponse());

            set_error_handler('var_dump', 0); // Never called because of empty mask.
            @trigger_error("");
            restore_error_handler();

            return 'El Servicio de consulta de Audios no responde';
        }
    }

    public function finderByName(Request $req, $date, $filename) {
        $date     = str_replace('-', '/', $date);
        $file_url = "http://**************/audios/{$date}/{$filename}";

        Storage::put($filename, file_get_contents($file_url)); // Guardar esto en la S3 del cliente correspondiente
        // Actualizar el ActivityActionField para que ahora apunte a la URL de la S3
        return response()->file(storage_path('app/' . $filename))->deleteFileAfterSend(true);
    }

    public function report(Request $req, $cpath, $date)
    {
        set_time_limit(600);

        $tmpDir    = "TMP" . uniqid();
        $dateQuery = $date . '%';
        $items     = ActivityActionField::with('activity_action.author')
            ->with('activity_action.activity')
            ->with('activity_action.activity.service')
            ->with('activity_action.activity.affiliate')
            ->where('created_at', 'LIKE', $dateQuery)
            ->where('action_field_id', 2)
            ->whereNotNull('value')
            ->get();

        $report = array();

        Storage::makeDirectory($tmpDir);

        foreach ($items as $item) {
            if ($item->activity_action == null) {
                continue;
            }

            if ($item->activity_action->author == null) {
                continue;
            }

            $report_item = array(
                'AUTOR'       => $item->activity_action->author->full_name,
                'TIPO_DOC'    => $item->activity_action->activity->affiliate->doc_type,
                'DOCUMENTO'   => $item->activity_action->activity->affiliate->doc_number,
                'SERVICIO'    => $item->activity_action->activity->service->name,
                'ID_SERVICIO' => $item->activity_action->activity->id,
                'ID_LLAMADA'  => $item->value,
                'STATUS'      => 'OK',
                'URL'         => null,
            );

            if (strpos($item->value, 'wav') !== false) {
                $id      = explode('/', $item->value);
                $idFinal = explode('.wav', $id[2]);

                $report_item['ID_LLAMADA'] = $idFinal[0];
                $report_item['URL']        = Storage::disk('s3')->url($item->value);
            } else {
                if (get_headers("http://************/callFinder/{$date}/{$item->value}")[0] == 'HTTP/1.1 200 OK'
                    || get_headers("http://************/callFinder/{$date}/{$item->value}")[0] == 'HTTP/1.0 200 OK') {

                    $url  = "http://************/callFinder/{$date}/{$item->value}";
                    $path = "calls/{$date}/{$item->value}.wav";

                    $tmp = storage_path('app/' . $tmpDir . "/{$item->value}.wav");

                    copy($url, $tmp);
                    $file    = fopen($tmp, 'r+');
                    $urlPath = Storage::disk('s3')->put($path, $file);
                    fclose($file);

                    if ($urlPath != null) {
                        $id = $item->value;

                        $item->value = $path;
                        $item->save();

                        $report_item['ID_LLAMADA'] = $id;
                        $report_item['URL']        = Storage::disk('s3')->url($path);
                    }
                } else {
                    $report_item['STATUS'] = 'ERROR';
                }
            }

            $report[] = $report_item;

        }

        Storage::deleteDirectory($tmpDir);

        Excel::create('REPORTE LLAMADAS ' . $date, function ($excel) use ($report) {

            // Set the title
            $excel->setTitle('Calls report');

            // Chain the setters
            $excel->setCreator('RENAPP')->setCompany('Ren Consultores');

            $excel->setDescription('Calls Status');

            $excel->sheet('Sheet 1', function ($sheet) use ($report) {
                $sheet->setOrientation('landscape');
                $sheet->fromArray($report);
            });

        })->download('xlsx');
    }

}
