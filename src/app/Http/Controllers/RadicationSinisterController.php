<?php

namespace App\Http\Controllers;

use App\Activity;
use App\ActivityActionDocument;
use App\ActivityDocument;
use App\Affiliate;
use App\Client;
use App\Template;
use App\Festivos;
use DateTime;
use App\RadicationSinister;
use App\Service;
use App\State;
use App\Action;
use Carbon\Carbon;
use DB;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Storage;
use PDF;

class RadicationSinisterController extends Controller
{
    public function radication(Request $req, $cpath)
    {
        $session = !(\Session::flash('flash_message_url_radication', "ok"));

        return view('radication_sinister_view/radication_siniestro', [
            'finish' => false,
            'session' => $session,
            'client' => $cpath,
        ]);
    }
    public function radicationDone(Request $req, $cpath, $id_radication,$id_sinister)
    {
        $link = RadicationSinister::query()
            ->where('activity_id', $id_radication)
            ->where('id', $id_sinister)
            ->first();
        $url = 'https://renapp-prod.s3.us-east-2.amazonaws.com/';
        if (App::environment('local')) {
            $url = 'https://renapp-dev.s3.us-east-2.amazonaws.com/';
        }
        return view('radication_sinister_view/radication_sinister_done', [
            'id' => $id_radication,
            'link' => $url . $link->doc_generate,
        ]);
    }
    public function saveRadication(Request $req, $cpath)
    {

        $client = Client::where('path', $cpath)->firstOrFail();

        $exists = RadicationSinister::where('doc_type_affiliate', '=', $req->input('doc_type_affiliate'))
            ->where('doc_number_affiliate', '=', $req->input('doc_number_affiliate'))
            ->where('created_at', '>=', date('Y-m-d H:i:s', time() - 60))
            ->get();
        if ($exists->count() == 0) {

            \Session::flash('flash_message_url_radication', "ok");

            // SEARCH AFFILIATE
            $affiliate = Affiliate::where('client_id', $client->id)->where('doc_number', $req->input('doc_number_affiliate'))->where('doc_type', $req->input('doc_type_affiliate'))->first();

            // IF NOT EXISTS AFFILIATE
            if (!$affiliate) {
                $session = !(\Session::flash('flash_message_url_radication', "ok"));
                return    view('radication_sinister_view/radication_sinister_no_affiliate', [
                    'finish' => false,
                    'session' => $session,
                    'client' => $cpath,
                ]);
            }
            $activity_1 = Activity::query()
                ->where('affiliate_id', $affiliate->id)
                ->where('service_id',Service::SERVICE_DEBTORS_INSURANCE_EXCEL)
                ->get();

            $ids = [];
            foreach ($activity_1 as $a_1) {
                $ids[] = $a_1->id;
            }
            $debtor = Template::query()
                ->whereIn('activity_id',$ids)
                ->where('policy_number',$req->input('policy_number'))
                ->where('credit_number',$req->input('credit_number'))
                ->first();

            // IF NOT EXISTS AFFILIATE
            if (!$debtor) {
                $session = !(\Session::flash('flash_message_url_radication', "ok"));
                return    view('radication_sinister_view/radication_sinister_no_debtor ', [
                    'finish' => false,
                    'session' => $session,
                    'client' => $cpath,
                ]);
            }

            $radication_sinister = new RadicationSinister;
            $radication_sinister->type_request = $req->input('type_request');
            $radication_sinister->doc_type_affiliate = $req->input('doc_type_affiliate');
            $radication_sinister->doc_number_affiliate = $req->input('doc_number_affiliate');
            $radication_sinister->first_name_affiliate = $req->input('first_name_affiliate');
            $radication_sinister->last_name_affiliate = $req->input('last_name_affiliate');
            $radication_sinister->credit_number = $req->input('credit_number');
            $radication_sinister->policy_number = $req->input('policy_number');
            $radication_sinister->last_name_affiliate = $req->input('last_name_affiliate');
            $radication_sinister->notifications = $req->input('notifications');
            $radication_sinister->notifications2 = $req->input('notifications2');
            $radication_sinister->radication_date = Carbon::now();
            if($radication_sinister->radication_date){
                $festivos = new Festivos(date('Y'));
                $date     = new DateTime();
                $count_days  = 0;
                $count_total_days = 0;
                do {
                    $date->modify('+1 day');
                    if ($date->format('N') <= 5 && !$festivos->esFestivo($date->format('d'), $date->format('n'))) {
                        $count_days++;
                    }
                    $count_total_days++;
                } while ($count_days<15);
                $radication_sinister->response_date           = \Carbon\Carbon::createFromFormat('Y-m-d', $radication_sinister->radication_date->formatLocalized('%Y-%m-%d'))->addDays($count_total_days)->formatLocalized('%Y-%m-%d');
            }
            $radication_sinister->save();


            $pdf = PDF::loadView('radication_sinister_view.docs.radication_sinister_pdf', ['radication_sinister' => $radication_sinister, 'watermark' => true]);

            $time = $radication_sinister->id;

            Storage::disk('s3')->put("radication_sinister/generate/{$radication_sinister->doc_number_affiliate}_{$time}.pdf", $pdf->output());

            if ($radication_sinister) {
                $radication_sinister->doc_generate = "radication_sinister/generate/{$radication_sinister->doc_number_affiliate}_{$time}.pdf";
                $radication_sinister->save();
            }

            $activity = Activity::query()->where('id',$debtor->activity_id)->first();

            $radication_sinister->activity_id = $activity->id;
            $radication_sinister->save();

            $activity_documets1 = new ActivityDocument;
            $activity_documets1->activity_id = $activity->id;
            $activity_documets1->path = $req->input('notifications');
            $activity_documets1->summary = "Soporte 1 cargado de radicacion";
            $activity_documets1->include_in_dictamen = 0;
            $activity_documets1->include_in_others = 1;
            $activity_documets1->physical = 0;
            $activity_documets1->uploaded_at = Carbon::now();
            $activity_documets1->save();

            $activity_documets2 = new ActivityDocument;
            $activity_documets2->activity_id = $activity->id;
            $activity_documets2->path = $req->input('notifications2');
            $activity_documets2->summary = $activity_documets2 ? "Soporte 2 cargado de radicacion" : '';
            $activity_documets2->include_in_dictamen = 0;
            $activity_documets2->include_in_others = 1;
            $activity_documets2->physical = 0;
            $activity_documets2->uploaded_at = Carbon::now();
            $activity_documets2->save();

            $activity_action = new \App\ActivityAction;
            $activity_action->activity_id = $activity->id;
            $activity_action->action_id = Action::SINISTER_REPORT;
            $activity_action->old_state_id = $activity->state_id;
            $activity_action->new_state_id = State::ADVISED_SINISTER;
            $activity_action->description = 'Se registra REPORTE DE SINIESTRO a partir de modulo radicación siniestro';
            $activity_action->old_user_id = $activity->user_id;
            $activity_action->new_user_id = $activity->user_id;

            $activity_action->author_id = 1;
            $activity_action->save();

            $activity->user_id = $activity_action->new_user_id;
            $activity->state_id = $activity_action->new_state_id;

            $activity->save();

            $activityActionDocument                     = new ActivityActionDocument;
            $activityActionDocument->activity_action_id = $activity_action->id;
            $activityActionDocument->name               ='sinister_radication_1';
            $activityActionDocument->path               = $req->input('notifications');
            $activityActionDocument->save();

            if ($radication_sinister->notifications2 != null) {
            $activityActionDocument2                     = new ActivityActionDocument;
            $activityActionDocument2->activity_action_id = $activity_action->id;
            $activityActionDocument2->name               ='sinister_radication_2';
            $activityActionDocument2->path               = $req->input('notifications2');
            $activityActionDocument2->save();
            }

            $activityActionDocument3                     = new ActivityActionDocument;
            $activityActionDocument3->activity_action_id = $activity_action->id;
            $activityActionDocument3->name               ='radication_sinister';
            $activityActionDocument3->path               = "radication_sinister/generate/{$radication_sinister->doc_number_affiliate}_{$time}.pdf";
            $activityActionDocument3->save();

            $req->merge([
                'action_id' => $activity_action->action_id,
            ]);

        }

        return redirect('/radicacion_siniestro/'.$activity->id.'/'.$radication_sinister->id, 302, [], true);
    }
}

