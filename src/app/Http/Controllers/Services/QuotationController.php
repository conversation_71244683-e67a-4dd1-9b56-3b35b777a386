<?php

namespace App\Http\Controllers\Services;

use App\Action;
use App\Actions\ActionCotizacionSort;
use App\Activity;
use App\ActivityAction;
use App\ActivityActionDocument;
use App\ActivityDocument;
use App\Affiliate;
use App\Client;
use App\EconomicActivity;
use App\EconomicBranch;
use App\Http\Controllers\ActionController;
use App\Http\Controllers\Controller;
use App\Http\Controllers\Integrations\WebserviceAcselController;
use App\Http\Controllers\Tables\MailBoardController;
use App\Http\Controllers\UtilsController;
use App\Mail\SendDocumentDataBase;
use App\MailBoard;
use App\PolicySort;
use App\Providers\AppServiceProvider;
use App\Quotation;
use App\QuotationConditionSpecial;
use App\Service;
use App\State;
use App\States\StatePoliza;
use App\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use DateTime;

class QuotationController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth')->except([
            'sendGeneratedQuoteEmailAsync'
        ]);
    }

    /**
     * @param Request $req
     * @param $cpath
     * @param $id id de la actividad
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\Foundation\Application|\Illuminate\View\View
     */
    public function index(Request $req, $cpath, $id)
    {
        Client::query()->where('path', $cpath)->firstOrFail();
        return view('services.quotation.menu.agent-data', compact('id'));
    }

    public function createQuotation(Request $req, $cpath)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        DB::beginTransaction();
        try {
            $activity = Activity::where('service_id', Service::SERVICE_QUOTATIONS_MNK)
                ->where('state_id', State::REGISTRADO)
                ->where('client_id', $client->id)
                ->where('user_id', Auth::id())
                ->first();
            if (!$activity) {
                $activity = new Activity;
                $activity->client_id = $client->id;
                $activity->service_id = Service::SERVICE_QUOTATIONS_MNK;
                $activity->affiliate_id = 1;
                $activity->user_id = Auth::id();
                $activity->state_id = State::REGISTRADO;
                $activity->save();

                $quotation = new Quotation;
                $quotation->activity_id = $activity->id;
                $quotation->save();
            }
            DB::commit();
            return response()->json(['id' => $activity->id]);
        } catch (\Exception $e) {
            DB::rollBack();
            // Retornar un mensaje de error o manejar la excepción
            return response()->json([
                'error' => 'Ocurrió un error al guardar los datos',
                'code' => $e->getCode(),
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function showIntermediary(Request $req, $cpath, $id)
    {
        Client::query()->where('path', $cpath)->firstOrFail();
        $activity = Activity::query()->where('id', $id)->firstOrFail();
        $quotation = $activity->quotation;
        if (!$activity->quotation) {
            $quotation = new Quotation();
            $quotation->activity_id = $id;
            $quotation->save();
        }

        return view('services.quotation.step.intermediary_data', compact('id', 'quotation'));
    }

    public function showPolicyholder(Request $req, $cpath, $id)
    {
        Client::query()->where('path', $cpath)->firstOrFail();
        $activity = Activity::query()->where('id', $id)->firstOrFail();
        $affiliate = Affiliate::where('id', '!=', 1)->find($activity->affiliate_id);
        $quotation = Quotation::where('activity_id', $id)->first();
        $jsonSource = ($quotation->economic_activity == 'public') ? '/js/economic_activity/public.json' : '/js/economic_activity/private.json';
        $json = file_get_contents(public_path($jsonSource));
        $economicActivities = json_decode($json, true);
        $activity_economic_name = collect($economicActivities)->firstWhere('CODE', $quotation->activity_economic_id)['ACTIVITY_NAME'];

        $economicBranch = EconomicBranch::all();

        if (!$quotation) {
            $quotation = new Quotation();
            $quotation->activity_id = $id;
            $quotation->save();
        }
        return view('services.quotation.step.policy_holder', compact('id', 'affiliate', 'quotation', 'activity_economic_name', 'economicBranch'));
    }

    function titleCaseSpanish($string)
    {
        $minWords = ['y', 'e', 'o', 'ni', 'de', 'del', 'la', 'las', 'los', 'el', 'en', 'con', 'a', 'por', 'para', 'al'];
        $words = explode(' ', strtolower($string));

        foreach ($words as $i => $word) {
            if ($i === 0 || !in_array($word, $minWords)) {
                $words[$i] = ucfirst($word);
            }
        }

        return implode(' ', $words);
    }

    public function dataEconomicActivities(Request $request)
    {
        $isPublic = $request->get('sector') === 'public' ? 1 : 0;
        $branch = $request->get('branch');
        $work_risk = $request->get('work_risk');

        $activities = EconomicActivity::where('is_public', $isPublic)
            ->when($branch, function ($query, $branch) {
                $query->where('branch_id', $branch);
            })
            ->when($work_risk == 3, function ($query) {
                $query->whereIn('code', ['9700', '8129']);
            })
            ->get(['code as CODE', 'activity_name as ACTIVITY_NAME', 'rounded_percentage as PERCENTAGE', 'branch_id']);

        return response()->json($activities);
    }

    public function showQuoteDetails(Request $req, $cpath, $id)
    {
        Client::query()->where('path', $cpath)->firstOrFail();
        Activity::query()->where('id', $id)->firstOrFail();
        $quotation = Quotation::where('activity_id', $id)->first();
        if (!$quotation) {
            $quotation = new Quotation();
            $quotation->activity_id = $id;
            $quotation->save();
        }

        return view('services.quotation.step.quote_details', compact('id', 'quotation'));
    }


    /**
     * @param $salary_projection
     * @param $temporality
     * @param $validity_from
     * @param $validity_to
     * @param $sector
     * @param $activityEconomicId
     * @return array
     * @throws \DateMalformedStringException
     * @throws \Exception
     */
    public static function calculatePolicyPrice($policySort, $specialCondition = true)
    {
        $webserviceController = new WebserviceAcselController();

        $minDolares = 100;
        $minColones = $webserviceController->convertDollarsToColones($minDolares);
        $min = $policySort->type_currency == 'USD' ? $minDolares : $minColones;
        $salary_projection = $policySort->salary_projection;
        $validity_from = $policySort->validity_from;
        $validity_to = $policySort->validity_to;
        $sector = $policySort->economic_activity;
        $activityEconomicId = $policySort->activity_economic_id;

        $salary_projection = floatval($salary_projection);
        $validityFrom = new DateTime($validity_from);
        $validityTo = new DateTime($validity_to);
        $dias = $validityTo->diff($validityFrom)->days;
        $months = $dias / 30;
        // Simulación de los datos de actividad económica
        $percentage = QuotationController::getActivityPercentage($sector, $activityEconomicId); // Función para obtener el porcentaje
        // Formato del porcentaje
        $percentage = str_replace(',', '.', $percentage);
        // $percentage = ($percentage / 100);
        $percentage = floatval($percentage);

        // Cálculo de primas según la temporalidad
        $amountPolicy = $salary_projection * 12 * ($percentage / 100);

        $temSemestral = round(($percentage * 1.04), 2);
        $temTrimestral = round(($percentage * 1.06), 2);
        $temMensual = round(($percentage * 1.08), 2);

        $amountPolicySemestral = $salary_projection * 12 *  ($temSemestral / 100)  / 2;   
        $amountPolicyTrimestral = $salary_projection * 12 * ($temTrimestral / 100)  / 4;
        $amountPolicyMensual = $salary_projection * 12 *    ($temMensual / 100) / 12;

        // Cálculo de prima semestral, trimestral y mensual

        if (isset($policySort->change_date) && $policySort->change_date > 0 && $policySort->change_date < 364) {
            $amountPolicy = ($amountPolicy / 365) * $dias;
            $amountPolicySemestral = ($amountPolicySemestral / 365) * $dias;
            $amountPolicyTrimestral = ($amountPolicyTrimestral / 365) * $dias;
            $amountPolicyMensual  = ($amountPolicyMensual / 365) * $dias;
        }


        if ($specialCondition) {
            //Sy la condicion especial es exclusion de francionamiento o ambas excluimos las TM
            if ($policySort->special_condition === "1") {
                $amountPolicySemestral = $amountPolicy / 2;
                $amountPolicyTrimestral = $amountPolicy / 4;
                $amountPolicyMensual = $amountPolicy / 12;
            }


            // si viene con descuento le aplicamos el descuento
            if ($policySort->special_condition === "2") {
                $discount = $policySort->preventive_actions / 100;

                $amountPolicy =  $amountPolicy - ($amountPolicy * $discount);

                $amountPolicySemestral = $amountPolicy / 2;
                $amountPolicyTrimestral = $amountPolicy / 4;
                $amountPolicyMensual = $amountPolicy  / 12;
            }

            //quito el fraccionamiento y aplico el descuento
            if ($policySort->special_condition === "3") {
                $discount = $policySort->preventive_actions / 100;

                $amountPolicy =  $amountPolicy - ($amountPolicy * $discount);
                $amountPolicySemestral = $amountPolicy / 2;
                $amountPolicyTrimestral = $amountPolicy / 4;
                $amountPolicyMensual = $amountPolicy / 12;
            }
        }


        $temAnual = number_format(round($percentage, 2), 2, ',', '');
        $temSemestral = number_format(round(($percentage * 1.04), 2), 2, ',', '');
        $temTrimestral = number_format(round(($percentage * 1.06), 2), 2, ',', '');
        $temMensual = number_format(round(($percentage * 1.08), 2), 2, ',', '');

        if ($months <= 3) {
            $pUnico = 1.08;
        } elseif ($months <= 6) {
            $pUnico = 1.06;
        } else {
            $pUnico = 1.04;
        }
        $temUnico = number_format(round(($percentage * $pUnico), 2), 2, ',', '');

        //calculamos la diferencia de dias para el pago del periodo corto 
        $recargo = 1;

        if ($dias >= 1 && $dias <= 90) {

            // Si tiene condciion especial en 1 0 3 no cobramos recargo
            if ($policySort->special_condition === "1" || $policySort->special_condition === "3") {
                $recargo = 1;
            } else {
                $recargo = 1.08;
            }
        };

        if ($dias > 90 && $dias <= 180) {

            // Si tiene condciion especial en 1 0 3 no cobramos recargo
            if ($policySort->special_condition === "1" || $policySort->special_condition === "3") {
                $recargo = 1;
            } else {
                $recargo = 1.06;
            }
        };

        if ($dias > 180 && $dias <= 364) {

            // Si tiene condciion especial en 1 0 3 no cobramos recargo
            if ($policySort->special_condition === "1" || $policySort->special_condition === "3") {
                $recargo = 1;
            } else {
                $recargo = 1.04;
            }
        };

        $valorUnico = ($salary_projection * ($percentage / 100)) * $recargo;

        //Formula periodo corto (salario proyectado x el porcentaje actividad economica * recargo)
        if ($policySort->special_condition === "2" || $policySort->special_condition === "3") {

            $discount = $policySort->preventive_actions / 100;

            $valorUnico = $valorUnico - ($valorUnico * $discount);
        }


        if ($valorUnico < $min) {
            $valorUnico = $min;
        }
        if ($amountPolicy < $min) {
            $amountPolicy = $min;
        }
        if ($amountPolicySemestral < $min) {
            $amountPolicySemestral = $min;
        }
        if ($amountPolicyTrimestral < $min) {
            $amountPolicyTrimestral = $min;
        }
        if ($amountPolicyMensual < $min) {
            $amountPolicyMensual = $min;
        }

        $policySort->anual_percentage = $temAnual;
        $policySort->semestral_percentage = $temSemestral;
        $policySort->trimestral_percentage = $temTrimestral;
        $policySort->mensual_percentage = $temMensual;
        $policySort->unico_percentage = $temUnico;

        $policySort->save();

        $activity_police = Activity::with(['parent_activity'])->where('id', $policySort->activity_id)->where('service_id', Service::SERVICE_POLICY_SORT_MNK)->first();

        if ($activity_police) {
            $quotation_activity = $activity_police->parent_activity;
            $condition = ActivityAction::where('action_id', '=', ActionCotizacionSort::REPORTAR_CONDICIONES_ESPECIALES)->where('activity_id', '=', $quotation_activity->id)->first();

            if ($condition) {
                $quotation_condition_special = QuotationConditionSpecial::where('activity_id', $quotation_activity->id)->first();


                $amountPolicy = $quotation_condition_special->anual_final;
                $amountPolicySemestral = $quotation_condition_special->semianual_fraccionada;
                $amountPolicyTrimestral = $quotation_condition_special->trimestral_fraccionada;
                $amountPolicyMensual = $quotation_condition_special->mensual_fraccionada;

                if ($quotation_condition_special->anual_continuidad) {

                    $temAnual = $quotation_condition_special->anual_continuidad;
                    $temSemestral = $quotation_condition_special->semianual_continuidad;
                    $temTrimestral = $quotation_condition_special->trimestral_continuidad;
                    $temMensual = $quotation_condition_special->mensual_continuidad;

                }
            }
        }

        return [
            'amountPolicy' => $amountPolicy,
            'amountPolicySemestral' => $amountPolicySemestral,
            'amountPolicyTrimestral' => $amountPolicyTrimestral,
            'amountPolicyMensual' => $amountPolicyMensual,
            'valorUnico' => $valorUnico,
            'percentage' => $percentage,
            'dias' => $dias,
            'temAnual' => $temAnual,
            'temSemestral' => $temSemestral,
            'temTrimestral' => $temTrimestral,
            'temMensual' => $temMensual,
            'temUnico' => $temUnico
        ];
    }

    /**
     * @throws \DateMalformedStringException
     */
    public function showPolicyCalculation(Request $req, $cpath, $id)
    {
        // Validar existencia del cliente y la actividad
        Client::query()->where('path', $cpath)->firstOrFail();
        $activity = Activity::where('id', $id)->firstOrFail();
        $quotation = Quotation::where('activity_id', $id)->first();


        if (!$quotation) {
            $quotation = new Quotation();
            $quotation->activity_id = $id;
            $quotation->save();
        }
        $errors = [];

        if (!$activity->affiliate_id == 1) {
            $errors[] = 'Debe ingresar los datos del tomador';
        }

        if (!$quotation->economic_activity || !$quotation->activity_economic_id) {
            $errors[] = 'Debe seleccionar una actividad económica';
        }

        if (!$quotation->type_currency) {
            $errors[] = 'Debe seleccionar una moneda';
        }

        if ($quotation->work_modality_id != 3 && $quotation->work_modality_id != 4) {
            if (!$quotation->salary_projection) {
                $errors[] = 'Debe ingresar una proyección salarial';
            }
        }

        if (!$quotation->temporality) {
            $errors[] = 'Debe ingresar temporalidad';
        }

        if (!$quotation->validity_from) {
            $errors[] = 'Debe seleccionar una fecha de inicio';
        }

        if (!$quotation->validity_to) {
            $errors[] = 'Debe seleccionar una fecha de fin';
        }

        if (!filter_var($quotation->email, FILTER_VALIDATE_EMAIL)) {
            $errors[] = 'Debe ingresar un correo electrónico válido del intermediario';
        }

        if (!filter_var($quotation->notification_email, FILTER_VALIDATE_EMAIL)) {
            $errors[] = 'Debe ingresar un correo electrónico válido del tomador';
        }

        $temporality = '';
        if (count($errors)) {
            return view('services.quotation.step.policy_calculation', compact('id', 'errors', 'temporality'));
        }

        // Simulación de datos obtenidos desde la base de datos o APIs (ajusta según sea necesario)
        $currency = $quotation->type_currency;
        // Preparar los valores formateados para la vista
        $currencySymbol = $currency === 'USD' ? '$' : '₡';
        //tipo de temporalidad
        $result = QuotationController::calculatePolicyPrice($quotation);

        //Si la modalidad de aseguramiento es distina a Riesgos del trabajo ocasionl y hogar.
        if ($quotation->work_modality_id != 3 && $quotation->work_modality_id != 4) {
            $quotation->amount_policy = $result['amountPolicy'];
        }

        //Si la modalidad de aseguramiento es igual a riesgos del trabajo hogar
        if ($quotation->work_modality_id == 3) {
            $values = [
                1 => ['USD' => 131, 'CRC' => 67581],
                2 => ['USD' => 246, 'CRC' => 126771],
                3 => ['USD' => 361, 'CRC' => 185961]
            ];

            $quotation->amount_policy = $values[$quotation->option_asegurement][$quotation->type_currency === 'USD' ? 'USD' : 'CRC'];
        }

        //Si la modalidad de aseguramiento es igual a riesgos del trabajo ocasional
        if ($quotation->work_modality_id == 4) {

            $new_amount_policy = 0;

            // Si son colones convertimos los 100 usd a colones con la trm del dia 
            if ($quotation->type_currency == 'CRC') {

                //Traemos la trm del dia
                $webserviceController = new WebserviceAcselController();
                $trm = $webserviceController->getTrm();

                if ($trm == 0) {
                    throw new \Exception('Error: El TRM no es valido');
                }

                $new_amount_policy =  100 * $trm;
            }

            $values = [
                1 => ['USD' => 100, 'CRC' => $new_amount_policy],
            ];

            $quotation->amount_policy = $values[1][$quotation->type_currency === 'USD' ? 'USD' : 'CRC'];
        }

        //Si la modalidad de aseguramiento es distina a Riesgos del trabajo ocasionl y hogar.
        if ($quotation->work_modality_id != 3 && $quotation->work_modality_id != 4) {
            $quotation->annual_calculation_amount = $result['amountPolicy'];
        }

        //Si la modalidad de aseguramiento es igual a riesgos del trabajo hogar
        if ($quotation->work_modality_id == 3) {
            $values = [
                1 => ['USD' => 131, 'CRC' => 67581],
                2 => ['USD' => 246, 'CRC' => 126771],
                3 => ['USD' => 361, 'CRC' => 185961]
            ];

            $quotation->annual_calculation_amount = $values[$quotation->option_asegurement][$quotation->type_currency === 'USD' ? 'USD' : 'CRC'];
        }

        //Si la modalidad de aseguramiento es igual a riesgos del trabajo ocasional
        if ($quotation->work_modality_id == 4) {

            $new_amount_policy = 0;

            // Si son colones convertimos los 100 usd a colones con la trm del dia 
            if ($quotation->type_currency == 'CRC') {

                //Traemos la trm del dia
                $webserviceController = new WebserviceAcselController();
                $trm = $webserviceController->getTrm();

                if ($trm == 0) {
                    throw new \Exception('Error: El TRM no es valido');
                }

                $new_amount_policy =  100 * $trm;
            }

            $values = [
                1 => ['USD' => 100, 'CRC' => $new_amount_policy],
            ];

            $quotation->annual_calculation_amount = $values[1][$quotation->type_currency === 'USD' ? 'USD' : 'CRC'];
        }

        //Si la modalidad de aseguramiento es distina a Riesgos del trabajo ocasionl y hogar.
        if ($quotation->work_modality_id != 3 && $quotation->work_modality_id != 4) {

            if ($quotation->option_asegurement != 1) {
                $quotation->semiannual_calculation_amount = $result['amountPolicySemestral'];
            } else {
                $quotation->semiannual_calculation_amount = 0;
            }
        }

        //Si la modalidad de aseguramiento es igual a riesgos del trabajo ocasional
        if (in_array($quotation->work_modality_id, [1, 2, 5, 6, 7])) {
            $quotation->semiannual_calculation_amount = $result['amountPolicySemestral'];
        }

        //Si la modalidad de aseguramiento es igual a riesgos del trabajo hogar
        if ($quotation->work_modality_id == 3) {
            $values = [
                2 => ['USD' => 128, 'CRC' => 65921],
                3 => ['USD' => 188, 'CRC' => 96700]
            ];

            $currency = $quotation->type_currency === 'USD' ? 'USD' : 'CRC';
            $quotation->semiannual_calculation_amount = $values[$quotation->option_asegurement][$currency] ?? 0;
        }

        //Si la modalidad de aseguramiento es igual a riesgos del trabajo ocasional
        if ($quotation->work_modality_id == 4) {
            $quotation->semiannual_calculation_amount =  0;
        }

        //Si la modalidad de aseguramiento es distina a Riesgos del trabajo ocasionl y hogar.
        if ($quotation->work_modality_id != 3 && $quotation->work_modality_id != 4) {
            $quotation->quarterly_calculation_amount = $result['amountPolicyTrimestral'];
        } else {
            $quotation->quarterly_calculation_amount = 0;
        }

        //Si la modalidad de aseguramiento es distina a Riesgos del trabajo ocasionl y hogar.
        if ($quotation->work_modality_id != 3 && $quotation->work_modality_id != 4) {
            $quotation->monthly_calculation_amount = $result['amountPolicyMensual'];
        } else {
            $quotation->monthly_calculation_amount = 0;
        }

        $quotation->single_payment_value = $result['valorUnico'];
        $quotation->save();

        if ($activity->state_id == State::REGISTRADO) {
            ActionController::create($id, Action::SOLICITAR_COTIZACION, 'Cotizacion solicitada');
        }

        $specialConditionsButton = true;
        if ($quotation->work_modality_id == 3 || $quotation->work_modality_id == 4) {
            $specialConditionsButton = false;
        }

        if ($quotation->activity->activity_actions->where('action_id', 355)->isNotEmpty()) {
            $specialConditionsButton = false;
        }

        //Si la cotización es de periodo corto
        if ($quotation->temporality == 'short') {
            $specialConditionsButton = false;
        }

        $salaryProjection = $quotation->salary_projection*12 ;

        if ($quotation->type_currency == 'USD') {

            $webserviceController = new WebserviceAcselController();
            $trm = $webserviceController->getTrm();

            $salaryProjection = $salaryProjection*$trm; //dolares a colones
        }

        // si los valores son iguales indica prima minima es menor igual a 100 dolares
        $equalCalculations = $quotation->annual_calculation_amount == $quotation->semiannual_calculation_amount
                            && $quotation->semiannual_calculation_amount == $quotation->quarterly_calculation_amount;

        if ($salaryProjection < 21000000 || $equalCalculations){

            $specialConditionsButton = false;
        }

        return view('services.quotation.step.policy_calculation', [
            'id' => $id,
            'quotation' => $quotation,
            'currencySymbol' => $currencySymbol,
            'anual' => number_format($quotation->annual_calculation_amount, 2, ',', '.'),
            'semestral' => number_format($quotation->semiannual_calculation_amount, 2, ',', '.'),
            'trimestral' => number_format($quotation->quarterly_calculation_amount, 2, ',', '.'),
            'mensual' => number_format($quotation->monthly_calculation_amount, 2, ',', '.'),
            'unico' => number_format($quotation->single_payment_value, 2, ',', '.'),
            'temporality' => $quotation->temporality,
            'errors' => [],
            'temAnual' => $result['temAnual'],
            'temSemestral' => $result['temSemestral'],
            'temTrimestral' => $result['temTrimestral'],
            'temMensual' => $result['temMensual'],
            'temUnico' => $result['temUnico'],
            'specialConditionsButton' => $specialConditionsButton,
            'info' => 'La prima a pagar debe ser mayor a los $100 o a su equivalente en colones.',
        ]);
    }

    private static function getActivityPercentage($sector, $activityEconomicId)
    {
        if ($sector === 'public') {
            // Ruta del archivo JSON en la carpeta public
            $jsonPath = public_path('js/economic_activity/public.json');
        } elseif ($sector === 'private') {
            // Ruta del archivo JSON en la carpeta private
            $jsonPath = public_path('js/economic_activity/private.json');
        } else {
            throw new \Exception('Sector no encontrado');
        }
        // Leer y decodificar el contenido del archivo JSON
        if (file_exists($jsonPath)) {
            $jsonContent = file_get_contents($jsonPath);
            $activities = json_decode($jsonContent, true);

            // Buscar la actividad económica por código
            foreach ($activities as $activity) {
                if ($activity['CODE'] == $activityEconomicId) {
                    // Retornar el porcentaje de la actividad encontrada
                    $percentage = str_replace(',', '.', $activity['PERCENTAGE']);
                    return number_format($percentage, 8);
                }
            }
        }

        throw new \Exception('Actividad económica no encontrada');
    }

    public function form(Request $req, $cpath, $id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();
        $quotation = Quotation::where('activity_id', $id)->first();
        $affiliate = Affiliate::find($activity->affiliate_id);
        $activity_documents = ActivityDocument::where('activity_id', $activity->id)->get();
        $quotation_condition_special = QuotationConditionSpecial::where('activity_id', $activity->id)->get();

        //nombre de la actividad economica
        $jsonSource = ($quotation->economic_activity == 'public') ? '/js/economic_activity/public.json' : '/js/economic_activity/private.json';
        $json = file_get_contents(public_path($jsonSource));
        $economicActivities = json_decode($json, true);
        $activity_economic_name = collect($economicActivities)->firstWhere('CODE', $quotation->activity_economic_id)['ACTIVITY_NAME'];

        $documents = [];
        foreach ($activity_documents as $doc) {
            $documents[$doc->document_id] = $doc;
        }

        $economicActivity = EconomicActivity::where('code', $quotation->activity_economic_id)->first();

        return view('services.quotation.form', [
            'activity' => $activity,
            'quotation' => $quotation,
            'affiliate' => $affiliate,
            'documents' => $documents,
            'quotation_condition_special' => $quotation_condition_special,
            'activity_economic_name' => $activity_economic_name,
            'economicActivity' => $economicActivity
        ]);
    }

    public function generate(Request $req, $cpath, $id)
    {
        Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('id', $id)->firstOrFail();

        $economicActivity = EconomicActivity::where('code', $activity->quotation->activity_economic_id)->first();

        return view('services.quotation.quotation_view', [
            'activity' => $activity,
            'quotation' => $activity->quotation,
            'affiliate' => $activity->affiliate,
            'hidden_contacto' => true,
            'economicActivity' => $economicActivity
        ]);
    }

    // public function generateActionCondition(Request $req, $cpath, $id)
    // {
    //     $client = Client::where('path', $cpath)->firstOrFail();

    //     \DB::beginTransaction();

    //     try {

    //         //Generamos la acción solictar condiciones especiales 
    //         $description = "se ejecuto la acción solicitar condiciones especiales";

    //         ActionController::create(
    //             $req->id,
    //             ActionCotizacionSort::SOLICITAR_CONDICIONES_ESPECIALES,
    //             $description
    //         );


    //         $quotation = Quotation::where('activity_id', $req->id)->first();

    //         //enviamos correo al intermediario 
    //         $subject = "Análisis de condiciones especiales requerido para cotización $quotation->id";
    //         $fecha = $quotation->created_at
    //             ? date('d/m/Y', strtotime($quotation->created_at))
    //             : '';

    //         $text = "Estimado/a Aseguramiento SORT, Por medio de este correo, le notificamos que hemos identificado una solicitud del intermediario $quotation->advisor_name para el análisis de condiciones especiales asociada a la cotización número $quotation->id de fecha $fecha.

    //         Para poder avanzar con este trámite, solicitamos su colaboración en la revisión y evaluación de los requerimientos específicos detallados en la solicitud, la cual encontrara en su bandeja de trabajo.
    //         ";

    //         $emails = config('app.env') == 'prod'  ? ['<EMAIL>', '<EMAIL>', '<EMAIL>'] : ['<EMAIL>', '<EMAIL>', '<EMAIL>'];


    //         // Enviar el correo a prevencion
    //         $mailSent = new SendDocumentDataBase(
    //             implode(',', $emails),
    //             $subject,
    //             "<EMAIL>",
    //             "Condiciones especiales",
    //             [
    //                 "text" => $text,
    //                 "sender" => 'MNK seguros'
    //             ],
    //             "<EMAIL>",
    //             [],
    //             "send_generic_document",
    //             $client,
    //             $req->host,
    //             $req->id,
    //             ActionCotizacionSort::SOLICITAR_CONDICIONES_ESPECIALES
    //         );

    //         $mailSent->sendMail();

    //         \DB::commit();

    //         return response()->json([
    //             'status' => 200,
    //             'response' => true,
    //             'message' => '¡Esta solicitud se envió a MNK!',
    //         ]);
    //     } catch (\Exception $e) {

    //         \DB::rollback();

    //         return response()->json([
    //             'error' => 'Ocurrió un error',
    //             'code' => $e->getCode(),
    //             'message' => $e->getMessage()
    //         ], 500);
    //     }
    // }

    // public function saveActionConditionSpecial(Request $req, $id)
    // {


    //     DB::beginTransaction();

    //     try {


    //         //Guardamos los datos nuevos de la cotzación
    //         $quotation = Quotation::where('activity_id', $req->id)->first();

    //         $activity = Activity::find($req->id);

    //         $quotation->special_condition = $req->special_condition;
    //         $quotation->preventive_actions = $req->preventive_actions;
    //         $quotation->save();

    //         $result = QuotationController::calculatePolicyPrice($quotation);
    //         $quotationCondition = QuotationConditionSpecial::where('activity_id', $req->id)->first();

    //         QuotationConditionSpecial::create([
    //             "activity_id" =>  $activity->id,
    //             "special_condition"         => $req->special_condition,
    //             "special_condition_payment" => $req->special_condition_payment,
    //             "preventive_actions"        => $req->preventive_actions,
    //             "amount_policy_before"      => $quotation->temporality == 'short' ? $quotationCondition ? $quotationCondition->amount_policy_before : $quotation->single_payment_value : $quotation->amount_policy,
    //             "amount_policy"             => $quotation->temporality == 'short' ? $result['valorUnico'] : $result['amountPolicy'],
    //             "annual_calculation_amount" => $result['amountPolicy'],
    //             "semiannual_calculation_amount" => $result['amountPolicySemestral'],
    //             "quarterly_calculation_amount"  => $result['amountPolicyTrimestral'],
    //             "monthly_calculation_amount" => $result['amountPolicyMensual'],
    //             "single_payment_value"   => $result['valorUnico']
    //         ]);

    //         //actualizamos los registros de la cotización
    //         $quotation->update([
    //             "special_condition_payment" => $req->special_condition_payment
    //         ]);


    //         //Generamos la acción reportar condiciones especiales 
    //         $description = "se ejecuto la acción reportar condiciones especiales";

    //         $newAmountFormatted = 0;

    //         $currencySymbols = [
    //             'USD' => '$',
    //             'CRC' => '₡',
    //         ];

    //         $currencySymbol = $currencySymbols[$quotation->type_currency] ?? '';

    //         $newAmountFormatted = $currencySymbol . number_format(
    //             $quotation->temporality === "short" ? $result['valorUnico']  : $result['amountPolicy'],
    //             2,
    //             ',',
    //             '.'
    //         );

    //         //enviamos correo al intermediario 
    //         $subject = "Condiciones especiales para la cotización N°  {$quotation->id}";
    //         $text = "Estimado/a {$quotation->advisor_name}, se ha actualizado el cálculo de la prima para la cotización número {$activity->quotation->id}, del cliente {$activity->affiliate->first_name}. La nueva prima para pagar es de {$newAmountFormatted}. Saludos.!";

    //         // Enviar el correo a prevencion
    //         $mailSent = new SendDocumentDataBase(
    //             $quotation->email,
    //             $subject,
    //             "<EMAIL>",
    //             "Condiciones especiales",
    //             [
    //                 "text" => $text,
    //                 "sender" => 'MNK seguros'
    //             ],
    //             "<EMAIL>",
    //             [],
    //             "send_generic_document",
    //             $activity->client,
    //             $req->host,
    //             $activity->id,
    //             ActionCotizacionSort::REPORTAR_CONDICIONES_ESPECIALES
    //         );

    //         $mailSent->sendMail();

    //         ActionController::create(
    //             $req->id,
    //             ActionCotizacionSort::REPORTAR_CONDICIONES_ESPECIALES,
    //             $description
    //         );

    //         DB::commit();

    //         return response()->json([
    //             'status' => 200,
    //             'response' => true,
    //             'message' => '¡Se generó la acción de forma correcta!',
    //         ]);
    //     } catch (\Exception $e) {
    //         DB::rollback();

    //         return response()->json([
    //             'error' => 'Ocurrió un error al guardar los datos',
    //             'code' => $e->getCode(),
    //             'message' => $e->getMessage()
    //         ], 500);
    //     }
    // }

    public function generatePdf(Request $req, $id)
    {
        // Buscar el cliente y la actividad
        $activity = Activity::where('id', $req->id)->firstOrFail();
        //Json de actividades economica
        $jsonSource = ($activity->quotation->economic_activity == 'public') ? '/js/economic_activity/public.json' : '/js/economic_activity/private.json';
        $json = file_get_contents(public_path($jsonSource));
        $economicActivities = json_decode($json, true);

        //Se transforma a una collección en laravel
        $activity_economic_name = collect($economicActivities)->firstWhere('CODE', $activity->quotation->activity_economic_id)['ACTIVITY_NAME'];

        $activity_policy = Activity::where('parent_id', $activity->id)->where('service_id', Service::SERVICE_POLICY_SORT_MNK)->first();
        $work_modality_id = '';
        if ($activity_policy) {
            $work_modality_id = $activity_policy->policy_sort->work_modality_id;
        }

        //capturar acción "Solicitar cotización" de la actividad cotización, la acción más antigua
        $actionCreatedAt = ActivityAction::where('action_id', Action::SOLICITAR_COTIZACION)
            ->where('activity_id', $activity->id)
            ->orderBy('created_at', 'asc')->first();

        //declarar valores para las fechas: Fecha de cotización
        $quotation_date = $actionCreatedAt->created_at ?? "";

        // Preparar los datos para la vista
        $data = [
            'activity' => $activity,
            'quotation' => $activity->quotation,
            'affiliate' => $activity->affiliate,
            'quotation_date' => $quotation_date,
            'hidden_contacto' => true,
            'activity_economic_name' => $activity_economic_name,
            'work_modality_id' => $work_modality_id
        ];

        // Renderizar la vista como PDF
        $pdf = \PDF::loadView('services.plantilla.docs.cotizaciones_resume_pdf', $data);

        // Opción 1: Descargar el PDF
        return $pdf->stream('Cotización_#_' . $activity->id . '.pdf');
    }

    public function getQuotations(Request $req, $cpath, $id)
    {
        $quotation = Quotation::where('activity_id', $id)->select('brokerage_name', 'advisor_name', 'code', 'email')->firstOrFail();
        return response()->json($quotation);
    }

    public function getQuotationsCal(Request $req, $cpath, $id)
    {
        $quotation = Quotation::where('activity_id', $id)
            ->select(
                'id',
                'activity_economic_id',
                'salary_projection',
                'type_currency',
                'validity_from',
                'validity_to',
                'temporality'
            )
            ->firstOrFail();
        return response()->json($quotation);
    }

    public function save(Request $req, $cpath, $id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();

        //Buscamos la actividad asociada que viene por parametro
        $activity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();

        \DB::beginTransaction();
        try {
            if (!$activity->quotation) {
                $quotation = new Quotation();
                $quotation->activity_id = $id;
            } else {
                $quotation = $activity->quotation;
            }
            $quotation->save();
            \DB::commit();
        } catch (\Exception $e) {
            \DB::rollback();
        }

        return redirect("servicio/$id/quotation");
    }

    public function save_intermediary(Request $req, $cpath)
    {
        DB::beginTransaction();
        try {
            $activity = Activity::where('id', $req->input('activity_id'))->firstOrFail();

            $activity->quotation->brokerage_name = $req->input('brokerage_name');
            $activity->quotation->advisor_name = $req->input('advisor_name');
            $activity->quotation->code = $req->input('code');
            $activity->quotation->email = $req->input('email');
            $activity->quotation->save();

            $action = $activity->activity_actions->where('id', ActionCotizacionSort::INICIAR_COTIZACION)->first();
            if ($action) {
                ActionController::create(
                    $req->input('activity_id'),
                    ActionCotizacionSort::INICIAR_COTIZACION,
                    'Cotización iniciada'
                );
            }

            DB::commit();
            return response()->json([
                'status' => 200,
                'response' => true,
                'message' => 'Registro actualizado exitosamente',
            ]);
        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'status' => 500,
                'response' => false,
                'message' => $e->getMessage(),
            ]);
        }
    }

    public function save_poliza(Request $req, $cpath, $id)
    {
        if ($req->activity_id) {
            $updated = Quotation::where('activity_id', $req['activity_id'])
                ->update([
                    'amount_policy' => $req['amount_policy'],
                    'periodicity' => $req['periodicity'],
                ]);

            if ($updated) {
                return response()->json([
                    'status' => 200,
                    'response' => true,
                    'message' => 'Registro actualizado exitosamente',
                ]);
            } else {
                return response()->json([
                    'status' => 404,
                    'response' => false,
                    'message' => 'Registro no encontrado',
                ]);
            }
        }
    }

    public function pdf(Request $req, $cpath, $id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();

        $quotation = new Quotation();
        $quotation->activity_id = $id;
        $quotation->setRelation('activity', $activity);
        $quotation->created_at = new \DateTime();

        $pdf = \PDF::loadView('services.quotation.docs.quotation_pdf', [
            'quotation' => $quotation,
            'watermark' => false,
            'activity' => $activity
        ]);

        return $pdf->stream('preview.pdf');
    }

    /**
     * Metodo para actualizar los datos de la cotización 1 a 1 dinamicamente
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request)
    {


        try {
            // Validar datos
            $validateData = $request->validate(['activity_id' => 'required|string']);
            $activity_id = $validateData['activity_id'];
            $activity = Activity::where('id', $activity_id)->firstOrFail();

            // Buscar el registro
            $quotation = Quotation::where('activity_id', $activity_id)->firstOrFail();

            if (!$quotation) {
                return response()->json([
                    'message' => 'Cotizacion no encontrada'
                ], 404);
            }
            $validity_to = $request['validity_to'];
            if (strpos($validity_to, '0000') === 0) {
                $validity_to = null;
            }




            if (
                isset($quotation->validity_to, $validity_to, $request['validity_from'])
                && $quotation->validity_to !== $validity_to
            ) {

                $newValidity = new DateTime($validity_to);
                $validity_from = new DateTime($request['validity_from']);

                $diff = $validity_from->diff($newValidity);
                $quotation->change_date = $diff->days;
            }
            // Actualizar el campo dinámicamente
            $temporality = $request['temporality'];
            $quotation->temporality = $temporality;
            $quotation->validity_from = $request['validity_from'];
            $quotation->validity_to = $validity_to;
            $quotation->type_currency = $request['type_currency'];
            $quotation->salary_projection = $request['salary_projection'];

            if ($temporality == "permanent") {
                $quotation->single_payment_value = null;
            }
            if ($temporality == "short") {
                $quotation->annual_calculation_amount = null;
                $quotation->semiannual_calculation_amount = null;
                $quotation->quarterly_calculation_amount = null;
                $quotation->monthly_calculation_amount = null;
            }

            $quotation->save();

            if ($activity->state_id == State::REGISTRADO) {
                ActionController::create($activity_id, Action::INICIAR_COTIZACION, 'Cotizacion Iniciada');
            }

            return response()->json([
                'status' => 200,
                'message' => 'Campo actualizado correctamente'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 500,
                'message' => $e->getMessage()
            ]);
        }
    }

    public function saveAffiliateQuotation(Request $req, $cpath)
    {
        $activityQuotation = Activity::where('id', $req->input('id'))
            ->firstOrFail();

        $client = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('id', $req->input('id'))->firstOrFail();

        $docType = $req->input('affiliate.doc_type');
        $docNumber = $req->input('affiliate.doc_number');

        // Validación de que existan el tipo de documento y el número
        if (!$docType || !$docNumber) {
            return response()->json([
                'status' => 500,
                'message' => 'Faltan datos del tomador',
                'response' => false
            ]);
        }

        // Validar que el tipo de documento tenga al menos 2 letras
        if (!preg_match('/^[a-zA-Z]{2,}$/', $docType)) {
            return response()->json([
                'status' => 500,
                'message' => 'El tipo de documento debe contener al menos 2 letras',
                'response' => false
            ]);
        }

        // Validar que el número de documento tenga al menos 5 caracteres
        if (strlen($docNumber) < 5) {
            return response()->json([
                'status' => 500,
                'message' => 'El número de documento debe tener al menos 5 caracteres',
                'response' => false
            ]);
        }

        DB::beginTransaction();
        try {
            $client = Client::where('path', $cpath)->firstOrFail();
            $activity = Activity::where('id', $req->input('id'))->firstOrFail();


            $docType = $req->input('affiliate.doc_type');
            $docNumber = $req->input('affiliate.doc_number');


            // Validación de que existan el tipo de documento y el número
            if (!$docType || !$docNumber) {
                return response()->json([
                    'message' => 'Faltan datos del tomador',
                    'response' => false
                ], 500);
            }

            // Validar que el tipo de documento tenga al menos 2 letras
            if (!preg_match('/^[a-zA-Z]{2,}$/', $docType)) {
                return response()->json([
                    'message' => 'El tipo de documento debe contener al menos 2 letras',
                    'response' => false
                ], 500);
            }


            // Validar que el número de documento tenga al menos 5 caracteres
            if (strlen($docNumber) < 5) {
                return response()->json([
                    'message' => 'El número de documento debe tener al menos 5 caracteres',
                    'response' => false
                ], 500);
            }

            $affiliate = Affiliate::where('client_id', $client->id)
                ->where('doc_type', $docType)
                ->where('doc_number', $docNumber)
                ->first();
            if (!$affiliate) {
                $affiliate = new Affiliate;
                $maxCode = Affiliate::max('unique_code');
                $nextCode = $maxCode ? intval($maxCode) + 1 : 1;
                $affiliate->unique_code = $nextCode;
            }

            $affiliate->client_id = $client->id;
            $affiliate->doc_type = $docType;
            $affiliate->doc_number = $docNumber;
            $affiliate->first_name = $req->input('affiliate.first_name');
            $affiliate->last_name = $req->input('affiliate.last_name') ? $req->input('affiliate.last_name') : '';
            $affiliate->phone = $req->input('affiliate.phone');
            $affiliate->cellphone = $req->input('affiliate.cellphone');
            if (filter_var($req->input('affiliate.email'), FILTER_VALIDATE_EMAIL)) {
                $affiliate->email = $req->input('affiliate.email');
            }
            $affiliate->save();

            // Validamos que el tomador no tenga otra póliza con la misma actividad económica escogida en esta
            $excludedStates = [
                StatePoliza::POLIZA_CANCELADA,
                StatePoliza::TRAMITE_ANULADO,
            ];
            $activityIds = Activity::where('affiliate_id', $affiliate->id)
                ->where('service_id', Service::SERVICE_POLICY_SORT_MNK)
                ->whereNotIn('state_id', $excludedStates)
                ->pluck('id');

            // Validamos que el tomador no tenga otra póliza con la misma actividad económica escogida en esta
            $countPolicies = PolicySort::whereIn('activity_id', $activityIds)
                ->where('activity_economic_id', $req->input('affiliate.economic_activity_code'))
                ->count();

            if ($countPolicies >= 1) {
                return response()->json([
                    'success' => false,
                    'message' => 'El tomador ya tiene una póliza activa con la misma actividad ecónomica',
                ]);
            }


            $activity->affiliate_id = $affiliate->id;
            $activity->save();

            $quotation = Quotation::where('activity_id', $req->input('id'))
                ->firstOrFail();
            $quotation->activity_economic_id = $req->input('affiliate.economic_activity_code');
            $quotation->economic_activity_percentage = $req->input('affiliate.economic_activity_percentage');
            $quotation->economic_activity = $req->input('affiliate.sector');
            $quotation->notification_email = $req->input('affiliate.email');
            $quotation->work_modality_id = $req->input('affiliate.work_modality_id');
            $quotation->option_asegurement = $req->input('affiliate.option_asegurement');
            $quotation->save();

            if ($activity->state_id == State::REGISTRADO) {
                ActionController::create($req->input('id'), Action::INICIAR_COTIZACION, 'Cotizacion Iniciada');
            }

            DB::commit();
            return response()->json(['success' => true, 'affiliate_id' => $affiliate->id]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['error' => 'Ocurrió un error al guardar los datos', 'exception' => $e], 500);
        }
    }

    /**
     * Método para guardar la actividad economica en cotizacion
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function saveEconomicActivity(Request $request)
    {
        DB::beginTransaction();
        try {
            $activity_id = $request->id;
            $quotation = Quotation::where('activity_id', $activity_id)->first();

            $quotation->activity_economic_id = $request->activity_economic_id;
            $quotation->economic_activity = $request->sector;
            $quotation->save();
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['error' => 'Ocurrió un error al guardar los datos'], 500);
        }
        return response()->json(['success' => true,]);
    }

    /**
     * Método para generar la cotización
     */
    public function generateQuotation(Request $request, $cpath, $id)
    {
        Client::where('path', $cpath)->firstOrFail();

        DB::beginTransaction();
        try {
            // Actualizo la cotización
            /***
             * Aquí se actualiza el estado de la actividad
             ***/
            $activity = Activity::where('id', $id)->firstOrFail();
            $highRiskEconomicActivities = AppServiceProvider::$highRiskEconomicActivities;
            if (in_array($activity->quotation->activity_economic_id, $highRiskEconomicActivities) && $activity->quotation->temporality == 'permanent') {
                if ($activity->state_id !== State::COTIZACION_EN_TRAMITE) {
                    return response()->json([
                        'success' => true,
                        'id' => $activity->id, // Devuelve el ID de la actividad
                    ], 200);
                }
                $action = ActionController::create(
                    $id,
                    ActionCotizacionSort::SOLICITAR_COTIZACION_ACT_ALTO_RIESGO,
                    'Cotización solicitada'
                );
                $jsonPath = public_path('js/economic_activity/private.json');
                $json = file_get_contents($jsonPath);
                $economicActivities = json_decode($json, true);
                $activity_economic_name = collect($economicActivities)->firstWhere('CODE', $activity->quotation->activity_economic_id)['ACTIVITY_NAME'];

                $subject = 'Cotización de seguro obligatorio de alto riesgo';
                $text = "Se ha ingresado la cotización N° {$activity->quotation->id}, del tomador {$activity->affiliate->first_name} con la actividad {$activity->quotation->activity_economic_id} - {$activity_economic_name}!";
                $email = env('APP_ENV') === 'production' ? '<EMAIL>' : '<EMAIL>';

                // Enviar el correo a prevencion
                $mailSent = new SendDocumentDataBase(
                    $email,
                    $subject,
                    "<EMAIL>",
                    "Cotización generada",
                    [
                        "text" => $text,
                        "sender" => 'MNK seguros'
                    ],
                    "<EMAIL>",
                    [],
                    "send_generic_document",
                    $activity->client,
                    $request->host,
                    $activity->id,
                    $action->id
                );

                // Capturar el resultado del envío
                $result = $mailSent->sendMail();

                //Registramos los datos del correo enviado para la trazabilidad
                $mailBoardController = new MailBoardController();
                $mailBoardController->createRegisterMail(
                    $activity->id,
                    $activity->service->id, 
                    '', 
                    'Tomador', 
                    $activity->affiliate->full_name, 
                    $activity->affiliate->doc_number, 
                    $subject, 
                    $text,
                    $email, 
                    $result,
                    null
                );

                //Guardamos el correo enviado para realizar la traza
            } else {
                $action = ActionController::create(
                    $id,
                    ActionCotizacionSort::SOLICITAR_COTIZACION,
                    'Cotización solicitada'
                );
            }

            //Guardamos 

            DB::commit();

            // Llamar con get a api/enviar-certificados-emision-poloza/{id}
            UtilsController::sendGeneratedQuoteEmailAsync(config('app.url') . '/api/enviar-correo-cotizacion-generada/' . $activity->quotation->id);

            return response()->json([
                'success' => true,
                'id' => $activity->id, // Devuelve el ID de la actividad
            ], 200);
        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withInput()->withErrors(['error' => $e->getMessage()]);
        }
    }

    /**
     * Método para enviar el email cuando se genere la cotizacón SORT
     *
     * @param $request
     * @param $quotation
     * @param $activity
     * @return void
     */

    public function sendGeneratedQuoteEmailAsync(Request $request, $id)
    {

        $quotation = Quotation::find($request->id);

        $activity = $quotation->activity;

        $action = $activity->activity_actions
            ->whereIn('action_id', [Action::SOLICITAR_COTIZACION, ActionCotizacionSort::SOLICITAR_COTIZACION_ACT_ALTO_RIESGO])
            ->first();



        $activity = Activity::where('id', $activity->id)->firstOrFail();
        //Json de actividades economica
        $jsonSource = ($activity->quotation->economic_activity == 'public') ? '/js/economic_activity/public.json' : '/js/economic_activity/private.json';
        $json = file_get_contents(public_path($jsonSource));
        $economicActivities = json_decode($json, true);

        //Se transforma a una collección en laravel
        $activity_economic_name = collect($economicActivities)->firstWhere('CODE', $activity->quotation->activity_economic_id)['ACTIVITY_NAME'];
        $activity_policy = Activity::where('parent_id', $activity->id)->where('service_id', Service::SERVICE_POLICY_SORT_MNK)->first();
        $work_modality_id = '';
        if ($activity_policy) {
            $work_modality_id = $activity_policy->policy_sort->work_modality_id;
        }

        //capturar acción "Solicitar cotización" de la actividad cotización, la acción más antigua
        $actionCreatedAt = ActivityAction::where('action_id', Action::SOLICITAR_COTIZACION)
            ->where('activity_id', $activity->id)
            ->orderBy('created_at', 'asc')->first();

        //declarar valores para las fechas: Fecha de cotización
        $quotation_date = $actionCreatedAt->created_at ?? "";

        // Preparar los datos para la vista
        $data = [
            'activity' => $activity,
            'quotation' => $activity->quotation,
            'affiliate' => $activity->affiliate,
            'quotation_date' => $quotation_date,
            'hidden_contacto' => true,
            'activity_economic_name' => $activity_economic_name,
            'work_modality_id' => $work_modality_id
        ];

        // Renderizar la vista como PDF
        $pdf = \PDF::loadView('services.plantilla.docs.cotizaciones_resume_pdf', $data);

        $files = array();

        $document = 'Cotización_SORT';

        //Cargamos el archivo en S3
        \Storage::disk('s3')
            ->put("activity_action_document/{$document}_{$quotation->id}.pdf", $pdf->output());

        //guardamos en activity_action_documents
        $activityActionDocument = new ActivityActionDocument();
        $activityActionDocument->activity_action_id = $action->id;
        $activityActionDocument->name = $document;
        $activityActionDocument->path = "activity_action_document/{$document}_{$quotation->id}.pdf";
        $activityActionDocument->save();

        //formamos archivo
        $files[] = [
            'type' => 'pdf',
            'path' => "activity_action_document/{$document}_{$quotation->id}.pdf",
            'name' => $document . '_' . $quotation->id . '.pdf',
        ];

        $nameIntermediary = mb_convert_case(mb_strtolower($quotation->advisor_name), MB_CASE_TITLE, "UTF-8");
        $nameTaker = $activity->affiliate->first_name . ' ' . $activity->affiliate->last_name;
        $nameTaker = mb_convert_case(mb_strtolower($nameTaker), MB_CASE_TITLE, "UTF-8");

        $subject = 'Cotización de Seguro Obligatorio de Riesgos del Trabajo';
        $text = "¡Buen día, $nameIntermediary!
                        
                Nos complace adjuntarle la cotización del Seguro Obligatorio de Riesgos del Trabajo que nos ha solicitado para el cliente $nameTaker, en la cual podrá encontrar todos los detalles y condiciones.
            
                Si tiene alguna consulta o necesita más detalles sobre la cotización, por favor, contáctenos al correo electrónico <EMAIL> o al teléfono 4102-7600. ¡Será un gusto servirle!
                        
                ¡Agradecemos sinceramente su preferencia y confianza en nosotros! Nuestro propósito es fortalecer la prevención en salud y seguridad laboral del país.
            ";

        $emails = [$quotation->email];
        $emailString = implode(',', $emails);

        // Enviar el correo el documento adjunto
        $mailSent = new SendDocumentDataBase(
            $emailString,
            $subject,
            "<EMAIL>",
            "Cotización de Seguro Obligatorio de Riesgos del Trabajo",
            [
                "text" => $text,
                "sender" => 'mnk aseguramiento'
            ],
            "<EMAIL>",
            $files,
            "send_document_db",
            $activity->client,
            $request->host,
            $activity->id,
            $action->id
        );

        // Capturar el resultado del envío
        $result = $mailSent->sendMail();

        $username = preg_replace('/^CO-/', '', $quotation->code); // Remover el prefijo "CO-"

        $user = User::where(function ($query) use ($username) {
            $query->where('username', $username) // Buscar directamente sin "CO-"
            ->orWhere('username', 'CO-' . $username) // Buscar con "CO-"
            ->orWhere('code_mnk',$username)
                ->orWhere('code_mnk','CO-' . $username);
        })->first();

       //Registramos los datos del correo enviado para la trazabilidad
       $mailBoardController = new MailBoardController();
       $mailBoardController->createRegisterMail(
           $activity->id,
           $activity->service->id, 
           '', 
           'Intermediario', 
           $nameIntermediary, 
           $user->identification_number ?? 'Sin identificación', 
           $subject, 
           $text,
           $emails, 
           $result,
           $files
       );
    }

    private function sendGeneratedQuoteEmail($request, $quotation, $activity, $action)
    {

        $activity = Activity::where('id', $activity->id)->firstOrFail();
        //Json de actividades economica
        $jsonSource = ($activity->quotation->economic_activity == 'public') ? '/js/economic_activity/public.json' : '/js/economic_activity/private.json';
        $json = file_get_contents(public_path($jsonSource));
        $economicActivities = json_decode($json, true);

        //Se transforma a una collección en laravel
        $activity_economic_name = collect($economicActivities)->firstWhere('CODE', $activity->quotation->activity_economic_id)['ACTIVITY_NAME'];

        $activity_policy = Activity::where('parent_id', $activity->id)->where('service_id', Service::SERVICE_POLICY_SORT_MNK)->first();
        $work_modality_id = '';
        if ($activity_policy) {
            $work_modality_id = $activity_policy->policy_sort->work_modality_id;
        }

        //capturar acción "Solicitar cotización" de la actividad cotización, la acción más antigua
        $actionCreatedAt = ActivityAction::where('action_id', Action::SOLICITAR_COTIZACION)
            ->where('activity_id', $activity->id)
            ->orderBy('created_at', 'asc')->first();


        //declarar valores para las fechas: Fecha de cotización
        $quotation_date = $actionCreatedAt->created_at ?? "";

        // Preparar los datos para la vista
        $data = [
            'activity' => $activity,
            'quotation' => $activity->quotation,
            'affiliate' => $activity->affiliate,
            'quotation_date' => $quotation_date,
            'hidden_contacto' => true,
            'activity_economic_name' => $activity_economic_name,
            'work_modality_id' => $work_modality_id
        ];

        // Renderizar la vista como PDF
        $pdf = \PDF::loadView('services.plantilla.docs.cotizaciones_resume_pdf', $data);

        $files = array();

        $document = 'Cotización_SORT';

        //Cargamos el archivo en S3
        \Storage::disk('s3')
            ->put("activity_action_document/{$document}_{$quotation->id}.pdf", $pdf->output());

        //guardamos en activity_action_documents
        $activityActionDocument = new ActivityActionDocument();
        $activityActionDocument->activity_action_id = $action->id;
        $activityActionDocument->name = $document;
        $activityActionDocument->path = "activity_action_document/{$document}_{$quotation->id}.pdf";
        $activityActionDocument->save();

        //formamos archivo
        $files[] = [
            'type' => 'pdf',
            'path' => "activity_action_document/{$document}_{$quotation->id}.pdf",
            'name' => $document . '_' . $quotation->id . '.pdf',
        ];

        $nameIntermediary = mb_convert_case(mb_strtolower($quotation->advisor_name ?? ''), MB_CASE_TITLE, "UTF-8");

        $nameTaker = mb_convert_case(mb_strtolower($activity->affiliate->first_name . ' ' . $activity->affiliate->last_name), MB_CASE_TITLE, "UTF-8");

        $subject = 'Cotización de Seguro Obligatorio de Riesgos del Trabajo';

        $text = "¡Buen día, $nameIntermediary!
                        
                Nos complace adjuntarle la cotización del Seguro Obligatorio de Riesgos del Trabajo que nos ha solicitado para el cliente $nameTaker, en la cual podrá encontrar todos los detalles y condiciones.
            
                Si tiene alguna consulta o necesita más detalles sobre la cotización, por favor, contáctenos al correo electrónico <EMAIL> o al teléfono 4102-7600. ¡Será un gusto servirle!
                        
                ¡Agradecemos sinceramente su preferencia y confianza en nosotros! Nuestro propósito es fortalecer la prevención en salud y seguridad laboral del país.
            ";

        $emails = [$quotation->email];
        $emailString = implode(',', $emails);

        // Enviar el correo el documento adjunto
        $mailSent = new SendDocumentDataBase(
            $emailString,
            $subject,
            "<EMAIL>",
            "Cotización de Seguro Obligatorio de Riesgos del Trabajo",
            [
                "text" => $text,
                "sender" => 'mnk aseguramiento'
            ],
            "<EMAIL>",
            $files,
            "send_document_db",
            $activity->client,
            $request->host,
            $activity->id,
            $action->id
        );
   
        // Capturar el resultado del envío
        $result = $mailSent->sendMail();

        //Registramos los datos del correo enviado para la trazabilidad
        $mailBoardController = new MailBoardController();
        $mailBoardController->createRegisterMail(
            $activity->id,
            $activity->service->id, 
            '', 
            'Tomador', 
            $activity->affiliate->full_name, 
            $activity->affiliate->doc_number, 
            $subject, 
            $text,
            $emails, 
            $result,
            null
        );
        
    }

    public function reSendEmail(Request $req, $cpath)
    {
        try {
            //Id de la cotización
            $id = $req->input('id');
            $emailsValid = $req->emails;

            $activity = Activity::where('id', Quotation::where('id', $id)->pluck('activity_id')->first())->firstOrFail();

            $quotation = $activity->quotation;
            $document = 'Cotización_SORT';

            //formamos archivo
            $files[] = [
                'type' => 'pdf',
                'path' => "activity_action_document/{$document}_{$quotation->id}.pdf",
                'name' => $document . '_' . $quotation->id . '.pdf',
            ];

            $nameIntermediary = mb_convert_case(mb_strtolower($quotation->advisor_name ?? ''), MB_CASE_TITLE, "UTF-8");
            $nameTaker = mb_convert_case(mb_strtolower($activity->affiliate->first_name . ' ' . $activity->affiliate->last_name), MB_CASE_TITLE, "UTF-8");

            $subject = 'Cotización de Seguro Obligatorio de Riesgos del Trabajo';

            $text = "¡Buen día, $nameIntermediary!
                        
                    Nos complace adjuntarle la cotización del Seguro Obligatorio de Riesgos del Trabajo que nos ha solicitado para el cliente $nameTaker, en la cual podrá encontrar todos los detalles y condiciones.
                
                    Si tiene alguna consulta o necesita más detalles sobre la cotización, por favor, contáctenos al correo electrónico <EMAIL> o al teléfono 4102-7600. ¡Será un gusto servirle!
                            
                    ¡Agradecemos sinceramente su preferencia y confianza en nosotros! Nuestro propósito es fortalecer la prevención en salud y seguridad laboral del país.
                ";

            $emails = $emailsValid;
            $emailString = implode(',', $emails);

            // Enviar el correo el documento adjunto
            $mailSent = new SendDocumentDataBase(
                $emailString,
                $subject,
                "<EMAIL>",
                "Cotización de Seguro Obligatorio de Riesgos del Trabajo",
                [
                    "text" => $text,
                    "sender" => 'mnk aseguramiento'
                ],
                "<EMAIL>",
                $files,
                "send_document_db",
                $activity->client,
                $req->host,
                $activity->id,
                ''
            );
            $mailSent->sendMail();
            // Verificamos si el correo fue enviado exitosamente
            if ($mailSent) {
                // Retornamos una respuesta positiva para que el frontend lo maneje como éxito
                return response()->json(true);
            } else {
                // Si hubo algún problema enviando el correo, retornamos false
                return response()->json(false);
            }
        } catch (\Exception $e) {
            return back()->withInput()->withErrors(['error' => $e->getMessage()]);
        }
    }

    function validateFile($files, $host)
    {
        $extraHtml = '';
        foreach ($files as $file) {
            $filePath = 'https://renapp-prod.s3.us-east-2.amazonaws.com/' . $file['path'];
            $extraHtml .= '<br/><br/>Puede descargar el documento presionando <a href="' . $filePath . '">AQUI</a> 
                                          <br/><br/>O puede descargarlos copiando y pengando el siguiente link en el navegador: <br/><br/>' . $filePath . '<br/><br/>';
        }
        return $extraHtml;
    }

    /***
     *
     * Este metodo permite pasar ejecutar la acción "Iniciar Cotización"
     * Y consecutivamente se guardarán los datos del intermediario y del tomador
     *
     */

    public function startQuotation(Request $req, $cpath)
    {

        $client = Client::where('path', $cpath)->firstOrFail();

        DB::beginTransaction();

        try {
            /***
             * Aquí se almacenan los datos del intermediario y del tomador
             ***/
            $affiliate_id = $this->saveIntermediaryAndPolicyHolderData($req, $cpath);

            /***
             * Aquí se actualiza el estado de la actividad y el id del afiliado(tomador)
             ***/
            $activity = Activity::where('client_id', $client->id)->where('id', $req->input('activity'))->firstOrFail();
            $activity->affiliate_id = $affiliate_id;
            $activity->state_id = State::COTIZACION_EN_TRAMITE;
            $activity->save();

            /***
             * Aquí se registra el historial de actividades (Activity_Actions)
             ***/

            $description = "Una descripción interesante para el historial de actividades";

            $this->registerActivityAction(
                $req,
                Action::INICIAR_COTIZACION,
                State::REGISTRADO,
                State::COTIZACION_EN_TRAMITE,
                $description,
                $activity->user_id
            );


            DB::commit();

            return response()->json([
                'status' => 'success',
                'message' => 'Campos han sido actualizados correctamente'
            ]);


            /***
             * La idea es crear llamar el metodo de Action de ActionController pero necesario
             * ver como hacer el roll back entre dos controladores para
             * la base de datos por si no funciona o ocurre un error - Standby
             ***/
        } catch (Exception $e) {
            DB::rollBack();
            return response()->json(['error' => 'Ocurrió un error al guardar los datos'], 500);
        }
    }

    /***
     * Este metodo permite pasar ejecutar la acción "Iniciar Emisión Poliza"
     ***/

    /**
     * Método para obtener los datos del intinerario
     *
     * @param Request $req
     * @return array
     */
    private function getDataIntermediary(Request $req)
    {
        return [
            'activity' => $req->input('activity'),
            'brokerage_name' => $req->input('brokerage_name'),
            'advisor_name' => $req->input('advisor_name'),
            'code' => $req->input('code'),
            'email' => $req->input('email')
        ];
    }

    /**
     * Método para obtener los datos del tomador
     *
     * @param Request $req
     * @return array[]
     */
    private function getDataPolicyHolder(Request $req)
    {
        return [
            'id' => $req->activity,
            'affiliate' => [
                'doc_type' => $req->input('docType'),
                'doc_number' => $req->input('docNumber'),
                'first_name' => $req->input('name'),
                'phone' => $req->input('phone'),
            ]
        ];
    }

    /**
     * Método para obtener los datos de la cotización
     *
     * @param Request $req
     * @return array[]
     */
    private function getDataQuoteDetails(Request $req)
    {
        return [
            'temporality' => $req->input('temporality'),
            'validity_from' => $req->input('validity_from'),
            'validity_to' => $req->input('validity_to'),
            'type_currency' => $req->input('type_currency'),
            'salary_projection' => $req->input('salary_projection')
        ];
    }

    /**
     * Método para obtener los datos del calculo de la poliza
     *
     * @param Request $req
     * @return array[]
     */
    private function getDataPolicyCalculation(Request $req)
    {
        return [
            'annual_calculation_amount' => $req->input('annual_calculation_amount'),
            'semiannual_calculation_amount' => $req->input('semiannual_calculation_amount'),
            'quarterly_calculation_amount' => $req->input('quarterly_calculation_amount'),
            'monthly_calculation_amount' => $req->input('monthly_calculation_amount'),
            'single_payment_value' => $req->input('single_payment_value')
        ];
    }

    /**
     * Método para guardar los datos del intermediario y el afiliado
     *
     * @param Request $req
     * @param $cpath
     * @return void
     */
    private function saveIntermediaryAndPolicyHolderData(Request $req, $cpath)
    {
        $dataIntermediary = $this->getDataIntermediary($req);
        $dataPolicyHolder = $this->getDataPolicyHolder($req);

        $this->save_intermediary(new Request($dataIntermediary), $cpath);
        $response = $this->saveAffiliateQuotation(new Request($dataPolicyHolder), $cpath);
        $jsonData = $response->getData(true); // true para obtener un array asociativo
        return $jsonData['affiliate_id'];
    }

    /**
     * Método para crear registro en activity_actions
     *
     * @param Request $req
     * @param Request $type
     * @return void
     */
    private function registerActivityAction(Request $req, $action_id, $old_state_id, $new_state_id, $description, $user_id)
    {
        $newActivityAction = new ActivityAction();
        $newActivityAction->activity_id = $req->input('activity');
        $newActivityAction->action_id = $action_id;
        $newActivityAction->old_state_id = $old_state_id;
        $newActivityAction->new_state_id = $new_state_id;
        $newActivityAction->description = $description;
        $newActivityAction->old_user_id = $user_id;
        $newActivityAction->new_user_id = $user_id;
        $newActivityAction->author_id = Auth::id();
        $newActivityAction->save();
    }

    /**
     * Vista para subir documentos de la cotización actividades economicas de alto riesgo
     */
    public function showUploadDocuments($cpath, $id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();
        $activity_documents = ActivityDocument::where('activity_id', $activity->id)->get();
        $documents = [];
        foreach ($activity_documents as $doc) {
            $documents[$doc->document_id] = $doc;
        }

        return view('services.quotation.step.upload_documents', [
            'id' => $id,
            'activity' => $activity,
            'documents' => $documents
        ]);
    }
    /**
     * Metodo para cargar los documentos de la cotización
     *
     * @param Request $req
     * @param Request $type
     * @return \Illuminate\Http\JsonResponse
     */
    public function uploadDocuments(Request $req, $cpath, $id)
    {

        $client = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();

        $validated = $req->validate([
            'document3' => 'required|file|mimes:pdf|max:10240',
            'document4' => 'required|file|mimes:pdf|max:10240',
            'document5' => 'required|file|mimes:pdf|max:10240',
            'document6' => 'required|file|mimes:pdf|max:10240',
            'document7' => 'required|file|mimes:pdf|max:10240',
            'document8' => 'required|file|mimes:pdf|max:10240',
        ]);

        //Lista de los documentos
        $documentFields = [
            'document1' => Quotation::DOCUMENTO_SOPORTE_1,
            'document2' => Quotation::DOCUMENTO_SOPORTE_2,
            'document3' => Quotation::DOCUMENTO_SOPORTE_3,
            'document4' => Quotation::DOCUMENTO_SOPORTE_4,
            'document5' => Quotation::DOCUMENTO_SOPORTE_5,
            'document6' => Quotation::DOCUMENTO_SOPORTE_6,
            'document7' => Quotation::DOCUMENTO_SOPORTE_7,
            'document8' => Quotation::DOCUMENTO_SOPORTE_8,
        ];
        DB::beginTransaction();
        try {
            // Verificar si algún archivo fue cargado
            foreach ($documentFields as $documentField => $documentId) {
                if ($req->hasFile($documentField)) {
                    // Subir el documento a S3
                    $file = $req->file($documentField);
                    $filePath = "activity_documents/{$documentField}_{$activity->id}.pdf";

                    Storage::disk('s3')->put($filePath, file_get_contents($file->getRealPath()));

                    // Guardar el documento en AcitivityDocument
                    $activityDocument = new ActivityDocument();
                    $activityDocument->activity_id = $activity->id;
                    $activityDocument->document_id = $documentId;
                    $activityDocument->path = $filePath;
                    $activityDocument->uploaded_at = now();
                    $activityDocument->save();
                }
            }

            DB::commit();
            return response()->json([
                'status' => 200,
                'message' => 'Documentos cargados correctamente'
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'status' => 500,
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Metodo para generar condiciones especiales de una cotización
     *
     * @param Request $req
     * @param Request $type
     * @return \Illuminate\Http\JsonResponse
     */
    public function conditionSpecial(Request $req, $cpath, $id)
    {

        DB::beginTransaction();

        try {

            $quotation = Quotation::where('activity_id', $id)->first();

            $quotation->update($req->all());

            //Generamos la acción solictar condiciones especiales 
            $description = "se ejecuto la acción solicitar condiciones especiales";

            ActionController::create(
                $req->id,
                ActionCotizacionSort::SOLICITAR_CONDICIONES_ESPECIALES,
                $description
            );

            $nameIntermediary = mb_convert_case(mb_strtolower($quotation->advisor_name), MB_CASE_TITLE, "UTF-8");

            $subject = 'Análisis de condiciones especiales requerido para cotización' . ' '  . $quotation->id;

            $text = "
                Estamido/a Equipo de Aseguramiento SORT, Por medio de este correo, le notificamos que hemos identificado una solicitud del intermediario $nameIntermediary para el análisis de condiciones especiales asociada a la cotización número $quotation->id de fecha $quotation->created_at. Para poder avanzar con este trámite, solicitamos su colaboración en la revisión y evaluación de los requerimientos específicos detallados en la solicitud, la cual encontrara en su bandeja de trabajo.
            ";

            $emails = config('app.env') == 'prod'  ? ['<EMAIL>', '<EMAIL>', '<EMAIL>'] : ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'];

            // Enviar el correo el documento adjunto
            $mailSent = new SendDocumentDataBase(
                implode(',', $emails),
                $subject,
                "<EMAIL>",
                "Análisis de condiciones especiales requerido para cotización",
                [
                    "text" => $text,
                    "sender" => 'MNK seguros'
                ],
                "<EMAIL>",
                [],
                "send_document_db",
                $quotation->activity->client,
                $req->host,
                $quotation->activity->id,
                ActionCotizacionSort::SOLICITAR_CONDICIONES_ESPECIALES
            );
            
            // // Capturar el resultado del envío
            // $result = $mailSent->sendMail();

            // $username = preg_replace('/^CO-/', '', $quotation->code); // Remover el prefijo "CO-"

            // $user = User::where(function ($query) use ($username) {
            //     $query->where('username', $username) // Buscar directamente sin "CO-"
            //     ->orWhere('username', 'CO-' . $username) // Buscar con "CO-"
            //     ->orWhere('code_mnk',$username)
            //         ->orWhere('code_mnk','CO-' . $username);
            // })->first();

            // //Registramos los datos del correo enviado para la trazabilidad
            // $mailBoardController = new MailBoardController();
            // $mailBoardController->createRegisterMail(
            //     $quotation->activity->id,
            //     $quotation->activity->service->id, 
            //     '', 
            //     'Intermediario', 
            //     $nameIntermediary, 
            //     $user->identification_number ?? 'Sin identificación', 
            //     $subject, 
            //     $text,
            //     $emails, 
            //     $result,
            //     null
            // );

            DB::commit();

            return response()->json([
                'status' => 200,
                'message' => 'Acción generada'
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'status' => 500,
                'message' => $e->getMessage()
            ]);
        }
    }

    public function saveConditionSpecial(Request $request)
    {

        //Ejecutamos la accion reportar condiciones especiales 
        $description = "se ejecuto la acción reportar condiciones especiales";

        $activity_action = ActionController::create(
            $request->activity_id,
            ActionCotizacionSort::REPORTAR_CONDICIONES_ESPECIALES,
            $description
        );

        $activity = Activity::find($request->activity_id);

        $quotation = $activity->quotation;

        // Guardar los datos en la base de datos
        QuotationConditionSpecial::create([

            'annual_calculation_amount' => $activity->quotation->annual_calculation_amount,
            'semiannual_calculation_amount' => $activity->quotation->semiannual_calculation_amount,
            'quarterly_calculation_amount'  => $activity->quotation->quarterly_calculation_amount,
            'monthly_calculation_amount' => $activity->quotation->monthly_calculation_amount,

            'activity_id' => $request->activity_id,
            'anual_base' => $request->anual_base,
            'semianual_base' => $request->semianual_base,
            'trimestral_base' => $request->trimestral_base,
            'mensual_base' => $request->mensual_base,
            
            'anual_continuidad' => $request->anual_continuidad,
            'semianual_continuidad' => $request->semianual_continuidad,
            'trimestral_continuidad' => $request->trimestral_continuidad,
            'mensual_continuidad' => $request->mensual_continuidad,
            
            'anual_cmc' => $request->anual_cmc,
            'semianual_cmc' => $request->semianual_cmc,
            'trimestral_cmc' => $request->trimestral_cmc,
            'mensual_cmc' => $request->mensual_cmc,
            
            'anual_clap' => $request->anual_clap,
            'semianual_clap' => $request->semianual_clap,
            'trimestral_clap' => $request->trimestral_clap,
            'mensual_clap' => $request->mensual_clap,
            
            'anual_descuento' => $request->anual_descuento,
            'semianual_descuento' => $request->semianual_descuento,
            'trimestral_descuento' => $request->trimestral_descuento,
            'mensual_descuento' => $request->mensual_descuento,
            
            'anual_final' => $request->anual_final,
            'semianual_final' => $request->semianual_final,
            'trimestral_final' => $request->trimestral_final,
            'mensual_final' => $request->mensual_final,
            
            'semianual_fraccionada' => $request->semianual_fraccionada,
            'trimestral_fraccionada' => $request->trimestral_fraccionada,
            'mensual_fraccionada' => $request->mensual_fraccionada,
            
            'continuidad_value' => $request->continuidad_value,
            'cmc' => $request->cmc,
            'clap' => $request->clap,
            'fraccionamiento_value' => $request->fraccionamiento_value,
        ]);

        $quotation->update([
            'annual_calculation_amount'     => $request->anual_final,
            'semiannual_calculation_amount' => $request->semianual_fraccionada,
            'quarterly_calculation_amount'  => $request->trimestral_fraccionada,
            'monthly_calculation_amount'    => $request->mensual_fraccionada,
        ]);

        if(!empty($request->anual_continuidad)){
            $quotation->update([
                'anual_percentage'          => number_format($request->anual_continuidad, 2, ',', ''),
                'semestral_percentage'      => number_format($request->semianual_continuidad,2, ',', ''),
                'trimestral_percentage'     => number_format($request->trimestral_continuidad, 2, ',', ''),
                'mensual_percentage'        => number_format($request->mensual_continuidad,2, ',', ''),
            ]);
        }

        $activity = Activity::where('id', $activity->id)->firstOrFail();
        //Json de actividades economica
        $jsonSource = ($activity->quotation->economic_activity == 'public') ? '/js/economic_activity/public.json' : '/js/economic_activity/private.json';
        $json = file_get_contents(public_path($jsonSource));
        $economicActivities = json_decode($json, true);

        //Se transforma a una collección en laravel
        $activity_economic_name = collect($economicActivities)->firstWhere('CODE', $activity->quotation->activity_economic_id)['ACTIVITY_NAME'];
        $activity_policy = Activity::where('parent_id', $activity->id)->where('service_id', Service::SERVICE_POLICY_SORT_MNK)->first();
        $work_modality_id = '';
        if ($activity_policy) {
            $work_modality_id = $activity_policy->policy_sort->work_modality_id;
        }

        //declarar valores para las fechas: Fecha de cotización
        $quotation_date = $activity->quotation->created_at ?? "";

        $activity->quotation->annual_calculation_amount     = $request->anual_final;
        $activity->quotation->semiannual_calculation_amount = $request->semianual_fraccionada;
        $activity->quotation->quarterly_calculation_amount  = $request->trimestral_fraccionada;
        $activity->quotation->monthly_calculation_amount    = $request->mensual_continuidad;

        if(!empty($request->anual_continuidad)){
            $activity->quotation->anual_percentage      = number_format($request->anual_continuidad, 2, ',', '');
            $activity->quotation->semestral_percentage  = number_format($request->semianual_continuidad,2, ',', '');
            $activity->quotation->trimestral_percentage = number_format($request->trimestral_continuidad, 2 , ',', '');
            $activity->quotation->mensual_percentage    = number_format($request->mensual_continuidad,2, ',', '');
        }

        // Preparar los datos para la vista
        $data = [
            'activity' => $activity,
            'quotation' => $activity->quotation,
            'affiliate' => $activity->affiliate,
            'quotation_date' => $quotation_date,
            'hidden_contacto' => true,
            'activity_economic_name' => $activity_economic_name,
            'work_modality_id' => $work_modality_id
        ];

        // Renderizar la vista como PDF
        $pdf = \PDF::loadView('services.plantilla.docs.cotizaciones_resume_pdf', $data);

        $files = array();

        $document = 'Cotización_SORT_CE';

        //Cargamos el archivo en S3
        \Storage::disk('s3')
            ->put("activity_action_document/{$document}_{$quotation->id}_CE.pdf", $pdf->output());

        //guardamos en activity_action_documents
        $activityActionDocument = new ActivityActionDocument();
        $activityActionDocument->activity_action_id = $activity_action->id;
        $activityActionDocument->name = $document;
        $activityActionDocument->path = "activity_action_document/{$document}_{$quotation->id}_CE.pdf";
        $activityActionDocument->save();

        //formamos archivo
        $files[] = [
            'type' => 'pdf',
            'path' => "activity_action_document/{$document}_{$quotation->id}_CE.pdf",
            'name' => $document . '_' . $quotation->id . '.pdf',
        ];

        $nameIntermediary = mb_convert_case(mb_strtolower($quotation->advisor_name), MB_CASE_TITLE, "UTF-8");
        $emailIntermediary = $quotation->email;
        $nameTaker =    mb_convert_case(mb_strtolower($activity->affiliate->full_name), MB_CASE_TITLE, "UTF-8");

        $subject = 'Respuesta de condiciones especiales requerido para cotización' . ' '  . $quotation->id;

        $text = "
            Estimado/a $nameIntermediary, se ha actualizado el cálculo de la prima para la cotización número $quotation->id del cliente $nameTaker. La nueva prima para pagar puede evidenciarla en el documento adjunto.
        ";

        $emails = config('app.env') == 'prod'  ? ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'] : ['<EMAIL>', '<EMAIL>', '<EMAIL>'];

        // Unimos el email del intermediario con los demás destinatarios
        $destinatarios = array_merge([$emailIntermediary], $emails);

        // Enviar el correo el documento adjunto
        $mailSent = new SendDocumentDataBase(
            implode(',', $destinatarios),
            $subject,
            "<EMAIL>",
            "Aprobación de condiciones especiales para la cotización",
            [
                "text" => $text,
                "sender" => 'MNK seguros'
            ],
            "<EMAIL>",
            $files,
            "send_document_db",
            $quotation->activity->client,
            '',
            $quotation->activity->id,
            ActionCotizacionSort::REPORTAR_CONDICIONES_ESPECIALES
        );
        
        // Capturar el resultado del envío
        $result = $mailSent->sendMail();

        $username = preg_replace('/^CO-/', '', $quotation->code); // Remover el prefijo "CO-"

        $user = User::where(function ($query) use ($username) {
            $query->where('username', $username) // Buscar directamente sin "CO-"
            ->orWhere('username', 'CO-' . $username) // Buscar con "CO-"
            ->orWhere('code_mnk',$username)
                ->orWhere('code_mnk','CO-' . $username);
        })->first();

        //Registramos los datos del correo enviado para la trazabilidad
        $mailBoardController = new MailBoardController();
        $mailBoardController->createRegisterMail(
            $quotation->activity->id,
            $quotation->activity->service->id, 
            '', 
            'Intermediario', 
            $nameIntermediary, 
            $user->identification_number ?? 'Sin identificación', 
            $subject, 
            $text,
            $emails, 
            $result,
            null
        );

        DB::commit();
        // Devolver una respuesta
        return response()->json(['success' => true, 'message' => 'Datos guardados correctamente']);
    }
}
