<?php

namespace App\Http\Controllers\Services;

use App\Actions\ActionPeItSort;
use App\Actions\ActionPemptsort;
use App\Activity;
use App\Client;
use App\Http\Controllers\ActionController;
use App\Http\Controllers\Controller;
use App\Http\Controllers\Tables\MailBoardController;
use App\Mail\SendDocumentDataBase;
use App\MailTemplates\Constants\Templates;
use App\MailTemplates\TemplateBuilder;
use App\PeMptBeneficiaries;
use App\PeMptSort;
use App\PolicySort;
use App\PolicySpreadsheet;
use App\PolicySpreadsheetAffiliate;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class PeMptSortController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function form(Request $request, $cpath, $id)
    {
        $client   = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();
        $detailDeathWorker = $this->getDetailDeathWorker($activity);

        return view('services.pe_death_by_working_person_sort.form', [
            'activity' => $activity,
            'affiliate' => $activity->affiliate,
            'pmtSort' => $detailDeathWorker->pmtSort,
            'id' => $id,
            'peMptBeneficiaries' => $detailDeathWorker->peMptBeneficiaries,
            'averageSalaryFormatted' => $detailDeathWorker->averageSalaryFormatted,
            'annualSalaryFormatted' => $detailDeathWorker->annualSalaryFormatted,
            'currencySymbol' => $detailDeathWorker->currencySymbol,

        ]);
    }

    public static function getDetailDeathWorker($activity)
    {
        $pmtSort = PeMptSort::where('activity_id', $activity->id)->first();
        $peMptBeneficiaries = collect();
        if($pmtSort){
            $peMptBeneficiaries = PeMptBeneficiaries::where('pe_mpt_id', $pmtSort->id)->get();
        }
        // Si no hay beneficiarios, crear una colección vacía para que los campos se muestren vacíos
        if ($peMptBeneficiaries->isEmpty()) {
            $peMptBeneficiaries = collect([new PeMptBeneficiaries()]);
        }

        $lastThreeMonths = PolicySpreadsheetAffiliate::query()
            ->where('affiliate_id', $activity->affiliate_id)
            ->orderBy('id', 'desc')
            ->limit(3)
            ->select('policy_spreadsheet_id', 'monthly_salary')
            ->get();

        $monthlySalaries = $lastThreeMonths->pluck('monthly_salary');

        // Calcular el promedio del salario mensual
        $averageSalary = $monthlySalaries->avg();


        // Calcular el salario anual
        $annualSalary = $averageSalary * 12;

        $currencySymbol = '';

        $firstItem = $lastThreeMonths->first();
        if ($firstItem){
            $policy_spreadsheet = PolicySpreadsheet::where('id', $firstItem->policy_spreadsheet_id)
                ->select('activity_id')
                ->first();
            $activity_policy_spreadsheet_id = Activity::where('id',$policy_spreadsheet->activity_id)
                ->select('parent_id')
                ->first();
            $policy_sort = PolicySort::where('activity_id', $activity_policy_spreadsheet_id->parent_id)
                ->select('type_currency')
                ->first();

            switch ($policy_sort->type_currency) {
                case 'USD':
                    $currencySymbol = '$'; // Dólares
                    break;
                case 'CRC':
                    $currencySymbol = '₡'; // Colones costarricenses
                    break;
                default:
                    $currencySymbol = ''; // Si no se define, dejar vacío
                    break;
            }
        }
        // Formatear los salarios con miles y dos decimales
        $averageSalaryFormatted = $currencySymbol . number_format($averageSalary, 2, '.', ',');
        $annualSalaryFormatted = $currencySymbol . number_format($annualSalary, 2, '.', ',');

        return (object) [
            'pmtSort' => $pmtSort,
            'peMptBeneficiaries' => $peMptBeneficiaries,
            'averageSalaryFormatted' => $averageSalaryFormatted,
            'annualSalaryFormatted' => $annualSalaryFormatted,
            'currencySymbol' => $currencySymbol
        ];
    }

    /**
    * Proceso para guardar los datos de PE MPT Sort
    * Se guarda y se crea acción REGISTRAR MPT MANUAL
    * @param Request $request
    * @param $cpath
    * @param $id
    * @return \Illuminate\Http\JsonResponse
    */
    public function save(Request $request, $cpath, $id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        // Buscamos la actividad asociada al servicio médico
        $activity = Activity::where('client_id', $client->id)
            ->where('id', $id)
            ->firstOrFail();
        // Iniciamos la transacción
        DB::beginTransaction();

        try {
            // Realizamos la validación del request
            $request->validate([
                'type_document' => 'required',
                'identification_number' => 'required',
                'name' => 'required',
                'case' => 'required',
                'date_of_death' => 'required',
                'entity_that_issued_the_rating' => 'required',
                'opinion_or_rating' => 'required',
                'qualification_date' => 'required',
                'code_cie' => 'required',
                'name_of_the_diagnosis' => 'required',
                'condition' => 'required',
                "start_date_of_rent" => "required",
                "start_date_of_rent_submit" => "required",
                "prom_monthly_salary" => "required",
                "annual_salary_person" => "required",
                "maximum_annual_salary" => "required",
                "annual_income" => "required",
                "monthly_value" => "required",
                "additional_monthly_sum" => "required",
                "total_month_to_pay" => "required",
                "total_years_to_pay" => "required",
                "type_of_beneficiary" => "required",
                "name_and_surname" => "required",
                "relationship" => "required",
                "monthly_payment" => "required",
                "account_number" => "required",
                "name_of_the_banking_entity" => "required",
                "General_information" => "required",
                "information_procedure" => "required",
                "detail_periods" => "required",
                "payment_information" => "required",
                "observation" => "required",
                "result_revision" => "required",
                "payment_office_number" => "required",
                "resolution_date" => "required",
            ]);
            $codeCieValue = $request->code_cie['cod'][0];


            $data = array_merge([
                "type_document" => $request->type_document,
                "identification_number" => $request->identification_number,
                "name" => $request->name,
                "case" => $request->case,
                "date_of_death" => $request->date_of_death,
                "date_of_death_submit" => $request->date_of_death_submit,
                "entity_that_issued_the_rating" => $request->entity_that_issued_the_rating,
                "opinion_or_rating" => $request->opinion_or_rating,
                "qualification_date" => $request->qualification_date,
                "qualification_date_submit" => $request->qualification_date_submit,
                "name_of_the_diagnosis" => $request->name_of_the_diagnosis,
                "condition" => $request->condition,
                "start_date_of_rent" => $request->start_date_of_rent,
                "start_date_of_rent_submit" => $request->start_date_of_rent_submit,
                "prom_monthly_salary" => $request->prom_monthly_salary,
                "annual_salary_person" => $request->annual_salary_person,
                "maximum_annual_salary" => $request->maximum_annual_salary,
                "annual_income" => $request->annual_income,
                "monthly_value" => $request->monthly_value,
                "additional_monthly_sum" => $request->additional_monthly_sum,
                "total_month_to_pay" => $request->total_month_to_pay,
                "total_years_to_pay" => $request->total_years_to_pay,
                "General_information" => $request->General_information,
                "information_procedure" => $request->information_procedure,
                "detail_periods" => $request->detail_periods,
                "payment_information" => $request->payment_information,
                "observation" => $request->observation,
                "result_revision" => $request->result_revision,
                "payment_office_number" => $request->payment_office_number,
                "resolution_date" => $request->resolution_date,
                "resolution_date_submit" => $request->resolution_date_submit,
                'activity_id' => $activity->id,
                'code_cie' => $codeCieValue,
                'qualification' => $request->qualification
            ]);
            // Buscar si ya existe un registro de PeMptSort
            $pmtSort = PeMptSort::where('activity_id', $activity->id)->first();

            if ($pmtSort) {
                // Si existe, actualizar el registro
                $pmtSort->update($data);
            } else {
                // Si no existe, se crea
                $pmtSort = PeMptSort::create($data);
                // Creación de acción
                $description = 'REGISTRAR MPT MANUAL';
                ActionController::create($activity->id, ActionPemptsort::REGISTRAR_MPT_MANUAL, $description);
            }
            // Creación o actualización de beneficiarios
            if ($request->has('type_of_beneficiary')) {
                foreach ($request->type_of_beneficiary as $index => $type) {
                    PeMptBeneficiaries::updateOrCreate(
                        [
                            'identification_number_beneficiary' => $request->identification_number_beneficiary[$index],
                            'pe_mpt_id' => $pmtSort->id // Asocia el beneficiario con el registro de PeMptSort
                        ],
                        [
                            'type_of_beneficiary' => $type,
                            'name_and_surname' => $request->name_and_surname[$index],
                            'birthdate' => $request->birthdate[$index],
                            'relationship' => $request->relationship[$index],
                            'monthly_payment' => $request->monthly_payment[$index],
                            'account_number' => $request->account_number[$index],
                            'name_of_the_banking_entity' => $request->name_of_the_banking_entity[$index],
                            'account_type' => $request->account_type[$index]
                        ]
                    );
                }
            }

            DB::commit();
            return response()->json(['success' => true, 'message' => 'Datos guardados correctamente.']);
        } catch (QueryException $e) {
            DB::rollback();
            return response()->json(['success' => false, 'message' => 'Error en la base de datos: ' . $e->getMessage()]);
        }
    }
    public function registradorMptIntegrado(Request $req, $cpath, $id)
    {

        $client = Client::where('path', $cpath)->firstOrFail();

        $description = 'REGISTRAR MPT INTEGRADO';
        DB::beginTransaction();
        try {
            $activity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();
            $activityParent = Activity::where('id',$activity->parent_id)->first();
            $gis = $activityParent->gis_sort;
            $activity_policy_sort = $activityParent->parent_activity;
            $policy_sort = policySort::where('activity_id', $activity_policy_sort->id)
                ->firstOrFail();
            $nameTaker = mb_convert_case(mb_strtolower($activity_policy_sort->affiliate->full_name, 'UTF-8'), MB_CASE_TITLE, 'UTF-8');

            $nameAffiliateActivity = mb_convert_case(mb_strtolower(optional($activity->affiliate)->full_name ?? '', 'UTF-8'), MB_CASE_TITLE, 'UTF-8');
            $actionController= ActionController::create(
                $activity->id,
                ActionPemptsort::REGISTRAR_MPT_INTEGRADO,
                $description
            );

            $email = $activity_policy_sort->affiliate->email;
            $client_id = $client->id;

            $emailData = TemplateBuilder::build(
                Templates::IDENTIFICATION_OF_BENEFICIARIES_IN_THE_EVENT_OF_WORKERS_DEATH,
                [
                    'name' => $nameTaker,
                    'policy_sort' => $policy_sort->formatNumberConsecutive(),
                    'name_affiliate' => $nameAffiliateActivity,
                    'doc_number' => $activity->affiliate->doc_number,
                    'connector' => $activity->affiliate->gender === 'F' ? 'de la trabajadora' : 'del trabajador',
                    'case_number' => sprintf('%04d', $gis->id),
                    'date_of_death' => ucfirst(strftime('%A %e de %B del %Y', strtotime($gis->date_death)))
                ]
            );

            $mailSent = new SendDocumentDataBase(
                $email,
                $emailData['subject'],
                "<EMAIL>",
                "Pago de incapacidad temporal",
                [
                    "text" => $emailData['body'],
                    "sender" => $emailData['sender']
                ],
                "<EMAIL>",
                [], // Sin archivos adjuntos
                "send_document_db",
                $client_id,
                $req->getHost(),
                $activity->id,
                $actionController->id,
                $activity->service->id
            );
            
             
            // Capturar el resultado del envío
            $result = $mailSent->sendMail();

            //Registramos los datos del correo enviado para la trazabilidad
            $mailBoardController = new MailBoardController();
            $mailBoardController->createRegisterMail(
                $activity->id,
                $activity->service->id, 
                $policy_sort->consecutive, 
                'Tomador', 
                $nameAffiliateActivity, 
                $activity->affiliate->doc_number, 
                $emailData['subject'], 
                $emailData['body'],
                $email, 
                $result,
                null
            );
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
            ]);
        }
        return response()->json([
            'status' => 'success',
            'message' => 'Reportar reconocimiento traslado hospedaje y alimentación',
        ]);
    }




}