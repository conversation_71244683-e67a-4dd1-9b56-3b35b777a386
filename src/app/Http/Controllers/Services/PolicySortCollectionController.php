<?php

namespace App\Http\Controllers\Services;

use App\Action;
use App\Actions\ActionPeIpSort;
use App\Actions\ActionPolicySortCollection;
use App\Activity;
use App\ActivityActionDocument;
use App\ActivityDocument;
use App\Affiliate;
use App\Area;
use App\Bank;
use App\BankAccountTransfer;
use App\Client;
use App\Holiday;
use App\Http\Controllers\ActionController;
use App\Http\Controllers\Controller;
use App\Http\Controllers\Integrations\WebserviceAcselController;
use App\Http\Controllers\Integrations\WebserviceElectronicInvoiceController;
use App\Http\Controllers\Integrations\WebServiceMnk\WebServiceHaciendaController;
use App\Http\Controllers\Tables\AnalystController;
use App\Http\Middleware\NumberToWords;
use App\Mail\SendDocumentDataBase;
use App\MailTemplates\Constants\Senders;
use App\MailTemplates\Constants\Templates;
use App\MailTemplates\TemplateBuilder;
use App\PolicySort;
use App\PolicySortCollection;
use App\PolicySpreadsheet;
use App\PremiumSurplus;
use App\PremiumSurplusesLog;
use App\Providers\AppServiceProvider;
use App\Service;
use App\State;
use App\States\StatePolicySortCollection;
use DateTime;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use PDF;
use App\User;
use App\Actions\ActionCotizacionSort;
use App\QuotationConditionSpecial;
use App\ActivityAction;
use App\Http\Controllers\Tables\MailBoardController;
use Excel;


class PolicySortCollectionController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */

     protected $policySortController;
    public function __construct()
    {
        $this->middleware('auth')->except([
            'reportInsuranceIncreaseUnpaid',
            'cronMonthlyNoPaymentReceipt',
            'cronQuarterlyNoPaymentReceipt',
            'cronBiannualNoPaymentReceipt',
            'cronRehabilitationNoPaymentReceipt',
            'exportTodayTc',
            'insertPremiumSurplus'
        ]);

        $this->policySortController = app()->make(PolicySortController::class);
    }

    //Calcula la fecha de vencimiento con los días feriados de COSTA RICA
    function calculateDueDate($businessDays)
    {

        // Obtener todos los feriados desde la tabla 'holidays'
        $holidays = Holiday::pluck('holiday') // Aquí obtienes solo las fechas
            ->toArray();    // Convertir a un array para usar en la función

        $currentDate = Carbon::now();
        $daysAdded = 0;

        // Mientras no hayamos alcanzado los días hábiles deseados
        while ($daysAdded < $businessDays) {
            // Sumar un día
            $currentDate->addDay();

            // Verificar si es fin de semana
            if ($currentDate->isWeekend()) {
                continue; // Saltar si es fin de semana
            }

            // Verificar si es un día feriado
            if (in_array($currentDate->format('Y-m-d'), $holidays)) {
                continue; // Saltar si es feriado
            }

            // Contar el día hábil
            $daysAdded++;
        }

        return $currentDate->format('Y-m-d');
    }

    function calculateDueDateWithValidityFrom($businessDays, $validityFrom)
    {

        // Obtener todos los feriados desde la tabla 'holidays'
        $holidays = Holiday::pluck('holiday') // Aquí obtienes solo las fechas
        ->toArray();    // Convertir a un array para usar en la función

        $dateFrom = Carbon::parse($validityFrom); // Usar la fecha validityFrom
        $daysAdded = 0;

        // Mientras no hayamos alcanzado los días hábiles deseados
        while ($daysAdded < $businessDays) {
            // Sumar un día
            $dateFrom->addDay();

            // Verificar si es fin de semana
            if ($dateFrom->isWeekend()) {
                continue; // Saltar si es fin de semana
            }

            // Verificar si es un día feriado
            if (in_array($dateFrom->format('Y-m-d'), $holidays)) {
                continue; // Saltar si es feriado
            }

            // Contar el día hábil
            $daysAdded++;
        }

        return $dateFrom->format('Y-m-d');
    }


    /**
     * Método para el formulario nativo de Costos Poliza SORT
     *
     * @param Request $req
     * @param Request $id activity de la poliza
     * Permite Activar pestañas de recibo a partir de diferentes
     * acciones realizadas realizadas en el servicio de Poliza Sort
     */
    public function form(Request $req, $cpath, $id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('client_id', $client->id)->where('id', $id)
            ->where('service_id', Service::SERVICE_POLICY_SORT_COLLECTION_MNK)
            ->firstOrFail();
        $activity_policy = Activity::where('id', $activity->parent_id)->first();

        $document = ActivityDocument::where('activity_id', $activity->id)
            ->where('document_id', PolicySortCollection::ACTIVITY_DOCUMENT_BANK_TRANSFER_DOCUMENT)
            ->first();

        // Sumar la columna total_amount de los registros cuyo activity_id esté
        $activityIds = Activity::where('service_id', Service::SERVICE_POLICY_SORT_COLLECTION_MNK)
            ->where('parent_id', $activity->parent_id)->pluck('id');
        $totalAmount = PolicySortCollection::whereIn('activity_id', $activityIds)
            ->sum('total_amount');
        $totalAmountCount = PolicySortCollection::whereIn('activity_id', $activityIds)
            ->where('payment_status', PolicySortCollection::PAYMENT_STATUS_APPROVED)
            ->count();

        $policy = PolicySort::where('activity_id', $activity->parent_id)->first();
        $bankAccountTransfers = BankAccountTransfer::where('currency', $policy->type_currency)->get();

        $webserviceController = new WebserviceAcselController();
        $trm = $webserviceController->getTrm();

        // se envia todos los banks para poder filtrar la cuenta por la moneda en el frontend
        $banks = Bank::with('accounts')->get();

        $calculatedAmount = 0;
        //monto de la prima
        switch ($policy->periodicity) {
            case 1: // Anual
                $calculatedAmount = $policy->annual_calculation_amount * 1;
                break;
            case 2: // Semestral
                $calculatedAmount = $policy->semiannual_calculation_amount * 2;
                break;
            case 3: // Trimestral
                $calculatedAmount = $policy->quarterly_calculation_amount * 4;
                break;
            case 4: // Mensual
                $calculatedAmount = $policy->monthly_calculation_amount * 12;
                break;
            default:
                $calculatedAmount = $policy->annual_calculation_amount;
                break;
        }

        $premiumSurplus = PremiumSurplus::whereHas('activity.parent_activity', function ($query) use ($activity) {
            $query->where('affiliate_id', $activity->parent->affiliate_id);
        })->get();

        $premiumSurplusDetails = PremiumSurplusesLog::where('charge_id', $activity->policy_sort_collection->id)->get();

        return view('services.policy_sort_collection.form', [
            'activity_policy' => $activity_policy,
            'activity' => $activity,
            'document' => $document,
            'totalAmount' => $totalAmount,
            'totalAmountCount' => $totalAmountCount,
            'banks' => $banks,
            'trm' => $trm,
            'id' => $id,
            'calculatedAmount' => $calculatedAmount,
            'premiumSurplus' => $premiumSurplus,
            'premiumSurplusDetails' => $premiumSurplusDetails
        ]);
    }

    public function paymentPolicy(Request $req, $cpath, $id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();
        $enabled_states_tb = [
            StatePolicySortCollection::RECIBO_AUMENTO_SEGURO_PENDIENTE_PAGO,
            StatePolicySortCollection::RECIBO_ABONO_MENSUAL_PENDIENTE_PAGO,
            StatePolicySortCollection::RECIBO_ABONO_TRIMESTRAL_PENDIENTE_PAGO,
            StatePolicySortCollection::RECIBO_ABONO_SEMESTRAL_PENDIENTE_PAGO,
            StatePolicySortCollection::RECIBO_REHABILITACION_PENDIENTE_PAGO,
            StatePolicySortCollection::PENDIENTE_PAGO_RECIBO_EMISION,
            StatePolicySortCollection::RECIBO_LIQUIDACION_PENDIENTE_PAGO,
            StatePolicySortCollection::RECIBO_RENOVACION_PENDIENTE_PAGO
        ];
        $enabled_states_tc = [
            StatePolicySortCollection::RECIBO_AUMENTO_SEGURO_PENDIENTE_PAGO,
            StatePolicySortCollection::RECIBO_ABONO_MENSUAL_PENDIENTE_PAGO,
            StatePolicySortCollection::RECIBO_ABONO_TRIMESTRAL_PENDIENTE_PAGO,
            StatePolicySortCollection::RECIBO_ABONO_SEMESTRAL_PENDIENTE_PAGO,
            StatePolicySortCollection::RECIBO_REHABILITACION_PENDIENTE_PAGO,
            StatePolicySortCollection::PENDIENTE_PAGO_RECIBO_EMISION,
            StatePolicySortCollection::RECIBO_LIQUIDACION_PENDIENTE_PAGO
        ];

        $policy = PolicySort::where('activity_id', $activity->parent_id)->first();
        $bankAccountTransfers = BankAccountTransfer::where('currency', $policy->type_currency)->get();

        $currency = $policy->type_currency;
        $banks = Bank::with(['accounts' => function ($query) use ($currency) {
            $query->where('currency', $currency);
        }])->get();

        $policy_collection = PolicySortCollection::where('activity_id', $id)->first();
        if (!$policy) {
            $liquidation = Activity::where('id', $activity->parent_id)->first();
            $policy = PolicySort::where('activity_id', $liquidation->parent_id)->first();
            $policy_collection = PolicySortCollection::where('activity_id', $id)->first();
        }

        $message = '';

        if ($policy_collection->type_receipt == 'rehabilitation' || $policy_collection->type_receipt == 'emission') {
            $message = 'Este recibo no tiene período de gracia; la vigencia iniciará en la fecha y hora del pago.';
        }

        $button_enabled = in_array($activity->state_id, $enabled_states_tb);
        $button_enabled_tc = in_array($activity->state_id, $enabled_states_tc);

        date_default_timezone_set('America/Costa_Rica');
        $horaActual = new DateTime();
        $horaCostarrica = $horaActual->format('H:i:s');
        $fechaCostarrica = $horaActual->format('Y-m-d');

        if ($policy_collection->payment_status == PolicySortCollection::PAYMENT_STATUS_PENDING_APPROVAL) {
            $message = 'La validación de pago se encuentra pendiente';
        }
        if ($policy_collection->payment_status == PolicySortCollection::PAYMENT_STATUS_APPROVED) {
            $message = 'El pago se encuentra aprobado';
        }

        $disable = $policy_collection->payment_status == PolicySortCollection::PAYMENT_STATUS_PENDING_APPROVAL
            || $policy_collection->payment_status == PolicySortCollection::PAYMENT_STATUS_APPROVED;

        $activityPending = PolicySortCollection::leftJoin('activities as a', 'a.id', '=', 'policy_sort_collections.activity_id')
                            ->where('a.parent_id', $activity->parent_id)
                            ->where('policy_sort_collections.activity_id', '<', $activity->id)
                            ->where('policy_sort_collections.payment_status', PolicySortCollection::PAYMENT_STATUS_PENDING)
                            ->min('policy_sort_collections.activity_id');

        $callback = false;

        if ($activityPending) {
            $message = "El pago no puede aplicarse al recibo más reciente. 
                        Primero debe completarse el pago pendiente del periodo anterior. 
                        <br><br>
                        <a href='/servicio/$activityPending/policy_sort_collection/pago_poliza' class='ui blue button' target='_blank'>
                            Realizar pago pendiente
                        </a>";

            $callback = true;
        }

        return view('services.policy_sort_collection.payment-policy', [
            'id' => $id,
            'policy' => $policy,
            'policy_collection' => $policy_collection,
            'activity' => $activity,
            'callback' => $callback,
            'button_enabled' => $button_enabled,
            'button_enabled_tc' => $button_enabled_tc,
            'bankAccountTransfers' => $bankAccountTransfers,
            'banks' => $banks,
            'message' => $message,
            'fechaCostarrica' => $fechaCostarrica,
            'horaCostarrica' => $horaCostarrica,
            'disable' => $disable,
        ]);
    }

    public function paymenQa(Request $req, $cpath)
    {

        $data = $req->only(['monto', 'indredirect', 'redirect', 'poliza', 'moneda', 'order']);

        return view('services.payment_qa.formPaymentQa', array_merge($data, [
            'id' => '',
        ]));
    }

    public function handleCallback(Request $req, $cpath, $id)
    {

        $document = '';
        $filePath = '';
        $responseText = $req->query('responsetext');
        $orderId = $req->query('orderid');
        $authCode = $req->query('authcode');
        $responseCode = $req->query('response_code');
        $client = Client::where('path', $cpath)->firstOrFail();
        $activity_collection = Activity::where('id', $id)->first();
        $policy_collection = $activity_collection->policy_sort_collection;
        $poliicy_activity = Activity::where('id', $activity_collection->parent_id)->first();
        $policy = $poliicy_activity->policy_sort;
        if (!$policy) {
            $liquidation = $poliicy_activity;
            $policy = PolicySort::where('activity_id', $liquidation->parent_id)->first();
        }
        if ($responseCode == '100') {
            try {
                $action = $this->registerAnyCcPayment($policy_collection, $activity_collection, $cpath, $authCode, $req);
                $emails = [$activity_collection->affiliate->email];

                $policyDos = PolicySort::where('activity_id', $activity_collection->parent_id)->first();
                $idPolicy = $policyDos->formatNumberConsecutive();

                $messageEmission = "pagada";

                if ($policy_collection->type_receipt == 'emission') {
                    $messageEmission = "emitida";
                }

                $message = "¡Su póliza ha sido $messageEmission con éxito! El número de contrato es {$idPolicy}.<br>¡Agradecemos sinceramente su preferencia y confianza en nosotros!";

                $isplanilla = Activity::where('parent_id', $policy->activity_id)->where('service_id', Service::SERVICE_REPORT_TAKEN_FORM_MNK)->count();

                if ($isplanilla == 0 && $policy_collection->type_receipt == 'emission') {

                    $message = "¡Su póliza ha sido emitida exitosamente! El número de contrato es el {$idPolicy}.<br><br>
                                Sin embargo, hemos notado que está pendiente la carga del reporte de planilla.
                                Le agradecemos mucho que, por favor, nos envíe este documento antes del inicio de operaciones.
                                De lo contrario, en caso de que ocurra un riesgo laboral a uno de sus colaboradores, tendremos que tramitar el evento como “no asegurado” y proceder al cobro del costo total de su atención.<br><br>
                                Si tiene alguna consulta adicional o necesita asistencia, por favor, contáctenos al 4102-7600. ¡Será un gusto servirle!<br>
                                ¡Agradecemos sinceramente su preferencia y confianza en nosotros!";
                }

                if (auth()->user()->area_id == Area::INTERMEDIARY) {
                    return redirect("intermediario")
                        ->with('success', $message);
                } elseif (auth()->user()->area_id == Area::ADMINISTRATIVE) {
                    return redirect("/nuevo")
                        ->with('success', $message);
                } else {
                    return redirect("tomador/poliza/" . $activity_collection->affiliate_id . "/recibos_pendientes/" . $policy->id)
                        ->with('success', $message);
                }
            } catch (Exception $e) {
                return view('errors.known', [
                    'message' => 'Error en el pago - Código de respuesta: ' . $responseCode . ', código de autorización: ' . $authCode . '\nError:' . $e->getMessage(),
                ]);
            }
        } else {
            return view('errors.known', [
                'message' => 'Error en el pago - Código de respuesta: ' . $responseCode . ', código de autorización: ' . $authCode,
            ]);
        }
    }

    /**
     * Accion REGISTRAR PAGO TC- Varios tipos de registros.
     *
     */
    private function registerAnyCcPayment($collection, $activity_collection, $cpath, $authCode, $req)
    {
        $client = Client::where('path', $cpath)->firstOrFail();

        $emision = false;
        DB::beginTransaction();
        try {
            switch ($activity_collection->state_id) {
                case StatePolicySortCollection::RECIBO_AUMENTO_SEGURO_PENDIENTE_PAGO:
                    $message = 'Registrar pago tc - Aumento del seguro periodo';
                    $action = ActionPolicySortCollection::REGISTRAR_PAGO_TC_AUMENTO_SEGURO_PERIODO;
                    //REPORTAR PAGO AUMENTO DE SEGURO en el servicio POLIZA SORT
                    break;
                case StatePolicySortCollection::RECIBO_ABONO_MENSUAL_PENDIENTE_PAGO:
                    $message = 'Registrar pago tc - Recibo abono mensual';
                    $action = ActionPolicySortCollection::REGISTRAR_PAGO_TC_RECIBO_ABONO_MENSUAL;
                    break;
                case StatePolicySortCollection::RECIBO_ABONO_TRIMESTRAL_PENDIENTE_PAGO:
                    $action = ActionPolicySortCollection::REGISTRAR_PAGO_TC_RECIBO_ABONO_TRIMESTRAL;
                    $message = 'Registrar pago tc - Recibo abono trimestral';
                    break;
                case StatePolicySortCollection::RECIBO_ABONO_SEMESTRAL_PENDIENTE_PAGO:
                    $message = 'Registrar pago tc - Recibo abono semestral';
                    $action = ActionPolicySortCollection::REGISTRAR_PAGO_TC_RECIBO_ABONO_SEMESTRAL;
                    break;
                case StatePolicySortCollection::RECIBO_REHABILITACION_PENDIENTE_PAGO:
                    $message = 'Registrar pago tc - Recibo rehabilitación';
                    $action = ActionPolicySortCollection::REGISTRAR_PAGO_TC_RECIBO_REHABILITACION;
                    break;
                case StatePolicySortCollection::RECIBO_LIQUIDACION_PENDIENTE_PAGO:
                    $message = 'Registrar pago tc - Liquidación';
                    $action = ActionPolicySortCollection::REGISTRAR_PAGO_TC_RECIBO_LIQUIDACION;

                    $activity_liquidation = Activity::where('parent_id', $activity_collection->parent_id)
                        ->where('service_id', Service::SERVICE_LIQUIDATION_SORT_MNK)
                        ->first();

                    $liquidation = new LiquidationPolicyController();
                    $liquidation->submitSettlementPayment($cpath, $activity_liquidation->id);

                    break;
                case StatePolicySortCollection::RECIBO_RENOVACION_PENDIENTE_PAGO:
                    $message = 'Registrar pago tc - renovacion';
                    $action = ActionPolicySortCollection::REGISTRAR_PAGO_TC_RECIBO_RENOVACION;

                    $activity_renewal = Activity::where('parent_id', $activity_collection->parent_id)
                        ->where('service_id', Service::SERVICE_RENEWAL_SORT_MNK)
                        ->first();
                    $renewal = new RenewalSortController();
                    $renewal->reportRenewalPayment($cpath, $activity_renewal->id);

                    break;
                case StatePolicySortCollection::PENDIENTE_PAGO_RECIBO_EMISION:
                    $action = ActionPolicySortCollection::REGISTRAR_PAGO_TC;
                    $message = 'Registrar pago tc';
                    $emision = true;
                    break;
                default:
                    return back()->with('message', 'No se pudo realizar la operación, ya que el estado de cobros no es válido: ' . $activity_collection->state_id);
            }

            $collection->payment_status = PolicySortCollection::PAYMENT_STATUS_APPROVED;
            $collection->transaction_id = $authCode;

            date_default_timezone_set('America/Costa_Rica');
            $horaActual = new DateTime();
            $horaFormateada = $horaActual->format('H:i:s');

            $collection->transaction_date = Carbon::now();
            $collection->time_transaction = $horaFormateada;
            $collection->payment_method = 'TC';

            $webserviceController = new WebserviceAcselController();
            $trm = $webserviceController->getTrm();
            $collection->trm = $trm;
            $collection->save();

            register_shutdown_function(function () use ($activity_collection) {
                $webserviceFac = new WebserviceElectronicInvoiceController();
                $resp = $webserviceFac->sendInvoice($activity_collection->id);
            });

            //Actualizar estado de la actividad
            $activityActionsCreated = ActionController::create($activity_collection->id, $action, $message);

            // Enviar correo confirmación del pago
            $policy = Activity::where('id', $activity_collection->parent_id)
                ->where('service_id', Service::SERVICE_POLICY_SORT_MNK)->first();
            if (!$policy) {
                throw new Exception('Póliza no encontrada para el servicio: ' . $activity_collection->id);
            }

            if ($emision) {
                $changeStatePolicy = new PolicySortCollectionController();
                $changeStatePolicy->reportPolicyPaymentMade($activity_collection, $cpath, $req);
            }

            $policySortController = new PolicySortController();
            $policySortController->generateAccountingEntry($cpath, $policy->policy_sort->id);

            $generateAccountingEntryCollections = new AnalystController();
            $generateAccountingEntryCollections->generateAccountingEntryCollections($cpath, $policy->policy_sort->id, $action);

            //envia recibo de pago
            $this->sendPaymentReceipt($client, $activity_collection, $activityActionsCreated, $action, true);

            $emails = [$policy->policy_sort->email];
            $imageUrl = asset('images/mnk.png');
            $text = [
                "text" => "
                    <br>
                    <img src='{$imageUrl}' alt='MNK Logo' style='width: 150px; height: auto;'>
                    <br>
                    MNK SEGUROS, le entrega el documento electrónico generado por la prima de su seguro. Estos documentos adjuntos son los solicitados por hacienda para la presentación de los gastos.
                ",
                "sender" => 'MNK Seguros'
            ];

            $mailSent = new SendDocumentDataBase(
                implode(',', $emails),
                "Aprobación de pago",
                "<EMAIL>",
                "Aprobación de pago",
                $text,
                "<EMAIL>",
                [],
                "send_document_db",
                $client,
                request()->getHost(),
                $activity_collection->id,
                $activityActionsCreated->id,
                $activity_collection->service->id
            );

            // Capturar el resultado del envío
            $result = $mailSent->sendMail();

            //Registramos los datos del correo enviado para la trazabilidad
            $mailBoardController = new MailBoardController();
            $mailBoardController->createRegisterMail(
                $activity_collection->id,
                $activity_collection->service->id,
                $activity_collection->parent->policy_sort->consecutive,
                'Tomador',
                $activity_collection->parent->affiliate->full_name,
                $activity_collection->parent->affiliate->doc_number,
                'Aprobación de pago',
                $text,
                $emails,
                $result,
                null
            );
            
            DB::commit();
            return $activityActionsCreated;
        } catch (Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    public function saveHasCardPayment(Request $req, $cpath, $id)
    {
        if (!isset($req->has_card_payment)) {
            return response()->json([
                'code' => 400,
                'status' => 'error',
                'message' => 'El campo de pago con tarjeta es obligatorio'
            ]);
        }

        $client = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();
        $policySortCollection = $activity->policy_sort_collection;

        DB::beginTransaction();
        try {
            $policySortCollection->has_card_payment = $req->has_card_payment;

            $policySortCollection->save();

            DB::commit();
        } catch (Exception $e) {
            DB::rollback();
            
            return response()->json([
                'code'=> 500,
                'status' => 'error',
                'message' => 'Error al actualizar datos'
            ]);
        }

        return response()->json([
            'code' => 200,
            'status' => 'success',
            'message' => 'Campos han sido actualizados correctamente',
        ]);
    }

    public function save(Request $req, $cpath, $id)
    {

        $client = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();
        $policySortCollection = $activity->policy_sort_collection;

        DB::beginTransaction();
        try {

            if ($req->transaction_date_submit) {
                $policySortCollection->transaction_date = $req->transaction_date_submit;
            }

            date_default_timezone_set('America/Costa_Rica');
            $horaActual = new DateTime();
            $horaFormateada = $horaActual->format('H:i:s');

            $valuePaind = str_replace(['₡', '$', '.', ','], ['', '', '', '.'], $req->value_paid);
            $paymentDifference = str_replace(['₡', '$', '.', ','], ['', '', '', '.'], $req->payment_difference);
            $appliedCreditNoteAmount = str_replace(['₡', '$', '.', ','], ['', '', '', '.'], $req->applied_credit_note_amount);

            $policySortCollection->time_transaction = $horaFormateada;
            $policySortCollection->bank_id = $req->bank_account;
            $policySortCollection->account_id = $req->account_details;
            $policySortCollection->value_paid = $valuePaind;
            $policySortCollection->payment_currency = $req->payment_currency;
            $policySortCollection->transaction_id = $req->transaction_id;
            $policySortCollection->payment_difference = $paymentDifference ?? 0;
            $policySortCollection->applied_credit_note_amount = $appliedCreditNoteAmount ?? 0;

            if (isset($req->has_card_payment)) {
                $policySortCollection->has_card_payment = $req->has_card_payment;
            }

            $policySortCollection->bank_id_pt = $req->bank_id_pt;

            $policySortCollection->save();

            $notasCredito = $req->input('notas_credito', []);

            if (!empty($notasCredito)) {
                foreach ($notasCredito as $row) {
                    if (!empty($row['id']) && !empty($row['saldo']) && isset($row['valor_aplicar'])) {

                        $valorAplicar = str_replace(['₡', '$', '.', ','], ['', '', '', '.'], $row['valor_aplicar']);
                        $valorAplicar = floatval($valorAplicar);

                        if($valorAplicar > 0){
                            PremiumSurplusesLog::create([
                                'premium_surplus_id' => $row['id'],
                                'charge_id' => $policySortCollection->id,
                                'taken_value' => $valorAplicar,
                                'previous_balance' => $row['saldo'], // saldo anterior,
                                'user_id' => auth()->user()->id
                            ]);

                            $creditBalance = $row['saldo'] - $valorAplicar;

                            PremiumSurplus::where('id', $row['id'])->update([
                                'credit_balance' => $creditBalance,
                                'status_surplus' => $creditBalance == 0 ? 'PAG' : 'ACT'
                            ]);
                        }
                    }
                }
            }

            DB::commit();

        } catch (Exception $e) {
            DB::rollback();
            return redirect()->back()->with('error', 'Error al actualizar datos. '.$e->getMessage());
        }

        return redirect()->back()->with('success', 'Datos actualizados exitosamente');
    }

    public function deleteCreditNotes($cpath, $id) //id de policy_sort_collection
    {
        try {
            DB::beginTransaction();

            $premiumSurplusDetails = PremiumSurplusesLog::where('charge_id', $id)->get();

            if ($premiumSurplusDetails->isEmpty()) {
                DB::rollBack();
                return response()->json([
                    'status' => 404,
                    'success' => false,
                    'message' => 'No se encontraron notas de crédito para eliminar'
                ], 404);
            }

            foreach ($premiumSurplusDetails as $detail) {

                $premiumSurplus = PremiumSurplus::where('id', $detail->premium_surplus_id)->first();

                if ($premiumSurplus) {
                    $newCreditBalance = $premiumSurplus->credit_balance + $detail->taken_value;

                    PremiumSurplus::where('id', $detail->premium_surplus_id)->update([
                        'credit_balance' => $newCreditBalance,
                        'status_surplus' => $newCreditBalance == 0 ? 'PAG' : 'ACT'
                    ]);
                }
            }

            $deletedCount = PremiumSurplusesLog::where('charge_id', $id)->delete();

            PolicySortCollection::where('id', $id)->update([
                'applied_credit_note_amount' => 0
            ]);

            DB::commit();

            return response()->json([
                'status' => 200,
                'success' => true,
                'message' => "Se eliminaron {$deletedCount} notas de crédito correctamente",
                'deleted_count' => $deletedCount
            ], 200);

        } catch (Exception $e) {
            DB::rollBack();

            return response()->json([
                'status' => 500,
                'success' => false,
                'message' => 'Error al eliminar notas de crédito'
            ], 500);
        }
    }
    public function pdf(Request $req, $cpath, $id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();

        $policy_sort_collection = new PolicySortCollection();
        $policy_sort_collection->activity_id = $id;
        $policy_sort_collection->setRelation('activity', $activity);
        $policy_sort_collection->created_at = new DateTime();

        $pdf = PDF::loadView(
            'services.policy_sort_collection.docs.policy_sort_collection_pdf',
            [
                'policy_sort_collection' => $policy_sort_collection,
                'watermark' => false,
                'activity' => $activity
            ]
        );

        return $pdf->stream('preview.pdf');
    }


    /**
     * Reporte de factura electrónica
     *
     * @param Request $req
     * @param int $id de la actividad asociado al servicio de cobro de póliza
     * @return \Illuminate\Http\JsonResponse
     */
    public function reportElectronicInvoice(Request $req, $cpath, $id)
    {

        $client = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();

        $haciendaController = new WebServiceHaciendaController();
        $response = $haciendaController->getElectronicInvoice();
        if ($response->getStatusCode() == 200) {
            $invoiceData = $response->getData();

            DB::beginTransaction();
            try {
                //Guardar información de la factura electrónica
                $policy_sort_collection = new PolicySortCollection();
                $policy_sort_collection->activity_id = $id;
                $policy_sort_collection->invoice_number = $invoiceData->invoice_number;
                $policy_sort_collection->total_amount = $invoiceData->total_amount;

                $policy_sort_collection->save();

                //Actualizar estado de la actividad
                $activity->state_id = State::FACTURA_ELECTRONICA_PENDIENTE_PAGO;
                $activity->save();

                $activityAction = ActionController::create(
                    $activity->id,
                    Action::REPORTAR_FACTURA_EMISION_POLIZA,
                    'Factura Emision Poliza'
                );

                DB::commit();
            } catch (Exception $e) {
                DB::rollback();
                return response()->json(['error' => 'Error al realizar la acción'], 500);
            }
            try {
                $pdf = ActionController::generatePDF($activity, 'policy_sort_collection', false);

                Storage::disk('s3')
                    ->put("activity_action_document/policy_sort_collection_{$activity->id}.pdf", $pdf->output());

                $affiliate = Affiliate::where('id', $activity->affiliate_id)->first();
                $emails = [$affiliate->email];
                // Definir el texto del correo
                $text = [
                    "text" => "Estimado cliente, adjunto encontrará la factura electrónica.",
                    "sender" => 'MNK Seguros'
                ];
                // Definir archivos adjuntos
                $affiliate_files = [
                    [
                        'path' => "activity_action_document/policy_sort_collection_{$activity->id}.pdf",
                        'name' => "policy_sort_collection_{$activity->id}.pdf",
                        'type' => 'PDF'
                    ]
                ];

                $mailSent = new SendDocumentDataBase(
                    implode(',', $emails),
                    "Factura Electrónica",
                    "<EMAIL>",
                    "Factura electrónica prueba mail",
                    $text,
                    "<EMAIL>",
                    $affiliate_files,
                    "send_document_db",
                    $client,
                    $req->getHost(),
                    $activity->id,
                    $activityAction->id,
                    $activity->service->id
                );
                $mailSent->sendMail();
            } catch (Exception $e) {
                return response()->json(['error' => 'Error al enviar el correo'], 500);
            }
            return response()->json([
                'status' => 'success',
                'message' => 'Campos han sido actualizados correctamente'
            ]);
        } else {

            throw new \Exception('No se pudo establecer conexión con el WebService de Hacienda');
        }
    }

    /**
     * Accion Reportar Aumento de Seguro Periodo
     *
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function reportInsuranceIncreasePeriod(Request $req, $cpath, $id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $policyCollectionActivity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail(); // ACTIVIDAD DE COBROS

        $policy_sorts = PolicySort::query()
            ->select('policy_sorts.*', 'ps.*', 'af.first_name', 'af.doc_number', 'ps.transaction_date')
            ->leftJoin('activities as b', 'b.parent_id', '=', 'policy_sorts.activity_id')
            ->leftJoin('policy_sort_collections as ps', 'ps.activity_id', '=', 'b.id')
            ->leftJoin('activities as a', 'a.id', '=', 'policy_sorts.activity_id')
            ->leftJoin('affiliates as af', 'af.id', '=', 'a.affiliate_id')
            ->where('ps.activity_id', $id)
            ->first();


        DB::beginTransaction();
        try {

            $action_id = ACTION::REPORTAR_AUMENTO_SEGURO_PERIODO;
            $document = "insurance_increase_letter";
            $activityActionsCreated = ActionController::create(
                $policyCollectionActivity->id,
                $action_id,
                'REPORTAR AUMENTO SEGURO PERIODO'
            );

            //TODO: GENERAR DOCUMENTO
            $pdf = PDF::loadView("services.policy_sort_collection.docs.{$document}", [
                'activity' => $policyCollectionActivity,
                'watermark' => false,
            ]);

            $filePath = "policyCollectionSort/{$document}_{$action_id}.pdf";
            Storage::disk('s3')
                ->put("policyCollectionSort/{$document}_{$action_id}.pdf", $pdf->output());

            $activityActionDocument = new ActivityActionDocument();
            $activityActionDocument->activity_action_id = $activityActionsCreated->id;
            $activityActionDocument->name = $document;
            $activityActionDocument->path = "policyCollectionSort/{$document}_{$action_id}.pdf";
            $activityActionDocument->save();

            //TODO: ENVÍO DEL CORREO
            $affiliate = Affiliate::where('id', $policyCollectionActivity->affiliate_id)->first();
            $emails = [$affiliate->email];

            $name = ucwords(strtolower($policy_sorts->first_name));
            $poliza = sprintf('%04d', $policy_sorts->id);
            $mensaje = "¡Buen día, " . $name . "!\n\n" .
                "Nos complace informarle que hemos procesado satisfactoriamente el pago de su póliza #{$policy_sorts->formatSortNumber()} del Seguro Obligatorio de Riesgos del Trabajo.
                
                En caso de cualquier consulta adicional, por favor, contáctenos al 4102-7600. ¡Será un gusto servirle!
                
                ¡Muchas gracias por la confianza que ha depositado en nosotros! Nuestro propósito es transformar la protección en una experiencia ágil, confiable y humana.";

            $text = [
                "text" => $mensaje,
                "sender" => 'MNK Seguros'
            ];
            $attachments = [
                [
                    'path' => $filePath,
                    'name' => basename($filePath),
                    'type' => 'PDF'
                ]
            ];

            $mailSent = new SendDocumentDataBase(
                implode(',', $emails),
                "Carta aumento de seguro",
                "<EMAIL>",
                "Carta aumento de seguro",
                $text,
                "<EMAIL>",
                $attachments,
                "send_document_db",
                $client,
                request()->getHost(),
                $policyCollectionActivity->id,
                $activityActionsCreated->id,
                $policyCollectionActivity->service->id
            );
            
            // // Capturar el resultado del envío
            // $result = $mailSent->sendMail();

            // //Registramos los datos del correo enviado para la trazabilidad
            // $mailBoardController = new MailBoardController();
            // $mailBoardController->createRegisterMail(
            //     $policyCollectionActivity->id,
            //     $policyCollectionActivity->service->id, 
            //     $policy_sorts->consecutive, 
            //     'Intermediario', 
            //     $name, 
            //     '', 
            //     'Carta aumento de seguro', 
            //     $mensaje ,
            //     $emails, 
            //     $result,
            //     $attachments
            // );

            //TODO: CREAR TABLA DE COBROS
            $policy_sort_collection = new PolicySortCollection();
            $policy_sort_collection->activity_id = $id;
            $policy_sort_collection->type_receipt = PolicySortCollection::PERIOD_INCREASE;
            $policy_sort_collection->invoice_number = 'Aumento seguro-' . uniqid();
            $policy_sort_collection->total_amount = $req->input('total_amount') ?? 0;
            $policy_sort_collection->payment_status = PolicySortCollection::PAYMENT_STATUS_PENDING;
            $policy_sort_collection->due_date = $this->calculateDueDate(10); // 10 días habiles
            $policy_sort_collection->save();
            DB::commit();
        } catch (Exception $e) {
            DB::rollback();
            return response()->json(['error' => 'Error al realizar la acción'], 500);
        }
        return response()->json([
            'status' => 'success',
            'message' => 'Campos han sido actualizados correctamente'
        ]);
    }

    /**
     * Accion Reportar Recibo abono Mensual
     *
     *
     *
     */

    public function reportMonthlyPaymentReceipt(Request $req, $cpath = null, $id)
    {
        $policyCollectionActivity = Activity::where('id', $id)->firstOrFail(); // ACTIVIDAD DE COBROS
        $client = $policyCollectionActivity->client;

        $alreadyInTransaction = DB::transactionLevel() > 0;
        if (!$alreadyInTransaction) {
            DB::beginTransaction();
        }
        try {

            $action_id = ACTION::REPORTAR_RECIBO_ABONO_MENSUAL;
            $activityActionsCreated = ActionController::create(
                $policyCollectionActivity->id,
                $action_id,
                'REPORTAR RECIBO ABONO MENSUAL'
            );

            //TODO: CREAR TABLA DE COBROS
            $policy_sort_collection = new PolicySortCollection();
            $policy_sort_collection->activity_id = $id;
            $policy_sort_collection->type_receipt = PolicySortCollection::MONTHLY;
            $policy_sort_collection->invoice_number = 'Mensual-' . uniqid();
            $policy_sort_collection->total_amount = $req->input('total_amount') ?? 0;
            $policy_sort_collection->payment_status = PolicySortCollection::PAYMENT_STATUS_PENDING;

            //capturar la actividad de la póliza
            $activity_policy = $policyCollectionActivity->parent;
            //capturar la vigencia de la póliza (fecha desde)
            $validity_from = $this->getValidityFromPaymentReceipt($activity_policy);

            //fecha de vencimiento
            $policy_sort_collection->due_date = $this->calculateDueDateWithValidityFrom(10, $validity_from); // 10 días habiles
            $policy_sort_collection->save();

            $this->sendPaymentReceipt($client, $policyCollectionActivity, $activityActionsCreated, $action_id, '', $req->input('month_passed'));

            if (!$alreadyInTransaction) {
                DB::commit();
            }
        } catch (Exception $e) {
            DB::rollback();
            throw new \Exception($e);
        }
        return $policy_sort_collection;
    }

    public function sendPaymentReceipt($client, $activityPayment, $activityActionsCreated, $action_id, $pagado = null, $monthPassed = null)
    {

        try {

            date_default_timezone_set('America/Costa_Rica');
            $horaActual = new DateTime();
            $horaCostarrica = $horaActual->format('h:i:s A');
            $fechaCostarrica = $horaActual->format('d/m/Y');

            $activity_policy_collection = Activity::where('client_id', $client->id)->where('id', $activityPayment->id)->firstOrFail(); // ACTIVIDAD DE COBROS
            //$activity_policy = Activity::where('client_id', $client->id)->where('id',$activity_policy_collection->parent->id)->firstOrFail();
            $activity_policy = $activity_policy_collection->parent;

            $work_modality_id = $activity_policy->policy_sort->work_modality_id;
            $type_receipt = $activity_policy_collection->policy_sort_collection->type_receipt;

            $totalWords = NumberToWords::convertToWords($activity_policy_collection->policy_sort_collection->total_amount ?? 0, $activity_policy->policy_sort->type_currency);

            //fecha del recibo
            $validityFrom = Carbon::parse($activity_policy->policy_sort->validity_from);
            $validityTo = Carbon::parse($activity_policy->policy_sort->validity_to);
            $intervals = [
                0 => $validityTo,
                1 => $validityTo,
                2 => $validityFrom->copy()->addMonths(6)->subDay(),
                3 => $validityFrom->copy()->addMonths(3)->subDay(),
                4 => $validityFrom->copy()->addMonth()->subDay(),
            ];
            $validityTo = isset($intervals[$activity_policy->policy_sort->periodicity]) ? $intervals[$activity_policy->policy_sort->periodicity] : $validityTo;
            //buscar cuantos recibos lleva para sumarle los meses
            $receiptCount = Activity::where('parent_id', $activity_policy->id)
                ->where('service_id', Service::SERVICE_POLICY_SORT_COLLECTION_MNK)
                ->count();
            $receiptIndex = max(0, $receiptCount - 1);
            $monthsToAddArray = [
                4 => 1,
                3 => 3,
                2 => 6,
            ];
            $monthsToAdd = isset($monthsToAddArray[$activity_policy->policy_sort->periodicity])
                ? $monthsToAddArray[$activity_policy->policy_sort->periodicity]
                : 0;
            $monthPassed = $receiptIndex  * $monthsToAdd;

            if ($monthPassed) {
                $validityFrom = Carbon::parse($validityFrom)->addMonths($monthPassed);
                $validityTo = Carbon::parse($validityTo)->addMonths($monthPassed);
            }
            //formatear en dia/mes y año
            $validityFromFormatDDMMYY = $validityFrom->format('d/m/Y');
            $validityToFormatDDMMYY = $validityTo->format('d/m/Y');

            $quotation = $activity_policy->parent;
            $condiciones = ActivityAction::where('action_id', '=', ActionCotizacionSort::REPORTAR_CONDICIONES_ESPECIALES)->where('activity_id', '=', $quotation->id)->first();
 

            if ($condiciones) {
                $quotationCondition = QuotationConditionSpecial::where('activity_id', '=', $quotation->id)->first();
                [$descuentoCml, $descuentoClap, $base, $final, $descuentos_total,$sumaDescuentos] = array_values(
                    $this->policySortController->calcularDescuentos(
                        $activity_policy->policy_sort,
                        $quotationCondition
                    )
                );
            } else {
                $total = $activity_policy_collection->policy_sort_collection->total_amount;
            }

            //Preguntar si es recibo de abono
            $abono = false;
            if ($activityPayment->policy_sort_collection->type_receipt === PolicySortCollection::MONTHLY || $activityPayment->policy_sort_collection->type_receipt === PolicySortCollection::QUARTERLY || $activityPayment->policy_sort_collection->type_receipt === PolicySortCollection::SEMIANNUAL)
            {
                $abono = true;
                $validityFromFormatDDMMYY = isset($activityPayment->policy_sort_collection->validity_from) ? Carbon::parse($activityPayment->policy_sort_collection->validity_from)->format('d/m/Y') : $validityFromFormatDDMMYY;
                $validityToFormatDDMMYY = isset($activityPayment->policy_sort_collection->validity_to) ? Carbon::parse($activityPayment->policy_sort_collection->validity_to)->format('d/m/Y') : $validityToFormatDDMMYY;
            }

            $datos = [
                'activity_policy' => $activity_policy,
                'activity_policy_collection' => $activity_policy_collection,
                'totalWords' => $totalWords,
                'date_from' => $validityFromFormatDDMMYY,
                'date_to' => $validityToFormatDDMMYY,
                'watermark' => false,
                'horaCostarrica' => $horaCostarrica,
                'fechaCostarrica' => $fechaCostarrica,
                'pagado' => $pagado,
                'descuentoCml' => $condiciones ? $descuentoCml : null,
                'descuentoClap' => $condiciones ? $descuentoClap : null,
                'base' => $condiciones ? $base : $total,
                'final' => $condiciones ? $final : $total,
                'descuentos_total' => $condiciones ? $descuentos_total : 0,
                'sumaDescuentos'=> $condiciones ? $sumaDescuentos :0,
                'quotationCondition' => $condiciones ? $quotationCondition : null,
                'work_modality_id' => $work_modality_id,
                'type_receipt'=> $type_receipt ?? ''
            ];

            $indexDoc = $activity_policy_collection->policy_sort_collection->type_receipt == 'emission'
                ? $activity_policy->policy_sort->periodicity
                : $activity_policy_collection->policy_sort_collection->type_receipt;

            $document = AppServiceProvider::$PERIODICITY_RECEIPTS[$indexDoc] ?? 'unknown_receipt_type';

            $pdf = PDF::loadView("services.policy_sort_collection.docs.monthly_payment_receipt", $datos);

            $filePath = "policyCollectionSort/{$document}_{$activityPayment->id}.pdf";
            Storage::disk('s3')
                ->put("policyCollectionSort/{$document}_{$activityPayment->id}.pdf", $pdf->output());

            $activityActionDocument = new ActivityActionDocument();
            $activityActionDocument->activity_action_id = $activityActionsCreated->id;
            $activityActionDocument->name = $document;
            $activityActionDocument->path = "policyCollectionSort/{$document}_{$activityPayment->id}.pdf";
            $activityActionDocument->save();

            //TODO: ENVÍO DEL CORREO

            $emailAffiliate = $activity_policy_collection->affiliate->email ?? '';
            $emailPolicy = $activity_policy->policy_sort->email ?? '';

            $username = preg_replace('/^CO-/', '', $activity_policy->policy_sort->code); // Remover el prefijo "CO-"

            $user = User::where(function ($query) use ($username) {
                $query->where('username', $username) // Buscar directamente sin "CO-"
                    ->orWhere('username', 'CO-' . $username); // Buscar con "CO-"
            })->first();

            $userEmailCorredor = isset($user) ? explode(' ', trim($user->new_business_email ?? ''))[0] : '';

            $emails = array_filter([$emailAffiliate, $userEmailCorredor, $emailPolicy], function ($email) {
                return !empty($email);
            });

            $numPoliza = $activity_policy->policy_sort->consecutive ? $activity_policy->policy_sort->formatNumberConsecutive() : '';
            $nameTaker = mb_convert_case(mb_strtolower($activity_policy->affiliate->full_name ?? ''), MB_CASE_TITLE, "UTF-8");
            $datePyment = ucfirst(strftime('%A %e de %B del %Y', strtotime($activity_policy_collection->policy_sort_collection->due_date)));

            $subject = '';
            $body = '';
            $sender = '';
            if ($pagado) {
                if ($numPoliza) {
                    $subject =  "Pago de póliza #" . $numPoliza;
                } else {
                    $subject =  "Pago de póliza";
                }
                $body = "¡Buen día, $nameTaker!,

                            Nos complace informarle que hemos procesado satisfactoriamente el pago de su póliza #$numPoliza del Seguro Obligatorio de Riesgos del Trabajo.
                            
                            En caso de cualquier consulta adicional, por favor, contáctenos al 4102-7600. ¡Será un gusto servirle!

                            ¡Muchas gracias por la confianza que ha depositado en nosotros! Nuestro propósito es transformar la protección en una experiencia ágil, confiable y humana.";

                if ($abono == true){
                    $emailData = TemplateBuilder::build(
                        Templates::PAY_POLICY_COLLECTION,
                        [
                            'policy_sort' => $numPoliza,
                            'name_taker' => mb_convert_case(mb_strtolower($activity_policy->affiliate->full_name ?? ''), MB_CASE_TITLE, "UTF-8"),
                            'url' =>config('app.url'),
                        ]
                    );
                    $subject = $emailData['subject'];
                    $body = $emailData['body'];
                }

                $sender = Senders::MNK_INSURANCE;
            } else {
                $validityFromFormat = $validityFrom->formatLocalized('%e de %B del %Y');
                $validityToFormat = $validityTo->formatLocalized('%e de %B del %Y');

                $emailData = TemplateBuilder::build(
                    Templates::PAYMENT_COLLECTION,
                    [
                        'policy_sort' => $numPoliza,
                        'name_taker' => mb_convert_case(mb_strtolower($activity_policy->affiliate->full_name ?? ''), MB_CASE_TITLE, "UTF-8"),
                        'start_period' => $validityFromFormat,
                        'end_period' => $validityToFormat,
                        'payment_date' => $datePyment,
                    ]
                );
                $subject = $emailData['subject'];
                $body = $emailData['body'];
                $sender = $emailData['sender'];
            }

            $attachments = [
                [
                    'path' => $filePath,
                    'name' => basename($filePath),
                    'type' => 'PDF'
                ]
            ];

            $mailSent = new SendDocumentDataBase(
                implode(',', $emails),
                $subject,
                "<EMAIL>",
                "",
                [
                    "text" => $body,
                    "sender" => $sender
                ],
                "<EMAIL>",
                $attachments,
                "send_document_db",
                $client,
                request()->getHost(),
                $activity_policy_collection->id,
                $activityActionsCreated->id,
                $activity_policy_collection->service->id
            );
            
            // Capturar el resultado del envío
            $result = $mailSent->sendMail();

            //Registramos los datos del correo enviado para la trazabilidad
            $mailBoardController = new MailBoardController();
            $mailBoardController->createRegisterMail(
                $activity_policy_collection->id,
                $activity_policy_collection->service->id, 
                $activity_policy->policy_sort->consecutive, 
                'Tomador', 
                //Nombre del tomador
                $nameTaker, 
                //documento del tomador
                $activity_policy->affiliate->doc_number, 
                $subject, 
                $body,
                $emails, 
                $result,
                $attachments
            );

        } catch (\Exception $e) {
            throw new \Exception('Error en el envio del correo de aprobación de pago ' . $e->getMessage());
        }
    }

    /**
     * Cron para anular pago despues de 11 dias
     *
     */
    public function reportInsuranceIncreaseUnpaid()
    {

        $elevenDaysAgo = Carbon::now()->subDays(10)->toDateString();
        $activityIds = Activity::where('service_id', Service::SERVICE_POLICY_SORT_COLLECTION_MNK)
            ->where('state_id', State::RECIBO_AUMENTO_SEGURO_PENDIENTE_PAGO)
            ->pluck('id');

        $results = PolicySortCollection::whereIn('activity_id', $activityIds)
            ->where('payment_status', 'pending')
            ->whereRaw('DATE_ADD(due_date, INTERVAL 1 DAY) <= CURRENT_DATE')
            ->whereNotNull('due_date')
            ->get();

        DB::beginTransaction();
        try {
            foreach ($results as $result) {
                $result->payment_status = 'cancelled';
                $result->save();

                $activityAction = ActionController::create(
                    $result->activity_id,
                    Action::REPORTAR_RECIBO_AUMENTO_SEGURO_NO_PAGADO,
                    'Anular Pago'
                );
            }
            DB::commit();
        } catch (Exception $e) {
            DB::rollback();

            return response()->json([
                'message' => 'No se pudieron anular los Pagos correctamente',
                'error' => $e->getMessage()
            ], 500);

        }

        return response()->json([
            'message' => 'Se Anularon los Pagos de incremento correctamente.',
        ], 200);

    }

    public function cronMonthlyNoPaymentReceipt()
    {

        $id = uniqid();
        if (\Cache::has('cron_monthly_no_payement')) {
            return response()->json([
                'status' => 200,
                'message' => 'Ya hay una validación en progreso',
            ], 200);

        }
        \Cache::put('cron_monthly_no_payement', $id, now()->addMinutes(5));

        $activityIds = Activity::where('service_id', Service::SERVICE_POLICY_SORT_COLLECTION_MNK)
            ->where('state_id', State::RECIBO_ABONO_MENSUAL_PENDIENTE_PAGO)
            ->get(['id', 'parent_id']);

        $ids = $activityIds->pluck('id');

        $results = PolicySortCollection::whereIn('activity_id', $ids)
            ->where('payment_status', 'pending')
            ->whereRaw('DATE_ADD(due_date, INTERVAL 1 DAY) <= CURRENT_DATE')
            ->whereNotNull('due_date')
            ->get();
        $count = 0;
        $errors = 0;

        foreach ($results as $result) {
            try {
                DB::beginTransaction();
                $result->payment_status = 'cancelled';
                $result->save();
                $activityAction = ActionController::create(
                    $result->activity_id,
                    Action::REPORTAR_RECIBO_ABONO_MENSUAL_NO_PAGO,
                    'Anular Pago Mensual'
                );
                $activity = $activityIds->firstWhere('id', $result->activity_id);
                $policy = new PolicySortController();
                $policy->suspensionPayment($activity, $result->due_date);
                DB::commit();
                $count++;
            } catch (Exception $e) {
                DB::rollback();
                $errors ++;
                continue;
            }
        }
        return response()->json([
            'status' => 'SUCCESS',
            'message' => 'Se Anularon los Pagos correctamente',
            'count' => $count,
        ], 200);
    }

    public function cronBiannualNoPaymentReceipt()
    {
        $id = uniqid();
        if (\Cache::has('cron_cron_biannual_no_payement_no_payement')) {
            return response()->json([
                'status' => 200,
                'message' => 'Ya hay una validación en progreso',
            ], 200);

        }
        \Cache::put('cron_biannual_no_payement', $id, now()->addMinutes(5));
        $elevenDaysAgo = Carbon::now()->subDays(10)->toDateString();
        $activityIds = Activity::where('service_id', Service::SERVICE_POLICY_SORT_COLLECTION_MNK)
            ->where('state_id', State::RECIBO_ABONO_SEMESTRAL_PENDIENTE_PAGO)
            ->get(['id', 'parent_id']);
        $ids = $activityIds->pluck('id');

        $results = PolicySortCollection::whereIn('activity_id', $ids)
            ->where('payment_status', 'pending')
            ->whereRaw('DATE_ADD(due_date, INTERVAL 1 DAY) <= CURRENT_DATE')
            ->whereNotNull('due_date')
            ->get();

        $count = 0;
        $errors = 0;
        foreach ($results as $result) {
            try {
                DB::beginTransaction();
                $result->payment_status = 'cancelled';
                $result->save();

                $activityAction = ActionController::create(
                    $result->activity_id,
                    Action::REPORTAR_RECIBO_ABONO_SEMESTRAL_NO_PAGADO,
                    'Anular Pago Trimestral'
                );
                $activity = $activityIds->firstWhere('id', $result->activity_id);
                $policy = new PolicySortController();
                $policy->suspensionPayment($activity, $result->due_date);

                DB::commit();
                $count++;
            } catch (Exception $e) {
                DB::rollback();
                continue;
            }

        }

        return response()->json([
            'status' => 'SUCCESS',
            'message' => 'Se Anularon los Pagos correctamente',
            'count' => $count,
        ], 200);
    }

    public function cronQuarterlyNoPaymentReceipt()
    {
        $id = uniqid();
        if (\Cache::has('cron_quarterly_no_payement')) {
            return response()->json([
                'status' => 200,
                'message' => 'Ya hay una validación en progreso',
            ], 200);

        }
        \Cache::put('cron_quarterly_no_payement', $id, now()->addMinutes(5));
        $elevenDaysAgo = Carbon::now()->subDays(10)->toDateString();
        $activityIds = Activity::where('service_id', Service::SERVICE_POLICY_SORT_COLLECTION_MNK)
            ->where('state_id', StatePolicySortCollection::RECIBO_ABONO_TRIMESTRAL_PENDIENTE_PAGO)
            ->get(['id', 'parent_id']);
        $ids = $activityIds->pluck('id');

        $results = PolicySortCollection::whereIn('activity_id', $ids)
            ->where('payment_status', 'pending')
            ->whereRaw('DATE_ADD(due_date, INTERVAL 1 DAY) <= CURRENT_DATE')
            ->whereNotNull('due_date')
            ->get();
        $count = 0;
        $errors = 0;
        foreach ($results as $result) {
            try {
                DB::beginTransaction();
                $result->payment_status = 'cancelled';
                $result->save();

                $activityAction = ActionController::create(
                    $result->activity_id,
                    Action::REPORTAR_RECIBO_ABONO_TRIMESTRAL_NO_PAGADO,
                    'Anular Pago Trimestral'
                );
                $activity = $activityIds->firstWhere('id', $result->activity_id);
                $policy = new PolicySortController();
                $policy->suspensionPayment($activity, $result->due_date);
                DB::commit();
            } catch (Exception $e) {
                DB::rollback();
                continue;
            }
        }

        return response()->json([
            'status' => 'SUCCESS',
            'message' => 'Se Anularon los Pagos correctamente',
            'count' => $count,
        ], 200);
    }

    public function reportBiannualPaymentReceipt(Request $req, $cpath, $id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $policyCollectionActivity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail(); // ACTIVIDAD DE COBROS

        DB::beginTransaction();
        try {

            $action_id = ACTION::REPORTAR_RECIBO_ABONO_SEMESTRAL;

            $activityActionsCreated = ActionController::create(
                $policyCollectionActivity->id,
                $action_id,
                'REPORTAR RECIBO ABONO SEMESTRAL'
            );

            //TODO: CREAR TABLA DE COBROS
            $policy_sort_collection = new PolicySortCollection();
            $policy_sort_collection->activity_id = $id;
            $policy_sort_collection->type_receipt = PolicySortCollection::SEMIANNUAL;
            $policy_sort_collection->invoice_number = 'Semestral-' . uniqid();
            $policy_sort_collection->total_amount = $req->input('total_amount') ?? 0;
            $policy_sort_collection->payment_status = PolicySortCollection::PAYMENT_STATUS_PENDING;

            //capturar la actividad de la póliza
            $activity_policy = $policyCollectionActivity->parent;
            //capturar la vigencia de la póliza (fecha desde)
            $validity_from = $this->getValidityFromPaymentReceipt($activity_policy);

            //fecha de vencimiento
            $policy_sort_collection->due_date = $this->calculateDueDateWithValidityFrom(10, $validity_from); // 10 días habiles
            $policy_sort_collection->save();

            $this->sendPaymentReceipt($client, $policyCollectionActivity, $activityActionsCreated, $action_id);


            DB::commit();
        } catch (Exception $e) {
            DB::rollback();
            return response()->json(['error' => 'Error al realizar la acción'], 500);
        }
        return response()->json([
            'status' => 'success',
            'message' => 'Campos han sido actualizados correctamente'
        ]);
    }

    public function reportquarterlyPaymentReceipt(Request $req, $cpath, $id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $policyCollectionActivity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail(); // ACTIVIDAD DE COBROS

        DB::beginTransaction();
        try {

            $action_id = ACTION::REPORTAR_RECIBO_ABONO_TRIMESTRAL;
            $document = "monthly_payment_receipt";
            $activityActionsCreated = ActionController::create(
                $policyCollectionActivity->id,
                $action_id,
                'REPORTAR RECIBO ABONO TRIMESTRAL'
            );

            //TODO: CREAR TABLA DE COBROS
            $policy_sort_collection = new PolicySortCollection();
            $policy_sort_collection->activity_id = $id;
            $policy_sort_collection->type_receipt = PolicySortCollection::QUARTERLY;
            $policy_sort_collection->invoice_number = 'Trimestral-' . uniqid();
            $policy_sort_collection->total_amount = $req->input('total_amount') ?? 0;
            $policy_sort_collection->payment_status = PolicySortCollection::PAYMENT_STATUS_PENDING;

            //capturar la actividad de la póliza
            $activity_policy = $policyCollectionActivity->parent;
            //capturar la vigencia de la póliza (fecha desde)
            $validity_from = $this->getValidityFromPaymentReceipt($activity_policy);

            //fecha de vencimiento
            $policy_sort_collection->due_date = $this->calculateDueDateWithValidityFrom(10, $validity_from); // 10 días habiles
            $policy_sort_collection->save();

            $this->sendPaymentReceipt($client, $policyCollectionActivity, $activityActionsCreated, $action_id);

            DB::commit();
        } catch (Exception $e) {
            DB::rollback();
            return response()->json(['error' => 'Error al realizar la acción'], 500);
        }
        return response()->json([
            'status' => 'success',
            'message' => 'Campos han sido actualizados correctamente'
        ]);
    }

    public function reportRehabilitationPaymentReceipt(Request $req, $cpath, $id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $policyCollectionActivity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail(); // ACTIVIDAD DE COBROS

        DB::beginTransaction();
        try {

            $action_id = ACTION::REPORTAR_RECIBO_REHABILITACION;
            $document = "rehabilitation_payment_receipt";
            $activityActionsCreated = ActionController::create(
                $policyCollectionActivity->id,
                $action_id,
                'REPORTAR RECIBO REHABILITACIÓN'
            );

            //TODO: GENERAR DOCUMENTO
            $pdf = PDF::loadView("services.policy_sort_collection.docs.{$document}", [
                'activity' => $policyCollectionActivity,
                'watermark' => false,
            ]);

            $filePath = "policyCollectionSort/{$document}_{$action_id}.pdf";
            Storage::disk('s3')
                ->put("policyCollectionSort/{$document}_{$action_id}.pdf", $pdf->output());

            $activityActionDocument = new ActivityActionDocument();
            $activityActionDocument->activity_action_id = $activityActionsCreated->id;
            $activityActionDocument->name = $document;
            $activityActionDocument->path = "policyCollectionSort/{$document}_{$action_id}.pdf";
            $activityActionDocument->save();

            //TODO: ENVÍO DEL CORREO
            $affiliate = Affiliate::where('id', $policyCollectionActivity->affiliate_id)->first();
            $emails = [$affiliate->email];

            $text = [
                "text" => "Estimado cliente, adjunto encontrará el recibo de pago rehabilitación.",
                "sender" => 'MNK Seguros'
            ];
            $attachments = [
                [
                    'path' => $filePath,
                    'name' => basename($filePath),
                    'type' => 'PDF'
                ]
            ];

            $mailSent = new SendDocumentDataBase(
                implode(',', $emails),
                "Recibo de pago rehabilitación",
                "<EMAIL>",
                "Recibo de pago rehabilitación",
                $text,
                "<EMAIL>",
                $attachments,
                "send_document_db",
                $client,
                request()->getHost(),
                $policyCollectionActivity->id,
                $activityActionsCreated->id,
                $policyCollectionActivity->service->id
            );
            
             // Capturar el resultado del envío
            $result = $mailSent->sendMail();

            //Registramos los datos del correo enviado para la trazabilidad
            $mailBoardController = new MailBoardController();
            $mailBoardController->createRegisterMail(
                $policyCollectionActivity->id,
                $policyCollectionActivity->service->id, 
                $policyCollectionActivity->parent->policy_sort->consecutive, 
                'Tomador', 
                $policyCollectionActivity->parent->affiliate->full_name, 
                $policyCollectionActivity->parent->affiliate->doc_number, 
                'Recibo de pago rehabilitación', 
                $text,
                $emails, 
                $result,
                $attachments
            );

            //TODO: CREAR TABLA DE COBROS
            $policy_sort_collection = new PolicySortCollection();
            $policy_sort_collection->activity_id = $id;
            $policy_sort_collection->type_receipt = PolicySortCollection::REHABILITATION;
            $policy_sort_collection->invoice_number = 'REHABILITACIÓN-' . uniqid();
            $policy_sort_collection->total_amount = $req->input('total_amount') ?? 0;
            $policy_sort_collection->payment_status = PolicySortCollection::PAYMENT_STATUS_PENDING;
            $policy_sort_collection->due_date = $this->calculateDueDate(10); // 10 días habiles
            $policy_sort_collection->save();

            DB::commit();
        } catch (Exception $e) {
            DB::rollback();
            return response()->json(['error' => 'Error al realizar la acción'], 500);
        }
        return response()->json([
            'status' => 'success',
            'message' => 'Campos han sido actualizados correctamente'
        ]);
    }

    public function cronRehabilitationNoPaymentReceipt()
    {
        $elevenDaysAgo = Carbon::now()->subDays(10)->toDateString();
        $activityIds = Activity::where('service_id', Service::SERVICE_POLICY_SORT_COLLECTION_MNK)
            ->where('state_id', State::RECIBO_REHABILITACION_PENDIENTE_PAGO)
            ->pluck('id');

        $results = PolicySortCollection::whereIn('activity_id', $activityIds)
            ->where('payment_status', 'pending')
            ->whereRaw('DATE_ADD(due_date, INTERVAL 1 DAY) <= CURRENT_DATE')
            ->whereNotNull('due_date')
            ->get();

        DB::beginTransaction();
        try {
            foreach ($results as $result) {
                $result->payment_status = 'cancelled';
                $result->save();


                $activityAction = ActionController::create(
                    $result->activity_id,
                    Action::REPORTAR_RECIBO_REHABILITACION_NO_PAGADO,
                    'Anular Pago Rehabilitación'
                );
            }
            DB::commit();
        } catch (Exception $e) {
            DB::rollback();

            return response()->json([
                'status' => 'ERROR',
                'message' => 'No se pudieron anular los Pagos correctamente',
                'error' => $e->getMessage()
            ], 500);
        }

        return response()->json([
            'status' => 'SUCCESS',
            'message' => 'Se Anularon los Pagos correctamente'
        ], 200);
    }

    public function updatePayment(Request $req, $cpath, $id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();
        $collection = Activity::where('parent_id', $id)
            ->where('service_id', Service::SERVICE_POLICY_SORT_COLLECTION_MNK)
            ->firstOrFail();
        DB::beginTransaction();
        try {
            if ($collection->state_id == State::RECIBO_AUMENTO_SEGURO_PENDIENTE_PAGO) {
                $collection->state_id = State::RECIBO_AUMENTO_SEGURO_PAGADO;
                $activityAction = ActionController::create(
                    $activity->id,
                    Action::REPORTAR_PAGO_AUMENTO_SEGURO_PERIODO,
                    'Pago Aumento Seguro'
                );
            } elseif ($collection->state_id == State::RECIBO_ABONO_MENSUAL_PENDIENTE_PAGO) {
                $collection->state_id = State::RECIBO_ABONO_MENSUAL_PAGADO;
                $activityAction = ActionController::create(
                    $activity->id,
                    Action::REPORTAR_PAGO_RECIBO_ABONO_MENSUAL,
                    'Pago Abono Mensual Realizado'
                );
            } elseif ($collection->state_id == State::RECIBO_ABONO_SEMESTRAL_PENDIENTE_PAGO) {
                $collection->state_id = State::RECIBO_ABONO_SEMESTRAL_PAGADO;
                $activityAction = ActionController::create(
                    $activity->id,
                    Action::REPORTAR_PAGO_RECIBO_ABONO_SEMESTRAL,
                    'Pago Abono Semestral Realizado'
                );
            } elseif ($collection->state_id == State::RECIBO_ABONO_TRIMESTRAL_PENDIENTE_PAGO) {
                $collection->state_id = State::RECIBO_ABONO_TRIMESTRAL_PAGADO;
                $activityAction = ActionController::create(
                    $activity->id,
                    Action::REPORTAR_PAGO_RECIBO_ABONO_TRIMESTRAL,
                    'Pago Abono Trimestral Realizado'
                );
            } elseif ($collection->state_id == State::RECIBO_REHABILITACION_PENDIENTE_PAGO) {
                $collection->state_id = State::RECIBO_REHABILITACION_PAGADO;
                $activityAction = ActionController::create(
                    $activity->id,
                    Action::REPORTAR_PAGO_RECIBO_REHABILITACION,
                    'Pago Recibo Rehabilitación Realizado'
                );
            } elseif ($collection->state_id == State::FACTURA_ELECTRONICA_PENDIENTE_PAGO) {
                $collection->state_id = State::FACTURA_ELECTRONICA_PAGADA;
                $activityAction = ActionController::create(
                    $activity->id,
                    Action::REPORTAR_PAGO_EMISION_POLIZA_REALIZADO,
                    'Pago Emision de Poliza Realizado'
                );
                $policy_sort = new PolicySortController();
                $policy_sort->reportPaymentMade($req, $cpath, $id);
            }
            $collection->save();
        } catch (Exception $e) {
            DB::rollback();
            return [
                'status' => 'ERROR',
                'message' => 'No se pudo actualizar el pago correctamente'
            ];
        }
        DB::commit();
        return [
            'status' => 'SUCCESS',
            'message' => 'Se actualizó el pago correctamente'
        ];
    }

    /**
     * Listado de recibos, recibos pendientes
     *
     * @param Request $req
     * @param $cpath
     * @param $id
     * @param $npoliza
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\Foundation\Application|\Illuminate\View\View
     */
    public function pendingReceipts(Request $req, $cpath, $id, $npoliza)
    {
        $client = Client::query()->where('path', $cpath)->firstOrFail();
        $data = $this->pendingReceiptsForDataViews($client, $id, $npoliza);

        return view(
            'services.policy_sort.holder_policy.menu.pending_receipts',
            [
                'id' => $id,
                'npoliza' => $npoliza,
                'listado_recibos' => $data['listado_recibos'],
                'messageSpreadsheets' => $data['messageSpreadsheets'],
                'active' => 'recibos_pendientes'
            ]
        );
    }

    protected function pendingReceiptsForDataViews($client, $id, $npoliza)
    {
        $listadoRecibos = DB::table('policy_sort_collections as psc')
            ->join('activities as act_servicio', 'psc.activity_id', '=', 'act_servicio.id')
            ->join('activities as act_poliza', 'act_servicio.parent_id', '=', 'act_poliza.id')
            ->join('policy_sorts as ps', 'ps.activity_id', '=', 'act_poliza.id')
            ->join('affiliates as aff', 'aff.id', '=', 'act_poliza.affiliate_id')
            ->where('ps.id', $npoliza)
            ->whereNull('act_servicio.deleted_at')
            ->select(
                DB::raw("CONCAT('SORT-', LPAD(ps.consecutive, 4, '0')) as no_poliza"), // Formatear no_poliza
                'ps.consecutive as consecutive',
                'ps.activity_id as id_actividad_poliza',
                'ps.type_currency as tipo_moneda',
                'aff.first_name as nombre_tomador',
                'psc.id as id_cobro',
                'psc.invoice_number as no_recibo',
                'psc.type_receipt as tipo_recibo',
                'psc.total_amount as monto_a_pagar',
                'psc.created_at as fecha_emision_recibo',
                'psc.due_date as fecha_limite_pago',
                'psc.due_date as dias_para_vencimiento',
                'psc.payment_status as estado_pago',
                'psc.activity_id as id_actividad_cobros'
            )->orderBy('psc.created_at', 'desc')->paginate(10);


        $messageSpreadsheets = false;
        $policy_sorts = PolicySort::where('id', $npoliza)->first();

        //actividad economica que requiere planilla

        $economicActivities =  AppServiceProvider::$ECONOMIC_ACTIVITES_WHITHOUT_SPREADSHEET;

        if ($policy_sorts && in_array($policy_sorts->activity_economic_id, $economicActivities)) {
            $countSpreadsheets = PolicySpreadsheet::leftJoin('activities as a', 'a.id', '=', 'policy_spreadsheets.activity_id')
                ->leftJoin('policy_sorts as p', 'p.activity_id', '=', 'a.parent_id')
                ->where('p.id', $npoliza)
                ->count();
            if ($countSpreadsheets == 0) {
                $messageSpreadsheets = true;
            }
        }

        return [
            'listado_recibos' => $listadoRecibos,
            'messageSpreadsheets' => $messageSpreadsheets
        ];
    }

    public function payPolicy(Request $request, $cpath, $idCobro)
    {
        /**
         * Proceso de simulacion de pago MS-618, actualizo el estado del pago de la poliza
         */
        $statuses = ['paid', 'cancelled'];
        $randomState = $statuses[array_rand($statuses)];

        // Actualizo el pago simulado
        $policyCollections = PolicySortCollection::find($idCobro);
        $policyCollections->payment_status = $randomState;
        $policyCollections->save();

        return back();
    }

    public function registerPaymentCc(Request $req, $cpath, $id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        DB::beginTransaction();

        try {
            //TODO: Capturar la actividad de cobros
            $activityPolicySortCollection = Activity::where('id', $id)->firstOrFail();

            //TODO: Generar documento para la orden de emisión imágenes diagnósticas
            // Generar el PDF
            $document = 'register_payment_cc';
            $descripcion = "REGISTRAR PAGO TC";
            $action_id = ActionPolicySortCollection::REGISTRAR_PAGO_TC;

            $pdf = PDF::loadView("services.policy_sort_collection.docs.{$document}_pdf", ['value' => "pendiente"]); //modificar

            // Guardar el PDF en S3
            Storage::disk('s3')
                ->put("activity_action_document/{$document}_{$action_id}.pdf", $pdf->output());


            //TODO: Cambiar el estado de la actividad
            $activityActionsCreated = ActionController::create(
                $activityPolicySortCollection->id,
                $action_id,
                $descripcion
            );

            $activityActionDocument = new ActivityActionDocument();
            $activityActionDocument->activity_action_id = $activityActionsCreated->id;
            $activityActionDocument->name = $document;
            $activityActionDocument->path = "activity_action_document/{$document}_{$action_id}.pdf";
            $activityActionDocument->save();

            //TODO: Enviar un correo al tomador
            $policy = PolicySort::where('activity_id', $activityPolicySortCollection->parent_id)->first();
            $emails = [$policy->email];

            // Definir el texto del correo
            $text = [
                "text" => "Estimado cliente, su pago se ha sido registrado exitosamente.",
                "sender" => 'MNK Seguros'
            ];
            // Definir archivos adjuntos
            $affiliate_files = [
                [
                    'path' => "activity_action_document/{$document}_{$action_id}.pdf",
                    'name' => "{$document}_{$action_id}.pdf",
                    'type' => 'PDF'
                ]
            ];

            $mailSent = new SendDocumentDataBase(
                implode(',', $emails),
                "Factura Electrónica",
                "<EMAIL>",
                "Factura electrónica prueba mail",
                $text,
                "<EMAIL>",
                $affiliate_files,
                "send_document_db",
                $client,
                $req->getHost(),
                $activityPolicySortCollection->id,
                $activityActionsCreated->id,
                $activityPolicySortCollection->service->id
            );
            
                // Capturar el resultado del envío
            $result2 = $mailSent->sendMail();

            //Registramos los datos del correo enviado para la trazabilidad
            $mailBoardController = new MailBoardController();
            $mailBoardController->createRegisterMail(
                $activityPolicySortCollection->id,
                $activityPolicySortCollection->service->id, 
                $activityPolicySortCollection->parent->policy_sort->consecutive, 
                'Tomador', 
                $activityPolicySortCollection->parent->affiliate->full_name, 
                $activityPolicySortCollection->parent->affiliate->doc_number, 
                'Factura Electrónica', 
                $text,
                $emails, 
                $result2,
                $affiliate_files
            );

            //TODO: Actualizar estado de cobros con la acción REPORTAR_PAGO_EMISION_POLIZA_REALIZADO
            // Actualizar el estado de la poliza y de la cotización
            $result = $this->reportPolicyPaymentMade($activityPolicySortCollection, $cpath, $req);

            DB::commit();
            //TODO: Redireccionar al resumen de la actividad
            $redirectUrl = "/intermediario/poliza/$activityPolicySortCollection->parent_id/pago/resumen";
            return response()->json(['redirect' => $redirectUrl]);
        } catch (Exception $e) {
            DB::rollback();
            return response()->json(['error' => 'No se pudo registrar el pago'], 500);
        }
    }

    //ACCION REGISTRAR PAGO TRANSAFERENCIA BANCARIA TB de los pago TB
    public function registerPaymentTb(Request $request, $cpath, $id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();
        $policySortCollection = $activity->policy_sort_collection;
        $enabled_states = [
            StatePolicySortCollection::RECIBO_AUMENTO_SEGURO_PENDIENTE_PAGO,
            StatePolicySortCollection::RECIBO_ABONO_MENSUAL_PENDIENTE_PAGO,
            StatePolicySortCollection::RECIBO_ABONO_TRIMESTRAL_PENDIENTE_PAGO,
            StatePolicySortCollection::RECIBO_ABONO_SEMESTRAL_PENDIENTE_PAGO,
            StatePolicySortCollection::RECIBO_REHABILITACION_PENDIENTE_PAGO,
            StatePolicySortCollection::PENDIENTE_PAGO_RECIBO_EMISION,
            StatePolicySortCollection::RECIBO_LIQUIDACION_PENDIENTE_PAGO,
            StatePolicySortCollection::RECIBO_RENOVACION_PENDIENTE_PAGO
        ];
        $state = in_array($activity->state_id, $enabled_states);

        if ($policySortCollection) {
            DB::beginTransaction();

            if ($state) {
                try {
                    $file = $request->file('transfer_file');
                    $path = "activity_action_document/policy_sort_collection_{$activity->id}.pdf";
                    Storage::disk('s3')->put($path, file_get_contents($file->getRealPath()));
                    $policySortCollection->transaction_id = $request->transfer_receipt;
                    $policySortCollection->invoice_tb = $path;
                    $policySortCollection->payment_status = PolicySortCollection::PAYMENT_STATUS_PENDING_APPROVAL;
                    $policySortCollection->transaction_date = Carbon::now();

                    date_default_timezone_set('America/Costa_Rica');
                    $horaActual = new DateTime();
                    $horaFormateada = $horaActual->format('H:i:s');

                    $policySortCollection->time_transaction = $horaFormateada;
                    $policySortCollection->payment_method = 'TB';
                    $policySortCollection->account_id = $request->account_details_summit;
                    $policySortCollection->bank_id = $request->bank_account_summit;

                    $webserviceController = new WebserviceAcselController();
                    $trm = $webserviceController->getTrm();
                    $policySortCollection->trm = $trm;
                    $policySortCollection->save();

                    register_shutdown_function(function () use ($activity) {
                        $webserviceFac = new WebserviceElectronicInvoiceController();
                        $resp = $webserviceFac->sendInvoice($activity->id);
                    });

                    $message = '';
                    switch ($activity->state_id) {
                        case StatePolicySortCollection::RECIBO_AUMENTO_SEGURO_PENDIENTE_PAGO:
                            $message = 'Registrar pago tb aumento del segur periodo';
                            $action = ActionPolicySortCollection::REGISTRAR_PAGO_TB_AUMENTO_SEGURO_PERIODO;
                            break;
                        case StatePolicySortCollection::RECIBO_ABONO_MENSUAL_PENDIENTE_PAGO:
                            $message = 'Registrar pago tb recibo abono mensual';
                            $action = ActionPolicySortCollection::REGISTRAR_PAGO_TB_RECIBO_ABONO_MENSUAL;
                            break;
                        case StatePolicySortCollection::RECIBO_ABONO_TRIMESTRAL_PENDIENTE_PAGO:
                            $action = ActionPolicySortCollection::REGISTRAR_PAGO_TB_RECIBO_ABONO_TRIMESTRAL;
                            $message = 'Registrar pago tb recibo abono trimestral';
                            break;
                        case StatePolicySortCollection::RECIBO_ABONO_SEMESTRAL_PENDIENTE_PAGO:
                            $message = 'Registrar pago tb recibo abono semestral';
                            $action = ActionPolicySortCollection::REGISTRAR_PAGO_TB_RECIBO_ABONO_SEMESTRAL;
                            break;
                        case StatePolicySortCollection::RECIBO_REHABILITACION_PENDIENTE_PAGO:
                            $message = 'Registrar pago tb recibo rehabilitación';
                            $action = ActionPolicySortCollection::REGISTRAR_PAGO_TB_RECIBO_REHABILITACION;
                            break;
                        case StatePolicySortCollection::PENDIENTE_PAGO_RECIBO_EMISION:
                            $action = ActionPolicySortCollection::REGISTRAR_PAGO_TB;
                            $message = 'Registrar pago tb recibo emision';
                            break;
                        case StatePolicySortCollection::RECIBO_LIQUIDACION_PENDIENTE_PAGO:
                            $message = 'Registrar pago tb recibo liquidación';
                            $action = ActionPolicySortCollection::REGISTRAR_PAGO_TB_RECIBO_LIQUIDACION;
                            break;
                        case StatePolicySortCollection::RECIBO_RENOVACION_PENDIENTE_PAGO:
                            $message = 'Registrar pago tb recibo renovación';
                            $action = ActionPolicySortCollection::REGISTRAR_PAGO_TB_RECIBO_RENOVACION;
                            break;
                        default:
                            $message = 'REGISTRO PAGO';
                            break;
                    }

                    //Actualizar estado de la actividad
                    $activity_action = ActionController::create($activity->id, $action, $message);

                    //Cargar documento en activity documents
                    $activityDocument = new ActivityDocument();
                    $activityDocument->activity_id = $activity->id;
                    $activityDocument->document_id = PolicySortCollection::ACTIVITY_DOCUMENT_BANK_TRANSFER_DOCUMENT;
                    $activityDocument->path = $path;
                    $activityDocument->uploaded_at = Carbon::now();
                    $activityDocument->save();

                    //$data = $this->spreadsheetsData($activity->id);

                    DB::commit();
                    return redirect()->back()
                        ->with('success', 'Se cargó correctamente el comprobante de pago.')
                        ->with('info', 'Se esta verificando el pago de la transferencia bancaria, si el pago fue aprobado o rechazado se le notificará por correo electrónico');
                } catch (Exception $e) {
                    DB::rollback();
                    return response()->json(['error' => 'Error al realizar la acción'], 500);
                }
            } elseif ($activity->state_id == StatePolicySortCollection::PENDIENTE_VALIDACION_PAGO) {
                try {
                    $file = $request->file('transfer_file');
                    $path = "activity_action_document/policy_sort_collection_{$activity->id}.pdf";
                    Storage::disk('s3')->put($path, file_get_contents($file->getRealPath()));
                    $policySortCollection->transfer_receipt = $request->transfer_receipt;
                    $policySortCollection->invoice_tb = $path;
                    $policySortCollection->account_id = $request->account_details_summit;
                    $policySortCollection->bank_id = $request->bank_account_summit;

                    $policySortCollection->save();

                    $activityDocument = ActivityDocument::where('activity_id', $activity->id)
                        ->where('document_id', PolicySortCollection::ACTIVITY_DOCUMENT_BANK_TRANSFER_DOCUMENT)
                        ->first() ?? new ActivityDocument();

                    $activityDocument->activity_id = $activity->id;
                    $activityDocument->document_id = PolicySortCollection::ACTIVITY_DOCUMENT_BANK_TRANSFER_DOCUMENT;
                    $activityDocument->path = $path;
                    $activityDocument->uploaded_at = Carbon::now();
                    $activityDocument->save();

                    //$data = $this->spreadsheetsData($activity->id);

                    DB::commit();
                    return redirect()->back()->with('success', 'Se cargó correctamente el comprobante de pago.');
                } catch (Exception $e) {
                    DB::rollback();
                    return redirect()->back()->with('error', 'No se puede cargar comprobante');
                }
            } else {
                return redirect()->back()->with('error', 'No se puede cargar comprobante');
            }
        }
    }

    //ACCION REGISTRAR PAGO TRANSAFERENCIA BANCARIA TB de los pago TB comprobante adicional
    public function registerPaymentTbAdditional(Request $request, $cpath, $id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();
        $policySortCollection = $activity->policy_sort_collection;

        if ($policySortCollection) {
            DB::beginTransaction();

            try {
                $file = $request->file('transfer_file_additional');
                $path = "activity_action_document/policy_sort_collection_additional_{$activity->id}.pdf";
                Storage::disk('s3')->put($path, file_get_contents($file->getRealPath()));

                $policySortCollection->transaction_id = $request->transfer_receipt;
                $policySortCollection->additional_invoice_tb = $path;
                $policySortCollection->save();

                //Cargar documento en activity documents
                $activityDocument = new ActivityDocument();
                $activityDocument->activity_id = $activity->id;
                $activityDocument->document_id = PolicySortCollection::ACTIVITY_DOCUMENT_BANK_TRANSFER_DOCUMENT;
                $activityDocument->path = $path;
                $activityDocument->uploaded_at = Carbon::now();
                $activityDocument->save();

                DB::commit();
                return redirect()->back()
                    ->with('success', 'Se cargó correctamente el comprobante de pago adicional.')
                    ->with('info', 'Se esta verificando el pago de la transferencia bancaria, si el pago fue aprobado o rechazado se le notificará por correo electrónico');
            } catch (Exception $e) {
                DB::rollback();
                return response()->json(['error' => 'Error al realizar la acción'], 500);
            }
        }

        return redirect()->back()->with('error', 'No se puede cargar comprobante adicional');
    }

    //ACCION REGISTRAR PAGO POR TARJETA PT
    public function registerPaymentPt(Request $request, $cpath, $id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();
        $policySortCollection = $activity->policy_sort_collection;
        $enabled_states = [
            StatePolicySortCollection::RECIBO_AUMENTO_SEGURO_PENDIENTE_PAGO,
            StatePolicySortCollection::RECIBO_ABONO_MENSUAL_PENDIENTE_PAGO,
            StatePolicySortCollection::RECIBO_ABONO_TRIMESTRAL_PENDIENTE_PAGO,
            StatePolicySortCollection::RECIBO_ABONO_SEMESTRAL_PENDIENTE_PAGO,
            StatePolicySortCollection::RECIBO_REHABILITACION_PENDIENTE_PAGO,
            StatePolicySortCollection::PENDIENTE_PAGO_RECIBO_EMISION,
            StatePolicySortCollection::RECIBO_LIQUIDACION_PENDIENTE_PAGO,
            StatePolicySortCollection::RECIBO_RENOVACION_PENDIENTE_PAGO
        ];
        $state = in_array($activity->state_id, $enabled_states);

        if ($policySortCollection) {
            DB::beginTransaction();

            if ($state) {
                try {
                    $file = $request->file('card_payment_file');
                    $extension = $file->getClientOriginalExtension();
                    $path = "activity_action_document/policy_sort_collection_{$activity->id}.{$extension}";
                    Storage::disk('s3')->put($path, file_get_contents($file->getRealPath()));
                    $policySortCollection->invoice_pt = $path;
                    $policySortCollection->payment_status = PolicySortCollection::PAYMENT_STATUS_PENDING_APPROVAL;
                    $policySortCollection->transaction_date = Carbon::now();

                    date_default_timezone_set('America/Costa_Rica');
                    $horaActual = new DateTime();
                    $horaFormateada = $horaActual->format('H:i:s');

                    $policySortCollection->time_transaction = $horaFormateada;
                    $policySortCollection->payment_method = 'PT';

                    $webserviceController = new WebserviceAcselController();
                    $trm = $webserviceController->getTrm();
                    $policySortCollection->trm = $trm;

                    $policySortCollection->save();

                    register_shutdown_function(function () use ($activity) {
                        $webserviceFac = new WebserviceElectronicInvoiceController();
                        $resp = $webserviceFac->sendInvoice($activity->id);
                    });

                    $message = '';
                    switch ($activity->state_id) {
                        case StatePolicySortCollection::RECIBO_AUMENTO_SEGURO_PENDIENTE_PAGO:
                            $message = 'Registrar pago pt aumento del seguro periodo';
                            $action = ActionPolicySortCollection::REGISTRAR_PAGO_TARJETA_AUMENTO_DEL_SEGURO_PERIODO;
                            break;
                        case StatePolicySortCollection::RECIBO_ABONO_MENSUAL_PENDIENTE_PAGO:
                            $message = 'Registrar pago pt recibo abono mensual';
                            $action = ActionPolicySortCollection::REGISTRAR_PAGO_TARJETA_RECIBO_ABONO_MENSUAL;
                            break;
                        case StatePolicySortCollection::RECIBO_ABONO_TRIMESTRAL_PENDIENTE_PAGO:
                            $message = 'Registrar pago pt recibo abono trimestral';
                            $action = ActionPolicySortCollection::REGISTRAR_PAGO_TARJETA_RECIBO_ABONO_TRIMESTRAL;
                            break;
                        case StatePolicySortCollection::RECIBO_ABONO_SEMESTRAL_PENDIENTE_PAGO:
                            $message = 'Registrar pago pt recibo abono semestral';
                            $action = ActionPolicySortCollection::REGISTRAR_PAGO_TARJETA_RECIBO_ABONO_SEMESTRAL;
                            break;
                        case StatePolicySortCollection::RECIBO_REHABILITACION_PENDIENTE_PAGO:
                            $message = 'Registrar pago pt recibo rehabilitación';
                            $action = ActionPolicySortCollection::REGISTRAR_PAGO_TARJETA_RECIBO_REHABILITACION;
                            break;
                        case StatePolicySortCollection::PENDIENTE_PAGO_RECIBO_EMISION:
                            $message = 'Registrar pago pt recibo emision';
                            $action = ActionPolicySortCollection::REGISTRAR_PAGO_TARJETA;
                            break;
                        case StatePolicySortCollection::RECIBO_LIQUIDACION_PENDIENTE_PAGO:
                            $message = 'Registrar pago pt recibo liquidación';
                            $action = ActionPolicySortCollection::REGISTRAR_PAGO_TARJETA_RECIBO_LIQUIDACION;
                            break;
                        case StatePolicySortCollection::RECIBO_RENOVACION_PENDIENTE_PAGO:
                            $message = 'Registrar pago pt recibo renovación';
                            $action = ActionPolicySortCollection::REGISTRAR_PAGO_TARJETA_RECIBO_RENOVACION;
                            break;
                        // default:
                        //     $message = 'REGISTRO PAGO';
                        //     break;
                    }

                    //Actualizar estado de la actividad
                    $activity_action = ActionController::create($activity->id, $action, $message);

                    //Cargar documento en activity documents
                    $activityDocument = new ActivityDocument();
                    $activityDocument->activity_id = $activity->id;
                    $activityDocument->document_id = PolicySortCollection::ACTIVITY_DOCUMENT_BANK_TRANSFER_DOCUMENT; // TODO: Quitar? Crear un nuevo documento para PT?
                    $activityDocument->path = $path;
                    $activityDocument->uploaded_at = Carbon::now();
                    $activityDocument->save();

                    //$data = $this->spreadsheetsData($activity->id);

                    DB::commit();
                    return redirect()->back()
                        ->with('success', 'Se cargó correctamente el comprobante de pago.')
                        ->with('info', 'Se esta verificando el pago por tarjeta, si el pago fue aprobado o rechazado se le notificará por correo electrónico');
                } catch (Exception $e) {
                    DB::rollback();
                    return response()->json(['error' => 'Error al realizar la acción'], 500);
                }
            } elseif ($activity->state_id == StatePolicySortCollection::PENDIENTE_VALIDACION_PAGO) {
                try {
                    $file = $request->file('card_payment_file');
                    $extension = $file->getClientOriginalExtension();
                    $path = "activity_action_document/policy_sort_collection_{$activity->id}.{$extension}";
                    Storage::disk('s3')->put($path, file_get_contents($file->getRealPath()));
                    $policySortCollection->invoice_pt = $path;

                    $policySortCollection->save();

                    $activityDocument = ActivityDocument::where('activity_id', $activity->id)
                        ->where('document_id', PolicySortCollection::ACTIVITY_DOCUMENT_BANK_TRANSFER_DOCUMENT)
                        ->first() ?? new ActivityDocument();

                    $activityDocument->activity_id = $activity->id;
                    $activityDocument->document_id = PolicySortCollection::ACTIVITY_DOCUMENT_BANK_TRANSFER_DOCUMENT; // TODO: Quitar?
                    $activityDocument->path = $path;
                    $activityDocument->uploaded_at = Carbon::now();
                    $activityDocument->save();

                    //$data = $this->spreadsheetsData($activity->id);

                    DB::commit();
                    return redirect()->back()->with('success', 'Se cargó correctamente el comprobante de pago.');
                } catch (Exception $e) {
                    DB::rollback();
                    return redirect()->back()->with('error', 'No se puede cargar comprobante');
                }
            } else {
                return redirect()->back()->with('error', 'No se puede cargar comprobante');
            }
        }
    }

    public function spreadsheetsData($id)
    {

        $messageSpreadsheets = false;
        $policy_sorts = PolicySort::query()
            ->select('policy_sorts.*')
            ->leftJoin('activities as b', 'b.parent_id', '=', 'policy_sorts.activity_id')
            ->leftJoin('policy_sort_collections as ps', 'ps.activity_id', '=', 'b.id')
            ->where('ps.activity_id', $id)
            ->first();
        $economicActivities = [
            '0111',
            '0112',
            '0113',
            '0115',
            '0116',
            '0119',
            '0121',
            '0122',
            '0123',
            '0124',
            '0125',
            '0126',
            '0127',
            '0128',
            '0129',
            '4100'
        ];

        if ($policy_sorts && in_array($policy_sorts->activity_economic_id, $economicActivities)) {
            $countSpreadsheets = PolicySpreadsheet::leftJoin('activities as a', 'a.id', '=', 'policy_spreadsheets.activity_id')
                ->leftJoin('policy_sorts as p', 'p.activity_id', '=', 'a.parent_id')
                ->where('p.id', $policy_sorts->id)
                ->count();
            if ($countSpreadsheets == 0) {
                $messageSpreadsheets = true;
            }
        }


        $data = [
            'messageSpreadsheets' => $messageSpreadsheets,
            'policy_id' => $policy_sorts->id
        ];

        return $data;
    }

    //ACCION EMITIR POLIZA REALIZADO

    /**
     * @param $activity
     * @param $cpath
     * @return void
     * @throws Exception
     */
    public function reportPolicyPaymentMade($activity, $cpath, $req)
    {
        $policySortActivity = Activity::where('id', $activity->parent_id)
            ->where('service_id', Service::SERVICE_POLICY_SORT_MNK)->first();
        if (!$policySortActivity) {
            throw new Exception('Póliza no encontrada para el servicio: ' . $activity->id);
        }
        ActionController::create($activity->id, ActionPolicySortCollection::REPORTAR_PAGO_EMISION_POLIZA_REALIZADO, 'REPORTAR PAGO EMISION POLIZA REALIZADO');

        //Actulizar fecha de inicio y fin de la poliza
        $transactionDate = Carbon::parse($activity->policy_sort_collection->transaction_date)->format('Y-m-d');
        $validityFrom = Carbon::parse($policySortActivity->policy_sort->validity_from)->format('Y-m-d');
        if ($transactionDate > $validityFrom) {
            $this->updateValidityDate($activity, $policySortActivity);
        }
        //emitir poliza en poliza
        $actions = new PolicySortController();
        $actions->actionsPolicyAndQuotation($req, $policySortActivity->id);
        // Llamar con get a api/enviar-certificados-emision-poloza/{id}
        $actions->actionIssuePolicy($req, $cpath, $policySortActivity->id);
    }

    //actualizar fecha de inicio y fin de la poliza
    public function updateValidityDate($collection_activity, $policy_activity)
    {
        $transactionDate = Carbon::parse($collection_activity->policy_sort_collection->transaction_date);
        if ($policy_activity->policy_sort->temporality == 'permanent') {
            $oneYearLater = $transactionDate->copy()->addYear()->subDay()->format('Y-m-d');
            $policy_activity->policy_sort->validity_from = $transactionDate->format('Y-m-d');
            $policy_activity->policy_sort->validity_to = $oneYearLater;
        } elseif ($policy_activity->policy_sort->temporality == 'short') {

            $validityFrom = Carbon::parse($policy_activity->policy_sort->validity_from);
            $validityTo = Carbon::parse($policy_activity->policy_sort->validity_to);
            $daysDifference = $validityFrom->diffInDays($validityTo);

            $newValidityTo = $transactionDate->copy()->addDays($daysDifference)->format('Y-m-d');
            $policy_activity->policy_sort->validity_from = $transactionDate->format('Y-m-d');
            $policy_activity->policy_sort->validity_to = $newValidityTo;
        }
        $policy_activity->policy_sort->save();
    }

    //ACCION RECHAZAR PAGO TB
    public function rejectPaymentTb(Request $request, $cpath, $id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();
        $policySortCollection = $activity->policy_sort_collection;
        $policy = PolicySort::where('activity_id', $activity->parent_id)->first();
        if ($policySortCollection) {
            DB::beginTransaction();
            try {
                $policySortCollection->payment_status = PolicySortCollection::PAYMENT_STATUS_REJECTED;
                $policySortCollection->save();

                //Actualizar estado de la actividad
                $activity_action = ActionController::create($activity->id, ActionPolicySortCollection::RECHAZAR_PAGO_TB, 'RECHAZAR PAGO TB');
                //Enviar correo de aprobación

                $emails = [$policy->email];
                $text = [
                    "text" => "Estimado cliente, su pago ha sido rechazado.",
                    "sender" => 'MNK Seguros'
                ];

                $mailSent = new SendDocumentDataBase(
                    implode(',', $emails),
                    "Factura Electrónica",
                    "<EMAIL>",
                    "Factura electrónica prueba mail",
                    $text,
                    "<EMAIL>",
                    [],
                    "send_document_db",
                    $client,
                    request()->getHost(),
                    $activity->id,
                    $activity_action->id,
                    $activity->service->id
                );
                

                // Capturar el resultado del envío
                $result = $mailSent->sendMail();

                //Registramos los datos del correo enviado para la trazabilidad
                $mailBoardController = new MailBoardController();
                $mailBoardController->createRegisterMail(
                    $activity->id,
                    $activity->service->id, 
                    $policy->consecutive, 
                    'Tomador', 
                    mb_convert_case(mb_strtolower($activity->parent->affiliate->full_name ?? ''), MB_CASE_TITLE, "UTF-8"), 
                    $activity->parent->affiliate->doc_number, 
                    'Factura Electrónica', 
                    $text,
                    $emails, 
                    $result,
                    null
                );
                //conexion con hacienda para facturacion electronica
                DB::commit();
            } catch (Exception $e) {
                DB::rollback();
                return response()->json(['error' => 'Error al realizar la acción'], 500);
            }
        } else {
            return response()->json(['error' => 'No se pudo aprobar el pago'], 500);
        }
    }

    /**
     * Accion Reportar recibo liquidación
     *
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function reportLiquidationPaymentReceipt(Request $req, $cpath, $id)
    {

        $client = Client::where('path', $cpath)->firstOrFail();
        $policyCollectionActivity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail(); // ACTIVIDAD DE COBROS

        DB::beginTransaction();
        try {

            $action_id = ACTION::REPORTAR_RECIBO_LIQUIDACION;
            $document = "liquidation_payment_receipt";
            $activityActionsCreated = ActionController::create(
                $policyCollectionActivity->id,
                $action_id,
                'REPORTAR RECIBO LIQUIDACIÓN'
            );

            //TODO: GENERAR DOCUMENTO
            $pdf = PDF::loadView("services.policy_sort_collection.docs.{$document}", [
                'activity' => $policyCollectionActivity,
                'watermark' => false,
            ]);

            $filePath = "policyCollectionSort/{$document}_{$action_id}.pdf";
            Storage::disk('s3')
                ->put("policyCollectionSort/{$document}_{$action_id}.pdf", $pdf->output());

            $activityActionDocument = new ActivityActionDocument();
            $activityActionDocument->activity_action_id = $activityActionsCreated->id;
            $activityActionDocument->name = $document;
            $activityActionDocument->path = "policyCollectionSort/{$document}_{$action_id}.pdf";
            $activityActionDocument->save();

            //TODO: ENVÍO DEL CORREO
            $affiliate = Affiliate::where('id', $policyCollectionActivity->affiliate_id)->first();
            $emails = [$affiliate->email];
            $text = [
                "text" => "Estimado cliente, adjunto encontrará el recibo de pago liquidación.",
                "sender" => 'MNK Seguros'
            ];
            $attachments = [
                [
                    'path' => $filePath,
                    'name' => basename($filePath),
                    'type' => 'PDF'
                ]
            ];

            $mailSent = new SendDocumentDataBase(
                implode(',', $emails),
                "Recibo de pago liquidación",
                "<EMAIL>",
                "Recibo de pago liquidación",
                $text,
                "<EMAIL>",
                $attachments,
                "send_document_db",
                $client,
                request()->getHost(),
                $policyCollectionActivity->id,
                $activityActionsCreated->id,
                $policyCollectionActivity->service->id
            );
            
            // Capturar el resultado del envío
            $result = $mailSent->sendMail();

            //Registramos los datos del correo enviado para la trazabilidad
            $mailBoardController = new MailBoardController();
            $mailBoardController->createRegisterMail(
                $policyCollectionActivity->id,
                $policyCollectionActivity->service->id, 
                $policyCollectionActivity->parent->policy_sort->consecutive, 
                'Tomador', 
                mb_convert_case(mb_strtolower($policyCollectionActivity->parent->affiliate->full_name ?? ''), MB_CASE_TITLE, "UTF-8"), 
                $policyCollectionActivity->parent->affiliate->doc_number, 
                'Recibo de pago liquidación', 
                 $text,
                $emails, 
                $result,
                $attachments
            );

            //TODO: CREAR TABLA DE COBROS
            $policy_sort_collection = new PolicySortCollection();
            $policy_sort_collection->activity_id = $id;
            $policy_sort_collection->type_receipt = PolicySortCollection::LIQUIDATION;
            $policy_sort_collection->invoice_number = 'LIQUIDACIÓN-' . uniqid();
            $policy_sort_collection->total_amount = $req->input('total_amount') ?? 0;
            $policy_sort_collection->payment_status = PolicySortCollection::PAYMENT_STATUS_PENDING;
            $policy_sort_collection->due_date = $this->calculateDueDate(10); // 10 días habiles
            $policy_sort_collection->save();

            DB::commit();
        } catch (Exception $e) {
            DB::rollback();
            return response()->json(['error' => 'Error al realizar la acción'], 500);
        }
        return response()->json([
            'status' => 'success',
            'message' => 'Campos han sido actualizados correctamente'
        ]);
    }

    //ME-2315 - REPORTAR RECIBO RENOVACIÓN PERIODO
    public function reportRenewalReceiptPeriod(Request $req, $cpath, $id)
    {

        $client = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail(); // ACTIVIDAD DE COBROS

        DB::beginTransaction();
        try {

            $activityAction = ActionController::create(
                $activity->id,
                ActionPeIpSort::REPORTAR_RECIBO_RENOVACION,
                'REPORTAR RECIBO RENOVACIÓN'
            );

            //TODO: CREAR TABLA DE COBROS
            $policy_sort_collection = new PolicySortCollection();
            $policy_sort_collection->activity_id = $id;
            $policy_sort_collection->type_receipt = PolicySortCollection::RENEWAL;
            $policy_sort_collection->invoice_number = 'Renovación-' . uniqid();
            $policy_sort_collection->total_amount = $req->input('total_amount') ?? 0;
            $policy_sort_collection->payment_status = PolicySortCollection::PAYMENT_STATUS_PENDING;
            $policy_sort_collection->due_date = $this->calculateDueDate(10); // 10 días habiles
            $policy_sort_collection->save();

            $emailIntermediary = $activity->parent->policy_sort->email;
            $emailTaker = $activity->parent->affiliate->email;

            $emails = array_filter([$emailIntermediary, $emailTaker], function ($email) {
                return !empty($email);
            });

            Carbon::setLocale('es');
            $fechaIniVigencia = Carbon::parse($activity->parent->policy_sort->validity_from)->formatLocalized('%A %d de %B de %Y');
            $fechaFinVigencia = Carbon::parse($activity->parent->policy_sort->validity_to)->formatLocalized('%A %d de %B de %Y');
            $monto =  ($activity->parent->policy_sort->type_currency === 'USD' ? '$' : '₡').
                number_format($activity->parent->policy_sort->amount_policy ?? 0, 2, ',', '.');

            $fechaMaxima = Carbon::parse($activity->parent->policy_sort->validity_to)
                ->addWeekdays(10)
                ->formatLocalized('%A %d de %B de %Y');

           switch ($activity->parent->policy_sort->periodicity) {
               case 1:
                   $periodicidad = 'anual';
                   break;
               case 2:
                   $periodicidad = 'semestral';
                   break;
               case 3:
                   $periodicidad = 'trimestral';
                   break;
               case 4:
                   $periodicidad = 'mensual';
                   break;
               default:
                   $periodicidad = '';
                   break;
           }

            $emailBuild = TemplateBuilder::build(
                Templates::NOTIFY_RENEWAL_PAYMENT,
                [
                    'policy_sort' => $activity->parent->policy_sort->formatNumberConsecutive(),
                    'name' => mb_convert_case(mb_strtolower($activity->parent->affiliate->full_name ?? ''), MB_CASE_TITLE, "UTF-8"),
                    'periodo_fecha' => $fechaIniVigencia.' al '.$fechaFinVigencia,
                    'monto' => $monto,
                    'fecha_maxima' => $fechaMaxima,
                    'periodicidad' => $periodicidad
                ]
            );


            //recibo
            date_default_timezone_set('America/Costa_Rica');
            $horaActual = new DateTime();
            $horaCostarrica = $horaActual->format('h:i:s A');
            $fechaCostarrica = $horaActual->format('d/m/Y');

            $totalWords = NumberToWords::convertToWords($req->input('total_amount') ?? 0, $activity->parent->policy_sort->type_currency);

            $validityTo = Carbon::parse($activity->parent->policy_sort->validity_to)->addDay(); //le suma un dia

            $intervals = [
                0 => $validityTo->copy()->addMonths(12)->subDay(),
                1 => $validityTo->copy()->addMonths(12)->subDay(),
                2 => $validityTo->copy()->addMonths(6)->subDay(),
                3 => $validityTo->copy()->addMonths(3)->subDay(),
                4 => $validityTo->copy()->addMonth()->subDay(),
            ];

            $fechaFin = isset($intervals[$activity->parent->policy_sort->periodicity]) ? $intervals[$activity->parent->policy_sort->periodicity] : $validityTo;

            $work_modality_id = $activity->parent->policy_sort->work_modality_id;
            $type_receipt = $activity->policy_sort_collection->type_receipt;
            $datos = [
                'activity_policy' => $activity->parent,
                'activity_policy_collection' => $activity,
                'totalWords' => $totalWords,
                'date_from' =>  $validityTo->format('d/m/Y'),
                'date_to' => $fechaFin->format('d/m/Y'),
                'watermark' => false,
                'horaCostarrica' => $horaCostarrica,
                'fechaCostarrica' => $fechaCostarrica,
                'pagado' => false,
                'descuentoCml' => null,
                'descuentoClap' => null,
                'base' => $req->input('total_amount') ?? 0,
                'final' => $req->input('total_amount') ?? 0,
                'descuentos_total' => 0,
                'sumaDescuentos'=> 0,
                'quotationCondition' => null,
                'type_payment' => 'RENOVACIÓN DE PÓLIZA SORT',
                'work_modality_id' => $work_modality_id,
                'type_receipt'=> $type_receipt ?? ''
            ];

            $document = AppServiceProvider::$PERIODICITY_RECEIPTS['renewal'] ?? 'unknown_receipt_type';

            $pdf = PDF::loadView("services.policy_sort_collection.docs.monthly_payment_receipt", $datos);
            $filePath = "policyCollectionSort/{$document}_{$activity->id}.pdf";
            Storage::disk('s3')->put($filePath, $pdf->output());

            $activityActionDocument = new ActivityActionDocument();
            $activityActionDocument->activity_action_id = $activityAction->id;
            $activityActionDocument->name = $document;
            $activityActionDocument->path = $filePath;
            $activityActionDocument->save();

            $attachments = [
                [
                    'path' => $filePath,
                    'name' => basename($filePath),
                    'type' => 'PDF'
                ]
            ];


            if ($emails != null) {

                $mailSent = new SendDocumentDataBase(
                    $emails,
                    $emailBuild['subject'],
                    "<EMAIL>",
                    "Recordatorio de pago de renovación",
                    [
                        "text" => $emailBuild['body'],
                        "sender" => $emailBuild['sender']
                    ],
                    "<EMAIL>",
                    $attachments, //files
                    "send_document_db",
                    $client->id,
                    request()->getHost(),
                    $activity->id,
                    $activityAction,
                    $activity->parent->service->id
                );

                // Capturar el resultado del envío
                $result = $mailSent->sendMail();

                //Registramos los datos del correo enviado para la trazabilidad
                $mailBoardController = new MailBoardController();
                $mailBoardController->createRegisterMail(
                    $activity->id,
                    $activity->service->id,
                    $activity->parent->policy_sort->consecutive,
                    'Tomador',
                    mb_convert_case(mb_strtolower($activity->parent->affiliate->full_name ?? ''), MB_CASE_TITLE, "UTF-8"),
                    $activity->parent->affiliate->doc_number,
                    $emailBuild['subject'],
                    $emailBuild['body'],
                    $emails,
                    $result,
                    $attachments
                );
            }

            //recibo

            DB::commit();
        } catch (Exception $e) {
            DB::rollback();
            return response()->json(['error' => 'Error al realizar la acción'], 500);
        }

        return response()->json([
            'status' => 'success',
            'message' => 'Registro creado'
        ]);
    }


    /*
     * Función que permite generar el reporte de transacciones de cobros
     * */
    public function exportTodayTc()
    {
        try {
            // cosulta de fecha de día anterior
            $yesterday     = Carbon::yesterday();
            $fechaTexto    = $yesterday->format('d/m/Y');
            $fechaFilename = $yesterday->format('Ymd');

            // Cabeceras del Excel
            $headings = [
                'PÓLIZA',
                'ID TRANSAC',
                'MONTO',
                'FECHA DE PAGO',
                'NÚMERO DE RECIBO',
                'NÚMERO DE FACTURA',
            ];

            // Consulta la vista filtrando FECHA_DE_PAGO = ayer en view para creación del reporte
            $registros = DB::table('vw_report_policy_collections')
                ->where('FECHA_DE_PAGO', $fechaTexto)
                ->get();

            $data = [];
            foreach ($registros as $r) {
                $formattedPoliza = $r->POLIZA
                    ? 'SORT-' . sprintf('%04d', $r->POLIZA)
                    : '';
                $data[] = [
                    $formattedPoliza,
                    $r->ID_TRANSAC,
                    $r->MONTO,
                    $r->FECHA_DE_PAGO,
                    $r->NUMERO_DE_RECIBO,
                    $r->NUMERO_DE_FACTURA,
                ];
            }

            // Nombre y ruta en S3
            $filename = "reporte_ecommerce_sort_{$fechaFilename}.xlsx";
            $s3Path   = "reports/{$filename}";

            //Generar el Excel en memoria
            $excel = Excel::create("reporte_ecommerce_sort_{$fechaFilename}", function($excel) use ($headings, $data) {
                $excel->sheet('Transacciones', function($sheet) use ($headings, $data) {
                    $sheet->row(1, $headings);
                    $sheet->fromArray($data, null, 'A2', false, false);
                });
            });

            // Obtener el binario XLSX
            $binary = $excel->string('xlsx');

            // Subir a S3
            Storage::disk('s3')->put($s3Path, $binary);

            $subject = "Reporte de pagos E-commerce SORT";
            $body    = "MNK SEGUROS, se le comunica a continuación un Listado de las transacciones de E-commerce del día {$fechaTexto}.";

            /*TODO: al subir a producción se debe <NAME_EMAIL> con copia a Wendy.quiros@<NAME_EMAIL> */
            $emails  = [
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>'
            ];

            $text = [
                'text'   => $body,
                'sender' => 'mnk aseguramiento',
            ];

            // Adjuntar directamente el binario
            $attachments = [
                [
                    'path' => $s3Path,
                    'data' => $binary,
                    'name' => $filename,
                    'type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                ],
            ];

            // Proceso de envio de correo
            $mailSent = new SendDocumentDataBase(
                implode(',', $emails),
                $subject,
                '<EMAIL>',
                $subject,
                $text,
                '<EMAIL>',
                $attachments,
                'send_document_db',
                null,
                request()->getHost()
            );
            $mailSent->sendMail();

            return back()->with('success', 'Reporte generado y enviado correctamente.');

        } catch (\Exception $e) {
            return back()->withErrors($e->getMessage());
        }
    }
    //reemplazar recibos manuales

    public function getValidityFromPaymentReceipt($activity_policy)
    {
        //fecha del recibo
        $validityFrom = Carbon::parse($activity_policy->policy_sort->validity_from);

        //buscar cuantos recibos lleva para sumarle los meses
        $receiptCount = Activity::where('parent_id', $activity_policy->id)
            ->where('service_id', Service::SERVICE_POLICY_SORT_COLLECTION_MNK)
            ->count();
        $receiptIndex = max(0, $receiptCount - 1);
        $monthsToAdd = isset($monthsToAddArray[$activity_policy->policy_sort->periodicity])
            ? $monthsToAddArray[$activity_policy->policy_sort->periodicity]
            : 0;
        $monthPassed = $receiptIndex  * $monthsToAdd;

        if ($monthPassed) {
            $validityFrom = Carbon::parse($validityFrom)->addMonths($monthPassed);
        }
        //retornar la fecha
        return $validityFrom->format('d/m/Y');
    }

    //ME-3223 registra salgo a favor
    public function insertPremiumSurplus($cpath, $policy_id)
    {

        try{

            $policy = PolicySort::find($policy_id);

            //ultimo pago aprobado
            $activityPayment = Activity::where('parent_id', $policy->activity->id)
                ->where("service_id", Service::SERVICE_POLICY_SORT_COLLECTION_MNK)
                ->whereHas('policy_sort_collection', function ($query) {
                    $query->where('payment_status', PolicySortCollection::PAYMENT_STATUS_APPROVED);
                })
                ->latest()
                ->first();

            $webserviceController = new WebserviceAcselController();
            $trm = $webserviceController->getTrm();

            if ($trm == 0) {
                throw new \Exception('Error: El TRM no es valido');
            }

           if($activityPayment->policy_sort_collection->applied_credit_note_amount > 0){
               return response()->json([
                   'success' => true,
                   'message' => 'Pago no valido para registrar sobrante de prima.'
               ]);
           }

            //en caso de que no registren payment_difference el realiza el calculo aqui para sacar la diferencia

            $paymentDifference = $activityPayment->policy_sort_collection->payment_difference ?
                $activityPayment->policy_sort_collection->payment_difference :
                $activityPayment->policy_sort_collection->total_amount - ($activityPayment->policy_sort_collection->value_paid ?? 0);

            $typeCurrency = $activityPayment->policy_sort_collection->payment_currency ?? $policy->type_currency;

            if ($paymentDifference <= 0) {
                return response()->json([
                    'success' => true,
                    'message' => 'No hay diferencia positiva en el pago. Diferencia: ' . $paymentDifference
                ]);
            }

            $paymentDifferenceUsd =  $typeCurrency == 'CRC' ? ($paymentDifference / $trm) : $paymentDifference;

            //la diferencia del pago es positiva y es menor a 5000 colones o 10 USD no se contabilizará como un sobrante de prima, por ende, no se registrara en la tabla
            if ($paymentDifferenceUsd <= 10) {
                return response()->json([
                    'success' => true,
                    'message' => sprintf(
                        'La diferencia (%.2f USD) es menor al umbral de 10 USD',
                        $paymentDifferenceUsd
                    )
                ]);
            }

            //acciones de aprobacion del pago
            $activityAction = ActivityAction::where('activity_id', $activityPayment->id)
                ->whereIn('action_id', [210, 216, 220, 221, 226, 183, 203, 217, 219, 218, 223, 182, 224, 332])->first();

            $premiumSurplus = PremiumSurplus::create([
                'activity_id' => $activityPayment->id,
                'policyholder_id' => $policy->activity->affiliate->doc_number,
                'policyholder_name' => $policy->activity->affiliate->full_name,
                'policy_number' => $policy->formatNumberConsecutive(),
                'surplus_amount' => $paymentDifference,
                'receipt_number' => $activityPayment->policy_sort_collection->id,
                'currency' => $typeCurrency,
                'credit_balance' => $paymentDifference,
                'issue_date' => $activityAction->created_at ?? now(),
                'status_surplus' => 'ACT'
            ]);

            return response()->json([
                'success' => true,
                'message' => sprintf(
                    'Sobrante de prima registrado correctamente. Monto: %.2f %s (%.2f USD)',
                    $paymentDifference,
                    $typeCurrency,
                    $paymentDifferenceUsd
                ),
                'surplus_id' => $premiumSurplus->id
            ]);

        }catch (\Exception $e){
            throw new \Exception('Error: '.$e->getMessage());
        }
    }
}
