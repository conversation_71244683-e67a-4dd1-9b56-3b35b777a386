<?php

namespace App\Http\Controllers\Services;

use App\Actions\ActionMedicalBills;
use App\Actions\ActionMedicalBillsServiceSort;
use App\Activity;
use App\ActivityActionDocument;
use App\ActivityDocument;
use App\Affiliate;
use App\Area;
use App\AuditMedicalBill;
use App\Client;
use App\DataElectronicInvoice;
use App\GisSort;
use App\DataPreInvoice;
use App\Http\Controllers\ActionController;
use App\Http\Controllers\Controller;
use App\Http\Controllers\Tables\MailBoardController;
use App\Mail\SendDocumentDataBase;
use App\MailTemplates\Constants\Templates;
use App\MailTemplates\TemplateBuilder;
use App\MedicalBill;
use App\MedicalServicesSort;
use App\Medication;
use App\PolicySort;
use App\Provider;
use App\Providers\AppServiceProvider;
use App\Service;
use App\State;
use App\States\StateMedicalBillsServiceSort;
use App\SuppliesMot;
use App\User;
use App\UserProvider;
use Carbon\Carbon;
use DateTime;
use GPBMetadata\Google\Bigtable\V2\Data;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\View;
use Maatwebsite\Excel\Facades\Excel;
use PHPExcel_IOFactory;
use Illuminate\Support\Facades\Validator;
use PDF;
use PHPExcel_Shared_Date;

class MedicalBillsController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function form(Request $req, $cpath, $id)
    {

        $client = Client::where('path', $cpath)->firstOrFail();
        //Actividad de Cuenta Medica
        $medical_bills_activity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();

        //Datos de medicamentos y sus relaciones
        $medical_bills_service_data = $medical_bills_activity->medical_bill;

        //Documentos subidos a esta actividad -> Excel de prefactura
        $doc_pre_invoice = ActivityDocument::where('activity_id', $medical_bills_service_data->activity_id)
            ->where('document_id', MedicalBill::ACTIVITY_DOCUMENT_PRE_INVOICE)->get();

        //Documentos subidos a esta actividad -> pdfs de soporte prefactura
        $doc_attachments_pre_invoice = ActivityDocument::where('activity_id', $medical_bills_service_data->activity_id)
            ->where('document_id', MedicalBill::ACTIVITY_DOCUMENT_ATTACHMENTS_PRE_INVOICE)->get();

        //Documentos subidos a esta actividad ->  XML de Factura Electronica
        $doc_electronic_invoice = ActivityDocument::where('activity_id', $medical_bills_service_data->activity_id)
            ->where('document_id', MedicalBill::ACTIVITY_DOCUMENT_ATTACHMENTS_ELECTRONIC_INVOICE)->get();

        //Documentos subidos a esta actividad -> pdf de soporte Factura Electronica
        $doc_attachments_electronic_invoice_support = ActivityDocument::where('activity_id', $medical_bills_service_data->activity_id)
            ->where('document_id', MedicalBill::ACTIVITY_DOCUMENT_ATTACHMENTS_ELECTRONIC_INVOICE_SUPPORT)->get();

        //Documentos subidos a esta actividad -> Excel de reporte de Servicio
        $doc_attachments_service_report = ActivityDocument::where('activity_id', $medical_bills_service_data->activity_id)
            ->where('document_id', MedicalBill::ACTIVITY_DOCUMENT_ATTACHMENTS_SERVICE_REPORT)->get();

        //Documentos subidos a esta actividad -> pdf de otros soportes
        $doc_attachments_other_support = ActivityDocument::where('activity_id', $medical_bills_service_data->activity_id)
            ->where('document_id', MedicalBill::ACTIVITY_DOCUMENT_ATTACHMENTS_OTHER_SUPPORT)->get();

        //Datos de la prefactura
        $dataPreinvoice = $medical_bills_service_data->data_pre_invoice;
        //Datos de la factura electronica
        $dataElectronicInvoice = $medical_bills_service_data->data_electronic_invoice;
        //estado de la actividad
        $state_medical_bills = State::where('id', $medical_bills_activity->state_id)->pluck('name')->first();

        return view('services.medical_bills.form.form', [
            'activity' => $medical_bills_activity,
            'id' => $id,
            'medical_bills_service' => $medical_bills_service_data,
            'dataPreInvoice' => $dataPreinvoice,
            'dataElectronicInvoice' => $dataElectronicInvoice,
            'doc_pre_invoice' => $doc_pre_invoice,
            'doc_attachments_pre_invoice' => $doc_attachments_pre_invoice,
            'doc_electronic_invoice' => $doc_electronic_invoice,
            'doc_attachments_electronic_invoice_support' => $doc_attachments_electronic_invoice_support,
            'doc_attachments_service_report' => $doc_attachments_service_report,
            'doc_attachments_other_support' => $doc_attachments_other_support,
            'state_medical_bills' => $state_medical_bills,
        ]);
    }

    public function save(Request $req, $cpath, $id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();

        $medical_bills_service_activity = Activity::where('client_id', $client->id)
            ->where('id', $id)
            ->firstOrFail();
        if (!$medical_bills_service_activity) {
            $errors = "La actividad no existe";
            throw new \Exception($errors); // Lanzar excepción con el error
        }

        //Datos de cuentas medicas y sus relaciones
        $medical_bills_service_data = $medical_bills_service_activity->medical_bill;

        if (!$medical_bills_service_data) {
            $errors = "La actividad no tiene una cuenta medica";
            throw new \Exception($errors); // Lanzar excepción con el error
        }
        //Iniciamos la transacción
        DB::beginTransaction();
        try {
            if ($medical_bills_service_data) {

                //Esto guarda los resultados de la prefactura por parte del analista
                if ($medical_bills_service_activity->state_id == StateMedicalBillsServiceSort::PREFACTURA_EN_REVISION) {
                    if ($req->has('resultPreInvoice')) {
                        $this->savePreInvoice($req, $cpath, $id);
                    } else {
                        $errors = "Hubo un error al guardar la pre factura por analista";
                        throw new \Exception($errors); // Lanzar excepción con el error
                    }
                }

                //Actualizar estados de los rubros factura electrónica
                if ($medical_bills_service_activity->state_id !== StateMedicalBillsServiceSort::REGISTRADO
                    && $medical_bills_service_activity->state_id !== StateMedicalBillsServiceSort::PREFACTURA_EN_REVISION) {

                    //Actualiza resultados de Cargue de factura electronica
                    $electronic_invoice_ids = $req->input('electronic_invoice_ids');
                    $electronic_results = $req->input('electronic_results');
                    $electronic_reasons = $req->input('rejection_reason');

                    if (!$electronic_invoice_ids) {
                        $errors = "No hay datos para actualizar";
                        throw new \Exception($errors); // Lanzar excepción con el error
                    }

                    foreach ($electronic_invoice_ids as $index => $id_invoice) {
                        $selectedResult = $electronic_results[$index];

                        $dataElectronicInvoice = DataElectronicInvoice::where('id', $id_invoice)->first();

                        if ($dataElectronicInvoice) {// Verifica si se encontró la factura electronica
                            $dataElectronicInvoice->result = $selectedResult;

                            // Aquí guardas la razón de rechazo
                            $dataElectronicInvoice->reason = $electronic_reasons[$index] ?? '';
                            $dataElectronicInvoice->save();
                        } else {
                            $errors = 'No se encontró la pre factura con ID ' . $id_invoice;
                            throw new \Exception($errors); // Lanzar excepción con el error
                        }
                    }
                }

                //Cuando se va accionar estando la factura en revision por el analista
                if ($medical_bills_service_activity->state_id == StateMedicalBillsServiceSort::FACTURA_ELECTRONICA_EN_REVISION) {
                    //guardar la gestión realizada por el analista
                    $this->saveElectronicInvoice($req, $client, $medical_bills_service_data, $medical_bills_service_activity);
                }

                //Guardamos los datos
                $medical_bills_service_data->save();
                DB::commit();

                //Redireccionamos al tablero base al area del usuario logueado
                if (Auth::user()->area_id === Area::ADMINISTRATIVE || Auth::user()->area_id === Area::AUDITOR || Auth::user()->area_id === Area::ANALISTA_INDEMNIZACION) {
                    return redirect('/tablero/auditoria_cuentas_medicas')->with('success', 'Acción realizada exitosamente');
                } elseif (Auth::user()->area_id === Area::ADMINISTRATIVE || Auth::user()->isProvider()) {
                    return redirect('/proveedor/tablero_cuentas_medicas')->with('success', 'Acción realizada exitosamente');
                } else {
                    $errors = "Acceso no permitido.";
                    throw new \Exception($errors); // Lanzar excepción con el error
                }
            } else {
                $errors = "No se puede actualizar el servicio Cuentas médicas.";
                throw new \Exception($errors); // Lanzar excepción con el error
            }
        } catch (\Exception $e) {
            // Hacer rollback si hay algún error
            DB::rollBack();

            // Capturar el mensaje de error
            $error = $e->getMessage();

            // Redirigir con el mensaje de error
            return redirect('/servicio/' . $medical_bills_service_activity->id . '/medical_bills')->withErrors($error)->withInput();
        }
    }

    public function validateFilesPDF($req, $nameFile, $customMessages, $sectionName)
    {

        $files = $req->file($nameFile); // Obtenemos todos los archivos subidos
        // Recorremos cada archivo para validar su tipo MIME
        foreach ($files as $file) {
            // Verificar que el archivo exista (esto normalmete pasa cuando el archivo es incorrecto / corrupto o no se carga bien)
            if (!$file->isValid()) {
                throw new \Exception("Se ha intentado subir un archivo, pero no se pudo subir correctamente en la sección: " . $sectionName);
            }

            $mimeType = $file->getMimeType(); // Obtenemos el tipo MIME de cada archivo

            // Tipos MIME válidos
            $validMimeTypes = ['application/pdf', 'application/x-pdf', 'pdf'];

            // Si el tipo MIME no es válido
            if (!in_array($mimeType, $validMimeTypes)) {
                $errors = ["Por favor, sube un archivo válido en formato PDF."];
                $errorMessage = "Se ha intentado subir un archivo, pero ocurrió un error: " . implode(", ", $errors) . " en la sección: " . $sectionName;
                throw new \Exception($errorMessage);
            }
        }

        // Realizar la validación del archivo (tamaño y tipo MIME)
        $validator = Validator::make($req->all(), [
            $nameFile => 'max:10240',
        ], $customMessages);

        // Si la validación falla
        if ($validator->fails()) {
            // Obtener los errores
            $errors = $validator->errors()->all();

            // Eliminamos duplicados en los mensajes de error
            $errors = array_unique($errors);

            // Concatenar los errores y la sección
            $errorMessage = "Se ha intentado subir un archivo, pero ocurrió un error: " . implode(", ", $errors) . " en la sección: " . $sectionName;

            // Lanza la excepción con el mensaje de error completo
            throw new \Exception($errorMessage);
        }

    }

    public function uploadPreInvoice(Request $req, $cpath, $processType = null)
    {
        //Obtener el cliente
        $client = Client::where('path', $cpath)->firstOrFail();

        //Asignar el tipo de acción
        $action_id = ActionMedicalBillsServiceSort::CARGUE_PRE_FACTURA;

        //Si no se proporciona el parámetro multiple correcto desde método post, se retorna un mensaje de error
        if (($processType !== "true") && ($processType !== "false")) {
            $errors[] = "Acceso no permitido, ruta no encontrada.";
            throw new \Exception(implode(", ", $errors));
        }

        //Asignar el tipo de proceso
        $processType = $processType === "true" ? MedicalBill::PROCESS_UNIQUE_XML : MedicalBill::PROCESS_MULTIPLE_XML;

        //Iniciamos la transacción
        DB::beginTransaction();
        try {

            //Solo los proveedores pueden subir archivos
            if (Auth::user()->isProvider()) {

                // Definir los mensajes personalizados para los errores
                $customMessages = [
                    'files.max' => 'El archivo subido no puede ser mayor de 10 MB'
                ];

                //capturar el afiliado del usuario loegueado (el proveedor)
                $affiliateId = optional(Auth::user()->provider)->affiliate_id;

                //capturar el afiliado del usuario loegueado (el proveedor)
                $providerId = optional(Auth::user()->provider)->id;

                //validar que el usuario logueado tenga un proveedor asociado
                if (is_null($providerId)) {
                    throw new \Exception("No se encontró un proveedor asociado al usuario logueado.");
                }

                //validar que el usuario lo
                //gueado tenga un afiliado asociado
                if (is_null($affiliateId)) {
                    throw new \Exception("No se encontró un afiliado asociado al usuario logueado.");
                }

                //Actividad de Cuenta Medica
                $medical_bills_service_activity = Activity::create([
                    'client_id' => $client->id,
                    'affiliate_id' => $affiliateId,
                    'service_id' => Service::SERVICE_MEDICAL_BILLS_MNK,
                    'state_id' => StateMedicalBillsServiceSort::REGISTRADO,
                    'user_id' => Auth::id(),
                ]);

                $medicalBillNew = MedicalBill::create([
                    'activity_id' => $medical_bills_service_activity->id,
                    'description' => $req->input('description'),
                    'provider' => $providerId
                ]);

                //Crear el activity_action del servicio
                $activity_action = ActionController::create($medical_bills_service_activity->id,
                    $action_id,
                    'CARGUE DE PRE FACTURA');

                //Validaciones para los archivos pdf's
                $this->validateFilesPDF($req, 'files', $customMessages, "Cargar soportes pre factura (PDF)");
                // Captura de documentos PDF
                if ($req->hasFile('files')) {
                    $files = $req->file('files');
                    foreach ($files as $pdf) {

                        //nombre original del documento
                        $originalName = pathinfo($pdf->getClientOriginalName(), PATHINFO_FILENAME);

                        // Generar un nombre único para cada archivo
                        $uniqueFilename = "activity_document/" . uniqid() . "_{$originalName}_{$action_id}.pdf";

                        // Guardar el PDF en S3
                        Storage::disk('s3')->put($uniqueFilename, file_get_contents($pdf));

                        // Guardar la información del documento en la base de datos
                        $activityDocument = new ActivityDocument();
                        $activityDocument->activity_id = $medical_bills_service_activity->id;
                        $activityDocument->document_id = MedicalBill::ACTIVITY_DOCUMENT_ATTACHMENTS_PRE_INVOICE;
                        $activityDocument->path = $uniqueFilename;
                        $activityDocument->uploaded_at = Carbon::now();
                        $activityDocument->save();
                    }
                }

                //cargar los archivos
                // Captura del documento EXCEL
                if ($req->hasFile('affiliate_file')) {
                        $file = $req->file('affiliate_file');

                } else {
                    $errors[] = "No se encontraron archivos.";
                    throw new \Exception(implode(", ", $errors));
                }

                // Validar que el archivo es un Excel
                if (!in_array($file->getClientMimeType(), ['application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'])) {
                    $errors[] = "Solo se permiten archivos Excel.";
                    throw new \Exception(implode(", ", $errors));
                }

                //validación antes del procesamiento de datos (solo es lectura y validación)
                $this->validateBeforeProcessing($file);

                // Obtener el nombre original del documento sin la extensión
                $originalName = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);

                // Generar un nombre único para cada archivo
                $uniqueFilename = "activity_document/" . uniqid() . "_{$originalName}_{$action_id}.xlsx";

                // Guardar el PDF en S3
                Storage::disk('s3')->put($uniqueFilename, file_get_contents($file));

                // Guardar la información del documento en la base de datos
                $activityDocument = new ActivityDocument();
                $activityDocument->activity_id = $medical_bills_service_activity->id;
                $activityDocument->document_id = MedicalBill::ACTIVITY_DOCUMENT_PRE_INVOICE;
                $activityDocument->path = $uniqueFilename;
                $activityDocument->uploaded_at = Carbon::now();
                $activityDocument->save();

                $currencyType = null; // Variable para almacenar el tipo de moneda


                // Cargar y leer el archivo Excel
                Excel::selectSheetsByIndex(0)
                    ->filter('chunk')
                    ->load($file->path())
                    ->chunk(10, function ($reader) use (&$medicalBillNew, &$errors, &$alerts, &$processedRecords, &$currencyType) {
                        $reader->each(function ($row) use ($medicalBillNew, &$errors, &$alerts, &$processedRecords, &$currencyType) {
                            $filteredRow = $this->prepareRow($row);

                            // Validar tipo de moneda
                            if (is_null($currencyType) && array_key_exists('tipo_moneda', $filteredRow)) {
                                $currencyType = strtolower($filteredRow['tipo_moneda']);
                            } elseif (is_null($currencyType)) {
                                $errors[] = "La fila 1 falta la columna: 'tipo_moneda'.";
                                throw new \Exception(implode(", ", $errors)); // Lanzar excepción con los errores acumulados
                            }

                            // Llama a la función de procesamiento
                            $this->processAffiliateSpreadsheet($medicalBillNew->id, $filteredRow, $errors, $alerts,  $processedRecords, $currencyType);
                        });
                    });


                // Recuperar los registros relacionados a la tabla 'DataPreInvoice' que están asociados al 'MedicalBill'
                $relatedData = DataPreInvoice::where('id_medical_bills', $medicalBillNew->id)->get();

                // Inicializar una variable para acumular el total
                $totalServiceValue = 0;

                // Recorrer $relatedData y hacer la sumatoria de la columna 'service_value'
                foreach ($relatedData as $data) {
                    $totalServiceValue += $data->service_value;
                }

                // Capturar todos los valores de 'type_currency'
                $typeCurrencyValues = $relatedData->pluck('type_currency')->unique();

                // Verificar si hay más de un valor único de 'type_currency'
                if ($typeCurrencyValues->count() > 1) {
                    // Si hay más de un tipo de moneda, lanzar un error
                    $errors[] = "Hay más de un tipo de moneda en los registros asociados al MedicalBill.";
                    throw new \Exception(implode(", ", $errors)); // Lanzar excepción con los errores acumulados
                } else {
                    // Si solo hay un valor único, capturarlo
                    $currencyTypeFromDataPreInvoice = $typeCurrencyValues->first();
                }

                // Ahora actualizamos el registro en la tabla MedicalBill después de procesar todas las filas
                $this->updateMedicalBillTotal($medicalBillNew->id, $totalServiceValue, $currencyTypeFromDataPreInvoice, $errors, $processType);

                DB::commit(); // Si todo va bien, se hace commit de la transacción

                return redirect('/proveedor/tablero_cuentas_medicas')->with('success', 'Se cargó la pre factura correctamente.');
            } else {
                $errors[] = "Debes ser un proveedor para poder radicar una pre factura.";
                throw new \Exception(implode(", ", $errors));
            }

        } catch (\Exception $e) {
            DB::rollBack();
            // Convertimos los errores en un array separado por saltos de línea
            $errors = explode("\n", $e->getMessage());

            return redirect()->back()->withErrors($errors)->withInput();
        }

    }
    public function uploadElectronicInvoice(Request $req, $cpath, $numPreInvoice)
    {
        //No permitir que venga vacío la pre factura
        if (empty($numPreInvoice)) {
            $errors[] = "Número de pre factura no proporcionado.";
            throw new \Exception(implode(", ", $errors));
        }

        $client = Client::where('path', $cpath)->firstOrFail();

        //Iniciamos la transacción
        DB::beginTransaction();
        try {
            //Solo los proveedores pueden subir archivos
            if (Auth::user()->isProvider()) {

                // Definir los mensajes personalizados para los errores
                $customMessages = [
                    'files.file' => 'Por favor, sube un archivo válido en formato PDF',
                    'files.mimes' => 'Por favor, sube un archivo válido en formato PDF',
                    'files.max' => 'El archivo subido no puede ser mayor de 10 MB',
                ];

                //Tabla de cuentas médicas
                $medicalBill = MedicalBill::where('id', $numPreInvoice)->first();
                $medicalBill->date_created_electronic_invoice = Carbon::now(); //Guardamos la fecha cuando se hizo la primera la factura electrónica
                $medicalBill->due_date_electronic_invoice = $medicalBill->date_created_electronic_invoice->copy()->addDays(30); // Guardamos la fecha de vencimiento de la factura electrónica

                //Se captura la descripción de la factura electronica (momento de la carga de archivo)
                $medicalBill->description_electronic_invoice = $req->input('description_electronic_invoice');
                $medicalBill->save();

                //Actividad de Cuenta Medica
                $medical_bills_service_activity = Activity::where('id', $medicalBill->activity_id)->first();
                $action_id = ActionMedicalBillsServiceSort::REPORTAR_FACTURA_ELECTRONICA;

                if ($req->hasFile('xml_files')) {
                    // Obtener los archivos XML subidos
                    $xmlFiles = $req->file('xml_files');
                } else {
                    // Si no se ha subido ningún archivo, lanzar error
                    $errors[] = "No se proporcionó ningún archivo XML.";
                    throw new \Exception(implode(", ", $errors));
                }

                // Flag para verificar si es un único archivo XML
                $uniqueXml = false;

                // Verificar si se subió exactamente un archivo
                if (count($xmlFiles) === 1) {
                    $uniqueXml = true;
                }

                //número de facturas electrónica subidas (esto nos servirá para acomodar el cuerpo de correo al radicar alguna factura electrónica)
                $numElectronicInvoice = 0;

                //Verificar el tipo de proceso del servicio, esto indica que tipo de factura se radicó, en esta caso:
                // Una prefactura con multiples XML
                if ($medicalBill->process_type === MedicalBill::PROCESS_MULTIPLE_XML) {
                    //Asignar número de facturas electrónica
                    $numElectronicInvoice = count($xmlFiles) ?? 0;
                    foreach ($xmlFiles as $xmlFile) {
                        // Verificar si el archivo es un XML
                        if ($xmlFile->getClientMimeType() !== 'text/xml' && $xmlFile->getClientMimeType() !== 'application/xml') {
                            $errors[] = "Solo se permiten archivos XML.";
                            throw new \Exception(implode(", ", $errors));

                        }
                        // Nombre original del documento
                        $originalName = pathinfo($xmlFile->getClientOriginalName(), PATHINFO_FILENAME);

                        // Generar un nombre único para el archivo XML
                        $uniqueFilename = "activity_document/" . uniqid() . "_{$originalName}_{$action_id}.xml";

                        // Guardar el XML en S3
                        Storage::disk('s3')->put($uniqueFilename, file_get_contents($xmlFile));

                        // Guardar la información del documento en la base de datos
                        $activityDocument = new ActivityDocument();
                        $activityDocument->activity_id = $medical_bills_service_activity->id;
                        $activityDocument->document_id = MedicalBill::ACTIVITY_DOCUMENT_ATTACHMENTS_ELECTRONIC_INVOICE;
                        $activityDocument->path = $uniqueFilename;
                        $activityDocument->uploaded_at = Carbon::now();
                        $activityDocument->save();
                    }
                    //Captura de todos los XML
                    $responseXml = $this->readXMLMultiple($xmlFiles, $medicalBill);
                } // Una prefactura con ÚNICO XML
                elseif ($uniqueXml) {
                    //Asignar número de facturas electrónica
                    $numElectronicInvoice = count($xmlFiles) ?? 0;

                    // Si se sube un solo archivo XML
                    $xmlFile = $xmlFiles[0]; // Acceder al único archivo

                    // Verificar que el archivo es un XML
                    if ($xmlFile->getClientMimeType() !== 'text/xml' && $xmlFile->getClientMimeType() !== 'application/xml') {
                        $errors[] = "Solo se permiten archivos XML.";
                        throw new \Exception(implode(", ", $errors));
                    }

                    // Nombre original del documento
                    $originalName = pathinfo($xmlFile->getClientOriginalName(), PATHINFO_FILENAME);

                    // Generar un nombre único para el archivo XML
                    $uniqueFilename = "activity_document/" . uniqid() . "_{$originalName}_{$action_id}.xml";

                    // Guardar el XML en S3
                    Storage::disk('s3')->put($uniqueFilename, file_get_contents($xmlFile));

                    // Guardar la información del documento en la base de datos
                    $activityDocument = new ActivityDocument();
                    $activityDocument->activity_id = $medical_bills_service_activity->id;
                    $activityDocument->document_id = MedicalBill::ACTIVITY_DOCUMENT_ATTACHMENTS_ELECTRONIC_INVOICE;
                    $activityDocument->path = $uniqueFilename;
                    $activityDocument->uploaded_at = Carbon::now();
                    $activityDocument->save();

                    //Captura de XML
                    $responseXml = $this->readXML($xmlFile, $medicalBill);
                } else {
                    $errors[] = "Estás intentando subir varios archivos XML para una pre factura única con múltiples servicios. 
                    Por favor, sube solo un archivo XML con los servicios correspondientes.";
                    throw new \Exception(implode(", ", $errors));
                }
                // Captura de soportes de factura electrónica (PDF)
                if ($req->hasFile('invoice_support_files')) {

                    //Validaciones para los archivos pdf's
                    $this->validateFilesPDF($req, "invoice_support_files", $customMessages, "Cargar soportes de factura electrónica (PDF)");

                    $pdfFiles = $req->file('invoice_support_files');
                    foreach ($pdfFiles as $pdf) {
                        if ($pdf->getClientMimeType() !== 'application/pdf') {
                            $errors[] = "Solo se permiten archivos PDF.";
                            throw new \Exception(implode(", ", $errors));
                        }

                        //nombre original del documento
                        $originalName = pathinfo($pdf->getClientOriginalName(), PATHINFO_FILENAME);

                        // Generar un nombre único para cada archivo
                        $uniqueFilename = "activity_document/" . uniqid() . "_{$originalName}_{$action_id}.pdf";

                        // Guardar el PDF en S3
                        Storage::disk('s3')->put($uniqueFilename, file_get_contents($pdf));

                        // Guardar la información del documento en la base de datos
                        $activityDocument = new ActivityDocument();
                        $activityDocument->activity_id = $medical_bills_service_activity->id;
                        $activityDocument->document_id = MedicalBill::ACTIVITY_DOCUMENT_ATTACHMENTS_ELECTRONIC_INVOICE_SUPPORT;
                        $activityDocument->path = $uniqueFilename;
                        $activityDocument->uploaded_at = Carbon::now();
                        $activityDocument->save();
                    }
                }

                // Captura otros soportes (PDF)
                if ($req->hasFile('additional_support_files')) {

                    //Validaciones para los archivos pdf's
                    $this->validateFilesPDF($req, "additional_support_files", $customMessages, "Cargar otros soportes (PDF)");

                    $files = $req->file('additional_support_files');
                    foreach ($files as $pdf) {
                        if ($pdf->getClientMimeType() !== 'application/pdf') {
                            $errors[] = "Solo se permiten archivos PDF.";
                            throw new \Exception(implode(", ", $errors));
                        }

                        //nombre original del documento
                        $originalName = pathinfo($pdf->getClientOriginalName(), PATHINFO_FILENAME);

                        // Generar un nombre único para cada archivo
                        $uniqueFilename = "activity_document/" . uniqid() . "_{$originalName}_{$action_id}.pdf";

                        // Guardar el PDF en S3
                        Storage::disk('s3')->put($uniqueFilename, file_get_contents($pdf));

                        // Guardar la información del documento en la base de datos
                        $activityDocument = new ActivityDocument();
                        $activityDocument->activity_id = $medical_bills_service_activity->id;
                        $activityDocument->document_id = MedicalBill::ACTIVITY_DOCUMENT_ATTACHMENTS_OTHER_SUPPORT;
                        $activityDocument->path = $uniqueFilename;
                        $activityDocument->uploaded_at = Carbon::now();
                        $activityDocument->save();
                    }
                }

                //Cambiar estado de la actividad
                $activity_action = ActionController::create($medical_bills_service_activity->id,
                    $action_id,
                    'REPORTAR FACTURA ELECTRÓNICA');

                //Si el afiliado no se encuentra
                if (empty(Auth::user()->email)) {
                    $errors[] = "El usuario logueado no cuenta con un correo asociado.";
                    throw new \Exception(implode(", ", $errors));
                }
                //Monto total a pagar factura electronica (correo)
                $mount = "";
                if (!empty($responseXml->total_value_invoice)) {
                    $mount = AppServiceProvider::$TYPE_CURRENCY[$responseXml->type_currency_prefacture] ?? '';
                    $mount .= number_format($responseXml->total_value_invoice, 2, ',', '.');
                }

                //Validación de la cantidad de facturas subidas
                if ($numElectronicInvoice === 0) {
                    $errors[] = "El número de facturas electronicas subidas no puede ser 0, intentelo nuevamente.";
                    throw new \Exception(implode(", ", $errors));
                }

                // Determinación de singular o plural
                $isPlural = $numElectronicInvoice > 1;

                $emailData = TemplateBuilder::build(
                    Templates::RECEIPT_OF_INVOICES,
                    [
                        'invoice' => $isPlural ? 'facturas' : 'factura',
                        'name' => ucwords(strtolower(Auth::user()->first_name)),
                        'connector' => $isPlural ? 'las' : 'la',
                        'num_electronic_invoice' => $numElectronicInvoice,
                        'mount' => $mount,
                    ]
                );

                //TODO: Envío de correo por haber radicado la factura electrónica correctamente
                $emails = [Auth::user()->email];
                $mailSent = new SendDocumentDataBase(
                    implode(',', $emails),
                    $emailData['subject'], //tener en cuenta el plural (si son varios xmls)
                    "<EMAIL>",
                    $emailData['subject'], //tener en cuenta el plural (si son varios xmls)
                    [
                        "text" => $emailData['body'],
                        "sender" => $emailData['sender']
                    ], //tener en cuenta el plural (si son varios xmls)
                    "<EMAIL>",
                    [],
                    "send_document_db",
                    $client,
                    request()->getHost(),
                    $medical_bills_service_activity->id,
                    $activity_action->id,
                    $medical_bills_service_activity->service->id
                );
                
                // Capturar el resultado del envío
                $result = $mailSent->sendMail();

                //Registramos los datos del correo enviado para la trazabilidad
                $mailBoardController = new MailBoardController();
                $mailBoardController->createRegisterMail(
                    $medical_bills_service_activity->id,
                    $medical_bills_service_activity->service->id, 
                    '', 
                    'Usuario', 
                    ucwords(strtolower(Auth::user()->first_name)), 
                    Auth::user()->identification_number, 
                    $emailData['subject'], 
                    $emailData['body'],
                    $emails, 
                    $result,
                    null
                );

                DB::commit();

                return redirect('/proveedor/tablero_cuentas_medicas')->with('success', 'Se cargó la factura electrónica correctamente.');
            } else {
                $errors[] = "Debes ser un proveedor para poder radicar una factura electrónica.";
                throw new \Exception(implode(", ", $errors));
            }
        } catch (\Exception $e) {
            // Hacer rollback si hay algún error
            DB::rollBack();

            // Aquí capturamos todos los errores acumulados
            $errors = collect([$e->getMessage()]); // Usamos un arreglo de un solo error

            // Redirigir con los mensajes de error
            return redirect()->back()->withErrors($errors)->withInput();
        }
    }

    public function savePreInvoice(Request $req, $cpath, $id)
    {

        $client = Client::where('path', $cpath)->firstOrFail();
        //Actividad de Cuenta Medica
        $medical_bills_service_activity = Activity::where('client_id', $client->id)
            ->where('id', $id)
            ->firstOrFail();


        $resultPreInvoice = $req->input('resultPreInvoice');
        $preinvoiceIds = $req->input('preinvoice_ids');
        $results = $req->input('results');
        $preinvoice_reasons = $req->input('rejection_reason_preinvoice');

        if (!$preinvoice_reasons) {
            $errors = "No hay datos para actualizar.";
            throw new \Exception($errors); // Lanzar excepción con el error
        }

        foreach ($preinvoiceIds as $index => $id_pre) {

            $selectedResult = $results[$index];

            $dataPreinvoice = DataPreInvoice::where('id', $id_pre)->first();


            if ($dataPreinvoice) { // Verifica si se encontró la pre factura
                $dataPreinvoice->result = $selectedResult;

                // Guardar la razón de rechazo
                $dataPreinvoice->reason = $preinvoice_reasons[$index] ?? ''; // Asignar razón si está disponible
                $dataPreinvoice->save();
            } else {
                $errors = 'No se encontró la pre factura con ID ' . $id_pre;
                throw new \Exception($errors); // Lanzar excepción con el error
            }

        }

        $medicalBill = MedicalBill::where('activity_id', $id)->first();
        $medicalBill->result_preinvoice = $resultPreInvoice;

        if ($resultPreInvoice == 'aprobado') {

            /*
            Renapp revisa de manera automática o se puede revisar de manera manual, con el número del siniestro, si:

            //Punto A
            Con esto validamos que el servicio (prestación médica o el padre de los servicios
            medicamentos e Insumos) tenga por lo menos asignado el número de siniestro, teniendo en cuenta la fecha de atención (o fecha de valoración)*/

            //Punto B
            //Todas las prestaciones médicas presentadas por el proveedor deben pertenecer a él

            //lista de todos los registros reportados en la prefactura asociada a la cuenta médica actual
            $listPreInvoice = $medicalBill->data_pre_invoice;

            //Lista de servicios permitidos
            $listServices = ["prestación médica", "medicamentos", "insumos o mot"];

            foreach ($listPreInvoice as $preInvoice) {
                $service = strtolower($preInvoice->service);
                //Aquí se valida el punto A y el punto B para cada uno de los servicios / registros reportados en la prefactura
                if (in_array($service, $listServices)) {
                    $this->validateApprovePreInvoice($preInvoice, $medicalBill, $medical_bills_service_activity);
                } else {
                    $errors = "El servicio proporcionado no es válido " . $preInvoice->service;
                    throw new \Exception($errors); // Lanzar excepción con el error
                }
            }

            //Aprobar la prefactura
            $action_id = ActionMedicalBillsServiceSort::APROBAR_PREFACTURA;
            $activity_action = ActionController::create($medical_bills_service_activity->id,
                $action_id,
                'APROBAR PRE FACTURA');

        }
        if ($resultPreInvoice == 'rechazado') {
            $action_id = ActionMedicalBillsServiceSort::RECHAZAR_PREFACTURA;
            $activity_action = ActionController::create($medical_bills_service_activity->id,
                $action_id,
                'RECHAZAR PRE FACTURA');
            $medicalBill->return_reason_pre_invoice = $req->input('return_reason_pre_invoice');
        }
        $medicalBill->save();
    }

    public function saveElectronicInvoice(Request $req, $client, $medical_bills_service_data, $medical_bills_service_activity)
    {
        $medical_bills_service_data->return_reason_pre_invoice = $req->input('return_reason_pre_invoice');
        $medical_bills_service_data->return_reason_electronic_invoice = $req->input('return_reason_electronic_invoice');
        $medical_bills_service_data->lift_deduction_reason = $req->input('lift_deduction_reason');
        $medical_bills_service_data->balance = $req->input('balance');
        $medical_bills_service_data->deducted_amount = $req->input('deducted_amount');
        $medical_bills_service_data->audit_management = $req->input('audit_management');
        $medical_bills_service_data->result_electronic_invoice = $req->input('result_electronic_invoice');

        //Actualiza estado de la actividad por la acción APROBAR CARGUE DE FACTURA
        if ($req->input('result_electronic_invoice') === 'approve-electronic-invoice') {

            /*
              Renapp revisa de manera automática o se puede revisar de manera manual, con el número del siniestro, si:

              //Punto A
              Con esto validamos que el servicio (prestación médica o el padre de los servicios
              medicamentos e Insumos) tenga por lo menos asignado el número de siniestro, teniendo en cuenta la fecha de atención (o fecha de valoración)*/

            //Punto B
            //Todas las prestaciones médicas presentadas por el proveedor deben pertenecer a él

            //lista de todos los registros reportados en la prefactura asociada a la cuenta médica actual
            $listPreInvoice = $medical_bills_service_data->data_pre_invoice;

            //Lista de servicios permitidos
            $listServices = ["prestación médica", "medicamentos", "insumos o mot"];

            foreach ($listPreInvoice as $preInvoice) {
                $service = strtolower($preInvoice->service);
                //Aquí se valida el punto A y el punto B para cada uno de los servicios / registros reportados en la prefactura
                if (in_array($service, $listServices)) {
                    $this->validateApprovePreInvoice($preInvoice, $medical_bills_service_data, $medical_bills_service_activity);
                } else {
                    $errors = "El servicio proporcionado no es válido " . $preInvoice->service;
                    throw new \Exception($errors); // Lanzar excepción con el error
                }
            }

            //Asignar estado de contabilidad (pendiente)
            $medical_bills_service_data->accounting_status = MedicalBill::ACCOUNTING_STATUS_PENDING;
            $medical_bills_service_data->save();

            //Cambiar estado de la actividad
            $action_id = ActionMedicalBillsServiceSort::APROBAR_PAGO_FACTURA_ELECTRONICA;
            $activity_action = ActionController::create($medical_bills_service_activity->id,
                $action_id,
                'APROBAR FACTURA ELECTRÓNICA');
        }

        //Actualiza estado de la actividad por la acción Reportar Factura Electronica
        if ($req->input('result_electronic_invoice') === 'return-electronic-invoice') {

            //Cambiar estado de la actividad
            $action_id = ActionMedicalBillsServiceSort::DEVOLVER_FACTURA_ELECTRONICA;
            $activity_action = ActionController::create($medical_bills_service_activity->id,
                $action_id,
                'DEVOLVER FACTURA ELECTRÓNICA');

            //lista de todos los registros reportados en la factura electrónica asociada a la cuenta médica actual
            $listElectronicInvoice = $medical_bills_service_data->data_electronic_invoice;

            //Validar que exista información
            if (!$listElectronicInvoice) {
                $errors = "No hay datos de factura electronica";
                throw new \Exception($errors); // Lanzar excepción con el error
            }
            // Construcción de las filas con los datos de las facturas y motivos en líneas separadas
            $invoiceRows = "<table><tr><th>N° de factura</th><th>N° de caso</th><th>Motivo del no pago</th></tr>";
            foreach ($listElectronicInvoice as $invoice) {
                if ($invoice->result === "rechazado") {
                    $invoiceRows .= "<tr>
                                <td>" . $medical_bills_service_data->id . "</td>
                                <td>" . ($invoice->policySort ? $invoice->policySort->formatNumberConsecutive() : 'Poliza no asociada') . "</td>
                                <td>" . $invoice->reason . "</td>
                            </tr>";
                }
            }

            //Capturar el usuario que radicó la factura electrónica
            $user = User::where('affiliate_id', $medical_bills_service_activity->affiliate_id)->first();
            if (!$user) {
                $errors = "La cuenta médica no tiene un un afiliado asociado.";
                throw new \Exception($errors); // Lanzar excepción con el error
            }
            if (!$user->email) {
                $errors = "El proveedor de la cuenta médica no tiene un correo asociado.";
                throw new \Exception($errors); // Lanzar excepción con el error
            }

            //TODO: Envío de correo por haber radicado la factura electrónica correctamente
            $emails = [$user->email];

            $isPluralInvoice = count($listElectronicInvoice) > 1;
            $textInvoice = 'Luego de la revisión efectuada, se determinó que lamentablemente la factura que se detalla, no puede ser pagada por el motivo que se indica a continuación:';
            if($isPluralInvoice) {
                $textInvoice = 'Luego de la revisión efectuada, se determinó que lamentablemente las facturas que se detallan, no pueden ser pagadas por el motivo que se indica a continuación:';
            }

            $emailData = TemplateBuilder::build(
                Templates::INVOICE_RETURN,
                [
                    'invoice_subject' => $isPluralInvoice ? "facturas" : "factura",
                    'provider_name' => ucwords(strtolower($user->first_name)) ?? '',
                    'text_invoice' => $textInvoice,
                    'invoice_rows' => preg_replace('/\s+/', ' ', $invoiceRows),
                ]
            );

            $mailSent = new SendDocumentDataBase(
                implode(',', $emails),
                $emailData['subject'], //tener en cuenta el plural (si son varios xmls)
                "<EMAIL>",
                $emailData['subject'], //tener en cuenta el plural (si son varios xmls)
                [
                    "text" => $emailData['body'],
                    "sender" => $emailData['sender']
                ], //tener en cuenta el plural (si son varios xmls)
                "<EMAIL>",
                [],
                "send_document_db",
                $client,
                request()->getHost(),
                $medical_bills_service_activity->id,
                $activity_action->id,
                $medical_bills_service_activity->service->id
            );
            
            // Capturar el resultado del envío
            $result = $mailSent->sendMail();

            //Registramos los datos del correo enviado para la trazabilidad
            $mailBoardController = new MailBoardController();
            $mailBoardController->createRegisterMail(
                $medical_bills_service_activity->id,
                $medical_bills_service_activity->service->id, 
                '', 
                'Usuario', 
                ucwords(strtolower(Auth::user()->first_name)), 
                Auth::user()->identification_number, 
                $emailData['subject'], 
                $emailData['body'],
                $emails, 
                $result,
                null
            );
        }
    }

    public function validateMedicalServicesPreInvoice($preInvoice, $medicalBill)
    {
        //Validar que haya un GIS con el numero de caso (siniestro) proporcionado por la prefactura
        $gisSort = GisSort::where('consecutive_gis', $preInvoice->case_number)->first(); //buscar por el # aviso del caso

        if (!$gisSort) {
            $errors = 'No se encontró el siniestro ' . $preInvoice->case_number . ', reportado en la pre factura.';
            throw new \Exception($errors); // Lanzar excepción con el error
        }
        // Lista de actividades de prestaciones médicas asociadas a ese siniestro
        $listMedicalServiceActivities = Activity::where('parent_id', $gisSort->activity_id)
            ->where('service_id', Service::SERVICE_MEDICAL_SERVICES_SORT_MNK)->pluck('id');

        // Buscamos todas las prestaciones médicas para ese siniestro
        $listMedicalServicesSort = MedicalServicesSort::whereIn('activity_id', $listMedicalServiceActivities)->get();

        // Validamos si existen prestaciones médicas
        if (count($listMedicalServicesSort) === 0) {
            $errors = 'No se puede aprobar la pre factura porque no se encontró ninguna prestación médica asociado al siniestro ' . $preInvoice->case_number . ', reportado en la pre factura.';
            throw new \Exception($errors); // Lanzar excepción con el error
        }
        return $listMedicalServicesSort;
    }

    public function validateFollowUpValuationDate($preInvoice, $medicalBill)
    {
        //Lista de prestaciones medicas para el siniestro
        $listMedicalServicesSort = $this->validateMedicalServicesPreInvoice($preInvoice, $medicalBill);

        //Esta variable es para validar el punto A
        $result = false;

        //Recorremos todas las prestaciones encontradas para validar la fecha de valoración contra la fecha de atención proporcionada por la prefacura
        foreach ($listMedicalServicesSort as $medicalServiceSort) {

            //Validamos si existe en un seguimiento ( de la servicio prestación médica) una la fecha valoración que sea la misma fecha reportada en la prefactura para este registro
            $lastFollowUpValuationDate = $medicalServiceSort->followUps()->orderBy('id', 'desc')->limit(1)->pluck('valuation_date')->first();

            //Si la fecha de la pre factura es igual a la fecha de valoración del último seguimiendo de la prestación médica
            if ($preInvoice->service_date === $lastFollowUpValuationDate) {
                return $medicalServiceSort;
            }
        }


        if (!$result) {
            $errors = "No se puede aprobar la pre factura.<br>" .
                "Porque no existe una prestación médica con la misma fecha de atención.<br>" .
                "<strong>Datos del servicio reportado en la pre factura que ha generado este conflicto:</strong><br>" .
                "<strong>Nombre del servicio:</strong> {$preInvoice->service}<br>" .
                "<strong>Número de siniestro:</strong> {$preInvoice->case_number}<br>" .
                "<strong>Fecha de atención:</strong> {$preInvoice->service_date}";
            throw new \Exception($errors); // Lanzar excepción con el error
        }
    }

    public function validateApprovePreInvoice($preInvoice, $medicalBill, $activity)
    {
        //Prestación médica con la misma fecha de atención
        $medicalServiceSort = $this->validateFollowUpValuationDate($preInvoice, $medicalBill);
        //Capturar el id del proveedor con el afiliado de la actividad (quién generó la pre factura)
        $provider_id = AppServiceProvider::$PROVIDERS_AFFILIATES[$activity->affiliate_id] ?? '';
        //consultar si existe el proveedor
        $provider = Provider::where('id', $provider_id)->first();

        //Validar si existe un nombre de proveedor
        if (!$provider) {
            $errors = "El proveedor asignado a la cuenta médica no es válido.";
            throw new \Exception($errors); // Lanzar excepción con el error
        }

        //Verificar si la prestación médica tiene un proveedor asociado
        if (empty($medicalServiceSort->primary_care_provider)) {
            $errors = "La prestación médica no tiene un proveedor asociado.";
            throw new \Exception($errors); // Lanzar excepción con el error
        }

        //Validar si el proveedor que creo la cuenta medica (pre factura) coincide con el proveedor asociado a la prestación médica (filtrada por el número de caso y fecha de valoración)
        if ($medicalServiceSort->primary_care_provider !== $provider->id) {
            $errors = "No se puede aprobar la pre factura.<br>" .
                "Porque el proveedor " . strtoupper(optional($medicalServiceSort->provider())->name) . " de la prestación médica, no coincide con el proveedor " . strtoupper($provider->name) . " que radicó esta prefactura.<br>" .
                "<strong>Datos del servicio reportado en la pre factura que ha generado este conflicto:</strong><br>" .
                "<strong>Nombre del servicio:</strong> {$preInvoice->service}<br>" .
                "<strong>Número de siniestro:</strong> {$preInvoice->case_number}<br>" .
                "<strong>Fecha de atención:</strong> {$preInvoice->service_date}";
            throw new \Exception($errors); // Lanzar excepción con el error
        }
    }

    public function pdf(Request $req, $cpath, $id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();

        $medical_bills = new MedicalBill;
        $medical_bills->activity_id = $id;
        $medical_bills->setRelation('activity', $activity);
        $medical_bills->save();

        $pdf = PDF::loadView('services.plantilla.docs.plantilla_pdf', ['plantilla' => $medical_bills, 'watermark' => true]);

        return $pdf->stream('preview.pdf');
    }

    public function preInvoice(Request $req, $cpath, $id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();

        if ($req->input('pre_invoice') == 'approve') {
            DB::beginTransaction();
            try {
                ActionController::create($activity->id, ActionMedicalBills::APROBAR_PRE_FACTURA, 'APROBAR PRE FACTURA');
                DB::commit();
                return response()->json(['status' => 'success', 'message' => 'PRE FACTURA APROBADA']);
            } catch (\Exception $e) {
                DB::rollback();
                return response()->json(['status' => 'error', 'message' => $e->getMessage()]);
            }
        }
    }

    public function rejectPreInvoice(Request $req, $cpath, $id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        DB::beginTransaction();
        try {

            //TODO: Recibir servicio / actividad
            $medicalBillsServiceActivity = Activity::where('id', $id)->where('client_id', $client->id); //Cuenta médica
            $medicalServiceActivity = Activity::where('id', $medicalBillsServiceActivity->parent_id)->where('client_id', $client->id); //prestación medica

            //Existe una relación entre padre e hijo. hubo una prestación médica
            if ($medicalServiceActivity) {
                //TODO: Cambiar el estado de la actividad
                $action_id = ActionMedicalBillsServiceSort::RECHAZAR_PREFACTURA;

                $description = "RECHAZAR PRE FACTURA";
                $activityActionsCreated = ActionController::create($medicalBillsServiceActivity->id,
                    $action_id,
                    $description);

                //Guardar razón o motivo del rechazo
                $medicalBillsTable = MedicalBill::where('activity_id', $id)->first();
                $medicalBillsTable->reason = $req->input('reason');
                $medicalBillsTable->save();

                DB::commit();
            }

            return response()->json(['id' => $medicalBillsServiceActivity->id]);

        } catch (\Exception $e) {
            DB::rollBack();
            // Retornar un mensaje de error o manejar la excepción
            return response()->json([
                'error' => 'Ocurrió un error al guardar los datos',
                'code' => $e->getCode(),
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function electronicInvoicePayment(Request $req, $cpath, $id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        DB::beginTransaction();
        try {

            //TODO: Recibir servicio / actividad
            $medicalBillsServiceActivity = Activity::where('id', $id)->where('client_id', $client->id); //Cuenta médica
            $medicalServiceActivity = Activity::where('id', $medicalBillsServiceActivity->parent_id)->where('client_id', $client->id); //prestación medica

            //Existe una relación entre padre e hijo, es decir existe una prestación médica
            if ($medicalServiceActivity) {
                //Preparamos nuestra tabla por si hay datos que necesiten guardarse en este
                $medicalBillsTable = MedicalBill::where('activity_id', $id)->first();

                // Capturamos el resultado de la gestión de factura electrónica
                $optionElectronicInvoice = $req->input('result_electronic_invoice_management');

                // Definir descripción y acción según el resultado
                switch ($optionElectronicInvoice) {
                    case 'approveElectronicInvoicePayment':
                        $description = "APROBAR PAGO FACTURA ELECTRÓNICA";
                        $action_id = ActionMedicalBillsServiceSort::APROBAR_PAGO_FACTURA_ELECTRONICA;
                        break;

                    case 'returnElectronicInvoice':
                        $description = "DEVOLVER FACTURA ELECTRÓNICA";
                        $action_id = ActionMedicalBillsServiceSort::DEVOLVER_FACTURA_ELECTRONICA;
                        break;

                    case 'partialDisallowance':
                        $description = "PAGAR GLOSA PARCIAL FACTURA ELECTRÓNICA";
                        $action_id = ActionMedicalBillsServiceSort::PAGAR_GLOSA_PARCIAL_FACTURA_ELECTRONICA;
                        break;

                    case 'ratifyDisallowance':
                        $description = "RATIFICAR GLOSA";
                        $action_id = ActionMedicalBillsServiceSort::RATIFICAR_GLOSA;
                        break;

                    default:
                        return response()->json([
                            'message' => 'Opción no válida, solicitud incorrecta.',
                        ], 400); // Código de error adecuado
                }

                // Guardar los nuevos campos en la tabla MedicalBill
                if ($req->has('state_electronic_invoice_management')) {
                    $medicalBillsTable->state_electronic_invoice_management = $req->input('state_electronic_invoice_management');
                }
                if ($req->has('audit_electronic_invoice_management')) {
                    $medicalBillsTable->audit_electronic_invoice_management = $req->input('audit_electronic_invoice_management');
                }

                // Guardar razón o motivo del rechazo si existe
                if ($req->has('reason')) {
                    $medicalBillsTable->reason = $req->input('reason');
                }

                // Guardar los cambios
                $medicalBillsTable->save();

                //TODO: Actualizar estado de la actividad
                $activityActionsCreated = ActionController::create(
                    $medicalBillsServiceActivity->id,
                    $action_id,
                    $description
                );

                // Confirmar la transacción
                DB::commit();

                return response()->json([
                    'message' => 'Acción realizada con éxito.',
                ], 200);
            }

            return response()->json(['id' => $medicalBillsServiceActivity->id]);

        } catch (\Exception $e) {
            DB::rollBack();
            // Retornar un mensaje de error o manejar la excepción
            return response()->json([
                'error' => 'Ocurrió un error al ejecutar la acción',
            ], 500);
        }
    }

    public function submitElectronicInvoice(Request $req, $cpath, $id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        DB::beginTransaction();
        try {

            //TODO: Recibir servicio / actividad
            $medicalBillsServiceActivity = Activity::where('id', $id)->where('client_id', $client->id); //Cuenta médica

            //Preparamos nuestra tabla por si hay datos que necesiten guardarse en este
            $medicalBillsTable = MedicalBill::where('activity_id', $id)->first();
            $medicalBillsTable->value_disallowance = 0; // Valor a pagar de glosa es 0
            $medicalBillsTable->save();

            $description = "REMITIR FACTURA ELECTRÓNICA";
            $action_id = ActionMedicalBillsServiceSort::REMITIR_FACTURA_ELECTRONICA;

            //TODO: Actualizar estado de la actividad
            $activityActionsCreated = ActionController::create(
                $medicalBillsServiceActivity->id,
                $action_id,
                $description
            );

            // Confirmar la transacción
            DB::commit();

            return response()->json(['id' => $medicalBillsServiceActivity->id]);

        } catch (\Exception $e) {
            DB::rollBack();
            // Retornar un mensaje de error o manejar la excepción
            return response()->json([
                'error' => 'Ocurrió un error al ejecutar la acción',
            ], 500);
        }
    }

    public function reportPaymentProcessed(Request $req, $cpath, $idActivityMedicalBills)
    {
        $client = Client::where('path', $cpath)->firstOrFail();

        //TODO: Recibir servicio / actividad
        $medicalBillsServiceActivity = Activity::where('id', $idActivityMedicalBills)->where('client_id', $client->id)->first(); //Cuenta médica

        //Verificar que se haya capturado la actividad
        if (!$medicalBillsServiceActivity) {
            $errors[] = "No se pudo encontrar la actividad de cuentas médicas.";
            throw new \Exception(implode(", ", $errors));
        }

        //TODO: Capturar tabla de cuentas médicas
        $medicalBillsTable = MedicalBill::where('activity_id', $medicalBillsServiceActivity->id)->first();

        // Validar que $medicalBillsTable
        if (empty($medicalBillsTable)) {
            throw new \Exception("Datos médicos no válidos o no encontrados.");
        }

        //TODO: Actualizar estado de la actividad
        $description = "REPORTAR PAGO REALIZADO";
        $action_id = ActionMedicalBillsServiceSort::REPORTAR_PAGO_REALIZADO;

        $activity_action = ActionController::create(
            $medicalBillsServiceActivity->id,
            $action_id,
            $description
        );

        //TODO: INDICE SINIESTRAL
        // Consultar valores asociados a cuentas médicas
        $dataElectronicInvoice = DataElectronicInvoice::where('id_medical_bills', $medicalBillsTable->id)->get();

        // Filtrar solo las facturas aprobadas
        $approvedInvoices = $dataElectronicInvoice->filter(function ($invoice) {
            return $invoice->result == 'aprobado';
        });

        // Agrupar por el número de caso (case_number)
        $groupedByCaseNumber = $approvedInvoices->groupBy('case_number');

        // Inicializar un arreglo para almacenar los totales por grupo
        $totalApprovedByCase = [];

        // Recorrer los grupos y acumular los valores aprobados por cada grupo
        foreach ($groupedByCaseNumber as $caseNumber => $invoices) {
            $totalApprovedByCase[$caseNumber] = $invoices->sum('service_value');
        }

        // Recorrer $totalApprovedByCase
        foreach ($totalApprovedByCase as $caseNumber => $approvedValue) {
            // Consultar GIS Sort por el número de caso
            $gisSort = GisSort::where('id', $caseNumber)->first();

            // Validar si existe el GIS Sort
            if (!$gisSort) {
                throw new \Exception("No se pudo encontrar el GIS Sort para el número de aviso {$caseNumber}.");
            }

            // Actualizar el incidente rate solo si hay valores aprobados
            $gisSort->incident_rate += $approvedValue;

            // Guardar el GIS Sort con el valor actualizado
            $gisSort->save();
        }

        //Actualizar estado de la cuenta médica
        $medicalBillsTable->accounting_status = MedicalBill::ACCOUNTING_STATUS_APPROVED;
        $medicalBillsTable->save();

        //TODO: Enviar correo al proveedor - Reportar pago realizado

        //Fecha de pago (esto lo proporciona acsel o se puede declarar la fecha de pago aquí?)
        $paymentDate = Carbon::now();

        if ($medicalBillsTable->process_type) {
            $this->sendEmailElectronicInvoiceMultiple($medicalBillsServiceActivity, $medicalBillsTable, $activity_action, $client, $paymentDate);
        } else {
            $this->sendEmailElectronicInvoice($medicalBillsServiceActivity, $medicalBillsTable, $activity_action, $client, $paymentDate);
        }
    }

    public function validateAccountingState($result, $cpath, $idActivityMedicalBills)
    {
        //TODO: Validar estado de la cuenta médica
        if ($result === "aprobado") {
            //Pasamos un request vacío
            $request = new Request([]);
            $this->reportPaymentProcessed($request, $cpath, $idActivityMedicalBills);

        } elseif ($result === "rechazado") {

            //TODO: Cambiar el estado de la cuenta médica (accounting_status)
            $medicalBillsServiceActivity = Activity::where('id', $idActivityMedicalBills)->first(); //Cuenta médica

            //Verificar que se haya capturado la actividad
            if (!$medicalBillsServiceActivity) {
                $errors[] = "No se pudo encontrar la actividad de cuentas médicas.";
                throw new \Exception(implode(", ", $errors));
            }

            //TODO: Capturar tabla de cuentas médicas
            $medicalBillsTable = MedicalBill::where('activity_id', $medicalBillsServiceActivity->id)->first();

            // Validar que $medicalBillsTable
            if (empty($medicalBillsTable)) {
                throw new \Exception("Datos médicos no válidos o no encontrados.");
            }

            //Actualizar estado de la cuenta médica
            $medicalBillsTable->accounting_status = MedicalBill::ACCOUNTING_STATUS_REJECTED;
            $medicalBillsTable->save();
        }
    }

    //Se ejecutará por un botón en la pantalla de auditoría de cuentas médicas (para test DEV Y QA)
    public function validateAccountingStateTest($cpath)
    {
        $this->approvedMedicalBills($cpath);
    }

    //se ejecturá automaticamente cada noche (API)
    public function approvedMedicalBills($cpath)
    {
        //TODO: ENDPOINT (Conexión con acsel) para obtener las cuentas médicas aprobadas -> esto toca mejorar cuando esté la integración
        //TODO: Cuando esté la integración de acsel hay que pasarle la retención de la cuenta medica y el total de la factura (de los servicios aprobados)

        // Obtener únicamente los IDs de las actividades que tienen facturas médicas asociadas
        $activityIds = Activity::where('service_id', Service::SERVICE_MEDICAL_BILLS_MNK)
            ->where('state_id', \App\States\StateMedicalBillsServiceSort::FACTURA_ELECTRONICA_REMITIDA_A_CONTABILIDAD)
            ->whereHas('medical_bill') // Filtrar solo las actividades con medicalBills
            ->pluck('id')
            ->toArray();

        // Verificar si se encontraron actividades
        if (!empty($activityIds)) {
            // Buscar los Medical Bills asociados a las actividades filtradas, aprobados
            $medicalBills = MedicalBill::whereIn('activity_id', $activityIds)
                ->where('accounting_status', MedicalBill::ACCOUNTING_STATUS_PENDING)
                ->get();

            // Verificar si se encontraron cuentas médicas
            if ($medicalBills->isNotEmpty()) {
                // Iterar sobre cada cuenta médica
                foreach ($medicalBills as $medicalBill) {
                    DB::beginTransaction();
                    try {
                        // Validar el estado contable de la cuenta médica
                        $this->validateAccountingState("aprobado", $cpath, $medicalBill->activity_id);

                        // Confirmar las operaciones si todo fue exitoso
                        DB::commit();
                    } catch (\Exception $e) {
                        // Revertir cualquier cambio si ocurre un error
                        DB::rollBack();

                        // Registrar el error en la auditoría
                        AuditMedicalBill::create([
                            'id_activity_medical_bill' => $medicalBill->activity_id,
                            'id_table_medical_bill' => $medicalBill->id,
                            'error' => $e->getMessage(),
                        ]);

                        // Continuar con la siguiente cuenta médica
                        continue;
                    }
                }
            }
        }
    }

    public function sendEmailElectronicInvoiceMultiple($medicalBillsServiceActivity, $medicalBillsTable, $activity_action, $client, $paymentDate)
    {
        //Buscar el proveedor asociado a la actividad de la cuenta médica
        $affiliate = $medicalBillsServiceActivity->affiliate;

        //Validación si existe el proveedor asociado a la actividad
        if (!$affiliate) {
            $errors[] = "Ocurrio un error al buscar el proveedor asociado a la actividad.";
            throw new \Exception(implode(", ", $errors));
        }

        //Datos de la factura electrónica
        $listElectronicInvoice = $medicalBillsTable->data_electronic_invoice;

        //Validación si existe la factura electrónica asociada a la actividad
        if (!$listElectronicInvoice) {
            $errors[] = "Ocurrio un error al buscar la factura electrónica asociada a la actividad.";
            throw new \Exception(implode(", ", $errors));
        }

        //Capturar el correo del proveedor (pero a través de tabla USER)
        $provider = User::where('affiliate_id', $affiliate->user_id)->first();

        //Validación si existe un registro del proveedor en la tabla USER
        if (!$provider) {
            $errors[] = "Ocurrio un error los datos del proveedor asociado a la cuenta médica.";
            throw new \Exception(implode(", ", $errors));
        }

        //Contador de factura electrónica
        $numberElectronicInvoice = count($listElectronicInvoice);

        // Determinación de singular o plural
        $isPlural = $numberElectronicInvoice > 1;

        //Datos de la factura electrónica necesarios para el envío del correo
        //Fecha de pago de la factura electrónica
        if (!empty($paymentDate)) {
            $paymentDate = ucfirst(strftime('%A %e de %B del %Y', strtotime($paymentDate)));
        } else {
            $paymentDate = "Fecha no disponible";
        }

        //Monto total a pagar factura electronica (correo)
        $mount = "";
        if (!empty($medicalBillsTable->total_value_invoice)) {
            $mount = AppServiceProvider::$TYPE_CURRENCY[$medicalBillsTable->type_currency_prefacture] ?? '';
            $mount .= number_format($medicalBillsTable->total_value_invoice, 2, ',', '.');
        }

        // Construcción de las filas con los datos de las facturas y montos en formato de tabla
        $invoiceRows = "<table border='1'>
        <tr>
            <th>N° de factura</th>
            <th>Monto</th>
        </tr>";

        // Construcción de las filas con los datos de las facturas y motivos en líneas separadas
        $invoiceRowsReject = "";

        // Variable para almacenar el total pagado
        $totalAprroved = 0;

        //Contadores de facturas aprobadas y rechazadas
        $countElectronicInvoiceApproved = 0;
        $countElectronicInvoiceReject = 0;

        foreach ($listElectronicInvoice as $invoice) {
            if ($invoice->result === "aprobado") {
                //Contador de facturas aprobadas
                $countElectronicInvoiceApproved++;
                // Sumar el valor de cada factura para el total
                $totalAprroved += $invoice->service_value;

                // Obtener el tipo de moneda y el valor formateado
                $value = (AppServiceProvider::$TYPE_CURRENCY[$medicalBillsTable->type_currency_prefacture] ?? '') .
                    number_format($invoice->service_value, 2, ',', '.');

                // Agregar cada factura en una línea con el formato correcto
                $invoiceRows .= "<tr>
                                <td>" . $invoice->num_electronic_invoice_for_auditor . "</td>
                                <td>" . $value . "</td>
                            </tr>";
            } else {
                //Contador de facturas rechazadas
                $countElectronicInvoiceReject++;
                //Obtenemos las filas de las facturas rechazadas
                $invoiceRowsReject .= "N° de factura: " . $invoice->num_electronic_invoice_for_auditor . "\n" .
                    "Motivo del no pago: " . $invoice->reason . "\n\n";
            }
        }


        //TODO: Actualizar el valor pagado de cuentas médicas
        //tener en cuenta la retención de la fuente
        $totalValueTax = is_numeric($medicalBillsTable->total_value_tax) ? $medicalBillsTable->total_value_tax : 0;
        $medicalBillsTable->paid_amount = $totalAprroved - $totalValueTax;
        $medicalBillsTable->save();

        // Agregar la línea del total pagado al final
        $totalAprroved = (AppServiceProvider::$TYPE_CURRENCY[$medicalBillsTable->type_currency_prefacture] ?? '') .
            number_format($totalAprroved, 2, ',', '.');
        $invoiceRows .= "<tr>
                            <td><strong>TOTAL PAGADO</strong></td>
                            <td><strong>" . $totalAprroved . "</strong></td>
                        </tr>
                        </table>" . "\n";

        // Determinación de singular o plural
        $isPluralAproved = $countElectronicInvoiceApproved > 1;
        $isPluralReject = $countElectronicInvoiceReject > 1;

        //este mensaje se muestra en el correo siempre y cuando haya facturas rechazadas
        $messageInvoiceElectronicReject = "";
        if ($countElectronicInvoiceReject > 0) {
            $messageInvoiceElectronicReject = "Luego de la revisión realizada, se determina que ";
            if ($isPluralReject) {
                $messageInvoiceElectronicReject .= "Por otra parte, luego de la revisión realizada, se determina que las facturas que se detallan, lamentablemente no pueden ser pagadas por el motivo que se indica a continuación:";
            } else {
                $messageInvoiceElectronicReject .= "Por otra parte, luego de la revisión realizada, se determina que la factura que se detalla, lamentablemente no puede ser pagada por el motivo que se indica a continuación:";
            }
        }

        //TODO: Envío de correo por haber radicado la factura electrónica correctamente
        $emails = [$provider->email];
        $emailData = TemplateBuilder::build(
            Templates::INVOICE_PAYMENT_AND_REJECTION,
            [
                'provider_name' => ucwords(strtolower($provider->first_name)) ?? '',
                'invoice_subject' => $isPluralAproved ? "las facturas" : "la factura",
                'invoice_body' => $isPluralAproved ? "facturas" : "factura",
                'mount' => $mount,
                'payment_date' => $paymentDate,
                'invoice_rows' => preg_replace('/\s+/', ' ', $invoiceRows),
                'message_invoice_electronic_reject' => $countElectronicInvoiceReject > 0 ? $messageInvoiceElectronicReject : "DELETE_LINE", //Enviar DELETE_LINE si no se va usar el campo en el correo para eliminar la linea
                'invoice_rows_reject' => $countElectronicInvoiceReject > 0 ? preg_replace('/\s+/', ' ', $invoiceRowsReject) : "DELETE_LINE", //Enviar DELETE_LINE si no se va usar el campo en el correo para eliminar la linea
            ]
        );

        $mailSent = new SendDocumentDataBase(
            implode(',', $emails),
            $emailData['subject'], //tener en cuenta el plural (si son varios xmls)
            "<EMAIL>",
            $emailData['subject'],
            [
                "text" => $emailData['body'],
                "sender" => $emailData['sender']
            ], //tener en cuenta el plural (si son varios xmls)
            "<EMAIL>",
            [],
            "send_document_db",
            $client,
            request()->getHost(),
            $medicalBillsServiceActivity->id,
            $activity_action->id,
            $medicalBillsServiceActivity->service->id
        );
        
        // Capturar el resultado del envío
        $result = $mailSent->sendMail();

        //Registramos los datos del correo enviado para la trazabilidad
        $mailBoardController = new MailBoardController();
        $mailBoardController->createRegisterMail(
            $medicalBillsServiceActivity->id,
            $medicalBillsServiceActivity->service->id, 
            '', 
            'Proveedor', 
            ucwords(strtolower(Auth::user()->first_name)), 
            Auth::user()->identification_number, 
            $emailData['subject'], 
            $emailData['body'],
            $emails, 
            $result,
            null
        );
    }

    public function sendEmailElectronicInvoice($medicalBillsServiceActivity, $medicalBillsTable, $activity_action, $client, $paymentDate)
    {
        //Buscar el proveedor asociado a la actividad de la cuenta médica
        $affiliate = $medicalBillsServiceActivity->affiliate;

        //Validación si existe el proveedor asociado a la actividad
        if (!$affiliate) {
            $errors[] = "Ocurrio un error al buscar el proveedor asociado a la actividad.";
            throw new \Exception(implode(", ", $errors));
        }

        //Datos de la factura electrónica
        $listElectronicInvoice = $medicalBillsTable->data_electronic_invoice;

        //Validación si existe la factura electrónica asociada a la actividad
        if (!$listElectronicInvoice) {
            $errors[] = "Ocurrio un error al buscar la factura electrónica asociada a la actividad.";
            throw new \Exception(implode(", ", $errors));
        }

        //Contador de factura electrónica
        $numberElectronicInvoice = count($listElectronicInvoice);

        // Determinación de singular o plural
        $isPlural = $numberElectronicInvoice > 1;

        //Capturar el correo del proveedor (pero a través de tabla USER)
        $provider = User::where('affiliate_id', $affiliate->user_id)->first();

        //Validación si existe un registro del proveedor en la tabla USER
        if (!$provider) {
            $errors[] = "Ocurrio un error los datos del proveedor asociado a la cuenta médica.";
            throw new \Exception(implode(", ", $errors));
        }


        //Datos de la factura electrónica necesarios para el envío del correo
        //Fecha de pago de la factura electrónica
        if (!empty($paymentDate)) {
            $paymentDate = ucfirst(strftime('%A %e de %B del %Y', strtotime($paymentDate)));
        } else {
            $paymentDate = "Fecha no disponible";
        }

        //Monto total a pagar factura electronica (correo)
        $mount = "";
        if (!empty($medicalBillsTable->total_value_invoice)) {
            $mount = AppServiceProvider::$TYPE_CURRENCY[$medicalBillsTable->type_currency_prefacture] ?? '';
            $mount .= number_format($medicalBillsTable->total_value_invoice, 2, ',', '.');
        }

        // Construcción de las filas con los datos de las facturas y montos en formato de tabla
        $invoiceRows = "<table border='1'>
                        <tr>
                            <th>N° de factura</th>
                            <th>Monto</th>
                        </tr>";
        foreach ($listElectronicInvoice as $invoice) {
            // Obtener el tipo de moneda y el valor formateado
            $value = (AppServiceProvider::$TYPE_CURRENCY[$medicalBillsTable->type_currency_prefacture] ?? '') .
                number_format($invoice->service_value, 2, ',', '.');

            // Agregar cada factura en una línea con el formato correcto
            $invoiceRows .= "<tr>
                                <td>" . $medicalBillsTable->id . "</td>
                                <td>" . $value . "</td>
                            </tr>";

        }
        // Agregar la línea del total pagado al final
        $invoiceRows .= "<tr>
                            <td><strong>TOTAL PAGADO</strong></td>
                            <td><strong>" . $mount . "</strong></td>
                        </tr>
                        </table>" . "\n";

        //TODO: Actualizar el valor pagado de cuentas médicas
        //tener en cuenta la retención de la fuente
        $totalValueTax = is_numeric($medicalBillsTable->total_value_tax) ? $medicalBillsTable->total_value_tax : 0;
        $medicalBillsTable->paid_amount = $medicalBillsTable->total_value_invoice - $totalValueTax;
        $medicalBillsTable->save();

        //TODO: Envío de correo por haber radicado la factura electrónica correctamente
        $emails = [$provider->email];
        $emailData = TemplateBuilder::build(
            Templates::INVOICE_PAYMENT_AND_REJECTION,
            [
                'provider_name' => ucwords(strtolower($provider->first_name)) ?? '',
                'invoice_subject' => $isPlural ? "las facturas" : "la factura",
                'invoice_body' => $isPlural ? "facturas" : "factura",
                'mount' => $mount,
                'payment_date' => $paymentDate,
                'invoice_rows' => preg_replace('/\s+/', ' ', $invoiceRows), // Eliminar todos los espacio de la cadena en la tablas
                'message_invoice_electronic_reject' => 'DELETE_LINE', //Enviar DELETE_LINE si no se va usar el campo en el correo para eliminar la linea
                'invoice_rows_reject' => 'DELETE_LINE', //Enviar DELETE_LINE si no se va usar el campo en el correo para eliminar la linea
            ]
        );

        $mailSent = new SendDocumentDataBase(
            implode(',', $emails),
            $emailData['subject'], //tener en cuenta el plural (si son varios xmls)
            "<EMAIL>",
            $emailData['subject'],
            [
                "text" => $emailData['body'],
                "sender" => $emailData['sender']
            ], //tener en cuenta el plural (si son varios xmls)
            "<EMAIL>",
            [],
            "send_document_db",
            $client,
            request()->getHost(),
            $medicalBillsServiceActivity->id,
            $activity_action->id,
            $medicalBillsServiceActivity->service->id
        );
        
          // Capturar el resultado del envío
        $result = $mailSent->sendMail();

        //Registramos los datos del correo enviado para la trazabilidad
        $mailBoardController = new MailBoardController();
        $mailBoardController->createRegisterMail(
            $medicalBillsServiceActivity->id,
            $medicalBillsServiceActivity->service->id, 
            '', 
            'Proveedor', 
            ucwords(strtolower(Auth::user()->first_name)), 
            Auth::user()->identification_number, 
            $emailData['subject'], 
            $emailData['body'],
            $emails, 
            $result,
            null
        );
        
    }

    private function processAffiliateSpreadsheet($idMedicalBillNew, $row, &$errors, &$alerts, &$processedRecords, &$currencyType)
    {
        $row = $this->prepareRow($row);
        $rowNumber = $processedRecords + 1; // Cambiar este número según tu lógica de conteo

        //validar headers del excel
        if ($rowNumber === 1) {
            $this->validateHeaders($row, $rowNumber);
        }

        // filas donde están los valores
        $rowNumberValues = $rowNumber + 1;

        // Filtrar y validar campos
        $filteredRow = array_slice($row, 0, 9);
        if ($this->isRowEmpty($filteredRow, $rowNumberValues, $errors)) {
            return;
        }

        // Validar campos requeridos
        $this->validateRequiredFields($filteredRow, $rowNumberValues, $errors);

        // Convertir fechas y validar
        $serviceDate = $this->validateDate($filteredRow['fecha_valoracion'], $rowNumberValues, $errors);

        $fechaCaso = $this->validateDate($filteredRow['fecha_caso'], $rowNumberValues, $errors);

        // Validar campos numéricos (identificacion, consecutivo poliza)
        $this->validateNumericFields($filteredRow, $rowNumberValues, $errors);

        // Validar campos numéricos (identificacion, consecutivo poliza) que no deben tener puntos, comas o separadores
        $this->validateNumericFieldsWithoutSeparator($filteredRow, $rowNumberValues, $errors);

        //variable que se usará para validar el formato del mismo
        $value_service_format = $filteredRow['valor_servicio'];
        // validar formato valor servicio
        $this->validateFormatValueService($value_service_format, $rowNumberValues, $errors);

        // Validar tipo de servicio
        $this->validateServiceType($filteredRow['servicio'], $rowNumberValues, $errors);

        // Validar número de caso basado en el tipo de servicio
        $this->validateServiceAndAffiliate($filteredRow, $rowNumberValues, $errors);

        // Obtener datos procesados
        $numPolicy = (int)$filteredRow['consecutivo_poliza_sort'];
        $this->validatePolicySort($numPolicy, $filteredRow['aviso_caso'], $rowNumberValues, $errors);

        //Validar el tipo de moneda
        $this->validateCurrencyType($filteredRow['tipo_moneda'], $rowNumberValues, $errors);

        // Verificar consistencia en tipo de moneda
        if (strtolower($currencyType) !== strtolower($row['tipo_moneda'])) {
            $errors[] = "El tipo de moneda '{$row['tipo_moneda']}' en la fila {$rowNumberValues} no coincide con el tipo de moneda previamente establecido '" . ucwords(strtolower($currencyType)) . "' en la fila " . ($rowNumberValues - 1) . ".";
            throw new \Exception(implode(", ", $errors));
        }

        // Crear registro de pre factura
        $this->createDataPreInvoice($filteredRow, $serviceDate, $fechaCaso, $idMedicalBillNew, $numPolicy, $filteredRow['tipo_moneda']);

        // Al final del método, si hay errores, lanza una excepción
        if (!empty($errors)) {
            throw new \Exception(implode(", ", $errors)); // Lanzar excepción con los errores acumulados
        }
    }

    private function validateHeaders($row, $rowNumber)
    {
        // Claves requeridas en el orden correcto
        $requiredKeys = [
            "nombre_paciente",
            "identificacion_paciente",
            "consecutivo_poliza_sort",
            "fecha_valoracion",
            "fecha_caso",
            "aviso_caso",
            "servicio",
            "valor_servicio",
            "tipo_moneda"
        ];

        // Filtrar claves numéricas (si es necesario)
        $rowKeys = array_filter(array_keys($row), 'is_string');

        // Inicializar errores
        $errors = [];

        // Validar la cantidad de claves
        if (count($rowKeys) < count($requiredKeys)) {
            $errors[] = "La fila {$rowNumber} no contiene suficientes columnas. Se esperaban " . count($requiredKeys) . " columnas, pero se encontraron " . count($rowKeys) . ".";
            throw new \Exception(implode("; ", $errors)); // Lanzar excepción con los errores acumulados
        }

        // Validar las claves y su orden
        foreach ($requiredKeys as $index => $key) {
            // Verificar si la clave requerida está presente
            if (!array_key_exists($key, $row)) {
                $errors[] = "La fila {$rowNumber} falta la columna: '{$key}'.";
            }
        }

        // Lanzar excepción si hay errores
        if (!empty($errors)) {
            throw new \Exception(implode("; ", $errors)); // Lanzar excepción con los errores acumulados
        }
    }

    private function validateServiceAndAffiliate($filteredRow, $rowNumber, &$errors)
    {
        $idPatient = (int)$filteredRow['identificacion_paciente'];
        $alert_case = $filteredRow['aviso_caso'];

        //Consulta número de caso y ver si existe una prestación médica existente y a su vez validar si es el mismo afiliado
        $this->validateAffiliateNumCase($alert_case, $rowNumber, $idPatient, $errors);

    }

    private function validateAffiliateNumCase($alert_case, $rowNumber, $idPatient, &$errors)
    {

        //Validar que haya un GIS con el numero de caso (siniestro) proporcionado por el excel
        $gisSort = GisSort::where('consecutive_gis', $alert_case)->first(); //buscar por el # aviso del caso

        if (!$gisSort) {
            $errors[] = "No se encontró el siniestro con el siguiente número de aviso: {$alert_case} en la fila {$rowNumber}.";
            throw new \Exception(implode(", ", $errors));
        }

        // Lista de actividades de prestaciones médicas asociadas a ese siniestro
        $listMedicalServiceActivities = Activity::where('parent_id', $gisSort->activity_id)
            ->where('service_id', Service::SERVICE_MEDICAL_SERVICES_SORT_MNK)->pluck('id');

        // Validamos si existen prestaciones médicas
        if (count($listMedicalServiceActivities) === 0) {
            $errors[] = "No se encontró ninguna prestación médica asociado a este número de aviso del siniestro {$alert_case} en la fila {$rowNumber}";
            throw new \Exception(implode(", ", $errors));
        }

        // Obtener la identificación del afiliado asociado a los casos.
        $affiliateDocNumbers = Affiliate::whereIn('id', Activity::whereIn('id', $listMedicalServiceActivities)->pluck('affiliate_id'))
            ->pluck('doc_number');

        // Validar si la identificación del paciente coincide con la del afiliado.
        if (!$affiliateDocNumbers->contains($idPatient)) {
            $errors[] = "El número de aviso del siniestro no corresponde a la identificación del afiliado en la fila {$rowNumber}.";
            throw new \Exception(implode(", ", $errors));
        }
    }

    private function updateMedicalBillTotal($idMedicalBillNew, $total, $currencyType, &$errors, $processType)
    {
        // Encuentra la factura médica y actualiza el total
        $medicalBillTable = MedicalBill::where('id', $idMedicalBillNew)->first();
        if ($medicalBillTable) {

            // Guardar total de valores del servicio
            $medicalBillTable->total_value_prefacture = $total;

            //guardar tipo de proceso
            $medicalBillTable->process_type = $processType;

            // Guardar tipo de moneda
            $medicalBillTable->type_currency_prefacture = $currencyType;

            // Guardar la factura médica actualizada
            $medicalBillTable->save();
        } else {
            $errors[] = "No se encontró la factura médica con ID {$idMedicalBillNew}.";
            throw new \Exception(implode(", ", $errors)); // Lanzar excepción con los errores acumulados
        }
    }

    private function prepareRow($row)
    {
        if ($row instanceof \Maatwebsite\Excel\Collections\CellCollection) {
            $row = $row->toArray();
        }
        return array_change_key_case($row, CASE_LOWER);
    }

    private function isRowEmpty($filteredRow, $rowNumber, $errors)
    {
        $result = empty(trim($filteredRow['identificacion_paciente'])) &&
            empty(trim($filteredRow['consecutivo_poliza_sort'])) &&
            empty(trim($filteredRow['fecha_valoracion'])) &&
            empty(trim($filteredRow['fecha_caso'])) &&
            empty(trim($filteredRow['aviso_caso'])) &&
            empty(trim($filteredRow['servicio'])) &&
            empty(trim($filteredRow['valor_servicio'])) &&
            empty(trim($filteredRow['tipo_moneda']));

        return $result;
    }

    private function validateRequiredFields($filteredRow, $rowNumber, &$errors)
    {
        $fields = [
            'nombre_paciente' => 'nombre_paciente',
            'identificacion_paciente' => 'identificacion_paciente',
            'consecutivo_poliza_sort' => 'consecutivo_poliza_sort',
            'fecha_valoracion' => 'fecha_valoracion',
            'fecha_caso' => 'fecha_caso',
            'aviso_caso' => 'aviso_caso',
            'servicio' => 'servicio',
            'valor_servicio' => 'valor_servicio',
            'tipo_moneda' => 'tipo_moneda'
        ];


        foreach ($fields as $field => $fieldName) {
            if (empty($filteredRow[$field])) {
                $errors[] = "Faltan datos en la fila {$rowNumber}, campo: {$fieldName}";
            }
        }
        // Al final del método, si hay errores, lanza una excepción
        if (!empty($errors)) {
            throw new \Exception(implode(". ", $errors)); // Lanzar excepción con los errores acumulados
        }
    }

    private function validateDate($date, $rowNumber, &$errors)
    {
        try {
            // Si la fecha es un número, la convertimos desde formato Excel
            if (is_numeric($date)) {
                $dateObject = PHPExcel_Shared_Date::ExcelToPHPObject($date);

                // Convertir a Carbon para facilitar validaciones
                $dateObject = Carbon::instance($dateObject);
            } else {
                // Intentar parsear la fecha con el formato correcto
                $dateObject = Carbon::createFromFormat('d/m/Y', $date);
                if (!$dateObject) {
                    throw new \Exception("Formato inválido");
                }
            }

            // Ajustar zona horaria si es necesario
            $dateObject->setTimezone('America/Costa_Rica');

            // Validar que la fecha no sea futura
            if ($dateObject->greaterThan(Carbon::today('America/Costa_Rica'))) {
                $errors[] = "La fecha en la fila {$rowNumber} es fútura y no está permitida: {$dateObject->format('d/m/Y')}";
                throw new \Exception(implode(", ", $errors));
            }

            return $dateObject;
        } catch (\Exception $e) {
            $errors[] = "El formato de la fecha en la fila {$rowNumber} no es válido (d/m/Y): {$date}";
            throw new \Exception(implode(", ", $errors));
        }
    }
    private function validateFormatValueService($value_service, $rowNumber, &$errors)
    {
        // Reemplazar el punto por coma si es necesario (dependiendo del formato que prefieras)
        // Esto asegura que tanto "1000.5" como "1000,5" se validen correctamente
        $value_service_normalized = str_replace('.', ',', $value_service);

        // Expresión regular para validar el formato numérico con una sola coma o punto (para decimales)
        // Además, aseguro que no haya más de una coma
        $pattern = '/^\d+(\,\d+)?$/';  // Acepta números enteros o decimales con una coma o punto

        // Verificar si la cadena cumple con el formato y si tiene más de una coma
        if (!preg_match($pattern, $value_service_normalized) || substr_count($value_service_normalized, ',') > 1) {
            // Si no cumple con el formato o tiene más de una coma, agrega el error
            $errors[] = "El valor '{$value_service}' en la fila {$rowNumber} no maneja el formato adecuado. El formato permitido es un número entero o con coma para los decimales (ejemplo: 65000 o 65000,42).";
            throw new \Exception(implode(", ", $errors));
        }
    }


    private function validateNumericFields($filteredRow, $rowNumber, &$errors)
    {
        $numericFields = ['identificacion_paciente', 'consecutivo_poliza_sort'];
        foreach ($numericFields as $field) {
            if (!is_numeric($filteredRow[$field])) {
                $errors[] = "El valor de '{$field}' en la fila {$rowNumber} no es una opción válida.";
            }
        }
        // Al final del método, si hay errores, lanza una excepción
        if (!empty($errors)) {
            throw new \Exception(implode(", ", $errors)); // Lanzar excepción con los errores acumulados
        }
    }

    private function validateNumericFieldsWithoutSeparator($filteredRow, $rowNumber, &$errors)
    {
        $numericFields = ['identificacion_paciente', 'consecutivo_poliza_sort'];

        foreach ($numericFields as $field) {

            if (!ctype_digit((string)$filteredRow[$field])) {
                $errors[] = "El campo '{$field}' en la fila {$rowNumber} contiene un valor inválido. Solo se permiten números sin separadores (ejemplo: 123456).";
            }
        }

        if (!empty($errors)) {
            throw new \Exception(implode(", ", $errors));
        }
    }

    private function validateServiceType($service, $rowNumber, &$errors)
    {
        $typeServices = ['prestación médica', 'medicamentos', 'insumos o mot'];
        if (!in_array(strtolower($service), $typeServices)) {
            $errors[] = "El servicio en la fila {$rowNumber} no es una opción válida.";
            throw new \Exception(implode(", ", $errors));
        }
    }

    private function validatePolicySort($numPolicy, $alert_case, $rowNumber, &$errors)
    {
        if (!PolicySort::where('consecutive', $numPolicy)->exists()) {
            $errors[] = "La póliza registrada en la fila {$rowNumber}: SORT-{$numPolicy}, no existe.";
            throw new \Exception(implode(", ", $errors));
        }

        //capturarmos las siguientes varaibles

        //POLIZA
        $policySort = PolicySort::where('consecutive', $numPolicy)->first();
        $activityPolicySort = Activity::where('id', $policySort->activity_id)->first();

        //Validar si existe la actividad poliza
        if (!$activityPolicySort) {
            $errors[] = "Se presentó un problema al encontrar la actividad de la póliza.";
            throw new \Exception(implode(", ", $errors));
        }

        //GIS
        $gisSort = GisSort::where('consecutive_gis', $alert_case)->first();
        $activityGisSort = Activity::where('id', $gisSort->activity_id)->first();

        //Validar si existe la actividad poliza
        if (!$activityGisSort) {
            $errors[] = "Se presentó un problema al encontrar la actividad de la GIS.";
            throw new \Exception(implode(", ", $errors));
        }

        //Validar si el padre de GIS es la poliza que se proporciona por el excel
        if ($activityGisSort->parent_id !== $activityPolicySort->id) {
            $errors[] = "La póliza SORT '{$numPolicy} no corresponde al número de aviso del siniestro en la fila {$rowNumber}";
            throw new \Exception(implode(", ", $errors));
        }
    }

    private function validateCurrencyType($currency, $rowNumber, &$errors)
    {
        $typeCurrencies = ['colones', 'dólares'];
        if (!in_array(strtolower($currency), $typeCurrencies)) {
            $errors[] = "El tipo de moneda en la fila {$rowNumber} no es una opción válida.";
            throw new \Exception(implode(", ", $errors));
        }
    }

    private function createDataPreInvoice($filteredRow, $serviceDate, $fechaCaso, $idMedicalBillNew, $numPolicy, $typeCurrency)
    {

        //validación del tipo moneda
        // Mapeo de tipos de moneda
        $currencyMapping = [
            'colones' => 1,
            'dólares' => 2
        ];

        // Convertir el tipo de moneda a minúsculas y buscar en el mapeo
        $currencyTypeLower = strtolower($typeCurrency);
        if (!array_key_exists($currencyTypeLower, $currencyMapping)) {
            $errors[] = "Tipo de moneda '{$typeCurrency}' no reconocido.";
            throw new \Exception(implode(", ", $errors)); // Lanzar excepción con los errores acumulados
        }

        DataPreInvoice::create([
            'patient_name' => $filteredRow['nombre_paciente'],
            'worker_id' => (int)$filteredRow['identificacion_paciente'],
            'policy_sort_no' => PolicySort::where('consecutive', $numPolicy)->first()->id ?? null,
            'service_date' => $serviceDate->format('Y-m-d'),
            'case_date' => $fechaCaso->format('Y-m-d'),
            'case_number' => $filteredRow['aviso_caso'],
            'service' => $filteredRow['servicio'],
            'service_value' => (float)$filteredRow['valor_servicio'],
            'result' => '',
            'id_medical_bills' => $idMedicalBillNew,
            'id_policy_sorts' => PolicySort::where('consecutive', $numPolicy)->first()->id ?? null,
            'type_currency' => $currencyMapping[$currencyTypeLower]
        ]);
    }


    public function validatePreInvoice(Request $request, $cpath)
    {
        //Iniciamos la transacción
        DB::beginTransaction();
        try {
            $numPreInvoice = $request->input('numPreInvoice') ?? null;
            // Verificar si se proporcionó un número de prefactura
            if (!$numPreInvoice) {
                return response()->json([
                    'valid' => false,
                    'message' => 'Debe proporcionar un número de pre factura.'
                ]);
            }

            //Consultar el estado de si está en el estado PREFACTURA APROBADA
            $medicalBill = MedicalBill::where('id', $numPreInvoice)->first();
            $activity = Activity::where('id', $medicalBill->activity_id)->first();

            // Si no se encuentra la prefactura, retornamos que no existe
            if (!$medicalBill) {
                return response()->json([
                    'valid' => false,
                    'message' => 'El número de pre factura no existe.'
                ]);
            }

            if ($activity->state_id == StateMedicalBillsServiceSort::PREFACTURA_RECHAZADA) {
                return response()->json([
                    'valid' => false,
                    'message' => 'No puedes usar este número de pre factura porque su estado es rechazado.'
                ]);
            }

            if ($activity->state_id == StateMedicalBillsServiceSort::PREFACTURA_APROBADA) {
                // Validar si está rechada la prefactura
                return response()->json([
                    'valid' => true,
                    'message' => 'Este número de pre factura es válido.'
                ]);
            }

            return response()->json([
                'valid' => false,
                'message' => 'El número de pre factura no es válida.'
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json(['error' => $e->getMessage()], 500);
        }

    }

    public function validateElectronicInvoice(Request $request, $cpath)
    {
        //Iniciamos la transacción
        DB::beginTransaction();
        try {
            $numPreInvoice = $request->input('numPreInvoice') ?? null;
            // Verificar si se proporcionó un número de prefactura
            if (!$numPreInvoice) {
                return response()->json([
                    'valid' => false,
                    'message' => 'Debe proporcionar un número de pre factura.'
                ]);
            }

            //Consultar el estado de si está en el estado PREFACTURA APROBADA
            $medicalBill = MedicalBill::where('id', $numPreInvoice)->first();
            $activity = Activity::where('id', $medicalBill->activity_id)->first();

            // Si no se encuentra la prefactura, retornamos que no existe
            if (!$medicalBill) {
                return response()->json([
                    'valid' => false,
                    'message' => 'El número de pre factura no existe.'
                ]);
            }

            if ($activity->state_id == StateMedicalBillsServiceSort::PREFACTURA_RECHAZADA) {
                return response()->json([
                    'valid' => false,
                    'message' => 'No puedes usar este número de pre factura porque su estado es rechazado.'
                ]);
            }

            if ($activity->state_id == StateMedicalBillsServiceSort::FACTURA_ELECTRONICA_REMITIDA_A_CONTABILIDAD_CON_GLOSA_PARCIAL) {
                // Validar si está rechada la prefactura
                return response()->json([
                    'valid' => true,
                    'message' => 'Este número de pre factura es válido, se encuentra en el estado pre factura aprobada.'
                ]);
            }

            return response()->json([
                'valid' => false,
                'message' => 'El número de pre factura no se encuentra en el estado pre factura aprobada.'
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json(['error' => $e->getMessage()], 500);
        }

    }

    public function readXML($xmlFile, $medicalBill)
    {

        // Cargar el contenido del archivo XML
        $xmlContent = file_get_contents($xmlFile);

        // Verificar si el contenido está vacío
        if (!$xmlContent) {
            throw new \Exception("El archivo XML está vacío o no se pudo leer.");
        }

        try {
            // Intentar cargar el XML en un objeto SimpleXMLElement
            $xml = new \SimpleXMLElement($xmlContent);
        } catch (\Exception $e) {
            throw new \Exception("El archivo no es un XML válido");
        }

        //Validar si existe el nodo NumeroConsecutivo en el XML
        if (!isset($xml->NumeroConsecutivo)) {
            throw new \Exception("El nodo &lt;NumeroConsecutivo&gt; no está presente en el XML.");
        }

        //guardar el nodo NumeroConsecutivo en una variable
        $numberConsecutive = $xml->NumeroConsecutivo ?? null;

        // Verificar que $numberConsecutive exista y no esté vacío
        if (!$numberConsecutive || count($numberConsecutive) === 0) {
            throw new \Exception("El XML no contiene el nodo &lt;NumeroConsecutivo&gt; requerido.");
        }


        if (!isset($xml->FechaEmision)) {
            throw new \Exception("El nodo &lt;FechaEmision&gt; no está presente en el XML.");
        }

        // Extraer la fecha de la factura electrónica
        $date_electronic_invoice = $xml->FechaEmision;

        //Verificar si la fecha es válida
        if (!empty($date_electronic_invoice)) {
            $timestamp = strtotime($date_electronic_invoice);
            if ($timestamp === false) {
                throw new \Exception("La fecha de la factura electrónica tiene un formato incorrecto.");
            }
            $date_electronic_invoice = date('Y-m-d', $timestamp);
        } else {
            throw new \Exception("La fecha de la factura electrónica no se encuentra o está vacía.");
        }

        //Verificar si la fecha es válida
        if (empty($date_electronic_invoice)) {
            throw new \Exception("La fecha de la factura electrónica no se encuentra o está vacía");
        }

        // Validar el nodo &lt;DetalleServicio&gt;
        if (!isset($xml->DetalleServicio)) {
            throw new \Exception("El nodo &lt;DetalleServicio&gt; no está presente en el XML.");
        }

        // Guardar el nodo de las líneas de detalle en una variable
        $lineItems = $xml->DetalleServicio->LineaDetalle ?? null;

        // Verificar que $lineItems exista y no esté vacío
        if (!$lineItems || count($lineItems) === 0) {
            throw new \Exception("El XML no contiene el nodo &lt;LineaDetalle&gt; requerido dentro del nodo &lt;DetalleServicio&gt;.");
        }

        // Extraer datos de la pre factura para validación
        $dataPreInvoice = $medicalBill->data_pre_invoice;
        $cantidadRegistros = count($dataPreInvoice);
        $cantidadLineas = count($lineItems);

        // Verificar que las cantidades de registros coincidan
        if ($cantidadRegistros !== $cantidadLineas) {
            throw new \Exception("La factura no contiene la misma cantidad de registros que la pre factura");
        }

        // Construcción de la tabla B desde el XML
        $dataElectronicInvoice = [];
        $count = 0;
        foreach ($lineItems as $linea) {
            $count++;
            // Verificar que $linea->SubTotal no esté vacío, pero permitir que sea 0
            if (!isset($linea->SubTotal) || trim((string)$linea->SubTotal) === '') {
                throw new \Exception("Se ha identificado que el nodo &lt;LineaDetalle&gt; #{$count} no trae consigo el valor en el nodo &lt;SubTotal&gt;.");
            }

            // Convertir a float después de la verificación
            $unitPrice = (float)$linea->SubTotal;

            // Verificar que el valor convertido sea numérico
            if (!is_numeric($unitPrice)) {
                throw new \Exception("El nodo &lt;LineaDetalle&gt; #{$count} tiene un valor no numérico en el nodo &lt;SubTotal&gt;.");
            }

            $dataElectronicInvoice[] = [
                'service_value' => $unitPrice,
            ];
        }

        // Emparejamiento entre $dataPreInvoice y $dataElectronicInvoice
        $matches = [];
        $unmatched = [];

        // Convertimos $dataPreInvoice a un array
        $dataPreInvoiceArray = $dataPreInvoice->toArray();

        foreach ($dataPreInvoiceArray as $recordA) {
            $foundMatch = false;
            foreach ($dataElectronicInvoice as $keyB => $recordB) {
                if ($recordA['service_value'] == $recordB['service_value']) {
                    $matches[] = array_merge($recordA, $recordB); // Combina los datos
                    unset($dataElectronicInvoice[$keyB]); // Elimina el par para evitar duplicados
                    $foundMatch = true;
                    break;
                }
            }
            if (!$foundMatch) {
                $unmatched[] = $recordA; // Si no hay pareja, lo guardamos
            }
        }

        $typeCurrencyPreFacture = $medicalBill->type_currency_prefacture;

        // Verificar si hay registros sin pareja en la pre factura
        if (!empty($unmatched)) {
            // Crear un mensaje de error con los registros no emparejados
            $unmatchedDetails = implode("<br>", array_map(function ($record) use ($typeCurrencyPreFacture) {
                return "<b>Pre factura:</b> {$record['id_medical_bills']} <b>Número de aviso:</b> {$record['case_number']} <b>Servicio:</b> 
            {$record['service']} <b>Valor:</b> " . (AppServiceProvider::$TYPE_CURRENCY[$typeCurrencyPreFacture] ?? '') .
                    " " . number_format($record['service_value'] ?? 0, 2, '.', ',');
            }, $unmatched));

            throw new \Exception("Se encontraron discrepancias entre la factura electrónica y la pre factura. <br><br>" .
                "<b>Los siguientes valores de la pre factura no coinciden con los valores de la factura electrónica:</b><br>{$unmatchedDetails}" .
                "<br><br>Por favor, revisa los valores de servicio de tu factura electrónica para asegurarte de que todo esté correcto.");
        }

        //Guardar datos para la tabla factura electronica
        foreach ($matches as $match) {
            DataElectronicInvoice::create([
                'patient_name' => $match['patient_name'],
                'worker_id' => $match['worker_id'],
                'policy_sort_no' => $match['policy_sort_no'],
                'service_date' => $match['service_date'],
                'case_date' => $match['case_date'],
                'case_number' => $match['case_number'],
                'service' => $match['service'],
                'service_value' => $match['service_value'],
                'id_medical_bills' => $medicalBill->id,
            ]);
        }


        //Suma de valores de cada servicio
        $total_value_invoice = 0;
        foreach ($matches as $match) {
            $total_value_invoice += $match['service_value'];
        }

        // Calcular la retencion en la fuente
        $total_value_tax = 0;

        foreach ($matches as $match) {
            if (strtolower($match['service']) == 'prestación médica') {
                $total_value_tax += $match['service_value'] * 0.02;
            }
        }

        //asignar valor único a la factura electrónica dentro de REN APP
        $number_electronic_invoice = $numberConsecutive;

        // Guardar los datos en la instancia de MedicalBill
        $medicalBill->number_electronic_invoice = $number_electronic_invoice;
        $medicalBill->date_electronic_invoice = $date_electronic_invoice;
        $medicalBill->account_number = $medicalBill->id;
        $medicalBill->total_value_invoice = $total_value_invoice;
        $medicalBill->total_value_tax = $total_value_tax;
        $medicalBill->type_currency = $medicalBill->type_currency_prefacture;
        $medicalBill->balance = 0;
        $medicalBill->paid_amount = 0;
        $medicalBill->deducted_amount = 0;
        $medicalBill->save();

        //Se retornan el objeto MedicalBill actualizado
        return $medicalBill;
    }

    public function readXMLMultiple($xmlFiles, $medicalBill)
    {
        // Obtener los datos de pre factura y contar los registros
        $dataPreInvoice = $medicalBill->data_pre_invoice;
        $countPreInvoice = count($dataPreInvoice);

        // Contar la cantidad de archivos XML subidos
        $countXML = count($xmlFiles);

        // Aquí se guardan los datos de la facturación
        $dataElectronicInvoice = [];

        // Verificar que las cantidades de registros coincidan
        if ($countPreInvoice !== $countXML) {
            throw new \Exception("El número de items de la pre factura no coincide con el número de archivos XML.");
        }

        //Recorrer los archivos XML
        foreach ($xmlFiles as $xmlFile) {
            // Cargar el contenido del archivo XML
            $xmlContent = file_get_contents($xmlFile);

            // Verificar si el contenido está vacío
            if (!$xmlContent) {
                throw new \Exception("El archivo XML está vacío o no se pudo leer.");
            }

            try {
                // Intentar cargar el XML en un objeto SimpleXMLElement
                $xml = new \SimpleXMLElement($xmlContent);
            } catch (\Exception $e) {
                throw new \Exception("El archivo no es un XML válido");
            }

            //Validar si existe el nodo NumeroConsecutivo en el XML
            if (!isset($xml->NumeroConsecutivo)) {
                throw new \Exception("El nodo &lt;NumeroConsecutivo&gt; no está presente en el XML.");
            }

            //Validar si existe el nodo ResumenFactura en el XML
            if (!isset($xml->ResumenFactura)) {
                throw new \Exception("El nodo &lt;ResumenFactura&gt; no está presente en el XML.");
            }

            //guardar el nodo NumeroConsecutivo en una variable
            $numberConsecutive = $xml->NumeroConsecutivo ?? null;

            // Verificar que $numberConsecutive exista y no esté vacío
            if (!$numberConsecutive || count($numberConsecutive) === 0) {
                throw new \Exception("El XML no contiene el nodo &lt;NumeroConsecutivo&gt; requerido.");
            }

            // Guardar el nodo TotalVentaNeta en una variable
            $totalVentaNeta = $xml->ResumenFactura->TotalVentaNeta ?? null;

            // Verificar que $lineItems exista y no esté vacío
            if (!isset($totalVentaNeta)) {
                throw new \Exception("El XML no contiene el nodo &lt;TotalVentaNeta&gt; requerido.");
            }

            // Convertir a float después de la verificación
            $unitPrice = (float)$totalVentaNeta;

            // Verificar que el valor convertido sea numérico
            if (!is_numeric($unitPrice)) {
                throw new \Exception("El nodo &lt;TotalVentaNeta&gt; tiene un valor no numérico.");
            }

            $dataElectronicInvoice[] = [
                'service_value' => $unitPrice,
                'number_consecutive' => $numberConsecutive
            ];
        }

        // Verificar que los valores de cada XML no este vacío
        if (empty($dataElectronicInvoice)) {
            throw new \Exception("Se ha presentado un error al guardar los valores de cada facturación.");
        }

        // Emparejamiento entre $dataPreInvoice y $dataElectronicInvoice
        $matches = [];
        $unmatched = [];

        // Convertimos $dataPreInvoice a un array
        $dataPreInvoiceArray = $dataPreInvoice->toArray();

        foreach ($dataPreInvoiceArray as $recordA) {
            $foundMatch = false;
            foreach ($dataElectronicInvoice as $keyB => $recordB) {
                if ($recordA['service_value'] == $recordB['service_value']) {
                    $matches[] = array_merge($recordA, $recordB); // Combina los datos
                    unset($dataElectronicInvoice[$keyB]); // Elimina el par para evitar duplicados
                    $foundMatch = true;
                    break;
                }
            }
            if (!$foundMatch) {
                $unmatched[] = $recordA; // Si no hay pareja, lo guardamos
            }
        }

        //asignamos el valor de la moneda que se encuentra en la pre factura
        $typeCurrencyPreFacture = $medicalBill->type_currency_prefacture;

        // Verificar si hay registros sin pareja en la pre factura
        if (!empty($unmatched)) {
            // Crear un mensaje de error con los registros no emparejados
            $unmatchedDetails = implode("<br>", array_map(function ($record) use ($typeCurrencyPreFacture) {
                return "<b>Pre factura:</b> {$record['id_medical_bills']} <b>Número de aviso:</b> {$record['case_number']} <b>Servicio:</b> " .
                    "{$record['service']} <b>Valor:</b> " . (AppServiceProvider::$TYPE_CURRENCY[$typeCurrencyPreFacture] ?? '') .
                    " " . number_format($record['service_value'] ?? 0, 2, '.', ',');
            }, $unmatched));


            // Adaptar el mensaje según la cantidad de pre facturas no coincidentes
            $messagePrefix = count($unmatched) > 1 ? "Los valores de las siguientes pre facturas" : "Los valores de la siguiente pre factura";

            throw new \Exception("Se encontraron discrepancias entre las facturas electrónicas y las pre facturas. <br><br>" .
                "<b>{$messagePrefix} no coinciden con algunos de los valores de las facturas electrónicas radicadas:</b><br>{$unmatchedDetails}" .
                "<br><br>Por favor, revisa los valores de servicio de cada factura electrónica que hayas subido, para asegurarte de que todo esté correcto.");
        }

        //Guardar datos para la tabla factura electronica
        foreach ($matches as $match) {
            DataElectronicInvoice::create([
                'patient_name' => $match['patient_name'],
                'worker_id' => $match['worker_id'],
                'policy_sort_no' => $match['policy_sort_no'],
                'service_date' => $match['service_date'],
                'case_date' => $match['case_date'],
                'case_number' => $match['case_number'],
                'service' => $match['service'],
                'service_value' => $match['service_value'],
                'id_medical_bills' => $medicalBill->id,
                'num_electronic_invoice_for_auditor' => $match['number_consecutive']
            ]);
        }

        //Suma de valores de cada servicio
        $total_value_invoice = 0;
        foreach ($matches as $match) {
            $total_value_invoice += $match['service_value'];
        }

        // Calcular la retencion en la fuente
        $total_value_tax = 0;

        foreach ($matches as $match) {
            if (strtolower($match['service']) == 'prestación médica' && $match['result'] === "aprobado") {
                $total_value_tax += $match['service_value'] * 0.02;
            }
        }

        // Guardar los datos en la instancia de MedicalBill
        $medicalBill->total_value_invoice = $total_value_invoice;
        $medicalBill->total_value_tax = $total_value_tax;
        $medicalBill->type_currency = $medicalBill->type_currency_prefacture;
        $medicalBill->balance = 0;
        $medicalBill->paid_amount = 0;
        $medicalBill->deducted_amount = 0;
        $medicalBill->save();

        //Se retornan el objeto MedicalBill actualizado
        return $medicalBill;
    }

    public function validateBeforeProcessing($file)
    {
        // Cargar el archivo Excel
        $objPHPExcel = PHPExcel_IOFactory::load($file->getPathname());
        $worksheet = $objPHPExcel->getActiveSheet();

        // Leer los datos fila por fila sin conversión automática
        $rows = [];

        foreach ($worksheet->getRowIterator() as $row) {
            $cellIterator = $row->getCellIterator();
            $cellIterator->setIterateOnlyExistingCells(false);
            $rowData = [];

            foreach ($cellIterator as $cell) {
                // FORZAR QUE LA CELDA SE LEA COMO TEXTO
                $cell->setDataType(\PHPExcel_Cell_DataType::TYPE_STRING);

                // Obtener el valor exacto de la celda
                $rowData[] = (string) $cell->getValue();
            }

            // Limitar a las primeras 9 columnas
            $rowData = array_slice($rowData, 0, 9);

            $rows[] = $rowData;
        }

        // Validar que el archivo tiene datos
        if (empty($rows) || count($rows) < 2) {
            throw new \Exception("El archivo Excel está vacío o tiene un formato incorrecto.");
        }

        // Obtener los encabezados de la primera fila y limpiarlos
        $headers = array_map('strtolower', array_map('trim', $rows[0]));

        // Verificar si existe la columna "valor_servicio"
        $valorServicioIndex = array_search('valor_servicio', $headers);

        if ($valorServicioIndex === false) {
            throw new \Exception("El archivo no tiene una columna llamada 'valor_servicio'.");
        }

        $errors = [];

        // Iterar sobre las filas del Excel
        foreach ($rows as $key => $fila) {
            if ($key === 0) continue; // Saltar la fila de encabezados

            $filaNum = $key + 1; // Ajustar número de fila real

            // Omitir filas completamente vacías
            if (empty(array_filter($fila, function ($value) { return trim($value) !== ''; }))) {
                continue;
            }

            // Verificar que "valor_servicio" existe en la fila
            if (!isset($fila[$valorServicioIndex]) || trim($fila[$valorServicioIndex]) === '') {
                $errors[] = "Error en la fila <strong>$filaNum</strong>: Falta el campo <strong>'valor_servicio'</strong>.";
                continue;
            }

            // Extraer el valor original tal como está en el Excel
            $valorOriginal = trim($fila[$valorServicioIndex]);

            // Si el valor es numérico y tiene punto decimal, convertirlo a coma
            if (is_numeric($valorOriginal) && strpos($valorOriginal, '.') !== false) {
                $valorOriginal = str_replace('.', ',', $valorOriginal);
            }

            // Validar el formato correcto (Ejemplo: 17800,20 o 250800)
            if (!preg_match('/^\d+(\.\d{3})*(,\d+)?$|^\d+$/', $valorOriginal)) {
                $errors[] = "Error en la fila <strong>$filaNum</strong>: El formato de '<strong>valor_servicio</strong>' es incorrecto (<strong>$valorOriginal</strong>).";
                continue;
            }

            // Convertir a un formato numérico válido en PHP
            $valor = str_replace(['.', ','], ['', '.'], $valorOriginal);

            if (!is_numeric($valor)) {
                $errors[] = "Error en la fila <strong>$filaNum</strong>: El valor <strong>'$valorOriginal'</strong> no es un número válido.";
            }
        }

        // Lanzar excepciones si hay errores
        if (!empty($errors)) {
            throw new \Exception(implode("\n", $errors));
        }
    }





}
