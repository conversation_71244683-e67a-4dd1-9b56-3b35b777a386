<?php

namespace App\Http\Controllers\Services;

use App\Activity;
use App\Client;
use App\Dictum;
use App\Http\Controllers\Controller;
use App\Pcl;
use App\PclAdminMark;
use App\PclCalificationDiagnostic;
use App\PclContactability;
use App\PclDeficience;
use App\PclDiagnostic;
use App\PclDocumentaryValidation;
use App\PclFollow;
use App\PclInterconsultation;
use App\PclJnciDiagnosis;
use App\PclJrciDiagnosis;
use App\PclNc;
use App\PclNotify;
use App\PclPclDiagnosis;
use App\PclPclType;
use App\PclPreliminaryOpinion;
use App\PclQualityControls;
use App\PclSends;
use App\PclTableData;
use App\PclTableKid1507Data;
use App\PclTableKidData;
use App\PclTypeNc;
use App\PclValorationDiagnostic;
use App\PclWorkHistory;
use DateTime;
use DB;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\View;
use PDF;
use App\Providers\AppServiceProvider;
use App\EPS;
use App\ARL;
use App\AFP;
use Illuminate\Http\File;
use Illuminate\Support\Facades\Storage;

class PCLController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function dictum(Request $req, $cpath, $id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();
        $existsBeginServiceNotify = false;
        $existsDictumServiceNotify = false;

        $beginServiceNotify = Activity::query()->where('parent_id',$activity->id)
            ->where('service_id',68)
            ->where('affiliate_id',$activity->affiliate_id)
            ->first();

        if ($beginServiceNotify) {
            $existsBeginServiceNotify = true;
        }

        $dictumServiceNotify = Activity::query()->where('parent_id',$activity->id)
            ->where('service_id',69)
            ->where('affiliate_id',$activity->affiliate_id)
            ->first();

        if ($dictumServiceNotify) {
            $existsDictumServiceNotify = true;
        }

        // Comprobemos si existe un Pcl relacionado con la $activity
        $pcl = Pcl::query()
            ->where('activity_id', $activity->id)
            ->first();

        // Si no existe un Pcl, creamos uno
        if (!$pcl) {
            $pcl = new Pcl;
            $pcl->activity_id = $activity->id;
            $pcl->save();
        }

        return view('services.pcl.dictum2', [
            'activity' => $activity,
            'client' => $client ,
            'existsBeginServiceNotify' => $existsBeginServiceNotify ,
            'existsDictumServiceNotify' => $existsDictumServiceNotify,
            'beginServiceNotify' => $beginServiceNotify,
            'dictumServiceNotify' => $dictumServiceNotify
        ]);
    }

    public function save(Request $req, $cpath, $id)
    {

        $client = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();

        DB::beginTransaction();
        try {
            if (!$activity->pcl) {
                $pcl = new Pcl;
                $pcl->activity_id = $id;
            } else {
                $pcl = $activity->pcl;
            }

            $pcl->pcl_type = $req->input('pcl_type');
            $pcl_type_aux = $pcl->pcl_type;

//            INFORMACION GENERAL
//             Datos del Afiliado
            $pcl->doc_type_affiliate = $req->input('doc_type_affiliate');
            $pcl->doc_number_affiliate = $req->input('doc_number_affiliate');
            $pcl->full_name_affiliate = $req->input('full_name_affiliate');
            $pcl->birthday = $req->input('birthday_submit');
            $pcl->age = $req->input('age');
            $pcl->gender = $req->input('gender');
            $pcl->regional = $req->input('regional');
            $pcl->civil_status = $req->input('civil_status');
            $pcl->school_level = $req->input('school_level');
            $pcl->email = $req->input('email');
            $pcl->eps = $req->input('eps');
            $pcl->department = $req->input('department');
            $pcl->municipality = $req->input('municipality');
            $pcl->address = $req->input('address');
            $pcl->phone = $req->input('phone');
            $pcl->cellphone = $req->input('cellphone');
//            Datos de beneficiario;
            $pcl->beneficiaries_qualification = $req->input('beneficiaries_qualification');
            $pcl->beneficiaries_doc_type = $req->input('beneficiaries_doc_type');
            $pcl->beneficiaries_doc_number = $req->input('beneficiaries_doc_number');
            $pcl->beneficiaries_full_name = $req->input('beneficiaries_full_name');
            $pcl->beneficiaries_birthdate = $req->input('beneficiaries_birthdate_submit');
            $pcl->beneficiaries_age = $req->input('beneficiaries_age');
            $pcl->beneficiaries_gender = $req->input('beneficiaries_gender');
            $pcl->beneficiaries_school_level = $req->input('beneficiaries_school_level');
            $pcl->beneficiaries_civil_status = $req->input('beneficiaries_civil_status');
//            Datos contacto Beneficiario
            $pcl->beneficiaries_address = $req->input('beneficiaries_address');
            $pcl->beneficiaries_phone = $req->input('beneficiaries_phone');
            $pcl->beneficiaries_cellphone = $req->input('beneficiaries_cellphone');
            $pcl->beneficiaries_email = $req->input('beneficiaries_email');
            $pcl->beneficiaries_department = $req->input('beneficiaries_department');
            $pcl->beneficiaries_municipality = $req->input('beneficiaries_municipality');
            $pcl->beneficiaries_address = $req->input('beneficiaries_address');
//            Gestión del radicado bizagi
            $pcl->id_case = $req->input('id_case');
            $pcl->radication_date = $req->input('radication_date_submit');
            $pcl->radication_number = $req->input('radication_number');
            $pcl->assignation_provider_date = $req->input('assignation_provider_date_submit');
            $pcl->assigned_provider_documental_validation = $req->input('assigned_provider_documental_validation');
            $pcl->radication_department = $req->input('radication_department');
            $pcl->radication_municipality = $req->input('radication_municipality');
//            Datos tutela o requerimiento jurídico
            $pcl->tutelage = $req->input('tutelage');
            $pcl->tutelage_radication_number = $req->input('tutelage_radication_number');
            $pcl->tutelage_radication_date = $req->input('tutelage_radication_date_submit');
            $pcl->tutelage_instance = $req->input('tutelage_instance');
//           VALIDACIÓN DOCUMENTAL
            $pcl->rejection_case = $req->input('rejection_case');
            $pcl->rejection_causal = $req->input('rejection_causal');
            $pcl->tutelage_observation = $req->input('tutelage_observation');
            $pcl->extern_communication_radication = $req->input('extern_communication_radication');
            $pcl->standardization_ri = $req->input('standardization_ri');
            $pcl->tutelage_doc_query = $req->input('tutelage_doc_query');
            $pcl->tutelage_international_convenio = $req->input('tutelage_international_convenio');
//            Solicitud de documentos
            $pcl->medical_evaluations_doc = $req->input('medical_evaluations_doc');
            $pcl->medical_evaluations_doc_observation = $req->input('medical_evaluations_doc_observation');
            $pcl->proof_objetive_query_doc = $req->input('proof_objetive_query_doc');
            $pcl->proof_objetive_query_doc_observation = $req->input('proof_objetive_query_doc_observation');
            $pcl->laboratory_proof_query_doc = $req->input('laboratory_proof_query_doc');
            $pcl->laboratory_proof_query_doc_observation = $req->input('laboratory_proof_query_doc_observation');
            $pcl->communication_send_date = $req->input('communication_send_date_submit');
            $pcl->delivery_communication_state = $req->input('delivery_communication_state');
            $pcl->effective_sol_document_date = $req->input('effective_sol_document_date_submit');
            $pcl->compliance_sol_doc_date = $req->input('compliance_sol_doc_date_submit');
            $pcl->extension_query_radication = $req->input('extension_query_radication');
//            Solicitud de prorroga
            $pcl->extension_query_radication_date = $req->input('extension_query_radication_date_submit');
            $pcl->extension_bizagi_rad_number = $req->input('extension_bizagi_rad_number');
            $pcl->compliance_extension_date = $req->input('compliance_extension_date_submit');
//            Recepción documentos
            $pcl->doc_reception_date = $req->input('doc_reception_date_submit');
            $pcl->doc_bizagi_rad_number = $req->input('doc_bizagi_rad_number');
            $pcl->provide_requested_documentation = $req->input('provide_requested_documentation');
//            AGENDAMIENTO CITA
//            Valoracion presencial / Telemedicina
//            Valoracion medica
            $pcl->valoration_valoration_type = $req->input('valoration_valoration_type');
            $pcl->valoration_v_date = $req->input('valoration_v_date');
            $pcl->valoration_v_time = $req->input('valoration_v_time');
            $pcl->valoration_consultation_channel = $req->input('valoration_consultation_channel');
//            Respondiente
            $pcl->valoration_companion_name = $req->input('valoration_companion_name');
            $pcl->valoration_companion_phone = $req->input('valoration_companion_phone');
            $pcl->valoration_companion_kinship = $req->input('valoration_companion_kinship');
//            Rol laboral / Ocupacional
            $pcl->valoration_laborally_active = $req->input('valoration_laborally_active');
            $pcl->valoration_use_laboral_role = $req->input('valoration_use_laboral_role');
            $pcl->valoration_use_disabilities_handicap = $req->input('valoration_use_disabilities_handicap');

//            Disabilities and Handicap
            $pcl->disabilities_conduct = $req->input('disabilities_conduct');
            $pcl->disabilities_communication = $req->input('disabilities_communication');
            $pcl->disabilities_personal_care = $req->input('disabilities_personal_care');
            $pcl->disabilities_locomotion = $req->input('disabilities_locomotion');
            $pcl->disabilities_body_disposition = $req->input('disabilities_body_disposition');
            $pcl->disabilities_skill = $req->input('disabilities_skill');
            $pcl->disabilities_situation = $req->input('disabilities_situation');

            $pcl->handicap_orientation = $req->input('handicap_orientation');
            $pcl->handicap_physical_independence = $req->input('handicap_physical_independence');
            $pcl->handicap_displacement = $req->input('handicap_displacement');
            $pcl->handicap_occupational = $req->input('handicap_occupational');
            $pcl->handicap_social_integration = $req->input('handicap_social_integration');

//            Rol laboral / Ocupacional 1507
            $pcl->laborally_active = $req->input('laborally_active');
            $pcl->role_restriction = $req->input('role_restriction');
            $pcl->economic_self = $req->input('economic_self');
            $pcl->use_laboral_role = $req->input('use_laboral_role');
            $pcl->use_laboral_role_description = $req->input('use_laboral_role_description');
            $pcl->role_category = $req->input('role_category');
//            Rol laboral
            $pcl->lab_rol_laborally_active = $req->input('lab_rol_laborally_active');
            $pcl->lab_rol_habitual_work = $req->input('lab_rol_habitual_work');
            $pcl->lab_rol_charge = $req->input('lab_rol_charge');
            $pcl->lab_rol_office_activity = $req->input('lab_rol_office_activity');
            $pcl->lab_rol_charge_time = $req->input('lab_rol_charge_time');
            $pcl->lab_rol_employer = $req->input('lab_rol_employer');
            $pcl->lab_rol_restrictions = $req->input('lab_rol_restrictions');
            $pcl->lab_rol_operations = $req->input('lab_rol_operations');
            $pcl->help_device_at_the_moment = $req->input('help_device_at_the_moment');
            $pcl->help_device_at_the_moment_description = $req->input('help_device_at_the_moment_description');
            $pcl->lab_rol_restrictions_eps_arl = $req->input('lab_rol_restrictions_eps_arl');
            $pcl->third_party_help = $req->input('third_party_help');
            $pcl->previous_dictum_origin_pcl = $req->input('previous_dictum_origin_pcl');
            $pcl->labor_role_description = $req->input('labor_role_description');

            if ($pcl_type_aux == 1) {
                $pcl->laboral_role_sustentation = $req->input('laboral_role_sustentation');
                $pcl->laboral_role_sustentation_and_other_occupational_activities = $req->input('laboral_role_sustentation_and_other_occupational_activities');
            }

//            Otras areas ocupacionales
            $pcl->valoration_learning_application_knowledge = $req->input('valoration_learning_application_knowledge');
            $pcl->valoration_communication = $req->input('valoration_communication');
            $pcl->valoration_mobility = $req->input('valoration_mobility');
            $pcl->valoration_personal_care = $req->input('valoration_personal_care');
            $pcl->valoration_domestic_life = $req->input('valoration_domestic_life');
            //Rol ocupacional
            $pcl->ocucupational_role_adults_description = $req->input('ocucupational_role_adults_description');
            $pcl->anamnesis_actual_disease = $req->input('anamnesis_actual_disease');
            $pcl->anamnesis_illness_history = $req->input('anamnesis_illness_history');
//                  Anamnesis - enfermedad actual
            $pcl->anamnesis_antecedents = $req->input('anamnesis_antecedents');
            $pcl->anamnesis_patological = $req->input('anamnesis_patological');
            $pcl->anamnesis_surgical = $req->input('anamnesis_surgical');
            $pcl->anamnesis_pharmaceutical = $req->input('anamnesis_pharmaceutical');
            $pcl->anamnesis_hospitalary = $req->input('anamnesis_hospitalary');
            $pcl->anamnesis_toxic = $req->input('anamnesis_toxic');
            $pcl->anamnesis_transfusion = $req->input('anamnesis_transfusion');
            $pcl->anamnesis_family = $req->input('anamnesis_family');
            // Antecedente laboral
            $pcl->laboral_antecedent = $req->input('laboral_antecedent');
//            Antecedentes
            $pcl->pathological = $req->input('pathological');
            $pcl->valoration_compasurgerynion_phone = $req->input('valoration_compasurgerynion_phone');
            $pcl->farmacological = $req->input('farmacological');
            $pcl->hospitalary = $req->input('hospitalary');
            $pcl->toxical = $req->input('toxical');
            $pcl->transfusion = $req->input('transfusion');
            $pcl->habitual_laboral_work = $req->input('habitual_laboral_work');
//            Examen Fisico
            $pcl->general_state = $req->input('general_state');
            $pcl->physic_exam_laterality = $req->input('physic_exam_laterality');
            $pcl->height = $req->input('height');
            $pcl->weight = $req->input('weight');
            $pcl->imc_data = $req->input('imc_data');
            $pcl->ta = $req->input('ta');
            $pcl->ta2 = $req->input('ta2');
            $pcl->fc = $req->input('fc');
            $pcl->fr = $req->input('fr');
            $pcl->temperature = $req->input('temperature');
            $pcl->head_and_neck = $req->input('head_and_neck');
            $pcl->pulmonaryal_cardio = $req->input('pulmonaryal_cardio');
            $pcl->abdomen = $req->input('abdomen');
            $pcl->phisical_exam_extremities = $req->input('phisical_exam_extremities');
            $pcl->phisical_exam_neurological = $req->input('phisical_exam_neurological');
            $pcl->phisical_exam_mental = $req->input('phisical_exam_mental');
            $pcl->phisical_exam_observation = $req->input('phisical_exam_observation');
            $pcl->req_hc_add_or_complement_exams = $req->input('req_hc_add_or_complement_exams');
//            Medico evaluador
            $pcl->doc_doc_type = $req->input('doc_doc_type');
            $pcl->doc_doc_number = $req->input('doc_doc_number');
            $pcl->doc_rethus = $req->input('doc_rethus');
            $pcl->doc_full_name = $req->input('doc_full_name');
            $pcl->doc_medical_record = $req->input('doc_medical_record');
            $pcl->doc_specialited = $req->input('doc_specialited');
            $pcl->doc_license = $req->input('doc_license');
            $pcl->doc_rethus = $req->input('doc_rethus');
//            DICTAMEN PRELIMINAR
//                        Solicitud de examenes
            $pcl->medical_concepts_clinical_valorations = $req->input('medical_concepts_clinical_valorations');
            $pcl->objective_necesary_proves_bremo = $req->input('objective_necesary_proves_bremo');
            $pcl->objective_necesary_laboratory_proves_bremo = $req->input('objective_necesary_laboratory_proves_bremo');
            $pcl->send_communication_date = $req->input('send_communication_date_submit');
            $pcl->communication_state_deliver = $req->input('communication_state_deliver');
            $pcl->sol_doc_deliver_date = $req->input('sol_doc_deliver_date_submit');
            $pcl->sol_doc_term_accomplished_date = $req->input('sol_doc_term_accomplished_date_submit');
            $pcl->prorrogation_request_radication = $req->input('prorrogation_request_radication');
//            Solicitud de prorroga
            $pcl->prorrogation_rad_date = $req->input('prorrogation_rad_date_submit');
            $pcl->bizagi_rad_number_prorrogation = $req->input('bizagi_rad_number_prorrogation');
            $pcl->accomplished_date_prorrogation_term = $req->input('accomplished_date_prorrogation_term_submit');
//            Recepción documentos
            $pcl->documents_reception_date = $req->input('documents_reception_date_submit');
            $pcl->bizagi_rad_number_doc_reception = $req->input('bizagi_rad_number_doc_reception');
            $pcl->required_exams_apportation = $req->input('required_exams_apportation');
//            CALIFICACIÓN DE PCL
            //Información básica
            if ($pcl_type_aux == 1) {
                $pcl->attorney_name = $req->input('attorney_name');
                $pcl->attorney_cc = $req->input('attorney_cc');
            }
            $pcl->request_reason = $req->input('request_reason');
            $pcl->applicant = $req->input('applicant');
            $pcl->sender = $req->input('sender');
            $pcl->nit_sender = $req->input('nit_sender');
            $pcl->address_sender = $req->input('address_sender');
            $pcl->city_sender = $req->input('city_sender');
            $pcl->description_valoration_current_illness = $req->input('description_valoration_current_illness');
            $pcl->dominance = $req->input('dominance');
            $pcl->previous_pcl = $req->input('previous_pcl');
            $pcl->require_3rd = $req->input('require_3rd');
            $pcl->require_3rd_dk = $req->input('require_3rd_dk');
            $pcl->require_device = $req->input('require_device');
            //    Antecedentes personales
            $pcl->patological_dictum = $req->input('patological_dictum');
            $pcl->surgical_dictum = $req->input('surgical_dictum');
            $pcl->pharmaceutical_dictum = $req->input('pharmaceutical_dictum');
            $pcl->hospitalary_dictum = $req->input('hospitalary_dictum');
            $pcl->toxic_dictum = $req->input('toxic_dictum');
            $pcl->transfusion_dictum = $req->input('transfusion_dictum');
            $pcl->family_dictum = $req->input('family_dictum');
            //Documentos tenidos en cuenta
            $pcl->doc_clinical_history = $req->input('doc_clinical_history');
            $pcl->doc_epicrisis = $req->input('doc_epicrisis');
            $pcl->doc_paraclinic = $req->input('doc_paraclinic');
            $pcl->doc_prehiring = $req->input('doc_prehiring');
            $pcl->doc_periodics = $req->input('doc_periodics');
            $pcl->doc_post_occupational = $req->input('doc_post_occupational');
            $pcl->doc_health_concept = $req->input('doc_health_concept');
            $pcl->doc_others = $req->input('doc_others');
            $pcl->doc_coprse_removal = $req->input('doc_coprse_removal');
            $pcl->doc_death_certificate = $req->input('doc_death_certificate');
            $pcl->doc_accident_report = $req->input('doc_accident_report');
            //Porcentaje de pérdida de capacidad laboral
            $pcl->deficiences_total_report = $req->input('deficiences_total_report');
            $pcl->laboral_role_total_report = $req->input('laboral_role_total_report');
            $pcl->pcl_percentage_report = $req->input('pcl_percentage_report');
            $pcl->structuring_at = $req->input('structuring_at_submit');
            $pcl->origin = $req->input('origin');
            $pcl->event_type = $req->input('event_type');
            $pcl->event_date = $req->input('event_date_submit');
            $pcl->pcl_pclo = $req->input('pcl_pclo');
            $pcl->catastrophic = $req->input('catastrophic');
            $pcl->congenital_progresion_cronical_disease = $req->input('congenital_progresion_cronical_disease');
            $pcl->congenital_near_to_birth = $req->input('congenital_near_to_birth');
            $pcl->structuring_support = $req->input('structuring_support');
            if ($pcl_type_aux == 3) {
                $pcl->handicaps_total_report = $req->input('handicaps_total_report');
                $pcl->pension_review = $req->input('pension_review');
                $pcl->ponencia = $req->input('ponencia');
                $pcl->details = $req->input('details');
                $pcl->disability_details = $req->input('disability_details');
                $pcl->handicaps_details = $req->input('handicaps_details');

            }
//            CONTROL CALIDAD
//            Datos control de calidad
            $pcl->quality_control_date = $req->input('quality_control_date_submit');
            $pcl->quality_control_reviewer = $req->input('quality_control_reviewer');
//            Solicitud de examenes                 
            $pcl->quality_medical_concepts_clinical_valorations = $req->input('quality_medical_concepts_clinical_valorations');
            $pcl->quality_objective_necesary_proves_bremo = $req->input('quality_objective_necesary_proves_bremo');
            $pcl->quality_objective_necesary_laboratory_proves_bremo = $req->input('quality_objective_necesary_laboratory_proves_bremo');
            $pcl->quality_send_communication_date = $req->input('quality_send_communication_date');
            $pcl->quality_communication_state_deliver = $req->input('quality_communication_state_deliver');
            $pcl->quality_deliver_date = $req->input('quality_deliver_date_submit');
            $pcl->quality_accomplished_date = $req->input('quality_accomplished_date_submit');
            $pcl->quality_prorrogation_request_radication = $req->input('quality_prorrogation_request_radication');
//            Solicitud de prorroga
            $pcl->quality_rad_date = $req->input('quality_rad_date_submit');
            $pcl->quality_bizagi_rad_number = $req->input('quality_bizagi_rad_number');
            $pcl->quality_accomplished_prororogation_date = $req->input('quality_accomplished_prororogation_date_submit');
//            Recepción documentos
            $pcl->quality_documents_reception_date = $req->input('quality_documents_reception_date_submit');
            $pcl->quality_bizagi_rad_number_doc_reception = $req->input('quality_bizagi_rad_number_doc_reception');
            $pcl->quality_required_exams_apportation = $req->input('quality_required_exams_apportation');
//            Resultado auditoria
            $pcl->quality_audit_result = $req->input('quality_audit_result');
            $pcl->quality_audit_result_observation = $req->input('quality_audit_result_observation');
//            AUDITORIA COLPENSIONES
//            Datos control de calidad
            $pcl->audit_quality_control_date = $req->input('audit_quality_control_date_submit');
            $pcl->audit_quality_control_reviewer = $req->input('audit_quality_control_reviewer');
//            Resultado auditoria
            $pcl->audit_quality_audit_result = $req->input('audit_quality_audit_result');
            $pcl->audit_quality_audit_result_observation = $req->input('audit_quality_audit_result_observation');


            //  kid data table adds
            $pcl->total0 = $req->input('total0');
            $pcl->total1 = $req->input('total1');
            $pcl->total2 = $req->input('total2');
            $pcl->totalAll = $req->input('totalAll');

            //Notificaciones Comunicacion inicio trámite perdidad capacidad laboral
            $pcl->n_csp_eps_entity = $req->input('n_csp_eps_entity');
            $pcl->n_csp_arl_entity = $req->input('n_csp_arl_entity');
            $pcl->n_csp_sd_notification_request = $req->input('n_csp_sd_notification_request');
            $pcl->n_csp_application_date = $req->input('n_csp_application_date_submit');
            $pcl->n_csp_rad_supports_notification = $req->input('n_csp_rad_supports_notification');
            $pcl->n_csp_bzg_notification_support_creation_date = $req->input('n_csp_bzg_notification_support_creation_date_submit');
            $pcl->n_csp_average_costs = $req->input('n_csp_average_costs');

            //Notificaciones de dictamen
            $pcl->n_attorney_interested_part = $req->input('n_attorney_interested_part');
            $pcl->n_attorney_name_third_party_representative = $req->input('n_attorney_name_third_party_representative');
            $pcl->n_attorney_address = $req->input('n_attorney_address');
            $pcl->n_attorney_phone = $req->input('n_attorney_phone');
            $pcl->n_attorney_city = $req->input('n_attorney_city');
            $pcl->n_attorney_department = $req->input('n_attorney_department');
            $pcl->n_attorney_allow_sending_email = $req->input('n_attorney_allow_sending_email');
            $pcl->n_attorney_email = $req->input('n_attorney_email');
            $pcl->n_attorney_automatic_paragraph = $req->input('n_attorney_automatic_paragraph');
            $pcl->n_attorney_rad_father = $req->input('n_attorney_rad_father');
            $pcl->n_attorney_ri_father_creation_date = $req->input('n_attorney_ri_father_creation_date_submit');
            $pcl->n_attorney_rad_ri_answer = $req->input('n_attorney_rad_ri_answer');
            $pcl->n_attorney_das_shipping_lot = $req->input('n_attorney_das_shipping_lot');
            $pcl->n_attorney_das_shipping_date = $req->input('n_attorney_das_shipping_date_submit');
            $pcl->n_attorney_average_costs = $req->input('n_attorney_average_costs');
            $pcl->n_eps_entity = $req->input('n_eps_entity');
            $pcl->n_eps_sd_notification_request = $req->input('n_eps_sd_notification_request');
            $pcl->n_eps_application_date = $req->input('n_eps_application_date_submit');
            $pcl->n_eps_rad_supports_notification = $req->input('n_eps_rad_supports_notification');
            $pcl->n_eps_bzg_notification_support_creation_date = $req->input('n_eps_bzg_notification_support_creation_date_submit');
            $pcl->n_eps_average_costs = $req->input('n_eps_average_costs');
            $pcl->n_arl_type_affiliate = $req->input('n_arl_type_affiliate');
            $pcl->n_arl_entity = $req->input('n_arl_entity');
            $pcl->n_arl_sd_notification_request = $req->input('n_arl_sd_notification_request');
            $pcl->n_arl_application_date = $req->input('n_arl_application_date_submit');
            $pcl->n_arl_rad_supports_notification = $req->input('n_arl_rad_supports_notification');
            $pcl->n_arl_bzg_notification_support_creation_date = $req->input('n_arl_bzg_notification_support_creation_date_submit');
            $pcl->n_arl_average_costs = $req->input('n_arl_average_costs');
            $pcl->n_emp_type_affiliate = $req->input('n_emp_type_affiliate');
            $pcl->n_emp_name_third_party_representative = $req->input('n_emp_name_third_party_representative');
            $pcl->n_emp_address = $req->input('n_emp_address');
            $pcl->n_emp_phone = $req->input('n_emp_phone');
            $pcl->n_emp_city = $req->input('n_emp_city');
            $pcl->n_emp_department = $req->input('n_emp_department');
            $pcl->n_emp_allow_sending_email = $req->input('n_emp_allow_sending_email');
            $pcl->n_emp_email = $req->input('n_emp_email');
            $pcl->n_emp_sd_notification_request = $req->input('n_emp_sd_notification_request');
            $pcl->n_emp_application_date = $req->input('n_emp_application_date_submit');
            $pcl->n_emp_rad_supports_notification = $req->input('n_emp_rad_supports_notification');
            $pcl->n_emp_bzg_notification_support_creation_date = $req->input('n_emp_bzg_notification_support_creation_date_submit');
            $pcl->n_emp_average_costs = $req->input('n_emp_average_costs');

            $pcl->save();

            if ($activity->affiliate->age() < 3) {
                $keysT1 = array_keys(Pcl::$KIDS_TABLES['T1']);
                $keysT2 = array_keys(Pcl::$KIDS_TABLES['T2']);

                $tables_kids = array('T1' => $keysT1, 'T2' => $keysT2);

                foreach ($tables_kids as $t => $keys) {
                    foreach ($keys as $k) {
                        PclTableKidData::updateOrCreate(
                            ['pcl_id' => $pcl->id, 'table' => $t, 'key' => $k],
                            ['value' => $req->input("{$t}.{$k}") ? $req->input("{$t}.{$k}") : 0]
                        );
                    }
                }
            }

            //  kid data table adds 1507
            $pcl->total0_1507 = $req->input('total0_1507');
            $pcl->total1_1507 = $req->input('total1_1507');
            $pcl->total2_1507 = $req->input('total2_1507');
            $pcl->totalAll_1507 = $req->input('totalAll_1507');

            $pcl->save();

            if ($activity->affiliate->age() < 3) {
                $keysT1_1507 = array_keys(Pcl::$KIDS_TABLES_1507['T1_1507']);
                $keysT2_1507 = array_keys(Pcl::$KIDS_TABLES_1507['T2_1507']);

                $tables_kids_1507 = array('T1_1507' => $keysT1_1507, 'T2_1507' => $keysT2_1507);

                foreach ($tables_kids_1507 as $t => $keys) {
                    foreach ($keys as $k) {
                        PclTableKid1507Data::updateOrCreate(
                            ['pcl_id' => $pcl->id, 'table' => $t, 'key' => $k],
                            ['value' => $req->input("{$t}.{$k}") ? $req->input("{$t}.{$k}") : 0]
                        );
                    }
                }
            }

//            AGENDAMIENTO CITA
//            Registros de contactabilidad
            $contactabilities = $req->input('contactabilities');

// Verificamos si $contactabilities['id'] es un array antes de aplicar whereNotIn()
            if (is_array($contactabilities['id'])) {
                PclContactability::where('pcl_id', $pcl->id)
                    ->whereNotIn('id', $contactabilities['id'])
                    ->delete();

                for ($i = 0; $i < count($contactabilities['id']); $i++) {
                    if ($contactabilities['contact_date'][$i] != null) {
                        if ($contactabilities['id'][$i] != null) {
                            $pcl_contactability = PclContactability::where('pcl_id', $pcl->id)
                                ->where('id', $contactabilities['id'][$i])
                                ->firstOrFail();
                        } else {
                            $pcl_contactability = new PclContactability;
                            $pcl_contactability->pcl_id = $pcl->id;
                        }
                        $pcl_contactability->contact_date = $contactabilities['contact_date'][2 * $i + 1];
                        $pcl_contactability->contact_phone = $contactabilities['contact_phone'][$i];
                        $pcl_contactability->communication_state = $contactabilities['communication_state'][$i];
                        $pcl_contactability->save();
                    }
                }
            }

            $admin_mark = $req->input('admin_mark');

            if (is_array($admin_mark['id'])) {
                PclAdminMark::where('pcl_id', $pcl->id)
                    ->whereNotIn('id', $admin_mark['id'])
                    ->delete();

                for ($i = 0; $i < count($admin_mark['id']); $i++) {
                    if ($admin_mark['admin_mark'][$i] != null) {
                        if ($admin_mark['id'][$i] != null) {
                            $pcl_admin_mark = PclAdminMark::where('pcl_id', $pcl->id)
                                ->where('id', $admin_mark['id'][$i])
                                ->firstOrFail();
                        } else {
                            $pcl_admin_mark = new PclAdminMark;
                            $pcl_admin_mark->pcl_id = $pcl->id;
                        }
                        $pcl_admin_mark->admin_mark = $admin_mark['admin_mark'][$i];
                        $pcl_admin_mark->admin_mark_observation = $admin_mark['admin_mark_observation'][$i];
                        $pcl_admin_mark->save();
                    }
                }
            }
            $diagnostics_valoration = $req->input('diagnostics_valoration');
            if ($diagnostics_valoration) {

                PclValorationDiagnostic::where('pcl_id', $pcl->id)
                    ->whereNotIn('id', $diagnostics_valoration['id'])
                    ->delete();

                for ($i = 0; $i < count($diagnostics_valoration['id']); $i++) {
                    if ($diagnostics_valoration['cod'][$i] != null) {
                        if ($diagnostics_valoration['id'][$i] != null) {
                            $pcl_diagnostic = PclValorationDiagnostic::where('pcl_id', $pcl->id)
                                ->where('id', $diagnostics_valoration['id'][$i])
                                ->firstOrFail();
                        } else {
                            $pcl_diagnostic = new PclValorationDiagnostic;
                            $pcl_diagnostic->pcl_id = $pcl->id;
                        }
                        $pcl_diagnostic->cod = newCodeCie10($diagnostics_valoration['cod'][$i]);
                        $pcl_diagnostic->description = $diagnostics_valoration['description'][$i];
                        $pcl_diagnostic->description_editable = $diagnostics_valoration['description_editable'][$i];
                        $pcl_diagnostic->save();
                    }
                }

            }

            if ($pcl_type_aux == 3) {
                $work_history = $req->input('work_history');

                if ($work_history) {

                    PclWorkHistory::where('pcl_id', $pcl->id)
                        ->whereNotIn('id', $work_history['id'])
                        ->delete();

                    for ($i = 0; $i < count($work_history['id']); $i++) {
                        if ($work_history['company'][$i] != null) {
                            if ($work_history['id'][$i] != null) {
                                $dictum_work_history = PclWorkHistory::where('pcl_id', $pcl->id)
                                    ->where('id', $work_history['id'][$i])
                                    ->firstOrFail();
                            } else {
                                $dictum_work_history = new PclWorkHistory;
                                $dictum_work_history->pcl_id = $pcl->id;
                            }

                            $dictum_work_history->company = $work_history['company'][$i];
                            $dictum_work_history->position = $work_history['position'][$i];
                            $dictum_work_history->risk_factor = $work_history['risk_factor'][$i];
                            $dictum_work_history->exposition_time = $work_history['exposition_time'][$i];
                            $dictum_work_history->save();
                        }
                    }
                }

            }

            $diagnostics = $req->input('diagnostics');
            if ($diagnostics) {

                PclDiagnostic::where('pcl_id', $pcl->id)
                    ->whereNotIn('id', $diagnostics['id'])
                    ->delete();

                for ($i = 0; $i < count($diagnostics['id']); $i++) {
                    if ($diagnostics['cod'][$i] != null) {
                        if ($diagnostics['id'][$i] != null) {
                            $pcl_diagnostic = PclDiagnostic::where('pcl_id', $pcl->id)
                                ->where('id', $diagnostics['id'][$i])
                                ->firstOrFail();
                        } else {
                            $pcl_diagnostic = new PclDiagnostic;
                            $pcl_diagnostic->pcl_id = $pcl->id;
                        }
                        $pcl_diagnostic->code = newCodeCie10($diagnostics['cod'][$i]);
                        $pcl_diagnostic->description = $diagnostics['description'][$i];
                        $pcl_diagnostic->origin = $diagnostics['origin'][$i];
                        if ($pcl_type_aux == 3 && isset($diagnostics['laterality'][$i])) {
                            $pcl_diagnostic->laterality = $diagnostics['laterality'][$i];
                        } else if (isset($diagnostics['deficiences'][$i])) {
                            $pcl_diagnostic->deficiences = $diagnostics['deficiences'][$i];
                        }
                        $pcl_diagnostic->save();
                    }
                }

            }
            if ($req->input('tutelage_international_convenio') == 'SI') {
                $interconsultations = $req->input('interconsultations');

                if (is_array($interconsultations)) {
                    PclInterconsultation::where('pcl_id', $pcl->id)
                        ->whereNotIn('id', $interconsultations['id'])
                        ->delete();

                    for ($i = 0; $i < count($interconsultations['id']); $i++) {
                        if ($interconsultations['exam_name'][$i] != null) {
                            if ($interconsultations['id'][$i] != null) {
                                $pcl_interconsultation = PclInterconsultation::where('pcl_id', $pcl->id)
                                    ->where('id', $interconsultations['id'][$i])
                                    ->firstOrFail();
                            } else {
                                $pcl_interconsultation = new PclInterconsultation;
                                $pcl_interconsultation->pcl_id = $pcl->id;
                            }
                            $pcl_interconsultation->exam_profesional = $interconsultations['exam_profesional'][$i];
                            $pcl_interconsultation->exam_name = $interconsultations['exam_name'][$i];
                            $pcl_interconsultation->exam_observation = $interconsultations['exam_observation'][$i];
                            $pcl_interconsultation->exam_result = $interconsultations['exam_result'][$i];
                            if (isset($interconsultations['exam_date'][2 * $i + 1])) {
                                $pcl_interconsultation->exam_date = $interconsultations['exam_date'][2 * $i + 1];
                            }
                            $pcl_interconsultation->save();
                        }
                    }
                }
            } else {
                $required_fields = [
                    'doc_full_name',
                    'doc_medical_record',
                    'valoration_valoration_type',
                    'valoration_v_date'
                ];

                $valid_required_fields = true;

                foreach ($required_fields as $field) {
                    if ($req->input($field) === null) {
                        $valid_required_fields = false;
                        break;
                    }
                }

                $interconsultations = $req->input('interconsultations');

                if (is_array($interconsultations) && $valid_required_fields) {
                    PclInterconsultation::where('pcl_id', $pcl->id)
                        ->whereNotIn('id', $interconsultations['id'])
                        ->delete();

                    for ($i = 0; $i < count($interconsultations['id']); $i++) {
                        if ($interconsultations['exam_name'][$i] != null) {
                            if ($interconsultations['id'][$i] != null) {
                                $pcl_interconsultation = PclInterconsultation::where('pcl_id', $pcl->id)
                                    ->where('id', $interconsultations['id'][$i])
                                    ->firstOrFail();
                            } else {
                                $pcl_interconsultation = new PclInterconsultation;
                                $pcl_interconsultation->pcl_id = $pcl->id;
                            }
                            $pcl_interconsultation->exam_profesional = $interconsultations['exam_profesional'][$i];
                            $pcl_interconsultation->exam_name = $interconsultations['exam_name'][$i];
                            $pcl_interconsultation->exam_observation = $interconsultations['exam_observation'][$i];
                            $pcl_interconsultation->exam_result = $interconsultations['exam_result'][$i];
                            if (isset($interconsultations['exam_date'][2 * $i + 1])) {
                                $pcl_interconsultation->exam_date = $interconsultations['exam_date'][2 * $i + 1];
                            }
                            $pcl_interconsultation->save();
                        }
                    }
                }
            }


            $deficiences = $req->input('deficiences');

            if (is_array($deficiences) && isset($deficiences['id']) && isset($deficiences['table'])) {
                PclDeficience::where('pcl_id', $pcl->id)
                    ->whereNotIn('id', $deficiences['id'])
                    ->delete();

                for ($i = 0; $i < count($deficiences['table']); $i++) {
                    if ($deficiences['table'][$i] != null) {
                        if ($deficiences['id'][$i] != null) {
                            $pcl_deficience = PclDeficience::where('pcl_id', $pcl->id)
                                ->where('id', $deficiences['id'][$i])
                                ->firstOrFail();
                        } else {
                            $pcl_deficience = new PclDeficience;
                            $pcl_deficience->pcl_id = $pcl->id;
                        }
                        $pcl_deficience->table = $deficiences['table'][$i];
                        $pcl_deficience->name = $deficiences['name'][$i];
                        $pcl_deficience->percentage = $deficiences['percentage'][$i];
                        if ($pcl->pcl_type != 3 && isset($deficiences['extremityx'][$i])) {
                            $pcl_deficience->extremity = ($deficiences['extremityx'][$i] == -1) ? 0 : 1;
                        } else {
                            $pcl_deficience->class       = isset($deficiences['class'][$i]) ? $deficiences['class'][$i] : null;
                            $pcl_deficience->cfpfu       = isset($deficiences['cfpfu'][$i]) ? $deficiences['cfpfu'][$i] : null;
                            $pcl_deficience->cfm1        = isset($deficiences['cfm1'][$i]) ? $deficiences['cfm1'][$i] : null;
                            $pcl_deficience->cfm2        = isset($deficiences['cfm2'][$i]) ? $deficiences['cfm2'][$i] : null;
                            $pcl_deficience->cfm3        = isset($deficiences['cfm3'][$i]) ? $deficiences['cfm3'][$i] : null;
                            $pcl_deficience->class_final = isset($deficiences['class_final'][$i]) ? $deficiences['class_final'][$i] : null;
                            $pcl_deficience->cat         = isset($deficiences['cat'][$i]) ? $deficiences['cat'][$i] : null;
                            //  $pcl_deficience->ear     = isset($deficiences['ears'][$i]) && $deficiences['ears'][$i] == -1 ? 0 : 1;
                            if (isset($deficiences['dominantx'][$i])) {
                                $pcl_deficience->dominant = ($deficiences['dominantx'][$i] == -1) ? 0 : 1;
                            } else {
                                $pcl_deficience->dominant    = 0;
                            }
                        }
                        $pcl_deficience->save();
                    }
                }
            }

            // DOCUMENTARY VALIDATIONS
            $documentary_validations = $req->input('documentary_validations');
            if ($documentary_validations) {
                PclDocumentaryValidation::query()
                    ->where('pcl_id', $pcl->id)
                    ->whereNotIn('id', $documentary_validations['id'])
                    ->delete();
                $counter = 0;
                for ($i = 0; $i < count($documentary_validations['id']); $i++) {
                    if ($documentary_validations['id'][$i] != null) {
                        $documentary_validation = PclDocumentaryValidation::query()
                            ->where('pcl_id', $pcl->id)
                            ->where('id', $documentary_validations['id'][$i])
                            ->firstOrFail();
                    } else {
                        $documentary_validation = new PclDocumentaryValidation;
                        $documentary_validation->pcl_id = $pcl->id;
                    }
                    $documentary_validation->document_validation = $documentary_validations['document_validation'][$i];
                    $documentary_validation->document_validation_observation = $documentary_validations['document_validation_observation'][$i];

                    $documentary_validation->save();
                }
            }

            // PRELIMINARY OPINIONS
            $preliminary_opinions = $req->input('preliminary_opinions');
            if ($preliminary_opinions) {
                PclPreliminaryOpinion::query()
                    ->where('pcl_id', $pcl->id)
                    ->whereNotIn('id', $preliminary_opinions['id'])
                    ->delete();
                $counter = 0;
                for ($i = 0; $i < count($preliminary_opinions['id']); $i++) {
                    if ($preliminary_opinions['id'][$i] != null) {
                        $preliminary_opinion = PclPreliminaryOpinion::query()
                            ->where('pcl_id', $pcl->id)
                            ->where('id', $preliminary_opinions['id'][$i])
                            ->firstOrFail();
                    } else {
                        $preliminary_opinion = new PclPreliminaryOpinion;
                        $preliminary_opinion->pcl_id = $pcl->id;
                    }
                    $preliminary_opinion->document_preliminary_opinion = $preliminary_opinions['document_preliminary_opinion'][$i];
                    $preliminary_opinion->document_preliminary_opinion_observation = $preliminary_opinions['document_preliminary_opinion_observation'][$i];

                    $preliminary_opinion->save();
                }
            }

            // QUALITY CONTROLS
            $quality_controls = $req->input('quality_controls');
            if ($quality_controls) {
                PclQualityControls::query()
                    ->where('pcl_id', $pcl->id)
                    ->whereNotIn('id', $quality_controls['id'])
                    ->delete();
                $counter = 0;
                for ($i = 0; $i < count($quality_controls['id']); $i++) {
                    if ($quality_controls['id'][$i] != null) {
                        $quality_control = PclQualityControls::query()
                            ->where('pcl_id', $pcl->id)
                            ->where('id', $quality_controls['id'][$i])
                            ->firstOrFail();
                    } else {
                        $quality_control = new PclQualityControls;
                        $quality_control->pcl_id = $pcl->id;
                    }
                    $quality_control->document_quality_control = $quality_controls['document_quality_control'][$i];
                    $quality_control->document_quality_control_observation = $quality_controls['document_quality_control_observation'][$i];

                    $quality_control->save();
                }
            }

            if ($req->input('laborally_active')) {

                PclTableData::where('pcl_id', $pcl->id)->delete();

                if (!($pcl_type_aux == 3)) {
                    $tables = array('T6', 'T7', 'T8', 'T9', 'T10');

                    foreach ($tables as $t) {
                        for ($i = 0; $i < 10; $i++) {
                            PclTableData::updateOrCreate(
                                ['pcl_id' => $pcl->id, 'table' => $t, 'key' => $i],
                                ['value' => $req->input("{$t}.{$i}") ? $req->input("{$t}.{$i}") : 0]
                            );
                        }
                    }
                } else {
                    $tables = array('T90701', 'T90702', 'T90703', 'T90704', 'T90705', 'T90706', 'T90707');
                    foreach ($tables as $t) {
                        for ($i = 0; $i < 10; $i++) {
                            PclTableData::updateOrCreate(
                                ['pcl_id' => $pcl->id, 'table' => $t, 'key' => $i],
                                ['value' => $req->input("{$t}.{$i}") ? $req->input("{$t}.{$i}") : 0]
                            );
                        }
                    }
                    $tables = array('T90711', 'T90712', 'T90713', 'T90714', 'T90715', 'T90716', 'T90717');
                    foreach ($tables as $t) {
                        $i = 0;
                        PclTableData::updateOrCreate(
                            ['pcl_id' => $pcl->id, 'table' => $t, 'key' => $i],
                            ['value' => $req->input("{$t}.{$i}") ? $req->input("{$t}.{$i}") : 0]
                        );
                    }
                }
            }

            DB::commit();

        } catch (Exception $e) {
            DB::rollback();
        }

        return redirect("servicio/$id/pcl", 302, [], true);
    }


    public function pdf(Request $req, $cpath, $id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();

        $pcl = new Pcl;
        $pcl->activity_id = $id;
        $pcl->setRelation('activity', $activity);
        $pcl->created_at = new DateTime();

        $pcl->pcl_type = $req->input('pcl_type');
        $pcl->attorney_name = $req->input('attorney_name');
        $pcl->attorney_cc = $req->input('attorney_cc');
        $pcl->sender = $req->input('sender');
        $pcl->applicant = $req->input('applicant');
        $pcl->dominance = $req->input('dominance');
        $pcl->required_support = $req->input('required_support');
        $pcl->previous_pcl = $req->input('previous_pcl');
        $pcl->description = $req->input('description');
        $pcl->require_3rd = $req->input('require_3rd');
        $pcl->require_3rd_dk = $req->input('require_3rd_dk');
        $pcl->require_device = $req->input('require_device');

        $pcl->request_reason = $req->input('request_reason');
        $pcl->sender_name = $req->input('sender_name');
        $pcl->sender_doc = $req->input('sender_doc');
        $pcl->sender_phone = $req->input('sender_phone');
        $pcl->sender_address = $req->input('sender_address');
        $pcl->sender_email = $req->input('sender_email');
        $pcl->sender_city = $req->input('sender_city');
        $pcl->sinister_date = $req->input('sinister_date');
        $pcl->effective_notify_date = $req->input('effective_notify_date');

        $pcl->ph_medical = $req->input('ph_medical');
        $pcl->ph_surgical = $req->input('ph_surgical');
        $pcl->ph_toxic = $req->input('ph_toxic');
        $pcl->ph_medicines = $req->input('ph_medicines');
        $pcl->ph_gynecobotters = $req->input('ph_gynecobotters');
        $pcl->ph_traumatic = $req->input('ph_traumatic');
        $pcl->ph_family = $req->input('ph_family');

        $pcl->doc_clinical_history = $req->input('doc_clinical_history');
        $pcl->doc_epicrisis = $req->input('doc_epicrisis');
        $pcl->doc_paraclinic = $req->input('doc_paraclinic');
        $pcl->doc_prehiring = $req->input('doc_prehiring');
        $pcl->doc_periodics = $req->input('doc_periodics');
        $pcl->doc_post_occupational = $req->input('doc_post_occupational');
        $pcl->doc_health_concept = $req->input('doc_health_concept');
        $pcl->doc_others = $req->input('doc_others');

        $pcl->laborally_active = $req->input('laborally_active');
        $pcl->use_laboral_role = $req->input('use_laboral_role');
        $pcl->use_laboral_role_description = $req->input('use_laboral_role_description');
        $pcl->role_restriction = $req->input('role_restriction');
        $pcl->economic_self = $req->input('economic_self');
        $pcl->role_category = $req->input('role_category');
        $pcl->monetary_value = $req->input('monetary_value');

        $pcl->role = $req->input('role');
        $pcl->sinister_number = $req->input('sinister_number');
        $pcl->pension_review = $req->input('pension_review');
        $pcl->catastrophic = $req->input('catastrophic');
        $pcl->details = $req->input('details');
        $pcl->board = $req->input('board');
        $pcl->structuring_at = $req->input('structuring_at_submit');

        if ($pcl->pcl_type == 3) {
            $pcl->doc_at = $req->input('doc_at');
            $pcl->doc_apt = $req->input('doc_apt');
            $pcl->ponencia = $req->input('ponencia');
        }

        $pcl->structuring_support = $req->input('structuring_support');
        $pcl->origin = $req->input('origin');
        $pcl->event_type = $req->input('event_type');
        $pcl->event_date = $req->input('event_date_submit');
        $pcl->concept_technical_audit = $req->input('concept_technical_audit');

        $diagnostics = $req->input('diagnostics');

        if (isset($diagnostics['id']) && is_array($diagnostics['id'])) {
            for ($i = 0; $i < count($diagnostics['id']); $i++) {
                if ($diagnostics['cod'][$i] != null) {
                    $pcl_diagnostic = new PclDiagnostic;
                    $pcl_diagnostic->code = newCodeCie10($diagnostics['cod'][$i]);
                    $pcl_diagnostic->description = $diagnostics['description'][$i];
                    $pcl_diagnostic->origin = $diagnostics['origin'][$i];
                    if ($pcl->pcl_type == 3) {
                        $pcl_diagnostic->laterality = $diagnostics['laterality'][$i];
                    } else {
                        $pcl_diagnostic->deficiences = $diagnostics['deficiences'][$i];
                    }
                    $pcl->diagnostics->add($pcl_diagnostic);
                }
            }
        }

        $sends = $req->input('sends');
        if (isset($sends['id']) && is_array($sends['id'])) {
            for ($i = 0; $i < count($sends['id']); $i++) {
                if ($sends['id'][$i] != null) {
                    $pcl_sends = new PclSends();
                    $pcl_sends->entity_type = $sends['entity_type'][$i];
                    $pcl_sends->send_method = $sends['send_method'][$i];
                    $pcl_sends->guide_email = $sends['guide_email'][$i];
                    $pcl_sends->send_state = $sends['send_state'][$i];
                    $pcl_sends->effective_notify_date = $sends['effective_notify_date'][$i];
                    $pcl->pcl_sends->add($pcl_sends);
                }
            }
        }

        $interconsultations = $req->input('interconsultations');
        if (isset($interconsultations['id']) && is_array($interconsultations['id'])) {
            for ($i = 0; $i < count($interconsultations['id']); $i++) {
                if ($interconsultations['exam_name'][$i] != null) {
                    $pcl_interconsultation = new PclInterconsultation;
                    $pcl_interconsultation->exam_profesional = $interconsultations['exam_profesional'][$i];
                    $pcl_interconsultation->exam_name = $interconsultations['exam_name'][$i];
                    $pcl_interconsultation->exam_result = $interconsultations['exam_result'][$i];
                    if (isset($interconsultations['exam_date'][2 * $i + 1])) {
                        $pcl_interconsultation->exam_date = $interconsultations['exam_date'][2 * $i + 1];
                    }
                    $pcl->interconsultations->add($pcl_interconsultation);
                }
            }
        }
        if ($pcl->pcl_type == 3) {
            $deficiences = $req->input('deficiences');
            if (is_array($deficiences['table'])) {     // Verificar si es array antes de contar

                for ($i = 0; $i < count($deficiences['table']); $i++) {
                    if ($deficiences['table'][$i] != null) {
                        $pcl_deficience = new PclDeficience;
                        $pcl_deficience->table = $deficiences['table'][$i];
                        $pcl_deficience->name = $deficiences['name'][$i];
                        $pcl_deficience->percentage = $deficiences['percentage'][$i];

                        $pcl->deficiences->add($pcl_deficience);
                    }
                }

            }
            $work_history = $req->input('work_history');
            if (is_array($work_history['id'])) {
                for ($i = 0; $i < count($work_history['id']); $i++) {
                    if ($work_history['company'][$i] != null) {
                        $pcl_work_history = new PclWorkHistory;
                        $pcl_work_history->company = $work_history['company'][$i];
                        $pcl_work_history->position = $work_history['position'][$i];
                        $pcl_work_history->risk_factor = $work_history['risk_factor'][$i];
                        $pcl_work_history->exposition_time = $work_history['exposition_time'][$i];

                        $pcl->work_history->add($pcl_work_history);
                    }
                }
            }

            $pcl->addTableValues('T90701', $req->input('T90701'));

            for ($i = 0; $i < 10; $i++) {
                $pcl_table_data = new PclTableData;
                $pcl_table_data->table = 'T90701';
                $pcl_table_data->key = $i;
                $pcl_table_data->value = $req->input("T90701.{$i}");
                $pcl->table_datas->add($pcl_table_data);
            }

            $pcl->addTableValues('T90702', $req->input('T90702'));
            for ($i = 0; $i < 10; $i++) {
                $pcl_table_data = new PclTableData;
                $pcl_table_data->table = 'T90702';
                $pcl_table_data->key = $i;
                $pcl_table_data->value = $req->input("T90702.{$i}");
                $pcl->table_datas->add($pcl_table_data);

            }

            $pcl->addTableValues('T90703', $req->input('T90703'));
            for ($i = 0; $i < 10; $i++) {
                $pcl_table_data = new PclTableData;
                $pcl_table_data->table = 'T90703';
                $pcl_table_data->key = $i;
                $pcl_table_data->value = $req->input("T90703.{$i}");
                $pcl->table_datas->add($pcl_table_data);

            }

            $pcl->addTableValues('T90704', $req->input('T90704'));
            for ($i = 0; $i < 10; $i++) {
                $pcl_table_data = new PclTableData;
                $pcl_table_data->table = 'T90704';
                $pcl_table_data->key = $i;
                $pcl_table_data->value = $req->input("T90704.{$i}");
                $pcl->table_datas->add($pcl_table_data);

            }

            $pcl->addTableValues('T90705', $req->input('T90705'));
            for ($i = 0; $i < 10; $i++) {
                $pcl_table_data = new PclTableData;
                $pcl_table_data->table = 'T90705';
                $pcl_table_data->key = $i;
                $pcl_table_data->value = $req->input("T90705.{$i}");
                $pcl->table_datas->add($pcl_table_data);

            }

            $pcl->addTableValues('T90706', $req->input('T90706'));
            for ($i = 0; $i < 10; $i++) {
                $pcl_table_data = new PclTableData;
                $pcl_table_data->table = 'T90706';
                $pcl_table_data->key = $i;
                $pcl_table_data->value = $req->input("T90706.{$i}");
                $pcl->table_datas->add($pcl_table_data);

            }

            $pcl->addTableValues('T90707', $req->input('T90707'));
            for ($i = 0; $i < 10; $i++) {
                $pcl_table_data = new PclTableData;
                $pcl_table_data->table = 'T90707';
                $pcl_table_data->key = $i;
                $pcl_table_data->value = $req->input("T90707.{$i}");
                $pcl->table_datas->add($pcl_table_data);
            }

            $l = 0;
            $pcl->addTableValues('T90711', $req->input('T90711'));
            $pcl_table_data = new PclTableData;
            $pcl_table_data->table = 'T90711';
            $pcl_table_data->key = $l;
            $pcl_table_data->value = $req->input("T90711.{$l}");
            $pcl->table_datas->add($pcl_table_data);

            $pcl->addTableValues('T90712', $req->input('T90712'));
            $pcl_table_data = new PclTableData;
            $pcl_table_data->table = 'T90712';
            $pcl_table_data->key = $l;
            $pcl_table_data->value = $req->input("T90712.{$l}");
            $pcl->table_datas->add($pcl_table_data);

            $pcl->addTableValues('T90713', $req->input('T90713'));
            $pcl_table_data = new PclTableData;
            $pcl_table_data->table = 'T90713';
            $pcl_table_data->key = $l;
            $pcl_table_data->value = $req->input("T90713.{$l}");
            $pcl->table_datas->add($pcl_table_data);

            $pcl->addTableValues('T90714', $req->input('T90714'));
            $pcl_table_data = new PclTableData;
            $pcl_table_data->table = 'T90714';
            $pcl_table_data->key = $l;
            $pcl_table_data->value = $req->input("T90714.{$l}");
            $pcl->table_datas->add($pcl_table_data);

            $pcl->addTableValues('T90715', $req->input('T90715'));
            $pcl_table_data = new PclTableData;
            $pcl_table_data->table = 'T90715';
            $pcl_table_data->key = $l;
            $pcl_table_data->value = $req->input("T90715.{$l}");
            $pcl->table_datas->add($pcl_table_data);

            $pcl->addTableValues('T90716', $req->input('T90716'));
            $pcl_table_data = new PclTableData;
            $pcl_table_data->table = 'T90716';
            $pcl_table_data->key = $l;
            $pcl_table_data->value = $req->input("T90716.{$l}");
            $pcl->table_datas->add($pcl_table_data);

            $pcl->addTableValues('T90717', $req->input('T90717'));
            $pcl_table_data = new PclTableData;
            $pcl_table_data->table = 'T90717';
            $pcl_table_data->key = $l;
            $pcl_table_data->value = $req->input("T90717.{$l}");
            $pcl->table_datas->add($pcl_table_data);

        } else {
            $deficiences = $req->input('deficiences');
            if (is_array($deficiences['table'])) {     // Verificar si es array antes de contar

                for ($i = 0; $i < count($deficiences['table']); $i++) {
                    if ($deficiences['table'][$i] != null) {
                        $pcl_deficience = new PclDeficience;
                        $pcl_deficience->table = $deficiences['table'][$i];
                        $pcl_deficience->name = $deficiences['name'][$i];
                        $pcl_deficience->percentage = $deficiences['percentage'][$i];
                        $pcl_deficience->class = $deficiences['class'][$i];
                        $pcl_deficience->cfpfu = $deficiences['cfpfu'][$i];
                        $pcl_deficience->cfm1 = $deficiences['cfm1'][$i];
                        $pcl_deficience->cfm2 = $deficiences['cfm2'][$i];
                        $pcl_deficience->cfm3 = $deficiences['cfm3'][$i];
                        $pcl_deficience->class_final = $deficiences['class_final'][$i];
                        $pcl_deficience->cat = $deficiences['cat'][$i];
//                            $pcl_deficience->ear = ($deficiences['ears'][$i] == -1) ? 0 : 1;
                        $pcl_deficience->dominant = ($deficiences['dominantx'][$i] == -1) ? 0 : 1;
                        $pcl->deficiences->add($pcl_deficience);
                    }
                }
            }
            $pcl->addTableValues('T6', $req->input('T6'));
            for ($i = 0; $i < 10; $i++) {
                $pcl_table_data = new PclTableData;
                $pcl_table_data->table = 'T6';
                $pcl_table_data->key = $i;
                $pcl_table_data->value = $req->input("T6.{$i}");
                $pcl->table_datas->add($pcl_table_data);
            }

            $pcl->addTableValues('T7', $req->input('T7'));
            for ($i = 0; $i < 10; $i++) {
                $pcl_table_data = new PclTableData;
                $pcl_table_data->table = 'T7';
                $pcl_table_data->key = $i;
                $pcl_table_data->value = $req->input("T7.{$i}");
                $pcl->table_datas->add($pcl_table_data);
            }

            $pcl->addTableValues('T8', $req->input('T8'));
            for ($i = 0; $i < 10; $i++) {
                $pcl_table_data = new PclTableData;
                $pcl_table_data->table = 'T8';
                $pcl_table_data->key = $i;
                $pcl_table_data->value = $req->input("T8.{$i}");
                $pcl->table_datas->add($pcl_table_data);
            }

            $pcl->addTableValues('T9', $req->input('T9'));
            for ($i = 0; $i < 10; $i++) {
                $pcl_table_data = new PclTableData;
                $pcl_table_data->table = 'T9';
                $pcl_table_data->key = $i;
                $pcl_table_data->value = $req->input("T9.{$i}");
                $pcl->table_datas->add($pcl_table_data);
            }

            $pcl->addTableValues('T10', $req->input('T10'));
            for ($i = 0; $i < 10; $i++) {
                $pcl_table_data = new PclTableData;
                $pcl_table_data->table = 'T10';
                $pcl_table_data->key = $i;
                $pcl_table_data->value = $req->input("T10.{$i}");
                $pcl->table_datas->add($pcl_table_data);
            }
        }
        $pcl_dates = Pcl::query()
            ->where('activity_id', $activity->id)
            ->firstOrFail();
        $pcl_date = $pcl_dates->pcl_date;
        $req_date = $pcl_dates->req_date;
        $preview = true;
        $pdf = PDF::loadView('services.pcl.docs.dictum_pcl_pdf', ['pcl' => $pcl, 'activity' => $activity, 'pcl_date' => $pcl_date, 'req_date' => $req_date, 'watermark' => true, 'preview' => $preview]);
        return $pdf->stream('preview.pdf');
    }

    public function pdfValoration(Request $req, $cpath, $id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();

        $pcl = new Pcl;
        $pcl->activity_id = $id;
        $pcl->setRelation('activity', $activity);
        $pcl->created_at = new DateTime();

        $pcl->pcl_type = $req->input('pcl_type');
        $pcl->attorney_name = $req->input('attorney_name');
        $pcl->attorney_cc = $req->input('attorney_cc');
        $pcl->sender = $req->input('sender');
        $pcl->applicant = $req->input('applicant');
        $pcl->dominance = $req->input('dominance');
        $pcl->required_support = $req->input('required_support');
        $pcl->previous_pcl = $req->input('previous_pcl');
        $pcl->description = $req->input('description');
        $pcl->require_3rd = $req->input('require_3rd');
        $pcl->require_3rd_dk = $req->input('require_3rd_dk');
        $pcl->require_device = $req->input('require_device');

        $pcl->request_reason = $req->input('request_reason');
        $pcl->sender_name = $req->input('sender_name');
        $pcl->sender_doc = $req->input('sender_doc');
        $pcl->sender_phone = $req->input('sender_phone');
        $pcl->sender_address = $req->input('sender_address');
        $pcl->sender_email = $req->input('sender_email');
        $pcl->sender_city = $req->input('sender_city');
        $pcl->sinister_date = $req->input('sinister_date');
        $pcl->effective_notify_date = $req->input('effective_notify_date');

        $pcl->ph_medical = $req->input('ph_medical');
        $pcl->ph_surgical = $req->input('ph_surgical');
        $pcl->ph_toxic = $req->input('ph_toxic');
        $pcl->ph_medicines = $req->input('ph_medicines');
        $pcl->ph_gynecobotters = $req->input('ph_gynecobotters');
        $pcl->ph_traumatic = $req->input('ph_traumatic');
        $pcl->ph_family = $req->input('ph_family');

        $pcl->doc_clinical_history = $req->input('doc_clinical_history');
        $pcl->doc_epicrisis = $req->input('doc_epicrisis');
        $pcl->doc_paraclinic = $req->input('doc_paraclinic');
        $pcl->doc_prehiring = $req->input('doc_prehiring');
        $pcl->doc_periodics = $req->input('doc_periodics');
        $pcl->doc_post_occupational = $req->input('doc_post_occupational');
        $pcl->doc_health_concept = $req->input('doc_health_concept');
        $pcl->doc_others = $req->input('doc_others');

        $pcl->laborally_active = $req->input('laborally_active');
        $pcl->use_laboral_role = $req->input('use_laboral_role');
        $pcl->use_laboral_role_description = $req->input('use_laboral_role_description');
        $pcl->role_restriction = $req->input('role_restriction');
        $pcl->economic_self = $req->input('economic_self');
        $pcl->role_category = $req->input('role_category');
        $pcl->monetary_value = $req->input('monetary_value');

        $pcl->role = $req->input('role');
        $pcl->sinister_number = $req->input('sinister_number');
        $pcl->pension_review = $req->input('pension_review');
        $pcl->catastrophic = $req->input('catastrophic');
        $pcl->details = $req->input('details');
        $pcl->board = $req->input('board');
        $pcl->structuring_at = $req->input('structuring_at_submit');

        if ($pcl->pcl_type == 3) {
            $pcl->doc_at = $req->input('doc_at');
            $pcl->doc_apt = $req->input('doc_apt');
            $pcl->ponencia = $req->input('ponencia');
        }

        $pcl->structuring_support = $req->input('structuring_support');
        $pcl->origin = $req->input('origin');
        $pcl->event_type = $req->input('event_type');
        $pcl->event_date = $req->input('event_date_submit');
        $pcl->concept_technical_audit = $req->input('concept_technical_audit');

        $diagnostics = $req->input('diagnostics');

        if (isset($diagnostics['id']) && is_array($diagnostics['id'])) {
            for ($i = 0; $i < count($diagnostics['id']); $i++) {
                if ($diagnostics['cod'][$i] != null) {
                    $pcl_diagnostic = new PclDiagnostic;
                    $pcl_diagnostic->code = newCodeCie10($diagnostics['cod'][$i]);
                    $pcl_diagnostic->description = $diagnostics['description'][$i];
                    $pcl_diagnostic->origin = $diagnostics['origin'][$i];
                    if ($pcl->pcl_type == 3) {
                        $pcl_diagnostic->laterality = $diagnostics['laterality'][$i];
                    } else {
                        $pcl_diagnostic->deficiences = $diagnostics['deficiences'][$i];
                    }
                    $pcl->diagnostics->add($pcl_diagnostic);
                }
            }
        }

        $sends = $req->input('sends');
        if (isset($sends['id']) && is_array($sends['id'])) {
            for ($i = 0; $i < count($sends['id']); $i++) {
                if ($sends['id'][$i] != null) {
                    $pcl_sends = new PclSends();
                    $pcl_sends->entity_type = $sends['entity_type'][$i];
                    $pcl_sends->send_method = $sends['send_method'][$i];
                    $pcl_sends->guide_email = $sends['guide_email'][$i];
                    $pcl_sends->send_state = $sends['send_state'][$i];
                    $pcl_sends->effective_notify_date = $sends['effective_notify_date'][$i];
                    $pcl->pcl_sends->add($pcl_sends);
                }
            }
        }

        $interconsultations = $req->input('interconsultations');
        if (isset($interconsultations['id']) && is_array($interconsultations['id'])) {
            for ($i = 0; $i < count($interconsultations['id']); $i++) {
                if ($interconsultations['exam_name'][$i] != null) {
                    $pcl_interconsultation = new PclInterconsultation;
                    $pcl_interconsultation->exam_profesional = $interconsultations['exam_profesional'][$i];
                    $pcl_interconsultation->exam_name = $interconsultations['exam_name'][$i];
                    $pcl_interconsultation->exam_result = $interconsultations['exam_result'][$i];
                    if (isset($interconsultations['exam_date'][2 * $i + 1])) {
                        $pcl_interconsultation->exam_date = $interconsultations['exam_date'][2 * $i + 1];
                    }
                    $pcl->interconsultations->add($pcl_interconsultation);
                }
            }
        }
        if ($pcl->pcl_type == 3) {
            $deficiences = $req->input('deficiences');
            for ($i = 0; $i < count($deficiences['table']); $i++) {
                if ($deficiences['table'][$i] != null) {
                    $pcl_deficience = new PclDeficience;
                    $pcl_deficience->table = $deficiences['table'][$i];
                    $pcl_deficience->name = $deficiences['name'][$i];
                    $pcl_deficience->percentage = $deficiences['percentage'][$i];

                    $pcl->deficiences->add($pcl_deficience);
                }
            }


            $work_history = $req->input('work_history');
            for ($i = 0; $i < count($work_history['id']); $i++) {
                if ($work_history['company'][$i] != null) {
                    $pcl_work_history = new PclWorkHistory;
                    $pcl_work_history->company = $work_history['company'][$i];
                    $pcl_work_history->position = $work_history['position'][$i];
                    $pcl_work_history->risk_factor = $work_history['risk_factor'][$i];
                    $pcl_work_history->exposition_time = $work_history['exposition_time'][$i];

                    $pcl->work_history->add($pcl_work_history);
                }
            }

            $pcl->addTableValues('T90701', $req->input('T90701'));

            for ($i = 0; $i < 10; $i++) {
                $pcl_table_data = new PclTableData;
                $pcl_table_data->table = 'T90701';
                $pcl_table_data->key = $i;
                $pcl_table_data->value = $req->input("T90701.{$i}");
                $pcl->table_datas->add($pcl_table_data);
            }

            $pcl->addTableValues('T90702', $req->input('T90702'));
            for ($i = 0; $i < 10; $i++) {
                $pcl_table_data = new PclTableData;
                $pcl_table_data->table = 'T90702';
                $pcl_table_data->key = $i;
                $pcl_table_data->value = $req->input("T90702.{$i}");
                $pcl->table_datas->add($pcl_table_data);

            }

            $pcl->addTableValues('T90703', $req->input('T90703'));
            for ($i = 0; $i < 10; $i++) {
                $pcl_table_data = new PclTableData;
                $pcl_table_data->table = 'T90703';
                $pcl_table_data->key = $i;
                $pcl_table_data->value = $req->input("T90703.{$i}");
                $pcl->table_datas->add($pcl_table_data);

            }

            $pcl->addTableValues('T90704', $req->input('T90704'));
            for ($i = 0; $i < 10; $i++) {
                $pcl_table_data = new PclTableData;
                $pcl_table_data->table = 'T90704';
                $pcl_table_data->key = $i;
                $pcl_table_data->value = $req->input("T90704.{$i}");
                $pcl->table_datas->add($pcl_table_data);

            }

            $pcl->addTableValues('T90705', $req->input('T90705'));
            for ($i = 0; $i < 10; $i++) {
                $pcl_table_data = new PclTableData;
                $pcl_table_data->table = 'T90705';
                $pcl_table_data->key = $i;
                $pcl_table_data->value = $req->input("T90705.{$i}");
                $pcl->table_datas->add($pcl_table_data);

            }

            $pcl->addTableValues('T90706', $req->input('T90706'));
            for ($i = 0; $i < 10; $i++) {
                $pcl_table_data = new PclTableData;
                $pcl_table_data->table = 'T90706';
                $pcl_table_data->key = $i;
                $pcl_table_data->value = $req->input("T90706.{$i}");
                $pcl->table_datas->add($pcl_table_data);

            }

            $pcl->addTableValues('T90707', $req->input('T90707'));
            for ($i = 0; $i < 10; $i++) {
                $pcl_table_data = new PclTableData;
                $pcl_table_data->table = 'T90707';
                $pcl_table_data->key = $i;
                $pcl_table_data->value = $req->input("T90707.{$i}");
                $pcl->table_datas->add($pcl_table_data);
            }

            $l = 0;
            $pcl->addTableValues('T90711', $req->input('T90711'));
            $pcl_table_data = new PclTableData;
            $pcl_table_data->table = 'T90711';
            $pcl_table_data->key = $l;
            $pcl_table_data->value = $req->input("T90711.{$l}");
            $pcl->table_datas->add($pcl_table_data);

            $pcl->addTableValues('T90712', $req->input('T90712'));
            $pcl_table_data = new PclTableData;
            $pcl_table_data->table = 'T90712';
            $pcl_table_data->key = $l;
            $pcl_table_data->value = $req->input("T90712.{$l}");
            $pcl->table_datas->add($pcl_table_data);

            $pcl->addTableValues('T90713', $req->input('T90713'));
            $pcl_table_data = new PclTableData;
            $pcl_table_data->table = 'T90713';
            $pcl_table_data->key = $l;
            $pcl_table_data->value = $req->input("T90713.{$l}");
            $pcl->table_datas->add($pcl_table_data);

            $pcl->addTableValues('T90714', $req->input('T90714'));
            $pcl_table_data = new PclTableData;
            $pcl_table_data->table = 'T90714';
            $pcl_table_data->key = $l;
            $pcl_table_data->value = $req->input("T90714.{$l}");
            $pcl->table_datas->add($pcl_table_data);

            $pcl->addTableValues('T90715', $req->input('T90715'));
            $pcl_table_data = new PclTableData;
            $pcl_table_data->table = 'T90715';
            $pcl_table_data->key = $l;
            $pcl_table_data->value = $req->input("T90715.{$l}");
            $pcl->table_datas->add($pcl_table_data);

            $pcl->addTableValues('T90716', $req->input('T90716'));
            $pcl_table_data = new PclTableData;
            $pcl_table_data->table = 'T90716';
            $pcl_table_data->key = $l;
            $pcl_table_data->value = $req->input("T90716.{$l}");
            $pcl->table_datas->add($pcl_table_data);

            $pcl->addTableValues('T90717', $req->input('T90717'));
            $pcl_table_data = new PclTableData;
            $pcl_table_data->table = 'T90717';
            $pcl_table_data->key = $l;
            $pcl_table_data->value = $req->input("T90717.{$l}");
            $pcl->table_datas->add($pcl_table_data);

        } else {
            $deficiences = $req->input('deficiences');
            if ($deficiences !== null) {
                for ($i = 0; $i < count($deficiences['table']); $i++) {
                    if ($deficiences['table'][$i] != null) {
                        $pcl_deficience = new PclDeficience;
                        $pcl_deficience->table = $deficiences['table'][$i];
                        $pcl_deficience->name = $deficiences['name'][$i];
                        $pcl_deficience->percentage = $deficiences['percentage'][$i];
                        $pcl_deficience->class = $deficiences['class'][$i];
                        $pcl_deficience->cfpfu = $deficiences['cfpfu'][$i];
                        $pcl_deficience->cfm1 = $deficiences['cfm1'][$i];
                        $pcl_deficience->cfm2 = $deficiences['cfm2'][$i];
                        $pcl_deficience->cfm3 = $deficiences['cfm3'][$i];
                        $pcl_deficience->class_final = $deficiences['class_final'][$i];
                        $pcl_deficience->cat = $deficiences['cat'][$i];
//                        $pcl_deficience->ear = ($deficiences['ears'][$i] == -1) ? 0 : 1;
                        $pcl_deficience->dominant = ($deficiences['dominantx'][$i] == -1) ? 0 : 1;

                        $pcl->deficiences->add($pcl_deficience);
                    }
                }
            }

            $pcl->addTableValues('T6', $req->input('T6'));
            for ($i = 0; $i < 10; $i++) {
                $pcl_table_data = new PclTableData;
                $pcl_table_data->table = 'T6';
                $pcl_table_data->key = $i;
                $pcl_table_data->value = $req->input("T6.{$i}");
                $pcl->table_datas->add($pcl_table_data);
            }

            $pcl->addTableValues('T7', $req->input('T7'));
            for ($i = 0; $i < 10; $i++) {
                $pcl_table_data = new PclTableData;
                $pcl_table_data->table = 'T7';
                $pcl_table_data->key = $i;
                $pcl_table_data->value = $req->input("T7.{$i}");
                $pcl->table_datas->add($pcl_table_data);
            }

            $pcl->addTableValues('T8', $req->input('T8'));
            for ($i = 0; $i < 10; $i++) {
                $pcl_table_data = new PclTableData;
                $pcl_table_data->table = 'T8';
                $pcl_table_data->key = $i;
                $pcl_table_data->value = $req->input("T8.{$i}");
                $pcl->table_datas->add($pcl_table_data);
            }

            $pcl->addTableValues('T9', $req->input('T9'));
            for ($i = 0; $i < 10; $i++) {
                $pcl_table_data = new PclTableData;
                $pcl_table_data->table = 'T9';
                $pcl_table_data->key = $i;
                $pcl_table_data->value = $req->input("T9.{$i}");
                $pcl->table_datas->add($pcl_table_data);
            }

            $pcl->addTableValues('T10', $req->input('T10'));
            for ($i = 0; $i < 10; $i++) {
                $pcl_table_data = new PclTableData;
                $pcl_table_data->table = 'T10';
                $pcl_table_data->key = $i;
                $pcl_table_data->value = $req->input("T10.{$i}");
                $pcl->table_datas->add($pcl_table_data);
            }
        }
        $pcl_dates = Pcl::query()
            ->where('activity_id', $activity->id)
            ->firstOrFail();
        $pcl_date = $pcl_dates->pcl_date;
        $req_date = $pcl_dates->req_date;
        $pdf = PDF::loadView('services.pcl.docs.pcl_valoration_pdf', ['pcl' => $pcl, 'activity' => $activity, 'pcl_date' => $pcl_date, 'req_date' => $req_date, 'watermark' => true]);
        return $pdf->stream('preview.pdf');
    }
    public function pdfRejection(Request $req, $cpath, $id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();

        $pcl = new Pcl;
        $pcl->activity_id = $id;
        $pcl->setRelation('activity', $activity);
        $pcl->created_at = new DateTime();

        $pcl->pcl_type = $req->input('pcl_type');
        $pcl->attorney_name = $req->input('attorney_name');
        $pcl->attorney_cc = $req->input('attorney_cc');
        $pcl->sender = $req->input('sender');
        $pcl->applicant = $req->input('applicant');
        $pcl->dominance = $req->input('dominance');
        $pcl->required_support = $req->input('required_support');
        $pcl->previous_pcl = $req->input('previous_pcl');
        $pcl->description = $req->input('description');
        $pcl->require_3rd = $req->input('require_3rd');
        $pcl->require_3rd_dk = $req->input('require_3rd_dk');
        $pcl->require_device = $req->input('require_device');

        $pcl->request_reason = $req->input('request_reason');
        $pcl->sender_name = $req->input('sender_name');
        $pcl->sender_doc = $req->input('sender_doc');
        $pcl->sender_phone = $req->input('sender_phone');
        $pcl->sender_address = $req->input('sender_address');
        $pcl->sender_email = $req->input('sender_email');
        $pcl->sender_city = $req->input('sender_city');
        $pcl->sinister_date = $req->input('sinister_date');
        $pcl->effective_notify_date = $req->input('effective_notify_date');

        $pcl->ph_medical = $req->input('ph_medical');
        $pcl->ph_surgical = $req->input('ph_surgical');
        $pcl->ph_toxic = $req->input('ph_toxic');
        $pcl->ph_medicines = $req->input('ph_medicines');
        $pcl->ph_gynecobotters = $req->input('ph_gynecobotters');
        $pcl->ph_traumatic = $req->input('ph_traumatic');
        $pcl->ph_family = $req->input('ph_family');

        $pcl->doc_clinical_history = $req->input('doc_clinical_history');
        $pcl->doc_epicrisis = $req->input('doc_epicrisis');
        $pcl->doc_paraclinic = $req->input('doc_paraclinic');
        $pcl->doc_prehiring = $req->input('doc_prehiring');
        $pcl->doc_periodics = $req->input('doc_periodics');
        $pcl->doc_post_occupational = $req->input('doc_post_occupational');
        $pcl->doc_health_concept = $req->input('doc_health_concept');
        $pcl->doc_others = $req->input('doc_others');

        $pcl->laborally_active = $req->input('laborally_active');
        $pcl->use_laboral_role = $req->input('use_laboral_role');
        $pcl->use_laboral_role_description = $req->input('use_laboral_role_description');
        $pcl->role_restriction = $req->input('role_restriction');
        $pcl->economic_self = $req->input('economic_self');
        $pcl->role_category = $req->input('role_category');
        $pcl->monetary_value = $req->input('monetary_value');

        $pcl->role = $req->input('role');
        $pcl->sinister_number = $req->input('sinister_number');
        $pcl->pension_review = $req->input('pension_review');
        $pcl->catastrophic = $req->input('catastrophic');
        $pcl->details = $req->input('details');
        $pcl->board = $req->input('board');
        $pcl->structuring_at = $req->input('structuring_at_submit');

        if ($pcl->pcl_type == 3) {
            $pcl->doc_at = $req->input('doc_at');
            $pcl->doc_apt = $req->input('doc_apt');
            $pcl->ponencia = $req->input('ponencia');
        }

        $pcl->structuring_support = $req->input('structuring_support');
        $pcl->origin = $req->input('origin');
        $pcl->event_type = $req->input('event_type');
        $pcl->event_date = $req->input('event_date_submit');
        $pcl->concept_technical_audit = $req->input('concept_technical_audit');

        $diagnostics = $req->input('diagnostics');

        if (isset($diagnostics['id']) && is_array($diagnostics['id'])) {
            for ($i = 0; $i < count($diagnostics['id']); $i++) {
                if ($diagnostics['cod'][$i] != null) {
                    $pcl_diagnostic = new PclDiagnostic;
                    $pcl_diagnostic->code = newCodeCie10($diagnostics['cod'][$i]);
                    $pcl_diagnostic->description = $diagnostics['description'][$i];
                    $pcl_diagnostic->origin = $diagnostics['origin'][$i];
                    if ($pcl->pcl_type == 3) {
                        $pcl_diagnostic->laterality = $diagnostics['laterality'][$i];
                    } else {
                        $pcl_diagnostic->deficiences = $diagnostics['deficiences'][$i];
                    }
                    $pcl->diagnostics->add($pcl_diagnostic);
                }
            }
        }

        $sends = $req->input('sends');
        if (isset($sends['id']) && is_array($sends['id'])) {
            for ($i = 0; $i < count($sends['id']); $i++) {
                if ($sends['id'][$i] != null) {
                    $pcl_sends = new PclSends();
                    $pcl_sends->entity_type = $sends['entity_type'][$i];
                    $pcl_sends->send_method = $sends['send_method'][$i];
                    $pcl_sends->guide_email = $sends['guide_email'][$i];
                    $pcl_sends->send_state = $sends['send_state'][$i];
                    $pcl_sends->effective_notify_date = $sends['effective_notify_date'][$i];
                    $pcl->pcl_sends->add($pcl_sends);
                }
            }
        }

        $interconsultations = $req->input('interconsultations');
        if (isset($interconsultations['id']) && is_array($interconsultations['id'])) {
            for ($i = 0; $i < count($interconsultations['id']); $i++) {
                if ($interconsultations['exam_name'][$i] != null) {
                    $pcl_interconsultation = new PclInterconsultation;
                    $pcl_interconsultation->exam_profesional = $interconsultations['exam_profesional'][$i];
                    $pcl_interconsultation->exam_name = $interconsultations['exam_name'][$i];
                    $pcl_interconsultation->exam_result = $interconsultations['exam_result'][$i];
                    if (isset($interconsultations['exam_date'][2 * $i + 1])) {
                        $pcl_interconsultation->exam_date = $interconsultations['exam_date'][2 * $i + 1];
                    }
                    $pcl->interconsultations->add($pcl_interconsultation);
                }
            }
        }
        if ($pcl->pcl_type == 3) {
            $deficiences = $req->input('deficiences');
            for ($i = 0; $i < count($deficiences['table']); $i++) {
                if ($deficiences['table'][$i] != null) {
                    $pcl_deficience = new PclDeficience;
                    $pcl_deficience->table = $deficiences['table'][$i];
                    $pcl_deficience->name = $deficiences['name'][$i];
                    $pcl_deficience->percentage = $deficiences['percentage'][$i];

                    $pcl->deficiences->add($pcl_deficience);
                }
            }


            $work_history = $req->input('work_history');
            for ($i = 0; $i < count($work_history['id']); $i++) {
                if ($work_history['company'][$i] != null) {
                    $pcl_work_history = new PclWorkHistory;
                    $pcl_work_history->company = $work_history['company'][$i];
                    $pcl_work_history->position = $work_history['position'][$i];
                    $pcl_work_history->risk_factor = $work_history['risk_factor'][$i];
                    $pcl_work_history->exposition_time = $work_history['exposition_time'][$i];

                    $pcl->work_history->add($pcl_work_history);
                }
            }

            $pcl->addTableValues('T90701', $req->input('T90701'));

            for ($i = 0; $i < 10; $i++) {
                $pcl_table_data = new PclTableData;
                $pcl_table_data->table = 'T90701';
                $pcl_table_data->key = $i;
                $pcl_table_data->value = $req->input("T90701.{$i}");
                $pcl->table_datas->add($pcl_table_data);
            }

            $pcl->addTableValues('T90702', $req->input('T90702'));
            for ($i = 0; $i < 10; $i++) {
                $pcl_table_data = new PclTableData;
                $pcl_table_data->table = 'T90702';
                $pcl_table_data->key = $i;
                $pcl_table_data->value = $req->input("T90702.{$i}");
                $pcl->table_datas->add($pcl_table_data);

            }

            $pcl->addTableValues('T90703', $req->input('T90703'));
            for ($i = 0; $i < 10; $i++) {
                $pcl_table_data = new PclTableData;
                $pcl_table_data->table = 'T90703';
                $pcl_table_data->key = $i;
                $pcl_table_data->value = $req->input("T90703.{$i}");
                $pcl->table_datas->add($pcl_table_data);

            }

            $pcl->addTableValues('T90704', $req->input('T90704'));
            for ($i = 0; $i < 10; $i++) {
                $pcl_table_data = new PclTableData;
                $pcl_table_data->table = 'T90704';
                $pcl_table_data->key = $i;
                $pcl_table_data->value = $req->input("T90704.{$i}");
                $pcl->table_datas->add($pcl_table_data);

            }

            $pcl->addTableValues('T90705', $req->input('T90705'));
            for ($i = 0; $i < 10; $i++) {
                $pcl_table_data = new PclTableData;
                $pcl_table_data->table = 'T90705';
                $pcl_table_data->key = $i;
                $pcl_table_data->value = $req->input("T90705.{$i}");
                $pcl->table_datas->add($pcl_table_data);

            }

            $pcl->addTableValues('T90706', $req->input('T90706'));
            for ($i = 0; $i < 10; $i++) {
                $pcl_table_data = new PclTableData;
                $pcl_table_data->table = 'T90706';
                $pcl_table_data->key = $i;
                $pcl_table_data->value = $req->input("T90706.{$i}");
                $pcl->table_datas->add($pcl_table_data);

            }

            $pcl->addTableValues('T90707', $req->input('T90707'));
            for ($i = 0; $i < 10; $i++) {
                $pcl_table_data = new PclTableData;
                $pcl_table_data->table = 'T90707';
                $pcl_table_data->key = $i;
                $pcl_table_data->value = $req->input("T90707.{$i}");
                $pcl->table_datas->add($pcl_table_data);
            }

            $l = 0;
            $pcl->addTableValues('T90711', $req->input('T90711'));
            $pcl_table_data = new PclTableData;
            $pcl_table_data->table = 'T90711';
            $pcl_table_data->key = $l;
            $pcl_table_data->value = $req->input("T90711.{$l}");
            $pcl->table_datas->add($pcl_table_data);

            $pcl->addTableValues('T90712', $req->input('T90712'));
            $pcl_table_data = new PclTableData;
            $pcl_table_data->table = 'T90712';
            $pcl_table_data->key = $l;
            $pcl_table_data->value = $req->input("T90712.{$l}");
            $pcl->table_datas->add($pcl_table_data);

            $pcl->addTableValues('T90713', $req->input('T90713'));
            $pcl_table_data = new PclTableData;
            $pcl_table_data->table = 'T90713';
            $pcl_table_data->key = $l;
            $pcl_table_data->value = $req->input("T90713.{$l}");
            $pcl->table_datas->add($pcl_table_data);

            $pcl->addTableValues('T90714', $req->input('T90714'));
            $pcl_table_data = new PclTableData;
            $pcl_table_data->table = 'T90714';
            $pcl_table_data->key = $l;
            $pcl_table_data->value = $req->input("T90714.{$l}");
            $pcl->table_datas->add($pcl_table_data);

            $pcl->addTableValues('T90715', $req->input('T90715'));
            $pcl_table_data = new PclTableData;
            $pcl_table_data->table = 'T90715';
            $pcl_table_data->key = $l;
            $pcl_table_data->value = $req->input("T90715.{$l}");
            $pcl->table_datas->add($pcl_table_data);

            $pcl->addTableValues('T90716', $req->input('T90716'));
            $pcl_table_data = new PclTableData;
            $pcl_table_data->table = 'T90716';
            $pcl_table_data->key = $l;
            $pcl_table_data->value = $req->input("T90716.{$l}");
            $pcl->table_datas->add($pcl_table_data);

            $pcl->addTableValues('T90717', $req->input('T90717'));
            $pcl_table_data = new PclTableData;
            $pcl_table_data->table = 'T90717';
            $pcl_table_data->key = $l;
            $pcl_table_data->value = $req->input("T90717.{$l}");
            $pcl->table_datas->add($pcl_table_data);

        } else {
            $deficiences = $req->input('deficiences');
            if ($deficiences !== null) {
                for ($i = 0; $i < count($deficiences['table']); $i++) {
                    if ($deficiences['table'][$i] != null) {
                        $pcl_deficience = new PclDeficience;
                        $pcl_deficience->table = $deficiences['table'][$i];
                        $pcl_deficience->name = $deficiences['name'][$i];
                        $pcl_deficience->percentage = $deficiences['percentage'][$i];
                        $pcl_deficience->class = $deficiences['class'][$i];
                        $pcl_deficience->cfpfu = $deficiences['cfpfu'][$i];
                        $pcl_deficience->cfm1 = $deficiences['cfm1'][$i];
                        $pcl_deficience->cfm2 = $deficiences['cfm2'][$i];
                        $pcl_deficience->cfm3 = $deficiences['cfm3'][$i];
                        $pcl_deficience->class_final = $deficiences['class_final'][$i];
                        $pcl_deficience->cat = $deficiences['cat'][$i];
//                        $pcl_deficience->ear = ($deficiences['ears'][$i] == -1) ? 0 : 1;
                        $pcl_deficience->dominant = ($deficiences['dominantx'][$i] == -1) ? 0 : 1;

                        $pcl->deficiences->add($pcl_deficience);
                    }
                }
            }

            $pcl->addTableValues('T6', $req->input('T6'));
            for ($i = 0; $i < 10; $i++) {
                $pcl_table_data = new PclTableData;
                $pcl_table_data->table = 'T6';
                $pcl_table_data->key = $i;
                $pcl_table_data->value = $req->input("T6.{$i}");
                $pcl->table_datas->add($pcl_table_data);
            }

            $pcl->addTableValues('T7', $req->input('T7'));
            for ($i = 0; $i < 10; $i++) {
                $pcl_table_data = new PclTableData;
                $pcl_table_data->table = 'T7';
                $pcl_table_data->key = $i;
                $pcl_table_data->value = $req->input("T7.{$i}");
                $pcl->table_datas->add($pcl_table_data);
            }

            $pcl->addTableValues('T8', $req->input('T8'));
            for ($i = 0; $i < 10; $i++) {
                $pcl_table_data = new PclTableData;
                $pcl_table_data->table = 'T8';
                $pcl_table_data->key = $i;
                $pcl_table_data->value = $req->input("T8.{$i}");
                $pcl->table_datas->add($pcl_table_data);
            }

            $pcl->addTableValues('T9', $req->input('T9'));
            for ($i = 0; $i < 10; $i++) {
                $pcl_table_data = new PclTableData;
                $pcl_table_data->table = 'T9';
                $pcl_table_data->key = $i;
                $pcl_table_data->value = $req->input("T9.{$i}");
                $pcl->table_datas->add($pcl_table_data);
            }

            $pcl->addTableValues('T10', $req->input('T10'));
            for ($i = 0; $i < 10; $i++) {
                $pcl_table_data = new PclTableData;
                $pcl_table_data->table = 'T10';
                $pcl_table_data->key = $i;
                $pcl_table_data->value = $req->input("T10.{$i}");
                $pcl->table_datas->add($pcl_table_data);
            }
        }
        $pcl_dates = Pcl::query()
            ->where('activity_id', $activity->id)
            ->firstOrFail();
        $pcl_date = $pcl_dates->pcl_date;
        $req_date = $pcl_dates->req_date;
        $pdf = PDF::loadView('services.pcl.docs.annex_preform_rejection_pdf', ['pcl' => $pcl, 'activity' => $activity, 'pcl_date' => $pcl_date, 'req_date' => $req_date, 'watermark' => true]);
        return $pdf->stream('preview.pdf');
    }

    public function getDepartmentAndMunicipalityByCode($code)
    {
        $jsonString = file_get_contents(public_path('js/colombia.json'));
        $jsonArray = json_decode($jsonString, true);

        foreach ($jsonArray as $department) {
            foreach ($department['municipalities'] as $municipality) {
                if ($municipality['code'] === intval($code)) {
                    return [
                        'department_code' => $department['code'],
                        'department_name' => $department['name'],
                        'municipality_code' => $municipality['code'],
                        'municipality_name' => $municipality['name'],
                    ];
                }
            }
        }

        return [
            'department_code' => 0,
            'department_name' => 'Sin dato',
            'municipality_code' => 0,
            'municipality_name' => 'Sin dato',
        ];
    }


    public function replaceTextAreas(Request $req)
    {
        $model = new Pcl;
        $model->replaceTextInColumns();
    }

    public function replaceTextAreasRelations(Request $req)
    {
        $model = new Pcl;
        $model->replaceTextInColumnRelations();
    }

}
