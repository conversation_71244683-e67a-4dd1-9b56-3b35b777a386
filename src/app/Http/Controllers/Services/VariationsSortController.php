<?php

namespace App\Http\Controllers\Services;

use App\Action;
use App\Activity;
use App\ActivityAction;
use App\ActivityActionDocument;
use App\ActivityDocument;
use App\Client;
use App\Http\Controllers\Controller;
use App\Mail\SendDocumentDataBase;
use App\MailTemplates\Constants\Senders;
use App\MailTemplates\Constants\Templates;
use App\MailTemplates\TemplateBuilder;
use App\PolicySort;
use App\ServiceDocument;
use App\State;
use App\User;
use App\Utilities\Utilities;
use App\VariationsSort;
use App\PolicySortCollection;
use App\Affiliate;
use App\Http\Controllers\ActionController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use PDF;
use App\Service;
use Exception;
use Illuminate\Support\Carbon;
use DateTime;
use App\PolicyContact;
use App\Holiday;
use App\Http\Controllers\Tables\MailBoardController;
use App\Providers\AppServiceProvider;

class VariationsSortController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth')->except([
            'cronReportDetentionResponse'
        ]);
    }

    public function form(Request $request, $cpath, $id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();

        $activity = Activity::with(['affiliate', 'state', 'activity_actions.action', 'variations_sort'])
            ->where('client_id', $client->id)
            ->where('id', $id)
            ->firstOrFail();


        $activityPolicy = Activity::with(['affiliate', 'state', 'policy_sort'])
            ->where('id', $activity->parent_id)->first();

        $policySort = PolicySort::where('activity_id', $activity->parent->id)->first();
        $activityAction = ActivityAction::where('activity_id', $id)->first();
        $action = Action::where('id', $activity->variations_sort->variation_type)->first();


        $activity_documents = Activity::with(['activity_actions.documents', 'state', 'activity_actions.action'])
            ->where('client_id', $client->id)
            ->where('id', $id)
            ->whereHas('activity_actions', function ($query) use ($action) {
                $query->where('action_id', $action->id);
            })
            ->get();


        $isotros_datos = $action->id == Action::SOLICITAR_ACTUALIZACION_OTROS_DATOS;
        if ($isotros_datos) {
            $policyContacts = PolicyContact::where('policy_sort_id', $policySort->id)
                ->get();
        }


        //Aquí traemos datos del afiliado y de poliza sort por si es necesario pintar algún dato requerido
        return view('services.variations_sort.form.form', [
            'activity' => $activity,
            'policySort' => $policySort,
            'activityPolicy' => $activityPolicy,
            'activityAction' => $activityAction,
            'documentos' => $activity_documents,
            'action' => $action,
            'id' => $id,
            'policy_contacts' => $isotros_datos ? $policyContacts : null
        ]);
    }

    //Metodo para enviar correos cuando la acción lo requiera
    public function indexTabla($cpath, Request $request)
    {

        // Recoger los parámetros del formulario
        $stateIds = $request->input('state_ids');


        $states = State::with('services')->whereBetween('id', [24, 36])->get();
        $client = Client::where('path', $cpath)->firstOrFail();

        $activityQuery = Activity::with([
            'parent_activity.policy_sort',
            'affiliate',
            'state',
            'variations_sort.action'
        ])
            ->where('service_id', Service::SERVICE_VARIATIONS_SORT_MNK)
            ->where('client_id', $client->id)
            ->orderBy('created_at', 'desc');


        if (!empty($stateIds)) {
            $activityQuery->whereIn('state_id', $stateIds);
        }

        // Obtener los resultados
        $activity = $activityQuery->paginate(10);


        return view('services.variations_sort.table.table', ['variations' => $activity, 'states' => $states, 'client' => $client]);
    }


    public function resendEmail(Request $req, $activity, $activity_service, $activityAction, $emails, $variation_id, $subject, $client_id, $body = '', $documentos = [], $sender = 'MNK Seguros')
    {
        $files = $documentos;
        $paths = array();
        $text = $body;
        $document = 'soporte_radicacion_solicitud_variacion';

        $variations = DB::table('variations_sort')->where('id', '=', $variation_id)->first();

        switch ($activityAction->action_id) {
            case Action::REPORTAR_APROBACION_CANCELACION:
                $pdf = PDF::loadView("services.plantilla.docs.poliza_cancelada", ['variations' => $variations, 'watermark' => false]);

                break;
            case Action::REPORTAR_RECHAZO_VARIACION_DE_LA_POLIZA:
                $pdf = PDF::loadView("services.plantilla.docs.carta_motivo_rechazo", ['variationSort' => $variations, 'watermark' => false]);

                break;

            default:
                $pdf = PDF::loadView("services.plantilla.docs.{$document}_pdf", ['variations' => $variations, 'watermark' => false, 'type_variation' => $activityAction->action->name]);
                break;
        }
        $document = ucfirst(mb_strtolower($activityAction->action->name));


        //Cargamos el archivo en S3
        Storage::disk('s3')
            ->put("activity_action_document/{$document}_{$activityAction->id}.pdf", $pdf->output());

        //guardamos en activity_action_documents
        $activityActionDocument = new ActivityActionDocument();
        $activityActionDocument->activity_action_id = $activityAction->id;
        $activityActionDocument->name = $document;
        $activityActionDocument->path = "activity_action_document/{$document}_{$activityAction->id}.pdf";
        $activityActionDocument->save();

        //formamos archivo
        $files[] = [
            'type' => 'pdf',
            'path' => "activity_action_document/{$document}_{$activityAction->id}.pdf",
            'name' => $document . '.pdf',
        ];

        //dirección path
        $paths[] = "activity_action_document/{$document}_{$activityAction->id}.pdf";

        //Logica para envio de correo
        if ($emails != null) {
            $emails = array_map('trim', explode(',', $emails));

            $affiliate_files = [];
            foreach ($files as $file) {
                if (!(strpos($file['path'], 'affiliate_employer') !== false)) {
                    $affiliate_files[] = $file;
                }
            }

            $mailSent = new SendDocumentDataBase(
                implode(',', $emails),
                $subject,
                "<EMAIL>",
                "Solicitud variación",
                [
                    "text" => $text,
                    "sender" => $sender
                ],
                "<EMAIL>",
                $files,
                "send_document_db",
                $client_id,
                $req->getHost(),
                $activity,
                $activityAction->id,
                $activity_service
            );
            
            //Actividad de la variación
            $activity = Activity::where('id', $activity)->first();

            // Capturar el resultado del envío
            $result = $mailSent->sendMail();

            //Registramos los datos del correo enviado para la trazabilidad
            $mailBoardController = new MailBoardController();
                $mailBoardController->createRegisterMail(
                $activity->id,
                Service::SERVICE_VARIATIONS_SORT_MNK, 
                $activity->parent->policy_sort->consecutive, 
                'Tomador', 
                mb_convert_case(mb_strtolower($activity->parent->affiliate->full_name ?? ''), MB_CASE_TITLE, "UTF-8"), 
                $activity->parent->affiliate->doc_number, 
                $subject, 
                $text,
                $emails, 
                $result,
                null
            );
        }
    }


    private function bodyTemplateEmail($value, $activity, $variation = null)
    {
        $text = '';
        $url_env = config('app.url');
        $documents = AppServiceProvider::$S3_DOCUMENTS_MNK;
        $pdf_condiciones_generales_url = $documents['condiciones_generales'];
        $pdf_norma_tecnica_url = $documents['norma_tecnica'];
        $tomador = mb_convert_case(mb_strtolower($activity->affiliate->first_name ?? ''), MB_CASE_TITLE, "UTF-8");
        $poliza = $activity->policy_sort;
        $user = User::where('affiliate_id', $activity->affiliate_id)->first();
        $user_name = isset($user) ? $user->username : '';
        switch ($value) {
            case 'APROBADA':
                $text = TemplateBuilder::build(
                    Templates::PROCESSED_VARIATION,
                    [
                        'name' => $tomador ?? '',
                        'policy_sort' => $poliza->formatNumberConsecutive()
                    ]
                )['body'];
                break;
            case 'REHABILITACION':
                $text = "¡Buen día, $tomador!

                      Nos complace informarle que hemos procesado satisfactoriamente la rehabilitación de su póliza #{$poliza->formatNumberConsecutive()}.
                      
                      Le invitamos a verificar la siguiente información importante:
                      
                         ·Condiciones particulares de su seguro.
                         ·Norma técnica, <a href='{$pdf_norma_tecnica_url}'>aquí</a>.
                         ·Condiciones generales de su póliza, a las cuales puede acceder <a href='{$pdf_condiciones_generales_url}'>aquí</a>
                           
                      Además, le recordamos que para realizar sus reportes de planillas, inclusiones o accidentes, por favor, debe ingresar <a href='{$url_env}'>aquí</a> con su usuario y contraseña.
                      
                      Su usuario y contraseña son los siguientes:
                      
                      Usuario: $user_name
                      Contraseña: *****
                        
                      Nos sentimos sumamente honrados y agradecidos por la confianza que ha depositado en nosotros. Nuestro propósito es transformar la protección en una experiencia ágil, confiable y humana.";
                break;
            case 'RECEPCION':
                $text = TemplateBuilder::build(
                    Templates::RECEIPT_OF_VARIATION,
                    [
                        'name' => $tomador ?? '',
                        'policy_sort' => $poliza->formatNumberConsecutive(),
                        'variation_id' => $variation->id,
                    ]
                )['body'];
                break;
            case 'DETENCIÓN': // Solo para detención retorna subject y body
                $text = TemplateBuilder::build(
                    Templates::POLICYHOLDER_PENDING_PROCESS_REQUIREMENTS,
                    [
                        'procedure_number' => $variation->id,
                        'name' => $tomador ?? '',
                        'management' => $variation->observation_reject ?? '',
                    ]
                );
                break;

            default:

                break;
        }

        return $text;
    }

    private function _bodyTemplateEmailIntermediary($value, $activity, $intermediary, $variation)
    {
        $template = null;
        $policySort = $activity->policy_sort;
        switch ($value) {
            case 'APROBADA':
            case 'REHABILITACION':
                $template = TemplateBuilder::build(
                    Templates::APPROVED_PROCESS_FOR_INTERMEDIARY,
                    [
                        'procedure_number' => $variation->id,
                        'name' => $intermediary->first_name ?? ''
                    ]
                );
                break;
            case 'RECHAZO':
                $template = TemplateBuilder::build(
                    Templates::PROCESS_REJECTION_FOR_INTERMEDIARY,
                    [
                        'procedure_number' => $variation->id,
                        'name' => $intermediary->first_name ?? '',
                        'management' => $variation->observation_reject ?? '',
                    ]
                );
                break;
            case 'DETENCIÓN':
                $template = TemplateBuilder::build(
                    Templates::INTERMEDIARY_PENDING_PROCESS_REQUIREMENTS,
                    [
                        'procedure_number' => $variation->id,
                        'name' => $intermediary->first_name ?? '',
                        'management' => $variation->observation_reject ?? '',
                    ]
                );
                break;
            default:
                break;
        }

        return $template;
    }

    //Generar documentos masivos del servicio
    public function massiveDocumentoService($req, $activity, $action, $activityAction, $services_id)
    {
        // Obtener los documentos (tipos) y archivos
        $documentos = $req->input('documentos'); // Tipos de documentos
        $archivos = $req->file('documentos'); // Archivos subidos

        $activityActionDoc = new ActivityAction();
        $activityActionDoc->activity_id = $activity->id;
        $activityActionDoc->action_id = $action;
        $activityActionDoc->old_user_id = $activity->user_id;
        $activityActionDoc->new_user_id = Auth::id();
        $activityActionDoc->description = "Cargue de documentos variaciones tomador";
        $activityActionDoc->old_state_id = $activity->state_id;
        $activityActionDoc->new_state_id = $activity->state_id;
        if (!$activityActionDoc->new_state_id) {
            $activityActionDoc->new_state_id = $activity->state_id;
        }
        $activityActionDoc->author_id = Auth::id() ? Auth::id() : 1;
        $activityActionDoc->save();


        // Recorrer cada documento y su archivo correspondiente
        foreach ($documentos as $index => $documento) {
            $tipo = $documento['tipo']; // Obtener el tipo de documento
            $archivo = $archivos[$index]['archivo']; // Obtener el archivo correspondiente

            // Verificar si el archivo existe
            if ($archivo) {
                // Obtener la extensión original del archivo
                $extension = $archivo->getClientOriginalExtension();
                // Generar un nombre de archivo único usando el tipo y una identificación (opcional
                $fileName = "activity_action_document/{$tipo}_" . uniqid() . ".{$extension}";
                // Subir el archivo a S3 usando el disco 's3'
                Storage::disk('s3')->put($fileName, file_get_contents($archivo));
                // Guardar la ruta del archivo o hacer otras operaciones
                $path = $fileName;

                $service_doc = ServiceDocument::where('service_id', $services_id)
                    ->where('code', $tipo)
                    ->first();

                $activityActionDocument = new ActivityActionDocument();
                $activityActionDocument->activity_action_id = $activityActionDoc->id;
                $activityActionDocument->name = $service_doc ? $service_doc->name : null;
                $activityActionDocument->path = $path;
                $activityActionDocument->save();
            }
        }
    }

    // View pdf REHABILITACION
    public function viewPdfRehabilitacion()
    {
    }

    //Guardar servicio de VARIACIONES SORT
    public function save(Request $req, $cpath, $id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();

        //Buscamos la actividad asociada que viene por parametro
        $activity = Activity::where('client_id', $client->id)
            ->where('id', $id)
            ->firstOrFail();

        DB::beginTransaction();
        try {
            if (!$activity->variation) {
                $variation = new VariationsSort();
                $variation->activity_id = $id;
            } else {
                $variation = $activity->variation;
            }
            $variation->save();
            DB::commit();
        } catch (Exception $e) {
            DB::rollback();
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
            ], 500);
        }

        return redirect("servicio/$id/variations_sort");
    }

    //PDF de vista previa de variaciones
    public function pdf(Request $req, $cpath, $id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();

        $activity = Activity::where('client_id', $client->id)
            ->where('id', $id)->firstOrFail();

        $quotation = new VariationsSort();
        $quotation->activity_id = $id;
        $quotation->setRelation('activity', $activity);
        $quotation->created_at = new \DateTime();

        $pdf = \PDF::loadView('services.quotation.docs.quotation_pdf', [
            'quotation' => $quotation,
            'watermark' => false,
            'activity' => $activity
        ]);

        return $pdf->stream('preview.pdf');
    }


    //Casos para redirigir a las acciones y estados correctos VARIACIONES SORT
    //CASOS de acciones para vista del TOMADOR

    public function actionStatus(Request $req, $cpath)
    {
        $variationType = intVal($req->variation_type);

        switch ($variationType) {
            //Acción SOLICITAR_CAMBIO_VIGENCIA_DE_POLIZA
            case Action::SOLICITAR_CAMBIO_VIGENCIA_DE_POLIZA:
                return $this->actionChangePolicyValidity($req, $cpath);
                break;

            //Accion SOLICITAR_ACTUALIZACION_DATOS_DE_CONTACTO
            case Action::SOLICITAR_ACTUALIZACION_DATOS_DE_CONTACTO:
                return $this->actionUpdateDataContact($req, $cpath);
                break;

            //Accion SOLICITAR_ACTUALIZACION_OTROS_DATOS
            case Action::SOLICITAR_ACTUALIZACION_OTROS_DATOS:
                return $this->actionUpdatedOtherData($req, $cpath);
                break;

            //Acción SOLICITAR_CANCELACION
            case Action::SOLICITAR_CANCELACION:
                return $this->actionCancell($req, $cpath);
                break;

            //Acción SOLICITAR_REHABILITACION
            case Action::SOLICITAR_REHABILITACION:
                return $this->actionRehabilitation($req, $cpath);
                break;

            //Acción SOLICITAR_CAMBIO_FORMA_DE_PAGO
            case Action::SOLICITAR_CAMBIO_FORMA_DE_PAGO:
                return $this->actionChangePaymentMethod($req, $cpath);
                break;
            default;

                //FIN CASOS de acciones para vista del TOMADOR
        }
    }

    public function actionStatusReport(Request $req, $cpath)
    {
        //Casos para Acciones de REPORTES variaciones SORT VISTA ADMINISTRATIVO
        $variationType = intVal($req->variation_type);

        switch (intVal($variationType)) {
            //Acción REPORTAR_APROBACION_CAMBIO_VIGENCIA_DE_POLIZA
            case Action::REPORTAR_APROBACION_CAMBIO_VIGENCIA_DE_POLIZA:
                return $this->reportChangeDurationPolicy($req, $cpath);
                break;

            //Acción REPORTAR_APROBACION_ACTUALIZACION_OTROS_DATOS
            case Action::REPORTAR_APROBACION_ACTUALIZACION_OTROS_DATOS:
                return $this->actionReportApprovalUpdateOtherData($req, $cpath);
                break;

            //Acción REPORTAR_APROBACION_ACTUALIZACION_DATOS_DE_CONTACTO
            case Action::REPORTAR_APROBACION_ACTUALIZACION_DATOS_DE_CONTACTO:
                return $this->reportUpdateDataContact($req, $cpath);
                break;

            //Acción REPORTAR_RECHAZO_VARIACION_DE_LA_POLIZA
            case Action::REPORTAR_RECHAZO_VARIACION_DE_LA_POLIZA:
                return $this->reportRejectionPolicyvariation($req, $cpath);
                break;

            //Acción REPORTAR_DETENCION_VARIACION_DE_LA_POLIZA
            case Action::REPORTAR_DETENCION_VARIACION_DE_LA_POLIZA:
                return $this->reportArrestPolicyVariation($req, $cpath);
                break;

            //Acción REPORTAR_APROBACION_CAMBIO_FORMA_DE_PAGO
            case Action::REPORTAR_APROBACION_CAMBIO_FORMA_DE_PAGO:
                return $this->reportChangePaymentMethod($req, $cpath);
                break;

            //Acción REPORTAR_APROBACION_CANCELACION
            case Action::REPORTAR_APROBACION_CANCELACION:
                return $this->reportAprovalCancellation($req, $cpath);
                break;

            //Acción REPORTAR_APROBACION_REHABILITACION
            case Action::REPORTAR_APROBACION_REHABILITACION:
                return $this->reportRehabilitation($req, $cpath);
                break;

            default:

                break;
        }
    }

    //ACCIONES VISTA TOMADOR
    //Acción solicitar cambio de vigencia de póliza en Variation SORT
    public function actionChangePolicyValidity(Request $req, $cpath)
    {
        //Buscamos el objeto de poliza mediante su activity_id
        $activityPoliza = Activity::with(['affiliate'])
            ->where('id', $req->input('activity_id'))
            ->firstOrFail();

        //Buscamos el cliente
        $client = Client::where('path', $cpath)
            ->firstOrFail();

        DB::beginTransaction();

        try {
            //1. creamos la activity de la variacion  con el parent id de la poliza, deberia llegar por parametro el id de la poliza
            $activity = new Activity;

            $activity->parent_id = $req->input('activity_id');
            $activity->client_id = $client->id;
            $activity->affiliate_id = $activityPoliza->affiliate_id;
            $activity->service_id = Service::SERVICE_VARIATIONS_SORT_MNK;
            $activity->state_id = $activityPoliza->state_id;
            $activity->user_id = Auth::id();
            $activity->state_id = State::REGISTRADO;
            $activity->save();


            //2. guardamos los datos de la variación y ejecutamos los demas pasos.
            $variation = new VariationsSort;
            $variation->activity_id = $activity->id;
            $variation->reason = $req->reason;
            $variation->description = $req->description;
            $variation->variation_type = $req->variation_type;
            $variation->new_start_date = $req->new_start_date;
            $variation->intermediary_user = Utilities::getIntermediaryVariation($req);
            $variation->save();


            $description = $req->input('description');

            $activityAction = ActionController::create(
                $activity->id,
                $req->variation_type,
                $description
            );


            if ($activityPoliza->state_id != State::POLIZA_SUSPENDIDA) {
                ActionController::create(
                    $activityPoliza->id,
                    Action::REPORTAR_SOLICITUD_VARIACION,
                    $description
                );
            } else {
                throw new Exception("La acción \"REPORTAR SOLICITUD VARIACIÓN\", no se puede ejecutar porque el servicio \"{$activityPoliza->service->name}\" se encuentra en el estado actual:\"{$activityPoliza->state->name}\" .", 400);
            }

            $subject = "Solicitud de variación de la póliza {$activityPoliza->policy_sort->formatNumberConsecutive()}";
            $body = $this->bodyTemplateEmail('RECEPCION', $activityPoliza, $variation);

            // //Generar soporte y envio de correo
            $this->resendEmail(
                $req,
                $activity->id,
                Service::SERVICE_VARIATIONS_SORT_MNK,
                $activityAction,
                $activityPoliza->affiliate->email,
                $variation->id,
                $subject,
                $client,
                $body,
                [],
                Senders::MNK_INSURANCE
            );

            //Generamos la subida de los documentos masivos
            $this->massiveDocumentoService(
                $req,
                $activity,
                Action::SOLICITAR_CAMBIO_VIGENCIA_DE_POLIZA,
                $activityAction,
                Service::SERVICE_VARIATIONS_SORT_MNK
            );

            DB::commit();
            return response()->json('Solicitud exitosa', 200);
        } catch (Exception $e) {
            DB::rollback();
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    //Acción para SOLICITAR_ACTUALIZACION_DATOS_DE_CONTACTO en variacion SORT
    public function actionUpdateDataContact(Request $req, $cpath)
    {
        $activityPoliza = Activity::where('id', $req->input('activity_id'))
            ->firstOrFail();

        //Buscamos el cliente
        $client = Client::where('path', $cpath)
            ->firstOrFail();

        DB::beginTransaction();

        try {
            //1. creamos la activity de la variacion  
            //con el parent id de la poliza, deberia llegar por parametro el id de la poliza
            $activity = new Activity;
            $activity->parent_id = $req->input('activity_id');
            $activity->client_id = $client->id;
            $activity->affiliate_id = $activityPoliza->affiliate_id;
            $activity->service_id = Service::SERVICE_VARIATIONS_SORT_MNK;
            $activity->state_id = State::REGISTRADO;
            $activity->user_id = Auth::id();
            $activity->save();

            //2. guardamos los datos de la variación y ejecutamos los demas pasos.
            $variation = new VariationsSort;
            $variation->activity_id = $activity->id;
            $variation->reason = $req->reason;
            $variation->description = $req->description;
            $variation->variation_type = $req->variation_type;
            $variation->new_start_date = $req->new_start_date;
            $variation->intermediary_user = Utilities::getIntermediaryVariation($req);
            $variation->save();

            //3. Ejecutamos la acción SOLICITAR_ACTUALIZACION_DATOS_DE_CONTACTO sobre la variación
            $description = $req->input('description');
            $activityAction = ActionController::create(
                $activity->id,
                $req->variation_type,
                $description
            );

            //4. Ejecutamos la acción REPORTAR_SOLICITUD_VARIACION en la poliza sort
            //Los estados no cambian en esta acción

            if ($activityPoliza->state_id != State::POLIZA_SUSPENDIDA) {
                ActionController::create($activityPoliza->id, Action::REPORTAR_SOLICITUD_VARIACION, $description);
            } else {
                throw new Exception("La acción \"REPORTAR SOLICITUD VARIACIÓN\", no se puede ejecutar porque el servicio \"{$activityPoliza->service->name}\" se encuentra en el estado actual:\"{$activityPoliza->state->name}\" .", 400);
            }


            $emails = $activity->affiliate->email;


            $subject = "Solicitud de variación de la póliza {$activityPoliza->policy_sort->formatNumberConsecutive()}";
            $body = $this->bodyTemplateEmail('RECEPCION', $activityPoliza, $variation);

            //6. Generar soporte y envio de correo
            $this->resendEmail(
                $req,
                $activity->id,
                Service::SERVICE_VARIATIONS_SORT_MNK,
                $activityAction,
                $emails,
                $variation->id,
                $subject,
                $client,
                $body,
                [],
                Senders::MNK_INSURANCE
            );

            DB::commit();
            return response()->json('Solicitud exitosa', 200);
        } catch (Exception $e) {

            DB::rollback();
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    //Acción SOLICITAR_ACTUALIZACION_OTROS_DATOS VARIACION SORT
    public function actionUpdatedOtherData(Request $req, $cpath)
    {

        //Buscamos el objeto de poliza mediante su activity_id
        $activityPoliza = Activity::where('id', $req->input('activity_id'))
            ->firstOrFail();


        //Buscamos el cliente
        $client = Client::where('path', $cpath)
            ->firstOrFail();

        DB::beginTransaction();

        try {
            //1. creamos la activity de la variacion  con el parent id de la poliza, deberia llegar por parametro el id de la poliza
            $activity = new Activity;

            $activity->parent_id = $req->input('activity_id');
            $activity->client_id = $client->id;
            $activity->affiliate_id = $activityPoliza->affiliate_id;
            $activity->service_id = Service::SERVICE_VARIATIONS_SORT_MNK;
            $activity->state_id = $activityPoliza->state_id;
            $activity->user_id = Auth::id();

            $activity->state_id = State::REGISTRADO;
            $activity->save();

            //2. guardamos los datos de la variación y ejecutamos los demas pasos.
            $variation = new VariationsSort;
            $variation->activity_id = $activity->id;
            $variation->reason = $req->reason;
            $variation->description = $req->description;
            $variation->variation_type = $req->variation_type;
            $variation->new_start_date = $req->new_start_date;
            $variation->intermediary_user = Utilities::getIntermediaryVariation($req);
            $variation->save();

            //Actualizamos el estado de la actividad


            $description = $req->input('description');
            $activityAction = ActionController::create($activity->id, $req->variation_type, $description);


            //reportar solicitud variación
            //Los estados no cambian con esta acción

            if ($activityPoliza->state_id != State::POLIZA_SUSPENDIDA) {
                ActionController::create($activityPoliza->id, Action::REPORTAR_SOLICITUD_VARIACION, $description);
            } else {
                throw new Exception("La acción \"REPORTAR SOLICITUD VARIACIÓN\", no se puede ejecutar porque el servicio \"{$activityPoliza->service->name}\" se encuentra en el estado actual:\"{$activityPoliza->state->name}\" .", 400);
            }


            $emails = $activity->affiliate->email;

            $subject = "Solicitud de variación de la póliza {$activityPoliza->policy_sort->formatNumberConsecutive()}";
            $body = $this->bodyTemplateEmail('RECEPCION', $activityPoliza, $variation);

            $this->massiveDocumentoService(
                $req,
                $activity,
                Action::SOLICITAR_ACTUALIZACION_OTROS_DATOS,
                $activityAction,
                Service::SERVICE_POLICY_SORT_MNK
            );

            //Generar soporte y envio de correo

            $this->resendEmail(
                $req,
                $activity->id,
                Service::SERVICE_VARIATIONS_SORT_MNK,
                $activityAction,
                $emails,
                $variation->id,
                $subject,
                $client,
                $body,
                [],
                Senders::MNK_INSURANCE
            );

            DB::commit();
            return response()->json('Solicitud exitosa', 200);
        } catch (Exception $e) {
            DB::rollback();
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    //Acción SOLICITAR_CANCELACION VARIACION SORT
    public function actionCancell(Request $req, $cpath)
    {
        //Buscamos el objeto de poliza mediante su activity_id
        $activityPoliza = Activity::with(['affiliate'])
            ->where('id', $req->input('activity_id'))
            ->firstOrFail();

        //Buscamos el cliente
        $client = Client::where('path', $cpath)
            ->firstOrFail();

        DB::beginTransaction();

        try {
            //1. creamos la activity de la variacion  con el parent id de la poliza, deberia llegar por parametro el id de la poliza
            $activity = new Activity;

            $activity->parent_id = $req->input('activity_id');
            $activity->client_id = $client->id;
            $activity->affiliate_id = $activityPoliza->affiliate_id;
            $activity->service_id = Service::SERVICE_VARIATIONS_SORT_MNK;
            $activity->state_id = State::REGISTRADO;
            $activity->user_id = Auth::id();
            $activity->save();

            //2. guardamos los datos de la variación y ejecutamos los demas pasos.
            $variation = new VariationsSort;
            $variation->activity_id = $activity->id;
            $variation->reason = $req->reason;
            $variation->description = $req->description;
            $variation->variation_type = $req->variation_type;
            $variation->new_start_date = $req->new_start_date;
            $variation->intermediary_user = Utilities::getIntermediaryVariation($req);
            $variation->save();

            //Registramos el historial de la acción SOLICITAR_CANCELACION en la variación
            $description = $req->input('description');

            $activityAction = ActionController::create($activity->id, $req->variation_type, $description);

            //Registramos el historial de acciones REPORTAR_SOLICITUD_VARIACION en la Poliza
            //Los estados de la poliza no cambian en este estado

            if ($activityPoliza->state_id == State::POLIZA_SUSPENDIDA) {
                ActionController::create($activityPoliza->id, Action::REPORTAR_SOLICITUD_VARIACION, $description);
            } else {
                throw new Exception("La acción \"REPORTAR SOLICITUD VARIACIÓN\", no se puede ejecutar porque el servicio \"{$activityPoliza->service->name}\" se encuentra en el estado actual:\"{$activityPoliza->state->name}\" .", 400);
            }

            $subject = "Solicitud de variación de la póliza {$activityPoliza->policy_sort->formatNumberConsecutive()}";
            $body = $this->bodyTemplateEmail('RECEPCION', $activityPoliza, $variation);

            //Generar soporte y envio de correo
            $this->resendEmail(
                $req,
                $activity->id,
                Service::SERVICE_VARIATIONS_SORT_MNK,
                $activityAction,
                $activityPoliza->affiliate->email,
                $variation->id,
                $subject,
                $client,
                $body,
                [],
                Senders::MNK_INSURANCE
            );

            //Generamos la subida de los documentos masivos
            $this->massiveDocumentoService(
                $req,
                $activity,
                Action::SOLICITAR_CANCELACION,
                $activityAction,
                Service::SERVICE_VARIATIONS_SORT_MNK
            );

            DB::commit();
            return response()->json('Solicitud exitosa', 200);
        } catch (Exception $e) {
            DB::rollback();
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    //Acción SOLICITAR_REHABILITACION Varicion SORT
    public function actionRehabilitation(Request $req, $cpath)
    {
        //Buscamos el objeto de poliza mediante su activity_id
        $activityPoliza = Activity::where('id', $req->input('activity_id'))
            ->firstOrFail();

        //Buscamos el cliente
        $client = Client::where('path', $cpath)
            ->firstOrFail();

        DB::beginTransaction();

        try {

            //1. creamos la activity de la variacion  con el parent id de la poliza, deberia llegar por parametro el id de la poliza
            $activity = new Activity;
            $activity->parent_id = $req->input('activity_id');
            $activity->client_id = $client->id;
            $activity->affiliate_id = $activityPoliza->affiliate_id;
            $activity->service_id = Service::SERVICE_VARIATIONS_SORT_MNK;
            $activity->state_id = State::REGISTRADO;
            $activity->user_id = Auth::id();
            $activity->save();

            //2. guardamos los datos de la variación y ejecutamos los demas pasos.
            $variation = new VariationsSort;
            $variation->activity_id = $activity->id;
            $variation->reason = $req->input('reason');
            $variation->description = $req->input('description');
            $variation->variation_type = $req->input('variation_type');
            $variation->new_start_date = $req->input('new_start_date');
            $variation->intermediary_user = Utilities::getIntermediaryVariation($req);
            $variation->save();

            //3. Guardamos el historial de  la acción SOLICITAR_REHABILITACION en la variación
            $description = $req->input('description');
            $activityAction = ActionController::create($activity->id, $req->variation_type, $description);

            if ($activityPoliza->state_id == State::POLIZA_SUSPENDIDA) {
                ActionController::create($activityPoliza->id, Action::REPORTAR_SOLICITUD_VARIACION, $description);
            } else {
                throw new Exception("La acción \"REPORTAR SOLICITUD VARIACIÓN\", no se puede ejecutar porque el servicio \"{$activityPoliza->service->name}\" se encuentra en el estado actual:\"{$activityPoliza->state->name}\" .", 400);
            }


            //Generamos la subida de los documentos masivos
            $this->massiveDocumentoService(
                $req,
                $activity,
                Action::SOLICITAR_REHABILITACION,
                $activityAction,
                Service::SERVICE_VARIATIONS_SORT_MNK
            );

            $subject = "Solicitud de variación de la póliza {$activityPoliza->policy_sort->formatNumberConsecutive()}";
            $body = $this->bodyTemplateEmail('RECEPCION', $activityPoliza, $variation);

            //Generar soporte y envio de correo
            $this->resendEmail(
                $req,
                $activity->id,
                Service::SERVICE_VARIATIONS_SORT_MNK,
                $activityAction,
                $activityPoliza->affiliate->email,
                $variation->id,
                $subject,
                $client,
                $body,
                [],
                Senders::MNK_INSURANCE
            );


            DB::commit();
            return response()->json('Solicitud exitosa', 200);
        } catch (Exception $e) {
            DB::rollback();
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    //Acción SOLICITAR_CAMBIO_FORMA_DE_PAGO
    public function actionChangePaymentMethod(Request $req, $cpath)
    {
        //Buscamos el objeto de poliza mediante su activity_id
        $activityPoliza = Activity::where('id', $req->input('activity_id'))
            ->firstOrFail();

        //Buscamos el cliente
        $client = Client::where('path', $cpath)
            ->firstOrFail();

        DB::beginTransaction();

        try {
            //1. creamos la activity de la variacion  con el parent id de la poliza, deberia llegar por parametro el id de la poliza
            $activity = new Activity;

            $activity->parent_id = $req->input('activity_id');
            $activity->client_id = $client->id;
            $activity->affiliate_id = $activityPoliza->affiliate_id;
            $activity->service_id = Service::SERVICE_VARIATIONS_SORT_MNK;
            $activity->user_id = Auth::id();

            $activity->state_id = State::REGISTRADO;
            $activity->save();

            //2. guardamos los datos de la variación y ejecutamos los demas pasos.
            $variation = new VariationsSort;
            $variation->activity_id = $activity->id;
            $variation->reason = $req->reason;
            $variation->description = $req->description;
            $variation->variation_type = $req->variation_type;
            $variation->new_start_date = $req->new_start_date;
            $variation->intermediary_user = Utilities::getIntermediaryVariation($req);
            $variation->save();

            //Actualizamos el estado de la actividad

            //registrar action REPORTAR_SOLICITUD_VARIACION en la poliza sort
            $description = $req->input('description');

            $activityAction = ActionController::create($activity->id, $req->variation_type, $description);

            //reportar REPORTAR_SOLICITUD_VARIACION en la poliza


            if ($activityPoliza->state_id != State::POLIZA_SUSPENDIDA) {
                ActionController::create($activityPoliza->id, Action::REPORTAR_SOLICITUD_VARIACION, $description);
            } else {
                throw new Exception("La acción \"REPORTAR SOLICITUD VARIACIÓN\", no se puede ejecutar porque el servicio \"{$activityPoliza->service->name}\" se encuentra en el estado actual:\"{$activityPoliza->state->name}\" .", 400);
            }

            $subject = "Solicitud de variación de la póliza {$activityPoliza->policy_sort->formatNumberConsecutive()}";
            $body = $this->bodyTemplateEmail('RECEPCION', $activityPoliza, $variation);

            //Generar soporte y envio de correo
            $this->resendEmail(
                $req,
                $activity->id,
                Service::SERVICE_VARIATIONS_SORT_MNK,
                $activityAction,
                $activityPoliza->affiliate->email,
                $variation->id,
                $subject,
                $client,
                $body,
                [],
                Senders::MNK_INSURANCE
            );

            DB::commit();
            return response()->json('Solicitud exitosa', 200);
        } catch (Exception $e) {
            DB::rollback();
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
            ], 500);
        }
    }
    //FIN ACCIONES VISTA TOMADOR


    //ACCIONES VISTA ADMINISTRATIVO

    //REPORTAR_APROBACION_CAMBIO_VIGENCIA_DE_POLIZA
    public function reportChangeDurationPolicy(Request $req, $cpath)
    {

        DB::beginTransaction();

        try {
            $client = Client::where('path', $cpath)->first();
            //Buscamos la activity de la variacion SORT
            $activity = Activity::where('id', $req->activity_id)->first();

            // Convertir la fecha usando Carbon (con control de errores)
            $operation_date = $req->operation_date
                ? Carbon::parse($req->operation_date)->format('Y-m-d')
                : Carbon::now()->format('Y-m-d');


            $description = $req->descripcion;

            $activityAction = ActionController::create($activity->id, $req->variation_type, $description);
            $policy = PolicySort::where('activity_id', $activity->parent_id)->first();
            $variation = VariationsSort::where('activity_id', $activity->id)->first();
            $variation->management_result = $req->management_result;
            $variation->observation_Reject = $req->descripcion;
            $variation->operation_date = $operation_date;

            $oldValidityTo = $policy->validity_to;
            $oldValidityFrom = $policy->validity_from;

            $variation->old_values = json_encode([
                'validity_to' => $oldValidityTo,
                'validity_from' => $oldValidityFrom
            ]);


            $variation->save();
            $activityPolicy = Activity::where('id', $activity->parent_id)->first();
            ActionController::create($activity->parent_id, Action::REPORTAR_CAMBIO_DE_VIGENCIA, $description);

            //capturamos la dos fechas renovación y vigencia desde
            $formDateValidityFrom = $req->fechaVigencia;
            $formDateRenovation = $req->fechaRenovacion;

            // Validamos que estos dos campos realmente vengan
            if ((empty($formDateValidityFrom) || is_null($formDateValidityFrom)) || (empty($formDateRenovation) || is_null($formDateRenovation))) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Por favor, validar si los campos que se desea realizar la variación están llenos.'
                ], 400);
            }

            $fechaRenovacion = new DateTime($formDateRenovation);
            $fechaRenovacion->modify('-1 day');
            // Asignar la nueva fecha a la propiedad validity_to
            $policy->validity_to = $fechaRenovacion->format('Y-m-d');
            $policy->validity_from = $formDateValidityFrom;          //Fecha antigua
            $policy->save();

            $this->_sendEmailHolderOrIntermediary(
                $req,
                $activity,
                $activityPolicy,
                $variation,
                $activityAction,
                $client,
                $activityPolicy->affiliate->email);

            DB::commit();
            return response()->json($activity);
        } catch (Exception $e) {

            DB::rollback();
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    //Acción REPORTAR_APROBACION_ACTUALIZACION_OTROS_DATOS
    public function actionReportApprovalUpdateOtherData(Request $req, $cpath)
    {
        DB::beginTransaction();

        try {
            $client = Client::where('path', $cpath)->first();
            //Buscamos la activity de la variacion SORT
            $activity = Activity::where('id', $req->activity_id)->firstOrFail();

            // Convertir la fecha usando Carbon (con control de errores)
            $operation_date = $req->operation_date
                ? Carbon::parse($req->operation_date)->format('Y-m-d')
                : Carbon::now()->format('Y-m-d');

            $description = $req->descripcion;
            // Crear la acción REPORTAR ACTUALIZACION_OTROS_DATOS en estado POLIZA_EMITIDA_ACTIVA
            $activityAction = ActionController::create($activity->id, $req->variation_type, $description);

            // Obtener los datos
            $fields = $req->fields;

            //validar que este campo no venga vacío
            if (empty($fields) || is_null($fields)) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Por favor, validar si los campos que se desea realizar la variación están llenos.'
                ], 400);
            }

            $policySort = PolicySort::where('activity_id', $activity->parent_id)->first();

            if ($fields) {
                foreach ($fields as &$fieldvalores) {

                    if (isset($fieldvalores['field']) && isset($fieldvalores['newValue'])) {

                        $column = $fieldvalores['field']; // Cambio: Usar $fieldvalores en lugar de $field
                        $newValue = $fieldvalores['newValue'];

                        // Asegurarse de que la columna existe en el modelo Contact o Affiliate
                        if (
                            in_array($column, [
                                'legal_representative_name',
                                'legal_representative_id',
                                'legal_representative_profession',
                            ])
                        ) {
                            // Actualizar en el modelo Contact
                            $policySort->$column = $newValue;
                            $policySort->save();
                        }
                    } elseif (isset($fieldvalores['field']) && isset($fieldvalores['fileUrl'])) {
                        $code = $fieldvalores['field'] == "150" ? ['218', '219'] : intval($fieldvalores['field']);

                        $documents = ActivityDocument::where('activity_id', $activity->parent_id)
                            ->when(is_array($code), function ($query) use ($code) {
                                return $query->whereIn('document_id', $code);
                            }, function ($query) use ($code) {
                                return $query->where('document_id', $code);
                            })
                            ->first();

                        $fieldvalores['fileUrl_old'] = $documents ? $documents->path : null; // Asegurarse de que $documents existe

                        if (!$documents && !is_array($code)) {
                            $documents = new ActivityDocument();
                            $documents->activity_id = $activity->parent_id;
                            $documents->document_id = $code;
                            $documents->path = $fieldvalores['fileUrl'];
                            $documents->save();
                        } elseif ($documents) {
                            $documents->path = $fieldvalores['fileUrl'];
                            $documents->save();
                        } else {
                            DB::rollBack();
                            return response()->json([
                                'status' => 'error',
                                'message' => 'Documento no encontrado'
                            ], 400);
                        }
                    } else {
                        // Si faltan datos, hacer rollback y retornar un error
                        DB::rollBack();
                        return response()->json([
                            'status' => 'error',
                            'message' => 'Faltan datos en algunos campos'
                        ], 400);
                    }
                }

                unset($fieldvalores); // Limpia la referencia
            }


            if ($req->input('responsable')) {

                // Obtener todos los IDs de los contactos enviados desde el frontend
                $idsContact = collect($req->input('responsable'))
                    ->pluck('id')
                    ->filter();

                // Obtener todos los IDs de los contactos que ya están en la base de datos
                $contactExists = PolicyContact::where('policy_sort_id', $policySort->id)
                    ->pluck('id');

                // Identificar los contactos que deben ser eliminados por su Id
                $idsDelete = $contactExists->diff($idsContact);

                // Eliminar los contactos que no están presentes en los datos enviados
                PolicyContact::whereIn('id', $idsDelete)->delete();
                // Ahora actualizamos o creamos nuevos contactos
                foreach ($req->input('responsable') as $field) {
                    if (!empty($field['id'])) {
                        // Actualización de registro existente
                        $responsable = PolicyContact::find($field['id']);

                        if ($responsable) {
                            $responsable->name_responsible = $field['name_responsible'];
                            $responsable->type_identification = $field['type_identification'];
                            $responsable->number_identify_responsible = $field['number_identify_responsible'];
                            $responsable->ocupation_responsible = $field['ocupation_responsible'];
                            $responsable->phone_responsible = $field['phone_responsible'];
                            $responsable->cellphone_responsible = $field['cellphone_responsible'];
                            $responsable->email_responsible = $field['email_responsible'];
                            $responsable->save();
                        }
                    } else {
                        // Crear un nuevo registro si no tiene ID
                        PolicyContact::create([
                            'policy_sort_id' => $policySort->id,
                            'name_responsible' => $field['name_responsible'],
                            'type_identification' => $field['type_identification'],
                            'number_identify_responsible' => $field['number_identify_responsible'],
                            'ocupation_responsible' => $field['ocupation_responsible'],
                            'phone_responsible' => $field['phone_responsible'],
                            'cellphone_responsible' => $field['cellphone_responsible'],
                            'email_responsible' => $field['email_responsible'],
                        ]);

                        $policySortController = new PolicySortController();
                        $policySortController->uniqueContactCode($cpath, $policySort->activity_id, true);
                    }
                }
            }


            $activityPolicy = Activity::where('id', $activity->parent_id)->first();

            // Convertir la fecha usando Carbon (con control de errores)
            $operation_date = $req->operation_date
                ? Carbon::parse($req->operation_date)->format('Y-m-d')
                : Carbon::now()->format('Y-m-d');

            //Crear activity action (historial
            $descriptions = "Reportar actualizacion datos de contacto";

            ActionController::create($activityPolicy->id, Action::REPORTAR_ACTUALIZACION_OTROS_DATOS, $descriptions);

            $variationSort = VariationsSort::where('activity_id', $activity->id)->first();

            $old_values = json_encode($fields);

            if ($variationSort) {
                $variationSort->reason = $req->reason;
                $variationSort->management_result = $req->management_result;
                $variationSort->observation_Reject = $req->descripcion;
                $variationSort->operation_date = $operation_date;
                $variationSort->old_values = $old_values;
                $variationSort->save();
            }

            $this->_sendEmailHolderOrIntermediary(
                $req,
                $activity,
                $activityPolicy,
                $variationSort,
                $activityAction,
                $client,
                $activityPolicy->affiliate->email);

            DB::commit();
            return response()->json('Solicitud exitosa', 200);
        } catch (Exception $e) {
            DB::rollback();
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    //Acción REPORTAR_APROBACION_ACTUALIZACION_DATOS_DE_CONTACTO
    public function reportUpdateDataContact(Request $req, $cpath)
    {
        DB::beginTransaction();
        try {
            $client = Client::where('path', $cpath)->firstOrFail();
            //Buscamos la activity de la variacion SORT
            $activity = Activity::where('id', $req->input('activity_id'))->first();

            // Convertir la fecha usando Carbon (con control de errores)
            $operation_date = $req->operation_date
                ? Carbon::parse($req->operation_date)->format('Y-m-d')
                : Carbon::now()->format('Y-m-d');

            $description = $req->descripcion;

            $activityAction = ActionController::create($activity->id, $req->variation_type, $description);

            // Capturar el array de campos enviados
            $fields = $req->fields;

            //validar que este campo no venga vacío
            if (empty($fields) || is_null($fields)) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Por favor, validar si los campos que se desea realizar la variación están llenos.'
                ], 400);
            }

            // Asumimos que tienes un modelo `Affiliate` y `PolicySort` que estás actualizando
            $affiliate = Affiliate::where('id', $activity->affiliate_id)->first();
            $policySort = PolicySort::where('activity_id', $activity->parent_id)->first();

            // Verificar si se encontró el registro
            if (!$affiliate) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'No se encontró el registro del afiliado'
                ], 404);
            }

            // Verificar si se encontró el registro de poliza sort
            if (!$policySort) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'No se encontró el registro de poliza sort'
                ], 404);
            }


            // Recorrer cada campo y actualizar su respectiva columna
            foreach ($fields as $field) {
                // Asegurarse de que tanto 'field' como 'newValue' estén presentes en cada campo
                if (isset($field['field']) && isset($field['newValue']) && $field['field'] != "activity_economic_id") {
                    $column = $field['field'];
                    $newValue = $field['newValue'];

                    // Asegurarse de que la columna existe en el modelo Contact o Affiliate
                    if (
                        in_array($column, [
                            'first_name',
                            'phone',
                            'electronic_billing_email',
                            'iban_account',
                            'employer_address',

                        ])
                    ) {
                        // Actualizar en el modelo Contact
                        $affiliate->$column = $newValue;
                        $affiliate->save();
                    } elseif (
                        in_array($column, [
                            'notification_email',
                        ])
                    ) {
                        // Actualizar en el modelo Affiliate
                        $policySort->$column = $newValue;
                        $policySort->save();

                        //Actualizar también el correo del afiliado asocciado a la variación (que es el mismo que el de la poliza)
                        $affiliate->email = $newValue;
                        $affiliate->save();
                    }
                } else if (isset($field['field']) && isset($field['canton']) && isset($field['district']) && isset($field['province']) && $field['field'] != "activity_economic_id") {

                    $affiliate->province = $field['province'];
                    $affiliate->canton = $field['canton'];
                    $affiliate->district = $field['district'];
                    $affiliate->save();
                } else if (isset($field['field']) && $field['field'] == "activity_economic_id") {

                    $policySort->activity_economic_id = $field['economic'];
                    $policySort->save();
                } else {
                    // Si faltan datos, hacer rollback y retornar un error
                    DB::rollBack();
                    return response()->json([
                        'status' => 'error',
                        'message' => 'Faltan datos en algunos campos'
                    ], 400);
                }
            }

            // // //Actualizamos el estado en la poliza SORT
            // //Buscamos la activity de la variacion SORT
            $activityPolicy = Activity::where('id', $activity->parent_id)->first();


            //Crear activity action (historial
            ActionController::create($activityPolicy->id, Action::REPORTAR_ACTUALIZACION_DATOS_DE_CONTACTO, $description);

            $variationSort = VariationsSort::where('activity_id', $activity->id)->first();

            $old_values = $req->old_values;


            if ($variationSort && $old_values) {
                $variationSort->reason = $req->reason;
                $variationSort->management_result = $req->management_result;
                $variationSort->observation_Reject = $req->descripcion;
                $variationSort->operation_date = $operation_date;
                $variationSort->old_values = $old_values;
                $variationSort->save();
            } else {
                // Si faltan datos, hacer rollback y retornar un error
                DB::rollBack();
                return response()->json([
                    'status' => 'error',
                    'message' => 'Variación no encontrada'
                ], 404);
            }

            $this->_sendEmailHolderOrIntermediary(
                $req,
                $activity,
                $activityPolicy,
                $variationSort,
                $activityAction,
                $client,
                $activityPolicy->affiliate->email);

            DB::commit();
            return response()->json('Solicitud exitosa', 200);
        } catch (Exception $e) {
            DB::rollback();
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    //Accion REPORTAR_RECHAZO_VARIACION_DE_LA_POLIZA
    public function reportRejectionPolicyVariation(Request $req, $cpath)
    {

        DB::beginTransaction();

        try {
            $client = Client::where('path', $cpath)
                ->firstOrFail();
            //Buscamos la activity de la variacion SORT
            $activity = Activity::where('id', $req->activity_id)->first();

            // Convertir la fecha usando Carbon (con control de errores)
            $operation_date = $req->operation_date
                ? Carbon::parse($req->operation_date)->format('Y-m-d')
                : Carbon::now()->format('Y-m-d');


            $description = $req->descripcion;


            $activityAction = ActionController::create($activity->id, $req->variation_type, $description);

            $variationSort = VariationsSort::where('activity_id', $activity->id)->first();


            if ($variationSort) {
                $variationSort->reason = $req->reason;
                $variationSort->management_result = $req->management_result;
                $variationSort->observation_reject = $req->descripcion;
                $variationSort->operation_date = $operation_date;
                $variationSort->reason_rejection = $req->reason_detention;
                $variationSort->save();
            }

            $activityPolicy = Activity::where('id', $activity->parent_id)->first();

            //TODO: Este correo tiene body?
            $this->_sendEmailHolderOrIntermediary(
                $req,
                $activity,
                $activityPolicy,
                $variationSort,
                $activityAction,
                $client,
                $activity->affiliate->email);

            DB::commit();
            return response()->json('Solicitud exitosa', 200);
        } catch (Exception $e) {
            DB::rollback();
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    //Accion Reportar DETENCIÖN de la variación
    public function reportArrestPolicyVariation(Request $req, $cpath)
    {

        DB::beginTransaction();

        try {
            $client = Client::where('path', $cpath)->first();
            //Buscamos la activity de la variacion SORT
            $activity = Activity::where('id', $req->activity_id)->first();

            // Convertir la fecha usando Carbon (con control de errores)
            $operation_date = $req->operation_date
                ? Carbon::parse($req->operation_date)->format('Y-m-d')
                : Carbon::now()->format('Y-m-d');

            $description = $req->descripcion;


            $activityAction = ActionController::create($activity->id, $req->variation_type, $description);

            $variationSort = VariationsSort::where('activity_id', $activity->id)->first();

            $activityPolicy = Activity::where('id', $activity->parent_id)->first();

            if ($variationSort) {
                $variationSort->reason_detention = $req->reason_detention;
                $variationSort->management_result = $req->management_result;
                $variationSort->observation_reject = $req->descripcion;
                $variationSort->operation_date = $operation_date;
                $variationSort->save();
            }

            //TODO: Este correo tiene body?
            $this->_sendEmailHolderOrIntermediary(
                $req,
                $activity,
                $activityPolicy,
                $variationSort,
                $activityAction,
                $client,
                $activity->affiliate->email);

            DB::commit();
            return response()->json('Solicitud exitosa', 200);
        } catch (Exception $e) {
            DB::rollback();
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    // Acción REPORTAR_APROBACION_CAMBIO_FORMA_DE_PAGO
    public function reportChangePaymentMethod(Request $req, $cpath)
    {
        //Buscamos el cliente
        $client = Client::where('path', $cpath)
            ->firstOrFail();


        DB::beginTransaction();

        try {
            //Buscamos la activity de la variacion SORT
            $activity = Activity::where('id', $req->activity_id)->first();
            // // Crear la acción REPORTAR CAMBIO FORMA DE PAGO en estado POLIZA_EMITIDA_ACTIVA

            // Convertir la fecha usando Carbon (con control de errores)
            $operation_date = $req->operation_date
                ? Carbon::parse($req->operation_date)->format('Y-m-d')
                : Carbon::now()->format('Y-m-d');

            $description = $req->descripcion;

            $activityAction = ActionController::create($activity->id, $req->variation_type, $description);

            //Buscamos la activity de la poliza por el parent_id de la actividad de la variacion
            $activityPolicy = Activity::where('id', $activity->parent_id)->firstOrFail();

            // //vamos al servicio Poliza SORT y generamos la accion  REPORTAR SOLICITUD VARIACIÓN
            // //Esta accion no cambia los estados de la poliza

            ActionController::create($activity->parent_id, Action::REPORTAR_CAMBIO_FORMA_DE_PAGO, $description);
            $policySort = PolicySort::where('activity_id', $activityPolicy->id)->first();
            //Buscamos el id de a variacion por medio de su activity_id
            $variation = VariationsSort::where('activity_id', $req->activity_id)->first();
            $variation->management_result = $req->management_result;
            $variation->observation_Reject = $req->descripcion;
            $variation->operation_date = $operation_date;


            $oldperiodicity = $policySort->periodicity;
            $newValue = $req->newperiodicity;

            //validar que este campo no venga vacío
            if (empty($newValue) || is_null($newValue)) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Por favor, validar si los campos que se desea realizar la variación están llenos.'
                ], 400);
            }

            $variation->old_values = json_encode([
                'periodicity_old' => $oldperiodicity,
                'periodicity_new' => $newValue
            ]);

            $variation->save();

            if ($policySort) {
                $policySort->periodicity = $newValue;
                $policySort->save();
            } else {
                throw new Exception("No se encontró el formulario POLIZA SORT asociado con la actividad.");
            }

            $this->_sendEmailHolderOrIntermediary(
                $req,
                $activity,
                $activityPolicy,
                $variation,
                $activityAction,
                $client,
                $activity->affiliate->email);
            DB::commit();
            return response()->json('Solicitud exitosa', 200);
        } catch (Exception $e) {
            DB::rollback();

            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    //Acción REPORTAR_APROBACION_CANCELACION
    public function reportAprovalCancellation(Request $req, $cpath)
    {

        //Buscamos el cliente
        $client = Client::where('path', $cpath)
            ->firstOrFail();

        DB::beginTransaction();

        try {
            //Buscamos la activity de la variacion SORT
            $activity = Activity::where('id', $req->activity_id)->first();

            // Convertir la fecha usando Carbon (con control de errores)
            $operation_date = $req->operation_date
                ? Carbon::parse($req->operation_date)->format('Y-m-d')
                : Carbon::now()->format('Y-m-d');

            $description = $req->descripcion;

            $activityAction = ActionController::create($activity->id, $req->variation_type, $description);

            //Buscamos la activity de la poliza por el parent_id de la actividad de la variacion 
            $activityPolicy = Activity::where('id', $activity->parent_id)->firstOrFail();

            ActionController::create($activity->parent_id, Action::REPORTAR_CANCELACION, $description);

            //Buscamos el id de a variacion por medio de su activity_id 
            $variation = VariationsSort::where('activity_id', $req->activity_id)->first();

            $variation->management_result = $req->management_result;
            $variation->observation_Reject = $req->descripcion;
            $variation->operation_date = $operation_date;
            $variation->save();

            $policy = PolicySort::where('activity_id', $activityPolicy->id)->first();

            //registra asiento contable
            $accountingEntryController = new AccountingEntryController();
            $accountingEntry002 = $accountingEntryController->reportAccountCaseTwo002($policy);
            $accountingEntry120 = $accountingEntryController->reportAccountCase120($policy);

            //SOLO SE DEBE EJECUTAR UN 153 ya sea del 120 o el 002
            //ejeucutamos el asiento 153 para el caso 2 del asiento 002
            //$accountingEntryController->reportAccountCaseOneTwo153($cpath, $policy->id, $accountingEntry002['value']);

            //ejeucutamos el asiento 153 para el asiento 120
            $accountingEntryController->reportAccountCaseOneTwo153($cpath, $policy->id, $accountingEntry120['value']);

            $this->_sendEmailHolderOrIntermediary(
                $req,
                $activity,
                $activityPolicy,
                $variation,
                $activityAction,
                $client,
                implode(',', [$policy->email, $activityPolicy->affiliate->email]));

            DB::commit();
            return response()->json('Solicitud exitosa', 200);
        } catch (Exception $e) {
            DB::rollback();
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
            ], 500);
        }
    }


    public function sendEmailWithAttachments($emails, $subject, $text, $files = [], $client_id, $activity, $activityAction, $activity_service, $req)
    {
        if ($emails) {

            try {
                $mailSent = new SendDocumentDataBase(
                    $emails,
                    $subject,
                    "<EMAIL>",
                    "Prueba",
                    [
                        "text" => $text,
                        "sender" => 'Prueba'
                    ],
                    "<EMAIL>",
                    $files,
                    "send_document_db",
                    $client_id,
                    $req->getHost(),
                    $activity,
                    $activityAction,
                    $activity_service
                );


                // Capturar el resultado del envío
                $result = $mailSent->sendMail();

                //Registramos los datos del correo enviado para la trazabilidad
                $mailBoardController = new MailBoardController();
                    $mailBoardController->createRegisterMail(
                    $activity->id,
                    $activity->service->id, 
                    $activity->parent->policy_sort->consecutive, 
                    'Tomador', 
                    mb_convert_case(mb_strtolower($activity->parent->affiliate->full_name ?? ''), MB_CASE_TITLE, "UTF-8"), 
                    $activity->parent->affiliate->doc_number, 
                    $subject, 
                    $text,
                    $emails, 
                    $result,
                    null
                );
            } catch (Exception $e) {

                return response()->json([
                    'response' => false,
                    'message' => 'Error al enviar el correo',
                    'error' => $e
                ]);
            }
        }

        return response()->json(['response' => false, 'message' => 'No se proporcionaron correos electrónicos']);
    }


    public function sendEmailCron($activity, $activity_action, $cpath, $request)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $activityPolicy = Activity::where('id', $activity->parent_id)->first();

        $emails = [
            $activityPolicy->affiliate->email,
            $activityPolicy->policy_sort->email
        ];

        // Remover correos duplicados y vacíos (opcional)
        $emails = array_filter(array_unique($emails));

        // Si necesitas convertirlos a un string separado por comas
        $emailsString = implode(',', $emails);

        $subject = 'Reporte anulación variación <' . $activity->id . '> - automática';
        $text = view('services.plantilla.docs.report_rejection_policy_variation')->render();

        $this->resendEmailSinFile($request, $activity, $activity->service_id, $activity_action, $emailsString, $subject, $text, $client);
    }


    public function resendEmailSinFile(Request $req, $activity, $activity_service, $activityAction, $emails, $subject, $cuerpo, $client_id)
    {
        $files = [];
        // Enviar el correo con o sin archivos adjuntos
        return $this->sendEmailWithAttachments($emails, $subject, $cuerpo, $files, $client_id, $activity, $activityAction, $activity_service, $req);
    }


    public function cronReportDetentionResponse(Request $req, $cpath)
    {
        $elevenDays = Carbon::now()->subDays(11)->startOfDay()->toDateString();

        $results = Activity::where('service_id', Service::SERVICE_VARIATIONS_SORT_MNK)
            ->where('state_id', State::VARIACION_EN_DETENCION)
            ->whereDate('updated_at', '<=', $elevenDays) // Facturas con 11 días o más de antigüedad
            ->get();


        $activitiesWithElevenBusinessDays = [];

        foreach ($results as $activity) {
            $updatedAt = Carbon::parse($activity->updated_at);

            if ($this->hasElevenBusinessDaysPassed($updatedAt)) {
                $activitiesWithElevenBusinessDays[] = $activity;
            }
        }

        DB::beginTransaction();
        try {
            foreach ($activitiesWithElevenBusinessDays as $result) {
                $activity = Activity::find($result->id);
                $variation = VariationsSort::where('activity_id', $activity->id)->first();
                $variation->management_result = 'RECHAZO';
                $variation->observation_Reject = 'VARIACIÓN RECHAZADA AUTOMÁTICAMENTE POR EL SISTEMA';
                $variation->save();

                $activityAction = ActionController::create($activity->id, Action::REPORTAR_RECHAZO_VARIACION_DE_LA_POLIZA, 'VARIACIÓN RECHAZADA AUTOMÁTICAMENTE POR CUMPLIR 11 DÍAS');


                $this->sendEmailCron($activity, $activityAction, $cpath, $req);
            }
            DB::commit();
        } catch (Exception $e) {
            DB::rollback();
            return [
                'status' => 'ERROR',
                'message' => 'No se pudieron recharzas las variaciones',
                'error' => $e
            ];
        }

        return [
            'status' => 'SUCCESS',
            'message' => 'Variaciones rechazadas correctamente'
        ];
    }

    private function hasElevenBusinessDaysPassed($startDate)
    {
        // Definir la fecha de fin como la fecha actual
        $endDate = Carbon::now();
        // Obtener los días festivos entre la fecha de inicio y la fecha actual
        $holidays = Holiday::whereBetween('holiday', [$startDate, $endDate])
            ->pluck('holiday')
            ->map(function ($holiday) {
                return Carbon::parse($holiday)->toDateString();
            })
            ->toArray();
        $currentDate = $startDate->copy();
        $businessDaysCount = 0;
        // Contar los días hábiles desde la fecha de inicio hasta la fecha actual
        while ($currentDate->lessThanOrEqualTo($endDate)) {
            // Verifica si el día actual es un día hábil (ni sábado, ni domingo, ni festivo)
            if ($currentDate->dayOfWeek !== Carbon::SUNDAY && $currentDate->dayOfWeek !== Carbon::SATURDAY && !in_array($currentDate->toDateString(), $holidays)) {
                $businessDaysCount++;
            }
            // Verifica si se ha alcanzado el onceavo día hábil
            if ($businessDaysCount === 11) {
                return true;
            }
            // Incrementa la fecha en un día
            $currentDate->addDay();
        }
        // Si no se alcanzaron los 11 días hábiles
        return false;
    }


    //Acción REPORTAR_APROBACION_REHABILITACION
    public function reportRehabilitation(Request $req, $cpath)
    {

        $subject = 'Aprobación de variación';

        //Buscamos el cliente
        $client = Client::where('path', $cpath)
            ->firstOrFail();
        try {

            DB::beginTransaction();

            // Convertir la fecha usando Carbon (con control de errores)
            $operation_date = $req->operation_date
                ? Carbon::parse($req->operation_date)->format('Y-m-d')
                : Carbon::now()->format('Y-m-d');

            $description = $req->descripcion;


            $activity = Activity::where('id', $req->activity_id)->first();
            $activityAction = ActionController::create($activity->id, $req->variation_type, $description);


            $activityPolicy = Activity::where('id', $activity->parent_id)->firstOrFail();

            //reportar REPORTAR_SOLICITUD_VARIACION en la poliza
            ActionController::create($activityPolicy->id, Action::REPORTAR_REHABILITACION, $description);


            //Servicio de cobros
            $this->createCobros($activityPolicy, $client);


            $variation = VariationsSort::where('activity_id', $req->activity_id)->first();

            $variation->management_result = $req->management_result;
            $variation->observation_Reject = $req->descripcion;
            $variation->operation_date = $operation_date;
            $variation->save();

            $subject = '';
            $body = '';
            $email = '';

            if ($variation->intermediary_user) {
                $intermediary = User::query()
                    ->where('id', $variation->intermediary_user)
                    ->first();
                $emailBuild = $this->_bodyTemplateEmailIntermediary($req->management_result, $activityPolicy, $intermediary, $variation);
                if ($emailBuild) {
                    $subject = $emailBuild['subject'];
                    $body = $emailBuild['body'];
                    $email = $intermediary->email;
                }
            } else {
                $subject = "Rehabilitación de póliza #{$activityPolicy->policy_sort->formatNumberConsecutive()}";
                $body = $this->bodyTemplateEmail('REHABILITACION', $activityPolicy);
                $email = implode(',', [$activityPolicy->policy_sort->email, $activityPolicy->affiliate->email]);
            }

            //Obtenemos los nombres de catón, provincia y distrito
            $ubicacion = $this->getLocationNamesFromJson($activityPolicy->affiliate->province, $activityPolicy->affiliate->canton, $activityPolicy->affiliate->district);

            //obtenemos la actividad economica
            $jsonSource = ($activityPolicy->policy_sort->economic_activity == 'public') ? '/js/economic_activity/public.json' : '/js/economic_activity/private.json';
            $json = file_get_contents(public_path($jsonSource));
            $economicActivities = json_decode($json, true);
            $activity_economic_name = collect($economicActivities)->firstWhere('CODE', $activityPolicy->policy_sort->activity_economic_id)['ACTIVITY_NAME'];
            $activityPolicy->policy_sort->economic_activity_name = $activity_economic_name;


            //Pdf para condiciones particulares de la póliza
            $pdf_particular = PDF::loadView("services.plantilla.docs.condiciones_particulares", [
                'watermark' => false,
                'policy' => $activityPolicy->policy_sort,
                'ubicacion' => $ubicacion,
                'type_document' => 'Rehabilitación',
                'affiliate' => $activity->affiliate,
            ]);

            $document_particular = 'condiciones_particulares';
            $pathDocument = "activity_action_document/{$document_particular}_{$activityPolicy->id}.pdf";
            //Subimos a Se el pdf de condiciones particules
            Storage::disk('s3')->put($pathDocument, $pdf_particular->output());


            // Guardamos el documento condiciones particulares
            $activityActionDocument = new ActivityActionDocument();
            $activityActionDocument->activity_action_id = $activityAction->id;
            $activityActionDocument->name = $document_particular;
            $activityActionDocument->path = $pathDocument;
            $activityActionDocument->save();

            $files[] = [
                'type' => 'pdf',
                'path' => $activityActionDocument->path,
                'name' => 'Condiciones particulares' . '.pdf',
            ];
            //TODO: Este correo tiene body?
            $this->resendEmail(
                $req,
                $activity->id,
                Service::SERVICE_VARIATIONS_SORT_MNK,
                $activityAction,
                $email,
                $variation->id,
                $subject,
                $client,
                $body,
                $files,
                Senders::MNK_INSURANCE
            );

            DB::commit();

            return response()->json('Solicitud exitosa', 200);
        } catch (Exception $e) {
            DB::rollback();
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    public function getLocationNamesFromJson($provinceCode, $cantonCode = null, $districtCode = null)
    {
        // Ruta al archivo JSON en la carpeta public
        $path = public_path('js/costarica.json');


        // Cargar y decodificar el archivo JSON
        if (!file_exists($path)) {
            return response()->json(['error' => 'Archivo JSON no encontrado'], 404);
        }


        $jsonData = file_get_contents($path);


        $provinces = json_decode($jsonData, true);

        $location = [
            'province' => null,
            'canton' => null,
            'district' => null
        ];


        foreach ($provinces['province'] as $province) {
            if ($province['code'] == $provinceCode) {
                $location['province'] = $province['name'];


                if (is_null($cantonCode) && is_null($districtCode)) {
                    return $location;
                }


                foreach ($province['cantons'] as $canton) {
                    if ($canton['code'] == $cantonCode) {
                        $location['canton'] = $canton['name'];


                        if (is_null($districtCode)) {
                            return $location;
                        }


                        foreach ($canton['districts'] as $district) {
                            if ($district['code'] == $districtCode) {
                                $location['district'] = $district['name'];
                                return $location;
                            }
                        }
                    }
                }
            }
        }
        // Si no se encuentra alguna de las ubicaciones, retornar null
        return $location;
    }


    private function createCobros($activityPolicy, $client)
    {
        $activity = new Activity;
        $activity->parent_id = $activityPolicy->id;
        $activity->client_id = $client->id;
        $activity->affiliate_id = $activityPolicy->affiliate_id;
        $activity->service_id = Service::SERVICE_POLICY_SORT_COLLECTION_MNK;
        $activity->state_id = State::REGISTRADO;
        $activity->user_id = Auth::id();
        $activity->save();

        $policy_sort_collection = new PolicySortCollection();
        $policy_sort_collection->activity_id = $activity->id;
        $policy_sort_collection->save();

        // llamar actions de cobros REPORTAR RECIBO REHABILITACIÓN

    }

    public function tableTomador(Request $request, $cpath, $id, $npoliza)
    {
        // Recoger los parámetros del formulario
        $stateIds = $request->input('state_ids');
        $policy = $request->input('policy');
        $variationType = $request->input('type');

        $client = Client::query()->where('path', $cpath)->firstOrFail();

        $states = State::with('services')->whereBetween('id', [24, 36])->get();
        $activityQuery = Activity::with([
            'parent_activity.policy_sort',
            'affiliate',
            'state',
            'variations_sort.action'
        ])
            ->where('service_id', Service::SERVICE_VARIATIONS_SORT_MNK)
            ->where('client_id', $client->id)
            ->where('affiliate_id', $id)
            ->orderBy('created_at', 'desc');

        if (!empty($stateIds)) {
            $activityQuery->whereIn('state_id', $stateIds);
        }


        if (!empty($policy)) {
            $activityQuery->whereHas('parent_activity.policy_sort', function ($query) use ($policy) {
                $query->where('consecutive', $policy); // Aquí aplicas el filtro de la póliza
            });
        }

        if (!empty($variationType)) {
            $activityQuery->whereHas('variations_sort', function ($query) use ($variationType) {
                $query->where('variation_type', $variationType); // Filtrar por el tipo de variación
            });
        }

        $activity = $activityQuery->paginate(10);

        return view(
            'services.policy_sort.holder_policy.components.table_variaciones',
            [
                'id' => $id,
                'npoliza' => $npoliza,
                'variations' => $activity,
                'states' => $states,
                'active' => 'svariaciones'
            ]
        );
    }

    private function _sendEmailHolderOrIntermediary(Request $req, $activity, $activityPolicy, $variation, $activityAction, $client, $emailVariation, $files = [])
    {
        $isApproveResult = $req->management_result == 'APROBADA';
        $subject = '';
        $body = '';
        $email = '';
        if ($variation->intermediary_user) { // Intermediario
            $intermediary = User::query()
                ->where('id', $variation->intermediary_user)
                ->first();
            $emailBuild = $this->_bodyTemplateEmailIntermediary($req->management_result, $activityPolicy, $intermediary, $variation);
            if ($emailBuild) {
                $subject = $emailBuild['subject'];
                $body = $emailBuild['body'];
                $email = $intermediary->email;
            }
        } else if($req->management_result == 'RECHAZO') {// Tomador
            $subject = 'Rechazo Variación';
            $email = $activityPolicy->affiliate->email;

        } else if($req->management_result == 'DETENCIÓN') {// Tomador
            $email = $activityPolicy->affiliate->email;
            $emailData = $this->bodyTemplateEmail('DETENCIÓN', $activityPolicy, $variation);
            $subject = $emailData['subject'];
            $body = $emailData['body'];
        } else {// Tomador
            $subject = $isApproveResult ? "Variación satisfactoria de la póliza {$activityPolicy->policy_sort->formatNumberConsecutive()}"
                : "Variación en la póliza {$activityPolicy->policy_sort->formatNumberConsecutive()}"; // Siempre se debe enviar la Variación satisfactoria?
            $body = $this->bodyTemplateEmail($req->management_result, $activityPolicy);
            $email = $emailVariation;
        }

        $this->resendEmail(
            $req,
            $activity->id,
            Service::SERVICE_VARIATIONS_SORT_MNK,
            $activityAction,
            $email,
            $variation->id,
            $subject,
            $client,
            $body,
            $files,
            Senders::MNK_INSURANCE
        );
    }

}
