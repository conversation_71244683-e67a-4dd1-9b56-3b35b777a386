<?php

namespace App\Http\Controllers\Services;

use App\Action;
use App\Actions\ActionPeexpenserecognition;
use App\Activity;
use App\ActivityActionDocument;
use App\ActivityDocument;
use App\Affiliate;
use App\Client;
use App\Http\Controllers\ActionController;
use App\Http\Controllers\Controller;
use App\Mail\SendDocumentDataBase;
use App\PeRecognitionExpenses;
use App\PeIpSort;
use App\PolicySort;
use App\Service;
use App\State;
use App\GisSort;
use App\Http\Controllers\Tables\MailBoardController;
use App\States\StatePeexpenserecognition;
use App\States\StatePoliza;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use PDF;

class PeRecognitionExpensesController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function form(Request $request, $cpath, $id)
    {
        $client   = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();
        $gitSort = GisSort::where('activity_id', $activity->parent_id)->first();
        $activityGis = Activity::where('client_id', $client->id)->where('id', $activity->parent_id)->first();

        $policySort = [];

        if ($activityGis && $activityGis->parent_id) {
            $policySort = PolicySort::where('activity_id', $activityGis->parent_id)->first();
        }

        $peEcogniti = PeRecognitionExpenses::where('activity_id', $activity->id)->first();

        $activityDocuments = [];
        $arcCountTravel = 0;
        $arcCountCounMedical = 0;

        if($peEcogniti){
            $activityDocuments = ActivityDocument::where('activity_id', $peEcogniti->activity_id)->get();
            $arcCountTravel = ActivityDocument::where('activity_id', $peEcogniti->activity_id)
                ->where('document_id', 251)
                ->count();

            $arcCountCounMedical = ActivityDocument::where('activity_id', $peEcogniti->activity_id)
                ->where('document_id', 253)
                ->count();
        }

        $affiliateId = $activity->affiliate_id;
        $gisSorts = GisSort::whereHas('activity', function ($query) use ($affiliateId) {
            $query->where('affiliate_id', $affiliateId);
        })->get();

        return view('services.pe_expense_recognition.form', [
            'activity' => $activity,
            'policySort' => $policySort,
            'affiliate' => $activity->affiliate,
            'id' => $id,
            'peEcogniti' =>$peEcogniti,
            'activityDocuments' => $activityDocuments,
            'arcCountTravel' => $arcCountTravel,
            'arcCountCounMedical' => $arcCountCounMedical,
            'gitSort' => $gitSort,
            'gisSorts' => $gisSorts,
            'activityGis' => $activityGis
        ]);
    }

    /**
     * REPORTAR RECONOCIMIENTO TRASLADO, HOSPEDAJE Y ALIMENTACIÓN
     * Servicio PE RECONOCIMIENTO DE GASTOS
     *
     * @param $activityVariation
     * @return \Illuminate\Http\JsonResponse
     */
    public static function recognitionTransferAccommodationAndFood($cpath, $id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();
        $gitSort = GisSort::where('activity_id', $id)->first();
        $policySort = PolicySort::where('activity_id', $activity->parent_id)->first();

        $description = 'REPORTAR RECONOCIMIENTO TRASLADO, HOSPEDAJE Y ALIMENTACIÓN';
        DB::beginTransaction();
        try {

            $activityNew = new Activity();
            $activityNew->parent_id = $gitSort->activity_id;
            $activityNew->client_id = $activity->client_id;
            $activityNew->affiliate_id = $activity->affiliate_id;
            $activityNew->service_id = Service::SERVICE_PE_RECOGNITION_EXPENSES_MNK;
            $activityNew->state_id = State::REGISTRADO;
            $activityNew->user_id = $activity->user_id;
            $activityNew->save();

            $peEcogniti = new PeRecognitionExpenses();
            $peEcogniti->activity_id = $activityNew->id;
            $peEcogniti->type_identification_expense = $gitSort->type_identification_affiliate;
            $peEcogniti->number_identification_expense = $gitSort->number_identification_affiliate;
            $peEcogniti->name_affiliate_expense = $gitSort->name_affiliate;
            $peEcogniti->n_policy_sort_expense = $policySort->id;
            $peEcogniti->date_case_expense = $gitSort->date_accident;

            $peEcogniti->home_phone_expense = $gitSort->cellphone_affiliate;
            $peEcogniti->phone_expense = $gitSort->cellphone_affiliate;
            $peEcogniti->email_affiliate_expense = $gitSort->email_affiliate;
            $peEcogniti->n_case_expense = $gitSort->id;
            $peEcogniti->invoice_concepts = 'transport_lodging_meals';
            $peEcogniti->save();

            $activityAction = ActionController::create($activityNew->id, ActionPeexpenserecognition::REPORTAR_RECONOCIMIENTO_TRASLADO_HOSPEDAJE_Y_ALIMENTACION,  'REPORTAR RECONOCIMIENTO TRASLADO HOSPEDAJE Y ALIMENTACION');

            DB::commit();

            return response()->json([
                'status' => 'success',
                'message' => 'Reportar reconocimiento traslado hospedaje y alimentación',
                'id' => $activityNew->id], 200);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
            ]);
        }


    }

    //REPORTAR RECONOCIMIENTO DE GASTOS FUNERAL Y TRASLADO
    public function reportFuneralAndTransportExpenses(Request $req, $cpath, $id)
    {

        $client = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();
        $gitSort = GisSort::where('activity_id', $id)->first();
        $policySort = PolicySort::where('activity_id', $activity->parent_id)->first();

        DB::beginTransaction();
        try {

            $activityNew = new Activity();
            $activityNew->parent_id = $gitSort->activity_id;
            $activityNew->client_id = $activity->client_id;
            $activityNew->affiliate_id = $activity->affiliate_id;
            $activityNew->service_id = Service::SERVICE_PE_RECOGNITION_EXPENSES_MNK;
            $activityNew->state_id = State::REGISTRADO;
            $activityNew->user_id = $activity->user_id;
            $activityNew->save();

            $peEcogniti = new PeRecognitionExpenses();//pe_recognition_expenses
            $peEcogniti->activity_id = $activityNew->id;
            $peEcogniti->type_identification_expense = $gitSort->type_identification_affiliate;
            $peEcogniti->number_identification_expense = $gitSort->number_identification_affiliate;
            $peEcogniti->name_affiliate_expense = $gitSort->name_affiliate;
            $peEcogniti->n_policy_sort_expense = $policySort->id;
            $peEcogniti->date_case_expense = $gitSort->date_accident;

            $peEcogniti->home_phone_expense = $gitSort->cellphone_affiliate;
            $peEcogniti->phone_expense = $gitSort->cellphone_affiliate;
            $peEcogniti->email_affiliate_expense = $gitSort->email_affiliate;
            $peEcogniti->n_case_expense = $gitSort->id;
            $peEcogniti->invoice_concepts = 'funeral_transport';
            $peEcogniti->pay_for_the_funeral = '500000'; //!!! pendiente definir

            $peEcogniti->save();

            $activityAction = ActionController::create($activityNew->id, ActionPeexpenserecognition::REPORTAR_RECONOCIMIENTO_DE_GASTO_FUNERAL_Y_TRASLADO,  'Reportar reconocimiento de gastos funerales y traslado');

            DB::commit();

            return response()->json([
                'status' => 'success',
                'message' => 'GENERADO REPORTAR RECONOCIMIENTO DE GASTOS FUNERAL Y TRASLADO',
                'id' => $activityNew->id], 200);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
            ]);
        }

    }

    public static function reportExpenseRecognitionInvoices($cpath,$id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();
        $gitSort = GisSort::where('activity_id', $id)->first();
        $policySort = PolicySort::where('activity_id', $activity->parent_id)->first();

        DB::beginTransaction();
        try {

            $activityNew = new Activity();
            $activityNew->parent_id = $gitSort->activity_id;
            $activityNew->client_id = $activity->client_id;
            $activityNew->affiliate_id = $activity->affiliate_id;
            $activityNew->service_id = Service::SERVICE_PE_RECOGNITION_EXPENSES_MNK;
            $activityNew->state_id = State::REGISTRADO;
            $activityNew->user_id = $activity->user_id;
            $activityNew->save();

            $peEcogniti = new PeRecognitionExpenses();
            $peEcogniti->activity_id = $activityNew->id;
            $peEcogniti->type_identification_expense = $gitSort->type_identification_affiliate;
            $peEcogniti->number_identification_expense = $gitSort->number_identification_affiliate;
            $peEcogniti->name_affiliate_expense = $gitSort->name_affiliate;
            $peEcogniti->n_policy_sort_expense = $policySort->id;
            $peEcogniti->date_case_expense = $gitSort->date_accident;

            $peEcogniti->home_phone_expense = $gitSort->cellphone_affiliate;
            $peEcogniti->phone_expense = $gitSort->cellphone_affiliate;
            $peEcogniti->email_affiliate_expense = $gitSort->email_affiliate;
            $peEcogniti->n_case_expense = $gitSort->id;
            $peEcogniti->invoice_concepts = 'invoices';
            $peEcogniti->save();

            $activityAction = ActionController::create($activityNew->id, ActionPeexpenserecognition::REPORTAR_RECONOCIMIENTO_DE_GASTO_FACTURAS,  'REPORTAR RECONOCIMIENTO DE GASTO FACTURAS');

            DB::commit();

            return response()->json([
                'status' => 'success',
                'message' => 'GENERADO REPORTAR RECONOCIMIENTO DE GASTO FACTURAS',
                'id' => $activityNew->id], 200);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
            ]);
        }

    }

    public function save(Request $req, $cpath, $id)
    {

        try{

            $client   = Client::where('path', $cpath)->firstOrFail();
            $activity = Activity::where('client_id', $client->id)->where('id', $id)->first();

            $peEcogniti = PeRecognitionExpenses::where('activity_id', $activity->id)->first();

            if (!$peEcogniti) {
                $peEcogniti = new PeRecognitionExpenses();
                $peEcogniti->activity_id  = $id;

                $activity->parent_id = $req->input('parent_id');
                $activity->save();

                if($req->input('invoice_concepts') == 'transport_lodging_meals'){
                    $activityAction = ActionController::create($id, ActionPeexpenserecognition::REPORTAR_RECONOCIMIENTO_TRASLADO_HOSPEDAJE_Y_ALIMENTACION,  'Reportar reconocimiento translado hospedaje y alimentacion');
                }else if($req->input('invoice_concepts') == 'funeral_transport'){
                    $activityAction = ActionController::create($id, ActionPeexpenserecognition::REPORTAR_RECONOCIMIENTO_DE_GASTO_FUNERAL_Y_TRASLADO,  'Reportar reconocimiento de gastos funerales y traslado');
                }else if($req->input('invoices') == 'transport_lodging_meals'){
                    $activityAction = ActionController::create($id, ActionPeexpenserecognition::REPORTAR_RECONOCIMIENTO_DE_GASTO_FACTURAS,  'Reportar reconocimiento de gasto factura');
                }
            }

            $peEcogniti->number_identification_expense = $req->input('type_identification_expense');
            $peEcogniti->number_identification_expense = $req->input('number_identification_expense');
            $peEcogniti->name_affiliate_expense = $req->input('name_affiliate_expense');
            $peEcogniti->home_phone_expense = $req->input('home_phone_expense');
            $peEcogniti->phone_expense = $req->input('phone_expense');
            $peEcogniti->email_affiliate_expense = $req->input('email_affiliate_expense');
            $peEcogniti->n_case_expense = $req->input('n_case_expense');
            $peEcogniti->n_policy_sort_expense = $req->input('n_policy_sort_expense');
            $peEcogniti->id_patrono_expense = $req->input('id_patrono_expense');
            $peEcogniti->name_of_patrono_expense = $req->input('name_of_patrono_expense');

            if ($req->input('date_case_expense_submit')) {
                $resolution_date = $req->input('date_case_expense_submit');
                if (!empty($resolution_date)) {
                    $peEcogniti->date_case_expense = $resolution_date;
                }
            }

            $peEcogniti->additional_documents = $req->input('additional_documents');
            $formattedValue = str_replace(['₡','$', '.', ','], ['', '', '', '.'], $req->input('pay_for_the_funeral'));
            $peEcogniti->pay_for_the_funeral = $formattedValue;
            $invoices_expense_recognition = str_replace(['₡','$', '.', ','], ['', '', '', '.'], $req->input('invoices_expense_recognition'));
            $peEcogniti->invoices_expense_recognition = $invoices_expense_recognition;
            $peEcogniti->invoice_concepts_doctors = $req->input('invoice_concepts_doctors');

            $value_of_medical_expense_invoices = str_replace(['₡','$', '.', ','], ['', '', '', '.'], $req->input('value_of_medical_expense_invoices'));
            $peEcogniti->value_of_medical_expense_invoices = $value_of_medical_expense_invoices;
            $peEcogniti->holder_type = $req->input('holder_type');
            $peEcogniti->type_indentification_titular = $req->input('type_indentification_titular');
            $peEcogniti->number_indentification_titular = $req->input('number_indentification_titular');
            $peEcogniti->name_titular = $req->input('name_titular');
            $peEcogniti->type_person = $req->input('type_person');

            $peEcogniti->account_number_IBAN = $req->input('account_number_IBAN');
            $peEcogniti->type_count = $req->input('type_count');
            $peEcogniti->account_name_banking = $req->input('account_name_banking');
            $accepted_value_of_recognition = str_replace(['₡','$', '.', ','], ['', '', '', '.'], $req->input('accepted_value_of_recognition'));
            $peEcogniti->accepted_value_of_recognition = $accepted_value_of_recognition;
            $peEcogniti->reason_for_acceptance = $req->input('reason_for_acceptance');
            $peEcogniti->reason_for_rejection = $req->input('reason_for_rejection');

            $peEcogniti->general_information = $req->input('general_information');
            $peEcogniti->information_about_the_procedure = $req->input('information_about_the_procedure');
            $peEcogniti->detail_periods_incapacity = $req->input('detail_periods_incapacity');
            $peEcogniti->payment_information = $req->input('payment_information');
            $peEcogniti->observation = $req->input('observation');
            $peEcogniti->review_result = $req->input('review_result');
            $peEcogniti->payment_office = $req->input('payment_office');

            if($req->input('invoice_concepts')){
                $peEcogniti->invoice_concepts = $req->input('invoice_concepts');
            }


            if ($req->input('resolution_date_submit')) {
                $resolution_date = $req->input('resolution_date_submit');
                if (!empty($resolution_date)) {
                    $peEcogniti->resolution_date = $resolution_date;
                }
            }

            $peEcogniti->save();

            $activityPeEco = $peEcogniti->activity_id ?? $req->input('parent_id');

            if ($req->hasFile('death_certificate_file')) {
                $this->storeActivityDocument($req, 'death_certificate_file', 249, $activityPeEco);
            }
            if ($req->hasFile('stamped_or_electronic_invoice')) {
                $this->storeActivityDocument($req, 'stamped_or_electronic_invoice', 250, $activityPeEco);
            }
            if ($req->hasFile('reimbursement_request_holder')) {
                $this->storeActivityDocument($req, 'reimbursement_request_holder', 252, $activityPeEco);
            }
            if ($req->hasFile('travel_accommodation_meals_invoices')) {
                $this->storeMultipleActivityDocuments($req, 'travel_accommodation_meals_invoices', 251, $activityPeEco);
            }
            if ($req->hasFile('medical_expenses_invoices')) {
                $this->storeMultipleActivityDocuments($req, 'medical_expenses_invoices', 253, $activityPeEco);
            }

            DB::commit();
            return redirect()->back()->with('success', 'Registro actualizados.');

        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()->with('error', 'Error:'.$e->getMessage());
        }

    }

    protected function storeActivityDocument(Request $req, $fileKey, $documentId, $activityPeEco)
    {
        try{
            $file = $req->file($fileKey);

            if ($file) {

                $activityDocument = ActivityDocument::where('activity_id', $activityPeEco)
                    ->where('document_id', $documentId)
                    ->first();

                $originalExtension = $req->file($fileKey)->getClientOriginalExtension();
                $uniqueName = Str::random(10) . uniqid() . '.' . $originalExtension;
                $filePath = "documents/{$uniqueName}";

                $req->file($fileKey)->storeAs('documents', $uniqueName, 's3');

                if (!$activityDocument) {
                    $activityDocument = new ActivityDocument();
                }

                $activityDocument->document_id = $documentId; //servic_doc
                $activityDocument->activity_id = $activityPeEco;
                $activityDocument->path = $filePath;
                $activityDocument->uploaded_at = Carbon::now();
                $activityDocument->save();

            }

        } catch (\Exception $e) {
            throw new \Exception('storeActivityDocument:'.$e->getMessage());
        }
    }

    protected function storeMultipleActivityDocuments(Request $req, $fileKey, $documentId, $activityPeEco)
    {
        try{
            $files = $req->file($fileKey);

            if ($files && is_array($files)) {
                $i=1;
                foreach ($files as $file) {

                    $activityDocument = ActivityDocument::where('activity_id', $activityPeEco)
                        ->where('document_id', $documentId)
                        ->where('path', 'like',  '%_file_'.$i.'%')
                        ->first();

                    // Genera un nombre único para el archivo
                    $originalExtension = $file->getClientOriginalExtension();
                    $uniqueName = Str::random(10) . uniqid() . '_file_'.$i.'.' . $originalExtension;
                    $filePath = "documents/{$uniqueName}";

                    // Almacena el archivo en S3 o en el disco configurado
                    $file->storeAs('documents', $uniqueName, 's3');


                    if (!$activityDocument) {
                        $activityDocument = new ActivityDocument();
                    }

                    // Actualiza o crea el registro en la base de datos
                    $activityDocument->document_id = $documentId;
                    $activityDocument->activity_id = $activityPeEco;
                    $activityDocument->path = $filePath;
                    $activityDocument->save();

                    $i++;
                }
            }

        } catch (\Exception $e) {
            throw new \Exception('storeMultipleActivityDocuments:'.$e->getMessage());
        }
    }

    //REPORTAR PAGO RECONOCIMIENTO GASTOS
    public function reportExpenseRecognitionPayment($cpath, $id)
    {

        $client = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();
        $policySort = $activity->parent->parent->policy_sort;

        DB::beginTransaction();

        try {

            $activityAction = ActionController::create(
                $activity->id,
                ActionPeexpenserecognition::REPORTAR_PAGO_RECONOCIMIENTO_DE_GASTOS,
                'REPORTAR PAGO RECONOCIMIENTO DE GASTOS'
            );


            $pdf = PDF::loadView('services.pe_expense_recognition.docs.report_expense_recognition_payment_pdf', [
                'activity' => $activity,
                'watermark' => true,
            ]);

            // Generamos el nombre del archivo
            $document = 'reporte_pago_reconocimiento_gastos';
            $fileName = "{$document}_{$id}.pdf";

            // Guardar el archivo en S3
            $filePath = "activity_action_document/{$fileName}";
            Storage::disk('s3')->put($filePath, $pdf->output());

            // Guardamos el path del documento en la base de datos
            $activityActionDocument = new ActivityActionDocument();
            $activityActionDocument->activity_action_id = $activityAction->id;
            $activityActionDocument->name = $document;
            $activityActionDocument->path = $filePath;
            $activityActionDocument->save();

            $emailIntermediary = $policySort->email;
            $emailTaker = $activity->affiliate->email;
            $nombre = ucwords(mb_strtolower($activity->affiliate->first_name));
            $emails = array_filter([$emailIntermediary, $emailTaker], function ($email) {
                return !empty($email);
            });

            $fecha = Carbon::parse($activity->created_at)->format('d/m/Y');

            $text = [
                "text" => "¡Buen día, {$nombre}!
                
                Nos complace informarle que el {$fecha} realizamos el pago de los gastos por los servicios ofrecidos mediante nuestro Seguro Obligatorio de Riesgos del Trabajo, según se detalla a continuación:
                
                <table style='border-collapse: collapse; width: 100%;'>
                    <thead>
                        <tr style='background-color: #A9D18E; text-align: center;'>
                            <th style='border: 1px solid black; padding: 5px;'>Número de factura</th>
                            <th style='border: 1px solid black; padding: 5px;'>Servicio brindado</th>
                            <th style='border: 1px solid black; padding: 5px;'>Monto</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td style='border: 1px solid black; padding: 5px;'>xxxxx</td>
                            <td style='border: 1px solid black; padding: 5px;'>Xxxxx</td>
                            <td style='border: 1px solid black; padding: 5px;'>¢xxxx</td>
                        </tr>
                        <tr>
                            <td style='border: 1px solid black; padding: 5px;'>xxxxx</td>
                            <td style='border: 1px solid black; padding: 5px;'>Xxxxx</td>
                            <td style='border: 1px solid black; padding: 5px;'>¢xxxx</td>
                        </tr>
                        <tr style='background-color: #A9D18E;'>
                            <td colspan='2' style='border: 1px solid black; padding: 5px; text-align: right;'><strong>Total:</strong></td>
                            <td style='border: 1px solid black; padding: 5px;'><strong>¢xxxx</strong></td>
                        </tr>
                    </tbody>
                </table>
                
                Las sumas correspondientes fueron depositadas en su cuenta IBAN autorizada para este efecto. Por favor, si tiene alguna duda al respecto, contáctenos al correo electrónico <EMAIL> o al teléfono 4102-7600 ext. 8129-8130.¡Para nosotros será un gusto servirle!
                
                Agradecemos su confianza. Nuestro propósito es fortalecer la prevención en salud y seguridad laboral del país.",
                "sender" => 'mnk aseguramiento'
            ];
            $attachments = [
                [
                    'path' => $filePath,
                    'name' => basename($filePath),
                    'type' => 'PDF'
                ]
            ];

            $subject = "Pago por reconocimiento de gastos. Caso " . $policySort->formatSortNumber();

            $this->sendEmail($emails, $subject, $text, $attachments, $client, $policySort->activity_id, $id, $policySort);


            DB::commit();

        } catch (Exception $e) {
            DB::rollback();
            return response()->json([
                'status' => 'error',
                'message' => 'Ocurrió un error al enviar el correo.'
            ], 500);
        }

        return response()->json([
            'status' => 'success',
            'message' => 'Registro creado y email enviado exitosamente'
        ]);

    }
    private function sendEmail($emails, $subject, $text, $attachments, $client, $id, $activityAction, $policySort)
    {
        $mailSent = new SendDocumentDataBase(
            implode(',', $emails),         // Correos a enviar
            $subject,                      // Asunto del correo
            "<EMAIL>",            // Remitente
            $subject,                      // Asunto
            $text,                         // Cuerpo del email
            "<EMAIL>",  // Email de respuesta
            $attachments,                  // Archivos adjuntos
            "send_document_db",            // Tipo de envío
            $client,                       // Información del cliente
            request()->getHost(),          // Dominio
            $id,                           // ID de la actividad
            $activityAction,           // ID de la acción de la actividad
            $policySort->activity->service->id // ID del servicio
        );

        // Capturar el resultado del envío
        $result = $mailSent->sendMail();

        //Registramos los datos del correo enviado para la trazabilidad
        $mailBoardController = new MailBoardController();
        $mailBoardController->createRegisterMail(
            $id,
            $policySort->activity->service->id, 
            $policySort->consecutive, 
            'Tomador', 
            $policySort->activity->affiliate->full_name, 
            $policySort->activity->affiliate->doc_number, 
            $subject, 
            $text,
            $emails, 
            $result,
            $attachments
        );

    }

    public function getDatacase($cpath ,$id)
    {
        try {

            if (!$id) {
                throw new \Exception('Código del caso no proporcionado.');
            }

            $gisSort = GisSort::where('id', $id)->first();

            if (!$gisSort) {
                throw new \Exception('Código del caso no encontrado.');
            }

            $data = [
                'n_case_expense' => $id,
                'n_policy_sort_expense' => $gisSort->activity->parent->policy_sort->formatSortNumber(),
                'id_patrono_expense' => $gisSort->activity->parent->affiliate->doc_number,
                'name_of_patrono_expense' => ucwords(strtolower($gisSort->activity->parent->affiliate->full_name)),
                'date_case_expense' => ucfirst(strftime('%A %e de %B del %Y',strtotime($gisSort->activity->gis_sort->created_at))),
                'parent_id' => $gisSort->activity->id
            ];

            return response()->json([
                'success' => true,
                'data' => $data
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Ocurrió un error al consultar el numero de caso.',
                'error' => $e->getMessage()
            ], 500);
        }
    }


}