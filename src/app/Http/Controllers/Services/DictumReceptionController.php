<?php

namespace App\Http\Controllers\Services;

use App\Activity;
use App\Client;
use App\Http\Controllers\Controller;
use App\DictumReception;
use App\DictumReceptionDiagnostic;
use App\DictumReceptionNc;
use DateTime;
use DB;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\View;
use PDF;

class DictumReceptionController extends Controller
{

    public function form(Request $req, $cpath, $id)
    {
        $client   = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();
        $date_action = null;
        foreach ($activity->activity_actions as $aa) {
            if ($aa->action_id == 1471) {
                $date_action = $aa->created_at;
            }
            if ($aa->action_id == 1472) {
                $date_action = $aa->created_at;
            }
        }
        if ($date_action !== null) {
            $dictum_reception = DictumReception::query()
                ->where('activity_id', '=', $activity->id)
                ->first();
            if ($dictum_reception->remission_argumentation_date == null) {
                $dictum_reception->remission_argumentation_date = $date_action;
                $dictum_reception->save();
            }
        }
        return view('services.dictum_reception.form', ['activity' => $activity, 'client' => $client->path]);
    }

    public function save(Request $req, $cpath, $id)
    {
        $client   = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();

        DB::beginTransaction();
        try {
            if ($activity->dictum_reception == null) {
                $dictum_reception              = new DictumReception;
                $dictum_reception->activity_id = $activity->id;
            } else {
                $dictum_reception = $activity->dictum_reception;
            }


            $dictum_reception->notify_dictum_date              = $req->input('notify_dictum_date_submit');
            $dictum_reception->dictum_date                     = $req->input('dictum_date_submit');
            $dictum_reception->dictum_number                   = $req->input('dictum_number');
            $dictum_reception->event_type                      = $req->input('event_type');
            $dictum_reception->sinister_number                 = $req->input('sinister_number');
            $dictum_reception->event_date                      = $req->input('event_date_submit');
            $dictum_reception->sinister_radication_date        = $req->input('sinister_radication_date_submit');
            $dictum_reception->qualifier_entity                = $req->input('qualifier_entity');
            $dictum_reception->entity_name                     = $req->input('entity_name');
            $dictum_reception->pronouncement                   = $req->input('pronouncement');
            $dictum_reception->remission_argumentation_date    = $req->input('remission_argumentation_date_submit');
            $dictum_reception->send_method                     = $req->input('send_method');
            $dictum_reception->argumentation                   = $req->input('argumentation');
            $dictum_reception->subject                   = $req->input('subject');
            $dictum_reception->out_radication_num              = $req->input('out_radication_num');
            $dictum_reception->state                           = $req->input('state');
            $dictum_reception->effective_notification          = $req->input('effective_notification');
            $dictum_reception->effective_notification_date     = $req->input('effective_notification_date');
            $dictum_reception->notifications = $req->input('notifications');

            $dictum_reception->save();

            $diagnostics = $req->input('diagnostics');

            DictumReceptionDiagnostic::where('dictum_reception_id', $dictum_reception->id)
                ->whereNotIn('id', $diagnostics['id'])
                ->delete();

            for ($i = 0; $i < count($diagnostics['id']); $i++) {
                if ($diagnostics['id'][$i] != null) {
                    $dictum_reception_diagnostic = DictumReceptionDiagnostic::where('dictum_reception_id', $dictum_reception->id)
                        ->where('id', $diagnostics['id'][$i])
                        ->firstOrFail();
                } else {
                    $dictum_reception_diagnostic           = new DictumReceptionDiagnostic;
                    $dictum_reception_diagnostic->dictum_reception_id = $dictum_reception->id;
                }
                $dictum_reception_diagnostic->cod        = newCodeCie10($diagnostics['cod'][$i]);
                $dictum_reception_diagnostic->description = $diagnostics['description'][$i];
                $dictum_reception_diagnostic->laterality = $diagnostics['laterality'][$i];
                $dictum_reception_diagnostic->origin = $diagnostics['origin'][$i];

                $dictum_reception_diagnostic->save();
            }
//            Inicio NCS
            $ncs = $req->input('ncs');
            if ($ncs) {
                DictumReceptionNc::where('dictum_reception_id', $dictum_reception->id)
                    ->whereNotIn('id', $ncs['id'])
                    ->delete();

                for ($i = 0; $i < count($ncs['id']); $i++) {
                    if ($ncs['description'][$i] != null) {
                        if ($ncs['id'][$i] != null) {
                            $pcl_nc = DictumReceptionNc::where('dictum_reception_id', $dictum_reception->id)
                                ->where('id', $ncs['id'][$i])
                                ->firstOrFail();
                        } else {
                            $pcl_nc = new DictumReceptionNc;
                            $pcl_nc->dictum_reception_id = $dictum_reception->id;
                        }

                        $pcl_nc->description = $ncs['description'][$i];
                        $pcl_nc->save();
                    }
                }
            } else {
                DictumReceptionNc::where('dictum_reception_id', $dictum_reception->id)
                    ->delete();
            }

            DB::commit();

        } catch (Exception $e) {
            DB::rollback();
        }

        return redirect("servicio/$id/dictum_reception");
    }

    public function pdf(Request $req, $cpath, $id)
    {
        $client   = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();

        $dictum_reception              = new DictumReception;
        $dictum_reception->activity_id = $activity->id;
        $dictum_reception->setRelation('activity', Activity::findOrFail($id));

        $dictum_reception->dictum_date                     = $req->input('dictum_date_submit');
        $dictum_reception->dictum_number                   = $req->input('dictum_number');
        $dictum_reception->event_type                      = $req->input('event_type');
        $dictum_reception->sinister_number                 = $req->input('sinister_number');
        $dictum_reception->event_date                      = $req->input('event_date');
        $dictum_reception->sinister_radication_date        = $req->input('sinister_radication_date_submit');
        $dictum_reception->qualifier_entity                = $req->input('qualifier_entity');
        $dictum_reception->entity_name                     = $req->input('entity_name');
        $dictum_reception->pronouncement                   = $req->input('pronouncement');
        $dictum_reception->remission_argumentation_date    = $req->input('remission_argumentation_date_submit');
        $dictum_reception->send_method                     = $req->input('send_method');
        $dictum_reception->argumentation                   = $req->input('argumentation');
        $dictum_reception->subject                   = $req->input('subject');
        $dictum_reception->out_radication_num              = $req->input('out_radication_num');
        $dictum_reception->state                           = $req->input('state');
        $dictum_reception->effective_notification          = $req->input('effective_notification');
        $dictum_reception->effective_notification_date     = $req->input('effective_notification_date');

        $dictum_reception->created_at = new DateTime();

        $diagnostics = $req->input('diagnostics');
        for ($i = 0; $i < count($diagnostics['id']); $i++) {
            if ($diagnostics['cod'][$i] != null && $diagnostics['description'][$i] != null) {
                $dictum_reception_diagnostic              = new DictumReceptionDiagnostic;
                $dictum_reception_diagnostic->cod        = newCodeCie10($diagnostics['cod'][$i]);
                $dictum_reception_diagnostic->description = $diagnostics['description'][$i];
                $dictum_reception_diagnostic->laterality      = $diagnostics['laterality'][$i];
                $dictum_reception_diagnostic->origin      = $diagnostics['origin'][$i];

                $dictum_reception->diagnostics->add($dictum_reception_diagnostic);
            }
        }

        if ($activity->dictum_reception->pronouncement == 'Acuerdo' || $activity->dictum_reception->pronouncement == 'Sin cobertura') {
            $pdf = PDF::loadView('services.dictum_reception.docs.pronouncement_agree_pdf', [
                'dictum_reception' => $dictum_reception,
                'activity'       => $activity,
                'watermark'      => true]);
        }
        else if ($activity->dictum_reception->pronouncement == 'Desacuerdo') {
            $pdf = PDF::loadView('services.dictum_reception.docs.pronouncement_disagree_pdf', [
                'dictum_reception' => $dictum_reception,
                'activity'       => $activity,
                'watermark'      => true]);
        } else {
            $pdf = PDF::loadView('services.dictum_reception.docs.dictum_reception_pdf', [
                'dictum_reception' => $dictum_reception,
                'activity'       => $activity,
                'watermark'      => true]);
        }

        return $pdf->stream('preview.pdf');
    }
}
