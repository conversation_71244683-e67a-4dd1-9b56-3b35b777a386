<?php

namespace App\Http\Controllers\Services;

use App\AccountingEntry;
use App\Actions\ActionGisSort;
use App\Actions\ActionPeaffiliatepayments;
use App\Actions\ActionPeItSort;
use App\Actions\ActionReintegrate;
use App\Activity;
use App\GisSort;
use App\Http\Controllers\ActionController;
use App\Http\Controllers\Controller;
use App\Http\Controllers\Integrations\WebserviceAcselController;
use App\indicatives_003;
use App\PeitFractionSort;
use App\PeitInabilitySort;
use App\PeItSort;
use App\PolicySort;
use App\PolicySortCollection;
use App\Providers\AppServiceProvider;
use App\ReleasePpndColonesReserve;
use App\ReleasePpndDollarsReserve;
use App\ReleaseReinsuranceColonesReserve;
use App\ReleaseReinsuranceDollarsReserve;
use App\Service;
use App\State;
use App\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class AccountingEntryController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth')->except([
            'reportAccountCaseOne003',
            'reportAccountCaseTwo003',
            'autoAccountCase308',
            'reportAccountCase108',
            'autoAccountCase305',
            'autoAccountCase150',
            'autoAccountCase301',
            'reportAccountCaseOneTwo153',
            'autoAccountCase306',
            'reportAccountCase151',
            'autoAccountCase138',
            'autoAccountCase307',
            'reportAccountCase050',
            'autoAccountCase087'
        ]);
    }

    //Asiento contable emisión de la póliza cuando es tipo correduria y moneda colones
    public function reportAccountCaseOne001($policy){

        //Codigo unico para cada bloque de asientos contables
        $nextEntryCode = AccountingEntry::max('entry_code');
        $nextEntryCode = sprintf('%04d',($nextEntryCode ?? 0) + 1);

        //Traemos el pago mediante l apoliza asociada
        $activityPayment = Activity::where('parent_id', $policy->activity->id)
            ->where("service_id", Service::SERVICE_POLICY_SORT_COLLECTION_MNK)
            ->whereHas('policy_sort_collection', function ($query) {
                $query->where('payment_status', PolicySortCollection::PAYMENT_STATUS_APPROVED);
            })
            ->latest()
            ->first();

        //Traemos los porcentajes de las tarifas para los calculos 
        $accountsPercentages = AppServiceProvider::$ACCOUNTS_PERCENTAGES;

        $paymentId = $activityPayment->policy_sort_collection->id;
        $amountPolicy = $activityPayment->policy_sort_collection->total_amount ?? 0;
        // Array con valores específicos de debit y credit para cada fila

        //Fila 1 
        $amount_policy =  $amountPolicy;
        //Fila 2 
        $valueCredit2 = $amount_policy * $accountsPercentages[3];
        //Fila 3 
        $valueCredit3 = $amount_policy * $accountsPercentages[5];
        //Fila 5 
        $valueCredit5 = $amountPolicy * 0.10;
        //Fila 6
        $valueDebit6 = $amountPolicy  * 0.10;
        //Fila 7
        $valueDebit7 = $amountPolicy  * 0;
        //Fila 8 
        $valueDebit8 = $amountPolicy * $accountsPercentages[3];
        //Fila 9 
        $valueDebit9 = $amountPolicy * $accountsPercentages[5];
        //Fila 10 
        $valueCredit10 = $amountPolicy;
        //Fila 11 
        $valueCredit11 = $amountPolicy * 0 * $accountsPercentages[23];
        //Fila 12
        $valueCredit12 = $amountPolicy * 0 * $accountsPercentages[25];
        //Fila 13
        $valueCredit13 = $amountPolicy * 0 * $accountsPercentages[27];
        //Fila 14
        $valueCredit14 = $amountPolicy * 0 * $accountsPercentages[19];

        //Fila 4
        $valueCredit4 = ($amountPolicy * 0) - $valueCredit11 - $valueCredit12 - $valueCredit13 - $valueCredit14;

        $t_debit  =  $amount_policy + $valueDebit6 + $valueDebit7  + $valueDebit8 + $valueDebit9;
        $t_credit =  $valueCredit2 + $valueCredit3 +  $valueCredit4 + $valueCredit5 + $valueCredit10 + $valueCredit11 + $valueCredit12 + $valueCredit13 + $valueCredit14;

        $values = [
            ['debit' => $amount_policy, 'credit' => 0,                  'movement_type'=> 'D', 'movement_type_org'=> 'D', 'cta_1' => '001', 'cta_2' => '040', 'cta_3' => '030', 'cta_4' => '010', 'cta_5' => '001', 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'PRIMAS', 'cod_cpto' => 'RECIBO', 'cor_relativo'  => '002', 'ctaux' => '**************'],
            ['debit' => 0,              'credit' => $valueCredit2,      'movement_type'=> 'C', 'movement_type_org'=> 'C', 'cta_1' => '002', 'cta_2' => '040', 'cta_3' => '020', 'cta_4' => '320', 'cta_5' => '001', 'cta_6' => '000', 'cta_7' => '000', 'cta_8' => '000', 'cod_grupo_cpto' => 'IMPUES', 'cod_cpto' => 'IMPBOM', 'cor_relativo'  => '010', 'ctaux' => '**************'],
            ['debit' => 0,              'credit' => $valueCredit3,      'movement_type'=> 'C', 'movement_type_org'=> 'C', 'cta_1' => '002', 'cta_2' => '040', 'cta_3' => '020', 'cta_4' => '330', 'cta_5' => '001', 'cta_6' => '000', 'cta_7' => '000', 'cta_8' => '000', 'cod_grupo_cpto' => 'IMPUES', 'cod_cpto' => 'IMPINE', 'cor_relativo'  => '063', 'ctaux' => '**************'],
            ['debit' => 0,              'credit' => $valueCredit4,      'movement_type'=> 'C', 'movement_type_org'=> 'C', 'cta_1' => '002', 'cta_2' => '060', 'cta_3' => '010', 'cta_4' => '010', 'cta_5' => '001', 'cta_6' => '000', 'cta_7' => '000', 'cta_8' => '000', 'cod_grupo_cpto' => 'IMPUES', 'cod_cpto' => 'IMPREA', 'cor_relativo'  => '012', 'ctaux' => '0000000000SORT'],
            ['debit' => 0,              'credit' => $valueCredit5,      'movement_type'=> 'C', 'movement_type_org'=> 'C', 'cta_1' => '002', 'cta_2' => '070', 'cta_3' => '020', 'cta_4' => '010', 'cta_5' => '001', 'cta_6' => '010', 'cta_7' => '010', 'cta_8' => '180', 'cod_grupo_cpto' => 'PRIMAS', 'cod_cpto' => 'RECIBO', 'cor_relativo'  => '003', 'ctaux' => '**************'],
            ['debit' => $valueDebit6,   'credit' => 0,                  'movement_type'=> 'D', 'movement_type_org'=> 'D', 'cta_1' => '004', 'cta_2' => '030', 'cta_3' => '010', 'cta_4' => '010', 'cta_5' => '001', 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'PRIMAS', 'cod_cpto' => 'RECIBO', 'cor_relativo'  => '020', 'ctaux' => '**************'],
            ['debit' => $valueDebit7,   'credit' => 0,                  'movement_type'=> 'D', 'movement_type_org'=> 'D', 'cta_1' => '004', 'cta_2' => '040', 'cta_3' => '010', 'cta_4' => '010', 'cta_5' => '001', 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'FACULT', 'cod_cpto' => 'RESER', 'cor_relativo'   => '022', 'ctaux' => '**************'],
            ['debit' => $valueDebit8,   'credit' => 0,                  'movement_type'=> 'D', 'movement_type_org'=> 'D', 'cta_1' => '004', 'cta_2' => '060', 'cta_3' => '090', 'cta_4' => '200', 'cta_5' => '001', 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'IMPUES', 'cod_cpto' => 'IMPBOM', 'cor_relativo'  => '019', 'ctaux' => '**************'],
            ['debit' => $valueDebit9,   'credit' => 0,                  'movement_type'=> 'D', 'movement_type_org'=> 'D', 'cta_1' => '004', 'cta_2' => '060', 'cta_3' => '090', 'cta_4' => '230', 'cta_5' => '001', 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'IMPUES', 'cod_cpto' => 'IMPINE', 'cor_relativo'  => '064', 'ctaux' => '**************'],
            ['debit' => 0,              'credit' => $valueCredit10,     'movement_type'=> 'C', 'movement_type_org'=> 'C', 'cta_1' => '005', 'cta_2' => '020', 'cta_3' => '010', 'cta_4' => '010', 'cta_5' => '001', 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'PRIMAS', 'cod_cpto' => 'RECIBO', 'cor_relativo'  => '024', 'ctaux' => '**************'],
            ['debit' => 0,              'credit' => $valueCredit11,     'movement_type'=> 'C', 'movement_type_org'=> 'C', 'cta_1' => '005', 'cta_2' => '030', 'cta_3' => '010', 'cta_4' => '010', 'cta_5' => '001', 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'COREAS', 'cod_cpto' => 'COMIS', 'cor_relativo'   => '025', 'ctaux' => '**************'],
            ['debit' => 0,              'credit' => $valueCredit12,     'movement_type'=> 'C', 'movement_type_org'=> 'C', 'cta_1' => '005', 'cta_2' => '030', 'cta_3' => '010', 'cta_4' => '010', 'cta_5' => '001', 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'FACULT', 'cod_cpto' => 'IMPBOM', 'cor_relativo'  => '027', 'ctaux' => '**************'],
            ['debit' => 0,              'credit' => $valueCredit13,     'movement_type'=> 'C', 'movement_type_org'=> 'C', 'cta_1' => '005', 'cta_2' => '030', 'cta_3' => '010', 'cta_4' => '010', 'cta_5' => '001', 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'FACULT', 'cod_cpto' => 'IMPMUN', 'cor_relativo'  => '030', 'ctaux' => '**************'],
            ['debit' => 0,              'credit' => $valueCredit14,     'movement_type'=> 'C', 'movement_type_org'=> 'C', 'cta_1' => '005', 'cta_2' => '030', 'cta_3' => '010', 'cta_4' => '010', 'cta_5' => '001', 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'FACULT', 'cod_cpto' => 'IMPINE', 'cor_relativo'  => '065', 'ctaux' => '**************'],
        ];
    
        $cod_cia = '01';
        // Array para almacenar los registros completos
        $data = [];

        $username = preg_replace('/^CO-/', '', $policy->code); // Remover el prefijo "CO-"

        $user = User::where(function ($query) use ($username) {
            $query->where('username', $username) // Buscar directamente sin "CO-"
            ->orWhere('username', 'CO-' . $username) // Buscar con "CO-"
            ->orWhere('code_mnk',$username)
                ->orWhere('code_mnk','CO-' . $username);
        })->first();

        // Recorre cada par de valores de debit y credit
        foreach ($values as $value) {
            $idecta = "{$cod_cia}{$value['cta_1']}{$value['cta_2']}{$value['cta_3']}{$value['cta_4']}{$value['cta_5']}{$value['cta_6']}{$value['cta_7']}{$value['cta_8']}000000{$value['ctaux']}";

            $data[] = [
                'cod_cia' =>  $cod_cia,
                'cod_oper' => '001',
                'receipt_number' => "RT-$nextEntryCode",
                'receipt_status' => 'ACT',
                'number_policy' => $policy->formatNumberConsecutive(),
                'number_payment' => $paymentId,
                'receipt_type' => '001',
                'date_register' => Carbon::now(),
                'descoper' => 'EMISION DE POLIZA (AM)',
                'cod_grupo_cpto' => $value['cod_grupo_cpto'],
                'cod_cpto' => $value['cod_cpto'],
                'cor_relativo' => $value['cor_relativo'],
                'cod_ramo' => 'SORT',
                'detail_movement' => 'EMISION DE POLIZA (AM)-'.$policy->formatNumberConsecutive().'-'.$paymentId.'-'."RT-$nextEntryCode".'-'.$policy->activity->affiliate->first_name.$value['cod_cpto'],
                'debit' => $value['debit'],
                'credit' => $value['credit'],
                't_debit' => $t_debit,
                't_credit' => $t_credit,
                'difference' => 0,
                'movement_type' => $value['movement_type'],
                'movement_type_org' => $value['movement_type_org'],
                'idecta' => $idecta,
                'cta_1' => $value['cta_1'],
                'cta_2' => $value['cta_2'],
                'cta_3' => $value['cta_3'],
                'cta_4' => $value['cta_4'],
                'cta_5' => $value['cta_5'],
                'cta_6' => $value['cta_6'],
                'cta_7' => $value['cta_7'],
                'cta_8' => $value['cta_8'],
                'cta_9' => '000',
                'cta_10' => '000',
                'document_type' => $policy->activity->affiliate->doc_type,
                'document_number' => $policy->activity->affiliate->doc_number,
                'name_taker' => $policy->activity->affiliate->full_name,
                'cod_intermediary' =>  isset($user) && isset($user->code_correduria) ? $user->code_correduria : $policy->code,
                'cod_moneda' => 'CO',
                'exchange_rate' => 0,
                'entry_code' => $nextEntryCode,
                'ctaux'  =>$value['ctaux'],
                'type' => isset($activityPayment) ? ($activityPayment->policy_sort_collection->type_receipt ?? '') : ''
            ];
        }

        // Inserta todos los registros en la base de datos tabla de asientos contables
        AccountingEntry::insert($data);
    }

    //Asiento contable emisión de la póliza cuando es tipo corredor-independiente y moneda colones
    public function reportAccountCaseTwo001($policy){

        //Codigo unico para cada bloque de asientos contables
        $nextEntryCode = AccountingEntry::max('entry_code');
        $nextEntryCode = sprintf('%04d',($nextEntryCode ?? 0) + 1);

        //Traemos el pago mediante l apoliza asociada
        $activityPayment = Activity::where('parent_id', $policy->activity->id)
            ->where("service_id", Service::SERVICE_POLICY_SORT_COLLECTION_MNK)
            ->whereHas('policy_sort_collection', function ($query) {
                $query->where('payment_status', PolicySortCollection::PAYMENT_STATUS_APPROVED);
            })
            ->latest()
            ->first();

        //Traemos los porcentajes de las tarifas para los calculos 
        $accountsPercentages = AppServiceProvider::$ACCOUNTS_PERCENTAGES;

        $paymentId = $activityPayment->policy_sort_collection->id;
        $amountPolicy = $activityPayment->policy_sort_collection->total_amount ?? 0;
        // Array con valores específicos de debit y credit para cada fila

        //Fila 1 
        $amount_policy =  $amountPolicy;
        //Fila 2 
        $valueCredit2 = $amount_policy * $accountsPercentages[3];
        //Fila 3 
        $valueCredit3 = $amount_policy * $accountsPercentages[5];
        //Fila 5 
        $valueCredit5 = $amountPolicy * 0.10;
        //Fila 6
        $valueDebit6 = $amountPolicy  * 0.10;
        //Fila 7
        $valueDebit7 = $amountPolicy  * 0;
        //Fila 8 
        $valueDebit8 = $amountPolicy * $accountsPercentages[3];
        //Fila 9 
        $valueDebit9 = $amountPolicy * $accountsPercentages[5];
        //Fila 10 
        $valueCredit10 = $amountPolicy;
        //Fila 11 
        $valueCredit11 = $amountPolicy * 0 * $accountsPercentages[23];
        //Fila 12
        $valueCredit12 = $amountPolicy * 0 * $accountsPercentages[25];
        //Fila 13
        $valueCredit13 = $amountPolicy * 0 * $accountsPercentages[27];
        //Fila 14
        $valueCredit14 = $amountPolicy * 0 * $accountsPercentages[19];
        //Fila 4
        $valueCredit4 = ($amountPolicy * 0) - $valueCredit11 - $valueCredit12 - $valueCredit13 - $valueCredit14;

        $t_debit  =  $amount_policy + $valueDebit6 + $valueDebit7  + $valueDebit8 + $valueDebit9;
        $t_credit =  $valueCredit2 + $valueCredit3 +  $valueCredit4 + $valueCredit5 + $valueCredit10 + $valueCredit11 + $valueCredit12 + $valueCredit13 + $valueCredit14;

        $username = preg_replace('/^CO-/', '', $policy->code); // Remover el prefijo "CO-"

        $user = User::where(function ($query) use ($username) {
            $query->where('username', $username) // Buscar directamente sin "CO-"
            ->orWhere('username', 'CO-' . $username) // Buscar con "CO-"
            ->orWhere('code_mnk',$username)
                ->orWhere('code_mnk','CO-' . $username);
        })->first();


        $values = [
            ['debit' => $amount_policy, 'credit' => 0,                  'movement_type'=> 'D', 'movement_type_org'=> 'D', 'cta_1' => '001', 'cta_2' => '040', 'cta_3' => '030', 'cta_4' => '010', 'cta_5' => '001', 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'PRIMAS', 'cod_cpto' => 'RECIBO', 'cor_relativo'  => '002', 'ctaux' => '**************'],
            ['debit' => 0,              'credit' => $valueCredit2,      'movement_type'=> 'C', 'movement_type_org'=> 'C', 'cta_1' => '002', 'cta_2' => '040', 'cta_3' => '020', 'cta_4' => '320', 'cta_5' => '001', 'cta_6' => '000', 'cta_7' => '000', 'cta_8' => '000', 'cod_grupo_cpto' => 'IMPUES', 'cod_cpto' => 'IMPBOM', 'cor_relativo'  => '010', 'ctaux' => '**************'],
            ['debit' => 0,              'credit' => $valueCredit3,      'movement_type'=> 'C', 'movement_type_org'=> 'C', 'cta_1' => '002', 'cta_2' => '040', 'cta_3' => '020', 'cta_4' => '330', 'cta_5' => '001', 'cta_6' => '000', 'cta_7' => '000', 'cta_8' => '000', 'cod_grupo_cpto' => 'IMPUES', 'cod_cpto' => 'IMPINE', 'cor_relativo'  => '063', 'ctaux' => '**************'],
            ['debit' => 0,              'credit' => $valueCredit4,      'movement_type'=> 'C', 'movement_type_org'=> 'C', 'cta_1' => '002', 'cta_2' => '060', 'cta_3' => '010', 'cta_4' => '010', 'cta_5' => '001', 'cta_6' => '000', 'cta_7' => '000', 'cta_8' => '000', 'cod_grupo_cpto' => 'IMPUES', 'cod_cpto' => 'IMPREA', 'cor_relativo'  => '012', 'ctaux' => '0000000000SORT'],
            ['debit' => 0,              'credit' => $valueCredit5,      'movement_type'=> 'C', 'movement_type_org'=> 'C', 'cta_1' => '002', 'cta_2' => '070', 'cta_3' => '020', 'cta_4' => '010', 'cta_5' => '001', 'cta_6' => '020', 'cta_7' => '010', 'cta_8' => '180', 'cod_grupo_cpto' => 'PRIMAS', 'cod_cpto' => 'RECIBO', 'cor_relativo'  => '003', 'ctaux' => '**************'],
            ['debit' => $valueDebit6,   'credit' => 0,                  'movement_type'=> 'D', 'movement_type_org'=> 'D', 'cta_1' => '004', 'cta_2' => '030', 'cta_3' => '010', 'cta_4' => '010', 'cta_5' => '001', 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'PRIMAS', 'cod_cpto' => 'RECIBO', 'cor_relativo'  => '020', 'ctaux' => '**************'],
            ['debit' => $valueDebit7,   'credit' => 0,                  'movement_type'=> 'D', 'movement_type_org'=> 'D', 'cta_1' => '004', 'cta_2' => '040', 'cta_3' => '010', 'cta_4' => '010', 'cta_5' => '001', 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'FACULT', 'cod_cpto' => 'RESER', 'cor_relativo'   => '022', 'ctaux' => '**************'],
            ['debit' => $valueDebit8,   'credit' => 0,                  'movement_type'=> 'D', 'movement_type_org'=> 'D', 'cta_1' => '004', 'cta_2' => '060', 'cta_3' => '090', 'cta_4' => '200', 'cta_5' => '001', 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'IMPUES', 'cod_cpto' => 'IMPBOM', 'cor_relativo'  => '019', 'ctaux' => '**************'],
            ['debit' => $valueDebit9,   'credit' => 0,                  'movement_type'=> 'D', 'movement_type_org'=> 'D', 'cta_1' => '004', 'cta_2' => '060', 'cta_3' => '090', 'cta_4' => '230', 'cta_5' => '001', 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'IMPUES', 'cod_cpto' => 'IMPINE', 'cor_relativo'  => '064', 'ctaux' => '**************'],
            ['debit' => 0,              'credit' => $valueCredit10,     'movement_type'=> 'C', 'movement_type_org'=> 'C', 'cta_1' => '005', 'cta_2' => '020', 'cta_3' => '010', 'cta_4' => '010', 'cta_5' => '001', 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'PRIMAS', 'cod_cpto' => 'RECIBO', 'cor_relativo'  => '024', 'ctaux' => '**************'],
            ['debit' => 0,              'credit' => $valueCredit11,     'movement_type'=> 'C', 'movement_type_org'=> 'C', 'cta_1' => '005', 'cta_2' => '030', 'cta_3' => '010', 'cta_4' => '010', 'cta_5' => '001', 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'COREAS', 'cod_cpto' => 'COMIS', 'cor_relativo'   => '025', 'ctaux' => '**************'],
            ['debit' => 0,              'credit' => $valueCredit12,     'movement_type'=> 'C', 'movement_type_org'=> 'C', 'cta_1' => '005', 'cta_2' => '030', 'cta_3' => '010', 'cta_4' => '010', 'cta_5' => '001', 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'FACULT', 'cod_cpto' => 'IMPBOM', 'cor_relativo'  => '027', 'ctaux' => '**************'],
            ['debit' => 0,              'credit' => $valueCredit13,     'movement_type'=> 'C', 'movement_type_org'=> 'C', 'cta_1' => '005', 'cta_2' => '030', 'cta_3' => '010', 'cta_4' => '010', 'cta_5' => '001', 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'FACULT', 'cod_cpto' => 'IMPMUN', 'cor_relativo'  => '030', 'ctaux' => '**************'],
            ['debit' => 0,              'credit' => $valueCredit14,     'movement_type'=> 'C', 'movement_type_org'=> 'C', 'cta_1' => '005', 'cta_2' => '030', 'cta_3' => '010', 'cta_4' => '010', 'cta_5' => '001', 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'FACULT', 'cod_cpto' => 'IMPINE', 'cor_relativo'  => '065', 'ctaux' => '**************'],
        ];

    
        $cod_cia = '01';
        // Array para almacenar los registros completos
        $data = [];

        // Recorre cada par de valores de debit y credit
        foreach ($values as $value) {
            $idecta = "{$cod_cia}{$value['cta_1']}{$value['cta_2']}{$value['cta_3']}{$value['cta_4']}{$value['cta_5']}{$value['cta_6']}{$value['cta_7']}{$value['cta_8']}000000{$value['ctaux']}";

            $data[] = [
                'cod_cia' =>  $cod_cia,
                'cod_oper' => '001',
                'receipt_number' => "RT-$nextEntryCode",
                'receipt_status' => 'ACT',
                'number_policy' => $policy->formatNumberConsecutive(),
                'number_payment' => $paymentId,
                'receipt_type' => '001',
                'date_register' => Carbon::now(),
                'descoper' => 'EMISION DE POLIZA (AM)',
                'cod_grupo_cpto' => $value['cod_grupo_cpto'],
                'cod_cpto' => $value['cod_cpto'],
                'cor_relativo' => $value['cor_relativo'],
                'cod_ramo' => 'SORT',
                'detail_movement' => 'EMISION DE POLIZA (AM)-'.$policy->formatNumberConsecutive().'-'.$paymentId.'-'."RT-$nextEntryCode".'-'.$policy->activity->affiliate->first_name.$value['cod_cpto'],
                'debit' => $value['debit'],
                'credit' => $value['credit'],
                't_debit' => $t_debit,
                't_credit' => $t_credit,
                'difference' => 0,
                'movement_type' => $value['movement_type'],
                'movement_type_org' => $value['movement_type_org'],
                'idecta' => $idecta,
                'cta_1' => $value['cta_1'],
                'cta_2' => $value['cta_2'],
                'cta_3' => $value['cta_3'],
                'cta_4' => $value['cta_4'],
                'cta_5' => $value['cta_5'],
                'cta_6' => $value['cta_6'],
                'cta_7' => $value['cta_7'],
                'cta_8' => $value['cta_8'],
                'cta_9' => '000',
                'cta_10' => '000',
                'document_type' => $policy->activity->affiliate->doc_type,
                'document_number' => $policy->activity->affiliate->doc_number,
                'name_taker' => $policy->activity->affiliate->full_name,
                'cod_intermediary' =>  isset($user) && isset($user->code_correduria) ? $user->code_correduria : $policy->code ,
                'cod_moneda' => 'CO',
                'exchange_rate' => 0,
                'entry_code' => $nextEntryCode,
                'ctaux'  =>$value['ctaux'],
                'type' => isset($activityPayment) ? ($activityPayment->policy_sort_collection->type_receipt ?? '') : ''
            ];
        }

        // Inserta todos los registros en la base de datos tabla de asientos contables
        AccountingEntry::insert($data);
    }

    //Asiento contable emisión de la póliza cuando es tipo correduria y moneda dolares
    public function reportAccountCaseTree001($policy){

        //Codigo unico para cada bloque de asientos contables
        $nextEntryCode = AccountingEntry::max('entry_code');
        $nextEntryCode = sprintf('%04d',($nextEntryCode ?? 0) + 1);

        //Traemos el pago mediante l apoliza asociada
        $activityPayment = Activity::where('parent_id', $policy->activity->id)
            ->where("service_id", Service::SERVICE_POLICY_SORT_COLLECTION_MNK)
            ->whereHas('policy_sort_collection', function ($query) {
                $query->where('payment_status', PolicySortCollection::PAYMENT_STATUS_APPROVED);
            })
            ->latest()
            ->first();

        
        $trm = 0;
        $amountPolicy = $activityPayment->policy_sort_collection->total_amount ?? 0;

        if ($policy->type_currency == 'USD'){
            $webserviceController = new WebserviceAcselController();
            $trm = $webserviceController->getTrm();

            if ($trm ==0) {
                throw new \Exception('Error: El TRM no es valido');
            }

            $amountPolicy =  $amountPolicy*$trm;

        }

        //Traemos los porcentajes de las tarifas para los calculos 
        $accountsPercentages = AppServiceProvider::$ACCOUNTS_PERCENTAGES;
        $paymentId = $activityPayment->policy_sort_collection->id;

        // Array con valores específicos de debit y credit para cada fila
        //Fila 2 
        $valueCredit2 = $amountPolicy * $accountsPercentages[3];
        //Fila 3 
        $valueCredit3 = $amountPolicy * $accountsPercentages[5];
        //Fila 5 
        $valueCredit5 = $amountPolicy * 0.10;
        //Fila 6
        $valueDebit6 = $amountPolicy  * 0.10;
        //Fila 7
        $valueDebit7 = $amountPolicy  * 0;
        //Fila 8 
        $valueDebit8 = $amountPolicy * $accountsPercentages[3];
        //Fila 9 
        $valueDebit9 = $amountPolicy * $accountsPercentages[5];
        //Fila 10 
        $valueCredit10 = $amountPolicy;
        //Fila 11 
        $valueCredit11 = $amountPolicy * 0 * $accountsPercentages[23];
        //Fila 12
        $valueCredit12 = $amountPolicy * 0 * $accountsPercentages[25];
        //Fila 13
        $valueCredit13 = $amountPolicy * 0 * $accountsPercentages[27];
        //Fila 14
        $valueCredit14 = $amountPolicy * 0 * $accountsPercentages[19];
        //Fila 4
        $valueCredit4 = ($amountPolicy * 0) - $valueCredit11 - $valueCredit12 - $valueCredit13 - $valueCredit14;

        $t_debit  =  $amountPolicy + $valueDebit6 + $valueDebit7  + $valueDebit8 + $valueDebit9;
        $t_credit =  $valueCredit2 + $valueCredit3 +  $valueCredit4 + $valueCredit5 + $valueCredit10 + $valueCredit11 + $valueCredit12 + $valueCredit13 + $valueCredit14;

        $values = [
            ['debit' => $amountPolicy, 'credit' => 0,                  'movement_type'=> 'D', 'movement_type_org'=> 'D', 'cta_1' => '001', 'cta_2' => '040', 'cta_3' => '030', 'cta_4' => '010', 'cta_5' => '002', 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'PRIMAS', 'cod_cpto' => 'RECIBO', 'cor_relativo'  => '002', 'ctaux' => '**************'],
            ['debit' => 0,              'credit' => $valueCredit2,      'movement_type'=> 'C', 'movement_type_org'=> 'C', 'cta_1' => '002', 'cta_2' => '040', 'cta_3' => '020', 'cta_4' => '320', 'cta_5' => '002', 'cta_6' => '000', 'cta_7' => '000', 'cta_8' => '000', 'cod_grupo_cpto' => 'IMPUES', 'cod_cpto' => 'IMPBOM', 'cor_relativo'  => '010', 'ctaux' => '**************'],
            ['debit' => 0,              'credit' => $valueCredit3,      'movement_type'=> 'C', 'movement_type_org'=> 'C', 'cta_1' => '002', 'cta_2' => '040', 'cta_3' => '020', 'cta_4' => '330', 'cta_5' => '002', 'cta_6' => '000', 'cta_7' => '000', 'cta_8' => '000', 'cod_grupo_cpto' => 'IMPUES', 'cod_cpto' => 'IMPINE', 'cor_relativo'  => '063', 'ctaux' => '**************'],
            ['debit' => 0,              'credit' => $valueCredit4,      'movement_type'=> 'C', 'movement_type_org'=> 'C', 'cta_1' => '002', 'cta_2' => '060', 'cta_3' => '010', 'cta_4' => '010', 'cta_5' => '002', 'cta_6' => '000', 'cta_7' => '000', 'cta_8' => '000', 'cod_grupo_cpto' => 'IMPUES', 'cod_cpto' => 'IMPREA', 'cor_relativo'  => '012', 'ctaux' => '0000000000SORT'],
            ['debit' => 0,              'credit' => $valueCredit5,      'movement_type'=> 'C', 'movement_type_org'=> 'C', 'cta_1' => '002', 'cta_2' => '070', 'cta_3' => '020', 'cta_4' => '010', 'cta_5' => '002', 'cta_6' => '010', 'cta_7' => '010', 'cta_8' => '180', 'cod_grupo_cpto' => 'PRIMAS', 'cod_cpto' => 'RECIBO', 'cor_relativo'  => '003', 'ctaux' => '**************'],
            ['debit' => $valueDebit6,   'credit' => 0,                  'movement_type'=> 'D', 'movement_type_org'=> 'D', 'cta_1' => '004', 'cta_2' => '030', 'cta_3' => '010', 'cta_4' => '010', 'cta_5' => '002', 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'PRIMAS', 'cod_cpto' => 'RECIBO', 'cor_relativo'  => '020', 'ctaux' => '**************'],
            ['debit' => $valueDebit7,   'credit' => 0,                  'movement_type'=> 'D', 'movement_type_org'=> 'D', 'cta_1' => '004', 'cta_2' => '040', 'cta_3' => '010', 'cta_4' => '010', 'cta_5' => '002', 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'FACULT', 'cod_cpto' => 'RESER', 'cor_relativo'   => '022', 'ctaux' => '**************'],
            ['debit' => $valueDebit8,   'credit' => 0,                  'movement_type'=> 'D', 'movement_type_org'=> 'D', 'cta_1' => '004', 'cta_2' => '060', 'cta_3' => '090', 'cta_4' => '200', 'cta_5' => '002', 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'IMPUES', 'cod_cpto' => 'IMPBOM', 'cor_relativo'  => '019', 'ctaux' => '**************'],
            ['debit' => $valueDebit9,   'credit' => 0,                  'movement_type'=> 'D', 'movement_type_org'=> 'D', 'cta_1' => '004', 'cta_2' => '060', 'cta_3' => '090', 'cta_4' => '230', 'cta_5' => '002', 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'IMPUES', 'cod_cpto' => 'IMPINE', 'cor_relativo'  => '064', 'ctaux' => '**************'],
            ['debit' => 0,              'credit' => $valueCredit10,     'movement_type'=> 'C', 'movement_type_org'=> 'C', 'cta_1' => '005', 'cta_2' => '020', 'cta_3' => '010', 'cta_4' => '010', 'cta_5' => '002', 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'PRIMAS', 'cod_cpto' => 'RECIBO', 'cor_relativo'  => '024', 'ctaux' => '**************'],
            ['debit' => 0,              'credit' => $valueCredit11,     'movement_type'=> 'C', 'movement_type_org'=> 'C', 'cta_1' => '005', 'cta_2' => '030', 'cta_3' => '010', 'cta_4' => '010', 'cta_5' => '002', 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'COREAS', 'cod_cpto' => 'COMIS', 'cor_relativo'   => '025', 'ctaux' => '**************'],
            ['debit' => 0,              'credit' => $valueCredit12,     'movement_type'=> 'C', 'movement_type_org'=> 'C', 'cta_1' => '005', 'cta_2' => '030', 'cta_3' => '010', 'cta_4' => '010', 'cta_5' => '002', 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'FACULT', 'cod_cpto' => 'IMPBOM', 'cor_relativo'  => '027', 'ctaux' => '**************'],
            ['debit' => 0,              'credit' => $valueCredit13,     'movement_type'=> 'C', 'movement_type_org'=> 'C', 'cta_1' => '005', 'cta_2' => '030', 'cta_3' => '010', 'cta_4' => '010', 'cta_5' => '002', 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'FACULT', 'cod_cpto' => 'IMPMUN', 'cor_relativo'  => '030', 'ctaux' => '**************'],
            ['debit' => 0,              'credit' => $valueCredit14,     'movement_type'=> 'C', 'movement_type_org'=> 'C', 'cta_1' => '005', 'cta_2' => '030', 'cta_3' => '010', 'cta_4' => '010', 'cta_5' => '002', 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'FACULT', 'cod_cpto' => 'IMPINE', 'cor_relativo'  => '065', 'ctaux' => '**************'],
        ];
        
        $username = preg_replace('/^CO-/', '', $policy->code); // Remover el prefijo "CO-"

        $user = User::where(function ($query) use ($username) {
            $query->where('username', $username) // Buscar directamente sin "CO-"
            ->orWhere('username', 'CO-' . $username) // Buscar con "CO-"
            ->orWhere('code_mnk',$username)
                ->orWhere('code_mnk','CO-' . $username);
        })->first();

        $cod_cia = '01';
        // Array para almacenar los registros completos
        $data = [];

        // Recorre cada par de valores de debit y credit
        foreach ($values as $value) {
            $idecta = "{$cod_cia}{$value['cta_1']}{$value['cta_2']}{$value['cta_3']}{$value['cta_4']}{$value['cta_5']}{$value['cta_6']}{$value['cta_7']}{$value['cta_8']}000000{$value['ctaux']}";

            $data[] = [
                'cod_cia' => $cod_cia,
                'cod_oper' => '001',
                'receipt_number' => "RT-$nextEntryCode",
                'receipt_status' => 'ACT',
                'number_policy' => $policy->formatNumberConsecutive(),
                'number_payment' => $paymentId,
                'receipt_type' => '001',
                'date_register' => Carbon::now(),
                'descoper' => 'EMISION DE POLIZA (AM)',
                'cod_grupo_cpto' => $value['cod_grupo_cpto'],
                'cod_cpto' => $value['cod_cpto'],
                'cor_relativo' => $value['cor_relativo'],
                'cod_ramo' => 'SORT',
                'detail_movement' => 'EMISION DE POLIZA (AM)-'.$policy->formatNumberConsecutive().'-'.$paymentId.'-'."RT-$nextEntryCode".'-'.$policy->activity->affiliate->first_name.$value['cod_cpto'],
                'debit' => $value['debit'],
                'credit' => $value['credit'],
                't_debit' => $t_debit,
                't_credit' => $t_credit,
                'difference' => 0,
                'movement_type' => $value['movement_type'],
                'movement_type_org' => $value['movement_type_org'],
                'idecta' => $idecta,
                'cta_1' => $value['cta_1'],
                'cta_2' => $value['cta_2'],
                'cta_3' => $value['cta_3'],
                'cta_4' => $value['cta_4'],
                'cta_5' => $value['cta_5'],
                'cta_6' => $value['cta_6'],
                'cta_7' => $value['cta_7'],
                'cta_8' => $value['cta_8'],
                'cta_9' => '000',
                'cta_10' => '000',
                'document_type' => $policy->activity->affiliate->doc_type,
                'document_number' => $policy->activity->affiliate->doc_number,
                'name_taker' => $policy->activity->affiliate->full_name,
                'cod_intermediary' =>  isset($user) && isset($user->code_correduria) ? $user->code_correduria : $policy->code ,
                'cod_moneda' => 'US',
                'exchange_rate' => $trm,
                'entry_code' => $nextEntryCode,
                'ctaux'  =>$value['ctaux'],
                'type' => isset($activityPayment) ? ($activityPayment->policy_sort_collection->type_receipt ?? '') : ''
            ];
        }

        // Inserta todos los registros en la base de datos tabla de asientos contables
        AccountingEntry::insert($data);
    }

    //Asiento contable emisión de la póliza cuando es tipo corredor-independiente y moneda dolares
    public function reportAccountCaseFour001($policy){

        $username = preg_replace('/^CO-/', '', $policy->code); // Limpiar el prefijo "CO-" del código

        $user = User::where(function ($query) use ($username) {
            $query->where('username', $username) // Buscar directamente sin "CO-"
            ->orWhere('username', 'CO-' . $username) // Buscar con "CO-"
            ->orWhere('code_mnk',$username)
                ->orWhere('code_mnk','CO-' . $username);
        })->first();

        //Codigo unico para cada bloque de asientos contables
        $nextEntryCode = AccountingEntry::max('entry_code');
        $nextEntryCode = sprintf('%04d',($nextEntryCode ?? 0) + 1);

        //Traemos el pago mediante l apoliza asociada
        $activityPayment = Activity::where('parent_id', $policy->activity->id)
            ->where("service_id", Service::SERVICE_POLICY_SORT_COLLECTION_MNK)
            ->whereHas('policy_sort_collection', function ($query) {
                $query->where('payment_status', PolicySortCollection::PAYMENT_STATUS_APPROVED);
            })
            ->latest()
            ->first();

        
        $trm = 0;
        $amountPolicy = $activityPayment->policy_sort_collection->total_amount ?? 0;

        if ($policy->type_currency == 'USD'){
            $webserviceController = new WebserviceAcselController();
            $trm = $webserviceController->getTrm();

            if ($trm ==0) {
                throw new \Exception('Error: El TRM no es valido');
            }

            $amountPolicy =  $amountPolicy*$trm;

        }

        //Traemos los porcentajes de las tarifas para los calculos 
        $accountsPercentages = AppServiceProvider::$ACCOUNTS_PERCENTAGES;
        $paymentId = $activityPayment->policy_sort_collection->id;

        // Array con valores específicos de debit y credit para cada fila
        //Fila 2 
        $valueCredit2 = $amountPolicy * $accountsPercentages[3];
        //Fila 3 
        $valueCredit3 = $amountPolicy * $accountsPercentages[5];
        //Fila 5 
        $valueCredit5 = $amountPolicy * 0.10;
        //Fila 6
        $valueDebit6 = $amountPolicy  * 0.10;
        //Fila 7
        $valueDebit7 = $amountPolicy  * 0;
        //Fila 8 
        $valueDebit8 = $amountPolicy * $accountsPercentages[3];
        //Fila 9 
        $valueDebit9 = $amountPolicy * $accountsPercentages[5];
        //Fila 10 
        $valueCredit10 = $amountPolicy;
        //Fila 11 
        $valueCredit11 = $amountPolicy * 0 * $accountsPercentages[23];
        //Fila 12
        $valueCredit12 = $amountPolicy * 0 * $accountsPercentages[25];
        //Fila 13
        $valueCredit13 = $amountPolicy * 0 * $accountsPercentages[27];
        //Fila 14
        $valueCredit14 = $amountPolicy * 0 * $accountsPercentages[19];
        //Fila 4
        $valueCredit4 = ($amountPolicy * 0) - $valueCredit11 - $valueCredit12 - $valueCredit13 - $valueCredit14;

        $t_debit  =  $amountPolicy + $valueDebit6 + $valueDebit7  + $valueDebit8 + $valueDebit9;
        $t_credit =  $valueCredit2 + $valueCredit3 +  $valueCredit4 + $valueCredit5 + $valueCredit10 + $valueCredit11 + $valueCredit12 + $valueCredit13 + $valueCredit14;

        $values = [
            ['debit' => $amountPolicy, 'credit' => 0,                  'movement_type'=> 'D', 'movement_type_org'=> 'D', 'cta_1' => '001', 'cta_2' => '040', 'cta_3' => '030', 'cta_4' => '010', 'cta_5' => '002', 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'PRIMAS', 'cod_cpto' => 'RECIBO', 'cor_relativo'  => '002', 'ctaux' => '**************'],
            ['debit' => 0,              'credit' => $valueCredit2,      'movement_type'=> 'C', 'movement_type_org'=> 'C', 'cta_1' => '002', 'cta_2' => '040', 'cta_3' => '020', 'cta_4' => '320', 'cta_5' => '002', 'cta_6' => '000', 'cta_7' => '000', 'cta_8' => '000', 'cod_grupo_cpto' => 'IMPUES', 'cod_cpto' => 'IMPBOM', 'cor_relativo'  => '010', 'ctaux' => '**************'],
            ['debit' => 0,              'credit' => $valueCredit3,      'movement_type'=> 'C', 'movement_type_org'=> 'C', 'cta_1' => '002', 'cta_2' => '040', 'cta_3' => '020', 'cta_4' => '330', 'cta_5' => '002', 'cta_6' => '000', 'cta_7' => '000', 'cta_8' => '000', 'cod_grupo_cpto' => 'IMPUES', 'cod_cpto' => 'IMPINE', 'cor_relativo'  => '063', 'ctaux' => '**************'],
            ['debit' => 0,              'credit' => $valueCredit4,      'movement_type'=> 'C', 'movement_type_org'=> 'C', 'cta_1' => '002', 'cta_2' => '060', 'cta_3' => '010', 'cta_4' => '010', 'cta_5' => '002', 'cta_6' => '000', 'cta_7' => '000', 'cta_8' => '000', 'cod_grupo_cpto' => 'IMPUES', 'cod_cpto' => 'IMPREA', 'cor_relativo'  => '012', 'ctaux' => '0000000000SORT'],
            ['debit' => 0,              'credit' => $valueCredit5,      'movement_type'=> 'C', 'movement_type_org'=> 'C', 'cta_1' => '002', 'cta_2' => '070', 'cta_3' => '020', 'cta_4' => '010', 'cta_5' => '002', 'cta_6' => '020', 'cta_7' => '010', 'cta_8' => '180', 'cod_grupo_cpto' => 'PRIMAS', 'cod_cpto' => 'RECIBO', 'cor_relativo'  => '003', 'ctaux' => '**************'],
            ['debit' => $valueDebit6,   'credit' => 0,                  'movement_type'=> 'D', 'movement_type_org'=> 'D', 'cta_1' => '004', 'cta_2' => '030', 'cta_3' => '010', 'cta_4' => '010', 'cta_5' => '002', 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'PRIMAS', 'cod_cpto' => 'RECIBO', 'cor_relativo'  => '020', 'ctaux' => '**************'],
            ['debit' => $valueDebit7,   'credit' => 0,                  'movement_type'=> 'D', 'movement_type_org'=> 'D', 'cta_1' => '004', 'cta_2' => '040', 'cta_3' => '010', 'cta_4' => '010', 'cta_5' => '002', 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'FACULT', 'cod_cpto' => 'RESER', 'cor_relativo'   => '022', 'ctaux' => '**************'],
            ['debit' => $valueDebit8,   'credit' => 0,                  'movement_type'=> 'D', 'movement_type_org'=> 'D', 'cta_1' => '004', 'cta_2' => '060', 'cta_3' => '090', 'cta_4' => '200', 'cta_5' => '002', 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'IMPUES', 'cod_cpto' => 'IMPBOM', 'cor_relativo'  => '019', 'ctaux' => '**************'],
            ['debit' => $valueDebit9,   'credit' => 0,                  'movement_type'=> 'D', 'movement_type_org'=> 'D', 'cta_1' => '004', 'cta_2' => '060', 'cta_3' => '090', 'cta_4' => '230', 'cta_5' => '002', 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'IMPUES', 'cod_cpto' => 'IMPINE', 'cor_relativo'  => '064', 'ctaux' => '**************'],
            ['debit' => 0,              'credit' => $valueCredit10,     'movement_type'=> 'C', 'movement_type_org'=> 'C', 'cta_1' => '005', 'cta_2' => '020', 'cta_3' => '010', 'cta_4' => '010', 'cta_5' => '002', 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'PRIMAS', 'cod_cpto' => 'RECIBO', 'cor_relativo'  => '024', 'ctaux' => '**************'],
            ['debit' => 0,              'credit' => $valueCredit11,     'movement_type'=> 'C', 'movement_type_org'=> 'C', 'cta_1' => '005', 'cta_2' => '030', 'cta_3' => '010', 'cta_4' => '010', 'cta_5' => '002', 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'COREAS', 'cod_cpto' => 'COMIS', 'cor_relativo'   => '025', 'ctaux' => '**************'],
            ['debit' => 0,              'credit' => $valueCredit12,     'movement_type'=> 'C', 'movement_type_org'=> 'C', 'cta_1' => '005', 'cta_2' => '030', 'cta_3' => '010', 'cta_4' => '010', 'cta_5' => '002', 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'FACULT', 'cod_cpto' => 'IMPBOM', 'cor_relativo'  => '027', 'ctaux' => '**************'],
            ['debit' => 0,              'credit' => $valueCredit13,     'movement_type'=> 'C', 'movement_type_org'=> 'C', 'cta_1' => '005', 'cta_2' => '030', 'cta_3' => '010', 'cta_4' => '010', 'cta_5' => '002', 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'FACULT', 'cod_cpto' => 'IMPMUN', 'cor_relativo'  => '030', 'ctaux' => '**************'],
            ['debit' => 0,              'credit' => $valueCredit14,     'movement_type'=> 'C', 'movement_type_org'=> 'C', 'cta_1' => '005', 'cta_2' => '030', 'cta_3' => '010', 'cta_4' => '010', 'cta_5' => '002', 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'FACULT', 'cod_cpto' => 'IMPINE', 'cor_relativo'  => '065', 'ctaux' => '**************'],
        ];
    
        $cod_cia = '01';
        // Array para almacenar los registros completos
        $data = [];

        // Recorre cada par de valores de debit y credit
        foreach ($values as $value) {
            $idecta = "{$cod_cia}{$value['cta_1']}{$value['cta_2']}{$value['cta_3']}{$value['cta_4']}{$value['cta_5']}{$value['cta_6']}{$value['cta_7']}{$value['cta_8']}000000{$value['ctaux']}";

            $data[] = [
                'cod_cia' => '01',
                'cod_oper' => '001',
                'receipt_number' => "RT-$nextEntryCode",
                'receipt_status' => 'ACT',
                'number_policy' => $policy->formatNumberConsecutive(),
                'number_payment' => $paymentId,
                'receipt_type' => '001',
                'date_register' => Carbon::now(),
                'descoper' => 'EMISION DE POLIZA (AM)',
                'cod_grupo_cpto' => $value['cod_grupo_cpto'],
                'cod_cpto' => $value['cod_cpto'],
                'cor_relativo' => $value['cor_relativo'],
                'cod_ramo' => 'SORT',
                'detail_movement' => 'EMISION DE POLIZA (AM)-'.$policy->formatNumberConsecutive().'-'.$paymentId.'-'."RT-$nextEntryCode".'-'.$policy->activity->affiliate->first_name.$value['cod_cpto'],
                'debit' => $value['debit'],
                'credit' => $value['credit'],
                't_debit' => $t_debit,
                't_credit' => $t_credit,
                'difference' => 0,
                'movement_type' => $value['movement_type'],
                'movement_type_org' => $value['movement_type_org'],
                'idecta' => $idecta,
                'cta_1' => $value['cta_1'],
                'cta_2' => $value['cta_2'],
                'cta_3' => $value['cta_3'],
                'cta_4' => $value['cta_4'],
                'cta_5' => $value['cta_5'],
                'cta_6' => $value['cta_6'],
                'cta_7' => $value['cta_7'],
                'cta_8' => $value['cta_8'],
                'cta_9' => '000',
                'cta_10' => '000',
                'document_type' => $policy->activity->affiliate->doc_type,
                'document_number' => $policy->activity->affiliate->doc_number,
                'name_taker' => $policy->activity->affiliate->full_name,
                'cod_intermediary' =>  isset($user) && isset($user->code_correduria) ? $user->code_correduria : $policy->code,
                'cod_moneda' => 'US',
                'exchange_rate' => $trm,
                'entry_code' => $nextEntryCode,
                'ctaux'  =>$value['ctaux'],
                'type' => isset($activityPayment) ? ($activityPayment->policy_sort_collection->type_receipt ?? '') : ''
            ];
        }

        // Inserta todos los registros en la base de datos tabla de asientos contables
        AccountingEntry::insert($data);
    }


    public function generaAsiento002($id){
        $policy = PolicySort::find($id);
        $this->reportAccountCaseTwo002($policy);
    }

    public function reportAccountCaseTwo002($policy){

        try{

            $nextEntryCode = AccountingEntry::max('entry_code');
            $nextEntryCode = sprintf('%04d',($nextEntryCode ?? 0) + 1);

            $activityPayment = Activity::where('parent_id', $policy->activity->id)
            ->where("service_id", Service::SERVICE_POLICY_SORT_COLLECTION_MNK)
            ->whereHas('policy_sort_collection', function ($query) {
                $query->where('payment_status', PolicySortCollection::PAYMENT_STATUS_APPROVED);
            })
            ->latest()
            ->first();

            $username = preg_replace('/^CO-/', '', $policy->code); // Remover el prefijo "CO-"

            $user = User::where(function ($query) use ($username) {
                $query->where('username', $username) // Buscar directamente sin "CO-"
                ->orWhere('username', 'CO-' . $username) // Buscar con "CO-"
                ->orWhere('code_mnk',$username)
                    ->orWhere('code_mnk','CO-' . $username);
            })->first();

            $intermediaryType = in_array($user->type_inter, ['G', 'E']) ? 'corredor' : 'intermediario';

            if ($user->type_inter=='') {
                throw new \Exception('Error: Intermediario no encontrado');
            }

            $trm = 0;
            $amount_policy = $activityPayment->policy_sort_collection->total_amount;//$policy->amount_policy;

            if ($policy->type_currency == 'USD'){
                $webserviceController = new WebserviceAcselController();
                $trm = $webserviceController->getTrm();

                if ($trm ==0) {
                    throw new \Exception('Error: El TRM no es valido');
                }

                $amount_policy =  $activityPayment->policy_sort_collection->total_amount*$trm;//$policy->amount_policy*$trm;

            }

            $accountsPercentages = AppServiceProvider::$ACCOUNTS_PERCENTAGES;
            $paymentId = $activityPayment->policy_sort_collection->id;

            $amount_policy = $amount_policy < 0 ? $amount_policy * -1 : $amount_policy;

            //Fila 2
            $valueDebit2 = $amount_policy * $accountsPercentages[3];
            //Fila 3
            $valueDebit3 = $amount_policy * $accountsPercentages[5];
            //Fila 5
            $valueDebit5 = $amount_policy * 0.10;
            //Fila 6
            $valueCredit6 = $amount_policy * 0.10;
            //Fila 7
            $valueCredit7 = $amount_policy * 0.05;
            //Fila 8
            $valueCredit8 = $amount_policy * $accountsPercentages[3];
            //Fila 9
            $valueCredit9 = $amount_policy * $accountsPercentages[5];
            //Fila 11
            $valueDebit11 = ($amount_policy * 0.05) * $accountsPercentages[23];
            //Fila 12
            $valueDebit12 = ($amount_policy * 0.05) * $accountsPercentages[25];
            //Fila 13
            $valueDebit13 = ($amount_policy * 0.05) * $accountsPercentages[27];
            //Fila 14
            $valueDebit14 = ($amount_policy * 0.05) * $accountsPercentages[19];
            //Fila 4
            $valueDebit4 = ($amount_policy * 0.05) - $valueDebit11-$valueDebit12-$valueDebit13-$valueDebit14 ;

            $t_debit  =  $valueDebit2 + $valueDebit3 + $valueDebit4  + $valueDebit5 + $amount_policy + $valueDebit11 + $valueDebit12 + $valueDebit13 + $valueDebit14;
            $t_credit =  $amount_policy + $valueCredit6 +  $valueCredit7 + $valueCredit8 + $valueCredit9;
            $ct6 = $intermediaryType == 'corredor' ? '020' : '010' ;

            $values = [
                ['debit' => 0,              'credit' => $amount_policy, 'movement_type'=> 'C', 'movement_type_org'=> 'C', 'cta_1' => '001', 'cta_2' => '040', 'cta_3' => '030', 'cta_4' => '010', 'cta_5' => '001', 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'PRIMAS', 'cod_cpto' => 'RECIBO', 'cor_relativo' => '002', 'ctaux'  => "**************"],
                ['debit' => $valueDebit2,   'credit' => 0,              'movement_type'=> 'D', 'movement_type_org'=> 'D', 'cta_1' => '002', 'cta_2' => '040', 'cta_3' => '020', 'cta_4' => '320', 'cta_5' => '001', 'cta_6' => '000', 'cta_7' => '000', 'cta_8' => '000', 'cod_grupo_cpto' => 'IMPUES', 'cod_cpto' => 'IMPBOM', 'cor_relativo' => '010', 'ctaux'  => "**************"],
                ['debit' => $valueDebit3,   'credit' => 0,              'movement_type'=> 'D', 'movement_type_org'=> 'D', 'cta_1' => '002', 'cta_2' => '040', 'cta_3' => '020', 'cta_4' => '330', 'cta_5' => '001', 'cta_6' => '000', 'cta_7' => '000', 'cta_8' => '000', 'cod_grupo_cpto' => 'IMPUES', 'cod_cpto' => 'IMPINE', 'cor_relativo' => '063', 'ctaux'  => "**************"],
                ['debit' => $valueDebit4,   'credit' => 0,              'movement_type'=> 'D', 'movement_type_org'=> 'D', 'cta_1' => '002', 'cta_2' => '060', 'cta_3' => '010', 'cta_4' => '010', 'cta_5' => '001', 'cta_6' => '000', 'cta_7' => '000', 'cta_8' => '000', 'cod_grupo_cpto' => 'IMPUES', 'cod_cpto' => 'IMPREA', 'cor_relativo' => '012', 'ctaux'  => "0000000000SORT"],
                ['debit' => $valueDebit5,   'credit' => 0,              'movement_type'=> 'D', 'movement_type_org'=> 'D', 'cta_1' => '002', 'cta_2' => '070', 'cta_3' => '020', 'cta_4' => '010', 'cta_5' => '001', 'cta_6' =>  $ct6, 'cta_7' => '010', 'cta_8' => '180', 'cod_grupo_cpto' => 'PRIMAS', 'cod_cpto' => 'RECIBO', 'cor_relativo' => '003', 'ctaux'  => "**************"],
                ['debit' => 0,              'credit' => $valueCredit6,  'movement_type'=> 'C', 'movement_type_org'=> 'C', 'cta_1' => '004', 'cta_2' => '030', 'cta_3' => '010', 'cta_4' => '010', 'cta_5' => '001', 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'PRIMAS', 'cod_cpto' => 'RECIBO', 'cor_relativo' => '020', 'ctaux'  => "**************"],
                ['debit' => 0,              'credit' => $valueCredit7,  'movement_type'=> 'C', 'movement_type_org'=> 'C', 'cta_1' => '004', 'cta_2' => '040', 'cta_3' => '010', 'cta_4' => '010', 'cta_5' => '001', 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'FACULT', 'cod_cpto' => 'RESER',  'cor_relativo' => '022', 'ctaux'  => "**************"],
                ['debit' => 0,              'credit' => $valueCredit8,  'movement_type'=> 'C', 'movement_type_org'=> 'C', 'cta_1' => '004', 'cta_2' => '060', 'cta_3' => '090', 'cta_4' => '200', 'cta_5' => '001', 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'IMPUES', 'cod_cpto' => 'IMPBOM', 'cor_relativo' => '019', 'ctaux'  => "**************"],
                ['debit' => 0,              'credit' => $valueCredit9,  'movement_type'=> 'C', 'movement_type_org'=> 'C', 'cta_1' => '004', 'cta_2' => '060', 'cta_3' => '090', 'cta_4' => '230', 'cta_5' => '001', 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'IMPUES', 'cod_cpto' => 'IMPINE', 'cor_relativo' => '064', 'ctaux'  => "**************"],
                ['debit' => $amount_policy,  'credit' => 0,             'movement_type'=> 'D', 'movement_type_org'=> 'D', 'cta_1' => '005', 'cta_2' => '020', 'cta_3' => '010', 'cta_4' => '010', 'cta_5' => '001', 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'PRIMAS', 'cod_cpto' => 'RECIBO', 'cor_relativo' => '024', 'ctaux'  => "**************"],
                ['debit' => $valueDebit11,  'credit' => 0,              'movement_type'=> 'D', 'movement_type_org'=> 'D', 'cta_1' => '005', 'cta_2' => '030', 'cta_3' => '010', 'cta_4' => '010', 'cta_5' => '001', 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'COREAS', 'cod_cpto' => 'COMIS',  'cor_relativo' => '025', 'ctaux'  => "**************"],
                ['debit' => $valueDebit12,  'credit' => 0,              'movement_type'=> 'D', 'movement_type_org'=> 'D', 'cta_1' => '005', 'cta_2' => '030', 'cta_3' => '010', 'cta_4' => '010', 'cta_5' => '001', 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'FACULT', 'cod_cpto' => 'IMPBOM', 'cor_relativo' => '027', 'ctaux'  => "**************"],
                ['debit' => $valueDebit13,  'credit' => 0,              'movement_type'=> 'D', 'movement_type_org'=> 'D', 'cta_1' => '005', 'cta_2' => '030', 'cta_3' => '010', 'cta_4' => '010', 'cta_5' => '001', 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'FACULT', 'cod_cpto' => 'IMPMUN', 'cor_relativo' => '030', 'ctaux'  => "**************"],
                ['debit' => $valueDebit14,  'credit' => 0,              'movement_type'=> 'D', 'movement_type_org'=> 'D', 'cta_1' => '005', 'cta_2' => '030', 'cta_3' => '010', 'cta_4' => '010', 'cta_5' => '001', 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'FACULT', 'cod_cpto' => 'IMPINE', 'cor_relativo' => '065', 'ctaux'  => "**************"],
            ];

            $data = [];

            foreach ($values as $value) {

                $idecta = "01{$value['cta_1']}{$value['cta_2']}{$value['cta_3']}{$value['cta_4']}{$value['cta_5']}{$value['cta_6']}{$value['cta_7']}{$value['cta_8']}000000"."**************";

                $data[] = [
                    'cod_cia' => '01',
                    'cod_oper' => '002',
                    'receipt_number' => "RT-$nextEntryCode",
                    'receipt_status' => 'ACT',
                    'number_policy' => $policy->formatNumberConsecutive(),
                    'number_payment' => $paymentId,
                    'receipt_type' => '002',
                    'date_register' => $policy->created_at,
                    'descoper' => 'NOTA CREDITO ANULACION / DEVOLUCION DE PRIMA (AM)',
                    'cod_grupo_cpto' => $value['cod_grupo_cpto'],
                    'cod_cpto' => $value['cod_cpto'],
                    'cor_relativo' => $value['cor_relativo'],
                    'cod_ramo' => 'SORT',
                    'detail_movement' => 'NOTA CREDITO ANULACION / DEVOLUCION DE PRIMA (AM)-'.$policy->formatNumberConsecutive().'-'.$paymentId.'-'."RT-$nextEntryCode".'-'.$policy->activity->affiliate->first_name.$value['cod_cpto'],
                    'debit' => $value['debit'],
                    'credit' => $value['credit'],
                    't_debit' => $t_debit,
                    't_credit' => $t_credit,
                    'difference' => ($t_debit-$t_credit),
                    'movement_type' => $value['movement_type'],
                    'movement_type_org' => $value['movement_type_org'],
                    'idecta' => $idecta,
                    'cta_1' => $value['cta_1'],
                    'cta_2' => $value['cta_2'],
                    'cta_3' => $value['cta_3'],
                    'cta_4' => $value['cta_4'],
                    'cta_5' => $value['cta_5'],
                    'cta_6' => $value['cta_6'],
                    'cta_7' => $value['cta_7'],
                    'cta_8' => $value['cta_8'],
                    'cta_9' => '000',
                    'cta_10' => '000',
                    'document_type' => $policy->activity->affiliate->doc_type,
                    'document_number' => $policy->activity->affiliate->doc_number,
                    'name_taker' => $policy->activity->affiliate->full_name,
                    'cod_intermediary' =>  $user->code_correduria ? $user->code_correduria : $policy->code,
                    'cod_moneda' => $policy->type_currency == 'USD' ? 'US' : 'CO',
                    'exchange_rate' => $trm,
                    'entry_code' => $nextEntryCode,
                    'ctaux'  => $value['ctaux']
                ];
            }

            AccountingEntry::insert($data);

            $result = [
                'success' => true,
                'value'   => $amount_policy,
                'id'      => $policy->id,
            ];

            return $result;

        }catch (\Exception $e){
            throw new \Exception('Error: '.$e->getMessage());
        }
    }

    public function generaAsientos003(){

        $consulta = "SELECT p.id as policy_id,ps.payment_method
                        FROM policy_sort_collections ps
                        LEFT JOIN activities a ON (a.id=ps.activity_id)
                        LEFT JOIN policy_sorts p ON (p.activity_id=a.parent_id)
                        WHERE p.consecutive in (362)
                        group by p.id ";

        $query = DB::select($consulta);

        foreach ($query as $item) {

            if($item->payment_method == 'TB') {
                $this->reportAccountCaseOne003('',$item->policy_id);
            }else{
                $this->reportAccountCaseTwo003('',$item->policy_id);
            }

        }

        return 'TERMINO:003:';

    }

    /**
     * Reporte contable 003
     * Función Situación 1 Transacción Bancaria (TB) - Se tienen en cuenta el tipo de moneda
     * */
    public function reportAccountCaseOne003($cpath, $id){

        $policy = PolicySort::find($id);

        $ctaux_date = AppServiceProvider::$CTAUX;
        $ctaux = '';
        //Codigo unico para cada bloque de asientos contables
        $nextEntryCode = AccountingEntry::max('entry_code');
        $nextEntryCode = sprintf('%04d',($nextEntryCode ?? 0) + 1);

        //$policy = PolicySort::find($id);
        $activityPayment = Activity::where('parent_id', $policy->activity->id)
            ->where("service_id", Service::SERVICE_POLICY_SORT_COLLECTION_MNK)
            ->whereHas('policy_sort_collection', function ($query) {
                $query->where('payment_status', PolicySortCollection::PAYMENT_STATUS_APPROVED);
            })
            ->latest('id')
            ->first();

//dd($activityPayment);
        $paymentId = $activityPayment->policy_sort_collection->id;
        $policySort_colletion = $activityPayment->policy_sort_collection;
        $amountPolicy = $activityPayment->policy_sort_collection->total_amount ?? 0;

        $currencyKey = ($policy->type_currency == 'CRC') ? 'CO' : 'DO';

        $username = preg_replace('/^CO-/', '', $policy->code); // Remover el prefijo "CO-"

        $user = User::where(function ($query) use ($username) {
            $query->where('username', $username) // Buscar directamente sin "CO-"
            ->orWhere('username', 'CO-' . $username) // Buscar con "CO-"
            ->orWhere('code_mnk',$username)
                ->orWhere('code_mnk','CO-' . $username);
        })->first();

        switch ($policySort_colletion->bank_id) {
            case 1:
                $ctaux = $ctaux_date['davivienda'][$currencyKey];
                break;
            case 2:
                $ctaux = $ctaux_date['banco_nacional'][$currencyKey];
                break;
            case 3:
                $ctaux = $ctaux_date['bac_redomatic'][$currencyKey];
                break;
            case 4:
                $ctaux = $ctaux_date['banco_costa_rica'][$currencyKey];
                break;
            default:
                $ctaux = '**************';
                break;
        }
        if ($policySort_colletion->payment_method == 'TC'){
            $ctaux = $ctaux_date['bac_redomatic'][$currencyKey];
        }
        $cod_moneda = 'CO';

        $trm = 0;

        if ($policy->type_currency == 'USD'){
            $webserviceController = new WebserviceAcselController();
            $trm = $webserviceController->getTrm();

            if ($trm ==0) {
                throw new \Exception('Error: El TRM no es valido');
            }

            $amountPolicy =  $amountPolicy*$trm;
            $cod_moneda = 'US';
        }
        $valueCredit = $amountPolicy ;
        $valueDebit = $amountPolicy ;



        $t_debit  =  $valueDebit ;
        $t_credit =  $valueCredit;

        $values =[];
        //Dependiendo del banco seleccionado en el pago
        if ($policySort_colletion->bank_id == 2 || $policySort_colletion->bank_id == 4 ){
            $values = [
                ['debit' => $amountPolicy, 'credit' => 0,                  'movement_type'=> 'D', 'movement_type_org'=> 'D', 'cta_1' => '001', 'cta_2' => '010', 'cta_3' => '030', 'cta_4' => '010', 'cta_5' => '001', 'cta_6' => '010', 'cta_7' => '000', 'cta_8' => '000', 'cta_9' => '000', 'cta_10' => '000','cod_grupo_cpto' => 'INGRES', 'cod_cpto' => 'MOVBAN', 'cor_relativo' => '005', 'descoper' => 'DEPOSITO BANCARIO', 'ctaux' => $ctaux, 'cod_oper'=>'017'],
                //['debit' => 0,              'credit' => 0,                  'movement_type'=> 'D', 'movement_type_org'=> 'D', 'cta_1' => '001', 'cta_2' => '010', 'cta_3' => '030', 'cta_4' => '020', 'cta_5' => '001', 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '000', 'cta_9' => '000', 'cta_10' => '000','cod_grupo_cpto' => 'INGRES', 'cod_cpto' => 'MOVBAN', 'cor_relativo' => '021','descoper' => 'DEPOSITO BANCARIO', 'ctaux' => $ctaux, 'cod_oper'=>'017'],
                ['debit' => 0,              'credit' => $amountPolicy,     'movement_type'=> 'C', 'movement_type_org'=> 'C', 'cta_1' => '001', 'cta_2' => '040', 'cta_3' => '030', 'cta_4' => '010', 'cta_5' => '001', 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cta_9' => '000', 'cta_10' => '000','cod_grupo_cpto' => 'PRIMAS', 'cod_cpto' => 'RECIBO', 'cor_relativo' => '003', 'descoper' => 'COBRO DE PRIMAS (AM)', 'ctaux' => '**************', 'cod_oper'=>'003'],
            ];
        }else{
            $values = [
                //  ['debit' => 0,              'credit' => 0,                  'movement_type'=> 'D', 'movement_type_org'=> 'D', 'cta_1' => '001', 'cta_2' => '010', 'cta_3' => '030', 'cta_4' => '010', 'cta_5' => '001', 'cta_6' => '010', 'cta_7' => '000', 'cta_8' => '100', 'cta_9' => '000', 'cta_10' => '000','cod_grupo_cpto' => 'INGRES', 'cod_cpto' => 'MOVBAN', 'cor_relativo' => '005', 'descoper' => 'DEPOSITO BANCARIO', 'ctaux' => $ctaux, 'cod_oper'=>'017'],
                ['debit' => $amountPolicy, 'credit' => 0,                  'movement_type'=> 'D', 'movement_type_org'=> 'D', 'cta_1' => '001', 'cta_2' => '010', 'cta_3' => '030', 'cta_4' => '020', 'cta_5' => '001', 'cta_6' => '010', 'cta_7' => '000', 'cta_8' => '000', 'cta_9' => '000', 'cta_10' => '000','cod_grupo_cpto' => 'INGRES', 'cod_cpto' => 'MOVBAN', 'cor_relativo' => '021','descoper' => 'DEPOSITO BANCARIO', 'ctaux' => $ctaux, 'cod_oper'=>'017'],
                ['debit' => 0,              'credit' => $amountPolicy,     'movement_type'=> 'C', 'movement_type_org'=> 'C', 'cta_1' => '001', 'cta_2' => '040', 'cta_3' => '030', 'cta_4' => '010', 'cta_5' => '001', 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cta_9' => '000', 'cta_10' => '000','cod_grupo_cpto' => 'PRIMAS', 'cod_cpto' => 'RECIBO', 'cor_relativo' => '003', 'descoper' => 'COBRO DE PRIMAS (AM)', 'ctaux' => '**************', 'cod_oper'=>'003'],
            ];
        }



        // Array para almacenar los registros completos
        $data = [];

        // Recorre cada par de valores de debit y credit
        foreach ($values as $value) {
            $idecta = "01{$value['cta_1']}{$value['cta_2']}{$value['cta_3']}{$value['cta_4']}{$value['cta_5']}{$value['cta_6']}{$value['cta_7']}{$value['cta_8']}000000{$value['ctaux']}";
            $data[] = [
                'cod_cia' => '01',
                'cod_oper' => $value['cod_oper'],
                'receipt_number' => "RT-$nextEntryCode",
                'receipt_status' => 'ACT',
                'number_policy' => $policy->formatNumberConsecutive(),
                'number_payment' => $paymentId,
                'receipt_type' => '003',
                'date_register' => $policy->created_at,
                'descoper' =>  $value['descoper'],
                'cod_grupo_cpto' => $value['cod_grupo_cpto'],
                'cod_cpto' => $value['cod_cpto'],
                'cor_relativo' => $value['cor_relativo'],
                'cod_ramo' => 'SORT',
                'detail_movement' =>$value['descoper']. "-" . $policy->formatNumberConsecutive(). "-" . $paymentId. "-" ."RT-$nextEntryCode".'-'.$policy->activity->affiliate->full_name. "-" . $value['cod_cpto'] ,
                'debit' => $value['debit'],
                'credit' => $value['credit'],
                't_debit' => $t_debit,
                't_credit' => $t_credit,
                'difference' => 0,
                'movement_type' => $value['movement_type'],
                'movement_type_org' => $value['movement_type_org'],
                'idecta' => $idecta,
                'cta_1' => $value['cta_1'],
                'cta_2' => $value['cta_2'],
                'cta_3' => $value['cta_3'],
                'cta_4' => $value['cta_4'],
                'cta_5' => $value['cta_5'],
                'cta_6' => $value['cta_6'],
                'cta_7' => $value['cta_7'],
                'cta_8' => $value['cta_8'],
                'cta_9' => $value['cta_9'],
                'cta_10' => $value['cta_10'],
                'document_type' => $policy->activity->affiliate->doc_type,
                'document_number' => $policy->activity->affiliate->doc_number,
                'name_taker' => $policy->activity->affiliate->full_name,
                'cod_intermediary' =>  isset($user) && isset($user->code_correduria) ? $user->code_correduria : $policy->code ,
                'cod_moneda' => $cod_moneda,
                'exchange_rate' => $trm,
                'ctaux' => $value['ctaux'],
                'entry_code' => $nextEntryCode,
                'type' => isset($activityPayment) ? ($activityPayment->policy_sort_collection->type_receipt ?? '') : ''
            ];
        }

        // Inserta todos los registros en la base de datos tabla de asientos contables
        AccountingEntry::insert($data);

    }
    /**
     * Reporte contable 003
     * Función Situación 2 Tarjeta de Crédito- Se tienen en cuenta el tipo de moneda
     * */
    public function reportAccountCaseTwo003($cpath, $id){
        $policy = PolicySort::find($id);

        $ctaux_date = AppServiceProvider::$CTAUX;
        $ctaux = '';
        //Codigo unico para cada bloque de asientos contables
        $nextEntryCode = AccountingEntry::max('entry_code');
        $nextEntryCode = sprintf('%04d',($nextEntryCode ?? 0) + 1);
        $accountsPercentages = AppServiceProvider::$ACCOUNTS_PERCENTAGES_003;

        $activityPayment = Activity::where('parent_id', $policy->activity->id)
            ->where("service_id", Service::SERVICE_POLICY_SORT_COLLECTION_MNK)
            ->whereHas('policy_sort_collection', function ($query) {
                $query->where('payment_status', PolicySortCollection::PAYMENT_STATUS_APPROVED);
            })
            ->latest()
            ->first();

        $paymentId = $activityPayment->policy_sort_collection->id;
        $policySort_colletion = $activityPayment->policy_sort_collection;
        $amountPolicy = $activityPayment->policy_sort_collection->total_amount ?? 0;

        $currencyKey = ($policy->type_currency == 'CRC') ? 'CO' : 'DO';

        if ($policySort_colletion->payment_method == 'TC'){
            $ctaux = $ctaux_date['bac_redomatic'][$currencyKey];
        }

        $cod_moneda = 'CO';

        $trm = 0;

        if ($policy->type_currency == 'USD'){

            $cod_moneda = 'US';
            $webserviceController = new WebserviceAcselController();
            $trm = $webserviceController->getTrm();

            if ($trm ==0) {
                throw new \Exception('Error: El TRM no es valido');
            }

            $amountPolicy =  $amountPolicy*$trm;

        }

        $username = preg_replace('/^CO-/', '', $policy->code); // Remover el prefijo "CO-"

         $user = User::where(function ($query) use ($username) {
            $query->where('username', $username) // Buscar directamente sin "CO-"
                  ->orWhere('username', 'CO-' . $username); // Buscar con "CO-"
        })->first();

        $valueCredit = round($amountPolicy , 2);  // Redondear a 2 decimales
        $t_credit =  $valueCredit;
        $valueDebit2 = round($amountPolicy * $accountsPercentages[5], 2);   // Redondear a 2 decimales
        $valueDebit3 = round($amountPolicy * $accountsPercentages[13], 2);  // Redondear a 2 decimales
        $valueDebit4 = round($amountPolicy * $accountsPercentages[7], 2);   // Redondear a 2 decimales
        $valueDebit = round($amountPolicy - $valueDebit2 - $valueDebit3 - $valueDebit4, 2);  // Redondear a 2 decimales

        $t_debit  = round($valueDebit + $valueDebit2 + $valueDebit3 + $valueDebit4, 2);  // Redondear a 2 decimales

        $values =[];
        //Dependiendo del banco seleccionado en el pago
       
        $values = [
            ['debit' => $valueDebit,               'credit' => 0,                  'movement_type'=> 'D', 'movement_type_org'=> 'D', 'cta_1' => '001', 'cta_2' => '010', 'cta_3' => '030', 'cta_4' => '020', 'cta_5' => '001', 'cta_6' => '010', 'cta_7' => '000', 'cta_8' => '000', 'cta_9' => '000', 'cta_10' => '000','cod_grupo_cpto' => 'INGRES', 'cod_cpto' => 'MOVBAN', 'cor_relativo' => '021', 'descoper' => 'DEPOSITO BANCARIO', 'ctaux' => $ctaux, 'cod_oper'=>'017'],
            ['debit' => 0,                         'credit' => $amountPolicy,     'movement_type'=> 'C', 'movement_type_org'=> 'C', 'cta_1' => '001', 'cta_2' => '040', 'cta_3' => '030', 'cta_4' => '010', 'cta_5' => '001', 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cta_9' => '000', 'cta_10' => '000','cod_grupo_cpto' => 'PRIMAS', 'cod_cpto' => 'RECIBO', 'cor_relativo' => '003', 'descoper' => 'COBRO DE PRIMAS (AM)', 'ctaux' => '**************', 'cod_oper'=>'003'],
            ['debit' => $valueDebit2,              'credit' => 0,                  'movement_type'=> 'D', 'movement_type_org'=> 'D', 'cta_1' => '001', 'cta_2' => '090', 'cta_3' => '010', 'cta_4' => '020', 'cta_5' => '001', 'cta_6' => '010', 'cta_7' => '000', 'cta_8' => '000', 'cta_9' => '000', 'cta_10' => '000','cod_grupo_cpto' => 'INGRES', 'cod_cpto' => 'RETISR', 'cor_relativo' => '041', 'descoper' => 'COBRO DE PRIMAS (AM)', 'ctaux' => '**************', 'cod_oper'=>'003'],
            ['debit' => $valueDebit3,              'credit' => 0,                  'movement_type'=> 'D', 'movement_type_org'=> 'D', 'cta_1' => '004', 'cta_2' => '060', 'cta_3' => '010', 'cta_4' => '030', 'cta_5' => '001', 'cta_6' => '000', 'cta_7' => '000', 'cta_8' => '000', 'cta_9' => '000', 'cta_10' => '000','cod_grupo_cpto' => 'INGRES', 'cod_cpto' => 'DCTCOM', 'cor_relativo' => '020', 'descoper' => 'COBRO DE PRIMAS (AM)', 'ctaux' => '**************', 'cod_oper'=>'003'],
            ['debit' => $valueDebit4,              'credit' => 0,                  'movement_type'=> 'D', 'movement_type_org'=> 'D', 'cta_1' => '002', 'cta_2' => '040', 'cta_3' => '020', 'cta_4' => '040', 'cta_5' => '001', 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '000', 'cta_9' => '000', 'cta_10' => '000','cod_grupo_cpto' => 'INGRES', 'cod_cpto' => 'IMPTAR', 'cor_relativo' => '019', 'descoper' => 'COBRO DE PRIMAS (AM)', 'ctaux' => '**************', 'cod_oper'=>'003'],
        ];
    
        // Array para almacenar los registros completos
        $data = [];
        // Recorre cada par de valores de debit y credit
        foreach ($values as $value) {
            $idecta = "01{$value['cta_1']}{$value['cta_2']}{$value['cta_3']}{$value['cta_4']}{$value['cta_5']}{$value['cta_6']}{$value['cta_7']}{$value['cta_8']}000000{$value['ctaux']}";
            $data[] = [
                'cod_cia' => '01',
                'cod_oper' => $value['cod_oper'],
                'receipt_number' => "RT-$nextEntryCode",
                'receipt_status' => 'ACT',
                'number_policy' => $policy->formatNumberConsecutive(),
                'number_payment' => $paymentId,
                'receipt_type' => '003',
                'date_register' => $policy->created_at,
                'descoper' =>  $value['descoper'],
                'cod_grupo_cpto' => $value['cod_grupo_cpto'],
                'cod_cpto' => $value['cod_cpto'],
                'cor_relativo' => $value['cor_relativo'],
                'cod_ramo' => 'SORT',
                'detail_movement' => $value['descoper']. "-" . $policy->formatNumberConsecutive(). "-" . $paymentId. "-" ."RT-$nextEntryCode".'-'.$policy->activity->affiliate->full_name. "-" . $value['cod_cpto'] ,
                'debit' => $value['debit'],
                'credit' => $value['credit'],
                't_debit' => $t_debit,
                't_credit' => $t_credit,
                'difference' => 0,
                'movement_type' => $value['movement_type'],
                'movement_type_org' => $value['movement_type_org'],
                'idecta' => $idecta,
                'cta_1' => $value['cta_1'],
                'cta_2' => $value['cta_2'],
                'cta_3' => $value['cta_3'],
                'cta_4' => $value['cta_4'],
                'cta_5' => $value['cta_5'],
                'cta_6' => $value['cta_6'],
                'cta_7' => $value['cta_7'],
                'cta_8' => $value['cta_8'],
                'cta_9' => $value['cta_9'],
                'cta_10' => $value['cta_10'],
                'document_type' => $policy->activity->affiliate->doc_type,
                'document_number' => $policy->activity->affiliate->doc_number,
                'name_taker' => $policy->activity->affiliate->full_name,
                'cod_intermediary' =>  $user->code_correduria ? $user->code_correduria : $policy->code ,
                'cod_moneda' => $cod_moneda,
                'exchange_rate' => $trm,
                'ctaux' => $value['ctaux'],
                'entry_code' => $nextEntryCode,
                'type' => isset($activityPayment) ? ($activityPayment->policy_sort_collection->type_receipt ?? '') : ''
            ];
        }

        // Inserta todos los registros en la base de datos tabla de asientos contables
        AccountingEntry::insert($data);

    }
    /**
     * Reporte contable 003
     * Función Situación 3 Tarjeta de Crédito o transferencia y se realiza el pago con una moneda diferente a la de la póliza
     * */
    public function reportAccountCaseThree003($cpath, $id, $valorPagado){

        $ctaux_date = AppServiceProvider::$CTAUX;

        $nextEntryCode = AccountingEntry::max('entry_code');
        $nextEntryCode = sprintf('%04d',($nextEntryCode ?? 0) + 1);
        //$accountsPercentages = AppServiceProvider::$ACCOUNTS_PERCENTAGES_003;

        $policy = PolicySort::find($id);

        $activityPayment = Activity::where('parent_id', $policy->activity->id)
            ->where("service_id", Service::SERVICE_POLICY_SORT_COLLECTION_MNK)
            ->whereHas('policy_sort_collection', function ($query) {
                $query->where('payment_status', PolicySortCollection::PAYMENT_STATUS_APPROVED);
            })
            ->latest()
            ->first();

        $paymentId = $activityPayment->policy_sort_collection->id;
        $policySort_colletion = $activityPayment->policy_sort_collection;

        $currencyKey = ($policy->type_currency == 'CRC') ? 'CO' : 'DO';

        switch ($policySort_colletion->bank_id) {
            case 1:
                $ctaux = $ctaux_date['davivienda'][$currencyKey];
                break;
            case 2:
                $ctaux = $ctaux_date['banco_nacional'][$currencyKey];
                break;
            case 3:
                $ctaux = $ctaux_date['bac_redomatic'][$currencyKey];
                break;
            default:
                $ctaux = '**************';
                break;
        }
        if ($policySort_colletion->payment_method == 'TC'){
            $ctaux = $ctaux_date['bac_redomatic'][$currencyKey];
        }

        $trm = 0;
        $amountPolicy = $activityPayment->policy_sort_collection->total_amount ?? 0;

        $cta5 = '001';
        if ($policy->type_currency == 'USD'){

            $webserviceController = new WebserviceAcselController();
            $trm = $webserviceController->getTrm();

            if ($trm ==0) {
                throw new \Exception('Error: El TRM no es valido');
            }

            $amountPolicy = $amountPolicy*$trm;
            $valorPagado = $valorPagado*$trm;
            $cta5 = '002';

        }
//        $valueCredit = $amountPolicy ;
//
//        $t_credit =  $valueCredit;
//        //Fila 2
//        $valueDebit2 = $amountPolicy * $accountsPercentages[5];
//        //Fila 3
//        $valueDebit3 = $amountPolicy * $accountsPercentages[13];
//        //Fila 4
//        $valueDebit4 = $amountPolicy * $accountsPercentages[7];
//        $valueDebit= $amountPolicy-$valueDebit2- $valueDebit3- $valueDebit4;
//        $t_debit  =  $valueDebit + $valueDebit2 + $valueDebit3 + $valueDebit4;

        $t_debit = $valorPagado;
        $t_credit = $amountPolicy;

        $diferencia = $valorPagado - $amountPolicy;
        $diferencia = $diferencia < 0 ? $diferencia*(-1) : $diferencia;

        $values =[];
        //Dependiendo del banco seleccionado en el pago
//        if ($policySort_colletion->bank_id == 2){
//            $values = [
//                ['debit' => $valueDebit,               'credit' => 0,                  'movement_type'=> 'D', 'movement_type_org'=> 'D', 'cta_1' => '001', 'cta_2' => '010', 'cta_3' => '030', 'cta_4' => '010', 'cta_5' => '001', 'cta_6' => '010', 'cta_7' => '000', 'cta_8' => '000', 'cta_9' => '000', 'cta_10' => '000','cod_grupo_cpto' => 'INGRES', 'cod_cpto' => 'MOVBAN', 'cor_relativo' => '005', 'descoper' => 'DEPOSITO BANCARIO', 'ctaux' => $ctaux, 'cod_oper'=>'017'],
//                ['debit' => 0,                         'credit' => $amountPolicy,     'movement_type'=> 'C', 'movement_type_org'=> 'C', 'cta_1' => '001', 'cta_2' => '040', 'cta_3' => '030', 'cta_4' => '010', 'cta_5' => '001', 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cta_9' => '000', 'cta_10' => '000','cod_grupo_cpto' => 'PRIMAS', 'cod_cpto' => 'RECIBO', 'cor_relativo' => '003', 'descoper' => 'COBRO DE PRIMAS (AM)', 'ctaux' => '**************', 'cod_oper'=>'003'],
//                ['debit' => $valueDebit2,              'credit' => 0,                  'movement_type'=> 'D', 'movement_type_org'=> 'D', 'cta_1' => '001', 'cta_2' => '090', 'cta_3' => '010', 'cta_4' => '010', 'cta_5' => '001', 'cta_6' => '010', 'cta_7' => '000', 'cta_8' => '000', 'cta_9' => '000', 'cta_10' => '000','cod_grupo_cpto' => 'INGRES', 'cod_cpto' => 'RETISR', 'cor_relativo' => '041', 'descoper' => 'COBRO DE PRIMAS (AM)', 'ctaux' => '**************', 'cod_oper'=>'003'],
//                ['debit' => $valueDebit3,              'credit' => 0,                  'movement_type'=> 'D', 'movement_type_org'=> 'D', 'cta_1' => '004', 'cta_2' => '060', 'cta_3' => '010', 'cta_4' => '010', 'cta_5' => '001', 'cta_6' => '000', 'cta_7' => '000', 'cta_8' => '000', 'cta_9' => '000', 'cta_10' => '000','cod_grupo_cpto' => 'INGRES', 'cod_cpto' => 'DCTCOM', 'cor_relativo' => '020', 'descoper' => 'COBRO DE PRIMAS (AM)', 'ctaux' => '**************', 'cod_oper'=>'003'],
//                ['debit' => $valueDebit4,              'credit' => 0,                  'movement_type'=> 'D', 'movement_type_org'=> 'D', 'cta_1' => '002', 'cta_2' => '040', 'cta_3' => '020', 'cta_4' => '010', 'cta_5' => '001', 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '000', 'cta_9' => '000', 'cta_10' => '000','cod_grupo_cpto' => 'INGRES', 'cod_cpto' => 'IMPTAR', 'cor_relativo' => '019', 'descoper' => 'COBRO DE PRIMAS (AM)', 'ctaux' => '**************', 'cod_oper'=>'003'],
//            ];
//        }else{
//            $values = [
//                ['debit' => $valueDebit,               'credit' => 0,                  'movement_type'=> 'D', 'movement_type_org'=> 'D', 'cta_1' => '001', 'cta_2' => '010', 'cta_3' => '030', 'cta_4' => '020', 'cta_5' => '001', 'cta_6' => '010', 'cta_7' => '000', 'cta_8' => '000', 'cta_9' => '000', 'cta_10' => '000','cod_grupo_cpto' => 'INGRES', 'cod_cpto' => 'MOVBAN', 'cor_relativo' => '005', 'descoper' => 'DEPOSITO BANCARIO', 'ctaux' => $ctaux, 'cod_oper'=>'017'],
//                ['debit' => 0,                         'credit' => $amountPolicy,     'movement_type'=> 'C', 'movement_type_org'=> 'C', 'cta_1' => '001', 'cta_2' => '040', 'cta_3' => '030', 'cta_4' => '010', 'cta_5' => '001', 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cta_9' => '000', 'cta_10' => '000','cod_grupo_cpto' => 'PRIMAS', 'cod_cpto' => 'RECIBO', 'cor_relativo' => '003', 'descoper' => 'COBRO DE PRIMAS (AM)', 'ctaux' => '**************', 'cod_oper'=>'003'],
//                ['debit' => $valueDebit2,              'credit' => 0,                  'movement_type'=> 'D', 'movement_type_org'=> 'D', 'cta_1' => '001', 'cta_2' => '090', 'cta_3' => '010', 'cta_4' => '010', 'cta_5' => '001', 'cta_6' => '010', 'cta_7' => '000', 'cta_8' => '000', 'cta_9' => '000', 'cta_10' => '000','cod_grupo_cpto' => 'INGRES', 'cod_cpto' => 'RETISR', 'cor_relativo' => '041', 'descoper' => 'COBRO DE PRIMAS (AM)', 'ctaux' => '**************', 'cod_oper'=>'003'],
//                ['debit' => $valueDebit3,              'credit' => 0,                  'movement_type'=> 'D', 'movement_type_org'=> 'D', 'cta_1' => '004', 'cta_2' => '060', 'cta_3' => '010', 'cta_4' => '010', 'cta_5' => '001', 'cta_6' => '000', 'cta_7' => '000', 'cta_8' => '000', 'cta_9' => '000', 'cta_10' => '000','cod_grupo_cpto' => 'INGRES', 'cod_cpto' => 'DCTCOM', 'cor_relativo' => '020', 'descoper' => 'COBRO DE PRIMAS (AM)', 'ctaux' => '**************', 'cod_oper'=>'003'],
//                ['debit' => $valueDebit4,              'credit' => 0,                  'movement_type'=> 'D', 'movement_type_org'=> 'D', 'cta_1' => '002', 'cta_2' => '040', 'cta_3' => '020', 'cta_4' => '010', 'cta_5' => '001', 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '000', 'cta_9' => '000', 'cta_10' => '000','cod_grupo_cpto' => 'INGRES', 'cod_cpto' => 'IMPTAR', 'cor_relativo' => '019', 'descoper' => 'COBRO DE PRIMAS (AM)', 'ctaux' => '**************', 'cod_oper'=>'003'],
//            ];
//        }

        //Dependiendo del banco seleccionado en el pago
        if ($policySort_colletion->bank_id == 2 || $policySort_colletion->bank_id == 4 ){ //banco_nacional o banco_costa_rica
            $values = [
                ['debit' => $valorPagado, 'credit' => 0,                  'movement_type'=> 'D', 'movement_type_org'=> 'D', 'cta_1' => '001', 'cta_2' => '010', 'cta_3' => '030', 'cta_4' => '010', 'cta_5' => $cta5, 'cta_6' => '010', 'cta_7' => '000', 'cta_8' => '000', 'cta_9' => '000', 'cta_10' => '000','cod_grupo_cpto' => 'INGRES', 'cod_cpto' => 'MOVBAN', 'cor_relativo' => '018', 'descoper' => 'DEPOSITO BANCARIO',    'ctaux' => $ctaux,           'cod_oper'=>'017'],
                ['debit' => 0,            'credit' => $amountPolicy,     'movement_type'=> 'C', 'movement_type_org'=> 'C', 'cta_1' => '001', 'cta_2' => '040', 'cta_3' => '030', 'cta_4' => '010', 'cta_5' => $cta5, 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cta_9' => '000', 'cta_10' => '000','cod_grupo_cpto' => 'PRIMAS', 'cod_cpto' => 'RECIBO', 'cor_relativo' => '019', 'descoper' => 'COBRO DE PRIMAS (AM)', 'ctaux' => '**************', 'cod_oper'=>'003']
            ];
        }else{ //Davivienda
            $values = [
                ['debit' => $valorPagado, 'credit' => 0,                  'movement_type'=> 'D', 'movement_type_org'=> 'D', 'cta_1' => '001', 'cta_2' => '010', 'cta_3' => '030', 'cta_4' => '020', 'cta_5' => $cta5, 'cta_6' => '010', 'cta_7' => '000', 'cta_8' => '000', 'cta_9' => '000', 'cta_10' => '000','cod_grupo_cpto' => 'INGRES', 'cod_cpto' => 'MOVBAN', 'cor_relativo' => '022', 'descoper' => 'DEPOSITO BANCARIO',    'ctaux' => $ctaux,           'cod_oper'=>'017'],
                ['debit' => 0,            'credit' => $amountPolicy,     'movement_type'=> 'C', 'movement_type_org'=> 'C', 'cta_1' => '001', 'cta_2' => '040', 'cta_3' => '030', 'cta_4' => '010', 'cta_5' => $cta5, 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cta_9' => '000', 'cta_10' => '000','cod_grupo_cpto' => 'PRIMAS', 'cod_cpto' => 'RECIBO', 'cor_relativo' => '019', 'descoper' => 'COBRO DE PRIMAS (AM)', 'ctaux' => '**************', 'cod_oper'=>'003']
            ];
        }

        if ($valorPagado < $amountPolicy) {
            $values[] = ['debit' => $diferencia,  'credit' => 0,                  'movement_type'=> 'D', 'movement_type_org'=> 'D', 'cta_1' => '004', 'cta_2' => '010', 'cta_3' => '080', 'cta_4' => '170', 'cta_5' => $cta5, 'cta_6' => '000', 'cta_7' => '000', 'cta_8' => '000', 'cta_9' => '000', 'cta_10' => '000','cod_grupo_cpto' => 'INGRES', 'cod_cpto' => 'DIFCAA', 'cor_relativo' => '030', 'descoper' => 'COBRO DE PRIMAS (AM)', 'ctaux' => '**************', 'cod_oper'=>'003'];
            $t_debit += $diferencia;
        } else {
            $values[] = ['debit' => 0,  'credit' => $diferencia,                  'movement_type'=> 'C', 'movement_type_org'=> 'C', 'cta_1' => '005', 'cta_2' => '010', 'cta_3' => '080', 'cta_4' => '170', 'cta_5' => $cta5, 'cta_6' => '000', 'cta_7' => '000', 'cta_8' => '000', 'cta_9' => '000', 'cta_10' => '000','cod_grupo_cpto' => 'INGRES', 'cod_cpto' => 'DIFCAA', 'cor_relativo' => '029', 'descoper' => 'COBRO DE PRIMAS (AM)', 'ctaux' => '**************', 'cod_oper'=>'003'];
            $t_credit += $diferencia;
        }


        $username = preg_replace('/^CO-/', '', $policy->code); // Remover el prefijo "CO-"

        $user = User::where(function ($query) use ($username) {
            $query->where('username', $username) // Buscar directamente sin "CO-"
            ->orWhere('username', 'CO-' . $username) // Buscar con "CO-"
            ->orWhere('code_mnk',$username)
                ->orWhere('code_mnk','CO-' . $username);
        })->first();

        $data = [];

        foreach ($values as $value) {
            $idecta = "01{$value['cta_1']}{$value['cta_2']}{$value['cta_3']}{$value['cta_4']}{$value['cta_5']}{$value['cta_6']}{$value['cta_7']}{$value['cta_8']}000000{$value['ctaux']}";
            $data[] = [
                'cod_cia' => '01',
                'cod_oper' => $value['cod_oper'],
                'receipt_number' => "RT-$nextEntryCode",
                'receipt_status' => 'ACT',
                'number_policy' => $policy->formatNumberConsecutive(),
                'number_payment' => $paymentId,
                'receipt_type' => '003',
                'date_register' => $policy->created_at,
                'descoper' =>  $value['descoper'],
                'cod_grupo_cpto' => $value['cod_grupo_cpto'],
                'cod_cpto' => $value['cod_cpto'],
                'cor_relativo' => $value['cor_relativo'],
                'cod_ramo' => 'SORT',
                'detail_movement' => $value['descoper']. "-" . $policy->formatNumberConsecutive(). "-" . $paymentId. "-" ."RT-$nextEntryCode".'-'.$policy->activity->affiliate->full_name. "-" . $value['cod_cpto'] ,
                'debit' => $value['debit'],
                'credit' => $value['credit'],
                't_debit' => $t_debit,
                't_credit' => $t_credit,
                'difference' => 0,
                'movement_type' => $value['movement_type'],
                'movement_type_org' => $value['movement_type_org'],
                'idecta' => $idecta,
                'cta_1' => $value['cta_1'],
                'cta_2' => $value['cta_2'],
                'cta_3' => $value['cta_3'],
                'cta_4' => $value['cta_4'],
                'cta_5' => $value['cta_5'],
                'cta_6' => $value['cta_6'],
                'cta_7' => $value['cta_7'],
                'cta_8' => $value['cta_8'],
                'cta_9' => $value['cta_9'],
                'cta_10' => $value['cta_10'],
                'document_type' => $policy->activity->affiliate->doc_type,
                'document_number' => $policy->activity->affiliate->doc_number,
                'name_taker' => $policy->activity->affiliate->full_name,
                'cod_intermediary' =>  isset($user) && isset($user->code_correduria) ? $user->code_correduria : $policy->code,
                'cod_moneda' => $policy->type_currency == 'USD' ? 'US' : 'CO',
                'exchange_rate' => $trm,
                'ctaux' => $value['ctaux'],
                'entry_code' => $nextEntryCode,
                'type' => isset($activityPayment) ? ($activityPayment->policy_sort_collection->type_receipt ?? '') : ''
            ];
        }

        AccountingEntry::insert($data);

    }

    /**
     * Reporte contable 003
     * Función Situación 4 Transacción Bancaria (TB) y el monto pagado es dirente al de la prima
     * */
    public function reportAccountCaseFour003($cpath, $id, $valorPagado){

        $policy = PolicySort::find($id);

        $ctaux_date = AppServiceProvider::$CTAUX;

        $nextEntryCode = AccountingEntry::max('entry_code');
        $nextEntryCode = sprintf('%04d',($nextEntryCode ?? 0) + 1);

        $activityPayment = Activity::where('parent_id', $policy->activity->id)
            ->where("service_id", Service::SERVICE_POLICY_SORT_COLLECTION_MNK)
            ->whereHas('policy_sort_collection', function ($query) {
                $query->where('payment_status', PolicySortCollection::PAYMENT_STATUS_APPROVED);
            })
            ->latest()
            ->first();

        $paymentId = $activityPayment->policy_sort_collection->id;
        $policySort_colletion = $activityPayment->policy_sort_collection;

        $currencyKey = ($policy->type_currency == 'CRC') ? 'CO' : 'DO';

        $username = preg_replace('/^CO-/', '', $policy->code); // Remover el prefijo "CO-"

        $user = User::where(function ($query) use ($username) {
            $query->where('username', $username) // Buscar directamente sin "CO-"
            ->orWhere('username', 'CO-' . $username) // Buscar con "CO-"
            ->orWhere('code_mnk',$username)
                ->orWhere('code_mnk','CO-' . $username);
        })->first();

        switch ($policySort_colletion->bank_id) {
            case 1:
                $ctaux = $ctaux_date['davivienda'][$currencyKey];
                break;
            case 2:
                $ctaux = $ctaux_date['banco_nacional'][$currencyKey];
                break;
            case 3:
                $ctaux = $ctaux_date['bac_redomatic'][$currencyKey];
                break;
            case 4:
                $ctaux = $ctaux_date['banco_costa_rica'][$currencyKey];
                break;
            default:
                $ctaux = '**************';
                break;
        }

        if ($policySort_colletion->payment_method == 'TC'){
            $ctaux = $ctaux_date['bac_redomatic'][$currencyKey];
        }

        $trm = 0;
        $amountPolicy = $activityPayment->policy_sort_collection->total_amount ?? 0;

        $cta5 = '001';
        $codRelativo1 = '005';
        $codRelativo2 = '003';
        $codRelativo3 = '021';
        $codRelativo4 = '003';
        $codRelativo5 = '033';
        $codRelativo6 = '031';

        if ($policy->type_currency == 'USD') {
            $webserviceController = new WebserviceAcselController();
            $trm = $webserviceController->getTrm();

            if ($trm ==0) {
                throw new \Exception('Error: El TRM no es valido');
            }

            $amountPolicy = $amountPolicy*$trm;
            $valorPagado = $valorPagado*$trm;
            $cta5 = '002';

            $codRelativo1 = '018';
            $codRelativo2 = '019';
            $codRelativo3 = '022';
            $codRelativo4 = '019';
            $codRelativo5 = '034';
            $codRelativo6 = '032';

        }

        $t_debit = $valorPagado;
        $t_credit = $amountPolicy;

        $diferencia = $valorPagado - $amountPolicy;
        $diferencia = $diferencia < 0 ? $diferencia*(-1) : $diferencia;

        $values = [];



        //Dependiendo del banco seleccionado en el pago
        if ($policySort_colletion->bank_id == 2 || $policySort_colletion->bank_id == 4 ){ //banco_nacional o banco_costa_rica
            $values = [
                ['debit' => $valorPagado, 'credit' => 0,                  'movement_type'=> 'D', 'movement_type_org'=> 'D', 'cta_1' => '001', 'cta_2' => '010', 'cta_3' => '030', 'cta_4' => '010', 'cta_5' => $cta5, 'cta_6' => '010', 'cta_7' => '000', 'cta_8' => '000', 'cta_9' => '000', 'cta_10' => '000','cod_grupo_cpto' => 'INGRES', 'cod_cpto' => 'MOVBAN', 'cor_relativo' => $codRelativo1, 'descoper' => 'DEPOSITO BANCARIO', 'ctaux' => $ctaux, 'cod_oper'=>'017'],
                ['debit' => 0,            'credit' => $amountPolicy,     'movement_type'=> 'C', 'movement_type_org'=> 'C', 'cta_1' => '001', 'cta_2' => '040', 'cta_3' => '030', 'cta_4' => '010', 'cta_5' => $cta5, 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cta_9' => '000', 'cta_10' => '000','cod_grupo_cpto' => 'PRIMAS', 'cod_cpto' => 'RECIBO', 'cor_relativo' => $codRelativo2,'descoper' => 'COBRO DE PRIMAS (AM)', 'ctaux' => '**************', 'cod_oper'=>'003']
            ];
        }else{ //Davivienda
            $values = [
                ['debit' => $valorPagado, 'credit' => 0,                  'movement_type'=> 'D', 'movement_type_org'=> 'D', 'cta_1' => '001', 'cta_2' => '010', 'cta_3' => '030', 'cta_4' => '020', 'cta_5' => $cta5, 'cta_6' => '010', 'cta_7' => '000', 'cta_8' => '000', 'cta_9' => '000', 'cta_10' => '000','cod_grupo_cpto' => 'INGRES', 'cod_cpto' => 'MOVBAN', 'cor_relativo' => $codRelativo3, 'descoper' => 'DEPOSITO BANCARIO', 'ctaux' => $ctaux, 'cod_oper'=>'017'],
                ['debit' => 0,            'credit' => $amountPolicy,     'movement_type'=> 'C', 'movement_type_org'=> 'C', 'cta_1' => '001', 'cta_2' => '040', 'cta_3' => '030', 'cta_4' => '010', 'cta_5' => $cta5, 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cta_9' => '000', 'cta_10' => '000','cod_grupo_cpto' => 'PRIMAS', 'cod_cpto' => 'RECIBO', 'cor_relativo' => $codRelativo4,'descoper' => 'COBRO DE PRIMAS (AM)', 'ctaux' => '**************', 'cod_oper'=>'003']
            ];
        }

        if ($valorPagado < $amountPolicy) {
            $values[] = ['debit' => $diferencia,  'credit' => 0,                  'movement_type'=> 'D', 'movement_type_org'=> 'D', 'cta_1' => '004', 'cta_2' => '060', 'cta_3' => '090', 'cta_4' => '990', 'cta_5' => $cta5, 'cta_6' => '990', 'cta_7' => '000', 'cta_8' => '000', 'cta_9' => '000', 'cta_10' => '599','cod_grupo_cpto' => 'INGRES', 'cod_cpto' => 'FALTAN', 'cor_relativo' => $codRelativo5, 'descoper' => 'COBRO DE PRIMAS (AM)', 'ctaux' => '**************', 'cod_oper'=>'003'];
            $t_debit += $diferencia;
        } else {
            $values[] = ['debit' => 0,  'credit' => $diferencia,                  'movement_type'=> 'C', 'movement_type_org'=> 'C', 'cta_1' => '005', 'cta_2' => '060', 'cta_3' => '090', 'cta_4' => '130', 'cta_5' => $cta5, 'cta_6' => '000', 'cta_7' => '000', 'cta_8' => '000', 'cta_9' => '000', 'cta_10' => '000','cod_grupo_cpto' => 'INGRES', 'cod_cpto' => 'SOBRAN', 'cor_relativo' => $codRelativo6, 'descoper' => 'COBRO DE PRIMAS (AM)', 'ctaux' => '**************', 'cod_oper'=>'003'];
            $t_credit += $diferencia;
        }

        $data = [];

        foreach ($values as $value) {
            $idecta = "01{$value['cta_1']}{$value['cta_2']}{$value['cta_3']}{$value['cta_4']}{$value['cta_5']}{$value['cta_6']}{$value['cta_7']}{$value['cta_8']}{$value['cta_9']}{$value['cta_10']}{$value['ctaux']}";
            $data[] = [
                'cod_cia' => '01',
                'cod_oper' => $value['cod_oper'],
                'receipt_number' => "RT-$nextEntryCode",
                'receipt_status' => 'ACT',
                'number_policy' => $policy->formatNumberConsecutive(),
                'number_payment' => $paymentId,
                'receipt_type' => '003',
                'date_register' => $policy->created_at,
                'descoper' =>  $value['descoper'],
                'cod_grupo_cpto' => $value['cod_grupo_cpto'],
                'cod_cpto' => $value['cod_cpto'],
                'cor_relativo' => $value['cor_relativo'],
                'cod_ramo' => 'SORT',
                'detail_movement' =>$value['descoper']. "-" . $policy->formatNumberConsecutive(). "-" . $paymentId. "-" ."RT-$nextEntryCode".'-'.$policy->activity->affiliate->full_name. "-" . $value['cod_cpto'] ,
                'debit' => $value['debit'],
                'credit' => $value['credit'],
                't_debit' => $t_debit,
                't_credit' => $t_credit,
                'difference' => 0,
                'movement_type' => $value['movement_type'],
                'movement_type_org' => $value['movement_type_org'],
                'idecta' => $idecta,
                'cta_1' => $value['cta_1'],
                'cta_2' => $value['cta_2'],
                'cta_3' => $value['cta_3'],
                'cta_4' => $value['cta_4'],
                'cta_5' => $value['cta_5'],
                'cta_6' => $value['cta_6'],
                'cta_7' => $value['cta_7'],
                'cta_8' => $value['cta_8'],
                'cta_9' => $value['cta_9'],
                'cta_10' => $value['cta_10'],
                'document_type' => $policy->activity->affiliate->doc_type,
                'document_number' => $policy->activity->affiliate->doc_number,
                'name_taker' => $policy->activity->affiliate->full_name,
                'cod_intermediary' =>  isset($user) && isset($user->code_correduria) ? $user->code_correduria : $policy->code ,
                'cod_moneda' => $policy->type_currency == 'USD' ? 'US' : 'CO',
                'exchange_rate' => $trm,
                'ctaux' => $value['ctaux'],
                'entry_code' => $nextEntryCode,
                'type' => isset($activityPayment) ? ($activityPayment->policy_sort_collection->type_receipt ?? '') : ''
            ];
        }

        AccountingEntry::insert($data);

    }

    /**
     * Reporte contable 100
     * Función Situación 1 Cuando la diferencia es mayor a 10US * TRM
     * */
    public function reportAccountCase100($cpath,$id){

        $ctaux_date = AppServiceProvider::$CTAUX;
        $ctaux = '';
        //Codigo unico para cada bloque de asientos contables
        $nextEntryCode = AccountingEntry::max('entry_code');
        $nextEntryCode = sprintf('%04d',($nextEntryCode ?? 0) + 1);

        $policy = PolicySort::find($id);

        //$policy = PolicySort::find($id);
        $activityPayment = Activity::where('parent_id', $policy->activity->id)
            ->where("service_id", Service::SERVICE_POLICY_SORT_COLLECTION_MNK)
            ->whereHas('policy_sort_collection', function ($query) {
                $query->where('payment_status', PolicySortCollection::PAYMENT_STATUS_APPROVED);
            })
            ->latest()
            ->first();

        $paymentId = $activityPayment->policy_sort_collection->id;
        $policySort_colletion = $activityPayment->policy_sort_collection;

        $currencyKey = ($policy->type_currency == 'CRC') ? 'CO' : 'DO';

        switch ($policySort_colletion->bank_id) {
            case 1:
                $ctaux = $ctaux_date['davivienda'][$currencyKey];
                break;
            case 2:
                $ctaux = $ctaux_date['banco_nacional'][$currencyKey];
                break;
            case 3:
                $ctaux = $ctaux_date['bac_redomatic'][$currencyKey];
                break;
            default:
                $ctaux = '**************';
                break;
        }

        if ($policySort_colletion->payment_method == 'TC'){
            $ctaux = $ctaux_date['bac_redomatic'][$currencyKey];
        }

        $valuePaid = $policySort_colletion->value_paid ?? 0;
        //$valuePaid = floatval(str_replace(['.', ','], ['', '.'], $valuePaid));

        $totalAmount = $policySort_colletion->total_amount ?? 0;

        $trm = 0;
        $valueCredit = 0;
        $valueDebit = 0;
        $value_difference_deposit = 0;
        //$amount_policy = floatval($policy->amount_policy);
        $difference_deposit = $valuePaid - $totalAmount;
        $cod_moneda = 'CO';
        $cta5 = '001';

        $corRelativo1 =  $policySort_colletion->bank_id == 2 ? '005' : '021';
        $corRelativo2 = '003';
        $corRelativo3 = '002';

        if ($policy->type_currency == 'USD'){
            $webserviceController = new WebserviceAcselController();
            $trm = $webserviceController->getTrm();

            if ($trm ==0) {
                throw new \Exception('Error: El TRM no es valido');
            }

            $totalAmount =  $totalAmount*$trm;
            $cod_moneda = 'US';
            $valueDebit = $totalAmount+$difference_deposit * $trm ;
            $value_difference_deposit = $difference_deposit * $trm ;
            $cta5 = '002';

            $corRelativo1 =  $policySort_colletion->bank_id == 2 ? '018' : '022';
            $corRelativo2 = '019';
        }

        $valueCredit = $totalAmount;
        $valueDebit = $totalAmount + $difference_deposit;
        $value_difference_deposit = $difference_deposit;

        $t_debit  =  $valueDebit ;
        $t_credit =  $valueCredit+$value_difference_deposit;

        $values =[];
        //Dependiendo del banco seleccionado en el pago
        if ($policySort_colletion->bank_id == 2){ //banco nacional
            $values = [
                ['debit' => $valueDebit,    'credit' => 0,                             'movement_type'=> 'D', 'movement_type_org'=> 'D', 'cta_1' => '001', 'cta_2' => '010', 'cta_3' => '030', 'cta_4' => '010', 'cta_5' => $cta5, 'cta_6' => '010', 'cta_7' => '000', 'cta_8' => '000', 'cta_9' => '000', 'cta_10' => '000','cod_grupo_cpto' => 'INGRES', 'cod_cpto' => 'MOVBAN', 'cor_relativo' => $corRelativo1, 'descoper' => 'DEPOSITO BANCARIO', 'ctaux' => $ctaux, 'cod_oper'=>'017'],
                ['debit' => 0,              'credit' => $valueCredit,                  'movement_type'=> 'C', 'movement_type_org'=> 'C', 'cta_1' => '001', 'cta_2' => '040', 'cta_3' => '030', 'cta_4' => '010', 'cta_5' => $cta5, 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cta_9' => '000', 'cta_10' => '000','cod_grupo_cpto' => 'PRIMAS', 'cod_cpto' => 'RECIBO', 'cor_relativo' => $corRelativo2, 'descoper' => 'COBRO DE PRIMAS (AM)', 'ctaux' => '**************', 'cod_oper'=>'003'],
                ['debit' => 0,              'credit' => $value_difference_deposit,     'movement_type'=> 'C', 'movement_type_org'=> 'C', 'cta_1' => '002', 'cta_2' => '070', 'cta_3' => '010', 'cta_4' => '010', 'cta_5' => $cta5, 'cta_6' => '010', 'cta_7' => '000', 'cta_8' => '010', 'cta_9' => '000', 'cta_10' => '000','cod_grupo_cpto' => 'PRIDE', 'cod_cpto' => 'PRIMAD', 'cor_relativo' => $corRelativo3, 'descoper' => 'REGISTRO DE PRIMA EN DEPOSITO X COBRO (AM)', 'ctaux' => '**************', 'cod_oper'=>'100'],
            ];
        }else{
            $values = [
                ['debit' => $valueDebit,    'credit' => 0,                             'movement_type'=> 'D', 'movement_type_org'=> 'D', 'cta_1' => '001', 'cta_2' => '010', 'cta_3' => '030', 'cta_4' => '020', 'cta_5' => $cta5, 'cta_6' => '010', 'cta_7' => '000', 'cta_8' => '000', 'cta_9' => '000', 'cta_10' => '000','cod_grupo_cpto' => 'INGRES', 'cod_cpto' => 'MOVBAN', 'cor_relativo' => $corRelativo1,'descoper' => 'DEPOSITO BANCARIO', 'ctaux' => $ctaux, 'cod_oper'=>'017'],
                ['debit' => 0,              'credit' => $valueCredit,                  'movement_type'=> 'C', 'movement_type_org'=> 'C', 'cta_1' => '001', 'cta_2' => '040', 'cta_3' => '030', 'cta_4' => '010', 'cta_5' => $cta5, 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cta_9' => '000', 'cta_10' => '000','cod_grupo_cpto' => 'PRIMAS', 'cod_cpto' => 'RECIBO', 'cor_relativo' => $corRelativo2, 'descoper' => 'COBRO DE PRIMAS (AM)', 'ctaux' => '**************', 'cod_oper'=>'003'],
                ['debit' => 0,              'credit' => $value_difference_deposit,     'movement_type'=> 'C', 'movement_type_org'=> 'C', 'cta_1' => '002', 'cta_2' => '070', 'cta_3' => '010', 'cta_4' => '010', 'cta_5' => $cta5, 'cta_6' => '010', 'cta_7' => '000', 'cta_8' => '010', 'cta_9' => '000', 'cta_10' => '000','cod_grupo_cpto' => 'PRIDE', 'cod_cpto' => 'PRIMAD', 'cor_relativo' => $corRelativo3, 'descoper' => 'REGISTRO DE PRIMA EN DEPOSITO X COBRO (AM)', 'ctaux' => '**************', 'cod_oper'=>'100'],
            ];
        }

        $username = preg_replace('/^CO-/', '', $policy->code); // Remover el prefijo "CO-"

        $user = User::where(function ($query) use ($username) {
            $query->where('username', $username) // Buscar directamente sin "CO-"
            ->orWhere('username', 'CO-' . $username); // Buscar con "CO-"
        })->first();

        // Array para almacenar los registros completos
        $data = [];

        // Recorre cada par de valores de debit y credit
        foreach ($values as $value) {
            $idecta = "01{$value['cta_1']}{$value['cta_2']}{$value['cta_3']}{$value['cta_4']}{$value['cta_5']}{$value['cta_6']}{$value['cta_7']}{$value['cta_8']}000000{$value['ctaux']}";
            $data[] = [
                'cod_cia' => '01',
                'cod_oper' => $value['cod_oper'],
                'receipt_number' => "RT-$nextEntryCode",
                'receipt_status' => 'ACT',
                'number_policy' => $policy->formatNumberConsecutive(),
                'number_payment' => $paymentId,
                'receipt_type' => '100',
                'date_register' => $policy->created_at,
                'descoper' =>  $value['descoper'],
                'cod_grupo_cpto' => $value['cod_grupo_cpto'],
                'cod_cpto' => $value['cod_cpto'],
                'cor_relativo' => $value['cor_relativo'],
                'cod_ramo' => 'SORT',
                'detail_movement' =>$value['descoper']. "-" . $policy->formatNumberConsecutive(). "-" . $paymentId. "-" ."RT-$nextEntryCode".'-'.$policy->activity->affiliate->full_name. "-" . $value['cod_cpto'] ,
                'debit' => $value['debit'],
                'credit' => $value['credit'],
                't_debit' => $t_debit,
                't_credit' => $t_credit,
                'difference' => $t_debit-$t_credit,
                'movement_type' => $value['movement_type'],
                'movement_type_org' => $value['movement_type_org'],
                'idecta' => $idecta,
                'cta_1' => $value['cta_1'],
                'cta_2' => $value['cta_2'],
                'cta_3' => $value['cta_3'],
                'cta_4' => $value['cta_4'],
                'cta_5' => $value['cta_5'],
                'cta_6' => $value['cta_6'],
                'cta_7' => $value['cta_7'],
                'cta_8' => $value['cta_8'],
                'cta_9' => $value['cta_9'],
                'cta_10' => $value['cta_10'],
                'document_type' => $policy->activity->affiliate->doc_type,
                'document_number' => $policy->activity->affiliate->doc_number,
                'name_taker' => $policy->activity->affiliate->full_name,
                'cod_intermediary' =>  $user->code_correduria ? $user->code_correduria : $policy->code,
                'cod_moneda' => $cod_moneda,
                'exchange_rate' => $trm,
                'ctaux' => $value['ctaux'],
                'entry_code' => $nextEntryCode,
                'type' => isset($activityPayment) ? ($activityPayment->policy_sort_collection->type_receipt ?? '') : ''
            ];
        }
        // Inserta todos los registros en la base de datos tabla de asientos contables
        AccountingEntry::insert($data);

    }

    /**
     * Reporte contable 011
     * MS-2049
     * */

    public function ejecutaAsiento011()
    {

        $consulta = "  SELECT ps.consecutive ,a.id as gis_activity, ps.type_currency , ps.id as policy_id, s.valor_a_pagar, s.fecha_creacion
                        FROM siniestros_temp s
                        LEFT JOIN gis_sort g ON g.consecutive=s.gis
                        LEFT JOIN activities a ON a.id=g.activity_id
                        left join activities ap on (ap.id = a.parent_id)
                        left join policy_sorts ps on (ps.activity_id=ap.id); ";

        $query = DB::select($consulta);

        foreach ($query as $item) {
            $policy = PolicySort::where('id', $item->policy_id)->first();
            $this->reportAccountCase011($policy, $item->valor_a_pagar, $item->gis_activity, $item->fecha_creacion) ;
        }

    }

    public function reportAccountCase011($policy, $value, $activity_gis, $fecha_creacion = null){

        //Codigo unico para cada bloque de asientos contables
        $nextEntryCode = AccountingEntry::max('entry_code');
        $nextEntryCode = sprintf('%04d',($nextEntryCode ?? 0) + 1);

        //Traemos el pago mediante la apoliza asociada
        $activityPayment = Activity::where('parent_id', $policy->activity->id)
            ->where("service_id", Service::SERVICE_POLICY_SORT_COLLECTION_MNK)
            ->whereHas('policy_sort_collection', function ($query) {
                $query->where('payment_status', PolicySortCollection::PAYMENT_STATUS_APPROVED);
            })
            ->latest()
            ->first();

        $trm = 0;
        //$amount_policy =  $policy->amount_policy;

        $cta5d = "001";
        $cta5c = "001";
        $ctauxC = '**************';
        if ($policy->type_currency == 'USD'){
            $webserviceController = new WebserviceAcselController();
            $trm = $webserviceController->getTrm();

            if ($trm ==0) {
                throw new \Exception('Error: El TRM no es valido');
            }

            $value =  $value*$trm;

            $cta5d = "002";
            $cta5c = "002";
            $ctauxC = '**************';
        }

        //Traemos los porcentajes de las tarifas para los calculos
        $accountsPercentages = AppServiceProvider::$ACCOUNTS_PERCENTAGES;

        $paymentId = $activityPayment->policy_sort_collection->id;
        // Array con valores específicos de debit y credit para cada fila
        $corRelativo = $policy->type_currency == 'USD' ? '067' : '068' ;

        $t_debit  =  $value ;
        $t_credit =  $value ;

        $values = [
            ['debit' => $value, 'credit' => 0,      'movement_type'=> 'D', 'movement_type_org'=> 'D', 'cta_1' => '004', 'cta_2' => '020', 'cta_3' => '010', 'cta_4' => '010', 'cta_5' => $cta5d, 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'SINIES', 'cod_cpto' => 'PGOMED', 'cor_relativo'  => $corRelativo, 'ctaux' => '**************'],
            ['debit' => 0,      'credit' => $value, 'movement_type'=> 'C', 'movement_type_org'=> 'C', 'cta_1' => '001', 'cta_2' => '010', 'cta_3' => '030', 'cta_4' => '010', 'cta_5' => $cta5c, 'cta_6' => '010', 'cta_7' => '000', 'cta_8' => '000', 'cod_grupo_cpto' => 'SINIES', 'cod_cpto' => 'GENERA', 'cor_relativo'  => $corRelativo, 'ctaux' => $ctauxC],
        ];

        $cod_cia = '01';
        // Array para almacenar los registros completos
        $data = [];

        $username = preg_replace('/^CO-/', '', $policy->code); // Remover el prefijo "CO-"

        $user = User::where(function ($query) use ($username) {
            $query->where('username', $username) // Buscar directamente sin "CO-"
            ->orWhere('username', 'CO-' . $username); // Buscar con "CO-"
        })->first();

        $casoGis = '';
        $trabajador = '';

        $gisSor = GisSort::where('activity_id',$activity_gis)->first();

        if($gisSor){
            $casoGis = $gisSor->id ?? '';
            $trabajador = $gisSor->activity->affiliate->full_name ?? '';
        }

        // Recorre cada par de valores de debit y credit
        foreach ($values as $value) {
            $idecta = "{$cod_cia}{$value['cta_1']}{$value['cta_2']}{$value['cta_3']}{$value['cta_4']}{$value['cta_5']}{$value['cta_6']}{$value['cta_7']}{$value['cta_8']}000000{$value['ctaux']}";

            $data[] = [
                'cod_cia' =>  $cod_cia,
                'cod_oper' => '011',
                'receipt_number' => "RT-$nextEntryCode",
                'receipt_status' => 'ACT',
                'number_policy' => $policy->formatNumberConsecutive(),
                'number_payment' => $paymentId,
                'receipt_type' => '011',
                'date_register' => $policy->created_at,
                'descoper' => 'PAGO DE SINIESTROS (SEGURO DIRECTO) (AM)',
                'cod_grupo_cpto' => $value['cod_grupo_cpto'],
                'cod_cpto' => $value['cod_cpto'],
                'cor_relativo' => $value['cor_relativo'],
                'cod_ramo' => 'SORT',
                'detail_movement' => 'PAGO DE SINIESTROS (SEGURO DIRECTO) (AM)-'.$policy->formatNumberConsecutive().'-'.$paymentId.'-'."RT-$nextEntryCode".'-'.$policy->activity->affiliate->doc_number.'-'.$value['cod_cpto'].'-'.$casoGis.'-'.$trabajador,
                'debit' => $value['debit'],
                'credit' => $value['credit'],
                't_debit' => $t_debit,
                't_credit' => $t_credit,
                'difference' => 0,
                'movement_type' => $value['movement_type'],
                'movement_type_org' => $value['movement_type_org'],
                'idecta' => $idecta,
                'cta_1' => $value['cta_1'],
                'cta_2' => $value['cta_2'],
                'cta_3' => $value['cta_3'],
                'cta_4' => $value['cta_4'],
                'cta_5' => $value['cta_5'],
                'cta_6' => $value['cta_6'],
                'cta_7' => $value['cta_7'],
                'cta_8' => $value['cta_8'],
                'cta_9' => '000',
                'cta_10' => '000',
                'document_type' => $policy->activity->affiliate->doc_type,
                'document_number' => $policy->activity->affiliate->doc_number,
                'name_taker' => $policy->activity->affiliate->full_name,
                'cod_intermediary' =>  $user->code_correduria ? $user->code_correduria : $policy->code ,
                'cod_moneda' => $policy->type_currency == 'USD' ? 'US' : 'CO',
                'exchange_rate' => $trm,
                'entry_code' => $nextEntryCode,
                'ctaux'  =>$value['ctaux'],
                'activity_gis' => $activity_gis,
                'caso_gis' => $casoGis,
                'type' => isset($activityPayment) ? ($activityPayment->policy_sort_collection->type_receipt ?? '') : '',
                'affected_worker' => $trabajador,
                'created_at' => $fecha_creacion ?? Carbon::now()
            ];
        }

        // Inserta todos los registros en la base de datos tabla de asientos contables
        AccountingEntry::insert($data);
    }

    /**
     * Reporte contable 308
     * MS-2075
     * */

    public function autoAccountCase308() {

        try {

            $numberPolicies = AccountingEntry::selectRaw("TRIM(LEADING '0' FROM REPLACE(number_policy, 'SORT-', '')) AS consecutive_policy")
                ->where('cod_moneda', 'US')
                ->where('cod_oper', '050')
                ->whereMonth('created_at', now()->month) // Obtiene el mes actual
                ->whereYear('created_at', now()->year) // Obtiene el año actual
                ->groupBy('number_policy')
                ->get();


            $webserviceController = new WebserviceAcselController();
            $trm = $webserviceController->getTrm();

            if ($trm ==0) {
                throw new \Exception('Error: El TRM no es valido');
            }

            foreach ($numberPolicies as $result) {
                $policy = PolicySort::where('consecutive', $result->consecutive_policy)->first();
                $this->reportAccountCase308($policy, $trm) ;

            }

            return response()->json(['success' => true], 200);

        } catch (\Exception $e) {

            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
            ],500);
        }
    }

    public function reportAccountCase308($policy, $trm){

        DB::beginTransaction();

        try{
            $account050 = DB::table('report_account_case_308')->where('consecutive_policy', $policy->consecutive)
                ->where('cod_oper', '050')
                ->first();

            $account087 = DB::table('report_account_case_308')->where('consecutive_policy', $policy->consecutive)
                ->where('cod_oper', '087')
                ->first();

            $account011 = DB::table('report_account_case_308')->where('consecutive_policy', $policy->consecutive)
                ->where('cod_oper', '011')
                ->first();

            $liberacionReserva = 0;
            $reserva = 0;
            $SaldoReserva = 0;

            $reservaCol = 0;

            if ($account050) {
                $reservaCol += $account050->dolares;
                $reserva = $account050->colones;
            }

            if ($account087) {
                $reservaCol += $account087->dolares;
                $liberacionReserva += $account087->colones;
            }

            if ($account011) {
                $reservaCol += $account011->dolares;
                $liberacionReserva += $account011->colones;
            }

            $SaldoReserva = $reserva - $liberacionReserva;

            $reservaHoy = $SaldoReserva * $trm;
            $total = $reservaHoy-$reservaCol;


            $nextEntryCode = AccountingEntry::max('entry_code');
            $nextEntryCode = sprintf('%04d',($nextEntryCode ?? 0) + 1);

            $activityPayment = Activity::where('parent_id', $policy->activity->id)
            ->where("service_id", Service::SERVICE_POLICY_SORT_COLLECTION_MNK)
            ->whereHas('policy_sort_collection', function ($query) {
                $query->where('payment_status', PolicySortCollection::PAYMENT_STATUS_APPROVED);
            })
            ->latest()
            ->first();

            $accountsPercentages = AppServiceProvider::$ACCOUNTS_PERCENTAGES;

            $paymentId = $activityPayment->policy_sort_collection->id;

            $t_debit  =  $total ;
            $t_credit =  $total ;

            if($total < 0){

                $total = $total * (-1);

                $values = [
                    ['debit' => $total, 'credit' => 0,      'movement_type'=> 'C', 'movement_type_org'=> 'C', 'cta_1' => '002', 'cta_2' => '050', 'cta_3' => '040', 'cta_4' => '050', 'cta_5' => '002', 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'PRIMAS', 'cod_cpto' => 'RECIBO', 'cor_relativo'  => '004', 'ctaux' => '**************'],
                    ['debit' => 0,      'credit' => $total, 'movement_type'=> 'D', 'movement_type_org'=> 'D', 'cta_1' => '004', 'cta_2' => '010', 'cta_3' => '080', 'cta_4' => '040', 'cta_5' => '002', 'cta_6' => '000', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'PRIMAS', 'cod_cpto' => 'RECIBO', 'cor_relativo'  => '002', 'ctaux' => '**************'],
                ];

            } else {

                $values = [
                    ['debit' => 0,      'credit' => $total, 'movement_type'=> 'D', 'movement_type_org'=> 'D', 'cta_1' => '002', 'cta_2' => '050', 'cta_3' => '040', 'cta_4' => '050', 'cta_5' => '002', 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'PRIMAS', 'cod_cpto' => 'RECIBO', 'cor_relativo'  => '004', 'ctaux' => '**************'],
                    ['debit' => $total, 'credit' => 0,      'movement_type'=> 'C', 'movement_type_org'=> 'C', 'cta_1' => '005', 'cta_2' => '010', 'cta_3' => '080', 'cta_4' => '040', 'cta_5' => '002', 'cta_6' => '000', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'PRIMAS', 'cod_cpto' => 'RECIBO', 'cor_relativo'  => '002', 'ctaux' => '**************'],
                ];

            }

            $cod_cia = '01';

            $data = [];

            $username = preg_replace('/^CO-/', '', $policy->code); // Remover el prefijo "CO-"

            $user = User::where(function ($query) use ($username) {
                $query->where('username', $username) // Buscar directamente sin "CO-"
                ->orWhere('username', 'CO-' . $username) // Buscar con "CO-"
                ->orWhere('code_mnk',$username)
                    ->orWhere('code_mnk','CO-' . $username);
            })->first();

            foreach ($values as $value) {
                $idecta = "{$cod_cia}{$value['cta_1']}{$value['cta_2']}{$value['cta_3']}{$value['cta_4']}{$value['cta_5']}{$value['cta_6']}{$value['cta_7']}{$value['cta_8']}000000{$value['ctaux']}";

                $data[] = [
                    'cod_cia' =>  $cod_cia,
                    'cod_oper' => '308',
                    'receipt_number' => "RT-$nextEntryCode",
                    'receipt_status' => 'ACT',
                    'number_policy' => $policy->formatNumberConsecutive(),
                    'number_payment' => $paymentId,
                    'receipt_type' => '087',
                    'date_register' => $policy->created_at,
                    'descoper' => 'DIFERENCIAL CAMBIARIO DE LA RESERVAS DE SINIESTROS',
                    'cod_grupo_cpto' => $value['cod_grupo_cpto'],
                    'cod_cpto' => $value['cod_cpto'],
                    'cor_relativo' => $value['cor_relativo'],
                    'cod_ramo' => 'SORT',
                    'detail_movement' => 'DIFERENCIAL CAMBIARIO DE LA RESERVAS DE SINIESTROS-'.$policy->formatNumberConsecutive().'-'.$paymentId.'-'."RT-$nextEntryCode".'-'.$policy->activity->affiliate->doc_number.'-'.$value['cod_cpto'],
                    'debit' => $value['debit'],
                    'credit' => $value['credit'],
                    't_debit' => $t_debit,
                    't_credit' => $t_credit,
                    'difference' => 0,
                    'movement_type' => $value['movement_type'],
                    'movement_type_org' => $value['movement_type_org'],
                    'idecta' => $idecta,
                    'cta_1' => $value['cta_1'],
                    'cta_2' => $value['cta_2'],
                    'cta_3' => $value['cta_3'],
                    'cta_4' => $value['cta_4'],
                    'cta_5' => $value['cta_5'],
                    'cta_6' => $value['cta_6'],
                    'cta_7' => $value['cta_7'],
                    'cta_8' => $value['cta_8'],
                    'cta_9' => '000',
                    'cta_10' => '000',
                    'document_type' => $policy->activity->affiliate->doc_type,
                    'document_number' => $policy->activity->affiliate->doc_number,
                    'name_taker' => $policy->activity->affiliate->full_name,
                    'cod_intermediary' =>  $user->code_correduria ? $user->code_correduria : $policy->code ,
                    'cod_moneda' => 'US',
                    'exchange_rate' => $trm,
                    'entry_code' => $nextEntryCode,
                    'ctaux'  =>$value['ctaux']
                ];
            }

            AccountingEntry::insert($data);
            DB::commit();

        }catch (\Exception $e){
            DB::rollBack();
            throw new \Exception('Error: '.$e->getMessage());
        }
    }

    /**
     * Reporte contable 050
     * Función con asiento contable para colones o dólares
     * */

    public function reportAccountCase050($activity_gis, $policy_id, $total)

    {

        $policy = PolicySort::where('activity_id', $policy_id)->first();

        $acountEntry050 = AccountingEntry::where('activity_gis', $activity_gis)->where('cod_oper', '050')->first();

        if (!$acountEntry050) {

            $activityPayment = Activity::where('parent_id', $policy->activity->id)
            ->where("service_id", Service::SERVICE_POLICY_SORT_COLLECTION_MNK)
            ->whereHas('policy_sort_collection', function ($query) {
                $query->where('payment_status', PolicySortCollection::PAYMENT_STATUS_APPROVED);
            })
            ->latest()
            ->first();

            $paymentId = $activityPayment->policy_sort_collection->id;

            $nextEntryCode = AccountingEntry::max('entry_code');
            $nextEntryCode = sprintf('%04d',($nextEntryCode ?? 0) + 1);
            $trm = 0;

            $cod_relative_c='001';
            $cod_relative_d='004';
            $cta_5 = '001';
            $total = str_replace(".", "", $total);
            $total = str_replace(",", ".", $total);
            $total = (float) $total;

            if ($policy->type_currency == 'USD'){
                $webserviceController = new WebserviceAcselController();
                $trm = $webserviceController->getTrm();

                if ($trm ==0) {
                    throw new \Exception('Error: El TRM no es valido');
                }

                $total =  $total*$trm;
                $cod_relative_c='003';
                $cod_relative_d='002';
                $cta_5 = '002';

            }
            $t_debit = $total;
            $t_credit = $total;

            $values = [
                ['debit' => 0,           'credit' => $total,     'movement_type'=> 'C', 'movement_type_org'=> 'C', 'cta_1' => '002', 'cta_2' => '050', 'cta_3' => '040', 'cta_4' => '050', 'cta_5' => $cta_5, 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'REASE', 'cod_cpto' => 'RESER', 'cor_relativo'  => $cod_relative_c, 'ctaux' => '**************'],
                ['debit' => $total,      'credit' => 0,          'movement_type'=> 'D', 'movement_type_org'=> 'D', 'cta_1' => '004', 'cta_2' => '080', 'cta_3' => '010', 'cta_4' => '030', 'cta_5' => $cta_5, 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'REASE', 'cod_cpto' => 'RESER', 'cor_relativo'  => $cod_relative_d, 'ctaux' => '**************'],
            ];

            $cod_cia = '01';
            // Array para almacenar los registros completos
            $data = [];

            $username = preg_replace('/^CO-/', '', $policy->code); // Remover el prefijo "CO-"


            $user = User::where(function ($query) use ($username) {
                $query->where('username', $username) // Buscar directamente sin "CO-"
                ->orWhere('username', 'CO-' . $username) // Buscar con "CO-"
                ->orWhere('code_mnk',$username)
                    ->orWhere('code_mnk','CO-' . $username);
            })->first();

            $casoGis = $this->getCasoGis($activity_gis);
            // Recorre cada par de valores de debit y credit
            foreach ($values as $value) {
                $idecta = "{$cod_cia}{$value['cta_1']}{$value['cta_2']}{$value['cta_3']}{$value['cta_4']}{$value['cta_5']}{$value['cta_6']}{$value['cta_7']}{$value['cta_8']}000000{$value['ctaux']}";

                $data[] = [
                    'cod_cia' =>  $cod_cia,
                    'cod_oper' => '050',
                    'receipt_number' => "RT-$nextEntryCode",
                    'receipt_status' => 'ACT',
                    'number_policy' => $policy->formatNumberConsecutive(),
                    'number_payment' => $paymentId,
                    'receipt_type' => '087',
                    'date_register' => $policy->created_at,
                    'descoper' => 'RESERVAS DE SINIESTROS',
                    'cod_grupo_cpto' => $value['cod_grupo_cpto'],
                    'cod_cpto' => $value['cod_cpto'],
                    'cor_relativo' => $value['cor_relativo'],
                    'cod_ramo' => 'SORT',
                    'detail_movement' => 'RESERVAS DE SINIESTROS'.$policy->formatNumberConsecutive().'-'.$paymentId.'-'."RT-$nextEntryCode".'-'.$policy->activity->affiliate->doc_number.'-'.$value['cod_cpto'].'-'.$casoGis,
                    'debit' => $value['debit'],
                    'credit' => $value['credit'],
                    't_debit' => $t_debit,
                    't_credit' => $t_credit,
                    'difference' => 0,
                    'movement_type' => $value['movement_type'],
                    'movement_type_org' => $value['movement_type_org'],
                    'idecta' => $idecta,
                    'cta_1' => $value['cta_1'],
                    'cta_2' => $value['cta_2'],
                    'cta_3' => $value['cta_3'],
                    'cta_4' => $value['cta_4'],
                    'cta_5' => $value['cta_5'],
                    'cta_6' => $value['cta_6'],
                    'cta_7' => $value['cta_7'],
                    'cta_8' => $value['cta_8'],
                    'cta_9' => '000',
                    'cta_10' => '000',
                    'document_type' => $policy->activity->affiliate->doc_type,
                    'document_number' => $policy->activity->affiliate->doc_number,
                    'name_taker' => $policy->activity->affiliate->full_name,
                    'cod_intermediary' =>  $user && is_object($user) && isset($user->code_correduria) ? $user->code_correduria : $policy->code,
                    'cod_moneda' => $policy->type_currency == 'USD' ? 'US' : 'CO',
                    'exchange_rate' => $trm,
                    'entry_code' => $nextEntryCode,
                    'ctaux'  =>$value['ctaux'],
                    'activity_gis' => $activity_gis,
                    'caso_gis' => $casoGis
                ];
            }
            // Inserta todos los registros en la base de datos tabla de asientos contables
            AccountingEntry::insert($data);
        }

    }

    /**
     * Reporte contable 107
     * MS-2216
     * */
    public function reportAccountCase107($policy){

        try{

            $nextEntryCode = AccountingEntry::max('entry_code');
            $nextEntryCode = sprintf('%04d',($nextEntryCode ?? 0) + 1);

            $activityPayment = Activity::where('parent_id', $policy->activity->id)
            ->where("service_id", Service::SERVICE_POLICY_SORT_COLLECTION_MNK)
            ->whereHas('policy_sort_collection', function ($query) {
                $query->where('payment_status', PolicySortCollection::PAYMENT_STATUS_APPROVED);
            })
            ->latest()
            ->first();

            $trm = 0;

            $amountPolicy = $activityPayment->policy_sort_collection->total_amount ?? 0;

            $cta5 = '001';

            if ($policy->type_currency == 'USD'){
                $webserviceController = new WebserviceAcselController();
                $trm = $webserviceController->getTrm();

                if ($trm ==0) {
                    throw new \Exception('Error: El TRM no es valido');
                }

                $amountPolicy =  $policy->amount_policy*$trm;
                $cta5 = '002';
            }

            $accountsPercentages = AppServiceProvider::$ACCOUNTS_PERCENTAGES;
            $paymentId = $activityPayment->policy_sort_collection->id;

            $t_debit  =  $amountPolicy ;
            $t_credit =  $amountPolicy ;

            $values = [
                ['debit' => 0,              'credit' => $amountPolicy, 'movement_type'=> 'C', 'movement_type_org'=> 'C', 'cta_1' => '002', 'cta_2' => '050', 'cta_3' => '010', 'cta_4' => '010', 'cta_5' => $cta5, 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'PRIMAS', 'cod_cpto' => 'RECIBO', 'cor_relativo' => '003'],
                ['debit' => $amountPolicy, 'credit' => 0,              'movement_type'=> 'D', 'movement_type_org'=> 'D', 'cta_1' => '004', 'cta_2' => '080', 'cta_3' => '010', 'cta_4' => '010', 'cta_5' => $cta5, 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'PRIMAS', 'cod_cpto' => 'RECIBO', 'cor_relativo' => '001'],
            ];

            $data = [];

            $username = preg_replace('/^CO-/', '', $policy->code); // Remover el prefijo "CO-"

            $user = User::where(function ($query) use ($username) {
                $query->where('username', $username) // Buscar directamente sin "CO-"
                ->orWhere('username', 'CO-' . $username) // Buscar con "CO-"
                ->orWhere('code_mnk',$username)
                ->orWhere('code_mnk','CO-' . $username);
            })->first();

            foreach ($values as $value) {

                $idecta = "01{$value['cta_1']}{$value['cta_2']}{$value['cta_3']}{$value['cta_4']}{$value['cta_5']}{$value['cta_6']}{$value['cta_7']}{$value['cta_8']}000000"."**************";

                $data[] = [
                    'cod_cia' => '01',
                    'cod_oper' => '107',
                    'receipt_number' => "RT-$nextEntryCode",
                    'receipt_status' => 'ACT',
                    'number_policy' => $policy->formatNumberConsecutive(),
                    'number_payment' => $paymentId,
                    'receipt_type' => '107',
                    'date_register' => $policy->created_at,
                    'descoper' => 'CONSTITUCION DE PROVISION PARA PRIMA NO DEVENGADA',
                    'cod_grupo_cpto' => $value['cod_grupo_cpto'],
                    'cod_cpto' => $value['cod_cpto'],
                    'cor_relativo' => $value['cor_relativo'],
                    'cod_ramo' => 'SORT',
                    'detail_movement' => 'CONSTITUCION DE PROVISION PARA PRIMA NO DEVENGADA-'.$policy->formatNumberConsecutive().'-'.$paymentId.'-'."RT-$nextEntryCode".'-'.$policy->activity->affiliate->first_name.$value['cod_cpto'],
                    'debit' => $value['debit'],
                    'credit' => $value['credit'],
                    't_debit' => $t_debit,
                    't_credit' => $t_credit,
                    'difference' => ($t_debit-$t_credit),
                    'movement_type' => $value['movement_type'],
                    'movement_type_org' => $value['movement_type_org'],
                    'idecta' => $idecta,
                    'cta_1' => $value['cta_1'],
                    'cta_2' => $value['cta_2'],
                    'cta_3' => $value['cta_3'],
                    'cta_4' => $value['cta_4'],
                    'cta_5' => $value['cta_5'],
                    'cta_6' => $value['cta_6'],
                    'cta_7' => $value['cta_7'],
                    'cta_8' => $value['cta_8'],
                    'cta_9' => '000',
                    'cta_10' => '000',
                    'document_type' => $policy->activity->affiliate->doc_type,
                    'document_number' => $policy->activity->affiliate->doc_number,
                    'name_taker' => $policy->activity->affiliate->full_name,
                    'cod_intermediary' =>  isset($user) && isset($user->code_correduria) ? $user->code_correduria : $policy->code,
                    'cod_moneda' => $policy->type_currency == 'USD' ? 'US' : 'CO',
                    'exchange_rate' => $trm,
                    'entry_code' => $nextEntryCode,
                    'ctaux'  => "**************",
                    'type' => isset($activityPayment) ? ($activityPayment->policy_sort_collection->type_receipt ?? '') : ''
                ];
            }

            AccountingEntry::insert($data);

        }catch (\Exception $e){
            throw new \Exception('Error asiento reportAccountCase107: '.$e->getMessage());
        }
    }

    /**
     * Reporte contable 120
     * MS-2217
     * */

    public function reportAccountCase120($policy){

        try{

            $nextEntryCode = AccountingEntry::max('entry_code');
            $nextEntryCode = sprintf('%04d',($nextEntryCode ?? 0) + 1);

            $activityPayment = Activity::where('parent_id', $policy->activity->id)
            ->where("service_id", Service::SERVICE_POLICY_SORT_COLLECTION_MNK)
            ->whereHas('policy_sort_collection', function ($query) {
                $query->where('payment_status', PolicySortCollection::PAYMENT_STATUS_APPROVED);
            })
            ->latest()
            ->first();

            $trm = 0;
            $amountPolicy = $activityPayment->policy_sort_collection->total_amount ?? 0;
            $cta5 = '001';

            if ($policy->type_currency == 'USD'){
                $webserviceController = new WebserviceAcselController();
                $trm = $webserviceController->getTrm();

                if ($trm ==0) {
                    throw new \Exception('Error: El TRM no es valido');
                }

                $amountPolicy =  $amountPolicy*$trm;
                $cta5 = '002';
            }

            $accountsPercentages = AppServiceProvider::$ACCOUNTS_PERCENTAGES;
            $paymentId = $activityPayment->policy_sort_collection->id;

            $t_debit  =  $amountPolicy ;
            $t_credit =  $amountPolicy ;

            $values = [
                ['debit' => $amountPolicy, 'credit' => 0 ,             'movement_type'=> 'D', 'movement_type_org'=> 'D', 'cta_1' => '002', 'cta_2' => '050', 'cta_3' => '010', 'cta_4' => '010', 'cta_5' => $cta5, 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'PRIMAS', 'cod_cpto' => 'RECIBO', 'cor_relativo' => '008'],
                ['debit' => 0,              'credit' => $amountPolicy, 'movement_type'=> 'C', 'movement_type_org'=> 'C', 'cta_1' => '005', 'cta_2' => '070', 'cta_3' => '010', 'cta_4' => '070', 'cta_5' => $cta5, 'cta_6' => '020', 'cta_7' => '010', 'cta_8' => '180', 'cod_grupo_cpto' => 'PRIMAS', 'cod_cpto' => 'RECIBO', 'cor_relativo' => '006'],
            ];

            $username = preg_replace('/^CO-/', '', $policy->code); // Remover el prefijo "CO-"


            $user = User::where(function ($query) use ($username) {
                $query->where('username', $username) // Buscar directamente sin "CO-"
                ->orWhere('username', 'CO-' . $username) // Buscar con "CO-"
                ->orWhere('code_mnk',$username)
                    ->orWhere('code_mnk','CO-' . $username);
            })->first();

            $data = [];

            foreach ($values as $value) {

                $idecta = "{$value['cta_1']}{$value['cta_2']}{$value['cta_3']}{$value['cta_4']}{$value['cta_5']}{$value['cta_6']}{$value['cta_7']}{$value['cta_8']}000000"."**************";

                $data[] = [
                    'cod_cia' => '01',
                    'cod_oper' => '120',
                    'receipt_number' => "RT-$nextEntryCode",
                    'receipt_status' => 'ACT',
                    'number_policy' => $policy->formatNumberConsecutive(),
                    'number_payment' => $paymentId,
                    'receipt_type' => '120',
                    'date_register' => $policy->created_at,
                    'descoper' => 'REV CONST DE PROVISION PARA PRIMA NO DEVENGADA',
                    'cod_grupo_cpto' => $value['cod_grupo_cpto'],
                    'cod_cpto' => $value['cod_cpto'],
                    'cor_relativo' => $value['cor_relativo'],
                    'cod_ramo' => 'SORT',
                    'detail_movement' => 'REV CONST DE PROVISION PARA PRIMA NO DEVENGADA-'.$policy->formatNumberConsecutive().'-'.$paymentId.'-'."RT-$nextEntryCode".'-'.$policy->activity->affiliate->first_name.$value['cod_cpto'],
                    'debit' => $value['debit'],
                    'credit' => $value['credit'],
                    't_debit' => $t_debit,
                    't_credit' => $t_credit,
                    'difference' => ($t_debit-$t_credit),
                    'movement_type' => $value['movement_type'],
                    'movement_type_org' => $value['movement_type_org'],
                    'idecta' => $idecta,
                    'cta_1' => $value['cta_1'],
                    'cta_2' => $value['cta_2'],
                    'cta_3' => $value['cta_3'],
                    'cta_4' => $value['cta_4'],
                    'cta_5' => $value['cta_5'],
                    'cta_6' => $value['cta_6'],
                    'cta_7' => $value['cta_7'],
                    'cta_8' => $value['cta_8'],
                    'cta_9' => '000',
                    'cta_10' => '000',
                    'document_type' => $policy->activity->affiliate->doc_type,
                    'document_number' => $policy->activity->affiliate->doc_number,
                    'name_taker' => $policy->activity->affiliate->full_name,
                    'cod_intermediary' =>  $user->code_correduria ? $user->code_correduria : $policy->code,
                    'cod_moneda' => $policy->type_currency == 'USD' ? 'US' : 'CO',
                    'exchange_rate' => $trm,
                    'entry_code' => $nextEntryCode,
                    'ctaux'  => "**************"
                ];
            }

            AccountingEntry::insert($data);

            $result = [
                'success' => true,
                'value'   => $amountPolicy,
                'id'      => $policy->id,
            ];

            return $result;

        }catch (\Exception $e){
            throw new \Exception('Error asiento reportAccountCase120 : '.$e->getMessage());
        }
    }

    /**
     * Generacion automatica reporte contable 087
     * ME-2807
     * */
    public function autoAccountCase087() {

        try {

            $consulta = "select * from report_087_inactive_gis where asiento087_total <> 0";
            $query = DB::select($consulta);

            foreach ($query as $result) {
                $this->reportAccountCase087('', $result->policy_id, $result->asiento087_total, $result->activity_gis, $result->caso_gis, $result->trabajador) ;

                $activityAction = ActionController::create($result->activity_gis, ActionGisSort::REPORTAR_CIERRE_CASO_AUTOMATICO,  'Reportar cierre de caso automático');
            }

            return response()->json(['success' => true], 200);

        } catch (\Exception $e) {

            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
            ],500);
        }
    }

    /**
     * Reporte contable 087
     * MS-2048
     * */

    public function reportAccountCase087($cpath, $policy, $value, $activity_gis, $casoGis, $trabajador){

        $policy = PolicySort::find($policy);
        
        //Codigo unico para cada bloque de asientos contables
        $nextEntryCode = AccountingEntry::max('entry_code');
        $nextEntryCode = sprintf('%04d',($nextEntryCode ?? 0) + 1);

        //Traemos el pago mediante la apoliza asociada
        $activityPayment = Activity::where('parent_id', $policy->activity->id)
            ->where("service_id", Service::SERVICE_POLICY_SORT_COLLECTION_MNK)
            ->whereHas('policy_sort_collection', function ($query) {
                $query->where('payment_status', PolicySortCollection::PAYMENT_STATUS_APPROVED);
            })
            ->latest()
            ->first();

        $trm = 0;
        //$amount_policy =  $policy->amount_policy;
        $totalAsientos=$value;

        $cta5d = "001";
        $cta5c = "001";

        if ($policy->type_currency == 'USD'){
            $webserviceController = new WebserviceAcselController();
            $trm = $webserviceController->getTrm();

            if ($trm ==0) {
                throw new \Exception('Error: El TRM no es valido');
            }

            $value =  $value*$trm;

            $cta5d = "002";
            $cta5c = "002";

        }

        //Traemos los porcentajes de las tarifas para los calculos
        $accountsPercentages = AppServiceProvider::$ACCOUNTS_PERCENTAGES;

        $paymentId = $activityPayment->policy_sort_collection->id;
        // Array con valores específicos de debit y credit para cada fila

        $t_debit  =  $value ;
        $t_credit =  $value ;

        $values = [
            ['debit' => $value, 'credit' => 0,      'movement_type'=> 'D', 'movement_type_org'=> 'D', 'cta_1' => '002', 'cta_2' => '050', 'cta_3' => '040', 'cta_4' => '050', 'cta_5' => $cta5d, 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'REASE', 'cod_cpto' => 'RESER', 'cor_relativo'  => '003', 'ctaux' => '**************'],
            ['debit' => 0,      'credit' => $value, 'movement_type'=> 'C', 'movement_type_org'=> 'C', 'cta_1' => '005', 'cta_2' => '070', 'cta_3' => '010', 'cta_4' => '030', 'cta_5' => $cta5c, 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'REASE', 'cod_cpto' => 'RESER', 'cor_relativo'  => '002', 'ctaux' => '**************'],
        ];

        $cod_cia = '01';
        // Array para almacenar los registros completos
        $data = [];

        $username = preg_replace('/^CO-/', '', $policy->code); // Remover el prefijo "CO-"

        $user = User::where(function ($query) use ($username) {
            $query->where('username', $username) // Buscar directamente sin "CO-"
            ->orWhere('username', 'CO-' . $username); // Buscar con "CO-"
        })->first();

        // Recorre cada par de valores de debit y credit
        foreach ($values as $value) {
            $idecta = "01{$value['cta_1']}{$value['cta_2']}{$value['cta_3']}{$value['cta_4']}{$value['cta_5']}{$value['cta_6']}{$value['cta_7']}{$value['cta_8']}000000{$value['ctaux']}";

            $data[] = [
                'cod_cia' =>  $cod_cia,
                'cod_oper' => '087',
                'receipt_number' => "RT-$nextEntryCode",
                'receipt_status' => 'ACT',
                'number_policy' => $policy->formatNumberConsecutive(),
                'number_payment' => $paymentId,
                //se cambia el recept type
                'receipt_type' => '109',
                'date_register' => $policy->created_at,
                'descoper' => 'LIBERACION DE LA  RESERVA DE SINIESTROS',
                'cod_grupo_cpto' => $value['cod_grupo_cpto'],
                'cod_cpto' => $value['cod_cpto'],
                'cor_relativo' => $value['cor_relativo'],
                'cod_ramo' => 'SORT',
                'detail_movement' => 'LIBERACION DE LA  RESERVA DE SINIESTROS-'.$policy->formatNumberConsecutive().'-'.$paymentId.'-'."RT-$nextEntryCode".'-'.$policy->activity->affiliate->doc_number.'-'.$value['cod_cpto'].'-'.$casoGis.'-'.$trabajador,
                'debit' => $value['debit'],
                'credit' => $value['credit'],
                't_debit' => $t_debit,
                't_credit' => $t_credit,
                'difference' => 0,
                'movement_type' => $value['movement_type'],
                'movement_type_org' => $value['movement_type_org'],
                'idecta' => $idecta,
                'cta_1' => $value['cta_1'],
                'cta_2' => $value['cta_2'],
                'cta_3' => $value['cta_3'],
                'cta_4' => $value['cta_4'],
                'cta_5' => $value['cta_5'],
                'cta_6' => $value['cta_6'],
                'cta_7' => $value['cta_7'],
                'cta_8' => $value['cta_8'],
                'cta_9' => '000',
                'cta_10' => '000',
                'document_type' => $policy->activity->affiliate->doc_type,
                'document_number' => $policy->activity->affiliate->doc_number,
                'name_taker' => $policy->activity->affiliate->full_name,
                'cod_intermediary' => $user && is_object($user) && isset($user->code_correduria) ? $user->code_correduria : $policy->code ,
                'cod_moneda' => $policy->type_currency == 'USD' ? 'US' : 'CO',
                'exchange_rate' => $trm,
                'entry_code' => $nextEntryCode,
                'ctaux'  =>$value['ctaux'],
                 'activity_gis' => $activity_gis,
                'caso_gis' => $casoGis,
                'type' => isset($activityPayment) ? ($activityPayment->policy_sort_collection->type_receipt ?? '') : '',
                'affected_worker' => $trabajador
            ];
        }
        // Inserta todos los registros en la base de datos tabla de asientos contables
        AccountingEntry::insert($data);

        //$this->reportAccountCase155($activity_gis, $policy->id, $totalAsientos);
        //$this->reportAccountCase156($activity_gis, $policy->id, $totalAsientos);
    }

    /**
     * Reporte contable 305
     * MS-2222
     * */
    public function autoAccountCase305() {

        try {

            $numberPolicies = DB::table('release_ppnd_dollars_reserves as r')
                ->select(
                    'number_policy',
                    DB::raw('SUM(amount_release_daily * r.trm) AS libera_reserva_crc'),
                    DB::raw("SUM(
                                    case when r.movement_type = '120' then
                                      (
                                          (CASE WHEN r.movement_value IS NULL THEN 0 ELSE r.movement_value END * r.trm) 
                                          +
                                          (amount_release_daily * r.trm)
                                      )*-1
                                    ELSE 
                                    (CASE WHEN r.movement_value IS NULL THEN 0 ELSE r.movement_value END * r.trm) 
                                      - 
                                      (amount_release_daily * r.trm)
                                    END
                            ) AS saldo_acumulado"),
                    DB::raw('SUM(amount_release_daily) AS libera_reserva_usd')
                )
                ->whereRaw('MONTH(r.date_reserve) = MONTH(CURDATE())')
                ->whereRaw('YEAR(r.date_reserve) = YEAR(CURDATE())')
                ->where('trm', '<>', 0)
                ->whereNotNull('trm')
                ->groupBy('number_policy')
                ->get();

            $webserviceController = new WebserviceAcselController();
            $trm = $webserviceController->getTrm();

            if ($trm ==0) {
                throw new \Exception('Error: El TRM no es valido');
            }

            foreach ($numberPolicies as $result) {

                $nextEntryCode = AccountingEntry::max('entry_code');
                $nextEntryCode = sprintf('%04d',($nextEntryCode ?? 0) + 1);

                $policy = PolicySort::where('consecutive', $result->number_policy)->first();
                $this->reportAccountCase305($result, $policy, $trm, $nextEntryCode) ;
            }

            return response()->json(['success' => true], 200);

        } catch (\Exception $e) {

            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
            ],500);
        }
    }

    public function reportAccountCase305($result, $policy, $trm, $nextEntryCode){

        $latestRelease = DB::table('release_ppnd_dollars_reserves as r')
            ->where('number_policy', $result->number_policy)
            ->where('trm', '<>', 0)
            ->whereNotNull('trm')
            ->orderByDesc('id')
            ->first();

        $variacion_recerva = $result->libera_reserva_usd;
        $reserva = $latestRelease->amount_accounty_entry_107;
        $saldo_acumulado = $result->saldo_acumulado;
        $saldo_reserva = $reserva-$variacion_recerva;
        $saldo_cambio_cierre = $saldo_reserva*$trm;
        $difCambio = $saldo_cambio_cierre-$saldo_acumulado;

//        dd([
//            'variacion_recerva' => $variacion_recerva,
//            'reserva' => $reserva,
//            'saldo_acumulado' => $saldo_acumulado,
//            'saldo_reserva' => $saldo_reserva,
//            'saldo_cambio_cierre' => $saldo_cambio_cierre,
//            'difCambio' => $difCambio,
//        ]);

        $paymentId = 'NA';

        $t_debit  =  $difCambio ;
        $t_credit =  $difCambio ;

        if ($difCambio < 0) {

            $difCambio = $difCambio * (-1);

            $values = [
                ['debit' => $difCambio, 'credit' => 0,      'movement_type'=> 'D', 'movement_type_org'=> 'D', 'cta_1' => '002',  'cta_2' => '050', 'cta_3' => '010', 'cta_4' => '010', 'cta_5' => '002', 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'REASE', 'cod_cpto' => 'RECIBO', 'cor_relativo'  => '004', 'ctaux' => '**************'],
                ['debit' => 0,      'credit' => $difCambio, 'movement_type'=> 'C', 'movement_type_org'=> 'C', 'cta_1' => '005', 'cta_2' => '010', 'cta_3' => '080', 'cta_4' => '040', 'cta_5' => '002', 'cta_6' => '000', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'REASE', 'cod_cpto' => 'RECIBO', 'cor_relativo'  => '002', 'ctaux' => '**************'],
            ];
        } else {
            $values = [
                ['debit' => 0,              'credit' => $difCambio,      'movement_type'=> 'C', 'movement_type_org'=> 'C', 'cta_1' => '002',  'cta_2' => '050', 'cta_3' => '010', 'cta_4' => '010', 'cta_5' => '002', 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'REASE', 'cod_cpto' => 'RECIBO', 'cor_relativo'  => '004', 'ctaux' => '**************'],
                ['debit' => $difCambio,     'credit' => 0,               'movement_type'=> 'D', 'movement_type_org'=> 'D', 'cta_1' => '004', 'cta_2' => '010', 'cta_3' => '080', 'cta_4' => '040', 'cta_5' => '002', 'cta_6' => '000', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'REASE', 'cod_cpto' => 'RECIBO', 'cor_relativo'  => '002', 'ctaux' => '**************'],
            ];
        }


        $cod_cia = '01';
        $data = [];

        $username = preg_replace('/^CO-/', '', $policy->code); // Remover el prefijo "CO-"
        $user = User::where(function ($query) use ($username) {
            $query->where('username', $username) // Buscar directamente sin "CO-"
            ->orWhere('username', 'CO-' . $username); // Buscar con "CO-"
        })->first();

        foreach ($values as $value) {
            $idecta = "01{$cod_cia}{$value['cta_1']}{$value['cta_2']}{$value['cta_3']}{$value['cta_4']}{$value['cta_5']}{$value['cta_6']}{$value['cta_7']}{$value['cta_8']}000000{$value['ctaux']}";

            $data[] = [
                'cod_cia' =>  $cod_cia,
                'cod_oper' => '305',
                'receipt_number' => "RT-$nextEntryCode",
                'receipt_status' => 'ACT',
                'number_policy' => $policy->formatNumberConsecutive(),
                'number_payment' => $paymentId,
                'receipt_type' => '305',
                'date_register' => Carbon::now(),
                'descoper' => 'DIFERENCIAL CAMBIARIO DE LA  RESERVA DE LA PRIMA PPND',
                'cod_grupo_cpto' => $value['cod_grupo_cpto'],
                'cod_cpto' => $value['cod_cpto'],
                'cor_relativo' => $value['cor_relativo'],
                'cod_ramo' => 'SORT',
                'detail_movement' => 'DIFERENCIAL CAMBIARIO DE LA  RESERVA DE LA PRIMA PPND-'.$policy->formatNumberConsecutive().'-'.$paymentId.'-'."RT-$nextEntryCode".'-'.'NA'.'-'.$value['cod_cpto'],
                'debit' => $value['debit'],
                'credit' => $value['credit'],
                't_debit' => $t_debit,
                't_credit' => $t_credit,
                'difference' => 0,
                'movement_type' => $value['movement_type'],
                'movement_type_org' => $value['movement_type_org'],
                'idecta' => $idecta,
                'cta_1' => $value['cta_1'],
                'cta_2' => $value['cta_2'],
                'cta_3' => $value['cta_3'],
                'cta_4' => $value['cta_4'],
                'cta_5' => $value['cta_5'],
                'cta_6' => $value['cta_6'],
                'cta_7' => $value['cta_7'],
                'cta_8' => $value['cta_8'],
                'cta_9' => '000',
                'cta_10' => '000',
                'document_type' => $policy->activity->affiliate->doc_type,
                'document_number' => $policy->activity->affiliate->doc_number,
                'name_taker' => $policy->activity->affiliate->full_name,
                'cod_intermediary' =>  $user && $user->code_correduria ? $user->code_correduria : $policy->code,
                'cod_moneda' => 'US',
                'exchange_rate' => $trm,
                'entry_code' => $nextEntryCode,
                'ctaux'  =>$value['ctaux']
            ];
        }

        AccountingEntry::insert($data);
    }



    /**
     * Reporte contable 108 LIBERACIÓN PPND EN COLONES (reservas)
     * MS-2218
    * */
    public function reportAccountCase108($cpath, $date = null){

        //Codigo unico para cada bloque de asientos contables
        $nextEntryCode = AccountingEntry::max('entry_code');
        $nextEntryCode = sprintf('%04d',($nextEntryCode ?? 0) + 1);

        //Armamos la data con todos las polizas que tengan asientos 107 generados
        $dataPolicy = PolicySort::join(
            'accounting_entries',
            'policy_sorts.consecutive',
            '=',
            DB::raw('CAST(REPLACE(accounting_entries.number_policy, "SORT-", "") AS UNSIGNED)')
        )
        ->where('accounting_entries.cod_oper', 107)
        ->whereNotNull('accounting_entries.credit')
        ->where('accounting_entries.credit', '<>', 0)
        ->get();

        //fecha por parametro o fecha actual
        $date =  $date ?? Carbon::now()->toDateString();


        $trm_daily = 0;

        //Consultamos la TRM DEL DIA
        $webserviceController = new WebserviceAcselController();
        $trm = $webserviceController->getTrm();

        if ($trm == 0) {
            throw new \Exception('Error: El TRM no es valido');
        }else{
            $trm_daily = $trm;
        }


        //Recorremos las polizas con asientos 107 para generar la liberacion de provisiones
        foreach($dataPolicy as $reserve){

            //Variables
            $amountReserveDollars = 0;
            $amountReserveColons = 0;

                //Si la fecha de finalización de la póliza es menor a la fecha por parametro o el dia actual
                if($reserve->validity_to >= $date){

                    //Si la póliza es en colones registramos la reserva en la tabla release_ppnd_colones_reserve
                    if ($reserve->type_currency === 'CRC'){
                        //$trm_daily = 0;

                        //Verificamos si se genero un asiento contable 120 para esta fecha
                        $accountEntry120 = AccountingEntry::where('cod_oper', 120)
                            ->where('date_register', $date)
                            ->where('cod_moneda', 'CO')
                            ->whereRaw('CAST(REPLACE(accounting_entries.number_policy, "SORT-", "") AS UNSIGNED) = ?', [$reserve->consecutive])
                            ->orderBy('id', 'desc')
                            ->first();

                        //calculamos la reserva diaria
                        $reserveDaily = $reserve->credit/365;

                        ReleasePpndColonesReserve::create([
                            'number_policy'             => $reserve->consecutive,
                            'amount_accounty_entry_107' => $reserve->credit,
                            'amount_release_daily'      => $reserveDaily,
                            'date_reserve'              => $date,
                            'trm'                       => 0
                        ]);

                        $amountReserveColons = $amountReserveColons + $reserveDaily;
                    }

                    if($reserve->type_currency === 'USD'){

                        //$trm_daily = $trm_daily;

                        //Verificamos si se genero un asiento contable 120 para esta fecha
                        $accountEntry120 = AccountingEntry::where('cod_oper', 120)
                            ->where('date_register', $date)
                            ->where('cod_moneda', 'US')
                            ->whereRaw('CAST(REPLACE(accounting_entries.number_policy, "SORT-", "") AS UNSIGNED) = ?', [$reserve->consecutive])
                            ->orderBy('id', 'desc')
                            ->first();

                        //si existe un asiento contable 120 generado realizamo el calculo del nuevo valor de la liberacion
                        // if($accountEntry120){
                        //     $reserve->consecutive + ,
                        // }
                        // dd($accountEntry120);

                        //calculamos la reserva diaria
                        $reserveDaily = $reserve->credit/365;

                        $valida = ReleasePpndDollarsReserve::where('number_policy', $reserve->consecutive)->first();
                        $movementType = null;
                        $movementValue = null;
                        if(!$valida) {
                            $movementType = '107';
                            $movementValue = $reserve->credit;
                        }

                        ReleasePpndDollarsReserve::create([
                            'number_policy'             => $reserve->consecutive,
                            'amount_accounty_entry_107' => $reserve->credit,
                            'amount_release_daily'      => $reserveDaily,
                            'date_reserve'              => $date,
                            'trm'                       => $trm_daily,
                            'movement_type'             => $movementType,
                            'movement_value'            => $movementValue
                        ]);

                        $amountReserveDollars = $amountReserveDollars + $reserveDaily;
                    }
                }
            }

            $data = [];
            $values = [];
            $t_debit = 0;
            $t_credit = 0;

            if($amountReserveColons > 0){
                $values = array_merge($values, [
                    ['debit' => $amountReserveColons, 'credit' => 0, 'movement_type' => 'D', 'movement_type_org' => 'D', 'cta_1' => '002', 'cta_2' => '050', 'cta_3' => '010', 'cta_4' => '010', 'cta_5' => '001', 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'PRIMAS', 'cod_cpto' => 'RECIBO', 'cor_relativo' => '003', 'ctaux' => '**************', 'money' => 'CO', 'trm' => 0],
                    ['debit' => 0, 'credit' => $amountReserveColons, 'movement_type' => 'C', 'movement_type_org' => 'C', 'cta_1' => '005', 'cta_2' => '070', 'cta_3' => '010', 'cta_4' => '010', 'cta_5' => '001', 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'PRIMAS', 'cod_cpto' => 'RECIBO', 'cor_relativo' => '001', 'ctaux' => '**************', 'money' => 'CO', 'trm' => 0],
                ]);
            }

            if($amountReserveDollars > 0){
                $values = array_merge($values, [
                    ['debit' => $amountReserveDollars, 'credit' => 0, 'movement_type' => 'D', 'movement_type_org' => 'D', 'cta_1' => '002', 'cta_2' => '050', 'cta_3' => '010', 'cta_4' => '010', 'cta_5' => '002', 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'PRIMAS', 'cod_cpto' => 'RECIBO', 'cor_relativo' => '003', 'ctaux' => '**************', 'money' => 'DO', 'trm' => $trm_daily],
                    ['debit' => 0, 'credit' => $amountReserveDollars, 'movement_type' => 'C', 'movement_type_org' => 'C', 'cta_1' => '005', 'cta_2' => '070', 'cta_3' => '010', 'cta_4' => '010', 'cta_5' => '002', 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'PRIMAS', 'cod_cpto' => 'RECIBO', 'cor_relativo' => '001', 'ctaux' => '**************', 'money' => 'DO', 'trm' => $trm_daily],
                ]);
            }

            if (!empty($values)) {
                foreach ($values as $value) {
                    $idecta = "01{$value['cta_1']}{$value['cta_2']}{$value['cta_3']}{$value['cta_4']}{$value['cta_5']}{$value['cta_6']}{$value['cta_7']}{$value['cta_8']}000000{$value['ctaux']}";

                    $data[] = [
                        'cod_cia' => "01",
                        'cod_oper' => '108',
                        'receipt_number' => "RT-$nextEntryCode",
                        'receipt_status' => 'ACT',
                        'number_policy' => $reserve->formatNumberConsecutive(),
                        'number_payment' => "NA",
                        'receipt_type' => '108',
                        'date_register' => $date,
                        'descoper' => 'LIBERACION DE PROVISION PARA PRIMA NO DEVENGADA',
                        'cod_grupo_cpto' => $value['cod_grupo_cpto'],
                        'cod_cpto' => $value['cod_cpto'],
                        'cor_relativo' => $value['cor_relativo'],
                        'cod_ramo' => 'SORT',
                        'detail_movement' => 'LIBERACION DE PROVISION PARA PRIMA NO DEVENGADA-' . $reserve->formatNumberConsecutive() . '-' . "NA" . '-' . "RT-$nextEntryCode" . '-NA-' . $value['cod_cpto'],
                        'debit' => $value['debit'],
                        'credit' => $value['credit'],
                        't_debit' => $t_debit,
                        't_credit' => $t_credit,
                        'difference' => 0,
                        'movement_type' => $value['movement_type'],
                        'movement_type_org' => $value['movement_type_org'],
                        'idecta' => $idecta,
                        'cta_1' => $value['cta_1'],
                        'cta_2' => $value['cta_2'],
                        'cta_3' => $value['cta_3'],
                        'cta_4' => $value['cta_4'],
                        'cta_5' => $value['cta_5'],
                        'cta_6' => $value['cta_6'],
                        'cta_7' => $value['cta_7'],
                        'cta_8' => $value['cta_8'],
                        'cta_9' => '000',
                        'cta_10' => '000',
                        'document_type' => "NA",
                        'document_number' => "NA",
                        'name_taker' => "NA",
                        'cod_intermediary' => "NA",
                        'cod_moneda' => $value['money'],
                        'exchange_rate' => $value['trm'],
                        'entry_code' => $nextEntryCode,
                        'ctaux' => $value['ctaux'],
                    ];
                }
            // Inserta todos los registros en la base de datos tabla de asientos contables
            AccountingEntry::insert($data);
        }
    }

    /**
     * Reporte contable 150
     * MS-2224
     * */
    public function autoAccountCase150() {

        return response()->json(
            [   'success' => true,
                'message' => 'Actualmente no se generan resultados para este proceso.'
            ], 200);

        try {

            $reinsurancePercentage = AppServiceProvider::$REINSURANCE_PERCENTAGE;

            //AND YEAR(a.created_at) = YEAR(CURRENT_DATE) AND MONTH(a.created_at) = MONTH(CURRENT_DATE)

            $results = DB::table('accounting_entries')
                ->select('cod_moneda',
                            DB::raw('SUM(credit*'.$reinsurancePercentage.') AS valor'),
                    DB::raw("GROUP_CONCAT(number_policy SEPARATOR ', ') AS policies")
                        )
                ->where('cod_oper', '107')
                ->whereRaw('date(created_at) = CURRENT_DATE')
                ->groupBy('cod_moneda')
                ->get();

            $webserviceController = new WebserviceAcselController();
            $trm = $webserviceController->getTrm();

            if ($trm ==0) {
                throw new \Exception('Error: El TRM no es valido');
            }

            foreach ($results as $result) {

                $nextEntryCode = AccountingEntry::max('entry_code');
                $nextEntryCode = sprintf('%04d',($nextEntryCode ?? 0) + 1);

                $this->reportAccountCase150($result, $trm, $nextEntryCode) ;
            }

            return response()->json(['success' => true], 200);

        } catch (\Exception $e) {

            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
            ],500);
        }
    }

    public function reportAccountCase150($result, $trm, $nextEntryCode){

        $cta5 = "001";
        if ($result->cod_moneda == 'US') {
            $cta5 = "002";
        }

        $paymentId = 'NA';

        $t_debit  =  $result->valor ;
        $t_credit =  $result->valor ;

        $values = [
            ['debit' => $result->valor, 'credit' => 0,      'movement_type'=> 'D', 'movement_type_org'=> 'D', 'cta_1' => '001', 'cta_2' => '060', 'cta_3' => '010', 'cta_4' => '010', 'cta_5' => $cta5, 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'PRIMAS', 'cod_cpto' => 'RECIBO', 'cor_relativo'  => '007', 'ctaux' => '**************'],
            ['debit' => 0,      'credit' => $result->valor, 'movement_type'=> 'C', 'movement_type_org'=> 'C', 'cta_1' => '005', 'cta_2' => '070', 'cta_3' => '010', 'cta_4' => '070', 'cta_5' => $cta5, 'cta_6' => '020', 'cta_7' => '010', 'cta_8' => '180', 'cod_grupo_cpto' => 'PRIMAS', 'cod_cpto' => 'RECIBO', 'cor_relativo'  => '005', 'ctaux' => '**************'],
        ];

        $cod_cia = '01';
        $data = [];

        $result->policies = rtrim($result->policies, ',');

        foreach ($values as $value) {
            $idecta = "01{$cod_cia}{$value['cta_1']}{$value['cta_2']}{$value['cta_3']}{$value['cta_4']}{$value['cta_5']}{$value['cta_6']}{$value['cta_7']}{$value['cta_8']}000000{$value['ctaux']}";

            $data[] = [
                'cod_cia' =>  $cod_cia,
                'cod_oper' => '150',
                'receipt_number' => "RT-$nextEntryCode",
                'receipt_status' => 'ACT',
                'number_policy' => $result->policies,
                'number_payment' => $paymentId,
                'receipt_type' => '150',
                'date_register' => Carbon::now(),
                'descoper' => 'CONSTITUCION PROVISION PRIMA NO DEVENGADA REASEG',
                'cod_grupo_cpto' => $value['cod_grupo_cpto'],
                'cod_cpto' => $value['cod_cpto'],
                'cor_relativo' => $value['cor_relativo'],
                'cod_ramo' => 'SORT',
                'detail_movement' => 'CONSTITUCION PROVISION PRIMA NO DEVENGADA REASEG-'.$result->policies.'-'.$paymentId.'-'."RT-$nextEntryCode".'-NA-'.$value['cod_cpto'],
                'debit' => $value['debit'],
                'credit' => $value['credit'],
                't_debit' => $t_debit,
                't_credit' => $t_credit,
                'difference' => 0,
                'movement_type' => $value['movement_type'],
                'movement_type_org' => $value['movement_type_org'],
                'idecta' => $idecta,
                'cta_1' => $value['cta_1'],
                'cta_2' => $value['cta_2'],
                'cta_3' => $value['cta_3'],
                'cta_4' => $value['cta_4'],
                'cta_5' => $value['cta_5'],
                'cta_6' => $value['cta_6'],
                'cta_7' => $value['cta_7'],
                'cta_8' => $value['cta_8'],
                'cta_9' => '000',
                'cta_10' => '000',
                'document_type' => 'NA',
                'document_number' => 'NA',
                'name_taker' => 'NA',
                'cod_intermediary' => 'NA' ,
                'cod_moneda' => $result->cod_moneda,
                'exchange_rate' => $result->cod_moneda == 'US' ? $trm : 0,
                'entry_code' => $nextEntryCode,
                'ctaux'  =>$value['ctaux']
            ];
        }

        AccountingEntry::insert($data);
    }

    /**
     * Reporte contable 301
     * MS-2226
     * */
    public function autoAccountCase301() {

        return response()->json(
            [   'success' => true,
                'message' => 'Actualmente no se generan resultados para este proceso.'
            ], 200);

        try {

            $reinsurancePercentage = AppServiceProvider::$REINSURANCE_PERCENTAGE;
            //AND YEAR(a.created_at) = YEAR(CURRENT_DATE) AND MONTH(a.created_at) = MONTH(CURRENT_DATE)

            $results = DB::table('accounting_entries')
                ->select('cod_moneda',
                            DB::raw('SUM(debit*'.$reinsurancePercentage.') AS valor'),
                            DB::raw("GROUP_CONCAT(number_policy SEPARATOR ', ') AS policies")
                           )
                ->where('cod_oper', '001')
                ->where('cor_relativo', '064')
                ->whereRaw('date(created_at) = CURRENT_DATE')
                ->groupBy('cod_moneda')
                ->get();

            $webserviceController = new WebserviceAcselController();
            $trm = $webserviceController->getTrm();

            if ($trm ==0) {
                throw new \Exception('Error: El TRM no es valido');
            }

            foreach ($results as $result) {

                $nextEntryCode = AccountingEntry::max('entry_code');
                $nextEntryCode = sprintf('%04d',($nextEntryCode ?? 0) + 1);
                $this->reportAccountCase301($result, $trm, $nextEntryCode) ;
            }

            return response()->json(['success' => true], 200);

        } catch (\Exception $e) {

            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
            ],500);
        }
    }

    public function reportAccountCase301($result, $trm, $nextEntryCode){

        $cta5 = "001";
        if ($result->cod_moneda == 'US') {
            $cta5 = "002";
        }

        $paymentId = 'NA';

        $t_debit  =  $result->valor ;
        $t_credit =  $result->valor ;

        $values = [
            ['debit' => 0,      'credit' => $result->valor, 'movement_type'=> 'C', 'movement_type_org'=> 'C', 'cta_1' => '002', 'cta_2' => '060', 'cta_3' => '010', 'cta_4' => '010', 'cta_5' => $cta5, 'cta_6' => '000', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'PRIMAS', 'cod_cpto' => 'RECIBO', 'cor_relativo'  => '008', 'ctaux' => '**************'],
            ['debit' => $result->valor, 'credit' => 0,      'movement_type'=> 'D', 'movement_type_org'=> 'D', 'cta_1' => '004', 'cta_2' => '040', 'cta_3' => '010', 'cta_4' => '070', 'cta_5' => $cta5, 'cta_6' => '010', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'PRIMAS', 'cod_cpto' => 'RECIBO', 'cor_relativo'  => '006', 'ctaux' => '**************'],
        ];

        $cod_cia = '01';
        $data = [];
        $policies = rtrim($result->policies, ',');

        foreach ($values as $value) {
            $idecta = "01{$cod_cia}{$value['cta_1']}{$value['cta_2']}{$value['cta_3']}{$value['cta_4']}{$value['cta_5']}{$value['cta_6']}{$value['cta_7']}{$value['cta_8']}000000{$value['ctaux']}";

            $data[] = [
                'cod_cia' =>  $cod_cia,
                'cod_oper' => '301',
                'receipt_number' => "RT-$nextEntryCode",
                'receipt_status' => 'ACT',
                'number_policy' => $policies,
                'number_payment' => $paymentId,
                'receipt_type' => '301',
                'date_register' => Carbon::now(),
                'descoper' => 'PRIMA CEDIDAS AL REASEGURO',
                'cod_grupo_cpto' => $value['cod_grupo_cpto'],
                'cod_cpto' => $value['cod_cpto'],
                'cor_relativo' => $value['cor_relativo'],
                'cod_ramo' => 'SORT',
                'detail_movement' => 'PRIMA CEDIDAS AL REASEGURO-'.$policies.'-'.$paymentId.'-'."RT-$nextEntryCode".'-NA-'.$value['cod_cpto'],
                'debit' => $value['debit'],
                'credit' => $value['credit'],
                't_debit' => $t_debit,
                't_credit' => $t_credit,
                'difference' => 0,
                'movement_type' => $value['movement_type'],
                'movement_type_org' => $value['movement_type_org'],
                'idecta' => $idecta,
                'cta_1' => $value['cta_1'],
                'cta_2' => $value['cta_2'],
                'cta_3' => $value['cta_3'],
                'cta_4' => $value['cta_4'],
                'cta_5' => $value['cta_5'],
                'cta_6' => $value['cta_6'],
                'cta_7' => $value['cta_7'],
                'cta_8' => $value['cta_8'],
                'cta_9' => '000',
                'cta_10' => '000',
                'document_type' => 'NA',
                'document_number' => 'NA',
                'name_taker' => 'NA',
                'cod_intermediary' => 'NA' ,
                'cod_moneda' => $result->cod_moneda,
                'exchange_rate' => $trm,
                'entry_code' => $nextEntryCode,
                'ctaux'  =>$value['ctaux']
            ];
        }

        AccountingEntry::insert($data);
    }


      /**
     * Reporte contable 153 casi disminucion reserva en colones contra asiento 120
     * MS-MS-2225
     * */

     public function reportAccountCaseOneTwo153($cpath, $policyId, $value){

        try{

            $reinsurancePercentage = AppServiceProvider::$REINSURANCE_PERCENTAGE;

            $policy = PolicySort::find($policyId);

            $nextEntryCode = AccountingEntry::max('entry_code');
            $nextEntryCode = sprintf('%04d',($nextEntryCode ?? 0) + 1);

            $activityPayment = Activity::where('parent_id', $policy->activity->id)
                ->where("service_id", Service::SERVICE_POLICY_SORT_COLLECTION_MNK)
                ->whereHas('policy_sort_collection', function ($query) {
                    $query->where('payment_status', PolicySortCollection::PAYMENT_STATUS_APPROVED);
                })
                ->latest()
                ->first();

            $trm = 0;
            $amount_policy =  $value;
            $cta5 = '001';

            if ($policy->type_currency == 'USD'){
                $webserviceController = new WebserviceAcselController();
                $trm = $webserviceController->getTrm();

                if ($trm ==0) {
                    throw new \Exception('Error: El TRM no es valido');
                }

                $amount_policy =  $value*$trm;
                $cta5 = '002';
            }

            $paymentId = $activityPayment->policy_sort_collection->id;

            //descontamos el porcentage de reaseguramiento 50%
            $amount_policy = $amount_policy - ($amount_policy *  $reinsurancePercentage);

            $t_debit  =  $amount_policy ;
            $t_credit =  $amount_policy ;

            $values = [
                ['debit' => 0,              'credit' => $amount_policy, 'movement_type'=> 'C', 'movement_type_org'=> 'C', 'cta_1' => '001', 'cta_2' => '060', 'cta_3' => '010', 'cta_4' => '010', 'cta_5' => $cta5, 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'PRIMAS', 'cod_cpto' => 'RECIBO', 'cor_relativo' => '008'],
                ['debit' => $amount_policy, 'credit' => 0,              'movement_type'=> 'D', 'movement_type_org'=> 'D', 'cta_1' => '004', 'cta_2' => '080', 'cta_3' => '010', 'cta_4' => '070', 'cta_5' => $cta5, 'cta_6' => '020', 'cta_7' => '010', 'cta_8' => '180', 'cod_grupo_cpto' => 'PRIMAS', 'cod_cpto' => 'RECIBO', 'cor_relativo' => '006'],
            ];

            $username = preg_replace('/^CO-/', '', $policy->code); // Remover el prefijo "CO-"


            $user = User::where(function ($query) use ($username) {
                $query->where('username', $username) // Buscar directamente sin "CO-"
                ->orWhere('username', 'CO-' . $username) // Buscar con "CO-"
                ->orWhere('code_mnk',$username)
                    ->orWhere('code_mnk','CO-' . $username);
            })->first();

            $data = [];

            foreach ($values as $value) {

                $idecta = "{$value['cta_1']}{$value['cta_2']}{$value['cta_3']}{$value['cta_4']}{$value['cta_5']}{$value['cta_6']}{$value['cta_7']}{$value['cta_8']}000000"."**************";

                $data[] = [
                    'cod_cia' => '01',
                    'cod_oper' => '153',
                    'receipt_number' => "RT-$nextEntryCode",
                    'receipt_status' => 'ACT',
                    'number_policy' => $policy->formatNumberConsecutive(),
                    'number_payment' => $paymentId,
                    'receipt_type' => '153',
                    'date_register' => $policy->created_at,
                    'descoper' => 'REVERSO LIBERA PROVISION PRIMA NO DEVENGADA REASEG',
                    'cod_grupo_cpto' => $value['cod_grupo_cpto'],
                    'cod_cpto' => $value['cod_cpto'],
                    'cor_relativo' => $value['cor_relativo'],
                    'cod_ramo' => 'SORT',
                    'detail_movement' => 'REVERSO LIBERA PROVISION PRIMA NO DEVENGADA REASEG-'.$policy->formatNumberConsecutive().'-'.$paymentId.'-'."RT-$nextEntryCode".'-'.$policy->activity->affiliate->first_name.$value['cod_cpto'],
                    'debit' => $value['debit'],
                    'credit' => $value['credit'],
                    't_debit' => $t_debit,
                    't_credit' => $t_credit,
                    'difference' => ($t_debit-$t_credit),
                    'movement_type' => $value['movement_type'],
                    'movement_type_org' => $value['movement_type_org'],
                    'idecta' => $idecta,
                    'cta_1' => $value['cta_1'],
                    'cta_2' => $value['cta_2'],
                    'cta_3' => $value['cta_3'],
                    'cta_4' => $value['cta_4'],
                    'cta_5' => $value['cta_5'],
                    'cta_6' => $value['cta_6'],
                    'cta_7' => $value['cta_7'],
                    'cta_8' => $value['cta_8'],
                    'cta_9' => '000',
                    'cta_10' => '000',
                    'document_type' => $policy->activity->affiliate->doc_type,
                    'document_number' => $policy->activity->affiliate->doc_number,
                    'name_taker' => $policy->activity->affiliate->full_name,
                    'cod_intermediary' =>  $user->code_correduria ? $user->code_correduria : $policy->code,
                    'cod_moneda' => $policy->type_currency == 'USD' ? 'US' : 'CO',
                    'exchange_rate' => $trm,
                    'entry_code' => $nextEntryCode,
                    'ctaux'  => "**************"
                ];
            }

            AccountingEntry::insert($data);

            return response()->json([
                'success' => true,
                'value' => $amount_policy,
            ]);

        }catch (\Exception $e){
            throw new \Exception('Error asiento reportAccountCase153 : '.$e->getMessage());
        }
    }


      /**
     * Reporte contable 151 ASIENTO CONTABLE 151 LIBERACION REASEGURO
     * MS-2218
    * */
    public function reportAccountCase151($cpath, $date = null){

        $reinsurancePercentage = AppServiceProvider::$REINSURANCE_PERCENTAGE;

        //Codigo unico para cada bloque de asientos contables
        $nextEntryCode = AccountingEntry::max('entry_code');
        $nextEntryCode = sprintf('%04d',($nextEntryCode ?? 0) + 1);

        //Armamos la data con todos las polizas que tengan asientos 107 generados
        $dataPolicy = PolicySort::join(
            'accounting_entries',
            'policy_sorts.consecutive',
            '=',
            DB::raw('CAST(REPLACE(accounting_entries.number_policy, "SORT-", "") AS UNSIGNED)')
        )
        ->where('accounting_entries.cod_oper', 107)
        ->whereNotNull('accounting_entries.credit')
        ->where('accounting_entries.credit', '<>', 0)
        ->get();

        //fecha por parametro o fecha actual
        $date =  $date ?? Carbon::now()->toDateString();

        //Variables
        $amountReserveDollars = 0;
        $amountReserveColons = 0;
        $trm_daily = 0;

        //Consultamos la TRM DEL DIA
        $webserviceController = new WebserviceAcselController();
        $trm = $webserviceController->getTrm();

        if ($trm == 0) {
            throw new \Exception('Error: El TRM no es valido');
        }else{
            $trm_daily = $trm;
        }


        //Recorremos las polizas con asientos 107 para generar la liberacion de provisiones
        foreach($dataPolicy as $reserve){

                //Si la fecha de finalización de la póliza es menor a la fecha por parametro o el dia actual
                if($reserve->validity_to >= $date){

                    //Si la póliza es en colones registramos la reserva en la tabla release_ppnd_colones_reserve
                    if ($reserve->type_currency === 'CRC'){
                        //$trm_daily = 0;

                        //Verificamos si se genero un asiento contable 120 para esta fecha
                        $accountEntry120 = AccountingEntry::where('cod_oper', 120)
                            ->where('date_register', $date)
                            ->where('cod_moneda', 'CO')
                            ->whereRaw('CAST(REPLACE(accounting_entries.number_policy, "SORT-", "") AS UNSIGNED) = ?', [$reserve->consecutive])
                            ->orderBy('id', 'desc')
                            ->first();

                        //calculamos la reserva diaria
                        //Multiplicamos por el valor de reaseguramiento
                        $reserveDaily = ($reserve->credit - ($reserve->credit* $reinsurancePercentage)) /365;

                        ReleaseReinsuranceColonesReserve::create([
                            'number_policy'             => $reserve->consecutive,
                            //Multiplicamos por el valor de reaseguramiento
                            'amount_accounty_entry_107' => $reserve->credit - ($reserve->credit* $reinsurancePercentage),
                            'amount_release_daily'      => $reserveDaily,
                            'date_reserve'              => $date,
                            'trm'                       => 0
                        ]);

                        $amountReserveColons = $amountReserveColons + $reserveDaily;
                    }

                    if($reserve->type_currency === 'USD'){

                        //$trm_daily = $trm_daily;

                        //Verificamos si se genero un asiento contable 120 para esta fecha
                        $accountEntry120 = AccountingEntry::where('cod_oper', 120)
                            ->where('date_register', $date)
                            ->where('cod_moneda', 'US')
                            ->whereRaw('CAST(REPLACE(accounting_entries.number_policy, "SORT-", "") AS UNSIGNED) = ?', [$reserve->consecutive])
                            ->orderBy('id', 'desc')
                            ->first();

                        //si existe un asiento contable 120 generado realizamo el calculo del nuevo valor de la liberacion
                        // if($accountEntry120){
                        //     $reserve->consecutive + ,
                        // }
                        // dd($accountEntry120);

                        //calculamos la reserva diaria
                        //Multiplicamos por el valor de reaseguramiento
                        $reserveDaily = ($reserve->credit - ($reserve->credit* $reinsurancePercentage)) /365;

                        //Validar si es el primer registro 107 de emisión, si es asi colocar el tipo de movimiento
                        $valida = ReleaseReinsuranceDollarsReserve::where('number_policy', $reserve->consecutive)->first();
                        $movementType = null;
                        $movementValue = null;
                        if(!$valida) {
                            $movementType = '107';
                            $movementValue = $reserve->credit;
                        }

                        ReleaseReinsuranceDollarsReserve::create([
                            'number_policy'             => $reserve->consecutive,
                            'amount_accounty_entry_107' => $reserve->credit - ($reserve->credit* $reinsurancePercentage),
                            'amount_release_daily'      => $reserveDaily,
                            'date_reserve'              => $date,
                            'trm'                       => $trm_daily,
                            'movement_type'             => $movementType,
                            'movement_value'            => $movementValue
                        ]);

                        $amountReserveDollars = $amountReserveDollars + $reserveDaily;
                    }
                }
            }

            $data = [];
            $values = [];
            $t_debit = 0;
            $t_credit = 0;

            if($amountReserveColons > 0){
                $values = array_merge($values, [
                    ['debit' => 0, 'credit' => $amountReserveColons, 'movement_type' => 'C', 'movement_type_org' => 'C', 'cta_1' => '001', 'cta_2' => '060', 'cta_3' => '010', 'cta_4' => '010', 'cta_5' => '001', 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'PRIMAS', 'cod_cpto' => 'RECIBO', 'cor_relativo' => '007', 'ctaux' => '**************', 'money' => 'CO', 'trm' => 0],
                    ['debit' => $amountReserveColons, 'credit' => 0, 'movement_type' => 'D', 'movement_type_org' => 'D', 'cta_1' => '004', 'cta_2' => '080', 'cta_3' => '010', 'cta_4' => '070', 'cta_5' => '001', 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'PRIMAS', 'cod_cpto' => 'RECIBO', 'cor_relativo' => '005', 'ctaux' => '**************', 'money' => 'CO', 'trm' => 0],
                ]);
            }

            if($amountReserveDollars > 0){
                $values = array_merge($values, [
                    ['debit' => 0, 'credit' => $amountReserveColons, 'movement_type' => 'C', 'movement_type_org' => 'C', 'cta_1' => '001', 'cta_2' => '060', 'cta_3' => '010', 'cta_4' => '010', 'cta_5' => '002', 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'PRIMAS', 'cod_cpto' => 'RECIBO', 'cor_relativo' => '008', 'ctaux' => '**************', 'money' => 'DO', 'trm' => $trm_daily],
                    ['debit' => $amountReserveColons, 'credit' => 0, 'movement_type' => 'D', 'movement_type_org' => 'D', 'cta_1' => '004', 'cta_2' => '080', 'cta_3' => '010', 'cta_4' => '070', 'cta_5' => '002', 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'PRIMAS', 'cod_cpto' => 'RECIBO', 'cor_relativo' => '006', 'ctaux' => '**************', 'money' => 'DO', 'trm' => $trm_daily],
                ]);
            }

            if (!empty($values)) {
                foreach ($values as $value) {
                    $idecta = "01{$value['cta_1']}{$value['cta_2']}{$value['cta_3']}{$value['cta_4']}{$value['cta_5']}{$value['cta_6']}{$value['cta_7']}{$value['cta_8']}000000{$value['ctaux']}";

                    $data[] = [
                        'cod_cia' => "01",
                        'cod_oper' => '151',
                        'receipt_number' => "RT-$nextEntryCode",
                        'receipt_status' => 'ACT',
                        'number_policy' => $reserve->formatNumberConsecutive(),
                        'number_payment' => "NA",
                        'receipt_type' => '151',
                        'date_register' => $date,
                        'descoper' => 'LIBERACION PROVISION PRIMA NO DEVENGADA REASEG',
                        'cod_grupo_cpto' => $value['cod_grupo_cpto'],
                        'cod_cpto' => $value['cod_cpto'],
                        'cor_relativo' => $value['cor_relativo'],
                        'cod_ramo' => 'SORT',
                        'detail_movement' => 'LIBERACION PROVISION PRIMA NO DEVENGADA REASEG-' . $reserve->formatNumberConsecutive() . '-' . "NA" . '-' . "RT-$nextEntryCode" . '-NA-' . $value['cod_cpto'],
                        'debit' => $value['debit'],
                        'credit' => $value['credit'],
                        't_debit' => $t_debit,
                        't_credit' => $t_credit,
                        'difference' => 0,
                        'movement_type' => $value['movement_type'],
                        'movement_type_org' => $value['movement_type_org'],
                        'idecta' => $idecta,
                        'cta_1' => $value['cta_1'],
                        'cta_2' => $value['cta_2'],
                        'cta_3' => $value['cta_3'],
                        'cta_4' => $value['cta_4'],
                        'cta_5' => $value['cta_5'],
                        'cta_6' => $value['cta_6'],
                        'cta_7' => $value['cta_7'],
                        'cta_8' => $value['cta_8'],
                        'cta_9' => '000',
                        'cta_10' => '000',
                        'document_type' => "NA",
                        'document_number' => "NA",
                        'name_taker' => "NA",
                        'cod_intermediary' => "NA",
                        'cod_moneda' => $value['money'],
                        'exchange_rate' => $value['trm'],
                        'entry_code' => $nextEntryCode,
                        'ctaux' => $value['ctaux'],
                    ];
                }
            // Inserta todos los registros en la base de datos tabla de asientos contables
            AccountingEntry::insert($data);
        }
    }

    /**
     * Reporte contable 306
     * MS-2228
     * */
    public function autoAccountCase306() {

        try {

            $numberPolicies = DB::table('release_reinsurance_dollars_reserves as r')
                ->select(
                    'number_policy',
                    DB::raw('SUM(amount_release_daily * r.trm) AS libera_reserva_crc'),
                    DB::raw("SUM(
                                case when r.movement_type = '153' then
                                    (
                                      (CASE WHEN r.movement_value IS NULL THEN 0 ELSE r.movement_value END * r.trm) 
                                      +
                                      (amount_release_daily * r.trm)
                                    )*-1
                                ELSE 
                                  (CASE WHEN r.movement_value IS NULL THEN 0 ELSE r.movement_value END * r.trm) 
                                  - 
                                  (amount_release_daily * r.trm)
                                END
                            ) AS saldo_acumulado"),
                    DB::raw('SUM(amount_release_daily) AS libera_reserva_usd')
                )
                ->whereRaw('MONTH(r.date_reserve) = MONTH(CURDATE())')
                ->whereRaw('YEAR(r.date_reserve) = YEAR(CURDATE())')
                ->where('trm', '<>', 0)
                ->whereNotNull('trm')
                ->groupBy('number_policy')
                ->get();

            $webserviceController = new WebserviceAcselController();
            $trm = $webserviceController->getTrm();

            if ($trm ==0) {
                throw new \Exception('Error: El TRM no es valido');
            }

            foreach ($numberPolicies as $result) {

                $nextEntryCode = AccountingEntry::max('entry_code');
                $nextEntryCode = sprintf('%04d',($nextEntryCode ?? 0) + 1);

                $policy = PolicySort::where('consecutive', $result->number_policy)->first();
                $this->reportAccountCase306($result, $policy, $trm, $nextEntryCode) ;
            }

            return response()->json(['success' => true], 200);

        } catch (\Exception $e) {

            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
            ],500);
        }
    }

    public function reportAccountCase306($result, $policy, $trm, $nextEntryCode){

        $latestRelease = DB::table('release_reinsurance_dollars_reserves as r')
            ->where('number_policy', $result->number_policy)
            ->where('trm', '<>', 0)
            ->whereNotNull('trm')
            ->orderByDesc('id')
            ->first();

        $variacion_recerva = $result->libera_reserva_usd;
        $reserva = $latestRelease->amount_accounty_entry_107;
        $saldo_acumulado = $result->saldo_acumulado;
        $saldo_reserva = $reserva-$variacion_recerva;
        $saldo_cambio_cierre = $saldo_reserva*$trm;
        $difCambio = $saldo_cambio_cierre-$saldo_acumulado;

//        dd([
//            'variacion_recerva' => $variacion_recerva,
//            'reserva' => $reserva,
//            'saldo_acumulado' => $saldo_acumulado,
//            'saldo_reserva' => $saldo_reserva,
//            'saldo_cambio_cierre' => $saldo_cambio_cierre,
//            'difCambio' => $difCambio,
//        ]);

        $cta1c = $difCambio < 0 ? "005" :"004" ;
        $paymentId = 'NA';

        $t_debit  =  $difCambio ;
        $t_credit =  $difCambio ;

        $values = [
            ['debit' => $difCambio, 'credit' => 0,      'movement_type'=> 'D', 'movement_type_org'=> 'D', 'cta_1' => '001' , 'cta_2' => '060', 'cta_3' => '010', 'cta_4' => '010', 'cta_5' => '002', 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'PRIMAS', 'cod_cpto' => 'RECIBO', 'cor_relativo'  => '004', 'ctaux' => '**************'],
            ['debit' => 0,      'credit' => $difCambio, 'movement_type'=> 'C', 'movement_type_org'=> 'C', 'cta_1' => $cta1c, 'cta_2' => '010', 'cta_3' => '080', 'cta_4' => '040', 'cta_5' => '002', 'cta_6' => '000', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'PRIMAS', 'cod_cpto' => 'RECIBO', 'cor_relativo'  => '002', 'ctaux' => '**************'],
        ];

        $cod_cia = '01';
        $data = [];

        $username = preg_replace('/^CO-/', '', $policy->code); // Remover el prefijo "CO-"
        $user = User::where(function ($query) use ($username) {
            $query->where('username', $username) // Buscar directamente sin "CO-"
            ->orWhere('username', 'CO-' . $username); // Buscar con "CO-"
        })->first();

        foreach ($values as $value) {
            $idecta = "{$cod_cia}{$value['cta_1']}{$value['cta_2']}{$value['cta_3']}{$value['cta_4']}{$value['cta_5']}{$value['cta_6']}{$value['cta_7']}{$value['cta_8']}000000{$value['ctaux']}";

            $data[] = [
                'cod_cia' =>  $cod_cia,
                'cod_oper' => '306',
                'receipt_number' => "RT-$nextEntryCode",
                'receipt_status' => 'ACT',
                'number_policy' => $policy->formatNumberConsecutive(),
                'number_payment' => $paymentId,
                'receipt_type' => '306',
                'date_register' => Carbon::now(),
                'descoper' => 'DIFERENCIAL CAMBIARIO DE LA  RESERVA DE LA PRIMA REASEGURO',
                'cod_grupo_cpto' => $value['cod_grupo_cpto'],
                'cod_cpto' => $value['cod_cpto'],
                'cor_relativo' => $value['cor_relativo'],
                'cod_ramo' => 'SORT',
                'detail_movement' => 'DIFERENCIAL CAMBIARIO DE LA  RESERVA DE LA PRIMA REASEGURO-'.$policy->formatNumberConsecutive().'-'.$paymentId.'-'."RT-$nextEntryCode".'-'.'NA'.'-'.$value['cod_cpto'],
                'debit' => $value['debit'],
                'credit' => $value['credit'],
                't_debit' => $t_debit,
                't_credit' => $t_credit,
                'difference' => 0,
                'movement_type' => $value['movement_type'],
                'movement_type_org' => $value['movement_type_org'],
                'idecta' => $idecta,
                'cta_1' => $value['cta_1'],
                'cta_2' => $value['cta_2'],
                'cta_3' => $value['cta_3'],
                'cta_4' => $value['cta_4'],
                'cta_5' => $value['cta_5'],
                'cta_6' => $value['cta_6'],
                'cta_7' => $value['cta_7'],
                'cta_8' => $value['cta_8'],
                'cta_9' => '000',
                'cta_10' => '000',
                'document_type' => $policy->activity->affiliate->doc_type,
                'document_number' => $policy->activity->affiliate->doc_number,
                'name_taker' => $policy->activity->affiliate->full_name,
                'cod_intermediary' =>  $user && $user->code_correduria ? $user->code_correduria : $policy->code,
                'cod_moneda' => 'US',
                'exchange_rate' => $trm,
                'entry_code' => $nextEntryCode,
                'ctaux'  =>$value['ctaux']
            ];
        }

        AccountingEntry::insert($data);
    }

    /**
     * Reporte contable 154
     * MS-2076
     * Función con asiento contable para colones o dólares
     * */

     public function reportAccountCase154($activity_gis, $policy_id, $total)
     {

        $policy = PolicySort::where('activity_id', $policy_id)->first();

        $reinsurancePercentageSinister = AppServiceProvider::$PERCENTAGE_REINSURANCE_SINISTER;

        //Traemos el pago mediante la apoliza asociada
        $activityPayment = Activity::where('parent_id', $policy->activity->id)
            ->where("service_id", Service::SERVICE_POLICY_SORT_COLLECTION_MNK)
            ->latest()
            ->first();

        $paymentId = $activityPayment->policy_sort_collection->id;

        $nextEntryCode = AccountingEntry::max('entry_code');
        $nextEntryCode = sprintf('%04d',($nextEntryCode ?? 0) + 1);
        $trm = 0;

        $cod_relative_c='007';
        $cod_relative_d='005';
        $cta_5 = '001';
        $total = str_replace(".", "", $total);
        $total = str_replace(",", ".", $total);

        // el total lo multiplicamos por el porcentage del reaseguro del siniestro
        $total = (float) ($total * $reinsurancePercentageSinister);

        if ($policy->type_currency == 'USD'){
            $webserviceController = new WebserviceAcselController();
            $trm = $webserviceController->getTrm();

            if ($trm ==0) {
                throw new \Exception('Error: El TRM no es valido');
            }

            $total =  $total*$trm;
            $cta_5 = '002';

        }

        $t_debit = $total;
        $t_credit = $total;

        $values = [
            ['debit' => $total, 'credit' => 0,                 'movement_type'=> 'D', 'movement_type_org'=> 'D', 'cta_1' => '001', 'cta_2' => '060', 'cta_3' => '030', 'cta_4' => '010', 'cta_5' => $cta_5, 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'REASE', 'cod_cpto' => 'RESER', 'cor_relativo'  => $cod_relative_c, 'ctaux' => '**************'],
            ['debit' => 0,      'credit' => $total,            'movement_type'=> 'C', 'movement_type_org'=> 'C', 'cta_1' => '005', 'cta_2' => '070', 'cta_3' => '010', 'cta_4' => '070', 'cta_5' => $cta_5, 'cta_6' => '020', 'cta_7' => '030', 'cta_8' => '180', 'cod_grupo_cpto' => 'REASE', 'cod_cpto' => 'RESER', 'cor_relativo'  => $cod_relative_d, 'ctaux' => '**************'],
        ];

        $cod_cia = '01';
        // Array para almacenar los registros completos
        $data = [];

        $username = preg_replace('/^CO-/', '', $policy->code);

        $user = User::where(function ($query) use ($username) {
            $query->where('username', $username) // Buscar directamente sin "CO-"
            ->orWhere('username', 'CO-' . $username); // Buscar con "CO-"
        })->first();

         $casoGis = $this->getCasoGis($activity_gis);

        // Recorre cada par de valores de debit y credit
        foreach ($values as $value) {
            $idecta = "{$cod_cia}{$value['cta_1']}{$value['cta_2']}{$value['cta_3']}{$value['cta_4']}{$value['cta_5']}{$value['cta_6']}{$value['cta_7']}{$value['cta_8']}000000{$value['ctaux']}";

            $data[] = [
                'cod_cia' =>  $cod_cia,
                'cod_oper' => '154',
                'receipt_number' => "RT-$nextEntryCode",
                'receipt_status' => 'ACT',
                'number_policy' => $policy->formatNumberConsecutive(),
                'number_payment' => $paymentId,
                'receipt_type' => '132',
                'date_register' => $policy->created_at,
                'descoper' => 'CONSTITUCION RESERVA DE SINIESTRO REASEG',
                'cod_grupo_cpto' => $value['cod_grupo_cpto'],
                'cod_cpto' => $value['cod_cpto'],
                'cor_relativo' => $value['cor_relativo'],
                'cod_ramo' => 'SORT',
                'detail_movement' => 'CONSTITUCION RESERVA DE SINIESTRO REASEG'.$policy->formatNumberConsecutive().'-'.'15'.'-'."RT-$nextEntryCode".'-'.$policy->activity->affiliate->doc_number.'-'.$value['cod_cpto'].'-'.$casoGis,
                'debit' => $value['debit'],
                'credit' => $value['credit'],
                't_debit' => $t_debit,
                't_credit' => $t_credit,
                'difference' => 0,
                'movement_type' => $value['movement_type'],
                'movement_type_org' => $value['movement_type_org'],
                'idecta' => $idecta,
                'cta_1' => $value['cta_1'],
                'cta_2' => $value['cta_2'],
                'cta_3' => $value['cta_3'],
                'cta_4' => $value['cta_4'],
                'cta_5' => $value['cta_5'],
                'cta_6' => $value['cta_6'],
                'cta_7' => $value['cta_7'],
                'cta_8' => $value['cta_8'],
                'cta_9' => '000',
                'cta_10' => '000',
                'document_type' => $policy->activity->affiliate->doc_type,
                'document_number' => $policy->activity->affiliate->doc_number,
                'name_taker' => $policy->activity->affiliate->full_name,
                'cod_intermediary' =>  $user && is_object($user) && isset($user->code_correduria) ? $user->code_correduria : $policy->code,
                'cod_moneda' => $policy->type_currency == 'USD' ? 'US' : 'CO',
                'exchange_rate' => $trm,
                'entry_code' => $nextEntryCode,
                'ctaux'  =>$value['ctaux'],
                'activity_gis' => $activity_gis,
                'caso_gis' => $casoGis
            ];
        }

        // Inserta todos los registros en la base de datos tabla de asientos contables
        AccountingEntry::insert($data);
    }

    public function getCasoGis($activity) {
         $gisSor = GisSort::where('activity_id',$activity)->first();

         if ($gisSor) {
            return $gisSor->id;
         }

         return '';
    }


    /**
     * Reporte contable 155
     * MS-2077
     * */
    public function reportAccountCase155($activity_gis, $policy_id, $total)
    {

        //$policy = PolicySort::where('activity_id', $policy_id)->first();
        $policy = PolicySort::find($policy_id);

        $reinsurancePercentageSinister = AppServiceProvider::$PERCENTAGE_REINSURANCE_SINISTER;

        //Traemos el pago mediante la apoliza asociada
        $activityPayment = Activity::where('parent_id', $policy->activity->id)
            ->where("service_id", Service::SERVICE_POLICY_SORT_COLLECTION_MNK)
            ->whereHas('policy_sort_collection', function ($query) {
                $query->where('payment_status', PolicySortCollection::PAYMENT_STATUS_APPROVED);
            })
            ->latest()
            ->first();

        $paymentId = $activityPayment->policy_sort_collection->id;

        $nextEntryCode = AccountingEntry::max('entry_code');
        $nextEntryCode = sprintf('%04d',($nextEntryCode ?? 0) + 1);
        $trm = 0;

        $cod_relative_c='007';
        $cod_relative_d='005';
        $cta_5 = '001';
        $total = str_replace(".", "", $total);
        $total = str_replace(",", ".", $total);

        // el total lo multiplicamos por el porcentage del reaseguro del siniestro
        $total = (float) ($total * $reinsurancePercentageSinister);

        if ($policy->type_currency == 'USD'){
            $webserviceController = new WebserviceAcselController();
            $trm = $webserviceController->getTrm();

            if ($trm ==0) {
                throw new \Exception('Error: El TRM no es valido');
            }

            $total =  $total*$trm;
            $cta_5 = '002';
            $cod_relative_c='008';
            $cod_relative_d='006';
        }

        $casoGis = '';
        $trabajador = '';

        $gisSor = GisSort::where('activity_id',$activity_gis)->first();

        if($gisSor){
            $casoGis = $gisSor->id ?? '';
            $trabajador = $gisSor->activity->affiliate->full_name ?? '';
        }

        $t_debit = $total;
        $t_credit = $total;

        $values = [
            ['debit' => 0,      'credit' => $total,            'movement_type'=> 'C', 'movement_type_org'=> 'C', 'cta_1' => '001', 'cta_2' => '060', 'cta_3' => '030', 'cta_4' => '010', 'cta_5' => $cta_5, 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'REASE', 'cod_cpto' => 'RESER', 'cor_relativo'  => $cod_relative_d, 'ctaux' => '**************'],
            ['debit' => $total, 'credit' => 0,                 'movement_type'=> 'D', 'movement_type_org'=> 'D', 'cta_1' => '004', 'cta_2' => '080', 'cta_3' => '010', 'cta_4' => '070', 'cta_5' => $cta_5, 'cta_6' => '020', 'cta_7' => '030', 'cta_8' => '180', 'cod_grupo_cpto' => 'REASE', 'cod_cpto' => 'RESER', 'cor_relativo'  => $cod_relative_c, 'ctaux' => '**************'],

        ];

        $cod_cia = '01';
        // Array para almacenar los registros completos
        $data = [];

        $username = preg_replace('/^CO-/', '', $policy->code);

        $user = User::where(function ($query) use ($username) {
            $query->where('username', $username) // Buscar directamente sin "CO-"
            ->orWhere('username', 'CO-' . $username); // Buscar con "CO-"
        })->first();

        //$casoGis = $this->getCasoGis($activity_gis);
        // Recorre cada par de valores de debit y credit
        foreach ($values as $value) {
            $idecta = "{$cod_cia}{$value['cta_1']}{$value['cta_2']}{$value['cta_3']}{$value['cta_4']}{$value['cta_5']}{$value['cta_6']}{$value['cta_7']}{$value['cta_8']}000000{$value['ctaux']}";

            $data[] = [
                'cod_cia' =>  $cod_cia,
                'cod_oper' => '155',
                'receipt_number' => "RT-$nextEntryCode",
                'receipt_status' => 'ACT',
                'number_policy' => $policy->formatNumberConsecutive(),
                'number_payment' => $paymentId,
                'receipt_type' => '133',
                'date_register' => $policy->created_at,
                'descoper' => 'REVERSO LIBERA RESERVA SINIESTRO REASEG',
                'cod_grupo_cpto' => $value['cod_grupo_cpto'],
                'cod_cpto' => $value['cod_cpto'],
                'cor_relativo' => $value['cor_relativo'],
                'cod_ramo' => 'SORT',
                'detail_movement' => 'REVERSO LIBERA RESERVA SINIESTRO REASEG-'.$policy->formatNumberConsecutive().'-'.$paymentId.'-'."RT-$nextEntryCode".'-'.$policy->activity->affiliate->doc_number.'-'.$value['cod_cpto'].'-'.$casoGis.'-'.$trabajador,
                'debit' => $value['debit'],
                'credit' => $value['credit'],
                't_debit' => $t_debit,
                't_credit' => $t_credit,
                'difference' => 0,
                'movement_type' => $value['movement_type'],
                'movement_type_org' => $value['movement_type_org'],
                'idecta' => $idecta,
                'cta_1' => $value['cta_1'],
                'cta_2' => $value['cta_2'],
                'cta_3' => $value['cta_3'],
                'cta_4' => $value['cta_4'],
                'cta_5' => $value['cta_5'],
                'cta_6' => $value['cta_6'],
                'cta_7' => $value['cta_7'],
                'cta_8' => $value['cta_8'],
                'cta_9' => '000',
                'cta_10' => '000',
                'document_type' => $policy->activity->affiliate->doc_type,
                'document_number' => $policy->activity->affiliate->doc_number,
                'name_taker' => $policy->activity->affiliate->full_name,
                'cod_intermediary' =>  $user && is_object($user) && isset($user->code_correduria) ? $user->code_correduria : $policy->code,
                'cod_moneda' => $policy->type_currency == 'USD' ? 'US' : 'CO',
                'exchange_rate' => $trm,
                'entry_code' => $nextEntryCode,
                'ctaux'  =>$value['ctaux'],
                'activity_gis' => $activity_gis,
                'caso_gis' => $casoGis,
                'affected_worker' => $trabajador
            ];
        }

        // Inserta todos los registros en la base de datos tabla de asientos contables
        AccountingEntry::insert($data);
    }

    /**
     * Reporte contable 156
     * MS-2078
     * */

    public function reportAccountCase156($activity_gis, $policy_id, $total)
    {

        //$policy = PolicySort::where('activity_id', $policy_id)->first();
        $policy = PolicySort::find($policy_id);

        $reinsurancePercentageSinister = AppServiceProvider::$PERCENTAGE_REINSURANCE_SINISTER;

        //Traemos el pago mediante la apoliza asociada
        $activityPayment = Activity::where('parent_id', $policy->activity->id)
            ->where("service_id", Service::SERVICE_POLICY_SORT_COLLECTION_MNK)
            ->whereHas('policy_sort_collection', function ($query) {
                $query->where('payment_status', PolicySortCollection::PAYMENT_STATUS_APPROVED);
            })
            ->latest()
            ->first();

        $paymentId = $activityPayment->policy_sort_collection->id;

        $nextEntryCode = AccountingEntry::max('entry_code');
        $nextEntryCode = sprintf('%04d',($nextEntryCode ?? 0) + 1);
        $trm = 0;

        $cod_relative_c='007';
        $cod_relative_d='005';
        $cta_5 = '001';
        $total = str_replace(".", "", $total);
        $total = str_replace(",", ".", $total);

        $total = (float) ($total * $reinsurancePercentageSinister);

        if ($policy->type_currency == 'USD'){
            $webserviceController = new WebserviceAcselController();
            $trm = $webserviceController->getTrm();

            if ($trm ==0) {
                throw new \Exception('Error: El TRM no es valido');
            }

            $total =  $total*$trm;
            $cta_5 = '002';
            $cod_relative_c='008';
            $cod_relative_d='006';
        }

        $t_debit = $total;
        $t_credit = $total;

        $values = [
            ['debit' => 0,      'credit' => $total,            'movement_type'=> 'C', 'movement_type_org'=> 'C', 'cta_1' => '001', 'cta_2' => '060', 'cta_3' => '030', 'cta_4' => '010', 'cta_5' => $cta_5, 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'REASE', 'cod_cpto' => 'RESER', 'cor_relativo'  => $cod_relative_d, 'ctaux' => '**************'],
            ['debit' => $total, 'credit' => 0,                 'movement_type'=> 'D', 'movement_type_org'=> 'D', 'cta_1' => '004', 'cta_2' => '080', 'cta_3' => '010', 'cta_4' => '070', 'cta_5' => $cta_5, 'cta_6' => '020', 'cta_7' => '030', 'cta_8' => '180', 'cod_grupo_cpto' => 'REASE', 'cod_cpto' => 'RESER', 'cor_relativo'  => $cod_relative_c, 'ctaux' => '**************'],

        ];

        $cod_cia = '01';
        // Array para almacenar los registros completos
        $data = [];

        $username = preg_replace('/^CO-/', '', $policy->code);

        $user = User::where(function ($query) use ($username) {
            $query->where('username', $username) // Buscar directamente sin "CO-"
            ->orWhere('username', 'CO-' . $username); // Buscar con "CO-"
        })->first();

        $casoGis = '';
        $trabajador = '';

        $gisSor = GisSort::where('activity_id',$activity_gis)->first();

        if($gisSor){
            $casoGis = $gisSor->id ?? '';
            $trabajador = $gisSor->activity->affiliate->full_name ?? '';
        }

        //$casoGis = $this->getCasoGis($activity_gis);
        // Recorre cada par de valores de debit y credit
        foreach ($values as $value) {
            $idecta = "{$cod_cia}{$value['cta_1']}{$value['cta_2']}{$value['cta_3']}{$value['cta_4']}{$value['cta_5']}{$value['cta_6']}{$value['cta_7']}{$value['cta_8']}000000{$value['ctaux']}";

            $data[] = [
                'cod_cia' =>  $cod_cia,
                'cod_oper' => '156',
                'receipt_number' => "RT-$nextEntryCode",
                'receipt_status' => 'ACT',
                'number_policy' => $policy->formatNumberConsecutive(),
                'number_payment' => $paymentId,
                'receipt_type' => '134',
                'date_register' => $policy->created_at,
                'descoper' => 'LIBERACION RESERVA DE SINIESTRO REASEG',
                'cod_grupo_cpto' => $value['cod_grupo_cpto'],
                'cod_cpto' => $value['cod_cpto'],
                'cor_relativo' => $value['cor_relativo'],
                'cod_ramo' => 'SORT',
                'detail_movement' => 'LIBERACION RESERVA DE SINIESTRO REASEG-'.$policy->formatNumberConsecutive().'-'.$paymentId.'-'."RT-$nextEntryCode".'-'.$policy->activity->affiliate->doc_number.'-'.$value['cod_cpto'].'-'.$casoGis.'-'.$trabajador,
                'debit' => $value['debit'],
                'credit' => $value['credit'],
                't_debit' => $t_debit,
                't_credit' => $t_credit,
                'difference' => 0,
                'movement_type' => $value['movement_type'],
                'movement_type_org' => $value['movement_type_org'],
                'idecta' => $idecta,
                'cta_1' => $value['cta_1'],
                'cta_2' => $value['cta_2'],
                'cta_3' => $value['cta_3'],
                'cta_4' => $value['cta_4'],
                'cta_5' => $value['cta_5'],
                'cta_6' => $value['cta_6'],
                'cta_7' => $value['cta_7'],
                'cta_8' => $value['cta_8'],
                'cta_9' => '000',
                'cta_10' => '000',
                'document_type' => $policy->activity->affiliate->doc_type,
                'document_number' => $policy->activity->affiliate->doc_number,
                'name_taker' => $policy->activity->affiliate->full_name,
                'cod_intermediary' =>  $user && is_object($user) && isset($user->code_correduria) ? $user->code_correduria : $policy->code,
                'cod_moneda' => $policy->type_currency == 'USD' ? 'US' : 'CO',
                'exchange_rate' => $trm,
                'entry_code' => $nextEntryCode,
                'ctaux'  =>$value['ctaux'],
                'activity_gis' => $activity_gis,
                'caso_gis' => $casoGis,
                'affected_worker' => $trabajador
            ];
        }

        // Inserta todos los registros en la base de datos tabla de asientos contables
        AccountingEntry::insert($data);
    }

    /**
     * REPORTE CONTABLE 138 (PAGO SINIESTRO RESERVA REASEGURO)
     * MS-2079
     * */
    public function autoAccountCase138() {

        try {

            $reinsurancePercentage = AppServiceProvider::$REINSURANCE_PERCENTAGE;

            $results = DB::table('accounting_entries')
                ->select(
                    'cod_moneda',
                    DB::raw('SUM(debit * '.$reinsurancePercentage.') AS valor'),
                    DB::raw("GROUP_CONCAT(DISTINCT number_policy SEPARATOR ',') AS policies")
                )
                ->where('cod_oper', '011')
                ->whereRaw('MONTH(accounting_entries.created_at) = MONTH(CURDATE())')
                ->whereRaw('YEAR(accounting_entries.created_at) = YEAR(CURDATE())')
                ->groupBy('cod_moneda')
                ->get();

            $webserviceController = new WebserviceAcselController();
            $trm = $webserviceController->getTrm();

            if ($trm ==0) {
                throw new \Exception('Error: El TRM no es valido');
            }

            foreach ($results as $result) {

                $nextEntryCode = AccountingEntry::max('entry_code');
                $nextEntryCode = sprintf('%04d',($nextEntryCode ?? 0) + 1);
                $this->reportAccountCase138($result, $trm, $nextEntryCode) ;
            }

            return response()->json(['success' => true], 200);

        } catch (\Exception $e) {

            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
            ],500);
        }
    }

    public function reportAccountCase138($result, $trm, $nextEntryCode){

        $cod_correlativo_d = "001";
        $cod_correlativo_c = "004";
        $cta5 = "001";
        if ($result->cod_moneda == 'US') {
            $cta5 = "002";
            $cod_correlativo_d = "003";
            $cod_correlativo_c = "002";
        }

        $paymentId = 'NA';

        $t_debit  =  $result->valor ;
        $t_credit =  $result->valor ;

        $values = [
            ['debit' => $result->valor, 'credit' => 0,      'movement_type'=> 'D', 'movement_type_org'=> 'D', 'cta_1' => '002', 'cta_2' => '060', 'cta_3' => '010', 'cta_4' => '010', 'cta_5' => $cta5, 'cta_6' => '000', 'cta_7' => '000', 'cta_8' => '000', 'cod_grupo_cpto' => 'IMPUES', 'cod_cpto' => 'IMPREA', 'cor_relativo'  => $cod_correlativo_d, 'ctaux' => '**************'],
            ['debit' => 0,      'credit' => $result->valor, 'movement_type'=> 'C', 'movement_type_org'=> 'C', 'cta_1' => '005', 'cta_2' => '040', 'cta_3' => '010', 'cta_4' => '010', 'cta_5' => $cta5, 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'INGRES', 'cod_cpto' => 'SINDES', 'cor_relativo'  => $cod_correlativo_c, 'ctaux' => '**************'],
        ];

        $cod_cia = '01';
        $data = [];
        $result->policies = rtrim($result->policies, ',');

        foreach ($values as $value) {
            $idecta = "01{$cod_cia}{$value['cta_1']}{$value['cta_2']}{$value['cta_3']}{$value['cta_4']}{$value['cta_5']}{$value['cta_6']}{$value['cta_7']}{$value['cta_8']}000000{$value['ctaux']}";

            $data[] = [
                'cod_cia' =>  $cod_cia,
                'cod_oper' => '138',
                'receipt_number' => "RT-$nextEntryCode",
                'receipt_status' => 'ACT',
                'number_policy' => $result->policies,
                'number_payment' => $paymentId,
                'receipt_type' => '138',
                'date_register' => Carbon::now(),
                'descoper' => 'SINIESTROS CONTRATOS',
                'cod_grupo_cpto' => $value['cod_grupo_cpto'],
                'cod_cpto' => $value['cod_cpto'],
                'cor_relativo' => $value['cor_relativo'],
                'cod_ramo' => 'SORT',
                'detail_movement' => 'SINIESTROS CONTRATOS-'.$result->policies.'-'.$paymentId.'-'."RT-$nextEntryCode".'-NA-'.$value['cod_cpto'],
                'debit' => $value['debit'],
                'credit' => $value['credit'],
                't_debit' => $t_debit,
                't_credit' => $t_credit,
                'difference' => 0,
                'movement_type' => $value['movement_type'],
                'movement_type_org' => $value['movement_type_org'],
                'idecta' => $idecta,
                'cta_1' => $value['cta_1'],
                'cta_2' => $value['cta_2'],
                'cta_3' => $value['cta_3'],
                'cta_4' => $value['cta_4'],
                'cta_5' => $value['cta_5'],
                'cta_6' => $value['cta_6'],
                'cta_7' => $value['cta_7'],
                'cta_8' => $value['cta_8'],
                'cta_9' => '000',
                'cta_10' => '000',
                'document_type' => 'NA',
                'document_number' => 'NA',
                'name_taker' => 'NA',
                'cod_intermediary' => 'NA' ,
                'cod_moneda' => $result->cod_moneda,
                'exchange_rate' => $trm,
                'entry_code' => $nextEntryCode,
                'ctaux'  =>$value['ctaux']
            ];
        }

        AccountingEntry::insert($data);
    }
    /**
     * Reporte contable 307
     * MS-2080
     * */
    public function autoAccountCase307() {

        try {

            $numberPolicies = DB::table('accounting_entries as a')
                ->selectRaw("
                            CAST(REPLACE(a.number_policy, 'SORT-', '') AS UNSIGNED) as number_policy,
                            SUM(
                                CASE a.cod_oper 
                                    WHEN 155 THEN a.debit * -1 
                                    WHEN 154 THEN a.debit
                                    ELSE 0 
                                END
                            ) AS reserva,
                            SUM(
                                CASE 
                                    WHEN a.cod_oper = 156 THEN a.debit * -1 
                                    ELSE 0 
                                END
                            ) AS liberacion_reserva,
                            SUM(CASE WHEN a.cod_oper IN (154) THEN a.debit ELSE 0 END)-SUM(CASE WHEN a.cod_oper IN (155, 156) THEN a.debit ELSE 0 END) AS reserva_col                           
                            SUM(
                                a.debit * 
                                (CASE WHEN a.cod_oper IN (155, 156) THEN -1 ELSE 1 END)
                            ) AS valor_crc
                        ")
                ->whereIn('a.cod_oper', [155, 156, 154])
                ->whereYear('a.created_at', DB::raw('YEAR(CURRENT_DATE)'))
                ->whereMonth('a.created_at', DB::raw('MONTH(CURRENT_DATE)'))
                ->where('a.cod_moneda', '=', 'US')
                ->groupBy(DB::raw("CAST(REPLACE(a.number_policy, 'SORT-', '') AS UNSIGNED)"))
                ->get();

            $webserviceController = new WebserviceAcselController();
            $trm = $webserviceController->getTrm();

            if ($trm ==0) {
                throw new \Exception('Error: El TRM no es valido');
            }
            $trm = 502.00;
            foreach ($numberPolicies as $result) {

                $nextEntryCode = AccountingEntry::max('entry_code');
                $nextEntryCode = sprintf('%04d',($nextEntryCode ?? 0) + 1);

                $policy = PolicySort::where('consecutive', $result->number_policy)->first();
                $this->reportAccountCase307($result, $policy, $trm, $nextEntryCode) ;
            }

            return response()->json(['success' => true], 200);

        } catch (\Exception $e) {

            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
            ],500);
        }
    }

    public function reportAccountCase307($result, $policy, $trm, $nextEntryCode){


        $saldoReserva = $result->reserva + $result->liberacion_reserva;
        $reservaCol = $saldoReserva*$trm;
        $difCambio = $reservaCol - $result->reserva_col;

//        dd([
//            'trm' => $trm,
//            'liberacion_reserva' => $result->liberacion_reserva,
//            'reserva' => $result->reserva,
//            '$saldoReserva' => $saldoReserva,
//            '$reservaCol' => $reservaCol,
//            'difCambio' => $difCambio,
//        ]);

        $cta1 = $difCambio < 0 ? "005" :"004" ;

        $paymentId = 'NA';

        $t_debit  =  $difCambio ;
        $t_credit =  $difCambio ;

        $values = [
            ['debit' => $difCambio, 'credit' => 0,      'movement_type'=> 'D', 'movement_type_org'=> 'D', 'cta_1' => '001', 'cta_2' => '060', 'cta_3' => '030', 'cta_4' => '010', 'cta_5' => '002', 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'PRIMAS', 'cod_cpto' => 'RECIBO', 'cor_relativo'  => '004', 'ctaux' => '**************'],
            ['debit' => 0,      'credit' => $difCambio, 'movement_type'=> 'C', 'movement_type_org'=> 'C', 'cta_1' => $cta1, 'cta_2' => '010', 'cta_3' => '080', 'cta_4' => '040', 'cta_5' => '002', 'cta_6' => '000', 'cta_7' => '000', 'cta_8' => '180', 'cod_grupo_cpto' => 'PRIMAS', 'cod_cpto' => 'RECIBO', 'cor_relativo'  => '002', 'ctaux' => '**************'],
        ];

        $cod_cia = '01';
        $data = [];

        $username = preg_replace('/^CO-/', '', $policy->code); // Remover el prefijo "CO-"
        $user = User::where(function ($query) use ($username) {
            $query->where('username', $username) // Buscar directamente sin "CO-"
            ->orWhere('username', 'CO-' . $username); // Buscar con "CO-"
        })->first();

        foreach ($values as $value) {
            $idecta = "01{$cod_cia}{$value['cta_1']}{$value['cta_2']}{$value['cta_3']}{$value['cta_4']}{$value['cta_5']}{$value['cta_6']}{$value['cta_7']}{$value['cta_8']}000000{$value['ctaux']}";

            $data[] = [
                'cod_cia' =>  $cod_cia,
                'cod_oper' => '307',
                'receipt_number' => "RT-$nextEntryCode",
                'receipt_status' => 'ACT',
                'number_policy' => $policy->formatNumberConsecutive(),
                'number_payment' => $paymentId,
                'receipt_type' => '307',
                'date_register' => Carbon::now(),
                'descoper' => 'DIFERENCIAL CAMBIARIO DE LA  RESERVA DE LA PRIMA PPND',
                'cod_grupo_cpto' => $value['cod_grupo_cpto'],
                'cod_cpto' => $value['cod_cpto'],
                'cor_relativo' => $value['cor_relativo'],
                'cod_ramo' => 'SORT',
                'detail_movement' => 'DIFERENCIAL CAMBIARIO DE LA  RESERVA DE LA PRIMA PPND-'.$policy->formatNumberConsecutive().'-'.$paymentId.'-'."RT-$nextEntryCode".'-'.'NA'.'-'.$value['cod_cpto'],
                'debit' => $value['debit'],
                'credit' => $value['credit'],
                't_debit' => $t_debit,
                't_credit' => $t_credit,
                'difference' => 0,
                'movement_type' => $value['movement_type'],
                'movement_type_org' => $value['movement_type_org'],
                'idecta' => $idecta,
                'cta_1' => $value['cta_1'],
                'cta_2' => $value['cta_2'],
                'cta_3' => $value['cta_3'],
                'cta_4' => $value['cta_4'],
                'cta_5' => $value['cta_5'],
                'cta_6' => $value['cta_6'],
                'cta_7' => $value['cta_7'],
                'cta_8' => $value['cta_8'],
                'cta_9' => '000',
                'cta_10' => '000',
                'document_type' => $policy->activity->affiliate->doc_type,
                'document_number' => $policy->activity->affiliate->doc_number,
                'name_taker' => $policy->activity->affiliate->full_name,
                'cod_intermediary' =>  $user && $user->code_correduria ? $user->code_correduria : $policy->code,
                'cod_moneda' => 'US',
                'exchange_rate' => $trm,
                'entry_code' => $nextEntryCode,
                'ctaux'  =>$value['ctaux']
            ];
        }
        AccountingEntry::insert($data);
    }

    /**
     * Reporte contable 307
     * MS-2042
     * */
    public function reportAccountCase055($cpath, $id){

        $policy = PolicySort::find($id);
        $ctaux_date = AppServiceProvider::$CTAUX;

        $nextEntryCode = AccountingEntry::max('entry_code');
        $nextEntryCode = sprintf('%04d',($nextEntryCode ?? 0) + 1);

        $activityPayment = Activity::where('parent_id', $policy->activity->id)
            ->where("service_id", Service::SERVICE_POLICY_SORT_COLLECTION_MNK)
            ->whereHas('policy_sort_collection', function ($query) {
                $query->where('payment_status', PolicySortCollection::PAYMENT_STATUS_APPROVED);
            })
            ->latest()
            ->first();

        $paymentId = $activityPayment->policy_sort_collection->id;
        $policySort_colletion = $activityPayment->policy_sort_collection;

        $currencyKey = ($policy->type_currency == 'CRC') ? 'CO' : 'DO';

        $username = preg_replace('/^CO-/', '', $policy->code);

        $user = User::where(function ($query) use ($username) {
            $query->where('username', $username) // Buscar directamente sin "CO-"
            ->orWhere('username', 'CO-' . $username) // Buscar con "CO-"
            ->orWhere('code_mnk',$username)
                ->orWhere('code_mnk','CO-' . $username);
        })->first();

        switch ($policySort_colletion->bank_id) {
            case 1:
                $ctaux = $ctaux_date['davivienda'][$currencyKey];
                break;
            case 2:
                $ctaux = $ctaux_date['banco_nacional'][$currencyKey];
                break;
            case 3:
                $ctaux = $ctaux_date['bac_redomatic'][$currencyKey];
                break;
            case 4:
                $ctaux = $ctaux_date['banco_costa_rica'][$currencyKey];
                break;
            default:
                $ctaux = '**************';
                break;
        }
        if ($policySort_colletion->payment_method == 'TC'){
            $ctaux = $ctaux_date['bac_redomatic'][$currencyKey];
        }

        $cod_moneda = 'CO';
        $trm = 0;


        $balance_in_favor =  $activityPayment->policy_sort_collection->applied_credit_note_amount;
        $amountPolicy = $activityPayment->policy_sort_collection->total_amount ?? 0;


        $cta5 = '001';

        if ($policy->type_currency == 'USD'){
            $webserviceController = new WebserviceAcselController();
            $trm = $webserviceController->getTrm();

            if ($trm ==0) {
                throw new \Exception('Error: El TRM no es valido');
            }

            $balance_in_favor = $balance_in_favor*$trm;
            $amountPolicy = $amountPolicy*$trm;

            $cod_moneda = 'US';
            $cta5 = '002';
        }


        $total1 = $amountPolicy-$balance_in_favor;
        $total1 = $total1 < 0 ? $total1*-1 : $total1;
        $total2 = $amountPolicy;
        $total3 = $balance_in_favor;

        $t_debit  =  $total1+$total3 ;
        $t_credit =  $total2;

        $codRelativo1 = $policy->type_currency == 'USD' ? (($policySort_colletion->bank_id == 2 || $policySort_colletion->bank_id == 4) ? '018' : '022') : (($policySort_colletion->bank_id == 2 || $policySort_colletion->bank_id == 4) ? '005' : '021') ;
        $codRelativo2 = $policy->type_currency == 'USD' ? '019' : '003';
        $codRelativo3 = $policy->type_currency == 'USD' ? '022' : '007';

        $values = [];

        if ($policySort_colletion->bank_id == 2 || $policySort_colletion->bank_id == 4 ){
            $values = [
                ['debit' => $total1,        'credit' => 0,        'movement_type'=> 'D', 'movement_type_org'=> 'D', 'cta_1' => '001', 'cta_2' => '010', 'cta_3' => '030', 'cta_4' => '010', 'cta_5' => $cta5, 'cta_6' => '010', 'cta_7' => '000', 'cta_8' => '000', 'cta_9' => '000', 'cta_10' => '000','cod_grupo_cpto' => 'INGRES', 'cod_cpto' => 'MOVBAN', 'cor_relativo' => $codRelativo1, 'descoper' => 'DEPOSITO BANCARIO',                 'ctaux' => $ctaux,           'cod_oper'=>'017'],
                ['debit' => 0,              'credit' => $total2,  'movement_type'=> 'C', 'movement_type_org'=> 'C', 'cta_1' => '001', 'cta_2' => '040', 'cta_3' => '030', 'cta_4' => '010', 'cta_5' => $cta5, 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cta_9' => '000', 'cta_10' => '000','cod_grupo_cpto' => 'PRIMAS', 'cod_cpto' => 'RECIBO', 'cor_relativo' => $codRelativo2, 'descoper' => 'COBRO DE PRIMAS (AM)',              'ctaux' => '**************', 'cod_oper'=>'055'],
                ['debit' => $total3, 'credit' => 0,               'movement_type'=> 'D', 'movement_type_org'=> 'D', 'cta_1' => '002', 'cta_2' => '040', 'cta_3' => '020', 'cta_4' => '030', 'cta_5' => $cta5, 'cta_6' => '000', 'cta_7' => '070', 'cta_8' => '000', 'cta_9' => '000', 'cta_10' => '000','cod_grupo_cpto' => 'PRIMDE', 'cod_cpto' => 'PRIMAD', 'cor_relativo' => $codRelativo3, 'descoper' => 'PAGO DE PRIMA EN DEPOSITO (AM)',    'ctaux' => '**************', 'cod_oper'=>'055'],
            ];
        }else{
            $values = [
                ['debit' => $total1,        'credit' => 0,        'movement_type'=> 'D', 'movement_type_org'=> 'D', 'cta_1' => '001', 'cta_2' => '010', 'cta_3' => '030', 'cta_4' => '020', 'cta_5' => $cta5, 'cta_6' => '010', 'cta_7' => '000', 'cta_8' => '000', 'cta_9' => '000', 'cta_10' => '000','cod_grupo_cpto' => 'INGRES', 'cod_cpto' => 'MOVBAN', 'cor_relativo' => '021', 'descoper' => 'DEPOSITO BANCARIO',                 'ctaux' => $ctaux,           'cod_oper'=>'017'],
                ['debit' => 0,              'credit' => $total2,  'movement_type'=> 'C', 'movement_type_org'=> 'C', 'cta_1' => '001', 'cta_2' => '040', 'cta_3' => '030', 'cta_4' => '010', 'cta_5' => $cta5, 'cta_6' => '020', 'cta_7' => '000', 'cta_8' => '180', 'cta_9' => '000', 'cta_10' => '000','cod_grupo_cpto' => 'PRIMAS', 'cod_cpto' => 'RECIBO', 'cor_relativo' => '003', 'descoper' => 'COBRO DE PRIMAS (AM)',              'ctaux' => '**************', 'cod_oper'=>'055'],
                ['debit' => $total3, 'credit' => 0,               'movement_type'=> 'D', 'movement_type_org'=> 'D', 'cta_1' => '002', 'cta_2' => '040', 'cta_3' => '020', 'cta_4' => '030', 'cta_5' => $cta5, 'cta_6' => '000', 'cta_7' => '070', 'cta_8' => '000', 'cta_9' => '000', 'cta_10' => '000','cod_grupo_cpto' => 'PRIMDE', 'cod_cpto' => 'PRIMAD', 'cor_relativo' => '007', 'descoper' => 'PAGO DE PRIMA EN DEPOSITO (AM)',    'ctaux' => '**************', 'cod_oper'=>'055'],
            ];
        }

        $data = [];

        foreach ($values as $value) {

            $idecta = "01{$value['cta_1']}{$value['cta_2']}{$value['cta_3']}{$value['cta_4']}{$value['cta_5']}{$value['cta_6']}{$value['cta_7']}{$value['cta_8']}000000{$value['ctaux']}";

            $data[] = [
                'cod_cia' => '01',
                'cod_oper' => $value['cod_oper'],
                'receipt_number' => "RT-$nextEntryCode",
                'receipt_status' => 'ACT',
                'number_policy' => $policy->formatNumberConsecutive(),
                'number_payment' => $paymentId,
                'receipt_type' => '055',
                'date_register' => $policy->created_at,
                'descoper' =>  $value['descoper'],
                'cod_grupo_cpto' => $value['cod_grupo_cpto'],
                'cod_cpto' => $value['cod_cpto'],
                'cor_relativo' => $value['cor_relativo'],
                'cod_ramo' => 'SORT',
                'detail_movement' =>$value['descoper']. "-" . $policy->formatNumberConsecutive(). "-" . $paymentId. "-" ."RT-$nextEntryCode".'-'.$policy->activity->affiliate->full_name. "-" . $value['cod_cpto'] ,
                'debit' => $value['debit'],
                'credit' => $value['credit'],
                't_debit' => $t_debit,
                't_credit' => $t_credit,
                'difference' => 0,
                'movement_type' => $value['movement_type'],
                'movement_type_org' => $value['movement_type_org'],
                'idecta' => $idecta,
                'cta_1' => $value['cta_1'],
                'cta_2' => $value['cta_2'],
                'cta_3' => $value['cta_3'],
                'cta_4' => $value['cta_4'],
                'cta_5' => $value['cta_5'],
                'cta_6' => $value['cta_6'],
                'cta_7' => $value['cta_7'],
                'cta_8' => $value['cta_8'],
                'cta_9' => $value['cta_9'],
                'cta_10' => $value['cta_10'],
                'document_type' => $policy->activity->affiliate->doc_type,
                'document_number' => $policy->activity->affiliate->doc_number,
                'name_taker' => $policy->activity->affiliate->full_name,
                'cod_intermediary' =>  isset($user) && isset($user->code_correduria) ? $user->code_correduria : $policy->code,
                'cod_moneda' => $cod_moneda,
                'exchange_rate' => $trm,
                'ctaux' => $value['ctaux'],
                'entry_code' => $nextEntryCode
            ];
        }

        AccountingEntry::insert($data);

    }
}