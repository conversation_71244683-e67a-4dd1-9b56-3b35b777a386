<?php

namespace App\Http\Controllers\Services;

use App\Actions\ActionPolicySortCollection;
use App\Http\Controllers\Integrations\WebserviceAcselController;
use App\MailTemplates\Constants\Templates;
use App\MailTemplates\TemplateBuilder;
use App\PolicySpreadsheet;
use App\Providers\AppServiceProvider;
use App\States\StatePolicySortCollection;
use App\User;
use Carbon\Carbon;
use DateTime;
use App\ActivityDocument;
use App\ConstancySort;
use App\Action;
use App\Actions\ActionConstaciasSort;
use App\Activity;
use App\ActivityAction;
use App\ActivityActionDocument;
use App\Affiliate;
use App\Client;
use App\Mail\SendDocumentDataBase;
use App\PolicySort;
use App\PolicySortCollection;
use App\Http\Controllers\Controller;
use App\Service;
use App\State;
use App\States\StatePoliza;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use App\Http\Controllers\ActionController;
use App\Http\Controllers\Tables\MailBoardController;
use App\LiquidationSorts;
use Illuminate\Support\Facades\DB;
use PDF;

class LiquidationPolicyController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */

    private $type_cost;
    private $periodicityMap;

    public function __construct()
    {
        $this->middleware('auth')->except([
            'notifyPolicyLiquidation',
            'generateLiquidationPolicy',
            'reportUnpaidSettlementReceipt'
        ]);

        $this->type_cost = [
            'policy_certification_up_to_date' => 'Certificado de póliza al día',
            'outstanding_sums_to_be_paid' => 'Constancia de sumas pendientes por pagar',
            'employee_account_statement' => 'Estado de cuenta del trabajador',
            'premiums_paid_certificate' => 'Constancia de primas pagadas',
        ];


        $this->periodicityMap = [
            '1' => 'Anual',
            '2' => 'Semestral',
            '3' => 'Trimestral',
            '4' => 'Mensual',
            '5' => 'Pago único'
        ];

    }

    public function form(Request $req, $cpath, $id)
    {
        //Buscamos el cliente
        $client = Client::where('path', $cpath)->first();

        //Buscamos la actividad de la constancia por su activity_id
        $activity = Activity::where('client_id', $client->id)
            ->where('id', $id)
            ->first();

        //Buscamos la constancia por activity_id
        $liquidationSort = LiquidationSorts::where('activity_id', $activity->id)
            ->first();

        //Buscamos la poliza con el parent_id de la constancia
        $policySort = PolicySort::where('activity_id', $activity->parent_id)
            ->first();

        //datos de pago
        $policySortCollections = PolicySortCollection::whereHas('activity', function ($query) use ($id) {
            $query->where('parent_id', $id);
        })->get();

        $policySortCollections = $policySortCollections->first();

        $liquidationData = LiquidationPolicyController::liquidationData($activity->parent_id, $policySort);

        //

        $jsonSource = ($policySort->economic_activity == 'public') ? '/js/economic_activity/public.json' : '/js/economic_activity/private.json';
        $json = file_get_contents(public_path($jsonSource));
        $economicActivities = json_decode($json, true);
        //Se transforma a una collección en laravel
        $activity_economic_name = collect($economicActivities)->firstWhere('CODE', $policySort->activity_economic_id)['ACTIVITY_NAME'];

        $policySort->economic_activity_name = $activity_economic_name;
        //$policySort->economic_activity_name = $policySort->economic_activity_name == 'private' ? 'Privado' : 'Público';

        if ($policySortCollections) {
            $policySortCollections->payment_status = $policySortCollections->payment_status == 'approved' ? 'Aprobado' : 'Pendiente';
        }

        $paymentMethod = '';
        if ($policySort) {
            $policySort->economic_activity = $policySort->economic_activity == 'private' ? 'Privado' : 'Público';
            $paymentMethod = $this->periodicityMap[$policySort->periodicity] ?? '';
        }


        return view('services.liquidation_sort.form',
            [
                'activity' => $activity,
                'policySort' => $policySort,
                'policySortCollections' => $policySortCollections,
                'tem' => $liquidationData['tem'],
                'salarios_reportados' => $liquidationData['salarios_reportados'],
                'resul_liquidation' => $liquidationData['resul_liquidation'],
                'prima_neta' => $liquidationData['prima_neta'],
                'sobrante_prima' => $liquidationData['sobrante_prima'],
                'id' => $id,
                'liquidationSort' => $liquidationSort,
                'paymentMethod' => $paymentMethod,
                'deuda_pendiente' => $liquidationData['deuda_pendiente']
            ]
        );
    }

    public function generateLiquidationPolicy(Request $req, $cpath)
    {

        $liquidacionSort = Action::LIQUIDACION_SORT;
        $policySortAllRows = PolicySort::whereDate('validity_to', '<', Carbon::now())
            ->with('activity')
            ->get();


        $camel = 0;

        if ($policySortAllRows->isNotEmpty()) {

            //Iniciamos la transacción
            DB::beginTransaction();

            //Inicio del try catch
            try {

                //Recorrremos una a una las polizas que cumplan la fecha para ser liquidadas
                foreach ($policySortAllRows as $policySortRow) {

                    //Buscamos la actividad de la póliza
                    $activityPolicy = $policySortRow->activity;

                    //Si no encontramos actividad de polizas continuamos al siguiente registro
                    if (!isset($activityPolicy->id)) {
                        continue;
                    }

                    //Verificamos si esa póliza ya cuenta con una liquidación
                    $existingActivity = Activity::where('parent_id', $activityPolicy->id)
                        ->where('service_id', Service::SERVICE_LIQUIDATION_SORT_MNK)
                        ->first();

                    //Si la póliza cuenta con una liquidación continuamos el proceso
                    if ($existingActivity || $activityPolicy->state_id != StatePoliza::POLIZA_EMITIDA_ACTIVA) {
                        continue;
                    }

                    //Si son polizas de periodo corto
                    if ($policySortRow->temporality == 'short') {
                        $from = Carbon::parse($policySortRow->validity_from);
                        $to = Carbon::parse($policySortRow->validity_to);
                    
                        // Verificamos si el período entre ambas fechas es menor o igual a 3 meses, si es asi continuamos
                        if ($from->diffInMonths($to) <= 3) {
                            continue;
                        }
                    }

                    //Cuando las pólizas tengan las modalidades de riesgos, Riesgos del Trabajo Especial Formación Técnica Dual, RT Hogar y RT Ocasional NO se realizara liquidación.
                    if($policySortRow->work_modality_id == 2 || $policySortRow->work_modality_id == 3 ||  $policySortRow->work_modality_id == 4){
                        continue;
                    }

                    //Creamos la nueva actividad para el servicio de liquidación
                    $activityNew = new Activity();
                    $activityNew->parent_id = $activityPolicy->id;
                    $activityNew->client_id = $activityPolicy->client_id;
                    $activityNew->affiliate_id = $activityPolicy->affiliate_id;
                    $activityNew->service_id = Service::SERVICE_LIQUIDATION_SORT_MNK;
                    $activityNew->state_id = State::REGISTRADO;
                    $activityNew->user_id = $activityPolicy->user_id ?? 1;
                    $activityNew->save();

                    $liquidationData = LiquidationPolicyController::liquidationData($activityPolicy->id, $policySortRow);

                    //dd($liquidationData, $policySortRow->salary_projection, $policySortRow->accumulated_premium);
                    $constancy = new LiquidationSorts();
                    $constancy->activity_id = $activityNew->id;
                    $constancy->pending_charges = $liquidationData['deuda_pendiente'] ?? 0;
                    $constancy->other_amounts_owed = $liquidationData['other_amounts_owed'] ?? 0;
                    $constancy->insurance_increase_payment = $liquidationData['insurance_increase_payment'] ?? 0; //PENDIENTE
                    $constancy->accumulated_premium_payment = $liquidationData['accumulated_premium_payment'] ?? 0;//$policySortRow->accumulated_premium ?? 0;
                    $constancy->salary_projection = $policySortRow->salary_projection ?? 0;
                    $constancy->reported_salaries = $liquidationData['salarios_reportados'] ?? 0;
                    $constancy->tem = $liquidationData['tem'];
                    $constancy->settlement_result = $liquidationData['resul_liquidation'] ?? 0;
                    $constancy->net_premium = $liquidationData['prima_neta'] ?? 0;
                    $constancy->premium_surcharge = $liquidationData['sobrante_prima'] ?? 0;
                    $constancy->pending_insurance_increases = $liquidationData['pending_insurance_increases'] ?? 0;
                    $constancy->save();

                    $activityAction = ActionController::create($activityNew->id, $liquidacionSort::INICIAR_LIQUIDACION_POLIZA, 'INICIAR LIQUIDACIÓN TRÁMITE');

                    $resultLiquidation = $liquidationData['resul_liquidation'] ?? 0;
                    $resultLiquidation = $resultLiquidation < 0 ? ($resultLiquidation)*-1 : $resultLiquidation;

                    if ($policySortRow->type_currency != 'USD'){
                        $webserviceController = new WebserviceAcselController();
                        $trm = $webserviceController->getTrm();

                        if ($trm ==0) {
                            throw new \Exception('Error: El TRM no es valido');
                        }

                        $resultLiquidation =  $resultLiquidation/$trm;

                    }


                    if ($resultLiquidation >= 100 ) {
                        if ($liquidationData['resul_liquidation'] > 0) {
                            LiquidationPolicyController::reportPendingSettlementPayment($cpath, $activityNew->id, $policySortRow, $liquidationData['resul_liquidation']);
                        } else {
                            LiquidationPolicyController::settledPolicyWithCreditBalance($cpath, $activityNew->id, $policySortRow);
                        }
                    }

                    DB::commit();
                    $camel++;

                }

                return response()->json([
                    'message' => 'Liquidacion generada',
                    'cant_generadas' => $camel,
                    'policySortAllRows' => $policySortAllRows], 200);

            } catch (\Exception $e) {
                DB::rollBack();
                return response()->json([
                    'status' => 'error',
                    'message' => $e->getMessage(),
                ], 500);
            }
        }

    }

    public function pruebaPDF($id)
    {

        $activity = Activity::where('id', $id)->first();
        $policy_sort = $activity->policy_sort;

        $activitiesPolicySpreadsheetIds = Activity::where('parent_id', $activity->id)
            ->where('service_id', Service::SERVICE_REPORT_TAKEN_FORM_MNK)
            ->whereIn('state_id', [State::PLANILLA_REPORTADA, State::CERTIFICADO_REPORTADO_TOMADOR])
            ->pluck('id');

        $policySpreadsheet = PolicySpreadsheet::whereIn('activity_id', $activitiesPolicySpreadsheetIds)
            ->get();

        $filteredPolicySpreadsheets = $policySpreadsheet->groupBy('activity_id')->map(function ($group) {
            return $group->sortByDesc('id')->first(); // Ordena de manera descendente y toma el primer registro (el de mayor id)
        });

        //$liquidation = Activity::where('id', 350009)->first();
        $liquidation = Activity::where('parent_id', $id)->where('service_id', Service::SERVICE_LIQUIDATION_SORT_MNK)->latest('id')->first();

        $datos = [
            'activity' => $liquidation,
            'policySort' => $policy_sort,
            'policySpreadsheet' => $filteredPolicySpreadsheets,
            'cobrar' => true,
            'watermark' => true,
        ];

        $pdf = PDF::loadView('services.liquidation_sort.docs.pending_payment_pdf', $datos);

        //return view('services.liquidation_sort.docs.pending_payment_pdf', $datos);
        //return $pdf->download('prueba.pdf');
        return $pdf->stream('prueba.pdf');
    }

    //ACCION REPORTAR LIQUIDACIÓN-PENDIENTE PAGO MS-87
    public function reportPendingSettlementPayment($cpath, $activity_id, $policySort, $resul_liquidation)
    {

        DB::beginTransaction();

        try {

            $activity = Activity::where('id', $policySort->activity_id)->first();
            $affiliate = Affiliate::where('id', $policySort->activity->affiliate_id)->first();
            $client = Client::where('path', $cpath)->first();

            $policy_sort = $activity->policy_sort;

            $activitiesPolicySpreadsheetIds = Activity::where('parent_id', $activity->id)
                ->where('service_id', Service::SERVICE_REPORT_TAKEN_FORM_MNK)
                ->whereIn('state_id', [State::PLANILLA_REPORTADA, State::CERTIFICADO_REPORTADO_TOMADOR])
                ->pluck('id');  // Solo obtiene los valores de la columna 'id'

            //Planillas del tomador
            $policySpreadsheet = PolicySpreadsheet::whereIn('activity_id', $activitiesPolicySpreadsheetIds)
                ->get();

            // Agrupamos por activity_id y obtenemos el registro con el id máximo en cada grupo
            $filteredPolicySpreadsheets = $policySpreadsheet->groupBy('activity_id')->map(function ($group) {
                return $group->sortByDesc('id')->first(); // Ordena de manera descendente y toma el primer registro (el de mayor id)
            });

            $liquidacionSort = Action::LIQUIDACION_SORT;

            $activityAction = ActionController::create($activity_id, $liquidacionSort::REPORTAR_LIQUIDACION_PENDIENTE_PAGO, 'REPORTAR LIQUIDACIÓN PENDIENTE PAGO');

            $liquidation = Activity::where('id', $activity_id)->first();

            $datos = [
                'activity' => $liquidation,
                'policySort' => $policy_sort,
                'policySpreadsheet' => $filteredPolicySpreadsheets,
                'cobrar' => true,
                'watermark' => true,
            ];

            $pdf = PDF::loadView('services.liquidation_sort.docs.pending_payment_pdf', $datos);

            $document = 'reporte_pago_pendiente';
            $fileName = "{$document}_{$activity_id}.pdf";

            // Guardar el archivo en S3
            $filePath = "activity_action_document/{$fileName}";
            Storage::disk('s3')->put($filePath, $pdf->output());

            // Guardamos el path del documento en la base de datos
            $activityActionDocument = new ActivityActionDocument();
            $activityActionDocument->activity_action_id = $activityAction->id;
            $activityActionDocument->name = $document;
            $activityActionDocument->path = $filePath;
            $activityActionDocument->save();

            // Agrega el email principal de notificaciones y todos los adicionales
            $emails = PolicySortController::getAdditionalNotificationEmails(
                $policySort->id,
                [
                    $policySort->email, // emailIntermediary
                ]
            );

            $validityFrom = Carbon::parse($policySort->validity_from)->format('d/m/Y');
            $validityTo = Carbon::parse($policySort->validity_to)->format('d/m/Y');
            $first_name = mb_convert_case(mb_strtolower($activity->affiliate->first_name ?? ''), MB_CASE_TITLE, "UTF-8");

            $emaDataLiquidation = $this->_buildPolicyLiquidation24Email($policySort->formatSortNumber(), $first_name, $validityFrom, $validityTo);

            $attachments = [
                [
                    'path' => $filePath,
                    'name' => basename($filePath),
                    'type' => 'PDF'
                ]
            ];

            $this->sendEmail($emails,
                $emaDataLiquidation->subject,
                $emaDataLiquidation->text,
                $attachments,
                $client,
                $policySort->activity_id,
                $activity_id,
                $policySort,
                $emaDataLiquidation->subject);

            //se genera el servicio COBROS POLIZA SORT.

            $activityNew = new Activity();
            $activityNew->client_id = $client->id;
            $activityNew->affiliate_id = $affiliate->id;
            $activityNew->service_id = Service::SERVICE_POLICY_SORT_COLLECTION_MNK;
            $activityNew->state_id = State::REGISTRADO;
            $activityNew->parent_id = $policySort->activity_id;//$activity_id;
            $activityNew->user_id = User::USER_ADMIN;
            $activityNew->save();

            $data_request = [
                'total_amount' => $resul_liquidation
            ];

            $policyCollectionController = new PolicySortCollectionController();
            $policyCollectionController->reportLiquidationPaymentReceipt(new Request($data_request), $cpath, $activityNew->id);

            DB::commit();

        } catch (Exception $e) {
            DB::rollback();
            return response()->json([
                'status' => 'error',
                'message' => 'Ocurrió un error al enviar el correo.'
            ], 500);
        }

        return response()->json([
            'status' => 'success',
            'message' => 'Registro creado y email enviado exitosamente'
        ]);

    }

    public function settledPolicyWithCreditBalance($cpath, $activity_id, $policySort)
    {

        $activity = Activity::where('id', $policySort->activity_id)->first();
        $client = Client::where('path', $cpath)->first();

        DB::beginTransaction();

        try {

            $policy_sort = $activity->policy_sort;

            $liquidacionSort = Action::LIQUIDACION_SORT;

            $activityAction = ActionController::create($activity_id, $liquidacionSort::REPORTAR_LIQUIDACION_SALDO_FAVOR, 'REPORTAR LIQUIDACIÓN-SALDO A FAVOR');

            $activitiesPolicySpreadsheetIds = Activity::where('parent_id', $activity->id)
                ->where('service_id', Service::SERVICE_REPORT_TAKEN_FORM_MNK)
                ->whereIn('state_id', [State::PLANILLA_REPORTADA, State::CERTIFICADO_REPORTADO_TOMADOR])
                ->pluck('id');

            $policySpreadsheet = PolicySpreadsheet::whereIn('activity_id', $activitiesPolicySpreadsheetIds)
                ->get();

            $filteredPolicySpreadsheets = $policySpreadsheet->groupBy('activity_id')->map(function ($group) {
                return $group->sortByDesc('id')->first(); // Ordena de manera descendente y toma el primer registro (el de mayor id)
            });

            $liquidation = Activity::where('id', $activity_id)->first();

            $datos = [
                'activity' => $liquidation,
                'policySort' => $policy_sort,
                'policySpreadsheet' => $filteredPolicySpreadsheets,
                'cobrar' => false,
                'watermark' => true,
            ];

            $pdf = PDF::loadView('services.liquidation_sort.docs.pending_payment_pdf', $datos);

            // Generamos el nombre del archivo
            $document = 'reporte_saldo';
            $fileName = "{$document}_{$activity_id}.pdf";

            // Guardar el archivo en S3
            $filePath = "activity_action_document/{$fileName}";
            Storage::disk('s3')->put($filePath, $pdf->output());

            // Guardamos el path del documento en la base de datos
            $activityActionDocument = new ActivityActionDocument();
            $activityActionDocument->activity_action_id = $activityAction->id;
            $activityActionDocument->name = $document;
            $activityActionDocument->path = $filePath;
            $activityActionDocument->save();

            // Agrega el email principal de notificaciones y todos los adicionales
            $emails = PolicySortController::getAdditionalNotificationEmails(
                $policySort->id,
                [
                    $policySort->email, // emailIntermediary
                ]
            );

            $first_name = $activity->affiliate->first_name ? mb_convert_case(mb_strtolower($activity->affiliate->first_name ?? ''), MB_CASE_TITLE, "UTF-8") : '';
            $validityFrom = Carbon::parse($policySort->validity_from)->format('d/m/Y');
            $validityTo = Carbon::parse($policySort->validity_to)->format('d/m/Y');

            $emaDataLiquidation = $this->_buildPolicyLiquidation24Email($policySort->formatSortNumber(), $first_name, $validityFrom, $validityTo);

            $attachments = [
                [
                    'path' => $filePath,
                    'name' => basename($filePath),
                    'type' => 'PDF'
                ]
            ];

            $this->sendEmail($emails,
                $emaDataLiquidation->subject,
                $emaDataLiquidation->text,
                $attachments, $client,
                $policySort->activity_id,
                $activity_id,
                $policySort,
                'Reporte Liquidación');

            DB::commit();

        } catch (Exception $e) {
            DB::rollback();
            return response()->json([
                'status' => 'error',
                'message' => 'Ocurrió un error al enviar el correo.'
            ], 500);
        }

        return response()->json([
            'status' => 'success',
            'message' => 'Registro creado y email enviado exitosamente'
        ]);
    }

    public function validaDataLiquitation($id)
    {
        $policySort = PolicySort::where('activity_id', $id)->first();
        return $this->liquidationData($id, $policySort);
    }

    public function liquidationData($id, $policySort)
    {

        $result = QuotationController::calculatePolicyPrice($policySort);
        $tem = 0;
        if ($policySort->periodicity == '1') { //ANUAL
            $tem = $result['percentage'];
        } else if ($policySort->periodicity == '2') { //SEMESTRAL
            $tem = round($result['percentage'] * (1.04), 2);
        } else if ($policySort->periodicity == '3') { //TRIMESTRAL
            $tem = round(($result['percentage']) * (1.06), 2);
        } else if ($policySort->periodicity == '4') { //MENSUAL
            $tem = round($result['percentage'] * (1.08), 2);
        } else { //PAGO UNICO
            $tem = $result['percentage'];
        }

        //Valor prima pagado acumulado
        $valorPrimaPagadoAcumulado = PolicySort::leftJoin('activities as a', 'a.parent_id', '=', 'policy_sorts.activity_id')
            ->leftJoin('policy_sort_collections as pc', 'a.id', '=', 'pc.activity_id')
            ->where('policy_sorts.activity_id', $id)
            ->whereNotNull('pc.id')
            ->where('pc.payment_status', 'approved')
            ->whereIn('pc.type_receipt', ['monthly_payment', 'quarterly_payment', 'semiannual_payment', 'emission'])
            ->sum('pc.total_amount');

        //Valor pagado por aumentos de seguro
        $valorPagoPorAumentoSeguro = PolicySort::leftJoin('activities as a', 'a.parent_id', '=', 'policy_sorts.activity_id')
            ->leftJoin('policy_sort_collections as pc', 'a.id', '=', 'pc.activity_id')
            ->where('policy_sorts.activity_id', $id)
            ->whereNotNull('pc.id')
            ->where('pc.payment_status', 'approved')
            ->where('pc.type_receipt', 'period_increase')
            ->sum('pc.total_amount');


        //Aumento seguros pendientes
        $aumentoSegurosPendientes = PolicySort::leftJoin('activities as a', 'a.parent_id', '=', 'policy_sorts.activity_id')
            ->leftJoin('policy_sort_collections as pc', 'a.id', '=', 'pc.activity_id')
            ->where('policy_sorts.activity_id', $id)
            ->whereNotNull('pc.id')
            ->where('pc.payment_status', 'pending')
            ->where('pc.type_receipt', 'period_increase')
            ->sum('pc.total_amount');

        //Cargos y deudas pendientes C
        $deudaPendiente = PolicySort::leftJoin('activities as a', 'a.parent_id', '=', 'policy_sorts.activity_id')
            ->leftJoin('policy_sort_collections as pc', 'a.id', '=', 'pc.activity_id')
            ->where('policy_sorts.activity_id', $id)
            ->whereNotNull('pc.id')
            ->where('pc.payment_status', 'pending')
            ->whereIn('pc.type_receipt', ['monthly_payment', 'quarterly_payment', 'semiannual_payment'])
            ->sum('pc.total_amount');


        $otrasSumasAdeudas = PolicySort::leftJoin('activities as a', 'a.parent_id', '=', 'policy_sorts.activity_id')
            ->leftJoin('policy_sort_collections as pc', 'a.id', '=', 'pc.activity_id')
            ->where('policy_sorts.activity_id', $id)
            ->whereNotNull('pc.id')
            ->where('pc.payment_status', 'pending')
            ->where('pc.type_receipt', '<>', 'period_increase')
            ->sum('pc.total_amount');

        $salaryRecords = Activity::select('policy_spreadsheets.total_salaries')
            ->leftJoin('policy_spreadsheets', 'policy_spreadsheets.activity_id', '=', 'activities.id')
            ->where('activities.parent_id', $id)
            ->where('activities.service_id', Service::SERVICE_REPORT_TAKEN_FORM_MNK)
            ->orderBy('policy_spreadsheets.id', 'asc')
            ->get();

        $salariosReportados = 0;
        foreach ($salaryRecords->slice(1) as $record) {
            $salariosReportados += $record->total_salaries;
        }

        $resul_liquidation = ($tem / 100) * ($salariosReportados - ($policySort->salary_projection * 12)) + $deudaPendiente;

        $prima_neta = $salariosReportados * ($tem / 100);
        $sobrante_prima = $salariosReportados < ($policySort->salary_projection * 12) ? $salariosReportados / ($policySort->salary_projection * 12) : '0';

        return [
            'resul_liquidation' => $resul_liquidation,
            'prima_neta' => $prima_neta,
            'sobrante_prima' => $sobrante_prima,
            'salarios_reportados' => $salariosReportados,
            'tem' => $tem,
            'deuda_pendiente' => $deudaPendiente,
            'other_amounts_owed' => $otrasSumasAdeudas,
            'insurance_increase_payment' => $valorPagoPorAumentoSeguro,
            'accumulated_premium_payment' => $valorPrimaPagadoAcumulado,
            'pending_insurance_increases' => $aumentoSegurosPendientes,
        ];

    }

    //ACCION REPORTAR PAGO LIQUIDACION MS-1848
    public function submitSettlementPayment($cpath, $id)
    {

        $client = Client::where('path', $cpath)->first();
        $activity = Activity::where('client_id', $client->id)->where('id', $id)->first();
        $policySort = PolicySort::where('activity_id', $activity->parent_id)->first();
        $affiliate = Affiliate::where('id', $policySort->activity->affiliate_id)->first();

        DB::beginTransaction();

        try {
            $liquidacionSort = Action::LIQUIDACION_SORT;
            $activityAction = ActionController::create($id, $liquidacionSort::REPORTAR_PAGO_LIQUIDACION, 'REPORTAR PAGO LIQUIDACIÓN');

            $emailIntermediary = $policySort->email;
            $emailTaker = $affiliate->email;
            $nameTaker = mb_convert_case(mb_strtolower($affiliate->full_name ?? ''), MB_CASE_TITLE, "UTF-8");

            $emails = array_filter([$emailIntermediary, $emailTaker], function ($email) {
                return !empty($email);
            });

            $currency = $policySort->type_currency == 'USD' ? '$' : '₡';
            $valor = number_format($activity->liquidation_sort->settlement_result ?? 0, 2, ',', '.');
            $fechaInicio = ucfirst(strftime('%e de %B del %Y', strtotime($policySort->validity_from))); // \Carbon\Carbon::parse($policySort->validity_from)->format('d/m/Y');
            $fechaFin = ucfirst(strftime('%e de %B del %Y', strtotime($policySort->validity_to))); // \Carbon\Carbon::parse($policySort->validity_to)->format('d/m/Y');

            $textEmail = "¡Buen día, $nameTaker!

                Nos complace confirmarle que hemos aplicado el pago de liquidación de la póliza #{$policySort->formatSortNumber()},para el periodo del {$fechaInicio} al {$fechaFin}. por un monto de {$currency} {$valor}.
                
                ¡Muchas gracias por la confianza que ha depositado en nosotros! Nuestro propósito es transformar la protección en una experiencia ágil, confiable y humana.";

            $text = [
                "text" => $textEmail,
                "sender" => 'mnk aseguramiento'
            ];

            $subject = "Confirmación del pago de la liquidación de póliza {$policySort->formatSortNumber()}";

            $this->sendEmail($emails, $subject, $text, [], $client, $policySort->activity_id, $id, $policySort, $subject);


            DB::commit();

        } catch (Exception $e) {
            DB::rollback();
            return response()->json([
                'status' => 'error',
                'message' => 'Ocurrió un error al enviar el correo.'
            ], 500);
        }

        return response()->json([
            'status' => 'success',
            'message' => 'Registro creado y email enviado exitosamente'
        ]);
    }

    private function sendEmail($emails, $subject, $text, $attachments, $client, $id, $activityAction, $policySort, $nameFrom)
    {
        $mailSent = new SendDocumentDataBase(
            implode(',', $emails),         // Correos a enviar
            $subject,                      // Asunto del correo
            "<EMAIL>",            // Remitente
            $nameFrom,                      // Asunto
            $text,                         // Cuerpo del email
            "<EMAIL>",  // Email de respuesta
            $attachments,                  // Archivos adjuntos
            "send_document_db",            // Tipo de envío
            $client,                       // Información del cliente
            request()->getHost(),          // Dominio
            $id,                           // ID de la actividad
            $activityAction,           // ID de la acción de la actividad
            $policySort->activity->service->id // ID del servicio
        );

        // Capturar el resultado del envío
        $result = $mailSent->sendMail();

        $activity = Activity::where('id', $id)->first();

        //Registramos los datos del correo enviado para la trazabilidad
        $mailBoardController = new MailBoardController();
        $mailBoardController->createRegisterMail(
            $activity->id,
            $activity->service->id, 
            $policySort->consecutive, 
            'Tomador', 
            mb_convert_case(mb_strtolower($policySort->activity->affiliate->full_name ?? ''), MB_CASE_TITLE, "UTF-8"), 
            $policySort->activity->affiliate->doc_number, 
            $subject, 
            $text,
            $emails, 
            $result,
            $attachments
        );
        
    }


    public function reportUnpaidSettlementReceipt()
    {

        DB::beginTransaction();
        try {


            //registros que tengan mas de 11 dias de generados
            $policySortCollection = PolicySortCollection::where('created_at', '<', Carbon::now()->subDays(11))
                ->whereIn('payment_status', ['pending-approval', 'pending'])
                ->where('type_receipt', PolicySortCollection::LIQUIDATION)->get();

            foreach ($policySortCollection as $row) {

                if ($row->activity->parent->state_id == StatePolicySortCollection::RECIBO_RENOVACION_PENDIENTE_PAGO) {
                    $activityAction = ActionController::create($row->activity->parent->id, ActionPolicySortCollection::REPORTAR_RECIBO_LIQUIDACION_NO_PAGADO, 'Reportar recibo de liquidación no pagado');
                }

                $row->payment_status = PolicySortCollection::PAYMENT_STATUS_CANCELLED;
                $row->save();
            }

            DB::commit();


            return response()->json([
                'message' => 'ejecución exitosa.',
            ], 200);

        } catch (Exception $e) {
            DB::rollback();

            return response()->json([
                'status' => 'error',
                'message' => 'Ocurrió un error',
                'error' => $e->getMessage()
            ], 500);

        }

    }

    // ME-1086 - Correo cobro de la liquidación de póliza
    public function notifyPolicyLiquidation()
    {

        try {

            $fiveDaysAgo = Carbon::now()->subDays(5)->startOfDay();
            $endOfDay = Carbon::now()->subDays(5)->endOfDay();

            $activityActions = ActivityAction::where('action_id', 213)
                ->whereBetween('created_at', [$fiveDaysAgo, $endOfDay])
                ->get();

            foreach ($activityActions as $row) {

                $activity = Activity::find($row->activity_id);

                $emailIntermediary = $activity->parent->policy_sort->email;
                $emailTaker = $activity->parent->affiliate->email;

                $emails = array_filter([$emailIntermediary, $emailTaker], function ($email) {
                    return !empty($email);
                });

                $validityFrom = Carbon::parse($activity->parent->policy_sort->validity_from)->format('d/m/Y');
                $validityTo = Carbon::parse($activity->parent->policy_sort->validity_to)->format('d/m/Y');
                $first_name = mb_convert_case(mb_strtolower($activity->parent->affiliate->full_name ?? ''), MB_CASE_TITLE, "UTF-8");

                $emaDataLiquidation = $this->_buildPolicyLiquidation24Email(
                    $activity->parent->policy_sort->formatNumberConsecutive(),
                    $first_name,
                    $validityFrom,
                    $validityTo);

                if ($emails != null) {
                    $mailSent = new SendDocumentDataBase(
                        $emails,
                        $emaDataLiquidation->subject,
                        "<EMAIL>",
                        "Liquidación de la póliza",
                        $emaDataLiquidation->text,
                        "<EMAIL>",
                        [], //files
                        "send_document_db",
                        1,
                        request()->getHost(),
                        $activity->id,
                        $row->id,
                        $activity->parent->service->id
                    );
                    
                      // Capturar el resultado del envío
                    $result = $mailSent->sendMail();

                    //Registramos los datos del correo enviado para la trazabilidad
                    $mailBoardController = new MailBoardController();
                    $mailBoardController->createRegisterMail(
                        $activity->id,
                        $activity->service->id, 
                        $activity->parent->policy_sort->consecutive, 
                        'Tomador', 
                        $first_name, 
                        $activity->parent->affiliate->doc_number, 
                        $emaDataLiquidation->subject, 
                        $emaDataLiquidation->text,
                        $emails, 
                        $result,
                        null
                    );

                    
                }
            }

            return response()->json([
                'message' => 'ejecución exitosa.',
            ], 200);

        } catch (Exception $e) {

            return response()->json([
                'status' => 'error',
                'message' => 'Ocurrió un error',
                'error' => $e->getMessage()
            ], 500);

        }

    }

    private function _buildPolicyLiquidation24Email($sortFormat, $name, $validityFrom, $validityTo): object
    {
        $emailData = TemplateBuilder::build(
            Templates::NOTIFY_POLICY_LIQUIDATION,
            [
                'policy_sort' => $sortFormat,
                'name' => $name,
                'validity_from' => $validityFrom,
                'validity_to' => $validityTo,
            ]
        );

        return (object) [
            'subject' => $emailData['subject'],
            'text' => [
                "text" => $emailData['body'],
                "sender" => $emailData['sender']
            ]
        ];
    }
}
