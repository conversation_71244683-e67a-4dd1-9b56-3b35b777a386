<?php

namespace App\Http\Controllers\Services;
use App\GisSort;
use App\MailTemplates\Constants\Templates;
use App\MailTemplates\TemplateBuilder;
use App\PeIpSort;
use App\PeItSort;
use App\PolicySpreadsheetAffiliate;
use Carbon\Carbon;
use DateTime;
use App\ActivityDocument;
use App\ConstancySort;
use App\Action;
use App\Actions\ActionConstaciasSort;
use App\Activity;
use App\ActivityAction;
use App\ActivityActionDocument;
use App\Affiliate;
use App\Client;
use App\Mail\SendDocumentDataBase;
use App\PolicySort;
use App\Http\Controllers\Controller;
use App\Service;
use App\State;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use App\Http\Controllers\ActionController;
use App\Http\Controllers\Tables\MailBoardController;
use App\PolicySortCollection;
use DB;
use PDF;

class ConstancySortController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    
     private $type_cost;
    
     public function __construct()
    {
        $this->middleware('auth');
        $this->type_cost = [
            'policy_certification_up_to_date' => 'Certificado de póliza al día',
            'outstanding_sums_to_be_paid' => 'Constancia de sumas pendientes por pagar',
            'employee_account_statement' => 'Estado de cuenta del trabajador',
            'premiums_paid_certificate' => 'Constancia de primas pagadas',
        ];
    }


    public function form(Request $req, $cpath, $id)
    {
        //Buscamos el cliente
        $client   = Client::where('path', $cpath)->first();

        //Buscamos la actividad de la constancia por su activity_id
        $activity = Activity::where('client_id', $client->id)
            ->where('id', $id)
            ->first();

        //Buscamos la constancia por activity_id
        $constacySort = ConstancySort::where('activity_id', $activity->id)
            ->first();

        //Buscamos la poliza con el parent_id de la constancia
        $policySort = PolicySort::where('activity_id', $activity->parent_id)
            ->first();
          
        //Json de actividades economica
        $jsonSource = ($policySort->economic_activity == 'public') ? '/js/economic_activity/public.json' : '/js/economic_activity/private.json';
        $json = file_get_contents(public_path($jsonSource));
        $economicActivities = json_decode($json, true);

        //Se transforma a una collección en laravel
        $activity_economic_name = collect($economicActivities)->firstWhere('CODE', $policySort->activity_economic_id)['ACTIVITY_NAME'];
        
        $policySort->economic_activity_name = $activity_economic_name;

        //Buscamos la poliza
        $activityPolicy = Activity::where('client_id', $client->id)
            ->where('id', $activity->parent_id)
            ->first();

        // Obtener las actividades de pagos
        $paymentActivity = Activity::where('parent_id', $activity->parent_id)->get();

        // Extraer los ids de esas actividades
        $activityIds = $paymentActivity->pluck('id')->toArray(); 

        // Sumar la columna total_amount de los registros cuyo activity_id esté en $activityIds
        $totalAmount = PolicySortCollection::whereIn('activity_id', $activityIds)
            ->sum('total_amount');

        // Obtener la fecha de creación más antigua
        $oldestCreatedAt = PolicySortCollection::whereIn('activity_id', $activityIds)
            ->min('created_at');

        // Obtener la fecha de creación más reciente
        $newestCreatedAt = PolicySortCollection::whereIn('activity_id', $activityIds)
            ->max('created_at');

        $tipoConstancias = [
            'policy_certification_up_to_date' => 'Certificación de póliza al día',
            'employee_account_statement' => 'Estado de cuenta del trabajador',
            'outstanding_sums_to_be_paid' => 'Constancia de sumas pendientes a pagar',
            'premiums_paid_certificate' => 'Constancia de primas pagadas'
        ];

        $estadoPlanilla = [
            '1' => 'Reportada',
            '2' => 'Pendiente de pago'
        ];

        return view('services.constancy_sort.form',
                        [
                            'activity'          => $activity,
                            'tipoConstancias'   => $tipoConstancias ,
                            'estadoPlanilla'    => $estadoPlanilla,
                            'constacySort'      => $constacySort,
                            'policySort'        => $policySort,
                            'activityPolicy'    => $activityPolicy,
                            'totalAmount'       => $totalAmount,
                            'oldestCreatedAt'   => $oldestCreatedAt,
                            'newestCreatedAt'   => $newestCreatedAt
                        ]
                    );
    }

    public function resendEmail(Request $req)
    {

        $files = array();
        $paths = array();
        $subject = false;
        $text = '';
        $document = 'base';

        //variables de acciones y servicios
        $activity = 1;
        $activity_service = 1;
        $activityAction = 1;

        //correo a enviar (Tomador)
        $emails = '<EMAIL>';

        //convirtiendo variables
        $activity_service_id = (object)[];
        $service_id = array("service_id" => 1);
        $activity_service_id->service_id = $service_id['service_id'];

        $client_id = (object)[];
        $client = array("id" => 1);
        $client_id->id = $client['id'];


        //generando pdf y subiendo a s3
        // $pdf = ActionController::generatePDF($activity_service_id, $document, false);

        $variations = DB::table('variations_sort')->where('id', '=', 1)->first();
        $pdf = PDF::loadView("services.plantilla.docs.{$document}_pdf", ['variations' => $variations, 'watermark' => false]);

        Storage::disk('s3')
            ->put("activity_action_document/{$document}_{$activityAction}.pdf", $pdf->output());

        //guardamos en activity_action_documents
        $activityActionDocument = new ActivityActionDocument();
        $activityActionDocument->activity_action_id = $activityAction;
        $activityActionDocument->name = $document;
        $activityActionDocument->path = "activity_action_document/{$document}_{$activityAction}.pdf";
        $activityActionDocument->save();

        //asunto
        $subject = 'prueba mas';

        //formamos archivo
        $files[] = [
            'type' => 'pdf',
            'path' => "activity_action_document/{$document}_{$activityAction}.pdf",
            'name' => $document . '.pdf',
        ];

        //dirección path
        $paths[] = "activity_action_document/{$document}_{$activityAction}.pdf";

        //Logica para envio de correo
        if ($emails != null) {
            $emails = array_map('trim', explode(',', $emails));

            try {
                $affiliate_files = [];
                foreach ($files as $file) {
                    if (!(strpos($file['path'], 'affiliate_employer') !== false)) {
                        $affiliate_files[] = $file;
                    }
                }

                $mailSent = new SendDocumentDataBase(
                    implode(',', $emails),
                    $subject,
                    "<EMAIL>",
                    "Prueba",
                    [
                        "text" => $text,
                        "sender" => 'Prueba'
                    ],
                    "<EMAIL>",
                    $files,
                    "send_document_db",
                    $client_id,
                    $req->getHost(),
                    $activity,
                    $activityAction,
                    $activity_service
                );
                $mailSent->sendMail();
                
                if ($mailSent) {
                    return response()->json(['response' => true, 'message' => 'Email enviado correctamente']);
                } else {
                    return response()->json(['response' => false, 'message' => 'No se pudo enviar el correo']);
                }
            } catch (Exception $e) {
                Log::error($e->getMessage());
                dd($e);
            }
        }
    }
     //Guardar servicio de VARIACIONES SORT
     public function save(Request $req, $cpath, $id)
     {
         $client = Client::where('path', $cpath)->firstOrFail();
 
         //Buscamos la actividad asociada que viene por parametro
         $activity = Activity::where('client_id', $client->id)
             ->where('id', $id)
             ->firstOrFail();
 
         \DB::beginTransaction();
         try {
             if (!$activity->variation) {
                 $variation = new VariationsSort();
                 $variation->activity_id = $id;
             } else {
                 $variation = $activity->variation;
             }
             $variation->save();
             \DB::commit();
         } catch (\Exception $e) {
             \DB::rollback();
         }
 
         return redirect("servicio/$id/constacy_sort");
     }

    public function pdf(Request $req, $cpath, $id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();

        $activity = Activity::where('client_id', $client->id)
            ->where('id', $id)->firstOrFail();

        $quotation = new VariationsSort();
        $quotation->activity_id = $id;
        $quotation->setRelation('activity', $activity);
        $quotation->created_at = new \DateTime();

        $pdf = \PDF::loadView('services.quotation.docs.quotation_pdf', [
            'quotation' => $quotation,
            'watermark' => false,
            'activity' => $activity
        ]);

        return $pdf->stream('preview.pdf');
    }

    private function registerActivityAction($id, $action_id, $old_state_id, $new_state_id, $description, $user_id,$author_id)
    {
        $newActivityAction = new ActivityAction();
        $newActivityAction->activity_id  = $id;
        $newActivityAction->action_id    = $action_id;
        $newActivityAction->old_state_id = $old_state_id;
        $newActivityAction->new_state_id = $new_state_id;
        $newActivityAction->description  = $description;
        $newActivityAction->old_user_id  = $user_id;
        $newActivityAction->new_user_id  = $user_id;
        $newActivityAction->author_id    = $author_id;
        $newActivityAction->save();

        return $newActivityAction;
    }

    public function generateFilingSupport($variation){

        //Generamos el documento SOPORTE RADICADO desde la vista deseada
        $pdf = PDF::loadView('services/ruta_por_definir/docs/SOPORTE_RADICADO' . '_pdf', ['watermark' => false]);
        $fileName = "activity_action_document/{$variation->document_name}_{$variation->id}.pdf";
        
        //Subimos el archivo a S3 relacionado a la actividad
        Storage::disk('s3')->put($fileName, $pdf->output());

        $path = $fileName;

        //creamos la actividad pero en action document
        $activityActionDocument = new ActivityActionDocument;
        $activityActionDocument->activity_action_id = $variation->id;
        $activityActionDocument->name = $variation->document_name;
        $activityActionDocument->path = $path;
        $activityActionDocument->save();
    }
    /**
     * Generar estado de cuenta del trabajor
     *
     * @param Request $req
     */
    public function generatAccountStatement(Request $req,$cpath,$id)
    {

        $client = Client::where('path', $cpath)->firstOrFail();

        DB::beginTransaction();
        try {
            //Generar Actividad
            $activity = new Activity();
            $activity->client_id = $client->id;
            $activity->affiliate_id = $id;
            $activity->service_id = Service::SERVICE_CONSTANCY_SORT_MNK;
            $activity->state_id = State::REGISTRADO;
            $activity->user_id = Auth::user()->id;
            $activity->save();

            //Registrar historial acción de actividad
            $activityAction = $this->registerActivityAction($activity->id, Action::GENERAR_ESTADO_CUENTA_TRABAJADOR, State::REGISTRADO,
                State::REGISTRADO,'Estado de cuenta del trabajador', $activity->user_id, Auth::user()->id);

            //Generar Constancia
            //Guardar información de la factura para el pago trimestral
            $constancy = new ConstancySort();
            $constancy->activity_id = $activity->id;
            $constancy->type = 'employee_account_statement';
            $constancy->save();


            $pdf = PDF::loadView('services.constancy_sort.docs.account_statement', [
                'activity' => $activity,
                'watermark' => false,
            ]);

            $filePath = "documents/constancy_sort_{$activity->id}.pdf";
            Storage::disk('s3')->put($filePath, $pdf->output());

            $affiliate = Affiliate::where('id', $activity->affiliate_id)->first();
            $emails = [$affiliate->email];
            $text = [
                "text" => "Estimado cliente, adjunto encontrará Estado de cuenta.",
                "sender" => 'MNK Seguros'
            ];
            $attachments = [
                [
                    'path' => $filePath,
                    'name' => basename($filePath),
                    'type' => 'PDF'
                ]
            ];

            $mailSent = new SendDocumentDataBase(
                implode(',', $emails),
                "Estado de cuenta del trabajador",
                "<EMAIL>",
                "Estado de cuenta del trabajador",
                $text,
                "<EMAIL>",
                $attachments,
                "send_document_db",
                $client,
                request()->getHost(),
                $activity->id,
                $activityAction->id,
                $activity->service->id
            );

            // Capturar el resultado del envío
            $result = $mailSent->sendMail();

            //Registramos los datos del correo enviado para la trazabilidad
            $mailBoardController = new MailBoardController();
            $mailBoardController->createRegisterMail(
                $activity->id,
                $activity->service->id, 
                $activity->parent->policy_sort->consecutive, 
                'Asegurado', 
                mb_convert_case(mb_strtolower($activity->affiliate->full_name ?? ''), MB_CASE_TITLE, "UTF-8"), 
                $activity->affiliate->doc_number, 
                'Estado de cuenta del trabajado', 
                $text,
                $emails, 
                $result,
                $attachments
            );

            DB::commit();

        }catch (Exception $e) {
            DB::rollback();
        }
        return response()->json([
            'status'  => 'success',
            'message' => 'Campos han sido actualizados correctamente'
        ]);
    }

    public function premiumsPaid(Request $req,$cpath,$id)
    {

        $client = Client::where('path', $cpath)->firstOrFail();

        DB::beginTransaction();
        try {
            //Generar Actividad
            $activity = new Activity();
            $activity->client_id = $client->id;
            $activity->affiliate_id = $id;
            $activity->service_id = Service::SERVICE_CONSTANCY_SORT_MNK;
            $activity->state_id = State::CONSTANCIA_GENERADA;
            $activity->user_id = Auth::user()->id;
            $activity->save();

            //Registrar historial acción de actividad
            $activityAction = $this->registerActivityAction($activity->id, Action::GENERAR_CONSTANCIA_DE_PRIMAS_PAGADAS, State::REGISTRADO,
                State::CONSTANCIA_GENERADA,'Constancia de primas pagadas', $activity->user_id, Auth::user()->id);

            //Generar Constancia
            //Guardar información de la factura para el pago trimestral
            $constancy = new ConstancySort();
            $constancy->activity_id = $activity->id;
            $constancy->type = 'premiums_paid_certificate';
            $constancy->save();


            $pdf = PDF::loadView('services.constancy_sort.docs.premium_paid', [
                'activity' => $activity,
                'watermark' => false,
            ]);

            $filePath = "documents/premium_paid_{$activity->id}.pdf";
            Storage::disk('s3')->put($filePath, $pdf->output());

            $affiliate = Affiliate::where('id', $activity->affiliate_id)->first();
            $emails = [$affiliate->email];
            $text = [
                "text" => "Estimado cliente, adjunto encontrará la Constancia de Prima Generada.",
                "sender" => 'MNK Seguros'
            ];
            $attachments = [
                [
                    'path' => $filePath,
                    'name' => basename($filePath),
                    'type' => 'PDF'
                ]
            ];

            $mailSent = new SendDocumentDataBase(
                implode(',', $emails),
                "Constancia de Prima Generada",
                "<EMAIL>",
                "Constancia de Prima Generada",
                $text,
                "<EMAIL>",
                $attachments,
                "send_document_db",
                $client,
                request()->getHost(),
                $activity->id,
                $activityAction->id,
                $activity->service->id
            );
            
            // Capturar el resultado del envío
            $result = $mailSent->sendMail();

            //Registramos los datos del correo enviado para la trazabilidad
            $mailBoardController = new MailBoardController();
            $mailBoardController->createRegisterMail(
                $activity->id,
                $activity->service->id, 
                $activity->parent->policy_sort->consecutive, 
                'Asegurado', 
                mb_convert_case(mb_strtolower($activity->affiliate->full_name ?? ''), MB_CASE_TITLE, "UTF-8"), 
                $activity->affiliate->doc_number, 
                'Constancia de Prima Generad', 
                $text,
                $emails, 
                $result,
                $attachments
            );

            DB::commit();

        }catch (Exception $e) {
            DB::rollback();
        }
        return response()->json([
            'status'  => 'success',
            'message' => 'Campos han sido actualizados correctamente'
        ]);
    }


    public function email(Request $req, $cpath, $id)
    {
        $policy_sort = PolicySort::where('id', $id)->firstOrFail();
        $client = Client::where('path', $cpath)->firstOrFail();

        DB::beginTransaction();
        try {

            //Registrar historial acción de actividad
            $activityAction = $this->registerActivityAction($id, Action::GENERAR_ESTADO_CUENTA_TRABAJADOR, State::REGISTRADO,
                State::CONSTANCIA_GENERADA,'Generar certificación de poliza al día', $policy_sort->activity->user_id, Auth::user()->id);

            //Guardar información de la factura para el pago trimestral
            $constancy = new ConstancySort();
            $constancy->activity_id = $id;
            $constancy->save();

            $pdf = PDF::loadView('services.constancy_sort.docs.constancy_sort_pdf', [
                'activity' => $constancy,
                'watermark' => false,
            ]);

            $filePath = "documents/constancy_sort_{$constancy->id}.pdf";
            Storage::disk('s3')->put($filePath, $pdf->output());

            $affiliate = Affiliate::where('id', $policy_sort->activity->affiliate_id)->first();
            $emails = [$affiliate->email];
            $text = [
                "text" => "Estimado cliente, adjunto encontrará Estado de cuenta.",
                "sender" => 'MNK Seguros'
            ];
            $attachments = [
                [
                    'path' => $filePath,
                    'name' => basename($filePath),
                    'type' => 'PDF'
                ]
            ];

            $mailSent = new SendDocumentDataBase(
                implode(',', $emails),
                "Estado de cuenta del trabajador",
                "<EMAIL>",
                "Estado de cuenta del trabajador",
                $text,
                "<EMAIL>",
                $attachments,
                "send_document_db",
                $client,
                request()->getHost(),
                $id,
                $activityAction->id,
                $policy_sort->activity->service->id
            );
            
             // Capturar el resultado del envío
            $result = $mailSent->sendMail();

            //Registramos los datos del correo enviado para la trazabilidad
            $mailBoardController = new MailBoardController();
            $mailBoardController->createRegisterMail(
                $constancy->activity->id,
                $constancy->activity->service->id, 
                $constancy->activity->parent->policy_sort->consecutive, 
                'Asegurado', 
                mb_convert_case(mb_strtolower($constancy->activity->affiliate->full_name ?? ''), MB_CASE_TITLE, "UTF-8"), 
                $constancy->activity->affiliate->doc_number, 
                'Estado de cuenta del trabajador', 
                $text,
                $emails, 
                $result,
                $attachments
            );

            DB::commit();

        }catch (Exception $e) {
            DB::rollback();
        }
        return response()->json([
            'status'  => 'success',
            'message' => 'Registro creado y email enviado exitosamente'
        ]);

    }

    public function handleEmail(Request $req, $cpath, $id, $type)
    {

        // Buscamos la póliza por su activity_id
        $policy_sort = PolicySort::where('id', $id)->first();
        $activity_collection = Activity::where('parent_id', $policy_sort->activity_id)
            ->where('service_id', Service::SERVICE_POLICY_SORT_COLLECTION_MNK)
            ->get();
        $policy_sort_collection = PolicySortCollection::whereIn('activity_id', $activity_collection->pluck('id'))
            ->where('payment_status' , PolicySortCollection::PAYMENT_STATUS_APPROVED)
            ->get();
        // Obtenemos el cliente
        $client = Client::where('path', $cpath)->first();

        // Buscamos el afiliado por la póliza
        $affiliate = Affiliate::where('id', $policy_sort->activity->affiliate_id)->first();

        $today= Carbon::now();

        // Obtener cédula solo si se recibe en la solicitud y el tipo es 'employee_account_statement'
        $doc_number = ($type === 'employee_account_statement') ? $req->input('doc_number') : null;
        $policy_sort_employee= null;
        $gis_employee= null;
        $peIp_employee= null;
        $peIt_employee= null;
        $affiliate_employee = null;
        if ($doc_number){
            $affiliate_employee = \DB::table('activities')
                ->join('policy_spreadsheets', 'policy_spreadsheets.activity_id', '=', 'activities.id')
                ->join('policy_spreadsheet_affiliates', 'policy_spreadsheet_affiliates.policy_spreadsheet_id', '=', 'policy_spreadsheets.id')
                ->where('activities.parent_id', $policy_sort->activity_id)
                ->where('activities.service_id', 79)
                ->where('policy_spreadsheet_affiliates.identification_number', $doc_number)
                ->orderBy('activities.created_at', 'desc')
                ->select('policy_spreadsheet_affiliates.*') // Selecciona todos los campos de policy_spreadsheet_affiliates
                ->first();
            if (!$affiliate_employee){
                return response()->json([
                    'status' => 'error',
                    'message' => 'No se encontró un afiliado con la cédula proporcionada.'
                ], 404);
            }
            $activityPolicy = Activity::where('affiliate_id',$affiliate_employee->affiliate_id)
                ->get();
            foreach ($activityPolicy as $policy) {

                if ($policy->service_id === Service::SERVICE_AFFILIATE_WORKFORCE_REPORT_MNK) {
                    $activityPolicy = Activity::where('id',$policy->parent_id)->first();
                    $policySort =  PolicySort::where('activity_id', $activityPolicy->parent_id)->first();
                    $policy_sort_employee = $policySort;
                }elseif($policy->service_id === Service::SERVICE_GIS_SORT_MNK){
                    $gis= GisSort::where('activity_id', $policy->id)->first();
                    $gis_employee = $gis;
                }elseif($policy->service_id === Service::SERVICE_PE_IP_SORT_MNK){
                    $peIp= PeIpSort::where('activity_id', $policy->id)->first();
                    $peIp_employee = $peIp;
                }elseif($policy->service_id === Service::SERVICE_PE_IT_SORT_MNK){
                    $peIt= PeItSort::where('activity_id', $policy->id)->first();
                    $peIt_employee = $peIt;
                }

            }
        }

        DB::beginTransaction();

        try {
            // Constantes de CONSTANCIAS SORT para las acciones
            $constanciasSortClass = Action::CONSTANCIAS_SORT;
            $subjectPart2 = '';
            $subjectPart3 = '';

            // Json de actividades económicas
            $jsonSource = ($policy_sort->economic_activity == 'public') ? '/js/economic_activity/public.json' : '/js/economic_activity/private.json';
            $json = file_get_contents(public_path($jsonSource));
            $economicActivities = json_decode($json, true);

            $activity_economic_name = collect($economicActivities)->firstWhere('CODE', $policy_sort->activity_economic_id)['ACTIVITY_NAME'];
            $policy_sort->economic_activity_name = $activity_economic_name;

            // Asignar tipo de documento a partir de la constancia seleccionada
            switch ($type) {
                case 'policy_certification_up_to_date':
                    $typeText = 'el certificado solicitado';
                    $subjectPart1 = 'certificado';
                    $subjectPart2 .= 'Certificado de póliza al día';
                    $actionId = $constanciasSortClass::GENERAR_CERTIFICACION_POLIZA;
                    $documentType = 'Certificación de Póliza';
                    $view = 'services.constancy_sort.docs.certification_policy_pdf';
                    break;

                case 'outstanding_sums_to_be_paid':
                    $typeText = 'la constancia solicitada';
                    $subjectPart1 = 'constancia';
                    $subjectPart2 .= 'Constancia de primas pendientes por pagar';
                    $actionId = $constanciasSortClass::GENERAR_CONSTANCIA_SUMAS_PENDIENTES_PAGAR;
                    $documentType = 'Constancia de primas Pendientes';
                    $view = 'services.constancy_sort.docs.certification_outstanding_premiums_pdf';
                    break;

                case 'employee_account_statement':
                    $typeText = 'la constancia solicitada';
                    $subjectPart1 = 'constancia';
                    $subjectPart2 .= 'Estado de cuenta del trabajador';
                    $actionId = $constanciasSortClass::GENERAR_ESTADO_CUENTA_TRABAJADOR;
                    $documentType = 'Estado de Cuenta del Trabajador';
                    $view = 'services.constancy_sort.docs.account_statement_certificate_pdf';
                    break;

                case 'premiums_paid_certificate':
                    $typeText = 'la constancia solicitada';
                    $subjectPart1 = 'constancia';
                    $subjectPart2 .= 'Constancia de primas pagadas';
                    $actionId = $constanciasSortClass::GENERAR_CONSTANCIA_PRIMAS_PAGADAS;
                    $documentType = 'Constancia de Primas Pagadas';
                    $view = 'services.constancy_sort.docs.premiums_paid_certificate_pdf';
                    break;

                default:
                    throw new Exception("Tipo de constancia no válido.");
            }

            // Crear la nueva actividad del servicio CONSTANCIAS SORT y asociarla
            $activityNew = new Activity();
            $activityNew->client_id = $client->id;
            $activityNew->affiliate_id = $affiliate->id;
            $activityNew->service_id = Service::SERVICE_CONSTANCY_SORT_MNK;
            $activityNew->state_id = State::REGISTRADO;
            $activityNew->parent_id = $policy_sort->activity_id;
            $activityNew->user_id = Auth::id();
            $activityNew->save();

            // Crear la Constancia SORT
            $constancy = new ConstancySort();
            $constancy->activity_id = $activityNew->id;
            $constancy->type = $type;
            $constancy->save();

            // Crear la activity actions con la acción correspondiente
            $activityAction = ActionController::create($activityNew->id, $actionId,  $this->type_cost[$type]);
            $policySortController = new PolicySortController();
            $ubication = $policySortController->getLocationNamesFromJson($affiliate->province, $affiliate->canton, $affiliate->district);
            $policy_ubication = ($ubication['province'] ?? '') . ', ' . ($ubication['canton'] ?? '') . ', ' . ($ubication['district'] ?? '');

            // Preparar el documento PDF
            $pdf = PDF::loadView($view, [
                'policy_sort' => $policy_sort,
                'activity' => $constancy,
                'watermark' => false,
                'affiliate_employee' => $affiliate_employee,
                'today' => $today,
                'policy_sort_employee' =>$policy_sort_employee,
                'gis_employee' => $gis_employee,
                'peIp_employee' => $peIp_employee,
                'peIt_employee' => $peIt_employee,
                'policy_ubication' => $policy_ubication,
                'policy_sort_collection' => $policy_sort_collection
            ]);

            // Generar el nombre del archivo
            $document = $this->type_cost[$type];
            $fileName = "{$documentType}_{$constancy->id}.pdf";

            // Guardar el archivo en S3
            $filePath = "activity_action_document/{$fileName}";
            Storage::disk('s3')->put($filePath, $pdf->output());

            // Guardar el path del documento en la base de datos
            $activityActionDocument = new ActivityActionDocument();
            $activityActionDocument->activity_action_id = $activityAction->id;
            $activityActionDocument->name = $document;
            $activityActionDocument->path = $filePath;
            $activityActionDocument->save();

            $emailIntermediary = $policy_sort->email;
            $emailTaker = $affiliate->email;
            $nombre = mb_convert_case(mb_strtolower($affiliate->first_name ?? ''), MB_CASE_TITLE, "UTF-8");

            $emails = array_filter([$emailIntermediary, $emailTaker], function ($email) {
                return !empty($email);
            });

            $emailData = TemplateBuilder::build(
                Templates::REQUESTED_CERTIFICATE_OR_PROOF,
                [
                    'subject_part_one' => $subjectPart1,
                    'policy_sort' => $policy_sort->formatSortNumber(),
                    'subject_part_two' => $subjectPart2,
                    'name' => $nombre,
                    'type_text' => $typeText,
                ]
            );

            $text = [
                "text" => $emailData['body'],
                "sender" => $emailData['sender']
            ];
            $attachments = [
                [
                    'path' => $filePath,
                    'name' => basename($filePath),
                    'type' => 'PDF'
                ]
            ];

            $this->sendEmail($emails, $emailData['subject'], $text, $attachments, $client, $policy_sort->activity_id, $activityNew, $policy_sort, $nombre);

            DB::commit();

        } catch (Exception $e) {
            DB::rollback();
            return response()->json([
                'status' => 'error',
                'message' => 'Ocurrió un error al enviar el correo.'
            ], 500);
        }

        return response()->json([
            'status' => 'success',
            'message' => 'Registro creado y email enviado exitosamente'
        ]);
    }

    /** Calcula los pagos pendientes de las polizas
     * @param $activity_policy
     * @return mixed
     */
    public function planillasPendientes($activity_policy){

        $consultaPlanillaEmision = "SELECT           
                        pp.total_salaries as valor_planilla
                    FROM policy_spreadsheets pp 
                    LEFT JOIN activities a ON a.id=pp.activity_id       
                    where a.parent_id = $activity_policy 
                    ORDER BY pp.id asc
                    limit 1";

        $planillaEmision = DB::selectOne($consultaPlanillaEmision);


        $consultaTwo = "SELECT            
                            sum(pp.total_salaries) as total_planillas,
                            count(*) as cat_planillas
                        FROM policy_spreadsheets pp 
                        LEFT JOIN activities a ON a.id=pp.activity_id
                        where a.parent_id = $activity_policy
                        ";

        $totalPlanillas = DB::select($consultaTwo);

        $valorPlanillasAnteriores = $totalPlanillas[0]->total_planillas ?? 0;
        $catPlanillas = $totalPlanillas[0]->cat_planillas ?? 0;
        $cantPlanillasPendientes = 12-$catPlanillas;

        $consultaUltimaPlanilla = "SELECT           
                        pp.total_salaries as valor_ult_planilla
                    FROM policy_spreadsheets pp 
                    LEFT JOIN activities a ON a.id=pp.activity_id       
                    where a.parent_id = $activity_policy 
                    ORDER BY pp.id desc
                    limit 1";

        $valorUltimaPlanilla = DB::selectOne($consultaUltimaPlanilla);

        $consultaTemp = "select 
                        case
                            when p.periodicity = '1' then p.anual_percentage
                            when p.periodicity = '2' then p.semestral_percentage
                            when p.periodicity = '3' then p.trimestral_percentage
                            when p.periodicity = '4' then p.mensual_percentage
                            else p.unico_percentage 
                        end AS temp,
                        p.periodicity
                    from policy_sorts p
                    where p.activity_id  = $activity_policy
                    ";

        $queryTemp = DB::select($consultaTemp);
        $temp = (float) str_replace(",", ".", $queryTemp[0]->temp);

        //solo la omite si tiene mas planilla
        $valorPlanillasAnteriores = $valorPlanillasAnteriores == $planillaEmision->valor_planilla ? $valorPlanillasAnteriores : ($valorPlanillasAnteriores - $planillaEmision->valor_planilla);
        $cantPlanillasPendientes = $valorPlanillasAnteriores == $planillaEmision->valor_planilla ? $cantPlanillasPendientes : ($cantPlanillasPendientes+1);

        $ps = ($valorPlanillasAnteriores) + ($cantPlanillasPendientes*$valorUltimaPlanilla->valor_ult_planilla);
        $psEmision = $planillaEmision->valor_planilla*12;
        $valorPrima = $ps*($temp/100 ?? 0);
        $valorPrimaEmision = $psEmision*($temp/100 ?? 0);
        $valorPagoEmision = 0;
        $valorPagoPendientes = 0;
        $valorPago = 0;

        if($queryTemp[0]->periodicity == 2){ //Semestral
            $valorPagoPendientes = $valorPrima/2;
            $valorPagoEmision = $valorPrimaEmision/2;
        } else if($queryTemp[0]->periodicity == 3){ //Trimestral
            $valorPagoPendientes = $valorPrima/4;
            $valorPagoEmision = $valorPrimaEmision/4;
        } else if($queryTemp[0]->periodicity == 4){ //Mensual
            $valorPagoPendientes = $valorPrima/12;
            $valorPagoEmision = $valorPrimaEmision/12;
        }

        if($valorPagoPendientes < $valorPagoEmision){
            $valorPago = $valorPagoEmision;
        }
        else{
            $valorPago = $valorPagoPendientes;
        }

        $consultapagos = " select 
                            p.fecha_ini_recibo as fechaIni,
                            p.fecha_fin_recibo as fechaFin,
                            '$valorPago' as valor
                        from report_pending_premiums_view p
                        where p.estado COLLATE utf8mb4_0900_ai_ci ='pendiente' and p.activity_policy = '$activity_policy'  
                        ";

        $queryPagosPend = DB::select($consultapagos);
        return $queryPagosPend;

    }

    public function downloadDocument(Request $req, $cpath, $id, $type)
    {

        // Buscamos la póliza por su activity_id
        $policy_sort = PolicySort::where('id', $id)->first();
        $activity_collection = Activity::where('parent_id', $policy_sort->activity_id)
            ->where('service_id', Service::SERVICE_POLICY_SORT_COLLECTION_MNK)
            ->get();
        $policy_sort_collection = PolicySortCollection::whereIn('activity_id', $activity_collection->pluck('id'))
            ->where('payment_status' , PolicySortCollection::PAYMENT_STATUS_APPROVED)
            ->get();

        // Obtenemos el cliente
        $client = Client::where('path', $cpath)->first();

        // Buscamos el afiliado por la póliza
        $affiliate = Affiliate::where('id', $policy_sort->activity->affiliate_id)->first();
        $today= Carbon::now();

        // Obtener cédula solo si se recibe en la solicitud y el tipo es 'employee_account_statement'
        $doc_number = ($type === 'employee_account_statement') ? $req->input('doc_number') : null;
        $policy_sort_employee= null;
        $gis_employee= null;
        $peIp_employee= null;
        $peIt_employee= null;
        $affiliate_employee = null;
        $affiliateCert = null;

        if ($doc_number){

            $affiliate_employee = \DB::table('activities')
                ->join('policy_spreadsheets', 'policy_spreadsheets.activity_id', '=', 'activities.id')
                ->join('policy_spreadsheet_affiliates', 'policy_spreadsheet_affiliates.policy_spreadsheet_id', '=', 'policy_spreadsheets.id')
                ->where('activities.parent_id', $policy_sort->activity_id)
                ->where('activities.service_id', 79)
                ->where('policy_spreadsheet_affiliates.identification_number', $doc_number)
                ->orderBy('activities.created_at', 'desc')
                ->select('policy_spreadsheet_affiliates.*') // Selecciona todos los campos de policy_spreadsheet_affiliates
                ->first();

            $affiliateCert = Affiliate::where('doc_number', $doc_number)->first();

            if (!$affiliate_employee){
                return response()->json([
                    'status' => 'error',
                    'message' => 'No se encontró un afiliado con la cédula proporcionada.'
                ], 404);
            }
            $activityPolicy = Activity::where('affiliate_id',$affiliate_employee->affiliate_id)
                ->get();
            foreach ($activityPolicy as $policy) {

                if ($policy->service_id === Service::SERVICE_AFFILIATE_WORKFORCE_REPORT_MNK) {
                    $activityPolicy = Activity::where('id',$policy->parent_id)->first();
                    $policySort =  PolicySort::where('activity_id', $activityPolicy->parent_id)->first();
                    $policy_sort_employee = $policySort;
                }elseif($policy->service_id === Service::SERVICE_GIS_SORT_MNK){
                    $gis= GisSort::where('activity_id', $policy->id)->first();
                    $gis_employee = $gis;
                }elseif($policy->service_id === Service::SERVICE_PE_IP_SORT_MNK){
                    $peIp= PeIpSort::where('activity_id', $policy->id)->first();
                    $peIp_employee = $peIp;
                }elseif($policy->service_id === Service::SERVICE_PE_IT_SORT_MNK){
                    $peIt= PeItSort::where('activity_id', $policy->id)->first();
                    $peIt_employee = $peIt;
                }

            }
        }

        // Json de actividades económicas
        $jsonSource = ($policy_sort->economic_activity == 'public') ? '/js/economic_activity/public.json' : '/js/economic_activity/private.json';
        $json = file_get_contents(public_path($jsonSource));
        $economicActivities = json_decode($json, true);

        $activity_economic_name = collect($economicActivities)->firstWhere('CODE', $policy_sort->activity_economic_id)['ACTIVITY_NAME'];
        $policy_sort->economic_activity_name = $activity_economic_name;

        DB::beginTransaction();

        try {
            // Constantes de CONSTANCIAS SORT para las acciones
            $constanciasSortClass = Action::CONSTANCIAS_SORT;

            $totalAsiento = 0;

            switch ($type) {
                case 'policy_certification_up_to_date':
                    $actionId = $constanciasSortClass::GENERAR_CERTIFICACION_POLIZA;
                    $documentType = 'Certificación de Póliza';
                    $view = 'services.constancy_sort.docs.certification_policy_pdf';
                    break;

                case 'outstanding_sums_to_be_paid':
                    $actionId = $constanciasSortClass::GENERAR_CONSTANCIA_SUMAS_PENDIENTES_PAGAR;
                    $documentType = 'Constancia de Sumas Pendientes';
                    $view = 'services.constancy_sort.docs.certification_outstanding_premiums_pdf';
                    $pay_pending = $this->planillasPendientes($policy_sort->activity_id);

                    break;

                case 'employee_account_statement':
                    $actionId = $constanciasSortClass::GENERAR_ESTADO_CUENTA_TRABAJADOR;
                    $documentType = 'Estado de Cuenta del Trabajador';
                    $view = 'services.constancy_sort.docs.account_statement_certificate_pdf';

                    $totalAsiento = \DB::table('accounting_entries as ac')
                        ->leftJoin('gis_sort as g', 'g.activity_id', '=', 'ac.activity_gis')
                        ->leftJoin('activities as a', 'a.id', '=', 'g.activity_id')
                        ->leftJoin('affiliates as af', 'af.id', '=', 'a.affiliate_id')
                        ->where('ac.cod_oper', '011')
                        ->where('ac.debit', '>', 0)
                        ->where('af.doc_number', $doc_number)
                        ->sum('ac.debit');
                    break;

                case 'premiums_paid_certificate':
                    $actionId = $constanciasSortClass::GENERAR_CONSTANCIA_PRIMAS_PAGADAS;
                    $documentType = 'Constancia de Primas Pagadas';
                    $view = 'services.constancy_sort.docs.premiums_paid_certificate_pdf';
                    break;

                default:
                    throw new Exception("Tipo de constancia no válido.");
            }

            // Crear la nueva actividad del servicio CONSTANCIAS SORT y asociarla
            $activityNew = new Activity();
            $activityNew->client_id = $client->id;
            $activityNew->affiliate_id = $affiliate->id;
            $activityNew->service_id = Service::SERVICE_CONSTANCY_SORT_MNK;
            $activityNew->state_id = State::REGISTRADO;
            $activityNew->parent_id = $policy_sort->activity_id;
            $activityNew->user_id = Auth::id();
            $activityNew->save();

            // Crear la Constancia SORT
            $constancy = new ConstancySort();
            $constancy->activity_id = $activityNew->id;
            $constancy->type = $type;
            $constancy->save();

            // Crear la actividad de acción correspondiente
            $activityAction = ActionController::create($activityNew->id, $actionId, $this->type_cost[$type]);
            ActionController::create($policy_sort->activity_id, Action::GENERAR_CONSTANCIA_DE_POLIZA, $this->type_cost[$type]);
            $policySortController = new PolicySortController();
            $ubication = $policySortController->getLocationNamesFromJson($affiliate->province, $affiliate->canton, $affiliate->district);
            $policy_ubication = ($ubication['province'] ?? '') . ', ' . ($ubication['canton'] ?? '') . ', ' . ($ubication['district'] ?? '');

            // Preparar el documento PDF
            $pdf = PDF::loadView($view, [
                'policy_sort' => $policy_sort,
                'activity' => $constancy,
                'watermark' => false,
                'affiliate_employee' => $affiliate_employee,
                'today' => $today,
                'policy_sort_employee' =>$policy_sort_employee,
                'gis_employee' => $gis_employee,
                'peIp_employee' => $peIp_employee,
                'peIt_employee' => $peIt_employee,
                'policy_ubication' => $policy_ubication,
                'policy_sort_collection' => $policy_sort_collection,
                'totalAsiento' => $totalAsiento,
                'affiliateCert' => $affiliateCert,
                'pay_pending' => $pay_pending ?? null
            ]);

            // Generar el nombre del archivo
            $fileName = "{$documentType}_{$constancy->id}.pdf";

            // Guardar el archivo en S3
            $filePath = "activity_action_document/{$fileName}";
            Storage::disk('s3')->put($filePath, $pdf->output());

            // Guardar el path del documento en la base de datos
            $activityActionDocument = new ActivityActionDocument();
            $activityActionDocument->activity_action_id = $activityAction->id;
            $activityActionDocument->name = $documentType;
            $activityActionDocument->path = $filePath;
            $activityActionDocument->save();

            // Confirmar la transacción
            DB::commit();

            // Enviar el PDF como respuesta para descarga
            return $pdf->stream($fileName);

        } catch (Exception $e) {
            DB::rollback();
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
            ], 500);
        }
    }


    private function sendEmail($emails, $subject, $text, $attachments, $client, $id, $activityAction, $policySort, $nombre)
    {

        $mailSent = new SendDocumentDataBase(
            implode(',', $emails),         // Correos a enviar
            $subject,                      // Asunto del correo
            "<EMAIL>",            // Remitente
            $subject,                      // Asunto
            $text,                         // Cuerpo del email
            "<EMAIL>",  // Email de respuesta
            $attachments,                  // Archivos adjuntos
            "send_document_db",            // Tipo de envío
            $client,                       // Información del cliente
            request()->getHost(),          // Dominio
            $id,                           // ID de la actividad
            $activityAction->id,           // ID de la acción de la actividad
            $policySort->activity->service->id // ID del servicio
        );

        // Capturar el resultado del envío
        $result = $mailSent->sendMail();

        //Registramos los datos del correo enviado para la trazabilidad
        $mailBoardController = new MailBoardController();
        $mailBoardController->createRegisterMail(
            $policySort->activity->id,
            Service::SERVICE_CONSTANCY_SORT_MNK, 
            $policySort->consecutive, 
            'Tomador', 
            $nombre, 
            $policySort->activity->affiliate->doc_number, 
            $subject, 
            $text,
            $emails, 
            $result,
            $attachments
        );
    }

}
