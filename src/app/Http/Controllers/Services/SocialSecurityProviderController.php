<?php

namespace App\Http\Controllers\Services;

use App\Actions\ActionSocialSecurityProvider;
use App\Activity;
use App\ActivityDocument;
use App\Area;
use App\Client;
use App\GisSort;
use App\Http\Controllers\ActionController;
use App\Http\Controllers\Controller;
use App\Http\Requests\Services\SocialSecurityProvider\storeOrdinaryInvoicesRequest;
use App\PolicySort;
use App\PssOrdinaryInvoice;
use App\Service;
use App\SocialSecurityProvider;
use App\States\StateSocialSecurityProvider;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Facades\Excel;
use PDF;
use PHPExcel_IOFactory;
use PHPExcel_Shared_Date;

class SocialSecurityProviderController extends Controller
{
    protected $invoiceTypes = [
        'temporary_disability' => 'Facturas por Incapacidad Temporal (Art. 73, Ley N° 9078)',
        'medical_services' => 'Facturas por Servicios Médicos',
        'ins' => 'Facturas Art. 65 y 73, Ley N° 9078',
    ];
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function form(Request $req, $cpath, $id)
    {

        $client = Client::where('path', $cpath)->firstOrFail();
        
        //Actividad de Cuenta Medica
        $socialSecurityProviderActivity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();

        //Datos de pss y sus relaciones
        $socialSecurityProvider = $socialSecurityProviderActivity->socialSecurityProvider;

        //Documentos subidos a esta actividad -> Zip de soporte de facturación
        $soportesDeDacturacionZip = ActivityDocument::where('activity_id', $socialSecurityProvider->activity_id)
            ->where('document_id', SocialSecurityProvider::SOPORTES_DE_FACTURACION_ZIP)->get();

        //Documentos subidos a esta actividad -> pdfs de soporte prefactura
        $facturacionOrdinariaExcel = ActivityDocument::where('activity_id', $socialSecurityProvider->activity_id)
            ->where('document_id', SocialSecurityProvider::FACTURACION_ORDINARIA_EXCEL)->get();

        return view('services.social_security_providers.form', [
            'activity' => $socialSecurityProviderActivity,
            'id' => $id,
            'socialSecurityProvider' => $socialSecurityProvider,
            'ordinaryInvoices' => $socialSecurityProvider->ordinaryInvoices,
            'ibanAccounts' => json_decode(optional($socialSecurityProvider->provider)->iban_accounts),
            'soportesDeDacturacionZip' => $soportesDeDacturacionZip,
            'facturacionOrdinariaExcel' => $facturacionOrdinariaExcel,
            'reportData' => $socialSecurityProvider->reportData ?? null,
        ]);
    }

    public function save(Request $req, $cpath, $id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();

        $socialSecurityProviderActivity = Activity::where('client_id', $client->id)
            ->where('id', $id)
            ->firstOrFail();

        if (!$socialSecurityProviderActivity) {
            $errors = "La actividad no existe";
            throw new \Exception($errors); // Lanzar excepción con el error
        }

        //Datos de cuentas medicas y sus relaciones
        $socialSecurityProvider = $socialSecurityProviderActivity->socialSecurityProvider;

        if (!$socialSecurityProvider) {
            $errors = "La actividad no tiene proveedor de seguridad social";
            throw new \Exception($errors); // Lanzar excepción con el error
        }
        //Iniciamos la transacción
        DB::beginTransaction();
        try {

            //Esto guarda los resultados de la prefactura por parte del analista
            if ($socialSecurityProviderActivity->state_id == StateSocialSecurityProvider::FACTURA_EN_REVISION_PSS) {
                $ordinaryInvoiceIds = $req->input('ordinary_invoice_ids');
                $results = $req->input('results');
                $preinvoice_reasons = $req->input('rejection_reason_preinvoice');

                foreach ($ordinaryInvoiceIds as $index => $ordinaryInvoiceId) {
                    $selectedResult = $results[$index];
        
                    $dataPreinvoice = PssOrdinaryInvoice::where('id', $ordinaryInvoiceId)->first();
        
                    if ($dataPreinvoice) { // Verifica si se encontró la pre factura
                        $dataPreinvoice->result = $selectedResult;

                        $reasonText = '';
                        if ($preinvoice_reasons[$index] == 'otros') {
                            $other_preinvoice_reasons = $req->input('rejection_reason_other');
                            $reasonText = $other_preinvoice_reasons[$index] ?? '';
                        } elseif (!empty($preinvoice_reasons[$index])) {
                            $reasonText = $preinvoice_reasons[$index];
                        }
        
                        // Guardar la razón de rechazo
                        $dataPreinvoice->reason = $selectedResult == 'aprobado' ? '' : $reasonText; // Asignar razón si está disponible
                        $dataPreinvoice->save();
                    } else {
                        $errors = 'No se encontró la factura ordinaria con ID ' . $ordinaryInvoiceId;
                        throw new \Exception($errors); // Lanzar excepción con el error
                    }
        
                }

                $unique = array_unique($results);
                if (count($unique) === 1) {
                    $resultPss = $unique[0]; // "aprobado" o "rechazado"
                } else {
                    $resultPss = "parcial";
                }

                $socialSecurityProvider->result = $resultPss;
                $socialSecurityProvider->save();

                if ($resultPss == 'aprobado') {
        
                    //Aprobar la factura
                    $action_id = ActionSocialSecurityProvider::APROBAR_FACTURA_PSS;
                    $activity_action = ActionController::create(
                        $socialSecurityProviderActivity->id,
                        $action_id,
                        'APROBAR FACTURA PSS'
                    );

                    // TODO: Asientos 011 será ejecutado por un cron a una hora determinada
                    //Reportar el pago
                    // TODO: Mover esto al cron despues del asiento 011
                    $action_id = ActionSocialSecurityProvider::REPORTAR_PAGO_REALIZADO_PSS;
                    $activity_action = ActionController::create(
                        $socialSecurityProviderActivity->id,
                        $action_id,
                        'REPORTAR PAGO REALIZADO PSS'
                    );
                }

                if ($resultPss == 'rechazado') {
                    $action_id = ActionSocialSecurityProvider::RECHAZAR_FACTURA_PSS;
                    $activity_action = ActionController::create(
                        $socialSecurityProviderActivity->id,
                        $action_id,
                        'RECHAZAR FACTURA PSS'
                    );
                }

                if ($resultPss == 'parcial') {
                    $action_id = ActionSocialSecurityProvider::APROBACION_PARCIAL_FACTURA_PSS;
                    $activity_action = ActionController::create(
                        $socialSecurityProviderActivity->id,
                        $action_id,
                        'APROBACION PARCIAL FACTURA PSS'
                    );

                    // TODO: Asientos 011 será ejecutado por un cron a una hora determinada
                    //Reportar el pago
                    // TODO: Mover esto al cron despues del asiento 011
                    $action_id = ActionSocialSecurityProvider::REPORTAR_PAGO_PARCIAL_REALIZADO_PSS;
                    $activity_action = ActionController::create(
                        $socialSecurityProviderActivity->id,
                        $action_id,
                        'REPORTAR PAGO PARCIAL REALIZADO PSS'
                    );
                }

            }

            
            DB::commit();

            if (Auth::user()->area_id === Area::ADMINISTRATIVE || Auth::user()->area_id === Area::AUDITOR || Auth::user()->area_id === Area::ANALISTA_INDEMNIZACION) {
                return redirect('/tablero/indemnizaciones/cuentas_medicas')->with('success', 'Acción realizada exitosamente');
            } elseif (Auth::user()->area_id === Area::ADMINISTRATIVE || Auth::user()->isProvider()) {
                return redirect('/tablero/indemnizaciones/cuentas_medicas')->with('success', 'Acción realizada exitosamente');
            } else {
                $errors = "Acceso no permitido.";
                throw new \Exception($errors); // Lanzar excepción con el error
            }
        } catch (\Exception $e) {
            // Hacer rollback si hay algún error
            DB::rollBack();

            // Capturar el mensaje de error
            $error = $e->getMessage();

            // Redirigir con el mensaje de error
            return redirect('/servicio/' . $socialSecurityProviderActivity->id . '/social_security_providers')->withErrors($error)->withInput();
        }
    }

    // TODO: crea metodo para ejecutar desde un cron el cual cree los asientos de los pagos aprobados
    public function cronProcessApprovedInvoices()
    {
        DB::beginTransaction();
        try {
            // Buscar todos los activities del servicio 99 y que esten aprobadas
            $activities = Activity::query()
                ->where('service_id', Service::SERVICE_SOCIAL_SECURITY_PROVIDERS_MNK)
                ->whereIn('state_id', [StateSocialSecurityProvider::FACTURA_PSS_APROBADA, StateSocialSecurityProvider::FACTURA_PSS_PARCIAL_APROBADA])
                ->with(['socialSecurityProvider.ordinaryInvoices' => function ($query) {
                    $query->where('result', 'aprobado');
                }])
                ->get();

            foreach ($activities as $activity) {
                // iteramos sobre las facturas del servicio
                foreach ($activity->socialSecurityProvider->ordinaryInvoices as $ordinaryInvoice) {
                    // Buscamos el gis_sort de la factura
                    $gisSort = GisSort::query()->where('consecutive', $ordinaryInvoice->case_number)->first();
                    // Buscamos la poliza de la factura usando el activity.parent_id del gis_sort
                    $policy = PolicySort::query()->where('activity_id', $gisSort->activity->parent_id)->first();

                    if ($policy) {
                        if ( config('app.env') != 'prod' ) {
                            //Instanciamos el controlado para los asientos contables
                            $accountingEntryController = new AccountingEntryController();
                            $accountingEntryController->reportAccountCase011(
                                $policy, 
                                $ordinaryInvoice->total_amount, 
                                $gisSort->activity_id
                            );
    
                            // TODO: almacenar el action REPORTAR PAGO REALIZADO PSS
                        }
                    }

                }
            }

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error in cronProcessApprovedInvoices '. $e->getMessage(), [$e]);
        }

        return ;
    }

    public function storeReportData(Request $request, $cpath, $id)
    {
        // Obtener el cliente
        $client = Client::where('path', $cpath)->firstOrFail();

        // Obtener la actividad de seguridad social
        $socialSecurityProviderActivity = Activity::where('client_id', $client->id)
            ->where('id', $id)
            ->firstOrFail();

        if (!$socialSecurityProviderActivity) {
            return response()->json(['error' => 'Actividad no encontrada'], 404);
        }

        $socialSecurityProvider = $socialSecurityProviderActivity->socialSecurityProvider;

        $validated = $request->validate([
            'letter_date' => 'required|date',
            'consecutive_number' => 'required|string',
            'ccss_employee_name' => 'required|string',
            'subject' => 'required|string',
            'process_month' => 'required|string',
            'observations' => 'nullable|string',
            'billing_month' => 'required|string',
            'unit_name' => 'required|string',
            'signer_qualifications' => 'required|string',
            'position' => 'required|string',
        ]);

        $reportData = $socialSecurityProvider->reportData()->first();

        if (!$reportData) {
            $socialSecurityProvider->reportData()->create($validated);
        } else {
            $reportData->update($validated);
        }

        // crear los datos desde la relacion
        
        return response()->json(['message' => 'Report data created successfully!']);
    }

    public function previewPdf(Request $request, $cpath, $id)
    {
        // Obtener el cliente
        $client = Client::where('path', $cpath)->firstOrFail();

        // Obtener la actividad de seguridad social
        $socialSecurityProviderActivity = Activity::where('client_id', $client->id)
            ->where('id', $id)
            ->firstOrFail();

        if (!$socialSecurityProviderActivity) {
            return response()->json(['error' => 'Actividad no encontrada'], 404);
        }

        $socialSecurityProvider = $socialSecurityProviderActivity->socialSecurityProvider;
        $reportData = $socialSecurityProvider->reportData()->first();

        $months = [
            'Enero',
            'Febrero',
            'Marzo',
            'Abril',
            'Mayo',
            'Junio',
            'Julio',
            'Agosto',
            'Septiembre',
            'Octubre',
            'Noviembre',
            'Diciembre',
        ];


        $data = [
            'fechaOficio' => optional($reportData)->letter_date ?? '',
            'consecutiveNumber' => optional($reportData)->consecutive_number ?? '',
            'ccssEmployeeName' => optional($reportData)->ccss_employee_name ?? '',
            'subject' => optional($reportData)->subject ?? '',
            'processMonth' => optional($reportData)->process_month ? $months[$reportData->process_month - 1] : '',
            
            // Datos para la primera tabla
            'facturasPagadas' => 200,
            'montoPagado' => 100000000,
            'facturasRechazadas' => 100,
            'montoRechazado' => 50000000,
            
            // Datos para la segunda tabla
            'reclamosPagados' => 200,
            'montoReclamosPagados' => 100000000,
            'reclamosRechazados' => 100,
            'montoReclamosRechazados' => 50000000,
            
            'observations' => optional($reportData)->observations ?? '',
            'billingMonth' => optional($reportData)->billing_month ? $months[$reportData->billing_month - 1] : '', // 'mesExcel' => 'abril 2025',
            'unitName' => optional($reportData)->unit_name ?? '',
            'signerQualifications' => optional($reportData)->signer_qualifications ?? '',
            'position' => optional($reportData)->position ?? '',
        ];

        $pdf = PDF::loadView('services.social_security_providers.reports.response_doc_ccss', $data);

        $pdf->setPaper('letter', 'portrait');

        return $pdf->download('respuesta_ccss_preview' . date('YmdHis') . '.pdf');
    }
    
    public function generateReimbursementReport(Request $request, $cpath, $id)
    {
        // Obtener el cliente
        $client = Client::where('path', $cpath)->firstOrFail();

        // Obtener la actividad de seguridad social
        $socialSecurityProviderActivity = Activity::where('client_id', $client->id)
            ->where('id', $id)
            ->firstOrFail();

        if (!$socialSecurityProviderActivity) {
            return response()->json(['error' => 'Actividad no encontrada'], 404);
        }

        $socialSecurityProvider = $socialSecurityProviderActivity->socialSecurityProvider;

        /*$data = [
            'nombre_trabajador' => 'Ana Rodríguez',
            'fecha_pago' => '15 de Mayo de 2024',
            'monto_total_pagado' => '1,250,000',
            'numero_caso' => '12345678',
            'facturas_pagadas' => [
                ['numero' => 'FAC-001', 'monto' => '500,000'],
                ['numero' => 'FAC-002', 'monto' => '450,000'],
                ['numero' => 'FAC-003', 'monto' => '300,000'],
            ],
            'facturas_no_pagadas' => [
                ['numero' => 'FAC-004', 'motivo_no_pago' => 'Documentación incompleta'],
                ['numero' => 'FAC-005', 'motivo_no_pago' => 'Servicio no cubierto'],
            ],
        ];*/
        
        $facturasPagadas = [];
        $facturasNoPagadas = [];
        $montoTotalPagado = 0;
        foreach ($socialSecurityProvider->ordinaryInvoices as $ordinaryInvoice) {
            if ($ordinaryInvoice->result == 'aprobado') {
                $facturasPagadas[] = [
                    'numero' => $ordinaryInvoice->invoice_number,
                    'monto' => number_format(optional($ordinaryInvoice)->total_amount ?? 0, 2, ',', '.'),
                ];

                $montoTotalPagado += $ordinaryInvoice->total_amount;
            }

            if ($ordinaryInvoice->result == 'rechazado') {
                $facturasNoPagadas[] = [
                    'numero' => $ordinaryInvoice->invoice_number,
                    'motivo_no_pago' => $ordinaryInvoice->reason,
                ];
            }
        }

        $pdf = PDF::loadView('services.social_security_providers.reports.reimbursement', [
            'nombre_trabajador' => 'Ana Rodríguez',
            'fecha_pago' => '15 de Mayo de 2024',
            'monto_total_pagado' => number_format($montoTotalPagado ?? 0, 2, ',', '.'),
            'numero_caso' => '12345678',
            'facturas_pagadas' => $facturasPagadas,
            'facturas_no_pagadas' => $facturasNoPagadas,
        ]);

        $pdf->setPaper('letter', 'portrait');

        return $pdf->download('pago_y_rechazo_de_factura(s)_por_reintegro_preview' . date('YmdHis') . '.pdf');
    }

    public function updateOrdinaryInvoices(Request $request, $cpath, $id)
    {
        // Validar que 'data' sea un arreglo
        $validator = Validator::make($request->all(), [
            'data' => 'required|array',
            'data.*.invoice_id' => 'required|exists:pss_ordinary_invoices,id', // Ajusta el nombre de la tabla si es necesario
            'data.*.result' => 'required|in:aprobado,rechazado',
            'data.*.rejection_reason' => 'nullable|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'message' => 'Datos inválidos.',
                'errors' => $validator->errors(),
            ], 422);
        }

        foreach ($request->data as $entry) {
            // Aquí puedes actualizar cada factura (invoice) con los datos recibidos
            $invoice = PssOrdinaryInvoice::find($entry['invoice_id']);

            // Suponiendo que el modelo tenga campos 'result' y 'rejection_reason'
            $invoice->result = $entry['result'];
            $invoice->reason = $entry['result'] === 'rechazado' ? $entry['rejection_reason'] : "";
            $invoice->save();
        }

        return response()->json([
            'status' => 'success',
            'message' => 'Datos guardados correctamente.',
        ]);
    }

    // Ordinary invoices
    public function ordinaryInvoices(Request $request)
    {
        // Obtener el usuario autenticado
        $user = Auth::user();

        if (is_null($user)) {
            return redirect()->back()->with('error', 'Usuario no encontrado.');
        }

        // capturar el afiliado del usuario loegueado (el proveedor)
        $provider = Auth::user()->provider;

        // validar que el usuario logueado tenga un proveedor asociado
        if (is_null($provider)) {
            throw new \Exception("No se encontró un proveedor asociado al usuario logueado.");
        }

        // Usa $request para obtener el path de la URL y verifica si contiene ciertas rutas
        $path = $request->path();

        if (strpos($path, 'proveedor/pss/temporary_disability') !== false) {
            if (auth()->user()->isProviderINS()) {
                throw new \Exception("El proveedor no es CCSS.");
            }

            $invoiceType = 'temporary_disability';
            $invoiceSubType = basename(rtrim($path, '/'));
            $invoiceTypeTitle = $this->invoiceTypes['temporary_disability'];
            $invoiceTypeSuccessMessage = 'Caja Costarricense de Seguro Social, se realizó con éxito la carga de los soportes de facturación. MNK Seguros procederá con la revisión y posible pago dentro del plazo de 15 días hábiles, conforme lo establece el Reglamento de Riesgos Excluidos CCSS.';
            $invoiceTypeSubtitle = $invoiceSubType == 'ordinary' ? 'Factura ordinaria remitida por la CCSS' : 'Factura de reclamo remitida por la CCSS';
        } elseif (strpos($path, 'proveedor/pss/medical_services') !== false) {
            if (auth()->user()->isProviderINS()) {
                throw new \Exception("El proveedor no es CCSS.");
            }

            $invoiceType = 'medical_services';
            $invoiceSubType = basename(rtrim($path, '/'));
            $invoiceTypeTitle = $this->invoiceTypes['medical_services'];
            $invoiceTypeSuccessMessage = 'Caja Costarricense de Seguro Social, se realizó con éxito la carga de los soportes de facturación. MNK Seguros procederá con la revisión y posible pago dentro del plazo de 15 días hábiles, conforme lo establece el Reglamento de Riesgos Excluidos CCSS.';
            $invoiceTypeSubtitle = $invoiceSubType == 'ordinary' ? 'Factura ordinaria remitida por la CCSS' : 'Factura de reclamo remitida por la CCSS';
        } elseif (strpos($path, 'proveedor/pss/ins') !== false) {
            if (auth()->user()->isProviderCCSS()) {
                throw new \Exception("El proveedor no es INS.");
            }
            
            $invoiceType = 'ins';
            $invoiceSubType = basename(rtrim($path, '/'));
            $invoiceTypeTitle = $this->invoiceTypes['ins'];
            $invoiceTypeSuccessMessage = 'Instituto Nacional de Seguros, se realizó con éxito la carga de los soportes de facturación. MNK Seguros procederá con la revisión y posible pago dentro del plazo de 30 días naturales.';
            $invoiceTypeSubtitle = 'Factura remitida por el INS';
        } else {
            $invoiceType = '';
            $invoiceSubType = '';
            $invoiceTypeTitle = '';
            $invoiceTypeSuccessMessage = '';
            $invoiceTypeSubtitle = '';
        }

        return view('services.social_security_providers.provider.ordinary_invoices', [
            'data' => [],
            'id' =>  $user->id, // Esto es para que el id de la ruta hacia la vista servicios asignados
            'user' => $user,
            'ibanAccounts' => json_decode($provider->iban_accounts),
            'invoiceType' => $invoiceType,
            'invoiceTypeTitle' => $invoiceTypeTitle,
            'invoiceTypeSuccessMessage' => $invoiceTypeSuccessMessage,
            'invoice_type_subtitle' => $invoiceTypeSubtitle,
            'invoice_sub_type' => $invoiceSubType,
        ]);
    }

    public function storeOrdinaryInvoices(storeOrdinaryInvoicesRequest $request, $cpath)
    {
        //Obtener el cliente
        $client = Client::where('path', $cpath)->firstOrFail();

        //Asignar el tipo de acción
        $actionId = ActionSocialSecurityProvider::CARGA_FACTURA_PSS;

        //Iniciamos la transacción
        DB::beginTransaction();
        try {

            //Solo los proveedores pueden subir archivos
            if (Auth::user()->isProvider()) {
                //capturar el afiliado del usuario loegueado (el proveedor)
                $affiliateId = optional(Auth::user()->provider)->affiliate_id;

                //validar que el usuario lo
                //gueado tenga un afiliado asociado
                if (is_null($affiliateId)) {
                    throw new \Exception("No se encontró un afiliado asociado al usuario logueado.");
                }

                //capturar el afiliado del usuario loegueado (el proveedor)
                $providerId = optional(Auth::user()->provider)->id;

                //validar que el usuario logueado tenga un proveedor asociado
                if (is_null($providerId)) {
                    throw new \Exception("No se encontró un proveedor asociado al usuario logueado.");
                }

                // Actividad de proveedores de la seguridad social
                $socialSecurityProviderServiceActivity = Activity::create([
                    'client_id' => $client->id,
                    'affiliate_id' => $affiliateId,
                    'service_id' => Service::SERVICE_SOCIAL_SECURITY_PROVIDERS_MNK,
                    'state_id' => StateSocialSecurityProvider::REGISTRADO,
                    'user_id' => Auth::id(),
                ]);

                // $medicalBillNew
                $socialSecurityProviderNew = SocialSecurityProvider::create([
                    'activity_id' => $socialSecurityProviderServiceActivity->id,
                    'provider_id' => $providerId,
                    'invoice_type' => $request->input('invoice_type'),
                    'invoice_subtype' => $request->input('invoice_subtype'),
                    'iban_account' => $this->ibanAccouttextToJson($request->input('iban_account')),
                ]);

                //Crear el activity_action del servicio
                ActionController::create(
                    $socialSecurityProviderServiceActivity->id,
                    $actionId,
                    'CARGUE DE FACTURA PSS'
                );

                // Captura de documentos ZIP
                if ($request->hasFile('invoce_zip_file')) {
                    $zipFile = $request->file('invoce_zip_file');
                    
                    //nombre original del documento
                    $originalName = pathinfo($zipFile->getClientOriginalName(), PATHINFO_FILENAME);

                    // Generar un nombre único para cada archivo
                    $uniqueFilename = "activity_document/" . uniqid() . "_{$originalName}_{$actionId}.zip";

                    // Guardar el PDF en S3
                    Storage::disk('s3')->put($uniqueFilename, file_get_contents($zipFile));

                    // Guardar la información del documento en la base de datos
                    $activityDocument = new ActivityDocument();
                    $activityDocument->activity_id = $socialSecurityProviderServiceActivity->id;
                    $activityDocument->document_id = SocialSecurityProvider::SOPORTES_DE_FACTURACION_ZIP;
                    $activityDocument->path = $uniqueFilename;
                    $activityDocument->uploaded_at = Carbon::now();
                    $activityDocument->save();
                }

                if ($request->hasFile('invoce_excel_file')) {
                    $excelFile = $request->file('invoce_excel_file');

                    //validación antes del procesamiento de datos (solo es lectura y validación)
                    $this->validateBeforeProcessing($excelFile);

                    /*
                        Explicación del procesamiento de datos:
                        - Se carga el archivo Excel y se procesa cada fila.
                        - Se utiliza la función `prepareRow` para preparar cada fila.
                        - Se llama a la función `processOrdinaryInvoicesSpreadsheet` para procesar cada fila.
                    */
                    Excel::selectSheetsByIndex(0)
                        ->load($excelFile->path())
                        ->each(function ($row) use ($socialSecurityProviderNew, &$errors, &$alerts, &$processedRecords) {
                            $filteredRow = $this->prepareRow($row);

                            // Llama a la función de procesamiento
                            $this->processOrdinaryInvoicesSpreadsheet(
                                $socialSecurityProviderNew, 
                                $filteredRow, 
                                $errors, 
                                $alerts,  
                                $processedRecords
                            );
                        });
                    
                    // Obtener el nombre original del documento sin la extensión
                    $originalName = pathinfo($excelFile->getClientOriginalName(), PATHINFO_FILENAME);

                    // Generar un nombre único para cada archivo
                    $uniqueFilename = "activity_document/" . uniqid() . "_{$originalName}_{$actionId}.xlsx";

                    // Guardar el EXCEL en S3
                    Storage::disk('s3')->put($uniqueFilename, file_get_contents($excelFile));

                    // Guardar la información del documento en la base de datos
                    $activityDocument = new ActivityDocument();
                    $activityDocument->activity_id = $socialSecurityProviderServiceActivity->id;
                    $activityDocument->document_id = SocialSecurityProvider::FACTURACION_ORDINARIA_EXCEL;
                    $activityDocument->path = $uniqueFilename;
                    $activityDocument->uploaded_at = Carbon::now();
                    $activityDocument->save();
                }

                DB::commit(); // Si todo va bien, se hace commit de la transacción

                return response()->json([
                    'success' => true,
                    'activity_id' => $socialSecurityProviderServiceActivity->id,
                    'message' => "Se cargó {$this->invoiceTypes[$request->input('invoice_type')]} correctamente.",
                ]);
            } else {
                $errors[] = "Debes ser un proveedor para realizar el cargue de factura.";
                throw new \Exception(implode(", ", $errors));
            }

        } catch (\Exception $e) {
            DB::rollBack();
            // Convertimos los errores en un array separado por saltos de línea
            $errors = explode("\n", $e->getMessage());

            Log::error($e);

            // return redirect()->back()->withErrors($errors)->withInput();

            return response()->json([
                'success' => false,
                'errors' => $errors,
            ]);
        }
    }

    private function processOrdinaryInvoicesSpreadsheet($socialSecurityProvider, $row, &$errors, &$alerts, &$processedRecords)
    {
        $row = $this->prepareRow($row);
        $rowNumber = $processedRecords + 1; // Cambiar este número según tu lógica de conteo

        //validar headers del excel
        if ($rowNumber === 1) {
            $this->validateHeaders($row, $rowNumber);
        }

        // filas donde están los valores
        $rowNumberValues = $rowNumber + 1;

        // Filtrar y validar campos
        $filteredRow = array_slice($row, 0, 41);
        if ($this->isRowEmpty($filteredRow)) {
            return;
        }

        // Validar campos requeridos
        $this->validateRequiredFields($filteredRow, $rowNumberValues, $errors);

        // Convertir fechas y validar
        $serviceDate = $this->validateDate($filteredRow['fecha'], $rowNumberValues, $errors);

        // Validar campos numéricos (identificacion, consecutivo poliza)
        $this->validateNumericFields($filteredRow, $rowNumberValues, $errors);

        // validar formato monto total
        $this->validateFormatValueService($filteredRow['montototal'], $rowNumberValues, $errors);

        // Verificar que la persona existe en  los casos reportados
        $gisSort = GisSort::query()
            ->where('number_identification_affiliate', $filteredRow['numidentificacion'])
            ->where('date_accident', $serviceDate->format('Y-m-d'))
            ->first();
            
        $reason = '';
        $result = 'aprobado';

        if (!$gisSort) {
            $result = 'rechazado';
            $reason = 'Caso no reportado como Riesgo de Trabajo';
        } elseif ($gisSort && strtoupper($gisSort->insured_case) == 'NO') {
            $result = 'rechazado';
            $reason = $socialSecurityProvider->invoice_type == 'ins' || $socialSecurityProvider->invoice_subtype == 'claim'  ? 
                'Caso no amparado: Art. 195, 196, 197 Código de Trabajo' :
                'Caso no reportado como Riesgo de Trabajo';
        }

        PssOrdinaryInvoice::create([
            'social_security_provider_id' => $socialSecurityProvider->id,
            'case_number' => $filteredRow['numcaso'],
            'gis_sort_id' => $gisSort->id ?? null,
            'identification_number' => $filteredRow['numidentificacion'],
            'name' => $filteredRow['nombre'],
            'date' => $serviceDate->format('Y-m-d'),
            'medical_center_code' => $filteredRow['codcentromedico'],
            'invoice_number' => $filteredRow['numfactura'],
            'total_amount' => (float)$filteredRow['montototal'],
            'result' => $result, // aprobado / rechazado
            'reason' => $reason,
        ]);

        // Al final del método, si hay errores, lanza una excepción
        if (!empty($errors)) {
            throw new \Exception(implode(", ", $errors)); // Lanzar excepción con los errores acumulados
        }
    }

    private function prepareRow($row)
    {
        if ($row instanceof \Maatwebsite\Excel\Collections\CellCollection) {
            $row = $row->toArray();
        }
        return $row; //array_change_key_case($row, CASE_LOWER);
    }

    private function validateFormatValueService($value_service, $rowNumber, &$errors)
    {
        // Reemplazar el punto por coma si es necesario (dependiendo del formato que prefieras)
        // Esto asegura que tanto "1000.5" como "1000,5" se validen correctamente
        $value_service_normalized = str_replace('.', ',', $value_service);

        // Expresión regular para validar el formato numérico con una sola coma o punto (para decimales)
        // Además, aseguro que no haya más de una coma
        $pattern = '/^\d+(\,\d+)?$/';  // Acepta números enteros o decimales con una coma o punto

        // Verificar si la cadena cumple con el formato y si tiene más de una coma
        if (!preg_match($pattern, $value_service_normalized) || substr_count($value_service_normalized, ',') > 1) {
            // Si no cumple con el formato o tiene más de una coma, agrega el error
            $errors[] = "El valor '{$value_service}' en la fila {$rowNumber} no maneja el formato adecuado. El formato permitido es un número entero o con coma para los decimales (ejemplo: 65000 o 65000,42).";
            throw new \Exception(implode(", ", $errors));
        }
    }

    private function validateNumericFields($filteredRow, $rowNumber, &$errors)
    {
        $numericFields = ['numidentificacion', 'numfactura'];
        foreach ($numericFields as $field) {
            if (!is_numeric($filteredRow[$field])) {
                $errors[] = "El valor de '{$field}' en la fila {$rowNumber} no es una opción válida.";
            }
        }
        // Al final del método, si hay errores, lanza una excepción
        if (!empty($errors)) {
            throw new \Exception(implode(", ", $errors)); // Lanzar excepción con los errores acumulados
        }
    }

    private function validateDate($date, $rowNumber, &$errors)
    {
        try {
            // Si la fecha es un número, la convertimos desde formato Excel
            if (is_numeric($date)) {
                $dateObject = PHPExcel_Shared_Date::ExcelToPHPObject($date);

                // Convertir a Carbon para facilitar validaciones
                $dateObject = Carbon::instance($dateObject);
            } elseif ($date instanceof \Carbon || $date instanceof \DateTime) {
                $dateObject = Carbon::instance($date);
            } else {
                // Intentar parsear la fecha con el formato correcto
                $dateObject = null;

                // Lista de posibles formatos que pueden venir en el archivo
                $formats = [
                    'd/m/Y',          // esperado
                    'Y-m-d H:i:s',    // como "2025-04-28 00:00:00"
                    'Y-m-d',          // solo fecha ISO
                    'd-m-Y',          // otras variantes comunes
                ];

                foreach ($formats as $format) {
                    $parsed = Carbon::createFromFormat($format, $date);
                    $parseErrors = Carbon::getLastErrors();
                    if ($parsed && $parseErrors['error_count'] === 0 && $parseErrors['warning_count'] === 0) {
                        $dateObject = $parsed;
                        break;
                    }
                }


                if (!$dateObject) {
                    throw new \Exception("Formato inválido");
                }
            }

            // Ajustar zona horaria si es necesario
            $dateObject->setTimezone('America/Costa_Rica');

            // Validar que la fecha no sea futura
            if ($dateObject->greaterThan(Carbon::today('America/Costa_Rica'))) {
                $errors[] = "La fecha en la fila {$rowNumber} es fútura y no está permitida: {$dateObject->format('d/m/Y')}";
                throw new \Exception(implode(", ", $errors));
            }

            return $dateObject;
        } catch (\Exception $e) {
            $errors[] = "El formato de la fecha en la fila {$rowNumber} no es válido (d/m/Y): {$date}";
            throw new \Exception(implode(", ", $errors));
        }
    }

    private function isRowEmpty($filteredRow)
    {
        $result = empty(trim($filteredRow['numcaso'])) &&
            empty(trim($filteredRow['nombre'])) &&
            empty(trim($filteredRow['numidentificacion'])) &&
            empty(trim($filteredRow['fecha'])) &&
            empty(trim($filteredRow['codcentromedico'])) &&
            empty(trim($filteredRow['numfactura'])) &&
            empty(trim($filteredRow['montototal']));

        return $result;
    }

    private function validateRequiredFields($filteredRow, $rowNumber, &$errors)
    {
        $fields = [
            'numcaso',
            'nombre',
            'numidentificacion',
            'fecha',
            'codcentromedico',
            'numfactura',
            'montototal',
        ];


        foreach ($fields as $fieldName) {
            if (empty($filteredRow[$fieldName])) {
                $errors[] = "Faltan datos en la fila {$rowNumber}, campo: {$fieldName}";
            }
        }
        // Al final del método, si hay errores, lanza una excepción
        if (!empty($errors)) {
            throw new \Exception(implode(". ", $errors)); // Lanzar excepción con los errores acumulados
        }
    }

    private function validateHeaders($row, $rowNumber)
    {
        // Claves requeridas en el orden correcto
        $requiredKeys = [
            'regimen',
            'numcaso',
            'nombre',
            'fecnacimiento',
            'tipidentificacion',
            'numidentificacion',
            'genero',
            'direccion',
            'numeropatronal',
            'nombre2',
            'codocupacion',
            'ocupacion',
            'fecha',
            'hora',
            'direccionacc',
            'descripcion',
            'epidemia_pandemia',
            'placa',
            'codcentromedico',
            'centromedico',
            'codmedico',
            'medico',
            'servicio',
            'lesiones',
            'fechainicioatencion',
            'fechafinatencion',
            'menoredad',
            'asegurado',
            'poliza',
            'numfactura',
            'numorden',
            'fecfacturacion',
            'montototal',
            'fecservicio',
            'cantidad',
            'codtarifa',
            'descripcion3',
            'costounitario',
            'totalservicio',
            'cantfacturas',
            'montofacturas',
        ];

        // Filtrar claves numéricas (si es necesario)
        $rowKeys = array_filter(array_keys($row), 'is_string');

        // Inicializar errores
        $errors = [];

        // Validar la cantidad de claves
        if (count($rowKeys) < count($requiredKeys)) {
            $errors[] = "La fila {$rowNumber} no contiene suficientes columnas. Se esperaban " . count($requiredKeys) . " columnas, pero se encontraron " . count($rowKeys) . ".";
            throw new \Exception(implode("; ", $errors)); // Lanzar excepción con los errores acumulados
        }

        // Validar las claves y su orden
        foreach ($requiredKeys as $key) {
            // Verificar si la clave requerida está presente
            if (!array_key_exists($key, $row)) {
                $errors[] = "La fila {$rowNumber} falta la columna: '{$key}'.";
            }
        }

        // Lanzar excepción si hay errores
        if (!empty($errors)) {
            throw new \Exception(implode("; ", $errors)); // Lanzar excepción con los errores acumulados
        }
    }

    public function validateBeforeProcessing($file)
    {
        // Validar que el archivo es un Excel
        if (!in_array($file->getClientMimeType(), ['application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'])) {
            $errors[] = "Solo se permiten archivos Excel. " . $file->getClientMimeType();
            throw new \Exception(implode(", ", $errors));
        }

        // Cargar el archivo Excel
        $objPHPExcel = PHPExcel_IOFactory::load($file->getPathname());
        $worksheet = $objPHPExcel->getActiveSheet();

        // Leer los datos fila por fila sin conversión automática
        $rows = [];

        foreach ($worksheet->getRowIterator() as $row) {
            $cellIterator = $row->getCellIterator();
            $cellIterator->setIterateOnlyExistingCells(false);
            $rowData = [];

            foreach ($cellIterator as $cell) {
                // FORZAR QUE LA CELDA SE LEA COMO TEXTO
                $cell->setDataType(\PHPExcel_Cell_DataType::TYPE_STRING);

                // Obtener el valor exacto de la celda
                $rowData[] = (string) $cell->getValue();
            }

            // Limitar a las primeras 9 columnas
            $rowData = array_slice($rowData, 0, 41);

            $rows[] = $rowData;
        }

        // Validar que el archivo tiene datos
        if (empty($rows) || count($rows) < 2) {
            throw new \Exception("El archivo Excel está vacío o tiene un formato incorrecto.");
        }

        // Obtener los encabezados de la primera fila y limpiarlos
        $headers = array_map('trim', $rows[0]);

        // Verificar si existe la columna "valor_servicio"
        // $valorServicioIndex = array_search('valor_servicio', $headers);
        $montoTotalIndex = array_search('MontoTotal', $headers);

        if ($montoTotalIndex === false) {
            throw new \Exception("El archivo no tiene una columna llamada 'MontoTotal'.");
        }

        $errors = [];

        // Iterar sobre las filas del Excel
        foreach ($rows as $key => $fila) {
            if ($key === 0) continue; // Saltar la fila de encabezados
            $filaNum = $key + 1; // Ajustar número de fila real

            // Omitir filas completamente vacías
            if (empty(array_filter($fila, function ($value) { return trim($value) !== ''; }))) {
                continue;
            }

            // Verificar que "valor_servicio" existe en la fila
            if (!isset($fila[$montoTotalIndex]) || trim($fila[$montoTotalIndex]) === '') {
                $errors[] = "Error en la fila <strong>$filaNum</strong>: Falta el campo <strong>'MontoTotal'</strong>.";
                continue;
            }

            // Extraer el valor original tal como está en el Excel
            $valorOriginal = trim($fila[$montoTotalIndex]);

            // Si el valor es numérico y tiene punto decimal, convertirlo a coma
            if (is_numeric($valorOriginal) && strpos($valorOriginal, '.') !== false) {
                $valorOriginal = str_replace('.', ',', $valorOriginal);
            }

            // Validar el formato correcto (Ejemplo: 17800,20 o 250800)
            if (!preg_match('/^\d+(\.\d{3})*(,\d+)?$|^\d+$/', $valorOriginal)) {
                $errors[] = "Error en la fila <strong>$filaNum</strong>: El formato de '<strong>MontoTotal</strong>' es incorrecto (<strong>$valorOriginal</strong>).";
                continue;
            }

            // Convertir a un formato numérico válido en PHP
            $valor = str_replace(['.', ','], ['', '.'], $valorOriginal);

            if (!is_numeric($valor)) {
                $errors[] = "Error en la fila <strong>$filaNum</strong>: El valor <strong>'$valorOriginal'</strong> no es un número válido.";
            }

            // TODO: Validar otros campos según sea necesario
        }

        // Lanzar excepciones si hay errores
        if (!empty($errors)) {
            throw new \Exception(implode("\n", $errors));
        }
    }

    private function ibanAccouttextToJson(string $value)
    {
        if (empty($value)) {
            return json_encode([]);
        }

        $account = explode('-', $value);

        if (empty($account[0]) && empty($account[1])) {
            return json_encode([]);
        }

        $ibanAccount = [
            'currency' => $account[0],
            'iban' => $account[1],
        ];

        return json_encode($ibanAccount);
    }
}
