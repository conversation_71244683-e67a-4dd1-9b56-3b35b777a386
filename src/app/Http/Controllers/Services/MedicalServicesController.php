<?php

namespace App\Http\Controllers\Services;

use App\AccidentType;
use App\Action;
use App\Actions\ActionGisSort;
use App\Actions\ActionMedicalServiceSecondarySort;
use App\Actions\ActionMedicalServiceSort;
use App\Actions\ActionMedicationServiceSort;
use App\Actions\ActionPeItSort;
use App\Activity;
use App\ActivityAction;
use App\ActivityActionDocument;
use App\ActivityDocument;
use App\Affiliate;
use App\AgetsGi;
use App\Area;
use App\Client;
use App\GisSort;
use App\Holiday;
use App\Http\Controllers\ActionController;
use App\Http\Controllers\Controller;
use App\Http\Controllers\RuleActionsController;
use App\Http\Controllers\Tables\MailBoardController;
use App\InjuryLocation;
use App\InjuryNature;
use App\Mail\SendDocumentDataBase;
use App\MailTemplates\Constants\Templates;
use App\MailTemplates\TemplateBuilder;
use App\MedicalServiceCompanion;
use App\MedicalServiceControlledMedication;
use App\MedicalServiceDiagnostics;
use App\MedicalServiceFollowUp;
use App\MedicalServiceImageDiagnostics;
use App\MedicalServiceMedicalPrescription;
use App\MedicalServiceReferralSpecialits;
use App\MedicalServicesSecondaryCareSort;
use App\MedicalServicesSort;
use App\OccupationalDisease;
use App\PeitInabilitySort;
use App\PeItSort;
use App\PeItSortCaseDx;
use App\PolicyContact;
use App\PolicySort;
use App\PolicySpreadsheetAffiliate;
use App\Provider;
use App\Providers\AppServiceProvider;
use App\Service;
use App\State;
use App\States\StateGis;
use App\States\StateMedicalServiceSort;
use App\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use PDF;
use PhpParser\Node\Stmt\DeclareDeclare;

class MedicalServicesController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    //funcion para capitalizar
    public function capitalizeWordsWithExceptions($str) {
        // Lista de palabras a no capitalizar
        $exceptions = ['de', 'y', 'la', 'el', 'los', 'las', 'un', 'una', 'por', 'para', 'en', 'con'];

        // Divide el string en palabras
        $words = explode(' ', $str);

        // Recorrer cada palabra y capitalizar solo si no es una excepción
        foreach ($words as $index => $word) {
            // Si es la primera palabra o no está en las excepciones, capitalízala
            if ($index == 0 || !in_array(strtolower($word), $exceptions)) {
                $words[$index] = ucfirst(strtolower($word));
            } else {
                // Si está en las excepciones, déjala en minúsculas
                $words[$index] = strtolower($word);
            }
        }

        // Vuelve a unir las palabras en una cadena
        return implode(' ', $words);
    }

    //prueba pdf
    public function medicalDisability_pdf(Request $req, $cpath, $id)
    {
        $client   = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();

        $logoPath = public_path('images/mnk.png');
        $footerPath = public_path('images/mnk_footer.png');

        //OBTENER EL ULTIMO FOLLOWUP DE LA PM
        $lastFollowUp = $activity->medical_services_sort->followUps()->latest()->first();
        $fecha_desde = $lastFollowUp->start_date_of_incapacity;
        $dias = $lastFollowUp->days_of_incapacity;
        $fecha_hasta = Carbon::parse($fecha_desde)->addDays($dias)->startOfDay()->format('Y-m-d');
        $holiday = Holiday::where('holiday', $fecha_hasta)->first();
        $fecha_regreso = $holiday ? Carbon::parse($fecha_hasta)->addDay()->format('Y-m-d') : $fecha_hasta;
        $fecha_hasta = Carbon::parse($fecha_hasta)->subDay()->format('Y-m-d');

        //Aquí buscamos la provincia, canton y distrito (los nombres) esto con el método getLocationNamesFromJson
        $policySortController = new PolicySortController();
        $location = $policySortController->getLocationNamesFromJson(
            $lastFollowUp->province_incapacity_or_leave,
            $lastFollowUp->canton_incapacity_or_leave,
            $lastFollowUp->district_incapacity_or_leave);

        // Aplica la función a cada valor del array $location
        foreach ($location as $key => $value) {
            $location[$key] = $this->capitalizeWordsWithExceptions($value);
        }

        $pdf = Pdf::loadView('services.medical_services.docs.medical_disability', [
            'logoPath' => $logoPath,
            'footerPath' => $footerPath,
            'activity' => $activity,
            'fecha_desde' => $fecha_desde,
            'fecha_hasta' => $fecha_hasta,
            'fecha_regreso' => $fecha_regreso,
            'lastFollowUp' => $lastFollowUp,
            'location' => $location
        ])->setPaper('A4', 'portrait');


        return $pdf->stream('preview.pdf');
    }

    public function form(Request $req, $cpath, $id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::with([
            'affiliate',
            'medical_services_sort',
            'medical_services_sort.followUps' => function ($query) {
                $query->orderBy('follow_up_number');
            },
            'medical_services_sort.followUps.companions',
            'medical_services_sort.followUps.diagnostics',
            'medical_services_sort.followUps.diagnosticsImages',
            'medical_services_sort.followUps.specialists',
            'medical_services_sort.followUps.medicalPrescriptions',
            'medical_services_sort.followUps.controlledMedications',
        ])->findOrFail($id);

        //se captura la data del afiliado
        $data_affiliate = $activity->affiliate;

        //se captura luego la fecha de nacimiento de la planilla
        $lastDateOfBirth = PolicySpreadsheetAffiliate::where('affiliate_id', $data_affiliate->id)
            ->pluck('date_of_birth') // Obtiene todos los valores de la columna
            ->last(); // Selecciona el último registro del resultado

        //calcular edad del paciente
        if ($lastDateOfBirth) {
            $age = Carbon::parse($lastDateOfBirth)->age;
        } else {
            $age = null; // O maneja el caso donde la fecha no está disponible
        }

        //Fecha de accidente
        $date_accident_gis = "";
        $lastThreeMonths = [];
        $gisActivity = [];
        $policy_contacts = [];
        $fieldData = [];
        $documents = [];
        $activitySecundary = [];

        $activity_gis = Activity::where('client_id', $client->id)->where('id', $activity->parent_id)->first();

        if ($activity_gis){
            $gis = GisSort::where('activity_id', $activity_gis->id)->first();
            $policy_sort = Activity::where('id', $activity_gis->parent_id)->first()->policy_sort;
            if(!$gis){
                return view('errors.known', ['message' => 'No se encontró el GIS: '.$activity_gis->id]);
            }
            if (!$policy_sort){
                return view('errors.known', ['message' => 'No se encontró la póliza relacionada a GIS: '.$activity_gis->id]);
            }
            //Json de actividades economica
            $jsonSource = ($policy_sort->economic_activity == 'public') ? '/js/economic_activity/public.json' : '/js/economic_activity/private.json';
            $json = file_get_contents(public_path($jsonSource));
            $economicActivities = json_decode($json, true);
            //Se transforma a una collección en laravel
            $activity_economic_name = collect($economicActivities)->firstWhere('CODE', $policy_sort->activity_economic_id)['ACTIVITY_NAME'];

            //Capturar la fecha del accidente
            $date_accident_gis = $gis->date_accident ?? '';

            //Capturar la fecha del informe formal
            $activiy_actions = $gis->activity->activity_actions;
            if(!$activiy_actions)
            {
                return view('errors.known', ['message' => 'Se encontró problemas en los datos del GIS: '.$activity_gis->id]);
            }
            $formal_report_date = $gis->activity->activity_actions
                ->whereIn('action_id', [ActionGisSort::REPORTAR_FORMATO_FORMAL_CASO,ActionGisSort::REPORTAR_FORMATO_FORMAL_CASO_SIN_SOPORTES])
                ->pluck('created_at')
                ->first() ?? '';



            $lastThreeMonths = PolicySpreadsheetAffiliate::query()
                ->where('affiliate_id', $activity_gis->affiliate_id)
                ->orderBy('created_at', 'desc')
                ->limit(3)
                ->select('policy_spreadsheet_id', 'monthly_salary', 'days')
                ->get();

            $gisActivity = Activity::with([
                    'parent_activity.policy_sort.policy_contacts',
                    'affiliate.policy_spreadsheet_affiliate',
                    'state',
                    'gis_sort',
                    'activity_documents',
                    'activity_actions' => function ($query) {
                        $query->where('action_id', ActionGisSort::SOLICITAR_EMISION_CASO_FIRMA_FISICA);
                    }
                ])
                ->where('service_id', Service::SERVICE_GIS_SORT_MNK)
                ->where('client_id', $client->id)
                ->where('id',$activity_gis->id )->first();

            $policy_contacts = $gisActivity->parent_activity->policy_sort->policy_contacts;

            $reportTypes = ['Accidente', 'Enfermedad'];

            if (count($policy_contacts) == 0) {
                $policy_contacts = [PolicyContact::first()];
            }

            $fieldData = $gisActivity->gis_sort->gis_body_parts;

            //Guardamos los documentos realacionado a la actividad (Soportes)
            $activity_documents = ActivityDocument::where('activity_id', $gisActivity->id)->get();
            foreach ($activity_documents as $doc) {
                $documents[$doc->document_id] = $doc;
            }

            //SECUNDARIA
            $activitySecundary = Activity::with([
                'affiliate',
                'medical_services_secondary_care_sort',
                'medical_services_secondary_care_sort.followUps' => function ($query) {
                    $query->orderBy('follow_up_number');
                },
                'medical_services_secondary_care_sort.followUps.companions',
                'medical_services_secondary_care_sort.followUps.diagnostics',
                'medical_services_secondary_care_sort.followUps.diagnosticsImages',
                'medical_services_secondary_care_sort.followUps.specialists',
                'medical_services_secondary_care_sort.followUps.medicalPrescriptions',
                'medical_services_secondary_care_sort.followUps.controlledMedications',
            ])->where('parent_id', $activity_gis->id)
              ->where('service_id', Service::SERVICE_MEDICAL_SERVICES_SECONDARY_CARE_SORT_MNK)
              ->get();

        }

        if ($date_accident_gis) {
            // Solo formatear la fecha al formato requerido
            $date_accident_gis = Carbon::parse($date_accident_gis)->format('D M d Y H:i:s T'); // Formato ejemplo: 'Wed Oct 31 2024 19:00:00 GMT-0500'
        }

        $occupationalDisease = OccupationalDisease::all();
        $injuryNature = InjuryNature::all();
        $injuryLocation = InjuryLocation::all();

        $accidentType = AccidentType::all();
        $agetsGi = AgetsGi::all();
        $occupationalDisease = OccupationalDisease::all();


        return view('services.medical_services.form.form', [
            'activity' => $activity,
            'id' => $id,
            'diagnostic_mirror' => [],
            'policy_sort' => $policy_sort ?? null,
            'activity_economic_name' => $activity_economic_name ?? null,
            'gis' => $gis ?? null,
            'formal_report_date' => $formal_report_date ?? '',
            'age' => $age ?? null,
            'date_accident_gis' => $date_accident_gis,
            'gisActivity' => $gisActivity,
            'planillas' => $lastThreeMonths,
            'policy_contacts' => $policy_contacts,
            'fieldData' => $fieldData,
            'documents' => $documents,
            'activityMedicalSecundary' => $activitySecundary,
            'occupationalDisease' => $occupationalDisease,
            'injuryNature' => $injuryNature,
            'injuryLocation' => $injuryLocation,
            'accidentType' => $accidentType,
            'agetsGi' => $agetsGi,
            'occupationalDisease' => $occupationalDisease
        ]);
    }

    public function save(Request $req, $cpath, $id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('client_id', $client->id)
            ->where('id', $id)
            ->firstOrFail();
        $medical_service = $activity->medical_services_sort;
        //Iniciamos la transacción
        DB::beginTransaction();
      
        try {
            if ($medical_service) {
                $data = [
                    //CAMPOS FIJOS
                    'identification' => $req->input('identification'),
                    'name_patient' => $req->input('name_patient') ?? $medical_service->name_patient,
                    'activity' => $req->input('activity') ?? $medical_service->activity,
                    'date_dictamen' => $req->input('date_dictamen') ?? $medical_service->date_dictamen,
                    'name_patron' => $req->input('name_patron') ?? $medical_service->name_patron,
                    'id_patron' => $req->input('id_patron') ?? $medical_service->id_patron,
                    'num_policy_sort' => $req->input('num_policy_sort') ?? $medical_service->num_policy_sort,
                    'service_group' => $req->input('service_group') ?? $medical_service->service_group,
                    'service_mode' => $req->input('service_mode') ?? $medical_service->service_mode,
                    'medical_record' => $req->input('medical_record') ?? $medical_service->medical_record,
                    'specialty' => $req->input('specialty_ips') ?? $medical_service->specialty,
                    'entity_promote' => $req->input('entity_promote') ?? $medical_service->entity_promote,
                    'date_case' => $req->date_case_medication_hidden,
                    'number_case' => $req->input('number_case'),
                    'requires_follow_up' => $req->input('requires_follow_up'),
                    'gis_disease_confirmation' => $req->input('gis_disease_confirmation') ?? $medical_service->gis_disease_confirmation,
                    'disease_report_type_id' => $req->input('disease_report_type_id') ?? $medical_service->disease_report_type_id,
                    'injury_nature_id' => $req->input('injury_nature_id') ?? $medical_service->injury_nature_id,
                    'injury_location_id' => $req->input('injury_location_id') ?? $medical_service->injury_location_id,
                ];

                $medical_service->update($data);

                if ($req->input('gis_disease_confirmation') && $req->input('disease_report_type_id')) {

                    $gisSortActivity = $activity->parent->gis_sort;
                    $gisSortActivity->disease_report_type_id = $req->input('disease_report_type_id');
                    $gisSortActivity->save();
//
//                    GisSort::where('activity_id', $activity->parent->id)
//                        ->update([
//                            'disease_report_type_id' => $req->input('disease_report_type_id')
//                        ]);
                }


                //DATA PARA LOS CAMPOS CON SEGUIMIENTO
                $data_follow = [
                    'valuation_date' => $req->input('valuation_date_submit'),
                    'valuation_time' => $req->input('valuation_time'),
                    'consultation_channel' => $req->input('consultation_channel1'),
                    'consultation_reason' => $req->input('consultation_reason'),
                    'labor_role_description' => $req->input('labor_role_description1'),
                    'records' => $req->input('records'),
                    'size' => $req->input('height_input_enabled'),
                    'weight' => $req->input('weight_input_enabled') ,
                    'ta' => $req->input('ta'),
                    'ta2' => $req->input('ta2'),
                    'imc' => $req->input('imc'),
                    'fc' => $req->input('fc'),
                    'fr' => $req->input('fr'),
                    'physical_examination_description' => $req->input('physical_examination_description') ,
                    'plan_description' => $req->input('plan_description'),
                    'closed_case' => $req->input('closed_case'),
                    'medical_discharge' => $req->input('medical_discharge'),
                    'disability_type' => $req->input('disability_type'),
                    'attention_mode' => $req->input('attention_mode'),
                    'start_date_of_incapacity' => $req->input('start_date_of_incapacity_display_submit'),
                    'days_of_incapacity' => $req->input('days_of_incapacity'),
                    'extended_incapacity_reason' => $req->input('extended_incapacity_reason'),
                    'origin_case' => $req->input('origin_case'),
                    //'dx_principal' => $req->input('dx_principal'),
                    //'dx_principal_description' => $req->input('dx_principal_description'),
                    //'dx_related' => $req->input('dx_related'),
                    //'dx_related_description' => $req->input('dx_related_description'),
                    'observations_explanatory_notes' => $req->input('observations_explanatory_notes'),
                    'origin_diagnosis_referral_specialist' => $req->input('origin_diagnosis_referral_specialist'),
                    'origin_diagnosis' => $req->input('origin_diagnosis'),
                    'document_type_evaluator' => $req->input('document_type_evaluator') ,
                    'identification_number_evaluator' => $req->input('identification_number_evaluator'),
                    'full_name_evaluator' => $req->input('full_name_evaluator'),
                    'medical_registration_number_evaluator' => $req->input('medical_registration_number_evaluator'),
                    'specialty_evaluator' => $req->input('specialty_evaluator'),
                    'license_number_evaluator' => $req->input('license_number_evaluator'),
                    'labor_role_description_observation' => $req->input('labor_role_description_observation'),
                    'approved_service' => $req->input('approved_service'),
                    'observation_audit' => $req->input('observation_audit'),
                    'diagnosis_origin_prescription' => $req->input('diagnosis_origin_prescription'),
                    'diagnosis_origin_controlled_medication' => $req->input('diagnosis_origin_controlled_medication'),
                    'province_controlled_medication' => $req->input('province_controlled_medication'),
                    'canton_controlled_medication' => $req->input('canton_controlled_medication'),
                    'district_controlled_medication' => $req->input('district_controlled_medication'),
                    'pharmacies_branch_controlled_medication' => $req->input('pharmacies_branch_controlled_medication'),
                    'province_incapacity_or_leave' => $req->input('province_incapacity_or_leave'),
                    'canton_incapacity_or_leave' => $req->input('canton_incapacity_or_leave'),
                    'district_incapacity_or_leave' => $req->input('district_incapacity_or_leave'),
                    'province_medical_prescription' => $req->input('province_medical_prescription'),
                    'canton_medical_prescription' => $req->input('canton_medical_prescription'),
                    'district_medical_prescription' => $req->input('district_medical_prescription'),
                    'pharmacies_branch_prescription' => $req->input('pharmacies_branch_prescription'),
                    'province_order_diagnostic' => $req->input('province_order_diagnostic'),
                    'canton_order_diagnostic' => $req->input('canton_order_diagnostic'),
                    'district_order_diagnostic' => $req->input('district_order_diagnostic'),
                    'province_referral_specialist' => $req->input('province_referral_specialist'),
                    'canton_referral_specialist' => $req->input('canton_referral_specialist'),
                    'district_referral_specialist' => $req->input('district_referral_specialist'),
                    'required_hospitalization' => $req->input('required_hospitalization'),
                    'travel_expenses' => $req->input('travel_expenses'),
                    'transportation' => $req->input('transportation'),
                    'other_signs' => $req->input('other_signs'),
                    'next_follow_up_date' => $req->input('next_follow_up_date'),
                    'referral_specialist' => $req->input('referral_specialist'),
                    'specialty' => $req->input('specialty'),
                    'requires_follow_up' => $req->input('requires_follow_up'),
                ];

                $requires_follow_up = $req->input('requires_follow_up');

                if ($requires_follow_up == null && !empty(array_filter($data_follow))){
                    // Realiza una actualización en el último seguimiento si $requires_follow_up es vacío
                    $follow_up = MedicalServiceFollowUp::where('medical_services_sort_id', $medical_service->id)
                        ->latest()->first();
                    if ($follow_up) {
                        $follow_up->update($data_follow);
                    } else {
                        // Si no existe un seguimiento previo, crea uno con follow_up_number = 1
                        $follow_up = new MedicalServiceFollowUp();
                        $follow_up->fill($data_follow);
                        $follow_up->medical_services_sort_id = $medical_service->id;
                        $follow_up->follow_up_number = 1;  // Asigna como primer seguimiento
                        $follow_up->save();
                    }
                }
                if ($requires_follow_up ==='1' || $requires_follow_up === '0') {
                    // Crear un nuevo seguimiento con follow_up_number incrementado
                    $latestFollowUp = MedicalServiceFollowUp::where('medical_services_sort_id', $medical_service->id)
                        ->latest('follow_up_number')
                        ->first();

                    $follow_up_number = $latestFollowUp ? $latestFollowUp->follow_up_number + 1 : 1;
                    $follow_up = new MedicalServiceFollowUp();
                    $follow_up->fill($data_follow);
                    $follow_up->medical_services_sort_id = $medical_service->id;
                    $follow_up->follow_up_number = $follow_up_number;
                    $follow_up->save();

                    if ($latestFollowUp && $latestFollowUp->requires_follow_up == 0) {
                        $latestFollowUp->requires_follow_up = 1;
                        $latestFollowUp->next_follow_up_date = date('Y-m-d');
                        $latestFollowUp->closed_case = 0;
                        $latestFollowUp->save();
                    }
                }

                $companions = $req->input('companions');
                if (isset($companions['name']) && !empty($companions['name'])) {
                    // Recorre cada acompañante
                    foreach ($companions['name'] as $index => $name) {
                        // Solo procesar si el nombre no está vacío
                        if (!empty($name)) {
                            $companion = new MedicalServiceCompanion();
                            $companion->medical_service_sort_id = $medical_service->id;
                            $companion->medical_service_follow_up_id = $follow_up->id;
                            $companion->name = $name;  // Se utiliza el nombre directamente
                            $companion->contact_phone = $companions['phone'][$index] ?? null; // Utiliza null si no existe
                            $companion->relationship = $companions['relationship'][$index] ?? null; // Utiliza null si no existe

                            $companion->save();
                        }
                    }
                }

                //GUARDAR DIAGNOSTICOS
                $diagnostics = $req->diagnostics;
                //print_r($diagnostics);
                if ($diagnostics != null) {
                    foreach ($diagnostics as $key => $values) {
                        if (is_array($values) && count($values) > 1) {
                            array_pop($values);
                            $diagnostics[$key] = $values;
                        }
                    }
                    
                    if (isset($diagnostics['origin'][0])) {
                        foreach ($diagnostics['origin'] as $index => $code) {
                            if (!is_null($code)) {
                                if (!empty($diagnostics['id'][$index])) {
                                    // Buscar el diagnóstico existente
                                    $diagnostic = MedicalServiceDiagnostics::find($diagnostics['id'][$index]);
                                    
                                    // Si lo encuentra, actualizar campos
                                    if ($diagnostic) {
                                        $diagnostic->diagnostic_status = $diagnostics['diagnostic_status'][$index] ?? $diagnostic->diagnostic_status;
                                        $diagnostic->origin = $diagnostics['origin'][$index] ?? $diagnostic->origin;
                                        $diagnostic->save();
                                    }
                                } else {
                                $diagnostic = new MedicalServiceDiagnostics();
                                $diagnostic->medical_service_sort_id = $medical_service->id;
                                $diagnostic->medical_service_follow_up_id = $follow_up->id;
                                $diagnostic->code = $diagnostics['cod'][$index] ?? '';
                                $diagnostic->description = $diagnostics['description'][$index] ?? '';
                                $diagnostic->description_editable = $diagnostics['description_editable'][$index] ?? '';
                                $diagnostic->laterality = $diagnostics['laterality'][$index] ?? '';
                                $diagnostic->origin = $diagnostics['origin'][$index] ?? '';
                                $diagnostic->diagnostic_status = $diagnostics['diagnostic_status'][$index] ?? '';
                                $diagnostic->clasificacion = $diagnostics['clasificacion'][$index] ?? '';
                                $diagnostic->save();
                                }
                            }
                        }
                    }
                }

                //GUARDAR IMAGENES DIAGNOSTICAS
                $diagnostics_images =$req->diagnostics_images;
                //SE ELIMINA EL ULTIMO ELEMENTO DEL ARRAY YA QUE MANDA DUPLICADO DESDE EL BLADE
                if ($diagnostics_images != null && !empty($diagnostics_images)) {
                    foreach ($diagnostics_images as $key => $values) {
                        if (is_array($values) && count($values) > 1) {
                            array_pop($values);
                            $diagnostics_images[$key] = $values;
                        }
                    }
                }
                if (isset($diagnostics_images['cod']) && is_array($diagnostics_images['cod']) && !empty($diagnostics_images['cod'][0])) {
                //if (isset($diagnostics_images['cod']) && !is_null($diagnostics_images['cod'][0])){
                    foreach ($diagnostics_images['cod'] as $index => $code) {
                        if (!is_null($code)){
                            $diagnostics_image = new MedicalServiceImageDiagnostics();
                            $diagnostics_image->medical_service_sort_id = $medical_service->id;
                            $diagnostics_image->medical_service_follow_up_id = $follow_up->id;
                            $diagnostics_image->cod = $code;
                            $diagnostics_image->laterality = $diagnostics_images['laterality'][$index] ?? '';
                            $diagnostics_image->quantity = $diagnostics_images['quantity'][$index] ?? '';
                            $diagnostics_image->notes = $diagnostics_images['notes'][$index] ?? '';
                            $diagnostics_image->description = $diagnostics_images['description'][$index] ?? '';
                            $diagnostics_image->save();
                        }
                    }
                }

                //GUARDAR REMISION A ESPECIALISTA
                $specialists = $req->specialist;
                //SE ELIMINA EL ULTIMO ELEMENTO DEL ARRAY YA QUE MANDA DUPLICADO DESDE EL BLADE
                if ($specialists != null) {
                    foreach ($specialists as $key => $values) {
                        if (is_array($values) && count($values) > 1) {
                            array_pop($values);
                            $specialists[$key] = $values;
                        }
                    }
                    if (isset($specialists['cod'][0]) && !is_null($specialists['cod'][0])) {
                        foreach ($specialists['cod'] as $index => $code) {
                            if (!is_null($code)) {
                                $newSpecialist = new MedicalServiceReferralSpecialits();
                                $newSpecialist->medical_service_sort_id = $medical_service->id;
                                $newSpecialist->medical_service_follow_up_id = $follow_up->id;
                                $newSpecialist->code = $code;
                                $newSpecialist->laterality = $specialists['laterality'][$index] ?? '';
                                $newSpecialist->description = $specialists['description'][$index] ?? '';
                                $newSpecialist->quantity = $specialists['quantity'][$index] ?? '';
                                $newSpecialist->notes = $specialists['notes'][$index] ?? '';

                                $newSpecialist->save();
                            }
                        }
                    }
                }

                //GUARDAR FORMULA MEDICA
                $molecula_medical_prescription = $req->input('molecula-prescription2');
                $tipo_medical_prescription = $req->input('tipo-prescription2');
                $descrip_medical_prescription = $req->input('descrip-prescription2');
                $codigo_medical_prescription = $req->input('codigo-vademecum2');
                $casa_medical_prescription = $req->input('casa-vademecum2');
                $treatment_durations_medical_prescription = $req->input('duracion_tratamiento2');
                $frequency_medical_prescription = $req->input('frecuencia2');
                $dosage_medical_prescription = $req->input('dosis2');
                $quantity_letters_medical_prescription = $req->input('cantidad-letras2');
                $quantity_numbers_medical_prescription = $req->input('cantidad-numeros2');
                $notes_medical_prescription = $req->input('notas2');

                if (isset($molecula_medical_prescription)) {
                    foreach ($molecula_medical_prescription as $index => $molecula) {
                        // Validar que no sean nulos los campos necesarios
                        if ($molecula === null ||
                            $tipo_medical_prescription[$index] === null ||
                            $descrip_medical_prescription[$index] === null ||
                            $codigo_medical_prescription[$index] === null ||
                            $casa_medical_prescription[$index] === null ||
                            $treatment_durations_medical_prescription[$index] === null ||
                            $frequency_medical_prescription[$index] === null ||
                            $dosage_medical_prescription[$index] === null ||
                            $quantity_letters_medical_prescription[$index] === null ||
                            $quantity_numbers_medical_prescription[$index] === null ||
                            $notes_medical_prescription[$index] === null) {

                            // Ignorar si algún campo es nulo
                            continue;
                        }
                        $array = [
                            'molecula' => $molecula,
                            'tipo' => $tipo_medical_prescription[$index],
                            'descrip' => $descrip_medical_prescription[$index],
                            'codigo' => $codigo_medical_prescription[$index],
                            'casa' => $casa_medical_prescription[$index],
                            'treatment_duration' => $treatment_durations_medical_prescription[$index],  
                            'frequency' => $frequency_medical_prescription[$index],
                            'dosage' => $dosage_medical_prescription[$index],
                            'quantity_letters' => $quantity_letters_medical_prescription[$index],
                            'quantity_numbers' => $quantity_numbers_medical_prescription[$index],
                            'notes' => $notes_medical_prescription[$index],
                            'medical_service_sort_id' => $medical_service->id,
                            'medical_service_follow_up_id' => $follow_up->id
                        ];
                        MedicalServiceMedicalPrescription::create($array);
                    }
                }

                //GUARDAR FORMULA DE MEDICAMENTOS CONTROLADOS
                $molecula_controlled_medication = $req->input('molecula-controlled-medication');
                $tipo_controlled_medication = $req->input('tipo-controlled-medication');
                $descrip_controlled_medication = $req->input('descrip-controlled-medication');
                $codigo_controlled_medication = $req->input('codigo-vademecum');
                $casa_controlled_medication = $req->input('casa-vademecum');
                $treatment_durations_controlled_medication = $req->input('duracion_tratamiento');
                $frequency_controlled_medication = $req->input('frecuencia');
                $dosage_controlled_medication = $req->input('dosis');
                $quantity_letters_controlled_medication = $req->input('cantidad-letras');
                $quantity_numbers_controlled_medication = $req->input('cantidad-numeros');
                $notes_controlled_medication = $req->input('notas');

                if (isset($molecula_controlled_medication)) {
                    foreach ($molecula_controlled_medication as $index => $molecula) {
                        // Ignorar los campos nulos o vacíos
                        if ($molecula === null ||
                            $tipo_controlled_medication[$index] === null ||
                            $descrip_controlled_medication[$index] === null ||
                            $codigo_controlled_medication[$index] === null ||
                            $casa_controlled_medication[$index] === null ||
                            $treatment_durations_controlled_medication[$index] === null ||
                            $frequency_controlled_medication[$index] === null ||
                            $dosage_controlled_medication[$index] === null ||
                            $quantity_letters_controlled_medication[$index] === null ||
                            $quantity_numbers_controlled_medication[$index] === null ||
                            $notes_controlled_medication[$index] === null) {

                            // Ignorar si algún campo es nulo
                            continue;
                        }
                        $array = [
                            'molecula' => $molecula,
                            'tipo' => $tipo_controlled_medication[$index],
                            'descrip' => $descrip_controlled_medication[$index],
                            'codigo' => $codigo_controlled_medication[$index],
                            'casa' => $casa_controlled_medication[$index],
                            'treatment_duration' => $treatment_durations_controlled_medication[$index],  
                            'frequency' => $frequency_controlled_medication[$index],
                            'dosage' => $dosage_controlled_medication[$index],
                            'quantity_letters' => $quantity_letters_controlled_medication[$index],
                            'quantity_numbers' => $quantity_numbers_controlled_medication[$index],
                            'notes' => $notes_controlled_medication[$index],
                            'medical_service_sort_id' => $medical_service->id,
                            'medical_service_follow_up_id' => $follow_up->id
                        ];

                        MedicalServiceControlledMedication::create($array);

                    }
                }

                //REPORTAR VALORACION AP
                if ($activity->state_id == StateMedicalServiceSort::ATENCION_PRIMARIA_ASIGNADA_A_PROVEEDOR) {
                    ActionController::create($activity->id, ActionMedicalServiceSort::REPORTAR_VALORACION_AP, 'Reportar valoración atención primaria');
                }
                //REPORTAR VALORACION PM
                if ($activity->state_id == StateMedicalServiceSort::VALORACION_ASIGNADA_A_PROVEEDOR) {
                    $description = "REPORTAR VALORACIÓN PM";
                    $activityActionsCreated = ActionController::create($activity->id,
                        ActionMedicalServiceSort::REPORTAR_VALORACION_PM,
                        $description);
                }
                //ME-410 EJECUTAR ACCIONES PARA AUDITORES MEDICOS
                if (Auth::user()->area_id == \App\Area::AUDITOR) {
                    $requiredFields = [
                        'incapacity'=>[
                            'province_incapacity_or_leave', 'canton_incapacity_or_leave', 'district_incapacity_or_leave', 'other_signs',
                        'origin_case', 'disability_type', 'attention_mode', 'start_date_of_incapacity',
                        'days_of_incapacity', 'travel_expenses',
                        'transportation', 'observations_explanatory_notes'
                        ],
                        'hospitalization'=>['required_hospitalization'],
                        'order_diagnostic'=>[
                            'province_order_diagnostic', 'canton_order_diagnostic', 'district_order_diagnostic','origin_diagnosis'
                        ],
                        'referral_specialist'=>[
                            'province_referral_specialist', 'canton_referral_specialist', 'district_referral_specialist','origin_diagnosis_referral_specialist'
                        ],
                        'medical_prescription'=>[
                            'province_medical_prescription', 'canton_medical_prescription', 'district_medical_prescription','pharmacies_branch_prescription','diagnosis_origin_prescription'
                        ],
                        'controlled_medication'=>[
                            'province_controlled_medication', 'canton_controlled_medication', 'district_controlled_medication','pharmacies_branch_controlled_medication','diagnosis_origin_controlled_medication'
                        ]
                    ];
                    foreach ($requiredFields as $group => $fields) {
                        $input = $req->only($fields);
                        // Verificar si TODOS los campos del grupo tienen valores
                        $allFieldsPresent = count($input) === count($fields) && !in_array(null, $input, true) && !in_array('', $input, true);
                        if ($allFieldsPresent) {
                            // Si vienen datos en este grupo, ejecuta la acción correspondiente
                            switch ($group) {
                                case 'incapacity':
                                    $this->medical_disability($cpath, $id);
                                    break;

                                case 'hospitalization':
                                    if ($input['required_hospitalization'] == 1) {
                                        $this->issueMedicalServiceHospitalization($cpath, $id);
                                    }
                                    break;

                                case 'order_diagnostic':
                                    $this->issueDiagnosticImagingOrder($cpath, $id);
                                    break;

                                case 'referral_specialist':
                                    $this->referral_specialist($cpath, $id);
                                    break;

                                case 'medical_prescription':
                                    $this->emitPrescription($cpath, $id);
                                    break;

                                case 'controlled_medication':
                                    $this->emitControlledPrescription($cpath, $id);
                                    break;
                            }
                        }
                    }
                }
                DB::commit();
                //ME-930 notificar a los auditores médicos
                if ($req->input('referral_specialist') == 1) {
                    $this->notifyMedicalAuditors($activity);
                }
                return redirect('servicio/' . $activity->id);
            } else {
                return response()->json(['error' => 'No se puede actualizar el servicio médico'], 500);
            }
        } catch (\Exception $e) {
            DB::rollback();
            return response()->json(['error' => $e->getMessage()], 500);
        }


        return redirect("servicio/$id/medical_services");
    }

    //ME-930 notificar a los audtiores medicos de remisiones a especialistas
    private function notifyMedicalAuditors($activity)
    {
        $policy = $activity->parent->parent->policy_sort;
        $medicalAuditEmails = User::where('area_id', Area::AUDITOR)->pluck('email')->toArray();

        $docNumberAsegurado = $activity->affiliate->doc_number;
        $nombreAsegurado = ucwords(strtolower($activity->affiliate->first_name . ' ' . $activity->affiliate->last_name));

        //Correo para gestion sort produccion
        if (App::environment('prod')) {
            $medicalAuditEmails[] = "<EMAIL>";
        }else{
            $medicalAuditEmails[] = "<EMAIL>";
        }
        $consecutive  = $policy->formatSortNumber();

        $text = 'Se recibió una solicitud de referencia médica. Favor proceder con el análisis y autorización médica del paciente ' . $nombreAsegurado . ' con número de identificación ' . $docNumberAsegurado . '.';
        // Enviar el correo el documento adjunto
        $mailSent = new SendDocumentDataBase(
            implode(',', $medicalAuditEmails),
            "Solicitud de referencia médica – $nombreAsegurado –  $docNumberAsegurado",
            "<EMAIL>",
            "Aviso de Referencia Médica - Póliza $consecutive",
            [
                "text" =>  $text,
                "sender" => 'MNK seguros'
            ],
            "<EMAIL>",
            [],
            "send_generic_document",
            $activity->client,
            request()->getHost(),
            $activity->id
        );
        
        $result = $mailSent->sendMail();

        //Se quita este correo 

        // //Registramos los datos del correo enviado para la trazabilidad
        // $mailBoardController = new MailBoardController();
        // $mailBoardController->createRegisterMail(
        //     $activity->id,
        //     $activity->service->id, 
        //     $policy->consecutive, 
        //     'Asegurado', 
        //     $nombreAsegurado, 
        //     $docNumberAsegurado, 
        //     'Solicitud de referencia médica' . ' ' .$nombreAsegurado . ' ' .$docNumberAsegurado, 
        //     $text,
        //     $medicalAuditEmails, 
        //     $result,
        //     null
        // );
    }

    //ANULAR SERVICIO
    public function cancelService(Request $request, $cpath, $id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $reason = $request->input('motivo');
        // Buscamos la actividad asociada al servicio médico
        $activity = Activity::where('client_id', $client->id)
            ->where('id', $id)
            ->firstOrFail();
        DB::beginTransaction();
        try {
            $medical_service = $activity->medical_services_sort;
            $medical_service->reason_cancel = $reason;
            $medical_service->save();
            ActionController::create($activity->id, ActionMedicalServiceSort::ANULAR_SERVICIO, 'Servicio Anulado por la acción anular servicio');
            DB::commit();
        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->back()->with('error', 'No existe Servicio');
        }
        return redirect()->back()->with('success', 'Servicio anulado correctamente');

    }

    //REPORTAR NEGACION DE SERVICIO
    public function denialService(Request $req, $cpath, $id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();

        // Buscamos la actividad asociada al servicio médico
        $activity = Activity::where('client_id', $client->id)
            ->where('id', $id)
            ->firstOrFail();
        $request_reason = $req->input('reason_cancel');
        $reason_cancel = [
            0 => 'Evento no laboral',
            1 => 'Diagnósticos de origen común',
            2 => 'Diagnósticos no derivados del evento',
            3 => 'No asegurado',
            4 => 'No pertinencia técnica'

        ];
        DB::beginTransaction();
        try {
            $medical_service = $activity->medical_services_sort;
            $medical_service->reason_cancel = $reason_cancel[$request_reason];
            $medical_service->save();

            $description = "SERVICIO NEGADO";
            $activityAction = ActionController::create($activity->id, ActionMedicalServiceSort::REPORTAR_NEGACION_SERVICIO, $description);
            $activityGisSort = $activity->parent_activity;
            $activityPolicySort = $activityGisSort->parent_activity;
            $gisSort = $activityGisSort->gis_sort;

            $emails = [
                $activityPolicySort->affiliate->email, // Tomador
                $activityGisSort->affiliate->email // Afiliado
            ];
            $emailData = TemplateBuilder::build(
                Templates::EMAIL_NOTIFICATION_OF_MEDICAL_SERVICE_REJECTION,
                [
                    'name' => mb_convert_case(mb_strtolower(optional($activity->affiliate)->full_name ?? '', 'UTF-8'), MB_CASE_TITLE, 'UTF-8'),
                    'case_number' => $gisSort->formatCaseNumber(),
                    'event_date' => Carbon::parse($gisSort->date_accident)->format('d/m/Y'),
                    'reason_cancel' => $reason_cancel[$request_reason],
                ]
            );

            $emailSent = new SendDocumentDataBase(
                implode(',', $emails),
                $emailData['subject'],
                "<EMAIL>",
                $emailData['subject'],
                [
                    "text" => $emailData['body'],
                    "sender" => $emailData['sender']
                ],
                "<EMAIL>",
                [],
                "send_document_db",
                $client,
                request()->getHost(),
                $activity->id,
                $activityAction->id,
                $activity->service_id
            );
            
            $result = $emailSent->sendMail();

            //Registramos los datos del correo enviado para la trazabilidad
            $mailBoardController = new MailBoardController();
            $mailBoardController->createRegisterMail(
                $activity->id,
                $activity->service->id, 
                $activityPolicySort->policy_sort->consecutive, 
                'Asegurado', 
                mb_convert_case(mb_strtolower(optional($activity->affiliate)->full_name ?? '', 'UTF-8'), MB_CASE_TITLE, 'UTF-8'), 
                $activity->affiliate->doc_number, 
                $emailData['subject'], 
                $emailData['body'],
                $emails, 
                $result,
                null
            );

            DB::commit();
        } catch (\Exception $e) {
            DB::rollback();
            return back()->with('message', 'No se pudo realizar la operacion');
        }
        return back()->with('message', 'Operación realizada con éxito');

    }

    // REQ 025 : ME-676 Cambio de proveedor manualmente por el auditor
    public function changeProviderMedicalService(Request $req, $cpath, $id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();

        // Buscamos la actividad asociada al servicio médico
        $activity = Activity::where('client_id', $client->id)
            ->where('id', $id)
            ->firstOrFail();

        //capturar nuevo proveedor
        $newProvider = $req->input('new_provider');

        //buscar si existe el proveedor en la tabla de proveedores
        $provider = Provider::where('id', $newProvider)->first();

        //validar si existe el proveedor
        if(!$provider){
            return back()->with('message', 'No se pudo realizar el cambio del proveedor, ya que el proveedor que deseas cambiar no encuentra en nuestro sistema');
        }

        DB::beginTransaction();
        try {
            // capturar el servicio prestación médica mediante la actividad
            $medical_service = $activity->medical_services_sort;

            //capturar la actividad GIS PADRE a través de la actividad de prestación médica
            $activityGis = Activity::where('id', $activity->parent_id)->first();

            //validar si existe la actividad GIS
            if (!$activityGis) {
                return back()->with('message', 'No se pudo realizar el cambio del proveedor, la prestación médica no está asociada a una servicio GIS');
            }

            //capturar el servicio GIS PADRE a través de la actividad GIS
            $gis = $activityGis->gis_sort;

            //validar si existe el servicio GIS
            if (!$gis) {
                return back()->with('message', 'No se pudo realizar el cambio del proveedor, ya que no se encontró el servicio GIS');
            }

            //validar si existe el servicio prestación médica
            if(!$medical_service){
                return back()->with('message', 'No se pudo realizar el cambio del proveedor, ya que no se encontró el servicio prestación médica');
            }

            //guadar el nuevo proveedor en el servicio GIS PADRE
            $gis->provider_id = $provider->id;
            $gis->save();

            //crear accion de cambio de proveedor para el servicio de prestación médica
            $description = "CAMBIAR PROVEEDOR" . " de " .  optional($medical_service->provider())->name . " a " .$provider->name;
            ActionController::create($activity->id, ActionMedicalServiceSort::CAMBIAR_PROVEEDOR, $description);

            //guardar el nuevo proveedor del servicio de prestación medica
            $medical_service->primary_care_provider = $provider->id;
            $medical_service->save();

            DB::commit();
        }
        catch (\Exception $e) {
            DB::rollback();
            return back()->with('message', 'No se pudo realizar el cambio del proveedor, por el siguente error: ' . $e->getMessage());
        }

        //Retorno a la vista con un mensaje de exito
        return back()->with('message', 'Operación realizada con éxito');
    }

    //ACCION REMITIR INCAPACIDAD MEDICA
    public function medical_disability($cpath, $id)
    {


        $client = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('id', $id)
            ->firstOrFail();
        if ($activity->state_id == StateMedicalServiceSort::VALORACION_REALIZADA) {
            DB::beginTransaction();
            try {
                //Cambio de estado de la actividad
                $activityAction = ActionController::create($activity->id, ActionMedicalServiceSort::EMITIR_INCAPACIDAD_MEDICA, 'Acción Emitir Incapacidad Médica');

                //CAMPOS PARA EL PDF DEL CORREO
                $logoPath = public_path('images/mnk.png');
                $footerPath = public_path('images/mnk_footer.png');

                //OBTENER EL ULTIMO FOLLOWUP DE LA PM
                $lastFollowUp = $activity->medical_services_sort->followUps()->latest()->first();
                $fecha_desde = $lastFollowUp->start_date_of_incapacity;
                $dias = $lastFollowUp->days_of_incapacity;
                $fecha_hasta = Carbon::parse($fecha_desde)->addDays($dias)->startOfDay()->format('Y-m-d');
                $fecha_regreso = $fecha_hasta;
                $fecha_hasta = Carbon::parse($fecha_hasta)->subDay()->format('Y-m-d');
                $gis = $activity->parent;
                $fecha_generacion_documento = optional($gis->gis_sort)->created_at;

                //Aquí buscamos la provincia, canton y distrito (los nombres) esto con el método getLocationNamesFromJson
                $policySortController = new PolicySortController();
                $location = $policySortController->getLocationNamesFromJson(
                    $lastFollowUp->province_incapacity_or_leave,
                    $lastFollowUp->canton_incapacity_or_leave,
                    $lastFollowUp->district_incapacity_or_leave);

                // Aplica la función a cada valor del array $location
                foreach ($location as $key => $value) {
                    $location[$key] = $this->capitalizeWordsWithExceptions($value);
                }

                $pdf = Pdf::loadView('services.medical_services.docs.medical_disability', [
                    'logoPath' => $logoPath,
                    'footerPath' => $footerPath,
                    'activity' => $activity,
                    'fecha_desde' => $fecha_desde,
                    'fecha_hasta' => $fecha_hasta,
                    'fecha_regreso' => $fecha_regreso,
                    'lastFollowUp' => $lastFollowUp,
                    'location' => $location,
                    'fecha_generacion_documento' => $fecha_generacion_documento
                ]);
                $filePathDisability = "documents/medical_disability.blade.php_{$activity->id}_".uniqid().".pdf";
                Storage::disk('s3')->put($filePathDisability, $pdf->output());
                $activityActionDocument = new ActivityActionDocument;
                $activityActionDocument->activity_action_id = $activityAction->id;
                $activityActionDocument->name = 'medical_disability';
                $activityActionDocument->path = $filePathDisability;
                $activityActionDocument->save();

                // Formar el arreglo de archivos con 'type', 'path' y 'name'
                $files = [
                    [
                        'type' => 'pdf',
                        'path' => $filePathDisability,
                        'name' => "Incapacidad médica o licencia.pdf"
                    ],
                ];
             
                $takerName = mb_convert_case(mb_strtolower($gis->parent->affiliate->full_name, 'UTF-8'), MB_CASE_TITLE, 'UTF-8');
                $numPolicySort = $activity->medical_services_sort->num_policy_sort;
                //$numCase = $activity->medical_services_sort->number_case;
                $numCase = $gis->gis_sort->formatCaseNumber();
                $nameWorker = mb_convert_case(mb_strtolower($activity->affiliate->full_name, 'UTF-8'), MB_CASE_TITLE, 'UTF-8');
                $workerDoc = $activity->medical_services_sort->identification;
                $dateCase = $gis->gis_sort->date_accident;
                $fecha_desde = ucfirst(strftime('%A %e de %B del %Y', strtotime($fecha_desde )));
                $fecha_hasta = ucfirst(strftime('%A %e de %B del %Y', strtotime($fecha_hasta )));

                if($dateCase)
                {
                    $dateFormat = ucfirst(strftime('%A %e de %B del %Y', strtotime($dateCase)));
                }
                else{
                    $dateFormat = "Fecha no disponible";
                }

                $text ="
                <p>¡Buen día, <strong>$takerName</strong>!
                    Como parte de nuestro compromiso de mantenerlo informado de las gestiones atendidas al amparo de su póliza del Seguro Obligatorio de Riesgos del Trabajo <strong>$numPolicySort</strong>, hacemos de su conocimiento que en el caso <strong>#$numCase</strong>, a nombre del trabajador(a): <strong>$nameWorker</strong>, número de identificación <strong>$workerDoc</strong>, con fecha de evento <strong>$dateFormat</strong>, la Red Médica de MNK Seguros ha extendido un período de incapacidad temporal que va del <strong>$fecha_desde</strong> hasta el <strong>$fecha_hasta</strong>, que será pagado a la persona trabajadora por semana vencida.
                    
                     Si tiene alguna consulta o necesita más detalles sobre este caso, por favor, contáctenos al <strong>4102-7600</strong>. ¡Será un gusto servirle!

                    Nuestro propósito es garantizar la protección y bienestar de sus colaboradores, así como brindarle la experiencia de servicio que usted y ellos merecen.
                </p>
                ";

                $emails = [$gis->parent->affiliate->email, $gis->affiliate->email];
                $emailString = implode(',', $emails);

                //enviar correo
                $emailRequestInfo = new SendDocumentDataBase(
                    $emailString,
                    "Otorgamiento de incapacidad temporal",
                    "<EMAIL>",
                    "Incapacidad realizada",
                    ["text" => $text,
                        "sender" => 'MNK seguros'],
                    "<EMAIL>",
                    $files,
                    "send_document_db",
                    $client,
                    request()->getHost(),
                    $activity->id,
                    $activityAction->id,
                    $activity->service->id
                );
                
                // Capturar el resultado del envío
                $result = $emailRequestInfo->sendMail();

                //Registramos los datos del correo enviado para la trazabilidad
                $mailBoardController = new MailBoardController();
                $mailBoardController->createRegisterMail(
                    $activity->id,
                    $activity->service->id, 
                    $gis->parent->policy_sort->consecutive, 
                    'Tomador', 
                    $takerName, 
                    $gis->parent->affiliate->doc_number, 
                    'Otorgamiento de incapacidad temporal', 
                    $text,
                    $emails, 
                    $result,
                    null
                );

                //aperturar servicio PE IT-SORT
                $activity_pe_sit = new Activity();
                $activity_pe_sit->parent_id = $activity->id;
                $activity_pe_sit->client_id = $client->id;
                $activity_pe_sit->service_id = Service::SERVICE_PE_IT_SORT_MNK;
                $activity_pe_sit->affiliate_id = $activity->affiliate_id;
                $activity_pe_sit->user_id = Auth::id();
                $activity_pe_sit->state_id = State::REGISTRADO;
                $activity_pe_sit->save();


                //Guardar campos en la tabla de pe sit
                $peItSort=PeItSort::create([
                'name_affiliated_pe' =>$activity->medical_services_sort->name_patient,
                'number_ide_pe' => $activity->medical_services_sort->identification,
                'type_attention' => $lastFollowUp->attention_mode,
                'code_ips_pe' =>$activity->medical_services_sort->primary_care_provider, //este es el id del proveedor
                'service_group_pe' => $activity->medical_services_sort->service_group,
                'type_of_service_pe' => $activity->medical_services_sort->service_mode,
                'medical_document_pe' => $lastFollowUp->document_type_evaluator,
                'medical_identification_pe' =>  $lastFollowUp->identification_number_evaluator,
                'names_surnames_pe' =>$lastFollowUp->full_name_evaluator,
                'medical_record_pe' => $lastFollowUp->medical_registration_number_evaluator,
                'specialty_pe' =>$lastFollowUp->specialty_evaluator,
                'activity_id' => $activity_pe_sit->id,
                'type_it' => ($lastFollowUp->disability_type === 'INI') ? 1 : (($activity->medical_services_sort->disability_type === 'PRO') ? 0 : null),
                'institution_health_pe' => $activity->medical_services_sort->entity_promote,
                'day_1' =>$lastFollowUp->start_date_of_incapacity,
                'number_case'=>$activity->medical_services_sort->number_case,
                'medical_service_follow_up_id' => $lastFollowUp->id
                ]);
                $startDate = $lastFollowUp->start_date_of_incapacity;
                $daysOfIncapacity = $lastFollowUp->days_of_incapacity ;

                // Crear instancia de Carbon a partir de la fecha de inicio
                $start = Carbon::parse($startDate);

                // Sumar los días de incapacidad, restando un día para contar desde la fecha de inicio
                $end = $start->addDays($daysOfIncapacity - 1);

                PeitInabilitySort::create([
                    'pe_it_sort_id' => $peItSort->id,
                    'start_date' => $lastFollowUp->start_date_of_incapacity,
                    'end_date' => $end->format('Y-m-d'),
                    'days_it' => $daysOfIncapacity,
                ]);

                $diagnostics= MedicalServiceDiagnostics::where('medical_service_sort_id',$activity->medical_services_sort->id)->get() ;


                foreach ($diagnostics as $record) {
                    PeItSortCaseDx::insert([
                        'pe_it_sort_id'       => $peItSort->id,
                        'code_cie_10'        => $record->code,     // Map 'code' to 'code_cie_10'
                        'diagnostico_pe'     => $record->description, // Map 'description' to 'diagnostico_pe'
                        'laterality'         => $record->laterality,
                    ]);
                }
                $description = "Acción generada Registrar IT integrado";
                ActionController::create(
                    $activity_pe_sit->id,
                    ActionPeItSort::REGISTRAR_IT_INTEGRADO,
                    $description
                );
                // Agregar documento de incapacidad
                $activityDocumentPeItSort = new ActivityDocument();
                $activityDocumentPeItSort->document_id = 261;
                $activityDocumentPeItSort->activity_id = $activity_pe_sit->id;
                $activityDocumentPeItSort->path = $filePathDisability;
                $activityDocumentPeItSort->save();

                DB::commit();
            } catch (\Exception $e) {
                DB::rollback();
                return response()->json(['error' => $e->getMessage()], 500);
            }
            return back()->with('message', 'Operación realizada con éxito');
        } else {
            return response()->json(['error' => 'No se puede generar la incapacidad medica'], 500);
        }
    }

    //ACCION EMITIR INCAPACIDAD MEDICA para cuando se actualize la fecha fin de incapacidad ME-2594
    public function AfterChangeEndDateMedicalDisability($cpath, $activity_id, $end_date_incapacity)
    {

        //actividad de pe_it sort
        $activityPeItSort = Activity::where('id', $activity_id)->first(); //activity_id es el id del peit pasado como parametro de la función

        //capturar actividad de pe_it sort
        $peItSort = PeItSort::where('activity_id', $activityPeItSort->id)->first();

        //OBTENER EL ULTIMO FOLLOWUP DE LA PM de la nota médica guardada en el servcio PEITSORT
        $lastFollowUp = MedicalServiceFollowUp::where("id", $peItSort->medical_service_follow_up_id)->first();
        if(!empty($lastFollowUp))
        {
            DB::beginTransaction();
            try {

                //CAMPOS PARA EL PDF
                $logoPath = public_path('images/mnk.png');
                $footerPath = public_path('images/mnk_footer.png');

                //actividad del padre de pe_it sort
                $activityMedicalService = Activity::where('id', $activityPeItSort->parent_id)->first();

                $fecha_desde = $lastFollowUp->start_date_of_incapacity;
                $fecha_hasta = $end_date_incapacity; //ejem "2023-01-01"
                $fecha_regreso = Carbon::parse($fecha_hasta)->addDay()->format('Y-m-d');
                $gis = $activityMedicalService->parent;
                $fecha_generacion_documento =  optional($gis->gis_sort)->created_at;
                //Aquí buscamos la provincia, canton y distrito (los nombres) esto con el método getLocationNamesFromJson
                $policySortController = new PolicySortController();
                $location = $policySortController->getLocationNamesFromJson(
                    $lastFollowUp->province_incapacity_or_leave,
                    $lastFollowUp->canton_incapacity_or_leave,
                    $lastFollowUp->district_incapacity_or_leave);

                // Aplica la función a cada valor del array $location
                foreach ($location as $key => $value) {
                    $location[$key] = $this->capitalizeWordsWithExceptions($value);
                }

                //incapacidad médica pdf
                $pdf = Pdf::loadView('services.medical_services.docs.medical_disability', [
                    'logoPath' => $logoPath,
                    'footerPath' => $footerPath,
                    'activity' => $activityMedicalService,
                    'fecha_desde' => $fecha_desde,
                    'fecha_hasta' => $fecha_hasta,
                    'fecha_regreso' => $fecha_regreso,
                    'lastFollowUp' => $lastFollowUp,
                    'location' => $location,
                    'fecha_generacion_documento' => $fecha_generacion_documento
                ]);

                //sobreescribir el archivo existente en la nube
                $filePathDisability = "documents/medical_disability.blade.php_{$activityMedicalService->id}".uniqid().".pdf";
                Storage::disk('s3')->put($filePathDisability, $pdf->output());


                // actualizar el path del documento de incapacidad
                $activityDocumentPeItSort = ActivityDocument::where('activity_id', $activityPeItSort->id)->where('document_id', 261)->first();
                $activityDocumentPeItSort->path = $filePathDisability;
                $activityDocumentPeItSort->save();

                DB::commit();
            } catch (\Exception $e) {
                DB::rollback();
                return response()->json(['error' => $e->getMessage()], 500);
            }
        }
    }

    //ME-2493 Este método permite generar la notifiación (o envío del correo) de la incapacidad medica
    public function email_medical_disability($cpath, $id, $id_last_follow_up)
    {

        $client = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('id', $id)
            ->firstOrFail(); //esta es la actividad de prestación médica

        try {
            //CAMPOS PARA EL PDF DEL CORREO
            $logoPath = public_path('images/mnk.png');
            $footerPath = public_path('images/mnk_footer.png');

            //OBTENER EL FOLLOWUP DE LA PM según el el solicitado
            $lastFollowUp = MedicalServiceFollowUp::where('id', $id_last_follow_up)->firstOrFail();
            $fecha_desde = $lastFollowUp->start_date_of_incapacity;
            $dias = $lastFollowUp->days_of_incapacity;
            $fecha_hasta = Carbon::parse($fecha_desde)->addDays($dias)->startOfDay()->format('Y-m-d');
            $holiday = Holiday::where('holiday', $fecha_hasta)->first();
            $fecha_regreso = $holiday ? Carbon::parse($fecha_hasta)->addDay()->format('Y-m-d') : $fecha_hasta;
            $fecha_hasta = Carbon::parse($fecha_hasta)->subDay()->format('Y-m-d');
            $gis = $activity->parent;
            $fecha_generacion_documento =  optional($gis->gis_sort)->created_at;

            //Aquí buscamos la provincia, canton y distrito (los nombres) esto con el método getLocationNamesFromJson
            $policySortController = new PolicySortController();
            $location = $policySortController->getLocationNamesFromJson(
                $lastFollowUp->province_incapacity_or_leave,
                $lastFollowUp->canton_incapacity_or_leave,
                $lastFollowUp->district_incapacity_or_leave);

            $pdf = Pdf::loadView('services.medical_services.docs.medical_disability', [
                'logoPath' => $logoPath,
                'footerPath' => $footerPath,
                'activity' => $activity,
                'fecha_desde' => $fecha_desde,
                'fecha_hasta' => $fecha_hasta,
                'fecha_regreso' => $fecha_regreso,
                'lastFollowUp' => $lastFollowUp,
                'location' => $location,
                'fecha_generacion_documento' => $fecha_generacion_documento
            ]);

            $filePathDisability = "documents/medical_disability.blade.php_{$activity->id}".uniqid().".pdf";
            Storage::disk('s3')->put($filePathDisability, $pdf->output());

            // Formar el arreglo de archivos con 'type', 'path' y 'name'
            $files = [
                [
                    'type' => 'pdf',
                    'path' => $filePathDisability,
                    'name' => "Incapacidad médica o licencia.pdf"
                ],
            ];

            $takerName = mb_convert_case(mb_strtolower($gis->parent->affiliate->full_name, 'UTF-8'), MB_CASE_TITLE, 'UTF-8');
            $numPolicySort = $activity->medical_services_sort->num_policy_sort;
            $numCase = $gis->gis_sort->formatCaseNumber();
            $nameWorker = mb_convert_case(mb_strtolower($activity->affiliate->full_name, 'UTF-8'), MB_CASE_TITLE, 'UTF-8');
            $workerDoc = $activity->medical_services_sort->identification;
            $dateCase = $gis->gis_sort->date_accident;
            $fecha_desde = ucfirst(strftime('%A %e de %B del %Y', strtotime($fecha_desde )));
            $fecha_hasta = ucfirst(strftime('%A %e de %B del %Y', strtotime($fecha_hasta )));

            if($dateCase)
            {
                $dateFormat = ucfirst(strftime('%A %e de %B del %Y', strtotime($dateCase)));
            }
            else{
                $dateFormat = "Fecha no disponible";
            }

            $text ="
                <p>¡Buen día, <strong>$takerName</strong>!
                    Como parte de nuestro compromiso de mantenerlo informado de las gestiones atendidas al amparo de su póliza del Seguro Obligatorio de Riesgos del Trabajo <strong>$numPolicySort</strong>, hacemos de su conocimiento que en el caso <strong>#$numCase</strong>, a nombre del trabajador(a): <strong>$nameWorker</strong>, número de identificación <strong>$workerDoc</strong>, con fecha de evento <strong>$dateFormat</strong>, la Red Médica de MNK Seguros ha extendido un período de incapacidad temporal que va del <strong>$fecha_desde</strong> hasta el <strong>$fecha_hasta</strong>, que será pagado a la persona trabajadora por semana vencida.
                    
                     Si tiene alguna consulta o necesita más detalles sobre este caso, por favor, contáctenos al <strong>4102-7600</strong>. ¡Será un gusto servirle!

                    Nuestro propósito es garantizar la protección y bienestar de sus colaboradores, así como brindarle la experiencia de servicio que usted y ellos merecen.
                </p>
                ";

            $emails = [$gis->parent->affiliate->email, $gis->affiliate->email, '<EMAIL>'];
            $emailString = implode(',', $emails);

            //enviar correo
            $emailRequestInfo = new SendDocumentDataBase(
                $emailString,
                "Otorgamiento de incapacidad temporal",
                "<EMAIL>",
                "Incapacidad realizada",
                ["text" => $text,
                    "sender" => 'MNK seguros'],
                "<EMAIL>",
                $files,
                "send_document_db",
                $client,
                request()->getHost(),
                $activity->id,
                '',
                $activity->service->id
            );
            
            // Capturar el resultado del envío
            $result = $emailRequestInfo->sendMail();

            //Registramos los datos del correo enviado para la trazabilidad
            $mailBoardController = new MailBoardController();
            $mailBoardController->createRegisterMail(
                $activity->id,
                $activity->service->id, 
                $gis->parent->policy_sort->consecutive, 
                'Tomador', 
                $takerName, 
                $gis->parent->affiliate->doc_number, 
                'Otorgamiento de incapacidad temporal', 
                $text,
                $emails, 
                $result,
                null
            );

            return response()->json([
                'valid' => true,
                'message' => 'Correo enviado correctamente'
            ]);
        }
        catch (Exception $e) {
            DB::rollBack();
            return response()->json([
                'valid' => false,
                'message' => $e->getMessage()
            ]);
        }

    }


    // ACCION EMITIR PRESTACION MEDICA HOSPITALIZACION con tiempo
    public function medicalDisabilityTime(Request $request, $cpath)
    {
        $activity_id = $request->input('activity_id') ?? null;
        // Verificar si se proporcionó un número de prefactura
        if (!$activity_id) {
            return response()->json([
                'valid' => false,
                'message' => 'No se proporcionó un número de actividad.'
            ]);
        }

        //Iniciamos la transacción
        DB::beginTransaction();
        try {
            //capturar la actividad
            $activity = Activity::where('id', $activity_id)
                ->firstOrFail();

            //verificar si la actividad existe
            if(empty($activity))
            {
                return response()->json([
                    'valid' => false,
                    'message' => 'No se encontró la actividad.'
                ]);
            }

            // Obtener la lista de actividades desde la sesión
            $lastEnableDisability = session()->get('time_enable_disability', []);

            // Si la variable de sesión es null o no es un array, inicializarla como array vacío
            if (!is_array($lastEnableDisability)) {
                $lastEnableDisability = [];
            }

            // Obtener la fecha y hora actual
            $now = Carbon::now();

            if (empty($lastEnableDisability)) {
                // Si no hay datos en la sesión, inicializar con la primera actividad
                $lastEnableDisability[] = ['id' => $activity_id, 'time' => $now];

                // Guardar en la sesión
                session(['time_enable_disability' => $lastEnableDisability]);

                //emitir la hospitalización
                $this->medical_disability($cpath, $activity_id);


            } else {
                // Buscar si la actividad ya está registrada en la sesión
                $foundKey = null;
                foreach ($lastEnableDisability as $key => $activity) {
                    if (!is_array($activity) || !isset($activity['id'])) {
                        continue; // Evita errores si hay datos corruptos
                    }

                    if ($activity['id'] === $activity_id) {
                        $foundKey = $key;
                        break;
                    }
                }
                if ($foundKey === null) {
                    // Si la actividad no está en la sesión, agregarla
                    $lastEnableDisability[] = ['id' => $activity_id, 'time' => $now];

                    // Guardar en la sesión
                    session(['time_enable_disability' => $lastEnableDisability]);

                    // Emitir la incapacidad
                    $this->medical_disability($cpath, $activity_id);
                } else {
                    // Si la actividad existe, validar si han pasado más de 5 minutos
                    if (Carbon::parse($lastEnableDisability[$foundKey]['time'])->diffInMinutes($now) > 5) {
                        // Actualizar el tiempo
                        $lastEnableDisability[$foundKey]['time'] = $now;

                        // Guardar en la sesión
                        session(['time_enable_disability' => $lastEnableDisability]);

                        // Emitir la incapacidad
                        $this->medical_disability($cpath, $activity_id);
                    } else {
                        return response()->json([
                            'valid' => false,
                            'message' => 'Debe esperar al menos 5 minutos antes de volver a emitir la incapacidad médica de este servicio.'
                        ]);
                    }
                }
            }

            //guardar transacción
            DB::commit();

            return response()->json(['valid' => true,
                'message' => 'Se ha emitido la incapacidad médica correctamente']);

        }
        catch (\Exception $e) {
            DB::rollback();
            return response()->json(['valid' => false, 'message' => 'Ha ocurrido un error al emitir la incapacidad']);
        }

    }

    //
    public function medicalHospitalizationTime(Request $request, $cpath)
    {
        $activity_id = $request->input('activity_id') ?? null;
        // Verificar si se proporcionó un número de prefactura
        if (!$activity_id) {
            return response()->json([
                'valid' => false,
                'message' => 'No se proporcionó un número de actividad.'
            ]); 
        }

        //Iniciamos la transacción
        DB::beginTransaction();
        try {
            //capturar la actividad
            $activity = Activity::where('id', $activity_id)
                ->firstOrFail();


            //verificar si la actividad existe
            if(empty($activity))
            {
                return response()->json([
                    'valid' => false,
                    'message' => 'No se encontró la actividad.'
                ]);
            }

            // Obtener la lista de actividades desde la sesión
            $lastEnableHospitalization = session()->get('time_enable_hospitalization', []);

            // Si la variable de sesión es null o no es un array, inicializarla como array vacío
            if (!is_array($lastEnableHospitalization)) {
                $lastEnableHospitalization = [];
            }

            // Obtener la fecha y hora actual
            $now = Carbon::now();

            if (empty($lastEnableHospitalization)) {
                // Si no hay datos en la sesión, inicializar con la primera actividad
                $lastEnableHospitalization[] = ['id' => $activity_id, 'time' => $now];

                // Guardar en la sesión
                session(['time_enable_hospitalization' => $lastEnableHospitalization]);

                //emitir la hospitalización
                $this->issueMedicalServiceHospitalization($cpath, $activity_id);


            } else {
                // Buscar si la actividad ya está registrada en la sesión
                $foundKey = null;
                foreach ($lastEnableHospitalization as $key => $activity) {
                    if (!is_array($activity) || !isset($activity['id'])) {
                        continue; // Evita errores si hay datos corruptos
                    }

                    if ($activity['id'] === $activity_id) {
                        $foundKey = $key;
                        break;
                    }
                }
                if ($foundKey === null) {
                    // Si la actividad no está en la sesión, agregarla
                    $lastEnableHospitalization[] = ['id' => $activity_id, 'time' => $now];

                    // Guardar en la sesión
                    session(['time_enable_hospitalization' => $lastEnableHospitalization]);

                    // Emitir la incapacidad
                    $this->issueMedicalServiceHospitalization($cpath, $activity_id);
                } else {
                    // Si la actividad existe, validar si han pasado más de 5 minutos
                    if (Carbon::parse($lastEnableHospitalization[$foundKey]['time'])->diffInMinutes($now) > 5) {
                        // Actualizar el tiempo
                        $lastEnableHospitalization[$foundKey]['time'] = $now;

                        // Guardar en la sesión
                        session(['time_enable_hospitalization' => $lastEnableHospitalization]);

                        // Emitir la incapacidad
                        $this->issueMedicalServiceHospitalization($cpath, $activity_id);
                    } else {
                        return response()->json([
                            'valid' => false,
                            'message' => 'Debe esperar al menos 5 minutos antes de volver a emitir la hospitalización de este servicio.'
                        ]);
                    }
                }
            }

            //guardar transacción
            DB::commit();

            return response()->json(['valid' => true,
                'message' => 'Se ha emitido la hospitalización correctamente']);


        }
        catch (\Exception $e) {
            DB::rollback();
            return response()->json(['valid' => false, 'message' => 'Ha ocurrido un error al emitir la hospitalización']);
        }

    }
    public function medicalDiagnosticImagingOrderTime(Request $request, $cpath)
    {
        $activity_id = $request->input('activity_id') ?? null;
        // Verificar si se proporcionó un número de prefactura
        if (!$activity_id) {
            return response()->json([
                'valid' => false,
                'message' => 'No se proporcionó un número de actividad.'
            ]);
        }

        //Iniciamos la transacción
        DB::beginTransaction();
        try {
            //capturar la actividad
            $activity = Activity::where('id', $activity_id)
                ->firstOrFail();

            //verificar si la actividad existe
            if(empty($activity))
            {
                return response()->json([
                    'valid' => false,
                    'message' => 'No se encontró la actividad.'
                ]);
            }

            // Obtener la lista de actividades desde la sesión
            $lastEnableDiagnosticImagingOrder = session()->get('time_enable_diagnostic_imaging_order', []);

            // Si la variable de sesión es null o no es un array, inicializarla como array vacío
            if (!is_array($lastEnableDiagnosticImagingOrder)) {
                $lastEnableDiagnosticImagingOrder = [];
            }

            // Obtener la fecha y hora actual
            $now = Carbon::now();

            if (empty($lastEnableDiagnosticImagingOrder)) {
                // Si no hay datos en la sesión, inicializar con la primera actividad
                $lastEnableDiagnosticImagingOrder[] = ['id' => $activity_id, 'time' => $now];

                // Guardar en la sesión
                session(['time_enable_diagnostic_imaging_order' => $lastEnableDiagnosticImagingOrder]);

                //emitir la hospitalización
                $this->issueDiagnosticImagingOrder($cpath, $activity_id, true);


            } else {
                // Buscar si la actividad ya está registrada en la sesión
                $foundKey = null;
                foreach ($lastEnableDiagnosticImagingOrder as $key => $activity) {
                    if (!is_array($activity) || !isset($activity['id'])) {
                        continue; // Evita errores si hay datos corruptos
                    }

                    if ($activity['id'] === $activity_id) {
                        $foundKey = $key;
                        break;
                    }
                }
                if ($foundKey === null) {
                    // Si la actividad no está en la sesión, agregarla
                    $lastEnableDiagnosticImagingOrder[] = ['id' => $activity_id, 'time' => $now];

                    // Guardar en la sesión
                    session(['time_enable_diagnostic_imaging_order' => $lastEnableDiagnosticImagingOrder]);

                    // Emitir la incapacidad
                    $this->issueDiagnosticImagingOrder($cpath, $activity_id, true);
                } else {
                    // Si la actividad existe, validar si han pasado más de 5 minutos
                    if (Carbon::parse($lastEnableDiagnosticImagingOrder[$foundKey]['time'])->diffInMinutes($now) > 5) {
                        // Actualizar el tiempo
                        $lastEnableDiagnosticImagingOrder[$foundKey]['time'] = $now;

                        // Guardar en la sesión
                        session(['time_enable_diagnostic_imaging_order' => $lastEnableDiagnosticImagingOrder]);

                        // Emitir la incapacidad
                        $this->issueDiagnosticImagingOrder($cpath, $activity_id, true);
                    } else {
                        return response()->json([
                            'valid' => false,
                            'message' => 'Debe esperar al menos 5 minutos antes de volver a emitir orden de imágenes diagnósticas de este servicio.'
                        ]);
                    }
                }
            }

            //guardar transacción
            DB::commit();

            return response()->json(['valid' => true,
                'message' => 'Se ha emitido orden de imágenes diagnósticas correctamente']);


        }
        catch (\Exception $e) {
            DB::rollback();
            return response()->json(['valid' => false, 'message' => 'Ha ocurrido un error al emitir orden de imágenes diagnósticas']);
        }

    }


    public function medical_disability_test($cpath, $id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('client_id', $client->id)
            ->where('id', $id)
            ->firstOrFail();
        //CAMPOS PARA EL PDF DEL CORREO
        $logoPath = public_path('images/mnk.png');
        $footerPath = public_path('images/mnk_footer.png');

        //OBTENER EL ULTIMO FOLLOWUP DE LA PM
        $lastFollowUp = $activity->medical_services_sort->followUps()->latest()->first();
        $fecha_desde = $lastFollowUp->start_date_of_incapacity;
        $dias = $lastFollowUp->days_of_incapacity;
        $fecha_hasta = Carbon::parse($fecha_desde)->addDays($dias)->startOfDay()->format('Y-m-d');
        $holiday = Holiday::where('holiday', $fecha_hasta)->first();
        $fecha_regreso = $holiday ? Carbon::parse($fecha_hasta)->addDay()->format('Y-m-d') : $fecha_hasta;
        $fecha_hasta = Carbon::parse($fecha_hasta)->subDay()->format('Y-m-d');
        $gis = $activity->parent;
        $fecha_generacion_documento =  optional($gis->gis_sort)->created_at;

        //Aquí buscamos la provincia, canton y distrito (los nombres) esto con el método getLocationNamesFromJson
        $policySortController = new PolicySortController();
        $location = $policySortController->getLocationNamesFromJson(
            $lastFollowUp->province_incapacity_or_leave,
            $lastFollowUp->canton_incapacity_or_leave,
            $lastFollowUp->district_incapacity_or_leave);

        // Aplica la función a cada valor del array $location
        foreach ($location as $key => $value) {
            $location[$key] = $this->capitalizeWordsWithExceptions($value);
        }

        $pdf = Pdf::loadView('services.medical_services.docs.medical_disability', [
            'logoPath' => $logoPath,
            'footerPath' => $footerPath,
            'activity' => $activity,
            'fecha_desde' => $fecha_desde,
            'fecha_hasta' => $fecha_hasta,
            'fecha_regreso' => $fecha_regreso,
            'lastFollowUp' => $lastFollowUp,
            'location' => $location,
            'fecha_generacion_documento' => $fecha_generacion_documento
        ]);

        return $pdf->stream('incapacidad_medica.pdf');
    }

    //ASIGNAR SERVICIO ATENCIÓN PRIMARIA
    /**
     * @throws \Exception
     */
    public function assignPrimaryCareService($id)
    {

        DB::beginTransaction();
        try {
            //TODO: Recibir servicio / actividad, de manera automática, cuando en el servicio GIS se ejecute la acción: "Aviso de accidente o enfermedad"
            $medicalServiceActivity = Activity::where('id', $id)->firstOrFail();
            $medicalService = MedicalServicesSort::where('activity_id', $medicalServiceActivity->id)->firstOrFail();

            //TODO: Teniendo en cuenta lo anterior, RenApp debe validar lo siguiente
            //Validar si el tomador ya tiene asignado un proveedor desde la poliza
            $gisSeviceActivity = Activity::where('id', $medicalServiceActivity->parent_id)->first();
            $gisSort = GisSort::where('activity_id', $gisSeviceActivity->id)->first();
            $policySort = PolicySort::where('activity_id', $gisSeviceActivity->parent_id)->first();

            //Validar si el tomador tiene asignado un proveedor VIP


            //POR EL MOMENTO NO SE VALIDA EL PROVEEDOR VIP
            //Json de GAM
            $province = $gisSort->province;
            $canton = $gisSort->canton;
            $district = $gisSort->district;
            $jsonSource = '/js/GAM.json';
            $json = file_get_contents(public_path($jsonSource));
            $gamArray = json_decode($json, true);
            $isGam = null;
            foreach ($gamArray['Hoja1'] as $row) {
                if ($row['Provincia'] == $province && $row['Cantón'] == $canton && $row['Distrito'] == $district) {
                    $isGam = $row['ES GAM'];
                    break;
                }
            }

            $nowDate = Carbon::now()->toDateString(); // Fecha actual: 'Y-m-d'
            $nowTime = Carbon::now()->format('H:i:s'); // Hora actual: 'H:i:s'

            //Si el siniestro ocurrió fuera del GAM (Gran Área Metropolitana) quedará el proveedor 'Addiuva' por defecto
            if ($isGam === 'NO') {
                //$medicalService->primary_care_provider = Provider::PROVIDER_ADDIUVA;

                     //Traen datos de los Providers que cumplen la condicionde no gam y fecha estipulada
                     $noGamProviderIds = Provider::where('no_gam', 1)
                     ->where('active', 1)
                     ->where(function ($query) use ($nowDate) {
                         $query->where(function ($q) use ($nowDate) {
                             // Validar fecha solo si change_date = 1
                             $q->where('change_date', 1)
                               ->whereDate('start_date_validity', '<=', $nowDate)
                               ->whereDate('end_date_validity', '>=', $nowDate);
                         })
                         ->orWhere(function ($q) {
                             // No validar fecha si change_date = 0 o campos vacíos
                             $q->where('change_date', 0);
                         });
                     })
                     ->where(function ($query) use ($nowTime) {
                         $query->where(function ($q) use ($nowTime) {
                             // Validar horas solo si stipulated_hours = 1
                             $q->where('stipulated_hours', 1)
                               ->whereTime('start_time', '<=', $nowTime)
                               ->whereTime('end_time', '>=', $nowTime);
                         })
                         ->orWhere(function ($q) {
                             // No validar hora si stipulated_hours = 0 o campos vacíos
                             $q->where('stipulated_hours', 0);
                         });
                     }) // <- esta llave faltaba cerrar
                     ->pluck('id')
                     ->toArray();

                     if (empty($noGamProviderIds)) {
                        throw new \Exception("No hay proveedores disponibles de tipo NO GAM para este horario.");
                    }


                     // Obtener los últimos N registros con esos proveedores
                    $lastAssignedProviders = MedicalServicesSort::whereIn('primary_care_provider', $noGamProviderIds)
                        ->where('is_gam', 0)
                        ->orderBy('created_at', 'desc') 
                        ->limit(count($noGamProviderIds)) 
                        ->pluck('primary_care_provider')
                        ->toArray();

                       
                    //  Buscar cuál proveedor no ha sido usado en los últimos N
                    $availableProviders = array_diff($noGamProviderIds, $lastAssignedProviders);

                    //  Si todos ya fueron usados recientemente, reiniciamos (empezamos una nueva ronda)
                    if (empty($availableProviders)) {
                        $availableProviders = $noGamProviderIds;
                    }


                    //  Obtener ID del proveedor seleccionado equitativamente
                    $selectedProviderId = collect($availableProviders)->random();
                    $medicalService->primary_care_provider = $selectedProviderId;


                $medicalService->type_of_care = "ATENCION PRIMARIA";
                $medicalService->is_gam = 0;

            } //Si el siniestro ocurrió dentro del GAM (Gran Área Metropolitana) se brindara los proveedores adecuados
            else {

                //Traen datos de los Providers que cumplen la condicionde gam y fecha estipulada
                $gamProviderIds = Provider::where('gam', 1)
                ->where('active', 1)
                ->where(function ($query) use ($nowDate, $nowTime) {
                    $query->where(function ($q) use ($nowDate) {
                    // Validar fecha solo si change_date = 1
                    $q->where('change_date', 1)
                    ->whereDate('start_date_validity', '<=', $nowDate)
                    ->whereDate('end_date_validity', '>=', $nowDate);
                    })
                    ->orWhere(function ($q) {
                    // No validar fecha si change_date = 0 o campos vacíos
                    $q->where('change_date', 0);
                    });
                })
                ->where(function ($query) use ($nowTime) {
                    $query->where(function ($q) use ($nowTime) {
                        // Validar horas solo si stipulated_hours = 1
                        $q->where('stipulated_hours', 1)
                        ->whereTime('start_time', '<=', $nowTime)
                        ->whereTime('end_time', '>=', $nowTime);
                    })
                ->orWhere(function ($q) {
                // No validar hora si stipulated_hours = 0 o campos vacíos
                    $q->where('stipulated_hours', 0);
                });
                })
                ->pluck('id')
                ->toArray();

                if (empty($gamProviderIds)) {
                    throw new \Exception("No hay proveedores disponibles de tipo GAM para este horario.");
                }


                // Obtener los últimos N registros con esos proveedores
                $lastAssignedProviders = MedicalServicesSort::whereIn('primary_care_provider', $gamProviderIds)
                ->where('is_gam', 1)
                ->orderBy('created_at', 'desc') 
                ->limit(count($gamProviderIds)) 
                ->pluck('primary_care_provider')
                ->toArray();

                // Buscar cuál proveedor no ha sido usado en los últimos N
                $availableProviders = array_diff($gamProviderIds, $lastAssignedProviders);

                //  Si todos ya fueron usados recientemente, reiniciamos (empezamos una nueva ronda)
                if (empty($availableProviders)) {
                    $availableProviders = $gamProviderIds;
                }


                //  Obtener ID del proveedor seleccionado equitativamente
                $selectedProviderId = collect($availableProviders)->random();



                $medicalService->primary_care_provider = $selectedProviderId;

                $medicalService->type_of_care = "ATENCION PRIMARIA";
                $medicalService->is_gam = 1;
            }

            $medicalService->save();


            //TODO: Enviar una alerta al proveedor informándole sobre el servicio que se le ha asignado - PENDIENTE

            //TODO: Cambiar estado actividad
            ActionController::create($medicalServiceActivity->id, ActionMedicalServiceSort::ASIGNAR_SERVICIO_ATENCION_PRIMARIA, 'Servicio asignado por la acción asignar servicio atención primaria');

            DB::commit();

            return $medicalService->primary_care_provider;
        } catch (\Exception $e) {
            DB::rollBack();
    // Puedes personalizar la respuesta aquí
    return response()->json([
        'message' => $e->getMessage()
    ], 400);
        }
    }

    //ACCION EMITIR PRESTACION MEDICA HOSPITALIZACION
    public function issueMedicalServiceHospitalization($cpath, $id)
    {

        $client = Client::where('path', $cpath)->firstOrFail();
        DB::beginTransaction();
        try {

            //actividad de prestación médica
            $medicalServiceActivity = Activity::where('id', $id)->where('client_id', $client->id)->firstOrFail();

            //servicio de la prestación médica
            $medicalServiceSort = MedicalServicesSort::where('activity_id', $medicalServiceActivity->id)->firstOrFail();

            //TODO: Generar documento para la orden de emisión de cirugía y /o hospitalización
            // Generar el PDF
            $document = 'issueMedicalServiceHospitalizationMedical';
            $action_id = ActionMedicalServiceSort::EMITIR_PRESTACION_MEDICA_HOSPITALIZACION;

            $pdf = PDF::loadView("services.medical_services.docs.{$document}_pdf", ['value' => "pendiente"]); //modificar

            // Guardar el PDF en S3
            Storage::disk('s3')
                ->put("activity_action_document/{$document}_{$action_id}.pdf", $pdf->output());

            //TODO: Crear historial de acciones
            $description = "EMITIR PRESTACIÓN MÉDICA HOSPITALIZACIÓN";
            $activityActionsCreated = ActionController::create($medicalServiceActivity->id, $action_id, $description);

            $activityActionDocument = new ActivityActionDocument();
            $activityActionDocument->activity_action_id = $activityActionsCreated->id;
            $activityActionDocument->name = $document;
            $activityActionDocument->path = "activity_action_document/{$document}_{$action_id}.pdf";
            $activityActionDocument->save();

            //TODO: aperturar de manera automática un nuevo servicio ""Prestación Médica SORT"",
            // y generar la acción REPORTAR SOLICITUD PRESTACIÓN MÉDICA"

            $this->reportMedicalServiceRequest($cpath, $medicalServiceActivity->id, $activityActionDocument->name, $activityActionDocument->path,1, $medicalServiceSort->primary_care_provider);

            DB::commit();

            return back()->with('message', 'Operación realizada con éxito');

        } catch (\Exception $e) {
            DB::rollBack();
            // Retornar un mensaje de error o manejar la excepción
            return response()->json([
                'error' => 'Ocurrió un error al guardar los datos',
                'code' => $e->getCode(),
                'message' => $e->getMessage()
            ], 500);
        }
    }

    //ACCION EMITIR ORDEN DE IMAGENES DIAGNOSTICAS
    public function issueDiagnosticImagingOrder($cpath, $id, $sendEmail = false)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        DB::beginTransaction();
        try {

            //actividad de prestación médica
            $medicalServiceActivity = Activity::where('id', $id)->where('client_id', $client->id)->firstOrFail();

            //servicio de la prestación médica
            $medicalServiceSort = MedicalServicesSort::where('activity_id', $medicalServiceActivity->id)->firstOrFail();

            //TODO: Generar documento para la orden de emisión imágenes diagnósticas
            // Generar el PDF
            $document = 'issueDiagnosticImagingOrderMedical';
            $action_id = ActionMedicalServiceSort::EMITIR_ORDEN_DE_IMAGENES_DIAGNOSTICAS;

            $pdf = PDF::loadView("services.medical_services.docs.{$document}_pdf", ['value' => "pendiente"]); //modificar
            $filePath = "activity_action_document/{$document}_{$action_id}.pdf";
            // Guardar el PDF en S3
            Storage::disk('s3')
                ->put($filePath, $pdf->output());

            $files[] = [
                'type' => 'pdf',
                'path' => $filePath,
                'name' => 'imagenes_diagnosticas.pdf',
            ];


            //TODO: Crear historial de acciones
            $description = "EMITIR ORDEN DE IMÁGENES DIAGNÓSTICAS";

            $activityActionsCreated = ActionController::create($medicalServiceActivity->id, $action_id, $description);

            $activityActionDocument = new ActivityActionDocument();
            $activityActionDocument->activity_action_id = $activityActionsCreated->id;
            $activityActionDocument->name = $document;
            $activityActionDocument->path = "activity_action_document/{$document}_{$action_id}.pdf";
            $activityActionDocument->save();


            //TODO: aperturar de manera automática un nuevo servicio ""Prestación Médica SORT"",
            // y generar la acción REPORTAR SOLICITUD PRESTACIÓN MÉDICA"
            $new_activity = $this->reportMedicalServiceRequest($cpath,
                $medicalServiceActivity->id,
                $activityActionDocument->name,
                $activityActionDocument->path,2, $medicalServiceSort->primary_care_provider);

            //ULTIMO FOLLOWUP DE LA PM
            $pm_img = MedicalServicesSort::where('activity_id', $new_activity)->first();
            $lastFollowUp = $medicalServiceActivity->medical_services_sort->followUps()->latest()->first();
            //PRIMER FOLLOW DE LA NUEVA PM IMG
            $new_follow_img = new MedicalServiceFollowUp();
            $new_follow_img->fill($lastFollowUp->toArray());
            $new_follow_img->medical_services_sort_id = $pm_img->id;
            $new_follow_img->follow_up_number = 1;
            $new_follow_img->next_follow_up_date = null;
            $new_follow_img->save();
            //CREAR DIAGNOSTICOS DE LA PM IMG
            $diagnostics_img = $lastFollowUp->diagnosticsImages;
            $diagnostic_imaging = '';
            foreach ($diagnostics_img as $diagnostic) {
                $diagnostic_imaging .= "• $diagnostic->cod, $diagnostic->description. \n"; //Se debe quitar un espacio en el cuerpo del correo para evitar doble espaciado
                $new_diagnostic_img = new MedicalServiceImageDiagnostics();
                $new_diagnostic_img->fill($diagnostic->toArray());
                $new_diagnostic_img->medical_service_sort_id = $pm_img->id;
                $new_diagnostic_img->medical_service_follow_up_id = $new_follow_img->id;
                $new_diagnostic_img->save();
            }

            if($sendEmail) {
                $activityGisSort = $medicalServiceActivity->parent_activity;
                $gisSort = $activityGisSort->gis_sort;

                $emails[] = $activityGisSort->affiliate->email;
                if(Auth::user()->isProvider()) {
                    $emails[] = Auth::user()->email;
                }

                $emailData = TemplateBuilder::build(
                    Templates::DIAGNOSTIC_IMAGING_ORDER,
                    [
                        'name' => mb_convert_case(mb_strtolower($activityGisSort->affiliate->first_name ?? ''), MB_CASE_TITLE, "UTF-8"),
                        'number_case' => $gisSort->consecutive_gis,
                        'event_date' => Carbon::parse($gisSort->date_accident)->format('d/m/Y'),
                        'diagnostic_imaging' => $diagnostic_imaging,
                        'place' => 'xxxx',
                        'location' => 'xxxx'
                    ]
                );


                //enviar correo
                $emailRequestInfo = new SendDocumentDataBase(
                    implode(',', $emails),
                    $emailData['subject'],
                    "<EMAIL>",
                    $emailData['subject'],
                    [
                        "text" => $emailData['body'],
                        "sender" => $emailData['sender']
                    ],
                    "<EMAIL>",
                    [], // TODO: cuando issueDiagnosticImagingOrderMedical_pdf este completo, remplazar por $files
                    "send_document_db",
                    $client,
                    request()->getHost(),
                    $medicalServiceActivity->id,
                    $activityActionsCreated->id,
                    $medicalServiceActivity->service->id
                );
                
                 // Capturar el resultado del envío
                $result = $emailRequestInfo->sendMail();

                //Registramos los datos del correo enviado para la trazabilidad
                $mailBoardController = new MailBoardController();
                $mailBoardController->createRegisterMail(
                    $medicalServiceActivity->id,
                    $medicalServiceActivity->service->id, 
                    $activityGisSort->parent->policy_sort->consecutive, 
                    //Asegurado
                    'Asegurado', 
                    //Nombre asegurado
                    mb_convert_case(mb_strtolower($activityGisSort->affiliate->first_name ?? ''), MB_CASE_TITLE, "UTF-8"), 
                    //identifcacion asegurado
                    $activityGisSort->affiliate->doc_number, 
                    $emailData['subject'], 
                    $emailData['body'],
                    $emails, 
                    $result,
                    null
                );
            }

            DB::commit();

            return back()->with('message', 'Operación realizada con éxito');

        } catch (\Exception $e) {
            DB::rollBack();
            // Retornar un mensaje de error o manejar la excepción
            return response()->json([
                'error' => 'Ocurrió un error al guardar los datos',
                'code' => $e->getCode(),
                'message' => $e->getMessage()
            ], 500);
        }
    }

    //EMITIR FORMULA DE MEDICAMENTOS
    public function emitPrescription($cpath, $id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        DB::beginTransaction();
        try {
            $medicalServiceActivity = Activity::where('id', $id)->where('client_id', $client->id)->firstOrFail();

            $action_id = ActionMedicalServiceSort::EMITIR_FORMULA_DE_MEDICAMENTOS;

            if ($medicalServiceActivity->service_id == Service::SERVICE_MEDICAL_SERVICES_SECONDARY_CARE_SORT_MNK){
                $action_id = ActionMedicalServiceSecondarySort::EMITIR_FORMULA_DE_MEDICAMENTOS;
            }

            //TODO: Generar documento fórmula de medicamentos
            // Generar el PDF
            $document = 'emitPrescriptionMedical';

            $pdf = PDF::loadView("services.medical_services.docs.{$document}_pdf", ['value' => "pendiente"]);

            // Guardar el PDF en S3
            Storage::disk('s3')
                ->put("activity_action_document/{$document}_{$action_id}.pdf", $pdf->output());

            //TODO: Crear historial de acciones
            $description = "EMITIR FORMULA DE MEDICAMENTOS";
            $activityActionsCreated = ActionController::create($medicalServiceActivity->id, $action_id, $description);

            $activityActionDocument = new ActivityActionDocument();
            $activityActionDocument->activity_action_id = $activityActionsCreated->id;
            $activityActionDocument->name = $document;
            $activityActionDocument->path = "activity_action_document/{$document}_{$action_id}.pdf";
            $activityActionDocument->save();

            //TODO: Entregar los medicamentos prescritos al paciente según la fórmula generada.


            //TODO: aperturar de manera automática un nuevo servicio "MEDICAMENTOS SORT",
            $medication_activity = new MedicationServicesController();
            $new_activity = $medication_activity->reportMedicationRequest($cpath, $medicalServiceActivity->id, $action_id, $document, $activityActionDocument->path);

            DB::commit();

            return back()->with('message', 'Operación realizada con éxito');

        } catch (\Exception $e) {
            DB::rollBack();
            // Retornar un mensaje de error o manejar la excepción
            return response()->json([
                'error' => 'Ocurrió un error al guardar los datos',
                'code' => $e->getCode(),
                'message' => $e->getMessage(),
                'line' => $e->getLine()
            ], 500);
        }
    }

    //EMITIR FORMULA DE MEDICAMENTOS CONTROLADOS
    public function emitControlledPrescription($cpath, $id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        DB::beginTransaction();
        try {

            $medicalServiceActivity = Activity::where('id', $id)->where('client_id', $client->id)->firstOrFail();

            //TODO: Generar documento fórmula de medicamentos controlados
            // Generar el PDF
            $document = 'emitControlledPrescriptionMedical';
            $action_id = ActionMedicalServiceSort::EMITIR_FORMULA_DE_MEDICAMENTOS_CONTROLADO;

            $pdf = PDF::loadView("services.medical_services.docs.{$document}_pdf", ['value' => "pendiente"]); //modificar

            // Guardar el PDF en S3
            Storage::disk('s3')
                ->put("activity_action_document/{$document}_{$action_id}.pdf", $pdf->output());

            //TODO: Crear historial de acciones
            $description = "EMITIR FORMULA DE MEDICAMENTOS CONTROLADO";
            $activityActionsCreated = ActionController::create($medicalServiceActivity->id, $action_id, $description);

            $activityActionDocument = new ActivityActionDocument();
            $activityActionDocument->activity_action_id = $activityActionsCreated->id;
            $activityActionDocument->name = $document;
            $activityActionDocument->path = "activity_action_document/{$document}_{$action_id}.pdf";
            $activityActionDocument->save();

            //TODO: Entregar los medicamentos prescritos al paciente según la fórmula generada.


            //TODO: aperturar de manera automática un nuevo servicio "MEDICAMENTOS SORT",
            $medication_activity = new MedicationServicesController();
            $medication_activity->reportMedicationRequest($cpath, $medicalServiceActivity->id, $action_id, $document, $activityActionDocument->path);

            DB::commit();

            return back()->with('message', 'Operación realizada con éxito');

        } catch (\Exception $e) {
            DB::rollBack();
            // Retornar un mensaje de error o manejar la excepción
            return response()->json([
                'error' => 'Ocurrió un error al guardar los datos',
                'code' => $e->getCode(),
                'message' => $e->getMessage(),
                'line' => $e->getLine()
            ], 500);
        }
    }

    public function reportMedicalAuditMnk(Request $req, $cpath, $id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        DB::beginTransaction();
        try {

            //TODO: Recibir servicio / actividad, de manera automática, cuando en el servicio GIS se ejecute la acción: "Aviso de accidente o enfermedad"
            $medicalServiceActivity = Activity::where('id', $id)->where('client_id', $client->id)->firstOrFail();

            //TODO: Generar documento reporte de auditoría médica MNK
            // Generar el PDF
            $document = 'reportMedicalAuditMnkMedical';
            $action_id = ActionMedicalServiceSort::REPORTAR_AUDITORIA_MEDICA_MNK;

            $pdf = PDF::loadView("services.medical_services.docs.{$document}_pdf", ['value' => "pendiente"]); //modificar

            // Guardar el PDF en S3
            Storage::disk('s3')
                ->put("activity_action_document/{$document}_{$action_id}.pdf", $pdf->output());

            //TODO: Crear historial de acciones
            $description = $req->input('description');
            $activityActionsCreated = ActionController::create($medicalServiceActivity->id, $action_id, $description);

            $activityActionDocument = new ActivityActionDocument();
            $activityActionDocument->activity_action_id = $activityActionsCreated->id;
            $activityActionDocument->name = $document;
            $activityActionDocument->path = "activity_action_document/{$document}_{$action_id}.pdf";
            $activityActionDocument->save();

            DB::commit();

            return back()->with('success', 'Operación realizada con éxito');

        } catch (\Exception $e) {
            DB::rollBack();
            // Retornar un mensaje de error o manejar la excepción
            return response()->json([
                'error' => 'Ocurrió un error al guardar los datos',
                'code' => $e->getCode(),
                'message' => $e->getMessage()
            ], 500);
        }
    }

    //ACCION REMISION A ESPECIALISTA
    public function referral_specialist($cpath, $id , Request $request)
    {
        $gestion = $request->input('gestion');
        $client = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('client_id', $client->id)
            ->where('id', $id)
            ->firstOrFail();

        if ($gestion == 0){
            ActionController::create($activity->id, ActionMedicalServiceSort::REPORTAR_SEGUIMIENTO, 'Seguimiento para referir a especialista negado');
            return response()->json(['success'],200);
        }

        if ($activity->state_id == StateMedicalServiceSort::VALORACION_REALIZADA) {
            DB::beginTransaction();
            try {
                //Cambio de estado de la actividad
                $activityAction = ActionController::create($activity->id, ActionMedicalServiceSort::EMITIR_REMISION_A_ESPECIALISTA, 'Acción emitir referir a especialista');

                //Creamos el activity de prestacion medica secundaria
                $medicalServiceActivity = new Activity();
                $medicalServiceActivity->parent_id = $activity->parent_id;
                $medicalServiceActivity->client_id = $client->id;
                $medicalServiceActivity->service_id = Service::SERVICE_MEDICAL_SERVICES_SECONDARY_CARE_SORT_MNK;
                $medicalServiceActivity->affiliate_id = $activity->affiliate_id;
                $medicalServiceActivity->user_id = $activity->user_id;
                $medicalServiceActivity->state_id = State::REGISTRADO;
                $medicalServiceActivity->save();

                //Creamos el servicio de prestacion medica secundaria
                $medicalServicesSecondary = new MedicalServicesSecondaryCareSort();
                $medicalServicesSecondary->activity_id = $medicalServiceActivity->id;
                $medicalServicesSecondary->action_id = MedicalServicesSecondaryCareSort::REFERRAL_SPECIALIST;
                $medicalServicesSecondary->type_of_care = $request->input('type_of_care');
                $medicalServicesSecondary->save();

                DB::commit();
            } catch (\Exception $e) {
                DB::rollback();
                return response()->json(['error' => $e->getMessage()], 500);
            }

            return response()->json(['success'],200);
        } else {
            return response()->json(['error' => 'No se puede referir al especialista estado incorrecto'], 500);
        }
    }

    public function reportMedicalServiceRequest($cpath, $id, $document_name, $document_path, $action_id, $provideMain) //REPORTAR SOLICITUD PRESTACIÓN MÉDICA
    {

        $client = Client::where('path', $cpath)->firstOrFail();
        try {

            //consulta a la tabla proveedor
            $provider = Provider::where('id', $provideMain)->first();

            //Servicio Prestación Medica SORT padre
            $med_Service_act_parent = Activity::where('id', $id)->where('client_id', $client->id)->firstOrFail();
            $med_Service_sort_parent = MedicalServicesSort::where('activity_id', $med_Service_act_parent->id)->firstOrFail();

            //TODO: Crear servicio Prestación Médica SORT
            $medicalServiceActivity = new Activity;
            $medicalServiceActivity->parent_id = $med_Service_act_parent->parent_id;
            $medicalServiceActivity->client_id = $client->id;
            $medicalServiceActivity->service_id = Service::SERVICE_MEDICAL_SERVICES_SORT_MNK;
            $medicalServiceActivity->affiliate_id = $med_Service_act_parent->affiliate_id; //Cambiar
            $medicalServiceActivity->user_id = Auth::id();
            $medicalServiceActivity->state_id = State::REGISTRADO;
            $medicalServiceActivity->save();


            //TODO: Asignar a un analista para auditoría médica
            $analyst = RuleActionsController::getLastUserAction(ActionMedicationServiceSort::REPORTAR_SOLICITUD_MEDICAMENTOS, Area::ANALISTA_ASEGURAMIENTO); //Devuelve el id del analista dinámicamente
            $medicalServiceSort = new MedicalServicesSort();
            $medicalServiceSort->activity_id = $medicalServiceActivity->id;
            $medicalServiceActivity->medical_audit_analyst = $analyst;
            //TODO: Asignar por default al proveedor Addiuva (dejar esta información en el formulario en la pestaña Datos del proveedor)
            $medicalServiceSort->fill($med_Service_sort_parent->toArray());
            $medicalServiceSort->provider = $provider->name;
            $medicalServiceSort->primary_care_provider =  $provider->id;
            $medicalServiceSort->action_id = $action_id;
            $medicalServiceSort->requires_follow_up = null;

            //TODO: Replicar en el formulario los datos de la pestaña:
            // Datos del paciente (no es necesario ya que lo trae GIS) y Datos del Caso"
            // Diagnósticos
            $medicalServiceSort->save();

            //TODO: Traer la orden médica generada o cargada en el servicio anterior en alguna de las acciones
            //TODO: Crear historial de acciones
            $description = "REPORTAR SOLICITUD PRESTACIÓN MÉDICA";
            $activityActionsCreated = ActionController::create($medicalServiceActivity->id,
                ActionMedicalServiceSort::REPORTAR_SOLICITUD_PRESTACION_MEDICA,
                $description);

            if ($document_name !== '' && $document_path !== '') {

                $activityActionDocument = new ActivityActionDocument;
                $activityActionDocument->activity_action_id = $activityActionsCreated->id;
                $activityActionDocument->name = $document_name;
                $activityActionDocument->path = $document_path;
                $activityActionDocument->save();

            }



            return $medicalServiceActivity->id;

        } catch (\Exception $e) {
            throw $e;
        }
    }

    public function reportPmAssessment(Request $req, $cpath, $id) //REPORTAR VALORACIÓN PM
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        DB::beginTransaction();
        try {

            //TODO: Recibir servicio / actividad, de manera automática, cuando en el servicio GIS se ejecute la acción: "Aviso de accidente o enfermedad"
            $medicalServiceActivity = Activity::where('id', $id)->where('client_id', $client->id);

            //TODO: Formulario Diligenciado: Utilizar RenApp para gestionar la información médica del proveedor y completar el formulario requerido.
            //Capturar todos los datos del formulario y guardarlos en la base de datos
            $this->save($req, $cpath, $id);

            /*
            Integración con el Sistema de Historia Clínica: Si se dispone de integración con el sistema de Historia Clínica del proveedor,
            importar la información necesaria directamente desde este sistema para completar el formulario. //Está por definirse

            Carga de Historia Clínica en Archivo Digital: Subir el archivo digital de la Historia Clínica en RenApp para que la información esté disponible en el sistema. //Está por definirse
             */

            //TODO: Crear historial de acciones
            $description = "REPORTAR VALORACIÓN PM";
            $activityActionsCreated = ActionController::create($medicalServiceActivity->id,
                ActionMedicalServiceSort::REPORTAR_VALORACION_PM,
                $description);

            DB::commit();

            return response()->json(['id' => $medicalServiceActivity->id]);

        } catch (\Exception $e) {
            DB::rollBack();
            // Retornar un mensaje de error o manejar la excepción
            return response()->json([
                'error' => 'Ocurrió un error al guardar los datos',
                'code' => $e->getCode(),
                'message' => $e->getMessage()
            ], 500);
        }
    }
    //APROBACION SERVICIO
    public function approveService(Request $req, $cpath, $id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('client_id', $client->id)
            ->where('id', $id)
            ->firstOrFail();

        DB::beginTransaction();
        try {
            ActionController::create($activity->id, ActionMedicalServiceSort::APROBAR, 'Aprobacion del servicio');
            DB::commit();
        } catch (\Exception $e) {
            DB::rollback();
            return back()->with('error', 'No se pudo realizar la operacion');
        }
        return back()->with('success', 'Operación realizada con éxito');
    }

}