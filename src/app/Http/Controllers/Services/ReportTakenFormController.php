<?php

namespace App\Http\Controllers\Services;

use App\Action;
use App\Activity;
use App\Affiliate;
use App\Client;
use App\GisSort;
use App\Holiday;
use App\Http\Controllers\ActionController;
use App\Http\Controllers\Controller;
use App\Http\Controllers\Integrations\IntegrationServiceController;
use App\Http\Controllers\Tables\MailBoardController;
use App\Mail\SendDocumentDataBase;
use App\MailTemplates\Constants\Senders;
use App\MailTemplates\Constants\Templates;
use App\MailTemplates\TemplateBuilder;
use App\PolicyCalendar;
use App\PolicySort;
use App\PolicySpreadsheet;
use App\PolicySpreadsheetAffiliate;
use App\ReportTakenForm;
use App\Service;
use App\State;
use App\States\StatePoliza;
use App\States\StateReportePlanillaTomador;
use App\Utilities\Utilities;
use Carbon\Carbon;
use DateTime;
use Excel;
use Exception;
use http\Env\Response;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;
use PDF;
use Illuminate\Support\Facades\DB;
use PhpOffice\PhpSpreadsheet\Shared\Date;
use PHPExcel_Cell_DataType;

class ReportTakenFormController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth')->except(['generateDefaultSpreadsheet','cronSpecialMonthlyReportSpreadsheetUpload','specialCalendarReplacementSheet']);
    }

    public function form(Request $req, $cpath, $id)
    {
        $client   = Client::where('path', $cpath)
            ->firstOrFail();

        $activity = Activity::where('client_id', $client->id)
            ->where('id', $id)->firstOrFail();

        $policy_sort = PolicySort::where('activity_id', $activity->parent_id)
            ->first();
        $activityActionSpreadsheet = $activity->activity_actions()->where('action_id', Action::GENERAR_CERTIFICADO_TOMADOR)->latest()->first();
        if ($activityActionSpreadsheet){
            $documentdSpreadsheet = $activityActionSpreadsheet->documents()->first();
        }

        //revisar si es la primera planilla de tomador de emision
        $firstActivity = Activity::where('parent_id', $activity->parent_id)
            ->where('service_id', Service::SERVICE_REPORT_TAKEN_FORM_MNK)
            ->first();

        $emision = false;
        if ($firstActivity && $firstActivity->id === $activity->id) {
            $emision = true;
        }
        //fecha de vigencia de la planilla de tomador de emision 
        if (!$emision){
            $validitySpreadsheet = Carbon::parse($activity->created_at)->subMonths(1);
            $firstDay =  $validitySpreadsheet->copy()->startOfMonth()->format('d-m-Y');
            $lastDayMonth = $validitySpreadsheet->copy()->endOfMonth()->format('d-m-Y');
        }

        return view('services.report_taken_form.form',
            ['activity' => $activity,
            'policy_sort' => $policy_sort,
            'documentdSpreadsheet' => $documentdSpreadsheet ?? null,
            'emision' => $emision ?? false,
            'firstDay' => $firstDay ?? null,
            'lastDayMonth' => $lastDayMonth ?? null
            ]);
    }

    public function save(Request $req, $cpath, $id)
    {
        $client   = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();

        DB::beginTransaction();
        try {
            if (!$activity->report_taken_form) {
                $report_taken_form              = new ReportTakenForm();
                $report_taken_form->activity_id = $id;
            } else {
                $report_taken_form = $activity->report_taken_form;
            }


            $report_taken_form->save();

            DB::commit();
        } catch (Exception $e) {
            DB::rollback();
        }

        return redirect("servicio/$id/report_taken_form");
    }

    public function pdf(Request $req, $cpath, $id)
    {
        $client   = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();

        $report_taken_form              = new ReportTakenForm();
        $report_taken_form->activity_id = $id;
        $report_taken_form->setRelation('activity', $activity);
        $report_taken_form->created_at = new DateTime();

        $pdf = PDF::loadView('services.report_taken_form.docs.report_taken_form_pdf', ['activity' => $activity, 'watermark' => true]);

        return $pdf->stream('preview.pdf');
    }
    public function reportAffiliate(Request $req, $cpath, $id)
    {
        $activity = 1;
        $policySort = DB::table('policy_spreadsheet_affiliates AS pa')
        ->leftJoin('policy_spreadsheets AS pss', 'pss.id', 'pa.policy_spreadsheet_id')
        ->leftJoin('policy_sorts AS ps', 'ps.id', 'pss.policy_sort_id')
        ->where('ps.activity_id', $activity)
        ->first();

        return view('services.affiliates.report', compact('policySort'));
    }

    //Creamos automaticamente la planilla del tomador de emision si no la carga en los 10 dias
    public function generateDefaultSpreadsheet(Request $request)
    {
        $spreadsheetIds = [];
        if ($request->has('day')) {
            $today = Carbon::createFromFormat('Y-m-d', $request->input('day'));
        }else{
            $today = Carbon::now();
        }

        if ($today->isLastOfMonth()){
            //traemos todas las polizas sin planillas ya que es fin del mes
            $activities = Activity::where('state_id', StatePoliza::POLIZA_EMITIDA_ACTIVA)
                ->whereHas('policy_sort')
                ->whereDoesntHave('children', function ($query) {
                    $query->where('service_id', Service::SERVICE_REPORT_TAKEN_FORM_MNK);
                })
                ->get();
        }else{
            //traemos las polizas que se hayan emitido, que lleven mas de 10 dias habiles de emision y que no tengan la planilla de tomador de emision
            // Obtener la fecha hace 10 días hábiles desde hoy
            $tenBusinessDaysAgo = $this->subtractBusinessDays($today, 10);
            $activities = Activity::where('state_id', StatePoliza::POLIZA_EMITIDA_ACTIVA)
                ->whereHas('policy_sort', function ($query) use ($tenBusinessDaysAgo) {
                    $query->whereDate('validity_from', '=', $tenBusinessDaysAgo);
                })
                ->whereDoesntHave('children', function ($query) {
                    $query->where('service_id', Service::SERVICE_REPORT_TAKEN_FORM_MNK);
                })
                ->get();
        }


        foreach ($activities as $activity) {
            try {
                DB::beginTransaction();
                //creamos la actividad planilla de tomador de emision
                $activitySpreadsheet = new Activity();
                $activitySpreadsheet->parent_id = $activity->id;
                $activitySpreadsheet->client_id = $activity->client_id;
                $activitySpreadsheet->affiliate_id = $activity->affiliate_id;
                $activitySpreadsheet->service_id = Service::SERVICE_REPORT_TAKEN_FORM_MNK;
                $activitySpreadsheet->state_id = StateReportePlanillaTomador::REGISTRADO;
                $activitySpreadsheet->user_id = $activity->user_id;
                $activitySpreadsheet->save();

                //ejecutamos acciones para el registro de la actividad
                ActionController::create($activitySpreadsheet->id, Action::REPORTAR_PLANILLA_TOMADOR, 'Reportar planilla tomador creada por el sistema');
                //crear la accion generar certificado de tomador, como es emision no se genera pdf
                ActionController::create($activitySpreadsheet->id, Action::GENERAR_CERTIFICADO_TOMADOR, 'Reportar certificado tomador creado por el sistema');

                //creamos la planilla de tomador de emision
                $policySpreadSheet = new PolicySpreadsheet();
                $policySpreadSheet->activity_id = $activitySpreadsheet->id;
                $policySpreadSheet->total_affiliates = 0;
                $policySpreadSheet->total_salaries = $activity->policy_sort->salary_projection;
                $policySpreadSheet->observacion = 'Planilla de tomador de emision generada automaticamente';
                $policySpreadSheet->entry_type = 'Sustituida';
                $policySpreadSheet->save();

                // Guardamos el ID de la planilla en el array
                $spreadsheetIds[] = $policySpreadSheet->id;

                DB::commit();
            }catch (\Exception $e) {
                DB::rollback();
                continue;
            }
        }

        return response()->json([
            'message' => 'Planillas generadas correctamente',
            'spreadsheet_ids' => $spreadsheetIds
        ], 200);

    }

    //restar dias habiles a una fecha
    public function subtractBusinessDays($date, $days)
    {
        // Definir el inicio y el final del mes
        $startRange = $date->copy()->subDays(30);
        $endOfMonth = $date->copy()->endOfMonth();
        // Obtener los días festivos dentro del mes actual
        $holidays = Holiday::whereBetween('holiday', [$startRange, $endOfMonth])
            ->pluck('holiday')
            ->map(function ($holiday) {
                return Carbon::parse($holiday)->toDateString();
            })
            ->toArray();
        $businessDaysCount = 0;
        $currentDate = Carbon::parse($date);

        while ($businessDaysCount < $days) {
            $currentDate->subDay();
            if (!in_array($currentDate->dayOfWeek, [Carbon::SATURDAY, Carbon::SUNDAY]) &&
                !in_array($currentDate->toDateString(), $holidays)) {
                $businessDaysCount++;
            }
        }

        return $currentDate->toDateString();
    }
    //sumar dias habiles a una fecha
    public function addBusinessDays($date, $days)
    {
        // Definir el rango para buscar festivos
        $startRange = $date->copy();
        $endRange = $date->copy()->addDays($days + 15);

        // Obtener los días festivos dentro del rango
        $holidays = Holiday::whereBetween('holiday', [$startRange, $endRange])
            ->pluck('holiday')
            ->map(function ($holiday) {
                return Carbon::parse($holiday)->toDateString();
            })
            ->toArray();

        $businessDaysCount = 0;
        $currentDate = Carbon::parse($date);

        while ($businessDaysCount < $days) {
            $currentDate->addDay();
            if (!in_array($currentDate->dayOfWeek, [Carbon::SATURDAY, Carbon::SUNDAY]) &&
                !in_array($currentDate->toDateString(), $holidays)) {
                $businessDaysCount++;
            }
        }

        return $currentDate->toDateString();
    }


    //cargar afiliados masivamente de txt
    public function storeAffiliatesMassivelyTxt(Request $request,$cpath, $id)
    {
        DB::beginTransaction();
        try {
            $request->validate([
                'txt_file' => 'required|file|mimetypes:text/plain,application/octet-stream|max:5120',
            ], [
                'txt_file.max' => 'El archivo no debe superar los 5 MB.',
            ]);


            $activityPolicy = Activity::where('id', $id)->first();

            if (
                $activityPolicy->state_id == StatePoliza::POLIZA_SUSPENDIDA ||
                $activityPolicy->state_id == StatePoliza::TRAMITE_ANULADO
            ) {

                return response()->json([
                    "status" => false,
                    "message" => "No puede cargar planilla porque la póliza está en estado suspendida o anulada"
                ]);
            } else {
                $activitySpreadsheet = Activity::where('parent_id', $id)
                    ->where('service_id', Service::SERVICE_REPORT_TAKEN_FORM_MNK)
                    ->where('state_id', StateReportePlanillaTomador::CERTIFICADO_DE_TOMADOR_REPORTADO)
                    ->first();

                //valida que la fecha en la que se va a crear el servico no sea menor a la vigencia de la poliza
                if ($activitySpreadsheet && Carbon::now()->lt($activityPolicy->policy_sort->validity_from)) {
                    return response()->json([
                        "status" => false,
                        "message" => "No puede cargar la planilla porque la fecha de carga es menor a la fecha de inicio de la vigencia de la póliza"
                    ]);
                }
            }

            //Variable para saber desde que vista viene si TOMADOR O INTERMEDIARIO
            $isTaker = $request->taker;
            //archivo txt
            $file = $request->file('txt_file');
            $client = Client::where('path', $cpath)->firstOrFail();
            //Validamos que actividad del reporte de planilla exista o la creamos
            $PolicySortController = new PolicySortController();
            $activityServiceSpreadsheet = $PolicySortController->updateCreateActivityService($id,$client);
            //creamos o actualizamos el servicio REPORTE PLANILLA TOMADOR
            //Obtenemos el path de S3
            $lpath = Utilities::putFileToAwsS3($file);
            $policySpreadsheet = $PolicySortController->updateCreatePolicySpreadsheet($activityServiceSpreadsheet, $lpath , 'TXT');
            //Manipulamos el archivo txt
            $dataTxt = $this->procesarTxt($file);
            if (count($dataTxt['errores']) > 0) {
                throw new \Exception(implode("\n", $dataTxt['errores']));
            }
            $totalSalario = array_sum(array_column($dataTxt['datosProcesados'], 'monthly_salary'));
            // Guardar los totales en el modelo
            $policySpreadsheet->total_affiliates = count($dataTxt['datosProcesados']);
            $policySpreadsheet->total_salaries = $totalSalario;
            $policySpreadsheet->file = null;
            $policySpreadsheet->file_txt = $lpath;
            $policySpreadsheet->save();

            //Si viene desde la vista del TOMADOR ejecutamos la ACCIÖN REPORTAR PLANILLA TOMADOR
            if ($isTaker !== null) {
                ActionController::create($activityServiceSpreadsheet->id, Action::REPORTAR_PLANILLA_TOMADOR, 'Planilla tomador reportada');
                //Enviamos correo de notificación
                $PolicySortController->notificationTaker($request,$activityServiceSpreadsheet);
            } else {
                //si es Vista intermediario ejecutamos la ACCION REPORTAR PLANILLA INICIAL
                $PolicySortController->actionReportInitialWorksheets($client, $id, $activityServiceSpreadsheet, $lpath, $policySpreadsheet->id);
            }

            DB::commit();

            //Ejecutamos la función para crear o actualizar los afiliados en segundo plano
            $res = $this->sendUploadSpreadsheetTxt($policySpreadsheet->id);

            return response()->json([
                'message' => 'Archivo procesado correctamente.',
                'total' => [
                    'type' => "Auto",
                    'registros_guardados' => count($dataTxt['datosProcesados']),
                    'salario_total' => number_format($totalSalario, 2, ',', '.')
                ],
                'policy_spreadsheet' => $policySpreadsheet
            ]);
        }catch (ValidationException $e) {
            DB::rollback();
            return response()->json([
                'message' => 'Error de validación',
                'e' => 'El archivo no cumple con la estructura requerida, es diferente a TXT o es superior a 5MB',
            ], 422);
        } catch (\Exception $e) {
            DB::rollback();
            if ($e)
            $mensajeDefaul = "El archivo de txt cargado no tiene la estructura correcta, por favor revíselo y vuelva a realizar la carga";
            return response()->json([
                'message' => 'Errores durante el procesamiento de carga automática del archivo TXT:',
                'e' => (strpos($e->getMessage(), 'Undefined') !== false) ? $mensajeDefaul : $e->getMessage(),
            ], 500);
        }
    }

    //procesar archivo txt
    public function procesarTxt($file)
    {

        $datosProcesados = [];
        if (is_string($file)) {
            // Ya es el contenido del archivo
            $contenido = explode("\r", $file);

        }else{
            $contenido = file($file->getRealPath(), FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        }
        // Ignorar las 3 primeras líneas
        $contenido = array_slice($contenido, 3);

        try {
            // Inicializar el array de errores por línea
            $errores = [];
            $datosProcesados = [];
            $uniqueRecords = [];

            //Validación de nacionalidades solo se permiten los establecido en el json paises.json
            $jsonPath = public_path('js/paises.json');
            $jsonContent = File::get($jsonPath);
            $countries = json_decode($jsonContent, true);
            //Validar si la nacionalidad está en la lista
            $validNationalities = array_column($countries, 'country_short_name');

            // Cargar ocupaciones desde la base de datos
            $occupations = DB::table('occupations_category')->pluck('name', 'code')->toArray();
            foreach ($contenido as $indice => $linea) {
                $validateData = [];
                // Limpiar espacios extra en la línea
                $linea = trim($linea);
                // Extraer los valores según la posición en la cadena
                $id_type = trim(substr($linea, 0, 1));
                switch ($id_type) {
                    case '0':
                        $id_type = 'CF';
                        break;
                    case '1':
                        $id_type = 'CR';
                        break;
                    case '6':
                        $id_type = 'DI';
                        break;
                    case '9':
                        $id_type = 'PA';
                        break;
                    default:
                        $validateData[] = "Tipo de documento no válido";
                        break;
                }

                $identification_number = trim(substr($linea, 1, 12));//extraer numero de documento
                $first_name = trim(substr($linea, 40, 15));//extraer nombre
                $last_name = trim(substr($linea, 55, 30));
                $last_name = preg_replace('/\s+/', ' ', $last_name); //extraer apellido
                $date_of_birth = trim(substr($linea, 85, 10)); // Fecha de nacimiento
                $email = trim(substr($linea, 103, 40)); // Email
                $gender = trim(substr($linea, 143, 1)); // Género
                if ($gender === '1') {
                    $gender = 'M';
                } elseif ($gender === '2') {
                    $gender = 'F';
                }
                $nationality = trim(substr($linea, 145, 2)); // Nacionalidad
                $monthly_salary = trim(substr($linea, 147, 13)); // Salario mensual
                $monthly_salary = ltrim($monthly_salary, '0'); // Elimina los ceros al inicio
                $monthly_salary = number_format((float) $monthly_salary, 2, '.', '');
                $days = trim(substr($linea, 161, 2)); // Días de trabajo
                $hours = trim(substr($linea, 164, 3)); // Horas de trabajo
                $work_shift_type = trim(substr($linea, 167, 2)); // Tipo de jornada
                switch ($work_shift_type){
                    case '01':
                        $work_shift_type = 'TC';
                        break;
                    case '02':
                        $work_shift_type = 'TM';
                        break;
                    case '03':
                        $work_shift_type = 'OD';
                        break;
                    case '04':
                        $work_shift_type = 'OH';
                        break;

                }
                $observation_affiliate = trim(substr($linea, 169, 2)); // Observación
                switch ($observation_affiliate){
                    case '00':
                        $observation_affiliate = 'Sin observación';
                        break;
                    case '01':
                        $observation_affiliate = 'Entró en el mes';
                        break;
                    case '02':
                        $observation_affiliate = 'Salió en el mes';
                        break;
                    case '03':
                        $observation_affiliate = 'Incapacidad por CCSS';
                        break;
                    case '04':
                        $observation_affiliate = 'Incapacidad por INS';
                        break;
                    case '05':
                        $observation_affiliate = 'Entró y salió en el mes';
                        break;
                    case '06':
                        $observation_affiliate = 'Licencia sin salario';
                        break;
                    case '07':
                        $observation_affiliate = 'Licencia por maternidad';
                        break;
                    case '08':
                        $observation_affiliate = 'Salario escolar';
                        break;
                }
                $occupation = trim(substr($linea, 172, 4)); // Ocupación

                // Validar campos vacíos
                if (empty($identification_number)){
                    $validateData[] = 'Número de documento vacío';
                }else{
                    $uniqueKey = $id_type . '_' . $identification_number;
                    if (isset($uniqueRecords[$uniqueKey])) {
                        // Agregar alerta de duplicado
                        $validateData[] = "El trabajador con No. de Identificación: {$identification_number} y Tipo: {$id_type} está duplicado en la línea " . ($indice + 1);
                    }else {
                        $uniqueRecords[$uniqueKey] = true; // Almacenar la clave única
                    }
                }

                if (empty($first_name)) $validateData[] = 'Nombre vacío';
                if (empty($last_name)) $validateData[] = 'Apellidos vacío';

                if (empty($date_of_birth)){
                    $validateData[] = 'Fecha de nacimiento vacía';
                }else{
                    if (preg_match('/^\d{2}\/\d{2}\/\d{4}$/', $date_of_birth)) {
                        $format = 'd/m/Y';
                    } elseif (preg_match('/^\d{2}-\d{2}-\d{4}$/', $date_of_birth)) {
                        $format = 'd-m-Y';
                    } else {
                        $format = null;
                    }
                    try {
                        $date_of_birth = Carbon::createFromFormat($format, $date_of_birth);
                        $age = $date_of_birth->age;
                        // Verifica si la edad es menor a 15 años
                        if ($age < 15) {
                            $validateData[] = "tiene menos de 15 años.";
                        }
                    } catch (\Exception $e) {
                        $validateData[] = 'Formato de fecha de nacimiento no válido';
                    }
                }

                if (empty($email)){
                    $validateData[] = 'Email vacío';
                }else{
                    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                        $validateData[] = "Correo electrónico no válido";
                    }
                }

                if (empty($gender)){
                    $validateData[] = 'Género vacío';
                }else{
                    if ($gender !== 'M' && $gender !== 'F') {
                        $validateData[] = "Género no válido";
                    }
                }

                if (empty($nationality)) {
                    $validateData[] = 'Nacionalidad vacía';
                }else{

                    if (!in_array($nationality, $validNationalities)) {
                        $validateData[] = "La nacionalidad no es válida.";
                    }

                }

                if (empty($monthly_salary)){
                    $validateData[] = 'Salario mensual vacío';
                }else{
                    $monthly_salary = floatval($monthly_salary);
                    if ($monthly_salary < 0) {
                        $validateData[] = "Salario mensual debe ser un número positivo mayor a 1.";
                    }
                }

                if (empty($days)){
                    $validateData[] = 'Días trabajados vacío';
                }else{
                    $days = floatval($days);
                    if ($days < 0) {
                        $validateData[] = "Días trabajados debe ser un número positivo mayor a 1.";
                    }elseif ($days > 31){
                        $validateData[] = "Días trabajados no puede ser mayor a 31.";
                    }
                }

                if (empty($hours)){
                    $validateData[] = 'Horas trabajadas vacío';
                } else {
                    $hours = floatval($hours);
                    if ($hours < 0) {
                        $validateData[] = "Horas trabajadas debe ser un número positivo mayor a 1.";
                    }elseif ($hours > 400){
                        $validateData[] = "Horas trabajadas no puede ser mayor a 400.";
                    }
                }

                if (empty($work_shift_type)){
                    $validateData[] = 'Tipo de jornada vacío';
                }else{
                    if ($work_shift_type !== 'TC' && $work_shift_type !== 'TM' && $work_shift_type !== 'OD' && $work_shift_type !== 'OH') {
                        $validateData[] = "Tipo de jornada no válido";
                    }
                }

                if (empty($occupation)){
                    $validateData[] = 'Ocupación vacía';
                }else{
                    // Si existe, usar el nombre; si no, dejar el código
                    $occupation = $occupations[$occupation] ?? $occupation;
                }

                // Si hay errores, los agregamos al array de errores
                if (!empty($validateData)) {
                    $errores[] = "Línea " . ($indice + 4) . ": " . implode(", ", $validateData);
                } else {
                    // Guardar los datos si no hay errores
                    $datosProcesados[] = [
                        'id_type' => $id_type,
                        'identification_number' => $identification_number,
                        'first_name' => $this->convertToUtf8($first_name),
                        'last_name' => $this->convertToUtf8($last_name),
                        'date_of_birth' => $date_of_birth,
                        'email' => $email,
                        'gender' => $gender,
                        'nationality' => $nationality,
                        'monthly_salary' => $monthly_salary,
                        'days' => $days,
                        'hours' => $hours,
                        'work_shift_type' => $work_shift_type,
                        'occupation' => $occupation,
                        'observation_affiliate' => $observation_affiliate,
                    ];
                }
            }
            return [
                'datosProcesados' => $datosProcesados,
                'errores' => $errores
            ];

        }catch (\Exception $e) {
            throw new \Exception($e);
        }

    }

    private function convertToUtf8($value) {
        // Detectar la codificación de entrada
        $encoding = mb_detect_encoding($value, ['UTF-8', 'ISO-8859-1', 'Windows-1252'], true);
        if ($encoding === false) {
            // Si no se puede detectar la codificación, asumir UTF-8
            $encoding = 'UTF-8';
        }
        // Convertir a UTF-8
        return mb_convert_encoding($value, 'UTF-8', $encoding);
    }

    //Generar excel de afiliados y descargar desde txt
    public function generateExcel($cpath, $id)
    {
        $spreadsheet = ReportTakenForm::where('id', $id)->firstOrFail();
        if ($spreadsheet->status_affiliate_uploaded !== 'COMPLETED' && $spreadsheet->file_txt !== null) {
            return response()->json([
                'status' => false,
                'message' => 'El proceso de carga de afiliados aún no ha terminado. Por favor, inténtelo más tarde.'
            ], 400);
        }

        if ($spreadsheet->file == null && $spreadsheet->total_affiliates < 5001) {
            return Utilities::exportAffiliateExcel($spreadsheet->id);
        }

        return \Storage::disk('s3')->download(
            $spreadsheet->file,
            basename($spreadsheet->file),
            ['ContentType' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet']
        );
    }

    public function statusExcel($cpath,$id)
    {
    
        $sheet = ReportTakenForm::where('id', $id)->firstOrFail();
       
        return response()->json([
            'status' => $sheet->status_affiliate_uploaded
        ]);
    }

    //Validar si hay afiliados para descargar excel
    public function validateExcelDownload($cpath,$id)
    {
        $spreadsheet = ReportTakenForm::findOrFail($id);

        // Opcional: también podrías validar si hay afiliados
        $hasAffiliates = PolicySpreadsheetAffiliate::where('policy_spreadsheet_id', $spreadsheet->id)->exists();

        if (!$hasAffiliates) {
            return response()->json([
                'status' => false,
                'message' => 'No hay afiliados para generar el Excel.'
            ], 400);
        }

        return response()->json(['status' => true]);
    }

    //Manejo exclusion afiliados con Beneficio de colectividad
    public function notifyAndUpdateAffiliateCollectivity($activityPolicy,$spreadsheet)
    {
        try {
            $affiliatesBenefitColective = PolicySpreadsheetAffiliate::where('policy_spreadsheet_id', $spreadsheet->id)
                ->where('benefit_colective','Si')
                ->get();
            //Actualizar el campo observation_colective y notificamos
            foreach ($affiliatesBenefitColective as $affiliateSpreadsheet) {
                $affiliateSpreadsheet->observation_colective = "Reportar planilla tomador duplicada - " . Carbon::now()->format('d-m-Y H:i:s');
                $affiliateSpreadsheet->save();


                //definimos las variables
                $affiliate = Affiliate::where('id',$affiliateSpreadsheet->affiliate_id)->first();
                $gis = GisSort::where('id', $affiliateSpreadsheet->gis_id)->first();

                // Agrega el email principal de notificaciones y todos los adicionales
                $emails = PolicySortController::getAdditionalNotificationEmails(
                    $activityPolicy->policy_sort->id,
                    [
                        $activityPolicy->policy_sort->email , 
                        $affiliate->email
                    ]
                );

                $emailBuild = TemplateBuilder::build(
                    Templates::NOTIFY_EMPLOYER_UNINSURED_CASE,
                    [
                        'name' =>mb_convert_case(mb_strtolower($activityPolicy->affiliate->full_name ?? ''), MB_CASE_TITLE, "UTF-8"),
                        'policy_sort' => $activityPolicy->policy_sort->formatSortNumber(),
                        'name_affiliate' =>mb_convert_case(mb_strtolower($affiliate->full_name ?? ''), MB_CASE_TITLE, "UTF-8") ,
                        'number_identification' => $affiliate->doc_number,
                        'fecha_accidente' => $gis->date_accident,
                    ]
                );
                if ($emails){
                    // Enviar el correo
                    $mailSent = new SendDocumentDataBase(
                        implode(',', $emails),
                        $emailBuild['subject'],
                        "<EMAIL>",
                        "Caso no asegurado",
                        [
                            "text" => $emailBuild['body'],
                            "sender" => $emailBuild['sender']
                        ],
                        "<EMAIL>",
                        [],
                        "send_document_db",
                        env('CLIENT_ID'),
                        request()->getHost()
                    );
                    $mailSent->sendMail();
                }
            }
        }catch (Exception $e) {
            throw  $e;
        }

    }

    public function sendUploadSpreadsheetTxt($policySpreadsheetId)
    {
        $integrationServiceController = new IntegrationServiceController();

        $url = config('app.api_url') . '/planilla_afiliados/cargar_txt';

        $method = 'POST';
        $data = [
            'policy_spreadsheet_id' => $policySpreadsheetId,
        ];

        $result = $integrationServiceController->requestRenAppApi($url, $method, $data);
        $data = json_decode($result->getContent(), true);

        if (isset($data['success']) && $data['success']) {
            return true;
        }
        return false;
    }

    //Notificar a las polizas con calendario especial el cargue de la planilla definitiva
    public function cronSpecialMonthlyReportSpreadsheetUpload(Request $request)
    {

        $id = uniqid();
        if (\Cache::has('cron_special_monthly_report_spreadsheet_upload')) {

            return response()->json([
                'status' => 200,
                'message' => 'Ya hay una validación en progreso',
            ], 200);

        }
        \Cache::put('cron_special_monthly_report_spreadsheet_upload', $id, now()->addMinutes(5));

        if ($request->get('fecha_referencia')) {
            $currentDate = Carbon::parse($request->get('fecha_referencia'));
        }else{
            $currentDate = Carbon::today();
        }

        $periodsToNotify = PolicyCalendar::whereDate('notify_date', $currentDate)
            ->whereHas('policySort', function($q) {
                // la póliza está activa y es especial
                $q->where('validity_from', '<', now())
                    ->whereIn('work_modality_id', [1, 5])
                    ->where('calendar_period', '2')
                    ->whereHas('activity', function ($a) {
                        $a->where('service_id', Service::SERVICE_POLICY_SORT_MNK)
                            ->where('state_id', StatePoliza::POLIZA_EMITIDA_ACTIVA);
                    });
            })
            ->with('policySort.activity')
            ->groupBy(['policy_sort_id'])
            ->get();

        $count_emails = 0;
        $failure_count = 0;
        //Recorremos los periodos de polizas a notificar
        foreach ($periodsToNotify as $period) {
            try {
                //traemos la actividad de la poliza
                $activityPolicy = $period->policySort->activity;
                // Verificamos que NO haya reportado planilla en este rango
                $hasReport = $activityPolicy->children()
                    ->where('service_id', Service::SERVICE_REPORT_TAKEN_FORM_MNK)
                    ->whereBetween('created_at', [
                        Carbon::parse($period->end_date)->startOfDay(),
                        Carbon::parse($period->deadline_date)->endOfDay(),
                    ])
                    ->exists();

                if ($hasReport) {
                    continue; // Si ya hay un reporte, no hacemos nada
                }
                //Traemos la ultima planilla asociada a la poliza
                $lastSpreadsheetActivity = Activity::where('parent_id', $period->policySort->activity_id)
                    ->where('service_id', Service::SERVICE_REPORT_TAKEN_FORM_MNK)
                    ->where('state_id', StateReportePlanillaTomador::CERTIFICADO_DE_TOMADOR_REPORTADO)
                    ->latest()
                    ->first();

                //Si existe contamos cuantos afiliados tiene
                if ($lastSpreadsheetActivity) {
                    $count = optional(optional($lastSpreadsheetActivity->policy_spreadsheets)->policy_spreadsheet_affiliate)->count() ?? 0;
                } else {
                    $count = 0;
                }

                // Si hay una planilla previa y solo hay un afiliado que es el tomador, no enviar correo
                if (
                    $lastSpreadsheetActivity &&
                    $lastSpreadsheetActivity->policy_spreadsheets &&
                    $count === 1 &&
                    $lastSpreadsheetActivity->policy_spreadsheets->policy_spreadsheet_affiliate->first()->affiliate_id === $lastSpreadsheetActivity->affiliate_id
                ) {
                    continue;
                }

                $finalEmailsArray = [$period->policySort->email,$period->policySort->notification_email];

                //Filtramos los emails
                $validEmails = array_filter($finalEmailsArray, function ($email) {
                    return filter_var($email, FILTER_VALIDATE_EMAIL);
                });

                if (!empty($validEmails)) {

                    $contractHolderName = mb_convert_case(mb_strtolower(optional($activityPolicy->affiliate)->first_name), MB_CASE_TITLE, "UTF-8");
                    $startPeriod =  Carbon::parse($period->start_date)->format('d-m-Y');
                    $endPeriod =  Carbon::parse($period->end_date)->format('d-m-Y');
                    $year = Carbon::parse($period->start_date)->year;
                    $periodMonth =  $startPeriod . ' al ' . $endPeriod;
                    $consecutive = optional($activityPolicy->policy_sort)->formatSortNumber();

                    if (!$lastSpreadsheetActivity) {
                        $monthName = "Emisión";
                    }

                    $emailBuild = TemplateBuilder::build(
                        Templates::MONTHLY_PAYROLL_SUBMISSION,
                        [
                            'policy_sort' => $consecutive,
                            'name' => $contractHolderName ?? '',
                            'monthName' => $periodMonth,
                            'year' => $year,
                        ]
                    );

                    $mailSent = new SendDocumentDataBase(
                        implode(',', $validEmails),
                        $emailBuild['subject'],
                        "<EMAIL>",
                        "Planilla mensual no reportada",
                        [
                            "text" => $emailBuild['body'],
                            "sender" => $emailBuild['sender']
                        ],
                        "<EMAIL>",
                        [],
                        "send_document_db",
                        $activityPolicy->client,
                        $request->host,
                        $activityPolicy->id
                    );

                    // Capturar el resultado del envío
                    $result = $mailSent->sendMail();

                    //Registramos los datos del correo enviado para la trazabilidad
                    $mailBoardController = new MailBoardController();

                    $mailBoardController->createRegisterMail(
                        $activityPolicy->id,
                        $activityPolicy->service->id,
                        $activityPolicy->policy_sort->consecutive,
                        'Tomador',
                        $contractHolderName,
                        $activityPolicy->affiliate->doc_number,
                        $emailBuild['subject'],
                        $emailBuild['body'],
                        $validEmails,
                        $result,
                        null
                    );

                    $count_emails++;
                    ActionController::create($activityPolicy->id, Action::REPORTAR_SEGUIMIENTO_CARGUE_PLANILLA, 'REPORTAR SEGUIMIENTO CARGUE PLANILLA');
                }

            }catch (\Exception $e){
                $failure_count++;
            }
        }

        return response()->json([
            'status' => 'success',
            'message' => "Se notificaron $count_emails emails del reporte de seguimiento cargue planilla.",
            'fallos' => $failure_count
        ], 200);
    }

    //Sustitucion de planilla para calendario especial al dia 10
    public function specialCalendarReplacementSheet(Request $request)
    {
        $id = uniqid();
        if (\Cache::has('special_calendar_replacement_sheet')) {

            return response()->json([
                'status' => 200,
                'message' => 'Ya hay una validación en progreso',
            ], 200);

        }
        \Cache::put('special_calendar_replacement_sheet', $id, now()->addMinutes(5));

        //traemos los periodos de polizas a sustituir

        if ($request->get('fecha_referencia')) {
            $currentDate = Carbon::parse($request->get('fecha_referencia'));
        }else{
            $currentDate = Carbon::today();
        }

        //periodos de polizas a sustituir
        $periodsToNotify = PolicyCalendar::whereDate('deadline_date', $currentDate)
            ->whereHas('policySort', function($q) {
                // la póliza está activa y es especial
                $q->where('validity_from', '<', now())
                    ->whereIn('work_modality_id', [1, 5])
                    ->where('calendar_period', '2')
                    ->whereHas('activity', function ($a) {
                        $a->where('service_id', Service::SERVICE_POLICY_SORT_MNK)
                            ->where('state_id', StatePoliza::POLIZA_EMITIDA_ACTIVA);
                    });
            })
            ->with('policySort.activity')
            ->groupBy(['policy_sort_id'])
            ->get();

        $count = 0;
        $errors = 0;

        foreach ($periodsToNotify as $period){
            try {
                DB::beginTransaction();
                //traemos la actividad de la poliza
                $activityPolicy = $period->policySort->activity;
                // Verificamos que NO haya reportado planilla en este rango
                $hasReport = $activityPolicy->children()
                    ->where('service_id', Service::SERVICE_REPORT_TAKEN_FORM_MNK)
                    ->whereBetween('created_at', [
                        Carbon::parse($period->end_date)->startOfDay(),
                        Carbon::parse($period->deadline_date)->endOfDay(),
                    ])
                    ->exists();

                if ($hasReport) {
                    continue; // Si ya hay un reporte, no hacemos nada
                }

                //Buscamos la actividad de la ultima planilla reportada
                $activitySpreadSheet = Activity::where('service_id', Service::SERVICE_REPORT_TAKEN_FORM_MNK)
                    ->where('parent_id', $activityPolicy->id)
                    ->latest()
                    ->first();

                if ($activitySpreadSheet) {
                    //buscamos la planilla
                    $spreadsheetLastReport = PolicySpreadsheet::where('activity_id', $activitySpreadSheet->id)->first();

                    $PolicySortController = new PolicySortController();
                    //duplicamos la planilla con sus afiliados
                    $PolicySortController->duplicateSpreadsheetData($activityPolicy,$spreadsheetLastReport,'Sustituida');
                    //Ajustes afiliados con beneficio colectividad se notifican y se actulizan campo de observacion colectividad tener en cuenta para colectividad

                    //Asignación de periodo correspondiente
                    $startPeriod =  Carbon::parse($period->start_date)->format('d-m-Y');
                    $endPeriod =  Carbon::parse($period->end_date)->format('d-m-Y');
                    $year = Carbon::parse($period->start_date)->year;
                    $periodMonth =  $startPeriod . ' al ' . $endPeriod;

                }else{
                    // Crear la actividad de reporte de planilla
                    $activityReport = new Activity();
                    $activityReport->service_id = Service::SERVICE_REPORT_TAKEN_FORM_MNK;
                    $activityReport->state_id = StateReportePlanillaTomador::REGISTRADO;
                    $activityReport->parent_id = $activityPolicy->id;
                    $activityReport->affiliate_id = $activityPolicy->affiliate_id;
                    $activityReport->user_id = $activityPolicy->user_id;
                    $activityReport->client_id = $activityPolicy->client->id;
                    $activityReport->save();

                    // Crear la acción de reporte de planilla
                    $actionReport = ActionController::create(
                        $activityReport->id,
                        Action::REPORTAR_PLANILLA_TOMADOR,
                        'REPORTAR PLANILLA TOMADOR CREADA POR EL SISTEMA'
                    );
                    //crear la accion generar certificado de tomador, como es emision no se genera pdf
                    ActionController::create($activityReport->id, Action::GENERAR_CERTIFICADO_TOMADOR, 'Reportar certificado tomador creado por el sistema');

                    //crear la planilla en la tabla spreadsheet con el valor de la proyeccion de salarios
                    $spreadsheet = new PolicySpreadsheet();
                    $spreadsheet->activity_id = $activityReport->id;
                    $spreadsheet->total_salaries = $period->policySort->salary_projection;
                    $spreadsheet->created_at = now();
                    $spreadsheet->total_affiliates = 0;
                    $spreadsheet->observacion = "Planilla de emisión creada por el sistema basada en la proyección de salarios";
                    $spreadsheet->entry_type = 'Sustituida';
                    $spreadsheet->save();
                    //planilla de emision
                    $periodMonth = "Emisión";
                    $year = Carbon::parse($period->start_date)->year;
                }

                //verificar si el tomador del seguro es la unica persona asegurada en la ultima planilla
                $lastSpreadsheetActivity = Activity::where('service_id', Service::SERVICE_REPORT_TAKEN_FORM_MNK)
                    ->where('parent_id', $activityPolicy->id)
                    ->latest()
                    ->with([
                        'policy_spreadsheets' => function ($query) {
                            $query->withCount('policy_spreadsheet_affiliate')->with('policy_spreadsheet_affiliate');
                        }
                    ])
                    ->first();
                $noEmail = false;
                if (
                    $lastSpreadsheetActivity &&
                    $lastSpreadsheetActivity->policy_spreadsheets &&
                    $lastSpreadsheetActivity->policy_spreadsheets->policy_spreadsheet_affiliate_count === 1 &&
                    $lastSpreadsheetActivity->policy_spreadsheets->policy_spreadsheet_affiliate->first()->affiliate_id === $lastSpreadsheetActivity->affiliate_id
                ) {
                    $noEmail = true;
                }
                //Enviamos el correo
                if (in_array($period->policySort->work_modality_id, [1, 5]) && !$noEmail) {

                    $affiliate = Affiliate::where('id', $activityPolicy->affiliate_id)->first();
                    $affiliate_name = mb_convert_case(mb_strtolower($affiliate->first_name ?? ''), MB_CASE_TITLE, "UTF-8");
                    $emailIntermediary = $period->policySort->email;
                    $emailTaker = $period->policySort->notification_email;

                    // Obtener id_user de la actividad
                    $userActivityId = $activityPolicy->user_id ?? null;

                    // Construcción del correo electrónico
                    $emailsToSend = [$emailTaker,$emailIntermediary];

                    // Filtrar correos válidos
                    $emailsToSend = array_filter($emailsToSend, function ($email) {
                        return filter_var($email, FILTER_VALIDATE_EMAIL);
                    });

                    //Contruccion del cuerpo del correo a enviar
                    $emailBuild = TemplateBuilder::build(
                        Templates::MONTHLY_PAYROLL_NOT_RECEIVED,
                        [
                            'policy_sort' => $period->policySort->formatSortNumber(),
                            'affiliate_name' => $affiliate_name ?? '',
                            'monthName' => $periodMonth,
                            'year' => $year,
                        ]
                    );
                    if(!empty($emailsToSend)){
                        $mailSent = new SendDocumentDataBase(
                            implode(',', $emailsToSend),
                            $emailBuild['subject'],
                            "<EMAIL>",
                            $emailBuild['subject'],
                            [
                                "text" => $emailBuild['body'],
                                "sender" => $emailBuild['sender']
                            ],
                            "<EMAIL>",
                            [], // Sin archivos adjuntos
                            "send_document_db",
                            $activityPolicy->client_id,
                            $request->getHost(),
                            $activityPolicy->id,
                            Action::REPORTAR_PLANILLA_TOMADOR,
                            Service::SERVICE_REPORT_TAKEN_FORM_MNK
                        );
                        $mailSent->sendMail();
                    }
                }

                DB::commit();
                $count++;

                DB::commit();
            }catch (\Exception $e){
                $errors++;
                DB::rollback();
                dd($e);
                continue;
            }
        }
        return response()->json([
            'message' => 'Correos enviados correctamente para todas las actividades sin planilla definitiva',
            'count' => $count,
            'errors' => $errors
        ]);
    }

}
