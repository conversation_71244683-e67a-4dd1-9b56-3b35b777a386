<?php

namespace App\Http\Controllers\Services;
use App\Actions\ActionPolicySortCollection;
use App\Actions\ActionRenewalSort;
use App\MailTemplates\Constants\Templates;
use App\MailTemplates\TemplateBuilder;
use App\Providers\AppServiceProvider;
use App\States\StatePolicySortCollection;
use Carbon\Carbon;
use DateTime;
use App\ActivityDocument;
use App\ConstancySort;
use App\Action;
use App\Actions\ActionConstaciasSort;
use App\Activity;
use App\ActivityAction;
use App\ActivityActionDocument;
use App\Affiliate;
use App\Client;
use App\Mail\SendDocumentDataBase;
use App\PolicySort;
use App\PolicySortCollection;
use App\Http\Controllers\Controller;
use App\Service;
use App\State;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use App\Http\Controllers\ActionController;
use App\Http\Controllers\Tables\MailBoardController;
use App\RenewalSorts;
use App\States\StatePoliza;
use Illuminate\Support\Facades\DB;
use PDF;

class RenewalSortController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    
     private $type_cost;

     public function __construct()
    {
        $this->middleware('auth')->except([
            'generateRenewalSort',
            'issueExpirationNotice'
        ]);

        $this->type_cost = [
            'policy_certification_up_to_date' => 'Certificado de póliza al día',
            'outstanding_sums_to_be_paid' => 'Constancia de sumas pendientes por pagar',
            'employee_account_statement' => 'Estado de cuenta del trabajador',
            'premiums_paid_certificate' => 'Constancia de primas pagadas'
        ];
    }


    public function form(Request $req, $cpath, $id)
    {
        //Buscamos el cliente
        $client   = Client::where('path', $cpath)->first();
        //Buscamos la actividad de la constancia por su activity_id
        $activity = Activity::where('client_id', $client->id)->where('id', $id)->first();
        //Buscamos la poliza con el parent_id de la constancia
        $policySort = PolicySort::where('activity_id', $activity->parent_id)->first();
        //Buscamos la renovacion por activity_id
        $renewalSorts = RenewalSorts::where('activity_id', $activity->id)->first();

        $data = $this->returtDataRenewal($activity, $policySort);

        return view('services.renewal_sort.form',
            [
                'activity'  => $activity,
                'id'    => $id,
                'policySort' => $policySort,
                'renewalSorts' => $renewalSorts,
                'data' => $data
            ]
        );
    }

    public function generateRenewalSort(Request $req, $cpath, $id=null)
    {
        $renewalSort = Action::RENEWAL_SORT;

        $policySortAllRows = PolicySort::whereBetween('validity_to', [Carbon::now(), Carbon::now()->addDays(15)])
            ->where('temporality' , 'permanent')
            ->with('activity')
            ->get();

        if ($id) {
            $policySortAllRows = PolicySort::where('activity_id', $id)->get();
        }

        if ($policySortAllRows->isNotEmpty())
        {
            DB::beginTransaction();
            try {
                $camel= 0;
                foreach ($policySortAllRows as $policySortRow){

                    $activityPolicy = $policySortRow->activity;

                    $existingActivity = Activity::where('parent_id', $activityPolicy->id)
                        ->where('service_id', Service::SERVICE_RENEWAL_SORT_MNK)
                        ->first();

                    if ($id== null && ($existingActivity || $activityPolicy->state_id != StatePoliza::POLIZA_EMITIDA_ACTIVA)) {
                        continue;

                    }

                    $activityNew = new Activity();
                    $activityNew->parent_id = $activityPolicy->id;
                    $activityNew->client_id = $activityPolicy->client_id;
                    $activityNew->affiliate_id = $activityPolicy->affiliate_id;
                    $activityNew->service_id = Service::SERVICE_RENEWAL_SORT_MNK;
                    $activityNew->state_id = State::REGISTRADO;
                    $activityNew->user_id = $activityPolicy->user_id;
                    $activityNew->save();

                    $data = $this->returtDataRenewal($activityNew, $policySortRow);

                    $renewalNew = new renewalSorts();
                    $renewalNew->activity_id = $activityNew->id;
                    $renewalNew->tvi = $data['tvi'];
                    $renewalNew->tm = $data['tm'];
                    $renewalNew->average_salary = $data['average_salary'];
                    $renewalNew->rf = $data['rf'];
                    $renewalNew->tex = $data['tex'];
                    $renewalNew->pe = $data['pe'];
                    $renewalNew->tmdr = $data['tmdr'];
                    $renewalNew->tre = $data['tre'];
                    $renewalNew->premium_value = $data['valor_prima'];
                    $renewalNew->tmi = $data['tmi'];
                    $renewalNew->save();

                    $activityAction = ActionController::create($activityNew->id, $renewalSort::INICIAR_RENOVACION,  'INICIAR RENOVACIÓN');
                    $this->pending_payment_renewal_report_pdf($cpath, $activityNew, $policySortRow);

                    $camel++;
                }

                DB::commit();

                return response()->json(['message' => 'Renovacion generada',
                    'registrosGuardados' => $camel
                ], 200);

            } catch (\Exception $e) {
                DB::rollBack();
                return response()->json([
                    'status' => 'error',
                    'message' => $e->getMessage(),
                ],500);
            }
        }

        return response()->json(['message' => 'No hay renovaciones pendientes',
            'registrosGuardados' => 0,
            'policySortAllRows' => 0
        ], 200);
    }

    public function reportRenewalPayment($cpath, $id)
    {

        $client = Client::where('path', $cpath)->first();
        $activity = Activity::where('client_id', $client->id)->where('id', $id)->first();
        $policySort = PolicySort::where('activity_id', $activity->parent_id)->first();
        $affiliate = Affiliate::where('id', $policySort->activity->affiliate_id)->first();
        $PolicySortController = new PolicySortController();

        try {
            $renewalSort = Action::RENEWAL_SORT;
            $activityAction = ActionController::create($id, $renewalSort::REPORTAR_RENOVACION_PAGADO,  'REPORTAR RENOVACIÓN PAGADA');

            // Agrega el email principal de notificaciones y todos los adicionales
            $emails = PolicySortController::getAdditionalNotificationEmails(
                $policySort->id,
                [
                    $policySort->email, // emailIntermediary
                ]
            );

            $url_env = config('app.url');
            $documents =  AppServiceProvider::$S3_DOCUMENTS_MNK;
            $pdf_condiciones_generales_url = $documents['condiciones_generales'];
            $pdf_norma_tecnica_url = $documents['norma_tecnica'];
            $firstName = $activity->affiliate->first_name ?  mb_convert_case(mb_strtolower( $activity->affiliate->first_name ?? ''), MB_CASE_TITLE, "UTF-8")  : '';
            //Obtenemos los nombres de catón, provincia y distrito
            $ubicacion = $PolicySortController->getLocationNamesFromJson($activity->affiliate->province, $activity->affiliate->canton, $activity->affiliate->district);
            //obtenemos la actividad economica
            $jsonSource = ($policySort->economic_activity == 'public') ? '/js/economic_activity/public.json' : '/js/economic_activity/private.json';
            $json = file_get_contents(public_path($jsonSource));
            $economicActivities = json_decode($json, true);
            $activity_economic_name = collect($economicActivities)->firstWhere('CODE', $policySort->activity_economic_id)['ACTIVITY_NAME'];
            $policySort->economic_activity_name = $activity_economic_name;

            $textEmail = "
                                ¡Buen día, $firstName!
                                
                                Nos complace informarle que hemos procesado satisfactoriamente la renovación de su póliza # ".$policySort->formatSortNumber().".
                                
                                Le invitamos a verificar la siguiente información importante:
                                • Condiciones particulares de su seguro.
                                • Norma técnica adjunta a este correo  <a href='$pdf_norma_tecnica_url'>aquí</a>.                          
                                • Condiciones generales de su póliza, a las cuales puede acceder <a href='{$pdf_condiciones_generales_url}'>aquí</a>
                                
                                Además, le recordamos que para realizar sus reportes de planillas, inclusiones o accidentes,por favor, debe ingresar <a href='{$url_env}'>aquí</a> con su usuario y contraseña. 
                                
                                Nos sentimos sumamente honrados y agradecidos por la confianza que ha depositado en nosotros. Nuestro propósito es transformar la protección en una experiencia ágil, confiable y humana.";

            $text = [
                "text" => $textEmail,
                "sender" => 'mnk aseguramiento'
            ];

            //Pdf para condiciones particulares de la póliza
            $pdf_particular = PDF::loadView("services.plantilla.docs.condiciones_particulares", [
                'policy' => $policySort,
                'ubicacion' => $ubicacion,
                'type_document' =>'Renovación',
                'affiliate' => $affiliate,
            ]);
            Storage::disk('s3')->put("activity_action_document/'Condiciones particulares_{$activityAction->id}.pdf", $pdf_particular->output());
            // Guardamos el documento condiciones particulares
            $activityActionDocument = new ActivityActionDocument();
            $activityActionDocument->activity_action_id = $activityAction->id;
            $activityActionDocument->name = 'Condiciones Particulares';
            $activityActionDocument->path = "activity_action_document/'Condiciones particulares_{$activityAction->id}.pdf";
            $activityActionDocument->save();
            $attachments[] = [
                'type' => 'pdf',
                'path' => $activityActionDocument->path,
                'name' => 'Condiciones particulares.pdf',
            ];
            $subject = "Renovación de la póliza - #{$policySort->formatSortNumber()}";

            try {
                foreach ($emails as $email) {
                    $this->sendEmail([$email], $subject, $text, $attachments, $client, $policySort->activity_id, $id, $policySort, 'evicertica');
                }
            } catch (\Throwable $e) {
               dd($e);
            }

        } catch (\Exception $e) {
            throw new Exception("Error en la aprobación del pago reportRenewalPayment: " . $e->getMessage());
        }

        return $activityAction;
    }

    //REPORTAR RENOVACIÓN PENDIENTE PAGO
    public function pending_payment_renewal_report_pdf ($cpath, $activityRenewal, $policySort)
    {

        $activity = Activity::where('id', $policySort->activity_id)->first();
        $affiliate = Affiliate::where('id', $policySort->activity->affiliate_id)->first();
        $client = Client::where('path', $cpath)->first();

        DB::beginTransaction();

        try {
            $renewalSort = Action::RENEWAL_SORT;

            $activityAction = ActionController::create($activityRenewal->id, $renewalSort::REPORTAR_RENOVACION_PENDIENTE_PAGO,  'REPORTAR RENOVACIÓN PENDIENTE DE PAGO');

            $pdf = PDF::loadView('services.renewal_sort.docs.pending_payment_renewal_report_pdf', [
                'activity' => $activity,
                'policySort' => $policySort,
                'watermark' => true,
            ]);

            // Generamos el nombre del archivo
            $document = 'reporte_renovacion';
            $fileName = "{$document}_{$activityRenewal->id}.pdf";

            // Guardar el archivo en S3
            $filePath = "activity_action_document/{$fileName}";
            Storage::disk('s3')->put($filePath, $pdf->output());

            // Guardamos el path del documento en la base de datos
            $activityActionDocument = new ActivityActionDocument();
            $activityActionDocument->activity_action_id = $activityAction->id;
            $activityActionDocument->name = $document;
            $activityActionDocument->path = $filePath;
            $activityActionDocument->save();

//            $emailIntermediary = $policySort->email;
//            $emailTaker = $affiliate->email;
//
//            $emails = array_filter([$emailIntermediary, $emailTaker], function ($email) {
//                return !empty($email);
//            });
//
//            $text = [
//                "text" => "Estimado cliente, adjunto encontrará los datos de la renovación",
//                "sender" => 'MNK Seguros'
//            ];
//            $attachments = [
//                [
//                    'path' => $filePath,
//                    'name' => basename($filePath),
//                    'type' => 'PDF'
//                ]
//            ];
//
//            $subject = "Renovación póliza SORT-{$policySort->id}";
//
//            $this->sendEmail($emails, $subject, $text, $attachments, $client, $policySort->activity_id, $activityAction->id, $policySort, 'evicertica');


            //se genera el servicio COBROS POLIZA SORT.

            $activityNew = new Activity();
            $activityNew->client_id = $client->id;
            $activityNew->affiliate_id = $affiliate->id;
            $activityNew->service_id = Service::SERVICE_POLICY_SORT_COLLECTION_MNK;
            $activityNew->state_id = State::REGISTRADO;
            $activityNew->parent_id = $policySort->activity_id;//$activityRenewal->id;
            $activityNew->user_id = $activityRenewal->user_id;
            $activityNew->save();

            $data = $this->returtDataRenewal($activityRenewal, $policySort);

            $data_request = [
                'total_amount' => $data['valor_prima'] ?? 0
            ];

            $policyCollectionController = new PolicySortCollectionController();
            $policyCollectionController->reportRenewalReceiptPeriod( new Request($data_request), $cpath, $activityNew->id);

            DB::commit();

        } catch (Exception $e) {
            DB::rollback();

            return response()->json([
                'status' => 'error',
                'message' => 'Ocurrió un error al enviar el correo.'
            ], 500);
        }

        return response()->json([
            'status' => 'success',
            'message' => 'Registro creado y email enviado exitosamente'
        ]);

    }

    public function issueExpirationNotice(Request $req, $cpath)
    {

        $renewalSort = Action::RENEWAL_SORT;
        $client = Client::where('path', $cpath)->first();
        $policySortAllRows = PolicySort::where('validity_to', Carbon::now()->addDays(45)->toDateString())
            ->where('temporality' , 'permanent')->get();

        if ($policySortAllRows->isNotEmpty()) {
            DB::beginTransaction();
            try {

                $camel=0;
                foreach ($policySortAllRows as $policySortRow) {

                    $activityPolicy = $policySortRow->activity;
                    $affiliate = $activityPolicy->affiliate;

                    if ($activityPolicy->state_id != StatePoliza::POLIZA_EMITIDA_ACTIVA) {
                        continue;
                    }

                    $activityAction = ActionController::create($activityPolicy->id, $renewalSort::EMITIR_PREAVISO_VENCIMIENTO,  'EMITIR PRE AVISO VENCIMIENTO');

                    $pdf = PDF::loadView('services.renewal_sort.docs.issue_expiration_notice_pdf', [
                        'activity' => $activityPolicy,
                        'policySort' => $policySortRow,
                        'watermark' => true,
                    ]);

                    // Generamos el nombre del archivo
                    $document = 'preaviso_vencimiento';
                    $fileName = "{$document}_{$activityPolicy->id}.pdf";

                    // Guardar el archivo en S3
                    $filePath = "activity_action_document/{$fileName}";
                    Storage::disk('s3')->put($filePath, $pdf->output());

                    // Guardamos el path del documento en la base de datos
                    $activityActionDocument = new ActivityActionDocument();
                    $activityActionDocument->activity_action_id = $activityAction->id;
                    $activityActionDocument->name = $document;
                    $activityActionDocument->path = $filePath;
                    $activityActionDocument->save();

                    $emailIntermediary = $policySortRow->email;
                    $emailTaker = $affiliate->email;

                    $emails = array_filter([$emailIntermediary, $emailTaker], function ($email) {
                        return !empty($email);
                    });

                    $text = [
                        "text" => "Estimado cliente, adjunto encontrará los datos del pre aviso del vencimiento",
                        "sender" => 'MNK Seguros'
                    ];
                    $attachments = [
                        [
                            'path' => $filePath,
                            'name' => basename($filePath),
                            'type' => 'PDF'
                        ]
                    ];

                    $subject = "Pre aviso vencimiento póliza SORT-{$policySortRow->formatSortNumber()}";

                    $this->sendEmail($emails, $subject, $text, $attachments, $client, $policySortRow->activity_id, $activityAction->id, $policySortRow);

                    $policySortRow->expiration_notice_date = now();
                    $policySortRow->save();

                    $camel++;

                }

                DB::commit();

                return response()->json([
                    'message' => 'Emitido aviso de vencimiento',
                    'registrosGuardados' => $camel,
                    'policySortAllRows' => $policySortAllRows
                ], 200);

            } catch (\Exception $e) {
                DB::rollBack();
                return response()->json([
                    'status' => 'error',
                    'message' => $e->getMessage(),
                ]);
            }
        } else {
            return response()->json(['message' => 'No hay polizas pendientes de emitir de vencimiento'], 200);
        }


    }

    //ME-1807 - REPORTAR RENOVACION - NO PAGADA
    public function reportUnpaidRenewal($cpath, $id)
    {

        $client = Client::where('path', $cpath)->first();
        $activity = Activity::where('client_id', $client->id)->where('id', $id)->first();

        DB::beginTransaction();

        try {

            $activityAction = ActionController::create($id, ActionRenewalSort::REPORTAR_RENOVACION_NO_PAGADA,  'REPORTAR RENOVACIÓN NO PAGADA');

            //file
//            $pdf = PDF::loadView('services.renewal_sort.docs.report_unpaid_renewal_pdf', [
//                'activity' => $activity,
//                'policySort' => $policySort,
//                'watermark' => true,
//            ]);
//
//            $document = 'reporte_renovacion_pago';
//            $fileName = "{$document}_{$id}.pdf";
//
//            $filePath = "activity_action_document/{$fileName}";
//            Storage::disk('s3')->put($filePath, $pdf->output());
//
//            $activityActionDocument = new ActivityActionDocument();
//            $activityActionDocument->activity_action_id = $activityAction->id;
//            $activityActionDocument->name = $document;
//            $activityActionDocument->path = $filePath;
//            $activityActionDocument->save();
//            $files = [
//                [
//                    'path' => $filePath,
//                    'name' => basename($filePath),
//                    'type' => 'PDF'
//                ]
//            ];

            $emailIntermediary = $activity->parent->policy_sort->email;
            $emailTaker = $activity->parent->affiliate->email;

            $emails = array_filter([$emailIntermediary, $emailTaker], function ($email) {
                return !empty($email);
            });

            Carbon::setLocale('es');
            $fechaFinVigencia = Carbon::parse($activity->parent->policy_sort->validity_to)->formatLocalized('%A %d de %B de %Y');

            $emailBuild = TemplateBuilder::build(
                Templates::NOTIFY_NON_RENEWAL,
                [
                    'policy_sort' => $activity->parent->policy_sort->formatNumberConsecutive(),
                    'name' => mb_convert_case(mb_strtolower($activity->parent->affiliate->full_name ?? ''), MB_CASE_TITLE, "UTF-8"),
                    'fecha_fin_vigencia' => $fechaFinVigencia
                ]
            );

            if ($emails != null) {

                $mailSent = new SendDocumentDataBase(
                    $emails,
                    $emailBuild['subject'],
                    "<EMAIL>",
                    "Notificación de no renovación de la póliza",
                    [
                        "text" => $emailBuild['body'],
                        "sender" => $emailBuild['sender']
                    ],
                    "<EMAIL>",
                    [], //files
                    "send_document_db",
                    $client->id,
                    request()->getHost(),
                    $activity->id,
                    $activityAction,
                    $activity->parent->service->id
                );
                
                // Capturar el resultado del envío
                $result = $mailSent->sendMail();

                //Registramos los datos del correo enviado para la trazabilidad
                $mailBoardController = new MailBoardController();
                $mailBoardController->createRegisterMail(
                    $activity->id,
                    $activity->service->id, 
                    $activity->parent->policy_sort->consecutive, 
                    'Tomador', 
                    mb_convert_case(mb_strtolower($activity->parent->affiliate->full_name ?? ''), MB_CASE_TITLE, "UTF-8"), 
                    $activity->parent->affiliate->doc_number, 
                    $emailBuild['subject'], 
                    $emailBuild['body'],
                    $emails, 
                    $result,
                    null
                );
        
            }

            DB::commit();

        } catch (Exception $e) {
            DB::rollback();
            return response()->json([
                'status' => 'error',
                'message' => 'Ocurrió un error al enviar el correo.',
                'error' => $e->getMessage()
            ], 500);
        }

        return response()->json([
            'status' => 'success',
            'message' => 'Registro creado y email enviado exitosamente'
        ]);

    }

    private function sendEmail($emails, $subject, $text, $attachments, $client, $id, $activityAction, $policySort, $mailer = 'default')
    {

        $mailSent = new SendDocumentDataBase(
            implode(',', $emails),         // Correos a enviar
            $subject,                      // Asunto del correo
            "<EMAIL>",            // Remitente
            $subject,                      // Asunto
            $text,                         // Cuerpo del email
            "<EMAIL>",  // Email de respuesta
            $attachments,                  // Archivos adjuntos
            "send_document_db",            // Tipo de envío
            $client,                       // Información del cliente
            request()->getHost(),          // Dominio
            $id,                           // ID de la actividad
            $activityAction,           // ID de la acción de la actividad
            $policySort->activity->service->id, // ID del servicio
            $mailer
        );

        // Capturar el resultado del envío
        $result = $mailSent->sendMail();

        $activity = Activity::where('id', $id)->first();

        //Registramos los datos del correo enviado para la trazabilidad
        $mailBoardController = new MailBoardController();

        $mailBoardController->createRegisterMail(
            $id,
            Service::SERVICE_RENEWAL_SORT_MNK,
            $policySort->consecutive, 
            'Tomador', 
            mb_convert_case(mb_strtolower($activity->affiliate->full_name ?? ''), MB_CASE_TITLE, "UTF-8"), 
            $activity->affiliate->doc_number, 
            $subject, 
            $text,
            $emails, 
            $result,
            $attachments
        );
    }

    public function returtDataRenewal($activity, $policySort){

        $salaryRecords = Activity::select('policy_spreadsheets.total_salaries')
            ->leftJoin('policy_spreadsheets', 'policy_spreadsheets.activity_id', '=', 'activities.id')
            ->where('activities.parent_id', $activity->parent_id)
            ->where('activities.service_id', Service::SERVICE_REPORT_TAKEN_FORM_MNK)
            ->orderBy('policy_spreadsheets.id', 'desc')
            ->limit(3)
            ->get();

        $totalSalaries = 0;

        foreach ($salaryRecords as $record) {
            $totalSalaries += $record->total_salaries;
        }

        $average_salary = $totalSalaries*12;


        $result = QuotationController::calculatePolicyPrice($policySort);

        $tvi = 0;
        $rf = 0;

        if ($policySort->periodicity == '1') { //ANUAL
            $tvi = $result['percentage'];
        } else if ($policySort->periodicity == '2') { //SEMESTRAL
            $tvi = round($result['percentage']*(1.04), 2);
            $rf = 1.04;
        } else if ($policySort->periodicity == '3') { //TRIMESTRAL
            $tvi = round(($result['percentage'])*(1.06), 2);
            $rf = 1.06;
        } else if ($policySort->periodicity == '4') { //MENSUAL
            $tvi = round($result['percentage']*(1.08), 2);
            $rf = 1.08;
        } else { //PAGO UNICO
            $tvi = $result['percentage'];
        }

        $pe=0;
        $tex = $tvi+$pe;
        $tmdr = ($result['percentage']*0.9)*$rf;
        $tmi = ($tmdr*100)<$tvi ? $tmdr : $tvi;
        $tre = $tmi<$tex ? $tex : $tmi;

        $valor_prima = ($tre/100)*$average_salary;

        // en caso de que el valor de la renovacion de 0 se toma de amount_policy
        $valor_prima = $valor_prima != 0 ? $valor_prima : ($policySort->amount_policy ?? 0);

        $data = [
            'tvi' => $tvi,
            'tm' => $result['percentage']*100,
            'average_salary' => $average_salary,
            'rf' => ($rf-1)*100,
            'tex' => $tex,
            'pe' => $pe,
            'tmdr' => $tmdr*100,
            'tre' => $tre,
            'valor_prima' => $valor_prima,
            'tmi' => $tmi*100
        ];

        return $data;
    }

    public function reportUnpaidRenewalReceipt(){

        DB::beginTransaction();
        try {

            //registros que tengan mas de 11 dias de generados
            $policySortCollection = PolicySortCollection::where('created_at', '<', Carbon::now()->subDays(11))
                                            ->whereIn('payment_status', ['pending-approval', 'pending'])
                                            ->where('type_receipt', PolicySortCollection::RENEWAL)->get();

            foreach ($policySortCollection as $row) {

                if ($row->activity->parent->state_id == StatePolicySortCollection::RECIBO_RENOVACION_PENDIENTE_PAGO) {
                    $activityAction = ActionController::create($row->activity->parent->id, ActionPolicySortCollection::REPORTAR_RECIBO_RENOVACION_NO_PAGADO, 'Reportar recibo renovacion no pagado');
                }

                $row->payment_status = PolicySortCollection::PAYMENT_STATUS_CANCELLED;
                $row->save();
            }

            DB::commit();

            return response()->json([
                'message' => 'ejecución exitosa.',
            ], 200);

        } catch (Exception $e) {
            DB::rollback();

            return response()->json([
            'status' => 'error',
            'message' => 'Ocurrió un error',
            'error' => $e->getMessage()
            ], 500);
        }

    }

}
