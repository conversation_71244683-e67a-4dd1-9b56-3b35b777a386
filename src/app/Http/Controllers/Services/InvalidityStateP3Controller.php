<?php

namespace App\Http\Controllers\Services;

use App\Activity;
use App\Client;
use App\Http\Controllers\Controller;
use App\InvalidityStatePthree;
use App\InvalidityStateBoardCieTen;
use App\InvalidityStateBoardFollow;
use App\InvalidityStateBoardJnciCieTen;
use App\InvalidityStateBoardJrciCieTen;
use App\InvalidityStateBoardPart;
use App\InvalidityStateBoardSend;
use App\InvalidityStateDiagnostic;
use App\InvalidityStateDocument;
use App\InvalidityStateNotification;
use App\InvalidityStatePclDeficience;
use App\InvalidityStatePclDiagnostic;
use App\InvalidityStatePclExam;
use App\InvalidityStatePclHistories;
use App\InvalidityStatePclResend;
use App\InvalidityStatePclSend;
use App\InvalidityStatePclTableData;
use App\InvalidityStatePhaseBoard;
use App\InvalidityStatePhasePcl;
use App\InvalidityStateResend;
use Carbon\Carbon;
use DateTime;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\View;
use PDF;

class InvalidityStateP3Controller extends Controller
{

    public function form(Request $req, $cpath, $id)
    {
        $client = Client::where('path', $cpath)->first();
        $activity = Activity::where('client_id', $client->id)->where('id', $id)->first();
        return view('services.invalidity_state_pthree.form', ['activity' => $activity, 'client' => $client]);
    }


    public function save(Request $req, $cpath, $id)
    {
        $client = Client::where('path', $cpath)->first();
        $activity = Activity::where('client_id', $client->id)->where('id', $id)->first();

        DB::beginTransaction();
        try {
            if ($activity->invalidity_state_pthree == null) {
                $invalidity_state_pthree = new InvalidityStatePthree();
                $invalidity_state_pthree->activity_id = $activity->id;
            } else {
                $invalidity_state_pthree = $activity->invalidity_state_pthree;
            }

            $invalidity_state_pthree->save();

            DB::commit();

        } catch (Exception $e) {
            DB::rollback();
        }

        return redirect("servicio/$id/invalidity_state_pthree", 302, [], true);
    }

    public function pdf(Request $req, $cpath, $id)
    {
        $client = Client::where('path', $cpath)->first();
        $activity = Activity::where('client_id', $client->id)->where('id', $id)->first();

        $pdf = PDF::loadView('services.invalidity_state_pthree.docs.blank_template_pdf', [
            'activity' => $activity,
            'watermark' => true,
        ]);
        return $pdf->stream('preview.pdf');
    }
}
