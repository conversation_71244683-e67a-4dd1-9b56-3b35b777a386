<?php

namespace App\Http\Controllers\Services;

use App\Action;
use App\Actions\ActionGisSort;
use App\Actions\ActionPeaffiliatepayments;
use App\Actions\ActionPeItSort;
use App\Activity;
use App\ActivityAction;
use App\ActivityActionDocument;
use App\ActivityDocument;
use App\Affiliate;
use App\AffiliatePayment;
use App\BankAccountIban;
use App\Client;
use App\GisSort;
use App\Http\Controllers\ActionController;
use App\Http\Controllers\Controller;
use App\Http\Controllers\Tables\CompensationBoardController;
use App\Http\Controllers\Tables\MailBoardController;
use App\Mail\SendDocumentDataBase;
use App\MailTemplates\Constants\Templates;
use App\MailTemplates\TemplateBuilder;
use App\PaymentSimulator;
use App\PeitFractionSort;
use App\PeitInabilitySort;
use App\PeItSort;
use App\PeItSortCaseDx;
use App\PolicySort;
use App\PolicySpreadsheet;
use App\PolicySpreadsheetAffiliate;
use App\Service;
use App\ServiceDocument;
use App\State;
use App\States\StatePeItSort;
use App\TrmRate;
use Carbon\Carbon;
use DateTime;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use PDF;
class PeItSortController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth')->except([
            'reportFullDisabilityPayment',
            'calculateAndSaveFraccionamientos',
            'generateFractionPaymentReport',
            'itPaymentSuspension'
        ]);
    }

    public function form(Request $req, $cpath, $id)
    {
        // Busca el cliente y verifica si existe
        $client = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('client_id', $client->id)->where('id', $id)->first();
        $activity_diagnostic = Activity::with([
            'medical_services_sort.followUps.diagnostics',
        ])->findOrFail($id);

        
        $daysInabilty = 0;
        $amount_pay = '';
        $formattedAmount_pay = '';
        $gisSort = null;
        $document = '';
        $activityParentPm = null;

        if ($activity->parent_id){
            $activityParentPm = Activity::where('client_id', $client->id)->where('id', $activity->parent_id)->first();
            if ($activityParentPm->service_id == Service::SERVICE_MEDICAL_SERVICES_SORT_MNK){
                $gisSort = GisSort::where('activity_id', $activityParentPm->parent_id)->first();
            }else{
                $gisSort = GisSort::where('activity_id', $activity->parent_id)->first();
            }
        }
        $activityDocument = ActivityDocument::where('activity_id', $activity->id)
            ->where('document_id',261)
            ->first();
        if ($activityDocument){
            $document =$activityDocument->path;
        }

        $peItSort = PeItSort::where('activity_id', $activity->id)->first();
        // Busca PeitInabilitySort solo si PeItSort existe
        $peitInabilitySort = null;
        $caseDx = collect();
        if ($peItSort) {
            $peitInabilitySort = PeitInabilitySort::where('pe_it_sort_id', $peItSort->id)->first();
            //$caseDx = PeItSortCaseDx::where('pe_it_sort_id', $peItSort->id)->get();
            $caseDx = PeItSortCaseDx::selectRaw('*')
                ->where('pe_it_sort_id', $peItSort->id)
                ->groupBy('classification', 'code_cie_10', 'diagnostico_pe', 'laterality')
                ->get();
        }

        // Busca PeitFractionSort solo si PeitInabilitySort existe
        $peitFractionSort = null;
        if ($peitInabilitySort) {

            $daysInabilty = $peitInabilitySort->days_it;

            $activityGis = $gisSort->activity_id;

            $totalDias = DB::table('activities as a')
                ->leftJoin('pe_it_sorts as p', 'p.activity_id', '=', 'a.id')
                ->leftJoin('peit_inability_sorts as pp', 'pp.pe_it_sort_id', '=', 'p.id')
                ->leftJoin('activities as ap', 'ap.id', '=', 'a.parent_id')
                ->where('ap.parent_id', $activityGis)
                ->where('a.id', '<=', $id)
                ->where('a.state_id', '<>', StatePeItSort::RECHAZAR_IT)
                ->sum('pp.days_it');

            $peitFractionSort = PeitFractionSort::where('peit_inability_sort_id', $peitInabilitySort->id)->get();

        }

        //Para obtener datos de planilla
        $dateAccident = Carbon::parse($gisSort->date_accident)->endOfDay();

        $lastThreeMonths = PolicySpreadsheetAffiliate::query()
            ->where('affiliate_id', $activity->affiliate_id)
            ->where('created_at', '<=', $dateAccident)
            ->orderBy('id', 'desc')
            ->limit(3)
            ->select('policy_spreadsheet_id', 'monthly_salary', 'days', 'created_at')
            ->get();

        //No borrar esto ya que va a asociado a al incidente ME-1314 se quema la planilla ya que se pago esa incapacidad
        if ($id == 4463){
            $lastThreeMonths = PolicySpreadsheetAffiliate::query()
                ->where('affiliate_id', $activity->affiliate_id)
                ->where('policy_spreadsheet_id',116)
                ->select('policy_spreadsheet_id', 'monthly_salary', 'days', 'created_at')
                ->get();
        }


        $totalSalary = $lastThreeMonths->sum('monthly_salary');
        $totalDays = $lastThreeMonths->sum('days');
        $salaryDailyAverage = ($totalDays > 0) ? ($totalSalary / $totalDays) : 0;
        $SMD = 11953.65;

        $currencySymbol = '';

        $firstItem = $lastThreeMonths->first();
        if ($firstItem){
            $policy_spreadsheet = PolicySpreadsheet::where('id', $firstItem->policy_spreadsheet_id)
                ->select('activity_id')
                ->first();
            $activity_policy_spreadsheet_id = Activity::where('id',$policy_spreadsheet->activity_id)
                ->select('parent_id')
                ->first();
            $policy_sort = PolicySort::where('activity_id', $activity_policy_spreadsheet_id->parent_id)
                ->select('type_currency')
                ->first();

            switch ($policy_sort->type_currency) {
                case 'USD':
                    $currencySymbol = '$';
                    break;
                case 'CRC':
                    $currencySymbol = '₡';
                    break;
                default:
                    $currencySymbol = '';
                    break;
            }
        }
        // Formatear los salarios con miles y dos decimales
        $totalSalaryFormate = $currencySymbol . number_format($totalSalary, 2, '.', ',');
        $formattedSalaryDailyAverage = $currencySymbol . number_format($salaryDailyAverage, 2, '.', ',');
        $formattedSMD = $currencySymbol . number_format($SMD, 2, '.', ',');

        $subsidyFirst45Days = $salaryDailyAverage * 0.60;

        // Subsidio Salarial Después de 45 días (SS = SMD + ((SD - SMD) * 0.67))
        $subsidyAfter45Days = $SMD + (($salaryDailyAverage - $SMD) * 0.67);


        //Lllegase el caso que los subsidios sean menores al valor del “SMD(Salario mínimo diario”)
        $subsidyFirst45Days = max($subsidyFirst45Days, $SMD);
        $subsidyAfter45Days = max($subsidyAfter45Days, $SMD);

        // Formatear ambos valores para presentarlos con separadores de miles y dos decimales
        $formattedSubsidyFirst45Days =  $currencySymbol . number_format($subsidyFirst45Days, 2, ',', '.');
        $formattedSubsidyAfter45Days =  $currencySymbol . number_format($subsidyAfter45Days, 2, ',', '.');

        $showComponent = false;
        if ($peitInabilitySort && $peitInabilitySort->fractioning == 1 && $activity && $activity->state_id != StatePeItSort::EN_VALIDACION_DE_PAGOS && $activity->state_id != StatePeItSort::VALIDACION_DE_INCAPACIDAD ) {
            $showComponent = true;
        }


        if($daysInabilty != 0 ){

             $diasServActual = $daysInabilty;
             $diasAnteriores = $totalDias-$daysInabilty;

             if ($diasAnteriores == 0 || $totalDias <45) {
                 $amount_pay = round($subsidyFirst45Days, 2) * $daysInabilty;
             } else if ($diasAnteriores<=45 && $totalDias>45) {
                 $diasAntes45 = 45-$diasAnteriores;
                 $amount_pay = $subsidyFirst45Days * ($diasAntes45 ==0 ? 45 : $diasAntes45);

                 $diasDespues45 = $diasServActual-$diasAntes45;
                 $amount_pay += $diasDespues45 >0 ? round($subsidyAfter45Days, 2) * $diasDespues45 : 0;

             } else {
                 $amount_pay = round($subsidyAfter45Days, 2) * $daysInabilty ;
             }

            $formattedAmount_pay =  $currencySymbol . number_format($amount_pay, 2, ',', '.');
        }

        if (!empty($activity->affiliate->birthday)) {
            // Convertimos la fecha de nacimiento a un objeto DateTime
            $birthDate = new DateTime($activity->affiliate->birthday);
            $today = new DateTime(); // Fecha actual

            // Calculamos la diferencia de años entre hoy y la fecha de nacimiento
            $age = $today->diff($birthDate)->y; // 'y' devuelve la diferencia en años
        } else {
            $age = null; // En caso de que no haya fecha de nacimiento
        }

        /**
         * Validaciones para obtener resumen de incapacidad
         * */

        $gisActivityId = $gisSort->activity_id ?? null;


        //Obtener IDs de actividades cuyo parent_id es el ID de GIS
        $directActivities = Activity::where('parent_id', $gisActivityId)
            ->where('service_id', Service::SERVICE_PE_IT_SORT_MNK)
            ->whereIn('state_id', [StatePeItSort::EN_PAGO_DE_FRACCIONES,StatePeItSort::PENDIENTE_SOPORTE_DE_PAGO_FRACCION,StatePeItSort::PAGO_SIT_REALIZADO])
            ->pluck('id')->toArray();
        // Buscar el ID de Prestaciones Médicas que tiene como parent_id el de GIS
        $prestacionesMedicasIds = Activity::where('parent_id', $gisActivityId)
            ->where('service_id', Service::SERVICE_MEDICAL_SERVICES_SORT_MNK)
            ->pluck('id')
            ->toArray();

        // Obtener IDs de actividades cuyo parent_id es alguno de los IDs de Prestaciones Médicas
        $indirectActivities = Activity::whereIn('parent_id', $prestacionesMedicasIds)
            ->where('service_id', Service::SERVICE_PE_IT_SORT_MNK)
            ->where('state_id','<>', StatePeItSort::RECHAZAR_IT)
            ->pluck('id')->toArray();

        // Almacenar id
        $allActivityIds = array_merge($directActivities, $indirectActivities);
        //Búsqueda de incapacidades temporales
        $peItSortIds = PeItSort::whereIn('activity_id', $allActivityIds)->pluck('id')->toArray();

        //Obtener resumen de incapacidad
        $disabilities = PeitInabilitySort::whereIn('pe_it_sort_id', $peItSortIds)->get();


        //traer la primera incapacidad para las fechas day 45 y day 540
        $firstPeItSort = PeItSort::whereIn('activity_id', $allActivityIds)
            ->orderBy('created_at', 'asc')
            ->first();

        // Obtener días acumulados
        $totalAccumulatedDays = $disabilities->sum('days_it');

        $affiliateId = $activity->affiliate_id;
        $gisSorts = GisSort::whereHas('activity', function ($query) use ($affiliateId) {
            $query->where('affiliate_id', $affiliateId);
        })->get();

        $averageRate = TrmRate::selectRaw('SUM(rate) / COUNT(*) as average_rate')->value('average_rate');
        $smDpopup = number_format(($SMD/$averageRate) ?? 0, 2, ',', '.');

        $activityLatestPeitSort = Activity::where('affiliate_id', $activity->affiliate_id)
            ->where("service_id", Service::SERVICE_PE_IT_SORT_MNK)
            ->whereHas('PeItSort', function ($query) {
                $query->whereNotNull('iban_account_number');
            })
            ->orderBy('id', 'desc')
            ->first();

        $payroll_type = '';

        if ($lastThreeMonths->count() == 1) {

            $payroll_type = 'Sustituta';

            if ($activity->parent->parent->parent->id) {

                $actionEmision = ActivityAction::where('activity_id',$activity->parent->parent->parent->id)
                    ->where('action_id',Action::EMITIR_POLIZA_DESDE_POLIZA)->first();

                if ($actionEmision) {
                    if ($actionEmision->created_at->toDateString() >= $firstItem->created_at->toDateString()) {
                        $payroll_type = 'Sustituta';
                    } else {
                        $payroll_type = 'Mensual';
                    }
                }
            }
        } elseif ($lastThreeMonths->count()>1) {
            $payroll_type = 'Mensual';
        }

        //consultar a activity actions
        $statusCase = ActivityAction::where('action_id',ActionGisSort::REPORTAR_AVISO_CASO_NO_AMPARADO)->where('activity_id', $gisActivityId)
            ->orderBy('created_at', 'desc')
            ->pluck('action_id')
            ->first();


        //validar si esta la última incapacidad (esto para lograr mostrar los botones de reapertura)
        // Consultar todas las actividades de prestaciones médicas y secundarias
        $activities_pms = Activity::where('parent_id', $gisActivityId)
            ->whereIn('service_id', [
                Service::SERVICE_MEDICAL_SERVICES_SORT_MNK,
                Service::SERVICE_MEDICAL_SERVICES_SECONDARY_CARE_SORT_MNK
            ])
            ->pluck('id')
            ->toArray();

        $activityvalue = Activity::with([
            'affiliate',
            'medical_services_sort',
            'medical_services_sort.followUps' => function ($query) {
                $query->orderBy('follow_up_number');
            },
            'medical_services_sort.followUps.companions',
            'medical_services_sort.followUps.diagnostics',
            'medical_services_sort.followUps.diagnosticsImages',
            'medical_services_sort.followUps.specialists',
            'medical_services_sort.followUps.medicalPrescriptions',
            'medical_services_sort.followUps.controlledMedications',
        ])->where('affiliate_id', $activity->affiliate_id)
        ->where('parent_id', $gisActivityId)
        ->where('client_id', $client->id)
          ->get();

        $day1 = $activity->parent->created_at->format('Y-m-d');
        $day45 = $activity->parent->created_at->format('Y-m-d');
        $day730 = $activity->parent->created_at->format('Y-m-d');

        $count = Activity::where('id', '<', $id)
            ->where('service_id', Service::SERVICE_PE_IT_SORT_MNK)
            ->whereHas('parent_activity', function ($query) use($activity) {
                $query->where('parent_id', $activity->parent->parent_id);
            })->count();

        $typeIt = $count > 0 ? 0 : 1;

        $bankAccountIban = BankAccountIban::all();

        return view('services.pe_it_sort.form', [
            'activity' => $activity,
            'activity_diagnostic' => $activityvalue,
            'affiliate' => $activity->affiliate ?? null, // Verifica si affiliate existe
            'peItSort' => $peItSort,
            'peitInabilitySort' => $peitInabilitySort,
            'peitFractionSort' => $peitFractionSort,
            'id' => $id,
            'totalSalaryFormate' => $totalSalaryFormate,
            'totalDays' => $totalDays,
            'formattedSalaryDailyAverage' =>$formattedSalaryDailyAverage,
            'formattedSMD' => $formattedSMD,
            'formattedSubsidyFirst45Days' => $formattedSubsidyFirst45Days,
            'formattedSubsidyAfter45Days' => $formattedSubsidyAfter45Days,
            'caseDx' => $caseDx,
            'showComponent' =>$showComponent,
            'gisSort' => $gisSort,
            'currencySymbol' => $currencySymbol,
            'formattedAmount_pay' => $formattedAmount_pay,
            'age' => $age,
            'document' => $document,
            'disabilities' => $disabilities,
            'totalAccumulatedDays' => $totalAccumulatedDays,
            'gisSorts' => $gisSorts,
            'smDpopup' => $smDpopup,
            'activityLatestPeitSort' => $activityLatestPeitSort,
            'lastThreeMonths' => $lastThreeMonths,
            'firstItem' => $firstItem,
            'payroll_type' => $payroll_type,
            'activityParentPm' =>$activityParentPm,
            'statusCase' => $statusCase,
            'firstPeItSort' => $firstPeItSort,
            'day1' => $day1,
            'day45' => $day45,
            'day730' => $day730,
            'typeIt' => $typeIt,
            'bankAccountIban' => $bankAccountIban
        ]);
    }


    /**
     * Proceso para guardar los datos de PE IT Sort
     * @param Request $request
     * @param $cpath
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function save(Request $request, $cpath, $id)
    {
        // Busca el cliente por su 'path' y lanza un error si no se encuentra
        $client = Client::where('path', $cpath)->firstOrFail();

        // Busca la actividad asociada al servicio médico
        $activity = Activity::where('client_id', $client->id)
            ->where('id', $id)
            ->firstOrFail();

        // Inicia la transacción
        DB::beginTransaction();

        try {
            // Realiza la validación del request
            $request->validate([
                "name_affiliated_pe" => 'required',
                "birthdate" => 'required',
                "age_pe" => 'required',
                "sexo_pe" => 'required',
                "home_phone_pe" => 'required',
                "phone_pe" => 'required',
                "worker_address_pe" => 'required',
                "email_affiliate_pe" => 'required',
                "payment_of_heirs" => 'required',
                "disabilityissuedate" => 'required',
                "dateoffilingdisability" => 'required',
                "service_group_pe" => 'required',
                "type_of_service_pe" => 'required',
                "medical_document_pe" => 'required',
                "medical_identification_pe" => 'required',
                "names_surnames_pe" => 'required',
                "medical_record_pe" => 'required',
                "specialty_pe" => 'required',
                "day_1" => 'required',
                "day_540" => 'required',
                "day_45" => 'required',
                //"identification_doctor_or_dentist" => 'required',
                "rehabilitation_concept_titular" => 'required',
                "identification_number_titular" => 'required',
                "name_titular" => 'required',
                "iban_account_number" => 'required',
                "name_of_the_bank" => 'required',
                //"general_information_medical" => 'required',
                //"procedure_information_medical" => 'required',
                //"detail_periods_of_disability_medical" => 'required',
                //"payment_information_medical" => 'required',
            ]);

            // Busca si ya existe un registro de PeItSort
            $peItSort = PeItSort::where('activity_id', $activity->id)->first();

            $codeCie10Array = $request->input('code_cie_10.cod', []); // Esto puede ser un array
            $code_cie_10 = reset($codeCie10Array); // Obtiene el primer elemento del array


            // Inicializar el array $incapacidades
            $incapacidades = [
                $this->buildIncapacityData($request, $code_cie_10, $peItSort ? $peItSort->id : null)
            ];
            $data = $request->all();
            $data['activity_id'] = $activity->id;
            $data['day_1'] = $request->input('day_1_submit');
            $data['birthdate'] = $request->input('birthdate_submit');
            $data['date_maximum_medical'] = $request->input('date_maximum_medical_submit');
            $data['date_payment'] = $request->input('date_payment_submit');
            $data['resolution_date_medical'] = $request->input('resolution_date_medical_submit');
            $data['day_45'] = $request->input('day_45_raw');
            $data['day_540'] = $request->input('day_540_raw');
            $data['dateoffilingdisability'] = $request->input('dateoffilingdisability_raw');
            $data['disabilityissuedate'] = $request->input('disabilityissuedate_raw');
            $data['date_mnk'] = $request->input('date_mnk_submit');
            $data['ccss_pe'] = $request->input('ccss_pe');
            $data['ocupation'] = $request->input('ocupation');


            //guardar información del afiliado
            if( $request->input('sexo_pe')){
                $affiliate = Affiliate::where('id', $activity->affiliate_id)->first();
                $affiliate->gender = $request->input('sexo_pe') ?? $affiliate->gender;
                $affiliate->save();
            }

            if ($peItSort) {
                // Actualizar el registro existente
                $peItSort->update($data);
                $peItSortId = $peItSort->id;

                // Busca el registro de incapacidad
                $peitInabilitySort = PeitInabilitySort::where('pe_it_sort_id', $peItSortId)->first();

                //generar nuevamente la incapacidad medica al haber cambiado la fecha fin de incapacidad
                $endIncapacity = $request->input('end_raw');

                //validar que es una fecha válida
                if ($endIncapacity && $peitInabilitySort) {
                    $date = Carbon::parse($endIncapacity);
                    //validar si la fecha cambió
                    if($peitInabilitySort->end_date != $endIncapacity) {
                        $medicalServiceController = new MedicalServicesController();
                        $medicalServiceController->AfterChangeEndDateMedicalDisability($cpath, $activity->id, $endIncapacity); //ME-2594
                    }
                }

                // Actualizar el registro de incapacidad
                $this->updateIncapacity($peItSortId, $incapacidades[0], $request, $activity);

            } else {
                // Crear nuevo registro si no existe
                $peItSort = PeItSort::create($data);
                $peItSortId = $peItSort->id;

                // Guardar incapacidad
                $this->insertIncapacity($incapacidades[0]);
            }

            if(!is_null($request->input('requirement_submitted')))
            {
                //buscar la actividad GIS
                $activityGis = $activity->getAncestorGis();

                //actualizar los datos del GIS SORT
                $gis_sort = GisSort::where('activity_id', $activityGis->id)->first();
                $gis_sort->requirement_submitted = $request->input('requirement_submitted');
                $gis_sort->save();

            }

            // Guardar o actualizar los diagnósticos
            $this->saveDiagnostics($request, $peItSort->id);

            $path =$request->filePath;
            if ($path){
                $activityDocument = ActivityDocument::where('activity_id', $activity->id)
                    ->where('document_id',261)
                    ->first();
                if($activityDocument){
                    $activityDocument->path = $path;
                    $activityDocument->uploaded_at = Carbon::now();
                    $activityDocument->save();
                }else{
                    $serviceDocument = ServiceDocument::where('service_id', Service::SERVICE_PE_IT_SORT_MNK)->get();

                    foreach ($serviceDocument as $document) {
                        $newActivityDocument = new ActivityDocument();
                        $newActivityDocument->document_id = $document->id;
                        $newActivityDocument->activity_id = $activity->id;
                        $newActivityDocument->path = $path;
                        $newActivityDocument->uploaded_at = Carbon::now();
                        $newActivityDocument->save();
                    }
                }

            }


            //actualizar informacion en afiliado de la cuenta
            $affiliate = Affiliate::where('id', $activity->affiliate_id)->first();
            $affiliate->iban_account_number = $request->input('iban_account_number');
            $affiliate->name_of_the_bank = $request->input('name_of_the_bank');
            $affiliate->save();
            // Commit de la transacción
            DB::commit();
            return response()->json(['success' => true, 'message' => 'Datos guardados correctamente.']);
        } catch (QueryException $e) {
            DB::rollback();
            return response()->json(['success' => false, 'message' => 'Error en la base de datos: ' . $e->getMessage()]);
        }
    }

    private function buildIncapacityData($request, $code_cie_10, $peItSortId)
    {

        return [
            'pe_it_sort_id' => $peItSortId,
            'type_payment' => $request->input('type_payment'),
            'start_date' => $request->input('start_date_raw'),
            'end_date' => $request->input('end_raw'),
            'institution_healt' => $request->input('institution_healt'),
            'days_inability' => $request->input('days_inability'),
            'days_it_pay' => $request->input('days_it_pay'),
            'amount_pay' => str_replace(['₡', '$', '.', ','], ['', '', '', '.'], $request->input('amount_pay')),
            'days_it' => $request->input('days_it'),
            'fractioning' => $request->input('fractioning'),
            'pp_forms_submitted' => $request->input('pp_forms_submitted'),
            'tdr' => $request->input('tdr'),
            'sd' => $request->input('sd'),
            'smd' => $request->input('smd'),
            'original' => $request->input('original'),
            'related_diagnosis' => $request->input('related_diagnosis'),
            'inability_file' => $request->input('inability_file'),
            'days_requested_it' => $request->input('days_requested_it'),
            'days_paid_pe' => $request->input('days_paid_pe'),
            'state_pay' => $request->input('state_pay'),
            'date_payment' => $request->input('date_payment_submit'),
            'id_pago_pe' => $request->input('id_pago_pe'),
            'iban_account_number' => $request->input('iban_account_number'),
            'legal_requirement' => $request->input('legal_requirement'),
            'name_of_the_bank' => $request->input('name_of_the_bank'),
            'filing_number' => $request->input('filing_number'),
            'payment_information' => $request->input('payment_information'),
            'ss_days' => $request->input('ss_days'),
            'ss_after_days' => $request->input('ss_after_days'),
            'new_daily_salary' => str_replace(['₡','$','','.',','], ['','','','','.'], $request->input('new_daily_salary') ),
            'new_it_value' => str_replace(['₡','$','','.',','], ['','','','','.'], $request->input('new_it_value')),
            'it_difference' => str_replace(['₡','$','','.',','], ['','','','','.'], $request->input('it_difference')),
            'management_result' => $request->input('management_result'),
            'reason_rejected'   => $request->input('reason_rejected'),
            'requirement_submitted'   => $request->input('requirement_submitted'),

        ];
    }

    private function updateIncapacity($peItSortId, $data,$request, $activityPeitSort)
    {

        // Verifica que 'n_inability' no sea un array y conviértelo si es necesario
        if (isset($data['n_inability']) && is_array($data['n_inability'])) {
            $data['n_inability'] = implode(',', $data['n_inability']); // Convierte a string si es un array
        }

        $peitInabilitySort = PeitInabilitySort::where('pe_it_sort_id', $peItSortId)->first();

        //Enviamos un correo con el resultado de la gestion si tiene ajsute del valor de incapacidad temporal,
        //Si existe una incapacidad , si el resultado de la gestion es rechazado si no tiene un resultado de la gestion guardado en la BD
        if ($peitInabilitySort && $data['management_result'] === 'rechazado' && empty($peitInabilitySort->management_result)) {
            $this->emailRejectedAjustIncapacity($activityPeitSort, $request, $data['reason_rejected']);
        }

        //Si existe una incapacidad , si el resultado de la gestion es rechazado si no tiene un resultado de la gestion guardado en la BD
        if ($peitInabilitySort && $data['management_result'] === 'aprobado' && empty($peitInabilitySort->management_result)) {
            $this->emailApprovedAjustIncapacity($activityPeitSort, $request);
        }

        if ($peitInabilitySort) {
            // Actualiza el registro de incapacidad
            $peitInabilitySort->update($data);

            $validationResult = $request->input('validation_result', []);
            $causeRejection = $request->input('cause_rejection', []);
            $fractionsId = $request->input('id_fractions', []); // Array de ID de diagnósticos

            foreach ($validationResult as $index => $result) {
                $validation = $validationResult[$index] ?? null;
                $cause =$causeRejection[$index] ?? null;
                $fId = $fractionsId[$index] ?? null; // ID del diagnóstico
                // Verifica si ya existe un ID para el diagnóstico
                if ($fId) {
                    $existingFractions = PeitFractionSort::find($fId);
                    if ($existingFractions) {
                        // Actualiza el registro existente
                        $existingFractions->update([
                            'validation_result' => $validation,
                            'cause_rejection' => $cause,

                        ]);
                    }
                }
            }

        } else {
            // Si no existe, se puede manejar como un error o crear uno nuevo, dependiendo de la lógica de negocio
            PeitInabilitySort::insert($data);
        }
        
    }

    public function emailRejectedAjustIncapacity($activity, $request, $reason ){

        $parent_activity = Activity::where('id', $activity->parent_id)->first();

        $activity_gis = Activity::where('id', $parent_activity->parent_id)
            ->first();
   
        $policy_sort = PolicySort::where('activity_id', $activity_gis->parent_id)->first();

        $parent = Activity::where('id', $activity->parent_id)
            ->first();

        //Correos al tomador y afiliado
        $emails = [
            $activity->affiliate->email,
            $policy_sort->activity->affiliate->email
        ];

        $validEmails = array_filter($emails, function ($email) {
            return !empty($email) && filter_var($email, FILTER_VALIDATE_EMAIL);
        });

        $emailString = implode(',', $validEmails);

        $client_id = $activity->client_id;
        $nameAffiliate = mb_convert_case(mb_strtolower($activity->PeItSort->name_affiliated_pe ?? ''), MB_CASE_TITLE, "UTF-8");
        $case_number = $activity_gis->gis_sort->consecutive;
        $date_accident = $activity_gis->gis_sort->date_accident;

        $emailData = TemplateBuilder::build(
            Templates::TEMPORARY_DISABILITY_REJECTION_NOTIFICATION,
            [
                'name' => $nameAffiliate,
                'reason' => $reason,
                'case_number' => $case_number,
                'date_accident' => $date_accident
            ]
        );

        $mailSent = new SendDocumentDataBase(
            $emailString,
            $emailData['subject'],
            "<EMAIL>",
            "Notificación rechazo solicitud de ajuste Incapacidad Temporal",
            [
                "text" => $emailData['body'],
                "sender" => $emailData['sender']
            ],
            "<EMAIL>",
            [],
            "send_document_db",
            $client_id,
            $request->getHost(),
            $activity->id,
            ActionPeItSort::RECHAZAR_PAGO_AJUSTE_INCAPACIDAD_TEMPORAL,
            $activity->service->id
        );
         
        // Capturar el resultado del envío
        $result = $mailSent->sendMail();

        //Registramos los datos del correo enviado para la trazabilidad
        $mailBoardController = new MailBoardController();
        $mailBoardController->createRegisterMail(
            $activity->id,
            $activity->service->id, 
            $policy_sort->consecutive, 
            'Tomador', 
            $nameAffiliate, 
            $activity->PeItSort->number_ide_pe, 
            $emailData['subject'], 
            $emailData['body'],
            $emailString, 
            $result,
            null
        );
        
    }

    public function emailApprovedAjustIncapacity($activity, $request){

        $parent_activity = Activity::where('id', $activity->parent_id)->first();

        $activity_gis = Activity::where('id', $parent_activity->parent_id)
            ->first();
   
        $policy_sort = PolicySort::where('activity_id', $activity_gis->parent_id)->first();

        $parent = Activity::where('id', $activity->parent_id)
            ->first();

        //Correos al tomador y afiliado
        $emails = [
            $activity->affiliate->email,
            $policy_sort->activity->affiliate->email
        ];

        $validEmails = array_filter($emails, function ($email) {
            return !empty($email) && filter_var($email, FILTER_VALIDATE_EMAIL);
        });

        $emailString = implode(',', $validEmails);

        $client_id = $activity->client_id;
        $nameAffiliate = mb_convert_case(mb_strtolower($activity->PeItSort->name_affiliated_pe ?? ''), MB_CASE_TITLE, "UTF-8");
        $case_number = $activity_gis->gis_sort->consecutive;
        $date_accident = Carbon::parse($activity_gis->gis_sort->date_accident)->format('d/m/Y');
        $periodStart = Carbon::parse($activity->PeItSort->inabilitySort->start_date)->format('d/m/Y');
        $periodEnd = Carbon::parse($activity->PeItSort->inabilitySort->end_date)->format('d/m/Y');
        $mountValue = '';
        if ($request->has('it_difference')) {
            $mountValue = $request->it_difference;
        } else {
            switch ($policy_sort->type_currency) {//falta
                case 'USD':
                    $currencySymbol = '$';
                    break;
                case 'CRC':
                    $currencySymbol = '₡';
                    break;
                default:
                    $currencySymbol = '';
                    break;
            }
            $mountValue = $currencySymbol . number_format($activity->PeItSort->inabilitySort->it_difference ?? 0, 2, ',', '.');
        }

        $emailData = TemplateBuilder::build(
            Templates::TEMPORARY_DISABILITY_ADJUSTMENT_PAYMENT_NOTIFICATION,
            [
                'name' => $nameAffiliate,
                'number_case' => $case_number,
                'event_date' => $date_accident,
                'period_start' => $periodStart,
                'period_end' => $periodEnd,
                'mount' => $mountValue,
            ]
        );

        $mailSent = new SendDocumentDataBase(
            $emailString,
            $emailData['subject'],
            "<EMAIL>",
            $emailData['subject'],
            [
                "text" => $emailData['body'],
                "sender" => $emailData['sender']
            ],
            "<EMAIL>",
            [],
            "send_document_db",
            $client_id,
            $request->getHost(),
            $activity->id,
            ActionPeItSort::RECHAZAR_PAGO_AJUSTE_INCAPACIDAD_TEMPORAL,
            $activity->service->id
        );
        
        // Capturar el resultado del envío
        $result = $mailSent->sendMail();

        //Registramos los datos del correo enviado para la trazabilidad
        $mailBoardController = new MailBoardController();
        $mailBoardController->createRegisterMail(
            $activity->id,
            $activity->service->id, 
            $policy_sort->consecutive, 
            //asegurado
            'Asegurado', 
            //nombre asegurado
            $nameAffiliate, 
            //identificacion asegurado
            $activity->PeItSort->number_ide_pe, 
            $emailData['subject'], 
            $emailData['body'],
            $emailString, 
            $result,
            null
        );
    }

    private function insertIncapacity($data)
    {
        PeitInabilitySort::insert($data);
    }

    private function saveDiagnostics($request, $peItSortId)
    {

        // Verifica que el pe_it_sort_id existe
        $peItSort = PeItSort::find($peItSortId);

        // Obtiene los datos del diagnóstico desde la solicitud
        $diagnosticsData = $request->input('diagnostico_pe', []);
        $diagnosticsCodeCie = $request->input('code_cie_10.cod', []);
        $casedataClassif = $request->input('classification', []);
        $casedataLaterality = $request->input('laterality', []);
        $diagnosticsIdDx = $request->input('id_dx', []); // Array de ID de diagnósticos

        foreach ($diagnosticsData as $index => $diagnosis) {
            $casedataCodeCie = $diagnosticsCodeCie[$index] ?? null;
            $casedataClassifValue = $casedataClassif[$index] ?? null;
            $casedataLateralityValue = $casedataLaterality[$index] ?? null;
            $casedataIdDx = $diagnosticsIdDx[$index] ?? null; // ID del diagnóstico
            // Verifica si ya existe un ID para el diagnóstico
            if ($casedataIdDx) {
                $existingDiagnosis = PeItSortCaseDx::find($casedataIdDx);

                if ($existingDiagnosis) {
                    // Actualiza el registro existente
                    $existingDiagnosis->update([
                        'classification' => $casedataClassifValue,
                        'code_cie_10' => $casedataCodeCie,
                        'diagnostico_pe' => $diagnosis,
                        'laterality' => $casedataLateralityValue,
                    ]);
                }
            } else {

                PeItSortCaseDx::insert([
                    'pe_it_sort_id' => $peItSortId,
                    'classification' => $casedataClassifValue,
                    'code_cie_10' => $casedataCodeCie,
                    'diagnostico_pe' => $diagnosis,
                    'laterality' => $casedataLateralityValue,
                ]);
            }
        }
    }



    public function EmitirIncapacidadMedica(Request $req, $cpath)
    {

        $activity = $req->input('activity_id');
        $activityPrestacionesMedicas = Activity::where('id', $activity)->firstOrFail();

        // Buscamos el cliente
        $client = Client::where('path', $cpath)->firstOrFail();

        DB::beginTransaction();

        try {
            // 1. Creamos la activity del servicio PE-IT SORT
            $newActivity = new Activity;
            $activity = $req->input('activity_id');
            $newActivity->parent_id = $activity;
            $newActivity->client_id = $client->id;
            $newActivity->affiliate_id = $activityPrestacionesMedicas->affiliate_id;
            $newActivity->service_id = Service::SERVICE_PE_IT_SORT_MNK;
            $newActivity->state_id = State::REGISTRADO;
            $newActivity->user_id = Auth::id();
            $newActivity->save();


            // 2. Ejecutamos la acción REGISTRAR IT INTEGRADO MANUAL sobre la incapacidad
            $description = "Acción generada REGISTRAR IT INTEGRADO";
            ActionController::create(
                $newActivity->id,
                ActionPeItSort::REGISTRAR_IT_MANUAL,
                $description
            );


            // 4. Ejecutamos la acción EMITIR INCAPACIDAD MEDICA en el servicio PRESTACIONES MEDICAS
            $description = "Acción generada EMITIR INCAPACIDAD MEDICA";
            ActionController::create(
                $activityPrestacionesMedicas->id,
                Action::EMITIR_INCAPACIDAD_MEDICA,
                $description
            );

            DB::commit();

            return response()->json([
                'message' => 'Solicitud exitosa',
                'activity_id' => $newActivity->id // Aquí pasas la variable con el ID correspondiente
            ], 200);


        } catch (\Exception $e) {
            DB::rollback();

            dd($e);
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
            ], 500);
        }

    }
    public function edit($id)
    {
        // Busca el documento en la base de datos
        $activityActionDocument = ActivityActionDocument::where('activity_action_id', $id)->first();

        // Verifica si se encontró el documento
        if (!$activityActionDocument) {
            return redirect()->back()->with('error', 'No se encontró el documento.');
        }

        // Pasa la variable a la vista
        return view('services.pe_it_sort.steps.disability_number_xxxx', compact('activityActionDocument'));
    }
    public function updatePDF($id)
    {
        try {
            // Cargar la vista para generar el PDF
            $pdf = \PDF::loadView("services.pe_it_sort.steps.docs.inability_pdf.pdf");

            $document = 'incapacidad';

            // Cargamos el archivo en S3
            $path = "activity_action_document/{$document}_{$id}.pdf";
            \Storage::disk('s3')->put($path, $pdf->output());

            // Guardamos la información en la tabla activity_action_documents
            $activityActionDocument = ActivityActionDocument::updateOrCreate(
                ['activity_action_id' => $id], // Busca si ya existe
                [
                    'name' => $document,
                    'path' => $path
                ]
            );

            return redirect()->back()->with('success', 'PDF actualizado con éxito.');
        } catch (\Exception $e) {
            // Manejo de errores
            return redirect()->back()->with('error', 'Error al actualizar el PDF: ' . $e->getMessage());
        }
    }



    public function reportarValidacionDocumental(Request $request, $activityId)
    {
        // Obtener la actividad actual
        $activity = Activity::find($activityId);

        DB::beginTransaction();
        //todo::verificar de donde vienen los documentos
        $description = 'REPORTAR VALIDACION DE DOCUMENTO';
        try {
            $description = "Acción generada REPORTAR VALIDACIÓN DOCUMENTAL";
            // Crear una nueva instancia de ActivityAction y guardar los cambios de estado
            $newActivityAction = ActionController::create($activity->id,
                ActionPeItSort::REPORTAR_VALIDACIÓN_DOCUMENTAL, $description);

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
            ]);
        }
        return response()->json([
            'status' => 'success',
            'message' => 'Reportar validacion de documento ',
        ]);
    }

    public function inability(Activity $activity, $id)
    {
        if (!$activity->pe_it_sort) {
            $pe_it_sort = new PeitInabilitySort();
            $pe_it_sort->activity_id = $id;
        } else {
            $pe_it_sort = $activity->policy_sort_collection;
        }


        $pe_it_sort->save();

    }

    public function calculateAndSaveFraccionamientos(Request $request, $cpath, $id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('client_id', $client->id)
            ->where('id', $id)
            ->with('PeItSort.inabilitySort')
            ->firstOrFail();
        $policySort = null;
        $accident = null;
        $parent = null;
        $parent_activity = Activity::where('id', $activity->parent_id)
            ->first();

        if ($parent_activity->service_id == Service::SERVICE_MEDICAL_SERVICES_SORT_MNK) {
            $activityGis = Activity::where('client_id', $client->id)
                ->where('id', $parent_activity->parent_id)
                ->where('service_id', Service::SERVICE_GIS_SORT_MNK)
                ->firstOrFail();
            $gis = GisSort::where('activity_id', $activityGis->id)
                ->firstOrFail();
            $policySort = policySort::where('activity_id', $activityGis->parent_id)
                ->firstOrFail();
            $parent = Activity::where('id', $activityGis->parent_id)
                ->first();
            $accident = $gis->date_accident;

        } else {
            $activityGis = Activity::where('client_id', $client->id)
                ->where('id', $activity->parent_id)
                ->where('service_id', Service::SERVICE_GIS_SORT_MNK)
                ->firstOrFail();
            $gis = GisSort::where('activity_id', $activityGis->id)
                ->firstOrFail();
            $policySort = policySort::where('activity_id', $activityGis->parent_id)
                ->firstOrFail();
            $parent = Activity::where('id', $activityGis->parent_id)
                ->first();
            $accident = $gis->date_accident;
        }

        DB::beginTransaction(); // Iniciar la transacción

        try {

            // Formatea la fecha actual en el formato deseado
            $currentDate = Carbon::now();
            $today = ucfirst(strftime('%A %e de %B del %Y', strtotime($currentDate)));
            $documentName = " Carta_de_otorgamiento_de_incapacidad_temporal_" . $activity->id . ".pdf";
            $pdfContent = PDF::loadView('services.pe_it_sort.docs.letter_granting_temporary_disability', ['activity' => $activity, 'policySort' => $policySort, 'accident' => $accident, 'today' => $today, 'parent' => $parent])->output();

            // Subir el archivo PDF a S3
            $filePath = "pe_ip_sort/{$documentName}";
            Storage::disk('s3')->put($filePath, $pdfContent);
            // Formar archivo para adjuntar en el correo
            $files[] = [
                'type' => 'pdf',
                'path' => $filePath,
                'name' => $documentName,
            ];

            $gisSort = new GisSort();
            $casoGis = $gisSort->formatCaseNumberLocal($activity->PeItSort->number_case);

            $this->createFractions($activity->id,$activityGis);
            $description = "Acción generada Aprobación de pagos de fraccionamiento";

            $actionController = ActionController::create(
                $activity->id,
                ActionPeItSort::APROBACIÓN_PAGOS_DE_FRACCIONAMIENTO,
                $description
            );

            //Correos al tomador y afiliado
            $emails = [
                $activity->affiliate->email,
                $parent->policy_sort->activity->affiliate->email
            ];

            $validEmails = array_filter($emails, function ($email) {
                return !empty($email) && filter_var($email, FILTER_VALIDATE_EMAIL);
            });

            $emailString = implode(',', $validEmails);

            $client_id = $activity->client_id;
            $nameAffiliate = mb_convert_case(mb_strtolower($activity->PeItSort->name_affiliated_pe ?? ''), MB_CASE_TITLE, "UTF-8");
            $numberIdAffiliate = $activity->PeItSort->number_ide_pe;
            $nameTaker = mb_convert_case(mb_strtolower($parent->policy_sort->activity->affiliate->full_name ?? ''), MB_CASE_TITLE, "UTF-8");
            $peItInability= PeitInabilitySort::where('pe_it_sort_id', $activity->PeItSort->id)->first();
            $periodStart = Carbon::parse($peItInability->start_date)->format('d/m/Y');
            $periodEnd = Carbon::parse($peItInability->end_date)->format('d/m/Y');
            $emailData = TemplateBuilder::build(
                Templates::WAGE_SUBSIDY_RECOGNITION,
                [
                    'name' => $nameTaker,
                    'policy_sort' => $parent->policy_sort->formatSortNumber(),
                    'name_affiliate' => $nameAffiliate,
                    'doc_number' => $numberIdAffiliate,
                    'connector' => $activity->affiliate->gender === 'F' ? 'de la trabajadora' : 'del trabajador',
                    'case_number' => $gis->formatCaseNumber(),
                    'event_date' => Carbon::parse($gis->date_accident)->format('d/m/Y'),
                    'period_start' => $periodStart,
                    'period_end' => $periodEnd,
                ]
            );

            $mailSent = new SendDocumentDataBase(
                $emailString,
                $emailData['subject'],
                "<EMAIL>",
                $emailData['subject'],
                [
                    "text" => $emailData['body'],
                    "sender" => $emailData['sender']
                ],
                "<EMAIL>",
                $files,
                "send_document_db",
                $client_id,
                $request->getHost(),
                $activity->id,
                $actionController->id,
                $activity->service->id
            );
            
            // Capturar el resultado del envío
            $result = $mailSent->sendMail();

            //Registramos los datos del correo enviado para la trazabilidad
            $mailBoardController = new MailBoardController();
            $mailBoardController->createRegisterMail(
                $activity->id,
                $activity->service->id, 
                $parent->policy_sort->consecutive, 
                //asegurado
                'Asegurado',
                //Nombre asegurado 
                $nameAffiliate, 
                //Identificación asegurado
                $numberIdAffiliate, 
                $emailData['subject'], 
                $emailData['body'],
                $emailString, 
                $result,
                null
            );

            DB::commit(); // Confirmar la transacción

            return back()->with('success', 'Operación realizada con éxito');
        } catch (\Exception $e) {
            DB::rollBack(); // Revertir la transacción en caso de error
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
            ]);
        }
    }
    public function createFractions($activity,$activityGis)
    {

        //Salario minimo diario
        $SMD = 11953.65;
        try {
            $peItSort = PeItSort::where('activity_id', $activity)->first();

            if ($peItSort) {
                $pe_it_inabilitySort = PeitInabilitySort::where('pe_it_sort_id', $peItSort->id)->first();

                // Validar los datos de entrada
                $startDate = $pe_it_inabilitySort->start_date;
                $endDate = $pe_it_inabilitySort->end_date;

                $start = Carbon::createFromFormat('Y-m-d', $startDate);
                $end = Carbon::createFromFormat('Y-m-d', $endDate);

                $totalDays = $end->diffInDays($start) + 1;

                //Lllegase el caso que los subsidios sean menores al valor del “SMD(Salario mínimo diario”)
                $value_ss_days = max( floatval(str_replace(['$', '.', ','], ['', '', '.'],$pe_it_inabilitySort->ss_days ?? 0)), $SMD);
                $value_ss_after_days = max( floatval(str_replace(['$', '.', ','], ['', '', '.'],$pe_it_inabilitySort->ss_after_days ?? 0)), $SMD);

                // Limpiar los valores de SS y convertirlos a float
                $ssDays =  $value_ss_days;
                $ssAfterDays =  $value_ss_after_days;

                $fractions = [];
                $currentStart = $start->copy();

                //traer todas las incapacidades aprobadas para llevar el conteo de los dias
                //Obtener IDs de actividades cuyo parent_id es el ID de GIS
                $directActivities = Activity::where('parent_id', $activityGis->id)
                    ->where('service_id', Service::SERVICE_PE_IT_SORT_MNK)
                    ->whereIn('state_id', [StatePeItSort::EN_PAGO_DE_FRACCIONES,StatePeItSort::PENDIENTE_SOPORTE_DE_PAGO_FRACCION,StatePeItSort::PAGO_SIT_REALIZADO])
                    ->pluck('id')->toArray();
                // Buscar el ID de Prestaciones Médicas que tiene como parent_id el de GIS
                $prestacionesMedicasIds = Activity::where('parent_id', $activityGis->id)
                    ->where('service_id', Service::SERVICE_MEDICAL_SERVICES_SORT_MNK)
                    ->pluck('id')
                    ->toArray();

                // Obtener IDs de actividades cuyo parent_id es alguno de los IDs de Prestaciones Médicas
                $indirectActivities = Activity::whereIn('parent_id', $prestacionesMedicasIds)
                    ->where('service_id', Service::SERVICE_PE_IT_SORT_MNK)
                    ->whereIn('state_id', [StatePeItSort::EN_PAGO_DE_FRACCIONES,StatePeItSort::PENDIENTE_SOPORTE_DE_PAGO_FRACCION,StatePeItSort::PAGO_SIT_REALIZADO])
                    ->pluck('id')->toArray();
                // Almacenar id
                $allActivityIds = array_merge($directActivities, $indirectActivities);
                //Búsqueda de incapacidades temporales
                $peItSortIds = PeItSort::whereIn('activity_id', $allActivityIds)->pluck('id')->toArray();

                //Obtener resumen de incapacidad
                $accumulatedDays = 0; // Lleva el conteo de días procesados en las fracciones
                $disabilities = PeitInabilitySort::whereIn('pe_it_sort_id', $peItSortIds)->get();
                $accumulatedDays = $disabilities->sum('days_it');


                // Calcular cada fracción hasta la fecha final
                while ($currentStart->lte($end)) {
                    $currentEnd = $currentStart->copy()->next(Carbon::FRIDAY);

                    // Si el próximo viernes está más allá de la fecha de fin, usar la fecha de fin
                    if ($currentEnd->gt($end)) {
                        $currentEnd = $end->copy();
                    }

                    // Calcular días de la fracción
                    $daysInFraction = $currentStart->copy()->startOfDay()->diffInDays($currentEnd->copy()->startOfDay()) + 1;
                    // Verificar si al sumar superamos los 45 días
                    if (($accumulatedDays + $daysInFraction) > 45 && $accumulatedDays < 45) {
                        // Cuántos días faltaban para llegar a 45
                        $daysUntil45 = 45 - $accumulatedDays;

                        // Cuántos días son después de los 45
                        $daysAfter45 = $daysInFraction - $daysUntil45;

                        // Calcular el valor en dos partes
                        $valuePart1 = $ssDays * $daysUntil45;
                        $valuePart2 = $ssAfterDays * $daysAfter45;

                        // Sumar los dos valores
                        $valueForFraction = $valuePart1 + $valuePart2;
                    } elseif ($accumulatedDays > 45) {
                        // No supera los 45 días, cálculo normal
                        $valueForFraction = $ssAfterDays * $daysInFraction;
                    }else{
                        // No supera los 45 días, cálculo normal
                        $valueForFraction = $ssDays * $daysInFraction;
                    }
                    $accumulatedDays = $accumulatedDays + $daysInFraction;

                    $currencySymbol = '$';
                    $fractions[] = [
                        'peit_inability_sort_id' => $pe_it_inabilitySort->id,
                        'n_inability' => $pe_it_inabilitySort->id,
                        'start_date_it_fraction' => $currentStart->format('Y-m-d'),
                        'end_date_it_fraction' => $currentEnd->format('Y-m-d'),
                        'days_it_fraction' => $daysInFraction,
                        'value_pay_fraction' => $currencySymbol . number_format($valueForFraction, 2, ',', '.'),
                    ];

                    // Avanzar al siguiente día después del viernes actual
                    $currentStart = $currentEnd->addDay();
                }

                // Almacenar las fracciones en la base de datos
                foreach ($fractions as $fraction) {
                    PeitFractionSort::insert($fraction);
                }

                return response()->json([
                    'message' => 'Fracciones creadas exitosamente',
                    'fractions' => $fractions
                ]);
            }
        } catch (\Exception $e) {
            DB::rollBack(); // Revertir la transacción en caso de error
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
            ]);
        }
    }




    public static function reportfractionpaymentmade(Activity $activity, $request)
    {
        // Obtener la actividad actual
        $activities = Activity::where('service_id', Service::SERVICE_PE_IT_SORT_MNK)
            ->where('action_id', ActionPeItSort::REPORTAR_PAGO_FRACCIÓN_REALIZADO)
            ->where('state_id', StatePeItSort::EN_PAGO_DE_FRACCIONES)
            ->get();

        DB::beginTransaction();



        try {
            // Obtener la fracción que se está pagando y el monto del pago
            $fractionId = $request->input('value_pay_fraction'); // ID de la fracción pagada
            $paymentAmount = $request->input('amount_pay'); // Monto del pago recibido

            // Verificar si se ha proporcionado un ID de fracción
            if (!$fractionId || $paymentAmount <= 0) {
                throw new \Exception('ID de fracción inválido o monto de pago incorrecto.');
            }

            // Buscar la fracción específica y actualizar su estado
            $fraction = PeitFractionSort::findOrFail($fractionId);
            $fraction->update(['state_fraction' => 'Aprobado']);
            $description = "Acción generada Reportar pago fracción realizado";
            $newActivityAction = ActionController::create(

                $activity->id,
                ActionPeItSort::REPORTAR_PAGO_FRACCIÓN_REALIZADO,
                $description
            );


            DB::commit();
            return response()->json([
                'status' => 'success',
                'message' => 'Pago fracción reportado con éxito',
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
            ]);
        }
    }


    /**
     * REPORTAR PAGO TOTAL INCAPACIDAD
     * Servicio PE IT-Sort (incapacidad temporal)
     *
     * @param $activityVariation
     * @return \Illuminate\Http\JsonResponse
     */
    public function reportFullDisabilityPayment(Request $req, $cpath, $id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $description = 'REPORTAR PAGO TOTAL INCAPACIDAD';


        DB::beginTransaction();
        try {
            $activity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();


            $activityMedical = Activity::where('client_id', $client->id)
                ->where('id', $activity->parent_id)
                ->where('service_id', Service::SERVICE_MEDICAL_SERVICES_SORT_MNK)
                ->firstOrFail();

            $activityGis = Activity::where('client_id', $client->id)
                ->where('id', $activityMedical->parent_id)
                ->where('service_id', Service::SERVICE_GIS_SORT_MNK)
                ->firstOrFail();

            $policySort = policySort::where('activity_id', $activityGis->parent_id)
                ->firstOrFail();

            //actividad poliza
            $activityPolicy = Activity::where('id', $activityGis->parent_id)->first();

            // La Actividad debe iniciar en estado EN PAGO DE FRACCIONES
            $activity->state_id = StatePeItSort::EN_PAGO_DE_FRACCIONES;
            $activity->save();

            $peItSort = PeItSort::where('activity_id', $activity->id)->first();
            $peItSortInability = PeitInabilitySort::where('pe_it_sort_id', $peItSort->id)->first();

            if (!empty($peItSortInability->amount_pay)) {

                // Sumar las fracciones pagadas
                $totalPaidFractions = PeitFractionSort::where([
                    ['peit_inability_sort_id', $peItSortInability->id],
                    ['state_fraction', 'Aprobado']
                ])->sum('value_pay_fraction');

                // Comparar si el valor total de las fracciones es igual al monto de la incapacidad
                if ($totalPaidFractions == $peItSortInability->amount_pay) {
                    // registrar actividad
                    $actionController = ActionController::create(
                        $activity->id,
                        ActionPeItSort::REPORTAR_PAGO_TOTAL_INCAPACIDAD,
                        $description
                    );

                    $nameWorker = mb_convert_case(
                        mb_strtolower(optional(optional($activityPolicy)->affiliate)->full_name, 'UTF-8'),
                        MB_CASE_TITLE,
                        'UTF-8'
                    );

                    $nameAffiliate = mb_convert_case(mb_strtolower(optional($activityGis->affiliate)->full_name ?? '', 'UTF-8'), MB_CASE_TITLE, 'UTF-8');
                    // Creación de correo notificando el pago de la incapacidad
                    $email = $activity->affiliate->email;
                    $subject = "Pago de incapacidad temporal";
                    $client_id = $client->id;

                    $text =
                    "¡Buen día, " . $nameWorker . "!

                    Nos complace mantenerte informado sobre las gestiones relacionadas con tu póliza del **Seguro Obligatorio de Riesgos del Trabajo N° {$policySort->formatSortNumber()}.

                    En el caso N° {$peItSort->id}, correspondiente al trabajador(a) {$nameAffiliate}, con número de identificación {$peItSort->number_ide_pe} y fecha de evento **{$peItSort->start_date_it_fraction}**, hemos realizado el pago total de la incapacidad temporal correspondiente al período desde **{$policySort->validity_from}** hasta **{$policySort->validity_to}**.
                    
                    Las sumas respectivas han sido depositadas en las fechas de pago establecidas, en la cuenta IBAN autorizada por el trabajador para este propósito.
                    
                                        Si tienes alguna consulta o necesitas más detalles sobre este caso, no dudes en contactarnos al 4102-7600. ¡Será un placer atenderte!
                    
                                        Nuestro compromiso es asegurar la protección y bienestar de tus colaboradores y brindarte la mejor experiencia de servicio.";


                    $mailSent = new SendDocumentDataBase(
                        $email,
                        $subject,
                        "<EMAIL>",
                        "Pago de incapacidad temporal",
                        [
                            "text" => $text,
                            "sender" => 'mnk aseguramiento'
                        ],
                        "<EMAIL>",
                        [], // Sin archivos adjuntos
                        "send_document_db",
                        $client_id,
                        $req->getHost(),
                        $activity->id,
                        $actionController->id,
                        $activity->service->id
                    );
                    
                    // Capturar el resultado del envío
                    $result = $mailSent->sendMail();

                    //Registramos los datos del correo enviado para la trazabilidad
                    $mailBoardController = new MailBoardController();
                    $mailBoardController->createRegisterMail(
                        $activity->id,
                        $activity->service->id, 
                        $activityPolicy->policy_sort->consecutive, 
                        'Tomador', 
                        $nameAffiliate, 
                        $peItSort->number_ide_pe, 
                        $subject, 
                        $text,
                        $email, 
                        $result,
                        null
                    );
                } else {
                    // Si las fracciones no suman el total de la incapacidad
                    return response()->json([
                        'status' => 'error',
                        'message' => 'Las fracciones no cubren el valor total de la incapacidad',
                    ]);
                }

            }
            return response()->json([
                'status' => 'success',
                'message' => 'REPORTAR PAGO TOTAL INCAPACIDAD',
            ]);


            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Función para eliminar un registro de Caso DX
     *
     * @param Request $request
     * @param $cpath
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function deleteCaseDx($cpath,$id)
    {
        try {
            $caseDx =  PeItSortCaseDx::where('id', $id)->first();
            $caseDx->delete();
            return response()->json([
                'success' => true,
                'message' => 'Caso Dx eliminado correctamente.'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error al eliminar el Case Dx: ' . $e->getMessage()
            ], 500);
        }
    }


    public function approveService(Request $req, $cpath, $id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('client_id', $client->id)
            ->where('id', $id)
            ->firstOrFail();

        DB::beginTransaction();
        try {
            ActionController::create($activity->id, ActionPeItSort::APROBAR_IT ,'Aprobación del servicio');
            DB::commit();
        } catch (\Exception $e) {
            DB::rollback();
            return back()->with('error', 'No se pudo realizar la operacion');
        }
        return back()->with('success', 'Operación realizada con éxito');
    }

    /**
     * GENERAR REPORTE PAGO FRACCIÓN REALIZADO
     * Servicio PE IT-Sort (incapacidad temporal)
     * La función se debe correr todos los días y cuando el día actual coincida con la fecha de una fracción realiza el proceso
     *
     * @param $activityVariation
     * @return \Illuminate\Http\JsonResponse
     */
    public function generateFractionPaymentReport()
    {
        DB::beginTransaction();
        try {
            $today = Carbon::today()->toDateString();
            $description = 'GENERAR REPORTE PAGO FRACCIÓN REALIZADO';

            $successfulFractions = [];
            $errors = [];
            $countSuccess = 0;

            // Obtener todas las actividades en estado EN PAGO DE FRACCIONES
            $activities = Activity::where('state_id', StatePeItSort::EN_PAGO_DE_FRACCIONES)->get();

            if ($activities->isEmpty()) {
                return response()->json(['error' => 'No hay actividades en estado EN PAGO DE FRACCIONES para procesar'], 404);
            }

            foreach ($activities as $activity) {
                try {

                    // Buscar la incapacidad relacionada con la fracción
                    $peItSort = PeItSort::where('activity_id', $activity->id)->first();

                    $pretacionMedica = $activity->parent;

                    if($pretacionMedica) {
                        $activity_gis = $pretacionMedica->parent->id;
                    } else {
                        throw new \Exception("No se encontró padre de PeItSort");
                    }

                    if (!$peItSort) {
                        throw new \Exception("No se encontró PeItSort para la actividad ID {$activity->id}");
                    }

                    $peItInabilitySort = PeitInabilitySort::where('pe_it_sort_id', $peItSort->id)->first();

                    if (!$peItInabilitySort) {
                        throw new \Exception("No se encontró PeItInabilitySort para PeItSort ID {$peItSort->id}");
                    }

                    // La fracción que tiene la fecha fin coincidente con hoy
                    $fractionToPay = PeitFractionSort::where('peit_inability_sort_id', $peItInabilitySort->id)
                        ->where('end_date_it_fraction', $today)
                        ->whereNull('state_fraction')
                        ->first();


                    if ($fractionToPay) {
                        // Creación del servicio Pagos afiliados
                        $activityPeAffiliatePayment = new Activity();
                        $activityPeAffiliatePayment->parent_id = $activity->id;
                        $activityPeAffiliatePayment->client_id = $activity->client_id;
                        $activityPeAffiliatePayment->affiliate_id = $activity->affiliate_id;
                        $activityPeAffiliatePayment->user_id = $activity->user_id;
                        $activityPeAffiliatePayment->service_id = Service::SERVICE_AFFILIATE_PAYMENT_MNK;
                        $activityPeAffiliatePayment->state_id = State::REGISTRADO;

                        $activityPeAffiliatePayment->save();

                       ActionController::create(
                            $activityPeAffiliatePayment->id,
                            ActionPeaffiliatepayments::REPORTAR_PE_SUBSIDIO_POR_INCAPACIDAD_TEMPORAL,
                            'REPORTAR PE SUBSIDIO POR INCAPACIDAD TEMPORAL'
                        );

                       //validar con leydi, la tabla no existe !!! por eso se comento
//                        $newAffiliatePayment = new AffiliatePayment();
//                        $newAffiliatePayment->value_payment = $fractionToPay->value_pay_fraction;
//                        $newAffiliatePayment->date_of_payment = $fractionToPay->end_date_it_fraction ;
//                        $newAffiliatePayment->n_inability = $fractionToPay->n_inability;
//                        $newAffiliatePayment->payment_status = "Pendiente de pago";
//                        $newAffiliatePayment->activity_id =  $activityPeAffiliatePayment->id;
//                        $newAffiliatePayment->save();

                        $fractionToPay->state_fraction = 'Pendiente de pago';
                        $fractionToPay->save();
                        $activityParentPolicy = '';

                        $activityParent = Activity::where('id', $activity->parent_id)->first();
                        if ($activityParent->service_id == Service::SERVICE_MEDICAL_SERVICES_SORT_MNK){
                            $activityParentGisSort = Activity::where('id', $activityParent->parent_id)->first();
                            $activityParentPolicy =  Activity::where('id', $activityParentGisSort->parent_id)->first();

                        }else{
                            $activityParentGisSort = Activity::where('id', $activityParent->parent_id)->first();
                            $activityParentPolicy =  Activity::where('id', $activityParentGisSort->parent_id)->first();
                        }

                        $policySort = $activityParentPolicy->policy_sort;
                        $policy = PolicySort::find($policySort->id);

                        $value = str_replace(['$', '.', ','], ['', '', '.'],  $fractionToPay->value_pay_fraction);

                        if ( config('app.env') != 'prod' ) {
                            //Instanciamos el controlado para los asientos contables
                            $accountingEntryController = new AccountingEntryController();
                            $accountingEntryController->reportAccountCase011($policy, $value, $activity_gis);
                        }

                        //TODO: Simular pago con acsel
                       /* $request = new Request([
                            'transaction_id' => 'TXN123457',
                            'amount' => $fractionToPay->value_pay_fraction,
                            'activity_id' => $activityPeAffiliatePayment->id,
                        ]);

                        $affiliatePaymentController = new AffiliatePaymentController();
                        $affiliatePaymentController->receivePaymentSimulator($request);*/


                        ActionController::create(
                            $activity->id,
                            ActionPeItSort::GENERAR_REPORTE_PAGO_FRACCION_REALIZADO,
                            $description
                        );

                        $successfulFractions[] = [
                            'fraction_id' => $fractionToPay->id,
                            'activity_id' => $activity->id
                        ];
                        $countSuccess++;
                        
                    }
                } catch (\Exception $activityError) {
                    // Almacenar errores para actividades específicas
                    $errors[] = [
                        'activity_id' => $activity->id,
                        'error' => $activityError->getMessage()
                    ];
                }
            }

            DB::commit();

            return response()->json([
                'total_fractions_generated' => $countSuccess,
                'successful_fractions' => $successfulFractions,
                'errors' => $errors
            ], 200);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'error' => 'No se pudo ejecutar la acción',
                'details' => $e->getMessage()
            ], 500);
        }
    }


    /**
     * Cargar archivo de incapacidad
     * Servicio PE IT-Sort (incapacidad temporal)
     *
     * @param $activityVariation
     * @return \Illuminate\Http\JsonResponse
     */
    public function uploadFile(Request $request,$cpath, $id)
    {
        $request->validate([
            'file' => 'required|file|max:2048', // Ajusta según tus necesidades
        ]);
        $filename = "incapacidad_{$id}_" . $request->file('file')->getClientOriginalName();

        $path = $request->file('file')->storeAs("documents", $filename, 's3');

        // Obtener la URL pública del archivo
        $s3Url = Storage::disk('s3')->url($path);


        return response()->json([
            'message' => 'Archivo cargado con éxito.',
            's3Url' => $s3Url,
            'path' => $path
        ]);
    }


    /**
     * Rechazar servicio
     * Servicio PE IT-Sort (incapacidad temporal)
     */
    public function rejectService(Request $req, $cpath, $id)
    {
        DB::beginTransaction();
        try {
            $client = Client::where('path', $cpath)->firstOrFail();
            $activity = Activity::where('client_id', $client->id)
                ->where('id', $id)
                ->with('PeItSort.inabilitySort')
                ->firstOrFail();
            $formartPolicy = '';
            $email_affiliate = '';
            $accident = null;
            $peItSort = PeItSort::where('activity_id', $activity->id)->first();
            if ($peItSort) {
                $peItSort->review_result_medical = 'rejected';
                $peItSort->reason_for_rejection = $req->motivo;
                $peItSort->save();
            }

            $activityPolicy = null;

            $parent_activity =Activity::where('id', $activity->parent_id)->first();
            $casoGis = '';

            if ($parent_activity) {

                if ($parent_activity->service_id == Service::SERVICE_MEDICAL_SERVICES_SORT_MNK || $parent_activity->service_id == Service::SERVICE_MEDICAL_SERVICES_SECONDARY_CARE_SORT_MNK) {
                    $activityGis = Activity::where('client_id', $client->id)
                        ->where('id', $parent_activity->parent_id)
                        ->where('service_id', Service::SERVICE_GIS_SORT_MNK)
                        ->firstOrFail();
                }else{
                    $activityGis = Activity::where('id', $parent_activity->id)->firstOrFail();
                }

                $gis = GisSort::where('activity_id', $activityGis->id)->firstOrFail();
                $email_affiliate= $gis->email_affiliate;
                $accident =$gis->date_accident;
                $policySort = policySort::where('activity_id', $activityGis->parent_id)->firstOrFail();
                $formartPolicy = $policySort->formatSortNumber();
                $activityPolicy = Activity::where('id', $activityGis->parent_id)->first();
                $casoGis = $gis->formatCaseNumber();
            }

            $reasons = [
                'time_limit_article_237_labor_code' => 'haber transcurrido el plazo de los 2 años que establece el artículo 237 del código de Trabajo',
                'case_prescription' => 'prescripción del caso',
                'treatment_abandonment' => 'abandono de tratamiento',
                'non_covered_case' => 'determinarse que se trata de un caso no amparado',
                'other' => 'otros',
            ];

            $reason = $peItSort->reason_for_rejection;

            if (array_key_exists($reason, $reasons)) {
                $reason_for_rejection = $reasons[$reason];
            } else {
                $reason_for_rejection = 'Motivo desconocido';
            }
            $emails = [$email_affiliate, $activityPolicy->affiliate->email];
            $emailString = implode(',', $emails);

            $nameWorker = mb_convert_case(
                mb_strtolower(optional(optional($activityPolicy)->affiliate)->full_name, 'UTF-8'),
                MB_CASE_TITLE,
                'UTF-8'
            );
            $nameAffiliate = mb_convert_case(mb_strtolower($activity->affiliate->full_name ?? ''), MB_CASE_TITLE, "UTF-8");

            $actionController =ActionController::create($activity->id, ActionPeItSort::RECHAZAR_IT ,'Rechazar del servicio');

            // Creación de correo notificando el rechazo de pago de la incapacidad
            $subject = "Rechazo de pago de incapacidad temporal.";
            $client_id = $client->id;
            $text = "¡Buen día, " . $nameWorker . "!

            Como parte de nuestro compromiso de mantenerlo informado de las gestiones atendidas al amparo de su póliza del Seguro Obligatorio de Riesgos del Trabajo {$formartPolicy}, hacemos de su conocimiento que en el caso #". $casoGis." a nombre del(a) trabajador(a) {$nameAffiliate}, con número de identificación {$activity->PeItSort->number_ide_pe}, con fecha de evento " . ucfirst(strftime('%A %e de %B del %Y', strtotime($accident))) . ", la Red de Salud de MNK Seguros ha extendido un período de incapacidad temporal que va del  " . ucfirst(strftime('%A %e de %B del %Y', strtotime($activity->PeItSort->inabilitySort->start_date))) . " hasta el " . ucfirst(strftime('%A %e de %B del %Y', strtotime($activity->PeItSort->inabilitySort->end_date))) . ", que será pagado a la persona trabajadora por semana vencida, los días viernes.
            
            No obstante, dicha incapacidad no será reconocida  por {$reason_for_rejection}.
            
            Si tiene alguna consulta o necesita más detalles sobre este caso, por favor, contáctenos al 4102-7600. ¡Será un gusto servirle! ";


            $mailSent = new SendDocumentDataBase(
                $emailString,
                $subject,
                "<EMAIL>",
                "Rechazo de pago de incapacidad temporal",
                [
                    "text" => $text,
                    "sender" => 'mnk indemnizaciones'
                ],
                "<EMAIL>",
                [], // Sin archivos adjuntos
                "send_document_db",
                $client_id,
                $req->getHost(),
                $activity->id,
                $actionController->id,
                $activity->service->id
            );
            $mailSent->sendMail();

            DB::commit();

            return back()->with('success', 'Operación realizada con éxito');

        }catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'error' => 'No se pudo ejecutar la acción',
                'details' => $e->getMessage()
            ], 500);
        }

    }

    /**
     * Suspensión de pago de it
     * Servicio PE IT-Sort (incapacidad temporal)
     *
     * @param $activityVariation
     * @return \Illuminate\Http\JsonResponse
     */
    public function itPaymentSuspension(Request $request)
    {
        $today = Carbon::today()->toDateString();
        $description = 'Suspensión de pago de IT';
        $activities = Activity::where('state_id', State::REGISTRADO)
            ->where('service_id', Service::SERVICE_PE_IT_SORT_MNK)
            ->get();

        if ($activities->isEmpty()) {
            return response()->json(['error' => 'No hay actividades en estado en Registrado para procesar'], 404);
        }

        foreach ($activities as $activity) {
            try {

                // Buscar la incapacidad relacionada con la fracción
                $accident = null;
                $peItSort = PeItSort::where('activity_id', $activity->id)
                    ->where('day_540', $today)
                    ->first();
                if ($peItSort) {
                    $parent_activity =Activity::where('id', $activity->parent_id)
                        ->first();
                    if ($parent_activity->service_id == Service::SERVICE_MEDICAL_SERVICES_SORT_MNK) {
                        $activityGis = Activity::where('id', $parent_activity->parent_id)
                            ->where('service_id', Service::SERVICE_GIS_SORT_MNK)
                            ->firstOrFail();
                        $gis = GisSort::where('activity_id', $activityGis->id)
                            ->firstOrFail();
                        $accident =$gis->date_accident;

                        //actividad poliza
                        $activityPolicy = Activity::where('id', $activityGis->parent_id)->first();
                    }
                    DB::beginTransaction();

                        $nameWorker = mb_convert_case(
                            mb_strtolower(optional(optional($activityPolicy)->affiliate)->full_name, 'UTF-8'),
                            MB_CASE_TITLE,
                            'UTF-8'
                        );
                        $nameAffiliate = mb_convert_case(mb_strtolower(optional($activity->affiliate)->full_name ?? '', 'UTF-8'), MB_CASE_TITLE, 'UTF-8');
                        $actionController = ActionController::create($activity->id, ActionPeItSort:: SUSPENSION_DE_PAGO_DE_IT,$description);
                        DB::commit();
                        // Creación de correo notificando el pago de la incapacidad
                        $email = $activity->affiliate->email;
                        $client_id = $activity->client_id;

                        $emailData = TemplateBuilder::build(
                            Templates::TEMPORARY_DISABILITY_PAYMENT_SUSPENSION_NOTICE,
                            [
                                'name' => $nameWorker,
                                'case_number' => sprintf('%04d', $peItSort->number_case),
                                'name_affiliate' => $nameAffiliate,
                                'doc_number' => $activity->PeItSort->number_ide_pe,
                                'connector' => $activity->affiliate->gender === 'F' ? 'de la trabajadora' : 'del trabajador',
                                'event_date' => Carbon::parse($gis->date_accident)->format('d/m/Y'),
                                'period_pay' => Carbon::parse($peItSort->day_540)->addDay()->format('d/m/Y'),
                            ]
                        );

                        $mailSent = new SendDocumentDataBase(
                            $email,
                            $emailData['subject'],
                            "<EMAIL>",
                            "Pago de incapacidad temporal",
                            [
                                "text" => $emailData['body'],
                                "sender" => $emailData['sender']
                            ],
                            "<EMAIL>",
                            [], // Sin archivos adjuntos
                            "send_document_db",
                            $client_id,
                            $request->getHost(),
                            $activity->id,
                            $actionController->id,
                            $activity->service->id
                        );
                        
                        
                        // Capturar el resultado del envío
                        $result = $mailSent->sendMail();

                        //Registramos los datos del correo enviado para la trazabilidad
                        $mailBoardController = new MailBoardController();
                        $mailBoardController->createRegisterMail(
                            $activity->id,
                            $activity->service->id, 
                            $activityPolicy->policy_sort->consecutive, 
                            'Tomador', 
                            $nameAffiliate, 
                            $peItSort->number_ide_pe, 
                            $emailData['subject'], 
                            $emailData['body'],
                            $email, 
                            $result,
                            null
                        );
                    return response()->json([
                        'success' => true,
                        'message' => 'acción ejecutada.'
                    ]);

                }
            } catch (\Exception $activityError) {
                // Almacenar errores para actividades específicas
                $errors[] = [
                    'activity_id' => $activity->id,
                    'error' => $activityError->getMessage()
                ];
            }
        }

    }

    //Aprobar ajuste incapacidad temporal
    public function approvedAjustIncapacity(Request $request, $cpath, $id){
        try {
            DB::beginTransaction();
            $description = "Acción generada Aprobar pago ajuste incapacidad temporal";
            ActionController::create(
                $id,
                ActionPeItSort::APROBAR_PAGO_AJUSTE_INCAPACIDAD_TEMPORAL,
                $description
            );
            $client = Client::query()
                ->where('path', $cpath)
                ->firstOrFail();
            $activityPeitSort = Activity::query()
                ->where('client_id', $client->id)
                ->where('id', $id)
                ->firstOrFail();

            $this->emailApprovedAjustIncapacity($activityPeitSort, $request);

            DB::commit();
            return back()->with('success', 'Se ejecuto la acción correctamente');
        }catch (\Exception $e){
            DB::rollBack();
            return back()->with('error', 'Error en ejecutar la acción');
        }
    }


    //Rechazar ajuste incapacidad temporal
    public function rejectedAjustIncapacity(Request $request, $cpath, $id){

        $client = Client::where('path', $cpath)->firstOrFail();
        // Busca la actividad asociada al servicio médico
        $activity = Activity::where('client_id', $client->id)
            ->where('id', $id)
            ->firstOrFail();

        $peitInabilitySort = PeitInabilitySort::where('pe_it_sort_id', $activity->PeItSort->id)->first();

        $description = "Acción generada rechazar pago ajuste incapacidad temporal";
        ActionController::create(
            $id,
            ActionPeItSort::RECHAZAR_PAGO_AJUSTE_INCAPACIDAD_TEMPORAL,
            $description
        );

        $this->emailRejectedAjustIncapacity($activity, $request, $peitInabilitySort->reason_rejected);

        return back()->with('success', 'Se ejecuto la acción correctamente');

    }

    //Actualizar el campo de ajuste incapacidad temporal en fracciones, se pasa el activity_id del siniestro
    public function AdjustmentInFractionsPeit($cpath, $id)
    {
        try {
            //pasamos el id de la actividad del siniestro
            $activityGis = Activity::where('id', $id)
                ->where('service_id', Service::SERVICE_GIS_SORT_MNK)
                ->firstOrFail();

            //consultamos todas las actividades de prestaciones medicas asociadas
            $activityPm = Activity::where('parent_id', $activityGis->id)
                ->where('service_id', Service::SERVICE_MEDICAL_SERVICES_SORT_MNK)
                ->pluck('id')
                ->toArray();
            //consultamos todas las incapacidades
            $activityPeit = Activity::whereIn('parent_id', $activityPm)
                ->where('service_id', Service::SERVICE_PE_IT_SORT_MNK)
                ->whereIn('state_id', [StatePeItSort::EN_PAGO_DE_FRACCIONES,StatePeItSort::PENDIENTE_SOPORTE_DE_PAGO_FRACCION,StatePeItSort::PAGO_SIT_REALIZADO])
                ->get();

            DB::beginTransaction();
            $accumulatedDays = 0;
            $SMD = 11953.65;
            $updatedFractions = [];
            foreach ($activityPeit as $activity) {
                $peItSort = PeItSort::where('activity_id', $activity->id)->first();
                $peItInabilitySort = PeitInabilitySort::where('pe_it_sort_id', $peItSort->id)->first();
                $peItFraction = PeitFractionSort::where('peit_inability_sort_id', $peItInabilitySort->id)->get();

                if ($peItFraction->isEmpty()) {
                    $accumulatedDays = $accumulatedDays + $peItInabilitySort->days_it;
                    continue; // Si no hay fracciones, continuar con la siguiente actividad
                }
                //llegase el caso que los subsidios sean menores al valor del “SMD(Salario mínimo diario”)
                $ss_days = floatval(str_replace(['₡', '.', ','], ['', '', '.'], $peItInabilitySort->ss_days ?? 0));
                $ss_after_days = floatval(str_replace(['₡', '.', ','], ['', '', '.'], $peItInabilitySort->ss_after_days ?? 0));

                $ss_days = max($ss_days,$SMD);
                $ss_after_days = max($ss_after_days,$SMD);
                foreach ($peItFraction as $fraction){
                    $daysInFraction = $fraction->days_it_fraction;

                    if (($accumulatedDays + $daysInFraction) > 45 && $accumulatedDays < 45) {
                        // Cuántos días faltaban para llegar a 45
                        $daysUntil45 = 45 - $accumulatedDays;

                        // Cuántos días son después de los 45
                        $daysAfter45 = $daysInFraction - $daysUntil45;

                        // Calcular el valor en dos partes
                        $valuePart1 = $ss_days * $daysUntil45;
                        $valuePart2 = $ss_after_days * $daysAfter45;

                        // Sumar los dos valores
                        $valueForFraction = $valuePart1 + $valuePart2;
                    } elseif ($accumulatedDays > 45) {
                        // No supera los 45 días, cálculo normal
                        $valueForFraction = $ss_after_days * $daysInFraction;
                    }else{
                        // No supera los 45 días, cálculo normal
                        $valueForFraction = $ss_days * $daysInFraction;
                    }
                    $accumulatedDays = $accumulatedDays + $daysInFraction;
                    $fraction->value_pay_fraction = $valueForFraction;
                    $fraction->save();

                    $updatedFractions[] = $fraction->id;

                }
            }
            DB::commit();
            return response()->json([
                'message' => 'Fracciones actualizadas con éxito.',
                'updated_fractions' => $updatedFractions,
            ]);
        }catch (\Exception $e){
            DB::rollBack();
            dd($e);
        }

    }


}

