<?php

namespace App\Http\Controllers\Services;

use App\Actions\ActionGisSort;
use App\Actions\ActionPeIpSort;
use App\Activity;
use App\ActivityActionDocument;
use App\Affiliate;
use App\Client;
use App\GisSort;
use App\Http\Controllers\ActionController;
use App\Http\Controllers\Controller;
use App\Http\Controllers\Tables\MailBoardController;
use App\PeIpSortCaseDx;
use App\PolicySort;
use Illuminate\Support\Facades\Log;
use App\Mail\SendDocumentDataBase;
use App\PeIpSort;
use App\PeIpSortBeneficiaries;
use App\PeItSort;
use App\PolicySpreadsheet;
use App\PolicySpreadsheetAffiliate;
use App\Service;
use App\State;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use PDF;

class PeIpSortController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth')->except([
            'approvalPayment'
        ]);
    }

    public function form(Request $request, $cpath, $id)
    {
        $client   = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();
        $peIpSort = PeIpSort::where('activity_id',$activity->id)->first();
        $beneficiaries = collect(); // Inicializa como colección vacía
        $caseDx = collect();

        if ($peIpSort && $peIpSort->casedata_type_disability == 'Gran invalidez') {

            $beneficiaries = PeIpSortBeneficiaries::where('peip_sort_id', $peIpSort->id)->get();
        }
        if ($peIpSort){
            $caseDx = PeIpSortCaseDx::where('peip_sort_id', $peIpSort->id)->get();
        }

        //Para obtener datos de planilla

        $lastThreeMonths = PolicySpreadsheetAffiliate::query()
            ->where('affiliate_id', $activity->affiliate_id)
            ->orderBy('created_at', 'desc')
            ->limit(3)
            ->select('policy_spreadsheet_id', 'monthly_salary')
            ->get();

        $monthlySalaries = $lastThreeMonths->pluck('monthly_salary');
        // Calcular el promedio del salario mensual
        $averageSalary = $monthlySalaries->avg();

        // Calcular el salario anual
        $annualSalary = $averageSalary * 12;

        $currencySymbol = '₡';

        $firstItem = $lastThreeMonths->first();
        if ($firstItem){
            $policy_spreadsheet = PolicySpreadsheet::where('id', $firstItem->policy_spreadsheet_id)
                ->select('activity_id')
                ->first();
            $activity_policy_spreadsheet_id = Activity::where('id',$policy_spreadsheet->activity_id)
                ->select('parent_id')
                ->first();
            $policy_sort = PolicySort::where('activity_id', $activity_policy_spreadsheet_id->parent_id)
                ->select('type_currency')
                ->first();

            switch ($policy_sort->type_currency) {
                case 'USD':
                    $currencySymbol = '$'; // Dólares
                    break;
                case 'CRC':
                    $currencySymbol = '₡'; // Colones costarricenses
                    break;
                default:
                    $currencySymbol = '₡'; // Si no se define, dejar vacío
                    break;
            }
        }
        // Formatear los salarios con miles y dos decimales
        //$averageSalaryFormatted = $currencySymbol . number_format($averageSalary, 2, ',', '.');
        //$annualSalaryFormatted = $currencySymbol . number_format($annualSalary, 2, ',', '.');

        $affiliateId = $activity->affiliate_id;
        $gisSorts = GisSort::whereHas('activity', function ($query) use ($affiliateId) {
            $query->where('affiliate_id', $affiliateId);
        })->get();


        return view('services.pe_ip_sort.form', [
            'activity' => $activity,
            'affiliate' => $activity->affiliate,
            'peIpSort' => $peIpSort,
            'beneficiaries' => $beneficiaries,
            'id' => $id,
            'averageSalary' => $averageSalary,
            'annualSalary' => $annualSalary,
            'currencySymbol' => $currencySymbol,
            'caseDx' => $caseDx,
            'gisSorts' => $gisSorts

        ]);

    }

    /**
     * Proceso para guardar los datos de PE IP Sort
     *
     * @param Request $request
     * @param $cpath
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function save(Request $request, $cpath, $id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();

        // Buscamos la actividad asociada al servicio médico
        $activity = Activity::where('client_id', $client->id)
            ->where('id', $id)
            ->firstOrFail();

        //Buscamos la activity del GIS SORT
        $activityGis = Activity::where('id', $activity->parent_id)->first();

        // Iniciamos la transacción
        DB::beginTransaction();

        try {
            // Validaciones de entrada
            $this->validateRequest($request);

            // Procesar los datos de entrada
            $dataToInsert = $this->prepareData($request, $activity->id);

            // Usar updateOrCreate para manejar inserciones y actualizaciones
            $peIpSort = PeIpSort::updateOrCreate(
                ['activity_id' => $activity->id],
                $dataToInsert
            );

            // Guardar beneficiarios solo si hay datos
            if ($request->input('casedata_type_disability') == 'Gran invalidez') {
                $this->saveBeneficiaries($request, $peIpSort->id);
            }

            // Guardar datos de diagnósticos
            $this->saveDiagnostics($request, $peIpSort->id);

            //ejecutamos los calculos de los PPS nuevamente
            // Crear un objeto Request simulado
            $request = new Request([
                'gis' => $activityGis->id,
                'severity' => $activityGis->gis_sort->severity, 
            ]);

             // Instanciar el controlador comprensive accident
            $gisController  = new ComprehensiveAccidentManagementController();

            DB::commit();

            //Realizamos los calculos de las provisiones
            $res = $gisController->CalculatePps($request); 

            $data = $res->getData();

            //Actualizamos los campos en el gis_sort con los pps calculado
            $activityGis->gis_sort->update([
                'pps_medical_benefits'                      => str_replace(['₡','$'], ['',''],$data->pps_medical_benefits),
                'pps_temporary_disability'                  => str_replace(['₡','$'], ['',''],$data->pps_it),
                'pps_minor_permanent_disability'            => str_replace(['₡','$'], ['',''],$data->ppsIpValues),
                'pps_partial_permanent_disability'          => str_replace(['₡','$'], ['',''],$data->ppsIpIppValues),
                'pps_total_permanent_disability'            => str_replace(['₡','$'], ['',''],$data->ppsIpITpValues),
                'pps_great_disability_permanent_disability' => str_replace(['₡','$'], ['',''],$data->ppsGreatDisabilityValues),
            ]);
            
            return response()->json(['success' => true, 'message' => 'Datos guardados correctamente.']);

        } catch (\Exception $e) {
            DB::rollback();
            //return response()->json(['success' => false, 'message' => 'ERROR::::'.$e->getMessage()]);
            throw new \Exception( $e->getMessage());
        }
    }

    /**
     * Función para validar datos antes de guardar
     *
     * @param Request $request
     * @param $cpath
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     */
    private function validateRequest(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'casedata_num' => 'required',
            'casedata_date_classif' => 'required',
            'casedata_date_accident' => 'required',
            'casedata_percent' => 'required',
            'casedata_date_medical_dis' => 'required',
            'casedata_time_accident' => 'required',
            'entity_classification' => 'required',
            'casedata_type_disability' => 'required',
            'calc_pay_date' => 'required',
            'calc_pay_date_submit' => 'required',
            'calc_ave_salary' => 'required',
            'calc_month_pay' => 'required',
            'calc_annual_income' => 'required',
            'calc_monthly_value' => 'required',
            'calc_total_years' => 'required',
            'payinfo_total_years_pay' => 'required',
            'payinfo_monthy_amount_pay' => 'required',
            'payinfo_doc_number' => 'required',
            'payinfo_name' => 'required',
            'payinfo_account_number' => 'required',
            'payinfo_bank_name' => 'required',
            'criteria_observ' => 'required',
            'criteria_payment_office' => 'required',
            'criteria_data_resolution' => 'required',
            'casedata_classif.*' => 'required',
            'diagnostics.cod.*' => 'required',
            'casedata_diagnosis.*' => 'required',
            'casedata_laterality.*' => 'required',
        ], [
            'casedata_num.required' => 'El campo # caso es obligatorio.',
            'casedata_date_classif.required' => 'El campo de fecha de calificación MNK es obligatorio.',
            'casedata_date_accident.required' => 'El campo de fecha de accidente es obligatorio.',
            'casedata_percent.required' => 'El campo de porcentaje de pérdida de capacidad general es obligatorio.',
            'casedata_date_medical_dis.required' => 'El campo de fecha de mejoría médica máxima (Alta médica) es obligatorio.',
            'casedata_time_accident.required' => 'El campo de hora de accidente es obligatorio.',
            'entity_classification.required' => 'El campo de entidad que emitió la calificación es obligatorio.',
            'casedata_type_disability.required' => 'El campo de tipo de discapacidad es obligatorio.',
            'calc_pay_date.required' => 'El campo de fecha de pago es obligatorio.',
            'calc_pay_date_submit.required' => 'El campo de fecha de pago de envío es obligatorio.',
            'calc_ave_salary.required' => 'El campo de promedio salario mensual reportado es obligatorio.',
            'calc_month_pay.required' => 'El campo de total meses a pagar es obligatorio.',
            'calc_annual_income.required' => 'El campo de renta anual es obligatorio.',
            'calc_monthly_value.required' => 'El campo de valor mensual es obligatorio.',
            'calc_total_years.required' => 'El campo de total años a pagar es obligatorio.',
            'payinfo_total_years_pay.required' => 'El campo de total de años a pagar es obligatorio.',
            'payinfo_monthy_amount_pay.required' => 'El campo de valor mensual a pagar es obligatorio.',
            'payinfo_doc_number.required' => 'El campo de número de identificación es obligatorio.',
            'payinfo_name.required' => 'El campo de nombre es obligatorio.',
            'payinfo_account_number.required' => 'El campo de Número de cuenta IBAN es obligatorio.',
            'payinfo_bank_name.required' => 'El campo de nombre de la entidad bancaria es obligatorio.',
            'criteria_observ.required' => 'El campo de observación es obligatorio.',
            'criteria_payment_office.required' => 'El campo de # oficio de pago es obligatorio.',
            'criteria_data_resolution.required' => 'El campo de fecha de resolución es obligatorio.',
            'casedata_classif.*.required' => 'La clasificación del caso es obligatoria.',
            'diagnostics.cod.*.required' => 'El código CIE es obligatorio.',
            'casedata_diagnosis.*.required' => 'El nombre del diagnóstico es obligatorio.',
            'casedata_laterality.*.required' => 'El Lateralidad es obligatorio.',

        ]);
        /*$validator->setAttributeNames([
            'diagnostics.cod.0' => 'Código CIE',
            'casedata_diagnosis.0' => 'Diagnóstico'
        ]);*/
        if ($validator->fails()) {

            throw new \Exception('Error:: '.$validator->errors());
//
//            return response()->json([
//                'status' => 'error',
//                'errors' => $validator->errors()  // Aquí devolverá los mensajes específicos
//            ], 422);
        }
    }

    private function prepareDataxxxx(Request $request, $activityId)
    {
        return array_merge($request->all(), [
            'activity_id' => $activityId,
            'affiliate_doc_type' => $request->input('affiliate_doc_type'),
            'payinfo_doc_type' => $request->input('payinfo_doc_type.0'), // Tomamos el primer elemento
            'number_document' => $request->input('number_document.0'),
            //'name_of_the_banking_entity' => $request->input('name_of_the_banking_entity.0'),
            'first_and_last_names' => $request->input('first_and_last_names.0'),
            'relationship' => $request->input('relationship.0'),
            'monthly_percentage' => $request->input('monthly_percentage.0'),
            'casedata_date_classif' => $request->input('casedata_date_classif_submit'),
            'casedata_date_medical_dis' => $request->input('casedata_date_medical_dis_submit'),
            'calc_pay_date' => $request->input('calc_pay_date_submit'),
            'criteria_data_resolution' => $request->input('criteria_data_resolution_submit'),
            'casedata_date_accident' => $request->input('casedata_date_accident_submit'),
            'calc_max_salary' => str_replace(['₡','$','.',','], ['','','','.'], $request->input('calc_max_salary')),
            'calc_monthly_addit' => str_replace(['₡','$','.',','], ['','','','.'], $request->input('calc_monthly_addit')),
            'calc_monthly_value' => str_replace(['₡','$','.',','], ['','','','.'], $request->input('calc_monthly_value')),
            'calc_annual_income' => str_replace(['₡','$','.',','], ['','','','.'], $request->input('calc_annual_income')),
            'payinfo_monthy_amount_pay' => str_replace(['₡','$','.',','], ['','','','.'], $request->input('payinfo_monthy_amount_pay')),
            'calc_annual_salary' => str_replace(['₡','$','.',','], ['','','','.'], $request->input('calc_annual_salary')),
            'calc_ave_salary' => str_replace(['₡','$','.',','], ['','','','.'], $request->input('calc_ave_salary')),
            'casedata_percent' => str_replace(['%'], [''], $request->input('casedata_percent'))
        ]);
    }

    private function prepareData(Request $request, $activityId)
    {
        try {
            // Validaciones necesarias antes de procesar los datos
            if (!$request->has('affiliate_doc_type')) {
                throw new \Exception('El campo "affiliate_doc_type" es obligatorio.');
            }
            if (!$request->has('payinfo_doc_type.0')) {
                throw new \Exception('El campo "payinfo_doc_type.0" es obligatorio.');
            }
            if (!$request->has('number_document.0')) {
                throw new \Exception('El campo "number_document.0" es obligatorio.');
            }
//            if (!$request->has('name_of_the_banking_entity.0')) {
//                throw new \Exception('El campo "name_of_the_banking_entity.0" es obligatorio.');
//            }
            if (!$request->has('first_and_last_names.0')) {
                throw new \Exception('El campo "first_and_last_names.0" es obligatorio.');
            }
            if (!$request->has('relationship.0')) {
                throw new \Exception('El campo "relationship.0" es obligatorio.');
            }
            if (!$request->has('monthly_percentage.0')) {
                throw new \Exception('El campo "monthly_percentage.0" es obligatorio.');
            }

            // Si todas las validaciones pasan, procesamos los datos
            return array_merge($request->all(), [
                'activity_id' => $activityId,
                'affiliate_doc_type' => $request->input('affiliate_doc_type'),
                'payinfo_doc_type' => $request->input('payinfo_doc_type.0'), // Tomamos el primer elemento
                'number_document' => $request->input('number_document.0'),
                //'name_of_the_banking_entity' => $request->input('name_of_the_banking_entity.0'),
                'first_and_last_names' => $request->input('first_and_last_names.0'),
                'relationship' => $request->input('relationship.0'),
                'monthly_percentage' => $request->input('monthly_percentage.0'),
                'casedata_date_classif' => $request->input('casedata_date_classif_submit'),
                'casedata_date_medical_dis' => $request->input('casedata_date_medical_dis_submit'),
                'calc_pay_date' => $request->input('calc_pay_date_submit'),
                'criteria_data_resolution' => $request->input('criteria_data_resolution_submit'),
                'casedata_date_accident' => $request->input('casedata_date_accident_submit'),
                'calc_max_salary' => str_replace(['₡', '$', '.', ','], ['', '', '', '.'], $request->input('calc_max_salary')),
                'calc_monthly_addit' => str_replace(['₡', '$', '.', ','], ['', '', '', '.'], $request->input('calc_monthly_addit')),
                'calc_monthly_value' => str_replace(['₡', '$', '.', ','], ['', '', '', '.'], $request->input('calc_monthly_value')),
                'calc_annual_income' => str_replace(['₡', '$', '.', ','], ['', '', '', '.'], $request->input('calc_annual_income')),
                'payinfo_monthy_amount_pay' => str_replace(['₡', '$', '.', ','], ['', '', '', '.'], $request->input('payinfo_monthy_amount_pay')),
                'calc_annual_salary' => str_replace(['₡', '$', '.', ','], ['', '', '', '.'], $request->input('calc_annual_salary')),
                'calc_ave_salary' => str_replace(['₡', '$', '.', ','], ['', '', '', '.'], $request->input('calc_ave_salary')),
                'casedata_percent' => str_replace(['%'], [''], $request->input('casedata_percent'))
            ]);
        } catch (\Exception $e) {
            // Lanzamos la excepción con un mensaje específico
            throw new \Exception('Error al preparar los datos: ' . $e->getMessage());
        }
    }


    /**
     * Función para guardar un registro de beneficiarios
     *
     * @param Request $request
     * @param $cpath
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     */
    private function saveBeneficiaries(Request $request, $peIpSortId)
    {
        $request->validate([
            'payinfo_doc_type.*' => 'required',
            'number_document.*' => 'required',
            'name_of_the_banking_entity.*' => 'required',
            'first_and_last_names.*' => 'required',
            'relationship.*' => 'required',
            'monthly_percentage.*' => 'required',
        ]);

        $beneficiariesData = [];

        DB::transaction(function () use ($request, $peIpSortId, &$beneficiariesData) {
            foreach ($request->input('payinfo_doc_type') as $index => $docType) {
                $beneficiaryId = $request->input('beneficiary_id')[$index] ?? null;

                if ($beneficiaryId) {
                    PeIpSortBeneficiaries::where('id', $beneficiaryId)->update([
                        'payinfo_doc_type' => $docType,
                        'doc_number' => $request->input('number_document')[$index],
                        'bank_name' => $request->input('name_of_the_banking_entity')[$index],
                        'full_name' => $request->input('first_and_last_names')[$index],
                        'relationship' => $request->input('relationship')[$index],
                        'percentage_monthly' => $request->input('monthly_percentage')[$index],
                    ]);
                } else {
                    $beneficiariesData[] = [
                        'peip_sort_id' => $peIpSortId,
                        'payinfo_doc_type' => $docType,
                        'doc_number' => $request->input('number_document')[$index],
                        'bank_name' => $request->input('name_of_the_banking_entity')[$index],
                        'full_name' => $request->input('first_and_last_names')[$index],
                        'relationship' => $request->input('relationship')[$index],
                        'percentage_monthly' => $request->input('monthly_percentage')[$index],
                    ];
                }
            }

            if (!empty($beneficiariesData)) {
                PeIpSortBeneficiaries::insert($beneficiariesData);
            }
        });

        return response()->json(['message' => 'Beneficiarios guardados exitosamente.']);
    }

    /**
     * Función para guardar un registro de Caso DX
     *
     * @param Request $request
     * @param $cpath
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     */
    private function saveDiagnostics(Request $request, $peIpSortId)
    {
        // Validar los datos entrantes
        $request->validate([
            'casedata_classif.*' => 'required',
            'diagnostics.cod.*' => 'required',
            'casedata_diagnosis.*' => 'required',
            'casedata_laterality.*' => 'required',
        ]);

        // Obtener todos los datos enviados para los diagnósticos
        $diagnosticsData = $request->input('casedata_diagnosis', []);
        $diagnosticsCodeCie = $request->input('diagnostics.cod', []);
        $casedataClassif = $request->input('casedata_classif', []);
        $casedataLaterality = $request->input('casedata_laterality', []);
        $diagnosticsIdDx = $request->input('id_dx', []); // Array de ID de diagnósticos

        // Iterar sobre los datos y guardar cada diagnóstico
        foreach ($diagnosticsData as $index => $diagnosis) {
            // Obtener los valores correspondientes a cada diagnóstico
            $casedataCodeCie = isset($diagnosticsCodeCie[$index]) ? $diagnosticsCodeCie[$index] : null;
            $casedataClassifValue = isset($casedataClassif[$index]) ? $casedataClassif[$index] : null;
            $casedataLateralityValue = isset($casedataLaterality[$index]) ? $casedataLaterality[$index] : null;
            $casedataIdDx = $diagnosticsIdDx[$index] ?? null; // ID del diagnóstico

            // Buscar si el diagnóstico ya existe usando el ID específico o un campo único
            if ($casedataIdDx) {
                $existingDiagnosis = PeIpSortCaseDx::find($casedataIdDx);

                if ($existingDiagnosis) {
                    // Actualizar el registro existente
                    $existingDiagnosis->update([
                        'casedata_classif' => $casedataClassifValue,
                        'casedata_code_cie' => $casedataCodeCie,
                        'casedata_diagnosis' => $diagnosis,
                        'casedata_laterality' => $casedataLateralityValue,
                    ]);
                }
            } else {
                // Insertar un nuevo registro
                PeIpSortCaseDx::create([
                    'peip_sort_id' => $peIpSortId,
                    'casedata_classif' => $casedataClassifValue,
                    'casedata_code_cie' => $casedataCodeCie,
                    'casedata_diagnosis' => $diagnosis,
                    'casedata_laterality' => $casedataLateralityValue,
                ]);
            }
        }

        return response()->json(['message' => 'Diagnósticos guardados exitosamente.']);
    }

    /**
     * Función para eliminar un registro de Caso DX
     *
     * @param Request $request
     * @param $cpath
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function deleteCaseDx($cpath,$id)
    {
        try {
            $caseDx =  PeIpSortCaseDx::where('id', $id)->first();
            $caseDx->delete();
            return response()->json([
                'success' => true,
                'message' => 'Caso Dx eliminado correctamente.'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error al eliminar el Case Dx: ' . $e->getMessage()
            ], 500);
        }
    }


    /**
     * Proceso para Acción REGISTRAR IP INTEGRADO
     *
     * @param Request $request
     * @param $cpath
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function registerIpIntegrado(Request $req, $cpath, $activity_id, $peipSortActivity)
    {

        $client = Client::where('path', $cpath)->firstOrFail();
        $description = 'REGISTRAR IP INTEGRADO';
        $activity = Activity::where('client_id', $client->id)->where('id', $activity_id)->firstOrFail();

        DB::beginTransaction();

        try {
           //todo::TRAER DATOS DEL SERVICIO GIS Y REGISTRAR EN EL SERVICIO DE IP SORT

           $GisSortactivity = Activity::where('client_id', $client->id)->where('id', $activity->id)->firstOrFail();

            $GisSort = $GisSortactivity->gis_sort;

            //Búsqueda de la actividad creada para el servicio
            $PeipSortActivity = Activity::where('id', $peipSortActivity->id)
                ->where('service_id', Service::SERVICE_PE_IP_SORT_MNK)
                ->first();

            // Se agregan los datos de gis al formulario de PE IP SORT
            $PeipSort = new PeipSort();
            if ($GisSort) {
                //$datosGis = Datos para traer de gis $activity->id;
                $PeipSort->activity_id = $PeipSortActivity->id;
                $PeipSort->affiliate_doc_type = $GisSort->type_identification_affiliate; // Tipo de identificación
                $PeipSort->affiliate_doc_number = $GisSort->number_identification_affiliate;// Número de identificación
                $PeipSort->affiliate_name = $GisSort->name_affiliate; //nombre
                $PeipSort->affiliate_email = $GisSort->email_affiliate; //email afiliado
                $PeipSort->casedata_dictamen = $GisSort->date_accident;  // N. dictamen o calificacion
                $PeipSort->casedata_num = $GisSort->id; // N° del caso
                $PeipSort->casedata_date_accident = $GisSort->date_accident; //fecha de accidente
                   /*$PeipSort->casedata_date_classi = $GisSort ->; // fecha calificación MNK
                   $PeipSort->casedata_laterality =$GisSort -> ; // lateralidad
                   $PeipSort->casedata_requires_assistance =$GisSort -> ; //Fecha de mejoría médica maxima (Alta medica)
                   $PeipSort->casedata_date_medical_dis= $GisSort ->; //Requiere de la asistencia de otra persona para realizar los actos esenciales de la vida
                    */
                $PeipSort->save();

            }

            // Creación de acción y estado
            $actionController = ActionController::create(
                $PeipSort->activity_id,
                ActionPeIpSort::REGISTRAR_IP_INTEGRADO,
                $description
            );
            $email = $activity->affiliate->email;
            $subject = "Recepción del dictamen de Pérdida de la Capacidad General";
            $client_id = $client->id;

            $text = "Señor(a)
                ".ucwords(mb_strtolower($PeipSort->affiliate_name))."
                Número de identificación: {$PeipSort->affiliate_doc_number}			
                Número de Caso: {$GisSort->formatCaseNumber()}			
                Correo electrónico: {$PeipSort->affiliate_email} 		
                
                Estimado(a) señor(a):
                Un gusto saludarle. Hemos recibido el “Dictamen de Pérdida de la Capacidad General”, correspondiente a la valoración médica realizada el ".ucfirst(strftime('%A %e de %B del %Y', strtotime($PeipSort->casedata_date_accident))).", en su caso del Seguro Obligatorio de Riesgos del Trabajo N° {$PeipSort->casedata_num}, donde se la fija un 10% de perdida de la capacidad general para el trabajo que, conforme la clasificación definida en el artículo 223 del Código de Trabajo, corresponde a una Incapacidad Permanente. (o por Gran Invalidez)
                
                Se procederá con el trámite para la determinación de la renta mensual correspondiente y en un plazo máximo de 5 días hábiles le estaremos informando sobre el monto de la renta mensual que se determine y las fecha en que se le depositará el pago mensual, por un plazo de (5 o 10) años o en forma vitalicia.
                
                Según lo dispuesto en el artículo 261 del Código de Trabajo, en caso de no estar de acuerdo con el porcentaje fijado, puede solicitar revisión de este ante la Junta Médica Calificadora de la Incapacidad para el Trabajo, ubicada en el primer piso de las oficinas centrales del Ministerio de Trabajo y Seguridad Social, en Barrio Tournón, San José, teléfono (506) 2542-5751.
                
                Si tiene alguna consulta o necesita más detalles sobre este caso, por favor, contáctenos al 4102-7600 ext. 8129-8130. ¡Será un gusto servirle!";


            $mailSent = new SendDocumentDataBase(
                $email,
                $subject,
                "<EMAIL>",
                "Pago de incapacidad temporal",
                [
                    "text" => $text,
                    "sender" => 'mnk indemnizaciones'
                ],
                "<EMAIL>",
                [], // Sin archivos adjuntos
                "send_document_db",
                $client_id,
                $req->getHost(),
                $activity->id,
                $actionController->id,
                $activity->service->id
            );
            
            // Capturar el resultado del envío
            $result = $mailSent->sendMail();

            //Registramos los datos del correo enviado para la trazabilidad
            $mailBoardController = new MailBoardController();
            $mailBoardController->createRegisterMail(
                $activity->id,
                $activity->service->id, 
                $GisSortactivity->parent->policy_sort->consecutive, 
                'Asegurado', 
                ucwords(mb_strtolower($PeipSort->affiliate_name)), 
                $PeipSort->affiliate_doc_number, 
                'Pago de incapacidad temporal', 
                $text,
                $email, 
                $result,
                null
            );

            //$result = $this->approvalPayment($req, $cpath, $PeipSort);

            $this->approvalPayment($req, $cpath, $PeipSort->id);

            DB::commit();
            return response()->json(["response" => true, "data" => $PeipSort]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
            ]);
        }
    }



    public function registerIpManually(Request $request, $cpath, $id)
    {

        $client = Client::where('path', $cpath)->firstOrFail();

        $description = 'REGISTRAR IP MANUAL';

        DB::beginTransaction();
        try {

            $activity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();

            //registrar actividad
            ActionController::create(
                $activity->id,
                ActionPeIpSort::REGISTRAR_IP_MANUAL,
                $description
            );

            /*
            TODO: este bloque de código se debe reemplazar para cuando se tenga la vista del afiliado,
             de esta forma recibir el documento que se carga allí */
            /* A LA ESPERA DE GIS PARA PROBAR Y CREAR EL DOCUMENTO
            $filePath = "documents/pe_it_sort/{$document}_{$activity->id}.pdf";
            $document = 'PCG';
            Storage::disk('s3')->put($filePath, $pdf->output());
            $fileUrl = Storage::disk('s3')->url($filePath);

            $activityDocument = new ActivityDocument();
            $activityDocument->activity_id = $activity->id;
            $activityDocument->document_id = ServiceDocument::;
            $activityDocument->path = $filePath;
            $activityDocument->save();

            */
            /* TODO: Fin de bloque de código a reemplazar*/

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
            ]);
        }
        return response()->json([
            'status' => 'success',
            'message' => 'Registro IP Manual',
        ]);
    }

    // REPORTAR VALIDACION DOCUMENTAL
    public function reportValidationDocument(Request $req, $cpath, $activity_id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $description = 'REPORTAR VALIDACIÓN DOCUMENTAL';
        DB::beginTransaction();
        try {
            $activity = Activity::where('client_id', $client->id)->where('id', $activity_id)->firstOrFail();

            //Crear acción y servicio ip sort
            $activityAction = ActionController::create(
                $activity->id,
                ActionPeIpSort::REPORTAR_VALIDACION_DOCUMENTAL,
                $description
            );


            DB::commit();
            return response()->json(["response" => true, "data" => $activityAction]);
        } catch (\Exception $e) {
            // dd($e);
            DB::rollBack();
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
            ]);
        }
    }


    /**
     * Acción APROBACIÓN PAGOS DE RENTA
     *
     * @param Request $request
     * @param $cpath
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function approvalPayment(Request $req, $cpath, $PeIpSort)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $description = 'Aprobación pagos de renta';
        DB::beginTransaction();
        try {
            $activity = Activity::where('client_id', $client->id)->where('id', $PeIpSort)->firstOrFail();

            $activityAction = ActionController::create(
                $activity->id,
                ActionPeIpSort::APROBACION_PAGOS_DE_RENTA,
                $description
            );

            $activityAffiliate = Affiliate::where('id', $activity->affiliate_id)->first();

            // Crear servicio
            $activityPeAffiliatePayment = new Activity();
            $activityPeAffiliatePayment->parent_id = $activity->id;
            $activityPeAffiliatePayment->client_id = $activity->client_id;
            $activityPeAffiliatePayment->affiliate_id = $activity->affiliate_id;
            $activityPeAffiliatePayment->user_id = $activity->user_id;
            $activityPeAffiliatePayment->service_id = Service::SERVICE_AFFILIATE_PAYMENT_MNK;
            $activityPeAffiliatePayment->state_id = State::REGISTRADO;


            $subject = 'Liquidación renta por IP - Aprobación pagos de renta ';
            $document = 'liquidacion_renta_por_IP';
            $text = 'Liquidación renta por IP';

            $result = $this->resendEmail($req, $activity->id, Service::SERVICE_PE_IP_SORT_MNK, $activityAction, $activityAffiliate->email, $PeIpSort, $subject, $client, $document, $text);

            if ($activityPeAffiliatePayment->save()) {
                $responseMessage = "Actividad de servicio guardada correctamente";
            } else {
                $responseMessage = "Error al guardar actividad de servicio";
            }

            if ($result) {
                DB::commit();
                return response()->json(["response" => true, "message" => "correo enviado correctamente", "debug" => $responseMessage]);
            } else {
                DB::rollBack();
                return response()->json(["response" => false, "message" => "error al enviar correo", "debug" => $responseMessage]);
            }

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
            ]);
        }
    }

    public function resendEmail(Request $req, $activity, $activity_service, $activityAction, $emails, $arrayData, $subject, $client_id, $document, $text)
    {
        $files = array();
        $paths = array();

        $pdf = PDF::loadView("services.plantilla.docs.{$document}", ['arrayData' => $arrayData, 'watermark' => true]);

        //Cargamos el archivo en S3
        Storage::disk('s3')
            ->put("activity_action_document/{$document}_{$activityAction}.pdf", $pdf->output());

        //guardamos en activity_action_documents
        $activityActionDocument = new ActivityActionDocument();
        $activityActionDocument->activity_action_id = $activityAction->id;
        $activityActionDocument->name = $document;
        $activityActionDocument->path = "activity_action_document/{$document}_{$activityAction->id}.pdf";
        $activityActionDocument->save();

        //formamos archivo
        $files[] = [
            'type' => 'pdf',
            'path' => "activity_action_document/{$document}_{$activityAction}.pdf",
            'name' => $document . '.pdf',
        ];

        //dirección path
        $paths[] = "activity_action_document/{$document}_{$activityAction}.pdf";

        //Logica para envio de correo
        if ($emails != null) {
            $emails = array_map('trim', explode(',', $emails));

            $affiliate_files = [];
            foreach ($files as $file) {
                if (!(strpos($file['path'], 'affiliate_employer') !== false)) {
                    $affiliate_files[] = $file;
                }
            }

            $mailSent = new SendDocumentDataBase(
                implode(',', $emails),
                $subject,
                "<EMAIL>",
                $text,
                [
                    "text" => $text,
                    "sender" => 'Prueba'
                ],
                "<EMAIL>",
                $files,
                "send_document_db",
                $client_id,
                $req->getHost(),
                $activity,
                $activityAction,
                $activity_service
            );
            
              // Capturar el resultado del envío
            $result = $mailSent->sendMail();

            // //Registramos los datos del correo enviado para la trazabilidad
            // $mailBoardController = new MailBoardController();
            // $mailBoardController->createRegisterMail(
            //     $activity->id,
            //     $activity->service->id, 
            //     '', 
            //     'Asegurado', 
            //     '', 
            //     '', 
            //     $subject, 
            //     $text,
            //     $emails, 
            //     $result,
            //     $files
            // );

            if ($mailSent) {
                return $mailSent;
            }
        }
    }


    /**
     * Rechazar servicio
     * Servicio PE IP-Sort (incapacidad permanente)
     *
     * @param $activityVariation
     * @return \Illuminate\Http\JsonResponse
     */
    public function rejectService(Request $req, $cpath, $id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('client_id', $client->id)
            ->where('id', $id)
            ->firstOrFail();

        DB::beginTransaction();
        try {
            $actionController =ActionController::create($activity->id, ActionPeIPSort::RECHAZAR_IP ,'Rechazar el servicio');
            DB::commit();
            // Creación de correo notificando el pago de la incapacidad
            $email = $activity->affiliate->email;
            $subject = "Rechazo de pago de incapacidad permanente.";
            $client_id = $client->id;
            $text = " Vacío por el momento";

            $mailSent = new SendDocumentDataBase(
                $email,
                $subject,
                "<EMAIL>",
                "Pago de incapacidad temporal",
                [
                    "text" => $text,
                    "sender" => 'MNK'
                ],
                "<EMAIL>",
                [], // Sin archivos adjuntos
                "send_document_db",
                $client_id,
                $req->getHost(),
                $activity->id,
                $actionController->id,
                $activity->service->id
            );
            
            // Capturar el resultado del envío
            $result = $mailSent->sendMail();

            //Registramos los datos del correo enviado para la trazabilidad
            $mailBoardController = new MailBoardController();
            $mailBoardController->createRegisterMail(
                $activity->id,
                $activity->service->id, 
                '', 
                'Asegurado', 
                ucwords(mb_strtolower($activity->affiliate->full_name)), 
                $activity->affiliate_doc_number, 
                $subject, 
                $text,
                $email, 
                $result,
                null
            );
            
        } catch (\Exception $e) {
            DB::rollback();
            return back()->with('error', 'No se pudo realizar la operacion');
        }
        return back()->with('success', 'Operación realizada con éxito');
    }
}