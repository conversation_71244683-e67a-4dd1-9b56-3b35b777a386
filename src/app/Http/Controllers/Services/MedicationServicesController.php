<?php

namespace App\Http\Controllers\Services;

use App\Action;
use App\Actions\ActionMedicalServiceSecondarySort;
use App\Actions\ActionMedicalServiceSort;
use App\Actions\ActionMedicationServiceSort;
use App\Activity;
use App\ActivityAction;
use App\ActivityActionDocument;
use App\Client;
use App\GisSort;
use App\Http\Controllers\ActionController;
use App\Http\Controllers\Controller;
use App\Http\Controllers\Integrations\DokkaController;
use App\MedicalServiceMedicalPrescription;
use App\MedicalServiceSecondaryMedicalPrescription;
use App\MedicalServicesSecondaryCareSort;
use App\MedicalServicesSort;
use App\Medication;
use App\MedicationServiceControlledMedication;
use App\MedicationServiceDiagnostics;
use App\MedicationServiceMedicalPrescription;
use App\Provider;
use App\Service;
use App\State;
use App\States\StateMedicationServiceSort;
use App\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use PDF;

class MedicationServicesController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function form(Request $req, $cpath, $id)
    {

        $client = Client::where('path', $cpath)->firstOrFail();

        //Actividad de medicamentos
        $medication_service_activity = Activity::where('client_id', $client->id)->where('id', $id)->first();

        //Actividad de prestación médica
        $medical_service_activity = Activity::where('client_id', $client->id)->where('id',  $medication_service_activity->parent_id)->first();

        //Actividad de Gis
        $activity_gis = $medical_service_activity ? Activity::where('client_id', $client->id)->where('id', $medical_service_activity->parent_id)->first() : null;
        $gis = $activity_gis ? GisSort::where('activity_id', $activity_gis->id)->first() : null;

        //Actividad de poliza
        $policy_sort_activity = $activity_gis ? Activity::where('id', $activity_gis->parent_id)->first() : null;
        $policy_sort = $policy_sort_activity ? $policy_sort_activity->policy_sort : null;

        //Json de actividades economica
        $jsonSource = $policy_sort && ($policy_sort->economic_activity == 'public') ? '/js/economic_activity/public.json' : '/js/economic_activity/private.json';
        $json = file_get_contents(public_path($jsonSource));
        $economicActivities = json_decode($json, true);
        //Se transforma a una collección en laravel
        $activity_economic_name = $economicActivities && $policy_sort ? collect($economicActivities)->firstWhere('CODE', $policy_sort->activity_economic_id)['ACTIVITY_NAME'] : null;

        //Datos de medicamentos y sus relaciones
        $medication_service_data = $medication_service_activity->medication;

        //RETORNAR DIAGNOSTICOS
        $diagnostics =  $medication_service_data ? $medication_service_data->diagnostics : null;
        //RETORNAR FORMULA MEDICA
        $medical_prescriptions = $medication_service_data ? $medication_service_data->medical_prescriptions : null;
        //RETORNAR FORMULA DE MEDICAMENTOS CONTROLADOS
        $controlled_medications = $medication_service_data ? $medication_service_data->controlled_medications : null;


        return view('services.medication_services.form.form', [
            'activity' => $medication_service_activity,
            'affiliate' => $medical_service_activity ? $medical_service_activity->affiliate ?? '' : null,
            'id' => $id,
            'policy_sort' => $policy_sort,
            'activity_economic_name' => $activity_economic_name,
            'gis' => $gis,
            'medication_service' => $medication_service_data,
            'diagnostics' => $diagnostics,
            'medical_prescriptions' => $medical_prescriptions,
            'controlled_medications' => $controlled_medications
        ]);
    }
    //REPORTAR SOLICITUD MEDICAMENTOS
    public function reportMedicationRequest($cpath, $id, $action, $nameDocument, $pathDocument)
    {
        $client   = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();
        $activity_actions = ActivityAction::query()
            ->where('activity_id', $activity->id)
            ->where('action_id', ActionMedicalServiceSort::ASIGNAR_SERVICIO_ATENCION_PRIMARIA)
            ->orderBy('created_at', 'desc')
            ->first();

        if ($activity->service_id == Service::SERVICE_MEDICAL_SERVICES_SECONDARY_CARE_SORT_MNK){
            $medical_service = MedicalServicesSecondaryCareSort::where('activity_id', $activity->id)->firstOrFail();
        } else {
            $medical_service = MedicalServicesSort::where('activity_id', $activity->id)->firstOrFail();
        }


        try {
            $new_activity = Activity::create([
                'parent_id' => $activity->id,
                'client_id' => $client->id,
                'affiliate_id' => $activity->affiliate_id,
                'service_id' => Service::SERVICE_MEDICAMENTOS_MNK,
                'state_id' => State::REGISTRADO,
                'user_id' => $activity->user_id,
            ]);
            if ($activity_actions) {
                $activityActionsCreated = ActionController::create($new_activity->id,ActionMedicationServiceSort::SOLICITAR_MEDICAMENTO_AP ,'Solicitar Medicamentos AP');
            }else{
                $activityActionsCreated = ActionController::create($new_activity->id,ActionMedicationServiceSort::REPORTAR_SOLICITUD_MEDICAMENTOS ,'REPORTAR SOLICITUD MEDICAMENTOS');
            }

            $activityActionDocument = new ActivityActionDocument();
            $activityActionDocument->activity_action_id = $activityActionsCreated->id;
            $activityActionDocument->name = $nameDocument;
            $activityActionDocument->path = $pathDocument;
            $activityActionDocument->save();

            $analyst = User::where('area_id', 44)->first();
            //CREAR SERVICIO MEDICAMENTOS
            $medication = Medication::create([
                'activity_id' => $new_activity->id,
                'analyst' => $analyst->id,
                'provider' => Provider::PROVIDER_DOKKA,
                'name_patient' => $medical_service->name_patient,
                'activity' => $medical_service->activity,
                'date_dictamen' => $medical_service->date_dictamen,
                'name_patron' => $medical_service->name_patron,
                'id_patron' => $medical_service->id_patron,
                'num_policy_sort' => $medical_service->num_policy_sort,
            ]);
            //DIAGNOSTICOS DE LA PRESTACION MEDICA
            $follow_up_medical_service = $medical_service->followUps()->latest()->first();
            $follow_diagnostics = $follow_up_medical_service->diagnostics;

            if ($follow_diagnostics->isNotEmpty()) {
                foreach ($follow_diagnostics as $diagnostic) {
                    MedicationServiceDiagnostics::create([
                        'medication_service_sort_id' => $medication->id,
                        'code'=> $diagnostic->code,
                        'description' => $diagnostic->description,
                        'description_editable' => $diagnostic->description_editable,
                        'laterality' => $diagnostic->laterality,
                        'origin' => $diagnostic->origin,
                    ]);
                }
            }
            //FORMULA DE MEDICAMENTOS DE LA PRESTACION MEDICA
            if ($action == ActionMedicalServiceSort::EMITIR_FORMULA_DE_MEDICAMENTOS || $action == ActionMedicalServiceSecondarySort::EMITIR_FORMULA_DE_MEDICAMENTOS){

                $medical_service_prescription = $follow_up_medical_service->medicalPrescriptions;
                $medication->province_medical_prescription = $follow_up_medical_service->province_medical_prescription;
                $medication->canton_medical_prescription = $follow_up_medical_service->canton_medical_prescription;
                $medication->district_medical_prescription = $follow_up_medical_service->district_medical_prescription;
                $medication->diagnosis_origin_prescription = $follow_up_medical_service->diagnosis_origin_prescription;
                $medication->save();

                foreach ($medical_service_prescription as $prescription) {
                    MedicationServiceMedicalPrescription::create([
                        'molecula' => $prescription->molecula,
                        'tipo' => $prescription->tipo,
                        'descrip' => $prescription->descrip,
                        'codigo' => $prescription->codigo,
                        'casa' => $prescription->casa,
                        'treatment_duration' => $prescription->treatment_duration,
                        'frequency' => $prescription->frequency,
                        'dosage' => $prescription->dosage,
                        'quantity_letters' => $prescription->quantity_letters,
                        'quantity_numbers' => $prescription->quantity_numbers,
                        'notes' => $prescription->notes,
                        'medication_service_sort_id' => $medication->id,
                    ]);
                }
            }


            //FORMULA DE MEDICAMENTOS CONTROLADOS DE LA PRESTACION MEDICA
            if ($action == ActionMedicalServiceSort::EMITIR_FORMULA_DE_MEDICAMENTOS_CONTROLADO){
                $medical_service_prescription = $follow_up_medical_service->controlledMedications;

                $medication->province_controlled_medication = $follow_up_medical_service->province_controlled_medication;
                $medication->canton_controlled_medication = $follow_up_medical_service->canton_controlled_medication;
                $medication->district_controlled_medication = $follow_up_medical_service->district_controlled_medication;
                $medication->diagnosis_origin_controlled_medication = $follow_up_medical_service->diagnosis_origin_controlled_medication;
                $medication->save();

                foreach ($medical_service_prescription as $prescription) {
                    MedicationServiceControlledMedication::create([
                        'molecula' => $prescription->molecula,
                        'tipo' => $prescription->generic_name,
                        'descrip' => $prescription->concentration,
                        'codigo' => $prescription->codigo,
                        'casa' => $prescription->casa,
                        'pharmaceutical_form' => $prescription->pharmaceutical_form,
                        'treatment_duration' => $prescription->treatment_duration,
                        'frequency' => $prescription->frequency,
                        'dosage' => $prescription->dosage,
                        'quantity_letters' => $prescription->quantity_letters,
                        'quantity_numbers' => $prescription->quantity_numbers,
                        'notes' => $prescription->notes,
                        'medication_service_sort_id' => $medication->id,
                    ]);
                }
            }

            // Asignar el tipo de servicio a la actividad
            $type_service_medication_map = [
                ActionMedicalServiceSort::EMITIR_FORMULA_DE_MEDICAMENTOS => "prescription",
                ActionMedicalServiceSort::EMITIR_FORMULA_DE_MEDICAMENTOS_CONTROLADO => "controlled-medication-formula"
            ];

            $type_service_medication = $type_service_medication_map[$action] ?? ""; // Asigna el valor o una cadena vacía si no hay coincidencias

            $new_activity->type_service_medication = $type_service_medication;
            $new_activity->save();

            return $new_activity;
        }catch (\Exception $e) {
            throw $e;
        }
    }

    public function save(Request $req, $cpath, $id)
    {

        $client = Client::where('path', $cpath)->firstOrFail();
        $medication_service_activity = Activity::where('client_id', $client->id)
            ->where('id', $id)
            ->firstOrFail();

        //Datos de la tabla medicamentos
        $medication_service_data = $medication_service_activity->medication;

        $provider_assignment_date = $req->input('provider_assignment_date');
        $medication_delivery_date = $req->input('medication_delivery_date');


        //Iniciamos la transacción
        DB::beginTransaction();
        try {
            if ($medication_service_data) {
                $data = [
                    'name_patient' => $req->input('name_patient') ?? $medication_service_data->name_patient,
                    'activity' => $req->input('activity') ?? $medication_service_data->activity,
                    'date_dictamen' => $req->input('date_dictamen') ?? $medication_service_data->date_dictamen,
                    'name_patron' => $req->input('name_patron') ?? $medication_service_data->name_patron,
                    'id_patron' => $req->input('id_patron') ?? $medication_service_data->id_patron,
                    'num_policy_sort' => $req->input('num_policy_sort') ?? $medication_service_data->num_policy_sort,
                    'doctor_name_generating_prescription' => $req->input('doctor_name_generating_prescription'),
                    'medical_license' => $req->input('medical_license'),
                    'specialty' => $req->input('specialty'),
                    'pickup_name' => $req->input('pickup_name') ?? $medication_service_data->pickup_name,
                    'pickup_id' => $req->input('pickup_id') ?? $medication_service_data->pickup_id,
                    'pickup_phone' => $req->input('pickup_phone') ?? $medication_service_data->pickup_phone,
                ];

                //Actualiza los campos de medicamentos
                $medication_service_data->update($data);


                //GUARDAR APROBACIÓN ENTREGA MEDICAMENTOS
                if ($medication_service_activity->state_id == StateMedicationServiceSort::SOLICITUD_MEDICAMENTOS_EN_REVISION_POR_AUDITORIA_MEDICA &&
                    $req->input('approved_service') !== null &&
                    $req->input('observation_audit') !== null)
                {
                    //guardar la aprobación o negación del auditor
                    $medication_service_data->approved_service = $req->input('approved_service');
                    $medication_service_data->observation_audit = $req->input('observation_audit');
                    $medication_service_data->save();

                    //guardar si hubo cambios en los medicamentos por parte del auditor
                    $isChange = $this->ValidateChangesInMedications($medication_service_data,$req);

                    //si aprobo generar acción al servicio
                    if($req->input('approved_service') == 1) {

                        //condicion si hubo cambios
                        if($isChange){
                            //cambio de medicamenos por auditor
                            ActionController::create($medication_service_activity->id,
                                ActionMedicationServiceSort::CAMBIAR_MEDICAMENTOS_POR_AUDITOR ,
                                'Cambiar medicamentos por auditor');
                        }
                      else{
                          //aprobación de los medicamenos por auditor
                          ActionController::create($medication_service_activity->id,
                              ActionMedicationServiceSort::APROBAR_MEDICAMENTOS_POR_AUDITOR ,
                              'Aprobar medicamentos por auditor');
                      }
                    }
                }

                //GUARDAR FARMACIA (O en su defecto compra externa)

                if ($medication_service_activity->state_id == StateMedicationServiceSort::PENDIENTE_SELECCIONAR_FARMACIA)
                {
                    //guardar los siguientes datos
                    $medication_service_data->external_purchase = $req->input('external_purchase') ?? '';

                    // Decodificar el JSON de 'pharmacy' para obtener id y nombre
                    $pharmacyData = json_decode($req->input('pharmacy'), true);

                    if ($pharmacyData) {
                        $medication_service_data->sucursal_id = $pharmacyData['id'] ?? '';
                        $medication_service_data->sucursal_name = $pharmacyData['name'] ?? '';
                    }
                    //guardar datos
                    $medication_service_data->save();

                    //Generación acción para la actividad
                    $description = ($medication_service_data->external_purchase == 1) ? "Generar orden de atención compra interna" : "Seleccionar farmacia"; //descripción de la acción

                    //sea que haya sido compra externa o ya por farmacia previamente seleccionada
                   if($medication_service_data->external_purchase == 1) //compra externa
                   {
                       ActionController::create($medication_service_activity->id,
                           ActionMedicationServiceSort::GENERAR_ORDEN_DE_ATENCION_COMPRA_INTERNA,
                           $description);
                   }
                   else //por farmacia seleccionada
                   {
                       ActionController::create($medication_service_activity->id,
                           ActionMedicationServiceSort::SELECCIONAR_FARMACIA,
                           $description);
                   }
                }

                 //GUARDAR ENTREGA DE MEDICAMENTOS
                if ($medication_service_activity->state_id == StateMedicationServiceSort::FARMACIA_SELECCIONADA &&
                    $provider_assignment_date !== null &&
                    $medication_delivery_date !== null &&
                    $req->input('delivery_type') !== null &&
                    $req->input('delivery_channel') !== null ) {

                    $medication_service_data->provider_assignment_date = $provider_assignment_date;
                    $medication_service_data->medication_delivery_date = $medication_delivery_date;
                    $medication_service_data->delivery_type = $req->input('delivery_type');
                    $medication_service_data->delivery_channel = $req->input('delivery_channel');
                    $medication_service_data->save();
                    ActionController::create($medication_service_activity->id,
                        ActionMedicationServiceSort::ENVIAR_ORDEN_DE_PREFACTURA,
                        'ENVIAR ORDEN DE PREFACTURA');
                }

                DB::commit();
                return redirect('servicio/' . $medication_service_activity->id);
            } else {
                return response()->json(['error' => 'No se puede actualizar el servicio Medicamentos'], 500);
            }
        } catch (\Exception $e) {
            DB::rollback();
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    public function reportMedicationDelivery(Request $req, $cpath, $id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();
        DB::beginTransaction();
        try {
            $medication = Medication::where('activity_id', $activity->id)->first();
            $medication->delivery_date = $req->delivery_date;
            $medication->delivery_mode = $req->delivery_mode;
            $medication->save();

            ActionController::create($activity->id, ActionMedicationServiceSort::REPORTAR_ENTREGA_DE_MEDICAMENTOS, 'REPORTAR ENTREGA DE MEDICAMENTOS');
            DB::commit();
            return response()->json(['status' => 'success', 'message' => 'Entrega de medicamentos reportada con éxito']);
        } catch (\Exception $e) {
            DB::rollback();
            return response()->json(['status' => 'error', 'message' => $e->getMessage()]);
        }

    }

    public function denialService(Request $req, $cpath, $id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        DB::beginTransaction();
        try {
            //TODO: Recibir servicio / actividad
            $medicationServiceActivity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();

            //TODO: Capturar motivo de anulación
            $medicationServiceSort = Medication::where('activity_id', $medicationServiceActivity->id)->first();
            $medicationServiceSort->reason_cancel = $req->motivo;
            $medicationServiceSort->save();

            //TODO: Crear historial de acciones
            $description = "ANULAR SERVICIO";
            $activityActionsCreated = ActionController::create($medicationServiceActivity->id,
                ActionMedicationServiceSort::ANULAR_SERVICIO,
                $description);

            DB::commit();

            return redirect('servicio/' . $medicationServiceActivity->id);

        } catch (\Exception $e) {
            DB::rollBack();
            // Retornar un mensaje de error o manejar la excepción
            return response()->json([
                'error' => 'Ocurrió un error al guardar los datos',
                'code' => $e->getCode(),
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function reportDenialService(Request $req, $cpath, $id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        DB::beginTransaction();
        try {
            //TODO: Recibir servicio / actividad
            $medicationServiceActivity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();
            //TODO: Capturar motivo de anulación
            $medicationServiceSort = Medication::where('activity_id', $medicationServiceActivity->id)->first();
            $request_reason = $req->input('reason_cancel');
            $reason_cancel = [
                0 => 'Evento no laboral',
                1 => 'Diagnósticos de origen común',
                2 => 'Diagnósticos no derivados del evento',
                3 => 'No asegurado',
                4 => 'No pertinencia técnica'
            ];
            // Asignar el valor si existe, de lo contrario asignar un valor por defecto
            $medicationServiceSort->reason_cancel = array_key_exists($request_reason, $reason_cancel)
                ? $reason_cancel[$request_reason]
                : 'Motivo no válido';

            $medicationServiceSort->save();
            //TODO: Crear historial de acciones
            $description = "SERVICIO NEGADO";
            $activityActionsCreated = ActionController::create($medicationServiceActivity->id,
                ActionMedicationServiceSort::REPORTAR_NEGACION_SERVICIO,
                $description);

            DB::commit();

            return redirect('servicio/' . $medicationServiceActivity->id);

        } catch (\Exception $e) {
            DB::rollBack();
            // Retornar un mensaje de error o manejar la excepción
            return response()->json([
                'error' => 'Ocurrió un error al guardar los datos',
                'code' => $e->getCode(),
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function reportMedicalAuditMnk(Request $req, $cpath, $id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        DB::beginTransaction();
        try {

            //TODO: Recibir servicio / actividad
            $medicationServiceActivity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();
            //TODO: Generar documento reporte de auditoría médica MNK
            // Generar el PDF
            $document = 'reportMedicalAuditMnkMedication';
            $action_id = ActionMedicationServiceSort::REPORTAR_AUDITORIA_MEDICA_MNK;

            $pdf = PDF::loadView("services.medication_services.docs.{$document}_pdf", ['value' => "pendiente"]); //modificar

            // Guardar el PDF en S3
            Storage::disk('s3')
                ->put("activity_action_document/{$document}_{$action_id}.pdf", $pdf->output());

            //TODO: Crear historial de acciones
            $description = $req->input('description');

            $activityActionsCreated = ActionController::create($medicationServiceActivity->id,
                $action_id,
                $description);

            $activityActionDocument = new ActivityActionDocument();
            $activityActionDocument->activity_action_id = $activityActionsCreated->id;
            $activityActionDocument->name = $document;
            $activityActionDocument->path = "activity_action_document/{$document}_{$action_id}.pdf";
            $activityActionDocument->save();
            DB::commit();

            return back()->with('success', 'Operación realizada con éxito');

        } catch (\Exception $e) {
            DB::rollBack();
            // Retornar un mensaje de error o manejar la excepción
            return response()->json([
                'error' => 'Ocurrió un error al guardar los datos',
                'code' => $e->getCode(),
                'message' => $e->getMessage()
            ], 500);
        }
    }

    //APROBACION SERVICIO
    public function approveService(Request $req, $cpath, $id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $medication_service_activity = Activity::where('client_id', $client->id)
            ->where('id', $id)
            ->firstOrFail();

        DB::beginTransaction();
        try {
            ActionController::create($medication_service_activity->id,
                ActionMedicationServiceSort::REPORTAR_APROBACION_SERVICIO ,
                'REPORTAR APROBACIÓN SERVICIO');

            DB::commit();
            return back()->with('success', 'Operación realizada con éxito');
        } catch (\Exception $e) {
            DB::rollback();
            return back()->with('error', 'No se pudo realizar la operacion');
        }
    }

    public function getPharmacies(Request $req, $cpath, $id)
    {
        // Capturar servicio de medicamentos
        $medication_service = Medication::where('activity_id', $id)->first();

        // Verificar si el servicio de medicamentos existe
        if (!$medication_service) {
            return response()->json([
                'error' => 'Servicio de medicamentos no encontrado',
                'code' => 404,
                'message' => 'Servicio de medicamentos no encontrado'
            ], 404);
        }

        // Lista de medicamentos activos del servicio
        $list_medications = collect();

        if ($medication_service->diagnosis_origin_prescription) {
            $list_medications = MedicationServiceMedicalPrescription::where('medication_service_sort_id', $medication_service->id)->get();
        } else {
            $list_medications = MedicationServiceControlledMedication::where('medication_service_sort_id', $medication_service->id)->get();
        }

        // Filtrar solo las columnas necesarias
        $list_medications_filtered = $list_medications->map(function ($med) {
            return [
                'codigo' => $med->codigo,
                'quantity_numbers' => intval($med->quantity_numbers) // Convertir a entero
            ];
        });

        $list_medications_filtered = collect([
            ['codigo' => '5283662', 'quantity_numbers' => 5],
        ['codigo' => '5283662', 'quantity_numbers' => 5]
        ]);

        // Verificar si hay medicamentos antes de llamar a la API
        if ($list_medications_filtered->isEmpty()) {
            return response()->json([
                'error' => 'No hay medicamentos activos en el servicio',
                'code' => 400,
                'message' => 'No se encontraron medicamentos para la consulta'
            ], 400);
        }

        // Capturar consultar saldo - API de Dokka
        $api = app(DokkaController::class);
        $response = $api->checkPharmaciesWithVademecum($list_medications_filtered);


        // Decodificar la respuesta, ya sea que venga como string o como JsonResponse
        if (is_string($response)) {
            $response = json_decode($response, true);
        } elseif ($response instanceof \Illuminate\Http\JsonResponse) {
            $response = json_decode($response->getContent(), true);
        }

        // Manejo de respuesta con código de error "NO_STOCK"
        if (isset($response['code']) && $response['code'] === 'NO_STOCK') {
            return response()->json([
                'success' => false,
                'message' => $response['message']
            ]);
        }

        // Verificar si la API respondió correctamente y se obtuvieron sucursales
        if (isset($response['data']['codigoRespuesta']) && $response['data']['codigoRespuesta'] == "200") {
            if (!empty($response['data']['sucursales'])) {
                return response()->json([
                    'success' => true,
                    'data' => $response['data']['sucursales'],
                    'message' => $response['data']['message']
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'data' => [],
                    'message' => 'No hay sucursales disponibles'
                ]);
            }
        }

        // Respuesta por defecto en caso de error
        return response()->json([
            'success' => false,
            'message' => $response['message'] ?? 'Error en la respuesta de la API Dokka'
        ], 400);

    }

    function ValidateChangesInMedications($medication, $req)
    {
        // Flag para detectar cambios
        $isChange = false;

        // Verificar si el medicamento tiene receta de origen
        if ($medication->diagnosis_origin_prescription) {

            // Obtener los medicamentos actuales en la base de datos
            $medicationsBd = MedicationServiceMedicalPrescription::where('medication_service_sort_id', $medication->id)->get();

            // Capturar los datos del request
            $molecula_medical_prescription = $req->input('molecula-prescription2');
            $tipo_medical_prescription = $req->input('tipo-prescription2');
            $descrip_medical_prescription = $req->input('descrip-prescription2');
            $codigo_medical_prescription = $req->input('codigo-vademecum2');
            $casa_medical_prescription = $req->input('casa-vademecum2');
            $treatment_durations_medical_prescription = $req->input('duracion_tratamiento2');
            $frequency_medical_prescription = $req->input('frecuencia2');
            $dosage_medical_prescription = $req->input('dosis2');
            $quantity_letters_medical_prescription = $req->input('cantidad-letras2');
            $quantity_numbers_medical_prescription = $req->input('cantidad-numeros2');
            $notes_medical_prescription = $req->input('notas2');

            // Variable para detectar si los datos son diferentes
            $newData = [];

            if (isset($molecula_medical_prescription)) {
                foreach ($molecula_medical_prescription as $index => $molecula) {
                    // Validar que no sean nulos los campos necesarios
                    if ($molecula === null ||
                        $tipo_medical_prescription[$index] === null ||
                        $descrip_medical_prescription[$index] === null ||
                        $codigo_medical_prescription[$index] === null ||
                        $casa_medical_prescription[$index] === null ||
                        $treatment_durations_medical_prescription[$index] === null ||
                        $frequency_medical_prescription[$index] === null ||
                        $dosage_medical_prescription[$index] === null ||
                        $quantity_letters_medical_prescription[$index] === null ||
                        $quantity_numbers_medical_prescription[$index] === null ||
                        $notes_medical_prescription[$index] === null) {
                        continue; // Ignorar si algún campo es nulo
                    }

                    // Crear array con los datos nuevos
                    $array = [
                        'molecula' => $molecula,
                        'tipo' => $tipo_medical_prescription[$index],
                        'descrip' => $descrip_medical_prescription[$index],
                        'codigo' => $codigo_medical_prescription[$index],
                        'casa' => $casa_medical_prescription[$index],
                        'treatment_duration' => $treatment_durations_medical_prescription[$index],
                        'frequency' => $frequency_medical_prescription[$index],
                        'dosage' => $dosage_medical_prescription[$index],
                        'quantity_letters' => $quantity_letters_medical_prescription[$index],
                        'quantity_numbers' => $quantity_numbers_medical_prescription[$index],
                        'notes' => $notes_medical_prescription[$index],
                        'medication_service_sort_id' => $medication->id,
                    ];

                    $newData[] = $array;

                    // Comparar con la BD antes de eliminar
                    if (isset($medicationsBd[$index])) {
                        $medicationBd = $medicationsBd[$index];

                        if ($medicationBd->molecula != $molecula ||
                            $medicationBd->tipo != $tipo_medical_prescription[$index] ||
                            $medicationBd->descrip != $descrip_medical_prescription[$index] ||
                            $medicationBd->codigo != $codigo_medical_prescription[$index] ||
                            $medicationBd->casa != $casa_medical_prescription[$index] ||
                            $medicationBd->treatment_duration != $treatment_durations_medical_prescription[$index] ||
                            $medicationBd->frequency != $frequency_medical_prescription[$index] ||
                            $medicationBd->dosage != $dosage_medical_prescription[$index] ||
                            $medicationBd->quantity_letters != $quantity_letters_medical_prescription[$index] ||
                            $medicationBd->quantity_numbers != $quantity_numbers_medical_prescription[$index] ||
                            $medicationBd->notes != $notes_medical_prescription[$index]) {

                            $isChange = true;
                        }
                    } else {
                        $isChange = true; // Hay un nuevo medicamento
                    }
                }
            }

            // Si hay cambios, eliminar e insertar nuevos registros
            if ($isChange) {
                MedicationServiceMedicalPrescription::where('medication_service_sort_id', $medication->id)->delete();
                MedicationServiceMedicalPrescription::insert($newData);
            }
        }
        else{

            // Obtener los medicamentos actuales en la base de datos
            $medicationsBd = MedicationServiceControlledMedication::where('medication_service_sort_id', $medication->id)->get();

            // Capturar los datos del request
            $molecula_controlled_medication = $req->input('molecula-controlled-medication');
            $tipo_controlled_medication = $req->input('tipo-controlled-medication');
            $descrip_controlled_medication = $req->input('descrip-controlled-medication');
            $codigo_controlled_medication = $req->input('codigo-vademecum');
            $casa_controlled_medication = $req->input('casa-vademecum');
            $treatment_durations_controlled_medication = $req->input('duracion_tratamiento');
            $frequency_controlled_medication = $req->input('frecuencia');
            $dosage_controlled_medication = $req->input('dosis');
            $quantity_letters_controlled_medication = $req->input('cantidad-letras');
            $quantity_numbers_controlled_medication = $req->input('cantidad-numeros');
            $notes_controlled_medication = $req->input('notas');

            // Variable para detectar si los datos son diferentes
            $newData = [];

            if (isset($molecula_controlled_medication)) {
                foreach ($molecula_controlled_medication as $index => $molecula) {
                    // Validar que no sean nulos los campos necesarios
                    if ($molecula === null ||
                        $tipo_controlled_medication[$index] === null ||
                        $descrip_controlled_medication[$index] === null ||
                        $codigo_controlled_medication[$index] === null ||
                        $casa_controlled_medication[$index] === null ||
                        $treatment_durations_controlled_medication[$index] === null ||
                        $frequency_controlled_medication[$index] === null ||
                        $dosage_controlled_medication[$index] === null ||
                        $quantity_letters_controlled_medication[$index] === null ||
                        $quantity_numbers_controlled_medication[$index] === null ||
                        $notes_controlled_medication[$index] === null) {
                        continue; // Ignorar si algún campo es nulo
                    }

                    // Crear array con los datos nuevos
                    $array = [
                        'molecula' => $molecula,
                        'tipo' => $tipo_controlled_medication[$index],
                        'descrip' => $descrip_controlled_medication[$index],
                        'codigo' => $codigo_controlled_medication[$index],
                        'casa' => $casa_controlled_medication[$index],
                        'treatment_duration' => $treatment_durations_controlled_medication[$index],
                        'frequency' => $frequency_controlled_medication[$index],
                        'dosage' => $dosage_controlled_medication[$index],
                        'quantity_letters' => $quantity_letters_controlled_medication[$index],
                        'quantity_numbers' => $quantity_numbers_controlled_medication[$index],
                        'notes' => $notes_controlled_medication[$index],
                        'medication_service_sort_id' => $medication->id,
                    ];

                    $newData[] = $array;

                    // Comparar con la BD antes de eliminar
                    if (isset($medicationsBd[$index])) {
                        $medicationBd = $medicationsBd[$index];

                        if ($medicationBd->molecula != $molecula ||
                            $medicationBd->tipo != $tipo_controlled_medication[$index] ||
                            $medicationBd->descrip != $descrip_controlled_medication[$index] ||
                            $medicationBd->codigo != $codigo_controlled_medication[$index] ||
                            $medicationBd->casa != $casa_controlled_medication[$index] ||
                            $medicationBd->treatment_duration != $treatment_durations_controlled_medication[$index] ||
                            $medicationBd->frequency != $frequency_controlled_medication[$index] ||
                            $medicationBd->dosage != $dosage_controlled_medication[$index] ||
                            $medicationBd->quantity_letters != $quantity_letters_controlled_medication[$index] ||
                            $medicationBd->quantity_numbers != $quantity_numbers_controlled_medication[$index] ||
                            $medicationBd->notes != $notes_controlled_medication[$index]) {

                            $isChange = true;
                        }
                    } else {
                        $isChange = true; // Hay un nuevo medicamento
                    }
                }
            }

            // Si hay cambios, eliminar e insertar nuevos registros
            if ($isChange) {
                MedicationServiceControlledMedication::where('medication_service_sort_id', $medication->id)->delete();
                MedicationServiceControlledMedication::insert($newData);
            }
        }

        return $isChange;
    }

}