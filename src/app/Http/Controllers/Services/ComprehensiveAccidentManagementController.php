<?php

namespace App\Http\Controllers\Services;

use App\AccidentType;
use App\Action;
use App\Actions\ActionGisSort;
use App\Activity;
use App\ActivityAction;
use App\ActivityActionDocument;
use App\ActivityDocument;
use App\Affiliate;
use App\AgetsGi;
use App\Client;
use App\CompensationColons;
use App\CompensationColonsFive;
use App\CompensationColonsFour;
use App\CompensationColonsSix;
use App\CompensationDollars;
use App\CompensationDollarsFive;
use App\CompensationDollarsFour;
use App\CompensationDollarsSix;
use App\EconomicBenefit;
use App\GisBodyPart;
use App\GisDiagnostic;
use App\GisQualificationPcg;
use App\GisRequestAuthorization;
use App\GisSeguimientoPlanTratamiento;
use App\GisOriginNotification;
use App\GisValoracionMedicoLaboral;
use App\MedicalServiceDiagnostics;
use App\GisSort;
use App\GisSubrogacion;
use App\Holiday;
use App\Http\Controllers\ActionController;
use App\Http\Controllers\Controller;
use App\Http\Controllers\Tables\MailBoardController;
use App\Http\Controllers\Tables\CompensationBoardController;
use App\Mail\SendDocument;
use App\Mail\SendDocumentDataBase;
use App\MailTemplates\Constants\Templates;
use App\MailTemplates\TemplateBuilder;
use App\MedicalServicesSort;
use App\OccupationalDisease;
use App\OccupationCategory;
use App\PeIpSort;
use App\PeMptBeneficiaries;
use App\PeMptSort;
use App\PolicyContact;
use App\PolicySort;
use App\PolicySpreadsheet;
use App\PolicySpreadsheetAffiliate;
use App\Provider;
use App\Providers\AppServiceProvider;
use App\SentSurvey;
use App\Service;
use App\State;
use App\States\StateGis;
use App\States\StatePeItSort;
use App\States\StatePeIpSort;
use App\States\StatePeexpenserecognition;
use App\User;
use App\UserAuthorizedPolicies;
use App\UserClient;
use App\Utilities\Utilities;
use Carbon\Carbon;
use DateTime;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Str;
use PDF;
use Illuminate\Support\Facades\File;
use App\MedicalServiceFollowUp;
use App\ServiceDocument;

class ComprehensiveAccidentManagementController extends Controller
{

    //Constantes
    private const PLANILLAS_PERMISSION_ID = 2;
    private const ACCIDENTS_ILLNESSES_PERMISSION_ID = 1;


    /**
     * Create a new controller instance.
     *
     * @return void
     */

    protected $policySortController;
    public function __construct()
    {
        $this->middleware('auth')->except([
            'formalCaseFormatClosureAutomatic',
            'closingCaseAutomatic',
            'calculatePpsDeathWorker',
            'calculateCommutationFactor',
            '_buildBodyEmailNotificationCaseNotCovered',
            'sendSurveyGis'
        ]);

        $this->policySortController = app()->make(PolicySortController::class);
    }

    public function index(Request $req, $cpath, $id = null)
    {
        return $this->form_responsable($req, $cpath, $id);
    }

    //Calcula la fecha con los días hábiles, sin tener en cuenta los días feriados de COSTA RICA y los fines de semana
    public function calculateBusinessDays($startDate, $businessDays)
    {
        // Obtener todos los feriados desde la tabla 'holidays'
        $holidays = Holiday::pluck('holiday') // Aquí obtienes solo las fechas
            ->toArray(); // Convertir a un array para usar en la función

        // Convertir la fecha de inicio a un objeto Carbon
        $currentDate = Carbon::parse($startDate);

        $daysAdded = 0;

        // Mientras no hayamos alcanzado los días hábiles deseados
        while ($daysAdded < $businessDays) {
            // Sumar un día
            $currentDate->addDay();

            // Verificar si es fin de semana
            if ($currentDate->isWeekend()) {
                continue; // Saltar si es fin de semana
            }

            // Verificar si es un día feriado
            if (in_array($currentDate->format('Y-m-d'), $holidays)) {
                continue; // Saltar si es feriado
            }

            // Contar el día hábil
            $daysAdded++;
        }

        return $currentDate->format('Y-m-d');
    }


    public function form_responsable(Request $request, $cpath, $id = null)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $policy = $id ? PolicySort::with([
            'activity.affiliate' => function ($query) use ($client) {
                $query->where('client_id', $client->id);
            }
        ])->where('id', $id)->firstOrFail() : null;

        $user = auth()->user();

        $policy_contacts = '';

        if ($policy) {

            //si es el tomador del seguro
            if ($user->area_id == 46) {

                // Obtener datos de PolicyContact filtrados por policy_sort_id
                $policys = PolicyContact::where('policy_sort_id', $id)->get()->map(function ($item) {
                    return (object) [
                        'type_identification' => $item->type_identification,
                        'number_identify_responsible' => $item->number_identify_responsible,
                        'name_responsible' => $item->name_responsible,
                        'phone_responsible' => $item->phone_responsible,
                        'unique_code' => $item->unique_code,
                        'model' => 'PolicyContact',
                        'responsible_id' => $item->id
                    ];
                });

                // Obtener datos de User filtrados por tomador_id
                $takerAuthorized = User::where('tomador_id', $user->id)->get()->map(function ($item) {
                    return (object) [
                        'type_identification' => $item->doc_type,
                        'number_identify_responsible' => $item->identification_number,
                        'name_responsible' => $item->full_name,
                        'phone_responsible' => $item->phone,
                        'unique_code' => $item->unique_code,
                        'model' => 'User',
                        'responsible_id' => $item->id
                    ];
                });

                // Combinar ambas colecciones
                $policy_contacts = $policys->merge($takerAuthorized);
            }
            //si es ealgun responsable de reprtar el siniestro o tomador autorizado
            else {
                $policy_contacts = [
                    (object) [
                        'type_identification' => $user->doc_type,
                        'number_identify_responsible' => $user->identification_number,
                        'name_responsible' => $user->full_name,
                        'phone_responsible' => $user->phone,
                        'unique_code' => $user->unique_code,
                        'model' => 'User',
                        'responsible_id' => $user->id
                    ]
                ];
            }

            return view('services.gis.table_gis.form.form_responsable', ['policy' => $policy, 'active' => 'responsable', 'policy_contacts' => $policy_contacts]);
        }

        return view('services.gis.table_gis.form.form_responsable', ['policy' => $policy, 'active' => 'responsable', 'policy_contacts' => $policy_contacts]);
    }

    public function form_datos(Request $request, $cpath, $id = null)
    {

        $client = Client::where('path', $cpath)->firstOrFail();
        $policy = $id ? PolicySort::with([
            'activity.affiliate' => function ($query) use ($client) {
                $query->where('client_id', $client->id);
            }
        ])->where('id', $id)->firstOrFail() : null;

        session([
            'model' => $request->model,
            'responsible_id' => $request->responsible_id,
        ]);

        return view('services.gis.table_gis.form.form_datos', ['policy' => $policy, 'active' => 'datos']);
    }

    public function form_accidente(Request $request, $cpath, $id = null)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $policy = $id ? PolicySort::with([
            'activity.affiliate' => function ($query) use ($client) {
                $query->where('client_id', $client->id);
            }
        ])->where('id', $id)->firstOrFail() : null;

        return view('services.gis.table_gis.form.form_accidente', ['policy' => $policy, 'active' => 'accidente']);
    }

    public function table_call()
    {
        return view('services.gis.table_call.gis_call');
    }

    //Validamos si el afiliado existe reportado en la planilla
    public function validateAffiliate(Request $req, $cpath, $id)
    {

        try {

            //Buscamos la poliza por su id
            $policy = PolicySort::find($req->policy_id);

            //Buscamos la ultima actividad del reporte planilla tomador mediante la poliza
            $activityReport = Activity::where('parent_id', $policy->activity_id)
                ->where('service_id', Service::SERVICE_REPORT_TAKEN_FORM_MNK)
                ->latest()
                ->first();

            $dateAdmission = Carbon::parse($req->date_admission);

            if (!$activityReport) {

                //Si el afiliado no se encuentra en la planilla pero la poliza tiene beneficio de colectividad
                //Si el mes de date_admission es el mes actual 
                if ($dateAdmission->month == Carbon::now()->month) {
                    $policy = PolicySort::where('id', $req->policy_id)
                        ->where('benefit_colective', 'Si')
                        ->first();
                } else {
                    $policy = null;
                }


                if ($policy) {
                    return response()->json([
                        'benefit_colective' => true,
                        'message' => 'Persona trabajadora asegurada, póliza con beneficio de Colectividad. Ingrese los datos'
                    ]);

                } else {

                    //Si no es modalidad RT de aseguramiento general
                    $policyModality = PolicySort::find($req->policy_id);

                    if ($policyModality->work_modality_id != 1) {

                        return response()->json([
                            'RT' => true,
                            'message' => 'Caso asegurado por modalidad de Riesgos de trabjado (RT)'
                        ]);

                    } else {

                        return response()->json([
                            'success' => false,
                            'message' => 'La póliza no tiene una planilla reportada'
                        ], 200);
                    }
                }
            }

            //Buscamos la planilla tomador mediante su actividad
            $lastReport = PolicySpreadsheet::where('activity_id', $activityReport->id)
                ->first();


            if (!$lastReport) {

                //Si el afiliado no se encuentra en la planilla pero la poliza tiene beneficio de colectividad
                if ($dateAdmission->month == Carbon::now()->month) {
                    $policy = PolicySort::where('id', $req->policy_id)
                        ->where('benefit_colective', 'Si')
                        ->first();
                } else {
                    $policy = null;
                }

                if ($policy) {

                    return response()->json([
                        'benefit_colective' => true,
                        'message' => 'Persona trabajadora asegurada, póliza con beneficio de Colectividad. Ingrese los datos'
                    ]);

                    //Si no esta en la planilla ni la poliza tiene beneficio de colectividad
                } else {

                    $policyModality = PolicySort::find($req->policy_id);

                    if ($policyModality->work_modality_id != 1) {

                        return response()->json([
                            'RT' => true,
                            'message' => 'Caso asegurado por modalidad de Riesgos de trabjado (RT)'
                        ]);

                    } else {
                        return response()->json([
                            'success' => false,
                            'message' => 'La póliza no tiene una planilla reportada'
                        ], 200);
                    }
                }
            }


            if ($req->has('identification') && $req->has('type_identification')) {
                $affiliateReport = PolicySpreadsheetAffiliate::leftJoin('affiliates', 'policy_spreadsheet_affiliates.affiliate_id', '=', 'affiliates.id')
                    ->where('policy_spreadsheet_affiliates.identification_number', $req->identification)
                    ->where('policy_spreadsheet_affiliates.id_type', $req->type_identification)
                    ->where('policy_spreadsheet_affiliates.policy_spreadsheet_id', $lastReport->id)
                    ->select('policy_spreadsheet_affiliates.*', 'affiliates.cellphone')
                    ->first();

                $gisSort = GisSort::where('type_identification_affiliate', $req->type_identification)
                    ->where('number_identification_affiliate', $req->identification)
                    ->whereRaw("DATE(created_at) = CURRENT_DATE()")
                    ->first();

                if ($gisSort) {
                    throw new Exception("El afiliado ya cuenta con un siniestro registrado el día de hoy. ");
                }
            }
            if ($req->has('full_name')) {

                $fullName = strtolower($req->full_name);
                $affiliateReport = PolicySpreadsheetAffiliate::whereRaw('LOWER(full_name) LIKE ?', ['%' . $fullName . '%'])
                    ->where('policy_spreadsheet_id', $lastReport->id)
                    ->get();

                $gisSort = GisSort::select('gis_sort.*')
                    ->leftJoin('activities', 'activities.id', '=', 'gis_sort.activity_id')
                    ->leftJoin('policy_sorts', 'policy_sorts.activity_id', '=', 'activities.parent_id')
                    ->whereRaw('LOWER(gis_sort.name_affiliate) LIKE ?', ['%' . $fullName . '%'])
                    ->where('policy_sorts.id', $req->policy_id)
                    ->whereRaw('DATE(gis_sort.created_at) = CURRENT_DATE()')
                    ->first();

                if ($gisSort) {
                    throw new Exception("El afiliado ya cuenta con un siniestro registrado el día de hoy.");
                }
            }


            if (!empty($affiliateReport)) {
                return response()->json([
                    'affiliateReport' => $affiliateReport
                ]);
            } else {

                //Si el afiliado no se encuentra en la planilla pero la poliza tiene beneficio de colectividad
                if ($dateAdmission->month == Carbon::now()->month) {
                    $policy = PolicySort::where('id', $req->policy_id)
                        ->where('benefit_colective', 'Si')
                        ->first();
                } else {
                    $policy = null;
                }

                if ($policy) {

                    return response()->json([
                        'benefit_colective' => true,
                        'message' => 'Persona trabajadora asegurada, póliza con beneficio de Colectividad. Ingrese los datos'
                    ]);


                } else {

                    $policyModality = PolicySort::find($req->policy_id);

                    if ($policyModality->work_modality_id != 1) {

                        return response()->json([
                            'RT' => true,
                            'message' => 'Caso asegurado por modalidad de Riesgos de trabjado (RT)'
                        ]);

                    } else {
                        return response()->json([
                            'message' => 'No se encontraron datos del afiliado'
                        ]);
                    }
                }
            }
        } catch (Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Ocurrió un error en el proceso. ' . $e->getMessage(),
                'error' => $e->getMessage(),
                'line' => $e->getLine(),
                'file' => $e->getFile()
            ], 500);
        }
    }

    //Guardar los datos reporte accidente formulario tomador
    public function saveDataAffiliate(Request $req, $cpath)
    {
        // Guardar datos en la sesión
        //Buscamos la poliza por su id
        $policy = PolicySort::find($req->policy_id);

        //Buscamos la ultima actividad del reporte planilla tomador mediante la poliza
        $activityReport = Activity::where('parent_id', $policy->activity_id)
            ->where('service_id', Service::SERVICE_REPORT_TAKEN_FORM_MNK)
            ->latest()
            ->first();

        //miramos si se debe incluir la persona en una planilla temporal
        $temporal = 0;

        //si no hay planilla reportada le creamos uan temporal
        if (!$activityReport) {
            $temporal = 1;
        }

        //Buscamos la planilla tomador mediante su actividad
        $lastReport = null;

        if ($activityReport) {
            $lastReport = PolicySpreadsheet::where('activity_id', $activityReport->id)
                ->first();
        }

        //si no hay planilla reportada le creamos uan temporal
        if (!$lastReport) {
            $temporal = 1;
        }

        session([
            'benefit_colective' => $req->benefit_colective,
            'insured_case' => $req->insured_case,
            'affiliate_id' => $req->affiliate_id,
            'policy_id' => $req->policy_id,
            'type_identification_affiliate' => $req->type_identification_affiliate,
            'number_identification_affiliate' => $req->number_identification_affiliate,
            'name_affiliate' => $req->name_affiliate,
            'nationality_affiliate' => $req->nationality_affiliate,
            'cellphone_affiliate' => $req->cellphone_affiliate,
            'email_affiliate' => $req->email_affiliate,
            'spreadsheet_id' => $lastReport ? $lastReport->id : null,
            'temporal' => $temporal,
            'model' => $req->model,
            'responsible_id' => $req->responsible_id,
            'date_admission' => $req->date_admission
        ]);

        // Redireccionar a la nueva vista sin pasar parámetros en la URL
        return response()->json([
            'redirect_url' => secure_url('/servicio/gis_sort/tomador_accidente' . '/' . $req->policy_id),
            'temporal' => $temporal
        ]);
    }

    public function form(Request $req, $cpath, $id)
    {
        //Array de diagnosticos
        $caseDx = collect();

        //cliente 
        $client = Client::where('path', $cpath)
            ->firstOrFail();

        //actividad de GIS
        $activityGis = Activity::where('client_id', $client->id)->where('id', $id)
            ->firstOrFail();

        $state_action_gis = "";

        //Buscamos la ultima acción
        $state = $activityGis->state_id;

        // Verificamos el estado con un switch
        switch (intVal($state)) {
            case StateGis::CASO_REPORTADO_VALIDACION_ORIGEN:
                $state_action_gis = "determinacion_origen";
                break;

            case StateGis::CASO_CERRADO:
                $state_action_gis = "caso_cerrado";
                break;

            case StateGis::CASO_EN_TRAMITE_JUDICIAL:
                $state_action_gis = "tramite_controversia";
                break;

            case StateGis::CASO_ACEPTADO_EN_SEGUIMIENTO_REHABILITACION:
                $state_action_gis = "faseDos";
                break;

            //Ejecutamos la acción REPORTAR CALIFICACIÓN PCG
            case StateGis::CASO_EN_CALIFICACION_PCG_SIN_ALTA_MEDICA:
            case StateGis::CASO_CON_ALTA_MEDICA_PENDIENTE_PCG:
                $state_action_gis = "faseTres";
                break;

            case StateGis::CASO_PCG_CALIFICADO_PENDIENTE_AUDITORIA_MEDICA:
                $state_action_gis = "auditoria_calificacion_pcg";
                break;

            default:
                $state_action_gis = 'Sin estado';
                break;
        }

        //Array de dignsoticos asociados a GIS
        $caseDx = GisDiagnostic::where('gis_id', $activityGis->gis_sort->id)
            ->get();

        $activity = Activity::with([
            'parent_activity.policy_sort.policy_contacts',
            'affiliate.policy_spreadsheet_affiliate',
            'state',
            'medical_services_sort',
            'gis_sort',
            'activity_documents',
            'activity_actions' => function ($query) {
                $query->where('action_id', ActionGisSort::SOLICITAR_EMISION_CASO_FIRMA_FISICA);
            }
        ])
            ->where('service_id', Service::SERVICE_GIS_SORT_MNK)
            ->where('client_id', $client->id)
            ->where('id', $id)->first();

        $caseDx = GisDiagnostic::where('gis_id', $activity->gis_sort->id)->get();
        //traer la fecha del accidente y buscar las ultimas 3 planillas antes de la fecha
        $dateAccident = Carbon::parse($activityGis->gis_sort->date_accident)->endOfDay();
        $lastThreeMonths = PolicySpreadsheetAffiliate::query()
            ->where('affiliate_id', $activity->affiliate_id)
            ->where('created_at', '<=', $dateAccident)
            ->orderBy('id', 'desc')
            ->limit(3)
            ->select('policy_spreadsheet_id', 'monthly_salary', 'days', 'created_at')
            ->get();


        $reportTypes = ['Accidente', 'Enfermedad'];


        $policy_contacts = "";

        $activityGis = Activity::find($id);

        $model = $activityGis->gis_sort->model;

        if ($model) {
            if ($model == 'PolicyContact') {
                $policy_contacts = PolicyContact::where('id', $activityGis->gis_sort->responsible_id)->get()->map(function ($item) {
                    return (object) [
                        'type_identification' => $item->type_identification,
                        'number_identify_responsible' => $item->number_identify_responsible,
                        'name_responsible' => $item->name_responsible,
                        'phone_responsible' => $item->phone_responsible,
                        'cellphone_responsible' => $item->cellphone_responsible,
                        'unique_code' => $item->unique_code,
                        'ocupation_responsible' => $item->ocupation_responsible,
                        'email_responsible' => $item->email_responsible
                    ];
                });
            }

            if ($model == 'User') {
                $policy_contacts = User::where('id', $activityGis->gis_sort->responsible_id)->get()->map(function ($item) {
                    return (object) [
                        'type_identification' => $item->doc_type,
                        'number_identify_responsible' => $item->identification_number,
                        'name_responsible' => $item->full_name,
                        'phone_responsible' => $item->phone,
                        'cellphone_responsible' => $item->phone,
                        'unique_code' => $item->unique_code,
                        'ocupation_responsible' => 'Tomador autorizado',
                        'email_responsible' => $item->email
                    ];
                });
            }
        } else {
            $policy_contacts = $activity->parent_activity->policy_sort->policy_contacts;
            if (count($policy_contacts) == 0) {
                $policy_contacts = [PolicyContact::first()];
            }
        }

        //Botones a deshabilitar Nodo Soportes
        switch ($activity->state_id) {
            case StateGis::CASO_AVISADO_PRENDIENTE_REPORTE_FORMAL:
            case StateGis::PENDIENTE_ENTREGA_INFORMACION_EXTRA:
                $disableUploads = false;
                break;

            default:
                $disableUploads = true;
        }
        //Guardamos los documentos realacionado a la actividad (Soportes)
        $activity_documents = ActivityDocument::where('activity_id', $activity->id)->get();
        $documents = [];
        foreach ($activity_documents as $doc) {
            $documents[$doc->document_id] = $doc;
        }

        $activityPrestaciones = Activity::where('parent_id', $activityGis->id)->where('service_id', Service::SERVICE_MEDICAL_SERVICES_SORT_MNK)
            ->get();

        // Obtener los IDs de $activityPrestaciones
        $parentIds = $activityPrestaciones->pluck('id');

        // Actividad ce incapacidad tempotal
        $activityEconomicIt = Activity::whereIn('parent_id', $parentIds)->where('service_id', Service::SERVICE_PE_IT_SORT_MNK)->with(['service', 'peItSort', 'peItSort.inabilitySort'])->first();

        $subrogacion = GisSubrogacion::where('gis_id', $activityGis->gis_sort->id)->first();


        $activityvalue = Activity::with([
            'affiliate',
            'medical_services_sort',
            'medical_services_sort.followUps' => function ($query) {
                $query->orderBy('follow_up_number');
            },
            'medical_services_sort.followUps.companions',
            'medical_services_sort.followUps.diagnostics',
            'medical_services_sort.followUps.diagnosticsImages',
            'medical_services_sort.followUps.specialists',
            'medical_services_sort.followUps.medicalPrescriptions',
            'medical_services_sort.followUps.controlledMedications',
        ])->where('affiliate_id', $activity->affiliate_id)
        ->where('parent_id', $activityGis->id)
          ->get();



        $gisTotals = $this->incapacidades($activityGis);

        // Obtener fecha de calificación a partir de las acciones
        $date_calification = ActivityAction::where('activity_id', $activity->id)
        ->whereIn('action_id', [ActionGisSort::REPORTAR_DESAPARICION_AFILIADO, ActionGisSort::REPORTAR_FORMATO_FORMAL_CASO])
            ->min('created_at');
        if ($date_calification) {
            // Parseas con Carbon y formateas a Y-m-d
            $date_calification = Carbon::parse($date_calification)
                ->format('Y-m-d');
        }

        $accidentType = AccidentType::all();
        $agetsGi = AgetsGi::all();
        $occupationalDisease = OccupationalDisease::all();

        return view('services.gis.form.form', [
            'activity' => $activity,
            'activity_diagnostic' => $activityvalue,
            'id' => $id,
            'caseDx' => $caseDx,
            'gis' => $activity->gis_sort,
            'state' => $state,
            'planillas' => $lastThreeMonths,
            'policy_contacts' => $policy_contacts,
            'reportTypes' => $reportTypes,
            'fieldData' => $activity->gis_sort->gis_body_parts,
            'validForm' => $state_action_gis,
            'disableUploads' => $disableUploads,
            'documents' => $documents,
            'prestaciones_medicas' => $activityPrestaciones,
            'activityEconomicIt' => $activityEconomicIt,
            'subrogacion' => $subrogacion ?? null,
            'gis_total' => $gisTotals ?? null,
            'date_calification' => $date_calification,
            'accidentType' => $accidentType,
            'agetsGi' => $agetsGi,
            'occupationalDisease' => $occupationalDisease

        ]);
    }

    public function pdf(Request $req, $cpath, $id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();
        $activityPolicy = $activity->parent_activity;
        $activityPeIpSort = Activity::where('client_id', $client->id)
            ->where('parent_id', $activity->id)
            ->where('service_id', Service::SERVICE_PE_IP_SORT_MNK)
            ->firstOrFail();
        $pdf = PDF::loadView(
            'services.gis.docs.dictum_receipt_pcg_pdf',
            [
                'activityGis' => $activity,
                'activityPolicy' => $activityPolicy,
                'activityPeIpSort' => $activityPeIpSort,
                'formatDate' => Carbon::now()->formatLocalized('%e de %B del %Y')
            ]
        );

        return $pdf->stream('preview.pdf');
    }

    public function incapacidades($activityGis)
    {
        return GisSort::query()
            // 1) Filtrar solo el GIS que nos interesa
            ->where('gis_sort.activity_id', $activityGis->id)

            // 2) Traer la actividad padre (GIS)
            ->join('activities as act', 'act.id', '=', 'gis_sort.activity_id')

            // 3) Prestaciones médicas (service_id = 83)
            ->leftJoin('activities as medical', function ($join) {
                $join->on('medical.parent_id', '=', 'act.id')
                    ->where('medical.service_id', Service::SERVICE_MEDICAL_SERVICES_SORT_MNK);
            })

            // 4) Incapacidades temporales (state_id = 74) hijas de cada prestación médica
            ->leftJoin('activities as temp_act', function ($join) {
                $join->on('temp_act.parent_id', '=', 'medical.id')
                    ->where('temp_act.state_id', StatePeItSort::PAGO_SIT_REALIZADO);
            })
            ->leftJoin('pe_it_sorts as its', 'its.activity_id', '=', 'temp_act.id')
            ->leftJoin('peit_inability_sorts as peit', 'peit.pe_it_sort_id', '=', 'its.id')

            // 5) Incapacidades permanentes ligadas al GIS (service_id = 86, state_id = 78)
            ->leftJoin('activities as perm', function ($join) {
                $join->on('perm.parent_id', '=', 'act.id')
                    ->where('perm.service_id', Service::SERVICE_PE_IP_SORT_MNK)
                    ->where('perm.state_id', StatePeIpSort::IP_APROBADA_PAGOS);
            })
            ->leftJoin('peip_sorts as peip', 'peip.activity_id', '=', 'perm.id')

            // 6) Reconocimientos (opcional)
            ->leftJoin('activities as reco', function ($join) {
                $join->on('reco.parent_id', '=', 'act.id')
                    ->where('reco.service_id', Service::SERVICE_PE_RECOGNITION_EXPENSES_MNK)
                    ->where('reco.state_id', StatePeexpenserecognition::RECONOCIMIENTO_PAGADO);
            })
            ->leftJoin('pe_recognition_expenses as pe_rec', 'pe_rec.activity_id', '=', 'reco.id')

            // 7) Seleccionar y agregar
            ->selectRaw(implode(', ', [
                'COALESCE(SUM(peit.amount_pay), 0)                             AS total_amount_pay',
                'COUNT(DISTINCT medical.id)                                   AS medical_count',
                'COUNT(DISTINCT temp_act.id)                                  AS temporary_count',
                'COUNT(DISTINCT perm.id)                                      AS permanent_count',
                'COALESCE(SUM( DISTINCT pe_rec.invoices_expense_recognition), 0)        AS invoices_expense_recognition',
                // Fecha de primer pago permanente
                'MIN(peip.criteria_data_resolution)                                 AS permanent_start_date',
                // Monto mensual de incapacidad permanente (asumido constante)
                'MAX(peip.payinfo_monthy_amount_pay)                          AS monthly_amount',
                // Meses transcurridos desde el inicio hasta hoy (incluye mes 1)
                '(TIMESTAMPDIFF(MONTH, MIN(peip.criteria_data_resolution), CURDATE()) + 1) AS months_count',
                // Total pagado permanente acumulado
                '(MAX(peip.payinfo_monthy_amount_pay) * (TIMESTAMPDIFF(MONTH, MIN(peip.criteria_data_resolution), CURDATE()) + 1)) AS total_permanent_paid',
            ]))
            // Agrupar por el GIS para que las agregaciones den una sola fila
            ->groupBy('gis_sort.id')
            ->first();

    }

    public function save(Request $request, $cpath, $id)
    {

        try {
            // Buscar el cliente
            $client = Client::where('path', $cpath)->firstOrFail();

            // Inicializar variables
            $activityGis = Activity::where('id', $request->id)->firstOrFail();

            if ($request->input('subrogacion_input') == '1') {
                $this->saveSubrogacion($request, $activityGis);
            }

            // Seleccionar la acción según el estado de la actividad
            switch ($activityGis->state_id) {
                case StateGis::CASO_REPORTADO_VALIDACION_ORIGEN:
                    // Guardar determinación de origen y acción reportar origen
                    $this->actionReportOrigin($request, $activityGis);
                    $response = 'Determinación de origen guardada y origen reportado con éxito.';
                    break;

                case StateGis::CASO_ACEPTADO_EN_SEGUIMIENTO_REHABILITACION:
                    // Guardar fase 2 GIS y acciones de auditoría y solicitud de calificación PCG
                    $this->fase_dos_valid($request, $cpath, $activityGis);
                    $response = 'Fase 2 GIS guardada y seguimiento de auditoría médica reportado.';
                    break;

                case StateGis::CASO_CON_ALTA_MEDICA_PENDIENTE_PCG:
                case StateGis::CASO_EN_CALIFICACION_PCG_SIN_ALTA_MEDICA:
                    // Guardamos la calificación PCG de fase 3
                    $this->saveGisPhase3($request, $activityGis);
                    $response = 'Calificación PCG reportada exitosamente.';
                    break;

                case StateGis::CASO_PCG_CALIFICADO_PENDIENTE_AUDITORIA_MEDICA:
                    $this->saveGisPhase3QualificationAudit($request, $activityGis, $cpath);
                    $response = 'Caso calificado pendiente de auditoria médica.';
                    break;


                case StateGis::PENDIENTE_ENTREGA_INFORMACION_EXTRA:
                    // Cambiar el estado de la actividad
                    $this->uploadSupportsGisForm($activityGis, $request);
                    $response = 'Información adicional subida exitosamente.';
                    break;

                case StateGis::CASO_EN_TRAMITE_JUDICIAL:
                    // Cambiar el estado de la actividad
                    $this->saveFirstLegalProceeding($request, $activityGis, $client);
                    $response = 'Información adicional subida exitosamente.';
                    break;

                case 'finalizado':
                    $response = 'La actividad ha sido finalizada.';
                    break;

                default:
                    $response = 'Estado no reconocido.';
                    break;
            }


            if (
                $request->pps_medical_benefits != '' ||
                $request->pps_temporary_disability != '' ||
                $request->pps_partial_permanent_disability != '' ||
                $request->pps_minor_permanent_disability != '' ||
                $request->pps_total_permanent_disability != '' ||
                $request->pps_great_disability_permanent_disability != '' ||
                $request->pps_death_or_disappearance != ''
            ) {

                $provisionForClaim = str_replace(['₡', '$'], ['', ''], $request->provision_for_claim);

                $gis = $activityGis->gis_sort;
                $gis->update([
                    'actuarial_severity' => str_replace(['₡', '$'], ['', ''], $request->por_actuarial_severity),
                    'pps_medical_benefits' => str_replace(['₡', '$'], ['', ''], $request->pps_medical_benefits),
                    'pps_temporary_disability' => str_replace(['₡', '$'], ['', ''], $request->pps_temporary_disability),
                    'pps_partial_permanent_disability' => str_replace(['₡', '$'], ['', ''], $request->pps_partial_permanent_disability),
                    'pps_minor_permanent_disability' => str_replace(['₡', '$'], ['', ''], $request->pps_minor_permanent_disability),
                    'pps_total_permanent_disability' => str_replace(['₡', '$'], ['', ''], $request->pps_total_permanent_disability),
                    'pps_great_disability_permanent_disability' => str_replace(['₡', '$'], ['', ''], $request->pps_great_disability_permanent_disability),
                    'pps_death_or_disappearance' => str_replace(['₡', '$'], ['', ''], $request->pps_death_or_disappearance),
                    'pps_expenses_recognition' => str_replace(['₡', '$'], ['', ''], $request->pps_expenses_recognition),
                    'provision_for_claim' => $provisionForClaim
                ]);


                // Reporte contable 050
                $accountingEntryController = new AccountingEntryController();
                $accountingEntryController->reportAccountCase050($activityGis->id, $activityGis->parent_id, $provisionForClaim);

                if (config('app.env') != 'prod') {
                    //Ejecutamos el asiento contable 154 de reaseguro para el valor del 050.
                    $accountingEntryController->reportAccountCase154($activityGis->id, $activityGis->parent_id, $provisionForClaim);
                }
            }

            return response()->json(['message' => $response, 'status' => 'success'], 200);
        } catch (Exception $e) {
            // Log de error si es necesario
            // Respuesta de error
            return response()->json(['message' => 'Ocurrió un error al procesar la actividad.', 'error' => $e->getMessage(), 'status' => 'error'], 500);
        }
    }

    public function saveSubrogacion(Request $request, $activityGis)
    {

        DB::beginTransaction();

        try {

            $gis = $activityGis->gis_sort;
            //Ejecutamos la acción Soliictar calificación PCG
            $description = "Aprobar notificación";

            ActionController::create(
                $activityGis->id,
                ActionGisSort::APROBAR_NOTIFICACION,
                $description
            );

            GisSubrogacion::create([
                'incapacidades_temporales' => $request->incapacidades_temporales,
                'incapacidades_permanentes' => $request->incapacidades_permanentes,
                'reconocimiento_gastos' => $request->reconocimiento_gastos,
                'prestaciones_medicas' => $request->prestaciones_medicas,
                'total_costos' => $request->total_costos,
                'demandado_nombre' => $request->demandado_nombre,
                'demandado_tipo_identificacion' => $request->demandado_tipo_identificacion,
                'demandado_num_identificacion' => $request->demandado_num_identificacion,
                'demandado_telefono' => $request->demandado_telefono,
                'demandado_email' => $request->demandado_email,
                'demandado_direccion' => $request->demandado_direccion,
                'rep_legal_nombre' => $request->rep_legal_nombre,
                'rep_legal_cedula' => $request->rep_legal_cedula,
                'expediente_judicial' => $request->expediente_judicial,
                'juzgado_caso' => $request->juzgado_caso,
                'numero_proceso' => $request->numero_proceso,
                'fecha_inicio_juicio' => $request->fecha_inicio_juicio,
                'estado_proceso' => $request->estado_proceso,
                'gis_id' => $gis->id,
            ]);

            DB::commit();

        } catch (Exception $e) {

            DB::rollback();

            throw new Exception("Error al guardar registros de subrogacion: " . $e->getMessage());

        }

    }

    //MS-2140
    public function saveFirstLegalProceeding($request, $activityGis, $client)
    {

        DB::beginTransaction();
        try {

            $path = $this->storeActivityDocument($request, 'mnk_support_document', 284, $activityGis->id);

            $gisSort = $activityGis->gis_sort;

            $gisSort->update([
                'name_comite' => $request->name_comite,
                'controversy_result' => intval($request->controversy_result),
                'mnk_support_document' => $path,
                'mnk_observations' => $request->mnk_observations,
                'controversy_judicial_instance' => intval($request->controversy_judicial_instance)
            ]);

            DB::commit();
        } catch (Exception $e) {
            DB::rollback();
            throw new Exception("Error al guardar registros de caso en trámite judicial " . $e->getMessage());
        }
    }

    //Soliictar calificación PCG MS-1788
    public function requestPcgQualification($activityGis)
    {

        //Iniciamos la transacción
        DB::beginTransaction();

        try {

            //Ejecutamos la acción Soliictar calificación PCG
            $description = "Se ejecuto la acción Soliictar calificación PCG";
            ActionController::create(
                $activityGis->id,
                ActionGisSort::SOLICITAR_CALIFICACION_PCG,
                $description
            );

            DB::commit();

            return response()->json([
                'message' => 'Se ejecuto la acción Soliictar calificación PCG'
            ]);
        } catch (Exception $e) {
            DB::rollback();
            return response()->json([
                'status' => 'error',
                'message' => 'Ocurrió un error en el proceso'
            ], 500);
        }
    }


    private function fase_dos_valid($request, $cpath, $activityGis)
    {
        #1 SEGUIMIENTO PERIÓDICO
        $this->save_evaluacion_inicial($request, $activityGis);
        #2
        $validAction = $this->save_solicitud_autorizacion($request, $activityGis);
        // #3
        $validAction = $this->save_seguimiento_plan_tratamiento($request, $activityGis, $validAction);
        // #4
        $this->save_seguimiento_auditoría_del_siniestro($request, $activityGis);

        // # VALORACIÓN MÉDICO LABORAL
        $this->save_valoracion_medico_laboral($request, $activityGis);

        // #  REHABILITACION
        $validAction = $this->save_rehabilitacion($request, $activityGis, $validAction);

        // #  REINCORPORACIÓN
        $validAction = $this->save_reincorporación($request, $activityGis, $validAction);

        switch ($validAction) {
            case 'SOLICITAR_CALIFICACIÓN_PCG':
                #  SOLICITAR CALIFICACIÓN PCG
                break;
            case 'REPORTAR_CIERRE_DE_CASO_POR_ABANDONO_DE_TRATAMIENTO':
                #  REPORTAR CIERRE DE CASO POR ABANDONO DE TRATAMIENTO
                $this->ActionCloseCaseDueToTreatmentAbandonment($request, $activityGis, $cpath);
                break;
            case 'REPORTAR_ALTA_MEDICA':
                #  REPORTAR ALTA MEDICA
                $this->reportMedicalDischarge($activityGis->id);
                break;

            default:
                //
                break;
        }
    }

    //GUARDADO FORM EVALUACIÓN INICIAL
    private function save_evaluacion_inicial($request, $activityGis)
    {
        DB::beginTransaction();
        try {
            $gis = $activityGis->gis_sort;
            $data = $request->only([
                'functional_prognosis',
                'rehabilitation_detail',
                'event_type_follow',
                'medical_center',
                'treatment_end_date',
                'expected_disability_days',
                'actuarial_severity_evaluacion',
                'diagnosis_description',
                'clinical_summary',
                'current_status',
                'recovery_possibility',
                'patient_prognosis',
                'pharmaceutical',
                'pharmaceutical_date',
                'treatment_purpose',
                'surgical',
                'surgical_date',
                'fisica_option',
                'fisica_date',
                'occupational_therapy_option',
                'occupational_therapy_date',
                'fonoaudiology_option',
                'fonoaudiology_date',
                'other_OptionEvalucion',
                'other_date_evaluacion',
                'bmi_details',
                'ccss_details',
                'medical_center_description',
                'follow_up_date',
                'cause_for_closure'
            ]);

            $gis->fill($data)->save();


            DB::commit();
            return $gis;
        } catch (Exception $e) {
            DB::rollback();
            throw new Exception($e->getMessage());
        }
    }

    //GUARDADO FORM SOLICITUD DE AUTORIZACIONES 
    private function save_solicitud_autorizacion($request, $activityGis)
    {
        $calificacion_pcg = '';
        DB::beginTransaction();
        try {
            $gis = $activityGis->gis_sort;

            // Eliminar registros existentes relacionados con la solicitud de autorización
            GisRequestAuthorization::where('gis_id', $gis->id)->delete();


            // Verificar si hay autorizaciones en la solicitud
            if (!empty($request->solicitud_autorizaciones)) {
                foreach ($request->solicitud_autorizaciones as $item) {
                    GisRequestAuthorization::create([
                        'authorization_request_autorizacion' => $item['authorization_request_autorizacion'],
                        'request_description_autorizacion' => $item['request_description_autorizacion'],
                        'request_reason_autorizacion' => $item['request_reason_autorizacion'],
                        'effective_notification_date_autorizacion' => $item['effective_notification_date_autorizacion'],
                        'gis_id' => $gis->id,
                    ]);


                    $calificacion_pcg = $item['request_reason_autorizacion'] == 'calificacion_pcg' ? 'SOLICITAR_CALIFICACIÓN_PCG' : '';
                }
            }

            DB::commit();
            return $calificacion_pcg;
        } catch (Exception $e) {
            DB::rollback();
            throw new Exception("Error al guardar la solicitud de autorización: " . $e->getMessage());
        }
    }

    //GUARDADO FORM SEGUIMIENTO PLAN DE TRATAMIENTO
    private function save_seguimiento_plan_tratamiento($request, $activityGis, $contexto)
    {
        $case_closure_abandonment = '';
        DB::beginTransaction();
        try {
            $gis = $activityGis->gis_sort;

            // Eliminar registros existentes relacionados con la solicitud de autorización
            GisSeguimientoPlanTratamiento::where('gis_id', $gis->id)->delete();


            // Verificar si hay autorizaciones en la solicitud
            if (!empty($request->plan_tratamiento)) {
                foreach ($request->plan_tratamiento as $item) {
                    GisSeguimientoPlanTratamiento::create([
                        'authorization_date_plan' => $item['authorization_date_plan'],
                        'provider_name' => $item['provider_name'],
                        'doctor_name' => $item['doctor_name'],
                        'specialty' => $item['specialty'],
                        'result_description' => $item['result_description'],
                        'call_date' => $item['call_date'],
                        'call_id' => $item['call_id'],
                        'observations' => $item['observations'],
                        'case_closure_abandonment' => $item['case_closure_abandonment'],
                        'gis_id' => $gis->id,
                    ]);

                    if ($item['case_closure_abandonment'] == 'SI') {
                        $case_closure_abandonment = 'REPORTAR_CIERRE_DE_CASO_POR_ABANDONO_DE_TRATAMIENTO';
                    }
                }


                $case_closure_abandonment = !empty($contexto)
                    ? $contexto
                    : $case_closure_abandonment;
            }

            DB::commit();
            return $case_closure_abandonment;
        } catch (Exception $e) {
            DB::rollback();
            throw new Exception("Error al guardar la solicitud de autorización: " . $e->getMessage());
        }
    }

    //GUARDADO FORM SEGUIMIENTO AUDITORÍA DEL SINIESTRO
    private function save_seguimiento_auditoría_del_siniestro($request, $activityGis)
    {

        DB::beginTransaction();
        try {
            $gis = $activityGis->gis_sort;

            $gis->fill([
                'authorization_date' => $request->authorization_date,
                'follow_up_type' => $request->follow_up_type,
                'result_description_auditoria' => $request->result_description_auditoria,
            ]);

            $gis->save();

            if ($activityGis->state_id == StateGis::CASO_ACEPTADO_EN_SEGUIMIENTO_REHABILITACION) {
                # REPORTAR SEGUIMIENTO AUDITORIA MEDICA
                $this->actionMedicalAuditTracking($activityGis);
            }

            DB::commit();
            return $gis;
        } catch (Exception $e) {
            DB::rollback();
            throw new Exception($e->getMessage());
        }
    }

    private function save_valoracion_medico_laboral($request, $activityGis)
    {

        DB::beginTransaction();
        try {
            $gis = $activityGis->gis_sort;

            // Eliminar registros existentes relacionados con la solicitud de autorización
            GisValoracionMedicoLaboral::where('gis_id', $gis->id)->delete();


            // Verificar si hay autorizaciones en la solicitud
            if (!empty($request->solicitud_autorizaciones)) {
                foreach ($request->valoracion_medico_laboral as $item) {
                    GisValoracionMedicoLaboral::create([
                        'service_status_valoracion' => $item['service_status_valoracion'],
                        'provider_name_valoracion' => $item['provider_name_valoracion'],
                        'authorization_request_date_valoracion' => $item['authorization_request_date_valoracion'],
                        'authorization_request_time_valoracion' => $item['authorization_request_time_valoracion'],
                        'plan_description_valoracion' => $item['plan_description_valoracion'],
                        'gis_id' => $gis->id,
                    ]);
                }
            }

            DB::commit();
        } catch (Exception $e) {
            DB::rollback();
            throw new Exception("Error al guardar la solicitud de autorización: " . $e->getMessage());
        }
    }

    //GUARDADO FORM REHABILITACION
    private function save_rehabilitacion($request, $activityGis, $contexto)
    {
        $medical_discharge_rehabilitacion = '';

        DB::beginTransaction();
        try {
            $gis = $activityGis->gis_sort;
            $data = $request->only([
                'has_closure_rehabilitacion',
                'closure_date_rehabilitacion',
                'requires_recommendations_rehabilitacion',
                'work_recommendations_rehabilitacion',
                'suspend_disability_rehabilitacion',
                'medical_discharge_rehabilitacion',
                'reason_medical_discharge_rehabilitacion'
            ]);


            $gis->fill($data)->save();

            $medical_discharge_rehabilitacion = !empty($contexto) ? $contexto
                : ($request->medical_discharge_rehabilitacion == 'SI'
                    ? 'REPORTAR_ALTA_MEDICA'
                    : 'SOLICITAR_CALIFICACIÓN_PCG');

            DB::commit();
            return $medical_discharge_rehabilitacion;
        } catch (Exception $e) {
            DB::rollback();
            throw new Exception($e->getMessage());
        }
    }

    //GUARDADO FORM REINCORPORACIÓN
    private function save_reincorporación($request, $activityGis, $contexto)
    {

        $medical_discharge_reintegration = '';
        DB::beginTransaction();
        try {
            $gis = $activityGis->gis_sort;
            $data = $request->only([
                'reintegration_date',
                'employer_email_date',
                'requires_support',
                'requires_visit',
                'medical_discharge_reintegration'
            ]);

            $gis->fill($data)->save();

            $medical_discharge_reintegration = !empty($contexto) ? $contexto
                : ($request->medical_discharge_reintegration == 'SI'
                    ? 'REPORTAR_ALTA_MEDICA'
                    : 'SOLICITAR_CALIFICACIÓN_PCG');

            DB::commit();
            return $medical_discharge_reintegration;
        } catch (Exception $e) {
            DB::rollback();
            throw new Exception($e->getMessage());
        }
    }

    public function resendEmailReportFolloup($req, $activityGis, $activity_service, $activityAction, $emails, $client_id, $files, $activityPolicy)
    {

        $subject = 'Reporte de seguimiento';

        // Lógica para el envío de correos
        if ($emails != null) {

            // Enviar el correo con los documentos adjuntos
            $mailSent = new SendDocumentDataBase(
                $emails,
                $subject,
                "<EMAIL>",
                "Poliza Emitida",
                [
                    "text" => 'Reportar seguimiento de evento',
                    "sender" => 'MNK seguros'
                ],
                "<EMAIL>",
                $files,
                "send_document_db",
                $client_id,
                $req->getHost(),
                $activityGis,
                $activityAction,
                $activity_service
            );
            $mailSent->sendMail();

        }
    }

    //Guardamos los campos del formulario fase 3 Auditoría calificación PCG
    public function saveGisPhase3QualificationAudit($request, $activityGis, $client)
    {

        $gisSort = $activityGis->gis_sort;

        //Guardamos los campos fase 3 Auditoría calificación PCG
        $gisSort->update([
            'agreement_members' => $request->agreement_members,
            'agreement_percentages' => $request->agreement_percentages,
            'audit_result_pcg' => $request->audit_result_pcg,
        ]);

        //si se aprobó la auditoria medica ejecutamos la acción REPORTAR AUDITORIA APROBACIÓN MS-1791
        if ($request->audit_result_pcg == "aprobado") {
            $this->ApproveAuditRejection($client, $activityGis->id);
        }

        if ($request->audit_result_pcg == "devuelto") {
            //Reportamos la acción rechazar auditoria medica con alta medica
            $this->ReportAuditRejection($client, $activityGis);
        }
    }

    //Función para guardar el siniestro
    public function saveAccident(Request $req, $cpath)
    {

        DB::beginTransaction();

        try {

            $client = Client::where('path', $cpath)->firstOrFail();

            //Buscamos la poliza por el id policiy 
            $policy = PolicySort::find($req->policy_id);

            //Buscamos la actividad de la poliza por el id policiy 
            $activityPolicy = Activity::find($policy->activity_id);


            $affiliateNew = null;

            $searchAffiliate = null;

            $policy_spreadsheet_affiliate = null;

            //Si el afiliado no existe lo creamos y si no existe se debe incluir en una planiulla temporal o no 
            if ($req->affiliate_id === "null") {

                //Buscamos el afiliado por si existe 
                $searchAffiliate = Affiliate::where('doc_type', $req->type_identification_affiliate)
                    ->where('doc_number', $req->number_identification_affiliate)
                    ->first();

                // Si no existe, lo creamos
                if (!$searchAffiliate) {
                    $affiliateNew = Affiliate::create([
                        'client_id' => $client->id,
                        'doc_type' => $req->type_identification_affiliate,
                        'doc_number' => $req->number_identification_affiliate,
                        'first_name' => $req->name_affiliate,
                        'cellphone' => $req->cellphone_affiliate,
                        'email' => $req->email_affiliate,
                        'country' => $req->nationality_affiliate
                    ]);
                }

                $spreadsheet_id = null;

                // si el afiliado no existe y tampoco existe planilla reportada le creamos la planilla temporal
                if ($req->temporal == 1) {

                    $reportTaker = $this->temporaryInclusion($activityPolicy, $client, $req->benfit_collective);

                    //Tomamos el id de la nueva planilla temporal creada
                    $spreadsheet_id = $reportTaker->id;

                    // Ejecutamos la acción 
                    $activityAction = ActionController::create(
                        $reportTaker->activity_id,
                        Action::INCLUSION_PERSONA_TRABAJADORA_DOS,
                        'Se ejecuto la acción inclusión persona trabajadora'
                    );
                } else {
                    //Tomamos el id de la planilla encontrada para la póliza
                    $spreadsheet_id = $req->spreadsheet_id;
                }

                $idAffiliate = null;

                if ($searchAffiliate) {
                    $idAffiliate = $searchAffiliate->id;
                } else {
                    $idAffiliate = $affiliateNew->id;
                }

                // Agregar otro campo solo si corresponde
                $temporal = 0;
                
                if ($req->benefit_colective === 'Si' && $req->insured_case === 'Si') {
                    $temporal = 1; 
                }

                $policy_spreadsheet_affiliate = PolicySpreadsheetAffiliate::create([
                    'policy_spreadsheet_id' => $spreadsheet_id,
                    'affiliate_id' => $idAffiliate,
                    'name' => $req->name_affiliate,
                    'id_type' => $req->type_identification_affiliate,
                    'nationality' => $req->nationality_affiliate,
                    'identification_number' => $req->number_identification_affiliate,
                    'email' => $req->email_affiliate,
                    'is_incluye' => 0,
                    'benefit_colective' => $req->benefit_colective ?? null,
                    'date_admission' => $req->date_admission,
                    'temporal' => $temporal
                ]);
            } else {
                $affiliateUpdate = Affiliate::where('id', $req->affiliate_id)->first();
                $affiliateUpdate->cellphone = $req->cellphone_affiliate;
                $affiliateUpdate->save();
            }

            //Creamos la actividad del servici GIS.
            $activityGis = new Activity();
            $activityGis->parent_id = $policy->activity_id;
            $activityGis->state_id = State::REGISTRADO;
            if ($req->affiliate_id === "null") {

                if ($searchAffiliate) {
                    $activityGis->affiliate_id = $searchAffiliate->id;
                } else {
                    $activityGis->affiliate_id = $affiliateNew->id;
                }
            } else {
                $activityGis->affiliate_id = $req->affiliate_id;
            }
            $activityGis->service_id = Service::SERVICE_GIS_SORT_MNK;
            $activityGis->user_id = auth()->user()->id;
            $activityGis->client_id = $client->id;
            $activityGis->save();

            $affiliateId = $activityGis->affiliate_id;
            $dateEventDay = $req->date_accident;

            // Contamos los avisos de siniestro que el asegurado ya ha realizado en la misma fecha
            $dateEventDayCount = GisSort::join('activities', 'gis_sort.activity_id', '=', 'activities.id')
                ->where('activities.affiliate_id', $affiliateId)
                ->where('activities.service_id', Service::SERVICE_GIS_SORT_MNK)
                ->whereDate('gis_sort.date_accident', $dateEventDay)
                ->count();

            // Validamos si ya existen dos avisos en el mismo día
            if ($dateEventDayCount >= 1) {
                return response()->json([
                    'dateEventDay' => true,
                    'message' => "El asegurado ya tiene 1 aviso de siniestro para el día $dateEventDay."
                ]);
            }

            // Creamos el servicio GIS con el reporte del accidente
            $attempts = 0;
            $maxAttempts = 5;
            $gis = null;
            //se hacen 5 intentos para guardar el siniestro
            do {
                try {
                    $gis = GisSort::create(array_merge(
                        $req->all(),
                        ['activity_id' => $activityGis->id]
                    ));
                    break;
                } catch (Exception $e) {
                    // Error por clave duplicada, intentamos nuevamente para solucionar el problema de race conditions
                    if ($e->getCode() == '23000') {
                        $attempts++;
                        usleep(100000);
                    } else {
                        throw $e;
                    }
                }
            } while ($attempts < $maxAttempts);

            if (!$gis) {
                throw new \Exception('No se pudo generar un siniestro con consecutivo único después de varios intentos.');
            }

            if ($policy_spreadsheet_affiliate) {
                $policy_spreadsheet_affiliate->gis_id = $gis->id;
                $policy_spreadsheet_affiliate->update();
            }

            $this->caseActionsReport($req, $client, $activityGis, $gis, $activityPolicy);


            if (app()->environment('prod')) {
                $emails = AppServiceProvider::$EMAILS_NOTIFICATIONS_MNK;
            } else {
                $emails = ['<EMAIL>', '<EMAIL>'];
            }
            $formattedConsecutive = $policy->formatNumberConsecutive();
            $affiliate = ucwords(mb_strtolower($activityPolicy->affiliate->first_name));
            $worker = isset($activityGis->affiliate->first_name) ? ucwords(mb_strtolower($activityGis->affiliate->first_name)) : '';
            $subject = "Nuevo Aviso de Riesgo Laboral - Póliza $formattedConsecutive";
            $text = "Recibido nuevo aviso de riesgo laboral. Póliza $formattedConsecutive, Tomador $affiliate. Persona trabajadora $worker";
            $mailSent = new SendDocumentDataBase(
                implode(',', $emails),
                "Nuevo Aviso de Riesgo Laboral - Póliza $formattedConsecutive",
                "<EMAIL>",
                "Reporte de siniestro - Caso # " . $activityGis->gis_sort->formatCaseNumber(),
                [
                    "text" => "Recibido nuevo aviso de riesgo laboral. Póliza $formattedConsecutive, Tomador $affiliate. Persona trabajadora $worker",
                    "sender" => 'MNK seguros'
                ],
                "<EMAIL>",
                [],
                "send_document_db",
                $client->id,
                $req->getHost(),
                $activityGis->id,
                1,
                88
            );


            // // Capturar el resultado del envío
            // $result = $mailSent->sendMail();

            // //Registramos los datos del correo enviado para la trazabilidad
            // $mailBoardController = new MailBoardController();
            // $mailBoardController->createRegisterMail(
            //     $activityGis->id,
            //     $activityGis->service->id,
            //     $activityPolicy->policy_sort->consecutive,
            //     'Tomador',
            //     $affiliate,
            //     $activityPolicy->affiliate->doc_number,
            //     $subject,
            //     $text,
            //     $emails,
            //     $result,
            //     null
            // );


            DB::commit();

            return response()->json([
                'activity' => $activityGis->id,
                'policy' => $policy->id,
                'proveedor_name' => optional($gis->provider())->name ?? '',
                'conditions' => $req->conditions,
                'tomador' => $activityPolicy->affiliate->id,
                'gis' => $gis->consecutive_gis
            ], 200);
        } catch (Exception $e) {
            DB::rollback();
            return response()->json([
                'status' => 'error',
                'message' => 'Ocurrió un error en el proceso',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    private function temporaryInclusion($activityPoliza, $client)
    {

        //creamos o devolvemos la actividad del servicio REPORTE PLANILLA TOMADOR
        $activityService = $this->updateCreateActivityService($activityPoliza->id, $client);

        //creamos o actualizamos el servicio REPORTE PLANILLA TOMADOR
        $policySpreadsheet = $this->updateCreatePolicySpreadsheet($activityService,null,'Inclusiones');

        return $policySpreadsheet;
    }

    //Creamos o devolvemos la actividad del servicio REPORTE PLANILLA TOMADOR
    public function updateCreateActivityService($policySortActivityId, $client)
    {
        //Buscamos la actividad por medio de la actividad de la poliza
        $activity_policy = Activity::find($policySortActivityId);

        // Busco si existe planilla en estado REGISTRADA y REPORTE PLANILLA - TOMADOR
        $template_activity = Activity::where('service_id', Service::SERVICE_REPORT_TAKEN_FORM_MNK)
            ->where('state_id', State::PLANILLA_REPORTADA)
            ->where('parent_id', $policySortActivityId)
            ->first();

        if ($template_activity) {
            return $template_activity;
        }

        //Si no existe actividad asociada creamos la actividad para el servicio Reporte Planilla tomador
        $new_template_activity = new Activity();
        $new_template_activity->client_id = $client->id;
        $new_template_activity->parent_id = $policySortActivityId;
        $new_template_activity->service_id = Service::SERVICE_REPORT_TAKEN_FORM_MNK;
        $new_template_activity->affiliate_id = $activity_policy->affiliate_id;
        $new_template_activity->user_id = Auth::id();
        $new_template_activity->state_id = State::REGISTRADO;
        $new_template_activity->save();

        return $new_template_activity;
    }

    //Creamos a actualizamos el SERVICIO REPORTE PLANILLA TOMADOR
    public function updateCreatePolicySpreadsheet($activityService, $path = null , $entryType = null)
    {
        // Busco si existen varias planillas ya registradas con ese Service plantilla
        $policySpreadsheet = PolicySpreadsheet::where('activity_id', $activityService->id)
            ->first();

        // Si existe registro PolicySpreadsheet asociado a la activity
        if ($policySpreadsheet) {
            // Elimino todos los registros asociados a la plantilla
            $policySpreadsheet->policy_spreadsheet_affiliate()->delete();

            // Actualizo el registro
            $policySpreadsheet->file = $path;

        } else {
            // Si no existe, crea una nueva PolicySpreadsheet
            $policySpreadsheet = new PolicySpreadsheet();
            $policySpreadsheet->activity_id = $activityService->id;
            $policySpreadsheet->file = $path;
        }

        $policySpreadsheet->entry_type = $entryType;
        $policySpreadsheet->save();

        // Retorno PolicySpreadsheet
        return $policySpreadsheet;
    }


    private function caseActionsReport($req, $client, $activityGis, $gis, $activityPolicy)
    {
        $action_Valid = $req->is_work_related === 'NO' ? 'REPORTAR_AVISO_CASO_NO_AMPARADO' : 'REPORTAR_AVISO_CASO';

        switch ($action_Valid) {

            case 'REPORTAR_AVISO_CASO':
                $this->reportar_aviso_caso($req, $client, $gis, $activityGis, $activityPolicy);
                break;
            case 'REPORTAR_AVISO_CASO_NO_AMPARADO':
                $this->reportar_aviso_caso_no_amparado($activityGis);
                break;

            default:
                break;
        }
    }

    private function reportar_aviso_caso($req, $client, $gis, $activityGis, $activityPolicy)
    {
        //Generamos la ACCIÓN REPORTAR AVISO DE SINIESTRO del SERVICIO GIS
        $description = "Se generó la acción reportar aviso del caso desde gis";
        $activityAction = ActionController::create($activityGis->id, ActionGisSort::REPORTAR_AVISO_CASO, $description);


        //Si la condicion es Vivo entonces creamos Servicio de prestaciones medicas
        if ($req->conditions === 'Vivo') {
            //Creamos la actividad del servicio de PRESTACIONES MEDICAS SORT RELACIONADO A GIS
            $activityMedical = new Activity();
            $activityMedical->parent_id = $gis->activity_id;
            $activityMedical->state_id = State::REGISTRADO;
            $activityMedical->affiliate_id = $activityGis->affiliate_id;
            $activityMedical->service_id = Service::SERVICE_MEDICAL_SERVICES_SORT_MNK;
            $activityMedical->user_id = auth()->user()->id;
            $activityMedical->client_id = $client->id;
            $activityMedical->save();

            //Creamos el servicio de PRESTACIONES MEDICAS SORT
            $serviceMedical = new MedicalServicesSort();
            $serviceMedical->activity_id = $activityMedical->id;
            $serviceMedical->save();

            //Generamos un usuario y contraseña en la tabla afiliados para el  afiliado reportado
            $user = $activityGis->affiliate->doc_number;

            $password = uniqid();
            $hashedPassword = Hash::make($password);

            // Actualizamos las credenciales del tomador
            Affiliate::where('id', $activityGis->affiliate_id)->update([
                "user" => $user,
                "password" => $hashedPassword
            ]);

            //Generamos las credenciales para ese tomador
            //Validamos si el usuario ya existe
            $userExist = User::where('username', $activityGis->affiliate->doc_number)
                ->first();
            $emailExist = User::where('email', $activityGis->affiliate->email)->first();

            if ($userExist) {
                $password = "******(tu contraseña actual)";
            } else {
                // Verificar si el correo ya existe y generar uno nuevo si es necesario
                $email = $activityGis->affiliate->email;

                if ($emailExist) {
                    // Generar un correo aleatorio basado en el existente
                    $uniqueSuffix = uniqid(); // Genera un ID único
                    $emailParts = explode('@', $email);
                    $email = $emailParts[0] . '+' . $uniqueSuffix . '@' . $emailParts[1];
                }
                $user_create = User::create([
                    "email" => $email,
                    "password" => $hashedPassword,
                    "affiliate_id" => $activityGis->affiliate->id,
                    "first_name" => $activityGis->affiliate->first_name,
                    "last_name" => $activityGis->affiliate->last_name ?? null,
                    "username" => trim($activityGis->affiliate->doc_number),
                    "active" => 1,
                    "area_id" => 51,
                    "identification_number" => trim($activityGis->affiliate->doc_number),
                ]);

                UserClient::create([
                    'user_id' => $user_create->id,
                    'client_id' => $client->id
                ]);
            }

            //Enviamos los documentos al tomador y al afiliado 
            $emails = [
                $activityPolicy->affiliate->email, // Tomador
                $activityGis->affiliate->email // Afiliado
            ];

            // Remover correos duplicados y vacíos (opcional)
            $emails = array_filter(array_unique($emails));

            // Si necesitas convertirlos a un string separado por comas
            $emailsString = implode(',', $emails);

            $emailTaker = $activityPolicy->affiliate->email;

            //Generar el document de envio para el tomador
            // GENERATE PDF
            $document = 'accepted_case_certificate';
            $path = "activity_action_document/{$document}_{$activityAction->id}.pdf";
            $pdf = PDF::loadView('services.gis.docs.accepted_case_certificate_pdf',
                [
                    'activityGis' => $activityGis,
                    'activityPolicy' => $activityPolicy,
                ]
            );

            Storage::disk('s3')->put($path, $pdf->output());

            $files[] = [
                'type' => 'pdf',
                'path' => $path,
                'name' => SendDocument::$SUBJECTS[$document] . '.pdf',
            ];

            // Guardamos el documento en la base de datos
            $activityActionDocument = new ActivityActionDocument();
            $activityActionDocument->activity_action_id = $activityAction->id;
            $activityActionDocument->name = $document;
            $activityActionDocument->path = $path;
            $activityActionDocument->save();

            //Creamos el servicio de prestaciones medicas
            $medicalService = new MedicalServicesController();
            $idProvider = $medicalService->assignPrimaryCareService($activityMedical->id);

            // //guardamos el nombre del proveedor en la tabla gis
            $gis->provider_id = Provider::where('id', $idProvider)->first()->id;
            $gis->save();

            //Ejecutamos la acción REPORTAR PROVEEDOR ATENCIÓN PRIMARIA ASIGNADO
            $description = "Se generó la acción reportar proveedor atención primaria";
            $activityAction = ActionController::create($activityGis->id, ActionGisSort::REPORTAR_PROVEEDOR_ATENCION_PRIEMARIA_ASIGNADA, $description);

            //Se envia solo el correo de tomador independiente de si es o no asegurado
            $takerEmail = [
                $activityPolicy->affiliate->email, // Tomador
            ];

            //Si el caso es asegurado
            if ($req->insured_case == "Si") {

                //Enviamos los documentos al tomador y al afiliado
                $emailTaker = [
                    $activityPolicy->affiliate->email, // Tomador
                    $activityPolicy->policy_sort->email, // Intermediario
                ];
                $emailAffiliate = [
                    $activityGis->affiliate->email, // Afiliado
                    $activityPolicy->policy_sort->email, // Intermediario
                ];

                // Remover correos duplicados y vacíos (opcional)
                $emailsTaker = array_filter(array_unique($emailTaker));
                $emailsAffiliate = array_filter(array_unique($emailAffiliate));

                $this->resendEmailInsuredCase($req, $activityGis, Service::SERVICE_GIS_SORT_MNK, ActionGisSort::REPORTAR_AVISO_CASO, $emailsTaker, $client, $files, $activityPolicy, $user, $password);
                $this->resendEmailInsuredCaseForAffiliate($req, $activityGis, Service::SERVICE_GIS_SORT_MNK, ActionGisSort::REPORTAR_AVISO_CASO, $emailsAffiliate, $client, $files, $activityPolicy, $user, $password);
            }

            //Si el caso no es asegurado
            if ($req->insured_case == "No") {

                //Ejecutamos la acción reportar caso aceptado por excpeción
                $this->reportAcceptedCaseException($activityGis);

                //Enviamos el correo para caso no asegurado
                //ME-2482 quitar el envio de correo hasta nuevo avis
                /*$this->resendEmailUninsuredCase($req, $activityGis, Service::SERVICE_GIS_SORT_MNK, ActionGisSort::REPORTAR_AVISO_CASO, $emailsString, $client, $files, $activityPolicy, $user, $password);*/
            }
            
            //Enviamos email al tomador por la acción de REPORTAR PROVEEDOR ATENCIÓN PRIMARIA ASIGN
            $this->resendEmailReportProveedor($req, $activityGis, Service::SERVICE_GIS_SORT_MNK, ActionGisSort::REPORTAR_AVISO_CASO, $takerEmail, $client, $files, $activityPolicy);
        }
    }

    private function reportar_aviso_caso_no_amparado($activityGis)
    {
        //Generamos la ACCIÓN REPORTAR AVISO CASO NO AMPARADO del SERVICIO GIS
        $description = "Aviso no laboral";
        $activity_action = ActionController::create($activityGis->id, ActionGisSort::REPORTAR_AVISO_CASO_NO_AMPARADO, $description);
        $this->resendEmailDeterminationOrigin($activityGis, $activity_action);
    }

    //Accion reportar desarición del afiliado MS-1780 
    public function ReportMissingffiliate($gis_id, $activityGis, $client)
    {
        //Ejecutamos la acción REPORTAR DESAPARICIÓN DE AFILIADO
        $description = "Se generó la acción reportar desaparición afiliado";
        ActionController::create($activityGis->id, ActionGisSort::REPORTAR_DESAPARICION_AFILIADO, $description);
    }

    //Acción rpeortar muerte del afialido MS-1775
    public function ReportDeathAffiliate($gisId, $activityGis, $client)
    {
        //Ejecutamos la acción REPORTAR MUERTE DE AFILIADO
        $description = "Se generó la acción reportar muerte afiliado";
        ActionController::create($activityGis->id, ActionGisSort::REPORTAR_MUERTE_AFILIADO, $description);
    }

    // Función para enviar correos del caso asegurado
    public function resendEmailInsuredCase(Request $req, $activityGis, $activity_service, $activityAction, $emails, $client_id, $files, $activityPolicy, $user, $password)
    {
        $nameTaker = ucwords(strtolower($activityPolicy->affiliate->first_name));
        $nameAffiliate = ucwords(strtolower($activityGis->affiliate->first_name));
        $numberIdentification = $activityGis->affiliate->doc_number;
        $typeEvent = $activityGis->gis_sort->type_report;
        $dateEvent = date('d/m/Y', strtotime($activityGis->gis_sort->date_accident));
        $nameContact = '';
        if (
            isset($activityPolicy->policy_sort) &&
            isset($activityPolicy->policy_sort->policy_contacts[0]) &&
            isset($activityPolicy->policy_sort->policy_contacts[0]->name_responsible)
        ) {
            $nameContact = ucwords(strtolower($activityPolicy->policy_sort->policy_contacts[0]->name_responsible));
        }

        //Se obtiene información del tomador autorizado
        $authorizedTakersData = collect();
        if ($activityPolicy) {
            $idActivityTaker = $activityPolicy->id;
            $authorizedTakersData = $this->getAuthorizedTakerEmails($idActivityTaker);
        }

        //Se obtienen solo los email de los tomadores autorizados
        $emailUsersTakerAuthorized = $authorizedTakersData->pluck('email');
        // Combina los emails iniciales con los emails de los tomadores autorizados
        $allEmails = collect($emails)
            ->merge($emailUsersTakerAuthorized)
            ->filter(function ($email) {
                return !empty($email);
            })
            ->unique()
            ->values();

        $finalEmailsArray = $allEmails->toArray();

        //Se validan correos
        $validEmails = array_filter($finalEmailsArray, function ($email) {
            return filter_var($email, FILTER_VALIDATE_EMAIL);
        });

        $emailBuild = TemplateBuilder::build(
            Templates::WORK_RISK_NOTICE,
            [
                'policy_sort' => $activityPolicy->policy_sort->formatSortNumber(),
                'name' => $nameTaker ?? '',
                'name_of_insured' => $nameAffiliate,
                'identification_of_insured' => $numberIdentification,
                'event_type' => $typeEvent,
                'event_date_time' => $dateEvent,
                'reported_by_name' => $nameContact,
            ]
        );

        // Lógica para el envío de correos
        if ($validEmails != null) {

            // Enviar el correo con los documentos adjuntos
            $mailSent = new SendDocumentDataBase(
                implode(',', $validEmails),
                $emailBuild['subject'],
                "<EMAIL>",
                $emailBuild['subject'],
                [
                    "text" => $emailBuild['body'],
                    "sender" => $emailBuild['sender']
                ],
                "<EMAIL>",
                [],
                "send_document_db",
                $client_id,
                $req->getHost(),
                $activityGis->id,
                $activityAction,
                $activity_service
            );

            // Capturar el resultado del envío
            $result = $mailSent->sendMail();

            //Registramos los datos del correo enviado para la trazabilidad
           $mailBoardController = new MailBoardController();

            foreach ($validEmails as $email) {

                $takerAuthorizedId = null;
                $authorizedTaker = $authorizedTakersData->firstWhere('email', $email);
                if($authorizedTaker){
                    $takerAuthorizedId = $authorizedTaker->id;
                }

                $mailBoardController->createRegisterMail(
                    $activityGis->id,
                    $activityGis->service->id,
                    $activityPolicy->policy_sort->consecutive,
                    'Tomador',
                    $nameTaker,
                    $activityPolicy->affiliate->doc_number,
                    $emailBuild['subject'],
                    $emailBuild['body'],
                    $emails,
                    $result,
                    null,
                    $takerAuthorizedId
                );
            }
        }
    }

    public function getAuthorizedTakerEmails($idActivityTaker)
    {
        $authorizedTakersData  = collect();

        if ($idActivityTaker) {

            // Se consultan los user id de los tomadores autorizados a la poliza
            $userIds = UserAuthorizedPolicies::where('activity_id', $idActivityTaker)
                ->pluck('user_id')
                ->toArray();
            //Se valida que existan usuarios autorizados
            if (!empty($userIds)) {
                $authorizedTakersData = User::whereIn('id', $userIds)
                    ->select('id','email')
                    ->get()
                    ->unique('email')
                    ->values();
            }
        }
        return $authorizedTakersData;
    }

    public function resendEmailInsuredCaseForAffiliate(Request $req, $activityGis, $activity_service, $activityAction, $emails, $client_id, $files, $activityPolicy, $user, $password)
    {
        $caseNumber = $activityGis->gis_sort->formatCaseNumber();
        $nameTaker = ucwords(strtolower($activityPolicy->affiliate->first_name));
        $id = $activityPolicy->policy_sort->formatNumberConsecutive();
        $nameAffiliate = ucwords(strtolower($activityGis->affiliate->first_name));
        $numberIdentification = $activityGis->affiliate->doc_number;
        $typeEvent = $activityGis->gis_sort->type_report;
        $dateEvent = date('d/m/Y', strtotime($activityGis->gis_sort->date_accident));
        $hourEvent = $activityGis->gis_sort->hour_accident;
        $formattedHour = date("h:i A", strtotime($hourEvent));

        //Se obtiene información del tomador autorizado
        $authorizedTakersData = collect();
        if ($activityPolicy) {
            $idActivityTaker = $activityPolicy->id;
            $authorizedTakersData = $this->getAuthorizedTakerEmails($idActivityTaker);
        }

        //Se obtienen solo los email de los tomadores autorizados
        $emailUsersTakerAuthorized = $authorizedTakersData->pluck('email');
        // Combina los emails iniciales con los emails de los tomadores autorizados
        $allEmails = collect($emails)
            ->merge($emailUsersTakerAuthorized)
            ->filter(function ($email) {
                return !empty($email);
            })
            ->unique()
            ->values();

        $finalEmailsArray = $allEmails->toArray();

        //Se validan correos
        $validEmails = array_filter($finalEmailsArray, function ($email) {
            return filter_var($email, FILTER_VALIDATE_EMAIL);
        });

        $emailData = TemplateBuilder::build(
            Templates::OCCUPATIONAL_RISK_REPORT,
            [
                'case_number' => $caseNumber,
                'name' => $nameTaker,
                'policy_sort' => $id,
                'name_affiliate' => $nameAffiliate ?? '',
                'number_identification' => $numberIdentification,
                'type_event' => $typeEvent,
                'event_date' => $dateEvent . ', a las ' . $formattedHour
            ]
        );

        // Lógica para el envío de correos
        if ($validEmails != null) {

            // Enviar el correo con los documentos adjuntos
            $mailSent = new SendDocumentDataBase(
                implode(',', $validEmails),
                $emailData['subject'],
                "<EMAIL>",
                $emailData['subject'],
                [
                    "text" => $emailData['body'],
                    "sender" => $emailData['sender']
                ],
                "<EMAIL>",
                [],
                "send_document_db",
                $client_id,
                $req->getHost(),
                $activityGis->id,
                $activityAction,
                $activity_service
            );

            // Capturar el resultado del envío
            $result = $mailSent->sendMail();

            //Registramos los datos del correo enviado para la trazabilidad
            $mailBoardController = new MailBoardController();

            foreach ($validEmails as $email) {

                $takerAuthorizedId = null;
                $authorizedTaker = $authorizedTakersData->firstWhere('email', $email);
                if ($authorizedTaker) {
                    $takerAuthorizedId = $authorizedTaker->id;
                }

                $mailBoardController->createRegisterMail(
                    $activityGis->id,
                    $activityGis->service->id,
                    $activityPolicy->policy_sort->consecutive,
                    'Tomador',
                    $nameTaker,
                    $activityPolicy->affiliate->doc_number,
                    $emailData['subject'],
                    $emailData['body'],
                    $emails,
                    $result,
                    null,
                    $takerAuthorizedId
                );
            }
        }
    }

    // Función resendEmail para enviar correos con varios documentos adjuntos
    public function resendEmailUninsuredCase(Request $req, $activityGis, $activity_service, $activityAction, $emails, $client_id, $files, $activityPolicy, $user, $password)
    {

        $nameTaker = mb_convert_case(mb_strtolower($activityPolicy->affiliate->first_name ?? ''), MB_CASE_TITLE, "UTF-8");
        $nameAffiliate = mb_convert_case(mb_strtolower($activityGis->affiliate->first_name . ' ' . $activityGis->affiliate->last_name ?? ''), MB_CASE_TITLE, "UTF-8");
        ;
        $numberIdentification = $activityGis->affiliate->doc_number;
        $fecha_accidente = $activityGis->gis_sort->date_accident
            ? \Carbon\Carbon::parse($activityGis->gis_sort->date_accident)->format('d/m/Y')
            : 'Fecha no disponible';

        $authorizedTakersData = collect();
        if ($activityPolicy) {
            $idActivityTaker = $activityPolicy->id;
            $authorizedTakersData = $this->getAuthorizedTakerEmails($idActivityTaker);
        }
        $emailUsersTakerAuthorized = $authorizedTakersData->pluck('email');
        $allEmails = collect([$emails])
            ->merge($emailUsersTakerAuthorized)
            ->filter(function ($email) {
                return !empty($email);
            })
            ->unique()
            ->values();

        $finalEmailsArray = $allEmails->toArray();

        $validEmails = array_filter($finalEmailsArray, function ($email) {
            return filter_var($email, FILTER_VALIDATE_EMAIL);
        });

        $emailBuild = TemplateBuilder::build(
            Templates::NOTIFY_EMPLOYER_UNINSURED_CASE,
            [
                'name' => $nameTaker ?? '',
                'policy_sort' => $activityPolicy->policy_sort->formatSortNumber(),
                'name_affiliate' => $nameAffiliate,
                'number_identification' => $numberIdentification,
                'fecha_accidente' => $fecha_accidente,
            ]
        );
        // Lógica para el envío de correos
        if ($validEmails != null) {

            // Enviar el correo con los documentos adjuntos
            $mailSent = new SendDocumentDataBase(
                $validEmails,
                $emailBuild['subject'],
                "<EMAIL>",
                $emailBuild['subject'],
                [
                    "text" => $emailBuild['body'],
                    "sender" => $emailBuild['sender']
                ],
                "<EMAIL>",
                $files,
                "send_document_db",
                $client_id->id,
                $req->getHost(),
                $activityGis->id,
                $activityAction,
                $activity_service
            );

            // Capturar el resultado del envío
            $result = $mailSent->sendMail();

            //Registramos los datos del correo enviado para la trazabilidad
            $mailBoardController = new MailBoardController();
            foreach ($validEmails as $email) {

                $takerAuthorizedId = null;
                $authorizedTaker = $authorizedTakersData->firstWhere('email', $email);
                if ($authorizedTaker) {
                    $takerAuthorizedId = $authorizedTaker->id;
                }
                $mailBoardController->createRegisterMail(
                    $activityGis->id,
                    $activityGis->service->id,
                    $activityPolicy->policy_sort->consecutive,
                    'Tomador',
                    $nameAffiliate,
                    $numberIdentification,
                    $emailBuild['subject'],
                    $emailBuild['body'],
                    $emails,
                    $result,
                    $files,
                    $takerAuthorizedId
                );
            }

        }
    }

    // ME-1056 correo 40 - REPORTAR AUDITORIA ADMINISTRATIVA - RECHAZO
    public function resendEmailNotificationCaseNotCovered(Request $req, $activityGis, $activityAction, $emails, $client_id, $files, $activityPolicy)
    {
        [
            'emails' => $emails,
            'body' => $body,
            'subject' => $subject
        ] = $this->_buildBodyEmailNotificationCaseNotCovered('administrativa', $activityGis, $activityPolicy);

        // Lógica para el envío de correos
        if ($emails != null) {

            // Enviar el correo con los documentos adjuntos
            $mailSent = new SendDocumentDataBase(
                $emails,
                $subject,
                "<EMAIL>",
                "Caso no amparado en la póliza #" . $activityPolicy->policy_sort->formatSortNumber(),
                [
                    "text" => $body,
                    "sender" => "mnk aseguramiento"
                ],
                "<EMAIL>",
                $files,
                "send_document_db",
                $client_id,
                $req->getHost(),
                $activityGis,
                $activityAction,
                SERVICE::SERVICE_GIS_SORT_MNK
            );

            // Capturar el resultado del envío
            $result = $mailSent->sendMail();

            //Registramos los datos del correo enviado para la trazabilidad
            $mailBoardController = new MailBoardController();
            $mailBoardController->createRegisterMail(
                $activityGis->id,
                $activityGis->service->id,
                $activityPolicy->policy_sort->consecutive,
                'Tomador',
                $activityPolicy->affiliate->full_name,
                $activityPolicy->affiliate->doc_number,
                $subject,
                $body,
                $emails,
                $result,
                null
            );
        }
    }

    //Enviar email de caso no asegurado
    public function resendEmialCaseNotCovered(Request $req, $activityGis, $activityAction, $emails, $client_id, $files, $activityPolicy)
    {

        $subject = 'Caso no amparado en la póliza #' . $activityPolicy->policy_sort->formatSortNumber();
        $nameTaker = mb_convert_case(mb_strtolower($activityPolicy->affiliate->first_name ?? ''), MB_CASE_TITLE, "UTF-8");
        $nameAffiliate = mb_convert_case(mb_strtolower($activityGis->affiliate->first_name . ' ' . $activityGis->affiliate->last_name), MB_CASE_TITLE, "UTF-8");
        $numberIdentification = $activityGis->affiliate->doc_number;

        $body = "¡Buen día, $nameTaker!

            Lamentamos informarle que, tras la revisión de nuestra parte, hemos identificado que la persona: $nameAffiliate, con número de identificación $numberIdentification, no cumple con los criterios para ser amparada bajo nuestro contrato. Por esta razón, estamos remitiendo a la persona trabajadora al régimen correspondiente para su atención.

            De acuerdo con el artículo 40 de nuestra norma técnica, conservamos el derecho de solicitar al lesionado o al régimen correspondiente el pago de los gastos incurridos por las prestaciones otorgadas, que no corresponden a un riesgo cubierto por nuestra representada, conforme al principio básico de protección a la vida y la salud.

            Si tiene alguna consulta o necesita más detalles sobre este caso, por favor, contáctenos al 4102-7600. ¡Será un gusto servirle!

            Nuestro propósito es fortalecer la prevención en salud y seguridad laboral del país, así como proteger a sus colaboradores en el momento que más lo necesitan, generando siempre bienestar.
        ";

        // Lógica para el envío de correos
        if ($emails != null) {

            // Enviar el correo con los documentos adjuntos
            $mailSent = new SendDocumentDataBase(
                $emails,
                $subject,
                "<EMAIL>",
                "Caso no amparado en la póliza #" . $activityPolicy->policy_sort->formatSortNumber(),
                [
                    "text" => $body,
                    "sender" => "mnk aseguramiento"
                ],
                "<EMAIL>",
                $files,
                "send_document_db",
                $client_id,
                $req->getHost(),
                $activityGis,
                $activityAction,
                SERVICE::SERVICE_GIS_SORT_MNK
            );

            // Capturar el resultado del envío
            $result = $mailSent->sendMail();

            //Registramos los datos del correo enviado para la trazabilidad
            $mailBoardController = new MailBoardController();
            $mailBoardController->createRegisterMail(
                $activityGis->id,
                $activityGis->service->id,
                $activityPolicy->policy_sort->consecutive,
                'Tomador',
                $nameTaker,
                $activityPolicy->affiliate->doc_number,
                $subject,
                $body,
                $emails,
                $result,
                null
            );
        }
    }

    //Validación del tomador de la póliza
    public function validateTaker(Request $req, $cpath)
    {
        //Buscamos la poliza por su codigo unico
        $policy = "";

        $policy_contacts = PolicyContact::where('number_identify_responsible', $req->person_report)
            ->where('unique_code', $req->code_unique)
            ->first();

        if ($policy_contacts) {
            $policy = PolicySort::where('id', $policy_contacts->policy_sort_id)->first();
        }

        $policy_taker_authorized = User::where('identification_number', $req->person_report)
            ->where('unique_code', $req->code_unique)
            ->first();

        if ($policy_taker_authorized) {

            //Buscamos el tomador de la poliza 
            $userTaker = User::find($policy_taker_authorized->tomador_id);

            if ($userTaker) {

                $activity = Activity::where('affiliate_id', $userTaker->affiliate_id)
                    ->where("service_id", 75)
                    ->get();

                if ($activity) {
                    $policy = PolicySort::whereIn('activity_id', $activity->pluck('id'))
                        ->whereNotNull('consecutive')
                        ->get();
                }
            }
        }

        $isTaker = false;
        if (!$policy_contacts && !$policy_taker_authorized) {
            $policy = PolicySort::with(['activity.affiliate'])
                ->where('unique_code', $req->code_unique)
                ->whereHas('activity.affiliate', function ($query) use ($req) {
                    $query->where('doc_number', $req->person_report);
                })
                ->first();
            if ($policy) {
                $isTaker = true;
            }
        }

        if (!$policy_contacts && !$policy_taker_authorized && !$policy) {

            if (!$policy) {
                return response()->json([
                    'message' => 'Tomador o responsable no encontrado'
                ], 200);
            }
        }

        // Buscamos el tomador usando el código único de la poliza
        if (is_a($policy, 'Illuminate\Database\Eloquent\Collection') && $policy->count() > 1) {
            // Si es una colección y tiene más de un elemento
            $affiliateTaker = $policy[0]->activity->affiliate;

            $responsible = $policy[0]->policy_contacts->where('number_identify_responsible', $req->person_report)
                ->first();
        } else {

            // Aquí evaluamos si $policy es un array o un objeto
            if ($policy instanceof \Illuminate\Support\Collection) {
                $firstPolicy = $policy->first();
            } elseif (is_array($policy)) {
                $firstPolicy = $policy[0];
            } else {
                $firstPolicy = $policy; // Ya es un objeto único
            }

            // Accedemos normalmente al primer elemento, ya como objeto
            $affiliateTaker = $firstPolicy->activity->affiliate;

            $responsible = $firstPolicy->policy_contacts
                ->where('number_identify_responsible', $req->person_report)
                ->first();
        }

        if ($responsible) {
            $responsible->model = "PolicyContact";
            $responsible->responsible_id = $responsible->id;
        }

        //si es respnsable del aviso
        if ($policy_contacts) {
            if ($affiliateTaker && !$responsible) {
                $responsible = new PolicyContact();
                $responsible->type_identification = $affiliateTaker->doc_type;
                $responsible->number_identify_responsible = $affiliateTaker->doc_number;
                $responsible->cellphone_responsible = $affiliateTaker->phone;
                $responsible->name_responsible = $affiliateTaker->first_name . ' ' . $affiliateTaker->last_name;
                $responsible->policy_sort = $policy[0]->id;
                $responsible->model = "Affiliate";
                $responsible->responsible_id = $affiliateTaker->id;
            }
        }


        //Si es tomador autorizado
        if ($policy_taker_authorized) {
            $responsible = new PolicyContact();
            $responsible->type_identification = $policy_taker_authorized->doc_type;
            $responsible->number_identify_responsible = $policy_taker_authorized->identification_number;
            $responsible->cellphone_responsible = $policy_taker_authorized->phone;
            $responsible->name_responsible = $policy_taker_authorized->first_name . ' ' . $policy_taker_authorized->last_name;
            $responsible->policy_sort = $policy[0]->id;
            $responsible->model = "User";
            $responsible->responsible_id = $policy_taker_authorized->id;
        }

        //Si es tomador


        if ($isTaker) {
            $datosAffiliate = $policy->activity->affiliate;

            $responsible = new PolicyContact();
            $responsible->type_identification = $datosAffiliate->doc_type;
            $responsible->number_identify_responsible = $datosAffiliate->doc_number;
            $responsible->cellphone_responsible = $datosAffiliate->phone;
            $responsible->name_responsible = $datosAffiliate->full_name;
            $responsible->policy_sort = $policy->id;
            $responsible->model = "Affiliate";
            $responsible->responsible_id = $datosAffiliate->id;
        }


        // Si ambos traen datos retornamos
        return response()->json([
            'taker' => $affiliateTaker,
            'responsible' => $responsible,
            'policy' => $policy
        ], 200);
    }

    //Table del tomador FURAT
    public function indexFurat(Request $request, $cpath, $id)
    {
        return view('services.gis.furat.steps.menu_affiliate', ['active' => 'affilate', 'id' => $id]);
    }

    public function form_affilate(Request $request, $cpath, $id)
    {

        $client = Client::where('path', $cpath)->firstOrFail();

        $activity = Activity::with([
            'parent_activity.policy_sort',
            'affiliate.policy_spreadsheet_affiliate',
            'state',
            'gis_sort'
        ])
            ->where('service_id', Service::SERVICE_GIS_SORT_MNK)
            ->where('client_id', $client->id)
            ->where('id', $id)->first();

        return view('services.gis.furat.steps.menu_affiliate', ['active' => 'affilate', 'id' => $id, 'activity' => $activity]);
    }

    public function form_affilate_data(Request $request)
    {

        $affiliate = Affiliate::where('id', $request->id)
            ->first();

        DB::beginTransaction();

        try {

            $affiliate->province = $request->province;
            $affiliate->country = $request->nationality_affiliate;
            $affiliate->province = $request->province;
            $affiliate->canton = $request->canton;
            $affiliate->district = $request->district;
            $affiliate->phone = $request->phone;
            $affiliate->civil_status = $request->civil_status;
            $affiliate->school_level = $request->escolaridad;
            $affiliate->employer_address = $request->otras;
            $affiliate->iban_account = $request->iban;
            $affiliate->save();


            DB::commit();

            return response()->json('Solicitud Exitosa', 200);
        } catch (Exception $e) {
            DB::rollback();
            return response()->json([
                'status' => 'error',
                'message' => 'Ocurrió un error en el proceso'
            ], 500);
        }
    }

    public function form_employment_relationship(Request $request, $cpath, $id)
    {

        $client = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::with([
            'parent_activity.policy_sort.policy_contacts',
            'affiliate.policy_spreadsheet_affiliate',
            'state',
            'gis_sort'

        ])
            ->where('service_id', Service::SERVICE_GIS_SORT_MNK)
            ->where('client_id', $client->id)
            ->where('id', $id)->first();

        $lastThreeMonths = PolicySpreadsheetAffiliate::query()
            ->where('affiliate_id', $activity->affiliate_id)
            ->orderBy('id', 'desc')
            ->limit(3)
            ->select('policy_spreadsheet_id', 'monthly_salary', 'days')
            ->get();

        $policy_contacts = "";

        $activityGis = Activity::find($id);

        $model = $activityGis->gis_sort->model;

        if ($model) {
            if ($model == 'PolicyContact') {
                $policy_contacts = PolicyContact::where('id', $activityGis->gis_sort->responsible_id)->get()->map(function ($item) {
                    return (object) [
                        'type_identification' => $item->type_identification,
                        'number_identify_responsible' => $item->number_identify_responsible,
                        'name_responsible' => $item->name_responsible,
                        'phone_responsible' => $item->phone_responsible,
                        'cellphone_responsible' => $item->cellphone_responsible,
                        'unique_code' => $item->unique_code,
                        'ocupation_responsible' => $item->ocupation_responsible,
                        'email_responsible' => $item->email_responsible
                    ];
                });
            }

            if ($model == 'User') {
                $policy_contacts = User::where('id', $activityGis->gis_sort->responsible_id)->get()->map(function ($item) {
                    return (object) [
                        'type_identification' => $item->doc_type,
                        'number_identify_responsible' => $item->identification_number,
                        'name_responsible' => $item->full_name,
                        'phone_responsible' => $item->phone,
                        'cellphone_responsible' => $item->phone,
                        'unique_code' => $item->unique_code,
                        'ocupation_responsible' => 'Tomador autorizado',
                        'email_responsible' => $item->email
                    ];
                });
            }
        } else {
            $policy_contacts = $activity->parent_activity->policy_sort->policy_contacts;
            if (count($policy_contacts) == 0) {
                $policy_contacts = [PolicyContact::first()];
            }
        }

        return view('services.gis.furat.steps.menu_relation_employment_relationship', [
            'active' => 'employment_relationship',
            'activity' => $activity,
            'id' => $id,
            'planillas' => $lastThreeMonths,
            'policy_contacts' => $policy_contacts
        ]);
    }

    public function dataOccupations(Request $request)
    {
        $occupationId = $request->get('occupation_id') ?? 54;
        $activities = OccupationCategory::where('occupation_id', $occupationId)->get();
        return response()->json($activities);
    }

    public function form_employment_relationship_data(Request $request)
    {

        $gis = GisSort::where('activity_id', $request->activity)
            ->first();

        DB::beginTransaction();

        try {

            $gis->date_entry = $request->entry_date;
            $gis->workplace = $request->work_place;
            $gis->occupation_position = $request->occupation;
            $gis->occupancy_group = $request->occupancy_group;
            $gis->method_payment = $request->salary_payment;
            $gis->days_worked = $request->work_days;
            $gis->working_hours = $request->work_hours;
            $gis->work_hours_hasta = $request->work_hours_hasta;
            $gis->conditions = $request->condition;
            $gis->date_death = $request->defuncion_date;
            $gis->occupations_category_id = $request->occupations_category_id;

            $gis->save();


            DB::commit();

            return response()->json('Solicitud Exitosa', 200);
        } catch (Exception $e) {
            DB::rollback();
            return response()->json([
                'status' => 'error',
                'message' => 'Ocurrió un error en el proceso'
            ], 500);
        }
    }

    public function form_accident_report(Request $request, $cpath, $activityId)
    {
        $activity = Activity::with([
            'parent_activity.policy_sort.policy_contacts',
            'affiliate.policy_spreadsheet_affiliate',
            'state',
            'gis_sort'

        ])
            ->where('service_id', Service::SERVICE_GIS_SORT_MNK)
            ->where('id', $activityId)
            ->first();

        $reportTypes = ['Accidente', 'Enfermedad'];

        $accidentType = AccidentType::all();
        $agetsGi = AgetsGi::all();
        $occupationalDisease = OccupationalDisease::all();

        return view('services.gis.furat.steps.menu_accident_report', [
            'active' => 'accident',
            'id' => $activityId,
            'gis' => $activity->gis_sort,
            'reportTypes' => $reportTypes,
            'fieldData' => $activity->gis_sort->gis_body_parts,
            'accidentType' => $accidentType,
            'agetsGi' => $agetsGi,
            'occupationalDisease' => $occupationalDisease

        ]);
    }

    public function form_signature_data(Request $request, $cpath, $id)
    {

        $client = Client::where('path', $cpath)->firstOrFail();

        $activity = Activity::with([
            'parent_activity.policy_sort.policy_contacts',
            'affiliate.policy_spreadsheet_affiliate',
            'state',
            'gis_sort'

        ])
            ->where('service_id', Service::SERVICE_GIS_SORT_MNK)
            ->where('client_id', $client->id)
            ->where('id', $id)->first();

        return view('services.gis.furat.steps.menu_signature_data', ['active' => 'signature', 'id' => $id, 'activity' => $activity]);
    }

    public function saveSignature(Request $request, $cpath, $id)
    {
        // Obtener la imagen en formato base64 desde la solicitud
        $image_gisHolder = $request->input('image_gisHolder');

        try {

            // Actividad hijo (Policy)
            $Activity = Activity::where('id', $id)
                ->where('service_id', Service::SERVICE_GIS_SORT_MNK)
                ->first();

            if (!$Activity) {
                return response()->json(['message' => 'No se encontró la actividad de la gis sort.'], 404);
            }

            // Obtengo la PolicySort segun su actividad
            $gis = GisSort::where('activity_id', $Activity->id)->first();

            if (!$gis) {
                return response()->json(['message' => 'No se encontró servicio gis.'], 404);
            }

            // Procesar policyholder
            if ($image_gisHolder) {
                $image = str_replace('data:image/png;base64,', '', $image_gisHolder);
                $image = str_replace(' ', '+', $image);
                $imageData = base64_decode($image);

                $fileName = 'signature/gis_' . $id . '_gistHolder.png';
                Storage::disk('s3')->put($fileName, $imageData);
                $gis->sign_holder = $fileName;
            }

            // Guardar los cambios en la póliza
            $gis->save();

            return response()->json([
                'message' => 'Firmas guardadas temporalmente.',
                'policyholder_url' => $gis->sign_holder ?? null,
            ]);
        } catch (Exception $e) {
            return response()->json([
                'message' => 'No se logró guardar la firma.',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function getSignature(Request $request, $cpath, $id)
    {

        try {

            $Activity = Activity::with([
                'activity_actions' => function ($query) {
                    $query->where('action_id', ActionGisSort::SOLICITAR_EMISION_CASO_FIRMA_FISICA)
                        ->with('documents'); // Asegura cargar los documentos de las acciones filtradas.
                }
            ])
                ->where('id', $id)
                ->where('service_id', Service::SERVICE_GIS_SORT_MNK)
                ->first();


            if (!$Activity) {
                return response()->json(['message' => 'Activity de la gis sort no encontrada.'], 200);
            }

            $sign_holder = $Activity->gis_sort->sign_holder;

            $document = '';


            if (
                !empty($Activity->activity_actions) && isset($Activity->activity_actions[0]) &&
                !empty($Activity->activity_actions[0]->documents) && isset($Activity->activity_actions[0]->documents[0])
            ) {
                $document = $Activity->activity_actions[0]->documents[0]->path;
            }


            return response()->json([
                'sign_holder' => $sign_holder,
                'document' => $document
            ]);
        } catch (Exception $e) {

            return response()->json([
                'message' => 'No se logró obtener la firma.',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    private function actionDeterminationOrigin(Request $request, $activityGis, $activityPolicy, $client)
    {
        // Validar los datos entrantes
        $request->validate([
            'casedata_classif.*' => 'required',
            'diagnostics.cod.*' => 'required',
            'casedata_diagnosis.*' => 'required',
            'casedata_laterality.*' => 'required',
        ]);

        // Obtener todos los datos enviados para los diagnósticos
        $diagnosticsData = $request->input('casedata_diagnosis', []);
        $diagnosticsCodeCie = $request->input('diagnostics.cod', []);
        $casedataClassif = $request->input('casedata_classif', []);
        $casedataLaterality = $request->input('casedata_laterality', []);

        $activityGis->gis_sort->update([
            'qualifier' => $request->qualifier,
            'test_name' => $request->test_name,
            'sustentation' => $request->sustentation,
            'doc_number' => $request->doc_number,
            'event_type' => $request->event_type,
            'siniestro_category' => $request->siniestro_category,
            'doc_date' => $request->doc_date,
            'severity' => $request->severity,
            'days_it' => $request->days_it,
            'qualification_date' => $request->qualification_date,
            'otro' => $request->otro
        ]);

        // Iterar sobre los datos y guardar cada diagnóstico
        foreach ($diagnosticsData as $index => $diagnosis) {
            // Obtener los valores correspondientes a cada diagnóstico
            $casedataCodeCie = isset($diagnosticsCodeCie[$index]) ? $diagnosticsCodeCie[$index] : null;
            $casedataClassifValue = isset($casedataClassif[$index]) ? $casedataClassif[$index] : null;
            $casedataLateralityValue = isset($casedataLaterality[$index]) ? $casedataLaterality[$index] : null;

            GisDiagnostic::create([
                'gis_id' => $activityGis->gis_sort->id,
                'casedata_classif' => $casedataClassifValue,
                'casedata_code_cie' => $casedataCodeCie,
                'casedata_diagnosis' => $diagnosis,
                'casedata_laterality' => $casedataLateralityValue,
            ]);
        }

        //Si el evento es diferente a Enfermedad o accidente
        //Ejecutamos la acción REPORTAR_AUDITORIA_ADMINISTRATIVA_RECHAZO
        if (intVal($request->event_type <> 1 && $request->event_type <> 2)) {

            //Ejecutamos la acción REPORTAR_AUDITORIA_ADMINISTRATIVA_RECHAZO
            $description = "Se ejecuto la acción reportar auditoria administrativa rechazo";
            $activityAction = ActionController::create(
                $activityGis->id,
                ActionGisSort::REPORTAR_AUDITORIA_ADMINISTRATIVA_RECHAZO,
                $description
            );

            $emails = $activityPolicy->affiliate->email;
            //Enviamos un correo al tomador reportando el caso no amparado
            $this->resendEmialCaseNotCovered(
                $request,
                $activityGis,
                $activityAction,
                $emails,
                $client->id,
                [],
                $activityPolicy
            );
        }

        //enviamos la notificación al afiliado
        $emails = $activityGis->affiliate->email;

        //Enviamos un email asociado a la acción
        $this->resendEmailDeterminationOrigin($request, $activityGis, $activityAction, $emails, $client);

        return response()->json([
            'message' => 'Acción generada'
        ]);
    }

    // ME-1056 correo 40 - REPORTAR AVISO DEL CASO - NO AMPARADO
    public function resendEmailDeterminationOrigin($activityGis, $activityAction)
    {
        [
            'emails' => $emails,
            'body' => $body,
            'policySort' => $policySort,
            'subject' => $subject
        ] = $this->_buildBodyEmailNotificationCaseNotCovered('administrativa', $activityGis);

        // Lógica para el envío de correos
        if ($emails != null) {
            // Enviar el correo con los documentos adjuntos
            $mailSent = new SendDocumentDataBase(
                $emails,
                $subject,
                "<EMAIL>", // Email de remitente
                "Caso no amparado en la póliza #" . $policySort,
                [
                    "text" => $body,
                    "sender" => "mnk aseguramiento"
                ],
                "<EMAIL>", // Responder a este email
                [], // Adjuntos (PDFs generados y los de public)
                "send_document_db",
                $activityGis->client_id,
                request()->getHost(),
                $activityGis,
                $activityAction,
                Service::SERVICE_GIS_SORT_MNK
            );

            // Capturar el resultado del envío
            $result = $mailSent->sendMail();

            $activityPolicy = Activity::query()
                ->where('id', $activityGis->parent_id)
                ->first();

            //Registramos los datos del correo enviado para la trazabilidad
            $mailBoardController = new MailBoardController();
            $mailBoardController->createRegisterMail(
                $activityGis->id,
                $activityGis->service->id,
                $activityPolicy->policy_sort->consecutive,
                'Tomador',
                $activityPolicy->affiliate->full_name,
                $activityPolicy->affiliate->doc_number,
                $subject,
                $body,
                $emails,
                $result,
                null
            );
        }
    }

    //ACCION RESPUESTA CONTROVERSIA RECHAZADA MS-1805
    public function rejectControversyResponse(Request $req, $cpath, $id)
    {
        $client = Client::query()
            ->where('path', $cpath)
            ->firstOrFail();
        $activityGis = Activity::query()
            ->where('client_id', $client->id)
            ->where('id', $id)
            ->firstOrFail();
        $activityPolicy = Activity::query()
            ->where('id', $activityGis->parent_id)
            ->first();
        DB::beginTransaction();
        try {
            //Ejecutamos la acción alta medica
            $description = "Se ejecuto la acción respuesta trámite judicial rechazado";

            $activityAction = ActionController::create(
                $activityGis->id,
                ActionGisSort::RESPUESTA_TRAMITE_JUDICIAL_RECHAZADA,
                $description
            );

            [
                'emails' => $emails,
                'body' => $body,
                'subject' => $subject
            ] = $this->_buildBodyEmailNotificationCaseNotCovered('médica', $activityGis, $activityPolicy);

            // Lógica para el envío de correos
            if ($emails != null) {

                // Enviar el correo con los documentos adjuntos
                $mailSent = new SendDocumentDataBase(
                    $emails,
                    $subject,
                    "<EMAIL>",
                    "Caso no amparado en la póliza #" . $activityPolicy->policy_sort->formatSortNumber(),
                    [
                        "text" => $body,
                        "sender" => "mnk aseguramiento"
                    ],
                    "<EMAIL>",
                    [],
                    "send_document_db",
                    $client->id,
                    $req->getHost(),
                    $activityGis,
                    $activityAction,
                    SERVICE::SERVICE_GIS_SORT_MNK
                );

                // Capturar el resultado del envío
                $result = $mailSent->sendMail();

                $activityPolicy = Activity::query()
                    ->where('id', $activityGis->parent_id)
                    ->first();

                //Registramos los datos del correo enviado para la trazabilidad
                $mailBoardController = new MailBoardController();
                $mailBoardController->createRegisterMail(
                    $activityGis->id,
                    $activityGis->service->id,
                    $activityPolicy->policy_sort->consecutive,
                    'Tomador',
                    $activityPolicy->affiliate->full_name,
                    $activityPolicy->affiliate->doc_number,
                    $subject,
                    $body,
                    $emails,
                    $result,
                    null
                );
            }

            DB::commit();

            return back()->with('success', 'Se ejecuto la acción respuesta trámite judicial rechazado');
        } catch (Exception $e) {
            DB::rollback();
            return back()->with('error', 'Ocurrió un error en el proceso' . $e);
        }
    }

    public function approveControversies(Request $req, $cpath, $id)
    {

        try {
            $activityGis = Activity::where('id', $id)->firstOrFail();

            if ($activityGis->gis_sort->controversy_reason == 'acuerdo_pcg') {

                $activityAction = ActivityAction::where('activity_id', $id)
                    ->whereIn('new_state_id', [StateGis::CASO_EN_CALIFICACION_PCG_SIN_ALTA_MEDICA, StateGis::CASO_CON_ALTA_MEDICA_PENDIENTE_PCG])
                    ->orderBy('created_at', 'desc')
                    ->first();

                if ($activityAction->new_state_id == StateGis::CASO_EN_CALIFICACION_PCG_SIN_ALTA_MEDICA) {
                    $response = $this->controversiesPcgCase($req, $cpath, $id);
                } else {
                    $response = $this->controversiesPcg2Case($req, $cpath, $id);
                }
            } else {
                //validar con wilmer el porcentaje de donde sale (campo tipo incapacidad permanente)
                $response = $this->ResponseControversyOrigin($req, $cpath, $id);
            }

            if ($response->getStatusCode() === 200) {
                $data = json_decode($response->getContent(), true);
                $message = $data['message'];
                return back()->with('success', $message);
            } else {
                return back()->with('error', 'No se pudo realizar la operacion');
            }
        } catch (Exception $e) {
            return back()->with('error', 'No se pudo realizar la operacion');
        }
    }

    // ACCION RESPUESTA CONTROVERSIA - PCG MS-1803
    public function controversiesPcgCase($req, $cpath, $activityGisId)
    {

        $client = Client::where('path', $cpath)
            ->firstOrFail();

        DB::beginTransaction();

        try {
            $activityGis = Activity::find($activityGisId);
            $dateControversie = Carbon::parse($activityGis->updated_at)->format('d/m/Y');

            $description = "Se ejecuto la acción respuesta trámite judicial pcg";

            $activityAction = ActionController::create(
                $activityGisId,
                ActionGisSort::RESPUESTA_TRAMITE_JUDICIAL_PCG,
                $description
            );

            $activityPolicy = Activity::where('id', $activityGis->parent_id)
                ->first();

            $emails = [
                //correo Tomador
                $activityPolicy->affiliate->email,
                //correo Afiliado
                $activityGis->affiliate->email,
            ];

            // Remover correos duplicados y vacíos
            $emails = array_filter(array_unique($emails));

            //separados por comas
            $emailsString = implode(',', $emails);

            //Enviamos correo Junta medica calificada
            //Se debe enviar dependiendo si en el campo (Instancia donde se instaura la segunda controversia) se selecciona Ministerio del trabajo
//            if ($activityGis->gis_sort->controversy_instance == '1') {
//                $this->resendEmailQualifiedMedical($req, $activityGis, $emailsString, $activityAction, $client->id, $dateControversie);
//            } else if ($activityGis->gis_sort->controversy_instance == '2') {
//                $this->resendEmailJudicialOpinion($req, $activityGis, $emailsString, $activityAction, $client->id, $dateControversie);
//            }
            if ($activityGis->gis_sort->controversy_judicial_instance == '1' || $activityGis->gis_sort->controversy_judicial_instance == '2') {// controversy_instance old
                $this->resendEmailQualifiedMedical($req, $activityGis, $emailsString, $activityAction, $client->id, $dateControversie);
            } else {
                $this->resendEmailJudicialOpinion($req, $activityGis, $emailsString, $activityAction, $client->id, $dateControversie);
            }
            DB::commit();

            return response()->json([
                'message' => 'Se ejecuto la acción respuesta trámite judicial psg'
            ], 200);
        } catch (Exception $e) {
            DB::rollback();
            return response()->json([
                'status' => 'error',
                'message' => 'Ocurrió un error en el proceso'
            ], 500);
        }
    }

    // ACCION RESPUESTA CONTROVERSIA - PCG 2 MS-1804
    public function controversiesPcg2Case($req, $cpath, $activityGisId)
    {

        $client = Client::where('path', $cpath)
            ->firstOrFail();

        DB::beginTransaction();

        try {
            $activityGis = Activity::find($activityGisId);
            $dateControversie = Carbon::parse($activityGis->updated_at)->format('d/m/Y');

            $description = "Se ejecuto la acción respuesta trámite judicial pcg dos";

            $activityAction = ActionController::create(
                $activityGisId,
                ActionGisSort::RESPUESTA_TRAMITE_JUDICIAL_PCG_DOS,
                $description
            );

            //Buscamos la actividad de la póliza mediante su padre
            $activityPolicy = Activity::where('id', $activityGis->parent_id)
                ->first();

            //Enviamos correo electronico al afiliado y tomador
            $emails = [
                //correo Tomador
                $activityPolicy->affiliate->email,
                //correo Afiliado
                $activityGis->affiliate->email,
            ];

            // Remover correos duplicados y vacíos
            $emails = array_filter(array_unique($emails));

            //separados por comas
            $emailsString = implode(',', $emails);

            //Enviamos correo Junta medica calificada
            //Se debe enviar dependiendo si en el campo (Instancia donde se instaura la segunda controversia) se selecciona Ministerio del trabajo
//            if ($activityGis->gis_sort->controversy_instance == '1') {
//                $this->resendEmailQualifiedMedical($req, $activityGis, $emailsString, $activityAction, $client->id, $dateControversie);
//            } else if ($activityGis->gis_sort->controversy_instance == '2') {
//                $this->resendEmailJudicialOpinion($req, $activityGis, $emailsString, $activityAction, $client->id, $dateControversie);
//            }
            if ($activityGis->gis_sort->controversy_judicial_instance == '1' || $activityGis->gis_sort->controversy_judicial_instance == '2') {// controversy_instance old
                $this->resendEmailQualifiedMedical($req, $activityGis, $emailsString, $activityAction, $client->id, $dateControversie);
            } else {
                $this->resendEmailJudicialOpinion($req, $activityGis, $emailsString, $activityAction, $client->id, $dateControversie);
            }

            DB::commit();

            return response()->json([
                'message' => 'Se ejecuto la acción respuesta trámite judicial psg dos'
            ], 200);
        } catch (Exception $e) {
            DB::rollback();
            return response()->json([
                'status' => 'error',
                'message' => 'Ocurrió un error en el proceso'
            ], 500);
        }
    }

    public function approveCaseReopening(Request $req, $cpath, $id)
    {

        try {

            $count = ActivityAction::where('activity_id', $id)
                ->where(function ($query) {
                    $query->whereIn('old_state_id', [StateGis::CASO_ACEPTADO_EN_SEGUIMIENTO_REHABILITACION, StateGis::CASO_PCG_CALIFICADO_PENDIENTE_AUDITORIA_MEDICA])
                        ->orWhereIn('new_state_id', [StateGis::CASO_ACEPTADO_EN_SEGUIMIENTO_REHABILITACION, StateGis::CASO_PCG_CALIFICADO_PENDIENTE_AUDITORIA_MEDICA]);
                })
                ->count();

            if ($count > 0) {
                $response = $this->reopeningPcg2Case($req, $id);
            } else {
                $response = $this->reopeningOriginCase($req, $id);
            }

            if ($response->getStatusCode() === 200) {
                $data = json_decode($response->getContent(), true);
                $message = $data['message'];
                return back()->with('success', $message);
            } else {
                return back()->with('error', 'No se pudo realizar la operacion');
            }
        } catch (Exception $e) {
            return back()->with('error', 'No se pudo realizar la operacion');
        }
    }

    //Acción reapertura de caso de origen MS-1809
    public function reopeningOriginCase(Request $req, $activityGisId)
    {

        DB::beginTransaction();

        try {
            //Ejecutamos la acción reapertura de caso de origen
            $description = "Se ejecuto la acción reapertura caso origen";

            ActionController::create(
                $activityGisId,
                ActionGisSort::REAPERTURA_DE_CASO_ORIGEN,
                $description
            );

            DB::commit();

            return response()->json([
                'message' => 'Se ejecuto la acción reapertura caso origen'
            ], 200);
        } catch (Exception $e) {
            DB::rollback();
            return response()->json([
                'status' => 'error',
                'message' => 'Ocurrió un error en el proceso'
            ], 500);
        }
    }

    //Acción reapertura de caso de rechazado MS-1810
    public function reopeningRejectedCase(Request $req, $cpath, $id)
    {
        DB::beginTransaction();
        try {
            //Ejecutamos la acción alta medica 
            $description = "Se ejecuto la acción reapertura caso rechazado";

            ActionController::create(
                $id,
                ActionGisSort::REAPERTURA_DE_CASO_RECHAZADA,
                $description
            );

            DB::commit();

            return back()->with('success', 'Se ejecuto la acción reapertura caso rechazado');
        } catch (Exception $e) {
            DB::rollback();
            return back()->with('error', 'Ocurrió un error en el proceso' . $e);
        }
    }

    //Acción reapertura de caso PCG 1 MS-1807
    public function reopeningPcg1Case(Request $req, $activityGisId)
    {

        DB::beginTransaction();

        try {
            //Ejecutamos la acción reapertura de caso rechazado
            $description = "Se ejecuto la acción reapertura caso pcg 1";

            ActionController::create(
                $activityGisId,
                ActionGisSort::REAPERTURA_DE_CASO_PCG_SIN_ALTA,
                $description
            );

            DB::commit();

            return response()->json([
                'message' => 'Se ejecuto la acción reapertura caso pcg 1'
            ]);
        } catch (Exception $e) {
            DB::rollback();
            return response()->json([
                'status' => 'error',
                'message' => 'Ocurrió un error en el proceso'
            ], 500);
        }
    }

    //Acción reapertura de caso PCG 2 MS-1808
    public function reopeningPcg2Case(Request $req, $activityGisId)
    {

        DB::beginTransaction();

        try {
            //Ejecutamos la acción reapertura de caso rechazado
            $description = "Se ejecuto la acción reapertura caso pcg 2";

            ActionController::create(
                $activityGisId,
                ActionGisSort::REAPERTURA_CASO_PCG,
                $description
            );

            DB::commit();

            return response()->json([
                'message' => 'Se ejecuto la acción reapertura caso pcg 2'
            ], 200);
        } catch (Exception $e) {
            DB::rollback();
            return response()->json([
                'status' => 'error',
                'message' => 'Ocurrió un error en el proceso'
            ], 500);
        }
    }

    //Acción reportar alta medica MS-1772
    public function reportMedicalDischarge($activityGisId)
    {
        DB::beginTransaction();

        try {
            //Ejecutamos la acción reportar alta medica 
            $description = "Se ejecuto la acción reportar alta medica";

            ActionController::create(
                $activityGisId,
                ActionGisSort::REPORTAR_ALTA_MEDICA,
                $description
            );

            DB::commit();

            return response()->json([
                'message' => 'Se ejecuto la acción reportar alta medica'
            ]);
        } catch (Exception $e) {
            DB::rollback();
            return response()->json([
                'status' => 'error',
                'message' => 'Ocurrió un error en el proceso'
            ], 500);
        }
    }

    //Acción REPORTAR AUDITORIA APROBACIÓN MS-1791
    public function ApproveAuditRejection($cpath, $idActivityGis)
    {
        DB::beginTransaction();

        try {
            //Capturar cliente
            $client = Client::where('path', $cpath)->firstOrFail();

            $activityGis = Activity::where('id', $idActivityGis)->first();

            if (!$activityGis)
                return redirect("/tablero/auditoria_medica")->with('error', 'No se pudo encontrar la actividad de GIS');
            //Validación del tipo de servicio de la actividad
            if (!Service::checkService($activityGis->service_id, Service::SERVICE_GIS_SORT_MNK)) {
                return redirect("/tablero/auditoria_medica")->with('error', 'Esta acción solo es permitida para servicios GIS SORT');
            }

            //TODO: Crear servicio  PE- IP SORT
            $peipSortActivity = new Activity();
            $peipSortActivity->parent_id = $activityGis->id;
            $peipSortActivity->client_id = $client->id;
            $peipSortActivity->service_id = Service::SERVICE_PE_IP_SORT_MNK;
            $peipSortActivity->affiliate_id = $activityGis->affiliate_id;
            $peipSortActivity->user_id = Auth::id();
            $peipSortActivity->state_id = State::REGISTRADO;
            $peipSortActivity->save();

            //Copiar los datos del servicio GIS a el formulario PE-RIP
            $GisSort = $activityGis->gis_sort;
            $activityPolicy = $activityGis->parent_activity;
            $activityPeIpSort = Activity::where('client_id', $client->id)
                ->where('parent_id', $activityGis->id)
                ->where('service_id', Service::SERVICE_PE_IP_SORT_MNK)
                ->firstOrFail();

            // Se agregan los datos de gis al formulario de PE IP SORT
            $PeipSort = new PeipSort();
            if ($GisSort) {
                //$datosGis = Datos para traer de gis $activity->id;
                $PeipSort->activity_id = $peipSortActivity->id;
                $PeipSort->affiliate_doc_type = $GisSort->type_identification_affiliate; // Tipo de identificación
                $PeipSort->affiliate_doc_number = $GisSort->number_identification_affiliate; // Número de identificación
                $PeipSort->affiliate_name = $GisSort->name_affiliate; //nombre
                $PeipSort->affiliate_email = $GisSort->email_affiliate; //email afiliado
                $PeipSort->casedata_dictamen = $GisSort->date_accident;  // N. dictamen o calificacion
                $PeipSort->casedata_num = $GisSort->id; // N° del caso
                $PeipSort->casedata_date_accident = $GisSort->date_accident; //fecha de accidente
                /*$PeipSort->casedata_date_classi = $GisSort ->; // fecha calificación MNK
                $PeipSort->casedata_laterality =$GisSort -> ; // lateralidad
                $PeipSort->casedata_requires_assistance =$GisSort -> ; //Fecha de mejoría médica maxima (Alta medica)
                $PeipSort->casedata_date_medical_dis= $GisSort ->; //Requiere de la asistencia de otra persona para realizar los actos esenciales de la vida
                 */
                $PeipSort->save();
            } else {
                return redirect("/tablero/auditoria_medica")->with('error', 'Error al crear el servicio PE IP SROT');
            }

            //Cambiar estados de la actividad
            $description = "REPORTAR AUDITORIA APROBACIÓN";
            $activityAction = ActionController::create(
                $activityGis->id,
                ActionGisSort::REPORTAR_AUDITORIA_APROBACION,
                $description
            );

            // GENERATE PDF
            $document = 'dictum_receipt_pcg';
            $path = "activity_action_document/{$document}_{$activityAction->id}.pdf";
            $pdf = PDF::loadView('services.gis.docs.dictum_receipt_pcg_pdf',
                [
                    'activityGis' => $activityGis,
                    'activityPolicy' => $activityPolicy,
                    'activityPeIpSort' => $activityPeIpSort,
                    'formatDate' => Carbon::now()->formatLocalized('%e de %B del %Y')
                ]
            );

            Storage::disk('s3')->put($path, $pdf->output());

            $files[] = [
                'type' => 'pdf',
                'path' => $path,
                'name' => SendDocument::$SUBJECTS[$document] . '.pdf',
            ];

            // Guardamos el documento en la base de datos
            $activityActionDocument = new ActivityActionDocument();
            $activityActionDocument->activity_action_id = $activityAction->id;
            $activityActionDocument->name = $document;
            $activityActionDocument->path = $path;
            $activityActionDocument->save();

            $emails = [
                $activityGis->parent_activity->affiliate->email, // Tomador
                $activityGis->affiliate->email // Afiliado
            ];

            $emailsTaker = array_filter(array_unique($emails));

            $emailData = TemplateBuilder::build(
                Templates::EMAIL_NOTIFICATION_OF_GENERAL_CAPACITY_LOSS_ASSESSMENT_OR_DETERMINATION,
                [
                    'name' => ucwords(strtolower($activityGis->affiliate->first_name)),
                    'case_number' => $GisSort->formatCaseNumber(),
                ]
            );

            if ($emails != null) {

                $mailSent = new SendDocumentDataBase(
                    implode(',', $emailsTaker),
                    $emailData['subject'],
                    "<EMAIL>",
                    $emailData['subject'],
                    [
                        "text" => $emailData['body'],
                        "sender" => $emailData['sender']
                    ],
                    "<EMAIL>",
                    $files,
                    "send_document_db",
                    $client->id,
                    request()->getHost(),
                    $activityGis->id,
                    $activityAction->id,
                    Service::SERVICE_GIS_SORT_MNK_SORT
                );
                $mailSent->sendMail();
            }

            // Capturar el resultado del envío
            $result = $mailSent->sendMail();

            //Registramos los datos del correo enviado para la trazabilidad
            $mailBoardController = new MailBoardController();
            $mailBoardController->createRegisterMail(
                $activityGis->id,
                $activityGis->service->id, 
                $activityGis->parent_activity->policy_sort->consecutive, 
                'Asegurado', 
                ucwords(strtolower($activityGis->affiliate->first_name)), 
                $activityGis->affiliate->doc_number, 
                $emailData['subject'], 
                $emailData['body'],
                $emails, 
                $result,
                $files
            );

            // Crear un objeto Request simulado
            $request = new Request([
                'gis' => $idActivityGis,
                'severity' => $GisSort->severity,
            ]);

            DB::commit();

            $res = $this->CalculatePps($request);

            $data = $res->getData();

            //Actualizamos los campos en el gis_sort con los pps calculado
            $activityGis->gis_sort->update([
                'pps_medical_benefits' => str_replace(['₡', '$'], ['', ''], $data->pps_medical_benefits),
                'pps_temporary_disability' => str_replace(['₡', '$'], ['', ''], $data->pps_it),
                'pps_minor_permanent_disability' => str_replace(['₡', '$'], ['', ''], $data->ppsIpValues),
                'pps_partial_permanent_disability' => str_replace(['₡', '$'], ['', ''], $data->ppsIpITpValues),
                'pps_total_permanent_disability' => str_replace(['₡', '$'], ['', ''], $data->ppsIpIppValues),
                'pps_great_disability_permanent_disability' => str_replace(['₡', '$'], ['', ''], $data->ppsGreatDisabilityValues),
            ]);

            return redirect("/tablero/auditoria_medica")->with('success', 'Acción ejecutada correctamente');
        } catch (Exception $e) {
            DB::rollback();
            return redirect("/tablero/auditoria_medica")->with('error', 'No se pudo ejecutar la acción');
        }
    }

    //Acción reportar auditoria rechazo MS-1792
    public function ReportAuditRejection($cpath, $idActivityGis)
    {
        DB::beginTransaction();
        try {
            $activityGis = Activity::where('id', $idActivityGis)->first();

            if (!$activityGis)
                return redirect("/tablero/auditoria_medica")->with('error', 'No se pudo encontrar la actividad de GIS');

            //Cambiar estados de la actividad
            $description = "REPORTAR AUDITORIA RECHAZO";
            $activityAction = ActionController::create(
                $activityGis->id,
                ActionGisSort::REPORTAR_AUDITORIA_RECHAZO,
                $description
            );

            DB::commit();
            return redirect("/tablero/auditoria_medica")->with('success', 'Acción ejecutada correctamente');
        } catch (Exception $e) {
            DB::rollback();
            return redirect("/tablero/auditoria_medica")->with('error', 'No se pudo ejecutar la acción');
        }
    }

    //Reportar auditoria rechazo sin alta medica MS-1793
    public function reportAuditRejectionWithoutMeedicalDischarge($activityGisId)
    {
        DB::beginTransaction();

        try {
            //Ejecutamos la acción reportar auditoria rechazo sin alata medica 
            $description = "Se ejecuto la acción reportar auditoria rechazo sin alata medica";

            ActionController::create(
                $activityGisId,
                ActionGisSort::REPORTAR_AUDITORIA_RECHAZO_SIN_ALTA,
                $description
            );

            DB::commit();

            return response()->json([
                'message' => 'Se ejecuto la acción reportar auditoria rechazo sin alata medica'
            ]);
        } catch (Exception $e) {
            DB::rollback();
            return response()->json([
                'status' => 'error',
                'message' => 'Ocurrió un error en el proceso'
            ], 500);
        }
    }

    //Acción RESPUESTA CONTROVERSIA - ORIGEN MS-1802, wiilmer me avisas cuando estes aqui
    public function ResponseControversyOrigin($req, $cpath, $activityGisId)
    {
        $client = Client::where('path', $cpath)->firstOrFail();

        DB::beginTransaction();

        try {
            $activityGis = Activity::find($activityGisId);
            $dateControversie = Carbon::parse($activityGis->updated_at)->format('d/m/Y');


            //Ejecutamos la acción  RESPUESTA CONTROVERSIA - ORIGEN
            $description = "Se ejecuto la acción respuesta trámite judicial origen";

            $activityAction = ActionController::create(
                $activityGisId,
                ActionGisSort::RESPUESTA_TRAMITE_JUDICIAL_ORIGEN,
                $description
            );

            //Buscamos la actividad de la póliza mediante su padre
            $activityPolicy = Activity::where('id', $activityGis->parent_id)
                ->first();

            //Enviamos correo electronico al afiliado y tomador
            $emails = [
                //correo Tomador
                $activityPolicy->affiliate->email,
                //correo Afiliado 
                $activityGis->affiliate->email,
            ];

            // Remover correos duplicados y vacíos
            $emails = array_filter(array_unique($emails));

            //separados por comas
            $emailsString = implode(',', $emails);

            //Enviamos correo Junta medica calificada 
            //Se debe enviar dependiendo si en el campo (Instancia donde se instaura la segunda controversia) se selecciona Ministerio del trabajo

            //            if ($activityGis->gis_sort->controversy_instance == '1') {
//                $this->resendEmailQualifiedMedical($req, $activityGis, $emailsString, $activityAction, $client->id, $dateControversie);
//            } else {
//                $this->resendEmailJudicialOpinion($req, $activityGis, $emailsString, $activityAction, $client->id, $dateControversie);
//            }
            if ($activityGis->gis_sort->controversy_judicial_instance == '1' || $activityGis->gis_sort->controversy_judicial_instance == '2') {// controversy_instance old
                $this->resendEmailQualifiedMedical($req, $activityGis, $emailsString, $activityAction, $client->id, $dateControversie);
            } else {
                $this->resendEmailJudicialOpinion($req, $activityGis, $emailsString, $activityAction, $client->id, $dateControversie);
            }

            DB::commit();

            return response()->json([
                'message' => 'Se ejecuto la acción respuesta trámite judicial origen'
            ], 200);
        } catch (Exception $e) {
            DB::rollback();
            return response()->json([
                'status' => 'error',
                'message' => 'Ocurrió un error en el proceso'
            ], 500);
        }
    }

    //correo para junta medica calificada respuesta de controversia
    public function resendEmailQualifiedMedical($req, $activityGis, $emails, $activityAction, $client_id, $dateControversie)
    {

        $total_pcg = $activityGis->total_pcg;

        if ($total_pcg >= 0.5 && $total_pcg <= 50) {
            $resultado = 'menor';
        } elseif ($total_pcg > 50 && $total_pcg < 67) {
            $resultado = 'parcial';
        } elseif ($total_pcg >= 67) {
            $resultado = 'total';
        } else {
            $resultado = '...';
        }

        $type = $activityGis->gis_sort->controversy_judicial_instance == '1' ? 'Junta Médica Calificadora' : 'Medicatura Forense';
        $subject = "Recepción del dictamen de la Junta Médica Calificadora (o Medicatura Forense) - Caso # " . $activityGis->gis_sort->formatCaseNumber();
        $nameAffiliate = mb_convert_case(mb_strtolower($activityGis->affiliate->full_name ?? ''), MB_CASE_TITLE, "UTF-8");
        $numberIdentification = $activityGis->affiliate->doc_number;
        // pe_ip_sort
        $activityPeIpSort = Activity::where('parent_id', $activityGis->id)
            ->where('service_id', Service::SERVICE_PE_IP_SORT_MNK)
            ->firstOrFail();
        $peIpSort = $activityPeIpSort->pe_ip_sort;
        $emailBuild = TemplateBuilder::build(
            Templates::NOTIFY_RECEIVE_MEDICAL,
            [
                'caso_gis' => $activityGis->gis_sort->formatCaseNumber(),
                'name' => $nameAffiliate,
                'fecha' => $dateControversie,
                'type' => $type,
                'porcentaje' => ($peIpSort ? $peIpSort->casedata_percent : '') . '%',
                'case_data_type_disability' => $peIpSort ? $peIpSort->casedata_type_disability : null
            ]
        );

        if ($emails != null) {

            $mailSent = new SendDocumentDataBase(
                $emails,
                $emailBuild['subject'],
                "<EMAIL>",
                $emailBuild['subject'],
                [
                    "text" => $emailBuild['body'],
                    "sender" => $emailBuild['sender']
                ],
                "<EMAIL>",
                [], //files
                "send_document_db",
                $client_id,
                request()->getHost(),
                $activityGis,
                $activityAction,
                Service::SERVICE_GIS_SORT_MNK_SORT
            );

            // Capturar el resultado del envío
            $result = $mailSent->sendMail();

            $activityPolicy = Activity::query()
                ->where('id', $activityGis->parent_id)
                ->first();

            //Registramos los datos del correo enviado para la trazabilidad
            $mailBoardController = new MailBoardController();
            $mailBoardController->createRegisterMail(
                $activityGis->id,
                $activityGis->service->id, 
                $activityPolicy->policy_sort->consecutive, 
                'Asegurado', 
                $nameAffiliate, 
                $numberIdentification,
                $subject,
                $emailBuild['body'],
                $emails, 
                $result,
                null
            );
        }
    }

    //correo para  Rama Judicial (Juzgados o tribunales)
    public function resendEmailJudicialOpinion($req, $activityGis, $emails, $activityAction, $client_id, $dateControversie)
    {

        $nameAffiliate = mb_convert_case(mb_strtolower($activityGis->affiliate->full_name ?? ''), MB_CASE_TITLE, "UTF-8");
        $numberIdentification = $activityGis->affiliate->doc_number;
        $emailBuild = TemplateBuilder::build(
            Templates::NOTIFY_RECEIVE_OPINION,
            [
                'caso_gis' => $activityGis->gis_sort->formatCaseNumber(),
                'name' => $nameAffiliate,
                'fecha' => $dateControversie,
                'court_or_tribunal' => $activityGis->gis_sort->controversy_judicial_instance,
            ]
        );

        if ($emails != null) {

            $mailSent = new SendDocumentDataBase(
                $emails,
                $emailBuild['subject'],
                "<EMAIL>",
                "Recepción del dictamen de juzgado",
                [
                    "text" => $emailBuild['body'],
                    "sender" => $emailBuild['sender']
                ],
                "<EMAIL>",
                [], //files
                "send_document_db",
                $client_id,
                request()->getHost(),
                $activityGis,
                $activityAction,
                Service::SERVICE_GIS_SORT_MNK_SORT
            );

            // Capturar el resultado del envío
            $result = $mailSent->sendMail();

            $activityPolicy = Activity::query()
                ->where('id', $activityGis->parent_id)
                ->first();

            //Registramos los datos del correo enviado para la trazabilidad
            $mailBoardController = new MailBoardController();
            $mailBoardController->createRegisterMail(
                $activityGis->id,
                $activityGis->service->id, 
                $activityPolicy->policy_sort->consecutive, 
                'Asegurado', 
                $nameAffiliate,
                $numberIdentification,
                $emailBuild['subject'],
                $emailBuild['body'],
                $emails, 
                $result,
                null
            );
        }


    }
    public function form_support(Request $request, $cpath, $id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();

        switch ($activity->state_id) {
            case StateGis::CASO_AVISADO_PRENDIENTE_REPORTE_FORMAL:
            case StateGis::PENDIENTE_ENTREGA_INFORMACION_EXTRA:
                $disableUploads = false;
                break;

            default:
                $disableUploads = true;
        }

        $activity_documents = ActivityDocument::where('activity_id', $activity->id)->get();

        $documents = [];
        foreach ($activity_documents as $doc) {
            $documents[$doc->document_id] = $doc;
        }

        return view('services.gis.furat.steps.menu_support', [
            'active' => 'support',
            'id' => $id,
            'activity' => $activity,
            'disableUploads' => $disableUploads,
            'documents' => $documents
        ]);
    }

    public function form_accident_report_view(Request $request)
    {
        $condition_gis_value = $request->input('condition_gis_value');

        return view('services.gis.form.components.reporte_accidente.components.form_reporte_accidente', compact('condition_gis_value'));
    }

    public function table_furat_view(Request $request, $cpath, $id, $table, $npoliza)
    {
        $client = Client::query()->where('path', $cpath)->firstOrFail();

        $activityQuery = Activity::with([
            'parent_activity.policy_sort',
            'affiliate',
            'state'
        ])
            ->where('service_id', Service::SERVICE_GIS_SORT_MNK)
            ->where('client_id', $client->id)
            ->whereHas('parent_activity.policy_sort', function ($query) use ($npoliza) {
                $query->where('id', $npoliza);
            })
            ->orderBy('created_at', 'desc');

        if ($table === 'reporte') {
            $activityQuery->where('state_id', StateGis::CASO_AVISADO_PRENDIENTE_REPORTE_FORMAL);
        }

        if ($table === 'anexar') {
            $activityQuery->where('state_id', StateGis::PENDIENTE_ENTREGA_INFORMACION_EXTRA);
        }

        $activity = $activityQuery->paginate(10);

        return view(
            'services.policy_sort.holder_policy.components.table_furat',
            [
                'id' => $id,
                'activity' => $activity,
                'npoliza' => $npoliza,
                'table' => $table,
                'active' => $table === 'anexar' ? 'anexar_soportes' : 'pendiente_reporte',
            ]
        );
    }


    public function table_furat_reportes_view(Request $request, $cpath, $id, $npoliza)
    {
        $client = Client::query()->where('path', $cpath)->firstOrFail();

        $activityQuery = Activity::with([
            'parent_activity.policy_sort',
            'affiliate',
            'state',
            'gis_sort',
            'activity_actions' => function ($query) {
                $query->whereIn('action_id', [
                    ActionGisSort::REPORTAR_FORMATO_FORMAL_CASO,
                    ActionGisSort::REPORTAR_FORMATO_FORMAL_CASO_SIN_SOPORTES
                ]);
            },
            'activity_actions.documents'
        ])
            ->where('service_id', Service::SERVICE_GIS_SORT_MNK)
            ->where('client_id', $client->id)
            ->whereHas('parent_activity.policy_sort', function ($query) use ($npoliza) {
                $query->where('id', $npoliza);
            })
            ->orderBy('created_at', 'desc');

        // Filtro por número de cédula del asegurado (affiliate.doc_number)
        if ($request->filled('cedula')) {
            $cedula = $request->input('cedula');
            $activityQuery->whereHas('affiliate', function ($query) use ($cedula) {
                $query->where('doc_number', 'like', '%' . $cedula . '%');
            });
        }

        // Filtro por número del caso (gis_sort.consecutive)
        if ($request->filled('numero_caso')) {
            $numeroCaso = $request->input('numero_caso');
            $activityQuery->whereHas('gis_sort', function ($query) use ($numeroCaso) {
                $query->where('consecutive', 'like', '%' . $numeroCaso . '%');
            });
        }

        // Filtro por rango de fecha en la ocurrencia del accidente (gis_sort.date_accident)
        if ($request->filled('fecha_inicio') && $request->filled('fecha_fin')) {
            // Asumiendo que las fechas se reciben en formato Y-m-d. 
            // Si se reciben en otro formato (por ejemplo, d/m/Y) se deberán convertir usando Carbon.
            $fechaInicio = $request->input('fecha_inicio');
            $fechaFin = $request->input('fecha_fin');
            $activityQuery->whereHas('gis_sort', function ($query) use ($fechaInicio, $fechaFin) {
                $query->whereBetween('date_accident', [$fechaInicio, $fechaFin]);
            });
        }

        $activity = $activityQuery->paginate(10);



        return view(
            'services.policy_sort.holder_policy.components.table_furat_reporte',
            [
                'id' => $id,
                'activity' => $activity,
                'npoliza' => $npoliza,
                'active' => 'reporte_accidente_enfermedad',
            ]
        );
    }

    //ME-2782
    public function reimbursementRequestFollowup(Request $request, $cpath, $id, $npoliza)
    {

        $activity = Activity::where('service_id', Service::SERVICE_REINTEGRATE_MNK)
            ->whereHas('parent_activity.parent_activity.policy_sort', function ($query) use ($npoliza) {
                $query->where('id', $npoliza);
            })
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view(
            'services.policy_sort.holder_policy.components.reimbursement_request_followup',
            [
                'id' => $id,
                'activity' => $activity,
                'npoliza' => $npoliza,
                'active' => 'seguimiento_solicitud_reintegro',
            ]
        );
    }

    //ME-1289
    public function registerRefund(Request $request, $cpath, $id, $npoliza)
    {

        $activityPolicy = PolicySort::where('id', $npoliza)->first();

        $activity = Activity::where('service_id', Service::SERVICE_GIS_SORT_MNK)
            ->whereHas('parent_activity.policy_sort', function ($query) use ($npoliza) {
                $query->where('id', $npoliza);
            })
            ->whereRaw('(
                SELECT COUNT(*) 
                FROM activity_actions aa 
                WHERE aa.new_state_id = 160 AND aa.activity_id = activities.id
            ) > 0')
            ->orderBy('created_at', 'desc')
            ->with(['gis_sort'])
            ->get();

        $firstActivity = $activity->first();

        $economicBenefit = EconomicBenefit::where('affiliate_id', $id)
            ->where('recognition_type', 'refund')
            ->where('state', 'pending')
            ->where('policy_id', $npoliza)
            ->latest('id')
            ->first();

        return view(
            'services.policy_sort.holder_policy.components.register_refund',
            [
                'id' => $id,
                'activity' => $activity,
                'npoliza' => $npoliza,
                'economicBenefit' => $economicBenefit, //activity de gis
                'active' => 'solicitud_reconocimiento_factura',
                'firstActivity' => $firstActivity,
                'activityPolicy' => $activityPolicy
            ]
        );
    }

    public function registerRefundFile(Request $request, $cpath, $id, $npoliza)
    {
        if ($request->isMethod('get')) {
            return redirect('/tomador/poliza/' . $id . '/gis/solicitud_reconocimiento_factura/' . $npoliza);
        }

        $economicBenefit = EconomicBenefit::where('activity_id', $request->case)
            ->where('recognition_type', 'refund')
            ->orderBy('id', 'desc')->first();

        if (!$economicBenefit) {
            $economicBenefit = new EconomicBenefit();
        }

        $economicBenefit->activity_id = $request->case; //activity de gis
        $economicBenefit->identification_type = $request->doc_type;
        $economicBenefit->identification_number = $request->identification_number;
        $economicBenefit->worker_name = $request->worker_name;
        $economicBenefit->worker_email = $request->worker_email;
        $economicBenefit->worker_phone = $request->worker_phone;
        $economicBenefit->worker_address = $request->worker_address;

        $economicBenefit->province = $request->province_id;
        $economicBenefit->canton = $request->canton_id;
        $economicBenefit->district = $request->district_id;
        //$economicBenefit->date_accident = $request->date_accident;
        $economicBenefit->authorization = $request->authorization;
        $economicBenefit->invoice_concept = $request->incapacity_type;
        $economicBenefit->request_description = $request->request_description;
        $economicBenefit->recognition_type = 'refund';
        $economicBenefit->request_type = 'expense_recognition';
        $economicBenefit->affiliate_id = $id;
        $economicBenefit->state = 'pending';
        $economicBenefit->policy_id = $npoliza;
        $economicBenefit->iban_account = $request->iban_account;
        $economicBenefit->save();

        return view(
            'services.policy_sort.holder_policy.components.register_refund_files',
            [
                'id' => $id,
                'npoliza' => $npoliza,
                'active' => 'solicitud_reconocimiento_factura',
                'economicBenefitId' => $economicBenefit->id
            ]
        );
    }

    public function saveRefund(Request $request, $cpath, $id, $npoliza)
    {
        try {
            $reintegrateServicesController = new ReintegrateServicesController();
            $response = $reintegrateServicesController->generateServiceReintegration($request, $cpath, $id);

            $statusCode = $response->getStatusCode();
            $data = json_decode($response->getContent(), true);

            if ($statusCode === 200) {
                return response()->json([
                    'success' => true,
                    'message' => $data['message'] ?? 'Servicio de reintegro generado correctamente',
                    'id' => $data['id'] ?? null,
                    'redirect' => '/tomador/poliza/' . $id . '/gis/seguimiento_solicitud_reintegro/' . $npoliza
                ]);
            } else if ($statusCode === 400) {
                return response()->json([
                    'success' => false,
                    'message' => $data['message'] ?? 'Error en los datos proporcionados',
                    'redirect' => '/tomador/poliza/' . $id . '/gis/solicitud_reconocimiento_factura/' . $npoliza
                ], 400);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Ocurrió un error al generar el servicio de reintegros',
                    'redirect' => '/tomador/poliza/' . $id . '/gis/solicitud_reconocimiento_factura/' . $npoliza
                ], 500);
            }
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error inesperado: ' . $e->getMessage(),
                'redirect' => '/tomador/poliza/' . $id . '/gis/solicitud_reconocimiento_factura/' . $npoliza
            ], 500);
        }
    }

    public function saveRefund_old(Request $request, $cpath, $id, $npoliza)
    {

        $reintegrateServicesController = new ReintegrateServicesController();
        $response = $reintegrateServicesController->generateServiceReintegration($request, $cpath);

        if ($response->getStatusCode() === 200) {

            $data = json_decode($response->getContent(), true);
            $message = $data['message'];
            $newActivityId = $data['id'];

            return redirect('/servicio/' . $newActivityId);
        } else if ($response->getStatusCode() === 400) {

            $data = json_decode($response->getContent(), true);
            $message = $data['message'];

            return redirect('/tomador/poliza/' . $id . '/gis/solicitud_reconocimiento_factura/' . $npoliza)->with('error', $message);
        } else {
            return redirect('/tomador/poliza/' . $id . '/gis/solicitud_reconocimiento_factura/' . $npoliza)->with('error', 'Ocurrió un error al generar el servicio de reintegros');
        }
    }

    public function getGisAffiliate($cpath, $id)
    {
        try {

            if (!$id) {
                throw new \Exception('Código activity no proporcionado.');
            }

            $activity = Activity::where('id', $id)->where('service_id', Service::SERVICE_GIS_SORT_MNK)->firstOrFail();
            $docType = ucfirst(strtolower(AppServiceProvider::$DOC_TYPES[$activity->affiliate->doc_type]));
            $policySortController = new PolicySortController();
            $ubicacion = $policySortController->getLocationNamesFromJson($activity->affiliate->province, $activity->affiliate->canton, $activity->affiliate->district);
            $dateAccident = \Illuminate\Support\Carbon::createFromFormat('Y-m-d', $activity->gis_sort->date_accident);

            $simulatedData = [
                'identification_titular' => $docType,
                'identification_number' => $activity->affiliate->doc_number,
                'worker_name' => mb_convert_case(mb_strtolower($activity->affiliate->full_name ?? ''), MB_CASE_TITLE, "UTF-8"),
                'worker_email' => $activity->affiliate->email,
                'worker_phone' => $activity->gis_sort->cellphone_affiliate ?? '',
                'worker_address' => $activity->affiliate->employer_address ?? '',
                'province' => ucwords(mb_strtolower($ubicacion['province'] ?? '')),
                'canton' => ucwords(mb_strtolower($ubicacion['canton'] ?? '')),
                'district' => ucwords(mb_strtolower($ubicacion['district'] ?? '')),
                'date_accident' => $dateAccident->format('d/m/Y'),
                'province_id' => $activity->affiliate->province,
                'canton_id' => $activity->affiliate->canton,
                'district_id' => $activity->affiliate->district,
                'doc_type' => $activity->affiliate->doc_type,
                'iban' => $activity->gis_sort->worker_iban_account_number ?? ''
            ];

            return response()->json([
                'success' => true,
                'data' => $simulatedData
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Ocurrió un error al realizar la solicitud del api getGisAffiliate.',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function table_gis_view(Request $request, $cpath, $id, $table, $npoliza)
    {
        $activity = Activity::select([
            'af.doc_number AS affiliate_id',
            'af.first_name AS affiliate_name',
            'pf.first_name AS holder_name',
            'p.id AS case_id',
            'p.created_at AS case_date',
            'st.name AS status',
            'activities.id as activity_id'
        ])
            ->leftJoin('gis_sort AS p', 'p.activity_id', '=', 'activities.id')
            ->leftJoin('states AS st', 'st.id', '=', 'activities.state_id')
            ->leftJoin('affiliates AS af', 'af.id', '=', 'activities.affiliate_id')
            ->leftJoin('activities AS ap', 'ap.id', '=', 'activities.parent_id')
            ->leftJoin('affiliates AS pf', 'pf.id', '=', 'ap.affiliate_id')
            ->leftJoin('policy_sorts AS ps', 'ps.activity_id', '=', 'activities.parent_id')
            ->where('activities.service_id', Service::SERVICE_GIS_SORT_MNK)
            ->where('activities.state_id', StateGis::CASO_CERRADO)
            ->where('ps.id', $npoliza)
            ->paginate(10);

        $tableView = $table === 'tramite_judicial' ? "table_gis_controversies" : "table_gis_reopenings";

        return view(
            'services.policy_sort.holder_policy.components.' . $tableView,
            [
                'id' => $id,
                'activity' => $activity,
                'npoliza' => $npoliza,
                'table' => $table,
                'active' => $table === 'tramite_judicial' ? 'tramite_judicial' : 'reapertura',
            ]
        );
    }

    public function registerReopenings($cpath, $id, $gis, $npoliza)
    {


        $gis_data = Activity::where('id', $gis)->with(['gis_sort', 'affiliate', 'parent_activity'])->first();

        $alta_medica = $gis_data->activity_actions()
            ->where('action_id', ActionGisSort::REPORTAR_ALTA_MEDICA)->first();

        $poliza = PolicySort::where('id', $npoliza)->first();

      
        return view(
            'services.policy_sort.holder_policy.components.register_reopenings',
            [
                'id' => $id,
                'npoliza' => $npoliza,
                'activity_gis' => $gis,
                'data' => $gis_data,
                'data_poliza' => $poliza,
                'alta_medica' => $alta_medica,
                'table' => 'table_gis_reopenings',
                'active' => 'reapertura'
            ]
        );
    }

    public function saveReopenings(Request $request, $cpath, $id)
    {

        $response = $this->ReportReopenings($request, $cpath, $id);

        $data = json_decode($response->getContent(), true);
        $message = $data['message'];

        if ($response->getStatusCode() === 200) {
            $case = sprintf('%04d', $data['case']);
            return redirect('/tomador/poliza/' . $request->id . '/gis/reapertura/' . $request->npoliza)->with('success', 'Reapertura del caso ' . $case . ' solicitada con éxito');
        } else {
            return redirect()->back()->with('error', 'Error al solicitar reapertura del caso');
        }
    }

    public function registerControversies($cpath, $id, $gis, $npoliza)
    {
        return view(
            'services.policy_sort.holder_policy.components.register_controversies',
            [
                'id' => $id,
                'npoliza' => $npoliza,
                'activity_gis' => $gis,
                'table' => 'tramite_judicial',
                'active' => 'tramite_judicial',
            ]
        );
    }

    public function saveControversies(Request $request, $cpath, $id)
    {

        if ($request->controversy_reason == 'acuerdo_pcg') {
            $controller = new ComprehensiveAccidentManagementController();
            $response = $controller->ReportPcgControversy($request, $id);
        } else {
            $controller = new ComprehensiveAccidentManagementController();
            $response = $controller->ReportOriginControversy($request, $id);
        }

        $data = json_decode($response->getContent(), true);
        $message = $data['message'];

        if ($response->getStatusCode() === 200) {
            $case = sprintf('%04d', $data['case']);
            return redirect('/tomador/poliza/' . $request->id . '/gis/tramite_judicial/' . $request->npoliza)->with('success', 'Trámite judicial del caso ' . $case . ' solicitado con éxito');
        } else {
            return redirect()->back()->with('error', 'Error al solicitar el trámite judicial');
        }
    }

    //Acción reportar caso aceptado por excepción MS-1776
    public function reportAcceptedCaseException($activityGis)
    {
        try {
            //Ejecutamos la acción  REPORTAR CASO ACEPTADO POR EXCEPCIÓN
            $description = "Se generó la acción reportar caso aceptado por excepción";
            ActionController::create(
                $activityGis->id,
                ActionGisSort::REPORTAR_CASOA_ACEPTADO_EXCEPCION,
                $description
            );
        } catch (Exception $e) {
            DB::rollback();
            return response()->json([
                'status' => 'error',
                'message' => 'Ocurrió un error en el proceso'
            ], 500);
        }
    }

    //Acción reportar caso aceptado por excepción MS-1850
    public function reportAcceptedCaseExceptionApprobal(Request $req, $cpath, $activityGisId)
    {
        DB::beginTransaction();
        try {

            $activityGisId = $req->id;

            $client = Client::query()->where('path', $cpath)->firstOrFail();

            $activityGis = Activity::find($activityGisId);

            //Ejecutamos la acción  REPORTAR CASO POR EXCEPCIÓN - APROBACIÓN
            $description = "Se generó la acción reportar caso aceptado por excepción aprobación";
            $activityAction = ActionController::create(
                $activityGisId,
                ActionGisSort::REPORTAR_CASO_EXCEPCION_APRONACION,
                $description
            );

            //Buscamos la poliza
            $policySort = PolicySort::where('activity_id', $activityGis->parent_id)
                ->first();

            $activityPolicy = Activity::where('id', $activityGis->parent_id)
                ->first();

            $takerName = mb_convert_case(mb_strtolower($activityPolicy->affiliate->first_name ?? ''), MB_CASE_TITLE, "UTF-8");
            $affiliateName = mb_convert_case(mb_strtolower($activityGis->affiliate->first_name . ' ' . $activityGis->affiliate->last_name), MB_CASE_TITLE, "UTF-8");
            $emailTaker = ($activityPolicy->affiliate->email);
            $affiliateNumber = $activityGis->affiliate->doc_number;
            $caseGis = ActivityAction::where('activity_id', $activityGis->id)
                ->where('action_id', ActionGisSort::REPORTAR_ORIGEN)
                ->first();

            $numberCaseGis = $caseGis ? $activityGis->gis_sort->consecutive : $activityGis->gis_sort->consecutive_gis;

            //Enviamos correo electronico al afiliado, tomador y al intermediario
            $emails = [
                $activityPolicy->affiliate->email,
                // $activityGis->affiliate->email,
                $policySort->email
            ];

            // Remover correos vacios
            $emails = array_filter($emails);

            //separados por comas
            //$emailsString = implode(',', $emails);

            $id = $policySort->formatSortNumber();
            $case = $activityGis->gis_sort->formatCaseNumber();
            $date_case = Carbon::parse($activityGis->gis_sort->date_accident)->format('d/m/Y');
            //El titulo del correo 
            $subject = "Caso aceptado por excepción en la póliza #$id";

            $text = "<b>Señor(es)</b> 
                    <b>$takerName </b>
                    <b>Correo electrónico: $emailTaker </b>
                    <b>Póliza $id </b>
                    <b>Caso: $numberCaseGis</b>

                <b>Referencia del caso:</b> Caso aceptado por excepción en la póliza #$id 

                Estimado(s) señor(es):

                Nos complace informarle que, tras una revisión exhaustiva, hemos aceptado el caso de la persona asegurada:  $affiliateName, con número de identificación $affiliateNumber, realizando una excepción conforme al artículo 41 de nuestra norma técnica.

                Para llegar a esta decisión hemos considerado los siguientes criterios:
                
                1. Pago puntual de todas las primas por parte de su representada.
                2. Presentación oportuna de todas las declaraciones de planilla.
                3. Ausencia de casos no asegurados durante el período evaluado.
                4. Regularización de las deudas asociadas al Seguro Obligatorio de Riesgos del Trabajo.

                Además, deseamos aclararle que la aceptación del caso implica su inclusión en la siniestralidad de la póliza, con ajustes en los puntos por experiencia y la tarifa para los períodos siguientes al siniestro. Esto puede provocar variaciones en los resultados de la liquidación de cada período de vigencia de la póliza, generando primas adicionales que deberán ser pagadas según lo establecido en nuestra norma técnica, para lo cual le informaremos oportunamente.

                Para proceder con la aceptación del caso, por favor, le solicitamos que reporte a la persona trabajadora mediante una planilla adicional para el período en que se omitió el aseguramiento, la cual debe ser cargada a través de nuestro sitio web.
                
                Si tiene alguna consulta o necesita más detalles sobre este caso, por favor, contáctenos al 4102-7600. ¡Será un gusto servirle!

                Agradecemos su confianza y colaboración. Nuestro propósito es fortalecer la prevención en salud y seguridad laboral del país.
                
                <b>Área de Indemnizaciones</b>
                <b>Seguro Obligatorio de Riesgos del Trabajo</b>
            ";
            $this->resendEmailReportAcceptedCaseException($req, $activityGis, $activityAction, $emails, $client->id, $subject, $text);
            DB::commit();
            return redirect("/tablero/auditoria_medica")->with('success', 'Acción ejecutada correctamente');
        } catch (Exception $e) {
            DB::rollback();
            return redirect("/tablero/auditoria_medica")->with('error', 'No se pudo ejecutar la acción');
        }
    }

    // Función resendEmail para enviar correos con varios documentos adjuntos
    public function resendEmailReportAcceptedCaseException(Request $req, $activityGis, $activityAction, $emails, $client_id, $subject, $text)
    {

        //Obtener información coreos tomadores autorizados
        $authorizedTakersData = collect();
        if ($activityGis) {
            $idActivityTaker = $activityGis->parent_id;
            $authorizedTakersData = $this->getAuthorizedTakerEmails($idActivityTaker);
        }

        $emailUsersTakerAuthorized = $authorizedTakersData->pluck('email');

        // Combina los emails iniciales con los emails de los usuarios autorizados
        $allEmails = collect($emails)
            ->merge($emailUsersTakerAuthorized)
            ->filter(function ($email) {
                return !empty($email);
            })
            ->unique()
            ->values();

        $finalEmailsArray = $allEmails->toArray();
        $validEmails = array_filter($finalEmailsArray, function ($email) {
            return filter_var($email, FILTER_VALIDATE_EMAIL);
        });
        // Lógica para el envío de correos
        if ($validEmails != null) {

            // Enviar el correo con los documentos adjuntos
            $mailSent = new SendDocumentDataBase(
                implode(',', $validEmails),
                $subject,
                "<EMAIL>",
                "Aceptación de caso no asegurado por excepción",
                [
                    "text" => $text,
                    "sender" => "MNK Seguros"
                ],
                "<EMAIL>",
                [],
                "send_document",
                $client_id,
                $req->getHost(),
                $activityGis,
                $activityAction,
                Service::SERVICE_GIS_SORT_MNK_SORT
            );

            // Capturar el resultado del envío
            $result = $mailSent->sendMail();

            $activityPolicy = Activity::query()
                ->where('id', $activityGis->parent_id)
                ->first();

            //Registramos los datos del correo enviado para la trazabilidad
            $mailBoardController = new MailBoardController();
            foreach ($validEmails as $email) {

                $takerAuthorizedId = null;
                $authorizedTaker = $authorizedTakersData->firstWhere('email', $email);
                if ($authorizedTaker) {
                    $takerAuthorizedId = $authorizedTaker->id;
                }
                $mailBoardController->createRegisterMail(
                    $activityGis->id,
                    $activityGis->service->id,
                    $activityPolicy->policy_sort->consecutive,
                    'Tomador',
                    $activityPolicy->affiliate->full_name,
                    $activityPolicy->affiliate->doc_number,
                    $subject,
                    $text,
                    $emails,
                    $result,
                    null,
                    $takerAuthorizedId
                );
            }

        }
    }

    //Guardamos los datos del accidente del caso 
    function saveDataAccidenteFurat(Request $req)
    {

        DB::beginTransaction();

        try {

            $gis = GisSort::find($req->gis_id);

            //Agregamos los datos del accidente del furat al GIS
            $gis->update($req->all());

            /// Decodifica el JSON de fieldData
            $fieldData = json_decode($req->fieldData, true);

            // Itera sobre los datos de fieldData y crea los registros en la tabla GisBodyPart
            GisBodyPart::where('gis_id', $req->gis_id)->delete();

            foreach ($fieldData as $input) {
                GisBodyPart::create([
                    'body_part_id' => $input['id'],
                    'body_part_name' => $input['value'],
                    'gis_id' => $req->gis_id,
                ]);
            }

            DB::commit();

            return response()->json([
                'message' => 'Accion generada correctamente',
            ]);
        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'message' => 'Errores durante el procesamiento del cargue manual',
                'e' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Acción SOLICITAR EMISIÓN FURAT - FIRMA Fisica
     *
     * @param Request $req
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function requestIssuancePhysicalSignature(Request $req, $cpath, $id)
    {
        DB::beginTransaction();
        try {
            $client = Client::where('path', $cpath)->firstOrFail();
            $activity = Activity::where('client_id', $client->id)->where('id', $req->id)->firstOrFail();
            $gis_sort = $activity->gis_sort;
            if ($req->file('pdfSignature')) {
                //GUARDAR FIRMA FISICA EN S3
                $file = $req->file('pdfSignature');
                $path = "activity_action_document/firma_fisica_gis_{$activity->id}.pdf";
                Storage::disk('s3')->put($path, file_get_contents($file->getRealPath()));
                $gis_sort->sign_holder = $path;
                $gis_sort->type_sign = 'physical';
                $gis_sort->save();

                //Ejecutamos la acción SOLICITAR_EMISION_FIRMA_FISICA
                $description = 'Solicitar caso firma fisica';
                $activityAction = ActionController::create($activity->id, ActionGisSort::SOLICITAR_EMISION_CASO_FIRMA_FISICA, $description);

                $activityActionDocument = new ActivityActionDocument();
                $activityActionDocument->activity_action_id = $activityAction->id;
                $activityActionDocument->name = 'Solicitar emisión firma física gis';
                $activityActionDocument->path = $gis_sort->sign_holder;
                $activityActionDocument->save();

                DB::commit();
                return response()->json([
                    'message' => 'Accion generada correctamente',
                    'fileUrl' => $path
                ]);
            } else {
                return response()->json([
                    'message' => 'No ha realizado la firma fisica',
                ], 500);
            }
        } catch (Exception $e) {
            DB::rollback();
            return response()->json([
                'message' => 'Errores durante el procesamiento del cargue manual',
                'e' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Acción SOLICITAR EMISIÓN FURAT - FIRMA DIGITAL
     *
     * @param Request $req
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function requestIssuanceDigitalSignature(Request $req, $cpath, $id)
    {
        DB::beginTransaction();
        try {
            $client = Client::where('path', $cpath)->firstOrFail();
            $activity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();
            $gis_sort = $activity->gis_sort;

            $path = '';


            if ($req->has('sign') && !empty($req->input('sign'))) {
                $image_request = $req->input('sign'); // base64 string
                $image_decode = base64_decode(preg_replace('#^data:image/\w+;base64,#i', '', $image_request));
                $path = "gist_sort/sign_digital_" . $id . ".png";
                Storage::disk('s3')->put($path, $image_decode);
            } elseif ($gis_sort->sign_holder) {

                $path = $gis_sort->sign_holder;
            } else {
                DB::rollback();
                return response()->json([
                    'message' => 'No ha realizado la firma digital',
                ], 500);
            }


            $gis_sort->sign_holder = $path;
            $gis_sort->type_sign = 'digital';
            $gis_sort->save();

            //Ejecutamos la acción SOLICITAR_EMISION_FIRMA_FISICA
            $description = 'Solicitar emisión caso firma digita';
            $activityAction = ActionController::create($activity->id, ActionGisSort::SOLICITAR_EMISION_CASO_FIRMA_DIGITAL, $description);
            $image = Storage::disk('s3')->url($path);

            $pdf = PDF::loadView("services.gis.docs.single_accident_format_pdf", [
                'firmaPath' => $image,
            ]);

            Storage::disk('s3')
                ->put("activity_action_document/firma_digital_gis_{$activity->id}.pdf", $pdf->output());

            $activityActionDocument = new ActivityActionDocument();
            $activityActionDocument->activity_action_id = $activityAction->id;
            $activityActionDocument->name = 'Solicitar emisión firma digital gis';
            $activityActionDocument->path = "activity_action_document/firma_digital_gis_{$activity->id}.pdf";
            $activityActionDocument->save();

            DB::commit();
            return response()->json([
                'message' => 'Accion generada correctamente',
            ]);
        } catch (Exception $e) {
            DB::rollback();
            return response()->json([
                'message' => 'Errores durante el procesamiento',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function downloadFormatSignature(Request $req, $cpath, $id)
    {
        try {
            $client = Client::where('path', $cpath)->firstOrFail();
            $activity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();
            $affiliate = Affiliate::where('id', $activity->affiliate_id)->first();

            $data = [
                'name' => $affiliate->first_name,
                'idNumber' => $affiliate->doc_number,
                'email' => $affiliate->email,
                'invoiceEmail' => $affiliate->electronic_billing_email,
                'province' => '',
                'canton' => '',
                'district' => '',
                'economicActivity' => '',
                'legalRepresentativeName' => '',
                'legalRepresentativeId' => '',
                'sign1_url' => '',
                'sign2_url' => '',
                'puesto_tomador' => $affiliate->occupation_responsible,
                'name_tomador' => $affiliate->first_name,
                'employer_address' => $affiliate->employer_address,
                'phone' => $affiliate->phone,
                'cellphone' => $affiliate->cellphone,
            ];

            $document = 'downloadPhysicalSignature_pdf';
            $pdf = PDF::loadView("services.policy_sort.docs.{$document}", [
                'data' => $data,
                'watermark' => false,
            ]);


            return $pdf->download('gis_sort.pdf');
        } catch (Exception $e) {
            return response()->json([
                'message' => 'Errores durante el procesamiento',
                'e' => $e->getMessage(),
            ], 500);
        }
    }

    public function getDocumentSignature(Request $request, $cpath, $id)
    {
        try {

            $client = Client::where('path', $cpath)->firstOrFail();

            $activity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();


            if (!isset($activity)) {
                return response()->json(['message' => 'Activity del servicio de gis sort no encontrada.'], 404);
            }


            if (!$activity->gis_sort->sign_holder) {
                return response()->json(['message' => 'Documento no Firmado.'], 200);
            }

            $sign_document = $activity->gis_sort->sign_holder;

            if ($sign_document && $activity->gis_sort->type_sign == 'physical') {

                // $document_path = storage_path('app/' . $sign_document);
                // // dd($document_path);
                $document_path = Storage::disk('s3')->url($sign_document);

                // Retorna la URL para la descarga directa
                return response()->json([
                    'document' => $document_path
                ], 200);
            }

            return response()->json(['message' => 'No se ha guardado ningún documento.'], 200);
        } catch (Exception $e) {
            return response()->json([
                'message' => 'Errores durante el procesamiento',
                'e' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * ACCIONES CARGUE SOPORTES O REPORTAR FURAT SIN SOPORTES
     *
     * @param Request $req
     * @param int $id
     *
     */
    public function loadMedia(Request $request, $cpath, $id)
    {

        $client = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();
        $activity_policy_sort = $activity->parent_activity;
        // Definir las reglas de validación
        $validator = Validator::make($request->all(), [
            'document1' => 'nullable|file|mimes:pdf|max:10240', // Max 10 MB
            'document2' => 'nullable|file|mimes:pdf|max:10240',
            'document3' => 'nullable|file|mimes:pdf|max:10240',
            'document4' => 'nullable|file|mimes:pdf|max:10240',
            'document5' => 'nullable|file|mimes:pdf|max:10240',
        ]);

        // Ejecutar la validación
        if ($validator->fails()) {
            // Recoger los errores
            $errors = $validator->errors();

            // Crear un mensaje de error personalizado
            $errorMessage = "Error en el cargue de documentos: ";

            // Mapeo de nombres de documentos
            $documentNames = [
                'document1' => 'Soporte 1',
                'document2' => 'Soporte 2',
                'document3' => 'Soporte 3',
                'document4' => 'Soporte 4',
                'document5' => 'Soporte 5',
            ];

            // Verificar qué documentos están fallando
            foreach ($errors->keys() as $key) {
                $documentName = $documentNames[$key] ?? $key; // Obtener el nombre del documento en español
                $errorMessage .= "$documentName no es válido. "; // Agregar el mensaje personalizado
            }

            return redirect()->back()->with('error', $errorMessage);
        }

        //Se redirecciona a otra vsta, si el estado de actividad no es correspondiente
        if ($activity->state_id !== StateGis::CASO_AVISADO_PRENDIENTE_REPORTE_FORMAL && $activity->state_id !== StateGis::PENDIENTE_ENTREGA_INFORMACION_EXTRA) {
            return redirect('/tomador/poliza/' . $activity_policy_sort->affiliate_id . '/datos');
        }
        //Lista de los documentos
        $documentFields = [
            'document1' => GisSort::DOCUMENTO_SOPORTE_1,
            'document2' => GisSort::DOCUMENTO_SOPORTE_2,
            'document3' => GisSort::DOCUMENTO_SOPORTE_3,
            'document4' => GisSort::DOCUMENTO_SOPORTE_4,
            'document5' => GisSort::DOCUMENTO_SOPORTE_5,
        ];

        DB::beginTransaction();
        try {
            // Verificar si algún archivo fue cargado
            foreach ($documentFields as $documentField => $documentId) {
                if ($request->hasFile($documentField)) {
                    // Subir el documento a S3
                    $file = $request->file($documentField);
                    $filePath = "activity_documents/{$documentField}_{$activity->id}.pdf";

                    Storage::disk('s3')->put($filePath, file_get_contents($file->getRealPath()));

                    // Guardar el documento en AcitivityDocument
                    $activityDocument = new ActivityDocument();
                    $activityDocument->activity_id = $activity->id;
                    $activityDocument->document_id = $documentId;
                    $activityDocument->path = $filePath;
                    $activityDocument->uploaded_at = now();
                    $activityDocument->save();
                }
            }

            $document_report = $this->reportGis($request, $cpath, $id);

            if ($activity->state_id === StateGis::PENDIENTE_ENTREGA_INFORMACION_EXTRA) {
                //Cambiamos el estado de la actividad
                $description = "REPORTAR SOPORTES";
                ActionController::create(
                    $activity->id,
                    ActionGisSort::REPORTAR_SOPORTES,
                    $description
                );
                DB::commit();
                //Se retornar al usuario al tablero tomador
                return redirect('/tomador/poliza/' . $activity_policy_sort->affiliate_id . '/datos');
            }

            $path = "activity_action_document/registro-siniestro-{$activity->id}.pdf";

            Storage::disk('s3')
                ->put($path, $document_report->output());


            //formamos archivo
            $files[] = [
                'type' => 'pdf',
                'path' => $path,
                'name' => 'registro_siniestro' . '.pdf',
            ];
            //Obtener información tomador autorizado
            $authorizedTakersData = collect();
            if ($activity_policy_sort) {
                $idActivityTaker = $activity_policy_sort->id;
                $authorizedTakersData = $this->getAuthorizedTakerEmails($idActivityTaker);
            }
            $emailUsersTakerAuthorized = $authorizedTakersData->pluck('email');

            // Si no se subió ningún documento, ejecutar acción
            if (
                !$request->hasFile('document1') && !$request->hasFile('document2') &&
                !$request->hasFile('document3') && !$request->hasFile('document4') &&
                !$request->hasFile('document5')
            ) {
                // Ejecutar acción Reportar formato formal del caso sin soportes
                $activityAction = ActionController::create($activity->id, ActionGisSort::REPORTAR_FORMATO_FORMAL_CASO_SIN_SOPORTES, 'Reportar formato formal del caso sin soportes');

                $nameTaker = ucwords(strtolower($activity_policy_sort->affiliate->first_name));
                $nameAffiliate = ucwords(strtolower($activity->affiliate->first_name));
                $identificationAffiliate = $activity->affiliate->doc_number;
                $typeEvent = $activity->gis_sort->type_report;
                $dateAccident = $activity->gis_sort->date_accident;
                $hourAccident = $activity->gis_sort->hour_accident;


                $activityActionDocument = new ActivityActionDocument();
                $activityActionDocument->activity_action_id = $activityAction->id;
                $activityActionDocument->name = 'registro siniestro';
                $activityActionDocument->path = $path;
                $activityActionDocument->save();


                //Enviamos correo al tomador y afiliado
                $emails = [
                    $activity->affiliate->email,
                    $activity_policy_sort->affiliate->email
                ];

                // Remover correos duplicados y vacíos (opcional)
                $emails = array_filter(array_unique($emails));

                // Combina los emails iniciales con los emails de los usuarios autorizados
                $allEmails = collect($emails)
                    ->merge($emailUsersTakerAuthorized)
                    ->filter(function ($email) {
                        return !empty($email);
                    })
                    ->unique()
                    ->values();

                $finalEmailsArray = $allEmails->toArray();
                $validEmails = array_filter($finalEmailsArray, function ($email) {
                    return filter_var($email, FILTER_VALIDATE_EMAIL);
                });

                $subject = "Recepción del reporte por $typeEvent laboral - Caso # " . $activity->gis_sort->formatCaseNumber();

                $text = "¡Buen día, $nameTaker!
                        
                            Le informamos que hemos recibido su “Reporte de accidente o enfermedad de trabajo y solicitud de atención médica” asociado a la póliza {$activity_policy_sort->policy_sort->formatNumberConsecutive()}. Los detalles de la persona asegurada son:
                        
                            Nombre: $nameAffiliate
                            Número de identificación: $identificationAffiliate
                            Tipo de evento:$typeEvent
                            Fecha y hora del accidente:  $dateAccident  a las  $hourAccident 
                        
                            Estamos procesando su solicitud y le contactaremos en caso de requerir información adicional.
                            Si tiene alguna consulta sobre este tema, por favor comuníquese con nosotros al 4102-7600. Será un gusto servirle y apoyarle en este momento.
                        
                            La salud y bienestar de las personas trabajadoras son de suma importancia para nosotros. Queremos asegurarle que estamos aquí para brindarle todo el acompañamiento necesario durante este proceso.
                            Nuestro propósito es garantizar la protección y bienestar de sus colaboradores así como brindarle la experiencia de servicio que usted y ellos merecen.
                            ";

                if(!empty($validEmails)){

                    $mailSent = new SendDocumentDataBase(
                        implode(',',$validEmails),
                        "Recepción del reporte por $typeEvent laboral - Caso # " . $activity->gis_sort->formatCaseNumber(),
                        "<EMAIL>",
                        "Recepción del reporte de accidente o enfermedad laboral - Caso # " . $activity->gis_sort->formatCaseNumber(),
                        [
                            "text" => $text,
                            "sender" => 'MNK seguros'
                        ],
                        "<EMAIL>",
                        $files,
                        "send_document_db",
                        $client,
                        request()->getHost(),
                        $activity->id,
                        $activityAction->id,
                        $activity->service->id
                    );

                    $result = $mailSent->sendMail();

                    //Registramos los datos del correo enviado para la trazabilidad
                    $mailBoardController = new MailBoardController();

                    foreach ($validEmails as $email) {

                        $takerAuthorizedId = null;
                        $authorizedTaker = $authorizedTakersData->firstWhere('email', $email);

                        if ($authorizedTaker) {
                            $takerAuthorizedId = $authorizedTaker->id;
                        }

                        $mailBoardController->createRegisterMail(
                            $activity->id,
                            $activity->service->id,
                            $activity_policy_sort->policy_sort->consecutive,
                            'Tomador',
                            $nameTaker,
                            $activity_policy_sort->affiliate->doc_number,
                            $subject,
                            $text,
                            $emails,
                            $result,
                            $files,
                            $takerAuthorizedId
                        );
                    }
                }

                // Capturar el resultado del envío


            } else {
                // Ejecutar acción Reportar formato formal del caso
                $activityAction = ActionController::create($activity->id, ActionGisSort::REPORTAR_FORMATO_FORMAL_CASO, 'Reportar formato formal del caso');
                $nameTaker = ucwords(strtolower($activity_policy_sort->affiliate->first_name));
                $nameAffiliate = ucwords(strtolower($activity->affiliate->first_name));
                $identificationAffiliate = $activity->affiliate->doc_number;
                $typeEvent = $activity->gis_sort->type_report;
                $dateAccident = $activity->gis_sort->date_accident;
                $hourAccident = $activity->gis_sort->hour_accident;


                $activityActionDocument = new ActivityActionDocument();
                $activityActionDocument->activity_action_id = $activityAction->id;
                $activityActionDocument->name = 'registro siniestro';
                $activityActionDocument->path = $path;
                $activityActionDocument->save();

                //Enviamos correo al tomador y afiliado
                $emails = [
                    $activity->affiliate->email,
                    $activity_policy_sort->affiliate->email
                ];

                // Remover correos duplicados y vacíos (opcional)
                $emails = array_filter(array_unique($emails));

                // Combina los emails iniciales con los emails de los usuarios autorizados
                $allEmails = collect($emails)
                    ->merge($emailUsersTakerAuthorized)
                    ->filter(function ($email) {
                        return !empty($email);
                    })
                    ->unique()
                    ->values();

                $finalEmailsArray = $allEmails->toArray();
                $validEmails = array_filter($finalEmailsArray, function ($email) {
                    return filter_var($email, FILTER_VALIDATE_EMAIL);
                });

                $subject = "Recepción del reporte por $typeEvent laboral - Caso # " . $activity->gis_sort->formatCaseNumber();

                $text = "¡Buen día, $nameTaker!
                        
                            Le informamos que hemos recibido su “Reporte de accidente o enfermedad de trabajo y solicitud de atención médica” asociado a la póliza {$activity_policy_sort->policy_sort->formatNumberConsecutive()}. Los detalles de la persona asegurada son:
                        
                            Nombre: $nameAffiliate
                            Número de identificación: $identificationAffiliate
                            Tipo de evento:$typeEvent
                            Fecha y hora del accidente:  $dateAccident  a las  $hourAccident 
                        
                            Estamos procesando su solicitud y le contactaremos en caso de requerir información adicional.
                            Si tiene alguna consulta sobre este tema, por favor comuníquese con nosotros al 4102-7600. Será un gusto servirle y apoyarle en este momento.
                        
                            La salud y bienestar de las personas trabajadoras son de suma importancia para nosotros. Queremos asegurarle que estamos aquí para brindarle todo el acompañamiento necesario durante este proceso.
                            Nuestro propósito es garantizar la protección y bienestar de sus colaboradores así como brindarle la experiencia de servicio que usted y ellos merecen.
                            ";

                if(!empty($validEmails)){

                    $mailSent = new SendDocumentDataBase(
                        implode(',',$validEmails),
                        "Recepción del reporte por $typeEvent laboral - Caso # " . $activity->gis_sort->formatCaseNumber(),
                        "<EMAIL>",
                        "Recepción del reporte de accidente o enfermedad laboral - Caso # " . $activity->gis_sort->formatCaseNumber(),
                        [
                            "text" => $text,
                            "sender" => 'MNK seguros'
                        ],
                        "<EMAIL>",
                        $files,
                        "send_document_db",
                        $client,
                        request()->getHost(),
                        $activity->id,
                        $activityAction->id,
                        $activity->service->id
                    );
                    // Capturar el resultado del envío
                    $result = $mailSent->sendMail();

                    //Registramos los datos del correo enviado para la trazabilidad
                    $mailBoardController = new MailBoardController();

                    foreach ($validEmails as $email) {

                        $takerAuthorizedId = null;
                        $authorizedTaker = $authorizedTakersData->firstWhere('email', $email);

                        if ($authorizedTaker) {
                            $takerAuthorizedId = $authorizedTaker->id;
                        }
                        $mailBoardController->createRegisterMail(
                            $activity->id,
                            $activity->service->id,
                            $activity_policy_sort->policy_sort->consecutive,
                            'Tomador',
                            $nameTaker,
                            $activity_policy_sort->affiliate->doc_number,
                            $subject,
                            $text,
                            $emails,
                            $result,
                            $files,
                            $takerAuthorizedId
                        );
                    }
                }
            }

            // Si la condición es fallecido creamos el servicio PEMT
            if ($activity->gis_sort->conditions === 'Fallecido') {
                $this->ReportDeathAffiliate($activity->gis_sort->id, $activity, $client);
            }

            // Si la condición es desaparecido generamos la acción
            if ($activity->gis_sort->conditions === 'Desaparecido') {
                $this->ReportMissingffiliate($activity->gis_sort->id, $activity, $client);
            }


            DB::commit();
            return redirect('/tomador/poliza/' . $activity_policy_sort->affiliate_id . '/datos');
        } catch (Exception $e) {
            DB::rollback();
            return response()->json([
                'message' => 'Errores durante el procesamiento',
                'e' => $e->getMessage(),
            ], 500);
        }
    }

    public function reportGis(Request $req, $cpath, $id)
    {


        try {
            $client = Client::where('path', $cpath)->firstOrFail();
            $activity = Activity::with('affiliate', 'parent_activity')->where('client_id', $client->id)->where('id', $id)->firstOrFail();
            $gis = GisSort::where('activity_id', $activity->id)->firstOrFail();
            $fieldData = GisBodyPart::where('gis_id', $gis->id)->pluck('body_part_name')->toArray();

            $activity_service_medical = Activity::with('medical_services_sort')->where('service_id', 83)->where('parent_id', $activity->id)->first();

            $follow_up = MedicalServiceFollowUp::where('medical_services_sort_id', $activity_service_medical->medical_services_sort->id)
                ->first();

            $signature_document_digital = $activity->parent_activity->activity_documents->where('document_id', ServiceDocument::SOLICITUD_EMISIÓN_SORT_FIRMA_DIGITAL)
                ->first();

            $policiSort = $activity->parent_activity;

            //asignar null de momento
            $sign_policyholder_url = null;

            //validar si existe la firma digital
            if($signature_document_digital && $policiSort->policy_sort)
            {
                $sign_policyholder_url = $policiSort->policy_sort->sign_policy_holder;
                //validar si es nullo
                if($sign_policyholder_url != null)
                {
                    $sign_policyholder_url = Storage::disk('s3')->url($policiSort->policy_sort->sign_policy_holder);
                }
            }

            $start_date_of_incapacity = $follow_up ? $follow_up->start_date_of_incapacity : '';


            $lastThreeMonths = PolicySpreadsheetAffiliate::query()
                ->where('affiliate_id', $activity->affiliate_id)
                ->orderBy('id', 'desc')
                ->limit(3)
                ->select('policy_spreadsheet_id', 'monthly_salary', 'days', 'created_at', 'work_shift_type')
                ->get();

            $ubicacion = $this->policySortController->getLocationNamesFromJson($activity->affiliate->province, $activity->affiliate->canton, $activity->affiliate->district);
            $jsonPath = public_path('js/paises.json');

            $jsonContent = File::get($jsonPath);
            $countries = json_decode($jsonContent, true);

            $country = $this->policySortController->getCountryName($activity->affiliate->country, $countries);

            $affiliateLastname = isset($activity->affiliate->last_name)
                ? explode(' ', trim($activity->affiliate->last_name))
                : [];

            $data = [
                'case_number' => $gis->consecutive,
                'affiliate' => $activity->affiliate,
                'policy_sort' => $policiSort,
                'gis_activity' => $activity,
                'ubicacion' => $ubicacion,
                'country' => $country,
                'affiliateLastname' => $affiliateLastname,
                'start_date_of_incapacity' => $start_date_of_incapacity,
                'firma_tomador' => $sign_policyholder_url,
                'follow_up' => $follow_up,
                'planilla' => $lastThreeMonths
            ];

            $document = 'reporte_accidente';

            $pdf = PDF::loadView("services.gis.docs.{$document}", ['fieldData' => $fieldData, 'data' => $data]);


            return $pdf;
        } catch (Exception $e) {
            return response()->json([
                'message' => 'Errores durante el procesamiento',
                'e' => $e->getMessage(),
            ], 500);
        }
    }



    public function reportGisrequest(Request $req, $cpath, $id)
    {

        $pdf = $this->reportGis($req, $cpath, $id);

        return $pdf->download('registro_siniestro.pdf');
    }


    //Action REPORTAR MUERTE DE AFILIADO - APROBACIÓN MS-1838
    public function reportMemberDeathApproval(Request $request, $cpath, $id)
    {
        try {
            // Iniciar la transacción
            DB::beginTransaction();

            //Buscamos el cliente asociado
            $client = Client::where('path', $cpath)->firstOrFail();

            //Buscamos la actividad del GIS
            $activity = Activity::where('client_id', $client->id)
                ->where('id', $id)
                ->firstOrFail();

            //Ejecutamos la acción reportar muerte del afiliado aprobación
            ActionController::create(
                $activity->id,
                ActionGisSort::REPORTAR_MUERTE_AFILIADO_APROBACION,
                'Se reportó muerte del asegurado'
            );

            // Crear la actividad para el servicio PEMPT SORT
            $activityPempt = new Activity();
            $activityPempt->parent_id = $activity->id;
            $activityPempt->state_id = State::REGISTRADO;
            $activityPempt->affiliate_id = $activity->affiliate_id;
            $activityPempt->service_id = Service::SERVICE_PE_MPT_SORT_MNK;
            $activityPempt->user_id = auth()->user()->id;
            $activityPempt->client_id = $client->id;
            $activityPempt->save();

            // Crear el servicio de PRESTACIONES MEDICAS SORT
            $servicePempt = new PeMptSort();
            $servicePempt->activity_id = $activityPempt->id;
            $servicePempt->save();

            $controller = new PeMptSortController();
            $controller->registradorMptIntegrado($request, $cpath, $activityPempt->id);

            // Confirmar la transacción si todo sale bien
            DB::commit();

            return redirect("/tablero/auditoria_medica")->with('success', 'Acción ejecutada correctamente');
        } catch (Exception $e) {
            // Revertir la transacción en caso de error
            DB::rollBack();

            // Devolver mensaje de error
            return redirect("/tablero/auditoria_medica")->with('error', 'No se pudo ejecutar la acción');
        }
    }

    //REPORTAR MUERTE DE AFILIADO - RECHAZO
    public function reportMemberDeathRejection(Request $request, $cpath, $id)
    {

        try {
            // Iniciar la transacción
            DB::beginTransaction();

            $client = Client::where('path', $cpath)->firstOrFail();
            $activity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();
            $activityAction = ActionController::create($activity->id, ActionGisSort::REPORTAR_MUERTE_AFILIADO_RECHAZO, '');
            $activity_policy_sort = $activity->parent_activity;
            $policy_sort = $activity->parent_activity->policy_sort->formatSortNumber();

            $nameTaker = mb_convert_case(mb_strtolower($activity_policy_sort->afiliate->first_name ?? ''), MB_CASE_TITLE, "UTF-8");
            $nameAffiliate = mb_convert_case(mb_strtolower($activity->affiliate->first_name . ' ' . $activity->affiliate->last_name), MB_CASE_TITLE, "UTF-8");
            $identificationAffiliate = $activity->affiliate->doc_number;

            // Enviar correo al tomador y afiliado
            $emails = [
                $activity->affiliate->email,
                $activity_policy_sort->affiliate->email
            ];
            $emails = array_filter(array_unique($emails));
            $emailsString = implode(',', $emails);

            $subject = "Caso no amparado en la póliza #" . $policy_sort;

            $text = "¡Buen día, $nameTaker!
                
                Lamentamos informarle que, tras la revisión de nuestra parte, hemos identificado que la persona: $nameAffiliate, con número de identificación $identificationAffiliate, no cumple con los criterios para ser amparada bajo nuestro contrato. Por esta razón, estamos remitiendo a la persona trabajadora al régimen correspondiente para su atención.

                De acuerdo con el artículo 40 de nuestra norma técnica, conservamos el derecho de solicitar al lesionado o al régimen correspondiente el pago de los gastos incurridos por las prestaciones otorgadas, que no corresponden a un riesgo cubierto por nuestra representada, conforme al principio básico de protección a la vida y la salud.

                Si tiene alguna consulta o necesita más detalles sobre este caso, por favor, contáctenos al 4102-7600. ¡Será un gusto servirle!

                Nuestro propósito es fortalecer la prevención en salud y seguridad laboral del país, así como proteger a sus colaboradores en el momento que más lo necesitan, generando siempre bienestar.
            ";

            $mailSent = new SendDocumentDataBase(
                $emailsString,
                "Caso no amparado en la póliza #" . $policy_sort,
                "<EMAIL>",
                "Caso no amparado en la póliza #" . $policy_sort,
                [
                    "text" => $text,
                    "sender" => "mnk aseguramiento"
                ],
                "<EMAIL>",
                [],
                "send_document_db",
                $client,
                request()->getHost(),
                $activity->id,
                $activityAction->id,
                $activity->service->id
            );

            // Capturar el resultado del envío
            $result = $mailSent->sendMail();

            //Registramos los datos del correo enviado para la trazabilidad
            $mailBoardController = new MailBoardController();
            $mailBoardController->createRegisterMail(
                $activity->id,
                $activity->service->id,
                $activity_policy_sort->policy_sort->consecutive,
                'Tomador',
                $nameTaker,
                $activity_policy_sort->affiliate->doc_number,
                $subject,
                $text,
                $emails,
                $result,
                null
            );

            DB::commit();

            return redirect("/tablero/auditoria_medica")->with('success', 'Acción ejecutada correctamente');
        } catch (Exception $e) {
            DB::rollBack();
            return redirect("/tablero/auditoria_medica")->with('error', 'No se pudo ejecutar la acción');
        }
    }

    public function reportMonitoringControl(Request $req, $cpath, $id)
    {
        DB::beginTransaction();
        try {
            //Buscamos el cliente
            $client = Client::where('path', $cpath)->firstOrFail();

            //Buscamos la actividad de GIS
            $gisActivity = Activity::where('client_id', $client->id)->where('id', $id)->first();

            //Validamos si existe la actividad de GIS
            if (empty($gisActivity)) {
                $errors = "No se pudo encontrar la actividad de GIS";
                throw new \Exception($errors); // Lanzar excepción con el error
            }

            //Buscamos GIS SORT
            $gisSort = GisSort::where('activity_id', $gisActivity->id)->first();

            //validar si existe GIS
            if (empty($gisSort)) {
                $errors = "No se pudo encontrar el GIS";
                throw new \Exception($errors); // Lanzar excepción con el error
            }

            //Validar si se seleccionó tipo de seguimiento
            if (empty($req->tipo_seguimiento) || $req->tipo_seguimiento == null || $req->tipo_seguimiento == "") {
                $errors = "No selecciono el tipo de seguimiento para el # de siniestralidad: " . $gisSort->id;
                throw new \Exception($errors); // Lanzar excepción con el error
            }

            //Guardamos el campo tipo de seguimiento
            $gisSort->update([
                'type_tracking' => $req->tipo_seguimiento
            ]);

            //Ejcutamos la acción Reportar seguimiento monitoreo y control
            $description = "Se ejecuto la acción reportar seguimiento monitoreo y control - " . $req->tipo_seguimiento;
            $activityActionsCreated = ActionController::create(
                $gisActivity->id,
                ActionGisSort::REPORTAR_SEGUIMIENTO_MONITOREO_CONTROL,
                $description
            );

            DB::commit();

            return redirect('/tablero/monitereo_y_control')->with('success', 'Acción realizada exitosamente');
        } catch (\Exception $e) {
            // Hacer rollback si hay algún error
            DB::rollBack();

            // Capturar el mensaje de error
            $error = $e->getMessage();

            // Redirigir con el mensaje de error
            return redirect('/tablero/monitereo_y_control')->withErrors($error)->withInput();
        }
    }

    //Envio de email para la cción reportar provvedore de atencion primaria
    public function resendEmailReportProveedor(Request $req, $activityGis, $activity_service, $activityAction, $emails, $client_id, $files, $activityPolicy)
    {
        $nameTaker = mb_convert_case(mb_strtolower($activityPolicy->affiliate->first_name ?? ''), MB_CASE_TITLE, "UTF-8");
        $nameAffiliate = mb_convert_case(mb_strtolower($activityGis->affiliate->full_name ?? ''), MB_CASE_TITLE, "UTF-8");
        $numberIdentification = $activityGis->affiliate->doc_number;
        $typeEvent = $activityGis->gis_sort->type_report;
        $id = $activityPolicy->policy_sort->formatNumberConsecutive();

        //Obtener informacion del tomador autorizado
        $authorizedTakersData = collect();
        if ($activityPolicy) {
            $idActivityTaker = $activityPolicy->id;
            $authorizedTakersData = $this->getAuthorizedTakerEmails($idActivityTaker);
        }
        $emailUsersTakerAuthorized = $authorizedTakersData->pluck('email');
        // Combina los emails iniciales con los emails de los usuarios autorizados
        $allEmails = collect($emails)
            ->merge($emailUsersTakerAuthorized)
            ->filter(function ($email) {
                return !empty($email);
            })
            ->unique()
            ->values();

        $finalEmailsArray = $allEmails->toArray();
        $validEmails = array_filter($finalEmailsArray, function ($email) {
            return filter_var($email, FILTER_VALIDATE_EMAIL);
        });

        $body = "¡Buen día, $nameTaker!\n\n" .
            "Lamentamos mucho lo ocurrido y estamos aquí para apoyarle. Le confirmamos que hemos recibido su reporte de accidente asociado a la póliza #$id. Los detalles de la persona asegurada son:" .
            "<ul>" .
            "    <li>Nombre: $nameAffiliate</li>" .
            "    <li>Número de identificación: $numberIdentification</li>" .
            "    <li>Tipo de evento: $typeEvent</li>" .
            "</ul>" .
            "Estamos procesando su solicitud y le contactaremos en caso de requerir información adicional.\n\n" .
            "Si tiene alguna consulta sobre este tema, por favor, comuníquese con nosotros al 4102-7600. Será un gusto servirle y apoyarle en este momento.\n\n" .
            "La salud y bienestar de las personas trabajadoras son de suma importancia para nosotros. Queremos asegurarle que estamos aquí para brindarle todo el acompañamiento necesario durante este proceso.\n\n" .
            "Nuestro propósito es fortalecer la prevención en salud y seguridad laboral del país, así como proteger a sus colaboradores en el momento que más lo necesitan, generando siempre bienestar.\n";

        $subject = 'Hemos recibido un reporte de accidente - Caso #' . $activityGis->gis_sort->formatCaseNumber();

        // Lógica para el envío de correos
        if ($validEmails != null) {

            // Enviar el correo con los documentos adjuntos
            $mailSent = new SendDocumentDataBase(
                implode(',', $validEmails),
                $subject,
                "<EMAIL>", // Email de remitente
                "Denuncia de siniestro - Caso # " . $activityGis->gis_sort->formatCaseNumber(),
                [
                    "text" => $body,
                    "sender" => 'mnk indemnizaciones'
                ],
                "<EMAIL>",
                $files,
                "send_document_db",
                $client_id,
                $req->getHost(),
                $activityGis->id,
                $activityAction,
                $activity_service
            );

            // Capturar el resultado del envío
            $result = $mailSent->sendMail();
            //Registramos los datos del correo enviado para la trazabilidad
            $mailBoardController = new MailBoardController();

            foreach ($validEmails as $email) {

                $takerAuthorizedId = null;
                $authorizedTaker = $authorizedTakersData->firstWhere('email', $email);

                if ($authorizedTaker) {
                    $takerAuthorizedId = $authorizedTaker->id;
                }

                $mailBoardController->createRegisterMail(
                    $activityGis->id,
                    $activityGis->service->id,
                    $activityPolicy->policy_sort->consecutive,
                    'Tomador',
                    $nameTaker,
                    $activityPolicy->affiliate->doc_number,
                    $subject,
                    $body,
                    $emails,
                    $result,
                    null,
                    $takerAuthorizedId
                );
            }

        }
    }

    //Función automatica para buscar los casos reportados en desaprecidos y pasarlos a fallecidos
    public function ReportMissingMemberAutomatic(Request $request, $cpath)
    {

        $client = Client::where('path', $cpath)->firstOrFail();
        //En el Gis buscamos los afiliados con condición desaparecido y que tengan mas de 30 dias
        $affiliateMissing = GisSort::where('conditions', 'Desaparecido')
            ->whereDate('fecha_desaparicion', '<=', Carbon::now()->subDays(30))
            ->get();

        foreach ($affiliateMissing as $gis) {

            // Cambiar la condición a "Fallecido"
            $gis->conditions = 'Fallecido';
            $gis->save();

            $activityGis = $gis->activity;

            // reportamos el servicio  PEMT
            $this->ReportDeathAffiliateAutomatic($gis->id, $activityGis, $client);
        }

        return response()->json([
            'message' => 'Proveedores inactivados correctamente.'
        ], 200);
    }

    //Función automatica debe deshabilitar el reporte de FURAT no se puede hacer mayor a 8 días
    public function formalCaseFormatClosureAutomatic(Request $request)
    {
        try {
            DB::beginTransaction();

            // Obtener todas las actividades iniciales sin filtrar
            $activitiesGis = Activity::where('state_id', StateGis::CASO_AVISADO_PRENDIENTE_REPORTE_FORMAL)
                ->where('service_id', Service::SERVICE_GIS_SORT_MNK)
                ->get();

            // Inicializar colecciones para separar actividades que aplican (que se extendieron de los 8 días hábiles)
            $activitiesApplies = $activitiesGis->filter(function ($activity) {
                $dueDate = $this->calculateBusinessDays($activity->updated_at, 8);
                $now = Carbon::now()->format('Y-m-d'); // Obtiene la fecha actual en formato 'Y-m-d'
                return $now > $dueDate; // es decir que si la fecha actual es mayor que la fecha de vencimiento, se considera que se extendieron los 8 días hábiles
            });

            foreach ($activitiesApplies as $gis) {

                //Cierre por no reportar formato formal del caso 
                $description = "Cierre por no reportar formato formal del caso";
                ActionController::create($gis->id, ActionGisSort::CIERRE_NO_REPORTAR_FORMATO_FORMAL_CASO, $description);
            }

            DB::commit();

            return response()->json(['success' => true, 'message' => 'Cambios realizados'], 200);
        } catch (Exception $e) {
            DB::rollback();
            throw new Exception("Error al cierre por no reportar formato formal del caso automático: " . $e->getMessage());
        }
    }

    //Función automatica debe deshabilitar el reporte de FURAT no se puede hacer mayor a 8 días
    public function closingCaseAutomatic(Request $request)
    {

        try {
            DB::beginTransaction();

            $activityGis = Activity::where('state_id', StateGis::CASO_CERRADO_POR_NO_REPORTAR_EL_FORMATO_FORMAL_DEL_CASO)
                ->where('service_id', Service::SERVICE_GIS_SORT_MNK)
                ->where('updated_at', '<=', Carbon::now()->subYears(3))
                ->get();


            foreach ($activityGis as $gis) {

                //Cierre por no reportar formato formal del caso 
                $description = "Caso cerrado - por no reportar el formato formal del caso";
                ActionController::create($gis->id, ActionGisSort::CIERRE_DEL_CASO, $description);
            }

            DB::commit();

            return response()->json([
                'message' => 'Ejecución exitosa.',
            ], 200);
        } catch (Exception $e) {
            DB::rollback();
            throw new Exception("Error al cerrar caso - por no reportar el formato formal del caso automático: " . $e->getMessage());
        }
    }

    //Ejecutamos la acción automatica de reportar muerte de asegurado si han pasado mas de 30 dias despues de su desaparición
    public function ReportDeathAffiliateAutomatic($gisId, $activityGis, $client)
    {
        //Ejecutamos la acción REPORTAR MUERTE DE AFILIADO
        $description = "Se generó la acción reportar muerte afiliado";
        ActionController::create($activityGis->id, ActionGisSort::REPORTAR_MUERTE_AFILIADO, $description);

        //Creamos la actividad para el servicio PEMPT SORT
        $activityPempt = new Activity();
        $activityPempt->parent_id = $activityGis->id;
        $activityPempt->state_id = State::REGISTRADO;
        $activityPempt->affiliate_id = $activityGis->affiliate_id;
        $activityPempt->service_id = Service::SERVICE_PE_MPT_SORT_MNK;
        $activityPempt->user_id = auth()->user()->id;
        $activityPempt->client_id = $client->id;
        $activityPempt->save();

        //Creamos el servicio de PRESTACIONES MEDICAS SORT
        $servicePempt = new PeMptSort();
        $servicePempt->activity_id = $activityPempt->id;
        $servicePempt->save();
    }

    //REPORTAR AUDITORIA ADMINISTRATIVA - APROBACIÓN  MS-1783
    public function reportAdministrativeAuditApproval(Request $request, $cpath, $id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $activityGis = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();
        DB::beginTransaction();
        try {
            $activityAction = ActionController::create($activityGis->id, ActionGisSort::REPORTAR_AUDITORIA_ADMINISTRATIVA_APROBACION, 'Reportar auditora administrativa aprobación');
            //Enviamos el correo de aprobación de la auditoria administrativa al afiliado y al tomador

            $trabajador = ucwords(strtolower($activityGis->affiliate->first_name));
            $gis = $activityGis->gis_sort;
            $policy = $activityGis->parent_activity;

            $nameTaker = ucwords(strtolower($activityGis->parent_activity->affiliate->first_name));
            $nameAffiliate = ucwords(strtolower($activityGis->affiliate->first_name));
            $numberIdentification = $activityGis->affiliate->doc_number;
            $dateEvent = date('d/m/Y', strtotime($activityGis->gis_sort->date_accident));
            $emailData = TemplateBuilder::build(
                Templates::OCCUPATIONAL_RISK_CASE_ACCEPTANCE_NOTIFICATION,
                [
                    'name' => $nameTaker,
                    'name_affiliate' => $nameAffiliate,
                    'number_identification' => $numberIdentification,
                    'event_date' => $dateEvent,
                    'policy_sort' => $policy->policy_sort->formatNumberConsecutive(),
                    'case_number' => $gis->formatCaseNumber(),
                ]
            );

            $emails = [$activityGis->affiliate->email, $policy->policy_sort->email, $activityGis->parent->affiliate->email];

            $mailSent = new SendDocumentDataBase(
                implode(',', $emails),
                $emailData['subject'],
                "<EMAIL>",
                $emailData['subject'],
                [
                    "text" => $emailData['body'],
                    "sender" => $emailData['sender']
                ],
                "<EMAIL>",
                [],
                "send_document_db",
                $client->id,
                $request->getHost(),
                $activityGis,
                $activityAction,
                $activityGis->service_id
            );

            // Capturar el resultado del envío
            $result = $mailSent->sendMail();

            //Registramos los datos del correo enviado para la trazabilidad
            $mailBoardController = new MailBoardController();
            $mailBoardController->createRegisterMail(
                $activityGis->id,
                $activityGis->service->id,
                $activityGis->parent_activity->policy_sort->consecutive,
                'Asegurado',
                $trabajador,
                $activityGis->affiliate->doc_number,
                $emailData['subject'],
                $emailData['body'],
                $emails,
                $result,
                null
            );

            DB::commit();

            return redirect("/tablero/auditoria_medica")->with('success', 'Acción ejecutada correctamente');
        } catch (Exception $e) {
            DB::rollback();
            return redirect("/tablero/auditoria_medica")->with('error', 'No se pudo ejecutar la acción');
        }
    }

    //ACCION REPORTAR AUDITORIA ADMINISTRATIVA - RECHAZO MS-1784
    public function reportAdministrativeAuditRejection(Request $request, $cpath, $id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $activityGis = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();
        $activityPolicy = Activity::where('id', $activityGis->parent_id)
            ->first();
        DB::beginTransaction();
        try {
            $description = "Se ejecuto la acción reportar auditoria administrativa-rechazo";
            $activityAction = ActionController::create($activityGis->id, ActionGisSort::REPORTAR_AUDITORIA_ADMINISTRATIVA_RECHAZO, $description);

            $emails = $activityPolicy->affiliate->email;

            // Enviamos un correo al tomador reportando el caso no amparado
            $this->resendEmailNotificationCaseNotCovered(
                $request,
                $activityGis,
                $activityAction,
                $emails,
                $client->id,
                [],
                $activityPolicy
            );
            DB::commit();
            return redirect("/tablero/auditoria_medica")->with('success', 'Acción ejecutada correctamente');
        } catch (Exception $e) {
            DB::rollback();
            return redirect("/tablero/auditoria_medica")->with('error', 'No se pudo ejecutar la acción');
        }
    }


    //ACCION REPORTAR ORIGEN
    private function actionReportOrigin($request, $activityGis)
    {
        DB::beginTransaction();
        try {

            $gis = $activityGis->gis_sort;
            $gis->update([
                'test_name' => $request->test_name,
                'sustentation' => $request->sustentation,
                'doc_number' => $request->doc_number,
                'event_type' => $request->event_type,
                'siniestro_category' => $request->siniestro_category,
                'doc_date' => $request->doc_date,
                'severity' => $request->severity,
                'days_it' => $request->days_it,
                'qualification_date' => $request->qualification_date,
                'require_extra_information' => $request->require_extra_information,
            ]);

            $diagnostics = $request->input('diagnostics', []);
            

             $diagnosticos_completos = array_filter($diagnostics, function ($key) {
                return is_int($key);
            }, ARRAY_FILTER_USE_KEY);
            $diagnosticos = [];

            

            if ($diagnostics) {

                if (isset($diagnostics['id'])) {
                    $cantidad = count($diagnostics['id']); // Número de registros a procesar
                
                    for ($i = 0; $i < $cantidad; $i++) {
                        $diagnosticos[] = [
                            'id' => $diagnostics['id'][$i] ?? null,
                            'cod' => $diagnostics['cod'][$i] ?? null,
                            'description' => $diagnostics['description'][$i] ?? null,
                            'description_editable' => $diagnostics['description_editable'][$i] ?? null,
                            'laterality' => $diagnostics['laterality'][$i] ?? null,
                            'origin' => $diagnostics['origin'][$i] ?? null,
                            'diagnostic_status' => $diagnostics['diagnostic_status'][$i] ?? null,
                            'clasificacion' => $diagnostics['clasificacion'][$i] ?? null,
                        ];
                    }
                   

                    foreach ($diagnosticos as $diagnostico) {
                        
                           
                            
                            // Buscar el diagnóstico existente
                            $diagnostico_actualizar = MedicalServiceDiagnostics::find($diagnostico['id']);
                            var_dump($diagnostico_actualizar);
                            if ($diagnostico_actualizar) {
                                $diagnostico_actualizar->diagnostic_status = $diagnostico['diagnostic_status'] ?? $diagnostico_actualizar->diagnostic_status;
                                $diagnostico_actualizar->save();
                            }
                       

                    }
                    


                }

                GisDiagnostic::where('gis_id', $gis->id)->delete();
                foreach ($diagnosticos_completos as $diagnostic) {
                    GisDiagnostic::create([
                        'gis_id' => $gis->id,
                        'casedata_classif' => $diagnostic['calification'],
                        'casedata_code_cie' => $diagnostic['code'],
                        'casedata_diagnosis' => $diagnostic['diagnosis'],
                        'casedata_laterality' => $diagnostic['laterality'],
                        'origin' => $diagnostic['origin'],
                    ]);
                }

            }

            $activityActions = ActionController::create($activityGis->id, ActionGisSort::REPORTAR_ORIGEN, 'Reportar origen');

            //GUARDADO FORM DATOS DE LA NOTIFICACIÓN
            $this->save_datos_notificacion($request, $activityGis, $activityActions);
            DB::commit();
            return response()->json([
                'status' => 'success',
                'message' => 'Se completo la accion'
            ], 200);
        } catch (Exception $e) {
            DB::rollback();

            return response()->json([
                'status' => 'error',
                'message' => 'Ocurrió un error en el proceso'
            ], 500);
        }
    }

    //GUARDADO FORM DATOS DE LA NOTIFICACIÓN
    private function save_datos_notificacion($request, $activityGis, $activityAction)
    {

        DB::beginTransaction();
        try {
            $gis = $activityGis->gis_sort;

            // Eliminar registros existentes relacionados con las notificaciones
            GisOriginNotification::where('gis_id', $gis->id)->delete();


            // Verificar si hay notificaciones
            if (!empty($request->notificacion)) {
                foreach ($request->notificacion as $index => $item) {

                    // Inicializamos el valor de $fileDocument como vacío por defecto
                    $fileDocument = '';

                    // Verificar si hay un archivo adjunto
                    if ($request->hasFile("notificacion.$index.attached_document")) {
                        // Almacenar el archivo y obtener la ruta
                        $fileDocument = $this->storeActivityActionDocument($request, "notificacion.$index.attached_document", $activityAction);
                    }

                    // Crear la notificación en la base de datos
                    GisOriginNotification::create([
                        'notification_date' => $item['hidden_notification_date'],
                        'entity_type' => $item['entity_type'],
                        'send_method' => $item['send_method'],
                        'notification_status' => $item['notification_status'],
                        'effective_notification_date' => $item['hidden_effective_notification_date'],
                        'attached_document' => $fileDocument,
                        'gis_id' => $gis->id
                    ]);
                }
            }


            DB::commit();
        } catch (Exception $e) {
            DB::rollback();
            throw new Exception("Error al guardar las notificaciones: " . $e->getMessage());
        }
    }

    private function storeActivityActionDocument(Request $req, $fileKey, $activityAction)
    {
        try {
            $file = $req->file($fileKey);

            if ($file) {

                $originalExtension = $req->file($fileKey)->getClientOriginalExtension();
                $uniqueName = Str::random(10) . uniqid() . '.' . $originalExtension;
                $filePath = "documents/{$uniqueName}";

                $req->file($fileKey)->storeAs('documents', $uniqueName, 's3');

                // Guardamos el documento en la base de datos
                $activityActionDocument = new ActivityActionDocument();
                $activityActionDocument->activity_action_id = $activityAction->id;
                $activityActionDocument->name = 'Notificacion de origen';
                $activityActionDocument->path = $filePath;
                $activityActionDocument->save();
            }

            return $filePath;
        } catch (Exception $e) {
            DB::rollback();
            throw new Exception("Error al guardar los documentos: " . $e->getMessage());
        }
    }

    //ACCION REPORTAR SEGUIMIENTO AUDITORIA MEDICA
    private function actionMedicalAuditTracking($activityGis)
    {
        DB::beginTransaction();
        try {

            ActionController::create($activityGis->id, ActionGisSort::REPORTAR_SEGUIMIENTO_AUDITORIA_MEDICA, 'Reportar seguimiento de auditoría médica');

            DB::commit();

            return response()->json([
                'status' => 'success',
                'message' => 'Se completo la accion'
            ], 200);
        } catch (Exception $e) {
            DB::rollback();

            return response()->json([
                'status' => 'error',
                'message' => 'Ocurrió un error en el proceso'
            ], 500);
        }
    }

    //Guardamos la fase 3 calificación PCG
    public function saveGisPhase3($request, $activityGis)
    {

        //Buscamos Gis mediante su actividad
        $gisSort = $activityGis->gis_sort;

        //Agregamos los datos de la calificación
        $gisSort->update([
            "calificacion_date" => $request->calificacion_date,
            "discharge_date" => $request->discharge_date,
            "work_recommendations" => $request->work_recommendations,
            "variation_request_description_1" => $request->variation_request_description_1,
            "variation_request_description_2" => $request->variation_request_description_2,
            "accident_description_calificacion" => $request->accident_description_calificacion,
            "brain_dominance" => $request->brain_dominance,
            "medical_preconditions" => $request->medical_preconditions,
            "cofactors" => $request->cofactors,
            "additional_description" => $request->additional_description,
            "total_pcg" => $request->total_pcg
        ]);

        //Guardamos las calificaciones
        foreach ($request->calificacion_pcg as $qualification) {
            GisQualificationPcg::create([
                'gis_id' => $gisSort->id,
                'affected_member' => $qualification['affected_member'],
                'loss_percentage' => $qualification['loss_percentage'],
            ]);
        }

        //Ejecutamos la //ACCION REPORTAR CALIFICACION PCG 
        $this->actionGradePCG($request, $activityGis);
    }

    //ACCION REPORTAR CALIFICACION PCG
    private function actionGradePCG($request, $activityGis)
    {
        DB::beginTransaction();
        try {

            if ($request->has('total_pcg') && !empty($request->total_pcg)) {


                $gis = $activityGis->gis_sort;


                $gis->update([
                    'total_pcg' => $request->total_pcg
                ]);

                ActionController::create(
                    $activityGis->id,
                    ActionGisSort::REPORTAR_CALIFICACION_PCG,
                    'Reportar calificación PCG'
                );

                DB::commit();
            }


            return response()->json([
                'status' => 'error',
                'message' => 'Todos los campos requeridos deben estar presentes y no vacíos.'
            ], 400);
        } catch (Exception $e) {

            DB::rollback();
            return response()->json([
                'status' => 'error',
                'message' => 'Ocurrió un error en el proceso'
            ], 500);
        }
    }

    //ACCION REPORTAR CONTROVERSIA - PCG
    public function ReportPcgControversy($request, $id)
    {
        DB::beginTransaction();
        try {

            $activityGis = Activity::where('id', $id)->firstOrFail();
            $gis = $activityGis->gis_sort;
            $case = $gis->id;

            $activityAction = ActionController::create($activityGis->id, ActionGisSort::REPORTAR_TRAMITE_JUDICIAL_PCG, 'Reportar trámite judicial PCG');

            $pathFileOne = '';
            $pathFileTwo = '';
            $pathFileThree = '';

            if ($request->hasFile('file_medical_board')) {
                $pathFileOne = $this->storeActivityDocument($request, 'file_medical_board', 267, $id);
            }
            if ($request->hasFile('file_forensic_medical_legal')) {
                $pathFileTwo = $this->storeActivityDocument($request, 'file_forensic_medical_legal', 268, $id);
            }
            if ($request->hasFile('file_judicial_ruling')) {
                $pathFileThree = $this->storeActivityDocument($request, 'file_judicial_ruling', 269, $id);
            }

            $gis->update([
                'interested_party' => 'afiliado',
                'controversy_reason' => $request->controversy_reason ?? '',
                'controversy_date' => Carbon::now(),
                'path_file_medical' => $pathFileOne,
                'path_file_forensic' => $pathFileTwo,
                'path_file_judicial' => $pathFileThree

            ]);

            $this->notifyReceiveOpnion($activityGis, $activityAction->id);

            DB::commit();

            return response()->json([
                'status' => 'success',
                'message' => 'Se completo la accion',
                'case' => $case
            ], 200);
        } catch (Exception $e) {
            DB::rollback();

            return response()->json([
                'status' => 'error',
                'message' => 'Ocurrió un error en el proceso' . $e
            ], 500);
        }
    }

    //ME-1969 - ACCION REPORTAR CONTROVERSIA - ORIGEN
    public function ReportOriginControversy($request, $id)
    {
        DB::beginTransaction();
        try {

            $activityGis = Activity::where('id', $id)->firstOrFail();
            $gis = $activityGis->gis_sort;
            $case = $gis->id;

            $activityAction = ActionController::create($activityGis->id, ActionGisSort::REPORTAR_TRAMITE_JUDICIAL_ORIGEN, 'Reportar trámite judicial origen');

            $pathFileOne = '';
            $pathFileTwo = '';
            $pathFileThree = '';

            if ($request->hasFile('file_medical_board')) {
                $pathFileOne = $this->storeActivityDocument($request, 'file_medical_board', 267, $id);
            }
            if ($request->hasFile('file_forensic_medical_legal')) {
                $pathFileTwo = $this->storeActivityDocument($request, 'file_forensic_medical_legal', 268, $id);
            }
            if ($request->hasFile('file_judicial_ruling')) {
                $pathFileThree = $this->storeActivityDocument($request, 'file_judicial_ruling', 269, $id);
            }

            $gis->update([
                'interested_party' => 'afiliado',
                'controversy_reason' => $request->controversy_reason ?? '',
                'controversy_date' => Carbon::now(),
                'path_file_medical' => $pathFileOne,
                'path_file_forensic' => $pathFileTwo,
                'path_file_judicial' => $pathFileThree

            ]);

            $this->notifyReceiveOpnion($activityGis, $activityAction->id);

            DB::commit();

            return response()->json([
                'status' => 'success',
                'message' => 'Se completo la accion',
                'case' => $case
            ], 200);
        } catch (Exception $e) {
            DB::rollback();

            return response()->json([
                'status' => 'error',
                'message' => 'Ocurrió un error en el proceso' . $e
            ], 500);
        }
    }

    public function notifyReceiveOpnion($activity, $activity_action_id)
    {

        $emailIntermediary = $activity->parent->policy_sort->email;
        $emailTaker = $activity->parent->affiliate->email;

        $emails = array_filter([$emailIntermediary, $emailTaker], function ($email) {
            return !empty($email);
        });

        $emailBuild = TemplateBuilder::build(
            Templates::NOTIFY_RECEIVE_OPINION,
            [
                'caso_gis' => $activity->gis_sort->formatCaseNumber(),
                'name' => mb_convert_case(mb_strtolower($activity->parent->affiliate->full_name ?? ''), MB_CASE_TITLE, "UTF-8"),
                'court_or_tribunal' => $activity->gis_sort->controversy_judicial_instance,
                'fecha' => Carbon::parse($activity->parent->policy_sort->validity_to)->format('d/m/Y')
            ]
        );
        if ($emails != null) {

            $mailSent = new SendDocumentDataBase(
                $emails,
                $emailBuild['subject'],
                "<EMAIL>",
                "Liquidación de la póliza",
                [
                    "text" => $emailBuild['body'],
                    "sender" => $emailBuild['sender']
                ],
                "<EMAIL>",
                [], //files
                "send_document_db",
                1,
                request()->getHost(),
                $activity->id,
                $activity_action_id,
                $activity->service->id
            );
         

            // Capturar el resultado del envío
            $result = $mailSent->sendMail();

            //Registramos los datos del correo enviado para la trazabilidad
            $mailBoardController = new MailBoardController();
            $mailBoardController->createRegisterMail(
                $activity->id,
                $activity->service->id, 
                $activity->parent->policy_sort->consecutive, 
                'Tomador', 
                mb_convert_case(mb_strtolower($activity->parent->affiliate->full_name ?? ''), MB_CASE_TITLE, "UTF-8"), 
                $activity->parent->affiliate->doc_number, 
                $emailBuild['subject'], 
                $emailBuild['body'],
                $emails, 
                $result,
                null
            );
            
        }
    }


    //ACCION REPORTAR REAPERTURA DE CASO MS-1806
    public function ReportReopenings(Request $request, $cpath, $id)
    {

        DB::beginTransaction();
        try {

            $activityGis = Activity::where('id', $id)->firstOrFail();
            $gis = $activityGis->gis_sort;
            $case = $gis->id;

            $activityAction = ActionController::create($id, ActionGisSort::REPORTAR_REAPERTURA_CASO, 'Reportar reapertura de caso');

            if ($request->filled('sign_img') && $request->input('sign_img') !== '') {
                $filePath = $this->requestDocumentSignatureReopening($request, $cpath, $id);

                $activityActionDocument = new ActivityActionDocument();
                $activityActionDocument->activity_action_id = $activityAction->id;
                $activityActionDocument->name = 'Formulario de reapertura';
                $activityActionDocument->path = $filePath;
                $activityActionDocument->save();
            }



            //validar si las reperturas del servicio gis son mayor o igual a 3
            if (
                ($activityGis->activity_actions()
                    ->where('action_id', ActionGisSort::REPORTAR_REAPERTURA_CASO)
                    ->count() ?? 0) >= 3
            ) {

                //llamar método de reaperturas
                $compensationController = new CompensationBoardController();
                $compensationController->requestInformationReopeningAutomatic($cpath, $activityGis->id);

            }

            if ($request->hasFile('file_medical_care_order')) {
                $this->storeActivityDocument($request, 'file_medical_care_order', 270, $id);
            }

            DB::commit();

            return response()->json([
                'status' => 'success',
                'message' => 'Se completo la accion',
                'case' => $case
            ], 200);
        } catch (Exception $e) {
            DB::rollback();

            return response()->json([
                'status' => 'error',
                'message' => 'Ocurrió un error en el proceso' . $e
            ], 500);
        }
    }


    public function requestDocumentSignatureReopening(Request $req, $cpath, $id)
    {

        try {
            $client = Client::where('path', $cpath)->firstOrFail();
            $activity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();
            $gisSort = GisSort::where('activity_id', $activity->id)->first();

            $url = "signature/gis_" . $id . "_representative.png";
            if ($req->filled('sign_img') && $req->input('sign_img') !== '') {
                $image_request = $req->input('sign_img'); // base64 string
                $image_decode = base64_decode(preg_replace('#^data:image/\w+;base64,#i', '', $image_request));
                $path1 = $url;
                Storage::disk('s3')->put($path1, $image_decode);
                $urls = Storage::disk('s3')->url($path1);

            } else {
                $urls['sign1_url'] = Storage::disk('s3')->url($gisSort->sign_gis_representative); // Obtener la URL de sign1
            }


            //consultar los datos del asegurado
            $asegurado = $activity->affiliate;

            //consultar los datos del tomador y poliza
            $policyActivty = $activity->parent_activity;
            $tomador = $policyActivty->affiliate;
            $policy = $policyActivty->policy_sort;

            //recibir los datos del formulario
            $data = [
                'name_affiliate' => $asegurado->full_name ?? '',
                'num_affiliate' => $asegurado->doc_number ?? '',
                'name_holder' => $tomador->full_name ?? '',
                'num_policy' => $policy->formatNumberConsecutive() ?? '',
                'num_case' => $gisSort->consecutive ?? '',
                'date_medical_discharge' => $req->alta_medica,
                'reason' => $req->motivo_reapertura,
                'name_applicant' => $req->tomador_nombre,
                'sign1_url' => $urls,
            ];


            // Cargar la vista del PDF y pasarle los datos
            $document = 'formulario_reapertura_pdf';
            $pdf = PDF::loadView("services.gis.docs.{$document}", [
                'fecha_solicitud' => date('Y-m-d'),
                'data' => $data,  // Reemplazamos los datos generados anteriormente
                'watermark' => false
            ]);


            //generar un unico nombre para el documento + el número de la fecha de hoy (año/Mes/Día)
            $uniqueid = uniqid() . date('Ymd');

            $filePath = "activity_action_document/{$document}_{$uniqueid}.pdf";



            Storage::disk('s3')->put($filePath, $pdf->output());

            return $filePath;

        } catch (Exception $e) {
            throw new Exception("Errores durante el procesamiento" . $e->getMessage(), 500);
        }
    }


    protected function storeActivityDocument(Request $req, $fileKey, $documentId, $id)
    {
        $file = $req->file($fileKey);
        $path = '';

        if ($file) {

            $originalExtension = $req->file($fileKey)->getClientOriginalExtension();
            $uniqueName = Str::random(10) . uniqid() . '.' . $originalExtension;
            $filePath = "documents/{$uniqueName}";

            $req->file($fileKey)->storeAs('documents', $uniqueName, 's3');

            $activityDocument = ActivityDocument::where('activity_id', $id)
                ->where('document_id', $documentId)
                ->first();

            if (!$activityDocument) {
                $activityDocument = new ActivityDocument();
            }

            $activityDocument->document_id = $documentId;
            $activityDocument->activity_id = $id;
            $activityDocument->path = $filePath;
            $activityDocument->uploaded_at = Carbon::now();
            $activityDocument->save();

            $path = $filePath;
        }

        return $path;
    }

    //ACCION REPORTAR CIERRE DE CASO POR ABANDONO DE TRATAMIENTO MS-1799
    private function ActionCloseCaseDueToTreatmentAbandonment($request, $activityGis, $client)
    {

        DB::beginTransaction();
        try {

            $activityAction = ActionController::create($activityGis->id, ActionGisSort::REPORTAR_CIERRE_CASO_ABANDONO_TRATAMIENTO, 'Reportar cierre de caso por abandono de tratamiento');

            $nameTaker = ucwords(strtolower($activityGis->parent->affiliate->first_name));
            $numberCase = $activityGis->gis_sort->formatCaseNumber();
            $eventDate = ucfirst(strftime('%A %e de %B del %Y', strtotime($activityGis->gis_sort->date_accident)));

            $nameWorker = mb_convert_case(mb_strtolower(optional($activityGis->affiliate)->full_name ?? '', 'UTF-8'), MB_CASE_TITLE, 'UTF-8');
            $docNumber = $activityGis->affiliate->doc_number;


            $emailData = TemplateBuilder::build(
                Templates::CASE_CLOSURE_NOTIFICATION_DUE_TO_TREATMENT_ABANDONMENT,
                [
                    'case_number' => $numberCase,
                    'name' => $nameTaker,
                    'connector' => $activityGis->affiliate->gender === 'F' ? 'de la trabajadora' : 'del trabajador',
                    'name_affiliate' => $nameWorker,
                    'doc_number' => $docNumber,
                    'event_date' => $eventDate,
                    'connector_two' => $activityGis->affiliate->gender === 'F' ? 'a la señora' : 'al señor',
                ]
            );

            $emails = [$activityGis->parent->affiliate->email, $activityGis->gis_sort->email_affiliate]; //trabajador
            $mailSent = new SendDocumentDataBase(
                implode(',', $emails),
                $emailData['subject'],
                "<EMAIL>",
                $emailData['subject'],
                [
                    "text" => $emailData['body'],
                    "sender" => $emailData['sender']
                ],
                "<EMAIL>",
                [],
                "send_document_db",
                $client,
                $request->getHost(),
                $activityGis->id,
                $activityAction,
                $activityGis->service_id
            );
            
            
            // Capturar el resultado del envío
            $result = $mailSent->sendMail();

            //Registramos los datos del correo enviado para la trazabilidad
            $mailBoardController = new MailBoardController();
            $mailBoardController->createRegisterMail(
                $activityGis->id,
                $activityGis->service->id, 
                $activityGis->parent->policy_sort->consecutive, 
                'Tomador', 
                mb_convert_case(mb_strtolower($activityGis->parent->affiliate->full_name ?? ''), MB_CASE_TITLE, "UTF-8"), 
                $activityGis->parent->affiliate->doc_number, 
                $emailData['subject'], 
                $emailData['body'],
                $emails, 
                $result,
                null
            );

            DB::commit();

            return response()->json([
                'status' => 'exito',
                'message' => 'registro creado.'
            ], 200);
        } catch (Exception $e) {
            DB::rollback();
            return response()->json([
                'status' => 'error',
                'message' => 'Ocurrió un error en el proceso'
            ], 500);
        }
    }

    //ACCION SOLICITUD INFORMACION EXTRA
    public function requestExtraInformation(Request $request, $cpath, $id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $activityGis = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();
        DB::beginTransaction();
        try {
            $activityAction = ActionController::create($activityGis->id, ActionGisSort::SOLICITUD_INFORMACIÓN_EXTRA, 'Solicitud de información extra');
            $nameTaker = ucwords(strtolower($activityGis->parent->affiliate->first_name));
            $numberCase = $activityGis->gis_sort->formatCaseNumber();
            $nameInsurance = mb_convert_case(mb_strtolower(optional($activityGis->affiliate)->full_name ?? '', 'UTF-8'), MB_CASE_TITLE, 'UTF-8');
            $numberPolicy = $activityGis->parent->policy_sort->formatNumberConsecutive();
            $eventDate = ucfirst(strftime('%A %e de %B del %Y', strtotime($activityGis->gis_sort->date_accident)));

            $emailData = TemplateBuilder::build(
                Templates::REQUEST_FOR_EVIDENCE_OR_ADDITIONAL_INFORMATION,
                [
                    'name' => $nameTaker,
                    'number_case' => $numberCase,
                    'connector' => $activityGis->affiliate->gender === 'F' ? 'de la trabajadora' : 'del trabajador',
                    'name_affiliate' => $nameInsurance,
                    'event_date' => $eventDate,
                    'policy_sort' => $numberPolicy,
                ]
            );

            if (App::environment('prod')) {
                $emails = [$activityGis->parent->affiliate->email, "<EMAIL>"];
            } else {
                $emails = [$activityGis->parent->affiliate->email, "<EMAIL>"];
            }

            $subject = "Solicitud de pruebas o información adicional - Caso #" . $activityGis->gis_sort->formatCaseNumber();

            $mailSent = new SendDocumentDataBase(
                implode(',', $emails),
                'Solicitud de pruebas o información adicional - Caso #' . $activityGis->gis_sort->formatCaseNumber(),
                "<EMAIL>",
                $emailData['subject'],
                [
                    "text" => $emailData['body'],
                    "sender" => $emailData['sender']
                ],
                "<EMAIL>",
                [],
                "send_document_db",
                $client->id,
                $request->getHost(),
                $activityGis,
                $activityAction,
                $activityGis->service_id
            );

            // Capturar el resultado del envío
            $result = $mailSent->sendMail();

            //Registramos los datos del correo enviado para la trazabilidad
            $mailBoardController = new MailBoardController();
            $mailBoardController->createRegisterMail(
                $activityGis->id,
                $activityGis->service->id,
                $activityGis->parent_activity->policy_sort->consecutive,
                'Tomador',
                $nameTaker,
                $activityGis->parent->affiliate->doc_number,
                $subject,
                $emailData['body'],
                $emails,
                $result,
                null
            );

            DB::commit();
            return redirect("/tablero/auditoria_medica")->with('success', 'Acción ejecutada correctamente');
        } catch (Exception $e) {
            DB::rollback();
            return redirect("/tablero/auditoria_medica")->with('error', 'No se pudo ejecutar la acción' . $e->getMessage());
        }
    }

    public function uploadSupportsGisForm($activityGis, $request)
    {

        DB::beginTransaction();
        try {
            //Obtenemos la actividad padre de GIS (Póliza)
            $activity_policy_sort = $activityGis->parent_activity;
            //El siguiente código permite guardar los documentos de los Soporte (Reporte accidente)
            //Lista de los documentos
            $documentFields = [
                'document1' => GisSort::DOCUMENTO_SOPORTE_1,
                'document2' => GisSort::DOCUMENTO_SOPORTE_2,
                'document3' => GisSort::DOCUMENTO_SOPORTE_3,
                'document4' => GisSort::DOCUMENTO_SOPORTE_4,
                'document5' => GisSort::DOCUMENTO_SOPORTE_5,
            ];
            // Verificar si algún archivo fue cargado
            foreach ($documentFields as $documentField => $documentId) {
                if ($request->hasFile($documentField)) {
                    // Subir el documento a S3
                    $file = $request->file($documentField);
                    $filePath = "activity_documents/{$documentField}_{$activityGis->id}.pdf";

                    Storage::disk('s3')->put($filePath, file_get_contents($file->getRealPath()));

                    // Guardar el documento en AcitivityDocument
                    $activityDocument = new ActivityDocument();
                    $activityDocument->activity_id = $activityGis->id;
                    $activityDocument->document_id = $documentId;
                    $activityDocument->path = $filePath;
                    $activityDocument->uploaded_at = now();
                    $activityDocument->save();
                }
            }
            //Cambiamos el estado de la actividad
            $description = "REPORTAR SOPORTES";
            ActionController::create(
                $activityGis->id,
                ActionGisSort::REPORTAR_SOPORTES,
                $description
            );
            DB::commit();
        } catch (Exception $e) {
            DB::rollback();
            return redirect("'/tomador/poliza/' . $activity_policy_sort->affiliate_id . '/datos'")->with('error', 'Ha ocurrido un error al guardar los soportes');
        }
    }

    //Reportar caso por excepcion rechazo MS-1851
    public function ReportCaseExceptionRejected(Request $request, $cpath, $activityGisId)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $activityGis = Activity::find($request->id);

        DB::beginTransaction();

        try {

            $gis = $activityGis->gis_sort;

            //Actualizamos el caso asegurado en no
            $gis->update([
                'insured_case' => 'No'
            ]);

            //ejecutamos la acción Reportar caso por excepción rechazo
            $activityAction = ActionController::create(
                $activityGis->id,
                ActionGisSort::REPORTAR_CASO_EXCEPCION_RECHAZO,
                'Reportar caso por excepción rechazo'
            );

            $emailData = TemplateBuilder::build(
                Templates::NOTIFICATION_TO_INSURED_OF_REQUIREMENT_INCONSISTENCY,
                [
                    'case_number' => $gis->formatCaseNumber(),
                    'name' => ucwords(strtolower($activityGis->parent_activity->affiliate->first_name)),
                    'observation' => 'Reportar caso por excepción rechazo',
                ]
            );

            $emails = [$activityGis->affiliate->email, $activityGis->parent->affiliate->email];

            $authorizedTakersData = collect();
            if ($activityGis) {
                $idActivityTaker = $activityGis->parent_id;
                $authorizedTakersData = $this->getAuthorizedTakerEmails($idActivityTaker);
            }

            $emailUsersTakerAuthorized = $authorizedTakersData->pluck('email');

            // Combina los emails iniciales con los emails de los usuarios autorizados
            $allEmails = collect($emails)
                ->merge($emailUsersTakerAuthorized)
                ->filter(function ($email) {
                    return !empty($email);
                })
                ->unique()
                ->values();

            $finalEmailsArray = $allEmails->toArray();
            $validEmails = array_filter($finalEmailsArray, function ($email) {
                return filter_var($email, FILTER_VALIDATE_EMAIL);
            });

            if(!empty($validEmails))
            $mailSent = new SendDocumentDataBase(
                implode(',', $validEmails),
                $emailData['subject'],
                "<EMAIL>",
                $emailData['subject'],
                [
                    "text" => $emailData['body'],
                    "sender" => $emailData['sender']
                ],
                "<EMAIL>",
                [],
                "send_document_db",
                $client->id,
                $request->getHost(),
                $activityGis->id,
                $activityAction->id,
                $activityGis->service_id
            );
                        // Capturar el resultado del envío
            $result = $mailSent->sendMail();

            //Registramos los datos del correo enviado para la trazabilidad
            $mailBoardController = new MailBoardController();

            foreach ($validEmails as $email){

                $takerAuthorizedId = null;
                $authorizedTaker = $authorizedTakersData->firstWhere('email', $email);
                if ($authorizedTaker) {
                    $takerAuthorizedId = $authorizedTaker->id;
                }
                $mailBoardController->createRegisterMail(
                    $activityGis->id,
                    $activityGis->service->id,
                    $activityGis->parent->policy_sort->consecutive,
                    'Tomador',
                    mb_convert_case(mb_strtolower($activityGis->parent->affiliate->full_name ?? ''), MB_CASE_TITLE, "UTF-8"),
                    $activityGis->parent->affiliate->doc_number,
                    $emailData['subject'],
                    $emailData['body'],
                    $emails,
                    $result,
                    null,
                    $takerAuthorizedId
                );
            }

            DB::commit();

            return redirect("/tablero/auditoria_medica")->with('success', 'Acción ejecutada correctamente');
        } catch (Exception $e) {
            DB::rollback();
            return redirect("/tablero/auditoria_medica")->with('error', 'No se pudo ejecutar la acción');
        }
    }

    //ME-2754
    public function reportAdministrativeCaseReopening(Request $request, $cpath, $activity_id)
    {

        //Iniciamos la transacción
        DB::beginTransaction();

        try {
            //Obtenemos la actividad de gis
            $activityGis = Activity::find($activity_id);

            //validamos si la actividad de gis existe
            if (empty($activityGis)) {
                $message = 'No se encuentra la actividad de gis';
                // Redirigir con el mensaje de error
                return redirect()->back()->withErrors($message)->withInput();
            }

            //Actualizamos el caso asegurado en no
            $activityGis->gis_sort->update([
                'report_reopening' => 1 //Reporte de reapertura
            ]);
            //creamos la accion de reportar el caso de reapertura administrativa
            ActionController::create(
                $activityGis->id,
                ActionGisSort::REPORTAR_REAPERTURA_CASO_ADMINISTRATIVA,
                'REPORTAR REAPERTURA DE CASO ADMINISTRATIVA'
            );

            //guardar los cambios
            DB::commit();

            //Redirigir con el mensaje de exito
            return redirect("/tablero/auditoria_medica")->with('success', 'Acción ejecutada correctamente');
        } catch (Exception $e) {
            DB::rollback();
            // Redirigir con el mensaje de error
            return redirect()->back()->withErrors('No se pudo ejecutar la acción, error: ' . $e)->withInput();
        }
    }
    public function validapps($cpath, $severity)
    {

        $gis = Activity::where('id', $severity)->first();

        $request = new Request(['severity' => $severity, 'gis' => $gis->parent->id]);
        return $this->CalculatePps($request);
    }

    /**
     * Cálculo PPS (Provisión para siniestros en el momento de apertura)
     * */
    public function CalculatePps(Request $request)
    {
        try {
            DB::commit();

            // Define el salario base
            $salario = 358609.50;
            $severity = $request->input('severity');
            $ppsValues = 0;
            $ppsItValues = 0;
            $ppsIpValues = 0;
            $ppsIpIppValues = 0;
            $balanceReserve = 0;
            $ppsIpITpValues = 0;
            $ppsGreatDisabilityValues = 0;

            //Buscamos el la actividad del GIS
            $activityGis = Activity::where('id', $request->gis)->first();

            //Buscamos las ultimas 3 planilla del afiliado
            $lastThreeMonths = PolicySpreadsheetAffiliate::query()
                ->where('affiliate_id', $activityGis->affiliate_id)
                ->orderBy('id', 'desc')
                ->limit(3)
                ->select('policy_spreadsheet_id', 'monthly_salary', 'days')
                ->get();

            //Colocamos el salario del empleado
            $totalSalary = $lastThreeMonths->sum('monthly_salary');

            //Colocamos los dias laborados el empleado
            $totalDays = $lastThreeMonths->sum('days');

            // Salario diario del trabajador
            $SD = ($totalDays > 0) ? ($totalSalary / $totalDays) : 0;
            // Salario minimo de la persona trabajadora
            $SMD = 11953.65;

            $FinalSMD = $SD<$SMD ? $SMD : $SD;

            /**
             * Cálculos según selección de severidad
             */
            switch ($severity) {
                case '1':  // Leve
                    // Cálculo para Prestación médica o sanitaria
                    $ppsValues = $salario * 2;
                    // Cálculo para Incapacidad temporal
                    $ppsItValues = $FinalSMD * 5;

                    break;
                case '2': // Moderado
                    // Cálculo para Prestación médica o sanitaria
                    $ppsValues = $salario * 4;
                    // Cálculo para Incapacidad temporal
                    $ppsItValues = $FinalSMD * 15;
                    break;
                case '3': // Grave
                    // Cálculo para Prestación médica o sanitaria
                    $ppsValues = $salario * 10;
                    // Cálculo para Incapacidad temporal
                    $ppsItValues = $FinalSMD * 45;
                    break;
                case '4': // Fatal
                    // Cálculo para Prestación médica o sanitaria
                    $ppsValues = $salario * 20;
                    // Cálculo para Incapacidad temporal
                    $ppsItValues = 90 * ($SMD + (($FinalSMD - $SMD) * 0.67));
                    break;
                default:
                    $ppsValues = $salario;
                    break;
            }

            /**
             * Función para cálculo PPS Pe IP -> IMP (incapacidad menor permanente)
             */
            $currencySymbol = '';

            //Obtener incapacidad permanente mediante GIS
            $activityPeip = Activity::where('parent_id', $activityGis->id)
                ->where('service_id', Service::SERVICE_PE_IP_SORT_MNK)
                ->first();

            $dataAdicional = [];

            //Si existe una incapcidad permanente entonces realizamos los calculos
            if ($activityPeip) {

                //Aqui se encuentra la primera incapacidades
                $peIpSort = PeIpSort::where('activity_id', $activityPeip->id)->first();

                //Fecha de nacimiento del afiliado
                $birthdate = new DateTime($activityPeip->affiliate->birthday);

                //Fecha de clasificación
                $contingency_date = new DateTime($peIpSort->casedata_date_classif);

                // Calcula la diferencia en días de la fecha de nacimiento VS respecto a la fecha de la PCG
                $differenceInDays = $contingency_date->diff($birthdate)->days;

                // Realiza el cálculo y redondea
                $X0 = floor($differenceInDays / 360 + 0.5);
                //$SAT = floatval(str_replace(['$', '₡', ',', ' '], '', $peIpSort->calc_annual_salary));
                //$percentagedisability = floatval(str_replace('%', '', $peIpSort->casedata_percent)) / 100;

                $SAT = $peIpSort->calc_annual_salary;
                $percentagedisability = floatval(str_replace('%', '', $peIpSort->casedata_percent)) / 100;


                /**
                 * Inicio de cálculo para obtener el pps
                 * Variables
                 */
                $court = (clone $contingency_date)->modify('first day of next month')->modify('-1 day');
                $differenceInDays2 = $court->diff($contingency_date)->days;
                $x = $X0 + floor($differenceInDays2 / 365);
                $x1 = $x + 1;
                $x0n = 0;
                switch ($peIpSort->casedata_type_disability) {
                    case 'IMP - Incapacidad menor permanente':
                        $x0n = $X0 + 5;
                        break;
                    case 'IPP - Incapacidad parcial permanente':
                        $x0n = $X0 + 10;
                        break;
                    case 'ITP - Incapacidad total permanente':
                    case 'Gran invalidez':
                        $n = 115 - (int) $x - 1;
                        $x0n = $X0 + $n;
                        break;
                    default:
                        $x0n = 0;
                        break;
                }



                // Obtener el tipo de moneda de la póliza


                $firstItem = $lastThreeMonths->first();
                if ($firstItem) {
                    $policy_spreadsheet = PolicySpreadsheet::where('id', $firstItem->policy_spreadsheet_id)
                        ->select('activity_id')
                        ->first();
                    $activity_policy_spreadsheet_id = Activity::where('id', $policy_spreadsheet->activity_id)
                        ->select('parent_id')
                        ->first();
                    $policy_sort = PolicySort::where('activity_id', $activity_policy_spreadsheet_id->parent_id)
                        ->select('type_currency')
                        ->first();


                    // Capturar la tabla de indemnización a partir del tipo de móneda de la póliza
                    switch ($policy_sort->type_currency) {
                        case 'USD':
                            $currencySymbol = '$';
                            $ages = [(int) $x, (int) $x1, (int) $x0n];

                            // Realiza una sola consulta que traiga los registros para todos los valores de edad necesarios
                            //$compensations = CompensationDollars::whereIn('age', $ages)->get()->keyBy('age');

                            switch ($peIpSort->casedata_type_disability) {
                                case 'IPP - Incapacidad parcial permanente':
                                    $compensations = CompensationDollarsFour::whereIn('age', $ages)->get()->keyBy('age');
                                    break;
                                case 'Gran invalidez':
                                case 'ITP - Incapacidad total permanente':
                                    $compensations = CompensationDollarsFive::whereIn('age', $ages)->get()->keyBy('age');
                                    break;
                                case 'Muerte':
                                    $compensations = CompensationDollarsSix::whereIn('age', $ages)->get()->keyBy('age');
                                    break;
                                default:
                                    $compensations = CompensationDollars::whereIn('age', $ages)->get()->keyBy('age');
                                    break;
                            }


                            break;
                        case 'CRC':
                            $ages = [(int) $x, (int) $x1, (int) $x0n];
                            $currencySymbol = '₡';
                            //$compensations = CompensationColons::whereIn('age', $ages)->get()->keyBy('age');

                            switch ($peIpSort->casedata_type_disability) {
                                case 'IPP - Incapacidad parcial permanente':
                                    $compensations = CompensationColonsFour::whereIn('age', $ages)->get()->keyBy('age');
                                    break;
                                case 'Gran invalidez':
                                case 'ITP - Incapacidad total permanente':
                                    $compensations = CompensationColonsFive::whereIn('age', $ages)->get()->keyBy('age');
                                    break;
                                case 'Muerte':
                                    $compensations = CompensationColonsSix::whereIn('age', $ages)->get()->keyBy('age');
                                    break;
                                default:
                                    $compensations = CompensationColons::whereIn('age', $ages)->get()->keyBy('age');
                                    break;
                            }

                            break;
                        default:
                            $currencySymbol = '';
                            break;
                    }
                }


                $compensation_x = $compensations[(int) $x] ?? null;
                $compensation_x1 = $compensations[(int) $x1] ?? null;
                $compensation_x0n = $compensations[(int) $x0n] ?? null;


                $nx = $compensation_x ? $compensation_x->Nx : null;
                $nx1 = $compensation_x1 ? $compensation_x1->Nx : null;
                $nxon = $compensation_x0n ? $compensation_x0n->Nx : null;

                $dx = $compensation_x ? $compensation_x->Dx2 : null;
                $dx1 = $compensation_x1 ? $compensation_x1->Dx2 : null;
                $dxon = $compensation_x0n ? $compensation_x0n->Dx2 : null;
                $annuity = $SAT * $percentagedisability;

                if ($peIpSort->casedata_type_disability == 'IMP - Incapacidad menor permanente' || $peIpSort->casedata_type_disability == 'IPP - Incapacidad parcial permanente') {
                    $tVx = (($nx - $nxon) / $dx - (11 / 24) * (1 - $dxon / $dx)) * $annuity * 1.028;
                    $tVx1 = (($nx1 - $nxon) / $dx1 - (11 / 24) * (1 - $dxon / $dx1)) * $annuity * 1.028;
                } else {
                    $tVx = (($nx - $nxon) / $dx - (11 / 24) * (1 - $dxon / $dx)) * $annuity * 1.028 * (13 / 12);
                    $tVx1 = (($nx1 - $nxon) / $dx1 - (11 / 24) * (1 - $dxon / $dx1)) * $annuity * 1.028 * (13 / 12);
                }

                // dd('x',$x, 'x+1', $x1, 'X0n' , $x0n,  'Nx', $nx, 'NX1', $nx1, 'NXon', $nxon, 'DX', $dx, 'dx1', $dx1, 'dx0n', $dxon, 'anualidad', $annuity, 'tvx',$tVx, 'tvx1', $tVx1 );

                // Calcular la diferencia de días
                $differenceInDays3 = $contingency_date->diff($court)->days;

                // Obtener el año original de $U12 y agregar la diferencia de años
                $newYear = $contingency_date->format('Y') + intdiv($differenceInDays3, 365);

                // Crear la nueva fecha con el año actualizado
                $newDate = DateTime::createFromFormat('Y-m-d', "$newYear-" . $contingency_date->format('m-d'));
                $anniversary_of_the_contingency = $newDate;
                $court->setTime(0, 0, 0);  // Establecer las horas, minutos y segundos a 00:00
                $anniversary_of_the_contingency->setTime(0, 0, 0);  // Lo mismo para la segunda fecha

                $interval = $court->diff($anniversary_of_the_contingency);
                $h = $interval->days;
                $daysInYear = 365;
                $remainingDays = $daysInYear - $h;
                $h365 = ($remainingDays / $daysInYear) * 100;
                $h_365 = ($h / $daysInYear) * 100;
                $roundedPercentageH365 = $h365;
                $percentageDecimalH365 = $roundedPercentageH365 / 100;
                $roundedPercentageH_365 = $h_365;
                $percentageDecimalH_365 = $roundedPercentageH_365 / 100;
                $balanceReserve = ($percentageDecimalH365 * $tVx) + ($percentageDecimalH_365 * $tVx1);
                $balanceReserveGreatDisability = $balanceReserve * 1.29;

                //Asignar valores después de los cálculos realizados
                switch ($peIpSort->casedata_type_disability) {
                    case 'IMP - Incapacidad menor permanente':
                        $ppsIpValues = $currencySymbol . number_format($balanceReserve, 2, ',', '.');
                        break;
                    case 'IPP - Incapacidad parcial permanente':
                        $ppsIpIppValues = $currencySymbol . number_format($balanceReserve, 2, ',', '.');
                        break;
                    case 'ITP - Incapacidad total permanente':
                        $ppsIpITpValues = $currencySymbol . number_format($balanceReserve, 2, ',', '.');
                        break;
                    case 'Gran invalidez':
                        $ppsGreatDisabilityValues = $currencySymbol . number_format($balanceReserveGreatDisability, 2, ',', '.');
                        break;
                    default:
                        $ppsIpITpValues = 0;
                        break;
                }

                $dataAdicional = [
                    'severity' => $severity,
                    'totalSalary' => $totalSalary,
                    'totalDays' => $totalDays,
                    'ppsValues' => $ppsValues,
                    'ppsItValues' => $ppsItValues,
                    'birthdate' => $birthdate,
                    'contingency_date' => $contingency_date,
                    'differenceInDays' => $differenceInDays,
                    'casedata_type_disability' => $peIpSort->casedata_type_disability,
                    'type_currency' => $policy_sort->type_currency,
                    'nx' => $nx,
                    'nx1' => $nx1,
                    'nxon' => $nxon,
                    'dx' => $dx,
                    'dx1' => $dx1,
                    'dxon' => $dxon,
                    'annuity' => $annuity,
                    'SAT' => $SAT,
                    'percentagedisability' => $percentagedisability,
                    'tVx' => $tVx,
                    'tVx1' => $tVx1,
                    'differenceInDays3' => $differenceInDays3,
                    'newYear' => $newYear,
                    'interval' => $interval,
                    'h' => $h,
                    'daysInYear' => $daysInYear,
                    'remainingDays' => $remainingDays,
                    'h365' => $h365,
                    'h_365' => $h_365,
                    'roundedPercentageH365' => $roundedPercentageH365,
                    'percentageDecimalH365' => $percentageDecimalH365,
                    'roundedPercentageH_365' => $roundedPercentageH_365,
                    'percentageDecimalH_365' => $percentageDecimalH_365,
                    'balanceReserve' => $balanceReserve,
                    'balanceReserveGreatDisability' => $balanceReserveGreatDisability
                ];
            }
            //ppsIpIppValues
            $pps_medical_benefits = $currencySymbol . number_format($ppsValues, 2, ',', '.');
            $pps_it = $currencySymbol . number_format($ppsItValues, 2, ',', '.');

            return response()->json([
                'pps_medical_benefits' => $pps_medical_benefits,
                'pps_it' => $pps_it,
                'ppsIpValues' => $ppsIpValues,
                'ppsIpIppValues' => $ppsIpIppValues,
                'ppsIpITpValues' => $ppsIpITpValues,
                'ppsGreatDisabilityValues' => $ppsGreatDisabilityValues,
                'dataAdicional' => $dataAdicional
            ]);
        } catch (Exception $e) {
            DB::rollback();
            return response()->json([
                'status' => 'error',
                'message' => 'Ocurrió un error en el proceso',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function calculatePpsDeathWorker($cpath, $id)
    {

        // Define variables de la función
        $ppsDeathWorkerValues = 0;
        $balanceReserve = 0;
        $percentagedisability = 0;
        $percentagedisability_y = 0;
        $y0 = 0;
        $has_spouse = false;
        $has_children_under_18 = false;
        $oldest_child_below_18 = null;

        //Buscamos el la actividad del GIS
        $activityGis = Activity::where('id', $id)->first();

        //Buscamos las ultimas 3 planilla del afiliado
        $lastThreeMonths = PolicySpreadsheetAffiliate::query()
            ->where('affiliate_id', $activityGis->affiliate_id)
            ->orderBy('id', 'desc')
            ->limit(3)
            ->select('policy_spreadsheet_id', 'monthly_salary', 'days')
            ->get();

        //Variable para signo de moneda
        $currencySymbol = '';

        //Obtenemos la actividad del servicio muerte persona trabjadora
        $activityPeMpt = Activity::where('parent_id', $activityGis->id)
            ->where('service_id', Service::SERVICE_PE_MPT_SORT_MNK)
            ->first();

        //Si existe la actividad del servicio muerte persona trabjadora
        if ($activityPeMpt) {

            //Obtenemos el servicio de muerte persona trabajadora
            $peMptSort = PeMptSort::where('activity_id', $activityPeMpt->id)->first();

            //Buscamos si tiene beneficiaros 
            $peMptBeneficiaries = PeMptBeneficiaries::where('pe_mpt_id', $peMptSort->id)->get();

            // Si tiene beneficiarios
            if ($peMptBeneficiaries) {
                foreach ($peMptBeneficiaries as $beneficiary) {

                    // Verificamos si tiene cónyuge
                    $is_spouse = $beneficiary->type_of_beneficiary == 'CN';
                    $is_child = false;

                    // Verificamos si tiene hijos menores de 18 años
                    if ($beneficiary->type_of_beneficiary == 'HJ') {
                        $child_age = Carbon::parse($beneficiary->birthdate)->age;
                        if ($child_age < 18) {
                            $is_child = true;
                            // Comparamos si este hijo es el mayor de los menores de 18 años
                            if (!$oldest_child_below_18 || Carbon::parse($beneficiary->birthdate)->greaterThan(Carbon::parse($oldest_child_below_18))) {
                                $oldest_child_below_18 = $beneficiary->birthdate; // Guardamos la fecha de nacimiento del hijo mayor
                            }
                        }
                    }

                    // Si tiene cónyuge
                    if ($is_spouse) {
                        $has_spouse = true;
                    }

                    // Si tiene hijos menores de 18 años
                    if ($is_child) {
                        $has_children_under_18 = true;
                    }
                }
            }

            //Fecha de nacimiento del asegurado
            $birthdate = new DateTime($activityPeMpt->affiliate->birthday);

            //Fecha de clasificación
            $contingency_date = new DateTime($peMptSort->date_of_death);

            //Calcula la diferencia en días de la fecha de nacimiento VS respecto a la fecha de la muerte del asegurado
            $differenceInDays = $contingency_date->diff($birthdate)->days;

            //Realiza el cálculo y redondea
            $X0 = floor($differenceInDays / 360 + 0.5);

            //Salario anual del trabajador
            $SAT = $peMptSort->annual_salary_person;

            $court = (clone $contingency_date)->modify('first day of next month')->modify('-1 day');
            $differenceInDays2 = $court->diff($contingency_date)->days;
            $x = $X0 + floor($differenceInDays2 / 365);
            $x1 = $x + 1;
            $x0n = $X0 + 10;

            // Obtener el tipo de moneda de la póliza
            $firstItem = $lastThreeMonths->first();
            $policy_sort = "";

            if ($firstItem) {
                $policy_spreadsheet = PolicySpreadsheet::where('id', $firstItem->policy_spreadsheet_id)
                    ->select('activity_id')
                    ->first();

                $activity_policy_spreadsheet_id = Activity::where('id', $policy_spreadsheet->activity_id)
                    ->select('parent_id')
                    ->first();

                $policy_sort = PolicySort::where('activity_id', $activity_policy_spreadsheet_id->parent_id)
                    ->select('type_currency')
                    ->first();

                //Capturar la tabla de indemnización a partir del tipo de móneda de la póliza
                switch ($policy_sort->type_currency) {
                    case 'USD':
                        $currencySymbol = '$';
                        $ages = [(int) $x, (int) $x1, (int) $x0n];
                        $compensations = CompensationDollarsSix::whereIn('age', $ages)->get()->keyBy('age');
                        break;

                    case 'CRC':
                        $ages = [(int) $x, (int) $x1, (int) $x0n];
                        $currencySymbol = '₡';
                        $compensations = CompensationColonsSix::whereIn('age', $ages)->get()->keyBy('age');
                        break;
                }
            }

            $compensation_x = $compensations[(int) $x] ?? null;
            $compensation_x1 = $compensations[(int) $x1] ?? null;
            $compensation_x0n = $compensations[(int) $x0n] ?? null;

            $nx = $compensation_x ? $compensation_x->Nx : null;
            $nx1 = $compensation_x1 ? $compensation_x1->Nx : null;
            $nxon = $compensation_x0n ? $compensation_x0n->Nx : null;

            $dx = $compensation_x ? $compensation_x->Dx2 : null;
            $dx1 = $compensation_x1 ? $compensation_x1->Dx2 : null;
            $dxon = $compensation_x0n ? $compensation_x0n->Dx2 : null;

            //caso 1 con conyugue y sin hijos < de 18 años el porcentaje es 75%
            if ($has_spouse && !$has_children_under_18) {
                $percentagedisability = 0.75;
            }

            //caso 2 con conyugue y con hijos < 18 años
            if ($has_spouse && $has_children_under_18) {
                $percentagedisability = 0.50;
                $percentagedisability_y = 0.25;

                $oldest_child_below_18 = Carbon::parse($oldest_child_below_18);

                // Calcula la diferencia en días de la fecha de nacimiento del hijo  VS respecto a la fecha de la muerte del asegurado
                $differenceInDaysY = $contingency_date->diff($oldest_child_below_18)->days;

                //Realiza el cálculo y redondea
                $y0 = floor($differenceInDaysY / 360 + 0.5);

                $nMax = 26 - $y0;

                $courtY = (clone $contingency_date)->modify('first day of next month')->modify('-1 day');
                $differenceInDaysY2 = $courtY->diff($contingency_date)->days;

                $y = $y0 + floor($differenceInDaysY2 / 365);
                $y1 = $y + 1;
                $y0n = $y + $nMax;

                //Capturar la tabla de indemnizacióes a partir del tipo de móneda de la póliza
                switch ($policy_sort->type_currency) {
                    case 'USD':
                        $currencySymbol = '$';
                        $ages = [(int) $y, (int) $y1, (int) $y0n];
                        $compensationsY = CompensationDollarsSix::whereIn('age', $ages)->get()->keyBy('age');
                        break;

                    case 'CRC':
                        $ages = [(int) $y, (int) $y1, (int) $y0n];
                        $currencySymbol = '₡';
                        $compensationsY = CompensationColonsSix::whereIn('age', $ages)->get()->keyBy('age');
                        break;
                }

                $compensation_y = $compensationsY[(int) $y] ?? null;
                $compensation_y1 = $compensationsY[(int) $y1] ?? null;
                $compensation_y0n = $compensationsY[(int) $y0n] ?? null;

                $ny = $compensation_y ? $compensation_y->Nx : null;
                $ny1 = $compensation_y1 ? $compensation_y1->Nx : null;
                $nyon = $compensation_y0n ? $compensation_y0n->Nx : null;

                $dy = $compensation_y ? $compensation_y->Dx2 : null;
                $dy1 = $compensation_y1 ? $compensation_y1->Dx2 : null;
                $dyon = $compensation_y0n ? $compensation_y0n->Dx2 : null;
                $annuity_y = $SAT * $percentagedisability_y;
            }

            //caso 3 sin conyugue y sin hijos menores de 18 años el porcentage es de 70%
            if (!$has_spouse && !$has_children_under_18) {
                $percentagedisability = 0.70;
            }
        }

        //salario para los valores de X
        $annuity = $SAT * $percentagedisability;

        //calculos para X
        $tVx = round((($nx - $nxon) / $dx - (11 / 24) * (1 - $dxon / $dx)) * $annuity * 1.028, 2);
        $tVx1 = round((($nx1 - $nxon) / $dx1 - (11 / 24) * (1 - $dxon / $dx1)) * $annuity * 1.028, 2);

        //calculos para y 
        if ($has_spouse && $has_children_under_18) {
            $tVy = round((($ny - $nyon) / $dy - (11 / 24) * (1 - $dyon / $dy)) * $annuity_y * 1.028, 2);
            $tVy1 = round((($ny1 - $nyon) / $dy1 - (11 / 24) * (1 - $dyon / $dy1)) * $annuity_y * 1.028, 2);
        }

        // Calcular la diferencia de días
        $differenceInDays3 = $contingency_date->diff($court)->days;

        // Obtener el año original de $U12 y agregar la diferencia de años
        $newYear = $contingency_date->format('Y') + intdiv($differenceInDays3, 365);

        // Crear la nueva fecha con el año actualizado
        $newDate = DateTime::createFromFormat('Y-m-d', "$newYear-" . $contingency_date->format('m-d'));
        $anniversary_of_the_contingency = $newDate;
        $court->setTime(0, 0, 0);  // Establecer las horas, minutos y segundos a 00:00
        $anniversary_of_the_contingency->setTime(0, 0, 0);  // Lo mismo para la segunda fecha
        $interval = $court->diff($anniversary_of_the_contingency);
        $h = $interval->days;
        $daysInYear = 365;
        $remainingDays = $daysInYear - $h;
        $h365 = ($remainingDays / $daysInYear) * 100;
        $h_365 = ($h / $daysInYear) * 100;
        $roundedPercentageH365 = round($h365, 2);
        $percentageDecimalH365 = $roundedPercentageH365 / 100;
        $roundedPercentageH_365 = round($h_365, 2);
        $percentageDecimalH_365 = $roundedPercentageH_365 / 100;
        $balanceReserve = round(($percentageDecimalH365 * $tVx) + ($percentageDecimalH_365 * $tVx1), 2);

        //calculos para y 
        if ($has_spouse && $has_children_under_18) {
            $balanceReserve = $balanceReserve + round(($percentageDecimalH365 * $tVy) + ($percentageDecimalH_365 * $tVy1), 2);
        }

        //dd($balanceReserve);
    }

    // Factores de conmutación
    public function calculateCommutationFactor($cpath, $id)
    {

        $value = 0;
        //Buscamos la actividad de GIS 
        $activityGis = Activity::where('id', $id)->first();

        if ($activityGis) {

            //Buscamos la actividad del servicio de incapacidad permanente
            $activityPeip = Activity::where('parent_id', $activityGis->id)
                ->where('service_id', Service::SERVICE_PE_IP_SORT_MNK)
                ->first();

            //Si existe una incapcidad permanente entonces realizamos los calculos
            if ($activityPeip) {

                //Aqui se encuentra la primera incapacidades
                $peIpSort = PeIpSort::where('activity_id', $activityPeip->id)->first();

                //Fecha de nacimiento del afiliado
                $birthdate = new DateTime($activityPeip->affiliate->birthday);

                //Fecha de clasificación
                $contingency_date = new DateTime($peIpSort->casedata_date_classif);

                // Calcula la diferencia en días de la fecha de nacimiento VS respecto a la fecha de la PCG
                $differenceInDays = $contingency_date->diff($birthdate)->days;

                // Realiza el cálculo y redondea
                $X0 = floor($differenceInDays / 360 + 0.5);

                $sat = $peIpSort->calc_annual_salary;
                $percentagedisability = floatval(str_replace('%', '', $peIpSort->casedata_percent)) / 100;


                if ($peIpSort->casedata_type_disability == 'IMP - Incapacidad menor permanente') {
                    $n = 5;
                    $pps = $activityGis->gis_sort->pps_minor_permanent_disability;

                    $raimp = $sat * $percentagedisability;

                    //PPS (T) - (5xRAIMP).(1/1,028)	
                    $value = $pps / ($n * $raimp) * (1 / 1.028);

                    $value = $value * 100;

                    $activityGis->gis_sort->update([
                        "factor_conmutation" => $value
                    ]);
                    ;
                }

                if ($peIpSort->casedata_type_disability == 'IPP - Incapacidad parcial permanente') {

                    $n = 10;
                    $pps = $activityGis->gis_sort->pps_partial_permanent_disability;
                    $raimp = $sat * $percentagedisability;

                    //PPS (T) - (10xRAIMP).(1/1,028)	
                    $value = $pps / ($n * $raimp) * (1 / 1.028);

                    $value = $value * 100;

                    $activityGis->gis_sort->update([
                        "factor_conmutation" => $value
                    ]);
                }

                if ($peIpSort->casedata_type_disability == 'ITP - Incapacidad total permanente') {
                    $n = 10;
                    $pps = $activityGis->gis_sort->pps_total_permanent_disability;
                    $raimp = $sat * $percentagedisability;

                    $value = 0;

                    if ($X0 <= 70) {
                        $value = $pps / ((78 - $X0) * $raimp) * (1 / 1.028);
                    }

                    if ($X0 > 70 && $X0 <= 85) {
                        $value = $pps / ((85 - $X0) * $raimp) * (1 / 1.028);
                    }

                    if ($X0 >= 86) {
                        $value = $pps / ((113 - $X0) * $raimp) * (1 / 1.028);
                    }

                    $value = $value * 100;

                    $activityGis->gis_sort->update([
                        "factor_conmutation" => $value
                    ]);
                }

                return response()->json([
                    "success" => true,
                    "message" => "Factor de conmutación realizado"
                ]);
            }
        }
    }

    // 40. Notificación al patrono de caso no amparado. COPIA AL INTERMEDIARIO Y AL ASEGURADO
    public function _buildBodyEmailNotificationCaseNotCovered($textAdministrativeOrMedical, $activityGis, $activityPolicy = null)
    {
        $numberCase = $activityGis->gis_sort->formatCaseNumber(); // Numero de caso
        $insuredName = Utilities::formatName(optional($activityGis->affiliate)->full_name ?? ''); // Nombre del asegurado
        $numberIdentification = $activityGis->affiliate->doc_number; // Número de identificación del asegurado
        $eventDate = Utilities::formatDate($activityGis->gis_sort->date_accident); // Fecha del evento
        $eventType = $activityGis->gis_sort->type_report; // Tipo de evento
        if (!$activityPolicy) {
            $activityPolicy = Activity::query()
                ->where('id', $activityGis->parent_id)
                ->first();
        }
        $policyholderName = Utilities::formatName($activityPolicy->affiliate->first_name ?? ''); // Nombre del tomador
        $policySort = $activityPolicy->policy_sort->formatSortNumber(); // poliza #sort

        $policy_contacts = ""; // Nombre de la persona que presentó el reporte
        $model = $activityGis->gis_sort->model;

        if ($model) {
            if ($model == 'PolicyContact') {
                $policy_contacts = PolicyContact::where('id', $activityGis->gis_sort->responsible_id)->get()->map(function ($item) {
                    return (object) [
                        'name_responsible' => $item->name_responsible,
                    ];
                });
            }

            if ($model == 'User') {
                $policy_contacts = User::where('id', $activityGis->gis_sort->responsible_id)->get()->map(function ($item) {
                    return (object) [
                        'name_responsible' => $item->full_name,
                    ];
                });
            }
        } else {
            $policy_contacts = $activityGis->parent_activity->policy_sort->policy_contacts;
            if (count($policy_contacts) == 0) {
                $policy_contacts = [PolicyContact::first()];
            }
        }

        $reportSubmitter = $policy_contacts && count($policy_contacts) > 0 ? ucwords(strtolower($policy_contacts[0]->name_responsible)) : '';

        // Agrega el email principal de notificaciones y todos los adicionales
        $notiEmails = PolicySortController::getAdditionalNotificationEmails(
            $activityPolicy->policy_sort->id,
            [
                $activityGis->affiliate->email, // Asegurado
                $activityPolicy->policy_sort->email, // Intermediario
            ]
        );
        $emails = implode(',', $notiEmails);

        $emailBuild = TemplateBuilder::build(
            Templates::UNCOVERED_CASE_NOTIFICATION_TO_EMPLOYER,
            [
                'policy_sort' => $policySort,
                'name' => $policyholderName ?? '',
                'case_number' => $numberCase,
                'insured_name' => $insuredName,
                'number_identification' => $numberIdentification,
                'event_type' => $eventType,
                'event_date' => $eventDate,
                'report_submitter' => $reportSubmitter,
                'text_administrative_or_medical' => $textAdministrativeOrMedical,
            ]
        );

        return [
            'subject' => $emailBuild['subject'],
            'body' => $emailBuild['body'],
            'policySort' => $policySort,
            'emails' => $emails
        ];
    }

    public function sendSurveyGis()
    {
        try {
            DB::beginTransaction();

            $host = request()->getHost();
            $gisSorts = GisSort::whereDate('created_at', Carbon::today()->subDays(5))->get();
            $camel = 0;

            foreach ($gisSorts as $gisSort) {

                $textEncuestasPatient = "<p><a href='{$host}/encuesta/{$gisSort->activity_id}/mnk/patient' style='color: #007bff;'>Hacer clic aquí para responder la encuesta</a></p>";
                $textEncuestasClient = "<p><a href='{$host}/encuesta/{$gisSort->activity_id}/mnk/client' style='color: #007bff;'>Hacer clic aquí para responder la encuesta</a></p>";
                ;

                if ($gisSort->activity->affiliate->email) {

                    $subject = "Encuesta de servicio tras atención médica";
                    $emails = [$gisSort->activity->affiliate->email];
                    $full_name = $gisSort->activity->affiliate->full_name ? ucwords(strtolower($gisSort->activity->affiliate->full_name)) : '';
                    $this->sedMailGis($emails, $full_name, $gisSort, $textEncuestasPatient, $subject);

                    $sentSurvey = new SentSurvey();
                    $sentSurvey->type_survey = 'patient';
                    $sentSurvey->activity_id = $gisSort->activity_id;
                    $sentSurvey->state = 'sent';
                    $sentSurvey->save();
                    $camel++;
                }

                if ($gisSort->activity->parent->affiliate->email) {
                    $subject = "Encuesta de servicio tras reporte de incidente laboral";
                    $emails = [$gisSort->activity->parent->affiliate->email];
                    $full_name = $gisSort->activity->affiliate->full_name ? ucwords(strtolower($gisSort->activity->affiliate->full_name)) : '';
                    $this->sedMailGis($emails, $full_name, $gisSort, $textEncuestasClient, $subject);

                    $sentSurvey = new SentSurvey();
                    $sentSurvey->type_survey = 'client';
                    $sentSurvey->activity_id = $gisSort->activity_id;
                    $sentSurvey->state = 'sent';
                    $sentSurvey->save();
                    $camel++;
                }
            }


            DB::commit();

            return response()->json([
                'message' => 'Encuestas enviadas con exito.',
                'count' => $camel
            ], 200);
        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'message' => 'Error al enviar encuestas.',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function sedMailGis($emails, $full_name, $gisSort, $textEncuestas, $subject)
    {

        $text = [
            "text" => "
                    ¡Hola!
                    Esperamos que hayas tenido una excelente experiencia con el servicio SORT de MNK Seguros.
                    Te invitamos a responder nuestra breve encuesta de satisfacción. Tu opinión es muy importante para nosotros.
                    $textEncuestas
                    ¡Gracias por tu tiempo y por confiar en nosotros
                    ",
            "sender" => "MNK seguros"
        ];

        $mailSent = new SendDocumentDataBase(
            implode(',', $emails),
            $subject,
            "<EMAIL>",
            $subject,
            $text,
            "<EMAIL>",
            [],
            "send_document_db",
            1,
            request()->getHost(),
            $gisSort->activity_id,
            null,
            $gisSort->activity->service_id
        );

        // Capturar el resultado del envío
        $result = $mailSent->sendMail();

        //Registramos los datos del correo enviado para la trazabilidad
        $mailBoardController = new MailBoardController();
        $mailBoardController->createRegisterMail(
            $gisSort->activity->id,
            $gisSort->activity->service->id,
            $gisSort->activity->parent->policy_sort->consecutive,
            'Asegurado',
            $full_name,
            $gisSort->activity->affiliate->doc_number,
            $subject,
            $text,
            $emails,
            $result,
            null
        );
    }
}
