<?php

namespace App\Http\Controllers\Services;

use App\Actions\ActionAdministrativePayment;
use App\Actions\ActionMedicationServiceSort;
use App\Actions\ActionPeexpenserecognition;
use App\Actions\ActionReintegrate;
use App\Activity;
use App\ActivityActionDocument;
use App\ActivityDocument;
use App\AdministrativePayment;
use App\Area;
use App\Client;
use App\EconomicBenefit;
use App\Http\Controllers\ActionController;
use App\Http\Controllers\Controller;
use App\IssuerAdministrativePayment;
use App\IssuerReintegrate;
use App\Mail\SendDocumentDataBase;
use App\MailTemplates\Constants\Senders;
use App\MedicationServiceControlledMedication;
use App\MedicationServiceDiagnostics;
use App\MedicationServiceMedicalPrescription;
use App\OtherChargesAdministrativePayment;
use App\OtherChargesReintegrate;
use App\Providers\AppServiceProvider;
use App\ReceiverAdministrativePayment;
use App\ReceiverReintegrate;
use App\Reintegrate;
use App\Service;
use App\ServiceDetailAdministrativePayment;
use App\ServiceDetailReintegrate;
use App\State;
use App\States\StateAdministrativePayment;
use App\States\StateMedicationServiceSort;
use App\States\StateReintegrate;
use App\States\StateRenewal;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use PDF;

class AdministrativePaymentController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function form(Request $req, $cpath, $id)
    {

        $client = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('client_id', $client->id)->where('id', $id)->first();

        $activiyDocumentsSoporte = ActivityDocument::where('activity_id', $id)->where('document_id',295)->get();
        $activiyDocumentsHacienda = ActivityDocument::where('activity_id', $id)->where('document_id',296)->get();
        $activiyDocumentsFacturas = ActivityDocument::where('activity_id', $id)->where('document_id',297)->get();

        $issuerAdministrativePayment = IssuerAdministrativePayment::where('activity_id', $id)->first();
        //$issuerAdministrativePayment = IssuerAdministrativePayment::where('activity_id', $id)->with(['serviceDetails'])->get();
        //$issuerIds = $issuerAdministrativePayment->pluck('id');
        //$total = ServiceDetailAdministrativePayment::whereIn('issuer_id', $issuerIds)->sum('total_line_amount');

        $disabled = $activity->state_id != StateAdministrativePayment::FACTURA_ELECTRONICA_REVISION;

        if (auth()->user()->area_id == Area::PROVIDER) {
            $disabled = true;
        }

        return view('services.administrative_payment.form.form', [
            'id' => $id,
            'activity' => $activity,
            'activiyDocumentsSoporte' => $activiyDocumentsSoporte,
            'activiyDocumentsHacienda' => $activiyDocumentsHacienda,
            'activiyDocumentsFacturas' => $activiyDocumentsFacturas,
            'issuerAdministrativePayment' => $issuerAdministrativePayment,
            'disabled' => $disabled ,
        ]);
    }

    public function save(Request $req, $cpath, $id)
    {

        DB::beginTransaction();
        try {

            $client = Client::where('path', $cpath)->firstOrFail();
            $activity = Activity::where('client_id', $client->id)->where('id', $id)->first();

            if (!$activity) {
                $errors = "No se encontró la actividad del servicio de pagos administrativos.";
                throw new \Exception($errors);
            }

            if ($req->input('invoice_status') == 'approved') {
                $this->approveInvoice($client, $id);
            } else {
                $this->invoiceRejection($client, $id, $req->input('description'));
            }

            $issuerAdministrativePayment = IssuerAdministrativePayment::where('activity_id', $id)->with(['serviceDetails'])->get();
            $issuerIds = $issuerAdministrativePayment->pluck('id');
            $total = ServiceDetailAdministrativePayment::whereIn('issuer_id', $issuerIds)->sum('total_line_amount');

            $administrativePayment = $activity->administrative_payment;

            $administrativePayment->reason = $req->input('description') ?? '';
            $administrativePayment->total_approved = $total ?? 0;
            $administrativePayment->invoice_status = $req->input('invoice_status');
            $administrativePayment->save();


            DB::commit();
            return redirect('servicio/' . $id);

        } catch (\Exception $e) {

            DB::rollBack();
            $error = $e->getMessage();
            dd($error);
        }

    }


    public function registerAdministrativePayment(Request $request, $cpath, $id){

        DB::beginTransaction();
        try{
            $client = Client::where('path', $cpath)->firstOrFail();

            $activityNew = new Activity();
            $activityNew->parent_id = null;
            $activityNew->client_id = $client->id;
            $activityNew->affiliate_id = auth()->user()->provider->affiliate_id;
            $activityNew->service_id = Service::SERVICE_ADMINISTRATIVE_PAYMENTS_MNK;
            $activityNew->state_id = State::REGISTRADO;
            $activityNew->user_id = auth()->user()->id;
            $activityNew->save();

            $administrativePaymentNew = new AdministrativePayment();
            $administrativePaymentNew->activity_id = $activityNew->id;
            $administrativePaymentNew->iban_account = $request->iban_account;
            $administrativePaymentNew->save();

            $categorias = [
                'archivosPdf' => 295,
                'facturaXML' => 296,
                'respuestaXML' => 297
            ];

            foreach ($categorias as $categoria => $documentId) {

                if ($request->hasFile($categoria)) {
                    $archivos = $request->file($categoria);

                    // Manejar caso de un solo archivo (no array)
                    if (!is_array($archivos)) {
                        $archivos = [$archivos]; // Convertirlo en array para iterarlo igual
                    }

                    foreach ($archivos as $archivo) {

                        //validar que la estructura del xml  facturaXML
                        if ($categoria === 'facturaXML' ) {

                            $errorValidacion = $this->validarEstructuraXML($archivo, $activityNew->id);

                            if ($errorValidacion !== true) {
                                DB::rollBack();

                                return response()->json([
                                    'status' => 'error',
                                    'message' => "Error en el archivo XML de factura electrónica ".$archivo->getClientOriginalName().": $errorValidacion"
                                ], 400);
                            }

                        }

                        //carga doc a s3
                        $this->storeActivityDocument($archivo, $documentId, $activityNew->id);

                    }
                }
            }

            $activityAction = ActionController::create($activityNew->id, ActionAdministrativePayment::CARGA_DOCUMENTO,  'CARGA DOCUMENTO');

            DB::commit();

            return response()->json([
                'success' => true,
                'administrative_payment_id' => $administrativePaymentNew->id,
                'activity_id' => $activityNew->id,
                'message' => 'Su solicitud de reintegro ha sido creada exitosamente. # trámite: '. $administrativePaymentNew->id
                ], 200);

        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'success' => false,
                'status' => 'error',
                'message' => $e->getMessage(),
            ],500);

        }

    }


    private function determineOverallStatus($total, $approved, $rejected, $requestInfo)
    {
        // Si no hay facturas procesadas
        if ($total == 0) {
            return 'empty';
        }

        // Si hay al menos una solicitud de información
        if ($requestInfo > 0) {
            return 'pending_info';
        }

        // Si todas son aprobadas
        if ($approved == $total) {
            return 'all_approved';
        }

        // Si todas son rechazadas
        if ($rejected == $total) {
            return 'all_rejected';
        }

        // Si hay al menos una aprobada y el resto rechazadas
        if ($approved > 0 && ($approved + $rejected == $total)) {
            return 'partial_approved';
        }

        // Estado por defecto para cualquier otra combinación
        return 'mixed';
    }

    function validarEstructuraXML($archivo, $activity_id)
    {
        try {
            $xml = simplexml_load_file($archivo->path());

            // Validar etiquetas principales
            $etiquetasPrincipales = [
                'Clave', 'CodigoActividad', 'NumeroConsecutivo', 'FechaEmision',
                'Emisor', 'Receptor', 'DetalleServicio', 'ResumenFactura'
            ];

            foreach ($etiquetasPrincipales as $etiqueta) {
                if (!isset($xml->$etiqueta)) {
                    return "Falta la etiqueta principal: $etiqueta";
                }
            }

            // Validar estructura del Emisor
            $etiquetasEmisor = [
                'Nombre', 'Identificacion', 'NombreComercial', 'Ubicacion',
                'Telefono', 'CorreoElectronico'
            ];
            foreach ($etiquetasEmisor as $etiqueta) {
                if (!isset($xml->Emisor->$etiqueta)) {
                    return "Falta la etiqueta en Emisor: $etiqueta";
                }
            }

            // Validar Identificación del Emisor
            if (!isset($xml->Emisor->Identificacion->Tipo) || !isset($xml->Emisor->Identificacion->Numero)) {
                return "Falta la información de identificación en Emisor.";
            }

            // Validar y convertir TotalVentaNeta
            if (!isset($xml->ResumenFactura->TotalVentaNeta) || !is_numeric((string)$xml->ResumenFactura->TotalVentaNeta)) {
                return "El valor de 'TotalVentaNeta' en ResumenFactura no es numérico o no está presente.";
            }
            $totalVentaNeta = (float) $xml->ResumenFactura->TotalVentaNeta;

            if (!isset($xml->ResumenFactura->TotalImpuesto) || !is_numeric((string)$xml->ResumenFactura->TotalImpuesto)) {
                return "El valor de 'TotalImpuesto' en ResumenFactura no es numérico o no está presente.";
            }
            $totalImpuestos = (float) $xml->ResumenFactura->TotalImpuesto;

            if (!isset($xml->ResumenFactura->TotalComprobante) || !is_numeric((string)$xml->ResumenFactura->TotalComprobante)) {
                return "El valor de 'TotalComprobante' en ResumenFactura no es numérico o no está presente.";
            }

            $TotalComprobante = (float) $xml->ResumenFactura->TotalComprobante;

            $existNumFact = IssuerAdministrativePayment::where('consecutive_number', $xml->NumeroConsecutivo)->get();

            if ($existNumFact->isNotEmpty()) {
                return "Ya existe un registro con el mismo número de factura (etiqueta: NumeroConsecutivo): $xml->NumeroConsecutivo.";
            }


            if (!isset($xml->ResumenFactura->CodigoTipoMoneda->CodigoMoneda) ) {
                return "Falta la etiqueta 'CodigoMoneda' en ResumenFactura.";
            }

            $issuerAdministrativePaymentNew = new IssuerAdministrativePayment();
            $issuerAdministrativePaymentNew->activity_id = $activity_id;
            $issuerAdministrativePaymentNew->name = $xml->Emisor->Nombre;
            $issuerAdministrativePaymentNew->commercial_name = $xml->Emisor->NombreComercial;
            $issuerAdministrativePaymentNew->id_type = $xml->Emisor->Identificacion->Tipo;
            $issuerAdministrativePaymentNew->id_number = $xml->Emisor->Identificacion->Numero;
            $issuerAdministrativePaymentNew->province = isset($xml->Emisor->Ubicacion->Provincia) ? $xml->Emisor->Ubicacion->Provincia : '';
            $issuerAdministrativePaymentNew->canton = isset($xml->Emisor->Ubicacion->Canton) ? $xml->Emisor->Ubicacion->Canton : '';
            $issuerAdministrativePaymentNew->district = isset($xml->Emisor->Ubicacion->Distrito) ? $xml->Emisor->Ubicacion->Distrito : '';
            $issuerAdministrativePaymentNew->neighborhood = isset($xml->Emisor->Ubicacion->Barrio) ? $xml->Emisor->Ubicacion->Barrio : '';
            $issuerAdministrativePaymentNew->address = isset($xml->Emisor->Ubicacion->OtrasSenas) ? $xml->Emisor->Ubicacion->OtrasSenas : '';
            $issuerAdministrativePaymentNew->country_code = isset($xml->Emisor->Telefono->CodigoPais) ? $xml->Emisor->Telefono->CodigoPais : '';
            $issuerAdministrativePaymentNew->phone_number = isset($xml->Emisor->Telefono->NumTelefono) ? $xml->Emisor->Telefono->NumTelefono : '';
            $issuerAdministrativePaymentNew->email = $xml->Emisor->CorreoElectronico;
            $issuerAdministrativePaymentNew->invoice_key = $xml->Clave;
            $issuerAdministrativePaymentNew->activity_code = $xml->CodigoActividad;
            $issuerAdministrativePaymentNew->consecutive_number = $xml->NumeroConsecutivo;
            $issuerAdministrativePaymentNew->net_sales_total = $totalVentaNeta ?? 0;
            $issuerAdministrativePaymentNew->total_tax = $totalImpuestos ?? 0;
            $issuerAdministrativePaymentNew->total_invoice_amount = $TotalComprobante ?? 0;
            $issuerAdministrativePaymentNew->total_amountDue = $totalVentaNeta - (($totalVentaNeta * 2) / 100) + $totalImpuestos;
            $issuerAdministrativePaymentNew->currency = $xml->ResumenFactura->CodigoTipoMoneda->CodigoMoneda ?? '';

            if (isset($xml->FechaEmision)) {
                $issueDate = Carbon::parse($xml->FechaEmision)->format('Y-m-d H:i:s');
                $issuerAdministrativePaymentNew->issue_date = $issueDate;
            }

            $issuerAdministrativePaymentNew->save();

            // Validar estructura del Receptor
            $etiquetasReceptor = ['Nombre', 'Identificacion', 'Telefono', 'CorreoElectronico'];
            foreach ($etiquetasReceptor as $etiqueta) {
                if (!isset($xml->Receptor->$etiqueta)) {
                    return "Falta la etiqueta en Receptor: $etiqueta";
                }
            }

            if (!isset($xml->Receptor->Identificacion->Tipo) || !isset($xml->Receptor->Identificacion->Numero)) {
                return "Falta la información de identificación en Receptor.";
            }

            $dataReceptor = [
                'issuer_id' => $issuerAdministrativePaymentNew->id,
                'name' => $xml->Receptor->Nombre,
                'id_type' => $xml->Receptor->Identificacion->Tipo,
                'id_number' => $xml->Receptor->Identificacion->Numero,
                'country_code' => isset($xml->Receptor->Telefono->CodigoPais) ? $xml->Receptor->Telefono->CodigoPais : '' ,
                'phone_number' => isset($xml->Receptor->Telefono->NumTelefono) ? $xml->Receptor->Telefono->NumTelefono : '' ,
                'email' => $xml->Receptor->CorreoElectronico,
            ];

            ReceiverAdministrativePayment::insert($dataReceptor);

            // Validar estructura de OtrosCargos (si existe)
            if (isset($xml->OtrosCargos)) {
                foreach ($xml->OtrosCargos as $otrosCargos) {
                    $etiquetasOtrosCargos = ['TipoDocumento', 'NombreTercero', 'Detalle', 'Porcentaje', 'MontoCargo'];
                    foreach ($etiquetasOtrosCargos as $etiqueta) {
                        if (!isset($otrosCargos->$etiqueta)) {
                            return "Falta la etiqueta en OtrosCargos: $etiqueta";
                        }
                    }

                    $dataOtrosCargos = [
                        'issuer_id' => $issuerAdministrativePaymentNew->id,
                        'document_type' => $otrosCargos->TipoDocumento,
                        'third_party_name' => $otrosCargos->NombreTercero,
                        'description' => $otrosCargos->Detalle,
                        'percentage' => $otrosCargos->Porcentaje,
                        'charge_amount' => $otrosCargos->MontoCargo,

                    ];

                    OtherChargesAdministrativePayment::insert($dataOtrosCargos);

                }
            }

            // Validar estructura de DetalleServicio y sus LineaDetalle
            if (!isset($xml->DetalleServicio->LineaDetalle)) {
                return "No se encontraron líneas de detalle en DetalleServicio.";
            }

            foreach ($xml->DetalleServicio->LineaDetalle as $linea) {
                $etiquetasLineaDetalle = [
                    'NumeroLinea', 'Codigo', 'Cantidad',
                    'UnidadMedida', 'Detalle',
                    'PrecioUnitario', 'MontoTotal', 'SubTotal', 'Impuesto',
                    'ImpuestoNeto', 'MontoTotalLinea'
                ];

                foreach ($etiquetasLineaDetalle as $etiqueta) {
                    if (!isset($linea->$etiqueta)) {
                        return "Falta la etiqueta en LineaDetalle: $etiqueta (Línea: {$linea->NumeroLinea})";
                    }
                }

                if (!isset($linea->Impuesto->Codigo) ||
                    !isset($linea->Impuesto->CodigoTarifa) ||
                    !isset($linea->Impuesto->Tarifa) ||
                    !isset($linea->Impuesto->Monto)) {
                    return "Falta información del Impuesto en LineaDetalle (Línea: {$linea->NumeroLinea})";
                }


                $dataDetalleServicio = [
                    'issuer_id' => $issuerAdministrativePaymentNew->id,
                    'invoice_id' => $linea->Codigo,
                    'line_number' => $linea->NumeroLinea,
                    'code' => $linea->Codigo,
                    'commercial_code_type' => $linea->CodigoComercial->Tipo ?? '',
                    'commercial_code' => $linea->CodigoComercial->Codigo ?? '',
                    'quantity' => $linea->Cantidad,
                    'unit_measure' => $linea->UnidadMedida,
                    'commercial_unit' => $linea->UnidadMedidaComercial ?? '',
                    'description' => $linea->Detalle,
                    'unit_price' => $linea->PrecioUnitario,
                    'total_amount' => $linea->MontoTotal,
                    'subtotal' => $linea->SubTotal,
                    'tax_code' => $linea->Impuesto->Codigo,
                    'tax_code_rate' => $linea->Impuesto->CodigoTarifa,
                    'tax_rate' => $linea->Impuesto->Tarifa,
                    'tax_amount' => $linea->Impuesto->Monto,
                    'net_tax' => $linea->ImpuestoNeto,
                    'total_line_amount' => $linea->MontoTotalLinea,
                ];

                ServiceDetailAdministrativePayment::insert($dataDetalleServicio);

            }


            return true;

        } catch (\Exception $e) {
            return "Error procesando el XML: " . $e->getMessage();
        }
    }

    protected function storeActivityDocument($file, $documentId, $id)
    {
        $originalName = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME); // Nombre sin extensión
        $safeName = Str::slug($originalName); // Reemplazar espacios por guiones y limpiar caracteres especiales

        $originalExtension = $file->getClientOriginalExtension();
        //$uniqueName = Str::random(10) . uniqid() . '.' . $originalExtension;

        $uniqueName = $safeName . '_' . uniqid() . '.' . $originalExtension;  // Evitar colisión de nombres agregando un timestamp único


        $filePath = "documents_administrative_payment/{$uniqueName}";

        $file->storeAs('documents_administrative_payment', $uniqueName, 's3');

        $activityDocument = new ActivityDocument();
        $activityDocument->document_id = $documentId;
        $activityDocument->activity_id = $id;
        $activityDocument->path = $filePath;
        $activityDocument->uploaded_at = Carbon::now();
        $activityDocument->save();
    }


    private function sendEmail($emails, $subject, $text, $attachments, $client, $id, $activityAction, $service_id, $nameFrom)
    {
        $mailSent = new SendDocumentDataBase(
            implode(',', $emails),         // Correos a enviar
            $subject,                      // Asunto del correo
            "<EMAIL>",            // Remitente
            $nameFrom,                      // Asunto
            $text,                         // Cuerpo del email
            "<EMAIL>",  // Email de respuesta
            $attachments,                  // Archivos adjuntos
            "send_document_db",            // Tipo de envío
            $client,                       // Información del cliente
            request()->getHost(),          // Dominio
            $id,                           // ID de la actividad
            $activityAction,           // ID de la acción de la actividad
            $service_id // ID del servicio
        );

        $mailSent->sendMail();
    }

    public function descargarXmlEjemplo()
    {
        // URL del archivo XML en S3
        $url = 'https://mnk-prod.s3.us-east-1.amazonaws.com/public/EjemplXmlFacturaServReintegros.xml';

        // Obtener el contenido del archivo
        $contenidoArchivo = file_get_contents($url);

        // Establecer las cabeceras apropiadas para la descarga
        $cabeceras = [
            'Content-Type' => 'application/xml',
            'Content-Disposition' => 'attachment; filename="ejemplo-factura-xml.xml"',
        ];

        // Devolver el archivo como una descarga
        return response($contenidoArchivo, 200, $cabeceras);
    }


    //RECHAZO DE FACTURAS
    public function invoiceRejection($client, $activity_id, $reason) {

        DB::beginTransaction();
        try{

            $activity = Activity::where('service_id', Service::SERVICE_ADMINISTRATIVE_PAYMENTS_MNK)->where('id', $activity_id)->firstOrFail();
            $fullName = mb_convert_case(mb_strtolower( $activity->affiliate->full_name ?? ''), MB_CASE_TITLE, "UTF-8");

            $issuerAdministrativePayment = IssuerAdministrativePayment::where('activity_id', $activity_id)
                ->with(['serviceDetails'])->get();

            $activityAction = ActionController::create($activity_id, ActionAdministrativePayment::RECHAZAR_FACTURA,  'RECHAZO FACTURA');

            $table = "<table style='border: 1px solid black; border-collapse: collapse;'>";
            $table .= "<tr>";
            $table .= "<th style='background-color: #8BC34A; color: black; border: 1px solid black; padding: 5px;' >N° de factura </th>";
            $table .= "<th style='background-color: #8BC34A; color: black; border: 1px solid black; padding: 5px;' >Código </th>";
            $table .= "</tr>";

            $total = 0;
            foreach ($issuerAdministrativePayment as $row) {
                foreach($row->serviceDetails as $index => $rowDetails){
                    //if ($rowDetails->state == 'reject') {

                        $table .= "<tr>";
                        $table .= "<td style='border: 1px solid black; padding: 5px;' >$row->consecutive_number</td>";
                        $table .= "<td style='border: 1px solid black; padding: 5px;' >$rowDetails->code</td>";
                        $table .= "</tr>";

                    //}
                }

            }

            $table .= "<tr>";
            $table .= "<td colspan='2' style='border: 1px solid black; padding: 5px; background-color: #f2f2f2;'><strong>Motivo:</strong>".$reason."</td>";
            $table .= "</tr>";

            $table .= "</table>";

            $text = [
                "text" => "¡Buen día, $fullName!

                            Le informamos que luego de la revisión realizada, se determina que la(s) factura(s) que se detalla(n), lamentablemente no puede(n) ser pagada(s) por el motivo que se indica a continuación:
                            $table
                            Esta suma fue depositada en la cuenta IBAN registrada para ese propósito. 
                            
                            Si tiene alguna consulta, por favor, contáctenos al correo electrónico <EMAIL> o al teléfono 4102-7600 ext. 8129-8130. ¡Será un gusto atenderle!

                            Nuestro propósito es fortalecer la prevención en salud y seguridad laboral del país, generando siempre bienestar.",
                "sender" => Senders::MNK_INDEMNITIES
            ];

            $attachments = [];

            $nameFrom = 'Rechazo de factura(s)';
            $subject = "Rechazo de factura(s)";

            $emails = [$activity->affiliate->email];

            $this->sendEmail($emails, $subject, $text, $attachments, $client, $activity_id, $activityAction->id, $activity->service_id, $nameFrom);

            DB::commit();

            return response()->json([
                'status' => 'success',
                'message' => 'RECHAZO FACTURA',
                'activity_id' => $activity_id], 200);

        } catch (\Exception $e) {
            DB::rollBack();
            throw new \Exception('Error al reportar RECHAZO FACTURA : '.$e->getMessage());

        }

    }

    //APROBAR FACTURA
    public function approveInvoice($client, $activity_id) {

        DB::beginTransaction();
        try{

            $activity = Activity::where('service_id', Service::SERVICE_ADMINISTRATIVE_PAYMENTS_MNK)->where('id', $activity_id)->firstOrFail();
            $fullName = mb_convert_case(mb_strtolower( $activity->affiliate->full_name ?? ''), MB_CASE_TITLE, "UTF-8");
            $fecha = Carbon::now()->format('d/m/Y');

            $issuerAdministrativePayment = IssuerAdministrativePayment::where('activity_id', $activity_id)
                ->with(['serviceDetails'])->get();

            $activityAction = ActionController::create($activity_id, ActionAdministrativePayment::APROBAR_FACTURA,  'APROBAR FACTURA');

            $table = "<table style='border: 1px solid black; border-collapse: collapse;' >";
            $table .= "<tr>";
            $table .= "<th style='background-color: #8BC34A; color: black; border: 1px solid black; padding: 5px;' >N° de factura</th>";
            $table .= "<th style='background-color: #8BC34A; color: black; border: 1px solid black; padding: 5px;' >Código</th>";
            $table .= "<th style='background-color: #8BC34A; color: black; border: 1px solid black; padding: 5px;' >Monto</th>";
            $table .= "</tr>";

            $total = 0;

            foreach ($issuerAdministrativePayment as $row) {
                foreach($row->serviceDetails as $index => $rowDetails){
                    //if ($rowDetails->state == 'approve') {
                        $table .= "<tr>";
                        $table .= "<td style='border: 1px solid black; padding: 5px;' >$row->consecutive_number</td>";
                        $table .= "<td style='border: 1px solid black; padding: 5px;' >$rowDetails->code</td>";
                        $table .= "<td style='border: 1px solid black; padding: 5px;' >".number_format($rowDetails->total_line_amount ?? 0, 2, ',', '.')."</td>";
                        $table .= "</tr>";

                        $total += $rowDetails->total_line_amount ?? 0;
                    //}
                }

            }

            $table .= "<tr>";
            $table .= "<th style='background-color: #8BC34A; color: black; border: 1px solid black; padding: 5px;' >Total pagado </th>";
            $table .= "<th style='background-color: #8BC34A; color: black; border: 1px solid black; padding: 5px;' ></th>";
            $table .= "<th style='background-color: #8BC34A; color: black; border: 1px solid black; padding: 5px;' >".number_format($total, 2, ',', '.')."</th>";
            $table .= "</tr>";
            $table .= "</table>";

            $text = [
                "text" => "¡Buen día, $fullName!

                            Le informamos que el $fecha se procedió con el pago de la (s) factura (s), que se detalla(n) a continuación, por los servicios brindados al amparo nuestro Seguro Obligatorio de Riesgos del Trabajo:
                            $table
                            Esta suma fue depositada en la cuenta IBAN registrada para ese propósito. 
                            
                            Si tiene alguna consulta, por favor, contáctenos al correo electrónico <EMAIL> o al teléfono 4102-7600 ext. 8129-8130-8140.
                            
                            Nuestro propósito es fortalecer la prevención en salud y seguridad laboral del país, generando siempre bienestar.",
                "sender" => Senders::MNK_INDEMNITIES
            ];

            $attachments = [];

            $nameFrom = 'Pago de factura(s)';
            $subject = "Pago de factura(s)";

            $emails = [$activity->affiliate->email];

            $this->sendEmail($emails, $subject, $text, $attachments, $client, $activity_id, $activityAction->id, $activity->service_id, $nameFrom);

            DB::commit();

            return response()->json([
                'status' => 'success',
                'message' => 'APROBAR FACTURA',
                'activity_id' => $activity_id], 200);

        } catch (\Exception $e) {
            DB::rollBack();
            throw new \Exception('Error al reportar APROBAR FACTURA : '.$e->getMessage());

        }



    }


}