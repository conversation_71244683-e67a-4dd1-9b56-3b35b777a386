<?php

namespace App\Http\Controllers\Services;

use App\Actions\ActionMedicationServiceSort;
use App\Actions\ActionPeexpenserecognition;
use App\Actions\ActionReintegrate;
use App\Activity;
use App\ActivityActionDocument;
use App\ActivityDocument;
use App\Affiliate;
use App\Area;
use App\Client;
use App\EconomicBenefit;
use App\Http\Controllers\ActionController;
use App\Http\Controllers\Controller;
use App\Http\Controllers\Tables\MailBoardController;
use App\IssuerReintegrate;
use App\Mail\SendDocumentDataBase;
use App\MailTemplates\Constants\Senders;
use App\MedicationServiceControlledMedication;
use App\MedicationServiceDiagnostics;
use App\MedicationServiceMedicalPrescription;
use App\OtherChargesReintegrate;
use App\Providers\AppServiceProvider;
use App\ReceiverReintegrate;
use App\Reintegrate;
use App\Service;
use App\ServiceDetailReintegrate;
use App\State;
use App\States\StateMedicationServiceSort;
use App\States\StateReintegrate;
use App\States\StateRenewal;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use PDF;

class ReintegrateServicesController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function form(Request $req, $cpath, $id)
    {

        $client = Client::where('path', $cpath)->firstOrFail();

        //Actividad de medicamentos
        $activity = Activity::where('client_id', $client->id)->where('id', $id)->first();

        $activiyDocumentsSoporte = ActivityDocument::where('activity_id', $id)->where('document_id',285)->get();
        $activiyDocumentsFactGastos = ActivityDocument::where('activity_id', $id)->where('document_id',286)->get();
        $activiyDocumentsHacienda = ActivityDocument::where('activity_id', $id)->where('document_id',287)->get();
        $activiyDocumentsFacturas = ActivityDocument::where('activity_id', $id)->where('document_id',288)->get();
        $activiyDocumentsAdicionales = ActivityDocument::where('activity_id', $id)->where('document_id',290)->get();

        $issuerReintegrate = IssuerReintegrate::where('activity_id', $id)->with(['serviceDetails'])->get();

        $policySortController = new PolicySortController();
        $ubicacion = $policySortController->getLocationNamesFromJson($activity->affiliate->province, $activity->affiliate->canton, $activity->affiliate->district);

        $disabledStates = [
            StateReintegrate::PENDIENTE_INFORMACION_ADICIONAL,
            StateReintegrate::FACTURA_REINTEGRO_APROBADA,
            StateReintegrate::FACTURA_PARCIAL_REINTEGRO_APROBADA,
            StateReintegrate::FACTURA_REINTEGRO_RECHAZADA,
            StateReintegrate::FACTURA_PAGADA,
            StateReintegrate::FACTURA_REINTEGRO_RECHAZADA_POR_AUDITORIA_MEDICA
        ];

        $disabled = in_array($activity->state_id, $disabledStates);

        $disabledStatesInfo = [
            StateReintegrate::INFORMACION_ADICIONAL_CARGADA,
            StateReintegrate::FACTURA_REINTEGRO_EN_REVISION,
        ];

        $disabledRequestInfo = in_array($activity->state_id, $disabledStatesInfo);

        $disabledSave = [
            StateReintegrate::FACTURA_PAGADA,
            StateReintegrate::FACTURA_REINTEGRO_RECHAZADA,
            StateReintegrate::FACTURA_REINTEGRO_RECHAZADA_POR_AUDITORIA_MEDICA
        ];

        $disabledButtonSave = in_array($activity->state_id, $disabledSave);

        if ((auth()->user()->area_id == Area::ASEGURADO || auth()->user()->area_id == Area::TOMADOR) && $activity->state_id != StateReintegrate::PENDIENTE_INFORMACION_ADICIONAL) {
            $disabledButtonSave = true;
        }

        $issuerIds = $issuerReintegrate->pluck('id');
        $query = ServiceDetailReintegrate::whereIn('issuer_id', $issuerIds);

        $countRequestInfo = (clone $query)->where('state', 'request_info')->count();
        $countApproved = (clone $query)->where('state', 'approve')->count();
        $countReject = (clone $query)->where('state', 'reject')->count();
        $totalFacturas = (clone $query)->count();

        $status_result = $this->determineOverallStatus(
            $totalFacturas,
            $countApproved,
            $countReject,
            $countRequestInfo
        );

        $textStatus = '';
        if ($status_result == 'all_approved') {
            $textStatus = "Aprobado para ". $countApproved." rubros";
        } else {
            $textStatus = AppServiceProvider::$STATES_RESULT_REINTEGRATE[$status_result] ?? '';
        }

        return view('services.reintegrate.form.form', [
            'id' => $id,
            'activity' => $activity,
            'ubicacion' => $ubicacion,
            'activiyDocumentsSoporte' => $activiyDocumentsSoporte,
            'activiyDocumentsFactGastos' => $activiyDocumentsFactGastos,
            'activiyDocumentsHacienda' => $activiyDocumentsHacienda,
            'activiyDocumentsFacturas' => $activiyDocumentsFacturas,
            'activiyDocumentsAdicionales' => $activiyDocumentsAdicionales,
            'issuerReintegrate' => $issuerReintegrate,
            'disabled' => $disabled,
            'disabledRequestInfo' => $disabledRequestInfo,
            'disabledButtonSave' => $disabledButtonSave,
            'textStatus' => $textStatus
        ]);
    }

    public function save(Request $req, $cpath, $id)
    {

        DB::beginTransaction();
        try {

            $client = Client::where('path', $cpath)->firstOrFail();
            $activity = Activity::where('client_id', $client->id)->where('id', $id)->first();

            if (!$activity) {
                $errors = "No se encontró la actividad del servicio de reintegro.";
                throw new \Exception($errors);
            }

            if ($activity->state_id == StateReintegrate::PENDIENTE_INFORMACION_ADICIONAL) {
                $this->uploadAdditionalInformation($req, $id); //CARGUE_INFORMACION_ADICIONAL

                $reintegrate = $activity->reintegrate;
                if ($req->input('response_load_additional')){
                    $reintegrate->response_load_additional = $req->input('response_load_additional') ?? '';
                }
                $reintegrate->save();

            } else if ($activity->state_id == StateReintegrate::FACTURA_REINTEGRO_APROBADA) {
                $this->reportPaymentMade($cpath, $id); //REPORTAR PAGO REALIZADO
            } else if ($activity->state_id == StateReintegrate::FACTURA_PARCIAL_REINTEGRO_APROBADA) {
                $this->reportPartialPaymentMade($cpath, $id); // REPORTAR PAGO PARCIAL REALIZADO
            } else {

                $resultDetail = $this->saveDetails($req, $activity);

                if($resultDetail['status'] == 'pending_info' && $activity->state_id == StateReintegrate::REVISION_AUD_MEDICA){
                    $this->requestAdditionalInformation($client, $id); //SOLICITAR_INFORMACION_ADICIONAL
                } else if ( $resultDetail['status'] == 'all_rejected' && ($activity->state_id == StateReintegrate::REVISION_AUD_MEDICA || $activity->state_id == StateReintegrate::INFORMACION_ADICIONAL_CARGADA) ) {
                    $this->rejectionOfMedicalAudit($client, $id); //RECHAZO_AUD_MED
                } else if (($resultDetail['status'] == 'all_approved' || $resultDetail['status'] == 'partial_approved') && ($activity->state_id == StateReintegrate::REVISION_AUD_MEDICA || $activity->state_id == StateReintegrate::INFORMACION_ADICIONAL_CARGADA)){
                    $this->medicalAuditApproval($id); //APROBACION_AUD_MEDICA
                } else if ($resultDetail['status'] == 'all_approved' && $activity->state_id == StateReintegrate::FACTURA_REINTEGRO_EN_REVISION){
                    $this->approveReimbursementInvoice($cpath, $id); //APROBAR_FACTURA_REINTEGRO
                } else if ($resultDetail['status'] == 'partial_approved' && $activity->state_id == StateReintegrate::FACTURA_REINTEGRO_EN_REVISION){
                    $this->partialApprovalInvoiceRefund($cpath, $id); //APROBACION_PARCIAL_FACTURA_REINTEGRO
                } else if ($resultDetail['status'] == 'all_rejected' && $activity->state_id == StateReintegrate::FACTURA_REINTEGRO_EN_REVISION) {
                    $this->rejectReimbursementInvoice($cpath, $id); //RECHAZAR_FACTURA_REINTEGRO
                }

                $reintegrate = $activity->reintegrate;

                if ($req->input('response_load_additional')){
                    $reintegrate->response_load_additional = $req->input('response_load_additional') ?? '';
                }

                $reintegrate->reason = $req->input('description') ?? '';
                $reintegrate->total_approved = $resultDetail['totalApproved'];
                $reintegrate->save();

            }

            DB::commit();
            return redirect('servicio/' . $id);

        } catch (\Exception $e) {

            DB::rollBack();
            $error = $e->getMessage();
            dd($error);
            return redirect('/servicio/' . $id)->withErrors($error)->withInput();
        }

    }

    public function saveDetails($req, $activity){

        try{

            $countRequestInfo = 0;
            $countApproved = 0;
            $countReject = 0;
            $totalApproved = 0;
            $totalFacturas = 0;

            $facturas = $req->input('facturas', []);

            if (empty($facturas)) {
                return [
                    'countRequestInfo' => 0,
                    'countApproved' => 0,
                    'countReject' => 0,
                    'totalApproved' => 0,
                    'status' => 'empty',
                ];
            }

            $ids = collect($facturas)->pluck('id')->filter()->toArray();

            if (empty($ids)) {
                return [
                    'countRequestInfo' => 0,
                    'countApproved' => 0,
                    'countReject' => 0,
                    'totalApproved' => 0,
                    'status' => 'empty',
                ];
            }


            $serviceDetails = ServiceDetailReintegrate::whereIn('id', $ids)->get()->keyBy('id');

            foreach ($facturas as $factura) {
                $id = $factura['id'] ?? null;
                $accion = $factura['accion'] ?? '';

                if (!$id || !isset($serviceDetails[$id])) {
                    continue;
                }

                $totalFacturas++;

                $serviceDetails[$id]->update([
                    'state'  => $accion,
                    'reason' => $factura['motivo'] ?? '',
                ]);


                switch ($accion) {
                    case 'request_info':
                        $countRequestInfo++;
                        break;

                    case 'approve':
                        $countApproved++;
                        $totalApproved += floatval($factura['valor'] ?? 0);
                        break;

                    case 'reject':
                        $countReject++;
                        break;
                }
            }

            $status = $this->determineOverallStatus(
                $totalFacturas,
                $countApproved,
                $countReject,
                $countRequestInfo
            );

            return [
                'countRequestInfo' => $countRequestInfo,
                'countApproved' => $countApproved,
                'countReject' => $countReject,
                'totalApproved' => $totalApproved,
                'status' => $status
            ];

        } catch (\Exception $e) {
            throw new \Exception('Error al guardar saveDetails : '.$e->getMessage());
        }
    }

    private function determineOverallStatus($total, $approved, $rejected, $requestInfo)
    {
        // Si no hay facturas procesadas
        if ($total == 0) {
            return 'empty';
        }

        // Si hay al menos una solicitud de información
        if ($requestInfo > 0) {
            return 'pending_info';
        }

        // Si todas son aprobadas
        if ($approved == $total) {
            return 'all_approved';
        }

        // Si todas son rechazadas
        if ($rejected == $total) {
            return 'all_rejected';
        }

        // Si hay al menos una aprobada y el resto rechazadas
        if ($approved > 0 && ($approved + $rejected == $total)) {
            return 'partial_approved';
        }

        // Estado por defecto para cualquier otra combinación
        return 'mixed';
    }

    //CARGUE_INFORMACION_ADICIONAL
    public function uploadAdditionalInformation($request, $activity_id) {

        try {
            $activityAction = ActionController::create($activity_id, ActionReintegrate::CARGUE_INFORMACION_ADICIONAL,  'CARGUE INFORMACIÓN ADICIONAL');
            $documentId = 290; //Docuementos adicionales


            if ($request->hasFile('documentosAdicionales')) {
                $archivos = $request->file('documentosAdicionales');

                if (!is_array($archivos)) {
                    $archivos = [$archivos]; // Convertirlo en array para iterarlo igual
                }

                foreach ($archivos as $archivo) {
                    //carga doc a s3
                    $this->storeActivityDocument($archivo, $documentId, $activity_id);

                }
            }

        } catch (\Exception $e) {
            throw new \Exception('Error en CARGUE_INFORMACION_ADICIONAL : '.$e->getMessage());
        }

    }
    // APROBACION_AUD_MEDICA
    public function medicalAuditApproval($activity_id) {

        $activityAction = ActionController::create($activity_id, ActionReintegrate::APROBACION_AUD_MEDICA,  'APROBACIÓN AUDITORIA MEDICA');

    }
    //RECHAZO AUDITORIA MEDICA
    public function rejectionOfMedicalAudit($client,$activity_id) {

        DB::beginTransaction();
        try{

            $reintegrate_activity = Activity::where('service_id', Service::SERVICE_REINTEGRATE_MNK)->where('id', $activity_id)->firstOrFail();
            $fullName = mb_convert_case(mb_strtolower( $reintegrate_activity->affiliate->full_name ?? ''), MB_CASE_TITLE, "UTF-8");
            $casoGis = $reintegrate_activity->parent->parent->policy_sort->formatNumberConsecutive();
            $issuerReintegrate = IssuerReintegrate::where('activity_id', $activity_id)
                ->with(['serviceDetails'])->get();

            $activityAction = ActionController::create($activity_id, ActionReintegrate::RECHAZO_AUD_MED,  'RECHAZO AUDITORIA MEDICA');

            $table = "<table style='border: 1px solid black; border-collapse: collapse;'>";
            $table .= "<tr>";
            $table .= "<th style='background-color: #8BC34A; color: black; border: 1px solid black; padding: 5px;' >N° de factura </th>";
            $table .= "<th style='background-color: #8BC34A; color: black; border: 1px solid black; padding: 5px;' >Código </th>";
            $table .= "<th style='background-color: #8BC34A; color: black; border: 1px solid black; padding: 5px;' >Motivo </th>";
            $table .= "</tr>";

            $total = 0;
            foreach ($issuerReintegrate as $row) {
                foreach($row->serviceDetails as $index => $rowDetails){
                    if ($rowDetails->state == 'reject') {

                        $table .= "<tr>";
                        $table .= "<td style='border: 1px solid black; padding: 5px;' >$row->consecutive_number</td>";
                        $table .= "<td style='border: 1px solid black; padding: 5px;' >$rowDetails->code</td>";
                        $table .= "<td style='border: 1px solid black; padding: 5px;' >".$rowDetails->reason."</td>";
                        $table .= "</tr>";

                    }
                }

            }

            $table .= "</table>";

            $text = [
                "text" => "<p>
                            ¡Buen día, $fullName!

                            Luego de la revisión efectuada, se determinó que lamentablemente la (s) factura(s) que se detalla(n), no puede(n) ser pagada(s) al amparo del caso del Seguro Obligatorio de Riesgos del Trabajo N° $casoGis, por el motivo que se indica a continuación:                            
                            $table
                            Si tiene alguna consulta, por favor, contáctenos al correo electrónico <EMAIL> o al teléfono 4102-7600 ext. 8129-8130-8140. ¡Será un gusto atenderle! 
                            
                            Nuestro propósito es fortalecer la prevención en salud y seguridad laboral del país, generando siempre bienestar.
                            </p>",
                "sender" => "MNK Aseguramiento"
            ];

            $attachments = [];

            $nameFrom = 'Rechazo de factura(s) por reintegro';
            $subject = "Devolución de factura(s) sin trámite";

            $emails = [$reintegrate_activity->affiliate->email];

            $this->sendEmail($emails, $subject, $text, $attachments, $client, $activity_id, $activityAction->id, $reintegrate_activity->service_id, $nameFrom);

            DB::commit();

            return response()->json([
                'status' => 'success',
                'message' => 'RECHAZO AUDITORIA MEDICA',
                'activity_id' => $activity_id], 200);

        } catch (\Exception $e) {
            DB::rollBack();
            throw new \Exception('Error al reportar RECHAZO AUDITORIA MEDICA : '.$e->getMessage());

        }



    }

    //ACCION SOLICITAR INFORMACION ADICIONAL
    public function requestAdditionalInformation($client, $activity_id) {

        DB::beginTransaction();
        try{

            $reintegrate_activity = Activity::where('service_id', Service::SERVICE_REINTEGRATE_MNK)->where('id', $activity_id)->firstOrFail();
            $fullName = mb_convert_case(mb_strtolower( $reintegrate_activity->reintegrate->affiliate->full_name ?? ''), MB_CASE_TITLE, "UTF-8");

            $issuerReintegrate = IssuerReintegrate::where('activity_id', $activity_id)
                ->with(['serviceDetails'])->get();

            $activityAction = ActionController::create($activity_id, ActionReintegrate::SOLICITAR_INFORMACION_ADICIONAL,  'SOLICITAR INFORMACIÓN ADICIONAL');

            $table = "<table style='border: 1px solid black; border-collapse: collapse;'>";
            $table .= "<tr>";
            $table .= "<th style='background-color: #8BC34A; color: black; border: 1px solid black; padding: 5px;' >N° de factura </th>";
            $table .= "<th style='background-color: #8BC34A; color: black; border: 1px solid black; padding: 5px;' >Código </th>";
            $table .= "<th style='background-color: #8BC34A; color: black; border: 1px solid black; padding: 5px;' >Motivo </th>";
            $table .= "</tr>";

            $total = 0;
            foreach ($issuerReintegrate as $row) {
                foreach($row->serviceDetails as $index => $rowDetails){
                    if ($rowDetails->state == 'request_info') {

                        $table .= "<tr>";
                        $table .= "<td style='border: 1px solid black; padding: 5px;' >$row->consecutive_number</td>";
                        $table .= "<td style='border: 1px solid black; padding: 5px;' >$rowDetails->code</td>";
                        $table .= "<td style='border: 1px solid black; padding: 5px;' >".$rowDetails->reason."</td>";
                        $table .= "</tr>";

                    }
                }

            }

            $table .= "</table>";

            $text = [
                "text" => "<p>
                            ¡Buen día, $fullName!
                            
                            Luego de la revisión efectuada, se determinó que para el pago de la factura se requiere ampliar los datos que se indican a continuación:
                            $table
                            Para consultas adicionales, puede comunicarse al correo electrónico <EMAIL> con copia <NAME_EMAIL> o al teléfono 4102-7600 ext. 8129-8130-8140.
                            </p> ",
                "sender" => "MNK Aseguramiento"
            ];

            $attachments = [];

            $nameFrom = 'Notificación Solicitud de información';
            $subject = "Notificación Solicitud de información";

            $emails = [$reintegrate_activity->reintegrate->affiliate->email];

            $this->sendEmail($emails, $subject, $text, $attachments, $client, $activity_id, $activityAction->id, $reintegrate_activity->service_id, $nameFrom);

            DB::commit();

            return response()->json([
                'status' => 'success',
                'message' => 'APROBACIÓN PARCIAL FACTURA REINTEGRO',
                'activity_id' => $activity_id], 200);

        } catch (\Exception $e) {
            DB::rollBack();
            throw new \Exception('Error al reportar APROBACIÓN PARCIAL FACTURA REINTEGRO : '.$e->getMessage());

        }


    }

    //ME-1290 APROBAR FACTURA REINTEGRO
    public function approveReimbursementInvoice($client, $activity_id){
        $activityAction = ActionController::create($activity_id, ActionReintegrate::APROBAR_FACTURA_REINTEGRO,  'APROBAR FACTURA REINTEGRO');
    }

    //ME-1291 APROBACION PARCIAL FACTURA REINTEGRO
    public function partialApprovalInvoiceRefund($client, $activity_id)
    {
        DB::beginTransaction();
        try{

            $reintegrate_activity = Activity::where('service_id', Service::SERVICE_REINTEGRATE_MNK)->where('id', $activity_id)->firstOrFail();
            $fullName = mb_convert_case(mb_strtolower( $reintegrate_activity->reintegrate->affiliate->full_name ?? ''), MB_CASE_TITLE, "UTF-8");
            $casoGis = $reintegrate_activity->parent->parent->policy_sort->formatNumberConsecutive();

            $fecha = Carbon::now()->format('d/m/Y');

            $issuerReintegrate = IssuerReintegrate::where('activity_id', $activity_id)
                ->with(['serviceDetails'])->get();

            $activityAction = ActionController::create($activity_id, ActionReintegrate::APROBACION_PARCIAL_FACTURA_REINTEGRO,  'APROBACIÓN PARCIAL FACTURA REINTEGRO');

            $table = "<table style='border: 1px solid black; border-collapse: collapse;'>";
            $table .= "<tr>";
            $table .= "<th style='background-color: #8BC34A; color: black; border: 1px solid black; padding: 5px;' >N° de factura </th>";
            $table .= "<th style='background-color: #8BC34A; color: black; border: 1px solid black; padding: 5px;' >Código </th>";
            $table .= "<th style='background-color: #8BC34A; color: black; border: 1px solid black; padding: 5px;' >Monto </th>";
            $table .= "</tr>";

            $tableRechazos = "<table style='border: 1px solid black; border-collapse: collapse;' >";
            $tableRechazos .= "<tr>";
            $tableRechazos .= "<th style='background-color: #8BC34A; color: black; border: 1px solid black; padding: 5px;' >N° de factura </th>";
            $tableRechazos .= "<th style='background-color: #8BC34A; color: black; border: 1px solid black; padding: 5px;' >Código </th>";
            $tableRechazos .= "<th style='background-color: #8BC34A; color: black; border: 1px solid black; padding: 5px;' >Motivo del no pago </th>";
            $tableRechazos .= "</tr>";

            $total = 0;
            foreach ($issuerReintegrate as $row) {
                foreach($row->serviceDetails as $index => $rowDetails){
                    if ($rowDetails->state == 'approve') {
                        $table .= "<tr>";
                        $table .= "<td style='border: 1px solid black; padding: 5px;' >$row->consecutive_number</td>";
                        $table .= "<td style='border: 1px solid black; padding: 5px;' >$rowDetails->code</td>";
                        $table .= "<td style='border: 1px solid black; padding: 5px;' >".number_format($rowDetails->total_line_amount ?? 0, 2, ',', '.')."</td>";
                        $table .= "</tr>";

                        $total += $rowDetails->total_line_amount ?? 0;

                    } else if ($rowDetails->state == 'reject'){
                        $tableRechazos .= "<tr>";
                        $tableRechazos .= "<td style='border: 1px solid black; padding: 5px;' >$row->consecutive_number</td>";
                        $tableRechazos .= "<td style='border: 1px solid black; padding: 5px;' >$rowDetails->code</td>";
                        $tableRechazos .= "<td style='border: 1px solid black; padding: 5px;' >$rowDetails->reason</td>";
                        $tableRechazos .= "</tr>";
                    }
                }

            }

            $totalApproved = ($reintegrate_activity->parent->parent->policy_sort->type_currency == 'USD' ? '$' : '₡').''.number_format($total ?? 0, 2, ',', '.');


            $table .= "<tr>";
            $table .= "<th style='background-color: #8BC34A; color: black; border: 1px solid black; padding: 5px;' >Total pagado </th>";
            $table .= "<th style='background-color: #8BC34A; color: black; border: 1px solid black; padding: 5px;' ></th>";
            $table .= "<th style='background-color: #8BC34A; color: black; border: 1px solid black; padding: 5px;' >".number_format($total, 2, ',', '.')."</th>";
            $table .= "</tr>";
            $table .= "</table>";

            $tableRechazos .= "</table>";

            $factRechazadas = " Por otra parte, luego de la revisión realizada, se determina que la(s) factura(s) que se detalla(n), lamentablemente no puede(n) ser pagada(s) por el motivo que se indica a continuación:
                                $tableRechazos
                                Si tiene alguna consulta, por favor, contáctenos al correo electrónico <EMAIL> o al teléfono 4102-7600 ext. 8129-8130-8140. ¡Será un gusto atenderle!
                                
                                Nuestro propósito es fortalecer la prevención en salud y seguridad laboral del país, generando siempre bienestar.                                
                                ";

            $text = [
                "text" => "<p>
                            ¡Buen día, $fullName!
                            
                            Le informamos que el $fecha se procedió con el pago de las facturas que se detallan, por un monto total de $totalApproved, correspondientes al reintegro de los servicios asociados al caso del Seguro Obligatorio de Riesgos del Trabajo N° $casoGis
                            $table
                            Esta suma fue depositada en la cuenta registrada para ese propósito.
                            
                            $factRechazadas
                            </p> ",
                "sender" => Senders::MNK_INDEMNITIES
            ];

            $attachments = [];

            $nameFrom = 'Pago de factura(s)';
            $subject = "Pago de factura(s)";

            $emailIntermediary = $reintegrate_activity->parent->parent->affiliate->email;
            $emailTaker = $reintegrate_activity->affiliate->email;

            $emails = array_filter([$emailIntermediary, $emailTaker], function ($email) {
                return !empty($email);
            });

            $this->sendEmail($emails, $subject, $text, $attachments, $client, $activity_id, $activityAction->id, $reintegrate_activity->service_id, $nameFrom);

            DB::commit();

            return response()->json([
                'status' => 'success',
                'message' => 'APROBACION PARCIAL FACTURA REINTEGRO',
                'activity_id' => $activity_id], 200);

        } catch (\Exception $e) {
            DB::rollBack();
            throw new \Exception('Error al reportar aprobacion parcial factura reintegro: '.$e->getMessage());

        }
    }

    //ME-1294 REPORTAR PAGO PARCIAL REALIZADO
    public function reportPartialPaymentMade($client, $activity_id)
    {
        DB::beginTransaction();
        try{

            $reintegrate_activity = Activity::where('service_id', Service::SERVICE_REINTEGRATE_MNK)->where('id', $activity_id)->firstOrFail();
            $fullName = mb_convert_case(mb_strtolower( $reintegrate_activity->affiliate->full_name ?? ''), MB_CASE_TITLE, "UTF-8");
            $casoGis = $reintegrate_activity->parent->parent->policy_sort->formatNumberConsecutive();
            $totalApproved = ($reintegrate_activity->parent->parent->policy_sort->type_currency == 'USD' ? '$' : '₡').''.number_format($reintegrate_activity->reintegrate->total_approved ?? 0, 2, ',', '.');

            $fecha = Carbon::now()->format('d/m/Y');

            $issuerReintegrate = IssuerReintegrate::where('activity_id', $activity_id)
                ->with(['serviceDetails'])->get();

            $activityAction = ActionController::create($activity_id, ActionReintegrate::REPORTAR_PAGO_PARCIAL_REALIZADO,  'REPORTAR PAGO PARCIAL REALIZADO');

            $table = "<table style='border: 1px solid black; border-collapse: collapse;' >";
            $table .= "<tr>";
            $table .= "<th style='background-color: #8BC34A; color: black; border: 1px solid black; padding: 5px;'>N° de factura </th>";
            $table .= "<th style='background-color: #8BC34A; color: black; border: 1px solid black; padding: 5px;'>Código </th>";
            $table .= "<th style='background-color: #8BC34A; color: black; border: 1px solid black; padding: 5px;'>Monto </th>";
            $table .= "</tr>";

            $tableRechazos ="<table style='border: 1px solid black; border-collapse: collapse;' >";
            $tableRechazos .="<tr>";
            $tableRechazos .="<th style='background-color: #8BC34A; color: black; border: 1px solid black; padding: 5px;'>N° de factura </th>";
            $tableRechazos .="<th style='background-color: #8BC34A; color: black; border: 1px solid black; padding: 5px;'>Código </th>";
            $tableRechazos .="<th style='background-color: #8BC34A; color: black; border: 1px solid black; padding: 5px;'>Motivo del no pago </th>";
            $tableRechazos .="</tr>";

            $total = 0;

            foreach ($issuerReintegrate as $row) {
                foreach($row->serviceDetails as $index => $rowDetails){
                    if ($rowDetails->state == 'approve') {
                        $table .= "<tr>";
                        $table .= "<td style='border: 1px solid black; padding: 5px;'>$row->consecutive_number</td>";
                        $table .= "<td style='border: 1px solid black; padding: 5px;'>$rowDetails->code</td>";
                        $table .= "<td style='border: 1px solid black; padding: 5px;'>".number_format($rowDetails->total_line_amount ?? 0, 2, ',', '.')."</td>";
                        $table .= "</tr>";

                        $total += $rowDetails->total_line_amount ?? 0;

                    } else if ($rowDetails->state == 'reject'){
                        $tableRechazos .= "<tr>";
                        $tableRechazos .= "<td style='border: 1px solid black; padding: 5px;'>$row->consecutive_number</td>";
                        $tableRechazos .= "<td style='border: 1px solid black; padding: 5px;'>$rowDetails->code</td>";
                        $tableRechazos .= "<td style='border: 1px solid black; padding: 5px;'>$rowDetails->reason</td>";
                        $tableRechazos .= "</tr>";
                    }
                }

            }

            $table .= "<tr>";
            $table .= "<th style='background-color: #8BC34A; color: black; border: 1px solid black; padding: 5px;'>Total pagado </th>";
            $table .= "<th style='background-color: #8BC34A; color: black; border: 1px solid black; padding: 5px;'></th>";
            $table .= "<th style='background-color: #8BC34A; color: black; border: 1px solid black; padding: 5px;'>".number_format($total, 2, ',', '.')."</th>";
            $table .= "</tr>";
            $table .= "</table>";

            $tableRechazos .= "</table> ";

            $factRechazadas = "Por otra parte, luego de la revisión realizada, se determina que la(s) factura(s) que se detalla(n), lamentablemente no puede(n) ser pagada(s) por el motivo que se indica:                               
                               $tableRechazos                               
                               Si tiene alguna consulta,  por favor, comuníquese con nosotros al 4102-7600 ext. 8129-8130-8140. ¡Será un gusto atenderle!
                                ";

            $text = [
                "text" => "<p>
                            ¡Buen día, $fullName!
                            
                            Un gusto saludarle. Le informamos que el $fecha se procedió con el pago de las facturas que se detallan, por un monto total de $totalApproved, correspondientes al reintegro de los servicios asociados al caso del Seguro Obligatorio de Riesgos del Trabajo N° $casoGis.
                            $table
                            Esta suma fue depositada en la cuenta registrada para ese propósito.
                            
                            $factRechazadas 
                            </p>",
                "sender" => "MNK Aseguramiento"
            ];

            $attachments = [];

            $nameFrom = 'Pago y rechazo de facturas';
            $subject = "Pago y rechazo de facturas";

            $emailIntermediary = $reintegrate_activity->parent->parent->affiliate->email;
            $emailTaker = $reintegrate_activity->affiliate->email;

            $emails = array_filter([$emailIntermediary, $emailTaker], function ($email) {
                return !empty($email);
            });

            $this->sendEmail($emails, $subject, $text, $attachments, $client, $activity_id, $activityAction->id, $reintegrate_activity->service_id, $nameFrom);

            DB::commit();

            return response()->json([
                'status' => 'success',
                'message' => 'REPORTAR PAGO PARCIAL REALIZADO',
                'activity_id' => $activity_id], 200);

        } catch (\Exception $e) {
            DB::rollBack();
            throw new \Exception('Error al reportar pago parcial realizado: '.$e->getMessage());

        }
    }

    //ME-1293 REPORTAR PAGO REALIZADO
    public function reportPaymentMade($client, $activity_id)
    {
        DB::beginTransaction();
        try{

            $reintegrate_activity = Activity::where('service_id', Service::SERVICE_REINTEGRATE_MNK)->where('id', $activity_id)->firstOrFail();
            $fullName = mb_convert_case(mb_strtolower( $reintegrate_activity->affiliate->full_name ?? ''), MB_CASE_TITLE, "UTF-8");
            $casoGis = $reintegrate_activity->parent->parent->policy_sort->formatNumberConsecutive();
            $totalApproved = ($reintegrate_activity->parent->parent->policy_sort->type_currency == 'USD' ? '$' : '₡').''.number_format($reintegrate_activity->reintegrate->total_approved ?? 0, 2, ',', '.');

            $fecha = Carbon::now()->format('d/m/Y');

            $issuerReintegrate = IssuerReintegrate::where('activity_id', $activity_id)
                ->with(['serviceDetails'])->get();

            $activityAction = ActionController::create($activity_id, ActionReintegrate::REPORTAR_PAGO_REALIZADO,  'REPORTAR PAGO REALIZADO');

            $table = "<table style='border: 1px solid black; border-collapse: collapse;' >";
            $table .= "<tr>";
            $table .= "<th style='background-color: #8BC34A; color: black; border: 1px solid black; padding: 5px;' >N° de factura</th>";
            $table .= "<th style='background-color: #8BC34A; color: black; border: 1px solid black; padding: 5px;' >Código</th>";
            $table .= "<th style='background-color: #8BC34A; color: black; border: 1px solid black; padding: 5px;' >Monto</th>";
            $table .= "</tr>";

            $total = 0;

            foreach ($issuerReintegrate as $row) {
                foreach($row->serviceDetails as $index => $rowDetails){
                    if ($rowDetails->state == 'approve') {
                        $table .= "<tr>";
                        $table .= "<td style='border: 1px solid black; padding: 5px;' >$row->consecutive_number</td>";
                        $table .= "<td style='border: 1px solid black; padding: 5px;' >$rowDetails->code</td>";
                        $table .= "<td style='border: 1px solid black; padding: 5px;' >".number_format($rowDetails->total_line_amount ?? 0, 2, ',', '.')."</td>";
                        $table .= "</tr>";

                        $total += $rowDetails->total_line_amount ?? 0;
                    }
                }

            }

            $table .= "<tr>";
            $table .= "<th style='background-color: #8BC34A; color: black; border: 1px solid black; padding: 5px;' >Total pagado </th>";
            $table .= "<th style='background-color: #8BC34A; color: black; border: 1px solid black; padding: 5px;' ></th>";
            $table .= "<th style='background-color: #8BC34A; color: black; border: 1px solid black; padding: 5px;' >".number_format($total, 2, ',', '.')."</th>";
            $table .= "</tr>";
            $table .= "</table>";


            $text = [
                "text" => "<p>
                            ¡Buen día, $fullName
                            
                            Un gusto saludarle. Le informamos que el $fecha se procedió con el pago de las facturas que se detallan, por un monto total de $totalApproved, correspondientes al reintegro de los servicios asociados al caso del Seguro Obligatorio de Riesgos del Trabajo N° $casoGis.
                            $table
                            Esta suma fue depositada en la cuenta registrada para ese propósito.
                            </p>",
                "sender" => "MNK Aseguramiento"
            ];

            $attachments = [];
            $nameFrom = 'Recepción de factura(s)';
            $subject = "Recepción de factura(s)";

            $emailIntermediary = $reintegrate_activity->parent->parent->affiliate->email;
            $emailTaker = $reintegrate_activity->affiliate->email;

            $emails = array_filter([$emailIntermediary, $emailTaker], function ($email) {
                return !empty($email);
            });

            $this->sendEmail($emails, $subject, $text, $attachments, $client, $activity_id, $activityAction->id, $reintegrate_activity->service_id, $nameFrom);

            DB::commit();

            return response()->json([
                'status' => 'success',
                'message' => 'REPORTAR PAGO REALIZADO',
                'activity_id' => $activity_id], 200);

        } catch (\Exception $e) {
            DB::rollBack();
            throw new \Exception('Error al reportar pago realizado: '.$e->getMessage());

        }
    }

    //ME-1292 RECHAZAR FACTURA REINTEGRO - solo se rechaza cuando todos los items fueron rechazados
    public function rejectReimbursementInvoice($client, $activity_id) {

        DB::beginTransaction();
        try{

            $reintegrate_activity = Activity::where('service_id', Service::SERVICE_REINTEGRATE_MNK)->where('id', $activity_id)->firstOrFail();
            $fullName = mb_convert_case(mb_strtolower( $reintegrate_activity->affiliate->full_name ?? ''), MB_CASE_TITLE, "UTF-8");
            $casoGis = $reintegrate_activity->parent->parent->policy_sort->formatNumberConsecutive();

            $issuerReintegrate = IssuerReintegrate::where('activity_id', $activity_id)
                ->with(['serviceDetails'])->get();

            $activityAction = ActionController::create($activity_id, ActionReintegrate::RECHAZAR_FACTURA_REINTEGRO,  'RECHAZAR FACTURA REINTEGRO');

            $table  = "<table style='border: 1px solid black; border-collapse: collapse;'>";
            $table .= "<tr>";
            $table .= "<th style='background-color: #8BC34A; color: black; border: 1px solid black; padding: 5px;' >N° de factura </th>";
            $table .= "<th style='background-color: #8BC34A; color: black; border: 1px solid black; padding: 5px;' >Código </th>";
            $table .= "<th style='background-color: #8BC34A; color: black; border: 1px solid black; padding: 5px;' >Motivo del no pago </th>";
            $table .= "</tr>";

            foreach ($issuerReintegrate as $row) {
                foreach($row->serviceDetails as $index => $rowDetails){
                    $table .= "<tr>";
                    $table .= "<td style='border: 1px solid black; padding: 5px;' >$row->consecutive_number</td>";
                    $table .= "<td style='border: 1px solid black; padding: 5px;' >$rowDetails->code</td>";
                    $table .= "<td style='border: 1px solid black; padding: 5px;' >$rowDetails->reason</td>";
                    $table .= "</tr>";
                }

            }

            $table .= "</table> ";


            $text = [
                "text" => "<p>
                            ¡Buen día, $fullName!
                            
                            Luego de la revisión efectuada, se determinó que lamentablemente la (s) factura(s) que se detalla(n), no puede(n) ser pagada(s) al amparo del caso del Seguro Obligatorio de Riesgos del Trabajo N° $casoGis, por el motivo que se indica a continuación:
                            $table
                            Si tiene alguna consulta, por favor, contáctenos al correo electrónico <EMAIL> o al teléfono 4102-7600 ext. 8129-8130-8140. ¡Será un gusto atenderle!
                            
                            Nuestro propósito es fortalecer la prevención en salud y seguridad laboral del país, generando siempre bienestar.
                            </p>",
                "sender" => "MNK Aseguramiento"
            ];

            $attachments = [];

            $nameFrom = 'Rechazo de factura(s) por reintegro';
            $subject = "Devolución de factura(s) sin trámite";

            $emailIntermediary = $reintegrate_activity->parent->parent->affiliate->email;
            $emailTaker = $reintegrate_activity->affiliate->email;

            $emails = array_filter([$emailIntermediary, $emailTaker], function ($email) {
                return !empty($email);
            });

            $this->sendEmail($emails, $subject, $text, $attachments, $client, $activity_id, $activityAction->id, $reintegrate_activity->service_id, $nameFrom);

            DB::commit();

            return response()->json([
                'status' => 'success',
                'message' => 'RECHAZAR FACTURA REINTEGRO',
                'activity_id' => $activity_id], 200);

        } catch (\Exception $e) {
            DB::rollBack();
            throw new \Exception('Error al rechazar factura reintegro: '.$e->getMessage());
        }
    }

    public function generateServiceReintegration(Request $request, $cpath, $affiliate_id){

        DB::beginTransaction();
        try{

            if (!$request->economicBenefitId) {
                throw new \Exception('error', 'Registro no encontrado');
            }

            $economicBenefit = EconomicBenefit::where('id', $request->economicBenefitId)->where('recognition_type','refund')->firstOrFail();

            $client = Client::where('path', $cpath)->firstOrFail();
            $activity = Activity::where('client_id', $client->id)->where('id', $economicBenefit->activity_id)->firstOrFail(); //activity de gis

            $gisSort = $activity->gis_sort;
            $gisSort->worker_iban_account_number = $economicBenefit->iban_account;
            $gisSort->save();

            $activityNew = new Activity();
            $activityNew->parent_id = $activity->id;
            $activityNew->client_id = $activity->client_id;
            $activityNew->affiliate_id = $activity->affiliate_id;
            $activityNew->service_id = Service::SERVICE_REINTEGRATE_MNK;
            $activityNew->state_id = State::REGISTRADO;
            $activityNew->user_id = $activity->user_id;
            $activityNew->save();

            $ReintegrateNew = new Reintegrate();
            $ReintegrateNew->activity_id = $activityNew->id;
            $ReintegrateNew->worker_name = $activity->affiliate->full_name;
            $ReintegrateNew->document_type = $activity->affiliate->doc_type;
            $ReintegrateNew->document_number = $activity->affiliate->doc_number;
            $ReintegrateNew->policy_sort = $activity->parent->policy_sort->id;
            $ReintegrateNew->authorization = $economicBenefit->authorization;
            $ReintegrateNew->request_description = $economicBenefit->request_description;
            $ReintegrateNew->iban_account = $economicBenefit->iban_account;
            $ReintegrateNew->affiliate_id = $affiliate_id;
            $ReintegrateNew->save();

            $categorias = [
                'solicitudReintegro' => 285,
                'facturaGastos' => 286,
                'facturaXML' => 288,
                'respuestaXML' => 287
            ];

            foreach ($categorias as $categoria => $documentId) {

                if ($request->hasFile($categoria)) {
                    $archivos = $request->file($categoria);

                    // Manejar caso de un solo archivo (no array)
                    if (!is_array($archivos)) {
                        $archivos = [$archivos]; // Convertirlo en array para iterarlo igual
                    }

                    foreach ($archivos as $archivo) {

                        //validar que la estructura del xml  facturaXML
                        if ($categoria === 'facturaXML' ) {

                            $errorValidacion = $this->validarEstructuraXML($archivo, $activityNew->id);

                            if ($errorValidacion !== true) {
                                DB::rollBack();

                                return response()->json([
                                    'status' => 'error',
                                    'message' => "Error en el archivo XML de factura electrónica ".$archivo->getClientOriginalName().": $errorValidacion"
                                ], 400);
                            }

                        }

                        //carga doc a s3
                        $this->storeActivityDocument($archivo, $documentId, $activityNew->id);

                    }
                }
            }

            $activityAction = ActionController::create($activityNew->id, ActionReintegrate::CARGUE_FACTURA_REINTEGRO,  'CARGUE FACTURA REINTEGRO');

            $economicBenefit->state = 'approved';
            $economicBenefit->save();

            DB::commit();

            $this->sendEmailRegistro($activityNew->id, $client, $activityAction->id);

            return response()->json([
                'status' => 'success',
                'message' => 'Su solicitud de reintegro ha sido creada exitosamente. # trámite: '. $ReintegrateNew->id,
                'id' => $activityNew->id], 200);

        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
            ],500);

        }

    }

    function sendEmailRegistro($activity_id, $client, $activity_action_id)
    {

        try{

            $data = DB::table('service_detail_reintegrates as s')
                ->leftJoin('issuer_reintegrates as sr', 'sr.id', '=', 's.issuer_id')
                ->where('sr.activity_id', $activity_id)
                ->selectRaw('COUNT(DISTINCT sr.id) as total_fact, SUM(s.total_line_amount) as total')
                ->first();

            $totalFact = $data->total_fact ?? 0;
            $totalAmount = $data->total ?? 0;

            $reintegrate_activity = Activity::where('service_id', Service::SERVICE_REINTEGRATE_MNK)->where('id', $activity_id)->firstOrFail();
            $fullName = mb_convert_case(mb_strtolower( $reintegrate_activity->reintegrate->affiliate->full_name ?? ''), MB_CASE_TITLE, "UTF-8");
            $casoGis = $reintegrate_activity->parent->parent->policy_sort->formatNumberConsecutive();
            $totalAmount = ($reintegrate_activity->parent->parent->policy_sort->type_currency == 'USD' ? '$' : '₡').''.number_format($totalAmount ?? 0, 2, ',', '.');

            $totalFact = $totalFact == 1 ? " " : " $totalFact ";

            $text = [
                "text" => "<p>
                            ¡Buen día, $fullName!
                            
                            Un gusto saludarle. Confirmamos la recepción de la(s)".$totalFact."factura(s) correspondientes al reintegro de los servicios asociados al caso del Seguro Obligatorio de Riesgos del Trabajo N° $casoGis, por un monto total de $totalAmount
                            
                            Procedemos con la revisión correspondiente y, en un plazo máximo de 8 días hábiles, le estaremos informando sobre el resultado y el proceso de pago respectivo.
                            
                            En caso de requerir alguna información o aclaración adicional, le estaremos contactando por este medio. Si tiene alguna consulta, por favor, contáctenos al correo electrónico <EMAIL> o al teléfono 4102-7600 ext. 8129-8130-8140. ¡Será un gusto atenderle!
                            
                            Nuestro propósito es fortalecer la prevención en salud y seguridad laboral del país, generando siempre bienestar.
                            <p> ",
                "sender" => "MNK Aseguramiento"
            ];

            $attachments = [];

            $nameFrom = 'Recepción de factura(s)';
            $subject = "Recepción de factura(s)";

            $emailIntermediary = $reintegrate_activity->parent->parent->affiliate->email;
            $emailTaker = $reintegrate_activity->affiliate->email;

            $emails = array_filter([$emailIntermediary, $emailTaker], function ($email) {
                return !empty($email);
            });

            $this->sendEmail($emails, $subject, $text, $attachments, $client, $activity_id, $activity_action_id, $reintegrate_activity->service_id, $nameFrom);

            return response()->json([
                'status' => 'success',
                'message' => 'Correo de recepcion enviado',
                'activity_id' => $activity_id], 200);

        } catch (\Exception $e) {

            throw new \Exception('Error al reportar aprobacion parcial factura reintegro: '.$e->getMessage());

        }
    }

    function validarEstructuraXML_xxxx($archivo)
    {
        try {
            $xml = simplexml_load_file($archivo->path());

            // Validar la presencia de las etiquetas principales
            if (!isset($xml->Clave) ||
                !isset($xml->CodigoActividad) ||
                !isset($xml->NumeroConsecutivo) ||
                !isset($xml->FechaEmision) ||
                !isset($xml->Emisor) ||
                !isset($xml->Receptor) ||
                !isset($xml->DetalleServicio) ||
                !isset($xml->ResumenFactura)) {
                return false;
            }

            // Validar estructura del Emisor
            if (!isset($xml->Emisor->Nombre) ||
                !isset($xml->Emisor->Identificacion) ||
                !isset($xml->Emisor->Identificacion->Tipo) ||
                !isset($xml->Emisor->Identificacion->Numero) ||
                !isset($xml->Emisor->NombreComercial) ||
                !isset($xml->Emisor->Ubicacion) ||
                !isset($xml->Emisor->Ubicacion->Provincia) ||
                !isset($xml->Emisor->Ubicacion->Canton) ||
                !isset($xml->Emisor->Ubicacion->Distrito) ||
                !isset($xml->Emisor->Ubicacion->Barrio) ||
                !isset($xml->Emisor->Ubicacion->OtrasSenas) ||
                !isset($xml->Emisor->Telefono) ||
                !isset($xml->Emisor->Telefono->CodigoPais) ||
                !isset($xml->Emisor->Telefono->NumTelefono) ||
                !isset($xml->Emisor->CorreoElectronico)) {
                return false;
            }

            // Validar estructura del Receptor
            if (!isset($xml->Receptor->Nombre) ||
                !isset($xml->Receptor->Identificacion) ||
                !isset($xml->Receptor->Identificacion->Tipo) ||
                !isset($xml->Receptor->Identificacion->Numero) ||
                !isset($xml->Receptor->Telefono) ||
                !isset($xml->Receptor->Telefono->CodigoPais) ||
                !isset($xml->Receptor->Telefono->NumTelefono) ||
                !isset($xml->Receptor->CorreoElectronico)) {
                return false;
            }

            // Validar estructura de OtrosCargos (puede haber más de uno)
            if (isset($xml->OtrosCargos)) {
                foreach ($xml->OtrosCargos as $otrosCargos) {
                    if (!isset($otrosCargos->TipoDocumento) ||
                        !isset($otrosCargos->NombreTercero) ||
                        !isset($otrosCargos->Detalle) ||
                        !isset($otrosCargos->Porcentaje) ||
                        !isset($otrosCargos->MontoCargo)) {
                        return false;
                    }
                }
            }

            // Validar estructura de DetalleServicio y sus LineaDetalle
            if (!isset($xml->DetalleServicio->LineaDetalle)) {
                return false;
            }

            foreach ($xml->DetalleServicio->LineaDetalle as $linea) {
                if (!isset($linea->NumeroLinea) ||
                    !isset($linea->Codigo) ||
                    !isset($linea->CodigoComercial) ||
                    !isset($linea->CodigoComercial->Tipo) ||
                    !isset($linea->CodigoComercial->Codigo) ||
                    !isset($linea->Cantidad) ||
                    !isset($linea->UnidadMedida) ||
                    !isset($linea->UnidadMedidaComercial) ||
                    !isset($linea->Detalle) ||
                    !isset($linea->PrecioUnitario) ||
                    !isset($linea->MontoTotal) ||
                    !isset($linea->SubTotal) ||
                    !isset($linea->Impuesto) ||
                    !isset($linea->Impuesto->Codigo) ||
                    !isset($linea->Impuesto->CodigoTarifa) ||
                    !isset($linea->Impuesto->Tarifa) ||
                    !isset($linea->Impuesto->Monto) ||
                    !isset($linea->ImpuestoNeto) ||
                    !isset($linea->MontoTotalLinea)) {
                    return false;
                }
            }

            return true; // Si pasa todas las validaciones, el XML es válido
        } catch (\Exception $e) {
            return false; // Si hay error al cargar el XML, no es válido
        }
    }

    function validarEstructuraXML($archivo, $activity_id)
    {
        try {
            $xml = simplexml_load_file($archivo->path());

            // Validar etiquetas principales
            $etiquetasPrincipales = [
                'Clave', 'CodigoActividad', 'NumeroConsecutivo', 'FechaEmision',
                'Emisor', 'Receptor', 'DetalleServicio', 'ResumenFactura'
            ];

            foreach ($etiquetasPrincipales as $etiqueta) {
                if (!isset($xml->$etiqueta)) {
                    return "Falta la etiqueta principal: $etiqueta";
                }
            }

            // Validar estructura del Emisor
            $etiquetasEmisor = [
                'Nombre', 'Identificacion', 'NombreComercial', 'Ubicacion',
                'Telefono', 'CorreoElectronico'
            ];
            foreach ($etiquetasEmisor as $etiqueta) {
                if (!isset($xml->Emisor->$etiqueta)) {
                    return "Falta la etiqueta en Emisor: $etiqueta";
                }
            }

            // Validar Identificación del Emisor
            if (!isset($xml->Emisor->Identificacion->Tipo) || !isset($xml->Emisor->Identificacion->Numero)) {
                return "Falta la información de identificación en Emisor.";
            }


            $issuerReintegrateNew = new IssuerReintegrate();
            $issuerReintegrateNew->activity_id = $activity_id; // actividad de reintegros
            $issuerReintegrateNew->name = $xml->Emisor->Nombre;
            $issuerReintegrateNew->commercial_name = $xml->Emisor->NombreComercial;
            $issuerReintegrateNew->id_type = $xml->Emisor->Identificacion->Tipo;
            $issuerReintegrateNew->id_number = $xml->Emisor->Identificacion->Numero;
            $issuerReintegrateNew->province = isset($xml->Emisor->Ubicacion->Provincia) ? $xml->Emisor->Ubicacion->Provincia : '';
            $issuerReintegrateNew->canton = isset($xml->Emisor->Ubicacion->Canton) ? $xml->Emisor->Ubicacion->Canton : '';
            $issuerReintegrateNew->district = isset($xml->Emisor->Ubicacion->Distrito) ? $xml->Emisor->Ubicacion->Distrito : '';
            $issuerReintegrateNew->neighborhood = isset($xml->Emisor->Ubicacion->Barrio) ? $xml->Emisor->Ubicacion->Barrio : '';
            $issuerReintegrateNew->address = isset($xml->Emisor->Ubicacion->OtrasSenas) ? $xml->Emisor->Ubicacion->OtrasSenas : '';
            $issuerReintegrateNew->country_code = isset($xml->Emisor->Telefono->CodigoPais) ? $xml->Emisor->Telefono->CodigoPais : '';
            $issuerReintegrateNew->phone_number = isset($xml->Emisor->Telefono->NumTelefono) ? $xml->Emisor->Telefono->NumTelefono : '';
            $issuerReintegrateNew->email = $xml->Emisor->CorreoElectronico;
            $issuerReintegrateNew->invoice_key = $xml->Clave;
            $issuerReintegrateNew->activity_code = $xml->CodigoActividad;
            $issuerReintegrateNew->consecutive_number = $xml->NumeroConsecutivo;

            if (isset($xml->FechaEmision)) {
                $issueDate = Carbon::parse($xml->FechaEmision)->format('Y-m-d H:i:s');
                $issuerReintegrateNew->issue_date = $issueDate;
            }

            $issuerReintegrateNew->save();

            // Validar estructura del Receptor
            $etiquetasReceptor = ['Nombre', 'Identificacion', 'Telefono', 'CorreoElectronico'];
            foreach ($etiquetasReceptor as $etiqueta) {
                if (!isset($xml->Receptor->$etiqueta)) {
                    return "Falta la etiqueta en Receptor: $etiqueta";
                }
            }

            if (!isset($xml->Receptor->Identificacion->Tipo) || !isset($xml->Receptor->Identificacion->Numero)) {
                return "Falta la información de identificación en Receptor.";
            }

            $dataReceptor = [
                'issuer_id' => $issuerReintegrateNew->id,
                'name' => $xml->Receptor->Nombre,
                'id_type' => $xml->Receptor->Identificacion->Tipo,
                'id_number' => $xml->Receptor->Identificacion->Numero,
                'country_code' => isset($xml->Receptor->Telefono->CodigoPais) ? $xml->Receptor->Telefono->CodigoPais : '' ,
                'phone_number' => isset($xml->Receptor->Telefono->NumTelefono) ? $xml->Receptor->Telefono->NumTelefono : '' ,
                'email' => $xml->Receptor->CorreoElectronico,
            ];

            ReceiverReintegrate::insert($dataReceptor);

            // Validar estructura de OtrosCargos (si existe)
            if (isset($xml->OtrosCargos)) {
                foreach ($xml->OtrosCargos as $otrosCargos) {
                    $etiquetasOtrosCargos = ['TipoDocumento', 'NombreTercero', 'Detalle', 'Porcentaje', 'MontoCargo'];
                    foreach ($etiquetasOtrosCargos as $etiqueta) {
                        if (!isset($otrosCargos->$etiqueta)) {
                            return "Falta la etiqueta en OtrosCargos: $etiqueta";
                        }
                    }

                    $dataOtrosCargos = [
                        'issuer_id' => $issuerReintegrateNew->id,
                        'document_type' => $otrosCargos->TipoDocumento,
                        'third_party_name' => $otrosCargos->NombreTercero,
                        'description' => $otrosCargos->Detalle,
                        'percentage' => $otrosCargos->Porcentaje,
                        'charge_amount' => $otrosCargos->MontoCargo,

                    ];

                    OtherChargesReintegrate::insert($dataOtrosCargos);

                }
            }

            // Validar estructura de DetalleServicio y sus LineaDetalle
            if (!isset($xml->DetalleServicio->LineaDetalle)) {
                return "No se encontraron líneas de detalle en DetalleServicio.";
            }

            foreach ($xml->DetalleServicio->LineaDetalle as $linea) {
                $etiquetasLineaDetalle = [
                    'NumeroLinea', 'Codigo', 'CodigoComercial', 'Cantidad',
                    'UnidadMedida', 'UnidadMedidaComercial', 'Detalle',
                    'PrecioUnitario', 'MontoTotal', 'SubTotal', 'Impuesto',
                    'ImpuestoNeto', 'MontoTotalLinea'
                ];

                foreach ($etiquetasLineaDetalle as $etiqueta) {
                    if (!isset($linea->$etiqueta)) {
                        return "Falta la etiqueta en LineaDetalle: $etiqueta (Línea: {$linea->NumeroLinea})";
                    }
                }

                if (!isset($linea->Impuesto->Codigo) ||
                    !isset($linea->Impuesto->CodigoTarifa) ||
                    !isset($linea->Impuesto->Tarifa) ||
                    !isset($linea->Impuesto->Monto)) {
                    return "Falta información del Impuesto en LineaDetalle (Línea: {$linea->NumeroLinea})";
                }


                $dataDetalleServicio = [
                    'issuer_id' => $issuerReintegrateNew->id,
                    'invoice_id' => $linea->Codigo,
                    'line_number' => $linea->NumeroLinea,
                    'code' => $linea->Codigo,
                    'commercial_code_type' => $linea->CodigoComercial->Tipo,
                    'commercial_code' => $linea->CodigoComercial->Codigo,
                    'quantity' => $linea->Cantidad,
                    'unit_measure' => $linea->UnidadMedida,
                    'commercial_unit' => $linea->UnidadMedidaComercial,
                    'description' => $linea->Detalle,
                    'unit_price' => $linea->PrecioUnitario,
                    'total_amount' => $linea->MontoTotal,
                    'subtotal' => $linea->SubTotal,
                    'tax_code' => $linea->Impuesto->Codigo,
                    'tax_code_rate' => $linea->Impuesto->CodigoTarifa,
                    'tax_rate' => $linea->Impuesto->Tarifa,
                    'tax_amount' => $linea->Impuesto->Monto,
                    'net_tax' => $linea->ImpuestoNeto,
                    'total_line_amount' => $linea->MontoTotalLinea,
                ];

                ServiceDetailReintegrate::insert($dataDetalleServicio);

            }


            return true;

        } catch (\Exception $e) {
            return "Error procesando el XML: " . $e->getMessage();
        }
    }

    protected function storeActivityDocument($file, $documentId, $id)
    {
        $originalName = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME); // Nombre sin extensión
        $safeName = Str::slug($originalName); // Reemplazar espacios por guiones y limpiar caracteres especiales

        $originalExtension = $file->getClientOriginalExtension();
        //$uniqueName = Str::random(10) . uniqid() . '.' . $originalExtension;

        $uniqueName = $safeName . '_' . uniqid() . '.' . $originalExtension;  // Evitar colisión de nombres agregando un timestamp único


        $filePath = "documents_reintegrates/{$uniqueName}";

        $file->storeAs('documents_reintegrates', $uniqueName, 's3');

        $activityDocument = new ActivityDocument();
        $activityDocument->document_id = $documentId;
        $activityDocument->activity_id = $id;
        $activityDocument->path = $filePath;
        $activityDocument->uploaded_at = Carbon::now();
        $activityDocument->save();
    }


    private function sendEmail($emails, $subject, $text, $attachments, $client, $id, $activityAction, $service_id, $nameFrom)
    {
        $mailSent = new SendDocumentDataBase(
            implode(',', $emails),         // Correos a enviar
            $subject,                      // Asunto del correo
            "<EMAIL>",            // Remitente
            $nameFrom,                      // Asunto
            $text,                         // Cuerpo del email
            "<EMAIL>",  // Email de respuesta
            $attachments,                  // Archivos adjuntos
            "send_document_db",            // Tipo de envío
            $client,                       // Información del cliente
            request()->getHost(),          // Dominio
            $id,                           // ID de la actividad
            $activityAction,           // ID de la acción de la actividad
            $service_id // ID del servicio
        );

        $reintegrate_activity = Activity::where('service_id', Service::SERVICE_REINTEGRATE_MNK)
            ->where('id', $id)
            ->firstOrFail();

        // Si el afiliado pertenece a una actividad de poliza entonces es el tomador
        $affiliate = Activity::where('affiliate_id', $reintegrate_activity->reintegrate->affiliate_id)
            ->where('service_id', 75)
            ->first();

        // Capturar el resultado del envío
        $result = $mailSent->sendMail();

        //Registramos los datos del correo enviado para la trazabilidad
        $mailBoardController = new MailBoardController();
        $mailBoardController->createRegisterMail(
            $id,
            $service_id, 
            $reintegrate_activity->parent->parent->policy_sort->consecutive, 
            $affiliate ? 'Tomador' : 'Asegurado', 
            mb_convert_case(mb_strtolower($reintegrate_activity->reintegrate->affiliate->full_name ?? ''), MB_CASE_TITLE, "UTF-8"), 
            $reintegrate_activity->reintegrate->affiliate->doc_number, 
            $subject, 
            $text,
            $emails, 
            $result,
            $attachments
        );
    }

    public function descargarXmlEjemplo()
    {
        // URL del archivo XML en S3
        $url = 'https://mnk-prod.s3.us-east-1.amazonaws.com/public/EjemplXmlFacturaServReintegros.xml';

        // Obtener el contenido del archivo
        $contenidoArchivo = file_get_contents($url);

        // Establecer las cabeceras apropiadas para la descarga
        $cabeceras = [
            'Content-Type' => 'application/xml',
            'Content-Disposition' => 'attachment; filename="ejemplo-factura-xml.xml"',
        ];

        // Devolver el archivo como una descarga
        return response($contenidoArchivo, 200, $cabeceras);
    }



}