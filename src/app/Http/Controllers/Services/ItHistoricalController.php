<?php

namespace App\Http\Controllers\Services;

use App\Activity;
use App\Client;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class ItHistoricalController extends Controller
{

    public function form(Request $req, $cpath, $id)
    {
        $client = Client::query()
            ->where('path', $cpath)
            ->firstOrFail();
        $activity = Activity::query()
            ->where('client_id', $client->id)
            ->where('id', $id)
            ->firstOrFail();
        $itRadicationIt = null;

        if ($activity->determination_it) {
            $itRadicationIt = $activity->it_historical->inabilities;
        }
        $it_historical = $activity->it_historical;
        return view('services.it_historical.form', [
            'activity' => $activity,
            'it_historical' => $it_historical,
            'client' => $client->path,
            'itRadicationIt' => $itRadicationIt,
        ]);
    }
}
