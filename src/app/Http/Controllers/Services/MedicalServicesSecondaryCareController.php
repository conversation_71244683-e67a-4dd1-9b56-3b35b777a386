<?php

namespace App\Http\Controllers\Services;

use App\Action;
use App\Actions\ActionMedicalServiceSecondarySort;
use App\Actions\ActionMedicalServiceSort;
use App\Actions\ActionMedicationServiceSort;
use App\Actions\ActionPeItSort;
use App\Activity;
use App\ActivityAction;
use App\ActivityActionDocument;
use App\ActivityDocument;
use App\Affiliate;
use App\Area;
use App\Client;
use App\GisSort;
use App\Holiday;
use App\Http\Controllers\ActionController;
use App\Http\Controllers\Controller;
use App\Http\Controllers\RuleActionsController;
use App\Http\Controllers\Tables\MailBoardController;
use App\Mail\SendDocumentDataBase;
use App\MailTemplates\Constants\Templates;
use App\MailTemplates\TemplateBuilder;
use App\MedicalServiceCompanion;
use App\MedicalServiceControlledMedication;
use App\MedicalServiceDiagnostics;
use App\MedicalServiceFollowUp;
use App\MedicalServiceImageDiagnostics;
use App\MedicalServiceMedicalPrescription;
use App\MedicalServiceReferralSpecialits;
use App\MedicalServiceSecondaryCareControlledMedication;
use App\MedicalServiceSecondaryCareReferralSpecialits;
use App\MedicalServiceSecondaryCompanion;
use App\MedicalServiceSecondaryDiagnostics;
use App\MedicalServiceSecondaryFollowUp;
use App\MedicalServiceSecondaryImageDiagnostics;
use App\MedicalServiceSecondaryMedicalPrescription;
use App\MedicalServicesSecondaryCareSort;
use App\MedicalServicesSort;
use App\PeitInabilitySort;
use App\PeItSort;
use App\PeItSortCaseDx;
use App\PolicySort;
use App\PolicySpreadsheetAffiliate;
use App\Provider;
use App\Providers\AppServiceProvider;
use App\Service;
use App\State;
use App\States\StateMedicalServiceSecondarySort;
use App\States\StateMedicalServiceSort;
use App\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use PDF;
use PhpParser\Node\Stmt\DeclareDeclare;

class MedicalServicesSecondaryCareController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function capitalizeWordsWithExceptions($str) {
        // Lista de palabras a no capitalizar
        $exceptions = ['de', 'y', 'la', 'el', 'los', 'las', 'un', 'una', 'por', 'para', 'en', 'con'];

        // Divide el string en palabras
        $words = explode(' ', $str);

        // Recorrer cada palabra y capitalizar solo si no es una excepción
        foreach ($words as $index => $word) {
            // Si es la primera palabra o no está en las excepciones, capitalízala
            if ($index == 0 || !in_array(strtolower($word), $exceptions)) {
                $words[$index] = ucfirst(strtolower($word));
            } else {
                // Si está en las excepciones, déjala en minúsculas
                $words[$index] = strtolower($word);
            }
        }

        // Vuelve a unir las palabras en una cadena
        return implode(' ', $words);
    }

    //prueba pdf
    public function medicalDisability_pdf(Request $req, $cpath, $id)
    {
        $client   = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();

        if ($client->id == '2') {
            $logoPath = public_path('images/oceanica.png');
            $footerPath = public_path('images/oceanica_footer.png');
        } else {
            $logoPath = public_path('images/mnk.png');
            $footerPath = public_path('images/mnk_footer.png');
        }
        $fecha_desde = $activity->medical_services_sort->start_date_of_incapacity;
        $dias = $activity->medical_services_sort->days_of_incapacity;
        $fecha_hasta = Carbon::parse($fecha_desde)->addDays($dias)->startOfDay()->format('Y-m-d');
        $holiday = Holiday::where('holiday', $fecha_hasta)->first();
        $fecha_regreso = $holiday ? Carbon::parse($fecha_hasta)->addDay()->format('Y-m-d') : $fecha_hasta;

        $pdf = Pdf::loadView('services.medical_services_secondary_care.docs.medical_disability', [
            'logoPath' => $logoPath,
            'footerPath' => $footerPath,
            'activity' => $activity,
            'fecha_hasta' => $fecha_hasta,
            'fecha_regreso' => $fecha_regreso,
        ]);

        return $pdf->stream('preview.pdf');
    }

    public function medicalDisabilityLetter_pdf(Request $req, $cpath, $id)
    {
        $client   = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();

        if ($client->id == '2') {
            $logoPath = public_path('images/oceanica.png');
            $footerPath = public_path('images/oceanica_footer.png');
        } else {
            $logoPath = public_path('images/mnk.png');
            $footerPath = public_path('images/mnk_footer.png');
        }
        //OBTENER EL ULTIMO FOLLOWUP DE LA PM
        $lastFollowUp = $activity->medical_services_secondary_care_sort->followUps()->latest()->first();
        $fecha_desde = $lastFollowUp->start_date_of_incapacity;
        $dias = $lastFollowUp->days_of_incapacity;
        $fecha_hasta = Carbon::parse($fecha_desde)->addDays($dias)->startOfDay()->format('Y-m-d');
        $holiday = Holiday::where('holiday', $fecha_hasta)->first();
        $fecha_regreso = $holiday ? Carbon::parse($fecha_hasta)->addDay()->format('Y-m-d') : $fecha_hasta;

        //Aquí buscamos la provincia, canton y distrito (los nombres) esto con el método getLocationNamesFromJson
        $policySortController = new PolicySortController();
        $location = $policySortController->getLocationNamesFromJson(
            $lastFollowUp->province_incapacity_or_leave,
            $lastFollowUp->canton_incapacity_or_leave,
            $lastFollowUp->district_incapacity_or_leave);

        // Aplica la función a cada valor del array $location
        foreach ($location as $key => $value) {
            $location[$key] = $this->capitalizeWordsWithExceptions($value);
        }
        $pdf = Pdf::loadView('services.medical_services_secondary_care.docs.medical_disability_letter', [
            'logoPath' => $logoPath,
            'footerPath' => $footerPath,
            'activity' => $activity,
            'fecha_desde' => $fecha_desde,
            'fecha_hasta' => $fecha_hasta,
            'fecha_regreso' => $fecha_regreso,
            'lastFollowUp' => $lastFollowUp,
            'location' => $location
        ]);

        return $pdf->stream('preview.pdf');
    }

    public function form(Request $req, $cpath, $id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::with([
            'affiliate',
            'medical_services_secondary_care_sort',
            'medical_services_secondary_care_sort.followUps' => function ($query) {
                $query->orderBy('follow_up_number');
            },
            'medical_services_secondary_care_sort.followUps.companions',
            'medical_services_secondary_care_sort.followUps.diagnostics',
            'medical_services_secondary_care_sort.followUps.diagnosticsImages',
            'medical_services_secondary_care_sort.followUps.specialists',
            'medical_services_secondary_care_sort.followUps.medicalPrescriptions',
            'medical_services_secondary_care_sort.followUps.controlledMedications',
        ])->findOrFail($id);
        $birthday =  $activity->affiliate->birthday;
        //$age = $birthday->diffInYears(Carbon::now());

        if ($birthday) {
            $age = $birthday->diffInYears(Carbon::now());
        } else {
            $age = null; // O asigna un valor por defecto si aplica
        }

        $occupation = PolicySpreadsheetAffiliate::where('affiliate_id',$activity->affiliate->id )
            ->select('occupation')
            ->first();
        $activityPrimary = [];
        $activity_gis = Activity::where('client_id', $client->id)->where('id', $activity->parent_id)->first();
        if ($activity_gis){
            $gis = GisSort::where('activity_id', $activity_gis->id)->first();
            $policy_sort = Activity::where('id', $activity_gis->parent_id)->first()->policy_sort;
            if (!$policy_sort){
                return view('errors.known', ['message' => 'No se encontró la póliza relacionada a GIS: '.$activity_gis->id]);
            }
            //Json de actividades economica
            $jsonSource = ($policy_sort->economic_activity == 'public') ? '/js/economic_activity/public.json' : '/js/economic_activity/private.json';
            $json = file_get_contents(public_path($jsonSource));
            $economicActivities = json_decode($json, true);
            //Se transforma a una collección en laravel
            $activity_economic_name = collect($economicActivities)->firstWhere('CODE', $policy_sort->activity_economic_id)['ACTIVITY_NAME'];
            $fieldData = $activity_gis->gis_sort->gis_body_parts;

            //PRIMARIA
            $activityPrimary = Activity::with([
                'affiliate',
                'medical_services_sort',
                'medical_services_sort.followUps' => function ($query) {
                    $query->orderBy('follow_up_number');
                },
                'medical_services_sort.followUps.companions',
                'medical_services_sort.followUps.diagnostics',
                'medical_services_sort.followUps.diagnosticsImages',
                'medical_services_sort.followUps.specialists',
                'medical_services_sort.followUps.medicalPrescriptions',
                'medical_services_sort.followUps.controlledMedications',
            ])->where('parent_id', $activity_gis->id)
              ->where('service_id', Service::SERVICE_MEDICAL_SERVICES_SORT_MNK)
              ->get();

        }

        return view('services.medical_services_secondary_care.form.form', [
            'activity' => $activity,
            'id' => $id,
            'policy_sort' => $policy_sort ?? null,
            'activity_economic_name' => $activity_economic_name ?? null,
            'gis' => $gis ?? null,
            'age' => $age,
            'occupation'=> $occupation,
            'gisActivity' => $activity_gis,
            'fieldData' => $fieldData,
            'activityMedicalPrimary' => $activityPrimary,
            'diagnostic_mirror' => [],
        ]);
    }

    public function save(Request $req, $cpath, $id)
    {

        $client = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('client_id', $client->id)
            ->where('id', $id)
            ->firstOrFail();
        $medical_service = $activity->medical_services_secondary_care_sort;
        //Iniciamos la transacción
        DB::beginTransaction();
        try {
            if ($medical_service) {
                $data = [
                    //CAMPOS FIJOS
                    'identification' => $req->input('identification'),
                    'name_patient' => $req->input('name_patient') ?? $medical_service->name_patient,
                    'activity' => $req->input('activity') ?? $medical_service->activity,
                    'date_dictamen' => $req->input('date_dictamen') ?? $medical_service->date_dictamen,
                    'name_patron' => $req->input('name_patron') ?? $medical_service->name_patron,
                    'id_patron' => $req->input('id_patron') ?? $medical_service->id_patron,
                    'num_policy_sort' => $req->input('num_policy_sort') ?? $medical_service->num_policy_sort,
                    'service_group' => $req->input('service_group') ?? $medical_service->service_group,
                    'service_mode' => $req->input('service_mode') ?? $medical_service->service_mode,
                    'medical_record' => $req->input('medical_record') ?? $medical_service->medical_record,
                    'entity_promote' => $req->input('entity_promote') ?? $medical_service->entity_promote,
                    'specialty' => $req->input('specialty_ips') ?? $medical_service->specialty,
                    'date_case' => $req->date_case_medication_hidden,
                    'number_case' => $req->input('number_case'),
                    'requires_follow_up' => $req->input('requires_follow_up'),
                    'doctors_case' => $req->input('doctors_case'),
                    'doctors_name' => $req->input('doctors_name'),

                ];
                $medical_service->update($data);

                //DATA PARA LOS CAMPOS CON SEGUIMIENTO
                $data_follow = [
                    'valuation_date' => $req->input('valuation_date_submit'),
                    'valuation_time' => $req->input('valuation_time'),
                    'consultation_channel' => $req->input('consultation_channel1'),
                    'consultation_reason' => $req->input('consultation_reason'),
                    'labor_role_description' => $req->input('labor_role_description1'),
                    'records' => $req->input('records'),
                    'size' => $req->input('height_input_enabled'),
                    'weight' => $req->input('weight_input_enabled') ,
                    'ta' => $req->input('ta'),
                    'ta2' => $req->input('ta2'),
                    'imc' => $req->input('imc'),
                    'fc' => $req->input('fc'),
                    'fr' => $req->input('fr'),
                    'physical_examination_description' => $req->input('physical_examination_description') ,
                    'plan_description' => $req->input('plan_description'),
                    'closed_case' => $req->input('closed_case'),
                    'disability_type' => $req->input('disability_type'),
                    'attention_mode' => $req->input('attention_mode'),
                    'start_date_of_incapacity' => $req->input('start_date_of_incapacity_display_submit'),
                    'days_of_incapacity' => $req->input('days_of_incapacity'),
                    'origin_case' => $req->input('origin_case'),
                    'dx_principal' => $req->input('dx_principal'),
                    'dx_principal_description' => $req->input('dx_principal_description'),
                    'dx_related' => $req->input('dx_related'),
                    'dx_related_description' => $req->input('dx_related_description'),
                    'observations_explanatory_notes' => $req->input('observations_explanatory_notes'),
                    'origin_diagnosis_referral_specialist' => $req->input('origin_diagnosis_referral_specialist'),
                    'origin_diagnosis' => $req->input('origin_diagnosis'),
                    'document_type_evaluator' => $req->input('document_type_evaluator') ,
                    'identification_number_evaluator' => $req->input('identification_number_evaluator'),
                    'full_name_evaluator' => $req->input('full_name_evaluator'),
                    'medical_registration_number_evaluator' => $req->input('medical_registration_number_evaluator'),
                    'specialty_evaluator' => $req->input('specialty_evaluator'),
                    'license_number_evaluator' => $req->input('license_number_evaluator'),
                    'labor_role_description_observation' => $req->input('labor_role_description_observation'),
                    'approved_service' => $req->input('approved_service'),
                    'observation_audit' => $req->input('observation_audit'),
                    'diagnosis_origin_prescription' => $req->input('diagnosis_origin_prescription'),
                    'diagnosis_origin_controlled_medication' => $req->input('diagnosis_origin_controlled_medication'),
                    'province_controlled_medication' => $req->input('province_controlled_medication'),
                    'canton_controlled_medication' => $req->input('canton_controlled_medication'),
                    'district_controlled_medication' => $req->input('district_controlled_medication'),
                    'province_incapacity_or_leave' => $req->input('province_incapacity_or_leave'),
                    'canton_incapacity_or_leave' => $req->input('canton_incapacity_or_leave'),
                    'district_incapacity_or_leave' => $req->input('district_incapacity_or_leave'),
                    'province_medical_prescription' => $req->input('province_medical_prescription'),
                    'canton_medical_prescription' => $req->input('canton_medical_prescription'),
                    'district_medical_prescription' => $req->input('district_medical_prescription'),
                    'province_order_diagnostic' => $req->input('province_order_diagnostic'),
                    'canton_order_diagnostic' => $req->input('canton_order_diagnostic'),
                    'district_order_diagnostic' => $req->input('district_order_diagnostic'),
                    'province_referral_specialist' => $req->input('province_referral_specialist'),
                    'canton_referral_specialist' => $req->input('canton_referral_specialist'),
                    'district_referral_specialist' => $req->input('district_referral_specialist'),
                    'pharmacies_branch_prescription' => $req->input('pharmacies_branch_prescription'),
                    'province_laboratory_diagnostic' => $req->input('province_laboratory_diagnostic'),
                    'canton_laboratory_diagnostic' => $req->input('canton_laboratory_diagnostic'),
                    'district_laboratory_diagnostic' => $req->input('district_laboratory_diagnostic'),
                    'laboratory_origin_diagnosis' => $req->input('laboratory_origin_diagnosis'),
                    'required_hospitalization' => $req->input('required_hospitalization'),
                    'travel_expenses' => $req->input('travel_expenses'),
                    'transportation' => $req->input('transportation'),
                    'other_signs' => $req->input('other_signs'),
                    'next_follow_up_date' => $req->input('next_follow_up_date_submit'),
                    'medical_discharge' => $req->input('medical_discharge'),
                    'type_case_incapacity' => $req->input('type_case_incapacity'),
                    'requires_follow_up' => $req->input('requires_follow_up'),
                    'specialty' => $req->input('specialty'),
                    'referral_specialist' => $req->input('referral_specialist'),
                ];

                $requires_follow_up = $req->input('requires_follow_up');
                if ($requires_follow_up == null && !empty(array_filter($data_follow))){
                    // Realiza una actualización en el último seguimiento si $requires_follow_up es vacío
                    $follow_up = MedicalServiceSecondaryFollowUp::where('medical_services_secondary_care_id', $medical_service->id)
                        ->latest()->first();
                    if ($follow_up) {
                        $follow_up->update($data_follow);
                    } else {
                        // Si no existe un seguimiento previo, crea uno con follow_up_number = 1
                        $follow_up = new MedicalServiceSecondaryFollowUp();
                        $follow_up->fill($data_follow);
                        $follow_up->medical_services_secondary_care_id = $medical_service->id;
                        $follow_up->follow_up_number = 1;  // Asigna como primer seguimiento
                        $follow_up->save();
                    }
                }
                if ($requires_follow_up ==='1' || $requires_follow_up === '0') {
                    // Crear un nuevo seguimiento con follow_up_number incrementado
                    $latestFollowUp = MedicalServiceSecondaryFollowUp::where('medical_services_secondary_care_id', $medical_service->id)
                        ->latest('follow_up_number')
                        ->first();
                    $follow_up_number = $latestFollowUp ? $latestFollowUp->follow_up_number + 1 : 1;
                    $follow_up = new MedicalServiceSecondaryFollowUp();
                    $follow_up->fill($data_follow);
                    $follow_up->medical_services_secondary_care_id = $medical_service->id;
                    $follow_up->follow_up_number = $follow_up_number;
                    $follow_up->save();
                }

                $companions = $req->input('companions');
                if (isset($companions['name']) && !empty($companions['name'])) {
                    // Recorre cada acompañante
                    foreach ($companions['name'] as $index => $name) {
                        // Solo procesar si el nombre no está vacío
                        if (!empty($name)) {
                            $companion = new MedicalServiceSecondaryCompanion();
                            $companion->medical_services_secondary_care_id = $medical_service->id;
                            $companion->medical_service_follow_up_id = $follow_up->id;
                            $companion->name = $name;  // Se utiliza el nombre directamente
                            $companion->contact_phone = $companions['phone'][$index] ?? null; // Utiliza null si no existe
                            $companion->relationship = $companions['relationship'][$index] ?? null; // Utiliza null si no existe

                            $companion->save();
                        }
                    }
                }

                //GUARDAR DIAGNOSTICOS

                $diagnostics = $req->diagnostics;
                if ($diagnostics != null) {
                    foreach ($diagnostics as $key => $values) {
                        if (is_array($values) && count($values) > 1) {
                            array_pop($values);
                            $diagnostics[$key] = $values;
                        }
                    }
                    if (isset($diagnostics['diagnosis'][0])) {
                        foreach ($diagnostics['diagnosis'] as $index => $code) {
                            if (!is_null($code)) {
                                $diagnostic = new MedicalServiceSecondaryDiagnostics();
                                $diagnostic->medical_services_secondary_care_id = $medical_service->id;
                                $diagnostic->medical_service_follow_up_id = $follow_up->id;
                                $diagnostic->code = $code;
                                $diagnostic->description = $diagnostics['description'][$index] ?? '';
                                $diagnostic->description_editable = $diagnostics['description_editable'][$index] ?? '';
                                $diagnostic->laterality = $diagnostics['laterality'][$index] ?? '';
                                $diagnostic->origin = $diagnostics['case'][$index] ?? '';
                                $diagnostic->anatomical_zone = $diagnostics['anatomical_zone'][$index] ?? '';
                                $diagnostic->diagnosis = $diagnostics['diagnosis'][$index] ?? '';

                                $diagnostic->save();
                            }
                        }
                    }
                }

                //GUARDAR IMAGENES DIAGNOSTICAS
                $diagnostics_images =$req->diagnostics_images;
                //SE ELIMINA EL ULTIMO ELEMENTO DEL ARRAY YA QUE MANDA DUPLICADO DESDE EL BLADE
                if ($diagnostics_images != null && !empty($diagnostics_images)) {
                    foreach ($diagnostics_images as $key => $values) {
                        if (is_array($values) && count($values) > 1) {
                            array_pop($values);
                            $diagnostics_images[$key] = $values;
                        }
                    }
                }
                if (isset($diagnostics_images['cod']) && !is_null($diagnostics_images['cod'][0])){
                    foreach ($diagnostics_images['cod'] as $index => $code) {
                        if (!is_null($code)){
                            $diagnostics_image = new MedicalServiceSecondaryImageDiagnostics();
                            $diagnostics_image->medical_services_secondary_care_id = $medical_service->id;
                            $diagnostics_image->medical_service_follow_up_id = $follow_up->id;
                            $diagnostics_image->cod = $code;
                            $diagnostics_image->laterality = $diagnostics_images['laterality'][$index] ?? '';
                            $diagnostics_image->quantity = $diagnostics_images['quantity'][$index] ?? '';
                            $diagnostics_image->notes = $diagnostics_images['notes'][$index] ?? '';
                            $diagnostics_image->description = $diagnostics_images['description'][$index] ?? '';
                            $diagnostics_image->save();
                        }
                    }
                }

                //GUARDAR REMISION A ESPECIALISTA
                $specialists = $req->specialist;
                //SE ELIMINA EL ULTIMO ELEMENTO DEL ARRAY YA QUE MANDA DUPLICADO DESDE EL BLADE
                if ($specialists != null) {
                    foreach ($specialists as $key => $values) {
                        if (is_array($values) && count($values) > 1) {
                            array_pop($values);
                            $specialists[$key] = $values;
                        }
                    }
                    if (isset($specialists['cod'][0]) && !is_null($specialists['cod'][0])) {
                        foreach ($specialists['cod'] as $index => $code) {
                            if (!is_null($code)) {
                                $newSpecialist = new MedicalServiceSecondaryCareReferralSpecialits();
                                $newSpecialist->medical_services_sort_secondary_care_id = $medical_service->id;
                                $newSpecialist->medical_service_follow_up_id = $follow_up->id;
                                $newSpecialist->code = $code;
                                $newSpecialist->laterality = $specialists['laterality'][$index] ?? '';
                                $newSpecialist->description = $specialists['description'][$index] ?? '';
                                $newSpecialist->quantity = $specialists['quantity'][$index] ?? '';
                                $newSpecialist->notes = $specialists['notes'][$index] ?? '';

                                $newSpecialist->save();
                            }
                        }
                    }
                }

                //GUARDAR FORMULA MEDICA
                $molecula_medical_prescription = $req->input('molecula-prescription2');
                $tipo_medical_prescription = $req->input('tipo-prescription2');
                $descrip_medical_prescription = $req->input('descrip-prescription2');
                $codigo_medical_prescription = $req->input('codigo-vademecum2');
                $casa_medical_prescription = $req->input('casa-vademecum2');
                $treatment_durations_medical_prescription = $req->input('duracion_tratamiento2');
                $frequency_medical_prescription = $req->input('frecuencia2');
                $dosage_medical_prescription = $req->input('dosis2');
                $quantity_letters_medical_prescription = $req->input('cantidad-letras2');
                $quantity_numbers_medical_prescription = $req->input('cantidad-numeros2');
                $notes_medical_prescription = $req->input('notas2');

                if (isset($molecula_medical_prescription)) {
                    foreach ($molecula_medical_prescription as $index => $molecula) {
                        // Validar que no sean nulos los campos necesarios
                        if ($molecula === null ||
                            $tipo_medical_prescription[$index] === null ||
                            $descrip_medical_prescription[$index] === null ||
                            $codigo_medical_prescription[$index] === null ||
                            $casa_medical_prescription[$index] === null ||
                            $treatment_durations_medical_prescription[$index] === null ||
                            $frequency_medical_prescription[$index] === null ||
                            $dosage_medical_prescription[$index] === null ||
                            $quantity_letters_medical_prescription[$index] === null ||
                            $quantity_numbers_medical_prescription[$index] === null ||
                            $notes_medical_prescription[$index] === null) {

                            // Ignorar si algún campo es nulo
                            continue;
                        }
                        $array = [
                            'molecula' => $molecula,
                            'tipo' => $tipo_medical_prescription[$index],
                            'descrip' => $descrip_medical_prescription[$index],
                            'codigo' => $codigo_medical_prescription[$index],
                            'casa' => $casa_medical_prescription[$index],
                            'treatment_duration' => $treatment_durations_medical_prescription[$index],
                            'frequency' => $frequency_medical_prescription[$index],
                            'dosage' => $dosage_medical_prescription[$index],
                            'quantity_letters' => $quantity_letters_medical_prescription[$index],
                            'quantity_numbers' => $quantity_numbers_medical_prescription[$index],
                            'notes' => $notes_medical_prescription[$index],
                            'medical_services_secondary_care_id' => $medical_service->id,
                            'medical_service_follow_up_id' => $follow_up->id
                        ];
                        MedicalServiceSecondaryMedicalPrescription::create($array);
                    }
                }

                //GUARDAR FORMULA DE MEDICAMENTOS CONTROLADOS
                $generic_codes_controlled_medication = $req->input('generic-code');
                $generic_names_controlled_medication = $req->input('generic-name');
                $concentration_controlled_medication = $req->input('concentration');
                $pharmaceutical_form_controlled_medication = $req->input('pharmaceutical-form');
                $treatment_durations_controlled_medication = $req->input('duracion_tratamiento');
                $frequency_controlled_medication = $req->input('frecuencia');
                $dosage_controlled_medication = $req->input('dosis');
                $quantity_letters_controlled_medication = $req->input('cantidad-letras');
                $quantity_numbers_controlled_medication = $req->input('cantidad-numeros');
                $notes_controlled_medication = $req->input('notas');

                if (isset($generic_codes_controlled_medication)) {
                    foreach ($generic_codes_controlled_medication as $index => $genericCode) {

                        // Ignorar los campos nulos o vacíos
                        if ($genericCode === null ||
                            $generic_names_controlled_medication[$index] === null ||
                            $concentration_controlled_medication[$index] === null ||
                            $pharmaceutical_form_controlled_medication[$index] === null ||
                            $treatment_durations_controlled_medication[$index] === null ||
                            $frequency_controlled_medication[$index] === null ||
                            $dosage_controlled_medication[$index] === null ||
                            $quantity_letters_controlled_medication[$index] === null ||
                            $quantity_numbers_controlled_medication[$index] === null ||
                            $notes_controlled_medication[$index] === null) {

                            // Ignorar si algún campo es nulo
                            continue;
                        }
                        $array = [
                            'generic_code' => $genericCode,
                            'generic_name' => $generic_names_controlled_medication[$index],
                            'concentration' => $concentration_controlled_medication[$index],
                            'pharmaceutical_form' => $pharmaceutical_form_controlled_medication[$index],
                            'treatment_duration' => $treatment_durations_controlled_medication[$index],  // Corregido para mantener consistencia
                            'frequency' => $frequency_controlled_medication[$index],
                            'dosage' => $dosage_controlled_medication[$index],
                            'quantity_letters' => $quantity_letters_controlled_medication[$index],
                            'quantity_numbers' => $quantity_numbers_controlled_medication[$index],
                            'notes' => $notes_controlled_medication[$index],
                            'medical_service_sort_id' => $medical_service->id,
                            'medical_service_follow_up_id' => $follow_up->id
                        ];

                        MedicalServiceSecondaryCareControlledMedication::create($array);

                    }
                }

                //REPORTAR VALORACION PM
                if ($activity->state_id == StateMedicalServiceSecondarySort::VALORACION_ASIGNADA_A_PROVEEDOR) {
                    $description = "REPORTAR VALORACIÓN PM";
                    $activityActionsCreated = ActionController::create($activity->id,
                        ActionMedicalServiceSecondarySort::REPORTAR_VALORACION_PM,
                        $description);
                }
                DB::commit();
                return response()->json(['success' => true, 'message' => 'Datos guardados correctamente.']);
            } else {
                return response()->json(['error' => 'No se puede actualizar el servicio médico'], 500);
            }
        } catch (\Exception $e) {
            DB::rollback();
            return response()->json(['error' => $e->getMessage()], 500);
        }

    }

    //ANULAR SERVICIO
    public function cancelService(Request $request, $cpath, $id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $reason = $request->input('motivo');
        // Buscamos la actividad asociada al servicio médico
        $activity = Activity::where('client_id', $client->id)
            ->where('id', $id)
            ->firstOrFail();
        DB::beginTransaction();
        try {
            $medical_service = $activity->medical_services_sort;
            $medical_service->reason_cancel = $reason;
            $medical_service->save();
            ActionController::create($activity->id, ActionMedicalServiceSort::ANULAR_SERVICIO, 'Servicio Anulado por la acción anular servicio');
            DB::commit();
        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->back()->with('error', 'No existe Servicio');
        }
        return redirect()->back()->with('success', 'Servicio anulado correctamente');

    }

    //REPORTAR NEGACION DE SERVICIO
    public function denialService(Request $req, $cpath, $id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();

        // Buscamos la actividad asociada al servicio médico
        $activity = Activity::where('client_id', $client->id)
            ->where('id', $id)
            ->firstOrFail();
        $request_reason = $req->input('reason_cancel');
        $reason_cancel = [
            0 => 'Evento no laboral',
            1 => 'Diagnósticos de origen común',
            2 => 'Diagnósticos no derivados del evento',
            3 => 'No asegurado',
            4 => 'No pertinencia técnica'

        ];
        DB::beginTransaction();
        try {
            $medical_service = $activity->medical_services_secondary_care_sort;
            $medical_service->reason_cancel = $reason_cancel[$request_reason];
            $medical_service->save();

            $description = "SERVICIO NEGADO";
            $activityAction = ActionController::create($activity->id, ActionMedicalServiceSecondarySort::REPORTAR_NEGACION_SERVICIO, $description);

            $activityGisSort = $activity->parent_activity;
            $activityPolicySort = $activityGisSort->parent_activity;
            $gisSort = $activityGisSort->gis_sort;

            $emails = [
                $activityPolicySort->affiliate->email, // Tomador
                $activityGisSort->affiliate->email // Afiliado
            ];

            $emailData = TemplateBuilder::build(
                Templates::EMAIL_NOTIFICATION_OF_MEDICAL_SERVICE_REJECTION,
                [
                    'name' => mb_convert_case(mb_strtolower(optional($activity->affiliate)->full_name ?? '', 'UTF-8'), MB_CASE_TITLE, 'UTF-8'),
                    'case_number' => $gisSort->formatCaseNumber(),
                    'event_date' => Carbon::parse($gisSort->date_accident)->format('d/m/Y'),
                    'reason_cancel' => $reason_cancel[$request_reason],
                ]
            );

            $emailSent = new SendDocumentDataBase(
                implode(',', $emails),
                $emailData['subject'],
                "<EMAIL>",
                $emailData['subject'],
                [
                    "text" => $emailData['body'],
                    "sender" => $emailData['sender']
                ],
                "<EMAIL>",
                [],
                "send_document_db",
                $client,
                request()->getHost(),
                $activity->id,
                $activityAction->id,
                $activity->service_id
            );
            
            // Capturar el resultado del envío
            $result = $emailSent->sendMail();

            //Registramos los datos del correo enviado para la trazabilidad
            $mailBoardController = new MailBoardController();
            $mailBoardController->createRegisterMail(
                $activity->id,
                $activity->service->id, 
                $activityPolicySort->policy_sort->consecutive, 
                'Tomador', 
                mb_convert_case(mb_strtolower($activityGisSort->affiliate->first_name ?? ''), MB_CASE_TITLE, "UTF-8"), 
                $activityGisSort->affiliate->doc_number, 
                $emailData['subject'], 
                $emailData['body'],
                $emails, 
                $result,
                null
            );

            DB::commit();
        } catch (\Exception $e) {
            DB::rollback();
            return back()->with('message', 'No se pudo realizar la operacion');
        }
        return back()->with('message', 'Operación realizada con éxito');

    }

    //ACCION REMITIR INCAPACIDAD MEDICA
    public function medical_disability($cpath, $id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('client_id', $client->id)
            ->where('id', $id)
            ->firstOrFail();
        if ($activity->state_id == StateMedicalServiceSecondarySort::VALORACION_REALIZADA) {
            DB::beginTransaction();
            try {
                //Cambio de estado de la actividad
                $activityAction = ActionController::create($activity->id, ActionMedicalServiceSecondarySort::EMITIR_INCAPACIDAD_MEDICA, 'Acción Emitir Incapacidad Médica');

                //CAMPOS PARA EL PDF DEL CORREO

                $logoPath = public_path('images/mnk.png');
                $footerPath = public_path('images/mnk_footer.png');

                //OBTENER EL ULTIMO FOLLOWUP DE LA PM
                $lastFollowUp = $activity->medical_services_secondary_care_sort->followUps()->latest()->first();
                $fecha_desde = $lastFollowUp->start_date_of_incapacity;
                $dias = $lastFollowUp->days_of_incapacity;
                $fecha_hasta = Carbon::parse($fecha_desde)->addDays($dias)->startOfDay()->format('Y-m-d');
                $holiday = Holiday::where('holiday', $fecha_hasta)->first();
                $fecha_regreso = $holiday ? Carbon::parse($fecha_hasta)->addDay()->format('Y-m-d') : $fecha_hasta;

                //Aquí buscamos la provincia, canton y distrito (los nombres) esto con el método getLocationNamesFromJson
                $policySortController = new PolicySortController();
                $location = $policySortController->getLocationNamesFromJson(
                    $lastFollowUp->province_incapacity_or_leave,
                    $lastFollowUp->canton_incapacity_or_leave,
                    $lastFollowUp->district_incapacity_or_leave);

                // Aplica la función a cada valor del array $location
                foreach ($location as $key => $value) {
                    $location[$key] = $this->capitalizeWordsWithExceptions($value);
                }
                $pdf = Pdf::loadView('services.medical_services_secondary_care.docs.medical_disability', [
                    'logoPath' => $logoPath,
                    'footerPath' => $footerPath,
                    'activity' => $activity,
                    'fecha_desde' => $fecha_desde,
                    'fecha_hasta' => $fecha_hasta,
                    'fecha_regreso' => $fecha_regreso,
                    'lastFollowUp' => $lastFollowUp,
                    'location' => $location
                ]);
                $filePathDisability = "documents/medical_disability.blade.php_{$activity->id}".uniqid().".pdf";
                Storage::disk('s3')->put($filePathDisability, $pdf->output());
                $activityActionDocument = new ActivityActionDocument;
                $activityActionDocument->activity_action_id = $activityAction->id;
                $activityActionDocument->name = 'medical_disability';
                $activityActionDocument->path = $filePathDisability;
                $activityActionDocument->save();

                // Formar el arreglo de archivos con 'type', 'path' y 'name'
                $files = [
                    [
                        'type' => 'pdf',
                        'path' => $filePathDisability,
                        'name' => "Incapacidad médica o licencia.pdf"
                    ],
                ];
                $gis = $activity->parent;
                $takerName = mb_convert_case(mb_strtolower($gis->parent->affiliate->full_name, 'UTF-8'), MB_CASE_TITLE, 'UTF-8');
                $numPolicySort = $activity->medical_services_secondary_care_sort->num_policy_sort;
                //$numCase = $activity->medical_services_secondary_care_sort->number_case;
                $numCase = $gis->gis_sort->formatCaseNumber();
                $nameWorker = mb_convert_case(mb_strtolower($activity->affiliate->full_name, 'UTF-8'), MB_CASE_TITLE, 'UTF-8');
                $workerDoc = $activity->medical_services_secondary_care_sort->identification;
                $dateCase = $gis->gis_sort->date_accident;
                $fecha_desde = ucfirst(strftime('%A %e de %B del %Y', strtotime($fecha_desde )));
                $fecha_hasta = ucfirst(strftime('%A %e de %B del %Y', strtotime($fecha_hasta )));

                if($dateCase)
                {
                    $dateFormat = ucfirst(strftime('%A %e de %B del %Y', strtotime($dateCase)));
                }
                else{
                    $dateFormat = "Fecha no disponible";
                }

                $text ="
                <p><strong>Asunto</strong>: Otorgamiento de incapacidad temporal.</p>
                <p>¡Buen día, <strong>$takerName</strong> !</p>
                <p>
                    Como parte de nuestro compromiso de mantenerlo informado de las gestiones atendidas al amparo de su póliza del Seguro Obligatorio de Riesgos del Trabajo <strong>$numPolicySort</strong>, hacemos de su conocimiento que en el caso <strong>#$numCase</strong>, a nombre del trabajador(a): <strong>$nameWorker</strong>, número de identificación <strong>$workerDoc</strong>, con fecha de evento <strong>$dateFormat</strong>, la Red Médica de MNK Seguros ha extendido un período de incapacidad temporal que va del <strong>$fecha_desde</strong> hasta el <strong>$fecha_hasta</strong>, que será pagado a la persona trabajadora por semana vencida.
                     Si tiene alguna consulta o necesita más detalles sobre este caso, por favor, contáctenos al <strong>4102-7600</strong>. ¡Será un gusto servirle!
                    Nuestro propósito es garantizar la protección y bienestar de sus colaboradores, así como brindarle la experiencia de servicio que usted y ellos merecen.
                    .
                </p>
                ";

                $emails = [];

                if (!empty($gis->parent->affiliate->email)) {
                    $emails[] = $gis->parent->affiliate->email;
                }

                if (!empty($gis->affiliate->email)) {
                    $emails[] = $gis->affiliate->email;
                }


                $emailString = implode(',', $emails);

                //enviar correo
                $emailRequestInfo = new SendDocumentDataBase(
                    $emailString,
                    "Otorgamiento de incapacidad temporal",
                    "<EMAIL>",
                    "Incapacidad realizada",
                    ["text" => $text,
                        "sender" => 'MNK'],
                    "<EMAIL>",
                    $files,
                    "send_document_db",
                    $client,
                    request()->getHost(),
                    $activity->id,
                    $activityAction->id,
                    $activity->service->id
                );
                
                // Capturar el resultado del envío
                $result = $emailRequestInfo->sendMail();

                //Registramos los datos del correo enviado para la trazabilidad
                $mailBoardController = new MailBoardController();
                $mailBoardController->createRegisterMail(
                    $activity->id,
                    $activity->service->id, 
                    $gis->parent->policy_sort->consecutive, 
                    'Tomador', 
                    $takerName, 
                    $gis->parent->affiliate->doc_number, 
                    'Otorgamiento de incapacidad temporal', 
                    $text,
                    $emails, 
                    $result,
                    $files
                );

                //aperturar servicio PE IT-SORT
                $activity_pe_sit = new Activity();
                $activity_pe_sit->parent_id = $activity->id;
                $activity_pe_sit->client_id = $client->id;
                $activity_pe_sit->service_id = Service::SERVICE_PE_IT_SORT_MNK;
                $activity_pe_sit->affiliate_id = $activity->affiliate_id;
                $activity_pe_sit->user_id = Auth::id();
                $activity_pe_sit->state_id = State::REGISTRADO;
                $activity_pe_sit->save();
                //Guardar campos en la tabla de pe sit
                $peItSort=PeItSort::create([
                    'name_affiliated_pe' =>$activity->medical_services_secondary_care_sort->name_patient,
                    'number_ide_pe' => $activity->medical_services_secondary_care_sort->identification,
                    'type_attention' => $lastFollowUp->attention_mode,
                    'code_ips_pe' =>$activity->medical_services_secondary_care_sort->primary_care_provider, //este es el id del proveedor
                    'service_group_pe' => $activity->medical_services_secondary_care_sort->service_group,
                    'type_of_service_pe' => $activity->medical_services_secondary_care_sort->service_mode,
                    'medical_document_pe' => $lastFollowUp->document_type_evaluator,
                    'medical_identification_pe' =>  $lastFollowUp->identification_number_evaluator,
                    'names_surnames_pe' =>$lastFollowUp->full_name_evaluator,
                    'medical_record_pe' => $lastFollowUp->medical_registration_number_evaluator,
                    'specialty_pe' =>$lastFollowUp->specialty_evaluator,
                    'activity_id' => $activity_pe_sit->id,
                    'type_it' => ($lastFollowUp->disability_type === 'INI') ? 1 : (($activity->medical_services_sort->disability_type === 'PRO') ? 0 : null),
                    'day_1' =>$lastFollowUp->start_date_of_incapacity,
                    'number_case'=>$activity->medical_services_secondary_care_sort->number_case,
                    'medical_service_follow_up_id' => $lastFollowUp->id
                ]);
                $startDate = $lastFollowUp->start_date_of_incapacity;
                $daysOfIncapacity = $lastFollowUp->days_of_incapacity ;

                // Crear instancia de Carbon a partir de la fecha de inicio
                $start = Carbon::parse($startDate);

                // Sumar los días de incapacidad, restando un día para contar desde la fecha de inicio
                $end = $start->addDays($daysOfIncapacity - 1);

                PeitInabilitySort::create([
                    'pe_it_sort_id' => $peItSort->id,
                    'start_date' => $lastFollowUp->start_date_of_incapacity,
                    'end_date' => $end->format('Y-m-d'),
                    'days_it' => $daysOfIncapacity,
                ]);

                $diagnostics= MedicalServiceSecondaryDiagnostics::where('medical_services_secondary_care_id',$activity->medical_services_secondary_care_sort->id)->get() ;


                foreach ($diagnostics as $record) {
                    PeItSortCaseDx::insert([
                        'pe_it_sort_id'       => $peItSort->id,
                        'code_cie_10'        => $record->code,     // Map 'code' to 'code_cie_10'
                        'diagnostico_pe'     => $record->description, // Map 'description' to 'diagnostico_pe'
                        'laterality'         => $record->laterality,
                    ]);
                }
                $description = "Acción generada Registrar IT integrado";
                ActionController::create(
                    $activity_pe_sit->id,
                    ActionPeItSort::REGISTRAR_IT_INTEGRADO,
                    $description
                );
                // Agregar documento de incapacidad
                $activityDocumentPeItSort = new ActivityDocument();
                $activityDocumentPeItSort->document_id = 261;
                $activityDocumentPeItSort->activity_id = $activity_pe_sit->id;
                $activityDocumentPeItSort->path = $filePathDisability;
                $activityDocumentPeItSort->save();

                DB::commit();
            } catch (\Exception $e) {
                DB::rollback();
                return response()->json(['error' => $e->getMessage()], 500);
            }

            return back()->with('message', 'Operación realizada con éxito');
        } else {
            return response()->json(['error' => 'No se puede generar la incapacidad medica'], 500);
        }
    }


    // ACCION EMITIR PRESTACION MEDICA HOSPITALIZACION con tiempo
    public function medical_disability_time(Request $request, $cpath)
    {
        $activity_id = $request->input('activity_id') ?? null;
        // Verificar si se proporcionó un número de prefactura
        if (!$activity_id) {
            return response()->json([
                'valid' => false,
                'message' => 'No se proporcionó un número de actividad.'
            ]);
        }

        //Iniciamos la transacción
        DB::beginTransaction();
        try {
            //capturar la actividad
            $activity = Activity::where('id', $activity_id)
                ->firstOrFail();

            //verificar si la actividad existe
            if(empty($activity))
            {
                return response()->json([
                    'valid' => false,
                    'message' => 'No se encontró la actividad.'
                ]);
            }

            //verificar si ha pasado 5min desde la última emisión de incapacidad
            $lastEnableDisability = session()->has('time_enable_disability') ? session()->get('time_enable_disability') : null;

            //validar si está vacío el campo
            if(empty($lastEnableDisability))
            {
                // Guardar una variable en la sesión
                session(['time_enable_disability' => Carbon::now()]);

                //descargar la incapacidad
                $this->medical_disability($cpath, $activity_id);

                //guardar transacción
                DB::commit();

                return response()->json(['valid' => true,
                    'message' => 'Se ha emitido la incapacidad médica correctamente']);
            }

            //validar si la diferencia es mayor a 5min
            if(Carbon::parse($lastEnableDisability)->diffInMinutes(Carbon::now()) > 5)
            {
                // Guardar una variable en la sesión
                session(['time_enable_disability' => Carbon::now()]);

                //descargar la incapacidad
                $this->medical_disability($cpath, $activity_id);

                //guardar transacción
                DB::commit();

                return response()->json(['valid' => true,
                    'message' => 'Se ha emitido la incapacidad médica correctamente']);
            }
            else
            {
                return response()->json(['valid' => false,
                    'message' => 'No puedes emitir la incapacidad médica nuevamente, debes esperar 5min']);
            }

        }
        catch (\Exception $e) {
            DB::rollback();
            return response()->json(['valid' => false, 'message' => 'Ha ocurrido un error al emitir la incapacidad']);
        }

    }

    //ACCION EMITIR PRESTACION MEDICA HOSPITALIZACION
    public function issueMedicalServiceHospitalization($cpath, $id)
    {

        $client = Client::where('path', $cpath)->firstOrFail();
        DB::beginTransaction();
        try {

            $medicalServiceActivity = Activity::where('id', $id)->where('client_id', $client->id)->firstOrFail();
            //TODO: Generar documento para la orden de emisión de cirugía y /o hospitalización
            // Generar el PDF
            $document = 'issueMedicalServiceHospitalizationMedical';
            $action_id = ActionMedicalServiceSecondarySort::EMITIR_PRESTACION_MEDICA_HOSPITALIZACION;

            $pdf = PDF::loadView("services.medical_services_secondary_care.docs.{$document}_pdf", ['value' => "pendiente"]); //modificar

            // Guardar el PDF en S3
            Storage::disk('s3')
                ->put("activity_action_document/{$document}_{$action_id}.pdf", $pdf->output());

            //TODO: Crear historial de acciones
            $description = "EMITIR PRESTACIÓN MÉDICA HOSPITALIZACIÓN";
            $activityActionsCreated = ActionController::create($medicalServiceActivity->id, $action_id, $description);

            $activityActionDocument = new ActivityActionDocument();
            $activityActionDocument->activity_action_id = $activityActionsCreated->id;
            $activityActionDocument->name = $document;
            $activityActionDocument->path = "activity_action_document/{$document}_{$action_id}.pdf";
            $activityActionDocument->save();

            //TODO: aperturar de manera automática un nuevo servicio ""Prestación Médica SORT"",
            // y generar la acción REPORTAR SOLICITUD PRESTACIÓN MÉDICA"

            $this->reportMedicalServiceRequest($cpath, $medicalServiceActivity->id, $activityActionDocument->name, $activityActionDocument->path,1);

            DB::commit();

            return back()->with('message', 'Operación realizada con éxito');

        } catch (\Exception $e) {
            DB::rollBack();
            // Retornar un mensaje de error o manejar la excepción
            return response()->json([
                'error' => 'Ocurrió un error al guardar los datos',
                'code' => $e->getCode(),
                'message' => $e->getMessage()
            ], 500);
        }
    }

    //ACCION EMITIR ORDEN DE IMAGENES DIAGNOSTICAS
    public function issueDiagnosticImagingOrder($cpath, $id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        DB::beginTransaction();
        try {

            $medicalServiceActivity = Activity::where('id', $id)->where('client_id', $client->id)->firstOrFail();

            //TODO: Generar documento para la orden de emisión imágenes diagnósticas
            // Generar el PDF
            $document = 'issueDiagnosticImagingOrderMedical';
            $action_id = ActionMedicalServiceSecondarySort::EMITIR_ORDEN_DE_IMAGENES_DIAGNOSTICAS;

            $pdf = PDF::loadView("services.medical_services_secondary_care.docs.{$document}_pdf", ['value' => "pendiente"]); //modificar

            // Guardar el PDF en S3
            Storage::disk('s3')
                ->put("activity_action_document/{$document}_{$action_id}.pdf", $pdf->output());

            //TODO: Crear historial de acciones
            $description = "EMITIR ORDEN DE IMÁGENES DIAGNÓSTICAS";

            $activityActionsCreated = ActionController::create($medicalServiceActivity->id, $action_id, $description);

            $activityActionDocument = new ActivityActionDocument();
            $activityActionDocument->activity_action_id = $activityActionsCreated->id;
            $activityActionDocument->name = $document;
            $activityActionDocument->path = "activity_action_document/{$document}_{$action_id}.pdf";
            $activityActionDocument->save();


            //TODO: aperturar de manera automática un nuevo servicio ""Prestación Médica SORT"",
            // y generar la acción REPORTAR SOLICITUD PRESTACIÓN MÉDICA"
            $new_activity = $this->reportMedicalServiceRequest($cpath,
                $medicalServiceActivity->id,
                $activityActionDocument->name,
                $activityActionDocument->path,2);

            //ULTIMO FOLLOWUP DE LA PM
            $pm_img = MedicalServicesSecondaryCareSort::where('activity_id', $new_activity)->first();
            $lastFollowUp = $medicalServiceActivity->medical_services_secondary_care_sort->followUps()->latest()->first();
            //PRIMER FOLLOW DE LA NUEVA PM IMG
            $new_follow_img = new MedicalServiceSecondaryFollowUp();
            $new_follow_img->fill($lastFollowUp->toArray());
            $new_follow_img->medical_services_sort_secondary_care_id = $pm_img->id;
            $new_follow_img->follow_up_number = 1;
            $new_follow_img->next_follow_up_date = null;
            $new_follow_img->save();
            //CREAR DIAGNOSTICOS DE LA PM IMG
            $diagnostics_img = $lastFollowUp->diagnosticsImages;
            foreach ($diagnostics_img as $diagnostic) {
                $new_diagnostic_img = new MedicalServiceSecondaryImageDiagnostics();
                $new_diagnostic_img->fill($diagnostic->toArray());
                $new_diagnostic_img->medical_services_secondary_care_id = $pm_img->id;
                $new_diagnostic_img->medical_service_follow_up_id = $new_follow_img->id;
                $new_diagnostic_img->save();
            }

            DB::commit();

            return back()->with('message', 'Operación realizada con éxito');

        } catch (\Exception $e) {
            DB::rollBack();
            // Retornar un mensaje de error o manejar la excepción
            return response()->json([
                'error' => 'Ocurrió un error al guardar los datos',
                'code' => $e->getCode(),
                'message' => $e->getMessage()
            ], 500);
        }
    }

    //EMITIR FORMULA DE MEDICAMENTOS
    public function emitPrescription($cpath, $id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        DB::beginTransaction();
        try {
            $medicalServiceActivity = Activity::where('id', $id)->where('client_id', $client->id)->firstOrFail();

            //TODO: Generar documento fórmula de medicamentos
            // Generar el PDF
            $document = 'emitPrescriptionMedical';
            $action_id = ActionMedicalServiceSecondarySort::EMITIR_FORMULA_DE_MEDICAMENTOS;

            $pdf = PDF::loadView("services.medical_services_secondary_care.docs.{$document}_pdf", ['value' => "pendiente"]);

            // Guardar el PDF en S3
            Storage::disk('s3')
                ->put("activity_action_document/{$document}_{$action_id}.pdf", $pdf->output());

            //TODO: Crear historial de acciones
            $description = "EMITIR FORMULA DE MEDICAMENTOS";
            $activityActionsCreated = ActionController::create($medicalServiceActivity->id, $action_id, $description);

            $activityActionDocument = new ActivityActionDocument();
            $activityActionDocument->activity_action_id = $activityActionsCreated->id;
            $activityActionDocument->name = $document;
            $activityActionDocument->path = "activity_action_document/{$document}_{$action_id}.pdf";
            $activityActionDocument->save();

            //TODO: Entregar los medicamentos prescritos al paciente según la fórmula generada.


            //TODO: aperturar de manera automática un nuevo servicio "MEDICAMENTOS SORT",
            $medication_activity = new MedicationServicesController();
            $new_activity = $medication_activity->reportMedicationRequest($cpath, $medicalServiceActivity->id, $action_id, $document, $activityActionDocument->path);

            DB::commit();

            return back()->with('message', 'Operación realizada con éxito');

        } catch (\Exception $e) {
            DB::rollBack();
            // Retornar un mensaje de error o manejar la excepción
            return response()->json([
                'error' => 'Ocurrió un error al guardar los datos',
                'code' => $e->getCode(),
                'message' => $e->getMessage(),
                'line' => $e->getLine()
            ], 500);
        }
    }

    //EMITIR FORMULA DE MEDICAMENTOS CONTROLADOS
    public function emitControlledPrescription($cpath, $id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        DB::beginTransaction();
        try {

            $medicalServiceActivity = Activity::where('id', $id)->where('client_id', $client->id)->firstOrFail();

            //TODO: Generar documento fórmula de medicamentos controlados
            // Generar el PDF
            $document = 'emitControlledPrescriptionMedical';
            $action_id = ActionMedicalServiceSort::EMITIR_FORMULA_DE_MEDICAMENTOS_CONTROLADO;

            $pdf = PDF::loadView("services.medical_services_secondary_care.docs.{$document}_pdf", ['value' => "pendiente"]); //modificar

            // Guardar el PDF en S3
            Storage::disk('s3')
                ->put("activity_action_document/{$document}_{$action_id}.pdf", $pdf->output());

            //TODO: Crear historial de acciones
            $description = "EMITIR FORMULA DE MEDICAMENTOS CONTROLADO";
            $activityActionsCreated = ActionController::create($medicalServiceActivity->id, $action_id, $description);

            $activityActionDocument = new ActivityActionDocument();
            $activityActionDocument->activity_action_id = $activityActionsCreated->id;
            $activityActionDocument->name = $document;
            $activityActionDocument->path = "activity_action_document/{$document}_{$action_id}.pdf";
            $activityActionDocument->save();

            //TODO: Entregar los medicamentos prescritos al paciente según la fórmula generada.


            //TODO: aperturar de manera automática un nuevo servicio "MEDICAMENTOS SORT",
            $medication_activity = new MedicationServicesController();
            $medication_activity->reportMedicationRequest($cpath, $medicalServiceActivity->id, $action_id, $document, $activityActionDocument->path);

            DB::commit();

            return back()->with('message', 'Operación realizada con éxito');

        } catch (\Exception $e) {
            DB::rollBack();
            // Retornar un mensaje de error o manejar la excepción
            return response()->json([
                'error' => 'Ocurrió un error al guardar los datos',
                'code' => $e->getCode(),
                'message' => $e->getMessage(),
                'line' => $e->getLine()
            ], 500);
        }
    }

    public function reportMedicalAuditMnk(Request $req, $cpath, $id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        DB::beginTransaction();
        try {

            //TODO: Recibir servicio / actividad, de manera automática, cuando en el servicio GIS se ejecute la acción: "Aviso de accidente o enfermedad"
            $medicalServiceActivity = Activity::where('id', $id)->where('client_id', $client->id)->firstOrFail();

            //TODO: Generar documento reporte de auditoría médica MNK
            // Generar el PDF
            $document = 'reportMedicalAuditMnkMedical';
            $action_id = ActionMedicalServiceSecondarySort::REPORTAR_AUDITORIA_MEDICA_MNK;

            $pdf = PDF::loadView("services.medical_services_secondary_care.docs.{$document}_pdf", ['value' => "pendiente"]); //modificar

            // Guardar el PDF en S3
            Storage::disk('s3')
                ->put("activity_action_document/{$document}_{$action_id}.pdf", $pdf->output());

            //TODO: Crear historial de acciones
            $description = $req->input('description');
            $activityActionsCreated = ActionController::create($medicalServiceActivity->id, $action_id, $description);

            $activityActionDocument = new ActivityActionDocument();
            $activityActionDocument->activity_action_id = $activityActionsCreated->id;
            $activityActionDocument->name = $document;
            $activityActionDocument->path = "activity_action_document/{$document}_{$action_id}.pdf";
            $activityActionDocument->save();

            DB::commit();

            return back()->with('success', 'Operación realizada con éxito');

        } catch (\Exception $e) {
            DB::rollBack();
            // Retornar un mensaje de error o manejar la excepción
            return response()->json([
                'error' => 'Ocurrió un error al guardar los datos',
                'code' => $e->getCode(),
                'message' => $e->getMessage()
            ], 500);
        }
    }

    //ACCION REMISION A ESPECIALISTA
    public function referral_specialist($cpath, $id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('client_id', $client->id)
            ->where('id', $id)
            ->firstOrFail();
        if ($activity->state_id == StateMedicalServiceSort::VALORACION_REALIZADA) {
            DB::beginTransaction();
            try {
                //Cambio de estado de la actividad
                $activityAction = ActionController::create($activity->id, ActionMedicalServiceSort::EMITIR_REMISION_A_ESPECIALISTA, 'Acción emitir remisión a especialista');

                //Generar documento y guardar el path
                $pdf = PDF::loadView('services.medical_services_secondary_care.docs.referral_specialist', [
                    'activity' => $activity,
                    'watermark' => true,
                ]);
                $filePath = "documents/referral_specialist.blade.php_{$activity->id}.pdf";
                Storage::disk('s3')->put($filePath, $pdf->output());
                $activityActionDocument = new ActivityActionDocument;
                $activityActionDocument->activity_action_id = $activityAction->id;
                $activityActionDocument->name = 'referral_specialist';
                $activityActionDocument->path = $filePath;
                $activityActionDocument->save();

                //llamar a la accion REPORTAR SOLICITUD PRESTACIÓN MÉDICA
                $new_activity = $this->reportMedicalServiceRequest($cpath, $activity->id, $activityActionDocument->name, $filePath,3);
                //ULTIMO FOLLOWUP DE LA PM
                $pm_referral_specialist = MedicalServicesSort::where('activity_id', $new_activity)->first();
                $lastFollowUp = $activity->medical_services_sort->followUps()->latest()->first();
                //PRIMER FOLLOW DE LA NUEVA PM IMG
                $new_follow_referral = new MedicalServiceFollowUp();
                $new_follow_referral->fill($lastFollowUp->toArray());
                $new_follow_referral->medical_services_sort_id = $pm_referral_specialist->id;
                $new_follow_referral->follow_up_number = 1;
                $new_follow_referral->next_follow_up_date = null;
                $new_follow_referral->save();
                //CREAR FOLLOW REMISION A ESPECIALISTA DE LA PM
                $referral_specialist = $lastFollowUp->specialists;
                foreach ($referral_specialist as $referral) {
                    $new_referral_spealist = new MedicalServiceReferralSpecialits();
                    $new_referral_spealist->fill($referral->toArray());
                    $new_referral_spealist->medical_service_sort_id = $pm_referral_specialist->id;
                    $new_referral_spealist->medical_service_follow_up_id = $new_follow_referral->id;
                    $new_referral_spealist->save();
                }

                DB::commit();
            } catch (\Exception $e) {
                DB::rollback();
                return response()->json(['error' => $e->getMessage()], 500);
            }

            return back()->with('message', 'Operación realizada con éxito');
        } else {
            return response()->json(['error' => 'No se puede referir al especialista estado incorrecto'], 500);
        }
    }

    public function reportMedicalServiceRequest($cpath, $id, $document_name, $document_path, $action_id) //REPORTAR SOLICITUD PRESTACIÓN MÉDICA
    {

        $client = Client::where('path', $cpath)->firstOrFail();
        try {
            //Servicio Prestación Medica SORT padre
            $med_Service_act_parent = Activity::where('id', $id)->where('client_id', $client->id)->firstOrFail();
            $med_Service_sort_parent = MedicalServicesSort::where('activity_id', $med_Service_act_parent->id)->firstOrFail();

            //TODO: Crear servicio Prestación Médica SORT
            $medicalServiceActivity = new Activity;
            $medicalServiceActivity->parent_id = $med_Service_act_parent->parent_id;
            $medicalServiceActivity->client_id = $client->id;
            $medicalServiceActivity->service_id = Service::SERVICE_MEDICAL_SERVICES_SORT_MNK;
            $medicalServiceActivity->affiliate_id = $med_Service_act_parent->affiliate_id; //Cambiar
            $medicalServiceActivity->user_id = Auth::id();
            $medicalServiceActivity->state_id = State::REGISTRADO;
            $medicalServiceActivity->save();


            //TODO: Asignar a un analista para auditoría médica
            $analyst = RuleActionsController::getLastUserAction(ActionMedicationServiceSort::REPORTAR_SOLICITUD_MEDICAMENTOS, Area::ANALISTA_ASEGURAMIENTO); //Devuelve el id del analista dinámicamente
            $medicalServiceSort = new MedicalServicesSort();
            $medicalServiceSort->activity_id = $medicalServiceActivity->id;
            $medicalServiceActivity->medical_audit_analyst = $analyst;
            //TODO: Asignar por default al proveedor BMI (dejar esta información en el formulario en la pestaña Datos del proveedor)
            $medicalServiceSort->fill($med_Service_sort_parent->toArray());
            $medicalServiceSort->provider = 'Addiuva';
            $medicalServiceSort->primary_care_provider =  Provider::PROVIDER_ADDIUVA;
            $medicalServiceSort->action_id = $action_id;
            $medicalServiceSort->requires_follow_up = null;

            //TODO: Replicar en el formulario los datos de la pestaña:
            // Datos del paciente (no es necesario ya que lo trae GIS) y Datos del Caso"
            // Diagnósticos
            $medicalServiceSort->save();

            //TODO: Traer la orden médica generada o cargada en el servicio anterior en alguna de las acciones
            //TODO: Crear historial de acciones
            $description = "REPORTAR SOLICITUD PRESTACIÓN MÉDICA";
            $activityActionsCreated = ActionController::create($medicalServiceActivity->id,
                ActionMedicalServiceSort::REPORTAR_SOLICITUD_PRESTACION_MEDICA,
                $description);

            if ($document_name !== '' && $document_path !== '') {

                $activityActionDocument = new ActivityActionDocument;
                $activityActionDocument->activity_action_id = $activityActionsCreated->id;
                $activityActionDocument->name = $document_name;
                $activityActionDocument->path = $document_path;
                $activityActionDocument->save();

            }



            return $medicalServiceActivity->id;

        } catch (\Exception $e) {
            throw $e;
        }
    }

    public function reportPmAssessment(Request $req, $cpath, $id) //REPORTAR VALORACIÓN PM
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        DB::beginTransaction();
        try {

            //TODO: Recibir servicio / actividad, de manera automática, cuando en el servicio GIS se ejecute la acción: "Aviso de accidente o enfermedad"
            $medicalServiceActivity = Activity::where('id', $id)->where('client_id', $client->id);

            //TODO: Formulario Diligenciado: Utilizar RenApp para gestionar la información médica del proveedor y completar el formulario requerido.
            //Capturar todos los datos del formulario y guardarlos en la base de datos
            $this->save($req, $cpath, $id);

            /*
            Integración con el Sistema de Historia Clínica: Si se dispone de integración con el sistema de Historia Clínica del proveedor,
            importar la información necesaria directamente desde este sistema para completar el formulario. //Está por definirse

            Carga de Historia Clínica en Archivo Digital: Subir el archivo digital de la Historia Clínica en RenApp para que la información esté disponible en el sistema. //Está por definirse
             */

            //TODO: Crear historial de acciones
            $description = "REPORTAR VALORACIÓN PM";
            $activityActionsCreated = ActionController::create($medicalServiceActivity->id,
                ActionMedicalServiceSecondarySort::REPORTAR_VALORACION_PM,
                $description);

            DB::commit();

            return response()->json(['id' => $medicalServiceActivity->id]);

        } catch (\Exception $e) {
            DB::rollBack();
            // Retornar un mensaje de error o manejar la excepción
            return response()->json([
                'error' => 'Ocurrió un error al guardar los datos',
                'code' => $e->getCode(),
                'message' => $e->getMessage()
            ], 500);
        }
    }
    //APROBACION SERVICIO
    public function approveService(Request $req, $cpath, $id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('client_id', $client->id)
            ->where('id', $id)
            ->firstOrFail();

        DB::beginTransaction();
        try {
            ActionController::create($activity->id, ActionMedicalServiceSecondarySort::APROBAR, 'Aprobación del servicio');
            DB::commit();
        } catch (\Exception $e) {
            DB::rollback();
            return back()->with('error', 'No se pudo realizar la operacion');
        }
        return back()->with('success', 'Operación realizada con éxito');
    }

}