<?php

namespace App\Http\Controllers\Services;

use App\AccountingEntry;
use App\Action;
use App\Actions\ActionCotizacionSort;
use App\Actions\ActionGisSort;
use App\Actions\ActionPolicySortCollection;
use App\Actions\ActionPolizaSort;
use App\Activity;
use App\ActivityAction;
use App\Http\Controllers\Integrations\LogtailController;
use App\Mail\SendDocument;
use App\MailTemplates\Constants\Senders;
use App\PeIpSort;
use App\PeitInabilitySort;
use App\PeItSort;
use App\PeMptSort;
use App\PeRecognitionExpenses;
use App\PolicyAdditionalNotificationEmail;
use App\PolicyAddress;
use App\PolicyPhone;
use App\PremiumSurplus;
use App\QuotationConditionSpecial;
use App\ActivityActionDocument;
use App\ActivityDocument;
use App\Affiliate;
use App\Client;
use app\ConstancySort;
use App\EconomicActivity;
use App\GisSort;
use App\Holiday;
use App\Http\Controllers\ActionController;
use App\Http\Controllers\Controller;
use App\Http\Controllers\Integrations\IntegrationServiceController;
use App\Http\Controllers\Integrations\WebserviceAcselController;
use App\Http\Middleware\NumberToWords;
use App\Mail\SendDocumentDataBase;
use App\MailTemplates\Constants\Templates;
use App\MailTemplates\TemplateBuilder;
use App\PolicyCalendar;
use App\PolicyContact;
use App\PolicySort;
use App\PolicySortCollection;
use App\PolicySpreadsheet;
use App\MedicalServicesSort;
use App\PolicySpreadsheetAffiliate;
use App\Providers\AppServiceProvider;
use App\Quotation;
use App\Service;
use App\ServiceDocument;
use App\State;
use App\States\StatePoliza;
use App\States\StateReportePlanillaTomador;
use App\User;
use App\UserAuthorizedPolicies;
use App\UserClient;
use App\Utilities\Utilities;
use App\Video;
use Carbon\Carbon;
use DateTime;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;
use Maatwebsite\Excel\Facades\Excel;
use PDF;
use App\Http\Controllers\MassiveController;
use App\Http\Controllers\Tables\MailBoardController;

class PolicySortController extends Controller
{
    //Constantes
    private const PLANILLAS_PERMISSION_ID = 2;

    protected $errors2 = [];
    protected $alerts = [];

    protected $massiveController;

    public function __construct()
    {
        $this->middleware('auth')->except([
            'cronMonthlyReportSpreadsheetUpload',
            'reportFinalPayroll',
            'reportFinalMonthlyPayroll',
            'report_affiliates',
            'generateMonthlyPaymentCalculation',
            'actionIssuePolicy',
            'generateAccountingEntry',
            'generateCertificate',
            'generateCertificatePlanilla'
        ]);

        $this->massiveController = app()->make(MassiveController::class);
    }

    public function policySortData(Request $req, $cpath, $id)
    {

        $selectedActivityId = $req->query('idpoliza');
       
        return view('services.policy_sort.menu.policy_sort_data', [
            'id' => $id,
            'npoliza' => $selectedActivityId
        ]);
    }

    public function form(Request $req, $cpath, $id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();
        $activityDocument = ActivityDocument::where('activity_id', $activity->id)->get();

        $jsonSource = ($activity->policy_sort->economic_activity == 'public') ? '/js/economic_activity/public.json' : '/js/economic_activity/private.json';
        $json = file_get_contents(public_path($jsonSource));
        $economicActivities = json_decode($json, true);

        //Se transforma a una collección en laravel
        $activity_economic_name = collect($economicActivities)->firstWhere('CODE', $activity->policy_sort->activity_economic_id)['ACTIVITY_NAME'];

        $activity->policy_sort->economic_activity_name = $activity_economic_name;

        $calendarPeriods = AppServiceProvider::$CALENDAR_PERIOD;

        $condiciones = ActivityAction::where('action_id', '=', ActionCotizacionSort::REPORTAR_CONDICIONES_ESPECIALES)->where('activity_id', '=', $activity->parent->id)->first();

        $workSheetId = Activity::where('parent_id', $activity->id)
            ->where('service_id', Service::SERVICE_REPORT_TAKEN_FORM_MNK)
            ->whereIn('state_id', [StateReportePlanillaTomador::PLANILLA_REPORTADA, StateReportePlanillaTomador::CERTIFICADO_DE_TOMADOR_REPORTADO])
            ->pluck('id');

        //Planillas del tomador
        $workSheets = PolicySpreadsheet::whereIn('activity_id', $workSheetId)
            ->whereNull('deleted_at')
            ->get();

        if ($workSheets->isNotEmpty()) {
            //Seleccionar el último PolicySpreadsheet y capturar el path para la descarga de la planilla con pluck
            $pathFile = PolicySpreadsheet::where('activity_id', $workSheetId)
                ->orderBy('id', 'desc')
                ->value('file');

            //marcar planilla de emision
            foreach ($workSheets as $index => $workSheet) {
                $workSheet->emission = ($workSheet->id === $workSheets->first()->id) ? true : false;
            }
        }

        // Obtener las actividades de pagos
        $paymentActivity = Activity::where('parent_id', $activity->id)->get();

        // Extraer los ids de esas actividades
        $activityIds = $paymentActivity->pluck('id')->toArray();

        // Sumar la columna total_amount de los registros cuyo activity_id esté en $activityIds
        $firstPayment = PolicySortCollection::whereIn('activity_id', $activityIds)
            ->where('payment_status', PolicySortCollection::PAYMENT_STATUS_APPROVED)
            ->where('type_receipt', PolicySortCollection::EMISSION)
            ->first();

        $lastPayment = PolicySortCollection::whereIn('activity_id', $activityIds)
            ->where('payment_status', PolicySortCollection::PAYMENT_STATUS_APPROVED)
            ->orderBy('id', 'desc')
            ->first();

        // Sumar la columna total_amount de los registros cuyo activity_id esté en $activityIds
        $totalAmount = PolicySortCollection::whereIn('activity_id', $activityIds)
            ->where('payment_status', PolicySortCollection::PAYMENT_STATUS_APPROVED)
            ->sum('total_amount');

        $totalAmountCount = PolicySortCollection::whereIn('activity_id', $activityIds)
            ->where('payment_status', PolicySortCollection::PAYMENT_STATUS_APPROVED)
            ->count();

        $payments = PolicySortCollection::whereIn('activity_id', $activityIds)
            ->where('payment_status', PolicySortCollection::PAYMENT_STATUS_APPROVED)
            ->get();

        $signature_document_fisica = $activity->activity_documents->where('document_id', ServiceDocument::SOLICITUD_EMISIÓN_SORT_FIRMA_FISICA)
            ->first();

        $signature_document_digital = $activity->activity_documents->where('document_id', ServiceDocument::SOLICITUD_EMISIÓN_SORT_FIRMA_DIGITAL)
            ->first();

        //Obtenemos los contactos asociados a la poliza
        $policyContacts = PolicyContact::where('policy_sort_id', $activity->policy_sort->id)
            ->orderByRaw('position IS NULL, position DESC')
            ->get();
        //Obtenemos las direcciones adicionales asociadas a la poliza
        $policyAddresses = PolicyAddress::where('policy_sort_id', $activity->policy_sort->id)->get();
        //Obtenemos los telefonos adicionales asociadas a la poliza
        $policyPhones = PolicyPhone::where('policy_sort_id', $activity->policy_sort->id)->get();
        //Obtenemos los correos de notificiones adicionales asociados a la poliza
        $policyAdditionalNotificationEmails = PolicyAdditionalNotificationEmail::where('policy_sort_id', $activity->policy_sort->id)->get();

        $gis_policy = Activity::with('gis_sort')
            ->where('parent_id', $activity->id)
            ->where('service_id', Service::SERVICE_GIS_SORT_MNK)
            ->get();

        $planilla = Activity::where('parent_id', $id)
            ->where('service_id', Service::SERVICE_REPORT_TAKEN_FORM_MNK)
            ->first();

        if ($planilla) {
            $activity->policy_sort->payroll_status = 'Reportada';
        } else {
            $activity->policy_sort->payroll_status = 'Pendiente de cargue';
        }

        //monto de la prima
        switch ($activity->policy_sort->periodicity) {
            case 1: // Anual
                $calculatedAmount = $activity->policy_sort->annual_calculation_amount * 1;
                break;
            case 2: // Semestral
                $calculatedAmount = $activity->policy_sort->semiannual_calculation_amount * 2;
                break;
            case 3: // Trimestral
                $calculatedAmount = $activity->policy_sort->quarterly_calculation_amount * 4;
                break;
            case 4: // Mensual
                $calculatedAmount = $activity->policy_sort->monthly_calculation_amount * 12;
                break;
            default:
                $calculatedAmount = $activity->policy_sort->annual_calculation_amount;
                break;
        }


        $activity->save();

        $costoSiniestro = 0;
        $cantSiniestro = 0;

        if ($activity->policy_sort->consecutive) {
            $accounting = AccountingEntry::selectRaw('SUM(debit) AS costo, COUNT(DISTINCT(activity_gis)) AS cantidad')
                ->whereRaw("TRIM(LEADING '0' FROM REPLACE(number_policy, 'SORT-', '')) = ?", $activity->policy_sort->consecutive)
                ->where('cod_oper', '011')
                ->where('debit', '>', 0)
                ->first();

            $costoSiniestro = $accounting->costo;
            $cantSiniestro = $accounting->cantidad;
        }

        //consultar la última planilla asociada a la póliza
        $activitySpreadsheet = Activity::where("parent_id", $activity->id)
            ->where("service_id", Service::SERVICE_REPORT_TAKEN_FORM_MNK)
            ->whereIn("state_id", [StatePoliza::PLANILLA_REPORTADA, StatePoliza::CERTIFICADO_REPORTADO_TOMADOR])
            ->orderBy('created_at', 'desc')
            ->first();

        //capturar el número de trabajadores de la planilla
        $number_workers_spreadsheet = null; // nulo para indicar que no hay una relación directa con póliza
        if ($activitySpreadsheet) {
            $number_workers_spreadsheet = PolicySpreadsheet::where('activity_id', $activitySpreadsheet->id)
                ->pluck('total_affiliates')->first();
        }

        $reportPayroll = 1;
        $modality = '';

        switch ($activity->policy_sort->work_modality_id) {
            case 1:
                //valida si en la ultima planilla solo tiene a tomador
                $totalAffiliates = $activitySpreadsheet->policy_spreadsheets->total_affiliates ?? 0;

                $reportPayroll = 1;
                $modality = '';

                if ($totalAffiliates == 1) {
                    $reportPayroll = 0;
                    $modality = 'Riesgo de trabajo general (único asegurado) ';
                }

                break;
            case 2:
                $reportPayroll = 0;
                $modality = 'Riesgos del trabajo especial formación técnica dual';
                break;
            case 3:
                $reportPayroll = 0;
                $modality = 'Riesgos del trabajo hogar';
                break;
            case 4:
                $reportPayroll = 0;
                $modality = 'Riesgos del trabajo ocasional';
                break;
            case 5:

                break;
            case 6:
                $reportPayroll = 0;
                $modality = 'Riesgos del trabajo independiente';
                break;
            case 7:
                $reportPayroll = 0;
                $modality = 'Riesgos del trabajo único trabajador';
                break;
            default:
                $reportPayroll = 1;
                $modality = '';
                break;
        }

        $view = false;
        if (str_contains($req->path(), 'view')) {
            $view = true;
        }

        $economicActivity = EconomicActivity::where('code', $activity->policy_sort->activity_economic_id)->first();


        return view('services.policy_sort.form.form', [
            'activity' => $activity,
            'document1' => $activityDocument->where('document_id', 1)->first(),
            'document2' => $activityDocument->where('document_id', 2)->first(),
            'document3' => $activityDocument->where('document_id', 3)->first(),
            'document4' => $activityDocument->where('document_id', 4)->first(),
            'document5' => $activityDocument->where('document_id', 9)->first(),
            'signature_document_fisica' => $signature_document_fisica,
            'signature_document_digital' => $signature_document_digital,
            'id' => $id,
            'policy_sort' => $activity->policy_sort,
            'creditsPayments' => [],
            'creditsCount' => 0,
            'affiliate' => $activity->affiliate,
            'calendarPeriods' => $calendarPeriods,
            'workSheets' => $workSheets,
            'first_workSheet' => optional($workSheets)->first() ?? null,
            'firstPayment' => $firstPayment,
            'totalAmount' => $totalAmount,
            'totalAmountCount' => $totalAmountCount,
            'payments' => $payments,
            'policy_contacts' => $policyContacts,
            'policy_addresses' => $policyAddresses,
            'policy_phones' => $policyPhones,
            'policy_additional_notification_emails' => $policyAdditionalNotificationEmails,
            'lastPayment' => $lastPayment,
            'gis_policy' => $gis_policy,
            'pathFile' => $pathFile ?? null,
            'calculatedAmount' => $calculatedAmount ?? null,
            'costoSiniestro' => $costoSiniestro,
            'cantSiniestro' => $cantSiniestro,
            'number_workers_spreadsheet' => !is_null($number_workers_spreadsheet) ? $number_workers_spreadsheet : 'No hay trabajadores', // cuando no hay trabajadores puede ser porque no se encontró una planilla asociada a la póliza
            'number_workers' => $activity->policy_sort->number_workers_optional,
            'reportPayroll' => $reportPayroll,
            'modality' => $modality,
            'view' => $view,
            'condiciones' => $condiciones ? 'SI' : 'NO',
            'economicActivity' => $economicActivity
        ]);
    }

    //Cargue Masivo de PLANILLA de afilidos
    public function storeAffiliatesMassively(Request $request, $cpath)
    {

        DB::beginTransaction();
        try {

            $activityIds = Activity::where('service_id', Service::SERVICE_REPORT_TAKEN_FORM_MNK)
                ->where('parent_id', $request->activity_policy_id)
                ->pluck('id');

            // Luego filtramos policy_spreadsheets
            $spreadSheetByPolicy = PolicySpreadsheet::whereIn('activity_id', $activityIds)->get();
            $countPolicySpreadSheet = $spreadSheetByPolicy->count();
            $thereArePreviouslyCreatedPolicies = false;
            $dateNow = Carbon::now()->format('Y-m-d');
            if($countPolicySpreadSheet > 0) {
                foreach($spreadSheetByPolicy as $spreadSheet) {
                    $createdAt = Carbon::parse($spreadSheet->created_at)->format('Y-m-d');
                    if($createdAt === $dateNow) {
                        $thereArePreviouslyCreatedPolicies = true;
                        break;
                    }
                }

                if($thereArePreviouslyCreatedPolicies) {
                    $countPolicySpreadSheet = 0;
                }
            }

            $activityPolicy = Activity::where('id', $request->activity_policy_id)->first();

            if (
                    $activityPolicy->state_id == StatePoliza::POLIZA_SUSPENDIDA ||
                    $activityPolicy->state_id == StatePoliza::TRAMITE_ANULADO
                ) {

                return response()->json([
                    "status" => false,
                    "message" => "No puede cargar planilla porque la póliza está en estado suspendida o anulada"
                ]);
            } else {
                $activitySpreadsheet = Activity::where('parent_id', $request->activity_policy_id)
                        ->where('service_id', Service::SERVICE_REPORT_TAKEN_FORM_MNK)
                        ->where('state_id', StateReportePlanillaTomador::CERTIFICADO_DE_TOMADOR_REPORTADO)
                        ->first();

                //valida que la fecha en la que se va a crear el servico no sea menor a la vigencia de la poliza
                if ($activitySpreadsheet && Carbon::now()->lt($activityPolicy->policy_sort->validity_from)) {
                    return response()->json([
                        "status" => false,
                        "message" => "No puede cargar la planilla porque la fecha de carga es menor a la fecha de inicio de la vigencia de la póliza"
                    ]);
                }
            }

            //Validaciones para el archivo EXCEL cargado
            $request->validate([
                'affiliate_file' => 'required|file|mimes:xlsx|max:5120',
            ], [
                'affiliate_file.max' => 'El archivo no debe superar los 5 MB.',
            ]);

            //Variable para saber desde que vista viene si TOMADOR O INTERMEDIARIO
            $isTaker = $request->taker;

            //Obtenemos el cliente
            $client = Client::where('path', $cpath)->firstOrFail();

            //Obtenemos el archivo excel con los datos a procesar
            $file = $request->file('affiliate_file');

            //Obtenemos el path de S3
            $lpath = Utilities::putFileToAwsS3($file);

            $total = [
                'registros_guardados' => 0,
                'salario_total' => 0,
                'type' => "Auto"
            ];

            //creamos o devolvemos la actividad del servicio REPORTE PLANILLA TOMADOR
            $activityService = $this->updateCreateActivityService($request->activity_policy_id, $client);

            //creamos o actualizamos el servicio REPORTE PLANILLA TOMADOR
            $policySpreadsheet = $this->updateCreatePolicySpreadsheet($activityService, $lpath, 'Masiva');

            $totalSalaries = 0;
            $rowNumber = 2;
            $totalAffiliate = 0;

            // Procesamos el archivo Excel
            $excel = Excel::load($request->file('affiliate_file')->path());

            // Definir las columnas esperadas
            $expectedColumns = [
                'ti',
                'nacionalidad',
                'no_identificacion',
                'nombres',
                'apellidos',
                'fecha_nacimiento',
                'sexo',
                'tipo_jornada',
                'correo_electronico',
                'salario_mensual',
                'horas',
                'dias',
                'ocupacion',
                'observacion',
            ];


            // Tomar los encabezados de las columnas y verificar si son las esperadas
            $excelRaw = Excel::load($request->file('affiliate_file')->getPathname(), function($reader) {
                $reader->noHeading(); // Para que no normalice ni use los headers como claves
            });
            // Obtener la primera hoja del archivo Excel
            $sheetRaw = $excelRaw->getSheet(0);
            $rawHeaderRow = [];
            // Iterar sobre la primera fila (índice 1) usando el RowIterator
            // getRowIterator(1, 1) indica que solo queremos iterar sobre la fila 1
            foreach ($sheetRaw->getRowIterator(1,1) as $row) {
                // Obtenemos el iterador de celdas para esa fila
                $cellIterator = $row->getCellIterator();
                // Le decimos al iterador que recorra todas las celdas, incluso las vacías
                $cellIterator->setIterateOnlyExistingCells(false);

                // Iterar sobre todas las celdas de la fila
                foreach ($cellIterator as $cell) {
                    // Usamos getFormattedValue() para obtener el valor formateado de la celda
                    // Esto asegura que obtendremos el valor visual, incluso si es una fecha o un número con formato
                    // El valor se limpia usando trim() para eliminar espacios innecesarios
                    $rawHeaderRow[] = trim($cell->getFormattedValue());
                }
            }

            // Normalizar manualmente (convertir a minúsculas)
            $actualColumns = array_map(function($value) {
                return strtolower($value);
            }, $rawHeaderRow);
            // //Verificar si todas las columnas esperadas están presentes
            $missingColumns = array_diff($expectedColumns, $actualColumns);

            if (!empty($missingColumns)) {
                // Convertir a mayúsculas
                $missingColumns = array_map('strtoupper', $missingColumns);
                return response()->json([
                    "status" => false,
                    "message" => "El archivo no puede ser procesado; falta(n) la(s) columna(s). \n" . implode(', ', $missingColumns)
                ]);
            }

            // Procesar todos los registros
            $rows = $excel->toArray();
            $allErrors = [];
            $alerts = [];
            $totalSalaries = 0;
            $processedRecords = [];

            $activityPolicicyQuotation = Activity::where('id', $request->activity_policy_id)->first();

            $quotation = Quotation::where('activity_id', $activityPolicicyQuotation->parent_id)->first();

            //Actualizamos el estatus de planilla de la poliza
            $policySort = PolicySort::where('id', $request->activity_policy_id)->first();
            if (!empty($policySort)) {
                $policySort->payroll_status = 'Reportada';
                $policySort->save();
            }

            //se recorre el array de atras hacia adelante para saber el ultimo campo con valores
            $lastValidRowIndex = -1;
            for ($i = count($rows) - 1; $i >= 0; $i--) {
                if (!empty(array_filter($rows[$i]))) {
                    $lastValidRowIndex = $i;
                    break;
                }
            }

            foreach ($rows as $index => $row) {
                //eliminar filas vacias
                if (empty(array_filter($row))) {
                    // Si la fila vacía está antes de la última fila válida, es un error porque esta en medio
                    if ($index < $lastValidRowIndex) {
                        $allErrors[] = "La fila {$rowNumber} está completamente vacía.";
                    }
                    $rowNumber++;
                    continue;
                }
                // Procesa la fila y lanza excepción si hay errores
                $errors = $this->processAffiliateSpreadsheetMassively($row, $rowNumber, $totalSalaries, $processedRecords, $quotation, $totalAffiliate);
                $allErrors = array_merge($allErrors, $errors);
                $rowNumber++;
            }


            // Verifica si hay errores acumulados después de procesar todas las filas
            if (!empty($allErrors)) {
                throw new \Exception(', ' . implode(", ", $allErrors));
            }

            // Guardar los totales en el modelo
            $policySpreadsheet->total_affiliates = $totalAffiliate;
            $policySpreadsheet->total_salaries = $totalSalaries;
            $policySpreadsheet->file = $lpath;
            $policySpreadsheet->file_txt = null;
            $policySpreadsheet->observacion = $request->observacion;
            $policySpreadsheet->save();

            if (!empty($policySort)) {
                //guardar número de trabajadores en la poliza
                $policySort->update(["number_workers_optional" => $policySpreadsheet->total_affiliates]);
            }

            //Contamos los afiliados guardados y el total de salarios
            $affiliateData = PolicySpreadsheet::where('id', $policySpreadsheet->id)
                ->first();

            //Si viene desde la vista del TOMADOR ejecutamos la ACCIÖN REPORTAR PLANILLA TOMADOR
            if ($isTaker !== null) {

                $this->actionReportTakerSheet($activityService, $policySpreadsheet->id);

                //Enviamos correo de notificación al TOMADOR
                $this->notificationTaker($request, $activityService);
                // $this->affiliatePayrollReport($request, $request->activity_policy_id, $client);
            } else {
                //si es Vista intermediario ejecutamos la ACCION REPORTAR PLANILLA INICIAL
                $this->actionReportInitialWorksheets($client, $request->activity_policy_id, $activityService, $lpath, $policySpreadsheet->id);
            }

            DB::commit();

            //Ejecutamos la función para crear o actualizar los afiliados en segundo plano 
            $res = $this->sendUploadSpreadsheet($policySpreadsheet->id);

            return response()->json([
                'message' => 'Archivo procesado correctamente.',
                'alerts' => $alerts,
                'id' => $affiliateData->id,
                'total' => [
                    'type' => "Auto",
                    'registros_guardados' => $affiliateData->total_affiliates,
                    'salario_total' => number_format($affiliateData->total_salaries, 2, ',', '.')
                ],
                'policy_spreadsheet' => $policySpreadsheet,
                'countPolicySpreadSheet' => $countPolicySpreadSheet
            ]);
        } catch (ValidationException $e) {
            DB::rollback();
            return response()->json([
                'message' => 'Error de validación',
                'e' => 'El archivo no cumple con la estructura requerida, es diferente a xlsx o es superior a 5MB',
                'countPolicySpreadSheet' => null
            ], 422);
        } catch (\Exception $e) {
            DB::rollback();

            $mensajeDefaul = "El archivo de Excel cargado no tiene la estructura correcta debe ser xlxs, por favor revíselo y vuelva a realizar la carga";

            return response()->json([
                'message' => 'Errores durante el procesamiento del cargue automático:',
                'e' => (strpos($e->getMessage(), 'Undefined') !== false) ? $mensajeDefaul : $e->getMessage(),
                'total' => $total,
                'countPolicySpreadSheet' => null
            ], 500);
        }
    }

    //Funciones para detectar el numero de empleados cargador en la planilla 
    function loadedEmployes(Request $req, $id)
    {

        try {
            //Buscamos la el encabezado del reporte de planilla
            $spreadsheet = PolicySpreadsheet::find($req->id);

            //calculamos el porcentaje de avance de carga de los afiliados
            if (isset($spreadsheet->total_affiliate_uploaded) && isset($spreadsheet->total_affiliates)) {
                $percentaje = ($spreadsheet->total_affiliate_uploaded / $spreadsheet->total_affiliates) * 100;

                return response()->json([
                    'percentaje' => $percentaje,
                    'success' => true
                ]);
            } else {

                //Si no se puede realizar el calculo devolvemo el avance en 0
                return response()->json([
                    'percentaje' => null,
                    'success' => false
                ]);
            }
        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'message' => 'Errores durante el procesamiento del cargue automatico',
                'e' => $e->getMessage(),
            ], 500);
        }
    }

    //Guardar Planilla MANUAL vista intermediario 
    public function storeAffiliatesManually(Request $request, $cpath)
    {
        //Recibimos todos los datos de los afiliados
        $request->validate([
            'tableData' => 'required|array',
        ]);

        //Buscamos el cliente por el cpath
        $client = Client::where('path', $cpath)
            ->firstOrFail();

        //Variable para saber desde que vista viene si TOMADOR O INTERMEDIARIO
        $isTaker = $request->taker;

        //Variable global para el total de sueldos y afiliados
        $total = [
            'registros_guardados' => 0,
            'salario_total' => 0,
            'type' => "Manual"
        ];

        $activityIds = Activity::where('service_id', Service::SERVICE_REPORT_TAKEN_FORM_MNK)
            ->where('parent_id', $request->activity_policy_id)
            ->pluck('id');

        // Luego filtramos policy_spreadsheets
        $spreadSheetByPolicy = PolicySpreadsheet::whereIn('activity_id', $activityIds)->get();
        $countPolicySpreadSheet = $spreadSheetByPolicy->count();
        $thereArePreviouslyCreatedPolicies = false;
        $dateNow = Carbon::now()->format('Y-m-d');
        if($countPolicySpreadSheet > 0) {
            foreach($spreadSheetByPolicy as $spreadSheet) {
                $createdAt = Carbon::parse($spreadSheet->created_at)->format('Y-m-d');

                if($createdAt === $dateNow) {
                    $thereArePreviouslyCreatedPolicies = true;
                    break;
                }
            }

            if($thereArePreviouslyCreatedPolicies) {
                $countPolicySpreadSheet = 0;
            }
        }

        DB::beginTransaction();

        try {


            $activityPolicicyQuotation = Activity::where('id', $request->activity_policy_id)->first();

            if (
                $activityPolicicyQuotation->state_id == StatePoliza::POLIZA_SUSPENDIDA ||
                $activityPolicicyQuotation->state_id == StatePoliza::TRAMITE_ANULADO
            ) {

                return response()->json([
                    "status" => false,
                    "message" => "No puede cargar planilla porque la póliza está en estado suspendida o anulada",
                    'total' => 0,
                ],500);
            } else {
                $activitySpreadsheet = Activity::where('parent_id', $request->activity_policy_id)
                    ->where('service_id', Service::SERVICE_REPORT_TAKEN_FORM_MNK)
                    ->where('state_id', StateReportePlanillaTomador::CERTIFICADO_DE_TOMADOR_REPORTADO)
                    ->first();

                //valida que la fecha en la que se va a crear el servico no sea menor a la vigencia de la poliza
                if ($activitySpreadsheet && Carbon::now()->lt($activityPolicicyQuotation->policy_sort->validity_from)) {
                    return response()->json([
                        "status" => false,
                        "message" => "No puede cargar la planilla porque la fecha de carga es menor a la fecha de inicio de la vigencia de la póliza",
                        'total' => 0,
                    ],500);
                }
            }


            $quotation = Quotation::where('activity_id', $activityPolicicyQuotation->parent_id)->first();

            //Actualizamos el estatus de planilla de la poliza
            $policySort = PolicySort::where('id', $request->activity_policy_id)->first();
            if (!empty($policySort)) {
                $policySort->payroll_status = 'Reportada';
                $policySort->save();
            }

            //creamos o devolvemos la actividad del servicio REPORTE PLANILLA TOMADOR
            $activityService = $this->updateCreateActivityService($request->activity_policy_id, $client);

            //creamos o actualizamos el servicio REPORTE PLANILLA TOMADOR
            $policySpreadsheet = $this->updateCreatePolicySpreadsheet($activityService,null, 'Manual');

            $processedRecords = [];

            $rowNumber = 2;

            foreach ($request->tableData as $row) {
                // Procesar los datos de la tabla
                $this->processAffiliateSpreadsheet($row, $policySpreadsheet->id, $errors, $alerts, $client, $total, $processedRecords, $rowNumber, $quotation, $activityPolicicyQuotation);
            }


            if (!empty($errors)) {
                throw new \Exception(', ' . implode(", ", $errors));
            }

            //Contamos los afiliados guardados y el total de salarios
            $affiliateData = PolicySpreadsheetAffiliate::where('policy_spreadsheet_id', $policySpreadsheet->id)
                ->selectRaw('COUNT(*) as registros_guardados, SUM(monthly_salary) as salario_total')
                ->first();


            //Guardamos los valores
            $policySpreadsheet->total_affiliates = $affiliateData->registros_guardados;
            $policySpreadsheet->total_salaries = $affiliateData->salario_total;
            $policySpreadsheet->observacion = $request->observacion;
            $policySpreadsheet->save();

            if (!empty($policySort)) {
                //guardar número de trabajadores en la poliza
                $policySort->update(["number_workers_optional" => $policySpreadsheet->total_affiliates]);
            }

            //Si viene desde la vista del TOMADOR ejecutamos la ACCIÖN REPORTAR PLANILLA TOMADOR
            if ($isTaker !== null) {

                //Ejecutamos la acción del reporte planilla tomador
                $this->actionReportTakerSheet($activityService, $policySpreadsheet->id);

                //Enviamos correo de notificación al TOMADOR
                $this->notificationTaker($request, $activityService);

                // $this->affiliatePayrollReport($request, $request->activity_policy_id, $client);
            } else {
                //si es Vista intermediario ejecutamos la ACCION REPORTAR PLANILLA INICIAL
                $this->actionReportInitialWorksheets($client, $request->activity_policy_id, $activityService, $lpath = null, $policySpreadsheet->id);
            }


            DB::commit();

            // Preparamos la respuesta
            return response()->json([
                'message' => 'Archivo procesado correctamente.',
                // Devolvemos afiliados con el total de salarios formateado
                'total' => [
                    'registros_guardados' => $affiliateData->registros_guardados,
                    'salario_total' => number_format($affiliateData->salario_total, 2, ',', '.')
                ],
                'countPolicySpreadSheet' => $countPolicySpreadSheet
            ]);
        } catch (\Exception $e) {

            DB::rollback();
            return response()->json([
                'message' => 'Errores durante el procesamiento del cargue manual',
                // 'errors' => $errors,
                'e' => $e->getMessage(),
                'total' => $total,
                'countPolicySpreadSheet' => null
            ], 500);
        }
    }

    //Creamos o devolvemos la actividad del servicio REPORTE PLANILLA TOMADOR
    public function updateCreateActivityService($policySortActivityId, $client)
    {
        //Buscamos la actividad por medio de la actividad de la poliza
        $activity_policy = Activity::find($policySortActivityId);

        // Busco si existe planilla en estado REGISTRADA y REPORTE PLANILLA - TOMADOR
        $template_activity = Activity::where('service_id', Service::SERVICE_REPORT_TAKEN_FORM_MNK)
            ->whereIn('state_id', [State::PLANILLA_REPORTADA, StateReportePlanillaTomador::PLANILLA_TEMPORAL])
            ->where('parent_id', $policySortActivityId)
            ->first();

        if ($template_activity) {
            $template_activity->created_at = Carbon::now();
            $template_activity->save();
            return $template_activity;
        }

        //Si no existe actividad asociada creamos la actividad para el servicio Reporte Planilla tomador
        $new_template_activity = new Activity();
        $new_template_activity->client_id = $client->id;
        $new_template_activity->parent_id = $policySortActivityId;
        $new_template_activity->service_id = Service::SERVICE_REPORT_TAKEN_FORM_MNK;
        $new_template_activity->affiliate_id = $activity_policy->affiliate_id;
        $new_template_activity->user_id = Auth::id();
        $new_template_activity->state_id = State::REGISTRADO;
        $new_template_activity->save();

        return $new_template_activity;
    }

    //Creamos a actualizamos el SERVICIO REPORTE PLANILLA TOMADOR
    public function updateCreatePolicySpreadsheet($activityService, $path = null, $entryType = null)
    {
        // Busco si existen varias planillas ya registradas con ese Service plantilla
        $policySpreadsheet = PolicySpreadsheet::where('activity_id', $activityService->id)
            ->first();

        // Si existe registro PolicySpreadsheet asociado a la activity
        if ($policySpreadsheet) {
            // Elimino todos los registros asociados a la planilla menos los temporales
            $policySpreadsheet->policy_spreadsheet_affiliate()
                ->where('temporal', false)
                ->delete();
        } else {
            // Si no existe, crea una nueva PolicySpreadsheet
            $policySpreadsheet = new PolicySpreadsheet();
            $policySpreadsheet->activity_id = $activityService->id;
        }
        //Consulto cuantas planillas tiene la poliza
        $countSpreadsheet = Activity::where('parent_id',$activityService->parent_id )
            ->where('service_id', Service::SERVICE_REPORT_TAKEN_FORM_MNK)
            ->count();

        if($countSpreadsheet == 1){
            $policySpreadsheet->entry_type = 'Emisión';
        }elseif(!is_null($entryType)) {
            $policySpreadsheet->entry_type = $entryType;
        }
        $policySpreadsheet->save();
        // Retorno PolicySpreadsheet
        return $policySpreadsheet;
    }

    //Accion desde del intermediario REPORTAR PLANILLA INICIAL
    public function actionReportInitialWorksheets($client, $policySortActivityId, $activityService, $lpath, $policySpreadSheetId)
    {
        // Busco la actividad de la poliza 
        $activity = Activity::where('client_id', $client->id)
            ->where('id', $policySortActivityId)
            ->first();

        //Verificamos si ya se ejecuto la acción REPORTAR_PLANILLA_INICIAL
        $existAction = $activity->activity_actions->where('action_id', Action::REPORTAR_PLANILLA_INICIAL)
            ->first();

        $activityActionPolicy = "";

        if ($existAction) {
            //Ejecutamos la acción REPORTAR SEGUIMIENTO
            $description = 'Se reporto seguimiento sobre la acción  reportar planilla inicial en poliza sort';
            $activityActionPolicy = ActionController::create($activity->id, Action::REPORTAR_SEGUIMIENTO, $description);
        } else {
            //Ejecutamos la acción REPORTAR_PLANILLA_INICIAL
            $description = 'Planilla inicial reportada';
            $activityActionPolicy = ActionController::create($activity->id, Action::REPORTAR_PLANILLA_INICIAL, $description);
        }

        //Verificamos si ya se ejecuto la acción REPORTAR_PLANILLA_TOMADOR
        $existActiontaker = $activityService->activity_actions->where('action_id', Action::REPORTAR_PLANILLA_TOMADOR)
            ->first();

        $activityActionTaker = "";

        if ($existActiontaker) {
            //Ejecutamos la acción REPORTAR SEGUIMIENTO
            $description = 'Se reporto seguimiento sobre la acción planilla inicial reportada en reporte planilla tomador';
            $activityActionTaker = ActionController::create($activityService->id, Action::REPORTAR_SEGUIMIENTO_PLANILLA_TOMADOR, $description);
        } else {
            //Ejecuto la acción REPORTAR PLANILLA TOMADOR en el servicio REPORTAR PLANILLA TOMADOR
            $description = "Planilla inicial reportada";
            $activityActionTaker = ActionController::create($activityService->id, Action::REPORTAR_PLANILLA_TOMADOR, $description);
        }
    }

    //ACCIÖN Reportar planilla Tomador desde la vista tomador
    public function actionReportTakerSheet($activityService, $policySpreadSheetId)
    {

        //Ejecutamos la acción REPORTAR PLANILLA TOMADOR 
        $description = 'Planilla tomador reportada';
        $activityActionTaker = ActionController::create(
            $activityService->id,
            Action::REPORTAR_PLANILLA_TOMADOR,
            $description
        );

        // $this->generateExcelTemplateAutomatic($policySpreadSheetId, $activityActionTaker);
    }

    //Generamos pdf de las planilla mediante los registros excel
    public function generateExcelTemplateAutomatic($policySpreadSheetId, $activityActionTaker)
    {
        // Buscamos todos los afiliados de la planilla
        $data = PolicySpreadsheet::with([
            'policy_spreadsheet_affiliate' => function ($query) {
                $query->where('temporal', 0);
            },
            'activity'
        ])
            ->where('id', $policySpreadSheetId)
            ->first();

        $numeroTotalPoliza = PolicySpreadsheet::where('activity_id', $data->activity_id)->count();

        $policy = Activity::where('id', $data->activity->parent_id)->first();

        $jsonPath = public_path('js/paises.json');
        $jsonContent = File::get($jsonPath);
        $countries = json_decode($jsonContent, true);


        foreach ($data->policy_spreadsheet_affiliate as &$employee) {
            $employee['nationality'] = $this->getCountryName($employee['nationality'], $countries);
        }

        $fileName2 = 'Planilla_' . $activityActionTaker->id;

        $pdf = PDF::loadView(
            'services.policy_sort.docs.planilla_dinamic',
            [
                'watermark' => false,
                'data' => $data,
                'policy' => $policy,
                'total' => $numeroTotalPoliza
            ]
        );

        $pdf->setPaper('A4', 'landscape');

        Storage::disk('s3')->put("activity_action_document/{$fileName2}.pdf", $pdf->output());

        $activityActionDocumentTaker = new ActivityActionDocument();
        $activityActionDocumentTaker->activity_action_id = $activityActionTaker->id;
        $activityActionDocumentTaker->name = "Reporte planilla_" . $activityActionTaker->id;
        $activityActionDocumentTaker->path = 'activity_action_document/' . $fileName2 . '.pdf';
        $activityActionDocumentTaker->save();
    }

    function getCountryName($code, $countries)
    {
        foreach ($countries as $country) {
            if ($country['country_short_name'] === $code) {
                return $country['country_name'];
            }
        }
        return 'Desconocido'; // Valor por defecto si no se encuentra el país
    }

    public function generateExcelTemplateManual(Request $req, $policySpreadSheetId)
    {
        // Buscamos todos los afiliados de la planilla
        $data = PolicySpreadsheet::with([
            'policy_spreadsheet_affiliate' => function ($query) {
                $query->where('temporal', 0);
            },
            'activity'
        ])
            ->where('id', $req->id)->first();

        $numeroTotalPoliza = PolicySpreadsheet::where('activity_id', $data->activity_id)->count();

        $policy = Activity::where('id', $data->activity->parent_id)->first();

        $jsonPath = public_path('js/paises.json');
        $jsonContent = File::get($jsonPath);
        $countries = json_decode($jsonContent, true);


        foreach ($data->policy_spreadsheet_affiliate as &$employee) {
            $employee['nationality'] = $this->getCountryName($employee['nationality'], $countries);
            $employee['first_name'] = $this->formatText($employee['first_name']);
            $employee['last_name'] = $this->formatText($employee['last_name']);
            $employee['email'] = $this->formatText($employee['email']);
            $employee['occupation'] = $this->formatText($employee['occupation']);
        }


        // Nombre del archivo temporal
        $fileName1 = 'Planilla_' . $req->id;


        $pdf = PDF::loadView(
            'services.policy_sort.docs.planilla_dinamic',
            [
                'watermark' => false,
                'data' => $data,
                'policy' => $policy,
                'total' => $numeroTotalPoliza
            ]
        );


        $pdf->setPaper('A4', 'landscape');

        Storage::disk('s3')->put("activity_action_document/{$fileName1}.pdf", $pdf->output());

        return $pdf->download('planilla.pdf');
    }

    public function generateReportSpreadsheet(Request $req)
    {

        try {

            $data = PolicySpreadsheet::where('id', $req->id)->firstOrFail();
            $policy = Activity::where('id', $data->activity->parent_id)->first();

            $primerPlanilla = Activity::where('parent_id', $data->activity->parent_id)
                ->where('service_id', Service::SERVICE_REPORT_TAKEN_FORM_MNK)->orderBy('id')->first();

            $tipo = $primerPlanilla->id == $data->activity_id ? 'Emisión' : 'Mensual';

            $fileName1 = 'ResumenPlanilla_' . $policy->policy_sort->formatSortNumber() . '_' . $data->created_at;

            $createdAt = Carbon::parse($data->created_at);
            $previousMonth = $createdAt->subMonth();
            $firstDayOfPreviousMonth = $previousMonth->copy()->firstOfMonth();
            $lastDayOfPreviousMonth = $previousMonth->copy()->lastOfMonth();

            $pdf = PDF::loadView(
                'services.policy_sort.docs.report_planilla',
                [
                    'watermark' => false,
                    'data' => $data,
                    'policy' => $policy,
                    'tipo' => $tipo,
                    'primerPlanilla' => $primerPlanilla,
                    'firstDayOfPreviousMonth' => $firstDayOfPreviousMonth->format('d/m/Y'),
                    'lastDayOfPreviousMonth' => $lastDayOfPreviousMonth->format('d/m/Y')
                ]
            );

            Storage::disk('s3')->put("activity_action_document/{$fileName1}.pdf", $pdf->output());

            //return $pdf->download($fileName1.'.pdf');

            return response($pdf->output(), 200)
                ->header('Content-Type', 'application/pdf')
                ->header('Content-Disposition', 'attachment; filename="' . $fileName1 . '.pdf"');


        } catch (exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Ocurrió un error al generar el reporte de planilla.',
                'details' => $e->getMessage(),
            ], 500);
        }
    }


    private function formatText($text)
    {
        return ucwords(mb_strtolower($text, 'UTF-8'));
    }


    //Enviar correo electrónico al Tomador 
    public function notificationTaker($request, $activityTaker)
    {
        $intermediary_email = "";
        $taker_email = "";

        //Datos del afiliado
        $affiliate = Affiliate::where('id', $activityTaker->affiliate_id)->first();

        //Datos de la póliza
        $dataPolicy = PolicySort::with('activity')
            ->where('activity_id', $activityTaker->parent_id)->first();

        // Obtener los correos de los usuarios tomadores autorizados usando el nuevo metodo
        $takerData = collect();
        if ($activityTaker) {
            $idActivityTaker = $activityTaker->parent_id;
            $takerData = $this->getAuthorizedTakerEmails($idActivityTaker);
        }

        $emailUsersTakerAuthorized = $takerData->pluck('email');


        $planillas = Activity::where('parent_id', $activityTaker->parent_id)
            ->where('service_id', Service::SERVICE_REPORT_TAKEN_FORM_MNK)
            ->get();

        if ($planillas->count() > 1) {
            // 1. Email del intermediador
            if ($dataPolicy && !empty($dataPolicy->email)) {
                $intermediary_email = $dataPolicy->email;
            }

            // 2. Email del tomador
            // 3. Proceso de construccion del envio del email
            // Agrega el email principal de notificaciones y todos los adicionales
            $emails = $this->getAdditionalNotificationEmails(
                $dataPolicy->id,
                [
                    $intermediary_email,
                ]
            );

            // Combina los emails iniciales con los emails de los usuarios autorizados
            $allEmails = collect($emails)
                ->merge($emailUsersTakerAuthorized)
                ->filter(function ($email) {
                    return !empty($email);
                })
                ->unique()
                ->values();

            $finalEmailsArray = $allEmails->toArray();

            // se valida y filtra los emails para que sean válidos
            /*$validEmails = array_filter($emails, function ($email) {
                return !empty($email) && filter_var($email, FILTER_VALIDATE_EMAIL);
            });*/
            $validEmails = array_filter($finalEmailsArray, function ($email) {
                return filter_var($email, FILTER_VALIDATE_EMAIL);
            });

            $first_name = mb_convert_case(mb_strtolower($affiliate->first_name ?? ''), MB_CASE_TITLE, "UTF-8");

            //Asignación de mes y año correspondientes
            $date = Carbon::parse($activityTaker->created_at)->subMonth();
            $month = ucfirst($date->formatLocalized('%B'));
            $year = $date->format('Y');

            $text = "¡Buen día, $first_name!
    
                ¡Muchas gracias por enviarnos su planilla mensual de trabajadores correspondiente a <b>$month</b> de <b>$year</b>!.
                
                Le confirmamos que recibimos la información satisfactoriamente y la aceptamos para el proceso de registro y gestión de pólizas.
                
                En caso de que tenga alguna consulta al respecto o necesite realizar algún ajuste, por favor, gestiónelo en nuestra plataforma a más tardar hoy a las 23:59 horas. 
        
                ¡Muchas gracias por la confianza que ha depositado en nosotros! Nuestro propósito es transformar la protección en una experiencia ágil, confiable y humana.
            ";
            try {
                $numPoliza = $dataPolicy->formatSortNumber();
            } catch (Exception $e) {
                $numPoliza = '';
            }

            if (!empty($validEmails)) {

                $mailSent = new SendDocumentDataBase(
                    implode(',', $validEmails),
                    "Recepción de su planilla mensual. Póliza #" . $numPoliza,
                    "<EMAIL>",
                    "Recepción de su planilla mensual. Póliza #" . $numPoliza,
                    [
                        "text" => $text,
                        "sender" => "mnk aseguramiento"
                    ],
                    "<EMAIL>",
                    [],
                    "send_document_db",
                    $activityTaker->client_id,
                    request()->getHost(),
                    $activityTaker->id
                );

                // Capturar el resultado del envío
                $result = $mailSent->sendMail();

                //Registramos los datos del correo enviado para la trazabilidad
                $mailBoardController = new MailBoardController();
                foreach ($validEmails as $email) {

                    $takerAuthorizedId = null;
                    $authorizedTaker = $takerData->firstWhere('email', $email);
                    if ($authorizedTaker) {
                        $takerAuthorizedId = $authorizedTaker->id;
                    }
                    $mailBoardController->createRegisterMail(
                        $activityTaker->id,
                        $activityTaker->service->id,
                        $dataPolicy->consecutive,
                        'Tomador',
                        $first_name,
                        $affiliate->doc_number,
                        "Recepción de su planilla mensual. Póliza #" . $numPoliza,
                        $text,
                        $validEmails,
                        $result,
                        null,
                        $takerAuthorizedId
                    );
                }

            }
        }
    }

    public function getAuthorizedTakerEmails($idActivityTaker)
    {
        $authorizedTakersData  = collect();

        if ($idActivityTaker) {

            // Se consultan los user id de los tomadores autorizados a la poliza
            $userIds = UserAuthorizedPolicies::where('activity_id', $idActivityTaker)
                ->pluck('user_id')
                ->toArray();
            //Se valida que existan usuarios autorizados
            if (!empty($userIds)) {
                $authorizedTakersData = User::whereIn('id', $userIds)
                    ->select('id','email')
                    ->get()
                    ->unique('email')
                    ->values();
            }
        }
        return $authorizedTakersData;
    }

    public function getRequiredFields($activityPolicicyQuotation)
    {
        return $activityPolicicyQuotation->policy_sort->work_modality_id != 2 && $activityPolicicyQuotation->policy_sort->work_modality_id != 3 ? [
            'ti' => 'Tipo de identificación',
            'nacionalidad' => 'Nacionalidad',
            'no_identificacion' => 'Número de identificación',
            'nombres' => 'Nombres',
            'apellidos' => 'Apellidos',
            'fecha_nacimiento' => 'Fecha de nacimiento',
            'sexo' => 'Sexo',
            'correo_electronico' => 'Correo electrónico',
            'salario_mensual' => 'Salario mensual',
            'dias' => 'Días',
            'horas' => 'Horas',
            'ocupacion' => 'Ocupación',
            'tipo_jornada' => 'Tipo de jornada',
        ] :
            [
                'ti' => 'Tipo de identificación',
                'no_identificacion' => 'Número de identificación',
                'nombres' => 'Nombres',
                'apellidos' => 'Apellidos',
            ];
    }

    private function validateRequiredFields($row, $fields, $rowNumber, &$errors)
    {
        foreach ($fields as $field => $fieldName) {
            // Verifica si el campo NO está establecido o es una cadena vacía (pero 0 es válido)
            if (!array_key_exists($field, $row) || $row[$field] === '') {
                $errors[] = "Faltan datos en la fila {$rowNumber}, campo: {$fieldName}.";
            }
        }
    }

    // Validación de tipo de documento
    private function validateDocumentType($documentType, $rowNumber, &$errors)
    {
        $VALID_DOCUMENT_TYPES = ['cf', 'cj', 'cd', 'di', 'pa', 'cr'];
        if (!in_array(strtolower($documentType), $VALID_DOCUMENT_TYPES)) {
            $errors[] = "El tipo de documento en la fila {$rowNumber} no es válido. Solo se permiten: " . implode(', ', $VALID_DOCUMENT_TYPES) . ".";
        }
    }


    // Validación de nacionalidad
    private function validateNationality($nationality, $rowNumber, &$errors)
    {
        $countries = $this->getValidCountries();

        if (is_numeric($nationality)) {
            $errors[] = "La nacionalidad en la fila {$rowNumber} no puede ser un valor numérico.";
        } elseif (!in_array(strtoupper($nationality), $countries)) {
            $errors[] = "La nacionalidad en la fila {$rowNumber} no es válida.";
        }
    }


    // Validación de sexo
    private function validateGender($gender, $rowNumber, &$errors)
    {
        $VALID_GENDERS = ['m', 'f'];
        if (!in_array(strtolower($gender), $VALID_GENDERS)) {
            $errors[] = "El sexo en la fila {$rowNumber} no es válido. Solo se permiten: M o F.";
        }
    }

    private function validateEmail($row, $rowNumber, &$errors)
    {
        if (!empty($row)) {
            // Comprobar si el correo electrónico tiene una estructura válida
            if (!filter_var($row, FILTER_VALIDATE_EMAIL)) {
                $errors[] = "El correo electrónico en la fila {$rowNumber} no tiene una estructura válida: {$row}.";
            }
        }
    }

    // Validación de fecha de nacimiento
    private function validateBirthDate($birthDate, $rowNumber, &$errors)
    {
        try {
            $parsedDate = is_numeric($birthDate)
                ? gmdate("Y-m-d", ($birthDate - 25569) * 86400)
                : \Carbon\Carbon::parse($birthDate)->format('Y-m-d');

            $age = \Carbon\Carbon::parse($parsedDate)->age;

            if ($age < 15) {
                $errors[] = "El trabajador en la fila {$rowNumber} tiene menos de 15 años.";
            }

            return $parsedDate;
        } catch (Exception $e) {
            $errors[] = "La fecha de nacimiento en la fila {$rowNumber} no es válida.";
            return null;
        }
    }


    // Validación de duplicados
    private function checkDuplicates($uniqueKey, $rowNumber, $ti, $number, &$processedRecords, &$errors)
    {
        if (isset($processedRecords[$uniqueKey])) {
            $errors[] = "El trabajador en la fila {$rowNumber} con {$ti} y No. de Identificación: {$number} está duplicado.";
        } else {
            $processedRecords[$uniqueKey] = true;
        }
    }

    // Obtener lista de países válidos
    private function getValidCountries()
    {
        $jsonPath = public_path('js/paises.json');
        $jsonContent = File::get($jsonPath);
        return array_column(json_decode($jsonContent, true), 'country_short_name');
    }


    // Validar días laborados
    private function validateDays($days, $rowNumber, $activityPolicicyQuotation, &$errors)
    {

        if (!is_numeric($days)) {
            $errors[] = "El valor de días en la fila {$rowNumber} debe ser un número válido.";
        } elseif ($days > 31) {
            $errors[] = "El trabajador en la fila {$rowNumber} tiene más de 31 días: {$days} días.";
        }

        if ($days > 5 && $activityPolicicyQuotation->policy_sort->work_modality_id == 4) {
            $errors[] = "El trabajador en la fila {$rowNumber} tiene más de 5 días laborados para la modalidad de aseguramiento riesgos del trabajo ocasional.";
        }
    }

    // Validar horas trabajadas
    private function validateHours($hours, $rowNumber, &$errors)
    {
        if (!is_numeric($hours)) {
            $errors[] = "El valor de horas en la fila {$rowNumber} debe ser un número válido.";
        } elseif ($hours > 400) {
            $errors[] = "El trabajador en la fila {$rowNumber} tiene más de 400 horas: {$hours} horas.";
        }
    }

    // Validar tipo de jornada
    private function validateWorkShift($workShift, $rowNumber, &$errors)
    {
        $VALID_WORK_SHIFTS = ['tc', 'tm', 'od', 'oh'];
        if (!in_array(strtolower($workShift), $VALID_WORK_SHIFTS)) {
            $errors[] = "El tipo de jornada en la fila {$rowNumber} no es válido. Solo se permiten: " . implode(', ', $VALID_WORK_SHIFTS) . ".";
        }
    }

    // Procesar los datos del Excel afiliado por afiliado reportado
    public function processAffiliateSpreadsheet($row, $policySpreadsheetId, &$errors, &$alerts, &$client, &$total, &$processedRecords, $rowNumber, $quotation, $activityPolicicyQuotation)
    {
        $birthDate = null;
        $fields = $this->getRequiredFields($activityPolicicyQuotation);

        // Convertir CellCollection a array si es necesario
        if ($row instanceof \Maatwebsite\Excel\Collections\CellCollection) {
            $row = $row->toArray(); // Convertir CellCollection a array
        }

        $row = array_change_key_case($row, CASE_LOWER);

        // Validar campos requeridos
        $this->validateRequiredFields($row, $fields, $rowNumber, $errors);

        // Validar tipo de documento
        if (!empty($row['ti'])) {
            $this->validateDocumentType($row['ti'], $rowNumber, $errors);
        }

        // Validaciones específicas para modalidades distintas de 2 y 3
        if ($activityPolicicyQuotation->policy_sort->work_modality_id != 2 && $activityPolicicyQuotation->policy_sort->work_modality_id != 3) {
            // Validar nacionalidad
            if (!empty($row['nacionalidad'])) {
                $this->validateNationality($row['nacionalidad'], $rowNumber, $errors);
            }

            // Validar sexo
            if (!empty($row['sexo'])) {
                $this->validateGender($row['sexo'], $rowNumber, $errors);
            }

            if (!empty($row['correo_electronico'])) {
                $this->validateEmail($row['correo_electronico'], $rowNumber, $errors);
            }
            // Validar fecha de nacimiento
            $birthDate = isset($row['fecha_nacimiento']) ? $this->validateBirthDate($row['fecha_nacimiento'], $rowNumber, $errors) : null;

            // Validar salario mensual
            if (!empty($row['salario_mensual']) && !is_numeric($row['salario_mensual'])) {
                $errors[] = "El salario mensual en la fila {$rowNumber} debe ser un número válido.";
            }

            // Validar días laborados
            if (!empty($row['dias'])) {
                $this->validateDays($row['dias'], $rowNumber, $activityPolicicyQuotation, $errors);
            }

            // Validar horas trabajadas
            if (!empty($row['horas'])) {
                $this->validateHours($row['horas'], $rowNumber, $errors);
            }

            // Validar tipo de jornada
            if (!empty($row['tipo_jornada'])) {
                $this->validateWorkShift($row['tipo_jornada'], $rowNumber, $errors);
            }
        }


        // Validar duplicados
        $uniqueKey = strtolower($row['ti']) . '_' . $row['no_identificacion'];
        $this->checkDuplicates($uniqueKey, $rowNumber, $row['ti'], $row['no_identificacion'], $processedRecords, $errors);
        // Crear o actualizar registros si no hay errores
        if (empty($errors)) {
            $this->createSpreadsheetAffiliateAndLinkAffiliate($policySpreadsheetId, $row, $quotation, $client, $birthDate, $activityPolicicyQuotation);
        }
    }


    private function createSpreadsheetAffiliateAndLinkAffiliate($policySpreadsheetId, $row, $quotation, $client, $birthDate, $activityPolicicyQuotation)
    {
        // Crear registro en PolicySpreadsheetAffiliate
        $spreadsheetAffiliate = PolicySpreadsheetAffiliate::updateOrCreate(
            [
                'policy_spreadsheet_id' => $policySpreadsheetId,
                'identification_number' => $row['no_identificacion']
            ],
            [
                'id_type' => $row['ti'],
                'nationality' => $activityPolicicyQuotation->policy_sort->work_modality_id != 2 && $activityPolicicyQuotation->policy_sort->work_modality_id != 3 ? $row['nacionalidad'] : null,
                'name' => $row['nombres'] . ' ' . $row['apellidos'],
                'first_name' => $row['nombres'],
                'last_name' => $row['apellidos'],
                'date_of_birth' => $birthDate ?? null,
                'gender' => $activityPolicicyQuotation->policy_sort->work_modality_id != 2 && $activityPolicicyQuotation->policy_sort->work_modality_id != 3 ? $row['sexo'] : null,
                'email' => $activityPolicicyQuotation->policy_sort->work_modality_id != 2 && $activityPolicicyQuotation->policy_sort->work_modality_id != 3 ? $row['correo_electronico'] : null,
                'monthly_salary' => $activityPolicicyQuotation->policy_sort->work_modality_id != 2 && $activityPolicicyQuotation->policy_sort->work_modality_id != 3 ? $row['salario_mensual'] : null,
                'days' => $activityPolicicyQuotation->policy_sort->work_modality_id != 2 && $activityPolicicyQuotation->policy_sort->work_modality_id != 3 ? $row['dias'] : null,
                'hours' => $activityPolicicyQuotation->policy_sort->work_modality_id != 2 && $activityPolicicyQuotation->policy_sort->work_modality_id != 3 ? $row['horas'] : null,
                'occupation' => $activityPolicicyQuotation->policy_sort->work_modality_id != 2 && $activityPolicicyQuotation->policy_sort->work_modality_id != 3 ? $row['ocupacion'] : null,
                'work_shift_type' => $activityPolicicyQuotation->policy_sort->work_modality_id != 2 && $activityPolicicyQuotation->policy_sort->work_modality_id != 3 ? $row['tipo_jornada'] : null,
                'observation_affiliate' => $row['observation_affiliate'] ?? null,
            ]
        );


        // Buscar o crear un registro en Affiliate
        $affiliate = Affiliate::where('client_id', $client->id)
            ->where('doc_type', $row['ti'])
            ->where('doc_number', $row['no_identificacion'])
            ->first();

        if ($affiliate) {
            //Actualizamos la informaicon del affiliate
            $affiliate->first_name = $row['nombres'];
            $affiliate->last_name = $row['apellidos'];
            $affiliate->country = !empty($row['nacionalidad']) ? $row['nacionalidad'] : $affiliate->country;
            $affiliate->birthday = $birthDate;
            $affiliate->email = !empty($row['correo_electronico']) ? $row['correo_electronico'] : $affiliate->email;
            $affiliate->occupation = isset($row['ocupacion']) ? $row['ocupacion'] : $affiliate->occupation;
            $affiliate->gender = isset($row['sexo']) ? $row['sexo'] : $affiliate->gender;
            $affiliate->save();

            $spreadsheetAffiliate->affiliate_id = $affiliate->id;
        } else {
            $newAffiliate = Affiliate::create([
                'client_id' => $client->id,
                'doc_type' => $row['ti'],
                'doc_number' => $row['no_identificacion'],
                'first_name' => $row['nombres'],
                'last_name' => $row['apellidos'],
                'country' => $activityPolicicyQuotation->policy_sort->work_modality_id != 2 && $activityPolicicyQuotation->policy_sort->work_modality_id != 3 ? $row['nacionalidad'] : null,
                'birthday' => $birthDate,
                'gender' => $activityPolicicyQuotation->policy_sort->work_modality_id != 2 && $activityPolicicyQuotation->policy_sort->work_modality_id != 3 ? $row['sexo'] : null,
                'email' => $activityPolicicyQuotation->policy_sort->work_modality_id != 2 && $activityPolicicyQuotation->policy_sort->work_modality_id != 3 ? $row['correo_electronico'] : null,
            ]);
            $spreadsheetAffiliate->affiliate_id = $newAffiliate->id;
        }

        // Guardar el registro de PolicySpreadsheetAffiliate
        $spreadsheetAffiliate->save();
    }

    // Procesar los datos del Excel afiliado por afiliado reportado por el excel subido
    public function processAffiliateSpreadsheetMassively($row, $rowNumber, &$totalSalaries, &$processedRecords, $quotation, &$totalAffiliate)
    {
        $errors = [];

        $fields = [
            'ti' => 'Tipo de identificación',
            'nacionalidad' => 'Nacionalidad',
            'no_identificacion' => 'Número de identificación',
            'nombres' => 'Nombres',
            'apellidos' => 'Apellidos',
            'fecha_nacimiento' => 'Fecha de nacimiento',
            'sexo' => 'Sexo',
            'correo_electronico' => 'Correo electrónico',
            'salario_mensual' => 'Salario mensual',
            'dias' => 'Días',
            'horas' => 'Horas',
            'ocupacion' => 'Ocupación',
            'tipo_jornada' => 'Tipo de jornada',
        ];

        if ($this->isRowEmpty($row, array_keys($fields))) {
            return $errors; // Omite esta fila vacía
        }

        // Convertir CellCollection a array si es necesario
        if ($row instanceof \Maatwebsite\Excel\Collections\CellCollection) {
            $row = $row->toArray(); // Convertir CellCollection a array
        }

        // Verificar que los campos esperados estén presentes en $row
        $row = array_change_key_case($row, CASE_LOWER);

        // Validación de datos completos para todas las columnas
        foreach ($fields as $field => $fieldName) {
            // Sacamos el valor (null si no existe)
            $value = array_key_exists($field, $row) ? $row[$field] : null;

            // Si es null, o es string y trim lo deja vacío, marcamos error.
            if (is_null($value) || (is_string($value) && trim($value) === '')) {
                $errors[] = "Faltan datos en la fila {$rowNumber}, campo: {$fieldName}.";
            }
        }

        // Validación de tipos de documentos
        $validTypeDocument = ["cf", "cj", "cd", "di", "pa", "cr"];

        if (!empty($row['ti'])) {

            // Asegurarse de que el tipo de documento sea una cadena
            if (!is_string($row['ti'])) {
                $errors[] = "El tipo de documento en la fila {$rowNumber} no es válido. El valor debe ser un texto.";
            } else {
                // Quitar espacios en blanco de los extremos y convertir a minúsculas
                $tipoDocumento = strtolower(trim($row['ti']));

                // Validar si el tipo de documento está en la lista de permitidos
                if (!in_array($tipoDocumento, $validTypeDocument)) {
                    $errors[] = "El tipo de documento en la fila {$rowNumber} no es válido. Solo se permiten: CF,CJ,CD,DI,PA,CR.";
                }
            }
        }

        //Validación de nacionalidades solo se permiten los establecido en el json paises.json
        $jsonPath = public_path('js/paises.json');
        $jsonContent = File::get($jsonPath);
        $countries = json_decode($jsonContent, true);

        if (!empty($row['nacionalidad'])) {
            // Asegurarse de que la nacionalidad sea una cadena
            if (!is_string($row['nacionalidad'])) {
                $errors[] = "El trabajador en la fila {$rowNumber} tiene una nacionalidad inválida. El valor debe ser un texto.";
            } else {
                // Quitar espacios en blanco de los extremos y convertir a mayúsculas
                $nacionality = strtoupper(trim($row['nacionalidad']));

                // Extraer todos los valores de `country_short_name` del JSON
                $validNationalities = array_column($countries, 'country_short_name');

                // Validar si la nacionalidad está en la lista
                if (!in_array($nacionality, $validNationalities)) {
                    $errors[] = "La nacionalidad en la fila {$rowNumber} no es válida.";
                }
            }
        }

        // Validación de tipos de sexos
        $validGender = ['m', 'f'];

        if (!empty($row['sexo'])) {
            // Asegurarse de que el valor de sexo sea una cadena
            if (!is_string($row['sexo'])) {
                $errors[] = "El sexo en la fila {$rowNumber} no es válido. El valor debe ser un texto.";
            } else {
                // Quitar espacios en blanco de los extremos y convertir a minúsculas
                $typeGender = strtolower(trim($row['sexo']));

                // Verifica si el tipo de sexo es válido
                if (!in_array($typeGender, $validGender)) {
                    $errors[] = "El sexo en la fila {$rowNumber} no es válido. Solo se permiten: M o F.";
                }
            }
        }

        // Validación de correo electrónico
        if (!empty($row['correo_electronico'])) {
            // Quitar espacios en blanco de los extremos
            $correoElectronico = trim($row['correo_electronico']);

            // Comprobar si el correo electrónico tiene una estructura válida
            if (!filter_var($correoElectronico, FILTER_VALIDATE_EMAIL)) {
                $errors[] = "El correo electrónico en la fila {$rowNumber} no tiene una estructura válida: {$correoElectronico}.";
            }
        }

        // Validación de días (solo valores numéricos y no más de 31 días)
        if (!empty($row['dias'])) {
            // Quitar espacios en blanco de los extremos
            $dias = trim($row['dias']);

            // Verificar si el valor es numérico
            if (!is_numeric($dias)) {
                $errors[] = "El valor de días en la fila {$rowNumber} debe ser un número válido.No se aceptan - * o espacios en blanco";
            } elseif ($dias > 31) {
                // Verificar si excede los 31 días
                $errors[] = "El trabajador en la fila {$rowNumber} tiene más de 31 días: {$dias} días.";
            }
        }

        // Si la modalidad de aseguramiento es Riesgos del Trabajo Ocasional
        if (!empty($row['dias'])) {

            $dias = $row['dias'];

            // validar que sea campo númerico
            if(is_string($dias)){
                $errors[] = "El valor de dias la fila {$rowNumber} debe ser un número.";
            }

            // Verificar si es numérico y si excede los 5 días
            if (is_numeric($dias) && $dias > 5 && $quotation->work_modality_id == 4) {
                $errors[] = "El trabajador en la fila {$rowNumber} tiene más de 5 días laborados para la modalidad de aseguramiento riesgos del trabajo ocasional.";
            }
            if ($dias < 0) {
                $errors[] = "El trabajador en la fila {$rowNumber} tiene menos de 1 día: {$dias} días.";
            }
        }

        // Validación de horas (solo valores numéricos y no más de 400 horas)
        if (!empty($row['horas'])) {
            // Quitar espacios en blanco de los extremos
            $horas = $row['horas'];

            // Verificar si el valor es numérico
            if (is_string($horas)) {
                $errors[] = "El valor de horas en la fila {$rowNumber} debe ser un número válido.No se aceptan - * o espacios en blanco";
            } elseif ($horas > 400) {
                // Verificar si excede las 400 horas
                $errors[] = "El trabajador en la fila {$rowNumber} tiene más de 400 horas: {$horas} horas.";
            }
            if ($horas < 1) {
                $errors[] = "El trabajador en la fila {$rowNumber} tiene menos de 1 hora: {$horas} horas.";
            }
        }

        // Identificar duplicados
        $uniqueKey = strtolower($row['ti']) . '_' . $row['no_identificacion'];
        if (isset($processedRecords[$uniqueKey])) {
            // Agregar alerta de duplicado
            $errors[] = "El trabajador en la fila {$rowNumber} con TI: {$row['ti']} y No. de Identificación: {$row['no_identificacion']} está duplicado.";
        } else {
            $processedRecords[$uniqueKey] = true;
        }
        //Validaciones de numero de documentos
        if (!empty($row['no_identificacion'])) {
            //verificar si es alfanumerico sin caracteres especiales
            if (!preg_match('/^[a-zA-Z0-9]*$/', $row['no_identificacion'])) {
                $errors[] = "El número de identificación en la fila {$rowNumber} no es válido. Solo se permiten caracteres alfanuméricos.";
            }
        }
        //Validaciones de nombres
        if (!empty($row['nombres'])) {
            //verificar si es alfanumerico con tildes
            if (!preg_match('/^[a-zA-ZÀ-ÿÑñ0-9\s]*$/', $row['nombres'])) {
                $errors[] = "El nombre en la fila {$rowNumber} no es válido. Solo se permiten caracteres alfabéticos,números,espacios,tildes y la letra ñ.";
            }
        }
        //Validaciones de apellidos
        if (!empty($row['apellidos'])) {
            //verificar si es alfanumerico con tildes
            if (!preg_match('/^[a-zA-ZÀ-ÿÑñ0-9\s]*$/', $row['apellidos'])) {
                $errors[] = "El apellido en la fila {$rowNumber} no es válido. Solo se permiten caracteres alfabéticos,números,espacios,tildes y la letra ñ.";
            }
        }

        $birthDate = null;

        if (isset($row['fecha_nacimiento'])) {
            if (is_numeric($row['fecha_nacimiento'])) {
                // Si la fecha es un valor numérico (fecha de Excel)
                $birthDate = gmdate("Y-m-d", ($row['fecha_nacimiento'] - 25569) * 86400);
            } else {
                // Se aceptan solo formato columna formato fecha en excel
                if (!$row['fecha_nacimiento'] instanceof Carbon) {
                    $errors[] = "La fecha de nacimiento en la fila {$rowNumber} debe ser en formato fecha dd/mm/aaaa.No se permi";
                }
                try {
                    $birthDate = \Carbon\Carbon::parse($row['fecha_nacimiento'])->format('Y-m-d');
                } catch (\Exception $e) {
                    $errors[] = "La fecha de nacimiento en la fila {$rowNumber} no es válida.";
                    $birthDate = null; // Opcionalmente asigna null en caso de error
                }
            }

            // Si se obtuvo una fecha válida, calcula la edad
            if ($birthDate) {
                $birthDateCarbon = \Carbon\Carbon::parse($birthDate);
                $age = $birthDateCarbon->age;

                // Verifica si la edad es menor a 15 años
                if ($age < 15) {
                    $errors[] = "El trabajador en la fila {$rowNumber} tiene menos de 15 años.";
                }
            }
        }

        // Validación de tipos de jornadas
        $validJornadas = ['tc', 'tm', 'od', 'oh'];

        if (!empty($row['tipo_jornada'])) {
            // Asegurarse de que el valor de tipo_jornada sea una cadena
            if (!is_string($row['tipo_jornada'])) {
                $errors[] = "El tipo de jornada en la fila {$rowNumber} no es válido. El valor debe ser una texto.";
            } else {
                // Quitar espacios en blanco de los extremos y convertir a minúsculas
                $tipoJornada = strtolower(trim($row['tipo_jornada']));

                // Verifica si el tipo de jornada es válido
                if (!in_array($tipoJornada, $validJornadas)) {
                    $errors[] = "El tipo de jornada en la fila {$rowNumber} no es válido. Solo se permiten:TC,TM,OD,OH.";
                }
            }
        }

        if (!empty($row['salario_mensual'])) {
            // Verificar si el valor es un número
            if (!is_numeric($row['salario_mensual'])) {
                $errors[] = "El salario mensual en la fila {$rowNumber} debe ser un número válido.No se aceptan - * o espacios en blanco";
            } else {
                if (($row['salario_mensual'] < 1)) {
                    $errors[] = "El salario mensual en la fila {$rowNumber} debe ser un número positivo mayor a 1.";
                }
            }
        }

        if (!empty($row['ocupacion'])) {
            if (!preg_match('/^[a-zA-ZÀ-ÿÑñ0-9\s,().:;]*$/', $row['ocupacion'])) {
                $errors[] = "La ocupación en la fila {$rowNumber} no es válida. Solo se permiten caracteres alfabéticos, paréntesis, punto, doble punto, comas, números, espacios, tildes y la letra ñ. ";
            }
        }

        if (empty($errors)) {
            $totalSalaries = $totalSalaries += $row['salario_mensual'];
            $totalAffiliate++;
        }

        return $errors;
    }

    private function isRowEmpty(array $row, array $fields): bool
    {
        foreach ($fields as $field) {
            if (trim($row[$field]) != '') {
                return false; // Si al menos un campo no está vacío, la fila no está vacía
            }
        }
        return true; // Todos los campos están vacíos
    }
    public function generateExcelTemplate(Request $request)
    {
        // Validar el policy_sort_id que llega por la solicitud AJAX
        $request->validate([
            'policy_sort_id' => 'required|integer',
        ]);

        //Buscamos la poliza por su id
        $policy = PolicySort::find($request->policy_sort_id);


        //Buscamos la ultima actividad del reporte planilla tomador mediante la poliza
        $activityReport = Activity::where('parent_id', $policy->activity_id)
        ->where('service_id', Service::SERVICE_REPORT_TAKEN_FORM_MNK)
        ->orderBy('id', 'desc')
        ->first();

        //Buscamos la planilla tomador mediante su actividad
        $lastReport = PolicySpreadsheet::where('activity_id', $activityReport->id)
            ->first();
        //Se llama la funcion que genera el excel con los campos actuales
        return Utilities::exportAffiliateExcel($lastReport->id);
    }

    /**
     * Método para reportar el seguimiento cargue planilla
     * @return JsonResponse
     */
    public function cronMonthlyReportSpreadsheetUpload(Request $request)
    {

        $id = uniqid();
        if (\Cache::has('cron_monthly_report_spreadsheet_upload')) {

            return response()->json([
                'status' => 200,
                'message' => 'Ya hay una validación en progreso',
            ], 200);

        }
        \Cache::put('cron_monthly_report_spreadsheet_upload', $id, now()->addMinutes(9));

        $currentDate = Carbon::now();
        $count_emails = 0;
        $failure_count = 0;
        if ($request->get('testDate')) {
            $currentDate = Carbon::parse($request->get('testDate'));
        }
        if (!$this->isNthBusinessDayOfMonth($currentDate, 5) && !$request->get('skipValidation')) {

            return response()->json([
                'status' => 'error',
                'message' => 'No es el quinto dia habil'
            ], 500);
        }

        try {
            // Buscar las actividades de póliza válidas
            $policyActivities = Activity::where('service_id', Service::SERVICE_POLICY_SORT_MNK)
                ->where('state_id', StatePoliza::POLIZA_EMITIDA_ACTIVA)
                ->whereHas('policy_sort', function ($query) {
                    $query->where('validity_from', '<', now())
                        ->whereIn('work_modality_id', [1, 5])
                        ->where('calendar_period', '=', '1');
                })
                ->whereDoesntHave('children', function ($query) use ($currentDate) {
                    $query->where('service_id', Service::SERVICE_REPORT_TAKEN_FORM_MNK)
                        ->whereMonth('created_at', '=', $currentDate->month)
                        ->whereYear('created_at', '=', $currentDate->year);
                })
                ->with(['policy_sort', 'affiliate', 'children' => function ($query) {
                    $query->where('service_id', Service::SERVICE_REPORT_TAKEN_FORM_MNK)
                        ->latest()
                        ->with(['policy_spreadsheets']);
                }])
                ->get();

            foreach ($policyActivities as $activity) {
                try {

                    $lastSpreadsheetActivity = $activity->children->first();

                    if ($lastSpreadsheetActivity) {
                        $count = optional(optional($lastSpreadsheetActivity->policy_spreadsheets)->policy_spreadsheet_affiliate)->count() ?? 0;
                    } else {
                        $count = 0;
                    }
                    // Si hay una planilla previa y solo hay un afiliado que es el tomador, no enviar correo
                    if (
                        $lastSpreadsheetActivity &&
                        $lastSpreadsheetActivity->policy_spreadsheets &&
                        $count === 1 &&
                        $lastSpreadsheetActivity->policy_spreadsheets->policy_spreadsheet_affiliate->first()->affiliate_id === $lastSpreadsheetActivity->affiliate_id
                    ) {
                        continue;
                    }
                    
                    // Si el usuario es tomador autorizado y  tiene permiso para la vista 
                    // se agrega su email para envio de correo electronico
                    $takerAuthData = collect();
                    if ($activity) {
                        $idActivityTaker = $activity->id;
                        $takerAuthData = $this->getAuthorizedTakerEmails($idActivityTaker);
                    }
                    $emailUsersTakerAuthorized = $takerAuthData->pluck('email');

                    // Obtener correos electrónicos del tomador e intermediario
                    // Agrega el email principal de notificaciones y todos los adicionales
                    $notiEmails = $this->getAdditionalNotificationEmails(
                        $activity->policy_sort->id,
                        [
                            optional($activity->policy_sort)->email ?? '',
                        ]
                    );
                    
                    // Combina los emails iniciales con los emails de los usuarios autorizados
                    $allEmails = collect($notiEmails)
                        ->merge($emailUsersTakerAuthorized)
                        ->filter(function ($email) {
                            return !empty($email);
                        })
                        ->unique()
                        ->values();

                    $finalEmailsArray = $allEmails->toArray();
                    $validEmails = array_filter($finalEmailsArray, function ($email) {
                        return filter_var($email, FILTER_VALIDATE_EMAIL);
                    });


                    if (!empty($validEmails)) {
                        $contractHolderName = mb_convert_case(mb_strtolower(optional($activity->affiliate)->first_name), MB_CASE_TITLE, "UTF-8");
                        $lastMonth = Carbon::now()->subMonth();
                        $year = $lastMonth->year;
                        $monthName = Str::ucfirst($lastMonth->formatLocalized('%B'));
                        $consecutive = optional($activity->policy_sort)->formatSortNumber();

                        if (!$lastSpreadsheetActivity) {
                            $monthName = "Emisión";
                        }

                        $emailBuild = TemplateBuilder::build(
                            Templates::MONTHLY_PAYROLL_SUBMISSION,
                            [
                                'policy_sort' => $consecutive,
                                'name' => $contractHolderName ?? '',
                                'monthName' => $monthName,
                                'year' => $year,
                            ]
                        );

                        $mailSent = new SendDocumentDataBase(
                            implode(',', $validEmails),
                            $emailBuild['subject'],
                            "<EMAIL>",
                            "Planilla mensual no reportada",
                            [
                                "text" => $emailBuild['body'],
                                "sender" => $emailBuild['sender']
                            ],
                            "<EMAIL>",
                            [],
                            "send_document_db",
                            $activity->client,
                            $request->host,
                            $activity->id
                        );

                        // Capturar el resultado del envío
                        $result = $mailSent->sendMail();

                        //Registramos los datos del correo enviado para la trazabilidad
                        $mailBoardController = new MailBoardController();
                        foreach ($validEmails as $email) {

                            $takerAuthorizedId = null;
                            $authorizedTaker = $takerAuthData->firstWhere('email', $email);
                            if ($authorizedTaker) {
                                $takerAuthorizedId = $authorizedTaker->id;
                            }
                            $mailBoardController->createRegisterMail(
                                $activity->id,
                                $activity->service->id,
                                $activity->policy_sort->consecutive,
                                'Tomador',
                                $contractHolderName,
                                $activity->affiliate->doc_number,
                                $emailBuild['subject'],
                                $emailBuild['body'],
                                $validEmails,
                                $result,
                                null,
                                $takerAuthorizedId
                            );
                        }

                        $count_emails++;
                        ActionController::create($activity->id, Action::REPORTAR_SEGUIMIENTO_CARGUE_PLANILLA, 'REPORTAR SEGUIMIENTO CARGUE PLANILLA');

                        if (!App::environment('prod') && $count_emails >= 5 && !$request->get('force')) {
                            break;
                        }
                    }
                } catch (\Exception $e) {
                    $failure_count++;
                    continue;
                }
            }
        } catch (\Exception $e) {
            $failure_count++;
        }

        return response()->json([
            'status' => 'success',
            'message' => "Se notificaron $count_emails emails del reporte de seguimiento cargue planilla.",
            'fallos' => $failure_count
        ], 200);
    }


    // Método para restar X días hábiles
    private function getPastBusinessDate($days, $startDate)
    {
        $date = $startDate->copy();
        $businessDaysCount = 0;

        while ($businessDaysCount < $days) {

            // Restar un día
            $date->subDay();

            // Si es un día hábil (lunes a viernes), sumar al contador
            if ($date->isWeekday()) {
                $businessDaysCount++;
            }
        }

        return $date;
    }

    /**
     * Método para guardar en S3 un documento de solicitud de variaciones
     * @param Request $request
     * @param $cpath
     * @param $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function uploadVariationsDocuments(Request $request, $cpath, $id)
    {
        // Validación
        $request->validate([
            'affiliate_file' => 'required|file|max:5120', // Máximo en kilobytes 5 MB = 5120 KB
        ], [
            'affiliate_file.max' => 'El archivo no debe superar los 5 MB.',
        ]);

        // Archivo cargado
        $file = $request->file('affiliate_file');

        // Subir el archivo a S3 usando el método putFileToAwsS3
        $fileUrl = Utilities::putFileToAwsS3($file, $cpath);

        // Respuesta JSON de éxito
        return response()->json([
            'message' => 'Archivo cargado y procesado exitosamente.',
            'file_url' => $fileUrl
        ]);
    }

    //Aociamos los ducmentos al guardar
    public function save(Request $req, $cpath, $id)
    {
        ///Buscamos el cliente
        $client = Client::where('path', $cpath)->firstOrFail();

        //Buscamos la activity de la poliza
        $activity = Activity::where('client_id', $client->id)->where('id', $id)
            ->firstOrFail();
        DB::beginTransaction();
        try {

            //Si no existe el servicio asociado se lo creamos
            if (!$activity->policy_sort) {
                $policy_sort = new PolicySort();
                $policy_sort->activity_id = $id;
                $policy_sort->save();
            }

            // Arreglo con los identificadores de actividad económica permitidos
            $economicActivities = ["0111", "0112", "0113", "0114", "0115", "0116", "0119", "0121", "0123", "0122", "0124", "0129", "0125", "0126", "0127", "0128", "4100"];
            $activityAction = "";
            $existActionWithNotReport = "";
            $existActionWithReport = "";
            if ($req->report_payroll == 'Si') {
                //Verificamos los estados en los que puede reportar documentos externos
                if ($activity->state_id != StatePoliza::PENDIENTE_DE_COBRO_SIN_PLANILLA && $activity->state_id != StatePoliza::DOCUMENTOS_EXTERNOS_PENDIENTES) {
                    //Ejecutamos la acción REPORTAR SEGUIMIENTO
                    $description = 'Se reporto seguimiento sobre la acción reportar documentos externos';
                    $activityAction = ActionController::create($activity->id, Action::REPORTAR_SEGUIMIENTO, $description);
                } else {
                    //Ejecutamos la acción SREPORTAR_DOCUMENTOS_EXTERNOS
                    $description = 'Pendiente reportar planilla';
                    $activityAction = ActionController::create($activity->id, Action::REPORTAR_DOCUMENTOS_EXTERNOS, $description);
                }
            } else {
                //Riesgos de trabajos o actividades economicas que no presentan planilla
                // (6) Riesgos del trabajo independiente,(7) Riesgos del trabajo Único Trabajador
                if (
                    (in_array($activity->policy_sort->activity_economic_id, $economicActivities) ||
                    $activity->policy_sort->work_modality_id == 4 ||
                    $req->report_payroll == 'No') &&
                    !in_array($activity->policy_sort->work_modality_id, [6, 7])
                ) {
                    //Verificamos los estados en donde puede reportar documentos externos sin planilla
                    if ($activity->state_id != StatePoliza::PENDIENTE_REPORTAR_PLANILLA && $activity->state_id != StatePoliza::DOCUMENTOS_EXTERNOS_PENDIENTES) {
                        //Ejecutamos la acción REPORTAR SEGUIMIENTO
                        $description = 'Se reporto seguimiento sobre la acción reportar documentos externos sin planilla';
                        $activityAction = ActionController::create($activity->id, Action::REPORTAR_SEGUIMIENTO, $description);
                    } else {
                        //Ejecutamos la acción REPORTAR_DOCUMENTOS_EXTERNOS_SIN_PLANILLAS
                        $description = 'Pendiente cobro sin planilla';
                        $activityAction = ActionController::create($activity->id, ActionPolizaSort::REPORTAR_DOCUMENTOS_EXTERNOS_SIN_PLANILLAS, $description);
                    }
                } else {

                    //Verificamos si ya se ejecuto la acción REPORTAR_DOCUMENTOS_EXTERNOS
                    $existActionWithReport = $activity->activity_actions->whereIn('action_id', [Action::REPORTAR_DOCUMENTOS_EXTERNOS, ActionPolizaSort::REPORTAR_DOCUMENTOS_EXTERNOS_SIN_PLANILLAS])
                        ->first();

                    if ($existActionWithReport) {
                        //Ejecutamos la acción REPORTAR SEGUIMIENTO
                        $description = 'Se reporto seguimiento sobre la acción reportar documentos externos';
                        $activityAction = ActionController::create($activity->id, Action::REPORTAR_SEGUIMIENTO, $description);
                    } else {
                        //Ejecutamos la acción SREPORTAR_DOCUMENTOS_EXTERNOS
                        $description = 'Pendiente reportar planilla';
                        $activityAction = ActionController::create($activity->id, Action::REPORTAR_DOCUMENTOS_EXTERNOS, $description);
                    }
                }
            }

            //Id de documentos del servicio de Poliza para este cargue
            $documentIds = [1, 2, 3, 4, 9];

            //empezamos con los index en 0
            $index = 0;

            //recorremos todos los docuemntos que vienen desde el form
            foreach ($documentIds as $documentId) {
                $index++;
                $documentName = 'document' . $index;
                if ($req->file($documentName)) {
                    $originalExtension = $req->file($documentName)->getClientOriginalExtension();
                    $uniqueName = Str::random(10) . uniqid() . '.' . $originalExtension;

                    //Subimos a S3 el documento que viene del form
                    $req->file($documentName)->storeAs('documents', $uniqueName, 's3');

                    //Creamos el registro de documentos en la tabla Activity Document
                    $activityDocument = ActivityDocument::where('activity_id', $activity->id)->where('document_id', $documentId)->first();
                    if (!$activityDocument) {
                        $activityDocument = new ActivityDocument;
                        $activityDocument->activity_id = $activity->id;
                        $activityDocument->document_id = $documentId;
                    }
                    $activityDocument->uploaded_at = Carbon::now();

                    //actualizamos el path del documento para luego poderlo visualizar
                    $activityDocument->path = 'documents/' . $uniqueName;
                    $activityDocument->save();
                }
            }

            DB::commit();

            //Riesgos de trabajos o actividades economicas que no presentan planilla
            // (6) Riesgos del trabajo independiente,(7) Riesgos del trabajo Único Trabajador
            if (
                (
                    in_array($activity->policy_sort->activity_economic_id, $economicActivities) ||
                    $activity->policy_sort->work_modality_id == 4 ||
                    $req->report_payroll == 'No'
                ) &&
                !in_array($activity->policy_sort->work_modality_id, [6, 7])
            ) {
                return redirect('/intermediario/poliza/' . $id . '/prima_emision');
            } else {
                return redirect('/intermediario/poliza/' . $id . '/informe_planilla');
            }
        } catch (\Exception $e) {
            DB::rollback();

            dd($e->getMessage());
            return back()->withInput();
        }
    }

    public function pdf(Request $req, $cpath, $id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();

        $policy_sort = new PolicySort();
        $policy_sort->activity_id = $id;
        $policy_sort->setRelation('activity', $activity);
        $policy_sort->created_at = new DateTime();

        $pdf = PDF::loadView(
            'services.policy_sort.docs.policy_sort_pdf',
            [
                'policy_sort' => $policy_sort,
                'watermark' => false,
                'activity' => $activity
            ]
        );

        return $pdf->stream('preview.pdf');
    }

    //generar condiciones particulares manualmente
    public function condiones_particulares($cpath, $id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();
        $economicActivity = EconomicActivity::where('code', $activity->policy_sort->activity_economic_id)->firstOrFail();
        switch ($activity->policy_sort->periodicity) {
            case 0:
            case null:
                $textPeriodicity = 'Pago único';
                $tem = $activity->policy_sort->unico_percentage;
                break;
            case 1:
                $textPeriodicity = 'Anual';
                $tem = $activity->policy_sort->anual_percentage;
                break;
            case 2:
                $textPeriodicity = 'Semestral';
                $tem = $activity->policy_sort->semestral_percentage;
                break;
            case 3:
                $textPeriodicity = 'Trimestral';
                $tem = $activity->policy_sort->trimestral_percentage;
                break;
            case 4:
                $textPeriodicity = 'Mensual';
                $tem = $activity->policy_sort->mensual_percentage;
                break;
            default:
                $textPeriodicity = 'Desconocido';
                break;
        }

        $pdf = PDF::loadView(
            'services.policy_sort.docs.condiciones_particulares_pdf',
            [
                'activity' => $activity,
                'economicActivity' => $economicActivity,
                'textPeriodicity' => $textPeriodicity,
                'tem' => $tem
            ]
        );

        return $pdf->stream('preview.pdf');
    }

    //ME-2605
    public function comunicado($cpath, $id)
    {
        $pdf = PDF::loadView(
            'services.policy_sort.docs.comunicado_pdf',
            [
                'nombre_tomador' => "Joseph Stiven Mosquera",
                'fecha_emision' => "2025-04-03"
            ]
        );

        return $pdf->stream('preview.pdf');
    }

    public function policyBill(Request $req, $cpath, $id)
    {

        $client = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();
        $affiliate = Affiliate::where('id', $activity->affiliate_id)->firstOrFail();
        $policy_sort = PolicySort::where('activity_id', $id)->firstOrFail();
        $pdf = PDF::loadView(
            'services.policy_sort.docs.policy_bill_pdf',
            [
                'policy_sort' => $policy_sort,
                'watermark' => false,
                'activity' => $activity,
                'affiliate' => $affiliate,
                'date' => Carbon::now()->format('d/m/Y')
            ]
        );

        return $pdf->stream('preview.pdf');
    }


    public function startPolicyIssuanceFromPolicy($cpath, $affiliate_id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();

        DB::beginTransaction();

        try {

            /***
             * Aquí se crea la actividad de POLIZA - SORT
             ***/


            $result = $this->createPolicy($cpath, $affiliate_id);
            DB::commit();

            return $result;
        } catch (Exception $e) {
            DB::rollBack();
            return response()->json(['error' => 'Ocurrió un error al guardar los datos'], 500);
        }
    }


    public function createPolicy($cpath, $affiliate_id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();

        DB::beginTransaction();

        try {
            $activity = new Activity;
            $activity->client_id = $client->id;
            $activity->service_id = Service::SERVICE_POLICY_SORT_MNK; // POLIZA - SORT
            $activity->affiliate_id = $affiliate_id;
            $activity->user_id = Auth::id();
            $activity->state_id = State::REGISTRADO;
            $activity->save();
            DB::commit();

            return $activity->id;
        } catch (Exception $e) {
            DB::rollBack();
            return response()->json(['error' => 'Ocurrió un error al guardar los datos'], 500);
        }
    }

    public function insuranceincreasepayment(Request $req, $cpath, $id)
    {

        $client = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();
        $action = Action::findOrFail($req->input('action_id'));

        if (Service::checkService($activity->service_id, Service::SERVICE_POLICY_SORT_COLLECTION)) {

            if ($action->id == Action::REPORTAR_PAGO_AUMENTO_DE_SEGURO) {
                $policy_sort_collection = PolicySortCollection::query()
                    ->where('activity_id', $activity->id)
                    ->first();

                if (!$policy_sort_collection) {
                    $policy_sort_collection = new PolicySortCollection();
                    $policy_sort_collection->activity_id = $activity->id;
                }

                // falta establecer de donde viene el valor de prima pagado acumulado y fecha de ultimo pago
                $policy_sort_collection->accumulatedPaidPremiumValue = ('accumulated_premium');
                $policy_sort_collection->lastPaymentDate = Carbon::now();
                $policy_sort_collection->save();
                //Llamar la accion REPORTAR PAGO REALIZADO del serivicio padre POLIZA SORT
            }
            // Crear un registro en ActivityAction para registrar el cambio de estado
            $activityAction = new ActivityAction();
            $activityAction->activity_id = $activity->id; // ID de la actividad actual
            $activityAction->action_id = $action->id; // ID de la acción realizada
            $activityAction->old_state_id = State::POLIZA_EMITIDA; // Estado inicial o antiguo
            $activityAction->new_state_id = State::POLIZA_EMITIDA; // Estado nuevo
            $activityAction->save();
        }
    }

    //Reporte planilla
    public function enterPayrollManually(Request $request, $cpath, $id, $npoliza)
    {
        $ReporTakenFormController = new ReportTakenFormController();
        $today = Carbon::now();
        //Se busca el cliente
        $client = Client::query()->where('path', $cpath)->firstOrFail();

        //Buscamos la poliza sort
        $polizaSort = PolicySort::find($npoliza);

        //Buscamos la actividad de la poliza
        $activity = Activity::where('client_id', $client->id)
            ->where('id', $polizaSort->activity_id)
            ->first();

        //Buscamos la ultima planilla del tomador de la poliza
        $activity_spread = Activity::with(['policy_spreadsheets'])
            ->where('parent_id', $polizaSort->activity_id)
            ->where('service_id', Service::SERVICE_REPORT_TAKEN_FORM_MNK)
            ->latest()
            ->first();

        if ($activity_spread && !$activity_spread->created_at->isCurrentMonth()) {
            $activity_spread = null;
        }

        //Buscamos la ultima actividad del reporte planilla tomador mediante la poliza
        $activityReport = Activity::where('parent_id', $polizaSort->activity_id)
            ->where('service_id', Service::SERVICE_REPORT_TAKEN_FORM_MNK)
            ->where('state_id', StatePoliza::CERTIFICADO_REPORTADO_TOMADOR)
            ->latest()
            ->first();


        $isReport = false;

        // Verificamos si se encontró la actividad y si fue creada en el mes actual
        if ($activityReport && $activityReport->created_at->isCurrentMonth() && $polizaSort->calendar_period == '1') {
            $isReport = true;
        }

        //Verificar el calendario especial si esta dentro del rango de fechas
        if ($polizaSort->calendar_period == '2') {
            $isReport = true;
            $calendarReport = $polizaSort->policy_calendars;
            //Validacion de la planilla de emision
            if ($activity_spread == null) {
                $fechaDesde = Carbon::parse($polizaSort->validity_from);
                $fechaLimitePoliza = $ReporTakenFormController->addBusinessDays($fechaDesde, 10);
                // Si hoy está dentro de los 10 días hábiles desde el inicio de vigencia y no tiene planilla
                if ($today->between($fechaDesde, $fechaLimitePoliza)) {
                    $isReport = false;
                }
            }
            // Validar cada período especial
            foreach ($calendarReport as $periodo) {
                if ($periodo->end_date) {
                    $fechaFin = Carbon::parse($periodo->end_date);
                    $fechaLimite = $ReporTakenFormController->addBusinessDays($fechaFin, 10);
                    // Si hoy está dentro del rango del periodo especial
                    if ($today->between($fechaFin, $fechaLimite)) {
                        $isReport = false;
                        //Verificar si la planilla es la del periodo actual
                        if ($activity_spread && !$activity_spread->created_at->between($fechaFin, $fechaLimite)) {
                            $activity_spread = null;
                        }
                        break;
                    }
                }
            }
        }


        //Verificamos si ya se ejecuto la acción REPORTAR_DOCUMENTOS_EXTERNOS_SIN_PLANILLAS
        $existActionWithNotReport = $activity->activity_actions->where('action_id', ActionPolizaSort::REPORTAR_DOCUMENTOS_EXTERNOS_SIN_PLANILLAS)
            ->first();


        //Retornamos la vista
        return view(
            'services.policy_sort.holder_policy.menu.enterpayroll_manually',
            [
                'id' => $id,
                'policy_spreadsheet' => isset($activity_spread) && isset($activity_spread->policy_spreadsheets)
                    ? $activity_spread->policy_spreadsheets
                    : null,
                'npoliza' => $npoliza,
                'activity' => $activity,
                'isReport' => $isReport,
                'policy_sort' => $polizaSort,
                'total_salaries' => isset($activity_spread) && isset($activity_spread->policy_spreadsheets->total_salaries)
                    ? (is_numeric($activity_spread->policy_spreadsheets->total_salaries)
                        ? number_format($activity_spread->policy_spreadsheets->total_salaries, 2, ',', '.')
                        : $activity_spread->policy_spreadsheets->total_salaries)
                    : null,
                'existActionWithNotReport' => $existActionWithNotReport,
                'active' => 'ingresar_manualmente'
            ]
        );
    }


    public function reportWithoutModifications(Request $request, $cpath, $id, $npoliza)
    {
        //Buscamos la poliza por su id
        $policy = PolicySort::find($npoliza);

        //Buscamos la ultima actividad del reporte planilla tomador mediante la poliza
       $activityReport = Activity::where('parent_id', $policy->activity_id)
    ->where('service_id', Service::SERVICE_REPORT_TAKEN_FORM_MNK)
    ->orderBy('id', 'desc')
    ->first();

        if (!$activityReport) {
            return back()->with('error', 'No hay planilla reportada para la póliza seleccionada');
        }
        //Buscamos la planilla tomador mediante su actividad
        $lastReport = PolicySpreadsheet::where('activity_id', $activityReport->id)
            ->first();

        return view(
            'services.policy_sort.holder_policy.menu.reportwithout_modifications',
            [
                'id' => $id,
                'npoliza' => $npoliza,
                'lastReport' => $lastReport,
                'active' => 'reportar_sin_modificaciones'
            ]
        );
    }

    public function duplicateReportPolicy(Request $request, $cpath)
    {
        try {
          
            //Se busca el cliente
            $client = Client::query()->where('path', $cpath)->firstOrFail();

            //Buscamos la poliza sort
            $polizaSort = PolicySort::find($request->policy_sort_id);

            //Necesito saber si la ultima planilla de esta poliza para el mes actual esta en estado ,
            //certificado generdado
            $activityPolicy = Activity::find($polizaSort->activity_id);

            if (
                $activityPolicy->state_id == StatePoliza::POLIZA_SUSPENDIDA ||
                $activityPolicy->state_id == StatePoliza::TRAMITE_ANULADO
            ) {

                return response()->json([
                    "success" => false,
                    "message" => "No puede cargar planilla porque la póliza está en estado suspendida o anulada"
                ]);
            } else {
                $activitySpreadsheet = Activity::where('parent_id', $request->activity_policy_id)
                    ->where('service_id', Service::SERVICE_REPORT_TAKEN_FORM_MNK)
                    ->where('state_id', StateReportePlanillaTomador::CERTIFICADO_DE_TOMADOR_REPORTADO)
                    ->first();

                //valida que la fecha en la que se va a crear el servico no sea menor a la vigencia de la poliza
                if ($activitySpreadsheet && Carbon::now()->lt($activityPolicy->policy_sort->validity_from)) {
                    return response()->json([
                        "success" => false,
                        "message" => "No puede cargar la planilla porque la fecha de carga es menor a la fecha de inicio de la vigencia de la póliza"
                    ]);
                }
            }


            //Buscamos la ultima actividad del reporte planilla tomador mediante la poliza


            $activityReport = Activity::where('parent_id', $polizaSort->activity_id)
                ->where('service_id', Service::SERVICE_REPORT_TAKEN_FORM_MNK)
                ->orderBy('id', 'desc')
                ->first();

            $isReport = true;

            // si la planilla del ultimo mes esta cerrada dejamos copiarla una nueva
            if ($activityReport->state_id == StatePoliza::CERTIFICADO_REPORTADO_TOMADOR && !$activityReport->created_at->isSameMonth(now())) {
                //Buscamos la planilla tomador mediante su actividad
                $lastReport = PolicySpreadsheet::where('activity_id', $activityReport->id)->first();

                if (!$lastReport) {
                    return response()->json([
                        "success" => false,
                        "message" => "No se encontró la planilla anterior para duplicar."
                    ]);
                }

                //Buscamos todos los afiliados de la planilla
                $affiliates = PolicySpreadsheetAffiliate::where('policy_spreadsheet_id', $lastReport->id)
                    ->where('benefit_colective', null)->count();
                   

                if ($affiliates > 0) {
                    $this->duplicateSpreadsheetData( $activityPolicy,$lastReport, 'Manual');
                }
                //Ajustes afiliados con beneficio colectividad se notifican y se actulizan campo de observacion colectividad
                $ReportTakenFormController = new ReportTakenFormController();
                $ReportTakenFormController->notifyAndUpdateAffiliateCollectivity($activityPolicy, $lastReport);

              
                return response()->json([
                    'success' => true,
                    'message' => 'Planilla duplicada de forma correcta.'
                ]);

            } else {

                return response()->json([
                    'success' => false,
                    'message' => 'No se puede realizar la copia: ya se ha generado una planilla para el mes actual o no está cerrada la actual.'
                ]);

            }

        } catch (Exception $e) {
        
            return response()->json([
                'success' => false,
                'message' => 'Ocurrió un error interno al intentar duplicar la planilla. ' . $e->getMessage()
            ]);
        }

    }

    public function duplicateSpreadsheetData($activityPolicy, $lastReport, $entryType)
    {

        try {
            DB::beginTransaction();
            //
            // ① Creamos la nueva actividad “Reporte tomador”
            //
            $activityReport = Activity::create([
                'client_id' => $activityPolicy->client_id,
                'parent_id' => $activityPolicy->id,
                'service_id' => Service::SERVICE_REPORT_TAKEN_FORM_MNK,
                'affiliate_id' => $activityPolicy->affiliate_id,
                'user_id' => $activityPolicy->user_id,
                'state_id' => State::REGISTRADO,
                'created_at' => now(),
                'updated_at' => now(),
            ]);

            // Registrar la acción
            ActionController::create(
                $activityReport->id,
                Action::REPORTAR_PLANILLA_TOMADOR,
                'Reportar planilla tomador duplicada'
            );

            //
            // ② Creamos el nuevo registro en `policy_spreadsheets`
            //

            $policySpreadsheet = PolicySpreadsheet::create([
                'activity_id' => $activityReport->id,
                'file' => $lastReport->file,
                'file_txt' => $lastReport->file_txt,
                'entry_type' => $entryType,
                'created_at' => now(),
                'updated_at' => now(),
            ]);


            //
            // ③ Duplicamos afiliados, pero en “chunks” y usando batch insert
            //

            PolicySpreadsheetAffiliate::where('policy_spreadsheet_id', $lastReport->id)
                ->whereNull('benefit_colective')
                ->chunkById(1000, function ($chunkedAffiliates) use ($policySpreadsheet) {
                    $batchInsert = [];
                    foreach ($chunkedAffiliates as $affiliate) {
                        $batchInsert[] = [
                            'policy_spreadsheet_id' => $policySpreadsheet->id,
                            'affiliate_id' => $affiliate->affiliate_id,
                            'name' => $affiliate->name,
                            'id_type' => $affiliate->id_type,
                            'nationality' => $affiliate->nationality,
                            'identification_number' => $affiliate->identification_number,
                            'first_name' => $affiliate->first_name,
                            'last_name' => $affiliate->last_name,
                            'date_of_birth' => $affiliate->date_of_birth,
                            'gender' => $affiliate->gender,
                            'work_shift_type' => $affiliate->work_shift_type,
                            'monthly_salary' => $affiliate->monthly_salary,
                            'days' => $affiliate->days,
                            'hours' => $affiliate->hours,
                            'occupation' => $affiliate->occupation,
                            'email' => $affiliate->email,
                            'created_at' => now(),
                            'updated_at' => now(),
                        ];
                    }

                    // Inserción masiva (batch insert)
                    PolicySpreadsheetAffiliate::insert($batchInsert);
                });

            //
            // ④ Recalculamos totales sin volver a consultar toda la tabla
            //     - count
            //     - sum
            //
            $totalAffiliates = PolicySpreadsheetAffiliate::where('policy_spreadsheet_id', $policySpreadsheet->id)->count();
            $totalSalaries = PolicySpreadsheetAffiliate::where('policy_spreadsheet_id', $policySpreadsheet->id)->sum('monthly_salary');

            $policySpreadsheet->total_affiliates = $totalAffiliates;
            $policySpreadsheet->total_salaries = $totalSalaries;
            $policySpreadsheet->save();

            //
            // ⑤ Actualizamos en la actividad padre (poliza) cuántos trabajadores quedaron
            //
            $activityPolicy->policy_sort()->update([
                'number_workers_optional' => $totalAffiliates
            ]);
            
            DB::commit();

        } catch (Exception $e) {
            DB::rollBack();
            throw new Exception('Error en la duplicación de la planilla ' . $e->getMessage());
        }

    }

    //Modificacion de inclusión persona trabajadora
    public function modifyWorkerData(Request $request, $cpath, $id, $npoliza)
    {
        //Buscamos la poliza por su id
        $policy = PolicySort::find($npoliza);

        //Buscamos la actividad de la poliza
        $activityPolicy = Activity::find($policy->activity_id);


        //Buscamos la última actividad del reporte planilla tomador mediante la poliza
        $activityReport = Activity::where('parent_id', $activityPolicy->id)
    ->where('service_id', Service::SERVICE_REPORT_TAKEN_FORM_MNK)
    ->orderBy('id', 'desc')
    ->first();

         
        if (!$activityReport) {
            return back()->with('error', 'No hay planilla reportada para la póliza seleccionada');
        }

        // Buscamos la planilla tomador mediante su actividad
        $lastReport = PolicySpreadsheet::where('activity_id', $activityReport->id)->first();

        // Buscamos todos los afiliados de la planilla
        $documentTypes = PolicySpreadsheetAffiliate::where('policy_spreadsheet_id', $lastReport->id)->get();

        // Determinar si la planilla está duplicada
        $planillaDuplicada = $activityReport->state_id == StatePoliza::CERTIFICADO_REPORTADO_TOMADOR ? 'SI' : 'NO';

        // Formatear el total de salarios
        $totalSalariesFormatted = number_format($lastReport->total_salaries, 2, ',', '.');


        // Pasar los datos a la vista
        return view('services.policy_sort.holder_policy.menu.modify_worker_data', [
            'id' => $id,
            'npoliza' => $npoliza,
            'documentTypes' => $documentTypes,
            'lastReport' => $lastReport,
            'planillaDuplicada' => $planillaDuplicada,
            'totalSalaries' => $totalSalariesFormatted, // Añadir el total de salarios formateado
            'active' => 'modificar_datos_trabajadores'
        ]);
    }

    //Buscamos afiliado de la planilla
    public function searchAffiliateReport(Request $request, $cpath)
    {

        //Buscamos la poliza por su id
        $policy = PolicySort::find($request->id);

        //Buscamos la actividad de la poliza
        $activityPolicy = Activity::find($policy->activity_id);

        //Buscamos la ultima actividad del reporte planilla tomador mediante la poliza
        $activityReport = Activity::where('parent_id', $policy->activity_id)
            ->where('service_id', Service::SERVICE_REPORT_TAKEN_FORM_MNK)
            ->latest()
            ->first();

        //Buscamos la planilla tomador mediante su actividad
        $lastReport = PolicySpreadsheet::where('activity_id', $activityReport->id)
            ->first();

        //Buscamos el afiliado en la planilla que viene por parametro
        $affiliateReport = PolicySpreadsheetAffiliate::where('id_type', $request->id_type)
            ->where('identification_number', $request->identification_number)
            ->where('policy_spreadsheet_id', $lastReport->id)
            ->first();

        if ($affiliateReport) {
            return response()->json([
                "status" => "success",
                "affiliate" => $affiliateReport,
                "message" => "Afiliado encontrando, se habilita formulario para actualizar o eliminar sus datos"
            ]);
        }

        return response()->json([
            "status" => "false",
            "message" => "Afiliado no encontrando, se habilita formulario por si desea crearlo"
        ]);
    }

    //Creamos o actualizamos los afiliados
    public function createUpdateAffiliate(Request $request, $cpath)
    {
        //Buscamos el cliente
        $client = Client::query()->where('path', $cpath)->firstOrFail();

        //Si el afiliado_id viene null creamos uno nuevo
        if ($request->affiliate_id == null) {

            //Buscamos la poliza por su id
            $policy = PolicySort::find($request->npoliza);

            //Buscamos la actividad de la poliza
            $activityPolicy = Activity::find($policy->activity_id);

            //Buscamos la ultima actividad del reporte planilla tomador mediante la poliza
            $activityReport = Activity::where('parent_id', $policy->activity_id)
                ->where('service_id', Service::SERVICE_REPORT_TAKEN_FORM_MNK)
                ->latest()
                ->first();

            //Buscamos la planilla tomador mediante su actividad
            $lastReport = PolicySpreadsheet::where('activity_id', $activityReport->id)
                ->first();

            $lastReport->total_affiliates = $lastReport->total_affiliates + 1;
            $lastReport->total_salaries = $lastReport->total_salaries + $request->monthly_salary;
            $lastReport->save();

            //guardar número de trabajadores
            $policy->update(["number_workers_optional" => $lastReport->total_affiliates]);

            $spreadsheetAffiliate = PolicySpreadsheetAffiliate::create([
                'policy_spreadsheet_id' => $lastReport->id,
                'affiliate_id' => $request->request_id,
                'name' => trim($request->first_name . ' ' . $request->last_name),
                'id_type' => $request->id_type,
                'nationality' => $request->nationality,
                'identification_number' => $request->identification_number,
                'first_name' => $request->first_name,
                'last_name' => $request->last_name,
                'date_of_birth' => $request->date_of_birth,
                'gender' => $request->gender,
                'work_shift_type' => $request->work_shift_type,
                'monthly_salary' => $request->monthly_salary,
                'days' => $request->days,
                'hours' => $request->hours,
                'occupation' => $request->occupation,
                'email' => $request->email,
            ]);

            // Vincular con Affiliate o crear uno nuevo
            $affiliate = Affiliate::where('doc_number', $request->identification_number)
                ->first();

            if ($affiliate) {
                $spreadsheetAffiliate->affiliate_id = $affiliate->id;
            } else {
                // Crear nuevo Affiliate si no existe
                $new_affiliate = Affiliate::create([
                    'client_id' => $client->id,
                    'doc_type' => $request->id_type,
                    'doc_number' => $request->identification_number,
                    'first_name' => $request->first_name,
                    'last_name' => $request->last_name,
                    'country' => $request->nationality,
                    'birthday' => $request->date_of_birth,
                    'gender' => $request->gender,
                ]);
                $spreadsheetAffiliate->affiliate_id = $new_affiliate->id;
            }

            $spreadsheetAffiliate->save();


            // Formatear el total de salarios antes de enviar la respuesta
            $totalSalariesFormatted = number_format($lastReport->total_salaries, 2, ',', '.');

            return response()->json([
                'status' => 'success',
                'message' => 'Acción exitosa',
                'lastReport' => [
                    'total_salaries' => $totalSalariesFormatted,
                    'total_affiliates' => $lastReport->total_affiliates,
                ]
            ]);
        }

        if ($request->id != null && $request->action == 'update') {

            //Buscamos el afiliado por su ID en la tabla de planilla
            $affiliate = PolicySpreadsheetAffiliate::find($request->id);

            $lastReport = PolicySpreadsheet::where('id', $affiliate->policy_spreadsheet_id)
                ->first();

            // Restamos el salario mensual actual del afiliado
            $newValue = $lastReport->total_salaries - $affiliate->monthly_salary;

            // Ahora sumamos el nuevo salario mensual del request
            $lastReport->total_salaries = $newValue + $request->monthly_salary;

            $lastReport->save();

            $affiliate->update([
                'name' => $request->first_name . $request->last_name,
                'id_type' => $request->id_type,
                'nationality' => $request->nationality,
                'identification_number' => $request->identification_number,
                'first_name' => $request->first_name,
                'last_name' => $request->last_name,
                'date_of_birth' => $request->date_of_birth,
                'gender' => $request->gender,
                'work_shift_type' => $request->work_shift_type,
                'monthly_salary' => $request->monthly_salary,
                'days' => $request->days,
                'hours' => $request->hours,
                'occupation' => $request->occupation,
                'email' => $request->email,
            ]);

            // Formatear el total de salarios antes de enviar la respuesta
            $totalSalariesFormatted = number_format($lastReport->total_salaries, 2, ',', '.');

            return response()->json([
                'status' => 'success',
                'message' => 'Acción exitosa',
                'lastReport' => [
                    'total_salaries' => $totalSalariesFormatted,
                    'total_affiliates' => $lastReport->total_affiliates,
                ]
            ]);
        }

        if ($request->id != null && $request->action == 'delete') {

            //Buscamos el afiliado por su ID en la tabla de planilla
            $affiliate = PolicySpreadsheetAffiliate::find($request->id);

            $lastReport = PolicySpreadsheet::where('id', $affiliate->policy_spreadsheet_id)
                ->first();

            //capturar actividad planilla
            $activity_spreadsheet = Activity::where('id', $lastReport->activity_id)
                ->first();

            //capturar policy sort
            $policy = PolicySort::where('activity_id', $activity_spreadsheet->parent_id)
                ->first();

            $lastReport->total_affiliates = $lastReport->total_affiliates - 1;
            $lastReport->total_salaries = $lastReport->total_salaries - $affiliate->monthly_salary;
            $lastReport->save();

            //guardar número de trabajadores
            $policy->update(["number_workers_optional" => $lastReport->total_affiliates]);

            $affiliate->delete();

            // Formatear el total de salarios antes de enviar la respuesta
            $totalSalariesFormatted = number_format($lastReport->total_salaries, 2, ',', '.');

            return response()->json([
                'status' => 'success',
                'message' => 'Acción exitosa',
                'lastReport' => [
                    'total_salaries' => $totalSalariesFormatted,
                    'total_affiliates' => $lastReport->total_affiliates,
                ]
            ]);
        }
    }

    //Acción inclusión de trabajador en la planilla
    public function includeWorkerReport(Request $request, $cpath)
    {
        DB::beginTransaction();

        try {

            $client = Client::query()->where('path', $cpath)->firstOrFail();

            //Buscamos la poliza por su id
            $policy = PolicySort::find($request->npoliza);

            //Buscamos la poliza por su id
            $activityPolicy = Activity::find($policy->activity_id);

            //Buscamos la ultima actividad del reporte planilla tomador mediante la poliza
            $activityReport = Activity::where('parent_id', $policy->activity_id)
                ->where('service_id', Service::SERVICE_REPORT_TAKEN_FORM_MNK)
                ->latest()
                ->first();

            $isTemporal = false;

            if (empty($activityReport)) {
                $activityReport = $this->temporaryInclusion($activityPolicy, $client);
                $isTemporal = true;
            }


            //Buscamos la planilla tomador mediante su actividad
            $lastReport = PolicySpreadsheet::where('activity_id', $activityReport->id)
                ->first();

            // Ejecutamos la acción
            $activityAction = ActionController::create(
                $activityReport->id,
                $isTemporal ? Action::INCLUSION_PERSONA_TRABAJADORA_DOS : Action::INCLUSION_PERSONA_TRABAJADORA,
                'Se ejecuto la acción inclusión persona trabajadora'
            );



            //Gaurdamos la firma.
            if ($request->signatureType == 'digital') {

                $files = [];

                if ($request->filled('sign') && $request->input('sign') !== '') {

                    $image_request = $request->input('sign'); // base64 string

                    $image_decode = base64_decode(preg_replace('#^data:image/\w+;base64,#i', '', $image_request));

                    $path1 = "policy_sort/sign_" . $policy->id . ".png";
                    Storage::disk('s3')->put($path1, $image_decode);
                    $url = Storage::disk('s3')->url($path1);

                    $pdf = $this->inclusion_data_pdf($activityPolicy, $url, $request);

                    $document_name = 'certificado_digital';

                    $filePath = "activity_action_document/{$document_name}_{$activityAction->id}.pdf";

                    Storage::disk('s3')->put($filePath, $pdf->output());

                    $activityActionDocument = new ActivityActionDocument();
                    $activityActionDocument->activity_action_id = $activityAction->id;
                    $activityActionDocument->name = "Certificado digital_" . $activityAction->id;
                    $activityActionDocument->path = $filePath;
                    $activityActionDocument->save();

                    $files[] = [
                        'type' => 'pdf',
                        'path' => $activityActionDocument->path,
                        'name' => 'Inclusión persona trabjadora ' . $policy->id . '.pdf',
                    ];
                }
            } else {

                $file = $request->file('pdfSignature');

                $fileName = "activity_action_document/firma_fisica_{$activityAction->id}.pdf";

                // Subir el archivo a S3
                $path = Storage::disk('s3')->put($fileName, file_get_contents($file));

                if ($path) {
                    // Si el archivo se subió correctamente, puedes almacenar el URL o hacer cualquier otra acción
                    $activityActionDocument = new ActivityActionDocument();
                    $activityActionDocument->activity_action_id = $activityAction->id;
                    $activityActionDocument->name = "Certificado fisico_" . $activityAction->id;
                    $activityActionDocument->path = $fileName;
                    $activityActionDocument->save();

                    $files[] = [
                        'type' => 'pdf',
                        'path' => $activityActionDocument->path,
                        'name' => 'Inclusión persona trabjadora ' . $policy->id . '.pdf',
                    ];
                }
            }

            //3.Guardamos los datos del formulario
            //se comenta ya que la planilla registrada es una temporal (fantasma) att:edward santiago
            //             $lastReport->total_affiliates = $lastReport->total_affiliates + 1;
            //             $lastReport->total_salaries = $lastReport->total_salaries + $request->monthly_salary;
            //             $lastReport->save();
            $monthly_salary = str_replace(',', '.', $request->monthly_salary);
            $monthly_salary = floatval($monthly_salary);
            $days = str_replace(',', '.', $request->days);
            $days = floatval($days);
            $hours = str_replace(',', '.', $request->hours);
            $hours = floatval($hours);
            $spreadsheetAffiliate = PolicySpreadsheetAffiliate::create([
                'policy_spreadsheet_id' => $lastReport->id,
                'affiliate_id' => $request->request_id,
                'name' => $request->first_name . ' ' . $request->last_name,
                'last_name' => $request->last_name,
                'id_type' => $request->id_type,
                'nationality' => $request->nationality,
                'identification_number' => $request->identification_number,
                'first_name' => $request->first_name,
                'date_of_birth' => $request->date_of_birth,
                'gender' => $request->gender,
                'work_shift_type' => $request->work_shift_type,
                'monthly_salary' => $monthly_salary,
                'days' => $days,
                'hours' => $hours,
                'occupation' => $request->occupation,
                'email' => $request->email,
                'temporal' => 1,
                'observation_affiliate' => $request->observation_affiliate
            ]);

            // Vincular con Affiliate o crear uno nuevo
            $affiliate = Affiliate::where('doc_number', $request->identification_number)
                ->first();

            if ($affiliate) {
                $spreadsheetAffiliate->affiliate_id = $affiliate->id;
            } else {
                // Crear nuevo Affiliate si no existe
                $new_affiliate = Affiliate::create([
                    'client_id' => $client->id,
                    'doc_type' => $request->id_type,
                    'doc_number' => $request->identification_number,
                    'first_name' => $request->first_name,
                    'last_name' => $request->last_name,
                    'country' => $request->nationality,
                    'birthday' => $request->date_of_birth,
                    'gender' => $request->gender,
                    'email' => $request->email,
                ]);
                $spreadsheetAffiliate->affiliate_id = $new_affiliate->id;
            }

            $spreadsheetAffiliate->save();

            //Guardamos la nueva proyección de salarios
            // $policy->salary_projection = $lastReport->total_salaries;

            //Calculamos el nuevo valor de prima de la póliza
            // $result = PolicySortController::calculatePolicyPriceIncludeWorker($policy);

            // $policy->amount_policy = $result['amountPolicy'];
            // $policy->annual_calculation_amount = $result['amountPolicy'];
            // $policy->semiannual_calculation_amount = $result['amountPolicySemestral'];
            // $policy->quarterly_calculation_amount = $result['amountPolicyTrimestral'];
            // $policy->monthly_calculation_amount = $result['amountPolicyMensual'];
            // $policy->single_payment_value = $result['valorUnico'];
            // $policy->save();

            //5. Generamos los correos al tomador e intermediario con cuerpos y formatos
            //correo Tomador
            $emailtaker = $activityPolicy->affiliate->email;

            //correo Intermediario
            $emailIntermediary = $policy->email;

            //Nombre del nuevo asegurado
            $nameAffiliate = mb_convert_case(mb_strtolower($request->first_name . ' ' . $request->last_name), MB_CASE_TITLE, "UTF-8");
            ;

            //enviamos correo al tomador
            $this->resendEmailIncludeWorkerTaker($request, $activityReport, $activityAction, $emailtaker, $client->id, $nameAffiliate, $activityPolicy, $files);

            //enviamos correo al intermediario
            $this->resendEmailIncludeWorkerIntermediary($request, $activityReport, $activityAction, $emailIntermediary, $client->id, $nameAffiliate, $activityPolicy, $files);


            //Generamos la planilla automatica en formato excel para asociar a
            // $this->generateExcelTemplateAutomatic($lastReport->id, $activityAction);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Acción exitosa',
                'id' => $activityPolicy->affiliate->id
            ], 200);
        } catch (Exception $e) {
            DB::rollBack();
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    private function temporaryInclusion($activityPoliza, $client)
    {

        //creamos o devolvemos la actividad del servicio REPORTE PLANILLA TOMADOR
        $activityService = $this->updateCreateActivityService($activityPoliza->id, $client);

        //creamos o actualizamos el servicio REPORTE PLANILLA TOMADOR
        $policySpreadsheet = $this->updateCreatePolicySpreadsheet($activityService);

        return $activityService;
    }

    //Descargar pdf de firma inclusion persona trabajdora
    function downloadSignalReport(Request $request, $id)
    {
        try {
            $policy = PolicySort::find($request->id);

            $activityPolicy = Activity::find($policy->activity->id);

            $ubicacion = $this->getLocationNamesFromJson($activityPolicy->affiliate->province, $activityPolicy->affiliate->canton, $activityPolicy->affiliate->district);
            $address = $ubicacion['province'] . ', ' . $ubicacion['canton'] . ', ' . $ubicacion['district'] . ', ' . $activityPolicy->affiliate->employer_address;

            $data = [
                'name' => $activityPolicy->affiliate->first_name ?? '',
                'policy' => $activityPolicy->policy_sort->formatNumberConsecutive() ?? '',
                'email' => $activityPolicy->affiliate->email ?? '',
                'employer_address' => $address ?? '',
                'phone' => $activityPolicy->affiliate->phone ?? '',
                'cellphone' => $activityPolicy->affiliate->phone ?? '',
                'sign' => $fileUrl ?? '',
                'name_asegurado' => $request->first_name . ' ' . $request->last_name,
                'id_type_asegurado' => $request->id_type ? AppServiceProvider::$DOC_TYPES[$request->id_type] : '',
                'identification_number_asegurado' => $request->identification_number ?? '',
                'date_of_birth_asegurado' => $request->date_of_birth ?? '',
                'email_asegurado' => $request->email ?? '',
                'monthly_salary_asegurado' => floatval(str_replace(',', '.', $request->monthly_salary)) ?? '',
                'occupation_asegurado' => $request->occupation ?? '',
                'gender_asegurado' => $request->gender ? AppServiceProvider::$GENDERS[$request->gender] : '',
                'work_shift_type_asegurado' => $request->work_shift_type ? AppServiceProvider::$TIPO_JORNADA_LABORAL[$request->work_shift_type] : '',
                'days_asegurado' => $request->days ?? '',
                'hours_asegurado' => $request->hours ?? '',
                'date_include' => $request->date_include ?? '',
                'hour_include' => $request->hour_include ?? '',
                'type_currency' => $activityPolicy->policy_sort ? $activityPolicy->policy_sort->type_currency : ''
            ];

            $document = 'firma_inclusion_trabajador';

            $pdf = PDF::loadView("services.policy_sort.docs.{$document}", [
                'data' => $data,
                'watermark' => false,
            ]);

            return $pdf->stream('firma_inclusion_trabajador.pdf');
        } catch (Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ], 500);
        }
    }
    //Descargar pdf de firma inclusion persona trabajdora
    function downloadSignalReportInclusion(Request $request, $cpath, $id)
    {
        $policy = PolicySort::find($id);

        $activityPolicy = Activity::find($policy->activity->id);

        //Buscamos la ultima actividad del reporte planilla tomador mediante la poliza
        $activityReport = Activity::where('parent_id', $activityPolicy->id)
            ->where('service_id', Service::SERVICE_REPORT_TAKEN_FORM_MNK)
            ->latest()
            ->first();

        //Buscamos la planilla tomador mediante su actividad
        $lastReport = PolicySpreadsheet::with([
            'policy_spreadsheet_affiliate' => function ($query) use ($request) {
                $query->where('id', $request->id);
            }
        ])
            ->where('activity_id', $activityReport->id)
            ->first();



        $fileUrl = '';

        $ubicacion = $this->getLocationNamesFromJson($activityPolicy->affiliate->province, $activityPolicy->affiliate->canton, $activityPolicy->affiliate->district);
        $address = $ubicacion['province'] . ', ' . $ubicacion['canton'] . ', ' . $ubicacion['district'] . ', ' . $activityPolicy->affiliate->employer_address;


        $data = [
            'name' => $activityPolicy->affiliate->first_name ?? '',
            'policy' => $activityPolicy->policy_sort->formatNumberConsecutive() ?? '',
            'email' => $activityPolicy->affiliate->email ?? '',
            'employer_address' => $address ?? '',
            'phone' => $activityPolicy->affiliate->phone ?? '',
            'cellphone' => $activityPolicy->affiliate->phone ?? '',
            'sign' => $fileUrl ?? '',
            'name_asegurado' => $lastReport->policy_spreadsheet_affiliate[0]->full_name ?? '',
            'id_type_asegurado' => $lastReport->policy_spreadsheet_affiliate[0]->id_type ? AppServiceProvider::$DOC_TYPES[$lastReport->policy_spreadsheet_affiliate[0]->id_type] : '',
            'identification_number_asegurado' => $lastReport->policy_spreadsheet_affiliate[0]->identification_number ?? '',
            'date_of_birth_asegurado' => $lastReport->policy_spreadsheet_affiliate[0]->date_of_birth ?? '',
            'email_asegurado' => $lastReport->policy_spreadsheet_affiliate[0]->email ?? '',
            'monthly_salary_asegurado' => $lastReport->policy_spreadsheet_affiliate[0]->monthly_salary ?? '',
            'occupation_asegurado' => $lastReport->policy_spreadsheet_affiliate[0]->occupation ?? '',
            'gender_asegurado' => $lastReport->policy_spreadsheet_affiliate[0]->gender ? AppServiceProvider::$GENDERS[$lastReport->policy_spreadsheet_affiliate[0]->gender] : '',
            'work_shift_type_asegurado' => $lastReport->policy_spreadsheet_affiliate[0]->work_shift_type ? AppServiceProvider::$TIPO_JORNADA_LABORAL[$lastReport->policy_spreadsheet_affiliate[0]->work_shift_type] : '',
            'days_asegurado' => $lastReport->policy_spreadsheet_affiliate[0]->days ?? '',
            'hours_asegurado' => $lastReport->policy_spreadsheet_affiliate[0]->hours ?? '',
            'date_include' => $lastReport->policy_spreadsheet_affiliate[0]->created_at ?? '',
            'hour_include' => $lastReport->policy_spreadsheet_affiliate[0]->created_at->format('H:i:s') ?? '',
            'type_currency' => $activityPolicy->policy_sort ? $activityPolicy->policy_sort->type_currency : ''
        ];

        // Cargar la vista del PDF y pasarle los datos
        $document = 'firma_inclusion_trabajador';

        $pdf = PDF::loadView("services.policy_sort.docs.{$document}", [
            'data' => $data,
            'watermark' => false,
        ]);

        // Previsualizar el PDF en el navegador
        return $pdf->stream('firma_inclusion_trabajador.pdf');
    }

    //Firma persona trabajadora
    function inclusion_data_pdf($activityPolicy, $fileUrl, $request)
    {

        $ubicacion = $this->getLocationNamesFromJson($activityPolicy->affiliate->province, $activityPolicy->affiliate->canton, $activityPolicy->affiliate->district);
        $address = $ubicacion['province'] . ', ' . $ubicacion['canton'] . ', ' . $ubicacion['district'] . ', ' . $activityPolicy->affiliate->employer_address;
        $monthly_salary = str_replace(',', '.', $request->monthly_salary);
        $monthly_salary = floatval($monthly_salary);
        $days = str_replace(',', '.', $request->days);
        $days = floatval($days);
        $hours = str_replace(',', '.', $request->hours);
        $hours = floatval($hours);

        $data = [
            'name' => $activityPolicy->affiliate->first_name ?? '',
            'policy' => $activityPolicy->policy_sort->formatNumberConsecutive() ?? '',
            'email' => $activityPolicy->affiliate->email ?? '',
            'employer_address' => $address ?? '',
            'phone' => $activityPolicy->affiliate->phone ?? '',
            'cellphone' => $activityPolicy->affiliate->phone ?? '',
            'sign' => $fileUrl ?? '',
            'name_asegurado' => $request->first_name . ' ' . $request->last_name,
            'id_type_asegurado' => $request->id_type ? AppServiceProvider::$DOC_TYPES[$request->id_type] : '',
            'identification_number_asegurado' => $request->identification_number ?? '',
            'date_of_birth_asegurado' => $request->date_of_birth ?? '',
            'email_asegurado' => $request->email ?? '',
            'monthly_salary_asegurado' => $monthly_salary ?? '',
            'occupation_asegurado' => $request->occupation ?? '',
            'gender_asegurado' => $request->gender ? AppServiceProvider::$GENDERS[$request->gender] : '',
            'work_shift_type_asegurado' => $request->work_shift_type ? AppServiceProvider::$TIPO_JORNADA_LABORAL[$request->work_shift_type] : '',
            'days_asegurado' => $days ?? '',
            'hours_asegurado' => $hours ?? '',
            'date_include' => $request->date_include ?? '',
            'hour_include' => $request->hour_include ?? '',
            'type_currency' => $activityPolicy->policy_sort ? $activityPolicy->policy_sort->type_currency : ''
        ];

        // Cargar la vista del PDF y pasarle los datos
        $document = 'firma_inclusion_trabajador';

        return $pdf = PDF::loadView("services.policy_sort.docs.{$document}", [
            'data' => $data,
            'watermark' => false,
        ]);
    }

    //calculo de la prima para la inclusión del trabajador
    public function calculatePolicyPriceIncludeWorker($policySort)
    {
        $webserviceController = new WebserviceAcselController();

        $minDolares = 100;
        $minColones = $webserviceController->convertDollarsToColones($minDolares);
        $min = $policySort->type_currency == 'USD' ? $minDolares : $minColones;
        $salary_projection = $policySort->salary_projection;
        $validity_from = $policySort->validity_from;
        $validity_to = $policySort->validity_to;
        $sector = $policySort->economic_activity;
        $activityEconomicId = $policySort->activity_economic_id;

        $salary_projection = floatval($salary_projection);
        $validityFrom = new \DateTime($validity_from);
        $validityTo = new \DateTime($validity_to);
        $dias = $validityTo->diff($validityFrom)->days;
        // Simulación de los datos de actividad económica
        $percentage = PolicySortController::getActivityPercentage($sector, $activityEconomicId); // Función para obtener el porcentaje
        // Formato del porcentaje
        $percentage = str_replace(',', '.', $percentage);
        $percentage = number_format($percentage / 100, 14);
        $percentage = floatval($percentage);

        // Cálculo de primas según la temporalidad

        $amountPolicy = $salary_projection * 12 * $percentage;
        // Cálculo de prima semestral, trimestral y mensual

        $amountPolicySemestral = $amountPolicy * 1.04 / 2;
        $amountPolicyTrimestral = $amountPolicy * 1.06 / 4;
        $amountPolicyMensual = $amountPolicy * 1.08 / 12;
        // Si es una póliza temporal

        $temAnual = number_format(round($percentage * 100, 2), 2, ',', '');
        $temSemestral = number_format(round(($percentage * 1.04) * 100, 2), 2, ',', '');
        $temTrimestral = number_format(round(($percentage * 1.06) * 100, 2), 2, ',', '');
        $temMensual = number_format(round(($percentage * 1.08) * 100, 2), 2, ',', '');
        $temUnico = number_format(round(($percentage * 1.06) * 100, 2), 2, ',', '');

        //calculamos la diferencia de dias para el pago del periodo corto
        $recargo = 1;
        if ($dias >= 1 && $dias <= 90) {
            $recargo = 1.08;
        }
        ;

        if ($dias > 90 && $dias <= 180) {
            $recargo = 1.06;
        }
        ;

        if ($dias > 180 && $dias <= 364) {
            $recargo = 1.04;
        }
        ;

        $valorUnico = ((($salary_projection * $percentage) / 360)) * $dias * $recargo;
        if ($valorUnico < $min) {
            $valorUnico = $min;
        }
        if ($amountPolicy < $min) {
            $amountPolicy = $min;
        }
        if ($amountPolicySemestral < $min) {
            $amountPolicySemestral = $min;
        }
        if ($amountPolicyTrimestral < $min) {
            $amountPolicyTrimestral = $min;
        }
        if ($amountPolicyMensual < $min) {
            $amountPolicyMensual = $min;
        }
        return [
            'amountPolicy' => $amountPolicy,
            'amountPolicySemestral' => $amountPolicySemestral,
            'amountPolicyTrimestral' => $amountPolicyTrimestral,
            'amountPolicyMensual' => $amountPolicyMensual,
            'valorUnico' => $valorUnico,
            'percentage' => $percentage,
            'dias' => $dias,
            'temAnual' => $temAnual,
            'temSemestral' => $temSemestral,
            'temTrimestral' => $temTrimestral,
            'temMensual' => $temMensual,
            'temUnico' => $temUnico
        ];
    }

    //Porcentajes de actividades economicas
    private static function getActivityPercentage($sector, $activityEconomicId)
    {
        if ($sector === 'public') {
            // Ruta del archivo JSON en la carpeta public
            $jsonPath = public_path('js/economic_activity/public.json');
        } elseif ($sector === 'private') {
            // Ruta del archivo JSON en la carpeta private
            $jsonPath = public_path('js/economic_activity/private.json');
        } else {
            throw new \Exception('Sector no encontrado');
        }
        // Leer y decodificar el contenido del archivo JSON
        if (file_exists($jsonPath)) {
            $jsonContent = file_get_contents($jsonPath);
            $activities = json_decode($jsonContent, true);

            // Buscar la actividad económica por código
            foreach ($activities as $activity) {
                if ($activity['CODE'] == $activityEconomicId) {
                    // Retornar el porcentaje de la actividad encontrada
                    $percentage = str_replace(',', '.', $activity['PERCENTAGE']);
                    return number_format($percentage, 8);
                }
            }
        }

        throw new \Exception('Actividad económica no encontrada');
    }

    //Enviamos correo al tomador e intermediario la inclusión del nuevo trabajador
    public function resendEmailIncludeWorkerTaker($req, $activityReport, $activityAction, $emails, $client_id, $nameAffiliate, $activityPolicy, $files)
    {
        $policy_id = $activityPolicy->policy_sort->formatNumberConsecutive();
        $nameTaker = mb_convert_case(mb_strtolower($activityPolicy->affiliate->first_name ?? ''), MB_CASE_TITLE, "UTF-8");

        $takerAuthData = collect();
        if ($activityPolicy) {
            $idActivityTaker = $activityPolicy->id;
            $takerAuthData = $this->getAuthorizedTakerEmails($idActivityTaker);
        }
        $emailUsersTakerAuthorized = $takerAuthData->pluck('email');

        // Combina los emails iniciales con los emails de los usuarios autorizados
        $allEmails = collect([$emails])
            ->merge($emailUsersTakerAuthorized)
            ->filter(function ($email) {
                return !empty($email);
            })
            ->unique()
            ->values();

        $finalEmailsArray = $allEmails->toArray();
        $validEmails = array_filter($finalEmailsArray, function ($email) {
            return filter_var($email, FILTER_VALIDATE_EMAIL);
        });

        $emailData = TemplateBuilder::build(
            Templates::POLICYHOLDER_EMAIL_PROVISIONAL_INCLUSION,
            [
                'policy_sort' => $policy_id,
                'name' => $nameTaker,
                'name_affiliate' => $nameAffiliate
            ]
        );

        // Lógica para el envío de correos
        if ($validEmails != null) {

            // Enviar el correo con los documentos adjuntos
            $mailSent = new SendDocumentDataBase(
                $validEmails,
                $emailData['subject'],
                "<EMAIL>",
                "Inclusión provisional de persona asegurada",
                [
                    "text" => $emailData['body'],
                    "sender" => $emailData['sender']
                ],
                "<EMAIL>",
                $files,
                "send_document_db",
                $client_id,
                $req->getHost(),
                $activityReport,
                $activityAction,
                SERVICE::SERVICE_POLICY_SORT_MNK
            );

            // Capturar el resultado del envío
            $result = $mailSent->sendMail();

            //Registramos los datos del correo enviado para la trazabilidad
            $mailBoardController = new MailBoardController();

            foreach ($validEmails as $email) {

                $takerAuthorizedId = null;
                $authorizedTaker = $takerAuthData->firstWhere('email', $email);
                if ($authorizedTaker) {
                    $takerAuthorizedId = $authorizedTaker->id;
                }
                $mailBoardController->createRegisterMail(
                    $activityReport->id,
                    $activityReport->service->id,
                    $activityPolicy->policy_sort->consecutive,
                    'Tomador',
                    $nameTaker,
                    $activityPolicy->affiliate->doc_number,
                    $emailData['subject'],
                    $emailData['body'],
                    $emails,
                    $result,
                    null,
                    $takerAuthorizedId
                );
            }

        }
    }

    //Enviamos correo al tomador e intermediario la inclusión del nuevo trabajador
    public function resendEmailIncludeWorkerIntermediary($req, $activityReport, $activityAction, $emails, $client_id, $nameAffiliate, $activityPolicy, $files)
    {

        $nameTaker = mb_convert_case(mb_strtolower($activityPolicy->affiliate->first_name ?? ''), MB_CASE_TITLE, "UTF-8");


        $policy_id = $activityPolicy->policy_sort->formatNumberConsecutive();

        $subject = 'Inclusión provisional de persona asegurada en póliza #' . $policy_id;

        $nameIntermediary = mb_convert_case(mb_strtolower($activityPolicy->policy_sort->advisor_name ?? ''), MB_CASE_TITLE, "UTF-8");

        $nameTaker = mb_convert_case(mb_strtolower($activityPolicy->affiliate->full_name ?? ''), MB_CASE_TITLE, "UTF-8");

        $body = "¡Buen día, $nameIntermediary!

        Nos complace informarle que hemos realizado la inclusión provisional de los siguientes asegurados en la póliza #$policy_id, a nombre de $nameTaker:

        $nameAffiliate

        Igualmente, hemos recibido el reporte provisional correspondiente.

        ¡Agradecemos sinceramente su preferencia y confianza en nosotros! Nuestro propósito es fortalecer la prevención en salud y seguridad laboral del país.";

        // Lógica para el envío de correos
        if ($emails != null) {

            // Enviar el correo con los documentos adjuntos
            $mailSent = new SendDocumentDataBase(
                $emails,
                $subject,
                "<EMAIL>",
                "Inclusión provisional de persona asegurada - póliza " . $policy_id,
                [
                    "text" => $body,
                    "sender" => 'mnk aseguramiento'
                ],
                "<EMAIL>",
                $files,
                "send_document_db",
                $client_id,
                $req->getHost(),
                $activityReport,
                $activityAction,
                SERVICE::SERVICE_POLICY_SORT_MNK
            );

            // Capturar el resultado del envío
            $result = $mailSent->sendMail();

            //Registramos los datos del correo enviado para la trazabilidad
            $mailBoardController = new MailBoardController();
            $mailBoardController->createRegisterMail(
                $activityReport->id,
                $activityReport->service->id,
                $activityPolicy->policy_sort->consecutive,
                'Tomador',
                $nameTaker,
                $activityPolicy->affiliate->doc_number,
                $subject,
                $body,
                $emails,
                $result,
                $files
            );

        }
    }

    //consulta de polizas
    public function consultspreadsheet(Request $request, $cpath, $id, $npoliza)
    {
        $action_start_date = $request->input('action_start_date_submit'); // Formato esperado: 'Y-m-d' o 'd/m/Y'
        $action_end_date = $request->input('action_end_date_submit'); // Formato esperado: 'Y-m-d' o 'd/m/Y'
        $numSpreedsheetFilter = $request->input('num_spreedsheet'); // Obtener el número de planilla del formulario
        $workerId = $request->input('worker_id');
        // Obtener los nombres de los trabajadores desde la solicitud
        $workerName = $request->input('worker_name');

        // Consultar la información de la póliza
        $policySort = PolicySort::where('id', $npoliza)->first();

        // Actividades asociadas a Reporte de planilla para tomador por la actividad de la póliza (padre)
        $activities = Activity::where('parent_id', $policySort->activity_id)
            ->where('service_id', Service::SERVICE_REPORT_TAKEN_FORM_MNK)
            ->whereIn('state_id', [StateReportePlanillaTomador::CERTIFICADO_DE_TOMADOR_REPORTADO, StateReportePlanillaTomador::PLANILLA_REPORTADA])
            ->get();

        // Obtener los IDs de las actividades
        $activityIds = $activities->pluck('id');

        // Planillas reportadas con relaciones cargadas
        $spreadsheetsQuery = PolicySpreadsheet::with(['activity', 'activity.activity_actions', 'activity.activity_actions.documents'])
            ->whereIn('activity_id', $activityIds);


        // Conseguir los nombres de los afiliados
        $name_affiliates = PolicySpreadsheetAffiliate::whereIn(
            'policy_spreadsheet_id',
            PolicySpreadsheet::whereIn('activity_id', $activityIds)->pluck('id')
        )->distinct()->pluck('name');

        // Unir con policy_spreadsheet_affiliates para aplicar filtros por trabajador
        if (!empty($workerId) || !empty($workerName) || !empty($numSpreedsheetFilter)) {
            // Aplicar filtro por número de planilla si se proporciona
            if (!empty($numSpreedsheetFilter)) {
                $spreadsheetsQuery->where('id', $numSpreedsheetFilter); // Filtrar por número de planilla
            }

            // Obtener los IDs de las planillas de la consulta original
            $spreadsheetIds = $spreadsheetsQuery->pluck('id');

            // Filtrar policy_spreadsheet_affiliates según los IDs de las planillas
            $policy_spreadsheet_affiliates = PolicySpreadsheetAffiliate::whereIn('policy_spreadsheet_id', $spreadsheetIds);

            // Aplicar filtro por id del trabajador
            if (!empty($workerId)) {
                $policy_spreadsheet_affiliates->where('affiliate_id', $workerId);
            }

            // Aplicar filtro por nombre del trabajador
            if (!empty($workerName)) {
                $policy_spreadsheet_affiliates->when(is_array($workerName), function ($query) use ($workerName) {
                    return $query->whereIn('name', $workerName);
                }, function ($query) use ($workerName) {
                    return $query->where('name', 'LIKE', "%{$workerName}%");
                });
            }

            // Obtener los IDs de las planillas que cumplen con los filtros de afiliados
            $filteredSpreadsheetIds = $policy_spreadsheet_affiliates->pluck('policy_spreadsheet_id');

            // Filtrar la consulta original de planillas
            $spreadsheetsQuery->whereIn('id', $filteredSpreadsheetIds);
        }

        // Procesar y aplicar los filtros de fechas
        $filteredData = $this->applyDateFilters($spreadsheetsQuery, $action_start_date, $action_end_date, 'policy_spreadsheets.created_at');

        // Obtener los resultados paginados
        $spreadsheets = $filteredData->paginate(10)
            ->appends(request()->only('worker_id', 'worker_name', 'num_spreedsheet', 'action_start_date_submit', 'action_end_date_submit'));

        // Asignar periodos a las planillas
        $spreadsheets->getCollection()->each(function ($spreadsheet, $key) {
            // Obtener el mes de la fecha de creación
            $month = $spreadsheet->created_at;
            $date = Carbon::parse($month);
            // Asignar el nombre del mes al objeto
            $spreadsheet->month_name = $date->formatLocalized('%B');
            if ($key == 0) {
                $spreadsheet->period = "Emisión";
            } else {
                // Para las demás posiciones, asignar el mes anterior
                $previousMonth = $date->subMonth()->formatLocalized('%B');
                $spreadsheet->period = ucfirst($previousMonth);
            }
        });


        return view('services.policy_sort.holder_policy.menu.consult_spreadsheet', [
            'id' => $id,
            'npoliza' => $npoliza,
            'policySelected' => $policySort,
            'spreadsheets' => $spreadsheets,
            'name_affiliates' => $name_affiliates,
            'active' => 'consultar_planilla'
        ]);
    }

    //consulta Certificado Aseguramiento
    public function certificadoAseguramiento(Request $request, $cpath, $id, $npoliza)
    {
        /* ----------------------------------------------------------------
         * 0. Datos base de la póliza y actividades
         * ---------------------------------------------------------------- */
        $policySort = PolicySort::findOrFail($npoliza);
        $activityIds = Activity::where('parent_id', $policySort->activity_id)
            ->where('service_id', Service::SERVICE_REPORT_TAKEN_FORM_MNK)
            ->pluck('id');


        $ultimaPlanillaActivityId = $activityIds->max();

        /* ----------------------------------------------------------------
         * 1. Sub-query: planillas relacionadas (con filtros opcionales)
         * ---------------------------------------------------------------- */
        $spreadsheetsQuery = PolicySpreadsheet::query()
            ->whereIn('activity_id', $activityIds);

        if ($request->filled('fecha_inicio')) {
            $spreadsheetsQuery->whereDate('created_at', '>=', $request->input('fecha_inicio'));
        }
        if ($request->filled('fecha_fin')) {
            $spreadsheetsQuery->whereDate('created_at', '<=', $request->input('fecha_fin'));
        }

        // ⇣ obtenemos IDs una sola vez
        $planillaIds = $spreadsheetsQuery->pluck('id');              // colección de ids
        $ultimaPlanillaId = $planillaIds->max();                     // id más reciente



        /* ----------------------------------------------------------------
         * 2. Sub-query: último registro por afiliado dentro de esas planillas
         *    (un solo SELECT con GROUP BY)
         * ---------------------------------------------------------------- */
        $latestIds = DB::table('policy_spreadsheet_affiliates')
            ->selectRaw('MAX(id) as id')
            ->whereIn('policy_spreadsheet_id', $planillaIds)
            ->groupBy('identification_number');

        /* ----------------------------------------------------------------
         * 3. Query principal de afiliados con estado calculado
         *    (todo en SQL; luego paginamos)
         * ---------------------------------------------------------------- */
        $affiliatesQuery = PolicySpreadsheetAffiliate::query()
            ->whereIn('id', $latestIds)                               // sólo el último de cada afiliado
            ->whereIn('policy_spreadsheet_id', $planillaIds)
            ->select('*')
            ->selectRaw(
                'CASE WHEN policy_spreadsheet_id = ? THEN "Activo" ELSE "Inactivo" END AS computed_status',
                [$ultimaPlanillaId]
            );


        /* ---------------- filtros adicionales ---------------- */
        if ($request->filled('nombre_Trabajador')) {
            $name = $request->input('nombre_Trabajador');
            $affiliatesQuery->where('first_name', 'like', "%{$name}%");
        }

        if ($request->filled('id_Trabajador')) {
            $idTrab = $request->input('id_Trabajador');
            $affiliatesQuery->where('identification_number', 'like', "%{$idTrab}%");
        }

        $estado = $request->input('estado', 'Activo');
        if ($estado !== 'Todos') {
            $affiliatesQuery->whereRaw(
                'LOWER(CASE WHEN policy_spreadsheet_id = ? THEN "activo" ELSE "inactivo" END) = ?',
                [$ultimaPlanillaId, strtolower($estado)]
            );
        }


        /* ----------------------------------------------------------------
         * 4. Paginación → sólo los registros ya filtrados
         * ---------------------------------------------------------------- */
        $perPage = $request->input('per_page', 10);

        $allAffiliates = $affiliatesQuery
            ->orderByDesc('created_at')
            ->paginate($perPage)
            ->appends($request->query());             // mantiene query-string


        return view('services.policy_sort.holder_policy.components.table_aseguramiento', [
            'id' => $id,
            'npoliza' => $npoliza,
            'policySelected' => $policySort,
            'allAffiliates' => $allAffiliates,       // paginado
            'planillaActivityId' => $ultimaPlanillaActivityId,
            'active' => 'certificado_aseguramiento',
        ]);
    }


    public function sqsCertificateAffiliate(Request $request, $cpath)
    {
        $affiliates = $request->input('Affiliates', []);

        if (empty($affiliates)) {
            return response()->json(['error' => 'No se recibieron afiliados.'], 400);
        }

        $results = [];
        // Procesar en lotes de 100 registros (ajusta el tamaño del chunk según tus necesidades)
        $chunks = array_chunk($affiliates, 100);

        foreach ($chunks as $chunk) {
            foreach ($chunk as $affiliateData) {
                try {
                    // Envía el mensaje a SQS para cada afiliado
                    $this->massiveController->sendReportCertificateSQS($affiliateData);

                    $results[] = [
                        'affiliate_spreadsheet_id' => $affiliateData['affiliate_spreadsheet_id'] ?? null,
                        'status' => 'success'
                    ];
                } catch (Exception $e) {
                    $results[] = [
                        'affiliate_spreadsheet_id' => $affiliateData['affiliate_spreadsheet_id'] ?? null,
                        'status' => 'error',
                        'message' => $e->getMessage()
                    ];
                }
            }
        }

        return response()->json($results);
    }

    public function sqsCertificateAffiliateMassive(Request $request, $cpath)
    {
        $affiliatesPlanilla = $request->input('activity_planilla');

        if (!$affiliatesPlanilla) {
            return response()->json(['error' => 'No se recibieron afiliados.'], 400);
        }

        try {
            // Envía el mensaje a SQS para cada afiliado
            $this->massiveController->sendReportCertificateMassiveSQS($affiliatesPlanilla);

            $results[] = [
                'activity_planilla' => $affiliatesPlanilla ?? null,
                'status' => 'success'
            ];
        } catch (Exception $e) {
            $results[] = [
                'activity_planilla' => $affiliatesPlanilla ?? null,
                'status' => 'error',
                'message' => $e->getMessage()
            ];
        }

        return response()->json($results);
    }

    public function downloadDocumentsAsegurado(Request $request, $cpath, $id)
    {


        try {
            $policySpreadsheet = PolicySpreadsheet::with('activity.parent_activity')->find($request->policy_spreadsheet_id);

            if (!$policySpreadsheet) {
                throw new Exception('No se encontró planilla', 404);
            }

            $activity = $policySpreadsheet->activity->parent_activity;

            $policy_sort = PolicySort::where('activity_id', $activity->id)->first();

            $affiliate = Affiliate::where('id', $id)->first();

            $period = $this->dataAsegurado($affiliate, $policy_sort);

            $pdf = $this->documentsAsegurado($activity, $policy_sort, $period, $affiliate);

            // 8. Construir un nombre de archivo único
            $timestamp = now()->format('YmdHis');
            $fileName = "certificado-afiliacion-{$affiliate->id}-{$timestamp}.pdf";

            // 9. Devolver el PDF como descarga
            return $pdf->download($fileName);
        } catch (Exception $e) {

            return response()->json([
                'status' => 'error',
                'message' => 'Ocurrió un error: ' . $e->getMessage()
            ], 500);
        }
    }

    private function documentsAsegurado($activity_policy, $policy_sort, $period, $affiliate)
    {

        date_default_timezone_set('America/Costa_Rica');
        $horaActual = new DateTime();
        $horaFormateada = $horaActual->format('H:i:s');
        $fechaActual = $horaActual->format('d/m/Y');

        $datos = [
            'activity' => $activity_policy,
            'watermark' => false,
            'horaFormateada' => $horaFormateada,
            'fechaActual' => $fechaActual,
            'policy_sort' => $policy_sort,
            'period' => $period,
            'affiliate' => $activity_policy->affiliate,
            'affiliateData' => $affiliate,
        ];


        $pdf = PDF::loadView("table.certificates.docs.secure_certificate_pdf", $datos);

        return $pdf;
    }

    private function dataAsegurado($affiliate, $policy_sort)
    {
        //Consultar planilla en la cual se encuentra el afiliado
        $spreadSheetAffiliate = PolicySpreadsheetAffiliate::where('affiliate_id', $affiliate->id)
            ->orderBy('id', 'desc')
            ->pluck('policy_spreadsheet_id')
            ->first();

        if (!$spreadSheetAffiliate) {
            throw new Exception('No se encontró planilla para el afiliado.', 404);
        }
        //consultamos la ultima planilla a la cual pertenece el afiliado
        $spreadSheet = PolicySpreadsheet::where('id', $spreadSheetAffiliate)
            ->orderBy('id', 'desc')
            ->first(['activity_id', 'created_at']);

        //consultamos todas las actividades de planilla de la poliza
        $ActivitySpreadsheet = Activity::where('service_id', Service::SERVICE_REPORT_TAKEN_FORM_MNK)
            ->where('parent_id', $policy_sort->activity_id)
            ->get();

        //Calcular el periodo al cual pertenece la planilla
        $position = $ActivitySpreadsheet->search(function ($item) use ($spreadSheet) {
            return $item->id === $spreadSheet->activity_id;
        });

        switch ($position) {
            case 0:
                $month = $spreadSheet->created_at;
                $date = Carbon::parse($month)->formatLocalized('%B');
                $period = "Emisión";
                break;
            default:
                $month = $spreadSheet->created_at;
                $date = Carbon::parse($month);
                $previousMonth = $date->subMonth()->formatLocalized('%B');
                $previousMonth = ucfirst($previousMonth);
                $period = $previousMonth . ' ' . $date->format('Y');
                break;
        }

        return $period;
    }

    public function emailAsegurado(Request $request, $cpath, $id)
    {
        DB::beginTransaction();

        try {

            $policySpreadsheet = PolicySpreadsheet::with('activity.parent_activity')->find($request->policy_spreadsheet_id);

            if (!$policySpreadsheet) {
                throw new Exception('No se encontró planilla', 404);
            }

            $activity = $policySpreadsheet->activity->parent_activity;



            $policy_sort = PolicySort::where('activity_id', $activity->id)->first();
            $client = Client::query()->where('path', $cpath)->firstOrFail();
            $affiliate = Affiliate::where('id', $id)->first();

            $period = $this->dataAsegurado($affiliate, $policy_sort);

            $pdf = $this->documentsAsegurado($activity, $policy_sort, $period, $affiliate);


            $subject = "Constancia de aseguramiento de " . mb_convert_case($affiliate->first_name ?? '', MB_CASE_TITLE, 'UTF-8');



            $document = (new DateTime())->format('Y-m-d-H-i-s');
            $fileName = "certificado-afiliacion-{$affiliate->id}-{$document}.pdf";
            $filePath = "activity_action_document/{$fileName}";


            Storage::disk('s3')->put($filePath, $pdf->output());

            $emails = $request->emails;

            $emailBody = " ¡Buen día, " . mb_convert_case($affiliate->first_name ?? '', MB_CASE_TITLE, 'UTF-8') . "!
            
                           Nos complace adjuntarle su constancia de aseguramiento correspondiente a la póliza del Seguro Obligatorio de Riesgos del Trabajo " . $policy_sort->formatNumberConsecutive() . ", a nombre de " . mb_convert_case($activity->affiliate->full_name ?? '', MB_CASE_TITLE, 'UTF-8') . ".

                           Por favor, si tiene alguna duda o consulta al respecto, contáctenos al correo electrónico <EMAIL> o al teléfono 4102-7600. ¡Para nosotros será un gusto servirle!

                           Nuestro propósito es fortalecer la prevención en salud y seguridad laboral del país, así como proteger a sus colaboradores en el momento que más lo necesitan, generando siempre bienestar.
                        ";

            $text = [
                "text" => $emailBody,
                "sender" => "Área de Aseguramiento <br>
            Seguro Obligatorio de Riesgos del Trabajo <br>
            MNK Seguros"
            ];
            $attachments = [
                [
                    'path' => $filePath,
                    'name' => basename($filePath),
                    'type' => 'PDF'
                ]
            ];

            $this->sendEmail($emails, $subject, $text, $attachments, $client, $policy_sort->activity_id, null, $policy_sort);

            DB::commit();

            return response()->json([
                'status' => 'success',
                'message' => 'Registro creado y email enviado exitosamente',
            ]);
        } catch (Exception $e) {
            DB::rollback();
            return response()->json([
                'status' => 'error',
                'message' => 'Ocurrió un error al enviar el correo: ' . $e->getMessage()
            ], 500);
        }
    }


    private function sendEmail($emails, $subject, $text, $attachments, $client, $id, $activityAction = null, $policySort)
    {
        $mailSent = new SendDocumentDataBase(
            implode(',', $emails),         // Correos a enviar
            $subject,                      // Asunto del correo
            "<EMAIL>",            // Remitente
            $subject,                      // Asunto
            $text,                         // Cuerpo del email
            "<EMAIL>",  // Email de respuesta
            $attachments,                  // Archivos adjuntos
            "send_document_db",            // Tipo de envío
            $client,                       // Información del cliente
            request()->getHost(),          // Dominio
            $id,                           // ID de la actividad
            null,           // ID de la acción de la actividad
            $policySort->activity->service->id // ID del servicio
        );

        // Capturar el resultado del envío
        $result = $mailSent->sendMail();

        //Registramos los datos del correo enviado para la trazabilidad
        $mailBoardController = new MailBoardController();
        $mailBoardController->createRegisterMail(
            $policySort->activity->id,
            $policySort->activity->service->id,
            $policySort->consecutive,
            'Tomador',
            mb_convert_case(mb_strtolower($policySort->activity->affiliate->full_name ?? ''), MB_CASE_TITLE, "UTF-8"),
            $policySort->activity->affiliate->doc_number,
            $subject,
            $text,
            $emails,
            $result,
            $attachments
        );
    }

    /**
     * Aplica los filtros de fecha a la consulta
     */
    private function applyDateFilters($query, $startDate, $endDate, $column)
    {
        if (!empty($startDate)) {
            $startDate = Carbon::createFromFormat('Y-m-d', $startDate)->startOfDay();
        }

        if (!empty($endDate)) {
            $endDate = Carbon::createFromFormat('Y-m-d', $endDate)->endOfDay();
        }

        if (!empty($startDate) && !empty($endDate)) {
            // Filtrar entre las fechas de inicio y fin
            $query->whereBetween($column, [$startDate, $endDate]);
        } elseif (!empty($startDate)) {
            // Filtrar desde la fecha de inicio
            $query->where($column, '>=', $startDate);
        } elseif (!empty($endDate)) {
            // Filtrar hasta la fecha de fin
            $query->where($column, '<=', $endDate);
        }

        return $query;
    }

    public function downloadSpreadsheetPdf(Request $req, $cpath, $nspreedsheet)
    {
        // set_time_limit(0);
        // ini_set('memory_limit', '800M');


        $data = PolicySpreadsheet::with([
            'policy_spreadsheet_affiliate' => function ($query) {
                $query->where('temporal', 0);
            },
            'activity'
        ])
            ->where('id', $nspreedsheet)
            ->first();

        $numeroTotalPoliza = PolicySpreadsheet::where('activity_id', $data->activity_id)->count();

        $policy = Activity::where('id', $data->activity->parent_id)->first();

        $jsonPath = public_path('js/paises.json');
        $jsonContent = File::get($jsonPath);
        $countries = json_decode($jsonContent, true);


        foreach ($data->policy_spreadsheet_affiliate as &$employee) {
            $employee['nationality'] = $this->getCountryName($employee['nationality'], $countries);
        }


        $pdf = PDF::loadView(
            'services.policy_sort.docs.planilla_dinamic',
            [
                'watermark' => false,
                'data' => $data,
                'policy' => $policy,
                'total' => $numeroTotalPoliza
            ]
        );

        $pdf->setPaper('A4', 'landscape');



        return $pdf->download('Planilla_' . $nspreedsheet . '.pdf'); // Esto enviará el archivo PDF al cliente

    }

    public function consultaccidentrate(Request $request, $cpath, $id, $npoliza)
    {
        $state_service_list = ['Activo', 'Inactivo', 'Pendiente', 'Suspendido', 'Finalizado'];
        $policySort = PolicySort::where('id', $npoliza)->first();

        if (empty($policySort)) {
            return redirect()->back()->with('error', 'No hay una póliza seleccionada');
        }

        $gisDataQuery = $this->getGisDataQuery($request, $npoliza);

        if (!$gisDataQuery) {
            return redirect()->back()->with('error', 'No se pudo obtener la consulta de datos GIS.');
        }

        // Obtener los resultados paginados
        $gisData = $gisDataQuery->paginate(10)
            ->appends(request()->only('worker_id', 'severity', 'state_service', 'action_start_date', 'action_end_date'));

        return view('services.policy_sort.holder_policy.menu.consult_accident_rate', [
            'id' => $id,
            'npoliza' => $npoliza,
            'activities' => $gisData,
            'state_services' => $state_service_list,
            'type_currency' => $policySort->type_currency ?? '',
            'active' => 'consultar_siniestralidad'
        ]);
    }

    //Consulta información siniestralidad
    private function getGisDataQuery(Request $request, $npoliza)
    {
        $workerId = $request->input('worker_id');
        $severity = $request->input('severity');
        $stateService = $request->input('state_service');
        $start_date = $request->input('action_start_date_submit');
        $end_date = $request->input('action_end_date_submit');
        // Consultar la información de la póliza
        //$policySort = PolicySort::where('id', $npoliza)->first();
        $policySort = PolicySort::where('id', $npoliza)->select('activity_id')->first();

        if (empty($policySort)) {
            return null;
        }

        // Consultar actividad de la póliza
        $activityIds = Activity::where('parent_id', $policySort->activity_id)
            ->where('service_id', Service::SERVICE_GIS_SORT_MNK)
            ->pluck('id');

        // Consultar tabla de GIS SORT con left join y aplicar filtros
        $gisDataQuery = GisSort::whereIn('gis_sort.activity_id', $activityIds)
            ->join('activities', 'gis_sort.activity_id', '=', 'activities.id')
            ->leftJoin('states', 'activities.state_id', '=', 'states.id')
            ->leftJoin('affiliates', 'activities.affiliate_id', '=', 'affiliates.id')
            ->leftJoin('gis_diagnostics', 'gis_sort.id', '=', 'gis_diagnostics.gis_id')
            //Actividades de prestaciones medicas
            ->leftJoin('activities as activities_medical', function ($join) {
                $join->on('activities_medical.parent_id', '=', 'activities.id')
                    ->whereIn('activities_medical.service_id', [
                        Service::SERVICE_PE_RECOGNITION_EXPENSES_MNK,
                        Service::SERVICE_PE_IP_SORT_MNK,
                        Service::SERVICE_PE_MPT_SORT_MNK,
                        Service::SERVICE_MEDICAL_SERVICES_SORT_MNK,
                        Service::SERVICE_PE_IT_SORT_MNK
                    ]);
            })

            //Activities de incapacidades temporales
            ->leftJoin('activities as activities_incapacity', function ($join) {
                $join->on('activities_incapacity.id', '=', 'activities_medical.id');
            })

            //Costo por incapacidad temporal
            ->leftJoin('activities as ait', function ($join) {
                $join->on('ait.parent_id', '=', 'activities_medical.id')
                    ->where('ait.service_id', Service::SERVICE_PE_IT_SORT_MNK);
            })
            ->leftJoin('pe_it_sorts as pe_it', 'pe_it.activity_id', '=', 'ait.id')
            ->leftJoin('peit_inability_sorts as peis', 'peis.pe_it_sort_id', '=', 'pe_it.id')

            //Costo Reconocimiento de gastos
            ->leftJoin('pe_recognition_expenses as pre', 'pre.activity_id', '=', 'activities_medical.id')

            //Costos Incapacidad permanente
            ->leftJoin('peip_sorts as peip', 'peip.activity_id', '=', 'activities_medical.id')

            //Costos por incapacidad por muerte de la persona
            ->leftJoin('pempt_sorts as pempt', 'pempt.activity_id', '=', 'activities_medical.id')

            // Esto solo valida si tiene o no incapacidad permanente
            ->leftJoin('activities as children', function ($join) {
                $join->on('children.parent_id', '=', 'activities.id')
                    ->where('children.service_id', 86);  // Solo traer hijos con service_id = 86
            })
            ->select(
                'gis_sort.*',
                'activities_incapacity.state_id as state_incapacity',
                'activities_incapacity.id as activity_incapcity_id',
                DB::raw('SUM(peis.days_it) as total_days_it'),
                DB::raw('SUM(peis.amount_pay) as total_amount_pay'),
                'activities.state_id',
                'states.name as state_name',
                'affiliates.full_name',
                DB::raw('COUNT(children.id) > 0 as has_permanent_disability'),
                DB::raw('COALESCE(gis_diagnostics.casedata_diagnosis, \'Sin diagnóstico\') as casedata_diagnosis'),
                DB::raw('SUM(pre.accepted_value_of_recognition) as value_recognition'),
                DB::raw('SUM(peip.payinfo_monthy_amount_pay) as monthy_amount_pay'),
                DB::raw('SUM(peip.payinfo_total_years_pay) as total_years_pay'),
                DB::raw('SUM(pempt.total_month_to_pay) as month_to_pay'),
                'pempt.total_years_to_pay as years_to_pay',
                'activities.id as act'
            )
            ->groupBy('affiliates.doc_number')
            ->where(function ($query) {
                $query->whereIn('gis_diagnostics.id', function ($subQuery) {
                    // Selecciona el ID más bajo de gis_diagnostics por gis_id
                    $subQuery->selectRaw('MIN(id)')
                        ->from('gis_diagnostics')
                        ->whereColumn('gis_diagnostics.gis_id', 'gis_sort.id');
                })
                    ->orWhereNull('gis_diagnostics.id'); // Incluye registros sin diagnósticos
            });

        // Aplicar filtros condicionales
        if (!empty($workerId)) {
            $gisDataQuery->where('gis_sort.number_identification_affiliate', $workerId);
        }

        //Severidad
        if (!empty($severity)) {
            $gisDataQuery->where('gis_sort.severity', $severity);
        }

        //Estado del servicio
        if (!empty($stateService)) {
            if (is_array($stateService)) {
                $gisDataQuery->whereIn('gis_sort.current_status', $stateService);
            } else {
                $gisDataQuery->where('gis_sort.current_status', $stateService);
            }
        }
        $filteredData = $this->applyDateFilters($gisDataQuery, $start_date, $end_date,'gis_sort.created_at');
        return $filteredData;
    }

    public function consultarInclusion(Request $request, $cpath, $id, $npoliza)
    {

        // Buscar la póliza por su id
        $policy = PolicySort::findOrFail($npoliza);

        // Recoger el parámetro de identificación del trabajador
        $identificacion = $request->input('identificacion_trabajador');
        $name = $request->input('nombre_trabajador');
        $desde = $request->input('vigencia_date');
        $hasta = $request->input('vigencia_hasta_date');

        // Inicializar el reporte de afiliados
        $affiliateReport = [];

        // Buscar la última actividad del reporte de planilla tomador mediante la póliza
        $activityReport = Activity::where('parent_id', $policy->activity_id)
            ->where('service_id', Service::SERVICE_REPORT_TAKEN_FORM_MNK)
            ->latest()
            ->first();

        if ($activityReport) {
            // Buscar la última planilla tomador mediante la actividad
            $lastReport = PolicySpreadsheet::where('activity_id', $activityReport->id)->first();

            if ($lastReport) {
                // Construir la consulta inicial para los afiliados
                $affiliateReportQuery = PolicySpreadsheetAffiliate::leftJoin('affiliates', 'policy_spreadsheet_affiliates.affiliate_id', '=', 'affiliates.id')
                    ->where('policy_spreadsheet_affiliates.policy_spreadsheet_id', $lastReport->id)
                    ->where('policy_spreadsheet_affiliates.temporal', 1)
                    ->select('policy_spreadsheet_affiliates.*')
                    ->orderBy('policy_spreadsheet_affiliates.created_at', 'desc');

                // Filtrar por identificación si se proporciona
                if (!empty($identificacion)) {
                    $affiliateReportQuery->where('policy_spreadsheet_affiliates.identification_number', $identificacion);
                }

                if (!empty($name)) {
                    $affiliateReportQuery->where('policy_spreadsheet_affiliates.name', 'LIKE', '%' . $name . '%');
                }

                if (!empty($desde) && !empty($hasta)) {
                    $affiliateReportQuery->whereBetween('policy_spreadsheet_affiliates.created_at', [$desde, $hasta]);
                } elseif (!empty($desde)) {
                    $affiliateReportQuery->where('policy_spreadsheet_affiliates.created_at', '>=', $desde);
                } elseif (!empty($hasta)) {
                    $affiliateReportQuery->where('policy_spreadsheet_affiliates.created_at', '<=', $hasta);
                }
                $affiliateReport = $affiliateReportQuery->paginate(10);
            }
        }

        // Retornar la vista con los datos
        return view('services.policy_sort.holder_policy.components.table_consultar_inclusion', [
            'id' => $id,
            'npoliza' => $npoliza,
            'affiliateReport' => $affiliateReport,
            'active' => 'consultar_inclusion',
            'policy' => $policy

        ]);
    }

    //Desacarga de excel de consulta de siniestralidad
    public function downloadExcelConsultAccidentRate(Request $request, $cpath, $npoliza)
    {
        $policySort = PolicySort::where('id', $npoliza)->first();

        if (empty($policySort)) {
            return redirect()->back()->with('error', 'No hay una póliza seleccionada');
        }

        $gisDataQuery = $this->getGisDataQuery($request, $npoliza);

        if (!$gisDataQuery) {
            return redirect()->back()->with('error', 'No se pudieron obtener los datos para el Excel.');
        }

        // Definir los nombres de las columnas
        $columns = [
            'Número de caso',
            'Id trabajador',
            'Nombre',
            'Fecha del accidente',
            'Costo por reconocimiento de gastos (colones)',
            'Costo por gastos médicos (Colones)',
            'Costo por incapacidad temporal (colones)',
            'Costo por incapacidad permanente (colones) (cuando exista valoración de daño y fijación de renta)',
            'Costo por incapacidad permanente (colones) (en caso de fallecimiento)',
            'Costo por recargo administrativo (colones) 10%',
            'Costo total del caso (colones)',
            'Días de incapacidad',
            'Estado del caso (aceptado)',
            'Severidad'
        ];

        return Excel::create('Siniestralidad_' . $npoliza, function ($excel) use ($gisDataQuery, $columns, $policySort) {
            $excel->setTitle('Siniestralidad');
            $excel->setCreator('RenApp');
            $excel->setDescription('Siniestralidad exportada desde el sistema.');

            $excel->sheet('Accidentes', function ($sheet) use ($gisDataQuery, $columns, $policySort) {
                $sheet->appendRow($columns);
                $gisData = $gisDataQuery->get();

                $totalcostRecognitionExpense = 0;
                $totalcostIncapacityTemporal = 0;
                $totalcostIncapacityPermanent = 0;
                $totalcostDisabilityToDeath = 0;
                $totalAdministrativeSurcharge = 0;
                $totalCostCase = 0;

                foreach ($gisData as $row) {
                    $costRecognitionExpense = $row->value_recognition;
                    //Agregar costo de citas medicas
                    $costIncapacityTemporalRaw = $row->total_amount_pay;
                    $costIncapacityPermanent = ($row->monthy_amount_pay * 12) * $row->total_years_pay;
                    $costDisabilityToDeath =  ($row->month_to_pay * 12) * $row->years_to_pay;

                    $administrativeSurcharge = (($costRecognitionExpense ?? 0)
                            + ($costIncapacityTemporalRaw ?? 0)
                            + ($costIncapacityPermanent ?? 0)
                            + ($costDisabilityToDeath ?? 0)) * 0.10;

                    $totalCost = (($costRecognitionExpense ?? 0)
                        + ($costIncapacityTemporalRaw ?? 0)
                        + ($costIncapacityPermanent ?? 0)
                        + ($costDisabilityToDeath ?? 0)
                        + $administrativeSurcharge);

                    $rowData = [
                        empty($row->formatCaseNumberIfReported()) ? ($row->consecutive_gis ?? '') : $row->formatCaseNumberIfReported(), //Nùmero de caso o nùmero de aviso
                        $row->number_identification_affiliate, // Id trabajador
                        ucwords(strtolower($row->full_name)), // Nombre del trabajador
                        $row->date_accident,              // Fecha del accidente
                        $this->formatCurrencyValue($costRecognitionExpense, $policySort->type_currency), //Reconocimiento de gastos
                        '',
                        $this->formatCurrencyValue($costIncapacityTemporalRaw, $policySort->type_currency), //Costos Incapacidad temporal
                        $this->formatCurrencyValue($costIncapacityPermanent, $policySort->type_currency), //Costos Incapacidad permanente
                        $this->formatCurrencyValue($costDisabilityToDeath, $policySort->type_currency), //Costos por incapacidad por muerte de la persona
                        $this->formatCurrencyValue($administrativeSurcharge, $policySort->type_currency), // Costo por recargo administrativo (colones) 10%
                        $this->formatCurrencyValue($totalCost, $policySort->type_currency), //Costo total del caso (colones)
                        $row->total_days_it ?? '', // Dias de incapacidad
                        $row->state_name ?? 'Caso aceptado', // Estado del caso
                        AppServiceProvider::$SEVERITY[$row->severity] ?? 'Severidad desconocida', //Severidad
                    ];

                    // Agregar cada fila de datos al Excel
                    $sheet->appendRow($rowData);
                    // Acumular totales (con los valores brutos)
                    $totalcostRecognitionExpense += ($costRecognitionExpense ?? 0);
                    $totalcostIncapacityTemporal += ($costIncapacityTemporalRaw ?? 0);
                    $totalcostIncapacityPermanent += ($costIncapacityPermanent ?? 0);
                    $totalcostDisabilityToDeath += ($costDisabilityToDeath ?? 0);
                    $totalAdministrativeSurcharge += $administrativeSurcharge;
                    $totalCostCase += $totalCost;
                }

                // Datos para la fila de totales
                $totalesRow = [
                    '', '', '', 'TOTALES',
                    $this->formatCurrencyValue($totalcostRecognitionExpense, $policySort->type_currency),
                    '',
                    $this->formatCurrencyValue($totalcostIncapacityTemporal, $policySort->type_currency),
                    $this->formatCurrencyValue($totalcostIncapacityPermanent, $policySort->type_currency),
                    $this->formatCurrencyValue($totalcostDisabilityToDeath, $policySort->type_currency),
                    $this->formatCurrencyValue($totalAdministrativeSurcharge, $policySort->type_currency),
                    $this->formatCurrencyValue($totalCostCase, $policySort->type_currency),
                    '', ''
                ];

                $sheet->appendRow($totalesRow);
                $lastRow = $sheet->getHighestRow();
                $sheet->cells("A{$lastRow}:{$sheet->getHighestColumn()}{$lastRow}", function ($cells) {
                    $cells->setFontWeight('bold');
                });
            });
        })->export('xlsx');
    }


    //Formato de moneda
    private function formatCurrencyValue($value, $currencyType)
    {
        if ($value !== null) {
            return (AppServiceProvider::$MONEY_TYPE[$currencyType]['symbol'] ?? '') . number_format($value, 2, ',', '.');
        } else {
            return (AppServiceProvider::$MONEY_TYPE[$currencyType]['symbol'] ?? '') . number_format(0, 2, ',', '.');
        }
    }

    //solicitud de variaciones
    public function policyvaliditychange(Request $request, $cpath, $id, $npoliza)
    {
        return view(
            'services.policy_sort.holder_policy.menu.policy_validity_change',
            [
                'id' => $id,
                'npoliza' => $npoliza,
                'active' => 'cambio_vigencia_poliza'
            ]
        );
    }

    public function basiccontactinformation(Request $request, $cpath, $id, $npoliza)
    {
        return view(
            'services.policy_sort.holder_policy.menu.basic_contact_information',
            [
                'id' => $id,
                'npoliza' => $npoliza,
                'active' => 'datos_basicos_contacto'
            ]
        );
    }

    public function otherdata(Request $request, $cpath, $id, $npoliza)
    {
        return view(
            'services.policy_sort.holder_policy.menu.other_data',
            [
                'id' => $id,
                'npoliza' => $npoliza,
                'active' => 'otros_datos'
            ]
        );
    }

    public function changepaymentmethod(Request $request, $cpath, $id, $npoliza)
    {
        return view(
            'services.policy_sort.holder_policy.menu.change_payment_method',
            [
                'id' => $id,
                'npoliza' => $npoliza,
                'active' => 'cambio_forma_pago'
            ]
        );
    }

    public function cancellation(Request $request, $cpath, $id, $npoliza)
    {
        return view(
            'services.policy_sort.holder_policy.menu.cancellation',
            [
                'id' => $id,
                'npoliza' => $npoliza,
                'active' => 'cancelación'
            ]
        );
    }

    public function rehabilitation(Request $request, $cpath, $id, $npoliza)
    {
        return view(
            'services.policy_sort.holder_policy.menu.rehabilitation',
            [
                'id' => $id,
                'npoliza' => $npoliza,
                'active' => 'rehabilitación'
            ]
        );
    }

    public function statisticsandreporting(Request $request, $cpath, $id, $npoliza)
    {
        try {

            //capturar póliza sort
            $policySort = PolicySort::where('id', $npoliza)->first();

            if (!$policySort) {
                $errors = "No existe la póliza de sort.";
                throw new Exception($errors); // Lanzar excepción con el error
            }

            //consultar actividad de la póliza
            $activity_policy = Activity::where('id', $policySort->activity_id)->first();

            //validación y tirar error
            if (!$activity_policy) {
                $errors = "No se encuentra la actividad asociada a la póliza.";
                throw new Exception($errors); // Lanzar excepción con el error
            }

            //capturar los GIS asociados a la póliza
            $list_gis_activity = Activity::where('parent_id', $activity_policy->id)
                ->where('service_id', Service::SERVICE_GIS_SORT_MNK)->get();

            // Actividades asociadas al reporte de planilla para tomador por la actividad de la póliza (padre)
            $activity_spreadsheet = Activity::where('parent_id', $policySort->activity_id)
                ->where('service_id', Service::SERVICE_REPORT_TAKEN_FORM_MNK)
                ->orderBy('created_at', 'desc') // Ordena por created_at en orden descendente
                ->first(); // Obtiene la última actividad

            // Planilla reportada
            $spreadsheet_id = PolicySpreadsheet::where('activity_id', $activity_spreadsheet->id)->value('id');

            // Capturar los afiliados asociados a la actividad
            $spreadsheet_affiliates = PolicySpreadsheetAffiliate::where('policy_spreadsheet_id', $spreadsheet_id)->get();

            // Verifica si hay afiliados antes de proceder
            if ($spreadsheet_affiliates->isNotEmpty()) {
                // Total de asegurados
                $count_affiliates = $spreadsheet_affiliates->count();

                // Contar hombres y mujeres directamente en la base de datos
                $count_men = PolicySpreadsheetAffiliate::where('policy_spreadsheet_id', $spreadsheet_id)
                    ->where('gender', 'M')
                    ->count();

                $count_women = PolicySpreadsheetAffiliate::where('policy_spreadsheet_id', $spreadsheet_id)
                    ->where('gender', 'F')
                    ->count();
            } else {
                // Valores por defecto si no hay actividades
                $count_affiliates = 0;
                $count_men = 0;
                $count_women = 0;
            }

            //Cantidad de accidentes laborales
            $count_gis = $list_gis_activity->count();

            // Crear los gráficos usando la función createPieChart() conparametros
            $chartData = [
                // Primer gráfico: Cantidad de asegurados por sexo
                $this->createPieChart(
                    'chart1', // ID del canvas
                    'Cantidad de asegurados por sexo', // Etiqueta del gráfico
                    ['Hombres', 'Mujeres'], // Etiquetas de los segmentos
                    [$count_men, $count_women], // Datos de los segmentos
                    ['rgb(255, 99, 132)', 'rgb(54, 162, 235)'] // Colores de fondo
                ),

                // Segundo gráfico: Siniestralidad total (Accidentes reportados/cantidad de asegurados)
                $this->createPieChart(
                    'chart2', // ID del canvas
                    'Siniestralidad total', // Etiqueta del gráfico
                    ['Accidentes reportados', 'Cantidad de asegurados'], // Etiquetas de los segmentos
                    [$count_gis, $count_affiliates], // Datos de los segmentos
                    ['rgb(75, 192, 192)', 'rgb(153, 102, 255)'] // Colores de fondo
                )
            ];

            //Otros datos para el reporte del tomador

            //Prima total anual (En Colones)
            $amount_policy_colon = ($policySort->type_currency == 'CRC') ? $policySort->amount_policy ?? 0 : (($policySort->amount_policy ?? 0) * 510.66);
            $amount_policy_colon = number_format($amount_policy_colon, 2, ',', '.');
            $amount_policy_colon = "₡ " . $amount_policy_colon;

            //Prima total anual (En Dólares)
            $amount_policy_dolars = ($policySort->type_currency == 'USD') ? $policySort->amount_policy ?? 0 : (($policySort->amount_policy ?? 0) / 510.66);
            $amount_policy_dolars = number_format($amount_policy_dolars, 2, ',', '.');
            $amount_policy_dolars = "$ " . $amount_policy_dolars;

            //Cantidad de abonos
            // Obtener las actividades de pagos
            $paymentActivities = Activity::where('parent_id', $activity_policy->id)
                ->where('service_id', Service::SERVICE_POLICY_SORT_COLLECTION_MNK)
                ->get();

            // Extraer los ids de esas actividades
            $paymentIds = $paymentActivities->pluck('id')->toArray();

            //Cantidad de abonos colones - dólares
            $number_payments_colon = 0;
            $number_payments_dolars = 0;

            // Sumar la columna total_amount de los registros cuyo activity_id esté en $paymentIds
            $totalAmountPayment = DB::table('policy_sort_collections as p')
                ->leftJoin(DB::raw('
                    (
                        SELECT date, MIN(rate) AS rate
                        FROM trm_rates
                        GROUP BY date
                    ) as t
                '), DB::raw('DATE(p.transaction_date)'), '=', 't.date')
                            ->selectRaw('
                    SUM(p.total_amount) AS total_amount_payment,
                    SUM(p.total_amount / NULLIF(t.rate, 0)) AS total_amount_usd
                ')
                ->whereIn('p.activity_id', $paymentIds)
                ->whereIn('p.type_receipt', [
                    'emission',
                    'monthly_payment',
                    'quarterly_payment',
                    'semiannual_payment'
                ])
                ->first();

            //validar si existen abonos
            if ($totalAmountPayment) {
                $number_payments_colon = $totalAmountPayment->total_amount_payment;
                $number_payments_dolars = $totalAmountPayment->total_amount_usd;
            }

            //formatear los abonos
            $number_payments_colon = number_format($number_payments_colon, 2, ',', '.');
            $number_payments_colon = "₡ " . $number_payments_colon;
            $number_payments_dolars = number_format($number_payments_dolars, 2, ',', '.');
            $number_payments_dolars = "$ " . $number_payments_dolars;

            //Monto total pagado en accidentes laborales colones - dólares
            $paid_workplace_accidents_colon = 0;
            $paid_workplace_accidents_dolars = 0;

            if ($policySort->consecutive) {
                $accounting = DB::table('accounting_entries as a')
                                    ->leftJoin(DB::raw('
                        (
                            SELECT date, MIN(rate) AS rate
                            FROM trm_rates
                            GROUP BY date
                        ) as t
                    '), DB::raw('DATE(a.created_at)'), '=', 't.date')
                                    ->selectRaw('
                        SUM(a.debit) AS costo,
                        SUM(a.debit / NULLIF(t.rate, 0)) AS costo_dolar,
                        COUNT(DISTINCT a.activity_gis) AS cantidad
                    ')
                    ->whereRaw("TRIM(LEADING '0' FROM REPLACE(a.number_policy, 'SORT-', '')) = ?", $policySort->consecutive)
                    ->where('a.cod_oper', '011')
                    ->where('a.debit', '>', 0)
                    ->limit(1)
                    ->first();

                $paid_workplace_accidents_colon = $accounting->costo;
                $paid_workplace_accidents_dolars = $accounting->costo_dolar;
            }

            //formatear los montos
            $paid_workplace_accidents_colon = number_format($paid_workplace_accidents_colon, 2, ',', '.');
            $paid_workplace_accidents_colon = "₡ " . $paid_workplace_accidents_colon;
            $paid_workplace_accidents_dolars = number_format($paid_workplace_accidents_dolars, 2, ',', '.');
            $paid_workplace_accidents_dolars = "$ " . $paid_workplace_accidents_dolars;

            //Cantidad de accidentes laborales con incapacidad
            //primero: consultar las prestaciones asociadas a la lista de GIS
            $list_medical_service = Activity::whereIn('parent_id', $list_gis_activity->pluck('id'))
                ->whereIn('service_id', [Service::SERVICE_MEDICAL_SERVICES_SORT_MNK, Service::SERVICE_MEDICAL_SERVICES_SECONDARY_CARE_SORT_MNK])
                ->get();

            //segundo: consultar las incapacidades temporales asociadas a las prestaciones
            $count_temporary_incapacities = Activity::whereIn('parent_id',
                $list_gis_activity->pluck('id')->merge($list_medical_service->pluck('id'))->all()
            )
                ->where('service_id', Service::SERVICE_PE_IT_SORT_MNK)
                ->get()
                ->unique('parent_id')
                ->count();

            return view(
                'services.policy_sort.holder_policy.menu.statistics_and_reporting',
                [
                    'id' => $id,
                    'npoliza' => $npoliza,
                    'chartData' => $chartData,
                    'count_gis' => $count_gis,
                    'count_affiliates' => $count_affiliates,
                    'amount_policy_colon' => $amount_policy_colon,
                    'amount_policy_dolars' => $amount_policy_dolars,
                    'number_payments_colon' => $number_payments_colon,
                    'number_payments_dolars' => $number_payments_dolars,
                    'paid_workplace_accidents_colon' => $paid_workplace_accidents_colon,
                    'paid_workplace_accidents_dolars' => $paid_workplace_accidents_dolars,
                    'count_temporary_incapacities' => $count_temporary_incapacities,
                    'active' => 'estadisticas_reporte'
                ]
            );
        } catch (\Exception $e) {
            // Capturar el mensaje de error
            $error = $e->getMessage();

            // Redirigir con el mensaje de error
            return redirect()->back()->withErrors($error)->withInput();
        }
    }

    //Validar las fechas de la poliza al momento de emitir la cotización
    public function validateDatePolicyEmissionFromQuotation($id)
    {

        //Buscamos la cotización
        $quotation = Quotation::where('id', $id)->firstOrFail();

        // Fecha de inicio de la póliza
        $validityFrom = Carbon::parse($quotation->validity_from);

        // Obtener la fecha actual
        $currentDate = Carbon::now()->startOfDay();

        //Si la fecha de inicio de la poliza es menor a la fecha actual recalculamos las fechas de la cotización
        if ($validityFrom->lt($currentDate)) {

            //Obtenemos la fecha de inicio de la poliza
            $quotation->validity_from;

            //Obtenermos la fecha final de la poliza
            $quotation->validity_to;

            // Calculamos la diferencia en días
            $differenceInDays = Carbon::parse($quotation->validity_from)->diffInDays(Carbon::parse($quotation->validity_to));

            // Generamos la nueva fecha, sumando esa misma diferencia a la fecha actual
            $newDate = Carbon::now()->addDays($differenceInDays);

            $quotation->validity_from = $currentDate;
            $quotation->validity_to = $newDate;
            $quotation->save();

            return true;
        }
    }

    //Validar las fechas de la poliza al momento de emitir la póliza
    public function validateDatePolicyEmissionFromPolicy($id)
    {

        //Buscamos la cotización
        $policy = PolicySort::where('id', $id)->firstOrFail();

        // Fecha de inicio de la póliza
        $validityFrom = Carbon::parse($policy->validity_from);

        // Obtener la fecha actual
        $currentDate = Carbon::now()->startOfDay();

        //Si la fecha de inicio de la poliza es menor a la fecha actual recalculamos las fechas de la póliza
        if ($validityFrom->lt($currentDate)) {

            //Obtenemos la fecha de inicio de la poliza
            $policy->validity_from;

            //Obtenermos la fecha final de la poliza
            $policy->validity_to;

            // Calculamos la diferencia en días
            $differenceInDays = Carbon::parse($policy->validity_from)->diffInDays(Carbon::parse($policy->validity_to));

            // Generamos la nueva fecha, sumando esa misma diferencia a la fecha actual
            $newDate = Carbon::now()->addDays($differenceInDays);

            $policy->validity_from = $currentDate;
            $policy->validity_to = $newDate;
            $policy->save();

            return true;
        }
    }

    //Inicar la poliza desde la cotización
    public function startPolicyIssuanceFromQuotation(Request $req, $cpath)
    {

        $client = Client::where('path', $cpath)->firstOrFail();

        //Validamos si la fecha de emision de la poliza es anterior al dia de hoy
        $res = $this->validateDatePolicyEmissionFromQuotation($req->id);

        DB::beginTransaction();

        try {

            $quotation = Quotation::where('id', $req->id)->firstOrFail();

            // $id es el id de la tabla quotation; lo usaremos para buscar el id de la actividad y el affiliate_id
            $activityQuotation = Activity::where('id', Quotation::where('id', $quotation->id)
                ->pluck('activity_id')->first())
                ->select('id', 'affiliate_id')
                ->first();

            // Crea la actividad del servicio de póliza
            $activityPolicy = new Activity;
            $activityPolicy->client_id = $client->id;
            $activityPolicy->parent_id = $activityQuotation->id;
            $activityPolicy->service_id = Service::SERVICE_POLICY_SORT_MNK;
            $activityPolicy->affiliate_id = $activityQuotation->affiliate_id;
            $activityPolicy->user_id = Auth::id();
            $activityPolicy->state_id = State::REGISTRADO;
            $activityPolicy->save();

            //Ejecutamos la acción INICIAR_EMISION_POLIZA_DESDE_POLIZA del servicio de poliza
            $description = "Póliza en proceso de emisión";
            ActionController::create($activityPolicy->id, Action::INICIAR_EMISION_POLIZA_DESDE_POLIZA, $description);

            //Ejecutamos la acción INICIAR_EMISION_POLIZA_DESDE_COTIZACION del servicio de cotización
            $description = "Cotización pendiente de emisión";
            ActionController::create($activityQuotation->id, Action::INICIAR_EMISION_POLIZA_DESDE_COTIZACION, $description);

            $uniqueCode = str_pad(mt_rand(1, 999999), 6, '0', STR_PAD_LEFT);

            //Creamos la póliza desde la cotización
            $policy_sort_data = [
                'unique_code' => $uniqueCode,
                'activity_id' => $activityPolicy->id,
                'brokerage_name' => $quotation->brokerage_name,
                'advisor_name' => $quotation->advisor_name,
                'code' => $quotation->code,
                'email' => $quotation->email,
                'doc_type' => $activityPolicy->affiliate->doc_type,
                'doc_number' => $activityPolicy->affiliate->doc_number,
                'first_name' => $activityPolicy->affiliate->first_name,
                'phones' => $activityPolicy->affiliate->phone,
                'notification_email' => $quotation->notification_email,
                'activity_economic_id' => $quotation->activity_economic_id,
                'economic_activity' => $quotation->economic_activity,
                'temporality' => $quotation->temporality,
                'validity_from' => $quotation->validity_from,
                'validity_to' => $quotation->validity_to,
                'type_currency' => $quotation->type_currency,
                'salary_projection' => $quotation->salary_projection,
                'periodicity' => $quotation->periodicity,
                'annual_calculation_amount' => $quotation->annual_calculation_amount,
                'semiannual_calculation_amount' => $quotation->semiannual_calculation_amount,
                'quarterly_calculation_amount' => $quotation->quarterly_calculation_amount,
                'monthly_calculation_amount' => $quotation->monthly_calculation_amount,
                'single_payment_value' => $quotation->single_payment_value,
                'amount_policy' => $quotation->amount_policy,
                'work_modality_id' => $quotation->work_modality_id,
                'option_asegurement' => $quotation->option_asegurement,
                "special_condition" => $quotation->special_condition,
                "special_condition_payment" => $quotation->special_condition_payment,
                "preventive_actions" => $quotation->preventive_actions,
                "intermediary_user" => $quotation->intermediary_user,
                "payroll_status" => $quotation->payroll_status,
                "change_date" => $quotation->change_date,
                "institutional_sector" => $quotation->institutional_sector
            ];

            PolicySort::create($policy_sort_data);


            DB::commit();

            //si se modificaron las fechas de la cotización
            if ($res) {
                return response()->json([
                    'continue' => false,
                    'redirect' => url("/intermediario/poliza/{$activityPolicy->id}"),
                ]);
            }

            return response()->json([
                'status' => 'success',
                'redirect' => url("/intermediario/poliza/{$activityPolicy->id}"),
                'message' => 'Campos han sido actualizados correctamente',
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['error' => 'Ocurrió un error al guardar los datos'], 500);
        }
    }

    private function registerActivityAction($activity_id, $action_id, $old_state_id, $new_state_id, $description, $user_id)
    {

        $newActivityAction = new ActivityAction();
        $newActivityAction->activity_id = $activity_id;
        $newActivityAction->action_id = $action_id;
        $newActivityAction->old_state_id = $old_state_id;
        $newActivityAction->new_state_id = $new_state_id;
        $newActivityAction->description = $description;
        $newActivityAction->old_user_id = $user_id;
        $newActivityAction->new_user_id = $user_id;
        $newActivityAction->author_id = Auth::id();
        $newActivityAction->save();

        return $newActivityAction;
    }

    private function registerActivityActionReturn($activity_id, $action_id, $old_state_id, $new_state_id, $description, $user_id)
    {
        $newActivityAction = new ActivityAction();
        $newActivityAction->activity_id = $activity_id;
        $newActivityAction->action_id = $action_id;
        $newActivityAction->old_state_id = $old_state_id;
        $newActivityAction->new_state_id = $new_state_id;
        $newActivityAction->description = $description;
        $newActivityAction->old_user_id = $user_id;
        $newActivityAction->new_user_id = $user_id;
        $newActivityAction->author_id = Auth::id();
        $newActivityAction->save();

        return $newActivityAction;
    }

    public function accountstatus(Request $req, $cpath, $id) //id == la actividad de la poliza
    {
        DB::beginTransaction();
        try {

            //TODO: Esta acción se ejecuta de manera automática al día siguiente de generar la acción "REPORTAR PLANILLA DEFINITIVA".

            //TODO: Cálculo estado cuenta
            // Obtener las planillas asociadas con la actividad poliza, donde el estado de la
            // planilla debe ser certificado reportado y el services_id Reportar Planilla Tomador

            $templateActivities = Activity::where('parent_id', $id)
                ->where('service_id', Service::SERVICE_REPORT_TAKEN_FORM_MNK)
                ->where('state_id', StatePoliza::CERTIFICADO_REPORTADO_TOMADOR)
                ->get();

            $policySpreadsheets = PolicySpreadsheet::wherein('activity_id', $templateActivities->pluck('id'))
                ->orderBy('created_at', 'asc')
                ->get();

            $totalSum = 0; // Sumatoria de los totales salarios de los perioddos existentes
            $statement = 0; // Cálculo del estado cuenta
            $months = 12; //Total de periodos o planillas reportadas a poliza sort
            $totalItems = count($policySpreadsheets); // Cantidad total de registros

            foreach ($policySpreadsheets as $key => $spreadsheet) {
                // Los meses restantes
                $monthleft = $months - $key;


                //    $higher_value_compare = max($spreadsheet->higher_value_compare, $higher_value_compare);

                // Si es el último registro
                if ($key === $totalItems - 1) {
                    // Realiza la multiplicación solo en el último registro
                    $statement = $spreadsheet->total_salaries * $monthleft;
                    $statement += $totalSum;
                } else {
                    // Sumatoria por registro
                    $totalSum += $spreadsheet->total_salaries;
                }
            }

            //Cálculo de la prima base al estado cuenta
            $TEM = 4.35 / 100; // TEM -> Tasa de Emisión (cambiarla)
            $premium_value_of_statement = $statement * $TEM;

            // Consultar la última planilla reportada para esa póliza
            $policy_spreadsheets_selected = PolicySpreadsheet::whereIn('activity_id', $templateActivities->pluck('id'))
                ->orderBy('created_at', 'desc')
                ->first(); // Obtener solo un registro

            $policy_spreadsheets_selected->statement = $statement;
            //Si es el primer periodo que se está capturando aplicar los siguientes cambios
            if ($totalItems === 1) {

                $policySort = PolicySort::find($id);

                //Espicificar el valor a comprar al inicio del recorrido de los periodos será el valor de la prima del primer periodo
                $policy_spreadsheets_selected->higher_value_compare = $premium_value_of_statement;

                //Definir la fechas próximas de ejecución:
                // FECHA DE EJECUCIÓN CÁLCULO ABONO MENSUAL
                $policySort->monthly_execution_date = $policy_spreadsheets_selected->created_at->addMonth();
                // FECHA DE EJECUCIÓN CÁLCULO ABONO TRIMESTRAL
                $policySort->quarterly_execution_date = $policy_spreadsheets_selected->created_at->addMonths(3);
                // FECHA DE EJECUCIÓN CÁLCULO ABONO SIMESTRAL
                $policySort->biannual_execution_date = $policy_spreadsheets_selected->created_at->addMonths(6);
            }
            $policy_spreadsheets_selected->premium_value_of_statement = $premium_value_of_statement;
            $policy_spreadsheets_selected->save();

            //TODO: Generar documento denominado  Estado de cuenta Poliza SORT
            // Generar el PDF
            $document = 'estado_cuenta';
            $activityAction = Action::REPORTAR_PLANILLA_DEFINITIVA;
            $pdf = PDF::loadView("services.policy_sort.docs.{$document}_pdf", ['value' => $statement]);

            // Guardar el PDF en S3
            Storage::disk('s3')
                ->put("activity_action_document/{$document}_{$activityAction}.pdf", $pdf->output());


            //TODO: Crear un nuevo registro en ActivityAction
            $description = "REPORTAR ESTADO DE CUENTA";
            $activityActions = new ActivityAction;
            $activityActions->activity_id = $id; // Usar el ID de la actividad
            $activityActions->action_id = Action::REPORTAR_ESTADO_DE_CUENTA;
            $activityActions->old_state_id = StatePoliza::POLIZA_EMITIDA_ACTIVA;
            $activityActions->description = $description;
            $activityActions->new_state_id = StatePoliza::POLIZA_EMITIDA_ACTIVA; // O el nuevo estado correspondiente
            $activityActions->author_id = Auth::id();
            $activityActions->save();


            $activityActionDocument = new ActivityActionDocument();
            $activityActionDocument->activity_action_id = $activityActions->id;
            $activityActionDocument->name = $document;
            $activityActionDocument->path = "activity_action_document/{$document}_{$activityAction}.pdf";
            $activityActionDocument->save();

            //Aquí se hacre el reporte de aumento seguro si aplica las condiciones
            $this->reportInsuranceIncrease($id);

            //Al guardar el estado cuenta y el valor prima procedemos a guardar la diferencia y valor mayor.
            $this->calculateHigherValueCompare($id);

            DB::commit();

            return response()->json([
                'status' => 'success',
                'message' => 'Campos han sido actualizados correctamente',
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
            ]);
        }
    }

    public function reportInsuranceIncrease($id)
    {

        DB::beginTransaction();
        try {

            $templateActivities = Activity::where('parent_id', $id)
                ->where('service_id', Service::SERVICE_REPORT_TAKEN_FORM_MNK)
                ->where('state_id', StatePoliza::CERTIFICADO_REPORTADO_TOMADOR)
                ->get();

            // Consultar la última planilla reportada para esa póliza
            $policy_spreadsheets_selected = PolicySpreadsheet::whereIn('activity_id', $templateActivities->pluck('id'))
                ->orderBy('created_at', 'desc')
                ->first(); // Obtener solo un registro

            $currentPeriod = $policy_spreadsheets_selected->created_at; // Asumiendo que 'periodo' es el campo que representa el periodo

            // Obtener el registro del periodo anterior
            $previousPolicySpreadsheet = PolicySpreadsheet::whereIn('activity_id', $templateActivities->pluck('id'))
                ->where('created_at', '<', $currentPeriod) // Filtra por periodo anterior
                ->orderBy('created_at', 'desc') // Ordena por fecha de creación, si hay varios registros con el mismo periodo
                ->first(); // Obtiene solo el registro más reciente del periodo anterior

            // esta es la cantidad O condición dLe valor prima (como un máximo excendente)
            $excessPremiumValue = 0.10 * $previousPolicySpreadsheet->premium_value_of_statement;
            $excessPremiumValue += $previousPolicySpreadsheet->premium_value_of_statement;


            //Capuramos los datos del policy sort (para la periodicidad, temporalidad, modalidad de trabajo)
            $policySort = PolicySort::where('activity_id', $id)->first(); // Obtener el objeto PolicySort

            $RIESGO_DE_TRABAJO_GENERAL = 1; //Modalidad de trabajo
            $periodicityAnual = 1; // Periocidad anual

            /*
             *
             *Esta condición se cumple cuando el total de afiliado es mayor a 1
             * Cuando excede el total salarios de la tarifa inicial
             * si el period es permanente
             * si la periodicitad es anual
             * y si la modalidad de trabajo es riesgo de trabajo general.
             */
            if (
                $policy_spreadsheets_selected->premium_value_of_statement >= $excessPremiumValue
                && $policy_spreadsheets_selected->total_affiliates > 1
                && $policySort->temporality === 'permanent'
                && $policySort->periodicity === $periodicityAnual
                && $policySort->work_modality_id === $RIESGO_DE_TRABAJO_GENERAL
            ) {


                //TODO: Crear un nuevo registro en ActivityAction
                $description = 'REPORTAR AUMENTO DE SEGURO';
                $activityActions = new ActivityAction;
                $activityActions->activity_id = $id; // Usar el ID de la actividad
                $activityActions->action_id = Action::REPORTAR_AUMENTO_DE_SEGURO;
                $activityActions->old_state_id = StatePoliza::POLIZA_EMITIDA_ACTIVA;
                $activityActions->description = $description;
                $activityActions->new_state_id = StatePoliza::POLIZA_EMITIDA_ACTIVA; // O el nuevo estado correspondiente
                $activityActions->author_id = Auth::id();
                $activityActions->save();

                //TODO: Crear de manera automática el servicio denominado
                // "COBROS- POLIZA SORT"
                // aqui se ejecuta la acción de santiago

                $activityPolicy = Activity::where('id', $id)->first();
                $policySortCollection = new Activity();
                $policySortCollection->parent_id = $activityPolicy->id;
                $policySortCollection->client_id = $activityPolicy->client_id;
                $policySortCollection->affiliate_id = $activityPolicy->affiliate_id;
                $policySortCollection->service_id = Service::SERVICE_POLICY_SORT_COLLECTION_MNK;
                $policySortCollection->state_id = State::REGISTRADO;
                $policySortCollection->user_id = Auth::id();
                $policySortCollection->save();

                $data_request = [
                    'total_amount' => ""
                ];

                //Aquí le paso el id de actividad de cobros
                //$policyCollectionController = new PolicySortCollectionController();
                //$policyCollectionController->reportInsuranceIncreasePeriod( new Request($data_request), $cpath, $policySortCollection->id);
            }

            DB::commit();

            return response()->json([
                'status' => 'success',
                'message' => 'Campos han sido actualizados correctamente',
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
            ]);
        }
    }

    public function calculateHigherValueCompare($id)
    {

        DB::beginTransaction();
        try {

            $templateActivities = Activity::where('parent_id', $id)
                ->where('service_id', Service::SERVICE_REPORT_TAKEN_FORM_MNK)
                ->where('state_id', StatePoliza::CERTIFICADO_REPORTADO_TOMADOR)
                ->get();

            $policySpreadsheets = PolicySpreadsheet::wherein('activity_id', $templateActivities->pluck('id'))
                ->orderBy('created_at', 'asc')
                ->get();

            $higher_value_compare = 0; // Guardará el campo mayor a comparar
            $totalItems = count($policySpreadsheets); // Cantidad total de registros

            foreach ($policySpreadsheets as $key => $spreadsheet) {
                if ($totalItems > 1) {
                    // Aplica solo al primer ítem
                    if ($key === 0) {
                        $higher_value_compare = $spreadsheet->higher_value_compare;
                    }
                    // Calcula la diferencia y encuentra el valor más alto para comparar
                    $value_difference = $higher_value_compare - $spreadsheet->premium_value_of_statement;
                    $higher_value_compare = max($higher_value_compare, $spreadsheet->premium_value_of_statement);
                    $spreadsheet->higher_value_compare = $higher_value_compare;
                    $spreadsheet->value_difference = $value_difference;
                    $spreadsheet->save();
                } else {
                    $higher_value_compare = $spreadsheet->higher_value_compare;
                    $value_difference = $higher_value_compare - $spreadsheet->premium_value_of_statement;
                    $spreadsheet->value_difference = $value_difference;
                    $spreadsheet->save();
                }
            }

            DB::commit();

            return response()->json([
                'status' => 'success',
                'message' => 'Campos han sido actualizados correctamente',
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
            ]);
        }
    }


    public function biannualpaymentcalculation(Request $req, $cpath)
    {

        // Parámetros necesarios
        $activityId = $req->input('activity_id');
        $client = Client::where('path', $cpath)->firstOrFail();
        // Obtener la fecha actual
        $today = now()->startOfDay();

        // Buscar las pólizas con la forma de pago "mensual"
        $policies = Policy::where('periodicity', 'semestral')
            ->where('status', StatePoliza::POLIZA_EMITIDA_ACTIVA)
            ->get();

        foreach ($policies as $policy) {
            // Calcular la fecha de la última acción GENERAR CALCULO ABONO SEMESTRAL para esta póliza
            $lastActionDate = ActivityAction::where('action_id', Action::GENERAR_CALCULO_ABONO_SEMESTRAL)
                ->where('activity_id', $policy->id)
                ->orderBy('created_at', 'desc')
                ->value('created_at');

            // Calcular la próxima fecha de vencimiento
            $nextDueDate = $policy->period_start_date->copy()->addDays(180);

            // Verificar si es necesario generar una nueva acción
            if (!$lastActionDate || $today->greaterThanOrEqualTo($nextDueDate)) {
                // Iniciar una transacción
                DB::transaction(function () use ($activityId, $client, $policy) {
                    // Crear una nueva actividad
                    $newActivity = Activity::create([
                        'parent_id' => $activityId,
                        'client_id' => $client->id,
                        'affiliate_id' => $policy->affiliate_id,
                        'service_id' => Service::SERVICE_POLICY_SORT_COLLECTION_MNK,
                        'state_id' => StatePoliza::POLIZA_EMITIDA_ACTIVA,
                        'user_id' => Auth::id(),
                    ]);

                    // Crear y registrar acciones
                    $description = "Acción generada CALCULO ABONO SEMESTRAL";
                    ActionController::create(
                        $newActivity->id,
                        Action::GENERAR_CALCULO_ABONO_SEMESTRAL,
                        $description
                    );
                    $description = "Acción generada REPORTAR ABONO SEMESTRAL";
                    ActionController::create(
                        $newActivity->id,
                        Action::REPORTAR_RECIBO_ABONO_SEMESTRAL,
                        $description
                    );
                });
                // Actualizar la fecha de inicio de la póliza
                $policy->period_start_date = $nextDueDate;
                $policy->save();
            }
        }
    }

    public function dataPreview(Request $req, $cpath, $id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();
        $calendarPeriods = AppServiceProvider::$CALENDAR_PERIOD;
        $typeincomes = AppServiceProvider::$TYPE_INCOME;

        $activityDocument = ActivityDocument::where('activity_id', $activity->id)->get();

        // capturar datos a enviar a formulario
        $results = DB::table('activities as ac_parent')
            ->join('activities as ac_son', 'ac_son.parent_id', '=', 'ac_parent.id')
            ->join('policy_sorts as ps_son', 'ps_son.activity_id', '=', 'ac_son.id')
            ->join('quotations', 'quotations.activity_id', '=', 'ac_parent.id')
            ->join('affiliates', 'affiliates.id', '=', 'ac_parent.affiliate_id')
            ->where('ac_parent.id', $id)
            ->select(
                'ac_parent.id as id_act_quot',
                'ac_parent.service_id as service_id_quot',
                'ac_son.id as act_id_policy',
                'ac_son.service_id as service_id_policy',
                'ps_son.id as policy_sorts_id',
                'ps_son.activity_id as activity_id_policy',
                'ac_parent.affiliate_id as activity_id_quot',

                'affiliates.id as id_affiliate',
                'affiliates.doc_type',
                'affiliates.doc_number',
                'affiliates.first_name',
                'affiliates.phone',

                'affiliates.email as affiliate_email',
                'affiliates.electronic_billing_email',

                'affiliates.iban_account',
                'affiliates.employer_address',
                'affiliates.province',
                'affiliates.canton',
                'affiliates.district',

                'ps_son.id as policy_sorts_id',
                'ps_son.brokerage_name',
                'ps_son.advisor_name',
                'ps_son.code',
                'ps_son.notification_email',
                'ps_son.validity_from',
                'ps_son.validity_to',
                'ps_son.payroll_status',
                'ps_son.temporality',
                'ps_son.salary_projection',
                'ps_son.periodicity',
                'ps_son.accumulated_premium',
                'ps_son.economic_activity',
                'ps_son.activity_economic_id',

                'ps_son.legal_representative_name',
                'ps_son.legal_representative_id',
                'ps_son.legal_representative_profession',
                'ps_son.typeSignature',
                'ps_son.type_currency',

                'quotations.amount_policy'
            )
            ->first();

        // Validacion si no existen datos
        if (empty($results)) {
            return redirect('/intermediario/polizas')->with('message', 'No se encontró la Póliza..');
        }

        $activityDocument = ActivityDocument::where('activity_id', $activity->id)->get();

        return view('services.policy_sort.intermediary_policy.menu.intermediary_data', [
            'activity' => $activity,
            'document1' => $activityDocument->where('document_id', 1)->first(),
            'document2' => $activityDocument->where('document_id', 2)->first(),
            'document3' => $activityDocument->where('document_id', 3)->first(),
            'document4' => $activityDocument->where('document_id', 4)->first(),
            'document5' => $activityDocument->where('document_id', 9)->first(),
            'data' => $results,
            'calendarPeriods' => $calendarPeriods,
            'typeincomes' => $typeincomes,
            'id' => $id,
        ]);
    }

    /*
     * >>Fin de bloque de vistas de intermediario<<
     * ***/


    public function paymentPolicyView(Request $req, $cpath, $id)
    {
        Client::query()->where('path', $cpath)->firstOrFail();

        $client = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();
        $policy = PolicySort::where('Activity_id', $activity->id)->firstOrFail();
        $amounts = [
            '1' => $policy->annual_calculation_amount,
            '2' => $policy->semiannual_calculation_amount,
            '3' => $policy->quarterly_calculation_amount,
            '4' => $policy->monthly_calculation_amount,
        ];

        if ($policy->temporality == 'permanent') {
            $valorPagar = $amounts[$policy->periodicity] ?? 0;
        } else {
            $valorPagar = $policy->single_payment_value;
        }

        return view('services.policy_sort.intermediary_policy.otherTaps.paymentPolicy', [
            'id' => $id,
            'policy' => $policy,
            'valorPagar' => $valorPagar,
            'callback' => false
        ]);
    }

    public function paymenQa(Request $req, $cpath)
    {

        $data = $req->only(['monto', 'indredirect', 'redirect', 'poliza', 'moneda', 'order']);

        return view('services.payment_qa.formPaymentQa', array_merge($data, [
            'id' => '',
        ]));
    }


    public function policySummaryView(Request $req, $cpath, $id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();
        $endDate = null;
        if ($activity->policy_sort && $activity->policy_sort->periodicity != 'permanent') {
            $endDate = Carbon::now()->addYears(1);
        }

        return view('services.policy_sort.intermediary_policy.otherTaps.policySummary', [
            'id' => $id,
            'policy_sort' => $activity->policy_sort,
            'startDate' => Carbon::now(),
            'endDate' => $endDate
        ]);
    }

    public function downloadSignature(Request $req, $cpath, $id)
    {

        $client = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();
        $affiliate = Affiliate::where('id', $activity->affiliate_id)->first();
        $policySort = PolicySort::where('activity_id', $activity->id)->first();

        $ubicacion = $this->getLocationNamesFromJson($affiliate->province, $affiliate->canton, $affiliate->district);

        //obtenemos la actividad economica
        $jsonSource = ($activity->policy_sort->economic_activity == 'public') ? '/js/economic_activity/public.json' : '/js/economic_activity/private.json';
        $json = file_get_contents(public_path($jsonSource));
        $economicActivities = json_decode($json, true);
        $activity_economic_name = collect($economicActivities)->firstWhere('CODE', $activity->policy_sort->activity_economic_id)['ACTIVITY_NAME'];
        $activity->policy_sort->economic_activity_name = $activity_economic_name;

        $isplanilla = $activity->activity_actions->where('action_id', ActionPolizaSort::REPORTAR_DOCUMENTOS_EXTERNOS_SIN_PLANILLAS)
            ->first();

        $totalSalarie = $this->totalSalariesPlanilla($policySort);

        $data = [
            'name' => $affiliate->first_name,
            'idNumber' => $affiliate->doc_number,
            'email' => $affiliate->email,
            'ocupacion' => $affiliate->occupation,
            'invoiceEmail' => $affiliate->electronic_billing_email,
            'province' => $ubicacion['province'],
            'canton' => $ubicacion['canton'],
            'district' => $ubicacion['district'],
            'economicActivity' => $policySort->activity_economic_id,
            'legalRepresentativeName' => $policySort->legal_representative_name,
            'legalRepresentativeId' => $policySort->legal_representative_id,
            'puesto_tomador' => $affiliate->occupation_responsible,
            'name_tomador' => $affiliate->first_name,
            'employer_address' => $affiliate->employer_address,
            'phone' => $affiliate->phone,
            'cellphone' => $affiliate->cellphone,
            'sign1_url' => '',
            'sign2_url' => '',
            'economic_activity_name' => $activity->policy_sort->economic_activity_name,
            'validity_from' => date('d/m/y', strtotime($policySort->validity_from)),
            'validity_to' => date('d/m/y', strtotime($policySort->validity_to)),
            'isplanilla' => $isplanilla ? true : null,
            'calendar' => $activity->policy_sort->calendar_period,
            'anual' => $policySort->annual_calculation_amount,
            'type_currency' => $policySort->type_currency,
            'salary_projection' => $policySort->salary_projection,
            'total_salaries' => $totalSalarie,
            'temporality' => $policySort->temporality,
            'single_payment_value' => $policySort->single_payment_value,
        ];


        $document = 'download_signature_mnk';
        // Cargar la vista del PDF y pasarle los datos
        $pdf = PDF::loadView("services.policy_sort.docs.{$document}", [
            'data' => $data,
            'watermark' => false,
        ]);

        // Previsualizar el PDF en el navegador
        return $pdf->stream('Solicitud_emision_sort.pdf');
    }


    public function totalSalariesPlanilla($policySort)
    {

        $activity_spread = Activity::with(['policy_spreadsheets'])
            ->where('parent_id', $policySort->activity_id)
            ->where('service_id', Service::SERVICE_REPORT_TAKEN_FORM_MNK)
            ->latest()
            ->first();


        return $activity_spread ? $activity_spread->policy_spreadsheets->total_salaries : 0;
    }

    public function uploadPhysicalSignatures(Request $request, $cpath)
    {
        // Validar que el archivo sea un PDF
        $request->validate([
            'pdfSignature' => 'required|mimes:pdf|max:2048', // Máximo de 2MB, ajusta según tus necesidades
        ]);

        // Subir el archivo al almacenamiento local y luego a S3
        if ($request->file('pdfSignature')) {

            // Archivo cargado
            $file = $request->file('pdfSignature');
            $nowTime = now();
            $date_now = $nowTime->format('Y/m/d');
            $filename = $date_now . '_' . time() . '.' . $file->getClientOriginalExtension();

            // Guardar en la tabla policy_spreadsheets, campo file
            /*$policySpreadsheet = PolicySpreadsheet::create([
                'policy_sort_id' => $id,
                'file' => $fileUrl,
            ]);
            */

            // Obtener el ID generado de PolicySpreadsheet
            /*
             * $policySpreadsheetId = $policySpreadsheet->id;
             */
            // Guardar el archivo localmente para procesamiento temporal
            $localPath = $file->store('uploads/temp', 'local');

            // Guarda el archivo en AWS S3
            $path = Storage::disk('s3')->putFileAs("policy_sort", $file, $filename);


            $activityDocument = ActivityDocument::create([
                'activity_id' => $request->activity_id,
                'document_id' => 1,
                'file' => $path,
                'uploaded_at' => new DateTime()
            ]);

            // Elimino el archivo local después de procesarlo
            Storage::disk('local')->delete($localPath);

            // Respuesta en JSON
            return response()->json([
                'status' => 'success',
                'message' => 'PDF subido exitosamente y guardado en S3.'
            ]);
        }
        return response()->json([
            'status' => 'error',
            'message' => 'Hubo un problema al subir el archivo.',
        ], 400);
    }

    public function downloadPolicySort(Request $req, $cpath, $id)
    {

        $client = Client::where('path', $cpath)->firstOrFail();
        try {

            // 1. Guardo los datos en la tabla policy_sorts
            $results = DB::table('quotations')
                ->join('activities', 'activities.id', '=', 'quotations.activity_id')
                ->join('policy_sorts', 'activities.id', '=', 'policy_sorts.activity_id')
                ->join('affiliates', 'affiliates.id', '=', 'activities.affiliate_id')
                ->where('activities.id', 1) //cambiar esto por favor
                ->where('activities.client_id', $client->id)
                ->where('affiliates.client_id', $client->id)
                ->select(
                    'quotations.*',
                    'activities.*',
                    'policy_sorts.*',
                    'affiliates.*',
                    'policy_sorts.id as policy_sorts_id',
                    'quotations.email as quotation_email',  // Alias para el email de quotations
                    'affiliates.email as affiliate_email'   // Alias para el email de affiliates
                )
                ->first();

            $document = 'policy_sort_pdf';
            // Cargar la vista del PDF y pasarle los datos
            $pdf = PDF::loadView("services.policy_sort.docs.{$document}", [
                'data' => $results,
                'watermark' => false
            ]);

            // Previsualizar el PDF en el navegador
            return $pdf->stream('documento.pdf');
        } catch (\Exception $e) {
            return 'ERROR: ' . $e->getMessage();
        }
    }


    public function uploadDigitalSignatures(Request $req, $cpath)
    {
        // SIGNS SAVE
        if ($req->input('sign')) {
            $image_request = $req->input('sign'); //base64 string
            $image_decode = base64_decode(preg_replace('#^data:image/\w+;base64,#i', '', $image_request));
            Storage::disk('s3')->put("policy_sort", $image_decode, 'public');
            ///$radicationInsuranceApp->nineth_section_sign_titular = $file_path; /// hay que guardar en la vase de datos
        }
        // END: SIGNS SAVE

        // SIGNS SAVE
        if ($req->input('sign_2')) {
            $image_request_2 = $req->input('sign_2'); //base64 string
            $image_decode_2 = base64_decode(preg_replace('#^data:image/\w+;base64,#i', '', $image_request_2));
            Storage::disk('s3')->put("policy_sort", $image_decode_2, 'public');
            ///$radicationInsuranceApp->nineth_section_sign_titular = $file_path; /// hay que guardar en la vase de datos
        }
    }

    /**
     * @param $id de la actividad de la cotizacion (Padre)
     * @return array
     */
    public function creditPaymentReport($id)
    {
        $results = DB::table('policy_sort_collections')
            ->join('activities', 'policy_sort_collections.activity_id', '=', 'activities.id')
            ->join('activity_actions', 'activity_actions.activity_id', '=', 'activities.id')
            ->where('activities.id', $id)
            ->whereIn('activity_actions.action_id', [68, 73, 75])
            ->orderBy('policy_sort_collections.created_at', 'desc')
            ->get();

        $count = $results->count(); // Contar el número de registros

        return [
            'results' => $results,
            'count' => $count,
        ];
    }

    /**
     * EMITIR FACTURACIÓN ELETRÓNICA
     *
     * @param Request $req
     * @param int $id de la actividad de la cotizacion poliza sort
     * @return JsonResponse
     */
    public function issueElectronicBilling(Request $req, $cpath, $id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();
        DB::beginTransaction();
        try {
            $activityCollection = new Activity();
            $activityCollection->parent_id = $activity->parent_id;
            $activityCollection->client_id = $activity->client_id;
            $activityCollection->affiliate_id = $activity->affiliate_id;
            $activityCollection->service_id = Service::SERVICE_POLICY_SORT_COLLECTION_MNK;
            $activityCollection->state_id = State::REGISTRADO;
            $activityCollection->user_id = $activity->user_id;
            $activityCollection->save();

            //cambiar estado a la poliza
            $activityPolicy = Activity::where('client_id', $client->id)->where('id', $activity->parent_id)->firstOrFail();
            $activityPolicy->state_id = 18;
            $activityPolicy->save();

            //registrar actividad
            $this->registerActivityAction(
                $activityPolicy->id,
                Action::EMITIR_FACTURACION_ELECTRONICA,
                17,
                18,
                'Emisión de facturación electrónica',
                $activityCollection->user_id
            );

            //llamar accion REPORTAR FACTURA ELECTRÓNICA EMISIÓN POLIZA
            $policySortController = new PolicySortCollectionController();
            $policySortController->reportElectronicInvoice($req, $cpath, $activityCollection->id);

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
            ]);
        }
        return response()->json([
            'status' => 'success',
            'message' => 'Facturación electrónica emitida correctamente',
        ]);
    }

    /**
     * ACCIIÖN REPORTAR PAGO POLIZA SORT
     *
     * @param Request $req
     * @param int $id de la actividad de POLIZA SORT
     * @return JsonResponse
     * @throws \Exception Si ocurre un error al actualizar el pago.
     */
    public function reportPaymentMade(Request $req, $cpath, $id)
    {
        //Iniciamos la transacción
        DB::beginTransaction();

        try {
            $client = Client::where('path', $cpath)->firstOrFail();
            $activity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();

            //Verificamos el estado de la póliza para ejecutar cobro
            if ($activity->state_id == StatePoliza::PENDIENTE_DE_COBRO_SIN_PLANILLA) {

                //Ejecutamos la acción GENERAR COBRO sin reportar planilla
                $descripcion = 'Se generó la acción generar cobro de póliza sort sin reportar planilla';
                //Ejecutamos la accion de generar cobro sin reportar planilla
                ActionController::create($activity->id, ActionPolizaSort::GENERAR_COBRO_SIN_PLANILLA, $descripcion);
            } else {

                //Ejecutamos la acción GENERAR COBRO
                $descripcion = 'Se generó la acción generar cobro de póliza sort';
                ActionController::create($activity->id, ActionPolizaSort::GENERAR_COBRO, $descripcion);
            }

            //Creamos la actividades el servico de COBROS SORT asociado a la poliza
            try {
                $activityPayment = new Activity();
                $activityPayment->parent_id = $activity->id;
                $activityPayment->client_id = $activity->client_id;
                $activityPayment->affiliate_id = $activity->affiliate_id;
                $activityPayment->user_id = $activity->user_id;
                $activityPayment->service_id = SERVICE::SERVICE_POLICY_SORT_COLLECTION_MNK;
                $activityPayment->state_id = State::REGISTRADO;
                $activityPayment->save();

                //Creamos el servico de COBROS SORT asociado a la poliza
                $policyPayment = new PolicySortCollection();
                $policyPayment->activity_id = $activityPayment->id;
                $policyPayment->invoice_number = 'Emisión-' . uniqid();
                $policyPayment->total_amount = $activity->policy_sort->amount_policy;
                $policyPayment->payment_status = 'pending';
                $policyPayment->type_receipt = 'emission';
                $policyPayment->payment_method = $req->method_payment ?? '';
                $policyPayment->save();

                //EJECUTAMOS LA ACCION DEL COBRO REPORTAR RECIBO EMISIÓN PÓLIZA
                $descripcion = 'Se generó la acción reportar recibo emisión póliza';
                $action_id = ActionPolicySortCollection::REPORTAR_RECIBO_EMISION_POLIZA;
                $activityActionsCreated = ActionController::create($activityPayment->id, $action_id, $descripcion);
                //Guardamos los valores en la base de datos

                $this->sendPaymentReceipt($client, $activityPayment, $activityActionsCreated);

                DB::commit();

                return $activityPayment->id;
            } catch (\Exception $e) {

                DB::rollBack();
                return response()->json([
                    'message' => 'Errores durante el procesamiento del cargue manual' . $e->getMessage(),
                    'e' => $e->getMessage(),
                ], 500);
            }
        } catch (\Exception $e) {
            DB::rollback();

            return response()->json([
                'success' => false,
                'message' => 'Error al actualizar el registro: ' . $e->getMessage(),
            ], 500);
        }
    }

    public function pagofacturaprueba($id)
    {

        $horaActual = new DateTime();
        $horaCostarrica = $horaActual->format('h:i:s A');
        $fechaCostarrica = $horaActual->format('d/m/Y');

        $activity_policy_collection = Activity::where('id', $id)->firstOrFail(); // ACTIVIDAD DE COBROS
        //$activity_policy = Activity::where('id', $parent_id)->firstOrFail();
        $activity_policy = $activity_policy_collection->parent;

        $totalWords = NumberToWords::convertToWords($activity_policy_collection->policy_sort_collection->total_amount ?? 0);

        $date1 = \Illuminate\Support\Carbon::createFromFormat('Y-m-d', $activity_policy->policy_sort->validity_from);
        $date_to = \Illuminate\Support\Carbon::createFromFormat('Y-m-d', $activity_policy->policy_sort->validity_to);
        $work_modality_id = $activity_policy->policy_sort->work_modality_id;
        $type_receipt = $activity_policy_collection->policy_sort_collection->type_receipt;

        $quotation = $activity_policy->parent;
        $condiciones = ActivityAction::where('action_id', '=', ActionCotizacionSort::REPORTAR_CONDICIONES_ESPECIALES)->where('activity_id', '=', $quotation->id)->first();

        if ($condiciones) {
            $quotationCondition = QuotationConditionSpecial::where('activity_id', '=', $quotation->id)->first();
            [$descuentoCml, $descuentoClap, $base, $final, $descuentos_total, $sumaDescuentos] = array_values(
                $this->calcularDescuentos(
                    $activity_policy->policy_sort,
                    $quotationCondition
                )
            );
        } else {
            $total = $activity_policy_collection->policy_sort_collection->total_amount;

        }

        $datos = [
            'activity_policy' => $activity_policy,
            'activity_policy_collection' => $activity_policy_collection,
            'totalWords' => $totalWords,
            'date_from' => $date1->format('d/m/Y'), //$date2->format('Y/m/d')
            'date_to' => $date_to->format('d/m/Y'), //$date3->format('Y/m/d')
            'watermark' => false,
            'horaCostarrica' => $horaCostarrica,
            'fechaCostarrica' => $fechaCostarrica,
            'descuentoCml' => $condiciones ? $descuentoCml : null,
            'descuentoClap' => $condiciones ? $descuentoClap : null,
            'base' => $condiciones ? $base : $total,
            'final' => $condiciones ? $final : $total,
            'descuentos_total' => $condiciones ? $descuentos_total : 0,
            'sumaDescuentos' => $condiciones ? $sumaDescuentos : 0,
            'quotationCondition' => $condiciones ? $quotationCondition : null,
            'work_modality_id' => $work_modality_id,
            'type_receipt'=> $type_receipt ?? ''
        ];

        $pdf = PDF::loadView("services.policy_sort_collection.docs.monthly_payment_receipt", $datos);

        return $pdf->stream('pago.pdf');
    }

    function calcularDescuentos($policySort, $quotationCondition)
    {
        // Caso 'short'
        if ($policySort->temporality === 'short') {
            return [
                'descuento' => 0,
                'descuentoClap' => 0,
                'base' => 0,
                'final' => 0,
                'descuentos_total' => 0,
                'sumaDescuentos' => 0
            ];
        }

        $key = $policySort->periodicity;
        $prefix = AppServiceProvider::$PERIODICITY_EXP[$key] ?? null;

        if (!$prefix) {
            // Periodicidad desconocida: devolvemos ceros
            return ['descuento' => 0, 'descuentoClap' => 0];
        }

        // Construimos dinámicamente los nombres de campo
        $fieldCmc = "{$prefix}_cmc";
        $fieldClap = "{$prefix}_clap";
        $fieldBase = "{$prefix}_base";
        $fieldFinal = "{$prefix}_fraccionada";
        $fielddDscuento = "{$prefix}_descuento";

        $base = $quotationCondition->{$fieldBase};
        $descuento = $quotationCondition->{$fielddDscuento};

        $divisor = AppServiceProvider::$PERIODICITY_DIVISOR[$prefix] ?? 1;


        $base /= $base ? $divisor : 1;

        $descuento /= $descuento ? $divisor : 1;


        return [
            'descuento' => $quotationCondition->{$fieldCmc} ?? 0,
            'descuentoClap' => $quotationCondition->{$fieldClap} ?? 0,
            'base' => $base ?? 0,
            'final' => $quotationCondition->{$fieldFinal} ?? 0,
            'descuentos_total' => $descuento ?? 0,
            'sumaDescuentos' => $quotationCondition->cmc + $quotationCondition->clap
        ];
    }

    public function sendPaymentReceipt($client, $activityPayment, $activityActionsCreated)
    {
        try {

            date_default_timezone_set('America/Costa_Rica');
            $horaActual = new DateTime();
            $horaCostarrica = $horaActual->format('h:i:s A');
            $fechaCostarrica = $horaActual->format('d/m/Y');

            $activity_policy_collection = Activity::where('client_id', $client->id)->where('id', $activityPayment->id)->firstOrFail(); // ACTIVIDAD DE COBROS
            //$activity_policy = Activity::where('client_id', $client->id)->where('id', $activityPayment->parent->id)->firstOrFail();
            $activity_policy = $activity_policy_collection->parent;

            if (!$activity_policy_collection->policy_sort_collection) {
                throw new \Exception('Error no hay registro de cobro en policy_sort_collection');
            }

            $totalWords = NumberToWords::convertToWords($activity_policy_collection->policy_sort_collection->total_amount ?? 0, $activity_policy->policy_sort->type_currency);
            $work_modality_id = $activity_policy->policy_sort->work_modality_id;
            $type_receipt = $activity_policy_collection->policy_sort_collection->type_receipt;

            //fecha del recibo
            $validityFrom = Carbon::parse($activity_policy->policy_sort->validity_from);
            $validityTo = Carbon::parse($activity_policy->policy_sort->validity_to);
            $intervals = [
                0 => $validityTo,
                1 => $validityTo,
                2 => $validityFrom->copy()->addMonths(6)->subDay(),
                3 => $validityFrom->copy()->addMonths(3)->subDay(),
                4 => $validityFrom->copy()->addMonth()->subDay(),
            ];
            $validityFrom = $validityFrom->format('d/m/Y');
            $validityTo = isset($intervals[$activity_policy->policy_sort->periodicity]) ? $intervals[$activity_policy->policy_sort->periodicity]->format('d/m/Y') : $validityTo->format('d/m/Y');

            $quotation = $activity_policy->parent;
            $condiciones = ActivityAction::where('action_id', '=', ActionCotizacionSort::REPORTAR_CONDICIONES_ESPECIALES)->where('activity_id', '=', $quotation->id)->first();

            if ($condiciones) {
                $quotationCondition = QuotationConditionSpecial::where('activity_id', '=', $quotation->id)->first();
                [$descuentoCml, $descuentoClap, $base, $final, $descuentos_total, $sumaDescuentos] = array_values(
                    $this->calcularDescuentos(
                        $activity_policy->policy_sort,
                        $quotationCondition
                    )
                );
            } else {
                $total = $activity_policy_collection->policy_sort_collection->total_amount;
            }

            $datos = [
                'activity_policy' => $activity_policy,
                'activity_policy_collection' => $activity_policy_collection,
                'totalWords' => $totalWords,
                'date_from' => $validityFrom,
                'date_to' => $validityTo,
                'watermark' => false,
                'horaCostarrica' => $horaCostarrica,
                'fechaCostarrica' => $fechaCostarrica,
                'pagado' => false,
                'descuentoCml' => $condiciones ? $descuentoCml : null,
                'descuentoClap' => $condiciones ? $descuentoClap : null,
                'base' => $condiciones ? $base : $total,
                'final' => $condiciones ? $final : $total,
                'descuentos_total' => $condiciones ? $descuentos_total : 0,
                'sumaDescuentos' => $condiciones ? $sumaDescuentos : 0,
                'quotationCondition' => $condiciones ? $quotationCondition : null,
                'work_modality_id' => $work_modality_id,
                'type_receipt'=> $type_receipt ?? ''
            ];

            $indexDoc = $activity_policy_collection->policy_sort_collection->type_receipt == 'emission'
                ? $activity_policy->policy_sort->periodicity
                : $activity_policy_collection->policy_sort_collection->type_receipt;

            $document = AppServiceProvider::$PERIODICITY_RECEIPTS[$indexDoc] ?? 'unknown_receipt_type';

            $pdf = PDF::loadView("services.policy_sort_collection.docs.monthly_payment_receipt", $datos);

            $filePath = "policyCollectionSort/{$document}_{$activityPayment->id}_emision.pdf";
            Storage::disk('s3')
                ->put("policyCollectionSort/{$document}_{$activityPayment->id}_emision.pdf", $pdf->output());

            $activityActionDocument = new ActivityActionDocument();
            $activityActionDocument->activity_action_id = $activityActionsCreated->id;
            $activityActionDocument->name = $document;
            $activityActionDocument->path = "policyCollectionSort/{$document}_{$activityPayment->id}_emision.pdf";
            $activityActionDocument->save();

            if (empty($activity_policy_collection->affiliate->email)) {
                throw new \Exception('El afiliado no tiene un correo definido.');
            }

            $emailAffiliate = $activity_policy_collection->affiliate->email ?? '';
            $username = preg_replace('/^CO-/', '', $activity_policy->policy_sort->code); // Remover el prefijo "CO-"
            $emailPolicy = $activity_policy->policy_sort->email ?? '';

            $user = User::where(function ($query) use ($username) {
                $query->where('username', $username) // Buscar directamente sin "CO-"
                    ->orWhere('username', 'CO-' . $username) // Buscar con "CO-"
                    ->orWhere('code_mnk', $username)
                    ->orWhere('code_mnk', 'CO-' . $username);
            })->first();

            $userEmailCorredor = isset($user) ? explode(' ', trim($user->new_business_email ?? ''))[0] : '';

            $emails = array_filter([$emailAffiliate, $userEmailCorredor, $emailPolicy], function ($email) {
                return !empty($email);
            });

            $text = [
                "text" => "Buen día, 
                           Estimado cliente, adjunto encontrará el recibo emisión póliza.",
                "sender" => 'MNK Seguros'
            ];
            $attachments = [
                [
                    'path' => $filePath,
                    'name' => 'abonoPoliza.pdf',
                    'type' => 'PDF'
                ]
            ];

            $mailSent = new SendDocumentDataBase(
                implode(',', $emails),
                "Recibo emisión póliza ",
                "<EMAIL>",
                "Recibo emisión póliza",
                $text,
                "<EMAIL>",
                $attachments,
                "send_document_db",
                $client,
                request()->getHost(),
                $activity_policy_collection->id,
                $activityActionsCreated->id,
                $activity_policy_collection->service->id
            );

            // Capturar el resultado del envío
            $result = $mailSent->sendMail();

            //Registramos los datos del correo enviado para la trazabilidad
            $mailBoardController = new MailBoardController();
            $mailBoardController->createRegisterMail(
                $activity_policy_collection->id,
                $activity_policy_collection->service->id,
                $activity_policy->policy_sort->consecutive,
                'Tomador',
                mb_convert_case(mb_strtolower($activity_policy->affiliate->full_name ?? ''), MB_CASE_TITLE, "UTF-8"),
                $activity_policy->affiliate->doc_number,
                "Recibo emisión póliza",
                $text,
                $emails,
                $result,
                $attachments
            );
        } catch (Exception $e) {
            DB::rollback();
            throw new Exception('Error en el envio del correo ' . $e->getMessage());
        }
    }

    public function getCorreduria(Request $req, $cpath)
    {
        // Recuperar usuarios agrupados por 'correduria'
        $users = User::select('correduria', DB::raw('COUNT(*) as total'))
            ->whereNotNull('correduria') // Filtra los registros donde 'correduria' no sea null
            ->groupBy('correduria')
            ->get();

        return response()->json([
            'users' => $users
        ]);
    }

    public function getUserCorreduria(Request $req, $cpath)
    {
        // Recuperar usuarios filtrador por corredurias
        $users = User::select('advisor_name', 'code_mnk', 'email')
            ->where('correduria', $req->correduria)
            ->get();

        return response()->json([
            'users' => $users
        ]);
    }

    // Envía el mensaje a la SQS para realizar el cargue asíncronos de los afiliados de la planilla
    public function sendUploadSpreadsheet($policySpreadsheetId)
    {
        $integrationServiceController = new IntegrationServiceController();

        $url = config('app.api_url') . '/planilla_afiliados/cargar';

        $method = 'POST';
        $data = [
            'policy_spreadsheet_id' => $policySpreadsheetId,
        ];

        $result = $integrationServiceController->requestRenAppApi($url, $method, $data);
        $data = json_decode($result->getContent(), true);

        if (isset($data['success']) && $data['success']) {
            return true;
        }
        return false;
    }

    public function sendConstancySpreadsheet($policySpreadsheetId)
    {
        $integrationServiceController = new IntegrationServiceController();

        $url = config('app.api_url') . '/planilla_afiliados/certificados';

        $method = 'POST';
        $data = [
            'policy_spreadsheet_id' => $policySpreadsheetId,
        ];

        $result = $integrationServiceController->requestRenAppApi($url, $method, $data);
        $data = json_decode($result->getContent(), true);

        if (isset($data['success']) && $data['success']) {
            return true;
        }
        return false;
    }

    //Acción REPORTAR_CAMBIO_DE_VIGENCIA en el servicio POlIZA SORT
    public function actionChangeDatePolicity(Request $req, $cpath)
    {

        //Buscamos la activity de la poliza
        $activityPolicy = Activity::where('id', $req->activity_id)
            ->firstOrFail();

        //Capturama el nuevo valor que viene por el req
        $newValue = $req->new_value;

        //Esta acción no cambia los estados de la poliza
        //Solicitar cambio de vigencia de póliza
        $description = "Acción generada REPORTAR CAMBIO DE VIGENCIA";
        $this->registerActivityAction(
            $activityPolicy->id,
            Action::REPORTAR_CAMBIO_DE_VIGENCIA,
            $activityPolicy->state_id,
            $activityPolicy->state_id,
            $description,
            $activityPolicy->user_id
        );

        //retornamos el nuevo valor al formulario.
        return response()->json($newValue);
    }


    /**
     * @param Request $req
     * @param $cpath
     * @throws Exception
     */

    public function actionIssuePolicy($req, $cpath, $id)
    {
        DB::beginTransaction();
        try {

            //Buscamos el cliente
            $client = Client::findOrFail(config('app.client'));

            // 1. Buscamos la activity de la poliza
            $activity = Activity::where('id', $id ?? $req->activity_id)
                ->where('service_id', Service::SERVICE_POLICY_SORT_MNK)->first();

            if (!$activity) {
                throw new Exception('Actividad de póliza no encontrada para el id: ' . $req->activity_id);
            }

            // 2. Buscamos la poliza asociada a la actividad
            $policy = $activity->policy_sort;

            //Generamos un usuario y contraseña en la tabla afiliados para el tomador
            $user = $activity->affiliate->doc_type . $activity->affiliate->doc_number;
            $password = uniqid(); // Contraseña única
            $hashedPassword = Hash::make($password);

            // Crear usuario con los datos basicos y asociarlo al afiliado, definirlo al rol tomador
            $userExist = User::where('username', $activity->affiliate->doc_type . $activity->affiliate->doc_number)
                ->first();
            $emailExist = User::where('email', $activity->affiliate->email)->first();

            if ($userExist) {
                $password = "******(tu contraseña actual)";
            } else {
                $email = $activity->affiliate->email;
                if ($emailExist) {
                    // Generar un correo aleatorio basado en el existente
                    $uniqueSuffix = uniqid(); // Genera un ID único
                    $emailParts = explode('@', $email);
                    $email = $emailParts[0] . '+' . $uniqueSuffix . '@' . $emailParts[1];
                }
                $user_create = User::create([
                    "email" => $email,
                    "password" => $hashedPassword,
                    "affiliate_id" => $activity->affiliate->id,
                    "first_name" => $activity->affiliate->first_name,
                    "last_name" => $activity->affiliate->last_name ?? null,
                    "username" => $user,
                    "active" => 1,
                    "area_id" => 46,
                    "identification_number" => $activity->affiliate->doc_number,
                ]);

                UserClient::create([
                    'user_id' => $user_create->id,
                    'client_id' => $client->id
                ]);
            }

            //$this->generateAccountingEntry($cpath, $policy->id);
            $result = QuotationController::calculatePolicyPrice($activity->policy_sort);
            $tem = $result['percentage'] ?? 0;

            DB::commit();

            //Generamos la poliza con beneficio de colectividad por defecto solicitado por MNK
            //Solo las polizas de Riesgo de Trabajo General llevan colectividad
            if ($policy->work_modality_id == 1) {
                $this->benefitColectiveAutomatic($policy);
            }

            $this->uniqueContactCode($cpath, $activity->id);

            //generar documentos de emisión
            $this->generateEmissionDocuments($activity, $user, $password, $tem, $policy->consecutive);

            return response()->json(['success' => true], 200);
        } catch (Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    public function uniqueContactCode($cpath, $activity_id, $contactNew = false)
    {

        //envio con node se cambio a laravel por que en prod en ocasiones falla
        //            $integrationServiceController = new IntegrationServiceController();
        //
        //            $url = config('app.api_url') . '/codigo_unico_contacto/envio';
        //
        //            $method = 'POST';
        //            $data = [
        //                'activity_id' => intval($activity->id),
        //                'consecutive' => intval($policy->consecutive)
        //            ];
        //
        //            $result = $integrationServiceController->requestRenAppApi($url, $method, $data);
        //            $data = json_decode($result->getContent(), true);

        try {

            $client = Client::where('path', $cpath)->firstOrFail();
            $activity = Activity::where('id', $activity_id)->first();
            $policyContact = PolicyContact::where('policy_sort_id', $activity->policy_sort->id)->get();

            if ($contactNew) {
                $policyContact = PolicyContact::where('policy_sort_id', $activity->policy_sort->id)->whereNull('unique_code')->get();
            }

            foreach ($policyContact as $row) {

                $uniqueCode = $row->unique_code;
                if (empty($row->unique_code)) {
                    $uniqueCode = substr(hash('sha256', $row->number_identify_responsible . microtime(true)), 0, 8);
                    $row->unique_code = $uniqueCode;
                    $row->save();
                }

                $email = $row->email_responsible ?? '';

                $emailBuild = TemplateBuilder::build(
                    Templates::UNIQUE_CODE_WORK_RISK_REPORT,
                    [
                        'policy_sort' => $activity->policy_sort->formatNumberConsecutive(),
                        'name_responsible' => mb_convert_case(mb_strtolower($row->name_responsible ?? ''), MB_CASE_TITLE, "UTF-8"),
                        'representante' => mb_convert_case(mb_strtolower($activity->affiliate->full_name ?? ''), MB_CASE_TITLE, "UTF-8"),
                        'unique_code' => $uniqueCode,
                    ]
                );
                $mailSent = new SendDocumentDataBase(
                    $email,
                    $emailBuild['subject'],
                    "<EMAIL>",
                    $emailBuild['subject'],
                    [
                        "text" => $emailBuild['body'],
                        "sender" => $emailBuild['sender']
                    ],
                    "<EMAIL>",
                    [],
                    "send_document_db",
                    $client,
                    request()->getHost(),
                    $activity->id
                );

                 // Capturar el resultado del envío
                $result = $mailSent->sendMail();

                //Registramos los datos del correo enviado para la trazabilidad
                $mailBoardController = new MailBoardController();
                $mailBoardController->createRegisterMail(
                    $activity->id,
                    $activity->service->id,
                    $activity->policy_sort->consecutive,
                    'Tomador',
                    mb_convert_case(mb_strtolower($activity->affiliate->full_name ?? ''), MB_CASE_TITLE, "UTF-8"),
                    $activity->affiliate->doc_number,
                    $emailBuild['subject'],
                    $emailBuild['body'],
                    $email,
                    $result,
                    null
                );
            }

            return response()->json(['success' => true], 200);
        } catch (Exception $e) {
            throw new \Exception('Error envio codigo unico contatos poliza:: ' . $e);
        }
    }

    public function generateEmissionDocuments($activity, $user, $password, $tem, $consecutive)
    {
        $integrationServiceController = new IntegrationServiceController();

        $url = config('app.api_url') . '/renapp_api/policy/generateEmissionDocuments';

        $method = 'POST';
        $data = [
            'activity_id' => intval($activity->id),
            'user_id' => $user,
            'password' => $password,
            'tem' => $tem,
            'consecutive' => strval($consecutive)
        ];

        $result = $integrationServiceController->requestRenAppApi($url, $method, $data);
        $data = json_decode($result->getContent(), true);

        return $data;
    }

    /**
     * @param $id
     * @throws Exception
     */
    public function actionsPolicyAndQuotation($req, $id)
    {
        try {
            $client = Client::findOrFail(config('app.client'));
            // 1. Buscamos la activity de la poliza
            $activity = Activity::where('id', $id)
                ->where('service_id', Service::SERVICE_POLICY_SORT_MNK)->first();
            if (!$activity) {
                throw new \Exception('Actividad de póliza no encontrada para el id: ' . $id);
            }

            //Registrar la actividad en activity action
            $description = "Acción generada emitir póliza desde póliza";
            $activityAction = ActionController::create($activity->id, ActionPolizaSort::EMITIR_POLIZA, $description);

            $this->calculateConsecutive($activity->policy_sort);
            $consecutive = $activity->policy_sort->formatNumberConsecutive();
            //Buscamos la activity_id de la Cotización mediante el parent_id de la acttividad de la la poliza
            $activityQuotation = Activity::where('id', $activity->parent_id)
                ->first();
            //Cambiamos el estado de la cotización
            $description = "Se generó la acción emitir póliza";
            ActionController::create($activityQuotation->id, ActionCotizacionSort::EMITIR_POLIZA, $description);

            $existActionWithNotReport = $activity->activity_actions->where('action_id', ActionPolizaSort::REPORTAR_DOCUMENTOS_EXTERNOS_SIN_PLANILLAS)
                ->first();
            if (!$existActionWithNotReport) {
                $this->affiliatePayrollReport($req, $activity->id, $client, $consecutive);
            }
        } catch (Exception $e) {
            throw $e;
        }
    }

    // guardar consecutivo
    private function calculateConsecutive($policy)
    {
        // Obtener el último consecutivo
        $ultimoConsecutivo = PolicySort::max('consecutive');
        // Calcular el nuevo consecutivo
        $nuevoConsecutivo = $ultimoConsecutivo ? $ultimoConsecutivo + 1 : 1;
        $policy->consecutive = $nuevoConsecutivo;
        $policy->save();
        return $nuevoConsecutivo;
    }

    public function manualGenerateAccountingEntry($id)
    {
        $this->generateAccountingEntry('', $id);

        return 'OK';
    }

    //Metodo para insertar los asientos contables de la emisión de la poliza
    public function generateAccountingEntry($cpath, $id)
    {

        $policy = PolicySort::find($id);

        $username = $policy->code;

        $user = User::where(function ($query) use ($username) {
            $query->where('username', $username) // Buscar directamente sin "CO-"
                ->orWhere('username', 'CO-' . $username) // Buscar con "CO-"
                ->orWhere('code_mnk', $username)
                ->orWhere('code_mnk', 'CO-' . $username);
        })->first();


        // Si el CODCORREDOR es null, vacío, o igual a CODIGO, es un corredor independiente.
        $isIndependentBroker = empty($user->code_correduria) || (str_replace('CO-', '', $user->code_mnk) === str_replace('CO-', '', $user->code_correduria));

        // Determinar el tipo de intermediario basándonos en la lógica anterior.
        $intermediaryType = $isIndependentBroker
            ? 'corredor-independiente'
            : 'correduria';

        //Instanciamos el controlado para los asientos contables
        $accountingEntryController = new AccountingEntryController();
        $accountingEntryController->reportAccountCase107($policy);

        //registra salgo a favor
        $policySortCollectionController = new PolicySortCollectionController();
        $policySortCollectionController->insertPremiumSurplus($cpath, $policy->id);


        //Caso 1 el tipo de la moneda es colon y el intermediario es de tipo correduria
        if ($policy->type_currency == 'CRC' && $intermediaryType == "correduria") {

            // Llamar al método reportAccountCaseOne001 y pasar la póliza como parámetro
            $accountingEntryController->reportAccountCaseOne001($policy);

            return response()->json([
                'success' => true
            ]);
        }

        //Caso 2 el tipo de la moneda es colon y el intermediario es de tipo corredor o agente independiente
        if ($policy->type_currency == 'CRC' && $intermediaryType == "corredor-independiente") {

            // Llamar al método reportAccountCaseOne001 y pasar la póliza como parámetro
            $accountingEntryController->reportAccountCaseTwo001($policy);

            return response()->json([
                'success' => true
            ]);
        }

        //Caso 3 el tipo de la moneda es Dolar y el intermediario es de tipo correduria
        if ($policy->type_currency == 'USD' && $intermediaryType == "correduria") {

            // Llamar al método reportAccountCaseOne001 y pasar la póliza como parámetro
            $accountingEntryController->reportAccountCaseTree001($policy);

            return response()->json([
                'success' => true
            ]);
        }

        //Caso 3 el tipo de la moneda es Dolar y el intermediario es de tipo corredor o agente independiente
        if ($policy->type_currency == 'USD' && $intermediaryType == "corredor-independiente") {

            // Llamar al método reportAccountCaseOne001 y pasar la póliza como parámetro
            $accountingEntryController->reportAccountCaseFour001($policy);

            return response()->json([
                'success' => true
            ]);
        }

    }

    private function firma_document($ubicacion, $activity, $activityAction)
    {

        /*
         * Documento PDF
         * */

        $signature_document_fisica = $activity->activity_actions->where('action_id', Action::REPORTAR_FIRMA_FISICA)->first();

        $signature_document_digital = $activity->activity_actions->where('action_id', Action::REPORTAR_FIRMA_DIGITAL)->first();

        if ($signature_document_fisica) {
            $document = $signature_document_fisica->documents->last();
            $activityActionDocument = new ActivityActionDocument();
            $activityActionDocument->activity_action_id = $activityAction->id;
            $activityActionDocument->name = "Firma Física_" . $activityAction->id;
            $activityActionDocument->path = $document->path;
            $activityActionDocument->save();
            return $activityActionDocument;
        }

        if ($signature_document_digital) {
            $urls = [];

            $path1 = "policy_sort/sign1_" . $activity->id . ".png";
            $urls['sign1_url'] = Storage::disk('s3')->url($path1);

            $path2 = "policy_sort/sign2_" . $activity->id . ".png";
            $urls['sign2_url'] = Storage::disk('s3')->url($path2);

            $isplanilla = $activity->activity_actions->where('action_id', ActionPolizaSort::REPORTAR_DOCUMENTOS_EXTERNOS_SIN_PLANILLAS)
                ->first();

            $total_salaries = null;
            $observacion = null;
            $activitySpreadsheet = Activity::where('parent_id', $activity->id)->where('service_id', Service::SERVICE_REPORT_TAKEN_FORM_MNK)->first();
            if ($activitySpreadsheet) {
                $policySpreadsheet = PolicySpreadsheet::where('activity_id', $activitySpreadsheet->id)->first();
                $total_salaries = $policySpreadsheet->total_salaries;
                $observacion = $policySpreadsheet->observacion;
            }


            $data = [
                'name' => $activity->affiliate->first_name,
                'idNumber' => $activity->affiliate->doc_number,
                'email' => $activity->affiliate->email,
                'ocupacion' => $activity->affiliate->occupation,
                'invoiceEmail' => $activity->affiliate->electronic_billing_email,
                'province' => $ubicacion['province'],
                'canton' => $ubicacion['canton'],
                'district' => $ubicacion['district'],
                'sign1_url' => $urls['sign1_url'],
                'sign2_url' => $urls['sign2_url'],
                'economicActivity' => $activity->policy_sort->activity_economic_id,
                'legalRepresentativeName' => $activity->policy_sort->legal_representative_name,
                'legalRepresentativeId' => $activity->policy_sort->legal_representative_id,
                'puesto_tomador' => $activity->affiliate->occupation_responsible,
                'name_tomador' => $activity->affiliate->first_name,
                'employer_address' => $activity->affiliate->employer_address,
                'phone' => $activity->affiliate->phone,
                'cellphone' => $activity->affiliate->cellphone,
                'economic_activity_name' => $activity->policy_sort->economic_activity_name,
                'validity_from' => date('d/m/y', strtotime($activity->policy_sort->validity_from)),
                'validity_to' => date('d/m/y', strtotime($activity->policy_sort->validity_to)),
                'isplanilla' => !!$isplanilla,
                'calendar' => $activity->policy_sort->calendar_period,
                'observacion' => $observacion,
                'total_salaries' => $total_salaries,
                'anual' => $activity->policy_sort->annual_calculation_amount,
                'type_currency' => $activity->policy_sort->type_currency,
                'salary_projection' => $activity->policy_sort->salary_projection,
                'temporality' => $activity->policy_sort->temporality,
                'single_payment_value' => $activity->policy_sort->single_payment_value,
            ];

            // Cargar la vista del PDF y pasarle los datos
            $document = 'download_signature_mnk';

            $pdf = PDF::loadView("services.policy_sort.docs.{$document}", [
                'data' => $data,  // Reemplazamos los datos generados anteriormente
                'watermark' => false,
            ]);

            $filePath = "activity_action_document/{$document}_{$activity->id}.pdf";

            Storage::disk('s3')->put($filePath, $pdf->output());

            $activityActionDocument = new ActivityActionDocument();
            $activityActionDocument->activity_action_id = $activityAction->id;
            $activityActionDocument->name = "Firma digital_" . $activityAction->id;
            $activityActionDocument->path = $filePath;
            $activityActionDocument->save();

            return $activityActionDocument;
        }

        throw new \Exception('No se pudo generar el documento de firma');
    }

    // Función resendEmail para enviar correos con varios documentos adjuntos
    public function resendEmailDigital(Request $req, $activity, $activity_service, $activityAction, $emails, $client_id, $files)
    {
        $tomador = mb_convert_case(mb_strtolower($activity->affiliate->first_name), MB_CASE_TITLE, "UTF-8");

        $emailData = TemplateBuilder::build(
            Templates::EMAIL_FOR_DIGITAL_SIGNATURE_UPLOAD,
            [
                'name' => $tomador,
            ]
        );

        // Lógica para el envío de correos
        if ($emails != null) {

            // Enviar el correo con los documentos adjuntos
            $mailSent = new SendDocumentDataBase(
                $emails,
                $emailData['subject'],
                "<EMAIL>", // Email de remitente
                "Firma digital generada",
                [
                    "text" => $emailData['body'],
                    "sender" => $emailData['sender']
                ],
                "<EMAIL>", // Responder a este email
                $files, // Adjuntos (PDFs generados y los de public)
                "send_document_db",
                $client_id,
                $req->getHost(),
                $activity,
                $activityAction,
                $activity_service
            );

            // Capturar el resultado del envío
            $result = $mailSent->sendMail();

            //Registramos los datos del correo enviado para la trazabilidad
            $mailBoardController = new MailBoardController();
            $mailBoardController->createRegisterMail(
                $activity->id,
                $activity->service->id,
                $activity->policy_sort->consecutive,
                'Tomador',
                mb_convert_case(mb_strtolower($activity->affiliate->full_name ?? ''), MB_CASE_TITLE, "UTF-8"),
                $activity->affiliate->doc_number,
                $emailData['subject'],
                $emailData['body'],
                $emails,
                $result,
                $files
            );
        }
    }

    // Función resendEmail para enviar correos con varios documentos adjuntos
    public function resendEmail(Request $req, $activity, $activity_service, $activityAction, $emails, $client_id, $files, $body, $mailer = 'default')
    {
        $subject = 'Documentos de póliza '; // Asunto del correo
        $text = $body;

        // Lógica para el envío de correos
        if ($emails != null) {

            // Enviar el correo con los documentos adjuntos
            $mailSent = new SendDocumentDataBase(
                $emails,
                $subject,
                "<EMAIL>", // Email de remitente
                "Poliza Emitida",
                [
                    "text" => $text,
                    "sender" => 'Sistema de Seguros'
                ],
                "<EMAIL>", // Responder a este email
                $files, // Adjuntos (PDFs generados y los de public)
                "send_document_db",
                $client_id,
                request()->getHost(),
                $activity,
                $activityAction,
                $activity_service,
                Service::SERVICE_POLICY_SORT_MNK
            );
            $mailSent->sendMail();
        }
    }


    // Función resendEmail para enviar correos al intermediario en la emisión de la póliza
    public function resendEmailIntermediary(Request $req, $activity, $activity_service, $activityAction, $emails, $client_id, $files, $bodyIntermediary, $mailer = 'default')
    {
        $subject = 'Emisión de la póliza #SORT-' . $activity->policy_sort->id;

        $text = $bodyIntermediary;

        // Lógica para el envío de correos
        if ($emails != null) {

            // Enviar el correo con los documentos adjuntos
            $mailSent = new SendDocumentDataBase(
                $emails,
                $subject,
                "<EMAIL>", // Email de remitente
                "Poliza Emitida",
                [
                    "text" => $text,
                    "sender" => 'mnk aseguramiento'
                ],
                "<EMAIL>", // Responder a este email
                $files, // Adjuntos (PDFs generados y los de public)
                "send_document_db",
                $client_id,
                $req->getHost(),
                $activity,
                $activityAction,
                $activity_service,
                Service::SERVICE_POLICY_SORT_MNK
            );
            $mailSent->sendMail();
        }
    }

    public function report_affiliates(Request $req, $cpath, $id)
    {
        // Buscar el cliente
        $client = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('client_id', $client->id)
            ->where('id', $id)
            ->where('state_id', 1)
            ->first();

        if (!$activity) {
            return response()->json(['message' => 'Actividad no encontrada, o en estado difernete a planilla reportada'], 404);
        }

        // Registrar la acción REPORTAR PLANILLA TOMADOR
        $descriptionReportarPlanilla = "Acción generada REPORTAR PLANILLA TOMADOR";

        // Registrar la acción GENERAR CERTIFICADO
        ActionController::create(
            $activity->id,
            Action::REPORTAR_PLANILLA_TOMADOR,
            $descriptionReportarPlanilla
        );
        $host = $req->getHost();

        // Llamar a generateCertificate con el ID de la actividad
        $this->generateCertificate($activity->id, $client, $host);

        return response()->json(['response' => true, 'message' => 'Correos enviados correctamente']);
    }

    public function generateCertificate($client, $id, $host)
    {
        $activity = Activity::where('id', $id)->first();

        if (!$activity) {
            throw new Exception('No se encontro la actividad para generar el certificado del tomador');
        }

        //Buscamos la actividad de la planilla por la poliza
        $activityParten = Activity::where('parent_id', $activity->id)
            ->where('service_id', Service::SERVICE_REPORT_TAKEN_FORM_MNK)
            ->latest('created_at')
            ->first();

        //Buscamos la planilla mediante su actividad
        $policySpreadSheet = PolicySpreadsheet::where('activity_id', $activityParten->id)->first();

        // Descripción para la acción GENERAR CERTIFICADO
        $descriptionGenerarCertificado = "Acción generada generar certificado tomador";

        // Registrar la acción GENERAR CERTIFICADO
        $activityAction = ActionController::create(
            $activityParten->id,
            Action::GENERAR_CERTIFICADO_TOMADOR,
            $descriptionGenerarCertificado
        );

        //Email afiliado
        $emailAffiliate = $activity->affiliate->email;

        // Si no hay correos electrónicos, devolver un mensaje de error no debe de haber correos null
        if (!$emailAffiliate) {
            throw new Exception('No se encontro correo electrónico para el afiliado');
        }
        //Obtener emails tomadores autorizados
        $idActivityTaker = $activity->id;
        $takerAuthData = $this->getAuthorizedTakerEmails($idActivityTaker);

        $emailUsersTakerAuthorized = $takerAuthData->pluck('email');

        $allEmails = collect([$emailAffiliate])
            ->merge($emailUsersTakerAuthorized)
            ->filter(function ($email) {
                return !empty($email);
            })
            ->unique()
            ->values();

        $finalEmailsArray = $allEmails->toArray();


        // Filtrar correos válidos
        $validEmails = array_filter([$finalEmailsArray], function ($email) {
            return filter_var($email, FILTER_VALIDATE_EMAIL);
        });

        // Generar documento "Pago por reconocimiento de gastos"
        $documentName = "certificado_tomador_" . $activityAction->id . ".pdf";

        //Construcción de PDF
        $data = PolicySpreadsheet::with([
            'policy_spreadsheet_affiliate' => function ($query) {
                $query->where('temporal', 0);
            },
            'activity'
        ])
            ->where('id', $policySpreadSheet->id)
            ->first();

        $numeroTotalPoliza = PolicySpreadsheet::where('activity_id', $data->activity_id)->count();

        $policy = Activity::where('id', $data->activity->parent_id)->first();

        $numPoliza = '';
        try {
            $numPoliza = $activity->policy_sort->formatSortNumber();
        } catch (Exception $e) {
            $numPoliza = '';
        }

        $subject = "Recepción y aceptación de su planilla mensual (póliza " . $numPoliza . ")";

        $jsonPath = public_path('js/paises.json');
        $jsonContent = File::get($jsonPath);
        $countries = json_decode($jsonContent, true);


        foreach ($data->policy_spreadsheet_affiliate as &$employee) {
            $employee['nationality'] = $this->getCountryName($employee['nationality'], $countries);
        }

        $pdfContent = PDF::loadView(
            'services.policy_sort.docs.planilla_dinamic',
            [
                'watermark' => false,
                'data' => $data,
                'policy' => $activity,
                'total' => $numeroTotalPoliza
            ]
        )->setPaper('a4', 'landscape')->output();

        // Subir el archivo PDF a S3
        $filePath = "activity_action_document/{$documentName}";
        Storage::disk('s3')->put($filePath, $pdfContent);

        // Guardar en la tabla activity_action_documents
        $activityActionDocument = new ActivityActionDocument();
        $activityActionDocument->activity_action_id = $activityAction->id;
        $activityActionDocument->name = $documentName;
        $activityActionDocument->path = "activity_action_document/{$documentName}";
        $activityActionDocument->save();


        // Formar archivo para adjuntar en el correo
        $files[] = [
            'type' => 'pdf',
            'path' => $filePath,
            'name' => $documentName,
        ];
        // Crear el correo para el beneficiario o afiliado

        $client_id = $client->id;


        //Asignación de mes y año correspondientes
        setlocale(LC_TIME, 'es_ES.UTF-8');
        $month = strftime('%B');
        $year = date('Y');
        $nameTaker = mb_convert_case(mb_strtolower($activityParten->affiliate->full_name), MB_CASE_TITLE, "UTF-8");
        // Creación del correo
        $text = "¡Buen día, " . $nameTaker . "!
        
        ¡Muchas gracias por enviarnos su planilla mensual de trabajadores correspondiente a $month de $year!
        
        Le confirmamos que recibimos la información satisfactoriamente y la aceptamos para el proceso de registro y gestión de pólizas.
        
        En caso de que tenga alguna consulta al respecto o necesite realizar algún ajuste, por favor, gestiónelo en nuestra plataforma a más tardar hoy a las 23:59 horas.
        
        ¡Muchas gracias por la confianza que ha depositado en nosotros! Nuestro propósito es transformar la protección en una experiencia ágil, confiable y humana.";

        if(!empty($validEmails)){

            $mailSent = new SendDocumentDataBase(
                $validEmails,
                $subject,
                "<EMAIL>",
                "Recepción y aceptación de su planilla mensual (póliza " . $numPoliza . ")",
                [
                    "text" => $text,
                    "sender" => 'mnk aseguramiento'
                ],
                "<EMAIL>",
                $files, // Adjuntar el documento generado
                "send_document_db",
                $client_id,
                $host,
                $activity->id,
                $activityAction->id,
                Service::SERVICE_POLICY_SORT_MNK
            );

            // Capturar el resultado del envío
            $result = $mailSent->sendMail();

            //Registramos los datos del correo enviado para la trazabilidad
            $mailBoardController = new MailBoardController();

            foreach ($validEmails as $email) {

                $takerAuthorizedId = null;
                $authorizedTaker = $takerAuthData->firstWhere('email', $email);
                if ($authorizedTaker) {
                    $takerAuthorizedId = $authorizedTaker->id;
                }
                $mailBoardController->createRegisterMail(
                    $activityParten->id,
                    $activityParten->service->id,
                    $policy->consecutive,
                    'Tomador',
                    $activityParten->affiliate->full_name,
                    $activityParten->affiliate->doc_number,
                    $subject,
                    $text,
                    $validEmails,
                    $result,
                    null,
                    $takerAuthorizedId
                );
            }

        }

    }


    public function resendEmailCert(Request $req, $activity, $activity_service, $activityAction, $emails, $subject, $client_id, $document, $text, $arrayData)
    {
        $files = [];
        $paths = [];

        // Generar el PDF
        $pdf = PDF::loadView("services.plantilla.docs.{$document}_pdf", ['arrayData' => $arrayData, 'watermark' => false]);

        // Guardar el PDF en S3
        Storage::disk('s3')
            ->put("activity_action_document/{$document}_{$activityAction}.pdf", $pdf->output());

        // Guardar en la tabla activity_action_documents
        $activityActionDocument = new ActivityActionDocument();
        $activityActionDocument->activity_action_id = $activityAction;
        $activityActionDocument->name = $document;
        $activityActionDocument->path = "activity_action_document/{$document}_{$activityAction}.pdf";
        $activityActionDocument->save();

        // Formar archivo para adjuntar en el correo
        $files[] = [
            'type' => 'pdf',
            'path' => "activity_action_document/{$document}_{$activityAction}.pdf",
            'name' => $document . '.pdf',
        ];

        // Lógica para el envío de correo a cada afiliado
        if (!empty($emails)) {
            foreach ($emails as $email) {
                // Crear una instancia de envío de correo
                $mailSent = new SendDocumentDataBase(
                    $email,
                    $subject,
                    "<EMAIL>",
                    "CERTIFICADO GENERADO",
                    [
                        "text" => $text,
                        "sender" => 'Prueba'
                    ],
                    "<EMAIL>",
                    $files,
                    "send_document_db",
                    $client_id,
                    $req->getHost(),
                    $activity,
                    $activityAction,
                    $activity_service
                );

                // Enviar el correo
                $mailSent->sendMail();
            }
        }
    }

    public function search(Request $request)
    {

        $request->validate([
            'doc_type' => 'required|string',
            'doc_number' => 'required|string',
        ]);

        $affiliates = PolicySpreadsheetAffiliate::where('id_type', $request->doc_type)
            ->where('identification_number', $request->doc_number)
            ->first();

        if ($affiliates) {
            return response()->json($affiliates);
        } else {
            return response()->json(null);
        }
    }

    public function update(Request $request)
    {

        if ($request->has('date_of_birth')) {
            $request->merge([
                'date_of_birth' => Carbon::createFromFormat('d/m/Y', $request->input('date_of_birth'))->format('Y-m-d'),
            ]);
        }

        $request->validate([
            'id' => 'required|integer|exists:policy_spreadsheet_affiliates,id',
            'id_type' => 'required|string',
            'nationality' => 'required|string',
            'identification_number' => 'required|string',
            'first_name' => 'required|string',
            'last_name' => 'required|string',
            'date_of_birth' => 'required|date',
            'gender' => 'required|string',
            'work_shift_type' => 'required|string',
            'monthly_salary' => 'required|numeric',
            'days' => 'required|integer',
            'hours' => 'required|integer',
            'occupation' => 'required|string',
        ]);

        $affiliate = PolicySpreadsheetAffiliate::findOrFail($request->id);

        $affiliate->id_type = $request->id_type;
        $affiliate->nationality = $request->nationality;
        $affiliate->identification_number = $request->identification_number;
        $affiliate->first_name = $request->first_name;
        $affiliate->last_name = $request->last_name;
        $affiliate->date_of_birth = $request->date_of_birth;
        $affiliate->gender = $request->gender;
        $affiliate->work_shift_type = $request->work_shift_type;
        $affiliate->monthly_salary = $request->monthly_salary;
        $affiliate->days = $request->days;
        $affiliate->hours = $request->hours;
        $affiliate->occupation = $request->occupation;

        $affiliate->save();

        return response()->json([
            'message' => 'Afiliado actualizado correctamente.',
            'affiliate' => $affiliate
        ], 200);
    }

    public function delete(Request $request)
    {
        $id = $request->input('id');

        $affiliate = PolicySpreadsheetAffiliate::find($id);

        if ($affiliate) {
            $affiliate->delete();
            return response()->json(['success' => true], 200);
        } else {
            return response()->json(['success' => false, 'message' => 'Registro no encontrado'], 404);
        }
    }

    public function intermediary(Request $req, $cpath, $id)
    {

        Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('id', $id)->firstOrFail();

        return view('services.policy_sort.intermediary_policy.menu.intermediary_data', [
            'id' => $id,
            'activity' => $activity,
            'policy_sort' => $activity->policy_sort,
        ]);
    }

    public function guardarintermediary(Request $request)
    {
        PolicySort::query()
            ->where('activity_id', $request->activities)
            ->update([
                'email' => $request->input('new_emails')
            ]);

        // Retornar respuesta JSON
        return response()->json(['message' => 'Datos guardados correctamente'], 200);
    }

    public function emission(Request $req, $cpath, $id)
    {
        //Obtiene los datos del cleinte
        $client = Client::where('path', $cpath)->firstOrFail();

        //Obtiene la actividad de la poliza por su activity_id
        $activity = Activity::where('client_id', $client->id)
            ->where('id', $id)
            ->firstOrFail();

        //Json de actividades economica
        $jsonSource = ($activity->policy_sort->economic_activity == 'public') ? '/js/economic_activity/public.json' : '/js/economic_activity/private.json';
        $json = file_get_contents(public_path($jsonSource));
        $economicActivities = json_decode($json, true);

        //Se transforma a una collección en laravel
        $activity_economic_name = collect($economicActivities)->firstWhere('CODE', $activity->policy_sort->activity_economic_id)['ACTIVITY_NAME'];

        $activity->policy_sort->economic_activity_name = $activity_economic_name;

        //Obtenemos los contactos asociados a la poliza
        $policyContacts = PolicyContact::where('policy_sort_id', $activity->policy_sort->id)->get();
        //Obtenemos las direcciones adicionales asociadas a la poliza
        $policyAddresses = PolicyAddress::where('policy_sort_id', $activity->policy_sort->id)->get();
        //Obtenemos los telefonos adicionales asociadas a la poliza
        $policyPhones = PolicyPhone::where('policy_sort_id', $activity->policy_sort->id)->get();
        //Obtenemos los telefonos adicionales asociadas a la poliza
        $policyAdditionalNotificationEmails = PolicyAdditionalNotificationEmail::where('policy_sort_id', $activity->policy_sort->id)->get();
        // Obtenemos la cotización asociada a la actividad
        $activityQuotation = Quotation::where('activity_id', $activity->parent_id)->first();

        //Verificamos si existe firma digital
        $existActionDigital = $activity->activity_actions->where('action_id', Action::REPORTAR_FIRMA_DIGITAL)
            ->first();

        //Verificamos si existe firma fisica
        $existActionFisica = $activity->activity_actions->where('action_id', Action::REPORTAR_FIRMA_FISICA)
            ->first();

        $economicActivity = EconomicActivity::where('code', $activity->policy_sort->activity_economic_id)->first();

        // Prepara los datos para la vista
        return view('services.policy_sort.intermediary_policy.menu.emission_data', [
            'activity' => $activity,
            'affiliate' => $activity->affiliate,
            'policy_sort' => $activity->policy_sort,
            'id' => $id,
            'policy_contacts' => $policyContacts,
            'existActionDigital' => $existActionDigital,
            'existActionFisica' => $existActionFisica,
            'activityQuotation' => $activityQuotation,
            'economicActivity' => $economicActivity,
            'policy_addresses' => $policyAddresses,
            'policy_phones' => $policyPhones,
            'policy_additional_notification_emails' => $policyAdditionalNotificationEmails,
        ]);
    }

    public function deleteAdditionalAddress($cpath, $id, $addressId)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();
        $policysort = $activity->policy_sort;

        // Validar que la dirección adicional exista
        $address = PolicyAddress::where('policy_sort_id', $policysort->id)
            ->where('id', $addressId)
            ->first();

        if (!$address) {
            return response()->json([
                'success' => false,
                'message' => 'Dirección adicional no encontrada.'
            ], 404);
        }

        DB::beginTransaction();
        try {
            // Eliminar la dirección adicional
            $address->delete();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Dirección adicional eliminada correctamente.'
            ], 200);
        } catch (Exception $e) {
            Log::error('Error al eliminar la dirección adicional: ' . $e);
            DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => 'Error al eliminar la dirección: ' . $e->getMessage()
            ], 500);
        }
    }

    public function deleteAdditionalPhone($cpath, $id, $phoneId)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();
        $policysort = $activity->policy_sort;

        // Validar que la dirección adicional exista
        $phone = PolicyPhone::where('policy_sort_id', $policysort->id)
            ->where('id', $phoneId)
            ->first();

        if (!$phone) {
            return response()->json([
                'success' => false,
                'message' => 'Teléfono adicional no encontrada.'
            ], 404);
        }

        DB::beginTransaction();
        try {
            // Eliminar el teléfono adicional
            $phone->delete();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Teléfono adicional eliminada correctamente.'
            ], 200);
        } catch (Exception $e) {
            Log::error('Error al eliminar el teléfono adicional: ' . $e);
            DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => 'Error al eliminar el teléfono: ' . $e->getMessage()
            ], 500);
        }
    }

    public function deleteAdditionalEmail($cpath, $id, $emailId)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();
        $policysort = $activity->policy_sort;

        // Validar que la dirección de email adicional exista
        $email = PolicyAdditionalNotificationEmail::where('policy_sort_id', $policysort->id)
            ->where('id', $emailId)
            ->first();

        if (!$email) {
            return response()->json([
                'success' => false,
                'message' => 'Correo electrónico de notificaciones adicional no encontrado.'
            ], 404);
        }

        DB::beginTransaction();
        try {
            // Eliminar el Correo electrónico de notificaciones adicional
            $email->delete();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Correo electrónico de notificaciones adicional eliminado correctamente.'
            ], 200);
        } catch (Exception $e) {
            Log::error('Error al eliminar el correo electrónico de notificaciones adicional: ' . $e);
            DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => 'Error al eliminar el correo electrónico de notificaciones adicional: ' . $e->getMessage()
            ], 500);
        }
    }

    public function upsertAdditionalEmail(Request $request, $cpath, $id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();
        $policysort = $activity->policy_sort;

        // Validar los datos de entrada
        $request->validate([
            'ane_noti_email' => 'required|email|max:255',
            'ane_email_id' => 'nullable|numeric|exists:policy_additional_notification_emails,id',
        ], [
            'ane_noti_email.required' => 'El correo electrónico de notificaciones es obligatorio.',
            'ane_noti_email.email' => 'El correo electrónico de notificaciones debe ser una dirección de correo válida.',
            'ane_noti_email.max' => 'El correo electrónico de notificaciones no puede exceder los 255 caracteres.',
            'ane_email_id.exists' => 'El correo electrónico de notificaciones adicional especificado no existe.'
        ]);

        // Verificar si el email ya existe
        $existingEmail = PolicyAdditionalNotificationEmail::where('policy_sort_id', $policysort->id)
            ->where('id', $request->input('ane_email_id'))
            ->first();

        DB::beginTransaction();
        try {
            if ($existingEmail) {
                // Actualizar el email existente
                $existingEmail->update([
                    'email' => $request->input('ane_noti_email'),
                ]);
            } else {
                // Crear un nuevo email adicional
                $existingEmail = $policysort->policyAdditionalNotificationEmails()->create([
                    'email' => $request->input('ane_noti_email'),
                ]);
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Correo electrónico de notificaciones adicional guardado correctamente',
                'email' => $existingEmail
            ], 200);
        } catch (Exception $e) {
            Log::error('Error al guardar el Correo electrónico de notificaciones adicional: ' . $e);
            DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => 'Error al validar el Correo electrónico de notificaciones adicional: ' . $e->getMessage()
            ], 500);
        }
    }

    public function upsertAdditionalPhone(Request $request, $cpath, $id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();
        $policysort = $activity->policy_sort;

        // Validar los datos de entrada
        $request->validate([
            'phon_type' => 'required|string|max:50',
            'phon_number' => 'required|string|max:50',
            'phon_country' => 'required|string|max:10',
            'phon_extention' => 'nullable|string|max:20',
            'phon_id' => 'nullable|numeric|exists:policy_phones,id',
        ], [
            'phon_type.required' => 'El tipo de teléfono es obligatorio.',
            'phon_type.string' => 'El tipo de teléfono debe ser una cadena de texto.',
            'phon_type.max' => 'El tipo de teléfono no puede exceder los 50 caracteres.',
            'phon_number.string' => 'El teléfono completo debe ser una cadena de texto.',
            'phon_number.max' => 'El teléfono completo no puede exceder los 20 caracteres.',
            'phon_number.required' => 'El teléfono completo es obligatoria.',
            'phon_country.max' => 'El código de país no puede exceder los 10 caracteres.',
            'phon_country.required' => 'El código de país es obligatoria.',
            'phon_extention.max' => 'La extensión no puede exceder los 20 caracteres.',
            'phon_id.exists' => 'El teléfono adicional especificado no existe.'
        ]);

        // Verificar si el teléfono ya existe
        $existingPhone = PolicyPhone::where('policy_sort_id', $policysort->id)
            ->where('id', $request->input('phon_id'))
            ->first();

        DB::beginTransaction();
        try {
            if ($existingPhone) {
                // Actualizar el teléfono existente
                $existingPhone->update([
                    'type' => $request->input('phon_type'),
                    'full_number' => $request->input('phon_number'),
                    'country' => $request->input('phon_country'),
                    'extention' => $request->input('phon_extention'),
                ]);
            } else {
                // Crear un nuevo teléfono adicional
                $existingPhone = $policysort->policyPhones()->create([
                    'type' => $request->input('phon_type'),
                    'full_number' => $request->input('phon_number'),
                    'country' => $request->input('phon_country'),
                    'extention' => $request->input('phon_extention'),
                ]);
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Teléfono adicional guardado correctamente',
                'phone' => $existingPhone
            ], 200);
        } catch (Exception $e) {
            Log::error('Error al guardar el teléfono adicional: ' . $e);
            DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => 'Error al validar el teléfono: ' . $e->getMessage()
            ], 500);
        }
    }

    public function upsertAdditionalAddress(Request $request, $cpath, $id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();
        $policysort = $activity->policy_sort;

        // Validar los datos de entrada
        $request->validate([
            'addr_tipo' => 'required|string|max:50',
            'addr_direccion' => 'required|string|max:255',
            'addr_provincia' => 'required|numeric',
            'addr_canton' => 'required|numeric',
            'addr_distrito' => 'required|numeric',
            'addr_senas' => 'nullable|string|max:255',
            'addr_id' => 'nullable|numeric|exists:policy_addresses,id',
        ], [
            'addr_tipo.required' => 'El tipo de dirección es obligatorio.',
            'addr_tipo.string' => 'El tipo de dirección debe ser una cadena de texto.',
            'addr_tipo.max' => 'El tipo de dirección no puede exceder los 50 caracteres.',
            'addr_direccion.string' => 'La dirección debe ser una cadena de texto.',
            'addr_direccion.max' => 'La dirección no puede exceder los 255 caracteres.',
            'addr_direccion.required' => 'La dirección completa es obligatoria.',
            'addr_provincia.required' => 'La provincia es obligatoria.',
            'addr_canton.required' => 'El cantón es obligatorio.',
            'addr_distrito.required' => 'El distrito es obligatorio.',
            'addr_senas.max' => 'Señas del lugar no pueden exceder los 255 caracteres.',
            'addr_id.exists' => 'La dirección adicional especificada no existe.'
        ]);

        // Verificar si la dirección ya existe
        $existingAddress = PolicyAddress::where('policy_sort_id', $policysort->id)
            ->where('id', $request->input('addr_id'))
            ->first();

        DB::beginTransaction();
        try {
            if ($existingAddress) {
                // Actualizar la dirección existente
                $existingAddress->update([
                    'type' => $request->input('addr_tipo'),
                    'full_address' => $request->input('addr_direccion'),
                    'province_id' => $request->input('addr_provincia'),
                    'canton_id' => $request->input('addr_canton'),
                    'district_id' => $request->input('addr_distrito'),
                    'reference' => $request->input('addr_senas', ''),
                ]);
            } else {
                // Crear una nueva dirección adicional
                $existingAddress = $policysort->policyAddresses()->create([
                    'type' => $request->input('addr_tipo'),
                    'full_address' => $request->input('addr_direccion'),
                    'province_id' => $request->input('addr_provincia'),
                    'canton_id' => $request->input('addr_canton'),
                    'district_id' => $request->input('addr_distrito'),
                    'reference' => $request->input('addr_senas', ''),
                ]);
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Dirección adicional guardada correctamente',
                'address' => $existingAddress
            ], 200);
        } catch (Exception $e) {
            Log::error('Error al guardar la dirección adicional: ' . $e);
            DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => 'Error al validar la dirección: ' . $e->getMessage()
            ], 500);
        }
    }

    public static function getAdditionalNotificationEmails($policySortId, $otherEmails = [])
    {
        $policySort = PolicySort::with('policyAdditionalNotificationEmails')->find($policySortId);

        $emails = [];

        if ($policySort) {
            if (!empty($policySort->notification_email)) {
                $emails[] = $policySort->notification_email;
            }

            if (!empty($policySort->notification_email_additional)) {
                $emails[] = $policySort->notification_email_additional;
            }

            $additionalEmails = $policySort->policyAdditionalNotificationEmails
                ->pluck('email')
                ->filter() // elimina nulos o vacíos
                ->toArray();

            $emails = array_merge($emails, $additionalEmails);
        }

        if (!empty($otherEmails)) {
            $emails = array_merge($emails, $otherEmails);
        }

        return array_unique($emails);
    }

    public function emissionSave(Request $request, $cpath, $id)
    {

        $client = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();
        $policysort = $activity->policy_sort;

        DB::beginTransaction();
        try {
            // Captura los datos enviados desde la solicitud AJAX
            $policysort->doc_type = $request->input('docType');
            $policysort->doc_number = $request->input('numberIdentify');
            $policysort->first_name = $request->input('policyHolderName');
            $policysort->phones = $request->input('policyHolderPhone');
            $policysort->legal_representative_name = $request->input('legalRepresentativeName');
            $policysort->legal_representative_id = $request->input('legalRepresentativeId');
            $policysort->legal_representative_profession = $request->input('legalRepresentativeProfession');

            $policysort->legal_representative_occupancy_group_id = $request->input('legalRepresentativeOccupancyGroupId');
            $policysort->legal_representative_occupations_category_id = $request->input('legalRepresentativeOccupationsCategoryId');

            $policysort->economic_activity = $request->input('sector');
            $policysort->activity_economic_id = $request->input('economic_activity_code');
            $policysort->notification_email = $request->input('policyHolderEmail');
            $policysort->notification_email_additional = $request->input('policyHolderEmailAdditional');
            $policysort->work_modality_id = $request->input('workRisk');
            $policysort->assurance = $request->input('assurance');
            $policysort->save();

            $affiliate = Affiliate::where('client_id', $client->id)
                ->where('doc_type', $request->input('docType'))
                ->where('doc_number', $request->input('numberIdentify'))
                ->first();

            if (!$affiliate) {
                $affiliate = new Affiliate;
                $maxCode = Affiliate::max('unique_code');
                $nextCode = $maxCode ? intval($maxCode) + 1 : 1;
                $affiliate->unique_code = $nextCode;
            }

            $affiliate->client_id = $client->id;
            $affiliate->doc_type = $request->input('docType');
            $affiliate->doc_number = $request->input('numberIdentify');
            $affiliate->first_name = $request->input('policyHolderName');
            $affiliate->occupation = $request->input('occupation');
            $affiliate->occupations_category_id = $request->input('occupations_category_id');
            $affiliate->occupancy_group = intval($request->input('occupancy_group'));
            $affiliate->phone = $request->input('policyHolderPhone');
            $affiliate->province = $request->input('province');
            $affiliate->canton = $request->input('canton');
            $affiliate->district = $request->input('district');
            $affiliate->iban_account = $request->input('ibanAccount');
            $affiliate->electronic_billing_email = $request->input('emailElectronicBilling');
            $affiliate->employer_address = $request->input('employerAddress');
            $affiliate->email = $request->input('policyHolderEmail');
            $affiliate->save();

            //Guardamos el id del afiliado (sea nuevo o existente) en la actividad policy
            $activity->affiliate_id = $affiliate->id;
            $activity->save();

            // Obtener todos los IDs de los contactos enviados desde el frontend
            $idsContact = collect($request->input('fields'))
                ->pluck('id')
                ->filter();

            // Obtener todos los IDs de los contactos que ya están en la base de datos
            $contactExists = PolicyContact::where('policy_sort_id', $policysort->id)
                ->pluck('id');

            // Identificar los contactos que deben ser eliminados por su Id
            $idsDelete = $contactExists->diff($idsContact);

            // Eliminar los contactos que no están presentes en los datos enviados
            PolicyContact::whereIn('id', $idsDelete)->delete();

            // Ahora actualizamos o creamos nuevos contactos
            foreach ($request->input('fields') as $field) {
                if (!empty($field['id'])) {

                    // Obtener el primer valor de 'type_identification'
                    $typeIdentification = strtoupper(explode(',', $field['type_identification'])[0]);

                    // Actualización de registro existente
                    $responsable = PolicyContact::find($field['id']);
                    if ($responsable) {
                        $responsable->name_responsible = $field['name_responsible'];
                        $responsable->type_identification = $typeIdentification;
                        $responsable->number_identify_responsible = $field['number_identify_responsible'];
                        $responsable->ocupation_responsible = $field['ocupation_responsible'];
                        $responsable->occupations_category_id = $field['occupations_category_id'];
                        $responsable->phone_responsible = $field['phone_responsible'];
                        $responsable->cellphone_responsible = $field['cellphone_responsible'];
                        $responsable->email_responsible = $field['email_responsible'];
                        $responsable->save();
                    }
                } else {

                    // Obtener el primer valor de 'type_identification'
                    $typeIdentification = explode(',', $field['type_identification'])[0];

                    // Crear un nuevo registro si no tiene ID
                    PolicyContact::create([
                        'policy_sort_id' => $policysort->id,
                        'name_responsible' => $field['name_responsible'],
                        'type_identification' => $typeIdentification,
                        'number_identify_responsible' => $field['number_identify_responsible'],
                        'ocupation_responsible' => $field['ocupation_responsible'],
                        'occupations_category_id' => $field['occupations_category_id'],
                        'phone_responsible' => $field['phone_responsible'],
                        'cellphone_responsible' => $field['cellphone_responsible'],
                        'email_responsible' => $field['email_responsible'],
                    ]);
                }
            }

            DB::commit();

            // Retornar una respuesta, puede ser un JSON con un mensaje de éxito
            return response()->json(['message' => 'Datos recibidos correctamente']);
        } catch (Exception $e) {
            Log::error('Error al guardar los datos de emisión: ' . $e, [
                'request' => $request->all(),
                'activity_id' => $id,
                'client_path' => $cpath,
            ]);
            DB::rollBack();
            return response()->json(['error' => 'Ocurrió un error al guardar los datos', 'message' => $e->getMessage()], 500);
        }
    }

    public function downloadDocument(Request $req, $id)
    {
        $activity = Activity::find($req->id);
        $activityAction = $activity->activity_actions->where('action_id', 16)->first();
        $documents = ActivityActionDocument::where('activity_action_id', $activityAction->id)->get();

        // Generar URLs firmadas (enlace de descarga) para cada documento
        $documentPaths = $documents->pluck('path')->map(function ($path) {
            return Storage::disk('s3')->temporaryUrl(
                $path,
                now()->addMinutes(30),
                ['ResponseContentDisposition' => 'attachment']
            );
        })->toArray();

        // Retornar las URLs firmadas para descarga directa
        return response()->json([
            'documentPaths' => $documentPaths,
        ]);
    }

    public function policyEmission(Request $req, $cpath, $id)
    {

        $client = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();
        $policysort = $activity->policy_sort;

        if ($req->method() == 'POST') {

            try {
                DB::beginTransaction();
                if ($req->period == 2) {

                    PolicyCalendar::where('policy_sort_id', $policysort->id)->delete();

                    $data = request()->all();

                    // Filtrar valores nulos de cada campo
                    $startDates = array_filter($data['start_Date']);
                    $endDates = array_filter($data['end_Date']);
                    $numberOfPeriods = array_filter($data['number_of_periods']);

                    if (count($numberOfPeriods) < 12){
                        return back()->withErrors('Debe ingresar al menos 12 periodos.')->withInput();
                    }
                    // Crear arrays para las fechas válidas
                    $validStartDates = [];
                    $validEndDates = [];

                    // Validar fechas de inicio
                    foreach ($startDates as $date) {
                        if (preg_match('/^\d{4}-\d{2}-\d{2}$/', $date)) {
                            $validStartDates[] = $date; // Solo agregar fechas válidas
                        }
                    }

                    // Validar fechas de finalización
                    foreach ($endDates as $date) {
                        if (preg_match('/^\d{4}-\d{2}-\d{2}$/', $date)) {
                            $validEndDates[] = $date; // Solo agregar fechas válidas
                        }
                    }

                    $errors = [];

                    for ($i = 1; $i < count($validStartDates); $i++) {
                        $previousEnd = Carbon::parse($validEndDates[$i - 1]);
                        $currentStart = Carbon::parse($validStartDates[$i]);

                        if ($currentStart->lessThanOrEqualTo($previousEnd)) {
                            $errors[] = "La fecha de inicio del período " . ($i + 1) . " no puede ser menor o igual a la fecha de finalización del período " . ($i);
                        }
                    }

                    if (!empty($errors)) {
                        return back()->withErrors($errors)->withInput();
                    }

                    // Contar el número máximo de entradas válidas
                    $recordCount = max(count($validStartDates), count($validEndDates), count($numberOfPeriods));
                    $totalPeriods = 26;
                    // Guardar los datos
                    $periods = [];
                    for ($i = 0; $i < $recordCount; $i++) {
                        //calcular notify_date y deadline_date
                        $carbonEndDates = isset($validEndDates[$i]) ? Carbon::parse($validEndDates[$i]) : null;
                        $deadlineDate = Utilities::addBusinessDaysFrom(10, $carbonEndDates)->format('Y-m-d');
                        $notifyDate = Utilities::addBusinessDaysFrom(5, $carbonEndDates)->format('Y-m-d');

                        $periods[] = [
                            'policy_sort_id' => $policysort->id,
                            'start_date' => isset($validStartDates[$i]) ? $validStartDates[$i] : null,
                            'end_date' => isset($validEndDates[$i]) ? $validEndDates[$i] : null,
                            'number_period' => isset($numberOfPeriods[$i]) ? $numberOfPeriods[$i] : 0,
                            'created_at' => now(),
                            'updated_at' => now(),
                            'notify_date' => $notifyDate,
                            'deadline_date' => $deadlineDate,
                        ];
                    }
                    // Asegurarse de que el número total de períodos no exceda el máximo permitido
                    if (count($periods) > $totalPeriods) {
                        return back()->withErrors('El número total de períodos no puede exceder ' . $totalPeriods . '.')->withInput();
                    }

                    //calculamos el payroll_frequency
                    if (count($periods) <= 13) {
                        $policysort->payroll_frequency = 'monthly';
                    } elseif (count($periods) >= 24) {
                        $policysort->payroll_frequency = 'biweekly';
                    } else {
                        $policysort->payroll_frequency = 'custom';
                    }
                    // Guardar los períodos en la base de datos
                    PolicyCalendar::insert($periods);

                }

                $policysort->periodicity = $req->input('periodicidad') ?: $policysort->periodicity;
                $policysort->temporality = $req->input('temporality') ?: $policysort->temporality;

                if (
                    isset($policysort->validity_to, $req->validity_to_new, $req->validity_from_new)
                    && $policysort->validity_to !== $req->validity_to_new
                ) {
                    // Si ambas fechas existen (no son nulas) procedemos a la comparación

                    $newValidity = new DateTime($req->validity_to_new);
                    $validity_from = new DateTime($req->validity_from_new);
                    // Verificar si la nueva fecha es menor (anterior) que la fecha actual


                    $diff = $validity_from->diff($newValidity);
                    $policysort->change_date = $diff->days;
                }


                $policysort->validity_from = $req->validity_from_new;
                $policysort->validity_to = $req->validity_to_new;
                $policysort->type_currency = $req->input('type_currency') ?: $policysort->type_currency;
                $salaryProjection = $req->input('salary_projection') ?: $policysort->salary_projection;
                // Reemplaza primero todas las comas por #
                $salaryProjection = str_replace(',', '#', $salaryProjection);
                // Luego, reemplaza todos los puntos por vacío
                $salaryProjection = str_replace('.', '', $salaryProjection);
                // Finalmente, reemplaza todos los # por puntos
                $salaryProjection = str_replace('#', '.', $salaryProjection);
                // Quita el primer carácter de la cadena
                $salaryProjection = substr($salaryProjection, 1);
                $salaryProjection = preg_replace('/[^\d.]/', '', $salaryProjection);
                // Asigna el valor procesado al campo correspondiente
                $policysort->salary_projection = $salaryProjection;
                $policysort->work_modality_id = $req->input('workRisk') ?: $policysort->work_modality_id;
                $policysort->calendar_period = $req->input('period') ?: $policysort->calendar_period;
                $policysort->number_workers_optional = $req->input('number_workers') ?: $policysort->number_workers_optional;
                $policysort->save();

            //Calculamos si la póliza tiene condiciones especiales
            $quotation = Quotation::where('activity_id', $policysort->activity->parent_id)
                ->first();

            $conditionSpecial = QuotationConditionSpecial::where('activity_id', $quotation->activity_id)
                ->first();

            // si la poliza tiene condiciones especiales realizamos el nuevo calculo de la prima
            // if($conditionSpecial){
            //     $this->calculateConditionSpecialPolicyPrice($policysort);
            // }

                DB::commit();

                $url = '/intermediario/poliza/' . $id . '/datos_firma';
                return redirect($url);

            }catch (Exception $e){
                DB::rollBack();

                return back()->withErrors('Error al intentar guardar la informacion')->withInput();
            }

        }

        $policy_calendar = PolicyCalendar::where('policy_sort_id', $policysort->id)->get();

        //consultar la última planilla asociada a la póliza
        $activitySpreadsheet = Activity::where("parent_id", $policysort->activity_id)
            ->where("service_id", Service::SERVICE_REPORT_TAKEN_FORM_MNK)
            ->whereIn("state_id", [StatePoliza::PLANILLA_REPORTADA, StatePoliza::CERTIFICADO_REPORTADO_TOMADOR])
            ->orderBy('created_at', 'desc')
            ->first();

        //capturar el número de trabajadores de la planilla
        $number_workers_spreadsheet = null; // nulo para indicar que no hay una relación directa con póliza
        if ($activitySpreadsheet) {
            $number_workers_spreadsheet = PolicySpreadsheet::where('activity_id', $activitySpreadsheet->id)
                ->pluck('total_affiliates')->first();
        }

        return view('services.policy_sort.intermediary_policy.menu.policy_emission', [
            'id' => $id,
            'activity' => $activity,
            'quotation' => Quotation::where('activity_id', $activity->parent_id)->first(),
            'data' => PolicySort::findOrFail($policysort->id),
            'anual' => number_format($policysort->annual_calculation_amount, 2, ',', '.'),
            'semestral' => number_format($policysort->semiannual_calculation_amount, 2, ',', '.'),
            'trimestral' => number_format($policysort->quarterly_calculation_amount, 2, ',', '.'),
            'mensual' => number_format($policysort->monthly_calculation_amount, 2, ',', '.'),
            'unico' => number_format($policysort->single_payment_value, 2, ',', '.'),
            'valida_prima' => false,
            'policy_calendar' => $policy_calendar,
            'number_workers_spreadsheet' => !is_null($number_workers_spreadsheet) ? $number_workers_spreadsheet : 'No hay trabajadores', // cuando no hay trabajadores puede ser porque no se encontró una planilla asociada a la póliza
            'number_workers' => $policysort->number_workers_optional
        ]);
    }

    public function premiumCalculation(Request $req, $cpath, $id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();
        $policysort = $activity->policy_sort;

        if ($policysort->economic_activity && $policysort->activity_economic_id && $activity->state_id != StatePoliza::POLIZA_EMITIDA_ACTIVA) {

            $result = QuotationController::calculatePolicyPrice($policysort, false);

            //Si la modalidad de aseguramiento es distina a Riesgos del trabajo ocasionl y hogar.
            if ($policysort->work_modality_id != 3 && $policysort->work_modality_id != 4) {
                $policysort->amount_policy = $result['amountPolicy'];
            }

            //Si la modalidad de aseguramiento es igual a riesgos del trabajo hogar
            if ($policysort->work_modality_id == 3) {
                $values = [
                    1 => ['USD' => 131, 'CRC' => 67581],
                    2 => ['USD' => 246, 'CRC' => 126771],
                    3 => ['USD' => 361, 'CRC' => 185961]
                ];

                $policysort->amount_policy = $values[$policysort->option_asegurement][$policysort->type_currency === 'USD' ? 'USD' : 'CRC'];
            }

            //Si la modalidad de aseguramiento es igual a riesgos del trabajo ocasional
            if ($policysort->work_modality_id == 4) {

                $new_amount_policy = 0;

                // Si son colones convertimos los 100 usd a colones con la trm del dia 
                if ($policysort->type_currency == 'CRC') {

                    //Traemos la trm del dia
                    $webserviceController = new WebserviceAcselController();
                    $trm = $webserviceController->getTrm();

                    if ($trm == 0) {
                        throw new \Exception('Error: El TRM no es valido');
                    }

                    $new_amount_policy = 100 * $trm;
                }

                $values = [
                    1 => ['USD' => 100, 'CRC' => $new_amount_policy],
                ];

                $policysort->amount_policy = $values[1][$policysort->type_currency === 'USD' ? 'USD' : 'CRC'];
            }

            //Si la modalidad de aseguramiento es distina a Riesgos del trabajo ocasionl y hogar.
            if ($policysort->work_modality_id != 3 && $policysort->work_modality_id != 4) {
                $policysort->annual_calculation_amount = $result['amountPolicy'];
            }

            //Si la modalidad de aseguramiento es igual a riesgos del trabajo hogar
            if ($policysort->work_modality_id == 3) {
                $values = [
                    1 => ['USD' => 131, 'CRC' => 67581],
                    2 => ['USD' => 246, 'CRC' => 126771],
                    3 => ['USD' => 361, 'CRC' => 185961]
                ];

                $policysort->annual_calculation_amount = $values[$policysort->option_asegurement][$policysort->type_currency === 'USD' ? 'USD' : 'CRC'];
            }

            //Si la modalidad de aseguramiento es igual a riesgos del trabajo ocasional
            if ($policysort->work_modality_id == 4) {

                $new_amount_policy = 0;

                // Si son colones convertimos los 100 usd a colones con la trm del dia 
                if ($policysort->type_currency == 'CRC') {

                    //Traemos la trm del dia
                    $webserviceController = new WebserviceAcselController();
                    $trm = $webserviceController->getTrm();

                    if ($trm == 0) {
                        throw new \Exception('Error: El TRM no es valido');
                    }

                    $new_amount_policy = 100 * $trm;
                }

                $values = [
                    1 => ['USD' => 100, 'CRC' => $new_amount_policy],
                ];

                $policysort->annual_calculation_amount = $values[1][$policysort->type_currency === 'USD' ? 'USD' : 'CRC'];
            }

            //Si la modalidad de aseguramiento es distina a Riesgos del trabajo ocasionl y hogar.
            if ($policysort->work_modality_id != 3 && $policysort->work_modality_id != 4) {

                if ($policysort->option_asegurement != 1) {
                    $policysort->semiannual_calculation_amount = $result['amountPolicySemestral'];
                } else {
                    $policysort->semiannual_calculation_amount = 0;
                }
            }


            if ($policysort->work_modality_id == 1 || $policysort->work_modality_id == 2 || $policysort->work_modality_id == 5) {
                $policysort->semiannual_calculation_amount = $result['amountPolicySemestral'];
            }

            //Si la modalidad de aseguramiento es igual a riesgos del trabajo hogar
            if ($policysort->work_modality_id == 3) {
                $values = [
                    2 => ['USD' => 128, 'CRC' => 65921],
                    3 => ['USD' => 188, 'CRC' => 96700]
                ];

                $currency = $policysort->type_currency === 'USD' ? 'USD' : 'CRC';
                $policysort->semiannual_calculation_amount = $values[$policysort->option_asegurement][$currency] ?? 0;
            }

            //Si la modalidad de aseguramiento es igual a riesgos del trabajo ocasional
            if ($policysort->work_modality_id == 4) {
                $policysort->semiannual_calculation_amount = 0;
            }

            //Si la modalidad de aseguramiento es distina a Riesgos del trabajo ocasionl y hogar.
            if ($policysort->work_modality_id != 3 && $policysort->work_modality_id != 4) {
                $policysort->quarterly_calculation_amount = $result['amountPolicyTrimestral'];
            } else {
                $policysort->quarterly_calculation_amount = 0;
            }

            //Si la modalidad de aseguramiento es distina a Riesgos del trabajo ocasionl y hogar.
            if ($policysort->work_modality_id != 3 && $policysort->work_modality_id != 4) {
                $policysort->monthly_calculation_amount = $result['amountPolicyMensual'];
            } else {
                $policysort->monthly_calculation_amount = 0;
            }

            $policysort->single_payment_value = $result['valorUnico'];
            $policysort->save();
        }
        if ($req->method() == 'POST') {
            if ($policysort->temporality == 'permanent') {
                $policysort->periodicity = $req->input('periodicidad') ?: $policysort->periodicity;
                switch ($policysort->periodicity) {
                    case '1':
                        $policysort->amount_policy = $policysort->annual_calculation_amount;
                        break;
                    case '2':
                        $policysort->amount_policy = $policysort->semiannual_calculation_amount;
                        break;
                    case '3':
                        $policysort->amount_policy = $policysort->quarterly_calculation_amount;
                        break;
                    case '4':
                        $policysort->amount_policy = $policysort->monthly_calculation_amount;
                        break;
                }
            } else {
                $policysort->amount_policy = $policysort->single_payment_value;
            }
            $policysort->save();

            //Ejecutamos la acción GENERAR COBRO
            //Verificamos si ya se ejecuto la acción GENERAR COBRO
            $existAction = $activity->activity_actions->whereIn('action_id', [ActionPolizaSort::GENERAR_COBRO, ActionPolizaSort::GENERAR_COBRO_SIN_PLANILLA])
                ->first();
            if (!$existAction) {
                $activityPayment = $this->reportPaymentMade($req, $cpath, $policysort->activity_id);
                if ($activityPayment instanceof JsonResponse) {
                    throw new \Exception($activityPayment->getData()->message);
                }
                $url = '/servicio/' . trim($activityPayment) . '/policy_sort_collection/pago_poliza';
            } else {
                $activityPayment = Activity::where('parent_id', $activity->id)
                    ->where('service_id', Service::SERVICE_POLICY_SORT_COLLECTION_MNK)
                    ->first();
                $url = '/servicio/' . trim($activityPayment->id) . '/policy_sort_collection/pago_poliza';
            }
            return redirect($url);
        }

        //Buscamos la ultima actividad del reporte planilla tomador mediante la poliza
        $activityReport = Activity::where('parent_id', $policysort->activity_id)
            ->where('service_id', Service::SERVICE_REPORT_TAKEN_FORM_MNK)
            ->latest()
            ->first();

        $type_preport = "";
        if ($activityReport) {
            //Buscamos la planilla tomador mediante su actividad
            $lastReport = PolicySpreadsheet::where('activity_id', $activityReport->id)
                ->first();

            if ($lastReport->file == null) {
                $type_preport = 'manual';
            } else {
                $type_preport = 'masivo';
            }
        }


        return view('services.policy_sort.intermediary_policy.menu.go_to_payment', [
            'id' => $id,
            'lastReportId' => isset($lastReport) ? $lastReport->id : null,
            'type_preport' => $type_preport,
            'activity' => $activity,
            'data' => PolicySort::findOrFail($policysort->id),
            'anual' => number_format($policysort->annual_calculation_amount, 2, ',', '.'),
            'semestral' => number_format($policysort->semiannual_calculation_amount, 2, ',', '.'),
            'trimestral' => number_format($policysort->quarterly_calculation_amount, 2, ',', '.'),
            'mensual' => number_format($policysort->monthly_calculation_amount, 2, ',', '.'),
            'unico' => number_format($policysort->single_payment_value, 2, ',', '.'),
            'info' => 'La prima a pagar debe ser mayor a los $100 o a su equivalente en colones.',
        ]);
    }

    public function updatePolitySort(Request $request)
    {


        $request->merge([
            'startDate' => Carbon::createFromFormat('d/m/Y', $request->startDate)->format('Y-m-d'),
            'endDate' => Carbon::createFromFormat('d/m/Y', $request->endDate)->format('Y-m-d'),
            'validityFrom' => Carbon::createFromFormat('d/m/Y', $request->validityFrom)->format('Y-m-d'),
            'validityTo' => Carbon::createFromFormat('d/m/Y', $request->validityTo)->format('Y-m-d')
        ]);


        $request->validate([
            'startDate' => 'required|date',
            'endDate' => 'required|date',
            'numberOfPeriods' => 'required|integer',
            'validityFrom' => 'required|date',
            'validityTo' => 'required|date',
            'typeCurrency' => 'required|string',
            'salaryProjection' => 'required|numeric',
            'temporality' => 'required|in:permanent,short'
        ]);


        try {

            DB::beginTransaction();

            $policy = PolicySort::where('Activity_id', $request->id)->firstOrFail();

            $policy->period_start_date = $request->startDate;
            $policy->period_end_date = $request->startDate;
            $policy->number_of_periods = $request->numberOfPeriods;
            $policy->start_date_validity_policy = $request->validityFrom;
            $policy->renewal_date = $request->validityTo;
            $policy->type_currency = $request->typeCurrency;
            $policy->salary_projection = $request->salaryProjection;
            $policy->insurance_modality = $request->modality;
            $policy->periodicity = $request->periodicity;

            if ($request->temporality === 'permanent') {

                $policy->annual_calculation_amount = $request->annualCalculation;
                $policy->quarterly_calculation_amount = $request->quarterlyCalculation;
                $policy->monthly_calculation_amount = $request->monthlyCalculation;
                //$policy->semiannual_calculation_amount = $request->semiannualCalculation;

            } else {
                $policy->single_payment_value = $request->singlePayment;
            }

            $policy->save();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Registro actualizado.',
                'policy' => $policy
            ], 200);
        } catch (\Exception $e) {

            DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => 'Error al actualizar el registro: ' . $e->getMessage(),
            ], 500);
        }
    }

    public function signature(Request $req, $cpath, $id)
    {
        $policy = PolicySort::where('activity_id', $id)->first();

        $activity = Activity::find($id);


        if ($policy->economic_activity && $policy->activity_economic_id && $activity->state_id != StatePoliza::POLIZA_EMITIDA_ACTIVA) {

            $result = QuotationController::calculatePolicyPrice($policy, false);

            //Si la modalidad de aseguramiento es distina a Riesgos del trabajo ocasionl y hogar.
            if ($policy->work_modality_id != 3 && $policy->work_modality_id != 4) {
                $policy->amount_policy = $result['amountPolicy'];
            }

            //Si la modalidad de aseguramiento es igual a riesgos del trabajo hogar
            if ($policy->work_modality_id == 3) {
                $values = [
                    1 => ['USD' => 131, 'CRC' => 67581],
                    2 => ['USD' => 246, 'CRC' => 126771],
                    3 => ['USD' => 361, 'CRC' => 185961]
                ];

                $policy->amount_policy = $values[$policy->option_asegurement][$policy->type_currency === 'USD' ? 'USD' : 'CRC'];
            }

            //Si la modalidad de aseguramiento es igual a riesgos del trabajo ocasional
            if ($policy->work_modality_id == 4) {

                $new_amount_policy = 0;

                // Si son colones convertimos los 100 usd a colones con la trm del dia 
                if ($policy->type_currency == 'CRC') {

                    //Traemos la trm del dia
                    $webserviceController = new WebserviceAcselController();
                    $trm = $webserviceController->getTrm();

                    if ($trm == 0) {
                        throw new \Exception('Error: El TRM no es valido');
                    }

                    $new_amount_policy = 100 * $trm;
                }

                $values = [
                    1 => ['USD' => 100, 'CRC' => $new_amount_policy],
                ];

                $policy->amount_policy = $values[1][$policy->type_currency === 'USD' ? 'USD' : 'CRC'];
            }

            //Si la modalidad de aseguramiento es distina a Riesgos del trabajo ocasionl y hogar.
            if ($policy->work_modality_id != 3 && $policy->work_modality_id != 4) {
                $policy->annual_calculation_amount = $result['amountPolicy'];
            }

            //Si la modalidad de aseguramiento es igual a riesgos del trabajo hogar
            if ($policy->work_modality_id == 3) {
                $values = [
                    1 => ['USD' => 131, 'CRC' => 67581],
                    2 => ['USD' => 246, 'CRC' => 126771],
                    3 => ['USD' => 361, 'CRC' => 185961]
                ];

                $policy->annual_calculation_amount = $values[$policy->option_asegurement][$policy->type_currency === 'USD' ? 'USD' : 'CRC'];
            }

            //Si la modalidad de aseguramiento es igual a riesgos del trabajo ocasional
            if ($policy->work_modality_id == 4) {

                $new_amount_policy = 0;

                // Si son colones convertimos los 100 usd a colones con la trm del dia 
                if ($policy->type_currency == 'CRC') {

                    //Traemos la trm del dia
                    $webserviceController = new WebserviceAcselController();
                    $trm = $webserviceController->getTrm();

                    if ($trm == 0) {
                        throw new \Exception('Error: El TRM no es valido');
                    }

                    $new_amount_policy = 100 * $trm;
                }

                $values = [
                    1 => ['USD' => 100, 'CRC' => $new_amount_policy],
                ];

                $policy->annual_calculation_amount = $values[1][$policy->type_currency === 'USD' ? 'USD' : 'CRC'];
            }

            //Si la modalidad de aseguramiento es distina a Riesgos del trabajo ocasionl y hogar.
            if ($policy->work_modality_id != 3 && $policy->work_modality_id != 4) {

                if ($policy->option_asegurement != 1) {
                    $policy->semiannual_calculation_amount = $result['amountPolicySemestral'];
                } else {
                    $policy->semiannual_calculation_amount = 0;
                }
            }


            if ($policy->work_modality_id == 1 || $policy->work_modality_id == 2 || $policy->work_modality_id == 5) {
                $policy->semiannual_calculation_amount = $result['amountPolicySemestral'];
            }

            //Si la modalidad de aseguramiento es igual a riesgos del trabajo hogar
            if ($policy->work_modality_id == 3) {
                $values = [
                    2 => ['USD' => 128, 'CRC' => 65921],
                    3 => ['USD' => 188, 'CRC' => 96700]
                ];

                $currency = $policy->type_currency === 'USD' ? 'USD' : 'CRC';
                $policy->semiannual_calculation_amount = $values[$policy->option_asegurement][$currency] ?? 0;
            }

            //Si la modalidad de aseguramiento es igual a riesgos del trabajo ocasional
            if ($policy->work_modality_id == 4) {
                $policy->semiannual_calculation_amount = 0;
            }

            //Si la modalidad de aseguramiento es distina a Riesgos del trabajo ocasionl y hogar.
            if ($policy->work_modality_id != 3 && $policy->work_modality_id != 4) {
                $policy->quarterly_calculation_amount = $result['amountPolicyTrimestral'];
            } else {
                $policy->quarterly_calculation_amount = 0;
            }

            //Si la modalidad de aseguramiento es distina a Riesgos del trabajo ocasionl y hogar.
            if ($policy->work_modality_id != 3 && $policy->work_modality_id != 4) {
                $policy->monthly_calculation_amount = $result['amountPolicyMensual'];
            } else {
                $policy->monthly_calculation_amount = 0;
            }

            $policy->single_payment_value = $result['valorUnico'];
            $policy->save();
        }

        $signature_document_fisica = $activity->activity_actions->where('action_id', Action::REPORTAR_FIRMA_FISICA)
            ->first();

        $signature_document_digital = $activity->activity_actions->where('action_id', Action::REPORTAR_FIRMA_DIGITAL)
            ->first();

        return view('services.policy_sort.intermediary_policy.menu.signature', [
            'id' => $id,
            'activity' => $activity,
            'policy' => $policy,
            'signature_document_fisica' => $signature_document_fisica,
            'signature_document_digital' => $signature_document_digital
        ]);
    }

    /**
     * Método para carga las firmas desde S3
     *
     * @param Request $request
     * @param $cpath
     * @param $id
     * @return JsonResponse
     */
    public function saveSignature(Request $request, $cpath, $id)
    {
        // Obtener la imagen en formato base64 desde la solicitud
        $image_policyholder = $request->input('image_policyholder');
        $image_intermediary = $request->input('image_intermediary');

        try {

            // Actividad hijo (Policy)
            $policyActivity = Activity::where('id', $id)
                ->where('service_id', Service::SERVICE_POLICY_SORT_MNK)
                ->first();

            if (!$policyActivity) {
                return response()->json(['message' => 'No se encontró la actividad de la póliza.'], 404);
            }

            // Obtengo la PolicySort segun su actividad
            $policy = PolicySort::where('activity_id', $policyActivity->id)->first();

            if (!$policy) {
                return response()->json(['message' => 'No se encontró la póliza.'], 404);
            }

            // Procesar policyholder
            if ($image_policyholder) {
                $image = str_replace('data:image/png;base64,', '', $image_policyholder);
                $image = str_replace(' ', '+', $image);
                $imageData = base64_decode($image);

                $fileName = 'signature/policy_' . $id . '_policyholder.png';
                Storage::disk('s3')->put($fileName, $imageData);
                $policy->sign_policy_holder = $fileName;
            }

            // Procesar intermediary
            if ($image_intermediary) {
                $image = str_replace('data:image/png;base64,', '', $image_intermediary);
                $image = str_replace(' ', '+', $image);
                $imageData = base64_decode($image);

                $fileName = 'signature/policy_' . $id . '_intermediary.png';
                Storage::disk('s3')->put($fileName, $imageData);
                $policy->sign_intermediary = $fileName;
            }

            // Guardar los cambios en la póliza
            $policy->save();

            return response()->json([
                'message' => 'Firmas guardadas temporalmente.',
                'policyholder_url' => $policy->sign_policy_holder ?? null,
                'intermediary_url' => $policy->sign_intermediary ?? null,
            ]);
        } catch (\Exception $e) {

            // Si hay un error al guardar en S3, guardar en la sesión
            if ($image_policyholder) {
                Session::put('sign_policyholder', $image);
            } else {
                Session::put('sign_intermediary', $image);
            }

            return response()->json([
                'message' => 'No se logró guardar la firma.',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Método para obtener la firma guardada en S3
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getSignature(Request $request)
    {
        $activityId = $request->input('id');

        $result = DB::table('activities as ac_parent')
            // ->join('activities as ac_son', 'ac_son.parent_id', '=', 'ac_parent.id')
            ->join('policy_sorts as ps_son', 'ps_son.activity_id', '=', 'ac_parent.id')
            ->where('ac_parent.id', intVal($activityId))
            ->where('ac_parent.service_id', Service::SERVICE_POLICY_SORT_MNK)
            ->select(
                'ac_parent.id as id_act_quot',
                'ac_parent.service_id as service_id_quot',
                'ps_son.id as id_policy',
                'ps_son.activity_id as activity_id_policy',
                'ps_son.sign_policy_holder as sign_policy_holder',
                'ps_son.sign_intermediary as sign_intermediary'
            )
            ->first();


        if (!$result) {
            return response()->json(['message' => 'Activity de la Quotation no encontrada.'], 404);
        }

        // $activityDocument = ActivityAction::with('documents')
        //     ->where('activity_id', $activityId)
        //     ->where('action_id', Action::REPORTAR_FIRMA_DIGITAL)
        //     ->first();
        $activityDocument = ActivityDocument::where('activity_id', $activityId)
            ->where('document_id', ServiceDocument::SOLICITUD_EMISIÓN_SORT_FIRMA_DIGITAL)
            ->first();

        if (!$activityDocument) {
            $activityDocument = ActivityDocument::where('activity_id', $activityId)
                ->where('document_id', ServiceDocument::SOLICITUD_EMISIÓN_SORT_FIRMA_FISICA)
                ->first();
        }


        $sign_policyholder = $result->sign_policy_holder;
        $sign_intermediary = $result->sign_intermediary;

        if ($sign_policyholder || $sign_intermediary || isset($activityDocument->path)) {
            $sign_policyholder_url = $sign_policyholder ? Storage::disk('s3')->url($sign_policyholder) : null;
            $sign_intermediary_url = $sign_intermediary ? Storage::disk('s3')->url($sign_intermediary) : null;
            $document_url = isset($activityDocument->path) ? $activityDocument->path : null;

            return response()->json([
                'image_policy_holder' => $sign_policyholder_url,
                'image_intermediary' => $sign_intermediary_url,
                'document' => $document_url
            ]);
        }

        return response()->json(['message' => 'No se ha guardado ninguna firma.'], 200);
    }

    public function getDocumentSignature(Request $request)
    {
        $activityId = $request->input('id');

        $policy = Activity::with([
            'activity_documents' => function ($query) {
                $query->where('document_id', ServiceDocument::SOLICITUD_EMISIÓN_SORT_FIRMA_FISICA);
            }
        ])->where('id', $activityId)->first();

        if (!isset($policy)) {
            return response()->json(['message' => 'Activity de la Poliza no encontrada.'], 404);
        }


        if ($policy->activity_documents->isEmpty()) {
            return response()->json(['message' => 'Documento no Firmado.'], 200);
        }

        $sign_document = $policy->activity_documents[0]->path;

        if ($sign_document) {

            $document_url = $sign_document ? Storage::disk('s3')->url($sign_document) : null;

            return response()->json([
                'document' => $document_url
            ]);
        }

        return response()->json(['message' => 'No se ha guardado ningun documento.'], 404);
    }


    public function documentationView(Request $req, $cpath, $id)
    {
        //buscamos el cliente por el cpath
        $client = Client::where('path', $cpath)->firstOrFail();

        //buscamos la actividad de la póliza
        $activity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();

        //Cargamos los calendarios
        $calendarPeriods = AppServiceProvider::$CALENDAR_PERIOD;
        $typeincomes = AppServiceProvider::$TYPE_INCOME;

        //buscamos los documentos asociados al servicio en caso que existan
        $activityDocument = ActivityDocument::where('activity_id', $id)->get();
        $isEconomicActivitySpreadsheet = in_array($activity->policy_sort->activity_economic_id, AppServiceProvider::$ECONOMIC_ACTIVITES_WHITHOUT_SPREADSHEET);

        return view('services.policy_sort.intermediary_policy.menu.documentation', [
            'activity' => $activity,
            'document1' => $activityDocument->where('document_id', 1)->first(),
            'document2' => $activityDocument->where('document_id', 2)->first(),
            'document3' => $activityDocument->where('document_id', 3)->first(),
            'document4' => $activityDocument->where('document_id', 4)->first(),
            'document5' => $activityDocument->where('document_id', 9)->first(),
            'calendarPeriods' => $calendarPeriods,
            'typeincomes' => $typeincomes,
            'id' => $id,
            'isEconomicActivitySpreadsheet' => $isEconomicActivitySpreadsheet
        ]);
    }

    //Vista de reportar planillas del intermediario
    // $id es la actividad de la policy luego de Emitir Poliza
    public function viewSpreadsheet(Request $req, $cpath, $id)
    {

        $client = Client::where('path', $cpath)->firstOrFail();


        $activity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();

        $activityQuotation = Quotation::where('activity_id', $activity->parent_id)->first();


        $activity_spread = Activity::with(['policy_spreadsheets.policy_spreadsheet_affiliate'])
            ->where('parent_id', $activity->id)
            ->first();

        //Verificamos si la ultima accion fue sin planilla o con planilla
        $existActionWithNotReport = $activity->activity_actions->whereIn('action_id', [ActionPolizaSort::REPORTAR_DOCUMENTOS_EXTERNOS_SIN_PLANILLAS, ActionPolizaSort::REPORTAR_DOCUMENTOS_EXTERNOS])
            ->sortBy('id')
            ->last();
        if ($existActionWithNotReport->action_id == ActionPolizaSort::REPORTAR_DOCUMENTOS_EXTERNOS) {
            $existActionWithNotReport = false;
        }

        return view('services.policy_sort.intermediary_policy.menu.spreadsheet', [
            'activity' => $activity,
            'policy_sort' => $activity->policy_sort,
            'policy_spreadsheet' => $activity_spread->policy_spreadsheets ?? [],
            'total_salaries' => isset($activity_spread) && isset($activity_spread->policy_spreadsheets->total_salaries)
                ? (is_numeric($activity_spread->policy_spreadsheets->total_salaries)
                    ? number_format($activity_spread->policy_spreadsheets->total_salaries, 2, ',', '.')
                    : $activity_spread->policy_spreadsheets->total_salaries)
                : null,
            'id' => $id,
            'existActionWithNotReport' => $existActionWithNotReport
        ]);
    }


    public function downloadPlanilla(Request $req, $cpath)
    {


        $filePath = public_path('files/PLANTILLA_SORT.xlsx');

        // Verifica si el archivo existe
        if (!file_exists($filePath)) {
            abort(404, 'Archivo no encontrado');
        }

        // Retorna el archivo como descarga
        return Response::download($filePath, 'PLANTILLA_SORT.xlsx');
    }

    public function downloadExplanation(Request $req, $cpath)
    {


        $filePath = public_path('files/Explicación_Planilla_MNK.xlsx');

        // Verifica si el archivo existe
        if (!file_exists($filePath)) {
            abort(404, 'Archivo no encontrado');
        }

        // Retorna el archivo como descarga
        return Response::download($filePath, 'Explicación_Planilla_MNK.xlsx');
    }


    /**
     * Acción SOLICITAR EMISIÓN - FIRMA DIGITAL
     *
     * @param Request $req
     * @param int $id
     * @return JsonResponse
     */
    public function requestIssuanceDigitalSignature(Request $req, $cpath, $id)
    {
        DB::beginTransaction();
        try {
            $client = Client::where('path', $cpath)->firstOrFail();
            $activity = Activity::where('client_id', $client->id)->where('id', $req->id)->firstOrFail();
            $affiliate = Affiliate::where('id', $activity->affiliate_id)->first();
            $policySort = PolicySort::where('activity_id', $activity->id)->first();

            $policySort->signed_document = true;
            $policySort->save();

            /*
             * Bloque carga de imagen
             * */
            $urls = []; // Arreglo para almacenar las URLs de las imágenes subidas

            if ($req->filled('sign1') && $req->sign1 !== "false" && $req->input('sign1') !== '') {
                $image_request = $req->input('sign1'); // base64 string
                $image_decode = base64_decode(preg_replace('#^data:image/\w+;base64,#i', '', $image_request));
                $path1 = "policy_sort/sign1_" . $id . ".png";
                Storage::disk('s3')->put($path1, $image_decode);
                $urls['sign1_url'] = Storage::disk('s3')->url($path1); // Obtener la URL de sign1
            } else {
                $urls['sign1_url'] = Storage::disk('s3')->url($policySort->sign_policy_holder);
            }

            if ($req->filled('sign_2') && $req->sign_2 !== "false" && $req->input('sign_2') !== '') {
                $image_request_2 = $req->input('sign_2'); // base64 string
                $image_decode_2 = base64_decode(preg_replace('#^data:image/\w+;base64,#i', '', $image_request_2));
                $path2 = "policy_sort/sign2_" . $id . ".png";
                Storage::disk('s3')->put($path2, $image_decode_2);
                $urls['sign2_url'] = Storage::disk('s3')->url($path2); // Obtener la URL de sign_2
            } else {
                $urls['sign2_url'] = Storage::disk('s3')->url($policySort->sign_intermediary); // Obtener la URL de sign1
            }


            /*
             * fin carga de imagen
             * */
            $document = 'request_issuance_digital_signature';
            // $activity->save();

            //Verificamos si ya se ejecuto la acción SOLICITAR_EMISION_FIRMA_DIGITAL
            $existAction = false;

            $activityAction = "";

            if ($existAction) {
                //Ejecutamos la acción REPORTAR SEGUIMIENTO
                $description = 'Se reporto seguimiento sobre la acción solicitar emisión firma digital';
                $activityAction = ActionController::create($activity->id, Action::REPORTAR_SEGUIMIENTO, $description);
            } else {
                //Ejecutamos la acción SOLICITAR_EMISION_FIRMA_DIGITAL
                $description = 'Pendiente documentos externos';
                $activityAction = ActionController::create($activity->id, Action::REPORTAR_FIRMA_DIGITAL, $description);
            }


            $ubicacion = $this->getLocationNamesFromJson($affiliate->province, $affiliate->canton, $affiliate->district);

            //obtenemos la actividad economica
            $jsonSource = ($activity->policy_sort->economic_activity == 'public') ? '/js/economic_activity/public.json' : '/js/economic_activity/private.json';
            $json = file_get_contents(public_path($jsonSource));
            $economicActivities = json_decode($json, true);
            $activity_economic_name = collect($economicActivities)->firstWhere('CODE', $activity->policy_sort->activity_economic_id)['ACTIVITY_NAME'];
            $activity->policy_sort->economic_activity_name = $activity_economic_name;

            $isplanilla = $activity->activity_actions->where('action_id', ActionPolizaSort::REPORTAR_DOCUMENTOS_EXTERNOS_SIN_PLANILLAS)
                ->first();



            $totalSalarie = $this->totalSalariesPlanilla($policySort);
            /*
             * Documento PDF
             * */


            $data = [
                'name' => $affiliate->first_name,
                'idNumber' => $affiliate->doc_number,
                'email' => $affiliate->email,
                'ocupacion' => $affiliate->occupation,
                'invoiceEmail' => $affiliate->electronic_billing_email,
                'province' => $ubicacion['province'],
                'canton' => $ubicacion['canton'],
                'district' => $ubicacion['district'],
                'sign1_url' => $urls['sign1_url'],
                'sign2_url' => $urls['sign2_url'],
                'economicActivity' => $policySort->activity_economic_id,
                'legalRepresentativeName' => $policySort->legal_representative_name,
                'legalRepresentativeId' => $policySort->legal_representative_id,
                'puesto_tomador' => $affiliate->occupation_responsible,
                'name_tomador' => $affiliate->first_name,
                'employer_address' => $affiliate->employer_address,
                'phone' => $affiliate->phone,
                'cellphone' => $affiliate->cellphone,
                'economic_activity_name' => $activity->policy_sort->economic_activity_name,
                'validity_from' => date('d/m/y', strtotime($policySort->validity_from)),
                'validity_to' => date('d/m/y', strtotime($policySort->validity_to)),
                'isplanilla' => $isplanilla ? true : false,
                'calendar' => $activity->policy_sort->calendar_period ?? '',
                'anual' => $policySort->annual_calculation_amount,
                'type_currency' => $policySort->type_currency,
                'salary_projection' => $policySort->salary_projection,
                'total_salaries' => $totalSalarie,
                'temporality' => $policySort->temporality,
                'single_payment_value' => $policySort->single_payment_value,
            ];


            // Cargar la vista del PDF y pasarle los datos
            $document = 'download_signature_mnk';
            $pdf = PDF::loadView("services.policy_sort.docs.{$document}", [
                'data' => $data,  // Reemplazamos los datos generados anteriormente
                'watermark' => false,
            ]);


            $filePath = "activity_action_document/{$document}_{$activityAction->id}.pdf";

            Storage::disk('s3')->put($filePath, $pdf->output());

            $fileUrl = Storage::disk('s3')->url($filePath);


            /*
             * Fin PDF
             * */

            $activityDocument = new ActivityDocument;
            $activityDocument->activity_id = $activity->id;
            $activityDocument->document_id = ServiceDocument::SOLICITUD_EMISIÓN_SORT_FIRMA_DIGITAL;
            $activityDocument->path = $filePath;
            $activityDocument->save();

            // $activityActionDocument = new ActivityActionDocument();
            // $activityActionDocument->activity_action_id = $activityAction->id;
            // $activityActionDocument->name = "Firma digital_" . $activityAction->id;
            // $activityActionDocument->path = $filePath;
            // $activityActionDocument->save();


            /*
             * Envio de correo al tomador
             * */
            // Agrega el email principal de notificaciones y todos los adicionales
            $notiEmails = $this->getAdditionalNotificationEmails($policySort->id);
            $emails = implode(',', $notiEmails);


            $files = [
                [
                    'path' => $filePath,
                    'name' => 'Solicitud_de_emisión_seguro_obligatorio_de_riesgos_del_trabajo.pdf',
                    'type' => 'PDF'
                ]
            ];

            $this->resendEmailDigital($req, $activity, Service::SERVICE_POLICY_SORT, $activityAction, $emails, $client->id, $files);
            /*
             * Fin de envio de correo
             * */

            DB::commit();
            return response()->json([
                'message' => 'Documento generado y enviado correctamente.',
                'fileUrl' => $fileUrl,
                'policy' => $policySort->signed_document
            ], 200);
        } catch (Exception $e) {
            DB::rollback();
            return response()->json([
                'message' => 'Errores durante el procesamiento del cargue manual',
                'e' => $e->getMessage(),
            ], 500);
        }
    }

    public function getLocationNamesFromJson($provinceCode, $cantonCode = null, $districtCode = null)
    {
        // Ruta al archivo JSON en la carpeta public
        $path = public_path('js/costarica.json');


        // Cargar y decodificar el archivo JSON
        if (!file_exists($path)) {
            return response()->json(['error' => 'Archivo JSON no encontrado'], 404);
        }


        $jsonData = file_get_contents($path);


        $provinces = json_decode($jsonData, true);

        $location = [
            'province' => null,
            'canton' => null,
            'district' => null
        ];


        foreach ($provinces['province'] as $province) {
            if ($province['code'] == $provinceCode) {
                $location['province'] = ucwords(strtolower($province['name']));


                if (is_null($cantonCode) && is_null($districtCode)) {
                    return $location;
                }


                foreach ($province['cantons'] as $canton) {
                    if ($canton['code'] == $cantonCode) {
                        $location['canton'] = ucwords(strtolower($canton['name']));


                        if (is_null($districtCode)) {
                            return $location;
                        }


                        foreach ($canton['districts'] as $district) {
                            if ($district['code'] == $districtCode) {
                                $location['district'] = ucwords(strtolower($district['name']));
                                return $location;
                            }
                        }
                    }
                }
            }
        }
        // Si no se encuentra alguna de las ubicaciones, retornar null
        return $location;
    }


    /**
     * Acción SOLICITAR EMISIÓN - FIRMA Fisica
     *
     * @param Request $req
     * @param int $id
     * @return JsonResponse
     */
    public function requestIssuancePhysicalSignature(Request $req, $cpath, $id)
    {
        DB::beginTransaction();
        try {
            $client = Client::where('path', $cpath)->firstOrFail();
            $activity = Activity::where('client_id', $client->id)->where('id', $req->id)->firstOrFail();
            $policySort = PolicySort::where('activity_id', $activity->id)->first();

            $policySort->signed_document = true;
            $policySort->save();

            //Verificamos si ya se ejecuto la acción SOLICITAR_EMISION_FIRMA_FISICA
            $existAction = $activity->activity_actions->where('action_id', Action::REPORTAR_FIRMA_FISICA)
                ->first();

            $activityAction = "";

            if ($existAction) {
                //Ejecutamos la acción REPORTAR SEGUIMIENTO
                $description = 'Se reporto seguimiento sobre la acción solicitar emisión firma fisica';
                $activityAction = ActionController::create($activity->id, Action::REPORTAR_SEGUIMIENTO, $description);
            } else {
                //Ejecutamos la acción SOLICITAR_EMISION_FIRMA_FISICA
                $description = 'Pendiente documentos externos';
                $activityAction = ActionController::create($activity->id, Action::REPORTAR_FIRMA_FISICA, $description);
            }


            $req->validate([
                'pdfSignature' => 'required|file|mimes:pdf', // asegurarse de que es un PDF
            ]);


            // Guardar el archivo en Amazon S3
            if ($req->hasFile('pdfSignature')) {
                $file = $req->file('pdfSignature');
                $fileName = "activity_action_document/firma_fisica_{$activityAction->id}.pdf";


                // Subir el archivo a S3
                $path = Storage::disk('s3')->put($fileName, file_get_contents($file));
                $fileUrl = Storage::disk('s3')->url($fileName);

                if ($path) {
                    // Si el archivo se subió correctamente, puedes almacenar el URL o hacer cualquier otra acción

                    // $activityActionDocument = new ActivityActionDocument();
                    // $activityActionDocument->activity_action_id = $activityAction->id;
                    // $activityActionDocument->name = "Firma fisica_" . $activityAction->id;
                    // $activityActionDocument->path = $fileName;
                    // $activityActionDocument->save();

                    $activityDocument = new ActivityDocument;
                    $activityDocument->activity_id = $activity->id;
                    $activityDocument->document_id = ServiceDocument::SOLICITUD_EMISIÓN_SORT_FIRMA_FISICA;
                    $activityDocument->path = $fileName;
                    $activityDocument->save();
                } else {
                    return response()->json(['message' => 'Error al subir el archivo'], 500);
                }
            }


            DB::commit();
            return response()->json([
                'message' => 'Documento generado y enviado correctamente.',
                'fileUrl' => $fileUrl,
                'policy' => $policySort->signed_document
            ]);
        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'message' => 'Errores durante el procesamiento del cargue manual',
                'e' => $e->getMessage(),
            ], 500);
        }
    }

    public function sendEmailDemo(Request $req, $cpath, $email)
    {

        try {
            $client = Client::where('path', $cpath)->firstOrFail();


            $subject = 'Documentos de Póliza'; // Asunto del correo
            $text = 'Adjuntamos los documentos solicitados para su póliza.';

            $document = 'demo';

            $pdf = PDF::loadView("services.plantilla.docs.demo_pdf");

            Storage::disk('s3')
                ->put("activity_action_document/{$document}.pdf", $pdf->output());


            $activityActionDocument = new ActivityActionDocument();
            $activityActionDocument->activity_action_id = 1;
            $activityActionDocument->name = $document;
            $activityActionDocument->path = "activity_action_document/{$document}.pdf";
            $activityActionDocument->save();

            //formamos archivo
            $files[] = [
                'type' => 'pdf',
                'path' => "activity_action_document/{$document}.pdf",
                'name' => $document . '.pdf',
            ];


            // Enviar el correo con los documentos adjuntos
            $mailSent = new SendDocumentDataBase(
                $email,
                $subject,
                config('mail.from.address'), // Email de remitente
                config('mail.from.name'),
                [
                    "text" => $text,
                    "sender" => 'Sistema de Seguros'
                ],
                config('mail.from.address'), // Responder a este email
                $files, // Adjuntos (PDFs generados y los de public)
                "send_document_db",
                $client,
                $req->getHost(),
                $activityActionDocument->id,
                Action::REPORT_NOTIFY,
                Service::SERVICE_POLICY_SORT_MNK
            );
            $mailSent->sendMail();
        } catch (\Exception $e) {
            DB::rollback();
        }

        return response()->json(200);
    }

    public function handleEmail(Request $req, $cpath, $id, $type)
    {
        $policy_sort = PolicySort::where('id', $id)->firstOrFail();
        $client = Client::where('path', $cpath)->firstOrFail();
        $affiliate = Affiliate::where('id', $policy_sort->activity->affiliate_id)->first();

        DB::beginTransaction();
        try {

            $activityType = ($type === 'email') ? 'GENERAR CERTIFICACIÓN DE POLIZA AL DIA' : 'GENERAR ESTADO DE CUENTA DEL TRABAJADOR';
            $emailSubject = ($type === 'email') ? "Certificación de Póliza" : "Certificación constancia sumas pendientes por pagar";
            $document_id = ($type === 'email') ? "221" : "222";

            $activityAction = $this->registerActivityAction(
                $id,
                Action::GENERAR_ESTADO_CUENTA_TRABAJADOR,
                State::REGISTRADO,
                State::CONSTANCIA_GENERADA,
                $activityType,
                $policy_sort->activity->user_id,
                Auth::user()->id
            );

            $constancy = new ConstancySort();
            $constancy->activity_id = $id;
            $constancy->save();

            // Generar el PDF con la información de la constancia
            $pdf = PDF::loadView('services.constancy_sort.docs.constancy_sort_pdf', [
                'activity' => $constancy,
                'watermark' => false,
            ]);

            $document = 'constancy_sort_pdf';
            $filePath = "documents/{$document}_{$constancy->id}.pdf";
            $path = Storage::disk('s3')->put($filePath, $pdf->output());

            $activityDocument = ActivityDocument::create([
                'activity_id' => $activityAction->id,
                'document_id' => $document_id,
                'file' => $path,
                'uploaded_at' => new DateTime()
            ]);

            $activityActionDocument = new ActivityActionDocument();
            $activityActionDocument->activity_action_id = $activityAction->id;
            $activityActionDocument->name = $document;
            $activityActionDocument->path = $filePath;
            $activityActionDocument->save();


            $emailIntermediary = $policy_sort->email;
            $emailTaker = $affiliate->email;

            $emails = array_filter([$emailIntermediary, $emailTaker], function ($email) {
                return !empty($email);
            });


            $text = [
                "text" => "Estimado cliente, adjunto encontrará el " . strtolower($emailSubject),
                "sender" => 'MNK Seguros'
            ];
            $attachments = [
                [
                    'path' => $filePath,
                    'name' => basename($filePath),
                    'type' => 'PDF'
                ]
            ];

            $this->sendSuspensionEmail($emails, $emailSubject, $text, $attachments, $client, $id, $activityAction, $policy_sort);

            DB::commit();
        } catch (Exception $e) {
            DB::rollback();
            return response()->json([
                'status' => 'error',
                'message' => 'Ocurrió un error al enviar el correo.'
            ], 500);
        }

        return response()->json([
            'status' => 'success',
            'message' => 'Registro creado y email enviado exitosamente'
        ]);
    }

    public function suspensionPayment($activity_collection, $dueDate)
    {
        $activity = Activity::where('id', $activity_collection->parent_id)->firstOrFail();
        $policySort = PolicySort::where('activity_id', $activity->id)->first();

        $emailIntermediary = $policySort->email;
        $emailTaker = $activity->affiliate->email;

        $emails = array_filter([$emailIntermediary, $emailTaker], function ($email) {
            return !empty($email);
        });


        DB::beginTransaction();
        try {
            $activityAction = ActionController::create(
                $activity->id,
                Action::REPORTAR_SUSPENSION_POR_NO_PAGO_ABONO,
                'Reporte de suspensión por no pago abono'
            );

            $pdf = PDF::loadView('services.policy_sort.docs.suspension_payment_pdf', [
                'data' => '',
                'watermark' => false,
            ]);

            $document = 'suspension_payment_pdf';
            $filePath = "documents/{$document}_{$policySort->id}.pdf";
            $path = Storage::disk('s3')->put($filePath, $pdf->output());

            $activityDocument = ActivityDocument::create([
                'activity_id' => $policySort->activity_id,
                'document_id' => 220,
                'file' => $path,
                'uploaded_at' => new DateTime()
            ]);

            $activityActionDocument = new ActivityActionDocument();
            $activityActionDocument->activity_action_id = $activityAction->id;
            $activityActionDocument->name = $document;
            $activityActionDocument->path = $filePath;
            $activityActionDocument->save();


            $validityToFormat =isset($activity_collection->policy_sort_collection->validity_to) ? Carbon::parse($activity_collection->policy_sort_collection->validity_to)->formatLocalized('%e de %B del %Y') : '';
            $validityFromFormat =isset($activity_collection->policy_sort_collection->validity_from) ? Carbon::parse($activity_collection->policy_sort_collection->validity_from)->formatLocalized('%e de %B del %Y') : '';
            $dueDateFormat = Carbon::parse($dueDate)->addDay()->formatLocalized('%e de %B del %Y');

            $emailData = TemplateBuilder::build(
                Templates::EMAIL_OF_NON_PAYMENT_OF_INSTALLMENT,
                [
                    'policy_sort' => $policySort->formatNumberConsecutive(),
                    'name_taker' =>  mb_convert_case(mb_strtolower($activity->affiliate->full_name ?? '', "UTF-8"), MB_CASE_TITLE, 'UTF-8'),
                    'start_period' => $validityFromFormat,
                    'end_period' => $validityToFormat,
                    'not_in_force' => $dueDateFormat,
                ]
            );

            $text = [
                "text" => $emailData['body'],
                "sender" => $emailData['sender']
            ];

            $attachments = [];

            //            $attachments = [
            //                [
            //                    'path' => $filePath,
            //                    'name' => basename($filePath),
            //                    'type' => 'PDF'
            //                ]
            //            ];

            $this->sendSuspensionEmail($emails, $emailData['subject'], $text, $attachments, $activity->client_id, $activity_collection->parent_id, $activityAction, $policySort);

            DB::commit();
        } catch (Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    /**
     * !!! FUNCION PARA EL ENVIO DE CORREO !!!
     */
    private function sendSuspensionEmail($emails, $subject, $text, $attachments, $client, $id, $activityAction, $policySort)
    {
        $mailSent = new SendDocumentDataBase(
            implode(',', $emails),         // Correos a enviar
            $subject,                      // Asunto del correo
            "<EMAIL>",            // Remitente
            $subject,                      // Asunto
            $text,                         // Cuerpo del email
            "<EMAIL>",  // Email de respuesta
            $attachments,                  // Archivos adjuntos
            "send_document_db",            // Tipo de envío
            $client,                       // Información del cliente
            request()->getHost(),          // Dominio
            $id,                           // ID de la actividad
            $activityAction->id,           // ID de la acción de la actividad
            $policySort->activity->service->id // ID del servicio
        );

        // Capturar el resultado del envío
        $result = $mailSent->sendMail();

        //Registramos los datos del correo enviado para la trazabilidad
        $mailBoardController = new MailBoardController();
        $mailBoardController->createRegisterMail(
            $policySort->activity->id,
            $policySort->activity->service->id,
            $policySort->consecutive,
            'Tomador',
            mb_convert_case(mb_strtolower($policySort->activity->affiliate->full_name ?? '', "UTF-8"), MB_CASE_TITLE, 'UTF-8'),
            $policySort->activity->affiliate->doc_number,
            $subject,
            $text,
            $emails,
            $result,
            $attachments
        );
    }


    /**
     * @param $activity_id
     * @return JsonResponse
     * Función que permite hacer generar el certificado de planilla  (automatica)
     * Se corre cada día a media noche
     */
    public function generateCertificatePlanilla(Request $request, $cpath)
    {
        try {
            DB::beginTransaction();

            // Obtener actividades que cumplan las condiciones
            $activityAffiliate = Activity::where('service_id', Service::SERVICE_REPORT_TAKEN_FORM_MNK)
                ->where('state_id', StateReportePlanillaTomador::PLANILLA_REPORTADA)
                ->get();

            // Validar si hay actividades
            if ($activityAffiliate->isEmpty()) {
                return response()->json(['success' => false, 'message' => 'No se encontraron actividades para procesar'], 404);
            }

            $client = Client::where('path', $cpath)->firstOrFail();
            $host = $request->getHost();

            // Procesar cada actividad
            foreach ($activityAffiliate as $activity) {
                if ($activity->parent_id) {
                    $this->generateCertificate($client, $activity->parent_id, $host);
                }
            }

            DB::commit();

            return response()->json(['success' => true, 'message' => 'Certificados generados correctamente'], 200);
        } catch (Exception $e) {
            DB::rollback();

            return response()->json(['success' => false, 'message' => 'Error al procesar certificados', 'error' => $e->getMessage()], 500);
        }
    }

    /**
     * @param $activity_id
     * @return void
     * Función que permite hacer envío de planilla definitiva (automatica)
     * Se corre cada día a media noche
     */
    public function reportFinalPayrollIntermediario($activity_id)
    {

        try {
            $integrationServiceController = new IntegrationServiceController();
            $url = config('app.api_url') . '/planilla_definitiva/envio';
            $method = 'POST';

            $data = [
                'activity_id' => $activity_id
            ];

            $result = $integrationServiceController->requestRenAppApi($url, $method, $data);
            $data = json_decode($result->getContent(), true);

            if (isset($data['success']) && $data['success']) {
                return;
            }
        } catch (\Exception $e) {
            throw new Exception("Error al crear el certificado de planilla: {$e->getMessage()}");
        }
    }

    /**
     * 
     * @return JsonResponse
     * Función que permite hacer envío de planilla definitiva (automatica)
     * Se corre cada día a media noche
     */
    public function reportFinalPayroll(Request $request, $cpath)
    {

        try {
            register_shutdown_function(function () {
                $integrationServiceController = new IntegrationServiceController();

                $url = config('app.api_url') . '/planilla_definitiva/envio';

                $method = 'POST';

                $result = $integrationServiceController->requestRenAppApi($url, $method);
                $data = json_decode($result->getContent(), true);
            });
        } catch (\Exception $e) {
            return response()->json(['error' => 'No se pudo enviar el correo', 'details' => $e->getMessage()], 500);
        }

        return response()->json(['success' => true], 200);
        /*
        try {
            $client = Client::where('path', $cpath)->first();
            if (!$client) {
                return response()->json(['error' => 'Cliente no encontrado'], 404);
            }

            // Buscar todas las actividades con el estado y acción que se requiere
            $activities = ActivityAction::with(['activity', 'activity.user']) // Cargar la relación con Activity y User
                ->where('action_id', Action::REPORTAR_PLANILLA_TOMADOR) // Acción 84
                ->whereHas('activity', function ($query) {
                    $query->whereDate('created_at', Carbon::today()); // Creada hoy
                })->get();

            foreach ($activities as $activityAction) {
                $activity = $activityAction->activity; // Obtener la actividad
                if ($activity && $activity->parent) {
                    // Comprobar si el estado es 20
                    if ($activity->parent->state_id == StatePoliza::POLIZA_EMITIDA_ACTIVA) {
                        $affiliate = Affiliate::where('id', $activity->affiliate_id)->first();
                        if (!$affiliate) {
                            return response()->json(['error' => 'Afiliado no encontrado'], 404);
                        }

                        $userEmail = $activity->user->email ?? null; // Obtener el correo del usuario (si existe)

                        $activity_service = $activity->service_id;
                        $description = "REPORTAR PLANILLA DEFINITIVA";

                        // Registrar la acción GENERAR CERTIFICADO
                        $activityAction = ActionController::create(
                            $activity->parent->id,
                            Action::REPORTAR_PLANILLA_DEFINITIVA,
                            $description
                        );

                        $activityActionId = $activityAction->id;

                        // Generar documento "Pago por reconocimiento de gastos"
                        $documentName = "certificado_afiliado_" . $activityActionId . ".pdf";


                        $data = PolicySpreadsheet::with(['policy_spreadsheet_affiliate' => function ($query) {
                            $query->where('temporal', 0);
                        }])
                            ->where('activity_id', $activity->id)
                            ->orderBy('id', 'desc')
                            ->first();

                        $policy = Activity::where('id', $data->activity_id)->first();

                        $pdfContent = PDF::loadView(
                            'services.policy_sort.docs.planilla_dinamic',
                            [
                                'watermark' => false,
                                'data' => $data,
                                'policy' => $policy
                            ]
                        )->setPaper('a4', 'landscape')->output();

                        // Subir el archivo PDF a S3
                        $filePath = "activity_action_document/{$documentName}";
                        Storage::disk('s3')->put($filePath, $pdfContent);

                        $files = [
                            [
                                'type' => 'pdf',
                                'path' => $filePath,
                                'name' => $documentName,
                            ]
                        ];

                        //

                        $subject = "Certificado de recepción de planilla póliza " . $activity->parent->policy_sort->formatNumberConsecutive();

                        $text = " ¡Buen día, " . ucwords(mb_strtolower($activity->parent->affiliate->full_name)) . "!
                                   Se ha generado el certificado de recepción de planilla para la póliza " . $activity->parent->policy_sort->formatNumberConsecutive();

                        $client_id = $client->id;

                        // Lista de correos a enviar
                        $emailsToSend = [$affiliate->email]; // Inicia la lista con el correo del afiliado
                        if ($userEmail) {
                            $emailsToSend[] = $userEmail; // Agrega el correo del usuario si existe
                        }

                        // Crear y enviar correos a cada destinatario
                        foreach ($emailsToSend as $email) {
                            // Crear una instancia de envío de correo
                            $mailSent = new SendDocumentDataBase(
                                $email,
                                $subject,
                                "<EMAIL>",
                                "Certificado generado",
                                [
                                    "text" => $text,
                                    "sender" => 'MNK Seguros'
                                ],
                                "<EMAIL>",
                                $files,
                                "send_document_db",
                                $client_id,
                                $request->getHost(),
                                $activity->id,
                                $activityAction->id,
                                $activity->service_id
                            );

                            // Enviar el correo
                            $mailSent->sendMail();
                        }
                    }
                }
            }

            return response()->json(['message' => 'Correos enviados correctamente'], 200);
        } catch (\Exception $e) {
            // Manejar errores
            return response()->json(['error' => 'No se pudo enviar el correo', 'details' => $e->getMessage()], 500);
        }

        */
    }


    /**
     * Reportar Solicitud variacion MS-217
     * De POLIZA EMITIDA - ACTIVA al estado POLIZA EMITIDA - ACTIVA
     *
     * @param $activityVariation
     * @return void
     */
    public function actionReportVariationRequest($activityVariation, $fromVariationAction)
    {
        $description = "Se generó desde la acción $fromVariationAction.";
        $this->registerActivityActionReturn(
            $activityVariation->id,
            ActionPolizaSort::REPORTAR_SOLICITUD_VARIACION,
            StatePoliza::POLIZA_EMITIDA_ACTIVA,
            StatePoliza::POLIZA_EMITIDA_ACTIVA,
            $description,
            $activityVariation->user_id
        );
    }

    /**
     * @param Request $request
     * @param $cpath
     * @return JsonResponse
     * Función de envío de planilla automatica, solo se debe enviar el día 10 habil de cada mes
     */
    public function reportFinalMonthlyPayroll(Request $request, $cpath)
    {
        $id = uniqid();
        if (\Cache::has('report_final_monthly_payroll')) {

            return response()->json([
                'status' => 200,
                'message' => 'Ya hay una validación en progreso',
            ], 200);

        }
        \Cache::put('report_final_monthly_payroll', $id, now()->addMinutes(3));

        try {
            // Obtener el día actual
            $currentDate = now();
            $startOfMonth = $currentDate->copy()->startOfMonth();
            $endOfMonth = $currentDate->copy()->endOfMonth();
            if ($request->get('testDate')) {
                $currentDate = Carbon::parse($request->get('testDate'));
            }

            // Verificar si es el día 10 hábil del mes antes de continuar
            if (!$this->isNthBusinessDayOfMonth($currentDate, 10) && !$request->get('skipValidation')) {
                return response()->json(['message' => 'No es el día 10 hábil del mes'], 400);
            }

            // Buscar cliente por cpath
            $client = Client::where('path', $cpath)->first();

            if (!$client) {
                return response()->json(['error' => 'Cliente no encontrado'], 404);
            }

            $policyActivities = Activity::where('service_id', Service::SERVICE_POLICY_SORT_MNK)
                ->where('state_id', StatePoliza::POLIZA_EMITIDA_ACTIVA)
                ->whereHas('policy_sort', function ($query) use ($startOfMonth) {
                    $query->where('validity_from', '<', $startOfMonth)
                        ->whereNotIn('work_modality_id', [4, 6]) // 4 = 'RT ocaional', 6 = 'RT idependiente'
                        ->where('calendar_period', '=', '1');
                })
                ->whereDoesntHave('children', function ($query) use ($startOfMonth, $endOfMonth) {
                    $query->where('service_id', Service::SERVICE_REPORT_TAKEN_FORM_MNK)
                        ->whereBetween('created_at', [$startOfMonth, $endOfMonth]);
                })
                ->with([
                    'policy_sort',
                    'affiliate',
                    'children' => function ($query) {
                        $query->where('service_id', Service::SERVICE_REPORT_TAKEN_FORM_MNK)
                            ->latest()
                            ->limit(1)
                            ->with([
                                'policy_spreadsheets' => function ($query) {
                                    $query->with([
                                        'policy_spreadsheet_affiliate'=> function ($q) {
                                            $q->limit(2);
                                        }
                                    ]);
                                }
                            ]);
                    }
                ])
                ->get();
            //asignar locale
            setlocale(LC_TIME, 'es_ES.UTF-8');
            // Iterar sobre todas las actividades que no tienen planilla definitiva
            $count = 0;
            $errors = 0;

            foreach ($policyActivities as $activity) {
                try {
                    //Buscamos la actividad de la ultima planilla reportada
                    $activitySpreadSheet = Activity::where('service_id', Service::SERVICE_REPORT_TAKEN_FORM_MNK)
                        ->where('parent_id', $activity->id)
                        ->latest()
                        ->first();
                    DB::beginTransaction();
                    if ($activitySpreadSheet) {
                        //buscamos la planilla
                        $spreadsheetLastReport = PolicySpreadsheet::where('activity_id', $activitySpreadSheet->id)->first();
                        //buscamos los afiliados de la planilla
                        //Buscamos todos los afiliados de la planilla
                        $affiliates = PolicySpreadsheetAffiliate::where('policy_spreadsheet_id', $spreadsheetLastReport->id)
                            ->where('benefit_colective', null)->count();
                         
                        //duplicamos la planilla con sus afiliados
                        $this->duplicateSpreadsheetData($activity, $spreadsheetLastReport,'Sustituida');
                        //Ajustes afiliados con beneficio colectividad se notifican y se actulizan campo de observacion colectividad
                        $ReportTakenFormController = new ReportTakenFormController();
                        $ReportTakenFormController->notifyAndUpdateAffiliateCollectivity($activity, $spreadsheetLastReport);

                        //Asignación de mes y año correspondientes
                        $lastMonth = Carbon::now()->subMonth();
                        $year = $lastMonth->year;
                        $monthName = Str::ucfirst($lastMonth->formatLocalized('%B'));
                    } else {

                        // Crear la actividad de reporte de planilla
                        $activityReport = new Activity();
                        $activityReport->service_id = Service::SERVICE_REPORT_TAKEN_FORM_MNK;
                        $activityReport->state_id = StateReportePlanillaTomador::REGISTRADO;
                        $activityReport->parent_id = $activity->id;
                        $activityReport->affiliate_id = $activity->affiliate_id;
                        $activityReport->user_id = $activity->user_id;
                        $activityReport->client_id = $activity->client->id;
                        $activityReport->save();

                        // Crear la acción de reporte de planilla
                        $actionReport = ActionController::create(
                            $activityReport->id,
                            Action::REPORTAR_PLANILLA_TOMADOR,
                            'REPORTAR PLANILLA TOMADOR CREADA POR EL SISTEMA'
                        );
                        //crear la accion generar certificado de tomador, como es emision no se genera pdf
                        ActionController::create($activityReport->id, Action::GENERAR_CERTIFICADO_TOMADOR, 'Reportar certificado tomador creado por el sistema');

                        //crear la planilla en la tabla spreadsheet con el valor de la proyeccion de salarios
                        $spreadsheet = new PolicySpreadsheet();
                        $spreadsheet->activity_id = $activityReport->id;
                        $spreadsheet->total_salaries = $activity->policy_sort->salary_projection;
                        $spreadsheet->created_at = now();
                        $spreadsheet->total_affiliates = 0;
                        $spreadsheet->observacion = "Planilla de emisión creada por el sistema basada en la proyección de salarios";
                        $spreadsheet->entry_type = 'Sustituida';
                        $spreadsheet->save();
                        //planilla de emision
                        $monthName = "Emisión";
                        $lastMonth = Carbon::now()->subMonth();
                        $year = $lastMonth->year;
                    }

                    //verificar si el tomador del seguro es la unica persona asegurada en la ultima planilla
                    $lastSpreadsheetActivity = Activity::where('service_id', Service::SERVICE_REPORT_TAKEN_FORM_MNK)
                        ->where('parent_id', $activity->id)
                        ->latest()
                        ->with([
                            'policy_spreadsheets' => function ($query) {
                                $query->withCount('policy_spreadsheet_affiliate')->with('policy_spreadsheet_affiliate');
                            }
                        ])
                        ->first();
                    $noEmail = false;
                    if (
                        $lastSpreadsheetActivity &&
                        $lastSpreadsheetActivity->policy_spreadsheets &&
                        $lastSpreadsheetActivity->policy_spreadsheets->policy_spreadsheet_affiliate_count === 1 &&
                        $lastSpreadsheetActivity->policy_spreadsheets->policy_spreadsheet_affiliate->first()->affiliate_id === $lastSpreadsheetActivity->affiliate_id
                    ) {
                        $noEmail = true;
                    }
                    if (in_array($activity->policy_sort->work_modality_id, [1, 5]) && !$noEmail) {

                        $affiliate = Affiliate::where('id', $activity->affiliate_id)->first();
                        $affiliate_name = mb_convert_case(mb_strtolower($affiliate->first_name ?? ''), MB_CASE_TITLE, "UTF-8");
                        $emailAffiliate = $affiliate->email;

                        // Obtener id_user de la actividad
                        $userActivityId = $activity->user_id ?? null;

                        // Construcción del correo electrónico
                        $emailsToSend = [$emailAffiliate];

                        //Se obtienen emails de tomadores autorizados
                        $takerAuthData = collect();
                        if ($activity) {
                            $idActivityTaker = $activity->id;
                            $takerAuthData = $this->getAuthorizedTakerEmails($idActivityTaker);
                        }
                        $emailUsersTakerAuthorized = $takerAuthData->pluck('email');
                        // Combina los emails iniciales con los emails de los usuarios autorizados
                        $allEmails = collect([$emailsToSend])
                            ->merge($emailUsersTakerAuthorized)
                            ->filter(function ($email) {
                                return !empty($email);
                            })
                            ->unique()
                            ->values();

                        $finalEmails = $allEmails->toArray();

                        // Filtrar correos válidos
                        $emailsToSend = array_filter($finalEmails, function ($email) {
                            return filter_var($email, FILTER_VALIDATE_EMAIL);
                        });

                        //Contruccion del cuerpo del correo a enviar
                        $subject = "Planilla mensual no reportada #" . $activity->policy_sort->formatSortNumber();
                        $client_id = $client->id;
                        $text = "¡Buen día, $affiliate_name!.

                        Le informamos que no hemos recibido su planilla mensual de trabajadores correspondiente a <b>$monthName</b> de <b>$year</b>.
                        
                        Como ha pasado el plazo establecido de 10 días hábiles para el reporte de su planilla, tomaremos como referencia la información proporcionada en la última planilla reportada para los registros y gestiones necesarias, la cual no contempla los trabajadores de nuevo ingreso a partir de ese último reporte.
                         
                        Le recordamos que esto podría ocasionar casos no asegurados, por lo que le agradecemos tomarlo en cuenta. Es fundamental trabajar juntos para asegurar que contemos con la información actualizada, y así, garantizar que estén debidamente protegidos en caso de requerir la cobertura.
                        
                        Agradecemos su confianza y colaboración. Nuestro propósito es fortalecer la prevención en salud y seguridad laboral del país.";

                        if(!empty($emailsToSend)){

                            $mailSent = new SendDocumentDataBase(
                                implode(',', $emailsToSend),
                                $subject,
                                "<EMAIL>",
                                $subject,
                                [
                                    "text" => $text,
                                    "sender" => Senders::MNK_INSURANCE
                                ],
                                "<EMAIL>",
                                [], // Sin archivos adjuntos
                                "send_document_db",
                                $client_id,
                                $request->getHost(),
                                $activity->id,
                                Action::REPORTAR_PLANILLA_TOMADOR,
                                Service::SERVICE_REPORT_TAKEN_FORM_MNK
                            );

                            // Capturar el resultado del envío
                            $result = $mailSent->sendMail();

                            //Registramos los datos del correo enviado para la trazabilidad
                            $mailBoardController = new MailBoardController();

                            foreach ($emailsToSend as $email) {

                                $takerAuthorizedId = null;
                                $authorizedTaker = $takerAuthData->firstWhere('email', $email);
                                if ($authorizedTaker) {
                                    $takerAuthorizedId = $authorizedTaker->id;
                                }
                                $mailBoardController->createRegisterMail(
                                    $activity->id,
                                    $activity->service->id,
                                    $activity->policy_sort->consecutive,
                                    'Asegurado',
                                    $affiliate_name,
                                    $affiliate->doc_number,
                                    $subject,
                                    $text,
                                    $emailsToSend,
                                    $result,
                                    null,
                                    $takerAuthorizedId
                                );
                            }
                        }
                    }
                    DB::commit();
                    $count++;
                } catch (Exception $e) {
                    DB::rollback();
                    $errors++;
                    continue; // Continuar con la siguiente actividad
                }
            }

            // Consulta adicional: Pólizas que tienen planilla TEMPORAL (state_id = 201)
            $policyActivitiesWithTempPlanilla = Activity::where('service_id', Service::SERVICE_POLICY_SORT_MNK)
                ->where('state_id', StatePoliza::POLIZA_EMITIDA_ACTIVA)
                ->whereHas('policy_sort', function ($query) {
                    $query->where('validity_from', '<', now())
                        ->where('work_modality_id', '!=', 4);
                })
                ->whereHas('children', function ($query) {
                    $query->where('service_id', Service::SERVICE_REPORT_TAKEN_FORM_MNK)
                        ->where('state_id', 201);
                })
                ->with([
                    'policy_sort',
                    'children' => function ($query) {
                        $query->where('service_id', Service::SERVICE_REPORT_TAKEN_FORM_MNK)
                            ->latest()
                            ->where('state_id', 201)
                            ->with('policy_spreadsheets');
                    }
                ])
                ->get();
            foreach ($policyActivitiesWithTempPlanilla as $activity) {
                try {
                    $activitySpreadsheet = $activity->children->first();
                    DB::beginTransaction();
                    //Actualizamos el encabezado de planilla
                    $spreadSheet = $activitySpreadsheet->policy_spreadsheets;
                    $spreadSheet->total_salaries = $activity->policy_sort->salary_projection;
                    $spreadSheet->observacion = 'Planilla creada por el sistema por medio de una planilla temporal';
                    $spreadSheet->save;

                    //Ejecutamos acciones sobre la planilla
                    ActionController::create(
                        $activitySpreadsheet->id,
                        Action::REPORTAR_PLANILLA_TOMADOR,
                        'REPORTAR PLANILLA TOMADOR CREADA POR EL SISTEMA'
                    );
                    ActionController::create(
                        $activitySpreadsheet->id,
                        Action::GENERAR_CERTIFICADO_TOMADOR,
                        'REPORTAR PLANILLA TOMADOR CREADA POR EL SISTEMA'
                    );
                    DB::commit();
                } catch (\Exception $e) {
                    DB::rollback();
                    continue;
                }
            }

            return response()->json([
                'message' => 'Correos enviados correctamente para todas las actividades sin planilla definitiva',
                'count' => $count,
                'errors' => $errors
            ]);
        } catch (\Exception $e) {
            return response()->json(['error' => 'No se pudo enviar el correo', 'details' => $e->getMessage()], 500);
        }
    }


    /**
     * Verifica si la fecha actual es el día N hábiles del mes
     *
     * @param \Carbon\Carbon $date
     * @return bool
     */
    private function isNthBusinessDayOfMonth($date, $number)
    {
        // Definir el inicio y el final del mes
        $startOfMonth = $date->copy()->startOfMonth();
        $endOfMonth = $date->copy()->endOfMonth();

        // Obtener los días festivos dentro del mes actual
        $holidays = Holiday::whereBetween('holiday', [$startOfMonth, $endOfMonth])
            ->pluck('holiday')
            ->map(function ($holiday) {
                return Carbon::parse($holiday)->toDateString();
            })
            ->toArray();

        $currentDate = $startOfMonth;
        $businessDaysCount = 0;

        // Contar los días hábiles hasta el día en cuestión
        while ($currentDate->lessThanOrEqualTo($date)) {
            // Verifica si el día actual es un día hábil (lunes a viernes y no festivo)
            if (
                !in_array($currentDate->dayOfWeek, [Carbon::SATURDAY, Carbon::SUNDAY]) &&
                !in_array($currentDate->toDateString(), $holidays)
            ) {
                $businessDaysCount++;
            }
            // Verifica si el día actual es el día 10 hábil del mes
            if ($businessDaysCount === $number && $currentDate->toDateString() === $date->toDateString()) {
                return true;
            }

            $currentDate->addDay();
        }

        return false;
    }

    //Acción Registrar PAGO
    public function actionRegisterPayment(Request $request, $cpath, $activity_id)
    {

        DB::beginTransaction();

        try {
            //Bussmao la actividad de la poliza por su activity_id
            $activityPolicy = Activity::where('id', $activity_id)->firstOrFail();

            //Se ejecuta la acción REPORTAR PAGO EMISIÓN POLIZA REALIZADO del servicio COBROS.


            //Ejecutamos la acción REGISTRAR_PAGO y cambiamos el estado de la poliza a PENDIENTE EMITIR POLIZA
            $descripcion = "se generó la acción REGISTRAR_PAGO de poliza SORT";
            ActionController::create($activityPolicy->id, ActionPolizaSort::REGISTRAR_PAGO, $descripcion);

            DB::commit();

            return response()->json(['success' => 'Acción realizada'], 200);
        } catch (\Exception $e) {
            return response()->json(['error' => 'No se pudo enviar el correo', 'details' => $e->getMessage()], 500);
        }
    }

    /**
     * Función que permite crear el servicio de Planilla Afiliados
     * @param $req
     * @param $activity_id
     * @param $client
     * @return JsonResponse|void
     * @throws Exception
     */
    public function affiliatePayrollReport($req, $activity_id, $client, $consecutive)
    {
        // Buscamos la actividad de la póliza por su activity_id
        $activity = Activity::where('id', $activity_id)->firstOrFail();

        // Obtener la última actividad relacionada
        $activityAffiliate = Activity::where('affiliate_id', $activity->affiliate_id)
            ->where('service_id', Service::SERVICE_REPORT_TAKEN_FORM_MNK)
            ->where('parent_id', $activity_id)
            ->latest('created_at') // Obtener la última actividad creada
            ->first();

        if (!$activityAffiliate) {
            throw new Exception('No se encontró actividad');
        }

        // Buscar la última PolicySpreadsheet para esta actividad
        $policySpreadsheet = PolicySpreadsheet::where('activity_id', $activityAffiliate->id)
            ->latest('created_at') // Obtener la última planilla creada
            ->first();

        if (!$policySpreadsheet) {
            throw new Exception('No se encontró planilla');
        }

        // Genera servicio para afiliados y envia correo con certificado
        //$this->affiliateCertificate($policySpreadsheet->id, $consecutive);
        // $host = $req->getHost();

        // Llamar a generateCertificate con el ID de la actividad para el envío del Tomador

        $this->reportFinalPayrollIntermediario($activity_id);

        // $this->generateCertificate($client, $activity->id, $host);
    }


    /**
     * @throws Exception
     */
    public function affiliateCertificate($policySpreadsheetID, $consecutive)
    {
        $integrationServiceController = new IntegrationServiceController();
        $url = config('app.api_url') . '/planilla_afiliados/certificados';
        $method = 'POST';
        $data = [
            'policy_spreadsheet_id' => $policySpreadsheetID,
            'consecutive' => $consecutive,
        ];

        $result = $integrationServiceController->requestRenAppApi($url, $method, $data);
        $data = json_decode($result->getContent(), true);

        if (isset($data['success']) && $data['success']) {
            return;
        }
        // Respuesta en caso de fallo en la API
        throw new Exception('Certificados no generados');
    }

    public function includeEmployeView(Request $req, $cpath, $id, $npoliza)
    {
        //Buscamos la poliza por su id
        $policy = PolicySort::find($npoliza);

        // Pasar los datos a la vista
        return view('services.policy_sort.holder_policy.menu.include_employe', [
            'npoliza' => $npoliza,
            'id' => $id,
            'policy_sort' => $policy,
            'active' => 'inclusion_empleado'
        ]);
    }

    //Vista blade para suspende la pólixa temporalmente
    public function suspendPolicyView(Request $req, $cpath, $id, $npoliza)
    {
        //Buscamos la poliza por su id
        $policy = PolicySort::find($npoliza);

        // Pasar los datos a la vista
        return view('services.policy_sort.holder_policy.menu.suspend_policy', [
            'npoliza' => $npoliza,
            'id' => $id,
            'policy_sort' => $policy,
            'active' => 'suspend_policy'
        ]);
    }

    //Ejecutamos la acción suspender poliza temporalmente MS-1901
    public function actionSuspendPolicyTemporality(Request $req, $id)
    {

        try {
            //Buscamos la poliza por su id
            $policy = PolicySort::find($req->id);

            //Ejecutmos la acción suspender poliza temporalmente
            $description = "se suspendio la póliza temporalmente";

            ActionController::create(
                $policy->activity->id,
                ActionPolizaSort::REPORTAR_SUSPENSION_TEMPORAL_TRABAJADORES,
                $description
            );

            //Guardamos la fecha de la suspensión de la poliza 
            $policy->date_suspend = Carbon::now();
            $policy->save();

            return response()->json([
                'status' => true,
                'message' => 'Se ejecuto la acción correctamente',
                'id' => $policy->activity->affiliate->id
            ]);
        } catch (Exception $e) {

            DB::rollback();
            return response()->json([
                'status' => 'error',
                'message' => 'Ocurrió un error en el proceso',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    //Ejecutamos la acción reactivar poliza suspendida MS-1902
    public function actionReactivePolicySuspend(Request $req, $id)
    {

        try {
            //Buscamos la poliza por su id
            $policy = PolicySort::find($id);

            //Ejecutmos la acción suspender poliza temporalmente
            $description = "se reactivo la póliza";

            ActionController::create(
                $policy->activity->id,
                ActionPolizaSort::REPORTAR_ACTIVACION_TRABAJADORES,
                $description
            );

            //Obtenemos la fecha de suspensión y la fecha actual
            $dateSuspend = Carbon::parse($policy->date_suspend);
            $currentDate = Carbon::now();

            // Calcular la diferencia en días
            $daysElapsed = $dateSuspend->diffInDays($currentDate);

            // Sumar los días transcurridos a la fecha actual de `validity_to`
            $policy->validity_to = Carbon::parse($policy->validity_to)->addDays($daysElapsed);
            $policy->save();

            return response()->json([
                'status' => true,
                'message' => 'Se ejecuto la acción correctamente'
            ]);
        } catch (Exception $e) {

            DB::rollback();
            return response()->json([
                'status' => 'error',
                'message' => 'Ocurrió un error en el proceso',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    //GUARDAR TIPO DE MONEDA DESDE INFORME PLANILLA Y OBSERVACION DE LA PLANILLA
    public function UpdateCurrencyType(Request $request)
    {
        $policySort = PolicySort::where('id', $request->policy_sort_id)->firstOrFail();
        $activityPolicySpreadSheet = Activity::where('parent_id', $policySort->activity_id)
            ->where('service_id', Service::SERVICE_REPORT_TAKEN_FORM_MNK)
            ->first();
        DB::beginTransaction();
        try {
            $policySort->type_currency = $request->type_currency;
            $policySort->save();
            if (isset($activityPolicySpreadSheet)) {
                $policySpreadSheet = $activityPolicySpreadSheet->policy_spreadsheets;
                $policySpreadSheet->observacion = $request->observation;
                $policySpreadSheet->save();
            }
            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Moneda de la póliza actualizada correctamente',
            ]);
        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'message' => 'Error al actualizar la moneda de la póliza',
                'e' => $e->getMessage(),
            ], 500);
        }
    }

    // Función reutilizable para crear gráficos Pie con parametros
    public function createPieChart($canvasId, $labelGraphic, $labels, $data, $backgroundColors)
    {
        return [
            'canvasId' => $canvasId, // El ID del canvas donde se renderizará el gráfico
            'type' => 'pie', // Tipo de gráfico, en este caso 'pie'
            'data' => [
                'labels' => $labels, // Etiquetas que se mostrarán en el gráfico (por ejemplo, nombres de las categorías)
                'datasets' => [
                    [
                        'label' => $labelGraphic, // Etiqueta que describe el conjunto de datos, se pasa como parámetro
                        'data' => $data, // Los valores numéricos para cada etiqueta
                        'backgroundColor' => $backgroundColors, // Colores de fondo para cada segmento del gráfico
                        'hoverOffset' => 4, // Desplazamiento al pasar el mouse sobre los segmentos
                    ],
                ],
            ],
            'options' => [
                'responsive' => true, // Hace que el gráfico sea responsive y se ajuste a diferentes tamaños de pantalla
                'maintainAspectRatio' => false, // Permite que el gráfico se ajuste sin mantener su relación de aspecto original
                'plugins' => [
                    'legend' => [
                        'position' => 'top', // Posición de la leyenda en la parte superior
                    ],
                ],
            ],
        ];
    }
    //ENVIAR CORREOS DE POLIZA
    public function sendEmailPolicy($id)
    {
        //Enviamos los documentos al tomador de la póliza
        $activity = Activity::where('id', $id)
            ->where('service_id', Service::SERVICE_POLICY_SORT_MNK)->first();

        if (!$activity) {
            return response()->json([
                'message' => 'Poliza no encontrada',
            ]);
        }
        $policy = $activity->policy_sort;

        //Buscar action para enviar al correo
        $activityAction = $activity->activity_actions()
            ->where('action_id', Action::EMITIR_POLIZA_DESDE_POLIZA)
            ->where('activity_id', $activity->id)
            ->latest()
            ->first();
        //Buscamos el path en acitivty action document
        $activityActionDocument = ActivityActionDocument::where('activity_action_id', $activityAction->id)->get();
        $files = array();
        foreach ($activityActionDocument as $document) {
            $files[] = [
                'type' => 'PDF',
                'path' => $document->path,
                'name' => "$document->name.pdf",
            ];
        }

        $url_env = config('app.url');
        $client = $activity->client;
        $documents = AppServiceProvider::$S3_DOCUMENTS_MNK;
        $pdf_norma_tecnica_url = $documents['norma_tecnica'];
        $pdf_condiciones_generales_url = $documents['condiciones_generales'];

        //Generamos un email para el intermediario
        $taker = ucwords(strtolower($policy->activity->affiliate->first_name . $policy->activity->affiliate->last_name));
        $intermediary = ucwords(strtolower($policy->advisor_name));

        $id = $policy->formatSortNumber();
        $unique_code = $policy->unique_code;
        $consecutive = $policy->formatNumberConsecutive();
        $user = User::Where('affiliate_id', $activity->affiliate_id)->first();

        $password = 'Temp'.$activity->id.'-'. date('Ymd');
        $user->password = Hash::make($password);
        $user->save();

        $body = "
        ¡Buen día, $taker!

        Nos complace informarle que hemos emitido su póliza $id.

        Le invitamos a verificar la siguiente información importante:
        <ul>
            <li>Condiciones particulares de su seguro.</li>
            <li>Norma técnica <a href='$pdf_norma_tecnica_url'>aquí</a>.</li>
            <li>Condiciones generales de su póliza, a las cuales puede acceder <a href='{$pdf_condiciones_generales_url}'>aquí</a>.</li>
            <li>Seguro obligatorio de riesgos del trabajo.</li>
        </ul>
        Además, le solicitamos que para realizar sus reportes de planillas, inclusiones o accidentes, ingrese <a href='{$url_env}'>aquí</a> con su usuario y contraseña, por favor.

        Su usuario y contraseña son los siguientes:
        Usuario: $user->username
        Contraseña: $password
        Código único(Aviso del caso): $unique_code
        
        Nos sentimos sumamente honrados y agradecidos por la confianza que ha depositado en nosotros. Nuestro propósito es transformar la protección en una experiencia ágil, confiable y humana
            ";

        // Agrega el email principal de notificaciones y todos los adicionales
        $notiEmails = $this->getAdditionalNotificationEmails(
            $policy->id
        );
        $strEmails = implode(',', $notiEmails);

        $mailSent = new SendDocumentDataBase(
            $strEmails,
            "Emisión de la póliza $consecutive",
            "<EMAIL>",
            "<EMAIL>",
            [
                "text" => $body,
                "sender" => 'MNK seguros'
            ],
            "<EMAIL>",
            $files,
            "send_document_db",
            $client
        );
        $mailSent->sendMail();

        return response()->json([
            'message' => 'Correo enviado correctamente',
            'poliza' => $consecutive,
            'email tomador' => $policy->notification_email,
            'user' => $user->username
        ]);
    }

    public function videoTutoriales(Request $req, $cpath)
    {

        $videoPlanilla = Video::where('type', 'planilla')->get();
        $videoReporte = Video::where('type', 'reporte')->get();

        return view('services.policy_sort.holder_policy.menu.video_tutorial', [
            'videosPlanilla' => $videoPlanilla,
            'videosReporte' => $videoReporte
        ]);
    }


    public function benefitColectiveIndividual($cpath, Request $req)
    {
        // Obtener los IDs en un array, ya sea uno solo o varios
        $policyIds = isset($req->ids) ? (array) $req->ids : [$req->id];

        // Validar si hay IDs
        if (empty($policyIds)) {
            return response()->json(['message' => 'No se encontraron pólizas'], 400);
        }

        try {
            DB::beginTransaction();

            foreach ($policyIds as $policyId) {
                $policy = PolicySort::find($policyId);

                if (!$policy) {
                    continue; // Si la póliza no existe, continuar con la siguiente
                }

                // Ejecutar la acción EXIME LA INCLUSIÓN DE NUEVAS PERSONAS TRABAJADORAS ME-903
                $description = 'Se ejecutó la acción EXIME LA INCLUSIÓN DE NUEVAS PERSONAS TRABAJADORAS';
                $activityAction = ActionController::create(
                    $policy->activity->id,
                    ActionPolizaSort::EXIME_INCLUSION_PERSONA_TRABAJADORA,
                    $description
                );

                // Guardar la fecha y el campo en sí de la colectividad
                $policy->update([
                    "benefit_colective" => "Si",
                    "date_benefit_colective" => $req->date_benefit
                ]);

                // Tomador de la póliza
                $taker = $policy->activity->affiliate;
                $nameTaker = mb_convert_case(mb_strtolower($taker->full_name, 'UTF-8'), MB_CASE_TITLE, 'UTF-8');

                $id = $policy->formatSortNumber();
                $client = $policy->activity->client;

                $body = "
                    ¡Buen día, $nameTaker!

                    Nos complace confirmar que su póliza de Seguro Obligatorio de Riesgos del Trabajo número $id goza del beneficio de Colectividad, por lo que está exonerada de la presentación del reporte de inclusión de las personas trabajadoras de nuevo ingreso.

                    Conforme este beneficio, cuando ingresa una nueva persona trabajadora, esta queda automáticamente asegurada en la póliza, hasta la presentación del reporte de la planilla correspondiente al mes de ingreso de esa persona.

                    Con el propósito de evitar inconvenientes, sírvase tomar en cuenta, que en caso de no realizarse la inclusión de la persona trabajadora de nuevo ingreso en la planilla del mes respectivo, cualquier riesgo laboral que le ocurra desde su ingreso y hasta la fecha en que sea reportado en planilla, será considerado como 'no asegurado', conforme a los artículos 216 y 231 del Código de Trabajo, por lo que el pago total de las prestaciones que MNK Seguros suministre en estos casos estaría exclusivamente a su cargo.

                    Igualmente, hacemos de su conocimiento las situaciones por las cuales este beneficio podría ser eliminado de la póliza:

                    1. Reclasificación de casos ocurridos a personas trabajadoras de nuevo ingreso a “caso no asegurado por no reportarse en planillas” (recurrencia de 2 eventos en el mismo año póliza).
                    2. Declaraciones inexactas sobre el siniestro que hagan incurrir a MNK Seguros en una aceptación indebida (recurrencia de más de 2 eventos).
                    3. Determinación de que no se realizó una adecuada investigación de los siniestros reportados a MNK Seguros (recurrencia de más de 2 eventos).
                    4. Deudas o recibos pendientes de pago correspondientes a periodos anteriores.
                    5. Periodos de no cobertura durante los 2 últimos años de vigencia (recurrencia de más de 1 evento)

                    ¡Muchas gracias por su confianza! Nuestro compromiso en continuar brindándole protección y soluciones de seguro ágiles e innovadoras, así como la experiencia de servicio que usted merece.

                    Cordialmente,

                    <b>Área de Aseguramiento</b> 
                    <b>Seguro Obligatorio de Riesgos del Trabajo</b>
                ";
                $files = $this->_buildCollectiveBenefitCertificate($policy, $activityAction);

                // Agrega el email principal de notificaciones y todos los adicionales
                $notiEmails = $this->getAdditionalNotificationEmails(
                    $policy->id
                );
                $strEmails = implode(',', $notiEmails);

                $mailSent = new SendDocumentDataBase(
                    $strEmails,
                    "Beneficio de colectividad en la póliza $id",
                    "<EMAIL>",
                    "<EMAIL>",
                    [
                        "text" => $body,
                        "sender" => Senders::MNK_INSURANCE
                    ],
                    "<EMAIL>",
                    $files,
                    "send_document_db",
                    $client,
                    null,
                    $policy->activity_id,
                    $activityAction->id
                );

                // Capturar el resultado del envío
                $result = $mailSent->sendMail();

                //Registramos los datos del correo enviado para la trazabilidad
                $mailBoardController = new MailBoardController();
                $mailBoardController->createRegisterMail(
                    $policy->activity_id,
                    Service::SERVICE_POLICY_SORT_MNK,
                    $policy->consecutive,
                    'Tomador',
                    $nameTaker,
                    $taker->doc_number,
                    'Beneficio de colectividad en la póliza' . ' ' .$id,
                    $body,
                    $strEmails,
                    $result,
                    $files
                );
            }

            DB::commit();

            return response()->json(['message' => 'Acción generada'], 200);
        } catch (\Exception $e) {
            dd($e);
            DB::rollback();
            return response()->json([
                'message' => 'Error al procesar la acción',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function benefitColectiveAutomatic($policy)
    {
        // Obtener los IDs en un array, ya sea uno solo o varios
        $policyIds = isset($policy->ids) ? (array) $policy->ids : [$policy->id];

        // Validar si hay IDs
        if (empty($policyIds)) {
            return response()->json(['message' => 'No se encontraron pólizas'], 200);
        }

        try {
            DB::beginTransaction();

            foreach ($policyIds as $policyId) {
                $policy = PolicySort::find($policyId);

                if (!$policy) {
                    continue; // Si la póliza no existe, continuar con la siguiente
                }

                // Ejecutar la acción EXIME LA INCLUSIÓN DE NUEVAS PERSONAS TRABAJADORAS ME-903
                $description = 'Se ejecutó la acción EXIME LA INCLUSIÓN DE NUEVAS PERSONAS TRABAJADORAS';
                $activityAction = ActionController::create(
                    $policy->activity->id,
                    ActionPolizaSort::EXIME_INCLUSION_PERSONA_TRABAJADORA,
                    $description
                );

                // Guardar la fecha y el campo en sí de la colectividad
                $policy->update([
                    "benefit_colective" => "Si",
                    "date_benefit_colective" => $policy->validity_from
                ]);

                // Tomador de la póliza
                $taker = $policy->activity->affiliate;
                $nameTaker = mb_convert_case(mb_strtolower($taker->full_name, 'UTF-8'), MB_CASE_TITLE, 'UTF-8');
                ;

                $id = $policy->formatSortNumber();
                $client = $policy->activity->client;

                $files = $this->_buildCollectiveBenefitCertificate($policy, $activityAction);

                $body = "
                    ¡Buen día, $nameTaker!

                    Nos complace confirmar que su póliza de Seguro Obligatorio de Riesgos del Trabajo número $id goza del beneficio de Colectividad, por lo que está exonerada de la presentación del reporte de inclusión de las personas trabajadoras de nuevo ingreso.

                    Conforme este beneficio, cuando ingresa una nueva persona trabajadora, esta queda automáticamente asegurada en la póliza, hasta la presentación del reporte de la planilla correspondiente al mes de ingreso de esa persona.

                    Con el propósito de evitar inconvenientes, sírvase tomar en cuenta, que en caso de no realizarse la inclusión de la persona trabajadora de nuevo ingreso en la planilla del mes respectivo, cualquier riesgo laboral que le ocurra desde su ingreso y hasta la fecha en que sea reportado en planilla, será considerado como 'no asegurado', conforme a los artículos 216 y 231 del Código de Trabajo, por lo que el pago total de las prestaciones que MNK Seguros suministre en estos casos estaría exclusivamente a su cargo.

                    Igualmente, hacemos de su conocimiento las situaciones por las cuales este beneficio podría ser eliminado de la póliza:

                    1. Reclasificación de casos ocurridos a personas trabajadoras de nuevo ingreso a “caso no asegurado por no reportarse en planillas” (recurrencia de 2 eventos en el mismo año póliza).
                    2. Declaraciones inexactas sobre el siniestro que hagan incurrir a MNK Seguros en una aceptación indebida (recurrencia de más de 2 eventos).
                    3. Determinación de que no se realizó una adecuada investigación de los siniestros reportados a MNK Seguros (recurrencia de más de 2 eventos).
                    4. Deudas o recibos pendientes de pago correspondientes a periodos anteriores.
                    5. Periodos de no cobertura durante los 2 últimos años de vigencia (recurrencia de más de 1 evento)

                    ¡Muchas gracias por su confianza! Nuestro compromiso en continuar brindándole protección y soluciones de seguro ágiles e innovadoras, así como la experiencia de servicio que usted merece.";

                // Agrega el email principal de notificaciones y todos los adicionales
                $notiEmails = $this->getAdditionalNotificationEmails(
                    $policy->id
                );
                $strEmails = implode(',', $notiEmails);

                $mailSent = new SendDocumentDataBase(
                    $strEmails,
                    "Beneficio de colectividad en la póliza $id",
                    "<EMAIL>",
                    "<EMAIL>",
                    [
                        "text" => $body,
                        "sender" => Senders::MNK_INSURANCE
                    ],
                    "<EMAIL>",
                    $files,
                    "send_document_db",
                    $client,
                    null,
                    $policy->activity_id,
                    $activityAction->id
                );
                
                   // Capturar el resultado del envío
                $result = $mailSent->sendMail();

                //Registramos los datos del correo enviado para la trazabilidad
                $mailBoardController = new MailBoardController();
                $mailBoardController->createRegisterMail(
                    $policy->activity_id,
                    Service::SERVICE_POLICY_SORT_MNK,
                    $policy->consecutive,
                    'Tomador',
                    $nameTaker,
                    $taker->doc_number,
                    'Beneficio de colectividad en la póliza' . ' ' .$id,
                    $body,
                    $strEmails,
                    $result,
                    $files
                );
            }

            DB::commit();

            return response()->json(['message' => 'Acción generada'], 200);
        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'message' => 'Error al procesar la acción',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    private function _buildCollectiveBenefitCertificate($policy, $activityAction)
    {
        $startPeriod = Carbon::parse($policy->validity_from)->format('d/m/Y');
        $endPeriod = Carbon::parse($policy->validity_to)->format('d/m/Y');
        // GENERATE PDF
        $document = 'collective_benefit_certificate';
        $path = "activity_action_document/{$document}_{$activityAction->id}.pdf";
        $pdf = PDF::loadView('services.policy_sort.docs.collective_benefit_certificate_pdf',
            [
                'activityPolicy' => $policy->activity,
                'policySort' => $policy,
                'startPeriod' => $startPeriod,
                'endPeriod' => $endPeriod,
            ]
        );

        Storage::disk('s3')->put($path, $pdf->output());

        $files[] = [
            'type' => 'pdf',
            'path' => $path,
            'name' => SendDocument::$SUBJECTS[$document] . '.pdf',
        ];

        // Guardamos el documento en la base de datos
        $activityActionDocument = new ActivityActionDocument();
        $activityActionDocument->activity_action_id = $activityAction->id;
        $activityActionDocument->name = $document;
        $activityActionDocument->path = $path;
        $activityActionDocument->save();

        return $files;
    }


    public function benefitColectiveIncludeWorker($cpath, Request $req)
    {

        $policy = PolicySort::find($req->id);

        if ($policy) {

            //Bucamos si la póliza tiene un beneficio de colectividad
            $benefitColective = $policy->benefit_colective;

            //Si la poliza tiene beneficio celectividad
            if ($benefitColective == 'Si') {
                return response()->json([
                    "success" => true
                ]);
            }

            //Si la poliza no tiene beneficio de colectividad
            return response()->json([
                "success" => false
            ]);
        }
    }

    //calcular valor de la prima de la póliza con condiciones especiales
    // public function calculateConditionSpecialPolicyPrice($policySort){
    //     dd($policySort);
    //     $webserviceController = new WebserviceAcselController();

    //     $minDolares = 100;
    //     $minColones = $webserviceController->convertDollarsToColones($minDolares);
    //     $min = $policySort->type_currency == 'USD' ? $minDolares : $minColones;
    //     $salary_projection = $policySort->salary_projection;
    //     $validity_from = $policySort->validity_from;
    //     $validity_to = $policySort->validity_to;
    //     $sector = $policySort->economic_activity;
    //     $activityEconomicId = $policySort->activity_economic_id;

    //     $salary_projection = floatval($salary_projection);
    //     $validityFrom = new DateTime($validity_from);
    //     $validityTo = new DateTime($validity_to);
    //     $dias = $validityTo->diff($validityFrom)->days;
    //     $months = $dias / 30;
    //     // Simulación de los datos de actividad económica
    //     $percentage = QuotationController::getActivityPercentage($sector, $activityEconomicId); // Función para obtener el porcentaje
    //     // Formato del porcentaje
    //     $percentage = str_replace(',', '.', $percentage);
    //     // $percentage = ($percentage / 100);
    //     $percentage = floatval($percentage);

    //     // Cálculo de primas según la temporalidad
    //     $amountPolicy = $salary_projection * 12 * ($percentage / 100);

    //     $temSemestral = round(($percentage * 1.04), 2);
    //     $temTrimestral = round(($percentage * 1.06), 2);
    //     $temMensual = round(($percentage * 1.08), 2);

    //     $amountPolicySemestral = $salary_projection * 12 *  ($temSemestral / 100)  / 2;
    //     $amountPolicyTrimestral = $salary_projection * 12 * ($temTrimestral / 100)  / 4;
    //     $amountPolicyMensual = $salary_projection * 12 *    ($temMensual / 100) / 12;


    //     $temAnual = number_format(round($percentage, 2), 2, ',', '');
    //     $temSemestral = number_format(round(($percentage * 1.04), 2), 2, ',', '');
    //     $temTrimestral = number_format(round(($percentage * 1.06), 2), 2, ',', '');
    //     $temMensual = number_format(round(($percentage * 1.08), 2), 2, ',', '');

    //     if ($months <= 3) {
    //         $pUnico = 1.08;
    //     } elseif ($months <= 6) {
    //         $pUnico = 1.06;
    //     } else {
    //         $pUnico = 1.04;
    //     }
    //     $temUnico = number_format(round(($percentage * $pUnico), 2), 2, ',', '');

    //     $valorUnico = ($salary_projection * ($percentage / 100)) * $pUnico;


    //     if ($valorUnico < $min) {
    //         $valorUnico = $min;
    //     }
    //     if ($amountPolicy < $min) {
    //         $amountPolicy = $min;
    //     }
    //     if ($amountPolicySemestral < $min) {
    //         $amountPolicySemestral = $min;
    //     }
    //     if ($amountPolicyTrimestral < $min) {
    //         $amountPolicyTrimestral = $min;
    //     }
    //     if ($amountPolicyMensual < $min) {
    //         $amountPolicyMensual = $min;
    //     }

    //     $policySort->anual_percentage = $temAnual;
    //     $policySort->semestral_percentage = $temSemestral;
    //     $policySort->trimestral_percentage = $temTrimestral;
    //     $policySort->mensual_percentage = $temMensual;
    //     $policySort->unico_percentage = $temUnico;

    //     $policySort->save();

    //     $activity_police = Activity::with(['parent_activity'])->where('id', $policySort->activity_id)->where('service_id', Service::SERVICE_POLICY_SORT_MNK)->first();

    //     if ($activity_police) {
    //         $quotation_activity = $activity_police->parent_activity;
    //         $condition = ActivityAction::where('action_id', '=', ActionCotizacionSort::REPORTAR_CONDICIONES_ESPECIALES)->where('activity_id', '=', $quotation_activity->id)->first();

    //         if ($condition) {
    //             $quotation_condition_special = QuotationConditionSpecial::where('activity_id', $quotation_activity->id)->first();


    //             $amountPolicy = $quotation_condition_special->anual_final;
    //             $amountPolicySemestral = $quotation_condition_special->semianual_fraccionada;
    //             $amountPolicyTrimestral = $quotation_condition_special->trimestral_fraccionada;
    //             $amountPolicyMensual = $quotation_condition_special->mensual_fraccionada;

    //             if ($quotation_condition_special->anual_continuidad) {

    //                 $temAnual = $quotation_condition_special->anual_continuidad;
    //                 $temSemestral = $quotation_condition_special->semianual_continuidad;
    //                 $temTrimestral = $quotation_condition_special->trimestral_continuidad;
    //                 $temMensual = $quotation_condition_special->mensual_continuidad;

    //             }
    //         }
    //     }

    //     return [
    //         'amountPolicy' => $amountPolicy,
    //         'amountPolicySemestral' => $amountPolicySemestral,
    //         'amountPolicyTrimestral' => $amountPolicyTrimestral,
    //         'amountPolicyMensual' => $amountPolicyMensual,
    //         'valorUnico' => $valorUnico,
    //         'percentage' => $percentage,
    //         'dias' => $dias,
    //         'temAnual' => $temAnual,
    //         'temSemestral' => $temSemestral,
    //         'temTrimestral' => $temTrimestral,
    //         'temMensual' => $temMensual,
    //         'temUnico' => $temUnico
    //     ];
    // }
}
