<?php

namespace App\Http\Controllers\Services;

use App\Action;
use App\Activity;
use App\ActivityAction;
use App\Client;
use App\DeterminationIt;
use App\DeterminationItInability;
use App\DeterminationItInabilityFrac;
use App\Http\Controllers\Controller;
use App\ItLiquidation;
use App\PaymentBase;
use App\PaymentBaseInability;
use App\Service;
use App\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class PaymentBasesController extends Controller
{

    public function form(Request $req, $cpath, $id)
    {
        $client = Client::query()
            ->where('path', $cpath)
            ->firstOrFail();
        $activity = Activity::query()
            ->where('client_id', $client->id)
            ->where('id', $id)
            ->firstOrFail();
        $parent = $activity->parent;
        return view('services.payment_bases.form', [
            'activity' => $activity,
            'client' => $client->path,
            'parent' => $parent
        ]);
    }

    public function save(Request $req, $cpath, $id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();

        DB::beginTransaction();
        try {
            if (!$activity->payment_bases) {
                $payment_bases = new PaymentBase;
                $payment_bases->activity_id = $id;
            } else {
                $payment_bases = $activity->payment_bases;
            }

            $ids = $req->input('id');

            $novedades = $req->input('novedad');
            $causal_novedades = $req->input('causal_novedad');
            $fechanovedades = $req->input('fechanovedad');
            $oficionovedades = $req->input('oficionovedad');
            $fechaoficionovedades = $req->input('fechaoficionovedad');
            $iblreliquidaciones = $req->input('iblreliquidacion');
            $difeventades = $req->input('diferencia');
            $interesemoras = $req->input('interesemora');
            $indexaciones = $req->input('indexacion');

            for ($i = 0; $i < count($ids); $i++) {
                $inability = PaymentBaseInability::find($ids[$i]);

                if ($inability) {

                    $inability->novedad = $novedades[$i];
                    $inability->causal_novedad = $causal_novedades[$i];
                    $inability->fechanovedad = $fechanovedades[2 * $i + 1];
                    $inability->oficionovedad = $oficionovedades[$i];
                    $inability->fechaoficionovedad = $fechaoficionovedades[2 * $i + 1];
                    $inability->iblreliquidacion = $iblreliquidaciones[$i];
                    $inability->diferencia = $difeventades[$i];
                    $inability->interesemora = $interesemoras[$i];
                    $inability->indexacion = $indexaciones[$i];

                    $inability->save();
                }
            }

            $payment_bases->save();

            DB::commit();
        } catch (Exception $e) {
            DB::rollback();
        }

        return redirect("servicio/$id/payment_bases");
    }

    public function migrateItToPaymentBases(Request $req, $cpath, $lastId)
    {
        $last_id = $lastId;
        $processed_service_payment_bases = []; // array to keep track of the processed service payment bases
        $client = Client::query()
            ->where('path', $cpath)
            ->firstOrFail();
        $activity = Activity::query()
            ->where('service_id', Service::SERVICE_DETERMINATION_IT_COLPENSIONES)
            ->where('id', '>', $last_id)
            ->where('client_id', $client->id)
            ->whereNotIn('state_id', [1, 210, 351])
            ->orderBy('id')
            ->first();

        DB::beginTransaction();

        try {
            if ($activity) {

                $activityActions = ActivityAction::query()
                    ->where('activity_id', $activity->id)
                    ->whereIn('action_id', [Action::APPROVE_IT, Action::APPROVED_QUALITY_CONTROL_IT])
                    ->first();

                if ($activityActions) {
                    $paymentBasesExists = PaymentBase::query()
                        ->where('radicado', $activity->id_bizagi)
                        ->first();

                    if (!$paymentBasesExists) {
                        $newServicePaymentBases = new Activity;
                        $newServicePaymentBases->service_id = Service::SERVICE_IT_LIQUIDATED_COLPENSIONES;
                        $newServicePaymentBases->state_id = 350;
                        $newServicePaymentBases->user_id = $activity->user_id;
                        $newServicePaymentBases->client_id = $activity->client_id;
                        $newServicePaymentBases->affiliate_id = $activity->affiliate_id;
                        $newServicePaymentBases->parent_id = $activity->id;
                        $newServicePaymentBases->save();

                        // add the newServicePaymentBases id to the processed_service_payment_bases array
                        $processed_service_payment_bases[] = $newServicePaymentBases->id;
                        $determinationIt = DeterminationIt::query()->where('activity_id', $activity->id)->first();
                        if ($determinationIt) {
                            $newPaymentBases = new  PaymentBase;
                            $newPaymentBases->activity_id = $newServicePaymentBases->id;
                            $newPaymentBases->radicado = $activity->id_bizagi;
                            $newPaymentBases->documento = $activity->affiliate->doc_number;
                            $newPaymentBases->save();

                            $determinationItInability = DeterminationItInability::query()
                                ->where('determination_it_id', $determinationIt->id)
                                ->where('right_validation', '=', 'APROBADO')
                                ->get();

                            // Calculate the sum of paid_days
                            $total_paid_days = $determinationItInability->sum('paid_days');

                            $author = User::find($activityActions->author_id);

                            foreach ($determinationItInability as $diti) {
                                $newPaymentBaseInability = new  PaymentBaseInability;
                                $newPaymentBaseInability->payment_base_id = $newPaymentBases->id;
                                $newPaymentBaseInability->radicado = $activity->id_bizagi;
                                $newPaymentBaseInability->documento = $activity->affiliate->doc_number;
                                $newPaymentBaseInability->fechacambio = '';
                                $newPaymentBaseInability->aprobado = 'TRUE';
                                $newPaymentBaseInability->nombre = $determinationIt->full_name_affiliate;
                                $newPaymentBaseInability->terceroautpago = $determinationIt->titular_account_type == 'TERCERO' ? $determinationIt->account_full_name : '';
                                $newPaymentBaseInability->numeroresolucion = $determinationIt->reserved_quality_resolution_number;
                                $newPaymentBaseInability->fecharesolucion = $determinationIt->resolution_quality_date;
                                $newPaymentBaseInability->indtramacreedor = '';
                                $newPaymentBaseInability->valorresolucion = $determinationIt->payment_value;
                                $newPaymentBaseInability->valorincapacidad = $diti->payment_it_value;
                                $newPaymentBaseInability->tutela = $determinationIt->tutelage_it;
                                $newPaymentBaseInability->diasapagar = $diti->paid_days;
                                $newPaymentBaseInability->fechainicio = $diti->initial_date;
                                $newPaymentBaseInability->fechafin = $diti->end_date;
                                $newPaymentBaseInability->totaldiasincapacidad = $total_paid_days;
                                $newPaymentBaseInability->registradopor = $author ? $author->username : '';
                                $newPaymentBaseInability->novedad = '';
                                $newPaymentBaseInability->save();
                            }
                        }
                    } else {
                        $activityPBExist = Activity::withTrashed()
                            ->where('id', $paymentBasesExists->activity_id)
                            ->first();

                        if ($activityPBExist) {
                            $activityPBExist->deleted_at = null;
                            $activityPBExist->save();
                        }
                    }
                }
                $last_id = $activity->id;
            }
            DB::commit();
        } catch (Exception $e) {
            DB::rollback();
            \Log::error($e);
            return 'ERROR: ' . $e->getMessage();
        }

        if ($last_id != $lastId) {
            return view('extras.migrate_it_to_pb', ['id' => $last_id, 'processed_service_payment_bases' => $processed_service_payment_bases]);
        }
        return 'FINALIZADO';
    }

    public function migrateItToPaymentBasesPaidValue(Request $req, $cpath, $lastId)
    {
        $last_id = $lastId;
        $processed_service_payment_bases = []; // array to keep track of the processed service payment bases
        $client = Client::query()
            ->where('path', $cpath)
            ->firstOrFail();
        $activity = Activity::query()
            ->where('service_id', Service::SERVICE_DETERMINATION_IT_COLPENSIONES)
            ->where('id', '>', $last_id)
            ->where('client_id', $client->id)
            ->whereNotIn('state_id', [1, 210, 351])
            ->orderBy('id')
            ->first();

        DB::beginTransaction();

        try {
            if ($activity) {

                $activityActions = ActivityAction::query()
                    ->where('activity_id', $activity->id)
                    ->whereIn('action_id', [Action::APPROVE_IT, Action::APPROVED_QUALITY_CONTROL_IT])
                    ->first();

                if ($activityActions) {
                    $paymentBasesExists = PaymentBase::query()
                        ->where('radicado', $activity->id_bizagi)
                        ->first();

                    if ($paymentBasesExists) {
                        $determinationIt = DeterminationIt::query()->where('activity_id', $activity->id)->first();
                        if ($determinationIt) {
                            $determinationItInability = DeterminationItInability::query()
                                ->where('determination_it_id', $determinationIt->id)
                                ->where('right_validation', '=', 'APROBADO')
                                ->get();
                            foreach ($determinationItInability as $diti) {
                                $newPaymentBaseInability = PaymentBaseInability::query()
                                    ->where('payment_base_id', $paymentBasesExists->id)
                                    ->whereNull('fraccionado')
                                    ->get();

                                foreach ($newPaymentBaseInability as $npbi) {
                                    $npbi->valorresolucion = $determinationIt->payment_value;
                                    $npbi->save();
                                }
                            }
                        }
                    } else {
                        $activityPBExist = Activity::withTrashed()
                            ->where('id', $paymentBasesExists->activity_id)
                            ->first();

                        if ($activityPBExist) {
                            $activityPBExist->deleted_at = null;
                            $activityPBExist->save();
                        }
                    }
                }
                $last_id = $activity->id;
            }
            DB::commit();
        } catch (Exception $e) {
            DB::rollback();
            \Log::error($e);
            return 'ERROR: ' . $e->getMessage();
        }

        if ($last_id != $lastId) {
            return view('extras.migrate_it_to_pb_valor', ['id' => $last_id, 'processed_service_payment_bases' => $processed_service_payment_bases]);
        }
        return 'FINALIZADO';
    }

    public function migrateItToPaymentBasesFractions(Request $req, $cpath, $lastId)
    {
        $last_id = $lastId;
        $processed_service_payment_bases = []; // array to keep track of the processed service payment bases
        $client = Client::query()
            ->where('path', $cpath)
            ->firstOrFail();
        $activity = Activity::query()
            ->where('service_id', Service::SERVICE_DETERMINATION_IT_COLPENSIONES)
            ->where('id', '>', $last_id)
            ->where('client_id', $client->id)
            ->whereNotIn('state_id', [1, 210, 351])
            ->orderBy('id')
            ->first();


        DB::beginTransaction();

        try {
            if ($activity) {

                $activityActions = ActivityAction::query()
                    ->where('activity_id', $activity->id)
                    ->whereIn('action_id', [Action::APPROVE_IT, Action::APPROVED_QUALITY_CONTROL_IT])
                    ->first();
                $paymentBasesExists = PaymentBase::query()
                    ->where('radicado', $activity->id_bizagi)
                    ->first();

                $determinationIt = DeterminationIt::query()->where('activity_id', $activity->id)->first();

                if ($activityActions) {
                    $author = User::find($activityActions->author_id);

                    if ($determinationIt && !$paymentBasesExists) {
                        $newServicePaymentBases = new Activity;
                        $newServicePaymentBases->service_id = Service::SERVICE_IT_LIQUIDATED_COLPENSIONES;
                        $newServicePaymentBases->state_id = 350;
                        $newServicePaymentBases->user_id = $activity->user_id;
                        $newServicePaymentBases->client_id = $activity->client_id;
                        $newServicePaymentBases->affiliate_id = $activity->affiliate_id;
                        $newServicePaymentBases->parent_id = $activity->id;
                        $newServicePaymentBases->save();

                        $newPaymentBases = new  PaymentBase;
                        $newPaymentBases->activity_id = $newServicePaymentBases->id;
                        $newPaymentBases->radicado = $activity->id_bizagi;
                        $newPaymentBases->documento = $activity->affiliate->doc_number;
                        $newPaymentBases->save();
                    } else {
                        $newPaymentBases = PaymentBase::find($paymentBasesExists->id);
                    }
                    $determinationItInability = DeterminationItInability::query()
                        ->where('determination_it_id', $determinationIt->id)
                        ->where('fractional_it', 'SI')
                        ->get();

                    foreach ($determinationItInability as $diti) {
                        $determinationItInabilityFraction = DeterminationItInabilityFrac::query()
                            ->where('determination_it_inability_id', $diti->id)
                            ->where('validation_result', 'APROBADO')
                            ->get();

                        // Calculate the sum of paid_days
                        $total_paid_days = $determinationItInabilityFraction->sum('it_days_pay');

                        foreach ($determinationItInabilityFraction as $ditif) {
                            $newPaymentBaseInability = new  PaymentBaseInability;
                            $newPaymentBaseInability->payment_base_id = $newPaymentBases->id;
                            $newPaymentBaseInability->radicado = $activity->id_bizagi;
                            $newPaymentBaseInability->documento = $activity->affiliate->doc_number;
                            $newPaymentBaseInability->fechacambio = '';
                            $newPaymentBaseInability->aprobado = 'TRUE';
                            $newPaymentBaseInability->nombre = $determinationIt->full_name_affiliate;
                            $newPaymentBaseInability->terceroautpago = $determinationIt->titular_account_type == 'TERCERO' ? $determinationIt->account_full_name : '';
                            $newPaymentBaseInability->numeroresolucion = $determinationIt->reserved_quality_resolution_number;
                            $newPaymentBaseInability->fecharesolucion = $determinationIt->resolution_quality_date;
                            $newPaymentBaseInability->indtramacreedor = '';
                            $newPaymentBaseInability->valorresolucion = $determinationIt->payment_value;
                            $newPaymentBaseInability->valorincapacidad = $ditif->value_pay;
                            $newPaymentBaseInability->tutela = $determinationIt->tutelage_it;
                            $newPaymentBaseInability->diasapagar = $ditif->it_days_pay;
                            $newPaymentBaseInability->fechainicio = $ditif->initial_date;
                            $newPaymentBaseInability->fechafin = $ditif->end_date;
                            $newPaymentBaseInability->totaldiasincapacidad = $total_paid_days;
                            $newPaymentBaseInability->registradopor = $author ? $author->username : '';
                            $newPaymentBaseInability->novedad = '';
                            $newPaymentBaseInability->fraccionado = 'SI';
                            $newPaymentBaseInability->save();
                        }
                    }
                }

                $last_id = $activity->id;
            }
            DB::commit();
        } catch (Exception $e) {
            DB::rollback();
            \Log::error($e);
            return 'ERROR: ' . $e->getMessage();
        }

        if ($last_id != $lastId) {
            return view('extras.migrate_it_to_pb_frac', ['id' => $last_id, 'processed_service_payment_bases' => $processed_service_payment_bases]);
        }
        return 'FINALIZADO';
    }
}
