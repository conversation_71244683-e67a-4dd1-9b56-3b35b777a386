<?php

namespace App\Http\Controllers;



use App\Action;
use App\Actions\ActionPolizaSort;
use App\Activity;
use App\ActivityDocument;
use App\Affiliate;
use App\Client;
use App\GisSort;
use App\Http\Controllers\Services\Exception;
use App\PolicySort;
use App\Service;
use App\ServiceDocument;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Storage;
use PDF;

class GisSignatureController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }
    
    public function saveSignature(Request $request, $cpath, $id)
    {
        // Obtener la imagen en formato base64 desde la solicitud
        $image_gis_representative = $request->input('image_gis_representative');

        try {

            // Actividad hijo (Policy)
            $gisActivity = Activity::where('id', $id)
                ->where('service_id', Service::SERVICE_GIS_SORT_MNK)
                ->first();

            if (!$gisActivity) {
                return response()->json(['message' => 'No se encontró la actividad de la póliza.'], 404);
            }

            // Obtengo la PolicySort segun su actividad
            $gis = GisSort::where('activity_id', $gisActivity->id)->first();

            if (!$gis) {
                return response()->json(['message' => 'No se encontró la póliza.'], 404);
            }

            // Procesar policyholder
            if ($image_gis_representative) {
                $image = str_replace('data:image/png;base64,', '', $image_gis_representative);
                $image = str_replace(' ', '+', $image);
                $imageData = base64_decode($image);

                $fileName = 'signature/gis_' . $id . '_representative.png';
                Storage::disk('s3')->put($fileName, $imageData);
                $gis->sign_gis_representative = $fileName;
            }

            // Guardar los cambios en la póliza
            $gis->save();

            //generar el documento temporal
            $ReopeningForm = $this->requestDocumentSignatureReopening($request, $cpath, $gis->sign_gis_representative, $gisActivity->id);

            return response()->json([
                'success' => true,
                'message' => 'Firma guardada temporalmente.',
                'representative_url' => $gis->sign_gis_representative ?? null,
                'reopening_url' => $ReopeningForm
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'No se logró procesar la firma.',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function requestDocumentSignatureReopening($req, $cpath, $sign_gis_representative, $gisActivityId)
    {
        try {


            $client = Client::where('path', $cpath)->firstOrFail();
            $activity = Activity::where('client_id', $client->id)->where('id', $gisActivityId)->firstOrFail();
            $gisSort = GisSort::where('activity_id', $activity->id)->first();

            //obtener la firma del representante
            $urls = Storage::disk('s3')->url($sign_gis_representative); // Obtener la URL de sign1

            //consultar los datos del asegurado
            $asegurado = $activity->affiliate;

            //consultar los datos del tomador y poliza
            $policyActivty = $activity->parent_activity;
            $tomador = $policyActivty->affiliate;
            $policy = $policyActivty->policy_sort;

            //recibir los datos del formulario
            $data = [
                'name_affiliate' => $asegurado->full_name ?? '',
                'num_affiliate' => $asegurado->doc_number ?? '',
                'name_holder' => $tomador->full_name ?? '',
                'num_policy' => $policy->formatNumberConsecutive() ?? '',
                'num_case' => $gisSort->consecutive ?? '',
                'date_medical_discharge' => $req->alta_medica,
                'reason' => $req->motivo_reapertura,
                'name_applicant' => $req->tomador_nombre,
                'sign1_url' => $urls,
            ];

            // Cargar la vista del PDF y pasarle los datos
            $document = 'formulario_reapertura_pdf';
            $pdf = PDF::loadView("services.gis.docs.{$document}", [
                'fecha_solicitud' => date('Y-m-d'),
                'data' => $data,  // Reemplazamos los datos generados anteriormente
                'watermark' => false
            ]);

            //generar un unico nombre para el documento + el número de la fecha de hoy (año/Mes/Día)
            $uniqueid = uniqid() . date('Ymd');

            //nombre de la ruta del dcumento
            $filePath = "activity_action_document/{$document}_{$uniqueid}.pdf";

            //guardar el documento en s3
            Storage::disk('s3')->put($filePath, $pdf->output());

            //ruta del documento en s3
            $urlS3 = Storage::disk('s3')->url($filePath);

            return $urlS3;

        } catch (Exception $e) {
            throw new Exception("Error al generar el formulario de respertura: " . $e->getMessage(), 500);
        }
    }


}
