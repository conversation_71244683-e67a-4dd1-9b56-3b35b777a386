<?php

namespace App\Http\Controllers;

use App\Client;
use App\Providers\AppServiceProvider;
use Auth;
use Illuminate\Http\Request;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Excel;

class ExcelReportGisController extends Controller
{

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
    }

    //ME-1158 - Requerimiento 41
    public function reportFirstLevelGis(Request $req, $cpath)
    {
        try {

            // Capturar las fechas del formulario
            $initDate = $req->rflg_start_date_submit;
            $lastDate = $req->rflg_end_date_submit;

            // Validar que las fechas no sean nulas
            if (empty($initDate) || empty($lastDate)) {
                throw new \Exception("Las fechas no pueden estar vacías");
            }

            // formateamos fechas
            $initDate = Carbon::parse($req->rflg_start_date_submit)->startOfDay();
            $lastDate = Carbon::parse($req->rflg_end_date_submit)->endOfDay();

            // Validar que $lastDate no sea menor que $initDate
            if ($lastDate->lessThan($initDate)) {
                throw new \Exception("La fecha de fin no puede ser menor a la fecha de inicio.");
            }

            // Consulta a la vista
            $consulta = "select * from report_first_level_gis where date_filter BETWEEN :initDate AND :lastDate ";

            $query = DB::select($consulta, [
                'initDate' => $initDate,
                'lastDate' => $lastDate
            ]);

            // Construir datos para Excel
            $activities = [];
            foreach ($query as $item) {
                $activities[] = [
                    $item->gis_consecutive,
                    $item->follow_up_number,
                    $item->policy_consecutive,
                    $item->doc_number_taker,
                    $item->name_taker,
                    mb_strtoupper($item->activity_economic_name, 'UTF-8'),
                    $item->doc_number_worker,
                    mb_strtoupper($item->worker_name, 'UTF-8'),
                    $item->worker_gender,
                    $item->worker_ager,
                    $item->cod_intermediario,
                    mb_strtoupper($item->nombre_intermediario, 'UTF-8'),
                    $item->cod_corredor,
                    mb_strtoupper($item->nombre_corredor, 'UTF-8'),
                    $item->accident_date,
                    $item->report_gis_date,
                    $item->qualification_date,
                    $item->expense_type, // Actualmente está en blanco en el SELECT
                    $item->expense_amount, // Actualmente está en blanco en el SELECT
                    mb_strtoupper($item->provider_name, 'UTF-8'),
                    mb_strtoupper($item->service_group, 'UTF-8'),
                    AppServiceProvider::$ACCIDENT_TYPES[$item->occurrence_manner ?? ''] ?? '',
                    AppServiceProvider::$AGENTS[$item->material_agent ?? ''] ?? '',
                    AppServiceProvider::$INJURIES[$item->injury_mechanism ?? ''] ?? '',
                    $item->third_party_assistance_required === null || $item->third_party_assistance_required === ''
                        ? ''
                        : ($item->third_party_assistance_required == 1 ? 'Sí' : 'No'),
                    AppServiceProvider::$WORK_MODES[$item->work_modality ?? ''] ?? '',
                    $item->external_cause === null || $item->external_cause === ''
                        ? ''
                        : ($item->external_cause == 1 ? 'Sí' : 'No'),
                    $item->external_medical_care === null || $item->external_medical_care === ''
                        ? ''
                        : ($item->external_medical_care == 1 ? 'Sí' : 'No'),
                    mb_strtoupper($item->diagnosis, 'UTF-8'),
                    mb_strtoupper($item->injured_body_parts, 'UTF-8'),
                    mb_strtoupper($item->worker_province, 'UTF-8'),
                    mb_strtoupper($item->worker_canton, 'UTF-8'),
                    mb_strtoupper($item->worker_district, 'UTF-8'),
                ];
            }

            $headings = [
                'NÚMERO DE CASO', 'NÚMERO DE ATENCIÓN', 'ID PÓLIZA', 'ID CLIENTE', 'NOMBRE CLIENTE', 'ACTIVIDAD ECONOMICA',
                'ID TRABAJADOR', 'NOMBRE TRABAJADOR', 'SEXO', 'EDAD', 'ID INTERMEDIARIO', 'NOMBRE INTERMEDIARIO',
                'ID CORREDOR', 'NOMBRE CORREDOR', 'FECHA DE OCURRENCIA', 'FECHA DE AVISO', 'FECHA DE VALORACIÓN',
                'TIPO DE GASTO', 'MONTO DEL GASTO', 'NOMBRE PROVEEDOR', 'NIVEL DE ATENCIÓN',
                'FORMA DE OCURRENCIA', 'AGENTE MATERIAL', 'MECANISMO DE TRAUMA', 'REQUIRIÓ AYUDA DE TERCEROS?',
                'MODALIDAD DE TRABAJO', 'CAUSA EXTERNA', 'RECIBIÓ ATENCIÓN EN UN CENTRO DE SALUD DIFERENTE?',
                'DIAGNÓSTICO', 'PARTES DEL CUERPO LESIONADAS', 'PROVINCIA', 'DISTRITO', 'CANTÓN'
            ];

            return Excel::create('reporte_siniestro_primer_nivel', function ($excel) use ($activities, $headings) {
                $excel->sheet('Entries', function ($sheet) use ($activities, $headings) {
                    $sheet->row(1, $headings);
                    $sheet->fromArray($activities, null, 'A2', false, false);
                });
            })->download('xlsx');

        } catch (\Exception $e) {
            return redirect('/admin/reportes/descargas_admin')
                ->withErrors($e->getMessage())
                ->withInput();
        }
    }
}
