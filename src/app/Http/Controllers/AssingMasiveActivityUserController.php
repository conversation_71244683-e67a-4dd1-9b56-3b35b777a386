<?php

namespace App\Http\Controllers;

use App\Action;
use App\Activity;
use App\Client;
use App\User;
use Auth;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Storage;

class AssingMasiveActivityUserController extends Controller
{

    public function stateChangeView(Request $req, $cpath)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $users = User::with('clients')->get();
        $users = $users->filter(function ($obj) use ($client) {
            foreach ($obj->clients as $c) {
                if ($c->id == $client->id) {
                    return true;
                }
            }
            return false;
        });
        return view('assing_masive_activity_user', [
            'users' => $users
        ]);
    }

    public function save(Request $request, $cpath)
    {
        try {
            $client = Client::where('path', $cpath)->firstOrFail();
            $id_activities = explode("\n", $request->id_activities);
            $id_activities = array_map('trim', $id_activities);
            $user_id = $request->user_id;
            $user = User::find($user_id);
            if ($request->id_activities == '') {
                throw new \Exception('No se ingreso ninguna id de actividad');
            }
            if ($user == null) {
                throw new \Exception('No se seleccionó ningun usuario');
            }
            DB::beginTransaction();
            foreach ($id_activities as $id_activity) {
                $activity = Activity::where('client_id', $client->id)
                                    ->where('id', $id_activity)
                                    ->first();
                $old_user_name = User::query()->find($activity->user_id)->full_name;
                if (!$activity) {
                    throw new \Exception('La actividad ' . $id_activity . ' no existe');
                }
                $activityAction  = new \App\ActivityAction;
                $activityAction->activity_id = $activity->id;
                if ($activity->service_id == 57 || $activity->service_id == 64) {
                    $activityAction->action_id = Action::FOLLOW_PCL;
                } else {
                    $activityAction->action_id = Action::FOLLOW;
                }
                $activityAction->old_user_id = $activity->user_id;
                $activityAction->new_user_id = $user_id;
                $activityAction->old_state_id = $activity->state_id;
                $activityAction->new_state_id = $activity->state_id;
                $activityAction->author_id = Auth::user()->id;
                $activityAction->description = 'Se realiza asignación de caso masiva del usuario <b>' . $old_user_name . '</b> al usuario <b>' . $user->name.'</b>';
                $activityAction->save();

                $activity->user_id = $user_id;
                $activity->save();
            }
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()->with('error', $e->getMessage())->withInput();
        }
        return redirect()->back()->with('success', 'Las actividades han sido asignadas al usuario ' . $user->name);
    }
}
