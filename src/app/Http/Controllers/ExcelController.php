<?php

namespace App\Http\Controllers;

use App\Action;
use App\Client;
use App\Http\Controllers\Integrations\WebserviceAcselController;
use App\Mail\SendDocumentDataBase;
use App\Service;
use App\State;
use App\States\StateGis;
use App\States\StatePoliza;
use App\States\StateRenewal;
use App\User;
use Auth;
use Illuminate\Http\Request;
use App\Providers\AppServiceProvider;
use App\Report;
use Carbon\Carbon;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Storage;
use PhpParser\Node\Stmt\DeclareDeclare;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Illuminate\Support\Facades\DB;
use Excel;

class ExcelController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function reportAudit(Request $req, $cpath)
    {
        ini_set('max_execution_time', 300);
        ini_set('memory_limit', '512M');
        $client = Client::where('path', $cpath)->firstOrFail();

        $activities = [];
        DB::table('audit_radication')->orderBy('FECHA_RECEPCION_IT', 'ASC')->chunk(1000, function ($items) use (&$activities) {

            foreach ($items as $item) {

                $item->EDAD = Carbon::now()->diffInYears(Carbon::parse($item->FECHA_NACIMIENTO));
                $item->ESTADO_CIVIL = AppServiceProvider::$CIVIL_STATUS[$item->ESTADO_CIVIL];
                $item->NIVEL_ESCOLAR = AppServiceProvider::$SCHOOL_LEVELS[$item->NIVEL_ESCOLAR];

                $item->EPS = \App\EPS::getNames()[$item->EPS];
                $item->ARL = \App\ARL::getNames()[$item->ARL];
                $item->AFP = \App\AFP::getNames()[$item->AFP];

                $item->TIPO_INCAPACIDAD = $item->TIPO_INCAPACIDAD == "1" ? 'PRORROGA' : 'NUEVA';
                $item->ORIGEN = $item->ORIGEN ? AppServiceProvider::$RADICATION_TYPE[$item->ORIGEN] : '';

                if (isset(AppServiceProvider::$SPECIALITY[$item->ESPECIALIDAD])) {
                    $item->ESPECIALIDAD = AppServiceProvider::$SPECIALITY[$item->ESPECIALIDAD];
                }
                if ($item->REQUIERE_INGRESO_PROGRAMA_PRI != null) {
                    $item->REQUIERE_INGRESO_PROGRAMA_PRI = $item->REQUIERE_INGRESO_PROGRAMA_PRI == 1 ? 'Si' : 'No';
                }
                /*
                $item->RESULTADO_AUDITORIA = $item->RESULTADO_AUDITORIA ? AppServiceProvider::$IT_RESULT_INABILITY[$item->RESULTADO_AUDITORIA] : '';
                $item->MOTIVO = $item->MOTIVO ? AppServiceProvider::$IT_REASON_INABILITY[$item->MOTIVO] : '';
                $item->DETALLE_RESULTADO = $item->DETALLE_RESULTADO ? AppServiceProvider::$QUALIFICATION_REASON_AUDIT[$item->DETALLE_RESULTADO] : '';
                */
                //Write excel

                if ($item->REQUERIMIENTO_JUDICIAL_EPS != null) {
                    $item->REQUERIMIENTO_JUDICIAL_EPS = $item->REQUERIMIENTO_JUDICIAL_EPS == 1 ? 'Si' : 'No';
                }

                $item->CAMBIO_ORIGEN_EPS = $item->CAMBIO_ORIGEN_EPS ? AppServiceProvider::$RADICATION_TYPE[$item->CAMBIO_ORIGEN_EPS] : '';

                if ($item->CAMBIO_TIPO_IT_EPS == "1") {
                    $item->CAMBIO_TIPO_IT_EPS = "PRORROGA";
                }
                if ($item->CAMBIO_TIPO_IT_EPS == "0") {
                    $item->CAMBIO_TIPO_IT_EPS = "NUEVA";
                }

                if (in_array($item->TIPO_PRESTADOR, ['1', '2', '3', '4', '00000'])) {
                    $item->TIPO_PRESTADOR = $item->TIPO_PRESTADOR ? AppServiceProvider::$TYPE_IPS[$item->TIPO_PRESTADOR] : '';
                }
                if (in_array($item->TIPO_IPS_EPS, ['1', '2', '3', '4', '00000'])) {
                    $item->TIPO_IPS_EPS = $item->TIPO_IPS_EPS ? AppServiceProvider::$TYPE_IPS[$item->TIPO_IPS_EPS] : '';
                }

                if ($item->TIPO_INCAPACIDAD_EPS != null) {
                    $item->TIPO_INCAPACIDAD_EPS = $item->TIPO_INCAPACIDAD_EPS ? AppServiceProvider::$INABILITY_ORIGIN[$item->TIPO_INCAPACIDAD_EPS] : '';
                }

                if ($item->TIPO_DE_ATENCION == '1') {
                    $item->TIPO_DE_ATENCION = 'Ambulatoria';
                }
                if ($item->TIPO_DE_ATENCION == '2') {
                    $item->TIPO_DE_ATENCION = 'Hospitalaria';
                }

                $activities[] = [
                    $item->FECHA_AUDITORIA,
                    $item->ID_SERVICIO,
                    $item->TIPO_DOCUMENTO,
                    $item->NRO_IDENTIFICACION,
                    $item->NOMBRE,
                    $item->FECHA_NACIMIENTO,
                    $item->EDAD,
                    $item->GENERO,
                    $item->ESTADO_CIVIL,
                    $item->NIVEL_ESCOLAR,
                    $item->EPS,
                    $item->AFP,
                    $item->ARL,
                    $item->NOMBRE_EMPRESA,
                    $item->NIT_EMPRESA,
                    $item->CARGO,
                    $item->FECHA_SERVICIO,
                    $item->NOMBRE_ESTADO_SERVICIO,
                    $item->NRO_INCAPACIDAD,
                    $item->TIPO_INCAPACIDAD,
                    $item->ORIGEN,
                    $item->TIPO_DE_ATENCION,
                    $item->FECHA_INICIAL,
                    $item->FECHA_FINAL,
                    $item->DIAS_IT,
                    $item->DIAS_ACUMULADOS,
                    $item->CODIGO_IPS,
                    $item->IPS_EXPIDE,
                    $item->TIPO_PRESTADOR,
                    $item->MEDICO_TIPO_DOC,
                    $item->MEDICO_NUMERO_DOC,
                    $item->MEDICO_NOMBRE,
                    $item->REGISTRO_MEDICO,
                    $item->ESPECIALIDAD,
                    $item->REGIONAL,
                    $item->CIE10_1,
                    $item->NOMBRE_DIAGNOSTICO_1,
                    $item->CIE10_2,
                    $item->NOMBRE_DIAGNOSTICO_2,
                    $item->IBC,
                    $item->RESULTADO_AUDITORIA,
                    $item->MOTIVO,
                    $item->DETALLE_RESULTADO,
                    $item->DIAS_REALES_ACUMULADOS,
                    $item->REQUERIMIENTO_JUDICIAL,
                    $item->OBSERVACION,
                    $item->REQUIERE_INGRESO_PROGRAMA_PRI,
                    $item->DIAS_APROBADOS_PARA_LIQUIDACION,
                    $item->FUNCIONARIO_ACTUAL,
                    $item->FECHA_RADICADO,
                    $item->MOTIVO_DE_LA_INCAPACIDAD,
                    $item->LINK_CARTA,
                    $item->FECHA_RECEPCION_IT,
                    $item->NUMERO_RADICADO,

                    $item->TIPO_IPS_EPS,
                    $item->TIPO_INCAPACIDAD_EPS,
                    $item->RESULTADO_AUDITORIA_EPS,
                    $item->MOTIVO_EPS,
                    $item->DETALLE_RESULTADO_EPS,
                    $item->CIE10_1_EPS,
                    $item->NOMBRE1_EPS,
                    $item->DESCRIPCION1_EPS,
                    $item->LATERALIDAD1_EPS,
                    $item->CIE10_2_EPS,
                    $item->NOMBRE2_EPS,
                    $item->DESCRIPCION2_EPS,
                    $item->LATERALIDAD2_EPS,
                    $item->CIE10_3_EPS,
                    $item->NOMBRE3_EPS,
                    $item->DESCRIPCION3_EPS,
                    $item->LATERALIDAD3_EPS,
                    $item->DIAS_REALES_ACUMULADOS_EPS,
                    $item->CAMBIO_TIPO_IT_EPS,
                    $item->CAMBIO_ORIGEN_EPS,
                    $item->REQUERIMIENTO_JUDICIAL_EPS,
                    $item->DESCRIPCION_EPS
                ];
            }

        });

        $fexcel = Excel::create("DeterminationIt", function ($excel) use ($activities) {

            $excel->sheet('HOJA 1', function ($sheet) use ($activities) {
                $sheet->setColumnFormat([
                    'A' => \PHPExcel_Style_NumberFormat::FORMAT_DATE_YYYYMMDD2,
                ]);
                $headings = array(
                    'FECHA_AUDITORIA',
                    'ID_SERVICIO',
                    'TIPO_DOCUMENTO',
                    'NRO_IDENTIFICACION',
                    'NOMBRE',
                    'FECHA_NACIMIENTO',
                    'EDAD',
                    'GENERO',
                    'ESTADO_CIVIL',
                    'NIVEL_ESCOLAR',
                    'EPS',
                    'AFP',
                    'ARL',
                    'NOMBRE_EMPRESA',
                    'NIT_EMPRESA',
                    'CARGO',
                    'FECHA_SERVICIO',
                    'NOMBRE_ESTADO_SERVICIO',
                    'NRO_INCAPACIDAD',
                    'TIPO_INCAPACIDAD',
                    'ORIGEN',
                    'TIPO_DE_ATENCION',
                    'FECHA_INICIAL',
                    'FECHA_FINAL',
                    'DIAS_IT',
                    'DIAS_ACUMULADOS',
                    'CODIGO_IPS',
                    'IPS_EXPIDE',
                    'TIPO_PRESTADOR',
                    'MEDICO_TIPO_DOC',
                    'MEDICO_NUMERO_DOC',
                    'MEDICO_NOMBRE',
                    'REGISTRO_MEDICO',
                    'ESPECIALIDAD',
                    'REGIONAL',
                    'CIE10_1',
                    'NOMBRE_DIAGNOSTICO_1',
                    'CIE10_2',
                    'NOMBRE_DIAGNOSTICO_2',
                    'IBC',
                    'RESULTADO_AUDITORIA',
                    'MOTIVO',
                    'DETALLE_RESULTADO',
                    'DIAS_REALES_ACUMULADOS',
                    'REQUERIMIENTO_JUDICIAL',
                    'OBSERVACION',
                    'REQUIERE_INGRESO_PROGRAMA_PRI',
                    'DIAS_APROBADOS_PARA_LIQUIDACION',
                    'FUNCIONARIO_ACTUAL',
                    'FECHA_RADICADO',
                    'MOTIVO_DE_LA_INCAPACIDAD',
                    'LINK_CARTA',
                    'FECHA_RECEPCION_IT',
                    'NUMERO_RADICADO',

                    'TIPO_IPS_EPS',
                    'TIPO_INCAPACIDAD_EPS',
                    'RESULTADO_AUDITORIA_EPS',
                    'MOTIVO_EPS',
                    'DETALLE_RESULTADO_EPS',
                    'CIE10_1_EPS',
                    'NOMBRE1_EPS',
                    'DESCRIPCION1_EPS',
                    'LATERALIDAD1_EPS',
                    'CIE10_2_EPS',
                    'NOMBRE2_EPS',
                    'DESCRIPCION2_EPS',
                    'LATERALIDAD2_EPS',
                    'CIE10_3_EPS',
                    'NOMBRE3_EPS',
                    'DESCRIPCION3_EPS',
                    'LATERALIDAD3_EPS',
                    'DIAS_REALES_ACUMULADOS_EPS',
                    'CAMBIO_TIPO_IT_EPS',
                    'CAMBIO_ORIGEN_EPS',
                    'REQUERIMIENTO_JUDICIAL_EPS',
                    'DESCRIPCION_EPS'
                );
                $sheet->prependRow(1, $headings);

                $sheet->fromArray($activities, null, 'A2', false, false);

                $sheet->freezeFirstRow();
                $sheet->setAutoFilter();
                $sheet->row(1, function ($row) {
                    $row->setBackground('#7EBE2C');
                    $row->setFontColor('#FFFFFF');
                    $row->setFontWeight('bold');
                });
                $sheet->setAutoSize(true);

            });
            $excel->setActiveSheetIndex(0);
        })->store('xlsx', false, true);
        //->download('xlsx');//->store('xlsx', false, true);
        $zipper = new \Chumper\Zipper\Zipper;

        $name_path = "Audit_" . time() . ".zip";

        $zipper->make(storage_path($name_path));

        $zipper->add($fexcel['full']);

        $zipper->close();

        unlink($fexcel['full']);

        return response()->download(storage_path($name_path))->deleteFileAfterSend(true);

        //return response()->download(storage_path("DeterminationIt.xlsx"))->deleteFileAfterSend(true);
        //return $response;
    }

    public function reportAdminActions(Request $req, $cpath)
    {
        $client = Client::where('path', $cpath)->firstOrFail();

        $columns = array_map(function ($column) {
            return $column->Field;
        }, DB::select(DB::raw('SHOW COLUMNS FROM actions_report')));

        $response = new StreamedResponse(function () use ($columns, $client) {

            $out = fopen('php://output', 'w');

            fputcsv($out, $columns, "\t");

            DB::table("actions_report")->orderBy('ID_SERVICIO', 'ASC')->chunk(1000, function ($items) use ($out, $columns) {

                foreach ($items as $item) {

                    if (isset($item->EPS)) {
                        $item->EPS = \App\EPS::getNames()[$item->EPS];
                    }
                    if (isset($item->ARL)) {
                        $item->ARL = \App\ARL::getNames()[$item->ARL];
                    }
                    if (isset($item->AFP)) {
                        $item->AFP = \App\AFP::getNames()[$item->AFP];
                    }

                    $data = [];
                    for ($i = 0; $i < count($columns); $i++) {
                        $data[] = mb_convert_encoding($item->{$columns[$i]}, 'utf-16', 'utf-8');
                    }

                    //Write excel
                    fputcsv($out, $data, "\t");
                }
            });

            fclose($out);

        }, 200, [
            'Content-Encoding' => 'UTF-8',
            'Content-Type' => 'application/vnd.ms-excel; charset=utf-8',
            'Content-Disposition' => 'attachment; filename="Actions.xls"',
        ]);

        return $response;
    }

    public function reportStates(Request $req, $cpath)
    {
        $client = Client::where('path', $cpath)->firstOrFail();

        $activities = [];
        DB::table('states_report')->orderBy('ID_SERVICIO', 'ASC')->chunk(5000, function ($items) use (&$activities) {

            foreach ($items as $item) {

                $item->EPS = \App\EPS::getNames()[$item->EPS];

                $activities[] = [
                    $item->NOMBRE_TIPO_SERVICIO,
                    $item->ID_SERVICIO,
                    $item->ID_TIPO_DOC,
                    $item->IDENTIFICACION,
                    $item->NOMBRE,
                    $item->NOMBRE_EMPRESA,
                    $item->NIT_EMPRESA,
                    $item->DIRECCION,
                    $item->TELEFONO,
                    $item->FUNCIONARIO_ACTUAL,
                    $item->FECHA_ALERTA,
                    $item->FECHA_REGISTRO,
                    $item->NOMBRE_ESTADO_SERVICIO,
                    $item->EPS,
                    $item->CELULAR,
                    $item->EMAIL,
                    $item->SEXO,
                    $item->NOMBRE_MUNICIPIO,
                    $item->NOMBRE_DEPTO,
                ];
            }
        });

        $fexcel = Excel::create("States", function ($excel) use ($activities) {

            $excel->sheet('HOJA 1', function ($sheet) use ($activities) {

                $headings = array(
                    'NOMBRE_TIPO_SERVICIO',
                    'ID_SERVICIO',
                    'ID_TIPO_DOC',
                    'IDENTIFICACION',
                    'NOMBRE',
                    'NOMBRE_EMPRESA',
                    'NIT_EMPRESA',
                    'DIRECCION',
                    'TELEFONO',
                    'FUNCIONARIO_ACTUAL',
                    'FECHA_ALERTA',
                    'FECHA_REGISTRO',
                    'NOMBRE_ESTADO_SERVICIO',
                    'EPS',
                    'CELULAR',
                    'EMAIL',
                    'SEXO',
                    'NOMBRE_MUNICIPIO',
                    'NOMBRE_DEPTO'
                );
                $sheet->prependRow(1, $headings);

                $sheet->fromArray($activities, null, 'A2', false, false);
                $sheet->freezeFirstRow();
                $sheet->setAutoFilter();
                $sheet->row(1, function ($row) {
                    $row->setBackground('#7EBE2C');
                    $row->setFontColor('#FFFFFF');
                    $row->setFontWeight('bold');
                });
                $sheet->setAutoSize(true);

            });
            $excel->setActiveSheetIndex(0);
        })->download('xlsx');//->store('xlsx', false, true);

    }

    public function reportAdminStates(Request $req, $cpath)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $columns = array_map(function ($column) {
            return $column->Field;
        }, DB::select(DB::raw('SHOW COLUMNS FROM states_report_udea')));
        unset($columns[0]);
        $response = new StreamedResponse(function () use ($columns) {

            $out = fopen('php://output', 'w');

            fputcsv($out, $columns, "\t");

            DB::table("states_report_udea")->orderBy('ID_SERVICIO', 'ASC')->chunk(1000, function ($items) use ($out, $columns) {

                foreach ($items as $item) {
                    if (isset($item->EPS)) {
                        $item->EPS = \App\EPS::getNames()[$item->EPS];
                    }
                    if (isset($item->ARL)) {
                        $item->ARL = \App\ARL::getNames()[$item->ARL];
                    }
                    if (isset($item->AFP)) {
                        $item->AFP = \App\AFP::getNames()[$item->AFP];
                    }

                    $data = [];
                    for ($i = 1; $i < count($columns); $i++) {
                        $data[] = mb_convert_encoding($item->{$columns[$i]}, 'utf-16', 'utf-8');
                    }

                    //Write excel
                    fputcsv($out, $data, "\t");
                }
            });

            fclose($out);

        }, 200, [
            'Content-Encoding' => 'UTF-8',
            'Content-Type' => 'application/vnd.ms-excel; charset=utf-8',
            'Content-Disposition' => 'attachment; filename="States.xls"',
        ]);

        return $response;
    }

    public function reportAdminStatesDelimited(Request $req, $cpath)
    {
        try {
            $initDate = $req->state_start_date_submit;
            $lastDate = $req->state_end_date_submit;
            $serviceId = $req->service_id;
            $stateId = $req->state_id;

            // Validar que las fechas no sean nulas
            if (empty($initDate) || empty($lastDate)) {
                throw new \Exception("Las fechas no pueden estar vacías, en la sección 'Reporte de estados'");
            }

            // Validar que $lastDate no sea menor que $initDate
            if (strtotime($lastDate) < strtotime($initDate)) {
                throw new \Exception("La fecha desde no puede ser menor a la fecha hasta, en la sección 'Reporte de estados'");
            }

            $columns = array_map(function ($column) {
                return $column->Field;
            }, DB::select(DB::raw('SHOW COLUMNS FROM states_report')));

            $response = new StreamedResponse(function () use ($columns, $initDate, $lastDate, $serviceId, $stateId) {

                $out = fopen('php://output', 'w');

                fputcsv($out, $columns, "\t");

                $connection = DB::connection()->table("states_report");

                if ($initDate) {
                    $connection->where('FECHA_REGISTRO', '>=', $initDate . ' 00:00:00');
                }
                if ($lastDate) {
                    $connection->where('FECHA_REGISTRO', '<=', $lastDate . ' 23:59:59');
                }
                if ($serviceId) {
                    $nameServices = Service::whereIn('id', explode(',', $serviceId))->select('name')->get()->toArray();
                    $connection->whereIn('NOMBRE_TIPO_SERVICIO', $nameServices);
                }
                if ($stateId) {
                    $nameStates = State::whereIn('id', explode(',', $stateId))->select('name')->get()->toArray();
                    $connection->whereIn('NOMBRE_ESTADO_SERVICIO', $nameStates);
                }

                $connection->orderBy('ID_SERVICIO', 'ASC')->chunk(5000, function ($items) use ($out, $columns) {

                    foreach ($items as $item) {

                        if (isset($item->EPS)) {
                            $item->EPS = \App\EPS::getNames()[$item->EPS];
                        }
                        if (isset($item->ARL)) {
                            $item->ARL = \App\ARL::getNames()[$item->ARL];
                        }
                        if (isset($item->AFP)) {
                            $item->AFP = \App\AFP::getNames()[$item->AFP];
                        }

                        $data = [];
                        for ($i = 0; $i < count($columns); $i++) {
                            $data[] = mb_convert_encoding($item->{$columns[$i]}, 'utf-16', 'utf-8');
                        }
                        //Write excel
                        fputcsv($out, $data, "\t");
                    }
                });

                fclose($out);

            }, 200, [
                'Content-Encoding' => 'UTF-8',
                'Content-Type' => 'application/vnd.ms-excel; charset=utf-8',
                'Content-Disposition' => 'attachment; filename="States' . '_' . date('Y-m-d') . '.xls"',
            ]);

            return $response;
        } catch (\Exception $e) {
            // Capturar el error y redirigir con mensaje
            return redirect('/admin/reportes/descargas_admin')
                ->withErrors($e->getMessage())
                ->withInput();
        }
    }

    public function reportAdminStatesItDelimited(Request $req, $cpath)
    {
        $initDate = $req->state_it_start_date_submit;
        $lastDate = $req->state_it_end_date_submit;
        $stateId = $req->state_it_id;
        $columns = array_map(function ($column) {
            return $column->Field;
        }, DB::select(DB::raw('SHOW COLUMNS FROM states_report_it')));

        $response = new StreamedResponse(function () use ($columns, $initDate, $lastDate, $stateId) {

            $out = fopen('php://output', 'w');

            fputcsv($out, $columns, "\t");

            $connection = DB::connection()->table("states_report_it");

            if ($initDate) {
                $connection->where('FECHA_REGISTRO', '>=', $initDate . ' 00:00:00');
            }
            if ($lastDate) {
                $connection->where('FECHA_REGISTRO', '<=', $lastDate . ' 23:59:59');
            }
            if ($stateId) {
                $nameStates = State::whereIn('id', explode(',', $stateId))->select('name')->get()->toArray();
                $connection->whereIn('NOMBRE_ESTADO_SERVICIO', $nameStates);
            }

            $connection->orderBy('ID_SERVICIO', 'ASC')->chunk(5000, function ($items) use ($out, $columns) {

                foreach ($items as $item) {

                    if (isset($item->EPS)) {
                        $item->EPS = \App\EPS::getNames()[$item->EPS];
                    }
                    if (isset($item->ARL)) {
                        $item->ARL = \App\ARL::getNames()[$item->ARL];
                    }
                    if (isset($item->AFP)) {
                        $item->AFP = \App\AFP::getNames()[$item->AFP];
                    }

                    $data = [];
                    for ($i = 0; $i < count($columns); $i++) {
                        $data[] = mb_convert_encoding($item->{$columns[$i]}, 'utf-16', 'utf-8');
                    }
                    //Write excel
                    fputcsv($out, $data, "\t");
                }
            });

            fclose($out);

        }, 200, [
            'Content-Encoding' => 'UTF-8',
            'Content-Type' => 'application/vnd.ms-excel; charset=utf-8',
            'Content-Disposition' => 'attachment; filename="States_IT_' . date('Y-m-d') . '.xls"',
        ]);

        return $response;
    }

    public function reportAdminAutorizadosSiniestro(Request $req, $cpath)
    {


        try {

            $activities = [];

            $consulta = " SELECT *
                            FROM authorized_claim_report";

            $query = DB::select($consulta);

            foreach ($query as $item) {
                $activities[] = [
                    // Datos principales de la póliza y afiliado
                    $item->poliza_id,
                    $item->tipo_doc,
                    $item->doc_number,
                    $item->full_name,
                    $item->unique_code,
                    $item->modalidad_aseguramiento,

                    // Responsable 1
                    $item->nombre_responsable_1,
                    $item->tipo_identificacion_responsable_1,
                    $item->identificacion_responsable_1,
                    $item->codigo_unico_responsable_1,

                    // Responsable 2
                    $item->nombre_responsable_2,
                    $item->tipo_identificacion_responsable_2,
                    $item->identificacion_responsable_2,
                    $item->codigo_unico_responsable_2,

                    // Responsable 3
                    $item->nombre_responsable_3,
                    $item->tipo_identificacion_responsable_3,
                    $item->identificacion_responsable_3,
                    $item->codigo_unico_responsable_3,

                    // Responsable 4
                    $item->nombre_responsable_4,
                    $item->tipo_identificacion_responsable_4,
                    $item->identificacion_responsable_4,
                    $item->codigo_unico_responsable_4,

                    // Responsable 5
                    $item->nombre_responsable_5,
                    $item->tipo_identificacion_responsable_5,
                    $item->identificacion_responsable_5,
                    $item->codigo_unico_responsable_5,

                    // Responsable 6
                    $item->nombre_responsable_6,
                    $item->tipo_identificacion_responsable_6,
                    $item->identificacion_responsable_6,
                    $item->codigo_unico_responsable_6,

                    // Responsable 7
                    $item->nombre_responsable_7,
                    $item->tipo_identificacion_responsable_7,
                    $item->identificacion_responsable_7,
                    $item->codigo_unico_responsable_7,

                    // Responsable 8
                    $item->nombre_responsable_8,
                    $item->tipo_identificacion_responsable_8,
                    $item->identificacion_responsable_8,
                    $item->codigo_unico_responsable_8,

                    // Responsable 9
                    $item->nombre_responsable_9,
                    $item->tipo_identificacion_responsable_9,
                    $item->identificacion_responsable_9,
                    $item->codigo_unico_responsable_9,

                    // Responsable 10
                    $item->nombre_responsable_10,
                    $item->tipo_identificacion_responsable_10,
                    $item->identificacion_responsable_10,
                    $item->codigo_unico_responsable_10,
                ];
            }


            $headings = [
                'Póliza',                    // poliza_id
                'Tipo de ID',                // tipo_doc
                'Identificación',            // doc_number
                'Nombre',                    // full_name
                'CóDIMEXgo',                 // unique_code
                'Modalidad',                 // modalidad_aseguramiento

                // Primera Persona Autorizada (Responsable 1)
                'Primera Persona Autorizada',           // nombre_responsable_1
                'Tipo de ID Responsable 1',             // tipo_identificacion_responsable_1
                'Identificación Responsable 1',         // identificacion_responsable_1
                'Código único Responsable 1',           // codigo_unico_responsable_1

                // Segunda Persona Autorizada (Responsable 2)
                'Segunda Persona Autorizada',           // nombre_responsable_2
                'Tipo de ID Responsable 2',             // tipo_identificacion_responsable_2
                'Identificación Responsable 2',         // identificacion_responsable_2
                'Código único Responsable 2',           // codigo_unico_responsable_2

                // Tercera Persona Autorizada (Responsable 3)
                'Tercera Persona Autorizada',           // nombre_responsable_3
                'Tipo de ID Responsable 3',             // tipo_identificacion_responsable_3
                'Identificación Responsable 3',         // identificacion_responsable_3
                'Código único Responsable 3',           // codigo_unico_responsable_3

                // Cuarta Persona Autorizada (Responsable 4)
                'Cuarta Persona Autorizada',            // nombre_responsable_4
                'Tipo de ID Responsable 4',             // tipo_identificacion_responsable_4
                'Identificación Responsable 4',         // identificacion_responsable_4
                'Código único Responsable 4',           // codigo_unico_responsable_4

                // Quinta Persona Autorizada (Responsable 5)
                'Quinta Persona Autorizada',            // nombre_responsable_5
                'Tipo de ID Responsable 5',             // tipo_identificacion_responsable_5
                'Identificación Responsable 5',         // identificacion_responsable_5
                'Código único Responsable 5',           // codigo_unico_responsable_5

                // Sexta Persona Autorizada (Responsable 6)
                'Sexta Persona Autorizada',             // nombre_responsable_6
                'Tipo de ID Responsable 6',             // tipo_identificacion_responsable_6
                'Identificación Responsable 6',         // identificacion_responsable_6
                'Código único Responsable 6',           // codigo_unico_responsable_6

                // Séptima Persona Autorizada (Responsable 7)
                'Séptima Persona Autorizada',           // nombre_responsable_7
                'Tipo de ID Responsable 7',             // tipo_identificacion_responsable_7
                'Identificación Responsable 7',         // identificacion_responsable_7
                'Código único Responsable 7',           // codigo_unico_responsable_7

                // Octava Persona Autorizada (Responsable 8)
                'Octava Persona Autorizada',            // nombre_responsable_8
                'Tipo de ID Responsable 8',             // tipo_identificacion_responsable_8
                'Identificación Responsable 8',         // identificacion_responsable_8
                'Código único Responsable 8',           // codigo_unico_responsable_8

                // Novena Persona Autorizada (Responsable 9)
                'Novena Persona Autorizada',            // nombre_responsable_9
                'Tipo de ID Responsable 9',             // tipo_identificacion_responsable_9
                'Identificación Responsable 9',         // identificacion_responsable_9
                'Código único Responsable 9',           // codigo_unico_responsable_9

                // Décima Persona Autorizada (Responsable 10)
                'Décima Persona Autorizada',            // nombre_responsable_10
                'Tipo de ID Responsable 10',            // tipo_identificacion_responsable_10
                'Identificación Responsable 10',        // identificacion_responsable_10
                'Código único Responsable 10'           // codigo_unico_responsable_10
            ];


            return Excel::create('Reporte_responsable_accidentes', function ($excel) use ($activities, $headings) {
                $excel->sheet('Entries', function ($sheet) use ($activities, $headings) {
                    $sheet->row(1, $headings);
                    $sheet->fromArray($activities, null, 'A2', false, false);
                });
            })->download('xlsx');
        } catch (\Exception $e) {
            // Capturar el error y redirigir con mensaje
            return redirect('/admin/reportes/descargas_admin')
                ->withErrors($e->getMessage())
                ->withInput();
        }
    }

    public function reportAdminStatesMdiDelimited(Request $req, $cpath)
    {
        $initDate = $req->state_mdi_start_date_submit;
        $lastDate = $req->state_mdi_end_date_submit;
        $stateId = $req->state_mdi_id;
        $columns = array_map(function ($column) {
            return $column->Field;
        }, DB::select(DB::raw('SHOW COLUMNS FROM states_report_mdi')));

        $response = new StreamedResponse(function () use ($columns, $initDate, $lastDate, $stateId) {

            $out = fopen('php://output', 'w');

            fputcsv($out, $columns, "\t");

            $connection = DB::connection()->table("states_report_mdi");

            if ($initDate) {
                $connection->where('FECHA_REGISTRO', '>=', $initDate . ' 00:00:00');
            }
            if ($lastDate) {
                $connection->where('FECHA_REGISTRO', '<=', $lastDate . ' 23:59:59');
            }
            if ($stateId) {
                $nameStates = State::whereIn('id', explode(',', $stateId))->select('name')->get()->toArray();
                $connection->whereIn('NOMBRE_ESTADO_SERVICIO', $nameStates);
            }

            $connection->orderBy('ID_SERVICIO', 'ASC')->chunk(5000, function ($items) use ($out, $columns) {

                foreach ($items as $item) {

                    if (isset($item->EPS)) {
                        $item->EPS = \App\EPS::getNames()[$item->EPS];
                    }
                    if (isset($item->ARL)) {
                        $item->ARL = \App\ARL::getNames()[$item->ARL];
                    }
                    if (isset($item->AFP)) {
                        $item->AFP = \App\AFP::getNames()[$item->AFP];
                    }

                    $data = [];
                    for ($i = 0; $i < count($columns); $i++) {
                        $data[] = mb_convert_encoding($item->{$columns[$i]}, 'utf-16', 'utf-8');
                    }
                    //Write excel
                    fputcsv($out, $data, "\t");
                }
            });

            fclose($out);

        }, 200, [
            'Content-Encoding' => 'UTF-8',
            'Content-Type' => 'application/vnd.ms-excel; charset=utf-8',
            'Content-Disposition' => 'attachment; filename="States_MDI_' . date('Y-m-d') . '.xls"',
        ]);

        return $response;
    }

    public function reportAdminPaymentBasesStatesDelimited(Request $req, $cpath)
    {
        $initDate = $req->state_pb_start_date_submit;
        $lastDate = $req->state_pb_end_date_submit;
        $columns = array_map(function ($column) {
            return $column->Field;
        }, DB::select(DB::raw('SHOW COLUMNS FROM states_payment_bases_report')));

        $response = new StreamedResponse(function () use ($columns, $initDate, $lastDate) {

            $out = fopen('php://output', 'w');

            fputcsv($out, $columns, "\t");

            $connection = DB::connection()->table("states_payment_bases_report");

            if ($initDate) {
                $connection->where('FECHA_REGISTRO', '>=', $initDate . ' 00:00:00');
            }
            if ($lastDate) {
                $connection->where('FECHA_REGISTRO', '<=', $lastDate . ' 23:59:59');
            }

            $connection->orderBy('ID_SERVICIO', 'ASC')->orderBy('FECHA_INICIO_IT', 'ASC')->chunk(5000, function ($items) use ($out, $columns) {

                foreach ($items as $item) {

                    if (isset($item->EPS)) {
                        $item->EPS = \App\EPS::getNames()[$item->EPS];
                    }
                    if (isset($item->ARL)) {
                        $item->ARL = \App\ARL::getNames()[$item->ARL];
                    }
                    if (isset($item->AFP)) {
                        $item->AFP = \App\AFP::getNames()[$item->AFP];
                    }

                    $dateFields = ['FECHA_REGISTRO', 'FECHA_DE_ESTADO', 'FECHA_INICIO_IT'];
                    foreach ($dateFields as $field) {
                        if (isset($item->{$field})) {
                            $item->{$field} = \Carbon\Carbon::parse($item->{$field})->format('d/m/Y');
                        }
                    }

                    if (isset($item->NO_OFICIO_DE_PAGO) && strlen($item->NO_OFICIO_DE_PAGO) <= 5) {
                        $item->NO_OFICIO_DE_PAGO = "DML-I " . str_pad($item->NO_OFICIO_DE_PAGO, 5, "0", STR_PAD_LEFT);
                    }

                    $data = [];
                    for ($i = 0; $i < count($columns); $i++) {
                        $data[] = mb_convert_encoding($item->{$columns[$i]}, 'utf-16', 'utf-8');
                    }
                    //Write excel
                    fputcsv($out, $data, "\t");
                }
            });

            fclose($out);

        }, 200, [
            'Content-Encoding' => 'UTF-8',
            'Content-Type' => 'application/vnd.ms-excel; charset=utf-8',
            'Content-Disposition' => 'attachment; filename="Estados_Base_de_Pagos' . '_' . date('Y-m-d') . '.xls"',
        ]);

        return $response;
    }

    public function reportDeterminationItDelimited(Request $req, $cpath)
    {
        $initDate = $req->it_start_date_submit;
        $lastDate = $req->it_end_date_submit;

        $client = Client::where('path', $cpath)->firstOrFail();
        $client_name = str_replace("-", "_", $client->name);
        $client_name = strtolower($client_name);

        $view = "{$client_name}_determination_it";
        $columns = array_map(function ($column) {
            return $column->Field;
        }, DB::select(DB::raw('SHOW COLUMNS FROM ' . $view)));
        $response = new StreamedResponse(function () use ($columns, $initDate, $lastDate, $client_name) {

            $out = fopen('php://output', 'w');

            fputcsv($out, $columns, "\t");

            $connection = DB::connection()->table("{$client_name}_determination_it");

            if ($initDate) {
                $connection->where('FECHA_REGISTRO', '>=', $initDate . ' 00:00:00');
            }
            if ($lastDate) {
                $connection->where('FECHA_REGISTRO', '<=', $lastDate . ' 23:59:59');
            }

            $connection->orderBy('ID_SERVICIO', 'ASC')->chunk(5000, function ($items) use ($out, $columns) {

                foreach ($items as $item) {
                    $data = [];
                    for ($i = 0; $i < count($columns); $i++) {
                        $data[] = mb_convert_encoding($item->{$columns[$i]}, 'utf-16', 'utf-8');
                    }
                    //Write excel
                    fputcsv($out, $data, "\t");
                }
            });

            fclose($out);

        }, 200, [
            'Content-Encoding' => 'UTF-8',
            'Content-Type' => 'application/vnd.ms-excel; charset=utf-8',
            'Content-Disposition' => 'attachment; filename="REPORTE_DETERMINACION_IT' . "_" . date('Y-m-d') . '.xls"',
        ]);

        return $response;
    }

    public function reportDeterminationPclDelimited(Request $req, $cpath)
    {
        $initDate = $req->pcl_start_date_submit;
        $lastDate = $req->pcl_end_date_submit;

        $client = Client::where('path', $cpath)->firstOrFail();
        $client_name = str_replace("-", "_", $client->name);
        $client_name = strtolower($client_name);

        $view = "{$client_name}_determination_pcl";
        $columns = array_map(function ($column) {
            return $column->Field;
        }, DB::select(DB::raw('SHOW COLUMNS FROM ' . $view)));

        $response = new StreamedResponse(function () use ($columns, $initDate, $lastDate, $client_name) {

            $out = fopen('php://output', 'w');

            fputcsv($out, $columns, "\t");

            $connection = DB::connection()->table("{$client_name}_determination_pcl");

            if ($initDate) {
                $connection->where('FECHA_REGISTRO', '>=', $initDate . ' 00:00:00');
            }
            if ($lastDate) {
                $connection->where('FECHA_REGISTRO', '<=', $lastDate . ' 23:59:59');
            }

            $connection->orderBy('ID_SERVICIO', 'ASC')->chunk(5000, function ($items) use ($out, $columns) {

                foreach ($items as $item) {
                    $data = [];
                    for ($i = 0; $i < count($columns); $i++) {
                        $data[] = mb_convert_encoding($item->{$columns[$i]}, 'utf-16', 'utf-8');
                    }
                    //Write excel
                    fputcsv($out, $data, "\t");
                }
            });

            fclose($out);

        }, 200, [
            'Content-Encoding' => 'UTF-8',
            'Content-Type' => 'application/vnd.ms-excel; charset=utf-8',
            'Content-Disposition' => 'attachment; filename="REPORTE_DETERMINACION_PCL' . "_" . date('Y-m-d') . '.xls"',
        ]);

        return $response;
    }


    public function reportReiDelimited(Request $req, $cpath)
    {
        $initDate = $req->rei_start_date_submit;
        $lastDate = $req->rei_end_date_submit;

        $client = Client::where('path', $cpath)->firstOrFail();
        $client_name = str_replace("-", "_", $client->name);
        $client_name = strtolower($client_name);

        $view = "{$client_name}_rei";
        $columns = array_map(function ($column) {
            return $column->Field;
        }, DB::select(DB::raw('SHOW COLUMNS FROM ' . $view)));

        $response = new StreamedResponse(function () use ($columns, $initDate, $lastDate, $client_name) {

            $out = fopen('php://output', 'w');

            fputcsv($out, $columns, "\t");

            $connection = DB::connection()->table("{$client_name}_rei");

            if ($initDate) {
                $connection->where('FECHA_REGISTRO', '>=', $initDate . ' 00:00:00');
            }
            if ($lastDate) {
                $connection->where('FECHA_REGISTRO', '<=', $lastDate . ' 23:59:59');
            }

            $connection->orderBy('ID_SERVICIO', 'ASC')->chunk(5000, function ($items) use ($out, $columns) {

                foreach ($items as $item) {
                    $data = [];
                    for ($i = 0; $i < count($columns); $i++) {
                        $data[] = mb_convert_encoding($item->{$columns[$i]}, 'utf-16', 'utf-8');
                    }
                    //Write excel
                    fputcsv($out, $data, "\t");
                }
            });

            fclose($out);

        }, 200, [
            'Content-Encoding' => 'UTF-8',
            'Content-Type' => 'application/vnd.ms-excel; charset=utf-8',
            'Content-Disposition' => 'attachment; filename="REPORTE_REVISION_ESTADO_INVALIDEZ_FASE_4' . "_" . date('Y-m-d') . '.xls"',
        ]);

        return $response;
    }


    public function reportAdminActionsDelimited(Request $req, $cpath)
    {
        try {
            $initDate = $req->action_start_date_submit;
            $lastDate = $req->action_end_date_submit;

            // Validar que las fechas no sean nulas
            if (empty($initDate) || empty($lastDate)) {
                throw new \Exception("Las fechas no pueden estar vacías, en la sección 'Reporte de acciones'");
            }

            // Validar que $lastDate no sea menor que $initDate
            if (strtotime($lastDate) < strtotime($initDate)) {
                throw new \Exception("La fecha desde no puede ser menor a la fecha hasta, en la sección 'Reporte de acciones'");
            }

            $serviceId = $req->service_id;
            $actionId = $req->action_id;
            $columns = array_map(function ($column) {
                return $column->Field;
            }, DB::select(DB::raw('SHOW COLUMNS FROM actions_report')));
            $response = new StreamedResponse(function () use ($columns, $initDate, $lastDate, $serviceId, $actionId) {

                $out = fopen('php://output', 'w');

                fputcsv($out, $columns, "\t");

                $connection = DB::connection()->table("actions_report");

                if ($initDate) {
                    $connection->where('FECHA_ACCION', '>=', $initDate . ' 00:00:00');
                }
                if ($lastDate) {
                    $connection->where('FECHA_ACCION', '<=', $lastDate . ' 23:59:59');
                }
                if ($serviceId) {
                    $nameServices = Service::whereIn('id', explode(',', $serviceId))->select('name')->get()->toArray();
                    $connection->whereIn('NOMBRE_TIPO_SERVICIO', $nameServices);
                }
                if ($actionId) {
                    $nameActions = Action::whereIn('id', explode(',', $actionId))->select('name')->get()->toArray();
                    $connection->whereIn('NOMBRE_ACCION_SERVICIO', $nameActions);
                }

                $connection->orderBy('FECHA_ACCION', 'ASC')->chunk(5000, function ($items) use ($out, $columns) {

                    foreach ($items as $item) {

                        if (isset($item->EPS)) {
                            $item->EPS = \App\EPS::getNames()[$item->EPS];
                        }
                        if (isset($item->ARL)) {
                            $item->ARL = \App\ARL::getNames()[$item->ARL];
                        }
                        if (isset($item->AFP)) {
                            $item->AFP = \App\AFP::getNames()[$item->AFP];
                        }

                        $data = [];
                        for ($i = 0; $i < count($columns); $i++) {
                            $data[] = mb_convert_encoding($item->{$columns[$i]}, 'utf-16', 'utf-8');
                        }
                        //Write excel
                        fputcsv($out, $data, "\t");
                    }
                });

                fclose($out);

            }, 200, [
                'Content-Encoding' => 'UTF-8',
                'Content-Type' => 'application/vnd.ms-excel; charset=utf-8',
                'Content-Disposition' => 'attachment; filename="Actions' . '_' . date('Y-m-d') . '.xls"',
            ]);

            return $response;
        } catch (\Exception $e) {
            // Capturar el error y redirigir con mensaje
            return redirect('/admin/reportes/descargas_admin')
                ->withErrors($e->getMessage())
                ->withInput();
        }
    }

    public function reportAdminActionsBox(Request $req, $cpath)
    {
        try {
            $initDate = $req->action_start_date_submit;
            $lastDate = $req->action_end_date_submit;

            // Validar que las fechas no sean nulas
            if (empty($initDate) || empty($lastDate)) {
                throw new \Exception("Las fechas no pueden estar vacías, en la sección 'Reporte de cajas'");
            }

            // Validar que $lastDate no sea menor que $initDate
            if (strtotime($lastDate) < strtotime($initDate)) {
                throw new \Exception("La fecha desde no puede ser menor a la fecha hasta, en la sección 'Reporte de cajas'");
            }

            $accountsPercentages = AppServiceProvider::$ACCOUNTS_PERCENTAGES;

            $headings = [
                'CODMONEDA',
                'FORMA PAGO',
                'BANCO',
                'NUMERO DE RECIBO',
                'NUMREFERBANC',
                'CODINTER',
                'CODCORREDOR',
                'NUMPOL',
                'NUMCERT',
                'CODRAMO',
                'TIPOOPE',
                'FECEMI',
                'FECINIVIG',
                'FECFINVIG',
                'ID',
                'CLIENTE',
                'NUM COMPROB CONTA',
                'FECPAGO',
                'FECAPROBACION',
                'MTOPRIMA',
                'MTOCOMISI COMISION',
                'MTOPRIMACOBRO COMISION',
                'VALOR IVA RET',
                'VALOR RENTA RET',
                'MTOBOMBEROSMONEDA',
                'MTOBOMBEROSLOCAL',
                'MTOINECMONEDAD',
                'MTOINECLOCAL',
                'MTOCPS',
                'CODPROD',
                'PRIMA DOLAR',
                'VALOR IMPUESTO DOLAR',
                'FACTURA',
                'FEC_FACT',
                'FORMA PAGO'
            ];

            $consulta = "select * from report_box_view where created_at BETWEEN :initDate AND :lastDate ";

            $query = DB::select($consulta, [
                'initDate' => $initDate . ' 00:00:00',
                'lastDate' => $lastDate . ' 23:59:59'
            ]);

            $activities = [];

            foreach ($query as $item) {

                $activities[] = [
                    $item->cod_moneda,
                    $item->forma_pago,
                    $item->banco,
                    $item->num_recibo,
                    $item->num_trasferencia,
                    $item->cod_correduria,
                    $item->cod_corredor,
                    $item->num_pol,
                    $item->num_cert,
                    $item->cod_ramo,
                    $item->tipo_ope,
                    $item->fe_emi,
                    $item->validity_from,
                    $item->validity_to,
                    $item->doc_number,
                    $item->full_name,
                    $item->num_comprob,
                    $item->fec_pago,
                    $item->payment_approval_date,
                    number_format($item->mto_prima ?? 0, 2, ',', ''),
                    number_format($item->mto_comsi ?? 0, 2, ',', ''),
                    number_format($item->mto_prima_cobro ?? 0, 2, ',', ''),
                    number_format($item->vlr_iva_ret ?? 0, 2, ',', ''),
                    number_format($item->vlr_renta_ret ?? 0, 2, ',', ''),
                    number_format($item->mto_bomberos ?? 0, 2, ',', ''),
                    number_format($item->mto_bomberos_local ?? 0, 2, ',', ''),
                    number_format($item->mto_inec ?? 0, 2, ',', ''),
                    number_format($item->mto_ineclocal ?? 0, 2, ',', ''),
                    number_format($item->mto_cps ?? 0, 2, ',', ''),
                    $item->cod_pro,
                    $item->prima_dolar,
                    $item->valor_imp,
                    $item->factura_eletro,
                    $item->fec_fact,
                    $item->for_pago
                ];

            }

            return Excel::create('reporteCajas', function ($excel) use ($activities, $headings) {
                $excel->sheet('Entries', function ($sheet) use ($activities, $headings) {
                    $sheet->row(1, $headings);
                    $sheet->fromArray($activities, null, 'A2', false, false);
                });
            })->download('xlsx');

        } catch (\Exception $e) {
            // Capturar el error y redirigir con mensaje
            return redirect('/admin/reportes/descargas_admin')
                ->withErrors($e->getMessage())
                ->withInput();
        }
    }


    public function reportPolicy(Request $req, $cpath)
    {

        try {
            $initDate = $req->action_start_date_submit;
            $lastDate = $req->action_end_date_submit;

            if (empty($initDate) || empty($lastDate)) {
                throw new \Exception("Las fechas no pueden estar vacías, en la sección 'Reporte de pólizas'");
            }

            if (strtotime($lastDate) < strtotime($initDate)) {
                throw new \Exception("La fecha desde no puede ser menor a la fecha hasta, en la sección 'Reporte de pólizas'");
            }

            $consulta = "SELECT 
                                concat('SORT-', lpad(p.consecutive, 4, '0')) AS id,
                                date(p.validity_from) as validity_from, 
                               af.full_name,
                               af.doc_number,
                                COALESCE(ts.total_affiliates, p.number_workers_optional, 0) AS num_trabajador,
                                COALESCE(ts.total_salaries, p.salary_projection, 0) AS nomina,
                                 eb.branch_name,
                                a.activity_name,
                                CONCAT(COALESCE(af.address, ''), ' ', COALESCE(af.employer_address, '')) AS dir_empresa,
                                c.name_provincia,
                                c.name_canton,
                                c.name_distrito,
                                 case when    
                                (case when p.periodicity = '1' then p.anual_percentage
                                      when p.periodicity = '2' then p.semestral_percentage
                                      when p.periodicity = '3' then p.trimestral_percentage  
                                      when p.periodicity = '4' then p.mensual_percentage
                                      ELSE p.unico_percentage end) 
                                    is null
                                    then  0
                                    else 
                                       (case when p.periodicity = '1' then p.anual_percentage
                                      when p.periodicity = '2' then p.semestral_percentage
                                      when p.periodicity = '3' then p.trimestral_percentage  
                                      when p.periodicity = '4' then p.mensual_percentage
                                      ELSE p.unico_percentage END) END 
                                    AS tarifa,
                                st.name AS estado,
                                case when p.temporality = 'short' then 'Periodo corto' ELSE 'permanente' END AS tipo_poliza,
                                '' AS feha_rehabilitacion,
                                case when af.doc_type = 'CJ' then 'N' ELSE 'S' END  AS trabajador_inde,
                                case when p.work_modality_id = '1' then 'Riesgo de trabajo general'
                                     when p.work_modality_id = '2' then 'Riesgos del trabajo especial formación técnica dual'
                                     when p.work_modality_id = '3' then 'Riesgos del trabajo hogar'
                                     when p.work_modality_id = '4' then 'Riesgos del trabajo ocasional'
                                     when p.work_modality_id = '5' then 'Riesgos del trabajo sector público'
                                     when p.work_modality_id = '6' then 'Riesgos del trabajo independiente'
                                     when p.work_modality_id = '7' then 'Riesgos del trabajo único trabajador' end AS modalida_asegu,
                                case when YEAR(p.validity_from) = '2025' THEN 30 ELSE 0 END AS por_contrato,
                                p.id as policysort_id,
                                EXISTS (SELECT * FROM policy_addresses pa WHERE pa.policy_sort_id = p.id) AS has_policyaddresses
                        FROM policy_sorts p
                        LEFT JOIN activities ac ON (ac.id=p.activity_id)
                        LEFT JOIN affiliates af ON (af.id=ac.affiliate_id)
                        LEFT JOIN economic_activities a ON (a.code=p.activity_economic_id)
                        left join economic_branches eb on eb.id=a.branch_id
                        LEFT JOIN states st ON (st.id=ac.state_id)
                        LEFT JOIN activity_actions aa ON (aa.activity_id=p.activity_id AND aa.action_id=16)
                        LEFT JOIN costarica c ON (
                            c.cod_provincia = af.province COLLATE utf8mb4_unicode_ci AND 
                            c.cod_canton = af.canton COLLATE utf8mb4_unicode_ci AND 
                            c.cod_distrito = af.district COLLATE utf8mb4_unicode_ci
                        )
                        LEFT JOIN (
                            SELECT a.parent_id, p_sp.total_affiliates, p_sp.total_salaries
                            FROM policy_spreadsheets p_sp
                            LEFT JOIN activities a 
                                ON a.id = p_sp.activity_id
                            WHERE a.id = (
                                SELECT MAX(a2.id) 
                                FROM activities a2 
                                WHERE a2.parent_id = a.parent_id  -- ← Corrección: usar `a.parent_id` en lugar de `p.activity_id`
                                AND a2.state_id IN (56, 57)
                            )
                        ) ts ON ts.parent_id = p.activity_id
                        WHERE aa.created_at BETWEEN :initDate AND :lastDate and p.consecutive is not null and ac.state_id <> 196
                        GROUP BY p.id,
                                p.validity_from,
                                a.activity_name,
                                c.name_provincia,
                                c.name_canton,
                                c.name_distrito,
                                st.name
                        ORDER BY p.id desc ";

                $query = DB::select($consulta, [
                    'initDate' => $initDate . ' 00:00:00',
                    'lastDate' => $lastDate . ' 23:59:59'
                ]);


            $headings = [
                'ID PÓLIZA',
                'ID DEL TOMADOR',
                'NOMBRE DEL TOMADOR',
                'FECHA SUSCRIPCIÓN',
                '#TRABAJADORES DE LA PÓLIZA',
                'ULTIMA NOMINA DEL MES',
                'ACTIVIDAD ECONÓMICA GENERAL',
                'ACTIVIDAD ECONÓMICA ESPECIFICADA',
                'LUGAR DE LA EMPRESA/DIRECCIÓN DE LA EMPRESA',
                'PROVINCIA DE LA PÓLIZA',
                'CANTON DE LA PÓLIZA',
                'DISTRITO',
                'INDOLE DE TARIFA',
                'ESTATUS DE LA PÓLIZA',
                'TIPO DE PÓLIZA',
                'FECHA DE REHABILITACIÓN',
                'TRABAJADOR INDEPENDIENTE',
                'MODALIDAD DE ASEGURAMIENTO',
                '% CONTRATO',
                'PUNTOS DE EXPERIENCIA',
                'CV'
            ];

            $activities = [];

            foreach ($query as $item) {
                $activities[] = [
                    $item->id,
                    $item->doc_number,
                    $item->full_name,
                    $item->validity_from,
                    $item->num_trabajador,
                    floatval($item->nomina ?? 0),
                    $item->branch_name ?? '',
                    $item->activity_name,
                    $item->dir_empresa,
                    $item->name_provincia,
                    $item->name_canton,
                    $item->name_distrito,
                    $item->tarifa ?? 0,
                    ucfirst(strtolower($item->estado ?? '')),
                    $item->tipo_poliza,
                    $item->feha_rehabilitacion,
                    $item->trabajador_inde,
                    $item->modalida_asegu,
                    $item->por_contrato,
                    '',
                    '',
                ];

                // Si la póliza tiene direcciones adicionales, las agregamos justo debajo de la póliza
                if (!empty($item->has_policyaddresses)) {
                    $queryAddresses = DB::select("
                        SELECT 
                            ps.full_address,
                            c.name_provincia,
                            c.name_canton,
                            c.name_distrito
                        FROM 
                            policy_addresses ps 
                            LEFT JOIN costarica c ON (
                                c.cod_provincia = ps.province_id COLLATE utf8mb4_unicode_ci AND 
                                c.cod_canton = ps.canton_id COLLATE utf8mb4_unicode_ci AND 
                                c.cod_distrito = ps.district_id COLLATE utf8mb4_unicode_ci
                            )
                        WHERE 
                            ps.policy_sort_id = :policySortId
                        GROUP BY ps.id
                    ", [
                        'policySortId' => $item->policysort_id,
                    ]);

                    foreach ($queryAddresses as $address) {
                        $activities[] = [
                            '',
                            '',
                            '',
                            '',
                            '',
                            '',
                            '',
                            '',
                            $address->full_address ?? '',
                            $address->name_provincia ?? '',
                            $address->name_canton ?? '',
                            $address->name_distrito ?? '',
                            '',
                            '',
                            '',
                            '',
                            '',
                            '',
                            '',
                            '',
                            '',
                        ];
                    }
                }
            }

            return Excel::create('reportePoliza', function ($excel) use ($activities, $headings) {
                $excel->sheet('Entries', function ($sheet) use ($activities, $headings) {
                    $sheet->row(1, $headings);
                    $sheet->fromArray($activities, null, 'A2', false, false);

                    $rowIndex = 2;
                    foreach ($activities as $activity) {
                        // Fuerza los valores 0 como flotantes
                        $activity[5] = (float) $activity[5];
                        $sheet->row($rowIndex++, $activity);
                    }


                    // Calcular la última fila
                    $lastRow = count($activities) + 1; // +1 por la fila de encabezados

                    // Aplicar formato de fecha (día/mes/año)
                    $sheet->setColumnFormat([
                        // Formatos para fechas
                        'D2:D'.$lastRow => 'dd/mm/yyyy',
    
                        // // Formatos para valores monetarios (2 decimales)
                        'F2:F'.$lastRow => '0.00',
                    ]);

                    // Establecer alineación a la derecha para columnas numéricas y fechas
                    $numericColumns = ['D'];

                    foreach ($numericColumns as $col) {
                        $sheet->cells($col.'2:'.$col.$lastRow, function($cells) {
                            $cells->setAlignment('right');
                        });
                    }

                });
            })->download('xlsx');

            // $response = new StreamedResponse(function () use ($columns, $initDate, $lastDate) {

            //     $out = fopen('php://output', 'w');

            //     fputcsv($out, mb_convert_encoding($columns, 'utf-16', 'utf-8'), "\t");

            //     $consulta = "SELECT 
            //                     concat('SORT-', lpad(p.consecutive, 4, '0')) AS id,
            //                     date(p.validity_from) as validity_from, 
            //                    af.full_name,
            //                    af.doc_number,
            //                     COALESCE(ts.total_affiliates, p.number_workers_optional, 0) AS num_trabajador,
            //                     COALESCE(ts.total_salaries, p.salary_projection, 0) AS nomina,
            //                     a.activity_name,
            //                     CONCAT(COALESCE(af.address, ''), ' ', COALESCE(af.employer_address, '')) AS dir_empresa,
            //                     c.name_provincia,
            //                     c.name_canton,
            //                     c.name_distrito,
            //                      case when    
            //                     (case when p.periodicity = '1' then p.anual_percentage
            //                           when p.periodicity = '2' then p.semestral_percentage
            //                           when p.periodicity = '3' then p.trimestral_percentage  
            //                           when p.periodicity = '4' then p.mensual_percentage
            //                           ELSE p.unico_percentage end) 
            //                         is null
            //                         then  0
            //                         else 
            //                            (case when p.periodicity = '1' then p.anual_percentage
            //                           when p.periodicity = '2' then p.semestral_percentage
            //                           when p.periodicity = '3' then p.trimestral_percentage  
            //                           when p.periodicity = '4' then p.mensual_percentage
            //                           ELSE p.unico_percentage END) END 
            //                         AS tarifa,
            //                     st.name AS estado,
            //                     case when p.temporality = 'short' then 'Periodo corto' ELSE 'permanente' END AS tipo_poliza,
            //                     '' AS feha_rehabilitacion,
            //                     case when af.doc_type = 'CJ' then 'N' ELSE 'S' END  AS trabajador_inde,
            //                     case when p.work_modality_id = '1' then 'Riesgo de trabajo general'
            //                          when p.work_modality_id = '2' then 'Riesgos del trabajo especial formación técnica dual'
            //                          when p.work_modality_id = '3' then 'Riesgos del trabajo hogar'
            //                          when p.work_modality_id = '4' then 'Riesgos del trabajo ocasional'
            //                          when p.work_modality_id = '5' then 'Riesgos del trabajo sector público'
            //                          when p.work_modality_id = '6' then 'Riesgos del trabajo independiente'
            //                          when p.work_modality_id = '7' then 'Riesgos del trabajo único trabajador' end AS modalida_asegu,
            //                     case when YEAR(p.validity_from) = '2025' THEN 30 ELSE 0 END AS por_contrato
            //             FROM policy_sorts p
            //             LEFT JOIN activities ac ON (ac.id=p.activity_id)
            //             LEFT JOIN affiliates af ON (af.id=ac.affiliate_id)
            //             LEFT JOIN economic_activities a ON (a.code=p.activity_economic_id)
            //             LEFT JOIN states st ON (st.id=ac.state_id)
            //             LEFT JOIN activity_actions aa ON (aa.activity_id=p.activity_id AND aa.action_id=16)
            //             LEFT JOIN costarica c ON (
            //                 c.cod_provincia = af.province COLLATE utf8mb4_unicode_ci AND 
            //                 c.cod_canton = af.canton COLLATE utf8mb4_unicode_ci AND 
            //                 c.cod_distrito = af.district COLLATE utf8mb4_unicode_ci
            //             )
            //             LEFT JOIN (
            //                 SELECT a.parent_id, p_sp.total_affiliates, p_sp.total_salaries
            //                 FROM policy_spreadsheets p_sp
            //                 LEFT JOIN activities a 
            //                     ON a.id = p_sp.activity_id
            //                 WHERE a.id = (
            //                     SELECT MAX(a2.id) 
            //                     FROM activities a2 
            //                     WHERE a2.parent_id = a.parent_id  -- ← Corrección: usar `a.parent_id` en lugar de `p.activity_id`
            //                     AND a2.state_id IN (56, 57)
            //                 )
            //             ) ts ON ts.parent_id = p.activity_id
            //             WHERE aa.created_at BETWEEN :initDate AND :lastDate and p.consecutive is not null and ac.state_id <> 196
            //             GROUP BY p.id,
            //                     p.validity_from,
            //                     a.activity_name,
            //                     c.name_provincia,
            //                     c.name_canton,
            //                     c.name_distrito,
            //                     st.name
            //             ORDER BY p.id desc ";

            //     $query = DB::select($consulta, [
            //         'initDate' => $initDate . ' 00:00:00',
            //         'lastDate' => $lastDate . ' 23:59:59'
            //     ]);

            //     foreach ($query as $item) {

            //         $data = [];
            //         $data[] = mb_convert_encoding($item->id, 'utf-16', 'utf-8');
            //         $data[] = mb_convert_encoding($item->doc_number, 'utf-16', 'utf-8');
            //         $data[] = mb_convert_encoding($item->full_name, 'utf-16', 'utf-8');
            //         $data[] = mb_convert_encoding($item->validity_from, 'utf-16', 'utf-8');
            //         $data[] = mb_convert_encoding($item->num_trabajador, 'utf-16', 'utf-8');
            //         $data[] = number_format($item->nomina ?? 0, 2, ',', '');
            //         $data[] = mb_convert_encoding($item->activity_name, 'utf-16', 'utf-8');
            //         $data[] = mb_convert_encoding($item->dir_empresa, 'utf-16', 'utf-8');
            //         $data[] = mb_convert_encoding($item->name_provincia, 'utf-16', 'utf-8');
            //         $data[] = mb_convert_encoding($item->name_canton, 'utf-16', 'utf-8');
            //         $data[] = mb_convert_encoding($item->name_distrito, 'utf-16', 'utf-8');
            //         $data[] = $item->tarifa ?? 0;
            //         $data[] = mb_convert_encoding(ucfirst(strtolower($item->estado ?? '')), 'utf-16', 'utf-8');
            //         $data[] = mb_convert_encoding($item->tipo_poliza, 'utf-16', 'utf-8');
            //         $data[] = mb_convert_encoding($item->feha_rehabilitacion, 'utf-16', 'utf-8');
            //         $data[] = mb_convert_encoding($item->trabajador_inde, 'utf-16', 'utf-8');
            //         $data[] = mb_convert_encoding($item->modalida_asegu, 'utf-16', 'utf-8');
            //         $data[] = $item->por_contrato;
            //         $data[] = '';
            //         $data[] = '';

            //         //$data[] = number_format($item->prima_co ?? 0, 2, ',', '.');
            //         fputcsv($out, $data, "\t");
            //     }

            //     fclose($out);

            // }, 200, [
            //     'Content-Encoding' => 'UTF-8',
            //     'Content-Type' => 'application/vnd.ms-excel; charset=utf-8',
            //     'Content-Disposition' => 'attachment; filename="reportePoliza' . '_' . date('Y-m-d') . '.xls"',
            // ]);

            // return $response;
        } catch (\Exception $e) {
            // Capturar el error y redirigir con mensaje
            return redirect('/admin/reportes/descargas_admin')
                ->withErrors($e->getMessage())
                ->withInput();
        }
    }

    public function reportAgentCommission(Request $req, $cpath)
    {

        try {
            $initDate = $req->action_start_date_submit;
            $lastDate = $req->action_end_date_submit;

            // Validar que las fechas no sean nulas
            if (empty($initDate) || empty($lastDate)) {
                throw new \Exception("Las fechas no pueden estar vacías, en la sección 'Reporte de comisión agente'");
            }

            // Validar que $lastDate no sea menor que $initDate
            if (strtotime($lastDate) < strtotime($initDate)) {
                throw new \Exception("La fecha desde no puede ser menor a la fecha hasta, en la sección 'Reporte de comisión agente'");
            }

            $columns = [
                'CODPROD',
                'NUMPOL',
                'CODINTER',
                'CODCORREDOR',
                'NOMBRE CORREDOR',
                'NOMBRE INTERMEDIARIO',
                'PORCENTAJE COMISION',
                'ID CLIENTE',
                'CLIENTE',
                'CODMONEDA',
                'CODRAMOCERT',
                'TIPOOPE',
                'TASA DE CAMBIO',
                'PRIMACOBRADA MONEDA',
                'PRIMACOBRADA DOLAR',
                'FECCOBRO',
                'FECHA INICIO RECIBO',
                'FECHA FIN RECIBO',
                'FECINIVIG POL',
                'FECFINVIG POL',
                'COMISION LOCAL REAL',
                'COMISION MONEDA REAL',
                'RT CONTABLE',
                'FEC COMPROBANTE',
                '% CONTRATO',
                'NUMERO RECIBO'
            ];

            $response = new StreamedResponse(function () use ($columns, $initDate, $lastDate) {

                $out = fopen('php://output', 'w');

                fputcsv($out, mb_convert_encoding($columns, 'utf-16', 'utf-8'), "\t");

                $consulta = "select * from report_agent_commission_view where created_at BETWEEN :initDate AND :lastDate ";

                $query = DB::select($consulta, [
                    'initDate' => $initDate . ' 00:00:00',
                    'lastDate' => $lastDate . ' 23:59:59'
                ]);

                foreach ($query as $item) {
                    $data = [];
                    $data[] = mb_convert_encoding($item->cod_prod, 'utf-16', 'utf-8');
                    $data[] = mb_convert_encoding($item->id, 'utf-16', 'utf-8');
                    $data[] = "\t" .mb_convert_encoding($item->cod_intermediario, 'utf-16', 'utf-8'); //\t evita que elimine los ceros a la izquierda
                    $data[] = "\t" .mb_convert_encoding($item->cod_corredor, 'utf-16', 'utf-8'); //\t evita que elimine los ceros a la izquierda
                    $data[] = mb_convert_encoding($item->nombre_corredor, 'utf-16', 'utf-8');
                    $data[] = mb_convert_encoding($item->nombre_intermediario, 'utf-16', 'utf-8');
                    $data[] = mb_convert_encoding($item->por_comision, 'utf-16', 'utf-8');
                    $data[] = mb_convert_encoding($item->id_cliente, 'utf-16', 'utf-8');
                    $data[] = mb_convert_encoding($item->affiliate_name, 'utf-16', 'utf-8');
                    $data[] = mb_convert_encoding($item->type_currency, 'utf-16', 'utf-8');
                    $data[] = mb_convert_encoding($item->codramocert, 'utf-16', 'utf-8');
                    $data[] = mb_convert_encoding($item->tipo_ope, 'utf-16', 'utf-8');
                    $data[] = number_format($item->trm ?? 0, 2, ',', '');
                    $data[] = number_format($item->prima_co ?? 0, 2, ',', '');
                    $data[] = number_format($item->prima_do ?? 0, 2, ',', '');
                    $data[] = mb_convert_encoding($item->fecha_cobro, 'utf-16', 'utf-8');
                    $data[] = mb_convert_encoding($item->fecha_ini_recibo, 'utf-16', 'utf-8');
                    $data[] = mb_convert_encoding($item->fecha_fin_recibo, 'utf-16', 'utf-8');
                    $data[] = mb_convert_encoding($item->validity_from, 'utf-16', 'utf-8');
                    $data[] = mb_convert_encoding($item->validity_to, 'utf-16', 'utf-8');
                    $data[] = number_format($item->comision_co ?? 0, 2, ',', '');
                    $data[] = number_format($item->comision_do ?? 0, 2, ',', '');
                    $data[] = mb_convert_encoding($item->rt_contable, 'utf-16', 'utf-8');
                    $data[] = mb_convert_encoding($item->fecha_compro, 'utf-16', 'utf-8');
                    $data[] = mb_convert_encoding($item->por_contrato, 'utf-16', 'utf-8');
                    $data[] = mb_convert_encoding($item->compro, 'utf-16', 'utf-8');

                    fputcsv($out, $data, "\t");
                }

                fclose($out);

            }, 200, [
                'Content-Encoding' => 'UTF-8',
                'Content-Type' => 'application/vnd.ms-excel; charset=utf-8',
                'Content-Disposition' => 'attachment; filename="reporteComisionAgente' . '_' . date('Y-m-d') . '.xls"',
            ]);

            return $response;

        } catch (\Exception $e) {
            // Capturar el error y redirigir con mensaje
            return redirect('/admin/reportes/descargas_admin')
                ->withErrors($e->getMessage())
                ->withInput();
        }
    }

    public function reportAdmin(Request $req, $cpath, $service)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $client_name = $client->path;
        $view = "{$client_name}_{$service}";
        $columns = array_map(function ($column) {
            return $column->Field;
        }, DB::select(DB::raw('SHOW COLUMNS FROM ' . $view)));

        $services = [
            'insurance_quota' => 'Cotizacion_Seguros',
        ];
        $response = new StreamedResponse(function () use ($columns, $service, $client_name) {

            $out = fopen('php://output', 'w');

            fputcsv($out, $columns, "\t");

            DB::table("{$client_name}_{$service}")->orderBy('ID_SERVICIO', 'ASC')->chunk(1000, function ($items) use ($out, $columns, $service) {

                foreach ($items as $item) {

                    if (isset($item->EPS)) {
                        $item->EPS = \App\EPS::getNames()[$item->EPS];
                    }
                    if (isset($item->ARL)) {
                        $item->ARL = \App\ARL::getNames()[$item->ARL];
                    }
                    if (isset($item->AFP)) {
                        $item->AFP = \App\AFP::getNames()[$item->AFP];
                    }
                    if (isset($item->ESTADO_CIVIL)) {
                        $item->ESTADO_CIVIL = AppServiceProvider::$CIVIL_STATUS[$item->ESTADO_CIVIL];
                    }
                    if (isset($item->NIVEL_ESCOLAR)) {
                        $item->NIVEL_ESCOLAR = AppServiceProvider::$SCHOOL_LEVELS[$item->NIVEL_ESCOLAR];
                    }


                    $data = [];
                    for ($i = 0; $i < count($columns); $i++) {
                        $data[] = mb_convert_encoding($item->{$columns[$i]}, 'utf-16', 'utf-8');
                    }

                    //Write excel
                    fputcsv($out, $data, "\t");
                }
            });

            fclose($out);

        }, 200, [
            'Content-Encoding' => 'UTF-8',
            'Content-Type' => 'application/vnd.ms-excel; charset=utf-8',
            'Content-Disposition' => 'attachment; filename="' . strtoupper($services[$service]) . '_' . date('Y-m-d') . '_.xls"',
        ]);

        return $response;
    }

    public function reportEmailsColpensiones(Request $req, $cpath)
    {
        $client = Client::query()->where('path', $cpath)->firstOrFail();
        $columns = array_map(function ($column) {
            return $column->Field;
        }, DB::select(DB::raw('SHOW COLUMNS FROM colpensiones_emails')));
//        unset($columns[0]);
        $response = new StreamedResponse(function () use ($columns) {

            $out = fopen('php://output', 'w');

            fputcsv($out, $columns, "\t");

            DB::table("colpensiones_emails")->orderBy('ID', 'ASC')->chunk(1000, function ($items) use ($out, $columns) {

                foreach ($items as $item) {
                    if (isset($item->EPS)) {
                        $item->EPS = \App\EPS::getNames()[$item->EPS];
                    }
                    if (isset($item->ARL)) {
                        $item->ARL = \App\ARL::getNames()[$item->ARL];
                    }
                    if (isset($item->AFP)) {
                        $item->AFP = \App\AFP::getNames()[$item->AFP];
                    }

                    $data = [];
                    for ($i = 0; $i < count($columns); $i++) {
                        $data[] = mb_convert_encoding($item->{$columns[$i]}, 'utf-16', 'utf-8');
                    }

                    //Write excel
                    fputcsv($out, $data, "\t");
                }
            });

            fclose($out);

        }, 200, [
            'Content-Encoding' => 'UTF-8',
            'Content-Type' => 'application/vnd.ms-excel; charset=utf-8',
            'Content-Disposition' => 'attachment; filename="Emails_' . date('Y-m-d') . '.xls"',
        ]);


        return $response;
    }

    public function reportEmailsEC(Request $req, $cpath)
    {
        $client = Client::where('path', $cpath)->firstOrFail();


        $name_view_client = "excelcredit_emails";

        $columns = array_values(DB::getSchemaBuilder()->getColumnListing($name_view_client));


//        unset($columns[0]);
        $response = new StreamedResponse(function () use ($columns, $name_view_client) {

            $out = fopen('php://output', 'w');

            fputcsv($out, $columns, "\t");

            DB::table($name_view_client)->orderBy('ID', 'ASC')->chunk(1000, function ($items) use ($out, $columns) {

                foreach ($items as $item) {
                    if (isset($item->EPS)) {
                        $item->EPS = \App\EPS::getNames()[$item->EPS];
                    }
                    if (isset($item->ARL)) {
                        $item->ARL = \App\ARL::getNames()[$item->ARL];
                    }
                    if (isset($item->AFP)) {
                        $item->AFP = \App\AFP::getNames()[$item->AFP];
                    }

                    $data = [];
                    for ($i = 0; $i < count($columns); $i++) {
                        $data[] = mb_convert_encoding($item->{$columns[$i]}, 'utf-16', 'utf-8');
                    }

                    //Write excel
                    fputcsv($out, $data, "\t");
                }
            });

            fclose($out);

        }, 200, [
            'Content-Encoding' => 'UTF-8',
            'Content-Type' => 'application/vnd.ms-excel; charset=utf-8',
            'Content-Disposition' => 'attachment; filename="Emails_' . date('Y-m-d') . '.xls"',
        ]);


        return $response;
    }

    public function reportCertificateExcelCredit(Request $req, $cpath, $service)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $client_path = $client->path;
        $columns = array_values(DB::getSchemaBuilder()->getColumnListing("excelcredit_" . $service . "_certificate"));
        $services = [
            'debtor' => 'Seguro_Deudores',
            'personal' => 'Accidentes_Personales',
        ];
        $response = new StreamedResponse(function () use ($columns, $client, $service) {

            $out = fopen('php://output', 'w');

            fputcsv($out, $columns, "\t");

            DB::table("excelcredit_" . $service . "_certificate")->orderBy('ID_SERVICIO', 'ASC')->chunk(1000, function ($items) use ($out, $columns, $service) {
                foreach ($items as $item) {
                    $data = [];
                    for ($i = 0; $i < count($columns); $i++) {
                        $data[] = mb_convert_encoding($item->{$columns[$i]}, 'utf-16', 'utf-8');
                    }
                    //Write excel
                    fputcsv($out, $data, "\t");
                }
            });

            fclose($out);
        }, 200, [
            'Content-Encoding' => 'UTF-8',
            'Content-Type' => 'application/vnd.ms-excel; charset=utf-8',
            'Content-Disposition' => 'attachment; filename="Descarga_Certificados_' . strtoupper($services[$service]) . '_' . strtoupper($client_path) . '_' . date('Y-m-d') . '.xls"',
        ]);

        return $response;
    }

    public function reportLoadedDocs(Request $req, $cpath)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $client_name = $client->path;
        $columns = array_values(DB::getSchemaBuilder()->getColumnListing("udea_activity_loaded_docs"));
//        unset($columns[0]);
        $response = new StreamedResponse(function () use ($columns) {

            $out = fopen('php://output', 'w');

            fputcsv($out, $columns, "\t");

            DB::table("udea_activity_loaded_docs")->orderBy('ID_SERVICIO', 'ASC')->chunk(1000, function ($items) use ($out, $columns) {

                foreach ($items as $item) {
                    if (isset($item->OBLIGATORIO)) {
                        $item->OBLIGATORIO = $item->OBLIGATORIO == 1 ? "SI" : "NO";
                    }
                    if (isset($item->INCLUIDO_EN_DICTAMEN)) {
                        $item->INCLUIDO_EN_DICTAMEN = $item->INCLUIDO_EN_DICTAMEN == 1 ? "SI" : "NO";
                    }
                    if (isset($item->INCLUIDO_EN_OTROS)) {
                        $item->INCLUIDO_EN_OTROS = $item->INCLUIDO_EN_OTROS == 1 ? "SI" : "NO";
                    }

                    $data = [];
                    for ($i = 0; $i < count($columns); $i++) {
                        $data[] = mb_convert_encoding($item->{$columns[$i]}, 'utf-16', 'utf-8');
                    }

                    //Write excel
                    fputcsv($out, $data, "\t");
                }
            });

            fclose($out);

        }, 200, [
            'Content-Encoding' => 'UTF-8',
            'Content-Type' => 'application/vnd.ms-excel; charset=utf-8',
            'Content-Disposition' => 'attachment; filename="Documentos_Cargados_' . strtoupper($client_name) . "_" . date('Y-m-d') . '.xls"',
        ]);


        return $response;
    }

    public function reportActiveUsers(Request $req, $cpath)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $clientPath = $client->path;
        $clientName = $client->name;

        $columns = array_values(DB::getSchemaBuilder()->getColumnListing($clientPath . '_active_users'));
//        unset($columns[0]);
        $response = new StreamedResponse(function () use ($columns, $clientPath) {

            $out = fopen('php://output', 'w');

            fputcsv($out, $columns, "\t");

            DB::table($clientPath . '_active_users')->orderBy('NOMBRE', 'ASC')->chunk(1000, function ($items) use ($out, $columns) {

                foreach ($items as $item) {

                    $data = [];
                    for ($i = 0; $i < count($columns); $i++) {
                        $data[] = mb_convert_encoding($item->{$columns[$i]}, 'utf-16', 'utf-8');
                    }

                    //Write excel
                    fputcsv($out, $data, "\t");
                }
            });

            fclose($out);

        }, 200, [
            'Content-Encoding' => 'UTF-8',
            'Content-Type' => 'application/vnd.ms-excel; charset=utf-8',
            'Content-Disposition' => 'attachment; filename="Funcionarios_activos_' . strtoupper($clientName) . '_' . date('Y-m-d') . '.xls"',
        ]);


        return $response;
    }


    public function reportAffiliateWithServices(Request $req, $cpath)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $client_name = $client->path;
        $view = "{$client_name}_affiliates_with_services";
        $columns = array_map(function ($column) {
            return $column->Field;
        }, DB::select(DB::raw('SHOW COLUMNS FROM ' . $view)));

//        unset($columns[0]);
        $response = new StreamedResponse(function () use ($columns, $client_name) {

            $out = fopen('php://output', 'w');

            fputcsv($out, $columns, "\t");

            DB::table("{$client_name}_affiliates_with_services")->orderBy('NUMERO_IDENTIFICACION', 'ASC')->chunk(1000, function ($items) use ($out, $columns) {

                foreach ($items as $item) {

                    if (isset($item->EPS)) {
                        $item->EPS = \App\EPS::getNames()[$item->EPS];
                    }
                    if (isset($item->ARL)) {
                        $item->ARL = \App\ARL::getNames()[$item->ARL];
                    }
                    if (isset($item->AFP)) {
                        $item->AFP = \App\AFP::getNames()[$item->AFP];
                    }
                    if (isset($item->ESTADO_CIVIL)) {
                        $item->ESTADO_CIVIL = AppServiceProvider::$CIVIL_STATUS[$item->ESTADO_CIVIL];
                    }
                    if (isset($item->NIVEL_ESCOLAR)) {
                        $item->NIVEL_ESCOLAR = AppServiceProvider::$SCHOOL_LEVELS[$item->NIVEL_ESCOLAR];
                    }
                    if (isset($item->OCUPACION)) {
                        $item->OCUPACION = $this->getCiuo($item->OCUPACION);
                    }
                    $data = [];
                    for ($i = 0; $i < count($columns); $i++) {
                        $data[] = mb_convert_encoding($item->{$columns[$i]}, 'utf-16', 'utf-8');
                    }
                    //Write excel
                    fputcsv($out, $data, "\t");
                }
            });
            fclose($out);
        }, 200, [
            'Content-Encoding' => 'UTF-8',
            'Content-Type' => 'application/vnd.ms-excel; charset=utf-8',
            'Content-Disposition' => 'attachment; filename="Afiliados_con_servicios_' . strtoupper($client_name) . "_" . date('Y-m-d') . '.xls"',
        ]);
        return $response;
    }

    public function getCiuo($cod)
    {
        $json = Storage::disk('public')->get('new_ciuo.json');
        $json = json_decode($json);
        foreach ($json as $item) {
            if ($item->COD == $cod) {
                return mb_strtoupper($item->DESCRIPTION);
            }
        }
        return null;
    }

    public function getBank($cod)
    {
        $json = Storage::disk('public')->get('banco.json');
        $json = json_decode($json);
        foreach ($json as $item) {
            if ($item->code == $cod) {
                return mb_strtoupper($item->name);
            }
        }
        return null;
    }

    public function reportTest(Request $req, $cpath, $service)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $client_path = $client->path;
        $columns = array_values(DB::getSchemaBuilder()->getColumnListing("report_empty_{$service}"));
        $services = [
            'pcl' => 'Determinación de PCLO',
            'redifc' => 'Revisión estado de Invalidez fase contactabilidad',
            'tutela' => 'Atencipón de tutelas',
            'pqr' => 'Atención de PQR',
            'tj' => 'Tramites de Juntas'
        ];
        $response = new StreamedResponse(function () use ($columns, $client, $service) {

            $out = fopen('php://output', 'w');

            fputcsv($out, $columns, "\t");

            DB::table("report_empty_{$service}")->orderBy('ID_SERVICIO', 'ASC')->chunk(1000, function ($items) use ($out, $columns, $service) {
                foreach ($items as $item) {
                    $data = [];
                    for ($i = 0; $i < count($columns); $i++) {
                        $data[] = mb_convert_encoding($item->{$columns[$i]}, 'utf-16', 'utf-8');
                    }

                    //Write excel
                    fputcsv($out, $data, "\t");
                }
            });

            fclose($out);
        }, 200, [
            'Content-Encoding' => 'UTF-8',
            'Content-Type' => 'application/vnd.ms-excel; charset=utf-8',
            'Content-Disposition' => 'attachment; filename="Facturacion_' . strtoupper($services[$service]) . '_' . strtoupper($client_path) . '_' . date('Y-m-d') . '.xls"',
        ]);

        return $response;
    }

    public function reportDeterminationIt(Request $req, $cpath)
    {

        $client = Client::where('path', $cpath)->firstOrFail();
        $client_name = str_replace("-", "_", $client->name);
        $client_name = strtolower($client_name);
        $view = "{$client_name}_determination_it";
        $columns = array_map(function ($column) {
            return $column->Field;
        }, DB::select(DB::raw('SHOW COLUMNS FROM ' . $view)));

//        unset($columns[0]);
        $response = new StreamedResponse(function () use ($columns, $client_name) {

            $out = fopen('php://output', 'w');

            fputcsv($out, $columns, "\t");

            DB::table("{$client_name}_determination_it")->orderBy('FECHA_ACCION', 'ASC')->chunk(1000, function ($items) use ($out, $columns) {

                foreach ($items as $item) {

                    if (isset($item->EPS)) {
                        $item->EPS = \App\EPS::getNames()[$item->EPS];
                    }
                    if (isset($item->ARL)) {
                        $item->ARL = \App\ARL::getNames()[$item->ARL];
                    }
                    if (isset($item->AFP)) {
                        $item->AFP = \App\AFP::getNames()[$item->AFP];
                    }
                    if (isset($item->ESTADO_CIVIL)) {
                        $item->ESTADO_CIVIL = AppServiceProvider::$CIVIL_STATUS[$item->ESTADO_CIVIL];
                    }
                    if (isset($item->ESCOLARIDAD)) {
                        $item->ESCOLARIDAD = AppServiceProvider::$SCHOOL_LEVELS[$item->ESCOLARIDAD];
                    }
                    $data = [];
                    for ($i = 0; $i < count($columns); $i++) {
                        $data[] = mb_convert_encoding($item->{$columns[$i]}, 'utf-16', 'utf-8');
                    }
                    //Write excel
                    fputcsv($out, $data, "\t");
                }
            });
            fclose($out);
        }, 200, [
            'Content-Encoding' => 'UTF-8',
            'Content-Type' => 'application/vnd.ms-excel; charset=utf-8',
            'Content-Disposition' => 'attachment; filename="REPORTE_DETERMINACIÓN_IT' . "_" . date('Y-m-d') . '.xls"',
        ]);
        return $response;
    }

    public function reportNotification(Request $req, $cpath, $service)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $client_path = $client->path;
        $view = "colpensiones_notification_{$service}";
        $columns = array_map(function ($column) {
            return $column->Field;
        }, DB::select(DB::raw('SHOW COLUMNS FROM ' . $view)));
        $services = [
            'pcl' => 'Determinación de PCLO',
            'rei' => 'FASE 4 - Revisión estado de invalidez',
        ];
        $response = new StreamedResponse(function () use ($columns, $client, $service, $view) {

            $out = fopen('php://output', 'w');

            fputcsv($out, $columns, "\t");

            DB::table("{$view}")->orderBy('ID_SERVICIO', 'ASC')->chunk(1000, function ($items) use ($out, $columns, $service) {
                foreach ($items as $item) {

                    if (isset($item->NOMBRE_EPS_INTERESADA)) {
                        $epsDetails = \App\EPS::getDataAttribute()[$item->NOMBRE_EPS_INTERESADA];

                        $item->DIRECCION_EPS_INTERESADA = $epsDetails['address'];
                        $item->TELEFONO_EPS_INTERESADA = $epsDetails['phone'];
                        $item->ADMINISTRADORA_EPS_INTERESADA = $epsDetails['adminName'];
                        $item->CORREO_EPS_INTERESADA = $epsDetails['email'];

                        if (isset($epsDetails['department']) && isset($epsDetails['municipality'])) {
                            $item->CIUDAD_EPS_INTERESADA = \App\EPS::getCity($epsDetails);
                        } else {
                            $item->CIUDAD_EPS_INTERESADA = null; // O cualquier valor predeterminado
                        }

                        $item->NOMBRE_EPS_INTERESADA = \App\EPS::getNames()[$item->NOMBRE_EPS_INTERESADA];
                    }

                    if (isset($item->NOMBRE_ARL_INTERESADA)) {
                        $arlDetails = \App\ARL::getDataAttribute()[$item->NOMBRE_ARL_INTERESADA];

                        $item->DIRECCION_ARL_INTERESADA = $arlDetails['address'];
                        $item->TELEFONO_ARL_INTERESADA = $arlDetails['phone'];
                        $item->CORREO_ARL_INTERESADA = $arlDetails['email'];

                        if (isset($arlDetails['department']) && isset($arlDetails['municipality'])) {
                            $item->CIUDAD_ARL_INTERESADA = \App\ARL::getCity($arlDetails);
                        } else {
                            $item->CIUDAD_ARL_INTERESADA = null; // O cualquier valor predeterminado
                        }

                        $item->NOMBRE_ARL_INTERESADA = \App\ARL::getNames()[$item->NOMBRE_ARL_INTERESADA];
                    }
                    $data = [];
                    for ($i = 0; $i < count($columns); $i++) {
                        $data[] = mb_convert_encoding($item->{$columns[$i]}, 'utf-16', 'utf-8');
                    }

                    //Write excel
                    fputcsv($out, $data, "\t");
                }
            });

            fclose($out);
        }, 200, [
            'Content-Encoding' => 'UTF-8',
            'Content-Type' => 'application/vnd.ms-excel; charset=utf-8',
            'Content-Disposition' => 'attachment; filename="Reporte_124_modulo_de_notificacion' . strtoupper($services[$service]) . '_' . strtoupper($client_path) . '_' . date('Y-m-d') . '.xls"',
        ]);

        return $response;
    }

    public function reportControlAppointments(Request $req, $cpath, $service)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $client_path = $client->path;
        $view = "control_appointment_{$service}";
        $columns = array_map(function ($column) {
            return $column->Field;
        }, DB::select(DB::raw('SHOW COLUMNS FROM ' . $view)));
        $services = [
            'pcl' => 'Determinación de PCLO',
            'rei' => 'FASE 4 - Revisión estado de invalidez',
        ];
        $response = new StreamedResponse(function () use ($columns, $client, $service, $view) {

            $out = fopen('php://output', 'w');

            fputcsv($out, $columns, "\t");

            DB::table("{$view}")->orderBy('ID_SERVICIO', 'ASC')->chunk(1000, function ($items) use ($out, $columns, $service) {
                foreach ($items as $item) {

                    $data = [];
                    for ($i = 0; $i < count($columns); $i++) {
                        $data[] = mb_convert_encoding($item->{$columns[$i]}, 'utf-16', 'utf-8');
                    }

                    //Write excel
                    fputcsv($out, $data, "\t");
                }
            });

            fclose($out);

        }, 200, [
            'Content-Encoding' => 'UTF-8',
            'Content-Type' => 'application/vnd.ms-excel; charset=utf-8',
            'Content-Disposition' => 'attachment; filename="113_INFO_COMPLETA_CITAS_' . strtoupper($services[$service]) . '_' . strtoupper($client_path) . '_' . date('Y-m-d') . '.xls"',
        ]);

        return $response;
    }

    public function reportRejectionPcl(Request $req, $cpath, $service)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $client_path = $client->path;
        $view = "colpensiones_rejection_{$service}";
        $columns = array_map(function ($column) {
            return $column->Field;
        }, DB::select(DB::raw('SHOW COLUMNS FROM ' . $view)));
        $services = [
            'pcl' => 'Determinación de PCLO',
            'rei' => 'FASE 4 - Revisión estado de invalidez',
        ];
        $response = new StreamedResponse(function () use ($columns, $client, $service, $view) {

            $out = fopen('php://output', 'w');

            fputcsv($out, $columns, "\t");

            DB::table("{$view}")->orderBy('ID_SERVICIO', 'ASC')->chunk(1000, function ($items) use ($out, $columns, $service) {
                foreach ($items as $item) {
                    if (isset($item->CAUSAL_DE_RECHAZO)) {
                        if (!is_numeric($item->CAUSAL_DE_RECHAZO)) {
                            $item->CAUSAL_DE_RECHAZO = AppServiceProvider::$REJECTION_CAUSALS_PCL[$item->CAUSAL_DE_RECHAZO];
                        } else {
                            $item->CAUSAL_DE_RECHAZO = AppServiceProvider::$REJECTION_CAUSALS_OLD_PCL[$item->CAUSAL_DE_RECHAZO];
                        }
                    }
                    $data = [];
                    for ($i = 0; $i < count($columns); $i++) {
                        $data[] = mb_convert_encoding($item->{$columns[$i]}, 'utf-16', 'utf-8');
                    }

                    //Write excel
                    fputcsv($out, $data, "\t");
                }
            });

            fclose($out);

        }, 200, [
            'Content-Encoding' => 'UTF-8',
            'Content-Type' => 'application/vnd.ms-excel; charset=utf-8',
            'Content-Disposition' => 'attachment; filename="118_CASOS_RECHAZADOS_' . strtoupper($services[$service]) . '_' . strtoupper($client_path) . '_' . date('Y-m-d') . '.xls"',
        ]);

        return $response;
    }

    public function reportExamRequestPcl(Request $req, $cpath, $service)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $client_path = $client->path;
        $view = "colpensiones_exam_request_{$service}";
        $columns = array_map(function ($column) {
            return $column->Field;
        }, DB::select(DB::raw('SHOW COLUMNS FROM ' . $view)));
        $services = [
            'pcl' => 'Determinación de PCLO',
            'rei' => 'FASE 4 - Revisión estado de invalidez',
        ];
        $response = new StreamedResponse(function () use ($columns, $client, $service, $view) {

            $out = fopen('php://output', 'w');

            fputcsv($out, $columns, "\t");

            DB::table("{$view}")->orderBy('ID_SERVICIO', 'ASC')->chunk(1000, function ($items) use ($out, $columns, $service) {
                foreach ($items as $item) {

                    $data = [];
                    for ($i = 0; $i < count($columns); $i++) {
                        $data[] = mb_convert_encoding($item->{$columns[$i]}, 'utf-16', 'utf-8');
                    }

                    //Write excel
                    fputcsv($out, $data, "\t");
                }
            });

            fclose($out);

        }, 200, [
            'Content-Encoding' => 'UTF-8',
            'Content-Type' => 'application/vnd.ms-excel; charset=utf-8',
            'Content-Disposition' => 'attachment; filename="117_SOLICITUD_EXAMENES_' . strtoupper($services[$service]) . '_' . strtoupper($client_path) . '_' . date('Y-m-d') . '.xls"',
        ]);

        return $response;
    }

    public function reportDocRequestPcl(Request $req, $cpath, $service)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $client_path = $client->path;
        $view = "colpensiones_doc_request_{$service}";
        $columns = array_map(function ($column) {
            return $column->Field;
        }, DB::select(DB::raw('SHOW COLUMNS FROM ' . $view)));
        $services = [
            'pcl' => 'Determinación de PCLO',
            'rei' => 'FASE 4 - Revisión estado de invalidez',
        ];
        $response = new StreamedResponse(function () use ($columns, $client, $service, $view) {

            $out = fopen('php://output', 'w');

            fputcsv($out, $columns, "\t");

            DB::table("{$view}")->orderBy('ID_SERVICIO', 'ASC')->chunk(1000, function ($items) use ($out, $columns, $service) {
                foreach ($items as $item) {

                    $data = [];
                    for ($i = 0; $i < count($columns); $i++) {
                        $data[] = mb_convert_encoding($item->{$columns[$i]}, 'utf-16', 'utf-8');
                    }

                    //Write excel
                    fputcsv($out, $data, "\t");
                }
            });

            fclose($out);

        }, 200, [
            'Content-Encoding' => 'UTF-8',
            'Content-Type' => 'application/vnd.ms-excel; charset=utf-8',
            'Content-Disposition' => 'attachment; filename="117_SOLICITUD_DE_DOCUMENTOS_' . strtoupper($services[$service]) . '_' . strtoupper($client_path) . '_' . date('Y-m-d') . '.xls"',
        ]);

        return $response;
    }

    public function reportAnsDatesPclRei(Request $req, $cpath, $service)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $client_path = $client->path;
        $view = "colpensiones_ans_date_{$service}";
        $columns = array_map(function ($column) {
            return $column->Field;
        }, DB::select(DB::raw('SHOW COLUMNS FROM ' . $view)));
        $services = [
            'pcl' => 'PCL',
            'rei' => 'REI',
        ];
        $response = new StreamedResponse(function () use ($columns, $client, $service, $view) {

            $out = fopen('php://output', 'w');

            fputcsv($out, $columns, "\t");

            DB::table("{$view}")->orderBy('NRO_RADICADO', 'ASC')->chunk(1000, function ($items) use ($out, $columns, $service) {
                foreach ($items as $item) {

                    $data = [];
                    for ($i = 0; $i < count($columns); $i++) {
                        $data[] = mb_convert_encoding($item->{$columns[$i]}, 'utf-16', 'utf-8');
                    }

                    //Write excel
                    fputcsv($out, $data, "\t");
                }
            });

            fclose($out);

        }, 200, [
            'Content-Encoding' => 'UTF-8',
            'Content-Type' => 'application/vnd.ms-excel; charset=utf-8',
            'Content-Disposition' => 'attachment; filename="122_COLP_FECHAS_ANS_' . strtoupper($services[$service]) . '_' . strtoupper($client_path) . '_' . date('Y-m-d') . '.xls"',
        ]);

        return $response;
    }

    public function reportDictumCasesPclRei(Request $req, $cpath, $service)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $client_path = $client->path;
        $view = "colpensiones_dictum_115_{$service}";
        $columns = array_map(function ($column) {
            return $column->Field;
        }, DB::select(DB::raw('SHOW COLUMNS FROM ' . $view)));
        $services = [
            'pcl' => 'PCL',
            'rei' => 'REI',
        ];
        $response = new StreamedResponse(function () use ($columns, $client, $service, $view) {

            $out = fopen('php://output', 'w');

            fputcsv($out, $columns, "\t");

            DB::table("{$view}")->orderBy('NRO_RADICADO', 'ASC')->chunk(1000, function ($items) use ($out, $columns, $service) {
                foreach ($items as $item) {

                    $data = [];
                    for ($i = 0; $i < count($columns); $i++) {
                        $data[] = mb_convert_encoding($item->{$columns[$i]}, 'utf-16', 'utf-8');
                    }

                    //Write excel
                    fputcsv($out, $data, "\t");
                }
            });

            fclose($out);

        }, 200, [
            'Content-Encoding' => 'UTF-8',
            'Content-Type' => 'application/vnd.ms-excel; charset=utf-8',
            'Content-Disposition' => 'attachment; filename="122_COLP_FECHAS_ANS_' . strtoupper($services[$service]) . '_' . strtoupper($client_path) . '_' . date('Y-m-d') . '.xls"',
        ]);

        return $response;
    }

    public function reportStates200PclRei(Request $req, $cpath, $service)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $client_path = $client->path;
        $view = "colpensiones_states_200_{$service}";
        $columns = array_map(function ($column) {
            return $column->Field;
        }, DB::select(DB::raw('SHOW COLUMNS FROM ' . $view)));
        $services = [
            'pcl' => 'PCL',
            'rei' => 'REI',
        ];
        $response = new StreamedResponse(function () use ($columns, $client, $service, $view) {

            $out = fopen('php://output', 'w');

            fputcsv($out, $columns, "\t");

            DB::table("{$view}")->orderBy('NRO_RADICADO', 'ASC')->chunk(1000, function ($items) use ($out, $columns, $service) {
                foreach ($items as $item) {

                    $data = [];
                    for ($i = 0; $i < count($columns); $i++) {
                        $data[] = mb_convert_encoding($item->{$columns[$i]}, 'utf-16', 'utf-8');
                    }

                    //Write excel
                    fputcsv($out, $data, "\t");
                }
            });

            fclose($out);

        }, 200, [
            'Content-Encoding' => 'UTF-8',
            'Content-Type' => 'application/vnd.ms-excel; charset=utf-8',
            'Content-Disposition' => 'attachment; filename="122_COLP_FECHAS_ANS_' . strtoupper($services[$service]) . '_' . strtoupper($client_path) . '_' . date('Y-m-d') . '.xls"',
        ]);

        return $response;
    }

    public function reportBase100(Request $req, $cpath, $service)
    {

        $client = Client::where('path', $cpath)->firstOrFail();
        $client_path = $client->path;
        $view = "colpensiones_100_{$service}";
        $columns = array_map(function ($column) {
            return $column->Field;
        }, DB::select(DB::raw('SHOW COLUMNS FROM ' . $view)));
        $services = [
            'pcl' => 'PCL',
            'rei' => 'REI',
        ];
        $response = new StreamedResponse(function () use ($columns, $client, $service, $view) {

            $out = fopen('php://output', 'w');

            fputcsv($out, $columns, "\t");

            DB::table("{$view}")->orderBy('RADICADO', 'ASC')->chunk(1000, function ($items) use ($out, $columns, $service) {
                foreach ($items as $item) {

                    $data = [];
                    for ($i = 0; $i < count($columns); $i++) {
                        $data[] = mb_convert_encoding($item->{$columns[$i]}, 'utf-16', 'utf-8');
                    }

                    //Write excel
                    fputcsv($out, $data, "\t");
                }
            });

            fclose($out);

        }, 200, [
            'Content-Encoding' => 'UTF-8',
            'Content-Type' => 'application/vnd.ms-excel; charset=utf-8',
            'Content-Disposition' => 'attachment; filename="100_COLP_ESTADOS_PCL_' . strtoupper($services[$service]) . '_' . strtoupper($client_path) . '_' . date('Y-m-d') . '.xls"',
        ]);

        return $response;
    }

    public function reportBilling(Request $req, $cpath, $service)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $client_path = $client->path;
        $view = "colpensiones_facturacion_{$service}";
        $columns = array_map(function ($column) {
            return $column->Field;
        }, DB::select(DB::raw('SHOW COLUMNS FROM ' . $view)));
        $services = [
            'pcl' => 'PCL',
            'rei' => 'REI',
        ];
        $response = new StreamedResponse(function () use ($columns, $client, $service, $view) {

            $out = fopen('php://output', 'w');

            fputcsv($out, $columns, "\t");

            DB::table("{$view}")->orderBy('ID_SERVICIO', 'ASC')->chunk(1000, function ($items) use ($out, $columns, $service) {
                foreach ($items as $item) {

                    $data = [];
                    for ($i = 0; $i < count($columns); $i++) {
                        $data[] = mb_convert_encoding($item->{$columns[$i]}, 'utf-16', 'utf-8');
                    }

                    //Write excel
                    fputcsv($out, $data, "\t");
                }
            });

            fclose($out);

        }, 200, [
            'Content-Encoding' => 'UTF-8',
            'Content-Type' => 'application/vnd.ms-excel; charset=utf-8',
            'Content-Disposition' => 'attachment; filename="402_COLP_VAL_FACTURACION_' . strtoupper($services[$service]) . '_' . strtoupper($client_path) . '_' . date('Y-m-d') . '.xls"',
        ]);

        return $response;
    }

    public function reportAllBilling(Request $req, $cpath, $service)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $client_path = $client->path;
        $view = "colpensiones_all_facturacion_{$service}";
        $columns = array_map(function ($column) {
            return $column->Field;
        }, DB::select(DB::raw('SHOW COLUMNS FROM ' . $view)));
        $services = [
            'pcl' => 'PCL',
            'rei' => 'REI',
        ];
        $response = new StreamedResponse(function () use ($columns, $client, $service, $view) {

            $out = fopen('php://output', 'w');

            fputcsv($out, $columns, "\t");

            DB::table("{$view}")->orderBy('ID_SERVICIO', 'ASC')->chunk(1000, function ($items) use ($out, $columns, $service) {
                foreach ($items as $item) {

                    $data = [];
                    for ($i = 0; $i < count($columns); $i++) {
                        $data[] = mb_convert_encoding($item->{$columns[$i]}, 'utf-16', 'utf-8');
                    }

                    //Write excel
                    fputcsv($out, $data, "\t");
                }
            });

            fclose($out);

        }, 200, [
            'Content-Encoding' => 'UTF-8',
            'Content-Type' => 'application/vnd.ms-excel; charset=utf-8',
            'Content-Disposition' => 'attachment; filename="409_COL_TODAS_LAS_ACCIONES_FACTURACION_' . strtoupper($services[$service]) . '_' . strtoupper($client_path) . '_' . date('Y-m-d') . '.xls"',
        ]);

        return $response;
    }

    public function reportFractionalControl(Request $req, $cpath)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $clientName = $client->name;
        $columns = array_map(function ($column) {
            return $column->Field;
        }, DB::select(DB::raw('SHOW COLUMNS FROM colpensiones_it_fractional')));

        $response = new StreamedResponse(function () use ($columns) {

            $out = fopen('php://output', 'w');

            fputcsv($out, $columns, "\t");

            DB::table('colpensiones_it_fractional')->orderBy('RAD_BIZAGI', 'ASC')->chunk(1000, function ($items) use ($out, $columns) {

                foreach ($items as $item) {

                    $data = [];
                    for ($i = 0; $i < count($columns); $i++) {
                        $data[] = mb_convert_encoding($item->{$columns[$i]}, 'utf-16', 'utf-8');
                    }

                    //Write excel
                    fputcsv($out, $data, "\t");
                }
            });

            fclose($out);

        }, 200, [
            'Content-Encoding' => 'UTF-8',
            'Content-Type' => 'application/vnd.ms-excel; charset=utf-8',
            'Content-Disposition' => 'attachment; filename="Control_fracciones_' . date('Y_m_d_H_i') . '.xls"',
        ]);


        return $response;
    }

    public function reportConsecutivePcl(Request $req, $cpath)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $clientName = $client->name;
        $columns = array_map(function ($column) {
            return $column->Field;
        }, DB::select(DB::raw('SHOW COLUMNS FROM control_consecutivo_pcl')));

        $response = new StreamedResponse(function () use ($columns) {

            $out = fopen('php://output', 'w');

            fputcsv($out, $columns, "\t");

            DB::table('control_consecutivo_pcl')->orderBy('CONSECUTIVO', 'ASC')->chunk(1000, function ($items) use ($out, $columns) {

                foreach ($items as $item) {

                    $data = [];
                    for ($i = 0; $i < count($columns); $i++) {
                        $data[] = mb_convert_encoding($item->{$columns[$i]}, 'utf-16', 'utf-8');
                    }

                    //Write excel
                    fputcsv($out, $data, "\t");
                }
            });

            fclose($out);

        }, 200, [
            'Content-Encoding' => 'UTF-8',
            'Content-Type' => 'application/vnd.ms-excel; charset=utf-8',
            'Content-Disposition' => 'attachment; filename="Control_Consecutivos_PCL_' . strtoupper($clientName) . '_' . date('Y-m-d') . '.xls"',
        ]);


        return $response;
    }

    public function reportConsecutiveIt(Request $req, $cpath)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $clientName = $client->name;
        $columns = array_map(function ($column) {
            return $column->Field;
        }, DB::select(DB::raw('SHOW COLUMNS FROM control_consecutivo_it')));

        $response = new StreamedResponse(function () use ($columns) {

            $out = fopen('php://output', 'w');

            fputcsv($out, $columns, "\t");

            DB::table('control_consecutivo_it')->orderBy('CONSECUTIVO', 'ASC')->chunk(1000, function ($items) use ($out, $columns) {

                foreach ($items as $item) {

                    $data = [];
                    for ($i = 0; $i < count($columns); $i++) {
                        $data[] = mb_convert_encoding($item->{$columns[$i]}, 'utf-16', 'utf-8');
                    }

                    //Write excel
                    fputcsv($out, $data, "\t");
                }
            });

            fclose($out);

        }, 200, [
            'Content-Encoding' => 'UTF-8',
            'Content-Type' => 'application/vnd.ms-excel; charset=utf-8',
            'Content-Disposition' => 'attachment; filename="Control_Consecutivos_IT_' . strtoupper($clientName) . '_' . date('Y-m-d') . '.xls"',
        ]);


        return $response;
    }

    public function reportHowManyActions(Request $req, $cpath)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $clientName = $client->name;
        $columns = array_map(function ($column) {
            return $column->Field;
        }, DB::select(DB::raw('SHOW COLUMNS FROM how_many_actions_current')));

        // Array de nombres de meses en español.
        $meses = ['01' => 'ENERO', '02' => 'FEBRERO', '03' => 'MARZO', '04' => 'ABRIL', '05' => 'MAYO', '06' => 'JUNIO', '07' => 'JULIO', '08' => 'AGOSTO', '09' => 'SEPTIEMBRE', '10' => 'OCTUBRE', '11' => 'NOVIEMBRE', '12' => 'DICIEMBRE'];
        $nombreArchivo = 'ACCIONES_' . $meses[date('m')] . '_' . date('d') . '.xls';
        $response = new StreamedResponse(function () use ($columns) {

            $out = fopen('php://output', 'w');

            fputcsv($out, $columns, "\t");

            DB::table('how_many_actions_current')->orderBy('FECHA_ACCION', 'ASC')->chunk(1000, function ($items) use ($out, $columns) {

                foreach ($items as $item) {

                    $data = [];
                    for ($i = 0; $i < count($columns); $i++) {
                        $data[] = mb_convert_encoding($item->{$columns[$i]}, 'utf-16', 'utf-8');
                    }

                    //Write excel
                    fputcsv($out, $data, "\t");
                }
            });

            fclose($out);

        }, 200, [
            'Content-Encoding' => 'UTF-8',
            'Content-Type' => 'application/vnd.ms-excel; charset=utf-8',
            'Content-Disposition' => 'attachment; filename="' . $nombreArchivo . '"',
        ]);


        return $response;
    }

    public function specialConditionsReport(Request $req, $cpath)
    {

        try {

            $activities = [];

            $consulta = " SELECT *
                            FROM special_conditions_report";

            $query = DB::select($consulta);

            foreach ($query as $item) {

                // 1) Validar periodicidad
                $periodKey = $item->periodicity;
                if (empty($periodKey) || ! array_key_exists($periodKey, AppServiceProvider::$PERIODICITYT)) {
                    $periodText = 'Pago Único';
                } else {
                    $periodText = AppServiceProvider::$PERIODICITYT[$periodKey];
                }
            
                // 2) Validar tipo de descuento
                // Normalizamos nulos o cadenas vacías a 'N/A'
                $rawDiscount = $item->tipo_descuento;
                $discountText = (! empty($rawDiscount) && trim($rawDiscount) !== '')
                    ? $rawDiscount
                    : 'N/A';
            
                // 3) Armar la fila
                $activities[] = [
                    $item->nombre_correduria,
                    $item->nombre_cliente,
                    $item->numero_identificacion,  // ojo sin tilde en la propiedad
                    $item->actividad_economica,
                    'SORT - ' . sprintf('%04d', $item->consecutive),
                    $periodText,
                    $item->type_currency,
                    number_format($item->prima_pura, 2, ',', '.'),
                    $item->monto !== null
                        ? number_format($item->monto, 2, ',', '.')
                        : 'N/A',
                    $item->porcentaje ?? 'N/A',
                    $discountText,
                    number_format($item->prima_total_pagar, 2, ',', '.'),
                ];
            }
            


            $headings = [
                'Nombre de la correduría',
                'Nombre del cliente',
                'Número de identificación',
                'Actividad económica',
                'Póliza',
                'Periodicidad','Moneda',
                'Prima pura (sin descuento)',
                'Monto',
                '%',
                'El tipo de descuento',
                'Prima total a pagar'
            ];


            return Excel::create('Reporte_condiciones_especiales', function ($excel) use ($activities, $headings) {
                $excel->sheet('Entries', function ($sheet) use ($activities, $headings) {
                    $sheet->row(1, $headings);
                    $sheet->fromArray($activities, null, 'A2', false, false);
                });
            })->download('xlsx');
        } catch (\Exception $e) {
            // Capturar el error y redirigir con mensaje
            return redirect('/admin/reportes/descargas_admin')
                ->withErrors($e->getMessage())
                ->withInput();
        }
    }

    public function reportFractionalControlByBizagi(Request $req, $cpath)
    {
        $idBizagi = $req->id_bizagi;
        $columns = array_map(function ($column) {
            return $column->Field;
        }, DB::select(DB::raw('SHOW COLUMNS FROM colpensiones_it_fractional')));

        $response = new StreamedResponse(function () use ($columns, $idBizagi) {

            $out = fopen('php://output', 'w');

            fputcsv($out, $columns, "\t");

            $connection = DB::connection()->table("colpensiones_it_fractional");

            if ($idBizagi) {
                $connection->where('RAD_BIZAGI', '=', $idBizagi);
            }

            $connection->orderBy('ID_SERVICIO', 'ASC')->chunk(5000, function ($items) use ($out, $columns) {

                foreach ($items as $item) {

                    $data = [];
                    for ($i = 0; $i < count($columns); $i++) {
                        $data[] = mb_convert_encoding($item->{$columns[$i]}, 'utf-16', 'utf-8');
                    }
                    //Write excel
                    fputcsv($out, $data, "\t");
                }
            });

            fclose($out);

        }, 200, [
            'Content-Encoding' => 'UTF-8',
            'Content-Type' => 'application/vnd.ms-excel; charset=utf-8',
            'Content-Disposition' => 'attachment; filename="Control_fracciones_' . date('Y_m_d_H_i') . '.xls"',
        ]);

        return $response;
    }

    public function reportPaymentControlIt(Request $req, $cpath)
    {
        $tipoDeDocumento = $req->doc_type;
        $documento = $req->doc_number;
        $columns = array_map(function ($column) {
            return $column->Field;
        }, DB::select(DB::raw('SHOW COLUMNS FROM colpensiones_payment_consult_it')));

        $response = new StreamedResponse(function () use ($columns, $tipoDeDocumento, $documento) {

            $out = fopen('php://output', 'w');

            fputcsv($out, $columns, "\t");

            $connection = DB::connection()->table("colpensiones_payment_consult_it");
            if ($tipoDeDocumento) {
                $connection->where('TIPO_DE_DOCUMENTO', '=', $tipoDeDocumento);
            }
            if ($documento) {
                $connection->where('DOCUMENTO', '=', $documento);
            }

            $connection->orderBy('FECHA_INICIO_IT', 'ASC')->chunk(5000, function ($items) use ($out, $columns) {

                foreach ($items as $item) {

                    $data = [];
                    for ($i = 0; $i < count($columns); $i++) {
                        $data[] = mb_convert_encoding($item->{$columns[$i]}, 'utf-16', 'utf-8');
                    }
                    //Write excel
                    fputcsv($out, $data, "\t");
                }
            });

            fclose($out);

        }, 200, [
            'Content-Encoding' => 'UTF-8',
            'Content-Type' => 'application/vnd.ms-excel; charset=utf-8',
            'Content-Disposition' => 'attachment; filename="Control_pagos_it_' . $documento . '_' . date('Y_m') . '.xls"',
        ]);

        return $response;
    }

    public function reportPaymentControlItExcel(Request $req, $doc_type, $doc_number)
    {
        $tipoDeDocumento = $doc_type;
        $documento = $doc_number;
        $columns = array_map(function ($column) {
            return $column->Field;
        }, DB::select(DB::raw('SHOW COLUMNS FROM colpensiones_payment_consult_it')));

        $response = new StreamedResponse(function () use ($columns, $tipoDeDocumento, $documento) {

            $out = fopen('php://output', 'w');

            fputcsv($out, $columns, "\t");

            $connection = DB::connection()->table("colpensiones_payment_consult_it");
            if ($tipoDeDocumento) {
                $connection->where('TIPO_DE_DOCUMENTO', '=', $tipoDeDocumento);
            }
            if ($documento) {
                $connection->where('DOCUMENTO', '=', $documento);
            }

            $connection->orderBy('FECHA_INICIO_IT', 'ASC')->chunk(5000, function ($items) use ($out, $columns) {

                foreach ($items as $item) {

                    if (isset($item->NO_OFICIO_DE_PAGO) && strlen($item->NO_OFICIO_DE_PAGO) <= 5) {
                        $item->NO_OFICIO_DE_PAGO = "DML-I " . str_pad($item->NO_OFICIO_DE_PAGO, 5, "0", STR_PAD_LEFT);
                    }
                    $data = [];
                    for ($i = 0; $i < count($columns); $i++) {
                        $data[] = mb_convert_encoding($item->{$columns[$i]}, 'utf-16', 'utf-8');
                    }
                    //Write excel
                    fputcsv($out, $data, "\t");
                }
            });

            fclose($out);

        }, 200, [
            'Content-Encoding' => 'UTF-8',
            'Content-Type' => 'application/vnd.ms-excel; charset=utf-8',
            'Content-Disposition' => 'attachment; filename="Control_pagos_it_' . $documento . '_' . date('Y_m') . '.xls"',
        ]);

        return $response;
    }

    public function searchPaymentControlIt()
    {
        return view('information.payment_control_it');
    }

    public function getPaymentControlIt(Request $req)
    {
        $tipoDeDocumento = $req->doc_type;
        $documento = $req->doc_number;
        $columns = array_map(function ($column) {
            return $column->Field;
        }, DB::select(DB::raw('SHOW COLUMNS FROM colpensiones_payment_consult_it')));

        $columnsToFormat = ['FECHA_OFICIO', 'FECHA_INICIO_IT', 'FECHA_FIN_IT'];

        $connection = DB::connection()->table("colpensiones_payment_consult_it");
        if ($tipoDeDocumento) {
            $connection->where('TIPO_DE_DOCUMENTO', '=', $tipoDeDocumento);
        }
        if ($documento) {
            $connection->where('DOCUMENTO', '=', $documento);
        }

        $items = $connection->orderBy('FECHA_INICIO_IT', 'ASC')->get();

        $data = [];
        foreach ($items as $item) {
            $rowArray = [];
            for ($i = 0; $i < count($columns); $i++) {
                $value = $item->{$columns[$i]};
                if (in_array($columns[$i], $columnsToFormat) &&
                    !is_null($value) &&
                    \DateTime::createFromFormat('Y-m-d', $value)) { // Verificar si la fecha es válida
                    $value = \Carbon\Carbon::parse($value)->format('d/m/Y');
                }
                if ($columns[$i] == "NO_OFICIO_DE_PAGO" && strlen($value) <= 5) {
                    $value = "DML-I " . str_pad($value, 5, "0", STR_PAD_LEFT);
                }
                if ($columns[$i] == "TERCERO_AUTORIZADO_DE_PAGO") {
                    $value = strtoupper($value);
                }
                $rowArray[] = mb_convert_encoding($value, 'utf-8', 'utf-8');
            }
            $data[] = $rowArray;
        }

        return view('information.payment_control_it', ['data' => $data, 'columns' => $columns]);
    }

    public function reportBudgetItDelimitedOld(Request $req, $cpath)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $clientName = $client->name;
        $initDate = $req->budget_start_date_submit;
        $lastDate = $req->budget_end_date_submit;
        $columns = array_map(function ($column) {
            return $column->Field;
        }, DB::select(DB::raw('SHOW COLUMNS FROM colpensiones_budget_consult_it')));

        $response = new StreamedResponse(function () use ($columns, $initDate, $lastDate) {

            $out = fopen('php://output', 'w');

            fputcsv($out, $columns, "\t");

            $connection = DB::connection()->table("colpensiones_budget_consult_it");

            if ($initDate) {
                $connection->where('FECHA_GENERACION_OFICIO', '>=', $initDate . ' 00:00:00');
            }
            if ($lastDate) {
                $connection->where('FECHA_GENERACION_OFICIO', '<=', $lastDate . ' 23:59:59');
            }

            $connection->orderBy('NO_RADICADO_BIZAGI', 'ASC')->chunk(5000, function ($items) use ($out, $columns) {

                foreach ($items as $item) {

                    $data = [];
                    for ($i = 0; $i < count($columns); $i++) {
                        $data[] = mb_convert_encoding($item->{$columns[$i]}, 'utf-16', 'utf-8');
                    }
                    //Write excel
                    fputcsv($out, $data, "\t");
                }
            });

            fclose($out);

        }, 200, [
            'Content-Encoding' => 'UTF-8',
            'Content-Type' => 'application/vnd.ms-excel; charset=utf-8',
            'Content-Disposition' => 'attachment; filename="Reporte_de_presupuesto_' . strtoupper($clientName) . '_' . date('Y-m-d') . '.xls"',
        ]);

        return $response;
    }

    public function reportBudgetItDelimited(Request $req, $cpath)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $clientName = $client->name;
        $initDate = $req->budget_start_date_submit;
        $lastDate = $req->budget_end_date_submit;


        $columns = array_map(function ($column) {
            return $column->Field;
        }, DB::select(DB::raw('SHOW COLUMNS FROM colpensiones_budget_consult_it')));

        $data = \DB::table("colpensiones_budget_consult_it")->where('FECHA_GENERACION_OFICIO', '>=', $initDate . ' 00:00:00')->where('FECHA_GENERACION_OFICIO', '<=', $lastDate . ' 23:59:59')->get();

        \Excel::create('reporte_de_presupuesto_' . $clientName . '_' . date('Y-m-d'), function ($excel) use ($data, $columns) {

            $excel->setTitle('Reporte de presupuesto');

            // Chain the setters
            $excel->setCreator('RenApp');

            $excel->setDescription('Reporte de presupuestos IT');

            $excel->sheet('Hoja 1', function ($sheet) use ($data, $columns) {
                $sheet->appendRow($columns); // column names

                foreach ($data as $row) {
                    $rowData = [];
                    foreach ($columns as $column) {
                        if ($column == 'NO_DE_CUENTA' || $column == 'BANCO' || $column == 'TIPO_DE_CUENTA') {
                            $rowData[] = "\t" . $row->{$column};
                        } else if ($column == 'FECHA_GENERACION_OFICIO' || $column == 'FECHA_RESOLUCION' || $column == 'FECHA_INICIO_IT' || $column == 'FECHA_FIN_IT') {
                            $rowData[] = \Carbon\Carbon::parse($row->{$column})->format('d/m/Y');
                        } else if ($column == 'VALOR_A_PAGAR') {
                            $cleanedValue = preg_replace('/[^\d.,-]/', '', $row->{$column});
                            $numericValue = floatval($cleanedValue);
                            $rowData[] = '$ ' . number_format($numericValue, 0, ',', '.');
                        } else if ($column == 'NO_OFICIO_DE_PAGO' && strlen($row->NO_OFICIO_DE_PAGO) <= 5) { // you should replace this with actual column name
                            $rowData[] = "DML-I " . str_pad($row->NO_OFICIO_DE_PAGO, 5, "0", STR_PAD_LEFT);
                        } else {
                            $rowData[] = $row->{$column};
                        }
                    }
                    $sheet->appendRow($rowData);
                }

            });

        })->export('xls');

    }

    public function reportAffiliateAcreedorItDelimited(Request $req, $cpath)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $clientName = $client->name;
        $initDate = $req->af_ac_start_date_submit;
        $lastDate = $req->af_ac_end_date_submit;


        $columns = array_map(function ($column) {
            return $column->Field;
        }, DB::select(DB::raw('SHOW COLUMNS FROM affiliate_acreedor_it')));

        $data = \DB::table("affiliate_acreedor_it")->where('FECHA_GENERACION_OFICIO', '>=', $initDate . ' 00:00:00')->where('FECHA_GENERACION_OFICIO', '<=', $lastDate . ' 23:59:59')->get();

        \Excel::create('Reporte_de_Acreedores_por_afiliado_' . $clientName . '_' . date('Y-m-d'), function ($excel) use ($data, $columns) {

            $excel->setTitle('Reporte de acreedores por Afiliado');

            // Chain the setters
            $excel->setCreator('RenApp');

            $excel->setDescription('Reporte de acreedores por Afiliado');

            $excel->sheet('Hoja 1', function ($sheet) use ($data, $columns) {
                $sheet->appendRow($columns); // column names

                foreach ($data as $row) {
                    $rowData = [];
                    foreach ($columns as $column) {
                        if ($column == 'CODIGO_BANCO_DE_LA_CUENTA_DEL_ACREEDOR'
                            || $column == 'NO_DE_CUENTA_DEL_ACREEDOR'
                            || $column == 'NOMBRE_DE_LA_POBLACION_DE_ACUERDO_AL_DANE'
                            || $column == 'CORRIENTE_AHORROS_CHEQUE'
                            || $column == 'CODIGO_DEL_DEPARTAMENTO') {
                            $rowData[] = "\t" . $row->{$column};
                        } else {
                            $rowData[] = $row->{$column};
                        }
                    }
                    $sheet->appendRow($rowData);
                }
            });

        })->export('xls');

    }

    public function reportThirdAcreedorItDelimited(Request $req, $cpath)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $clientName = $client->name;
        $initDate = $req->thd_ac_start_date_submit;
        $lastDate = $req->thd_ac_end_date_submit;


        $columns = array_map(function ($column) {
            return $column->Field;
        }, DB::select(DB::raw('SHOW COLUMNS FROM third_person_acreedor_it')));

        $data = \DB::table("third_person_acreedor_it")->where('FECHA_REGISTRO', '>=', $initDate . ' 00:00:00')->where('FECHA_REGISTRO', '<=', $lastDate . ' 23:59:59')->get();

        \Excel::create('Reporte_de_Acreedores_a_terceros_' . $clientName . '_' . date('Y-m-d'), function ($excel) use ($data, $columns) {

            $excel->setTitle('Reporte de acreedores a Terceros');

            // Chain the setters
            $excel->setCreator('RenApp');

            $excel->setDescription('Reporte de acreedores a Terceros');

            $excel->sheet('Hoja 1', function ($sheet) use ($data, $columns) {
                $sheet->appendRow($columns); // column names

                foreach ($data as $row) {
                    $rowData = [];
                    foreach ($columns as $column) {
                        if ($column == 'CODIGO_BANCO_DE_LA_CUENTA_DEL_ACREEDOR'
                            || $column == 'NO_DE_CUENTA_DEL_ACREEDOR'
                            || $column == 'NOMBRE_DE_LA_POBLACION_DE_ACUERDO_AL_DANE'
                            || $column == 'CORRIENTE_AHORROS_CHEQUE'
                            || $column == 'CODIGO_DEL_DEPARTAMENTO') {
                            $rowData[] = "\t" . $row->{$column};
                        } else {
                            $rowData[] = $row->{$column};
                        }
                    }
                    $sheet->appendRow($rowData);
                }
            });

        })->export('xls');

    }

    public function reportUfCreDelimited(Request $req, $cpath)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $clientName = $client->name;
        $initDate = $req->cre_start_date_submit;
        $lastDate = $req->cre_end_date_submit;

        $columns = array_map(function ($column) {
            return $column->Field;
        }, DB::select(DB::raw('SHOW COLUMNS FROM colpensiones_forecast_cre_uf')));

        $data = \DB::table("colpensiones_forecast_cre_uf")->where('FECHA_OFICIO', '>=', $initDate . ' 00:00:00')->where('FECHA_OFICIO', '<=', $lastDate . ' 23:59:59')->orderBy('ID_SERVICIO')->orderBy('FECHA_INICIO_IT')->get();

        \Excel::create('Reporte_de_CRE_DESFAVORABLES_' . $clientName . '_' . date('Y-m-d'), function ($excel) use ($data, $columns) {

            $excel->setTitle('Reporte de cre desfavorable');

            // Chain the setters
            $excel->setCreator('RenApp');

            $excel->setDescription('Reporte de cre desfavorable');

            $excel->sheet('Hoja 1', function ($sheet) use ($data, $columns) {
                $sheet->appendRow($columns); // column names

                foreach ($data as $row) {
                    $rowData = [];
                    foreach ($columns as $column) {
                        if ($column == 'FECHA_OFICIO' || $column == 'FECHA_INICIO_IT' || $column == 'FECHA_FIN_IT') {
                            $rowData[] = \Carbon\Carbon::parse($row->{$column})->format('d/m/Y');
                        } else {
                            $rowData[] = $row->{$column};
                        }
                    }
                    $sheet->appendRow($rowData);
                }
            });

        })->export('xls');

    }

    public function reportInabilityIt701(Request $req, $cpath)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $clientName = $client->name;
        $columns = array_map(function ($column) {
            return $column->Field;
        }, DB::select(DB::raw('SHOW COLUMNS FROM colpensiones_inabilities_701')));

        $response = new StreamedResponse(function () use ($columns) {

            $out = fopen('php://output', 'w');

            fputcsv($out, $columns, "\t");

            DB::table('colpensiones_inabilities_701')->orderBy('NRO_RADICADO', 'ASC')->chunk(1000, function ($items) use ($out, $columns) {

                foreach ($items as $item) {

                    $data = [];
                    for ($i = 0; $i < count($columns); $i++) {
                        $data[] = mb_convert_encoding($item->{$columns[$i]}, 'utf-16', 'utf-8');
                    }

                    //Write excel
                    fputcsv($out, $data, "\t");
                }
            });

            fclose($out);

        }, 200, [
            'Content-Encoding' => 'UTF-8',
            'Content-Type' => 'application/vnd.ms-excel; charset=utf-8',
            'Content-Disposition' => 'attachment; filename="701_Incapacidades_' . strtoupper($clientName) . '_' . date('Y-m-d') . '.xls"',
        ]);


        return $response;
    }

    public function reportInabilityIt410(Request $req, $cpath)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $clientName = $client->name;
        $columns = array_map(function ($column) {
            return $column->Field;
        }, DB::select(DB::raw('SHOW COLUMNS FROM colpensiones_it_410')));

        $response = new StreamedResponse(function () use ($columns) {

            $out = fopen('php://output', 'w');

            fputcsv($out, $columns, "\t");

            DB::table('colpensiones_it_410')->orderBy('NRO_RADICADO', 'ASC')->chunk(1000, function ($items) use ($out, $columns) {

                foreach ($items as $item) {

                    $data = [];
                    for ($i = 0; $i < count($columns); $i++) {
                        $data[] = mb_convert_encoding($item->{$columns[$i]}, 'utf-16', 'utf-8');
                    }

                    //Write excel
                    fputcsv($out, $data, "\t");
                }
            });

            fclose($out);

        }, 200, [
            'Content-Encoding' => 'UTF-8',
            'Content-Type' => 'application/vnd.ms-excel; charset=utf-8',
            'Content-Disposition' => 'attachment; filename="410_Incapacidades_' . strtoupper($clientName) . '_' . date('Y-m-d') . '.xls"',
        ]);


        return $response;
    }

    public function reportMdi(Request $req, $cpath)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $clientName = $client->name;
        $columns = array_map(function ($column) {
            return $column->Field;
        }, DB::select(DB::raw('SHOW COLUMNS FROM colpensiones_mdi')));

        $response = new StreamedResponse(function () use ($columns) {

            $out = fopen('php://output', 'w');

            fputcsv($out, $columns, "\t");

            DB::table('colpensiones_mdi')->orderBy('FECHA_REGISTRO', 'ASC')->chunk(1000, function ($items) use ($out, $columns) {

                foreach ($items as $item) {

                    $data = [];
                    for ($i = 0; $i < count($columns); $i++) {
                        $data[] = mb_convert_encoding($item->{$columns[$i]}, 'utf-16', 'utf-8');
                    }

                    //Write excel
                    fputcsv($out, $data, "\t");
                }
            });

            fclose($out);

        }, 200, [
            'Content-Encoding' => 'UTF-8',
            'Content-Type' => 'application/vnd.ms-excel; charset=utf-8',
            'Content-Disposition' => 'attachment; filename="Reporte_MDI_' . strtoupper($clientName) . '_' . date('Y-m-d') . '.xls"',
        ]);


        return $response;
    }

    public function reportActionsByMonth(Request $req, $cpath)
    {
        $month = $req->month;
        $year = $req->year;

        $columns = array_map(function ($column) {
            return $column->Field;
        }, DB::select(DB::raw('SHOW COLUMNS FROM how_many_actions_by_month')));
        $meses = ['01' => 'ENERO', '02' => 'FEBRERO', '03' => 'MARZO', '04' => 'ABRIL', '05' => 'MAYO', '06' => 'JUNIO', '07' => 'JULIO', '08' => 'AGOSTO', '09' => 'SEPTIEMBRE', '10' => 'OCTUBRE', '11' => 'NOVIEMBRE', '12' => 'DICIEMBRE'];
        $nombreArchivo = 'ACCIONES_' . $meses[$month] . '_' . $year . '.xls';
        // Remueve las columnas MES y ANIO del array de columnas
        $response = new StreamedResponse(function () use ($columns, $month, $year) {

            $out = fopen('php://output', 'w');

            fputcsv($out, $columns, "\t");

            $connection = DB::connection()->table("how_many_actions_by_month");

            if ($month) {
                $connection->where('MES', '=', $month);
            }
            if ($year) {
                $connection->where('ANIO', '=', $year);
            }

            $connection->orderBy('FECHA_ACCION', 'ASC')->chunk(5000, function ($items) use ($out, $columns) {

                foreach ($items as $item) {

                    $data = [];
                    for ($i = 0; $i < count($columns); $i++) {
                        $data[] = mb_convert_encoding($item->{$columns[$i]}, 'utf-16', 'utf-8');
                    }
                    //Write excel
                    fputcsv($out, $data, "\t");
                }
            });

            fclose($out);

        }, 200, [
            'Content-Encoding' => 'UTF-8',
            'Content-Type' => 'application/vnd.ms-excel; charset=utf-8',
            'Content-Disposition' => 'attachment; filename="' . $nombreArchivo . '"',
        ]);

        return $response;
    }

    public function downloadReportIntermedaryHolder(Request $req, $cpath)
    {
        try {
            // Buscar el cliente por el path
            $client = Client::where('path', $cpath)->firstOrFail();
            $clientName = $client->name;

            // Capturar las fechas del formulario
            $initDate = Carbon::parse($req->report_start_date_submit)->startOfDay();
            $lastDate = Carbon::parse($req->report_end_date_submit)->endOfDay();

            // Validar que las fechas no sean nulas
            if (empty($initDate) || empty($lastDate)) {
                throw new \Exception("Las fechas no pueden estar vacías, en la sección 'Reporte intermediario y tomador'");
            }

            // Validar que $lastDate no sea menor que $initDate
            if ($lastDate->lessThan($initDate)) {
                throw new \Exception("La fecha de fin no puede ser menor a la fecha de inicio.");
            }

            // Consulta a la vista
            $data = DB::table('report_intermedary_holder_view')
                ->whereRaw("(created_at_quotation BETWEEN ? AND ?) OR (created_at_policy BETWEEN ? AND ?)", [
                    $initDate, $lastDate,
                    $initDate, $lastDate
                ])
                ->get();

            //si no hay datos
            if ($data->isEmpty()) {
                throw new \Exception("No se encontraron datos en el rango de fechas proporcionado.");
            }

            // Construir datos para Excel
            $activities = [];
            foreach ($data as $item) {
                $activities[] = [
                    mb_strtoupper($item->servicio, 'UTF-8'),
                    $item->consecutivo,
                    mb_strtoupper($item->method_payment, 'UTF-8'),
                    $item->created_date,
                    mb_strtoupper($item->brokerage_name, 'UTF-8'),
                    mb_strtoupper($item->advisor_name, 'UTF-8'),
                    mb_strtoupper($item->affiliate_name, 'UTF-8'),
                    $item->currency,
                    number_format($item->amount_annual_premium, 2, ',', '.'),
                    $item->trabajadores,
                    mb_strtoupper($item->work_modality_description, 'UTF-8'),
                    mb_strtoupper($item->economic_activity_name, 'UTF-8'),
                    mb_strtoupper($item->state_service, 'UTF-8'),
                    $item->number_workers,
                    $item->fee
                ];
            }

            $headings = [
                'SERVICIO', 'NO DE PÓLIZA','FORMA DE PAGO', 'FECHA DE CREACIÓN', 'CORREDURÍA', 'ASESOR', 'TOMADOR',
                'MONEDA', 'PRIMA ANUAL', 'TRABAJADORES', 'MODALIDAD DE ASEGURAMIENTO', 'ACTIVIDAD ECONÓMICA',
                'ESTADO DEL SERVICIO','CANTIDAD DE TRABAJADORES','TARIFA'
            ];

            return Excel::create('Reporte_intermediario_tomador', function ($excel) use ($activities, $headings) {
                $excel->sheet('Entries', function ($sheet) use ($activities, $headings) {
                    $sheet->row(1, $headings);
                    $sheet->fromArray($activities, null, 'A2', false, false);
                });
            })->download('xlsx');

            // Crear consulta para obtener los datos
//            $data = \DB::table('quotations')
//                ->select(
//                    \DB::raw("'Cotización' as servicio"),
//                    \DB::raw("'No aplica' as consecutivo"),
//                    'aa.created_at as created_date',
//                    'quotations.brokerage_name as brokerage_name',
//                    'quotations.advisor_name as advisor_name',
//                    'affiliates.full_name as affiliate_name',
//                    'quotations.type_currency as currency',
//                    'quotations.amount_policy as amount_annual_premium',
//                    \DB::raw("'No aplica' as trabajadores"),
//                    \DB::raw("CASE
//                    WHEN quotations.work_modality_id = 1 THEN 'Riesgo de Trabajo General'
//                    WHEN quotations.work_modality_id = 2 THEN 'Riesgos del Trabajo Especial Formación Técnica Dual'
//                    WHEN quotations.work_modality_id = 3 THEN 'Riesgos del Trabajo Hogar'
//                    WHEN quotations.work_modality_id = 4 THEN 'Riesgos del Trabajo Ocasional'
//                    WHEN quotations.work_modality_id = 5 THEN 'Riesgos del Trabajo Sector Publico'
//                    WHEN quotations.work_modality_id = 6 THEN 'Riesgos del Trabajo Independiente'
//                    WHEN quotations.work_modality_id = 7 THEN 'Riesgos del Trabajo único Trabajador'
//                    ELSE 'Desconocido'
//                 END as work_modality_description"),
//                    'ea.activity_name as economic_activity_name',
//                    \DB::raw("REPLACE(FORMAT(TRUNCATE(CONVERT(REPLACE(ea.percentage, ',', '.'), DECIMAL(10, 8)), 2), 2), '.', ',') as economic_activity_percentage_number")
//                )
//                ->join('activities', 'quotations.activity_id', '=', 'activities.id')
//                ->join(
//                    \DB::raw("(
//                        SELECT activity_id, created_at
//                        FROM (
//                            SELECT activity_id, created_at,
//                                   ROW_NUMBER() OVER (PARTITION BY activity_id ORDER BY created_at ASC) AS rn
//                            FROM activity_actions
//                            WHERE action_id IN (3, 350)
//                            AND created_at BETWEEN '{$initDate} 00:00:00' AND '{$lastDate} 23:59:59'
//                        ) aa_ranked
//                        WHERE rn = 1
//                    ) as aa"),
//                    'aa.activity_id', '=', 'activities.id'
//                )
//                ->leftJoin('states', 'activities.state_id', '=', 'states.id')
//                ->leftJoin('affiliates', 'activities.affiliate_id', '=', 'affiliates.id')
//                ->leftJoin('economic_activities as ea', 'ea.code', '=', 'quotations.activity_economic_id')
//                ->whereBetween('aa.created_at', [$initDate . ' 00:00:00', $lastDate . ' 23:59:59'])
//                ->orderBy('quotations.created_at', 'desc')
//                ->union(
//                    \DB::table('policy_sorts')
//                        ->select(
//                            \DB::raw("'Póliza' as servicio"),
//                            \DB::raw("CONCAT(policy_sorts.consecutive, '') AS consecutive_string"),
//                            'aa.created_at as created_date',
//                            'policy_sorts.brokerage_name as brokerage_name',
//                            'policy_sorts.advisor_name as advisor_name',
//                            'affiliates.full_name as affiliate_name',
//                            'policy_sorts.type_currency as currency',
//                            'policy_sorts.amount_policy as amount_annual_premium',
//                            \DB::raw("COALESCE(
//                                (SELECT CONCAT(policy_spreadsheets.total_affiliates, '')
//                                 FROM policy_spreadsheets
//                                 WHERE policy_spreadsheets.activity_id IN (
//                                     SELECT MAX(activities_policy_spreadsheets.id)
//                                     FROM activities AS activities_policy_spreadsheets
//                                     WHERE activities_policy_spreadsheets.parent_id = activities.id
//                                       AND activities_policy_spreadsheets.service_id = 79
//                                       AND activities_policy_spreadsheets.state_id IN (57)
//                                 )
//                                 ORDER BY policy_spreadsheets.id DESC
//                                 LIMIT 1),
//                                '0') AS total_workers_policy"),
//                            \DB::raw("CASE
//                            WHEN policy_sorts.work_modality_id = 1 THEN 'Riesgo de Trabajo General'
//                            WHEN policy_sorts.work_modality_id = 2 THEN 'Riesgos del Trabajo Especial Formación Técnica Dual'
//                            WHEN policy_sorts.work_modality_id = 3 THEN 'Riesgos del Trabajo Hogar'
//                            WHEN policy_sorts.work_modality_id = 4 THEN 'Riesgos del Trabajo Ocasional'
//                            WHEN policy_sorts.work_modality_id = 5 THEN 'Riesgos del Trabajo Sector Publico'
//                            WHEN policy_sorts.work_modality_id = 6 THEN 'Riesgos del Trabajo Independiente'
//                            WHEN policy_sorts.work_modality_id = 7 THEN 'Riesgos del Trabajo Único Trabajador'
//                            ELSE 'Desconocido'
//                         END as work_modality_description"),
//                            'ea.activity_name as economic_activity_name',
//                            \DB::raw("REPLACE(FORMAT(TRUNCATE(CONVERT(REPLACE(ea.percentage, ',', '.'), DECIMAL(10, 8)), 2), 2), '.', ',') as economic_activity_percentage_number")
//                        )
//                        ->join('activities', 'policy_sorts.activity_id', '=', 'activities.id')
//                        ->join('activity_actions as aa', function ($join) {
//                            $join->on('aa.activity_id', '=', 'activities.id')
//                                ->where('aa.action_id', '=', 16);
//                        })
//                        ->leftJoin('states', 'activities.state_id', '=', 'states.id')
//                        ->leftJoin('affiliates', 'activities.affiliate_id', '=', 'affiliates.id')
//                        ->leftJoin('economic_activities as ea', 'ea.code', '=', 'policy_sorts.activity_economic_id')
//                        ->whereBetween('aa.created_at', [$initDate . ' 00:00:00', $lastDate . ' 23:59:59']) // Filtrar por rango de fechas
//                        ->where('activities.state_id', '<>', StatePoliza::TRAMITE_ANULADO)
//                        ->orderBy('policy_sorts.created_at', 'desc')
//                )
//                ->get();
        } catch (\Exception $e) {
            // Capturar el error y redirigir con mensaje
            return redirect('/admin/reportes/reportesIntermediarioYTomador')
                ->withErrors($e->getMessage())
                ->withInput();
        }
    }

    public function reportPrimas(Request $req, $cpath)
    {
        try {
            $initDate = $req->primas_start_date_submit;
            $lastDate = $req->primas_end_date_submit;

            if (empty($initDate) || empty($lastDate)) {
                throw new \Exception("Las fechas no pueden estar vacías, en la sección 'Reporte de primas'");
            }

            if (strtotime($lastDate) < strtotime($initDate)) {
                throw new \Exception("La fecha desde no puede ser menor a la fecha hasta, en la sección 'Reporte de primas'");
            }

            $query = $this->queryReportPrimas($initDate, $lastDate);

            $headings = [
                'ID PÓLIZA', 
                'TIPO ID',
                'ID TOMADOR',
                'CANAL',
                'NOMBRE TOMADOR',
                'CÓD. INTERMEDIARIO',
                'NOMBRE DEL INTERMEDIARIO',
                'CÓD. CORREDOR',
                'NOMBRE DEL CORREDOR',
                'FECHA DE APROBACIÓN DEL PAGO',
                'FECHA VIGENCIA INICIO PÓLIZA',
                'FECHA VIGENCIA FIN DE PÓLIZA',
                'FECHA INICIO RECIBO',
                'FECHA FIN RECIBO',
                'FRACCIONAMIENTO',
                'TASA DE CAMBIO EMISIÓN',
                'PRIMA ANUAL MONEDA',
                'PRIMA ANUAL DOLAR',
                'PRIMA ANUAL LOCAL',
                'PRIMA PAGADA MONEDA',
                'PRIMA PAGADA LOCAL',
                'PRIMA PAGADA DOLAR',
                'FECHA EMISIÓN',
                'FECHA DE APROBACION DE ANULACIÓN DE LA PÓLIZA',
                'FECHA DE ANULACIÓN DEL RECIBO',
                'TIPO DE MOVIMIENTO',
                'PRIMA EMITIDA',
                'PRIMA EMITIDA LOCAL',
                'PRIMA EMITIDA DÓLAR',
                '% COMISIÓN PAGADA AL INTERMEDIARIO',
                'MONEDA DE LA PÓLIZA',
                '# TRABAJADORES MOVIMIENTO',
                'NÓMINA DEL MOVIMIENTO',
                'PRIMA ACUMULADA DE LA VIGENCIA MONEDA',
                '% CONTRATO',
                '% FACULTATIVO',
                'COMPROBANTE DE CONTABILIDAD',
                'PRIMA PENDIENTE POR PAGO EN MONEDA',
                'TIPO PÓLIZA',
                'PROYECCIÓN SALARIOS',
                'ACTIVIDAD ECONÓMICA GENERAL',
                'ACTIVIDAD ECONÓMICA ESPECIFICADA',
            ];  

            $activities = [];

            foreach ($query as $item) {
                $activities[] = [
                    $item->policy_id,
                    $item->doc_type,
                    $item->doc_number,
                    $item->canal ?? '',
                    $item->full_name,
                    $item->cod_intermediario ?? '',
                    $item->nombre_intermediario ?? '',
                    $item->cod_corredor ?? '',
                    $item->nombre_corredor ?? '',
                    $item->fecha_aprob ?? '',
                    $item->validity_from ?? '',
                    $item->validity_to ?? '',
                    $item->fecha_ini_recibo ?? '',
                    $item->fecha_fin_recibo ?? '',
                    $item->periodicity_description ?? '',
                    floatval($item->trm ?? 0),
                    floatval($item->prima_moneda ?? 0),
                    floatval($item->prima_dolar ?? 0),
                    floatval($item->prima_local ?? 0),
                    floatval($item->prima_pagada ?? 0),
                    floatval($item->prima_pagada_colonizada ?? 0),
                    floatval($item->prima_pagada_dolarizada ?? 0),
                    $item->fecha_pago,
                    $item->expiration_notice_date,
                    $item->date_suspend,
                    $item->tipo_mov ?? '',
                    floatval($item->prima_emitidad ?? 0),
                    floatval($item->prima_emitida_local ?? 0),
                    floatval($item->prima_emitida_dolar ?? 0),
                    $item->por_comi,
                    $item->type_currency ?? '',
                    $item->total_affiliates,
                    floatval($item->salary_projection ?? 0),
                    floatval($item->total_alafecha ?? 0),
                    $item->por_contrato,
                    $item->por_facultativo,
                    $item->comp_contabilida ?? '',
                    floatval($item->tota_pend ?? 0),
                    $item->temporality ?? '',
                    floatval($item->proyeccion_salarios ?? 0),
                    $item->branch_name ?? '',
                    $item->actividad_eco ?? '',
                ];
            }

            return Excel::create('reportePrimas', function ($excel) use ($activities, $headings) {
                $excel->sheet('Entries', function ($sheet) use ($activities, $headings) {
                    $sheet->row(1, $headings);
                    $sheet->fromArray($activities, null, 'A2', false, false);

                    $rowIndex = 2;
                    foreach ($activities as $activity) {
                        // Fuerza los valores 0 como flotantes
                        $activity[15] = (float) $activity[15];
                        $activity[16] = (float) $activity[16];
                        $activity[17] = (float) $activity[17];
                        $activity[18] = (float) $activity[18];
                        $activity[19] = (float) $activity[19];
                        $activity[20] = (float) $activity[20];
                        $activity[21] = (float) $activity[21];
                        $activity[26] = (float) $activity[26];
                        $activity[27] = (float) $activity[27];
                        $activity[28] = (float) $activity[28];
                        $activity[32] = (float) $activity[32];
                        $activity[33] = (float) $activity[33];
                        $activity[37] = (float) $activity[37];
                        $activity[39] = (float) $activity[39];

                        $sheet->row($rowIndex++, $activity);
                    }


                    // Calcular la última fila
                    $lastRow = count($activities) + 1; // +1 por la fila de encabezados

                    $sheet->setColumnFormat([
                        // Fechas (columnas +1)
                        'J2:J'.$lastRow   => 'dd/mm/yyyy',
                        'K2:K'.$lastRow   => 'dd/mm/yyyy',
                        'L2:L'.$lastRow   => 'dd/mm/yyyy',
                        'M2:M'.$lastRow   => 'dd/mm/yyyy',
                        'N2:N'.$lastRow   => 'dd/mm/yyyy',
                        'W2:W'.$lastRow   => 'dd/mm/yyyy',
                        'X2:X'.$lastRow   => 'dd/mm/yyyy',
                        'Y2:Y'.$lastRow   => 'dd/mm/yyyy',

                        // Números con decimales (columnas +1)
                        'P2:P'.$lastRow   => '0.00',
                        'Q2:Q'.$lastRow   => '0.00',
                        'R2:R'.$lastRow   => '0.00',
                        'S2:S'.$lastRow   => '0.00',
                        'T2:T'.$lastRow   => '0.00',
                        'U2:U'.$lastRow   => '0.00',
                        'V2:V'.$lastRow   => '0.00',
                        'AA2:AA'.$lastRow => '0.00',
                        'AB2:AB'.$lastRow => '0.00',
                        'AC2:AC'.$lastRow => '0.00',
                        'AG2:AG'.$lastRow => '0.00',
                        'AH2:AH'.$lastRow => '0.00',
                        'AL2:AL'.$lastRow => '0.00',
                        'AN2:AN'.$lastRow => '0.00',
                    ]);

                    $numericColumns = ['J', 'K', 'L', 'M', 'N', 'W', 'X', 'Y'];

                    foreach ($numericColumns as $col) {
                        $sheet->cells($col.'2:'.$col.$lastRow, function($cells) {
                            $cells->setAlignment('right');
                        });
                    }

                });
            })->download('xlsx');


        } catch (\Exception $e) {
            return redirect('/admin/reportes/descargas_admin')
                ->withErrors($e->getMessage())
                ->withInput();
        }

    }

    public function queryReportPrimas($initDate, $lastDate)
    {

        $consulta =  "
                    select
                        t.id AS id,
                        concat('SORT-', lpad(ps.consecutive, 4, '0')) AS policy_id,
                        ps.activity_id AS activity_id,
                        a.parent_id AS parent_id,
                          (case
                            when (pspb.total_affiliates is null) then coalesce(ps.number_workers_optional, '0')
                            else pspb.total_affiliates end) AS total_affiliates,
                        (case
                            when (ps.periodicity = 4) then psp.total_salaries
                            else pspb.total_salaries
                        end) AS total_salaries,
                        ps.type_currency AS type_currency,
                        t.calculated_amount AS amount_policy,
                        cast(t.transaction_date as date) AS created_at,
                        date_format(acc.created_at, '%d/%m/%Y') AS expiration_notice_date,
                        case when t.type_receipt = 'cancelacion' then  date_format(t.transaction_date, '%d/%m/%Y') end AS date_suspend,
                        ps.brokerage_name AS brokerage_name,
                        ps.code AS code,
                        (case
                            when (ps.periodicity = 4) then psp.total_salaries
                            else pspb.total_salaries
                        end) AS salary_projection,
                        ps.doc_type AS doc_type,
                        date_format(ps.validity_from, '%d/%m/%Y') AS validity_from,
                        date_format(ps.validity_to, '%d/%m/%Y') AS validity_to,
                        afp.doc_number AS doc_number,
                        af.full_name AS full_name,
                        ps.annual_calculation_amount AS annual_calculation_amount,
                        (case
                            when (ps.periodicity = 0) then 'Pago único'
                            when (ps.periodicity = 1) then 'Anual'
                            when (ps.periodicity = 2) then 'Semestral'
                            when (ps.periodicity = 3) then 'Trimestral'
                            when (ps.periodicity = 4) then 'Mensual'
                            else 'Pago único'
                        end) AS periodicity_description,
                        case
                            when t.type_receipt = 'cancelacion' then 
                                date_format(t.transaction_date, '%d/%m/%Y')
                            else
                                date_format(r.fecha_ini_recibo, '%d/%m/%Y')
                        end as fecha_ini_recibo,
                        case
                            when t.type_receipt = 'cancelacion' then 
                                date_format(ps.validity_to, '%d/%m/%Y')
                            else
                                date_format(r.fecha_fin_recibo, '%d/%m/%Y')
                        end as fecha_fin_recibo,
                        (case
                            when (ps.temporality = 'short') then 'Corto'
                            else 'Permanente'
                        end) AS temporality,
                       case
                            when t.type_receipt = 'cancelacion' then 
                                case when ps.periodicity='1' or ps.temporality='short' then 
                                 ps.amount_policy-t.total_amount
                                else 
                                t.calculated_amount - tp.total_amount_abono
                                end 
                            else
                                t.PRIMA_ANUAL_LOCAL_FINAL
                        end AS prima_moneda,
                        case
                            when (ps.type_currency <> 'USD') then 
                                (case
                                    when t.type_receipt = 'cancelacion' then 
                                        case when ps.periodicity='1' or ps.temporality='short' then 
                                         ps.amount_policy-t.total_amount
                                        else 
                                        t.calculated_amount - tp.total_amount_abono
                                        end 
                                    else
                                        t.PRIMA_ANUAL_LOCAL_FINAL
                                end) / pct.trm
                            else 
                                case
                                    when t.type_receipt = 'cancelacion' then 
                                        case when ps.periodicity='1' or ps.temporality='short' then 
                                         ps.amount_policy-t.total_amount
                                        else 
                                        t.calculated_amount - tp.total_amount_abono
                                        end 
                                    else
                                        t.PRIMA_ANUAL_LOCAL_FINAL
                                end
                        end AS prima_dolar,
                        case
                            when (ps.type_currency = 'USD') then
                                (case
                                    when t.type_receipt = 'cancelacion' then 
                                        case when ps.periodicity='1' or ps.temporality='short' then 
                                         ps.amount_policy-t.total_amount
                                        else 
                                        t.calculated_amount - tp.total_amount_abono
                                        end 
                                    else
                                        t.PRIMA_ANUAL_LOCAL_FINAL
                                end)* pct.trm
                            else 
                                case
                                    when t.type_receipt = 'cancelacion' then 
                                        case when ps.periodicity='1' or ps.temporality='short' then 
                                         ps.amount_policy-t.total_amount
                                        else 
                                        t.calculated_amount - tp.total_amount_abono
                                        end 
                                    else
                                        t.PRIMA_ANUAL_LOCAL_FINAL
                                end
                        end AS prima_local,
                        t.calculated_amount * case when ps.type_currency = 'USD' then pct.trm else 1 end AS prima_coloniza,
                        t.calculated_amount / case when ps.type_currency = 'USD' then 1 else pct.trm end AS prima_dolarizada,
                        t.total_amount AS prima_pagada,
                        t.total_amount * case when (ps.type_currency = 'USD') then t.trm else 1 end AS prima_pagada_colonizada,
                        t.total_amount / case when ps.type_currency = 'USD' then 1 else t.trm end AS prima_pagada_dolarizada,
                        (case
                            when (up.cod_intermediario is not null) then up.cod_corredor
                        end) AS cod_corredor,
                        (case
                            when (up.cod_intermediario is null) then up.cod_corredor
                            else up.cod_intermediario
                        end) AS cod_intermediario,
                        case when up.cod_intermediario = '000013' then 0 else 10 end AS por_comi,
                        case
                            when (ps.temporality = 'short') then (ps.salary_projection * timestampdiff(MONTH,
                            ps.validity_from,
                            ps.validity_to))
                            else (ps.salary_projection * 12)
                        end AS proyeccion_salarios,
                        ps.periodicity AS periodicity,                     
                        ace.receipt_number AS comp_contabilida,
                        pct.trm AS trm,
                        date_format(ac.created_at, '%d/%m/%Y') AS fecha_pago,                           
                        ps.brokerage_name AS nombre_intermediario,
                        case when trim(lower(ps.brokerage_name)) = trim(lower(ps.advisor_name)) then '' else ps.advisor_name end AS nombre_corredor,
                        case
                            when t.type_receipt = 'cancelacion' then 'Cancelación'
                            when t.indice = 1 then 'Emisión'
                            else 'Abono'
                        end AS tipo_mov,
                         t.total_amount * (case t.periodicity  
                                            when 4 then 12-t.indice
                                            when 2 then 2-t.indice
                                            when 3 then 4-t.indice
                                            when 1 then 0 
                                            else 0 end) AS tota_pend,
                        DATE_FORMAT(t.fecha_aprob, '%d/%m/%Y') as fecha_aprob,
                        t.total_amount_acumulado as total_alafecha,
                        t.PRIMA_EMITIDAD as prima_emitidad,
                            case when year(ps.validity_from) = '2025' then '30' else 0 end AS por_contrato,
                            '' AS por_facultativo,
                        t.PRIMA_EMITIDAD * CASE WHEN ps.type_currency = 'USD' then pct.trm  else 1 end as prima_emitida_local,
                        t.PRIMA_EMITIDAD / CASE WHEN ps.type_currency <> 'USD' then pct.trm  else 1 end as prima_emitida_dolar,
                       case when up.cod_intermediario = '000013' then 'MNK' 
                          else 
                             case when trim(lower(ps.brokerage_name)) = trim(lower(ps.advisor_name)) then 'AGENTES' else 'CORREDURIAS' end
                          end AS canal,
                      eb.branch_name,
                      ea.activity_name AS actividad_eco
                    FROM (
                        select tt.*,
                            CASE WHEN tt.indice = 1 THEN
                                    tt.PRIMA_ANUAL_LOCAL_FINAL	
                                ELSE
                                    tt.PRIMA_ANUAL_LOCAL_FINAL -  LAG(tt.PRIMA_ANUAL_LOCAL_FINAL) OVER (ORDER BY tt.consecutive, tt.id) 
                                END AS PRIMA_EMITIDAD
                        from (
                            SELECT
                                    ps.id,
                                    p.consecutive,
                                    p.periodicity,
                                    CASE
                                        WHEN p.periodicity = '1' THEN 'Anual'
                                        WHEN p.periodicity = '2' THEN 'Semestral'
                                        WHEN p.periodicity = '3' THEN 'Trimestral'
                                        WHEN p.periodicity = '4' THEN 'Mensual'
                                        ELSE 'Periodo corto'
                                    END AS fracionamiento,
                                    p.validity_from AS validity_from,
                                    p.validity_to AS validity_to,
                                    CASE
                                        WHEN p.periodicity = 4 THEN 1
                                        WHEN p.periodicity = 3 THEN 3
                                        WHEN p.periodicity = 2 THEN 6
                                        ELSE 12
                                    END AS meses,
                                    ps.trm AS trm,
                                    ps.total_amount AS total_amount,
                                    ps.transaction_date AS transaction_date,
                                    CASE
                                        WHEN p.temporality = 'permanent' THEN 
                                            CASE p.periodicity 
                                                WHEN 1 THEN p.annual_calculation_amount * 1
                                                WHEN 2 THEN p.semiannual_calculation_amount * 2
                                                WHEN 3 THEN p.quarterly_calculation_amount * 4
                                                WHEN 4 THEN p.monthly_calculation_amount * 12
                                                ELSE p.annual_calculation_amount
                                            END
                                        ELSE p.single_payment_value
                                    END AS calculated_amount,
                                    ac.fecha_cancelacion,
                                    ps.total_amount + (ps.total_amount * (case p.periodicity  
                                                                                when 4 then 12-(ROW_NUMBER() OVER (PARTITION BY p.consecutive ORDER BY ps.id))
                                                                                when 2 then 2-(ROW_NUMBER() OVER (PARTITION BY p.consecutive ORDER BY ps.id))
                                                                                when 3 then 4-(ROW_NUMBER() OVER (PARTITION BY p.consecutive ORDER BY ps.id))
                                                                                when 1 then 0 
                                                                                else 0 end )) as TOTAL_PEND_FINAL,
                                    ps.type_receipt,
                                    (SUM(ps.total_amount) OVER (PARTITION BY p.consecutive ORDER BY ps.id)) 
                                    +
                                    ( ps.total_amount 
                                    * 
                                    (case p.periodicity  
                                        when 4 then 12-(ROW_NUMBER() OVER (PARTITION BY p.consecutive ORDER BY ps.id))
                                        when 2 then 2-(ROW_NUMBER() OVER (PARTITION BY p.consecutive ORDER BY ps.id))
                                        when 3 then 4-(ROW_NUMBER() OVER (PARTITION BY p.consecutive ORDER BY ps.id))
                                        when 1 then 0 
                                        else 0 end ) ) as PRIMA_ANUAL_LOCAL_FINAL,
                                    date(aa.created_at) AS fecha_aprob,
                                    SUM(ps.total_amount) OVER (PARTITION BY p.consecutive ORDER BY ps.id) AS total_amount_acumulado,
                                    ROW_NUMBER() OVER (PARTITION BY p.consecutive ORDER BY ps.id) AS indice
                                FROM policy_sort_collections ps
                                LEFT JOIN activities a ON a.id = ps.activity_id
                                LEFT JOIN policy_sorts p ON p.activity_id = a.parent_id
                                LEFT JOIN activity_actions aa ON aa.activity_id = ps.activity_id 
                                    AND action_id IN (210,216,220,221,226,183,203,217,219,218,223,182,224,332)
                                left join (
                                    select date(a.created_at) as fecha_cancelacion ,number_payment 
                                    from accounting_entries a 
                                    where receipt_type = 002
                                    group by date(a.created_at), number_payment ) ac on ac.number_payment = ps.id
                                WHERE p.consecutive IS NOT NULL AND ps.payment_status = 'approved'
                                    ORDER BY p.consecutive, ps.id
                            ) tt
                                ) t
                    LEFT JOIN report_payments_view r on r.cuota_numero=t.indice and r.consecutive=t.consecutive
                    LEFT JOIN policy_sorts ps ON ps.consecutive = t.consecutive
                    LEFT JOIN activities a ON ps.activity_id = a.parent_id
                    LEFT JOIN affiliates af ON af.id = a.affiliate_id
                    LEFT JOIN activities ap ON ap.id = ps.activity_id
                    LEFT JOIN affiliates afp ON afp.id = ap.affiliate_id
                    LEFT JOIN (
                        SELECT
                            subquery.parent_id AS parent_id,
                            subquery.total_affiliates AS total_affiliates,
                            subquery.total_salaries AS total_salaries
                        FROM (
                            SELECT
                                a.parent_id AS parent_id,
                                p.total_affiliates AS total_affiliates,
                                p.total_salaries AS total_salaries,
                                ROW_NUMBER() OVER (PARTITION BY a.parent_id ORDER BY p.id DESC) AS indice
                            FROM policy_spreadsheets p
                            LEFT JOIN activities a ON a.id = p.activity_id
                            WHERE a.state_id IN (56,57)
                        ) AS subquery
                        WHERE subquery.indice = 1
                    ) AS pspb ON pspb.parent_id = ps.activity_id
                        left join (
                            select
                                a.parent_id AS parent_id,
                                p.id AS id,
                                p.total_affiliates AS total_affiliates,
                                p.total_salaries AS total_salaries,
                                row_number() OVER (PARTITION BY a.parent_id
                            ORDER BY
                                p.id ) AS indice
                            from
                                (policy_spreadsheets p
                            left join activities a on
                                ((a.id = p.activity_id)))
                            order by
                                a.parent_id,
                                p.id) psp on psp.parent_id = ps.activity_id and psp.indice = t.indice
                        left join (
                            select
                                a.parent_id AS parent_id,
                                count(0) AS cant_pagos,
                                sum(psc.total_amount) AS total_amount,
                                sum((case when (ps.type_currency = 'CRC') then psc.total_amount else (psc.total_amount * psc.trm) end)) AS total_amount_colones,
                            sum((case when (ps.type_currency = 'USD') then psc.total_amount else (psc.total_amount / psc.trm) end)) AS total_amount_dolares,
                            sum( case when date(aa.created_at) <= '$lastDate' then psc.total_amount else 0 end ) as total_alafecha,
                            sum( case when type_receipt <> 'cancelacion' then psc.total_amount else 0 end ) AS total_amount_abono
                        from policy_sort_collections psc
                        left join activities a on a.id = psc.activity_id
                        left join policy_sorts ps on ps.activity_id = a.parent_id
                        LEFT JOIN activity_actions aa ON aa.activity_id = psc.activity_id AND action_id IN (210,216,220,221,226,183,203,217,219,218,223,182,224,332)
                        where psc.payment_status = 'approved'
                        group by
                            a.parent_id) tp on tp.parent_id = ps.activity_id
                    left join (
                        select
                            replace((s.username collate utf8mb4_unicode_ci), 'CO-', '') AS username,
                            replace(s.code_mnk, 'CO-', '') AS cod_corredor,
                            replace(s.code_correduria, 'CO-', '') AS cod_intermediario,
                            s.advisor_name AS advisor_name,
                            s.brokerage_name AS brokerage_name
                        from
                            users s
                        group by
                            replace((s.username collate utf8mb4_unicode_ci), 'CO-', '')) up on up.username = replace((ps.code collate utf8mb4_unicode_ci), 'CO-', '')
                    left join (
                        select
                            trim(leading '0' from replace(a.number_policy, 'SORT-', '')) AS consecutive,
                            a.receipt_number AS receipt_number,
                            a.number_payment
                        from
                            accounting_entries a
                        where
                            (a.cod_oper = '001')
                        group by
                            a.number_policy,
                            a.receipt_number,
                            a.number_payment
                            ) ace on ace.consecutive = ps.consecutive and ace.number_payment = t.id
                    left join (
                        select
                            a.parent_id AS parent_id,
                            pss.trm AS trm
                        from
                            (policy_sort_collections pss
                        left join activities a on
                            ((a.id = pss.activity_id)))
                        where
                            ((pss.type_receipt = 'emission')
                                and (pss.payment_status = 'approved'))
                        group by
                            a.parent_id) pct on pct.parent_id = ps.activity_id
                    left join activity_actions ac on ac.activity_id = ps.activity_id and ac.action_id = 16
                    left join activity_actions acc on acc.activity_id = ps.activity_id and acc.action_id = 32
                    left join economic_activities ea on ea.code = ps.activity_economic_id
                    left join economic_branches eb on eb.id=ea.branch_id
                    where ap.state_id <> 196 and t.fecha_aprob BETWEEN '$initDate' AND '$lastDate' 
                    group by
                        t.id,
                        ps.consecutive;
                       
                       "; 

        $data = DB::select($consulta);

        return $data;
    }

    public function reportPendingPremiums(Request $req, $cpath)
    {
        try {
            $initDate = $req->primas_pendientes_start_date_submit;
            //$lastDate = $req->primas_end_date_submit;

            if (empty($initDate)) {
                throw new \Exception("Las fecha de calculo no pueden estar vacía, en la sección 'Reporte de primas pendientes'");
            }

            $webserviceController = new WebserviceAcselController();
            $trm = $webserviceController->getTrm();
            if ($trm == 0) {
                throw new \Exception('error', 'Error: El TRM no es valido');
            }

            $consulta = "SELECT * from report_pending_premiums_view WHERE estado COLLATE utf8mb4_0900_ai_ci <> 'pagado' AND fecha_ini_recibo > '$initDate' AND  '$initDate' >= fecha_emision";

            $query = DB::select($consulta);

            $headings = [
                'ID PÓLIZA',
                'FECHA INICIO RECIBO',
                'FECHA FIN RECIBO',
                'PRIMA PENDIENTE MONEDA',
                'PRIMA PENDIENTE DOLAR',
                'PRIMA PENDIENTE LOCAL',
                'TIPO DE MOVIMIENTO',
                'MONEDA DE LA PÓLIZA',
            ];
            
            $activities = [];

            foreach ($query as $item) {
                $activities[] = [
                    $item->consecutive_policy,
                    $item->fecha_ini_recibo,
                    $item->fecha_fin_recibo,
                    floatval($item->total_pend ?? 0),
                    floatval($item->total_pend_dolar ?? 0),
                    floatval($item->total_pend_local ?? 0),
                    $item->fraccionamiento,
                    $item->type_currency
                ];
            }

            return Excel::create('reportePrimasPendientes', function ($excel) use ($activities, $headings) {
                $excel->sheet('Entries', function ($sheet) use ($activities, $headings) {
                    $sheet->row(1, $headings);
                    $sheet->fromArray($activities, null, 'A2', false, false);

                    $rowIndex = 2;
                    foreach ($activities as $activity) {
                        // Fuerza los valores 0 como flotantes
                        $activity[3] = (float) $activity[3];
                        $activity[4] = (float) $activity[4];
                        $activity[5] = (float) $activity[5];
                        $sheet->row($rowIndex++, $activity);
                    }

                    // Calcular la última fila
                    $lastRow = count($activities) + 1; // +1 por la fila de encabezados

                    // Aplicar formato de fecha (día/mes/año)
                    $sheet->setColumnFormat([
                        // Formatos para fechas
                        'B2:B'.$lastRow => 'dd/mm/yyyy',
                        'C2:C'.$lastRow => 'dd/mm/yyyy',
                    
                        // Formatos para valores monetarios (2 decimales)
                        'D2:D'.$lastRow => '0.00',
                        'E2:E'.$lastRow => '0.00',
                        'F2:F'.$lastRow => '0.00',
                    ]);

                    // Establecer alineación a la derecha para columnas numéricas y fechas
                    $numericColumns = ['B', 'C'];

                    foreach ($numericColumns as $col) {
                        $sheet->cells($col.'2:'.$col.$lastRow, function($cells) {
                            $cells->setAlignment('right');
                        });
                    }

                });
            })->download('xlsx');

            // $response = new StreamedResponse(function () use ($columns, $initDate, $trm) {

            //     $out = fopen('php://output', 'w');

            //     fputcsv($out, mb_convert_encoding($columns, 'utf-16', 'utf-8'), "\t");

            //     $consulta = "SELECT * from report_pending_premiums_view WHERE estado COLLATE utf8mb4_0900_ai_ci <> 'pagado' AND fecha_ini_recibo > '$initDate' AND  '$initDate' >= fecha_emision";

            //     $query = DB::select($consulta);

            //     foreach ($query as $item) {
            //         $data = [
            //             $item->consecutive_policy,
            //             mb_convert_encoding($item->fecha_ini_recibo, 'utf-16', 'utf-8'),
            //             mb_convert_encoding($item->fecha_fin_recibo, 'utf-16', 'utf-8'),
            //             number_format($item->total_pend ?? 0, 2, ',', ''),
            //             number_format($item->total_pend_dolar ?? 0, 2, ',', ''),
            //             number_format($item->total_pend_local ?? 0, 2, ',', ''),
            //             mb_convert_encoding($item->fraccionamiento, 'utf-16', 'utf-8'),
            //             mb_convert_encoding($item->type_currency, 'utf-16', 'utf-8')
            //         ];
            //         fputcsv($out, $data, "\t");
            //     }
            //     fclose($out);

            // }, 200, [
            //     'Content-Encoding' => 'UTF-8',
            //     'Content-Type' => 'application/vnd.ms-excel; charset=utf-8',
            //     'Content-Disposition' => 'attachment; filename="reportePrimasPendientes' . '_' . date('Y-m-d') . '.xls"',
            // ]);

            // return $response;
        } catch (\Exception $e) {
            return redirect('/admin/reportes/descargas_admin')
                ->withErrors($e->getMessage())
                ->withInput();
        }

    }

    //MS-2210 REPORTE DE SINIESTROS
    public function reportIncidents(Request $req, $cpath)
    {
        try {
            $initDate = $req->primas_start_date_submit;
            $lastDate = $req->primas_end_date_submit;

            if (empty($initDate) || empty($lastDate)) {
                throw new \Exception("Las fechas no pueden estar vacías, en la sección 'Reporte de siniestros'");
            }

            if (strtotime($lastDate) < strtotime($initDate)) {
                throw new \Exception("La fecha desde no puede ser menor a la fecha hasta, en la sección 'Reporte de siniestros'");
            }

            $consulta = "select * from report_incidents_view where created_at BETWEEN :initDate AND :lastDate ";

            $query = DB::select($consulta, [
                'initDate' => $initDate . ' 00:00:00',
                'lastDate' => $lastDate . ' 23:59:59'
            ]);

            $headings = [
                'ID SINIESTRO',
                'NO CASO',
                'COMPROBANTE CONTABLE',
                'ID PÓLIZA',
                'COD  CORREDOR',
                'COD INTERMEDIARIO',
                'NOMBRE CORREDOR',
                'NOMBRE INTERMEDIARIO',
                'NOMBRE DEL TOMADOR',
                'ID CLIENTE',
                'ID TRABAJADOR AFECTADO',
                'NOMBRE TRABAJADOR AFECTADO',
                'MONEDA DEL SINIESTRO',
                'FECHA DE MOVIMIENTO',
                'TIPO DE MOVIMIENTO',
                'MONTO MOVIMIENTO MONEDA',
                'MONTO MOVIMIENTO LOCAL',
                'MONTO MOVIMIENTO DÓLAR',

                'TASA DE CAMBIO FECHA MOV', //nueva

                'COBERTURA AFECTADA',
                'FECHA DE REPORTE FORMAL DEL ACCIDENTE',
                'FECHA OCURRENCIA DEL SINIESTRO',
                'FECHA NOTIFICACIÓN DEL SINIESTRO',
                'FECHA INICIO DE INCAPACIDAD',
                'FECHA FIN DE INCAPACIDAD',
                'TIPO DE INCAPACIDAD',
                'TIPO DE CONTRATANTE',
                'ACTIVIDAD ECONÓMICA GENERAL',
                'ACTIVIDAD ECONÓMICA ESPECIFICADA',
                'SEXO DEL TRABAJADOR AFECTADO',
                'EDAD AL MOMENTO DE RECLAMO',
                'CAUSA DEL SINIESTRO',
                'DESCRIPTIVO DEL SINIESTRO',
                'LUGAR DEL ACCIDENTE',
                'PROVINCIA DEL ACCIDENTE',
                'CANTON DEL ACCIDENTE',
                'PARTES DEL CUERPO LESIONADAS',
                'TIPO DE ENFERMEDAD LABORAL',
                'FECHA DE FALLECIMIENTO',
                'TIENE O NO CONYUGE/COMPAÑERA?',
                'FECHA DE NACIMIENTO DEL CONYUGE/COMPAÑERA AL MOMENTO DEL FALLECIMIENTO',
                'FECHA NACIMIENTO DEL MENOR DE LOS HIJOS  < 18 AÑOS  AL MOMENTO DEL FALLECIMIENTO',
                'CANTIDAD DE HIJOS (PÓSTUMOS) AL MOMENTO DEL FALLECIMIENTO',
                'FECHA NACIMIENTO DE LA MADRE AL MOMENTO DEL FALLECIMIENTO',
                'FECHA NACIMIENTO DEL PADREE AL MOMENTO DEL FALLECIMIENTO',
                'TIENE DEPENDIENTES ECONÓMICOS? S/N',
                'FECHA NACIMIENTO MENOR DE LOS DEPENDIENTES > 60 AÑOS',
                'ID BENEFICIARIO',
                'NOMBRE BENEFICIARIO',
                'CENTRO ASISTENCIAL O SANITARIO DONDE SE PRESTÓ EL SERVICIO',
                '% CONTRATO',
                '%FACULTATIVO'
            ];

            $activities = [];

            foreach ($query as $item) {
                $activities[] = [
                    $item->id,
                    $item->caso_gis,
                    $item->comprobante,
                    $item->consecutive_policy,
                    $item->cod_corredor,
                    $item->cod_intermediario,
                    $item->nombre_corredor,
                    $item->nombre_intermediario,
                    $item->full_name,
                    $item->doc_number,
                    $item->number_identification_affiliate,
                    $item->name_affiliate,
                    $item->type_currency,
                    $item->fecha_movimiento,
                    $item->tipo_movimiento,
                    floatval($item->mont_mov_moneda ?? 0),
                    floatval($item->mot_mov_local ?? 0),
                    floatval($item->mot_mov_dolar ?? 0),

                    floatval($item->trm?? 0), // nueva

                    $item->cobertura_afectada,
                    $item->fecha_reporte,
                    $item->fecha_ocurrencia_siniestro,
                    $item->fecha_not_siniestro,
                    $item->fecha_ini_incapacidad,
                    $item->fecha_fin_incapacidad,
                    $item->tipo_incapacidad,
                    $item->tipo_contratante,
                    $item->branch_name,
                    $item->actividad_eco,
                    $item->genero,
                    $item->edad,
                    $item->causa_siniestro,
                    $item->descriptivo_siniestro,
                    $item->lugar_accidente,
                    $item->provincia,
                    $item->canton,
                    $item->part_lesionada,
                    $item->tipo_enfermedad,
                    $item->fecha_fallecimiento,
                    $item->conyuge,
                    $item->fecha_naci_conyuge,
                    $item->fecha_naci_hijo_menor,
                    $item->cant_hijos,
                    $item->fecha_naci_madre,
                    $item->fecha_naci_padre,
                    $item->pendientes_economicos,
                    $item->fecha_naci_pend,
                    $item->id_beneficiario,
                    $item->nombre_beneficiario,
                    $item->centro_asistencia,
                    $item->por_contrato,
                    $item->por_facultativo,
                ];

            }


            return Excel::create('reporteSiniestros', function ($excel) use ($activities, $headings) {
                $excel->sheet('Entries', function ($sheet) use ($activities, $headings) {
                    $sheet->row(1, $headings);
                    $sheet->fromArray($activities, null, 'A2', false, false);

                    $rowIndex = 2;
                    foreach ($activities as $activity) {
                        // Fuerza los valores 0 como flotantes
                        $activity[15] = (float) $activity[15];
                        $activity[16] = (float) $activity[16];
                        $activity[17] = (float) $activity[17];

                        $sheet->row($rowIndex++, $activity);
                    }


                    // Calcular la última fila
                    $lastRow = count($activities) + 1; // +1 por la fila de encabezados

                    $sheet->setColumnFormat([
                        // Formatos de FECHA (dd/mm/yyyy)
                        'N2:N'.$lastRow => 'dd/mm/yyyy', // FECHA DE MOVIMIENTO
                        'U2:U'.$lastRow => 'dd/mm/yyyy', // FECHA DE REPORTE FORMAL DEL ACCIDENTE
                        'V2:V'.$lastRow => 'dd/mm/yyyy', // FECHA OCURRENCIA DEL SINIESTRO
                        'W2:W'.$lastRow => 'dd/mm/yyyy', // FECHA NOTIFICACIÓN DEL SINIESTRO
                        'X2:X'.$lastRow => 'dd/mm/yyyy', // FECHA INICIO DE INCAPACIDAD
                        'Y2:Y'.$lastRow => 'dd/mm/yyyy', // FECHA FIN DE INCAPACIDAD
                        'AN2:AN'.$lastRow => 'dd/mm/yyyy', // FECHA DE FALLECIMIENTO
                        'AP2:AP'.$lastRow => 'dd/mm/yyyy', // FECHA NACIMIENTO DEL CÓNYUGE/COMPAÑERA
                        'AQ2:AQ'.$lastRow => 'dd/mm/yyyy', // FECHA NACIMIENTO DEL MENOR DE LOS HIJOS < 18
                        'AS2:AS'.$lastRow => 'dd/mm/yyyy', // FECHA NACIMIENTO DE LA MADRE
                        'AT2:AT'.$lastRow => 'dd/mm/yyyy', // FECHA NACIMIENTO DEL PADRE
                        'AV2:AV'.$lastRow => 'dd/mm/yyyy', // FECHA NACIMIENTO DEPENDIENTE > 60

                        // Formatos NUMÉRICOS (0.00)
                        'P2:P'.$lastRow => '0.00', // MONTO MOVIMIENTO MONEDA
                        'Q2:Q'.$lastRow => '0.00', // MONTO MOVIMIENTO LOCAL
                        'R2:R'.$lastRow => '0.00', // MONTO MOVIMIENTO DÓLAR
                        'S2:S'.$lastRow => '0.00', // TASA DE CAMBIO (TRM)
                        'BB2:BB'.$lastRow => '0.00', // % CONTRATO
                        'BC2:BC'.$lastRow => '0.00', // % FACULTATIVO
                    ]);


                    // Establecer alineación a la derecha para columnas numéricas y fechas
                    $numericColumns = ['N', 'U', 'V', 'W', 'X', 'Y', 'AN', 'AP', 'AQ', 'AS', 'AT', 'AV', 'P', 'Q', 'R', 'S', 'BB', 'BC'];

                    foreach ($numericColumns as $col) {
                        $sheet->cells($col.'2:'.$col.$lastRow, function($cells) {
                            $cells->setAlignment('right');
                        });
                    }

                });
            })->download('xlsx');


        }catch (\Exception $e) {

            return redirect('/admin/reportes/descargas_admin')
                ->withErrors($e->getMessage())
                ->withInput();
        }

    }

    //ME-2584 Reporte Reaseguro
    public function reportReinsurance(Request $req, $cpath)
    {
        try {
            $initDate = $req->reinsurance_start_date_submit;
            $lastDate = $req->reinsurance_end_date_submit;

            if (empty($initDate) || empty($lastDate)) {
                throw new \Exception("Las fechas no pueden estar vacías, en la sección 'Reporte de reaseguro'");
            }

            if (strtotime($lastDate) < strtotime($initDate)) {
                throw new \Exception("La fecha desde no puede ser menor a la fecha hasta, en la sección 'Reporte de reaseguro'");
            }

            $activities = [];

            $consulta = "select * 
                         from report_reinsurace_view 
                         where created_at BETWEEN :initDate AND :lastDate ";

            $query = DB::select($consulta, [
                'initDate' => $initDate . ' 00:00:00',
                'lastDate' => $lastDate . ' 23:59:59'
            ]);

            foreach ($query as $item) {

                $activities[] = [
                    $item->id,
                    $item->consecutive_policy,
                    $item->full_name,
                    $item->doc_number,
                    $item->number_identification_affiliate,
                    $item->name_affiliate,
                    $item->type_currency,
                    $item->fecha_movimiento,
                    $item->tipo_movimiento,
                    number_format($item->mont_mov_moneda ?? 0 , 2, ',', ''),
                    $item->fecha_ocurrencia_siniestro,
                    $item->fecha_not_siniestro,
                    $item->fecha_ini_incapacidad,
                    $item->fecha_fin_incapacidad,
                    $item->dias_incap,
                    $item->tipo_incap,
                    $item->actividad_eco,
                    $item->genero,
                    $item->edad,
                    $item->grupo_ocup,
                    $item->descriptivo_siniestro,
                    $item->lugar_accidente,
                    $item->provincia,
                    $item->canton,
                    $item->distrito,
                    $item->tipo_siniestro,
                    $item->severidad,//n
                    $item->pertinencia,
                    $item->fecha_alta_medica,//n
                    $item->ibnr,//n
                    $item->part_lesionada,
                    $item->dianostico,
                    $item->lateralidad,
                    $item->origen,
                    $item->fecha_caso_cierre,//n
                    $item->duracion_aviso_cierre,//n
                    $item->proveedor_serv_med,//n
                    $item->fecha_fallecimiento,
                    $item->conyuge
                ];
            }
//parte
            $headings = [
                'Id siniestro',
                'Id póliza',
                'Nombre del tomador',
                'Id cliente',
                'Id trabajador afectado',
                'Nombre trabajador afectado',
                'Moneda del siniestro',
                'Fecha de reserva o pago',
                'Tipo de movimiento',
                'Monto movimiento moneda',
                'Fecha ocurrencia del siniestro',
                'Fecha de aviso',
                'Fecha inicio de incapacidad',
                'Fecha fin de incapacidad',
                'Número de días de incapacidad',
                'Tipo de incapacidad',
                'Actividad económica',
                'Género del trabajador afectado',
                'Edad al momento de reclamo',
                'Grupo ocupacional',
                'Descriptivo del siniestro',
                'Lugar del accidente',
                'Provincia del accidente',
                'Cantón del accidente',
                'Distrito',
                'Tipo de siniestro',
                'Severidad',
                'Pertinencia de reconocimiento',
                'Fecha de alta medica',
                'IBNR',
                'Partes del cuerpo lesionadas',
                'Diagnóstico principal (CIE10)',
                'Lateralidad',
                'Origen',
                'Fecha cierre caso',
                'Duracion entre aviso y cierre del siniestro',
                'Proveedor de servicios medicos',
                'Fecha de fallecimiento',
                '¿Tiene o no cónyuge/compañera?'
            ];

            return Excel::create('reporteSiniestros', function ($excel) use ($activities, $headings) {
                $excel->sheet('Entries', function ($sheet) use ($activities, $headings) {
                    $sheet->row(1, $headings);
                    $sheet->fromArray($activities, null, 'A2', false, false);
                });
            })->download('xlsx');

        }catch (\Exception $e) {

            return redirect('/admin/reportes/descargas_admin')
                ->withErrors($e->getMessage())
                ->withInput();
        }

    }


    //ME-1158 - Requerimiento 41
    public function reportPendingIncidentAmount(Request $req, $cpath)
    {
        try {
            
            // se valida que las fechas no esten vacias
            if (empty($req->rpia_start_date_submit) || empty($req->rpia_end_date_submit)) {
                throw new \Exception("Las fechas no pueden estar vacías, en la sección 'Reporte monto pendiente de siniestros'");
            }

            //formateamos la fechas
            $initDate = Carbon::parse($req->rpia_start_date_submit)->startOfDay()->format('Y-m-d H:i:s');

            $lastDate = Carbon::parse($req->rpia_end_date_submit)->endOfDay()->format('Y-m-d H:i:s');

            // Validar que las fechas no sean nulas
            if (empty($initDate) || empty($lastDate)) {
                throw new \Exception("Las fechas no pueden estar vacías, en la sección 'Reporte intermediario y tomador'");
            }

            // Validar que $lastDate no sea menor que $initDate
                if (strtotime($lastDate) < strtotime($initDate)) {
                throw new \Exception("La fecha desde no puede ser menor a la fecha hasta, en la sección 'Reporte monto pendiente de siniestros'");
            }


            // Consulta a la vista
            $query = "WITH total_sub_query AS (
                        WITH trm_filtered AS (
                            SELECT *,
                                   ROW_NUMBER() OVER (PARTITION BY DATE(created_at) ORDER BY created_at ASC) AS rn
                            FROM ebdb.trm_rates
                        )SELECT CASE WHEN aab.cant > 0 THEN g.consecutive ELSE g.consecutive_gis END AS id_row,
                                SUM(CASE
                                        WHEN ac.cod_oper = '011' AND ac.cod_moneda = 'US'
                                            THEN ac.debit / ac.exchange_rate
                                        WHEN ac.cod_oper = '011' THEN ac.debit
                                        ELSE 0
                                    END)                                                             AS total_011_cod_moneda,
                                SUM(CASE
                                        WHEN ac.cod_oper = '050' AND ac.cod_moneda = 'US'
                                            THEN ac.debit / ac.exchange_rate
                                        WHEN ac.cod_oper = '050' THEN ac.debit
                                        ELSE 0
                                    END)                                                             AS total_050_cod_moneda,
                                SUM(CASE
                                        WHEN ac.cod_oper = '011' AND ac.cod_moneda = 'US'
                                            THEN ac.debit / ac.exchange_rate
                                        WHEN ac.cod_oper = '011' THEN ac.t_debit / tr.rate
                                        ELSE 0
                                    END)                                                             AS total_011_mov_dolar,
                                SUM(CASE
                                        WHEN ac.cod_oper = '050' AND ac.cod_moneda = 'US'
                                            THEN ac.debit / ac.exchange_rate
                                        WHEN ac.cod_oper = '050' THEN ac.t_debit / tr.rate
                                        ELSE 0
                                    END)                                                             AS total_050_mov_dolar,
                                SUM(CASE WHEN ac.cod_oper = '011' THEN ac.debit ELSE 0 END)          AS total_011,
                                SUM(CASE WHEN ac.cod_oper = '050' THEN ac.debit ELSE 0 END)          AS total_050
                        FROM ebdb.accounting_entries ac
                                 LEFT JOIN ebdb.gis_sort g ON g.activity_id = ac.activity_gis
                                 LEFT JOIN (SELECT a.activity_id, COUNT(0) AS cant
                                            FROM ebdb.activity_actions a
                                            WHERE a.new_state_id = 160
                                            GROUP BY a.activity_id) aab ON aab.activity_id = ac.activity_gis
                                 LEFT JOIN trm_filtered tr ON DATE(tr.created_at) = DATE(ac.created_at) AND tr.rn = 1
                                 WHERE ac.created_at <= '" . $lastDate . "'
                        GROUP BY id_row)
                    SELECT  CASE WHEN aab.cant > 0 THEN g.consecutive ELSE g.consecutive_gis END                      AS id_row,
                           g.id                                                                                        AS caso_gis,
                           GROUP_CONCAT(DISTINCT ac.receipt_number ORDER BY ac.receipt_number ASC SEPARATOR ', ')      AS comprobante,
                           g.activity_id                                                                               AS activity_id,
                           g.type_report                                                                               AS type_report,
                           CONCAT('SORT-', LPAD(ps.consecutive, 4, '0'))                                               AS consecutive_policy,
                           af.doc_number                                                                               AS doc_number,
                           af.full_name                                                                                AS full_name,
                           g.number_identification_affiliate                                                           AS number_identification_affiliate,
                           afg.full_name                                                                               AS name_affiliate,
                           ps.type_currency                                                                            AS type_currency,
                           GROUP_CONCAT(DISTINCT DATE_FORMAT(ac.created_at, '%d/%m/%Y') ORDER BY ac.created_at ASC SEPARATOR
                                        ', ')                                                                          AS fecha_movimiento,
                           ac.cod_oper                                                                                 AS cod_oper,
                           'SALDO RESERVA'                                                                             AS tipo_movimiento,
                           COALESCE(tsq.total_050_cod_moneda - tsq.total_011_cod_moneda, 0)                            AS mont_mov_moneda,
                           COALESCE(tsq.total_050 - tsq.total_011, 0)                                                  AS mot_mov_local,
                           COALESCE(tsq.total_050_mov_dolar - tsq.total_011_mov_dolar, 0)                              AS mot_mov_dolar,
                           ''                                                                                          AS cobertura_afectada,
                           GROUP_CONCAT(DISTINCT DATE_FORMAT(g.date_accident, '%d/%m/%Y') ORDER BY g.date_accident ASC SEPARATOR
                                        ', ')                                                                          AS fecha_ocurrencia_siniestro,
                           GROUP_CONCAT(DISTINCT DATE_FORMAT(g.date_accident, '%d/%m/%Y') ORDER BY g.date_accident ASC SEPARATOR
                                        ', ')                                                                          AS fecha_not_siniestro,
                           GROUP_CONCAT(DISTINCT DATE_FORMAT(pis.start_date, '%d/%m/%Y') ORDER BY pis.start_date ASC SEPARATOR
                                        ', ')                                                                          AS fecha_ini_incapacidad,
                           GROUP_CONCAT(DISTINCT DATE_FORMAT(pis.end_date, '%d/%m/%Y') ORDER BY pis.end_date ASC SEPARATOR
                                        ', ')                                                                          AS fecha_fin_incapacidad,
                           ''                                                                                          AS tipo_incapacidad,
                           case when af.doc_type <> 'CJ' then 'Persona natural' else 'Persona jurídica' end            AS tipo_contratante,
                           ea.activity_name                                                                            AS actividad_eco,
                           CASE WHEN psa.gender = 'M' THEN 'Masculino' ELSE 'Femenino' END                             AS genero,
                           CASE
                               WHEN psa.date_of_birth IS NOT NULL THEN TIMESTAMPDIFF(YEAR, psa.date_of_birth, g.created_at)
                               ELSE 0
                               END                                                                                     AS edad,
                           ''                                                                                          AS causa_siniestro,
                           g.accident_description                                                                      AS descriptivo_siniestro,
                           CASE
                               WHEN g.accident_place = '1' THEN 'Puesto de trabajo'
                               WHEN g.accident_place = '2' THEN 'Casa'
                               WHEN g.accident_place = '3' THEN 'Trayecto'
                               WHEN g.accident_place = '4' THEN 'En comisión'
                               WHEN g.accident_place = '5' THEN 'Dentro del centro de trabajo'
                               WHEN g.accident_place = '6' THEN 'Fuera del centro del trabajo'
                               WHEN g.accident_place = '7' THEN 'Labores o trabajos no habituales fuera del centro de trabajo'
                               WHEN g.accident_place = '8' THEN 'Otro lugar dentro del centro de trabajo'
                               ELSE 'Desconocido'
                               END                                                                                     AS lugar_accidente,
                           p.name                                                                                      AS provincia,
                           ct.name                                                                                     AS canton,
                           dt.name                                                                                     AS distrito,
                           GROUP_CONCAT(DISTINCT gbp.body_part_name ORDER BY gbp.body_part_name ASC SEPARATOR ', ')    AS part_lesionada,
                           CASE
                               WHEN g.agent_involved = 'agente_fisico' THEN 'Agente físico'
                               WHEN g.agent_involved = 'agente_biologico' THEN 'Agente biológico'
                               WHEN g.agent_involved = 'factores_psicosociales' THEN 'Factores psicosociales'
                               WHEN g.agent_involved = 'enfermedades_osteomuscular' THEN 'Enfermedades del sistema osteomuscular'
                               WHEN g.agent_involved = 'otros' THEN 'Otros'
                               ELSE ''
                               END                                                                                     AS tipo_enfermedad,
                           u.code_correduria                                                                           AS cod_correduria,
                           ac.activity_gis                                                                             AS activity_gis,
                           aa.cant                                                                                     AS cant,
                           ac.created_at                                                                               AS created_at,
                           CASE WHEN up.cod_intermediario IS NOT NULL THEN up.cod_corredor END                         AS cod_corredor,
                           CASE WHEN up.cod_intermediario IS NULL THEN up.cod_corredor ELSE up.cod_intermediario END   AS cod_intermediario,
                           ps.brokerage_name                                                                           AS nombre_intermediario,                          
                           case when trim(lower(ps.brokerage_name)) = trim(lower(ps.advisor_name)) then '' else ps.advisor_name end AS nombre_corredor,
                           pis.days_it                                                                                 AS dias_incap,
                           CASE WHEN pis.days_it IS NOT NULL THEN 'temporal' ELSE '' END                               AS tipo_incap,
                           g.occupation_position                                                                       AS grupo_ocup,
                           g.type_report                                                                               AS tipo_siniestro,
                           CASE WHEN g.type_report IN ('Accidente', 'Enfermedad') THEN 'Laboral' ELSE 'No Laboral' END AS pertinencia,
                           dg.diagnostico                                                                              AS dianostico,
                           dg.laterality                                                                               AS lateralidad,
                           dg.origin                                                                                   AS origen,
                           ''                                                                                          AS conyuge,
                           ''                                                                                          AS fecha_fallecimiento,
                           case when year(ps.validity_from) = '2025' then '30' else 0 end AS por_contrato
                    FROM ebdb.accounting_entries ac
                             LEFT JOIN ebdb.gis_sort g
                                       ON g.activity_id = ac.activity_gis
                             LEFT JOIN ebdb.activities a
                                       ON a.id = g.activity_id
                             LEFT JOIN ebdb.affiliates afg
                                       ON afg.id = a.affiliate_id
                             LEFT JOIN ebdb.activities ap
                                       ON ap.id = a.parent_id
                             LEFT JOIN ebdb.policy_sorts ps
                                       ON ps.activity_id = ap.id
                             LEFT JOIN ebdb.affiliates af
                                       ON af.id = ap.affiliate_id
                             LEFT JOIN ebdb.economic_activities ea
                                       ON ea.code = ps.activity_economic_id
                             LEFT JOIN ebdb.provinces p
                                       ON p.id = g.province
                             LEFT JOIN ebdb.cantons ct
                                       ON ct.id = g.canton AND ct.province_id = g.province
                             LEFT JOIN ebdb.districts dt
                                       ON dt.id = g.district AND dt.canton_id = g.canton AND dt.province_id = g.province
                             LEFT JOIN ebdb.activities ape
                                       ON ape.parent_id = g.activity_id
                             LEFT JOIN ebdb.activities apeb
                                       ON apeb.parent_id = ape.id
                             LEFT JOIN ebdb.pe_it_sorts pit
                                       ON pit.activity_id = apeb.id
                             LEFT JOIN (SELECT ap.parent_id  AS serv_gis,
                                               ps.id         AS id,
                                               ps.start_date AS start_date,
                                               ps.end_date   AS end_date,
                                               ps.days_it    AS days_it
                                        FROM ebdb.pe_it_sorts p
                                                 LEFT JOIN ebdb.activities a
                                                           ON a.id = p.activity_id
                                                 LEFT JOIN ebdb.activities ap
                                                           ON ap.id = a.parent_id
                                                 LEFT JOIN ebdb.peit_inability_sorts ps
                                                           ON ps.pe_it_sort_id = p.id
                                        GROUP BY ap.parent_id) pis
                                       ON pis.serv_gis = ac.activity_gis
                             LEFT JOIN ebdb.policy_spreadsheet_affiliates psa
                                       ON psa.identification_number = g.number_identification_affiliate
                             LEFT JOIN ebdb.gis_body_parts gbp
                                       ON gbp.gis_id = g.id
                             LEFT JOIN ebdb.users u
                                       ON u.id = ap.user_id
                             LEFT JOIN (SELECT a.activity_id AS activity_id,
                                               COUNT(0)      AS cant
                                        FROM ebdb.activity_actions a
                                        WHERE a.new_state_id IN (194, 157)
                                        GROUP BY a.activity_id) aa
                                       ON aa.activity_id = ac.activity_gis
                             LEFT JOIN (SELECT a.activity_id AS activity_id,
                                               COUNT(0)      AS cant
                                        FROM ebdb.activity_actions a
                                        WHERE a.new_state_id = 160
                                        GROUP BY a.activity_id) aab
                                       ON aab.activity_id = ac.activity_gis
                             LEFT JOIN (SELECT REPLACE(s.username COLLATE utf8mb4_unicode_ci, 'CO-', '') AS username,
                                               REPLACE(s.code_mnk, 'CO-', '')                            AS cod_corredor,
                                               REPLACE(s.code_correduria, 'CO-', '')                     AS cod_intermediario,
                                               s.tipo_corredor                                           AS tipo_corredor,
                                               s.correduria                                              AS correduria,
                                               s.advisor_name                                            AS advisor_name,
                                               s.brokerage_name                                          AS brokerage_name
                                        FROM ebdb.users s
                                        GROUP BY REPLACE(s.username COLLATE utf8mb4_unicode_ci, 'CO-', '')) up
                                       ON up.username = REPLACE(ps.code COLLATE utf8mb4_unicode_ci, 'CO-', '')
                             LEFT JOIN (SELECT g.gis_id                                          AS gis_id,
                                               GROUP_CONCAT(CONCAT(g.casedata_code_cie, '-', g.casedata_diagnosis) SEPARATOR
                                                            ',')                                 AS diagnostico,
                                               GROUP_CONCAT(g.casedata_laterality SEPARATOR ',') AS laterality,
                                               GROUP_CONCAT(g.origin SEPARATOR ',')              AS origin
                                        FROM ebdb.gis_diagnostics g
                                        GROUP BY g.gis_id) dg
                                       ON dg.gis_id = g.id
                             LEFT JOIN total_sub_query tsq
                                       ON tsq.id_row = (CASE WHEN aab.cant > 0 THEN g.consecutive ELSE g.consecutive_gis END)
                    WHERE ac.cod_oper IN ('050', '011')
                      AND ac.debit <> 0
                      AND ap.state_id <> 196
                      AND ac.created_at >= '" . $initDate . "' AND ac.created_at <= '" . $lastDate . "'
                    GROUP BY  (CASE WHEN aab.cant > 0 THEN g.consecutive ELSE g.consecutive_gis END)
                    ORDER BY ac.created_at DESC;
                    ";

            $query = DB::select($query);

            $headings = [
                'ID SINIESTRO',
                'NO CASO',
                'COMPROBANTE CONTABLE',
                'ID PÓLIZA',
                'NOMBRE DE TOMADOR PÓLIZA',
                'COD  CORREDOR',
                'COD INTERMEDIARIO',
                'NOMBRE CORREDOR',
                'NOMBRE INTERMEDIARIO',
                'ID CLIENTE',
                'ID TRABAJADOR AFECTADO',
                'NOMBRE TRABAJADOR AFECTADO',
                'MONEDA DEL SINIESTRO',
                'FECHA DE MOVIMIENTO',
                'TIPO DE MOVIMIENTO',
                'MONTO MOVIMIENTO MONEDA',
                'MONTO MOVIMIENTO LOCAL',
                'MONTO MOVIMIENTO DÓLAR',
                'COBERTURA AFECTADA',
                'FECHA OCURRENCIA DEL SINIESTRO',
                'FECHA NOTIFICACIÓN DEL SINIESTRO',
                'FECHA INICIO DE INCAPACIDAD',
                'FECHA FIN DE INCAPACIDAD',
                'TIPO DE INCAPACIDAD',
                'TIPO DE CONTRATANTE',
                'ACTIVIDAD ECONÓMICA',
                'SEXO DEL TRABAJADOR AFECTADO',
                'EDAD AL MOMENTO DE RECLAMO',
                'CAUSA DEL SINIESTRO',
                'DESCRIPTIVO DEL SINIESTRO',
                'LUGAR DEL ACCIDENTE',
                'PROVINCIA DEL ACCIDENTE',
                'CANTON DEL ACCIDENTE',
                'PARTES DEL CUERPO LESIONADAS',
                'TIPO DE ENFERMEDAD LABORAL',
                'FECHA DE FALLECIMIENTO',
                'TIENE O NO CONYUGE/COMPAÑERA?',
                'FECHA DE NACIMIENTO DEL CONYUGE/COMPAÑERA AL MOMENTO DEL FALLECIMIENTO',
                'FECHA NACIMIENTO DEL MENOR DE LOS HIJOS  < 18 AÑOS  AL MOMENTO DEL FALLECIMIENTO',
                'CANTIDAD DE HIJOS (PÓSTUMOS) AL MOMENTO DEL FALLECIMIENTO',
                'FECHA NACIMIENTO DE LA MADRE AL MOMENTO DEL FALLECIMIENTO',
                'FECHA NACIMIENTO DEL PADREE AL MOMENTO DEL FALLECIMIENTO',
                'TIENE DEPENDIENTES ECONÓMICOS? S/N',
                'FECHA NACIMIENTO MENOR DE LOS DEPENDIENTES > 60 AÑOS',
                'ID BENEFICIARIO',
                'NOMBRE BENEFICIARIO',
                'CENTRO ASISTENCIAL O SANITARIO DONDE SE PRESTÓ EL SERVICIO',
                '% CONTRATO',
                '%FACULTATIVO'
            ];

            foreach ($query as $item) {
                $activities[] = [
                    $item->id_row,
                    $item->caso_gis,
                    $item->comprobante,
                    $item->consecutive_policy,
                    $item->full_name,
                    $item->cod_corredor,
                    $item->cod_intermediario,
                    $item->nombre_corredor,
                    $item->nombre_intermediario,
                    $item->doc_number,
                    $item->number_identification_affiliate,
                    $item->name_affiliate,
                    $item->type_currency,
                    $item->fecha_movimiento,
                    $item->tipo_movimiento,
                    floatval($item->mont_mov_moneda ?? 0 ),
                    floatval($item->mot_mov_local ?? 0 ),
                    floatval($item->mot_mov_dolar ?? 0 ),
                    $item->cobertura_afectada,
                    $item->fecha_ocurrencia_siniestro,
                    $item->fecha_not_siniestro,
                    $item->fecha_ini_incapacidad,
                    $item->fecha_fin_incapacidad,
                    $item->tipo_incap,
                    $item->tipo_contratante,
                    $item->actividad_eco,
                    $item->genero,
                    $item->edad,
                    $item->causa_siniestro,
                    $item->descriptivo_siniestro,
                    $item->lugar_accidente,
                    $item->provincia,
                    $item->canton,
                    $item->part_lesionada ?? '',
                    $item->tipo_enfermedad,
                    $item->fecha_fallecimiento,
                    $item->conyuge,
                    $item->fecha_naci_conyuge ?? '',
                    $item->fecha_naci_hijo_menor ?? '',
                    $item->cant_hijos ?? '',
                    $item->fecha_naci_madre ?? '',
                    $item->fecha_naci_padre ?? '',
                    $item->pendientes_economicos ?? '',
                    $item->fecha_naci_pend ?? '',
                    $item->id_beneficiario ?? '',
                    $item->nombre_beneficiario ?? '',
                    $item->centro_asistencia ?? '',
                    $item->por_contrato ?? '',
                    $item->por_facultativo ?? '',
                ];

                }


            return Excel::create('reporteMontoPendienteSiniestros', function ($excel) use ($activities, $headings) {
                $excel->sheet('Entries', function ($sheet) use ($activities, $headings) {
                    $sheet->row(1, $headings);
                    $sheet->fromArray($activities, null, 'A2', false, false);

                    $rowIndex = 2;
                    foreach ($activities as $activity) {
                        // Fuerza los valores 0 como flotantes
                        $activity[15] = (float) $activity[15];
                        $activity[16] = (float) $activity[16];
                        $activity[17] = (float) $activity[17];

                        $sheet->row($rowIndex++, $activity);
                    }


                    // Calcular la última fila
                    $lastRow = count($activities) + 1; // +1 por la fila de encabezados

                    // Aplicar formato de fecha (día/mes/año)
                    $sheet->setColumnFormat([
                        // Fechas
                        'N2:N'.$lastRow => 'dd/mm/yyyy', // FECHA DE MOVIMIENTO
                        'T2:T'.$lastRow => 'dd/mm/yyyy', // FECHA OCURRENCIA DEL SINIESTRO
                        'U2:U'.$lastRow => 'dd/mm/yyyy', // FECHA NOTIFICACIÓN DEL SINIESTRO
                        'V2:V'.$lastRow => 'dd/mm/yyyy', // FECHA INICIO DE INCAPACIDAD
                        'W2:W'.$lastRow => 'dd/mm/yyyy', // FECHA FIN DE INCAPACIDAD
                        'AJ2:AJ'.$lastRow => 'dd/mm/yyyy', // FECHA DE FALLECIMIENTO
                        'AL2:AL'.$lastRow => 'dd/mm/yyyy', // FECHA NACIMIENTO CONYUGE
                        'AM2:AM'.$lastRow => 'dd/mm/yyyy', // FECHA NACIMIENTO HIJO <18
                        'AO2:AO'.$lastRow => 'dd/mm/yyyy', // FECHA NACIMIENTO MADRE
                        'AP2:AP'.$lastRow => 'dd/mm/yyyy', // FECHA NACIMIENTO PADRE
                        'AR2:AR'.$lastRow => 'dd/mm/yyyy', // FECHA NACIMIENTO DEPENDIENTE >60

                        // Montos (0.00)
                        'P2:P'.$lastRow => '0.00', // MONTO MOVIMIENTO MONEDA
                        'Q2:Q'.$lastRow => '0.00', // MONTO MOVIMIENTO LOCAL
                        'R2:R'.$lastRow => '0.00', // MONTO MOVIMIENTO DÓLAR
                        'AZ2:AZ'.$lastRow => '0.00', // % CONTRATO
                        'BA2:BA'.$lastRow => '0.00', // % FACULTATIVO
                    ]);

                    // Establecer alineación a la derecha para columnas numéricas y fechas
                    $numericColumns = ['N', 'T', 'U', 'V', 'W', 'AJ', 'AL', 'AM', 'AO', 'AP', 'AR'];

                    foreach ($numericColumns as $col) {
                        $sheet->cells($col.'2:'.$col.$lastRow, function($cells) {
                            $cells->setAlignment('right');
                        });
                    }

                });
            })->download('xlsx');


        }catch (\Exception $e) {

            return redirect('/admin/reportes/descargas_admin')
                ->withErrors($e->getMessage())
                ->withInput();
        }

    }

    //MS-2229 REPORTE DE ASIENDO BD
    public function reportAccountingEntry(Request $req, $cpath)
    {
        try {

            $initDate = $req->asientos_start_date_submit;
            $lastDate = $req->asientos_end_date_submit;
            $consecutive = $req->policy_sort_id;

            if (empty($initDate) || empty($lastDate)) {
                throw new \Exception("Las fechas no pueden estar vacías, en la sección 'Reporte de asientos contables'");
            }

            if (strtotime($lastDate) < strtotime($initDate)) {
                throw new \Exception("La fecha desde no puede ser menor a la fecha hasta, en la sección 'Reporte de asientos contables'");
            }

            $activities = [];

            $query = DB::table('accounting_entries')
                ->whereBetween('created_at', [$initDate . ' 00:00:00', $lastDate . ' 23:59:59']);

            if (!empty($consecutive)) {
                $query->whereRaw("TRIM(LEADING '0' FROM REPLACE(number_policy, 'SORT-', '')) = ?", [$consecutive]);
            }

            $query->orderBy('created_at', 'ASC')
                ->chunk(1000, function ($items) use (&$activities) {
                    foreach ($items as $item) {
                        $activities[] = [
                            $item->id,
                            $item->cod_cia,
                            $item->cod_oper,
                            $item->receipt_number,
                            $item->receipt_status,
                            $item->number_policy,
                            $item->number_payment,
                            $item->receipt_type,
                            $item->date_register,
                            $item->cod_ramo,
                            $item->detail_movement,
                            $item->debit,
                            $item->credit,
                            $item->t_debit,
                            $item->t_credit,
                            $item->difference,
                            $item->movement_type,
                            $item->movement_type_org,
                            $item->idecta,
                            $item->cta_1,
                            $item->cta_2,
                            $item->cta_3,
                            $item->cta_4,
                            $item->cta_5,
                            $item->cta_6,
                            $item->cta_7,
                            $item->cta_8,
                            $item->cta_9,
                            $item->cta_10,
                            $item->document_type,
                            $item->document_number,
                            $item->name_taker,
                            $item->cod_intermediary,
                            $item->cod_moneda,
                            $item->exchange_rate,
                            $item->created_at,
                            $item->updated_at,
                            $item->descoper,
                            $item->cod_grupo_cpto,
                            $item->cod_cpto,
                            $item->cor_relativo,
                            $item->entry_code,
                            $item->ctaux,
                            $item->activity_gis,
                            $item->type,
                            $item->caso_gis
                        ];
                    }
                });

            $headings = [
                'ID',
                'Código Compañía',
                'Código Operación',
                'Número de Recibo',
                'Estado del Recibo',
                'Número de Póliza',
                'Número de Pago',
                'Tipo de Recibo',
                'Fecha de Registro',
                'Código Ramo',
                'Detalle de Movimiento',
                'Débito',
                'Crédito',
                'Total Débito',
                'Total Crédito',
                'Diferencia',
                'Tipo de Movimiento',
                'Tipo de Movimiento Original',
                'ID ECTA',
                'Cuenta 1',
                'Cuenta 2',
                'Cuenta 3',
                'Cuenta 4',
                'Cuenta 5',
                'Cuenta 6',
                'Cuenta 7',
                'Cuenta 8',
                'Cuenta 9',
                'Cuenta 10',
                'Tipo de Documento',
                'Número de Documento',
                'Nombre del Tomador',
                'Código del Intermediario',
                'Código de Moneda',
                'Tipo de Cambio',
                'Fecha de Creación',
                'Fecha de Actualización',
                'Descoper',
                'Código Grupo Cpto',
                'Código Cpto',
                'Código Relativo',
                'Código de Entrada',
                'CTAux',
                'Actividad GIS',
                'Tipo',
                'Caso GIS',
            ];

            return Excel::create('reporteAsientosContables', function ($excel) use ($activities, $headings) {
                $excel->sheet('Entries', function ($sheet) use ($activities, $headings) {
                    $sheet->row(1, $headings);
                    $sheet->fromArray($activities, null, 'A2', false, false);
                });
            })->download('xlsx');

        } catch (\Exception $e) {
            return redirect('/admin/reportes/descargas_admin')
                ->withErrors($e->getMessage())
                ->withInput();
        }
    }

    public function reinsuranceReport(Request $req, $cpath)
    {
        try {
            $initDate = $req->reaseguro_start_date_submit;
            $lastDate = $req->reaseguro_end_date_submit;

            if (empty($initDate) || empty($lastDate)) {
                throw new \Exception("Las fechas no pueden estar vacías, en la sección 'Reporte de reaseguro'");
            }

            if (strtotime($lastDate) < strtotime($initDate)) {
                throw new \Exception("La fecha desde no puede ser menor a la fecha hasta, en la sección 'Reporte de reaseguro'");
            }

            $reinsurancePercentage = AppServiceProvider::$REINSURANCE_PERCENTAGE;
            $activities = [];

            $consulta = "select * from report_reinsurance_view where created_at BETWEEN :initDate AND :lastDate ";

            $query = DB::select($consulta, [
                'initDate' => $initDate . ' 00:00:00',
                'lastDate' => $lastDate . ' 23:59:59'
            ]);

            foreach ($query as $item) {
                $activities[] = [
                    $item->reasegurado,
                    $item->id_poliza,
                    $item->fec_ini_vigencia,
                    $item->fec_fin_vigencia,
                    $item->fecha_ini_recibo,
                    $item->fecha_fin_recibo,
                    floatval($item->prima_moneda_reasegurador ?? 0),
                    floatval($item->prima_dolar_reasegurador ?? 0),
                    floatval($item->prima_local_reasegurador ?? 0),
                    floatval($item->monto_comision ?? 0),
                    $item->fecha_emision,
                    $item->fecha_anula_poliza,
                    $item->fecha_anula_recibo,
                    $item->tipo_movimiento,
                    '',
                    $item->contrato_reasegurador,
                    $item->facultativo_reasegurador,
                    $item->comision_reasegurador,
                ];
            }

            $headings = [
                'REASEGURADOR',
                'ID PÓLIZA',
                'FECHA VIGENCIA INICIO PÓLIZA',
                'FECHA VIGENCIA FIN DE PÓLIZA',
                'FECHA INICIO RECIBO',
                'FECHA FIN RECIBO',
                'PRIMA MONEDA POR REASEGURADOR',
                'PRIMA DÓLAR POR REASEGURADOR',
                'PRIMA LOCAL POR REASEGURADOR',
                'MONTO COMISIÓN',
                'FECHA EMISIÓN',
                'FECHA DE ANULACIÓN DE LA PÓLIZA',
                'FECHA DE ANULACIÓN DEL RECIBO',
                'TIPO DE MOVIMIENTO',
                'RECUPERACIONES',
                '% CONTRATO POR REASEGURADOR',
                '%FACULTATIVO POR REASEGURADOR',
                '%COMISIÓN RECIBIDA POR REASEGURADOR',
            ];
            return Excel::create('reporteReaseguro', function ($excel) use ($activities, $headings) {
                $excel->sheet('Entries', function ($sheet) use ($activities, $headings) {
                    $sheet->row(1, $headings);
                    $sheet->fromArray($activities, null, 'A2', false, false);

                     $rowIndex = 2;
                    foreach ($activities as $activity) {
                        // Fuerza los valores 0 como flotantes
                        $activity[6] = (float) $activity[7];
                        $activity[7] = (float) $activity[7];
                        $activity[8] = (float) $activity[8];
                        $activity[9] = (float) $activity[9];

                        $sheet->row($rowIndex++, $activity);
                    }

                    // Calcular la última fila
                    $lastRow = count($activities) + 1; // +1 por la fila de encabezados

                    $sheet->setColumnFormat([
                        'B2:B'.$lastRow => 'dd/mm/yyyy',
                        'C2:C'.$lastRow => 'dd/mm/yyyy',
                        'D2:D'.$lastRow => 'dd/mm/yyyy',
                        'E2:E'.$lastRow => 'dd/mm/yyyy',
                        'F2:F'.$lastRow => 'dd/mm/yyyy',

                        'G2:G'.$lastRow => '0.00',
                        'H2:H'.$lastRow => '0.00',
                        'I2:I'.$lastRow => '0.00',
                        'J2:J'.$lastRow => '0.00',
                        'R2:R'.$lastRow => '0.00',

                    ]);

                    $numericColumns = ['B', 'C', 'D', 'F'];

                    foreach ($numericColumns as $col) {
                        $sheet->cells($col.'2:'.$col.$lastRow, function($cells) {
                            $cells->setAlignment('right');
                        });
                    }


                });
            })->download('xlsx');

        } catch (\Exception $e) {
            return redirect('/admin/reportes/descargas_admin')
                ->withErrors($e->getMessage())
                ->withInput();
        }
    }

    public function ppndReport(Request $req, $cpath)
    {
        try {
            $initDate = $req->ppnd_start_date_submit;

            if (empty($initDate)) {
                throw new \Exception("La fecha de calculo no pueden estar vacías, en la sección 'Reporte PPND'");
            }

            $formatdDate = date('j/n/Y', strtotime($initDate));

            $webserviceController = new WebserviceAcselController();
            $trm = $webserviceController->getTrm('US', $formatdDate);
            if ($trm == 0) {
                throw new \Exception('error', 'Error: El TRM no es valido');
            }

            $reinsurancePercentage = AppServiceProvider::$REINSURANCE_PERCENTAGE;

            $query = $this->queryPpndReport($initDate, $trm);

            $headings = [
                'ID PÓLIZA',
                'FECHA VIGENCIA INICIO PÓLIZA',
                'FECHA VIGENCIA FIN DE PÓLIZA',
                'FECHA INICIO RECIBO',
                'FECHA FIN RECIBO',
                'FRACCIONAMIENTO',
                'COMPROBANTE CONTABLLE',
                'PRIMA MONEDA',
                'PRIMA PAGADA',
                'PRIMA DOLAR',
                'PRIMA LOCAL',
                'FECHA EMISIÓN',
                'FECHA DE APROBACIÓN DE LA ANULACIÓN DE LA PÓLIZA',
                'FECHA DE ANULACIÓN DEL RECIBO',
                'TIPO DE MOVIMIENTO',
                'COD  CORREDOR',
                'COD INTERMEDIARIO',
                'NOMBRE INTERMEDIARIO',
                'NOMBRE CORREDOR',
                '%COMISIÓN PAGADA AL INTERMEDIARIO',
                'MONEDA DE LA PÓLIZA',
                '% CONTRATO',
                '%FACULTATIVO',
                'TASA CAMBIO (EMISIÓN)',
                'FC',
                'FPD',
                'PPND DIRECTA LOCAL COBRADA',
                'PPND DIRECTA DOLAR COBRADA',
                'TRM FECHA CALCULO'
            ];

            $activities = [];

            foreach ($query as $item) {
                $activities[] = [
                    $item->consecutive_policy,
                    $item->fec_ini_vigencia,
                    $item->fec_fin_vigencia,
                    $item->fecha_ini_recibo,
                    $item->fecha_fin_recibo,
                    $item->fraccionamiento,
                    $item->receipt_number,
                    floatval($item->prima_moneda ?? 0),
                    floatval($item->prima_pagada ?? 0),
                    floatval($item->prima_dolar ?? 0),
                    floatval($item->prima_local ?? 0),
                    $item->fecha_emision,
                    $item->fecha_anulacion_poliza,
                    $item->fecha_anulacion_recibo,
                    $item->tipo_movimiento,
                    $item->cod_corredor,
                    $item->cod_intermediario,
                    $item->nombre_intermediario,
                    $item->nombre_corredor,
                    $item->comision,
                    $item->moneda_poliza,
                    $item->por_contrato,
                    $item->facultativo,
                    floatval($item->tasa_cambio_emision ?? 0 ),
                    $item->fc,
                    floatval($item->fpd ?? 0 ),
                    floatval($item->ppnd_local_cobrada ?? 0 ),
                    floatval($item->ppnd_dolar_cobrada ?? 0 ),
                    floatval($item->tasa_cambio ?? 0 ),
                ];
            }

             return Excel::create('reportePpnd', function ($excel) use ($activities, $headings) {
                $excel->sheet('Entries', function ($sheet) use ($activities, $headings) {
                    $sheet->row(1, $headings);
                    $sheet->fromArray($activities, null, 'A2', false, false);

                    $rowIndex = 2;
                    foreach ($activities as $activity) {
                        // Fuerza los valores 0 como flotantes
                        $activity[7] = (float) $activity[7];
                        $activity[8] = (float) $activity[8];
                        $activity[9] = (float) $activity[9];
                        $activity[10] = (float) $activity[10];
                        $activity[23] = (float) $activity[23];
                        $activity[25] = (float) $activity[25];
                        $activity[26] = (float) $activity[26];
                        $activity[27] = (float) $activity[27];
                        $activity[28] = (float) $activity[28];

                        $sheet->row($rowIndex++, $activity);
                    }


                    // Calcular la última fila
                    $lastRow = count($activities) + 1; // +1 por la fila de encabezados

                    // Aplicar formato de fecha (día/mes/año)
                    $sheet->setColumnFormat([
                        // Fechas
                        //'B2:B'.$lastRow => 'dd/mm/yyyy',
                        //'C2:C'.$lastRow => 'dd/mm/yyyy',
                        'D2:D'.$lastRow => 'dd/mm/yyyy',
                        'E2:E'.$lastRow => 'dd/mm/yyyy',
                        'L2:L'.$lastRow => 'dd/mm/yyyy',
                        'M2:M'.$lastRow => 'dd/mm/yyyy',
                        'N2:N'.$lastRow => 'dd/mm/yyyy',
                        //'Y2:Y'.$lastRow => 'dd/mm/yyyy',

                        // Floats
                        'H2:K'.$lastRow => '0.00',
                        'T2:W'.$lastRow => '0.00',
                        'Y2:Y'.$lastRow => '0.00',
                        'AA2:AC'.$lastRow => '0.00',
                        'AD2:AD'.$lastRow => '0.00',

                    ]);

                    // Establecer alineación a la derecha para columnas numéricas y fechas
                    $numericColumns = ['D','E', 'F', 'L', 'M', 'N'];

                    foreach ($numericColumns as $col) {
                        $sheet->cells($col.'2:'.$col.$lastRow, function($cells) {
                            $cells->setAlignment('right');
                        });
                    }

                });
            })->download('xlsx');

        } catch (\Exception $e) {
            return redirect('/admin/reportes/descargas_admin')
                ->withErrors($e->getMessage())
                ->withInput();
        }
    }

    public function queryPpndReport($initDate, $trm){

        $consulta = "SELECT 
                                CONCAT('SORT-', LPAD(p.consecutive, 4, '0')) AS consecutive_policy,
                                CONCAT(date_format( p.validity_from ,'%d/%m/%Y'), ' 00:00:00') AS fec_ini_vigencia,
                                CONCAT(date_format( p.validity_to ,'%d/%m/%Y'), ' 00:00:00') AS fec_fin_vigencia,
                                case
	                                when ps.type_receipt = 'cancelacion' then 
	                                	date_format(ps.transaction_date, '%d/%m/%Y')
	                                else
	                                	date_format(t.fecha_ini_recibo, '%d/%m/%Y')
	                            end as fecha_ini_recibo,
	                            case
	                                when ps.type_receipt = 'cancelacion' then 
	                                	date_format(p.validity_to, '%d/%m/%Y')
	                                else
	                                	date_format(t.fecha_fin_recibo, '%d/%m/%Y')
	                            end as fecha_fin_recibo,
                                    t.fraccionamiento,
						            t.calculated_amount AS prima_moneda_old,
						            case when ps.total_amount_acumulado is not null 
                        				then 
											(ps.total_amount_acumulado  + ( ps.total_amount * (case t.periodicity  
                        						when 4 then 12-t.indice
                        						when 2 then 2-t.indice
                        						when 3 then 4-t.indice
                        						when 1 then 0 
                        						else 0 end ) ))
                        				else
                        					(tp.total_amount  + ( p.amount_policy * (case t.periodicity  
                        						when 4 then 12-tp.cant_pagos
                        						when 2 then 2-tp.cant_pagos
                        						when 3 then 4-tp.cant_pagos
                        						when 1 then 0 
                        						else 0 end ) ))
                        				end as prima_moneda,
                                    case when ps.total_amount is not null 
                                    	then
                                    		ps.total_amount
                                    	else
                                    		p.amount_policy
                                    	end as prima_pagada,
						            CASE
						                WHEN p.type_currency <> 'USD' THEN t.calculated_amount/pct.trm
						                ELSE t.calculated_amount
						            END AS prima_dolar,
						            CASE
						                WHEN p.type_currency = 'USD' THEN t.calculated_amount*pct.trm
						                ELSE t.calculated_amount
						            END AS prima_local,
						            date_format(ac.created_at,'%d/%m/%Y') as fecha_emision,
						            date_format(aaa.created_at,'%d/%m/%Y') as fecha_anulacion_poliza,
						            DATE_FORMAT(aee.created_at,'%d/%m/%Y') as fecha_anulacion_recibo,
						            case when  aee.receipt_number is not null then 'Cancelación' else 'Cobro' end AS tipo_movimiento,
						            case when up.cod_intermediario IS not null then up.cod_corredor end AS cod_corredor,
						            case when up.cod_intermediario IS null then up.cod_corredor else up.cod_intermediario END AS cod_intermediario,						
						            p.brokerage_name AS nombre_intermediario,                          
                                    case when trim(lower(p.brokerage_name)) = trim(lower(p.advisor_name)) then '' else p.advisor_name end AS nombre_corredor,
						            up.tipo_corredor,
						            case when up.cod_corredor = '000013' then '0' else '10' end AS comision,
						            p.type_currency AS moneda_poliza,
						            case when YEAR(p.validity_from) >= 2025  then 30 else 0 end AS por_contrato,
						            '' AS facultativo,
						            '$trm' AS tasa_cambio,
						            pct.trm as tasa_cambio_emision,
                                    (t.calculated_amount-tp.total_amount) AS prima_pendiente,
                                    CONCAT(date_format(p.validity_to,'%d/%m/%Y'), ' 00:00:00') AS ff,
                                    CONCAT(date_format('$initDate','%d/%m/%Y'), ' 23:59:59') AS fc,
                                    CONCAT(date_format(p.validity_from,'%d/%m/%Y'), ' 00:00:00') AS fd,
                                    FORMAT( IF(
                                            '$initDate' < p.validity_from, 
                                            1, 
                                            IF(
                                                p.validity_to <= '$initDate', 
                                                0, 
                                                FORMAT((TIMESTAMPDIFF(SECOND, CONCAT('$initDate',' 23:59:59'), CONCAT(p.validity_to,' 00:00:00')) / 86400)+ 1, 10)
                                                                /
                                                                FORMAT((TIMESTAMPDIFF(SECOND, CONCAT(p.validity_from,' 00:00:00'), CONCAT(p.validity_to,' 00:00:00')) / 86400)+1, 10)
                                            )
                                        ) , 10) AS fpd,
                                    IF(p.type_currency = 'CRC', 
                                         
                                            (case when ps.total_amount_acumulado is not null 
                                                then 
                                                    (ps.total_amount_acumulado  + ( ps.total_amount * (case t.periodicity  
                                                        when 4 then 12-t.indice
                                                        when 2 then 2-t.indice
                                                        when 3 then 4-t.indice
                                                        when 1 then 0 
                                                        else 0 end ) ))
                                                else
                                                    (tp.total_amount  + ( p.amount_policy * (case t.periodicity  
                                                        when 4 then 12-tp.cant_pagos
                                                        when 2 then 2-tp.cant_pagos
                                                        when 3 then 4-tp.cant_pagos
                                                        when 1 then 0 
                                                        else 0 end ) ))
                                                end)
                                                * 
                                            IF(
                                                '$initDate' < p.validity_from, 
                                                1, 
                                                IF(
                                                    p.validity_to <= '$initDate', 
                                                    0, 
                                                    FORMAT((TIMESTAMPDIFF(SECOND, CONCAT('$initDate',' 23:59:59'), CONCAT(p.validity_to,' 00:00:00')) / 86400)+ 1, 10)
                                                                    /
                                                                    FORMAT((TIMESTAMPDIFF(SECOND, CONCAT(p.validity_from,' 00:00:00'), CONCAT(p.validity_to,' 00:00:00')) / 86400)+1, 10)
                                                )
                                            )
                                                , 0) AS ppnd_local_cobrada,
                                    IF(p.type_currency = 'USD', 
                                        (
                                            (case when ps.total_amount_acumulado is not null 
                                                then 
                                                    (ps.total_amount_acumulado  + ( ps.total_amount * (case t.periodicity  
                                                        when 4 then 12-t.indice
                                                        when 2 then 2-t.indice
                                                        when 3 then 4-t.indice
                                                        when 1 then 0 
                                                        else 0 end ) ))
                                                else
                                                    (tp.total_amount  + ( p.amount_policy * (case t.periodicity  
                                                        when 4 then 12-tp.cant_pagos
                                                        when 2 then 2-tp.cant_pagos
                                                        when 3 then 4-tp.cant_pagos
                                                        when 1 then 0 
                                                        else 0 end ) ))
                                                end) 
                                                *
                                             CASE WHEN p.type_currency = 'USD' THEN $trm ELSE 1 END 
                                          ) 
                                        * 
                                            FORMAT( IF(
                                                '$initDate' < p.validity_from, 
                                                1, 
                                                IF(
                                                    p.validity_to <= '$initDate', 
                                                    0, 
                                                    FORMAT((TIMESTAMPDIFF(SECOND, CONCAT('$initDate',' 23:59:59'), CONCAT(p.validity_to,' 00:00:00')) / 86400)+ 1, 10)
                                                                    /
                                                                    FORMAT((TIMESTAMPDIFF(SECOND, CONCAT(p.validity_from,' 00:00:00'), CONCAT(p.validity_to,' 00:00:00')) / 86400)+1, 10)
                                                )
                                            ) , 10)
                                        , 0) AS ppnd_dolar_cobrada,
                                    IF(p.type_currency = 'CRC',  
                                        (t.calculated_amount-tp.total_amount)
                                        *
                                        IF(
                                                '$initDate' < p.validity_from, 
                                                1, 
                                                IF(
                                                    p.validity_to <= '$initDate', 
                                                    0, 
                                                    (DATEDIFF(p.validity_to, '$initDate') + 1) / (DATEDIFF(p.validity_to, p.validity_from) + 1)
                                                )
                                            ) 
                                                , 0) AS ppnd_directa_local_cobrar,
                                    IF(p.type_currency = 'USD',  
                                        (t.calculated_amount-tp.total_amount)
                                        *
                                            FORMAT( IF(
                                                        '$initDate' < p.validity_from, 
                                                        1, 
                                                        IF(
                                                            p.validity_to <= '$initDate', 
                                                            0, 
                                                            FORMAT((TIMESTAMPDIFF(SECOND, CONCAT('$initDate',' 23:59:59'), CONCAT(p.validity_to,' 00:00:00')) / 86400)+ 1, 10)
                                                                            /
                                                                            FORMAT((TIMESTAMPDIFF(SECOND, CONCAT(p.validity_from,' 00:00:00'), CONCAT(p.validity_to,' 00:00:00')) / 86400)+1, 10)
                                                        )
                                                    ) , 10)
                                                , 0) AS ppnd_directa_dolar_cobrar,
                                    case when ae.receipt_number is not null then ae.receipt_number else aee.receipt_number end as receipt_number
                             FROM (  
									select 
										r.consecutive, 
										r.cuota_numero  as indice, 
										r.calculated_amount,
										r.validity_from,
										r.validity_to, 
										r.periodicity, 
										r.meses,
										r.fraccionamiento, 
										r.fecha_ini_recibo, 
										r.fecha_fin_recibo
									from report_payments_view r
                                   ) AS t                                  
                            left join  (
                             	SELECT 
							      ps.id,
							      ps.activity_id,
							      p.consecutive, 
								  ps.trm,
								  ps.total_amount,
								  ps.transaction_date,
								  ps.type_receipt,
								  SUM(ps.total_amount) OVER (PARTITION BY p.consecutive ORDER BY ps.id) AS total_amount_acumulado,
								  ROW_NUMBER() OVER (PARTITION BY p.consecutive ORDER BY ps.id ASC) AS indice
								FROM policy_sort_collections ps
								LEFT JOIN activities a ON (a.id=ps.activity_id)
								LEFT JOIN policy_sorts p ON (p.activity_id=a.parent_id)
								LEFT JOIN activity_actions aa ON (aa.activity_id=p.activity_id AND aa.action_id=16)
								WHERE p.consecutive is not null AND ps.payment_status = 'approved' 
								ORDER BY p.consecutive, ps.id asc
                            ) AS ps on ps.consecutive=t.consecutive and ps.indice=t.indice
                            LEFT JOIN activities AS a ON a.id = ps.activity_id
                            LEFT JOIN policy_sorts AS p ON p.consecutive = t.consecutive
                            LEFT JOIN activities AS ap ON ap.id = p.activity_id
                            left JOIN (
                                            select a.parent_id, 
                                                    count(*) as cant_pagos, 
                                                    sum(psc.total_amount) as total_amount,
                                                    sum( case when ps.type_currency  = 'CRC' then 
                                                            psc.total_amount
                                                           else 
                                                             psc.total_amount*psc.trm
                                                           end ) as total_amount_colones,
                                                     sum( case when ps.type_currency  = 'USD' then 
                                                            psc.total_amount
                                                           else 
                                                            psc.total_amount/psc.trm 
                                                            end ) as total_amount_dolares
                                           from policy_sort_collections psc
                                           left join activities a on (a.id=psc.activity_id)
                                           left join policy_sorts ps on (ps.activity_id=a.parent_id)
                                           where psc.payment_status = 'approved'
                                           group by a.parent_id
                                        ) tp on (tp.parent_id=p.activity_id)
                             LEFT JOIN (
                                            SELECT
                                                a.parent_id,
                                                pss.trm
                                            FROM policy_sort_collections AS pss
                                            LEFT JOIN activities AS a ON a.id = pss.activity_id
                                            WHERE pss.type_receipt = 'emission' AND pss.payment_status = 'approved'
                                            GROUP BY a.parent_id
                                        ) AS pct ON pct.parent_id = p.activity_id 
                            left join activity_actions ac on (ac.activity_id=p.activity_id and ac.action_id = 16)
                            left join (
                                        select REPLACE(s.username COLLATE utf8mb4_unicode_ci, 'CO-', '') as username,
                                               REPLACE(s.code_mnk, 'CO-', '') as cod_corredor,
                                               REPLACE(s.code_correduria, 'CO-', '') as cod_intermediario,
                                               s.tipo_corredor,
                                               s.correduria,
                                               s.advisor_name,
                                               s.brokerage_name
                                        from users s
                                        group by REPLACE(s.username COLLATE utf8mb4_unicode_ci, 'CO-', '')
                                       ) up on (up.username=REPLACE(p.code COLLATE utf8mb4_unicode_ci, 'CO-', ''))   
                            LEFT JOIN activity_actions aa ON (aa.activity_id=p.activity_id AND aa.action_id=16)
                            LEFT JOIN activity_actions aaa ON (aaa.activity_id=p.activity_id AND aaa.action_id=32)
                            left join (
                                   select ae.number_payment,ae.receipt_number
		                           from accounting_entries ae
		                           where ae.cod_oper = '003' 
		                           group by ae.number_payment,ae.receipt_number
                            	) ae on ae.number_payment=ps.id
                            left join (
                                   select ae.number_payment,ae.receipt_number, ae.created_at
		                           from accounting_entries ae
		                           where ae.cod_oper = '002' 
		                           group by ae.number_payment,ae.receipt_number
                            	) aee on aee.number_payment=ps.id
                            WHERE ap.state_id <> " . StatePoliza::TRAMITE_ANULADO . " 
                                    AND (
                                        p.temporality <> 'short' 
                                        OR (p.temporality = 'short' AND DATE(p.validity_to) >= '$initDate')
                                    )
                                    AND DATE(aa.created_at) <= '$initDate' 
                        ORDER BY p.consecutive
                       ";

        $query = DB::select($consulta);

        return $query;
    }

    public function generateActivePortfolioReport(Request $req, $cpath)
    {
        try {
            $initDate = $req->cartera_start_date_submit;

            if (empty($initDate)) {
                throw new \Exception("La fecha de calculo no pueden estar vacías, en la sección 'Reporte cartera vigente'");
            }

            $query = $this->queryActivePortfolio($initDate);

            $headings = [
                'ID PÓLIZA',
                'NOMBRE CLIENTE',
                'ID CLIENTE',
                '%  DE TASA',
                'FECHA INICIO PÓLIZA',
                'FECHA FIN DE PÓLIZA',
                'FRACCIONAMIENTO',
                'PRIMA MONEDA',
                'PRIMA DOLAR',
                'PRIMA LOCAL',
                'PRIMA PAGADA',
                'FECHA SUSCRIPCIÓN',
                'NOMBRE CORREDOR',
                'COD CORREDOR',
                'COD INTERMEDIARIO',
                'NOMBRE INTERMEDIARIO',
                '%COMISIÓN PAGADA AL INTERMEDIARIO',
                'MONTO COMISIÓN PAGADA AL INTERMEDIARIO',
                'MONEDA DE LA PÓLIZA',
                '#TRABAJADORES',
                'NÓMINA DEL MES',
                'ACTIVIDAD ECONÓMICA',
                'TIPO DE CONTRATANTE',
                'LUGAR DE LA EMPRESA',
                'PROVINCIA DE LA PÓLIZA',
                'CANTON DE LA PÓLIZA',
                'DISTRITO',
                'CANTIDAD DE PLANILLAS CARGADAS',
                'CANTIDAD DE RECIBOS GENERADOS',
                '% CONTRATO',
                '%FACULTATIVO',
                'TIPO DE CAMBIO'
            ];

            $activities = [];

            foreach ($query as $item) {
                $activities[] = [
                $item->consecutive_policy ,
                $item->full_name ,
                $item->id_cliente ,
                $item->tasa_emision ,
                $item->fec_ini_vigencia ,
                $item->fec_fin_vigencia ,
                $item->fracionamiento ,
                floatval($item->prima_moneda ?? 0),
                floatval($item->prima_dolar ?? 0),
                floatval($item->prima_local ?? 0),
                floatval($item->prima_pagada ?? 0),
                $item->fecha_suscription ,
                $item->nombre_corredor ,
                $item->cod_corredor ,
                $item->cod_intermediario ,
                $item->nombre_intermediario ,
                $item->comision_pagada ,
                floatval($item->mont_comision_paga ?? 0),
                $item->moneda_poliza ,
                $item->num_trabajadores ,
                floatval($item->nomina_anual ?? 0),
                $item->activity_name,
                $item->tipo_contratante,
                $item->employer_address,
                $item->provincias,
                $item->canton,
                $item->distrito,
                $item->cant_planillas,
                $item->cant_recibos,
                $item->por_cotrato,
                $item->por_facultativo,
                floatval($item->trm_emision ?? 0),
                ];
            }

            return Excel::create('reporteCarteraVigente', function ($excel) use ($activities, $headings) {
                $excel->sheet('Entries', function ($sheet) use ($activities, $headings) {
                    $sheet->row(1, $headings);
                    $sheet->fromArray($activities, null, 'A2', false, false);

                    $rowIndex = 2;
                    foreach ($activities as $activity) {
                        // Fuerza los valores 0 como flotantes
                        $activity[7] = (float) $activity[7];
                        $activity[8] = (float) $activity[8];
                        $activity[9] = (float) $activity[9];
                        $activity[10] = (float) $activity[10];
                        $activity[17] = (float) $activity[17];
                        $activity[20] = (float) $activity[20];
                        $activity[31] = (float) $activity[31];

                        $sheet->row($rowIndex++, $activity);
                    }


                    // Calcular la última fila
                    $lastRow = count($activities) + 1; // +1 por la fila de encabezados

                    // Aplicar formato de fecha (día/mes/año)
                    $sheet->setColumnFormat([
                        // Formatos para fechas
                        'E2:E'.$lastRow => 'dd/mm/yyyy',
                        'F2:F'.$lastRow => 'dd/mm/yyyy',
                        'L2:L'.$lastRow => 'dd/mm/yyyy',

                        // Formatos para valores monetarios (2 decimales)
                        'H2:H'.$lastRow => '0.00',
                        'I2:I'.$lastRow => '0.00',
                        'J2:J'.$lastRow => '0.00',
                        'K2:K'.$lastRow => '0.00',
                        'R2:R'.$lastRow => '0.00',
                        'U2:U'.$lastRow => '0.00',
                        'AF2:AF'.$lastRow => '0.00',

                    ]);

                    // Establecer alineación a la derecha para columnas numéricas y fechas
                    $numericColumns = ['E', 'F', 'L'];

                    foreach ($numericColumns as $col) {
                        $sheet->cells($col.'2:'.$col.$lastRow, function($cells) {
                            $cells->setAlignment('right');
                        });
                    }

                });
            })->download('xlsx');


        } catch (\Exception $e) {
            return redirect('/admin/reportes/descargas_admin')
                ->withErrors($e->getMessage())
                ->withInput();
        }
    }

    public function generateActivePortfolioReport_v2(Request $req, $cpath)
    {
        try {
            $initDate = $req->cartera_start_date_submit;

            if (empty($initDate)) {
                throw new \Exception("La fecha de calculo no pueden estar vacías, en la sección 'Reporte cartera vigente'");
            }

            $activities = [];

            $query = $this->queryActivePortfolio($initDate);

            foreach ($query as $item) {
                $activities[] = [
                    $item->consecutive_policy,
                    $item->full_name,
                    intval($item->id_cliente),
                    $item->tasa_emision,
                    $item->fec_ini_vigencia,
                    $item->fec_fin_vigencia,
                    $item->fracionamiento,
                    number_format($item->prima_moneda ?? 0, 2, ',', ''),
                    number_format($item->prima_dolar ?? 0, 2, ',', ''),
                    number_format($item->prima_local ?? 0, 2, ',', ''),
                    number_format($item->prima_pagada ?? 0, 2, ',', ''),
                    $item->fecha_suscription,
                    $item->nombre_corredor,
                    $item->cod_corredor,
                    $item->cod_intermediario,
                    $item->nombre_intermediario,
                    $item->comision_pagada,
                    number_format($item->mont_comision_paga ?? 0, 2, ',', ''),
                    $item->moneda_poliza,
                    $item->num_trabajadores,
                    number_format($item->nomina_anual ?? 0, 2, ',', ''),
                    $item->activity_name,
                    $item->tipo_contratante,
                    $item->employer_address,
                    $item->provincias,
                    $item->canton,
                    $item->distrito,
                    $item->cant_planillas,
                    $item->cant_recibos,
                    $item->por_cotrato,
                    $item->por_facultativo,
                    $item->trm_emision
                ];
            }

            $headings = [
                'ID PÓLIZA',
                'NOMBRE CLIENTE',
                'ID CLIENTE',
                '%  DE TASA',
                'FECHA INICIO PÓLIZA',
                'FECHA FIN DE PÓLIZA',
                'FRACCIONAMIENTO',
                'PRIMA MONEDA',
                'PRIMA DOLAR',
                'PRIMA LOCAL',
                'PRIMA PAGADA',
                'FECHA SUSCRIPCIÓN',
                'NOMBRE CORREDOR',
                'COD  CORREDOR',
                'COD INTERMEDIARIO',
                'NOMBRE INTERMEDIARIO',
                '%COMISIÓN PAGADA AL INTERMEDIARIO',
                'MONTO COMISIÓN PAGADA AL INTERMEDIARIO',
                'MONEDA DE LA PÓLIZA',
                '#TRABAJADORES',
                'NÓMINA ANUAL',
                'ACTIVIDAD ECONÓMICA',
                'TIPO DE CONTRATANTE',
                'LUGAR DE LA EMPRESA',
                'PROVINCIA DE LA PÓLIZA',
                'CANTON DE LA PÓLIZA',
                'DISTRITO',
                'CANTIDAD DE PLANILLAS CARGADAS',
                'CANTIDAD DE RECIBOS GENERADOS',
                '% CONTRATO',
                '%FACULTATIVO',
                'TIPO DE CAMBIO'
            ];

            return Excel::create('reporteCarteraVigente', function ($excel) use ($activities, $headings) {
                $excel->sheet('Entries', function ($sheet) use ($activities, $headings) {
                    $sheet->row(1, $headings);
                    $sheet->fromArray($activities, null, 'A2', false, false);

                    // Calcular la última fila
                    $lastRow = count($activities) + 1; // +1 por la fila de encabezados

                    // Aplicar formato de fecha (día/mes/año)
                    $sheet->setColumnFormat([
                        // Formatos para fechas
                        'E2:E'.$lastRow => 'dd/mm/yyyy',
                        'F2:F'.$lastRow => 'dd/mm/yyyy',
                        'L2:L'.$lastRow => 'dd/mm/yyyy',

                        // Formatos para porcentajes
//                        'D2:D'.$lastRow => '0.00%',          // % DE TASA
//                        'Q2:Q'.$lastRow => '0.00%',          // %COMISIÓN PAGADA AL INTERMEDIARIO
//                        'AD2:AD'.$lastRow => '0.00%',        // % CONTRATO
//                        'AE2:AE'.$lastRow => '0.00%',        // %FACULTATIVO

                        // Formatos para valores monetarios (2 decimales)
                        'H2:H'.$lastRow => '#,##0.00',       // PRIMA MONEDA
                        'I2:I'.$lastRow => '#,##0.00',       // PRIMA DÓLAR
                        'J2:J'.$lastRow => '#,##0.00',       // PRIMA LOCAL
                        'K2:K'.$lastRow => '#,##0.00',       // PRIMA PAGADA
                        'R2:R'.$lastRow => '#,##0.00',       // MONTO COMISIÓN
                        'U2:U'.$lastRow => '#,##0.00',       // NÓMINA ANUAL
                        'AF2:AF'.$lastRow => '#,##0.00',     // TIPO DE CAMBIO

                        // Formatos para enteros
                        'T2:T'.$lastRow => '#,##0',          // #TRABAJADORES
                        'AB2:AB'.$lastRow => '#,##0',        // CANTIDAD DE PLANILLAS
                        'AC2:AC'.$lastRow => '#,##0'         // CANTIDAD DE RECIBOS
                    ]);

                    // Establecer alineación a la derecha para columnas numéricas y fechas
                    $numericColumns = ['D', 'E', 'F', 'H', 'I', 'J', 'K', 'L', 'Q', 'R', 'T', 'U', 'AB', 'AC', 'AD', 'AE', 'AF'];

                    foreach ($numericColumns as $col) {
                        $sheet->cells($col.'2:'.$col.$lastRow, function($cells) {
                            $cells->setAlignment('right');
                        });
                    }

                    // Estilo para la fila de encabezados
                    $sheet->cells('A1:AF1', function($cells) {
                        $cells->setFontWeight('bold');
                        $cells->setBackground('#D3D3D3');
                        $cells->setAlignment('center');
                    });
                });
            })->download('xlsx');
        } catch (\Exception $e) {
            return redirect('/admin/reportes/descargas_admin')
                ->withErrors($e->getMessage())
                ->withInput();
        }
    }

    public function queryActivePortfolio($date){

        $consulta="select
                    concat('SORT-', lpad(p.consecutive, 4, '0')) AS consecutive_policy,
                    af.doc_number AS id_cliente,
                    date_format(p.validity_from, '%d/%m/%Y') AS fec_ini_vigencia,
                    date_format(p.validity_to, '%d/%m/%Y') AS fec_fin_vigencia,
                    (case
                        when (p.periodicity = '1') then 'Anual'
                        when (p.periodicity = '2') then 'Semestral'
                        when (p.periodicity = '3') then 'Trimestral'
                        when (p.periodicity = '4') then 'Mensual'
                        else 'Periodo corto'
                    end) AS fracionamiento,
                    (case
                        when (p.temporality = 'permanent') then (case
                            p.periodicity when 1 then (p.annual_calculation_amount * 1)
                            when 2 then (p.semiannual_calculation_amount * 2)
                            when 3 then (p.quarterly_calculation_amount * 4)
                            when 4 then (p.monthly_calculation_amount * 12)
                            else p.annual_calculation_amount
                        end)
                        else p.single_payment_value
                    end) AS prima_moneda,
                    (case
                        when (p.type_currency <> 'USD') then ((case
                            when (p.temporality = 'permanent') then (case
                                p.periodicity when 1 then (p.annual_calculation_amount * 1)
                                when 2 then (p.semiannual_calculation_amount * 2)
                                when 3 then (p.quarterly_calculation_amount * 4)
                                when 4 then (p.monthly_calculation_amount * 12)
                                else p.annual_calculation_amount
                            end)
                            else p.single_payment_value
                        end) / pct.trm)
                        else (case
                            when (p.temporality = 'permanent') then (case
                                p.periodicity when 1 then (p.annual_calculation_amount * 1)
                                when 2 then (p.semiannual_calculation_amount * 2)
                                when 3 then (p.quarterly_calculation_amount * 4)
                                when 4 then (p.monthly_calculation_amount * 12)
                                else p.annual_calculation_amount
                            end)
                            else p.single_payment_value
                        end)
                    end) AS prima_dolar,
                    (case
                        when (p.type_currency = 'USD') then ((case
                            when (p.temporality = 'permanent') then (case
                                p.periodicity when 1 then (p.annual_calculation_amount * 1)
                                when 2 then (p.semiannual_calculation_amount * 2)
                                when 3 then (p.quarterly_calculation_amount * 4)
                                when 4 then (p.monthly_calculation_amount * 12)
                                else p.annual_calculation_amount
                            end)
                            else p.single_payment_value
                        end) * pct.trm)
                        else (case
                            when (p.temporality = 'permanent') then (case
                                p.periodicity when 1 then (p.annual_calculation_amount * 1)
                                when 2 then (p.semiannual_calculation_amount * 2)
                                when 3 then (p.quarterly_calculation_amount * 4)
                                when 4 then (p.monthly_calculation_amount * 12)
                                else p.annual_calculation_amount
                            end)
                            else p.single_payment_value
                        end)
                    end) AS prima_local,
                    tp.total_amount AS prima_pagada,
                    date_format(psc.transaction_date, '%d/%m/%Y') AS fecha_suscription,
                    up.cod_corredor AS cod_corredor,
                    up.cod_intermediario AS cod_intermediario,
                    case when up.cod_corredor = '000013' then 0 else 10 end AS comision_pagada,
                    p.type_currency AS moneda_poliza,
                    (case
                        when (ps.total_affiliates is null) then 0
                        else ps.total_affiliates
                    end) AS num_trabajadores,
                    (case
                        when (ps.total_salaries is null) then 0
                        else ps.total_salaries
                    end) AS nomina_anual,
                    ec.activity_name AS activity_name,
                    '' AS contratante,
                    '' AS cliente,
                    case when af.doc_type <> 'CJ' then 'Persona natural' else 'Persona jurídica' end AS tipo_contratante,
                    af.employer_address AS employer_address,
                    pv.name AS provincias,
                    c.name AS canton,
                    case when YEAR(p.validity_from) >= 2025 then '30,0' else '0' end AS por_cotrato,
                    '' AS por_facultativo,
                    p.validity_from AS validity_from,
                    p.validity_to AS validity_to,
                    af.full_name AS full_name,
                    p.brokerage_name AS nombre_intermediario,                  
                    case when trim(lower(p.brokerage_name)) = trim(lower(p.advisor_name)) then '' else p.advisor_name end AS nombre_corredor,
                    pct.trm AS trm_emision,
                    cast(ac.created_at as date) AS fecha_emision,
                    d.name AS distrito,
                    pl.cant_planillas-1 AS cant_planillas,
                    tpp.cant_recibos AS cant_recibos,
                    case when up.cod_corredor = '000013' then 0 else (tp.total_amount * 0.1) end AS mont_comision_paga,
                    (case
                        when (p.periodicity = '1') then p.anual_percentage
                        when (p.periodicity = '2') then p.semestral_percentage
                        when (p.periodicity = '3') then p.trimestral_percentage
                        when (p.periodicity = '4') then p.mensual_percentage
                        else p.unico_percentage
                    end) AS tasa_emision
                from policy_sorts p
                left join activities a on a.id = p.activity_id
                left join affiliates af on af.id = a.affiliate_id
                left join (
                    select
                        replace((s.username collate utf8mb4_unicode_ci), 'CO-', '') AS username,
                        replace(s.code_correduria, 'CO-', '') AS cod_corredor,
                        replace(s.code_mnk, 'CO-', '') AS cod_intermediario
                    from
                        users s
                    group by
                        replace((s.username collate utf8mb4_unicode_ci), 'CO-', '')) up on up.username = replace((p.code collate utf8mb4_unicode_ci), 'CO-', '')
                left join economic_activities ec on ec.code = p.activity_economic_id
                left join provinces pv on pv.id = af.province
                left join cantons c on c.id = af.canton and c.province_id = af.province
                left join (
                    select
                        a.parent_id AS parent_id,
                        ps.id AS id,
                        ps.total_affiliates AS total_affiliates,
                        ps.total_salaries AS total_salaries
                    from
                        (policy_spreadsheets ps
                    left join activities a on
                        ((a.id = ps.activity_id)))
                    where
                        (ps.id = (
                        select
                            max(ps_inner.id)
                        from
                            (policy_spreadsheets ps_inner
                        left join activities a_inner on
                            ((a_inner.id = ps_inner.activity_id)))
                        where
                            (a_inner.parent_id = a.parent_id)))) ps on ps.parent_id = p.activity_id
                left join (
                    select
                        a.parent_id AS parent_id,
                        psc.total_amount AS total_amount,
                        psc.id AS id,
                        psc.type_receipt AS type_receipt,
                        cast(psc.transaction_date as date) AS transaction_date
                    from
                        (policy_sort_collections psc
                    left join activities a on
                        ((a.id = psc.activity_id)))
                    where
                        ((psc.type_receipt in ('emission', 'renewal'))
                            and (psc.payment_status = 'approved')
                                and (psc.id = (
                                select
                                    max(ps_collection.id)
                                from
                                    policy_sort_collections ps_collection
                                where
                                    (ps_collection.id = psc.id))))) psc on psc.parent_id = p.activity_id
                left join (
                    select
                        a.parent_id AS parent_id,
                        pss.trm AS trm
                    from
                        (policy_sort_collections pss
                    left join activities a on
                        ((a.id = pss.activity_id)))
                    where
                        ((pss.type_receipt = 'emission')
                            and (pss.payment_status = 'approved'))
                    group by
                        a.parent_id) pct on pct.parent_id = p.activity_id
                left join (
                    select
                        a.parent_id AS parent_id,
                        count(0) AS cant_pagos,
                        sum(psc.total_amount) AS total_amount,
                        sum((case when (ps.type_currency = 'CRC') then psc.total_amount else (psc.total_amount * psc.trm) end)) AS total_amount_colones,
                        sum((case when (ps.type_currency = 'USD') then psc.total_amount else (psc.total_amount / psc.trm) end)) AS total_amount_dolares
                    from
                        ((policy_sort_collections psc
                    left join activities a on
                        ((a.id = psc.activity_id)))
                    left join policy_sorts ps on
                        ((ps.activity_id = a.parent_id)))
                    where
                        (psc.payment_status = 'approved')
                    group by
                        a.parent_id) tp on tp.parent_id = p.activity_id
                left join trm_rates tr on cast(tr.created_at as date) = psc.transaction_date
                left join activity_actions ac on ac.activity_id = p.activity_id and ac.action_id = 16
                left join activity_actions cp on cp.activity_id = p.activity_id and cp.action_id = 32 
                left join districts d on d.id = af.district and d.canton_id = af.canton and d.province_id = af.province
                left join (
                    select
                        a.parent_id AS activity_poliza,
                        count(1) AS cant_planillas
                    from policy_spreadsheets ps
                    left join activities a on a.id = ps.activity_id
                    where date(a.created_at) <= '$date'
                    group by a.parent_id
                    ) pl on pl.activity_poliza = p.activity_id
                left join (
                    select
                        a.parent_id AS activity_poliza,
                        count(1) AS cant_recibos
                    from policy_sort_collections ps
                    left join activities a on a.id = ps.activity_id
                    where date(a.created_at) <= '$date'
                    group by a.parent_id
                        ) tpp on tpp.activity_poliza = p.activity_id
                where p.consecutive is not null and a.state_id <> 196 and a.state_id = 20 and p.validity_to >= CURRENT_DATE and date(ac.created_at) <= '$date'
                        and (cp.created_at is null or date(cp.created_at) >= '$date') 
                group by p.consecutive
                order by
                    p.consecutive;";


        //$consulta = "select * from report_active_portfolio_view where fecha_emision <= '$initDate' ";
        $query = DB::select($consulta);

        return $query;

    }

    //MS-2234 Reporte pago a terceros
    public function reportThirdPartyPayments(Request $req, $cpath)
    {
        try {

            $initDate = $req->terceros_start_date_submit;
            $lastDate = $req->terceros_end_date_submit;

            if (empty($initDate) || empty($lastDate)) {
                throw new \Exception("Las fechas no pueden estar vacías, en la sección 'Reporte pago a terceros'");
            }

            if (strtotime($lastDate) < strtotime($initDate)) {
                throw new \Exception("La fecha desde no puede ser menor a la fecha hasta, en la sección 'Reporte pago a terceros'");
            }

            $activities = [];

            $consulta = " SELECT af.full_name, 
                                 af.doc_number, 
                                 ac.number_payment AS num_fact, 
                                 DATE(ps.transaction_date) AS fec_fact,
                                '' AS num_oblig,
                                 ac.receipt_number AS numcomp,
                                 ac.type AS concepto,
                                 '' AS condicion,
                                 '' AS porc_agrado,
                                 0 AS mont_gravado,
                                 '' AS mont_excent,
                                 ac.debit AS mot_no_suject,
                                 0 AS mot_iva,
                                 0 AS rentam
                        from accounting_entries ac
                        LEFT JOIN policy_sorts p ON (p.consecutive=TRIM(LEADING '0' FROM REPLACE(ac.number_policy, 'SORT-', '')))
                        LEFT JOIN activities a ON (a.id=p.activity_id)
                        LEFT JOIN affiliates af ON (af.id=a.affiliate_id)
                        LEFT JOIN policy_sort_collections ps ON (ps.id=ac.number_payment)
                        where ac.cod_oper = '011' AND ac.debit <> 0 AND ac.created_at BETWEEN :initDate AND :lastDate
                        ORDER BY ac.number_policy ";

            $query = DB::select($consulta, [
                'initDate' => $initDate . ' 00:00:00',
                'lastDate' => $lastDate . ' 23:59:59'
            ]);

            foreach ($query as $item) {
                $activities[] = [
                    $item->full_name,
                    $item->doc_number,
                    $item->num_fact,
                    $item->fec_fact,
                    $item->num_oblig,
                    $item->numcomp,
                    $item->concepto,
                    $item->condicion,
                    $item->porc_agrado,
                    $item->mont_gravado,
                    $item->mont_excent,
                    number_format($item->mot_no_suject ?? 0, 2, ',', ''),
                    $item->mot_iva,
                    $item->rentam,
                ];
            }

            $headings = [
                'NOMBRE DEL CLIENTE',
                'CEDULA',
                'NUMERO DE FACTURA',
                'FEC_FACT',
                'NUM. OBLIG.',
                'NUMCOMP',
                'CONCEPTO',
                'CONDICION',
                'PORC. DE GRAVADO',
                'MONTO GRAVADO NETO ',
                'MONTO EXENTO NETO ',
                'MONTO NO SUJETO ',
                'MONTO IVA ',
                'RENTA '
            ];

            return Excel::create('reportePagoTerceros', function ($excel) use ($activities, $headings) {
                $excel->sheet('Entries', function ($sheet) use ($activities, $headings) {
                    $sheet->row(1, $headings);
                    $sheet->fromArray($activities, null, 'A2', false, false);
                });
            })->download('xlsx');

        } catch (\Exception $e) {
            return redirect('/admin/reportes/descargas_admin')
                ->withErrors($e->getMessage())
                ->withInput();
        }
    }

    //MS-2235 Reporte OYNR
    public function reportOynr(Request $req, $cpath)
    {
        try {
            $initDate = $req->oynr_start_date_submit;

            if (empty($initDate)) {
                throw new \Exception("Las fecha no pueden estar vacía, en la sección 'Reporte OYNR'");
            }

            $activities = [];
            $activitiesTwo = [];
            $activitiesThree = [];
            $activitiesReserve = [];
            $activitiesPendiente = [];
            $activitiesPendienteAno = [];

            $consulta = "SELECT * FROM report_oynr_view WHERE date(created_at) <= '$initDate' ";
            $query = DB::select($consulta);

            foreach ($query as $item) {
                $activities[] = [
                    $item->id_siniestro,
                    $item->no_caso,
                    $item->cont,
                    $item->id_cliente,
                    $item->trabajador_afectado,
                    $item->nombre_afectado,
                    $item->type_currency,
                    $item->fec_movimiento,
                    $item->tipo_mov,
                    floatval($item->monto_mov_moneda ?? 0),
                    floatval($item->monto_mov_dolar ?? 0),
                    $item->notif_ocurr,
                    $item->fec_ocurrencia_siniestro,
                    $item->fec_noti,
                    $item->por_contrato
                ];
            }

            $headings = [
                'ID SINIESTRO',
                'NO CASO',
                'CONT',
                'ID CLIENTE',
                'ID TRABAJADOR AFECTADO',
                'NOMBRE TRABAJADOR AFECTADO',
                'MONEDA DEL SINIESTRO',
                'FECHA DE MOVIMIENTO',
                'TIPO DE MOVIMIENTO',
                'MONTO MOVIMIENTO MONEDA',
                'MONTO MOVIMIENTO DÓLAR',
                'NOTIF-OCURR',
                'FECHA OCURRENCIA DEL SINIESTRO',
                'FECHA NOTIFICACIÓN DEL SINIESTRO',
                '% CONTRATO'
            ];

            $consultaTwo = "SELECT SUM(cont) as sum_cont, 
                                   SUM(monto_mov_moneda) as sum_mont_mov, 
                                   SUM(notif_ocurr) as sum_notif_ocurr, 
                                   '01/12/2024' as fec_ini_vig_policy_one,
                                   DATEDIFF('$initDate', '2024-12-01') as dias_fec 
                            FROM report_oynr_view 
                            WHERE date(created_at) <= '$initDate' ";

            $resultTwo = DB::selectOne($consultaTwo);

            $headingsTwo = [
                'Suma de siniestros',
                'Monto movimiento',
                'Suma de notif-ocurr',
                'Inicio de vigencia de primer póliza'
            ];

            $activitiesTwo[] = [
                intval($resultTwo->sum_cont ?? 0),
                floatval($resultTwo->sum_mont_mov ?? 0),
                floatval($resultTwo->sum_notif_ocurr ?? 0),
                $resultTwo->fec_ini_vig_policy_one
            ];

            $headingsThree = [
                'Plazo promedio demora en la denuncia del siniestro diario',
                'Costo de siniestros promedio',
                'Promedio de siniestros diarios',
                'OYNR'
            ];

            $oynr = round((max(1, $resultTwo->sum_notif_ocurr) / $resultTwo->sum_cont),5) *
                    round((max(1, $resultTwo->sum_mont_mov) / $resultTwo->sum_cont) ,5) *
                    round( $resultTwo->sum_cont / max(1, $resultTwo->dias_fec) , 5);

            $activitiesThree[] = [
                floatval((max(1, $resultTwo->sum_notif_ocurr / $resultTwo->sum_cont)) ?? 0),
                floatval((max(1, $resultTwo->sum_mont_mov) / $resultTwo->sum_cont) ?? 0),
                floatval(($resultTwo->sum_cont / max(1, $resultTwo->dias_fec)) ?? 0),
                floatval($oynr ?? 0)
            ];

            $consultaFour = "SELECT SUM(case when cod_oper = '050' then debit END) sum_050,
                                    SUM(case when cod_oper = '011' then debit END) AS sum_011,
		                            SUM(case when cod_oper = '050' then debit END)-SUM(case when cod_oper = '011' then debit END) AS reserva_reportada
                             FROM accounting_entries 
                             WHERE cod_oper IN ('050','011') AND debit >0 AND date(created_at) <= '$initDate' ";

            $resultFour = DB::selectOne($consultaFour);

            $ioynr = 0;

            $headingsFour = [
                'Pf',
                'Pe',
                'Pi',
                'Factor IOYNR',
                'Max',
                'Reserva reportada',
                'IOYNR',
                'Provisión total'
            ];

            $activitiesFour[] = [
                0,
                0,
                0,
                0,
                0,
                floatval($resultFour->reserva_reportada ?? 0),
                0,
                floatval($oynr + $ioynr ?? 0)
            ];


            $consultaReserve = "SELECT case when aa.cant > 0 then g.consecutive ELSE g.consecutive_gis END AS siniestro_id,
                                         g.id AS no_caso,
                                         afp.doc_number as cliente_id,
                                         af.doc_number,
                                         af.full_name,
                                         ac.cod_moneda,
                                         date_format(ac.created_at, '%d/%m/%Y') as created_at ,
                                         ac.debit
                                FROM accounting_entries ac
                                LEFT JOIN gis_sort g ON (g.activity_id=ac.activity_gis)
                                LEFT JOIN activities a ON (a.id=ac.activity_gis)
                                LEFT JOIN activities ap ON (ap.id=a.parent_id)
                                LEFT JOIN affiliates afp ON (afp.id=ap.affiliate_id)
                                LEFT JOIN affiliates af ON (af.id=a.affiliate_id)
                                LEFT JOIN (
                                                    SELECT COUNT(*) AS cant , a.activity_id
                                                    FROM activity_actions a
                                                    WHERE a.new_state_id = 160
                                                    GROUP BY a.activity_id
                                            )  aa ON (aa.activity_id=ac.activity_gis)
                                WHERE ac.cod_oper = '050' AND ac.debit >0 AND date(ac.created_at) <= '$initDate' ";

            $resultReserve = DB::select($consultaReserve);

            foreach ($resultReserve as $item) {
                $activitiesReserve[] = [
                    $item->siniestro_id,
                    $item->no_caso,
                    $item->cliente_id,
                    $item->doc_number,
                    $item->full_name,
                    $item->cod_moneda,
                    $item->created_at,
                    'RESERVA INICIAL',
                    $item->debit ?? 0,
                ];
            }

            $headingsReserve = [
                'ID SINIESTRO',
                'NO CASO',
                'ID CLIENTE',
                'ID TRABAJADOR',
                'NOMBRE TRABAJADOR AFECTADO',
                'MONEDA DEL SINIESTRO',
                'FECHA DE MOVIMIENTO',
                'TIPO DE MOVIMIENTO',
                'MONTO MOVIMIENTO MONEDA',
            ];


            $consultaPendiente = "SELECT 
                                    CASE 
                                        WHEN aa.cant > 0 THEN g.consecutive 
                                        ELSE g.consecutive_gis 
                                    END AS siniestro_id,
                                    g.id AS no_caso,
                                    afp.doc_number as cliente_id,
                                    af.doc_number,
                                    af.full_name,
                                    ac.cod_moneda,
                                    date_format(ac.created_at, '%d/%m/%Y') as created_at,
                                    SUM(CASE WHEN ac.cod_oper = '011' THEN ac.debit ELSE 0 END) AS total_011,
                                    SUM(CASE WHEN ac.cod_oper = '050' THEN ac.debit ELSE 0 END) AS total_050,
                                    SUM(CASE WHEN ac.cod_oper = '050' THEN ac.debit ELSE 0 END)-SUM(CASE WHEN ac.cod_oper = '011' THEN ac.debit ELSE 0 END) as pendiente,
                                    GROUP_CONCAT(CASE WHEN ac.cod_oper = '050' THEN ac.receipt_number END SEPARATOR ', ') as receipt_number,
                                    date_format(gs.date_accident, '%d/%m/%Y') AS fec_ocurrencia_siniestro,
                                    date_format(gs.created_at, '%d/%m/%Y') AS fec_noti
                                FROM accounting_entries ac
                                LEFT JOIN gis_sort g ON g.activity_id = ac.activity_gis
                                LEFT JOIN activities a ON a.id = ac.activity_gis
                                LEFT JOIN affiliates af ON af.id = a.affiliate_id
                                LEFT JOIN activities ap ON (ap.id=a.parent_id)
                                LEFT JOIN affiliates afp ON (afp.id=ap.affiliate_id)
                                left join gis_sort gs  on (gs.activity_id=ac.activity_gis)
                                LEFT JOIN (
                                            SELECT COUNT(*) AS cant, a.activity_id
                                            FROM activity_actions a
                                            WHERE a.new_state_id = 160
                                            GROUP BY a.activity_id
                                        ) aa ON aa.activity_id = ac.activity_gis
                                WHERE ac.cod_oper IN ('011', '050') 
                                    AND ac.debit > 0 
                                    AND date(ac.created_at) <= '$initDate'
                                GROUP BY siniestro_id, g.id
                                ORDER BY siniestro_id";

            $resultPendiente = DB::select($consultaPendiente);

            foreach ($resultPendiente as $item) {
                $activitiesPendiente[] = [
                    $item->siniestro_id,
                    $item->no_caso,
                    $item->cliente_id,
                    $item->doc_number,
                    $item->full_name,
                    $item->cod_moneda,
                    $item->created_at,
                    $item->fec_ocurrencia_siniestro,
                    floatval($item->total_011 ?? 0),
                    floatval($item->total_050 ?? 0),
                    floatval($item->pendiente ?? 0),
                    $item->receipt_number,
                    $item->fec_ocurrencia_siniestro
                ];
            }
            $headingsPendiente = [
                'ID SINIESTRO',
                'NO CASO',
                'ID CLIENTE',
                'ID TRABAJADOR AFECTADO',
                'NOMBRE TRABAJADOR AFECTADO',
                'MONEDA DEL SINIESTRO',
                'FECHA DE MOVIMIENTO',
                'FECHA OCURRENCIA',
                'PAGO',
                'RESERVA INICIAL',
                'PENDIENTE',
                'COMPROBANTE CONTABLE',
                'FECHA NOTIFICACIÓN DEL SINIESTRO'
            ];

            //
            $newDate = date('Y-m-d', strtotime('-1 year', strtotime($initDate)));

            $consultaPendienteAno = "SELECT 
                                    CASE 
                                        WHEN aa.cant > 0 THEN g.consecutive 
                                        ELSE g.consecutive_gis 
                                    END AS siniestro_id,
                                    g.id AS no_caso,
                                    afp.doc_number as cliente_id,
                                    af.doc_number,
                                    af.full_name,
                                    ac.cod_moneda,
                                    date_format(ac.created_at, '%d/%m/%Y') as created_at,
                                    SUM(CASE WHEN ac.cod_oper = '011' THEN ac.debit ELSE 0 END) AS total_011,
                                    SUM(CASE WHEN ac.cod_oper = '050' THEN ac.debit ELSE 0 END) AS total_050,
                                    SUM(CASE WHEN ac.cod_oper = '050' THEN ac.debit ELSE 0 END)-SUM(CASE WHEN ac.cod_oper = '011' THEN ac.debit ELSE 0 END) as pendiente,
                                    GROUP_CONCAT(CASE WHEN ac.cod_oper = '050' THEN ac.receipt_number END SEPARATOR ', ') as receipt_number,
                                    date_format(gs.date_accident, '%d/%m/%Y') AS fec_ocurrencia_siniestro,
                                    date_format(gs.created_at, '%d/%m/%Y') AS fec_noti
                                FROM accounting_entries ac
                                LEFT JOIN gis_sort g ON g.activity_id = ac.activity_gis
                                LEFT JOIN activities a ON a.id = ac.activity_gis
                                LEFT JOIN affiliates af ON af.id = a.affiliate_id
                                LEFT JOIN activities ap ON (ap.id=a.parent_id)
                                LEFT JOIN affiliates afp ON (afp.id=ap.affiliate_id)
                                left join gis_sort gs  on (gs.activity_id=ac.activity_gis)
                                LEFT JOIN (
                                            SELECT COUNT(*) AS cant, a.activity_id
                                            FROM activity_actions a
                                            WHERE a.new_state_id = 160
                                            GROUP BY a.activity_id
                                        ) aa ON aa.activity_id = ac.activity_gis
                                WHERE ac.cod_oper IN ('011', '050') 
                                    AND ac.debit > 0 
                                    AND date(ac.created_at) <= '$newDate'
                                GROUP BY siniestro_id, g.id
                                ORDER BY siniestro_id";

            $resultPendienteAno = DB::select($consultaPendienteAno);

            foreach ($resultPendienteAno as $item) {
                $activitiesPendienteAno[] = [
                    $item->siniestro_id,
                    $item->no_caso,
                    $item->cliente_id,
                    $item->doc_number,
                    $item->full_name,
                    $item->cod_moneda,
                    $item->created_at,
                    $item->fec_ocurrencia_siniestro,
                    floatval($item->total_011 ?? 0),
                    floatval($item->total_050 ?? 0),
                    floatval($item->pendiente ?? 0),
                    $item->receipt_number,
                    $item->fec_ocurrencia_siniestro
                ];
            }
            $headingsPendienteAno = [
                'ID SINIESTRO',
                'NO CASO',
                'ID CLIENTE',
                'ID TRABAJADOR AFECTADO',
                'NOMBRE TRABAJADOR AFECTADO',
                'MONEDA DEL SINIESTRO',
                'FECHA DE MOVIMIENTO',
                'FECHA OCURRENCIA',
                'PAGO',
                'RESERVA INICIAL',
                'PENDIENTE',
                'COMPROBANTE CONTABLE',
                'FECHA NOTIFICACIÓN DEL SINIESTRO'

            ];

            return Excel::create('reporteOynr', function ($excel) use (
                $activities, $headings, $activitiesTwo, $headingsTwo, $activitiesThree, $headingsThree, $activitiesFour, $headingsFour,
                $activitiesReserve, $headingsReserve, $activitiesPendiente, $headingsPendiente, $activitiesPendienteAno, $headingsPendienteAno
            ) {

                $excel->sheet('Pagado', function ($sheet) use ($activities, $headings) {
                    $sheet->row(1, $headings);
                    $sheet->fromArray($activities, null, 'A2', false, false);

                    $rowIndex = 2;
                    foreach ($activities as $activity) {
                        // Fuerza los valores 0 como flotantes
                        $activity[9] = (float) $activity[9]; // monto_mov_moneda
                        $activity[10] = (float) $activity[10]; // monto_mov_dolar

                        $sheet->row($rowIndex++, $activity);
                    }


                    // Calcular la última fila
                    $lastRow = count($activities) + 1;

                    $sheet->setColumnFormat([

                        // Formatos para fechas
                        'H2:H'.$lastRow => 'dd/mm/yyyy',
                        'M2:M'.$lastRow => 'dd/mm/yyyy',
                        'N2:N'.$lastRow => 'dd/mm/yyyy',

                        'J2:J'.$lastRow => '0.00',
                        'K2:K'.$lastRow => '0.00',
                    ]);

                    //alinear a la derecha
                    $dateColumns = ['H','M','N','J','K'];
                    foreach ($dateColumns as $col) {
                        $sheet->cells($col.'2:'.$col.$lastRow, function($cells) {
                            $cells->setAlignment('right');
                        });
                    }

                });

                $excel->sheet('Resumen', function ($sheet) use ($activitiesTwo, $headingsTwo) {
                    $sheet->row(1, $headingsTwo);
                    $sheet->fromArray($activitiesTwo, null, 'A2', false, false);

                    $rowIndex = 2;
                    foreach ($activitiesTwo as $activity) {
                        // Fuerza los valores 0 como flotantes
                        $activity[1] = (float) $activity[1];
                        $activity[2] = (float) $activity[2];

                        $sheet->row($rowIndex++, $activity);
                    }


                    // Calcular la última fila
                    $lastRow = count($activitiesTwo) + 1;

                    $sheet->setColumnFormat([

                        // Formatos para fechas
                        'D2:D'.$lastRow => 'dd/mm/yyyy',

                        // Formatos para valores numéricos con decimales
                        //'A2:A'.$lastRow => '0.00',
                        'B2:B'.$lastRow => '0.00',
                        'C2:C'.$lastRow => '0.00',
                    ]);

                    //alinear a la derecha
                    $dateColumns = ['A', 'B', 'C', 'D'];
                    foreach ($dateColumns as $col) {
                        $sheet->cells($col.'2:'.$col.$lastRow, function($cells) {
                            $cells->setAlignment('right');
                        });
                    }


                });

                $excel->sheet('oynr', function ($sheet) use ($activitiesThree, $headingsThree) {
                    $sheet->row(1, $headingsThree);
                    $sheet->fromArray($activitiesThree, null, 'A2', false, false);

                    $rowIndex = 2;
                    foreach ($activitiesThree as $activity) {
                        // Fuerza los valores 0 como flotantes
                        $activity[0] = (float) $activity[0];
                        $activity[1] = (float) $activity[1];
                        $activity[2] = (float) $activity[2];
                        $activity[3] = (float) $activity[3];

                        $sheet->row($rowIndex++, $activity);
                    }

                    // Calcular la última fila
                    $lastRow = count($activitiesThree) + 1;

                    $sheet->setColumnFormat([

                        // Formatos para valores numéricos con decimales
                        'A2:A'.$lastRow => '0.00',
                        'B2:B'.$lastRow => '0.00',
                        'C2:C'.$lastRow => '0.00',
                        'D2:D'.$lastRow => '0.00',
                    ]);


                    //alinear a la derecha
                    $dateColumns = ['A', 'B', 'C', 'D'];
                    foreach ($dateColumns as $col) {
                        $sheet->cells($col.'2:'.$col.$lastRow, function($cells) {
                            $cells->setAlignment('right');
                        });
                    }

                });
                $excel->sheet('ioynr', function ($sheet) use ($activitiesFour, $headingsFour) {
                    $sheet->row(1, $headingsFour);
                    $sheet->fromArray($activitiesFour, null, 'A2', false, false);

                    $rowIndex = 2;
                    foreach ($activitiesFour as $activity) {
                        // Fuerza los valores 0 como flotantes
                        $activity[0] = (float) $activity[0];
                        $activity[1] = (float) $activity[1];
                        $activity[2] = (float) $activity[2];
                        $activity[3] = (float) $activity[3];
                        $activity[4] = (float) $activity[4];
                        $activity[5] = (float) $activity[5];
                        $activity[6] = (float) $activity[6];
                        $activity[7] = (float) $activity[7];

                        $sheet->row($rowIndex++, $activity);
                    }

                    // Calcular la última fila
                    $lastRow = count($activitiesFour) + 1;

                    $sheet->setColumnFormat([

                        // Formatos para valores numéricos con decimales
                        'A2:A'.$lastRow => '0.00',
                        'B2:B'.$lastRow => '0.00',
                        'C2:C'.$lastRow => '0.00',
                        'D2:D'.$lastRow => '0.00',
                        'E2:E'.$lastRow => '0.00',
                        'F2:F'.$lastRow => '0.00',
                        'G2:G'.$lastRow => '0.00',
                        'H2:H'.$lastRow => '0.00',
                    ]);

                    //alinear a la derecha
//                    $dateColumns = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H'];
//                    foreach ($dateColumns as $col) {
//                        $sheet->cells($col.'2:'.$col.$lastRow, function($cells) {
//                            $cells->setAlignment('right');
//                        });
//                    }

                });

//                $excel->sheet('Reservas', function ($sheet) use ($activitiesReserve, $headingsReserve) {
//                    $sheet->row(1, $headingsReserve);
//                    $sheet->fromArray($activitiesReserve, null, 'A2', false, false);
//                });

                $excel->sheet('Pendiente', function ($sheet) use ($activitiesPendiente, $headingsPendiente) {
                    $sheet->row(1, $headingsPendiente);
                    $sheet->fromArray($activitiesPendiente, null, 'A2', false, false);

                    $rowIndex = 2;
                    foreach ($activitiesPendiente as $activity) {
                        // Forzar los valores monetarios a float para asegurar que 0 se escriba correctamente
                        $activity[8] = (float) $activity[8]; // I columna
                        $activity[9] = (float) $activity[9]; // J columna
                        $activity[10] = (float) $activity[10]; // K columna

                        $sheet->row($rowIndex++, $activity);
                    }

                    // Calcular la última fila
                    $lastRow = count($activitiesPendiente) + 1;

                    $sheet->setColumnFormat([

                        'G2:G'.$lastRow => 'dd/mm/yyyy',
                        'H2:H'.$lastRow => 'dd/mm/yyyy',
                        'M2:M'.$lastRow => 'dd/mm/yyyy',

                        'I2:I'.$lastRow => '0.00',
                        'J2:J'.$lastRow => '0.00',
                        'K2:K'.$lastRow => '0.00',
                    ]);


                    //alinear a la derecha
                    $dateColumns = ['G', 'H', 'M', 'I', 'J', 'K'];
                    foreach ($dateColumns as $col) {
                        $sheet->cells($col.'2:'.$col.$lastRow, function($cells) {
                            $cells->setAlignment('right');
                        });
                    }
                });

                $excel->sheet('Pendiente 1 año atrás', function ($sheet) use ($activitiesPendienteAno, $headingsPendienteAno) {
                    $sheet->row(1, $headingsPendienteAno);
                    $sheet->fromArray($activitiesPendienteAno, null, 'A2', false, false);

                    $rowIndex = 2;
                    foreach ($activitiesPendienteAno as $activity) {
                        // Asegurar que la columna K (índice 10) tenga formato numérico válido
                        $activity[10] = (float) $activity[10]; // K columna

                        $sheet->row($rowIndex++, $activity);
                    }

                    // Calcular la última fila
                    $lastRow = count($activitiesPendienteAno) + 1;

                    $sheet->setColumnFormat([

                        'G2:G'.$lastRow => 'dd/mm/yyyy',
                        'H2:H'.$lastRow => 'dd/mm/yyyy',
                        'K2:K'.$lastRow => '0.00',
                    ]);

                    //alinear a la derecha
                    $dateColumns = ['G', 'H', 'K'];
                    foreach ($dateColumns as $col) {
                        $sheet->cells($col.'2:'.$col.$lastRow, function($cells) {
                            $cells->setAlignment('right');
                        });
                    }

                });

            })->download('xlsx');

        } catch (\Exception $e) {
            return redirect('/admin/reportes/descargas_admin')
                ->withErrors($e->getMessage())
                ->withInput();
        }
    }


    public function acselAccountingEntriesView (Request $req, $cpath)
    {
        try {

            $initDate = $req->acsel_start_date_submit;
            $lastDate = $req->acsel_end_date_submit;

            if (empty($initDate) || empty($lastDate)) {
                throw new \Exception("Las fechas no pueden estar vacías, en la sección 'Reporte asientos acsel'");
            }

            if (strtotime($lastDate) < strtotime($initDate)) {
                throw new \Exception("La fecha desde no puede ser menor a la fecha hasta, en la sección 'Reporte asientos acsel'");
            }

            $activities = [];

            $query = DB::table('acsel_accounting_entries_view')
                ->whereBetween('FECMOV', [$initDate . ' 00:00:00', $lastDate . ' 23:59:59']);

            $query->orderBy('FECMOV', 'ASC')
                ->chunk(1000, function ($items) use (&$activities) {
                    foreach ($items as $item) {
                        $activities[] = [
                            $item->ID,
                            $item->COD_CIA,
                            $item->COD_OPER,
                            $item->NUMERO_COMPROBANTE,
                            $item->ESTATUS_DE_COMPROBANTE,
                            $item->NO_POLIZA,
                            $item->NO_RECIBO,
                            $item->TIPO_COMPROBANTE,
                            $item->FECMOV,
                            $item->COD_INTERMEDIARY,
                            $item->COD_MONEDA,
                            $item->COD_RAMO,
                            $item->CTA_1,
                            $item->CTA_2,
                            $item->CTA_3,
                            $item->CTA_4,
                            $item->CTA_5,
                            $item->CTA_6,
                            $item->CTA_7,
                            $item->CTA_8,
                            $item->CTA_9,
                            $item->CTA_10,
                            number_format($item->CREDITO ?? 0 , 2, ',', ''),
                            number_format($item->MONTOMOV_CREDITO ?? 0 , 2, ',', ''),
                            number_format($item->DEBITO ?? 0 , 2, ',', ''),
                            number_format($item->MONTOMOV_DEBITO ?? 0 , 2, ',', ''),
                            $item->DETALLE_MOVIMIENTO,
                            number_format($item->MTODIFERENCIA ?? 0 , 2, ',', ''),
                            $item->TIPO_MOVIMIENTO,
                            $item->TIPOMOVORG,
                            number_format($item->T_CREDITO ?? 0 , 2, ',', ''),
                            number_format($item->T_DEBITO ?? 0 , 2, ',', ''),
                            $item->NUMDOC,
                            $item->TIPO_DE_DOCUMENTO,
                            number_format($item->TASACAMBIO ?? 0 , 2, ',', ''),
                            $item->IDECTA,
                            $item->NAME_TAKER,
                            $item->DESCOPER,
                            $item->COD_CPTO,
                            $item->COD_GRUPO_CPTO,
                            $item->COR_RELATIVO,
                            $item->ENTRY_CODE,
                            $item->CTAAUX,
                            $item->RESULTADO,
                        ];
                    }
                });

            $headings = [
                'ID',
                'COD_CIA',
                'COD_OPER',
                'NUMERO_COMPROBANTE',
                'ESTATUS_DE_COMPROBANTE',
                'NO_POLIZA',
                'NO_RECIBO',
                'TIPO_COMPROBANTE',
                'FECMOV',
                'COD_INTERMEDIARY',
                'COD_MONEDA',
                'COD_RAMO',
                'CTA_1',
                'CTA_2',
                'CTA_3',
                'CTA_4',
                'CTA_5',
                'CTA_6',
                'CTA_7',
                'CTA_8',
                'CTA_9',
                'CTA_10',
                'CREDITO',
                'MONTOMOV_CREDITO',
                'DEBITO',
                'MONTOMOV_DEBITO',
                'DETALLE_MOVIMIENTO',
                'MTODIFERENCIA',
                'TIPO_MOVIMIENTO',
                'TIPOMOVORG',
                'T_CREDITO',
                'T_DEBITO',
                'NUMDOC',
                'TIPO_DE_DOCUMENTO',
                'TASACAMBIO',
                'IDECTA',
                'NAME_TAKER',
                'DESCOPER',
                'COD_CPTO',
                'COD_GRUPO_CPTO',
                'COR_RELATIVO',
                'ENTRY_CODE',
                'CTAAUX',
                'RESULTADO',
            ];

            return Excel::create('reporteAsientosAcsel', function ($excel) use ($activities, $headings) {
                $excel->sheet('Entries', function ($sheet) use ($activities, $headings) {
                    $sheet->row(1, $headings);
                    $sheet->fromArray($activities, null, 'A2', false, false);
                });
            })->download('xlsx');

        }catch (\Exception $e) {
            return redirect('/admin/reportes/descargas_admin')
                ->withErrors($e->getMessage())
                ->withInput();
        }
    }
    public function acselPolicyView (Request $req, $cpath)
    {
        try {

            $initDate = $req->acsel_policy_start_date_submit;
            $lastDate = $req->acsel_policy_end_date_submit;

            if (empty($initDate) || empty($lastDate)) {
                throw new \Exception("Las fechas no pueden estar vacías, en la sección 'Reporte pólizas acsel'");
            }

            if (strtotime($lastDate) < strtotime($initDate)) {
                throw new \Exception("La fecha desde no puede ser menor a la fecha hasta, en la sección 'Reporte pólizas acsel'");
            }

            $activities = [];

            $consulta = "   SELECT *
                            FROM acsel_policy_view p
                            where DATE_FORMAT(STR_TO_DATE(FECEMISION, '%d/%m/%Y'), '%Y-%m-%d') BETWEEN  '$initDate' and '$lastDate' ";

            $query = DB::select($consulta);

            foreach ($query as $item) {
                $activities[] = [
                    $item->NUMPOLRENAPP,
                    $item->CODRAMO,
                    $item->NUMREN,
                    $item->FECEMISION,
                    $item->FECINIVIG,
                    $item->FECFINVIG,
                    $item->CODMONEDA,
                    $item->TASACAMBIO,
                    $item->STSPOL,
                    $item->CODINTER,
                    $item->CODCORREDOR,
                    $item->TIPOIDENTASEG,
                    $item->IDENTASEG,
                    $item->NOMASEG,
                    $item->APEASEG,
                    $item->NUMREC,
                    $item->NUMCERT,
                    number_format($item->MTOPRIMA ?? 0 , 2, ',', ''),
                    number_format($item->MTOCOMISION ?? 0 , 2, ',', ''),
                    number_format($item->MTOMONEDA ?? 0 , 2, ',', ''),
                    number_format($item->SUMASEGMONEDA ?? 0 , 2, ',', ''),
                    $item->PORCCOM,
                    $item->RESULTADO,
                ];
            }

            $headings = [
                'NUMPOLRENAPP',
                'CODRAMO',
                'NUMREN',
                'FECEMISION',
                'FECINIVIG',
                'FECFINVIG',
                'CODMONEDA',
                'TASACAMBIO',
                'STSPOL',
                'CODINTER',
                'CODCORREDOR',
                'TIPOIDENTASEG',
                'IDENTASEG',
                'NOMASEG',
                'APEASEG',
                'NUMREC',
                'NUMCERT',
                'MTOPRIMA',
                'MTOCOMISION',
                'MTOMONEDA',
                'SUMASEGMONEDA',
                'PORCCOM',
                'RESULTADO',
            ];

            return Excel::create('reportePolizaAcsel', function ($excel) use ($activities, $headings) {
                $excel->sheet('Entries', function ($sheet) use ($activities, $headings) {
                    $sheet->row(1, $headings);
                    $sheet->fromArray($activities, null, 'A2', false, false);
                });
            })->download('xlsx');

        }catch (\Exception $e) {
            return redirect('/admin/reportes/descargas_admin')
                ->withErrors($e->getMessage())
                ->withInput();
        }
    }

    public function reportAccountIban (Request $req, $cpath)
    {
        try {

            $initDate = $req->iban_start_date_submit;
            $lastDate = $req->iban_end_date_submit;

            if (empty($initDate) || empty($lastDate)) {
                throw new \Exception("Las fechas no pueden estar vacías, en la sección 'Reporte de cuentas IBAN'");
            }

            if (strtotime($lastDate) < strtotime($initDate)) {
                throw new \Exception("La fecha desde no puede ser menor a la fecha hasta, en la sección 'Reporte de cuentas IBAN'");
            }

            $activities = [];

            $consulta = "  SELECT af.doc_number, af.full_name, p.name_of_the_bank, p.iban_account_number
                            FROM pe_it_sorts p 
                            LEFT JOIN activities a ON (a.id=p.activity_id)
                            LEFT JOIN affiliates af ON (af.id=a.affiliate_id)
                            WHERE a.service_id = 84 AND DATE(a.created_at) BETWEEN '$initDate' and '$lastDate' 
                            GROUP BY af.doc_number, af.full_name";

            $query = DB::select($consulta);

            foreach ($query as $item) {
                $activities[] = [
                    $item->doc_number,
                    ucwords(mb_strtolower($item->full_name ?? '')),
                    $item->iban_account_number,
                    $item->name_of_the_bank
                ];
            }

            $headings = [
                'ID AFILIADO',
                'NOMBRE AFILIADO',
                'CUENTA IBAN',
                'BANCO'
            ];

            return Excel::create('reporteCuentasIban', function ($excel) use ($activities, $headings) {
                $excel->sheet('Entries', function ($sheet) use ($activities, $headings) {
                    $sheet->row(1, $headings);
                    $sheet->fromArray($activities, null, 'A2', false, false);
                });
            })->download('xlsx');

        }catch (\Exception $e) {
            return redirect('/admin/reportes/descargas_admin')
                ->withErrors($e->getMessage())
                ->withInput();
        }
    }

    //Reportes automatico de afiliados
    public function affiliateReport(Request $request)
    {
        $period = $request->affiliate_start_date_submit;
        if (empty($period)) {
            throw new \Exception("El periodo en el reporte de afiliados no puede estar vacio");
        }

        try {
            $activities = [];

            $consulta = "WITH activity_75 AS (
            SELECT
                a.id AS activity_id,
                a.parent_id,
                a.affiliate_id,
                ps.consecutive,
                ps.advisor_name,
                ps.id AS policysort_id
            FROM activities a
                     LEFT JOIN policy_sorts ps ON a.id = ps.activity_id
            WHERE a.service_id = 75
              AND a.state_id = 20
              AND ps.validity_from <= LAST_DAY(STR_TO_DATE('$period', '%Y-%m-%d'))
        ),
             activity_79 AS (
                 SELECT
                     a.id AS activity_79_id,
                     a.parent_id
                 FROM activities a
                 WHERE a.service_id = 79
                   AND deleted_at IS NULL
                   AND DATE(a.created_at) BETWEEN DATE_FORMAT(STR_TO_DATE('$period', '%Y-%m-%d'), '%Y-%m-01') AND DATE(STR_TO_DATE('$period', '%Y-%m-%d'))
                   AND a.parent_id IN (SELECT activity_id FROM activity_75)
             ),
             latest_activity_79 AS (
                SELECT 
                a75.activity_id AS parent_id,
                COALESCE(
                    (SELECT MAX(a79.activity_79_id) FROM activity_79 a79 WHERE a79.parent_id = a75.activity_id),
                    (SELECT MAX(a_prev.id) FROM activities a_prev 
                     WHERE a_prev.service_id = 79 
                       AND a_prev.deleted_at IS NULL 
                       AND a_prev.parent_id = a75.activity_id 
                       AND DATE(a_prev.created_at) < DATE_FORMAT(STR_TO_DATE('$period', '%Y-%m-%d'), '%Y-%m-01'))
                ) AS latest_activity_79_id
                    FROM activity_75 a75
            )
            SELECT
            act75.activity_id AS id_actividad_75,
            la79.latest_activity_79_id AS id_actividad_79,
            act75.consecutive AS consecutivo,
            af.full_name AS nombre_completo,
            af.employer_address AS direccion_empleador,
            pv.name AS nombre_provincia,
            cn.name AS nombre_canton,
            ds.name AS nombre_distrito,
            COALESCE(COUNT(psa.id), 0) AS cantidad_afiliados,
            act75.policysort_id,
            EXISTS (SELECT * FROM policy_addresses pa WHERE pa.policy_sort_id = act75.policysort_id) AS has_policyaddresses
        FROM activity_75 act75
                 LEFT JOIN latest_activity_79 la79 ON la79.parent_id = act75.activity_id
                 LEFT JOIN policy_spreadsheets pss ON pss.activity_id = la79.latest_activity_79_id
                 LEFT JOIN policy_spreadsheet_affiliates psa ON psa.policy_spreadsheet_id = pss.id AND DATE(psa.created_at) <= DATE(STR_TO_DATE('$period', '%Y-%m-%d'))
                 LEFT JOIN affiliates af ON af.id = act75.affiliate_id
                 LEFT JOIN provinces pv ON pv.id = af.province
                 LEFT JOIN cantons cn ON cn.province_id = pv.id AND cn.id = af.canton
                 LEFT JOIN districts ds ON ds.province_id = pv.id AND ds.canton_id = cn.id AND ds.id = af.district
        GROUP BY act75.activity_id, la79.latest_activity_79_id, act75.consecutive, act75.advisor_name, af.full_name, af.employer_address, pv.name, cn.name, ds.name;
                            ";

            // Ejecutar la consulta
            $query = DB::select($consulta);

            // Inicializamos el arreglo de actividades con un primer valor vacío
            $activities = [];
            $rowCount = 0; // Inicializamos el contador de filas
            $totalAfiliados = 0; // Inicializamos el total de afiliados


            // Convertimos los resultados a un array con las columnas correctas
            foreach ($query as $item) {
                $activities[] = [
                    $item->consecutivo,
                    $item->nombre_completo,
                    $item->nombre_provincia,
                    $item->nombre_canton,
                    $item->nombre_distrito,
                    $item->direccion_empleador,
                    strval($item->cantidad_afiliados)
                ];
                $rowCount++;
                // Sumar el número de afiliados (sólo si la fila no está vacía)
                $totalAfiliados += (int)$item->cantidad_afiliados;

                // Si la póliza tiene direcciones adicionales, las agregamos justo debajo de la póliza
                if (!empty($item->has_policyaddresses)) {
                    $queryAddresses = DB::select("
                        SELECT 
                            ps.full_address,
                            c.name_provincia,
                            c.name_canton,
                            c.name_distrito
                        FROM 
                            policy_addresses ps 
                            LEFT JOIN costarica c ON (
                            c.cod_provincia = ps.province_id COLLATE utf8mb4_unicode_ci AND 
                            c.cod_canton = ps.canton_id COLLATE utf8mb4_unicode_ci AND 
                            c.cod_distrito = ps.district_id COLLATE utf8mb4_unicode_ci
                        )
                        WHERE 
                            ps.policy_sort_id = :policySortId
                        GROUP BY ps.id
                    ", [
                        'policySortId' => $item->policysort_id,
                    ]);

                    foreach ($queryAddresses as $address) {
                        $activities[] = [
                            '',
                            '',
                            $address->name_provincia ?? '',
                            $address->name_canton ?? '',
                            $address->name_distrito ?? '',
                            $address->full_address ?? '',
                            '',
                        ];
                    }
                }
            }

            // Agregar la fila total si hay más de 1 fila de datos
            if ($rowCount > 0) {
                // Agregar una fila de totales al final
                $activities[] = [
                    'TOTAL',
                    '',
                    '',
                    '',
                    '',
                    '',
                    $totalAfiliados
                ];
            }

            // Títulos de las columnas
            $headings = [
                'CONSECUTIVO',
                'NOMBRE PATRONO',
                'PROVINCIA',
                'CANTON',
                'DISTRITO',
                'DIRECCION',
                'NUMERO DE AFILIADOS'
            ];
            $filename = 'reporte-afiliados-' . $period;
            Excel::create($filename, function ($excel) use ($activities, $headings) {
                $excel->sheet('Entries', function ($sheet) use ($activities, $headings) {
                    $sheet->row(1, $headings);
                    $sheet->fromArray($activities, null, 'A2', false, false);
                });
            })->download('xlsx');

        } catch (\Exception $e) {
            return redirect('/admin/reportes/descargas_admin')
                ->withErrors($e->getMessage())
                ->withInput();
        }
    }

    //ME-1157 - Requerimiento 40 parte 1: Generación de modelos XML 13 SUGESE
    public function reportXmlOne(Request $req)
    {
        try {

            $xmlYear = $req->xml_one_year;
            $periodo = $req->periodo ?? date('Y-m-d'); // Período a reportar (opcional)

            // Validaciones
            if (empty($xmlYear)) {
                throw new \Exception("El campo año no puede estar vacío, en la sección 'MODELO XML 13'");
            }

            // Generar el XML
            $xml = new \SimpleXMLElement('<?xml version="1.0" encoding="utf-8"?>
            <ModeloAccidentesLaboralesPorRamaDeActividadRT 
                xmlns:xsd="http://www.w3.org/2001/XMLSchema" 
                xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            </ModeloAccidentesLaboralesPorRamaDeActividadRT>');

            // Agregar encabezado
            $encabezado = $xml->addChild('Encabezado');
            $encabezado->addChild('NombreArchivo', 'ModeloAccidentesLaboralesPorRamaDeActividadRT');
            $encabezado->addChild('Fecha', date('Y-m-d'));
            $encabezado->addChild('Periodo', $xmlYear.'-12-31');
            $encabezado->addChild('Periodicidad', 'A');
            $encabezado->addChild('Moneda', '1');

            // Agregar datos
            $datos = $xml->addChild('Datos');
            $modelo = $datos->addChild('Modelo');
            $modelo->addAttribute('EntidadFuente', 'A13');

            $accidentesLaboralesEnRamaDeActividad = $modelo->addChild('AccidentesLaboralesEnRamaDeActividad');
            $totalesRamasDeActividades = $accidentesLaboralesEnRamaDeActividad->addChild('TotalesRamasDeActividades');


             $query = "  SELECT grupo_ocupacion, SUM(total) AS total, SUM(total_hombres) AS total_hombres, SUM(total_mujeres) AS total_mujeres, ano, type,id
                         FROM report_xml_one
                         WHERE ano = '$xmlYear'
                         GROUP by grupo_ocupacion, ano, type,id
                         ORDER BY id, grupo_ocupacion ";

            $data = DB::select($query);

            $totalesGruposOcupacionales = $totalesRamasDeActividades->addChild('TotalesGruposOcupacionales');
            $totalesFormasDelAccidente = $totalesRamasDeActividades->addChild('TotalesFormasDelAccidente');
            $totalesNaturalezasDeLaLesion = $totalesRamasDeActividades->addChild('TotalesNaturalezasDeLaLesion'); // pendiente
            $totalesUbicacionDeLaLesion = $totalesRamasDeActividades->addChild('TotalesUbicacionDeLaLesion'); //validar
            $totalesAgentesMaterialesInvolucrados = $totalesRamasDeActividades->addChild('TotalesAgentesMaterialesInvolucrados'); //pendiente
            $totalesTiposDeIncapacidades = $totalesRamasDeActividades->addChild('TotalesTiposDeIncapacidades');
            $totalesLugaresDelAccidente = $totalesRamasDeActividades->addChild('TotalesLugaresDelAccidente');

            $TotalEnRamasDeActividades = 0;
            $TotalEnRamasDeActividadesHombres = 0;
            $TotalEnRamasDeActividadesMujeres = 0;

            foreach ($data as $row) {

                if ($row->type == 'GruposOcupacionales') {
                    $totalGrupoOcupacional = $totalesGruposOcupacionales->addChild('TotalGrupoOcupacional');
                    $totalGrupoOcupacional->addAttribute('CodGrupoOcupacional', $row->grupo_ocupacion);
                    $totalGrupoOcupacional->addAttribute('TotalPorGrupoOcupacional', intval($row->total));

                    $totalGrupoOcupacional->addChild('TotalPorGrupoOcupacionalHombres', intval($row->total_hombres));
                    $totalGrupoOcupacional->addChild('TotalPorGrupoOcupacionalMujeres', intval($row->total_mujeres));
                } else if ($row->type == 'FormasDelosAccidentes') {
                    $totalFormaDelAccidente = $totalesFormasDelAccidente->addChild('TotalFormaDelAccidente');
                    $totalFormaDelAccidente->addAttribute('CodFormaDelAccidente', $row->grupo_ocupacion);
                    $totalFormaDelAccidente->addAttribute('TotalPorFormaDelAccidente', intval($row->total));

                    $totalFormaDelAccidente->addChild('TotalPorFormaDelAccidenteHombres', intval($row->total_hombres));
                    $totalFormaDelAccidente->addChild('TotalPorFormaDelAccidenteMujeres', intval($row->total_mujeres));
                } else if ($row->type == 'NaturalezasDeLasLesiones') {
                    $totalNaturalezaDeLaLesion = $totalesNaturalezasDeLaLesion->addChild('TotalNaturalezaDeLaLesion');
                    $totalNaturalezaDeLaLesion->addAttribute('CodNaturalezaDeLaLesion', $row->grupo_ocupacion);
                    $totalNaturalezaDeLaLesion->addAttribute('TotalPorNaturalezaDeLaLesion', intval($row->total));

                    $totalNaturalezaDeLaLesion->addChild('TotalPorNaturalezaDeLaLesionHombres', intval($row->total_hombres));
                    $totalNaturalezaDeLaLesion->addChild('TotalPorNaturalezaDeLaLesionMujeres', intval($row->total_mujeres));
                } else if ($row->type == 'UbicacionesDeLasLesiones') {
                    $totalUbicacionDeLaLesion = $totalesUbicacionDeLaLesion->addChild('TotalUbicacionDeLaLesion');
                    $totalUbicacionDeLaLesion->addAttribute('CodUbicacionDeLaLesion', $row->grupo_ocupacion);
                    $totalUbicacionDeLaLesion->addAttribute('TotalPorUbicacionDeLaLesion', intval($row->total));

                    $totalUbicacionDeLaLesion->addChild('TotalPorUbicacionDeLaLesionHombres', intval($row->total_hombres));
                    $totalUbicacionDeLaLesion->addChild('TotalPorUbicacionDeLaLesionMujeres', intval($row->total_mujeres));
                } else if ($row->type == 'AgentesMaterialesInvolucrados') {
                    $totalAgenteMaterialInvolucrado  = $totalesAgentesMaterialesInvolucrados->addChild('TotalAgenteMaterialInvolucrado');
                    $totalAgenteMaterialInvolucrado->addAttribute('CodAgenteMaterialInvolucrado', $row->grupo_ocupacion);
                    $totalAgenteMaterialInvolucrado->addAttribute('TotalPorAgenteMaterialInvolucrado', intval($row->total));

                    $totalAgenteMaterialInvolucrado->addChild('TotalPorAgenteMaterialInvolucradoHombres', intval($row->total_hombres));
                    $totalAgenteMaterialInvolucrado->addChild('TotalPorAgenteMaterialInvolucradoMujeres', intval($row->total_mujeres));
                } else if ($row->type == 'TiposDeIncapacidades') {
                    $totalTipoDeIncapacidad = $totalesTiposDeIncapacidades->addChild('TotalTipoDeIncapacidad');
                    $totalTipoDeIncapacidad->addAttribute('CodTipoDeIncapacidad', $row->grupo_ocupacion);
                    $totalTipoDeIncapacidad->addAttribute('TotalPorTipoDeIncapacidad', intval($row->total));

                    $totalTipoDeIncapacidad->addChild('TotalPorTipoDeIncapacidadHombres', intval($row->total_hombres));
                    $totalTipoDeIncapacidad->addChild('TotalPorTipoDeIncapacidadMujeres', intval($row->total_mujeres));
                } else if ($row->type == 'LugaresDelosAccidentes') {
                    $totalLugarDelAccidente = $totalesLugaresDelAccidente->addChild('TotalLugarDelAccidente');
                    $totalLugarDelAccidente->addAttribute('CodLugarDelAccidente', $row->grupo_ocupacion);
                    $totalLugarDelAccidente->addAttribute('TotalPorLugarDelAccidente', intval($row->total));

                    $totalLugarDelAccidente->addChild('TotalPorLugarDelAccidenteHombres', intval($row->total_hombres));
                    $totalLugarDelAccidente->addChild('TotalPorLugarDelAccidenteMujeres', intval($row->total_mujeres));
                }

                $TotalEnRamasDeActividades += $row->total;
                $TotalEnRamasDeActividadesHombres += $row->total_hombres;
                $TotalEnRamasDeActividadesMujeres += $row->total_mujeres;

            }

            $query = "  SELECT
                              SUM(CASE WHEN psa.gender IN ('F','M') THEN 1 ELSE 0 END) AS total,
                              SUM(CASE WHEN psa.gender = 'M' THEN 1 ELSE 0 END) AS total_hombres,
                              SUM(CASE WHEN psa.gender = 'F' THEN 1 ELSE 0 END) AS total_mujeres
                        FROM gis_sort g
                        LEFT JOIN activities a ON a.id=g.activity_id
                        LEFT JOIN activities ap ON ap.id=a.parent_id
                        LEFT JOIN (
                                    SELECT p.identification_number, p.gender, p.hours, ROW_NUMBER() OVER (PARTITION BY identification_number, gender ORDER BY id DESC) AS rn
                                    FROM policy_spreadsheet_affiliates p
                                  ) psa ON psa.identification_number = g.number_identification_affiliate AND psa.rn = 1
                        WHERE a.deleted_at IS NULL AND g.type_report = 'Accidente' AND YEAR(g.created_at) = '$xmlYear'
                        ";

            $resultGis = DB::selectOne($query);

            $TotalEnRamasDeActividades = intval($resultGis->total);
            $TotalEnRamasDeActividadesHombres = intval($resultGis->total_hombres);
            $TotalEnRamasDeActividadesMujeres = intval($resultGis->total_mujeres);

            $totalesRamasDeActividades->addAttribute('TotalEnRamasDeActividades', intval($TotalEnRamasDeActividades)); // intval($resultGis->total)
            $totalesRamasDeActividades->addAttribute('TotalEnRamasDeActividadesHombres', intval($TotalEnRamasDeActividadesHombres)); // intval($resultGis->total_hombres)
            $totalesRamasDeActividades->addAttribute('TotalEnRamasDeActividadesMujeres', intval($TotalEnRamasDeActividadesMujeres)); // intval($resultGis->total_mujeres)

            $ramasDeActividades = $accidentesLaboralesEnRamaDeActividad->addChild('RamasDeActividades');

            $data = $this->queryXmlOne($xmlYear);

            $codRamaDeActividad = '';

            $ramaDeActividad = $ramasDeActividades->addChild('RamaDeActividad');

            $query = "SELECT ea.group_economic AS grupo_actividad,
                                          g.mechanism_trauma AS grupo_ocupacion,                           
                                          SUM(CASE WHEN psa.gender IN ('F','M') THEN 1 ELSE 0 END) AS total,
                                          SUM(CASE WHEN psa.gender = 'M' THEN 1 ELSE 0 END) AS total_hombres,
                                          SUM(CASE WHEN psa.gender = 'F' THEN 1 ELSE 0 END) AS total_mujeres,
                                          YEAR(g.created_at) AS ano
                                    FROM gis_sort g 
                                    LEFT JOIN activities a ON a.id=g.activity_id
                                    LEFT JOIN activities ap ON ap.id=a.parent_id
                                    LEFT JOIN policy_sorts ps ON ps.activity_id = ap.id
                                    LEFT JOIN economic_activities ea ON ea.code = ps.activity_economic_id
                                    LEFT JOIN ( 
                                                SELECT p.identification_number, p.gender, p.hours, ROW_NUMBER() OVER (PARTITION BY identification_number, gender ORDER BY id DESC) AS rn
                                                FROM policy_spreadsheet_affiliates p
                                              ) psa ON psa.identification_number = g.number_identification_affiliate AND psa.rn = 1
                                    WHERE a.deleted_at IS NULL AND ea.group_economic IS NOT NULL AND g.type_report = 'Accidente' AND  YEAR(g.created_at) = '$xmlYear'
                                    GROUP BY ea.group_economic, YEAR(g.created_at)";

            $resultGis = DB::select($query);

            // Convertir los resultados en un array asociativo
            $dataArray = collect($resultGis)->map(function ($item) {
                return (array) $item;
            })->toArray();

            $subTotalPorRamaDeActividad = 0;
            $subTotalPorRamaDeActividadHombres = 0;
            $subTotalPorRamaDeActividadMujeres = 0;


            foreach ($data as $row) {

                if ($row->grupo_actividad !=$codRamaDeActividad) {

                    if (!empty($codRamaDeActividad)){

                        $filtrados = array_filter($dataArray, function ($item) use ($codRamaDeActividad) {
                            return $item['grupo_actividad'] === $codRamaDeActividad;
                        });

                        $subTotalPorRamaDeActividad = !empty($filtrados) ? array_values($filtrados)[0]['total'] : 0;
                        $subTotalPorRamaDeActividadHombres = !empty($filtrados) ? array_values($filtrados)[0]['total_hombres'] : 0;
                        $subTotalPorRamaDeActividadMujeres = !empty($filtrados) ? array_values($filtrados)[0]['total_mujeres'] : 0;

                        $ramaDeActividad->addAttribute('CodRamaDeActividad', $codRamaDeActividad);
                        $ramaDeActividad->addAttribute('SubTotalPorRamaDeActividad', intval($subTotalPorRamaDeActividad));
                        $ramaDeActividad->addAttribute('SubTotalPorRamaDeActividadHombres', intval($subTotalPorRamaDeActividadHombres));
                        $ramaDeActividad->addAttribute('SubTotalPorRamaDeActividadMujeres', intval($subTotalPorRamaDeActividadMujeres));

                        $ramaDeActividad = $ramasDeActividades->addChild('RamaDeActividad');

                        $subTotalPorRamaDeActividad = 0;
                        $subTotalPorRamaDeActividadHombres = 0;
                        $subTotalPorRamaDeActividadMujeres = 0;
                    }

                    $codRamaDeActividad=$row->grupo_actividad;

                    $gruposOcupacionales = $ramaDeActividad->addChild('GruposOcupacionales');
                    $formasDelosAccidentes = $ramaDeActividad->addChild('FormasDelosAccidentes');
                    $naturalezasDeLasLesiones = $ramaDeActividad->addChild('NaturalezasDeLasLesiones'); //pendiente
                    $ubicacionesDeLasLesiones = $ramaDeActividad->addChild('UbicacionesDeLasLesiones');
                    $agentesMaterialesInvolucrados = $ramaDeActividad->addChild('AgentesMaterialesInvolucrados'); //pendiente
                    $tiposDeIncapacidades = $ramaDeActividad->addChild('TiposDeIncapacidades');
                    $lugaresDelosAccidentes = $ramaDeActividad->addChild('LugaresDelosAccidentes');
                }

                if ($row->type == 'GruposOcupacionales') {
                    $grupoOcupacional = $gruposOcupacionales->addChild('GrupoOcupacional');
                    $grupoOcupacional->addAttribute('CodGrupoOcupacional', $row->grupo_ocupacion);
                    $grupoOcupacional->addAttribute('CantidadPorGrupoOcupacionalEnRamaDeActividad', intval($row->total));

                    $grupoOcupacional->addChild('CantidadPorGrupoOcupacionalEnRamaDeActividadHombres', intval($row->total_hombres));
                    $grupoOcupacional->addChild('CantidadPorGrupoOcupacionalEnRamaDeActividadMujeres', intval($row->total_mujeres));
                } else if ($row->type == 'FormasDelosAccidentes') {
                    $formaDelAccidente = $formasDelosAccidentes->addChild('FormaDelAccidente');
                    $formaDelAccidente->addAttribute('CodFormaDelAccidente', $row->grupo_ocupacion);
                    $formaDelAccidente->addAttribute('CantidadPorFormaDelAccidenteEnRamaDeActividad', intval($row->total));

                    $formaDelAccidente->addChild('CantidadPorFormaDelAccidenteEnRamaDeActividadHombres', intval($row->total_hombres));
                    $formaDelAccidente->addChild('CantidadPorFormaDelAccidenteEnRamaDeActividadMujeres', intval($row->total_mujeres));

                } else if ($row->type == 'NaturalezasDeLasLesiones') {

                    $naturalezaDeLaLesion = $naturalezasDeLasLesiones->addChild('NaturalezaDeLaLesion');
                    $naturalezaDeLaLesion->addAttribute('CodNaturalezaDeLaLesion', $row->grupo_ocupacion);
                    $naturalezaDeLaLesion->addAttribute('CantidadPorNaturalezaDeLaLesionEnRamaDeActividad', intval($row->total));

                    $naturalezaDeLaLesion->addChild('CantidadPorNaturalezaDeLaLesionEnRamaDeActividadHombres', intval($row->total_hombres));
                    $naturalezaDeLaLesion->addChild('CantidadPorNaturalezaDeLaLesionEnRamaDeActividadMujeres', intval($row->total_mujeres));

                } else if ($row->type == 'UbicacionesDeLasLesiones') {

                    $ubicacionDeLaLesion = $ubicacionesDeLasLesiones->addChild('UbicacionDeLaLesion');
                    $ubicacionDeLaLesion->addAttribute('CodUbicacionDeLaLesion', $row->grupo_ocupacion);
                    $ubicacionDeLaLesion->addAttribute('CantidadPorUbicacionDeLaLesionEnRamaDeActividad', intval($row->total));

                    $ubicacionDeLaLesion->addChild('CantidadPorUbicacionDeLaLesionEnRamaDeActividadHombres', intval($row->total_hombres));
                    $ubicacionDeLaLesion->addChild('CantidadPorUbicacionDeLaLesionEnRamaDeActividadMujeres', intval($row->total_mujeres));

                } else if ($row->type == 'AgentesMaterialesInvolucrados') {

                    $agenteMaterialInvolucrado = $agentesMaterialesInvolucrados->addChild('AgenteMaterialInvolucrado');
                    $agenteMaterialInvolucrado->addAttribute('CodAgenteMaterialInvolucrado', $row->grupo_ocupacion);
                    $agenteMaterialInvolucrado->addAttribute('CantidadPorAgenteMaterialInvolucradoEnRamaDeActividad', intval($row->total));

                    $agenteMaterialInvolucrado->addChild('CantidadPorAgenteMaterialInvolucradoEnRamaDeActividadHombres', intval($row->total_hombres));
                    $agenteMaterialInvolucrado->addChild('CantidadPorAgenteMaterialInvolucradoEnRamaDeActividadMujeres', intval($row->total_mujeres));

                } else if ($row->type == 'TiposDeIncapacidades') {
                    $tipoDeIncapacidad = $tiposDeIncapacidades->addChild('TipoDeIncapacidad');
                    $tipoDeIncapacidad->addAttribute('CodTipoDeIncapacidad', $row->grupo_ocupacion);
                    $tipoDeIncapacidad->addAttribute('CantidadPorTipoDeIncapacidadEnRamaDeActividad', intval($row->total));

                    $tipoDeIncapacidad->addChild('CantidadPorTipoDeIncapacidadEnRamaDeActividadHombres', intval($row->total_hombres));
                    $tipoDeIncapacidad->addChild('CantidadPorTipoDeIncapacidadEnRamaDeActividadMujeres', intval($row->total_mujeres));

                } else if ($row->type == 'LugaresDelosAccidentes') {
                    $lugarDelAccidente = $lugaresDelosAccidentes->addChild('LugarDelAccidente');
                    $lugarDelAccidente->addAttribute('CodLugarDelAccidente', $row->grupo_ocupacion);
                    $lugarDelAccidente->addAttribute('CantidadPorLugarDelAccidenteEnRamaDeActividad', intval($row->total));

                    $lugarDelAccidente->addChild('CantidadPorLugarDelAccidenteEnRamaDeActividadHombres', intval($row->total_hombres));
                    $lugarDelAccidente->addChild('CantidadPorLugarDelAccidenteEnRamaDeActividadMujeres', intval($row->total_mujeres));
                }

                $subTotalPorRamaDeActividad += $row->total;
                $subTotalPorRamaDeActividadHombres += $row->total_hombres;
                $subTotalPorRamaDeActividadMujeres += $row->total_mujeres;

            }

            if (!empty($codRamaDeActividad)) {

                $filtrados = array_filter($dataArray, function ($item) use ($codRamaDeActividad) {
                    return $item['grupo_actividad'] === $codRamaDeActividad;
                });

                $subTotalPorRamaDeActividad = !empty($filtrados) ? array_values($filtrados)[0]['total'] : 0;
                $subTotalPorRamaDeActividadHombres = !empty($filtrados) ? array_values($filtrados)[0]['total_hombres'] : 0;
                $subTotalPorRamaDeActividadMujeres = !empty($filtrados) ? array_values($filtrados)[0]['total_mujeres'] : 0;


                $ramaDeActividad->addAttribute('CodRamaDeActividad', $codRamaDeActividad);
                $ramaDeActividad->addAttribute('SubTotalPorRamaDeActividad', intval($subTotalPorRamaDeActividad));
                $ramaDeActividad->addAttribute('SubTotalPorRamaDeActividadHombres', intval($subTotalPorRamaDeActividadHombres));
                $ramaDeActividad->addAttribute('SubTotalPorRamaDeActividadMujeres', intval($subTotalPorRamaDeActividadMujeres));
            }


            // Formatear el XML adecuadamente
            $dom = new \DOMDocument('1.0');
            $dom->preserveWhiteSpace = false;
            $dom->formatOutput = true;
            $dom->loadXML($xml->asXML());

            // Establecer headers y devolver el XML
            $fileName = 'ModeloAccidentesLaboralesPorRamaDeActividadRT_'.$xmlYear. '.xml';
            return response($dom->saveXML(), 200)
                ->header('Content-Type', 'application/xml')
                ->header('Content-Disposition', 'attachment; filename="' . $fileName . '"');

        } catch (\Exception $e) {
            return redirect('/admin/reportes/descargas_admin')
                ->withErrors($e->getMessage())
                ->withInput();
        }
    }

    public function queryXmlOne($xmlYear)
    {
        $query = "CREATE OR REPLACE
ALGORITHM = UNDEFINED VIEW `report_xml_one` AS
(
                    SELECT ea.group_economic AS grupo_actividad,
							      case when oc.group_id IS NULL then 0 ELSE oc.group_id END AS grupo_ocupacion,                           
							      SUM(CASE WHEN psa.gender IN ('F','M') THEN 1 ELSE 0 END) AS total,
							      SUM(CASE WHEN psa.gender = 'M' THEN 1 ELSE 0 END) AS total_hombres,
							      SUM(CASE WHEN psa.gender = 'F' THEN 1 ELSE 0 END) AS total_mujeres,
							      YEAR(g.created_at) AS ano,
							      'GruposOcupacionales' AS type,
							      1 AS id
							FROM gis_sort g 
							LEFT JOIN activities a ON a.id=g.activity_id
							LEFT JOIN activities ap ON ap.id=a.parent_id
							LEFT JOIN policy_sorts ps ON ps.activity_id = ap.id
							LEFT JOIN economic_activities ea ON ea.code = ps.activity_economic_id
							LEFT JOIN occupations oc ON oc.id= g.occupancy_group
							LEFT JOIN ( 
							            SELECT p.identification_number, p.gender, p.hours, ROW_NUMBER() OVER (PARTITION BY identification_number, gender ORDER BY id DESC) AS rn
							            FROM policy_spreadsheet_affiliates p
							          ) psa ON psa.identification_number = g.number_identification_affiliate AND psa.rn = 1
						   LEFT JOIN ( 								
										SELECT a.activity_id
										FROM activity_actions a 
										WHERE a.new_state_id = 160
										GROUP BY a.activity_id
									) aa ON aa.activity_id=g.activity_id
							WHERE ea.group_economic IS NOT NULL AND aa.activity_id IS NOT NULL 
							GROUP BY ea.group_economic
                    
                    UNION ALL
                    
                    SELECT ea.group_economic AS grupo_actividad,
                             act.cod_sugese as forma, 
                           SUM(CASE WHEN psa.gender IN ('F','M') THEN 1 ELSE 0 END) AS total,
                           SUM(CASE WHEN psa.gender = 'M' THEN 1 ELSE 0 END) AS total_hombres,
                           SUM(CASE WHEN psa.gender = 'F' THEN 1 ELSE 0 END) AS total_mujeres,
                           YEAR(g.created_at) AS ano,
                           'FormasDelosAccidentes' AS type,
                           2 AS id
                    FROM gis_sort g 
                    LEFT JOIN activities a ON a.id=g.activity_id
                    LEFT JOIN activities ap ON ap.id=a.parent_id
                    LEFT JOIN policy_sorts ps ON ps.activity_id = ap.id
                    LEFT JOIN economic_activities ea ON ea.code = ps.activity_economic_id
                    LEFT JOIN accident_types act ON act.id = g.ocurrencia
                    LEFT JOIN ( 
                                 SELECT p.identification_number, p.gender, p.hours, ROW_NUMBER() OVER (PARTITION BY identification_number, gender ORDER BY id DESC) AS rn
                                 FROM policy_spreadsheet_affiliates p
                               ) psa ON psa.identification_number = g.number_identification_affiliate AND psa.rn = 1
                     LEFT JOIN ( 								
										SELECT a.activity_id
										FROM activity_actions a 
										WHERE a.new_state_id = 160
										GROUP BY a.activity_id
									) aa ON aa.activity_id=g.activity_id
                    WHERE ea.group_economic IS NOT NULL AND act.cod_sugese IS NOT NULL AND aa.activity_id IS NOT NULL 
                    GROUP BY ea.group_economic, act.cod_sugese
                    
                    UNION all
                    
                      SELECT ea.group_economic AS grupo_actividad,
							      g.mechanism_trauma AS grupo_ocupacion,                           
							      SUM(CASE WHEN psa.gender IN ('F','M') THEN 1 ELSE 0 END) AS total,
							      SUM(CASE WHEN psa.gender = 'M' THEN 1 ELSE 0 END) AS total_hombres,
							      SUM(CASE WHEN psa.gender = 'F' THEN 1 ELSE 0 END) AS total_mujeres,
							      YEAR(g.created_at) AS ano,
							      'NaturalezasDeLasLesiones' AS type,
							      3 AS id
							FROM gis_sort g 
							LEFT JOIN activities a ON a.id=g.activity_id
							LEFT JOIN activities ap ON ap.id=a.parent_id
							LEFT JOIN policy_sorts ps ON ps.activity_id = ap.id
							LEFT JOIN economic_activities ea ON ea.code = ps.activity_economic_id
							LEFT JOIN ( 
							            SELECT p.identification_number, p.gender, p.hours, ROW_NUMBER() OVER (PARTITION BY identification_number, gender ORDER BY id DESC) AS rn
							            FROM policy_spreadsheet_affiliates p
							          ) psa ON psa.identification_number = g.number_identification_affiliate AND psa.rn = 1
						   LEFT JOIN ( 								
											SELECT a.activity_id
											FROM activity_actions a 
											WHERE a.new_state_id = 160
											GROUP BY a.activity_id
										) aa ON aa.activity_id=g.activity_id
							WHERE ea.group_economic IS NOT NULL  AND g.type_report = 'Accidente' AND aa.activity_id IS NOT NULL  
							GROUP BY ea.group_economic

                    
                    UNION ALL 
                    
                    SELECT
							  ea.group_economic AS grupo_actividad,
						     bp.code as grupo,
						     SUM(CASE WHEN psa.gender IN ('F','M') THEN 1 ELSE 0 END) AS total,
						     SUM(CASE WHEN psa.gender = 'M' THEN 1 ELSE 0 END) as total_hombres,
						     SUM(CASE WHEN psa.gender = 'F' THEN 1 ELSE 0 END) as total_mujeres,
						      YEAR(g.created_at) AS ano,
						      'UbicacionesDeLasLesiones' AS type,
						      4 AS id
						  FROM gis_sort g
						  LEFT JOIN activities a ON a.id = g.activity_id
						  LEFT JOIN activities ap ON ap.id=a.parent_id
						  LEFT JOIN policy_sorts ps ON ps.activity_id = ap.id
						  LEFT JOIN gis_body_parts gp ON gp.gis_id=g.id
						  LEFT JOIN body_parts bp ON bp.name COLLATE utf8mb4_unicode_ci = gp.body_part_name COLLATE UTF8MB4_UNICODE_CI
						  LEFT JOIN economic_activities ea ON ea.code = ps.activity_economic_id
						  LEFT JOIN ( SELECT p.identification_number, p.gender
						              FROM policy_spreadsheet_affiliates p
						              GROUP BY p.identification_number, p.gender) psa ON psa.identification_number = g.number_identification_affiliate
					     LEFT JOIN ( 								
										SELECT a.activity_id
										FROM activity_actions a 
										WHERE a.new_state_id = 160
										GROUP BY a.activity_id
									) aa ON aa.activity_id=g.activity_id
						  WHERE bp.code IS NOT NULL  AND aa.activity_id IS NOT NULL AND ea.group_economic is not null 
						  GROUP BY ea.group_economic, gp.body_part_name

						  UNION all
						  
						  SELECT ea.group_economic AS grupo_actividad,
							      ag.code_sugese AS grupo_ocupacion,                           
							      SUM(CASE WHEN psa.gender IN ('F','M') THEN 1 ELSE 0 END) AS total,
							      SUM(CASE WHEN psa.gender = 'M' THEN 1 ELSE 0 END) AS total_hombres,
							      SUM(CASE WHEN psa.gender = 'F' THEN 1 ELSE 0 END) AS total_mujeres,
							      YEAR(g.created_at) AS ano,
							      'AgentesMaterialesInvolucrados' AS type,
							      5 AS id
							FROM gis_sort g 
							LEFT JOIN activities a ON a.id=g.activity_id
							LEFT JOIN activities ap ON ap.id=a.parent_id
							LEFT JOIN policy_sorts ps ON ps.activity_id = ap.id
							LEFT JOIN economic_activities ea ON ea.code = ps.activity_economic_id
							LEFT JOIN agets_gis ag ON ag.id=g.material_agente
							LEFT JOIN ( 
							            SELECT p.identification_number, p.gender, p.hours, ROW_NUMBER() OVER (PARTITION BY identification_number, gender ORDER BY id DESC) AS rn
							            FROM policy_spreadsheet_affiliates p
							          ) psa ON psa.identification_number = g.number_identification_affiliate AND psa.rn = 1
							LEFT JOIN ( 								
											SELECT a.activity_id
											FROM activity_actions a 
											WHERE a.new_state_id = 160
											GROUP BY a.activity_id
										) aa ON aa.activity_id=g.activity_id
							WHERE ea.group_economic IS NOT NULL  AND g.type_report = 'Accidente' AND aa.activity_id IS NOT NULL  
							GROUP BY ea.group_economic, ag.code_sugese
                    
                    UNION ALL
                    
                    SELECT
                            ea.group_economic, 
                            case ap.service_id 
                                    when 84 then 1
                                    when 86 then 2
                                    ELSE 3 END AS grupo,
                            SUM(CASE WHEN psa.gender IN ('F','M') THEN 1 ELSE 0 END) AS total,
                          SUM(CASE WHEN psa.gender = 'M' THEN 1 ELSE 0 END) AS total_hombres,
                          SUM(CASE WHEN psa.gender = 'F' THEN 1 ELSE 0 END) AS total_mujeres,
                          YEAR(g.created_at) AS ano,
                          'TiposDeIncapacidades' AS TYPE,
                          6 AS id
                    FROM gis_sort g 
                    LEFT JOIN activities a ON a.id = g.activity_id
                    LEFT JOIN activities app ON app.id = a.parent_id
                    LEFT JOIN policy_sorts ps ON ps.activity_id = app.id
                    LEFT JOIN economic_activities ea ON ea.code = ps.activity_economic_id
                    LEFT JOIN ( 
                                 SELECT p.identification_number, p.gender, p.hours, ROW_NUMBER() OVER (PARTITION BY identification_number, gender ORDER BY id DESC) AS rn
                                 FROM policy_spreadsheet_affiliates p
                               ) psa ON psa.identification_number = g.number_identification_affiliate AND psa.rn = 1
                    LEFT JOIN (
                            SELECT a.parent_id, a.service_id
                            FROM activities a 
                            where a.service_id IN (84, 86)
                            ) ap on ap.parent_id = a.id
                    LEFT JOIN ( 								
										SELECT a.activity_id
										FROM activity_actions a 
										WHERE a.new_state_id = 160
										GROUP BY a.activity_id
									) aa ON aa.activity_id=g.activity_id
                    WHERE ea.group_economic IS NOT NULL AND aa.activity_id IS NOT NULL 
                    GROUP BY case ap.service_id 
                                    when 84 then 1
                                    when 86 then 2
                                    ELSE 3 END,
                                ea.group_economic
                    
                    UNION ALL
                    
                    SELECT 
                            ea.group_economic,
                          case g.accident_place 
                                when 1 then 1
                                when 2 then 3
                                when 3 then 3
                                when 4 then 4
                                when 5 then 1
                                when 6 then 1
                                when 6 then 3
                                when 7 then 2 END  as grupo,
                          SUM(CASE WHEN psa.gender IN ('F','M') THEN 1 ELSE 0 END) AS total,
                          SUM(CASE WHEN psa.gender = 'M' THEN 1 ELSE 0 END) AS total_hombres,
                          SUM(CASE WHEN psa.gender = 'F' THEN 1 ELSE 0 END) AS total_mujeres,
                          YEAR(g.created_at) AS ano,
                          'LugaresDelosAccidentes' AS TYPE,
                          7 AS id
                      FROM gis_sort g 
                      LEFT JOIN activities a ON a.id = g.activity_id
                      LEFT JOIN activities app ON app.id = a.parent_id
                      LEFT JOIN policy_sorts ps ON ps.activity_id = app.id
                      LEFT JOIN economic_activities ea ON ea.code = ps.activity_economic_id
                      LEFT JOIN ( 
                             SELECT p.identification_number, p.gender, p.hours, ROW_NUMBER() OVER (PARTITION BY identification_number, gender ORDER BY id DESC) AS rn
                             FROM policy_spreadsheet_affiliates p
                           ) psa ON psa.identification_number = g.number_identification_affiliate AND psa.rn = 1
                        LEFT JOIN ( 								
											SELECT a.activity_id
											FROM activity_actions a 
											WHERE a.new_state_id = 160
											GROUP BY a.activity_id
										) aa ON aa.activity_id=g.activity_id
                      WHERE g.accident_place IS NOT NULL
                          AND ea.group_economic IS NOT NULL AND aa.activity_id IS NOT NULL 
                      GROUP BY (case g.accident_place 
                                when 1 then 1
                                when 2 then 3
                                when 3 then 3
                                when 4 then 4
                                when 5 then 1
                                when 6 then 1
                                when 6 then 3
                                when 7 then 2 END ), ea.group_economic  
                    )  ";

        $query = "  SELECT * 
                    FROM report_xml_one 
                    WHERE ano = '$xmlYear' 
                    ORDER BY grupo_actividad, id, grupo_ocupacion";

        $data = DB::select($query);

        return $data;

    }

    //ME-1320 - Requerimiento 40 parte 2 : Generación de modelos XML 14 SUGESE
    public function reportXmlTwo(Request $req)
    {
        try {
            // Obtener parámetros de la solicitud
            $xmlYear = $req->xml_two_year;
            $periodo = $req->periodo ?? date('Y-m-d'); // Período a reportar (opcional)

            // Validaciones
            if (empty($xmlYear)) {
                throw new \Exception("El campo año no puede estar vacío, en la sección 'MODELO XML 14'");
            }

            // Generar el XML
            $xml = new \SimpleXMLElement('<?xml version="1.0" encoding="utf-8"?>
            <ModeloAccidentesLaboralesRT  
                xmlns:xsd="http://www.w3.org/2001/XMLSchema" 
                xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            </ModeloAccidentesLaboralesRT >');

            // Agregar encabezado
            $encabezado = $xml->addChild('Encabezado');
            $encabezado->addChild('NombreArchivo', 'ModeloAccidentesLaboralesRT');
            $encabezado->addChild('Fecha', date('Y-m-d'));
            $encabezado->addChild('Periodo', $xmlYear.'-12-31');
            $encabezado->addChild('Periodicidad', 'A');
            $encabezado->addChild('Moneda', '1');

            // Agregar datos
            $datos = $xml->addChild('Datos');
            $modelo = $datos->addChild('Modelo');
            $modelo->addAttribute('EntidadFuente', 'A13');

            $accidentes = $modelo->addChild('AccidentesLaborales');

            //Totales>
            $totales = $accidentes->addChild('Totales');


            //Detalle>
            $detalle = $accidentes->addChild('Detalle');

            $provinciasCantonesDistritos = $detalle->addChild('ProvinciasCantonesDistritos');
            $gruposDeEdades = $detalle->addChild('GruposDeEdades');
            $gruposOcupacionales = $detalle->addChild('GruposOcupacionales');
            $ramasDeActividades = $detalle->addChild('RamasDeActividades');
            $tamanosDeEmpresas = $detalle->addChild('TamanosDeEmpresas');

            $data = $this->queryDetalle($xmlYear);

            //$query = "SELECT * FROM report_xml_gis where ano = '$xmlYear'"; ;
            $totalAccidentesLaboralesEnProvinciasCantonesDistritosHombres = 0;
            $totalAccidentesLaboralesEnProvinciasCantonesDistritosMujeres = 0;
            $totalHorasTrabajadasEnProvinciasCantonesDistritosHombres = 0;
            $totalHorasTrabajadasEnProvinciasCantonesDistritosMujeres = 0;

            $totalAccidentesLaboralesEnGruposDeEdadesHombres = 0;
            $totalAccidentesLaboralesEnGruposDeEdadesMujeres = 0;
            $totalHorasTrabajadasEnGruposDeEdadesHombres = 0;
            $totalHorasTrabajadasEnGruposDeEdadesMujeres = 0;

            $totalAccidentesLaboralesEnGruposOcupacionalesHombres = 0;
            $totalAccidentesLaboralesEnGruposOcupacionalesMujeres = 0;
            $totalHorasTrabajadasEnGruposOcupacionalesHombres = 0;
            $totalHorasTrabajadasEnGruposOcupacionalesMujeres = 0;

            $totalAccidentesLaboralesEnRamasDeActividadesHombres = 0;
            $totalAccidentesLaboralesEnRamasDeActividadesMujeres = 0;
            $totalHorasTrabajadasEnRamasDeActividadesHombres = 0;
            $totalHorasTrabajadasEnRamasDeActividadesMujeres = 0;

            $totalAccidentesLaboralesEnTamanosDeEmpresasHombres = 0;
            $totalAccidentesLaboralesEnTamanosDeEmpresasMujeres = 0;
            $totalHorasTrabajadasEnTamanosDeEmpresasHombres = 0;
            $totalHorasTrabajadasEnTamanosDeEmpresasMujeres = 0;

            $TotalTrabajadoresAseguradosEnProvinciasCantonesDistritosHombres = 0;
            $TotalTrabajadoresAseguradosEnProvinciasCantonesDistritosMujeres = 0;
            $TotalAccidentesLaboralesUnoMasDiasEnProvinciasCantonesDistritosHombres = 0;
            $TotalAccidentesLaboralesUnoMasDiasEnProvinciasCantonesDistritosMujeres = 0;
            $TotalDiasDeIncapacidadUnoMasDiasEnProvinciasCantonesDistritosHombres = 0;
            $TotalDiasDeIncapacidadUnoMasDiasEnProvinciasCantonesDistritosMujeres = 0;

            $TotalTrabajadoresAseguradosEnGruposDeEdadesHombres = 0;
            $TotalTrabajadoresAseguradosEnGruposDeEdadesMujeres = 0;
            $TotalAccidentesLaboralesUnoMasDiasEnGruposDeEdadesHombres = 0;
            $TotalAccidentesLaboralesUnoMasDiasEnGruposDeEdadesMujeres = 0;
            $TotalDiasDeIncapacidadUnoMasDiasEnGruposDeEdadesHombres = 0;
            $TotalDiasDeIncapacidadUnoMasDiasEnGruposDeEdadesMujeres = 0;

            $TotalTrabajadoresAseguradosEnGruposOcupacionalesHombres = 0;
            $TotalTrabajadoresAseguradosEnGruposOcupacionalesMujeres = 0;
            $TotalAccidentesLaboralesUnoMasDiasEnGruposOcupacionalesHombres = 0;
            $TotalAccidentesLaboralesUnoMasDiasEnGruposOcupacionalesMujeres = 0;
            $TotalDiasDeIncapacidadUnoMasDiasEnGruposOcupacionalesHombres = 0;
            $TotalDiasDeIncapacidadUnoMasDiasEnGruposOcupacionalesMujeres = 0;

            $TotalTrabajadoresAseguradosEnRamasDeActividadesHombres = 0;
            $TotalTrabajadoresAseguradosEnRamasDeActividadesMujeres = 0;
            $TotalAccidentesLaboralesUnoMasDiasEnRamasDeActividadesHombres = 0;
            $TotalAccidentesLaboralesUnoMasDiasEnRamasDeActividadesMujeres = 0;
            $TotalDiasDeIncapacidadUnoMasDiasEnRamasDeActividadesHombres = 0;
            $TotalDiasDeIncapacidadUnoMasDiasEnRamasDeActividadesMujeres = 0;

            $TotalTrabajadoresAseguradosEnTamanosDeEmpresasHombres = 0;
            $TotalTrabajadoresAseguradosEnTamanosDeEmpresasMujeres = 0;
            $TotalAccidentesLaboralesUnoMasDiasEnTamanosDeEmpresasHombres = 0;
            $TotalAccidentesLaboralesUnoMasDiasEnTamanosDeEmpresasMujeres = 0;
            $TotalDiasDeIncapacidadUnoMasDiasEnTamanosDeEmpresasHombres = 0;
            $TotalDiasDeIncapacidadUnoMasDiasEnTamanosDeEmpresasMujeres = 0;

            $TotalTrabajadoresAsegurados = 0;
            $TotalHorasTrabajadas = 0;

            foreach ($data as $row) {
                if ($row->type == 'ProvinciasCantonesDistritos') {
                    $provinciaCantonDistrito = $provinciasCantonesDistritos->addChild('ProvinciaCantonDistrito');
                    //ATRIBUTOS
                    $provinciaCantonDistrito->addAttribute('CodProvinciaCantonDistrito', $row->grupo);
                    $provinciaCantonDistrito->addAttribute('CantidadAccidentesLaboralesPorProvinciaCantonDistrito', intval(($row->cat_accidente_hombre + $row->cat_accidente_mujer)));
                    $provinciaCantonDistrito->addAttribute('CantidadTrabajadoresAseguradosPorProvinciaCantonDistrito', intval($row->as_cant_asegurados));
                    $provinciaCantonDistrito->addAttribute('CantidadHorasTrabajadasPorProvinciaCantonDistrito', intval($row->as_total_horas));
                    $provinciaCantonDistrito->addAttribute('CantidadAccidentesLaboralesUnoMasDiasPorProvinciaCantonDistrito', intval(($row->cat_unomasdias_hombres + $row->cat_unomasdias_mujeres)));
                    $provinciaCantonDistrito->addAttribute('CantidadDiasDeIncapacidadUnoMasDiasPorProvinciaCantonDistrito', intval(($row->cat_dias_hombres + $row->cat_dias_mujeres)));
                    //ETIQUETAS
                    $provinciaCantonDistrito->addChild('CantidadAccidentesLaboralesPorProvinciaCantonDistritoHombres', intval($row->cat_accidente_hombre));
                    $provinciaCantonDistrito->addChild('CantidadAccidentesLaboralesPorProvinciaCantonDistritoMujeres', intval($row->cat_accidente_mujer));
                    $provinciaCantonDistrito->addChild('CantidadTrabajadoresAseguradosPorProvinciaCantonDistritoHombres', intval($row->as_cant_hombres));
                    $provinciaCantonDistrito->addChild('CantidadTrabajadoresAseguradosPorProvinciaCantonDistritoMujeres', intval($row->as_cant_mujeres));
                    $provinciaCantonDistrito->addChild('CantidadHorasTrabajadasPorProvinciaCantonDistritoHombres', intval($row->as_horas_hombres));
                    $provinciaCantonDistrito->addChild('CantidadHorasTrabajadasPorProvinciaCantonDistritoMujeres', intval($row->as_horas_mujeres));
                    $provinciaCantonDistrito->addChild('CantidadAccidentesLaboralesUnoMasDiasPorProvinciaCantonDistritoHombres', intval($row->cat_unomasdias_hombres));
                    $provinciaCantonDistrito->addChild('CantidadAccidentesLaboralesUnoMasDiasPorProvinciaCantonDistritoMujeres', intval($row->cat_unomasdias_mujeres));
                    $provinciaCantonDistrito->addChild('CantidadDiasDeIncapacidadUnoMasDiasPorProvinciaCantonDistritoHombres', intval($row->cat_dias_hombres));
                    $provinciaCantonDistrito->addChild('CantidadDiasDeIncapacidadUnoMasDiasPorProvinciaCantonDistritoMujeres', intval($row->cat_dias_mujeres));

                    $totalAccidentesLaboralesEnProvinciasCantonesDistritosHombres += $row->cat_accidente_hombre ?? 0 ;
                    $totalAccidentesLaboralesEnProvinciasCantonesDistritosMujeres += $row->cat_accidente_mujer ?? 0 ;
                    $totalHorasTrabajadasEnProvinciasCantonesDistritosHombres += $row->as_horas_hombres ?? 0;
                    $totalHorasTrabajadasEnProvinciasCantonesDistritosMujeres += $row->as_horas_mujeres ?? 0;

                    $TotalTrabajadoresAseguradosEnProvinciasCantonesDistritosHombres += $row->as_cant_hombres;
                    $TotalTrabajadoresAseguradosEnProvinciasCantonesDistritosMujeres += $row->as_cant_mujeres;
                    $TotalAccidentesLaboralesUnoMasDiasEnProvinciasCantonesDistritosHombres += $row->cat_unomasdias_hombres;
                    $TotalAccidentesLaboralesUnoMasDiasEnProvinciasCantonesDistritosMujeres += $row->cat_unomasdias_mujeres;
                    $TotalDiasDeIncapacidadUnoMasDiasEnProvinciasCantonesDistritosHombres += $row->cat_dias_hombres;
                    $TotalDiasDeIncapacidadUnoMasDiasEnProvinciasCantonesDistritosMujeres += $row->cat_dias_mujeres;

                    $TotalTrabajadoresAsegurados += $row->as_cant_asegurados;
                    $TotalHorasTrabajadas += $row->as_total_horas;

                } else if ($row->type == 'GruposDeEdades') {
                    $grupoDeEdad = $gruposDeEdades->addChild('GrupoDeEdad');
                    //ATRIBUTOS
                    $grupoDeEdad->addAttribute('CodGrupoDeEdad', $row->grupo);
                    $grupoDeEdad->addAttribute('CantidadAccidentesLaboralesPorGrupoDeEdad', intval(($row->cat_accidente_hombre + $row->cat_accidente_mujer)));
                    $grupoDeEdad->addAttribute('CantidadTrabajadoresAseguradosPorGrupoDeEdad', intval($row->as_cant_asegurados));
                    $grupoDeEdad->addAttribute('CantidadHorasTrabajadasPorGrupoDeEdad', intval($row->as_total_horas));
                    $grupoDeEdad->addAttribute('CantidadAccidentesLaboralesUnoMasDiasPorGrupoDeEdad', intval(($row->cat_unomasdias_hombres + $row->cat_unomasdias_mujeres)));
                    $grupoDeEdad->addAttribute('CantidadDiasDeIncapacidadUnoMasDiasPorGrupoDeEdad', intval(($row->cat_dias_hombres + $row->cat_dias_mujeres)));
                    //ETIQUETAS
                    $grupoDeEdad->addChild('CantidadAccidentesLaboralesPorGrupoDeEdadHombres', intval($row->cat_accidente_hombre));
                    $grupoDeEdad->addChild('CantidadAccidentesLaboralesPorGrupoDeEdadMujeres', intval($row->cat_accidente_mujer));
                    $grupoDeEdad->addChild('CantidadTrabajadoresAseguradosPorGrupoDeEdadHombres', intval($row->as_cant_hombres));
                    $grupoDeEdad->addChild('CantidadTrabajadoresAseguradosPorGrupoDeEdadMujeres', intval($row->as_cant_mujeres));
                    $grupoDeEdad->addChild('CantidadHorasTrabajadasPorGrupoDeEdadHombres', intval($row->as_horas_hombres));
                    $grupoDeEdad->addChild('CantidadHorasTrabajadasPorGrupoDeEdadMujeres', intval($row->as_horas_mujeres));
                    $grupoDeEdad->addChild('CantidadAccidentesLaboralesUnoMasDiasPorGrupoDeEdadHombres', intval($row->cat_unomasdias_hombres));
                    $grupoDeEdad->addChild('CantidadAccidentesLaboralesUnoMasDiasPorGrupoDeEdadMujeres', intval($row->cat_unomasdias_mujeres));
                    $grupoDeEdad->addChild('CantidadDiasDeIncapacidadUnoMasDiasPorGrupoDeEdadHombres', intval($row->cat_dias_hombres));
                    $grupoDeEdad->addChild('CantidadDiasDeIncapacidadUnoMasDiasPorGrupoDeEdadMujeres', intval($row->cat_dias_mujeres));

                    $totalAccidentesLaboralesEnGruposDeEdadesHombres += $row->cat_accidente_hombre ?? 0 ;
                    $totalAccidentesLaboralesEnGruposDeEdadesMujeres += $row->cat_accidente_mujer ?? 0 ;
                    $totalHorasTrabajadasEnGruposDeEdadesHombres += $row->as_horas_hombres ?? 0;
                    $totalHorasTrabajadasEnGruposDeEdadesMujeres += $row->as_horas_mujeres ?? 0;


                    $TotalTrabajadoresAseguradosEnGruposDeEdadesHombres += $row->as_cant_hombres;
                    $TotalTrabajadoresAseguradosEnGruposDeEdadesMujeres += $row->as_cant_mujeres;
                    $TotalAccidentesLaboralesUnoMasDiasEnGruposDeEdadesHombres += $row->cat_unomasdias_hombres;
                    $TotalAccidentesLaboralesUnoMasDiasEnGruposDeEdadesMujeres += $row->cat_unomasdias_mujeres;
                    $TotalDiasDeIncapacidadUnoMasDiasEnGruposDeEdadesHombres += $row->cat_dias_hombres;
                    $TotalDiasDeIncapacidadUnoMasDiasEnGruposDeEdadesMujeres += $row->cat_dias_mujeres;

                }else if ($row->type == 'GruposOcupacionales') {
                    $grupoOcupacional = $gruposOcupacionales->addChild('GrupoOcupacional');
                    //ATRIBUTOS
                    $grupoOcupacional->addAttribute('CodGrupoOcupacional', $row->grupo);
                    $grupoOcupacional->addAttribute('CantidadAccidentesLaboralesPorGrupoOcupacional', intval(($row->cat_accidente_hombre + $row->cat_accidente_mujer)));
                    $grupoOcupacional->addAttribute('CantidadTrabajadoresAseguradosPorGrupoOcupacional',  intval(($row->cat_asegurado_hombres + $row->cat_asegurado_mujeres)));
                    $grupoOcupacional->addAttribute('CantidadHorasTrabajadasPorGrupoOcupacional', intval(($row->cat_horas_hombres + $row->cat_horas_mujer)));
                    $grupoOcupacional->addAttribute('CantidadAccidentesLaboralesUnoMasDiasPorGrupoOcupacional', intval(($row->cat_unomasdias_hombres + $row->cat_unomasdias_mujeres)));
                    $grupoOcupacional->addAttribute('CantidadDiasDeIncapacidadUnoMasDiasPorGrupoOcupacional', intval(($row->cat_dias_hombres + $row->cat_dias_mujeres)));
                    //ETIQUETAS
                    $grupoOcupacional->addChild('CantidadAccidentesLaboralesPorGrupoOcupacionalHombres', intval($row->cat_accidente_hombre));
                    $grupoOcupacional->addChild('CantidadAccidentesLaboralesPorGrupoOcupacionalMujeres', intval($row->cat_accidente_mujer));
                    $grupoOcupacional->addChild('CantidadTrabajadoresAseguradosPorGrupoOcupacionalHombres',intval($row->cat_asegurado_hombres));
                    $grupoOcupacional->addChild('CantidadTrabajadoresAseguradosPorGrupoOcupacionalMujeres', intval($row->cat_asegurado_mujeres));
                    $grupoOcupacional->addChild('CantidadHorasTrabajadasPorGrupoOcupacionalHombres', intval($row->cat_horas_hombres));
                    $grupoOcupacional->addChild('CantidadHorasTrabajadasPorGrupoOcupacionalMujeres', intval($row->cat_horas_mujer));
                    $grupoOcupacional->addChild('CantidadAccidentesLaboralesUnoMasDiasPorGrupoOcupacionalHombres', intval($row->cat_unomasdias_hombres));
                    $grupoOcupacional->addChild('CantidadAccidentesLaboralesUnoMasDiasPorGrupoOcupacionalMujeres', intval($row->cat_unomasdias_mujeres));
                    $grupoOcupacional->addChild('CantidadDiasDeIncapacidadUnoMasDiasPorGrupoOcupacionalHombres', intval($row->cat_dias_hombres));
                    $grupoOcupacional->addChild('CantidadDiasDeIncapacidadUnoMasDiasPorGrupoOcupacionalMujeres', intval($row->cat_dias_mujeres));

                    $totalAccidentesLaboralesEnGruposOcupacionalesHombres += $row->cat_accidente_hombre ?? 0 ;
                    $totalAccidentesLaboralesEnGruposOcupacionalesMujeres += $row->cat_accidente_mujer ?? 0 ;
                    $totalHorasTrabajadasEnGruposOcupacionalesHombres += $row->as_horas_hombres ?? 0;
                    $totalHorasTrabajadasEnGruposOcupacionalesMujeres += $row->as_horas_mujeres ?? 0;

                    $TotalTrabajadoresAseguradosEnGruposOcupacionalesHombres += $row->as_cant_hombres;
                    $TotalTrabajadoresAseguradosEnGruposOcupacionalesMujeres += $row->as_cant_mujeres;
                    $TotalAccidentesLaboralesUnoMasDiasEnGruposOcupacionalesHombres += $row->cat_unomasdias_hombres;
                    $TotalAccidentesLaboralesUnoMasDiasEnGruposOcupacionalesMujeres += $row->cat_unomasdias_mujeres;
                    $TotalDiasDeIncapacidadUnoMasDiasEnGruposOcupacionalesHombres += $row->cat_dias_hombres;
                    $TotalDiasDeIncapacidadUnoMasDiasEnGruposOcupacionalesMujeres += $row->cat_dias_mujeres;

                }else if ($row->type == 'RamasDeActividades') {
                    $ramaDeActividad = $ramasDeActividades->addChild('RamaDeActividad');
                    //ATRIBUTOS
                    $ramaDeActividad->addAttribute('CodRamaDeActividad', $row->grupo);
                    $ramaDeActividad->addAttribute('CantidadAccidentesLaboralesPorRamaDeActividad', intval(($row->cat_accidente_hombre + $row->cat_accidente_mujer)));
                    $ramaDeActividad->addAttribute('CantidadTrabajadoresAseguradosPorRamaDeActividad', intval($row->as_cant_asegurados));
                    $ramaDeActividad->addAttribute('CantidadHorasTrabajadasPorRamaDeActividad', intval($row->as_total_horas));
                    $ramaDeActividad->addAttribute('CantidadAccidentesLaboralesUnoMasDiasPorRamaDeActividad', intval(($row->cat_unomasdias_hombres + $row->cat_unomasdias_mujeres)));
                    $ramaDeActividad->addAttribute('CantidadDiasDeIncapacidadUnoMasDiasPorRamaDeActividad', intval(($row->cat_dias_hombres + $row->cat_dias_mujeres)));
                    //ETIQUETAS
                    $ramaDeActividad->addChild('CantidadAccidentesLaboralesPorRamaDeActividadHombres', intval($row->cat_accidente_hombre));
                    $ramaDeActividad->addChild('CantidadAccidentesLaboralesPorRamaDeActividadMujeres', intval($row->cat_accidente_mujer));
                    $ramaDeActividad->addChild('CantidadTrabajadoresAseguradosPorRamaDeActividadHombres', intval($row->as_cant_hombres));
                    $ramaDeActividad->addChild('CantidadTrabajadoresAseguradosPorRamaDeActividadMujeres', intval($row->as_cant_mujeres));
                    $ramaDeActividad->addChild('CantidadHorasTrabajadasPorRamaDeActividadHombres', intval($row->as_horas_hombres));
                    $ramaDeActividad->addChild('CantidadHorasTrabajadasPorRamaDeActividadMujeres', intval($row->as_horas_mujeres));
                    $ramaDeActividad->addChild('CantidadAccidentesLaboralesUnoMasDiasPorRamaDeActividadHombres', intval($row->cat_unomasdias_hombres));
                    $ramaDeActividad->addChild('CantidadAccidentesLaboralesUnoMasDiasPorRamaDeActividadMujeres', intval($row->cat_unomasdias_mujeres));
                    $ramaDeActividad->addChild('CantidadDiasDeIncapacidadUnoMasDiasPorRamaDeActividadHombres', intval($row->cat_dias_hombres));
                    $ramaDeActividad->addChild('CantidadDiasDeIncapacidadUnoMasDiasPorRamaDeActividadMujeres', intval($row->cat_dias_mujeres));

                    $totalAccidentesLaboralesEnRamasDeActividadesHombres += $row->cat_accidente_hombre ?? 0 ;
                    $totalAccidentesLaboralesEnRamasDeActividadesMujeres += $row->cat_accidente_mujer ?? 0 ;
                    $totalHorasTrabajadasEnRamasDeActividadesHombres += $row->as_horas_hombres ?? 0;
                    $totalHorasTrabajadasEnRamasDeActividadesMujeres += $row->as_horas_mujeres ?? 0;

                    $TotalTrabajadoresAseguradosEnRamasDeActividadesHombres += $row->as_cant_hombres;
                    $TotalTrabajadoresAseguradosEnRamasDeActividadesMujeres += $row->as_cant_mujeres;
                    $TotalAccidentesLaboralesUnoMasDiasEnRamasDeActividadesHombres += $row->cat_unomasdias_hombres;
                    $TotalAccidentesLaboralesUnoMasDiasEnRamasDeActividadesMujeres += $row->cat_unomasdias_mujeres;
                    $TotalDiasDeIncapacidadUnoMasDiasEnRamasDeActividadesHombres += $row->cat_dias_hombres;
                    $TotalDiasDeIncapacidadUnoMasDiasEnRamasDeActividadesMujeres += $row->cat_dias_mujeres;

                }else if ($row->type == 'TamanosDeEmpresas') {
                    $tamanoDeEmpresa = $tamanosDeEmpresas->addChild('TamanoDeEmpresa');
                    //ATRIBUTOS
                    $tamanoDeEmpresa->addAttribute('CodTamanoDeEmpresa', $row->grupo);
                    $tamanoDeEmpresa->addAttribute('CantidadAccidentesLaboralesPorTamanoDeEmpresa', intval(($row->cat_accidente_hombre + $row->cat_accidente_mujer)));
                    $tamanoDeEmpresa->addAttribute('CantidadTrabajadoresAseguradosPorTamanoDeEmpresa', intval($row->as_cant_asegurados));
                    $tamanoDeEmpresa->addAttribute('CantidadHorasTrabajadasPorTamanoDeEmpresa', intval($row->as_total_horas));
                    $tamanoDeEmpresa->addAttribute('CantidadAccidentesLaboralesUnoMasDiasPorTamanoDeEmpresa', intval(($row->cat_unomasdias_hombres + $row->cat_unomasdias_mujeres)));
                    $tamanoDeEmpresa->addAttribute('CantidadDiasDeIncapacidadUnoMasDiasPorTamanoDeEmpresa', intval(($row->cat_dias_hombres + $row->cat_dias_mujeres)));
                    //ETIQUETAS
                    $tamanoDeEmpresa->addChild('CantidadAccidentesLaboralesPorTamanoDeEmpresaHombres', intval($row->cat_accidente_hombre));
                    $tamanoDeEmpresa->addChild('CantidadAccidentesLaboralesPorTamanoDeEmpresaMujeres', intval($row->cat_accidente_mujer));
                    $tamanoDeEmpresa->addChild('CantidadTrabajadoresAseguradosPorTamanoDeEmpresaHombres', intval($row->as_cant_hombres));
                    $tamanoDeEmpresa->addChild('CantidadTrabajadoresAseguradosPorTamanoDeEmpresaMujeres', intval($row->as_cant_mujeres));
                    $tamanoDeEmpresa->addChild('CantidadHorasTrabajadasPorTamanoDeEmpresaHombres', intval($row->as_horas_hombres));
                    $tamanoDeEmpresa->addChild('CantidadHorasTrabajadasPorTamanoDeEmpresaMujeres', intval($row->as_horas_mujeres));
                    $tamanoDeEmpresa->addChild('CantidadAccidentesLaboralesUnoMasDiasPorTamanoDeEmpresaHombres', intval($row->cat_unomasdias_hombres));
                    $tamanoDeEmpresa->addChild('CantidadAccidentesLaboralesUnoMasDiasPorTamanoDeEmpresaMujeres', intval($row->cat_unomasdias_mujeres));
                    $tamanoDeEmpresa->addChild('CantidadDiasDeIncapacidadUnoMasDiasPorTamanoDeEmpresaHombres', intval($row->cat_dias_hombres));
                    $tamanoDeEmpresa->addChild('CantidadDiasDeIncapacidadUnoMasDiasPorTamanoDeEmpresaMujeres', intval($row->cat_dias_mujeres));

                    $totalAccidentesLaboralesEnTamanosDeEmpresasHombres += $row->cat_accidente_hombre ?? 0 ;
                    $totalAccidentesLaboralesEnTamanosDeEmpresasMujeres += $row->cat_accidente_mujer ?? 0 ;
                    $totalHorasTrabajadasEnTamanosDeEmpresasHombres += $row->as_horas_hombres ?? 0;
                    $totalHorasTrabajadasEnTamanosDeEmpresasMujeres += $row->as_horas_mujeres ?? 0;

                    $TotalTrabajadoresAseguradosEnTamanosDeEmpresasHombres += $row->as_cant_hombres;
                    $TotalTrabajadoresAseguradosEnTamanosDeEmpresasMujeres += $row->as_cant_mujeres;
                    $TotalAccidentesLaboralesUnoMasDiasEnTamanosDeEmpresasHombres += $row->cat_unomasdias_hombres;
                    $TotalAccidentesLaboralesUnoMasDiasEnTamanosDeEmpresasMujeres += $row->cat_unomasdias_mujeres;
                    $TotalDiasDeIncapacidadUnoMasDiasEnTamanosDeEmpresasHombres += $row->cat_dias_hombres;
                    $TotalDiasDeIncapacidadUnoMasDiasEnTamanosDeEmpresasMujeres += $row->cat_dias_mujeres;
                }

            }

            // <Totales
            $totalesProvinciaCantonDistrito = $totales->addChild('TotalesProvinciaCantonDistrito');
            $totalesProvinciaCantonDistrito->addChild('TotalAccidentesLaboralesEnProvinciasCantonesDistritosHombres', intval($totalAccidentesLaboralesEnProvinciasCantonesDistritosHombres));
            $totalesProvinciaCantonDistrito->addChild('TotalAccidentesLaboralesEnProvinciasCantonesDistritosMujeres', intval($totalAccidentesLaboralesEnProvinciasCantonesDistritosMujeres));
            $totalesProvinciaCantonDistrito->addChild('TotalTrabajadoresAseguradosEnProvinciasCantonesDistritosHombres', intval($TotalTrabajadoresAseguradosEnProvinciasCantonesDistritosHombres));
            $totalesProvinciaCantonDistrito->addChild('TotalTrabajadoresAseguradosEnProvinciasCantonesDistritosMujeres', intval($TotalTrabajadoresAseguradosEnProvinciasCantonesDistritosMujeres));
            $totalesProvinciaCantonDistrito->addChild('TotalHorasTrabajadasEnProvinciasCantonesDistritosHombres', intval($totalHorasTrabajadasEnProvinciasCantonesDistritosHombres));
            $totalesProvinciaCantonDistrito->addChild('TotalHorasTrabajadasEnProvinciasCantonesDistritosMujeres', intval($totalHorasTrabajadasEnProvinciasCantonesDistritosMujeres));
            $totalesProvinciaCantonDistrito->addChild('TotalAccidentesLaboralesUnoMasDiasEnProvinciasCantonesDistritosHombres', intval($TotalAccidentesLaboralesUnoMasDiasEnProvinciasCantonesDistritosHombres));
            $totalesProvinciaCantonDistrito->addChild('TotalAccidentesLaboralesUnoMasDiasEnProvinciasCantonesDistritosMujeres', intval($TotalAccidentesLaboralesUnoMasDiasEnProvinciasCantonesDistritosMujeres));
            $totalesProvinciaCantonDistrito->addChild('TotalDiasDeIncapacidadUnoMasDiasEnProvinciasCantonesDistritosHombres', intval($TotalDiasDeIncapacidadUnoMasDiasEnProvinciasCantonesDistritosHombres));
            $totalesProvinciaCantonDistrito->addChild('TotalDiasDeIncapacidadUnoMasDiasEnProvinciasCantonesDistritosMujeres', intval($TotalDiasDeIncapacidadUnoMasDiasEnProvinciasCantonesDistritosMujeres));

            $totalesGruposDeEdades = $totales->addChild('TotalesGruposDeEdades');
            $totalesGruposDeEdades->addChild('TotalAccidentesLaboralesEnGruposDeEdadesHombres', intval($totalAccidentesLaboralesEnGruposDeEdadesHombres));
            $totalesGruposDeEdades->addChild('TotalAccidentesLaboralesEnGruposDeEdadesMujeres', intval($totalAccidentesLaboralesEnGruposDeEdadesMujeres));
            $totalesGruposDeEdades->addChild('TotalTrabajadoresAseguradosEnGruposDeEdadesHombres', intval($TotalTrabajadoresAseguradosEnGruposDeEdadesHombres));
            $totalesGruposDeEdades->addChild('TotalTrabajadoresAseguradosEnGruposDeEdadesMujeres', intval($TotalTrabajadoresAseguradosEnGruposDeEdadesMujeres));
            $totalesGruposDeEdades->addChild('TotalHorasTrabajadasEnGruposDeEdadesHombres', intval($totalHorasTrabajadasEnGruposDeEdadesHombres));
            $totalesGruposDeEdades->addChild('TotalHorasTrabajadasEnGruposDeEdadesMujeres', intval($totalHorasTrabajadasEnGruposDeEdadesMujeres));
            $totalesGruposDeEdades->addChild('TotalAccidentesLaboralesUnoMasDiasEnGruposDeEdadesHombres', intval($TotalAccidentesLaboralesUnoMasDiasEnGruposDeEdadesHombres));
            $totalesGruposDeEdades->addChild('TotalAccidentesLaboralesUnoMasDiasEnGruposDeEdadesMujeres', intval($TotalAccidentesLaboralesUnoMasDiasEnGruposDeEdadesMujeres));
            $totalesGruposDeEdades->addChild('TotalDiasDeIncapacidadUnoMasDiasEnGruposDeEdadesHombres', intval($TotalDiasDeIncapacidadUnoMasDiasEnGruposDeEdadesHombres));
            $totalesGruposDeEdades->addChild('TotalDiasDeIncapacidadUnoMasDiasEnGruposDeEdadesMujeres', intval($TotalDiasDeIncapacidadUnoMasDiasEnGruposDeEdadesMujeres));

            $totalesGruposOcupacionales = $totales->addChild('TotalesGruposOcupacionales');
            $totalesGruposOcupacionales->addChild('TotalAccidentesLaboralesEnGruposOcupacionalesHombres', intval($totalAccidentesLaboralesEnGruposOcupacionalesHombres));
            $totalesGruposOcupacionales->addChild('TotalAccidentesLaboralesEnGruposOcupacionalesMujeres', intval($totalAccidentesLaboralesEnGruposOcupacionalesMujeres));
            $totalesGruposOcupacionales->addChild('TotalTrabajadoresAseguradosEnGruposOcupacionalesHombres', intval($TotalTrabajadoresAseguradosEnProvinciasCantonesDistritosHombres));
            $totalesGruposOcupacionales->addChild('TotalTrabajadoresAseguradosEnGruposOcupacionalesMujeres', intval($TotalTrabajadoresAseguradosEnProvinciasCantonesDistritosMujeres));
            $totalesGruposOcupacionales->addChild('TotalHorasTrabajadasEnGruposOcupacionalesHombres', intval($totalHorasTrabajadasEnProvinciasCantonesDistritosHombres));
            $totalesGruposOcupacionales->addChild('TotalHorasTrabajadasEnGruposOcupacionalesMujeres', intval($totalHorasTrabajadasEnProvinciasCantonesDistritosMujeres));
            $totalesGruposOcupacionales->addChild('TotalAccidentesLaboralesUnoMasDiasEnGruposOcupacionalesHombres', intval($TotalAccidentesLaboralesUnoMasDiasEnGruposOcupacionalesHombres));
            $totalesGruposOcupacionales->addChild('TotalAccidentesLaboralesUnoMasDiasEnGruposOcupacionalesMujeres', intval($TotalAccidentesLaboralesUnoMasDiasEnGruposOcupacionalesMujeres));
            $totalesGruposOcupacionales->addChild('TotalDiasDeIncapacidadUnoMasDiasEnGruposOcupacionalesHombres', intval($TotalDiasDeIncapacidadUnoMasDiasEnGruposOcupacionalesHombres));
            $totalesGruposOcupacionales->addChild('TotalDiasDeIncapacidadUnoMasDiasEnGruposOcupacionalesMujeres', intval($TotalDiasDeIncapacidadUnoMasDiasEnGruposOcupacionalesMujeres));

            $totalesRamasDeActividades = $totales->addChild('TotalesRamasDeActividades');
            $totalesRamasDeActividades->addChild('TotalAccidentesLaboralesEnRamasDeActividadesHombres', intval($totalAccidentesLaboralesEnRamasDeActividadesHombres));
            $totalesRamasDeActividades->addChild('TotalAccidentesLaboralesEnRamasDeActividadesMujeres', intval($totalAccidentesLaboralesEnRamasDeActividadesMujeres));
            $totalesRamasDeActividades->addChild('TotalTrabajadoresAseguradosEnRamasDeActividadesHombres', intval($TotalTrabajadoresAseguradosEnRamasDeActividadesHombres));
            $totalesRamasDeActividades->addChild('TotalTrabajadoresAseguradosEnRamasDeActividadesMujeres', intval($TotalTrabajadoresAseguradosEnRamasDeActividadesMujeres));
            $totalesRamasDeActividades->addChild('TotalHorasTrabajadasEnRamasDeActividadesHombres', intval($totalHorasTrabajadasEnRamasDeActividadesHombres));
            $totalesRamasDeActividades->addChild('TotalHorasTrabajadasEnRamasDeActividadesMujeres', intval($totalHorasTrabajadasEnRamasDeActividadesMujeres));
            $totalesRamasDeActividades->addChild('TotalAccidentesLaboralesUnoMasDiasEnRamasDeActividadesHombres', intval($TotalAccidentesLaboralesUnoMasDiasEnRamasDeActividadesHombres));
            $totalesRamasDeActividades->addChild('TotalAccidentesLaboralesUnoMasDiasEnRamasDeActividadesMujeres', intval($TotalAccidentesLaboralesUnoMasDiasEnRamasDeActividadesMujeres));
            $totalesRamasDeActividades->addChild('TotalDiasDeIncapacidadUnoMasDiasEnRamasDeActividadesHombres', intval($TotalDiasDeIncapacidadUnoMasDiasEnRamasDeActividadesHombres));
            $totalesRamasDeActividades->addChild('TotalDiasDeIncapacidadUnoMasDiasEnRamasDeActividadesMujeres', intval($TotalDiasDeIncapacidadUnoMasDiasEnRamasDeActividadesMujeres));

            $totalesTamanosDeEmpresas = $totales->addChild('TotalesTamanosDeEmpresas');
            $totalesTamanosDeEmpresas->addChild('TotalAccidentesLaboralesEnTamanosDeEmpresasHombres', intval($totalAccidentesLaboralesEnTamanosDeEmpresasHombres));
            $totalesTamanosDeEmpresas->addChild('TotalAccidentesLaboralesEnTamanosDeEmpresasMujeres', intval($totalAccidentesLaboralesEnTamanosDeEmpresasMujeres));
            $totalesTamanosDeEmpresas->addChild('TotalTrabajadoresAseguradosEnTamanosDeEmpresasHombres', intval($TotalTrabajadoresAseguradosEnTamanosDeEmpresasHombres));
            $totalesTamanosDeEmpresas->addChild('TotalTrabajadoresAseguradosEnTamanosDeEmpresasMujeres', intval($TotalTrabajadoresAseguradosEnTamanosDeEmpresasMujeres));
            $totalesTamanosDeEmpresas->addChild('TotalHorasTrabajadasEnTamanosDeEmpresasHombres', intval($totalHorasTrabajadasEnTamanosDeEmpresasHombres));
            $totalesTamanosDeEmpresas->addChild('TotalHorasTrabajadasEnTamanosDeEmpresasMujeres', intval($totalHorasTrabajadasEnTamanosDeEmpresasMujeres));
            $totalesTamanosDeEmpresas->addChild('TotalAccidentesLaboralesUnoMasDiasEnTamanosDeEmpresasHombres', intval($TotalAccidentesLaboralesUnoMasDiasEnTamanosDeEmpresasHombres));
            $totalesTamanosDeEmpresas->addChild('TotalAccidentesLaboralesUnoMasDiasEnTamanosDeEmpresasMujeres', intval($TotalAccidentesLaboralesUnoMasDiasEnTamanosDeEmpresasMujeres));
            $totalesTamanosDeEmpresas->addChild('TotalDiasDeIncapacidadUnoMasDiasEnTamanosDeEmpresasHombres', intval($TotalDiasDeIncapacidadUnoMasDiasEnTamanosDeEmpresasHombres));
            $totalesTamanosDeEmpresas->addChild('TotalDiasDeIncapacidadUnoMasDiasEnTamanosDeEmpresasMujeres', intval($TotalDiasDeIncapacidadUnoMasDiasEnTamanosDeEmpresasMujeres));


            $totalAccidentesLaborales  = $totalAccidentesLaboralesEnProvinciasCantonesDistritosHombres;
            $totalAccidentesLaborales += $totalAccidentesLaboralesEnProvinciasCantonesDistritosMujeres;
            $totalAccidentesLaborales += $totalAccidentesLaboralesEnGruposDeEdadesHombres;
            $totalAccidentesLaborales += $totalAccidentesLaboralesEnGruposDeEdadesMujeres;
            $totalAccidentesLaborales += $totalAccidentesLaboralesEnGruposOcupacionalesHombres;
            $totalAccidentesLaborales += $totalAccidentesLaboralesEnGruposOcupacionalesMujeres;

            $totalHorasTrabajadas  = $totalHorasTrabajadasEnProvinciasCantonesDistritosHombres;
            $totalHorasTrabajadas += $totalHorasTrabajadasEnProvinciasCantonesDistritosMujeres;
            $totalHorasTrabajadas += $totalHorasTrabajadasEnGruposDeEdadesHombres;
            $totalHorasTrabajadas += $totalHorasTrabajadasEnGruposDeEdadesMujeres;
            $totalHorasTrabajadas += $totalHorasTrabajadasEnGruposOcupacionalesHombres;
            $totalHorasTrabajadas += $totalHorasTrabajadasEnGruposOcupacionalesMujeres;

            $query = " 
                        SELECT 
                                 SUM(CASE WHEN psa.gender IN ('F','M') THEN 1 ELSE 0 END) AS total_f,
                                 SUM(CASE WHEN psa.gender = 'M' THEN 1 ELSE 0 END) AS total_hombres,
                                 SUM(CASE WHEN psa.gender = 'F' THEN 1 ELSE 0 END) AS total_mujeres,
                                 SUM(CASE WHEN g.type_report = 'Accidente' and psa.gender = 'M' THEN 1 ELSE 0 END) AS cat_accidente_hombre,
                                 SUM(CASE WHEN g.type_report = 'Accidente' and psa.gender = 'F' THEN 1 ELSE 0 END) AS cat_accidente_mujer,
                                 SUM(CASE WHEN psa.gender = 'M' THEN (case when psa.hours IS not NULL then psa.hours else 0 end) ELSE 0 END) AS cat_horas_hombres,
                                 SUM(CASE WHEN psa.gender = 'F' THEN (case when psa.hours IS not NULL then psa.hours else 0 end) ELSE 0 END) AS cat_horas_mujer,
                                 SUM(CASE WHEN psa.gender = 'M' THEN 1 ELSE 0 END) AS cat_asegurado_hombres,
                                    SUM(CASE WHEN psa.gender = 'F' THEN 1 ELSE 0 END) as cat_asegurado_mujeres,
                                    SUM(CASE WHEN psa.gender = 'M' && pit.activity_gis IS NOT null THEN 1 ELSE 0 END) as cat_unomasdias_hombres,
                                    SUM(CASE WHEN psa.gender = 'F' && pit.activity_gis IS NOT null THEN 1 ELSE 0 END) as cat_unomasdias_mujeres,
                                    SUM(CASE WHEN psa.gender = 'M' && pit.days_it IS NOT null THEN pit.days_it  ELSE 0 END) as cat_dias_hombres,
                                    SUM(CASE WHEN psa.gender = 'F' && pit.days_it IS NOT null THEN pit.days_it ELSE 0 END) as cat_dias_mujeres
                         FROM gis_sort g
                         LEFT JOIN activities a ON a.id = g.activity_id
                         LEFT JOIN ( 
                                 SELECT p.identification_number, p.gender, p.hours, ROW_NUMBER() OVER (PARTITION BY identification_number, gender ORDER BY id DESC) AS rn
                                 FROM policy_spreadsheet_affiliates p
                               ) psa ON psa.identification_number = g.number_identification_affiliate AND psa.rn = 1
                         LEFT JOIN ( 								
                                            SELECT a.activity_id
                                            FROM activity_actions a 
                                            WHERE a.new_state_id = 160
                                            GROUP BY a.activity_id
                                        ) aa ON aa.activity_id=g.activity_id
                            LEFT JOIN ( 
                                            SELECT 
                                          a.service_id, -- PE - Subsidio por incapacidad temporal
                                          app.id AS activity_gis,
                                          SUM(p.days_it) AS days_it,
                                          YEAR(a.created_at) AS ano
                                        FROM activities a 
                                        LEFT JOIN activities ap ON ap.id = a.parent_id
                                        LEFT JOIN activities app ON app.id = ap.parent_id
                                        LEFT JOIN pe_it_sorts pit ON pit.activity_id=a.id
                                        LEFT JOIN peit_inability_sorts p ON p.pe_it_sort_id=pit.id
                                        WHERE a.service_id IN (84) AND a.state_id = 74  
                                        GROUP BY app.id 
                                    ) pit on pit.activity_gis = g.activity_id
                         WHERE aa.activity_id IS NOT NULL and a.deleted_at is null  AND YEAR(g.created_at) = '$xmlYear'
                         ";

            $resultGis = DB::selectOne($query);


            $totales->addAttribute('TotalAccidentesLaborales', intval($resultGis->total_f));
            $totales->addAttribute('TotalTrabajadoresAsegurados', intval($TotalTrabajadoresAsegurados));
            $totales->addAttribute('TotalHorasTrabajadas', intval($TotalHorasTrabajadas));
            $totales->addAttribute('TotalAccidentesLaboralesUnoMasDias', intval(($resultGis->cat_unomasdias_hombres+$resultGis->cat_unomasdias_mujeres)));
            $totales->addAttribute('TotalDiasDeIncapacidadUnoMasDias', intval(($resultGis->cat_dias_hombres+$resultGis->cat_dias_mujeres)));

            //Totales>


            $sectoresInstitucionales = $detalle->addChild('SectoresInstitucionales');
            //for
            $sectorInstitucional = $sectoresInstitucionales->addChild('SectorInstitucional');
            //ATRIBUTOS
            $sectorInstitucional->addAttribute('CodSectorInstitucional', 20);
            $sectorInstitucional->addAttribute('CantidadAccidentesLaboralesPorSectorInstitucional', intval($resultGis->total_f));
            $sectorInstitucional->addAttribute('CantidadTrabajadoresAseguradosPorSectorInstitucional', intval($TotalTrabajadoresAsegurados));
            $sectorInstitucional->addAttribute('CantidadHorasTrabajadasPorSectorInstitucional', intval($TotalHorasTrabajadas));
            $sectorInstitucional->addAttribute('CantidadAccidentesLaboralesUnoMasDiasPorSectorInstitucional', intval(($resultGis->cat_unomasdias_hombres+$resultGis->cat_unomasdias_mujeres)));
            $sectorInstitucional->addAttribute('CantidadDiasDeIncapacidadUnoMasDiasPorSectorInstitucional', intval(($resultGis->cat_dias_hombres+$resultGis->cat_dias_mujeres)));
            //ETIQUETAS

            $sectorInstitucional->addChild('CantidadAccidentesLaboralesPorSectorInstitucionalHombres', intval($resultGis->total_hombres));
            $sectorInstitucional->addChild('CantidadAccidentesLaboralesPorSectorInstitucionalMujeres', intval($resultGis->total_mujeres));
            $sectorInstitucional->addChild('CantidadTrabajadoresAseguradosPorSectorInstitucionalHombres', intval($TotalTrabajadoresAseguradosEnProvinciasCantonesDistritosHombres));
            $sectorInstitucional->addChild('CantidadTrabajadoresAseguradosPorSectorInstitucionalMujeres', intval($TotalTrabajadoresAseguradosEnProvinciasCantonesDistritosMujeres));
            $sectorInstitucional->addChild('CantidadHorasTrabajadasPorSectorInstitucionalHombres', intval($totalHorasTrabajadasEnProvinciasCantonesDistritosHombres));
            $sectorInstitucional->addChild('CantidadHorasTrabajadasPorSectorInstitucionalMujeres', intval($totalHorasTrabajadasEnProvinciasCantonesDistritosMujeres));
            $sectorInstitucional->addChild('CantidadAccidentesLaboralesUnoMasDiasPorSectorInstitucionalHombres', intval($resultGis->cat_unomasdias_hombres));
            $sectorInstitucional->addChild('CantidadAccidentesLaboralesUnoMasDiasPorSectorInstitucionalMujeres', intval($resultGis->cat_unomasdias_mujeres));
            $sectorInstitucional->addChild('CantidadDiasDeIncapacidadUnoMasDiasPorSectorInstitucionalHombres', intval($resultGis->cat_dias_hombres) );
            $sectorInstitucional->addChild('CantidadDiasDeIncapacidadUnoMasDiasPorSectorInstitucionalMujeres', intval($resultGis->cat_dias_mujeres));


            $totalesSectoresInstitucionales = $totales->addChild('TotalesSectoresInstitucionales');
            $totalesSectoresInstitucionales->addChild('TotalAccidentesLaboralesEnSectoresInstitucionalesHombres', intval($resultGis->total_hombres));
            $totalesSectoresInstitucionales->addChild('TotalAccidentesLaboralesEnSectoresInstitucionalesMujeres', intval($resultGis->total_mujeres));
            $totalesSectoresInstitucionales->addChild('TotalTrabajadoresAseguradosEnSectoresInstitucionalesHombres', intval($TotalTrabajadoresAseguradosEnProvinciasCantonesDistritosHombres));
            $totalesSectoresInstitucionales->addChild('TotalTrabajadoresAseguradosEnSectoresInstitucionalesMujeres', intval($TotalTrabajadoresAseguradosEnProvinciasCantonesDistritosMujeres));
            $totalesSectoresInstitucionales->addChild('TotalHorasTrabajadasEnSectoresInstitucionalesHombres', intval($totalHorasTrabajadasEnProvinciasCantonesDistritosHombres));
            $totalesSectoresInstitucionales->addChild('TotalHorasTrabajadasEnSectoresInstitucionalesMujeres', intval($totalHorasTrabajadasEnProvinciasCantonesDistritosMujeres));
            $totalesSectoresInstitucionales->addChild('TotalAccidentesLaboralesUnoMasDiasEnSectoresInstitucionalesHombres', intval($resultGis->cat_unomasdias_hombres));
            $totalesSectoresInstitucionales->addChild('TotalAccidentesLaboralesUnoMasDiasEnSectoresInstitucionalesMujeres', intval($resultGis->cat_unomasdias_mujeres));
            $totalesSectoresInstitucionales->addChild('TotalDiasDeIncapacidadUnoMasDiasEnSectoresInstitucionalesHombres', intval($resultGis->cat_dias_hombres));
            $totalesSectoresInstitucionales->addChild('TotalDiasDeIncapacidadUnoMasDiasEnSectoresInstitucionalesMujeres', intval($resultGis->cat_dias_mujeres));


            // Formatear el XML adecuadamente
            $dom = new \DOMDocument('1.0');
            $dom->preserveWhiteSpace = false;
            $dom->formatOutput = true;
            $dom->loadXML($xml->asXML());

            // Establecer headers y devolver el XML
            $fileName = 'ModeloAccidentesLaboralesRT'.'_'.$xmlYear. '.xml';
            return response($dom->saveXML(), 200)
                ->header('Content-Type', 'application/xml')
                ->header('Content-Disposition', 'attachment; filename="' . $fileName . '"');

        } catch (\Exception $e) {
            return redirect('/admin/reportes/descargas_admin')
                ->withErrors($e->getMessage())
                ->withInput();
        }
    }

    public function queryDetalle($xmlYear)
    {

        $query = "SELECT CONCAT(g.province, LPAD(g.canton, 2, '0'), LPAD(g.district, 2, '0') ) AS grupo,
                             SUM(CASE WHEN psa.gender IN ('F','M') THEN 1 ELSE 0 END) AS total_f,
                             SUM(CASE WHEN psa.gender = 'M' THEN 1 ELSE 0 END) AS total_hombres,
                             SUM(CASE WHEN psa.gender = 'F' THEN 1 ELSE 0 END) AS total_mujeres,
                             SUM(CASE WHEN g.type_report = 'Accidente' and psa.gender = 'M' THEN 1 ELSE 0 END) AS cat_accidente_hombre,
                             SUM(CASE WHEN g.type_report = 'Accidente' and psa.gender = 'F' THEN 1 ELSE 0 END) AS cat_accidente_mujer,
                             SUM(CASE WHEN psa.gender = 'M' THEN (case when psa.hours IS not NULL then psa.hours else 0 end) ELSE 0 END) AS cat_horas_hombres,
                             SUM(CASE WHEN psa.gender = 'F' THEN (case when psa.hours IS not NULL then psa.hours else 0 end) ELSE 0 END) AS cat_horas_mujer,
                             YEAR(g.created_at) AS ano,
                              'ProvinciasCantonesDistritos' AS type
                     FROM gis_sort g
                     LEFT JOIN ( 
                             SELECT p.identification_number, p.gender, p.hours, ROW_NUMBER() OVER (PARTITION BY identification_number, gender ORDER BY id DESC) AS rn
                             FROM policy_spreadsheet_affiliates p
                           ) psa ON psa.identification_number = g.number_identification_affiliate AND psa.rn = 1
                     LEFT JOIN ( 								
										SELECT a.activity_id
										FROM activity_actions a 
										WHERE a.new_state_id = 160
										GROUP BY a.activity_id
									) aa ON aa.activity_id=g.activity_id
                     WHERE aa.activity_id IS NOT NULL AND  YEAR(g.created_at) = '2025'
                    GROUP BY CONCAT(g.province, LPAD(g.canton, 2, '0'), LPAD(g.district, 2, '0') )
                    UNION ALL
                    SELECT CASE 
                               WHEN psa.age < 15 THEN 1
                               WHEN psa.age >= 15 AND psa.age < 18 THEN 15
                               WHEN psa.age >= 18 AND psa.age < 20 THEN 18
                               WHEN psa.age >= 20 AND psa.age < 25 THEN 20
                               WHEN psa.age >= 25 AND psa.age < 30 THEN 25
                               WHEN psa.age >= 30 AND psa.age < 35 THEN 30
                               WHEN psa.age >= 35 AND psa.age < 40 THEN 35
                               WHEN psa.age >= 40 AND psa.age < 45 THEN 40
                               WHEN psa.age >= 45 AND psa.age < 50 THEN 45
                               WHEN psa.age >= 50 AND psa.age < 55 THEN 50
                               WHEN psa.age >= 55 AND psa.age < 60 THEN 55
                               WHEN psa.age >= 60 AND psa.age < 65 THEN 60
                               WHEN psa.age >= 65 THEN 65
                               ELSE 99 
                           END AS grupo,
                           SUM(CASE WHEN psa.gender IN ('F','M') THEN 1 ELSE 0 END) AS total_f,
                           SUM(CASE WHEN psa.gender = 'M' THEN 1 ELSE 0 END) AS total_hombres,
                           SUM(CASE WHEN psa.gender = 'F' THEN 1 ELSE 0 END) AS total_mujeres,
                           SUM(CASE WHEN g.type_report = 'Accidente' and psa.gender = 'M' THEN 1 ELSE 0 END) AS cat_accidente_hombre,
                           SUM(CASE WHEN g.type_report = 'Accidente' and psa.gender = 'F' THEN 1 ELSE 0 END) AS cat_accidente_mujer,
                           SUM(CASE WHEN psa.gender = 'M' THEN (case when psa.hours IS not NULL then psa.hours else 0 end) ELSE 0 END) AS cat_horas_hombres,
                           SUM(CASE WHEN psa.gender = 'F' THEN (case when psa.hours IS not NULL then psa.hours else 0 end) ELSE 0 END) AS cat_horas_mujer,
                           YEAR(g.created_at) AS ano,
                            'GruposDeEdades' AS type
                    FROM gis_sort g
                    LEFT JOIN ( 
                           SELECT p.identification_number, p.gender, p.hours, TIMESTAMPDIFF(YEAR, p.date_of_birth, CURDATE()) AS age, ROW_NUMBER() OVER (PARTITION BY identification_number, gender ORDER BY id DESC) AS rn
                           FROM policy_spreadsheet_affiliates p
                           WHERE p.date_of_birth IS NOT NULL 
                         ) psa ON psa.identification_number = g.number_identification_affiliate AND psa.rn = 1
                      LEFT JOIN ( 								
										SELECT a.activity_id
										FROM activity_actions a 
										WHERE a.new_state_id = 160
										GROUP BY a.activity_id
									) aa ON aa.activity_id=g.activity_id
                    WHERE aa.activity_id IS NOT NULL AND YEAR(g.created_at) = '$xmlYear'
                    GROUP BY (CASE 
                                   WHEN psa.age < 15 THEN 1
                                   WHEN psa.age >= 15 AND psa.age < 18 THEN 15
                                   WHEN psa.age >= 18 AND psa.age < 20 THEN 18
                                   WHEN psa.age >= 20 AND psa.age < 25 THEN 20
                                   WHEN psa.age >= 25 AND psa.age < 30 THEN 25
                                   WHEN psa.age >= 30 AND psa.age < 35 THEN 30
                                   WHEN psa.age >= 35 AND psa.age < 40 THEN 35
                                   WHEN psa.age >= 40 AND psa.age < 45 THEN 40
                                   WHEN psa.age >= 45 AND psa.age < 50 THEN 45
                                   WHEN psa.age >= 50 AND psa.age < 55 THEN 50
                                   WHEN psa.age >= 55 AND psa.age < 60 THEN 55
                                   WHEN psa.age >= 60 AND psa.age < 65 THEN 60
                                   WHEN psa.age >= 65 THEN 65
                                   ELSE 99 
                               END )
                     UNION all
                     SELECT case when oc.group_id IS NULL then 0 ELSE oc.group_id END AS grupo,
                            SUM(CASE WHEN psa.gender IN ('F','M') THEN 1 ELSE 0 END) AS total_f,
                            SUM(CASE WHEN psa.gender = 'M' THEN 1 ELSE 0 END) AS total_hombres,
                            SUM(CASE WHEN psa.gender = 'F' THEN 1 ELSE 0 END) AS total_mujeres,
                            SUM(CASE WHEN g.type_report = 'Accidente' and psa.gender = 'M' THEN 1 ELSE 0 END) AS cat_accidente_hombre,
                            SUM(CASE WHEN g.type_report = 'Accidente' and psa.gender = 'F' THEN 1 ELSE 0 END) AS cat_accidente_mujer,
                            SUM(CASE WHEN psa.gender = 'M' THEN (case when psa.hours IS not NULL then psa.hours else 0 end) ELSE 0 END) AS cat_horas_hombres,
                            SUM(CASE WHEN psa.gender = 'F' THEN (case when psa.hours IS not NULL then psa.hours else 0 end) ELSE 0 END) AS cat_horas_mujer,
                            YEAR(g.created_at) AS ano,
                            'GruposOcupacionales' AS type
                    FROM gis_sort g
                    LEFT JOIN activities a ON a.id = g.activity_id
                    LEFT JOIN activities ap ON ap.id = a.parent_id
                    LEFT JOIN policy_sorts ps ON ps.activity_id = ap.id
                    LEFT JOIN economic_activities ea ON ea.code = ps.activity_economic_id
                    LEFT JOIN occupations oc ON oc.id= g.occupancy_group
                    LEFT JOIN ( 
                            SELECT p.identification_number, p.gender, p.hours, ROW_NUMBER() OVER (PARTITION BY identification_number, gender ORDER BY id DESC) AS rn
                            FROM policy_spreadsheet_affiliates p
                          ) psa ON psa.identification_number = g.number_identification_affiliate AND psa.rn = 1
                    LEFT JOIN ( 								
										SELECT a.activity_id
										FROM activity_actions a 
										WHERE a.new_state_id = 160
										GROUP BY a.activity_id
									) aa ON aa.activity_id=g.activity_id
                    WHERE aa.activity_id IS NOT NULL AND YEAR(g.created_at) = '$xmlYear'
                    GROUP BY oc.group_id
                    UNION ALL
                      SELECT ea.group_economic AS grupo,
                           SUM(CASE WHEN psa.gender IN ('F','M') THEN 1 ELSE 0 END) AS total_f,
                           SUM(CASE WHEN psa.gender = 'M' THEN 1 ELSE 0 END) AS total_hombres,
                           SUM(CASE WHEN psa.gender = 'F' THEN 1 ELSE 0 END) AS total_mujeres,
                           SUM(CASE WHEN g.type_report = 'Accidente' and psa.gender = 'M' THEN 1 ELSE 0 END) AS cat_accidente_hombre,
                           SUM(CASE WHEN g.type_report = 'Accidente' and psa.gender = 'F' THEN 1 ELSE 0 END) AS cat_accidente_mujer,
                           SUM(CASE WHEN psa.gender = 'M' THEN (case when psa.hours IS not NULL then psa.hours else 0 end) ELSE 0 END) AS cat_horas_hombres,
                           SUM(CASE WHEN psa.gender = 'F' THEN (case when psa.hours IS not NULL then psa.hours else 0 end) ELSE 0 END) AS cat_horas_mujer,
                           YEAR(g.created_at) AS ano,
                           'RamasDeActividades' AS type
                    FROM gis_sort g
                    LEFT JOIN activities a ON a.id = g.activity_id
                    LEFT JOIN activities ap ON ap.id = a.parent_id
                    LEFT JOIN policy_sorts ps ON ps.activity_id = ap.id
                    LEFT JOIN economic_activities ea ON ea.code = ps.activity_economic_id
                    LEFT JOIN ( 
                           SELECT p.identification_number, p.gender, p.hours, ROW_NUMBER() OVER (PARTITION BY identification_number, gender ORDER BY id DESC) AS rn
                           FROM policy_spreadsheet_affiliates p
                         ) psa ON psa.identification_number = g.number_identification_affiliate AND psa.rn = 1
                     LEFT JOIN ( 								
										SELECT a.activity_id
										FROM activity_actions a 
										WHERE a.new_state_id = 160
										GROUP BY a.activity_id
									) aa ON aa.activity_id=g.activity_id
                    WHERE aa.activity_id IS NOT NULL AND ea.group_economic is not null AND YEAR(g.created_at) = '$xmlYear'
                    GROUP BY ea.group_economic 
                    UNION ALL
                    SELECT 
                            CASE 
                               WHEN pro_afiliado = 1 THEN 1
                               WHEN pro_afiliado = 2 THEN 2
                               WHEN pro_afiliado = 3 THEN 3
                               WHEN pro_afiliado = 4 THEN 4
                               WHEN pro_afiliado = 5 THEN 5
                               WHEN pro_afiliado >= 6 AND pro_afiliado <= 9 THEN 6
                               WHEN pro_afiliado >= 10 AND pro_afiliado <= 19 THEN 10
                               WHEN pro_afiliado >= 20 AND pro_afiliado <= 29 THEN 20
                               WHEN pro_afiliado >= 30 AND pro_afiliado <= 39 THEN 30
                               WHEN pro_afiliado >= 40 AND pro_afiliado <= 49 THEN 40
                               WHEN pro_afiliado >= 50 AND pro_afiliado <= 99 THEN 50
                               WHEN pro_afiliado >= 100 AND pro_afiliado <= 149 THEN 100
                               WHEN pro_afiliado >= 150 AND pro_afiliado <= 199 THEN 150
                               WHEN pro_afiliado >= 200 AND pro_afiliado <= 249 THEN 200
                               WHEN pro_afiliado >= 250 AND pro_afiliado <= 499 THEN 250
                               WHEN pro_afiliado >= 500 AND pro_afiliado <= 999 THEN 500
                               WHEN pro_afiliado >= 1000 THEN 1000
                               ELSE 9999
                           END AS grupo,
                           SUM(CASE WHEN psa.gender IN ('F','M') THEN 1 ELSE 0 END) AS total_f,
                           SUM(CASE WHEN psa.gender = 'M' THEN 1 ELSE 0 END) AS total_hombres,
                           SUM(CASE WHEN psa.gender = 'F' THEN 1 ELSE 0 END) AS total_mujeres,
                           SUM(CASE WHEN g.type_report = 'Accidente' and psa.gender = 'M' THEN 1 ELSE 0 END) AS cat_accidente_hombre,
                           SUM(CASE WHEN g.type_report = 'Accidente' and psa.gender = 'F' THEN 1 ELSE 0 END) AS cat_accidente_mujer,
                           SUM(CASE WHEN psa.gender = 'M' THEN (case when psa.hours IS not NULL then psa.hours else 0 end) ELSE 0 END) AS cat_horas_hombres,
                           SUM(CASE WHEN psa.gender = 'F' THEN (case when psa.hours IS not NULL then psa.hours else 0 end) ELSE 0 END) AS cat_horas_mujer,
                           YEAR(g.created_at) AS ano,
                           'TamanosDeEmpresas' AS type
                    FROM gis_sort g
                    LEFT JOIN activities a ON a.id = g.activity_id
                    LEFT JOIN activities ap ON ap.id = a.parent_id
                    LEFT JOIN policy_sorts ps ON ps.activity_id = ap.id
                    LEFT JOIN ( 
                           SELECT p.identification_number, p.gender, p.hours, ROW_NUMBER() OVER (PARTITION BY identification_number, gender ORDER BY id DESC) AS rn
                           FROM policy_spreadsheet_affiliates p
                         ) psa ON psa.identification_number = g.number_identification_affiliate AND psa.rn = 1
                    LEFT JOIN (
                                    SELECT a.parent_id , ROUND(SUM(p.total_affiliates)/COUNT(*)) AS pro_afiliado
                                    FROM policy_spreadsheets p 
                                    LEFT JOIN activities a ON a.id = p.activity_id
                                    WHERE p.total_affiliates IS NOT NULL AND p.total_affiliates <> 0 AND a.parent_id IS NOT NULL 
                                    GROUP BY a.parent_id
                            ) pss ON pss.parent_id = ps.activity_id
                   LEFT JOIN ( 								
										SELECT a.activity_id
										FROM activity_actions a 
										WHERE a.new_state_id = 160
										GROUP BY a.activity_id
									) aa ON aa.activity_id=g.activity_id
                    WHERE aa.activity_id IS NOT NULL AND pss.pro_afiliado is not null AND YEAR(g.created_at) = '$xmlYear'
                    GROUP BY (CASE 
                               WHEN pro_afiliado = 1 THEN 1
                               WHEN pro_afiliado = 2 THEN 2
                               WHEN pro_afiliado = 3 THEN 3
                               WHEN pro_afiliado = 4 THEN 4
                               WHEN pro_afiliado = 5 THEN 5
                               WHEN pro_afiliado >= 6 AND pro_afiliado <= 9 THEN 6
                               WHEN pro_afiliado >= 10 AND pro_afiliado <= 19 THEN 10
                               WHEN pro_afiliado >= 20 AND pro_afiliado <= 29 THEN 20
                               WHEN pro_afiliado >= 30 AND pro_afiliado <= 39 THEN 30
                               WHEN pro_afiliado >= 40 AND pro_afiliado <= 49 THEN 40
                               WHEN pro_afiliado >= 50 AND pro_afiliado <= 99 THEN 50
                               WHEN pro_afiliado >= 100 AND pro_afiliado <= 149 THEN 100
                               WHEN pro_afiliado >= 150 AND pro_afiliado <= 199 THEN 150
                               WHEN pro_afiliado >= 200 AND pro_afiliado <= 249 THEN 200
                               WHEN pro_afiliado >= 250 AND pro_afiliado <= 499 THEN 250
                               WHEN pro_afiliado >= 500 AND pro_afiliado <= 999 THEN 500
                               WHEN pro_afiliado >= 1000 THEN 1000
                               ELSE 9999
                           END )";

        $query = "select * 
                  from report_xml_two
                  where ano = '$xmlYear'
                  order by id ";

        $data = DB::select($query);

        return $data;

    }

    //ME-1321 - Requerimiento 40: parte 3 Generación de modelos XML 15 SUGESE
    public function reportXmlThree(Request $req)
    {
        try {

            $xmlYear = $req->xml_three_year;
            $periodo = $req->periodo ?? date('Y-m-d');

            if (empty($xmlYear)) {
                throw new \Exception("El campo año no puede estar vacío, en la sección 'MODELO XML 15'");
            }

            // Generar el XML
            $xml = new \SimpleXMLElement('<?xml version="1.0" encoding="utf-8"?>
            <ModeloEnfermedadesProfesionalesRT  
                xmlns:xsd="http://www.w3.org/2001/XMLSchema" 
                xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            </ModeloEnfermedadesProfesionalesRT >');

            // Agregar encabezado
            $encabezado = $xml->addChild('Encabezado');
            $encabezado->addChild('NombreArchivo', 'ModeloEnfermedadesProfesionalesRT');
            $encabezado->addChild('Fecha', date('Y-m-d'));
            $encabezado->addChild('Periodo', $xmlYear.'-12-31');
            $encabezado->addChild('Periodicidad', 'A');
            $encabezado->addChild('Moneda', '1');

            // Agregar datos
            $datos = $xml->addChild('Datos');
            $modelo = $datos->addChild('Modelo');
            $modelo->addAttribute('EntidadFuente', 'A13');

            $enfermedadesProfesionales = $modelo->addChild('EnfermedadesProfesionales');

            $query = " SELECT SUM(1) AS total,
                                SUM(total_hombres) AS total_hombres,
                                SUM(total_mujeres) AS total_mujeres
                        FROM (
                            SELECT grupo,
                                 SUM(total) AS total,
                                 SUM(total_hombres) AS total_hombres,
                                 SUM(total_mujeres) AS total_mujeres,
                                 ano,
                                 type,
                                 id
                         FROM report_xml_three
                         WHERE ano = '$xmlYear'
                         GROUP BY grupo,ano,type,id
                         ORDER BY id, grupo ) t";

            $resultTotal = DB::selectOne($query);

            $totalesGruposOcupacionales = $enfermedadesProfesionales->addChild('TotalesGruposOcupacionales');
            $totalesGruposOcupacionales->addAttribute('TotalDeGruposOcupacionales', intval($resultTotal->total ?? 0));
            $totalesGruposOcupacionales->addAttribute('TotalDeGruposOcupacionalesHombres', intval($resultTotal->total_hombres ?? 0));
            $totalesGruposOcupacionales->addAttribute('TotalDeGruposOcupacionalesMujeres', intval($resultTotal->total_mujeres ?? 0));

               $query = "  SELECT grupo, 
                                    SUM(total) AS total, 
                                    SUM(total_hombres) AS total_hombres,
                                    SUM(total_mujeres) AS total_mujeres,	
                                    ano,
                                    type,
                                    id	
                            FROM report_xml_three 
                            WHERE ano = '$xmlYear'
                            GROUP BY grupo,ano,type,id	
                            ORDER BY id, grupo";

            $data = DB::select($query);

            $totalDeRamasDeActividades = 0;
            $totalDeRamasDeActividadesHombres = 0;
            $totalDeRamasDeActividadesMujeres = 0;
            $totalDeEnfermedadesProfesionales = 0;
            $totalDeEnfermedadesProfesionalesHombres = 0;
            $totalDeEnfermedadesProfesionalesMujeres = 0;
            $totalDeGruposDeEdades = 0;
            $totalDeGruposDeEdadesHombres = 0;
            $totalDeGruposDeEdadesMujeres = 0;
            $totalDeTamanosDeEmpresa = 0;
            $totalDeTamanosDeEmpresasHombres = 0;
            $totalDeTamanosDeEmpresasMujeres = 0;

            $totalesRamasDeActividades = $totalesGruposOcupacionales->addChild('TotalesRamasDeActividades');
            $totalesEnfermedadesProfesionales  = $totalesGruposOcupacionales->addChild('TotalesEnfermedadesProfesionales');
            $totalesGruposDeEdades = $totalesGruposOcupacionales->addChild('TotalesGruposDeEdades');
            $totalesTamanosDeEmpresas = $totalesGruposOcupacionales->addChild('TotalesTamanosDeEmpresas');

            foreach ($data as $row) {
                if ($row->type == 'GruposOcupacionales') {

                    $totalRamaDeActividad = $totalesRamasDeActividades->addChild('TotalRamaDeActividad');
                    $totalRamaDeActividad->addAttribute('CodRamaActividad', $row->grupo);
                    $totalRamaDeActividad->addAttribute('TotalPorRamaDeActividad', intval($row->total));
                    $totalRamaDeActividad->addChild('TotalPorRamaDeActividadHombres', intval($row->total_hombres));
                    $totalRamaDeActividad->addChild('TotalPorRamaDeActividadMujeres', intval($row->total_mujeres));

                    $totalDeRamasDeActividades += $row->total ?? 0;
                    $totalDeRamasDeActividadesHombres += $row->total_hombres ?? 0;
                    $totalDeRamasDeActividadesMujeres += $row->total_mujeres ?? 0;

                } else if ($row->type == 'EnfermedadProfesional') {

                    $totalEnfermedadProfesional = $totalesEnfermedadesProfesionales->addChild('TotalEnfermedadProfesional');
                    $totalEnfermedadProfesional->addAttribute('CodEnfermedadProfesional', $row->grupo);
                    $totalEnfermedadProfesional->addAttribute('TotalPorEnfermedadProfesional', intval($row->total));
                    $totalEnfermedadProfesional->addChild('TotalPorEnfermedadProfesionalHombres', intval($row->total_hombres));
                    $totalEnfermedadProfesional->addChild('TotalPorEnfermedadProfesionalMujeres', intval($row->total_mujeres));

                    $totalDeEnfermedadesProfesionales += $row->total ?? 0;
                    $totalDeEnfermedadesProfesionalesHombres += $row->total_hombres ?? 0;
                    $totalDeEnfermedadesProfesionalesMujeres += $row->total_mujeres ?? 0;

                } else if ($row->type == 'GruposDeEdades') {

                    $totalGrupoDeEdad = $totalesGruposDeEdades->addChild('TotalGrupoDeEdad');
                    $totalGrupoDeEdad->addAttribute('CodGrupoDeEdad', $row->grupo);
                    $totalGrupoDeEdad->addAttribute('TotalPorGrupoDeEdad', intval($row->total));
                    $totalGrupoDeEdad->addChild('TotalPorGrupoDeEdadHombres', intval($row->total_hombres));
                    $totalGrupoDeEdad->addChild('TotalPorGrupoDeEdadHombres', intval($row->total_mujeres));

                    $totalDeGruposDeEdades += $row->total ?? 0;
                    $totalDeGruposDeEdadesHombres += $row->total_hombres ?? 0;
                    $totalDeGruposDeEdadesMujeres += $row->total_mujeres ?? 0;

                } else if ($row->type == 'TamanosDeEmpresas') {

                    $totalTamanoDeEmpresa = $totalesTamanosDeEmpresas->addChild('TotalTamanoDeEmpresa');
                    $totalTamanoDeEmpresa->addAttribute('CodTamanoEmpresa', $row->grupo);
                    $totalTamanoDeEmpresa->addAttribute('TotalPorTamanoEmpresa', intval($row->total));
                    $totalTamanoDeEmpresa->addChild('TotalPorTamanoEmpresaHombres', intval($row->total_hombres));
                    $totalTamanoDeEmpresa->addChild('TotalPorTamanoEmpresaMujeres', intval($row->total_mujeres));

                    $totalDeTamanosDeEmpresa += $row->total ?? 0;
                    $totalDeTamanosDeEmpresasHombres += $row->total_hombres ?? 0;
                    $totalDeTamanosDeEmpresasMujeres += $row->total_mujeres ?? 0;
                }

            }

            $totalesRamasDeActividades->addAttribute('TotalDeRamasDeActividades', intval($totalDeRamasDeActividades));
            $totalesRamasDeActividades->addAttribute('TotalDeRamasDeActividadesHombres', intval($totalDeRamasDeActividadesHombres));
            $totalesRamasDeActividades->addAttribute('TotalDeRamasDeActividadesMujeres', intval($totalDeRamasDeActividadesMujeres));

            $totalesEnfermedadesProfesionales->addAttribute('TotalDeEnfermedadesProfesionales', intval($totalDeEnfermedadesProfesionales));
            $totalesEnfermedadesProfesionales->addAttribute('TotalDeEnfermedadesProfesionalesHombres', intval($totalDeEnfermedadesProfesionalesHombres));
            $totalesEnfermedadesProfesionales->addAttribute('TotalDeEnfermedadesProfesionalesMujeres', intval($totalDeEnfermedadesProfesionalesMujeres));

            $totalesGruposDeEdades->addAttribute('TotalDeGruposDeEdades', intval($totalDeGruposDeEdades));
            $totalesGruposDeEdades->addAttribute('TotalDeGruposDeEdadesHombres', intval($totalDeGruposDeEdadesHombres));
            $totalesGruposDeEdades->addAttribute('TotalDeGruposDeEdadesMujeres', intval($totalDeGruposDeEdadesMujeres));

            $totalesTamanosDeEmpresas->addAttribute('TotalDeTamanosDeEmpresa', intval($totalDeTamanosDeEmpresa));
            $totalesTamanosDeEmpresas->addAttribute('TotalDeTamanosDeEmpresasHombres', intval($totalDeTamanosDeEmpresasHombres));
            $totalesTamanosDeEmpresas->addAttribute('TotalDeTamanosDeEmpresasMujeres', intval($totalDeTamanosDeEmpresasMujeres));

            $gruposOcupacionales = $enfermedadesProfesionales->addChild('GruposOcupacionales');
            $grupoOcupacional = $gruposOcupacionales->addChild('GrupoOcupacional');

            $data = $this->queryXmlThree($xmlYear);

            $codGrupoOcupacion = '';
            $subTotalPorGrupoOcupacional = 0;
            $subTotalPorGrupoOcupacionalHombres = 0;
            $subTotalPorGrupoOcupacionalMujeres = 0;

            foreach ($data as $row) {

                if ($row->grupo_ocupacion != $codGrupoOcupacion) {

                    if (!empty($codGrupoOcupacion)) {
                        $grupoOcupacional->addAttribute('CodGrupoOcupacional', $codGrupoOcupacion);
                        $grupoOcupacional->addAttribute('SubTotalPorGrupoOcupacional', intval($subTotalPorGrupoOcupacional));
                        $grupoOcupacional->addAttribute('SubTotalPorGrupoOcupacionalHombres', intval($subTotalPorGrupoOcupacionalHombres));
                        $grupoOcupacional->addAttribute('SubTotalPorGrupoOcupacionalMujeres', intval($subTotalPorGrupoOcupacionalMujeres));

                        $subTotalPorGrupoOcupacional = 0;
                        $subTotalPorGrupoOcupacionalHombres = 0;
                        $subTotalPorGrupoOcupacionalMujeres = 0;

                        $grupoOcupacional = $gruposOcupacionales->addChild('GrupoOcupacional');
                    }

                    $codGrupoOcupacion=$row->grupo_ocupacion;

                    $ramasDeActividadesPorGrupoOcupacional = $grupoOcupacional->addChild('RamasDeActividadesPorGrupoOcupacional');
                    $enfermedadesProfesionalesPorGrupoOcupacional = $grupoOcupacional->addChild('EnfermedadesProfesionalesPorGrupoOcupacional');
                    $gruposDeEdadesPorGrupoOcupacional = $grupoOcupacional->addChild('GruposDeEdadesPorGrupoOcupacional');
                    $tamanosDeEmpresasPorGrupoOcupacional = $grupoOcupacional->addChild('TamanosDeEmpresasPorGrupoOcupacional');

                }

                if ($row->type == 'GruposOcupacionales') {
                    $ramaDeActividad = $ramasDeActividadesPorGrupoOcupacional->addChild('RamaDeActividad');
                    $ramaDeActividad->addAttribute('CodRamaActividad', $row->grupo);
                    $ramaDeActividad->addAttribute('CantidadPorRamaDeActividadEnGrupoOcupacional', intval($row->total));
                    $ramaDeActividad->addChild('CantidadPorRamaActividadEnGrupoOcupacionalHombres', intval($row->total_hombres));
                    $ramaDeActividad->addChild('CantidadPorRamaActividadEnGrupoOcupacionalMujeres', intval($row->total_mujeres));
                } else if ($row->type == 'EnfermedadProfesional') {
                    $enfermedadProfesional = $enfermedadesProfesionalesPorGrupoOcupacional->addChild('EnfermedadProfesional');
                    $enfermedadProfesional->addAttribute('CodEnfermedadProfesional', 0);
                    $enfermedadProfesional->addAttribute('CantidadPorEnfermedadProfesionalEnGrupoOcupacional', 0);
                    $enfermedadProfesional->addChild('CantidadPorEnfermedadProfesionalHombres', 0);
                    $enfermedadProfesional->addChild('CantidadPorEnfermedadProfesionalMujeres', 0);
                } else if ($row->type == 'GruposDeEdades') {
                    $GrupoDeEdad = $gruposDeEdadesPorGrupoOcupacional->addChild('GrupoDeEdad');
                    $GrupoDeEdad->addAttribute('CodGrupoDeEdad', $row->grupo);
                    $GrupoDeEdad->addAttribute('CantidadPorGrupoDeEdadEnGrupoOcupacional', intval($row->total));
                    $GrupoDeEdad->addChild('CantidaPorGrupoDeEdadHombres', intval($row->total_hombres));
                    $GrupoDeEdad->addChild('CantidaPorGrupoDeEdadMujeres', intval($row->total_mujeres));
                } else if ($row->type == 'TamanosDeEmpresas') {
                    $TamanoDeEmpresa = $tamanosDeEmpresasPorGrupoOcupacional->addChild('TamanoDeEmpresa');
                    $TamanoDeEmpresa->addAttribute('CodTamanoEmpresa', $row->grupo);
                    $TamanoDeEmpresa->addAttribute('CantidadPorTamanoDeEmpresaEnGrupoOcupacional', intval($row->total));
                    $TamanoDeEmpresa->addChild('CantidadEnTamanoDeEmpresaHombres', intval($row->total_hombres));
                    $TamanoDeEmpresa->addChild('CantidadEnTamanoDeEmpresaMujeres', intval($row->total_mujeres));
                }

                $subTotalPorGrupoOcupacional += $row->total;
                $subTotalPorGrupoOcupacionalHombres += $row->total_hombres;
                $subTotalPorGrupoOcupacionalMujeres += $row->total_mujeres;

            }

            if (!empty($codGrupoOcupacion)) {
                $grupoOcupacional->addAttribute('CodGrupoOcupacional', $codGrupoOcupacion);
                $grupoOcupacional->addAttribute('SubTotalPorGrupoOcupacional', intval($subTotalPorGrupoOcupacional));
                $grupoOcupacional->addAttribute('SubTotalPorGrupoOcupacionalHombres', intval($subTotalPorGrupoOcupacionalHombres));
                $grupoOcupacional->addAttribute('SubTotalPorGrupoOcupacionalMujeres', intval($subTotalPorGrupoOcupacionalMujeres));
            }

            //esta informacion se agrega ya que para el 2024 no trae nada TotalesRamasDeActividades
            if (!$data) {
                $grupoOcupacional->addAttribute('CodGrupoOcupacional', '8189');
                $grupoOcupacional->addAttribute('SubTotalPorGrupoOcupacional', 0);
                $grupoOcupacional->addAttribute('SubTotalPorGrupoOcupacionalHombres', 0);
                $grupoOcupacional->addAttribute('SubTotalPorGrupoOcupacionalMujeres', 0);

                $ramasDeActividadesPorGrupoOcupacional = $grupoOcupacional->addChild('RamasDeActividadesPorGrupoOcupacional');
                $enfermedadesProfesionalesPorGrupoOcupacional = $grupoOcupacional->addChild('EnfermedadesProfesionalesPorGrupoOcupacional');
                $gruposDeEdadesPorGrupoOcupacional = $grupoOcupacional->addChild('GruposDeEdadesPorGrupoOcupacional');
                $tamanosDeEmpresasPorGrupoOcupacional = $grupoOcupacional->addChild('TamanosDeEmpresasPorGrupoOcupacional');


                $ramaDeActividad = $ramasDeActividadesPorGrupoOcupacional->addChild('RamaDeActividad');
                $ramaDeActividad->addAttribute('CodRamaActividad', '9810');
                $ramaDeActividad->addAttribute('CantidadPorRamaDeActividadEnGrupoOcupacional', 0);
                $ramaDeActividad->addChild('CantidadPorRamaActividadEnGrupoOcupacionalHombres', 0);
                $ramaDeActividad->addChild('CantidadPorRamaActividadEnGrupoOcupacionalMujeres', 0);

                $enfermedadProfesional = $enfermedadesProfesionalesPorGrupoOcupacional->addChild('EnfermedadProfesional');
                $enfermedadProfesional->addAttribute('CodEnfermedadProfesional', '1101');
                $enfermedadProfesional->addAttribute('CantidadPorEnfermedadProfesionalEnGrupoOcupacional', 0);
                $enfermedadProfesional->addChild('CantidadPorEnfermedadProfesionalHombres', 0);
                $enfermedadProfesional->addChild('CantidadPorEnfermedadProfesionalMujeres', 0);

                $GrupoDeEdad = $gruposDeEdadesPorGrupoOcupacional->addChild('GrupoDeEdad');
                $GrupoDeEdad->addAttribute('CodGrupoDeEdad', '15');
                $GrupoDeEdad->addAttribute('CantidadPorGrupoDeEdadEnGrupoOcupacional', 0);
                $GrupoDeEdad->addChild('CantidaPorGrupoDeEdadHombres', 0);
                $GrupoDeEdad->addChild('CantidaPorGrupoDeEdadMujeres', 0);

                $TamanoDeEmpresa = $tamanosDeEmpresasPorGrupoOcupacional->addChild('TamanoDeEmpresa');
                $TamanoDeEmpresa->addAttribute('CodTamanoEmpresa', '1');
                $TamanoDeEmpresa->addAttribute('CantidadPorTamanoDeEmpresaEnGrupoOcupacional', 0);
                $TamanoDeEmpresa->addChild('CantidadEnTamanoDeEmpresaHombres', 0);
                $TamanoDeEmpresa->addChild('CantidadEnTamanoDeEmpresaMujeres', 0);


                //--------totales

                $totalRamaDeActividad = $totalesRamasDeActividades->addChild('TotalRamaDeActividad');
                $totalRamaDeActividad->addAttribute('CodRamaActividad', '9810');
                $totalRamaDeActividad->addAttribute('TotalPorRamaDeActividad', 0);
                $totalRamaDeActividad->addChild('TotalPorRamaDeActividadHombres', 0);
                $totalRamaDeActividad->addChild('TotalPorRamaDeActividadMujeres', 0);

                $totalEnfermedadProfesional = $totalesEnfermedadesProfesionales->addChild('TotalEnfermedadProfesional');
                $totalEnfermedadProfesional->addAttribute('CodEnfermedadProfesional', '1101');
                $totalEnfermedadProfesional->addAttribute('TotalPorEnfermedadProfesional', 0);
                $totalEnfermedadProfesional->addChild('TotalPorEnfermedadProfesionalHombres', 0);
                $totalEnfermedadProfesional->addChild('TotalPorEnfermedadProfesionalMujeres', 0);

                $totalGrupoDeEdad = $totalesGruposDeEdades->addChild('TotalGrupoDeEdad');
                $totalGrupoDeEdad->addAttribute('CodGrupoDeEdad', '15');
                $totalGrupoDeEdad->addAttribute('TotalPorGrupoDeEdad', 0);
                $totalGrupoDeEdad->addChild('TotalPorGrupoDeEdadHombres', 0);
                $totalGrupoDeEdad->addChild('TotalPorGrupoDeEdadHombres', 0);

                $totalTamanoDeEmpresa = $totalesTamanosDeEmpresas->addChild('TotalTamanoDeEmpresa');
                $totalTamanoDeEmpresa->addAttribute('CodTamanoEmpresa', '1');
                $totalTamanoDeEmpresa->addAttribute('TotalPorTamanoEmpresa', 0);
                $totalTamanoDeEmpresa->addChild('TotalPorTamanoEmpresaHombres', 0);
                $totalTamanoDeEmpresa->addChild('TotalPorTamanoEmpresaMujeres', 0);


            }

            // Formatear el XML adecuadamente
            $dom = new \DOMDocument('1.0');
            $dom->preserveWhiteSpace = false;
            $dom->formatOutput = true;
            $dom->loadXML($xml->asXML());

            // Establecer headers y devolver el XML
            $fileName = 'ModeloEnfermedadesProfesionalesRT_' . $xmlYear . '.xml';
            return response($dom->saveXML(), 200)
                ->header('Content-Type', 'application/xml')
                ->header('Content-Disposition', 'attachment; filename="' . $fileName . '"');

        } catch (\Exception $e) {
            return redirect('/admin/reportes/descargas_admin')
                ->withErrors($e->getMessage())
                ->withInput();
        }
    }

    public function queryXmlThree($xmlYear)
    {
        $query = "
        CREATE OR REPLACE
ALGORITHM = UNDEFINED VIEW `report_xml_three` AS
        (
                    SELECT 
                          case when oc.group_id IS NULL then 0 ELSE oc.group_id END AS grupo_ocupacion,  
                            ea.group_economic AS grupo,  
                          SUM(CASE WHEN psa.gender IN ('F','M') THEN 1 ELSE 0 END) AS total,
                          SUM(CASE WHEN psa.gender = 'M' THEN 1 ELSE 0 END) AS total_hombres,
                          SUM(CASE WHEN psa.gender = 'F' THEN 1 ELSE 0 END) AS total_mujeres,
                          YEAR(g.created_at) AS ano,
                          'GruposOcupacionales' AS type,
                          1 AS id
                    FROM gis_sort g 
                    LEFT JOIN activities a ON a.id=g.activity_id
                    LEFT JOIN activities ap ON ap.id=a.parent_id
                    LEFT JOIN policy_sorts ps ON ps.activity_id = ap.id
                    LEFT JOIN economic_activities ea ON ea.code = ps.activity_economic_id
                    LEFT JOIN occupations oc ON oc.id= g.occupancy_group
                    LEFT JOIN ( 
                                SELECT p.identification_number, p.gender, p.hours, ROW_NUMBER() OVER (PARTITION BY identification_number, gender ORDER BY id DESC) AS rn
                                FROM policy_spreadsheet_affiliates p
                              ) psa ON psa.identification_number = g.number_identification_affiliate AND psa.rn = 1
                     LEFT JOIN ( 								
										SELECT a.activity_id
										FROM activity_actions a 
										WHERE a.new_state_id = 160
										GROUP BY a.activity_id
									) aa ON aa.activity_id=g.activity_id
                    WHERE g.type_report = 'Enfermedad' AND ea.group_economic IS NOT NULL AND aa.activity_id IS NOT NULL 
                    GROUP BY oc.group_id
                    
                    UNION ALL
                    
                    SELECT
                            case when oc.group_id IS NULL then 0 ELSE oc.group_id END AS grupo_ocupacion, 
                            CASE 
                              WHEN psa.age < 15 THEN 1
                              WHEN psa.age >= 15 AND psa.age < 18 THEN 15
                              WHEN psa.age >= 18 AND psa.age < 20 THEN 18
                              WHEN psa.age >= 20 AND psa.age < 25 THEN 20
                              WHEN psa.age >= 25 AND psa.age < 30 THEN 25
                              WHEN psa.age >= 30 AND psa.age < 35 THEN 30
                              WHEN psa.age >= 35 AND psa.age < 40 THEN 35
                              WHEN psa.age >= 40 AND psa.age < 45 THEN 40
                              WHEN psa.age >= 45 AND psa.age < 50 THEN 45
                              WHEN psa.age >= 50 AND psa.age < 55 THEN 50
                              WHEN psa.age >= 55 AND psa.age < 60 THEN 55
                              WHEN psa.age >= 60 AND psa.age < 65 THEN 60
                              WHEN psa.age >= 65 THEN 65
                              ELSE 99 
                          END AS grupo,
                          SUM(CASE WHEN psa.gender IN ('F','M') THEN 1 ELSE 0 END) AS total_f,
                          SUM(CASE WHEN psa.gender = 'M' THEN 1 ELSE 0 END) AS total_hombres,
                          SUM(CASE WHEN psa.gender = 'F' THEN 1 ELSE 0 END) AS total_mujeres,
                          YEAR(g.created_at) AS ano,
                           'GruposDeEdades' AS TYPE,
                           3 AS id
                    FROM gis_sort g
                    LEFT JOIN activities a ON a.id=g.activity_id
                    LEFT JOIN activities ap ON ap.id=a.parent_id
                    LEFT JOIN policy_sorts ps ON ps.activity_id = ap.id
                    LEFT JOIN occupations oc ON oc.id= g.occupancy_group
                    LEFT JOIN ( 
                          SELECT p.identification_number, p.gender, p.hours, TIMESTAMPDIFF(YEAR, p.date_of_birth, CURDATE()) AS age, ROW_NUMBER() OVER (PARTITION BY identification_number, gender ORDER BY id DESC) AS rn
                          FROM policy_spreadsheet_affiliates p
                          WHERE p.date_of_birth IS NOT NULL 
                        ) psa ON psa.identification_number = g.number_identification_affiliate AND psa.rn = 1
                    LEFT JOIN ( 								
										SELECT a.activity_id
										FROM activity_actions a 
										WHERE a.new_state_id = 160
										GROUP BY a.activity_id
									) aa ON aa.activity_id=g.activity_id
                    WHERE g.type_report = 'Enfermedad' AND aa.activity_id IS NOT NULL                   
                    GROUP BY oc.group_id,
                                (CASE 
                                  WHEN psa.age < 15 THEN 1
                                  WHEN psa.age >= 15 AND psa.age < 18 THEN 15
                                  WHEN psa.age >= 18 AND psa.age < 20 THEN 18
                                  WHEN psa.age >= 20 AND psa.age < 25 THEN 20
                                  WHEN psa.age >= 25 AND psa.age < 30 THEN 25
                                  WHEN psa.age >= 30 AND psa.age < 35 THEN 30
                                  WHEN psa.age >= 35 AND psa.age < 40 THEN 35
                                  WHEN psa.age >= 40 AND psa.age < 45 THEN 40
                                  WHEN psa.age >= 45 AND psa.age < 50 THEN 45
                                  WHEN psa.age >= 50 AND psa.age < 55 THEN 50
                                  WHEN psa.age >= 55 AND psa.age < 60 THEN 55
                                  WHEN psa.age >= 60 AND psa.age < 65 THEN 60
                                  WHEN psa.age >= 65 THEN 65
                                  ELSE 9
                                    END )
                                    
                    UNION ALL 
                    
                    SELECT 
                             case when oc.group_id IS NULL then 0 ELSE oc.group_id END AS grupo_ocupacion,
                              CASE 
                                 WHEN pro_afiliado = 1 THEN 1
                                 WHEN pro_afiliado = 2 THEN 2
                                 WHEN pro_afiliado = 3 THEN 3
                                 WHEN pro_afiliado = 4 THEN 4
                                 WHEN pro_afiliado = 5 THEN 5
                                 WHEN pro_afiliado >= 6 AND pro_afiliado <= 9 THEN 6
                                 WHEN pro_afiliado >= 10 AND pro_afiliado <= 19 THEN 10
                                 WHEN pro_afiliado >= 20 AND pro_afiliado <= 29 THEN 20
                                 WHEN pro_afiliado >= 30 AND pro_afiliado <= 39 THEN 30
                                 WHEN pro_afiliado >= 40 AND pro_afiliado <= 49 THEN 40
                                 WHEN pro_afiliado >= 50 AND pro_afiliado <= 99 THEN 50
                                 WHEN pro_afiliado >= 100 AND pro_afiliado <= 149 THEN 100
                                 WHEN pro_afiliado >= 150 AND pro_afiliado <= 199 THEN 150
                                 WHEN pro_afiliado >= 200 AND pro_afiliado <= 249 THEN 200
                                 WHEN pro_afiliado >= 250 AND pro_afiliado <= 499 THEN 250
                                 WHEN pro_afiliado >= 500 AND pro_afiliado <= 999 THEN 500
                                 WHEN pro_afiliado >= 1000 THEN 1000
                                 ELSE 9999
                             END AS grupo,
                             SUM(CASE WHEN psa.gender IN ('F','M') THEN 1 ELSE 0 END) AS total_f,
                             SUM(CASE WHEN psa.gender = 'M' THEN 1 ELSE 0 END) AS total_hombres,
                             SUM(CASE WHEN psa.gender = 'F' THEN 1 ELSE 0 END) AS total_mujeres,
                             YEAR(g.created_at) AS ano,
                             'TamanosDeEmpresas' AS TYPE,
                             4 as id
                      FROM gis_sort g
                      LEFT JOIN activities a ON a.id = g.activity_id
                      LEFT JOIN activities ap ON ap.id = a.parent_id
                      LEFT JOIN policy_sorts ps ON ps.activity_id = ap.id
                      LEFT JOIN occupations oc ON oc.id= g.occupancy_group
                      LEFT JOIN ( 
                             SELECT p.identification_number, p.gender, p.hours, ROW_NUMBER() OVER (PARTITION BY identification_number, gender ORDER BY id DESC) AS rn
                             FROM policy_spreadsheet_affiliates p
                           ) psa ON psa.identification_number = g.number_identification_affiliate AND psa.rn = 1
                      LEFT JOIN (
                                      SELECT a.parent_id , ROUND(SUM(p.total_affiliates)/COUNT(*)) AS pro_afiliado
                                      FROM policy_spreadsheets p 
                                      LEFT JOIN activities a ON a.id = p.activity_id
                                      WHERE p.total_affiliates IS NOT NULL AND p.total_affiliates <> 0 AND a.parent_id IS NOT NULL 
                                      GROUP BY a.parent_id
                              ) pss ON pss.parent_id = ps.activity_id
                      LEFT JOIN ( 								
										SELECT a.activity_id
										FROM activity_actions a 
										WHERE a.new_state_id = 160
										GROUP BY a.activity_id
									) aa ON aa.activity_id=g.activity_id
                      WHERE g.type_report = 'Enfermedad' and pss.pro_afiliado is not null AND aa.activity_id IS NOT NULL  
                      GROUP BY case when oc.group_id IS NULL then 0 ELSE oc.group_id END,
                                    (CASE 
                                 WHEN pro_afiliado = 1 THEN 1
                                 WHEN pro_afiliado = 2 THEN 2
                                 WHEN pro_afiliado = 3 THEN 3
                                 WHEN pro_afiliado = 4 THEN 4
                                 WHEN pro_afiliado = 5 THEN 5
                                 WHEN pro_afiliado >= 6 AND pro_afiliado <= 9 THEN 6
                                 WHEN pro_afiliado >= 10 AND pro_afiliado <= 19 THEN 10
                                 WHEN pro_afiliado >= 20 AND pro_afiliado <= 29 THEN 20
                                 WHEN pro_afiliado >= 30 AND pro_afiliado <= 39 THEN 30
                                 WHEN pro_afiliado >= 40 AND pro_afiliado <= 49 THEN 40
                                 WHEN pro_afiliado >= 50 AND pro_afiliado <= 99 THEN 50
                                 WHEN pro_afiliado >= 100 AND pro_afiliado <= 149 THEN 100
                                 WHEN pro_afiliado >= 150 AND pro_afiliado <= 199 THEN 150
                                 WHEN pro_afiliado >= 200 AND pro_afiliado <= 249 THEN 200
                                 WHEN pro_afiliado >= 250 AND pro_afiliado <= 499 THEN 250
                                 WHEN pro_afiliado >= 500 AND pro_afiliado <= 999 THEN 500
                                 WHEN pro_afiliado >= 1000 THEN 1000
                                 ELSE 9999
                             END )
                             )";

        $query = "  SELECT * 
                    FROM report_xml_three 
                    WHERE ano = '$xmlYear'
                    ORDER BY grupo_ocupacion, id ";

        $data = DB::select($query);

        return $data;

    }

    //ME-1322 Requerimiento 40 parte 4: Generación de modelos XML 16 SUGESE
    public function reportXmlFour(Request $req)
    {
        try {

            $xmlYear = $req->xml_four_year;
            $periodo = $req->periodo ?? date('Y-m-d');

            if (empty($xmlYear)) {
                throw new \Exception("El campo año no puede estar vacío, en la sección 'MODELO XML 16'");
            }

            // Generar el XML
            $xml = new \SimpleXMLElement('<?xml version="1.0" encoding="utf-8"?>
            <ModeloFallecidosRT  
                xmlns:xsd="http://www.w3.org/2001/XMLSchema" 
                xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            </ModeloFallecidosRT >');

            // Agregar encabezado
            $encabezado = $xml->addChild('Encabezado');
            $encabezado->addChild('NombreArchivo', 'ModeloFallecidosRiesgosDelTrabajo');
            $encabezado->addChild('Fecha', date('Y-m-d'));
            $encabezado->addChild('Periodo', $xmlYear.'-12-31');
            $encabezado->addChild('Periodicidad', 'A');
            $encabezado->addChild('Moneda', '1');

            // Agregar datos
            $datos = $xml->addChild('Datos');
            $modelo = $datos->addChild('Modelo');
            $modelo->addAttribute('EntidadFuente', 'A13');

            $fallecidos = $modelo->addChild('Fallecidos');


            $gruposOcupacionales = $fallecidos->addChild('GruposOcupacionales');

            $query = "  SELECT 
                               case when oc.group_id IS NULL then 0 ELSE oc.group_id END AS grupo_ocupacion,
                               ea.code AS grupo,  
                               SUM(CASE WHEN psa.gender IN ('F','M') THEN 1 ELSE 0 END) AS total,
                               SUM(CASE WHEN psa.gender = 'M' THEN 1 ELSE 0 END) AS total_hombres,
                               SUM(CASE WHEN psa.gender = 'F' THEN 1 ELSE 0 END) AS total_mujeres,
                               YEAR(g.created_at) AS ano
                        FROM gis_sort g 
                        LEFT JOIN activities a ON a.id=g.activity_id
                        LEFT JOIN activities ap ON ap.id=a.parent_id
                        LEFT JOIN policy_sorts ps ON ps.activity_id = ap.id
                        LEFT JOIN economic_activities ea ON ea.code = ps.activity_economic_id
                        LEFT JOIN occupations oc ON oc.id= g.occupancy_group
                        LEFT JOIN ( 
                                   SELECT p.identification_number, p.gender, p.hours, ROW_NUMBER() OVER (PARTITION BY identification_number, gender ORDER BY id DESC) AS rn
                                   FROM policy_spreadsheet_affiliates p
                                 ) psa ON psa.identification_number = g.number_identification_affiliate AND psa.rn = 1
                        LEFT JOIN (
                                        SELECT a.activity_id
                                        FROM activity_actions a
                                        WHERE a.action_id IN (253, 265)
                                        GROUP BY a.activity_id
                                    ) aa ON aa.activity_id = g.activity_id
                        WHERE YEAR(g.created_at) = '$xmlYear' AND ea.code IS NOT NULL AND aa.activity_id IS NOT NULL
                        GROUP BY oc.group_id ";

            $data = DB::select($query);

            $totalFallecidos = 0;
            $totalCantidadFallecidosHombres = 0;
            $totalCantidadFallecidosMujeres = 0;

            foreach ($data as $row) {

                $grupoOcupacional = $gruposOcupacionales->addChild('GrupoOcupacional');
                $grupoOcupacional->addAttribute('CodGrupoOcupacional', $row->grupo_ocupacion);
                $grupoOcupacional->addAttribute('TotalFallecidosPorGrupoOcupacional', intval($row->total));
                $grupoOcupacional->addChild('CantidadFallecidosHombres', intval($row->total_hombres));
                $grupoOcupacional->addChild('CantidadFallecidosHombres', intval($row->total_mujeres));

                $totalFallecidos += $row->total ?? 0;
                $totalCantidadFallecidosHombres += $row->total_hombres ?? 0;
                $totalCantidadFallecidosMujeres += $row->total_mujeres ?? 0;

            }

            //se agrega esta data quemada ya que para el 2024 no trae nada
            if(!$data){
                $grupoOcupacional = $gruposOcupacionales->addChild('GrupoOcupacional');
                $grupoOcupacional->addAttribute('CodGrupoOcupacional', '8189');
                $grupoOcupacional->addAttribute('TotalFallecidosPorGrupoOcupacional', 0);
                $grupoOcupacional->addChild('CantidadFallecidosHombres', 0);
                $grupoOcupacional->addChild('CantidadFallecidosMujeres', 0);
            }

            $fallecidos->addAttribute('TotalFallecidos', intval($totalFallecidos));
            $fallecidos->addAttribute('TotalCantidadFallecidosHombres', intval($totalCantidadFallecidosHombres));
            $fallecidos->addAttribute('TotalCantidadFallecidosMujeres', intval($totalCantidadFallecidosMujeres));


            $ramasDeActividades = $fallecidos->addChild('RamasDeActividades');

            foreach ($data as $row) {
                $ramaDeActividad = $ramasDeActividades->addChild('RamaDeActividad');
                $ramaDeActividad->addAttribute('CodRamaActividad', $row->grupo);
                $ramaDeActividad->addAttribute('TotalFallecidosPorRamaDeActividad', intval($row->total));
                $ramaDeActividad->addChild('CantidadFallecidosHombres', intval($row->total_hombres));
                $ramaDeActividad->addChild('CantidadFallecidosMujeres', intval($row->total_mujeres));
            }

            //se agrega esta data quemada ya que para el 2024 no trae nada
            if(!$data){
                $ramaDeActividad = $ramasDeActividades->addChild('RamaDeActividad');
                $ramaDeActividad->addAttribute('CodRamaActividad', '9810');
                $ramaDeActividad->addAttribute('TotalFallecidosPorRamaDeActividad', 0);
                $ramaDeActividad->addChild('CantidadFallecidosHombres', 0);
                $ramaDeActividad->addChild('CantidadFallecidosMujeres', 0);
            }

            // Formatear el XML adecuadamente
            $dom = new \DOMDocument('1.0');
            $dom->preserveWhiteSpace = false;
            $dom->formatOutput = true;
            $dom->loadXML($xml->asXML());

            // Establecer headers y devolver el XML
            $fileName = 'ModeloFallecidosRiesgosDelTrabajo_' . $xmlYear . '.xml';
            return response($dom->saveXML(), 200)
                ->header('Content-Type', 'application/xml')
                ->header('Content-Disposition', 'attachment; filename="' . $fileName . '"');

        } catch (\Exception $e) {
            return redirect('/admin/reportes/descargas_admin')
                ->withErrors($e->getMessage())
                ->withInput();
        }
    }

    //ME-1323 Requerimiento 40 parte 5: Generación de modelos XML 17 SUGESE
    public function reportXmlFive(Request $req)
    {
        try {

            $xmlYear = $req->xml_five_year;
            $periodo = $req->periodo ?? date('Y-m-d');

            if (empty($xmlYear)) {
                throw new \Exception("El campo año no puede estar vacío, en la sección 'MODELO XML 17'");
            }

            $xml = new \SimpleXMLElement('<?xml version="1.0" encoding="utf-8"?>
            <ModeloPatronosRT  
                xmlns:xsd="http://www.w3.org/2001/XMLSchema" 
                xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            </ModeloPatronosRT >');

            $encabezado = $xml->addChild('Encabezado');
            $encabezado->addChild('NombreArchivo', 'ModeloPatronosRT');
            $encabezado->addChild('Fecha', date('Y-m-d'));
            $encabezado->addChild('Periodo', $xmlYear.'-12-31');
            $encabezado->addChild('Periodicidad', 'A');
            $encabezado->addChild('Moneda', '1');

            $datos = $xml->addChild('Datos');
            $modelo = $datos->addChild('Modelo');
            $modelo->addAttribute('EntidadFuente', 'A13');

            $patronosAsegurados = $modelo->addChild('PatronosAsegurados');

            $totalTamanoEmpresa = $patronosAsegurados->addChild('TotalTamanoEmpresa');

            $ramasDeActividades = $patronosAsegurados->addChild('RamasDeActividades');

            $query = "        
                    WITH clasificacion_empresa AS (
                        SELECT 
                            a.parent_id, 
                            ROUND(SUM(p.total_affiliates) / NULLIF(COUNT(*), 0)) AS pro_afiliado
                        FROM policy_spreadsheets p 
                        LEFT JOIN activities a ON a.id = p.activity_id
                        WHERE p.total_affiliates IS NOT NULL 
                          AND p.total_affiliates <> 0 
                          AND a.parent_id IS NOT NULL 
                        GROUP BY a.parent_id
                    ),
                    tamano_empresa AS (
                        SELECT 
                            parent_id,
                            CASE 
                                WHEN pro_afiliado BETWEEN 1 AND 5 THEN pro_afiliado
                                WHEN pro_afiliado BETWEEN 6 AND 9 THEN 6
                                WHEN pro_afiliado BETWEEN 10 AND 19 THEN 10
                                WHEN pro_afiliado BETWEEN 20 AND 29 THEN 20
                                WHEN pro_afiliado BETWEEN 30 AND 39 THEN 30
                                WHEN pro_afiliado BETWEEN 40 AND 49 THEN 40
                                WHEN pro_afiliado BETWEEN 50 AND 99 THEN 50
                                WHEN pro_afiliado BETWEEN 100 AND 149 THEN 100
                                WHEN pro_afiliado BETWEEN 150 AND 199 THEN 150
                                WHEN pro_afiliado BETWEEN 200 AND 249 THEN 200
                                WHEN pro_afiliado BETWEEN 250 AND 499 THEN 250
                                WHEN pro_afiliado BETWEEN 500 AND 999 THEN 500
                                WHEN pro_afiliado >= 1000 THEN 1000
                                ELSE 9999
                            END AS tam_empresa
                        FROM clasificacion_empresa
                    )
                    SELECT 
                    		ea.code as group_economic,
                        -- ea.code AS grupo_ocupacion,
                        te.tam_empresa,
                        SUM(CASE WHEN psa.gender IN ('F', 'M') THEN 1 ELSE 0 END) AS total,
                        SUM(CASE WHEN psa.gender = 'M' THEN 1 ELSE 0 END) AS total_hombres,
                        SUM(CASE WHEN psa.gender = 'F' THEN 1 ELSE 0 END) AS total_mujeres
                    FROM gis_sort g
                    LEFT JOIN activities a ON a.id = g.activity_id
                    LEFT JOIN activities ap ON ap.id = a.parent_id
                    LEFT JOIN policy_sorts ps ON ps.activity_id = ap.id
                    LEFT JOIN economic_activities ea ON ea.code = ps.activity_economic_id
                    LEFT JOIN (
                        SELECT p.identification_number, p.gender, p.hours, 
                               ROW_NUMBER() OVER (PARTITION BY identification_number, gender ORDER BY id DESC) AS rn
                        FROM policy_spreadsheet_affiliates p
                    ) psa ON psa.identification_number = g.number_identification_affiliate AND psa.rn = 1
                    LEFT JOIN tamano_empresa te ON te.parent_id = ps.activity_id
                    WHERE te.tam_empresa IS NOT NULL AND ea.code IS NOT NULL 
                      AND YEAR(g.created_at) = '$xmlYear' AND a.deleted_at IS null
                    GROUP BY ea.code, te.tam_empresa
                    ORDER BY ea.code, te.tam_empresa;";


            $query = "WITH clasificacion_empresa AS (
                        SELECT 
                            a.parent_id, 
                            ROUND(SUM(p.total_affiliates) / NULLIF(COUNT(*), 0)) AS pro_afiliado
                        FROM policy_spreadsheets p 
                        LEFT JOIN activities a ON a.id = p.activity_id
                        WHERE p.total_affiliates IS NOT NULL 
                          AND p.total_affiliates <> 0 
                          AND a.parent_id IS NOT NULL 
                        GROUP BY a.parent_id
                    ),
                    tamano_empresa AS (
                        SELECT 
                            parent_id,
                            CASE 
                                WHEN pro_afiliado BETWEEN 1 AND 5 THEN pro_afiliado
                                WHEN pro_afiliado BETWEEN 6 AND 9 THEN 6
                                WHEN pro_afiliado BETWEEN 10 AND 19 THEN 10
                                WHEN pro_afiliado BETWEEN 20 AND 29 THEN 20
                                WHEN pro_afiliado BETWEEN 30 AND 39 THEN 30
                                WHEN pro_afiliado BETWEEN 40 AND 49 THEN 40
                                WHEN pro_afiliado BETWEEN 50 AND 99 THEN 50
                                WHEN pro_afiliado BETWEEN 100 AND 149 THEN 100
                                WHEN pro_afiliado BETWEEN 150 AND 199 THEN 150
                                WHEN pro_afiliado BETWEEN 200 AND 249 THEN 200
                                WHEN pro_afiliado BETWEEN 250 AND 499 THEN 250
                                WHEN pro_afiliado BETWEEN 500 AND 999 THEN 500
                                WHEN pro_afiliado >= 1000 THEN 1000
                                ELSE 9999
                            END AS tam_empresa
                        FROM clasificacion_empresa
                    )
                    SELECT 
                    		ea.code as group_economic,
                        -- ea.code AS grupo_ocupacion,
                        te.tam_empresa,
                        1 AS total,
                        SUM(CASE WHEN af.gender = 'M' THEN 1 ELSE 0 END) AS total_hombres,
                        SUM(CASE WHEN af.gender = 'F' THEN 1 ELSE 0 END) AS total_mujeres
                    FROM policy_sorts ps 
                    LEFT JOIN economic_activities ea ON ea.code = ps.activity_economic_id
                    LEFT JOIN activities a on a.id = ps.activity_id 
                    left join affiliates af on af.id =a.affiliate_id 
                    LEFT JOIN tamano_empresa te ON te.parent_id = ps.activity_id
                    left join activity_actions aa on aa.activity_id =ps.activity_id and aa.action_id = 16
                    WHERE te.tam_empresa IS NOT NULL AND ea.code IS NOT NULL 
                      AND YEAR(aa.created_at) = '$xmlYear' AND a.deleted_at IS null
                    GROUP BY ea.code, te.tam_empresa
                    ORDER BY ea.code, te.tam_empresa ";

            $data = DB::select($query);

            $codRamaDeActividad = '';
            $totalPatronosAseguradosPorRama = 0;

            $cantidadTotalPorTamanoEmpresa1 = 0;
            $cantidadTotalPorTamanoEmpresa2 = 0;
            $cantidadTotalPorTamanoEmpresa3 = 0;
            $cantidadTotalPorTamanoEmpresa4 = 0;
            $cantidadTotalPorTamanoEmpresa5 = 0;
            $cantidadTotalPorTamanoEmpresa6 = 0;
            $cantidadTotalPorTamanoEmpresa10 = 0;
            $cantidadTotalPorTamanoEmpresa20 = 0;
            $cantidadTotalPorTamanoEmpresa30 = 0;
            $cantidadTotalPorTamanoEmpresa40 = 0;
            $cantidadTotalPorTamanoEmpresa50 = 0;
            $cantidadTotalPorTamanoEmpresa100 = 0;
            $cantidadTotalPorTamanoEmpresa150 = 0;
            $cantidadTotalPorTamanoEmpresa200 = 0;
            $cantidadTotalPorTamanoEmpresa250 = 0;
            $cantidadTotalPorTamanoEmpresa500 = 0;
            $cantidadTotalPorTamanoEmpresa1000 = 0;
            $totalPatronosAsegurados = 0;
            $patronosAseguradosPorRamaDeActividad = $ramasDeActividades->addChild('PatronosAseguradosPorRamaDeActividad');

            foreach ($data as $row) {

                if ($row->group_economic != $codRamaDeActividad) {

                    if (!empty($codRamaDeActividad)) {

                        $patronosAseguradosPorRamaDeActividad->addAttribute('CodRamaActividad', $codRamaDeActividad);
                        $patronosAseguradosPorRamaDeActividad->addAttribute('TotalPatronosAseguradosPorRama', intval($totalPatronosAseguradosPorRama));
                        $totalPatronosAsegurados += $totalPatronosAseguradosPorRama;
                        $totalPatronosAseguradosPorRama = 0;

                        $patronosAseguradosPorRamaDeActividad = $ramasDeActividades->addChild('PatronosAseguradosPorRamaDeActividad');
                    }

                    $codRamaDeActividad = $row->group_economic;
                }

                $tamanoEmpresa = $patronosAseguradosPorRamaDeActividad->addChild('TamanoEmpresa');
                $tamanoEmpresa->addAttribute('CodTamanoEmpresa', $row->tam_empresa);
                $tamanoEmpresa->addChild('CantidadPatronosAseguradosPorTamanoEmpresa', intval($row->total));

                $totalPatronosAseguradosPorRama += $row->total;

                if ($row->tam_empresa == "1") {
                    $cantidadTotalPorTamanoEmpresa1 += $row->total;
                } else if ($row->tam_empresa == "2"){
                    $cantidadTotalPorTamanoEmpresa2 += $row->total;
                } else if ($row->tam_empresa == "3"){
                    $cantidadTotalPorTamanoEmpresa3 += $row->total;
                } else if ($row->tam_empresa == "4"){
                    $cantidadTotalPorTamanoEmpresa4 += $row->total;
                } else if ($row->tam_empresa == "5"){
                    $cantidadTotalPorTamanoEmpresa5 += $row->total;
                } else if ($row->tam_empresa == "6"){
                    $cantidadTotalPorTamanoEmpresa6 += $row->total;
                } else if ($row->tam_empresa == "10"){
                    $cantidadTotalPorTamanoEmpresa10 += $row->total;
                } else if ($row->tam_empresa == "20"){
                    $cantidadTotalPorTamanoEmpresa20 += $row->total;
                } else if ($row->tam_empresa == "30"){
                    $cantidadTotalPorTamanoEmpresa30 += $row->total;
                } else if ($row->tam_empresa == "40"){
                    $cantidadTotalPorTamanoEmpresa40 += $row->total;
                } else if ($row->tam_empresa == "50"){
                    $cantidadTotalPorTamanoEmpresa50 += $row->total;
                } else if ($row->tam_empresa == "100"){
                    $cantidadTotalPorTamanoEmpresa100 += $row->total;
                } else if ($row->tam_empresa == "150"){
                    $cantidadTotalPorTamanoEmpresa150 += $row->total;
                } else if ($row->tam_empresa == "200"){
                    $cantidadTotalPorTamanoEmpresa200 += $row->total;
                } else if ($row->tam_empresa == "250"){
                    $cantidadTotalPorTamanoEmpresa250 += $row->total;
                } else if ($row->tam_empresa == "500"){
                    $cantidadTotalPorTamanoEmpresa500 += $row->total;
                } else if ($row->tam_empresa == "1000"){
                    $cantidadTotalPorTamanoEmpresa1000 += $row->total;
                }
            }

            if (!empty($codRamaDeActividad)) {

                $patronosAseguradosPorRamaDeActividad->addAttribute('CodRamaActividad', $codRamaDeActividad);
                $patronosAseguradosPorRamaDeActividad->addAttribute('TotalPatronosAseguradosPorRama', intval($totalPatronosAseguradosPorRama));


                $totalPatronosAsegurados += $totalPatronosAseguradosPorRama;
                $patronosAsegurados->addAttribute('TotalPatronosAsegurados', intval($totalPatronosAsegurados));

            }


            if($cantidadTotalPorTamanoEmpresa1 != 0) {
                $totalTamanoEmpresa->addChild('CantidadTotalPorTamanoEmpresa1', intval($cantidadTotalPorTamanoEmpresa1));
            }
            if($cantidadTotalPorTamanoEmpresa2 != 0) {
                $totalTamanoEmpresa->addChild('CantidadTotalPorTamanoEmpresa2', intval($cantidadTotalPorTamanoEmpresa2));
            }
            if($cantidadTotalPorTamanoEmpresa3 != 0) {
                $totalTamanoEmpresa->addChild('CantidadTotalPorTamanoEmpresa3', intval($cantidadTotalPorTamanoEmpresa3));
            }
            if($cantidadTotalPorTamanoEmpresa4 != 0) {
                $totalTamanoEmpresa->addChild('CantidadTotalPorTamanoEmpresa4', intval($cantidadTotalPorTamanoEmpresa4));
            }
            if($cantidadTotalPorTamanoEmpresa5 != 0) {
                $totalTamanoEmpresa->addChild('CantidadTotalPorTamanoEmpresa5', intval($cantidadTotalPorTamanoEmpresa5));
            }
            if($cantidadTotalPorTamanoEmpresa6 != 0) {
                $totalTamanoEmpresa->addChild('CantidadTotalPorTamanoEmpresa6', intval($cantidadTotalPorTamanoEmpresa6));
            }
            if($cantidadTotalPorTamanoEmpresa10 != 0) {
                $totalTamanoEmpresa->addChild('CantidadTotalPorTamanoEmpresa10', intval($cantidadTotalPorTamanoEmpresa10));
            }
            if($cantidadTotalPorTamanoEmpresa20 != 0) {
                $totalTamanoEmpresa->addChild('CantidadTotalPorTamanoEmpresa20', intval($cantidadTotalPorTamanoEmpresa20));
            }
            if($cantidadTotalPorTamanoEmpresa30 != 0) {
                $totalTamanoEmpresa->addChild('CantidadTotalPorTamanoEmpresa30', intval($cantidadTotalPorTamanoEmpresa30));
            }
            if($cantidadTotalPorTamanoEmpresa40 != 0) {
                $totalTamanoEmpresa->addChild('CantidadTotalPorTamanoEmpresa40', intval($cantidadTotalPorTamanoEmpresa40));
            }
            if($cantidadTotalPorTamanoEmpresa50 != 0) {
                $totalTamanoEmpresa->addChild('CantidadTotalPorTamanoEmpresa50', intval($cantidadTotalPorTamanoEmpresa50));
            }
            if($cantidadTotalPorTamanoEmpresa100 != 0) {
                $totalTamanoEmpresa->addChild('CantidadTotalPorTamanoEmpresa100', intval($cantidadTotalPorTamanoEmpresa100));
            }
            if($cantidadTotalPorTamanoEmpresa150 != 0) {
                $totalTamanoEmpresa->addChild('CantidadTotalPorTamanoEmpresa150', intval($cantidadTotalPorTamanoEmpresa150));
            }
            if($cantidadTotalPorTamanoEmpresa200 != 0) {
                $totalTamanoEmpresa->addChild('CantidadTotalPorTamanoEmpresa200', intval($cantidadTotalPorTamanoEmpresa200));
            }
            if($cantidadTotalPorTamanoEmpresa250 != 0) {
                $totalTamanoEmpresa->addChild('CantidadTotalPorTamanoEmpresa250', intval($cantidadTotalPorTamanoEmpresa250));
            }
            if($cantidadTotalPorTamanoEmpresa500 != 0) {
                $totalTamanoEmpresa->addChild('CantidadTotalPorTamanoEmpresa500', intval($cantidadTotalPorTamanoEmpresa500));
            }
            if($cantidadTotalPorTamanoEmpresa1000 != 0) {
                $totalTamanoEmpresa->addChild('CantidadTotalPorTamanoEmpresa1000', intval($cantidadTotalPorTamanoEmpresa1000));
            }


            // Formatear el XML adecuadamente
            $dom = new \DOMDocument('1.0');
            $dom->preserveWhiteSpace = false;
            $dom->formatOutput = true;
            $dom->loadXML($xml->asXML());

            // Establecer headers y devolver el XML
            $fileName = 'ModeloPatronosRT_' . $xmlYear . '.xml';
            return response($dom->saveXML(), 200)
                ->header('Content-Type', 'application/xml')
                ->header('Content-Disposition', 'attachment; filename="' . $fileName . '"');

        } catch (\Exception $e) {
            return redirect('/admin/reportes/descargas_admin')
                ->withErrors($e->getMessage())
                ->withInput();
        }
    }

    public function reportProviders (Request $req, $cpath)
    {
        try {


            $activities = [];

            $consulta = "                          
                        SELECT p.name, 
                               p.doc_type, 
                               p.doc_number, 
                               p.email, 
                               date_format(p.start_date, '%d/%m/%Y') as start_date, 
                               date_format(p.end_date, '%d/%m/%Y') as end_date,
                                case when p.active = '1' then 'Activo' ELSE 'Inactivo' END AS estado 
                        from providers p; ";

            $query = DB::select($consulta);

            foreach ($query as $item) {
                $activities[] = [
                    $item->name,
                    $item->doc_type,
                    $item->doc_number,
                    $item->email,
                    $item->start_date,
                    $item->end_date,
                    $item->estado
                ];
            }

            $headings = [
                'Proveedor',
                'Tipo documento',
                'Numero documento',
                'Correo',
                'Fecha inicio vigencia',
                'Fecha fin vigencia',
                'Estado'
            ];

            return Excel::create('reporteProveedores', function ($excel) use ($activities, $headings) {
                $excel->sheet('Entries', function ($sheet) use ($activities, $headings) {
                    $sheet->row(1, $headings);
                    $sheet->fromArray($activities, null, 'A2', false, false);
                });
            })->download('xlsx');

        }catch (\Exception $e) {
            return response()->json([
                'message' => 'Error',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    //ME-3140
    public function reportUsers (Request $req, $cpath)
    {
        try {

            $activities = [];
            $users = User::with('area:id,name')->get(['id', 'identification_number', 'doc_type', 'username', 'email', 'last_name', 'first_name', 'area_id', 'active']);

            $activities = $users->map(function ($user) {
                return [
                    $user->doc_type,
                    $user->identification_number,
                    $user->username,
                    $user->email,
                    $user->last_name,
                    $user->first_name,
                    optional($user->area)->name,
                    $user->active ? 'Activo' : 'Inactivo',
                ];
            })->toArray();

            $headings = [
                'Tipo de documento',
                'Número de documento',
                'Usuario',
                'Correo electrónico',
                'Apellidos',
                'Nombres',
                'Rol',
                'Estado'
            ];

            return Excel::create('reporteUsuarios', function ($excel) use ($activities, $headings) {
                $excel->sheet('Entries', function ($sheet) use ($activities, $headings) {
                    $sheet->row(1, $headings);
                    $sheet->fromArray($activities, null, 'A2', false, false);
                });
            })->download('xlsx');

        }catch (\Exception $e) {
            return response()->json([
                'message' => 'Error',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    //ME-2492 Reporte de Suscripción
    public function reportSubscription(Request $req, $cpath)
    {
        try {
            $initDate = $req->subscription_start_date_submit;
            $lastDate = $req->subscription_end_date_submit;

            if (empty($initDate) || empty($lastDate)) {
                throw new \Exception("Las fechas no pueden estar vacías, en la sección 'Reporte de reaseguro'");
            }

            if (strtotime($lastDate) < strtotime($initDate)) {
                throw new \Exception("La fecha desde no puede ser menor a la fecha hasta, en la sección 'Reporte de reaseguro'");
            }

            $activities = [];

            $consulta = "select * 
                         from report_subscription_view 
                         where created_at BETWEEN :initDate AND :lastDate ";

            $query = DB::select($consulta, [
                'initDate' => $initDate . ' 00:00:00',
                'lastDate' => $lastDate . ' 23:59:59'
            ]);

            foreach ($query as $item) {

                $activities[] = [
                    $item->poliza,
                    $item->fecha_emision,
                    $item->fecha_creacion,
                    $item->correduria,
                    $item->asesor,
                    $item->nombre_tomador,
                    $item->nombre_asegurado,
                    $item->genero,
                    $item->edad,
                    $item->provincia,
                    $item->canton,
                    $item->distrito,
                    $item->direccion,
                    $item->moneda_poliza,
                    $item->tasa_cambio,
                    number_format($item->prima_anual ?? 0, 2, ',', ''),
                    number_format($item->prima_cobrar ?? 0, 2, ',', ''),
                    $item->forma_pago,
                    number_format($item->prima_pagada ?? 0, 2, ',', ''),
                    $item->modalidad_aseguramiento,
                    $item->actividad_economica,
                    $item->tarifa_emision,
                    $item->estado_poliza
                ];
            }

            $headings = [
                '#de póliza',
                'Fecha de emisión',
                'Fecha de creación',
                'Correduría',
                'Asesor',
                'Nombre del Tomador',
                'Nombre Asegurado',
                'Genero',
                'Edad',
                'Provincia',
                'Canton',
                'Distrito',
                'Direccion',
                'Moneda de la póliza',
                'Tasa de cambio',
                'Prima anual',
                'Prima por cobrar',
                'Forma de pago',
                'Prima pagada',
                'Modalidad de aseguramiento',
                'Actividad economica',
                'Tarifa de emision',
                'Estado de la póliza'
            ];

            return Excel::create('reporteSuscripcion', function ($excel) use ($activities, $headings) {
                $excel->sheet('Entries', function ($sheet) use ($activities, $headings) {
                    $sheet->row(1, $headings);
                    $sheet->fromArray($activities, null, 'A2', false, false);
                });
            })->download('xlsx');

        }catch (\Exception $e) {

            return redirect('/admin/reportes/descargas_admin')
                ->withErrors($e->getMessage())
                ->withInput();
        }

    }

//reporte ramiro
    public function reportGis(Request $req, $cpath)
    {
        try {
            $initDate = $req->gis_start_date_submit;
            $lastDate = $req->gis_end_date_submit;

            if (empty($initDate) || empty($lastDate)) {
                throw new \Exception("Las fechas no pueden estar vacías, en la sección 'Reporte de gis'");
            }

            if (strtotime($lastDate) < strtotime($initDate)) {
                throw new \Exception("La fecha desde no puede ser menor a la fecha hasta, en la sección 'Reporte de gis'");
            }

            // ajuste fechas con tiempo
            $initDate = Carbon::parse($req->gis_start_date_submit)->startOfDay();
            $lastDate = Carbon::parse($req->gis_end_date_submit)->endOfDay();

            $headings = [
                'ID SERVICIO',
                'TIPO',
                '# CASO',
                '# AVISO',
                'NOMBRE DE LA EMPRESA',
                'IDENTIFICACIÓN',
                'FECHA DEL CASO',
                'FECHA DEL ACCIDENTE',
                'FECHA DEL AVISO',
                'FECHA DE ATENCION',
                'DIAGNOSTICO',
                'IMCAPACIDAD',
                'GENERO',
                'EDAD',
                'PARTE DEL CUERPO LESIONADA',
            ];

            $consulta = "select g.activity_id as servicio ,
                                    g.type_report,
                                    g.consecutive as numero_caso , 
                                    g.consecutive_gis as numero_aviso, 
                                    af.full_name as empresa , 
                                    af.doc_number as identificacion,
                                    date(a.created_at) as fecha_caso,
                                    date(g.date_accident) as fecha_acidente,
                                    aa.action_id ,
                                    at.name as accion,
                                    date(aa.created_at) as fecha_aviso,
                                    aaa.valuation_date as fecha_atencion,
                                    dg.diagnostico,
                                    case when pis.days_it IS NOT NULL then 'SI' ELSE 'NO' END AS impacaCidadSIoNo,
                                    psa.gender,
                                    psa.age AS edad,
                                     group_concat(distinct gbp.body_part_name order by gbp.body_part_name ASC separator ', ') AS part_lesionada
                            from gis_sort g
                            left join activities a on a.id =g.activity_id 
                            left join activities ap on ap.id = a.parent_id
                            left join affiliates af on af.id = ap.affiliate_id 
                            left join policy_sorts s on s.activity_id =ap.id
                            left join activity_actions aa on aa.activity_id = g.activity_id and aa.action_id = 264
                            left join actions at on at.id =aa.action_id
                            LEFT JOIN (
                                select a.parent_id  as gis, 
                                    mt.valuation_date,
                                    rOW_NUMBER() OVER (PARTITION BY a.parent_id ORDER BY a.parent_id) AS indicador
                                from activities a 
                                left join medical_services_sort m on m.activity_id =a.id
                                left join medical_service_follow_ups mt on mt.medical_services_sort_id =m.id 
                                where a.service_id  = 83
                                order by a.parent_id
                            ) aaa on aaa.gis = g.activity_id and aaa.indicador=1
                            left join gis_body_parts gbp ON gbp.gis_id = g.id
                            left join (
                                select
                                    p.identification_number AS identification_number,
                                    p.gender AS gender,
                                    p.hours AS hours,
                                    timestampdiff(YEAR, p.date_of_birth,curdate()) AS age,
                                    row_number() OVER (PARTITION BY p.identification_number,
                                    p.gender
                                ORDER BY
                                    p.id desc ) AS rn
                                from
                                    policy_spreadsheet_affiliates p) psa ON psa.identification_number = g.number_identification_affiliate and psa.rn = 1
                            left join (
                                select
                                    g.gis_id AS gis_id,
                                    group_concat(concat(g.casedata_code_cie, '-', g.casedata_diagnosis) separator ', ') AS diagnostico,
                                    group_concat(g.casedata_laterality separator ', ') AS laterality,
                                    group_concat((case g.origin when 'LAB' then 'LABORAL' when 'COM' then 'COMÚN' when 'NAT' then 'NO DERIVADO DEL ACCIDENTE DE TRABAJO' when 'MIX' then 'MIXTO' when 'EST' then 'EN ESTUDIO' when 'INC' then 'INCIDENTE' when 'NDE' then 'NO DERIVADO DEL EVENTO' when 'DAT' then 'DERIVADO DEL ACCIDENTE DE TRABAJO' when 'DE' then 'DERIVADO DEL EVENTO' else 'DESCONOCIDO' end) separator ', ') AS origin
                                from
                                    gis_diagnostics g
                                group by
                                    g.gis_id) dg ON dg.gis_id = g.id
                            left join (
                                select
                                    ap.parent_id AS serv_gis,
                                    ps.id AS id,
                                    ps.start_date AS start_date,
                                    ps.end_date AS end_date,
                                    ps.days_it AS days_it
                                from
                                    (((pe_it_sorts p
                                left join activities a on
                                    ((a.id = p.activity_id)))
                                left join activities ap on
                                    ((ap.id = a.parent_id)))
                                left join peit_inability_sorts ps on
                                    ((ps.pe_it_sort_id = p.id)))
                                group by
                                    ap.parent_id) pis ON pis.serv_gis = g.activity_id
                            where at.name is not NULL AND a.deleted_at IS null and g.created_at BETWEEN '$initDate' AND '$lastDate' 
                            GROUP BY g.activity_id;";

            $query = DB::select($consulta);

            foreach ($query as $item) {

                $activities[] = [
                    $item->servicio,
                    $item->type_report,
                    $item->numero_caso,
                    $item->numero_aviso,
                    $item->empresa ?? '',
                    $item->identificacion ?? '',
                    $item->fecha_caso ?? '',
                    $item->fecha_acidente ?? '',
                    $item->fecha_aviso ?? '',
                    $item->fecha_atencion ?? '',
                    $item->diagnostico ?? '',
                    $item->impacaCidadSIoNo ?? '',
                    $item->gender ?? '',
                    $item->edad ?? '',
                    $item->part_lesionada ?? '',
                ];
            }

            return Excel::create('reportGis', function ($excel) use ($activities, $headings) {
                $excel->sheet('Entries', function ($sheet) use ($activities, $headings) {
                    $sheet->row(1, $headings);
                    $sheet->fromArray($activities, null, 'A2', false, false);
                });
            })->download('xlsx');

        } catch (\Exception $e) {
            return redirect('/admin/reportes/descargas_admin')
                ->withErrors($e->getMessage())
                ->withInput();
        }

    }

}
