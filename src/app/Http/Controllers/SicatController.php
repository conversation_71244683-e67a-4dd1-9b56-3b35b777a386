<?php

namespace App\Http\Controllers;

use App\Action;
use App\Activity;
use App\Client;
use App\Service;
use App\State;
use Carbon\Carbon;
use DB;
use Illuminate\Http\Request;

class SicatController extends Controller
{
    public function listSicat(Request $req, $cpath)
    {
        $client = Client::query()->where('path', $cpath)->with(['services'])->firstOrFail();
        $services_sicat = [$req->input('service_id')];
        $state_sicat = [];
        $actions_sicat = '';
        $services_sicat_all = [
            Service::SERVICE_AT_FAMISANAR,
            Service::SERVICE_EL_FAMISANAR,
            Service::SERVICE_PCL_FAMISANAR,
            Service::SERVICE_AUDIT_FAMISANAR,
            Service::SERVICE_REHABILITATION_FAMISANAR,
            Service::SERVICE_RECOMMENDATIONS_FAMISANAR,
            Service::SERVICE_VALORATION_FAMISANAR
        ];
        $date_start = $req->input('action_start_date_submit');
        $date_end = $req->input('action_end_date_submit');

        $activities = [];
        if ($actions_sicat != '') {
            $activities = Activity::query()
                ->where('client_id', $client->id)
                ->whereIn('activities.service_id', $services_sicat)
                ->whereIn('activities.state_id', $state_sicat)
                ->whereNull('activities.audited_sicat')
                ->join(
                    DB::raw('(SELECT created_at, action_id, activity_id, author_id, id FROM activity_actions WHERE action_id in
                        (' . $actions_sicat . ') GROUP BY activity_id, created_at, action_id, author_id, id ) AS last_action_sicat'),
                    function ($join) use ($state_sicat, $date_start, $date_end, $req) {
                        $join->on('activities.id', '=', 'last_action_sicat.activity_id')
                            ->whereBetween('last_action_sicat.created_at', [$date_start, $date_end]);
                    })
                ->join('activity_actions', 'last_action_sicat.id', '=', 'activity_actions.id')
                ->join('actions', 'last_action_sicat.action_id', '=', 'actions.id')
                ->join('users', 'last_action_sicat.author_id', '=', 'users.id');
            $activities = $activities->select(
                'activities.*',
                'last_action_sicat.author_id AS author_id',
                'last_action_sicat.created_at AS last_action_sicat_created_at',
                'actions.name AS name_action',
                'users.full_name AS author_full_name'
            )
                ->inRandomOrder()
                ->limit($req->input('limit') ? $req->input('limit') : 10)
                ->get();
        }

        $new_activities = [];
        // Add only uniques users
        foreach ($activities as $activity) {
            if (!searchForAuthorId('author_id', $activity->author_id, $new_activities)) {
                $new_activity = array(
                    'id' => $activity->id,
                    'service_name' => $activity->service->name,
                    'affiliate_doc_number' => $activity->affiliate->doc_number,
                    'affiliate_full_name' => $activity->affiliate->full_name,
                    'action_name' => $activity->name_action,
                    'author_id' => $activity->author_id,
                    'author_full_name' => $activity->author_full_name,
                    'last_action_sicat_created_at' => Carbon::createFromFormat('Y-m-d H:i:s', $activity->last_action_sicat_created_at)->formatLocalized('%Y/%m/%d'),
                    'user_full_name' => $activity->user->full_name,
                    'audited_sicat' => $activity->audited_sicat,
                );
                array_push($new_activities, $new_activity);
            }
        }
        // Add users to complete limit
        if (count($new_activities) <= $req->input('limit')) {
            foreach ($activities as $activity) {
                if (!searchForAuthorId('id', $activity->id, $new_activities)) {
                    $new_activity = array(
                        'id' => $activity->id,
                        'service_name' => $activity->service->name,
                        'affiliate_doc_number' => $activity->affiliate->doc_number,
                        'affiliate_full_name' => $activity->affiliate->full_name,
                        'action_name' => $activity->name_action,
                        'author_id' => $activity->author_id,
                        'author_full_name' => $activity->author_full_name,
                        'last_action_sicat_created_at' => Carbon::createFromFormat('Y-m-d H:i:s', $activity->last_action_sicat_created_at)->formatLocalized('%Y/%m/%d'),
                        'user_full_name' => $activity->user->full_name,
                        'audited_sicat' => $activity->audited_sicat,
                    );
                    array_push($new_activities, $new_activity);
                }
                if (count($new_activities) >= $req->input('limit')) {
                    break;
                }
            }
        }
        return view('sicat', [
            'client' => $cpath,
            'activities' => $new_activities,
            'services' => $client->services()->whereIn('services.id', $services_sicat_all)->orderBy('name', 'asc')->get(),
        ]);
    }

    public function mark_with_audit(Request $req, $cpath, $id = null)
    {
        $activity =  Activity::query()->where('id', $id)->first();
        if ($activity) {
            if ($activity->audited_sicat === 'SI') {
                $activity->audited_sicat = 'NO';
            } else {
                $activity->audited_sicat = 'SI';
            }
        }
        $activity->save();
        return $activity;
    }

}

function searchForAuthorId($key_search, $id, $array) {
    foreach ($array as $key => $val) {
        if ($val[$key_search] === $id) {
            return true;
        }
    }
    return false;
}

