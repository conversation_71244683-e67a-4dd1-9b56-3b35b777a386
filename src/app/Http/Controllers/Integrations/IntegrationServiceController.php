<?php

namespace App\Http\Controllers\Integrations;

use App\Http\Controllers\Controller;
use GuzzleHttp\Client;
use Illuminate\Http\Request;

class IntegrationServiceController extends Controller
{

    const TIPOIDENTIFICACION = 'NI';
    const NUMEROIDENTIFICACION = '900810402';

    const USERNAME = 'colpensiones';
    const PASSWORD = 'R3nC0lp3ns10n3s*';
    const NAMESPACE = "IntegrationService";
    const URLSERVE = "https://colpensiones.renapp.com/IntegrationService";


    public function handleRequest(Request $request)
    {

        $GLOBALS["bitacora"] = '';
        $GLOBALS["metodo"] = '';
        $arrContextOptions = stream_context_create(
            array(
                "ssl" => array(
                    "verify_peer" => false,
                    "verify_peer_name" => false,
                    'allow_self_signed' => true
                )
            )
        );

        error_log("Valores de las variables de entorno: " . env('WSDL_COLPENSIONES') . "--" . env('CLIENT_ID'));

        $wsdlURL = env('WSDL_COLPENSIONES') . '/wsdl/IntegrationService.wsdl';

        $server = new \SoapServer(
            $wsdlURL
        );

        $server->setClass('App\Http\Controllers\Integrations\IntegrationServiceBackend');

        ob_start();
        $server->handle();
        $result = ob_get_contents();

        $responseDoc = new IntegrationServiceBackend();
        $responseDoc->actualizarResponse($GLOBALS["bitacora"], $result, $GLOBALS["metodo"]);

        exit();
        ob_end_clean();
    }

    public function getRenAppJWT($payload, $secretKey)
    {

        $header = [
            'alg' => 'HS256',
            'typ' => 'JWT'
        ];

        $headerBase64 = self::base64UrlEncode(json_encode($header));
        $payloadBase64 = self::base64UrlEncode(json_encode($payload));

        $signature = hash_hmac('sha256', "$headerBase64.$payloadBase64", $secretKey, true);
        $signatureBase64 = self::base64UrlEncode($signature);

        return "$headerBase64.$payloadBase64.$signatureBase64";

    }

    public static function verifyRenAppJWT($jwt, $secretKey)
    {
        list($headerBase64, $payloadBase64, $signatureBase64) = explode('.', $jwt);

        $signature = self::base64UrlDecode($signatureBase64);
        $expectedSignature = hash_hmac('sha256', "$headerBase64.$payloadBase64", $secretKey, true);

        if ($signature !== $expectedSignature) {
            throw new \Exception('La firma no es válida');
        }

        $payload = json_decode(self::base64UrlDecode($payloadBase64), true);

        if (isset($payload['exp']) && $payload['exp'] < time()) {
            throw new \Exception('El token ha expirado');
        }

        return $payload;
    }

    private static function base64UrlEncode($data)
    {
        return rtrim(strtr(base64_encode($data), '+/', '-_'), '=');
    }

    private static function base64UrlDecode($data)
    {
        return base64_decode(strtr($data, '-_', '+/'));
    }

    public function requestRenAppApi($url, $method, $data = null)
    {
        try {
            $secretKey = config('app.jwt_secret_ren', '');
            if (empty($secretKey)) {
                throw new \Exception('La clave secreta de RenApp no ha sido configurada');
            }

            $payload = [
                'iat' => time(),
                'exp' => time() + 3600, // Expira en 1 hora
                'data' => [
                    'userId' => \Auth::id(),
                    'username' => \Auth::user()->username ?? ''
                ]
            ];

            $jwt = $this->getRenAppJWT($payload, $secretKey);

            $client = new Client([
                'timeout' => 30, // tiempo de espera en segundos
            ]);


            $options = [
                'headers' => [
                    'Content-Type' => 'application/json',
                    'Authorization' => 'Bearer ' . $jwt,
                ],
            ];

            if ($method === 'POST') {
                $options['json'] = $data;
            } elseif ($method === 'GET' && $data !== null) {
                $options['query'] = $data;
            }

            $response = $method === 'POST' ? $client->post($url, $options) : $client->get($url, $options);
            $data = json_decode($response->getBody(), true);
            return response()->json($data);

        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Error al consumir la API: ' . $e->getMessage(),
                'code' => $e->getCode(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ], 500);
        }
    }

}