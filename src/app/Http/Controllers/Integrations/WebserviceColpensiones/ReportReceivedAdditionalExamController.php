<?php

namespace App\Http\Controllers\Integrations\WebserviceColpensiones;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class ReportReceivedAdditionalExamController extends Controller
{
    /**
     * @param $obj
     * @return array
     * @description Función que trae y manipula los datos obtenidos en el metodo InformarRecExamenAdicional
     * <AUTHOR>
     * @date 2023-02-27
     */
    public static function ReportReceivedAdditionalExam($obj){
        try{
            $message = "No se ha definido funcionalidad";
            $codeError = 1;
            $responseType = "Success Message";
        } catch (\Exception $e) {
            $message = "Error: ".$e->getMessage();
            $codeError = 2;
            $responseType = "Exception error";
        }
        return array('CodeError' => $codeError,
            'DescriptionResponse' => $message,
            'ResponseType' => $responseType);
    }
}
