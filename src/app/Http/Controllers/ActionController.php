<?php

namespace App\Http\Controllers;

use App\Action;
use App\ActionField;
use App\Actions\ActionCotizacionSort;
use App\Actions\ActionGisSort;
use App\Actions\ActionPeaffiliatepayments;
use App\Actions\ActionPeexpenserecognition;
use App\Actions\ActionPeIpSort;
use App\Actions\ActionPeItSort;
use App\Actions\ActionPemptsort;
use App\Actions\ActionPolizaSort;
use App\ActionServiceState;
use App\Activity;
use App\ActivityAction;
use App\ActivityActionDelete;
use App\ActivityActionDocument;
use App\ActivityActionField;
use App\ActivityIt;
use App\ActivityPcl;
use App\BusySchedule;
use App\Client;
use App\CorrespondenceItem;
use App\DeterminationIt;
use App\DeterminationItInability;
use App\DeterminationItInabilityFrac;
use App\ExpressionDisagreement;
use App\Holiday;
use App\Http\Controllers\Integrations\SlackController;
use App\Http\Controllers\Services\PeIpSortController;
use App\Http\Controllers\Services\PeMptSortController;
use App\Http\Controllers\Services\PolicySortController;
use App\InvalidityStatePfour;
use App\JR;
use App\Mail\SendDocument;
use App\Mail\SendDocumentDataBase;
use App\MailTemplates\Constants\Templates;
use App\MailTemplates\TemplateBuilder;
use App\Meeting;
use App\MeetingJnciControversy;
use App\PaymentBase;
use App\PaymentBaseInability;
use App\Pcl;
use App\PeIpSort;
use App\PeitInabilitySort;
use App\PolicySortCollection;
use App\RuleAction;
use App\Service;
use App\ServiceState;
use App\State;
use App\StateAction;
use App\States\StatePeItSort;
use App\User;
use Carbon\Carbon;
use Carbon\CarbonPeriod;
use Illuminate\Http\File;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use PDF;
use App\Actions\ActionPolicySortCollection;
use App\Http\Controllers\Tables\MailBoardController;

class ActionController extends Controller
{

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function deleteAction(Request $req, $cpath, $id)
    {
        Client::where('path', $cpath)->firstOrFail();
        $activityAction = ActivityAction::findOrFail($id);
        $action = $activityAction->action;

        DB::beginTransaction();

        try {

            $activity = $activityAction->activity;
            $activity->user_id = $activityAction->old_user_id;
            $activity->state_id = $activityAction->old_state_id;
            $activity->save();

            $deletes = new ActivityActionDelete;
            $deletes->activity_action_id = $activityAction->id;
            $deletes->author_id = Auth::id();
            $deletes->deleted_date = Carbon::now();
            $deletes->activity_id = $activity->id;
            $deletes->action_id = $activityAction->action_id;
            $deletes->save();

            $follow = new ActivityAction;
            $follow->activity_id = $activity->id;
            $follow->action_id = Action::FOLLOW;
            $follow->old_state_id = $activityAction->new_state_id;
            $follow->new_state_id = $activity->state_id;
            $follow->author_id = Auth::id();
            $follow->old_user_id = $activityAction->new_user_id;
            $follow->new_user_id = $activity->user_id;
            $follow->description = 'El usuario <b>' . Auth::user()->full_name . '</b> eliminó la acción <b>' . $activityAction->action->name . '</b> en la fecha <b>' . Carbon::now() . '</b>';
            $follow->save();

            $activityAction->correspondences()->whereHas('correspondence', function ($query) {
                return $query->where('closed', false);
            })->delete();

            $activityAction->delete();

            if ($action->dictum_dates && $activity->dictum) {
                foreach (explode(',', $action->dictum_dates) as $dd) {
                    $activity->dictum->setAttribute($dd, null);
                }
                $activity->dictum->save();
            }

            DB::commit();
        } catch (Exception $e) {
            DB::rollback();
        }

        return redirect('servicio/' . $activity->id);
    }

    public function validateNewUser($activityAction, $action)
    {
        $state = State::findOrFail($activityAction->new_state_id);
        if ($state->autoassign_to_area_id) {
            if (!in_array($action->id, [Action::FOLLOW, Action::FOLLOW_PCL])) {
                $activityAction->new_user_id = RuleActionsController::getLastUserAction(
                    $action->id,
                    $state->autoassign_to_area_id
                );
            }
        }
        if ($action->return_last_user_action_id) {
            $lastActivityAction = ActivityAction::where('activity_id', $activityAction->activity_id)
                ->where('action_id', $action->return_last_user_action_id)
                ->whereNull('deleted_at')
                ->orderBy('id', 'desc')
                ->first();
            $activityAction->new_user_id = $lastActivityAction ? $lastActivityAction->author_id : $activityAction->new_user_id;
        }

        return $activityAction->new_user_id;
    }

    public function action(Request $req, $cpath, $id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();
        $action = Action::findOrFail($req->input('action_id'));

        if ($action->id == ActionPeItSort::RECHAZAR_PAGO_AJUSTE_INCAPACIDAD_TEMPORAL &&
            $activity->service_id == Service::SERVICE_PE_IT_SORT_MNK &&
            ($activity->state_id == StatePeItSort::EN_PAGO_DE_FRACCIONES || $activity->state_id == StatePeItSort::PENDIENTE_SOPORTE_DE_PAGO_FRACCION)) {

            return redirect('servicio/' . $activity->id)
                ->with('error', "La acción $action->name no está permitida de manera manual mientras se encuentre en el estado 'EN PAGO DE FRACCIONES' o 'PENDIENTE DE SOPORTE DE PAGO FRACCIÓN'.");
        }

        DB::beginTransaction();

        try {

            $activityAction = new ActivityAction;
            $activityAction->activity_id = $activity->id;
            $activityAction->action_id = $action->id;
            $activityAction->old_user_id = $activity->user_id;
            $activityAction->new_user_id = $req->input('user_id') ? $req->input('user_id') : $activity->user_id;
            $activityAction->alert_date = $req->input('alert_date_submit');
            $activityAction->description = $req->input('description');
            $activityAction->old_state_id = $activity->state_id;
            $activityAction->new_state_id = $action->getStateId($activity->service_id) ? $action->getStateId($activity->service_id) : $activity->state_id;
            $activityAction->author_id = Auth::id();
            // Valida si hay que devolver a un usuario o asignar de forma automática
            if ($activity->service_id == 57 || $activity->service_id == 64) {
                if ($action->id == Action::FOLLOW_PCL) {
                    $activityAction->new_user_id = $activity->user_id;
                } else {
                    $activityAction->new_user_id = $req->input('user_id') != 1 ? $req->input('user_id') : $this->validateNewUser($activityAction, $action);
                }
            } else {
                if ($action->id == Action::FOLLOW) {
                    $activityAction->new_user_id = $activity->user_id;
                } else {
                    $activityAction->new_user_id = $this->validateNewUser($activityAction, $action);
                }
            }
            $activityAction->save();
            $documents = null;
            $emails = null;
            $date = null;
            $time = null;
            $subjectEmail = null;
            $name_ips = null;
            $address = null;
            $acudienteName = null;
            $acudientePhone = null;
            $acudienteKinship = null;
            $companionName = null;
            $companionPhone = null;
            $companionKinship = null;
            $button = null;

            foreach ($action->fields as $field) {
                $inputName = strtolower(str_replace(' ', '_', $field->name));

                if ($field->type == 'file') {
                    $value = $req->input("fields.{$inputName}");
                    $documents = $value;
                } else if ($field->type == 'date') {
                    $value = $req->input("fields.{$inputName}");
                    $date = $value;
                } else if ($field->type == 'time') {
                    $value = $req->input("fields.{$inputName}");
                    $time = $value;
                } else if ($field->type == 'button') {
                    $value = $req->input("fields.{$inputName}");
                    $button = $value;
                } else if ($field->type == 'call') {

                    // @todo Integrate with Call System

                    $value = $req->input("fields.{$inputName}");
                } else if ($field->type == 'multiple') {
                    $value = $req->input("fields.{$inputName}");
                    $value = implode(', ', $value);
                } else {
                    $value = $req->input("fields.{$inputName}");
                    if ($inputName == 'nombre_ips') {
                        $name_ips = $value;
                    }
                    if ($inputName == 'direccion_ips') {
                        $address = $value;
                    }

                    if ($inputName == 'nombre_acudiente') {
                        $acudienteName = $value;
                    }
                    if ($inputName == 'telefono_acudiente') {
                        $acudientePhone = $value;
                    }
                    if ($inputName == 'parentesco_acudiente') {
                        $acudienteKinship = $value;
                    }

                    if ($inputName == 'nombre_acompanante') {
                        $companionName = $value;
                    }
                    if ($inputName == 'telefono_acompanante') {
                        $companionPhone = $value;
                    }
                    if ($inputName == 'parentesco_acompanante') {
                        $companionKinship = $value;
                    }
                }

                $activityActionField = new ActivityActionField;
                $activityActionField->activity_action_id = $activityAction->id;
                $activityActionField->action_field_id = $field->id;
                $activityActionField->value = $value;
                $activityActionField->save();

                if ($field->type == 'email') {
                    $emails = $value != '' ? $value : null;
                }

                if ($field->id == 204 || $field->id == 268) {
                    $subjectEmail = $value != '' ? $value : null;
                }
            }

            if ($action->restore_user) {
                $activity->user_id = $activity->last_action->author_id;
            } else {
                $activity->user_id = $activityAction->new_user_id;
            }

            if ($action->dictum_dates) {

                if ($activity->parent && $activity->parent->dictum) {
                    foreach (explode(',', $action->dictum_dates) as $dd) {
                        $activity->parent->dictum->setAttribute($dd, ($date ? $date : date('Y-m-d')));
                    }
                    $activity->parent->dictum->save();
                }

                if ($activity->dictum) {
                    foreach (explode(',', $action->dictum_dates) as $dd) {
                        $activity->dictum->setAttribute($dd, ($date ? $date : date('Y-m-d')));
                    }
                    $activity->dictum->save();
                }

                if ($activity->pcl) {
                    foreach (explode(',', $action->dictum_dates) as $dd) {
                        $activity->pcl->setAttribute($dd, ($date ? $date : date('Y-m-d')));
                    }
                    $activity->pcl->save();
                }

                if ($activity->disability) {
                    foreach (explode(',', $action->dictum_dates) as $dd) {
                        $activity->disability->setAttribute($dd, ($date ? $date : date('Y-m-d')));
                    }
                    $activity->disability->save();
                }
            }
            //Poliza Sort
            if(Service::checkService($activity->service_id,Service::SERVICE_POLICY_SORT_MNK)){
                //Si ejecutan la acción de anulación de poliza anulamos la cotizacion
                if($req->action_id == ActionPolizaSort::ANULAR_POLIZA_SORT){
                     $this->create(
                        $activity->parent_id,
                        ActionCotizacionSort::REPORTAR_COTIZACION_ANULADA,
                        'Se ejecuto la acción reportar cotización caducada con la acción manual de anular póliza'
                    );

                    $collection_activity = Activity::where('parent_id', $activity->id)->where('service_id', 76)
                        ->first();


                    $this->create(
                        $collection_activity->id,
                        ActionPolicySortCollection::ANULAR_PAGO_POLIZA_SORT,
                        'Se ejecuto la acción anular pago póliza sort con la acción manual de anular póliza'
                    );


                    $collection_activity->policy_sort_collection->payment_status = "recibo-anulado";
                    $collection_activity->policy_sort_collection->save();


                }
            }
            //Cotizacion Sort
            if (Service::checkService($activity->service_id, Service::SERVICE_QUOTATIONS_MNK)) {
                if ($action->id == ActionCotizacionSort::SOLICITAR_COTIZACION_ACT_ALTO_RIESGO || $action->id == ActionCotizacionSort::SOLICITAR_COTIZACION) {
                    $quotation = $activity->quotation;
                    //Json de actividades economica
                    $jsonSource = ($activity->quotation->economic_activity == 'public') ? '/js/economic_activity/public.json' : '/js/economic_activity/private.json';
                    $json = file_get_contents(public_path($jsonSource));
                    $economicActivities = json_decode($json, true);

                    //Se transforma a una collección en laravel
                    $activity_economic_name = collect($economicActivities)->firstWhere('CODE', $activity->quotation->activity_economic_id)['ACTIVITY_NAME'];
                    $activity_policy = Activity::where('parent_id', $activity->id)->where('service_id', Service::SERVICE_POLICY_SORT_MNK)->first();
                    $work_modality_id = '';
                    if ($activity_policy) {
                        $work_modality_id = $activity_policy->policy_sort->work_modality_id;
                    }

                    //capturar acción "Solicitar cotización" de la actividad cotización, la acción más antigua
                    $actionCreatedAt = ActivityAction::where('action_id', Action::SOLICITAR_COTIZACION)
                        ->where('activity_id', $activity->id)
                        ->orderBy('created_at', 'asc')->first();

                    //declarar valores para las fechas: Fecha de cotización
                    $quotation_date = $actionCreatedAt->created_at ?? "";

                    // Preparar los datos para la vista
                    $data = [
                        'activity' => $activity,
                        'quotation' => $activity->quotation,
                        'affiliate' => $activity->affiliate,
                        'quotation_date' => $quotation_date,
                        'hidden_contacto' => true,
                        'activity_economic_name' => $activity_economic_name,
                        'work_modality_id' => $work_modality_id
                    ];

                    // Renderizar la vista como PDF
                    $pdf = \PDF::loadView('services.plantilla.docs.cotizaciones_resume_pdf', $data);

                    $files = array();

                    $document = 'Cotización_SORT';

                    //Cargamos el archivo en S3
                    \Storage::disk('s3')
                        ->put("activity_action_document/{$document}_{$quotation->id}.pdf", $pdf->output());

                    //guardamos en activity_action_documents
                    $activityActionDocument = new ActivityActionDocument();
                    $activityActionDocument->activity_action_id = $activityAction->id;
                    $activityActionDocument->name = $document;
                    $activityActionDocument->path = "activity_action_document/{$document}_{$quotation->id}.pdf";
                    $activityActionDocument->save();

                    //formamos archivo
                    $files[] = [
                        'type' => 'pdf',
                        'path' => "activity_action_document/{$document}_{$quotation->id}.pdf",
                        'name' => $document . '_' . $quotation->id . '.pdf',
                    ];

                    $nameIntermediary = mb_convert_case(mb_strtolower( $quotation->advisor_name ?? ''), MB_CASE_TITLE, "UTF-8");
                    $nameTaker = mb_convert_case(mb_strtolower( $activity->affiliate->first_name . ' ' .$activity->affiliate->last_name), MB_CASE_TITLE, "UTF-8");

                    $subject = 'Cotización de Seguro Obligatorio de Riesgos del Trabajo';

                        $text = "¡Buen día, $nameIntermediary!
                        
                        Nos complace adjuntarle la cotización del Seguro Obligatorio de Riesgos del Trabajo que nos ha solicitado para el cliente $nameTaker, en la cual podrá encontrar todos los detalles y condiciones.
            
                        Si tiene alguna consulta o necesita más detalles sobre la cotización, por favor, contáctenos al correo electrónico <EMAIL> o al teléfono 4102-7600. ¡Será un gusto servirle!
                        
                        ¡Agradecemos sinceramente su preferencia y confianza en nosotros! Nuestro propósito es fortalecer la prevención en salud y seguridad laboral del país.
                    ";

                    $emails = [$quotation->email];
                    $emailString = implode(',', $emails);

                    // Enviar el correo el documento adjunto
                    $mailSent = new SendDocumentDataBase(
                        $emailString,
                        $subject,
                        "<EMAIL>",
                        "Cotización de Seguro Obligatorio de Riesgos del Trabajo",
                        [
                            "text" => $text,
                            "sender" => 'mnk aseguramiento'
                        ],
                        "<EMAIL>",
                        $files,
                        "send_document_db",
                        $activity->client,
                        $req->host,
                        $activity->id,
                        $activityAction->id
                    );

                    // Capturar el resultado del envío
                    $result = $mailSent->sendMail();

                    //Registramos los datos del correo enviado para la trazabilidad
                    $mailBoardController = new MailBoardController();
                    $mailBoardController->createRegisterMail(
                        $activity->id,
                        $activity->service->id, 
                        '', 
                        'Intermediario', 
                        $nameIntermediary, 
                        $activity->affiliate->doc_number, 
                        $subject, 
                        $text,
                        $emails, 
                        $result,
                        $files
                    );
                }
                $activity->user_id = $activityAction->old_user_id;
                $activity->save();
            }
            //Cobros Poliza sort
            if (Service::checkService($activity->service_id, Service::SERVICE_POLICY_SORT_COLLECTION)) {
                if ($action->id == Action::REPORTAR_PAGO_EMISION_POLIZA_REALIZADO) {
                    $policy_sort_collection = PolicySortCollection::query()
                        ->where('activity_id', $activity->id)
                        ->first();
                    $policy_sort_collection->payment_status = 'paid';
                    $policy_sort_collection->save();
                    //Llamar la accion REPORTAR PAGO REALIZADO del serivicio padre POLIZA SORT
                }
                if ($action->id == Action::REPORTAR_PAGO_AUMENTO_DE_SEGURO) {
                    $policy_sort_collection = PolicySortCollection::query()
                        ->where('activity_id', $activity->id)
                        ->first();
                    if (!$policy_sort_collection) {
                        $policy_sort_collection = new PolicySortCollection ();
                        $policy_sort_collection->activity_id = $activity->id;
                    }
                    $policy_sort_collection->accumulatedPaidPremiumValue = ('accumulated_premium');
                    $policy_sort_collection->lastPaymentDate = Carbon::now();
                    $policy_sort_collection->save();
                    //Llamar la accion REPORTAR PAGO REALIZADO del serivicio padre POLIZA SORT
                }
            }
            // PE Reconocimiento de Gastos REPORTAR RECHAZO DE LA SOLICITUD
            if (Service::checkService($activity->service_id, Service::SERVICE_PE_RECOGNITION_EXPENSES_MNK)) {
                if ($action->id == ActionPeexpenserecognition::REPORTAR_RECHAZO_DE_LA_SOLICITUD) {
                    //Proceso para envío del correo tras ejecutar acción de forma manual
                    $email = $activity->affiliate->email;
                    $subject = "REPORTAR RECHAZO DE LA SOLICITUD";
                    $client_id = $client->id;
                    $mailSent = new SendDocumentDataBase(
                        $email,
                        $subject,
                        "<EMAIL>",
                        "Reportar rechazo de la solicitud",
                        [
                            "text" => 'Pendiente cuerpo de correo',
                            "sender" => 'Pendiente'
                        ],
                        "<EMAIL>",
                        [], // Sin archivos adjuntos
                        "send_document_db",
                        $client_id,
                        $req->getHost(),
                        $activity->id,
                        $activityAction->id,
                        $activity->service->id
                    );
                }
            }
            // PE IT Sort Acción reportar documentos incompletos - Pendiente validar documentos adjuntos
            if (Service::checkService($activity->service_id, Service::SERVICE_PE_IT_SORT)) {
                if ($action->id == ActionPeItSort::REPORTAR_DOCUMENTOS_INCOMPLETOS) {
                    //Proceso para envío del correo tras ejecutar acción de forma manual
                    $email = $activity->affiliate->email;
                    $subject = "REPORTAR DOCUMENTOS INCOMPLETOS";
                    $client_id = $client->id;
                    $mailSent = new SendDocumentDataBase(
                        $email,
                        $subject,
                        "<EMAIL>",
                        "Reportar documentos incompletos",
                        [
                            "text" => 'Pendiente cuerpo de correo',
                            "sender" => 'MNK'
                        ],
                        "<EMAIL>",
                        [], // Sin archivos adjuntos
                        "send_document_db",
                        $client_id,
                        $req->getHost(),
                        $activity->id,
                        $activityAction->id,
                        $activity->service->id
                    );
                    // Enviar el correo
                    $mailSent->sendMail();
                }

                if ($action->id == ActionPeItSort::REPORTAR_PAGO_TOTAL_INCAPACIDAD) {// ME-1351
                    $client_id = $client->id;
                    $activityMedicalSort = $activity->parent_activity;
                    $activityGisSort = $activityMedicalSort->parent_activity;
                    $activityPolicySort = $activityGisSort->parent_activity;
                    $nameWorker = mb_convert_case(mb_strtolower(optional(optional($activityPolicySort)->affiliate)->full_name, 'UTF-8'), MB_CASE_TITLE, 'UTF-8');
                    $nameAffiliate = mb_convert_case(mb_strtolower(optional($activityGisSort->affiliate)->full_name ?? '', 'UTF-8'), MB_CASE_TITLE, 'UTF-8');
                    $eventDate = Carbon::parse($activityGisSort->gis_sort->date_accident)->format('d/m/Y');
                    $periodStart = Carbon::parse($activity->PeItSort->inabilitySort->start_date)->format('d/m/Y');
                    $periodEnd = Carbon::parse($activity->PeItSort->inabilitySort->end_date)->format('d/m/Y');

                    $emailData = TemplateBuilder::build(
                        Templates::TEMPORARY_DISABILITY_PAYMENT,
                        [
                            'name' => $nameWorker ?? '',
                            'case_number' => $activity->PeItSort->id,
                            'policy_sort' => $activityPolicySort->policy_sort->formatSortNumber(),
                            'connector' => $activityPolicySort->affiliate->gender === 'F' ? 'de la trabajadora' : 'del trabajador',
                            'worker_name' => $nameAffiliate,
                            'worker_identification' => $activity->PeItSort->number_ide_pe,
                            'event_date' => $eventDate,
                            'period_start' => $periodStart,
                            'period_end' => $periodEnd,
                        ]
                    );

                    // Agrega el email principal de notificaciones y todos los adicionales
                    $emails = PolicySortController::getAdditionalNotificationEmails(
                        $activityPolicySort->policy_sort->id,
                        [
                            $activity->affiliate->email
                        ]
                    );

                    $mailSent = new SendDocumentDataBase(
                        implode(',', $emails),
                        $emailData['subject'],
                        "<EMAIL>",
                        $emailData['subject'],
                        [
                            "text" => $emailData['body'],
                            "sender" => $emailData['sender']
                        ],
                        "<EMAIL>",
                        [], // Sin archivos adjuntos
                        "send_document_db",
                        $client_id,
                        $req->getHost(),
                        $activity->id,
                        $activityAction->id,
                        $activity->service->id
                    );
                    $mailSent->sendMail();
                }
                // PE IT APROBAR_PAGO_AJUSTE_INCAPACIDAD_TEMPORAL
                if ($action->id == ActionPeItSort::APROBAR_PAGO_AJUSTE_INCAPACIDAD_TEMPORAL) {
                    $activityPm = $activity->parent;
                    $activityGisSort = $activityPm->parent;
                    $activityPolicySort = $activityGisSort->parent;
                    $policy_sort = $activityPolicySort->policy_sort;

                    $emails = [
                        $activity->affiliate->email,
                        $policy_sort->activity->affiliate->email
                    ];

                    $validEmails = array_filter($emails, function ($email) {
                        return !empty($email) && filter_var($email, FILTER_VALIDATE_EMAIL);
                    });

                    $emailString = implode(',', $validEmails);

                    $nameAffiliate = mb_convert_case(mb_strtolower($activity->PeItSort->name_affiliated_pe ?? ''), MB_CASE_TITLE, "UTF-8");
                    $case_number = $activityGisSort->gis_sort->formatCaseNumber();
                    $date_accident = $activityGisSort->gis_sort->date_accident;
                    $peItInability= PeitInabilitySort::where('pe_it_sort_id', $activity->PeItSort->id)->first();
                    $periodStart = Carbon::parse($peItInability->start_date)->format('d/m/Y');
                    $periodEnd = Carbon::parse($peItInability->end_date)->format('d/m/Y');
                    $currencySymbol = '';
                    switch ($policy_sort->type_currency) {
                        case 'USD':
                            $currencySymbol = '$';
                            break;
                        case 'CRC':
                            $currencySymbol = '₡';
                            break;
                        default:
                            $currencySymbol = '';
                            break;
                    }
                    $mountValue = $currencySymbol . ' ' . number_format($peItInability->it_difference ?? 0, 2, ',', '');

                    $emailData = TemplateBuilder::build(
                        Templates::TEMPORARY_DISABILITY_ADJUSTMENT_PAYMENT_NOTIFICATION,
                        [
                            'name' => $nameAffiliate,
                            'number_case' => $case_number,
                            'event_date' => $date_accident,
                            'period_start' => $periodStart,
                            'period_end' => $periodEnd,
                            'mount' => $mountValue,
                        ]
                    );

                    $mailSent = new SendDocumentDataBase(
                        $emailString,
                        $emailData['subject'],
                        "<EMAIL>",
                        $emailData['subject'],
                        [
                            "text" => $emailData['body'],
                            "sender" => $emailData['sender']
                        ],
                        "<EMAIL>",
                        [],
                        "send_document_db",
                        $client->id,
                        $req->getHost(),
                        $activity->id,
                        $activityAction->id,
                        $activity->service_id
                    );
                    $mailSent->sendMail();
                }
            }
            // PE IP Sort Acción reportar documentos incompletos - Pendiente validar documentos adjuntos
            if (Service::checkService($activity->service_id, Service::SERVICE_PE_IP_SORT)) {

                if ($action->id == ActionPeIpSort::REPORTAR_DOCUMENTOS_INCOMPLETOS) {

                    $peipSort = PeIpSort::where('activity_id', $activity->id)->first();

                    if ($peipSort->calc_additional) {

                        //Proceso para envío del correo tras ejecutar acción de forma manual
                        $email = $activity->affiliate->email;
                        $subject = "REPORTAR DOCUMENTOS INCOMPLETOS";
                        $client_id = $client->id;
                        $mailSent = new SendDocumentDataBase(
                            $email,
                            $subject,
                            "<EMAIL>",
                            "Reportar documentos incompletos",
                            [
                                "text" => $peipSort->calc_additional,
                                "sender" => 'MNK'
                            ],
                            "<EMAIL>",
                            [], // Sin archivos adjuntos
                            "send_document_db",
                            $client_id,
                            $req->getHost(),
                            $activity->id,
                            $activityAction->id,
                            $activity->service->id
                        );
                        // Enviar el correo
                        $mailSent->sendMail();
                    }
                }
            }
            // PE RECONOCIMIENTO DE GASTOS Acción reportar documentos incompletos
            if (Service::checkService($activity->service_id, Service::SERVICE_PE_RECOGNITION_EXPENSES_MNK)) {
                if ($action->id == ActionPeexpenserecognition::REPORTAR_DOCUMENTOS_INCOMPLETOS) {
                    //Proceso para envío del correo tras ejecutar acción de forma manual
                    $email = $activity->affiliate->email;
                    $subject = "REPORTAR DOCUMENTOS INCOMPLETOS";
                    $client_id = $client->id;
                    $mailSent = new SendDocumentDataBase(
                        $email,
                        $subject,
                        "<EMAIL>",
                        "Reportar documentos incompletos",
                        [
                            "text" => 'Buen día, estos son los documentos que hacen falta: ' . $activityAction->description,
                            "sender" => 'MNK'
                        ],
                        "<EMAIL>",
                        [], // Sin archivos adjuntos
                        "send_document_db",
                        $client_id,
                        $req->getHost(),
                        $activity->id,
                        $activityAction->id,
                        $activity->service->id
                    );
                    // Enviar el correo
                    $mailSent->sendMail();
                }
                if ($action->id == ActionPeexpenserecognition::APROBACION_PAGO_RECONOCIMIENTO_DE_GASTOS) {
                    //Creación de servicio pe ip
                    $newActiviy = new Activity;
                    $newActiviy->parent_id = $activity->id;
                    $newActiviy->client_id = $client->id;
                    $newActiviy->service_id = Service::SERVICE_AFFILIATE_PAYMENT_MNK;
                    $newActiviy->affiliate_id = $activity->affiliate_id;
                    $newActiviy->user_id = Auth::id();
                    $newActiviy->state_id = State::REGISTRADO;
                    $newActiviy->save();

                    $this->create(
                        $newActiviy->id,
                        ActionPeaffiliatepayments::REPORTAR_RECONOCIMIENTO_DE_GASTOS,
                        'REPORTAR RECONOCIMIENTO DE GASTOS '
                    );

                }
                if ($action->id == ActionPeexpenserecognition::REPORTAR_PAGO_RECONOCIMIENTO_DE_GASTOS) {//ME-1088
                    $activityGisSort = $activity->parent_activity;
                    $activityPolicySort = $activityGisSort->parent_activity;
                    $symbol = $activityPolicySort->policy_sort && $activityPolicySort->policy_sort->type_currency === 'USD' ? '$' : '₡';
                    $service = $activity->pe_recognition_expenses && $activity->pe_recognition_expenses->invoice_concepts
                        ? (\App\Providers\AppServiceProvider::$TYPE_EXPENSE[$activity->pe_recognition_expenses->invoice_concepts] ?? '')
                        : '';
                    $mount = $symbol . ' ' .number_format($activity->pe_recognition_expenses->invoices_expense_recognition ?? 0, 2, ',', '.');
                    $table = "<table style='border: 1px solid black; border-collapse: collapse;'>";
                    $table .= "<tr>";
                    $table .= "<th style='background-color: #8BC34A; color: black; border: 1px solid black; padding: 5px;' >Servicio brindado </th>";
                    $table .= "<th style='background-color: #8BC34A; color: black; border: 1px solid black; padding: 5px;' >Monto </th>";
                    $table .= "</tr>";

                    $table .= "<tr>";
                    $table .= "<td style='border: 1px solid black; padding: 5px;' >$service</td>";
                    $table .= "<td style='border: 1px solid black; padding: 5px;' >$mount</td>";
                    $table .= "</tr>";
                    $table .= "</table>";
                    $emailData = TemplateBuilder::build(
                        Templates::EXPENSE_REIMBURSEMENT_PAYMENT_CONFIRMATION_EMAIL,
                        [
                            'number_case' => $activityGisSort->gis_sort->formatCaseNumber(),
                            'name' => ucwords(mb_strtolower($activity->affiliate->first_name)),
                            'date_pay' => Carbon::parse($activity->created_at)->format('d/m/Y'),
                            'table' => preg_replace('/\s+/', ' ', $table)
                        ]
                    );

                    $mailSent = new SendDocumentDataBase(
                        $activity->affiliate->email,
                        $emailData['subject'],
                        "<EMAIL>",
                        $emailData['subject'],
                        [
                            "text" => $emailData['body'],
                            "sender" => $emailData['sender']
                        ],
                        "<EMAIL>",
                        [], // Sin archivos adjuntos
                        "send_document_db",
                        $activity->client_id,
                        $req->getHost(),
                        $activity->id,
                        $activityAction->id,
                        $activity->service_id
                    );
                    $mailSent->sendMail();
                }
            }
            // PE IP Sort -> Acción REPORTAR CALIFICACION PCG
            // TODO: Esta acción se ejecutara de forma manual mientras se crea en el servicio de GIS
            if (Service::checkService($activity->service_id, Service::SERVICE_GIS_SORT_MNK)) {
                if ($action->id == ActionGisSort::REPORTAR_CALIFICACION_PCG) {

                    //Creación de servicio pe ip
                    $peipSortActivity = new Activity;
                    $peipSortActivity->parent_id = $activity->id;
                    $peipSortActivity->client_id = $client->id;
                    $peipSortActivity->service_id = Service::SERVICE_PE_IP_SORT_MNK;
                    $peipSortActivity->affiliate_id = $activity->affiliate_id;
                    $peipSortActivity->user_id = Auth::id();
                    $peipSortActivity->state_id = State::REGISTRADO;
                    $peipSortActivity->save();

                    //llamar acción Registrar ip integral
                    $peIpSortController = new PeIpSortController();
                    $peIpSortController->registerIpIntegrado($req, $cpath, $activity->id, $peipSortActivity);
                }

                //Envio de correo Recordatorio de reporte de Accidente
                if ($action->id == ActionGisSort::SOLICITAR_REPORTE_FORMAL_DEL_CASO){
                    $activity_policy = $activity->parent;

                    $emailBuild = TemplateBuilder::build(
                        Templates::ACCIDENT_REPORT_REMINDER,
                        [
                            'policy_sort' => $activity_policy->policy_sort->formatNumberConsecutive(),
                            'name' => mb_convert_case(mb_strtolower($activity_policy->affiliate->first_name ?? ''), MB_CASE_TITLE, "UTF-8"),
                            'consecutive_gis' => $activity->gis_sort->consecutive_gis,
                            'date_accident' => Carbon::parse($activity->gis_sort->date_accident)->format('d/m/Y'),
                            'full_name_worker' => mb_convert_case(mb_strtolower($activity->affiliate->full_name ?? ''), MB_CASE_TITLE, "UTF-8"),
                            'connector' => $activity->affiliate->gender === 'F' ? 'a la trabajadora' : 'al trabajador',
                            'doc_number' => $activity->affiliate->doc_number,
                            'connector_two' => $activity->affiliate->gender === 'F' ? 'doña' : 'don',
                            'name_worker' => mb_convert_case(mb_strtolower($activity->affiliate->first_name ?? ''), MB_CASE_TITLE, "UTF-8"),
                            'created_at' => Carbon::parse($activityAction->created_at)->addDays(8)->formatLocalized('%e de %B del %Y'),
                        ]
                    );
                    
                    // Agrega el email principal de notificaciones y todos los adicionales
                    $notiEmails = PolicySortController::getAdditionalNotificationEmails(
                        $activity_policy->policy_sort->id
                    );
                    $strEmails = implode(',', $notiEmails);

                    $mailSent = new SendDocumentDataBase(
                        $strEmails,
                        $emailBuild['subject'],
                        "<EMAIL>",
                        $emailBuild['subject'],
                        [
                            "text" => $emailBuild['body'],
                            "sender" => $emailBuild['sender']
                        ],
                        "<EMAIL>",
                        [],
                        "send_document_db",
                        $client,
                        request()->getHost(),
                        $activity->id,
                        $activityAction->id
                    );

                    $mailSent->sendMail();
                }
            }
            // PE IP Sort Acción Aprobación pagos de renta
            if (Service::checkService($activity->service_id, Service::SERVICE_PE_IP_SORT)) {
                if ($action->id == ActionPeIpSort::APROBACION_PAGOS_DE_RENTA) {
                    //llamar acción Registrar ip integral
                    $peIpSortController = new PeIpSortController();
                    $peIpSortController->approvalPayment($req, $cpath, $activity->id);
                }
            }
            // PE MPT Sort -> Acción APROBACIÓN PAGO MESADA
            if (Service::checkService($activity->service_id, Service::SERVICE_PE_MPT_SORT)) {
                if ($action->id == ActionPemptsort::APROBACION_PAGO_MESADA) {
                    //Creación de servicio pagos afiliados
                    $affiliatePaymentActivity = new Activity;
                    $affiliatePaymentActivity->parent_id = $activity->id;
                    $affiliatePaymentActivity->client_id = $client->id;
                    $affiliatePaymentActivity->service_id = Service::SERVICE_AFFILIATE_PAYMENT_MNK;
                    $affiliatePaymentActivity->affiliate_id = $activity->affiliate_id;
                    $affiliatePaymentActivity->user_id = Auth::id();
                    $affiliatePaymentActivity->state_id = State::REGISTRADO;
                    $affiliatePaymentActivity->save();

                    // Creación de acción y estado
                    $this->create(
                        $affiliatePaymentActivity->id,
                        ActionPeaffiliatepayments::REPORTAR_PAGO_RENTA_POR_MUERTE_DE_PERSONA_TRABAJADORA,
                        'REPORTAR PAGO RENTA POR MUERTE DE PERSONA TRABAJADORA'
                    );
                    // Generar documento "Liquidación Mesada"
                    $documentName = " Liquidación_Mesada" . $activity->id . ".pdf";
                    $pdfContent = PDF::loadView('services.pe_death_by_working_person_sort.documents.monthly_payment_settlement', ['activity' => $activity])->output();

                    // Subir el archivo PDF a S3
                    $filePath = "pe_death_by_working_person_sort/{$documentName}";
                    Storage::disk('s3')->put($filePath, $pdfContent);

                    // Formar archivo para adjuntar en el correo
                    $files[] = [
                        'type' => 'pdf',
                        'path' => $filePath,
                        'name' => $documentName,
                    ];

                    $detailDeathWorker = PeMptSortController::getDetailDeathWorker($activity);
                    $startDate = Carbon::createFromFormat('d/m/Y', $detailDeathWorker->pmtSort->start_date_of_rent_submit);
                    $endDate = $startDate->copy()->addYears(10);
                    // Crear el correo para el beneficiario o afiliado
                    $emailData = TemplateBuilder::build(
                        Templates::RENT_PAYMENT_TO_BENEFICIARIES,
                        [
                            'number_case' => $activity->parent_activity->gis_sort->consecutive_gis,
                            'beneficiary' => mb_convert_case(mb_strtolower($detailDeathWorker->peMptBeneficiaries ? $detailDeathWorker->peMptBeneficiaries[0]->name_and_surname : ''), MB_CASE_TITLE, "UTF-8"),
                            'insured_name' => mb_convert_case(mb_strtolower($activity->affiliate->full_name ?? ''), MB_CASE_TITLE, "UTF-8"),
                            'connector' => $activity->affiliate->gender === 'F' ? 'de la trabajadora' : 'del trabajador',
                            'date_of_death' => Carbon::parse($detailDeathWorker->pmtSort->pe_mpt_sort)->format('d/m/Y'),
                            'monthly_rent' => $detailDeathWorker->averageSalaryFormatted,
                            'percentage' => $detailDeathWorker->peMptBeneficiaries ? $detailDeathWorker->peMptBeneficiaries[0]->monthly_payment : '',
                            'period_start' => $startDate->formatLocalized('%e de %B del %Y'),
                            'period_end' => $endDate->formatLocalized('%e de %B del %Y'),
                            'total_rent' => $detailDeathWorker->pmtSort->annual_income,
                        ]
                    );

                    $mailSent = new SendDocumentDataBase(
                        $activity->affiliate->email,
                        $emailData['subject'],
                        "<EMAIL>",
                        "Liquidación Mesada",
                        [
                            "text" => $emailData['body'],
                            "sender" => $emailData['sender']
                        ],
                        "<EMAIL>",
                        $files,
                        "send_document_db",
                        $client->id,
                        $req->getHost(),
                        $activity->id,
                        $activityAction->id,
                        $activity->service->id
                    );
                    $mailSent->sendMail();
                }
            }

            // HAS DOCUMENT
            if ($action->documents) {
                $files = array();
                $paths = array();
                $subject = false;
                $text = '';
                foreach (explode(',', $action->documents) as $document) {
                    if ((strpos($document, 'controversy_remission_arl_employer') !== false) || (strpos($document, 'controversy_remission_affiliate_employer') !== false)) {
                        if ($activity->affiliate->employments->count() > 0) {
                            if ($activity->employment) {
                                $pdf = ActionController::generatePDF($activity, $document, false);

                                Storage::disk('s3')
                                    ->put("activity_action_document/{$document}_{$activityAction->id}.pdf", $pdf->output());

                                //ADD PASSWORD
                                //$this->addPasswordPDF($document, $activityAction, $pdf, $activity);

                                $activityActionDocument = new ActivityActionDocument;
                                $activityActionDocument->activity_action_id = $activityAction->id;
                                $activityActionDocument->name = $document;
                                $activityActionDocument->path = "activity_action_document/{$document}_{$activityAction->id}.pdf";
                                $activityActionDocument->save();

                                if (!$subject) {
                                    $subject = SendDocument::$SUBJECTS[$document] . " CASO USUARIO {$activity->affiliate->doc_type} {$activity->affiliate->doc_number}";
                                    $text = sprintf(SendDocument::$TEXTS[$document], $activity->affiliate->full_name, $activity->affiliate->doc_number);
                                }

                                $files[] = [
                                    'type' => 'pdf',
                                    'path' => "activity_action_document/{$document}_{$activityAction->id}.pdf",
                                    'name' => SendDocument::$DOCUMENTS[$document] . '.pdf',
                                ];
                                $paths[] = "activity_action_document/{$document}_{$activityAction->id}.pdf";
                            }
                        }
                    } else {
                        if (!in_array($activityAction->action_id, [Action::APPROVE_IT, Action::APPROVE_QUALITY_IT, Action::APPROVED_QUALITY_CONTROL_IT, Action::APPROVE_CONT_PCL_REI, Action::INFORM_PCL_DICTUM_APROBATION_REI])) {
                            $pdf = ActionController::generatePDF($activity, $document, false);

                            Storage::disk('s3')
                                ->put("activity_action_document/{$document}_{$activityAction->id}.pdf", $pdf->output());

                            //ADD PASSWORD
                            //$this->addPasswordPDF($document, $activityAction, $pdf, $activity);

                            $activityActionDocument = new ActivityActionDocument;
                            $activityActionDocument->activity_action_id = $activityAction->id;
                            $activityActionDocument->name = $document;
                            $activityActionDocument->path = "activity_action_document/{$document}_{$activityAction->id}.pdf";
                            $activityActionDocument->save();

                            if (!$subject) {
                                $subject = SendDocument::$SUBJECTS[$document] . " CASO USUARIO {$activity->affiliate->doc_type} {$activity->affiliate->doc_number}";
                                $text = sprintf(SendDocument::$TEXTS[$document], $activity->affiliate->full_name, $activity->affiliate->doc_number);
                            }

                            $files[] = [
                                'type' => 'pdf',
                                'path' => "activity_action_document/{$document}_{$activityAction->id}.pdf",
                                'name' => SendDocument::$DOCUMENTS[$document] . '.pdf',
                            ];
                            $paths[] = "activity_action_document/{$document}_{$activityAction->id}.pdf";
                        }
                    }
                }
                if ($action->send_targets) {

                    if ($emails != null) {
                        $emails = array_map('trim', explode(',', $emails));

                        try {
                            if (Service::checkService($activity->service_id, Service::SERVICE_CONTROVERSY) && ($action->id == Action::CONTROVERSY_REMISSION_AFFILIATE)) {
                                $affiliate_files = [];
                                foreach ($files as $file) {
                                    if (!(strpos($file['path'], 'affiliate_employer') !== false)) {
                                        $affiliate_files[] = $file;
                                    }
                                }
                                $mailSent = new SendDocumentDataBase(
                                    implode(',', $emails),
                                    $subject,
                                    "<EMAIL>",
                                    "Programa de salud - Universidad de Antioquia Convenio Ren Consultores",
                                    ["text" => $text,
                                        "sender" => 'Convenio Programa de Salud Universidad de Antioquia'],
                                    "<EMAIL>",
                                    $affiliate_files,
                                    "send_document_db",
                                    $client,
                                    $req->getHost(),
                                    $activity->id,
                                    $activityAction->id,
                                    $activity->service->id
                                );
                                $mailSent->sendMail();
                            } elseif (Service::checkService($activity->service_id, Service::SERVICE_CONTROVERSY) && ($action->id == Action::CONTROVERSY_REMISSION_ARL)) {
                                $affiliate_files = [];
                                foreach ($files as $file) {
                                    if (!(strpos($file['path'], 'arl_employer') !== false)) {
                                        $affiliate_files[] = $file;
                                    }
                                }
                                $mailSent = new SendDocumentDataBase(
                                    implode(',', $emails),
                                    $subject,
                                    "<EMAIL>",
                                    "Programa de salud - Universidad de Antioquia Convenio Ren Consultores",
                                    ["text" => $text,
                                        "sender" => 'Convenio Programa de Salud Universidad de Antioquia'],
                                    "<EMAIL>",
                                    $affiliate_files,
                                    "send_document_db",
                                    $client,
                                    $req->getHost(),
                                    $activity->id,
                                    $activityAction->id,
                                    $activity->service->id
                                );
                                $mailSent->sendMail();
                            } else {
                                $mailSent = new SendDocumentDataBase(
                                    implode(',', $emails),
                                    $subject,
                                    "<EMAIL>",
                                    "Programa de salud - Universidad de Antioquia Convenio Ren Consultores",
                                    ["text" => $text,
                                        "sender" => 'Convenio Programa de Salud Universidad de Antioquia'],
                                    "<EMAIL>",
                                    $files,
                                    "send_document_db",
                                    $client,
                                    $req->getHost(),
                                    $activity->id,
                                    $activityAction->id,
                                    $activity->service->id
                                );
                                $mailSent->sendMail();
                            }

                        } catch (Exception $e) {
                            Log::error($e->getMessage());
                        }
                    }

                    // CORRESPONDENCE
                    if ($client->path != 'mapfre') {

                        if (Service::checkService($activity->service_id, Service::SERVICE_REHABILITATION) && ($action->id == Action::REPORT_REHABILITATION_NOTIFY)) {

                            $employer_paths = [];
                            foreach ($paths as $path) {
                                if (strpos($path, 'notification') !== false) {
                                    $employer_paths[] = $path;
                                }
                            }

                            //add document to correspondence items
                            if ($documents != null) {
                                array_push($paths, $documents);
                            }

                            CorrespondenceItem::createFromActivityAction($client, $activityAction, $employer_paths, 'employer');
                            CorrespondenceItem::createFromActivityAction($client, $activityAction, $paths, 'afp');

                            $activity_action_rehabilitation = ActivityAction::where('action_id', '=', Action::REPORT_FORMAT_REHABILITATION)
                                ->where('activity_id', '=', $activity->id)
                                ->orderBy('created_at', 'DESC')
                                ->first();

                            $action_field = ActionField::where('action_id', '=', Action::REPORT_FORMAT_REHABILITATION)
                                ->where('type', '=', 'file')
                                ->first();

                            if ($activity_action_rehabilitation && $action_field) {

                                $activity_action_field = ActivityActionField::where('activity_action_id', '=', $activity_action_rehabilitation->id)
                                    ->where('action_field_id', '=', $action_field->id)
                                    ->first();

                                if ($activity_action_field) {
                                    $paths[] = $activity_action_field->value;
                                }

                            }
                            CorrespondenceItem::createFromActivityAction($client, $activityAction, $paths, 'affiliate');


                        } elseif (Service::checkService($activity->service_id, Service::SERVICE_CONTROVERSY) && ($action->id == Action::CONTROVERSY_REMISSION_AFFILIATE)) {

                            $employer_paths = [];
                            $affiliate_paths = [];
                            foreach ($paths as $path) {
                                if (strpos($path, 'affiliate_employer') !== false) {
                                    $employer_paths[] = $path;
                                } else {
                                    $affiliate_paths[] = $path;
                                }
                            }

                            CorrespondenceItem::createFromActivityAction($client, $activityAction, $employer_paths, 'employer');
                            CorrespondenceItem::createFromActivityAction($client, $activityAction, $affiliate_paths, 'arl');
                            CorrespondenceItem::createFromActivityAction($client, $activityAction, $affiliate_paths, 'afp');
                            CorrespondenceItem::createFromActivityAction($client, $activityAction, $affiliate_paths, 'jrci');
                            CorrespondenceItem::createFromActivityAction($client, $activityAction, $affiliate_paths, 'affiliate');
                        } elseif (Service::checkService($activity->service_id, Service::SERVICE_CONTROVERSY) && ($action->id == Action::CONTROVERSY_REMISSION_ARL)) {
                            $employer_paths = [];
                            $affiliate_paths = [];
                            foreach ($paths as $path) {
                                if (strpos($path, 'arl_employer') !== false) {
                                    $employer_paths[] = $path;
                                } else {
                                    $affiliate_paths[] = $path;
                                }
                            }

                            CorrespondenceItem::createFromActivityAction($client, $activityAction, $employer_paths, 'employer');
                            CorrespondenceItem::createFromActivityAction($client, $activityAction, $affiliate_paths, 'arl');
                            CorrespondenceItem::createFromActivityAction($client, $activityAction, $affiliate_paths, 'afp');
                            CorrespondenceItem::createFromActivityAction($client, $activityAction, $affiliate_paths, 'jrci');
                            CorrespondenceItem::createFromActivityAction($client, $activityAction, $affiliate_paths, 'affiliate');

                        } else {
                            CorrespondenceItem::createFromActivityAction($client, $activityAction, $paths);
                        }
                    }
                }

            } else if ($action->send_targets && $documents != null) {

                if ($client->path != 'mapfre') {
                    CorrespondenceItem::createFromActivityAction($client, $activityAction, array($documents));
                }

                $subject = $action->name . " CASO {$activity->affiliate->doc_type} {$activity->affiliate->doc_number}";
                $files = [
                    ['path' => $documents, 'name' => explode('/', $documents)[1]],
                ];

                if ($emails != null) {
                    $emails = array_map('trim', explode(',', $emails));

                    try {
                        $mailSent = new SendDocumentDataBase(
                            implode(',', $emails),
                            $subject,
                            "<EMAIL>",
                            "Programa de salud - Universidad de Antioquia Convenio Ren Consultores",
                            ["text" => '',
                                "sender" => 'Convenio Programa de Salud Universidad de Antioquia'],
                            "<EMAIL>",
                            $files,
                            "send_document_db",
                            $client,
                            $req->getHost(),
                            $activity->id,
                            $activityAction->id,
                            $activity->service->id
                        );
                        $mailSent->sendMail();
                    } catch (Exception $e) {
                        Log::error($e->getMessage());
                    }
                }
            }

            $activity->state_id = $activityAction->new_state_id;
            $activity->save();

            if (Service::checkService($activity->service_id, Service::SERVICE_CONTROVERSY)) {
                if ($activity->state_id == State::CONTROVERSY_IN_REGIONAL_BOARD && $activity->parent_id) {
                    // SET EN DICTUM_IN_REGIONAL_BOARD
                    $parent = $activity->parent;
                    $parent->state_id = State::DICTUM_IN_REGIONAL_BOARD; // IN DICTUM_IN_REGIONAL_BOARD
                    $parent->save();
                }
            }

            //PCL REI COLPENSIONES
            if (Service::checkService($activity->service_id, Service::SERVICE_PCL_COLPENSIONES) || Service::checkService($activity->service_id, Service::SERVICE_INVALIDITY_STATE_P4_COLPENSIONES)) {
                // CANCEL APPOINTMENT WITH DELETE ACTION AND SCHEDULE
                if ($action->id === Action::CANCEL_APPOINTMENT_CONT_PCL || $action->id === Action::CANCEL_APPOINTMENT_PCL) {
                    // GET ACTION APPOINTMENT
                    $activityActionAppointment = ActivityAction::query()
                        ->whereIn('action_id', [Action::REGISTER_APPOINTMENT_INFORMATION_CONT_PCL, Action::REGISTER_APPOINTMENT_INFORMATION_PCL])
                        ->where('activity_id', $activity->id)
                        ->orderBy('created_at', 'desc')
                        ->first();

                    if ($activityActionAppointment) {
                        // SAVE HISTORY DELETE APPOINTMENT
                        $deletes = new ActivityActionDelete;
                        $deletes->activity_action_id = $activityActionAppointment->id;
                        $deletes->author_id = Auth::id();
                        $deletes->deleted_date = Carbon::now();
                        $deletes->activity_id = $activity->id;
                        $deletes->action_id = $activityAction->action_id;
                        $deletes->save();

                        // DELETE ACTION APPOINTMENT
                        $activityActionAppointment->delete();

                        // DELETE SCHEDULE DOCTOR
                        $busySchedule = BusySchedule::query()
                            ->where('activity_action_id', $activityActionAppointment->id)
                            ->orderBy('created_at', 'desc')
                            ->first();

                        if ($busySchedule) {
                            $busySchedule->delete();
                        }
                        // RETURN ACTIVITY FOR THE INITIAL STATE
                        $activity->state_id = $activityActionAppointment->old_state_id;
                        $activity->user_id = $activityActionAppointment->author_id;
                        $activity->save();

                        // NEW STATE FOR ACTION
                        $activityAction->new_state_id = $activityActionAppointment->old_state_id;
                        $activityAction->new_user_id = $activityActionAppointment->author_id;
                        $activityAction->save();
                    }
                }

                if ($action->id === Action::RETURN_REJECTION_CONT_PCL_REI) {
                    $actionRejects = ActivityAction::query()
                        ->where('activity_id', $activity->id)
                        ->where('action_id', Action::REJECT_CASE_CONT_PCL)
                        ->orderBy('created_at', 'desc')
                        ->first();

                    $activityAction->new_state_id = $actionRejects->old_state_id;
                    $activityAction->new_user_id = $actionRejects->author_id;
                    $activityAction->save();

                    $activity->state_id = $actionRejects->old_state_id;
                    $activity->user_id = $actionRejects->author_id;
                    $activity->save();
                }
            }

            // IT
            if (Service::checkService($activity->service_id, Service::SERVICE_DETERMINATION_IT_COLPENSIONES)) {
                // DEVOLUCION DE ESTADO Y USUARIO CUANDO YA SE HA DEVUELTO LA SOLUCITUD DE DOCUMENTOS
                if ($action->id == Action::DOC_REQUEST_IT) {
                    $actionReturnRequestDoc = ActivityAction::query()
                        ->where('activity_id', $activity->id)
                        ->where('action_id', Action::RETURN_DOC_REQUEST_IT)
                        ->first();
                    if ($actionReturnRequestDoc) {
                        $activity->state_id = State::DOCUMENT_REQUEST_REVIEW_IT;
                        $activity->user_id = $actionReturnRequestDoc->author_id;
                        $activity->save();
                        $activityAction->new_state_id = State::DOCUMENT_REQUEST_REVIEW_IT;
                        $activityAction->new_user_id = $actionReturnRequestDoc->author_id;
                        $activityAction->save();
                    }
                }

                // DEVOLUCION DE ESTADO Y USUARIO CUANDO YA SE HA DEVUELTO EL RECHAZO
                if ($action->id == Action::TRAMITE_REJECTION_IT) {
                    $actionReturnTramiteRejection = ActivityAction::query()
                        ->where('activity_id', $activity->id)
                        ->where('action_id', Action::RETURN_TRAMITE_REJECTION_IT)
                        ->first();
                    if ($actionReturnTramiteRejection) {
                        $activity->state_id = State::REJECTION_REVIEW_IT;
                        $activity->user_id = $actionReturnTramiteRejection->author_id;
                        $activity->save();
                        $activityAction->new_state_id = State::REJECTION_REVIEW_IT;
                        $activityAction->new_user_id = $actionReturnTramiteRejection->author_id;
                        $activityAction->save();
                    }
                }
            }

            $new_activity = null;

            if ($action->new_service) {

                $new_activity = new Activity;
                if (Action::SECOND_CONTROVERSY != $action->id) {
                    $new_activity->parent_id = $activity->id;
                }
                $new_activity->client_id = $client->id;
                $new_activity->employment_id = $activity->employment_id;
                $new_activity->service_id = $action->new_service;
                $new_activity->affiliate_id = $activity->affiliate_id;

                $new_activity->user_id = $action->new_service == 10 ? 1 : Auth::id();


                $new_activity->state_id = Service::find($action->new_service)->default_state_id;
                $new_activity->save();

                if (Action::SECOND_CONTROVERSY == $action->id) {
                    if ($new_activity->dictum == null) {
                        $new_dictum = $activity->dictum->replicate();
                        $new_dictum->activity_id = $new_activity->id;
                        $new_dictum->save();

                        foreach ($activity->dictum->diagnostics as $diagnostic_cal) {
                            $new_diagnostic = $diagnostic_cal->replicate();
                            $new_diagnostic->dictum_id = $new_dictum->id;
                            $new_diagnostic->save();
                        }
                    }

                    $new_activity_action = new ActivityAction;
                    $new_activity_action->description = 'Se crea esta segunda controversia de la calificación # ' . $activity->id . ' del mismo afiliado.';
                    $new_activity_action->action_id = Action::FOLLOW;
                    $new_activity_action->activity_id = $new_activity->id;
                    $new_activity_action->old_state_id = $new_activity->state_id;
                    $new_activity_action->new_state_id = $new_activity->state_id;
                    $new_activity_action->old_user_id = $new_activity->user_id;
                    $new_activity_action->new_user_id = $new_activity->user_id;
                    $new_activity_action->author_id = 1;
                    $new_activity_action->save();

                }

                if ($activity->id == Service::SERVICE_AUDIT_PRI_ALIANSALUD) {
                    $new_activity_action = new ActivityAction;

                    if ($action->id == Action::REQUEST_RHB_PRI) {
                        $new_activity_action->description = 'Solicitud de concepto creada desde el Programa PRI.';
                    } elseif ($action->id == Action::REQUEST_VALORATION_ML_PRI) {
                        $new_activity_action->description = 'Solicitud de valoración creada desde el Programa PRI.';
                    } elseif ($action->id == Action::REQUEST_RECOMMENDATION_PRI) {
                        $new_activity_action->description = 'Solicitud de recomendación creada desde el Programa PRI.';
                    } else {
                        $new_activity_action->description = 'Solicitud de calificación creada desde el Programa PRI.';
                    }

                    $new_activity_action->action_id = Action::FOLLOW;
                    $new_activity_action->activity_id = $new_activity->id;
                    $new_activity_action->old_state_id = $new_activity->state_id;
                    $new_activity_action->new_state_id = $new_activity->state_id;
                    $new_activity_action->old_user_id = $new_activity->user_id;
                    $new_activity_action->new_user_id = $new_activity->user_id;
                    $new_activity_action->author_id = 1;
                    $new_activity_action->save();
                }
            }

            DB::commit();
            try {
                $this->consecutiveNumber($activity->id, $action->id);
            } catch (\Exception $e) {
                SlackController::errorRuleActions($e->getMessage(), $e->getTraceAsString());
            }

            if ($new_activity) {
                return redirect('servicio/' . $new_activity->id);
            } else {
                return redirect('servicio/' . $activity->id)
                ->with('success', "La acción $action->name  se realizó de manera adecuada y el cambio ha sido guardado exitosamente.");
            }

        } catch (\Exception $e) {
            DB::rollback();
            return back()->withInput();
        }

    }

    /**
     * @throws \Exception
     */
    public static function create($activity_id, $action_id, $description, $new_user_id = null)
    {
        DB::beginTransaction();
        try {
            // Buscar la activity
            $activity = Activity::find($activity_id);
            if (!$activity) {
                throw new \Exception('La actividad no existe: activity_id = ' . $activity_id);
            }

            // Validar que el estado actual del servicio sea el correcto
            if ($activity->state_id != $activity->service->default_state_id) {
                $serviceState = ServiceState::where('service_id', $activity->service_id)
                    ->where('state_id', $activity->state_id)
                    ->first();
                if (!$serviceState) {
                    throw new \Exception('Estado del servicio invalido: activity_id = ' . $activity_id . ' state_id = ' . $activity->state_id);
                }
            }

            // Validar estado inicial si puede ejecutar la acción
            $stateAction = StateAction::where('state_id', $activity->state_id)
                ->where('action_id', $action_id)
                ->first();
            if (!$stateAction && $action_id != Action::FOLLOW) {
                $action = Action::where('id', $action_id)->first();
                throw new \Exception("La acción \"{$action->name}\" de la actividad \"{$activity->id}\", no se puede ejecutar porque el servicio \"{$activity->service->name}\" se encuentra en el estado actual:\"{$activity->state->name}\" .", 400);
            }

            // Buscar el estado resultante para la acción
            $actionServiceState = ActionServiceState::where('service_id', $activity->service_id)
                ->where('action_id', $action_id)
                ->first();

            if ($actionServiceState && $actionServiceState->state_id != null) {
                $newState = State::findOrFail($actionServiceState->state_id);
            } else {
                $newState = State::findOrFail($activity->state_id);
            }


            // Crea la acción
            $newActivityAction = new ActivityAction();
            $newActivityAction->activity_id = $activity->id;
            $newActivityAction->action_id = $action_id;
            $newActivityAction->old_state_id = $activity->state_id;
            $newActivityAction->new_state_id = $newState->id;
            $newActivityAction->description = $description;
            $newActivityAction->old_user_id = $activity->user_id;
            $newActivityAction->new_user_id = $new_user_id ?: $activity->user_id;
            $newActivityAction->author_id = Auth::id() ? Auth::id() : $activity->user_id;
            $newActivityAction->save();
            // Cambia de estado
            $activity->state_id = $newActivityAction->new_state_id;
            $activity->user_id = $newActivityAction->new_user_id;
            $activity->save();

            DB::commit();
            return $newActivityAction;
        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
    }


    private function consecutiveNumber($activity_id, $action_id)
    {
        $activity = Activity::find($activity_id);
        $action = Action::find($action_id);

        if (Service::checkService($activity->service_id, Service::SERVICE_DETERMINATION_IT_COLPENSIONES)) {
            // Verificar si la acción es de aprobación
            if ($action->id == Action::APPROVE_IT || $action->id == Action::APPROVED_QUALITY_CONTROL_IT) {
                // Verificar si la actividad no tiene uno existente
                if (!$activity->it_id) {
                    // Crear una instancia de ActivityIt y asociarla a la actividad
                    $activityIt = ActivityIt::create();
                    $activity->it_id = $activityIt->id;
                    $activity->save();
                    // Buscar el 'DeterminationIt' asociado a la actividad y actualizar sus atributos
                    $it = DeterminationIt::query()->where('activity_id', $activity->id)->first();
                    if ($it) {
                        $it->reserved_quality_resolution_number = $activityIt->id;
                        $it->resolution_quality_date = Carbon::now();
                        $it->save();
                    }
                }
                sleep(1);
                $activityAction = ActivityAction::query()
                    ->where('action_id', $action->id)
                    ->where('activity_id', $activity->id)
                    ->orderBy('created_at', 'DESC')
                    ->first();
                // Verificar reglas
                $rules = RuleAction::query()
                    ->where('enable', 1)
                    ->where('service_id', $activity->service_id)
                    ->where('yes_action_id', $activityAction->action_id)
                    ->first();
                // Si no hay reglas, se debe generar un PDF
                if (!$rules) {
                    RuleActionsController::generatePDFIt($activity, $activityAction);
                }

                $paymentBasesExists = PaymentBase::query()
                    ->where('radicado', $activity->id_bizagi)
                    ->first();

                if (!$paymentBasesExists) {

                    $newServicePaymentBases = new Activity;
                    $newServicePaymentBases->service_id = Service::SERVICE_IT_LIQUIDATED_COLPENSIONES;
                    $newServicePaymentBases->state_id = 350;
                    $newServicePaymentBases->user_id = $activity->user_id;
                    $newServicePaymentBases->client_id = $activity->client_id;
                    $newServicePaymentBases->affiliate_id = $activity->affiliate_id;
                    $newServicePaymentBases->parent_id = $activity->id;
                    $newServicePaymentBases->save();

                    $determinationIt = DeterminationIt::query()->where('activity_id', $activity->id)->first();
                    if ($determinationIt) {
                        $newPaymentBases = new PaymentBase;
                        $newPaymentBases->activity_id = $newServicePaymentBases->id;
                        $newPaymentBases->radicado = $activity->id_bizagi;
                        $newPaymentBases->documento = $activity->affiliate->doc_number;
                        $newPaymentBases->save();

                        $determinationItInability = DeterminationItInability::query()
                            ->where('determination_it_id', $determinationIt->id)
                            ->where('right_validation', '=', 'APROBADO')
                            ->get();

                        // Calculate the sum of paid_days
                        $total_paid_days = $determinationItInability->sum('paid_days');

                        $author = User::find($activityAction->author_id);
                        $newPaymentBaseInabilities = [];

                        foreach ($determinationItInability as $diti) {
                            $newPaymentBaseInabilities[] = [
                                'payment_base_id' => $newPaymentBases->id,
                                'radicado' => $activity->id_bizagi,
                                'documento' => $activity->affiliate->doc_number,
                                'fechacambio' => '',
                                'aprobado' => 'TRUE',
                                'nombre' => $determinationIt->full_name_affiliate,
                                'terceroautpago' => $determinationIt->titular_account_type == 'TERCERO' ? $determinationIt->account_full_name : '',
                                'numeroresolucion' => $determinationIt->reserved_quality_resolution_number,
                                'fecharesolucion' => $determinationIt->resolution_quality_date,
                                'indtramacreedor' => '',
                                'valorresolucion' => $determinationIt->payment_value,
                                'valorincapacidad' => $diti->payment_it_value,
                                'tutela' => $determinationIt->tutelage_it,
                                'diasapagar' => $diti->paid_days,
                                'fechainicio' => $diti->initial_date,
                                'fechafin' => $diti->end_date,
                                'totaldiasincapacidad' => $total_paid_days,
                                'registradopor' => $author ? $author->username : '',
                                'novedad' => '',
                                'fraccionado' => null,
                                'created_at' => Carbon::now(),
                                'updated_at' => Carbon::now(),
                            ];
                        }

                        $determinationItInabilityn = DeterminationItInability::query()
                            ->where('determination_it_id', $determinationIt->id)
                            ->where('fractional_it', 'SI')
                            ->get();

                        foreach ($determinationItInabilityn as $ditin) {

                            $determinationItInabilityFraction = DeterminationItInabilityFrac::query()
                                ->where('determination_it_inability_id', $ditin->id)
                                ->where('validation_result', 'APROBADO')
                                ->get();

                            // Calculate the sum of paid_days
                            $total_paid_days = $determinationItInabilityFraction->sum('it_days_pay');

                            foreach ($determinationItInabilityFraction as $ditif) {
                                $newPaymentBaseInabilities[] = [
                                    'payment_base_id' => $newPaymentBases->id,
                                    'radicado' => $activity->id_bizagi,
                                    'documento' => $activity->affiliate->doc_number,
                                    'fechacambio' => '',
                                    'aprobado' => 'TRUE',
                                    'nombre' => $determinationIt->full_name_affiliate,
                                    'terceroautpago' => $determinationIt->titular_account_type == 'TERCERO' ? $determinationIt->account_full_name : '',
                                    'numeroresolucion' => $determinationIt->reserved_quality_resolution_number,
                                    'fecharesolucion' => $determinationIt->resolution_quality_date,
                                    'indtramacreedor' => '',
                                    'valorresolucion' => $determinationIt->payment_value,
                                    'valorincapacidad' => $ditif->value_pay,
                                    'tutela' => $determinationIt->tutelage_it,
                                    'diasapagar' => $ditif->it_days_pay,
                                    'fechainicio' => $ditif->initial_date,
                                    'fechafin' => $ditif->end_date,
                                    'totaldiasincapacidad' => $total_paid_days,
                                    'registradopor' => $author ? $author->username : '',
                                    'novedad' => '',
                                    'fraccionado' => 'SI',
                                    'created_at' => Carbon::now(),
                                    'updated_at' => Carbon::now(),
                                ];
                            }
                        }
                        PaymentBaseInability::insert($newPaymentBaseInabilities);
                    }
                } else {
                    $activityPBExist = Activity::withTrashed()
                        ->where('id', $paymentBasesExists->activity_id)
                        ->first();
                    if ($activityPBExist) {
                        $activityPBExist->deleted_at = null;
                        $activityPBExist->save();
                    }
                }
            }
        }
        // Verificar si el servicio de la actividad es 'SERVICE_PCL_COLPENSIONES' o 'SERVICE_INVALIDITY_STATE_P4_COLPENSIONES'
        if (Service::checkService($activity->service_id, Service::SERVICE_PCL_COLPENSIONES) || Service::checkService($activity->service_id, Service::SERVICE_INVALIDITY_STATE_P4_COLPENSIONES)) {
            // Verificar si la acción es una determinada aprobación
            if ($action->id == Action::APPROVE_CONT_PCL_REI || $action->id == Action::INFORM_PCL_DICTUM_APROBATION_REI || $action->id == Action::APPROVE_WITHOUT_AUDIT_PCL) {
                // Verificar si la actividad no tiene uno existente
                if (!$activity->pcl_id) {
                    // Crear una instancia de ActivityPcl y asociarla a la actividad
                    $activityPcl = ActivityPcl::create();
                    $activity->pcl_id = $activityPcl->id;
                    $activity->save();
                }
                // Generar un PDF para la 'ActivityAction'.
                $this->generatePDFPcl($action, $activity);
            }
        }
    }

    public function regenerateForActivityId(Request $request, $cpath, $activity_id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        // Buscar las ActivityActions para el activity_id proporcionado
        $activityActions = ActivityAction::whereIn('action_id', [513, 544])
            ->where('activity_id', $activity_id)
            ->orderBy('id')
            ->get();

        DB::beginTransaction();

        try {
            if ($activityActions) {
                foreach ($activityActions as $activityAction) {
                    $action = $activityAction->action;
                    $activity = $activityAction->activity;

                    $activityAction->documents()->delete();

                    if ($action->documents) {
                        foreach (explode(',', $action->documents) as $document) {
                            $pdf = ActionController::generatePDF($activity, $document, false);

                            Storage::disk('s3')->put("activity_action_document/{$document}_{$activityAction->id}.pdf", $pdf->output());

                            $activityActionDocument = new ActivityActionDocument;
                            $activityActionDocument->activity_action_id = $activityAction->id;
                            $activityActionDocument->name = $document;
                            $activityActionDocument->path = "activity_action_document/{$document}_{$activityAction->id}.pdf";
                            $activityActionDocument->save();
                        }
                    }
                }
            }

            DB::commit();
            return 'FINALIZADO EXITOSAMENTE';
        } catch (Exception $e) {
            DB::rollback();
            \Log::error($e);
            return 'ERROR: ' . $e->getMessage();
        }
    }

    public function regenerate521ForActivityId(Request $request, $cpath, $activity_id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        // Buscar las ActivityActions para el activity_id proporcionado
        $activityActions = ActivityAction::where('action_id', 521)
            ->where('activity_id', $activity_id)
            ->orderBy('id')
            ->get();

        DB::beginTransaction();

        try {
            if ($activityActions) {
                foreach ($activityActions as $activityAction) {
                    $action = $activityAction->action;
                    $activity = $activityAction->activity;
                    if (Service::checkService($activity->service_id, Service::SERVICE_PCL_COLPENSIONES)) {
                        if ($activityAction->action_id == Action::APPROVE_CONT_PCL_REI || $activityAction->action_id == Action::INFORM_PCL_DICTUM_APROBATION_REI || $activityAction->action_id == Action::APPROVE_WITHOUT_AUDIT_PCL) {
                            if (!$activity->pcl_id) {
                                $activityPcl = ActivityPcl::create();
                                $activity->pcl_id = $activityPcl->id;
                                $activity->save();
                            }
                        }
                    }
                    $activityAction->documents()->delete();

                    if ($action->documents) {
                        foreach (explode(',', $action->documents) as $document) {
                            $pdf = ActionController::generatePDF($activity, $document, false);

                            Storage::disk('s3')->put("activity_action_document/{$document}_{$activityAction->id}.pdf", $pdf->output());

                            $activityActionDocument = new ActivityActionDocument;
                            $activityActionDocument->activity_action_id = $activityAction->id;
                            $activityActionDocument->name = $document;
                            $activityActionDocument->path = "activity_action_document/{$document}_{$activityAction->id}.pdf";
                            $activityActionDocument->save();
                        }
                    }
                }
            }

            DB::commit();
            return 'FINALIZADO EXITOSAMENTE';
        } catch (Exception $e) {
            DB::rollback();
            \Log::error($e);
            return 'ERROR: ' . $e->getMessage();
        }
    }

    public function masiveRegenerate(Request $req, $cpath, $id)
    {
        $last_id = $id;
        $client = Client::where('path', $cpath)->firstOrFail();
        $activityAction = ActivityAction::whereIn('action_id', [513, 518])
            ->whereIn('activity_id', [2427,
                1839,
                1480,
                2109,
                1784,
                1884,
                630,
                846,
                1925,
                2638,
                683279,
                685719,
                684675,
                688415,
                684504,
                687222,
                684615,
                683812,
                690020,
                830700,
                690328,
                690111,
                836938,
                2883,
                686556,
                683047,
                684661,
                683874,
                684340,
                685846,
                682477,
                682544,
                682982,
                684651,
                683007,
                682663,
                683889,
                685819,
                685506])
            ->where('id', '>', $last_id)
            ->orderBy('id')
            ->first();

        DB::beginTransaction();

        try {
            if ($activityAction != null) {
                $activity = $activityAction->activity;
                $action = $activityAction->action;

                $activityAction->documents()->delete();

                if ($activity->service_id == Service::SERVICE_DETERMINATION_IT_COLPENSIONES) {
                    if ($action->documents) {
                        foreach (explode(',', $action->documents) as $document) {
                            $pdf = ActionController::generatePDF($activity, $document, false);

                            Storage::disk('s3')->put("activity_action_document/{$document}_{$activityAction->id}.pdf", $pdf->output());

                            //ADD PASSWORD
                            //$this->addPasswordPDF($document, $activityAction, $pdf, $activity);

                            $activityActionDocument = new ActivityActionDocument;
                            $activityActionDocument->activity_action_id = $activityAction->id;
                            $activityActionDocument->name = $document;
                            $activityActionDocument->path = "activity_action_document/{$document}_{$activityAction->id}.pdf";
                            $activityActionDocument->save();
                        }
                    } else {
                        $determinationIt = DeterminationIt::query()->where('activity_id', $activity->id)->first();
                        if ($determinationIt && $determinationIt->communication_to_emit) {
                            $banks = json_decode(file_get_contents(public_path('js/banco.json')), true);
                            $bankName = 'NO HAS GUARDADO EL NOMBRE DEL BANCO';
                            if ($activity->determination_it && $activity->determination_it->bank_name) {
                                foreach ($banks as $bank) {
                                    if ($bank['code'] == $activity->determination_it->bank_name) {
                                        $bankName = $bank['name'];
                                    }
                                }
                            }

                            $pdf = \PDF::loadView('services/determination_it_subsidy/docs/' . $determinationIt->communication_to_emit . '_pdf', ['activity' => $activity, 'bankName' => $bankName, 'determination_it' => $determinationIt, 'watermark' => false]);
                            $fileName = "activity_action_document/{$determinationIt->communication_to_emit}_{$activityAction->id}.pdf";
                            Storage::disk('s3')->put($fileName, $pdf->output());

                            $path = $fileName;

                            $activityActionDocument = new ActivityActionDocument;
                            $activityActionDocument->activity_action_id = $activityAction->id;
                            $activityActionDocument->name = $determinationIt->communication_to_emit;
                            $activityActionDocument->path = $path;
                            $activityActionDocument->save();
                        }
                    }
                } else if ($action->documents) {

                    foreach (explode(',', $action->documents) as $document) {
                        $pdf = ActionController::generatePDF($activity, $document, false);

                        Storage::disk('s3')->put("activity_action_document/{$document}_{$activityAction->id}.pdf", $pdf->output());

                        //ADD PASSWORD
                        //$this->addPasswordPDF($document, $activityAction, $pdf, $activity);

                        $activityActionDocument = new ActivityActionDocument;
                        $activityActionDocument->activity_action_id = $activityAction->id;
                        $activityActionDocument->name = $document;
                        $activityActionDocument->path = "activity_action_document/{$document}_{$activityAction->id}.pdf";
                        $activityActionDocument->save();
                    }
                }

                $last_id = $activityAction->id;
            }

            DB::commit();
        } catch (Exception $e) {
            DB::rollback();
            \Log::error($e);
            return 'ERROR: ' . $e->getMessage();
        }

        if ($last_id != $id) {
            return view('extras.masive_regenerate', ['id' => $last_id]);
        }

        return 'FINISHED';
    }

    public function regenerate(Request $req, $cpath, $id)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $activityAction = ActivityAction::findOrFail($id);
        $action = $activityAction->action;
        $activity = $activityAction->activity;

        $activityAction->documents()->delete();

        if ($activity->service_id == Service::SERVICE_DETERMINATION_IT_COLPENSIONES) {
            if ($action->documents) {
                foreach (explode(',', $action->documents) as $document) {
                    $pdf = ActionController::generatePDF($activity, $document, false);

                    Storage::disk('s3')->put("activity_action_document/{$document}_{$activityAction->id}.pdf", $pdf->output());

                    //ADD PASSWORD
                    //$this->addPasswordPDF($document, $activityAction, $pdf, $activity);

                    $activityActionDocument = new ActivityActionDocument;
                    $activityActionDocument->activity_action_id = $activityAction->id;
                    $activityActionDocument->name = $document;
                    $activityActionDocument->path = "activity_action_document/{$document}_{$activityAction->id}.pdf";
                    $activityActionDocument->save();
                }
            } else {
                $determinationIt = DeterminationIt::query()->where('activity_id', $activity->id)->first();
                if ($determinationIt && $determinationIt->communication_to_emit) {
                    $banks = json_decode(file_get_contents(public_path('js/banco.json')), true);
                    $bankName = 'NO HAS GUARDADO EL NOMBRE DEL BANCO';
                    if ($activity->determination_it && $activity->determination_it->bank_name) {
                        foreach ($banks as $bank) {
                            if ($bank['code'] == $activity->determination_it->bank_name) {
                                $bankName = $bank['name'];
                            }
                        }
                    }

                    $pdf = \PDF::loadView('services/determination_it_subsidy/docs/' . $determinationIt->communication_to_emit . '_pdf', ['activity' => $activity, 'bankName' => $bankName, 'determination_it' => $determinationIt, 'watermark' => false]);
                    $fileName = "activity_action_document/{$determinationIt->communication_to_emit}_{$activityAction->id}.pdf";
                    Storage::disk('s3')->put($fileName, $pdf->output());

                    $path = $fileName;

                    $activityActionDocument = new ActivityActionDocument;
                    $activityActionDocument->activity_action_id = $activityAction->id;
                    $activityActionDocument->name = $determinationIt->communication_to_emit;
                    $activityActionDocument->path = $path;
                    $activityActionDocument->save();
                }
            }
        } else if ($action->documents) {

            foreach (explode(',', $action->documents) as $document) {
                $pdf = ActionController::generatePDF($activity, $document, false);

                Storage::disk('s3')->put("activity_action_document/{$document}_{$activityAction->id}.pdf", $pdf->output());

                //ADD PASSWORD
                //$this->addPasswordPDF($document, $activityAction, $pdf, $activity);

                $activityActionDocument = new ActivityActionDocument;
                $activityActionDocument->activity_action_id = $activityAction->id;
                $activityActionDocument->name = $document;
                $activityActionDocument->path = "activity_action_document/{$document}_{$activityAction->id}.pdf";
                $activityActionDocument->save();
            }
        }

        return redirect('servicio/' . $activity->id);
    }

    public function docPreview(Request $req, $cpath, $id, $doc)
    {
        $client = Client::where('path', $cpath)->firstOrFail();
        $activity = Activity::where('client_id', $client->id)->where('id', $id)->firstOrFail();

        $pdf = ActionController::generatePDF($activity, $doc);

        return $pdf->stream('preview.pdf');
    }

    private function generatePdfPcl($action, $activity)
    {
        $activityAction = ActivityAction::query()
            ->where('action_id', $action->id)
            ->where('activity_id', $activity->id)
            ->orderBy('created_at', 'DESC')
            ->first();

        if ($action->documents) {

            $activityAction->documents()->delete();

            foreach (explode(',', $action->documents) as $document) {
                $pdf = ActionController::generatePDF($activity, $document, false);

                Storage::disk('s3')->put("activity_action_document/{$document}_{$activityAction->id}.pdf", $pdf->output());

                $activityActionDocument = new ActivityActionDocument;
                $activityActionDocument->activity_action_id = $activityAction->id;
                $activityActionDocument->name = $document;
                $activityActionDocument->path = "activity_action_document/{$document}_{$activityAction->id}.pdf";
                $activityActionDocument->save();
            }
        }
    }

    static function generatePDF($activity, $document, $watermark = true, $author_req_id = null, $activityActionDate = null)
    {
        $bankName = null;
        if (Service::checkService($activity->service_id, Service::SERVICE_TEMPLATE_AXA)) {
            $pdf = PDF::loadView("services.plantilla.docs.{$document}_pdf", [
                'activity' => $activity,
                'plantilla' => $activity->plantilla,
                'watermark' => $watermark,
            ]);
        }
        if (Service::checkService($activity->service_id, Service::SERVICE_QUOTATIONS)) {
            $pdf = PDF::loadView("services.quotation.docs.{$document}_pdf", [
                'activity' => $activity,
                'quotation' => $activity->quotation,
                'watermark' => $watermark,
            ]);
        }
        if (Service::checkService($activity->service_id, Service::SERVICE_EXPRESSION_DISAGREEMENT_COLPENSIONES)) {
            $pdf = PDF::loadView("services.expression_disagreement.docs.{$document}_pdf", [
                'activity' => $activity,
                'expression_disagreement' => $activity->expression_disagreement,
                'watermark' => $watermark,
            ]);
        }
        if (Service::checkService($activity->service_id, Service::SERVICE_DETERMINATION_IT)) {
            $bankName = 'NO HAS GUARDADO EL NOMBRE DEL BANCO';
            $banks = json_decode(file_get_contents(public_path('js/banco.json')), true);
            if ($activity->determination_it && $activity->determination_it->bank_name) {
                foreach ($banks as $bank) {
                    if ($bank['code'] == $activity->determination_it->bank_name) {
                        $bankName = $bank['name'];
                    }
                }
            }
            $pdf = PDF::loadView("services.determination_it_subsidy.docs.{$document}_pdf", [
                'activity' => $activity,
                'bankName' => $bankName,
                'determination_it' => $activity->determination_it,
                'watermark' => $watermark,
            ]);
        }
        if (Service::checkService($activity->service_id, Service::SERVICE_INVALIDITY_STATE_P1)) {
            $pdf = PDF::loadView("services.invalidity_state_pone.docs.{$document}_pdf", [
                'activity' => $activity,
                'invalidity_state_pone' => $activity->invalidity_state_pone,
                'watermark' => $watermark,
            ]);
        }
        if (Service::checkService($activity->service_id, Service::SERVICE_INVALIDITY_STATE_P4)) {
            $preview = false;

            $pdf = PDF::loadView("services.invalidity_state_pfour.docs.{$document}_pdf", [
                'activity' => $activity,
                'invalidity_state_pfour' => $activity->invalidity_state_pfour,
                'preview' => $preview,
                'watermark' => $watermark,
            ]);
        }
        if (Service::checkService($activity->service_id, Service::SERVICE_MEETING_COLPENSIONES)) {
            $pdf = PDF::loadView("services.meeting.docs.{$document}_pdf", [
                'activity' => $activity,
                'meeting_argument' => $activity->meeting,
                'watermark' => $watermark,
                'author_req_id' => $author_req_id
            ]);
        }
        if (Service::checkService($activity->service_id, Service::SERVICE_DICTUM_RECEPTION_COLPENSIONES)) {
            $pdf = PDF::loadView("services.dictum_reception.docs.{$document}_pdf", [
                'activity' => $activity,
                'dictum_reception' => $activity->dictum_reception,
                'watermark' => $watermark,
                'author_req_id' => $author_req_id
            ]);
        }
        if (Service::checkService($activity->service_id, Service::SERVICE_RESPONSE_PQR)) {
            $pdf = PDF::loadView("services.pqr.docs.{$document}_pdf", [
                'activity' => $activity,
                'pqr' => $activity->pqr,
                'watermark' => $watermark,
                'author_req_id' => $author_req_id
            ]);
        }
        if (Service::checkService($activity->service_id, Service::SERVICE_PCL_COLPENSIONES)) {
            $pcl_dates = Pcl::query()
                ->where('activity_id', $activity->id)
                ->firstOrFail();
            $pcl_date = $pcl_dates->pcl_date;
            $req_date = $pcl_dates->req_date;
            $preview = false;

            $pdf = PDF::loadView("services.pcl.docs.{$document}_pdf", [
                'activity' => $activity,
                'pcl' => $activity->pcl,
                'watermark' => $watermark,
                'author_req_id' => $author_req_id,
                'pcl_date' => $pcl_date,
                'req_date' => $req_date,
                'activityActionDate' => $activityActionDate,
                'preview' => $preview
            ]);
        }
        if (Service::checkService($activity->service_id, Service::SERVICE_POLICY_SORT)) {
            $pdf = PDF::loadView("services.policy_sort.docs.{$document}_pdf", [
                'activity' => $activity,
                'policy_sort' => $activity->policy_sort,
                'watermark' => $watermark,
            ]);
        }
        if (Service::checkService($activity->service_id, Service::SERVICE_POLICY_SORT_COLLECTION)) {
            $pdf = PDF::loadView("services.policy_sort_collection.docs.{$document}_pdf", [
                'activity' => $activity,
                'policy_sort_collection' => $activity->policy_sort_collection,
                'watermark' => $watermark,
            ]);
        }
        return $pdf;
    }

    public function addPasswordPDF($document, $activityAction, $pdf, $activity)
    {
        //ADD PASSWORD TO PDF FILE
        Storage::put("testing/activity_action_document/{$document}_{$activityAction->id}.pdf", $pdf->output());

        $fpdi = new \setasign\FpdiProtection\FpdiProtection;

        $url = ("../storage/app/testing/activity_action_document/{$document}_{$activityAction->id}.pdf");

        //calculate the number of pages from the original document
        $pagecount = $fpdi->setSourceFile($url);

        // copy all pages from the old unprotected pdf in the new one
        for ($loop = 1; $loop <= $pagecount; $loop++) {
            $tplidx = $fpdi->importPage($loop);
            $fpdi->addPage();
            $fpdi->useTemplate($tplidx);
        }

        $fpdi->SetProtection(array('print'), $activity->affiliate->doc_number);

        $fpdi->Output($url, "F");

        Storage::disk('s3')
            ->putFileAs("activity_action_document", new File(storage_path("app/testing/activity_action_document/{$document}_{$activityAction->id}.pdf")), "{$document}_{$activityAction->id}.pdf");

        Storage::delete("testing/activity_action_document/{$document}_{$activityAction->id}.pdf");
        Storage::deleteDirectory("testing/activity_action_document");
        Storage::deleteDirectory("testing");
        //END: ADD PASSWORD TO PDF FILE
    }

    public function replaceTextAreas(Request $req)
    {
        $model = new ActivityAction;
        $model->replaceTextInColumns();
    }


    public function fillingRadicationDate(Request $request, $cpath, $id)
    {
        $last_id = $id;

        $client = Client::where('path', $cpath)->firstOrFail();
        // Buscar las ActivityActions para el activity_id proporcionado
        $pcl = Pcl::query()->whereIn('activity_id',
            [683397,
                683812,
                683987,
                684504,
                686238,
                688120,
                688387,
                688447,
                690829,
                690830,
                690831,
                690832,
                690833,
                690834,
                690835,
                690836,
                690838,
                690839,
                690840,
                690841,
                690842,
                690843,
                690844,
                690845,
                690846,
                690847,
                690848,
                690849,
                690851,
                690852,
                690853,
                690854,
                690855,
                690856,
                690857,
                690858,
                690859,
                690860,
                690861,
                690862,
                690863,
                690864,
                690865,
                690866,
                690867,
                690868,
                690869,
                690871,
                690872,
                690873,
                690874,
                690875,
                690876,
                690877,
                690878,
                690879,
                690880,
                690881,
                690882,
                690883,
                690884,
                690885,
                690887,
                690889,
                690890,
                829423,
                830692,
                832167,
                832200,
                832878,
                832907,
                832908,
                832909,
                832910,
                832911,
                832912,
                832913,
                832914,
                832915,
                832916,
                832917,
                832918,
                832919,
                832920,
                832921,
                832922,
                832923,
                832924,
                832925,
                832926,
                832927,
                832928,
                832929,
                832930,
                832931,
                832932,
                832933,
                832934,
                832935,
                832936,
                832937,
                832938,
                832939,
                832940,
                832941,
                832942,
                832943,
                832944,
                832945,
                832946,
                832947,
                832948,
                832949,
                832950,
                832951,
                832952,
                832953,
                832954,
                832955,
                832956,
                832957,
                832958,
                832959,
                832960,
                832961,
                832962,
                832963,
                832964,
                832965,
                832966,
                832967,
                832968,
                832969,
                832970,
                832971,
                832972,
                832973,
                832974,
                832975,
                832976,
                832977,
                832978,
                832979,
                832980,
                832981,
                832982,
                832983,
                832984,
                832985,
                832986,
                832987,
                832988,
                832989,
                832990,
                832991,
                832992,
                832993,
                832994,
                832995,
                832996,
                832997,
                832998,
                832999,
                833000,
                833001,
                833002,
                833003,
                833004,
                833005,
                833006,
                833007,
                833008,
                833009,
                833010,
                833011,
                833012,
                833013,
                833014,
                833015,
                833016,
                833017,
                833018,
                833019,
                833020,
                833021,
                833022,
                833023,
                833024,
                833025,
                833026,
                833027,
                833028,
                833029,
                833030,
                833031,
                833032,
                833033,
                833034,
                833035,
                833036,
                833037,
                833038,
                833039,
                833040,
                833041,
                833042,
                833043,
                833044,
                833045,
                833046,
                833047,
                833048,
                833049,
                833050,
                833051,
                833052,
                833053,
                833054,
                833055,
                833056,
                833057,
                833058,
                833059,
                833060,
                833061,
                833062,
                833063,
                833064,
                833065,
                833066,
                833067,
                833068,
                833069,
                833070,
                833071,
                833072,
                833073,
                833074,
                833075,
                833076,
                833077,
                833078,
                833079,
                833080,
                833081,
                833082,
                833083,
                833084,
                833085,
                833086,
                833087,
                833088,
                833089,
                833090,
                833091,
                833092,
                833093,
                833135,
                833139,])
            ->where('id', '>', $last_id)
            ->orderBy('id')
            ->first();


        DB::beginTransaction();

        try {
            if ($pcl) {
                if ($pcl->radication_date == null) {
                    $activity = $pcl->activity;
                    $pcl->radication_date = $activity->created_at;
                    $pcl->save();
                }
                $last_id = $pcl->id;

            }

            DB::commit();
        } catch (Exception $e) {
            DB::rollback();
            \Log::error($e);
            return 'ERROR: ' . $e->getMessage();
        }

        if ($last_id != $id) {
            return view('extras.fill_radication_date', ['id' => $last_id]);
        }

        return 'FINISHED';
    }
}
