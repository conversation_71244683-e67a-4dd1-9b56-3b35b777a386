<?php

namespace App\Http\Middleware;

use Closure;

class FrameHeadersMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request $request
     * @param  \Closure                 $next
     *
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        $response = $next($request);
        //$response->headers->remove('X-Frame-Options');
        //$response->headers->set('X-Frame-Options', 'ALLOW-FROM https://www.aliansalud.com.co/');
        $response->header('X-Frame-Options', 'ALLOW FROM https://www.aliansalud.com.co/');
        //$response->header('X-Frame-Options', 'ALLOW FROM http://teate.dvanegas.localhost.com/');
        //$response->headers->set('X-Frame-Options', 'allow-from https://last-el.com');

        return $response;
    }
}

