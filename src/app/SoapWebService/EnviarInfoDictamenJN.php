<?php

use App\Http\Controllers\Integrations\WebserviceColpensiones\SendOpinionInfoJNController;

/**
 * @param $data
 * @return array
 * @description Función que transforma los datos obtenidos del método EnviarInfoDictamenJN, en un objeto y es enviado al controlador del método
 * <AUTHOR>
 * @date 2023-02-27
 */
function SendOpinionInfoJN($data){

    $opinionDate        = date("Y-m-d",strtotime($data['FECHADICTAMEN']));
    $structuringDate    = date("Y-m-d",strtotime($data['FECHAESTRUCTURACION']));
    $caseId             = $data['Id_case'];
    $opinionNumber      = $data['NUMERODICTAMEN'];
    $filedNumber        = $data['NumeroRadicado'];
    $ratingPercentage   = $data['PorcentageCalificacion'];

    $enviarInfoDictamenJN = new stdClass();
    $enviarInfoDictamenJN->FechaDictamen = $opinionDate;
    $enviarInfoDictamenJN->FechaEstructuracion = $structuringDate;
    $enviarInfoDictamenJN->IdCase = $caseId;
    $enviarInfoDictamenJN->NumeroDictamen = $opinionNumber;
    $enviarInfoDictamenJN->NumeroRAdicado = $filedNumber;
    $enviarInfoDictamenJN->PorcentajeCalifiacion = $ratingPercentage;

    $response = SendOpinionInfoJNController::SendOpinionInfoJN($enviarInfoDictamenJN);

    return array(
        'CodeError' => $response['CodeError'],
        'DescriptionResponse' => $response['DescriptionResponse'],
        'ResponseType' => $response['ResponseType']
    );

}