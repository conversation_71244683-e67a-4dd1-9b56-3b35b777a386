<?php


use App\Http\Controllers\Integrations\WebserviceColpensiones\InformSendJRController;

/**
 * @param $data
 * @return array
 * @description Función que transforma los datos obtenidos del método InformarEnvioJR, en un objeto y es enviado al controlador del método
 * <AUTHOR>
 * @date 2023-02-27
 */
function InformarEnvioJR($data){
    $sendDate       = date("Y-m-d",strtotime($data['FechaEnvioJR']));
    $caseId         = $data['Id_case'];
    $regionalBoard  = $data['JuntaRegional'];
    $reason         = $data['Motivo'];
    $filedNumber    = $data['NumeroRadicado'];
    $whoAppealed    = $data['QuienApelo'];

    $informarEnvioJR = new stdClass();
    $informarEnvioJR->FechaEnvioJR = $sendDate;
    $informarEnvioJR->IdCase = $caseId;
    $informarEnvioJR->JuntaRegional = $regionalBoard;
    $informarEnvioJR->Motivo = $reason;
    $informarEnvioJR->NumeroRadicado = $filedNumber;
    $informarEnvioJR->QuienApelo = $whoAppealed;

    $response = InformSendJRController::InformSendJR($informarEnvioJR);

    return array(
        'CodeError' => $response['CodeError'],
        'DescriptionResponse' => $response['DescriptionResponse'],
        'ResponseType' => $response['ResponseType']
    );
}