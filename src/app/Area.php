<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use OwenIt\Auditing\Contracts\Auditable;


class Area extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable;

    protected $fillable = [
        'id',
        'name',
        'created_at',
        'updated_at',
        'description',
        'aseguramiento',
        'bandeja',
        'busqueda',
        'gestion',
        'reports',
        'administrator',
        'variations',
        'loads',
        'cobros',
        'provider',
        'auditoria',
        'cuentas',
        'auditoria_cuentas',
        'monitoreo',
        'intermediario',
        'tomador',
        'afiliado',
        'call_center',
        'reportes_internos',
        'reporte_intermediario_tomador',
        'reportes_empresariales',
        'reportes_operativos_consolidados',
        'reportes_operativos',
        'reportes_fallecido',
        'reportes_por_poliza',
        'indemnizaciones',
        'ejecutivo_comercial',
        'autorizaciones',
        'correos'
    ];

    const ADMINISTRATIVE = 1;
    const MNK_EXECUTIVE = 58;
    const INTERMEDIARY = 43;
    const COMMERCIAL_EXECUTIVE = 56;
    const INTERMEDIARY_EXECUTIVE = 57;
    const ANALISTA_ASEGURAMIENTO = 44;
    const ANALISTA_INDEMNIZACION = 49;
    const ASESOR_CALL_CENTER = 45;
    const TOMADOR = 46;
    const TOMADOR_AUTORIZADO = 61;
    const AUTORIZADO = 63;
    const PROVIDER = 47;
    const AUDITOR = 48;
    const ASEGURADO = 51;
    const PREVENCION = 52;
    const ADMINISTRATIVE_MNK = 64;
}
