<?php

namespace App;

use App\Festivos;
use DateTime;
use Illuminate\Database\Eloquent\Model;

class Correspondence extends Model
{
    //
    protected $dates = ['deleted_at', 'send_at', 'next_day'];
    protected $casts = [
        'closed' => 'boolean',
    ];
    protected $fillable = ['client_id', 'closed'];

    public function client()
    {
        return $this->belongsTo('App\Client');
    }

    public function items()
    {
        return $this->hasMany('App\CorrespondenceItem')->orderBy('order');
    }

    public function activity_items($activity_id)
    {
        return $this->hasMany('App\CorrespondenceItem')->where('activity_id', $activity_id);
    }

    public function getNextDayAttribute()
    {
        return self::nextDay();
    }

    public static function nextDay()
    {
        $festivos = new Festivos(date('Y'));
        $date     = new DateTime();
        $isHabil  = false;
        do {
            $date->modify('+1 day');
            if ($date->format('N') <= 5 && !$festivos->esFestivo($date->format('d'), $date->format('n'))) {
                $isHabil = true;
            }
        } while (!$isHabil);

        return $date->format('Y-m-d');
    }

    public static function nextDates($dates)
    {
        $festivos = new Festivos(date('Y'));
        $date     = new DateTime();

        for ($j = 0; $j < $dates; $j++) {
            $isHabil = false;
            do {
                $date->modify('+1 day');
                if ($date->format('N') <= 5 && !$festivos->esFestivo($date->format('d'), $date->format('n'))) {
                    $isHabil = true;
                }
            } while (!$isHabil);
        }

        return $date->format('Y-m-d');
    }

    public static function plusDays($date, $days)
    {
        $festivos = new Festivos(date('Y'));
        $count_days = 0;
        do {
            $date->modify('+1 day');
            if ($date->format('N') <= 5 && !$festivos->esFestivo($date->format('d'), $date->format('n'))) {
                $count_days++;
            }
        } while ($count_days<$days);

        //return '2018-11-30';
        return $date->format('Y-m-d');
    }

    public static function current($client)
    {
        $order = self::with('items')
            ->with('items.activity')
            ->with('items.activity.affiliate')
            ->with('items.activity.service')
            ->firstOrNew(['client_id' => $client->id, 'closed' => false]);

        if (!$order->consecutive) {
            $order->consecutive = self::where('client_id', $client->id)->max('consecutive') + 1;
            $order->save();
        }

        return $order;
    }

    public static function currentData($clientId)
    {
        $order = self::firstOrNew(['client_id' => $clientId, 'closed' => false]);

        if (!$order->consecutive) {
            $order->consecutive = self::where('client_id', $clientId)->max('consecutive') + 1;
            $order->save();
        }

        return $order;
    }
    public static function weekDays($firstDate, $secondDate)
    {
        $firstDate = DateTime::createFromFormat('Y-m-d', date('Y-m-d', strtotime($firstDate)));
        $secondDate = DateTime::createFromFormat('Y-m-d', date('Y-m-d', strtotime($secondDate)));
        $festivos = new Festivos(date('Y'));
        $count_days = 0;
        do {
            $firstDate->modify('+1 day');
            if ($firstDate->format('N') <= 5 && !$festivos->esFestivo($firstDate->format('d'), $firstDate->format('n'))) {
                $count_days++;
            }
        } while ($firstDate < $secondDate);

        return $count_days;
    }
}
