<?php

namespace App;
use Illuminate\Database\Eloquent\Model;

class MedicalServiceFollowUp extends Model
{
    protected $fillable = [
        'medical_services_sort_id',
        'follow_up_number',
        'valuation_date',
        'valuation_time',
        'consultation_channel',
        'consultation_reason',
        'labor_role_description',
        'records',
        'size',
        'weight',
        'ta',
        'ta2',
        'imc',
        'fc',
        'fr',
        'physical_examination_description',
        'plan_description',
        'closed_case',
        'disability_type',
        'attention_mode',
        'start_date_of_incapacity',
        'days_of_incapacity',
        'origin_case',
        'dx_principal',
        'dx_principal_description',
        'dx_related',
        'dx_related_description',
        'observations_explanatory_notes',
        'origin_diagnosis_referral_specialist',
        'origin_diagnosis',
        'document_type_evaluator',
        'identification_number_evaluator',
        'full_name_evaluator',
        'medical_registration_number_evaluator',
        'specialty_evaluator',
        'license_number_evaluator',
        'labor_role_description_observation',
        'approved_service',
        'observation_audit',
        'diagnosis_origin_prescription',
        'diagnosis_origin_controlled_medication',
        'province_controlled_medication',
        'canton_controlled_medication',
        'district_controlled_medication',
        'province_incapacity_or_leave',
        'canton_incapacity_or_leave',
        'district_incapacity_or_leave',
        'province_medical_prescription',
        'canton_medical_prescription',
        'district_medical_prescription',
        'province_order_diagnostic',
        'canton_order_diagnostic',
        'district_order_diagnostic',
        'province_referral_specialist',
        'canton_referral_specialist',
        'district_referral_specialist',
        'required_hospitalization',
        'travel_expenses',
        'transportation',
        'other_signs',
        'next_follow_up_date',
        'extended_incapacity_reason',
        'pharmacies_branch_controlled_medication',
        'pharmacies_branch_prescription',
        'medical_discharge',
        'referral_specialist',
        'specialty',
        'requires_follow_up'
    ];

    public function medicalService()
    {
        return $this->belongsTo('App\MedicalServicesSort', 'medical_services_sort_id');
    }
    public function companions()
    {
        return $this->hasMany('App\MedicalServiceCompanion', 'medical_service_follow_up_id');
    }
    public function diagnostics()
    {
        return $this->hasMany('App\MedicalServiceDiagnostics', 'medical_service_follow_up_id');
    }

    public function previousDiagnostics()
    {
         return MedicalServiceDiagnostics::where('medical_service_sort_id', $this->medical_services_sort_id)
            ->where('medical_service_follow_up_id', '<=', $this->id)
            ->get() ?? null;

    }

    public function diagnosticsImages()
    {
        return $this->hasMany('App\MedicalServiceImageDiagnostics', 'medical_service_follow_up_id');
    }
    public function specialists()
    {
        return $this->hasMany('App\MedicalServiceReferralSpecialits' ,'medical_service_follow_up_id');
    }
    public function medicalPrescriptions()
    {
        return $this->hasMany('App\MedicalServiceMedicalPrescription' ,'medical_service_follow_up_id');
    }
    public function controlledMedications()
    {
        return $this->hasMany('App\MedicalServiceControlledMedication' ,'medical_service_follow_up_id');
    }
}