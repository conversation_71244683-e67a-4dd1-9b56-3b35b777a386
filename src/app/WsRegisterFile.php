<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use App\Http\Controllers\Integrations\SoapRequest\DocumentManagerController;

class WsRegisterFile extends Model
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'ws_dm_register_files';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'request_date_time',
        'response_date_time',
        'system_invoked_service',
        'result_code',
        'result_description',
        'transaction_id',
        'content',
        'case_number',
        'document_name',
        'extension',
        'document_code',
        'identification_code',
        'number_of_sheets',
        'document_series_code',
        'procedure_code',
        'sub_procedure_code',
        'version',
        'mtom_page_type',
        'activity_id',
        'affiliate_id',
    ];

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = true;
}