<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use OwenIt\Auditing\Contracts\Auditable;

class Permission extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable;
    const TRAMITE = 'TRAMITE';
    const INFORMACION_DE_LA_ACTIVIDAD = 'INFORMACION_DE_LA_ACTIVIDAD';
    const CARGAR_DOCUMENTOS = 'CARGAR_DOCUMENTOS';
    const NUEVA_ACCION = 'NUEVA_ACCION';
    const HISTORIAL_DE_ACCIONES = 'HISTORIAL_DE_ACCIONES';

    public static function general_permissions()
    {

        return [
            self::TRAMITE,
            self::INFORMACION_DE_LA_ACTIVIDAD,
            self::CARGAR_DOCUMENTOS,
            self::NUEVA_ACCION,
            self::HISTORIAL_DE_ACCIONES
        ];
    }

    public function areaPermissions()
    {
        return $this->hasMany(AreaPermission::class);
    }
}
