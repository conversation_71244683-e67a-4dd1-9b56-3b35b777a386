<?php

namespace App;

use App\LoggedModel;
use Carbon\Carbon;
use OwenIt\Auditing\Contracts\Auditable;
use Storage;

class Employment extends LoggedModel implements Auditable
{
    use \OwenIt\Auditing\Auditable;
    protected $dates = ['deleted_at', 'start_date', 'end_date'];
    protected $casts = [
        'actual' => 'boolean',
    ];
    protected $appends = array('period');

    protected $fillable = [
        'employer_id',
        'affiliate_id',
        'branch_id',
        'actual',
        'position',
        'ocupation',
        'start_date',
        'company_time',
        'position_time',
        'position_functions',
        'mission_employer',
        'use_protection_elements',
    ];

    public function getOcupationNameAttribute()
    {
        if (strpos($this->ocupation, 'N') !== false) {
            $json = Storage::disk('public')->get('new_ciuo.json');
            $json = json_decode($json);
            foreach ($json as $item) {
                if ($item->COD == $this->ocupation) {
                    return mb_strtoupper($item->DESCRIPTION);
                }
            }
        }
        $json = Storage::disk('public')->get('ciuo.json');
        $json = json_decode($json);
        foreach ($json as $item) {
            if ($item->COD == $this->ocupation) {
                return mb_strtoupper($item->DESCRIPTION);
            }
        }

        return null;
    }

    public function getOcupationCodeAttribute()
    {
        if (strpos($this->ocupation, 'N') !== false) {
            return substr($this->ocupation, 0, 4);
        }
        if($this->ocupation){
            return $this->ocupation;
        }

        return null;
    }

    public function getPeriodAttribute()
    {
        Carbon::setLocale('es');
        $period = $this->start_date->formatLocalized('%B %d, %Y');
        $period .= ' a ' . ($this->actual || $this->end_date == null ? 'la actualidad' : $this->end_date->formatLocalized('%B %d, %Y'));
        return $period;
    }

    public function getPositionAttribute($value)
    {
        return mb_strtoupper($value);
    }

    public function getEmployerName()
    {
        return ($this->employer_id == null ? 'INDEPENDIENTE' : $this->employer->name);
    }

    public function employer()
    {
        return $this->belongsTo('App\Employer');
    }

    public function branch()
    {
        return $this->belongsTo('App\Branch');
    }

    public function affiliate()
    {
        return $this->belongsTo('App\Affiliate');
    }
}
