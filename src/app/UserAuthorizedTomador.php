<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class UserAuthorizedTomador extends Model
{
    protected $table = 'user_authorized_tomador';
    protected $fillable = ['tomador_id', 'user_id'];


    
    public function user()
    {
       
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

   
    public function tomador()
    {
        return $this->belongsTo(User::class, 'tomador_id', 'id');
    }

}
