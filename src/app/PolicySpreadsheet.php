<?php

namespace App;

use DateTime;
use Illuminate\Database\Eloquent\Model;

class PolicySpreadsheet extends Model
{
    protected $fillable = [
        'activity_id',
        'file',
        'file_txt',
        'observacion',
        'path_report',
        'entry_type'
    ];

    public function activity()
    {
        return $this->belongsTo('App\Activity');
    }

    public function policy_spreadsheet_affiliate()
    {
        return $this->hasMany('App\PolicySpreadsheetAffiliate');
    }

    public function activities()
    {
        return $this->hasMany('App\Activity');
    }

    public function fileUrl()
    {
        try {
            return \Storage::disk('s3')->url($this->file);
        } catch (\Exception $e) {
            return null;
        }
    }
}
