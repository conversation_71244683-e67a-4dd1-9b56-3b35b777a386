<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use OwenIt\Auditing\Contracts\Auditable;

class InvalidityStatePfourPclDiagnosis extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable;
    protected $table = 'invalidity_state_pfour_pcl_diagnosis';

    public function getDescriptionAttribute($value)
    {
        return mb_strtoupper($value, 'utf-8');
    }

    public function getCodeAttribute($value)
    {
        return mb_strtoupper($value, 'utf-8');
    }
}
