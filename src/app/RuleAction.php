<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use OwenIt\Auditing\Contracts\Auditable;


class RuleAction extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable;
    protected $fillable = [
        'initial_state',
        'yes_action_id',
        'yes_area_id',
        'no_action_id',
        'no_area_id',
        'author_id',
        'yes_description',
        'no_description',
        'from_date',
        'percentage',
        'frequency_in_minutes',
        'enable',
    ];

    public function service()
    {
        return $this->belongsTo(Service::class);
    }

    public function initialState()
    {
        return $this->belongsTo(State::class, 'initial_state');
    }

    public function yesAction()
    {
        return $this->belongsTo(Action::class, 'yes_action_id');
    }

    public function noAction()
    {
        return $this->belongsTo(Action::class, 'no_action_id');
    }

    public function yesArea()
    {
        return $this->belongsTo(Area::class, 'yes_area_id');
    }

    public function noArea()
    {
        return $this->belongsTo(Area::class, 'no_area_id');
    }
}
