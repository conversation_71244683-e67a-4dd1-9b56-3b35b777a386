<?php

namespace App;

use DateTime;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class Medication extends Model
{
    public function activity()
    {
        return $this->belongsTo('App\Activity');
    }

    protected $fillable = [
        'delivery_date',
        'delivery_mode',
        'reason_cancel',
        'analyst',
        'provider',
        'provider_medication_code',
        'reps_code',
        'provider_identification',
        'issuing_ips',
        'doctor_name_generating_prescription',
        'medical_license',
        'specialty',
        'diagnosis_origin_prescription',
        'diagnosis_origin_controlled_medication',
        'province_supplier_details',
        'canton_supplier_details',
        'district_supplier_details',
        'province_controlled_medication',
        'canton_controlled_medication',
        'district_controlled_medication',
        'province_medical_prescription',
        'canton_medical_prescription',
        'district_medical_prescription',
        'provider_assignment_date',
        'medication_delivery_date',
        'delivery_type',
        'delivery_channel',
        'approved_service',
        'observation_audit',
        'medications',
        'name_patient',
        'activity',
        'date_dictamen',
        'name_patron',
        'id_patron',
        'num_policy_sort',
        'activity_id',
        'date_case',
        'number_case',
        'pharmacies_branch_controlled_medication',
        'pharmacies_branch_prescription',
        'pickup_name',
        'pickup_id',
        'pickup_phone',
        'consecutive',
        'sucursal_id',
        'sucursal_name',
        'external_purchase'
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($medication) {
            $currentDate = date('Ymd');

            $nextConsecutive = DB::table('medications')
                                ->lockForUpdate()
                                ->selectRaw("COALESCE(MAX(id) + 1, 1) AS next_consecutive")
                                ->value('next_consecutive');

            if ($nextConsecutive <= 9999) {
                $medication->consecutive = "REC-{$currentDate}-" . str_pad($nextConsecutive, 4, '0', STR_PAD_LEFT);
            }else {
                $medication->consecutive = "REC-{$currentDate}-" . $nextConsecutive;
            }

        });
    }

    public function diagnostics()
    {
        return $this->hasMany('App\MedicationServiceDiagnostics', 'medication_service_sort_id');
    }
    public function medical_prescriptions()
    {
        return $this->hasMany('App\MedicationServiceMedicalPrescription' ,'medication_service_sort_id');
    }
    public function controlled_medications()
    {
        return $this->hasMany('App\MedicationServiceControlledMedication' ,'medication_service_sort_id');
    }

    public function provider()
    {
        return Provider::where('id', $this->provider)->first();
    }
}
