<?php

namespace App;

use App\Notifications\ResetPassword;
use App\Providers\AppServiceProvider;
use Aws\Credentials\Credentials;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Facades\Auth;
use Aws\S3\S3Client;
use OwenIt\Auditing\Contracts\Auditable;

class User extends Authenticatable implements Auditable
{
    use \OwenIt\Auditing\Auditable;
    use Notifiable;

    const TYPE_USER_ADMIN = 'ADM';
    const USER_JCRICAURTE = 6;
    const USER_BARODRIGUEZ = 28;
    const USER_ADMIN = 1;
    const USER_AFQUINTANILLA = 231;
//    const USER_DFVILLAMIL = 245;
    const USER_LRBENAVIDES = 261;
    const USER_WDSOLER = 265;
    const USER_LDRONDONG = 376;
    const USER_ACONTRERASM = 38;
    const USER_ASISTENTEPCL = 614;
    const USER_YAMENDOZAV = 732;

//    Proveedores
    const USER_PROVEEDOR_DOKKA = 100;
    const USER_PROVEEDOR_EMERGENCIAS_MEDICAS = 101;
    const USER_PROVEEDOR_ADDIUVA  = 102;
    const USER_PROVEEDOR_CRUZ_ROJA = 103;
    const USER_PROVEEDOR_CCRR = 104;
    const USER_PROVEEDOR_BMI = 105;
    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        'password', 'remember_token', 'active',
    ];

    protected $fillable = [
        'password',
        'old_password3',
        'old_password2',
        'old_password1',
        'next_update',
        'email',
        'affiliate_id',
        'full_name',
        'first_name',
        'last_name',
        'username' ,
        'active' ,
        'identification_number',
        'area_id',
        'code_mnk',
        'resp_acsel_login',
        'tomador_id',
        'provider_id',
        'unique_code',
        'autorizados'
    ];

    protected $dates = ['deleted_at'];

    public function area()
    {
        return $this->belongsTo('App\Area');
    }

    public function schedule()
    {
        return $this->hasOne(Schedule::class, 'user_id', 'id');
    }

    public function getFullNameAttribute($value)
    {
        return mb_strtoupper($value);
    }

    public function getNameAttribute()
    {
        return $this->full_name;
    }

    public function clients()
    {
        return $this->belongsToMany('App\Client', 'user_clients');
    }

    public function provider(){
        return $this->belongsTo('App\Provider');
    }

    public function affiliate(){
        return $this->belongsTo('App\Affiliate');
    }

    public function clients_ids()
    {
        $ids = [];
        foreach ($this->clients as $client) {
            $ids[] = $client->id;
        }
        return $ids;
    }


    /**
     * @todo ROLE CHECK AND AREA CHECK
     */
    public function checkAccess($client)
    {
        return true;
    }

    
    public function userPermission()
    {
        return $this->hasMany('App\UserAuthorizedPolicies');
    }

    public function isCorrespondence()
    {
        return $this->area_id == 2 || $this->area_id == 5; // 2 => CORRESPONDENCIA IN db.areas;
    }

    public function isAdmin()
    {
        return $this->area_id == 1;
    }

    public function isAnalistaIndemnizaciones()
    {
        return $this->area_id == 49;
    }

    public function isAuditor()
    {
        return $this->area_id == 48;
    }

    public function isCall()
    {
        return $this->area_id == 45;
    }

    public function Analista_Monitoreo_Control()
    {
        return $this->area_id == 53;
    }

    public function isCommercialExecutive()
    {
        return $this->area_id == 56;
    }
    public function isExecutiveMNK()
    {
        return $this->area_id == 58;
    }
    public function isAdmin2()
    {
        return $this->area_id == 16 || $this->area_id == 10 || $this->area_id == 17 || $this->area_id == 28 || $this->area_id == 1;
    }
    public function isMigratorPClData()
    {
        return $this->area_id == 1 || $this->area_id == 17 || $this->area_id == 7;
    }
    public function isAgenda()
    {
        return $this->area_id == 1 || $this->area_id == 19 || $this->area_id == 10;
    }
    public function isVisibleAgenda()
    {
        return $this->area_id == 25 || $this->area_id == 19 || $this->area_id == 20 || $this->area_id == 23;
    }
    public function isCoordinatorCallCenter()
    {
        return $this->area_id == 33;
    }
    public function isFisio()
    {
        return $this->area_id == 20;
    }
    public function isCallCenterAgent()
    {
        return $this->area_id == 19;
    }
    public function isNotPclRei()
    {
        return $this->area_id == 16 || $this->area_id == 17;
    }
    public function isCloseAdmon()
    {
        return $this->area_id == 17;
    }
    public function isConsultant()
    {
        return $this->area_id == 14;
    }
    public function isLimitedGestion()
    {
        return $this->area_id == 32 || $this->area_id == 33 || $this->area_id == 34;
    }
    public function valorationEdit()
    {
        return $this->area_id == 1 || $this->area_id == 19 || $this->area_id == 10 || $this->area_id == 33;
    }
    public function regenerateDocIt()
    {
        return $this->area_id == 1 || $this->area_id == 16;
    }

    public function isAdvanced()
    {
        return $this->area_id == 1;
    }
    public function isReport()
    {
        return $this->area_id == 35 || $this->email == '<EMAIL>';
    }

    public function isBasic()
    {
        return false;
    }

    public function isLimited()
    {
        return false;
    }

    public function isViewer($limitedToo = true)
    {
        return false;
    }

    public function isProvider()
    {
        return $this->area_id == 47;
    }

    # Professional
    public function isProfessional()
    {
        return $this->extra_data == 'PROFESIONAL';
    }

    public function isAuxiliar()
    {
        return $this->extra_data == 'AUXILIAR';
    }

    /**
     * Send the password reset notification.
     *
     * @param string $token
     * @return void
     */
    public function sendPasswordResetNotification($token)
    {
        $this->notify(new ResetPassword($token));
    }

    public function showToAuthorizedUsersInMainCargues()
    {
        $AuthorizedEmails = ['<EMAIL>', '<EMAIL>', '<EMAIL>',
            '<EMAIL>', '<EMAIL>', '<EMAIL>',
            '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>',
            '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'
        ];

        return in_array(Auth::user()->email, $AuthorizedEmails);
    }

    public function showToAuthorizedUsersInMainCargueRei()
    {
        $AuthorizedEmails = ['<EMAIL>', '<EMAIL>',
        ];

        return in_array(Auth::user()->email, $AuthorizedEmails);
    }

    public function zoomApiKey()
    {
        return 'gxhYdlJZSv6Vlpu8HCDkJQ';
    }

    public function zoomApiSecret()
    {
        return 'EJOCrrM8rbfjgJmOy7IQZnH95sbW435W4iT1';
    }

    public function zoomApiEmail($client_id)
    {

        return $this->zoom_api_secret ? $this->email : '<EMAIL>';
    }

    /**
     * Esta es la configuración de roles y permisos del usuario
     */


    /**
     * Válida sí tiene permiso general de ver sobre el servicio
     * @param $service_id
     * @param $permission
     * @return bool
     */
    public function is_view_general_permission_service($service_id, $permission)
    {
        $user = auth()->user();

        if ($service_id == Service::SERVICE_MEDICAL_SERVICES_SORT_MNK) {
            return $user->area_id === Area::PROVIDER && $user->provider->clap == 1 || $user->area_id === Area::ADMINISTRATIVE;
        }

        return (bool)Service::select('services.*')
            ->leftJoin('permissions as p', 'services.id', '=', 'p.service_id')
            ->leftJoin('area_permissions as ap', 'p.id', '=', 'ap.permission_id')
            ->where('services.id', $service_id)
            ->where('ap.area_id', $this->area_id)
            ->where('ap.view', 1)
            ->where('p.name', $permission)
            ->first();
    }

    /**
     * Válida sí tiene permiso de editar sobre el servicio
     * @param $service_id
     * @param $permission
     * @return bool
     */
    public function is_edit_general_permission_service($service_id, $permission)
    {
        return (bool)Service::select('services.*')
            ->leftJoin('permissions as p', 'services.id', '=', 'p.service_id')
            ->leftJoin('area_permissions as ap', 'p.id', '=', 'ap.permission_id')
            ->where('services.id', $service_id)
            ->where('ap.area_id', $this->area_id)
            ->where('ap.edit', 1)
            ->where('p.name', $permission)
            ->first();
    }

    /**
     * Obtener la lista de permisos de un servicio
     * @param $service_id
     * @return mixed
     */
    public function service_permissions_list($service_id)
    {
        return Permission::where('service_id', $service_id)
            ->where(function ($query) {
                $query->where('description', '!=', 'GENERAL')
                    ->orWhereNull('description');
            })
            ->with(['areaPermissions' => function ($query) {
                $query->where('area_id', $this->area_id);
            }])
            ->get();
    }

    /**
     * Valida si un usuario puede ver un servicio
     * @param $service_id
     * @return bool
     */
    public function is_view_service($service_id)
    {
        return (bool)Service::select('services.*')
            ->leftJoin('permissions as p', 'services.id', '=', 'p.service_id')
            ->leftJoin('area_permissions as ap', 'p.id', '=', 'ap.permission_id')
            ->where('services.id', $service_id)
            ->where('ap.area_id', $this->area_id)
            ->where('ap.view', 1)
            ->where('p.name', Permission::TRAMITE)
            ->first();
    }

    /**
     * Obtener la lista de servicios a los cuales les puede crear trámites
     * @return array
     */
    public function colpensiones_services()
    {
        $create_service_list = Service::select('services.*')
            ->leftJoin('permissions as p', 'services.id', '=', 'p.service_id')
            ->leftJoin('area_permissions as ap', 'p.id', '=', 'ap.permission_id')
            ->where('ap.area_id', $this->area_id)
            ->where('ap.edit', 1)
            ->where('p.name', Permission::TRAMITE)
            ->get();
        $access_services = $create_service_list->pluck('id')->toArray();
        $colpensiones = AppServiceProvider::$MNK_SERVICES;
        foreach ($colpensiones as $key => $value) {
            $colpensiones[$key]['OPTIONS'] = array_intersect_key(
                $value['OPTIONS'],
                array_flip($access_services)
            );
        }
        return array_filter($colpensiones, function ($service) {
            return !empty($service['OPTIONS']);
        });
    }

    public function view_case_search()
    {
        return true;
    }

    public function view_gestion()
    {
        return Area::find($this->area_id)->gestion == 1;
    }
    public function view_aseguramiento()
    {
        return Area::find($this->area_id)->aseguramiento == 1;
    }
    public function view_busqueda()
    {
        return Area::find($this->area_id)->busqueda == 1;
    }
    public function view_bandeja()
    {
        return Area::find($this->area_id)->bandeja == 1;
    }
    public function view_variations()
    {
        return Area::find($this->area_id)->variations == 1;
    }
    public function view_cobros()
    {
        return Area::find($this->area_id)->cobros == 1;
    }
    public function view_provider()
    {
        return Area::find($this->area_id)->provider == 1;
    }
    public function view_auditoria()
    {
        return Area::find($this->area_id)->auditoria == 1;
    }
    public function view_monitoreo()
    {
        return Area::find($this->area_id)->monitoreo == 1;
    }
    public function view_tablero_intermediario()
    {
        return Area::find($this->area_id)->intermediario == 1;
    }
    public function view_Tablero_ejecutivo_comercial()
    {
        return Area::find($this->area_id)->ejecutivo_comercial == 1;
    }
    public function view_Tablero_autorizaciones()
    {
        return Area::find($this->area_id)->autorizaciones == 1;
    }
    public function view_tomador()
    {
        return Area::find($this->area_id)->tomador == 1;
    }
    public function view_tomador_autorizado()
    {
        return $this->area_id == Area::TOMADOR_AUTORIZADO;
    }

    public function isTomador()
    {
        return $this->area_id == Area::TOMADOR;
    }
    public function view_autorizado()
    {
        return $this->area_id == Area::AUTORIZADO;
    }

    public function find_tomador_autorizado()
    {
        $user = User::find($this->tomador_id);
        $tomador = $user ? $user->affiliate_id : null;
        return $tomador;
    }
    public function view_afiliado()
    {
        return Area::find($this->area_id)->afiliado == 1;
    }
    public function view_call_center()
    {
        return Area::find($this->area_id)->call_center == 1;
    }
    public function view_cuentas()
    {
        return Area::find($this->area_id)->cuentas == 1;
    }
    public function view_auditoria_cuentas()
    {
        return Area::find($this->area_id)->auditoria_cuentas == 1;
    }

    public function view_compensation_board()
    {
        return $this->area_id == 1 || $this->area_id == 49 ;
    }
    public function view_reports()
    {
        return Area::find($this->area_id)->reports == 1;
    }

    public function view_administrator()
    {
        return Area::find($this->area_id)->administrator == 1;
    }

    //Función para validar usuario intermediario
    public function view_intermediario()
    {
        //se consulta frente a la tabla Area si el usuiario es INTERMEDIARIO
        return Area::find($this->area_id)->name ==  'Intermediario';
    }
    public function view_asesor()
    {
        //se consulta frente a la tabla Area si el usuiario es ASESOR
        return Area::find($this->area_id)->name ==  'Asesor';
    }

    public function view_loads()
    {
        return Area::find($this->area_id)->loads == 1;
    }

    public function getSignFromId($user_id)
    {
        if ($user_id) {
            $user = self::find($user_id);

            if ($user) {

                return [
                    'email' => $user->email,
                    'full_name' => strtoupper($user->first_name . ' ' . $user->last_name),
                    'photo' => $user->photo,
                    'signature' => $user->signature,
                    'rm' => $user->medical_record_number,
                    'license' => $user->license_number,
                    'rethus' => $user->rethus,
                    'company' => $user->company_id? Company::find($user->company_id)->name : '',
                ];
            }

        }
        return null;
    }
    //Validar si tiene acceso a los reportes
    public function canViewReport($reportColumn)
    {
        $area = Area::find($this->area_id);
        return $area ? $area->$reportColumn == 1 : false;
    }
    public function isTPA()
    {
        return $this->area_id == 62;
    }

    public function isProviderCCSS()
    {
        return $this->isProvider() && strtoupper(optional($this->provider)->name) == 'CCSS';
    }

    public function isProviderINS()
    {
        return $this->isProvider() && strtoupper(optional($this->provider)->name) == 'INS';
    }

    public function isAdministrativoMNK()
    {
        return $this->area_id == 64;
    }

    public function view_correos()
    {
        return Area::find($this->area_id)->correos == 1;
    }
}
