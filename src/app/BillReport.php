<?php

namespace App;

class BillReport
{
    const AT_SQL = "
		SELECT
		SR.name AS NOMBRE_TIPO_SERVICIO,
		A.id AS ID_SERVICIO,
		CONCAT(AF.first_name, ' ', AF.last_name) AS NOMBRE,
		AF.doc_type AS ID_TIPO_DOC,
		AF.doc_number AS IDENTIFICACION,
		AA.created_at AS FECHA_SERVICIO,
		U.full_name AS FUNCIONARIO,
		'' AS DETALLE
		FROM activities AS A
		INNER JOIN activity_actions AS AA
		ON AA.id = (
			SELECT id
            FROM activity_actions
            WHERE activity_id = A.id AND action_id IN (8,15,148)
            ORDER BY created_at ASC
            LIMIT 1
        )
		INNER JOIN affiliates AS AF
		ON A.affiliate_id = AF.id
		INNER JOIN users AS U
		ON A.user_id = U.id
		INNER JOIN dicta AS D
		ON D.activity_id = A.id
		INNER JOIN states AS S
		ON A.state_id = S.id
		INNER JOIN services AS SR
		ON A.service_id = SR.id
		LEFT JOIN employments AS E
		ON A.employment_id = E.id
		LEFT JOIN employers AS EM
		ON E.employer_id = EM.id
		WHERE A.client_id = ? AND AA.created_at BETWEEN ? AND ? AND A.service_id IN (1)
	";
    const EL_SQL = "
		SELECT
		SR.name AS NOMBRE_TIPO_SERVICIO,
		A.id AS ID_SERVICIO,
		CONCAT(AF.first_name, ' ', AF.last_name) AS NOMBRE,
		AF.doc_type AS ID_TIPO_DOC,
		AF.doc_number AS IDENTIFICACION,
		AA.created_at AS FECHA_SERVICIO,
		U.full_name AS FUNCIONARIO,
		'' AS DETALLE
		FROM activities AS A
		INNER JOIN activity_actions AS AA
		ON AA.id = (
			SELECT id
            FROM activity_actions
            WHERE activity_id = A.id AND action_id IN (8,15,148)
            ORDER BY created_at ASC
            LIMIT 1
        )
		INNER JOIN affiliates AS AF
		ON A.affiliate_id = AF.id
		INNER JOIN users AS U
		ON A.user_id = U.id
		INNER JOIN dicta AS D
		ON D.activity_id = A.id
		INNER JOIN states AS S
		ON A.state_id = S.id
		INNER JOIN services AS SR
		ON A.service_id = SR.id
		LEFT JOIN employments AS E
		ON A.employment_id = E.id
		LEFT JOIN employers AS EM
		ON E.employer_id = EM.id
		WHERE A.client_id = ? AND AA.created_at BETWEEN ? AND ? AND A.service_id IN (2)
	";
    const PCL_SQL = "
		SELECT
		SR.name AS NOMBRE_TIPO_SERVICIO,
		A.id AS ID_SERVICIO,
		CONCAT(AF.first_name, ' ', AF.last_name) AS NOMBRE,
		AF.doc_type AS ID_TIPO_DOC,
		AF.doc_number AS IDENTIFICACION,
		AA.created_at AS FECHA_SERVICIO,
		UA.full_name AS FUNCIONARIO,
		'' AS DETALLE
		FROM activities AS A
		INNER JOIN activity_actions AS AA
		ON AA.id = (
			SELECT id
            FROM activity_actions
            WHERE activity_id = A.id AND action_id IN (157)
            ORDER BY created_at ASC
            LIMIT 1
        )
		INNER JOIN action_service_states ASS
		ON ASS.service_id = A.service_id AND ASS.action_id = AA.action_id
		INNER JOIN states AS AAS
		ON ASS.state_id = AAS.id
		INNER JOIN users AS UA
		ON AA.author_id = UA.id
		INNER JOIN affiliates AS AF
		ON A.affiliate_id = AF.id
		INNER JOIN users AS U
		ON A.user_id = U.id
		INNER JOIN pcls AS P
		ON P.activity_id = A.id
		INNER JOIN states AS S
		ON A.state_id = S.id
		INNER JOIN services AS SR
		ON A.service_id = SR.id
		LEFT JOIN employments AS E
		ON A.employment_id = E.id
		LEFT JOIN employers AS EM
		ON E.employer_id = EM.id
		WHERE A.client_id = ? AND AA.created_at BETWEEN ? AND ? AND A.service_id IN (8,9)
	";

    const REHABILITATION_SQL = "
		SELECT
		SR.name AS NOMBRE_TIPO_SERVICIO,
		A.id AS ID_SERVICIO,
		CONCAT(AF.first_name, ' ', AF.last_name) AS NOMBRE,
		AF.doc_type AS ID_TIPO_DOC,
		AF.doc_number AS IDENTIFICACION,
		AA.created_at AS FECHA_SERVICIO,
		UA.full_name AS FUNCIONARIO,
		'' AS DETALLE
		FROM activities AS A
		INNER JOIN activity_actions AS AA
		ON AA.id = (
			SELECT id
            FROM activity_actions
            WHERE activity_id = A.id AND action_id IN (156)
            ORDER BY created_at ASC
            LIMIT 1
        )
		INNER JOIN action_service_states ASS
		ON ASS.service_id = A.service_id AND ASS.action_id = AA.action_id
		INNER JOIN states AS AAS
		ON ASS.state_id = AAS.id
		INNER JOIN users AS UA
		ON AA.author_id = UA.id
		INNER JOIN affiliates AS AF
		ON A.affiliate_id = AF.id
		INNER JOIN rehabilitations AS R
		ON R.activity_id = A.id
		INNER JOIN services AS SR
		ON A.service_id = SR.id
		LEFT JOIN employments AS E
		ON A.employment_id = E.id
		LEFT JOIN employers AS EM
		ON E.employer_id = EM.id
		WHERE A.client_id = ? AND AA.created_at BETWEEN ? AND ? AND A.service_id IN (12)
	";

    const RECOMMENDATION_SQL = "
		SELECT
		SR.name AS NOMBRE_TIPO_SERVICIO,
		A.id AS ID_SERVICIO,
		CONCAT(AF.first_name, ' ', AF.last_name) AS NOMBRE,
		AF.doc_type AS ID_TIPO_DOC,
		AF.doc_number AS IDENTIFICACION,
		AA.created_at AS FECHA_SERVICIO,
		UA.full_name AS FUNCIONARIO,
		'' AS DETALLE
		FROM activities AS A
		INNER JOIN activity_actions AS AA
		ON AA.id = (
			SELECT id
            FROM activity_actions
            WHERE activity_id = A.id AND action_id IN (150,152)
            AND deleted_at IS NULL
            ORDER BY FIELD(action_id, 150, 152), created_at ASC
            LIMIT 1
        )
		INNER JOIN action_service_states ASS
		ON ASS.service_id = A.service_id AND ASS.action_id = AA.action_id
		INNER JOIN states AS AAS
		ON ASS.state_id = AAS.id
		INNER JOIN users AS UA
		ON AA.author_id = UA.id
		INNER JOIN affiliates AS AF
		ON A.affiliate_id = AF.id
		INNER JOIN recommendations AS R
		ON R.activity_id = A.id
		INNER JOIN services AS SR
		ON A.service_id = SR.id
		LEFT JOIN employments AS E
		ON A.employment_id = E.id
		LEFT JOIN employers AS EM
		ON E.employer_id = EM.id
		WHERE A.client_id = ? AND AA.created_at BETWEEN ? AND ? AND A.service_id IN (13)
	";

    const DISABILITY_SQL = "
		SELECT
		SR.name AS NOMBRE_TIPO_SERVICIO,
		A.id AS ID_SERVICIO,
		CONCAT(AF.first_name, ' ', AF.last_name) AS NOMBRE,
		AF.doc_type AS ID_TIPO_DOC,
		AF.doc_number AS IDENTIFICACION,
		AA.created_at AS FECHA_SERVICIO,
		UA.full_name AS FUNCIONARIO,
		'' AS DETALLE
		FROM activities AS A
		INNER JOIN activity_actions AS AA
		ON AA.id = (
			SELECT id
            FROM activity_actions
            WHERE activity_id = A.id AND action_id IN (154)
            AND deleted_at IS NULL
            ORDER BY FIELD(action_id, 154), created_at ASC
            LIMIT 1
        )
		INNER JOIN action_service_states ASS
		ON ASS.service_id = A.service_id AND ASS.action_id = AA.action_id
		INNER JOIN states AS AAS
		ON ASS.state_id = AAS.id
		INNER JOIN users AS UA
		ON AA.author_id = UA.id
		INNER JOIN affiliates AS AF
		ON A.affiliate_id = AF.id
		INNER JOIN users AS U
		ON A.user_id = U.id
		INNER JOIN disabilities AS P
		ON P.activity_id = A.id
		INNER JOIN states AS S
		ON A.state_id = S.id
		INNER JOIN services AS SR
		ON A.service_id = SR.id
		LEFT JOIN employments AS E
		ON A.employment_id = E.id
		LEFT JOIN employers AS EM
		ON E.employer_id = EM.id
		WHERE A.client_id = ? AND AA.created_at BETWEEN ? AND ? AND A.service_id IN (14)
	";

    const AUDIT_SQL = "
		SELECT
		SR.name AS NOMBRE_TIPO_SERVICIO,
		A.id AS ID_SERVICIO,
		CONCAT(AF.first_name, ' ', AF.last_name) AS NOMBRE,
		AF.doc_type AS ID_TIPO_DOC,
		AF.doc_number AS IDENTIFICACION,
		AA.created_at AS FECHA_SERVICIO,
		UA.full_name AS FUNCIONARIO,
		'' AS DETALLE
		FROM activities AS A
		INNER JOIN activity_actions AS AA
		ON AA.id = (
			SELECT id
            FROM activity_actions
            WHERE activity_id = A.id AND action_id IN (83,84,86,88)
            ORDER BY created_at ASC
            LIMIT 1
        )
		INNER JOIN action_service_states ASS
		ON ASS.service_id = A.service_id AND ASS.action_id = AA.action_id
		INNER JOIN states AS AAS
		ON ASS.state_id = AAS.id
		INNER JOIN users AS UA
		ON AA.author_id = UA.id
		INNER JOIN affiliates AS AF
		ON A.affiliate_id = AF.id
		INNER JOIN audits AS AUD
		ON AUD.activity_id = A.id
		INNER JOIN services AS SR
		ON A.service_id = SR.id
		LEFT JOIN employments AS E
		ON A.employment_id = E.id
		LEFT JOIN employers AS EM
		ON E.employer_id = EM.id
		WHERE A.client_id = ? AND AA.created_at BETWEEN ? AND ? AND A.service_id IN (15)
	";

    const AT_SQL_NEW = "
		SELECT
		SR.name AS NOMBRE_TIPO_SERVICIO,
		A.id AS ID_SERVICIO,
		CONCAT(AF.first_name, ' ', AF.last_name) AS NOMBRE,
		AF.doc_type AS ID_TIPO_DOC,
		AF.doc_number AS IDENTIFICACION,
		AA.created_at AS FECHA_SERVICIO,
		U.full_name AS FUNCIONARIO,
		'' AS DETALLE,
		IA.type AS TIPO,
		CONCAT(I.init_date, '-', I.last_date) AS PERIODO_CORRESPONDIENTE,
		I.date_invoice AS FECHA_FACTURA_REN,
		I.invoice_number AS NUMERO_FACTURA_REN,
		I.date_receive_invoice AS FECHA_RADICADO_REN,
		I.date_invoice_orienta AS FECHA_FACTURA_ORIENTA,
		I.invoice_number_orienta AS NUMERO_FACTURA_ORIENTA,
		'' AS FECHA_RADICADO_ORIENTA,
		DIA.user_name AS PROF_ADSCRITO,
		DIA.id AS FECHA_PROF_ADSCRITO
		FROM activities AS A
		INNER JOIN activity_actions AS AA
		ON AA.id = (
			SELECT id
            FROM activity_actions
            WHERE activity_id = A.id AND action_id IN (8,15,148)
            ORDER BY created_at ASC
            LIMIT 1
        )
        LEFT JOIN activity_actions AS AAD
        ON AAD.activity_id = A.id AND AAD.id = (
            SELECT id
            FROM activity_actions
            WHERE activity_id = A.id AND action_id IN (44, 55, 54)
            AND created_at > '2017-08-01'
            AND deleted_at IS NULL
            AND old_state_id IN (18, 22)
            ORDER BY created_at ASC
            LIMIT 1
        )
		INNER JOIN affiliates AS AF
		ON A.affiliate_id = AF.id
		INNER JOIN users AS U
		ON A.user_id = U.id
		INNER JOIN dicta AS D
		ON D.activity_id = A.id
		INNER JOIN states AS S
		ON A.state_id = S.id
		INNER JOIN services AS SR
		ON A.service_id = SR.id
		LEFT JOIN employments AS E
		ON A.employment_id = E.id
		LEFT JOIN employers AS EM
		ON E.employer_id = EM.id
		INNER JOIN invoice_activities AS IA
        ON (IA.activity_id = A.id AND IA.type = 'facturado')
        LEFT JOIN docinvoices AS DIA
        ON (IA.invoice_id = DIA.invoice_id AND AAD.author_id = DIA.user_id)
        LEFT JOIN invoices I ON IA.invoice_id = I.id
		WHERE A.client_id = ? AND IA.invoice_id = ? AND A.service_id IN (1) 
		AND IA.service = SR.name
	";

    const EL_SQL_NEW = "
		SELECT
		SR.name AS NOMBRE_TIPO_SERVICIO,
		A.id AS ID_SERVICIO,
		CONCAT(AF.first_name, ' ', AF.last_name) AS NOMBRE,
		AF.doc_type AS ID_TIPO_DOC,
		AF.doc_number AS IDENTIFICACION,
		AA.created_at AS FECHA_SERVICIO,
		U.full_name AS FUNCIONARIO,
		'' AS DETALLE,
		IA.type AS TIPO,
		CONCAT(I.init_date, '-', I.last_date) AS PERIODO_CORRESPONDIENTE,
		I.date_invoice AS FECHA_FACTURA_REN,
		I.invoice_number AS NUMERO_FACTURA_REN,
		I.date_receive_invoice AS FECHA_RADICADO_REN,
		I.date_invoice_orienta AS FECHA_FACTURA_ORIENTA,
		I.invoice_number_orienta AS NUMERO_FACTURA_ORIENTA,
		'' AS FECHA_RADICADO_ORIENTA,
		DIA.user_name AS PROF_ADSCRITO,
		DIA.id AS FECHA_PROF_ADSCRITO
		FROM activities AS A
		INNER JOIN activity_actions AS AA
		ON AA.id = (
			SELECT id
            FROM activity_actions
            WHERE activity_id = A.id AND action_id IN (8,15,148)
            ORDER BY created_at ASC
            LIMIT 1
        )
        LEFT JOIN activity_actions AS AAD
        ON AAD.activity_id = A.id AND AAD.id = (
            SELECT id
            FROM activity_actions
            WHERE activity_id = A.id AND action_id IN (44, 55, 54)
            AND created_at > '2017-08-01'
            AND deleted_at IS NULL
            AND old_state_id IN (18, 22)
            ORDER BY created_at ASC
            LIMIT 1
        )
		INNER JOIN affiliates AS AF
		ON A.affiliate_id = AF.id
		INNER JOIN users AS U
		ON A.user_id = U.id
		INNER JOIN dicta AS D
		ON D.activity_id = A.id
		INNER JOIN states AS S
		ON A.state_id = S.id
		INNER JOIN services AS SR
		ON A.service_id = SR.id
		LEFT JOIN employments AS E
		ON A.employment_id = E.id
		LEFT JOIN employers AS EM
		ON E.employer_id = EM.id
		INNER JOIN invoice_activities AS IA
        ON (IA.activity_id = A.id AND IA.type = 'facturado')
        LEFT JOIN docinvoices AS DIA
        ON (IA.invoice_id = DIA.invoice_id AND AAD.author_id = DIA.user_id)
        LEFT JOIN invoices I ON IA.invoice_id = I.id
		WHERE A.client_id = ? AND IA.invoice_id = ? AND A.service_id IN (2) 
		AND IA.service = SR.name
	";
    const PCL_SQL_NEW = "
		SELECT
		SR.name AS NOMBRE_TIPO_SERVICIO,
		A.id AS ID_SERVICIO,
		CONCAT(AF.first_name, ' ', AF.last_name) AS NOMBRE,
		AF.doc_type AS ID_TIPO_DOC,
		AF.doc_number AS IDENTIFICACION,
		AA.created_at AS FECHA_SERVICIO,
		UA.full_name AS FUNCIONARIO,
		'' AS DETALLE,
		IA.type AS TIPO,
		CONCAT(I.init_date, '-', I.last_date) AS PERIODO_CORRESPONDIENTE,
		I.date_invoice AS FECHA_FACTURA_REN,
		I.invoice_number AS NUMERO_FACTURA_REN,
		I.date_receive_invoice AS FECHA_RADICADO_REN,
		I.date_invoice_orienta AS FECHA_FACTURA_ORIENTA,
		I.invoice_number_orienta AS NUMERO_FACTURA_ORIENTA,
		'' AS FECHA_RADICADO_ORIENTA,
		DIA.user_name AS PROF_ADSCRITO,
		DIA.id AS FECHA_PROF_ADSCRITO
		FROM activities AS A
		INNER JOIN activity_actions AS AA
		ON AA.id = (
			SELECT id
            FROM activity_actions
            WHERE activity_id = A.id AND action_id IN (157)
            ORDER BY created_at ASC
            LIMIT 1
        )
        LEFT JOIN activity_actions AS AAD
        ON AAD.activity_id = A.id AND AAD.id = (
            SELECT id
            FROM activity_actions
            WHERE activity_id = A.id AND action_id IN (44, 55, 54)
            AND created_at > '2017-08-01'
            AND deleted_at IS NULL
            AND old_state_id IN (18, 22)
            ORDER BY created_at ASC
            LIMIT 1
        )
		INNER JOIN action_service_states ASS
		ON ASS.service_id = A.service_id AND ASS.action_id = AA.action_id
		INNER JOIN states AS AAS
		ON ASS.state_id = AAS.id
		INNER JOIN users AS UA
		ON AA.author_id = UA.id
		INNER JOIN affiliates AS AF
		ON A.affiliate_id = AF.id
		INNER JOIN users AS U
		ON A.user_id = U.id
		INNER JOIN pcls AS P
		ON P.activity_id = A.id
		INNER JOIN states AS S
		ON A.state_id = S.id
		INNER JOIN services AS SR
		ON A.service_id = SR.id
		LEFT JOIN employments AS E
		ON A.employment_id = E.id
		LEFT JOIN employers AS EM
		ON E.employer_id = EM.id
		INNER JOIN invoice_activities AS IA
        ON (IA.activity_id = A.id AND IA.type = 'facturado')
        LEFT JOIN docinvoices AS DIA
        ON (IA.invoice_id = DIA.invoice_id AND AAD.author_id = DIA.user_id)
        LEFT JOIN invoices I ON IA.invoice_id = I.id
		WHERE A.client_id = ? AND IA.invoice_id = ? AND A.service_id IN (8,9) 
		AND IA.service = SR.name
	";

    const REHABILITATION_SQL_NEW = "
		SELECT
		SR.name AS NOMBRE_TIPO_SERVICIO,
		A.id AS ID_SERVICIO,
		CONCAT(AF.first_name, ' ', AF.last_name) AS NOMBRE,
		AF.doc_type AS ID_TIPO_DOC,
		AF.doc_number AS IDENTIFICACION,
		AA.created_at AS FECHA_SERVICIO,
		UA.full_name AS FUNCIONARIO,
		'' AS DETALLE,
		IA.type AS TIPO,
		CONCAT(I.init_date, '-', I.last_date) AS PERIODO_CORRESPONDIENTE,
		I.date_invoice AS FECHA_FACTURA_REN,
		I.invoice_number AS NUMERO_FACTURA_REN,
		I.date_receive_invoice AS FECHA_RADICADO_REN,
		I.date_invoice_orienta AS FECHA_FACTURA_ORIENTA,
		I.invoice_number_orienta AS NUMERO_FACTURA_ORIENTA,
		'' AS FECHA_RADICADO_ORIENTA,
		'' AS PROF_ADSCRITO,
		'' AS FECHA_PROF_ADSCRITO
		FROM activities AS A
		INNER JOIN activity_actions AS AA
		ON AA.id = (
			SELECT id
            FROM activity_actions
            WHERE activity_id = A.id AND action_id IN (156)
            ORDER BY created_at ASC
            LIMIT 1
        )
		INNER JOIN action_service_states ASS
		ON ASS.service_id = A.service_id AND ASS.action_id = AA.action_id
		INNER JOIN states AS AAS
		ON ASS.state_id = AAS.id
		INNER JOIN users AS UA
		ON AA.author_id = UA.id
		INNER JOIN affiliates AS AF
		ON A.affiliate_id = AF.id
		INNER JOIN rehabilitations AS R
		ON R.activity_id = A.id
		INNER JOIN services AS SR
		ON A.service_id = SR.id
		LEFT JOIN employments AS E
		ON A.employment_id = E.id
		LEFT JOIN employers AS EM
		ON E.employer_id = EM.id
		INNER JOIN invoice_activities AS IA
        ON (IA.activity_id = A.id AND IA.type = 'facturado')
        LEFT JOIN invoices I ON IA.invoice_id = I.id
		WHERE A.client_id = ? AND IA.invoice_id = ? AND A.service_id IN (12)
		AND IA.service = SR.name
	";

    const RECOMMENDATION_SQL_NEW = "
		SELECT
		SR.name AS NOMBRE_TIPO_SERVICIO,
		A.id AS ID_SERVICIO,
		CONCAT(AF.first_name, ' ', AF.last_name) AS NOMBRE,
		AF.doc_type AS ID_TIPO_DOC,
		AF.doc_number AS IDENTIFICACION,
		AA.created_at AS FECHA_SERVICIO,
		UA.full_name AS FUNCIONARIO,
		'' AS DETALLE,
		IA.type AS TIPO,
		CONCAT(I.init_date, '-', I.last_date) AS PERIODO_CORRESPONDIENTE,
		I.date_invoice AS FECHA_FACTURA_REN,
		I.invoice_number AS NUMERO_FACTURA_REN,
		I.date_receive_invoice AS FECHA_RADICADO_REN,
		I.date_invoice_orienta AS FECHA_FACTURA_ORIENTA,
		I.invoice_number_orienta AS NUMERO_FACTURA_ORIENTA,
		'' AS FECHA_RADICADO_ORIENTA,
		'' AS PROF_ADSCRITO,
		'' AS FECHA_PROF_ADSCRITO
		FROM activities AS A
		INNER JOIN activity_actions AS AA
		ON AA.id = (
			SELECT id
            FROM activity_actions
            WHERE activity_id = A.id AND action_id IN (150,152)
            AND deleted_at IS NULL
            ORDER BY FIELD(action_id, 150, 152), created_at ASC
            LIMIT 1
        )
		INNER JOIN action_service_states ASS
		ON ASS.service_id = A.service_id AND ASS.action_id = AA.action_id
		INNER JOIN states AS AAS
		ON ASS.state_id = AAS.id
		INNER JOIN users AS UA
		ON AA.author_id = UA.id
		INNER JOIN affiliates AS AF
		ON A.affiliate_id = AF.id
		INNER JOIN recommendations AS R
		ON R.activity_id = A.id
		INNER JOIN services AS SR
		ON A.service_id = SR.id
		LEFT JOIN employments AS E
		ON A.employment_id = E.id
		LEFT JOIN employers AS EM
		ON E.employer_id = EM.id
		INNER JOIN invoice_activities AS IA
        ON (IA.activity_id = A.id AND IA.type = 'facturado')
        LEFT JOIN invoices I ON IA.invoice_id = I.id
		WHERE A.client_id = ? AND IA.invoice_id = ? AND A.service_id IN (13) 
		AND IA.service = SR.name
	";

    const DISABILITY_SQL_NEW = "
		SELECT
		SR.name AS NOMBRE_TIPO_SERVICIO,
		A.id AS ID_SERVICIO,
		CONCAT(AF.first_name, ' ', AF.last_name) AS NOMBRE,
		AF.doc_type AS ID_TIPO_DOC,
		AF.doc_number AS IDENTIFICACION,
		AA.created_at AS FECHA_SERVICIO,
		UA.full_name AS FUNCIONARIO,
		'' AS DETALLE,
		IA.type AS TIPO,
		CONCAT(I.init_date, '-', I.last_date) AS PERIODO_CORRESPONDIENTE,
		I.date_invoice AS FECHA_FACTURA_REN,
		I.invoice_number AS NUMERO_FACTURA_REN,
		I.date_receive_invoice AS FECHA_RADICADO_REN,
		I.date_invoice_orienta AS FECHA_FACTURA_ORIENTA,
		I.invoice_number_orienta AS NUMERO_FACTURA_ORIENTA,
		'' AS FECHA_RADICADO_ORIENTA,
		'' AS PROF_ADSCRITO,
		'' AS FECHA_PROF_ADSCRITO
		FROM activities AS A
		INNER JOIN activity_actions AS AA
		ON AA.id = (
			SELECT id
            FROM activity_actions
            WHERE activity_id = A.id AND action_id IN (154)
            AND deleted_at IS NULL
            ORDER BY FIELD(action_id, 154), created_at ASC
            LIMIT 1
        )
		INNER JOIN action_service_states ASS
		ON ASS.service_id = A.service_id AND ASS.action_id = AA.action_id
		INNER JOIN states AS AAS
		ON ASS.state_id = AAS.id
		INNER JOIN users AS UA
		ON AA.author_id = UA.id
		INNER JOIN affiliates AS AF
		ON A.affiliate_id = AF.id
		INNER JOIN users AS U
		ON A.user_id = U.id
		INNER JOIN disabilities AS P
		ON P.activity_id = A.id
		INNER JOIN states AS S
		ON A.state_id = S.id
		INNER JOIN services AS SR
		ON A.service_id = SR.id
		LEFT JOIN employments AS E
		ON A.employment_id = E.id
		LEFT JOIN employers AS EM
		ON E.employer_id = EM.id
		INNER JOIN invoice_activities AS IA
        ON (IA.activity_id = A.id AND IA.type = 'facturado')
        LEFT JOIN invoices I ON IA.invoice_id = I.id
		WHERE A.client_id = ? AND IA.invoice_id = ? AND A.service_id IN (14) 
		AND IA.service = SR.name
	";

    const AUDIT_SQL_NEW = "
		SELECT
		SR.name AS NOMBRE_TIPO_SERVICIO,
		A.id AS ID_SERVICIO,
		CONCAT(AF.first_name, ' ', AF.last_name) AS NOMBRE,
		AF.doc_type AS ID_TIPO_DOC,
		AF.doc_number AS IDENTIFICACION,
		AA.created_at AS FECHA_SERVICIO,
		UA.full_name AS FUNCIONARIO,
		'' AS DETALLE,
		IA.type AS TIPO,
		CONCAT(I.init_date, '-', I.last_date) AS PERIODO_CORRESPONDIENTE,
		I.date_invoice AS FECHA_FACTURA_REN,
		I.invoice_number AS NUMERO_FACTURA_REN,
		I.date_receive_invoice AS FECHA_RADICADO_REN,
		I.date_invoice_orienta AS FECHA_FACTURA_ORIENTA,
		I.invoice_number_orienta AS NUMERO_FACTURA_ORIENTA,
		'' AS FECHA_RADICADO_ORIENTA,
		'' AS PROF_ADSCRITO,
		'' AS FECHA_PROF_ADSCRITO
		FROM activities AS A
		INNER JOIN activity_actions AS AA
		ON AA.id = (
			SELECT id
            FROM activity_actions
            WHERE activity_id = A.id AND action_id IN (83,84,86,88)
            ORDER BY created_at ASC
            LIMIT 1
        )
		INNER JOIN action_service_states ASS
		ON ASS.service_id = A.service_id AND ASS.action_id = AA.action_id
		INNER JOIN states AS AAS
		ON ASS.state_id = AAS.id
		INNER JOIN users AS UA
		ON AA.author_id = UA.id
		INNER JOIN affiliates AS AF
		ON A.affiliate_id = AF.id
		INNER JOIN audits AS AUD
		ON AUD.activity_id = A.id
		INNER JOIN services AS SR
		ON A.service_id = SR.id
		LEFT JOIN employments AS E
		ON A.employment_id = E.id
		LEFT JOIN employers AS EM
		ON E.employer_id = EM.id
		INNER JOIN invoice_activities AS IA
        ON (IA.activity_id = A.id AND IA.type = 'facturado')
        LEFT JOIN invoices I ON IA.invoice_id = I.id
		WHERE A.client_id = ? AND IA.invoice_id = ? AND A.service_id IN (15)
		AND IA.service = SR.name
	";

    const CONSOLIDACION_SQL_NEW = "
        SELECT 
		'CONSOLIDACION DOCS' AS NOMBRE_TIPO_SERVICIO,
		A.id AS ID_SERVICIO,
		CONCAT(AF.first_name, ' ', AF.last_name) AS NOMBRE,
		AF.doc_type AS ID_TIPO_DOC,
		AF.doc_number AS IDENTIFICACION,
		AA.created_at AS FECHA_SERVICIO,
        UA.full_name AS FUNCIONARIO,
		'' AS DETALLE,
		IA.type AS TIPO,
		CONCAT(I.init_date, '-', I.last_date) AS PERIODO_CORRESPONDIENTE,
		I.date_invoice AS FECHA_FACTURA_REN,
		I.invoice_number AS NUMERO_FACTURA_REN,
		I.date_receive_invoice AS FECHA_RADICADO_REN,
		I.date_invoice_orienta AS FECHA_FACTURA_ORIENTA,
		I.invoice_number_orienta AS NUMERO_FACTURA_ORIENTA,
		'' AS FECHA_RADICADO_ORIENTA,
		'' AS PROF_ADSCRITO,
		'' AS FECHA_PROF_ADSCRITO
		        FROM activities AS A
        INNER JOIN activity_actions AS AA
        ON AA.activity_id = A.id AND AA.id = (
            SELECT id
            FROM activity_actions
            WHERE activity_id = A.id AND action_id IN (27,29,44,46,76)
          AND created_at > '2017-08-01'
            AND deleted_at IS NULL
            ORDER BY created_at ASC
            LIMIT 1
        )
        INNER JOIN activity_actions AS AA_DOCS
        ON AA_DOCS.activity_id = A.id AND AA_DOCS.id = (
            SELECT id
            FROM activity_actions
            WHERE activity_id = A.id AND action_id IN (31)
            AND deleted_at IS NULL
            ORDER BY created_at ASC
            LIMIT 1
        )
        INNER JOIN activity_actions AS AA_DOCR1
        ON AA_DOCR1.activity_id = A.id AND AA_DOCR1.id = (
            SELECT id
            FROM activity_actions
            WHERE activity_id = A.id AND action_id IN (38,39,156)
            AND deleted_at IS NULL
            ORDER BY created_at ASC
            LIMIT 1
        )
        INNER JOIN services AS SR
		ON A.service_id = SR.id
		INNER JOIN affiliates AS AF
		ON A.affiliate_id = AF.id
		INNER JOIN users AS UA
		ON AA.author_id = UA.id
		INNER JOIN invoice_activities AS IA
        ON (IA.activity_id = A.id AND IA.type = 'facturado')
        LEFT JOIN invoices I ON IA.invoice_id = I.id
        WHERE A.client_id = ? AND IA.invoice_id = ?
		AND IA.service = 'CONSOLIDACION DOCS'
    ";

    const MOBILITY_SQL_NEW = "
    SELECT 
		SR.name AS NOMBRE_TIPO_SERVICIO,
		A.id AS ID_SERVICIO,
		CONCAT(AF.first_name, ' ', AF.last_name) AS NOMBRE,
		AF.doc_type AS ID_TIPO_DOC,
		AF.doc_number AS IDENTIFICACION,
		AA.created_at AS FECHA_SERVICIO,
		UA.full_name AS FUNCIONARIO,
		'' AS DETALLE,
		IA.type AS TIPO,
		CONCAT(I.init_date, '-', I.last_date) AS PERIODO_CORRESPONDIENTE,
		I.date_invoice AS FECHA_FACTURA_REN,
		I.invoice_number AS NUMERO_FACTURA_REN,
		I.date_receive_invoice AS FECHA_RADICADO_REN,
		I.date_invoice_orienta AS FECHA_FACTURA_ORIENTA,
		I.invoice_number_orienta AS NUMERO_FACTURA_ORIENTA,
		'' AS FECHA_RADICADO_ORIENTA,
		'' AS PROF_ADSCRITO,
		'' AS FECHA_PROF_ADSCRITO
		FROM activities AS A
        INNER JOIN activity_actions AS AA
        ON AA.activity_id = A.id AND AA.id = (
            SELECT id
            FROM activity_actions
            WHERE activity_id = A.id AND action_id IN (155)
            AND deleted_at IS NULL
            ORDER BY FIELD(action_id, 155), created_at ASC
            LIMIT 1
        )
        INNER JOIN services AS SR
		ON A.service_id = SR.id
		INNER JOIN affiliates AS AF
		ON A.affiliate_id = AF.id
		INNER JOIN users AS UA
		ON AA.author_id = UA.id
		INNER JOIN invoice_activities AS IA
        ON (IA.activity_id = A.id AND IA.type = 'facturado')
        LEFT JOIN invoices I ON IA.invoice_id = I.id
        WHERE A.client_id = ? AND IA.invoice_id = ? 
		AND IA.service = SR.name
    ";

    const VALORACION_BOG_SQL_NEW = "
    SELECT 
		'VALORACION BOGOTA' AS NOMBRE_TIPO_SERVICIO,
		A.id AS ID_SERVICIO,
		CONCAT(AF.first_name, ' ', AF.last_name) AS NOMBRE,
		AF.doc_type AS ID_TIPO_DOC,
		AF.doc_number AS IDENTIFICACION,
		AA.created_at AS FECHA_SERVICIO,
		UA.full_name AS FUNCIONARIO,
		'' AS DETALLE,
		IA.type AS TIPO,
		CONCAT(I.init_date, '-', I.last_date) AS PERIODO_CORRESPONDIENTE,
		I.date_invoice AS FECHA_FACTURA_REN,
		I.invoice_number AS NUMERO_FACTURA_REN,
		I.date_receive_invoice AS FECHA_RADICADO_REN,
		I.date_invoice_orienta AS FECHA_FACTURA_ORIENTA,
		I.invoice_number_orienta AS NUMERO_FACTURA_ORIENTA,
		'' AS FECHA_RADICADO_ORIENTA,
		'' AS PROF_ADSCRITO,
		'' AS FECHA_PROF_ADSCRITO
		FROM activities AS A
        INNER JOIN activity_actions AS AA
        ON AA.activity_id = A.id AND AA.id = (
            SELECT id
            FROM activity_actions
            WHERE activity_id = A.id AND action_id IN (146)
            AND created_at > '2017-08-01'
            ORDER BY created_at ASC
            LIMIT 1
        )
        INNER JOIN services AS SR
		ON A.service_id = SR.id
		INNER JOIN affiliates AS AF
		ON A.affiliate_id = AF.id
		INNER JOIN users AS UA
		ON AA.author_id = UA.id
		INNER JOIN invoice_activities AS IA
        ON (IA.activity_id = A.id AND IA.type = 'facturado')
        LEFT JOIN invoices I ON IA.invoice_id = I.id
        WHERE A.client_id = ? AND IA.invoice_id = ? AND AF.department = 11 
		AND IA.service = 'VALORACION'
    ";

    const VALORATION_NAL_SQL_NEW = "
    SELECT 
		'VALORACION NACIONAL' AS NOMBRE_TIPO_SERVICIO,
		A.id AS ID_SERVICIO,
		CONCAT(AF.first_name, ' ', AF.last_name) AS NOMBRE,
		AF.doc_type AS ID_TIPO_DOC,
		AF.doc_number AS IDENTIFICACION,
		AA.created_at AS FECHA_SERVICIO,
		UA.full_name AS FUNCIONARIO,
		'' AS DETALLE,
		IA.type AS TIPO,
		CONCAT(I.init_date, '-', I.last_date) AS PERIODO_CORRESPONDIENTE,
		I.date_invoice AS FECHA_FACTURA_REN,
		I.invoice_number AS NUMERO_FACTURA_REN,
		I.date_receive_invoice AS FECHA_RADICADO_REN,
		I.date_invoice_orienta AS FECHA_FACTURA_ORIENTA,
		I.invoice_number_orienta AS NUMERO_FACTURA_ORIENTA,
		'' AS FECHA_RADICADO_ORIENTA,
		'' AS PROF_ADSCRITO,
		'' AS FECHA_PROF_ADSCRITO
		FROM activities AS A
        INNER JOIN activity_actions AS AA
        ON AA.activity_id = A.id AND AA.id = (
            SELECT id
            FROM activity_actions
            WHERE activity_id = A.id AND action_id IN (146)
            AND created_at > '2017-08-01'
            ORDER BY created_at ASC
            LIMIT 1
        )
        INNER JOIN services AS SR
		ON A.service_id = SR.id
		INNER JOIN affiliates AS AF
		ON A.affiliate_id = AF.id
		INNER JOIN users AS UA
		ON AA.author_id = UA.id
		INNER JOIN invoice_activities AS IA
        ON (IA.activity_id = A.id AND IA.type = 'facturado')
        LEFT JOIN invoices I ON IA.invoice_id = I.id
        WHERE A.client_id = ? AND IA.invoice_id = ? AND AF.department <> 11 
		AND IA.service = 'VALORACION'
    ";

    const VALORATION_SQL_NEW = "
    SELECT 
		'CONSULTA MÉDICO GENERAL' AS NOMBRE_TIPO_SERVICIO,
		A.id AS ID_SERVICIO,
		CONCAT(AF.first_name, ' ', AF.last_name) AS NOMBRE,
		AF.doc_type AS ID_TIPO_DOC,
		AF.doc_number AS IDENTIFICACION,
		AA.created_at AS FECHA_SERVICIO,
		UA.full_name AS FUNCIONARIO,
		'' AS DETALLE,
		IA.type AS TIPO,
		CONCAT(I.init_date, '-', I.last_date) AS PERIODO_CORRESPONDIENTE,
		I.date_invoice AS FECHA_FACTURA_REN,
		I.invoice_number AS NUMERO_FACTURA_REN,
		I.date_receive_invoice AS FECHA_RADICADO_REN,
		I.date_invoice_orienta AS FECHA_FACTURA_ORIENTA,
		I.invoice_number_orienta AS NUMERO_FACTURA_ORIENTA,
		'' AS FECHA_RADICADO_ORIENTA,
		'' AS PROF_ADSCRITO,
		'' AS FECHA_PROF_ADSCRITO
		FROM activities AS A
        INNER JOIN activity_actions AS AA
        ON AA.activity_id = A.id AND AA.id = (
            SELECT id
            FROM activity_actions
            WHERE activity_id = A.id AND action_id IN (146)
            AND created_at > '2017-08-01'
            ORDER BY created_at ASC
            LIMIT 1
        )
        INNER JOIN services AS SR
		ON A.service_id = SR.id
		INNER JOIN affiliates AS AF
		ON A.affiliate_id = AF.id
		INNER JOIN users AS UA
		ON AA.author_id = UA.id
		INNER JOIN invoice_activities AS IA
        ON (IA.activity_id = A.id AND IA.type = 'facturado')
        LEFT JOIN invoice_activity_services AS IAS
        ON (IA.invoice_id <> IAS.invoice_id AND IA.activity_id = IAS.activity_id AND IAS.service = 'CONSULTA MÉDICO GENERAL')
        LEFT JOIN invoices I ON IA.invoice_id = I.id
        WHERE A.client_id = ? AND IA.invoice_id = ? 
        AND IA.service = 'VALORACION'
    ";

    const TRACKING_SQL_NEW = "
    SELECT 
		'SEGUIMIENTO EN JUNTAS' AS NOMBRE_TIPO_SERVICIO,
		A.id AS ID_SERVICIO,
		CONCAT(AF.first_name, ' ', AF.last_name) AS NOMBRE,
		AF.doc_type AS ID_TIPO_DOC,
		AF.doc_number AS IDENTIFICACION,
		AA.created_at AS FECHA_SERVICIO,
		UA.full_name AS FUNCIONARIO,
		'' AS DETALLE,
		IA.type AS TIPO,
		CONCAT(I.init_date, '-', I.last_date) AS PERIODO_CORRESPONDIENTE,
		I.date_invoice AS FECHA_FACTURA_REN,
		I.invoice_number AS NUMERO_FACTURA_REN,
		I.date_receive_invoice AS FECHA_RADICADO_REN,
		I.date_invoice_orienta AS FECHA_FACTURA_ORIENTA,
		I.invoice_number_orienta AS NUMERO_FACTURA_ORIENTA,
		'' AS FECHA_RADICADO_ORIENTA,
		'' AS PROF_ADSCRITO,
		'' AS FECHA_PROF_ADSCRITO
		FROM activities AS A
        INNER JOIN activity_actions AS AA
        ON AA.activity_id = A.id AND AA.id = (
            SELECT id
            FROM activity_actions
            WHERE activity_id = A.id AND action_id IN (10, 18, 150, 157, 159, 158, 56)
            AND created_at > '2017-08-01'
            AND deleted_at IS NULL
            ORDER BY created_at ASC
            LIMIT 1
        )
        INNER JOIN services AS SR
		ON A.service_id = SR.id
		INNER JOIN affiliates AS AF
		ON A.affiliate_id = AF.id
		INNER JOIN users AS UA
		ON AA.author_id = UA.id
		INNER JOIN invoice_activities AS IA
        ON (IA.activity_id = A.id AND IA.type = 'facturado')
        LEFT JOIN invoices I ON IA.invoice_id = I.id
        WHERE A.client_id = ? AND IA.invoice_id = ? AND A.service_id IN (1, 2, 9, 10) 
        AND IA.service = 'SEGUIMIENTO EN JUNTAS'
    ";

    const CONTROVERSY_SQL_NEW = "
    SELECT 
		'CONTROVERSIAS' AS NOMBRE_TIPO_SERVICIO,
		A.id AS ID_SERVICIO,
		CONCAT(AF.first_name, ' ', AF.last_name) AS NOMBRE,
		AF.doc_type AS ID_TIPO_DOC,
		AF.doc_number AS IDENTIFICACION,
		AA.created_at AS FECHA_SERVICIO,
		UA.full_name AS FUNCIONARIO,
		'' AS DETALLE,
		IA.type AS TIPO,
		CONCAT(I.init_date, '-', I.last_date) AS PERIODO_CORRESPONDIENTE,
		I.date_invoice AS FECHA_FACTURA_REN,
		I.invoice_number AS NUMERO_FACTURA_REN,
		I.date_receive_invoice AS FECHA_RADICADO_REN,
		I.date_invoice_orienta AS FECHA_FACTURA_ORIENTA,
		I.invoice_number_orienta AS NUMERO_FACTURA_ORIENTA,
		'' AS FECHA_RADICADO_ORIENTA,
		'' AS PROF_ADSCRITO,
		'' AS FECHA_PROF_ADSCRITO
		FROM activities AS A
        INNER JOIN activity_actions AS AA
        ON AA.activity_id = A.id AND AA.id = (
            SELECT id
            FROM activity_actions
            WHERE activity_id = A.id AND action_id IN (63, 64, 58, 168, 169)
            AND created_at > '2017-08-01'
            AND deleted_at IS NULL
            ORDER BY created_at ASC
            LIMIT 1
        )
        INNER JOIN services AS SR
		ON A.service_id = SR.id
		INNER JOIN affiliates AS AF
		ON A.affiliate_id = AF.id
		INNER JOIN users AS UA
		ON AA.author_id = UA.id
		INNER JOIN invoice_activities AS IA
        ON (IA.activity_id = A.id AND IA.type = 'facturado')
        LEFT JOIN invoices I ON IA.invoice_id = I.id
        WHERE A.client_id = ? AND IA.invoice_id = ? AND A.service_id IN (10) 
        AND IA.service = 'CONTROVERSIAS'
    ";

    const ANALISIS_SQL_NEW = "
    SELECT 
		'EVALUACIÓN, ANALISIS y ACCION' AS NOMBRE_TIPO_SERVICIO,
		A.id AS ID_SERVICIO,
		CONCAT(AF.first_name, ' ', AF.last_name) AS NOMBRE,
		AF.doc_type AS ID_TIPO_DOC,
		AF.doc_number AS IDENTIFICACION,
		AA.created_at AS FECHA_SERVICIO,
		UA.full_name AS FUNCIONARIO,
		'' AS DETALLE,
		IA.type AS TIPO,
		CONCAT(I.init_date, '-', I.last_date) AS PERIODO_CORRESPONDIENTE,
		I.date_invoice AS FECHA_FACTURA_REN,
		I.invoice_number AS NUMERO_FACTURA_REN,
		I.date_receive_invoice AS FECHA_RADICADO_REN,
		I.date_invoice_orienta AS FECHA_FACTURA_ORIENTA,
		I.invoice_number_orienta AS NUMERO_FACTURA_ORIENTA,
		'' AS FECHA_RADICADO_ORIENTA,
		'' AS PROF_ADSCRITO,
		'' AS FECHA_PROF_ADSCRITO
		FROM activities AS A
        INNER JOIN activity_actions AS AA
        ON AA.activity_id = A.id AND AA.id = (
            SELECT id
            FROM activity_actions
            WHERE activity_id = A.id AND action_id IN (8, 60, 65, 59)
            AND created_at > '2017-08-01'
            AND deleted_at IS NULL
            ORDER BY created_at ASC
            LIMIT 1
        )
        INNER JOIN services AS SR
		ON A.service_id = SR.id
		INNER JOIN affiliates AS AF
		ON A.affiliate_id = AF.id
		INNER JOIN users AS UA
		ON AA.author_id = UA.id
		INNER JOIN invoice_activities AS IA
        ON (IA.activity_id = A.id AND IA.type = 'facturado')
        LEFT JOIN invoices I ON IA.invoice_id = I.id
        WHERE A.client_id = ? AND IA.invoice_id = ? AND A.service_id IN (1, 2, 9, 10) 
        AND IA.service = 'EVALUACIÓN, ANALISIS y ACCION'
    ";

    const CARGUE_SQL_NEW = "
    SELECT 
		'CARGUE DE DOCUMENTOS' AS NOMBRE_TIPO_SERVICIO,
		A.id AS ID_SERVICIO,
		CONCAT(AF.first_name, ' ', AF.last_name) AS NOMBRE,
		AF.doc_type AS ID_TIPO_DOC,
		AF.doc_number AS IDENTIFICACION,
		AA.created_at AS FECHA_SERVICIO,
		UA.full_name AS FUNCIONARIO,
		'' AS DETALLE,
		IA.type AS TIPO,
		CONCAT(I.init_date, '-', I.last_date) AS PERIODO_CORRESPONDIENTE,
		I.date_invoice AS FECHA_FACTURA_REN,
		I.invoice_number AS NUMERO_FACTURA_REN,
		I.date_receive_invoice AS FECHA_RADICADO_REN,
		I.date_invoice_orienta AS FECHA_FACTURA_ORIENTA,
		I.invoice_number_orienta AS NUMERO_FACTURA_ORIENTA,
		'' AS FECHA_RADICADO_ORIENTA,
		'' AS PROF_ADSCRITO,
		'' AS FECHA_PROF_ADSCRITO
		FROM activities AS A
        INNER JOIN activity_actions AS AA
        ON AA.activity_id = A.id AND AA.id = (
            SELECT id
            FROM activity_actions
            WHERE activity_id = A.id AND action_id IN (10, 15, 33, 34, 140, 141, 150, 157)
            AND created_at > '2017-08-01'
            AND deleted_at IS NULL
            ORDER BY created_at ASC
            LIMIT 1
        )
        INNER JOIN services AS SR
		ON A.service_id = SR.id
		INNER JOIN affiliates AS AF
		ON A.affiliate_id = AF.id
		INNER JOIN users AS UA
		ON AA.author_id = UA.id
		INNER JOIN invoice_activities AS IA
        ON (IA.activity_id = A.id AND IA.type = 'facturado')
        LEFT JOIN invoices I ON IA.invoice_id = I.id
        WHERE A.client_id = ? AND IA.invoice_id = ? 
        AND IA.service = 'CARGUE DE DOCUMENTOS'
    ";

    const FURAT_SQL_NEW = "
    SELECT 
		'FURAT' AS NOMBRE_TIPO_SERVICIO,
		A.id AS ID_SERVICIO,
		CONCAT(AF.first_name, ' ', AF.last_name) AS NOMBRE,
		AF.doc_type AS ID_TIPO_DOC,
		AF.doc_number AS IDENTIFICACION,
		AA.created_at AS FECHA_SERVICIO,
		UA.full_name AS FUNCIONARIO,
		'' AS DETALLE,
		IA.type AS TIPO,
		CONCAT(I.init_date, '-', I.last_date) AS PERIODO_CORRESPONDIENTE,
		I.date_invoice AS FECHA_FACTURA_REN,
		I.invoice_number AS NUMERO_FACTURA_REN,
		I.date_receive_invoice AS FECHA_RADICADO_REN,
		I.date_invoice_orienta AS FECHA_FACTURA_ORIENTA,
		I.invoice_number_orienta AS NUMERO_FACTURA_ORIENTA,
		'' AS FECHA_RADICADO_ORIENTA,
		'' AS PROF_ADSCRITO,
		'' AS FECHA_PROF_ADSCRITO
		FROM activities AS A
        INNER JOIN activity_actions AS AA
        ON AA.activity_id = A.id AND AA.id = (
            SELECT id
            FROM activity_actions
            WHERE activity_id = A.id AND action_id IN (149)
            AND created_at > '2017-08-01'
            AND deleted_at IS NULL
            ORDER BY created_at ASC
            LIMIT 1
        )
        INNER JOIN services AS SR
		ON A.service_id = SR.id
		INNER JOIN affiliates AS AF
		ON A.affiliate_id = AF.id
		INNER JOIN users AS UA
		ON AA.author_id = UA.id
		INNER JOIN invoice_activities AS IA
        ON (IA.activity_id = A.id AND IA.type = 'facturado')
        LEFT JOIN invoices I ON IA.invoice_id = I.id
        WHERE A.client_id = ? AND IA.invoice_id = ? 
        AND IA.service = 'FURAT'
    ";

    /******************************************/
    /**************  TENTATIVOS  **************/
    /******************************************/


    const AT_SQL_NEW_TENT = "
		SELECT
		SR.name AS NOMBRE_TIPO_SERVICIO,
		A.id AS ID_SERVICIO,
		CONCAT(AF.first_name, ' ', AF.last_name) AS NOMBRE,
		AF.doc_type AS ID_TIPO_DOC,
		AF.doc_number AS IDENTIFICACION,
		AA.created_at AS FECHA_SERVICIO,
		U.full_name AS FUNCIONARIO
		FROM activities AS A
		INNER JOIN activity_actions AS AA
		ON AA.id = (
			SELECT id
            FROM activity_actions
            WHERE activity_id = A.id AND action_id IN (8,15,148)
            ORDER BY created_at ASC
            LIMIT 1
        )
        LEFT JOIN activity_actions AS AAD
        ON AAD.activity_id = A.id AND AAD.id = (
            SELECT id
            FROM activity_actions
            WHERE activity_id = A.id AND action_id IN (44, 55, 54)
            AND created_at > '2017-08-01'
            AND deleted_at IS NULL
            AND old_state_id IN (18, 22)
            ORDER BY created_at ASC
            LIMIT 1
        )
		INNER JOIN affiliates AS AF
		ON A.affiliate_id = AF.id
		INNER JOIN users AS U
		ON A.user_id = U.id
		INNER JOIN dicta AS D
		ON D.activity_id = A.id
		INNER JOIN states AS S
		ON A.state_id = S.id
		INNER JOIN services AS SR
		ON A.service_id = SR.id
		LEFT JOIN employments AS E
		ON A.employment_id = E.id
		LEFT JOIN employers AS EM
		ON E.employer_id = EM.id
        LEFT JOIN invoices AS I ON I.id = ?
		WHERE A.client_id = ? AND A.service_id IN (1)
		AND AA.created_at >= I.init_date AND AA.created_at <= (I.last_date + INTERVAL 1 DAY)
	";

    const EL_SQL_NEW_TENT = "
		SELECT
		SR.name AS NOMBRE_TIPO_SERVICIO,
		A.id AS ID_SERVICIO,
		CONCAT(AF.first_name, ' ', AF.last_name) AS NOMBRE,
		AF.doc_type AS ID_TIPO_DOC,
		AF.doc_number AS IDENTIFICACION,
		AA.created_at AS FECHA_SERVICIO,
		U.full_name AS FUNCIONARIO
		FROM activities AS A
		INNER JOIN activity_actions AS AA
		ON AA.id = (
			SELECT id
            FROM activity_actions
            WHERE activity_id = A.id AND action_id IN (8,15,148)
            ORDER BY created_at ASC
            LIMIT 1
        )
        LEFT JOIN activity_actions AS AAD
        ON AAD.activity_id = A.id AND AAD.id = (
            SELECT id
            FROM activity_actions
            WHERE activity_id = A.id AND action_id IN (44, 55, 54)
            AND created_at > '2017-08-01'
            AND deleted_at IS NULL
            AND old_state_id IN (18, 22)
            ORDER BY created_at ASC
            LIMIT 1
        )
		INNER JOIN affiliates AS AF
		ON A.affiliate_id = AF.id
		INNER JOIN users AS U
		ON A.user_id = U.id
		INNER JOIN dicta AS D
		ON D.activity_id = A.id
		INNER JOIN states AS S
		ON A.state_id = S.id
		INNER JOIN services AS SR
		ON A.service_id = SR.id
		LEFT JOIN employments AS E
		ON A.employment_id = E.id
		LEFT JOIN employers AS EM
		ON E.employer_id = EM.id
        LEFT JOIN invoices AS I ON I.id = ?
		WHERE A.client_id = ? AND A.service_id IN (2)
		AND AA.created_at >= I.init_date AND AA.created_at <= (I.last_date + INTERVAL 1 DAY)
	";

    const PCL_SQL_NEW_TENT = "
		SELECT
		SR.name AS NOMBRE_TIPO_SERVICIO,
		A.id AS ID_SERVICIO,
		CONCAT(AF.first_name, ' ', AF.last_name) AS NOMBRE,
		AF.doc_type AS ID_TIPO_DOC,
		AF.doc_number AS IDENTIFICACION,
		AA.created_at AS FECHA_SERVICIO,
		UA.full_name AS FUNCIONARIO
		FROM activities AS A
		INNER JOIN activity_actions AS AA
		ON AA.id = (
			SELECT id
            FROM activity_actions
            WHERE activity_id = A.id AND action_id IN (157)
            ORDER BY created_at ASC
            LIMIT 1
        )
        LEFT JOIN activity_actions AS AAD
        ON AAD.activity_id = A.id AND AAD.id = (
            SELECT id
            FROM activity_actions
            WHERE activity_id = A.id AND action_id IN (44, 55, 54)
            AND created_at > '2017-08-01'
            AND deleted_at IS NULL
            AND old_state_id IN (18, 22)
            ORDER BY created_at ASC
            LIMIT 1
        )
		INNER JOIN action_service_states ASS
		ON ASS.service_id = A.service_id AND ASS.action_id = AA.action_id
		INNER JOIN states AS AAS
		ON ASS.state_id = AAS.id
		INNER JOIN users AS UA
		ON AA.author_id = UA.id
		INNER JOIN affiliates AS AF
		ON A.affiliate_id = AF.id
		INNER JOIN users AS U
		ON A.user_id = U.id
		INNER JOIN pcls AS P
		ON P.activity_id = A.id
		INNER JOIN states AS S
		ON A.state_id = S.id
		INNER JOIN services AS SR
		ON A.service_id = SR.id
		LEFT JOIN employments AS E
		ON A.employment_id = E.id
		LEFT JOIN employers AS EM
		ON E.employer_id = EM.id
        LEFT JOIN invoices AS I ON I.id = ?
		WHERE A.client_id = ? AND A.service_id IN (8,9)
		AND AA.created_at >= I.init_date AND AA.created_at <= (I.last_date + INTERVAL 1 DAY)
	";

    const REHABILITATION_SQL_NEW_TENT = "
		SELECT
		SR.name AS NOMBRE_TIPO_SERVICIO,
		A.id AS ID_SERVICIO,
		CONCAT(AF.first_name, ' ', AF.last_name) AS NOMBRE,
		AF.doc_type AS ID_TIPO_DOC,
		AF.doc_number AS IDENTIFICACION,
		AA.created_at AS FECHA_SERVICIO,
		UA.full_name AS FUNCIONARIO
		FROM activities AS A
		INNER JOIN activity_actions AS AA
		ON AA.id = (
			SELECT id
            FROM activity_actions
            WHERE activity_id = A.id AND action_id IN (156)
            ORDER BY created_at ASC
            LIMIT 1
        )
		INNER JOIN action_service_states ASS
		ON ASS.service_id = A.service_id AND ASS.action_id = AA.action_id
		INNER JOIN states AS AAS
		ON ASS.state_id = AAS.id
		INNER JOIN users AS UA
		ON AA.author_id = UA.id
		INNER JOIN affiliates AS AF
		ON A.affiliate_id = AF.id
		INNER JOIN rehabilitations AS R
		ON R.activity_id = A.id
		INNER JOIN services AS SR
		ON A.service_id = SR.id
		LEFT JOIN employments AS E
		ON A.employment_id = E.id
		LEFT JOIN employers AS EM
		ON E.employer_id = EM.id
        LEFT JOIN invoices AS I ON I.id = ?
		WHERE A.client_id = ? AND A.service_id IN (12) 
		AND AA.created_at >= I.init_date AND AA.created_at <= (I.last_date + INTERVAL 1 DAY)
	";

    const RECOMMENDATION_SQL_NEW_TENT = "
		SELECT
		SR.name AS NOMBRE_TIPO_SERVICIO,
		A.id AS ID_SERVICIO,
		CONCAT(AF.first_name, ' ', AF.last_name) AS NOMBRE,
		AF.doc_type AS ID_TIPO_DOC,
		AF.doc_number AS IDENTIFICACION,
		AA.created_at AS FECHA_SERVICIO,
		UA.full_name AS FUNCIONARIO
		FROM activities AS A
		INNER JOIN activity_actions AS AA
		ON AA.id = (
			SELECT id
            FROM activity_actions
            WHERE activity_id = A.id AND action_id IN (150,152)
            AND deleted_at IS NULL
            ORDER BY FIELD(action_id, 150, 152), created_at ASC
            LIMIT 1
        )
		INNER JOIN action_service_states ASS
		ON ASS.service_id = A.service_id AND ASS.action_id = AA.action_id
		INNER JOIN states AS AAS
		ON ASS.state_id = AAS.id
		INNER JOIN users AS UA
		ON AA.author_id = UA.id
		INNER JOIN affiliates AS AF
		ON A.affiliate_id = AF.id
		INNER JOIN recommendations AS R
		ON R.activity_id = A.id
		INNER JOIN services AS SR
		ON A.service_id = SR.id
		LEFT JOIN employments AS E
		ON A.employment_id = E.id
		LEFT JOIN employers AS EM
		ON E.employer_id = EM.id
        LEFT JOIN invoices AS I ON I.id = ?
        WHERE A.client_id = ? AND A.service_id IN (13) 
		AND AA.created_at >= I.init_date AND AA.created_at <= (I.last_date + INTERVAL 1 DAY)
	";

    const DISABILITY_SQL_NEW_TENT = "
		SELECT
		SR.name AS NOMBRE_TIPO_SERVICIO,
		A.id AS ID_SERVICIO,
		CONCAT(AF.first_name, ' ', AF.last_name) AS NOMBRE,
		AF.doc_type AS ID_TIPO_DOC,
		AF.doc_number AS IDENTIFICACION,
		AA.created_at AS FECHA_SERVICIO,
		UA.full_name AS FUNCIONARIO
		FROM activities AS A
		INNER JOIN activity_actions AS AA
		ON AA.id = (
			SELECT id
            FROM activity_actions
            WHERE activity_id = A.id AND action_id IN (154)
            AND deleted_at IS NULL
            ORDER BY FIELD(action_id, 154), created_at ASC
            LIMIT 1
        )
		INNER JOIN action_service_states ASS
		ON ASS.service_id = A.service_id AND ASS.action_id = AA.action_id
		INNER JOIN states AS AAS
		ON ASS.state_id = AAS.id
		INNER JOIN users AS UA
		ON AA.author_id = UA.id
		INNER JOIN affiliates AS AF
		ON A.affiliate_id = AF.id
		INNER JOIN users AS U
		ON A.user_id = U.id
		INNER JOIN disabilities AS P
		ON P.activity_id = A.id
		INNER JOIN states AS S
		ON A.state_id = S.id
		INNER JOIN services AS SR
		ON A.service_id = SR.id
		LEFT JOIN employments AS E
		ON A.employment_id = E.id
		LEFT JOIN employers AS EM
		ON E.employer_id = EM.id
        LEFT JOIN invoices AS I ON I.id = ?
        WHERE A.client_id = ? AND A.service_id IN (14) 
		AND AA.created_at >= I.init_date AND AA.created_at <= (I.last_date + INTERVAL 1 DAY)
	";

    const AUDIT_SQL_NEW_TENT = "
		SELECT
		SR.name AS NOMBRE_TIPO_SERVICIO,
		A.id AS ID_SERVICIO,
		CONCAT(AF.first_name, ' ', AF.last_name) AS NOMBRE,
		AF.doc_type AS ID_TIPO_DOC,
		AF.doc_number AS IDENTIFICACION,
		AA.created_at AS FECHA_SERVICIO,
		UA.full_name AS FUNCIONARIO
		FROM activities AS A
		INNER JOIN activity_actions AS AA
		ON AA.id = (
			SELECT id
            FROM activity_actions
            WHERE activity_id = A.id AND action_id IN (83,84,86,88)
            ORDER BY created_at ASC
            LIMIT 1
        )
		INNER JOIN action_service_states ASS
		ON ASS.service_id = A.service_id AND ASS.action_id = AA.action_id
		INNER JOIN states AS AAS
		ON ASS.state_id = AAS.id
		INNER JOIN users AS UA
		ON AA.author_id = UA.id
		INNER JOIN affiliates AS AF
		ON A.affiliate_id = AF.id
		INNER JOIN audits AS AUD
		ON AUD.activity_id = A.id
		INNER JOIN services AS SR
		ON A.service_id = SR.id
		LEFT JOIN employments AS E
		ON A.employment_id = E.id
		LEFT JOIN employers AS EM
		ON E.employer_id = EM.id
        LEFT JOIN invoices AS I ON I.id = ?
        WHERE A.client_id = ? AND A.service_id IN (15) 
		AND AA.created_at >= I.init_date AND AA.created_at <= (I.last_date + INTERVAL 1 DAY)
	";

    const CONSOLIDACION_SQL_NEW_TENT = "
        SELECT 
		'CONSOLIDACION DOCS' AS NOMBRE_TIPO_SERVICIO,
		A.id AS ID_SERVICIO,
		CONCAT(AF.first_name, ' ', AF.last_name) AS NOMBRE,
		AF.doc_type AS ID_TIPO_DOC,
		AF.doc_number AS IDENTIFICACION,
		AA.created_at AS FECHA_SERVICIO,
        UA.full_name AS FUNCIONARIO
		        FROM activities AS A
        INNER JOIN activity_actions AS AA
        ON AA.activity_id = A.id AND AA.id = (
            SELECT id
            FROM activity_actions
            WHERE activity_id = A.id AND action_id IN (27,29,44,46,76)
          AND created_at > '2017-08-01'
            AND deleted_at IS NULL
            ORDER BY created_at ASC
            LIMIT 1
        )
        INNER JOIN activity_actions AS AA_DOCS
        ON AA_DOCS.activity_id = A.id AND AA_DOCS.id = (
            SELECT id
            FROM activity_actions
            WHERE activity_id = A.id AND action_id IN (31)
            AND deleted_at IS NULL
            ORDER BY created_at ASC
            LIMIT 1
        )
        INNER JOIN activity_actions AS AA_DOCR1
        ON AA_DOCR1.activity_id = A.id AND AA_DOCR1.id = (
            SELECT id
            FROM activity_actions
            WHERE activity_id = A.id AND action_id IN (38,39,156)
            AND deleted_at IS NULL
            ORDER BY created_at ASC
            LIMIT 1
        )
        INNER JOIN services AS SR
		ON A.service_id = SR.id
		INNER JOIN affiliates AS AF
		ON A.affiliate_id = AF.id
		INNER JOIN users AS UA
		ON AA.author_id = UA.id
        LEFT JOIN invoices AS I ON I.id = ?
        WHERE A.client_id = ? 
        AND AA.created_at >= I.init_date AND AA.created_at <= (I.last_date + INTERVAL 1 DAY)
    ";

    const MOBILITY_SQL_NEW_TENT = "
    SELECT 
		SR.name AS NOMBRE_TIPO_SERVICIO,
		A.id AS ID_SERVICIO,
		CONCAT(AF.first_name, ' ', AF.last_name) AS NOMBRE,
		AF.doc_type AS ID_TIPO_DOC,
		AF.doc_number AS IDENTIFICACION,
		AA.created_at AS FECHA_SERVICIO,
		UA.full_name AS FUNCIONARIO
		FROM activities AS A
        INNER JOIN activity_actions AS AA
        ON AA.activity_id = A.id AND AA.id = (
            SELECT id
            FROM activity_actions
            WHERE activity_id = A.id AND action_id IN (155)
            AND deleted_at IS NULL
            ORDER BY FIELD(action_id, 155), created_at ASC
            LIMIT 1
        )
        INNER JOIN services AS SR
		ON A.service_id = SR.id
		INNER JOIN affiliates AS AF
		ON A.affiliate_id = AF.id
		INNER JOIN users AS UA
		ON AA.author_id = UA.id
        LEFT JOIN invoices AS I ON I.id = ?
        WHERE A.client_id = ? 
        AND AA.created_at >= I.init_date AND AA.created_at <= (I.last_date + INTERVAL 1 DAY)
    ";

    const VALORACION_BOG_SQL_NEW_TENT = "
    SELECT 
		'VALORACION BOGOTA' AS NOMBRE_TIPO_SERVICIO,
		A.id AS ID_SERVICIO,
		CONCAT(AF.first_name, ' ', AF.last_name) AS NOMBRE,
		AF.doc_type AS ID_TIPO_DOC,
		AF.doc_number AS IDENTIFICACION,
		AA.created_at AS FECHA_SERVICIO,
		UA.full_name AS FUNCIONARIO
		FROM activities AS A
        INNER JOIN activity_actions AS AA
        ON AA.activity_id = A.id AND AA.id = (
            SELECT id
            FROM activity_actions
            WHERE activity_id = A.id AND action_id IN (146)
            AND created_at > '2017-08-01'
            ORDER BY created_at ASC
            LIMIT 1
        )
        INNER JOIN services AS SR
		ON A.service_id = SR.id
		INNER JOIN affiliates AS AF
		ON A.affiliate_id = AF.id
		INNER JOIN users AS UA
		ON AA.author_id = UA.id
        LEFT JOIN invoices AS I ON I.id = ?
        WHERE A.client_id = ? AND AF.department = 11 
        AND AA.created_at >= I.init_date AND AA.created_at <= (I.last_date + INTERVAL 1 DAY)
    ";

    const VALORATION_NAL_SQL_NEW_TENT = "
    SELECT 
		'VALORACION NACIONAL' AS NOMBRE_TIPO_SERVICIO,
		A.id AS ID_SERVICIO,
		CONCAT(AF.first_name, ' ', AF.last_name) AS NOMBRE,
		AF.doc_type AS ID_TIPO_DOC,
		AF.doc_number AS IDENTIFICACION,
		AA.created_at AS FECHA_SERVICIO,
		UA.full_name AS FUNCIONARIO
		FROM activities AS A
        INNER JOIN activity_actions AS AA
        ON AA.activity_id = A.id AND AA.id = (
            SELECT id
            FROM activity_actions
            WHERE activity_id = A.id AND action_id IN (146)
            AND created_at > '2017-08-01'
            ORDER BY created_at ASC
            LIMIT 1
        )
        INNER JOIN services AS SR
		ON A.service_id = SR.id
		INNER JOIN affiliates AS AF
		ON A.affiliate_id = AF.id
		INNER JOIN users AS UA
		ON AA.author_id = UA.id
        LEFT JOIN invoices AS I ON I.id = ?
        WHERE A.client_id = ? AND AF.department <> 11
        AND AA.created_at >= I.init_date AND AA.created_at <= (I.last_date + INTERVAL 1 DAY)
    ";

    const VALORATION_SQL_NEW_TENT = "
    SELECT 
		'VALORACION' AS NOMBRE_TIPO_SERVICIO,
		A.id AS ID_SERVICIO,
		CONCAT(AF.first_name, ' ', AF.last_name) AS NOMBRE,
		AF.doc_type AS ID_TIPO_DOC,
		AF.doc_number AS IDENTIFICACION,
		AA.created_at AS FECHA_SERVICIO,
		UA.full_name AS FUNCIONARIO
		FROM activities AS A
        INNER JOIN activity_actions AS AA
        ON AA.activity_id = A.id AND AA.id = (
            SELECT id
            FROM activity_actions
            WHERE activity_id = A.id AND action_id IN (146)
            AND created_at > '2017-08-01'
            ORDER BY created_at ASC
            LIMIT 1
        )
        INNER JOIN services AS SR
		ON A.service_id = SR.id
		INNER JOIN affiliates AS AF
		ON A.affiliate_id = AF.id
		INNER JOIN users AS UA
		ON AA.author_id = UA.id
        LEFT JOIN invoices AS I ON I.id = ?
        WHERE A.client_id = ? 
        AND AA.created_at >= I.init_date AND AA.created_at <= (I.last_date + INTERVAL 1 DAY)
    ";

    const TRACKING_SQL_NEW_TENT = "
    SELECT 
		'SEGUIMIENTO EN JUNTAS' AS NOMBRE_TIPO_SERVICIO,
		A.id AS ID_SERVICIO,
		CONCAT(AF.first_name, ' ', AF.last_name) AS NOMBRE,
		AF.doc_type AS ID_TIPO_DOC,
		AF.doc_number AS IDENTIFICACION,
		AA.created_at AS FECHA_SERVICIO,
		UA.full_name AS FUNCIONARIO
		FROM activities AS A
        INNER JOIN activity_actions AS AA
        ON AA.activity_id = A.id AND AA.id = (
            SELECT id
            FROM activity_actions
            WHERE activity_id = A.id AND action_id IN (10, 18, 150, 157, 159, 158, 56)
            AND created_at > '2017-08-01'
            AND deleted_at IS NULL
            ORDER BY created_at ASC
            LIMIT 1
        )
        INNER JOIN services AS SR
		ON A.service_id = SR.id
		INNER JOIN affiliates AS AF
		ON A.affiliate_id = AF.id
		INNER JOIN users AS UA
		ON AA.author_id = UA.id
        LEFT JOIN invoices AS I ON I.id = ?
        WHERE A.client_id = ? AND A.service_id IN (1, 2, 9, 10) 
        AND AA.created_at >= I.init_date AND AA.created_at <= (I.last_date + INTERVAL 1 DAY)
    ";

    const CONTROVERSY_SQL_NEW_TENT = "
    SELECT 
		'CONTROVERSIAS' AS NOMBRE_TIPO_SERVICIO,
		A.id AS ID_SERVICIO,
		CONCAT(AF.first_name, ' ', AF.last_name) AS NOMBRE,
		AF.doc_type AS ID_TIPO_DOC,
		AF.doc_number AS IDENTIFICACION,
		AA.created_at AS FECHA_SERVICIO,
		UA.full_name AS FUNCIONARIO
		FROM activities AS A
        INNER JOIN activity_actions AS AA
        ON AA.activity_id = A.id AND AA.id = (
            SELECT id
            FROM activity_actions
            WHERE activity_id = A.id AND action_id IN (63, 64, 58, 168, 169)
            AND created_at > '2017-08-01'
            AND deleted_at IS NULL
            ORDER BY created_at ASC
            LIMIT 1
        )
        INNER JOIN services AS SR
		ON A.service_id = SR.id
		INNER JOIN affiliates AS AF
		ON A.affiliate_id = AF.id
		INNER JOIN users AS UA
		ON AA.author_id = UA.id
        LEFT JOIN invoices AS I ON I.id = ?
        WHERE A.client_id = ? AND A.service_id IN (10) 
        AND AA.created_at >= I.init_date AND AA.created_at <= (I.last_date + INTERVAL 1 DAY)
    ";

    const ANALISIS_SQL_NEW_TENT = "
    SELECT 
		'EVALUACIÓN, ANALISIS y ACCION' AS NOMBRE_TIPO_SERVICIO,
		A.id AS ID_SERVICIO,
		CONCAT(AF.first_name, ' ', AF.last_name) AS NOMBRE,
		AF.doc_type AS ID_TIPO_DOC,
		AF.doc_number AS IDENTIFICACION,
		AA.created_at AS FECHA_SERVICIO,
		UA.full_name AS FUNCIONARIO
		FROM activities AS A
        INNER JOIN activity_actions AS AA
        ON AA.activity_id = A.id AND AA.id = (
            SELECT id
            FROM activity_actions
            WHERE activity_id = A.id AND action_id IN (8, 60, 65, 59)
            AND created_at > '2017-08-01'
            AND deleted_at IS NULL
            ORDER BY created_at ASC
            LIMIT 1
        )
        INNER JOIN services AS SR
		ON A.service_id = SR.id
		INNER JOIN affiliates AS AF
		ON A.affiliate_id = AF.id
		INNER JOIN users AS UA
		ON AA.author_id = UA.id
        LEFT JOIN invoices AS I ON I.id = ?
        WHERE A.client_id = ? AND A.service_id IN (1, 2, 9, 10) 
        AND AA.created_at >= I.init_date AND AA.created_at <= (I.last_date + INTERVAL 1 DAY)
    ";

    const CARGUE_SQL_NEW_TENT = "
    SELECT 
		'CARGUE DE DOCUMENTOS' AS NOMBRE_TIPO_SERVICIO,
		A.id AS ID_SERVICIO,
		CONCAT(AF.first_name, ' ', AF.last_name) AS NOMBRE,
		AF.doc_type AS ID_TIPO_DOC,
		AF.doc_number AS IDENTIFICACION,
		AA.created_at AS FECHA_SERVICIO,
		UA.full_name AS FUNCIONARIO
		FROM activities AS A
        INNER JOIN activity_actions AS AA
        ON AA.activity_id = A.id AND AA.id = (
            SELECT id
            FROM activity_actions
            WHERE activity_id = A.id AND action_id IN (10, 15, 33, 34, 140, 141, 150, 157)
            AND created_at > '2017-08-01'
            AND deleted_at IS NULL
            ORDER BY created_at ASC
            LIMIT 1
        )
        INNER JOIN services AS SR
		ON A.service_id = SR.id
		INNER JOIN affiliates AS AF
		ON A.affiliate_id = AF.id
		INNER JOIN users AS UA
		ON AA.author_id = UA.id
        LEFT JOIN invoices AS I ON I.id = ?
        WHERE A.client_id = ? 
        AND AA.created_at >= I.init_date AND AA.created_at <= (I.last_date + INTERVAL 1 DAY)
    ";

    const FURAT_SQL_NEW_TENT = "
    SELECT 
		'FURAT' AS NOMBRE_TIPO_SERVICIO,
		A.id AS ID_SERVICIO,
		CONCAT(AF.first_name, ' ', AF.last_name) AS NOMBRE,
		AF.doc_type AS ID_TIPO_DOC,
		AF.doc_number AS IDENTIFICACION,
		AA.created_at AS FECHA_SERVICIO,
		UA.full_name AS FUNCIONARIO
		FROM activities AS A
        INNER JOIN activity_actions AS AA
        ON AA.activity_id = A.id AND AA.id = (
            SELECT id
            FROM activity_actions
            WHERE activity_id = A.id AND action_id IN (149)
            AND created_at > '2017-08-01'
            AND deleted_at IS NULL
            ORDER BY created_at ASC
            LIMIT 1
        )
        INNER JOIN services AS SR
		ON A.service_id = SR.id
		INNER JOIN affiliates AS AF
		ON A.affiliate_id = AF.id
		INNER JOIN users AS UA
		ON AA.author_id = UA.id
        LEFT JOIN invoices AS I ON I.id = ?
        WHERE A.client_id = ? 
        AND AA.created_at >= I.init_date AND AA.created_at <= (I.last_date + INTERVAL 1 DAY)
    ";


    /******************************************/
    /**************  ENGLOSADOS  **************/
    /******************************************/
    const G_AT_SQL_NEW = "
		SELECT
		SR.name AS NOMBRE_TIPO_SERVICIO,
		A.id AS ID_SERVICIO,
		CONCAT(AF.first_name, ' ', AF.last_name) AS NOMBRE,
		AF.doc_type AS ID_TIPO_DOC,
		AF.doc_number AS IDENTIFICACION,
		AA.created_at AS FECHA_SERVICIO,
		U.full_name AS FUNCIONARIO,
		'' AS DETALLE,
		IA.type AS TIPO
		FROM activities AS A
		INNER JOIN activity_actions AS AA
		ON AA.id = (
			SELECT id
            FROM activity_actions
            WHERE activity_id = A.id AND action_id IN (8,15,148)
            ORDER BY created_at ASC
            LIMIT 1
        )
		INNER JOIN affiliates AS AF
		ON A.affiliate_id = AF.id
		INNER JOIN users AS U
		ON A.user_id = U.id
		INNER JOIN dicta AS D
		ON D.activity_id = A.id
		INNER JOIN states AS S
		ON A.state_id = S.id
		INNER JOIN services AS SR
		ON A.service_id = SR.id
		LEFT JOIN employments AS E
		ON A.employment_id = E.id
		LEFT JOIN employers AS EM
		ON E.employer_id = EM.id
		INNER JOIN invoice_activities AS IA
        ON (IA.activity_id = A.id AND IA.type = 'englosado')
        LEFT JOIN invoice_activity_services AS IAS
        ON (IA.invoice_id <> IAS.invoice_id AND IA.activity_id = IAS.activity_id AND IAS.service = SR.name)
		WHERE A.client_id = ? AND IA.invoice_id = ? AND A.service_id IN (1) AND IAS.id IS NULL
	";
    const G_EL_SQL_NEW = "
		SELECT
		SR.name AS NOMBRE_TIPO_SERVICIO,
		A.id AS ID_SERVICIO,
		CONCAT(AF.first_name, ' ', AF.last_name) AS NOMBRE,
		AF.doc_type AS ID_TIPO_DOC,
		AF.doc_number AS IDENTIFICACION,
		AA.created_at AS FECHA_SERVICIO,
		U.full_name AS FUNCIONARIO,
		'' AS DETALLE,
		IA.type AS TIPO
		FROM activities AS A
		INNER JOIN activity_actions AS AA
		ON AA.id = (
			SELECT id
            FROM activity_actions
            WHERE activity_id = A.id AND action_id IN (8,15,148)
            ORDER BY created_at ASC
            LIMIT 1
        )
		INNER JOIN affiliates AS AF
		ON A.affiliate_id = AF.id
		INNER JOIN users AS U
		ON A.user_id = U.id
		INNER JOIN dicta AS D
		ON D.activity_id = A.id
		INNER JOIN states AS S
		ON A.state_id = S.id
		INNER JOIN services AS SR
		ON A.service_id = SR.id
		LEFT JOIN employments AS E
		ON A.employment_id = E.id
		LEFT JOIN employers AS EM
		ON E.employer_id = EM.id
		INNER JOIN invoice_activities AS IA
        ON (IA.activity_id = A.id AND IA.type = 'englosado')
        LEFT JOIN invoice_activity_services AS IAS
        ON (IA.invoice_id <> IAS.invoice_id AND IA.activity_id = IAS.activity_id AND IAS.service = SR.name)
		WHERE A.client_id = ? AND IA.invoice_id = ? AND A.service_id IN (2) AND IAS.id IS NULL
	";
    const G_PCL_SQL_NEW = "
		SELECT
		SR.name AS NOMBRE_TIPO_SERVICIO,
		A.id AS ID_SERVICIO,
		CONCAT(AF.first_name, ' ', AF.last_name) AS NOMBRE,
		AF.doc_type AS ID_TIPO_DOC,
		AF.doc_number AS IDENTIFICACION,
		AA.created_at AS FECHA_SERVICIO,
		UA.full_name AS FUNCIONARIO,
		'' AS DETALLE,
		IA.type AS TIPO
		FROM activities AS A
		INNER JOIN activity_actions AS AA
		ON AA.id = (
			SELECT id
            FROM activity_actions
            WHERE activity_id = A.id AND action_id IN (157)
            ORDER BY created_at ASC
            LIMIT 1
        )
		INNER JOIN action_service_states ASS
		ON ASS.service_id = A.service_id AND ASS.action_id = AA.action_id
		INNER JOIN states AS AAS
		ON ASS.state_id = AAS.id
		INNER JOIN users AS UA
		ON AA.author_id = UA.id
		INNER JOIN affiliates AS AF
		ON A.affiliate_id = AF.id
		INNER JOIN users AS U
		ON A.user_id = U.id
		INNER JOIN pcls AS P
		ON P.activity_id = A.id
		INNER JOIN states AS S
		ON A.state_id = S.id
		INNER JOIN services AS SR
		ON A.service_id = SR.id
		LEFT JOIN employments AS E
		ON A.employment_id = E.id
		LEFT JOIN employers AS EM
		ON E.employer_id = EM.id
		INNER JOIN invoice_activities AS IA
        ON (IA.activity_id = A.id AND IA.type = 'englosado')
        LEFT JOIN invoice_activity_services AS IAS
        ON (IA.invoice_id <> IAS.invoice_id AND IA.activity_id = IAS.activity_id AND IAS.service = SR.name)
		WHERE A.client_id = ? AND IA.invoice_id = ? AND A.service_id IN (8,9) AND IAS.id IS NULL
	";

    const G_REHABILITATION_SQL_NEW = "
		SELECT
		SR.name AS NOMBRE_TIPO_SERVICIO,
		A.id AS ID_SERVICIO,
		CONCAT(AF.first_name, ' ', AF.last_name) AS NOMBRE,
		AF.doc_type AS ID_TIPO_DOC,
		AF.doc_number AS IDENTIFICACION,
		AA.created_at AS FECHA_SERVICIO,
		UA.full_name AS FUNCIONARIO,
		'' AS DETALLE,
		IA.type AS TIPO
		FROM activities AS A
		INNER JOIN activity_actions AS AA
		ON AA.id = (
			SELECT id
            FROM activity_actions
            WHERE activity_id = A.id AND action_id IN (156)
            ORDER BY created_at ASC
            LIMIT 1
        )
		INNER JOIN action_service_states ASS
		ON ASS.service_id = A.service_id AND ASS.action_id = AA.action_id
		INNER JOIN states AS AAS
		ON ASS.state_id = AAS.id
		INNER JOIN users AS UA
		ON AA.author_id = UA.id
		INNER JOIN affiliates AS AF
		ON A.affiliate_id = AF.id
		INNER JOIN rehabilitations AS R
		ON R.activity_id = A.id
		INNER JOIN services AS SR
		ON A.service_id = SR.id
		LEFT JOIN employments AS E
		ON A.employment_id = E.id
		LEFT JOIN employers AS EM
		ON E.employer_id = EM.id
		INNER JOIN invoice_activities AS IA
        ON (IA.activity_id = A.id AND IA.type = 'englosado')
        LEFT JOIN invoice_activity_services AS IAS
        ON (IA.invoice_id <> IAS.invoice_id AND IA.activity_id = IAS.activity_id AND IAS.service = SR.name)
		WHERE A.client_id = ? AND IA.invoice_id = ? AND A.service_id IN (12) AND IAS.id IS NULL
	";

    const G_RECOMMENDATION_SQL_NEW = "
		SELECT
		SR.name AS NOMBRE_TIPO_SERVICIO,
		A.id AS ID_SERVICIO,
		CONCAT(AF.first_name, ' ', AF.last_name) AS NOMBRE,
		AF.doc_type AS ID_TIPO_DOC,
		AF.doc_number AS IDENTIFICACION,
		AA.created_at AS FECHA_SERVICIO,
		UA.full_name AS FUNCIONARIO,
		'' AS DETALLE,
		IA.type AS TIPO
		FROM activities AS A
		INNER JOIN activity_actions AS AA
		ON AA.id = (
			SELECT id
            FROM activity_actions
            WHERE activity_id = A.id AND action_id IN (150,152)
            AND deleted_at IS NULL
            ORDER BY FIELD(action_id, 150, 152), created_at ASC
            LIMIT 1
        )
		INNER JOIN action_service_states ASS
		ON ASS.service_id = A.service_id AND ASS.action_id = AA.action_id
		INNER JOIN states AS AAS
		ON ASS.state_id = AAS.id
		INNER JOIN users AS UA
		ON AA.author_id = UA.id
		INNER JOIN affiliates AS AF
		ON A.affiliate_id = AF.id
		INNER JOIN recommendations AS R
		ON R.activity_id = A.id
		INNER JOIN services AS SR
		ON A.service_id = SR.id
		LEFT JOIN employments AS E
		ON A.employment_id = E.id
		LEFT JOIN employers AS EM
		ON E.employer_id = EM.id
		INNER JOIN invoice_activities AS IA
        ON (IA.activity_id = A.id AND IA.type = 'englosado')
        LEFT JOIN invoice_activity_services AS IAS
        ON (IA.invoice_id <> IAS.invoice_id AND IA.activity_id = IAS.activity_id AND IAS.service = SR.name)
		WHERE A.client_id = ? AND IA.invoice_id = ? AND A.service_id IN (13) AND IAS.id IS NULL
	";

    const G_DISABILITY_SQL_NEW = "
		SELECT
		SR.name AS NOMBRE_TIPO_SERVICIO,
		A.id AS ID_SERVICIO,
		CONCAT(AF.first_name, ' ', AF.last_name) AS NOMBRE,
		AF.doc_type AS ID_TIPO_DOC,
		AF.doc_number AS IDENTIFICACION,
		AA.created_at AS FECHA_SERVICIO,
		UA.full_name AS FUNCIONARIO,
		'' AS DETALLE,
		IA.type AS TIPO
		FROM activities AS A
		INNER JOIN activity_actions AS AA
		ON AA.id = (
			SELECT id
            FROM activity_actions
            WHERE activity_id = A.id AND action_id IN (154)
            AND deleted_at IS NULL
            ORDER BY FIELD(action_id, 154), created_at ASC
            LIMIT 1
        )
		INNER JOIN action_service_states ASS
		ON ASS.service_id = A.service_id AND ASS.action_id = AA.action_id
		INNER JOIN states AS AAS
		ON ASS.state_id = AAS.id
		INNER JOIN users AS UA
		ON AA.author_id = UA.id
		INNER JOIN affiliates AS AF
		ON A.affiliate_id = AF.id
		INNER JOIN users AS U
		ON A.user_id = U.id
		INNER JOIN disabilities AS P
		ON P.activity_id = A.id
		INNER JOIN states AS S
		ON A.state_id = S.id
		INNER JOIN services AS SR
		ON A.service_id = SR.id
		LEFT JOIN employments AS E
		ON A.employment_id = E.id
		LEFT JOIN employers AS EM
		ON E.employer_id = EM.id
		INNER JOIN invoice_activities AS IA
        ON (IA.activity_id = A.id AND IA.type = 'englosado')
        LEFT JOIN invoice_activity_services AS IAS
        ON (IA.invoice_id <> IAS.invoice_id AND IA.activity_id = IAS.activity_id AND IAS.service = SR.name)
		WHERE A.client_id = ? AND IA.invoice_id = ? AND A.service_id IN (14) AND IAS.id IS NULL
	";

    const G_AUDIT_SQL_NEW = "
		SELECT
		SR.name AS NOMBRE_TIPO_SERVICIO,
		A.id AS ID_SERVICIO,
		CONCAT(AF.first_name, ' ', AF.last_name) AS NOMBRE,
		AF.doc_type AS ID_TIPO_DOC,
		AF.doc_number AS IDENTIFICACION,
		AA.created_at AS FECHA_SERVICIO,
		UA.full_name AS FUNCIONARIO,
		'' AS DETALLE,
		IA.type AS TIPO
		FROM activities AS A
		INNER JOIN activity_actions AS AA
		ON AA.id = (
			SELECT id
            FROM activity_actions
            WHERE activity_id = A.id AND action_id IN (83,84,86,88)
            ORDER BY created_at ASC
            LIMIT 1
        )
		INNER JOIN action_service_states ASS
		ON ASS.service_id = A.service_id AND ASS.action_id = AA.action_id
		INNER JOIN states AS AAS
		ON ASS.state_id = AAS.id
		INNER JOIN users AS UA
		ON AA.author_id = UA.id
		INNER JOIN affiliates AS AF
		ON A.affiliate_id = AF.id
		INNER JOIN audits AS AUD
		ON AUD.activity_id = A.id
		INNER JOIN services AS SR
		ON A.service_id = SR.id
		LEFT JOIN employments AS E
		ON A.employment_id = E.id
		LEFT JOIN employers AS EM
		ON E.employer_id = EM.id
		INNER JOIN invoice_activities AS IA
        ON (IA.activity_id = A.id AND IA.type = 'englosado')
        LEFT JOIN invoice_activity_services AS IAS
        ON (IA.invoice_id <> IAS.invoice_id AND IA.activity_id = IAS.activity_id AND IAS.service = SR.name)
		WHERE A.client_id = ? AND IA.invoice_id = ? AND A.service_id IN (15) AND IAS.id IS NULL
	";

    const G_CONSOLIDACION_SQL_NEW = "
        SELECT 
		'CONSOLIDACION DOCS' AS NOMBRE_TIPO_SERVICIO,
		A.id AS ID_SERVICIO,
		CONCAT(AF.first_name, ' ', AF.last_name) AS NOMBRE,
		AF.doc_type AS ID_TIPO_DOC,
		AF.doc_number AS IDENTIFICACION,
		AA.created_at AS FECHA_SERVICIO,
		UA.full_name AS FUNCIONARIO,
		'' AS DETALLE,
		IA.type AS TIPO
		FROM activities AS A
        INNER JOIN activity_actions AS AA
        ON AA.activity_id = A.id AND AA.id = (
            SELECT id
            FROM activity_actions
            WHERE activity_id = A.id AND action_id IN (27,29,44,46,76)
          AND created_at > '2017-08-01'
            AND deleted_at IS NULL
            ORDER BY created_at ASC
            LIMIT 1
        )
        INNER JOIN activity_actions AS AA_DOCS
        ON AA_DOCS.activity_id = A.id AND AA_DOCS.id = (
            SELECT id
            FROM activity_actions
            WHERE activity_id = A.id AND action_id IN (31)
            AND deleted_at IS NULL
            ORDER BY created_at ASC
            LIMIT 1
        )
        INNER JOIN activity_actions AS AA_DOCR1
        ON AA_DOCR1.activity_id = A.id AND AA_DOCR1.id = (
            SELECT id
            FROM activity_actions
            WHERE activity_id = A.id AND action_id IN (38,39,156)
            AND deleted_at IS NULL
            ORDER BY created_at ASC
            LIMIT 1
        )
        INNER JOIN services AS SR
		ON A.service_id = SR.id
		INNER JOIN affiliates AS AF
		ON A.affiliate_id = AF.id
		INNER JOIN users AS UA
		ON AA.author_id = UA.id
		INNER JOIN invoice_activities AS IA
        ON (IA.activity_id = A.id AND IA.type = 'englosado')
        LEFT JOIN invoice_activity_services AS IAS
        ON (IA.invoice_id <> IAS.invoice_id AND IA.activity_id = IAS.activity_id AND IAS.service = 'CONSOLIDACION DOCS')
        WHERE A.client_id = ? AND IA.invoice_id = ? AND IAS.id IS NULL
    ";

    const G_MOBILITY_SQL_NEW = "
    SELECT 
		SR.name AS NOMBRE_TIPO_SERVICIO,
		A.id AS ID_SERVICIO,
		CONCAT(AF.first_name, ' ', AF.last_name) AS NOMBRE,
		AF.doc_type AS ID_TIPO_DOC,
		AF.doc_number AS IDENTIFICACION,
		AA.created_at AS FECHA_SERVICIO,
		UA.full_name AS FUNCIONARIO,
		'' AS DETALLE,
		IA.type AS TIPO
		FROM activities AS A
        INNER JOIN activity_actions AS AA
        ON AA.activity_id = A.id AND AA.id = (
            SELECT id
            FROM activity_actions
            WHERE activity_id = A.id AND action_id IN (155)
            AND deleted_at IS NULL
            ORDER BY FIELD(action_id, 155), created_at ASC
            LIMIT 1
        )
        INNER JOIN services AS SR
		ON A.service_id = SR.id
		INNER JOIN affiliates AS AF
		ON A.affiliate_id = AF.id
		INNER JOIN users AS UA
		ON AA.author_id = UA.id
		INNER JOIN invoice_activities AS IA
        ON (IA.activity_id = A.id AND IA.type = 'englosado')
        LEFT JOIN invoice_activity_services AS IAS
        ON (IA.invoice_id <> IAS.invoice_id AND IA.activity_id = IAS.activity_id AND IAS.service = SR.name)
        WHERE A.client_id = ? AND IA.invoice_id = ? AND IAS.id IS NULL
    ";

    const G_VALORACION_BOG_SQL_NEW = "
    SELECT 
		'VALORACIÓN BOGOTA' AS NOMBRE_TIPO_SERVICIO,
		A.id AS ID_SERVICIO,
		CONCAT(AF.first_name, ' ', AF.last_name) AS NOMBRE,
		AF.doc_type AS ID_TIPO_DOC,
		AF.doc_number AS IDENTIFICACION,
		AA.created_at AS FECHA_SERVICIO,
		UA.full_name AS FUNCIONARIO,
		'' AS DETALLE,
		IA.type AS TIPO
		FROM activities AS A
        INNER JOIN activity_actions AS AA
        ON AA.activity_id = A.id AND AA.id = (
            SELECT id
            FROM activity_actions
            WHERE activity_id = A.id AND action_id IN (146)
            AND created_at > '2017-08-01'
            ORDER BY created_at ASC
            LIMIT 1
        )
        INNER JOIN services AS SR
		ON A.service_id = SR.id
		INNER JOIN affiliates AS AF
		ON A.affiliate_id = AF.id
		INNER JOIN users AS UA
		ON AA.author_id = UA.id
		INNER JOIN invoice_activities AS IA
        ON (IA.activity_id = A.id AND IA.type = 'englosado')
        LEFT JOIN invoice_activity_services AS IAS
        ON (IA.invoice_id <> IAS.invoice_id AND IA.activity_id = IAS.activity_id AND IAS.service = 'VALORACIÓN BOGOTA')
        WHERE A.client_id = ? AND IA.invoice_id = ? AND AF.department = 11 AND IAS.id IS NULL
    ";

    const G_VALORATION_NAL_SQL_NEW = "
    SELECT 
		'VALORACIÓN NACIONAL' AS NOMBRE_TIPO_SERVICIO,
		A.id AS ID_SERVICIO,
		CONCAT(AF.first_name, ' ', AF.last_name) AS NOMBRE,
		AF.doc_type AS ID_TIPO_DOC,
		AF.doc_number AS IDENTIFICACION,
		AA.created_at AS FECHA_SERVICIO,
		UA.full_name AS FUNCIONARIO,
		'' AS DETALLE,
		IA.type AS TIPO
		FROM activities AS A
        INNER JOIN activity_actions AS AA
        ON AA.activity_id = A.id AND AA.id = (
            SELECT id
            FROM activity_actions
            WHERE activity_id = A.id AND action_id IN (146)
            AND created_at > '2017-08-01'
            ORDER BY created_at ASC
            LIMIT 1
        )
        INNER JOIN services AS SR
		ON A.service_id = SR.id
		INNER JOIN affiliates AS AF
		ON A.affiliate_id = AF.id
		INNER JOIN users AS UA
		ON AA.author_id = UA.id
		INNER JOIN invoice_activities AS IA
        ON (IA.activity_id = A.id AND IA.type = 'englosado')
        LEFT JOIN invoice_activity_services AS IAS
        ON (IA.invoice_id <> IAS.invoice_id AND IA.activity_id = IAS.activity_id AND IAS.service = 'VALORACIÓN NACIONAL')
        WHERE A.client_id = ? AND IA.invoice_id = ? AND AF.department <> 11 AND IAS.id IS NULL
    ";

    const G_VALORATION_SQL_NEW = "
    SELECT 
		'CONSULTA MÉDICO LABORAL' AS NOMBRE_TIPO_SERVICIO,
		A.id AS ID_SERVICIO,
		CONCAT(AF.first_name, ' ', AF.last_name) AS NOMBRE,
		AF.doc_type AS ID_TIPO_DOC,
		AF.doc_number AS IDENTIFICACION,
		AA.created_at AS FECHA_SERVICIO,
		UA.full_name AS FUNCIONARIO,
		'' AS DETALLE,
		IA.type AS TIPO
		FROM activities AS A
        INNER JOIN activity_actions AS AA
        ON AA.activity_id = A.id AND AA.id = (
            SELECT id
            FROM activity_actions
            WHERE activity_id = A.id AND action_id IN (146)
            AND created_at > '2017-08-01'
            ORDER BY created_at ASC
            LIMIT 1
        )
        INNER JOIN services AS SR
		ON A.service_id = SR.id
		INNER JOIN affiliates AS AF
		ON A.affiliate_id = AF.id
		INNER JOIN users AS UA
		ON AA.author_id = UA.id
		INNER JOIN invoice_activities AS IA
        ON (IA.activity_id = A.id AND IA.type = 'englosado')
        LEFT JOIN invoice_activity_services AS IAS
        ON (IA.invoice_id <> IAS.invoice_id AND IA.activity_id = IAS.activity_id AND IAS.service = 'CONSULTA MÉDICO LABORAL')
        WHERE A.client_id = ? AND IA.invoice_id = ? AND IAS.id IS NULL
    ";

    const G_TRACKING_SQL_NEW = "
    SELECT 
		'SEGUIMIENTO EN JUNTAS' AS NOMBRE_TIPO_SERVICIO,
		A.id AS ID_SERVICIO,
		CONCAT(AF.first_name, ' ', AF.last_name) AS NOMBRE,
		AF.doc_type AS ID_TIPO_DOC,
		AF.doc_number AS IDENTIFICACION,
		AA.created_at AS FECHA_SERVICIO,
		UA.full_name AS FUNCIONARIO,
		'' AS DETALLE,
		IA.type AS TIPO
		FROM activities AS A
        INNER JOIN activity_actions AS AA
        ON AA.activity_id = A.id AND AA.id = (
            SELECT id
            FROM activity_actions
            WHERE activity_id = A.id AND action_id IN (10, 18, 150, 157, 159, 158, 56)
            AND created_at > '2017-08-01'
            AND deleted_at IS NULL
            ORDER BY created_at ASC
            LIMIT 1
        )
        INNER JOIN services AS SR
		ON A.service_id = SR.id
		INNER JOIN affiliates AS AF
		ON A.affiliate_id = AF.id
		INNER JOIN users AS UA
		ON AA.author_id = UA.id
		INNER JOIN invoice_activities AS IA
        ON (IA.activity_id = A.id AND IA.type = 'englosado')
        LEFT JOIN invoice_activity_services AS IAS
        ON (IA.invoice_id <> IAS.invoice_id AND IA.activity_id = IAS.activity_id AND IAS.service = 'SEGUIMIENTO EN JUNTAS')
        WHERE A.client_id = ? AND IA.invoice_id = ? AND A.service_id IN (1, 2, 9, 10) AND IAS.id IS NULL
    ";

    const G_CONTROVERSY_SQL_NEW = "
    SELECT 
		'CONTROVERSIAS' AS NOMBRE_TIPO_SERVICIO,
		A.id AS ID_SERVICIO,
		CONCAT(AF.first_name, ' ', AF.last_name) AS NOMBRE,
		AF.doc_type AS ID_TIPO_DOC,
		AF.doc_number AS IDENTIFICACION,
		AA.created_at AS FECHA_SERVICIO,
		UA.full_name AS FUNCIONARIO,
		'' AS DETALLE,
		IA.type AS TIPO
		FROM activities AS A
        INNER JOIN activity_actions AS AA
        ON AA.activity_id = A.id AND AA.id = (
            SELECT id
            FROM activity_actions
            WHERE activity_id = A.id AND action_id IN (63, 64, 58, 168, 169)
            AND created_at > '2017-08-01'
            AND deleted_at IS NULL
            ORDER BY created_at ASC
            LIMIT 1
        )
        INNER JOIN services AS SR
		ON A.service_id = SR.id
		INNER JOIN affiliates AS AF
		ON A.affiliate_id = AF.id
		INNER JOIN users AS UA
		ON AA.author_id = UA.id
		INNER JOIN invoice_activities AS IA
        ON (IA.activity_id = A.id AND IA.type = 'englosado')
        LEFT JOIN invoice_activity_services AS IAS
        ON (IA.invoice_id <> IAS.invoice_id AND IA.activity_id = IAS.activity_id AND IAS.service = 'CONTROVERSIAS')
        WHERE A.client_id = ? AND IA.invoice_id = ? AND A.service_id IN (10) AND IAS.id IS NULL
    ";

    const G_ANALISIS_SQL_NEW = "
    SELECT 
		'EVALUACIÓN, ANALISIS Y ACCIÓN' AS NOMBRE_TIPO_SERVICIO,
		A.id AS ID_SERVICIO,
		CONCAT(AF.first_name, ' ', AF.last_name) AS NOMBRE,
		AF.doc_type AS ID_TIPO_DOC,
		AF.doc_number AS IDENTIFICACION,
		AA.created_at AS FECHA_SERVICIO,
		UA.full_name AS FUNCIONARIO,
		'' AS DETALLE,
		IA.type AS TIPO
		FROM activities AS A
        INNER JOIN activity_actions AS AA
        ON AA.activity_id = A.id AND AA.id = (
            SELECT id
            FROM activity_actions
            WHERE activity_id = A.id AND action_id IN (8, 60, 65, 59)
            AND created_at > '2017-08-01'
            AND deleted_at IS NULL
            ORDER BY created_at ASC
            LIMIT 1
        )
        INNER JOIN services AS SR
		ON A.service_id = SR.id
		INNER JOIN affiliates AS AF
		ON A.affiliate_id = AF.id
		INNER JOIN users AS UA
		ON AA.author_id = UA.id
		INNER JOIN invoice_activities AS IA
        ON (IA.activity_id = A.id AND IA.type = 'englosado')
        LEFT JOIN invoice_activity_services AS IAS
        ON (IA.invoice_id <> IAS.invoice_id AND IA.activity_id = IAS.activity_id AND IAS.service = 'EVALUACIÓN, ANALISIS Y ACCIÓN')
        WHERE A.client_id = ? AND IA.invoice_id = ? AND A.service_id IN (1, 2, 9, 10) AND IAS.id IS NULL
    ";

    const G_CARGUE_SQL_NEW = "
    SELECT 
		'CARGUE DE DOCUMENTOS' AS NOMBRE_TIPO_SERVICIO,
		A.id AS ID_SERVICIO,
		CONCAT(AF.first_name, ' ', AF.last_name) AS NOMBRE,
		AF.doc_type AS ID_TIPO_DOC,
		AF.doc_number AS IDENTIFICACION,
		AA.created_at AS FECHA_SERVICIO,
		UA.full_name AS FUNCIONARIO,
		'' AS DETALLE,
		IA.type AS TIPO
		FROM activities AS A
        INNER JOIN activity_actions AS AA
        ON AA.activity_id = A.id AND AA.id = (
            SELECT id
            FROM activity_actions
            WHERE activity_id = A.id AND action_id IN (10, 15, 33, 34, 140, 141, 150, 157)
            AND created_at > '2017-08-01'
            AND deleted_at IS NULL
            ORDER BY created_at ASC
        )
        INNER JOIN services AS SR
		ON A.service_id = SR.id
		INNER JOIN affiliates AS AF
		ON A.affiliate_id = AF.id
		INNER JOIN users AS UA
		ON AA.author_id = UA.id
		INNER JOIN invoice_activities AS IA
        ON (IA.activity_id = A.id AND IA.type = 'englosado')
        LEFT JOIN invoice_activity_services AS IAS
        ON (IA.invoice_id <> IAS.invoice_id AND IA.activity_id = IAS.activity_id AND IAS.service = 'CARGUE DE DOCUMENTOS')
        WHERE A.client_id = ? AND IA.invoice_id = ? AND IAS.id IS NULL
    ";

    const G_FURAT_SQL_NEW = "
    SELECT 
		'FURAT' AS NOMBRE_TIPO_SERVICIO,
		A.id AS ID_SERVICIO,
		CONCAT(AF.first_name, ' ', AF.last_name) AS NOMBRE,
		AF.doc_type AS ID_TIPO_DOC,
		AF.doc_number AS IDENTIFICACION,
		AA.created_at AS FECHA_SERVICIO,
		UA.full_name AS FUNCIONARIO,
		'' AS DETALLE,
		IA.type AS TIPO
		FROM activities AS A
        INNER JOIN activity_actions AS AA
        ON AA.activity_id = A.id AND AA.id = (
            SELECT id
            FROM activity_actions
            WHERE activity_id = A.id AND action_id IN (149)
            AND created_at > '2017-08-01'
            AND deleted_at IS NULL
            ORDER BY created_at ASC
            LIMIT 1
        )
        INNER JOIN services AS SR
		ON A.service_id = SR.id
		INNER JOIN affiliates AS AF
		ON A.affiliate_id = AF.id
		INNER JOIN users AS UA
		ON AA.author_id = UA.id
		INNER JOIN invoice_activities AS IA
        ON (IA.activity_id = A.id AND IA.type = 'englosado')
        LEFT JOIN invoice_activity_services AS IAS
        ON (IA.invoice_id <> IAS.invoice_id AND IA.activity_id = IAS.activity_id AND IAS.service = 'FURAT')
        WHERE A.client_id = ? AND IA.invoice_id = ? AND IAS.id IS NULL
    ";

    const ATELPCL_DOCTOR_CONSOLIDATE_SQL_NEW = "
    SELECT 
		UA.full_name AS FUNCIONARIO,
		UA.id AS FUNCIONARIO_ID,
        UA.email AS EMAIL
		FROM activities AS A
        INNER JOIN activity_actions AS AA
        ON AA.activity_id = A.id AND AA.id = (
            SELECT id
            FROM activity_actions
            WHERE activity_id = A.id AND action_id IN (44, 55, 54)
            AND created_at > '2017-08-01'
            AND deleted_at IS NULL
            AND old_state_id IN (18, 22)
            ORDER BY created_at ASC
            LIMIT 1
        )
        INNER JOIN services AS SR
		ON A.service_id = SR.id
		INNER JOIN affiliates AS AF
		ON A.affiliate_id = AF.id
		INNER JOIN users AS UA
		ON AA.author_id = UA.id
		INNER JOIN invoice_activities AS IA
        ON (IA.activity_id = A.id)
        LEFT JOIN invoice_activity_services AS IAS
        ON (IA.invoice_id <> IAS.invoice_id AND IA.activity_id = IAS.activity_id AND IAS.service = SR.name)
        WHERE A.client_id = ? AND IA.invoice_id = ? AND IAS.id IS NULL
        AND UA.ascribed = 1 
        GROUP BY UA.id 
    ";

    const ATELPCL_DOCTOR_SQL_NEW = "
    SELECT 
        SR.name AS NOMBRE_SERVICIO,
		A.id AS ID_SERVICIO,
		UA.full_name AS FUNCIONARIO,
		UA.id AS FUNCIONARIO_ID,
		DOCI.id AS DOCINVOICE_ID
		FROM activities AS A
        INNER JOIN activity_actions AS AA
        ON AA.activity_id = A.id AND AA.id = (
            SELECT id
            FROM activity_actions
            WHERE activity_id = A.id AND action_id IN (44, 55, 54)
            AND created_at > '2017-08-01'
            AND deleted_at IS NULL
            AND old_state_id IN (18, 22)
            ORDER BY created_at ASC
            LIMIT 1
        )
        INNER JOIN services AS SR
		ON A.service_id = SR.id
		INNER JOIN affiliates AS AF
		ON A.affiliate_id = AF.id
		INNER JOIN users AS UA
		ON AA.author_id = UA.id
		INNER JOIN invoice_activities AS IA
        ON (IA.activity_id = A.id)
        LEFT JOIN docinvoices AS DOCI
        ON (IA.invoice_id=DOCI.invoice_id AND AA.author_id=DOCI.user_id)
        LEFT JOIN invoice_activity_services AS IAS
        ON (IA.invoice_id <> IAS.invoice_id AND IA.activity_id = IAS.activity_id AND IAS.service = SR.name)
        WHERE A.client_id = ? AND IA.invoice_id = ? AND IAS.id IS NULL
        AND UA.ascribed = 1 AND (SELECT count(*) FROM docinvoice_activities WHERE docinvoice_id<>DOCI.id AND activity_id=A.id)<=0
    ";

    const ATELPCL_DOCTOR_SPECIFIC_SQL_NEW = "
    SELECT 
        SR.name AS NOMBRE_SERVICIO,
		A.id AS ID_SERVICIO,
		UA.full_name AS FUNCIONARIO,
		UA.id AS FUNCIONARIO_ID,
		DOCI.id AS DOCINVOICE_ID
		FROM activities AS A
        INNER JOIN activity_actions AS AA
        ON AA.activity_id = A.id AND AA.id = (
            SELECT id
            FROM activity_actions
            WHERE activity_id = A.id AND action_id IN (44, 55, 54)
            AND created_at > '2017-08-01'
            AND deleted_at IS NULL
            AND old_state_id IN (18, 22)
            ORDER BY created_at ASC
            LIMIT 1
        )
        INNER JOIN services AS SR
		ON A.service_id = SR.id
		INNER JOIN affiliates AS AF
		ON A.affiliate_id = AF.id
		INNER JOIN users AS UA
		ON AA.author_id = UA.id
		INNER JOIN invoice_activities AS IA
        ON (IA.activity_id = A.id)
        LEFT JOIN docinvoices AS DOCI
        ON (IA.invoice_id=DOCI.invoice_id AND AA.author_id=DOCI.user_id)
        LEFT JOIN invoice_activity_services AS IAS
        ON (IA.invoice_id <> IAS.invoice_id AND IA.activity_id = IAS.activity_id AND IAS.service = SR.name)
        WHERE A.client_id = ? AND IA.invoice_id = ? AND DOCI.user_id = ? AND IAS.id IS NULL
        AND UA.ascribed = 1 AND (SELECT count(*) FROM docinvoice_activities WHERE docinvoice_id<>DOCI.id AND activity_id=A.id)<=0
    ";

    public static function recobros($client_id, $initial_date, $end_date)
    {
        $items = RecobroInvoice::join('recobros', 'recobro_invoices.recobro_id', '=', 'recobros.id')
            ->join('activities', 'recobros.activity_id', '=', 'activities.id')
            ->whereNull('activities.deleted_at')
            ->where('activities.client_id', $client_id)
            ->where('activities.service_id', Service::SERVICE_RECOBRO_FAMISANAR)
            ->whereNotNull('recobro_invoices.value')
            ->select('recobro_invoices.id', 'recobro_invoices.recobro_id', 'ips_type', 'mapiiss_code', 'radication_num', 'unit_value', 'quantity', 'value')
            ->with(array('recobro' => function ($query) {
                $query->select('id', 'activity_id');
            }))
            ->with(array('recobro.diagnostics' => function ($query) {
                $query->select('id', 'recobro_id', 'code', 'description');
            }))
            ->with(array('recobro.activity' => function ($query) {
                $query->select('id', 'user_id', 'affiliate_id', 'created_at');
            }))
            ->with(array('recobro.activity.documents' => function ($query) {
                $query->select('id', 'activity_id', 'document_id', 'path');
            }))
            ->with(array('recobro.activity.affiliate' => function ($query) {
                $query->select('id', 'doc_type', 'doc_number', 'first_name', 'last_name', 'arl');
            }))
            ->with(array('recobro.activity.user' => function ($query) {
                $query->select('id', 'full_name');
            }))
            ->whereBetween('activities.created_at', [$initial_date, $end_date])
            ->orderBy('activities.created_at', 'ASC')
            ->get();

        return collect($items)->map(function ($item) {
            return array(
                'NOMBRE_TIPO_SERVICIO' => 'RECOBRO',
                'ID_SERVICIO'          => $item->recobro->activity_id,
                'NOMBRE'               => $item->recobro->activity->affiliate->full_name,
                'ID_TIPO_DOC'          => $item->recobro->activity->affiliate->doc_type,
                'IDENTIFICACION'       => $item->recobro->activity->affiliate->doc_number,
                'FECHA_SERVICIO'       => $item->recobro->activity->created_at,
                'FUNCIONARIO'          => $item->recobro->activity->user->full_name,
                'DETALLE'              => $item->radication_num,
            );
        });
    }

    public static function recobros_new($client_id, $invoice_id)
    {
        $items = RecobroInvoice::join('recobros', 'recobro_invoices.recobro_id', '=', 'recobros.id')
                               ->join('activities', 'recobros.activity_id', '=', 'activities.id')
                               ->join('invoice_activities', 'invoice_activities.activity_id', '=', 'activities.id')
                                ->leftJoin('invoice_activity_services', function ($join) {
                                    $join->on('invoice_activity_services.activity_id', '=', 'invoice_activities.activity_id')
                                        ->on('invoice_activity_services.invoice_id', '<>', 'invoice_activities.invoice_id')
                                        ->where('invoice_activity_services.service', '=', 'RECOBRO');
                                })
                               ->whereNull('activities.deleted_at')
                               ->where('activities.client_id', $client_id)
                               ->where('activities.service_id', Service::SERVICE_RECOBRO_FAMISANAR)
                               ->where('invoice_activities.type', '=', 'facturado')
                               ->whereNotNull('recobro_invoices.value')
                               ->select('recobro_invoices.id', 'recobro_invoices.recobro_id', 'ips_type', 'mapiiss_code', 'radication_num', 'unit_value', 'quantity', 'value', 'invoice_activities.type')
                               ->with(array('recobro' => function ($query) {
                                   $query->select('id', 'activity_id');
                               }))
                               ->with(array('recobro.diagnostics' => function ($query) {
                                   $query->select('id', 'recobro_id', 'code', 'description');
                               }))
                               ->with(array('recobro.activity' => function ($query) {
                                   $query->select('id', 'user_id', 'affiliate_id', 'created_at');
                               }))
                               ->with(array('recobro.activity.documents' => function ($query) {
                                   $query->select('id', 'activity_id', 'document_id', 'path');
                               }))
                               ->with(array('recobro.activity.affiliate' => function ($query) {
                                   $query->select('id', 'doc_type', 'doc_number', 'first_name', 'last_name', 'arl');
                               }))
                               ->with(array('recobro.activity.user' => function ($query) {
                                   $query->select('id', 'full_name');
                               }))
                               ->where('invoice_activities.invoice_id', '=', $invoice_id)
                               ->whereNull('invoice_activity_services.id')
                               ->orderBy('activities.created_at', 'ASC')
                               ->get();

        return collect($items)->map(function ($item) {
            return array(
                'NOMBRE_TIPO_SERVICIO' => 'RECOBRO',
                'ID_SERVICIO'          => $item->recobro->activity_id,
                'NOMBRE'               => $item->recobro->activity->affiliate->full_name,
                'ID_TIPO_DOC'          => $item->recobro->activity->affiliate->doc_type,
                'IDENTIFICACION'       => $item->recobro->activity->affiliate->doc_number,
                'FECHA_SERVICIO'       => $item->recobro->activity->created_at,
                'FUNCIONARIO'          => $item->recobro->activity->user->full_name,
                'DETALLE'              => $item->radication_num,
                'TIPO'                 => $item->type,
            );
        });
    }

    public static function recobros_new_englosed($client_id, $invoice_id)
    {
        $items = RecobroInvoice::join('recobros', 'recobro_invoices.recobro_id', '=', 'recobros.id')
                               ->join('activities', 'recobros.activity_id', '=', 'activities.id')
                               ->join('invoice_activities', 'invoice_activities.activity_id', '=', 'activities.id')
                                ->leftJoin('invoice_activity_services', function ($join) {
                                    $join->on('invoice_activity_services.activity_id', '=', 'invoice_activities.activity_id')
                                         ->on('invoice_activity_services.invoice_id', '<>', 'invoice_activities.invoice_id')
                                         ->where('invoice_activity_services.service', '=', 'RECOBRO');
                                })
                                ->whereNull('activities.deleted_at')
                               ->where('activities.client_id', $client_id)
                               ->where('activities.service_id', Service::SERVICE_RECOBRO_FAMISANAR)
                               ->where('invoice_activities.type', '=', 'englosado')
                               ->whereNotNull('recobro_invoices.value')
                               ->select('recobro_invoices.id', 'recobro_invoices.recobro_id', 'ips_type', 'mapiiss_code', 'radication_num', 'unit_value', 'quantity', 'value', 'invoice_activities.type')
                               ->with(array('recobro' => function ($query) {
                                   $query->select('id', 'activity_id');
                               }))
                               ->with(array('recobro.diagnostics' => function ($query) {
                                   $query->select('id', 'recobro_id', 'code', 'description');
                               }))
                               ->with(array('recobro.activity' => function ($query) {
                                   $query->select('id', 'user_id', 'affiliate_id', 'created_at');
                               }))
                               ->with(array('recobro.activity.documents' => function ($query) {
                                   $query->select('id', 'activity_id', 'document_id', 'path');
                               }))
                               ->with(array('recobro.activity.affiliate' => function ($query) {
                                   $query->select('id', 'doc_type', 'doc_number', 'first_name', 'last_name', 'arl');
                               }))
                               ->with(array('recobro.activity.user' => function ($query) {
                                   $query->select('id', 'full_name');
                               }))
                               ->where('invoice_activities.invoice_id', '=', $invoice_id)
                               ->whereNull('invoice_activity_services.id')
                               ->orderBy('activities.created_at', 'ASC')
                               ->get();

        return collect($items)->map(function ($item) {
            return array(
                'NOMBRE_TIPO_SERVICIO' => 'RECOBRO',
                'ID_SERVICIO'          => $item->recobro->activity_id,
                'NOMBRE'               => $item->recobro->activity->affiliate->full_name,
                'ID_TIPO_DOC'          => $item->recobro->activity->affiliate->doc_type,
                'IDENTIFICACION'       => $item->recobro->activity->affiliate->doc_number,
                'FECHA_SERVICIO'       => $item->recobro->activity->created_at,
                'FUNCIONARIO'          => $item->recobro->activity->user->full_name,
                'DETALLE'              => $item->radication_num,
                'TIPO'                 => $item->type,
            );
        });
    }

}
