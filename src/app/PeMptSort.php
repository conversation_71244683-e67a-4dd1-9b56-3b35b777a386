<?php

namespace App;


use Illuminate\Database\Eloquent\Model;

class PeMptSort extends Model
{
    protected $table = 'pempt_sorts';

    protected $fillable = [
        'activity_id',
        'type_document',
        'identification_number',
        'name',
        'case',
        'date_of_death',
        'entity_that_issued_the_rating',
        'opinion_or_rating',
        'qualification_date',
        'qualification',
        'code_cie',
        'name_of_the_diagnosis',
        'condition',
        "start_date_of_rent",
        "start_date_of_rent_submit",
        "prom_monthly_salary",
        "annual_salary_person",
        "maximum_annual_salary",
        "annual_income",
        "monthly_value",
        "additional_monthly_sum",
        "total_month_to_pay",
        "total_years_to_pay",
        "type_of_beneficiary",
        "name_and_surname",
        "relationship",
        "monthly_payment",
        "account_number",
        "name_of_the_banking_entity",
        "General_information",
        "information_procedure",
        "detail_periods",
        "payment_information",
        "observation",
        "paid_office",
        "result_revision",
        "payment_office_number",
        "resolution_date",
    ];

    public function activity()
    {
        return $this->belongsTo('App\Activity');
    }

    public function pe_mpt_beneficiaries()
    {
        return $this->hasMany(PeMptBeneficiaries::class, 'pe_mpt_id');
    }

}
