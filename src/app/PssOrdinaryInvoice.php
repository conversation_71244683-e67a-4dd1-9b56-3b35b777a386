<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class PssOrdinaryInvoice extends Model
{
    protected $table = 'pss_ordinary_invoices';

    protected $fillable = [
        'social_security_provider_id',
        'case_number',
        'identification_number',
        'name',
        'date',
        'medical_center_code',
        'invoice_number',
        'total_amount',
        'result',
        'reason',
        'gis_sort_id',
    ];

    public function activity()
    {
        return $this->belongsTo('App\Activity');
    }
}
