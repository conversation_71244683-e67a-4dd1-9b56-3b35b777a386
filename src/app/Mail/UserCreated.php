<?php

namespace App\Mail;

use App\User;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class UserCreated extends Mailable
{
    use Queueable, SerializesModels;

    public $user;
    public $plainPW;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(User $user, $plainPW)
    {
        $this->user    = $user;
        $this->plainPW = $plainPW;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        $address = '<EMAIL>';
        $name = 'MNK seguros';

        return $this->subject('Creación de cuenta MNK seguros')
            ->from($address, $name)
            ->view('emails.user_created');
    }
}
