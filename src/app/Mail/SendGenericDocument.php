<?php

namespace App\Mail;

use App\Http\Controllers\MailController;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Storage;

class SendGenericDocument extends Mailable
{
    use Queueable, SerializesModels;

    public $subject;
    public $text;
    public $files;
    public $fromE;
    public $nameFrom;
    public $client_id;
    public $templateEmail;
    public $timestamp;
    public $emails;
    public $host;

    /**
     * Constructor de la clase, instanciar...
     *
     * @param string $emails
     * @param string $subjectEmail
     * @param string $fromE
     * @param string $nameFrom
     * @param array $description
     * @param string $sender
     * @param array $files
     * @param string $templateEmail
     * @param int $client_id
     * @param int|null $activity_id
     * @param int|null $activityAction_id
     * @param int|null $service_id
     * @return void
     */
    public function __construct($emails, $subjectEmail, $fromE, $nameFrom, $description, $sender, $files,
                                $templateEmail, $client_id, $activity_id = null, $activityAction_id = null, $service_id = null, $mailer = 'default')
    {
        $this->emails = $emails;
        $this->subject = $subjectEmail;
        $this->text = $description;
        $this->sender = $sender;
        $this->files = $files;
        $this->activityAction_id = $activityAction_id;
        $this->activity_id = $activity_id;
        $this->service_id = $service_id;
        $this->templateEmail = $templateEmail;
        $this->fromE = $fromE;
        $this->nameFrom = $nameFrom;
        $this->client_id = $client_id;
        $this->timestamp = Carbon::now()->timestamp;
        $this->mailer = $mailer;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        $this->text["files"] = $this->validateFile($this->files);

        $mail = $this->subject($this->subject.' <'.$this->timestamp.'>')
            ->from($this->fromE, $this->nameFrom)
            ->view('emails.'.$this->templateEmail, $this->text);

        foreach ($this->files as $file) {
            $fileContent = $this->getFileContent($file);
            if ($fileContent) {
                $mail = $mail->attachData($fileContent, $file['name'], [
                    'mime' => $this->getFileMimeType($file),
                ]);
            }
        }

        return $mail;
    }

    /**
     * Método para enviar el email y guardar en DB
     *
     * @return void
     */
    public function sendMail()
    {
        $this->text["files"] = $this->validateFile($this->files, $this->host);
        if (!empty($this->emails)) {
            $this->emails = array_unique(array_map('trim', explode(',', $this->emails)));
            MailController::sendEmail($this->emails, $this->buildEmail(), $this->mailer);
        }
    }

    /**
     * Construye los enlaces segun el Bucket
     *
     * @param $files
     * @return string
     */
    public function validateFile($files)
    {
        $extraHtml = '';
        foreach ($files as $file) {

            if ($this->isLocalFile($file['path'])) {
                continue;
            } else {
                $filePath = 'https://' . env('AWS_BUCKET') . '.s3.amazonaws.com/' . $file['path'];
            }

            $extraHtml .= '<br/><br/>Puede descargar el documento presionando <a href="' . $filePath . '">AQUÍ</a>';
            $extraHtml .= '<br/><br/>O puede descargarlo copiando y pegando el siguiente link en el navegador: <br/><br/>' . $filePath . '<br/><br/>';
        }
        return $extraHtml;
    }

    /**
     * Obtiene el archivo, ya sea local o de S3.
     *
     * @param $file
     * @return false|string
     */
    private function getFileContent($file)
    {
        if ($this->isLocalFile($file['path'])) {
            return file_get_contents($file['path']);
        }

        return Storage::disk('s3')->get($file['path']);
    }

    /**
     * Valida si es un archivo local
     *
     * @param $filePath
     * @return bool
     */
    private function isLocalFile($filePath)
    {
        return file_exists($filePath);
    }

    /**
     * Asigna el tipo MIME del archivo
     * @param $file
     * @return string
     */
    private function getFileMimeType($file)
    {
        if (isset($file['type']) && strtolower($file['type']) === 'pdf') {
            return 'application/pdf';
        }

        return 'application/octet-stream';
    }

    /**
     * Guarda los emails en la BD
     * @return $this
     */
    public function buildEmail()
    {
        $mail = $this->build();

        foreach ($this->emails as $email) {
            saveMailInDB(
                $email,
                $this->subject,
                $this->timestamp,
                $this->files,
                $mail->render(),
                $this->sender,
                $this->service_id,
                $this->activity_id,
                $this->activityAction_id,
                $this->client_id,
                $this->fromE,
                $this->nameFrom
            );
        }

        return $mail;
    }
}
