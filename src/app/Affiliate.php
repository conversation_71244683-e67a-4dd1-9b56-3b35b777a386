<?php

namespace App;

use App\AffiliateFile;
use App\LoggedModel;
use App\Providers\AppServiceProvider;
use DateTime;
use OwenIt\Auditing\Contracts\Auditable;
use stdClass;
use Storage;

class Affiliate extends LoggedModel implements Auditable
{
    use \OwenIt\Auditing\Auditable;
    protected $dates   = ['deleted_at', 'birthday'];
    protected $appends = array('full_name');
    protected $fillable = [
        'doc_type',
        'doc_number',
        'client_id',
        'first_name',
        'last_name',
        'birthday',
        'gender',
        'address',
        'phone',
        'cellphone',
        'email',
        'affiliation_type',
        'eps',
        'regional',
        'department',
        'municipality',
        'civil_status',
        'school_level',
        'arl',
        'afp',
        'pension',
        'transfer',
        'survival_state',
        'ibc',
        'province',
        'canton',
        'district',
        'unique_code',
        'country',
        'occupation',
        'occupancy_group',
        'iban_account',
        'occupations_category_id',
    ];

    public function client()
    {
        return $this->belongsTo('App\Client');
    }

    public function activities()
    {
        return $this->hasMany('App\Activity');
    }

    public function employments()
    {
        return $this->hasMany('App\Employment');
    }

    public function authorizations()
    {
        return $this->hasMany('App\Authorization', 'nat_num_ide_doc', 'doc_number')->orderBy('fecha_expedicion', 'DESC');
    }

    public function historyDisabilities()
    {
        return $this->hasMany('App\HistoryDisability', 'num_ide_paciente', 'doc_number')->orderBy('inicio_expedicion', 'DESC');
    }

    public function policy_spreadsheet_affiliate()
    {
        return $this->hasMany('App\PolicySpreadsheetAffiliate');
    }

    public function age()
    {
        $birthday    = new DateTime($this->birthday);
        $currentDate = new DateTime('now');

        $interval = $birthday->diff($currentDate);

        return $interval->format('%y');
    }
    public function pcl_age_restriction()
    {
        if ($this->age() < 18) {
            return 2.5;
        }

        if ($this->age() >= 18 && $this->age() < 30) {
            return 0.5;
        }

        if ($this->age() >= 30 && $this->age() < 40) {
            return 1.0;
        }

        if ($this->age() >= 40 && $this->age() < 50) {
            return 1.5;
        }

        if ($this->age() >= 50 && $this->age() < 60) {
            return 2.0;
        }

        if ($this->age() >= 60) {
            return 2.5;
        }
    }

    public function injury_age_restriction()
    {
        if ($this->age() < 18) {
            return 2.5;
        }

        if ($this->age() >= 18 && $this->age() < 30) {
            return 0.5;
        }

        if ($this->age() >= 30 && $this->age() < 40) {
            return 1.0;
        }

        if ($this->age() >= 40 && $this->age() < 50) {
            return 1.5;
        }

        if ($this->age() >= 50 && $this->age() < 60) {
            return 2.0;
        }

        if ($this->age() >= 60) {
            return 2.5;
        }
    }

    public function getFullNameAttribute()
    {
        return mb_strtoupper($this->first_name . ' ' . $this->last_name);
    }

    public function getFirstNameAttribute($value)
    {
        return mb_strtoupper($value);
    }
    public function gender()
    {
        $value = $this->gender;
        $gender_list = [
            'M' => 'Masculino',
            'F' => 'Femenino',
            'NB' => 'No Binario'
        ];

        return $gender_list[$value] ? $gender_list[$value] : '';
    }
    public function regional()
    {
        $value = $this->regional;
        $regional_list = AppServiceProvider::$REGIONAL;
        return $regional_list[$value] ? $regional_list[$value] : '';
    }

    public function getLastNameAttribute($value)
    {
        return mb_strtoupper($value);
    }

    public function getAddressAttribute($value)
    {
        return mb_strtoupper($value);
    }


    public function fullLogs()
    {
        $logs = $this->logs();

        foreach ($this->employments as $employment) {
            $logs = $logs->merge($employment->logs());
            if ($employment->employer != null) {
                $logs = $logs->merge($employment->employer->logs());
            }
            if ($employment->branch != null) {
                $logs = $logs->merge($employment->branch->logs());
            }

            // $logs = array_merge($logs, $employment->logs());
            // $logs = array_merge($logs, $employment->employer->logs());
        }

        return $logs->sortByDesc('created_at');
    }

    public function clinicalHistoryFiles()
    {
        $files  = array();
        $groups = AffiliateFile::select(['created_at', 'comment'])
            ->groupBy(['created_at', 'comment'])
            ->where('affiliate_id', $this->id)
            ->where('category', 'clinical_histories')
            ->get();

        foreach ($groups as $group) {
            $file             = new stdClass();
            $file->created_at = $group->created_at;
            $file->comment    = $group->comment;
            $file->files      = AffiliateFile::select(['id', 'path'])
                ->where('created_at', $group->created_at)
                ->where('comment', $group->comment)
                ->where('affiliate_id', $this->id)
                ->where('category', 'clinical_histories')
                ->get();
            $files[] = $file;
        }

        return $files;
    }

    public function loadedDocuments()
    {
        $activities = [];
        foreach ($this->activities as $activity) {
            $activities[] = $activity->id;
        }
        $documents = \App\ActivityDocument::whereIn('activity_id', $activities)->whereNull('deleted_at')->whereNotNull('path')->orderBy('uploaded_at', 'desc')->get();
        return $documents;
    }

    public function generatedDocuments()
    {
        $activity_actions = [];
        foreach ($this->activities as $activity) {
            foreach ($activity->activity_actions as $aa) {
                $activity_actions[] = $aa->id;
            }
        }

        $documents = \App\ActivityActionDocument::whereIn('activity_action_id', $activity_actions)->whereNull('deleted_at')->whereNotNull('path')->orderBy('created_at', 'desc')->get();
        return $documents;
    }

    public function loadedDocumentsInActivityActions()
    {
        $activity_actions = [];
        foreach ($this->activities as $activity) {
            foreach ($activity->activity_actions as $aa) {
                $activity_actions[] = $aa->id;
            }
        }
        $documents = \App\ActivityActionField::leftJoin('action_fields', 'activity_action_fields.action_field_id', '=', 'action_fields.id')->with('action_field')->whereIn('activity_action_id', $activity_actions)->whereNotNull('activity_action_fields.value')->where('action_fields.type', '=', 'file')->orderBy('activity_action_fields.created_at', 'desc')->get();
        return $documents;
    }

    public function loadedCalls()
    {
        $activity_actions = [];
        foreach ($this->activities as $activity) {
            foreach ($activity->activity_actions as $aa) {
                $activity_actions[] = $aa->id;
            }
        }
        $calls = \App\ActivityActionField::leftJoin('action_fields', 'activity_action_fields.action_field_id', '=', 'action_fields.id')->with('action_field')->select('activity_action_fields.*')->whereIn('activity_action_id', $activity_actions)->whereNotNull('activity_action_fields.value')->where('action_fields.type', '=', 'call')->orderBy('activity_action_fields.created_at', 'desc')->get();
        return $calls;
    }

    public function loadedPriCalls()
    {
        $calls = [];
        foreach ($this->activities as $activity) {
            if ($activity->pri) {
                foreach ($activity->pri->valorations as $val) {
                    if ($val->call_date) {
                        $calls[] = $val;
                    }
                }
            }
        }
        return $calls;
    }

    public function fullARLs($created_at)
    {
        $logs = $this->logs();
        $logs = $logs->where("action", "UPDATING")->where("field", "arl")->where('created_at', '>', $created_at);
        return $logs->sortByDesc('created_at');
    }

    public function fullAFPs($created_at)
    {
        $logs = $this->logs();
        $logs = $logs->where("action", "UPDATING")->where("field", "afp")->where('created_at', '>', $created_at);

        return $logs->sortByDesc('created_at');
    }

    public function haveARLChange($created_at)
    {
        $logs = $this->logs();
        $logs = $logs->where("action", "UPDATING")->where("field", "arl")->where('created_at', '>', $created_at);
        return (count($logs) > 0);
    }

    public function haveAFPChange($created_at)
    {
        $logs = $this->logs();
        $logs = $logs->where("action", "UPDATING")->where("field", "afp")->where('created_at', '>', $created_at);
        return (count($logs) > 0);
    }

    public function searchIdAffiliate($typeDocument, $numDocument)
    {

        $idAffiliate  = 0;

        $idAffiliate = $this->select('id')
            ->where('doc_type', $typeDocument)
            ->where('doc_number', $numDocument)
            ->where('client_id', env('CLIENT_ID'))
            ->first();

        //error_log ("Resultado: ". print_r ($idAffiliate, true) );

        if (!$idAffiliate) {
            return 0;
        } else {
            return $idAffiliate->id;
        }
    }
}
