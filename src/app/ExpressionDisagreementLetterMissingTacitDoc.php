<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Schema;
use OwenIt\Auditing\Contracts\Auditable;

class ExpressionDisagreementLetterMissingTacitDoc extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable;
    public function isComplete()
    {
    }

    public function activity()
    {
        return $this->belongsTo('App\DeterminationIt');
    }

}
