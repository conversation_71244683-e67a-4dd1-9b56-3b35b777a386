<?php

namespace App\Console\Commands;

use App\Services\ColmedicaWebService;
use Illuminate\Console\Command;

class VerifyColmedicaQueues extends Command
{
    private $service;
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'verifyColmedicaQueues';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Realiza una verificacion de los procesos en cola del webservice colmedica';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(ColmedicaWebService $service)
    {
        parent::__construct();
        $this->service = $service;
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        return $this->service->verify();
    }
}
