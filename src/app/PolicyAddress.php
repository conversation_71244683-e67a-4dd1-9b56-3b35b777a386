<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class PolicyAddress extends Model
{
    protected $table = 'policy_addresses';

    protected $fillable = [
        'policy_sort_id',
        'type',
        'full_address',
        'province_id',
        'canton_id',
        'district_id',
        'reference',
    ];

    public function policySort()
    {
        return $this->belongsTo('App\PolicySort', 'policy_sort_id');
    }
}
