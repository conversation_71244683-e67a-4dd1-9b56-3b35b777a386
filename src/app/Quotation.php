<?php

namespace App;

use App\Actions\ActionCotizacionSort;
use Illuminate\Database\Eloquent\Model;

class Quotation extends Model
{
    //DOCUMENTOS ASOCIADOS A COTIZACION
    public const DOCUMENTO_SOPORTE_1 = 274;
    public const DOCUMENTO_SOPORTE_2 = 277;
    public const DOCUMENTO_SOPORTE_3 = 278;
    public const DOCUMENTO_SOPORTE_4 = 279;
    public const DOCUMENTO_SOPORTE_5 = 280;
    public const DOCUMENTO_SOPORTE_6 = 281;
    public const DOCUMENTO_SOPORTE_7 = 282;
    public const DOCUMENTO_SOPORTE_8 = 283;
    public function activity()
    {
        return $this->belongsTo('App\Activity');
    }

    public function policy()
    {
        $activity = Activity::where('parent_id', $this->activity_id)
            ->where('service_id', Service::SERVICE_POLICY_SORT_MNK)->first();
        if ($activity) {
            return PolicySort::where('activity_id', $activity->id)->first();
        }
        return null;
    }

    public function getFile()
    {

        $action = ActivityAction::where('activity_id', $this->activity_id)->whereIn('action_id', [Action::SOLICITAR_COTIZACION, ActionCotizacionSort::SOLICITAR_COTIZACION_ACT_ALTO_RIESGO])->first();
        if (!$action) {
            return null;
        }
        $document = ActivityActionDocument::where('activity_action_id', $action->id)->first();
        if (!$document) {
            return null;
        }
        return $document->path;
    }

    protected $fillable = [
        "validity_from",
        "validity_to",
        "economic_activity_percentage",
        "option_asegurement",
        "unico_percentage",
        "mensual_percentage",
        "trimestral_percentage",
        "semestral_percentage",
        "anual_percentage",
        "special_condition_payment",
        "preventive_actions",
        "amount_policy",
        "annual_calculation_amount",
        "semiannual_calculation_amount" ,
        "quarterly_calculation_amount" ,
        "monthly_calculation_amount",
        "single_payment_value",
        "special_condition",
        "has_office",
        "tariff_bonuses",
        "has_medical_office",
        "change_date",
        "institutional_sector"
    ];

    public function formatNumber()
    {
        // Formatear el número para que tenga al menos 4 cifras con ceros a la izquierda
        return sprintf('%04d', $this->id);
    }
}
