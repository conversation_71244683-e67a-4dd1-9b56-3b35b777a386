<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class DataPreInvoice extends Model
{
    // Especifica la tabla asociada al modelo
    protected $table = 'data_preinvoice';

    // Indica los campos que se pueden asignar en masa (mass assignment)
    protected $fillable = [
        'patient_name',
        'worker_id',
        'policy_sort_no',
        'service_date',
        'case_date',
        'case_number',
        'service',
        'service_value',
        'result',
        'id_medical_bills',
        'id_policy_sorts',
        'type_currency'
    ];

    public function medicalBill()
    {
        return $this->belongsTo('App\MedicalBill', 'id_medical_bills');
    }

    public function policySort()
    {
        return $this->belongsTo(PolicySort::class, 'id_policy_sorts');
    }
}
