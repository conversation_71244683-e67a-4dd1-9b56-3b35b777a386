<?php

namespace App;
use Illuminate\Database\Eloquent\Model;

use App\States\StateGis;
use Illuminate\Support\Facades\DB;

class GisSort extends Model
{
    protected $table = 'gis_sort';

    protected $fillable = [
        'factor_conmutation',
        'type_identification_affiliate',
        'number_identification_affiliate',
        'name_affiliate',
        'nationality_affiliate',
        'cellphone_affiliate',
        'email_affiliate',
        'activity_id',
        'type_report',
        'date_accident',
        'hour_accident',
        'insured_case',
        'province',
        'canton',
        'district',
        'other_signs',
        'conditions',
        'date_death',
        'place_death',
        'where_died',
        'condicion_desaparicion',
        'date_demise',
        'place_disappeared',
        'date_demise',
        'accident_description',
        'ocurrencia',
        'material_agente',
        'mechanism_trauma',
        'third_party',
        'work_modality',
        'death_place',
        'accident_place',
        'external_cause',
        'external_cause_description',
        'first_aid',
        'auxilios_descripcion',
        'qualifier',
        'test_name',
        'sustentation',
        'doc_number',
        'event_type',
        'siniestro_category',
        'doc_date',
        'severity',
        'days_it',
        'qualification_date',
        'alto_costo',
        'mortal',
        'otro',
        'material_agente_otro',
        'forma_ocurrencia_otro',
        'mecanismo_trauma_otro',
        'date_disappearance',
        'another_place',
        'marital_status',
        'educational_level',
        'worker_address',
        'worker_iban_account_number',
        'follow_up_date',
        'cause_for_closure',
        'rehabilitation_detail',
        'functional_prognosis',
        'event_type_follow',
        'medical_center',
        'bmi_details',
        'ccss_details',
        'medical_center_description',
        'treatment_end_date',
        'expected_disability_days',
        'actuarial_severity_evaluacion',
        'diagnosis_description',
        'clinical_summary',
        'current_status',
        'recovery_possibility',
        'patient_prognosis',
        'pharmaceutical',
        'pharmaceutical_date',
        'treatment_purpose',
        'surgical',
        'surgical_option',
        'surgical_date',
        'fisica_option',
        'fisica_date',
        'occupational_therapy_option',
        'occupational_therapy_date',
        'fonoaudiology_option',
        'fonoaudiology_date',
        'other_option',
        'other_date',
        'authorization_date',
        'result_description',
        'has_closure',
        'closure_date',
        'requires_recommendations',
        'work_recommendations',
        'suspend_disability',
        'medical_discharge',
        'reason_medical_discharge',
        'reintegration_date',
        'employer_email_date',
        'requires_support',
        'requires_visit',
        'medical_discharge_reintegration',
        'type_tracking',
        'doc_type_witness',
        'doc_number_witness',
        'name_witness',
        'telefonos_witness',
        'date_qualification_f3',
        'date_high_f3',
        'job_recommendations_f3',
        'description_injuries_f3',
        'description_impediment_f3',
        'observations_f3',
        'cerebral_dominance_f3',
        'preexisting_medical_f3',
        'concauses_f3',
        'description_f3',
        'percentage_total_pcg_f3',
        'activity_at_accident',
        'witness_present',
        'witness_first_name',
        'witness_last_name',
        'is_work_related',
        'other_OptionEvalucion',
        'other_date_evaluacion',
        'according_the_affected_members',
        'according_the_assigned_percentages',
        'audit_result_calification_pcg',
        'total_pcg',
        'result_description_auditoria',
        'follow_up_type',
        'has_closure_rehabilitacion',
        'closure_date_rehabilitacion',
        'requires_recommendations_rehabilitacion',
        'work_recommendations_rehabilitacion',
        'suspend_disability_rehabilitacion',
        'medical_discharge_rehabilitacion',
        'reason_medical_discharge_rehabilitacion',
        'work_hours_hasta',
        'work_hours',
        'require_extra_information',
        'interested_party',
        'controversy_reason',
        'controversy_date',
        'path_file_medical',
        'path_file_medical',
        'path_file_forensic',
        'path_file_judicial',
        'occupancy_group',
        'name_comite',
        'controversy_result',
        'mnk_support_document',
        'mnk_observations',
        'symptoms_start_date',
        'symptoms_description',
        'ayuda',
        'agent_involved',
        'actuarial_severity',
        'pps_medical_benefits',
        'pps_temporary_disability',
        'pps_minor_permanent_disability',
        'pps_partial_permanent_disability',
        'pps_total_permanent_disability',
        'pps_great_disability_permanent_disability',
        'pps_death_or_disappearance',
        'provision_for_claim',
        'pps_expenses_recognition',
        'consecutive',
        'consecutive_gis',
        'model',
        'responsible_id',
        'report_reopening',
        'controversy_judicial_instance',
        'insured_updates',
        'requirement_submitted',
        'occupations_category_id',
        'disease_report_type_id'
    ];

    //DOCUMENTOS ASOCIADOS AL SERVICIO GIS
    public const DOCUMENTO_SOPORTE_1 = 244;
    public const DOCUMENTO_SOPORTE_2 = 245;
    public const DOCUMENTO_SOPORTE_3 = 246;
    public const DOCUMENTO_SOPORTE_4 = 247;
    public const DOCUMENTO_SOPORTE_5 = 248;

    protected $casts = [
        'insured_updates' => 'array',
    ];

    public static function boot()
    {
        parent::boot();

        static::creating(function ($gisSort) {
            $currentYear = date('Y');

            do {
                $nextConsecutive = DB::table('gis_sort')
                    ->where('consecutive', 'LIKE', "{$currentYear}-%")
                    ->selectRaw("COALESCE(MAX(CAST(SUBSTRING_INDEX(consecutive, '-', -1) AS UNSIGNED)), 0) + 1 AS next_consecutive")
                    ->value('next_consecutive');

                $consecutive = "{$currentYear}-" . str_pad($nextConsecutive, 4, '0', STR_PAD_LEFT);
            } while (DB::table('gis_sort')->where('consecutive', $consecutive)->exists());

            $gisSort->consecutive = $consecutive;

            do {
                $nextConsecutiveGis = DB::table('gis_sort')
                    ->selectRaw("COALESCE(MAX(CAST(SUBSTRING_INDEX(consecutive_gis, '-', -1) AS UNSIGNED)), 0) + 1 AS next_consecutive_gis")
                    ->value('next_consecutive_gis');

                $consecutiveGis = "AV-" . str_pad($nextConsecutiveGis, 4, '0', STR_PAD_LEFT);
            } while (DB::table('gis_sort')->where('consecutive_gis', $consecutiveGis)->exists());

            $gisSort->consecutive_gis = $consecutiveGis;
        });
    }


    public function activity()
    {
        return $this->belongsTo('App\Activity');
    }

    public function gis_body_parts()
    {
        return $this->hasMany(GisBodyPart::class, 'gis_id');
    }
    public function gis_subrogacion()
    {
        return $this->hasMany(GisSubrogacion::class, 'gis_id');
    }
    public function gis_request_authorizations()
    {
        return $this->hasMany(GisRequestAuthorization::class, 'gis_id');
    }
    public function gis_valoracion_medico_laboral()
    {
        return $this->hasMany(GisValoracionMedicoLaboral::class, 'gis_id');
    }
    public function gis_seguimiento_plan_tratamiento()
    {
        return $this->hasMany(GisSeguimientoPlanTratamiento::class, 'gis_id');
    }
    public function gis_diagnostics()
    {
        return $this->hasMany(GisDiagnostic::class, 'gis_id');
    }
    public function gis_origin_notifications()
    {
        return $this->hasMany(GisOriginNotification::class, 'gis_id');
    }


    public function get_state($id)
    {
        return (bool) medicalBenefitRequest::where('activity_id', $id)
            ->where('state', '!=', 'pending')
            ->exists();
    }

    public function formatCaseNumber()
    {

        if ($this->activity_id === null) {
            return '';
        }
        $count = ActivityAction::where('activity_id', $this->activity_id)->where('new_state_id', StateGis::CASO_REPORTADO_VALIDACION_ORIGEN)->count();

        if ($count>0) {
            return $this->consecutive;
        }else {
            return $this->consecutive_gis;
        }

    }

    public function formatCaseNumberIfReported()
    {

        if ($this->activity_id === null) {
            return '';
        }

        $count = ActivityAction::where('activity_id', $this->activity_id)->where('new_state_id', StateGis::CASO_REPORTADO_VALIDACION_ORIGEN)->count();

        return $count>0 ? $this->consecutive : '' ;

    }

    public function provider()
    {
        return Provider::where('id', $this->provider_id)->first();
    }


    public function formatCaseNumberLocal($id)
    {

        $gis = GisSort::where('id', $id)->first();

        if (!$gis) {
            return '';
        }

        $count = ActivityAction::where('activity_id', $gis->activity_id)->where('new_state_id', StateGis::CASO_REPORTADO_VALIDACION_ORIGEN)->count();

        if ($count>0) {
            return $gis->consecutive;
        }else {
            return $gis->consecutive_gis;
        }

    }
}
