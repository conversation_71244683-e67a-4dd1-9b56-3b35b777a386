<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use OwenIt\Auditing\Contracts\Auditable;

class InvalidityStatePfourCalificationDiagnostic extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable;
    //

    public function getDescriptionAttribute($value)
    {
        return mb_strtoupper($value, 'utf-8');
    }

    public function getCodeAttribute($value)
    {
        return mb_strtoupper($value, 'utf-8');
    }
}
