<?php

namespace App;

use App\LoggedModel;
use OwenIt\Auditing\Contracts\Auditable;
use Storage;

class Branch extends LoggedModel implements Auditable
{
    use \OwenIt\Auditing\Auditable;
    protected $fillable = [
        'employer_id',
        'name',
        'department',
        'municipality',
        'phone',
        'cellphone',
        'address',
        'email',
        'contact'
    ];
    //
    public function getCityAttribute()
    {
        $json = Storage::disk('public')->get('colombia.json');
        $json = json_decode($json);
        foreach ($json as $item) {
            if ($item->code == $this->department) {
                foreach ($item->municipalities as $muni) {
                    if ($muni->code == $this->municipality) {
                        return $muni->name . ' - ' . $item->name;
                    }

                }
            }

        }

        return null;
    }

    public function employer()
    {
        return $this->belongsTo('App\Employer');
    }
}
