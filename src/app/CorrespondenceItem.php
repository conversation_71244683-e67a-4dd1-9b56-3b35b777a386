<?php

namespace App;

use App\ActivityAction;
use App\ActivityActionField;
use App\AFP;
use App\ARL;
use App\Correspondence;
use App\EPS;
use App\Service;
use App\State;
use Auth;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Cache;
use Storage;
use App\Mail\SendDocument;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;

class CorrespondenceItem extends Model
{
    use SoftDeletes;

    public static $ID_USER_DEFAULT = 1;
    public static $ENTITY_TYPES = [
        "affiliate"  => "AFILIADO",
        "employer"   => "EMPLEADOR",
        "eps"        => "EPS",
        "arl"        => "ARL",
        "afp"        => "AFP",
        "others"     => "OTROS",
        'minsalud'   => "MINSALUD",
        'supersalud' => "SUPERSALUD",
        "jrci"       => "JUNTA",
        "secure"     => "ASEGURADORA",
    ];

    public static $EXCLUDE_DOC_NUMBER = 1020717690;

    protected $dates    = ['deleted_at', 'notified_at'];
    protected $fillable = ['state'];
    protected $appends  = ['department_name', 'municipality_name', 'created_at_formatted', 'entity_type_name'];

    public static function createFromActivityAction($client, $activityAction, $paths, $only = null, $id = null)
    {
        $action = $activityAction->action;
        if ($action->send_targets != null && $action->send_targets != '') {
            $activity = $activityAction->activity;
            $action   = $activityAction->action;

            if ($only == null) {
                foreach (explode(',', $action->send_targets) as $target) {
                    if ($target == 'employer' && (!$activity->employment || !$activity->employment->branch)) {
                        continue;
                    }
                    if ($target == 'arl' && ($activity->affiliate->arl == 0 || $activity->affiliate->arl == 33)) {
                        continue;
                    }
                    if ($target == 'afp' && $activity->affiliate->afp == 0) {
                        continue;
                    }
                    if ($target == 'secure' && ($activity->affiliate->afp == 0 || !isset(SEGURO::$LIST[$activity->affiliate->afp]) )) {
                        continue;
                    }

                    if ($id == null) {
                        $current = Correspondence::currentData($client->id);
                    } else {
                        $current = Correspondence::find($id);
                    }

                    $item                    = new CorrespondenceItem;
                    $item->correspondence_id = $current->id;
                    $item->order             = $current->counter++;
                    $item->entity_type       = $target;
                    if ($target == 'affiliate') {
                        $item->name         = $activity->affiliate->full_name;
                        $item->address      = $activity->affiliate->address;
                        $item->phone        = $activity->affiliate->phone . ' - ' . $activity->affiliate->cellphone;
                        $item->department   = $activity->affiliate->department;
                        $item->municipality = $activity->affiliate->municipality;
                    } else if ($target == 'employer') {
                        $item->name         = $activity->employment->employer->name;
                        $item->address      = $activity->employment->branch->address;
                        $item->phone        = $activity->employment->branch->phone . ' - ' . $activity->employment->branch->cellphone;
                        $item->department   = $activity->employment->branch->department;
                        $item->municipality = $activity->employment->branch->municipality;
                    } else if ($target == 'arl') {
                        if (Service::checkService($activity->service_id, Service::SERVICE_GLOSA)) {
                            $arl = ARL::$LIST[$activity->glosa->arl];

                            $arl = ARL::forceTo($activity->client_id, 5, $activity->glosa->arl, $arl, $activity->glosa->benefits);
                        } else if (Service::checkService($activity->service_id, Service::SERVICE_AT)) {
                            if ($activity->employment && $activity->employment->employer) {
                                $arl = ARL::$LIST[$activity->employment->employer->arl];
                                $arl = ARL::forceItem($activity->employment->employer->arl, $arl, $activity->client_id, $activity->affiliate->department);
                            } else {
                                $arl = ARL::$LIST[$activity->affiliate->arl];
                                $arl = ARL::forceItem($activity->affiliate->arl, $arl, $activity->client_id, $activity->affiliate->department);
                            }

                        } else {
                            $arl = ARL::$LIST[$activity->affiliate->arl];
                            $arl = ARL::forceItem($activity->affiliate->arl, $arl, $activity->client_id, $activity->affiliate->department);
                        }

                        $item->name         = $arl['name'];
                        $item->address      = $arl['address'];
                        $item->phone        = $arl['phone'];
                        $item->department   = $arl['department'];
                        $item->municipality = $arl['municipality'];
                    } else if ($target == 'afp') {
                        $afp = AFP::$LIST[$activity->affiliate->afp];
                        $afp = AFP::forceItem($activity->affiliate->afp, $afp, $activity->client_id, $activity->affiliate->department);

                        $item->name         = $afp['name'];
                        $item->address      = $afp['address'];
                        $item->phone        = $afp['phone'];
                        $item->department   = $afp['department'];
                        $item->municipality = $afp['municipality'];
                    } else if ($target == 'secure') {
                        $secure = SEGURO::$LIST[$activity->affiliate->afp];

                        $item->name         = $secure['name'];
                        $item->address      = $secure['address'];
                        $item->phone        = $secure['phone'];
                        $item->department   = $secure['department'];
                        $item->municipality = $secure['municipality'];
                    } else if ($target == 'eps') {
                        $eps = EPS::$LIST[$activity->affiliate->eps];

                        $item->name         = $eps['name'];
                        $item->address      = $eps['address'];
                        $item->phone        = $eps['phone'];
                        $item->department   = $eps['department'];
                        $item->municipality = $eps['municipality'];
                    } else if ($target == 'jrci') {
                        $jr = JR::$LIST[$activity->parent->dictum->controversy_regional_board];

                        $item->name         = $jr['name'];
                        $item->address      = $jr['address'];
                        $item->phone        = $jr['phone'];
                        $item->department   = $jr['department'];
                        $item->municipality = $jr['municipality'];
                    }
                    $item->activity_id        = $activity->id;
                    $item->activity_action_id = $activityAction->id;
                    $item->path               = implode(',', $paths);
                    if($activity->affiliate->doc_number == CorrespondenceItem::$EXCLUDE_DOC_NUMBER){
                        $item->state = 4;
                        $item->cause_of_return = 'ESTA ES UNA CUENTA DE PRUEBA';
                    }
                    $item->save();
                    $current->save();
                }
            } else {
                if ($only == 'employer' && (!$activity->employment || !$activity->employment->branch)) {
                    return;
                }
                if ($only == 'arl' && ($activity->affiliate->arl == 0 || $activity->affiliate->arl == 33)) {
                    return;
                }
                if ($only == 'afp' && $activity->affiliate->afp == 0) {
                    return;
                }
                if ($only == 'secure' && ($activity->affiliate->afp == 0 || !isset(SEGURO::$LIST[$activity->affiliate->afp]) )) {
                    return;
                }

                if ($id == null) {
                    $current = Correspondence::currentData($client->id);
                } else {
                    $current = Correspondence::find($id);
                }
                //$current                 = Correspondence::current($client);
                $item                    = new CorrespondenceItem;
                $item->correspondence_id = $current->id;
                $item->order             = $current->counter++;
                $item->entity_type       = $only;
                if ($only == 'affiliate') {
                    $item->name         = $activity->affiliate->full_name;
                    $item->address      = $activity->affiliate->address;
                    $item->phone        = $activity->affiliate->phone . ' - ' . $activity->affiliate->cellphone;
                    $item->department   = $activity->affiliate->department;
                    $item->municipality = $activity->affiliate->municipality;
                } else if ($only == 'employer') {
                    $item->name         = $activity->employment->employer->name;
                    $item->address      = $activity->employment->branch->address;
                    $item->phone        = $activity->employment->branch->phone . ' - ' . $activity->employment->branch->cellphone;
                    $item->department   = $activity->employment->branch->department;
                    $item->municipality = $activity->employment->branch->municipality;
                } else if ($only == 'arl') {
                    if (Service::checkService($activity->service_id, Service::SERVICE_GLOSA)) {
                        $arl = ARL::$LIST[$activity->glosa->arl];

                        $arl = ARL::forceTo($activity->client_id, 5, $activity->glosa->arl, $arl, $activity->glosa->benefits);
                    } else {
                        $arl = ARL::$LIST[$activity->affiliate->arl];
                        $arl = ARL::forceItem($activity->affiliate->arl, $arl, $activity->client_id, $activity->affiliate->department);
                    }

                    $item->name         = $arl['name'];
                    $item->address      = $arl['address'];
                    $item->phone        = $arl['phone'];
                    $item->department   = $arl['department'];
                    $item->municipality = $arl['municipality'];
                } else if ($only == 'afp') {
                    $afp = AFP::$LIST[$activity->affiliate->afp];
                    $afp = AFP::forceItem($activity->affiliate->afp, $afp, $activity->client_id, $activity->affiliate->department);

                    $item->name         = $afp['name'];
                    $item->address      = $afp['address'];
                    $item->phone        = $afp['phone'];
                    $item->department   = $afp['department'];
                    $item->municipality = $afp['municipality'];
                } else if ($only == 'secure') {
                    $secure = SEGURO::$LIST[$activity->affiliate->afp];

                    $item->name         = $secure['name'];
                    $item->address      = $secure['address'];
                    $item->phone        = $secure['phone'];
                    $item->department   = $secure['department'];
                    $item->municipality = $secure['municipality'];
                } else if ($only == 'eps') {
                    $eps = EPS::$LIST[$activity->affiliate->eps];

                    $item->name         = $eps['name'];
                    $item->address      = $eps['address'];
                    $item->phone        = $eps['phone'];
                    $item->department   = $eps['department'];
                    $item->municipality = $eps['municipality'];
                } else if ($only == 'jrci') {
                    $jr = JR::$LIST[$activity->parent->dictum->controversy_regional_board];

                    $item->name         = $jr['name'];
                    $item->address      = $jr['address'];
                    $item->phone        = $jr['phone'];
                    $item->department   = $jr['department'];
                    $item->municipality = $jr['municipality'];
                }
                $item->activity_id        = $activity->id;
                $item->activity_action_id = $activityAction->id;
                $item->path               = implode(',', $paths);
                if($activity->affiliate->doc_number == CorrespondenceItem::$EXCLUDE_DOC_NUMBER){
                    $item->state = 4;
                    $item->cause_of_return = 'ESTA ES UNA CUENTA DE PRUEBA';
                }
                $item->save();
                $current->save();

                return $item;
            }
        }
    }

    public function notify($notified_at, $description, $file_path, $alert_date = null)
    {
        $activity          = $this->activity;
        $this->state       = 3;
        $this->notified_at = $notified_at;

        if (Service::checkService($activity->service_id, Service::SERVICE_CONTROVERSY)
            || Service::checkService($activity->service_id, Service::SERVICE_ATEL)) {

            if ($this->entity_type == 'arl' && $activity->state_id == State::PENDING_ARL_NOTIFY) {
                if (Service::checkService($activity->service_id, Service::SERVICE_CONTROVERSY)) {
                    $dictum = $activity->parent->dictum;
                } else {
                    $dictum = $activity->dictum;
                }

                $dictum->setAttribute('controversy_arl_radication', $notified_at);
                $dictum->save();

                // ENTREGADO
                if (Service::checkService($activity->service_id, Service::SERVICE_CONTROVERSY)) {
                    $action = Action::find(Action::REPORT_NOTIFY_CONTROVERSY);
                } else {
                    $action = Action::find(Action::REPORT_NOTIFY);
                }

                $activityAction               = new ActivityAction;
                $activityAction->activity_id  = $activity->id;
                $activityAction->action_id    = $action->id;
                $activityAction->old_user_id  = $activity->user_id;
                $activityAction->new_user_id  = $activity->user_id;
                $activityAction->description  = $description;
                $activityAction->alert_date   = $alert_date;
                $activityAction->old_state_id = $activity->state_id;
                $activityAction->new_state_id = $action->getState($activity->service_id) ? $action->getStateId($activity->service_id) : $activity->state_id;
                $activityAction->author_id    = !!(Auth::id()) ? Auth::id() : self::$ID_USER_DEFAULT;;
                $activityAction->save();

                foreach ($action->fields as $field) {
                    if ($field->type == 'file') {
                        $activityActionField                     = new ActivityActionField;
                        $activityActionField->activity_action_id = $activityAction->id;
                        $activityActionField->action_field_id    = $field->id;
                        $activityActionField->value              = $file_path;
                        $activityActionField->save();
                    }
                    if ($field->type == 'date') {
                        $activityActionField                     = new ActivityActionField;
                        $activityActionField->activity_action_id = $activityAction->id;
                        $activityActionField->action_field_id    = $field->id;
                        $activityActionField->value              = $notified_at;
                        $activityActionField->save();
                    }
                }

                return $activityAction;

            }

            if ($this->entity_type == 'jrci' && $activity->state_id == State::PENDING_JRCI_NOTIFY) {
                if (Service::checkService($activity->service_id, Service::SERVICE_CONTROVERSY)) {
                    $dictum = $activity->parent->dictum;
                } else {
                    $dictum = $activity->dictum;
                }

                // ENTREGADO
                if (Service::checkService($activity->service_id, Service::SERVICE_CONTROVERSY)) {
                    $action = Action::find(Action::REPORT_NOTIFY_CONTROVERSY);
                } else {
                    $action = Action::find(Action::REPORT_NOTIFY);
                }

                $activityAction               = new ActivityAction;
                $activityAction->activity_id  = $activity->id;
                $activityAction->action_id    = $action->id;
                $activityAction->old_user_id  = $activity->user_id;
                $activityAction->new_user_id  = $activity->user_id;
                $activityAction->description  = $description;
                $activityAction->alert_date   = $alert_date;
                $activityAction->old_state_id = $activity->state_id;
                $activityAction->new_state_id = $action->getState($activity->service_id) ? $action->getStateId($activity->service_id) : $activity->state_id;
                $activityAction->author_id    = !!(Auth::id()) ? Auth::id() : self::$ID_USER_DEFAULT;;
                $activityAction->save();

                foreach ($action->fields as $field) {
                    if ($field->type == 'file') {
                        $activityActionField                     = new ActivityActionField;
                        $activityActionField->activity_action_id = $activityAction->id;
                        $activityActionField->action_field_id    = $field->id;
                        $activityActionField->value              = $file_path;
                        $activityActionField->save();
                    }
                    if ($field->type == 'date') {
                        $activityActionField                     = new ActivityActionField;
                        $activityActionField->activity_action_id = $activityAction->id;
                        $activityActionField->action_field_id    = $field->id;
                        $activityActionField->value              = $notified_at;
                        $activityActionField->save();
                    }
                }

                return $activityAction;

            }
        } else if (Service::checkService($activity->service_id, Service::SERVICE_PCL)) {
            if ($activity->state_id == 23) {

                // ENTREGADO
                $action = Action::find(Action::REPORT_NOTIFY);

                $activityAction               = new ActivityAction;
                $activityAction->activity_id  = $activity->id;
                $activityAction->action_id    = $action->id;
                $activityAction->old_user_id  = $activity->user_id;
                $activityAction->new_user_id  = $activity->user_id;
                $activityAction->description  = $description;
                $activityAction->alert_date   = $alert_date;
                $activityAction->old_state_id = $activity->state_id;
                $activityAction->new_state_id = $action->getState($activity->service_id) ? $action->getStateId($activity->service_id) : $activity->state_id;
                $activityAction->author_id    = !!(Auth::id()) ? Auth::id() : self::$ID_USER_DEFAULT;;
                $activityAction->save();

                foreach ($action->fields as $field) {
                    if ($field->type == 'file') {
                        $activityActionField                     = new ActivityActionField;
                        $activityActionField->activity_action_id = $activityAction->id;
                        $activityActionField->action_field_id    = $field->id;
                        $activityActionField->value              = $file_path;
                        $activityActionField->save();
                    }
                    if ($field->type == 'date') {
                        $activityActionField                     = new ActivityActionField;
                        $activityActionField->activity_action_id = $activityAction->id;
                        $activityActionField->action_field_id    = $field->id;
                        $activityActionField->value              = $notified_at;
                        $activityActionField->save();
                    }
                }

                return $activityAction;

            }
        } else if (Service::checkService($activity->service_id, Service::SERVICE_REHABILITATION)) {
            if ($this->entity_type == 'afp') {

                // ENTREGADO
                $action = Action::find(Action::REPORT_NOTIFY_REHABILITATION);

                $activityAction               = new ActivityAction;
                $activityAction->activity_id  = $activity->id;
                $activityAction->action_id    = $action->id;
                $activityAction->old_user_id  = $activity->user_id;
                $activityAction->new_user_id  = $activity->user_id;
                $activityAction->description  = $description;
                $activityAction->alert_date   = $alert_date;
                $activityAction->old_state_id = $activity->state_id;
                $activityAction->new_state_id = $action->getState($activity->service_id) ? $action->getStateId($activity->service_id) : $activity->state_id;
                $activityAction->author_id    = !!(Auth::id()) ? Auth::id() : self::$ID_USER_DEFAULT;;
                $activityAction->save();

                foreach ($action->fields as $field) {
                    if ($field->type == 'file') {
                        $activityActionField                     = new ActivityActionField;
                        $activityActionField->activity_action_id = $activityAction->id;
                        $activityActionField->action_field_id    = $field->id;
                        $activityActionField->value              = $file_path;
                        $activityActionField->save();
                    }
                    if ($field->type == 'date') {
                        $activityActionField                     = new ActivityActionField;
                        $activityActionField->activity_action_id = $activityAction->id;
                        $activityActionField->action_field_id    = $field->id;
                        $activityActionField->value              = $notified_at;
                        $activityActionField->save();
                    }
                }

                return $activityAction;

            }
        } else if (Service::checkService($activity->service_id, Service::SERVICE_RECOMMENDATIONS)) {
            $continue = false;
            if ($activity->employment && $activity->employment->employer) {
                if ($this->entity_type == 'employer') {
                    $continue = true;
                }

            } else if ($this->entity_type == 'affiliate') {
                $continue = true;
            }

            if ($continue) {

                $activity_action = $this->activity_action;
                $old_action = $activity_action->action;

                if($old_action->id != Action::REQUEST_CERTIFICATED_RECOMMENDATION){

                    // ENTREGADO
                    $action = Action::find(Action::REPORT_NOTIFY_RECOMMENDATIONS);

                    $activityAction               = new ActivityAction;
                    $activityAction->activity_id  = $activity->id;
                    $activityAction->action_id    = $action->id;
                    $activityAction->old_user_id  = $activity->user_id;
                    $activityAction->new_user_id  = $activity->user_id;
                    $activityAction->description  = $description;
                    $activityAction->alert_date   = $alert_date;
                    $activityAction->old_state_id = $activity->state_id;
                    $activityAction->new_state_id = $action->getState($activity->service_id) ? $action->getStateId($activity->service_id) : $activity->state_id;
                    $activityAction->author_id    = !!(Auth::id()) ? Auth::id() : self::$ID_USER_DEFAULT;;
                    $activityAction->save();

                    foreach ($action->fields as $field) {
                        if ($field->type == 'file') {
                            $activityActionField                     = new ActivityActionField;
                            $activityActionField->activity_action_id = $activityAction->id;
                            $activityActionField->action_field_id    = $field->id;
                            $activityActionField->value              = $file_path;
                            $activityActionField->save();
                        }
                        if ($field->type == 'date') {
                            $activityActionField                     = new ActivityActionField;
                            $activityActionField->activity_action_id = $activityAction->id;
                            $activityActionField->action_field_id    = $field->id;
                            $activityActionField->value              = $notified_at;
                            $activityActionField->save();
                        }
                    }

                    return $activityAction;
                }

            }
        } else if (Service::checkService($activity->service_id, Service::SERVICE_GLOSA)) {
            $continue = false;
            if ($this->entity_type == 'arl') {

                // ENTREGADO
                if ($activity->state_id == State::PENDING_ARL_NOTIFY_GLOSA) {
                    $action   = Action::find(Action::REPORT_NOTIFY_GLOSA);
                    $continue = true;
                } else if ($activity->state_id == State::PENDING_EPS_NOTIFY_GLOSA) {
                    $action   = Action::find(Action::REPORT_NOTIFY_EPS_GLOSA);
                    $continue = true;
                }

                if ($continue) {
                    $activityAction               = new ActivityAction;
                    $activityAction->activity_id  = $activity->id;
                    $activityAction->action_id    = $action->id;
                    $activityAction->old_user_id  = $activity->user_id;
                    $activityAction->new_user_id  = $activity->user_id;
                    $activityAction->description  = $description;
                    $activityAction->alert_date   = $alert_date;
                    $activityAction->old_state_id = $activity->state_id;
                    $activityAction->new_state_id = $action->getState($activity->service_id) ? $action->getStateId($activity->service_id) : $activity->state_id;
                    $activityAction->author_id    = !!(Auth::id()) ? Auth::id() : self::$ID_USER_DEFAULT;;
                    $activityAction->save();

                    foreach ($action->fields as $field) {
                        if ($field->type == 'file') {
                            $activityActionField                     = new ActivityActionField;
                            $activityActionField->activity_action_id = $activityAction->id;
                            $activityActionField->action_field_id    = $field->id;
                            $activityActionField->value              = $file_path;
                            $activityActionField->save();
                        }
                        if ($field->type == 'date') {
                            $activityActionField                     = new ActivityActionField;
                            $activityActionField->activity_action_id = $activityAction->id;
                            $activityActionField->action_field_id    = $field->id;
                            $activityActionField->value              = $notified_at;
                            $activityActionField->save();
                        }
                    }

                    return $activityAction;
                }

            }
        }

        // SEGUIMIENTO
        $action = Action::find(Action::FOLLOW);

        $activityAction               = new ActivityAction;
        $activityAction->activity_id  = $activity->id;
        $activityAction->action_id    = $action->id;
        $activityAction->old_user_id  = $activity->user_id;
        $activityAction->new_user_id  = $activity->user_id; // TODO: SPP
        $activityAction->description  = '<b>CORRESPONDENCIA:</b> [NOTIFICADO A ' . CorrespondenceItem::$ENTITY_TYPES[$this->entity_type] . ' el ' . $notified_at . '] ' . $description;
        $activityAction->alert_date   = $alert_date;
        $activityAction->old_state_id = $activity->state_id;
        $activityAction->new_state_id = $action->getStateId($activity->service_id) ? $action->getStateId($activity->service_id) : $activity->state_id;
        $activityAction->author_id    = !!(Auth::id()) ? Auth::id() : self::$ID_USER_DEFAULT;;
        $activityAction->save();

        foreach ($action->fields as $field) {
            if ($field->type == 'file') {
                $activityActionField                     = new ActivityActionField;
                $activityActionField->activity_action_id = $activityAction->id;
                $activityActionField->action_field_id    = $field->id;
                $activityActionField->value              = $file_path;
                $activityActionField->save();
            }
        }

        return $activityAction;
    }

    public function returny($description, $alert_date, $manual = NULL)
    {
        $action_id = Action::FOLLOW;
        $activity  = $this->activity;

        $this->state           = 4;
        $this->cause_of_return = $description;
        if($manual){
            $this->internal = 'X';
        }
        $this->save();

        switch ($activity->state_id) {
            case 14:
                if (Service::checkService($activity->service_id, Service::SERVICE_ATEL)) {
                    if ($this->entity_type == 'arl') {
                        $action_id = 32;
                    }

                } else if (Service::checkService($activity->service_id, Service::SERVICE_CONTROVERSY)) {
                    if ($this->entity_type == 'arl') {
                        $action_id = 72;
                    }

                }

                break;
            case 23:
                if (Service::checkService($activity->service_id, Service::SERVICE_PCL)) {
                    $action_id = 32;
                } else if (Service::checkService($activity->service_id, Service::SERVICE_RECOMMENDATIONS)) {
                    if ($activity->employment && $activity->employment->employer) {
                        if ($this->entity_type == 'employer') {
                            $action_id = 110;
                        }
                    } else if ($this->entity_type == 'affiliate') {
                        $action_id = 110;
                    }

                } else if (Service::checkService($activity->service_id, Service::SERVICE_REHABILITATION)) {
                    if ($this->entity_type == 'afp') {
                        $action_id = 97;
                    }

                }

                break;
            case 28: // CONTROVERSY JRCI
                if ($this->entity_type == 'jrci') {
                    $action_id = 71;
                }

                break;
            case 42: // REHABILITATION WITH PCL
                if ($this->entity_type == 'afp') {
                    $action_id = 98;
                }

                break;
            case 59: // GLOSA
                if ($this->entity_type == 'arl') {
                    $action_id = 137;
                }

                break;

            default:
                $action_id = Action::FOLLOW;
                break;
        }

        $action = Action::find($action_id);

        $activityAction               = new ActivityAction;
        $activityAction->activity_id  = $activity->id;
        $activityAction->action_id    = $action->id;
        $activityAction->old_user_id  = $activity->user_id;
        $activityAction->new_user_id  = 155; //WENDY HASBLEIDY VELANDIA PINEDA
        if($manual){
            $activityAction->description  = '<b>CORRESPONDENCIA:</b> [DEVOLUCIÓN INTERNA DEL ' . self::$ENTITY_TYPES[$this->entity_type] . '] ' . $description;
        }
        else{
            $activityAction->description  = '<b>CORRESPONDENCIA:</b> [DEVOLUCIÓN DEL ' . self::$ENTITY_TYPES[$this->entity_type] . '] ' . $description;
        }
        $activityAction->alert_date   = $alert_date;
        $activityAction->old_state_id = $activity->state_id;
        $activityAction->new_state_id = $action->getStateId($activity->service_id) ? $action->getStateId($activity->service_id) : $activity->state_id;
        $activityAction->author_id    = !!(Auth::id()) ? Auth::id() : self::$ID_USER_DEFAULT;
        $activityAction->save();

        return $activityAction;
    }

    public function activity()
    {
        return $this->belongsTo('App\Activity');
    }

    public function activity_action()
    {
        return $this->belongsTo('App\ActivityAction')->withTrashed();
    }

    public function correspondence()
    {
        return $this->belongsTo('App\Correspondence');
    }

    public function getEmailAttribute()
    {
        if ($this->entity_type == 'affiliate') {
            return $this->activity->affiliate->email;
        }

        if ($this->entity_type == 'employer') {
            return $this->activity->branch ? $this->activity->branch->email : $this->activity->affiliate->email;
        }

        return null;
    }

    public function getObservationAttribute()
    {
        if ($this->activity->dictum) {
            if ($this->activity->dictum->isLaboral()) {
                return 'LABORAL';
            } else if (!$this->activity->dictum->isLaboral()) {
                return 'COMUN';
            } else {
                return null;
            }
        }

        return null;
    }

    public function getCityAttribute()
    {
        $json = Cache::rememberForever('colombia', function () {
            return json_decode(Storage::disk('public')->get('colombia.json'));
        });
        foreach ($json as $item) {
            if ($item->code == $this->department) {
                foreach ($item->municipalities as $muni) {
                    if ($muni->code == $this->municipality) {
                        return $muni->name . ' - ' . $item->name;
                    }

                }
            }

        }

        return null;
    }

    public function getDepartmentNameAttribute()
    {
        $json_colombia = json_decode(Storage::disk('public')->get('colombia.json'), true);

        foreach ($json_colombia as $key => $value) {
            if ($value['code'] == $this->department) {
                return $value['name'];
            }
        }

        return null;
    }

    public function getMunicipalityNameAttribute()
    {
        // $json = Storage::disk('public')->get('colombia.json');
        // $json = json_decode($json);
        $json_colombia = json_decode(Storage::disk('public')->get('colombia.json'), true);
        foreach ($json_colombia as $key => $value) {
            if ($value['code'] == $this->department) {
                foreach ($value['municipalities'] as $muni) {
                    if ($muni['code'] == $this->municipality) {
                        return $muni['name'];
                    }

                }
            }

        }

        return null;
    }

    public function getCreatedAtFormattedAttribute()
    {
        return $this->created_at ? $this->created_at->formatLocalized('%d/%m/%Y') : null;
    }

    public function getEntityTypeNameAttribute()
    {
        return self::$ENTITY_TYPES[$this->entity_type];
    }

}
