<?php

namespace App;

use App\Actions\ActionPolizaSort;
use Illuminate\Database\Eloquent\Model;

class PolicySort extends Model
{
    protected $fillable = [
        'unique_code',
        'activity_id',
        'brokerage_name',
        'advisor_name',
        'code',
        'email',
        'doc_type',
        'doc_number',
        'first_name',
        'phones',
        'notification_email',
        'activity_economic_id',
        'economic_activity',
        'temporality',
        'validity_from',
        'validity_to',
        'type_currency',
        'periodicity',
        'salary_projection',
        'annual_calculation_amount',
        'semiannual_calculation_amount',
        'quarterly_calculation_amount',
        'monthly_calculation_amount',
        'single_payment_value',
        'start_date_validity_policy',
        'renewal_date',
        'payroll_status',
        'premium_value',
        'method_of_payment',
        'initial_payment_date',
        'legal_representative_name',
        'legal_representative_id',
        'legal_representative_profession',
        'calendar_period',
        'period_start_date',
        'period_end_date',
        'number_of_periods',
        'insurance_modality',
        'typeSignature',
        'accumulated_premium',
        'work_modality_id',
        'sign_policy_holder',
        'sign_intermediary',
        'amount_policy',
        'signed_document',
        'assurance',
        'date_suspend',
        'option_asegurement',
        'consecutive',
        "special_condition",
        "special_condition_payment",
        "preventive_actions",
        "balance_in_favor",
        "benefit_colective",
        "date_benefit_colective",
        "unfulfilled_conditions",
        "change_date",
        "number_workers_optional",
        "intermediary_updates",
        "taker_updates",
        "notification_email_additional",
        "legal_representative_occupancy_group_id",
        "legal_representative_occupations_category_id",
        "institutional_sector",
    ];

    protected $casts = [
        'intermediary_updates' => 'array',
        'taker_updates' => 'array',
    ];

    public function activity()
    {
        return $this->belongsTo('App\Activity');
    }

    public function policy_contacts()
    {

        return $this->hasMany('App\PolicyContact');
    }
    
    public function policy_calendars()
    {
        return $this->hasMany('App\PolicyCalendar');
    }

    public function formatSortNumber()
    {
        if ($this->consecutive === null) {
            return '';
        }

        if ($this->consecutive <= 9999) {
            return 'SORT-' . sprintf('%04d', $this->consecutive);
        }

        return 'SORT-' . $this->consecutive;
    }


    public function formatNumberConsecutive()
    {
        // Formatear el número para que tenga al menos 4 cifras con ceros a la izquierda
        return $this->consecutive ?   'SORT-' . sprintf('%04d', $this->consecutive) : '';
    }

    public function getFile()
    {
        $action = ActivityAction::where('activity_id', $this->activity_id)->where('action_id', ActionPolizaSort::EMITIR_POLIZA)->first();
        if (!$action) {
            return null;
        }
        $document = ActivityActionDocument::where('activity_action_id', $action->id)->first();
        if (!$document) {
            return null;
        }
        return $document->path;
    }

    public function sumTotalAffiliatesWithReport($policySortId)
    {
        // Buscamos la póliza por su ID
        $policy = self::find($policySortId);

        if (!$policy) {
            return 0; // Retorna 0 si no se encuentra la póliza
        }

        // Buscamos la actividad de la póliza
        $activityPolicy = Activity::find($policy->activity_id);

        // Buscamos la última actividad del reporte planilla tomador mediante la póliza
        $activityReport = Activity::where('parent_id', $policy->activity_id)
            ->where('service_id', Service::SERVICE_REPORT_TAKEN_FORM_MNK)
            ->latest()
            ->first();

        if (!$activityReport) {
            return 0; // Retorna 0 si no se encuentra la actividad
        }

        // Buscamos la planilla tomador mediante su actividad
        $lastReport = PolicySpreadsheet::where('activity_id', $activityReport->id)
            ->first();

        if (!$lastReport) {
            return 0; // Retorna 0 si no se encuentra la planilla
        }

        return $lastReport->total_salaries; // Retorna la suma de total_afiliados
    }

    public function calculatedAmount()
    {

        //monto de la prima
        if ($this->temporality == 'permanent'){
            switch ($this->periodicity) {
                case 1: // Anual
                    $calculatedAmount = $this->annual_calculation_amount * 1;
                    break;
                case 2: // Semestral
                    $calculatedAmount = $this->semiannual_calculation_amount * 2;
                    break;
                case 3: // Trimestral
                    $calculatedAmount = $this->quarterly_calculation_amount * 4;
                    break;
                case 4: // Mensual
                    $calculatedAmount = $this->monthly_calculation_amount * 12;
                    break;
                default:
                    $calculatedAmount = $this->annual_calculation_amount;
                    break;
            }
        } else {
            $calculatedAmount = $this->single_payment_value;
        }

        return $calculatedAmount ?? 0;
    }

    public function policyAddresses()
    {
        return $this->hasMany('App\PolicyAddress', 'policy_sort_id');
    }

    public function policyPhones()
    {
        return $this->hasMany('App\PolicyPhone', 'policy_sort_id');
    }

    public function policyAdditionalNotificationEmails()
    {
        return $this->hasMany('App\PolicyAdditionalNotificationEmail', 'policy_sort_id');
    }
}
