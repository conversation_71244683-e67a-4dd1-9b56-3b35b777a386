<?php

namespace App;
use Illuminate\Database\Eloquent\Model;

class MedicationServiceControlledMedication extends Model
{
    protected $fillable = [
        'generic_code',
        'generic_name',
        'concentration',
        'pharmaceutical_form',
        'molecula',
        'tipo',
        'descrip',
        'codigo',
        'casa',
        'treatment_duration',
        'frequency',
        'dosage',
        'quantity_letters',
        'quantity_numbers',
        'notes',
        'medication_service_sort_id',
    ];

    public function medication_service()
    {
        return $this->belongsTo('App\Medication', 'medication_service_sort_id');
    }
}
