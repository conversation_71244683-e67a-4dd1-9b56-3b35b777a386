<?php


namespace App;

use Illuminate\Database\Eloquent\Model;

class PremiumSurplusesLog extends Model
{

    protected $fillable = [
        'id',
        'premium_surplus_id',
        'taken_value',
        'charge_id',
        'previous_balance',
        'user_id'
    ];

    public function premium_surplus()
    {
        return $this->belongsTo('App\PremiumSurplus');
    }

    public function user()
    {
        return $this->belongsTo('App\User');
    }

}
