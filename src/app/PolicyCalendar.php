<?php

namespace App;

use DateTime;
use Illuminate\Database\Eloquent\Model;

class PolicyCalendar extends Model
{
    // Campos que se pueden rellenar con asignación masiva
    protected $fillable = [
        'start_date',
        'end_date',
        'number_period',
        'policy_sort_id',
        'created_at',
        'updated_at',
        'notify_date',
        'deadline_date'
    ];

    // Relación con la tabla policy_sorts 
    public function policySort()
    {
        return $this->belongsTo(PolicySort::class, 'policy_sort_id');
    }
}
