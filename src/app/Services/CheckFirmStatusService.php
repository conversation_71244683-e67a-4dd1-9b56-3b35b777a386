<?php

namespace App\Services;

use App\Activity;
use App\State;
use DB;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class CheckFirmStatusService
{

    public function verify()
    {
        Log::debug('Begin CheckFirmStatusService');

        $items     = array();
        $processed = 0;
        $last_id   = Cache::get('last_guide_firm_status', 0);

        $activities = Activity::where('state_id', '=', State::NOTIFIED_ARL)
                                ->orderBy('id', 'asc')
                                ->limit(50)->get();


        $total = count($activities);

        if ($total == 0) {
            Cache::put('last_guide_firm_status', 0, 60);
        }

        DB::beginTransaction();

        try {

            foreach ($activities as $activity) {

                if ($activity->state_id == State::NOTIFIED_ARL && $activity->dictum) {
                    $activity->dictum->checkFirmStatus();
                }
                $items[] = "https://aliansalud.renconsultores.co/servicio/{$activity->id}";

                $processed++;

                Cache::put('last_guide_firm_status', $activity->id, 60);
            }

            DB::commit();
            Log::debug('End CheckFirmStatusService');

        } catch (Exception $e) {
            DB::rollback();

            return array(
                'STATUS' => 'ERROR',
            );
        }
        Log::error('[CHECK FIRM STATUS]');
        return array(
            'STATUS'     => 'OK',
            'MESSAGE'    => "PROCESSED: {$processed} de {$total}",
            'ACTIVITIES' => $items,
        );
    }
}