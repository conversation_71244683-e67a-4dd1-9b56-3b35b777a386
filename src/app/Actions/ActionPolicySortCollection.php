<?php

namespace App\Actions;

class ActionPolicySortCollection
{
    const  REPORTAR_PAGO_EMISION_POLIZA_REALIZADO = 67;
    const APROBAR_PAGO_TB_RECIBO_REHABILITACION = 77;
    const REPORTAR_RECIBO_EMISION_POLIZA = 180;

    // PAGO TARJETA
    const REGISTRAR_PAGO_TARJETA = 379;
    const APROBAR_PAGO_TARJETA = 380;
    const RECHAZAR_PAGO_TARJETA = 381;
    const REGISTRAR_PAGO_TARJETA_RECIBO_ABONO_MENSUAL = 395;
    const APROBAR_PAGO_TARJETA_RECIBO_ABONO_MENSUAL = 396;
    const RECHAZAR_PAGO_TARJETA_RECIBO_ABONO_MENSUAL = 397;
    const REGISTRAR_PAGO_TARJETA_RECIBO_ABONO_TRIMESTRAL = 398;
    const APROBAR_PAGO_TARJETA_RECIBO_ABONO_TRIMESTRAL = 399;
    const RECHAZAR_PAGO_TARJETA_RECIBO_ABONO_TRIMESTRAL = 400;
    const REGISTRAR_PAGO_TARJETA_RECIBO_ABONO_SEMESTRAL = 408;
    const APROBAR_PAGO_TARJETA_RECIBO_ABONO_SEMESTRAL = 409;
    const RECHAZAR_PAGO_TARJETA_RECIBO_ABONO_SEMESTRAL = 410;
    const REGISTRAR_PAGO_TARJETA_RECIBO_REHABILITACION = 411;
    const APROBAR_PAGO_TARJETA_RECIBO_REHABILITACION = 412;
    const RECHAZAR_PAGO_TARJETA_RECIBO_REHABILITACION = 413;
    const REGISTRAR_PAGO_TARJETA_RECIBO_LIQUIDACION = 414;
    const APROBAR_PAGO_TARJETA_RECIBO_LIQUIDACION = 415;
    const RECHAZAR_PAGO_TARJETA_RECIBO_LIQUIDACION = 416;
    const REGISTRAR_PAGO_TARJETA_RECIBO_RENOVACION = 417;
    const APROBAR_PAGO_TARJETA_RECIBO_RENOVACION = 418;
    const RECHAZAR_PAGO_TARJETA_RECIBO_RENOVACION = 419;
    const REGISTRAR_PAGO_TARJETA_AUMENTO_DEL_SEGURO_PERIODO = 420;
    const APROBAR_PAGO_TARJETA_AUMENTO_DEL_SEGURO_PERIODO = 421;
    const RECHAZAR_PAGO_TARJETA_AUMENTO_DEL_SEGURO_PERIODO = 422;

    // TRANSFERENCIA BANCARIA
    const  REGISTRAR_PAGO_TB = 181;
    const  APROBAR_PAGO_TB = 183;
    const RECHAZAR_PAGO_TB = 184;
    const REGISTRAR_PAGO_TB_AUMENTO_SEGURO_PERIODO = 202;
    const REGISTRAR_PAGO_TB_RECIBO_ABONO_MENSUAL = 204;
    const REGISTRAR_PAGO_TB_RECIBO_LIQUIDACION = 209;
    const REGISTRAR_PAGO_TB_RECIBO_REHABILITACION = 207;
    const REGISTRAR_PAGO_TB_RECIBO_ABONO_SEMESTRAL = 205;
    const REGISTRAR_PAGO_TB_RECIBO_ABONO_TRIMESTRAL = 206;
    const REGISTRAR_PAGO_TC = 182;
    const REGISTRAR_PAGO_TC_AUMENTO_SEGURO_PERIODO = 203;
    const REGISTRAR_PAGO_TC_RECIBO_ABONO_MENSUAL = 217;
    const REGISTRAR_PAGO_TC_RECIBO_ABONO_SEMESTRAL = 218;
    const REGISTRAR_PAGO_TC_RECIBO_ABONO_TRIMESTRAL = 219;
    const REGISTRAR_PAGO_TC_RECIBO_REHABILITACION = 223;
    const REGISTRAR_PAGO_TC_RECIBO_LIQUIDACION = 224;
    const REGISTRA_PAGO_TB_RECIBO_ABONO_SEMESTRAL = 205;
    const REGISTRA_PAGO_TB_RECIBO_ABONO_TRIMESTRAL = 206;
    const APROBAR_PAGO_TB_AUMENTO_SEGURO = 210;
    const APROBAR_PAGO_TB_ABONO_MENSUAL = 216;
    const APROBAR_PAGO_TB_RECIBO_ABONO_SEMESTRAL = 220;
    const APROBAR_PAGO_TB_RECIBO_ABONO_TRIMESTRAL = 221;
    const APROBAR_PAGO_TB_RECIBO_LiQUIDACION = 226;
    const RECHAZAR_PAGO_TB_AUMENTO_SEGURO = 227;
    const RECHAZAR_PAGO_TB_ABONO_RECIBO_MENSUAL = 228;
    const RECHAZAR_PAGO_TB_RECIBO_ABONO_SEMESTRAL = 229;
    const RECHAZAR_PAGO_TB_RECIBO_ABONO_TRIMESTRAL = 230;
    const RECHAZAR_PAGO_TB_RECIBO_REHABILITACION = 231;
    const RECHAZAR_PAGO_TB_RECIBO_LIQUIDACION = 232;
    const REPORTAR_SEGUIMIENTO_COBROS = 261;

    const REGISTRAR_PAGO_TB_RECIBO_RENOVACION = 309;

    const RECHAZAR_PAGO_TB_RECIBO_RENOVACION = 325;

    const APROBAR_PAGO_TB_RECIBO_RENOVACION = 331;

    const REGISTRAR_PAGO_TC_RECIBO_RENOVACION = 332;

    const REPORTAR_RECIBO_RENOVACION_NO_PAGADO = 333;

    const REPORTAR_RECIBO_LIQUIDACION_NO_PAGADO = 334;

    const ANULAR_PAGO_POLIZA_SORT = 369;
    
    const REPORTAR_DEVOLUCION_AREA_SORT = 387;

    const REPORTAR_DEVOLUCIÓN_ÁREA_COBROS = 388;



}