<?php

namespace App;
use Illuminate\Database\Eloquent\Model;

class MedicalServiceSecondaryCareControlledMedication extends Model
{
    protected $fillable = [
        'generic_code',
        'generic_name',
        'concentration',
        'pharmaceutical_form',
        'treatment_duration',
        'frequency',
        'dosage',
        'quantity_letters',
        'quantity_numbers',
        'notes',
        'medical_services_sort_secondary_care_id',
        'medical_service_follow_up_id'
    ];

    public function medicalServiceSecondaryCare()
    {
        return $this->belongsTo('App\MedicalServicesSecondaryCareSort', 'medical_services_sort_secondary_care_id');
    }
}
