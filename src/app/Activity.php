<?php

namespace App;

use App\Action;
use App\Actions\ActionGisSort;
use App\Actions\ActionPolizaSort;
use App\States\StateGis;
use Carbon\Carbon;
use App\LoggedModel;
use DB;
use OwenIt\Auditing\Contracts\Auditable;

class Activity extends LoggedModel implements Auditable
{
    use \OwenIt\Auditing\Auditable;

    protected $dates = ['finished_at'];
    protected $fillable = [
        'parent_id',
        'client_id',
        'affiliate_id',
        'service_id',
        'state_id',
        'user_id',
        'policy_spreadsheet_id',
        'id_bizagi',
        'information_source',
        'creation_source',
        'lote_number',
        'actual_phase',
        'created_at',
        'activity_policy_sort_id',
    ];

    /**
     * The "booting" method of the model.
     *
     * @return void
     */
    protected static function boot()
    {
        parent::boot();

        static::created(function ($activity) {
            if ($activity->service_id == Service::SERVICE_POLICY_SORT_MNK) {
                $activity->activity_policy_sort_id = $activity->id;
                $activity->save();

                $activity->parent_activity()->update([
                    'activity_policy_sort_id' => $activity->id,
                ]);
            }
        });
    }

    public function sources()
    {
        return InformationSource::where('service_id', $this->service_id)
            ->where('client_id', $this->client_id)
            ->get();
    }

    public function source()
    {
        return $this->hasOne('App\InformationSource', 'id', 'information_source');
    }

    public function lastEmployerDocsRequest()
    {
        $last = $this->activity_actions()->where('action_id', Action::EMPLOYER_DOCS_REQUESTS)->latest()->first();
        return $last ? $last : $this;
    }

    public function lastAffiliateDocsRequest()
    {
        $last = $this->activity_actions()->where('action_id', Action::AFFILIATE_DOCS_REQUESTS)->latest()->first();
        return $last ? $last : $this;
    }


    public function getStateActionsAttribute()
    {
        $ids = [];
        foreach ($this->state->actions as $a) {
            $ids[] = $a->id;
        }

        $sactions = ServiceAction::where('service_id', $this->service_id)
            ->whereIn('action_id', $ids)
            ->get();

        $saids = [];
        foreach ($sactions as $sa) {
            $saids[] = $sa->action_id;
        }

        return Action::whereIn('id', $saids)->orderBy('name', 'asc')->get();
    }

    public function findDocPath($doc_name)
    {
        foreach ($this->activity_actions as $a) {
            foreach ($a->documents as $d) {
                if ($d->name == $doc_name) {
                    return $d->path;
                }
            }
        }

        return null;
    }

    public function findDocDate($action_id)
    {
        foreach ($this->activity_actions as $a) {
            if ($a->action_id == $action_id) {
                return $a->created_at->format('Y-m-d');
            }
        }

        return null;
    }

    public function findPclPath()
    {
        return $this->findDocPath('dictum_pcl');
    }

    public function findLecturePath()
    {
        return $this->findDocPath('lecture_pcl');
    }

    public function employment()
    {
        return $this->belongsTo('App\Employment');
    }

    public function affiliate()
    {
        return $this->belongsTo('App\Affiliate');
    }

    public function parent()
    {
        if ($this->parent_id && !$this->dictum) {
            return $this->belongsTo('App\Activity', 'parent_id');
        } else {
            return $this->belongsTo('App\Activity', 'id');
        }
    }
    public function getAncestorActivityByDepth($depth = 1)
    {
        $activity = $this;

        for ($i = 0; $i < $depth; $i++) {
            if ($activity->parent_activity) {
                $activity = $activity->parent_activity;
            } else {
                return null; // No se encontró el ancestro en ese nivel
            }
        }

        return $activity;
    }


    public function parent_activity()
    {
        return $this->belongsTo('App\Activity', 'parent_id');
    }

    public function children()
    {
        return $this->hasMany('App\Activity', 'parent_id');
    }


    public function service()
    {
        return $this->belongsTo('App\Service');
    }

    public function liquidation_sort()
    {
        return $this->belongsTo('App\LiquidationSorts','id','activity_id');
    }


    public function user()
    {
        return $this->belongsTo('App\User');
    }

    public function state()
    {
        return $this->belongsTo('App\State');
    }

    public function states()
    {
        return $this->hasMany('App\State', "id", "state_id");
    }

    public function client()
    {
        return $this->belongsTo('App\Client');
    }

    public function activity_actions()
    {
        return $this->hasMany('App\ActivityAction')->orderBy('created_at', 'desc')->orderBy('id', 'desc');
    }
    public function count_reopenings_activity_actions()
    {
        // Capturar actividad actual
        $activity = $this;

        // Navega hacia arriba hasta encontrar el servicio GIS
        while ($activity && $activity->parent_activity) {
            $activity = $activity->parent_activity;

            if ($activity->service_id == Service::SERVICE_GIS_SORT_MNK) {
                // Una vez encontrada, contamos las acciones
                return $activity->activity_actions()
                    ->where('action_id', ActionGisSort::REPORTAR_REAPERTURA_CASO)
                    ->count();
            }
        }

        // Si no encontró GIS, retornar 0
        return 0;
    }


    public function last_action_pcl_rejection()
    {
        return $this->hasMany('App\ActivityAction')->whereIn('action_id', [487, 538, 539, 540])->orderBy('created_at', 'desc')->limit(1);
    }
    public function activity_actions_it()
    {
        return $this->hasMany('App\ActivityAction')->orderBy('created_at', 'desc');
    }

    public function last_action()
    {
        return $this->hasOne('App\ActivityAction')->orderBy('created_at', 'desc');
    }

    public function plantilla()
    {
        return $this->hasOne('App\Template');
    }

    public function report_taken_form()
    {
        return $this->hasOne('App\ReportTakenForm');
    }

    public function affiliate_workforce_report()
    {
        return $this->hasOne('App\AffiliateWorkforceReport');
    }

    public function quotation()
    {
        return $this->hasOne('App\Quotation');
    }
    public function medication()
    {
        return $this->hasOne('App\Medication');
    }

    public function reintegrate()
    {
        return $this->hasOne('App\Reintegrate');
    }

    public function administrative_payment()
    {
        return $this->hasOne('App\AdministrativePayment');
    }

    public function policy_sort()
    {
        return $this->hasOne('App\PolicySort');
    }
    public function activity_documents()
    {
        return $this->hasMany('App\ActivityDocument');
    }

    public function variations_sort()
    {
        return $this->hasOne('App\VariationsSort');
    }

    public function medical_services_sort()
    {
        return $this->hasOne('App\MedicalServicesSort');
    }
    public function medical_services_secondary_care_sort()
    {
        return $this->hasOne('App\MedicalServicesSecondaryCareSort');
    }
    public function medical_bill()
    {
        return $this->hasOne('App\MedicalBill', 'activity_id');
    }
    public function gis_sort()
    {
        return $this->hasOne('App\GisSort');
    }

    public function premium_surplus()
    {
        return $this->hasOne('App\PremiumSurplus');
    }

    public function constancy_sort()
    {
        return $this->hasOne('App\ConstancySort');
    }

    public function policy_sort_collection()
    {
        return $this->hasOne('App\PolicySortCollection');
    }
    public function PeItSort()
    {
        return $this->hasOne('App\PeItSort');
    }

    public function pe_ip_sort()
    {
        return $this->hasOne('App\PeIpSort');
    }

    public function pe_mpt_sort()
    {
        return $this->hasOne('App\PeMptSort');
    }

    public function pe_recognition_expenses()
    {
        return $this->hasOne('App\PeRecognitionExpenses');
    }

    public function supplies_mot()
    {
        return $this->hasOne('App\SuppliesMot');
    }
    public function affiliate_payment()
    {
        return $this->hasOne('App\AffiliatePayment');
    }


    public function determination_it()
    {
        return $this->hasOne('App\DeterminationIt');
    }

    public function dictum_pcl_notification()
    {
        return $this->hasOne('App\DictumPclNotification');
    }

    public function it_historical()
    {
        return $this->hasOne('App\ItHistorical');
    }

    public function it_liquidation()
    {
        return $this->hasOne('App\ItLiquidation');
    }

    public function payment_bases()
    {
        return $this->hasOne('App\PaymentBase');
    }

    public function expression_disagreement()
    {
        return $this->hasOne('App\ExpressionDisagreement');
    }

    public function meeting()
    {
        return $this->hasOne('App\Meeting');
    }

    public function invalidity_state_pone()
    {
        return $this->hasOne('App\InvalidityStatePone');
    }

    public function invalidity_state_ptwo()
    {
        return $this->hasOne('App\InvalidityStatePtwo');
    }

    public function invalidity_state_pthree()
    {
        return $this->hasOne('App\InvalidityStatePthree');
    }


    // Nueva relacion
    public function policy_spreadsheets()
    {
        return $this->hasOne('App\PolicySpreadsheet');
    }


    public function invalidity_state_pfour()
    {
        return $this->hasOne('App\InvalidityStatePfour');
    }

    public function dictum_reception()
    {
        return $this->hasOne('App\DictumReception');
    }

    public function pcl()
    {
        return $this->hasOne('App\Pcl');
    }

    public function tutelage()
    {
        return $this->hasOne('App\Tutelage');
    }

    public function pqr()
    {
        return $this->hasOne('App\Pqr');
    }

    public function correspondences()
    {
        return $this->hasMany('App\CorrespondenceItem');
    }

    public function peipSorts()
    {
        return $this->hasMany('App\PeIpSort', 'activity_id');
    }
    public function PaymentSimulator()
    {
        return $this->hasOne('App\PaymentSimulator');
    }

    public function correspondencesGroups()
    {
        $data = array();
        $id = $this->id;

        $orders = Correspondence::join('correspondence_items', function ($join) use ($id) {
            $join->on('correspondences.id', '=', 'correspondence_items.correspondence_id')
                ->where('correspondence_items.activity_id', $id);
        })->select('correspondences.*')->distinct()->with('items')->get();

        foreach ($orders as $order) {
            if (!isset($data[$order->consecutive])) {
                $data[$order->consecutive] = array();
            }

            foreach ($order->items as $item) {
                if ($item->activity_id == $id) {
                    if (!isset($data[$order->consecutive][$item->activity_action_id])) {
                        $data[$order->consecutive][$item->activity_action_id] = array();
                    }

                    $data[$order->consecutive][$item->activity_action_id][] = $item;
                }
            }
        }

        return $data;
    }

    public function setField($fieldId, $value)
    {
        $field = $this->service_fields()->where('service_field_id', $fieldId)->first();

        if ($field) {
            $field->value = $value;
        } else {
            $field = new ActivityServiceField();
            $field->activity_id = $this->id;
            $field->service_field_id = $fieldId;
            $field->value = $value;
        }

        return $field->save();

    }

    public function fieldValue($fieldId)
    {
        $field = $this->service_fields()->where('service_field_id', $fieldId)->first();
        if ($field) {
            return $field->value;
        }

        return null;
    }

    public function service_fields()
    {
        return $this->hasMany('App\ActivityServiceField');
    }

    public function fieldsComplete()
    {
        foreach ($this->service->fields as $f) {
            if ($f->required) {
                if ($this->fieldValue($f->id) == null) {
                    return false;
                }

            }
        }

        return true;
    }

    public function allFieldsRenIPSComplete()
    {
        $active_positiva = false;
        foreach ($this->service->fields as $f) {
            if ($f->required) {
                if ($this->fieldValue($f->id) == null) {
                    return false;
                }
            }
            if ($this->fieldValue($f->id) == 'POSITIVA') {
                $active_positiva = true;
            }
        }
        if ($active_positiva) {
            foreach ($this->service->fields as $f) {
                if (!$f->required) {
                    if ($this->fieldValue($f->id) == null) {
                        return false;
                    }
                }
            }
        }

        return true;
    }

    public function documents()
    {
        return $this->hasMany('App\ActivityDocument')->orderBy(DB::raw('CASE WHEN document_id IS NULL THEN 1 ELSE 0 END, document_id'));
    }

    public function getDocuments($doc_id = -1)
    {
        if ($doc_id == -1) {
            return ActivityDocument::where('activity_id', $this->id)->whereNull('document_id')->get();
        } else {
            return ActivityDocument::where('activity_id', $this->id)->where('document_id', $doc_id)->get();
        }

    }

    public function getDocumentsWithoutIntegration($doc_id = -1)
    {
        if ($doc_id == -1) {
            return ActivityDocument::where('activity_id', $this->id)->whereNull('document_id')
                ->whereNull('is_integration')->get();
        } else {
            return ActivityDocument::where('activity_id', $this->id)->where('document_id', $doc_id)
                ->whereNull('is_integration')->get();
        }

    }

    public function getDocument($doc_id = -1)
    {
        if ($doc_id == -1) {
            return ActivityDocument::where('activity_id', $this->id)->whereNull('document_id')->first();
        } else {
            return ActivityDocument::where('activity_id', $this->id)->where('document_id', $doc_id)->first();
        }

    }

    public function missingDocuments($source = 'employer')
    {
        $result = [];
        if ($source == 'all') {
            $required_documents = $this->service->documents()->where('required', true)->get();
        } else {
            $required_documents = $this->service->documents()->where('source', $source)->where('required', true)->get();
        }

        foreach ($required_documents as $doc) {
            $activity_doc = ActivityDocument::where('activity_id', $this->id)
                ->where('document_id', $doc->id)
                ->first();

            if (!$activity_doc) {
                $result[] = $doc;
                continue;
            }

            if (!$activity_doc->path && !$activity_doc->include_in_others) {
                $result[] = $doc;
            }
        }

        return $result;
    }

    public function getSendTargets($data)
    {
        return $send = explode(',', $data[0]->activity_action->action->send_targets);
    }

    public function getSendTarget($data, $value)
    {
        foreach ($data as $d) {
            if ($d->entity_type == $value) {
                return $d;
            }
        }
        return null;
    }

    public function documentsCompleted()
    {
        $required_documents = $this->service->documents()->where('required', true)->get();
        foreach ($required_documents as $doc) {
            $activity_doc = ActivityDocument::where('activity_id', $this->id)
                ->where('document_id', $doc->id)
                ->first();

            if (!$activity_doc) {
                return false;
            }

            if (!$activity_doc->path && !$activity_doc->include_in_others) {
                return false;
            }
        }

        return true;
    }

    public function isTraslape()
    {
        $determination_it = null;
        if ($this->determination_it && !is_null($this->determination_it->start_date) && !is_null($this->determination_it->end_date)) {
            $determination_it = Activity::where('affiliate_id', $this->affiliate_id)
                ->leftJoin('determination_its', 'determination_its.activity_id', '=', 'activities.id')
                ->where('service_id', 53)
                ->where('activities.id', '<>', $this->id)
                ->where('determination_its.traslapable', '<>', 'X')
                ->whereNotNull('determination_its.start_date')
                ->whereNotNull('determination_its.end_date')
                ->where('activities.state_id', '<>', State::ANULATED_CASE)
                ->whereBetween('determination_its.end_date', [$this->determination_it->start_date, $this->determination_it->end_date])
                ->first();
        }

        return $determination_it ? true : false;
    }

    public function posibleProrroga()
    {
        $determination_it = null;
        if ($this->determination_it && !is_null($this->determination_it->start_date) && !is_null($this->determination_it->end_date)) {
            $determination_it = Activity::where('affiliate_id', $this->affiliate_id)
                ->leftJoin('determination_its', 'determination_its.activity_id', '=', 'activities.id')
                ->where('service_id', 53)
                ->where('activities.id', '<>', $this->id)
                ->where('determination_its.created_at', '<=', $this->created_at)
                ->whereNotNull('determination_its.start_date')
                ->whereNotNull('determination_its.end_date')
                ->where('activities.state_id', '<>', State::ANULATED_CASE)
                ->whereBetween('determination_its.end_date', [date('Y-m-d', strtotime("-30 days", strtotime($this->determination_it->start_date))), date('Y-m-d', strtotime($this->determination_it->start_date))])
                ->first();
        }

        return $determination_it ? true : false;
    }

    public function listProrrogas()
    {
        if ($this->determination_it && !is_null($this->determination_it->start_date) && !is_null($this->determination_it->end_date)) {
            $determination_its = Activity::where('affiliate_id', $this->affiliate_id)
                ->leftJoin('determination_its', 'determination_its.activity_id', '=', 'activities.id')
                ->where('service_id', 53)
                ->where('activities.id', '<>', $this->id)
                ->where('activities.state_id', '<>', State::ANULATED_CASE)
                ->whereBetween('determination_its.end_date', [date('Y-m-d', strtotime("-30 days", strtotime($this->determination_it->start_date))), date('Y-m-d', strtotime("+1 days", strtotime($this->determination_it->start_date)))])
                ->select('current_days', DB::raw("concat('Diag: ', (SELECT code FROM determination_it_diagnostics WHERE determination_it_id=determination_its.id ORDER BY id ASC LIMIT 1), ' desde el ', start_date, ' al ', end_date, ' No. Radicado: ', num_radicate) as text_description"))
                ->get()->toArray();

            return $determination_its;
        }

        return [];
    }

    public function getDateAttribute()
    {
        Carbon::setLocale('es');
        $date = $this->created_at->formatLocalized('%A %d de %B de %Y');
        return $date;
    }

    public function policy_sort_profile_affiliate()
    {
        // Obtener el ID de prestación médica desde la actividad actual
        $idMedicalServiceFromMedication = $this->parent_id;

        if (!$idMedicalServiceFromMedication) return '';

        // Buscar la actividad de prestación médica
        $medical_activity = Activity::where("id", $idMedicalServiceFromMedication)->first();

        if (!$medical_activity) return '';

        // Buscar la actividad de GIS
        $activity_gis = Activity::where("id", $medical_activity->parent_id)->first();

        if (!$activity_gis) return '';

        // Buscar la actividad de Policy Sort
        $policySortActivity = Activity::where("id", $activity_gis->parent_id)->first();

        // Retornar el ID de policy_sort si existe, de lo contrario, retornar cadena vacía
        return $policySortActivity->policy_sort ?? '';
    }

    public function gis_profile_affiliate()
    {
        // Obtener el ID de prestación médica desde la actividad actual
        $idMedicalServiceFromMedication = $this->parent_id;

        if (!$idMedicalServiceFromMedication) return '';

        // Buscar la actividad de prestación médica
        $medical_activity = Activity::where("id", $idMedicalServiceFromMedication)->first();

        if (!$medical_activity) return '';

        // Buscar la actividad de GIS
        $activity_gis = Activity::where("id", $medical_activity->parent_id)->first();

        if (!$activity_gis) return '';

        $gisTable = GisSort::where('activity_id', $activity_gis->id)->first();

        // Retornar el ID de policy_sort si existe, de lo contrario, retornar cadena vacía
        return $gisTable ?? '';
    }

    public function policy_sort_profile_affiliate_medical_service()
    {
        // Buscar la actividad de prestación médica
        $medical_activity = Activity::where("id", $this->id)->first();

        if (!$medical_activity) return '';

        // Buscar la actividad de GIS
        $activity_gis = Activity::where("id", $medical_activity->parent_id)->first();

        if (!$activity_gis) return '';

        // Buscar la actividad de Policy Sort
        $policySortActivity = Activity::where("id", $activity_gis->parent_id)->first();

        // Retornar el ID de policy_sort si existe, de lo contrario, retornar cadena vacía
        return $policySortActivity->policy_sort ?? '';
    }

    public function gis_profile_affiliate_medical_service()
    {
        // Buscar la actividad de prestación médica
        $medical_activity = Activity::where("id", $this->id)->first();

        if (!$medical_activity) return '';

        // Buscar la actividad de GIS
        $activity_gis = Activity::where("id", $medical_activity->parent_id)->first();

        if (!$activity_gis) return '';

        $gisTable = GisSort::where('activity_id', $activity_gis->id)->first();

        // Retornar el ID de policy_sort si existe, de lo contrario, retornar cadena vacía
        return $gisTable ?? '';
    }

    public function getFormattedPolicySortAuditMedic($depth = 1)
    {
        $activity = $this;

        // Navega a través de los niveles de parent_activity
        for ($i = 0; $i < $depth; $i++) {
            if ($activity->parent_activity) {
                $activity = $activity->parent_activity;
            } else {
                return 'No se encontró el número de póliza'; // Si algún nivel es null, retorna vacío
            }
        }

        // Devuelve el número de póliza formateado si existe, o vacío en caso contrario
        //return $activity->policy_sort ? ucwords(strtolower($activity->policy_sort->formatNumberConsecutive())) : 'No se encontró el número de póliza';
        return $activity->policy_sort ? $activity->policy_sort->formatNumberConsecutive() : 'No se encontró el número de póliza';
    }

    public function getAffiliateNameAuditMedic($depth = 1)
    {
        $activity = $this;

        // Navega a través de los niveles de parent_activity
        for ($i = 0; $i < $depth; $i++) {
            if ($activity->parent_activity) {
                $activity = $activity->parent_activity;
            } else {
                return 'Nombre de patrono no encontrado'; // Si algún nivel es null, retorna mensaje de error
            }
        }

        // Devuelve el nombre completo del affiliate si existe, o un mensaje si no se encuentra
        return $activity->affiliate && !empty($activity->affiliate->full_name)
            ? mb_convert_case(mb_strtolower($activity->affiliate->full_name, 'UTF-8'), MB_CASE_TITLE, 'UTF-8')
            : 'Nombre de patrono no encontrado';
    }

    public function getFormattedDateAccidentAuditMedic($depth = 1)
    {
        $activity = $this;

        // Navega a través de los niveles de parent_activity
        for ($i = 0; $i < $depth; $i++) {
            if ($activity->parent_activity) {
                $activity = $activity->parent_activity;
            } else {
                return 'Fecha no disponible'; // Retorna un mensaje si algún nivel es null
            }
        }

        // Verifica que gis_sort exista antes de intentar acceder a la fecha del accidente
        if ($activity->gis_sort && !empty($activity->gis_sort->id)) {
            return ucfirst(strftime('%A %e de %B del %Y', strtotime($activity->gis_sort->date_accident)));
        }

        return 'Fecha no disponible';
    }

    public function getCaseNumberAuditMedic($depth = 1)
    {
        $activity = $this;

        // Navega a través de los niveles de parent_activity
        for ($i = 0; $i < $depth; $i++) {
            if ($activity->parent_activity) {
                $activity = $activity->parent_activity;
            } else {
                return 'Número de caso no disponible'; // Retorna un mensaje si algún nivel es null
            }
        }

        // Verifica que gis_sort exista antes de intentar acceder a su id
        if ($activity->gis_sort && isset($activity->gis_sort->id)) {
//            if ($activity->gis_sort->id <= 9999) {
//                return sprintf('%04d', $activity->gis_sort->id);
//            }

            return $activity->gis_sort->formatCaseNumber();
        }

        return 'Número de caso no disponible';
    }

    public function getNumberAuditMedicAv($depth = 1)
    {
        $activity = $this;

        // Navega a través de los niveles de parent_activity
        for ($i = 0; $i < $depth; $i++) {
            if ($activity->parent_activity) {
                $activity = $activity->parent_activity;
            } else {
                return 'Número de aviso no disponible'; // Retorna un mensaje si algún nivel es null
            }
        }

        // Verifica que gis_sort exista antes de intentar acceder a su id
        if ($activity->gis_sort && isset($activity->gis_sort->id)) {
            return $activity->gis_sort->consecutive_gis;
        }

        return 'Número de aviso no disponible';
    }

    public function getNumberAuditMedicCase($depth = 1)
    {
        $activity = $this;

        for ($i = 0; $i < $depth; $i++) {
            if ($activity->parent_activity) {
                $activity = $activity->parent_activity;
            } else {
                return 'Número de caso no disponible'; // Retorna un mensaje si algún nivel es null
            }
        }

        if ($activity->gis_sort && isset($activity->gis_sort->id)) {

            $count = ActivityAction::where('activity_id', $activity->id)->where('new_state_id', StateGis::CASO_REPORTADO_VALIDACION_ORIGEN)->count();

            if ($count>0) {
                return $activity->gis_sort->consecutive;
            }

        }

        return '';

    }

    public function getAncestorGis()
    {
        $activity = $this;

        // Navega a través de los niveles de parent_activity hasta encontrar el servicio GIS
        while ($activity && $activity->parent_activity) {
            $activity = $activity->parent_activity;

            if ($activity->service_id == Service::SERVICE_GIS_SORT_MNK) {
                return $activity;
            }
        }

        return null;
    }

    public function validatePeitReopening()
    {
        $activity = $this;

        // Buscar todas las actividades hijas AP y AS
        $pms = Activity::where('parent_id', $activity->id)
            ->whereIn('service_id', [
                Service::SERVICE_MEDICAL_SERVICES_SORT_MNK,
                Service::SERVICE_MEDICAL_SERVICES_SECONDARY_CARE_SORT_MNK
            ])
            ->get();

        // Obtener los IDs de esas actividades
        $ids = $pms->pluck('id');

        if ($ids->isEmpty()) {
            return null;
        }

        // Buscar la primera incapacidad PEIT hija de cualquiera de esas
        return Activity::whereIn('parent_id', $ids)
            ->where('service_id', Service::SERVICE_PE_IT_SORT_MNK)
            ->orderBy('created_at', 'desc') // Ordenar por la más reciente
            ->pluck('id')
            ->first(); // Devuelve el ID de la más reciente
    }



    public function latestChildSpreadsheet()
    {
        return $this->hasOne(Activity::class, 'parent_id', 'id')
            ->where('service_id', Service::SERVICE_REPORT_TAKEN_FORM_MNK)
            ->latest('id');
    }

    // Obtener la última activity de pago de la poliza
    public function latestCollection()
    {

        return Activity::where("parent_id", $this->id)->where('service_id', Service::SERVICE_POLICY_SORT_COLLECTION_MNK)->latest()->first() ?? null;

//        return $this->hasOne(Activity::class, 'parent_id', 'id')
//            ->where('service_id', Service::SERVICE_POLICY_SORT_COLLECTION_MNK)
//            ->latest('id');
    }

    public function issuer_administrative_payments()
    {
        return $this->hasMany('App\IssuerAdministrativePayment')->orderBy('created_at', 'desc')->orderBy('id', 'desc');
    }

    public function last_issuer_administrative_payments()
    {
        $last = $this->issuer_administrative_payments()->latest()->first();
        return $last ? $last : $this;
    }

    public function socialSecurityProvider()
    {
        return $this->hasOne('App\SocialSecurityProvider', 'activity_id');
    }

    public function determinePeriodAffiliateWorkforce()
    {

        if (!Service::checkService($this->service_id, Service::SERVICE_AFFILIATE_WORKFORCE_REPORT_MNK)) {
            return '';
        }

        $period = '';

        $activityParent = !empty($this->parent_id) ? Activity::find($this->parent_id) : null;
        if (!$activityParent) {
            return $period;
        }

        $activityPolice = !empty($activityParent->parent_id) ? Activity::find($activityParent->parent_id) : null;
        if (!$activityPolice) {
            return $period;
        }

        $spreadsheetPeriods = Activity::where('parent_id', $activityPolice->id)
            ->where('service_id', Service::SERVICE_REPORT_TAKEN_FORM_MNK)
            ->get();

        $activity = $this;

        $position = $spreadsheetPeriods->search(function ($spreadsheet) use ($activity) {
            return $spreadsheet->id === $activity->parent_id;
        });

        if ($position === 0) {
            $period = "Emisión";
        } else {
            $period = ucfirst(Carbon::parse($activity->created_at)
                ->subMonth()
                ->formatLocalized('%B')) . " #" . $position;
        }

        return $period;
    }

    public function lastAction()
    {
        return $this->hasOne(ActivityAction::class)->orderBy('created_at', 'desc');
    }
    public function lastActionPolicyEmission()
    {
        return $this->hasOne(ActivityAction::class, 'activity_id', 'activity_policy_sort_id')
            ->where('action_id', ActionPolizaSort::EMITIR_POLIZA)
            ->orderBy('created_at', 'desc');
    }

}
