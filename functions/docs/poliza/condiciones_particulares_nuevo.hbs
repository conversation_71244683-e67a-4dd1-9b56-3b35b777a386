<!DOCTYPE html>

<head xmlns="http://www.w3.org/1999/html">
  <meta content="text/html; charset=utf-8" http-equiv="Content-Type" />
  <meta content="text/html; charset=utf-8" http-equiv="Content-Type" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <style>
    * {
      font-family: 'Arial', sans-serif;
      font-size: 8pt;
      box-sizing:
        border-box;
    }

    body {
      margin: 0;
      padding: 0;
      text-align: justify;
    }

    table,
    th,
    td {
      border: 0.5px solid black;
      padding: 4px;
      margin: 0;
      text-align: center;
      vertical-align: middle;
      /* Para centrar también verticalmente */
    }

    table.no-border,
    table.no-border th,
    table.no-border td,
    table.no-border td b {
      font-size: 7pt;
      border: none;
      text-align: center;
    }


    table {
      border-collapse: collapse;
      width: 100%;
    }

    .numpage:after {
      content: counter(page);
    }

    .td_paddin {
      padding-top: 10px;
      padding-bottom: 10px;
    }

    .fixed-width {
      width: 200px;
      /* Ajusta el tamaño según necesites */
      text-align: center;
      white-space: normal;
      /* Permite que el texto se divida en varias líneas */
    }

    .text_justi {
      text-align: justify;
    }

    .content_header {
      display: flex;
      flex-direction: row;
      align-content: center;
      justify-content: center;
      align-items: center;
    }

    .icon_style {
      display: flex;
      justify-content: end;
      width: 33%;
    }

    .footer {
        position: fixed;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 55px; /* o el valor que desees */
        z-index: 1000;
    }
    .footer img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .td_paddin {
      padding-top: 10px;
      padding-bottom: 10px;
    }

    .fixed-width {
      width: 200px;
      /*
      Ajusta el tamaño según necesites */
      text-align: center;
      white-space:
        normal;
      /* Permite que el texto se divida en varias líneas */
    }

    .text_justi {
      text-align: justify;
    }

    .icon_style {
      display: flex;
      justify-content: end;
      width: 33%;
    }

    .title-border {
      width: 400px;
      position: absolute;
      left: -23px;
      top: -10px;
    }

    .title_custom {
      background-color: #7EC81F;
    }

    .section-title {
      font-size: 2.2em;
      margin-top: 20px;
      color: #92C846;
      background-color: black;
      padding: 10px;
      text-align: center;
      font-family: 'Helvetica';
      font-weight: bold;
      border-radius: 10px;
    }
    .content-segment {
      padding: 10px;
      margin-top: 15px;
    }
    .contract-table {
      width: 100%;
      border-collapse: collapse;
      font-family: Arial, sans-serif;
      font-size: 14px;
      table-layout: auto;
      border: none
    }

    .contract-table td {
      padding: 2px 15px;
      vertical-align: middle;
      border: none
    }

    .contract-label {
        width: 30%;
        text-align: left;
        font-size: 1.2em;
        font-weight: bold;
        vertical-align: top;
    }

    .contract-input {
        display: inline-block;
        width: 100%;
        height: 30px;
        background-color: #e9e9e9;
        border-radius: 4px;
        text-align: center;
        padding-top: 5px;
        margin-top: 5px;
        box-sizing: border-box;
    }

    .contract-input-2 {
        display: inline-block;
        width: 100px;
        height: 30px;
        background-color: #e9e9e9;
        border-radius: 4px;
        text-align: center;
        padding-top: 5px;
        margin-top: 5px;
        box-sizing: border-box;
    }

    .contract-input-3 {
        display: inline-block;
        width: 30px;
        height: 30px;
        background-color: #e9e9e9;
        border-radius: 4px;
        text-align: center;
        padding-top: 5px;
        margin-left: 5px;
        box-sizing: border-box;
    }
    .contract-input-4 {
      display: inline-block;
      width: 90%;
      height: 30px;
      background-color: #e9e9e9;
      /* Simula los campos en gris */
      border-radius: 4px;
      box-sizing: border-box;
      text-align: center;
      padding-top: 5px;
      vertical-align: middle; /* alinea verticalmente con el texto */
    }
    .contract-input-5 {
        display: inline-block;
        width: 38.5%;
        height: 30px;
        background-color: #e9e9e9;
        /* Simula los campos en gris */
        border-radius: 4px;
        box-sizing: border-box;
        text-align: center;
        padding-top: 5px;
        vertical-align: middle; /* alinea verticalmente con el texto */
    }

    .contract-input-text {
      display: block;
      width: 100%;
      height: 120px;
      background-color: #e9e9e9;
      /* Simula los campos en gris */
      border-radius: 4px;
      box-sizing: border-box;
      text-align: left;
      padding-top: 1px;
    }
    form-table {
      border: none;
      width: 100%;
      border-collapse: collapse;
      font-family: Arial, sans-serif;
      font-size: 14px;
      table-layout: fixed;
      /* Para mantener las celdas del mismo tamaño */
    }

    .form-table td {
      border: none;
      padding: 2px 15px;

      width: 25%;
      /* Asegura que cada celda tenga el mismo ancho */
      vertical-align: middle;
    }
    .card {
      background-color: #f0f0f0;
      padding: 20px;
      border-radius: 5px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      margin-top: 20px;
    }
    .card-black {
      background-color: #000000;
      border-radius: 15px;
      width: 100%;
        height: 100%;
      color: #ffffff;
      /* Texto en blanco para mantener legibilidad */
    }

    .card-table {
      width: 100%;
      table-layout: fixed;
      /* Asegura un tamaño fijo para la tabla */
      border-spacing: 20px;
      /* Espaciado entre las imágenes */
    }
    .page-container {
      margin: 0 40px 40px 0; /* 40px en lados izquierdo, derecho e inferior */
    }


    /* Para mejorar la visibilidad en pantallas
      pequeñas */
    @media (max-width: 768px) {

      th,
      td {
        font-size: 0.9em;
        /*
      Ajusta el tamaño de fuente en pantallas pequeñas */
      }
    }
  </style>
</head>

<body>
  <div class="header">
    <script type="text/php">
      </script>
  </div>

  <table style="width: 100%; border: none; table-layout: fixed; font-size: 13px !important; margin-top: 30px !important;">
      <tbody>
      <tr>
          <!-- Celda del logo (más pequeña) -->
          <td style="width: 20%; border: none; text-align: left; vertical-align: top;">
              <img src="https://mnk-prod.s3.us-east-1.amazonaws.com/imagenes/mnk.png" alt="Logo" class="logo-image"
                   style="height: 180px; width: 200px; margin-top: -16%" />
          </td>

          <!-- Celda del texto (más larga) -->
          <td style="width: 80%; padding: 10px; border: none; text-align: right; vertical-align: top;">
              <div>
                  <div style="margin-bottom: 2px; font-weight: bold; font-size: 25px !important; color: #92C846;">
                      Condiciones particulares
                  </div>
                  <div style="font-size: 25px !important; font-weight: bold;">
                      Seguro Obligatorio de Riesgos del Trabajo
                  </div>
              </div>
          </td>
      </tr>
      </tbody>
  </table>
  <div class="section-title">Datos generales de la póliza</div>
  <br />
  <span class="ui segment content-segment">
      <table class="contract-table" border="1" cellpadding="5" cellspacing="0" style="border-collapse: collapse; width: 100%;">
          <tr>
              <td class="contract-label"><strong>Tipo de producto:</strong></td>
              <td><div class="contract-input">SORT</div></td>
          </tr>
          <tr>
              <td class="contract-label"><strong>Vigencia:</strong></td>
              <td style="text-align: left">
                  <span style="display: inline-block; vertical-align: middle;"> Desde:</span> &nbsp;&nbsp;&nbsp;&nbsp;
                  <span class="contract-input-5" style="display: inline-block; vertical-align: middle;">{{fromDate}}</span> &nbsp;&nbsp;&nbsp;
                  <span style="display: inline-block; vertical-align: middle;"> Hasta:</span> &nbsp;&nbsp;&nbsp;&nbsp;
                  <span class="contract-input-5" style="display: inline-block; vertical-align: middle;">{{toDate}}</span>
              </td>
          </tr>
          <tr>
              <td class="contract-label"></td>
              <td style="text-align: left">
                  <span style="display: inline-block; vertical-align: middle;"> <strong>Período:</strong></span>
                  <span class="contract-input-4" style="display: inline-block; vertical-align: middle;">{{textPeriodicity}}</span> &nbsp;&nbsp;
              </td>
          </tr>
          <tr>
              <td class="contract-label"><strong>Moneda:</strong></td>
              <td style="text-align: left;">
                  <span style="display: inline-block; vertical-align: middle;">Colones</span>
                  <span class="contract-input-3" style="display: inline-block; vertical-align: middle;">
                      {{#ifEquals currencyLabel "Colones"}}x{{/ifEquals}}
                  </span>&nbsp;&nbsp;

                  <span style="display: inline-block; vertical-align: middle;">Dólares</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                  <span class="contract-input-3" style="display: inline-block; vertical-align: middle;">
                      {{#ifEquals currencyLabel "Dólares"}}x{{/ifEquals}}
                  </span> &nbsp;&nbsp;
                  <span style="display: inline-block; vertical-align: middle;">Fecha de emisión:</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                  <span class="contract-input-5" style="display: inline-block; vertical-align: middle;">
                      {{currentDate}}
                  </span>

              </td>
          </tr>
          <tr>
              <td class="contract-label"><strong>Forma de pago:</strong></td>
              <td style="text-align: left">
                  <span style="display: inline-block; vertical-align: middle;">Anual</span>&nbsp;&nbsp;&nbsp;&nbsp;
                  <span class="contract-input-3" style="display: inline-block; vertical-align: middle;">
                      {{#ifEquals textPeriodicity "Anual"}}x{{/ifEquals}}
                  </span>&nbsp;&nbsp;

                  <span style="display: inline-block; vertical-align: middle;">Semestral</span>
                  <span class="contract-input-3" style="display: inline-block; vertical-align: middle;">
                      {{#ifEquals textPeriodicity "Semestral"}}x{{/ifEquals}}
                  </span>&nbsp;&nbsp;

                  <span style="display: inline-block; vertical-align: middle;">Trimestral</span>
                  <span class="contract-input-3" style="display: inline-block; vertical-align: middle;">
                      {{#ifEquals textPeriodicity "Trimestral"}}x{{/ifEquals}}
                  </span>&nbsp;&nbsp;

                  <span style="display: inline-block; vertical-align: middle;">Mensual</span>
                  <span class="contract-input-3" style="display: inline-block; vertical-align: middle;">
                      {{#ifEquals textPeriodicity "Mensual"}}x{{/ifEquals}}
                  </span>
              </td>
          </tr>
          <tr>
              <td class="contract-label"><strong>Número de póliza:</strong></td>
              <td><div class="contract-input">{{sortNumber}}</div></td>
          </tr>
          <tr>
              <td class="contract-label"><strong>Fecha de renovación:</strong></td>
              <td style="text-align: left"><div class="contract-input-5">{{toDateRenova}}</div>
                  <span style="display: inline-block; vertical-align: middle;">Tipo de movimiento:</span>
                      <span class="contract-input-5" style="display: inline-block; vertical-align: middle;">
                          Emisión
                      </span>
              </td>

          </tr>
      </table>
  </span>
  <div class="section-title">Datos del tomador</div>
  <br />
  <div class="ui segment content-segment">
    <table class="contract-table" border="1" cellpadding="5" cellspacing="0" style="border-collapse: collapse; width: 100%;">
      <tr>
        <td class="contract-label"><strong>Nombre completo:</strong></td>
        <td colspan="3">
          <div class="contract-input">{{full_name_word}}</div>
        </td>
      </tr>
      <tr>
        <td class="contract-label"><strong>Número de identificación:</strong></td>
        <td colspan="3">
          <div class="contract-input">{{affiliates.doc_number}}</div>
        </td>
      </tr>
      <tr>
        <td class="contract-label"><strong>Nombre del representante legal (razón jurídica):</strong></td>
        <td colspan="3">
          <div class="contract-input">{{ representante }}</div>
        </td>
      </tr>
    </table>
  </div>
  <div style="page-break-before: always;"></div>  {{!-- fuerza salto de página --}}
  <div class="section-title">Dirección exacta donde se desarrolla la actividad económica</div>
  <br />
  <div class="ui segment content-segment">
    <table class="contract-table" border="1" cellpadding="5" cellspacing="0" style="border-collapse: collapse; width: 100%;">
      <tr>
        <td class="contract-label"><strong>Provincia:</strong></td>
        <td colspan="3">
          <div class="contract-input">{{provincia}}</div>
        </td>
      </tr>
      <tr>
        <td class="contract-label"><strong>Cantón:</strong></td>
        <td colspan="3">
          <div class="contract-input">{{canto}}</div>
        </td>
      </tr>
      <tr>
        <td class="contract-label"><strong>Distrito:</strong></td>
        <td colspan="3">
          <div class="contract-input">{{distrito}}</div>
        </td>
      </tr>
      <tr>
        <td class="contract-label"><strong>Otras señas:</strong></td>
        <td colspan="3">
          <div class="contract-input">{{affiliates.employer_address}}</div>
        </td>
      </tr>
    </table>
  </div>
  <div class="section-title">Actividad económica</div>
  <br />
  <div class="ui segment content-segment">
    <div class="contract-input">{{policy_sorts.[0].activity_economic_id}} - {{nameEconomicActivity}}</div>
  </div>
  <div class="section-title">Cobertura</div>
  <br />
  <div class="ui segment content-segment card">
    <p>La cobertura de este seguro se establece en el artículo 5 ("Cobertura de riesgos
      del trabajo") de las condiciones generales de la póliza, entre lo que resalta:</p>
    <p>
      Todo patrono está obligado a asegurar a sus personas trabajadoras contra los
      riesgos del trabajo, tanto accidentes como enfermedades con ocasión al trabajo,
      según se establece en el Título IV del Código de Trabajo y su reglamento.
    </p>
    <p>
      Constituyen riesgos del trabajo los accidentes y las enfermedades que ocurran a
      las personas trabajadoras, con ocasión o por consecuencia del trabajo que
      desempeñen en forma subordinada y remunerada, así como la agravación o
      reagravación que resulte como consecuencia directa, inmediata e indudable de
      esos accidentes y enfermedades.
    </p>
  </div>
  <div class="section-title">Observaciones</div>
  <br />
  <div class="ui segment content-segment card">
    <p>Se excluyen de la cobertura de esta póliza los riesgos del trabajo que se
      produzcan en las circunstancias señaladas en el artículo 199 del Código de
      Trabajo.</p>
    <p>
      Este seguro está regulado por todo lo establecido en el Titulo IV del Código de
      Trabajo, su reglamento y la Norma Técnica del Seguro Obligatorio de Riesgos del
      Trabajo.
    </p>
  </div>
  <br/>
  <div style="page-break-before: always;"></div>  {{!-- fuerza salto de página --}}
  <div class="ui segment content-segment">
    <table class="contract-table" border="1" cellpadding="5" cellspacing="0" style="border-collapse: collapse; width: 100%;">
      <tr>
        <td class="contract-label"><strong>Corredor:</strong></td>
        <td colspan="3">
          <div class="contract-input">{{capitalizeEachWord policy_sorts.[0].advisor_name}}</div>
        </td>
      </tr>
      <tr>
        <td class="contract-label"><strong>Intermediario:</strong></td>
        <td colspan="3">
          <div class="contract-input">{{capitalizeEachWord policy_sorts.[0].brokerage_name}}</div>
        </td>
      </tr>
      <tr>
          <td class="contract-label"><strong>Prima neta:</strong></td>
          <td style="text-align: left">
                <span class="contract-input-5">{{currencyIcon  currency}}
                <strong>{{estimacion}}</strong></span>&nbsp;&nbsp;&nbsp;&nbsp;
              <span style="display: inline-block; vertical-align: middle;">Recargo por fraccionamiento:</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
              <span class="contract-input-2" style="display: inline-block; vertical-align: middle;">{{rFration}}%</span>
          </td>
      </tr>
        <tr>
            <td class="contract-label"><strong>Prima total:</strong></td>
            <td style="text-align: left">
                <span class="contract-input-5">{{currencyIcon  currency}}
                    <strong>{{total_amount}}</strong></span> &nbsp;&nbsp;&nbsp;&nbsp;
                <span style="display: inline-block; vertical-align: middle;">Tarifa aplicada:</span>&nbsp;&nbsp;&nbsp;
                <span class="contract-input-2" style="display: inline-block; vertical-align: middle;">{{tem}}%</span>
            </td>
        </tr>
    </table>
  </div>
  <div class="ui segment content-segment card">
    <p>La documentación contractual, la norma técnica y la nota técnica están
      registradas ante la Superintendencia General de Seguros de conformidad con lo
      dispuesto por el artículo 29, inciso d) de la Ley Reguladora del Mercado de Seguros,
      Ley 8653, bajo el registro número xxxx, de fecha 06 de noviembre del 2024.</p>
  </div>
  <br />
  <br />
  <div class="card-black">
    <table class="card-table">
      <tr>
        <td style="width: 50%;">
          <img src="https://mnk-prod.s3.us-east-1.amazonaws.com/imagenes/mnk_personas.png" alt="Grupo MNK" class="card-img" style=" width: 100%;">
        </td>
        <td>
          <img src="https://mnk-prod.s3.us-east-1.amazonaws.com/imagenes/mnk_white_simples.png" alt="Logo MNK"
               style="margin-left: 10%">
        </td>
      </tr>
    </table>
  </div>
  <footer class="footer">
    <img src="https://mnk-prod.s3.us-east-1.amazonaws.com/imagenes/mnk_footer.png" alt="pie de documento"
         style="width: 100%;" />
  </footer>
</body>
</html>