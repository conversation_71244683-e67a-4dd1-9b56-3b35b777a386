{"name": "crons", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"deploy-dev": "npm run lint && serverless deploy --stage dev", "deploy-qa": "npm run lint && serverless deploy --stage qa", "deploy-prod": "npm run lint && serverless deploy --stage prod", "package-dev": "serverless package --stage dev", "package-qa": "serverless package --stage qa", "package-prod": "serverless package --stage prod", "run-dev": "npm run lint && serverless offline start --stage dev", "run-qa": "npm run lint && serverless offline start --stage qa", "run-prod": "npm run lint && serverless offline start --stage prod", "generate": "npx prisma generate", "db-sync": "npx prisma db pull && prisma generate", "package": "npx prisma generate && serverless package", "lint": "npx eslint src", "postinstall": "npm run generate"}, "pre-commit": ["lint"], "keywords": [], "author": "", "license": "ISC", "dependencies": {"@aws-sdk/client-s3": "^3.632.0", "@aws-sdk/client-sqs": "^3.632.0", "@logtail/node": "^0.5.0", "@nestjs/config": "^3.1.1", "@nestjs/jwt": "^10.2.0", "@nestjs/mapped-types": "^2.0.2", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^10.0.0", "@nestjs/swagger": "^7.4.2", "@prisma/client": "^6.0.1", "@serverless/sdk": "^0.5.26", "@slack/web-api": "^7.0.4", "@sparticuz/chromium": "^131.0.1", "aws-sdk": "^2.1691.0", "axios": "^1.7.7", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "cors": "^2.8.5", "exceljs": "^4.4.0", "express": "^4.21.0", "express-basic-auth": "^1.2.1", "express-handlebars": "^8.0.1", "handlebars": "^4.7.8", "jsonwebtoken": "^9.0.2", "moment": "^2.30.1", "moment-timezone": "^0.5.48", "nodemailer": "^6.9.14", "passport": "^0.7.0", "passport-http": "^0.3.0", "passport-jwt": "^4.0.1", "pdf-lib": "^1.17.1", "puppeteer": "^24.1.0", "puppeteer-core": "^10.4.0", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "serverless-http": "^3.2.0", "soap": "^1.1.6", "soap-server-lambda": "^1.0.0", "sql-formatter": "^15.4.6", "swagger-ui-express": "^5.0.1", "winston": "^3.14.2", "winston-transport": "^4.8.0", "xml2js": "^0.6.2", "zod": "^3.23.8"}, "devDependencies": {"@cosva-lab/serverless-esbuild-prisma": "^1.1.2", "@eslint/js": "^9.9.0", "@nestjs/cli": "^10.4.5", "@nestjs/common": "^10.4.4", "@nestjs/core": "^10.4.4", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/aws-lambda": "^8.10.143", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/handlebars": "^4.0.40", "@types/jest": "^29.5.12", "@types/jsonwebtoken": "^9.0.6", "@types/node": "^22.2.0", "@types/nodemailer": "^6.4.15", "@types/passport-http": "^0.3.11", "@types/passport-jwt": "^4.0.1", "@types/supertest": "^6.0.0", "@types/xml2js": "^0.4.14", "@typescript-eslint/eslint-plugin": "^8.8.0", "@typescript-eslint/parser": "^8.8.0", "dotenv": "^16.4.5", "esbuild": "^0.13.0", "esbuild-loader": "^4.2.2", "esbuild-plugin-ts-decorators": "^1.0.3", "esbuild-plugin-typescript-decorators": "^0.1.0", "eslint": "^9.12.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.2", "globals": "^15.9.0", "jest": "^29.7.0", "pre-commit": "^1.2.2", "prettier": "^3.4.2", "prisma": "^6.0.1", "serverless": "^3.38.0", "serverless-bundle": "^6.1.0", "serverless-dotenv-plugin": "^6.0.0", "serverless-esbuild": "^1.54.3", "serverless-offline": "^13.8.0", "serverless-webpack": "^5.14.1", "serverless-webpack-prisma": "^1.2.0", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.4", "ts-loader": "^9.5.1", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^4.9.5", "typescript-eslint": "^8.1.0", "webpack": "^5.93.0"}}