service: functions
frameworkVersion: '3'

plugins:
  - serverless-dotenv-plugin
  - serverless-esbuild
  - '@cosva-lab/serverless-esbuild-prisma'
  - serverless-offline

useDotenv: true

provider:
  name: aws
  runtime: nodejs20.x
  region: us-east-1
  stage: ${opt:stage,'dev'}
  timeout: 60
  memorySize: 128
  architecture: x86_64
  versionFunctions: false
  vpc:
    securityGroupIds:
      - ${self:custom.securityGroups.${self:provider.stage}}
    subnetIds:
      - subnet-0b08f2141916400a0
      - subnet-04b70de7ac044422b
  iam:
    role:
      Fn::GetAtt:
        - LambdaExecutionRole
        - Arn
  environment:
    API_URL: ${env:API_URL}
    SQS_REPORTES: "https://sqs.us-east-1.amazonaws.com/120569611095/ReportsQueue-${opt:stage,'dev'}"
    SQS_PLANILLA: "https://sqs.us-east-1.amazonaws.com/120569611095/PlanillaQueue-${opt:stage,'dev'}"
    SQS_PLANILLA_CERTIFICADO: "https://sqs.us-east-1.amazonaws.com/120569611095/PlanillaCertificadoQueue-${opt:stage,'dev'}"
    SQS_CODIGO_UNICO_CONTACTO: "https://sqs.us-east-1.amazonaws.com/120569611095/CodigoUnicoContactoQueue-${opt:stage,'dev'}"
    SQS_PLANILLA_DEFINITIVA: "https://sqs.us-east-1.amazonaws.com/120569611095/PlanillaDefinitivaQueue-${opt:stage,'dev'}"
    SQS_EMISION_POLICE: "https://sqs.us-east-1.amazonaws.com/120569611095/EmisionPoliceQueue-${opt:stage,'dev'}"
    SQS_COMMUNICATION_POLICE: "https://sqs.us-east-1.amazonaws.com/120569611095/CommunicationPoliceQueue-${opt:stage,'dev'}"
    SQS_REEMPLAZAR_CERTIFICADO_PLANILLA: "https://sqs.us-east-1.amazonaws.com/120569611095/ReemplazarCertificadoPlanillaQueue-${opt:stage,'dev'}"
    SQS_CARGAR_TXT: "https://sqs.us-east-1.amazonaws.com/120569611095/CargarTxtQueue-${opt:stage,'dev'}"
    CLIENT_ID: ${self:custom.clients.${self:provider.stage}}
    DATABASE_URL: ${env:DATABASE_URL}
    MAIL_HOST: ${env:MAIL_HOST}
    MAIL_PORT: ${env:MAIL_PORT}
    MAIL_FROM_ADDRESS: ${env:MAIL_FROM_ADDRESS}
    MAIL_FROM_NAME: ${env:MAIL_FROM_NAME}
    MAIL_PASSWORD: ${env:MAIL_PASSWORD}
    MAIL_USERNAME: ${env:MAIL_USERNAME}
    SLACK_TOKEN: ${env:SLACK_TOKEN}
    ENVIRONMENT: ${opt:stage,'dev'}
    JWT_SECRET_AIVO: ${env:JWT_SECRET_AIVO}
    JWT_SECRET_RENAPP: ${env:JWT_SECRET_RENAPP}
    JWT_SECRET_DOCMEDICA: ${env:JWT_SECRET_DOCMEDICA}
    ACSEL_USERNAME: ${env:ACSEL_USERNAME}
    ACSEL_PASSWORD: ${env:ACSEL_PASSWORD}
    ACSEL_PORT: ${env:ACSEL_PORT}
    AUTHORIZATION_CREDID: ${env.AUTHORIZATION_CREDID}
    ARCHIVADOR_USERNAME: ${env:ARCHIVADOR_USERNAME}
    ARCHIVADOR_PASSWORD: ${env:ARCHIVADOR_PASSWORD}
    SOAP_USERNAME: RenappMnk
    SOAP_PASSWORD: RenappMnk2024*
    MAIL_EVICERTICA_HOST: ${env:MAIL_EVICERTICA_HOST}
    MAIL_EVICERTICA_PORT: ${env:MAIL_EVICERTICA_PORT}
    MAIL_EVICERTICA_USERNAME: ${env:MAIL_EVICERTICA_USERNAME}
    MAIL_EVICERTICA_PASSWORD: ${env:MAIL_EVICERTICA_PASSWORD}
    MAIL_EVICERTICA_ENCRYPTION: ${env:MAIL_EVICERTICA_ENCRYPTION}
    BUCKET_NAME: mnk-${opt:stage,'dev'}
    URL_ENV : ${env:URL_ENV}
    URL_FACT_ELETRONICA: ${env:URL_FACT_ELETRONICA}
    USER_ARCHIVED_VISION: ${env:USER_ARCHIVED_VISION}
    PASSWORD_ARCHIVED_VISION: ${env:PASSWORD_ARCHIVED_VISION}
    FOLDER_NAME_ARCHIVED: ${env:FOLDER_NAME_ARCHIVED}

package:
  individually: true
  patterns:
    - assets/**
    - docs/**
    - src/**/*.wsdl
custom:
  stages:
    - dev
    - qa
    - prod
  securityGroups:
    dev: sg-0834784f7b3e23cf2
    qa: sg-094456ddf058ebb77
    prod: sg-0d626ed84d3c87890
  clients:
    dev: 2
    qa: 3
    prod: 1
  prisma:
    installDeps: false
    ignoreFunctions:
      - generar_pdf
  esbuild:
    bundle: true
    minify: false
    plugins: esbuild-plugins.js
    external:
      - "@nestjs/websockets"
      - "@nestjs/microservices"
      - "class-transformer/storage"
      - puppeteer
      - "@sparticuz/chromium"
  serverless-offline:
    httpPort: 4000
    noPrependStageInUrl: true

resources:
  - ${file(src/reports/resources.yml)}
  - ${file(src/planilla_afiliados/resources.yml)}
  - ${file(src/resources/lambda-role.yml)}
  - ${file(src/resources/api-domain.yml)}
  - ${file(src/codigo_unico_contacto/resources.yml)}
  - ${file(src/queue/cierre_planilla_queue/resources.yml)}
  - ${file(src/queue/emision_police_queue/resources.yml)}
  - ${file(src/queue/communication_police_queue/resources.yml)}
  - ${file(src/queue/affiliate/certificate_affiliate_queue/resources.yml)}
  - ${file(src/queue/affiliate/affiliate_send_masive_queue/resources.yml)}
  - ${file(src/queue/cargue_planilla/planilla_txt_queue/resources.yml)}
  - ${file(src/reemplazar_certificado_planilla/resources.yml)}

functions:
  - ${file(src/not_found/sls.yml)}
  - ${file(src/reports/sls.yml)}
  - ${file(src/planilla_afiliados/sls.yml)}
  - ${file(src/planilla_afiliados/api.yml)}
  - ${file(src/planilla_definitiva/api.yml)}
  - ${file(src/codigo_unico_contacto/sls.yml)}
  - ${file(src/codigo_unico_contacto/api.yml)}
  - ${file(src/queue/emision_police_queue/sls.yml)}
  - ${file(src/queue/communication_police_queue/sls.yml)}
  - ${file(src/queue/affiliate/certificate_affiliate_queue/sls.yml)}
  - ${file(src/queue/affiliate/affiliate_send_masive_queue/sls.yml)}
  - ${file(src/queue/cargue_planilla/planilla_txt_queue/sls.yml)}
  - ${file(src/queue/cierre_planilla_queue/sls.yml)}
  - ${file(src/reemplazar_certificado_planilla/sls.yml)}
  - ${file(src/reemplazar_certificado_planilla/api.yml)}
  #  APIS
  - ${file(src/apis/acsel/sls.yml)}
  - ${file(src/apis/aivo/sls.yml)}
  - ${file(src/apis/credid/sls.yml)}
  - ${file(src/apis/archivador/sls.yml)}
  - ${file(src/apis/renapp_soap/sls.yml)}
  - ${file(src/apis/renapp_api/sls.yml)}
  - ${file(src/apis/reporte_medico/sls.yml)}
  - ${file(src/apis/factura_eletronica/sls.yml)}
  - ${file(src/apis/generar_pdf/sls.yml)}
  # CRON
  - ${file(src/crons/validate_caducado/sls.yml)}
  - ${file(src/crons/insurance_increase_unpaid/sls.yml)}
  - ${file(src/crons/monthly_no_payment/sls.yml)}
  - ${file(src/crons/quarterly_no_payment/sls.yml)}
  - ${file(src/crons/generate_liquidation_policy/sls.yml)}
  - ${file(src/crons/generate_renewal_sort/sls.yml)}
  - ${file(src/crons/report_unpaid_renewal_receipt/sls.yml)}
  - ${file(src/crons/report_unpaid_settlement_receipt/sls.yml)}
  - ${file(src/crons/issue_expiration_notice/sls.yml)}
  - ${file(src/crons/biannual_no_payment/sls.yml)}
  - ${file(src/crons/rehabilitation_no_payment/sls.yml)}
  - ${file(src/crons/report_rejection_policy_variation/sls.yml)}
  - ${file(src/crons/report_final_payroll/sls.yml)}
  - ${file(src/crons/report_final_monthly_payroll/sls.yml)}
  - ${file(src/crons/generate_fraction_payment_report/sls.yml)}
  - ${file(src/crons/formal_case_format_closure/sls.yml)}
  - ${file(src/crons/closure_case_gis/sls.yml)}
  - ${file(src/crons/update_invoice_status/sls.yml)}
  - ${file(src/crons/send_archived_documents/sls.yml)}
  - ${file(src/crons/report_account_case_308/sls.yml)}
  - ${file(src/crons/report_account_case_305/sls.yml)}
  - ${file(src/crons/report_account_case_150/sls.yml)}
  - ${file(src/crons/report_account_case_301/sls.yml)}
  - ${file(src/crons/report_account_case_306/sls.yml)}
  - ${file(src/crons/report_account_case_307/sls.yml)}
  - ${file(src/crons/desactivate_providers/sls.yml)}
  - ${file(src/crons/send_survey_gis/sls.yml)}
  - ${file(src/crons/monthly_account_statement/sls.yml)}
  - ${file(src/crons/quarterly_account_statement/sls.yml)}
  - ${file(src/crons/semmiannual_account_statement/sls.yml)}
  - ${file(src/crons/report_account_case_087/sls.yml)}
  - ${file(src/crons/generate_default_spreadsheet/sls.yml)}
  - ${file(src/crons/cron_notify_spreadsheet_upload_5th_business_day/sls.yml)}
  - ${file(src/crons/report_policy_collections/sls.yml)}
