SELECT
  `ea`.`group_economic` AS `grupo_actividad`,
(
    CASE
      WHEN (`oc`.`group_id` IS NULL) THEN '0'
      ELSE `oc`.`group_id`
    END
  ) AS `grupo_ocupacion`,
  sum(
    (
      CASE
        WHEN (`psa`.`gender` IN ('F', 'M')) THEN 1
        ELSE 0
      END
    )
  ) AS `total`,
  sum(
    (
      CASE
        WHEN (`psa`.`gender` = 'M') THEN 1
        ELSE 0
      END
    )
  ) AS `total_hombres`,
  sum(
    (
      CASE
        WHEN (`psa`.`gender` = 'F') THEN 1
        ELSE 0
      END
    )
  ) AS `total_mujeres`,
  year(`g`.`created_at`) AS `ano`,
  'GruposOcupacionales' AS `type`,
  GROUP_CONCAT(`a`.`id` SEPARATOR ', ') AS `activities_gis`,
  1 AS `id`
FROM
  (
    (
      (
        (
          (
            (
              (
                (
                  `ebdb`.`gis_sort` `g`
                  LEFT JOIN `ebdb`.`activities` `a` ON((`a`.`id` = `g`.`activity_id`))
                )
                LEFT JOIN `ebdb`.`activities` `ap` ON((`ap`.`id` = `a`.`parent_id`))
              )
              LEFT JOIN `ebdb`.`policy_sorts` `ps` ON((`ps`.`activity_id` = `ap`.`id`))
            )
            LEFT JOIN `ebdb`.`economic_activities` `ea` ON((`ea`.`code` = `ps`.`activity_economic_id`))
          )
          LEFT JOIN `ebdb`.`occupations` `oc` ON((`oc`.`id` = `g`.`occupancy_group`))
        )
        LEFT JOIN `ebdb`.`occupation_groups` `ocp` ON((`ocp`.`id` = `oc`.`group_id`))
      )
      LEFT JOIN (
        SELECT
          `p`.`identification_number` AS `identification_number`,
          `p`.`gender` AS `gender`,
          `p`.`hours` AS `hours`,
          row_number() OVER (
            PARTITION BY `p`.`identification_number`,
            `p`.`gender`
            ORDER BY
              `p`.`id` DESC
          ) AS `rn`
        FROM
          `ebdb`.`policy_spreadsheet_affiliates` `p`
      ) `psa` ON(
        (
          (
            `psa`.`identification_number` = `g`.`number_identification_affiliate`
          )
          AND (`psa`.`rn` = 1)
        )
      )
    )
    LEFT JOIN (
      SELECT
        `a`.`activity_id` AS `activity_id`
      FROM
        `ebdb`.`activity_actions` `a`
      WHERE
        (`a`.`new_state_id` = 160)
      GROUP BY
        `a`.`activity_id`
    ) `aa` ON((`aa`.`activity_id` = `g`.`activity_id`))
  )
WHERE
  (
    (`ea`.`group_economic` IS NOT NULL)
    AND (`aa`.`activity_id` IS NOT NULL)
  )
GROUP BY
  `ea`.`group_economic`,
  year(`g`.`created_at`)
UNION
ALL
SELECT
  `ea`.`group_economic` AS `grupo_actividad`,
  `act`.`cod_sugese` AS `forma`,
  sum(
    (
      CASE
        WHEN (`psa`.`gender` IN ('F', 'M')) THEN 1
        ELSE 0
      END
    )
  ) AS `total`,
  sum(
    (
      CASE
        WHEN (`psa`.`gender` = 'M') THEN 1
        ELSE 0
      END
    )
  ) AS `total_hombres`,
  sum(
    (
      CASE
        WHEN (`psa`.`gender` = 'F') THEN 1
        ELSE 0
      END
    )
  ) AS `total_mujeres`,
  year(`g`.`created_at`) AS `ano`,
  'FormasDelosAccidentes' AS `type`,
  GROUP_CONCAT(`a`.`id` SEPARATOR ', ') AS `activities_gis`,
  2 AS `id`
FROM
  (
    (
      (
        (
          (
            (
              (
                `ebdb`.`gis_sort` `g`
                LEFT JOIN `ebdb`.`activities` `a` ON((`a`.`id` = `g`.`activity_id`))
              )
              LEFT JOIN `ebdb`.`activities` `ap` ON((`ap`.`id` = `a`.`parent_id`))
            )
            LEFT JOIN `ebdb`.`policy_sorts` `ps` ON((`ps`.`activity_id` = `ap`.`id`))
          )
          LEFT JOIN `ebdb`.`economic_activities` `ea` ON((`ea`.`code` = `ps`.`activity_economic_id`))
        )
        LEFT JOIN `ebdb`.`accident_types` `act` ON((`act`.`id` = `g`.`ocurrencia`))
      )
      LEFT JOIN (
        SELECT
          `p`.`identification_number` AS `identification_number`,
          `p`.`gender` AS `gender`,
          `p`.`hours` AS `hours`,
          row_number() OVER (
            PARTITION BY `p`.`identification_number`,
            `p`.`gender`
            ORDER BY
              `p`.`id` DESC
          ) AS `rn`
        FROM
          `ebdb`.`policy_spreadsheet_affiliates` `p`
      ) `psa` ON(
        (
          (
            `psa`.`identification_number` = `g`.`number_identification_affiliate`
          )
          AND (`psa`.`rn` = 1)
        )
      )
    )
    LEFT JOIN (
      SELECT
        `a`.`activity_id` AS `activity_id`
      FROM
        `ebdb`.`activity_actions` `a`
      WHERE
        (`a`.`new_state_id` = 160)
      GROUP BY
        `a`.`activity_id`
    ) `aa` ON((`aa`.`activity_id` = `g`.`activity_id`))
  )
WHERE
  (
    (`ea`.`group_economic` IS NOT NULL)
    AND (`act`.`cod_sugese` IS NOT NULL)
    AND (`aa`.`activity_id` IS NOT NULL)
  )
GROUP BY
  `ea`.`group_economic`,
  `act`.`cod_sugese`,
  year(`g`.`created_at`)
UNION
ALL
SELECT
  `ea`.`group_economic` AS `grupo_actividad`,
  `i`.`code_sugese` AS `grupo_ocupacion`,
  sum(
    (
      CASE
        WHEN (`psa`.`gender` IN ('F', 'M')) THEN 1
        ELSE 0
      END
    )
  ) AS `total`,
  sum(
    (
      CASE
        WHEN (`psa`.`gender` = 'M') THEN 1
        ELSE 0
      END
    )
  ) AS `total_hombres`,
  sum(
    (
      CASE
        WHEN (`psa`.`gender` = 'F') THEN 1
        ELSE 0
      END
    )
  ) AS `total_mujeres`,
  year(`g`.`created_at`) AS `ano`,
  'NaturalezasDeLasLesiones' AS `type`,
  GROUP_CONCAT(`a`.`id` SEPARATOR ', ') AS `activities_gis`,
  3 AS `id`
FROM
  (
    (
      (
        (
          (
            (
              (
                `ebdb`.`gis_sort` `g`
                LEFT JOIN `ebdb`.`activities` `a` ON((`a`.`id` = `g`.`activity_id`))
              )
              LEFT JOIN `ebdb`.`activities` `ap` ON((`ap`.`id` = `a`.`parent_id`))
            )
            LEFT JOIN `ebdb`.`policy_sorts` `ps` ON((`ps`.`activity_id` = `ap`.`id`))
          )
          LEFT JOIN `ebdb`.`economic_activities` `ea` ON((`ea`.`code` = `ps`.`activity_economic_id`))
        )
        LEFT JOIN `ebdb`.`injuries` `i` ON((`i`.`id` = `g`.`mechanism_trauma`))
      )
      LEFT JOIN (
        SELECT
          `p`.`identification_number` AS `identification_number`,
          `p`.`gender` AS `gender`,
          `p`.`hours` AS `hours`,
          row_number() OVER (
            PARTITION BY `p`.`identification_number`,
            `p`.`gender`
            ORDER BY
              `p`.`id` DESC
          ) AS `rn`
        FROM
          `ebdb`.`policy_spreadsheet_affiliates` `p`
      ) `psa` ON(
        (
          (
            `psa`.`identification_number` = `g`.`number_identification_affiliate`
          )
          AND (`psa`.`rn` = 1)
        )
      )
    )
    LEFT JOIN (
      SELECT
        `a`.`activity_id` AS `activity_id`
      FROM
        `ebdb`.`activity_actions` `a`
      WHERE
        (`a`.`new_state_id` = 160)
      GROUP BY
        `a`.`activity_id`
    ) `aa` ON((`aa`.`activity_id` = `g`.`activity_id`))
  )
WHERE
  (
    (`ea`.`group_economic` IS NOT NULL)
    AND (`g`.`type_report` = 'Accidente')
    AND (`aa`.`activity_id` IS NOT NULL)
  )
GROUP BY
  `ea`.`group_economic`,
  year(`g`.`created_at`)
UNION
ALL
SELECT
  `ea`.`group_economic` AS `grupo_actividad`,
  `bp`.`code` AS `grupo`,
  sum(
    (
      CASE
        WHEN (`psa`.`gender` IN ('F', 'M')) THEN 1
        ELSE 0
      END
    )
  ) AS `total`,
  sum(
    (
      CASE
        WHEN (`psa`.`gender` = 'M') THEN 1
        ELSE 0
      END
    )
  ) AS `total_hombres`,
  sum(
    (
      CASE
        WHEN (`psa`.`gender` = 'F') THEN 1
        ELSE 0
      END
    )
  ) AS `total_mujeres`,
  year(`g`.`created_at`) AS `ano`,
  'UbicacionesDeLasLesiones' AS `type`,
  GROUP_CONCAT(`a`.`id` SEPARATOR ', ') AS `activities_gis`,
  4 AS `id`
FROM
  (
    (
      (
        (
          (
            (
              (
                (
                  `ebdb`.`gis_sort` `g`
                  LEFT JOIN `ebdb`.`activities` `a` ON((`a`.`id` = `g`.`activity_id`))
                )
                LEFT JOIN `ebdb`.`activities` `ap` ON((`ap`.`id` = `a`.`parent_id`))
              )
              LEFT JOIN `ebdb`.`policy_sorts` `ps` ON((`ps`.`activity_id` = `ap`.`id`))
            )
            LEFT JOIN (
              SELECT
                `g`.`gis_id` AS `gis_id`,
                `g`.`body_part_name` AS `body_part_name`
              FROM
                (
                  `ebdb`.`gis_body_parts` `g`
                  JOIN (
                    SELECT
                      `ebdb`.`gis_body_parts`.`gis_id` AS `gis_id`,
                      max(`ebdb`.`gis_body_parts`.`id`) AS `max_id`
                    FROM
                      `ebdb`.`gis_body_parts`
                    GROUP BY
                      `ebdb`.`gis_body_parts`.`gis_id`
                  ) `sub` ON(
                    (
                      (`g`.`gis_id` = `sub`.`gis_id`)
                      AND (`g`.`id` = `sub`.`max_id`)
                    )
                  )
                )
            ) `gp` ON((`gp`.`gis_id` = `g`.`id`))
          )
          LEFT JOIN `ebdb`.`body_parts` `bp` ON(
            (
              (`bp`.`name` COLLATE utf8mb4_unicode_ci) = (`gp`.`body_part_name` COLLATE UTF8MB4_UNICODE_CI)
            )
          )
        )
        LEFT JOIN `ebdb`.`economic_activities` `ea` ON((`ea`.`code` = `ps`.`activity_economic_id`))
      )
      LEFT JOIN (
        SELECT
          `p`.`identification_number` AS `identification_number`,
          `p`.`gender` AS `gender`
        FROM
          `ebdb`.`policy_spreadsheet_affiliates` `p`
        GROUP BY
          `p`.`identification_number`,
          `p`.`gender`
      ) `psa` ON(
        (
          `psa`.`identification_number` = `g`.`number_identification_affiliate`
        )
      )
    )
    LEFT JOIN (
      SELECT
        `a`.`activity_id` AS `activity_id`
      FROM
        `ebdb`.`activity_actions` `a`
      WHERE
        (`a`.`new_state_id` = 160)
      GROUP BY
        `a`.`activity_id`
    ) `aa` ON((`aa`.`activity_id` = `g`.`activity_id`))
  )
WHERE
  (
    (`bp`.`code` IS NOT NULL)
    AND (`aa`.`activity_id` IS NOT NULL)
    AND (`ea`.`group_economic` IS NOT NULL)
  )
GROUP BY
  `ea`.`group_economic`,
  `bp`.`code`,
  year(`g`.`created_at`)
UNION
ALL
SELECT
  `ea`.`group_economic` AS `grupo_actividad`,
  `ag`.`code_sugese` AS `grupo_ocupacion`,
  sum(
    (
      CASE
        WHEN (`psa`.`gender` IN ('F', 'M')) THEN 1
        ELSE 0
      END
    )
  ) AS `total`,
  sum(
    (
      CASE
        WHEN (`psa`.`gender` = 'M') THEN 1
        ELSE 0
      END
    )
  ) AS `total_hombres`,
  sum(
    (
      CASE
        WHEN (`psa`.`gender` = 'F') THEN 1
        ELSE 0
      END
    )
  ) AS `total_mujeres`,
  year(`g`.`created_at`) AS `ano`,
  'AgentesMaterialesInvolucrados' AS `type`,
  GROUP_CONCAT(`a`.`id` SEPARATOR ', ') AS `activities_gis`,
  5 AS `id`
FROM
  (
    (
      (
        (
          (
            (
              (
                `ebdb`.`gis_sort` `g`
                LEFT JOIN `ebdb`.`activities` `a` ON((`a`.`id` = `g`.`activity_id`))
              )
              LEFT JOIN `ebdb`.`activities` `ap` ON((`ap`.`id` = `a`.`parent_id`))
            )
            LEFT JOIN `ebdb`.`policy_sorts` `ps` ON((`ps`.`activity_id` = `ap`.`id`))
          )
          LEFT JOIN `ebdb`.`economic_activities` `ea` ON((`ea`.`code` = `ps`.`activity_economic_id`))
        )
        LEFT JOIN `ebdb`.`agets_gis` `ag` ON((`ag`.`id` = `g`.`material_agente`))
      )
      LEFT JOIN (
        SELECT
          `p`.`identification_number` AS `identification_number`,
          `p`.`gender` AS `gender`,
          `p`.`hours` AS `hours`,
          row_number() OVER (
            PARTITION BY `p`.`identification_number`,
            `p`.`gender`
            ORDER BY
              `p`.`id` DESC
          ) AS `rn`
        FROM
          `ebdb`.`policy_spreadsheet_affiliates` `p`
      ) `psa` ON(
        (
          (
            `psa`.`identification_number` = `g`.`number_identification_affiliate`
          )
          AND (`psa`.`rn` = 1)
        )
      )
    )
    LEFT JOIN (
      SELECT
        `a`.`activity_id` AS `activity_id`
      FROM
        `ebdb`.`activity_actions` `a`
      WHERE
        (`a`.`new_state_id` = 160)
      GROUP BY
        `a`.`activity_id`
    ) `aa` ON((`aa`.`activity_id` = `g`.`activity_id`))
  )
WHERE
  (
    (`ea`.`group_economic` IS NOT NULL)
    AND (`g`.`type_report` = 'Accidente')
    AND (`aa`.`activity_id` IS NOT NULL)
  )
GROUP BY
  `ea`.`group_economic`,
  `ag`.`code_sugese`,
  year(`g`.`created_at`)
UNION
ALL
SELECT
  `ea`.`group_economic` AS `group_economic`,
(
    CASE
      `ap`.`service_id`
      WHEN 84 THEN 1
      WHEN 86 THEN 2
      ELSE 3
    END
  ) AS `grupo`,
  sum(
    (
      CASE
        WHEN (`psa`.`gender` IN ('F', 'M')) THEN 1
        ELSE 0
      END
    )
  ) AS `total`,
  sum(
    (
      CASE
        WHEN (`psa`.`gender` = 'M') THEN 1
        ELSE 0
      END
    )
  ) AS `total_hombres`,
  sum(
    (
      CASE
        WHEN (`psa`.`gender` = 'F') THEN 1
        ELSE 0
      END
    )
  ) AS `total_mujeres`,
  year(`g`.`created_at`) AS `ano`,
  'TiposDeIncapacidades' AS `TYPE`,
  GROUP_CONCAT(`a`.`id` SEPARATOR ', ') AS `activities_gis`,
  6 AS `id`
FROM
  (
    (
      (
        (
          (
            (
              (
                `ebdb`.`gis_sort` `g`
                LEFT JOIN `ebdb`.`activities` `a` ON((`a`.`id` = `g`.`activity_id`))
              )
              LEFT JOIN `ebdb`.`activities` `app` ON((`app`.`id` = `a`.`parent_id`))
            )
            LEFT JOIN `ebdb`.`policy_sorts` `ps` ON((`ps`.`activity_id` = `app`.`id`))
          )
          LEFT JOIN `ebdb`.`economic_activities` `ea` ON((`ea`.`code` = `ps`.`activity_economic_id`))
        )
        LEFT JOIN (
          SELECT
            `p`.`identification_number` AS `identification_number`,
            `p`.`gender` AS `gender`,
            `p`.`hours` AS `hours`,
            row_number() OVER (
              PARTITION BY `p`.`identification_number`,
              `p`.`gender`
              ORDER BY
                `p`.`id` DESC
            ) AS `rn`
          FROM
            `ebdb`.`policy_spreadsheet_affiliates` `p`
        ) `psa` ON(
          (
            (
              `psa`.`identification_number` = `g`.`number_identification_affiliate`
            )
            AND (`psa`.`rn` = 1)
          )
        )
      )
      LEFT JOIN (
        SELECT
          `subquery`.`service_id` AS `service_id`,
          `subquery`.`activity_gis` AS `activity_gis`,
          `subquery`.`ano` AS `ano`
        FROM
          (
            SELECT
              `a`.`service_id` AS `service_id`,
              `app`.`id` AS `activity_gis`,
              year(`a`.`created_at`) AS `ano`
            FROM
              (
                (
                  `ebdb`.`activities` `a`
                  LEFT JOIN `ebdb`.`activities` `ap` ON((`ap`.`id` = `a`.`parent_id`))
                )
                LEFT JOIN `ebdb`.`activities` `app` ON((`app`.`id` = `ap`.`parent_id`))
              )
            WHERE
              (
                (`a`.`service_id` = 84)
                AND (`a`.`state_id` = 74)
              )
            UNION
            ALL
            SELECT
              `a`.`service_id` AS `service_id`,
              `ap`.`id` AS `activity_gis`,
              year(`a`.`created_at`) AS `ano`
            FROM
              (
                (
                  `ebdb`.`activities` `a`
                  LEFT JOIN `ebdb`.`activities` `ap` ON((`ap`.`id` = `a`.`parent_id`))
                )
                LEFT JOIN `ebdb`.`activities` `app` ON((`app`.`id` = `ap`.`parent_id`))
              )
            WHERE
              (
                (`a`.`service_id` = 86)
                AND (`a`.`state_id` = 78)
              )
          ) `subquery`
        GROUP BY
          `subquery`.`service_id`,
          `subquery`.`activity_gis`,
          `subquery`.`ano`
      ) `ap` ON(
        (
          (`ap`.`activity_gis` = `a`.`id`)
          AND (`ap`.`ano` = year(`g`.`created_at`))
        )
      )
    )
    LEFT JOIN (
      SELECT
        `a`.`activity_id` AS `activity_id`
      FROM
        `ebdb`.`activity_actions` `a`
      WHERE
        (`a`.`new_state_id` = 160)
      GROUP BY
        `a`.`activity_id`
    ) `aa` ON((`aa`.`activity_id` = `g`.`activity_id`))
  )
WHERE
  (
    (`ea`.`group_economic` IS NOT NULL)
    AND (`aa`.`activity_id` IS NOT NULL)
  )
GROUP BY
  (
    CASE
      `ap`.`service_id`
      WHEN 84 THEN 1
      WHEN 86 THEN 2
      ELSE 3
    END
  ),
  `ea`.`group_economic`,
  year(`g`.`created_at`)
UNION
ALL
SELECT
  `ea`.`group_economic` AS `group_economic`,
(
    CASE
      `g`.`accident_place`
      WHEN 1 THEN 1
      WHEN 2 THEN 3
      WHEN 3 THEN 3
      WHEN 4 THEN 4
      WHEN 5 THEN 1
      WHEN 6 THEN 1
      WHEN 6 THEN 3
      WHEN 7 THEN 2
    END
  ) AS `grupo`,
  sum(
    (
      CASE
        WHEN (`psa`.`gender` IN ('F', 'M')) THEN 1
        ELSE 0
      END
    )
  ) AS `total`,
  sum(
    (
      CASE
        WHEN (`psa`.`gender` = 'M') THEN 1
        ELSE 0
      END
    )
  ) AS `total_hombres`,
  sum(
    (
      CASE
        WHEN (`psa`.`gender` = 'F') THEN 1
        ELSE 0
      END
    )
  ) AS `total_mujeres`,
  year(`g`.`created_at`) AS `ano`,
  'LugaresDelosAccidentes' AS `TYPE`,
  GROUP_CONCAT(`a`.`id` SEPARATOR ', ') AS `activities_gis`,
  7 AS `id`
FROM
  (
    (
      (
        (
          (
            (
              `ebdb`.`gis_sort` `g`
              LEFT JOIN `ebdb`.`activities` `a` ON((`a`.`id` = `g`.`activity_id`))
            )
            LEFT JOIN `ebdb`.`activities` `app` ON((`app`.`id` = `a`.`parent_id`))
          )
          LEFT JOIN `ebdb`.`policy_sorts` `ps` ON((`ps`.`activity_id` = `app`.`id`))
        )
        LEFT JOIN `ebdb`.`economic_activities` `ea` ON((`ea`.`code` = `ps`.`activity_economic_id`))
      )
      LEFT JOIN (
        SELECT
          `p`.`identification_number` AS `identification_number`,
          `p`.`gender` AS `gender`,
          `p`.`hours` AS `hours`,
          row_number() OVER (
            PARTITION BY `p`.`identification_number`,
            `p`.`gender`
            ORDER BY
              `p`.`id` DESC
          ) AS `rn`
        FROM
          `ebdb`.`policy_spreadsheet_affiliates` `p`
      ) `psa` ON(
        (
          (
            `psa`.`identification_number` = `g`.`number_identification_affiliate`
          )
          AND (`psa`.`rn` = 1)
        )
      )
    )
    LEFT JOIN (
      SELECT
        `a`.`activity_id` AS `activity_id`
      FROM
        `ebdb`.`activity_actions` `a`
      WHERE
        (`a`.`new_state_id` = 160)
      GROUP BY
        `a`.`activity_id`
    ) `aa` ON((`aa`.`activity_id` = `g`.`activity_id`))
  )
WHERE
  (
    (`g`.`accident_place` IS NOT NULL)
    AND (`ea`.`group_economic` IS NOT NULL)
    AND (`aa`.`activity_id` IS NOT NULL)
  )
GROUP BY
  (
    CASE
      `g`.`accident_place`
      WHEN 1 THEN 1
      WHEN 2 THEN 3
      WHEN 3 THEN 3
      WHEN 4 THEN 4
      WHEN 5 THEN 1
      WHEN 6 THEN 1
      WHEN 6 THEN 3
      WHEN 7 THEN 2
    END
  ),
  `ea`.`group_economic`,
  year(`g`.`created_at`)