SELECT
  `t`.`number_policy` AS `number_policy`,
  trim(
    LEADING '0'
    FROM
      REPLACE(`t`.`number_policy`, 'SORT-', '')
  ) AS `consecutive_policy`,
  sum(`t`.`t_debit`) AS `colones`,
  sum((`t`.`t_debit` / `t`.`exchange_rate`)) AS `dolares`,
  `t`.`cod_oper` AS `cod_oper`
FROM
  (
    SELECT
      `a`.`number_policy` AS `number_policy`,
      `a`.`receipt_number` AS `receipt_number`,
      `a`.`t_debit` AS `t_debit`,
      `a`.`exchange_rate` AS `exchange_rate`,
      `a`.`created_at` AS `created_at`,
      `a`.`cod_oper` AS `cod_oper`
    FROM
      `ebdb`.`accounting_entries` `a`
    WHERE
      (
        (`a`.`cod_moneda` = 'US')
        AND (`a`.`cod_oper` IN ('050', '087', '011'))
        AND (MONTH(`a`.`created_at`) = MONTH(curdate()))
        AND (year(`a`.`created_at`) = year(curdate()))
      )
    GROUP BY
      `a`.`number_policy`,
      `a`.`receipt_number`,
      `a`.`t_debit`,
      `a`.`exchange_rate`,
      `a`.`created_at`,
      `a`.`cod_oper`
  ) `t`
GROUP BY
  `t`.`number_policy`,
  `t`.`cod_oper`
ORDER BY
  `t`.`number_policy`