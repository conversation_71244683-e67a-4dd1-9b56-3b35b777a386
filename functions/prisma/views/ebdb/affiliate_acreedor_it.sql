SELECT
  `A`.`id` AS `ID_SERVICIO`,
  `A`.`id_bizagi` AS `NO_RADICADO_BIZAGI`,
  `A`.`created_at` AS `FECHA_REGISTRO`,
  'COLP' AS `SOCIEDAD_EN_LA_QUE_SE_VA_A_CREAR_EL_ACREEDOR`,
  '1000' AS `ENTE_QUE_REGULA_Y_COORDINA_LOS_DIFERENTES_GRUPOS_DE_COMPRAS`,
  'IMPE' AS `TIPO_DE_ACREEDOR_DE_ACUERDO_A_SU_CLASIFICACIÓN`,
  IF(
    (`DIT`.`gender` IS NULL),
    IF((`AF`.`gender` = 'M'), 'SENIOR', 'SENIORA'),
    IF((`DIT`.`gender` = 'M'), 'SENIOR', 'SENIORA')
  ) AS `TRATAMIENTO_QUE_SE_LE_DA_AL_ACREEDOR`,
  IF(
    (`DIT`.`full_name_affiliate` IS NULL),
    concat(`AF`.`first_name`, ' ', `AF`.`last_name`),
    `DIT`.`full_name_affiliate`
  ) AS `NOMBRE_COMPLETO_DEL_ACREEDOR`,
  '' AS `NOMBRE_OPCIONAL_2`,
  `AF`.`first_name` AS `NOMBRE`,
  `AF`.`last_name` AS `APELLIDO`,
  `DIT`.`doc_number_affiliate` AS `NUMERO_DE_IDENTIFICACION_DEL_ACREEDOR_CC_NI_TI_AFILIADO`,
  IF(
    (`DIT`.`address` IS NULL),
    `AF`.`address`,
    `DIT`.`address`
  ) AS `DIRECCION_DEL_ACREEDOR`,
  'CO' AS `PAIS_O_NACIONALIDAD_DEL_ACREEDOR`,
  `DIT`.`department` AS `CODIGO_DEL_DEPARTAMENTO`,
  concat(`DIT`.`department`, `DIT`.`municipality`) AS `NOMBRE_DE_LA_POBLACION_DE_ACUERDO_AL_DANE`,
  IF(
    (`DIT`.`cellphone` IS NULL),
    `DIT`.`phone`,
    `DIT`.`cellphone`
  ) AS `TELEFONO_DE_LOCALIZACION_DEL_ACREEDOR`,
  ' ' AS `CORREO_ELECTRONICO_DEL_ACREEDOR`,
  ' ' AS `FAX_DE_LO76ZACION_DEL_ACREEDOR`,
  '' AS `CAMPO_SE_DEBE_DILIGENCIAR_SI_EL_ACREEDOR_ES_A_LA_VEZ_DEUDOR`,
  `AF`.`doc_number` AS `NUMERO_DE_IDENTIFICACION_DEL_ACREEDOR_CC_NI_TI`,
  '' AS `TIPO_DE_DOCUMENTO_DE_IDENTIFICACION`,
  'PN' AS `CLASE_DE_IMPUESTO`,
  'X' AS `X_SI_ES_CEDULA_Y_DESACTIVA_VALIDACION_DIGITO_VALIDACION_NIT`,
  '9999' AS `CODIGO_ACTIVIDAD_DE_DIAN_PARA_REPORTE_DE_MEDIOS_MAGNETICOS`,
  'E' AS `TIPO_DE_CONTRIBUYENTE_DE_ACUERDO_A_LA_CLASIFICACION_DE_DIAN`,
  'CO' AS `PAIS_CONSTANTE_CO`,
  `DIT`.`bank_name` AS `CODIGO_BANCO_DE_LA_CUENTA_DEL_ACREEDOR`,
  `DIT`.`account_number` AS `NO_DE_CUENTA_DEL_ACREEDOR`,
  `DIT`.`account_full_name` AS `NOMBRE_DEL_TITULAR_DE_LA_CUENTA`,
  IF(
    (`DIT`.`account_type` = 'AHORROS'),
    '02',
    IF((`DIT`.`account_type` = 'CORRIENTE'), '01', '')
  ) AS `01_CORRIENTE_02_AHORROS_00_CHEQUE`,
  '' AS `PERSONA_CONTACTO_ENCARGADO_EN_EL_ACREEDOR`,
  '**********' AS `CUENTA_CONTABLE_PARA_REALIZAR_REGISTRO_CUENTA_POR_PAGAR`,
  'K021' AS `GRUPO_DE_TESORERIA`,
  'K001' AS `CONDICIONES_DE_PAGO_CONVENIOS_PAGO_DE_ACUERDO_AL_PROCESO`,
  'X' AS `SE_HABILITA_SISTEMA_PARA_QUE_LLEVE_CONTROL_FACTURACION_DOBLE`,
  'F' AS `IDENTIFICA_VIAS_PAGO_PUEDEN_UTILIZARSE_PAGOS_AUTOMATICOS`,
  'COLP' AS `CONSTANTE_ISS`,
  '' AS `INDICADOR_RETENCION_SI_EL_ACREEDOR_TIENE_RETENCIONES`,
  '' AS `IND_RETENCION_1`,
  '' AS `IND_RETENCION_2`,
  '' AS `IND_RETENCION_3`,
  '' AS `IND_RETENCION_4`,
  '' AS `IND_RETENCION_5`,
  '' AS `IND_RETENCION_6`,
  '' AS `IND_RETENCION_7`,
  '' AS `IND_RETENCION_8`,
  '' AS `IND_RETENCION_9`,
  '' AS `IND_RETENCION_10`,
  '' AS `IND_RETENCION_11`,
  '' AS `LLENAR_SOLO_SI_HAY_POR_LO_MENOS_UNA_RETENCION`,
  'COP' AS `CODIGO_DE_LA_MONEDA_DE_NEGOCIACION_CON_EL_ACREEDOR`,
  'COP Z1' AS `ESQUEMA_DE_PROVEEDOR_NACIONAL_EXTERIOR`,
  'COP X' AS `INDICADOR_OBLIGA_RECIBO_MERCANCIA_ANTES_DE_REGISTRAR_FACTURA`,
  'COP 141' AS `CLAVE_COMPRADOR_RESPONSABLE_DETERMINADAS_ACTIVIDADES_DE_COMPRAS`
FROM
  (
    (
      `ebdb`.`activities` `A`
      LEFT JOIN `ebdb`.`determination_its` `DIT` ON((`A`.`id` = `DIT`.`activity_id`))
    )
    LEFT JOIN `ebdb`.`affiliates` `AF` ON((`A`.`affiliate_id` = `AF`.`id`))
  )
WHERE
  (
    (`A`.`service_id` = 53)
    AND (`A`.`client_id` = 1)
    AND (`A`.`affiliate_id` <> 297724)
  )