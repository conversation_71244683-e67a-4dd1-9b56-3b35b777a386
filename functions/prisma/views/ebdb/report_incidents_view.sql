SELECT
  `t`.`id` AS `id`,
  `t`.`caso_gis` AS `caso_gis`,
  `t`.`comprobante` AS `comprobante`,
  `t`.`activity_id` AS `activity_id`,
  `t`.`type_report` AS `type_report`,
  `t`.`consecutive_policy` AS `consecutive_policy`,
  `t`.`doc_number` AS `doc_number`,
  `t`.`full_name` AS `full_name`,
  `t`.`number_identification_affiliate` AS `number_identification_affiliate`,
  `t`.`name_affiliate` AS `name_affiliate`,
  `t`.`type_currency` AS `type_currency`,
  `t`.`fecha_movimiento` AS `fecha_movimiento`,
  `t`.`cod_oper` AS `cod_oper`,
  `t`.`tipo_movimiento2` AS `tipo_movimiento2`,
  `t`.`mont_mov_moneda_uno` AS `mont_mov_moneda_uno`,
  `t`.`mot_mov_local_uno` AS `mot_mov_local_uno`,
  `t`.`exchange_rate` AS `exchange_rate`,
  `t`.`t_debit` AS `t_debit`,
  `t`.`rate` AS `rate`,
  `t`.`trm` AS `trm`,
  `t`.`ffecha` AS `ffecha`,
  `t`.`mot_mov_dolar_uno` AS `mot_mov_dolar_uno`,
  `t`.`cobertura_afectada` AS `cobertura_afectada`,
  `t`.`fecha_ocurrencia_siniestro` AS `fecha_ocurrencia_siniestro`,
  `t`.`fecha_not_siniestro` AS `fecha_not_siniestro`,
  `t`.`fecha_ini_incapacidad` AS `fecha_ini_incapacidad`,
  `t`.`fecha_fin_incapacidad` AS `fecha_fin_incapacidad`,
  `t`.`tipo_incapacidad` AS `tipo_incapacidad`,
  `t`.`tipo_contratante` AS `tipo_contratante`,
  `t`.`branch_name` AS `branch_name`,
  `t`.`actividad_eco` AS `actividad_eco`,
  `t`.`genero` AS `genero`,
  `t`.`edad` AS `edad`,
  `t`.`causa_siniestro` AS `causa_siniestro`,
  `t`.`descriptivo_siniestro` AS `descriptivo_siniestro`,
  `t`.`lugar_accidente` AS `lugar_accidente`,
  `t`.`provincia` AS `provincia`,
  `t`.`canton` AS `canton`,
  `t`.`distrito` AS `distrito`,
  `t`.`part_lesionada` AS `part_lesionada`,
  `t`.`tipo_enfermedad` AS `tipo_enfermedad`,
  `t`.`fecha_fallecimiento` AS `fecha_fallecimiento`,
  `t`.`conyuge` AS `conyuge`,
  `t`.`fecha_naci_conyuge` AS `fecha_naci_conyuge`,
  `t`.`fecha_naci_hijo_menor` AS `fecha_naci_hijo_menor`,
  `t`.`cant_hijos` AS `cant_hijos`,
  `t`.`fecha_naci_madre` AS `fecha_naci_madre`,
  `t`.`fecha_naci_padre` AS `fecha_naci_padre`,
  `t`.`pendientes_economicos` AS `pendientes_economicos`,
  `t`.`fecha_naci_pend` AS `fecha_naci_pend`,
  `t`.`id_beneficiario` AS `id_beneficiario`,
  `t`.`nombre_beneficiario` AS `nombre_beneficiario`,
  `t`.`centro_asistencia` AS `centro_asistencia`,
  `t`.`cod_correduria` AS `cod_correduria`,
  `t`.`por_contrato` AS `por_contrato`,
  `t`.`por_facultativo` AS `por_facultativo`,
  `t`.`activity_gis` AS `activity_gis`,
  `t`.`cant` AS `cant`,
  `t`.`created_at` AS `created_at`,
  `t`.`cod_corredor` AS `cod_corredor`,
  `t`.`cod_intermediario` AS `cod_intermediario`,
  `t`.`nombre_intermediario` AS `nombre_intermediario`,
  `t`.`nombre_corredor` AS `nombre_corredor`,
  `t`.`fecha_reporte` AS `fecha_reporte`,
(
    CASE
      WHEN (`t`.`cod_oper` = '087') THEN (`t`.`mot_mov_dolar_uno` * -(1))
      ELSE `t`.`mot_mov_dolar_uno`
    END
  ) AS `mot_mov_dolar`,
(
    CASE
      WHEN (`t`.`cod_oper` = '087') THEN (`t`.`mot_mov_local_uno` * -(1))
      ELSE `t`.`mot_mov_local_uno`
    END
  ) AS `mot_mov_local`,
(
    CASE
      WHEN (`t`.`cod_oper` = '087') THEN (`t`.`mont_mov_moneda_uno` * -(1))
      ELSE `t`.`mont_mov_moneda_uno`
    END
  ) AS `mont_mov_moneda`,
(
    CASE
      `t`.`cod_oper`
      WHEN '050' THEN (
        CASE
          WHEN (
            row_number() OVER (
              PARTITION BY `t`.`cod_oper`,
              `t`.`activity_gis`
              ORDER BY
                `t`.`cod_oper`,
                `t`.`activity_gis` DESC
            ) = 1
          ) THEN 'Reserva inicial'
          ELSE 'Aumento de Reserva'
        END
      )
      WHEN '087' THEN 'liberación de Reserva'
      WHEN '011' THEN 'Pago IT'
    END
  ) AS `tipo_movimiento`,
  row_number() OVER (
    PARTITION BY `t`.`cod_oper`,
    `t`.`activity_gis`
    ORDER BY
      `t`.`cod_oper`,
      `t`.`activity_gis` DESC
  ) AS `indice`
FROM
  (
    SELECT
      (
        CASE
          WHEN (`aab`.`cant` > 0) THEN `g`.`consecutive`
          ELSE `g`.`consecutive_gis`
        END
      ) AS `id`,
      `g`.`id` AS `caso_gis`,
      `ac`.`receipt_number` AS `comprobante`,
      `g`.`activity_id` AS `activity_id`,
      `g`.`type_report` AS `type_report`,
      concat('SORT-', lpad(`ps`.`consecutive`, 4, '0')) AS `consecutive_policy`,
      `af`.`doc_number` AS `doc_number`,
      `af`.`full_name` AS `full_name`,
      `g`.`number_identification_affiliate` AS `number_identification_affiliate`,
      `afg`.`full_name` AS `name_affiliate`,
      `ps`.`type_currency` AS `type_currency`,
      date_format(`ac`.`created_at`, '%d/%m/%Y') AS `fecha_movimiento`,
      `ac`.`cod_oper` AS `cod_oper`,
(
        CASE
          `ac`.`cod_oper`
          WHEN '050' THEN 'RESERVA INICIAL'
          WHEN '087' THEN 'AJUSTE DE RESERVA'
          WHEN '011' THEN 'PAGO'
        END
      ) AS `tipo_movimiento2`,
      `ac`.`t_debit` AS `mont_mov_moneda_uno`,
(
        CASE
          WHEN (`ps`.`type_currency` = 'CRC') THEN `ac`.`t_debit`
          ELSE 0
        END
      ) AS `mot_mov_local_uno`,
      `ac`.`exchange_rate` AS `exchange_rate`,
      `ac`.`t_debit` AS `t_debit`,
      `tr`.`rate` AS `rate`,
      `tr`.`rate` AS `trm`,
      `ac`.`created_at` AS `ffecha`,
(
        CASE
          WHEN (`ps`.`type_currency` = 'USD') THEN (`ac`.`t_debit` / `ac`.`exchange_rate`)
          ELSE (`ac`.`t_debit` / `tr`.`rate`)
        END
      ) AS `mot_mov_dolar_uno`,
      '' AS `cobertura_afectada`,
      date_format(`g`.`date_accident`, '%d/%m/%Y') AS `fecha_ocurrencia_siniestro`,
      date_format(`g`.`created_at`, '%d/%m/%Y') AS `fecha_not_siniestro`,
      date_format(`pis`.`start_date`, '%d/%m/%Y') AS `fecha_ini_incapacidad`,
      date_format(`pis`.`end_date`, '%d/%m/%Y') AS `fecha_fin_incapacidad`,
(
        CASE
          WHEN (`ac`.`cod_oper` = '011') THEN 'Temporal'
          ELSE ''
        END
      ) AS `tipo_incapacidad`,
(
        CASE
          WHEN (`af`.`doc_type` <> 'CJ') THEN 'Persona natural'
          ELSE 'Persona jurídica'
        END
      ) AS `tipo_contratante`,
      `eb`.`branch_name` AS `branch_name`,
      `ea`.`activity_name` AS `actividad_eco`,
(
        CASE
          WHEN (`psa`.`gender` = 'M') THEN 'Masculino'
          ELSE 'Femenino'
        END
      ) AS `genero`,
(
        CASE
          WHEN (`psa`.`date_of_birth` IS NOT NULL) THEN timestampdiff(YEAR, `psa`.`date_of_birth`, curdate())
          ELSE 0
        END
      ) AS `edad`,
      '' AS `causa_siniestro`,
      `g`.`accident_description` AS `descriptivo_siniestro`,
(
        CASE
          WHEN (`g`.`accident_place` = '1') THEN 'Puesto de trabajo'
          WHEN (`g`.`accident_place` = '2') THEN 'Casa'
          WHEN (`g`.`accident_place` = '3') THEN 'Trayecto'
          WHEN (`g`.`accident_place` = '4') THEN 'En comisión'
          WHEN (`g`.`accident_place` = '5') THEN 'Dentro del centro de trabajo'
          WHEN (`g`.`accident_place` = '6') THEN 'Fuera del centro del trabajo'
          WHEN (`g`.`accident_place` = '7') THEN 'Labores o trabajos no habituales fuera del centro de trabajo'
          WHEN (`g`.`accident_place` = '8') THEN 'Otro lugar dentro del centro de trabajo'
          ELSE 'Desconocido'
        END
      ) AS `lugar_accidente`,
      `p`.`name` AS `provincia`,
      `ct`.`name` AS `canton`,
      `dt`.`name` AS `distrito`,
      GROUP_CONCAT(
        DISTINCT `gbp`.`body_part_name`
        ORDER BY
          `gbp`.`body_part_name` ASC SEPARATOR ', '
      ) AS `part_lesionada`,
(
        CASE
          WHEN (`g`.`agent_involved` = 'agente_fisico') THEN 'Agente físico'
          WHEN (`g`.`agent_involved` = 'agente_biologico') THEN 'Agente biológico'
          WHEN (`g`.`agent_involved` = 'factores_psicosociales') THEN 'Factores psicosociales'
          WHEN (
            `g`.`agent_involved` = 'enfermedades_osteomuscular'
          ) THEN 'Enfermedades del sistema osteomuscular'
          WHEN (`g`.`agent_involved` = 'otros') THEN 'Otros'
          ELSE ''
        END
      ) AS `tipo_enfermedad`,
      '' AS `fecha_fallecimiento`,
      '' AS `conyuge`,
      '' AS `fecha_naci_conyuge`,
      '' AS `fecha_naci_hijo_menor`,
      '' AS `cant_hijos`,
      '' AS `fecha_naci_madre`,
      '' AS `fecha_naci_padre`,
      '' AS `pendientes_economicos`,
      '' AS `fecha_naci_pend`,
      '' AS `id_beneficiario`,
      '' AS `nombre_beneficiario`,
      '' AS `centro_asistencia`,
      `u`.`code_correduria` AS `cod_correduria`,
(
        CASE
          WHEN (year(`ps`.`validity_from`) = '2025') THEN 30
          ELSE 0
        END
      ) AS `por_contrato`,
      '' AS `por_facultativo`,
      `ac`.`activity_gis` AS `activity_gis`,
      `aa`.`cant` AS `cant`,
      `ac`.`created_at` AS `created_at`,
(
        CASE
          WHEN (`up`.`cod_intermediario` IS NOT NULL) THEN `up`.`cod_corredor`
        END
      ) AS `cod_corredor`,
(
        CASE
          WHEN (`up`.`cod_intermediario` IS NULL) THEN `up`.`cod_corredor`
          ELSE `up`.`cod_intermediario`
        END
      ) AS `cod_intermediario`,
      `ps`.`brokerage_name` AS `nombre_intermediario`,
(
        CASE
          WHEN (
            trim(lower(`ps`.`brokerage_name`)) = trim(lower(`ps`.`advisor_name`))
          ) THEN ''
          ELSE `ps`.`advisor_name`
        END
      ) AS `nombre_corredor`,
      date_format(`aaa`.`created_at`, '%d/%m/%Y') AS `fecha_reporte`
    FROM
      (
        (
          (
            (
              (
                (
                  (
                    (
                      (
                        (
                          (
                            (
                              (
                                (
                                  (
                                    (
                                      (
                                        (
                                          (
                                            (
                                              (
                                                (
                                                  (
                                                    `ebdb`.`accounting_entries` `ac`
                                                    LEFT JOIN `ebdb`.`gis_sort` `g` ON((`g`.`activity_id` = `ac`.`activity_gis`))
                                                  )
                                                  LEFT JOIN `ebdb`.`trm_rates` `tr` ON(
                                                    (
                                                      cast(`tr`.`created_at` AS date) = cast(`ac`.`created_at` AS date)
                                                    )
                                                  )
                                                )
                                                LEFT JOIN `ebdb`.`activities` `a` ON((`a`.`id` = `g`.`activity_id`))
                                              )
                                              LEFT JOIN `ebdb`.`affiliates` `afg` ON((`afg`.`id` = `a`.`affiliate_id`))
                                            )
                                            LEFT JOIN `ebdb`.`activities` `ap` ON((`ap`.`id` = `a`.`parent_id`))
                                          )
                                          LEFT JOIN `ebdb`.`policy_sorts` `ps` ON((`ps`.`activity_id` = `ap`.`id`))
                                        )
                                        LEFT JOIN `ebdb`.`affiliates` `af` ON((`af`.`id` = `ap`.`affiliate_id`))
                                      )
                                      LEFT JOIN `ebdb`.`economic_activities` `ea` ON((`ea`.`code` = `ps`.`activity_economic_id`))
                                    )
                                    LEFT JOIN `ebdb`.`economic_branches` `eb` ON((`eb`.`id` = `ea`.`branch_id`))
                                  )
                                  LEFT JOIN `ebdb`.`provinces` `p` ON((`p`.`id` = `g`.`province`))
                                )
                                LEFT JOIN `ebdb`.`cantons` `ct` ON(
                                  (
                                    (`ct`.`id` = `g`.`canton`)
                                    AND (`ct`.`province_id` = `g`.`province`)
                                  )
                                )
                              )
                              LEFT JOIN `ebdb`.`districts` `dt` ON(
                                (
                                  (`dt`.`id` = `g`.`district`)
                                  AND (`dt`.`canton_id` = `g`.`canton`)
                                  AND (`dt`.`province_id` = `g`.`province`)
                                )
                              )
                            )
                            LEFT JOIN `ebdb`.`activities` `ape` ON((`ape`.`parent_id` = `g`.`activity_id`))
                          )
                          LEFT JOIN `ebdb`.`activities` `apeb` ON((`apeb`.`parent_id` = `ape`.`id`))
                        )
                        LEFT JOIN `ebdb`.`pe_it_sorts` `pit` ON((`pit`.`activity_id` = `apeb`.`id`))
                      )
                      LEFT JOIN (
                        SELECT
                          `ap`.`parent_id` AS `serv_gis`,
                          `ps`.`id` AS `id`,
                          `ps`.`start_date` AS `start_date`,
                          `ps`.`end_date` AS `end_date`
                        FROM
                          (
                            (
                              (
                                `ebdb`.`pe_it_sorts` `p`
                                LEFT JOIN `ebdb`.`activities` `a` ON((`a`.`id` = `p`.`activity_id`))
                              )
                              LEFT JOIN `ebdb`.`activities` `ap` ON((`ap`.`id` = `a`.`parent_id`))
                            )
                            LEFT JOIN `ebdb`.`peit_inability_sorts` `ps` ON((`ps`.`pe_it_sort_id` = `p`.`id`))
                          )
                        GROUP BY
                          `ap`.`parent_id`
                      ) `pis` ON((`pis`.`serv_gis` = `ac`.`activity_gis`))
                    )
                    LEFT JOIN `ebdb`.`policy_spreadsheet_affiliates` `psa` ON(
                      (
                        `psa`.`identification_number` = `g`.`number_identification_affiliate`
                      )
                    )
                  )
                  LEFT JOIN `ebdb`.`gis_body_parts` `gbp` ON((`gbp`.`gis_id` = `g`.`id`))
                )
                LEFT JOIN `ebdb`.`users` `u` ON((`u`.`id` = `ap`.`user_id`))
              )
              LEFT JOIN (
                SELECT
                  `a`.`activity_id` AS `activity_id`,
                  count(0) AS `cant`
                FROM
                  `ebdb`.`activity_actions` `a`
                WHERE
                  (`a`.`new_state_id` IN (194, 157))
                GROUP BY
                  `a`.`activity_id`
              ) `aa` ON((`aa`.`activity_id` = `ac`.`activity_gis`))
            )
            LEFT JOIN (
              SELECT
                `a`.`activity_id` AS `activity_id`,
                cast(`a`.`created_at` AS date) AS `created_at`
              FROM
                `ebdb`.`activity_actions` `a`
              WHERE
                (`a`.`action_id` IN (273, 274))
              GROUP BY
                `a`.`activity_id`
            ) `aaa` ON((`aaa`.`activity_id` = `ac`.`activity_gis`))
          )
          LEFT JOIN (
            SELECT
              `a`.`activity_id` AS `activity_id`,
              count(0) AS `cant`
            FROM
              `ebdb`.`activity_actions` `a`
            WHERE
              (`a`.`new_state_id` = 160)
            GROUP BY
              `a`.`activity_id`
          ) `aab` ON((`aab`.`activity_id` = `ac`.`activity_gis`))
        )
        LEFT JOIN (
          SELECT
            REPLACE(
              (`s`.`username` COLLATE utf8mb4_unicode_ci),
              'CO-',
              ''
            ) AS `username`,
            REPLACE(`s`.`code_mnk`, 'CO-', '') AS `cod_corredor`,
            REPLACE(`s`.`code_correduria`, 'CO-', '') AS `cod_intermediario`,
            `s`.`tipo_corredor` AS `tipo_corredor`,
            `s`.`correduria` AS `correduria`,
            `s`.`advisor_name` AS `advisor_name`,
            `s`.`brokerage_name` AS `brokerage_name`
          FROM
            `ebdb`.`users` `s`
          GROUP BY
            REPLACE(
              (`s`.`username` COLLATE utf8mb4_unicode_ci),
              'CO-',
              ''
            )
        ) `up` ON(
          (
            `up`.`username` = REPLACE(
              (`ps`.`code` COLLATE utf8mb4_unicode_ci),
              'CO-',
              ''
            )
          )
        )
      )
    WHERE
      (
        (`ac`.`cod_oper` IN ('050', '087', '011'))
        AND (`ac`.`debit` <> 0)
        AND (`ap`.`state_id` <> 196)
      )
    GROUP BY
      `ac`.`id`
    ORDER BY
      `ac`.`cod_oper`,
      `ac`.`activity_gis` DESC
  ) `t`