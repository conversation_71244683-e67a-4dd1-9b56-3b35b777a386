SELECT
  `apm`.`id` AS `actividad_pm`,
  `g`.`consecutive` AS `gis_consecutive`,
  `msfu`.`follow_up_number` AS `follow_up_number`,
  `ps`.`consecutive` AS `policy_consecutive`,
  `afftk`.`doc_number` AS `doc_number_taker`,
  `afftk`.`full_name` AS `name_taker`,
  `ea`.`activity_name` AS `activity_economic_name`,
  `affwk`.`doc_number` AS `doc_number_worker`,
  `affwk`.`full_name` AS `worker_name`,
(
    CASE
      WHEN (`affwk`.`gender` = 'M') THEN 'Masculino'
      ELSE 'Femenino'
    END
  ) AS `worker_gender`,
  timestampdiff(YEAR, `affwk`.`birthday`, curdate()) AS `worker_ager`,
(
    CASE
      WHEN (`up`.`cod_intermediario` IS NOT NULL) THEN `up`.`cod_corredor`
    END
  ) AS `cod_corredor`,
(
    CASE
      WHEN (`up`.`cod_intermediario` IS NULL) THEN `up`.`cod_corredor`
      ELSE `up`.`cod_intermediario`
    END
  ) AS `cod_intermediario`,
  `ps`.`brokerage_name` AS `nombre_intermediario`,
(
    CASE
      WHEN (
        trim(lower(`ps`.`brokerage_name`)) = trim(lower(`ps`.`advisor_name`))
      ) THEN ''
      ELSE `ps`.`advisor_name`
    END
  ) AS `nombre_corredor`,
  `g`.`date_accident` AS `accident_date`,
  `aag`.`created_at` AS `report_gis_date`,
  `msfu`.`valuation_date` AS `qualification_date`,
  '' AS `expense_type`,
  '' AS `expense_amount`,
  `prov`.`name` AS `provider_name`,
  `ms`.`service_group` AS `service_group`,
  `g`.`ocurrencia` AS `occurrence_manner`,
  `g`.`material_agente` AS `material_agent`,
  `g`.`mechanism_trauma` AS `injury_mechanism`,
  `g`.`third_party` AS `third_party_assistance_required`,
  `g`.`work_modality` AS `work_modality`,
  `g`.`external_cause` AS `external_cause`,
  `g`.`first_aid` AS `external_medical_care`,
  `msd`.`id` AS `diagnosis_id`,
  `msd`.`medical_service_follow_up_id` AS `medical_service_follow_up_id`,
  GROUP_CONCAT(
    DISTINCT concat(`msd`.`code`, ' - ', `msd`.`description`)
    ORDER BY
      `msd`.`code` ASC SEPARATOR ', '
  ) AS `diagnosis`,
  GROUP_CONCAT(
    DISTINCT `gbp`.`body_part_name`
    ORDER BY
      `gbp`.`body_part_name` ASC SEPARATOR ', '
  ) AS `injured_body_parts`,
  `pv`.`name` AS `worker_province`,
  `cn`.`name` AS `worker_canton`,
  `ds`.`name` AS `worker_district`,
  `apm`.`created_at` AS `date_filter`
FROM
  (
    (
      (
        (
          (
            (
              (
                (
                  (
                    (
                      (
                        (
                          (
                            (
                              (
                                (
                                  (
                                    (
                                      SELECT
                                        `apm`.`id` AS `id`,
                                        `apm`.`parent_id` AS `parent_id`,
                                        `apm`.`service_id` AS `service_id`,
                                        `apm`.`created_at` AS `created_at`,
                                        `apm`.`deleted_at` AS `deleted_at`,
                                        row_number() OVER (
                                          PARTITION BY `apm`.`parent_id`
                                          ORDER BY
                                            `apm`.`created_at`
                                        ) AS `rn`
                                      FROM
                                        `ebdb`.`activities` `apm`
                                      WHERE
                                        (`apm`.`service_id` = 83)
                                    ) `apm`
                                    JOIN (
                                      SELECT
                                        `ebdb`.`medical_services_sort`.`id` AS `id`,
                                        `ebdb`.`medical_services_sort`.`service_group` AS `service_group`,
                                        `ebdb`.`medical_services_sort`.`activity_id` AS `activity_id`,
                                        `ebdb`.`medical_services_sort`.`primary_care_provider` AS `primary_care_provider`,
                                        row_number() OVER (
                                          PARTITION BY `ebdb`.`medical_services_sort`.`activity_id`
                                          ORDER BY
                                            `ebdb`.`medical_services_sort`.`created_at`
                                        ) AS `rn`
                                      FROM
                                        `ebdb`.`medical_services_sort`
                                    ) `ms` ON(
                                      (
                                        (`ms`.`activity_id` = `apm`.`id`)
                                        AND (`ms`.`rn` = 1)
                                      )
                                    )
                                  )
                                  JOIN `ebdb`.`medical_service_follow_ups` `msfu` ON((`msfu`.`medical_services_sort_id` = `ms`.`id`))
                                )
                                LEFT JOIN (
                                  SELECT
                                    `ebdb`.`medical_service_diagnostics`.`id` AS `id`,
                                    `ebdb`.`medical_service_diagnostics`.`medical_service_follow_up_id` AS `medical_service_follow_up_id`,
                                    `ebdb`.`medical_service_diagnostics`.`medical_service_sort_id` AS `medical_service_sort_id`,
                                    `ebdb`.`medical_service_diagnostics`.`code` AS `code`,
                                    `ebdb`.`medical_service_diagnostics`.`description` AS `description`,
                                    row_number() OVER (
                                      PARTITION BY `ebdb`.`medical_service_diagnostics`.`medical_service_sort_id`
                                      ORDER BY
                                        `ebdb`.`medical_service_diagnostics`.`created_at`
                                    ) AS `rn`
                                  FROM
                                    `ebdb`.`medical_service_diagnostics`
                                ) `msd` ON(
                                  (
                                    (`msd`.`medical_service_sort_id` = `ms`.`id`)
                                    AND (`msd`.`rn` = 1)
                                  )
                                )
                              )
                              LEFT JOIN `ebdb`.`activities` `ag` ON((`apm`.`parent_id` = `ag`.`id`))
                            )
                            LEFT JOIN (
                              SELECT
                                `ebdb`.`gis_sort`.`id` AS `id`,
                                `ebdb`.`gis_sort`.`activity_id` AS `activity_id`,
                                `ebdb`.`gis_sort`.`consecutive` AS `consecutive`,
                                `ebdb`.`gis_sort`.`date_accident` AS `date_accident`,
                                `ebdb`.`gis_sort`.`ocurrencia` AS `ocurrencia`,
                                `ebdb`.`gis_sort`.`material_agente` AS `material_agente`,
                                `ebdb`.`gis_sort`.`mechanism_trauma` AS `mechanism_trauma`,
                                `ebdb`.`gis_sort`.`third_party` AS `third_party`,
                                `ebdb`.`gis_sort`.`work_modality` AS `work_modality`,
                                `ebdb`.`gis_sort`.`external_cause` AS `external_cause`,
                                `ebdb`.`gis_sort`.`first_aid` AS `first_aid`,
                                row_number() OVER (
                                  PARTITION BY `ebdb`.`gis_sort`.`activity_id`
                                  ORDER BY
                                    `ebdb`.`gis_sort`.`created_at`
                                ) AS `rn`
                              FROM
                                `ebdb`.`gis_sort`
                            ) `g` ON(
                              (
                                (`g`.`activity_id` = `ag`.`id`)
                                AND (`g`.`rn` = 1)
                              )
                            )
                          )
                          LEFT JOIN `ebdb`.`activities` `aps` ON((`aps`.`id` = `ag`.`parent_id`))
                        )
                        LEFT JOIN `ebdb`.`policy_sorts` `ps` ON((`ps`.`activity_id` = `aps`.`id`))
                      )
                      LEFT JOIN `ebdb`.`affiliates` `afftk` ON((`afftk`.`id` = `aps`.`affiliate_id`))
                    )
                    LEFT JOIN `ebdb`.`affiliates` `affwk` ON((`affwk`.`id` = `ag`.`affiliate_id`))
                  )
                  LEFT JOIN `ebdb`.`economic_activities` `ea` ON((`ea`.`code` = `ps`.`activity_economic_id`))
                )
                LEFT JOIN `ebdb`.`providers` `prov` ON((`prov`.`id` = `ms`.`primary_care_provider`))
              )
              LEFT JOIN `ebdb`.`provinces` `pv` ON((`pv`.`id` = `affwk`.`province`))
            )
            LEFT JOIN `ebdb`.`cantons` `cn` ON(
              (
                (`cn`.`province_id` = `pv`.`id`)
                AND (`cn`.`id` = `affwk`.`canton`)
              )
            )
          )
          LEFT JOIN `ebdb`.`districts` `ds` ON(
            (
              (`ds`.`province_id` = `pv`.`id`)
              AND (`ds`.`canton_id` = `cn`.`id`)
              AND (`ds`.`id` = `affwk`.`district`)
            )
          )
        )
        LEFT JOIN `ebdb`.`gis_body_parts` `gbp` ON((`gbp`.`gis_id` = `g`.`id`))
      )
      LEFT JOIN (
        SELECT
          REPLACE(
            (`s`.`username` COLLATE utf8mb4_unicode_ci),
            'CO-',
            ''
          ) AS `username`,
          REPLACE(`s`.`code_mnk`, 'CO-', '') AS `cod_corredor`,
          REPLACE(`s`.`code_correduria`, 'CO-', '') AS `cod_intermediario`,
          `s`.`tipo_corredor` AS `tipo_corredor`,
          `s`.`correduria` AS `correduria`,
          `s`.`advisor_name` AS `advisor_name`,
          `s`.`brokerage_name` AS `brokerage_name`
        FROM
          `ebdb`.`users` `s`
        GROUP BY
          REPLACE(
            (`s`.`username` COLLATE utf8mb4_unicode_ci),
            'CO-',
            ''
          )
      ) `up` ON(
        (
          `up`.`username` = REPLACE(
            (`ps`.`code` COLLATE utf8mb4_unicode_ci),
            'CO-',
            ''
          )
        )
      )
    )
    LEFT JOIN `ebdb`.`activity_actions` `aag` ON(
      (
        (`aag`.`activity_id` = `ag`.`id`)
        AND (`aag`.`action_id` = 264)
      )
    )
  )
WHERE
  (
    (`apm`.`rn` = 1)
    AND (`apm`.`service_id` = 83)
    AND (`apm`.`deleted_at` IS NULL)
    AND (`ag`.`deleted_at` IS NULL)
    AND (`aps`.`deleted_at` IS NULL)
    AND (`aps`.`state_id` <> 196)
    AND (
      (`ms`.`service_group` = 'Primer nivel')
      OR (`ms`.`service_group` = 'Segundo nivel')
      OR (`ms`.`service_group` = 'Tercer nivel')
    )
  )
GROUP BY
  `apm`.`id`,
  `msfu`.`id`