SELECT
  (
    CASE
      WHEN (`oc`.`group_id` IS NULL) THEN 0
      ELSE `oc`.`group_id`
    END
  ) AS `grupo_ocupacion`,
  `ea`.`group_economic` AS `grupo`,
  sum(
    (
      CASE
        WHEN (`psa`.`gender` IN ('F', 'M')) THEN 1
        ELSE 0
      END
    )
  ) AS `total`,
  sum(
    (
      CASE
        WHEN (`psa`.`gender` = 'M') THEN 1
        ELSE 0
      END
    )
  ) AS `total_hombres`,
  sum(
    (
      CASE
        WHEN (`psa`.`gender` = 'F') THEN 1
        ELSE 0
      END
    )
  ) AS `total_mujeres`,
  year(`g`.`created_at`) AS `ano`,
  'GruposOcupacionales' AS `type`,
  1 AS `id`
FROM
  (
    (
      (
        (
          (
            (
              (
                `ebdb`.`gis_sort` `g`
                LEFT JOIN `ebdb`.`activities` `a` ON((`a`.`id` = `g`.`activity_id`))
              )
              LEFT JOIN `ebdb`.`activities` `ap` ON((`ap`.`id` = `a`.`parent_id`))
            )
            LEFT JOIN `ebdb`.`policy_sorts` `ps` ON((`ps`.`activity_id` = `ap`.`id`))
          )
          LEFT JOIN `ebdb`.`economic_activities` `ea` ON((`ea`.`code` = `ps`.`activity_economic_id`))
        )
        LEFT JOIN `ebdb`.`occupations` `oc` ON((`oc`.`id` = `g`.`occupancy_group`))
      )
      LEFT JOIN (
        SELECT
          `p`.`identification_number` AS `identification_number`,
          `p`.`gender` AS `gender`,
          `p`.`hours` AS `hours`,
          row_number() OVER (
            PARTITION BY `p`.`identification_number`,
            `p`.`gender`
            ORDER BY
              `p`.`id` DESC
          ) AS `rn`
        FROM
          `ebdb`.`policy_spreadsheet_affiliates` `p`
      ) `psa` ON(
        (
          (
            `psa`.`identification_number` = `g`.`number_identification_affiliate`
          )
          AND (`psa`.`rn` = 1)
        )
      )
    )
    LEFT JOIN (
      SELECT
        `a`.`activity_id` AS `activity_id`
      FROM
        `ebdb`.`activity_actions` `a`
      WHERE
        (`a`.`new_state_id` = 160)
      GROUP BY
        `a`.`activity_id`
    ) `aa` ON((`aa`.`activity_id` = `g`.`activity_id`))
  )
WHERE
  (
    (`g`.`type_report` = 'Enfermedad')
    AND (`ea`.`group_economic` IS NOT NULL)
    AND (`aa`.`activity_id` IS NOT NULL)
    AND (`a`.`deleted_at` IS NULL)
  )
GROUP BY
  `oc`.`group_id`,
  year(`g`.`created_at`)
UNION
ALL
SELECT
  (
    CASE
      WHEN (`oc`.`group_id` IS NULL) THEN 0
      ELSE `oc`.`group_id`
    END
  ) AS `grupo_ocupacion`,
(
    CASE
      WHEN (`psa`.`age` < 15) THEN 1
      WHEN (
        (`psa`.`age` >= 15)
        AND (`psa`.`age` < 18)
      ) THEN 15
      WHEN (
        (`psa`.`age` >= 18)
        AND (`psa`.`age` < 20)
      ) THEN 18
      WHEN (
        (`psa`.`age` >= 20)
        AND (`psa`.`age` < 25)
      ) THEN 20
      WHEN (
        (`psa`.`age` >= 25)
        AND (`psa`.`age` < 30)
      ) THEN 25
      WHEN (
        (`psa`.`age` >= 30)
        AND (`psa`.`age` < 35)
      ) THEN 30
      WHEN (
        (`psa`.`age` >= 35)
        AND (`psa`.`age` < 40)
      ) THEN 35
      WHEN (
        (`psa`.`age` >= 40)
        AND (`psa`.`age` < 45)
      ) THEN 40
      WHEN (
        (`psa`.`age` >= 45)
        AND (`psa`.`age` < 50)
      ) THEN 45
      WHEN (
        (`psa`.`age` >= 50)
        AND (`psa`.`age` < 55)
      ) THEN 50
      WHEN (
        (`psa`.`age` >= 55)
        AND (`psa`.`age` < 60)
      ) THEN 55
      WHEN (
        (`psa`.`age` >= 60)
        AND (`psa`.`age` < 65)
      ) THEN 60
      WHEN (`psa`.`age` >= 65) THEN 65
      ELSE 99
    END
  ) AS `grupo`,
  sum(
    (
      CASE
        WHEN (`psa`.`gender` IN ('F', 'M')) THEN 1
        ELSE 0
      END
    )
  ) AS `total_f`,
  sum(
    (
      CASE
        WHEN (`psa`.`gender` = 'M') THEN 1
        ELSE 0
      END
    )
  ) AS `total_hombres`,
  sum(
    (
      CASE
        WHEN (`psa`.`gender` = 'F') THEN 1
        ELSE 0
      END
    )
  ) AS `total_mujeres`,
  year(`g`.`created_at`) AS `ano`,
  'GruposDeEdades' AS `TYPE`,
  3 AS `id`
FROM
  (
    (
      (
        (
          (
            (
              `ebdb`.`gis_sort` `g`
              LEFT JOIN `ebdb`.`activities` `a` ON((`a`.`id` = `g`.`activity_id`))
            )
            LEFT JOIN `ebdb`.`activities` `ap` ON((`ap`.`id` = `a`.`parent_id`))
          )
          LEFT JOIN `ebdb`.`policy_sorts` `ps` ON((`ps`.`activity_id` = `ap`.`id`))
        )
        LEFT JOIN `ebdb`.`occupations` `oc` ON((`oc`.`id` = `g`.`occupancy_group`))
      )
      LEFT JOIN (
        SELECT
          `p`.`identification_number` AS `identification_number`,
          `p`.`gender` AS `gender`,
          `p`.`hours` AS `hours`,
          timestampdiff(YEAR, `p`.`date_of_birth`, curdate()) AS `age`,
          row_number() OVER (
            PARTITION BY `p`.`identification_number`,
            `p`.`gender`
            ORDER BY
              `p`.`id` DESC
          ) AS `rn`
        FROM
          `ebdb`.`policy_spreadsheet_affiliates` `p`
        WHERE
          (`p`.`date_of_birth` IS NOT NULL)
      ) `psa` ON(
        (
          (
            `psa`.`identification_number` = `g`.`number_identification_affiliate`
          )
          AND (`psa`.`rn` = 1)
        )
      )
    )
    LEFT JOIN (
      SELECT
        `a`.`activity_id` AS `activity_id`
      FROM
        `ebdb`.`activity_actions` `a`
      WHERE
        (`a`.`new_state_id` = 160)
      GROUP BY
        `a`.`activity_id`
    ) `aa` ON((`aa`.`activity_id` = `g`.`activity_id`))
  )
WHERE
  (
    (`g`.`type_report` = 'Enfermedad')
    AND (`aa`.`activity_id` IS NOT NULL)
    AND (`a`.`deleted_at` IS NULL)
  )
GROUP BY
  `oc`.`group_id`,
(
    CASE
      WHEN (`psa`.`age` < 15) THEN 1
      WHEN (
        (`psa`.`age` >= 15)
        AND (`psa`.`age` < 18)
      ) THEN 15
      WHEN (
        (`psa`.`age` >= 18)
        AND (`psa`.`age` < 20)
      ) THEN 18
      WHEN (
        (`psa`.`age` >= 20)
        AND (`psa`.`age` < 25)
      ) THEN 20
      WHEN (
        (`psa`.`age` >= 25)
        AND (`psa`.`age` < 30)
      ) THEN 25
      WHEN (
        (`psa`.`age` >= 30)
        AND (`psa`.`age` < 35)
      ) THEN 30
      WHEN (
        (`psa`.`age` >= 35)
        AND (`psa`.`age` < 40)
      ) THEN 35
      WHEN (
        (`psa`.`age` >= 40)
        AND (`psa`.`age` < 45)
      ) THEN 40
      WHEN (
        (`psa`.`age` >= 45)
        AND (`psa`.`age` < 50)
      ) THEN 45
      WHEN (
        (`psa`.`age` >= 50)
        AND (`psa`.`age` < 55)
      ) THEN 50
      WHEN (
        (`psa`.`age` >= 55)
        AND (`psa`.`age` < 60)
      ) THEN 55
      WHEN (
        (`psa`.`age` >= 60)
        AND (`psa`.`age` < 65)
      ) THEN 60
      WHEN (`psa`.`age` >= 65) THEN 65
      ELSE 9
    END
  ),
  year(`g`.`created_at`)
UNION
ALL
SELECT
  (
    CASE
      WHEN (`oc`.`group_id` IS NULL) THEN 0
      ELSE `oc`.`group_id`
    END
  ) AS `grupo_ocupacion`,
(
    CASE
      WHEN (`pss`.`pro_afiliado` = 1) THEN 1
      WHEN (`pss`.`pro_afiliado` = 2) THEN 2
      WHEN (`pss`.`pro_afiliado` = 3) THEN 3
      WHEN (`pss`.`pro_afiliado` = 4) THEN 4
      WHEN (`pss`.`pro_afiliado` = 5) THEN 5
      WHEN (
        (`pss`.`pro_afiliado` >= 6)
        AND (`pss`.`pro_afiliado` <= 9)
      ) THEN 6
      WHEN (
        (`pss`.`pro_afiliado` >= 10)
        AND (`pss`.`pro_afiliado` <= 19)
      ) THEN 10
      WHEN (
        (`pss`.`pro_afiliado` >= 20)
        AND (`pss`.`pro_afiliado` <= 29)
      ) THEN 20
      WHEN (
        (`pss`.`pro_afiliado` >= 30)
        AND (`pss`.`pro_afiliado` <= 39)
      ) THEN 30
      WHEN (
        (`pss`.`pro_afiliado` >= 40)
        AND (`pss`.`pro_afiliado` <= 49)
      ) THEN 40
      WHEN (
        (`pss`.`pro_afiliado` >= 50)
        AND (`pss`.`pro_afiliado` <= 99)
      ) THEN 50
      WHEN (
        (`pss`.`pro_afiliado` >= 100)
        AND (`pss`.`pro_afiliado` <= 149)
      ) THEN 100
      WHEN (
        (`pss`.`pro_afiliado` >= 150)
        AND (`pss`.`pro_afiliado` <= 199)
      ) THEN 150
      WHEN (
        (`pss`.`pro_afiliado` >= 200)
        AND (`pss`.`pro_afiliado` <= 249)
      ) THEN 200
      WHEN (
        (`pss`.`pro_afiliado` >= 250)
        AND (`pss`.`pro_afiliado` <= 499)
      ) THEN 250
      WHEN (
        (`pss`.`pro_afiliado` >= 500)
        AND (`pss`.`pro_afiliado` <= 999)
      ) THEN 500
      WHEN (`pss`.`pro_afiliado` >= 1000) THEN 1000
      ELSE 9999
    END
  ) AS `grupo`,
  sum(
    (
      CASE
        WHEN (`psa`.`gender` IN ('F', 'M')) THEN 1
        ELSE 0
      END
    )
  ) AS `total_f`,
  sum(
    (
      CASE
        WHEN (`psa`.`gender` = 'M') THEN 1
        ELSE 0
      END
    )
  ) AS `total_hombres`,
  sum(
    (
      CASE
        WHEN (`psa`.`gender` = 'F') THEN 1
        ELSE 0
      END
    )
  ) AS `total_mujeres`,
  year(`g`.`created_at`) AS `ano`,
  'TamanosDeEmpresas' AS `TYPE`,
  4 AS `id`
FROM
  (
    (
      (
        (
          (
            (
              (
                `ebdb`.`gis_sort` `g`
                LEFT JOIN `ebdb`.`activities` `a` ON((`a`.`id` = `g`.`activity_id`))
              )
              LEFT JOIN `ebdb`.`activities` `ap` ON((`ap`.`id` = `a`.`parent_id`))
            )
            LEFT JOIN `ebdb`.`policy_sorts` `ps` ON((`ps`.`activity_id` = `ap`.`id`))
          )
          LEFT JOIN `ebdb`.`occupations` `oc` ON((`oc`.`id` = `g`.`occupancy_group`))
        )
        LEFT JOIN (
          SELECT
            `p`.`identification_number` AS `identification_number`,
            `p`.`gender` AS `gender`,
            `p`.`hours` AS `hours`,
            row_number() OVER (
              PARTITION BY `p`.`identification_number`,
              `p`.`gender`
              ORDER BY
                `p`.`id` DESC
            ) AS `rn`
          FROM
            `ebdb`.`policy_spreadsheet_affiliates` `p`
        ) `psa` ON(
          (
            (
              `psa`.`identification_number` = `g`.`number_identification_affiliate`
            )
            AND (`psa`.`rn` = 1)
          )
        )
      )
      LEFT JOIN (
        SELECT
          `a`.`parent_id` AS `parent_id`,
          round((sum(`p`.`total_affiliates`) / count(0)), 0) AS `pro_afiliado`
        FROM
          (
            `ebdb`.`policy_spreadsheets` `p`
            LEFT JOIN `ebdb`.`activities` `a` ON((`a`.`id` = `p`.`activity_id`))
          )
        WHERE
          (
            (`p`.`total_affiliates` IS NOT NULL)
            AND (`p`.`total_affiliates` <> 0)
            AND (`a`.`parent_id` IS NOT NULL)
          )
        GROUP BY
          `a`.`parent_id`
      ) `pss` ON((`pss`.`parent_id` = `ps`.`activity_id`))
    )
    LEFT JOIN (
      SELECT
        `a`.`activity_id` AS `activity_id`
      FROM
        `ebdb`.`activity_actions` `a`
      WHERE
        (`a`.`new_state_id` = 160)
      GROUP BY
        `a`.`activity_id`
    ) `aa` ON((`aa`.`activity_id` = `g`.`activity_id`))
  )
WHERE
  (
    (`g`.`type_report` = 'Enfermedad')
    AND (`pss`.`pro_afiliado` IS NOT NULL)
    AND (`aa`.`activity_id` IS NOT NULL)
    AND (`a`.`deleted_at` IS NULL)
  )
GROUP BY
  (
    CASE
      WHEN (`oc`.`group_id` IS NULL) THEN 0
      ELSE `oc`.`group_id`
    END
  ),
(
    CASE
      WHEN (`pss`.`pro_afiliado` = 1) THEN 1
      WHEN (`pss`.`pro_afiliado` = 2) THEN 2
      WHEN (`pss`.`pro_afiliado` = 3) THEN 3
      WHEN (`pss`.`pro_afiliado` = 4) THEN 4
      WHEN (`pss`.`pro_afiliado` = 5) THEN 5
      WHEN (
        (`pss`.`pro_afiliado` >= 6)
        AND (`pss`.`pro_afiliado` <= 9)
      ) THEN 6
      WHEN (
        (`pss`.`pro_afiliado` >= 10)
        AND (`pss`.`pro_afiliado` <= 19)
      ) THEN 10
      WHEN (
        (`pss`.`pro_afiliado` >= 20)
        AND (`pss`.`pro_afiliado` <= 29)
      ) THEN 20
      WHEN (
        (`pss`.`pro_afiliado` >= 30)
        AND (`pss`.`pro_afiliado` <= 39)
      ) THEN 30
      WHEN (
        (`pss`.`pro_afiliado` >= 40)
        AND (`pss`.`pro_afiliado` <= 49)
      ) THEN 40
      WHEN (
        (`pss`.`pro_afiliado` >= 50)
        AND (`pss`.`pro_afiliado` <= 99)
      ) THEN 50
      WHEN (
        (`pss`.`pro_afiliado` >= 100)
        AND (`pss`.`pro_afiliado` <= 149)
      ) THEN 100
      WHEN (
        (`pss`.`pro_afiliado` >= 150)
        AND (`pss`.`pro_afiliado` <= 199)
      ) THEN 150
      WHEN (
        (`pss`.`pro_afiliado` >= 200)
        AND (`pss`.`pro_afiliado` <= 249)
      ) THEN 200
      WHEN (
        (`pss`.`pro_afiliado` >= 250)
        AND (`pss`.`pro_afiliado` <= 499)
      ) THEN 250
      WHEN (
        (`pss`.`pro_afiliado` >= 500)
        AND (`pss`.`pro_afiliado` <= 999)
      ) THEN 500
      WHEN (`pss`.`pro_afiliado` >= 1000) THEN 1000
      ELSE 9999
    END
  ),
  year(`g`.`created_at`)