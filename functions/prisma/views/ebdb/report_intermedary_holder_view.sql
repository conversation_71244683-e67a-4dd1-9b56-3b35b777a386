SELECT
  'Cotización' AS `servicio`,
  'NO APLICA' AS `consecutivo`,
  'NO APLICA' AS `method_payment`,
  `aa_quotation`.`first_action_date` AS `created_date`,
  `aa_quotation`.`first_action_date` AS `created_at_quotation`,
  NULL AS `created_at_policy`,
  `q`.`brokerage_name` AS `brokerage_name`,
  `q`.`advisor_name` AS `advisor_name`,
  `a`.`full_name` AS `affiliate_name`,
  `q`.`type_currency` AS `currency`,
  `q`.`amount_policy` AS `amount_annual_premium`,
  'NO APLICA' AS `trabajadores`,
(
    CASE
      WHEN (`q`.`work_modality_id` = 1) THEN 'Riesgo de Trabajo General'
      WHEN (`q`.`work_modality_id` = 2) THEN 'Riesgos del Trabajo Especial Formación Técnica Dual'
      WHEN (`q`.`work_modality_id` = 3) THEN 'Riesgos del Trabajo Hogar'
      WHEN (`q`.`work_modality_id` = 4) THEN 'Riesgos del Trabajo Ocasional'
      WHEN (`q`.`work_modality_id` = 5) THEN 'Riesgos del Trabajo Sector Publico'
      ELSE 'Desconocido'
    END
  ) AS `work_modality_description`,
  `ea`.`activity_name` AS `economic_activity_name`,
  REPLACE(
    format(
      TRUNCATE(
        cast(
          REPLACE(`ea`.`percentage`, ',', '.') AS decimal(10, 8)
        ),
        2
      ),
      2
    ),
    '.',
    ','
  ) AS `economic_activity_percentage_number`,
  `s`.`name` AS `state_service`,
  'NO APLICA' AS `number_workers`,
  'NO APLICA' AS `fee`
FROM
  (
    (
      (
        (
          (
            `ebdb`.`quotations` `q`
            JOIN `ebdb`.`activities` `act` ON((`q`.`activity_id` = `act`.`id`))
          )
          JOIN (
            SELECT
              `aa_quotation`.`activity_id` AS `activity_id`,
              min(`aa_quotation`.`created_at`) AS `first_action_date`
            FROM
              `ebdb`.`activity_actions` `aa_quotation`
            WHERE
              (`aa_quotation`.`action_id` IN (3, 350))
            GROUP BY
              `aa_quotation`.`activity_id`
          ) `aa_quotation` ON((`aa_quotation`.`activity_id` = `act`.`id`))
        )
        LEFT JOIN `ebdb`.`states` `s` ON((`act`.`state_id` = `s`.`id`))
      )
      LEFT JOIN `ebdb`.`affiliates` `a` ON((`act`.`affiliate_id` = `a`.`id`))
    )
    LEFT JOIN `ebdb`.`economic_activities` `ea` ON((`ea`.`code` = `q`.`activity_economic_id`))
  )
UNION
ALL
SELECT
  'Póliza' AS `servicio`,
  concat(
    'SORT-',
(
      lpad(`ps`.`consecutive`, 4, '0') COLLATE utf8mb4_unicode_ci
    )
  ) AS `consecutivo`,
(
    CASE
      WHEN (`ps`.`temporality` = 'short') THEN 'PAGO ÚNICO'
      ELSE (
        CASE
          `ps`.`periodicity`
          WHEN 1 THEN 'ANUAL'
          WHEN 2 THEN 'SEMESTRAL'
          WHEN 3 THEN 'TRIMESTRAL'
          WHEN 4 THEN 'MENSUAL'
          ELSE 'Desconocido'
        END
      )
    END
  ) AS `method_payment`,
  `aa_policy`.`first_action_date` AS `created_date`,
  NULL AS `created_at_quotation`,
  `aa_policy`.`first_action_date` AS `created_at_policy`,
  `ps`.`brokerage_name` AS `brokerage_name`,
  `ps`.`advisor_name` AS `advisor_name`,
  `aff`.`full_name` AS `affiliate_name`,
  `ps`.`type_currency` AS `currency`,
  `ps`.`amount_policy` AS `amount_annual_premium`,
  coalesce(`pss`.`total_affiliates`, '0') AS `trabajadores`,
(
    CASE
      WHEN (`ps`.`work_modality_id` = 1) THEN 'Riesgo de Trabajo General'
      WHEN (`ps`.`work_modality_id` = 2) THEN 'Riesgos del Trabajo Especial Formación Técnica Dual'
      WHEN (`ps`.`work_modality_id` = 3) THEN 'Riesgos del Trabajo Hogar'
      WHEN (`ps`.`work_modality_id` = 4) THEN 'Riesgos del Trabajo Ocasional'
      WHEN (`ps`.`work_modality_id` = 5) THEN 'Riesgos del Trabajo Sector Publico'
      ELSE 'Desconocido'
    END
  ) AS `work_modality_description`,
  `ea`.`activity_name` AS `economic_activity_name`,
  REPLACE(
    format(
      TRUNCATE(
        cast(
          REPLACE(`ea`.`percentage`, ',', '.') AS decimal(10, 8)
        ),
        2
      ),
      2
    ),
    '.',
    ','
  ) AS `economic_activity_percentage_number`,
  `s`.`name` AS `state_service`,
  `ps`.`number_workers_optional` AS `number_workers`,
(
    CASE
      WHEN (`ps`.`temporality` = 'short') THEN `ps`.`unico_percentage`
      ELSE (
        CASE
          `ps`.`periodicity`
          WHEN 1 THEN `ps`.`anual_percentage`
          WHEN 2 THEN `ps`.`semestral_percentage`
          WHEN 3 THEN `ps`.`trimestral_percentage`
          WHEN 4 THEN `ps`.`mensual_percentage`
          ELSE NULL
        END
      )
    END
  ) AS `fee`
FROM
  (
    (
      (
        (
          (
            (
              (
                `ebdb`.`policy_sorts` `ps`
                JOIN `ebdb`.`activities` `act` ON((`ps`.`activity_id` = `act`.`id`))
              )
              JOIN (
                SELECT
                  `aa_policy`.`activity_id` AS `activity_id`,
                  min(`aa_policy`.`created_at`) AS `first_action_date`
                FROM
                  `ebdb`.`activity_actions` `aa_policy`
                WHERE
                  (`aa_policy`.`action_id` = 16)
                GROUP BY
                  `aa_policy`.`activity_id`
              ) `aa_policy` ON((`aa_policy`.`activity_id` = `act`.`id`))
            )
            LEFT JOIN `ebdb`.`states` `s` ON((`act`.`state_id` = `s`.`id`))
          )
          LEFT JOIN `ebdb`.`affiliates` `aff` ON((`act`.`affiliate_id` = `aff`.`id`))
        )
        LEFT JOIN `ebdb`.`economic_activities` `ea` ON((`ea`.`code` = `ps`.`activity_economic_id`))
      )
      JOIN (
        SELECT
          max(`apss`.`id`) AS `id_max`,
          `apss`.`parent_id` AS `parent_id`
        FROM
          `ebdb`.`activities` `apss`
        WHERE
          (
            (`apss`.`service_id` = 79)
            AND (`apss`.`state_id` IN (56, 57))
          )
        GROUP BY
          `apss`.`parent_id`
      ) `apss` ON((`apss`.`parent_id` = `act`.`id`))
    )
    JOIN `ebdb`.`policy_spreadsheets` `pss` ON((`pss`.`activity_id` = `apss`.`id_max`))
  )
WHERE
  (`act`.`state_id` <> 196)
ORDER BY
  `created_date` DESC