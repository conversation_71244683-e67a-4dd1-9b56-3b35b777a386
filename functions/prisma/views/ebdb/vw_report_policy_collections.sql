SELECT
  `ps`.`consecutive` AS `POLIZA`,
  `c`.`transaction_id` AS `ID_TRANSAC`,
  format(`c`.`total_amount`, 2) AS `MONTO`,
  date_format(`c`.`transaction_date`, '%d/%m/%Y') AS `FECHA_DE_PAGO`,
  `c`.`id` AS `NUMERO_DE_RECIBO`,
  `c`.`invoice_number` AS `NUMERO_DE_FACTURA`
FROM
  (
    (
      `ebdb`.`policy_sort_collections` `c`
      JOIN `ebdb`.`activities` `a` ON((`a`.`id` = `c`.`activity_id`))
    )
    JOIN `ebdb`.`policy_sorts` `ps` ON((`a`.`parent_id` = `ps`.`activity_id`))
  )
WHERE
  (`c`.`payment_method` = 'TC')