SELECT
  `tp`.`cuota_numero` AS `cuota_numero`,
  `tp`.`activity_id` AS `activity_policy`,
  concat('SORT-', lpad(`tp`.`consecutive`, 4, '0')) AS `consecutive_policy`,
(
    CASE
      WHEN (`tp`.`periodicity` IN ('1', '2', '3', '4')) THEN (
        CASE
          WHEN (`tp`.`cuota_numero` = 1) THEN `tp`.`validity_from`
          ELSE (
            `tp`.`validity_from` + INTERVAL lag((`tp`.`cuota_numero` * `tp`.`meses`)) OVER (
              ORDER BY
                `tp`.`consecutive`,
                `tp`.`cuota_numero`
            ) MONTH
          )
        END
      )
      ELSE `tp`.`validity_from`
    END
  ) AS `fecha_ini_recibo`,
(
    CASE
      WHEN (`tp`.`periodicity` IN ('1', '2', '3', '4')) THEN cast(
        (
          (
            CASE
              WHEN (`tp`.`periodicity` = 4) THEN (
                (
                  CASE
                    WHEN (`tp`.`cuota_numero` = 1) THEN `tp`.`validity_from`
                    ELSE (
                      `tp`.`validity_from` + INTERVAL lag((`tp`.`cuota_numero` * `tp`.`meses`)) OVER (
                        ORDER BY
                          `tp`.`consecutive`,
                          `tp`.`cuota_numero`
                      ) MONTH
                    )
                  END
                ) + INTERVAL 1 MONTH
              )
              WHEN (`tp`.`periodicity` = 3) THEN (
                (
                  CASE
                    WHEN (`tp`.`cuota_numero` = 1) THEN `tp`.`validity_from`
                    ELSE (
                      `tp`.`validity_from` + INTERVAL lag((`tp`.`cuota_numero` * `tp`.`meses`)) OVER (
                        ORDER BY
                          `tp`.`consecutive`,
                          `tp`.`cuota_numero`
                      ) MONTH
                    )
                  END
                ) + INTERVAL 3 MONTH
              )
              WHEN (`tp`.`periodicity` = 2) THEN (
                (
                  CASE
                    WHEN (`tp`.`cuota_numero` = 1) THEN `tp`.`validity_from`
                    ELSE (
                      `tp`.`validity_from` + INTERVAL lag((`tp`.`cuota_numero` * `tp`.`meses`)) OVER (
                        ORDER BY
                          `tp`.`consecutive`,
                          `tp`.`cuota_numero`
                      ) MONTH
                    )
                  END
                ) + INTERVAL 6 MONTH
              )
              ELSE (
                (
                  CASE
                    WHEN (`tp`.`cuota_numero` = 1) THEN `tp`.`validity_from`
                    ELSE (
                      `tp`.`validity_from` + INTERVAL lag((`tp`.`cuota_numero` * `tp`.`meses`)) OVER (
                        ORDER BY
                          `tp`.`consecutive`,
                          `tp`.`cuota_numero`
                      ) MONTH
                    )
                  END
                ) + INTERVAL 12 MONTH
              )
            END
          ) - INTERVAL 1 DAY
        ) AS date
      )
      ELSE `tp`.`validity_to`
    END
  ) AS `fecha_fin_recibo`,
(
    (
      CASE
        WHEN (`tp`.`periodicity` = 2) THEN 2
        WHEN (`tp`.`periodicity` = 3) THEN 4
        WHEN (`tp`.`periodicity` = 4) THEN 12
        ELSE 1
      END
    ) - coalesce(`ps`.`cant_pagos`, 0)
  ) AS `pagos_pend`,
(
    CASE
      WHEN (`tp`.`cuota_numero` <= `ps`.`cant_pagos`) THEN 'pagado'
      ELSE 'pendiente'
    END
  ) AS `estado`,
(
    (`tp`.`calculated_amount` - `ps`.`total_amount`) / (
      (
        CASE
          WHEN (`tp`.`periodicity` = 2) THEN 2
          WHEN (`tp`.`periodicity` = 3) THEN 4
          WHEN (`tp`.`periodicity` = 4) THEN 12
          ELSE 1
        END
      ) - coalesce(`ps`.`cant_pagos`, 0)
    )
  ) AS `total_pend`,
(
    (
      CASE
        WHEN (`tp`.`type_currency` = 'USD') THEN (`tp`.`calculated_amount` - `ps`.`total_amount`)
        ELSE (
          (`tp`.`calculated_amount` / `ps`.`trm_emision`) - `ps`.`total_amount_dolares`
        )
      END
    ) / (
      (
        CASE
          WHEN (`tp`.`periodicity` = 2) THEN 2
          WHEN (`tp`.`periodicity` = 3) THEN 4
          WHEN (`tp`.`periodicity` = 4) THEN 12
          ELSE 1
        END
      ) - coalesce(`ps`.`cant_pagos`, 0)
    )
  ) AS `total_pend_dolar`,
(
    (
      CASE
        WHEN (`tp`.`type_currency` <> 'USD') THEN (`tp`.`calculated_amount` - `ps`.`total_amount`)
        ELSE (
          (`tp`.`calculated_amount` * `ps`.`trm_emision`) - `ps`.`total_amount_colones`
        )
      END
    ) / (
      (
        CASE
          WHEN (`tp`.`periodicity` = 2) THEN 2
          WHEN (`tp`.`periodicity` = 3) THEN 4
          WHEN (`tp`.`periodicity` = 4) THEN 12
          ELSE 1
        END
      ) - coalesce(`ps`.`cant_pagos`, 0)
    )
  ) AS `total_pend_local`,
  `tp`.`type_currency` AS `type_currency`,
  `ps`.`trm_emision` AS `trm_emision`,
  `tp`.`fraccionamiento` AS `fraccionamiento`,
  cast(`aa`.`created_at` AS date) AS `fecha_emision`
FROM
  (
    (
      (
        SELECT
          `ebdb`.`r`.`consecutive` AS `consecutive`,
          `ebdb`.`r`.`fraccionamiento` AS `fraccionamiento`,
          `ebdb`.`r`.`cuota_numero` AS `cuota_numero`,
          `ebdb`.`r`.`calculated_amount` AS `calculated_amount`,
          `ebdb`.`r`.`validity_from` AS `validity_from`,
          `ebdb`.`r`.`validity_to` AS `validity_to`,
          `ebdb`.`r`.`periodicity` AS `periodicity`,
          `ebdb`.`r`.`meses` AS `meses`,
          `ebdb`.`r`.`activity_id` AS `activity_id`,
          `ebdb`.`r`.`type_currency` AS `type_currency`
        FROM
          `ebdb`.`report_payments_view` `r`
        WHERE
          (`ebdb`.`r`.`periodicity` IN (4, 3, 2))
      ) `tp`
      LEFT JOIN (
        SELECT
          `a`.`parent_id` AS `parent_id`,
          count(0) AS `cant_pagos`,
          sum(`psc`.`total_amount`) AS `total_amount`,
          sum(
            (
              CASE
                WHEN (`ps`.`type_currency` = 'CRC') THEN `psc`.`total_amount`
                ELSE (`psc`.`total_amount` * `psc`.`trm`)
              END
            )
          ) AS `total_amount_colones`,
          sum(
            (
              CASE
                WHEN (`ps`.`type_currency` = 'USD') THEN `psc`.`total_amount`
                ELSE (`psc`.`total_amount` / `psc`.`trm`)
              END
            )
          ) AS `total_amount_dolares`,
(
            CASE
              WHEN (
                (`psc`.`type_receipt` = 'emission')
                AND (`psc`.`payment_status` = 'approved')
              ) THEN `psc`.`trm`
            END
          ) AS `trm_emision`
        FROM
          (
            (
              `ebdb`.`policy_sort_collections` `psc`
              LEFT JOIN `ebdb`.`activities` `a` ON((`a`.`id` = `psc`.`activity_id`))
            )
            LEFT JOIN `ebdb`.`policy_sorts` `ps` ON((`ps`.`activity_id` = `a`.`parent_id`))
          )
        WHERE
          (`psc`.`payment_status` = 'approved')
        GROUP BY
          `a`.`parent_id`
      ) `ps` ON((`ps`.`parent_id` = `tp`.`activity_id`))
    )
    LEFT JOIN `ebdb`.`activity_actions` `aa` ON(
      (
        (`aa`.`activity_id` = `tp`.`activity_id`)
        AND (`aa`.`action_id` = 16)
      )
    )
  )
ORDER BY
  `tp`.`consecutive`,
  `tp`.`cuota_numero`