import { GetObjectCommand, S3Client } from "@aws-sdk/client-s3";
import { activities, policy_spreadsheets, Prisma, PrismaClient } from "@prisma/client";
import { SQSEvent } from "aws-lambda";
import * as ExcelJS from "exceljs";
import moment from "moment";
import { z } from "zod";
import { clientId } from "../constants/constants";
import {createRegisterMail, generateHtmlFromTemplate, sendEmail} from "../utils/email";
import { LogSource, sendError, sendInfo } from "../utils/logs";
import { capitalizeEachWord, excelSerialToDate, formatSortNumber } from "../utils/utils";
import {SERVICE_AFFILIATE_WORKFORCE_REPORT_MNK} from "../utils/constants";

const prisma = new PrismaClient();

const schema = z.object({
    TI: z.string().length(2),
    NACIONALIDAD: z.string().min(2).max(3).optional(),
    NO_IDENTIFICACION: z.string().min(1),
    NOMBRES: z.string().min(1),
    APELLIDOS: z.string().min(1),
    FECHA_NACIMIENTO: z.date().optional(),
    SEXO: z.string().length(1).optional(),
    CORREO_ELECTRONICO: z.string().min(1).optional(),
    TIPO_JORNADA: z.string().length(2).optional(),
    SALARIO_MENSUAL: z.number().min(0).optional(),
    DIAS: z.number().min(0).optional(),
    HORAS: z.number().min(0).optional(),
    OCUPACION: z.string().min(1).optional(),
    OBSERVACION: z.string().optional()
});

type Row = z.infer<typeof schema>;

export async function handler(event: SQSEvent): Promise<void> {
    const start = Date.now();
    let policySpreadsheet: policy_spreadsheets | null = null;
    let activity_planilla : activities | null = null;
    let activity_policy : activities | null = null;

    const policy_spreadsheet_id = event.Records[0].messageAttributes["policy_spreadsheet_id"].stringValue;

    try {
        if (!policy_spreadsheet_id) throw new Error("policy_spreadsheet_id no encontrado");

        const planillaId = Number.parseInt(policy_spreadsheet_id);
        
        policySpreadsheet = await prisma.policy_spreadsheets.findUnique({where: {id: planillaId}});

        sendInfo(LogSource.PLANILLA_AFILIADOS, `planilla ID: ${JSON.stringify(planillaId, null, 2)}`);

        if(policySpreadsheet){
            //buscamos la actividad de la planilla
            activity_planilla = await prisma.activities.findFirst({
                where: { id: policySpreadsheet.activity_id }
            });

            sendInfo(LogSource.PLANILLA_AFILIADOS, `Actividad de la planilla tomador:  ${JSON.stringify(activity_planilla, null, 2)}`);

        }

        if (activity_planilla?.parent_id) {
            activity_policy = await prisma.activities.findFirst({
                where: { id: activity_planilla.parent_id }
            });

            sendInfo(LogSource.PLANILLA_AFILIADOS, `Actividad de la poliza:  ${JSON.stringify(activity_policy,  null, 2)}`);

        }
        
        // Declarar fuera del bloque para tener acceso global en la función
        let activity_planilla_last = null;

        //Verificamos si hay una planilla anterior a esta
        if (activity_policy) {
            if (activity_policy) {
                activity_planilla_last = await prisma.activities.findFirst({
                    where: {
                        parent_id: activity_policy.id,
                        service_id: 79,
                        NOT: { id: activity_planilla?.id }
                    },
                    orderBy: { id: 'desc' },
                    include: {
                        policy_spreadsheets_policy_spreadsheets_activity_idToactivities: {
                            include: {
                                policy_spreadsheet_affiliates: true
                            }
                        }
                    }
                });

                sendInfo(LogSource.PLANILLA_AFILIADOS, `Ultima actividad de la planilla(1) que no es la actual (antepenultima):  ${JSON.stringify(activity_policy,  null, 2)}`);

            }

            sendInfo(LogSource.PLANILLA_AFILIADOS, `Ultima actividad de la planilla que no es la actual (antepenultima):  ${activity_policy}`);

        }

        if (!policySpreadsheet) throw new Error("Planilla de afiliados no encontrada");
        await changeStatus(planillaId, start, 'STARTED');
        if (!policySpreadsheet.file) throw new Error("Archivo de planilla no encontrado");

        // Descargar el archivo del S3
        await changeStatus(planillaId, start, 'DESCARGANDO_EXCEL');
        const s3Client = new S3Client({region: process.env.AWS_REGION});
        const resFile = await s3Client.send(new GetObjectCommand({
            Bucket: "mnk-" + process.env.ENVIRONMENT,
            Key: policySpreadsheet.file,
        }));
        if (!resFile.Body || !resFile.ContentType?.startsWith("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")) {
            throw new Error("Formato de archivo no compatible");
        }

        await changeStatus(planillaId, start, 'LEYENDO_EXCEL');
        const data = await resFile.Body.transformToByteArray();
        const workbook = new ExcelJS.Workbook();
        await workbook.xlsx.load(data);

        function isRowCompletelyEmpty(rowData: Record<string, any>): boolean {
            return Object.values(rowData).every(
                (value) =>
                    value === null ||
                    value === undefined ||
                    (typeof value === "string" && value.trim() === "")
            );
        }

        const worksheet = workbook.worksheets[0];
        const jsonData: Array<{ [key: string]: string | number | object | null }> = [];
        worksheet.eachRow((row, rowNumber) => {
            if (rowNumber === 1) return;
            const rowData: { [key: string]: string | number | object | null } = {};
            row.eachCell((cell, colNumber) => {
                const header = worksheet.getRow(1).getCell(colNumber).text;
                rowData[header] = cell.value as string | number | null;
            });

            // Salta la fila si está completamente vacía
            if (isRowCompletelyEmpty(rowData)) return;

            //validaciones campo TI 
            if (typeof rowData["TI"] === "object") {
                console.log(`Detalles del objeto en "TI":`, rowData["TI"]);
                sendInfo(LogSource.PLANILLA_AFILIADOS, `Campo TI:  ${rowData["TI"]}`);
                rowData["TI"] = ((rowData["TI"] as any).text || (rowData["TI"] as any).result).trim();
            } else if (typeof rowData["TI"] === "string") {
                rowData["TI"] = rowData["TI"].trim();
            }

            //Validaciones campo nacionalidad
            if (typeof rowData["NACIONALIDAD"] === "object") {
                console.log(`Detalles del objeto en NACIONALIDAD:`, rowData["NACIONALIDAD"]);
                sendInfo(LogSource.PLANILLA_AFILIADOS, `Campo nacionalidad:  ${rowData["NACIONALIDAD"]}`);
                rowData["NACIONALIDAD"] = ((rowData["NACIONALIDAD"] as any).text || (rowData["NACIONALIDAD"] as any).result).trim();
            } else if (typeof rowData["NACIONALIDAD"] === "string") {
                rowData["NACIONALIDAD"] = rowData["NACIONALIDAD"].trim();
            }
            
            //Validaciones campo numero identificación
            if (typeof rowData["NO_IDENTIFICACION"] === "object") {
                console.log(`Detalles del objeto en NO_IDENTIFICACION:`, rowData["NO_IDENTIFICACION"]);
                sendInfo(LogSource.PLANILLA_AFILIADOS, `Campo numero identificación:  ${rowData["NO_IDENTIFICACION"]}`);
                rowData["NO_IDENTIFICACION"] = ((rowData["NO_IDENTIFICACION"] as any).text || (rowData["NO_IDENTIFICACION"] as any).result).trim();
            } else if (typeof rowData["NO_IDENTIFICACION"] === "string") {
                rowData["NO_IDENTIFICACION"] = rowData["NO_IDENTIFICACION"].trim();
            }
            if (typeof rowData["NO_IDENTIFICACION"] === "number") rowData["NO_IDENTIFICACION"] = rowData["NO_IDENTIFICACION"].toString();

            //Validación de nombres
            if (typeof rowData["NOMBRES"] === "object") {
                console.log(`Detalles del objeto en NOMBRES:`, rowData["NOMBRES"]);
                sendInfo(LogSource.PLANILLA_AFILIADOS, `Campo nombres:  ${rowData["NOMBRES"]}`);
                rowData["NOMBRES"] = ((rowData["NOMBRES"] as any).text || (rowData["NOMBRES"] as any).result).trim();
            } else if (typeof rowData["NOMBRES"] === "string") {
                rowData["NOMBRES"] = rowData["NOMBRES"].trim();
            }

            //Validación de apellidos
            if (typeof rowData["APELLIDOS"] === "object") {
                console.log(`Detalles del objeto en APELLIDOS:`, rowData["APELLIDOS"]);
                sendInfo(LogSource.PLANILLA_AFILIADOS, `Campo apellidos:  ${rowData["APELLIDOS"]}`);
                rowData["APELLIDOS"] = ((rowData["APELLIDOS"] as any).text || (rowData["APELLIDOS"] as any).result).trim();
            } else if (typeof rowData["APELLIDOS"] === "string") {
                rowData["APELLIDOS"] = rowData["APELLIDOS"].trim();
            }

            //campo fecha nacimiento
            if(typeof rowData["FECHA_NACIMIENTO"] === "object"){
                console.log(`Detalles del objeto en FECHA_NACIMIENTO:`, rowData["FECHA_NACIMIENTO"]);
                sendInfo(LogSource.PLANILLA_AFILIADOS, `Campo fecha nacimiento:  ${rowData["FECHA_NACIMIENTO"]}`);
                rowData["FECHA_NACIMIENTO"] = moment.utc(rowData["FECHA_NACIMIENTO"]).format("YYYY-MM-DD");
                console.log(`Fecha nacimiento:`, rowData["FECHA_NACIMIENTO"]);
            }
            if(typeof rowData["FECHA_NACIMIENTO"] === "string") rowData["FECHA_NACIMIENTO"] = moment(rowData["FECHA_NACIMIENTO"], "YYYY-MM-DD").toDate();
            if (typeof rowData["FECHA_NACIMIENTO"] === "number") rowData["FECHA_NACIMIENTO"] = excelSerialToDate(rowData["FECHA_NACIMIENTO"]);

            //campo sexo
            if (typeof rowData["SEXO"] === "object") {
                console.log(`Detalles del objeto en SEXO:`, rowData["SEXO"]);
                sendInfo(LogSource.PLANILLA_AFILIADOS, `Campo sexo:  ${rowData["SEXO"]}`);
                rowData["SEXO"] = ((rowData["SEXO"] as any).text || (rowData["SEXO"] as any).result).trim();
            } else if (typeof rowData["SEXO"] === "string") {
                // Elimina caracteres invisibles (null, espacio, tab, etc.)
                rowData["SEXO"] = [...rowData["SEXO"]]
                    .filter(char => char.charCodeAt(0) > 31 && char.charCodeAt(0) !== 127)
                    .join('')
                    .trim();
            }

            //campo correo electrónico
            const cell = rowData["CORREO_ELECTRONICO"];
            if (typeof cell === "object" && (cell as any)?.richText) {
                // Es richText
                rowData["CORREO_ELECTRONICO"] = (cell as any).richText.map((part: any) => part.text).join("").trim();
            } else if (typeof cell === "object" && ((cell as any).text || (cell as any).result)) {
                // Es un objeto con .text o .result
                rowData["CORREO_ELECTRONICO"] = ((cell as any).text || (cell as any).result).trim();
            } else if (typeof cell === "string") {
                // Es string plano
                rowData["CORREO_ELECTRONICO"] = cell.trim();
            }

            //campo tipo jornada
            if (typeof rowData["TIPO_JORNADA"] === "object") {
                console.log(`Detalles del objeto en TIPO_JORNADA:`, rowData["TIPO_JORNADA"]);
                sendInfo(LogSource.PLANILLA_AFILIADOS, `Campo tipo jornada:  ${rowData["TIPO_JORNADA"]}`);
                rowData["TIPO_JORNADA"] = ((rowData["TIPO_JORNADA"] as any).text || (rowData["TIPO_JORNADA"] as any).result).trim();
            } else if (typeof rowData["TIPO_JORNADA"] === "string") {
                rowData["TIPO_JORNADA"] = rowData["TIPO_JORNADA"].trim();
            }

           // Campo salario mensual
            if (typeof rowData["SALARIO_MENSUAL"] === "object") {
                console.log(`Detalles del objeto en SALARIO_MENSUAL:`, rowData["SALARIO_MENSUAL"]);
                sendInfo(LogSource.PLANILLA_AFILIADOS, `Campo tipo salario mensual:  ${rowData["SALARIO_MENSUAL"]}`);
                
                // Extraemos el valor como string
                rowData["SALARIO_MENSUAL"] = (rowData["SALARIO_MENSUAL"] as any).text || (rowData["SALARIO_MENSUAL"] as any).result;
            }

            // Si es un string, reemplazar los puntos por comas
            if (typeof rowData["SALARIO_MENSUAL"] === "string") {
                console.log(`Detalles del objeto en SALARIO_MENSUAL:`, rowData["SALARIO_MENSUAL"]);
                sendInfo(LogSource.PLANILLA_AFILIADOS, `Campo tipo salario mensual:  ${rowData["SALARIO_MENSUAL"]}`);
                rowData["SALARIO_MENSUAL"] = parseFloat(rowData["SALARIO_MENSUAL"]);
            }

            //campo dias
            if(typeof rowData["DIAS"] === "object"){
                console.log(`Detalles del objeto en DIAS:`, rowData["DIAS"]);
                sendInfo(LogSource.PLANILLA_AFILIADOS, `Campo tipo dias:  ${rowData["DIAS"]}`);
                rowData["DIAS"] = (rowData["DIAS"] as any).text || (rowData["DIAS"] as any).result || 1;
            }
            // Si DIAS es menor a 1, ponerlo en 1
            if (!rowData["DIAS"] || rowData["DIAS"] < 1) {
                rowData["DIAS"] = 1;
            }
            //campo horas
            if(typeof rowData["HORAS"] === "object"){
                console.log(`Detalles del objeto en horas:`, rowData["HORAS"]);
                sendInfo(LogSource.PLANILLA_AFILIADOS, `Campo tipo horas:  ${rowData["HORAS"]}`);
                rowData["HORAS"] = (rowData["HORAS"] as any).text || (rowData["HORAS"] as any).result
            }

            //campo ocupación
            if (typeof rowData["OCUPACION"] === "object") {
                console.log(`Detalles del objeto en ocupacion:`, rowData["OCUPACION"]);
                sendInfo(LogSource.PLANILLA_AFILIADOS, `Campo tipo ocupacion:  ${rowData["OCUPACION"]}`);
                rowData["OCUPACION"] = ((rowData["OCUPACION"] as any).text || (rowData["OCUPACION"] as any).result).trim();
            } else if (typeof rowData["OCUPACION"] === "string") {
                rowData["OCUPACION"] = rowData["OCUPACION"].trim();
            }
            rowData["OCUPACION"] = String(rowData["OCUPACION"]);

            //campo observación
            if (typeof rowData["OBSERVACION"] === "object") {
                console.log(`Detalles del objeto en observacion:`, rowData["OBSERVACION"]);
                sendInfo(LogSource.PLANILLA_AFILIADOS, `Campo tipo observacion:  ${rowData["OBSERVACION"]}`);
                rowData["OBSERVACION"] = ((rowData["OBSERVACION"] as any).text || (rowData["OBSERVACION"] as any).result).trim();
            } else if (typeof rowData["OBSERVACION"] === "string") {
                rowData["OBSERVACION"] = rowData["OBSERVACION"].trim();
            }
            rowData["OBSERVACION"] = String(rowData["OBSERVACION"] || '');

            jsonData.push(rowData);
        });

        // Validar los datos
        const parsedData = schema.array().safeParse(jsonData);
        if (!parsedData.success) throw new Error(parsedData.error.message);

        // Buscar los afiliados
        await changeStatus(planillaId, start, 'BUSCANDO_AFILIADOS');
        const planilla: Row[] = parsedData.data;
        const affiliates = await prisma.affiliates.findMany({
            where: {doc_number: {in: planilla.map(affiliate => affiliate.NO_IDENTIFICACION)}}
        });
        const affiliatesMap = new Map(affiliates.map(affiliate => [affiliate.doc_type + affiliate.doc_number, affiliate]));

        // Crear o actualizar los afiliados
        await changeStatus(planillaId, start, 'ACTUALIZANDO_AFILIADOS');
        const batchSize = 50;
        let count = 0;
        for (let i = 0; i < planilla.length; i += batchSize) {
            const batch = planilla.slice(i, i + batchSize).map(p => {
                const affiliate = affiliates.find(a => a.doc_number === p.NO_IDENTIFICACION && a.doc_type === p.TI);
                const data: Prisma.affiliatesCreateInput = {
                    country: p.NACIONALIDAD?.toString(),
                    doc_number: p.NO_IDENTIFICACION,
                    first_name: p.NOMBRES,
                    last_name: p.APELLIDOS,
                    birthday: p.FECHA_NACIMIENTO,
                    gender: p.SEXO,
                    occupation: p.OCUPACION,
                    email: p.CORREO_ELECTRONICO ?? affiliate?.email ?? null,
                    doc_type: p.TI,
                    clients: {connect: {id: clientId}},
                };

                return prisma.affiliates.upsert({
                    where: {doc_type_doc_number: {doc_type: p.TI, doc_number: p.NO_IDENTIFICACION}},
                    create: {
                        ...data,
                        created_at: new Date(),
                        updated_at: new Date(),
                    },
                    update: {
                        ...data,
                        updated_at: new Date()
                    },
                });
            });
            const res = await Promise.all(batch);
            res.forEach(a => {
                affiliatesMap.set(a.doc_type + a.doc_number, a);
            });
            count += batch.length;
            await prisma.policy_spreadsheets.update({
                where: {id: planillaId},
                data: {
                    updated_at: new Date(),
                    total_affiliate_uploaded: count
                }
            });
        }

        let affiliatesWithBenefit = null;

        sendInfo(LogSource.PLANILLA_AFILIADOS, `Antepenúltima planillas: ${JSON.stringify(activity_planilla_last, null, 2)}`);

        // Extraer solo los registros de afiliados de la planilla anterior que hayan sido reportado como beneficios de colectividad
        if(activity_planilla_last){
            affiliatesWithBenefit = activity_planilla_last?.policy_spreadsheets_policy_spreadsheets_activity_idToactivities
                ?.flatMap(planilla => planilla.policy_spreadsheet_affiliates || [])  
                .filter(benefit => benefit.benefit_colective && ['Sí', 'Si'].includes(benefit.benefit_colective)) || [];

                sendInfo(LogSource.PLANILLA_AFILIADOS, `Antepenúltima planilla: ${JSON.stringify(activity_planilla_last, null, 2)}`);
                sendInfo(LogSource.PLANILLA_AFILIADOS, `Afiliados con beneficios antepenultima planilla: ${JSON.stringify(affiliatesWithBenefit, null, 2)}`);

        }

        sendInfo(LogSource.PLANILLA_AFILIADOS, `Afiliados con beneficios antepenultima planilla: ${JSON.stringify(affiliatesWithBenefit, null, 2)}`);

        // Comparar identificaciones
        sendInfo(LogSource.PLANILLA_AFILIADOS, `Afiliados planilla anterior Beneficios de colectividad filtrados: ${JSON.stringify(affiliatesWithBenefit, null, 2)}`);

        const newAffiliatesIds = planilla.map(affiliate => affiliate.NO_IDENTIFICACION ?? '');

        sendInfo(LogSource.PLANILLA_AFILIADOS, `Afiliados nueva planilla cargada: ${JSON.stringify(newAffiliatesIds, null, 2)}`);

        // Identificar afiliados que están en la planilla anterior pero no en la nueva
        const affiliatesToUpdateAffilate = affiliatesWithBenefit?.filter(affiliate => 
            !newAffiliatesIds.includes(affiliate.identification_number ?? '')
        );

        // Identificar afiliados que están en la planilla anterior pero no en la nueva
        const affiliatesToUpdateGis = affiliatesWithBenefit?.filter(affiliate => 
            !newAffiliatesIds.includes(affiliate.identification_number ?? '')
        );

        sendInfo(LogSource.PLANILLA_AFILIADOS, `Afiliados que estan en la planilla anterior marcados con colectividad pero no estan en la planilla nueva (afiliados y gis): ${JSON.stringify(affiliatesToUpdateAffilate, null, 2)}`);

        // Actualizar al afiliado para mantener un historial
        if (affiliatesToUpdateAffilate?.length) {
            await prisma.policy_spreadsheet_affiliates.updateMany({
                where: {
                    identification_number: { 
                        in: affiliatesToUpdateAffilate
                        .map(affiliate => affiliate.identification_number ?? '')
                    }
                },
                data: {
                    observation_colective: 'Estaba asegurado por beneficio pero no fue reportado en la planilla'
                }
            });
        }

        //Enviar correo de casos no asegurados
        if (affiliatesToUpdateGis?.length) {
            const gisIds = affiliatesToUpdateGis
                .map(affiliate => affiliate.gis_id)
                .filter(gis_id => typeof gis_id === 'number');

            // Actualizar los registros de GIS con caso no asegurando en NO
            await prisma.gis_sort.updateMany({
                where: {
                    id: {
                        in: affiliatesToUpdateGis
                            .map(affiliate => affiliate.gis_id)
                            .filter((gis_id): gis_id is number => typeof gis_id === 'number')
                    }
                },
                data: {
                    insured_case: 'No'
                }
            });

            //limpiar nulls
            const filteredGisIds = gisIds.filter((gis_id): gis_id is number => typeof gis_id === 'number');

            // Obtener los GIS con fecha de accidente y la activity
            const gisRecords = await prisma.gis_sort.findMany({
                where: {
                    id: { in: filteredGisIds }
                },
                select: {
                    id: true,
                    date_accident: true,
                    activity_id : true
                }
            });

            //Obtener las activitidades de los GIS
            const activityGis = await prisma.activities.findFirst({
                where: {
                    id: gisRecords[0].activity_id
                }
            });

            //Obtener las actividades de la póliza sort
            let activityPolicy = null;
            if (activityGis?.parent_id != null) {
                activityPolicy = await prisma.activities.findFirst({
                    where: {
                        id: activityGis.parent_id
                    },
                    include: {
                        affiliates: true
                    }
                });
            }

            //Obtener la póliza sort relacionada al GIS
            let policySort = null;
            if (activityPolicy) {
                policySort = await prisma.policy_sorts.findFirst({
                    where: {
                        activity_id: activityPolicy.id
                    }
                });
            }

            //Correo del tomador de la póliza
            const emailTaker = activityPolicy?.affiliates.email;
            //Correo del intermediario de la póliza
            const emailIntermediary = policySort?.email;


            // Enviar el correo a cada afiliado + tomador + intermediario del caso no asegurado
            for (const affiliate of affiliatesToUpdateGis) {
                const email = affiliate.email || '';
                const policyConsecutive = formatSortNumber(policySort?.consecutive || 0);
                const takerName = capitalizeEachWord(activityPolicy?.affiliates.full_name || '');
                const affiliateName = capitalizeEachWord(affiliate.full_name || '');
                const affiliateIdentification = affiliate.identification_number || '';
                const gisId = affiliate.gis_id;

                const gis = gisRecords.find(g => g.id === gisId);

                const eventDate = gis?.date_accident
                    ? moment(gis.date_accident).locale('es').format('D [de] MMMM [del] YYYY')
                    : 'Sin fecha de accidente';

                if (email) {
                    const bodyEmail = `
                        <p>
                            <strong>Cobro de caso no asegurado en la póliza #${policyConsecutive|| ''}</strong>
                        </p>
                        <p>
                            ¡Buen día, ${takerName}!
                        </p>
                        <p>
                            Lamentamos informarle que, tras la revisión de nuestra parte, hemos identificado que la persona trabajadora ${affiliateName}, con número de identificación ${affiliateIdentification}, no fue reportado en las planillas presentadas de previo a la ocurrencia del evento (o en la planilla correspondiente al mes de ingreso la persona trabajadora).
                        </p>
                        <p>
                            Por este motivo, el caso del Seguro Obligatorio de Riesgos del Trabajo N° ${gisId}, por el evento ocurrido el ${eventDate}, deberá tramitarse como “no asegurado”, según los términos
                            de los artículos 216, 231, 232 y 307 del Código de Trabajo; por lo que el pago de todas las prestaciones que MNK Seguros suministre a la persona trabajadora no asegurada estará exclusivamente a su cargo como Tomador del contrato, por lo cual procederemos con el cálculo y cobro respectivo, el cual se le estará comunicando oportunamente.
                        </p>
                        <p>
                            En caso de cualquier consulta, por favor contáctenos <NAME_EMAIL>, o al teléfono 4102-7600 extensiones 8129-8130.
                            ¡Será un gusto servirle!
                        </p>
                        <p>
                        Nuestro propósito es fortalecer la prevención en salud y seguridad laboral del país, así como proteger a sus colaboradores en el momento que más lo necesitan, generando siempre
                        bienestar.
                        </p>
                            Atentamente,
                            <br>
                            <strong>Área de Indemnizaciones</strong>
                            <br>
                            <strong>Seguro Obligatorio de Riesgos del Trabajo</strong>
                            <br>
                             <strong>MNK Seguros</strong>
                        </p>
                    `;

                    const emailHtml  = generateHtmlFromTemplate(
                        "docs/report.hbs",
                        {
                            body: bodyEmail
                        }
                    );


                    const emails = [email, emailTaker, emailIntermediary].filter(e => !!e) as string[];

                    for (const recipient of emails) {
                        await sendEmail(
                            recipient,
                            "Notificación del caso no asegurado en la póliza",
                            emailHtml,
                            [],
                            true
                        );


                        try {

                            await sendEmail(
                                recipient,
                                "Notificación del caso no asegurado en la póliza",
                                emailHtml,
                                [],
                                true
                            );

                            const emailResult = {
                                status: 'Enviado',
                                message: 'Email enviado correctamente'
                            };


                            await createRegisterMail(
                                Number(policySpreadsheet.activity_id || 0),
                                SERVICE_AFFILIATE_WORKFORCE_REPORT_MNK,
                                String(policySort?.consecutive || ''),
                                'Asegurado',
                                affiliateName,
                                String(affiliateIdentification || ''),
                                "Notificación del caso no asegurado en la póliza",
                                emailHtml,
                                [recipient],
                                emailResult,
                                [],
                                null,
                                'evicertica'
                            );


                        } catch (error) {

                            const emailResult = {
                                status: 'Reboto',
                                message: 'Error al enviar email'
                            };

                            await createRegisterMail(
                                Number(policySpreadsheet.activity_id || 0),
                                SERVICE_AFFILIATE_WORKFORCE_REPORT_MNK,
                                String(policySort?.consecutive || ''),
                                'Asegurado',
                                affiliateName,
                                String(affiliateIdentification || ''),
                                "Notificación del caso no asegurado en la póliza",
                                emailHtml,
                                [recipient],
                                emailResult,
                                [],
                                null,
                                'evicertica'
                            );

                        }


                    }
                }
            }
        }

        // Actualizar la planilla
        await changeStatus(planillaId, start, 'CREANDO_PLANILLA');
        //eliminar solo afiliados que no sean temporales de planilla
        await prisma.policy_spreadsheet_affiliates.deleteMany({
            where: {
                policy_spreadsheet_id: planillaId,
                temporal: false
            }
        });
        for (const value of planilla) {
            const existingAffiliate = await prisma.policy_spreadsheet_affiliates.findFirst({
                where: {
                    policy_spreadsheet_id: planillaId,
                    identification_number: value.NO_IDENTIFICACION
                }
            });

            if (existingAffiliate) {
                await prisma.policy_spreadsheet_affiliates.updateMany({
                    where: {
                        policy_spreadsheet_id: planillaId,
                        identification_number: value.NO_IDENTIFICACION
                    },
                    data: {
                        first_name: value.NOMBRES,
                        last_name: value.APELLIDOS,
                        date_of_birth: value.FECHA_NACIMIENTO,
                        gender: value.SEXO,
                        work_shift_type: value.TIPO_JORNADA,
                        monthly_salary: value.SALARIO_MENSUAL,
                        days: value.DIAS,
                        hours: value.HORAS,
                        occupation: value.OCUPACION,
                        email: value.CORREO_ELECTRONICO ?? affiliatesMap.get(value.TI + value.NO_IDENTIFICACION)?.email ?? null,
                        observation_affiliate: value.OBSERVACION,
                        updated_at: new Date()
                    }
                });
            } else {
                await prisma.policy_spreadsheet_affiliates.create({
                    data: {
                        policy_spreadsheet_id: planillaId!,
                        affiliate_id: affiliatesMap.get(value.TI + value.NO_IDENTIFICACION)!.id,
                        name: value.NOMBRES,
                        created_at: new Date(),
                        updated_at: new Date(),
                        id_type: value.TI,
                        nationality: value.NACIONALIDAD?.toString(),
                        identification_number: value.NO_IDENTIFICACION,
                        first_name: value.NOMBRES,
                        last_name: value.APELLIDOS,
                        date_of_birth: value.FECHA_NACIMIENTO,
                        gender: value.SEXO,
                        work_shift_type: value.TIPO_JORNADA,
                        monthly_salary: value.SALARIO_MENSUAL,
                        days: value.DIAS,
                        hours: value.HORAS,
                        occupation: value.OCUPACION,
                        email: value.CORREO_ELECTRONICO ?? affiliatesMap.get(value.TI + value.NO_IDENTIFICACION)?.email ?? null,
                        observation_affiliate: value.OBSERVACION
                    }
                });
            }
        }


        await changeStatus(planillaId, start, 'COMPLETED');
        await prisma.activities.update({
            where: { id: policySpreadsheet.activity_id },
            data: {
                updated_at: new Date(),
            }
        });


        await prisma.$disconnect();
        await sendInfo(LogSource.PLANILLA_AFILIADOS, `Planilla Afiliados cargada: ${planillaId} con ${planilla.length} afiliados`);
    } catch (e: any) {
        if (policySpreadsheet) {
            await prisma.policy_spreadsheets.update({
                where: {id: policySpreadsheet.id},
                data: {
                    error_affiliate_uploaded: e.toString(),
                    status_affiliate_uploaded: 'FAILED',
                    ms_affiliate_uploaded: Date.now() - start,
                    updated_at: new Date(),
                }
            });
        }
        await prisma.$disconnect();
        await sendError(LogSource.PLANILLA_AFILIADOS, "Error al generar la planilla: " + policy_spreadsheet_id, e);
    }
}

async function changeStatus(planillaId: number, start: number, status: 'STARTED' | 'DESCARGANDO_EXCEL' | 'LEYENDO_EXCEL' | 'BUSCANDO_AFILIADOS' | 'ACTUALIZANDO_AFILIADOS' | 'CREANDO_PLANILLA' | 'COMPLETED' | 'FAILED') {
    await sendInfo(LogSource.PLANILLA_AFILIADOS, `Status de planilla inicial: ${planillaId} cambiado a ${status}`);
    return prisma.policy_spreadsheets.update({
        where: {id: planillaId},
        data: {
            status_affiliate_uploaded: status,
            error_affiliate_uploaded: null,
            ms_affiliate_uploaded: Date.now() - start,
            total_affiliate_uploaded: status === 'STARTED' ? 0 : undefined,
            updated_at: new Date(),
        }
    });
}