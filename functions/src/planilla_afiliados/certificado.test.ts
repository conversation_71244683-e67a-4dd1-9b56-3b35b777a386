import { SQSEvent } from "aws-lambda";
import {handler} from "./certificado";

require("dotenv").config();


describe("Planilla certificados", () => {
    let event: SQSEvent;

    beforeEach(() => {
        event = {
            Records: [
                {
                    messageId: "some-id",
                    receiptHandle: "some-receipt-handle",
                    body: "Hello from SQS!",
                    attributes: {
                        ApproximateReceiveCount: "1",
                        SentTimestamp: "1523232000000",
                        SenderId: "123456789012",
                        ApproximateFirstReceiveTimestamp: "1523232000001"
                    },
                    messageAttributes: {
                        policy_spreadsheet_id: {
                            stringValue: "259",
                            dataType: "String"
                        },
                        consecutive: {
                            stringValue: "49",
                            dataType: "String"
                        },
                        user_id: {
                            stringValue: "1",
                            dataType: "String"
                        },
                        exclude_email: {
                            stringValue: "false",
                            dataType: "String"
                        }
                    },
                    md5OfBody: "some-md5",
                    eventSource: "aws:sqs",
                    eventSourceARN: "arn:aws:sqs:us-east-1:123456789012:MyQueue",
                    awsRegion: process.env.AWS_REGION || "us-east-1"
                }
            ]
        };
    });

    it("should not throw", async () => {
        await expect(handler(event)).resolves.not.toThrow();
    });
});
