import {policy_spreadsheets, Prisma, PrismaClient} from "@prisma/client";
import {SQSEvent} from "aws-lambda";
import {LogSource, sendError, sendInfo} from "../utils/logs";
import {clientId} from "../constants/constants";
import {sendEmail, createRegisterMail} from "../utils/email";
import {generatePdf} from "../utils/pdf";
import {capitalizeEachWord, capitalizeFirstLetter, formatNumber, currencyIcon, formatSortNumber} from "../utils/utils";
import moment from 'moment-timezone'; 
import {PutObjectCommand, S3Client} from "@aws-sdk/client-s3";
import 'moment/locale/es';
import {loadCostaRicaData} from "../utils/load-costarica";
import {Canton, Distrito} from "../types";
import {SERVICE_AFFILIATE_WORKFORCE_REPORT_MNK} from "../utils/constants";

const prisma = new PrismaClient();

function chunkArray<T>(array: T[], size: number): T[][] {
    return Array.from({length: Math.ceil(array.length / size)}, (_, i) =>
        array.slice(i * size, i * size + size)
    );
}

export async function handler(event: SQSEvent): Promise<void> {
    moment.tz.setDefault('America/Costa_Rica');
    moment.locale('es');
    const start = Date.now();
    let policySpreadsheet: policy_spreadsheets | null = null;
    const policy_spreadsheet_id = event.Records[0].messageAttributes["policy_spreadsheet_id"].stringValue;
    const consecutive = event.Records[0].messageAttributes["consecutive"].stringValue;
    const period = event.Records[0].messageAttributes["period"] ? event.Records[0].messageAttributes["period"].stringValue : "Emisión";
    const exclude_email = event.Records[0].messageAttributes["exclude_email"]?.stringValue === "true";
    await sendInfo(LogSource.PLANILLA_AFILIADOS, `Iniciando certificados de afiliados para planilla ${policy_spreadsheet_id}`);

    try {
        if (!policy_spreadsheet_id) throw new Error("policy_spreadsheet_id no encontrado");

        const planillaId = Number.parseInt(policy_spreadsheet_id);
        policySpreadsheet = await prisma.policy_spreadsheets.findUnique({where: {id: planillaId}});
        if (!policySpreadsheet) throw new Error("Planilla de afiliados no encontrada");

        const activityPolicySpreadsheet = await prisma.activities.findUnique({
            where: {id: policySpreadsheet.activity_id}
        });
        if (!activityPolicySpreadsheet) throw new Error("Actividad de planilla de afiliados no encontrada");

        if (!activityPolicySpreadsheet.parent_id) {
            throw new Error("Actividad padre de planilla de afiliados no encontrada");
        }


        const tomador = await prisma.activities.findUnique({
            where: {
                id: activityPolicySpreadsheet.parent_id,
            },
            select: {
                affiliates: {
                    select: {
                        full_name: true,
                        phone: true,
                        email: true,
                        address: true,
                        province: true,
                        canton: true,
                        district: true,
                        employer_address: true,
                        first_name: true,
                    },
                },
                policy_sorts: {
                    select: {
                        id: true,
                        type_currency: true,
                        consecutive: true,
                        validity_from: true,
                        doc_number: true,
                    },
                },
            },
        });

        let provincia = '';
        let canto = '';
        let distrito = '';

        if (tomador?.affiliates) {
            const {
                province,
                canton,
                district
            } = tomador?.affiliates || {};

            const costaRicaData = await loadCostaRicaData();
            const provinciaData = costaRicaData.province.find(
                (p) => p.code === province
            );

            const cantonIndex = String(canton) || "0";
            const districtIndex = String(district) || "0";

            if (provinciaData) {
                provincia = provinciaData.name;
                const cantonData = provinciaData.cantons.find(
                    (c: Canton) => c.code === cantonIndex
                );
                if (cantonData) {
                    const distritoData = cantonData.districts.find(
                        (d: Distrito) => d.code === districtIndex
                    );
                    canto = cantonData.name;
                    distrito = capitalizeEachWord(distritoData?.name);
                }
            }
        }
        const direccionTomador = `${provincia}, ${canto}, ${distrito}, ${tomador?.affiliates?.employer_address}`

        const userId = activityPolicySpreadsheet.user_id;

        // Obtener afiliados de la planilla
        await sendInfo(LogSource.PLANILLA_AFILIADOS, `Obteniendo afiliados de la planilla ${planillaId}`);
        const affiliates = await prisma.policy_spreadsheet_affiliates.findMany({
            where: {policy_spreadsheet_id: planillaId},
        });

        const SERVICE_AFFILIATE_WORKFORCE_REPORT_MNK = 80;
        const GENERAR_CERTIFICADO_AFILIADO = 86;
        const CERTIFICADO_AFILIACION_REPORTADO = 59;

        // Crear actividades en lotes de 1000
        await sendInfo(LogSource.PLANILLA_AFILIADOS, `Creando actividades en lotes para la planilla ${planillaId}`);
        const createActivities: Prisma.activitiesCreateManyInput[] = affiliates.map(afiliado => ({
            parent_id: policySpreadsheet!.activity_id,
            client_id: clientId,
            affiliate_id: Number(afiliado.affiliate_id),
            service_id: SERVICE_AFFILIATE_WORKFORCE_REPORT_MNK,
            state_id: CERTIFICADO_AFILIACION_REPORTADO,
            user_id: userId,
            created_at: new Date(),
            updated_at: new Date(),
        }));
        for (const chunk of chunkArray(createActivities, 500)) {
            await prisma.activities.createMany({data: chunk});
        }

        // Crear acciones en lotes de 1000
        await sendInfo(LogSource.PLANILLA_AFILIADOS, `Creando acciones en lotes para la planilla ${planillaId}`);
        const activities = await prisma.activities.findMany({
            where: {parent_id: policySpreadsheet.activity_id},
        });

        const createActions: Prisma.activity_actionsCreateManyInput[] = activities.map(activity => ({
            activity_id: activity.id,
            action_id: GENERAR_CERTIFICADO_AFILIADO,
            old_state_id: activity.state_id,
            new_state_id: CERTIFICADO_AFILIACION_REPORTADO,
            description: 'GENERAR_CERTIFICADO_AFILIADO',
            old_user_id: activity.user_id,
            new_user_id: userId,
            author_id: userId,
        }));
        for (const chunk of chunkArray(createActions, 500)) {
            await prisma.activity_actions.createMany({data: chunk});
        }

        // Enviar correos en lotes de 1000
        await sendInfo(LogSource.PLANILLA_AFILIADOS, `Enviando correos en lotes para la planilla ${planillaId}`);

        // const date = new Date();
        const currentDate = moment().tz("America/Costa_Rica").format('DD/MM/YYYY');
        const currentTime = moment().tz("America/Costa_Rica").format('HH:mm:ss');
        const consecutiveAsNumber = tomador?.policy_sorts?.[0].consecutive;
        const consecutiveFormat = formatSortNumber(consecutiveAsNumber);

        const DOC_TYPES: { [key: string]: string } = {
            CC: "Cédula de ciudadanía",
            NU: "Número único de identificación personal",
            NI: "Nit",
            TI: "Tarjeta de identidad",
            CE: "Cédula de extranjería",
            PA: "Pasaporte",
            RC: "Registro civil",
            AS: "Adulto sin identificación",
            MS: "Menor sin identificación",
            F: "Documento extranjero",
            PE: "Permiso especial de permanencia (pep)",
            SS: "Adulto sin identificación",
            EE: "Extranjero",
            PT: "Permiso por protección temporal (ppt)",
            SE: "Sociedad extranjera sin nit en colombia",
            PP: "Permiso por protección temporal (pp)",
            DI: "Dimex",
            CJ: "Cédula jurídica",
            CD: "Carnet diplomático",
            CR: "Cédula de residencia",
            CF: "Cédula física"
        };

        for (const chunk of chunkArray(affiliates.filter(a => a.email), 30)) {//mas de 30 no envia todos los correos
            try {
                await Promise.all(
                    chunk.map(async affiliate => {
                        const createdAt = moment(tomador?.policy_sorts?.[0].validity_from).format('DD/MM/YYYY');
                        const dateOfBirth = moment(affiliate?.date_of_birth).format('DD/MM/YYYY');
                        const idType = affiliate?.id_type;
                        const docTypeDescription = DOC_TYPES[idType || ''] || "TIPO DESCONOCIDO";
                        const currency = currencyIcon(tomador?.policy_sorts?.[0].type_currency || '');
                        const monthlySalary = formatNumber(Number(affiliate?.monthly_salary));
                        const nameTomador = capitalizeEachWord(tomador?.affiliates.full_name);
                        const nameAffiliate = capitalizeEachWord(affiliate.full_name);
                        const textOccupation = capitalizeFirstLetter(String(affiliate.occupation));
                        const documentTaker = tomador?.policy_sorts?.[0].doc_number || '';
                        const firstName = capitalizeEachWord(affiliate.first_name);
                        const identificationNumber = affiliate.identification_number;


                        Object.assign(affiliate, {
                            tomador,
                            currentDate,
                            currentTime,
                            createdAt,
                            dateOfBirth,
                            docTypeDescription,
                            consecutiveFormat,
                            monthlySalary,
                            currency,
                            nameTomador,
                            nameAffiliate,
                            textOccupation,
                            direccionTomador,
                            documentTaker,
                            period
                        });

                        const file = await generatePdf('docs/certificados/affiliation_certificate_pdf.hbs', affiliate);
                        // subir a S3
                        const filename = `certificado-afiliacion-${affiliate.id}-${moment().format("YYYY-MM-DD-HH-mm-ss")}.pdf`;
                        await new S3Client({region: process.env.AWS_REGION}).send(
                            new PutObjectCommand({
                                Body: file,
                                Bucket: process.env.BUCKET_NAME,
                                Key: `activity_action_document/${filename}`,
                            })
                        );

                        const activityActionsId = await prisma.activity_actions.findFirst({
                            where: {
                                activities: {
                                    parent_id: policySpreadsheet?.activity_id,
                                    affiliate_id: Number(affiliate.affiliate_id),
                                },
                            },
                            orderBy: {
                                id: 'desc',
                            },
                        });

                        if (activityActionsId) {
                            await prisma.activity_action_documents.create({
                                data: {
                                    activity_action_id: Number(activityActionsId.id),
                                    name: 'Certificado_afiliado',
                                    path: `activity_action_document/${filename}`,
                                },
                            });

                        }

                        if(exclude_email) return;

                        const fileUrl = `https://${process.env.BUCKET_NAME}.s3.amazonaws.com/activity_action_document/${filename}`;
                        const emailBody = `
                        <p>¡Buen día, ${firstName}!</p>
                        <p>Nos complace adjuntarle su constancia de aseguramiento correspondiente a la póliza del Seguro Obligatorio de Riesgos del Trabajo SORT-${consecutive}, a nombre de ${nameTomador}.</p>
                        <p>Por favor, si tiene alguna duda o consulta al respecto, contáctenos al correo electrónico <EMAIL> o al teléfono 4102-7600. ¡Para nosotros será un gusto servirle!</p>
                        <p>Nuestro propósito es fortalecer la prevención en salud y seguridad laboral del país, así como proteger a sus colaboradores en el momento que más lo necesitan, generando siempre bienestar.</p>
                        <p>Cordialmente,</p>
                        <p><b>Área de Aseguramiento<br>Seguro Obligatorio de Riesgos del Trabajo<br>MNK Seguros</b></p>
                        `;

                        try {

                             await sendEmail(
                                affiliate.email!,
                                `Constancia de aseguramiento de ${nameAffiliate}`,
                                emailBody,
                                [fileUrl]
                            );

                            const emailResult = {
                                status: 'Enviado',
                                message: 'Email enviado correctamente'
                            };

                            const files = [
                                {
                                    type: 'pdf',
                                    path: `activity_action_document/${filename}`,
                                    name: filename
                                }
                            ];

                            await createRegisterMail(
                                Number(activityActionsId?.activity_id || 0),
                                SERVICE_AFFILIATE_WORKFORCE_REPORT_MNK,
                                String(consecutive || ''),
                                'Asegurado',
                                nameAffiliate,
                                String(identificationNumber || ''),
                                `Constancia de aseguramiento de ${nameAffiliate}`,
                                emailBody,
                                [affiliate.email!],
                                emailResult,
                                files,
                                null
                            );


                        } catch (error) {

                            const emailResult = {
                                status: 'Reboto',
                                message: 'Error al enviar email'
                            };

                            const files = [
                                {
                                    type: 'pdf',
                                    path: `activity_action_document/${filename}`,
                                    name: filename
                                }
                            ];

                            await createRegisterMail(
                                Number(activityActionsId?.activity_id || 0),
                                SERVICE_AFFILIATE_WORKFORCE_REPORT_MNK,
                                String(consecutive || ''),
                                'Asegurado',
                                nameAffiliate,
                                String(identificationNumber || ''),
                                `Constancia de aseguramiento de ${nameAffiliate}`,
                                emailBody,
                                [affiliate.email!],
                                emailResult,
                                files,
                                null
                            );

                            throw error;
                        }


                    })
                );
            } catch (error) {
                await sendError(LogSource.PLANILLA_AFILIADOS, `Error al procesar un lote: ${error}`, error);
            }
        }

        await sendInfo(LogSource.PLANILLA_AFILIADOS, `Correos enviados con exito!`);

    } catch (e: any) {
        if (policySpreadsheet) {
            await prisma.policy_spreadsheets.update({
                where: {id: policySpreadsheet.id},
                data: {
                    error_affiliate_uploaded: e.toString(),
                    status_affiliate_uploaded: 'FAILED_GENERATE_CERTIFICATES',
                    ms_affiliate_uploaded: Date.now() - start,
                    updated_at: new Date(),
                }
            });
        }
        await prisma.$disconnect();
        await sendError(LogSource.PLANILLA_AFILIADOS, "Error al generar certificados de la planilla: " + policy_spreadsheet_id, e);
    }
}