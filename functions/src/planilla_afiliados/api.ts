import { SendMessageCommand, SendMessageCommandInput, SQSClient } from "@aws-sdk/client-sqs";
import { APIGatewayEvent } from "aws-lambda";
import cors from "cors";
import express, { NextFunction, Request, Response } from "express";
import jwt from "jsonwebtoken";
import serverless from "serverless-http";
import { LogSource, sendError, sendInfo, sendLogApiEvent } from "../utils/logs";

const app = express();
const router = express.Router();  // Define un router para aplicar el prefijo
const sqsClient = new SQSClient({region: process.env.AWS_REGION});

// Middleware 1: Parsear el body en JSON
app.use(express.json());

// Middleware 2: Agregar CORS
app.use(cors());

// Middleware 3: Verificar JWT (para todas las rutas bajo `/aivo`)
router.use((req: Request, res: Response, next: NextFunction): void => {
    const token = req.headers.authorization?.split(' ')[1]; // Espera el token JWT en el header "Authorization: Bearer <token>"
    if (!token) {
        res.status(401).send({
            success: false,
            message: "Acceso no autorizado. Token no proporcionado."
        });
        return;
    }
    const secret = process.env.JWT_SECRET_RENAPP;
    if (!secret) {
        throw new Error("JWT_SECRET_RENAPP no configurado: " + secret);
    }
    jwt.verify(token, secret, (err) => {
        if (err) {
            res.status(401).send({
                success: false,
                message: "Acceso no autorizado. Token no válido."
            });
            return;
        }
        next();
    });
});

router.post('/cargar', async function (req, res) {
    const {policy_spreadsheet_id} = req.body;
    if (!policy_spreadsheet_id) {
        res.status(400).send({
            success: false,
            message: "Falta el id de la planilla de afiliados."
        });
        return;
    }
    const params: SendMessageCommandInput = {
        MessageBody: `Carga de planilla de afiliados: ${policy_spreadsheet_id}`,
        QueueUrl: process.env.SQS_PLANILLA, // Reemplaza con tu URL de la cola
        MessageAttributes: {
            policy_spreadsheet_id: {
                DataType: "String",
                StringValue: policy_spreadsheet_id
            }
        }
    };

    try {
        const data = await sqsClient.send(new SendMessageCommand(params));
        await sendInfo(LogSource.PLANILLA_AFILIADOS, `Carga de planilla de afiliados: ${policy_spreadsheet_id}`, data);
    } catch (err) {
        await sendError(LogSource.PLANILLA_AFILIADOS, "Error al enviar el mensaje", err);
        throw err;
    }
    res.send({
        success: true,
        message: "Carga de planilla de afiliados exitosa. Se ha enviado un mensaje de SQS.",
    });
});

router.post('/certificados', async function (req, res) {
    const {policy_spreadsheet_id, consecutive, period, exclude_email} = req.body;
    if (!policy_spreadsheet_id || !consecutive) {
        res.status(400).send({
            success: false,
            message: "Falta el id de la planilla de afiliados."
        });
        return;
    }
    const params: SendMessageCommandInput = {
        MessageBody: `Certificados de planilla de afiliados: ${policy_spreadsheet_id}`,
        QueueUrl: process.env.SQS_PLANILLA_CERTIFICADO, // Reemplaza con tu URL de la cola
        MessageAttributes: {
            policy_spreadsheet_id: {
                DataType: "String",
                StringValue: policy_spreadsheet_id
            },
            consecutive: {
                DataType: "String",
                StringValue: consecutive
            }
        }
    };
    if (period) {
        params.MessageAttributes.period = {
            DataType: "String",
            StringValue: period
        };
    }
    if (exclude_email) {
        params.MessageAttributes.exclude_email = {
            DataType: "String",
            StringValue: "true"
        };
    }


    console.log('-->', process.env.SQS_PLANILLA_CERTIFICADO);

    try {
        const data = await sqsClient.send(new SendMessageCommand(params));
        await sendInfo(LogSource.PLANILLA_AFILIADOS, `Certificados de planilla de afiliados: ${policy_spreadsheet_id}`, data);
    } catch (err) {
        await sendError(LogSource.PLANILLA_AFILIADOS, "Error al enviar el mensaje", err);
        throw err;
    }
    res.send({
        success: true,
        message: "Certificados de planilla de afiliados exitosa. Se ha enviado un mensaje de SQS.",
    });
});


router.post('/certificadoAffiliados', async function (req, res) {
    const {activity_planilla,exclude_email,exclude_activity} = req.body;

    const params: SendMessageCommandInput = {
        MessageBody: `Certificados de planilla de afiliados: ${activity_planilla}`,
        QueueUrl: process.env.SQS_PLANILLA_CERTIFICADO, // Reemplaza con tu URL de la cola
        MessageAttributes: {
            activity_planilla: {
                DataType: "String",
                StringValue: activity_planilla
            },
            exclude_email: {
                DataType: "String",
                StringValue: exclude_email
            },
            exclude_activity: {
                DataType: "String",
                StringValue: exclude_activity
            }
        }
    };

    try {
        const data = await sqsClient.send(new SendMessageCommand(params));
        await sendInfo(LogSource.PLANILLA_AFILIADOS, `Certificados de planilla de afiliados: ${activity_planilla}`, data);
    } catch (err) {
        await sendError(LogSource.PLANILLA_AFILIADOS, "Error al enviar el mensaje", err);
        throw err;
    }
    res.send({
        success: true,
        message: "Certificados de planilla de afiliados exitosa. Se ha enviado un mensaje de SQS.",
    });
});

//cargar txt
router.post('/cargar_txt', async function (req, res) {
    const {policy_spreadsheet_id} = req.body;
    if (!policy_spreadsheet_id) {
        res.status(400).send({
            success: false,
            message: "Falta el id de la planilla de afiliados."
        });
        return;
    }
    const params: SendMessageCommandInput = {
        MessageBody: `Carga de planilla de afiliados: ${policy_spreadsheet_id}`,
        QueueUrl: process.env.SQS_CARGAR_TXT, // Reemplaza con tu URL de la cola
        MessageAttributes: {
            policy_spreadsheet_id: {
                DataType: "String",
                StringValue: policy_spreadsheet_id
            }
        }
    };

    try {
        const data = await sqsClient.send(new SendMessageCommand(params));
        await sendInfo(LogSource.PLANILLA_AFILIADOS, `Carga de planilla de afiliados: ${policy_spreadsheet_id}`, data);
    } catch (err) {
        await sendError(LogSource.PLANILLA_AFILIADOS, "Error al enviar el mensaje", err);
        throw err;
    }
    res.send({
        success: true,
        message: "Carga de planilla de afiliados exitosa. Se ha enviado un mensaje de SQS.",
    });
});

// Middleware 4: Capturar errores globalmente para todas las rutas
// eslint-disable-next-line @typescript-eslint/no-unused-vars
app.use(async (err: Error, req: Request, res: Response, next: NextFunction): Promise<void> => {
    // Enviar el error a Slack (u otra herramienta de monitoreo)
    await sendError(LogSource.PLANILLA_AFILIADOS, err.toString(), err);
    // Responder con un mensaje de error genérico
    res.status(500).send({
        message: "Ocurrió un error interno. Por favor, inténtalo más tarde.",
        error: err.message,
        body: req.body,
        params: req.params,
    });
});

// Aplica el prefijo `/aivo` a todas las rutas del router
app.use('/planilla_afiliados', router);

export const handler = async (event: APIGatewayEvent, context: any) => {
    const start = Date.now();
    const response: any = await serverless(app)(event, context);
    const duration_ms = Date.now() - start;
    await sendLogApiEvent(LogSource.PLANILLA_AFILIADOS, event, response, duration_ms);
    return response;
};