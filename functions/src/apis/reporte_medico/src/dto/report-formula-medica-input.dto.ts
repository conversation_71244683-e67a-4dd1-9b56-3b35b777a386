import {IsNumberString, IsOptional, IsString} from "class-validator";

export class ReportFormulaMedicaInputDto {
    @IsOptional()
    @IsNumberString()
    provincia?: string;

    @IsOptional()
    @IsNumberString()
    canton?: string;

    @IsOptional()
    @IsNumberString()
    distrito?: string;

    @IsOptional()
    @IsString()
    origen_diagnosticos?: string;

    @IsOptional()
    @IsString()
    codigo_vademecum?: string;

    @IsOptional()
    @IsString()
    nombre_vademecum?: string;

    @IsOptional()
    @IsString()
    duracion_del_tratamiento?: string;

    @IsOptional()
    @IsString()
    frecuencia?: string;

    @IsOptional()
    @IsString()
    dosis_via_de_administracion?: string;

    @IsOptional()
    @IsString()
    cantidad_prescrita?: string;

    @IsOptional()
    @IsString()
    notas_aclaratorias?: string;
}