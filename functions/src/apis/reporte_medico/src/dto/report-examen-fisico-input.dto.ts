import {IsNotEmpty, IsPositive, IsString, IsInt} from "class-validator";
import {IsBloodPressure} from "../../../../utils/decorators/is-blood-pressure.decorator";

export class ReportExamenFisicoInputDto {
    @IsNotEmpty()
    @IsPositive()
    talla!: number;

    @IsNotEmpty()
    @IsPositive()
    peso!: number;

    @IsNotEmpty()
    @IsBloodPressure()
    ta_presion_arterial!: string;

    @IsNotEmpty()
    @IsPositive()
    @IsInt()
    fc_frecuencia_cardiaca!: number;

    @IsNotEmpty()
    @IsPositive()
    @IsInt()
    fr_frecuencia_respiratoria!: number;

    @IsNotEmpty()
    @IsString()
    descripcion_arcos_movimientos!: string;
}