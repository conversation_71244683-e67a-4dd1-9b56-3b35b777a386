import {Inject, Injectable} from '@nestjs/common';
import {PrismaService} from '../../../../utils/prisma/prisma.service';
import {createRegisterMail, generateHtmlFromTemplate, sendEmail} from '../../../../utils/email';
import {GenerateEmissionDto} from './dto/generate-emission.dto';
import moment from 'moment/moment';
import {PutObjectCommand, S3Client} from '@aws-sdk/client-s3';
import {generatePdf} from '../../../../utils/pdf';
import {
    loadCostaRicaData,
    loadActivityPublic,
    loadActivityPrivate,
} from '../../../../utils/load-costarica';
import {
    capitalizeEachWord,
    capitalizeFirstLetter,
    formatNumber,
    formatSortNumber,
    getS3Url,
    isNullOrUndefined,
    registerHelpers
} from '../../../../utils/utils';
import {Canton, Distrito} from 'src/types';

import {
    SendMessageCommand,
    SendMessageCommandInput,
    SQSClient,
} from '@aws-sdk/client-sqs';
import {LogSource, sendError, sendInfo} from 'src/utils/logs';
import {SERVICE_REPORT_TAKEN_FORM_MNK} from "../../../../utils/constants";

const sqsClient = new SQSClient({region: process.env.AWS_REGION});

@Injectable()
export class PolicyService {
    constructor(@Inject(PrismaService) private readonly prisma: PrismaService) {
    }

    async generateEmissionPdf(body: GenerateEmissionDto): Promise<any> {
        const result = await this.prisma.activities.findFirst({
            where: {id: body.activity_id},
            select: {
                id: true,
                parent_id: true,
                affiliates: {
                    select: {
                        first_name: true,
                        full_name: true,
                        last_name: true,
                        doc_number: true,
                        doc_type: true,
                        email: true,
                        occupation: true,
                        electronic_billing_email: true,
                        province: true,
                        canton: true,
                        district: true,
                        occupation_responsible: true,
                        employer_address: true,
                        phone: true,
                        cellphone: true,
                        name_responsible: true,
                    },
                },
                policy_sorts: {
                    select: {
                        id: true,
                        activity_economic_id: true,
                        legal_representative_name: true,
                        legal_representative_id: true,
                        validity_from: true,
                        validity_to: true,
                        calendar_period: true,
                        email: true,
                        advisor_name: true,
                        brokerage_name: true,
                        type_currency: true,
                        temporality: true,
                        periodicity: true,
                        amount_policy: true,
                        economic_activity: true,
                        consecutive: true,
                        unique_code: true,
                        anual_percentage: true,
                        semestral_percentage: true,
                        trimestral_percentage: true,
                        mensual_percentage: true,
                        unico_percentage: true,
                    },
                },
            },
        });

        //se pone en true en caso de solo querer enviar los documentos sin generar acciones
        const ejecutarDocLocal = false;

        if (result) {
            let nameEconomicActivity = '';
            let activities = [];

            if (result.policy_sorts?.[0].economic_activity === 'public') {
                activities = await loadActivityPublic();
            } else {
                activities = await loadActivityPrivate();
            }

            const matchedActivity = activities.find(
                (activity) =>
                    activity.CODE === result.policy_sorts?.[0].activity_economic_id
            );

            if (matchedActivity) {
                nameEconomicActivity = matchedActivity.ACTIVITY_NAME;
            }

            let provincia = null;
            let canto = null;
            let distrito = null;
            let full_name_word = '';
            let occupation_word = '';
            let first_name_word = '';

            if (result?.affiliates) {
                const {
                    province,
                    canton,
                    district,
                    full_name,
                    occupation,
                    first_name,
                } = result?.affiliates || {};

                full_name_word = capitalizeEachWord(full_name);
                first_name_word = capitalizeEachWord(first_name);
                occupation_word = capitalizeFirstLetter(occupation);
                const costaRicaData = await loadCostaRicaData();
                const provinciaData = costaRicaData.province.find(
                    (p) => p.code === province
                );

                const cantonIndex = String(canton) || '0';
                const districtIndex = String(district) || '0';

                if (provinciaData) {
                    provincia = provinciaData.name;
                    const cantonData = provinciaData.cantons.find(
                        (c: Canton) => c.code === cantonIndex
                    );
                    if (cantonData) {
                        const distritoData = cantonData.districts.find(
                            (d: Distrito) => d.code === districtIndex
                        );
                        canto = cantonData.name;
                        distrito = capitalizeEachWord(distritoData?.name);
                    }
                }
            }

            const date = new Date();
            const currentDate = moment(date).format('DD/MM/YYYY');
            const today = moment();
            const day = today.format('DD');
            const month = today.format('MMMM');
            const year = today.format('YYYY');
            const url_env = process.env.URL_ENV;

            Object.assign(result, {
                provincia,
                canto,
                distrito,
                full_name_word,
                occupation_word,
                first_name_word,
            });
            Object.assign(result, {currentDate, date, day, month, year});
            Object.assign(result, {nameEconomicActivity});

            const EMITIR_POLIZA_DESDE_POLIZA = 16;
            const activityActions = await this.prisma.activity_actions.findFirst({
                where: {
                    activity_id: body.activity_id,
                    action_id: EMITIR_POLIZA_DESDE_POLIZA,
                },
                orderBy: {
                    created_at: 'desc',
                },
            });

            const fileUrl = await this.condicionesParticulares(
                body,
                result,
                activityActions,
                ejecutarDocLocal
            );
            console.log('DOC_CONDICIONES->' + fileUrl);
            const fileUrlTwo = await this.firmaDocument(
                body,
                result,
                activityActions,
                ejecutarDocLocal
            );

            console.log('DOC_FIRMA->' + fileUrlTwo);

            const representante = capitalizeEachWord(result?.affiliates.full_name);
            const sortNumber = formatSortNumber(
                result.policy_sorts?.[0].consecutive || 0
            );


            const isPlanilla = await this.prisma.activities.count({
                where: {
                    parent_id: body.activity_id,
                    service_id: SERVICE_REPORT_TAKEN_FORM_MNK
                }
            });

            const collection = await this.prisma.policy_sort_collections.findFirst({
                select: {
                    type_receipt: true
                },
                where: {
                    activities: {
                        parent_id: body.activity_id
                    }
                },
                orderBy: {
                    id: 'desc'
                }
            });

            const typeReceipt = collection ? collection.type_receipt : "";


            let subject = `Emisión de la póliza #${sortNumber}`;
            let cuerpoEmail = '<p> Le invitamos a verificar la siguiente información importante: </p>';

            if (isPlanilla == 0 && typeReceipt == 'emission') {

                subject = `Emisión de la póliza #${sortNumber} sin reporte de planilla`;

                cuerpoEmail = `<p>
                                Sin embargo, hemos notado que está pendiente la carga del reporte de planilla. Le agradecemos mucho que, por favor, nos envíe este documento antes del inicio de operaciones. De lo contrario, en caso de que ocurra un riesgo laboral a uno de sus colaboradores, tendremos que tramitar el evento como “no asegurado” y proceder al cobro del costo total de su atención.
                               </p>
                               <p>
                                De igual forma, le invitamos a verificar la siguiente información importante:
                               </p>`; }
            let bodyEmail = `
                    <p>
                    ¡Buen día, ${representante}!
                    </p>
                    <p>
                      Nos complace informarle que hemos emitido su póliza #${sortNumber}.
                    </p>          
                     ${cuerpoEmail}
                      <ul>
                          <li>Condiciones particulares de su seguro.</li>
                          <li>Norma técnica <a href='https://mnk-prod.s3.us-east-1.amazonaws.com/public/Norma+te%CC%81cnica+Seguro+Obligatorio+de+Riesgos+del+Trabajo.pdf'>aquí</a>.</li>
                          <li>Condiciones generales de su póliza, a las cuales puede acceder <a href='https://mnk-prod.s3.us-east-1.amazonaws.com/public/Condiciones+generales+Seguro+Obligatorio+de+Riesgos+del+Trabajo.pdf'>aquí</a>.</li>
                          <li>Seguro obligatorio de riesgos del trabajo.</li>
                      </ul>
                      <p>
                          Además, le solicitamos que para realizar sus reportes de planillas, inclusiones o riesgos laborales, ingrese <a href='${url_env}'>aquí</a> con su usuario y contraseña, por favor.
                      </p>
                      <p>
                          Su usuario y contraseña son los siguientes:
                          <br>
                          Usuario: ${body.user_id}
                          <br>
                          Contraseña: ${body.password}
                          <br>
                          Código único(Aviso del caso): ${result.policy_sorts?.[0].unique_code}
                          <br>
                          Acceso a la plataforma: <a href="${url_env}/login">${url_env}/login</a>
                      </p>
                      <p>
                          Nos sentimos sumamente honrados y agradecidos por la confianza que ha depositado en nosotros. Nuestro propósito es transformar la protección en una experiencia ágil, confiable y humana.
                      </p>
                     <p>Cordialmente,</p>
                     <p>
                        <strong>Área de Aseguramiento <strong><br>
                        <strong>Seguro Obligatorio de Riesgos del Trabajo<strong><br>
                        <strong>MNK seguros<strong><br>
                     </p>
          `;

            const emailTemplate = generateHtmlFromTemplate(
                "docs/report.hbs",
                {
                    body: bodyEmail
                }
            );


            try {

                await sendEmail(
                    result.affiliates?.email || '',
                    subject,
                    emailTemplate,
                    [fileUrl, fileUrlTwo],
                    true
                );

                const emailResult = {
                    status: 'Enviado',
                    message: 'Email enviado correctamente'
                };

                const files = [
                    {
                        type: 'pdf',
                        path: fileUrl,
                        name: 'condiciones_particulares'
                    },
                    {
                        type: 'pdf',
                        path: fileUrlTwo,
                        name: 'Firma Física'
                    }
                ];

                await createRegisterMail(
                    Number(result?.id || 0),
                    75,
                    String(result.policy_sorts[0]?.consecutive || ''),
                    'Tomador',
                    representante,
                    String(result?.affiliates.doc_number || ''),
                    subject,
                    emailTemplate,
                    [result.affiliates?.email || ''],
                    emailResult,
                    files,
                    null,
                    'evicertica'
                );


            } catch (error) {

                const emailResult = {
                    status: 'Reboto',
                    message: 'Error al enviar email'
                };


                const files = [
                    {
                        type: 'pdf',
                        path: fileUrl,
                        name: 'condiciones_particulares'
                    },
                    {
                        type: 'pdf',
                        path: fileUrlTwo,
                        name: 'Firma Física'
                    }
                ];

                await createRegisterMail(
                    Number(result?.id || 0),
                    75,
                    String(result.policy_sorts[0]?.consecutive || ''),
                    'Tomador',
                    representante,
                    String(result?.affiliates.doc_number || ''),
                    subject,
                    emailTemplate,
                    [result.affiliates?.email || ''],
                    emailResult,
                    files,
                    null,
                    'evicertica'
                );
            }



            //intermediario
            const intermediary = capitalizeEachWord(
                result.policy_sorts?.[0].advisor_name || ''
            );

            //Nombre del tomador
            const takerName = capitalizeEachWord(
                result.affiliates?.full_name
            );

            const emailTemplateIntermediary = generateHtmlFromTemplate(
                'docs/report_intermediary.hbs',
                {
                    intermediary: intermediary,
                    taker: representante,
                    policy: result.policy_sorts?.[0].consecutive,
                    url_env: url_env,
                    linkUrl: fileUrl,
                    linkUrlTwo: fileUrlTwo,
                }
            );


            try {

                await sendEmail(
                    result.policy_sorts?.[0].email || '',
                    `Emisión de la póliza del cliente ${takerName}`,
                    emailTemplateIntermediary,
                    [fileUrl, fileUrlTwo],
                    true
                );

                const emailResult = {
                    status: 'Enviado',
                    message: 'Email enviado correctamente'
                };

                const files = [
                    {
                        type: 'pdf',
                        path: fileUrl,
                        name: 'condiciones_particulares'
                    },
                    {
                        type: 'pdf',
                        path: fileUrlTwo,
                        name: 'Firma Física'
                    }
                ];

                await createRegisterMail(
                    Number(result?.id || 0),
                    75,
                    String(result.policy_sorts[0]?.consecutive || ''),
                    'Intermediario',
                    intermediary,
                    String(result?.affiliates.doc_number || ''),
                    `Emisión de la póliza del cliente ${takerName}`,
                    emailTemplateIntermediary,
                    [result.policy_sorts?.[0].email || ''],
                    emailResult,
                    files,
                    null,
                    'evicertica'
                );


            } catch (error) {

                const emailResult = {
                    status: 'Reboto',
                    message: 'Error al enviar email'
                };


                const files = [
                    {
                        type: 'pdf',
                        path: fileUrl,
                        name: 'condiciones_particulares'
                    },
                    {
                        type: 'pdf',
                        path: fileUrlTwo,
                        name: 'Firma Física'
                    }
                ];

                await createRegisterMail(
                    Number(result?.id || 0),
                    75,
                    String(result.policy_sorts[0]?.consecutive || ''),
                    'Intermediario',
                    intermediary,
                    String(result?.affiliates.doc_number || ''),
                    `Emisión de la póliza del cliente ${takerName}`,
                    emailTemplateIntermediary,
                    [result.policy_sorts?.[0].email || ''],
                    emailResult,
                    files,
                    null,
                    'evicertica'
                );
            }


        } else {

            throw `activity_id ${body.activity_id} no existe`;
        }

        return {
            status: 200,
            success: true,
            message: 'La póliza se generó y envió exitosamente.',
        };
    }

    private async uniqueCodeContats(
        result: any,
        representante: string
    ): Promise<any> {
        const sortNumber = formatSortNumber(
            result.policy_sorts[0]?.consecutive || 0
        );
        const policy_id = result.policy_sorts[0]?.id || 0;

        const policyContacts = await this.prisma.policy_contacts.findMany({
            where: {
                policy_sort_id: Number(policy_id),
            },
            select: {
                name_responsible: true,
                email_responsible: true,
                number_identify_responsible: true,
            },
        });

        for (let contact of policyContacts) {
            const nameResponsible = capitalizeEachWord(contact.name_responsible);
            const uniqueCode = contact.number_identify_responsible;

            const bodyEmail = `
              <p>
                  <strong>Notificación del código único seguro de riesgo laboral</strong>
              </p>
              <p>
                  Estimado/a ${nameResponsible},
              </p>
              <p>
                  Nos dirigimos a usted para informarle que ha sido registrado en nustro sistema del Seguro Obligatorio de Riesgo del Trabajo, como autorizado para realizar el reporte de accidente de la póliza 
                  <strong>${sortNumber}</strong> a nombre de ${representante}. A continuación, encontrará su código único de identificación:
              </p>
              <p>
                  <strong>Código Único: ${uniqueCode} </strong>
                  <br>
                  Este código es personal e intransferible.
              </p>
              <p>
                  Si tiene alguna pregunta o necesita más información, no dude en ponerse en contacto mediante el correo <a href="mailto:<EMAIL>"><EMAIL></a> o al teléfono 4102-7681.
              </p>
              <p>
                  Atentamente,
                  <br>
                  <strong>Área de Aseguramiento</strong>
                  <br>
                  Gerencia Riesgos del Trabajo
                  <br>
                  MNK Seguros
              </p>
          `;

            const emailUnicode = generateHtmlFromTemplate('docs/report.hbs', {
                body: bodyEmail,
            });

            await sendEmail(
                contact.email_responsible || '',
                'Notificación del código único seguro de riesgo laboral',
                emailUnicode,
                [],
                true
            );
        }
    }

    private async firmaDocument(
        body: GenerateEmissionDto,
        result: any,
        activityActions: any,
        ejecutarDocLocal: boolean
    ): Promise<any> {
        const REPORTAR_DOCUMENTOS_EXTERNOS_SIN_PLANILLAS = 311;
        const SERVICE_REPORT_TAKEN_FORM_MNK = 79;
        const REPORTAR_FIRMA_FISICA = 179;
        const REPORTAR_FIRMA_DIGITAL = 178;

        const signature_document_fisica =
            await this.prisma.activity_actions.findFirst({
                where: {
                    activity_id: body.activity_id,
                    action_id: REPORTAR_FIRMA_FISICA,
                },
                orderBy: {
                    created_at: 'desc',
                },
            });

        if (signature_document_fisica && activityActions?.id) {
            const lastDocument = await this.prisma.activity_documents.findFirst({
                where: {
                    activity_id: body.activity_id,
                    document_id: 218,
                },
            });

            if (!ejecutarDocLocal) {
                await this.prisma.activity_action_documents.create({
                    data: {
                        activity_action_id: activityActions.id,
                        name: `Firma Física_${activityActions?.id}`,
                        path: lastDocument?.path || '',
                    },
                });
            }

            return (
                `https://${process.env.BUCKET_NAME}.s3.amazonaws.com/` +
                lastDocument?.path || ''
            );
        }

        const signature_document_digital =
            await this.prisma.activity_actions.findFirst({
                where: {
                    activity_id: body.activity_id,
                    action_id: REPORTAR_FIRMA_DIGITAL,
                },
                orderBy: {
                    created_at: 'desc',
                },
            });

        if (signature_document_digital) {
            const isplanilla =
                (await this.prisma.activity_actions.count({
                    where: {
                        activity_id: body.activity_id,
                        action_id: REPORTAR_DOCUMENTOS_EXTERNOS_SIN_PLANILLAS,
                    },
                })) > 0;

            let policy_spreadsheets = await this.prisma.policy_spreadsheets.findFirst(
                {
                    where: {
                        activities_policy_spreadsheets_activity_idToactivities: {
                            parent_id: body.activity_id,
                            service_id: SERVICE_REPORT_TAKEN_FORM_MNK,
                        },
                    },
                    select: {
                        total_salaries: true,
                        observacion: true,
                    },
                }
            );

            let checkOne = '';
            let checkTwo = '';
            let checkThree = '';

            if (!isplanilla && result?.policy_sorts?.[0]?.calendar_period == '1') {
                checkOne = 'checked';
            }
            if (!isplanilla && result?.policy_sorts?.[0]?.calendar_period == '2') {
                checkTwo = 'checked';
            }
            if (isplanilla) {
                checkThree = 'checked';
            }

            let total_salaries = '';
            if (!isNullOrUndefined(policy_spreadsheets?.total_salaries)) {
                total_salaries = formatNumber(
                    Number(policy_spreadsheets?.total_salaries)
                );
            }

            const fromDate = moment
                .utc(result?.policy_sorts?.[0]?.validity_from)
                .format('DD/MM/YYYY');
            const fromDay = moment
                .utc(result?.policy_sorts?.[0]?.validity_from)
                .format('DD');
            const fromMonth = moment
                .utc(result?.policy_sorts?.[0]?.validity_from)
                .format('MMMM');
            const fromYear = moment
                .utc(result?.policy_sorts?.[0]?.validity_from)
                .format('YYYY');

            const toDate = moment
                .utc(result?.policy_sorts?.[0]?.validity_to)
                .format('DD/MM/YYYY');
            const toDay = moment
                .utc(result?.policy_sorts?.[0]?.validity_to)
                .format('DD');
            const toMonth = moment
                .utc(result?.policy_sorts?.[0]?.validity_to)
                .format('MMMM');
            const toYear = moment
                .utc(result?.policy_sorts?.[0]?.validity_to)
                .format('YYYY');

            const sign1_url = getS3Url(
                'policy_sort/sign1_' + body.activity_id + '.png'
            );
            const sign2_url = getS3Url(
                'policy_sort/sign2_' + body.activity_id + '.png'
            );

            Object.assign(result, {checkThree});
            Object.assign(result, {checkOne});
            Object.assign(result, {checkTwo});
            Object.assign(result, {policy_spreadsheets, total_salaries});
            Object.assign(result, {sign1_url});
            Object.assign(result, {sign2_url});
            Object.assign(result, {fromDate});
            Object.assign(result, {fromDay});
            Object.assign(result, {fromMonth});
            Object.assign(result, {fromYear});
            Object.assign(result, {toDate});
            Object.assign(result, {toDay});
            Object.assign(result, {toMonth});
            Object.assign(result, {toYear});
            Object.assign(result, {body});

            const file = await generatePdf(
                'docs/poliza/download_signature_mnk.hbs',
                result
            );
            // subir a S3
            const filename = `activity_action_document/poliza-${moment().format('YYYY-MM-DD-HH-mm-ss')}.pdf`;

            await new S3Client({region: process.env.AWS_REGION}).send(
                new PutObjectCommand({
                    Body: file,
                    Bucket: process.env.BUCKET_NAME,
                    Key: filename,
                })
            );

            const fileUrl = `https://${process.env.BUCKET_NAME}.s3.amazonaws.com/${filename}`;

            if (activityActions?.id && !ejecutarDocLocal) {
                await this.prisma.activity_action_documents.create({
                    data: {
                        activity_action_id: activityActions.id,
                        name: `Firma digital_${activityActions.id}`,
                        path: filename,
                    },
                });
            }

            return fileUrl;
        } else {
            throw 'Error: No se pudo generar el documento de firma';
        }
    }

    private async condicionesParticulares(
        body: GenerateEmissionDto,
        result: any,
        activityActions: any,
        ejecutarDocLocal: boolean
    ): Promise<string> {
        const currencyLabel =
            result.policy_sorts[0]?.type_currency === 'USD' ? 'Dólares' : 'Colones';
        const valueToFormat =
            result.policy_sorts[0]?.consecutive || result.policy_sorts[0]?.id;
        const sortNumber = valueToFormat ? formatSortNumber(valueToFormat) : '';
        const fromDate = moment
            .utc(result?.policy_sorts?.[0]?.validity_from)
            .format('DD/MM/YYYY');
        const fromDay = moment
            .utc(result?.policy_sorts?.[0]?.validity_from)
            .format('DD');
        const fromMonth = moment
            .utc(result?.policy_sorts?.[0]?.validity_from)
            .locale('es')
            .format('MMMM');
        const fromYear = moment
            .utc(result?.policy_sorts?.[0]?.validity_from)
            .format('YYYY');
        const toDate = moment
            .utc(result?.policy_sorts?.[0]?.validity_to)
            .format('DD/MM/YYYY');
        const toDateRenova = moment
            .utc(result?.policy_sorts?.[0]?.validity_to)
            .add(1, 'days')
            .format('DD/MM/YYYY');
        const toDay = moment
            .utc(result?.policy_sorts?.[0]?.validity_to)
            .format('DD');
        const toMonth = moment
            .utc(result?.policy_sorts?.[0]?.validity_to)
            .format('MMMM');
        const toYear = moment
            .utc(result?.policy_sorts?.[0]?.validity_to)
            .format('YYYY');

        //const total_amount = `${result.policy_sorts[0]?.type_currency === "USD" ? "$" : "₡"} ${formatNumber(result?.policy_sorts?.[0]?.amount_policy)}`;
        const total_amount = formatNumber(result?.policy_sorts?.[0]?.amount_policy);

        const temporality =
            result?.policy_sorts?.[0]?.temporality == "permanent"
                ? "Permanente"
                : "Periodo Corto";
        const periodicity = result?.policy_sorts?.[0]?.periodicity;
        const representante =
            result?.affiliates.doc_type === 'CJ'
                ? capitalizeEachWord(result?.affiliates.name_responsible)
                : capitalizeEachWord(result?.affiliates.full_name);

        var estimacion_prima = Number(result?.policy_sorts?.[0]?.amount_policy);
        var tem = body.tem;
        let textPeriodicity = '';
        var rFration = 0;

        if (periodicity == 0 || periodicity == null) {
            textPeriodicity = "Pago único";
            tem = result?.policy_sorts?.[0]?.unico_percentage;
            rFration = 0;
        } else if (periodicity == 1) {
            textPeriodicity = 'Anual';
            tem = result?.policy_sorts?.[0]?.anual_percentage;
            rFration = 0;
        } else if (periodicity == 2) {
            textPeriodicity = 'Semestral';
            estimacion_prima = Number(estimacion_prima) * 2;
            tem = result?.policy_sorts?.[0]?.semestral_percentage;
            rFration = 4;
        } else if (periodicity == 3) {
            textPeriodicity = 'Trimestral';
            estimacion_prima = Number(estimacion_prima) * 4;
            tem = result?.policy_sorts?.[0]?.trimestral_percentage;
            rFration = 6;
        } else if (periodicity == 4) {
            textPeriodicity = 'Mensual';
            estimacion_prima = Number(estimacion_prima) * 12;
            tem = result?.policy_sorts?.[0]?.mensual_percentage;
            rFration = 8;
        }

        var estimacion = formatNumber(estimacion_prima);
        const currency = result.policy_sorts[0]?.type_currency;

        Object.assign(result, {
            currencyLabel,
            sortNumber,
            fromDate,
            fromDay,
            fromMonth,
            fromYear,
            toDate,
            toDay,
            toMonth,
            toYear,
            temporality,
            textPeriodicity,
            total_amount,
            representante,
            toDateRenova,
            estimacion,
            tem,
            currency,
            rFration
        });

        console.log(result);

        const file = await generatePdf(
            'docs/poliza/condiciones_particulares_nuevo.hbs',
            result
        );
        const filename = `activity_action_document/condiciones-${moment().format('YYYY-MM-DD-HH-mm-ss')}.pdf`;

        await new S3Client({region: process.env.AWS_REGION}).send(
            new PutObjectCommand({
                Body: file,
                Bucket: process.env.BUCKET_NAME,
                Key: filename,
            })
        );

        if (activityActions?.id && !ejecutarDocLocal) {
            await this.prisma.activity_action_documents.create({
                data: {
                    activity_action_id: activityActions.id,
                    name: `condiciones_particulares_${activityActions.id}`,
                    path: filename,
                },
            });
        }

        return `https://${process.env.BUCKET_NAME}.s3.amazonaws.com/${filename}`;
    }

    async generateEmissionDocuments(body: GenerateEmissionDto): Promise<void> {
        const {activity_id, tem, password, user_id,consecutive} = body;

        const params: SendMessageCommandInput = {
            MessageBody: `Correo planilla definitiva: ${activity_id ?? ''}`,
            QueueUrl: process.env.SQS_EMISION_POLICE,
            MessageAttributes: {
                activity_id: {
                    DataType: 'String',
                    StringValue: activity_id.toString(),
                },
                user_id: {
                    DataType: 'String',
                    StringValue: user_id.toString(),
                },
                password: {
                    DataType: 'String',
                    StringValue: password,
                },
                tem: {
                    DataType: 'String',
                    StringValue: tem.toString(),
                },
                consecutive: {
                    DataType: 'String',
                    StringValue: consecutive.toString(),
                }
            },
        };

        try {
            const data = await sqsClient.send(new SendMessageCommand(params));
            await sendInfo(
                LogSource.REPORTS,
                `Se ha enviado un mensaje de SQS emision_police_queue: ${activity_id}`,
                data
            );
        } catch (err) {
            await sendError(
                LogSource.REPORTS,
                'Error al enviar el mensaje SQS emision_police_queue',
                err
            );
            throw err;
        }
    }

    async generateCommunicationDocument(): Promise<void> {

        //consultar actividades (pólizas) con el estado "Póliza emitida activa"
        const activities = await this.prisma.activities.findMany({
            where: {
                state_id: 20
            },
            select: {
                id: true,
            }
        })

        const promises = activities.map(async (activity) => {
            const params: SendMessageCommandInput = {
                MessageBody: `Correo con el documento comunicado: ${activity.id ?? ''}`,
                QueueUrl: process.env.SQS_COMMUNICATION_POLICE,
                MessageAttributes: {
                    activity_id: {
                        DataType: 'String',
                        StringValue: activity.id.toString(),
                    }
                },
            };

            try {
                const data = await sqsClient.send(new SendMessageCommand(params));
                await sendInfo(
                    LogSource.REPORTS,
                    `Se ha enviado un mensaje de SQS communication_police_queue: ${activity.id}`,
                    data
                );
            } catch (err) {
                await sendError(
                    LogSource.REPORTS,
                    'Error al enviar el mensaje SQS communication_police_queue',
                    err
                );
            }
        });

        await Promise.all(promises); // ejecutar en paralelo
    }
}
