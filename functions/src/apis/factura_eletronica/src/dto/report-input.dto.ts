import {Type} from "class-transformer";
import {IsArray, IsNotEmpty, IsNumberString, ValidateNested} from "class-validator";

import {ReportDocumentosInputDto} from "./report-documentos-input.dto";

export class ReportInput {

    @IsNotEmpty()
    @IsNumberString()
    NumCuenta!: string;

    @IsNotEmpty()
    @IsArray()
    @ValidateNested({each: true})
    @Type(() => ReportDocumentosInputDto)
    Documentos!: ReportDocumentosInputDto[];


}

