import {NestFactory} from '@nestjs/core';
import {ExpressAdapter, NestExpressApplication,} from '@nestjs/platform-express';
import {Context, Handler} from 'aws-lambda';
import express, {Response} from 'express';
import serverless from 'serverless-http';
import bodyParser from 'body-parser';

import {ValidationPipe} from "@nestjs/common";
import {LoggingException} from "../../../utils/logging/logging.exception";
import {LoggingHttp} from "../../../utils/logging/logging-http";
import {logger, LoggingService} from "../../../utils/logging/logging.service";
import {AppModule} from "./app.module";
import {LogSource, logTail} from "../../../utils/logs";

let server: Handler;

async function bootstrap(): Promise<Handler> {
    const expressApp = express();
    const app = await NestFactory.create<NestExpressApplication>(
        AppModule,
        new ExpressAdapter(expressApp),
        {logger: new LoggingService(LogSource.FUNCTIONS)},
    );
    app.use(bodyParser.json({limit: '10mb'}));
    app.use(bodyParser.urlencoded({extended: true, limit: '10mb'}));
    app.useGlobalPipes(
        new ValidationPipe({
            forbidNonWhitelisted: true,
            skipUndefinedProperties: false,
            whitelist: true,
            transform: true,
        }),
    );
    app.enableCors();
    app.useGlobalFilters(new LoggingException());
    app.useGlobalInterceptors(new LoggingHttp());
    await app.init();
    return serverless(expressApp, {provider: 'aws'});
}

exports.handler = async (event: unknown, context: Context): Promise<Response> => {
    server = server ?? (await bootstrap());
    const response = await server(event, context, async (err: unknown, res: Response) => {
        if (err) {
            logger.error('UNKNOWN', {
                error: err as object,
                event,
            });
            return {
                body: JSON.stringify({
                    error: (err as object),
                    message: 'Internal server error',
                }),
                statusCode: 500,
            };
        }
        return res;
    });
    await logTail?.flush();
    return response;
};
