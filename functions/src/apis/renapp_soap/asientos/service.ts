import moment from 'moment';
import { AsientoDto } from './dto/asiento.dto';
import { PrismaClient } from '@prisma/client';
import { getDocTypeAcsel } from '../../../utils/doctypes';
import { LogSource, sendError, sendInfo } from '../../../utils/logs';

export const service = {
  renapp_soap_asientos: {
    ASIENTOPort: {
      ASIENTO: async function (args: { date: string }): Promise<{
        asientos: AsientoDto[];
        response: string;
        code: number;
        error: string;
      }> {
        try {
          const date = moment.utc(args.date);
          const dateStart = date.clone().startOf('day');
          const dateEnd = date.clone().endOf('day');
          await sendInfo(LogSource.RENAPP_SOAP, 'Request SOAP - ASIENTO', {
            args,
            date,
            dateStart,
            dateEnd,
          });

          const prisma = new PrismaClient();

          const asientos = await prisma.accounting_entries.findMany({
            where: {
              created_at: {
                gte: dateStart.toDate(),
                lte: dateEnd.toDate(),
              },
            },
          });

          const response = asientos.map<AsientoDto>((asiento) => {
            let trm = Number.parseFloat(asiento.exchange_rate);
            if (trm == 0) {
              trm = 1;
            }
            const doctype = getDocTypeAcsel(asiento.document_type);
            return {
              COD_CIA: asiento.cod_cia,
              COD_OPER: asiento.cod_oper,
              NUMERO_COMPROBANTE: asiento.receipt_number,
              ESTATUS_DE_COMPROBANTE: asiento.receipt_status,
              NO_POLIZA: Number.parseInt(
                asiento.number_policy.replace('SORT-', '')
              ).toString(), // solo el número de póliza
              NO_RECIBO: asiento.number_payment,
              TIPO_COMPROBANTE: asiento.receipt_type,
              FECMOV: moment(asiento.date_register).format('YYYY-MM-DD'), // fecha de movimiento
              COD_INTERMEDIARY: asiento.cod_intermediary,
              COD_MONEDA: asiento.cod_moneda,
              COD_RAMO: asiento.cod_ramo,
              CTA_1: asiento.cta_1,
              CTA_2: asiento.cta_2,
              CTA_3: asiento.cta_3,
              CTA_4: asiento.cta_4,
              CTA_5: asiento.cta_5,
              CTA_6: asiento.cta_6,
              CTA_7: asiento.cta_7,
              CTA_8: asiento.cta_8,
              CTA_9: asiento.cta_9,
              CTA_10: asiento.cta_10,
              CREDITO: asiento.credit.toNumber(),
              MONTOMOV_CREDITO: asiento.credit.toNumber() / trm,
              DEBITO: asiento.debit.toNumber(),
              MONTOMOV_DEBITO: asiento.debit.toNumber() / trm,
              DETALLE_MOVIMIENTO: asiento.detail_movement,
              MTODIFERENCIA: asiento.difference?.toNumber(),
              TIPO_MOVIMIENTO: asiento.movement_type,
              TIPOMOVORG: asiento.movement_type_org,
              T_CREDITO: asiento.t_credit?.toNumber(),
              T_DEBITO: asiento.t_debit?.toNumber(),
              NUMDOC: asiento.document_number,
              TIPO_DE_DOCUMENTO: doctype,
              TASACAMBIO: trm.toString(),
              IDECTA: asiento.idecta,
              NAME_TAKER: asiento.name_taker,
              DESCOPER: asiento.descoper,
              COD_CPTO: asiento.cod_cpto,
              COD_GRUPO_CPTO: asiento.cod_grupo_cpto,
              COR_RELATIVO: asiento.cor_relativo,
              ENTRY_CODE: asiento.entry_code,
              CTAAUX: asiento.ctaux ?? '00000000000000', // 14 dígitos sempre
            } as AsientoDto;
          });
          prisma.audit_acsel_asientos.create({
            data: {
              req_date: args.date,
              req_start_date: dateStart.format('YYYY-MM-DD HH:mm:ss'),
              req_end_date: dateEnd.format('YYYY-MM-DD HH:mm:ss'),
              res_json: JSON.stringify(response),
              created_at: new Date(),
              updated_at: new Date(),
            },
          });
          return {
            asientos: response,
            response: 'OK',
            code: 200,
            error: '',
          };
        } catch (e: any) {
          await sendError(LogSource.RENAPP_SOAP, e.toString(), e);
          return {
            asientos: [],
            response: 'ERROR',
            code: 500,
            error: e.toString(),
          };
        }
      },
    },
  },
};
