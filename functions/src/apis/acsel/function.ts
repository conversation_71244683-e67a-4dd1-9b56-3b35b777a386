import express, {NextFunction, Request, Response} from "express";
import cors from "cors";
import jwt from "jsonwebtoken";
import serverless from "serverless-http";
import {LogSource, sendError, sendLogApiEvent} from "../../utils/logs";
import {tasaCambio} from "./tasa-cambio";
import {intermediario} from "./intermediario";
import {correduria} from "./correduria";
import {loginIntermediario} from "./login-intermediario";
import {APIGatewayEvent} from "aws-lambda";

const app = express();
const router = express.Router();

// Middleware de tracking
app.use(express.json());
app.use(cors());

router.use((req: Request, res: Response, next: NextFunction): void => {
    const token = req.headers.authorization?.split(' ')[1];
    if (!token) {
        res.status(401).send({message: "Acceso no autorizado. Token no proporcionado."});
        return;
    }
    const secret = process.env.JWT_SECRET_RENAPP;
    if (!secret) {
        throw new Error("JWT_SECRET no configurado: " + secret);
    }
    jwt.verify(token, secret, (err) => {
        if (err) {
            res.status(401).send({message: "Acceso no autorizado. Token no válido."});
            return;
        }
        next();
    });
});

router.get('/', function (req: Request, res: Response) {
    res.send({
        message: "Hello World desde ACSEL!",
    });
});

tasaCambio(router);
intermediario(router);
correduria(router);
loginIntermediario(router);

// Middleware 4: Capturar errores globalmente para todas las rutas
// eslint-disable-next-line @typescript-eslint/no-unused-vars
app.use(async (err: Error, req: Request, res: Response, next: NextFunction): Promise<void> => {
    // Enviar el error a Slack (u otra herramienta de monitoreo)
    await sendError(LogSource.FUNCTIONS, err.toString(), err);
    // Responder con un mensaje de error genérico
    res.status(500).send({
        message: "Ocurrió un error interno. Por favor, inténtalo más tarde.",
        error: err.message,
        body: req.body,
        params: req.params,
    });
});

app.use('/acsel', router);

export const handler = async (event: APIGatewayEvent, context: any) => {
    const start = Date.now();
    const response: any = await serverless(app)(event, context);
    const duration_ms = Date.now() - start;
    await sendLogApiEvent(LogSource.FUNCTIONS, event, response, duration_ms);
    return response;
};
