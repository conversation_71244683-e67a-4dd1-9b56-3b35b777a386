import express, {Request, Response} from "express";
import axios from "axios";
import {PrismaClient} from "@prisma/client";

const prisma = new PrismaClient();

export function loginIntermediario(router: express.Router) {
    router.post(
        "/login-intermediario",
        async function (req: Request, res: Response) {
            const {cod_inter, password} = req.body;


            if (!cod_inter) {
                await prisma.audit_acsel_login.create({
                    data: {
                        req_cod: String(cod_inter),
                        req_pass: "",
                        req_xml: "",
                        res_xml: "",
                        res_json: "",
                        res_code: "",
                        error: "Campo 'cod_inter' es requerido",
                        res_cod: "400",
                    },
                });
                res.status(400).send({
                    success: false,
                    message: "Campo 'cod_inter' es requerido",
                });
                return;
            }

            if (!password) {
                await prisma.audit_acsel_login.create({
                    data: {
                        req_cod: String(cod_inter),
                        req_pass: String(password),
                        req_xml: "",
                        res_xml: "",
                        res_json: "",
                        res_code: "",
                        error: "Campo 'password' es requerido",
                        res_cod: "400",
                    },
                });
                res.status(400).send({
                    success: false,
                    message: "Campo 'password' es requerido",
                });
                return;
            }

            const soapMessage = `
            <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:dev="http://xmlns.oracle.com/orawsv/WSERVICE/PR_WEBSERVICE_RT/DEVINTERVALIDACION">
            <soapenv:Header/>
            <soapenv:Body>
                <dev:DEVINTERVALIDACIONInput>
                    <dev:CCLAVE-VARCHAR2-IN>${password}</dev:CCLAVE-VARCHAR2-IN>
                    <dev:CCODINTER-VARCHAR2-OUT/>
                    <dev:CDESCRIP-VARCHAR2-OUT/>
                    <dev:CMENSAJE-VARCHAR2-OUT/>
                    <dev:CSTSCORREDOR-VARCHAR2-OUT/>
                    <dev:CSTSINTER-VARCHAR2-OUT/>
                    <dev:CUSERNAME-VARCHAR2-IN>${cod_inter}</dev:CUSERNAME-VARCHAR2-IN>
                </dev:DEVINTERVALIDACIONInput>
            </soapenv:Body>
        </soapenv:Envelope>
            `;

            const authorization = `${process.env.ACSEL_USERNAME}:${process.env.ACSEL_PASSWORD}`;

            try {
                const response = await axios({
                    method: "POST",
                    url:
                        "http://portal.oceanica-cr.com:" +
                        process.env.ACSEL_PORT +
                        "/orawsv/WSERVICE/PR_WEBSERVICE_RT/DEVINTERVALIDACION",
                    headers: {
                        "Content-Type": "text/xml; charset=utf-8",
                        Authorization:
                            "Basic " + Buffer.from(authorization).toString("base64"),
                    },
                    data: soapMessage,
                });

                const soapResponse = response.data;
                const statusRes = soapResponse.match(/<CDESCRIP>(.*?)<\/CDESCRIP>/);
                const status = statusRes[1];

                const matches = soapResponse.match(
                    /<CSTSINTER>(.*?)<\/CSTSINTER>.*?<CDESCRIP>(.*?)<\/CDESCRIP>.*?<CCODINTER>(.*?)<\/CCODINTER>/s
                );
                const responseData = {
                    CSTSINTER: matches[1],
                    CDESCRIP: matches[2],
                    CCODINTER: matches[3],
                };

                if (status == "ACTIVO" || status == "VALIDO") {
                    await prisma.audit_acsel_login.create({
                        data: {
                            req_cod: String(cod_inter),
                            req_pass: String(password),
                            req_xml: soapMessage,
                            res_xml: soapResponse,
                            res_json: JSON.stringify(responseData, null, 2),
                            res_code: responseData.CCODINTER,
                            res_cod: "200",
                        },
                    });
                    res.status(200).send({
                        success: true,
                        message: "Autenticación exitosa",
                        status: status,
                    });
                } else {
                    const message = "Autenticación fallida";
                    await prisma.audit_acsel_login.create({
                        data: {
                            req_cod: String(cod_inter),
                            req_pass: String(password),
                            req_xml: soapMessage,
                            res_xml: soapResponse,
                            error: message,
                            res_cod: "403",
                        },
                    });
                    res.status(403).send({
                        success: false,
                        message: message,
                        status: status,
                    });
                }
            } catch (e) {
                const message = "Error al consultar el servicio SOAP: " + e
                await prisma.audit_acsel_login.create({
                    data: {
                        req_cod: String(cod_inter),
                        req_pass: String(password),
                        req_xml: soapMessage,
                        error: message,
                        res_cod: "500",
                    },
                });
                res.status(500).send({
                    success: false,
                    message: message,
                });
                return;
            }
        },
    );
}
