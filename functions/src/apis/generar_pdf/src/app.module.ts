import { Modu<PERSON> } from "@nestjs/common";
import { AppController } from "./app.controller";
import { AppService } from "./app.service";
import { ConfigModule } from "@nestjs/config";
import { AuthJwtModule } from "../../../utils/auth-jwt/auth-jwt.module";

@Module({
  imports: [
    ConfigModule.forRoot({ isGlobal: true }), // Asegura que ConfigModule esté disponible globalmente
    AuthJwtModule.register({
      jwtSecret: process.env.JWT_SECRET_RENAPP || "default_secret",
    }),
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
