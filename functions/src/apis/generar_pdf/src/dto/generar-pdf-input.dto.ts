import {IsIn, IsOptional, IsString} from "class-validator";
import {ORIENTATION, PAGE_FORMAT, WAIT_UNTIL} from "../constants";

export class GenerarPdfInput {
    @IsOptional()
    @IsString()
    html?: string;

    @IsOptional()
    @IsString()
    htmlUrl?: string;

    @IsIn(PAGE_FORMAT)
    pageFormat: string = 'A4';


    @IsIn(WAIT_UNTIL)
    waitUntil: string = 'networkidle0';

    @IsIn(ORIENTATION)
    orientation: string = 'portrait';
}

