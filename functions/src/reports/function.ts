import {PrismaClient, reports} from "@prisma/client";
import {SQSEvent} from "aws-lambda";
import {generarReporteAcciones} from "./reports/reporte-acciones";
import {PutObjectCommand, S3Client} from "@aws-sdk/client-s3";
import {sendReport} from "../utils/email";
import {ReportStatus} from "../constants/constants";
import * as fs from "fs";
import {LogSource, sendError} from "../utils/logs";
import moment from 'moment-timezone';
import {generarReporteAfiliados} from "./reports/reporte-afiliados";

const prisma = new PrismaClient();

export async function handler(event: SQSEvent): Promise<void> {
    let historyId: number | null = null;
    try {
        const reportName = event.Records[0].messageAttributes["name"].stringValue;
        if (!reportName) {
            throw new Error("Reporte no encontrado");
        }
        const report: reports = await getReport(reportName);
        const emails = validateEmails(report.emails, reportName);

        historyId = await createReportHistory(report.id, report.emails);

        const filePath = await generateReportFile(report.name);
        await uploadAndSendReport(filePath, report, historyId, emails);

        await prisma.$disconnect();
        console.log("Reporte enviado: ", reportName);
    } catch (e) {
        await handleError(historyId, e);
    }
}

async function getReport(reportName: string): Promise<reports> {
    return prisma.reports.findFirstOrThrow({
        where: {name: reportName}
    });
}

function validateEmails(emailsStr: string | null, reportName: string): string[] {
    const emails = emailsStr?.split(",") || [];
    if (!emails.length) {
        throw new Error("No hay correos configurados para el reporte: " + reportName);
    }
    for (const email of emails) {
        if (!email.match(/^[\w-.]+@([\w-]+\.)+[\w-]{2,4}$/)) {
            throw new Error("Email inválido: " + email);
        }
    }
    return emails;
}

async function createReportHistory(reportId: number, emails: string | null): Promise<number> {
    const history = await prisma.report_histories.create({
        data: {
            report_id: reportId,
            emails: emails || "",
            status: ReportStatus.GENERANDO,
            created_at: new Date(),
            updated_at: new Date()
        }
    });
    return history.id;
}

async function generateReportFile(reportName: string): Promise<string> {
    console.log("Generando reporte: ", reportName);
    switch (reportName) {
        case "CONSOLIDADO DE ACCIONES":
            return await generarReporteAcciones(prisma);
        case "REPORTE DE AFILIADOS":
            return await generarReporteAfiliados(prisma);
        default:
            throw new Error("Reporte no encontrado: " + reportName);
    }
}

async function uploadAndSendReport(filePath: string, report: reports, historyId: number, emails: string[]): Promise<void> {
    const startTime = moment();
    const seconds = moment().diff(startTime, "seconds");
    await updateReportHistoryStatus(historyId, ReportStatus.SUBIENDO_ARCHIVO, seconds, fs.statSync(filePath).size);

    const fileName = await uploadToS3(filePath, report.name, historyId);
    await updateReportHistoryStatus(historyId, ReportStatus.ENVIANDO_CORREOS);

    const url = `https://reportes.renapp.us/${fileName}`;
    let subject = report.subject || "";
    let body = report.body || "";
    // Modificar el asunto del correo solo para el reporte de afiliados
    if (report.name === "REPORTE DE AFILIADOS") {
        const fechaCostaRica = moment().tz("America/Costa_Rica");
        const mesActual = fechaCostaRica.format("MMMM");
        const diaActual = fechaCostaRica.format("DD");
        subject = `${subject} - Para el periodo del mes de ${mesActual} hasta el ${diaActual}`;
        const periodoTexto = `Para el periodo del mes de ${mesActual} hasta el ${diaActual}`;
        body = `${body}\n\n${periodoTexto}`;

    }
    await sendEmails(emails, subject, body, url);

    await updateReportHistoryStatus(historyId, ReportStatus.ENVIADO, 0, 0, url);
}

async function updateReportHistoryStatus(historyId: number, status: string, seconds = 0, size = 0, url = ""): Promise<void> {
    await prisma.report_histories.update({
        where: {id: historyId},
        data: {status, seconds, size, url, updated_at: new Date()}
    });
}

async function uploadToS3(filePath: string, reportName: string, historyId: number): Promise<string> {
    const fileName = `${reportName}_${historyId}_${moment().format("YYYY-MM-DD-HH-mm-ss")}.xlsx`;
    const fileStream = fs.createReadStream(filePath);
    await new S3Client({region: process.env.AWS_REGION}).send(
        new PutObjectCommand({
            Body: fileStream,
            Bucket: "mnk-reports",
            Key: fileName
        })
    );
    return fileName;
}

async function sendEmails(emails: string[], subject: string, body: string, url: string): Promise<void> {
    for (let email of emails) {
        await sendReport(email, subject, body, url);
    }
}

async function handleError(historyId: number | null, e: any): Promise<void> {
    if (historyId) {
        await prisma.report_histories.update({
            where: {id: historyId},
            data: {
                status: ReportStatus.ERROR,
                error: e.toString(),
                updated_at: new Date()
            }
        });
    }
    await prisma.$disconnect();
    await sendError(LogSource.REPORTS, "Error al generar el reporte", e);
    throw e;
}
