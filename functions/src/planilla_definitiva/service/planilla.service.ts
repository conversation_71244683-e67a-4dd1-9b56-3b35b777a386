import {SendMessageCommand, SendMessageCommandInput, SQSClient} from "@aws-sdk/client-sqs";
import {LogSource, sendError, sendInfo} from "../../utils/logs";
import {DefinitivaRepository} from "../repository/definitiva.repository";
import {Inject} from "@nestjs/common";


const sqsClient = new SQSClient({region: process.env.AWS_REGION});


export class PlanillaService {
    constructor(@Inject(DefinitivaRepository) private repository: DefinitivaRepository) {
    }

    async envioPlanilla(): Promise<void> {

        const preadsheets = await this.repository.findsPreadsheetPolice();

        for (const preadsheet of preadsheets) {

            const params: SendMessageCommandInput = {
                MessageBody: `Correo planilla definitiva: ${preadsheet.id ?? ''}`,
                QueueUrl: process.env.SQS_PLANILLA_DEFINITIVA, // Reemplaza con tu URL de la cola
                MessageAttributes:
                    {
                        activity_spreadsheet_id: {
                            DataType: "String",
                            StringValue: preadsheet.id.toString(),
                        },
                    },
            };

            sqsClient.send(new SendMessageCommand(params))
                .then(data => {
                    sendInfo(
                        LogSource.PLANILLA_AFILIADOS,
                        `Enviando mensaje SQS para planilla definitiva: ${preadsheet.id}`,
                        data
                    ).catch(() => {/*no bloquea*/
                    });
                })
                .catch(err => {
                    // Si falla el envío, lo registramos, pero no abortamos todo el bucle
                    sendError(
                        LogSource.PLANILLA_AFILIADOS,
                        `Error al encolar planilla definitiva ${preadsheet.id}`,
                        err
                    ).catch(() => {/*no bloquea*/
                    });
                });
        }
    }

    async closeSpreadsheet(activity_spreadsheet_id: string): Promise<void> {

        const params: SendMessageCommandInput = {
            MessageBody: `Correo planilla definitiva: ${activity_spreadsheet_id}`,
            QueueUrl: process.env.SQS_PLANILLA_DEFINITIVA, // Reemplaza con tu URL de la cola
            MessageAttributes:
                {
                    activity_spreadsheet_id: {
                        DataType: "String",
                        StringValue: activity_spreadsheet_id,
                    },
                },
        };

        sqsClient.send(new SendMessageCommand(params))
            .then(data => {
                sendInfo(
                    LogSource.PLANILLA_AFILIADOS,
                    `Enviando mensaje SQS para planilla definitiva: ${activity_spreadsheet_id}`,
                    data
                ).catch(() => {/*no bloquea*/
                });
            })
            .catch(err => {
                // Si falla el envío, lo registramos, pero no abortamos todo el bucle
                sendError(
                    LogSource.PLANILLA_AFILIADOS,
                    `Error al encolar planilla definitiva ${activity_spreadsheet_id}`,
                    err
                ).catch(() => {/*no bloquea*/
                });
            });
    }
}