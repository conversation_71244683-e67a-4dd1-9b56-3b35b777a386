// __tests__/planilla.routes.test.ts
import jwt from "jsonwebtoken";
import request from "supertest";

import {app} from "../api";

describe("POST /planilla_definitiva/envio", () => {
    let token: string;

    beforeAll(() => {
        // Generamos un JWT “válido” con la misma clave que usa tu aplicación
        // Supongamos que el payload solo necesita contener { userId: 1 }, por ejemplo.
        const secret = process.env.JWT_SECRET_RENAPP || "test-secret";
        token = jwt.sign({userId: 1}, secret, {expiresIn: "1h"});
    });

    it("debe responder 200 y llamar a PlanillaServices", async () => {
        const res = await request(app)
            .post("/planilla_definitiva/envio")
            .set("Authorization", `Bearer ${token}`)
            .send({}); // tu endpoint no espera body, pero enviamos un objeto vacío

        expect(res.status).toBe(200);
        expect(res.body).toEqual({
            success: true,
            message: "Solicitudes de planilla definitivas encoladas con éxito.",
        });

    });

    it("devuelve 401 si no se envía token", async () => {
        const res = await request(app)
            .post("/planilla_definitiva/envio")
            .send({});
        expect(res.status).toBe(401);
        expect(res.body).toEqual({
            success: false,
            message: "Acceso no autorizado. Token no proporcionado.",
        });
    });

    it("devuelve 401 si el token es inválido", async () => {
        const res = await request(app)
            .post("/planilla_definitiva/envio")
            .set("Authorization", `Bearer invalidtoken`)
            .send({});
        expect(res.status).toBe(401);
        expect(res.body).toEqual({
            success: false,
            message: "Acceso no autorizado. Token no válido.",
        });
    });
});

describe("GET /planilla_definitiva/tomador/:activity_spreadsheet_id", () => {
    let token: string;

    beforeAll(() => {
        // Generamos un JWT “válido” con la misma clave que usa tu aplicación
        // Supongamos que el payload solo necesita contener { userId: 1 }, por ejemplo.
        const secret = process.env.JWT_SECRET_RENAPP || "test-secret";
        token = jwt.sign({userId: 1}, secret, {expiresIn: "1h"});
    });

    it("debe responder 200 y llamar a PlanillaServices", async () => {
        // 2) Número de prueba para activity
        const actividadPrueba = "367149";

        const res = await request(app)
            .get(`/planilla_definitiva/tomador/${actividadPrueba}`)
            .set("Authorization", `Bearer ${token}`)
            .expect(200);

        expect(res.status).toBe(200);
        expect(res.body).toEqual({
            success: true,
            message: "Solicitudes de planilla definitivas encoladas con éxito.",
        });

    });

    it("devuelve 401 si no se envía token", async () => {
        const res = await request(app)
            .post("/planilla_definitiva/envio")
            .send({});
        expect(res.status).toBe(401);
        expect(res.body).toEqual({
            success: false,
            message: "Acceso no autorizado. Token no proporcionado.",
        });
    });

    it("responde 401 si el token es inválido", async () => {
        const badToken = "Bearer token-invalido";
        await request(app)
            .get(`/planilla_definitiva/tomador/123`)
            .set("Authorization", badToken)
            .expect(401)
            .then((res) => {
                expect(res.body).toMatchObject({
                    success: false,
                    message: expect.any(String),
                });
            });
    });
});
