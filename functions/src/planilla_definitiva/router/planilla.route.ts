// src/routes/planilla.routes.ts
import express from "express";
import { jwtMiddleware } from "../../utils/jwt-middleware";
import * as PlanillaController from "../controller/planilla.controller";

const router = express.Router();

// ─── Middleware de autenticación JWT (solo para estas rutas) ───────────────────
router.use(jwtMiddleware);

// ─── POST /planilla_definitiva/envio ─────────────────────────────────────────────
router.post("/envio", PlanillaController.enviarPlanillaDefinitiva);// ─── POST /planilla_definitiva/envio ─────────────────────────────────────────────
router.get("/tomador/:activity_spreadsheet_id", PlanillaController.closePlanilla);

export default router;
