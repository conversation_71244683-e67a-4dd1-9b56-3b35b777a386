import {PrismaClient} from "@prisma/client";
import {SQSEvent} from "aws-lambda";
import {LogSource, sendError, sendInfo} from "../utils/logs";
import {sendEmail, createRegisterMail} from "../utils/email";
import {
    capitalizeEachWord,
    currencyIcon,
    dataSpreadsheets,
    formatNumber,
    formatSortNumber, registerHelpers
} from "../utils/utils";
import moment from "moment";
import 'moment/locale/es';
import {generatePdf} from "../utils/pdf";
import {PutObjectCommand, S3Client} from "@aws-sdk/client-s3";
import IntegrationService from '../utils/IntegrationService';
import {SERVICE_AFFILIATE_WORKFORCE_REPORT_MNK, SERVICE_REPORT_TAKEN_FORM_MNK} from "../utils/constants";

const prisma = new PrismaClient();

export async function handler(event: SQSEvent): Promise<void> {

    const REPORTAR_PLANILLA_DEFINITIVA = 19;
    const GENERAR_CERTIFICADO_TOMADOR = 85;
    const REPORTAR_PLANILLA_TOMADOR = 84;
    const POLIZA_EMITIDA_ACTIVA = 20;

    const PLANILLA_REPORTADA = 56;
    const CERTIFICADO_DE_TOMADOR_REPORTADO = 57

    registerHelpers();

    moment.locale('es');
    const activity_id = event.Records[0].messageAttributes["activity_id"]?.stringValue;
    let results: any[] = [];
    let planillasTotal = 0;

    if (activity_id) {

        const reporte_planilla = await prisma.activities.findMany({
            where: {
                parent_id: Number(activity_id),
                service_id: 79,
                deleted_at: null,
            },
            orderBy: {
                created_at: 'desc',
            },
        });

        if (reporte_planilla) {

            planillasTotal = reporte_planilla.length;
            // Consulta con activity_id

            const idplanilla = reporte_planilla.find((report) => report.state_id == 56);

            if (idplanilla) {

                results = await prisma.activity_actions.findMany({
                    where: {
                        action_id: REPORTAR_PLANILLA_TOMADOR,
                        activities: {
                            id: idplanilla?.id,
                        },
                    },
                    include: {
                        activities: {
                            include: {
                                users: true,
                                affiliates: true,
                            },
                        },
                    },
                    distinct: ['activity_id'],
                });
            }


        }


    } else {

        // Fechas usando date-fns
        const today = moment();
        const startOfDay = today.clone().subtract(3, 'days').startOf('day'); // Hace 3 días al inicio del día
        const endOfDay = today.clone().endOf('day'); // Hoy al final del día


        // Consulta para acciones del día actual
        results = await prisma.activity_actions.findMany({
            where: {
                action_id: 84,
                activities: {
                    OR: [
                        {
                            created_at: {
                                gte: startOfDay.toDate(),
                                lte: endOfDay.toDate(),
                            },
                        },
                        {
                            updated_at: {
                                gte: startOfDay.toDate(),
                                lte: endOfDay.toDate(),
                            },
                        },
                    ],
                    state_id: PLANILLA_REPORTADA,
                },
            },
            include: {
                activities: {
                    include: {
                        users: true,
                        affiliates: true,
                    },
                },
            },
            distinct: ['activity_id'],
        });
    }

    // Retorno o procesamiento adicional
    const date = new Date();
    const currentDate = moment(date).format('DD/MM/YYYY');

    for (const activity of results) {

        try {
            if (activity.activities.parent_id) {

                const parent = await prisma.activities.findFirst({
                    where: {
                        id: activity.activities.parent_id,
                    },
                    select: {
                        state_id: true,
                        id: true
                    },
                });

                if (!activity_id && parent) {
                    const reportePlanilla = await prisma.activities.findMany({
                        where: {
                            parent_id: parent.id,
                            service_id: 79,
                            deleted_at: null,
                        },
                        orderBy: {
                            created_at: 'desc',
                        },
                    });

                    planillasTotal = reportePlanilla.length;
                }

                if (parent?.state_id === POLIZA_EMITIDA_ACTIVA) {

                    const result = await prisma.policy_spreadsheet_affiliates.findMany({
                        where: {
                            policy_spreadsheets: {
                                activity_id: activity.activities.id,
                            },
                        },
                        select: {
                            id: true,
                            id_type: true,
                            nationality: true,
                            identification_number: true,
                            first_name: true,
                            last_name: true,
                            date_of_birth: true,
                            gender: true,
                            email: true,
                            work_shift_type: true,
                            monthly_salary: true,
                            days: true,
                            hours: true,
                            occupation: true,
                            affiliate_id: true,
                            observation_affiliate: true,
                            policy_spreadsheets: {
                                select: {
                                    total_affiliates: true,
                                    total_salaries: true,
                                    activity_id: true,
                                    id:true,
                                    created_at: true
                                },
                            },
                        },
                    });
                    const totalAffiliates = result.length;
                    const policy = await prisma.activities.findFirst({
                        where: {
                            id: activity.activities.parent_id
                        },
                        select: {
                            id: true,
                            affiliate_id: true,
                            affiliates: {
                                select: {
                                    full_name: true,
                                    first_name: true,
                                    doc_number: true
                                },
                            },
                            policy_sorts: {
                                select: {
                                    id:true,
                                    type_currency: true,
                                    consecutive: true,
                                    email: true,
                                    notification_email: true,
                                    work_modality_id:true,
                                    calendar_period:true,
                                },
                            },
                        },
                    });
                    //const userEmail = policy?.policy_sorts[0]?.email ?? '';
                    const affiliateEmail = policy?.policy_sorts[0]?.notification_email ?? '';

                    //REPORTAR_PLANILLA_DEFINITIVA - accion del servicio de poliza
                    await sendInfo(LogSource.PLANILLA_AFILIADOS, `Creando activity action Reporte planilla: ${policy?.policy_sorts[0]?.consecutive}`);

                    await prisma.activity_actions.create({
                        data: {
                            activity_id: activity.activities.parent_id,
                            action_id: REPORTAR_PLANILLA_DEFINITIVA,
                            old_state_id: POLIZA_EMITIDA_ACTIVA,
                            new_state_id: POLIZA_EMITIDA_ACTIVA,
                            description: 'Reportar planilla definitiva',
                            old_user_id: activity.activities.users.id,
                            new_user_id: activity.activities.users.id,
                            author_id: activity.activities.users.id,
                            created_at: new Date(),
                        },
                    });

                    await prisma.activities.update({
                        where: {id: activity.activity_id},
                        data: {
                            state_id: CERTIFICADO_DE_TOMADOR_REPORTADO,
                            updated_at: new Date(),
                        }
                    });

                    await sendInfo(LogSource.PLANILLA_AFILIADOS, `Se actualizo Estado Planilla poliza: ${policy?.policy_sorts[0]?.consecutive}`);

                    const activity_Action = await prisma.activity_actions.create({
                        data: {
                            activity_id: result[0]?.policy_spreadsheets.activity_id,
                            action_id: GENERAR_CERTIFICADO_TOMADOR,
                            old_state_id: PLANILLA_REPORTADA,
                            new_state_id: CERTIFICADO_DE_TOMADOR_REPORTADO,
                            description: 'Reportar planilla definitiva',
                            old_user_id: activity.activities.users.id,
                            new_user_id: activity.activities.users.id,
                            author_id: activity.activities.users.id,
                            created_at: new Date(),
                        },
                    });


                    const dataHtml = dataSpreadsheets(result);
                    const total_affiliates = result[0]?.policy_spreadsheets?.total_affiliates;
                    const total_salaries = formatNumber(Number(result[0]?.policy_spreadsheets?.total_salaries));

                    const totalPlanillas = planillasTotal > 1 ? `${planillasTotal - 1}` : 'Emisión';
                    const full_name = capitalizeEachWord(policy?.affiliates?.full_name || '');
                    const currency = currencyIcon(policy?.policy_sorts[0]?.type_currency || '');
                    const sort_id = formatSortNumber(policy?.policy_sorts[0]?.consecutive || 0);

                    let currentMonth = '';
                    //Calendario periodo especial
                    if (policy?.policy_sorts?.[0]?.calendar_period === '2') {
                        let policyCalendars = [];
                        policyCalendars = await prisma.policy_calendars.findMany({
                            where: {
                                policy_sort_id:policy?.policy_sorts?.[0]?.id
                            }
                        });
                        // Obtenemos la fecha de creación de la actividad de planilla
                        const planillaDate = moment.utc(results[0].activities.created_at).subtract(15, 'days');
                        // Buscar periodo al que pertenece la fecha de la planilla
                        const periodoAsignado = policyCalendars.find(periodo => {
                            const start = moment.utc(periodo.start_date);
                            const end = moment.utc(periodo.end_date);
                            return planillaDate.isBetween(start, end, null, '[]');
                        });
                        // Asignamos el periodo en formato "Del dd/mm/aa al dd/mm/aa"
                        if (periodoAsignado) {
                            const start = moment.utc(periodoAsignado.start_date).format('DD/MM/YY');
                            const end = moment.utc(periodoAsignado.end_date).format('DD/MM/YY');
                            currentMonth = `Del ${start} al ${end}`;
                        }
                    }else{
                        const date = new Date();
                        date.setMonth(date.getMonth() - 1);
                        const lastMonth = date.toLocaleString('es-ES', { month: 'long' });
                        currentMonth = lastMonth.charAt(0).toUpperCase() + lastMonth.slice(1);
                    }

                    const name = capitalizeEachWord(policy?.affiliates?.first_name);

                    const titlePlanillas = {
                        totalPlanillas,
                        currentMonth
                    }

                    const dataPdf = {};

                    Object.assign(dataPdf, {
                        dataHtml,
                        currentDate,
                        total_affiliates,
                        total_salaries,
                        full_name,
                        currency,
                        sort_id,
                        currentMonth,
                        totalPlanillas,
                        titlePlanillas
                    });

                    const file = await generatePdf('docs/planilla/planilla_dinamic.hbs', dataPdf, 'landscape');

                    // subir a S3
                    const filename = `planilla-definitiva-${activity.activities.id}-${moment().format("YYYY-MM-DD-HH-mm-ss")}.pdf`;
                    await new S3Client({region: process.env.AWS_REGION}).send(
                        new PutObjectCommand({
                            Body: file,
                            Bucket: process.env.BUCKET_NAME,
                            Key: `activity_action_document/${filename}`,
                        })
                    );

                    //Resumen de reporte de planilla
                    const fechaFormateada = moment(result[0]?.policy_spreadsheets.created_at).format('DD/MM/YYYY');
                    const horaFormateada = moment(result[0]?.policy_spreadsheets.created_at).format('HH:mm:ss');

                    const mesEnLetras = moment(results[0].activities.created_at).locale('es').format('MMMM');
                    const mes_capitalizado = mesEnLetras.charAt(0).toUpperCase() + mesEnLetras.slice(1);
                    const anio = moment(results[0].activities.created_at).format('YYYY');

                    const type = planillasTotal > 1 ? 'Mensual' : 'Emisión';
                    const monthReport = currentMonth;
                    const yearReport = new Date().getFullYear();
                    const dataPdfReport = {};

                    Object.assign(dataPdfReport, {
                        full_name,
                        sort_id,
                        type,
                        mes_capitalizado,
                        titlePlanillas,
                        total_affiliates,
                        total_salaries,
                        currency,
                        anio,
                        fechaFormateada,
                        horaFormateada,
                        planillasTotal
                    });

                    const fileReport = await generatePdf('docs/certificados/report_planilla.hbs', dataPdfReport);

                    const filenameReport = `ResumenPlanilla_${sort_id}-${moment().format("YYYY-MM-DD-HH-mm-ss")}.pdf`;

                    await new S3Client({region: process.env.AWS_REGION}).send(
                        new PutObjectCommand({
                            Body: fileReport,
                            Bucket: process.env.BUCKET_NAME,
                            Key: `activity_action_document/${filenameReport}`,
                        })
                    );

                    const fileUrlReport = `https://${process.env.BUCKET_NAME}.s3.amazonaws.com/activity_action_document/${filenameReport}`;

                    await sendInfo(LogSource.PLANILLA_AFILIADOS, `Se crea activity_action_documents : ${policy?.policy_sorts[0]?.consecutive}`);

                    await prisma.activity_action_documents.create({
                        data: {
                            activity_action_id: activity_Action.id,
                            name: 'Certificado_Planilla',
                            path: `activity_action_document/${filename}`,
                            created_at: new Date(),
                        }
                    });

                    await prisma.activity_action_documents.create({
                        data: {
                            activity_action_id: activity_Action.id,
                            name: 'Resumen planilla',
                            path: `activity_action_document/${filenameReport}`,
                            created_at: new Date(),
                        }
                    });

                    await prisma.policy_spreadsheets.update({
                        where: {
                            id: result[0]?.policy_spreadsheets.id,
                        },
                        data: {
                            path_report: `activity_action_document/${filenameReport}`
                        },
                    });


                    const fileUrl = `https://${process.env.BUCKET_NAME}.s3.amazonaws.com/${filename}`;

                    await sendInfo(LogSource.PLANILLA_AFILIADOS, `Se genero documento Planilla poliza: ${policy?.policy_sorts[0]?.consecutive}`);

                    const isExcludedModality = policy?.policy_sorts[0]?.work_modality_id === 2 || policy?.policy_sorts[0]?.work_modality_id === 3;
                    let finalExcludedModality = isExcludedModality;
                    const hasMatchingAffiliate = policy?.affiliate_id ? result.some((affiliate) => affiliate.affiliate_id === policy.affiliate_id) : false;
                    if (totalAffiliates === 1 && hasMatchingAffiliate){

                        const currentYear = moment().year();
                        finalExcludedModality = true;
                        //Correos de planilla afiliado
                        const url = `${process.env.URL_FUNTIONS}/planilla_afiliados/certificados`;
                        const data = {
                            policy_spreadsheet_id: result[0]?.policy_spreadsheets.id,
                            consecutive: policy?.policy_sorts[0]?.consecutive,
                            period: currentMonth + ' ' + currentYear,
                            exclude_email: true
                        };
                        const response = await IntegrationService.requestRenAppApi(url, 'POST', data);
                        if (response.status === 200) {
                            await sendInfo(LogSource.PLANILLA_AFILIADOS, `Se inicia proceso de generación de certificados afiliados`);
                        }
                    }

                    if (!activity_id && planillasTotal > 1 && !finalExcludedModality) {
                        const currentYear = moment().year();

                        const subject = `Recepción y aceptación de su planilla mensual. Póliza #${sort_id}`;
                        const body = `
                            <p>¡Buen día, ${name}!</p>        
                            <p>¡Muchas gracias por enviarnos su planilla mensual de trabajadores correspondiente a <b>${currentMonth}</b> de <b>${currentYear}</b>!</p>
                            <p>Le confirmamos que recibimos la información satisfactoriamente y la aceptamos para el proceso de registro y gestión de pólizas.</p>
                            <p>Se adjunta archivo con el resumen de la información recibida.</p>
                            <p>¡Muchas gracias por la confianza que ha depositado en nosotros! Nuestro propósito es transformar la protección en una experiencia ágil, confiable y humana.</p>
                            <p>Cordialmente,</p>
                            <p>
                            <strong>Área de Aseguramiento</strong> <br>
                            <strong>Seguro Obligatorio de Riesgos del Trabajo</strong> <br>
                            <strong>MNK Seguros </strong> <br>
                            </p>
                        `;

                        // await Promise.all([
                        //     //Se comenta correo del intermediario por solicitud ME-3137 Incidente 127
                        //     //sendEmail(userEmail, subject, body, [fileUrl, fileUrlReport]),
                        //     sendEmail(affiliateEmail, subject, body, [fileUrl, fileUrlReport]),
                        // ]);

                        await Promise.all([
                            // Operación para el email del afiliado con manejo de errores
                            (async () => {
                                try {
                                    // Primero envía el email
                                    await sendEmail(
                                        affiliateEmail,
                                        subject,
                                        body,
                                        [fileUrl, fileUrlReport]
                                    );

                                    const emailResult = {
                                        status: 'Enviado',
                                        message: 'Email enviado correctamente'
                                    };

                                    const files = [
                                        {
                                            type: 'pdf',
                                            path: `activity_action_document/${filename}`,
                                            name: filename
                                        },
                                        {
                                            type: 'pdf',
                                            path: `activity_action_document/${filenameReport}`,
                                            name: filenameReport
                                        }
                                    ];

                                    await createRegisterMail(
                                        Number(result[0]?.policy_spreadsheets.activity_id || 0),
                                        SERVICE_REPORT_TAKEN_FORM_MNK,
                                        String(policy?.policy_sorts[0]?.consecutive || ''),
                                        'Tomador',
                                        full_name,
                                        String(policy?.affiliates?.doc_number || ''),
                                        subject,
                                        body,
                                        [affiliateEmail],
                                        emailResult,
                                        files,
                                        null
                                    );

                                } catch (error) {
                                    const emailResult = {
                                        status: 'Reboto',
                                        message: 'Error al enviar email'
                                    };

                                    const files = [
                                        {
                                            type: 'pdf',
                                            path: `activity_action_document/${filename}`,
                                            name: filename
                                        },
                                        {
                                            type: 'pdf',
                                            path: `activity_action_document/${filenameReport}`,
                                            name: filenameReport
                                        }
                                    ];

                                    await createRegisterMail(
                                        Number(result[0]?.policy_spreadsheets.activity_id || 0),
                                        SERVICE_REPORT_TAKEN_FORM_MNK,
                                        String(policy?.policy_sorts[0]?.consecutive || ''),
                                        'Tomador',
                                        full_name,
                                        String(policy?.affiliates?.doc_number || ''),
                                        subject,
                                        body,
                                        [affiliateEmail],
                                        emailResult,
                                        files,
                                        null
                                    );

                                    throw error;
                                }
                            })(),

                            // Aquí puedes agregar más operaciones si es necesario
                        ]);

                        await sendInfo(LogSource.PLANILLA_AFILIADOS, `Correos enviados con exito para la poliza ${sort_id} `);

                        //Correos de planilla afiliado
                        const url = `${process.env.URL_FUNTIONS}/planilla_afiliados/certificados`;
                        const data = {policy_spreadsheet_id: result[0]?.policy_spreadsheets.id,
                            consecutive: policy?.policy_sorts[0]?.consecutive,
                            period: currentMonth + ' '  + currentYear};
                        const response = await IntegrationService.requestRenAppApi(url, 'POST', data);
                        if (response.status === 200) {
                            await sendInfo(LogSource.PLANILLA_AFILIADOS, `Se inicia proceso de generación de certificados afiliados`);
                        }
                    }
                }
            }

        } catch (error) {
            await sendError(LogSource.PLANILLA_AFILIADOS, `Error procesando planilla definitiva activity ${activity.activities.id}`, error);
        }
    }

    await sendInfo(LogSource.PLANILLA_AFILIADOS, `Correos planilla definitiva enviados con exito`);

}