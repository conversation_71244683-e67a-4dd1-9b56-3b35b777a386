// src/controllers/planilla.controller.ts
import {NextFunction, Request, Response} from "express";
import {PrismaService} from "../../utils/prisma/prisma.service";
import {DefinitivaRepository} from "../repository/definitiva.repository";
import {PlanillaService} from "../service/planilla.service";

const prisma = new PrismaService();
const definitivaRepo = new DefinitivaRepository(prisma);
const planillaService = new PlanillaService(definitivaRepo);


export async function enviarPlanillaDefinitiva(req: Request, res: Response, next: NextFunction) {
    try {
        // 1) Invocamos el servicio sin await para no bloquear la respuesta
        await planillaService.envioPlanilla();

        // 2) Respondemos de inmediato: “200 OK”
        res.json({
            success: true,
            message: "Solicitudes de planilla definitivas encoladas con éxito."
        });
    } catch (err: any) {
        // En caso de error “rápido” (por ejemplo body inválido), delegamos al error handler global
        next(err);
    }
}

export async function closePlanilla({params}: Request, res: Response, next: NextFunction) {
    try {
        const activityId = params.activity_spreadsheet_id;
        if (!activityId) {
            res.status(400).json({success: false, message: "Falta activity_spreadsheet_id en la ruta."});
        }

        await planillaService.closeSpreadsheet(activityId);

        // 2) Respondemos de inmediato: “200 OK”
        res.json({
            success: true,
            message: "Solicitudes de planilla definitivas encoladas con éxito."
        });
    } catch (err: any) {
        // En caso de error “rápido” (por ejemplo body inválido), delegamos al error handler global
        next(err);
    }
}
