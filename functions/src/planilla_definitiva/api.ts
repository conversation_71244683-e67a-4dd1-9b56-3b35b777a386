// src/api.ts
import {APIGatewayEvent} from "aws-lambda";
import cors from "cors";
import express, {NextFunction, Request, Response} from "express";
import serverless from "serverless-http";
import {LogSource, sendError, sendLogApiEvent} from "../utils/logs";
import planillaRouter from "./router/planilla.route";


// const app = express();
export const app = express();
// ─── Middlewares genéricos ────────────────────────────────────────────────────
// 1) Parsear JSON en el body
app.use(express.json());
// 2) CORS
app.use(cors());

// ─── Montar rutas (applica prefijo) ─────────────────────────────────────────────
app.use("/planilla_definitiva", planillaRouter);

// ─── Error handler global (único) ──────────────────────────────────────────────
// Debe ir **después** de haber registrado todas las rutas.
app.use(async (err: any, req: Request, res: Response, next: NextFunction) => {
    // 1) Logueo (slack / otro)
    // Enviar el error a Slack (u otra herramienta de monitoreo)
    await sendError(LogSource.PLANILLA_AFILIADOS, err.toString(), err);

    // 2) Responder al cliente
    res.status(500).json({
        success: false,
        message: "Ocurrió un error interno. Por favor inténtalo más tarde.",
        error: err.message,
    });
});

// ─── Exportar handler para Lambda ───────────────────────────────────────────────
export const handler = async (event: APIGatewayEvent, context: any) => {
    const start = Date.now();
    // `serverless(app)` regresa una función que recibe (event, context)
    const response: any = await serverless(app)(event, context);
    const duration_ms = Date.now() - start;
    await sendLogApiEvent(LogSource.PLANILLA_AFILIADOS, event, response, duration_ms);
    return response;
};
