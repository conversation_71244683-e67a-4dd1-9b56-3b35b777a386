import {Injectable} from "@nestjs/common";
import {
    PLANILLA_REPORTADA,
    SERVICE_REPORT_TAKEN_FORM_MNK
} from "../../utils/constants";
import {PrismaService} from "../../utils/prisma/prisma.service";

@Injectable()
export class DefinitivaRepository {

    constructor(private readonly prisma: PrismaService) {
    }

    async findsPreadsheetPolice(): Promise<{ id: number }[]> {

        return await this.prisma.activities.findMany({
            where: {
                service_id: SERVICE_REPORT_TAKEN_FORM_MNK,
                state_id: PLANILLA_REPORTADA,
            },
            select: {id: true},
        });
    }
}