import {Injectable} from '@nestjs/common';
import {PrismaService} from '../../../utils/prisma/prisma.service';
import {DocumentActivity} from 'src/types';
import {
    activity_actions,
    activity_documents,
    policy_spreadsheets,
} from '@prisma/client';
import {IActivityWithDetails} from "../interface/activity.interface";
import {SERVICE_REPORT_TAKEN_FORM_MNK} from "../../../utils/constants";


@Injectable()
export class ActivityRepository {
    constructor(private readonly prisma: PrismaService) {
    }

    async findActivityById(
        activityId: number
    ): Promise<IActivityWithDetails | null> {
        return await this.prisma.activities.findFirst({
            where: {id: activityId},
            select: {
                id: true,
                parent_id: true,
                affiliates: {
                    select: {
                        full_name: true,
                        email: true,
                    },
                },
                policy_sorts: {
                    select: {
                        validity_from: true,
                    },
                }
            },
        });
    }
}
