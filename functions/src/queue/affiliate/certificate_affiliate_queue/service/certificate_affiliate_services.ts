import { PutObjectCommand, S3Client } from '@aws-sdk/client-s3';
import { SQSRecord } from 'aws-lambda';
import moment from 'moment-timezone';
import 'moment/locale/es';
import { clientId } from '../../../../constants/constants';
import { IActivityWithDetails } from '../../../../types';
import {
    CERTIFICADO_AFILIACION_REPORTADO,
    GENERAR_CERTIFICADO_AFILIADO,
    SERVICE_AFFILIATE_WORKFORCE_REPORT_MNK,
} from '../../../../utils/constants';
import { DOC_TYPES } from '../../../../utils/doctypes';
import { sendEmail } from '../../../../utils/email';
import { LogSource, sendError, sendInfo } from '../../../../utils/logs';
import { generatePdf } from '../../../../utils/pdf';
import { PrismaService } from '../../../../utils/prisma/prisma.service';
import {
    capitalizeEachWord,
    capitalizeFirstLetter,
    currencyIcon,
    formatNumber,
    formatSortNumber,
    getCostaRicaLocation,
} from '../../../../utils/utils';
import { CertificaAffiliateDto } from '../dto/CertificaAffiliateDto';
import { ActivityRepository } from '../repository/activity.repository';

const prismaService = new PrismaService();
const repositoryCertificate = new ActivityRepository(prismaService);

moment.locale('es');
moment.tz.setDefault('America/Costa_Rica');

export async function processMessage(record: SQSRecord): Promise<void> {
    await sendInfo(
        LogSource.PLANILLA_AFILIADOS,
        `Procesando mensaje: Generación certificado afiliado` 
    );

    const {messageAttributes} = record;

    const affiliate =
        messageAttributes['affiliate_spreadsheet_id']?.stringValue || '';

    const exclude_email = messageAttributes['exclude_email']?.stringValue || '';

    const exclude_activity =
        messageAttributes['exclude_activity']?.stringValue || '';

    try {
        let bodyDto: CertificaAffiliateDto =
            CertificaAffiliateDto.createFromSqs(messageAttributes);

        const {affiliate_spreadsheet_id} = bodyDto;

        await sendInfo(
            LogSource.PLANILLA_AFILIADOS,
            `Procesando mensaje: Certificado - ${affiliate_spreadsheet_id}`
        );

        const activity_spreadsheet =
            await repositoryCertificate.findActivityAffiliate(
                Number(affiliate_spreadsheet_id)
            );

        if (!activity_spreadsheet) {
            throw new Error('No se encontró la actividad planilla');
        }

        const {
            policy_spreadsheets: {
                activities_policy_spreadsheets_activity_idToactivities: {
                    id,
                    parent_id,
                    created_at,
                },
            },
        } = activity_spreadsheet;

        const activity_police = await repositoryCertificate.findActivityById(
            Number(parent_id)
        );

        if (!activity_police) {
            throw new Error('No se encontró la actividad Poliza');
        }

        await sendInfo(LogSource.PLANILLA_AFILIADOS, `Creando actividad certificado`);

        let activityAction = null;

        if (exclude_activity === 'false') {
            activityAction = await createActivityAndAction(
                id,
                activity_police,
                activity_spreadsheet
            );
        }

        const affiliate = await buildAffiliateCertificateData(
            activity_police,
            activity_spreadsheet,
            created_at
        );

        const urlDocument: string = await generateAndUploadPdf(affiliate);

        if (activityAction) {
            await repositoryCertificate.createActivityActionDocuments({
                activity_action_id: Number(activityAction.id),
                name: 'Certificado_afiliado',
                path: urlDocument,
                created_at: new Date(),
                updated_at: new Date(),
            });
        }

        if (exclude_email === 'true') return;

        await sendInfo(LogSource.PLANILLA_AFILIADOS, `Enviando correo certificado ${id}`);

        const fileUrl = `https://${process.env.BUCKET_NAME}.s3.amazonaws.com/${urlDocument}`;
        const emailBody = `
                        <p>¡Buen día, ${affiliate.firstName}!</p>
                        <p>Nos complace adjuntarle su constancia de aseguramiento correspondiente a la póliza del Seguro Obligatorio de Riesgos del Trabajo ${affiliate.consecutiveFormat}, a nombre de ${affiliate.nameTomador}.</p>
                        <p>Por favor, si tiene alguna duda o consulta al respecto, contáctenos al correo electrónico <EMAIL> o al teléfono 4102-7600. ¡Para nosotros será un gusto servirle!</p>
                        <p>Nuestro propósito es fortalecer la prevención en salud y seguridad laboral del país, así como proteger a sus colaboradores en el momento que más lo necesitan, generando siempre bienestar.</p>
                        <p>Cordialmente,</p>
                        <p><b>Área de Aseguramiento<br>Seguro Obligatorio de Riesgos del Trabajo<br>MNK Seguros</b></p>
                        `;

        return await sendEmail(
            affiliate.email!,
            `Constancia de aseguramiento de ${affiliate.firstName}`,
            emailBody,
            [fileUrl]
        );
    } catch (error) {
        await sendError(
            LogSource.PLANILLA_AFILIADOS,
            `Error procesando mensaje:Generación certificado afiliado - ${affiliate}`,
            error
        );
        throw error;
    }
}

/**
 * Resuelve la ubicación a partir de los códigos de provincia, cantón y distrito.
 */
const resolveLocation = async (
    activityPolice: IActivityWithDetails
): Promise<string> => {
    const {province, canton, district, employer_address} =
    activityPolice?.affiliates || {};
    // Suponemos que getCostaRicaLocation recibe los códigos en string
    const ubicacion = await getCostaRicaLocation(
        province!,
        canton ? String(canton) : '0',
        district ? String(district) : '0'
    );
    return `${ubicacion.provincia}, ${ubicacion.canto}, ${ubicacion.distrito}, ${employer_address}`;
};

/**
 * Crea la actividad y la acción asociada en la base de datos.
 */
const createActivityAndAction = async (
    policy_spreadsheet_id: string,
    activityPolice: any,
    affiliatesData: any
): Promise<any> => {
    const activityAffiliate = await repositoryCertificate.createActivity({
        parent_id: Number(policy_spreadsheet_id),
        client_id: clientId,
        affiliate_id: Number(affiliatesData?.affiliate_id),
        service_id: SERVICE_AFFILIATE_WORKFORCE_REPORT_MNK,
        state_id: CERTIFICADO_AFILIACION_REPORTADO,
        user_id: activityPolice?.user_id ?? 0,
        created_at: new Date(),
        updated_at: new Date(),
    });

    const activityAction = await repositoryCertificate.createActivityAction({
        activity_id: activityAffiliate.id,
        action_id: GENERAR_CERTIFICADO_AFILIADO,
        old_state_id: CERTIFICADO_AFILIACION_REPORTADO,
        new_state_id: CERTIFICADO_AFILIACION_REPORTADO,
        description: 'GENERAR_CERTIFICADO_AFILIADO',
        old_user_id: activityPolice?.user_id ?? 0,
        new_user_id: activityPolice?.user_id ?? 0,
        author_id: activityPolice?.user_id ?? 0,
    });

    return activityAction;
};

/**
 * Genera el PDF a partir de la plantilla y lo sube a S3.
 * Devuelve el nombre del archivo subido.
 */

const generateAndUploadPdf = async (affiliate: any): Promise<string> => {
    const file = await generatePdf(
        'docs/certificados/affiliation_certificate_pdf.hbs',
        affiliate
    );
    const filename = `activity_action_document/certificado-afiliacion-${affiliate.id}-${moment().format('YYYY-MM-DD-HH-mm-ss')}.pdf`;

    await new S3Client({region: process.env.AWS_REGION}).send(
        new PutObjectCommand({
            Body: file,
            Bucket: process.env.BUCKET_NAME,
            Key: filename,
        })
    );
    return filename;
};

/**
 * Construye el objeto con los datos necesarios para el certificado.
 */
export const buildAffiliateCertificateData = async (
    activity_police: any,
    affiliateSpreadSheet: any,
    periodo: string
): Promise<any> => {
    // Extraer datos de la política (tomador) que se encuentra en el primer elemento de policy_sorts
    const {validity_from, doc_number, type_currency, consecutive} =
    activity_police.policy_sorts?.[0] || {};

    // Aplicar las transformaciones usando Moment.js y las funciones auxiliares
    const createdAt = moment(validity_from).add(1, 'days').format('DD/MM/YYYY');

    const dateOfBirth = moment(affiliateSpreadSheet?.date_of_birth).format(
        'DD/MM/YYYY'
    );
    const idType = affiliateSpreadSheet?.id_type;
    const docTypeDescription = DOC_TYPES[idType || ''] || 'TIPO DESCONOCIDO';
    const currency = currencyIcon(type_currency || '');
    const monthlySalary = formatNumber(
        Number(affiliateSpreadSheet?.monthly_salary)
    );
    const nameTomador = capitalizeEachWord(
        activity_police?.affiliates?.full_name
    );
    const nameAffiliate = capitalizeEachWord(affiliateSpreadSheet?.full_name);
    const textOccupation = capitalizeFirstLetter(
        String(affiliateSpreadSheet?.occupation)
    );
    const documentTaker = doc_number || '';
    const firstName = capitalizeEachWord(affiliateSpreadSheet?.first_name);
    const currentDate = moment().tz('America/Costa_Rica').format('DD/MM/YYYY');
    const currentTime = moment().tz('America/Costa_Rica').format('HH:mm:ss');
    const consecutiveFormat = formatSortNumber(consecutive);
    const direccionTomador = await resolveLocation(activity_police);

    let period = moment(periodo)
        .tz('America/Costa_Rica')
        .subtract(1, 'month')
        .locale('es')
        .format('MMMM YYYY');

    period = period.charAt(0).toUpperCase() + period.slice(1);

    return {
        ...affiliateSpreadSheet,
        createdAt,
        dateOfBirth,
        idType,
        docTypeDescription,
        currency,
        monthlySalary,
        nameTomador,
        nameAffiliate,
        textOccupation,
        documentTaker,
        firstName,
        currentDate,
        currentTime,
        consecutiveFormat,
        direccionTomador,
        period,
    };
};
