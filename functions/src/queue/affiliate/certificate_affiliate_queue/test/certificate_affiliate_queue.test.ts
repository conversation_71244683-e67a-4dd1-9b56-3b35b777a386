import {SQSEvent} from 'aws-lambda';
import {handler} from '../function';

require('dotenv').config();

describe('Certificado Afiliado', () => {
    let event: SQSEvent;

    beforeEach(() => {
        event = {
            Records: [
                {
                    messageId: 'some-id',
                    receiptHandle: 'some-receipt-handle',
                    body: 'Hello from SQS!',
                    attributes: {
                        ApproximateReceiveCount: '1',
                        SentTimestamp: '1523232000000',
                        SenderId: '123456789012',
                        ApproximateFirstReceiveTimestamp: '1523232000001',
                    },
                    messageAttributes: {
                        affiliate_spreadsheet_id: {
                            stringValue: '442088',
                            dataType: 'String',
                        },
                        exclude_email: {
                            stringValue: 'false',
                            dataType: 'String',
                        },
                        exclude_activity: {
                            stringValue: 'true',
                            dataType: 'String',
                        },
                    },
                    md5OfBody: 'some-md5',
                    eventSource: 'aws:sqs',
                    eventSourceARN: 'arn:aws:sqs:us-east-1:123456789012:MyQueue',
                    awsRegion: process.env.AWS_REGION || 'us-east-1',
                },
            ],
        };
    });

    it('should not throw', async () => {
        await expect(handler(event)).resolves.not.toThrow();
    });
});
