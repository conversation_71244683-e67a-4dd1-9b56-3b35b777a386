import {SendMessageCommand, SQSClient} from '@aws-sdk/client-sqs';
import moment from 'moment-timezone';
import 'moment/locale/es';
import {PrismaService} from '../../../../utils/prisma/prisma.service';
import {PlanillaRepository} from '../repository/activity.repository';
import {LogSource, sendError, sendInfo} from "../../../../utils/logs";
import {GenerateCertificateDto} from "../dto/certificate.dto";

const prismaService = new PrismaService();
const repository = new PlanillaRepository(prismaService);
const sqsClient = new SQSClient({region: process.env.AWS_REGION});
moment.locale('es');
moment.tz.setDefault('America/Costa_Rica');


export async function generateCertificates({
                                               activity_planilla,
                                               exclude_email,
                                               exclude_activity
                                           }: GenerateCertificateDto): Promise<void> {
    const PAGE_SIZE = 5_000;
    // 1) Busco planilla
    const planilla = await repository.findByActivityId(activity_planilla);
    if (!planilla) throw new Error('No planilla encontrada');

    // 2) Paginación
    let lastId = 0;
    while (true) {
        const page = await repository.getAffiliatesPage(
            planilla.id,
            lastId,
            PAGE_SIZE
        );
        if (page.length === 0) break;
        lastId = page[page.length - 1].id;

        // 3) Encolo cada afiliado
        for (const aff of page) {
            enqueue(activity_planilla, aff.id, exclude_activity, exclude_email);
        }
    }

    // 4) Log final
    await sendInfo(
        LogSource.PLANILLA_AFILIADOS,
        `Mensajes de certificado generados para planilla ${activity_planilla}`
    );
}


function enqueue(spreadsheetId: number, affiliateId: number, exclude_activity: string, exclude_email: string): void {
    const cmd = new SendMessageCommand({
        QueueUrl: process.env.AWS_SQS_CERTIFICATE_AFFILIATE,
        MessageBody: JSON.stringify({spreadsheetId, affiliateId}),
        MessageAttributes: {
            affiliate_spreadsheet_id: {
                DataType: 'String',
                StringValue: affiliateId.toString()
            },
            exclude_email: {
                DataType: 'String',
                StringValue: exclude_email
            },
            exclude_activity: {
                DataType: 'String',
                StringValue: exclude_activity
            }
        }
    });

    // fire & forget, log errors

    void sqsClient.send(cmd)
        .catch(err =>
            sendError(
                LogSource.PLANILLA_AFILIADOS,
                `Error encolando afiliado ${affiliateId} de planilla ${spreadsheetId}`,
                err
            )
        );
}