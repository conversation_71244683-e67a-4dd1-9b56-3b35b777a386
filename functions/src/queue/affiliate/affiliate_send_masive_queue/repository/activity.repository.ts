// src/repository/planilla.repository.ts
import { PrismaClient } from '@prisma/client';

export class PlanillaRepository {
  constructor(private prisma: PrismaClient) { }

  async findByActivityId(activityId: number) {
    return this.prisma.policy_spreadsheets.findFirst({
      where: { activity_id: activityId }
    });
  }

  async getAffiliatesPage(planillaId: number, lastId: number, pageSize: number) {
    return this.prisma.policy_spreadsheet_affiliates.findMany({
      where: {
        policy_spreadsheet_id: planillaId,
        id: { gt: lastId }
      },
      orderBy: { id: 'asc' },
      take: pageSize
    });
  }
}
