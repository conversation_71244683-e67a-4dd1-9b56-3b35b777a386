import {Injectable} from '@nestjs/common';
import {ActivityRepository} from '../repositories/activity.repository';
import {GenerateEmissionDto} from '../dto/generate-emission.dto';
import {
    capitalizeEachWord,
    capitalizeFirstLetter,
    formatNumber,
    formatSortNumber,
    getS3Url,
    isNullOrUndefined,
} from '../../../utils/utils';
import moment from 'moment/moment';
import 'moment/locale/es';
import {generatePdf} from '../../../utils/pdf';
import {PutObjectCommand, S3Client} from '@aws-sdk/client-s3';
import {loadActivityPrivate, loadActivityPublic, loadCostaRicaData,} from '../../../utils/load-costarica';
import {createRegisterMail, generateHtmlFromTemplate, sendEmail} from '../../../utils/email';
import {PeriodicityInfo} from '../../../types';
import {
    DOCUMENTS_FIRMA_FISICA,
    EMITIR_POLIZA_DESDE_POLIZA,
    REPORTAR_DOCUMENTOS_EXTERNOS_SIN_PLANILLAS,
    REPORTAR_FIRMA_DIGITAL,
    REPORTAR_FIRMA_FISICA,
    SERVICE_REPORT_TAKEN_FORM_MNK,
} from '../../../utils/constants';
import {IActivityExLocation, IActivityWithDetails, IAdrees, IPolicySort} from '../interface/activity.interface';
import {activity_actions, activity_documents} from "@prisma/client";
import {PrismaService} from "../../../utils/prisma/prisma.service";
import { PrismaClient } from '@prisma/client';

@Injectable()
export class ActivityService {
    private activityRepository: ActivityRepository;
    private urlEnv: string | undefined = process.env.URL_ENV;

    constructor(private readonly prismaService: PrismaService) {
        this.activityRepository = new ActivityRepository(prismaService);
    }

    async getActivityDetails(activityId: number): Promise<IActivityWithDetails> {
        // Esperar 10 segundos (10000 ms)
        await new Promise(resolve => setTimeout(resolve, 10000));

        const activity = await this.activityRepository.findActivityById(activityId);

        if (!activity) {
            throw new Error(`No se encontró la actividad con ID ${activityId}`);
        }

        return activity;
    }

    async getActivityActionDetails(
        activityId: number,
        action: number
    ): Promise<activity_actions | null> {
        return await this.activityRepository.findActivityActions(
            activityId,
            action
        );
    }

    async getActivityPaymen(
        activityId: number,
    ): Promise<IActivityWithDetails | null> {
        return await this.activityRepository.findActivityPaymen(activityId);
    }

    async getActivityDocumentsDetails(
        activityId: number,
        document: number
    ): Promise<activity_documents | null> {
        return await this.activityRepository.findActivityDocuments(
            activityId,
            document
        );
    }

    private async condicionesParticulares(
        body: GenerateEmissionDto,
        activity: IActivityExLocation,
        activityActions: activity_actions,
        ejecutarDocLocal: boolean
    ): Promise<string> {

        const {policy_sorts, affiliates} = activity;

        const {
            type_currency,
            consecutive,
            validity_from,
            validity_to,
            amount_policy,
            temporality,
            periodicity,
            unico_percentage,
            anual_percentage,
            semestral_percentage,
            trimestral_percentage,
            mensual_percentage,
            legal_representative_name
        } = policy_sorts?.[0] || {};

        const {full_name, doc_type, name_responsible} = affiliates || {};

        // Moneda
        const currencyLabel = type_currency === 'USD' ? 'Dólares' : 'Colones';
        // const sortNumber = formatSortNumber(Number(body.consecutive));
        const sortNumber = consecutive ? formatSortNumber(consecutive) : '';

        // Fechas
        const fromDate = moment.utc(validity_from).format('DD/MM/YYYY');
        const fromDay = moment.utc(validity_from).format('DD');
        const fromMonth = moment.utc(validity_from).locale('es').format('MMMM');
        const fromYear = moment.utc(validity_from).format('YYYY');

        const toDate = moment.utc(validity_to).format('DD/MM/YYYY');
        const toDateRenova = moment
            .utc(validity_to)
            .add(1, 'days')
            .format('DD/MM/YYYY');
        const toDay = moment.utc(validity_to).format('DD');
        const toMonth = moment.utc(validity_to).format('MMMM');
        const toYear = moment.utc(validity_to).format('YYYY');

        // Monto total y periodicidad
        const total_amount = formatNumber(amount_policy!.toNumber());

        const percentages = {
            unico_percentage: parseFloat((unico_percentage || '').replace(',', '.')!) || 0,
            anual_percentage: parseFloat((anual_percentage || '').replace(',', '.')!) || 0,
            semestral_percentage: parseFloat((semestral_percentage || '').replace(',', '.')!) || 0,
            trimestral_percentage: parseFloat((trimestral_percentage || '').replace(',', '.')!) || 0,
            mensual_percentage: parseFloat((mensual_percentage || '').replace(',', '.')!) || 0,
        };

        const periodicityData = await this.calculatePeriodicity(
            periodicity!,
            amount_policy!.toNumber(),
            percentages
        );

        const representante = legal_representative_name;

        activity = {
            ...activity,
            currencyLabel,
            sortNumber,
            fromDate,
            fromDay,
            fromMonth,
            fromYear,
            toDate,
            toDateRenova,
            toDay,
            toMonth,
            toYear,
            temporality: temporality === 'permanent' ? 'Permanente' : 'Periodo corto',
            total_amount,
            representante,
            currency: type_currency ?? '',
            ...periodicityData
        }

        // // Generar PDF
        const file = await generatePdf(
            'docs/poliza/condiciones_particulares_nuevo.hbs',
            activity
        );

        // Guardar en S3
        const path = `activity_action_document/condiciones-${moment().format('YYYY-MM-DD-HH-mm-ss')}.pdf`;

        await new S3Client({region: process.env.AWS_REGION}).send(
            new PutObjectCommand({
                Body: file,
                Bucket: process.env.BUCKET_NAME,
                Key: path,
            })
        );

        // Crear registro en base de datos
        if (activityActions?.id && !ejecutarDocLocal) {
            await this.activityRepository.createActivityActionDocuments({
                activity_action_id: activityActions.id,
                name: `condiciones_particulares_${activityActions.id}`,
                path,
            });
        }

        return `https://${process.env.BUCKET_NAME}.s3.amazonaws.com/${path}`;
    }

    private async communication(
        activity: IActivityExLocation
    ): Promise<string> {

        moment.locale('es'); // Establece el locale a español

        // Datos de fecha actual
        const date = new Date();
        const currentDate = moment.utc(date).locale('es').format('dddd D [de] MMMM [del] YYYY');

        activity = {
            ...activity,
            currentDate
        }

        // // Generar PDF
        const file = await generatePdf(
            'docs/poliza/comunicado.hbs',
            activity
        );

        // Guardar en S3
        const path = `activity_action_document/comunicado-${moment().format('YYYY-MM-DD-HH-mm-ss')}.pdf`;

        await new S3Client({region: process.env.AWS_REGION}).send(
            new PutObjectCommand({
                Body: file,
                Bucket: process.env.BUCKET_NAME,
                Key: path,
            })
        );

        return `https://${process.env.BUCKET_NAME}.s3.amazonaws.com/${path}`;
    }

    private async calculatePeriodicity(
        periodicity: number,
        amount_policy: number,
        percentages: {
            unico_percentage: number;
            anual_percentage: number;
            semestral_percentage: number;
            trimestral_percentage: number;
            mensual_percentage: number;
            rFration: number;
        }
    ): Promise<PeriodicityInfo> {
        let textPeriodicity = '';
        let estimacion_prima = amount_policy || 0;
        let tem = 0;
        var rFration = 0;

        switch (periodicity) {
            case 0:
            case null:
                textPeriodicity = 'Pago único';
                tem = percentages.unico_percentage || 0;
                rFration = 0;
                break;
            case 1:
                textPeriodicity = 'Anual';
                tem = percentages.anual_percentage || 0;
                rFration = 0;
                break;
            case 2:
                textPeriodicity = 'Semestral';
                estimacion_prima *= 2;
                tem = percentages.semestral_percentage || 0;
                rFration = 4;
                break;
            case 3:
                textPeriodicity = 'Trimestral';
                estimacion_prima *= 4;
                tem = percentages.trimestral_percentage || 0;
                rFration = 6;
                break;
            case 4:
                textPeriodicity = 'Mensual';
                estimacion_prima *= 12;
                tem = percentages.mensual_percentage || 0;
                rFration = 8;
                break;
            default:
                textPeriodicity = 'Desconocido';
                break;
        }

        return {
            textPeriodicity,
            estimacion: formatNumber(estimacion_prima),
            tem: formatNumber(tem),
            rFration:formatNumber(rFration)
        };
    }

    async firmaDocument(
        body: GenerateEmissionDto,
        result: IActivityExLocation,
        activityActions: activity_actions,
        ejecutarDocLocal: boolean
    ): Promise<string> {
        const {activity_id} = body;
        const {salary_projection, temporality} = result?.policy_sorts?.[0] || {};

        const signature_document_fisica = await this.getActivityActionDetails(
            Number(activity_id),
            REPORTAR_FIRMA_FISICA
        );

        if (signature_document_fisica && activityActions?.id) {
            const lastDocument = await this.getActivityDocumentsDetails(
                activity_id,
                DOCUMENTS_FIRMA_FISICA
            );

            if(!ejecutarDocLocal)
            {
                await this.activityRepository.createActivityActionDocuments({
                    activity_action_id: activityActions.id,
                    name: `Firma Física_${activityActions.id}`,
                    path: lastDocument?.path || '',
                });
            }

            return (
                `https://${process.env.BUCKET_NAME}.s3.amazonaws.com/` +
                lastDocument?.path || ''
            );
        }

        const signature_document_digital = await this.getActivityActionDetails(
            Number(activity_id),
            REPORTAR_FIRMA_DIGITAL
        );

        if (signature_document_digital) {
            const isplanilla: boolean = await this.activityRepository.planillaCount(
                activity_id,
                REPORTAR_DOCUMENTOS_EXTERNOS_SIN_PLANILLAS
            );

            let policy_spreadsheets =
                await this.activityRepository.findPolicySpreadsheets(
                    activity_id,
                    SERVICE_REPORT_TAKEN_FORM_MNK
                );

            let checkOne = '';
            let checkTwo = '';
            let checkThree = '';

            if (!isplanilla && result?.policy_sorts?.[0]?.calendar_period == '1') {
                checkOne = 'checked';
            }
            if (!isplanilla && result?.policy_sorts?.[0]?.calendar_period == '2') {
                checkTwo = 'checked';
            }
            if (isplanilla) {
                checkThree = 'checked';
            }


            let payment_method = await this.getActivityPaymen(
                activity_id
            );

            const {policy_sort_collections} = payment_method || {}

            let tcPayment = policy_sort_collections && policy_sort_collections[0].payment_method === 'TC' ? 'checked' : '';
            let tbPayment = policy_sort_collections && policy_sort_collections[0].payment_method === 'TB' ? 'checked' : '';

            let total_salaries = '0.0';
            if (!isNullOrUndefined(policy_spreadsheets?.total_salaries)) {
                total_salaries = formatNumber(
                    Number(policy_spreadsheets?.total_salaries)
                );
            } else {
                total_salaries = formatNumber(
                    Number(salary_projection)
                );
            }

            let montoAsegurado = '0.0';
            if (!isNullOrUndefined(salary_projection)) {
                if (temporality === 'permanent') {
                    montoAsegurado = formatNumber(
                        Number(salary_projection) * 12
                    );
                } else {
                    montoAsegurado = total_salaries;
                }
            }

            let primaAnualEstimada = '0.0';
            if (temporality === 'permanent') {
                if (!isNullOrUndefined(result?.policy_sorts?.[0]?.annual_calculation_amount)) {
                    primaAnualEstimada = formatNumber(
                        Number(result?.policy_sorts?.[0]?.annual_calculation_amount)
                    );
                }
            } else {
                if (!isNullOrUndefined(result?.policy_sorts?.[0]?.single_payment_value)) {
                    primaAnualEstimada = formatNumber(
                        Number(result?.policy_sorts?.[0]?.single_payment_value)
                    );
                }
            }


            const fromDate = moment
                .utc(result?.policy_sorts?.[0]?.validity_from)
                .format('DD/MM/YYYY');
            const fromDay = moment
                .utc(result?.policy_sorts?.[0]?.validity_from)
                .format('DD');
            const fromMonth = moment
                .utc(result?.policy_sorts?.[0]?.validity_from)
                .format('MMMM');
            const fromYear = moment
                .utc(result?.policy_sorts?.[0]?.validity_from)
                .format('YYYY');

            const toDate = moment
                .utc(result?.policy_sorts?.[0]?.validity_to)
                .format('DD/MM/YYYY');
            const toDay = moment
                .utc(result?.policy_sorts?.[0]?.validity_to)
                .format('DD');
            const toMonth = moment
                .utc(result?.policy_sorts?.[0]?.validity_to)
                .format('MMMM');
            const toYear = moment
                .utc(result?.policy_sorts?.[0]?.validity_to)
                .format('YYYY');

            const sign1_url = getS3Url(
                'policy_sort/sign1_' + body.activity_id + '.png'
            );
            const sign2_url = getS3Url(
                'policy_sort/sign2_' + body.activity_id + '.png'
            );


            const currency = result?.policy_sorts?.[0]?.type_currency;
            const resultActivity = {
                ...result,
                checkThree,
                checkOne,
                checkTwo,
                policy_spreadsheets,
                total_salaries,
                montoAsegurado,
                primaAnualEstimada,
                sign1_url,
                sign2_url,
                fromDate,
                fromDay,
                fromMonth,
                fromYear,
                toDate,
                toDay,
                toMonth,
                toYear,
                body,
                tbPayment,
                tcPayment,
                currency,
                temporality
            }


            const file = await generatePdf(
                'docs/poliza/download_signature_mnk.hbs',
                resultActivity
            );
            // subir a S3
            const filename = `activity_action_document/poliza-${moment().format('YYYY-MM-DD-HH-mm-ss')}.pdf`;

            await new S3Client({region: process.env.AWS_REGION}).send(
                new PutObjectCommand({
                    Body: file,
                    Bucket: process.env.BUCKET_NAME,
                    Key: filename,
                })
            );

            const fileUrl = `https://${process.env.BUCKET_NAME}.s3.amazonaws.com/${filename}`;

            if (activityActions?.id && !ejecutarDocLocal) {
                await this.activityRepository.createActivityActionDocuments({
                    activity_action_id: activityActions.id,
                    name: `Firma digital_${activityActions.id}`,
                    path: filename,
                });
            }

            return fileUrl;
        } else {
            throw 'Error: No se pudo generar el documento de firma';
        }
    }

    async getEconomicActivityName(policy_sorts: IPolicySort[]): Promise<string> {
        const activities =
            policy_sorts?.[0].economic_activity === 'public'
                ? await loadActivityPublic()
                : await loadActivityPrivate();

        const matchedActivity = activities.find(
            (econAct) => econAct.CODE === policy_sorts?.[0].activity_economic_id
        );

        return matchedActivity?.ACTIVITY_NAME || '';
    }

    async getLocationData(activity: IActivityWithDetails): Promise<IAdrees> {
        const {province, canton, district} = activity.affiliates!;

        const costaRicaData = await loadCostaRicaData();

        const provinciaData = costaRicaData.province.find(
            (p) => p.code === province
        );
        const cantonData = provinciaData?.cantons.find(
            (c) => c.code === canton?.toString()
        );
        const distritoData = cantonData?.districts.find(
            (d) => d.code === district?.toString()
        );

        return {
            provincia: provinciaData?.name,
            canto: cantonData?.name,
            distrito: distritoData?.name
                ? capitalizeEachWord(distritoData.name)
                : '',
        };
    }

    private async sendEmails(
        activity: any,
        fileUrl: string,
        fileUrlTwo: string
    ): Promise<void> {
        const representante = capitalizeEachWord(activity.affiliates.full_name);
        const sortNumber = formatSortNumber(
            activity.policy_sorts?.[0].consecutive || 0
        );

        // Correo principal
        const emailTemplate = generateHtmlFromTemplate('docs/report_emision.hbs', {
            taker: representante,
            policy: sortNumber,
            linkUrl: fileUrl,
            linkUrlTwo: fileUrlTwo,
        });


        try {
            await sendEmail(
                activity.affiliates?.email || '',
                `Emisión de la póliza #${sortNumber}`,
                emailTemplate,
                [fileUrl, fileUrlTwo]
            );

            const emailResult = {
                status: 'Enviado',
                message: 'Email enviado correctamente'
            };

            const files = [
                {
                    type: 'pdf',
                    path: fileUrl,
                    name: 'condiciones_particulares'
                },
                {
                    type: 'pdf',
                    path: fileUrlTwo,
                    name: 'Firma Física'
                }
            ];

            await createRegisterMail(
                Number(activity?.id || 0),
                75,
                String(activity.policy_sorts[0]?.consecutive || ''),
                'Tomador',
                representante,
                String(activity?.affiliates.doc_number || ''),
                `Emisión de la póliza #${sortNumber}`,
                emailTemplate,
                [activity.affiliates?.email || ''],
                emailResult,
                files,
                null
            );


        } catch (error) {

            const emailResult = {
                status: 'Reboto',
                message: 'Error al enviar email'
            };

            const files = [
                {
                    type: 'pdf',
                    path: fileUrl,
                    name: 'condiciones_particulares'
                },
                {
                    type: 'pdf',
                    path: fileUrlTwo,
                    name: 'Firma Física'
                }
            ];

            await createRegisterMail(
                Number(activity?.id || 0),
                75,
                String(activity.policy_sorts[0]?.consecutive || ''),
                'Tomador',
                representante,
                String(activity?.affiliates.doc_number || ''),
                `Emisión de la póliza #${sortNumber}`,
                emailTemplate,
                [activity.affiliates?.email || ''],
                emailResult,
                files,
                null
            );

        }

        // Correo al intermediario
        const intermediary = capitalizeEachWord(
            activity.policy_sorts?.[0].advisor_name || ''
        );

        //Nombre del tomador
        const takerName = capitalizeEachWord(
            activity.affiliates.full_name
        );

        const emailTemplateIntermediary = generateHtmlFromTemplate(
            'docs/report_intermediary.hbs',
            {
                intermediary,
                taker: representante,
                policy: sortNumber,
                linkUrl: fileUrl,
                linkUrlTwo: fileUrlTwo,
            }
        );

        //identificacion del intermediario 
        const rawCode = activity.policy_sorts?.[0].code;
        const username = rawCode.replace(/^CO-/, ''); // Elimina el prefijo "CO-"

        const prisma = new PrismaClient();

        //buscamos la identificacion del intermediario por su codigo de corredor
        const user = await prisma.users.findFirst({
        where: {
            OR: [
            { username },
            { username: `CO-${username}` },
            { code_mnk: username },
            { code_mnk: `CO-${username}` }
            ]
        },
        select: { identification_number: true }              
        });

        /* 2) Elegir docNumber o valor por defecto */
        const intermediaryDocNumber =
        user?.identification_number?.trim() ? user.identification_number : 'Sin identificación';

        try {
            await sendEmail(
                activity.policy_sorts?.[0].email || '',
                `Emisión de la póliza del cliente ${takerName}`,
                emailTemplateIntermediary,
                [fileUrl, fileUrlTwo]
            );

            const emailResult = {
                status: 'Enviado',
                message: 'Email enviado correctamente'
            };

            const files = [
                {
                    type: 'pdf',
                    path: fileUrl,
                    name: 'condiciones_particulares'
                },
                {
                    type: 'pdf',
                    path: fileUrlTwo,
                    name: 'Firma Física'
                }
            ];

            await createRegisterMail(
                Number(activity?.id || 0),
                75,
                String(activity.policy_sorts[0]?.consecutive || ''),
                'Intermediario',
                intermediary,
                intermediaryDocNumber,
                `Emisión de la póliza del cliente ${takerName}`,
                emailTemplateIntermediary,
                [activity.policy_sorts?.[0].email || ''],
                emailResult,
                files,
                null
            );


        } catch (error) {

            const emailResult = {
                status: 'Reboto',
                message: 'Error al enviar email'
            };

            const files = [
                {
                    type: 'pdf',
                    path: fileUrl,
                    name: 'condiciones_particulares'
                },
                {
                    type: 'pdf',
                    path: fileUrlTwo,
                    name: 'Firma Física'
                }
            ];

            await createRegisterMail(
                Number(activity?.id || 0),
                75,
                String(activity.policy_sorts[0]?.consecutive || ''),
                'Intermediario',
                intermediary,
                intermediaryDocNumber,
                `Emisión de la póliza del cliente ${takerName}`,
                emailTemplateIntermediary,
                [activity.policy_sorts?.[0].email || ''],
                emailResult,
                files,
                null
            );
        }

    }

    async prepareActivityDetails(activity: IActivityWithDetails): Promise<IActivityExLocation> {
        // Obtener el nombre de la actividad económica
        const {policy_sorts, affiliates} = activity || {};

        const nameEconomicActivity =
            await this.getEconomicActivityName(policy_sorts!);

        // Obtener los datos de ubicación
        const locationData: IAdrees = await this.getLocationData(activity);

        // Formatear nombres
        let full_name_word = '';
        let occupation_word = '';
        let first_name_word = '';

        if (affiliates) {
            const {full_name, occupation, first_name} = affiliates;
            full_name_word = capitalizeEachWord(full_name);
            first_name_word = capitalizeEachWord(first_name);
            occupation_word = capitalizeFirstLetter(occupation);
        }

        // Datos de fecha actual
        const date = new Date();
        const currentDate = moment(date).format('DD/MM/YYYY');
        const today = moment();
        const day = today.format('DD');
        const month = today.format('MMMM');
        const year = today.format('YYYY');


        return {
            ...activity,
            ...locationData,
            nameEconomicActivity,
            full_name_word,
            occupation_word,
            first_name_word,
            currentDate,
            date,
            day,
            month,
            year
        };


    }

    async sendPolicyEmails(
        body: GenerateEmissionDto,
        activity: IActivityExLocation
    ): Promise<void> {

        //se pone en true en caso de solo querer enviar los documentos sin generar acciones
        const ejecutarDocLocal = false;

        // Generar los documentos
        const activityActions = await this.getActivityActionDetails(
            Number(body.activity_id),
            EMITIR_POLIZA_DESDE_POLIZA
        );

        if (!activityActions) {
            throw new Error(`No se encontró la Action EMITIR_POLIZA_DESDE_POLIZA ID ${body.activity_id}`);
        }

        const fileUrl = await this.condicionesParticulares(
            body,
            activity,
            activityActions,
            ejecutarDocLocal
        );

        const fileUrlTwo = await this.firmaDocument(
            body,
            activity,
            activityActions,
            ejecutarDocLocal
        );

        const fileUrlCommunication = await this.communication(
            activity
        );


        //Obtener datos del afiliado
        const representante = capitalizeEachWord(activity?.affiliates!.full_name);
        const sortNumber = formatSortNumber(Number(body.consecutive));

        const isplanilla: boolean = await this.activityRepository.isPlanillaCount(
            activity.id
        );

        let subject = `Emisión de la póliza #${sortNumber}`;
        let cuerpoEmail = '<p> Le invitamos a verificar la siguiente información importante: </p>';

        if (isplanilla) {

            subject = `Emisión de la póliza #${sortNumber} sin reporte de planilla`;

            cuerpoEmail = `<p>
                            Sin embargo, hemos notado que está pendiente la carga del reporte de planilla. Le agradecemos mucho que, por favor, nos envíe este documento antes del inicio de operaciones. De lo contrario, en caso de que ocurra un riesgo laboral a uno de sus colaboradores, tendremos que tramitar el evento como “no asegurado” y proceder al cobro del costo total de su atención.
                           </p>
                           <p>
                            De igual forma, le invitamos a verificar la siguiente información importante:
                           </p>`;
        }


        let bodyEmail = `
                    <p>
                    ¡Buen día, ${representante}!
                    </p>
                    <p>
                      Nos complace informarle que hemos emitido su póliza #${sortNumber}.
                    </p>
                    ${cuerpoEmail} 
                      <ul>
                          <li>Condiciones particulares de su seguro.</li>
                          <li>Norma técnica <a href='https://mnk-prod.s3.us-east-1.amazonaws.com/public/Norma+te%CC%81cnica+Seguro+Obligatorio+de+Riesgos+del+Trabajo.pdf'>aquí</a>.</li>
                          <li>Condiciones generales de su póliza, a las cuales puede acceder <a href='https://mnk-prod.s3.us-east-1.amazonaws.com/public/Condiciones+generales+Seguro+Obligatorio+de+Riesgos+del+Trabajo.pdf'>aquí</a>.</li>
                          <li>Seguro obligatorio de riesgos del trabajo.</li>
                      </ul>
                      <p>
                          Además, le solicitamos que para realizar sus reportes de planillas, inclusiones o riesgos laborales, ingrese <a href='${this.urlEnv}'>aquí</a> con su usuario y contraseña, por favor.
                      </p>
                      <p>
                          Su usuario y contraseña son los siguientes:
                          <br>
                          Usuario: ${body.user_id}
                          <br>
                          Contraseña: ${body.password}
                          <br>
                          Código único(Aviso del caso): ${activity.policy_sorts?.[0].unique_code}
                          <br>
                          Acceso a la plataforma: <a href="${this.urlEnv}/login">${this.urlEnv}/login</a>
                      </p>
                      <p>
                          Nos sentimos sumamente honrados y agradecidos por la confianza que ha depositado en nosotros. Nuestro propósito es transformar la protección en una experiencia ágil, confiable y humana.
                      </p>
                     <p>Cordialmente,</p>
                     <p>
                        <strong>Área de Aseguramiento <strong><br>
                        <strong>Seguro Obligatorio de Riesgos del Trabajo<strong><br>
                        <strong>MNK seguros<strong><br>
                     </p>
          `;

        const emailTemplate = generateHtmlFromTemplate(
            "docs/report.hbs",
            {
                body: bodyEmail
            }
        );


        let emails = [
            activity.affiliates?.email || ''
        ];

        let emailsCc = [];

        // Añadir destinatario de prueba si es producción
        if (process.env.ENVIRONMENT == "prod") {
            emailsCc.push('<EMAIL>');
        }

          //identificacion del intermediario 
        const rawCode = activity.policy_sorts?.[0].code ?? '';
        
        const username = rawCode.replace(/^CO-/, ''); // Elimina el prefijo "CO-"

        const prisma = new PrismaClient();

        //buscamos la identificacion del intermediario por su codigo de corredor
        const user = await prisma.users.findFirst({
        where: {
            OR: [
            { username },
            { username: `CO-${username}` },
            { code_mnk: username },
            { code_mnk: `CO-${username}` }
            ]
        },
        select: { identification_number: true }              
        });

        /* 2) Elegir docNumber o valor por defecto */
        const intermediaryDocNumber =
        user?.identification_number?.trim() ? user.identification_number : 'Sin identificación';

        try {
            await sendEmail(
                emails,
                subject,
                emailTemplate,
                [fileUrl, fileUrlTwo, fileUrlCommunication],
                true
            );

            const emailResult = {
                status: 'Enviado',
                message: 'Email enviado correctamente'
            };

            const files = [
                {
                    type: 'pdf',
                    path: fileUrl,
                    name: 'condiciones_particulares'
                },
                {
                    type: 'pdf',
                    path: fileUrlTwo,
                    name: 'Firma Física'
                },
                {
                    type: 'pdf',
                    path: fileUrlCommunication,
                    name: 'Comunicado'
                }
            ];

            await createRegisterMail(
                Number(activity?.id || 0),
                75,
                String(activity.policy_sorts?.[0].consecutive || ''),
                'Tomador',
                representante,
                String(activity.affiliates?.doc_number || ''),
                subject,
                emailTemplate,
                [activity.affiliates?.email  || ''],
                emailResult,
                files,
                null,
                'evicertica'
            );


        } catch (error) {

            const emailResult = {
                status: 'Reboto',
                message: 'Error al enviar email'
            };

            const files = [
                {
                    type: 'pdf',
                    path: fileUrl,
                    name: 'condiciones_particulares'
                },
                {
                    type: 'pdf',
                    path: fileUrlTwo,
                    name: 'Firma Física'
                },
                {
                    type: 'pdf',
                    path: fileUrlCommunication,
                    name: 'Comunicado'
                }
            ];

            await createRegisterMail(
                Number(activity?.id || 0),
                75,
                String(activity.policy_sorts?.[0].consecutive || ''),
                'Tomador',
                representante,
                String(activity.affiliates?.doc_number || ''),
                subject,
                emailTemplate,
                [activity.affiliates?.email  || ''],
                emailResult,
                files,
                null,
                'evicertica'
            );
        }

        //---

        // Obtener datos del intermediario
        const intermediary = capitalizeEachWord(
            activity.policy_sorts?.[0].advisor_name || ''
        );

        //Nombre del tomador
        const takerName = capitalizeEachWord(
            activity.affiliates?.full_name
        );

        const emailTemplateIntermediary = generateHtmlFromTemplate(
            'docs/report_intermediary.hbs',
            {
                intermediary,
                taker: representante,
                policy: sortNumber,
                url_env: this.urlEnv,
                linkUrl: fileUrl,
                linkUrlTwo: fileUrlTwo,
            }
        );

        try {
            await sendEmail(
                activity.policy_sorts?.[0].email || '',
                `Emisión de la póliza del cliente ${takerName}`,
                emailTemplateIntermediary,
                [fileUrl, fileUrlTwo],
                true,
                emailsCc
            );

            const emailResult = {
                status: 'Enviado',
                message: 'Email enviado correctamente'
            };

            const files = [
                {
                    type: 'pdf',
                    path: fileUrl,
                    name: 'condiciones_particulares'
                },
                {
                    type: 'pdf',
                    path: fileUrlTwo,
                    name: 'Firma Física'
                }
            ];

            await createRegisterMail(
                Number(activity?.id || 0),
                75,
                String(activity.policy_sorts?.[0].consecutive || ''),
                'Intermediario',
                intermediary,
                intermediaryDocNumber,
                `Emisión de la póliza del cliente ${takerName}`,
                emailTemplateIntermediary,
                [activity.policy_sorts?.[0].email || ''],
                emailResult,
                files,
                null,
                'evicertica'
            );


        } catch (error) {

            const emailResult = {
                status: 'Reboto',
                message: 'Error al enviar email'
            };

            const files = [
                {
                    type: 'pdf',
                    path: fileUrl,
                    name: 'condiciones_particulares'
                },
                {
                    type: 'pdf',
                    path: fileUrlTwo,
                    name: 'Firma Física'
                }
            ];

            await createRegisterMail(
                Number(activity?.id || 0),
                75,
                String(activity.policy_sorts?.[0].consecutive || ''),
                'Intermediario',
                intermediary,
                intermediaryDocNumber,
                `Emisión de la póliza del cliente ${takerName}`,
                emailTemplateIntermediary,
                [activity.policy_sorts?.[0].email || ''],
                emailResult,
                files,
                null,
                'evicertica'
            );
        }
    }
}
