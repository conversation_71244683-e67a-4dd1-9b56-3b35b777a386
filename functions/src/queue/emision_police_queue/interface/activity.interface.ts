import {Decimal} from '@prisma/client/runtime/library';
import {GenerateEmissionDto} from "../dto/generate-emission.dto";
import {policy_spreadsheets} from "@prisma/client";

// 1. Definimos la interfaz para los affiliates
export interface IAffiliate {
    first_name: string | null;
    full_name?: string | null;
    last_name: string | null;
    doc_number: string | null;
    doc_type: string | null;
    email: string | null;
    occupation: string | null;
    electronic_billing_email: string | null;
    province: string | null;
    canton: number | null;
    district: number | null;
    occupation_responsible: string | null;
    employer_address: string | null;
    phone: string | null;
    cellphone: string | null;
    name_responsible: string | null;
}

// 2. Definimos la interfaz para los policy_sorts
export interface IPolicySort {
    id: number;
    activity_economic_id: string | null;
    legal_representative_name: string | null;
    legal_representative_id: string | null;
    validity_from: Date | null;
    validity_to: Date | null;
    calendar_period: string | null;
    email: string | null;
    advisor_name: string | null;
    brokerage_name: string | null;
    type_currency: string | null;
    temporality: string | null;
    periodicity: number | null;
    amount_policy: Decimal | null;
    economic_activity: string | null;
    consecutive: number | null;
    unique_code: bigint | null;
    anual_percentage: string | null;
    semestral_percentage: string | null;
    trimestral_percentage: string | null;
    mensual_percentage: string | null;
    unico_percentage: string | null;
    annual_calculation_amount?: Decimal | null;
    single_payment_value?: Decimal | null;
    salary_projection?: Decimal | null;
    code?: string ;
}

export interface IpolicySortCollections {
    id: number;
    payment_method: string | null;
}

// 3. Definimos la interfaz que representa el objeto final
export interface IActivityWithDetails {
    id: number;
    parent_id: number | null; // Ajuste para permitir null
    affiliates?: IAffiliate;
    policy_sorts?: IPolicySort[];
    policy_sort_collections?: IpolicySortCollections[];
}

export interface IAdrees {
    provincia?: string;
    canto?: string;
    distrito?: string;
}

export interface IActivityExLocation extends IActivityWithDetails {
    nameEconomicActivity?: string;
    full_name_word?: string;
    occupation_word?: string;
    first_name_word?: string;
    currentDate?: string;
    date?: Date;
    day?: string;
    month?: string;
    year?: string;
    provincia?: string;
    canto?: string;
    distrito?: string;
    currencyLabel?: string;
    currency?: string;
    sortNumber?: string;
    fromDate?: string;
    fromDay?: string;
    fromMonth?: string;
    fromYear?: string;
    toDate?: string;
    toDateRenova?: string;
    toDay?: string;
    toMonth?: string;
    toYear?: string;
    temporality?: 'Permanente' | 'Periodo corto';
    total_amount?: string;
    representante?: string;
    checkThree?: string;
    checkOne?: string;
    checkTwo?: string;
    policy_spreadsheets?: Pick<policy_spreadsheets, 'total_salaries' | 'observacion'> | null;
    total_salaries?: string;
    sign1_url?: string;
    sign2_url?: string;
    body?: GenerateEmissionDto;
    annual_calculation_amount?: string;
}