import {Injectable} from '@nestjs/common';
import {PrismaService} from '../../../utils/prisma/prisma.service';
import {DocumentActivity} from 'src/types';
import {
    activity_actions,
    activity_documents,
    policy_spreadsheets,
} from '@prisma/client';
import {IActivityWithDetails} from "../interface/activity.interface";
import {SERVICE_REPORT_TAKEN_FORM_MNK} from "../../../utils/constants";


@Injectable()
export class ActivityRepository {
    constructor(private readonly prisma: PrismaService) {
    }

    async findActivityById(
        activityId: number
    ): Promise<IActivityWithDetails | null> {
        return await this.prisma.activities.findFirst({
            where: {id: activityId},
            select: {
                id: true,
                parent_id: true,
                affiliates: {
                    select: {
                        first_name: true,
                        full_name: true,
                        last_name: true,
                        doc_number: true,
                        doc_type: true,
                        email: true,
                        occupation: true,
                        electronic_billing_email: true,
                        province: true,
                        canton: true,
                        district: true,
                        occupation_responsible: true,
                        employer_address: true,
                        phone: true,
                        cellphone: true,
                        name_responsible: true,
                    },
                },
                policy_sorts: {
                    select: {
                        id: true,
                        activity_economic_id: true,
                        legal_representative_name: true,
                        legal_representative_id: true,
                        validity_from: true,
                        validity_to: true,
                        calendar_period: true,
                        email: true,
                        advisor_name: true,
                        brokerage_name: true,
                        type_currency: true,
                        temporality: true,
                        periodicity: true,
                        amount_policy: true,
                        economic_activity: true,
                        consecutive: true,
                        unique_code: true,
                        anual_percentage: true,
                        semestral_percentage: true,
                        trimestral_percentage: true,
                        mensual_percentage: true,
                        unico_percentage: true,
                        annual_calculation_amount: true,
                        single_payment_value: true,
                        salary_projection: true
                    },
                }
            },
        });
    }


    async findActivityPaymen(
        activityId: number
    ): Promise<IActivityWithDetails | null> {
        return await this.prisma.activities.findFirst({
            where: {
                parent_id: activityId,
                service_id: 76
            },
            select: {
                id: true,
                parent_id: true,
                policy_sort_collections: {
                    select: {
                        id: true,
                        payment_method: true
                    },
                }
            },
        });
    }

    async findActivityActions(
        activityId: number,
        action: number
    ): Promise<activity_actions | null> {
        return await this.prisma.activity_actions.findFirst({
            where: {
                activity_id: activityId,
                action_id: action,
            },
            orderBy: {
                created_at: 'desc',
            },
        });
    }

    async createActivityActionDocuments(data: DocumentActivity): Promise<void> {
        await this.prisma.activity_action_documents.create({data});
    }

    async findActivityDocuments(
        activity_id: number,
        documents: number
    ): Promise<activity_documents | null> {
        return await this.prisma.activity_documents.findFirst({
            where: {
                activity_id: activity_id,
                document_id: documents,
            },
        });
    }

    async planillaCount(
        activity_id: number,
        action_id: number
    ): Promise<boolean> {
        return (
            (await this.prisma.activity_actions.count({
                where: {
                    activity_id,
                    action_id,
                },
            })) > 0
        );
    }

    async isPlanillaCount(
        activity_id: number
    ): Promise<boolean> {

        const count = await this.prisma.activities.count({
            where: {
                parent_id: activity_id,
                service_id: SERVICE_REPORT_TAKEN_FORM_MNK
            }
        });

        const collection = await this.prisma.policy_sort_collections.findFirst({
            select: {
                type_receipt: true
            },
            where: {
                activities: {
                    parent_id: activity_id
                }
            },
            orderBy: {
                id: 'desc'
            }
        });

        const typeReceipt = collection ? collection.type_receipt : "";

        return (
            (count == 0 && typeReceipt == 'emission')
        );
    }

    async findPolicySpreadsheets(
        activity_id: number,
        service_id: number
    ): Promise<Pick<
        policy_spreadsheets,
        'total_salaries' | 'observacion'
    > | null> {
        return await this.prisma.policy_spreadsheets.findFirst({
            where: {
                activities_policy_spreadsheets_activity_idToactivities: {
                    parent_id: activity_id,
                    service_id,
                },
            },
            select: {
                total_salaries: true,
                observacion: true,
            },
        });
    }
}
