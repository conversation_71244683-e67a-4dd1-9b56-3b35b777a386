import { SQSE<PERSON>, SQSRecord } from "aws-lambda";
import { LogSource, sendError, sendInfo } from "../../utils/logs";
import { registerHelpers } from "../../utils/utils";
import { processMessage } from "./service/planilla.service";

export async function handler(event: SQSEvent): Promise<void> {

   
    const record: SQSRecord = event.Records[0];

    if (!record) {
        throw new Error('No se encontró ningún mensaje en el evento.');
    }

    const { messageAttributes } = record;

    const activity_spreadsheet_id = Number.parseInt(messageAttributes['activity_spreadsheet_id']?.stringValue || '0');

     await sendInfo(
        LogSource.PLANILLA_AFILIADOS,
        `Procesando mensaje: Generación cierre planilla ${activity_spreadsheet_id}`
    );

    try {


        registerHelpers();

        await processMessage(record);

    } catch (error) {
        await sendError(
            LogSource.PLANILLA_AFILIADOS,
            `Error procesando mensaje: Cierre planilla: ${activity_spreadsheet_id}`,
            error
        );
    }
}
