import {Injectable} from "@nestjs/common";
import {PrismaService} from "../../../utils/prisma/prisma.service";
import {
    CERTIFICADO_DE_TOMADOR_REPORTADO, SERVICE_REPORT_TAKEN_FORM_MNK
} from "../../../utils/constants";

@Injectable()
export class PlanillaRepository {
    constructor(private readonly prisma: PrismaService) {
    }

    async findActivityPolice(activityId: number) {

        const result = await this.prisma.policy_spreadsheets.findFirst({
            where: {activity_id: activityId},
            include: {
                activities_policy_spreadsheets_activity_idToactivities: {
                    include: {
                        activities: {
                            select: {
                                id: true,
                                state_id: true,
                                user_id: true,
                                affiliate_id: true,
                                created_at: true,
                                affiliates: {
                                    select: {
                                        full_name: true,
                                        first_name: true
                                    },
                                },
                                policy_sorts: {
                                    select: {
                                        id: true,
                                        type_currency: true,
                                        consecutive: true,
                                        email: true,
                                        notification_email: true,
                                        work_modality_id: true,
                                        calendar_period: true,
                                    },
                                },
                            }

                        }
                    }
                },
                policy_spreadsheet_affiliates: true
            }
        })

        if (!result) {
            throw new Error(`Planilla no encontrada para actividad ${activityId}`);
        }

        return result;
    }

    async createActivityAction(data: any) {
        return await this.prisma.activity_actions.create({
            data,
        });
    }

    async updateActivity(activity_spreadsheet: number) {
        await this.prisma.activities.update({
            where: {id: activity_spreadsheet},
            data: {
                state_id: CERTIFICADO_DE_TOMADOR_REPORTADO,
                updated_at: new Date(),
            }
        });
    }

    async findCalendar(policy_sort_id: number) {
        return await this.prisma.policy_calendars.findMany({
            where: {
                policy_sort_id
            }
        });

    }

    async findsPreadsheetPolice(activity_police: number): Promise<any> {
        const activitys_spreadsheet = await this.prisma.activities.findMany({
            where: {
                parent_id: activity_police, service_id: SERVICE_REPORT_TAKEN_FORM_MNK
            }
        });

        if (!activitys_spreadsheet) throw new Error('Planilla no encontrada para actividad');

        return activitys_spreadsheet;
    }

    async createActivityActionDocument(data: any) {
        await this.prisma.activity_action_documents.create({
            data
        });
    }

    async updateSpreadsheet(policy_spreadsheets: number, filenameReport: string) {
        await this.prisma.policy_spreadsheets.update({
            where: {
                id: policy_spreadsheets,
            },
            data: {
                path_report: filenameReport
            },
        });
    }
}
