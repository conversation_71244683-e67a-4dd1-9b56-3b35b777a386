import { <PERSON>Q<PERSON><PERSON><PERSON> } from 'aws-lambda';
import moment from 'moment-timezone';
import 'moment/locale/es';
import { PDFDocument } from "pdf-lib";

import { generateJwt } from "../../../utils/auth-jwt/jwt-generate";
import {
    CERTIFICADO_DE_TOMADOR_REPORTADO,
    GENERAR_CERTIFICADO_TOMADOR, PLANILLA_REPORTADA,
    POLIZA_EMITIDA_ACTIVA,
    REPORTAR_PLANILLA_DEFINITIVA,
    SERVICE_AFFILIATE_WORKFORCE_REPORT_MNK,
    SERVICE_REPORT_TAKEN_FORM_MNK
} from "../../../utils/constants";
import { createRegisterMail, generateHtmlFromTemplate, sendEmail } from "../../../utils/email";
import IntegrationService from "../../../utils/IntegrationService";
import { LogSource, sendError, sendInfo } from "../../../utils/logs";
import { generateBodyPdfs, generatePdf, requestGeneratePdf, uploadFileToS3 } from "../../../utils/pdf";
import { PrismaService } from "../../../utils/prisma/prisma.service";
import {
    capitalizeEachWord,
    currencyIcon,
    dataSpreadsheetsChuck,
    formatNumber,
    formatSortNumber
} from "../../../utils/utils";
import { ReportData } from "../interfaces/data";
import { PlanillaRepository } from "../repository/planilla.repository";

moment.locale('es');
moment.tz.setDefault('America/Costa_Rica');
const prismaService = new PrismaService();
const repository = new PlanillaRepository(prismaService)

export async function processMessage(record: SQSRecord): Promise<void> {


    const { messageAttributes } = record;

    const activity_spreadsheet_id = Number.parseInt(messageAttributes['activity_spreadsheet_id']?.stringValue || '0');

    await sendInfo(
        LogSource.PLANILLA_AFILIADOS,
        `Procesando mensaje: Generación certificado planilla tomador ${activity_spreadsheet_id}`
    );


    try {


        const spreadsheet = await repository.findActivityPolice(activity_spreadsheet_id);

        let contexto: ReportData = extractContext(spreadsheet);

        contexto.activity_action_spreadsheet = await reportPlanilla(contexto);

        contexto.total_Spreadsheet = (await repository.findsPreadsheetPolice(contexto.activity_police.id)).length;

        contexto.dataPdf = await buildPdfContext(contexto);

        contexto.current_month = contexto.dataPdf.currentMonth;

        //documentos
        await generateAndRecordDocuments(contexto);

        await sendInfo(LogSource.PLANILLA_AFILIADOS, `Se genero documento Planilla poliza: ${contexto.consecutive}`);

        const finalExcludedModality: boolean = await maybeSendAffiliateCertificates(contexto);

        await sendPolicyAcceptanceEmails(contexto, finalExcludedModality)

    } catch
    (error) {
        await sendError(
            LogSource.PLANILLA_AFILIADOS,
            `Error procesando mensaje: Generación certificado planilla tomador - ${activity_spreadsheet_id}`,
            error
        );
        throw error;
    }
}


async function computeCurrentMonth(policy: any, createdAt: Date | null = new Date()): Promise<string> {
    if (policy.calendar_period === '2') {

        const calendars = await repository.findCalendar(policy.id);
        // Obtenemos la fecha de creación de la actividad de planilla
        const planillaDate = moment.utc(createdAt).subtract(15, 'days');
        // Buscar periodo al que pertenece la fecha de la planilla
        const found = calendars.find((p: any) =>
            planillaDate.isBetween(moment.utc(p.start_date), moment.utc(p.end_date), null, '[]')
        );

        if (found) {
            return `Del ${moment.utc(found.start_date).format('DD/MM/YY')} al ${moment.utc(found.end_date).format('DD/MM/YY')}`;
        }
    }

    const d = new Date();
    d.setMonth(d.getMonth() - 1);
    const mes: string = d.toLocaleString('es-ES', { month: 'long' });
    return mes.charAt(0).toUpperCase() + mes.slice(1);
}

async function buildPdfContext({
    policy_spreadsheet_affiliates,

    spreadsheet,
    affiliates,
    police,
    activity_spreadsheet,
    total_Spreadsheet
}: ReportData) {

    const dataChunks: string[] = dataSpreadsheetsChuck(policy_spreadsheet_affiliates);
    const totalAffiliates = spreadsheet.total_affiliates;
    const totalSalaries = formatNumber(Number(spreadsheet.total_salaries));
    const totalPlanillas = total_Spreadsheet! > 1 ? `${total_Spreadsheet! - 1}` : 'Emisión';
    const type = total_Spreadsheet! > 1 ? 'Mensual' : 'Emisión';
    const fullName = capitalizeEachWord(affiliates.full_name || '');
    const currency = currencyIcon(police.type_currency || '');
    const sortId = formatSortNumber(police.consecutive || 0);
    const currentMonth = await computeCurrentMonth(police, activity_spreadsheet.created_at);
    const mesEnLetras = moment(activity_spreadsheet.created_at).locale('es').format('MMMM');
    const mes_capitalizado = mesEnLetras.charAt(0).toUpperCase() + mesEnLetras.slice(1);
    const anio = moment(activity_spreadsheet.created_at).format('YYYY');
    const fechaFormateada = moment(activity_spreadsheet.created_at).format('DD/MM/YYYY');
    const horaFormateada = moment(activity_spreadsheet.created_at).format('HH:mm:ss');

    return {
        dataHtml: dataChunks,
        currentDate: moment(new Date()).format('DD/MM/YYYY'),
        total_affiliates: totalAffiliates,
        total_salaries: totalSalaries,
        full_name: fullName,
        currency,
        sort_id: sortId,
        currentMonth,
        totalPlanillas,
        mes_capitalizado,
        type,
        anio,
        fechaFormateada,
        horaFormateada,
        planillasTotal: total_Spreadsheet!,
        titlePlanillas: {
            totalPlanillas,
            currentMonth
        }
    };
}


function extractContext(data: any): ReportData {


    const {
        activities_policy_spreadsheets_activity_idToactivities: activity_spreadsheet, policy_spreadsheet_affiliates
    } = data;

    const { activities: activity_police } = activity_spreadsheet;

    const {
        affiliates,
        policy_sorts: [police] = []
    } = activity_police;

    if (!police) throw new Error('No se encontró ninguna Póliza.');

    const { consecutive, work_modality_id } = police;


    return {
        activity_spreadsheet,
        policy_spreadsheet_affiliates,
        activity_police,
        affiliates,
        police,
        consecutive,
        spreadsheet: data,
        sort_id: formatSortNumber(consecutive || 0),
        work_modality_id
    };
}

async function generatePlanillaReport(data: any): Promise<Uint8Array> {
    // 1) Renderiza los HTML
    const htmlHeader = generateHtmlFromTemplate('docs/planilla/planilla_header_template.hbs', data);
    const htmlFooter = generateHtmlFromTemplate('docs/planilla/planilla_footer_template.hbs', data);

    // 2) JWT para todas las llamadas
    const jwt = generateJwt(process.env.JWT_SECRET_RENAPP!);

    // 3) Genera header PDF
    const { base64: base64Header } = await requestGeneratePdf(htmlHeader, null, 'landscape', jwt);

    if (!base64Header) {
        throw new Error('No se pudo generar el PDF de cabecera');
    }
    const PDFHeader = new Uint8Array(Buffer.from(base64Header, 'base64'));

    // 4) Genera todos los body chunks
    const bodyPdfs: Uint8Array[] = await generateBodyPdfs(data, jwt, 'landscape');

    // 5) Genera footer PDF
    const { base64: base64Footer } = await requestGeneratePdf(htmlFooter, null, 'landscape', jwt);
    if (!base64Footer) {
        throw new Error('No se pudo generar el PDF de pie de página');
    }
    const PDFFooter = new Uint8Array(Buffer.from(base64Footer, 'base64'));

    // 6) Junta todos los buffers en orden: header + body... + footer
    const allBuffers = [PDFHeader, ...bodyPdfs, PDFFooter];

    // 7) Fusiónalos
    const mergedPdf = await mergePdfs(allBuffers);

    // 8) Súbelo a S3 y devuelve la URL pública
    await uploadFileToS3(mergedPdf, `html-templates/${Date.now()}.pdf`);
    return mergedPdf
}


export async function mergePdfs(buffers: Uint8Array[]): Promise<Uint8Array> {
    const mergedPdf = await PDFDocument.create();

    for (const buf of buffers) {
        const pdf = await PDFDocument.load(buf);
        const pages = await mergedPdf.copyPages(pdf, pdf.getPageIndices());
        pages.forEach(page => mergedPdf.addPage(page));
    }

    const mergedBytes = await mergedPdf.save();
    return new Uint8Array(mergedBytes);
}


async function reportPlanilla({ activity_police, activity_spreadsheet, consecutive }: ReportData) {

    await sendInfo(LogSource.PLANILLA_AFILIADOS, `Reportando planilla: ${consecutive}`);

    await repository.createActivityAction({
        activity_id: activity_police.id,
        action_id: REPORTAR_PLANILLA_DEFINITIVA,
        old_state_id: POLIZA_EMITIDA_ACTIVA,
        new_state_id: POLIZA_EMITIDA_ACTIVA,
        description: 'Reportar planilla definitiva',
        old_user_id: activity_police.user_id,
        new_user_id: activity_police.user_id,
        author_id: activity_police.user_id,
        created_at: new Date(),
    });

    // actualizar estado de la activity_spreadsheet
    await repository.updateActivity(activity_spreadsheet.id);


    const activity_action_spreadsheet = await repository.createActivityAction({
        activity_id: activity_spreadsheet.id,
        action_id: GENERAR_CERTIFICADO_TOMADOR,
        old_state_id: PLANILLA_REPORTADA,
        new_state_id: CERTIFICADO_DE_TOMADOR_REPORTADO,
        description: 'Reportar planilla definitiva',
        old_user_id: activity_spreadsheet.user_id,
        new_user_id: activity_spreadsheet.user_id,
        author_id: activity_spreadsheet.user_id,
        created_at: new Date(),
    });

    await sendInfo(LogSource.PLANILLA_AFILIADOS, `Estado planilla actualizado: ${consecutive}`);

    return activity_action_spreadsheet
}

// dentro de ReportService (añádelo junto al resto de métodos privados)

async function generateAndRecordDocuments(ctx: ReportData): Promise<void> {
    const endpoint: string = `https://${process.env.BUCKET_NAME}.s3.amazonaws.com/`;
    const { dataPdf, activity_spreadsheet, activity_action_spreadsheet } = ctx;

    const timestamp = moment().format("YYYY-MM-DD-HH-mm-ss");

    // 1) Generar PDF de planilla
    const planillaBuffer: Uint8Array = await methodCertificadoPlanilla(ctx);
    const planillaKey = `activity_action_document/planilla-definitiva-${activity_spreadsheet.id}-${timestamp}.pdf`;
    await uploadFileToS3(planillaBuffer, planillaKey);
    await repository.createActivityActionDocument({
        activity_action_id: activity_action_spreadsheet.id,
        name: 'Certificado_Planilla',
        path: planillaKey,
        created_at: new Date(),
    });
    ctx.url_documento_planilla = `${endpoint}${planillaKey}`;
    ctx.path_planilla = `${endpoint}${planillaKey}`;

    await sendInfo(LogSource.PLANILLA_AFILIADOS, `Certificado planilla subido: ${planillaKey}`);

    // 2) Generar PDF de resumen
    const resumenBuffer = await generatePdf('docs/certificados/report_planilla.hbs', dataPdf);
    const resumenKey = `activity_action_document/ResumenPlanilla_${ctx.sort_id}-${timestamp}.pdf`;
    await uploadFileToS3(resumenBuffer, resumenKey);

    await sendInfo(LogSource.PLANILLA_AFILIADOS, `Resumen planilla subido: ${resumenKey}`);
    await repository.createActivityActionDocument({
        activity_action_id: activity_action_spreadsheet.id,
        name: 'Resumen planilla',
        path: resumenKey,
        created_at: new Date(),
    });
    ctx.url_documento_resumen = `${endpoint}${resumenKey}`;
    ctx.path_resumen = `${resumenKey}`;

    // 3) Actualizar el registro de la planilla con la URL del resumen
    await repository.updateSpreadsheet(ctx.spreadsheet.id, resumenKey);
    await sendInfo(LogSource.PLANILLA_AFILIADOS, `Spreadsheet ${ctx.spreadsheet.id} actualizado con resumen`);
}

async function methodCertificadoPlanilla({ dataPdf, policy_spreadsheet_affiliates }: ReportData): Promise<Uint8Array> {
    return policy_spreadsheet_affiliates.length > 4000 ? await generatePlanillaReport(dataPdf) : await generatePdf('docs/planilla/planilla_dinamic.hbs', dataPdf, 'landscape');
}


async function maybeSendAffiliateCertificates(ctx: ReportData): Promise<boolean> {
    const {
        work_modality_id,
        activity_police,
        policy_spreadsheet_affiliates,
        spreadsheet
    } = ctx;

    // Si la modalidad está en [2,3], los excluimos de lista general:
    let finalExcludedModality = [2, 3].includes(work_modality_id);

    // Verifica si el único afiliado de la planilla coincide con el que generó la póliza
    const hasMatchingAffiliate = activity_police?.affiliate_id != null
        ? policy_spreadsheet_affiliates.some(a => a.affiliate_id === activity_police.affiliate_id)
        : false;

    // Sólo cuando hay 1 afiliado y coincide, forzamos la exclusión y llamamos al endpoint
    if (spreadsheet.total_affiliates === 1 && hasMatchingAffiliate) {
        finalExcludedModality = true;
        const url = `${process.env.URL_FUNCTIONS}/planilla_afiliados/certificadoAffiliados`;
        const data = {
            exclude_activity: 'false',
            exclude_email: 'true',
            activity_planilla: ctx.activity_spreadsheet.id
        };

        const resp = await IntegrationService.requestRenAppApi(url, 'POST', data);
        if (resp.status === 200) {
            await sendInfo(
                LogSource.PLANILLA_AFILIADOS,
                `Se inicia proceso de generación de certificados afiliados para planilla ${spreadsheet.id}`
            );
        }
    }

    return finalExcludedModality;
}


async function sendPolicyAcceptanceEmails(
    ctx: ReportData,
    finalExcludedModality: boolean
): Promise<void> {
    const UMBRAL_FILAS_PARA_ADJUNTAR = 4000;
    const { total_Spreadsheet, sort_id, current_month, affiliates } = ctx;
    const affiliateEmail = ctx.police.notification_email ?? '';
    const currentYear = moment().year();
    const name = capitalizeEachWord(affiliates.first_name);

    const urlPlanilla = ctx.url_documento_planilla!;
    const urlResumen = ctx.url_documento_resumen!;

    // Solo si hay más de 1 planilla y no está excluida la modalidad:
    if (total_Spreadsheet! > 1 && !finalExcludedModality) {
        const subject = `Recepción y aceptación de su planilla mensual. Póliza #${sort_id}`;
        let body = `
      <p>¡Buen día, ${name}!</p>
      <p>Muchas gracias por enviarnos su planilla mensual de trabajadores correspondiente a <b>${current_month}</b> de <b>${currentYear}</b>!</p>
      <p>Le confirmamos que recibimos la información satisfactoriamente y la aceptamos para el proceso de registro y gestión de pólizas.</p>
      <p>Se adjunta archivo con el resumen de la información recibida.</p>
      <p>En caso de que tenga alguna consulta, por favor hágalo en nuestra plataforma antes de las 23:59 de hoy.</p>
    `;
        const filasTotales = ctx.policy_spreadsheet_affiliates.length;
        const esGrande = filasTotales > UMBRAL_FILAS_PARA_ADJUNTAR;

        body += esGrande ? `
      <p>
      Debido al tamaño del documento, podrá descargarlo desde este enlace:
      <a href="${urlPlanilla}">Descargar planilla aquí</a>
      </p>
      `: '';

        // 4) Cerrar saludo
        body += `
         <p>Cordialmente,</p>
         <p>
           <strong>Área de Aseguramiento</strong><br>
           <strong>Seguro Obligatorio de Riesgos del Trabajo</strong><br>
           <strong>MNK Seguros</strong>
         </p>
         `;


        let attachments: string[] = [urlResumen];
        if (!esGrande) {
            attachments.push(urlPlanilla);
        }

        try {

            await sendEmail(
                affiliateEmail,
                subject,
                body,
                attachments
            );

            const emailResult = {
                status: 'Enviado',
                message: 'Email enviado correctamente'
            };

            const files = [
                {
                    type: 'pdf',
                    path: ctx.path_planilla || '',
                    name: 'Certificado_Planilla'
                },
                {
                    type: 'pdf',
                    path: ctx.path_resumen || '',
                    name: 'Resumen planilla'
                }
            ];

            await createRegisterMail(
                Number(ctx.activity_spreadsheet.id || 0),
                SERVICE_REPORT_TAKEN_FORM_MNK,
                String(ctx.police.consecutive || ''),
                'Tomador',
                name,
                String(affiliates.doc_number || ''),
                subject,
                body,
                [affiliateEmail!],
                emailResult,
                files,
                null
            );


            await sendInfo(
                LogSource.PLANILLA_AFILIADOS,
                `Correos enviados con éxito para la póliza ${sort_id}`
            );

            // Y luego, encolar la generación de certificados de afiliado
            const url = `${process.env.URL_FUNCTIONS}/planilla_afiliados/certificadoAffiliados`;

            const payload = {
                exclude_activity: 'false',
                exclude_email: 'false',
                activity_planilla: ctx.activity_spreadsheet.id
            };

            const resp = await IntegrationService.requestRenAppApi(url, 'POST', payload);

            if (resp.status === 200) {
                await sendInfo(
                    LogSource.PLANILLA_AFILIADOS,
                    `Se inicia proceso de generación de certificados afiliados`
                );
            }


        } catch (error) {

            const emailResult = {
                status: 'Reboto',
                message: 'Error al enviar email'
            };


            const files = [
                {
                    type: 'pdf',
                    path: ctx.path_planilla || '',
                    name: 'Certificado_Planilla'
                },
                {
                    type: 'pdf',
                    path: ctx.path_resumen || '',
                    name: 'Resumen planilla'
                }
            ];

            await createRegisterMail(
                Number(ctx.activity_spreadsheet.id || 0),
                SERVICE_REPORT_TAKEN_FORM_MNK,
                String(ctx.police.consecutive || ''),
                'Tomador',
                name,
                String(affiliates.doc_number || ''),
                subject,
                body,
                [affiliateEmail!],
                emailResult,
                files,
                null
            );

            await sendError(
                LogSource.PLANILLA_AFILIADOS,
                `Error al enviar email`
            );

        }


    }
}

