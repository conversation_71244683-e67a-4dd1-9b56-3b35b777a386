Resources:
  CargarTxtQueue:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: CargarTxtQueue-${self:provider.stage}
      VisibilityTimeout: 900 # debe coincidir con el de la lambda
      RedrivePolicy:
        deadLetterTargetArn:
          Fn::GetAtt:
            - CargarTxtDLQ
            - Arn
        maxReceiveCount: 1



  CargarTxtDLQ:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: CargarTxtDLQ-${self:provider.stage}