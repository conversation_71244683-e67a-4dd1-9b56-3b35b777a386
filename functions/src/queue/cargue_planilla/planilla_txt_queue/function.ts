import {SQSE<PERSON>, SQSRecord} from "aws-lambda";
import {SpreadsheetProcessor} from "./service/SpreadsheetProcessor";
import {LogSource, sendError, sendInfo} from "../../../utils/logs";

export async function handler(event: SQSEvent): Promise<void> {

    await sendInfo(
        LogSource.PLANILLA_AFILIADOS,
        `Procesando planilla TXT`
    );

    try {
        const record: SQSRecord = event.Records[0];

        if (!record) {
            throw new Error('No se encontró ningún mensaje en el evento.');
        }

        await SpreadsheetProcessor(record);

    } catch (error) {

        await sendError(
            LogSource.PLANILLA_AFILIADOS,
            `Error procesando mensaje:Generación certificado afiliado`,
            error
        );
    }
}