import axios from "axios";
import {LogSource, sendError, sendInfo} from "../../utils/logs";

export async function handler(): Promise<void> {

    try {
        await sendInfo(LogSource.MNK_CRONS, "inicio el cron reportar-planilla-definitiva-mensual", { cron: 'reportar-planilla-definitiva-mensual'});

        const response = await axios.get(process.env.API_URL + "/reportar-planilla-definitiva-mensual",
            {
                maxRedirects: 0 // No seguir redirecciones
            });

        if (response.status !== 200) {

            await sendError(LogSource.MNK_CRONS,
                "Error de la respuesta API en el cron reportar-planilla-definitiva-mensual" ,
                { cron: 'reportar-planilla-definitiva-mensual', status: response.status, data: response.data }
            );

        } else {

            await sendInfo(LogSource.MNK_CRONS,
                "Se envio el correo de planilla definitiva mensual",
                { cron: 'reportar-planilla-definitiva-mensual', status: response.status, data: response.data});
        }

    } catch (e) {

        await sendError(LogSource.MNK_CRONS,
            "Error al enviar el correo de planilla definitiva mensual",
            { cron: 'reportar-planilla-definitiva-mensual', status: 500 , error: e});

    }
}
