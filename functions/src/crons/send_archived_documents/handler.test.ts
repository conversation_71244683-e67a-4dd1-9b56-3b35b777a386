import { handler } from "./function";
import dotenv from "dotenv";

// Carga las variables de entorno
dotenv.config();

describe("Envia documentos archivador vision2020", () => {
    beforeAll(() => {
        process.env.CLIENT_ID = "2"; // Configura aquí
        // Puedes agregar más variables de entorno según sea necesario
    });

    it("debería ejecutar el handler sin errores", async () => {
        try {
            console.log("Ejecutando el handler manualmente...");
            await handler();  // Llama al handler de forma asíncrona
            console.log("Ejecución completada sin errores.");
        } catch (error) {
            console.error("Se produjo un error durante la ejecución:", error);
            throw error;  // Lanza el error para que Jest lo registre
        }
    });
});
