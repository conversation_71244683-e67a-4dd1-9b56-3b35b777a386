import axios from "axios";
import {LogSource, sendError, sendInfo} from "../../utils/logs";

export async function handler(): Promise<void> {

    try {

        await sendInfo(LogSource.MNK_CRONS, "inicio el cron reportar-E-commerce", { cron: 'reportar-E-commerce'});

        const response = await axios.get(process.env.API_URL + "/export_today_tc");

        if (response.status !== 200) {

            await sendError(LogSource.MNK_CRONS,
                "Error de la respuesta API en el cron reportar-E-commerce" ,
                { cron: 'reportar-planilla-definitiva', status: response.status, data: response.data }
            );

        } else {

            await sendInfo(LogSource.MNK_CRONS,
                "Se envio el correo de detalle de los pagos que realizan por medio del E-commerce",
                { cron: 'reportar-E-commerce', status: response.status, data: response.data});
        }

    } catch (e) {

        await sendError(LogSource.MNK_CRONS,
            "Error al enviar el correo de detalle de los pagos que realizan por medio del E-commerce",
            { cron: 'reportar-E-commerce', status: 500 , error: e});

    }
}
