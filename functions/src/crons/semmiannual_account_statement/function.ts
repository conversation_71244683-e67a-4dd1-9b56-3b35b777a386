import axios from "axios";
import {LogSource, sendError, sendInfo} from "../../utils/logs";

export async function handler(): Promise<void> {

    try {

        await sendInfo(LogSource.MNK_CRONS, "inicio el cron generate_account_statement_semmiannual", { cron: 'generate_account_statement_semmiannual'});

        const response = await axios.get(process.env.API_URL + "/generate_account_statement_semmiannual");

        if (response.status !== 200) {

            await sendError(LogSource.MNK_CRONS,
                "Error de la respuesta API en el cron generate_account_statement_semmiannual" ,
                { cron: 'generate_account_statement_semmiannual', status: response.status, data: response.data }
            );

        } else {

            await sendInfo(LogSource.MNK_CRONS,
                "Se ejecutaron los estados de cuenta mensuales",
                { cron: 'generate_account_statement_semmiannual', status: response.status, data: response.data});
        }

    } catch (e) {

        await sendError(LogSource.MNK_CRONS,
            "No se pudieron ejecutar los estados de cuentas mensuales",
            { cron: 'generate_account_statement_semmiannual', status: 500 , error: e});

    }
}