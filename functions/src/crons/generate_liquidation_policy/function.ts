import axios from "axios";
import {LogSource, sendError, sendInfo} from "../../utils/logs";

export async function handler(): Promise<void> {

    try {
        await sendInfo(LogSource.MNK_CRONS, "inicio el cron generate_liquidation_policy", { cron: 'generate_liquidation_policy'});

        const response = await axios.get(process.env.API_URL + "/generate_liquidation_policy");

        if (response.status !== 200) {

            await sendError(LogSource.MNK_CRONS,
                "Error de la respuesta API en el cron iniciar liquidación póliza" ,
                { cron: 'generate_liquidation_policy', status: response.status, data: response.data }
            );

        } else {

            await sendInfo(LogSource.MNK_CRONS,
                "Cron iniciar liquidación póliza ejecutado correctamente",
                { cron: 'generate_liquidation_policy', status: response.status, data: response.data});
        }

    } catch (e) {

        await sendError(LogSource.MNK_CRONS,
            "No se pudo ejecutar el cron iniciar liquidación póliza",
            { cron: 'generate_liquidation_policy', status: 500 , error: e});

    }

}