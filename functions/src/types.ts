import {Decimal} from '@prisma/client/runtime/library';

export interface Barrio {
    code: string;
    name: string;
}

export interface Distrito {
    code: string;
    name: string;
    barrios: Barrio[];
}

export interface Canton {
    code: string;
    name: string;
    districts: Distrito[];
}

export interface Provincia {
    code: string;
    name: string;
    cantons: Canton[];
}

export interface CostaRicaData {
    province: Provincia[];
}

export interface EconomicActivity {
    CODE: string;
    ACTIVITY_NAME: string;
    PERCENTAGE: string;
}

export interface DocumentActivity {
    activity_action_id: number;
    name: string;
    path: string;
    created_at: Date;
    updated_at: Date;
}

export interface IActivity {
    parent_id: number;
    client_id: number;
    affiliate_id: number;
    service_id: number;
    state_id: number;
    user_id: number;
    created_at: Date;
    updated_at: Date;
}

export interface IActivityAction {
    activity_id: number;
    action_id: number;
    old_state_id: number;
    new_state_id: number;
    description: string;
    old_user_id: number;
    new_user_id: number;
    author_id: number;
}

export interface PeriodicityInfo {
    textPeriodicity: string;
    estimacion: string;
    tem: string;
    rFration: string;
}

export interface IActivityWithDetails {
    id?: number;
    parent_id?: number | null; // Ajuste para permitir null
    user_id?: number | null; // Ajuste para permitir null
    affiliates?: IAffiliate;
    policy_sorts?: IPolicySort[];
    policy_sort_collections?: IpolicySortCollections[];
}

export interface IAffiliate {
    first_name?: string | null;
    full_name?: string | null;
    last_name?: string | null;
    doc_number?: string | null;
    doc_type?: string | null;
    email?: string | null;
    occupation?: string | null;
    electronic_billing_email?: string | null;
    province?: string | null;
    canton?: number | null;
    district?: number | null;
    occupation_responsible?: string | null;
    employer_address?: string | null;
    phone?: string | null;
    cellphone?: string | null;
    name_responsible?: string | null;
}

export interface IPolicySort {
    id?: number;
    activity_economic_id?: string | null;
    legal_representative_name?: string | null;
    legal_representative_id?: string | null;
    validity_from?: Date | null;
    validity_to?: Date | null;
    calendar_period?: string | null;
    email?: string | null;
    advisor_name?: string | null;
    brokerage_name?: string | null;
    type_currency?: string | null;
    temporality?: string | null;
    periodicity?: number | null;
    amount_policy?: Decimal | null;
    economic_activity?: string | null;
    consecutive?: number | null;
    unique_code?: bigint | null;
    anual_percentage?: string | null;
    semestral_percentage?: string | null;
    trimestral_percentage?: string | null;
    mensual_percentage?: string | null;
    unico_percentage?: string | null;
    annual_calculation_amount?: Decimal | null;
    single_payment_value?: Decimal | null;
    salary_projection?: Decimal | null;
    doc_number?: string | null;
    code?: string ;
}

export interface IpolicySortCollections {
    id?: number;
    payment_method?: string | null;
}


export interface PdfServiceResult {
    base64?: string;
    url?: string;
}

export interface DataWithChunks {
    dataHtml: string[];
    [key: string]: any;
}