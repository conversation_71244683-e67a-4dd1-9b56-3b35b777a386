import Handlebars from 'handlebars';
import moment from 'moment';
import { Provincia } from 'src/types';
import { loadCostaRicaData } from './load-costarica';

export function excelSerialToDate(
    serial: number | undefined | null
): Date | null {
    if (serial == null || serial === 0) {
        return null;
    }

    const unixTime = (serial - 25569) * 86400 * 1000;
    return new Date(unixTime);
}

export function getS3Url(key: string): string {
    return `https://${process.env.BUCKET_NAME}.s3.amazonaws.com/` + key;
}

export function formatSortNumber(
    consecutive: number | null | undefined
): string {
    return !isNullOrUndefined(consecutive)
        ? `SORT-${consecutive.toString().padStart(4, '0')}`
        : '';
}

export const formatNumber = (value: number): string => {
    return new Intl.NumberFormat('es-ES', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
    }).format(value);
};

export const capitalizeFirstLetter = (
    text: string | undefined | null
): string => {
    if (isNullOrUndefined(text)) return '';
    return text.charAt(0).toUpperCase() + text.slice(1).toLowerCase();
};

export const capitalizeEachWord = (text: string | undefined | null): string => {
    if (isNullOrUndefined(text)) return '';
    return text
        .toLocaleLowerCase('es')
        .split(/\s+/)
        .map((word) => word.charAt(0).toLocaleUpperCase() + word.slice(1))
        .join(' ');
};

export const currencyIcon = (value: string): string => {
    if (value === 'USD') {
        return '$';
    } else if (value === 'CRC') {
        // Suposición: CRC para colón
        return new Handlebars.SafeString(
            '<img src="https://mnk-prod.s3.us-east-1.amazonaws.com/imagenes/colon.png" alt="Colón" width="12" height="12" style="margin: 2px 0 0;">'
        ) as unknown as string; // Asegura el tipo en TS
    } else {
        return value; // Devolver el valor como está si no es USD o CRC
    }
};

export const dataSpreadsheets = (spreadsheets: any[]): string => {
    let processedData = '';
    let index = 1;
    for (const sheet of spreadsheets) {
        const dateOfBirth = moment.utc(sheet.date_of_birth).format('DD/MM/YYYY');

        processedData += ` <tr>
                        <td>${index ?? ''}</td>
                        <td>${sheet.id_type ?? ''}</td>
                        <td>${sheet.nationality ?? ''}</td>
                        <td>${sheet.identification_number ?? ''}</td>
                        <td>${sheet.first_name ?? ''}</td>
                        <td>${sheet.last_name ?? ''}</td>
                        <td>${dateOfBirth ?? ''}</td>
                        <td>${sheet.gender ?? ''}</td>
                        <td>${sheet.email ?? ''}</td>
                        <td>${sheet.work_shift_type ?? ''}</td>
                        <td>${sheet.monthly_salary ?? ''}</td>
                        <td>${sheet.days ?? ''}</td>
                        <td>${sheet.hours ?? ''}</td>
                        <td>${sheet.occupation ?? ''}</td>
                        <td>${sheet.observation_affiliate ?? ''}</td>
                    </tr>
    `;
        index++;
    }

    return processedData;
};

export const dataSpreadsheetsChuck = (spreadsheets: any[]): string[] => {
    const chunks = chunkSpreadsheets(spreadsheets, 10, 2400);
    const result: string[] = [];
    let globalIndex = 1;

    for (const chunk of chunks) {
        let htmlChunk = '';

        for (const sheet of chunk) {
            const dateOfBirth = moment.utc(sheet.date_of_birth).format('DD/MM/YYYY');

            htmlChunk += `
        <tr>
          <td>${globalIndex}</td>
          <td>${sheet.id_type ?? ''}</td>
          <td>${sheet.nationality ?? ''}</td>
          <td>${sheet.identification_number ?? ''}</td>
          <td>${sheet.first_name ?? ''}</td>
          <td>${sheet.last_name ?? ''}</td>
          <td>${dateOfBirth}</td>
          <td>${sheet.gender ?? ''}</td>
          <td>${sheet.email ?? ''}</td>
          <td>${sheet.work_shift_type ?? ''}</td>
          <td>${sheet.monthly_salary ?? ''}</td>
          <td>${sheet.days ?? ''}</td>
          <td>${sheet.hours ?? ''}</td>
          <td>${sheet.occupation ?? ''}</td>
          <td>${sheet.observation_affiliate ?? ''}</td>
        </tr>
      `;
            globalIndex++;
        }

        result.push(htmlChunk);
    }

    return result;
};
function chunkSpreadsheets(
    spreadsheets: any[],
    firstSize = 10,
    otherSize = 17
): any[][] {
    const chunks: any[][] = [];
    let idx = 0;

    // Primer chunk de tamaño fijo
    if (spreadsheets.length > 0) {
        chunks.push(spreadsheets.slice(0, firstSize));
        idx = firstSize;
    }

    // Resto de chunks de tamaño fijo
    while (idx < spreadsheets.length) {
        chunks.push(spreadsheets.slice(idx, idx + otherSize));
        idx += otherSize;
    }

    return chunks;
}

export const getCostaRicaLocation = async (
    province: string,
    canton: string,
    district: string
) => {
    if (!province) {
        return {provincia: '', canto: '', distrito: ''};
    }

    const costaRicaData = await loadCostaRicaData();

    const provinciaData = costaRicaData.province.find(
        (p: Provincia) => p.code === province
    );
    if (!provinciaData) {
        return {provincia: '', canto: '', distrito: ''};
    }

    const provincia = provinciaData.name;
    const cantonIndex = canton ? String(canton) : '0';
    const districtIndex = district ? String(district) : '0';

    const cantonData = provinciaData.cantons.find((c) => c.code === cantonIndex);
    if (!cantonData) {
        return {provincia, canto: '', distrito: ''};
    }

    const canto = cantonData.name;
    const distritoData = cantonData.districts.find(
        (d) => d.code === districtIndex
    );
    const distrito = distritoData ? capitalizeEachWord(distritoData.name) : '';

    return {provincia, canto, distrito};
};

export const isNullOrUndefined = <T>(
    obj: T | null | undefined
): obj is null | undefined => {
    return typeof obj === 'undefined' || obj === null;
};

export const registerHelpers = () => {
    Handlebars.registerHelper('capitalizeEachWord', (text: string) =>
        capitalizeEachWord(text)
    );

    Handlebars.registerHelper('capitalizeFirstLetter', (text: string) =>
        capitalizeFirstLetter(text)
    );

    Handlebars.registerHelper('formatNumber', (value: number) =>
        formatNumber(value)
    );

    Handlebars.registerHelper('currencyIcon', (currency: string) => {
        if (currency === 'CRC') {
            return new Handlebars.SafeString(
                '<img src="https://mnk-prod.s3.us-east-1.amazonaws.com/imagenes/colon.png" alt="" width="12" height="12" style="margin: 2px 0 0;">'
            );
        } else {
            return '$';
        }
    });

    Handlebars.registerHelper('titlePlanilla', (value: any) => {
        const {totalPlanillas, currentMonth} = value;
        if (value.totalPlanillas === 'Emisión') {
            return 'Emisión';
        } else {
            return `${currentMonth} (${totalPlanillas})`;
        }
    });

    Handlebars.registerHelper(
        'ifEquals',
        function (this: any, arg1: any, arg2: any, options: any) {
            return arg1 === arg2 ? options.fn(this) : options.inverse(this);
        }
    );
    Handlebars.registerHelper(
        'ifGreater',
        function (this: any, arg1: any, arg2: any, options: any) {
            return (arg1 > arg2) ? options.fn(this) : options.inverse(this);
        }
    );
};
