import * as nodemailer from 'nodemailer';
import * as fs from 'fs';
import * as handlebars from 'handlebars';
import { SentMessageInfo } from 'nodemailer/lib/smtp-transport';
import { registerHelpers } from './utils';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// Función para compilar una plantilla y generar el HTML
export function generateHtmlFromTemplate(
  templatePath: string,
  params: unknown
): string {
  // Registrar helpers antes de compilar el template
  registerHelpers();

  const templateSource = fs.readFileSync(templatePath, 'utf8');
  const template = handlebars.compile(templateSource);
  return template(params);
}

// Configura el transporte de nodemailer
let transporter: nodemailer.Transporter<SentMessageInfo>;

// Función para enviar correo electrónico
export async function sendEmail(
  to: string | string[],
  subject: string,
  html: string,
  files: string[] = [],
  isCertificate = false,
  emailsCc?: string | string[]
): Promise<void> {
  try {
    let address = process.env.MAIL_FROM_ADDRESS;
    let port = process.env.MAIL_PORT;
    let userName = process.env.MAIL_USERNAME;
    let password = process.env.MAIL_PASSWORD;
    let host = process.env.MAIL_HOST;

    if (isCertificate) {
      address =
        process.env.MAIL_EVICERTICA_USERNAME != 'null'
          ? process.env.MAIL_EVICERTICA_USERNAME
          : address;
      port = process.env.MAIL_EVICERTICA_PORT;
      userName = process.env.MAIL_EVICERTICA_USERNAME;
      password = process.env.MAIL_EVICERTICA_PASSWORD;
      host = process.env.MAIL_EVICERTICA_HOST;
    }

    //Lista de dominios permitidos en QA
    const ALLOWED_DOMAINS = [
      'renconsultores.com.co',
      'rengroup.co',
      'mnkseguros.com',
      'gufum.com',
    ];

    //correo, sea que vengan como string o array
    const emails = Array.isArray(to) ? to : [to];

    //Si no es producción, verificar dominio del correo
    if (process.env.ENVIRONMENT !== 'prod') {
      for (const email of emails) {
        // Extrae el dominio del correo
        const domain = email.split('@').pop();
        if (!domain || !ALLOWED_DOMAINS.includes(domain)) {
          console.log(`Correo bloqueado en QA: ${to}`);
          //No se envía el correo si el dominio no está en la lista
          return;
        }
      }
    }

    // Modificar el asunto si el entorno no es producción
    if (process.env.ENVIRONMENT !== 'prod') {
      subject += ' - ESTOS SON CORREOS DE PRUEBA SERVIDOR QA';
    }

    const cc = Array.isArray(emailsCc) ? emailsCc : emailsCc ? [emailsCc]: [];
    const toEmail = isCertificate ? emails.concat(cc) : emails;
    const mailOptions: nodemailer.SendMailOptions = {
      from: address,
      to: toEmail,
      cc: isCertificate ? undefined : cc,
      subject,
      html,
      attachments: files.map((url) => ({
        filename: url.split('/').pop() ?? 'file', // Usa el nombre de archivo de la URL
        path: url, // La URL del archivo
      })),
    };
    if (!transporter) {
      transporter = nodemailer.createTransport({
        host: host ?? 'smtp-relay.sendinblue.com',
        port: Number(port),
        secure: false, // true para SSL/TLS, false para STARTTLS
        auth: {
          user: userName,
          pass: password,
        },
      });
    }
    const info = await transporter.sendMail(mailOptions);
    console.log('Correo electrónico enviado:', {
      from: mailOptions.from,
      to: mailOptions.to,
      cc: mailOptions.cc,
      messageId: info.messageId,
      envelope: info.envelope,
      accepted: info.accepted,
      rejected: info.rejected,
      pending: info.pending,
      response: info.response,
    });
  } catch (error) {
    console.error('Error al enviar el correo electrónico:', error);
    throw error;
  }
}

// Función para enviar un reporte
export async function sendReport(
  to: string,
  subject: string,
  body: string,
  linkUrl: string
): Promise<void> {
  try {
    const template = generateHtmlFromTemplate('docs/reports.hbs', {
      body,
      linkUrl,
    });
    await sendEmail(to, subject, template);
  } catch (error) {
    console.error('Error al enviar el reporte:', error);
    throw error;
  }
}

// Interfaces para tipado
interface EmailResult {
  status: string;
  message: string;
}

interface EmailFile {
  type: string;
  path: string;
  name: string;
}



// Función para registrar emails enviados en la base de datos
export async function createRegisterMail(
    activityId: number,
    serviceId: number,
    numberPolicy: string,
    personType: string,
    personName: string,
    personIdentification: string,
    subject: string,
    text: string | { text?: string },
    email: string | string[],
    result: EmailResult,
    files: EmailFile[] = [],
    takerAuthorizedId: number | null = null,
    type_provider: string | null = null
) {
  try {
    // Procesar el texto del email
    let bodyEmail = '';
    if (typeof text === 'object' && text !== null) {
      bodyEmail = text.text || '';
    } else {
      bodyEmail = text || '';
    }

    // Procesar los emails
    let sendEmails = '';
    if (Array.isArray(email)) {
      sendEmails = email.join(',');
    } else {
      sendEmails = email || '';
    }

    // Crear el registro en la tabla mail_board
    const mailBoard = await prisma.mail_boards.create({
      data: {
        activity_id: activityId,
        service_id: serviceId,
        number_policy: numberPolicy,
        person_type: personType,
        person_name: personName,
        person_identification: personIdentification,
        subject_email: subject,
        name_email: subject,
        body_email: bodyEmail,
        send_emails: sendEmails,
        date_send: new Date(),
        state: result.status,
        res_email: result.message,
        taker_authorized_id: takerAuthorizedId,
        created_at: new Date(),
        updated_at: new Date(),
        type_provider: type_provider
      }
    });

    // Registrar los documentos en la tabla mail_board_documents si existen

    // if (files && files.length > 0) {
    //   for (const file of files) {
    //     await prisma.mail_board_documents.create({
    //       data: {
    //         type: file.type as any,
    //         path: file.path,
    //         name: file.name,
    //         mail_board_id: Number(mailBoard.id)
    //       }
    //     });
    //   }
    // }

    if (files && files.length > 0) {
      const baseUrl = `https://${process.env.BUCKET_NAME}.s3.amazonaws.com/`;

      for (const file of files) {
        // Normalizar el path eliminando la URL base si está presente
        const cleanedPath = file.path.startsWith(baseUrl) ? file.path.replace(baseUrl, '') : file.path;
        // Obtener solo el nombre del archivo
        const fileName = cleanedPath.split('/').pop() || '';

        await prisma.mail_board_documents.create({
          data: {
            type: file.type as any,
            path: cleanedPath,
            name: fileName,
            mail_board_id: Number(mailBoard.id)
          }
        });
      }
    }


    return mailBoard;

  } catch (error) {
    console.error('Error al guardar en MailBoard:', error);
    throw error;
  }
}
