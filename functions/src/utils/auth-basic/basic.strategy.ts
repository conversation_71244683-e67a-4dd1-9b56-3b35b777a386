import {BasicStrategy} from 'passport-http';
import {PassportStrategy} from '@nestjs/passport';
import {Injectable, UnauthorizedException} from '@nestjs/common';

@Injectable()
export class BasicStrategyAuth extends PassportStrategy(BasicStrategy) {
    async validate(username: string, password: string): Promise<any> {
        const validUsername = process.env.SOAP_USERNAME;
        const validPassword = process.env.SOAP_PASSWORD;
        console.log(validUsername, validPassword);

        if (username === validUsername && password === validPassword) {
            return {username}; // Retorna el usuario autenticado
        }
        throw new UnauthorizedException();
    }
}