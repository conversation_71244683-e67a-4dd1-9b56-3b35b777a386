import axios from 'axios';
import { generateJwt } from './auth-jwt/jwt-generate';

class IntegrationService {
    public static async requestRenAppApi(url: string, method: 'POST' | 'GET', data?: any) {
        try {
            const secretKey = process.env.JWT_SECRET_RENAPP;
            if (!secretKey) {
                throw new Error('La clave secreta de RenApp no ha sido configurada');
            }

            // Generamos el JWT usando la función `generateJwt`
            const jwtToken = generateJwt(secretKey, {
                userId: 1
            });

            const options = {
                method,
                url,
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Bearer ${jwtToken}`
                },
                ...(method === 'POST' ? { data } : { params: data })
            };

            const response = await axios(options);
            return response.data;
        } catch (error: any) {
            throw new Error(`Error al consumir la API: ${error.message}`);
        }
    }
}

export default IntegrationService;
