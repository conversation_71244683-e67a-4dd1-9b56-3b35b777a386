
import { NextFunction, Request, Response } from "express";
import jwt from "jsonwebtoken";

export function jwtMiddleware(req: Request, res: Response, next: NextFunction): void {
    const authHeader = req.headers.authorization;
    const token = authHeader?.startsWith("Bearer ") ? authHeader.split(" ")[1] : null;

    if (!token) {
        res.status(401).json({ success: false, message: "Acceso no autorizado. Token no proporcionado." });
        return;
    }

    const secret = process.env.JWT_SECRET_RENAPP;
    if (!secret) {
        throw new Error("JWT_SECRET_RENAPP no configurado");
    }

    jwt.verify(token, secret, (err) => {
        if (err) {
            res.status(401).json({ success: false, message: "Acceso no autorizado. Token no válido." });
            return;
        }
        next();
    });
}
