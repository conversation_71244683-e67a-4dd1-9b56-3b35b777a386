import {ArgumentsHost, Catch, ExceptionFilter, HttpException,} from '@nestjs/common';
import {Request, Response} from 'express';

import {logger} from './logging.service';

@Catch()
export class LoggingException implements ExceptionFilter {


    catch(exception: unknown, host: ArgumentsHost): void {
        const ctx = host.switchToHttp();
        const response = ctx.getResponse<Response>();

        let error: object = {
            message: (exception?.toString() as string) ?? 'Unknown',
        };
        if (exception instanceof Error) {
            error = {
                message: exception.message,
                stack: exception.stack,
            };
        }

        const status =
            exception instanceof HttpException ? exception.getStatus() : 500;
        const request = response.req as Request;
        const logData = {
            body: response.req.body as object,
            error: exception as object,
            operation: request.method,
            path: request.originalUrl,
        };
        if (status < 500) {
            logger.warn(request.originalUrl, logData);
            response.status(status).json(exception);
        } else {
            logger.error(request.originalUrl, logData);
            response.status(status).json({
                error: error,
                message: 'Internal Server Error',
            });
        }

    }
}
