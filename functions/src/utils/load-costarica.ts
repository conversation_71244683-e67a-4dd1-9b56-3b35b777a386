import {promises as fs} from "fs";
import path from "path";
import {LogSource, sendInfo} from "./logs";
import {CostaRicaData, EconomicActivity} from "../types";

//Función para cargar los datos de manera asíncrona
export async function loadCostaRicaData(): Promise<CostaRicaData> {
    // Define la ruta a partir del directorio de trabajo actual
    const filePath = path.join(process.cwd(), 'assets', 'costarica.json');
    const data = await fs.readFile(filePath, 'utf8');
    // Asigna y parsea los datos
    await sendInfo(LogSource.FUNCTIONS, 'Datos de Costa Rica cargados correctamente');
    return JSON.parse(data);
}

export async function loadActivityPublic(): Promise<EconomicActivity[]> {
    const filePath = path.join(process.cwd(), 'assets/economic_activity', 'public.json');
    const data = await fs.readFile(filePath, 'utf8');
    await sendInfo(LogSource.FUNCTIONS, 'Datos de json economic_activity/public cargados correctamente');
    return JSON.parse(data);
}

export async function loadActivityPrivate(): Promise<EconomicActivity[]> {
    const filePath = path.join(process.cwd(), 'assets/economic_activity', 'private.json');
    const data = await fs.readFile(filePath, 'utf8');
    await sendInfo(LogSource.FUNCTIONS, 'Datos de json economic_activity/private cargados correctamente');
    return JSON.parse(data);
}