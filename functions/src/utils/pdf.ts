import {PutObjectCommand, S3Client} from '@aws-sdk/client-s3';
import axios, {AxiosResponse} from 'axios';
import https from 'https';
import {generateJwt} from './auth-jwt/jwt-generate';
import {generateHtmlFromTemplate} from './email';
import {LogSource, sendError, sendInfo} from './logs';
import {DataWithChunks, PdfServiceResult} from "../types";

const httpsAgent = new https.Agent({
    rejectUnauthorized: false,
});

function isHtmlTooLarge(html: string, maxSizeMB = 0.5): boolean {
    const sizeInBytes = Buffer.byteLength(html, 'utf-8');
    const sizeInMB = sizeInBytes / (1024 * 1024);
    return sizeInMB > maxSizeMB; // Retorna true si el HTML es mayor al límite
}

export async function uploadHtmlToS3(html: string): Promise<string> {
    const fileName = `html-templates/${Date.now()}.html`;

    await new S3Client({region: process.env.AWS_REGION}).send(
        new PutObjectCommand({
            Body: html, // Enviar directamente el string
            Bucket: process.env.BUCKET_NAME,
            Key: fileName,
            ContentType: "text/html", // Especificar que es un HTML
        })
    );

    return `https://${process.env.BUCKET_NAME}.s3.amazonaws.com/${fileName}`;
}

export async function generatePdf(
    template: string,
    data: any,
    orientation: string = 'portrait'
): Promise<Uint8Array> {
    await sendInfo(LogSource.FUNCTIONS, `Generando pdf`, {template, data});

    try {
        const htmlContent = generateHtmlFromTemplate(template, data);

        await sendInfo(LogSource.FUNCTIONS, `Generando pdf`, {
            template,
            data,
            htmlContent,
        });

        const jwt: string = generateJwt(process.env.JWT_SECRET_RENAPP!);


        let htmlUrl: string | null = null;

        // Si el HTML es demasiado grande, lo subimos a S3
        if (isHtmlTooLarge(htmlContent)) {
            await sendInfo(LogSource.FUNCTIONS, `HTML es demasiado grande, subiéndolo a S3...`, {template, data});
            htmlUrl = await uploadHtmlToS3(htmlContent);
        }

        // Enviar la petición
        const response: AxiosResponse<any> = await axios.post(
            `${process.env.URL_FUNTIONS}/generar_pdf`,
            {
                html: htmlUrl ? null : htmlContent,
                htmlUrl: htmlUrl ?? null,
                orientation,
            },
            {
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Bearer ${jwt}`,
                },
                responseType: 'json',
                httpsAgent,
            }
        );

        if (response.status >= 200 && response.status < 300) {
            const {data: {base64, url}} = response

            if (base64) {
                const base64Data = base64 ?? '';

                // Convierte el Base64 en binario
                const buffer = Buffer.from(base64Data, 'base64');
                // Retorna como Uint8Array
                return new Uint8Array(buffer);
            }

            if (url) {
                await sendInfo(LogSource.FUNCTIONS, `Descargando PDF desde S3: ${url}`);

                try {
                    const fileResponse = await axios.get(url, {responseType: "arraybuffer"});
                    return new Uint8Array(fileResponse.data);
                } catch (err) {
                    await sendError(LogSource.FUNCTIONS, "Error descargando el PDF desde S3", {url, error: err});
                    throw new Error(`No se pudo descargar el PDF desde S3: ${url}`);
                }
            }

            throw new Error("El servicio de generación de PDF no devolvió ni Base64 ni URL.");


        } else {
            throw new Error(
                `Error en la generación del PDF: Estado ${response.status}`
            );
        }
    } catch (error) {
        await sendError(LogSource.FUNCTIONS, 'Error en la generación del PDF', {
            error,
            template,
            data,
        });
        throw error;
    }
}

export async function requestGeneratePdf(
    html: string | null,
    htmlUrl: string | null,
    orientation: string,
    jwt: string
): Promise<PdfServiceResult> {
    const payload = {html, htmlUrl, orientation};

    const response: AxiosResponse<PdfServiceResult> = await axios.post(
        `${process.env.URL_FUNTIONS}/generar_pdf`,
        payload,
        {
            headers: {
                'Content-Type': 'application/json',
                Authorization: `Bearer ${jwt}`,
            },
            responseType: 'json',
            httpsAgent,
        }
    );

    if (response.status < 200 || response.status >= 300) {
        throw new Error(`Error en la generación del PDF: Estado ${response.status}`);
    }

    return response.data;
}

export async function generateBodyPdfs(
    data: DataWithChunks,
    jwt: string,
    orientation: 'portrait' | 'landscape' = 'landscape'
): Promise<Uint8Array[]> {
    const pdfBuffers: Uint8Array[] = [];

    // Recorre desde el índice 2 hasta el final
    for (let i = 1; i < data.dataHtml.length; i++) {
        // 1) Genera el HTML para este chunk

        const htmlBody = generateHtmlFromTemplate(
            'docs/planilla/planilla_table_template.hbs',
            {
                ...data,
                dataHtml: data.dataHtml[i],
            }
        );


        await sendInfo(LogSource.FUNCTIONS, `HTML es demasiado grande, subiéndolo a S3...`);

        const htmlUrl = await uploadHtmlToS3(htmlBody);

        // 2) Llama a requestGeneratePdf y espera la respuesta
        const {base64, url} = await requestGeneratePdf(
            null,
            htmlUrl,
            orientation,
            jwt
        );

        // 3) Convierte el resultado en Uint8Array
        let buffer: Uint8Array;
        if (base64) {
            buffer = new Uint8Array(Buffer.from(base64, 'base64'));
        } else if (url) {

            try {
                const fileResponse = await axios.get(url, {responseType: "arraybuffer"});
                buffer = new Uint8Array(fileResponse.data);
            } catch (err) {
                await sendError(LogSource.FUNCTIONS, "Error descargando el PDF desde S3", {url, error: err});
                throw new Error(`No se pudo descargar el PDF desde S3: ${url}`);
            }

        } else {
            throw new Error(
                `Chunk ${i} no devolvió ni base64 ni url en requestGeneratePdf`
            );
        }

        // 4) Guarda el pdf de este chunk
        pdfBuffers.push(buffer);
    }

    return pdfBuffers;
}

export async function uploadFileToS3(file: any, path: string): Promise<string> {
    await new S3Client({region: process.env.AWS_REGION}).send(
        new PutObjectCommand({
            Body: file,
            Bucket: process.env.BUCKET_NAME,
            Key: path,
            ContentType: "application/pdf",
        })
    );

    return `https://${process.env.BUCKET_NAME}.s3.amazonaws.com/${path}`;
}