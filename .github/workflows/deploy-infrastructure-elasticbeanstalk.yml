name: Deploy CloudFormation Elastic Beanstalk

on:
  push:
    branches:
      - main
      - qa
      - dev
    paths:
      - infrastructure/elasticbeanstalk/*.yml

jobs:
  deploy:
    runs-on: ubuntu-latest
    env:
      ENVIRONMENT: ${{ github.ref == 'refs/heads/main' && 'prod' || github.ref == 'refs/heads/qa' && 'qa' || 'dev' }}
      AWS_DEFAULT_REGION: us-east-1
    steps:
      - name: Check out the repo
        uses: actions/checkout@v4.1.7

      - name: Set up AWS CLI
        uses: aws-actions/configure-aws-credentials@v4.0.2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_DEFAULT_REGION }}

      - name: Deploy CloudFormation 01.application-elasticbeanstalk.yml
        shell: bash
        run: |
          aws cloudformation deploy \
            --stack-name application-elasticbeanstalk-mnk \
            --template-file infrastructure/elasticbeanstalk/01.application-elasticbeanstalk.yml \
            --capabilities CAPABILITY_NAMED_IAM --region ${{ env.AWS_DEFAULT_REGION }}

      - name: Deploy CloudFormation 02.elasticbeanstalk.yml
        shell: bash
        run: |
          aws cloudformation deploy \
            --stack-name elasticbeanstalk-mnk-${{ env.ENVIRONMENT }} \
            --template-file infrastructure/elasticbeanstalk/02.elasticbeanstalk.yml \
            --parameter-overrides Environment=${{ env.ENVIRONMENT }} \
            --capabilities CAPABILITY_NAMED_IAM --region ${{ env.AWS_DEFAULT_REGION }}