name: Deploy CloudFormation Common Stacks

on:
  push:
    branches:
      - main
      - qa
      - dev
    paths:
      - infrastructure/common/*.yml

jobs:
  deploy:
    runs-on: ubuntu-latest
    env:
      ENVIRONMENT: ${{ github.ref == 'refs/heads/main' && 'prod' || github.ref == 'refs/heads/qa' && 'qa' || 'dev' }}
      AWS_DEFAULT_REGION: us-east-1

    steps:
      - name: Check out the repo
        uses: actions/checkout@v4.1.7

      - name: Set up AWS CLI
        uses: aws-actions/configure-aws-credentials@v4.0.2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_DEFAULT_REGION }}

      - name: Deploy certificates
        shell: bash
        run: |
          aws cloudformation deploy \
            --stack-name certificates \
            --template-file infrastructure/common/01.certificate.yml \
            --capabilities CAPABILITY_NAMED_IAM --region ${{ env.AWS_DEFAULT_REGION }}

#      - name: Deploy security groups
#        shell: bash
#        run: |
#          aws cloudformation deploy \
#            --stack-name security-groups-${{ env.ENVIRONMENT }} \
#            --template-file infrastructure/common/02.security-group.yml \
#            --parameter-overrides Environment=${{ env.ENVIRONMENT }} \
#            --capabilities CAPABILITY_NAMED_IAM --region ${{ env.AWS_DEFAULT_REGION }}

      - name: Deploy load balancer
        shell: bash
        run: |
          aws cloudformation deploy \
            --stack-name load-balancer \
            --template-file infrastructure/common/03.loadbalancer.yml \
            --capabilities CAPABILITY_NAMED_IAM --region ${{ env.AWS_DEFAULT_REGION }}

      - name: Deploy S3 buckets
        shell: bash
        run: |
          aws cloudformation deploy \
            --stack-name buckets \
            --template-file infrastructure/common/04.buckets.yml \
            --capabilities CAPABILITY_NAMED_IAM --region ${{ env.AWS_DEFAULT_REGION }}

      - name: Deploy Cognito
        shell: bash
        run: |
          aws cloudformation deploy \
            --stack-name cognito \
            --template-file infrastructure/common/05.cognito.yml \
            --capabilities CAPABILITY_NAMED_IAM --region ${{ env.AWS_DEFAULT_REGION }}