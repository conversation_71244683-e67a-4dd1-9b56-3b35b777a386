name: Deploy functions

on:
  push:
    branches:
      - main
      - qa
      - dev
    paths:
      - 'functions/**'

jobs:
  deploy:
    runs-on: ubuntu-22.04
    env:
      ENVIRONMENT: ${{ github.ref == 'refs/heads/main' && 'prod' || github.ref == 'refs/heads/qa' && 'qa' || 'dev' }}
      AWS_DEFAULT_REGION: us-east-1
      AWS_S3_BUCKET_NAME: mnk-builds
      ELASTICBEANSTALK_APP_NAME: mnk

    steps:
      - name: Check out the repo
        uses: actions/checkout@v4.1.7

      - name: Set up AWS CLI
        uses: aws-actions/configure-aws-credentials@v4.0.2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_DEFAULT_REGION }}

      - name: Get secrets from AWS Secrets Manager
        id: get-secrets
        run: |
          SECRETS_JSON=$(aws secretsmanager get-secret-value --secret-id mnk-functions-${{ env.ENVIRONMENT }} --query SecretString --output text)
          echo "$SECRETS_JSON" | jq -r 'to_entries[] | "\(.key)=\(.value)"' > functions/.env

      - name: Install dependencies
        run: |
          cd functions
          npm ci

      - name: Deploy functions
        run: |
          cd functions
          npm run deploy-${{ env.ENVIRONMENT }}